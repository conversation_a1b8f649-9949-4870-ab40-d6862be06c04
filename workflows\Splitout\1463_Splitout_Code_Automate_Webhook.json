{"id": "GM9Qxzul4NPQpJkn", "meta": {"instanceId": "31e69f7f4a77bf465b805824e303232f0227212ae922d12133a0f96ffeab4fef", "templateCredsSetupCompleted": true}, "name": "⚡📽️ Ultimate AI-Powered Chatbot for YouTube Summarization & Analysis", "tags": [], "nodes": [{"id": "10cfb27f-ef93-41cd-972e-37dfdcab97ad", "name": "Get YouTube Transcript", "type": "n8n-nodes-base.code", "position": [20, 360], "parameters": {"jsCode": "// Get all input items\nconst items = $input.all();\nconst results = [];\n\n// Import the YoutubeTranscript module from the youtube-transcript package\n// npm i -g youtube-transcript\nconst { YoutubeTranscript } = require('youtube-transcript');\n\nfor (let i = 0; i < items.length; i++) {\n  // Extract the VIDEO_ID from the input JSON\n  const VIDEO_ID = items[i].json.VIDEO_ID;\n  \n  if (!VIDEO_ID) {\n    throw new Error('The video ID parameter is empty.');\n  }\n  \n  try {\n    // Fetch the transcript for the provided video ID\n    const transcript = await YoutubeTranscript.fetchTranscript(VIDEO_ID);\n    \n    // Append the fetched transcript data to the results\n    results.push({\n      json: {\n        youtubeId: VIDEO_ID,\n        transcript,\n      },\n    });\n  } catch (error) {\n    // In case of an error, add an error message to the output for this item\n    results.push({\n      json: {\n        youtubeId: VIDEO_ID,\n        error: error.message,\n      },\n    });\n  }\n}\n\n// Return the results to be used by the next node in the workflow\nreturn results;\n"}, "typeVersion": 2}, {"id": "a7b7740e-7470-4ce0-a698-6043559eb781", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-580, 180], "parameters": {"inputSource": "jsonExample", "jsonExample": "{\n  \"query\": {\n\t\"videoId\": \"YouTube video id\"\n  }\n}"}, "typeVersion": 1.1}, {"id": "f6c6cbc2-ba2d-4f16-a3f2-ad4d6280f6b6", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-180, -720], "webhookId": "5ed6c28d-2469-4f32-bd16-939f2942a0de", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "ac051c4a-0dc7-4f75-97a7-cb4bed0c8845", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [40, -860], "parameters": {"color": 6, "width": 580, "height": 380, "content": "## 🤖 AI Agent <PERSON> for YouTube Videos"}, "typeVersion": 1}, {"id": "1a737d98-747e-40ae-9075-2b30c93f83a6", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [340, -460], "parameters": {"width": 280, "height": 280, "content": "## 🛠️ YouTube Video Processing Tool"}, "typeVersion": 1}, {"id": "54d39566-a028-48be-87d6-4412d4c53f33", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-260, -460], "parameters": {"color": 5, "width": 280, "height": 280, "content": "## OpenAI\nhttps://platform.openai.com/api-keys"}, "typeVersion": 1}, {"id": "90468bc1-9f91-49ab-bde3-d823d7ac6d05", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [140, -340], "parameters": {}, "typeVersion": 1.3}, {"id": "fe54da1d-05e7-4da1-9347-83e1cf370710", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [40, -460], "parameters": {"color": 2, "width": 280, "height": 280, "content": "## Chat History Memory"}, "typeVersion": 1}, {"id": "0fce1309-2982-4579-8454-34df88e5976c", "name": "gpt-4o-mini1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-160, -340], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {"temperature": 0.1}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "8d841054-2096-49bf-8539-822b14f598df", "name": "YouTube Video Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [200, -720], "parameters": {"text": "={{ $json.chatInput }}", "options": {"systemMessage": "=You are an AI assistant tasked with analyzing and summarizing the transcript of a YouTube video. Your role is to answer user questions and extract relevant insights from the video content. Additionally, identify and extract the **YouTube video ID** from the user's input.\n\nYou have access to a tool called `youtube_video_analyzer`, which can analyze YouTube videos. Use this tool effectively to process video transcripts and generate structured summaries.\n\n#### Instructions:\n1. **Extract YouTube Video ID**:\n   - Identify the **video ID** from the user's input.\n   - Use this ID to analyze the video using the `youtube_video_analyzer` tool.\n\n2. **Analyze and Summarize**:\n   - Break down the video content into main topics using Level 2 headers (##).\n  2.1. Under each header:\n     - List only the most essential concepts and key points\n     - Use bullet points for clarity\n     - Keep explanations concise\n     - Preserve technical accuracy\n     - Highlight key terms in bold\n  2.2. Organize the information in this sequence:\n     - Definition/Background\n     - Main characteristics\n     - Implementation details\n     - Advantages/Disadvantages\n  2.3. Format requirements:\n     - Use markdown formatting\n     - Keep bullet points simple (no nesting)\n     - Bold important terms using **term**\n     - Use tables for comparisons\n     - Include relevant technical details\n\n3. **Organize Output**:\n   - Structure your response in this sequence:\n     1. **Definition/Background**: Provide a brief overview or context of the topic.\n     2. **Main Characteristics**: Highlight critical features or ideas.\n     3. **Implementation Details**: Explain how concepts are applied or executed.\n     4. **Advantages/Disadvantages**: Summarize benefits and limitations.\n\n4. **Formatting Requirements**:\n   - Use markdown formatting for clarity.\n   - Keep bullet points simple (no nested lists).\n   - Use tables for comparisons when applicable.\n   - Include relevant technical details where necessary.\n\nPlease provide a clear, structured summary that captures the core concepts while maintaining technical accuracy.\n\n#### Example Output Structure:\n## Title: Title of video.\n\n## Topic 1: [Main Topic]\n- **Definition/Background**: Brief explanation of the topic.\n- **Main Characteristics**:\n  - Key feature 1\n  - Key feature 2\n- **Implementation Details**:\n  - How it works\n- **Advantages/Disadvantages**:\n  - Advantage 1\n  - Disadvantage 1\n\n## Topic 2: [Next Main Topic]\n...\n\n#### Additional Notes:\n- Ensure your summaries are clear, structured, and technically accurate.\n- If any information is missing or unclear, note it explicitly in your response.\n\nCurrent date: {{ $now }}\n\n\n\n\n \n"}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "5252b7ce-0e3f-4f1d-a76a-6df780b4f8d4", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [-640, -140], "parameters": {"color": 7, "width": 1910, "height": 720, "content": "## 🛠️YouTube Video Processing Tool"}, "typeVersion": 1}, {"id": "d458ef6c-9149-4515-89ac-1c0569186123", "name": "Create YouTube API URL", "type": "n8n-nodes-base.code", "position": [20, 20], "parameters": {"jsCode": "// Define the base URL for the YouTube Data API\nconst BASE_URL = 'https://www.googleapis.com/youtube/v3/videos';\n\n// Get the first input item\nconst item = $input.first();\n\n// Extract the videoId and google_api_key from the input JSON\nconst VIDEO_ID = item.json.VIDEO_ID;\nconst GOOGLE_API_KEY = item.json.GOOGLE_API_KEY; // Dynamically retrieve API key\n\nif (!VIDEO_ID) {\n  throw new Error('The video ID parameter is empty.');\n}\n\nif (!GOOGLE_API_KEY) {\n  throw new Error('The Google API Key is missing.');\n}\n\n// Construct the API URL with the video ID and dynamically retrieved API key\nconst youtubeUrl = `${BASE_URL}?part=snippet,contentDetails,status,statistics,player,topicDetails&id=${VIDEO_ID}&key=${GOOGLE_API_KEY}`;\n\n// Return the constructed URL\nreturn [\n  {\n    json: {\n      youtubeUrl: youtubeUrl,\n    },\n  },\n];\n"}, "typeVersion": 2}, {"id": "f6cf2215-8ad2-4890-a67d-f91a4752e076", "name": "Split Out Transcript Segments", "type": "n8n-nodes-base.splitOut", "position": [220, 360], "parameters": {"options": {}, "fieldToSplitOut": "transcript"}, "typeVersion": 1}, {"id": "93c499e0-a10d-4cfb-959f-9590390722f3", "name": "Combine Transcript Segments", "type": "n8n-nodes-base.summarize", "position": [420, 360], "parameters": {"options": {}, "fieldsToSummarize": {"values": [{"field": "text", "separateBy": " ", "aggregation": "concatenate"}]}}, "typeVersion": 1}, {"id": "39e4c739-e241-4113-a003-25cee18b01e1", "name": "Get YouTube Video Details", "type": "n8n-nodes-base.httpRequest", "position": [220, 20], "parameters": {"url": "={{ $json.youtubeUrl }}", "options": {}}, "typeVersion": 4.2}, {"id": "a76d8498-5556-40b6-b259-3d93940f0a04", "name": "Merge YouTube Details & Transcript", "type": "n8n-nodes-base.merge", "position": [660, 160], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3}, {"id": "db7233ee-8491-47e2-b3c6-ef3f7765470e", "name": "Create One JSON Object", "type": "n8n-nodes-base.aggregate", "position": [860, 160], "parameters": {"options": {}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}, {"id": "1f910801-a802-4e21-bc1e-383f03267711", "name": "Respond with YouTube Details & Transcript", "type": "n8n-nodes-base.set", "position": [1060, 160], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "56c5bc98-fdd1-41c0-8da8-bc81a257570d", "name": "response", "type": "string", "value": "={{ $json.data }}"}]}}, "typeVersion": 3.4}, {"id": "961cec25-9f95-4564-bbdb-4c136bea34f6", "name": "Workflow Variables", "type": "n8n-nodes-base.set", "position": [-340, 180], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "e656b8ef-4266-4f50-bd41-703b4bdb04df", "name": "GOOGLE_API_KEY", "type": "string", "value": "[Your-Google-API-Key]"}, {"id": "32db428d-a2e2-48a0-92c6-3880e744d140", "name": "VIDEO_ID", "type": "string", "value": "={{ $json.query.videoId }}"}]}}, "typeVersion": 3.4}, {"id": "2819e5fb-4c6d-4672-9fe5-ce83bb51b92f", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [-420, 60], "parameters": {"width": 260, "height": 340, "content": "## Workflow Variables\nhttps://cloud.google.com/docs/get-started/access-apis\n\n"}, "typeVersion": 1}, {"id": "cdf3e883-8835-408a-901e-037ad46e9bde", "name": "Sticky Note14", "type": "n8n-nodes-base.stickyNote", "position": [-100, -100], "parameters": {"color": 4, "width": 500, "height": 300, "content": "## YouTube Video Details\nhttps://developers.google.com/youtube/v3/docs\n"}, "typeVersion": 1}, {"id": "d34d3603-f527-4c77-b219-3db3fe634d1f", "name": "Sticky Note15", "type": "n8n-nodes-base.stickyNote", "position": [-100, 240], "parameters": {"color": 5, "width": 700, "height": 300, "content": "## YouTube Video Transcript\nhttps://docs.n8n.io/integrations/creating-nodes/test/run-node-locally/\nhttps://www.npmjs.com/package/youtube-transcript"}, "typeVersion": 1}, {"id": "4ab8a422-90df-4efd-91dd-582cef76b865", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-260, -860], "parameters": {"color": 4, "width": 280, "height": 380, "content": "## 👍 Try Me!"}, "typeVersion": 1}, {"id": "a4cdd7b8-3be1-42ff-a59c-9a615c69093b", "name": "YouTube Processing Tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [440, -340], "parameters": {"name": "youtube_video_analyzer", "workflowId": "={{ $workflow.id }}", "description": "Call this tool to get details and transcript from a YouTube video.  Get the videoId from the users prompt.", "jsonSchemaExample": "{\n\t\"videoId\": \"YouTube video id\"\n}", "specifyInputSchema": true}, "typeVersion": 1}, {"id": "bb254e70-e416-451e-8334-9297e6714d0c", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [640, -960], "parameters": {"color": 3, "width": 620, "height": 780, "content": "## 📽️ YouTube Video AI Agent Workflow\n\nThis n8n workflow template enables users to interact with an AI agent that extracts details and the transcript of a YouTube video based on a provided video ID. Once the video details and transcript are retrieved, users can chat with the AI agent to explore or analyze the content of the video in a conversational manner.\n\n### How the Workflow Works\n1. **Input Video ID**: The user provides a YouTube video ID as input to the workflow.\n   \n2. **Data Retrieval**: The workflow fetches key details about the video (e.g., title, description, upload date) and retrieves its transcript. This involves using YouTube's Data API and other integrated tools for transcript extraction.\n\n3. **AI Agent Interaction**: The extracted details and transcript are processed by an AI-powered agent. Users can then ask questions or engage in a discussion with the agent regarding the video's content, such as summarizing the transcript, analyzing key points, or clarifying specific sections.\n\n4. **Dynamic Responses**: The AI agent uses natural language processing to provide contextual and accurate responses based on the video data, making the interaction intuitive and user-friendly.\n\n### Use Cases\n- **Content Analysis**: Quickly analyze long YouTube videos by querying specific sections or extracting summaries.\n- **Research and Learning**: Use the AI agent to gain insights from educational videos or tutorials without watching them in full.\n- **Content Creation**: Creators can use this workflow to repurpose transcripts into blogs, social media posts, or other formats.\n- **Accessibility**: Improve accessibility for users who prefer text-based interaction over watching videos.\n\n"}, "typeVersion": 1}, {"id": "dfe4a389-cb16-4eea-bd48-d5850c113401", "name": "DeepSeek-V3   Chat", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-500, -340], "parameters": {"model": "=deepseek-chat", "options": {}}, "credentials": {"openAiApi": {"id": "MSl7SdcvZe0SqCYI", "name": "deepseek"}}, "typeVersion": 1.1}, {"id": "8e6b8e43-bbac-4e5a-ab9f-6b23c50b156b", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-640, -960], "parameters": {"color": 3, "width": 360, "height": 480, "content": "## 🛠️ Resources for Getting Started\n\n- **Google Cloud Console** (for API setup): Visit Google Cloud's [Get Started Guide](https://cloud.google.com/docs/get-started/access-apis) to configure your API access.\n- **YouTube Data API Key Setup**: Follow this [guide](https://developers.google.com/youtube/v3/docs) to create and manage your YouTube Data API key.\n- **Install n8n Locally**: Refer to this [installation guide](https://docs.n8n.io/integrations/creating-nodes/test/run-node-locally/) for setting up n8n on your local machine.\n\n---\n\n## ✨ Sample Prompt\n*\"Tell me about this YouTube video with id: JWfNLF_g_V0\"*  \n \n*\"Can you provide a list of key takeaways from this video with id: [youtube-video-id]?\"*\n"}, "typeVersion": 1}, {"id": "65fc9096-57c7-4d68-84e9-2e93094e561e", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-640, -460], "parameters": {"color": 6, "width": 360, "height": 280, "content": "## DeepSeek\nhttps://api-docs.deepseek.com/"}, "typeVersion": 1}, {"id": "f75c6462-ec46-48e7-9b82-7de7590f5422", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-260, -960], "parameters": {"color": 7, "width": 880, "height": 80, "content": "## 📽️Ultimate AI-Powered Chatbot for YouTube Summarization & Analysis"}, "typeVersion": 1}], "active": false, "pinData": {"When Executed by Another Workflow": [{"json": {"query": {"videoId": "JWfNLF_g_V0"}}}]}, "settings": {"executionOrder": "v1"}, "versionId": "9b1e07da-c789-4b3a-bb41-337dd42ee551", "connections": {"gpt-4o-mini1": {"ai_languageModel": [[{"node": "YouTube Video Agent", "type": "ai_languageModel", "index": 0}]]}, "Workflow Variables": {"main": [[{"node": "Create YouTube API URL", "type": "main", "index": 0}, {"node": "Get YouTube Transcript", "type": "main", "index": 0}]]}, "YouTube Video Agent": {"main": [[]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "YouTube Video Agent", "type": "ai_memory", "index": 0}]]}, "Create One JSON Object": {"main": [[{"node": "Respond with YouTube Details & Transcript", "type": "main", "index": 0}]]}, "Create YouTube API URL": {"main": [[{"node": "Get YouTube Video Details", "type": "main", "index": 0}]]}, "Get YouTube Transcript": {"main": [[{"node": "Split Out Transcript Segments", "type": "main", "index": 0}]]}, "YouTube Processing Tool": {"ai_tool": [[{"node": "YouTube Video Agent", "type": "ai_tool", "index": 0}]]}, "Get YouTube Video Details": {"main": [[{"node": "Merge YouTube Details & Transcript", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "YouTube Video Agent", "type": "main", "index": 0}]]}, "Combine Transcript Segments": {"main": [[{"node": "Merge YouTube Details & Transcript", "type": "main", "index": 1}]]}, "Split Out Transcript Segments": {"main": [[{"node": "Combine Transcript Segments", "type": "main", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Workflow Variables", "type": "main", "index": 0}]]}, "Merge YouTube Details & Transcript": {"main": [[{"node": "Create One JSON Object", "type": "main", "index": 0}]]}}}