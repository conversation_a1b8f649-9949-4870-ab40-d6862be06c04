{"id": "ax8PJlp1UDb6EGFt", "meta": {"instanceId": "fb924c73af8f703905bc09c9ee8076f48c17b596ed05b18c0ff86915ef8a7c4a"}, "name": "Telegram AI Langchain bot", "tags": [], "nodes": [{"id": "e275f31f-6a5f-4444-8bf7-6c003a8e53df", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1100, 600], "parameters": {"model": "gpt-4-1106-preview", "options": {"temperature": 0.7, "frequencyPenalty": 0.2}}, "credentials": {"openAiApi": {"id": "63", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "f25a6666-ff23-4372-afd0-4920a99aab6a", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [1220, 600], "parameters": {"sessionKey": "=chat_with_{{ $('Listen for incoming events').first().json.message.chat.id }}", "contextWindowLength": 10}, "typeVersion": 1}, {"id": "96faef5d-0349-47fe-a7cf-150953490e90", "name": "Telegram", "type": "n8n-nodes-base.telegram", "onError": "continueErrorOutput", "position": [1500, 380], "parameters": {"text": "={{ $json.output }}", "chatId": "={{ $('Listen for incoming events').first().json.message.from.id }}", "additionalFields": {"parse_mode": "HTML", "appendAttribution": false}}, "credentials": {"telegramApi": {"id": "70", "name": "Telegram sdfsdfsdfsdfsfd_bot"}}, "typeVersion": 1.1}, {"id": "5ad43039-aaa6-43cd-9b0f-1d02f4d9c4ff", "name": "Correct errors", "type": "n8n-nodes-base.telegram", "position": [1700, 380], "parameters": {"text": "={{ $('AI Agent').item.json.output.replace(/&/g, \"&amp;\").replace(/>/g, \"&gt;\").replace(/</g, \"&lt;\").replace(/\"/g, \"&quot;\") }}", "chatId": "={{ $('Listen for incoming events').first().json.message.from.id }}", "additionalFields": {"parse_mode": "HTML", "appendAttribution": false}}, "credentials": {"telegramApi": {"id": "70", "name": "Telegram sdfsdfsdfsdfsfd_bot"}}, "typeVersion": 1.1}, {"id": "0349a250-966a-4064-970a-8bcfba1647ad", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [940, 900], "parameters": {}, "typeVersion": 1}, {"id": "69a45c1f-838f-49ce-9b89-75db6a8b876f", "name": "Listen for incoming events", "type": "n8n-nodes-base.telegramTrigger", "position": [940, 380], "webhookId": "322dce18-f93e-4f86-b9b1-3305519b7834", "parameters": {"updates": ["message"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "70", "name": "Telegram sdfsdfsdfsdfsfd_bot"}}, "typeVersion": 1}, {"id": "2f5d5f25-9870-40d6-ad42-52750e62de63", "name": "Send back an image", "type": "n8n-nodes-base.telegram", "position": [1300, 900], "parameters": {"file": "={{ $json.data[0].url }}", "chatId": "={{ $('Execute Workflow Trigger').first().json.chat_id }}", "operation": "sendPhoto", "additionalFields": {"parse_mode": "HTML"}}, "credentials": {"telegramApi": {"id": "70", "name": "Telegram sdfsdfsdfsdfsfd_bot"}}, "typeVersion": 1.1}, {"id": "50b43dbf-39e3-4d00-8e47-01e8c193cd1a", "name": "add response field", "type": "n8n-nodes-base.set", "position": [1480, 900], "parameters": {"fields": {"values": [{"name": "response", "stringValue": "Success"}]}, "options": {}}, "typeVersion": 3.2}, {"id": "171bee83-c8e1-4af3-9d1c-12cb6ede4943", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [900, 840], "parameters": {"width": 752.0361990950231, "height": 247.42081447963798, "content": "## Generate an image with Dall-E 3 and send it via Telegram"}, "typeVersion": 1}, {"id": "4d81d201-70bf-4c80-9689-4b65851ad770", "name": "Dall-E 3 Tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [1360, 600], "parameters": {"name": "Draw_Dalle_image", "fields": {"values": [{"name": "chat_id", "stringValue": "={{ $('Listen for incoming events').first().json.message.chat.id }}"}]}, "workflowId": "={{ $workflow.id }}", "description": "Call this tool to request a Dall-E 3 model, when the user asks to draw something. Please send the user request for an image as an inline query string."}, "typeVersion": 1}, {"id": "39d532d3-8c96-4722-9cb0-cad92ff39e94", "name": "Generate image in Dall-E 3", "type": "n8n-nodes-base.httpRequest", "position": [1120, 900], "parameters": {"url": "https://api.openai.com/v1/images/generations", "method": "POST", "options": {}, "sendBody": true, "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "model", "value": "dall-e-3"}, {"name": "prompt", "value": "={{ $json.query }}"}, {"name": "n", "value": 1}, {"name": "size", "value": "1024x1024"}]}, "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "63", "name": "OpenAi account"}}, "typeVersion": 4.1}, {"id": "e5aa496d-55d3-456b-82bb-fe10a06c7338", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1140, 380], "parameters": {"text": "={{ $json.message.text }}", "options": {"humanMessage": "TOOLS\n------\nAssistant can ask the user to use tools to look up information that may be helpful in answering the users original question. The tools the human can use are:\n\n{tools}\n\n{format_instructions}\n\nUSER'S INPUT\n--------------------\nHere is the user's input (remember to respond with a markdown code snippet of a json blob with a single action, and NOTHING else):\n\n{{input}}", "systemMessage": "=You are a helpful AI assistant. You are chatting with the user named `{{ $json.message.from.first_name }}`. Today is {{ DateTime.fromISO($now).toLocaleString(DateTime.DATETIME_FULL) }}\n\nFrom time to time call a user by name (if the user name is provided). In your reply, always send a message in Telegram-supported HTML format. Here are the formatting instructions:\n1. The following tags are currently supported:\n<b>bold</b>, <strong>bold</strong>\n<i>italic</i>, <em>italic</em>\n<u>underline</u>, <ins>underline</ins>\n<s>strikethrough</s>, <strike>strikethrough</strike>, <del>strikethrough</del>\n<span class=\"tg-spoiler\">spoiler</span>, <tg-spoiler>spoiler</tg-spoiler>\n<b>bold <i>italic bold <s>italic bold strikethrough <span class=\"tg-spoiler\">italic bold strikethrough spoiler</span></s> <u>underline italic bold</u></i> bold</b>\n<a href=\"http://www.example.com/\">inline URL</a>\n<code>inline fixed-width code</code>\n<pre>pre-formatted fixed-width code block</pre>\n2. Any code that you send should be wrapped in these tags: <pre><code class=\"language-python\">pre-formatted fixed-width code block written in the Python programming language</code></pre>\nOther programming languages are supported as well.\n3. All <, > and & symbols that are not a part of a tag or an HTML entity must be replaced with the corresponding HTML entities (< with &lt;, > with &gt; and & with &amp;)\n4. If the user sends you a message starting with / sign, it means this is a Telegram bot command. For example, all users send /start command as their first message. Try to figure out what these commands mean and reply accodringly\n"}}, "typeVersion": 1.1}], "active": true, "pinData": {}, "settings": {"callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1", "saveManualExecutions": true, "saveDataSuccessExecution": "all"}, "versionId": "3e9c27eb-1d2f-40bf-b284-4f6a1bece30c", "connections": {"AI Agent": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "Telegram": {"main": [[], [{"node": "Correct errors", "type": "main", "index": 0}]]}, "Dall-E 3 Tool": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Send back an image": {"main": [[{"node": "add response field", "type": "main", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "Generate image in Dall-E 3", "type": "main", "index": 0}]]}, "Generate image in Dall-E 3": {"main": [[{"node": "Send back an image", "type": "main", "index": 0}]]}, "Listen for incoming events": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}}}