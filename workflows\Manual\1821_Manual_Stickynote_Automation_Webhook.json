{"id": "cKFPrgXstN3JgdJs", "meta": {"instanceId": "885b4fb4a6a9c2cb5621429a7b972df0d05bb724c20ac7dac7171b62f1c7ef40", "templateCredsSetupCompleted": true}, "name": "Extract & Summarize Yelp Business Review with Bright Data and Google Gemini", "tags": [{"id": "Kujft2FOjmOVQAmJ", "name": "Engineering", "createdAt": "2025-04-09T01:31:00.558Z", "updatedAt": "2025-04-09T01:31:00.558Z"}, {"id": "ddPkw7Hg5dZhQu2w", "name": "AI", "createdAt": "2025-04-13T05:38:08.053Z", "updatedAt": "2025-04-13T05:38:08.053Z"}], "nodes": [{"id": "b7847e5b-1855-4bce-a9ba-123053085f99", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [340, -535], "parameters": {}, "typeVersion": 1}, {"id": "9bee5052-3aeb-4a76-a688-3128c20877ec", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [780, -820], "parameters": {"width": 420, "height": 220, "content": "## LLM Usages\n\nGoogle Gemini Flash Exp model is being used.\n\nBasic LLM Chain with the Output parser for building the structured data.\n\nSummarization Chain to summarize the structured response."}, "typeVersion": 1}, {"id": "ee05bcc3-0971-4e8c-9e2d-89708fc4ecf4", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [320, -820], "parameters": {"width": 400, "height": 220, "content": "## Note\n\nDeals with the Yelp Business Review data extraction using the Bright Data and LLM for structured data extraction and summarization.\n\n**Please make sure to update the \"Set Yelp URL with the Bright Data Zone\" and the Webhook Notification URL**"}, "typeVersion": 1}, {"id": "8917dd4e-75aa-4c51-ba80-577be3573578", "name": "Set Yelp URL with the Bright Data Zone", "type": "n8n-nodes-base.set", "notes": "Set the URL which you are interested to scrap the data", "position": [560, -535], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "1c132dd6-31e4-453b-a8cf-cad9845fe55b", "name": "url", "type": "string", "value": "https://www.yelp.com/search?find_desc=Restaurants&find_loc=San+Francisco%2C+CA&sortby=rating?product=unlocker&method=api"}, {"id": "0fa387df-2511-4228-b6aa-237cceb3e9c7", "name": "zone", "type": "string", "value": "web_unlocker1"}]}}, "notesInFlow": true, "typeVersion": 3.4}, {"id": "4cccab05-2584-4557-a58a-f92cbd67c67e", "name": "HTTP Request to fetch the Yelp Business Reviews", "type": "n8n-nodes-base.httpRequest", "position": [780, -535], "parameters": {"url": "https://api.brightdata.com/request", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "zone", "value": "={{ $json.zone }}"}, {"name": "url", "value": "={{ $json.url }}"}, {"name": "format", "value": "raw"}]}, "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{}]}}, "credentials": {"httpHeaderAuth": {"id": "kdbqXuxIR8qIxF7y", "name": "Header Auth account"}}, "typeVersion": 4.2}, {"id": "5b39a992-1902-4afe-9cbb-2fca524a5272", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1000, -320], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "YeO7dHZnuGBVQKVZ", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "dba8b9f8-0739-4f34-9c3a-41ad447c1dd3", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [1160, -320], "parameters": {"jsonSchemaExample": "[\n   {\n     \"restaurant_name\": \"string\",\n     \"location\": \"string\",\n     \"average_rating\": \"float\",\n     \"review_count\": \"int\",\n     \"reviews\": [\n      {\n        \"reviewer\": \"string\",\n        \"rating\": \"float\",\n        \"date\": \"YYYY-MM-DD\",\n        \"text\": \"string\"\n      }\n    ]\n   }\n]"}, "typeVersion": 1.2}, {"id": "375dc3e5-02f4-499d-922d-31070188b864", "name": "Summarization Chain", "type": "@n8n/n8n-nodes-langchain.chainSummarization", "position": [1376, -660], "parameters": {"options": {"summarizationMethodAndPrompts": {"values": {"prompt": "Write a concise summary of the following:\n\n\n\"{text}\"\n\n", "combineMapPrompt": "=Write a concise summary of the following:\n\n\n\n\n\nCONCISE SUMMARY: {{ $json.output }}"}}}}, "typeVersion": 2}, {"id": "3846b2a2-a670-4264-9028-11c5f76770e8", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [1760, -520], "parameters": {}, "typeVersion": 3.1}, {"id": "5f617e4c-17c2-437b-8a7a-1cdab587c5dd", "name": "Webhook Notifier for the merged response", "type": "n8n-nodes-base.httpRequest", "position": [1972, -535], "parameters": {"url": "https://webhook.site/daf9d591-a130-4010-b1d3-0c66f8fcf467", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "reviews", "value": "={{ $json.output }}"}, {"name": "summary", "value": "={{ $json.response.text }}"}]}}, "typeVersion": 4.2}, {"id": "66bd58de-a235-43b3-bb3e-491644aaabd8", "name": "Google Gemini Chat Model for Summarization", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1464, -440], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "YeO7dHZnuGBVQKVZ", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "4bd63e0d-5f58-4232-b638-cede71a50f0f", "name": "Structured Data Extractor", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1000, -535], "parameters": {"text": "=Summarize and analyze Yelp reviews {{ $json.data }}", "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.6}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "32c41687-2a9b-4ab8-b7fb-a34e5111548a", "connections": {"Merge": {"main": [[{"node": "Webhook Notifier for the merged response", "type": "main", "index": 0}]]}, "Summarization Chain": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Structured Data Extractor", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Structured Data Extractor", "type": "ai_outputParser", "index": 0}]]}, "Structured Data Extractor": {"main": [[{"node": "Summarization Chain", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set Yelp URL with the Bright Data Zone", "type": "main", "index": 0}]]}, "Set Yelp URL with the Bright Data Zone": {"main": [[{"node": "HTTP Request to fetch the Yelp Business Reviews", "type": "main", "index": 0}]]}, "Webhook Notifier for the merged response": {"main": [[]]}, "Google Gemini Chat Model for Summarization": {"ai_languageModel": [[{"node": "Summarization Chain", "type": "ai_languageModel", "index": 0}]]}, "HTTP Request to fetch the Yelp Business Reviews": {"main": [[{"node": "Structured Data Extractor", "type": "main", "index": 0}]]}}}