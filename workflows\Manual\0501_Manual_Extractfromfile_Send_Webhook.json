{"meta": {"instanceId": "cb484ba7b742928a2048bf8829668bed5b5ad9787579adea888f05980292a4a7"}, "nodes": [{"id": "e7725ddb-8cdc-4e36-8a9e-5bf079d94972", "name": "When clicking \"Test workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [460, 460], "parameters": {}, "typeVersion": 1}, {"id": "7cd477d3-e7fd-4a2b-b39e-f5b00271540a", "name": "Compose message", "type": "n8n-nodes-base.set", "position": [1340, 460], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "2addc1b4-68a0-4c72-87d6-d47286eef70c", "name": "raw", "type": "string", "value": "={{ \"From: \"+$('Message settings').item.json.from+\"\\nTo: \"+$('Message settings').item.json.to+\"\\nSubject: \"+$('Message settings').item.json.subject+\"\\nMIME-Version: 1.0\\nContent-Type: multipart/related; boundary=boundary1\\n\\n--boundary1\\nContent-Type: text/html; charset=UTF-8\\n\\n<html>\\n<body>\\n\"+$('Message settings').item.json.body_html+\"\\n</body>\\n</html>\\n\\n--boundary1\\nContent-Type: \"+$('Get image').item.binary.data.mimeType+\"\\nContent-Transfer-Encoding: base64\\nContent-Disposition: inline\\nContent-ID: <image1>\\n\\n\"+$json.chart1+\"\\n\\n--boundary1--\\n\" }}"}]}}, "typeVersion": 3.3}, {"id": "4aca2efe-cf79-4cec-8912-44761595e9ea", "name": "Send message", "type": "n8n-nodes-base.httpRequest", "position": [1560, 460], "parameters": {"url": "https://www.googleapis.com/gmail/v1/users/me/messages/send", "body": "={ \"raw\": \"{{ $json.raw.base64Encode() }}\"}", "method": "POST", "options": {}, "sendBody": true, "contentType": "raw", "authentication": "predefinedCredentialType", "rawContentType": "application/json", "nodeCredentialType": "gmailOAuth2"}, "credentials": {"gmailOAuth2": {"id": "198", "name": "Gmail account (<PERSON>)"}}, "typeVersion": 4.2}, {"id": "75ec79b0-782a-462e-8f68-5c3f6a77190a", "name": "Get image", "type": "n8n-nodes-base.httpRequest", "position": [900, 460], "parameters": {"url": "https://thistleandrose.co.uk/img/userimages/Page/0/bgmainfront.jpg", "options": {}}, "typeVersion": 4.2}, {"id": "23d3665c-0dfe-470c-98b6-ac67bcd186ee", "name": "Message settings", "type": "n8n-nodes-base.set", "position": [680, 460], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "b640b120-cf83-4141-8a74-59da3ec1bb92", "name": "from", "type": "string", "value": "<EMAIL>"}, {"id": "a01d10b2-a61c-4173-b31c-b24c6c0859d4", "name": "to", "type": "string", "value": "<EMAIL>"}, {"id": "1173b361-ed4b-4c3d-af96-c66b9909a4c4", "name": "subject", "type": "string", "value": "Email with embedded image"}, {"id": "b6c8771a-f1c9-4952-9b9d-2684a8017ff4", "name": "body_html", "type": "string", "value": "=<p>This email contains an embedded image:</p>\n<p><img src='cid:image1'></p>"}]}}, "typeVersion": 3.3}, {"id": "f2586628-8664-442b-b822-2caa075f6f4d", "name": "Convert image to base64", "type": "n8n-nodes-base.extractFromFile", "position": [1120, 460], "parameters": {"options": {}, "operation": "binaryToPropery", "destinationKey": "chart1"}, "typeVersion": 1}, {"id": "69de86e7-eef2-4792-81db-1fdb930c7790", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [860, 340], "parameters": {"color": 7, "width": 168.75, "height": 281.25, "content": "Gets a random image from the internet. Replace this with your image (should be called 'data')"}, "typeVersion": 1}, {"id": "9bf60739-3388-4394-bec4-542ec3fddbb8", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1520, 340], "parameters": {"color": 7, "width": 168.75, "height": 281.25, "content": "We use an HTTP node rather than the Gmail node. Add your Gmail creds here"}, "typeVersion": 1}, {"id": "2700414e-3fb1-45de-9550-c1ffb5702b94", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [640, 340], "parameters": {"color": 7, "width": 168.75, "height": 281.25, "content": "To use the image in the body of the email, insert <img src='cid:image1'>"}, "typeVersion": 1}, {"id": "81d9af8b-b232-4d15-8c7a-c773a2fb7aa8", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [160, 360], "parameters": {"height": 205, "content": "## Try me out\n1. Make sure you add your Gmail credential in the last node\n2. Update the sender and recipient in the 'Message settings' node\n3. Click 'test workflow'"}, "typeVersion": 1}], "pinData": {}, "connections": {"Get image": {"main": [[{"node": "Convert image to base64", "type": "main", "index": 0}]]}, "Compose message": {"main": [[{"node": "Send message", "type": "main", "index": 0}]]}, "Message settings": {"main": [[{"node": "Get image", "type": "main", "index": 0}]]}, "Convert image to base64": {"main": [[{"node": "Compose message", "type": "main", "index": 0}]]}, "When clicking \"Test workflow\"": {"main": [[{"node": "Message settings", "type": "main", "index": 0}]]}}}