{"meta": {"instanceId": "cb484ba7b742928a2048bf8829668bed5b5ad9787579adea888f05980292a4a7", "templateCredsSetupCompleted": true}, "nodes": [{"id": "a2061232-329f-4288-9b01-ba832463c31e", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [2280, -400], "parameters": {}, "typeVersion": 1}, {"id": "42df9296-82ac-44cd-8370-50e4507fb91d", "name": "Check if Product Data Found", "type": "n8n-nodes-base.if", "position": [2800, -340], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "1a67895e-3ab7-4c93-8e16-202b3882ded5", "operator": {"type": "array", "operation": "lengthGte", "rightType": "number"}, "leftValue": "={{ $json.AIoutput.ProductFeedback }}", "rightValue": 1}]}}, "typeVersion": 2.2}, {"id": "84e93120-92d8-45fd-bb63-8626743e7fe0", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [2720, -580], "parameters": {"color": 7, "width": 1340, "height": 440, "content": "## Product Data Processing"}, "typeVersion": 1}, {"id": "5cb1df66-abba-4d82-8fe5-c2313c8f7b44", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [2140, -760], "parameters": {"color": 7, "width": 560, "height": 620, "content": "## Receives AI Data from other workflow\n"}, "typeVersion": 1}, {"id": "7c046627-f418-4b7e-aa5b-7cff69f98f59", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [1780, -960], "parameters": {"width": 340, "height": 820, "content": "![Callforge](https://uploads.n8n.io/templates/callforgeshadow.png)\n## CallForge - The AI Gong Sales Call Processor\nCallForge allows you to extract important information for different departments from your Sales Gong Calls. \n\n### AI Output Processor\nOnce the AI data is generated, it is then added (or not!) to the Notion Database here. This is also where the Pipedrive or Salesforce integration will be added once that portion is complete. "}, "typeVersion": 1}, {"id": "a04dac9d-5477-41a3-8696-1871c1cccf53", "name": "Create Product Data Object1", "type": "n8n-nodes-base.notion", "position": [3280, -940], "parameters": {"title": "={{ $('Execute Workflow Trigger').item.json.metaData.title }}", "options": {"icon": "💬"}, "resource": "databasePage", "databaseId": {"__rl": true, "mode": "list", "value": "1775b6e0-c94f-80ac-9885-d9695af5bc89", "cachedResultUrl": "https://www.notion.so/1775b6e0c94f80ac9885d9695af5bc89", "cachedResultName": "AI use-case database"}, "propertiesUi": {"propertyValues": [{"key": "Company|title", "title": "={{ $json.metaData.CompanyName }}"}, {"key": "Department|multi_select", "multiSelectValue": "={{ $json.AIoutput.AI_ML_References.Details.Department }}"}, {"key": "Dev status|select", "selectValue": "={{ $json.AIoutput.AI_ML_References.Details.DevelopmentStatus }}"}, {"key": "Employees|select", "selectValue": "={{ $json.sfOpp[0].Employees }}"}, {"key": "Engagement with n8n|select", "selectValue": "Prospect"}, {"key": "Requires agents|checkbox", "checkboxValue": "={{ $json.AIoutput.AI_ML_References.Details.RequiresAgents }}"}, {"key": "More info|url", "urlValue": "={{ $json.metaData.url }}"}, {"key": "Requires RAG|checkbox", "checkboxValue": "={{ $json.AIoutput.AI_ML_References.Details.RequiresRAG }}"}, {"key": "Requires chat|select", "selectValue": "={{ $json.AIoutput.AI_ML_References.Details.RequiresChat }}"}, {"key": "Use case|rich_text", "textContent": "={{ $json.AIoutput.AI_ML_References.Context }}"}]}}, "credentials": {"notionApi": {"id": "2B3YIiD4FMsF9Rjn", "name": "Angelbot Notion"}}, "retryOnFail": true, "typeVersion": 2.2, "waitBetweenTries": 3000}, {"id": "66c252a9-e330-4742-84d1-d17042585f79", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [2720, -1040], "parameters": {"color": 7, "width": 1340, "height": 440, "content": "## AI use Case "}, "typeVersion": 1}, {"id": "caded10f-8662-4a2b-ab47-b1a825c39c4b", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [2720, -120], "parameters": {"color": 7, "width": 1340, "height": 360, "content": "## AI Mentioned on call"}, "typeVersion": 1}, {"id": "750c2853-3653-4557-b636-354fd91f846b", "name": "Create Product Feedback Data Object", "type": "n8n-nodes-base.notion", "position": [3440, -480], "parameters": {"title": "={{ $('Execute Workflow Trigger').item.json.metaData.title }}", "options": {"icon": "💬"}, "resource": "databasePage", "databaseId": {"__rl": true, "mode": "list", "value": "1375b6e0-c94f-80a8-93c9-c623b76dd14a", "cachedResultUrl": "https://www.notion.so/1375b6e0c94f80a893c9c623b76dd14a", "cachedResultName": "Product Feedback"}, "propertiesUi": {"propertyValues": [{"key": "Sentiment|multi_select", "multiSelectValue": "={{ $json.Sentiment }}"}, {"key": "Feedback|title", "title": "={{ $json.<PERSON><PERSON> }}"}, {"key": "Feedback Date|date", "date": "={{ $('Execute Workflow Trigger').item.json.metaData.started }}"}, {"key": "Sales Call Summaries|relation", "relationValue": ["={{ $('Execute Workflow Trigger').item.json.notionData[0].id }}"]}]}}, "credentials": {"notionApi": {"id": "80", "name": "Notion david-internal"}}, "retryOnFail": true, "typeVersion": 2.2, "waitBetweenTries": 3000}, {"id": "343f536f-2aa3-4fc9-9c75-e288a5019b84", "name": "Check if AI Use Case Data Found", "type": "n8n-nodes-base.if", "position": [2800, -800], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "1a67895e-3ab7-4c93-8e16-202b3882ded5", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.AIoutput.AI_ML_References.Exist }}", "rightValue": 1}]}}, "typeVersion": 2.2}, {"id": "3d261de2-61fe-40e8-806b-f311b72081f0", "name": "Check if AI Mentioned On Call", "type": "n8n-nodes-base.if", "position": [2860, 40], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "1a67895e-3ab7-4c93-8e16-202b3882ded5", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.AIoutput.AI_ML_References.Exist }}", "rightValue": 1}]}}, "typeVersion": 2.2}, {"id": "e422c25b-05c0-4549-a12b-50b727cbcb83", "name": "Wait for rate limiting - AI Use Case", "type": "n8n-nodes-base.wait", "position": [3020, -940], "webhookId": "a26d4c04-4092-45fb-9ba3-d6c70ac0934c", "parameters": {"amount": 3}, "typeVersion": 1.1}, {"id": "9ceb4ac2-6539-4c19-b207-883d61670c07", "name": "Wait for rate limiting - Product Data", "type": "n8n-nodes-base.wait", "position": [3020, -480], "webhookId": "04bed240-5bae-4524-bb6f-011d8a6e1431", "parameters": {"amount": 3}, "typeVersion": 1.1}, {"id": "61d6864c-a7fa-488e-a252-f60b497de675", "name": "Split Out Product Data", "type": "n8n-nodes-base.splitOut", "position": [3220, -480], "parameters": {"options": {}, "fieldToSplitOut": "AIoutput.ProductFeedback"}, "typeVersion": 1}, {"id": "49bd2056-4eeb-43d7-a210-e4b777fd8535", "name": "Bundle AI Use Case Data to 1 object", "type": "n8n-nodes-base.aggregate", "position": [3540, -940], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "tagdata"}, "typeVersion": 1}, {"id": "ce6e127d-9ff0-493c-bb47-02c30594f0e2", "name": "Bundle Product Feedback Data to 1 object", "type": "n8n-nodes-base.aggregate", "position": [3660, -480], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "tagdata"}, "typeVersion": 1}, {"id": "ce06a39c-8066-4a3a-9ef4-b8bf6d14273a", "name": "Merge AI Use Case Thread", "type": "n8n-nodes-base.set", "position": [3860, -780], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d8fc65ad-2b05-40c1-84c7-7bda819f0f1f", "name": "aiResponse", "type": "object", "value": "={{ $('Execute Workflow Trigger').item.json.aiResponse }}"}]}}, "typeVersion": 3.4}, {"id": "1d64eff6-442a-4f71-a497-d6261bf4753f", "name": "Merge Product Feedback Thread", "type": "n8n-nodes-base.set", "position": [3880, -320], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d8fc65ad-2b05-40c1-84c7-7bda819f0f1f", "name": "aiResponse", "type": "object", "value": "={{ $('Execute Workflow Trigger').item.json.aiResponse }}"}]}}, "typeVersion": 3.4}, {"id": "50116044-d468-4f07-a711-8373c1b26e94", "name": "Update Call with AI Data Summary", "type": "n8n-nodes-base.notion", "position": [3180, -40], "parameters": {"pageId": {"__rl": true, "mode": "id", "value": "={{ $('Execute Workflow Trigger').item.json.notionData[0].id }}"}, "options": {}, "resource": "databasePage", "operation": "update", "propertiesUi": {"propertyValues": [{"key": "AI Related|checkbox", "checkboxValue": "={{ $json.AIoutput.AI_ML_References.Exist }}"}, {"key": "AI Summary|rich_text", "textContent": "={{ $json.AIoutput.AI_ML_References.Context }}"}]}}, "credentials": {"notionApi": {"id": "80", "name": "Notion david-internal"}}, "retryOnFail": true, "typeVersion": 2.2, "waitBetweenTries": 3000}], "pinData": {}, "connections": {"Split Out Product Data": {"main": [[{"node": "Create Product Feedback Data Object", "type": "main", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "Check if Product Data Found", "type": "main", "index": 0}, {"node": "Check if AI Use Case Data Found", "type": "main", "index": 0}, {"node": "Check if AI Mentioned On Call", "type": "main", "index": 0}]]}, "Check if Product Data Found": {"main": [[{"node": "Wait for rate limiting - Product Data", "type": "main", "index": 0}], [{"node": "Merge Product Feedback Thread", "type": "main", "index": 0}]]}, "Create Product Data Object1": {"main": [[{"node": "Bundle AI Use Case Data to 1 object", "type": "main", "index": 0}]]}, "Check if AI Mentioned On Call": {"main": [[{"node": "Update Call with AI Data Summary", "type": "main", "index": 0}]]}, "Merge Product Feedback Thread": {"main": [[]]}, "Check if AI Use Case Data Found": {"main": [[{"node": "Wait for rate limiting - AI Use Case", "type": "main", "index": 0}], [{"node": "Merge AI Use Case Thread", "type": "main", "index": 0}]]}, "Bundle AI Use Case Data to 1 object": {"main": [[{"node": "Merge AI Use Case Thread", "type": "main", "index": 0}]]}, "Create Product Feedback Data Object": {"main": [[{"node": "Bundle Product Feedback Data to 1 object", "type": "main", "index": 0}]]}, "Wait for rate limiting - AI Use Case": {"main": [[{"node": "Create Product Data Object1", "type": "main", "index": 0}]]}, "Wait for rate limiting - Product Data": {"main": [[{"node": "Split Out Product Data", "type": "main", "index": 0}]]}, "Bundle Product Feedback Data to 1 object": {"main": [[{"node": "Merge Product Feedback Thread", "type": "main", "index": 0}]]}}}