{"nodes": [{"name": "QuickBooks2", "type": "n8n-nodes-base.quickbooks", "position": [870, 300], "parameters": {"email": "", "resource": "invoice", "invoiceId": "={{$json[\"Id\"]}}", "operation": "send"}, "credentials": {"quickBooksOAuth2Api": "QuickBooks OAuth Credentials"}, "typeVersion": 1}, {"name": "QuickBooks1", "type": "n8n-nodes-base.quickbooks", "position": [670, 300], "parameters": {"Line": [{"Amount": 100, "itemId": "1", "DetailType": "SalesItemLineDetail", "Description": "Consulting service"}], "resource": "invoice", "operation": "create", "CustomerRef": "={{$json[\"Id\"]}}", "additionalFields": {}}, "credentials": {"quickBooksOAuth2Api": "QuickBooks OAuth Credentials"}, "typeVersion": 1}, {"name": "QuickBooks", "type": "n8n-nodes-base.quickbooks", "position": [470, 300], "parameters": {"operation": "create", "displayName": "<PERSON>", "additionalFields": {"PrimaryEmailAddr": "<EMAIL>"}}, "credentials": {"quickBooksOAuth2Api": "QuickBooks OAuth Credentials"}, "typeVersion": 1}], "connections": {"QuickBooks": {"main": [[{"node": "QuickBooks1", "type": "main", "index": 0}]]}, "QuickBooks1": {"main": [[{"node": "QuickBooks2", "type": "main", "index": 0}]]}}}