{"id": "58", "name": "Turn on a light and set its brightness", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [590, 260], "parameters": {}, "typeVersion": 1}, {"name": "<PERSON>", "type": "n8n-nodes-base.philipsHue", "position": [790, 260], "parameters": {"lightId": "123", "additionalFields": {"bri": 90}}, "credentials": {"philipsHueOAuth2Api": "philips-hue"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"On clicking 'execute'": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}}}