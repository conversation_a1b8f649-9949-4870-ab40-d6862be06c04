{"id": "YoUP55V241b9F2ze", "meta": {"instanceId": "35ec7a1e5284dd5dab4dac454bbb30405138d2784c99e56ef8887a4fa9cd1977", "templateCredsSetupCompleted": true}, "name": "Qdrant Vector Database Embedding Pipeline", "tags": [], "nodes": [{"id": "934ffad4-c93e-40c1-b4fd-1c09b518a9c3", "name": "Qdrant Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "position": [460, -460], "parameters": {"mode": "insert", "options": {}, "qdrantCollection": {"__rl": true, "mode": "list", "value": "sv_lang_data", "cachedResultName": "sv_lang_data"}, "embeddingBatchSize": 100}, "credentials": {"qdrantApi": {"id": "vUb9tbEnXzu7uNUb", "name": "QdrantApi svenska"}}, "typeVersion": 1.1}, {"id": "4127d85d-45c9-4536-a15d-08af9dfdcfa8", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-960, -460], "parameters": {}, "typeVersion": 1}, {"id": "abb61b81-72e0-468e-855b-72402db828fc", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [400, -240], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "kftHaZgVKiB9BmKU", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "e9ae24be-6da9-4c04-b891-7e450f505e02", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [780, -180], "parameters": {"options": {}, "dataType": "binary"}, "typeVersion": 1}, {"id": "9aff896d-4edb-494c-b84f-ede4e47db1e3", "name": "Character Text Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterCharacterTextSplitter", "position": [800, 20], "parameters": {"separator": "\"chunk_id\""}, "typeVersion": 1}, {"id": "a083a47e-a835-4323-86a8-a2eaed226aaa", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-760, -680], "parameters": {"color": 4, "width": 260, "height": 200, "content": "### Fetch JSON File List\n**Node:** FTP (all files)\n**Operation:** List\n**Path:** <file path>\n\nRecursively lists all .json files prepared for embedding."}, "typeVersion": 1}, {"id": "072ae9dc-c1cd-4ceb-954a-6b6b1b984e29", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-460, -660], "parameters": {"color": 5, "height": 180, "content": "### Iterate Over Files\n**Node:** Loop Over Items\n\nBatches each file path individually for processing."}, "typeVersion": 1}, {"id": "08d852f2-f1de-42ce-b882-1dc1343ed967", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-160, -700], "parameters": {"color": 4, "width": 420, "height": 220, "content": "### Download Each File\n**Node:** FTP (1 file download)\n\nDownloads the current file in binary form using:\n```\nPath = file_path/{{ $json.name }}\n```"}, "typeVersion": 1}, {"id": "905c3d74-2817-4aa3-865d-51e972cbbb5a", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [920, -80], "parameters": {"color": 3, "width": 320, "height": 400, "content": "### Parse JSON Document (Default Data Loader)\n**Node:** Default Data Loader\n**Loader Type**: binary\n- Converts JSON structure into a document format compatible with embedding.\n\n\n### Split into Smaller Chunks\n**Node:** Character Text Splitter\n**Split by:** \"chunk_id\" or custom logic based on chunk formatting\n\nOptional node if chunk size normalization is required before embedding."}, "typeVersion": 1}, {"id": "9fb8e5be-3ee1-42b4-a858-40bc6afcf457", "name": "List all the files", "type": "n8n-nodes-base.ftp", "position": [-700, -460], "parameters": {"path": "Oracle/AI/embedding/svenska", "operation": "list"}, "credentials": {"ftp": {"id": "JufoKeNjsIgbCBWe", "name": "FTP account"}}, "typeVersion": 1}, {"id": "6f8d0390-5851-44ca-9712-0ae51f9a22ef", "name": "Loop over one item", "type": "n8n-nodes-base.splitInBatches", "position": [-400, -460], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "1c89a4a9-ec68-4c48-b7bc-74f5b30d8ac2", "name": "Downloading item", "type": "n8n-nodes-base.ftp", "position": [-40, -440], "parameters": {"path": "=Oracle/AI/embedding/svenska/{{ $json.name }}", "binaryPropertyName": "binary.data"}, "credentials": {"ftp": {"id": "JufoKeNjsIgbCBWe", "name": "FTP account"}}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "01ca4ee3-5f1c-4977-a7f9-88e46db580ad", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [360, -960], "parameters": {"width": 480, "height": 460, "content": "### Store in Vector DB\n**Node:** Qdrant Vector Store\n**Batch Size:** 100\n\n**Collection:** <collection_name>\nSends cleaned text chunks to OpenAI to get embeddings (1536 dim for text-embedding-ada-002)\n\n#### collection settings in Qdrant cluster\n```\nPUT /collections/{collection_name}\n{\n    \"vectors\": {\n      \"size\": 1536,\n      \"distance\": \"Cosine\"\n    }\n}\n```\nEmbed Chunks\n**Node:** Embeddings OpenAI\nPushes the embedded chunks (with metadata) into Qdrant for semantic retrieval."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "c71fca63-26e9-4795-9a00-942dab6d07ce", "connections": {"Downloading item": {"main": [[{"node": "Qdrant Vector Store", "type": "main", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Qdrant Vector Store", "type": "ai_embedding", "index": 0}]]}, "List all the files": {"main": [[{"node": "Loop over one item", "type": "main", "index": 0}]]}, "Loop over one item": {"main": [[], [{"node": "Downloading item", "type": "main", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Qdrant Vector Store", "type": "ai_document", "index": 0}]]}, "Qdrant Vector Store": {"main": [[{"node": "List all the files", "type": "main", "index": 0}]]}, "Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "List all the files", "type": "main", "index": 0}]]}}}