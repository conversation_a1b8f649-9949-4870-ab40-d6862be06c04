{"id": 91, "name": "New invoice email notification", "nodes": [{"name": "Check for new emails", "type": "n8n-nodes-base.emailReadImap", "position": [500, 300], "parameters": {"format": "resolved", "mailbox": "Inbox", "options": {"allowUnauthorizedCerts": true}}, "credentials": {"imap": {"id": "24", "name": "GMAIL"}}, "typeVersion": 1}, {"name": "If email body contains invoice", "type": "n8n-nodes-base.if", "position": [700, 300], "parameters": {"conditions": {"string": [{"value1": "={{$json[\"text\"].toLowerCase()}}", "value2": "invoice", "operation": "contains"}]}, "combineOperation": "any"}, "typeVersion": 1}, {"name": "Extract the total amount", "type": "n8n-nodes-base.mindee", "position": [900, 280], "parameters": {"rawData": true, "resource": "invoice", "binaryPropertyName": "attachment_0"}, "credentials": {"mindeeInvoiceApi": {"id": "62", "name": "Mindee Invoice account"}}, "typeVersion": 1}, {"name": "Send new invoice notification", "type": "n8n-nodes-base.slack", "position": [1580, 300], "parameters": {"text": ":new: There is a new invoice to pay :new:", "channel": "team-accounts", "blocksUi": {"blocksValues": []}, "attachments": [{"color": "#FFBF00", "fields": {"item": [{"short": true, "title": "Amount", "value": "={{$node[\"If Amount > 1000\"].json[\"predictions\"][0][\"total_incl\"][\"amount\"]}}"}, {"short": false, "title": "From", "value": "={{$node[\"Check for new emails\"].json[\"from\"][\"value\"][0][\"address\"]}}"}, {"short": true, "title": "Subject", "value": "={{$node[\"Check for new emails\"].json[\"subject\"]}}"}]}, "footer": "=*Date:* {{$node[\"Check for new emails\"].json[\"date\"]}}"}], "otherOptions": {}}, "credentials": {"slackApi": {"id": "53", "name": "Slack Access Token"}}, "typeVersion": 1}, {"name": "Send email to finance manager", "type": "n8n-nodes-base.emailSend", "position": [1340, 120], "parameters": {"text": "Hi,\n\nThere is a new high value invoice to be paid that you may need to approve.\n\n~ n8n workflow", "options": {}, "subject": "New high value invoice", "toEmail": "<EMAIL>", "fromEmail": "<EMAIL>", "attachments": "attachment_0"}, "credentials": {"smtp": {"id": "26", "name": "mailtrap"}}, "typeVersion": 1}, {"name": "If Amount > 1000", "type": "n8n-nodes-base.if", "position": [1080, 280], "parameters": {"conditions": {"number": [{"value1": "={{$json[\"predictions\"][0][\"total_incl\"][\"amount\"]}}", "value2": 1000, "operation": "larger"}]}}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"If Amount > 1000": {"main": [[{"node": "Send email to finance manager", "type": "main", "index": 0}], [{"node": "Send new invoice notification", "type": "main", "index": 0}]]}, "Check for new emails": {"main": [[{"node": "If email body contains invoice", "type": "main", "index": 0}]]}, "Extract the total amount": {"main": [[{"node": "If Amount > 1000", "type": "main", "index": 0}]]}, "Send email to finance manager": {"main": [[{"node": "Send new invoice notification", "type": "main", "index": 0}]]}, "If email body contains invoice": {"main": [[{"node": "Extract the total amount", "type": "main", "index": 0}]]}}}