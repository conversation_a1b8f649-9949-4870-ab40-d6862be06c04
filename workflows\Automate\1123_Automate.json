{"nodes": [{"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.function", "position": [550, 300], "parameters": {"functionCode": "return [{json:[\"item-1\", \"item-2\", \"item-3\", \"item-4\"]}];"}, "typeVersion": 1}, {"name": "Function", "type": "n8n-nodes-base.function", "position": [750, 300], "parameters": {"functionCode": "return items[0].json.map(item => {\n  return {\n    json: {\n      data:item\n    },\n  }\n});\n"}, "typeVersion": 1}], "connections": {"Mock Data": {"main": [[{"node": "Function", "type": "main", "index": 0}]]}}}