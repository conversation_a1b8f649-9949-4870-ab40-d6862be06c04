{"meta": {"instanceId": "d68b0885df4f6057c27649c0cc1cdbf154a8c3c6de34051d82d8f9164d66f031"}, "nodes": [{"id": "648130c4-5195-4b91-995b-443624019cd0", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [820, 280], "parameters": {}, "typeVersion": 1}, {"id": "c25e5656-9ce2-4429-98f5-f86a88a8fe16", "name": "n8n1", "type": "n8n-nodes-base.n8n", "position": [2380, 140], "parameters": {"filters": {}, "options": {}, "resource": "execution", "returnAll": true, "requestOptions": {}}, "credentials": {"n8nApi": {"id": "23", "name": "n8n account"}}, "typeVersion": 1}, {"id": "93acd82f-22ce-435c-b89e-a3f8ae876bc5", "name": "n8n list execution", "type": "n8n-nodes-base.n8n", "position": [1040, 380], "parameters": {"filters": {}, "options": {}, "resource": "execution", "returnAll": true, "requestOptions": {}}, "credentials": {"n8nApi": {"id": "23", "name": "n8n account"}}, "typeVersion": 1}, {"id": "da03ff80-480d-4616-8aed-dd955d5e92d8", "name": "If", "type": "n8n-nodes-base.if", "position": [1260, 380], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "6a124591-3347-4224-a997-a7824de12c96", "operator": {"type": "dateTime", "operation": "before"}, "leftValue": "={{ $json.startedAt }}", "rightValue": "={{ new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString();  }}"}]}}, "typeVersion": 2.2}, {"id": "6bc96f0a-5ed9-43f9-91e8-ced15ae53ef5", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [820, 500], "parameters": {"rule": {"interval": [{"triggerAtHour": 4, "triggerAtMinute": 44}]}}, "typeVersion": 1.2}, {"id": "272f94d2-fcb5-4e6a-a32e-655ac1db9a00", "name": "delete execution", "type": "n8n-nodes-base.n8n", "position": [1480, 280], "parameters": {"resource": "execution", "operation": "delete", "executionId": "={{ $json.id }}", "requestOptions": {}}, "credentials": {"n8nApi": {"id": "23", "name": "n8n account"}}, "typeVersion": 1}, {"id": "b2067d59-3678-400a-a464-cb7aab62413f", "name": "No Operation, do nothing", "type": "n8n-nodes-base.noOp", "position": [1480, 480], "parameters": {}, "typeVersion": 1}], "pinData": {}, "connections": {"If": {"main": [[{"node": "delete execution", "type": "main", "index": 0}], [{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "n8n list execution", "type": "main", "index": 0}]]}, "n8n list execution": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "n8n list execution", "type": "main", "index": 0}]]}}}