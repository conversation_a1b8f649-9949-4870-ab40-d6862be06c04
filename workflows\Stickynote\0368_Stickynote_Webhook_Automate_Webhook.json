{"meta": {"instanceId": "257476b1ef58bf3cb6a46e65fac7ee34a53a5e1a8492d5c6e4da5f87c9b82833"}, "nodes": [{"id": "23dc1873-b376-473e-935b-b1df5e663c9e", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [1100, 1120], "webhookId": "c80ce133-899b-41c9-b2ae-2c876694f9fd", "parameters": {"path": "c80ce133-899b-41c9-b2ae-2c876694f9fd", "options": {}, "httpMethod": "POST"}, "typeVersion": 1}, {"id": "417ddfd1-8a27-498b-b203-0f410f8092b8", "name": "Set", "type": "n8n-nodes-base.set", "position": [1320, 1120], "parameters": {"values": {"string": [{"name": "email", "value": "={{ $json.body.data.results.emails[0].email }}"}, {"name": "firstname", "value": "={{ $json.body.data.results.firstname }}"}, {"name": "lastname", "value": "={{ $json.body.data.results.lastname }}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 2}, {"id": "ecf055ad-3f12-43c0-8dcc-0868fdfff5d8", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [660, 760], "parameters": {"height": 395, "content": "## Real-time listening and processing of search results with Icypeas.\n\n‍\n\nThis workflow, with the webhook, allows you to retrieve the results of your searches with Icypeas (https://www.icypeas.com/) and redirect them wherever you want."}, "typeVersion": 1}, {"id": "1c4410ef-d5c8-4da1-8f7a-104082a1aacd", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [980, 886], "parameters": {"width": 332, "height": 882.*************, "content": "## Link your Icypeas account with a webhook\n\n\n\nThe first node, « Webhook », is a webhook that needs to be referenced in the Icypeas application under the API section of the user’s profile (see documentation here : https://api-doc.icypeas.com/category/push-notifications/).\n\n\n\n‍\n\n\n\n\n\n\n\nYou need to copy the Test URL (don't forget to change it to the production URL before you active the workflow) provided by n8n when clicking on the node. \n\nThen go to the user profile in the Icypeas application (https://app.icypeas.com/bo/profile). After logging in, click on the profile icon > Select Your Profile > Go to the API section > Click on the Enable API Access button, and enter the URL in the field \"Notification when each row is treated during a bulk search\".\n\nSave the notification routes.\n\nThe webhook will be called by our system whenever a new result is available.\n\nThis allows for real-time notification of new results as they become available. \n\n\n**Be careful** : this template only works for email searches and domain scans. An adaptation for email verification will be made very soon."}, "typeVersion": 1}, {"id": "03417e50-c191-4f75-91e7-158a16e5ee55", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1310, 857], "parameters": {"width": 237, "height": 628, "content": "## Retrieve the relevant informations\n\n\n\nFinally, the « Set » node allows you to retrieve the relevant information from the search: name / first name / email.\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nFor your information, you can manipulate this webhook as you want. For example, you can add an additional node to redirect the responses to the service of your choice, like Lemlist (https://www.lemlist.com/). Simply click on the “+” and create a lead on Le<PERSON>list"}, "typeVersion": 1}], "pinData": {}, "connections": {"Webhook": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}}