{"id": "8jdT4wXjV5NljqKa", "meta": {"instanceId": "885b4fb4a6a9c2cb5621429a7b972df0d05bb724c20ac7dac7171b62f1c7ef40", "templateCredsSetupCompleted": true}, "name": "Enhance Chat Responses with Real-Time Search Data via Bright Data & Gemini AI", "tags": [{"id": "Kujft2FOjmOVQAmJ", "name": "Engineering", "createdAt": "2025-04-09T01:31:00.558Z", "updatedAt": "2025-04-09T01:31:00.558Z"}, {"id": "ZOwtAMLepQaGW76t", "name": "Building Blocks", "createdAt": "2025-04-13T15:23:40.462Z", "updatedAt": "2025-04-13T15:23:40.462Z"}, {"id": "ddPkw7Hg5dZhQu2w", "name": "AI", "createdAt": "2025-04-13T05:38:08.053Z", "updatedAt": "2025-04-13T05:38:08.053Z"}], "nodes": [{"id": "7294b048-5804-4620-a53e-52df293c3df1", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-460, 160], "webhookId": "3ad383ee-ded9-4a46-9165-9af0bad6c450", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "8ff09a26-ffa4-451d-9452-35b8f2936cab", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-140, 60], "parameters": {"options": {"systemMessage": "You are a helpful assistant.\n\nUse MCP Search Engine assistant tools for Bright Data for Google, Bing or Yandex Search. \n\nImportant: Return the response to <PERSON><PERSON> and also perform the webhook notification of responses.\n\nUse the relevant tool in the order of execution. "}}, "typeVersion": 1.8}, {"id": "********-7fe5-407d-aa34-96ac19b13284", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [-240, 280], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "YeO7dHZnuGBVQKVZ", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "b6d947d1-9752-4aff-834c-de99ff1ad903", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [-60, 280], "parameters": {}, "typeVersion": 1.3}, {"id": "73273d82-2a2f-41a2-ad1c-369f7a05ebe1", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-480, -200], "parameters": {}, "typeVersion": 1}, {"id": "********-03e0-46a2-ba3b-ab96aa14461e", "name": "MCP Client list all tools for Bright Data", "type": "n8n-nodes-mcp.mcpClient", "position": [-260, -200], "parameters": {}, "credentials": {"mcpClientApi": {"id": "JtatFSfA2kkwctYa", "name": "MCP Client (STDIO) account"}}, "typeVersion": 1}, {"id": "9d0d498f-10da-4a66-9e59-1773089d5d7c", "name": "MCP Client Bright Data Search Tool", "type": "n8n-nodes-mcp.mcpClient", "position": [160, -200], "parameters": {"toolName": "={{ $('MCP Client list all tools for Bright Data').item.json.tools[0].name }}", "operation": "executeTool", "toolParameters": "={\n   \"query\": \"{{ $json.search_query }}\",\n   \"engine\": \"google\"\n} "}, "credentials": {"mcpClientApi": {"id": "JtatFSfA2kkwctYa", "name": "MCP Client (STDIO) account"}}, "typeVersion": 1}, {"id": "346fd1f7-be97-47b6-b767-74382dc90979", "name": "Set search query", "type": "n8n-nodes-base.set", "position": [-60, -200], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "214e61a0-3587-453f-baf5-eac013990857", "name": "search_query", "type": "string", "value": "Bright Data"}]}}, "typeVersion": 3.4}, {"id": "1dc4dabe-d651-4b43-b561-4528be14e578", "name": "Google Search Engine for Bright Data", "type": "n8n-nodes-mcp.mcpClientTool", "notes": "Scrape search results from Google, Bing or Yandex. Returns SERP results in markdown (URL, title, description)", "position": [240, 540], "parameters": {"toolName": "search_engine", "operation": "executeTool", "toolParameters": "={\n   \"query\": \"{{ $json.chatInput }}\",\n   \"engine\": \"google\"\n}"}, "credentials": {"mcpClientApi": {"id": "JtatFSfA2kkwctYa", "name": "MCP Client (STDIO) account"}}, "notesInFlow": true, "typeVersion": 1}, {"id": "029f5e0e-070f-47a7-8c77-2b59ca01ada4", "name": "Bing Search Engine for Bright Data", "type": "n8n-nodes-mcp.mcpClientTool", "notes": "Scrape search results from Google, Bing or Yandex. Returns SERP results in markdown (URL, title, description)", "position": [40, 540], "parameters": {"toolName": "search_engine", "operation": "executeTool", "toolParameters": "={\n   \"query\": \"{{ $json.chatInput }}\",\n   \"engine\": \"bing\"\n} "}, "credentials": {"mcpClientApi": {"id": "JtatFSfA2kkwctYa", "name": "MCP Client (STDIO) account"}}, "notesInFlow": true, "typeVersion": 1}, {"id": "580d37de-deb9-49cf-b9b8-4d14edca28f2", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-40, 460], "parameters": {"color": 4, "width": 640, "height": 240, "content": "## Bright Data Search Engines"}, "typeVersion": 1}, {"id": "bb77ba7c-c70e-4912-96f6-4f63b966c7a9", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-100, -260], "parameters": {"color": 3, "width": 460, "height": 260, "content": "## Bright Data Google Search"}, "typeVersion": 1}, {"id": "ecdd9f42-f56c-4bdb-b778-cd3b7545bb37", "name": "MCP Client List all tools", "type": "n8n-nodes-mcp.mcpClientTool", "position": [260, 280], "parameters": {}, "credentials": {"mcpClientApi": {"id": "JtatFSfA2kkwctYa", "name": "MCP Client (STDIO) account"}}, "typeVersion": 1}, {"id": "a1adfa84-6e1a-4b5c-9148-feddb1e6ab72", "name": "HTTP Request for Webhook Notification", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [500, 240], "parameters": {"url": "https://webhook.site/daf9d591-a130-4010-b1d3-0c66f8fcf467", "method": "POST", "sendBody": true, "parametersBody": {"values": [{"name": "chat_response"}]}, "toolDescription": "Webhook notification for search responses"}, "typeVersion": 1.1}, {"id": "ae88bb19-170f-443f-b777-561cf2e3be25", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-100, -400], "parameters": {"width": 440, "height": 120, "content": "## Disclaimer\nThis template is only available on n8n self-hosted as it's making use of the community node for MCP Client."}, "typeVersion": 1}, {"id": "80ac697d-2c4a-4f97-82aa-edcabbf7ef6f", "name": "Yandex Search Engine for Bright Data", "type": "n8n-nodes-mcp.mcpClientTool", "notes": "Scrape search results from Google, Bing or Yandex. Returns SERP results in markdown (URL, title, description)", "position": [460, 540], "parameters": {"toolName": "search_engine", "operation": "executeTool", "toolParameters": "={\n   \"query\": \"{{ $json.chatInput }}\",\n   \"engine\": \"yandex\"\n}"}, "credentials": {"mcpClientApi": {"id": "JtatFSfA2kkwctYa", "name": "MCP Client (STDIO) account"}}, "notesInFlow": true, "typeVersion": 1}, {"id": "dfb2117d-782f-44d9-baca-1ee4b0fef863", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-940, -40], "parameters": {"color": 5, "width": 400, "height": 220, "content": "## Note\nUse Bright Data MCP Search Engine assistant tools to perform Google, Bing or Yandex Search.\n\nThe AI Agent will make use of suitable search engine-based tools, returns the response to <PERSON><PERSON> and also performs the Webhook notification call for sending the AI responses via the MCP Client tools.\n\nSource - https://github.com/luminati-io/brightdata-mcp"}, "typeVersion": 1}, {"id": "694b3381-8ebe-4afb-be93-019715c0c2cf", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-440, 460], "parameters": {"width": 300, "height": 180, "content": "## LLM Usage\nGoogle Gemini is employed by the AI agent to understand and interpret user queries. Based on this interpretation, the agent initiates a call to the appropriate MCP client to perform the required web search task."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "2382b23d-fd06-4f10-bcbd-f09a944a1c8d", "connections": {"Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Set search query": {"main": [[{"node": "MCP Client Bright Data Search Tool", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "MCP Client List all tools": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "MCP Client list all tools for Bright Data", "type": "main", "index": 0}]]}, "Bing Search Engine for Bright Data": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Google Search Engine for Bright Data": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Yandex Search Engine for Bright Data": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "HTTP Request for Webhook Notification": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "MCP Client list all tools for Bright Data": {"main": [[{"node": "Set search query", "type": "main", "index": 0}]]}}}