{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9"}, "nodes": [{"id": "7263f921-1622-47eb-903c-729a75965e20", "name": "About You", "type": "n8n-nodes-base.form", "position": [600, 200], "webhookId": "14efc5e8-0984-4ccb-a118-ce3492f8ea02", "parameters": {"options": {"formTitle": "Thanks For Signing Up!", "buttonLabel": "Continue (1 of 3)", "formDescription": "Before you go, we'd love to know more about you and why you're interested in our service. Complete the following questions for a nice treat at the end!\n\n* This survey is optional."}, "formFields": {"values": [{"fieldLabel": "First Name", "placeholder": "eg. <PERSON>", "requiredField": true}, {"fieldLabel": "Last Name", "placeholder": "eg. <PERSON><PERSON><PERSON><PERSON>", "requiredField": true}, {"fieldLabel": "Country/Region"}, {"fieldType": "dropdown", "fieldLabel": "Job Level", "fieldOptions": {"values": [{"option": "CEO"}, {"option": "VP"}, {"option": "Director"}, {"option": "Manager"}, {"option": "Non-manager"}, {"option": "Student or Intern"}, {"option": "Other"}]}, "requiredField": true}, {"fieldType": "dropdown", "fieldLabel": "Job Function", "multiselect": true, "fieldOptions": {"values": [{"option": "Accounting/Finance"}, {"option": "Admin/Office"}, {"option": "Customer Service"}, {"option": "Design"}, {"option": "Engineering/Software"}, {"option": "HR/Operations"}, {"option": "Leadership/Management"}, {"option": "Legal"}, {"option": "Other"}]}, "requiredField": true}]}}, "typeVersion": 1}, {"id": "590e8da4-e4b5-46de-af19-f07f82305c19", "name": "Your Interests", "type": "n8n-nodes-base.form", "position": [780, 200], "webhookId": "14efc5e8-0984-4ccb-a118-ce3492f8ea02", "parameters": {"options": {"formTitle": "What Brings You Here?", "buttonLabel": "Continue (2 of 3)", "formDescription": "Thanks <name>!\nPlease tell us why you are interested in our product? It'll help us tailor your onboarding and communication journeys to better suit your needs."}, "formFields": {"values": [{"fieldType": "dropdown", "fieldLabel": "How familiar are you with no-code automation?", "fieldOptions": {"values": [{"option": "I've Just started or exploring no-code automation tools"}, {"option": "I've tried tools like Zapier to automate small tasks"}, {"option": "I've built several no-code automations and workflows already"}]}, "requiredField": true}, {"fieldType": "textarea", "fieldLabel": "Describe briefly what you'd like to get out of our product?", "placeholder": "Eg. short term pain points and long term solutions...", "requiredField": true}]}}, "typeVersion": 1}, {"id": "c8f837be-4c09-4cf5-be33-913814d7b1c4", "name": "Join <PERSON> Testers", "type": "n8n-nodes-base.form", "position": [960, 200], "webhookId": "14efc5e8-0984-4ccb-a118-ce3492f8ea02", "parameters": {"options": {"formTitle": "Join Our Beta Testers List", "buttonLabel": "Finish (3 of 3)", "formDescription": "Finally, we're always looking for Beta testers to try out our latest features and help us figure out what works. Beta testers join on a voluntary basis but we often send little tokens of appreciation such as increased usage limits and sometimes brand merchandise!"}, "formFields": {"values": [{"fieldType": "dropdown", "fieldLabel": "Would you like to be considered for our beta testers list?", "fieldOptions": {"values": [{"option": "Yes"}, {"option": "No"}, {"option": "Maybe"}]}, "requiredField": true}]}}, "typeVersion": 1}, {"id": "9d8f8a98-7cf6-4dc9-bbed-b999dbdfc6d5", "name": "Sign Up Form", "type": "n8n-nodes-base.formTrigger", "position": [-120, 160], "webhookId": "c9deb1b7-52c5-4046-bb8f-7dcfdd00fa4b", "parameters": {"path": "newsletter-signup", "options": {"buttonLabel": "Sign Up to Newsletter", "appendAttribution": true, "useWorkflowTimezone": true}, "formTitle": "Sign Up for My Newsletter", "formFields": {"values": [{"fieldType": "email", "fieldLabel": "Email", "placeholder": "eg. <EMAIL>", "requiredField": true}]}, "responseMode": "lastNode", "formDescription": "Use this form to signup for my newsletter where members will receive the latest workflow templates from me before everyone else!\n\nYou can unsubscribe at any time."}, "typeVersion": 2.1}, {"id": "e7143922-7de1-448d-9abb-72034437f79c", "name": "Capture More Info", "type": "n8n-nodes-base.googleSheets", "position": [1140, 200], "parameters": {"columns": {"value": {"job_level": "={{ $('About You').item.json['Job Level'] }}", "last_name": "={{ $('About You').item.json['Last Name'] }}", "first_name": "={{ $('About You').item.json['First Name'] }}", "execution_id": "={{ $execution.id }}", "job_function": "={{ $('About You').item.json['Job Function'].join(', ') }}", "product_goals": "={{ $('Your Interests').item.json['Describe briefly what you\\'d like to get out of our product?'] }}", "country_region": "={{ $('About You').item.json['Country/Region'] }}", "enrol_betatesters": "={{ $json['Would you like to be considered for our beta testers list?'] }}", "product_experience": "={{ $('Your Interests').item.json['How familiar are you with no-code automation?'] }}"}, "schema": [{"id": "execution_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "execution_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date", "type": "string", "display": true, "removed": true, "required": false, "displayName": "date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "email", "type": "string", "display": true, "removed": true, "required": false, "displayName": "email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "first_name", "type": "string", "display": true, "required": false, "displayName": "first_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "last_name", "type": "string", "display": true, "required": false, "displayName": "last_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_level", "type": "string", "display": true, "required": false, "displayName": "job_level", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_function", "type": "string", "display": true, "required": false, "displayName": "job_function", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "country_region", "type": "string", "display": true, "removed": false, "required": false, "displayName": "country_region", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "product_experience", "type": "string", "display": true, "required": false, "displayName": "product_experience", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "product_goals", "type": "string", "display": true, "required": false, "displayName": "product_goals", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "enrol_betatesters", "type": "string", "display": true, "required": false, "displayName": "enrol_betatesters", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["execution_id"]}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/15W1PiFjCoiEBHHKKCRVMLmpKg4AWIy9w1dQ2Dq8qxPs/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "15W1PiFjCoiEBHHKKCRVMLmpKg4AWIy9w1dQ2Dq8qxPs", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/15W1PiFjCoiEBHHKKCRVMLmpKg4AWIy9w1dQ2Dq8qxPs/edit?usp=drivesdk", "cachedResultName": "Newsletter Signup"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "XHvC7jIRR8A2TlUl", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "0cacb296-0d12-44e5-a749-65aa2e89a42d", "name": "Capture Email", "type": "n8n-nodes-base.googleSheets", "position": [60, 160], "parameters": {"columns": {"value": {"date": "={{ $json.submittedAt }}", "email": "={{ $json.Email }}", "execution_id": "={{ $execution.id }}"}, "schema": [{"id": "execution_id", "type": "string", "display": true, "required": false, "displayName": "execution_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date", "type": "string", "display": true, "required": false, "displayName": "date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "email", "type": "string", "display": true, "required": false, "displayName": "email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "first_name", "type": "string", "display": true, "removed": true, "required": false, "displayName": "first_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "last_name", "type": "string", "display": true, "removed": true, "required": false, "displayName": "last_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_level", "type": "string", "display": true, "removed": true, "required": false, "displayName": "job_level", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_function", "type": "string", "display": true, "removed": true, "required": false, "displayName": "job_function", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "country_region", "type": "string", "display": true, "removed": true, "required": false, "displayName": "country_region", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "product_experience", "type": "string", "display": true, "removed": true, "required": false, "displayName": "product_experience", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "product_goals", "type": "string", "display": true, "removed": true, "required": false, "displayName": "product_goals", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "enrol_betatesters", "type": "string", "display": true, "removed": true, "required": false, "displayName": "enrol_betatesters", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": []}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/15W1PiFjCoiEBHHKKCRVMLmpKg4AWIy9w1dQ2Dq8qxPs/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "15W1PiFjCoiEBHHKKCRVMLmpKg4AWIy9w1dQ2Dq8qxPs", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/15W1PiFjCoiEBHHKKCRVMLmpKg4AWIy9w1dQ2Dq8qxPs/edit?usp=drivesdk", "cachedResultName": "Newsletter Signup"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "XHvC7jIRR8A2TlUl", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "9befb4d6-7c50-4acb-9972-97e95981632f", "name": "Show Completion Screen", "type": "n8n-nodes-base.form", "position": [1560, 140], "webhookId": "c1e775ff-f9fd-44ee-b4c6-257fdf291227", "parameters": {"options": {"formTitle": "NewsLetter Signup Short Survey Complete"}, "operation": "completion", "completionTitle": "Thank you!", "completionMessage": "Many thanks for taking the time to complete this short survey. A community representative will contact you shortly!\n\nWe hope you enjoy the newsletter and please feel free to contact us at <EMAIL> should you have any questions.\n\nGo back to <HOMEPAGE>."}, "typeVersion": 1}, {"id": "01b7b455-a64f-42a1-9c5a-f04908eced41", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-260, -120], "parameters": {"color": 7, "width": 740, "height": 480, "content": "## 1. Easy Lead Capture with n8n Forms\n[Learn more about Form Triggers](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.formtrigger)\n\nPreviously, the n8n form experience was quite limited as you were only given one form page to work with. Now with multi-page forms where its possible to link between them, you can get creative on providing a richer form experience for your users.\n\nHere, we start by capturing the most important information first - the user's email address - and saving it to our Google Sheet. We can then follow-up with an optional short onboarding survey to capture more details about the user if they are willing."}, "typeVersion": 1}, {"id": "00b6bcac-2c39-4b5c-aef6-bd6e2731240b", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [500, -60.69767441860472], "parameters": {"color": 7, "width": 840, "height": 460.6976744186047, "content": "## 2. Follow-on Short Survey via Multi-Step Forms\n[Read more about n8n Form node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.form/)\n\nMulti-page forms are built by simply chaining a series of n8n form nodes. n8n handles the progress of the form for you - ie. proceeds when the form validates and the user submits the form - which makes it easier to build as you don't need to add additional nodes in between.\n\nAfter the user provides their email, we present an optional short survey to capture additional details. This step is made of 3 form nodes capturing profession, experience and goals of the user which is then saved to the same row in the google sheet."}, "typeVersion": 1}, {"id": "e76311ce-ab8e-4563-9fe4-a58a7578b3d0", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1360, -60], "parameters": {"color": 7, "width": 500, "height": 460, "content": "## 3. Customise Your Completion Screen\n[Read more about n8n Form node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.form/)\n\nOnce complete, use the Form node in \"form ending\" page type to show the completion screen. This screen can be customised with a personal message or set to redirect the user depending on the use-case."}, "typeVersion": 1}, {"id": "56dc48c4-0232-4dce-bdb5-08e928389425", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-740, -300], "parameters": {"width": 440, "height": 660, "content": "## Try It Out!\n\n### This template builds a simple newsletter signup form with a follow-on short survey entirely in n8n! Taking full advantage of n8n's new multi-page form functionality, it's easy to build impactful forms to serve your business.\n\n### How it works\n* Our flow begins with a form trigger to capture a newsletter signup and the user's email is captured into a google sheet. Google Sheet is used for demonstration purposes but this could be any database.\n* Multi-page forms allow you to continue the onboarding experience with a short survey. 3 form nodes are chained to capture more details from the user which update the same row in the google sheet.\n* Finally, a form ending node shows a customised completion screen for our user.\n\nCheck out the example sheet here: https://docs.google.com/spreadsheets/d/15W1PiFjCoiEBHHKKCRVMLmpKg4AWIy9w1dQ2Dq8qxPs/edit?usp=sharing\n\n\n### Need Help?\nJoin the [Discord](https://discord.com/invite/XPKeKXeB7d) or ask in the [Forum](https://community.n8n.io/)!\n\nHappy Hacking!\n"}, "typeVersion": 1}, {"id": "8035269e-224f-4036-9e8a-9447cfa87252", "name": "Notify New Signup!", "type": "n8n-nodes-base.slack", "position": [240, 160], "webhookId": "1a9cb618-a2fd-4ee8-b3cf-4140b65d55c1", "parameters": {"text": "=A user signed up to the newsletter!", "select": "channel", "blocksUi": "={\n\t\"blocks\": [\n\t\t{\n\t\t\t\"type\": \"section\",\n\t\t\t\"text\": {\n\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\"text\": \"{{ $('Sign Up Form').first().json.Email.extractEmail() }} *just signed up to the newsletter!*\"\n\t\t\t}\n\t\t}\n\t]\n}", "channelId": {"__rl": true, "mode": "name", "value": "#general"}, "messageType": "block", "otherOptions": {}}, "credentials": {"slackApi": {"id": "VfK3js0YdqBdQLGP", "name": "Slack account"}}, "typeVersion": 2.2}], "pinData": {}, "connections": {"About You": {"main": [[{"node": "Your Interests", "type": "main", "index": 0}]]}, "Sign Up Form": {"main": [[{"node": "Capture Email", "type": "main", "index": 0}]]}, "Capture Email": {"main": [[{"node": "Notify New Signup!", "type": "main", "index": 0}]]}, "Your Interests": {"main": [[{"node": "Join <PERSON> Testers", "type": "main", "index": 0}]]}, "Capture More Info": {"main": [[{"node": "Show Completion Screen", "type": "main", "index": 0}]]}, "Join Beta Testers": {"main": [[{"node": "Capture More Info", "type": "main", "index": 0}]]}, "Notify New Signup!": {"main": [[{"node": "About You", "type": "main", "index": 0}]]}}}