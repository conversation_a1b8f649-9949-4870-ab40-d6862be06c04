{"\"nodes\"": "[", "\"name\"": "\"NoOp\",", "\"type\"": "\"main\",", "\"position\"": "[", "\"webhookId\"": "\"2d0805da-143e-40c9-b327-242b1f052c31\",", "\"parameters\"": "{},", "\"updates\"": "[", "\"additionalFields\"": "{", "\"credentials\"": "{", "\"telegramApi\"": "\"telegram_habot\"", "\"typeVersion\"": "1", "\"text\"": "\"I don't tolerate toxic language!\",", "\"options\"": "{", "\"languages\"": "\"en\"", "\"requestedAttributesUi\"": "{", "\"requestedAttributesValues\"": "[", "\"attributeName\"": "\"profanity\"", "\"googlePerspectiveOAuth2Api\"": "\"perspective_api\"", "\"conditions\"": "{", "\"number\"": "[", "\"value1\"": "\"={{$json[\\\"attributeScores\\\"][\\\"PROFANITY\\\"][\\\"summaryScore\\\"][\\\"value\\\"]}}\",", "\"value2\"": "0.7,", "\"operation\"": "\"larger\"", "\"chatId\"": "\"={{$node[\\\"Telegram Trigger\\\"].json[\\\"message\\\"][\\\"chat\\\"][\\\"id\\\"]}}\",", "\"reply_to_message_id\"": "\"={{$node[\\\"Telegram Trigger\\\"].json[\\\"message\\\"][\\\"message_id\\\"]}}\"", "\"connections\"": "{", "\"IF\"": "{", "\"main\"": "[", "\"node\"": "\"IF\",", "\"index\"": "0", "\"Telegram Trigger\"": "{", "\"Google Perspective\"": "{"}