{"id": "2Eba0OHGtOmoTWOU", "meta": {"instanceId": "9219ebc7795bea866f70aa3d977d54417fdf06c41944be95e20cfb60f992db19", "templateCredsSetupCompleted": true}, "name": "RAG AI Agent with Milvus and Cohere", "tags": [{"id": "yj7cF3GCsZiargFT", "name": "rag", "createdAt": "2025-05-03T17:14:30.099Z", "updatedAt": "2025-05-03T17:14:30.099Z"}], "nodes": [{"id": "361065cc-edbf-47da-8da7-c59b564db6f3", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [0, 320], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "a01b9512-ced1-4e28-a2aa-88077ab79d9a", "name": "Embeddings Cohere", "type": "@n8n/n8n-nodes-langchain.embeddingsCohere", "position": [-140, 320], "parameters": {"modelName": "embed-multilingual-v3.0"}, "credentials": {"cohereApi": {"id": "8gcYMleu1b8Hm03D", "name": "CohereApi account"}}, "typeVersion": 1}, {"id": "1da6ea4b-de88-44d3-a215-78c55b5592a2", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-800, 520], "webhookId": "a4257301-3fb9-4b9d-a965-1fa66f314696", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "********-3f6d-4909-a626-0eba0557a5bd", "name": "Watch New Files", "type": "n8n-nodes-base.googleDriveTrigger", "position": [-800, 100], "parameters": {"event": "fileCreated", "options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "mode": "list", "value": "15gjDQZiHZuBeVscnK8Ic_kIWt3mOaVfs", "cachedResultUrl": "https://drive.google.com/drive/folders/15gjDQZiHZuBeVscnK8Ic_kIWt3mOaVfs", "cachedResultName": "RAG template"}}, "credentials": {"googleDriveOAuth2Api": {"id": "r1DVmNxwkIL8JO17", "name": "Google Drive account"}}, "typeVersion": 1}, {"id": "001fbdbe-dfcb-4552-bf09-de416b253389", "name": "Download New", "type": "n8n-nodes-base.googleDrive", "position": [-580, 100], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "options": {}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "r1DVmNxwkIL8JO17", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "c1116cba-beb9-4d28-843d-c5c21c0643de", "name": "Insert into Milvus", "type": "@n8n/n8n-nodes-langchain.vectorStoreMilvus", "position": [-124, 100], "parameters": {"mode": "insert", "options": {"clearCollection": false}, "milvusCollection": {"__rl": true, "mode": "list", "value": "collectionName", "cachedResultName": "collectionName"}}, "credentials": {"milvusApi": {"id": "Gpsxqr2l9Qxu48h0", "name": "Mil<PERSON>s account"}}, "typeVersion": 1.1}, {"id": "2dbc7139-46f6-41d8-8c13-9fafad5aec55", "name": "RAG Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-540, 520], "parameters": {"options": {}}, "typeVersion": 1.8}, {"id": "a103506e-9019-41f2-9b0d-9b831434c9e9", "name": "Retrieve from Milvus", "type": "@n8n/n8n-nodes-langchain.vectorStoreMilvus", "position": [-340, 740], "parameters": {"mode": "retrieve-as-tool", "topK": 10, "toolName": "vector_store", "toolDescription": "You are an AI agent that responds based on information received from a vector database.", "milvusCollection": {"__rl": true, "mode": "list", "value": "collectionName", "cachedResultName": "collectionName"}}, "credentials": {"milvusApi": {"id": "Gpsxqr2l9Qxu48h0", "name": "Mil<PERSON>s account"}}, "typeVersion": 1.1}, {"id": "74ccdff1-b976-4e1c-a2c4-237ffff19e34", "name": "OpenAI 4o", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-580, 740], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "gpt-4o"}, "options": {}}, "credentials": {"openAiApi": {"id": "vupAk5StuhOafQcb", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "36e35eaf-f723-4eeb-9658-143d5bc390a0", "name": "Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [-460, 740], "parameters": {}, "typeVersion": 1.3}, {"id": "ec7b6b92-065c-455c-a3f0-17586d9e48d7", "name": "Cohere embeddings", "type": "@n8n/n8n-nodes-langchain.embeddingsCohere", "position": [-220, 900], "parameters": {"modelName": "embed-multilingual-v3.0"}, "credentials": {"cohereApi": {"id": "8gcYMleu1b8Hm03D", "name": "CohereApi account"}}, "typeVersion": 1}, {"id": "3c3a8900-0b98-4479-8602-16b21e011ba1", "name": "Set <PERSON>", "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "position": [80, 480], "parameters": {"options": {}, "chunkSize": 700, "chunkOverlap": 60}, "typeVersion": 1}, {"id": "3a43bf1a-7e22-4b5e-bbb1-6bb2c1798c07", "name": "Extract from File", "type": "n8n-nodes-base.extractFromFile", "position": [-360, 100], "parameters": {"options": {}, "operation": "pdf"}, "typeVersion": 1}, {"id": "e0c9d4d7-5e3e-4e47-bb1f-dbdca360b20a", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1440, 120], "parameters": {"color": 2, "width": 540, "height": 600, "content": "## Why Milvus\nBased on comparisons and user feedback, **Milvus is often considered a more performant and scalable vector database solution compared to Supabase**, particularly for demanding use cases involving large datasets, high-volume vector search operations, and multilingual support.\n\n\n### Requirements\n- Create an account on [<PERSON><PERSON><PERSON>](https://zilliz.com/) to generate the Milvus cluster. \n- There is no need to create docker containers or your own instance, Zilliz provides the cloud infraestructure to build it easily\n- Get your credentials ready from Drive, Milvus (Zilliz), and [Cohere](https://cohere.com)\n\n### Usage\nEvery time a new pdf is added into the Drive folder, it will be inserted into the Milvus Vector Store, allowing for the interaction with the RAG agent in seconds.\n\n## Calculate your company's RAG costs\n\nWant to run Milvus on your own server on n8n? Zilliz provides a great [cost calculator](https://zilliz.com/rag-cost-calculator/)\n\n### Get in touch with us\nWant to implement a RAG AI agent for your company? [Shoot us a message](https://1node.ai)\n"}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "8b5fc2b8-50f7-425c-8fc8-94ba4f76ecf3", "connections": {"Memory": {"ai_memory": [[{"node": "RAG Agent", "type": "ai_memory", "index": 0}]]}, "OpenAI 4o": {"ai_languageModel": [[{"node": "RAG Agent", "type": "ai_languageModel", "index": 0}]]}, "Set Chunks": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Download New": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "Watch New Files": {"main": [[{"node": "Download New", "type": "main", "index": 0}]]}, "Cohere embeddings": {"ai_embedding": [[{"node": "Retrieve from Milvus", "type": "ai_embedding", "index": 0}]]}, "Embeddings Cohere": {"ai_embedding": [[{"node": "Insert into Milvus", "type": "ai_embedding", "index": 0}]]}, "Extract from File": {"main": [[{"node": "Insert into Milvus", "type": "main", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Insert into Milvus", "type": "ai_document", "index": 0}]]}, "Retrieve from Milvus": {"ai_tool": [[{"node": "RAG Agent", "type": "ai_tool", "index": 0}]]}, "When chat message received": {"main": [[{"node": "RAG Agent", "type": "main", "index": 0}]]}}}