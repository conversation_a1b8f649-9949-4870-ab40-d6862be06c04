{"meta": {"instanceId": "8eadf351d49a11e77d3a57adf374670f06c5294af8b1b7c86a1123340397e728"}, "nodes": [{"id": "f28a0602-f02c-4f41-8bbf-dfd46d0def87", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [2020, 620], "parameters": {"options": {}, "fieldToSplitOut": "Email"}, "typeVersion": 1}, {"id": "d995d088-9be1-4a64-a533-d764587b3ae4", "name": "Remove Duplicates", "type": "n8n-nodes-base.removeDuplicates", "onError": "continueRegularOutput", "position": [2480, 600], "parameters": {}, "retryOnFail": true, "typeVersion": 1}, {"id": "b64f9bc5-7e85-41df-b27c-10d53df6809f", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [2740, 600], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "466cf9ce-4baf-45f9-bd70-d2041c20605e", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1204.5476340072564, 520], "parameters": {"color": 4, "width": 1764.2311804548722, "height": 309.99889350400827, "content": "\n* Scraping emails from websites using an api"}, "typeVersion": 1}, {"id": "566ca1f5-b6c4-4566-97e7-59bc2d616e1c", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1300, 800], "parameters": {"color": 5, "width": 520.3009626085002, "height": 249.39327996703526, "content": "\n* Call the webhook using a query parameter eg \n\nhttp://localhost:5678/webhook/ea568868-5770-4b2a-8893-7e?Website=https://mailsafi.com\n\nHTTP request rest the query Website and gets the emails therein"}, "typeVersion": 1}, {"id": "ea95c9a3-b7c8-4288-8fdf-6504caee46f4", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [440, 380], "parameters": {"width": 728.4741979436378, "height": 430.0825742795921, "content": "# How to scrap emails from websites\n\nThis workflow shows how you can quickly build an Email scraping API using n8n.\nUsage\nCopy the webhook URL to your browser and add a query parameter eg {{$n8nhosteingurl/webhook/ea568868-5770-4b2a-8893-700b344c995e?Website=https://mailsafi.com\nThis will return the email address on the website or if there is no email, the response will be \"workflow successfully executed\"\n\n# Make sure to use HTTP:// for your domains\n\nOtherwise, you may get an error. \n\n\n\n"}, "typeVersion": 1}, {"id": "05d4e9d4-d803-4e74-b4d0-166f4873dbca", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [1360, 620], "webhookId": "ea568868-5770-4b2a-8893-700b344c995e", "parameters": {"path": "ea568868-5770-4b2a-8893-700b344c995e", "options": {}, "responseMode": "responseNode"}, "typeVersion": 1.1}, {"id": "555c8f81-25ea-4be5-b260-7b6039c705a8", "name": "Get the website data", "type": "n8n-nodes-base.httpRequest", "onError": "continueRegularOutput", "position": [1600, 620], "parameters": {"url": "={{ $json.query['Website'] }}", "options": {}}, "retryOnFail": true, "typeVersion": 4.1}, {"id": "e83b38b8-dc13-49eb-9482-1dbd8a9ef583", "name": "Extract the emails found", "type": "n8n-nodes-base.set", "position": [1800, 620], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "80a8a8ec-9ac7-4545-beab-390732218548", "name": "Email", "type": "array", "value": "={{$json.data.match(/(?:[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,})/g)}}"}]}}, "typeVersion": 3.3}, {"id": "3fe56efc-0d7b-4e0f-8f9c-3b10ce59cb94", "name": "If contains email", "type": "n8n-nodes-base.if", "position": [2220, 620], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "701ead8f-02ba-4689-8054-9e40d9b9f770", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.Email }}", "rightValue": ""}]}}, "typeVersion": 2}], "pinData": {}, "connections": {"Webhook": {"main": [[{"node": "Get the website data", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "If contains email", "type": "main", "index": 0}]]}, "If contains email": {"main": [[{"node": "Remove Duplicates", "type": "main", "index": 0}]]}, "Remove Duplicates": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Get the website data": {"main": [[{"node": "Extract the emails found", "type": "main", "index": 0}]]}, "Extract the emails found": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}}}