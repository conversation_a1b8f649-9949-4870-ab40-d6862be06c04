{"meta": {"instanceId": "26ba763460b97c249b82942b23b6384876dfeb9327513332e743c5f6219c2b8e"}, "nodes": [{"id": "51dbe3b4-42f6-43c9-85dc-42ae49be6ba9", "name": "Get RFP Data", "type": "n8n-nodes-base.extractFromFile", "position": [1003, 278], "parameters": {"options": {}, "operation": "pdf"}, "typeVersion": 1}, {"id": "c42e6bfc-a426-4d12-bf95-f3fe6e944631", "name": "Item List Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserItemList", "position": [2140, 540], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "1703e9c3-f49e-4272-ad11-0b9d4e9a76c6", "name": "For Each Question...", "type": "n8n-nodes-base.splitInBatches", "position": [2460, 340], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "a54fa4ee-6f67-41a9-89fe-fd9f2bf094de", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [760, 60], "parameters": {"color": 7, "width": 532.597092515486, "height": 508.1316876142587, "content": "## 1. API to Trigger Workflow\n[Read more about using Webhooks](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.webhook/)\n\nThis workflow requires the user to submit the RFP document via an API request. It's a common pattern to use the webhook node for this purpose. Be sure to secure this webhook endpoint in production!"}, "typeVersion": 1}, {"id": "fdef005f-7838-4b8c-8af4-4b7c6f947ee2", "name": "Set Variables", "type": "n8n-nodes-base.set", "position": [1143, 278], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "={\n  \"doc_title\": \"{{ $('Wait for Request').item.json.body.title }}\",\n  \"doc_filename\": \"{{ $('Wait for Request').item.json.body.id }} | {{ $('Wait for Request').item.json.body.title }} | {{ $now.format('yyyyMMddhhmmss') }}| RFP Response\",\n  \"reply_to\": \"{{ $('Wait for Request').item.json.body.reply_to }}\"\n}\n"}, "typeVersion": 3.3}, {"id": "a64f6274-62fc-42fb-b7c7-5aa85746c621", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1320, 148.42417112849222], "parameters": {"color": 7, "width": 493.289385759178, "height": 418.29352785836636, "content": "## 2. Create a new Doc to Capture Responses For RFP Questions\n[Read more about working with Google Docs](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googledocs/)\n\nFor each RFP we process, let's create its very own document to store the results. It will serve as a draft document for the RFP response."}, "typeVersion": 1}, {"id": "2b3df6af-c1ab-44a1-8907-************", "name": "Create new RFP Response Document", "type": "n8n-nodes-base.googleDocs", "position": [1420, 340], "parameters": {"title": "={{ $json.doc_filename }}", "folderId": "=1y0I8MH32maIWCJh767mRE_NMHC6A3bUu"}, "credentials": {"googleDocsOAuth2Api": {"id": "V0G0vi1DRj7Cqbp9", "name": "Google Docs account"}}, "typeVersion": 2}, {"id": "0bf30bef-2910-432b-b5eb-dee3fe39b797", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1840, 110.**************], "parameters": {"color": 7, "width": 500.*************, "height": 599.*************, "content": "## 3. Identifying Questions using AI\n[Read more about Question & Answer Chain](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.chainretrievalqa/)\n\nUsing the power of LLMs, we're able to extract the RFP questionnaire regardless of original formatting or layout. This allows AutoRFP to handle a wide range of RFPs without requiring explicit extraction rules for edge cases.\n\nAdditionally, We'll use the Input List Output Parser to return a list of questions for further processing."}, "typeVersion": 1}, {"id": "1c064047-1f6a-47c8-bb49-85b4d6f8e854", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [2380, 84.**************], "parameters": {"color": 7, "width": 746.3888903304862, "height": 600.3660610069576, "content": "## 4. Generating Question & Answer Pairs with AI\n[Read more about using OpenAI Assistants in n8n](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-langchain.openai/)\n\nBy preparing an OpenAI Assistant with marketing material and sales documents about our company and business, we are able to use AI to answer RFP questions with the accurate and relevant context. Potentially allowing sales teams to increase the number of RFPs they can reply to.\n\nThis portion of the workflow loops through and answers each question individually for better answers. We can record the Question and Answer pairings to the RFP response document we created earlier."}, "typeVersion": 1}, {"id": "e663ba01-e9a6-4247-9d97-8f796d29d72a", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1960, 540], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "ec0b439e-9fd8-4960-b8bb-04f4f7814a0a", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [300, 60], "parameters": {"width": 421.************, "height": 515.*************, "content": "## Try It Out!\n\n**This workflow does the following:**\n* Receives a RFP document via webhook\n* Creates a new RFP response document via Google Docs\n* Uses LLMs to extract the questions from the RFP document into a questions list\n* Loops through each question and uses an OpenAI Assistant to generate an answer. Saving each answer into the response document.\n* Once complete, sends a gmail and slack notification to the team.\n\n\n📃**Example Documents**\nTo run this workflow, you'll need to following 2 documents:\n* [RFP Document](https://drive.google.com/file/d/1G42h4Vz2lBuiNCnOiXF_-EBP1MaIEVq5/view?usp=sharing)\n* [Example Company Document](https://drive.google.com/file/d/16WywCYcxBgYHXB3TY3wXUTyfyG2n_BA0/view?usp=sharing)\n\n### Need Help?\nJoin the [Discord](https://discord.com/invite/XPKeKXeB7d) or ask in the [Forum](https://community.n8n.io/)!\n\nHappy Hacking!"}, "typeVersion": 1}, {"id": "244ff32d-9bc4-4a67-a6c2-4a7dc308058e", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [3160, 80], "parameters": {"color": 7, "width": 474.3513281516049, "height": 390.51033452105344, "content": "## 5. Send Notification Once Completed\n[Read more about using Slack](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.slack)\n\n\nFinally, we can use a number of ways to notify the sales team when the process is complete. Here, we've opted to send the requesting user an email with a link to the RFP response document."}, "typeVersion": 1}, {"id": "94243b69-43b8-4731-9a6b-2934db832cc6", "name": "Send Chat Notification", "type": "n8n-nodes-base.slack", "position": [3440, 280], "parameters": {"text": "=RFP document \"{{ $('Set Variables').item.json.title }}\" completed!", "select": "channel", "channelId": {"__rl": true, "mode": "name", "value": "RFP-channel"}, "otherOptions": {}}, "credentials": {"slackApi": {"id": "VfK3js0YdqBdQLGP", "name": "Slack account"}}, "typeVersion": 2.1}, {"id": "391d7e07-2a6d-4c4d-bf42-9cc5466cc1b5", "name": "Send Email Notification", "type": "n8n-nodes-base.gmail", "position": [3240, 280], "parameters": {"sendTo": "={{ $('Set Variables').item.json.reply_to }}", "message": "=Your RFP document \"{{ $('Set Variables').item.json.title }}\" is now complete!", "options": {}, "subject": "=RFP Questionnaire \"{{ $('Set Variables').item.json.title }}\" Completed!", "emailType": "text"}, "credentials": {"gmailOAuth2": {"id": "Sf5Gfl9NiFTNXFWb", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "34115f45-21ff-49a0-95f4-1fed53b53583", "name": "Add <PERSON> to Response Doc", "type": "n8n-nodes-base.googleDocs", "position": [1600, 340], "parameters": {"actionsUi": {"actionFields": [{"text": "=Title: {{ $('Set Variables').item.json.doc_title }}\nDate generated: {{ $now.format(\"yyyy-MM-dd @ hh:mm\") }}\nRequested by: {{ $('Set Variables').item.json.reply_to }}\nExecution Id: http://localhost:5678/workflow/{{ $workflow.id }}/executions/{{ $execution.id }}\n\n---\n\n", "action": "insert"}]}, "operation": "update", "documentURL": "={{ $json.id }}"}, "credentials": {"googleDocsOAuth2Api": {"id": "V0G0vi1DRj7Cqbp9", "name": "Google Docs account"}}, "typeVersion": 2}, {"id": "f285d896-ba15-4f8a-b041-7cbcbe2e1050", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [783, 238], "parameters": {"width": 192.**************, "height": 306.*************, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n🚨**Required**\n* Use a tool such as Postman to send data to the webhook."}, "typeVersion": 1}, {"id": "b6e4e40e-b10b-48f2-bfe2-1ad38b1c6518", "name": "Record Question & Answer in Response Doc", "type": "n8n-nodes-base.googleDocs", "position": [2940, 460], "parameters": {"actionsUi": {"actionFields": [{"text": "={{ $runIndex+1 }}. {{ $json.content }}\n{{ $json.output }}\n\n", "action": "insert"}]}, "operation": "update", "documentURL": "={{ $('Create new RFP Response Document').item.json.id }}"}, "credentials": {"googleDocsOAuth2Api": {"id": "V0G0vi1DRj7Cqbp9", "name": "Google Docs account"}}, "typeVersion": 2}, {"id": "ae8cc28f-4fd3-41d7-8a30-2675f58d1067", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [2600, 440], "parameters": {"width": 306.*************, "height": 481.**************, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n🚨**Required**\nYou'll need to create an OpenAI Assistant to use this workflow.\n* Sign up for [OpenAI Dashboard](https://platform.openai.com) if you haven't already.\n* Create an [OpenAI Assistant](https://platform.openai.com/playground/assistants)\n* Upload the [example company doc](https://drive.google.com/file/d/16WywCYcxBgYHXB3TY3wXUTyfyG2n_BA0/view?usp=sharing) to the assistant.\n\nThe assistant will use the company doc to answer the questions."}, "typeVersion": 1}, {"id": "********-5cbe-469b-8511-a92d5ea165cb", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [3200, 460], "parameters": {"width": 386.**************, "height": 94.**************, "content": "🚨**Required**\n* Update the email address to send to in Gmail Node.\n* Update the channel and message for Slack."}, "typeVersion": 1}, {"id": "25a57ca0-6789-499c-873b-07aba40530ed", "name": "Answer Question with Context", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [2620, 460], "parameters": {"text": "={{ $json.response.text }}", "prompt": "define", "options": {}, "resource": "assistant", "assistantId": {"__rl": true, "mode": "list", "value": "asst_QBI5lLKOsjktr3DRB4MwrgZd", "cachedResultName": "Nexus Digital Solutions Bot"}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.3}, {"id": "1b4cc83b-a793-47c1-9dd6-0d7484db07b4", "name": "Wait for Request", "type": "n8n-nodes-base.webhook", "position": [823, 278], "webhookId": "35e874df-2904-494e-a9f5-5a3f20f517f8", "parameters": {"path": "35e874df-2904-494e-a9f5-5a3f20f517f8", "options": {}, "httpMethod": "POST"}, "typeVersion": 2}, {"id": "2f97e3e6-c100-4045-bcb3-6fbd17cfb420", "name": "Extract Questions From RFP", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1960, 380], "parameters": {"text": "=You have been given a RFP document as part of a tender process of a buyer. Please extract all questions intended for the supplier. You must ensure the questions extracted are exactly has they are written in the RFP document.\n\n<RFP>{{ $('Get RFP Data').item.json.text }}<RFP>", "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.4}, {"id": "4945b975-ac84-406e-8482-44cfa5679ef9", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [760, 600], "parameters": {"color": 5, "width": 529.9947173986736, "height": 157.64231937074243, "content": "### Example Webhook Request\ncurl --location 'https://<n8n_webhook_url>' \\\n--form 'id=\"RFP001\"' \\\n--form 'title=\"BlueChip Travel and StarBus Web Services\"' \\\n--form 'reply_to=\"<EMAIL>\"' \\\n--form 'data=@\"k9pnbALxX/RFP Questionnaire.pdf\"'\n"}, "typeVersion": 1}], "pinData": {}, "connections": {"Get RFP Data": {"main": [[{"node": "Set Variables", "type": "main", "index": 0}]]}, "Set Variables": {"main": [[{"node": "Create new RFP Response Document", "type": "main", "index": 0}]]}, "Wait for Request": {"main": [[{"node": "Get RFP Data", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Extract Questions From RFP", "type": "ai_languageModel", "index": 0}]]}, "For Each Question...": {"main": [[{"node": "Send Email Notification", "type": "main", "index": 0}], [{"node": "Answer Question with Context", "type": "main", "index": 0}]]}, "Item List Output Parser": {"ai_outputParser": [[{"node": "Extract Questions From RFP", "type": "ai_outputParser", "index": 0}]]}, "Send Email Notification": {"main": [[{"node": "Send Chat Notification", "type": "main", "index": 0}]]}, "Extract Questions From RFP": {"main": [[{"node": "For Each Question...", "type": "main", "index": 0}]]}, "Add Metadata to Response Doc": {"main": [[{"node": "Extract Questions From RFP", "type": "main", "index": 0}]]}, "Answer Question with Context": {"main": [[{"node": "Record Question & Answer in Response Doc", "type": "main", "index": 0}]]}, "Create new RFP Response Document": {"main": [[{"node": "Add <PERSON> to Response Doc", "type": "main", "index": 0}]]}, "Record Question & Answer in Response Doc": {"main": [[{"node": "For Each Question...", "type": "main", "index": 0}]]}}}