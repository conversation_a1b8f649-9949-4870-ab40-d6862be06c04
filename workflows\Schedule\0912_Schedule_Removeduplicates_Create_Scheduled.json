{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "1c583599-826d-4a02-bfd9-f22f020f4af7", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-640, -140], "parameters": {"rule": {"interval": [{"field": "hours"}]}}, "typeVersion": 1.2}, {"id": "aaddc5fd-4b05-4ee2-9f71-222b14fb05d6", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [280, 40], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "cd2a47fb-3e04-464d-bcac-00e84952d72c", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [480, 40], "parameters": {"schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"labels\": {\n      \"type\": \"array\",\n      \"items\": { \"type\": \"string\" }\n    },\n    \"priority\": { \"type\": \"number\" },\n    \"summary\": { \"type\": \"string\" },\n    \"description\": { \"type\": \"string\" }\n  }\n}"}, "typeVersion": 1.2}, {"id": "********-66fd-4a5e-b940-5e6e07a95ad9", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [0, -340], "parameters": {"color": 7, "width": 700, "height": 540, "content": "## 2. Automate Generation and Triaging of Ticket\n[Read more about the Basic LLM node](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.chainllm)\n\nNew tickets always need to be properly labelled and prioritised but it's not always possible to get to update all incoming tickets if you're light on hands. Using an AI is a great use-case for triaging of tickets as its contextual understanding helps automates this step."}, "typeVersion": 1}, {"id": "c25fd99f-4898-479f-bf63-a79c3ca084fc", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.markdown", "position": [100, -140], "parameters": {"html": "={{ $json.html }}", "options": {}}, "typeVersion": 1}, {"id": "b27f5e33-d149-4395-84b2-e1e1070c8a0b", "name": "<PERSON> as Seen", "type": "n8n-nodes-base.removeDuplicates", "position": [-220, -140], "parameters": {"options": {}, "operation": "removeItemsSeenInPreviousExecutions", "dedupeValue": "={{ $json.id }}"}, "typeVersion": 2}, {"id": "e282e452-0dbb-4d00-b319-13840264<PERSON>da", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-740, -340], "parameters": {"color": 7, "width": 720, "height": 540, "content": "## 1. Watch Gmail Inbox for Support Emails\n[Learn more about the Gmail node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.gmail/)\n\n**This template assumes a group email specifically for support tickets!** If you have a general inbox, you may need to classify and filter each message which might become costly. The \"remove duplicates\" node (ie. \"Mark as seen\") ensures we only process each email exactly once."}, "typeVersion": 1}, {"id": "d43db00e-bfd4-4b18-ad33-4bccb3373d09", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [720, -340], "parameters": {"color": 7, "width": 460, "height": 440, "content": "## 3. Create Issue in Linear.App\n[Read more about the Linear.App node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.linear)\n\nThis is only a simple example to create an issue in Linear.App but easily extendable to add much more!"}, "typeVersion": 1}, {"id": "13f657aa-5af1-4af4-af04-f81a13d2ce29", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-1160, -720], "parameters": {"width": 380, "height": 940, "content": "## Try It Out!\n### This n8n template watches a Gmail inbox for support messages and creates an equivalent issue item in Linear.\n\n### How it works\n* A scheduled trigger fetches recent Gmail messages from the inbox which collects support requests.\n* These support requests are filtered to ensure they are only processed once and their HTML body is converted to markdown for easier parsing.\n* Each support request is then triaged via an AI Agent which adds appropriate labels, assesses priority and summarises a title and description of the original request.\n* Finally, the AI generated values are used to create an issue in Linear to be actioned.\n\n### How to use\n* Ensure the messages fetched are solely support requests otherwise you'll need to classify messages before processing them.\n* Specify the labels and priorities to use in the system prompt of the AI agent.\n\n### Requirements\n* Gmail for incoming support messages\n* OpenAI for LLM\n* Linear for issue management\n\n### Customising this workflow\n* Consider automating more steps after the issue is created such as attempting issue resolution or capacity planning.\n\n\n### Need Help?\nJoin the [Discord](https://discord.com/invite/XPKeKXeB7d) or ask in the [Forum](https://community.n8n.io/)!\n\nHappy Hacking!"}, "typeVersion": 1}, {"id": "684a5300-41c9-4ec4-8780-d1797e4dcfa2", "name": "Generate Issue From Support Request", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [300, -140], "parameters": {"text": "=Reported by {{ $json.from.value[0].name }} <{{ $json.from.value[0].address }}>\nReported at: {{ $now.toISO() }}\nSummary: {{ $json.subject }}\nDescription:\n{{ $json.data.replaceAll('\\n', ' ') }}", "messages": {"messageValues": [{"message": "=Your are Issues triage assistant who's task is to\n1) classify and label the given issue.\n2) Prioritise the given issue.\n3) Rewrite the issue summary and description.\n\n## Labels\nUse one or more labels.\n* Technical\n* Account\n* Access\n* Billing\n* Product\n* Training\n* Feedback\n* Complaints\n* Security\n* Privacy\n\n## Priority\n* 1 - highest\n* 2 - high\n* 3 - medium\n* 4 - low\n* 5 - lowest\n\n## Write Summary and Description\n* Remove emotional and anedotal phrases or information\n* Keep to the facts of the matter\n* Highlight what was attempted and is/was failing"}]}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.6}, {"id": "50aa5f53-680a-4518-a3a5-b97c3bd82af3", "name": "Get Recent Messages", "type": "n8n-nodes-base.gmail", "position": [-440, -140], "webhookId": "f3528949-056d-4013-ab62-9694e72b38cd", "parameters": {"limit": 1, "simple": false, "filters": {"q": "to:<EMAIL>"}, "options": {}, "operation": "getAll"}, "credentials": {"gmailOAuth2": {"id": "Sf5Gfl9NiFTNXFWb", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "a7a41e51-3852-43f3-98b9-d67bab4f8e41", "name": "Create Issue in Linear.App", "type": "n8n-nodes-base.linear", "position": [900, -140], "parameters": {"title": "={{ $json.output.summary }}", "teamId": "1c721608-321d-4132-ac32-6e92d04bb487", "additionalFields": {"stateId": "********-3d1f-4cf8-993b-0c982cc95245", "priorityId": "={{ $json.output.priority ?? 3 }}", "description": "={{ $json.output.description }}\n\n{{ $json.output.labels.map(label => `#${label}`).join(' ') }}"}}, "credentials": {"linearApi": {"id": "Nn0F7T9FtvRUtEbe", "name": "Linear account"}}, "typeVersion": 1}, {"id": "4593cd01-8fa3-4828-ba77-21082a2f31fb", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-500, 40], "parameters": {"color": 5, "height": 120, "content": "### Gmail Filters\nHere we're using the filter `to:<EMAIL>` to capture support requests."}, "typeVersion": 1}], "pinData": {}, "connections": {"Markdown": {"main": [[{"node": "Generate Issue From Support Request", "type": "main", "index": 0}]]}, "Mark as Seen": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Get Recent Messages", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Generate Issue From Support Request", "type": "ai_languageModel", "index": 0}]]}, "Get Recent Messages": {"main": [[{"node": "<PERSON> as Seen", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Generate Issue From Support Request", "type": "ai_outputParser", "index": 0}]]}, "Generate Issue From Support Request": {"main": [[{"node": "Create Issue in Linear.App", "type": "main", "index": 0}]]}}}