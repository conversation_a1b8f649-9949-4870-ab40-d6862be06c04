{"id": "0KZs18Ti2KXKoLIr", "meta": {"instanceId": "31e69f7f4a77bf465b805824e303232f0227212ae922d12133a0f96ffeab4fef", "templateCredsSetupCompleted": true}, "name": "✨🩷Automated Social Media Content Publishing Factory + System Prompt Composition", "tags": [], "nodes": [{"id": "74fb48a6-1acd-4693-9b8e-39b36c5649a9", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-520, -2080], "webhookId": "faddb40a-7048-4398-a0f9-d239a19c32ce", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "09f4a998-2d69-4683-9251-2694a77efeba", "name": "Sticky Note20", "type": "n8n-nodes-base.stickyNote", "position": [-600, -1720], "parameters": {"color": 7, "height": 240, "content": "## LLM"}, "typeVersion": 1}, {"id": "03b93e0b-a917-41f6-b99e-5a27ad07cd3e", "name": "Sticky Note21", "type": "n8n-nodes-base.stickyNote", "position": [-600, -1460], "parameters": {"color": 7, "height": 240, "content": "## Chat Memory"}, "typeVersion": 1}, {"id": "b6c61fe5-a519-4bdb-8641-3149362fbb54", "name": "Sticky Note22", "type": "n8n-nodes-base.stickyNote", "position": [-620, -2160], "parameters": {"color": 4, "width": 300, "height": 280, "content": "## 👍Start Here"}, "typeVersion": 1}, {"id": "2cf0448a-76de-4b2c-a200-953d47e29a52", "name": "Sticky Note32", "type": "n8n-nodes-base.stickyNote", "position": [1980, -2000], "parameters": {"color": 2, "width": 340, "height": 420, "content": "## Social Media Publishing Router"}, "typeVersion": 1}, {"id": "dff757e6-8ef4-4479-a9f8-71cb814fb8ef", "name": "Sticky Note33", "type": "n8n-nodes-base.stickyNote", "position": [-300, -1640], "parameters": {"color": 6, "height": 240, "content": "## 1️⃣ X - Twitter"}, "typeVersion": 1}, {"id": "fda64627-952a-4be9-b4c5-799d8c7801ad", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [-220, -1540], "parameters": {"name": "create_x_twitter_posts_tool", "fields": {"values": [{"name": "route", "stringValue": "=xtwitter"}, {"name": "user_prompt", "stringValue": "={{ $('When chat message received').item.json.chatInput }}"}]}, "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "description": "Use this tool to create XTwitter posts", "jsonSchemaExample": ""}, "typeVersion": 1.2}, {"id": "5023b0b3-468b-4cbb-829c-e06aaf822b99", "name": "Sticky Note34", "type": "n8n-nodes-base.stickyNote", "position": [-40, -1640], "parameters": {"color": 6, "height": 240, "content": "## 2️⃣ Instagram"}, "typeVersion": 1}, {"id": "781df8c5-0b06-42a4-bbe9-6948ae345599", "name": "Instagram", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [40, -1540], "parameters": {"name": "create_instagram_posts_tool", "fields": {"values": [{"name": "route", "stringValue": "=instagram"}, {"name": "user_prompt", "stringValue": "={{ $('When chat message received').item.json.chatInput }}"}]}, "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "description": "Use this tool to create Instagram posts", "jsonSchemaExample": ""}, "typeVersion": 1.2}, {"id": "8687d1ff-06ee-44c7-a26e-f08da72bbd15", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [-520, -1360], "parameters": {}, "typeVersion": 1.3}, {"id": "30cbcc50-e19b-43ea-8f0a-5e2021dc5e48", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-700, -560], "parameters": {"workflowInputs": {"values": [{"name": "user_prompt"}, {"name": "route"}]}}, "typeVersion": 1.1}, {"id": "0b9b7f07-d603-4890-96b0-f815feb38185", "name": "Sticky Note35", "type": "n8n-nodes-base.stickyNote", "position": [220, -1640], "parameters": {"color": 6, "height": 240, "content": "## 3️⃣ Facebook"}, "typeVersion": 1}, {"id": "12b17b82-8f98-4d80-9b49-aa9860827e01", "name": "Sticky Note36", "type": "n8n-nodes-base.stickyNote", "position": [480, -1640], "parameters": {"color": 6, "height": 240, "content": "## 4️⃣ LinkedIn"}, "typeVersion": 1}, {"id": "71dc9ccf-3691-4c0d-b53b-f3ff10f382a9", "name": "Facebook", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [300, -1540], "parameters": {"name": "create_facebook_posts_tool", "fields": {"values": [{"name": "route", "stringValue": "=facebook"}, {"name": "user_prompt", "stringValue": "={{ $('When chat message received').item.json.chatInput }}"}]}, "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "description": "Use this tool to create Facebook posts", "jsonSchemaExample": ""}, "typeVersion": 1.2}, {"id": "f953cd87-88a8-451f-841e-78227949b64d", "name": "LinkedIn", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [560, -1540], "parameters": {"name": "create_linkedin_posts_tool", "fields": {"values": [{"name": "route", "stringValue": "=linkedin"}, {"name": "user_prompt", "stringValue": "={{ $('When chat message received').item.json.chatInput }}"}]}, "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "description": "Use this tool to create LinkedIn posts", "jsonSchemaExample": ""}, "typeVersion": 1.2}, {"id": "97b6829d-6c9d-410a-8fa0-d89d884fd76e", "name": "<PERSON>y Note37", "type": "n8n-nodes-base.stickyNote", "position": [-40, -1380], "parameters": {"color": 6, "height": 240, "content": "## 5️⃣Threads"}, "typeVersion": 1}, {"id": "463259f7-71b4-492f-b05a-d1a958917d5c", "name": "<PERSON>y Note38", "type": "n8n-nodes-base.stickyNote", "position": [220, -1380], "parameters": {"color": 6, "height": 240, "content": "## 6️⃣YouTube Shorts"}, "typeVersion": 1}, {"id": "0cd9003b-8eeb-4e4a-9f1f-5f6b611d5194", "name": "Short", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [40, -1280], "parameters": {"name": "create_threads_posts_tool", "fields": {"values": [{"name": "route", "stringValue": "=threads"}, {"name": "user_prompt", "stringValue": "={{ $('When chat message received').item.json.chatInput }}"}]}, "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "description": "Use this tool to create Threads posts", "jsonSchemaExample": ""}, "typeVersion": 1.2}, {"id": "54c2bf4b-8053-4e9d-beb4-570db66f9bd4", "name": "YouTube Short", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [300, -1280], "parameters": {"name": "create_youtube_short_tool", "fields": {"values": [{"name": "route", "stringValue": "=youtube_short"}, {"name": "user_prompt", "stringValue": "={{ $('When chat message received').item.json.chatInput }}"}, {"name": "llm", "stringValue": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Value', ``, 'string') }}"}]}, "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "description": "Use this tool to create a YouTube short", "jsonSchemaExample": ""}, "typeVersion": 1.2}, {"id": "a72c3242-3a8b-444f-9623-fbcb0b47a817", "name": "Sticky Note18", "type": "n8n-nodes-base.stickyNote", "position": [-340, -1720], "parameters": {"color": 7, "width": 1100, "height": 620, "content": "## Social Media Agent Tools"}, "typeVersion": 1}, {"id": "586a33ae-3546-4b31-9235-9a8fcfd28598", "name": "Sticky Note25", "type": "n8n-nodes-base.stickyNote", "position": [-500, -940], "parameters": {"color": 6, "width": 3520, "height": 820, "content": "# 🏭Social Media Content Factory"}, "typeVersion": 1}, {"id": "153da903-fcd3-4694-aaa4-bef2b300d158", "name": "pollinations.ai1", "type": "n8n-nodes-base.httpRequest", "onError": "continueErrorOutput", "maxTries": 5, "position": [1440, -560], "parameters": {"url": "=https://image.pollinations.ai/prompt/{{ $json.output.common_schema.image_suggestion.replaceAll(' ','-').replaceAll(',','').replaceAll('.','').slice(0,100) }}", "options": {}}, "retryOnFail": true, "typeVersion": 4.2}, {"id": "6c114f0b-1395-4fe6-8de7-0b3d0d9fd6b2", "name": "<PERSON><PERSON> Note26", "type": "n8n-nodes-base.stickyNote", "position": [1340, -720], "parameters": {"color": 7, "width": 300, "height": 340, "content": "## Create Post Image\nhttps://pollinations.ai/\nhttps://image.pollinations.ai/prompt/[your image description]\n\n"}, "typeVersion": 1}, {"id": "e196ea9b-f5d0-4fa6-a3d9-bea2f98fd872", "name": "Save Image to imgbb.com", "type": "n8n-nodes-base.httpRequest", "position": [1760, -680], "parameters": {"url": "https://api.imgbb.com/1/upload", "method": "POST", "options": {"redirect": {"redirect": {}}}, "sendBody": true, "sendQuery": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"name": "image", "parameterType": "formBinaryData", "inputDataFieldName": "data"}]}, "queryParameters": {"parameters": [{"name": "expiration", "value": "0"}, {"name": "key", "value": "={{ $env.IMGBB_API_KEY}} "}]}}, "typeVersion": 4.2}, {"id": "225e34be-26ee-40d7-88d6-e866420e083a", "name": "Sticky Note41", "type": "n8n-nodes-base.stickyNote", "position": [1980, -2280], "parameters": {"width": 340, "height": 180, "content": "💡Notes\n\nUpdate all Social Media Platform Credentials as required.\n\nAdjust parameters and content for each platform to suit your needs."}, "typeVersion": 1}, {"id": "2f48f19d-92c1-478a-b7fa-3fc3b1100993", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1240, -1760], "parameters": {"color": 4, "width": 400, "height": 360, "content": "# 👍 Approve Content Before Proceeding"}, "typeVersion": 1}, {"id": "ce4e9f3c-801a-478e-8ffc-008c5e7d4e49", "name": "Gmail", "type": "n8n-nodes-base.gmail", "position": [2640, -780], "webhookId": "cfc2a53d-14a7-47e1-8385-c0b0792d9843", "parameters": {"sendTo": "={{ $env.TELEGRAM_CHAT_ID }}", "message": "={{ $json.output }}", "options": {"appendAttribution": false}, "subject": "=Social Media Content - {{ $('Social Content').item.json.output.title }}"}, "credentials": {"gmailOAuth2": {"id": "1xpVDEQ1yx8gV022", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "31ee0735-c863-476c-9c4a-41b50ae9c61a", "name": "Social Media Schema", "type": "n8n-nodes-base.googleDocs", "position": [-320, -700], "parameters": {"operation": "get", "documentURL": "=12345"}, "credentials": {"googleDocsOAuth2Api": {"id": "YWEHuG28zOt532MQ", "name": "Google Docs account"}}, "typeVersion": 2}, {"id": "18cfde4e-2637-496c-acca-070bdb84c2ba", "name": "Social Media System Prompt", "type": "n8n-nodes-base.googleDocs", "position": [-320, -420], "parameters": {"operation": "get", "documentURL": "=12345"}, "credentials": {"googleDocsOAuth2Api": {"id": "YWEHuG28zOt532MQ", "name": "Google Docs account"}}, "typeVersion": 2}, {"id": "383ce472-ccf8-47fb-aa36-5b8aacbcd64f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-440, -840], "parameters": {"color": 7, "width": 1120, "height": 640, "content": "## Prompt & Schema Composition from External Sources"}, "typeVersion": 1}, {"id": "8d2a2a64-bbaa-4692-94ed-2f541d0d40ca", "name": "gpt-40-mini", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [2320, -600], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "gpt-4o-mini"}, "options": {"responseFormat": "text"}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "6e5faa4d-25a1-4dbe-998e-3255ed181ac5", "name": "Instagram Image", "type": "n8n-nodes-base.httpRequest", "onError": "continueRegularOutput", "position": [2440, -1940], "parameters": {"url": "https://graph.facebook.com/v20.0/[your-unique-id]/media", "method": "POST", "options": {}, "sendQuery": true, "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "image_url", "value": "={{ $json.output.social_image.medium.url }}"}, {"name": "caption", "value": "={{ $json.output.caption }}"}]}, "nodeCredentialType": "facebookGraphApi"}, "credentials": {"facebookGraphApi": {"id": "PzDfmiwB7GPtmSaP", "name": "Facebook Graph account"}}, "typeVersion": 4.2}, {"id": "958793c8-7a74-498f-ac75-256232469fbc", "name": "X Post", "type": "n8n-nodes-base.twitter", "onError": "continueRegularOutput", "position": [2640, -2180], "parameters": {"text": "={{ $json.data.social_content.schema.post }}", "additionalFields": {}}, "credentials": {"twitterOAuth2Api": {"id": "wRDruLTCqjQ7C5jq", "name": "X account"}}, "typeVersion": 2, "alwaysOutputData": true}, {"id": "1f04a4b5-e97d-4574-abdb-270265da77fa", "name": "Instragram Post", "type": "n8n-nodes-base.facebookGraphApi", "onError": "continueRegularOutput", "position": [2640, -2000], "parameters": {"edge": "media_publish", "node": "[your-unique-id]", "options": {"queryParameters": {"parameter": [{"name": "creation_id", "value": "={{ $json.id }}"}, {"name": "caption", "value": "={{ $('Social Media Publishing Router').item.json.output.caption }}"}]}}, "graphApiVersion": "v20.0", "httpRequestMethod": "POST"}, "credentials": {"facebookGraphApi": {"id": "PzDfmiwB7GPtmSaP", "name": "Facebook Graph account"}}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "92a917ff-d20d-4bbc-be8f-00e17be83ea2", "name": "Facebook Post", "type": "n8n-nodes-base.facebookGraphApi", "onError": "continueRegularOutput", "position": [2640, -1820], "parameters": {"edge": "photos", "node": "[your-unique-id]", "options": {"queryParameters": {"parameter": [{"name": "message", "value": "={{ $json.output.post }}\n\n{{ $json.output.call_to_action }}\n"}]}}, "sendBinaryData": true, "graphApiVersion": "v20.0", "httpRequestMethod": "POST", "binaryPropertyName": "data"}, "credentials": {"facebookGraphApi": {"id": "PzDfmiwB7GPtmSaP", "name": "Facebook Graph account"}}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "6c80332d-1aaf-4f3a-91fd-58c25f20ee0c", "name": "LinkedIn Post", "type": "n8n-nodes-base.linkedIn", "onError": "continueRegularOutput", "position": [2640, -1640], "parameters": {"text": "={{ $json.data.social_content.schema.post }}\n{{ $json.data.social_content.schema.call_to_action }}\n{{ $json.data.social_content.common_schema.hashtags }}\n", "postAs": "organization", "organization": "********", "additionalFields": {}, "binaryPropertyName": "=data", "shareMediaCategory": "IMAGE"}, "credentials": {"linkedInOAuth2Api": {"id": "WMm6pzAEgNd4wJdO", "name": "LinkedIn account"}}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "f9d80261-8543-4a12-969c-eecd58513ef2", "name": "Gmail User for Approval", "type": "n8n-nodes-base.gmail", "position": [1380, -1600], "webhookId": "abfae12d-ddcf-4981-ad33-bb7a8cc115a2", "parameters": {"sendTo": "={{ $env.TELEGRAM_CHAT_ID }}", "message": "={{ $json.output }}", "options": {"limitWaitTime": {"values": {"resumeUnit": "minutes", "resumeAmount": 45}}}, "subject": "=🔥FOR APPROVAL🔥 {{$('Extract as JSON').item.json.data.social_content.root_schema.name  }}", "operation": "sendAndWait", "approvalOptions": {"values": {"approvalType": "double"}}}, "credentials": {"gmailOAuth2": {"id": "1xpVDEQ1yx8gV022", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "97c2dec9-9e1e-4a42-9538-8a37392114e6", "name": "Get Social Post Image", "type": "n8n-nodes-base.httpRequest", "position": [1640, -1340], "parameters": {"url": "={{ $('Extract as JSON').item.json.data.social_image.medium.url }}", "options": {}}, "retryOnFail": true, "typeVersion": 4.2}, {"id": "b5b6b7b9-d275-4c1a-a3c5-195b13be1538", "name": "gpt-40-mini1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [860, -1420], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "gpt-4o-mini"}, "options": {"responseFormat": "text"}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "0b5b8237-9e34-44b7-82d9-372a12c67546", "name": "gpt-4o-mini", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [780, -360], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "gpt-4o-mini"}, "options": {"responseFormat": "json_object"}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "df61bbeb-1432-434b-9993-18362dba097f", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-1840, -1220], "parameters": {"color": 5, "width": 760, "height": 1540, "content": "<system>\nYou are a specialized content creation AI for social media platforms.\nYour primary function is generating platform-optimized social media content across various platforms including LinkedIn, Instagram, Facebook, Twitter (X), Threads, and YouTube Shorts. Each piece of content must:\nMatch the specific platform's audience expectations and algorithm preferences\nShowcase relevant expertise in your field\nDeliver actionable insights for your target audience\nDrive meaningful engagement through value-driven content\nOBJECTIVES:\nCreate platform-specific content following each platform's best practices\nImplement strategic hashtag usage combining general and trending tags\nDesign content that encourages user interaction and community building\nMaintain consistent brand voice while adapting to platform requirements\nIncorporate data-driven insights to maximize content performance\nOUTPUT REQUIREMENTS:\nDeliver content in valid JSON format according to the platform-specific schema\nInclude all required fields as specified in the schema\nOmit any explanatory text or code fencing in your response\nTailor content specifically to the platform indicated in the user's request\nFor each content request, adapt your output based on the platform guidelines.  Never provide URLS for video or image suggestions and only describe the suggestion.\n</system>\n\n\n<rules>\nOnly provide final response in valid JSON for the appropriate social platform\nNever include any preamble or further explanation\nAlways remove any ``` ```json\n</rules>\n\n\n<linkedin>\n**Style**: Professional and insightful.\n**Tone**: Business-oriented; focus on automation use cases, industry insights, and community impact.\n**Content Length**: 3-4 sentences; concise but detailed.\n**Hashtags**: #Innovation #Automation #WorkflowSolutions #DigitalTransformation #Leadership\n**Call to Action (CTA)**: Encourage comments or visits to workflows.diy's website for more insights.\n</linkedin>\n\n<instagram>\n**Style**: Visual storytelling with creative captions.\n**Tone**: Inspirational and engaging; use emojis for relatability.\n**Content Length**: 2-3 sentences paired with eye-catching visuals (e.g., infographics or workflow demos).\n**Visuals**: Showcase milestones (e.g., new workflow launches), tutorials, or product highlights.\n**CTA**: Use phrases like \"Swipe to learn more,\" \"Tag your team,\" or \"Check out the link below!\"\n**Link Placement**: Add the provided link before hashtags; if no link is provided, use \"Visit our website: https://example.com\"\n**Hashtags**: #AutomationLife #TechInnovation #WorkflowTips #Programming #Engineering\n</instagram>\n\n<facebook>\n**Style**: Friendly and community-focused.\n**Tone**: Relatable; highlight user success stories or company achievements in automation.\n**Content Length**: 2-3 sentences; conversational yet professional.\n**Hashtags**: #SmallBusinessAutomation #Entrepreneurship #Leadership #WorkflowInnovation\n**CTA**: Encourage likes, shares, comments (e.g., \"What's your favorite automation tip?\").\n</facebook>\n\n<xtwitter>\n**Style**: Concise and impactful.\n**Tone**: Crisp and engaging; spark curiosity in 150 characters or less.\n**Hashtags**: #WorkflowTrends #AIWorkflows #AutomationTips #NoCodeSolutions\n**CTA**: Drive quick engagement through retweets or replies (e.g., \"What's your go-to n8n workflow?\").\n</xtwitter>\n\n<threads>\n**Style**: Conversational and community-driven posts.\n**Tone**: Casual yet informative; encourage discussions around automation trends or innovations.\n**Content Length**: 1-2 short paragraphs with a question or thought-provoking statement at the end.\n**Hashtags**: Similar to Instagram but tailored for trending Threads topics related to automation.\n</threads>\n\n<youtube_short>\n**Style**: Short-form video content showcasing quick workflow tutorials or use cases.\n**Tone**: Authoritative yet approachable; establish workflows.diy as a leader in n8n automation solutions.\n**Content Length**:\n  Tutorials/Reviews (long-form): 5-10 minutes\n  Shorts/Highlights (short-form): Under 1 minute\n**CTA**: Encourage subscriptions, likes, comments (e.g., \"Subscribe for more workflow tips!\").\n</youtube_short>\n\n\n\n\n"}, "typeVersion": 1}, {"id": "ddf3d7d3-0218-4ba0-b990-34a6220a53fa", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-1060, -1220], "parameters": {"color": 3, "height": 1540, "content": "<common>\n{\n    \"type\": \"object\",\n    \"properties\": {\n        \"hashtags\": {\n            \"type\": \"array\",\n            \"items\": {\n                \"type\": \"string\"\n            }\n        },\n        \"image_suggestion\": {\n            \"type\": \"string\"\n        }\n    }\n}\n</common>\n\n<root>\n{\n    \"type\": \"object\",\n    \"properties\": {\n        \"name\": {\n            \"type\": \"string\"\n        },\n        \"description\": {\n            \"type\": \"string\"\n        },\n        \"additional_notes\": {\n            \"type\": \"string\"\n        }\n    }\n}\n</root>\n\n<linkedin>\n{\n    \"type\": \"object\",\n    \"properties\": {\n        \"post\": {\n            \"type\": \"string\"\n        },\n        \"call_to_action\": {\n            \"type\": \"string\"\n        }\n    }\n}\n</linkedin>\n\n<instagram>\n{\n    \"type\": \"object\",\n    \"properties\": {\n        \"caption\": {\n            \"type\": \"string\"\n        },\n        \"emojis\": {\n            \"type\": \"array\",\n            \"items\": {\n                \"type\": \"string\"\n            }\n        },\n        \"call_to_action\": {\n            \"type\": \"string\"\n        }\n    }\n}\n</instagram>\n\n<facebook>\n{\n    \"type\": \"object\",\n    \"properties\": {\n        \"post\": {\n            \"type\": \"string\"\n        },\n        \"call_to_action\": {\n            \"type\": \"string\"\n        }\n    }\n}\n</facebook>\n\n<xtwitter>\n{\n    \"type\": \"object\",\n    \"properties\": {\n        \"video_suggestion\": {\n            \"type\": \"string\"\n        },\n        \"post\": {\n            \"type\": \"string\"\n        },\n        \"character_limit\": {\n            \"type\": \"integer\"\n        }\n    }\n}\n</xtwitter>\n\n<threads>\n{\n    \"type\": \"object\",\n    \"properties\": {\n        \"text_post\": {\n            \"type\": \"string\"\n        },\n        \"call_to_action\": {\n            \"type\": \"string\"\n        }\n    }\n}\n</threads>\n\n<youtube_short>\n{\n    \"type\": \"object\",\n    \"properties\": {\n        \"video_suggestion\": {\n            \"type\": \"string\"\n        },\n        \"title\": {\n            \"type\": \"string\"\n        },\n        \"description\": {\n            \"type\": \"string\"\n        },\n        \"call_to_action\": {\n            \"type\": \"string\"\n        }\n    }\n}\n</youtube_short>\n\n\n\n"}, "typeVersion": 1}, {"id": "72b378bd-6035-45da-8c76-ddd897d107c7", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-400, -480], "parameters": {"color": 5, "width": 260, "height": 240, "content": "### 👈System Prompt"}, "typeVersion": 1}, {"id": "ba60e52d-722a-4f07-86b4-f4ea64cb2bab", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-400, -760], "parameters": {"color": 3, "width": 260, "height": 240, "content": "### 👈Social Media Schema"}, "typeVersion": 1}, {"id": "bc1ff038-26ad-44d6-94d1-2c1f72a9bf87", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.set", "position": [-60, -700], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9d6d41f2-7216-4659-af34-7215298494d9", "name": "schema", "type": "string", "value": "={{ $json.content }}"}, {"id": "7d8c85f5-3f4a-4d72-bef0-0957c6ce82a4", "name": "platform", "type": "string", "value": "={{ $('When Executed by Another Workflow').item.json.route }}"}]}}, "typeVersion": 3.4}, {"id": "777d231c-f69c-4b48-bec5-6674175703bc", "name": "System Prompt", "type": "n8n-nodes-base.set", "position": [-60, -420], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "5f789b37-b021-4cd4-b359-fdfbb9b71c2b", "name": "system_prompt_doc_id", "type": "string", "value": "={{ $json.documentId }}"}, {"id": "daac5758-38ad-4afe-966b-a9b4b89691b2", "name": "system_prompt", "type": "string", "value": "={{ $json.content }}"}]}}, "typeVersion": 3.4}, {"id": "3813a552-cf99-49ca-9617-7eaac56f6819", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.code", "position": [140, -700], "parameters": {"jsCode": "// Get the input data from previous node\nconst inputData = $input.first().json;\nconst xmlString = inputData.schema;\n\nconsole.log(inputData)\n\n// Function to extract content between XML tags with better regex handling\nfunction extractFromXmlTags(xmlString, tagName) {\n  const regex = new RegExp(`<${tagName}>(.*?)<\\/${tagName}>`, 'gs');\n  const match = regex.exec(xmlString);\n  return match ? match[1].trim() : null;\n}\n\n// Get the platform from the input or use a default\nconst platform = inputData.platform;\n\n// Extract the content from the specified tag\nconst extractedContent = extractFromXmlTags(xmlString, platform);\nconst rootContent = extractFromXmlTags(xmlString, 'root');\nconst commonContent = extractFromXmlTags(xmlString, 'common');\n\njsonData = JSON.parse(extractedContent);\nrootSchema = JSON.parse(rootContent);\ncommonSchema = JSON.parse(commonContent);\n\n// Return the result\nreturn {\n  json: {\n    schema: jsonData,\n    root_schema: rootSchema,\n    common_schema: commonSchema\n  }\n};\n"}, "typeVersion": 2}, {"id": "c55da4a1-91f8-4d17-ad73-730013a99231", "name": "Parse System Prompt", "type": "n8n-nodes-base.code", "position": [140, -420], "parameters": {"jsCode": "// Get the input data from previous node\nconst inputData = $input.first().json;\nconst xmlString = inputData.system_prompt;\n\n// Function to extract all content between XML tags\nfunction extractAllXmlTags(xmlString) {\n  // Create a result object to store tag contents\n  const result = {};\n  \n  // Regular expression to find all XML tags and their content\n  // This regex matches opening tag, content, and closing tag\n  const tagRegex = /<([^>\\/]+)>([\\s\\S]*?)<\\/\\1>/g;\n  \n  // Find all matches\n  let match;\n  while ((match = tagRegex.exec(xmlString)) !== null) {\n    const tagName = match[1].trim();\n    const content = match[2].trim();\n    \n    // Store the content with the tag name as the key\n    result[tagName] = content;\n  }\n  \n  return result;\n}\n\n// Extract all XML tags and their content\nconst extractedTags = extractAllXmlTags(xmlString);\n\n// Return the result as a JSON object\nreturn {\n  json: {\n    system_config: extractedTags\n  }\n};\n"}, "typeVersion": 2}, {"id": "1767c787-943b-43d6-86cb-3fb60eaf878e", "name": "Compose Prompt & Schema", "type": "n8n-nodes-base.set", "position": [520, -560], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9216ad1c-a281-4c94-835d-e20507ef0cb5", "name": "route", "type": "string", "value": "={{ $json.route }}"}, {"id": "e6ca5cdf-5139-4db7-b065-ee52028216c5", "name": "user_prompt", "type": "string", "value": "={{ $json.user_prompt }}"}, {"id": "2927cd6f-c351-49df-954b-9f87b0338c58", "name": "system_config.system", "type": "string", "value": "={{ $json.system_config.system }}"}, {"id": "829b1519-9ffa-44d7-8caa-455e15b30614", "name": "system_config.rules", "type": "string", "value": "={{ $json.system_config.rules }}"}, {"id": "b44472ba-6e98-448b-bad6-e02da8b32b0a", "name": "={{ $json.route }}", "type": "string", "value": "={{ $json.system_config[$json.route.toLowerCase()] }}"}, {"id": "a96e8c30-1d44-4e23-9ef4-95d7303ea41e", "name": "root_schema", "type": "object", "value": "={{ $json.root_schema }}"}, {"id": "6cb68192-10f3-496d-88ca-289ee0c19940", "name": "common_schema", "type": "object", "value": "={{ $json.common_schema }}"}, {"id": "8f9b85f0-abaa-46c2-ba98-897f6a677105", "name": "schema", "type": "object", "value": "={{ $json.schema }}"}]}}, "typeVersion": 3.4}, {"id": "b7d78f57-ee83-4e03-ada6-fd6e2048c272", "name": "Social Media Content Creator", "type": "@n8n/n8n-nodes-langchain.agent", "position": [800, -560], "parameters": {"text": "=Social Media Platform: {{ $json.route }}\nUser Prompt: {{ $json.user_prompt }}\n", "options": {"systemMessage": "={{ $json.system_config.system }}\n\n<tools>\nYou have been provided with an internet search tool.  Use this tool to find relavent information about the users request before responding.  Todays date is: {{ $now }}\n</tools>\n\n<rules>\n{{ $json.system_config.rules }}\n- Output must conform to provided JSON schema\n</rules>\n\nFollow this Output JSON Schema:\n{\n  root_schema: {{ $json.root_schema.toJsonString() }},\n  common_schema: {{ $json.common_schema.toJsonString()}},\n  schema: {{  $json.schema.toJsonString() }}\n}"}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "35469698-0eb5-4238-85d1-c67ccbacf2cb", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-1880, -1400], "parameters": {"color": 7, "width": 1100, "height": 1760, "content": "# External System Prompt and Schema"}, "typeVersion": 1}, {"id": "12b55edd-ff51-423b-a153-96a8a2a09678", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [2380, -2280], "parameters": {"color": 6, "width": 700, "height": 1240, "content": "# 📄 Publish to Social Media "}, "typeVersion": 1}, {"id": "78cd8af0-c10c-4bf5-8420-63061e7687bc", "name": "<PERSON><PERSON> Prompts and <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [340, -560], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition", "numberInputs": 3}, "typeVersion": 3}, {"id": "e7a62296-729b-45df-bd15-002ccaae2fa0", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [720, -680], "parameters": {"width": 400, "height": 480, "content": "## Social Media Content Creator"}, "typeVersion": 1}, {"id": "f6cb5a95-4047-4e41-a635-a73681fe6d8b", "name": "Social Content", "type": "n8n-nodes-base.set", "position": [1180, -560], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "8c318996-aa79-4970-b8d7-33ae1931c8c6", "name": "output", "type": "object", "value": "={{ $json.output }}"}]}}, "typeVersion": 3.4}, {"id": "148df311-24db-4243-bc62-1a51579720d7", "name": "Save Image to Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [1760, -480], "parameters": {"name": "={{ $json.output.root_schema.name.replaceAll(' ','-').replaceAll(',','').replaceAll('.','') }}", "driveId": {"__rl": true, "mode": "id", "value": "=My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "id", "value": "=12345"}}, "credentials": {"googleDriveOAuth2Api": {"id": "UhdXGYLTAJbsa0xX", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "f84ab889-d193-4d9a-8e8b-4f35805edaa4", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [1980, -560], "parameters": {"mode": "combine", "options": {"includeUnpaired": true}, "combineBy": "combineByPosition", "numberInputs": 3}, "typeVersion": 3}, {"id": "ca43646b-ca79-4ff6-ac02-da4f668e7aeb", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [2200, -880], "parameters": {"color": 7, "width": 660, "height": 420, "content": "## Send Social Media Image and Post Contents to Gmail\n(optional)"}, "typeVersion": 1}, {"id": "cf7bda38-260f-42c0-b60a-6a93181712de", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [2120, -420], "parameters": {"color": 7, "width": 840, "height": 260, "content": "## Prepare Social Media Post and Save to Google Drive"}, "typeVersion": 1}, {"id": "13cafb9c-5f04-4193-8f0e-0384f68d5e45", "name": "Save Social Post to Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [2560, -340], "parameters": {"name": "={{ $json.response.google_drive_image.id }}", "content": "={{ $json.response.toJsonString() }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "id", "value": "= 12345"}, "operation": "createFromText"}, "credentials": {"googleDriveOAuth2Api": {"id": "UhdXGYLTAJbsa0xX", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "2bdecf8d-e180-4795-92c2-6158adf71daf", "name": "Google Drive Image Meta", "type": "n8n-nodes-base.set", "position": [2200, -340], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "769d86e6-3764-4023-8932-f25f7d4fe34a", "name": "id", "type": "string", "value": "={{ $json.id }}"}, {"id": "4ccae0cc-d246-477c-9e94-3be461134d01", "name": "webContentLink", "type": "string", "value": "={{ $json.webContentLink }}"}, {"id": "74e22694-c7e6-4598-8e87-8ea6ae00144e", "name": "webViewLink", "type": "string", "value": "={{ $json.webViewLink }}"}, {"id": "e8eedbbf-7d42-475b-a748-0afbe8b730da", "name": "thumbnailLink", "type": "string", "value": "={{ $json.thumbnailLink }}"}]}}, "typeVersion": 3.4}, {"id": "d6e4902a-af19-45a4-8253-432135e17998", "name": "Social Post JSON", "type": "n8n-nodes-base.set", "position": [2380, -340], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "b705af39-d286-4461-956c-d963ea151734", "name": "response", "type": "object", "value": "={ \n  \"route\": \"{{ $('When Executed by Another Workflow').item.json.route }}\",\n \"social_image\": {{ $('Merge').item.json.data.toJsonString()  }},\n  \"social_content\": {{ $('Social Content').item.json.output.toJsonString() }},\n  \"google_drive_image\": {{ $json.toJsonString() }}\n}"}]}}, "typeVersion": 3.4}, {"id": "b35623e6-9ddc-4091-905f-7b485efc5d60", "name": "Respond with Google Drive Id", "type": "n8n-nodes-base.set", "position": [2740, -340], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "ad353ca7-059a-4108-88b9-fb92720a34fe", "name": "response", "type": "string", "value": "={{ $json.id }}"}]}}, "typeVersion": 3.4}, {"id": "410c3924-1011-480d-874c-a395c243f4c6", "name": "Telegram Success Message (Optional)", "type": "n8n-nodes-base.telegram", "position": [1760, -880], "webhookId": "********-02c0-42ee-98c3-a2ec72b3bf12", "parameters": {"text": "Image created successfully", "chatId": "={{ $env.TELEGRAM_CHAT_ID }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "eba8445d-55da-43e2-b2aa-4822168a70ea", "name": "Telegram Error Message (Optional)", "type": "n8n-nodes-base.telegram", "position": [1760, -300], "webhookId": "********-02c0-42ee-98c3-a2ec72b3bf12", "parameters": {"text": "Error creating image (Debugging)", "chatId": "={{ $env.TELEGRAM_CHAT_ID }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "af2c4945-0dac-4398-b8ad-329066<PERSON><PERSON><PERSON>", "name": "Social Media Publishing Router", "type": "n8n-nodes-base.switch", "position": [2080, -1900], "parameters": {"rules": {"values": [{"outputKey": "1️⃣X-Twitter", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.route }}", "rightValue": "xtwitter"}]}, "renameOutput": true}, {"outputKey": " 2️⃣Instagram", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "86d44336-bab7-422f-9266-fcb513252d19", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.route }}", "rightValue": "instagram"}]}, "renameOutput": true}, {"outputKey": " 3️⃣Facebook", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "29f37628-6381-46af-babb-74bf00b4a849", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.route }}", "rightValue": "facebook"}]}, "renameOutput": true}, {"outputKey": "4️⃣Linkedin", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "fdb7c8aa-4108-43f6-8f6b-71cd8f383d2a", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.route }}", "rightValue": "=linkedin"}]}, "renameOutput": true}, {"outputKey": "5️⃣Threads", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "956baedd-4a0b-4e41-b85c-ef2c84332bdc", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.route }}", "rightValue": "threads"}]}, "renameOutput": true}, {"outputKey": "6️⃣YouTube Short", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "4d690442-197c-4ff9-b176-b55dfabaecc9", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.route }}", "rightValue": "youtube_short"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "682ee752-8e7f-4ee5-b617-88d7c1f7d4e7", "name": "Prepare Email Contents", "type": "@n8n/n8n-nodes-langchain.agent", "position": [880, -1600], "parameters": {"text": "=Use the HTML template and populate [fields] as required from this: {{ $json.data.social_content.toJsonString() }}\n-----\nOnly output HTML without code block tags, preamble or further explanation in the format provided.\n\n## HTML Template\n<table style=\"max-width:640px;min-width:320px;width:100%;border-collapse:collapse;font-family:Arial,sans-serif;margin:20px auto\">\n    <tbody>\n        <tr>\n            <td colspan=\"2\" style=\"background-color:#ffffff;padding:15px;text-align:left\">\n                <img src=\"{{ $json.data.google_drive_image.thumbnailLink }}\" alt=\"{{ $json.data.social_content.root_schema.name }}\" style=\"max-width:100%;height:auto;\">\n            </td>\n        </tr>\n        <tr>\n            <td colspan=\"2\" style=\"background-color:#efefef;padding:15px;font-size:20px;text-align:left;font-weight:bold\">\n                {{ $json.data.social_content.root_schema.name }}\n            </td>\n        </tr>\n        <tr>\n            <td style=\"background-color:#f9f9f9;padding:15px;width:30%;text-align:left\"><strong>[label_1]:</strong></td>\n            <td style=\"background-color:#f9f9f9;padding:15px;text-align:left\">[content_1]</td>\n        </tr>\n        <tr>\n            <td style=\"background-color:#f1f1f1;padding:15px;text-align:left\"><strong>[label_2]:</strong></td>\n            <td style=\"background-color:#f1f1f1;padding:15px;text-align:left\">[content_2]</td>\n        </tr>\n\n        [continue the pattern ...]\n\n        <tr>\n            <td colspan=\"2\" style=\"background-color:#efefef;padding:15px;text-align:left\">\n                <strong>[footer_label]:</strong> [footer_content]\n            </td>\n        </tr>\n    </tbody>\n</table>\n\n", "options": {}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "eeab0159-cb24-4653-ba14-461740c8753c", "name": "Is Approved?", "type": "n8n-nodes-base.if", "position": [1380, -1340], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "313cec9b-aad5-4f9c-a209-afe83af53df0", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.data.approved }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "dbe7cd63-9ec0-46cd-9255-8c1dc738847d", "name": "File Id", "type": "n8n-nodes-base.set", "position": [1200, -2080], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "efb1c03b-8465-443d-a442-b76b8cd86a73", "name": "output", "type": "object", "value": "={{ $json.output }}"}]}}, "typeVersion": 3.4}, {"id": "cdb01e90-c54c-4ae4-87e2-75a3baec2295", "name": "Get Social Post from Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [1380, -2080], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $json.output.response }}"}, "options": {}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "UhdXGYLTAJbsa0xX", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "aea6ec59-fcf4-4b5a-9fbc-86d9eb834388", "name": "Extract as JSON", "type": "n8n-nodes-base.extractFromFile", "position": [1560, -2080], "parameters": {"options": {}, "operation": "fromJson"}, "typeVersion": 1}, {"id": "70ccf82d-3871-40cd-8dc0-961e36acd070", "name": "Merge Image and Post Contents", "type": "n8n-nodes-base.merge", "position": [1820, -1840], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3}, {"id": "077f7110-215f-4ccf-8546-41d21c1105ad", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [2540, -1460], "parameters": {"width": 320, "height": 380, "content": ""}, "typeVersion": 1}, {"id": "650a4191-6ed6-4868-b433-f44a0ddf959b", "name": "Implement Threads Here", "type": "n8n-nodes-base.noOp", "position": [2640, -1420], "parameters": {}, "typeVersion": 1}, {"id": "59651d51-92ce-42e5-a9c7-4faa18526ef2", "name": "Implement YouTube Shorts Here", "type": "n8n-nodes-base.noOp", "position": [2640, -1260], "parameters": {}, "typeVersion": 1}, {"id": "7cb931d5-d890-4ea6-9fca-84b934dd911c", "name": "X Response", "type": "n8n-nodes-base.set", "position": [2840, -2180], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "4015bb20-da3b-4781-ab8c-46f4d826138e", "name": "output", "type": "string", "value": "={{ $('Social Media Publishing Router').item.json.data.route }}\n\n{{ $('Social Media Publishing Router').item.json.data.social_content.root_schema.name }}\n\n{{ $('Social Media Publishing Router').item.json.data.social_content.schema.post }}\n\n![{{ $('Social Media Publishing Router').item.json.data.social_content.root_schema.name }}]({{ $('Social Media Publishing Router').item.json.data.social_image.thumb.url }})"}]}}, "typeVersion": 3.4}, {"id": "90c4faa3-3376-41d1-83e7-0c4c2bc03de5", "name": "Instagram Response", "type": "n8n-nodes-base.set", "position": [2840, -2000], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "da8fe7e3-e74d-46b6-91eb-1bf4432b73b0", "name": "output", "type": "string", "value": "={{ $('Social Media Publishing Router').item.json.data.route }}  \n{{ $('Social Media Publishing Router').item.json.data.social_content.root_schema.name }}\n{{ $('Social Media Publishing Router').item.json.data.social_content.schema.caption }}\n![{{ $('Social Media Publishing Router').item.json.data.social_content.root_schema.name }}]({{ $('Social Media Publishing Router').item.json.data.social_image.thumb.url }})"}]}}, "typeVersion": 3.4}, {"id": "de57e2c7-acac-4268-a535-e90f00548956", "name": "Facebook Response", "type": "n8n-nodes-base.set", "position": [2840, -1820], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "e349e314-2967-456f-856a-85727bdf94f3", "name": "output", "type": "string", "value": "={{ $('Social Media Publishing Router').item.json.data.route }}\n\n{{ $('Social Media Publishing Router').item.json.data.social_content.root_schema.name }}\n\n{{ $('Social Media Publishing Router').item.json.data.social_content.schema.post }}\n\n![{{ $('Social Media Publishing Router').item.json.data.social_content.root_schema.name }}]({{ $('Social Media Publishing Router').item.json.data.social_image.thumb.url }})"}]}}, "typeVersion": 3.4}, {"id": "030e8933-0eae-4e6c-956d-dce0e702b163", "name": "LinkedIn Response", "type": "n8n-nodes-base.set", "position": [2840, -1640], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "88404fde-a41b-4da5-bbdb-0e41b879a52c", "name": "output", "type": "string", "value": "={{ $('Social Media Publishing Router').item.json.data.route }}\n\n{{ $('Social Media Publishing Router').item.json.data.social_content.root_schema.name }}\n\n{{ $('Social Media Publishing Router').item.json.data.social_content.schema.post }}\n\n![{{ $('Social Media Publishing Router').item.json.data.social_content.root_schema.name }}]({{ $('Social Media Publishing Router').item.json.data.social_image.thumb.url }})\n"}]}}, "typeVersion": 3.4}, {"id": "d48b6b3a-9410-4012-85bf-f70b4e91eccb", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [-1060, -1300], "parameters": {"color": 3, "height": 80, "content": "## Social Media Schema"}, "typeVersion": 1}, {"id": "e3ef0a13-42ca-4021-aeb5-2230f4ac7eac", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [-1840, -1300], "parameters": {"color": 5, "width": 760, "height": 80, "content": "## System Prompt"}, "typeVersion": 1}, {"id": "11457221-fa5e-41f4-93a5-bb3eea3f02a9", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [-120, -2200], "parameters": {"width": 620, "height": 320, "content": "# Social Media Router Agent"}, "typeVersion": 1}, {"id": "f40e9cde-1810-4471-9751-6da724a06f6c", "name": "🤖Social Media Router Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [60, -2080], "parameters": {"text": "=You are a helpful assistant that uses the provided tools.  Respond with a valid JSON object.\n\nUser prompt:  {{ $json.chatInput }}", "options": {"systemMessage": "## RULES\n- You do not answer the users questions directly and your sole purpose is to call the appropriate tool to and provide the verbatim response.\n\n## TOOLS\n- create_x_twitter_posts_tool: Use this tool to create X-Twitter posts\n- create_instagram_posts_tool: Use this tool to create Instagram posts\n- create_facebook_posts_tool: Use this tool to create Facebook posts\n- create_linkedin_posts_tool: Use this tool to create LinkedIn posts\n- create_threads_posts_tool: Use this tool to create Threads posts\n- create_youtube_short_tool: Use this tool to create a YouTube short\n\n\n"}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "e6715ea6-f70d-427b-bf3d-499ed8040140", "name": "Sticky Note14", "type": "n8n-nodes-base.stickyNote", "position": [800, -1720], "parameters": {"width": 400, "height": 440, "content": "## Prepare Email Approval Contents as HTML"}, "typeVersion": 1}, {"id": "cbb2a190-8aad-4e09-9d5c-a43dbcb32184", "name": "SerpAPI", "type": "@n8n/n8n-nodes-langchain.toolSerpApi", "position": [980, -360], "parameters": {"options": {}}, "credentials": {"serpApi": {"id": "DfdkTTaZtPp0iHYv", "name": "SerpAPI account"}}, "typeVersion": 1}, {"id": "58da1f9e-caa4-4ea2-8b02-3e734758af80", "name": "Sticky Note15", "type": "n8n-nodes-base.stickyNote", "position": [-500, -120], "parameters": {"color": 7, "width": 3520, "height": 680, "content": "# 💫Features & Benefits"}, "typeVersion": 1}, {"id": "e65c6e37-0523-4e7d-b634-d3499aef5516", "name": "Sticky Note16", "type": "n8n-nodes-base.stickyNote", "position": [-440, -40], "parameters": {"color": 7, "width": 680, "height": 280, "content": "## 1️⃣System Prompt Composition from External Source\nCentralized prompt management: Store and update system prompts in Google Docs for easy team collaboration\n\n- Consistent brand voice: Ensure all generated content maintains consistent tone and style across platforms\n\n- Flexible customization: Quickly modify prompts without changing workflow code\n\n- Version control: Track changes to prompts over time with Google Docs revision history\n\n- Role-specific access: Control who can edit core prompts while allowing broader viewing access\n"}, "typeVersion": 1}, {"id": "b7ccbaa4-3c33-4f32-a72d-fb693b9c63d6", "name": "Sticky Note17", "type": "n8n-nodes-base.stickyNote", "position": [-440, 260], "parameters": {"color": 7, "width": 680, "height": 260, "content": "## 2️⃣Output Schema Composition from External Source\n- Structured content generation: Enforce consistent JSON output formats for each platform\n\n- Platform-specific optimization: Tailor content structure to each social network's requirements\n\n- Reduced errors: Validate content against schemas before publishing\n\n- Easy updates: Modify schemas as platform requirements change without workflow modifications\n\n- Standardized metadata: Ensure all required fields are included for each platform"}, "typeVersion": 1}, {"id": "b36e874a-a893-439d-bd29-524ea567b695", "name": "Sticky Note19", "type": "n8n-nodes-base.stickyNote", "position": [720, -40], "parameters": {"color": 7, "width": 400, "height": 520, "content": "## 4️⃣ Dynamic Social Media Content Creator Agent with Web Search Tool to Match Social Post and Platform Schema\n\n- Real-time research: Incorporate current events and trending topics into content\n\n- Fact-checking: Verify information before publishing to maintain credibility\n\n- Competitive analysis: Reference industry trends and competitor content\n\n- Contextual relevance: Create content that responds to current market conditions\n\n- Enhanced engagement: Generate content that aligns with trending conversations"}, "typeVersion": 1}, {"id": "d57e47d1-e95e-45b6-95cf-a8952320da35", "name": "Sticky Note23", "type": "n8n-nodes-base.stickyNote", "position": [1160, -40], "parameters": {"color": 7, "width": 340, "height": 520, "content": "## 5️⃣ Dynamic Image Creation to Match Social Post & Platform Schema\n\n- Visual consistency: Generate platform-optimized images that match content themes\n\n- Automated alt text: Create accessibility-compliant image descriptions\n\n- Multi-format output: Generate images in various dimensions for different platforms\n\n- Brand compliance: Ensure all visuals align with brand guidelines\n\n- Reduced design bottlenecks: Eliminate waiting for custom graphics for each post"}, "typeVersion": 1}, {"id": "4323fd33-f2ff-434a-bd44-0ecb59fc4962", "name": "Sticky Note24", "type": "n8n-nodes-base.stickyNote", "position": [1520, -40], "parameters": {"color": 7, "width": 300, "height": 520, "content": "## 6️⃣ Image Archiving to Multiple Cloud Services for Future Use\n\n- Redundant storage: Prevent content loss with multi-location backups\n\n-Searchable repository: Build a library of past content for reference and reuse\n\n- Asset tracking: Maintain records of which images were used for which campaigns\n\n- Compliance documentation: Keep records of published content for regulatory purposes\n\n- Resource optimization: Reuse successful visual assets for future campaigns"}, "typeVersion": 1}, {"id": "e0dc7e2a-275f-4389-973a-e0d92af6de6c", "name": "Sticky Note27", "type": "n8n-nodes-base.stickyNote", "position": [1840, -40], "parameters": {"color": 7, "width": 260, "height": 520, "content": "## 7️⃣ Telegram Messaging for Workflow Status \n\n- Real-time notifications: Get immediate alerts about workflow execution\n\n- Error tracking: Quickly identify and address failures in the content pipeline\n\n- Team coordination: Keep stakeholders informed of content progress\n\n- Audit trail: Maintain records of when content was created and published\n\n- Remote monitoring: Track workflow execution from mobile devices"}, "typeVersion": 1}, {"id": "0d9940df-faad-4adc-a79f-4b89f2f2d16e", "name": "Sticky Note28", "type": "n8n-nodes-base.stickyNote", "position": [2120, -40], "parameters": {"color": 7, "width": 840, "height": 280, "content": "## 8️⃣ Optional Workflow Reporting to Gmail in with Structured HTML Content\n- Executive summaries: Provide management with clean, formatted reports\n\n- Content approval: Enable stakeholders to review content before publishing\n\n- Performance tracking: Document content metrics in standardized formats\n\n- Schedule adherence: Monitor if content is being published according to plan\n\n- Resource allocation: Track time and effort spent on different content types"}, "typeVersion": 1}, {"id": "febb3aa7-d0ed-422a-b115-33863889a152", "name": "Sticky Note29", "type": "n8n-nodes-base.stickyNote", "position": [2120, 260], "parameters": {"color": 7, "width": 840, "height": 260, "content": "## 9️⃣ Social Post Archiving to Google Drive for Future Use\n- Content library: Build a searchable repository of all published content\n\n- Performance correlation: Connect content with its performance metrics\n\n- Compliance records: Maintain documentation of published materials\n\n- Content repurposing: Easily find and adapt past successful content\n\n- Campaign documentation: Group related content for campaign analysis"}, "typeVersion": 1}, {"id": "697fab77-d1bb-41d8-8683-193230471721", "name": "Sticky Note30", "type": "n8n-nodes-base.stickyNote", "position": [260, -40], "parameters": {"width": 420, "height": 960, "content": "## 3️⃣Dynamic System Prompt and Platform Schema Composition based on User Prompt using External Sources\n- Centralized prompt management: Store all system prompts in a single external source for easier maintenance and updates\n\n- Platform-specific optimization: Automatically tailor content to each platform's unique requirements and audience expectations\n\n- Consistent brand voice: Ensure all generated content maintains your brand's tone and messaging guidelines across platforms\n\n- Reduced technical debt: Modify prompts and schemas without changing workflow code or redeploying applications\n\n- Collaborative improvement: Enable marketing teams to refine prompts without developer intervention\n\n- Version control: Track changes to prompts and schemas over time with document revision history\n\n- A/B testing capability: Easily test different prompt variations to optimize content performance\n\n- Scalable content strategy: Add support for new platforms by simply creating new prompt and schema documents\n\n- Dynamic adaptation: Respond to platform algorithm changes by quickly updating external prompt documents\n\n- Knowledge preservation: Maintain institutional knowledge about effective platform-specific content strategies\n\n- Reduced onboarding time: New team members can understand content requirements by reviewing documented prompts\n\n- Compliance management: Ensure all generated content follows legal and brand guidelines by centralizing rules"}, "typeVersion": 1}, {"id": "11ba3bef-7036-416b-a63d-a82cf7cbe30f", "name": "Prepare Social Media Email Contents", "type": "@n8n/n8n-nodes-langchain.agent", "position": [2300, -780], "parameters": {"text": "=Use the HTML template and populate [fields] as required from this: {{ $('pollinations.ai1').item.json.output.toJsonString() }}\n-----\nOnly output HTML without code block tags, preamble or further explanation in the format provided.\n\n## HTML Template\n<table style=\"max-width:640px;min-width:320px;width:100%;border-collapse:collapse;font-family:Arial,sans-serif;margin:20px auto\">\n    <tbody>\n        <tr>\n            <td colspan=\"2\" style=\"background-color:#ffffff;padding:15px;text-align:left\">\n                <img src=\"{{ $json.thumbnailLink }}\" alt=\"{{ $json.output.root_schema.name }}\" style=\"max-width:100%;height:auto;\">\n            </td>\n        </tr>\n        <tr>\n            <td colspan=\"2\" style=\"background-color:#efefef;padding:15px;font-size:20px;text-align:left;font-weight:bold\">\n                {{ $json.output.root_schema.name  }}\n            </td>\n        </tr>\n        <tr>\n            <td style=\"background-color:#f9f9f9;padding:15px;width:30%;text-align:left\"><strong>Platform:</strong></td>\n            <td style=\"background-color:#f9f9f9;padding:15px;text-align:left\">{{ $('Compose Prompt & Schema').item.json.route }}</td>\n        </tr>\n        <tr>\n            <td style=\"background-color:#f9f9f9;padding:15px;width:30%;text-align:left\"><strong>[label_1]:</strong></td>\n            <td style=\"background-color:#f9f9f9;padding:15px;text-align:left\">[content_1]</td>\n        </tr>\n        <tr>\n            <td style=\"background-color:#f1f1f1;padding:15px;text-align:left\"><strong>[label_2]:</strong></td>\n            <td style=\"background-color:#f1f1f1;padding:15px;text-align:left\">[content_2]</td>\n        </tr>\n\n        [continue the pattern ...]\n\n        <tr>\n            <td colspan=\"2\" style=\"background-color:#efefef;padding:15px;text-align:left\">\n                <strong>[footer_label]:</strong> [footer_content]\n            </td>\n        </tr>\n    </tbody>\n</table>\n\n", "options": {}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "1dc19a25-ff27-4582-a574-279831f7bc28", "name": "Sticky Note43", "type": "n8n-nodes-base.stickyNote", "position": [-760, -340], "parameters": {"height": 500, "content": "💡Notes\n\n- Create Google Doc for the Social Media Schema and copy the provided schema.\n\n- Update the Google Doc ID in the Social Media Schema node.\n\n- Create Google Doc for the Social Media System Prompt and copy the provided System Prompt.\n\n- Update the Google Doc ID in the Social Media System Prompt node.\n\n\n\nAdjust system prompt and platform specific prompts to suit your needs."}, "typeVersion": 1}, {"id": "6b1d2ad9-9ad8-4a33-ab7d-430f96dc317c", "name": "<PERSON><PERSON> Note44", "type": "n8n-nodes-base.stickyNote", "position": [1340, -360], "parameters": {"width": 300, "content": "💡Notes\n\nReplace pollinations.ai with any online image generation service that produces an image file you can download."}, "typeVersion": 1}, {"id": "e7c2d9ba-6b9a-404f-a84d-8e90e4c5f4bb", "name": "Sticky Note45", "type": "n8n-nodes-base.stickyNote", "position": [720, -840], "parameters": {"width": 400, "height": 140, "content": "💡Notes\n\nReplace Chat model with other LLMs and test out the results.  Add more tools or try other web search tools to suit your use case."}, "typeVersion": 1}, {"id": "********-dd0f-46d5-89c8-60fd92f1388e", "name": "gpt-4o", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-520, -1620], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "gpt-4o"}, "options": {"responseFormat": "json_object"}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1.2}], "active": false, "pinData": {"Social Media Schema": [{"json": {"content": "<common>\n{\n    \"type\": \"object\",\n    \"properties\": {\n        \"hashtags\": {\n            \"type\": \"array\",\n            \"items\": {\n                \"type\": \"string\"\n            }\n        },\n        \"image_suggestion\": {\n            \"type\": \"string\"\n        }\n    }\n}\n</common>\n\n<root>\n{\n    \"type\": \"object\",\n    \"properties\": {\n        \"name\": {\n            \"type\": \"string\"\n        },\n        \"description\": {\n            \"type\": \"string\"\n        },\n        \"additional_notes\": {\n            \"type\": \"string\"\n        }\n    }\n}\n</root>\n\n<linkedin>\n{\n    \"type\": \"object\",\n    \"properties\": {\n        \"post\": {\n            \"type\": \"string\"\n        },\n        \"call_to_action\": {\n            \"type\": \"string\"\n        }\n    }\n}\n</linkedin>\n\n<instagram>\n{\n    \"type\": \"object\",\n    \"properties\": {\n        \"caption\": {\n            \"type\": \"string\"\n        },\n        \"emojis\": {\n            \"type\": \"array\",\n            \"items\": {\n                \"type\": \"string\"\n            }\n        },\n        \"call_to_action\": {\n            \"type\": \"string\"\n        }\n    }\n}\n</instagram>\n\n<facebook>\n{\n    \"type\": \"object\",\n    \"properties\": {\n        \"post\": {\n            \"type\": \"string\"\n        },\n        \"call_to_action\": {\n            \"type\": \"string\"\n        }\n    }\n}\n</facebook>\n\n<xtwitter>\n{\n    \"type\": \"object\",\n    \"properties\": {\n        \"video_suggestion\": {\n            \"type\": \"string\"\n        },\n        \"post\": {\n            \"type\": \"string\"\n        },\n        \"character_limit\": {\n            \"type\": \"integer\"\n        }\n    }\n}\n</xtwitter>\n\n<threads>\n{\n    \"type\": \"object\",\n    \"properties\": {\n        \"text_post\": {\n            \"type\": \"string\"\n        },\n        \"call_to_action\": {\n            \"type\": \"string\"\n        }\n    }\n}\n</threads>\n\n<youtube_short>\n{\n    \"type\": \"object\",\n    \"properties\": {\n        \"video_suggestion\": {\n            \"type\": \"string\"\n        },\n        \"title\": {\n            \"type\": \"string\"\n        },\n        \"description\": {\n            \"type\": \"string\"\n        },\n        \"call_to_action\": {\n            \"type\": \"string\"\n        }\n    }\n}\n</youtube_short>\n\n\n\n", "documentId": "[your-doc-id-here]"}}], "Social Media System Prompt": [{"json": {"content": "<system>\nYou are a specialized content creation AI for social media platforms.\nYour primary function is generating platform-optimized social media content across various platforms including LinkedIn, Instagram, Facebook, Twitter (X), Threads, and YouTube Shorts. Each piece of content must:\nMatch the specific platform's audience expectations and algorithm preferences\nShowcase relevant expertise in your field\nDeliver actionable insights for your target audience\nDrive meaningful engagement through value-driven content\nOBJECTIVES:\nCreate platform-specific content following each platform's best practices\nImplement strategic hashtag usage combining general and trending tags\nDesign content that encourages user interaction and community building\nMaintain consistent brand voice while adapting to platform requirements\nIncorporate data-driven insights to maximize content performance\nOUTPUT REQUIREMENTS:\nDeliver content in valid JSON format according to the platform-specific schema\nInclude all required fields as specified in the schema\nOmit any explanatory text or code fencing in your response\nTailor content specifically to the platform indicated in the user's request\nFor each content request, adapt your output based on the platform guidelines and ensure it aligns with your organization's mission and values.  Never provide URLS for video or image suggestions and only describe the suggestion.\n</system>\n\n\n<rules>\n- Only provide final response in valid JSON for the appropriate social platform\n- Never include any preamble or further explanation\n- Always remove any ``` ```json\n</rules>\n\n\n<linkedin>\n**Style**: Professional and insightful.\n**Tone**: Business-oriented; focus on automation use cases, industry insights, and community impact.\n**Content Length**: 3-4 sentences; concise but detailed.\n**Hashtags**: #Innovation #Automation #WorkflowSolutions #DigitalTransformation #Leadership\n**Call to Action (CTA)**: Encourage comments or visits to workflows.diy's website for more insights.\n</linkedin>\n\n<instagram>\n**Style**: Visual storytelling with creative captions.\n**Tone**: Inspirational and engaging; use emojis for relatability.\n**Content Length**: 2-3 sentences paired with eye-catching visuals (e.g., infographics or workflow demos).\n**Visuals**: Showcase milestones (e.g., new workflow launches), tutorials, or product highlights.\n**CTA**: Use phrases like \"Swipe to learn more,\" \"Tag your team,\" or \"Check out the link below!\"\n**Link Placement**: Add the provided link before hashtags; if no link is provided, use \"Visit our website: https://workflows.diy.\"\n**Hashtags**: #AutomationLife #TechInnovation #WorkflowTips #Programming #Engineering\n</instagram>\n\n<facebook>\n**Style**: Friendly and community-focused.\n**Tone**: Relatable; highlight user success stories or company achievements in automation.\n**Content Length**: 2-3 sentences; conversational yet professional.\n**Hashtags**: #SmallBusinessAutomation #Entrepreneurship #Leadership #WorkflowInnovation\n**CTA**: Encourage likes, shares, comments (e.g., \"What's your favorite automation tip?\").\n</facebook>\n\n<xtwitter>\n**Style**: Concise and impactful.\n**Tone**: Crisp and engaging; spark curiosity in 150 characters or less.\n**Hashtags**: #WorkflowTrends #AIWorkflows #AutomationTips #NoCodeSolutions\n**CTA**: Drive quick engagement through retweets or replies (e.g., \"What's your go-to n8n workflow?\").\n</xtwitter>\n\n<threads>\n**Style**: Conversational and community-driven posts.\n**Tone**: Casual yet informative; encourage discussions around automation trends or innovations.\n**Content Length**: 1-2 short paragraphs with a question or thought-provoking statement at the end.\n**Hashtags**: Similar to Instagram but tailored for trending Threads topics related to automation.\n</threads>\n\n<youtube_short>\n**Style**: Short-form video content showcasing quick workflow tutorials or use cases.\n**Tone**: Authoritative yet approachable; establish workflows.diy as a leader in n8n automation solutions.\n**Content Length**:\n  - Tutorials/Reviews (long-form): 5-10 minutes\n  - Shorts/Highlights (short-form): Under 1 minute\n**CTA**: Encourage subscriptions, likes, comments (e.g., \"Subscribe for more workflow tips!\").\n</youtube_short>\n\n\n\n", "documentId": "[your-doc-id-here]"}}], "When Executed by Another Workflow": [{"json": {"route": "instagram", "user_prompt": "i need an instagram post about using n8n to transform business automation with reference to a related historical fact and example"}}]}, "settings": {"executionOrder": "v1"}, "versionId": "110ac387-48e7-4ed2-98d6-0e3ddbb34063", "connections": {"Gmail": {"main": [[]]}, "Merge": {"main": [[{"node": "Prepare Social Media Email Contents", "type": "main", "index": 0}, {"node": "Google Drive Image Meta", "type": "main", "index": 0}]]}, "Short": {"ai_tool": [[{"node": "🤖Social Media Router Agent", "type": "ai_tool", "index": 0}]]}, "Schema": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "X Post": {"main": [[{"node": "X Response", "type": "main", "index": 0}]]}, "gpt-4o": {"ai_languageModel": [[{"node": "🤖Social Media Router Agent", "type": "ai_languageModel", "index": 0}]]}, "File Id": {"main": [[{"node": "Get Social Post from Google Drive", "type": "main", "index": 0}]]}, "SerpAPI": {"ai_tool": [[{"node": "Social Media Content Creator", "type": "ai_tool", "index": 0}]]}, "Facebook": {"ai_tool": [[{"node": "🤖Social Media Router Agent", "type": "ai_tool", "index": 0}]]}, "LinkedIn": {"ai_tool": [[{"node": "🤖Social Media Router Agent", "type": "ai_tool", "index": 0}]]}, "X-Twiter": {"ai_tool": [[{"node": "🤖Social Media Router Agent", "type": "ai_tool", "index": 0}]]}, "Instagram": {"ai_tool": [[{"node": "🤖Social Media Router Agent", "type": "ai_tool", "index": 0}]]}, "gpt-40-mini": {"ai_languageModel": [[{"node": "Prepare Social Media Email Contents", "type": "ai_languageModel", "index": 0}]]}, "gpt-4o-mini": {"ai_languageModel": [[{"node": "Social Media Content Creator", "type": "ai_languageModel", "index": 0}]]}, "Is Approved?": {"main": [[{"node": "Get Social Post Image", "type": "main", "index": 0}]]}, "Parse Schema": {"main": [[{"node": "<PERSON><PERSON> Prompts and <PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "gpt-40-mini1": {"ai_languageModel": [[{"node": "Prepare Email Contents", "type": "ai_languageModel", "index": 0}]]}, "Facebook Post": {"main": [[{"node": "Facebook Response", "type": "main", "index": 0}]]}, "LinkedIn Post": {"main": [[{"node": "LinkedIn Response", "type": "main", "index": 0}]]}, "System Prompt": {"main": [[{"node": "Parse System Prompt", "type": "main", "index": 0}]]}, "YouTube Short": {"ai_tool": [[{"node": "🤖Social Media Router Agent", "type": "ai_tool", "index": 0}]]}, "Social Content": {"main": [[{"node": "pollinations.ai1", "type": "main", "index": 0}]]}, "Extract as JSON": {"main": [[{"node": "Merge Image and Post Contents", "type": "main", "index": 0}, {"node": "Prepare Email Contents", "type": "main", "index": 0}]]}, "Instagram Image": {"main": [[{"node": "Instragram Post", "type": "main", "index": 0}]]}, "Instragram Post": {"main": [[{"node": "Instagram Response", "type": "main", "index": 0}]]}, "Social Post JSON": {"main": [[{"node": "Save Social Post to Google Drive", "type": "main", "index": 0}]]}, "pollinations.ai1": {"main": [[{"node": "Telegram Success Message (Optional)", "type": "main", "index": 0}, {"node": "Save Image to imgbb.com", "type": "main", "index": 0}, {"node": "Save Image to Google Drive", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 1}], [{"node": "Telegram Error Message (Optional)", "type": "main", "index": 0}]]}, "Parse System Prompt": {"main": [[{"node": "<PERSON><PERSON> Prompts and <PERSON><PERSON><PERSON>", "type": "main", "index": 2}]]}, "Social Media Schema": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "🤖Social Media Router Agent", "type": "ai_memory", "index": 0}]]}, "Get Social Post Image": {"main": [[{"node": "Merge Image and Post Contents", "type": "main", "index": 1}]]}, "Prepare Email Contents": {"main": [[{"node": "Gmail User for Approval", "type": "main", "index": 0}]]}, "Compose Prompt & Schema": {"main": [[{"node": "Social Media Content Creator", "type": "main", "index": 0}]]}, "Gmail User for Approval": {"main": [[{"node": "Is Approved?", "type": "main", "index": 0}]]}, "Google Drive Image Meta": {"main": [[{"node": "Social Post JSON", "type": "main", "index": 0}]]}, "Save Image to imgbb.com": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Merge Prompts and Schema": {"main": [[{"node": "Compose Prompt & Schema", "type": "main", "index": 0}]]}, "Save Image to Google Drive": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 2}]]}, "Social Media System Prompt": {"main": [[{"node": "System Prompt", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "🤖Social Media Router Agent", "type": "main", "index": 0}]]}, "Social Media Content Creator": {"main": [[{"node": "Social Content", "type": "main", "index": 0}]]}, "Merge Image and Post Contents": {"main": [[{"node": "Social Media Publishing Router", "type": "main", "index": 0}]]}, "🤖Social Media Router Agent": {"main": [[{"node": "File Id", "type": "main", "index": 0}]]}, "Social Media Publishing Router": {"main": [[{"node": "X Post", "type": "main", "index": 0}], [{"node": "Instagram Image", "type": "main", "index": 0}], [{"node": "Facebook Post", "type": "main", "index": 0}], [{"node": "LinkedIn Post", "type": "main", "index": 0}], [{"node": "Implement Threads Here", "type": "main", "index": 0}], [{"node": "Implement YouTube Shorts Here", "type": "main", "index": 0}]]}, "Save Social Post to Google Drive": {"main": [[{"node": "Respond with Google Drive Id", "type": "main", "index": 0}]]}, "Get Social Post from Google Drive": {"main": [[{"node": "Extract as JSON", "type": "main", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Social Media System Prompt", "type": "main", "index": 0}, {"node": "Social Media Schema", "type": "main", "index": 0}, {"node": "<PERSON><PERSON> Prompts and <PERSON><PERSON><PERSON>", "type": "main", "index": 1}]]}, "Prepare Social Media Email Contents": {"main": [[{"node": "Gmail", "type": "main", "index": 0}]]}}}