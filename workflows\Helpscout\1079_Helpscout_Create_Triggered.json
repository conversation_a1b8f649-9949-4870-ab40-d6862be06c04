{"id": "61", "name": "Receive updates when a customer is created in HelpScout", "nodes": [{"name": "HelpScout Trigger", "type": "n8n-nodes-base.helpScoutTrigger", "position": [690, 260], "webhookId": "aaaf8b3f-8247-4d98-ae65-8c6626aade95", "parameters": {"events": ["customer.created"]}, "credentials": {"helpScoutOAuth2Api": "helpscout"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {}}