{"id": "Qj1307oyBx1hZJy5", "meta": {"instanceId": "1abe0e4c2be794795d12bf72aa530a426a6f87aabad209ed6619bcaf0f666fb0", "templateCredsSetupCompleted": true}, "name": "SSL Expiry <PERSON>", "tags": [{"id": "aqlZb2qfWiaT4Xr5", "name": "IT Ops", "createdAt": "2025-01-03T12:20:11.917Z", "updatedAt": "2025-01-03T12:20:11.917Z"}, {"id": "zJaZorWWcGpTp35U", "name": "DevOps", "createdAt": "2025-01-03T12:19:34.273Z", "updatedAt": "2025-01-03T12:19:34.273Z"}], "nodes": [{"id": "260b66a2-0841-4dc7-9666-acbc9317fd91", "name": "URLs to Monitor", "type": "n8n-nodes-base.googleSheets", "position": [1120, -120], "parameters": {"columns": {"value": {"URL": "={{ $json.result.host }}", "KnownExpiryDate": "={{ $json.result.valid_till }}"}, "schema": [{"id": "Website ", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Website ", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URL", "type": "string", "display": true, "removed": false, "required": false, "displayName": "URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "KnownExpiryDate", "type": "string", "display": true, "required": false, "displayName": "KnownExpiryDate", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["URL"]}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1VfsX4cW2oKQ3ZHUjBvGk--d1X7509c6__b6gPvA5VpI/edit#gid=0", "cachedResultName": "URLs to Check"}, "documentId": {"__rl": true, "mode": "url", "value": "https://docs.google.com/spreadsheets/d/1VfsX4cW2oKQ3ZHUjBvGk--d1X7509c6__b6gPvA5VpI/edit?gid=0#gid=0"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "I7vwmkFVGPrI7Os1", "name": "V<PERSON>al - Google Sheets"}}, "typeVersion": 4.5}, {"id": "a2922f1b-9d29-4b66-9560-44207f3e14d2", "name": "Weekly Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [160, 140], "parameters": {"rule": {"interval": [{"field": "weeks", "triggerAtDay": [1], "triggerAtHour": 8}]}}, "typeVersion": 1.2}, {"id": "005564e9-5ecb-4ee9-aca0-69a660656b09", "name": "Fetch URLs", "type": "n8n-nodes-base.googleSheets", "position": [420, 140], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "url", "value": "https://docs.google.com/spreadsheets/d/1pnUfIkD90MUG99Fp0vRoAB-w-GPSAwRZw0-JsNl-h3s/edit?gid=0#gid=0"}, "documentId": {"__rl": true, "mode": "url", "value": "https://docs.google.com/spreadsheets/d/1pnUfIkD90MUG99Fp0vRoAB-w-GPSAwRZw0-JsNl-h3s/edit?usp=sharing"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "I7vwmkFVGPrI7Os1", "name": "V<PERSON>al - Google Sheets"}}, "typeVersion": 4.5}, {"id": "943c561c-ca89-461c-a6fb-c3011baaf81a", "name": "Check SSL", "type": "n8n-nodes-base.httpRequest", "position": [680, 140], "parameters": {"url": "=https://ssl-checker.io/api/v1/check/{{ $json[\"URL\"].replace(/^https?:\\/\\//, \"\").replace(/\\/$/, \"\") }}", "options": {}}, "typeVersion": 4.2}, {"id": "911fa691-decf-4572-a46e-d8644d3b2a35", "name": "Expiry <PERSON>", "type": "n8n-nodes-base.if", "position": [1120, 220], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "ee6e2ce8-569a-4f1f-91b5-2c55f605a16b", "operator": {"type": "number", "operation": "lte"}, "leftValue": "={{ $json.result.days_left }}", "rightValue": 7}]}}, "typeVersion": 2.2}, {"id": "8b59ebbb-0a87-40c2-be79-cc38431ebdbd", "name": "<PERSON> <PERSON><PERSON>", "type": "n8n-nodes-base.gmail", "position": [1440, 240], "webhookId": "cd6b6b20-e619-4526-aa69-64754e3d9035", "parameters": {"sendTo": "phani<PERSON><PERSON>@quantana.com", "message": "=SSL Expiry - {{ $json.result.days_left }} Days Left - {{ $json.result.host }}", "options": {"appendAttribution": false}, "subject": "=SSL Expiry - {{ $json.result.days_left }} Days Left - {{ $json.result.host }}", "emailType": "text"}, "credentials": {"gmailOAuth2": {"id": "brYm5tKb5se1DyUw", "name": "<PERSON><PERSON><PERSON>"}}, "typeVersion": 2.1}, {"id": "32eebd68-f0e6-467c-bf65-f2d513a60666", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [100, 0], "parameters": {"height": 329.860465116279, "content": "Triggers the workflow once a week."}, "typeVersion": 1}, {"id": "3c0ed796-94a4-488c-9cb7-e3d46db63815", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [360, 0], "parameters": {"height": 327.0154373927959, "content": "Pulls the list of URLs to monitor from the Google Sheet. Ensure you clone the Google Sheet worksheet and update this node with its URL."}, "typeVersion": 1}, {"id": "fdb2077c-7d6a-4255-b499-e90513a0de1d", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [620, 0], "parameters": {"height": 323.89365351629556, "content": "Uses SSL-Checker.io to verify the SSL certificate of each URL. Fetches details like the host, validity period, and days remaining until expiry."}, "typeVersion": 1}, {"id": "5cc1644b-6abc-4299-8a25-9507b09d863f", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1060, -260], "parameters": {"height": 344.1852487135509, "content": "Updates the Google Sheet with SSL details, including the expiry date and certificate status."}, "typeVersion": 1}, {"id": "1001a69e-8efc-4a8b-a97b-a1bc021ada35", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1060, 140], "parameters": {"height": 344.1852487135509, "content": "Checks if any SSL certificate is set to expire in 7 days or less."}, "typeVersion": 1}, {"id": "ad9e359e-3d95-4e8c-97b0-d06475bb8883", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1360, 140], "parameters": {"height": 344.1852487135509, "content": "Sends an email alert if an SSL certificate is nearing expiry, including the host and days remaining."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"timezone": "Asia/Kolkata", "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": "rL70w0U4LV2l9a5b", "executionOrder": "v1"}, "versionId": "f60d6e6e-dace-497a-b58b-113993ec36e5", "connections": {"Check SSL": {"main": [[{"node": "URLs to Monitor", "type": "main", "index": 0}, {"node": "Expiry <PERSON>", "type": "main", "index": 0}]]}, "Fetch URLs": {"main": [[{"node": "Check SSL", "type": "main", "index": 0}]]}, "Expiry Alert": {"main": [[{"node": "<PERSON> <PERSON><PERSON>", "type": "main", "index": 0}]]}, "Weekly Trigger": {"main": [[{"node": "Fetch URLs", "type": "main", "index": 0}]]}}}