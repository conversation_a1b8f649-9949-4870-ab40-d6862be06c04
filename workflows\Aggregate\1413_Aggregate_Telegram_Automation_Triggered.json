{"name": "Dsp agent", "nodes": [{"parameters": {"updates": ["message"], "additionalFields": {"download": false}}, "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.1, "position": [-600, 500], "id": "8e952294-ec48-426e-ad2c-775ab295afb7", "name": "<PERSON>eg<PERSON>", "webhookId": "ece1b7c8-0758-4c1f-8db2-6a14ba1ed182", "credentials": {"telegramApi": {"id": "VrV0OZcaiBOi3ejB", "name": "Telegram account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.message.text }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "id": "b8cc5586-5c76-4295-b8ba-1cecfa47cc5d"}], "combinator": "and"}, "renameOutput": true, "outputKey": "text"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "66856d79-632e-4e2d-9e54-6e28df629aeb", "leftValue": "={{ $json.message.voice.file_id }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "voice"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-320, 160], "id": "faef9906-72b5-47b3-8707-4c34c81c9096", "name": "Switch", "retryOnFail": false, "alwaysOutputData": false}, {"parameters": {"assignments": {"assignments": [{"id": "4e2b9056-34d7-4867-8f1e-4265fe80bb8c", "name": "text", "value": "={{ $('<PERSON><PERSON><PERSON> Trigger').item.json.message.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [0, 0], "id": "5a51d584-0484-4757-903b-e772a634f94e", "name": "<PERSON>"}, {"parameters": {"resource": "file", "fileId": "={{ $json.message.voice.file_id }}"}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-100, 260], "id": "627c1d4b-a495-4a2f-8a07-e3699a71b671", "name": "Telegram", "webhookId": "21933f09-43da-413d-ab94-a6af068c35b6", "credentials": {"telegramApi": {"id": "VrV0OZcaiBOi3ejB", "name": "Telegram account"}}}, {"parameters": {"resource": "audio", "operation": "transcribe", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [40, 260], "id": "10edf485-e6bc-453a-b2ff-cc061ed73adc", "name": "OpenAI", "credentials": {"openAiApi": {"id": "IOLYY7gLnrluESNv", "name": "OpenAi account"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.text }}", "options": {"systemMessage": "=\n**Current time and date:** {{$now}}  \n\nHey there! You are an advanced study assistant, built to help students tackle complex problems in signal processing. You’re not just here to give answers—you’re here to **guide the user, deepen their understanding, and make learning more interactive**.  \n\nYou have access to several powerful tools, and knowing when and how to use them is key to being truly effective. Here’s what you can do and how you should approach each situation:  \n\n### **Your Capabilities and How to Use Them**  \n\n#### **1. Language Model (LLM) – Your Core Intelligence**  \n- You analyze questions, provide explanations, refine wording, and help the user grasp key signal processing concepts.  \n- Your job is to **guide the user toward the solution** rather than just giving direct answers—ask the right questions to encourage deeper thinking.  \n\n#### **2. Wikipedia Access – Your Knowledge Base**  \n- When a user asks about theoretical concepts, mathematical principles, or physics-related topics, you can **retrieve summarized, reliable information** from Wikipedia.  \n- This is great for definitions, historical context, and fundamental principles that support problem-solving.  \n\n#### **3. Calculator – Your Instant Problem Solver**  \n- You can quickly compute mathematical expressions, integrals, derivatives, and more.  \n- Use this tool when the user needs a quick numerical solution or when breaking down an equation.  \n\n#### **4. Memory Storage – Your Personalization Engine**  \n- You **remember relevant user details** to provide a more personalized experience.  \n- This allows you to track learning progress, recall previous topics, and offer tailored recommendations.  \n\n#### **5. (Coming Soon) Python / MATLAB Code Generation – Your Computational Power**  \n- Once integrated, you’ll be able to **generate Python and MATLAB code** to solve signal processing problems.  \n- This will include tasks like designing filters, performing Fourier transforms, and running simulations to analyze data.  \n\n- contentCreatorAgent: Use this tool to create blog posts\n---\n\n### **How You Should Interact with the User**  \n\n#### **Step 1: Understand the User’s Needs**  \n- If the question is unclear, don’t assume—**ask for clarification** or guide them with follow-up questions.  \n- Figure out if they need a **theoretical explanation, a step-by-step solution, or just study guidance**.  \n\n#### **Step 2: Choose the Right Approach**  \n- If it’s a **theory-based question**, pull relevant knowledge from Wikipedia or explain it in your own words.  \n- If it’s a **numerical problem**, use the calculator or suggest an appropriate method to solve it.  \n- If it requires **MATLAB or Python-based solutions**, propose an implementation and (once available) generate the code.  \n\n#### **Step 3: Encourage Learning and Retention**  \n- Always check if the user **fully understands the topic**—ask follow-up questions if necessary.  \n- If they struggle, offer alternative explanations or different ways to approach the problem.  \n- Use your memory storage to **connect topics and build continuity**, so the learning experience feels more cohesive over time.  \n\nYour role isn’t just to answer questions—you’re a **mentor, tutor, and study partner**. The goal is to **help the user develop problem-solving skills** so they can confidently tackle complex challenges on their own.  \n\nNow, go out there and make learning signal processing easier and more engaging! "}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [520, 480], "id": "b05d3c86-eca0-4a69-81ea-4b3f078d4f18", "name": "AI Agent"}, {"parameters": {"modelName": "models/gemini-1.5-flash-001", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [220, 920], "id": "921b72db-200a-4a47-bd2d-135c4f8450c8", "name": "Google Gemini Chat Model"}, {"parameters": {"chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "text": "={{ $json.output }}", "additionalFields": {"appendAttribution": false}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [880, 480], "id": "32277fd6-3d66-4bb9-a1c6-07d23d0d50b3", "name": "Telegram1", "webhookId": "e1966a9e-b402-4d56-92ff-7042f181ed35", "credentials": {"telegramApi": {"id": "VrV0OZcaiBOi3ejB", "name": "Telegram account"}}, "onError": "continueRegularOutput"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolCalculator", "typeVersion": 1, "position": [380, 900], "id": "3276e9b7-358f-4b9a-8537-918ce7c9bc54", "name": "Calculator"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolWikipedia", "typeVersion": 1, "position": [520, 880], "id": "76c41081-f01d-43bc-8895-3af69cc8ceea", "name": "Wikipedia"}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "appoBzMsCIm3Bno0X", "mode": "list", "cachedResultName": "Agent memory", "cachedResultUrl": "https://airtable.com/appoBzMsCIm3Bno0X"}, "table": {"__rl": true, "value": "tblb5AH2UtMVj3HLZ", "mode": "list", "cachedResultName": "Memory", "cachedResultUrl": "https://airtable.com/appoBzMsCIm3Bno0X/tblb5AH2UtMVj3HLZ"}, "returnAll": false, "limit": 50, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [-360, 660], "id": "38834d64-56fb-4170-9885-8d5e5c94a74f", "name": "Airtable", "credentials": {"airtableTokenApi": {"id": "eWfDvgRAeJ0q7Unh", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "Memory"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [-60, 660], "id": "f5f3fbf7-26ce-4754-bcc1-1d046b1a6e0a", "name": "Aggregate"}, {"parameters": {"mode": "combine", "combineBy": "combineAll", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3, "position": [320, 480], "id": "390ccee0-48c6-434d-ad51-53148540ddbe", "name": "<PERSON><PERSON>"}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('Telegram Trigger').item.json.message.chat.id }}"}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [400, 680], "id": "99b213f3-73c9-4649-b5d6-a7aa67886daf", "name": "Simple Memory"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [220, 680], "id": "a3bf96ef-ad73-44f2-a867-42ba149082ed", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "IOLYY7gLnrluESNv", "name": "OpenAi account"}}}, {"parameters": {"operation": "create", "base": {"__rl": true, "value": "appoBzMsCIm3Bno0X", "mode": "list", "cachedResultName": "Agent memory", "cachedResultUrl": "https://airtable.com/appoBzMsCIm3Bno0X"}, "table": {"__rl": true, "value": "tblb5AH2UtMVj3HLZ", "mode": "list", "cachedResultName": "Memory", "cachedResultUrl": "https://airtable.com/appoBzMsCIm3Bno0X/tblb5AH2UtMVj3HLZ"}, "columns": {"mappingMode": "defineBelow", "value": {"Memory": "={{ $fromAI('add_Memory', `Write a memory about the user for future referance in 140 characters `, 'string') }}"}, "matchingColumns": ["id"], "schema": [{"id": "Memory", "displayName": "Memory", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtableTool", "typeVersion": 2.1, "position": [660, 880], "id": "44bf3697-1689-4f8a-8363-ce547d614cae", "name": "memory_tool", "credentials": {"airtableTokenApi": {"id": "eWfDvgRAeJ0q7Unh", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"name": "contentCreatorAgent", "description": "call this tool whan you need to create contact,post or blog", "workflowId": {"__rl": true, "value": "ma0fuAza3j9sB4PL", "mode": "list", "cachedResultName": "My project — contact creator agent"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2.1, "position": [820, 880], "id": "2fc2f3f7-c8ba-4fb8-86be-ad72938df0b7", "name": "contentCreatorAgent"}, {"parameters": {"name": "EmailAgent", "description": "use this tool to send,get and lable emails", "workflowId": {"__rl": true, "value": "ANJ05aXmXcKpfhyk", "mode": "list", "cachedResultName": "Email agent"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2.1, "position": [1000, 880], "id": "833dce37-a852-4341-92f4-1ae3d41a0914", "name": "Email Agent"}], "pinData": {}, "connections": {"Telegram Trigger": {"main": [[{"node": "Airtable", "type": "main", "index": 0}, {"node": "Switch", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}], [{"node": "Telegram", "type": "main", "index": 0}]]}, "Telegram": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Telegram1", "type": "main", "index": 0}]]}, "Calculator": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Wikipedia": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Airtable": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Merge": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "memory_tool": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "contentCreatorAgent": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Email Agent": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "bfadace7-e00a-4849-97b9-d8e13fb0c0b2", "meta": {"instanceId": "94de0b0234836a6581f98085078a07c06e3d6f8dac7b83621b73e6356c09de9b"}, "id": "Ix2EKF85AgkBkvOG", "tags": []}