
# Project Management - N8N Workflows

## Overview
This document catalogs the **Project Management** workflows from the n8n Community Workflows repository.

**Category:** Project Management  
**Total Workflows:** 34  
**Generated:** 2025-07-27  
**Source:** https://scan-might-updates-postage.trycloudflare.com/api

---

## Workflows

### Create a new task in Todoist
**Filename:** `0007_Manual_Todoist_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Todoist to create new records. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Todoist,  

---

### Create a task in ClickUp
**Filename:** `0030_Manual_Clickup_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Clickup to create new records. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Clickup,  

---

### Trello Googlecloudnaturallanguage Automate Triggered
**Filename:** `0044_Trello_Googlecloudnaturallanguage_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Typeform, Trello, and Googlecloudnaturallanguage for data processing. Uses 6 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (6 nodes)  
**Integrations:** Typeform,Trello,Googlecloudnaturallanguage,Notion,Slack,  

---

### Receive updates for events in ClickUp
**Filename:** `0047_Clickup_Update_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Clickup to update existing data. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Clickup,  

---

### Trello Googlecalendar Create Scheduled
**Filename:** `0053_Trello_GoogleCalendar_Create_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Splitinbatches, Trello, and Google Calendar to create new records. Uses 8 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (8 nodes)  
**Integrations:** Splitinbatches,Trello,Google Calendar,  

---

### Receive updates for changes in the specified list in Trello
**Filename:** `0076_Trello_Update_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Trello to update existing data. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Trello,  

---

### User Request Management
**Filename:** `0215_Typeform_Clickup_Automation_Triggered.json`  
**Description:** Webhook-triggered automation that connects Typeform and Clickup for data processing. Uses 7 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Typeform,Clickup,  

---

### Asana Notion Create Triggered
**Filename:** `0241_Asana_Notion_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Notion and Asana to create new records. Uses 10 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** Notion,Asana,  

---

### Clickup Notion Update Triggered
**Filename:** `0282_Clickup_Notion_Update_Triggered.json`  
**Description:** Webhook-triggered automation that connects Notion and Clickup to update existing data. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Notion,Clickup,  

---

### Datetime Todoist Create Webhook
**Filename:** `0444_Datetime_Todoist_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Crypto, Datetime, and Httprequest to create new records. Uses 19 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (19 nodes)  
**Integrations:** Crypto,Datetime,Httprequest,Box,Itemlists,Todoist,  

---

### Code Todoist Create Scheduled
**Filename:** `0446_Code_Todoist_Create_Scheduled.json`  
**Description:** Scheduled automation that connects Todoist and Box to create new records. Uses 13 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (13 nodes)  
**Integrations:** Todoist,Box,  

---

### Clickup Respondtowebhook Create Webhook
**Filename:** `0469_Clickup_Respondtowebhook_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Webhook, Clickup, and Slack to create new records. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (6 nodes)  
**Integrations:** Webhook,Clickup,Slack,  

---

### Add task to tasklist
**Filename:** `0744_Manual_Googletasks_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Google Tasks for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Google Tasks,  

---

### Receive updates when an event occurs in Asana
**Filename:** `0967_Asana_Update_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Asana to update existing data. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Asana,  

---

### Manual Mondaycom Automate Triggered
**Filename:** `1024_Manual_Mondaycom_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Monday.com for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Monday.com,  

---

### CFP Selection 2
**Filename:** `1028_Manual_Trello_Automation_Triggered.json`  
**Description:** Manual workflow that orchestrates Airtable, Bannerbear, and Trello for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Airtable,Bannerbear,Trello,  

---

### Get Product Feedback
**Filename:** `1091_Noop_Trello_Import_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Airtable, Typeform, and Trello for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (6 nodes)  
**Integrations:** Airtable,Typeform,Trello,  

---

### Create, update, and get an issue on Taiga
**Filename:** `1100_Manual_Taiga_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Taiga to create new records. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Taiga,  

---

### Receive updates when an event occurs in Taiga
**Filename:** `1114_Taiga_Update_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Taiga to update existing data. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Taiga,  

---

### Manual Wekan Automation Triggered
**Filename:** `1115_Manual_Wekan_Automation_Triggered.json`  
**Description:** Manual workflow that integrates with Wekan for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (6 nodes)  
**Integrations:** Wekan,  

---

### Create a new card in Trello
**Filename:** `1175_Manual_Trello_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Trello to create new records. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Trello,  

---

### Asana Webhook Automate Webhook
**Filename:** `1223_Asana_Webhook_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that connects Webhook and Asana for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Webhook,Asana,  

---

### Create a new task in Asana
**Filename:** `1225_Manual_Asana_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Asana to create new records. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Asana,  

---

### Trello Googlecloudnaturallanguage Create Triggered
**Filename:** `1298_Trello_Googlecloudnaturallanguage_Create_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Typeform, Trello, and Googlecloudnaturallanguage to create new records. Uses 6 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (6 nodes)  
**Integrations:** Typeform,Trello,Googlecloudnaturallanguage,Notion,Slack,  

---

### Trello Limit Automate Scheduled
**Filename:** `1302_Trello_Limit_Automate_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Trello, Gmail, and Rssfeedread for data processing. Uses 15 nodes and integrates with 4 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** medium (15 nodes)  
**Integrations:** Trello,Gmail,Rssfeedread,Form Trigger,  

---

### Code Todoist Automate Scheduled
**Filename:** `1478_Code_Todoist_Automate_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Todoist, Gmail, and Rssfeedread for data processing. Uses 7 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (7 nodes)  
**Integrations:** Todoist,Gmail,Rssfeedread,Form Trigger,  

---

### Microsoft Outlook AI Email Assistant
**Filename:** `1551_Mondaycom_Schedule_Send_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, Splitinbatches, and OpenAI for data processing. Uses 28 nodes and integrates with 9 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (28 nodes)  
**Integrations:** Markdown,Splitinbatches,OpenAI,Airtable,Agent,Outputparserstructured,Outlook,Monday.com,Microsoftoutlook,  

---

### TEMPLATES
**Filename:** `1553_Mondaycom_Splitout_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Monday.com, Splitout, and Httprequest for data processing. Uses 14 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** Monday.com,Splitout,Httprequest,Converttofile,  

---

### Email mailbox as Todoist tasks
**Filename:** `1749_Todoist_Schedule_Send_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Email (IMAP), and Agent for data processing. Uses 25 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (25 nodes)  
**Integrations:** OpenAI,Email (IMAP),Agent,Gmail,Outputparserstructured,Box,Todoist,  

---

### MONDAY GET FULL ITEM
**Filename:** `1781_Mondaycom_Splitout_Import_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Monday.com, Splitout, and Executeworkflow for data processing. Uses 26 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (26 nodes)  
**Integrations:** Monday.com,Splitout,Executeworkflow,  

---

### Zoom AI Meeting Assistant
**Filename:** `1785_Stopanderror_Clickup_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, OpenAI, and Clickup for data processing. Uses 24 nodes and integrates with 12 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (24 nodes)  
**Integrations:** Splitinbatches,OpenAI,Clickup,Splitout,Extractfromfile,Toolworkflow,Emailsend,Httprequest,Form Trigger,Executeworkflow,Cal.com,Zoom,  

---

### Zoom AI Meeting Assistant
**Filename:** `1894_Stopanderror_Clickup_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Toolthink, and Clickup for data processing. Uses 25 nodes and integrates with 14 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (25 nodes)  
**Integrations:** Splitinbatches,Toolthink,Clickup,Splitout,Agent,Extractfromfile,Toolworkflow,Emailsend,Anthropic,Httprequest,Form Trigger,Executeworkflow,Cal.com,Zoom,  

---

### Automate Your Customer Service With WhatsApp Business Cloud & Asana
**Filename:** `1908_Form_Asana_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates WhatsApp, Asana, and Form Trigger for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** WhatsApp,Asana,Form Trigger,  

---

### Microsoft Outlook AI Email Assistant
**Filename:** `1974_Mondaycom_Schedule_Send_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, Splitinbatches, and OpenAI for data processing. Uses 28 nodes and integrates with 9 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (28 nodes)  
**Integrations:** Markdown,Splitinbatches,OpenAI,Airtable,Agent,Outputparserstructured,Outlook,Monday.com,Microsoftoutlook,  

---


## Summary

**Total Project Management workflows:** 34  
**Documentation generated:** 2025-07-27 14:37:11  
**API Source:** https://scan-might-updates-postage.trycloudflare.com/api  

This documentation was automatically generated using the n8n workflow API endpoints.
