{"meta": {"instanceId": "dbd43d88d26a9e30d8aadc002c9e77f1400c683dd34efe3778d43d27250dde50"}, "nodes": [{"id": "1c7b05e0-d82b-4851-a1ec-713093cdf489", "name": "<PERSON><PERSON> (IMAP)", "type": "n8n-nodes-base.emailReadImap", "position": [540, 660], "parameters": {"format": "resolved", "options": {"forceReconnect": 60, "customEmailConfig": "[\"UNSEEN\"]"}}, "typeVersion": 2}, {"id": "734424e6-d292-47d7-abb9-9630bdc00e35", "name": "Telegram", "type": "n8n-nodes-base.telegram", "position": [1140, 660], "parameters": {"text": "=📧 <b>You've got mail!</b>\n\nA new email has arrived from this address: <code>{{ $node[\"<PERSON><PERSON> (IMAP)\"].json[\"from\"][\"value\"][\"0\"][\"address\"] }}</code>\n\n🌐 A secert HTML page has been created for it, where you can preview the message by following the link below 👇", "chatId": "<Your Chat ID Here>", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "={{ $('Github Gist').item.json.files[\"email.html\"].filename }}", "additionalFields": {"url": "={{'http://emails.nskha.com/?iloven8n=nskha&id='+ $('Github Gist').item.json.id}}"}}]}}]}, "additionalFields": {"parse_mode": "HTML", "appendAttribution": true, "disable_web_page_preview": true}}, "typeVersion": 1.1}, {"id": "260c6ba6-1922-4bcb-bd5e-20b307ac638d", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.httpRequest", "notes": "Save HTML content", "position": [840, 660], "parameters": {"url": "https://api.github.com/gists", "method": "POST", "options": {"redirect": {"redirect": {}}}, "jsonBody": "={\n  \"description\": \"{{ $json.date }} - from {{ JSON.stringify($json.from.value[0].address).slice(1, -1) }} - to {{ JSON.stringify($json.to.value[0].address).slice(1, -1) }}\",\n  \"public\": false,\n  \"files\": {\n    \"email.html\": {\n      \"content\": \"{{ JSON.stringify($json.html).slice(1, -1) }}\"\n    }\n  }\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{"name": "Accept", "value": "application/vnd.github+json"}]}, "nodeCredentialType": "githubApi"}, "notesInFlow": true, "typeVersion": 4.1}, {"id": "0a77d236-e387-4458-a9cc-9ff7977ba4aa", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [460, 440], "parameters": {"color": 7, "width": 872, "height": 626.9128738621571, "content": "## Simple Conversion of Emails into HTML Webpages\nTo-do:\n* Configure your GitHub credentials through `Predefined Credential Type` => `GitHub API`.\n* Add your Telegram credentials by providing your `Chat ID`.\n* [**Optional**] You can host this [small project](https://github.com/Automations-Project/Emails/tree/main) on your own domain using GitHub Pages.\n\n ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ![image](https://cdn.statically.io/gh/Automations-Project/Emails/main/iloven8n.min.svg)\n\n\n\n\n\n\n\n ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ![image](https://cdn.statically.io/gh/Automations-Project/Emails/main/iloven8n%E2%80%8C.min.svg)"}, "typeVersion": 1}, {"id": "f69cf395-0050-44b3-a713-61f0cc5977ad", "name": "Wait", "type": "n8n-nodes-base.wait", "notes": "Delete within 3h", "position": [540, 900], "webhookId": "c5202512-f84e-44b4-b357-9ee2124bd507", "parameters": {"amount": 3}, "notesInFlow": true, "typeVersion": 1}, {"id": "c6067792-4fc2-4ced-bb04-6c5449a533ab", "name": "Telegram ‌", "type": "n8n-nodes-base.telegram", "position": [1140, 900], "parameters": {"chatId": "<Your Chat ID Here>", "messageId": "={{ $('Telegram').item.json.result.message_id }}", "operation": "deleteMessage"}, "typeVersion": 1.1}, {"id": "ebfe89fb-b0a3-4826-a72b-3fb8baa473c4", "name": "<PERSON><PERSON><PERSON> ‌", "type": "n8n-nodes-base.httpRequest", "notes": "Remove HTML content", "position": [840, 900], "parameters": {"url": "=https://api.github.com/gists/{{ $item(\"0\").$node[\"Github Gist\"].json[\"id\"] }}", "method": "DELETE", "options": {"redirect": {"redirect": {}}}, "sendHeaders": true, "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{"name": "Accept", "value": "application/vnd.github+json"}]}, "nodeCredentialType": "githubApi"}, "notesInFlow": true, "typeVersion": 4.1}], "pinData": {}, "connections": {"Wait": {"main": [[{"node": "<PERSON><PERSON><PERSON> ‌", "type": "main", "index": 0}]]}, "Telegram": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Github Gist": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "Github Gist ‌": {"main": [[{"node": "Telegram ‌", "type": "main", "index": 0}]]}, "Email Trigger (IMAP)": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}}}