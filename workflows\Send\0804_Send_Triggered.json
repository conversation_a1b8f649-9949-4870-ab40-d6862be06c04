{"meta": {"instanceId": "02e782574ebb30fbddb2c3fd832c946466d718819d25f6fe4b920124ff3fc2c1", "templateCredsSetupCompleted": true}, "nodes": [{"id": "bc58bd73-921a-445c-a905-6f1bbbc0e9c3", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [1160, 420], "webhookId": "cf762550-98e7-42f0-a0f3-cd9594331c00", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "308aea70-2831-4abd-90f6-d4cbf3901be4", "name": "n8n Research AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1440, 420], "parameters": {"options": {"systemMessage": "You are an assistant integrated with the n8n Multi-Channel Platform (MCP). Your primary role is to interact with the MCP to retrieve available tools and content based on user queries about n8n. When a user asks for information or assistance regarding n8n, first send a request to the MCP to fetch the relevant tools and content. Analyze the retrieved data to understand the available options, then create a tailored response that addresses their specific needs regarding n8n functionalities, documentation, forum posts, or example workflows. Ensure that your responses are clear, actionable, and directly related to the user's queries about n8n."}}, "typeVersion": 1.8}, {"id": "94cb78f5-3520-4432-b3c9-0524411113e9", "name": "n8n-assistant <PERSON><PERSON>", "type": "n8n-nodes-mcp.mcpClientTool", "position": [1500, 640], "parameters": {}, "credentials": {"mcpClientApi": {"id": "w1ZOoPXYGz6W2g1T", "name": "n8n-assistant"}}, "typeVersion": 1}, {"id": "78a87949-afda-4c52-ae9f-f8d343fb6567", "name": "n8n-assistant <PERSON><PERSON><PERSON>", "type": "n8n-nodes-mcp.mcpClientTool", "position": [1700, 640], "parameters": {"toolName": "={{$fromAI(\"tool\",\"Set this specific tool name\")}}", "operation": "executeTool", "toolParameters": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Tool_Parameters', ``, 'json') }}"}, "credentials": {"mcpClientApi": {"id": "w1ZOoPXYGz6W2g1T", "name": "n8n-assistant"}}, "typeVersion": 1}, {"id": "cc1619ec-6f49-45e6-8a7b-440da7ee5bc5", "name": "OpenAI Chat Model2", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1320, 640], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "q2i0xAiFxUOYOlJ0", "name": "OpenAI_BCP"}}, "typeVersion": 1.2}], "pinData": {}, "connections": {"OpenAI Chat Model2": {"ai_languageModel": [[{"node": "n8n Research AI Agent", "type": "ai_languageModel", "index": 0}]]}, "n8n-assistant Tool Lookup": {"ai_tool": [[{"node": "n8n Research AI Agent", "type": "ai_tool", "index": 0}]]}, "When chat message received": {"main": [[{"node": "n8n Research AI Agent", "type": "main", "index": 0}]]}, "n8n-assistant Execute Tool": {"ai_tool": [[{"node": "n8n Research AI Agent", "type": "ai_tool", "index": 0}]]}}}