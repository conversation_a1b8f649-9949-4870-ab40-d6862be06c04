{"nodes": [{"id": "d7ba34e4-5f98-4a32-abe7-1ed1a3d30410", "name": "n8n Form Trigger", "type": "n8n-nodes-base.formTrigger", "position": [-800, 840], "webhookId": "ee00f236-5dad-49db-8f29-71b7bce37894", "parameters": {"path": "0bf8840f-1cc4-46a9-86af-a3fa8da80608", "options": {}, "formTitle": "Contact us", "formFields": {"values": [{"fieldLabel": "What's your business email?"}]}, "formDescription": "We'll get back to you soon"}, "typeVersion": 2}, {"id": "4e91bf1d-ff5b-4a5c-805e-08c930e8dbe9", "name": "Check if the email is valid", "type": "n8n-nodes-base.if", "position": [-380, 840], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "54d84c8a-63ee-40ed-8fb2-301fff0194ba", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "valid"}]}}, "typeVersion": 2}, {"id": "d27ef50a-a80d-4f27-bd94-0c354f71fad1", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-800, 620], "parameters": {"color": 5, "width": 545.9804141018467, "height": 183.48964745383324, "content": "### 👨‍🎤 Setup\n1. Add you **Mad<PERSON><PERSON>u**, **<PERSON>**, and **Telegram** credentials \n2. Set the chat id in Telegram\n3. Click the Test Workflow button, enter your email and check your Telegram chat\n4. Activate the workflow and use the form trigger production URL to collect your leads in a smart way "}, "typeVersion": 1}, {"id": "ba0f2f9f-c95f-43a2-9f79-1f6b15f3cd5f", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-800, 980], "parameters": {"color": 7, "width": 162, "height": 139, "content": "👆 You can exchange this with any form you like (*e.g. Typeform, Google forms, Survey Monkey...*)"}, "typeVersion": 1}, {"id": "e74306a7-f430-4d00-80e1-3dd13ccd456a", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [180, 900], "parameters": {"color": 7, "width": 162, "height": 84, "content": "👆 Adjust the fit as you see necessary"}, "typeVersion": 1}, {"id": "a1c972d5-1455-48d8-9f6d-053147db5db2", "name": "Email is not valid, do nothing", "type": "n8n-nodes-base.noOp", "position": [-40, 980], "parameters": {}, "typeVersion": 1}, {"id": "84f0521d-38e8-4ef4-b590-5ef6d06ebfa2", "name": "Score lead with <PERSON><PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.httpRequest", "position": [-40, 740], "parameters": {"url": "=https://api.madkudu.com/v1/persons?email={{ $json.email }}", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "typeVersion": 4.1}, {"id": "f9553935-ca49-43d5-b3a5-d469edac5e83", "name": "Verify email with <PERSON>", "type": "n8n-nodes-base.hunter", "position": [-580, 840], "parameters": {"email": "={{ $json['What\\'s your business email?'] }}", "operation": "emailVerifier"}, "typeVersion": 1}, {"id": "4f3de033-8936-44f0-9a07-e21f98f6811b", "name": "Not interesting enough", "type": "n8n-nodes-base.noOp", "position": [520, 880], "parameters": {}, "typeVersion": 1}, {"id": "f6b6829a-7bc7-4145-8933-db1ce965c1c9", "name": "if customer fit score > 60", "type": "n8n-nodes-base.if", "position": [200, 740], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "c23d7b34-a4ae-421f-bd7a-6a3ebb05aafe", "operator": {"type": "number", "operation": "gt"}, "leftValue": "={{ $json.properties.customer_fit.score }}", "rightValue": 60}]}}, "typeVersion": 2}, {"id": "7e739bf6-1786-49b4-80d3-eeef406d7a6e", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [460, 460], "parameters": {"color": 7, "width": 162, "height": 84, "content": "👇🏽 Update the chat id to send to"}, "typeVersion": 1}, {"id": "fd0e1600-b1d9-4829-a86b-2cccc6a565f2", "name": "Telegram", "type": "n8n-nodes-base.telegram", "position": [500, 560], "parameters": {"text": "=⭐ New hot lead: {{ $json.email }}... \n\n{{ $json.properties.customer_fit.top_signals_formatted }}", "chatId": "1688282582", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "6", "name": "mymontsbot token"}}, "typeVersion": 1.1}], "pinData": {"n8n Form Trigger": [{"formMode": "test", "submittedAt": "2024-02-22T13:59:54.709Z", "What's your business email?": "<EMAIL>"}], "Score lead with MadKudu": [{"email": "<EMAIL>", "company": {"properties": {"name": "n8n", "domain": "n8n.io", "industry": "Internet Software & Services", "location": {"tags": ["high_gdp_per_capita"], "state": "Berlin", "country": "Germany", "state_code": "BE", "country_code": "DE"}, "number_of_employees": 60}}, "properties": {"domain": "n8n.io", "is_spam": false, "last_name": "<PERSON><PERSON><PERSON><PERSON>", "first_name": "Jan", "is_student": false, "customer_fit": {"score": 81, "segment": "good", "top_signals": [{"name": "Company raised $", "type": "positive", "value": "13500000"}, {"name": "Company is located in", "type": "positive", "value": "Germany"}, {"name": "Website traffic is medium large", "type": "positive", "value": null}, {"name": "Company industry is Software", "type": "positive", "value": null}, {"name": "Company is a Google shop", "type": "positive", "value": null}, {"name": "Company size", "type": "negative", "value": "60"}], "top_signals_formatted": "✔ Company raised $ is 13,500,000\n✔ Company is located in is Germany\n✔ Website traffic is medium large\n✔ Company industry is Software\n✔ Company is a Google shop\n✘ Company size is 60"}, "is_personal_email": false}, "object_type": "person"}], "Verify email with Hunter": [{"block": false, "email": "<EMAIL>", "score": 91, "regexp": true, "result": "deliverable", "status": "valid", "sources": [], "webmail": false, "gibberish": false, "accept_all": false, "disposable": false, "mx_records": true, "smtp_check": true, "smtp_server": true, "_deprecation_notice": "Using result is deprecated, use status instead"}]}, "connections": {"n8n Form Trigger": {"main": [[{"node": "Verify email with <PERSON>", "type": "main", "index": 0}]]}, "Score lead with MadKudu": {"main": [[{"node": "if customer fit score > 60", "type": "main", "index": 0}]]}, "Verify email with Hunter": {"main": [[{"node": "Check if the email is valid", "type": "main", "index": 0}]]}, "if customer fit score > 60": {"main": [[{"node": "Telegram", "type": "main", "index": 0}], [{"node": "Not interesting enough", "type": "main", "index": 0}]]}, "Check if the email is valid": {"main": [[{"node": "Score lead with <PERSON><PERSON><PERSON><PERSON>", "type": "main", "index": 0}], [{"node": "Email is not valid, do nothing", "type": "main", "index": 0}]]}}}