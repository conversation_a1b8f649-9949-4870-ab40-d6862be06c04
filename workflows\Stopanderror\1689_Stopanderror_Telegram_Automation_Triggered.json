{"id": "5Ycrm1MuK8htwd96", "meta": {"instanceId": "e5595d8cd58f3a24b5a8cf05dd852846c05423873db868a2b7d01a778210c45a", "templateCredsSetupCompleted": true}, "name": "Telegram RAG pdf", "tags": [], "nodes": [{"id": "9fbce801-8c42-43a4-bc70-d93042d68b2c", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "position": [-220, 240], "webhookId": "b178f034-9997-4832-9bb4-a43c3015506e", "parameters": {"updates": ["message"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "", "name": ""}}, "typeVersion": 1.1}, {"id": "1bfc1fbd-86b1-4a8a-9301-fe54497f5acd", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [720, 460], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "", "name": ""}}, "typeVersion": 1}, {"id": "d5ad7851-ed40-4b3a-b0d5-aeaf04362f1c", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [860, 460], "parameters": {"options": {}, "dataType": "binary"}, "typeVersion": 1}, {"id": "fed803d0-49a2-4b82-8f20-a02a10caa027", "name": "Recursive Character Text Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "position": [940, 680], "parameters": {"options": {}, "chunkSize": 3000, "chunkOverlap": 200}, "typeVersion": 1}, {"id": "ab60f36f-fada-4812-8dbd-441ad372cb80", "name": "Stop and Error", "type": "n8n-nodes-base.stopAndError", "position": [220, 840], "parameters": {"errorMessage": "An error occurred"}, "typeVersion": 1}, {"id": "c87f1db3-7cc9-4063-9895-4b4d68ea53a1", "name": "Question and Answer Chain", "type": "@n8n/n8n-nodes-langchain.chainRetrievalQa", "position": [-280, 500], "parameters": {"text": "={{ $json.message.text }}\nSearch the database with the retriever for information for the answer", "promptType": "define"}, "typeVersion": 1.3}, {"id": "c9bc4c80-8e57-48bc-a405-131ed7348c1d", "name": "Vector Store Retriever", "type": "@n8n/n8n-nodes-langchain.retrieverVectorStore", "position": [-240, 680], "parameters": {}, "typeVersion": 1}, {"id": "0217056f-2b71-4308-adf1-19dcd4d2cc11", "name": "Pinecone Vector Store1", "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "position": [-280, 860], "parameters": {"options": {}, "pineconeIndex": {"__rl": true, "mode": "list", "value": "telegram", "cachedResultName": "telegram"}}, "credentials": {"pineconeApi": {"id": "", "name": ""}}, "typeVersion": 1}, {"id": "693f9026-f47f-48dc-8e5d-e8b832a37235", "name": "<PERSON><PERSON><PERSON>", "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "position": [-380, 660], "parameters": {"model": "llama-3.1-70b-versatile", "options": {}}, "credentials": {"groqApi": {"id": "", "name": ""}}, "typeVersion": 1}, {"id": "c7acf014-138f-4be7-b569-c309bb10e50d", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [500, 73.04879287725316], "parameters": {"color": 7, "width": 1139.5159692915001, "height": 873.6068151028411, "content": "# Load data into database\nFetch file from **Telegram**, split it into chunks and insert into **Pinecone** index, a message from **Telegram** will be sent just to let the user know that the process finished"}, "typeVersion": 1}, {"id": "dd3b9d8b-5771-4a09-8c1b-794cb8737d5d", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-878.769, 400], "parameters": {"color": 7, "width": 1344.7918019808176, "height": 806.8716167324012, "content": "# Chat with Database\n\n1. **Receive** the incoming chat message.\n2. **Retrieve** relevant chunks from the _vector store_.\n3. **Pass** these chunks to the model.\n\nThe model will use the retrieved information to **formulate a precise response**.\n"}, "typeVersion": 1}, {"id": "9aaf575a-5e40-407c-951c-10b1d16e5d3c", "name": "Check If is a document", "type": "n8n-nodes-base.if", "position": [220, 240], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "8839993b-9fe7-4e1e-a1cc-fe5de6b0bb62", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.message.document }}", "rightValue": ""}]}}, "typeVersion": 2}, {"id": "c1edb6bf-ba95-4a5f-9626-add673274086", "name": "Change to application/pdf", "type": "n8n-nodes-base.code", "position": [700, 220], "parameters": {"jsCode": "// Função para modificar os metadados do arquivo binário\nfunction modifyBinaryMetadata(items) {\n for (const item of items) {\n if (item.binary && item.binary.data) {\n // Modifica o tipo MIME\n item.binary.data.mimeType = 'application/pdf';\n \n // Garante que o nome do arquivo termine com .pdf\n if (!item.binary.data.fileName.toLowerCase().endsWith('.pdf')) {\n item.binary.data.fileName += '.pdf';\n }\n \n // Atualiza o contentType no fileType (se existir)\n if (item.binary.data.fileType) {\n item.binary.data.fileType.contentType = 'application/pdf';\n }\n }\n }\n return items;\n}\n\n// Aplica a modificação e retorna os itens atualizados\nreturn modifyBinaryMetadata($input.all());"}, "typeVersion": 2}, {"id": "ea4d4e74-8954-47f0-a3a0-662d47ea2298", "name": "Telegram get File", "type": "n8n-nodes-base.telegram", "position": [520, 220], "parameters": {"fileId": "={{ $json.message.document.file_id }}", "resource": "file"}, "credentials": {"telegramApi": {"id": "", "name": ""}}, "typeVersion": 1.2}, {"id": "cf548bee-d5d5-4f1a-a059-932ea163e155", "name": "Embeddings", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [-100, 1080], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "", "name": ""}}, "typeVersion": 1}, {"id": "e3bd4759-80cc-42bb-ba53-f9e88e9ba916", "name": "Telegram Response", "type": "n8n-nodes-base.telegram", "onError": "continueErrorOutput", "position": [160, 560], "parameters": {"text": "={{ $json.response.text }}", "chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "", "name": ""}}, "typeVersion": 1.2}, {"id": "e478df48-9e6d-4a84-89be-beb569914ae3", "name": "Telegram Response about Database", "type": "n8n-nodes-base.telegram", "onError": "continueErrorOutput", "position": [1400, 220], "parameters": {"text": "={{ $json.metadata.pdf.totalPages }} pages saved on Pinecone", "chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "", "name": ""}}, "typeVersion": 1.2}, {"id": "5be7a321-1be6-4173-83de-3d569666718d", "name": "Stop and Error1", "type": "n8n-nodes-base.stopAndError", "position": [1400, 580], "parameters": {"errorMessage": "An error occurred."}, "typeVersion": 1}, {"id": "aae26861-f34d-4b59-bd99-3662fbd6676c", "name": "Pinecone Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "position": [880, 220], "parameters": {"mode": "insert", "options": {}, "pineconeIndex": {"__rl": true, "mode": "list", "value": "telegram", "cachedResultName": "telegram"}}, "credentials": {"pineconeApi": {"id": "", "name": ""}}, "typeVersion": 1}, {"id": "312fb807-4225-4630-ab32-aa12fe07c127", "name": "Limit to 1", "type": "n8n-nodes-base.limit", "position": [1220, 220], "parameters": {}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"timezone": "America/Sao_Paulo", "callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1", "saveManualExecutions": true}, "versionId": "03612d23-6630-4ec6-8738-1dae593c8d23", "connections": {"Embeddings": {"ai_embedding": [[{"node": "Pinecone Vector Store1", "type": "ai_embedding", "index": 0}]]}, "Limit to 1": {"main": [[{"node": "Telegram Response about Database", "type": "main", "index": 0}]]}, "Groq Chat Model": {"ai_languageModel": [[{"node": "Question and Answer Chain", "type": "ai_languageModel", "index": 0}]]}, "Telegram Trigger": {"main": [[{"node": "Check If is a document", "type": "main", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Pinecone Vector Store", "type": "ai_embedding", "index": 0}]]}, "Telegram Response": {"main": [[], [{"node": "Stop and Error", "type": "main", "index": 0}]]}, "Telegram get File": {"main": [[{"node": "Change to application/pdf", "type": "main", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Pinecone Vector Store", "type": "ai_document", "index": 0}]]}, "Pinecone Vector Store": {"main": [[{"node": "Limit to 1", "type": "main", "index": 0}]]}, "Check If is a document": {"main": [[{"node": "Telegram get File", "type": "main", "index": 0}], [{"node": "Question and Answer Chain", "type": "main", "index": 0}]]}, "Pinecone Vector Store1": {"ai_vectorStore": [[{"node": "Vector Store Retriever", "type": "ai_vectorStore", "index": 0}]]}, "Vector Store Retriever": {"ai_retriever": [[{"node": "Question and Answer Chain", "type": "ai_retriever", "index": 0}]]}, "Change to application/pdf": {"main": [[{"node": "Pinecone Vector Store", "type": "main", "index": 0}]]}, "Question and Answer Chain": {"main": [[{"node": "Telegram Response", "type": "main", "index": 0}]]}, "Telegram Response about Database": {"main": [[], [{"node": "Stop and Error1", "type": "main", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}}}