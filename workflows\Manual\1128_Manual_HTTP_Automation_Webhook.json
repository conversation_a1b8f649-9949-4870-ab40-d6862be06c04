{"id": "77", "name": "Extract information from an image of a receipt", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 340], "parameters": {}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.mindee", "position": [650, 340], "parameters": {}, "credentials": {"mindeeReceiptApi": "mindee"}, "typeVersion": 1}, {"name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [450, 340], "parameters": {"url": "https://miro.medium.com/max/1400/0*1T9GkAb93w5NSMsf", "options": {}, "responseFormat": "file"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"HTTP Request": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}}}