{"id": "fvgP264GysfRJXdr", "meta": {"instanceId": "a4bfc93e975ca233ac45ed7c9227d84cf5a2329310525917adaf3312e10d5462", "templateCredsSetupCompleted": true}, "name": "WordPress Contact Form (CF7) Responses and Classification", "tags": [], "nodes": [{"id": "789a4732-c652-45b5-9019-4aa082cd3a29", "name": "From Wordpress", "type": "n8n-nodes-base.webhook", "position": [-500, -120], "webhookId": "61858d25-af82-4cab-bb1b-68bea4989e15", "parameters": {"path": "61858d25-af82-4cab-bb1b-68bea4989e15", "options": {}, "httpMethod": "POST"}, "typeVersion": 2}, {"id": "958507a3-d9ac-430f-8d3d-701544e995a0", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [-240, -120], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c2fb7eb9-898e-47ab-ae67-b3d2dcd9ac0e", "name": "first_name", "type": "string", "value": "={{ $json.body.first_name }}"}, {"id": "8fb2afd5-aef8-4118-b760-ea21f0d3da95", "name": "last_name", "type": "string", "value": "={{ $json.body.last_name }}"}, {"id": "292727f0-f08c-48a1-ada6-9437a056662d", "name": "email", "type": "string", "value": "={{ $json.body.email }}"}, {"id": "394aec5f-2553-4210-8d37-b109772ac083", "name": "phone", "type": "string", "value": "={{ $json.body.phone }}"}, {"id": "db9a1211-3aa5-4421-9ede-5231a2017c8a", "name": "message", "type": "string", "value": "={{ $json.body.message }}"}]}}, "typeVersion": 3.4}, {"id": "00b0653e-34a1-434e-abb5-ed3d4995ae58", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [-40, 80], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash"}, "credentials": {"googlePalmApi": {"id": "0p34rXqIqy8WuoPg", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "55e80a8c-7c44-4324-bd79-024ab494177e", "name": "Message Classifier", "type": "@n8n/n8n-nodes-langchain.textClassifier", "position": [-20, -120], "parameters": {"options": {"fallback": "other", "systemPromptTemplate": "Please classify the text provided by the user into one of the following categories: {categories}, and use the provided formatting instructions below. Don't explain, and only output the json."}, "inputText": "={{ $json.message }}", "categories": {"categories": [{"category": "Product Info", "description": "Product information request"}, {"category": "Order Info", "description": "Request information on the order placed"}]}}, "typeVersion": 1}, {"id": "44c87bc8-7b6d-4d0b-8f34-b3ab1150e5e1", "name": "Google Gemini Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [520, 420], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "0p34rXqIqy8WuoPg", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "a66b653e-7df1-4f69-b37e-71a064d975be", "name": "Email draft - Other info", "type": "n8n-nodes-base.gmail", "position": [980, 220], "webhookId": "37831ee6-2a6e-4036-a567-ed839ab4276e", "parameters": {"message": "={{ $json.output.text }}\n\n---\n\nFirst Name: {{ $('Set Fields').item.json.first_name }}\nLast Name: {{ $('Set Fields').item.json.last_name }}\nEmail: {{ $('Set Fields').item.json.email }}\nPhone: {{ $('Set Fields').item.json.phone }}\n\nMessage:\n{{ $('Set Fields').item.json.message }}", "options": {"sendTo": "={{ $('Message Classifier').item.json.email }}"}, "subject": "={{ $json.output.subject }}", "resource": "draft"}, "credentials": {"gmailOAuth2": {"id": "nyuHvSX5HuqfMPlW", "name": "Gmail account (n3w.it)"}}, "typeVersion": 2.1}, {"id": "adc0baaa-f265-4912-9a2a-0f5c6f5a15db", "name": "Email writer (Others)", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [540, 220], "parameters": {"text": "=This is the message you received that you need to reply to:\n\nFirst Name: {{ $('Set Fields').item.json.first_name }}\nLast Name: {{ $('Set Fields').item.json.last_name }}\nEmail: {{ $('Set Fields').item.json.email }}\nPhone: {{ $('Set Fields').item.json.phone }}\n\nMessage:\n{{ $('Set Fields').item.json.message }}", "messages": {"messageValues": [{"message": "=# System Prompt for Form Response AI Agent\n\nYou are an AI assistant specialized in creating professional responses to customers who have filled out a form on the company website. Your purpose is to analyze the data received from the form and prepare a professional, courteous, and helpful draft response.\n\n## Basic Behavior\n- Carefully analyze all fields of the received form.\n- Generate a personalized response based on the information provided by the customer.\n- Maintain a professional yet friendly tone.\n- If crucial information is missing, insert a placeholder in square brackets [example: status of order #12345].\n- Adapt the response style according to the nature of the request (information request, complaint, technical support, etc.).\n\n## Response Structure\n1. **Header**: Appropriate greeting with the customer's name if available.\n2. **Acknowledgment**: Thank the customer for contacting the company.\n3. **Body**: Detailed response to the specific request, with all relevant details.\n4. **Action**: Clearly indicate what steps will be taken or what actions are required from the customer.\n5. **Closing**: Professional farewell formula with an offer of further assistance.\n6. **Signature**: Company name and relevant department.\n\n## Handling Specific Scenarios\n\n### Product/Service Information Requests\n- Provide precise details about requested products/services.\n- Include links to relevant pages on the website when appropriate.\n- Offer complementary options if relevant.\n\n### Order Status Requests\n- Confirm receipt of the request.\n- Insert order information if available or use placeholders [current status of order #12345].\n- Indicate expected delivery or completion times.\n\n### Complaints\n- Show empathy and understanding for the inconvenience.\n- Summarize the problem to demonstrate attentiveness.\n- Propose a concrete solution to the exposed problem.\n- Offer compensation when appropriate.\n\n### Technical Support\n- Confirm understanding of the technical issue.\n- Provide clear, step-by-step instructions.\n- Propose alternative solutions if necessary.\n- Offer a direct channel for continued assistance.\n\n## Personalization\n- Use the customer's name when available.\n- Reference previous interactions if mentioned.\n- Adapt technical language to the customer's apparent level of expertise.\n\n## Tone of Voice\n- Professional but not detached\n- Empathetic without being overly informal\n- Solution-oriented and action-focused\n- Clear and concise, avoiding ambiguity\n\nRemember: Each response must best represent the company image and leave the customer with a positive feeling of being heard and receiving competent assistance.\n\nToday is {{ $now }}"}]}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.6}, {"id": "a700a2c9-0d12-48fb-92f0-c060ae656010", "name": "Google Gemini Chat Model2", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [520, 40], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "0p34rXqIqy8WuoPg", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "********-2d04-4f25-8610-c57dd5a6d0b7", "name": "Google Gemini Chat Model3", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [540, -300], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "0p34rXqIqy8WuoPg", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "92a70faa-673d-4354-9146-4a533e096969", "name": "Email writer (Order info)", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [540, -120], "parameters": {"text": "=This is the message you received that you need to reply to:\n\nFirst Name: {{ $('Set Fields').item.json.first_name }}\nLast Name: {{ $('Set Fields').item.json.last_name }}\nEmail: {{ $('Set Fields').item.json.email }}\nPhone: {{ $('Set Fields').item.json.phone }}\n\nMessage:\n{{ $('Set Fields').item.json.message }}", "messages": {"messageValues": [{"message": "=# System Prompt for Form Response AI Agent\n\nYou are an AI assistant specialized in creating professional responses to customers who have filled out a form on the company website. Your purpose is to analyze the data received from the form and prepare a professional, courteous, and helpful draft response.\n\n## Basic Behavior\n- Carefully analyze all fields of the received form.\n- Generate a personalized response based on the information provided by the customer.\n- Maintain a professional yet friendly tone.\n- If crucial information is missing, insert a placeholder in square brackets [example: status of order #12345].\n- Adapt the response style according to the nature of the request (information request, complaint, technical support, etc.).\n\n## Response Structure\n1. **Header**: Appropriate greeting with the customer's name if available.\n2. **Acknowledgment**: Thank the customer for contacting the company.\n3. **Body**: Detailed response to the specific request, with all relevant details.\n4. **Action**: Clearly indicate what steps will be taken or what actions are required from the customer.\n5. **Closing**: Professional farewell formula with an offer of further assistance.\n6. **Signature**: Company name and relevant department.\n\n## Handling Specific Scenarios\n\n### Product/Service Information Requests\n- Provide precise details about requested products/services.\n- Include links to relevant pages on the website when appropriate.\n- Offer complementary options if relevant.\n\n### Order Status Requests\n- Confirm receipt of the request.\n- Insert order information if available or use placeholders [current status of order #12345].\n- Indicate expected delivery or completion times.\n\n### Complaints\n- Show empathy and understanding for the inconvenience.\n- Summarize the problem to demonstrate attentiveness.\n- Propose a concrete solution to the exposed problem.\n- Offer compensation when appropriate.\n\n### Technical Support\n- Confirm understanding of the technical issue.\n- Provide clear, step-by-step instructions.\n- Propose alternative solutions if necessary.\n- Offer a direct channel for continued assistance.\n\n## Personalization\n- Use the customer's name when available.\n- Reference previous interactions if mentioned.\n- Adapt technical language to the customer's apparent level of expertise.\n\n## Tone of Voice\n- Professional but not detached\n- Empathetic without being overly informal\n- Solution-oriented and action-focused\n- Clear and concise, avoiding ambiguity\n\nRemember: Each response must best represent the company image and leave the customer with a positive feeling of being heard and receiving competent assistance.\n\nToday is {{ $now }}"}]}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.6}, {"id": "1def4974-d267-4b6d-9256-412a6d02d6ba", "name": "Email writer (Product info)", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [540, -480], "parameters": {"text": "=This is the message you received that you need to reply to:\n\nFirst Name: {{ $('Set Fields').item.json.first_name }}\nLast Name: {{ $('Set Fields').item.json.last_name }}\nEmail: {{ $('Set Fields').item.json.email }}\nPhone: {{ $('Set Fields').item.json.phone }}\n\nMessage:\n{{ $('Set Fields').item.json.message }}", "messages": {"messageValues": [{"message": "=# System Prompt for Form Response AI Agent\n\nYou are an AI assistant specialized in creating professional responses to customers who have filled out a form on the company website. Your purpose is to analyze the data received from the form and prepare a professional, courteous, and helpful draft response.\n\n## Basic Behavior\n- Carefully analyze all fields of the received form.\n- Generate a personalized response based on the information provided by the customer.\n- Maintain a professional yet friendly tone.\n- If crucial information is missing, insert a placeholder in square brackets [example: status of order #12345].\n- Adapt the response style according to the nature of the request (information request, complaint, technical support, etc.).\n\n## Response Structure\n1. **Header**: Appropriate greeting with the customer's name if available.\n2. **Acknowledgment**: Thank the customer for contacting the company.\n3. **Body**: Detailed response to the specific request, with all relevant details.\n4. **Action**: Clearly indicate what steps will be taken or what actions are required from the customer.\n5. **Closing**: Professional farewell formula with an offer of further assistance.\n6. **Signature**: Company name and relevant department.\n\n## Handling Specific Scenarios\n\n### Product/Service Information Requests\n- Provide precise details about requested products/services.\n- Include links to relevant pages on the website when appropriate.\n- Offer complementary options if relevant.\n\n### Order Status Requests\n- Confirm receipt of the request.\n- Insert order information if available or use placeholders [current status of order #12345].\n- Indicate expected delivery or completion times.\n\n### Complaints\n- Show empathy and understanding for the inconvenience.\n- Summarize the problem to demonstrate attentiveness.\n- Propose a concrete solution to the exposed problem.\n- Offer compensation when appropriate.\n\n### Technical Support\n- Confirm understanding of the technical issue.\n- Provide clear, step-by-step instructions.\n- Propose alternative solutions if necessary.\n- Offer a direct channel for continued assistance.\n\n## Personalization\n- Use the customer's name when available.\n- Reference previous interactions if mentioned.\n- Adapt technical language to the customer's apparent level of expertise.\n\n## Tone of Voice\n- Professional but not detached\n- Empathetic without being overly informal\n- Solution-oriented and action-focused\n- Clear and concise, avoiding ambiguity\n\nRemember: Each response must best represent the company image and leave the customer with a positive feeling of being heard and receiving competent assistance.\n\nToday is {{ $now }}"}]}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.6}, {"id": "16be317a-c0ef-4603-913d-8bc5ad141d29", "name": "Email draft - Product info", "type": "n8n-nodes-base.gmail", "position": [980, -480], "webhookId": "37831ee6-2a6e-4036-a567-ed839ab4276e", "parameters": {"message": "={{ $json.output.text }}\n\n---\n\nFirst Name: {{ $('Set Fields').item.json.first_name }}\nLast Name: {{ $('Set Fields').item.json.last_name }}\nEmail: {{ $('Set Fields').item.json.email }}\nPhone: {{ $('Set Fields').item.json.phone }}\n\nMessage:\n{{ $('Set Fields').item.json.message }}", "options": {"sendTo": "={{ $('Message Classifier').item.json.email }}"}, "subject": "={{ $json.output.subject }}", "resource": "draft"}, "credentials": {"gmailOAuth2": {"id": "nyuHvSX5HuqfMPlW", "name": "Gmail account (n3w.it)"}}, "typeVersion": 2.1}, {"id": "9cc28565-e0e6-49ca-80f0-98f6eafe15e3", "name": "Email draft - Order info", "type": "n8n-nodes-base.gmail", "position": [980, -120], "webhookId": "37831ee6-2a6e-4036-a567-ed839ab4276e", "parameters": {"message": "={{ $json.output.text }}\n\n---\n\nFirst Name: {{ $('Set Fields').item.json.first_name }}\nLast Name: {{ $('Set Fields').item.json.last_name }}\nEmail: {{ $('Set Fields').item.json.email }}\nPhone: {{ $('Set Fields').item.json.phone }}\n\nMessage:\n{{ $('Set Fields').item.json.message }}", "options": {"sendTo": "={{ $('Message Classifier').item.json.email }}"}, "subject": "={{ $json.output.subject }}", "resource": "draft"}, "credentials": {"gmailOAuth2": {"id": "nyuHvSX5HuqfMPlW", "name": "Gmail account (n3w.it)"}}, "typeVersion": 2.1}, {"id": "098f2af1-8596-43e0-84cf-8271da85d63f", "name": "Save on Sheet (product)", "type": "n8n-nodes-base.googleSheets", "position": [1220, -480], "parameters": {"columns": {"value": {"DATE": "={{ $now.format('dd/MM/yyyy') }}", "DRAFT": "={{ $('Email writer (Product info)').item.json.output.text }}", "PHONE": "={{ $('Set Fields').item.json.phone }}", "EMAIL ": "={{ $('Set Fields').item.json.email }}", "MESSAGE": "={{ $('Set Fields').item.json.message }}", "LAST NAME": "={{ $('Set Fields').item.json.last_name }}", "CLASSIFIED": "Other request", "FIRST NAME": "={{ $('Set Fields').item.json.first_name }}"}, "schema": [{"id": "DATE", "type": "string", "display": true, "required": false, "displayName": "DATE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "FIRST NAME", "type": "string", "display": true, "required": false, "displayName": "FIRST NAME", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "LAST NAME", "type": "string", "display": true, "required": false, "displayName": "LAST NAME", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "EMAIL ", "type": "string", "display": true, "required": false, "displayName": "EMAIL ", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "PHONE", "type": "string", "display": true, "required": false, "displayName": "PHONE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MESSAGE", "type": "string", "display": true, "required": false, "displayName": "MESSAGE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CLASSIFIED", "type": "string", "display": true, "required": false, "displayName": "CLASSIFIED", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "DRAFT", "type": "string", "display": true, "removed": false, "required": false, "displayName": "DRAFT", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/18nEagLwTPmJUN9UAJ2rEqZKB9C6LLD18bUpuY5vdOw4/edit#gid=0", "cachedResultName": "Foglio1"}, "documentId": {"__rl": true, "mode": "list", "value": "18nEagLwTPmJUN9UAJ2rEqZKB9C6LLD18bUpuY5vdOw4", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/18nEagLwTPmJUN9UAJ2rEqZKB9C6LLD18bUpuY5vdOw4/edit?usp=drivesdk", "cachedResultName": "Contact Form 7"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "545afc6a-b6b1-445f-856a-cde7d8a0f2f6", "name": "Save on Sheet (order)", "type": "n8n-nodes-base.googleSheets", "position": [1220, -120], "parameters": {"columns": {"value": {"DATE": "={{ $now.format('dd/MM/yyyy') }}", "DRAFT": "={{ $('Email writer (Order info)').item.json.output.text }}", "PHONE": "={{ $('Set Fields').item.json.phone }}", "EMAIL ": "={{ $('Set Fields').item.json.email }}", "MESSAGE": "={{ $('Set Fields').item.json.message }}", "LAST NAME": "={{ $('Set Fields').item.json.last_name }}", "CLASSIFIED": "Other request", "FIRST NAME": "={{ $('Set Fields').item.json.first_name }}"}, "schema": [{"id": "DATE", "type": "string", "display": true, "required": false, "displayName": "DATE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "FIRST NAME", "type": "string", "display": true, "required": false, "displayName": "FIRST NAME", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "LAST NAME", "type": "string", "display": true, "required": false, "displayName": "LAST NAME", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "EMAIL ", "type": "string", "display": true, "required": false, "displayName": "EMAIL ", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "PHONE", "type": "string", "display": true, "required": false, "displayName": "PHONE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MESSAGE", "type": "string", "display": true, "required": false, "displayName": "MESSAGE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CLASSIFIED", "type": "string", "display": true, "required": false, "displayName": "CLASSIFIED", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "DRAFT", "type": "string", "display": true, "removed": false, "required": false, "displayName": "DRAFT", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/18nEagLwTPmJUN9UAJ2rEqZKB9C6LLD18bUpuY5vdOw4/edit#gid=0", "cachedResultName": "Foglio1"}, "documentId": {"__rl": true, "mode": "list", "value": "18nEagLwTPmJUN9UAJ2rEqZKB9C6LLD18bUpuY5vdOw4", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/18nEagLwTPmJUN9UAJ2rEqZKB9C6LLD18bUpuY5vdOw4/edit?usp=drivesdk", "cachedResultName": "Contact Form 7"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "6a8fb3a0-f31a-4177-98cb-de607e412772", "name": "Save on Sheet (other)", "type": "n8n-nodes-base.googleSheets", "position": [1220, 220], "parameters": {"columns": {"value": {"DATE": "={{ $now.format('dd/MM/yyyy') }}", "DRAFT": "={{ $('Email writer (Others)').item.json.output.text }}", "PHONE": "={{ $('Set Fields').item.json.phone }}", "EMAIL ": "={{ $('Set Fields').item.json.email }}", "MESSAGE": "={{ $('Set Fields').item.json.message }}", "LAST NAME": "={{ $('Set Fields').item.json.last_name }}", "CLASSIFIED": "Other request", "FIRST NAME": "={{ $('Set Fields').item.json.first_name }}"}, "schema": [{"id": "DATE", "type": "string", "display": true, "required": false, "displayName": "DATE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "FIRST NAME", "type": "string", "display": true, "required": false, "displayName": "FIRST NAME", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "LAST NAME", "type": "string", "display": true, "required": false, "displayName": "LAST NAME", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "EMAIL ", "type": "string", "display": true, "required": false, "displayName": "EMAIL ", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "PHONE", "type": "string", "display": true, "required": false, "displayName": "PHONE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MESSAGE", "type": "string", "display": true, "required": false, "displayName": "MESSAGE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CLASSIFIED", "type": "string", "display": true, "required": false, "displayName": "CLASSIFIED", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "DRAFT", "type": "string", "display": true, "removed": false, "required": false, "displayName": "DRAFT", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/18nEagLwTPmJUN9UAJ2rEqZKB9C6LLD18bUpuY5vdOw4/edit#gid=0", "cachedResultName": "Foglio1"}, "documentId": {"__rl": true, "mode": "list", "value": "18nEagLwTPmJUN9UAJ2rEqZKB9C6LLD18bUpuY5vdOw4", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/18nEagLwTPmJUN9UAJ2rEqZKB9C6LLD18bUpuY5vdOw4/edit?usp=drivesdk", "cachedResultName": "Contact Form 7"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "bec108fe-4a5c-4d75-b589-1d74245f4bb9", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-520, -360], "parameters": {"color": 6, "width": 800, "height": 140, "content": "## PRELIMINARY STEP\n- Download the Wordpress Plugin [CF7 to Webhook](https://wordpress.org/plugins/cf7-to-zapier/) and install it\n- Go to webhook tab on Wordpress and set the url of the n8n Webhook trigger\n- Set the POST request"}, "typeVersion": 1}, {"id": "e82d4a99-0839-4ef3-a89f-25c1fdcfd636", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-80, -180], "parameters": {"width": 360, "height": 200, "content": "Set your own classification categories"}, "typeVersion": 1}, {"id": "6e9967e0-21ca-4307-9fbc-e846e63e03ac", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [520, -600], "parameters": {"width": 320, "height": 1140, "content": "Create the draft of the reply email by dividing it into subject and text ready to be sent"}, "typeVersion": 1}, {"id": "7390f9c8-d243-4d15-b887-ab8c61c32948", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [940, -600], "parameters": {"width": 180, "height": 1140, "content": "send the draft to the correct department's company email"}, "typeVersion": 1}, {"id": "002e8fc9-92f2-4453-bc16-58068f372bf4", "name": "Subject and Text", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [720, -300], "parameters": {"schemaType": "manual", "inputSchema": "{\n\t\"type\": \"object\",\n\t\"properties\": {\n\t\t\"subject\": {\n\t\t\t\"type\": \"string\"\n\t\t},\n\t\t\"text\": {\n\t\t\t\"type\": \"string\"\n\t\t}\n\t}\n}"}, "typeVersion": 1.2}, {"id": "c5b3690c-47c2-4faa-90fd-84556658f4a5", "name": "Subject and Text 2", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [720, 20], "parameters": {"schemaType": "manual", "inputSchema": "{\n\t\"type\": \"object\",\n\t\"properties\": {\n\t\t\"subject\": {\n\t\t\t\"type\": \"string\"\n\t\t},\n\t\t\"text\": {\n\t\t\t\"type\": \"string\"\n\t\t}\n\t}\n}"}, "typeVersion": 1.2}, {"id": "d1498232-9ecd-4abd-ae84-f8936bcbb2b8", "name": "Subject and Text 3", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [740, 420], "parameters": {"schemaType": "manual", "inputSchema": "{\n\t\"type\": \"object\",\n\t\"properties\": {\n\t\t\"subject\": {\n\t\t\t\"type\": \"string\"\n\t\t},\n\t\t\"text\": {\n\t\t\t\"type\": \"string\"\n\t\t}\n\t}\n}"}, "typeVersion": 1.2}, {"id": "05fa4b16-4328-49f0-bb31-6b2a0f4b1df4", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-520, -680], "parameters": {"color": 3, "width": 800, "height": 280, "content": "# WordPress Contact Form (CF7) Responses and Classification \n\nThis workflow optimizes the management of inquiries received through a contact form on a WordPress site, automating the process of classification, response drafting, and data storage.\n\nThis workflow is particularly useful for businesses that receive multiple daily inquiries and want to improve their efficiency in managing customer communications. "}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "0c27484c-95ca-45c4-89cb-eada3117c9a3", "connections": {"Set Fields": {"main": [[{"node": "Message Classifier", "type": "main", "index": 0}]]}, "From Wordpress": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Subject and Text": {"ai_outputParser": [[{"node": "Email writer (Product info)", "type": "ai_outputParser", "index": 0}]]}, "Message Classifier": {"main": [[{"node": "Email writer (Product info)", "type": "main", "index": 0}], [{"node": "Email writer (Order info)", "type": "main", "index": 0}], [{"node": "Email writer (Others)", "type": "main", "index": 0}]]}, "Subject and Text 2": {"ai_outputParser": [[{"node": "Email writer (Order info)", "type": "ai_outputParser", "index": 0}]]}, "Subject and Text 3": {"ai_outputParser": [[{"node": "Email writer (Others)", "type": "ai_outputParser", "index": 0}]]}, "Email writer (Others)": {"main": [[{"node": "Email draft - Other info", "type": "main", "index": 0}]]}, "Email draft - Order info": {"main": [[{"node": "Save on Sheet (order)", "type": "main", "index": 0}]]}, "Email draft - Other info": {"main": [[{"node": "Save on Sheet (other)", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Message Classifier", "type": "ai_languageModel", "index": 0}]]}, "Email writer (Order info)": {"main": [[{"node": "Email draft - Order info", "type": "main", "index": 0}]]}, "Google Gemini Chat Model1": {"ai_languageModel": [[{"node": "Email writer (Others)", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini Chat Model2": {"ai_languageModel": [[{"node": "Email writer (Order info)", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini Chat Model3": {"ai_languageModel": [[{"node": "Email writer (Product info)", "type": "ai_languageModel", "index": 0}]]}, "Email draft - Product info": {"main": [[{"node": "Save on Sheet (product)", "type": "main", "index": 0}]]}, "Email writer (Product info)": {"main": [[{"node": "Email draft - Product info", "type": "main", "index": 0}]]}}}