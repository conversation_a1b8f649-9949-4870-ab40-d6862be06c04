{"id": "AlEVIPHR3dMJkYWt", "meta": {"instanceId": "58e59e36ad4158b4534237c364ed053a36843e3394fa02af59feb8df38262a79", "templateCredsSetupCompleted": true}, "name": "Monitor USDT ERC-20 Wallet Balance with Etherscan and Telegram Notifications", "tags": [], "nodes": [{"id": "35b62ca1-3603-4dcb-a3b5-77e1325c78f7", "name": "Balance Changed?", "type": "n8n-nodes-base.if", "position": [-40, 0], "parameters": {"conditions": {"boolean": [{"value1": "={{$json.balanceChanged}}", "value2": true}]}}, "typeVersion": 1}, {"id": "dfeef0d5-0bb2-40a1-ae75-51d7caeb9c3d", "name": "Balance Changed.", "type": "n8n-nodes-base.telegram", "position": [320, -140], "webhookId": "a8fa72ce-638b-4245-bcbc-d59948ae1144", "parameters": {"text": "=🚨 *USDT Balance Change!*\n\nWallet Address: {{ $json.walletAddress }}\n\n🔴 Previous Balance: {{parseFloat($json.previousBalance)/1e6}} USDT\n\n🟢 New Balance: {{parseFloat($json.currentBalance)/1e6}} USDT", "chatId": "< Your Telegram Chat ID >", "additionalFields": {"parse_mode": "<PERSON><PERSON>"}}, "credentials": {"telegramApi": {"id": "Ge3vEXak2MymWtcp", "name": "Telegram account"}}, "typeVersion": 1}, {"id": "ffebdb46-a6f0-4ed8-88ed-75ab427af969", "name": "Balance Not Changed.", "type": "n8n-nodes-base.telegram", "position": [320, 20], "webhookId": "a8fa72ce-638b-4245-bcbc-d59948ae1144", "parameters": {"text": "=Balance Unchanged. USDT balance remained stable.", "chatId": "< Your Telegram Chat ID >", "additionalFields": {"parse_mode": "<PERSON><PERSON>"}}, "typeVersion": 1}, {"id": "049ff717-ba10-4b7f-9f84-9eaaeee902ec", "name": "userData", "type": "n8n-nodes-base.set", "position": [-780, 0], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "4455d1e7-a489-4ab6-a526-4fc755db99d0", "name": "Your Wallet Address", "type": "string", "value": "< Wallet Address Paste Here >"}, {"id": "3d84deba-8093-42cf-833f-6891db778de7", "name": "Your Etherscan Api Key", "type": "string", "value": "< Etherscan Api Key Paste Here>"}, {"id": "971ea723-e3de-4cff-b4e7-5899f3d8fb00", "name": "USDT ERC-20 Token Address", "type": "string", "value": "******************************************"}]}}, "typeVersion": 3.4}, {"id": "0488f2dd-6b71-4be5-9ce8-cf0763b82990", "name": "balance<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.code", "position": [-280, 0], "parameters": {"jsCode": "const staticData = $getWorkflowStaticData('global');\n\nconst currentBalance = items[0].json.result;\n\nconst walletAddress = $('userData').first().json['Your Wallet Address']\n\nlet previousBalance = staticData.previousBalance;\n\nif (!previousBalance) {\n  staticData.previousBalance = currentBalance;\n  previousBalance = currentBalance;\n}\n\nconst balanceChanged = previousBalance !== currentBalance;\n\nstaticData.previousBalance = currentBalance;\n\nreturn [{json: {balanceChanged, previousBalance, currentBalance, walletAddress}}];"}, "typeVersion": 2}, {"id": "d7b23d5b-b4c5-4d9a-93f9-360ae0d539c7", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1040, -180], "parameters": {"color": 4, "width": 1540, "height": 400, "content": "## USDT ERC-20 Wallet Balance Tracker\n**This workflow** Is a basic concept of integrating your ERC-20 wallet with n8n nodes."}, "typeVersion": 1}, {"id": "7c8f0d69-6c37-469c-b466-89a467db9bbd", "name": "Check Balance Every 5 Minutes", "type": "n8n-nodes-base.cron", "position": [-1000, 0], "parameters": {"triggerTimes": {"item": [{"mode": "everyX", "unit": "minutes", "value": 5}]}}, "typeVersion": 1}, {"id": "ea603f03-25e0-4c80-90f2-eb5f09e71ad1", "name": "Fetch USDT Balance from Etherscan", "type": "n8n-nodes-base.httpRequest", "position": [-480, 0], "parameters": {"url": "https://api.etherscan.io/api", "options": {}, "sendQuery": true, "queryParameters": {"parameters": [{"name": "module", "value": "account"}, {"name": "action", "value": "tokenbalance"}, {"name": "address", "value": "={{ $json['Your Wallet Address'] }}"}, {"name": "tag", "value": "latest"}, {"name": "apikey", "value": "={{ $json['Your Etherscan Api Key'] }}"}, {"name": "contractaddress", "value": "={{ $json['USDT ERC-20 Token Address'] }}"}]}}, "typeVersion": 3}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "7ebf18de-7adf-40dd-99b4-ff8dd1e37f08", "connections": {"userData": {"main": [[{"node": "Fetch USDT Balance from Etherscan", "type": "main", "index": 0}]]}, "balanceChecker": {"main": [[{"node": "Balance Changed?", "type": "main", "index": 0}]]}, "Balance Changed.": {"main": [[]]}, "Balance Changed?": {"main": [[{"node": "Balance Changed.", "type": "main", "index": 0}], [{"node": "Balance Not Changed.", "type": "main", "index": 0}]]}, "Check Balance Every 5 Minutes": {"main": [[{"node": "userData", "type": "main", "index": 0}]]}, "Fetch USDT Balance from Etherscan": {"main": [[{"node": "balance<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}}}