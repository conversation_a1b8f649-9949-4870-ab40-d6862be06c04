{"id": "fGq0vUaD6JoqAbDa", "meta": {"instanceId": "e22925d3ea42bbdc2dfe92a253c7671a357e393fc99ccbe9178245197b7f4017"}, "name": "Query List of Sign-in IPs", "tags": [], "nodes": [{"id": "9d54681d-7f8b-4996-b734-96626c9134dc", "name": "GET Events - Login Successful", "type": "n8n-nodes-base.httpRequest", "onError": "continueRegularOutput", "position": [-20, 180], "parameters": {"url": "=https://us-central1-the-byway-248217.cloudfunctions.net/reportApi/api/v1/reports/events?eventType=login.success&start={{ $json['Last 24 Hours'] }}&timeSort=asc&size=10000&scroll=5s", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "api_key", "value": "={{ $json.API }}"}]}}, "typeVersion": 4.2}, {"id": "d789cb1c-0e8f-4389-85da-4a9b02d438d5", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-280, -220], "parameters": {"color": 4, "width": 480, "height": 620, "content": "## Query the SaaS Alerts API\n**SaaS Alerts API Reference Guide** [Link](https://app.swaggerhub.com/apis/SaaS_Alerts/functions)"}, "typeVersion": 1}, {"id": "67628c17-6520-45e9-b3af-a066b7759481", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [240, -140], "parameters": {"color": 5, "width": 680, "height": 540, "content": "## Data Processing and Deduplication"}, "typeVersion": 1}, {"id": "6a64676a-b9f7-4e16-aa5a-cc554902376b", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [940, 80], "parameters": {"width": 340, "height": 340, "content": "## SMTP2Go API\nAPI Documentation [Link](https://developers.smtp2go.com/docs/send-an-email)"}, "typeVersion": 1}, {"id": "afefbd9f-5442-478f-b3da-3baaf7803245", "name": "Send Email Upon Completion (SMTP2Go)", "type": "n8n-nodes-base.httpRequest", "position": [1040, 240], "parameters": {"url": "https://api.smtp2go.com/v3/email/send", "method": "POST", "options": {}, "jsonBody": "={\n  \"sender\": \"<EMAIL>\",\n  \"to\": [\n    \"{{ $('Set Date and Form Variables').first().json.Email }}\"\n  ],\n  \"attachments\": [\n    {\n      \"filename\": \"testfile.csv\",\n      \"fileblob\": \"{{ $json.data }}\",\n      \"mimetype\": \"application/csv\"\n    }\n  ],\n  \"subject\": \"Workflow Complete\",\n  \"text_body\": \"{{ $('Set Date and Form Variables').first().json.Name }}, attached is your IP information.\\n\\n\\n\\n\"\n}\n", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "accept", "value": "application/json"}]}}, "credentials": {"httpHeaderAuth": {"id": "v8R6pwWX9krYGBnw", "name": "SMTP2Go"}}, "executeOnce": true, "typeVersion": 4.2}, {"id": "04997dff-1f22-46b2-9c1c-2b6575ca7606", "name": "Remove Duplicate IPs", "type": "n8n-nodes-base.removeDuplicates", "onError": "continueRegularOutput", "position": [720, 20], "parameters": {"compare": "<PERSON><PERSON><PERSON>s", "options": {}, "fieldsToCompare": "ip"}, "typeVersion": 2, "alwaysOutputData": true}, {"id": "bb73047a-3f43-4cf5-bc6e-706f0c76f83c", "name": "Convert CSV to Base64", "type": "n8n-nodes-base.moveBinaryData", "position": [640, 240], "parameters": {"options": {"encoding": "base64"}, "setAllData": false}, "typeVersion": 1}, {"id": "6172642a-ec22-4fb3-9141-34afd7c7785e", "name": "Convert to CSV", "type": "n8n-nodes-base.convertToFile", "position": [400, 240], "parameters": {"options": {"headerRow": true}}, "typeVersion": 1.1}, {"id": "c748b13f-76c9-4552-83b9-53de9a0aa1e1", "name": "Filter IP Information", "type": "n8n-nodes-base.set", "position": [500, 20], "parameters": {"include": "selected", "options": {}, "assignments": {"assignments": []}, "includeFields": "customer.name, user.fullName,  ip, location.city, location.region, location.country, ", "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "23da49b8-b3c9-421a-a6e1-750762130314", "name": "Combine all Authentication Events", "type": "n8n-nodes-base.merge", "position": [300, 20], "parameters": {"numberInputs": 3}, "typeVersion": 3}, {"id": "cdb534b1-4f72-4466-8661-b4c72a60f69e", "name": "GET Events - OAuth Authentication", "type": "n8n-nodes-base.httpRequest", "onError": "continueRegularOutput", "position": [-20, 20], "parameters": {"url": "=https://us-central1-the-byway-248217.cloudfunctions.net/reportApi/api/v1/reports/events?eventType=oauth.granted.permission&start={{ $json['Last 24 Hours'] }}&timeSort=asc&size=10000&scroll=5s", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "api_key", "value": "={{ $json.API }}"}]}}, "typeVersion": 4.2}, {"id": "26ae2f0a-0349-4168-b6eb-48f94eb75348", "name": "GET Events - Office365 Shell WCSS", "type": "n8n-nodes-base.httpRequest", "onError": "continueRegularOutput", "position": [-20, -140], "parameters": {"url": "=https://us-central1-the-byway-248217.cloudfunctions.net/reportApi/api/v1/reports/events?eventType=ms.shell.login.success\n&start={{ $json['Last 24 Hours'] }}&timeSort=asc&size=10000&scroll=5s", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "api_key", "value": "={{ $json.API }}"}]}}, "typeVersion": 4.2}, {"id": "aae345d7-3250-4f4e-a464-164f830e8ecf", "name": "Set Date and Form Variables", "type": "n8n-nodes-base.set", "position": [-240, 20], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "a81fab55-cc84-4e34-96ea-66e3f13304d5", "name": "Last 24 Hours", "type": "string", "value": "={{ new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString() }}\n"}, {"id": "a6da98ec-6da1-422d-a6dc-3a1d4417c285", "name": "API", "type": "string", "value": "={{ $json['What is your API key?'] }}"}, {"id": "35cb5896-f667-4727-843e-ad2fb3446422", "name": "Name", "type": "string", "value": "={{ $json['What is your name?'] }}"}, {"id": "f630dcbb-1b15-49b4-a287-f940e1eddae8", "name": "Email", "type": "string", "value": "={{ $json['What is your e-mail?'] }}"}]}}, "typeVersion": 3.4}, {"id": "c8afb2af-4adf-4f90-afdd-31929b5d851d", "name": "Authentication Request Form", "type": "n8n-nodes-base.formTrigger", "position": [-540, 20], "webhookId": "63923cfa-a41e-4649-922f-b83e527e8d6b", "parameters": {"options": {"buttonLabel": "Process"}, "formTitle": "Request Sign-In CSV", "formFields": {"values": [{"fieldLabel": "What is your name?", "requiredField": true}, {"fieldType": "email", "fieldLabel": "What is your e-mail?", "requiredField": true}, {"fieldLabel": "What is your API key?", "requiredField": true}]}, "formDescription": "This will email you a list of all Organizations, \nAccounts, IPs and Locations.\n\nThis information is for the last 24 hours.\n\nPlease be patient, this can take some time.  \n\nYour list will be provided without duplicates."}, "typeVersion": 2.2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "a2cace5e-a6da-4953-901e-7f762c96ea77", "connections": {"Convert to CSV": {"main": [[{"node": "Convert CSV to Base64", "type": "main", "index": 0}]]}, "Remove Duplicate IPs": {"main": [[{"node": "Convert to CSV", "type": "main", "index": 0}]]}, "Convert CSV to Base64": {"main": [[{"node": "Send Email Upon Completion (SMTP2Go)", "type": "main", "index": 0}]]}, "Filter IP Information": {"main": [[{"node": "Remove Duplicate IPs", "type": "main", "index": 0}]]}, "Authentication Request Form": {"main": [[{"node": "Set Date and Form Variables", "type": "main", "index": 0}]]}, "Set Date and Form Variables": {"main": [[{"node": "GET Events - Login Successful", "type": "main", "index": 0}, {"node": "GET Events - OAuth Authentication", "type": "main", "index": 0}, {"node": "GET Events - Office365 Shell WCSS", "type": "main", "index": 0}]]}, "GET Events - Login Successful": {"main": [[{"node": "Combine all Authentication Events", "type": "main", "index": 2}]]}, "Combine all Authentication Events": {"main": [[{"node": "Filter IP Information", "type": "main", "index": 0}]]}, "GET Events - OAuth Authentication": {"main": [[{"node": "Combine all Authentication Events", "type": "main", "index": 1}]]}, "GET Events - Office365 Shell WCSS": {"main": [[{"node": "Combine all Authentication Events", "type": "main", "index": 0}]]}, "Send Email Upon Completion (SMTP2Go)": {"main": [[]]}}}