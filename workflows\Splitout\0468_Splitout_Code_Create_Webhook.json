{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "8eaf0925-1394-4771-bf43-281ad14fefb4", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [540, 880], "parameters": {"color": 4, "width": 301.3874093724939, "height": 371.765663140765, "content": "## Data check"}, "typeVersion": 1}, {"id": "ab31ac7c-6bd4-44f6-8c0c-8e41463a3983", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [560, 940], "parameters": {"color": 7, "width": 272.8190508599808, "height": 80, "content": "Checks that the data returned by OpenAI is correct"}, "typeVersion": 1}, {"id": "306ffdb5-d6b6-4e49-a26d-a256e32f7c67", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1940, 880], "parameters": {"color": 5, "width": 302, "height": 392, "content": "## Draft on WordPress"}, "typeVersion": 1}, {"id": "928da5f9-194c-461d-a5dd-7fd5c8563345", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [1960, 960], "parameters": {"color": 7, "width": 254.77269221373095, "height": 80, "content": "The article is posted as a draft on WordPress"}, "typeVersion": 1}, {"id": "271f6b4d-cf7c-49b8-9479-dd753d7c5199", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [2260, 880], "parameters": {"color": 3, "width": 678, "height": 389, "content": "## Featured image"}, "typeVersion": 1}, {"id": "efb047c4-b835-4706-af6a-b40c6cd76757", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [2280, 960], "parameters": {"color": 7, "width": 517.9195082760601, "height": 80, "content": "The image is generated with Dall-E, uploaded to WordPress, and then connected to the post as its featured image"}, "typeVersion": 1}, {"id": "4f78eb2c-a501-4774-b5ab-29d7aa83817d", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [160, 940], "parameters": {"color": 7, "width": 287.370178643191, "height": 80, "content": "Starting from the given keywords, generates the article title, subtitle, chapters, and image prompt"}, "typeVersion": 1}, {"id": "536d265a-1c0b-4262-adab-96edd5924530", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [140, 880], "parameters": {"color": 6, "width": 360, "height": 371, "content": "## Article structure"}, "typeVersion": 1}, {"id": "8b4b7cf6-4809-44e7-af9a-c72919981698", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-400, 880], "parameters": {"color": 7, "width": 239.97343293577688, "height": 370.512611879577, "content": "## User form"}, "typeVersion": 1}, {"id": "734b8ac2-2148-4d56-ab04-84f12991cf44", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [-380, 940], "parameters": {"color": 7, "width": 199.7721486302032, "height": 80, "content": "The user triggers the post creation"}, "typeVersion": 1}, {"id": "c54f91a7-ac3d-4029-a19f-3fa3794d581a", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [2960, 880], "parameters": {"color": 7, "width": 220, "height": 391, "content": "## User feedback"}, "typeVersion": 1}, {"id": "509e89e7-8916-4228-91ae-baae25a75be7", "name": "Sticky Note14", "type": "n8n-nodes-base.stickyNote", "position": [2980, 960], "parameters": {"color": 7, "width": 183.38125554060056, "height": 80, "content": "Final confirmation to the user"}, "typeVersion": 1}, {"id": "5c6e90c4-9714-43db-a82c-54fcbf43a26c", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [880, 1280], "parameters": {"color": 7, "width": 281.2716777103785, "height": 288.4116890365125, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nUser is notified to try again since some data is missing"}, "typeVersion": 1}, {"id": "54e10057-b300-475b-8280-cb761acc303a", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [140, 1280], "parameters": {"color": 7, "width": 340, "height": 275, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nWikipedia is used to write the article"}, "typeVersion": 1}, {"id": "5f5b9ad9-1da5-4321-b6b4-e1985ff257ca", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [-120, 880], "parameters": {"color": 2, "width": 226.71615243495023, "height": 370.512611879577, "content": "## Settings"}, "typeVersion": 1}, {"id": "6e298414-634f-45fb-b931-2abcae9c6db1", "name": "Sticky Note15", "type": "n8n-nodes-base.stickyNote", "position": [-100, 940], "parameters": {"color": 7, "width": 179.37633247508526, "height": 80, "content": "Set the URL of your WordPress here"}, "typeVersion": 1}, {"id": "94b09f31-31aa-4284-936e-c37fb3088acc", "name": "Sticky Note16", "type": "n8n-nodes-base.stickyNote", "position": [880, 880], "parameters": {"color": 2, "width": 225.47038972308582, "height": 370.512611879577, "content": "## Chapters split"}, "typeVersion": 1}, {"id": "633cba39-ec92-410c-b010-083048487b2b", "name": "Sticky Note17", "type": "n8n-nodes-base.stickyNote", "position": [900, 940], "parameters": {"color": 7, "width": 185.6051460344073, "height": 80, "content": "Splits out chapter contents from the previous node"}, "typeVersion": 1}, {"id": "4c9fd35f-d69d-4ced-a759-175336f43c8a", "name": "Sticky Note18", "type": "n8n-nodes-base.stickyNote", "position": [1160, 940], "parameters": {"color": 7, "width": 287.370178643191, "height": 80, "content": "Writes the text for each chapter"}, "typeVersion": 1}, {"id": "0d751d84-117a-4bf4-a9a7-d5ad1b557fec", "name": "Sticky Note19", "type": "n8n-nodes-base.stickyNote", "position": [1140, 880], "parameters": {"color": 6, "width": 333.40108076977657, "height": 370.512611879577, "content": "## Chapters text"}, "typeVersion": 1}, {"id": "ce36a500-136b-4082-988c-5f9b6dd6d971", "name": "Sticky Note21", "type": "n8n-nodes-base.stickyNote", "position": [1500, 880], "parameters": {"color": 4, "width": 420.4253447940705, "height": 514.2177254645992, "content": "## Content preparation"}, "typeVersion": 1}, {"id": "9da83dc4-9e99-42a4-88a1-27ef87df6d09", "name": "Sticky Note22", "type": "n8n-nodes-base.stickyNote", "position": [1520, 960], "parameters": {"color": 7, "width": 368.1523541074699, "height": 80, "content": "Merges the content and prepare it before sending it to WordPress"}, "typeVersion": 1}, {"id": "8591f4cb-edc4-4582-96b4-bcb2214a27a7", "name": "On form submission", "type": "n8n-nodes-base.formTrigger", "position": [-340, 1080], "webhookId": "080f8376-cc82-49cc-8dd0-6db36bb887ab", "parameters": {"options": {"path": "create-wordpress-post"}, "formTitle": "Create a WordPress post with AI", "formFields": {"values": [{"fieldLabel": "Keywords (comma-separated)", "requiredField": true}, {"fieldType": "dropdown", "fieldLabel": "Number of chapters", "fieldOptions": {"values": [{"option": "1"}, {"option": "2"}, {"option": "3"}, {"option": "4"}, {"option": "5"}, {"option": "6"}, {"option": "7"}, {"option": "8"}, {"option": "9"}, {"option": "10"}]}, "requiredField": true}, {"fieldType": "number", "fieldLabel": "Max words count", "requiredField": true}]}, "responseMode": "lastNode", "formDescription": "Fill this form with the required information to create a draft post on WordPress"}, "typeVersion": 2.2}, {"id": "59619ea3-ac29-4188-9050-a6711c3f0921", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [940, 1080], "parameters": {"options": {}, "fieldToSplitOut": "message.content.chapters"}, "typeVersion": 1}, {"id": "728b7e4c-7b4a-46d9-ac86-98c23bda6c98", "name": "OpenAI", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1180, 1080], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "messages": {"values": [{"content": "=Write a chapter for the article: {{ $('Create post title and structure').item.json.message.content.title }}, {{ $('Create post title and structure').item.json.message.content.subtitle  }}, that talks about {{ $('Settings').item.json[\"keywords\"] }}\n\nThis is the prompt for the chapter titled {{ $json.title }}: {{ $json.prompt }}.\n\nGuidelines:\n- Just return the plain text for each chapter (no JSON structure).\n- Don't use markdown for formatting.\n- Use HTML for formatting, but limited to bold, italic and lists.\n- Don't add internal titles or headings.\n- The length of each chapther should be around {{ Math.round(($('Settings').item.json.words - 120)/ $('Settings').item.json.chapters) }} words long\n- Go deep in the topic you treat, don't just throw some superficial info\n{{ $itemIndex > 0 ? \"- The previous chapter talks about \" + $input.all()[$itemIndex-1].json.title : \"\" }}\n{{ $itemIndex > 0 ? \"- The promt for the previous chapter is \" + $input.all()[$itemIndex-1].json.prompt : \"\" }}\n{{ $itemIndex < $input.all().length ? \"- The following chapter will talk about \" + $input.all()[$itemIndex+1].json.title: \"\" }}\n{{ $itemIndex < $input.all().length ? \"- The prompt for the following chapter is \" + $input.all()[$itemIndex+1].json.prompt : \"\" }}\n- Consider the previous and following chapters what writing the text for this chapter. The text must be coherent with the previous and following chapters.\n- This chapter should not repeat the concepts already exposed in the previous chapter.\n- This chapter is a part of a larger article so don't include an introduction or conclusions. This chapter should merge with the rest of the article.\n"}]}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "85007abe-b4b1-4263-a7e6-62f0a2ebd7c3", "name": "Settings", "type": "n8n-nodes-base.set", "position": [-60, 1080], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c07386d7-9f51-4052-a62d-500e9aff9336", "name": "wordpress_url", "type": "string", "value": "https://you-wordpress-url-here.com/"}, {"id": "2bbdd88a-3d66-4407-9b77-32af63f44e11", "name": "keywords", "type": "string", "value": "={{ $json['Keywords (comma-separated)'] }}"}, {"id": "4a199e44-1033-446a-a019-e2e1a694009e", "name": "chapters", "type": "string", "value": "={{ $json['Number of chapters'] }}"}, {"id": "312d2e97-d1b6-46d9-b2ae-35f7234b5404", "name": "words", "type": "string", "value": "={{ $json['Max words count'] }}"}]}}, "typeVersion": 3.4}, {"id": "d87b4460-1bf1-4c7f-8f5a-51993c1b7cd0", "name": "Create post title and structure", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [180, 1080], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "messages": {"values": [{"content": "=Write the title, the subtitle, the chapters details, the introduction, the conclusions, and an image prompt for a SEO-friendly article about these topics:\n{{ $json.keywords }}.\n\nInstructions:\n- Place the article title in a JSON field called `title`\n- Place the subtitle in a JSON field called `subtitle`\n- Place the introduction in a JSON field called `introduction`\n- In the introduction introduce the topic that is then explored in depth in the rest of the text\n- The introduction should be around 60 words\n- Place the conclusions in a JSON field called `conclusions`\n- The conclusions should be around 60 words\n- Use the conclusions to sum all said in the article and offer a conclusion to the reader\n- The image prompt will be used to produce a photographic cover image for the article and should depict the topics discussed in the article\n- Place the image prompt in a JSON field called `imagePrompt`\n- There should be {{ $json.chapters.toString() }} chapters.\n- For each chapter provide a title and an exaustive prompt that will be used to write the chapter text.\n- Place the chapters in an array field called `chapters`\n- For each chapter provide the fields `title` and `prompt`\n- The chapters should follow a logical flow and not repeat the same concepts.\n- The chapters should be one related to the other and not isolated blocks of text. The text should be fluent and folow a linear logic.\n- Don't start the chapters with \"Chapter 1\", \"Chapter 2\", \"Chapter 3\"... just write the title of the chapter\n- For the title and the capthers' titles don't use colons (`:`)\n- For the text, use HTML for formatting, but limited to bold, italic and lists.\n- Don't use markdown for formatting.\n- Always search on Wikipedia for useful information or verify the accuracy of what you write.\n- Never mention it if you don't find information on Wikipedia or the web\n- Go deep in the topic you treat, don't just throw some superficial info"}]}, "jsonOutput": true}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "57f5811a-e82e-4dd3-8d53-0559b2716dac", "name": "Wikipedia", "type": "@n8n/n8n-nodes-langchain.toolWikipedia", "position": [280, 1360], "parameters": {}, "typeVersion": 1}, {"id": "1987ab2c-4b6e-451d-a831-0817004be72b", "name": "Check data consistency", "type": "n8n-nodes-base.if", "position": [620, 1080], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "9523cb70-8467-4e65-9ecf-65cb91c29cb7", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.message.content.title }}", "rightValue": ""}, {"id": "d754869d-10fe-4348-807f-2e1bc82a7b41", "operator": {"type": "array", "operation": "lengthGt", "rightType": "number"}, "leftValue": "={{ $json.message.content.chapters }}", "rightValue": 0}, {"id": "79a60fc1-66f8-4cfc-a61b-de528dfb7978", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.message.content.subtitle }}", "rightValue": ""}, {"id": "c0c44d88-1c3d-44ba-9030-6e8fa9f2860f", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.message.content.introduction }}", "rightValue": ""}, {"id": "338cd7e0-d2b8-40f4-838d-3aaf618268d2", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.message.content.conclusions }}", "rightValue": ""}, {"id": "76eb9ba1-7675-403c-9287-ac1319791ffe", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.message.content.imagePrompt }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "6b22ae14-80c9-48ac-9a03-9266bc3a9aa4", "name": "Form", "type": "n8n-nodes-base.form", "position": [940, 1340], "webhookId": "691e1010-7083-46be-9e6e-4e77fb853a9a", "parameters": {"operation": "completion", "respondWith": "showText", "responseText": "There was a problem creating the article, please refresh the form and try again!"}, "typeVersion": 1}, {"id": "5a22467a-6835-4a62-951f-e8cd43bef3af", "name": "Merge chapters title and text", "type": "n8n-nodes-base.merge", "position": [1580, 1200], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3}, {"id": "f4609858-7f65-4f2d-b222-f03ee207f166", "name": "Final article text", "type": "n8n-nodes-base.code", "position": [1760, 1080], "parameters": {"jsCode": "let article = \"\";\n\n// Introduction\narticle += $('Create post title and structure').first().json.message.content.introduction;\narticle += \"<br><br>\";\n\nfor (const item of $input.all()) {\n  article += \"<strong>\" + item.json.title + \"</strong>\";\n  article += \"<br><br>\";\n  article += item.json.message.content;\n  article += \"<br><br>\";\n}\n\n// Conclusions\narticle += \"<strong>Conclusions</strong>\";\narticle += \"<br><br>\";\narticle += $('Create post title and structure').first().json.message.content.conclusions;\n\n\nreturn [\n  {\n    \"article\": article\n  }\n];"}, "typeVersion": 2}, {"id": "e21a5519-315b-442e-97d0-8ee745138652", "name": "Post on Wordpress", "type": "n8n-nodes-base.wordpress", "position": [2040, 1080], "parameters": {"title": "={{ $('Create post title and structure').first().json.message.content.title }}", "additionalFields": {"status": "draft", "content": "={{ $json.article }}"}}, "credentials": {"wordpressApi": {"id": "YMW8mGrekjfxKJUe", "name": "Wordpress account"}}, "typeVersion": 1}, {"id": "7d7de1e3-cff2-41f2-a8ae-123441d9b18c", "name": "Generate featured image", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [2340, 1080], "parameters": {"prompt": "=Generate a photographic image to be used as the cover image for the article titled: {{ $('Create post title and structure').first().json.message.content.title }}. This is the prompt for the image: {{ $('Create post title and structure').first().json.message.content.imagePrompt }}, photography, realistic, sigma 85mm f/1.4", "options": {"size": "1792x1024", "style": "natural", "quality": "hd"}, "resource": "image"}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "edbd0658-0edf-43e8-8428-3fea52639d62", "name": "Upload media", "type": "n8n-nodes-base.httpRequest", "position": [2560, 1080], "parameters": {"url": "https://wp-demo.mondo.surf/wp-json/wp/v2/media", "method": "POST", "options": {}, "sendBody": true, "contentType": "binaryData", "sendHeaders": true, "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{"name": "Content-Disposition", "value": "attachment; filename=\"example.jpg\""}]}, "inputDataFieldName": "data", "nodeCredentialType": "wordpressApi"}, "credentials": {"wordpressApi": {"id": "YMW8mGrekjfxKJUe", "name": "Wordpress account"}}, "typeVersion": 4.2}, {"id": "e2cd720b-03e6-4cee-8901-a6a06e4bd1ec", "name": "Set image ID for the post", "type": "n8n-nodes-base.httpRequest", "position": [2780, 1080], "parameters": {"url": "=https://wp-demo.mondo.surf/wp-json/wp/v2/posts/{{ $('Post on Wordpress').first().json.id }}", "method": "POST", "options": {}, "sendQuery": true, "queryParameters": {"parameters": [{"name": "featured_media", "value": "={{ $json.id }}"}]}}, "typeVersion": 4.2}, {"id": "daddda39-db6a-4266-b823-8ae9080ca1a8", "name": "Form1", "type": "n8n-nodes-base.form", "position": [3020, 1080], "webhookId": "a9bf2986-4c9d-4d89-b5bf-ef6e93130b60", "parameters": {"options": {}, "operation": "completion", "completionTitle": "Created Successfully!", "completionMessage": "=The article {{ $json.title.rendered }} was correctly created as a draft on WordPress!"}, "typeVersion": 1}], "pinData": {}, "connections": {"OpenAI": {"main": [[{"node": "Merge chapters title and text", "type": "main", "index": 0}]]}, "Settings": {"main": [[{"node": "Create post title and structure", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}, {"node": "Merge chapters title and text", "type": "main", "index": 1}]]}, "Wikipedia": {"ai_tool": [[{"node": "Create post title and structure", "type": "ai_tool", "index": 0}]]}, "Upload media": {"main": [[{"node": "Set image ID for the post", "type": "main", "index": 0}]]}, "Post on Wordpress": {"main": [[{"node": "Generate featured image", "type": "main", "index": 0}]]}, "Final article text": {"main": [[{"node": "Post on Wordpress", "type": "main", "index": 0}]]}, "On form submission": {"main": [[{"node": "Settings", "type": "main", "index": 0}]]}, "Check data consistency": {"main": [[{"node": "Split Out", "type": "main", "index": 0}], [{"node": "Form", "type": "main", "index": 0}]]}, "Generate featured image": {"main": [[{"node": "Upload media", "type": "main", "index": 0}]]}, "Set image ID for the post": {"main": [[{"node": "Form1", "type": "main", "index": 0}]]}, "Merge chapters title and text": {"main": [[{"node": "Final article text", "type": "main", "index": 0}]]}, "Create post title and structure": {"main": [[{"node": "Check data consistency", "type": "main", "index": 0}]]}}}