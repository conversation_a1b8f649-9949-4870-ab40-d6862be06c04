{"id": "105", "name": "Create a new member, update the information of the member, create a note and a post for the member in Orbit", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "Orbit", "type": "n8n-nodes-base.orbit", "position": [450, 300], "parameters": {"operation": "upsert", "identityUi": {"identityValue": {"source": "github", "searchBy": "username", "username": ""}}, "workspaceId": "425", "additionalFields": {}}, "credentials": {"orbitApi": "orbit-review"}, "typeVersion": 1}, {"name": "Orbit1", "type": "n8n-nodes-base.orbit", "position": [650, 300], "parameters": {"memberId": "={{$node[\"Orbit\"].json[\"id\"]}}", "operation": "update", "workspaceId": "={{$node[\"Orbit\"].parameter[\"workspaceId\"]}}", "updateFields": {"tagsToAdd": ""}}, "credentials": {"orbitApi": "orbit-review"}, "typeVersion": 1}, {"name": "Orbit2", "type": "n8n-nodes-base.orbit", "position": [850, 300], "parameters": {"note": "", "memberId": "={{$node[\"Orbit\"].json[\"id\"]}}", "resource": "note", "workspaceId": "={{$node[\"Orbit\"].parameter[\"workspaceId\"]}}"}, "credentials": {"orbitApi": "orbit-review"}, "typeVersion": 1}, {"name": "Orbit3", "type": "n8n-nodes-base.orbit", "position": [1050, 300], "parameters": {"url": "https://medium.com/n8n-io/sending-sms-the-low-code-way-with-airtable-twilio-programmable-sms-and-n8n-90dbde74223e", "memberId": "={{$node[\"Orbit\"].json[\"id\"]}}", "resource": "post", "workspaceId": "={{$node[\"Orbit\"].parameter[\"workspaceId\"]}}", "additionalFields": {}}, "credentials": {"orbitApi": "orbit-review"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Orbit": {"main": [[{"node": "Orbit1", "type": "main", "index": 0}]]}, "Orbit1": {"main": [[{"node": "Orbit2", "type": "main", "index": 0}]]}, "Orbit2": {"main": [[{"node": "Orbit3", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Orbit", "type": "main", "index": 0}]]}}}