{"meta": {"instanceId": "378c072a34d9e63949fd9cf26b8d28ff276a486e303f0d8963f23e1d74169c1b"}, "nodes": [{"id": "3035a456-e783-4ac3-a6b7-1925a81672c1", "name": "Execute Workflow", "type": "n8n-nodes-base.executeWorkflow", "onError": "continueRegularOutput", "position": [1980, 440], "parameters": {"options": {}, "workflowId": "4cnnwIeC9Sr5ngGZ"}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "c2d4d0f3-5f84-41de-9a06-4cd5a19e3337", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [860, 440], "parameters": {"rule": {"interval": [{"field": "seconds", "secondsInterval": 5}]}}, "typeVersion": 1.2}, {"id": "deb51138-4f68-4e8e-8118-d29bd4b79cd6", "name": "Get Status", "type": "n8n-nodes-base.redis", "position": [1080, 440], "parameters": {"key": "=workflowStatus_{{ $workflow.id }}", "options": {}, "operation": "get", "propertyName": "=workflowStatus"}, "credentials": {"redis": {"id": "Hvn2Vf7bGjmFgDr0", "name": "Redis account"}}, "typeVersion": 1}, {"id": "73d4e23e-7860-4ac1-8f90-781817e2c98b", "name": "Set Running", "type": "n8n-nodes-base.redis", "position": [1760, 440], "parameters": {"key": "=workflowStatus_{{ $workflow.id }}", "value": "running", "operation": "set"}, "credentials": {"redis": {"id": "Hvn2Vf7bGjmFgDr0", "name": "Redis account"}}, "typeVersion": 1}, {"id": "c7bc785f-dbb0-48be-98ef-d0f940be7749", "name": "Set Idle", "type": "n8n-nodes-base.redis", "position": [2200, 440], "parameters": {"key": "=workflowStatus_{{ $workflow.id }}", "value": "idle", "operation": "set"}, "credentials": {"redis": {"id": "Hvn2Vf7bGjmFgDr0", "name": "Redis account"}}, "typeVersion": 1}, {"id": "f65c374e-f189-4d43-9b45-53776a74cbf2", "name": "Continue if Idle", "type": "n8n-nodes-base.filter", "position": [1540, 360], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "0c6308f0-1c96-41a9-b821-97031454d555", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.workflowStatus }}", "rightValue": "idle"}]}}, "typeVersion": 2}, {"id": "45956f6b-52bf-48d2-8c68-0aa1fa338f8f", "name": "Redis Key exists", "type": "n8n-nodes-base.if", "position": [1300, 440], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a844597a-21f9-4869-9abb-4e4b1530931a", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.workflowStatus }}", "rightValue": ""}]}}, "typeVersion": 2}, {"id": "29896363-cb31-4940-9cef-a993b931484d", "name": "No Operation", "type": "n8n-nodes-base.noOp", "position": [1540, 520], "parameters": {}, "typeVersion": 1}, {"id": "7a8b0ceb-0c9c-4aa5-9cbb-68a7aee3641f", "name": "When clicking \"Test workflow\"", "type": "n8n-nodes-base.manualTrigger", "disabled": true, "position": [860, 740], "parameters": {}, "typeVersion": 1}, {"id": "d9ab7f18-fe96-4514-a576-49e50575185f", "name": "Reset to Idle", "type": "n8n-nodes-base.redis", "disabled": true, "position": [1080, 740], "parameters": {"key": "=workflowStatus_{{ $workflow.id }}", "value": "idle", "operation": "set"}, "credentials": {"redis": {"id": "Hvn2Vf7bGjmFgDr0", "name": "Redis account"}}, "typeVersion": 1}, {"id": "043e00ca-d191-4b54-b0ec-c14e87a5facb", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [811, 614], "parameters": {"color": 5, "width": 433, "height": 300, "content": "## Troubleshooting\nUnplanned server outage? Need to reset the flag? Disable the schedule trigger, activate these nodes and run the **Reset to Idle** node manually."}, "typeVersion": 1}, {"id": "dc045338-4e41-41f3-b197-704e7560c54a", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1696, 320], "parameters": {"color": 7, "width": 222, "height": 281, "content": "This updates the flag, indicating, that the workflow is currently running"}, "typeVersion": 1}, {"id": "5b8dae2f-c2cf-4e16-b4a2-bc97bb64b9e0", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [810.*************, 320], "parameters": {"width": 205.**************, "height": 280, "content": "## Set Interval\nDefine how frequently the main workflow should run."}, "typeVersion": 1}, {"id": "d55419d3-84e4-4b73-ae4c-a719d94f9bae", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1248, 286], "parameters": {"color": 7, "width": 445, "height": 382, "content": "If the flag stored in Redis already exists and indicates, that the worklow is still running, another execution will be prevented. In that case this workflow ends here."}, "typeVersion": 1}, {"id": "55ab702d-0feb-4d13-a42c-f9aed6d4389d", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1920, 320], "parameters": {"width": 218, "height": 281, "content": "## Set Workflow ID\nSet the ID of the main workflow which should be executed\n"}, "typeVersion": 1}, {"id": "7d469990-d3d7-41a1-9a59-7fcf76472342", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1020, 320], "parameters": {"color": 7, "width": 222, "height": 281, "content": "This checks for a dynamic flag (containing the workflow ID) which represents if the workflow is currently running."}, "typeVersion": 1}, {"id": "0d96a5e4-40a2-4abf-96be-7e17c187bc3d", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [2140, 320], "parameters": {"color": 7, "width": 222, "height": 281, "content": "This updates the flag, indicating, that the workflow is currently idle"}, "typeVersion": 1}], "pinData": {}, "connections": {"Get Status": {"main": [[{"node": "Redis Key exists", "type": "main", "index": 0}]]}, "Set Running": {"main": [[{"node": "Execute Workflow", "type": "main", "index": 0}]]}, "No Operation": {"main": [[{"node": "Set Running", "type": "main", "index": 0}]]}, "Continue if Idle": {"main": [[{"node": "Set Running", "type": "main", "index": 0}]]}, "Execute Workflow": {"main": [[{"node": "Set Idle", "type": "main", "index": 0}]]}, "Redis Key exists": {"main": [[{"node": "Continue if Idle", "type": "main", "index": 0}], [{"node": "No Operation", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Get Status", "type": "main", "index": 0}]]}, "When clicking \"Test workflow\"": {"main": [[{"node": "Reset to Idle", "type": "main", "index": 0}]]}}}