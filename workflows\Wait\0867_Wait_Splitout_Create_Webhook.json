{"meta": {"instanceId": "1eadd5bc7c3d70c587c28f782511fd898c6bf6d97963d92e836019d2039d1c79"}, "nodes": [{"id": "e936b195-744d-4c0b-a1ee-d9123190c0cd", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [-520, -160], "parameters": {"color": 4, "width": 1280, "height": 460, "content": "=======================================\n            WORKFLOW ASSISTANCE\n=======================================\n\nScrape Glassdoor Job Listings For Prospecting with Bright Data and LLMS\n\nFor any questions or support, please contact:\n    <EMAIL>\n\nExplore more tips and tutorials here:\n   - YouTube: https://www.youtube.com/@YaronBeen/videos\n   - LinkedIn: https://www.linkedin.com/in/yaronbeen/\n=======================================\nBright Data Docs: https://docs.brightdata.com/introduction\n\n\n*Important*\nMake Sure To Add Your API Keys to the HTTTP REQUESTS NODES (BRIGHT DATA API), GOOGLE RELATED NODES AND LLM NODE\n"}, "typeVersion": 1}, {"id": "60db8b95-e1c8-464d-a214-599e963db599", "name": "Snapshot Progress", "type": "n8n-nodes-base.httpRequest", "position": [2080, 260], "parameters": {"url": "=https://api.brightdata.com/datasets/v3/progress/{{ $('HTTP Request- Post API call to Bright Data').item.json.snapshot_id }}", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer <YOUR_BRIGHT_DATA_API_KEY>"}]}}, "typeVersion": 4.2}, {"id": "d7b8a05a-0545-4c7e-ba4a-077325d7061c", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [3140, 40], "parameters": {"width": 195, "height": 646, "content": "In this workflow, I use Google Sheets to store the results. \n\nYou can use my template to get started faster:\n\n1. [Click on this link to get the template](https://docs.google.com/spreadsheets/d/1ZYRk83hNIQCyQNaKpchdnbTiapVxE4aG6ZFIQlwEoWM/edit?usp=sharing)\n2. Make a copy of the Sheets\n3. Add the URL to this node \n\n\n"}, "typeVersion": 1}, {"id": "92c5471e-8980-4328-ae88-2f5798d9e010", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [780, -160], "parameters": {"width": 480, "height": 880, "content": "🔍 Glassdoor Jobs Scraper – Parameter Guide\nUse this object to query Glassdoor jobs.\nEach field filters results appropriately.\n\n\n{\n  \"location\": \"{{ $json.Location }}\",\n  \"keyword\": \"{{ $json.Keyword }}\",\n  \"country\": \"{{ $json.Country }}\"\n}\n🧾 Field Explanations & Valid Options\n🗺️ location\nCity or region for the jobs.\n✅ Example: \"Berlin\", \"New York\"\n\n🧠 keyword\nJob title or keyword to match.\n✅ Example: \"Data Scientist\", \"Marketing Manager\"\n\n🌍 country\nISO two‑letter country code.\nUse two letters like US, FR.\n\n✅ Full Example\n{\n  \"location\": \"Berlin\",\n  \"keyword\": \"Data Scientist\",\n  \"country\": \"DE\"\n}"}, "typeVersion": 1}, {"id": "9c33f4ab-e235-4118-91e6-be8f9b02a7ce", "name": "On form submission - Discover Jobs", "type": "n8n-nodes-base.formTrigger", "position": [1160, 480], "webhookId": "8d0269c7-d1fc-45a1-a411-19634a1e0b82", "parameters": {"options": {}, "formTitle": "Linkedin High Intent Prospects And Job Post Hunt", "formFields": {"values": [{"fieldLabel": "Job Location", "placeholder": "example: new york", "requiredField": true}, {"fieldLabel": "Keyword", "placeholder": "example: <PERSON><PERSON>, AI architect", "requiredField": true}, {"fieldLabel": "Country (2 letters)", "placeholder": "example: US,UK,IL", "requiredField": true}]}, "formDescription": "This form lets you customize your job search / prospecting by choosing:\n\nLocation (city or region)\n\nJob title or keywords\n\nCountry code\n"}, "typeVersion": 2.2}, {"id": "64644514-8b80-4b6d-ad03-3c1e3910bcbc", "name": "HTTP Request- Post API call to Bright Data", "type": "n8n-nodes-base.httpRequest", "position": [1500, 520], "parameters": {"url": "https://api.brightdata.com/datasets/v3/trigger", "method": "POST", "options": {}, "jsonBody": "=[\n  {\n    \"location\": \"{{ $json['Job Location'] }}\",\n    \"keyword\": \"{{ $json.Keyword }}\",\n    \"country\": \"{{ $json['Country (2 letters)'] }}\"\n  }\n] ", "sendBody": true, "sendQuery": true, "sendHeaders": true, "specifyBody": "json", "queryParameters": {"parameters": [{"name": "dataset_id", "value": "gd_lpfbbndm1xnopbrcr0"}, {"name": "include_errors", "value": "true"}, {"name": "type", "value": "discover_new"}, {"name": "discover_by", "value": "keyword"}, {"name": "uncompressed_webhook", "value": "true"}, {"name": "type", "value": "discover_new"}, {"name": "discover_by", "value": "=keyword"}]}, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer <YOUR_BRIGHT_DATA_API_KEY>"}]}}, "typeVersion": 4.2}, {"id": "a3ecdf16-4e39-43d6-8409-5dec26fc2b37", "name": "Wait - Polling Bright Data", "type": "n8n-nodes-base.wait", "position": [1840, 260], "webhookId": "8005a2b3-2195-479e-badb-d90e4240e699", "parameters": {"unit": "minutes"}, "executeOnce": false, "typeVersion": 1.1}, {"id": "27e70649-72e6-4241-9568-a11c8a7de93d", "name": "If - Checking status of Snapshot - if data is ready or not", "type": "n8n-nodes-base.if", "position": [2280, 260], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "7932282b-71bb-4bbb-ab73-4978e554de7e", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "running"}]}}, "typeVersion": 2.2}, {"id": "8e264a0c-b326-4bec-af4e-433cd1ed77c2", "name": "HTTP Request - Getting data from Bright Data", "type": "n8n-nodes-base.httpRequest", "position": [2560, 280], "parameters": {"url": "=https://api.brightdata.com/datasets/v3/snapshot/{{ $('HTTP Request- Post API call to Bright Data').item.json.snapshot_id }}", "options": {}, "sendQuery": true, "sendHeaders": true, "queryParameters": {"parameters": [{"name": "format", "value": "json"}]}, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer <YOUR_BRIGHT_DATA_API_KEY>"}]}}, "typeVersion": 4.2}, {"id": "c29a06db-00bf-4d6b-bbf1-c0716ba8f7ce", "name": "Google Sheets - Adding All Job Posts", "type": "n8n-nodes-base.googleSheets", "position": [3180, 340], "parameters": {"columns": {"value": {}, "schema": [{"id": "url", "type": "string", "display": true, "removed": false, "required": false, "displayName": "url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_url_overview", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_url_overview", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_rating", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_rating", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_title", "type": "string", "display": true, "removed": false, "required": false, "displayName": "job_title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_location", "type": "string", "display": true, "removed": false, "required": false, "displayName": "job_location", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_overview", "type": "string", "display": true, "removed": false, "required": false, "displayName": "job_overview", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_headquarters", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_headquarters", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_founded_year", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_founded_year", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_industry", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_industry", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_revenue", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_revenue", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_size", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_size", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_type", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_type", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_sector", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_sector", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "percentage_that_recommend_company_to_a friend", "type": "string", "display": true, "removed": false, "required": false, "displayName": "percentage_that_recommend_company_to_a friend", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "percentage_that_approve_of_ceo", "type": "string", "display": true, "removed": false, "required": false, "displayName": "percentage_that_approve_of_ceo", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_ceo", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_ceo", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_career_opportunities_rating", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_career_opportunities_rating", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_comp_and_benefits_rating", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_comp_and_benefits_rating", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_culture_and_values_rating", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_culture_and_values_rating", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_senior_management_rating", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_senior_management_rating", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_work/life_balance_rating", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_work/life_balance_rating", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "reviews_by_same_job_pros", "type": "string", "display": true, "removed": false, "required": false, "displayName": "reviews_by_same_job_pros", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "reviews_by_same_job_cons", "type": "string", "display": true, "removed": false, "required": false, "displayName": "reviews_by_same_job_cons", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_benefits_rating", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_benefits_rating", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_benefits_employer_summary", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_benefits_employer_summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "employee_benefit_reviews", "type": "string", "display": true, "removed": false, "required": false, "displayName": "employee_benefit_reviews", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_posting_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "job_posting_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_application_link", "type": "string", "display": true, "removed": false, "required": false, "displayName": "job_application_link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_website", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_website", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "pay_range_glassdoor_est", "type": "string", "display": true, "removed": false, "required": false, "displayName": "pay_range_glassdoor_est", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "pay_median_glassdoor", "type": "string", "display": true, "removed": false, "required": false, "displayName": "pay_median_glassdoor", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "pay_range_employer_est__DUPLICATE", "type": "string", "display": true, "removed": false, "required": false, "displayName": "pay_range_employer_est__DUPLICATE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "pay_median_employer", "type": "string", "display": true, "removed": false, "required": false, "displayName": "pay_median_employer", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "pay_range_currency", "type": "string", "display": true, "removed": false, "required": false, "displayName": "pay_range_currency", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "pay_type", "type": "string", "display": true, "removed": false, "required": false, "displayName": "pay_type", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "discovery_input", "type": "string", "display": true, "removed": false, "required": false, "displayName": "discovery_input", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": ["row_number"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {"handlingExtraData": "insertInNewColumn"}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1_jbr5zBllTy_pGbogfGSvyv1_0a77I8tU-Ai7BjTAw4/edit#gid=0", "cachedResultName": "input"}, "documentId": {"__rl": true, "mode": "list", "value": "1ZYRk83hNIQCyQNaKpchdnbTiapVxE4aG6ZFIQlwEoWM", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ZYRk83hNIQCyQNaKpchdnbTiapVxE4aG6ZFIQlwEoWM/edit?usp=drivesdk", "cachedResultName": "NoFluff-N8N-Sheet-Template- GlassdoorJob Scraping WIth Bright Data"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "4RJOMlGAcB9ZoYfm", "name": "Google Sheets account 2"}}, "typeVersion": 4.3, "alwaysOutputData": true}, {"id": "cffc101d-cf3f-46c8-a2e0-9989fa2ec0fe", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1400, 100], "parameters": {"width": 300, "height": 880, "content": "🧠 Bright Data Trigger – Customize Your Job Query\n\nThis HTTP Request sends a POST call to Bright Data to start a new dataset snapshot based on your filters.\n\n👋 If you don’t want to use the Form Trigger,\nyou can directly adjust the filters here in this node.\n"}, "typeVersion": 1}, {"id": "e74213c5-dafe-4c7a-a8fc-4014b94e434b", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1780, 120], "parameters": {"color": 4, "width": 940, "height": 360, "content": "Bright Data Getting Jobs\n"}, "typeVersion": 1}, {"id": "a01857bf-ef31-4972-940e-e3bac2c5fe40", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [3400, 320], "parameters": {"options": {}, "fieldToSplitOut": "company_name, job_title, description_text"}, "typeVersion": 1}, {"id": "855217f7-f790-413e-a767-68dd204fe0b4", "name": "Basic LLM Chain", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [3620, 320], "parameters": {"text": "=Read these fields from the job post:\n- Company: `{{ $json.company_name }}`\n- Title: `{{ $json.job_title }}`\n- Description: `{{ $('Google Sheets - Adding All Job Posts').item.json.job_overview }}`\n\n**Task**  \n1. If this role relates to marketing, content creation, or audience engagement, write **1–2 concise icebreaker sentences** that:\n   - Reference the company or job context  \n   - Explain how our Content Repurposing service can help\nMake sure to add the compnay name and job title.\n\nNote that we're not pitching based on the job title.\nWere pitching to the organization only if the job position they are looking for, can be fulfilled by our agency.\n\nExample:\nHey,\nI've noticed your'e looking for {{ $('Google Sheets - Adding All Job Posts').item.json.job_title }}.\n\nI have an offer that might be relevant to your team.\n\nThen transition to our offer of content repurpose\n2. Otherwise, reply with:  \n---JOB POST NOT RELEVANT---", "promptType": "define"}, "typeVersion": 1.6}, {"id": "c7b193d4-aec4-4438-8e0c-8bb12c50e629", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [3720, 540], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "MX2lQOZcGpmRvdVD", "name": "OpenAi account 2"}}, "typeVersion": 1.2}, {"id": "28b581d7-1245-45b2-af16-8eb945f2c553", "name": "Google Sheets - Update Pitches", "type": "n8n-nodes-base.googleSheets", "position": [3980, 320], "parameters": {"columns": {"value": {"Pitch": "={{ $json.text }}", "company_name": "={{ $('Split Out').item.json.company_name }}"}, "schema": [{"id": "url", "type": "string", "display": true, "removed": true, "required": false, "displayName": "url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_url_overview", "type": "string", "display": true, "removed": true, "required": false, "displayName": "company_url_overview", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_rating", "type": "string", "display": true, "removed": true, "required": false, "displayName": "company_rating", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_title", "type": "string", "display": true, "removed": true, "required": false, "displayName": "job_title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_location", "type": "string", "display": true, "removed": true, "required": false, "displayName": "job_location", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_overview", "type": "string", "display": true, "removed": true, "required": false, "displayName": "job_overview", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_headquarters", "type": "string", "display": true, "removed": true, "required": false, "displayName": "company_headquarters", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_founded_year", "type": "string", "display": true, "removed": true, "required": false, "displayName": "company_founded_year", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_industry", "type": "string", "display": true, "removed": true, "required": false, "displayName": "company_industry", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_revenue", "type": "string", "display": true, "removed": true, "required": false, "displayName": "company_revenue", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_size", "type": "string", "display": true, "removed": true, "required": false, "displayName": "company_size", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_type", "type": "string", "display": true, "removed": true, "required": false, "displayName": "company_type", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_sector", "type": "string", "display": true, "removed": true, "required": false, "displayName": "company_sector", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "percentage_that_recommend_company_to_a friend", "type": "string", "display": true, "removed": true, "required": false, "displayName": "percentage_that_recommend_company_to_a friend", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "percentage_that_approve_of_ceo", "type": "string", "display": true, "removed": true, "required": false, "displayName": "percentage_that_approve_of_ceo", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_ceo", "type": "string", "display": true, "removed": true, "required": false, "displayName": "company_ceo", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_career_opportunities_rating", "type": "string", "display": true, "removed": true, "required": false, "displayName": "company_career_opportunities_rating", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_comp_and_benefits_rating", "type": "string", "display": true, "removed": true, "required": false, "displayName": "company_comp_and_benefits_rating", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_culture_and_values_rating", "type": "string", "display": true, "removed": true, "required": false, "displayName": "company_culture_and_values_rating", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_senior_management_rating", "type": "string", "display": true, "removed": true, "required": false, "displayName": "company_senior_management_rating", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_work/life_balance_rating", "type": "string", "display": true, "removed": true, "required": false, "displayName": "company_work/life_balance_rating", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "reviews_by_same_job_pros", "type": "string", "display": true, "removed": true, "required": false, "displayName": "reviews_by_same_job_pros", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "reviews_by_same_job_cons", "type": "string", "display": true, "removed": true, "required": false, "displayName": "reviews_by_same_job_cons", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_benefits_rating", "type": "string", "display": true, "removed": true, "required": false, "displayName": "company_benefits_rating", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_benefits_employer_summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "company_benefits_employer_summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "employee_benefit_reviews", "type": "string", "display": true, "removed": true, "required": false, "displayName": "employee_benefit_reviews", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_posting_id", "type": "string", "display": true, "removed": true, "required": false, "displayName": "job_posting_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_id", "type": "string", "display": true, "removed": true, "required": false, "displayName": "company_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_application_link", "type": "string", "display": true, "removed": true, "required": false, "displayName": "job_application_link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_website", "type": "string", "display": true, "removed": true, "required": false, "displayName": "company_website", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "pay_range_glassdoor_est", "type": "string", "display": true, "removed": true, "required": false, "displayName": "pay_range_glassdoor_est", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "pay_median_glassdoor", "type": "string", "display": true, "removed": true, "required": false, "displayName": "pay_median_glassdoor", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "pay_range_employer_est__DUPLICATE", "type": "string", "display": true, "removed": true, "required": false, "displayName": "pay_range_employer_est__DUPLICATE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "pay_median_employer", "type": "string", "display": true, "removed": true, "required": false, "displayName": "pay_median_employer", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "pay_range_currency", "type": "string", "display": true, "removed": true, "required": false, "displayName": "pay_range_currency", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "pay_type", "type": "string", "display": true, "removed": true, "required": false, "displayName": "pay_type", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "discovery_input", "type": "string", "display": true, "removed": true, "required": false, "displayName": "discovery_input", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "timestamp", "type": "string", "display": true, "removed": true, "required": false, "displayName": "timestamp", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "input", "type": "string", "display": true, "removed": true, "required": false, "displayName": "input", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "error", "type": "string", "display": true, "removed": true, "required": false, "displayName": "error", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "error_code", "type": "string", "display": true, "removed": true, "required": false, "displayName": "error_code", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "pay_range_Employer_est", "type": "string", "display": true, "removed": true, "required": false, "displayName": "pay_range_Employer_est", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Pitch", "type": "string", "display": true, "required": false, "displayName": "Pitch", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["company_name"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ZYRk83hNIQCyQNaKpchdnbTiapVxE4aG6ZFIQlwEoWM/edit#gid=0", "cachedResultName": "input"}, "documentId": {"__rl": true, "mode": "list", "value": "1ZYRk83hNIQCyQNaKpchdnbTiapVxE4aG6ZFIQlwEoWM", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ZYRk83hNIQCyQNaKpchdnbTiapVxE4aG6ZFIQlwEoWM/edit?usp=drivesdk", "cachedResultName": "NoFluff-N8N-Sheet-Template- GlassdoorJob Scraping WIth Bright Data"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "4RJOMlGAcB9ZoYfm", "name": "Google Sheets account 2"}}, "typeVersion": 4.5}], "pinData": {}, "connections": {"Split Out": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "Basic LLM Chain": {"main": [[{"node": "Google Sheets - Update Pitches", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "Snapshot Progress": {"main": [[{"node": "If - Checking status of Snapshot - if data is ready or not", "type": "main", "index": 0}]]}, "Wait - Polling Bright Data": {"main": [[{"node": "Snapshot Progress", "type": "main", "index": 0}]]}, "On form submission - Discover Jobs": {"main": [[{"node": "HTTP Request- Post API call to Bright Data", "type": "main", "index": 0}]]}, "Google Sheets - Adding All Job Posts": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "HTTP Request- Post API call to Bright Data": {"main": [[{"node": "Wait - Polling Bright Data", "type": "main", "index": 0}]]}, "HTTP Request - Getting data from Bright Data": {"main": [[{"node": "Google Sheets - Adding All Job Posts", "type": "main", "index": 0}]]}, "If - Checking status of Snapshot - if data is ready or not": {"main": [[{"node": "Wait - Polling Bright Data", "type": "main", "index": 0}], [{"node": "HTTP Request - Getting data from Bright Data", "type": "main", "index": 0}]]}}}