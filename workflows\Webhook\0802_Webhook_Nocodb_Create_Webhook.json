{"meta": {"instanceId": "4e8285376decaea86c34202e3f6f0900c15ccf72a22e44fbb5cd9851bb3fd11f", "templateCredsSetupCompleted": true}, "nodes": [{"id": "971e1616-af67-4961-9ef4-4b8dd24e392c", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [-660, 240], "webhookId": "29a6482f-36ac-4c15-8792-450aa32cf5f4", "parameters": {"path": "29a6482f-36ac-4c15-8792-450aa32cf5f4", "options": {}, "httpMethod": ["POST", "GET"], "responseMode": "responseNode", "multipleMethods": true}, "typeVersion": 2}, {"id": "22d8dca1-a2fd-474e-a1cd-7f75be1c04a6", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-720, -220], "parameters": {"color": 6, "width": 620, "height": 920, "content": "## Dropbox\n\nDropbox call me each time a modification is done somewhere in my dropbox."}, "typeVersion": 1}, {"id": "0178e813-ae15-4729-933b-2799ec405863", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [240, -280], "parameters": {"color": 3, "width": 840, "height": 140, "content": "## Watch Files, 2 ways :\n1. We explore each file in a folder (new and old ones)\n2. We want to filter new files only"}, "typeVersion": 1}, {"id": "229b9a46-5d76-4cb7-b1ac-4b10a6427f66", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [240, -80], "parameters": {"width": 1040, "height": 100, "content": "### Way 1 - We call the subworklow for each file in the specified folder"}, "typeVersion": 1}, {"id": "43a4fa3c-e378-443b-81a1-349201a85056", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [240, 200], "parameters": {"color": 4, "width": 1240, "height": 100, "content": "### Way 2- We filter new/old files then we call the subworkflow only for new files"}, "typeVersion": 1}, {"id": "e9639a07-6672-46ee-a86e-025265e45069", "name": "Dropbox - List watched folder", "type": "n8n-nodes-base.dropbox", "position": [500, 280], "parameters": {"path": "={{ $json.folder_to_watch }}", "filters": {"include_deleted": false, "include_mounted_folders": false}, "resource": "folder", "operation": "list", "returnAll": true, "authentication": "oAuth2"}, "credentials": {"dropboxOAuth2Api": {"id": "GocmYXzmqQnUpojt", "name": "Dropbox BT"}}, "typeVersion": 1}, {"id": "61707418-fcbb-4b70-ab06-02b9b7060bfb", "name": "Switch File vs Folder", "type": "n8n-nodes-base.switch", "position": [700, 280], "parameters": {"rules": {"values": [{"outputKey": "file", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "f7fda7a2-1961-41aa-a332-f9a8be9b3bfa", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.type }}", "rightValue": "file"}]}, "renameOutput": true}, {"outputKey": "folder", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "d8bb9414-ae11-4b8f-ad0e-6bfe0e2f1071", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.type }}", "rightValue": "folder"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "2670d2d1-acfb-4d0b-9bde-12dbd3806976", "name": "Switch File vs Folder1", "type": "n8n-nodes-base.switch", "position": [780, 0], "parameters": {"rules": {"values": [{"outputKey": "file", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "f7fda7a2-1961-41aa-a332-f9a8be9b3bfa", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.type }}", "rightValue": "file"}]}, "renameOutput": true}, {"outputKey": "folder", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "d8bb9414-ae11-4b8f-ad0e-6bfe0e2f1071", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.type }}", "rightValue": "folder"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "8225e284-b193-4859-891e-2277b2bc004b", "name": "NocoDB - Get know files to exclude", "type": "n8n-nodes-base.nocoDb", "position": [600, 480], "parameters": {"table": "m0tqa79y2sv4g0j", "options": {"where": "=(folder_to_watch,eq,{{ $json.folder_to_watch }})"}, "operation": "getAll", "projectId": "p73a23pw65qwwr4", "returnAll": true, "authentication": "nocoDbApiToken"}, "credentials": {"nocoDbApiToken": {"id": "GW7a5UwWksviQWb7", "name": "NocoDB cloudron"}}, "typeVersion": 3}, {"id": "0b9cb74b-ce02-4ed2-a417-084529a79977", "name": "Merge - Keep only new items", "type": "n8n-nodes-base.merge", "position": [1000, 280], "parameters": {"mode": "combine", "options": {}, "advanced": true, "joinMode": "keepNonMatches", "mergeByFields": {"values": [{"field1": "id", "field2": "data.id"}]}, "outputDataFrom": "input1"}, "typeVersion": 3}, {"id": "c1f52687-7b6e-4c69-bcce-cc9bf053c62f", "name": "Just a quick answer to Dropbox - webhook validation", "type": "n8n-nodes-base.respondToWebhook", "position": [-400, 520], "parameters": {"options": {}, "respondWith": "text", "responseBody": "={{ $json.query.challenge }}"}, "typeVersion": 1.1}, {"id": "42c84256-cbce-46d6-ab5a-01b1ef0715b0", "name": "Respond to Dropbox in less than 10sec", "type": "n8n-nodes-base.respondToWebhook", "position": [-400, 0], "parameters": {"options": {}, "respondWith": "text", "responseBody": "={{ $json.query.challenge }}"}, "typeVersion": 1.1}, {"id": "bb45006e-f12e-4b9d-99fa-ec5003f1c141", "name": "Dropbox get files", "type": "n8n-nodes-base.dropbox", "position": [500, 0], "parameters": {"path": "={{ $json.folder_to_watch }}", "filters": {"include_deleted": false, "include_mounted_folders": false}, "resource": "folder", "operation": "list", "returnAll": true, "authentication": "oAuth2"}, "credentials": {"dropboxOAuth2Api": {"id": "GocmYXzmqQnUpojt", "name": "Dropbox BT"}}, "typeVersion": 1}, {"id": "b4f8c38c-c674-488d-8c9a-f68e42c702cf", "name": "NocoDB - Add this file in the table", "type": "n8n-nodes-base.nocoDb", "position": [1200, 280], "parameters": {"table": "m0tqa79y2sv4g0j", "fieldsUi": {"fieldValues": [{"fieldName": "folder_to_watch", "fieldValue": "={{ $('set_folder to watch B').item.json.folder_to_watch }}"}, {"fieldName": "data", "fieldValue": "={\n\"id\":\"{{ $json.id }}\",\n\"name\":\"{{ $json.name }}\",\n\"lastModifiedClient\": \"{{ $json.lastModifiedClient }}\",\n\"lastModifiedServer\": \"{{ $json.lastModifiedServer }}\",\n\"rev\": \"{{ $json.rev }}\",\n\"contentSize\": {{ $json.contentSize }},\n\"type\": \"{{ $json.type }}\",\n\"contentHash\": \"{{ $json.contentHash }}\",\n\"pathLower\": \"{{ $json.pathLower }}\",\n\"pathDisplay\": \"{{ $json.pathDisplay }}\",\n\"isDownloadable\": {{ $json.isDownloadable }}\n}"}, {"fieldName": "file_id", "fieldValue": "={{ $json.id }}"}]}, "operation": "create", "projectId": "p73a23pw65qwwr4", "authentication": "nocoDbApiToken"}, "credentials": {"nocoDbApiToken": {"id": "GW7a5UwWksviQWb7", "name": "NocoDB cloudron"}}, "typeVersion": 3}, {"id": "9d6d2d96-f16f-408d-bc35-0e17da4d4e6d", "name": "set_folder A", "type": "n8n-nodes-base.set", "position": [260, 0], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "8416f15a-a6a2-47d2-8c15-e1742b323a8f", "name": "folder_to_watch", "type": "string", "value": "/z_Apps/a_iphone/RecUp Memos/"}]}}, "typeVersion": 3.4}, {"id": "aaa1ef55-10ef-4d8f-ae71-d317930229bc", "name": "set_folder to watch B", "type": "n8n-nodes-base.set", "position": [280, 280], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "8416f15a-a6a2-47d2-8c15-e1742b323a8f", "name": "folder_to_watch", "type": "string", "value": "/z_Apps/auphonic/whisper"}]}}, "typeVersion": 3.4}, {"id": "770b7f54-e85e-4d62-ab2a-60c9a73411e3", "name": "Execute Workflow - what i want to do for this folder/file A", "type": "n8n-nodes-base.executeWorkflow", "position": [1040, -20], "parameters": {"mode": "each", "options": {"waitForSubWorkflow": false}, "workflowId": {"__rl": true, "mode": "list", "value": "PRxsqnbMoqY6avr6", "cachedResultName": "02 JE TRANSMETS > Dropbox RecUp Memos - transcription audio - 2025-01 v1"}, "workflowInputs": {"value": {}, "schema": [], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": true}}, "typeVersion": 1.2}, {"id": "d9230834-13b4-420f-9e9a-154176803a66", "name": "Execute Workflow - Something to do for new files", "type": "n8n-nodes-base.executeWorkflow", "position": [1400, 280], "parameters": {"options": {}, "workflowId": {"__rl": true, "mode": "list", "value": "NzETudhVA1JruLCl", "cachedResultName": "210 PODCAST > Dropbox txt - Pré rédige épisode avec transcription - 2025-03 v2"}, "workflowInputs": {"value": {}, "schema": [], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": true}}, "typeVersion": 1.2}, {"id": "68d02221-861c-41d6-afe3-3757b272746f", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-60, 120], "parameters": {"color": 2, "height": 80, "content": "I duplicate those processes for each folder i want to watch"}, "typeVersion": 1}, {"id": "6befd86e-9a7b-442e-abb2-e1ed535f5166", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [180, 460], "parameters": {"color": 2, "content": "I define in a \"variable\" the folder to watch to ease the next steps"}, "typeVersion": 1}], "pinData": {"set_folder A": [{"folder_to_watch": "/z_Apps/a_iphone/RecUp Memos/"}], "Dropbox get files": [{"id": "id:m50XjFjkXDsAAAAAAAVw5w", "name": "done", "type": "folder", "pathLower": "/z_apps/a_iphone/recup memos/done", "pathDisplay": "/z_Apps/a_iphone/RecUp Memos/done"}, {"id": "id:m50XjFjkXDsAAAAAAAVxNg", "name": "txt", "type": "folder", "pathLower": "/z_apps/a_iphone/recup memos/txt", "pathDisplay": "/z_Apps/a_iphone/RecUp Memos/txt"}, {"id": "id:m50XjFjkXDsAAAAAAAVxNw", "name": "notes_md", "type": "folder", "pathLower": "/z_apps/a_iphone/recup memos/notes_md", "pathDisplay": "/z_Apps/a_iphone/RecUp Memos/notes_md"}, {"id": "id:m50XjFjkXDsAAAAAAAVxTA", "name": "done vraiment", "type": "folder", "pathLower": "/z_apps/a_iphone/recup memos/done vraiment", "pathDisplay": "/z_Apps/a_iphone/RecUp Memos/done vraiment"}, {"id": "id:m50XjFjkXDsAAAAAAAV1Jg", "name": "trash", "type": "folder", "pathLower": "/z_apps/a_iphone/recup memos/trash", "pathDisplay": "/z_Apps/a_iphone/RecUp Memos/trash"}, {"id": "id:m50XjFjkXDsAAAAAAAV1uQ", "rev": "63104211ae04e0b4b15a2", "name": "test DV-2025-02-04-050515.mp3", "type": "file", "pathLower": "/z_apps/a_iphone/recup memos/test dv-2025-02-04-050515.mp3", "contentHash": "feb314cba10772588af3e56bdbc2a1efb693e7174794d660efed188b87ba9398", "contentSize": 1464900, "pathDisplay": "/z_Apps/a_iphone/RecUp Memos/test DV-2025-02-04-050515.mp3", "isDownloadable": true, "lastModifiedClient": "2025-02-04T04:05:17Z", "lastModifiedServer": "2025-03-23T15:27:45Z"}], "set_folder to watch B": [{"folder_to_watch": "/z_Apps/auphonic/whisper"}], "Dropbox - List watched folder": [{"id": "id:lXWFwy5R52AAAAAAAAAIeA", "rev": "631049fe37ae34f316ea3", "name": "test btXXX - stabilité émotionnelle-final-auphonic-final-auphonic copie.txt", "type": "file", "pathLower": "/z_apps/auphonic/whisper/test btxxx - stabilité émotionnelle-final-auphonic-final-auphonic copie.txt", "contentHash": "fda114fad56df672588bc1543c869863c81c7e6b843d05158cdc8d1573515587", "contentSize": 26671, "pathDisplay": "/z_Apps/auphonic/whisper/test btXXX - stabilité émotionnelle-final-auphonic-final-auphonic copie.txt", "isDownloadable": true, "lastModifiedClient": "2025-01-22T13:26:49Z", "lastModifiedServer": "2025-03-23T16:03:12Z"}, {"id": "id:lXWFwy5R52AAAAAAAAAIeQ", "rev": "63104b143779c4f316ea3", "name": "test-nospace-normal.txt", "type": "file", "pathLower": "/z_apps/auphonic/whisper/test-nospace-normal.txt", "contentHash": "fda114fad56df672588bc1543c869863c81c7e6b843d05158cdc8d1573515587", "contentSize": 26671, "pathDisplay": "/z_Apps/auphonic/whisper/test-nospace-normal.txt", "isDownloadable": true, "lastModifiedClient": "2025-01-22T13:26:49Z", "lastModifiedServer": "2025-03-23T16:08:03Z"}], "Respond to Dropbox in less than 10sec": [{"body": {"delta": {"users": [*********]}, "list_folder": {"accounts": ["dbid:AAA9MBwnssJK-R5bqnTuKBDTg7UMwOZb8mk"]}}, "query": {}, "params": {}, "headers": {"host": "n8n.app.businesstemple.co", "accept": "*/*", "x-real-ip": "*************", "connection": "close", "user-agent": "DropboxWebhooks/1.0", "content-type": "application/json", "content-length": "108", "accept-encoding": "gzip,deflate", "x-forwarded-for": "*************", "x-forwarded-ssl": "on", "x-forwarded-host": "n8n.app.businesstemple.co", "x-forwarded-port": "443", "x-forwarded-proto": "https", "x-dropbox-signature": "339bfefa3e30feaddaa59cd7a37df9de12ccd490d7aee9b1e055d32ae7d4acee"}, "webhookUrl": "https://n8n.app.businesstemple.co/webhook-test/dropboxbox-watch-files", "executionMode": "test"}]}, "connections": {"Webhook": {"main": [[{"node": "Respond to Dropbox in less than 10sec", "type": "main", "index": 0}], [{"node": "Just a quick answer to Dropbox - webhook validation", "type": "main", "index": 0}]]}, "set_folder A": {"main": [[{"node": "Dropbox get files", "type": "main", "index": 0}]]}, "Dropbox get files": {"main": [[{"node": "Switch File vs Folder1", "type": "main", "index": 0}]]}, "Switch File vs Folder": {"main": [[{"node": "Merge - Keep only new items", "type": "main", "index": 0}]]}, "set_folder to watch B": {"main": [[{"node": "Dropbox - List watched folder", "type": "main", "index": 0}, {"node": "NocoDB - Get know files to exclude", "type": "main", "index": 0}]]}, "Switch File vs Folder1": {"main": [[{"node": "Execute Workflow - what i want to do for this folder/file A", "type": "main", "index": 0}]]}, "Merge - Keep only new items": {"main": [[{"node": "NocoDB - Add this file in the table", "type": "main", "index": 0}]]}, "Dropbox - List watched folder": {"main": [[{"node": "Switch File vs Folder", "type": "main", "index": 0}]]}, "NocoDB - Get know files to exclude": {"main": [[{"node": "Merge - Keep only new items", "type": "main", "index": 1}]]}, "NocoDB - Add this file in the table": {"main": [[{"node": "Execute Workflow - Something to do for new files", "type": "main", "index": 0}]]}, "Respond to Dropbox in less than 10sec": {"main": [[{"node": "set_folder A", "type": "main", "index": 0}, {"node": "set_folder to watch B", "type": "main", "index": 0}]]}}}