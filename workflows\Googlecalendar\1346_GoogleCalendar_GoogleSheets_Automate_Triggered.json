{"id": "AvCMhDoSUAYXsrQX", "meta": {"instanceId": "14e4c77104722ab186539dfea5182e419aecc83d85963fe13f6de862c875ebfa"}, "name": "Automate Event Creation in Google Calendar from Google Sheets", "tags": [], "nodes": [{"id": "b973046b-ff52-464e-8d34-fe57c5b1df7d", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-240, 0], "parameters": {"color": 6, "width": 1200, "height": 280, "content": "# Automate Event Creation in Google Calendar from Google Sheets\n"}, "typeVersion": 1}, {"id": "e845b624-6c0a-4d31-aace-cc050f8613dc", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-240, 300], "parameters": {"color": 6, "width": 1200, "height": 280, "content": "## Description \nIn this workflow, we streamline the process of creating events in Google Calendar using event data stored in a Google Sheet through n8n automation. The workflow begins by retrieving the latest event entry from Google Sheets, ensuring that only the most recent event details are processed. Once the event data is fetched, a Function node is used to format the event date so that it aligns with Google Calendar's required format. This step ensures consistency and prevents any date-related errors.\n\nAfter formatting, the workflow sends the structured event details to Google Calendar, where the event is created with essential information such as the event title (summary), description, event date, and location. Additionally, the workflow allows customization by setting the event's status as either \"Busy\" or \"Available,\" helping attendees manage their schedules effectively. Furthermore, a background color can be assigned to the event to enhance visibility and categorization in the calendar.\n\nBy automating this process, the workflow eliminates the need for manual event creation, ensuring seamless synchronization between Google Sheets and Google Calendar. This approach improves efficiency, accuracy, and productivity, making event management effortless."}, "typeVersion": 1}, {"id": "60f2c8b8-a953-4fc1-8751-01d8b7924cb2", "name": "Event Date Formatter", "type": "n8n-nodes-base.code", "position": [320, 100], "parameters": {"jsCode": "// Get the last item from the input data\nconst lastEvent = items[items.length - 1].json;\n\n// Extract relevant fields\nconst eventName = lastEvent[\"Event Name\"];\nconst eventDescription = lastEvent[\"Event Description\"];\nconst currentYear = new Date().getFullYear(); \n// Get the current year\nconst location = lastEvent[\"Location\"];\n\n// Ensure the date includes the year\nconst formatDateWithYear = (dateStr) => {\n    return dateStr.includes(currentYear) ? dateStr : `${dateStr} ${currentYear}`;\n};\n\n// Format the start date\nconst startDateString = formatDateWithYear(lastEvent[\"Event Start Date\"]); // Example: \"11 March 2024\"\n\n// Convert to JavaScript Date object\nconst startDate = new Date(startDateString);\n\n// Convert to ISO format (YYYY-MM-DD)\nconst formattedStartDate = startDate.toISOString().split(\"T\")[0]; // Extract only the date\n\n// Return the last event's formatted data\nreturn [{\n    json: {\n        eventName,\n        eventDescription,\n        startDate: formattedStartDate,\n      location: location,\n    }\n}];\n"}, "typeVersion": 2}, {"id": "e27e0d10-71bb-4d01-ba92-5fb8c3195422", "name": "New Event Entry Listener", "type": "n8n-nodes-base.googleSheetsTrigger", "position": [-120, 100], "parameters": {"event": "rowAdded", "options": {"valueRender": "FORMULA"}, "pollTimes": {"item": [{"mode": "everyMinute"}, {}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1dKjIGmcnQgSEMVuWAAFVDaj_MCBFKBX8hCOk5OH2dK4/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1dKjIGmcnQgSEMVuWAAFVDaj_MCBFKBX8hCOk5OH2dK4", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1dKjIGmcnQgSEMVuWAAFVDaj_MCBFKBX8hCOk5OH2dK4/edit?usp=drivesdk", "cachedResultName": "N8n Event List"}}, "typeVersion": 1}, {"id": "04864602-bf6a-4def-9bc3-c5ab4b5c8336", "name": "Google Calendar Event Creator", "type": "n8n-nodes-base.googleCalendar", "position": [700, 100], "parameters": {"end": "={{ $json.startDate }}", "start": "={{ $json.startDate }}", "calendar": {"__rl": true, "mode": "list", "value": "", "cachedResultName": ""}, "additionalFields": {"color": "3", "allday": "yes", "summary": "={{ $json.eventName }}", "location": "={{ $json.location }}", "showMeAs": "transparent", "description": "={{ $json.eventDescription }}", "guestsCanInviteOthers": true}}, "typeVersion": 1.3}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "98bd043e-8dce-4eca-a22f-95ff61f07a1f", "connections": {"Event Date Formatter": {"main": [[{"node": "Google Calendar Event Creator", "type": "main", "index": 0}]]}, "New Event Entry Listener": {"main": [[{"node": "Event Date Formatter", "type": "main", "index": 0}]]}}}