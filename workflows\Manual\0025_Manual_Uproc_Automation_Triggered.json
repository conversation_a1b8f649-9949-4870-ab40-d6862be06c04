{"id": "104", "name": "location_by_ip", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [440, 510], "parameters": {}, "typeVersion": 1}, {"name": "Get Location By IP", "type": "n8n-nodes-base.uproc", "position": [850, 510], "parameters": {"ip": "={{$node[\"Create IP and Email Item\"].json[\"ip\"]}}", "tool": "getLocationByIp", "group": "geographic", "additionalOptions": {}}, "credentials": {"uprocApi": "miquel-uproc"}, "typeVersion": 1}, {"name": "User in Spain?", "type": "n8n-nodes-base.if", "position": [1050, 510], "parameters": {"conditions": {"string": [{"value1": "={{$node[\"Get Location By IP\"].json[\"message\"][\"country_code\"]}}", "value2": "ES"}]}}, "typeVersion": 1}, {"name": "Create IP and Email Item", "type": "n8n-nodes-base.functionItem", "position": [640, 510], "parameters": {"functionCode": "item.ip = \"************\";\nitem.email = \"<EMAIL>\";\n\nreturn item;"}, "typeVersion": 1}, {"name": "Send English Email", "type": "n8n-nodes-base.awsSes", "position": [1270, 650], "parameters": {"body": "Hi,\n\nThank you for your signup!", "subject": "Welcome aboard", "fromEmail": "<EMAIL>", "toAddresses": ["={{$node[\"Create IP and Email Item\"].json[\"email\"]}}"], "additionalFields": {}}, "credentials": {"aws": "ses"}, "typeVersion": 1}, {"name": "Send Spanish Email", "type": "n8n-nodes-base.awsSes", "position": [1270, 420], "parameters": {"body": "<PERSON><PERSON>,\n\n¡Gracias por registrarte!", "subject": "Bienvenido a bordo", "fromEmail": "<EMAIL>", "toAddresses": ["={{$node[\"Create IP and Email Item\"].json[\"email\"]}}"], "additionalFields": {}}, "credentials": {"aws": "ses"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"User in Spain?": {"main": [[{"node": "Send Spanish Email", "type": "main", "index": 0}], [{"node": "Send English Email", "type": "main", "index": 0}]]}, "Get Location By IP": {"main": [[{"node": "User in Spain?", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Create IP and Email Item", "type": "main", "index": 0}]]}, "Create IP and Email Item": {"main": [[{"node": "Get Location By IP", "type": "main", "index": 0}]]}}}