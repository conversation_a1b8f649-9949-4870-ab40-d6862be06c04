{"id": "MVPlLz3CiQok6rXy", "meta": {"instanceId": "7599ed929ea25767a019b87ecbc83b90e16a268cb51892887b450656ac4518a2", "templateCredsSetupCompleted": true}, "name": "Merge PDFs", "tags": [], "nodes": [{"id": "282b146b-ee58-4089-9ee6-94b153024bfa", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [740, -40], "parameters": {}, "typeVersion": 1}, {"id": "39a37abd-924f-474c-8dde-7536170797f2", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [960, -140], "parameters": {"url": "=https://www.intewa.com/fileadmin/documents/pdf-file.pdf", "options": {}}, "typeVersion": 4.2}, {"id": "b2359766-896f-4966-873e-b2dc0ee4f684", "name": "HTTP Request2", "type": "n8n-nodes-base.httpRequest", "position": [960, 60], "parameters": {"url": "=https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf", "options": {}}, "typeVersion": 4.2}, {"id": "7f70b25c-a171-4a7c-8067-141ce3275226", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [1180, -40], "parameters": {}, "typeVersion": 3.1}, {"id": "183b90b4-5f22-4ff8-b47f-96b7c4d522d7", "name": "Merge PDF1", "type": "@custom-js/n8n-nodes-pdf-toolkit.mergePdfs", "position": [1400, -40], "parameters": {}, "credentials": {"customJsApi": {"id": "h29wo2anYKdANAzm", "name": "CustomJS account"}}, "typeVersion": 1}, {"id": "131474f2-c6ca-4d1f-ba49-1f2f6d91394a", "name": "Read/Write Files from Disk", "type": "n8n-nodes-base.readWriteFile", "position": [1620, -40], "parameters": {"options": {}, "fileName": "test.pdf", "operation": "write"}, "typeVersion": 1}, {"id": "a86ddc41-60aa-482c-90f2-8eacc6bb0a9b", "name": "Read/Write Files from Disk4", "type": "n8n-nodes-base.readWriteFile", "position": [1840, -40], "parameters": {"options": {}, "fileSelector": "test.pdf"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "02f73022-a678-4660-ab1d-3531e4848cba", "connections": {"Merge": {"main": [[{"node": "Merge PDF1", "type": "main", "index": 0}]]}, "Merge PDF1": {"main": [[{"node": "Read/Write Files from Disk", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "HTTP Request2": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Read/Write Files from Disk": {"main": [[{"node": "Read/Write Files from Disk4", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}, {"node": "HTTP Request2", "type": "main", "index": 0}]]}}}