{"id": "3BkxvtCbF6hHGUgM", "meta": {"instanceId": "d847dccbed2cefba539a228a44c266869b59eafbd4f307c4928a1149fb542a9e", "templateCredsSetupCompleted": true}, "name": "N8N Financial Tracker Telegram Invoices to Notion with AI Summaries & Reports", "tags": [{"id": "OXcPKHaINFSvU1ux", "name": "Money", "createdAt": "2025-05-09T11:02:15.929Z", "updatedAt": "2025-05-09T11:02:15.929Z"}, {"id": "witgF3iHQ0sAlkjG", "name": "experimental", "createdAt": "2025-05-09T11:02:15.933Z", "updatedAt": "2025-05-09T11:02:15.933Z"}], "nodes": [{"id": "3792ae58-807f-4e83-a219-25c17c8b4048", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [680, 380], "parameters": {"options": {}, "modelName": "models/gemini-2.5-flash-preview-04-17"}, "credentials": {"googlePalmApi": {"id": "haEP6ehKtsSUjFmK", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "943f87e2-a1ac-4f7e-999b-8ea261259e5a", "name": "Basic LLM Chain", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [640, 220], "parameters": {"text": "=ini ada base64 invoice rangkumkan Pengeluaran dari invoice tersebut Nama Barang jumlah dan Pengeluaran masing masing barang dan total, outputnya jangan panjang panjang saya cukup berikan \n\ndate: DD-MM-YYYY ( Jika dari OCR tidak ada tanggal ambil tanggal hari ini )\nid:\nname:\n qty: \nprice:\n total:\ncategory:\ntax : (jika di total berbeda dengan item brati ada pajak nya hitungkan juga pajaknya masukan kesini)\n\nuntuk pilihan categorynya : Food & Beverage / Transportation / Utilities / Shopping / Healthcare / Entertaiment / Housing / Education\n\ndalam bentuk JSON array object, berikan juga key message summary untuk rangkuman, berikan rangkauman singkat total pengeluaran dan barang apa saja yang dibeli serta jumlah nya berikan juga pajaknya", "messages": {"messageValues": [{"type": "HumanMessagePromptTemplate", "messageType": "imageBinary"}]}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.4}, {"id": "247b78cb-c3f6-4f31-8559-0fff70de9ba9", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [0, 0], "parameters": {"width": 1703, "height": 580, "content": "## Automated Financial Tracker: Telegram Invoices to Notion with AI Summaries & Reports\n"}, "typeVersion": 1}, {"id": "e20045c2-a8ef-43d6-b619-6825f605e183", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [0, 620], "parameters": {"color": 5, "width": 1706, "height": 527, "content": "## Schedule report to send on chanel or private message\n"}, "typeVersion": 1}, {"id": "ed8d6544-af9e-416a-b1f3-624ca108427f", "name": "Schedule Trigger | for send chart report", "type": "n8n-nodes-base.scheduleTrigger", "position": [80, 880], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.2}, {"id": "22ad7ea1-9404-48bd-9d0f-0c58b8b66e3d", "name": "Get Recent Data from Notions", "type": "n8n-nodes-base.notion", "position": [400, 940], "parameters": {"filters": {"conditions": [{"key": "Created time|created_time", "condition": "past_week"}]}, "options": {}, "resource": "databasePage", "operation": "getAll", "returnAll": true, "databaseId": {"__rl": true, "mode": "list", "value": "1d858554-d218-807c-936c-d06c8a8ec769", "cachedResultUrl": "https://www.notion.so/1d858554d218807c936cd06c8a8ec769", "cachedResultName": "<PERSON><PERSON><PERSON><PERSON>"}, "filterType": "manual"}, "credentials": {"notionApi": {"id": "AhjWhO7Jpc5x7xKG", "name": "Notion account"}}, "typeVersion": 2.2}, {"id": "********-52da-4f9c-96a2-0a01d0a640f9", "name": "Summarize Transaction Data", "type": "n8n-nodes-base.summarize", "position": [760, 920], "parameters": {"options": {}, "fieldsToSplitBy": "property_category", "fieldsToSummarize": {"values": [{"field": "property_total", "aggregation": "sum"}]}}, "typeVersion": 1}, {"id": "80a374cb-00cf-46b1-9505-709be1c550da", "name": "Generate Chart", "type": "n8n-nodes-base.quick<PERSON><PERSON>", "position": [1200, 900], "parameters": {"data": "={{ $json.chart.data.datasets[0].data }}", "labelsMode": "array", "labelsArray": "={{ $json.chart.data.labels }}", "chartOptions": {}, "datasetOptions": {}}, "typeVersion": 1}, {"id": "6b7c67ee-b205-42f5-9441-eb2ecee4a503", "name": "Send Chart Image to Group or Private Chat", "type": "n8n-nodes-base.telegram", "position": [1460, 760], "webhookId": "66cce6e1-819c-487b-b8ad-3f02aebd40cb", "parameters": {"chatId": "-*************", "operation": "sendPhoto", "binaryData": true, "additionalFields": {"fileName": "chart", "message_thread_id": 571}}, "credentials": {"telegramApi": {"id": "J8yRVYmsnH74HuaD", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "06afd5ea-77b2-468d-b12b-1386d37a3ee6", "name": "Convert Data to JSON chart payload", "type": "n8n-nodes-base.code", "position": [1080, 900], "parameters": {"jsCode": "const labels = [];\nconst values = [];\n\nfor (const item of items) {\n  labels.push(item.json.property_category);\n  values.push(item.json.sum_property_total);\n}\n\nreturn [\n  {\n    json: {\n      chart: {\n        type: 'bar',\n        data: {\n          labels,\n          datasets: [\n            {\n              label: 'Spending by Category',\n              data: values,\n              backgroundColor: 'rgba(54, 162, 235, 0.6)',\n              borderColor: 'rgba(54, 162, 235, 1)',\n              borderWidth: 1\n            }\n          ]\n        },\n        options: {\n          plugins: {\n            title: {\n              display: true,\n              text: 'Spending Summary by Category'\n            }\n          },\n          scales: {\n            y: {\n              beginAtZero: true\n            }\n          }\n        }\n      }\n    }\n  }\n];"}, "typeVersion": 2}, {"id": "4ad8c9c9-fbec-46ce-943d-447ca687e031", "name": "Telegram Trigger | When recive photo", "type": "n8n-nodes-base.telegramTrigger", "position": [160, 160], "webhookId": "cac4ce91-ed1f-42ea-aebe-97ac3612aea6", "parameters": {"updates": ["message"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "J8yRVYmsnH74HuaD", "name": "Telegram account"}}, "typeVersion": 1.1}, {"id": "5231929f-2d7d-43ff-b9ae-************", "name": "Get Image Info", "type": "n8n-nodes-base.editImage", "position": [460, 160], "parameters": {"operation": "information"}, "typeVersion": 1}, {"id": "c8dcc6a1-2367-4049-9a8b-d8a04299ee72", "name": "Parse To your object | Table", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [1040, 460], "parameters": {"schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"message\": {\n      \"type\": \"string\"\n    },\n    \"summary\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\"date\": { \"type\": \"date\" },\n          \"id\": { \"type\": \"integer\" },\n          \"name\": { \"type\": \"string\" },\n          \"qty\": { \"type\": \"integer\" },\n          \"price\": { \"type\": \"number\" },\n          \"tax\": { \"type\": \"number\" },\n          \"total\": { \"type\": \"number\" },\"category\": { \"type\": \"string\" }\n        },\n        \"required\": [\"id\", \"name\", \"qty\", \"price\", \"total\",\"category\"]\n      }\n    }\n  },\n  \"required\": [\"message\", \"summary\"]\n}\n"}, "typeVersion": 1.2}, {"id": "bc098a26-4e55-4908-880c-e5f27737a941", "name": "Split Out | data transaction", "type": "n8n-nodes-base.splitOut", "position": [1120, 40], "parameters": {"options": {}, "fieldToSplitOut": "output.summary"}, "typeVersion": 1}, {"id": "2a42bc4b-a5c7-433e-91e4-aa5531570f73", "name": "Sendback to chat and give summarize text", "type": "n8n-nodes-base.telegram", "position": [1480, 400], "webhookId": "f90475fa-69cd-4e19-bc93-bffdceae8324", "parameters": {"text": "={{ $json.output.message }}", "chatId": "={{ $('<PERSON><PERSON><PERSON> Trigger | When recive photo').item.json.message.chat.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "J8yRVYmsnH74HuaD", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "bfc5c52e-313d-4257-bdfa-c542b687a853", "name": "Record To Notion Database", "type": "n8n-nodes-base.notion", "position": [1580, 120], "parameters": {"options": {}, "resource": "databasePage", "databaseId": {"__rl": true, "mode": "list", "value": "1d858554-d218-807c-936c-d06c8a8ec769", "cachedResultUrl": "https://www.notion.so/1d858554d218807c936cd06c8a8ec769", "cachedResultName": "<PERSON><PERSON><PERSON><PERSON>"}, "propertiesUi": {"propertyValues": [{"key": "Name|title", "title": "={{ $json.name }}"}, {"key": "Quantity|number", "numberValue": "={{ $json.qty }}"}, {"key": "Price|number", "numberValue": "={{ $json.price }}"}, {"key": "Total|number", "numberValue": "={{ $json.total }}"}, {"key": "Category|select", "selectValue": "={{ $json.category }}"}, {"key": "Date|rich_text", "textContent": "={{ $json.date }}"}, {"key": "Tax|number", "numberValue": "={{ $json.tax }}"}]}}, "credentials": {"notionApi": {"id": "AhjWhO7Jpc5x7xKG", "name": "Notion account"}}, "typeVersion": 2.2}, {"id": "f514554b-eb9e-47e2-ad6b-0b13036beaf4", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [40, 60], "parameters": {"color": 3, "width": 340, "height": 280, "content": "📸 INVOICE INPUT 📸\nBot listens here for photos of your receipts/invoices.\nEnsure your Telegram Bot API token is set in credentials."}, "typeVersion": 1}, {"id": "53fc4c77-3f16-4cb8-82e8-f4810af1f569", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [600, 60], "parameters": {"color": 5, "width": 360, "height": 460, "content": "🤖 AI MAGIC HAPPENS HERE 🧠\n- Image is sent to Google Gemini for data extraction.\n- Check 'Basic LLM Chain' to customize the AI prompt (e.g., categories, output format).\n- Requires Google Gemini API credentials."}, "typeVersion": 1}, {"id": "c6fb1193-7cc9-4f45-8a5f-20af41cdf3c8", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [980, 340], "parameters": {"color": 5, "width": 280, "height": 200, "content": "✨ STRUCTURING AI DATA ✨\nConverts the AI's text output into a usable JSON object.\nCheck the schema if you modify the AI prompt significantly."}, "typeVersion": 1}, {"id": "79a4e9ba-d1ea-4cfc-870c-145bae80c9b4", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1320, 0], "parameters": {"color": 2, "width": 380, "height": 240, "content": "📝 SAVING TO NOTION 📝\n- Extracted transaction data is saved here.\n- Configure with your Notion API key & Database ID.\n- Map fields correctly to your database columns!"}, "typeVersion": 1}, {"id": "9406306b-9f3d-4877-a888-1f5e16a431c1", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [20, 760], "parameters": {"height": 280, "content": "REPORTING SCHEDULE 🗓️\nSet how often you want to receive your spending report (e.g., weekly, monthly)."}, "typeVersion": 1}, {"id": "1b6c8a28-b0f0-44fb-be02-21725d950716", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [320, 760], "parameters": {"color": 2, "width": 280, "height": 380, "content": "📊 FETCHING DATA FOR REPORT 📊\n- Retrieves transactions from Notion for the report period.\n- Default: \"Past Week\". Adjust filter as needed.\n- Requires Notion API credentials & Database ID."}, "typeVersion": 1}, {"id": "4612006e-04a9-4ad5-9f05-d49ec13f31cf", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [660, 740], "parameters": {"width": 320, "height": 360, "content": "➕ SUMMARIZING SPENDING ➕\nAggregates your expenses, usually by category,\nto prepare for the chart."}, "typeVersion": 1}, {"id": "103132cf-37a6-455f-b19f-14d3e17af912", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [1040, 740], "parameters": {"width": 300, "height": 340, "content": "📈 GENERATING VISUAL REPORT 📈\nCreates the actual chart image based on your spending data.\nYou can customize chart type (bar, pie, etc.) here."}, "typeVersion": 1}, {"id": "24324366-33e5-4097-ab36-aac31cef0006", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [1380, 640], "parameters": {"color": 6, "width": 300, "height": 300, "content": "📤 SENDING REPORT TO TELEGRAM 📤\n- Delivers the generated chart to your chosen Telegram chat/group.\n- Set the correct Chat ID and Bot API token."}, "typeVersion": 1}, {"id": "e9fc1140-411b-411a-87a6-bbe9718ba3b3", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [1320, 280], "parameters": {"color": 6, "width": 300, "height": 280, "content": "💬 TRANSACTION SUMMARY 💬\nSends a confirmation message back to the user in Telegram\nwith a summary of the recorded expense."}, "typeVersion": 1}, {"id": "013fd587-3504-44b8-97e1-09cad47a0089", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [40, 360], "parameters": {"color": 7, "width": 460, "height": 240, "content": "  🔑 CREDENTIALS NEEDED 🔑\n  Remember to set up API keys/tokens for:\n  - Telegram\n  - Google Gemini\n  - Notion\n\n  💡 CUSTOMIZE ME! 💡\n  - Adjust AI prompts for better accuracy.\n  - Change Notion database structure.\n  - Modify report frequency and content.\n"}, "typeVersion": 1}, {"id": "8f6f0fdb-d3be-4464-a7db-ea4d642a4f55", "name": "Telegram", "type": "n8n-nodes-base.telegram", "position": [320, 160], "webhookId": "6e801e0b-72d1-42a9-ac47-61ac113a01d2", "parameters": {"fileId": "={{ $json.message.photo[3].file_id }}", "resource": "file"}, "credentials": {"telegramApi": {"id": "J8yRVYmsnH74HuaD", "name": "Telegram account"}}, "typeVersion": 1.2}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "a192c50c-4a77-44ee-b98a-f18d4ced2cb1", "connections": {"Telegram": {"main": [[{"node": "Get Image Info", "type": "main", "index": 0}]]}, "Generate Chart": {"main": [[{"node": "Send Chart Image to Group or Private Chat", "type": "main", "index": 0}]]}, "Get Image Info": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "Basic LLM Chain": {"main": [[{"node": "Split Out | data transaction", "type": "main", "index": 0}, {"node": "Sendback to chat and give summarize text", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "Summarize Transaction Data": {"main": [[{"node": "Convert Data to JSON chart payload", "type": "main", "index": 0}]]}, "Get Recent Data from Notions": {"main": [[{"node": "Summarize Transaction Data", "type": "main", "index": 0}]]}, "Parse To your object | Table": {"ai_outputParser": [[{"node": "Basic LLM Chain", "type": "ai_outputParser", "index": 0}]]}, "Split Out | data transaction": {"main": [[{"node": "Record To Notion Database", "type": "main", "index": 0}]]}, "Convert Data to JSON chart payload": {"main": [[{"node": "Generate Chart", "type": "main", "index": 0}]]}, "Telegram Trigger | When recive photo": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "Schedule Trigger | for send chart report": {"main": [[{"node": "Get Recent Data from Notions", "type": "main", "index": 0}]]}}}