{"id": "cDmsWx8ASzIxE3zw", "meta": {"instanceId": "494d0146a0f47676ad70a44a32086b466621f62da855e3eaf0ee51dee1f76753", "templateCredsSetupCompleted": true}, "name": "Update all Zammad Roles to default values", "tags": [], "nodes": [{"id": "32904945-fd7b-413f-898c-62b04406dfb0", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-920, 160], "parameters": {}, "typeVersion": 1}, {"id": "cbb0abd2-e153-477a-9346-00eaa1628c78", "name": "Zammad Univeral User Object", "type": "n8n-nodes-base.set", "position": [100, 220], "parameters": {"values": {"number": [{"name": "user_id", "value": "={{ $json.id }}"}, {"name": "organization_id", "value": "={{ $json.organization_id }}"}], "string": [{"name": "email", "value": "={{ $json.email }}"}, {"name": "firstname", "value": "={{ $json.firstname }}"}, {"name": "lastname", "value": "={{ $json.lastname }}"}, {"name": "role_ids", "value": "={{ $json.role_ids.join() }}\n"}, {"name": "groups", "value": "={{ $json.group_ids }}"}, {"name": "active", "value": "={{ $json.active }}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"id": "bbf82b69-07b5-4dff-afec-ee65eeedd037", "name": "Get all Users", "type": "n8n-nodes-base.zammad", "position": [-220, 220], "parameters": {"filters": {}, "operation": "getAll", "returnAll": true}, "credentials": {"zammadTokenAuthApi": {"id": "fj5GuzcJuNLQeMxz", "name": "<PERSON><PERSON><PERSON> account"}}, "typeVersion": 1}, {"id": "0c122c6b-22a7-41f5-8ea1-c9a32cd12ed6", "name": "If", "type": "n8n-nodes-base.if", "position": [400, 220], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "0be03c44-5017-4db6-87cd-91838b953d09", "operator": {"type": "array", "operation": "notContains", "rightType": "any"}, "leftValue": "={{ $('Basic Variables').item.json.exclude_zammad_users_by_id }}", "rightValue": "={{ $json.user_id }}"}, {"id": "6a730e91-fa40-4e8a-b6cb-894f2cb367c5", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.active }}", "rightValue": ""}, {"id": "b3d13440-f3fc-4172-b16d-e191f050a2bf", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "63f98607-1f56-4f83-93be-aa3b271da11b", "name": "Get all Roles", "type": "n8n-nodes-base.httpRequest", "position": [-220, -20], "parameters": {"url": "={{ $json.zammad_base_url }}/api/v1/roles", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{ $json.zammad_api_key }}"}]}}, "typeVersion": 4.2}, {"id": "961bf663-30be-45b4-87f0-d7ec94c3435b", "name": "Basic Variables", "type": "n8n-nodes-base.set", "position": [-620, 160], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "68b32087-5e23-4590-8042-0061234ce479", "name": "zammad_base_url", "type": "string", "value": "-put-your-zammad-base-url-"}, {"id": "7db7572e-2524-4f2a-a1d6-b44330662c30", "name": "zammad_api_key", "type": "string", "value": "-put-your-api-key-"}, {"id": "7cd0a1d2-8898-47c7-a1c6-195ff6162bb9", "name": "default_roles", "type": "array", "value": "={{ [-put-in-the-default-roles-ids-separated-by-comma-] }}"}, {"id": "4628705c-4875-4d42-90d1-692b9b44fc24", "name": "exclude_zammad_users_by_id", "type": "array", "value": "={{ [-put-in-the-user-ids-separated-by-comma-] }}"}]}}, "typeVersion": 3.4}, {"id": "a86577aa-1eb2-48e6-b941-bdfb37b58073", "name": "<PERSON><PERSON><PERSON> Univeral Role Object", "type": "n8n-nodes-base.set", "position": [100, -20], "parameters": {"values": {"number": [{"name": "role_id", "value": "={{ $json.id }}"}, {"name": "name", "value": "={{ $json.name }}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"id": "ecc387b3-092d-43c9-9187-784f07c4df77", "name": "Convert to Excel Roles", "type": "n8n-nodes-base.convertToFile", "position": [760, -40], "parameters": {"options": {"fileName": "Zammad_Roles.xlsx"}, "operation": "xlsx"}, "typeVersion": 1.1}, {"id": "e8055ae6-78d8-4736-adee-4b8c883b4000", "name": "Filter Roles if needed", "type": "n8n-nodes-base.if", "position": [400, -20], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "0ca9d3a3-b726-4396-8cec-4a74c8e3949b", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json }}", "rightValue": 1781}]}}, "typeVersion": 2.2}, {"id": "45b01919-ba58-4c7a-a97f-d169cb361855", "name": "Update Users to default Role(s)", "type": "n8n-nodes-base.httpRequest", "onError": "continueErrorOutput", "position": [760, 200], "parameters": {"url": "={{ $('Basic Variables').item.json.zammad_base_url }}/api/v1/users/{{ $json.user_id }}", "method": "PUT", "options": {}, "jsonBody": "={\n  \"role_ids\": [\n    {{ $('Basic Variables').item.json.default_roles }}\n  ]\n} ", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{ $('Basic Variables').item.json.zammad_api_key }}"}]}}, "typeVersion": 4.2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "4fa55089-104d-477e-8a93-c8cb0caf7bed", "connections": {"If": {"main": [[{"node": "Update Users to default Role(s)", "type": "main", "index": 0}]]}, "Get all Roles": {"main": [[{"node": "<PERSON><PERSON><PERSON> Univeral Role Object", "type": "main", "index": 0}]]}, "Get all Users": {"main": [[{"node": "Zammad Univeral User Object", "type": "main", "index": 0}]]}, "Basic Variables": {"main": [[{"node": "Get all Roles", "type": "main", "index": 0}, {"node": "Get all Users", "type": "main", "index": 0}]]}, "Filter Roles if needed": {"main": [[{"node": "Convert to Excel Roles", "type": "main", "index": 0}]]}, "Zammad Univeral Role Object": {"main": [[{"node": "Filter Roles if needed", "type": "main", "index": 0}]]}, "Zammad Univeral User Object": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Update Users to default Role(s)": {"main": [[], []]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Basic Variables", "type": "main", "index": 0}]]}}}