{"meta": {"instanceId": "cb484ba7b742928a2048bf8829668bed5b5ad9787579adea888f05980292a4a7", "templateCredsSetupCompleted": true}, "nodes": [{"id": "86ddd018-3d6b-46b9-aa93-dedd6c6b5076", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-880, 360], "webhookId": "a9668bb8-bbe8-418a-b5c9-ff7dd431244f", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "a5ba5090-8e3b-4408-82df-92d2c524039e", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-680, 360], "parameters": {"options": {"systemMessage": "You are a cybersecurity expert trained on MITRE ATT&CK and enterprise incident response. Your job is to:\n1. Extract TTP information from SIEM data.\n2. Provide actionable remediation steps tailored to the alert.\n3. Cross-reference historical patterns and related alerts.\n4. Recommend external resources for deeper understanding.\n\nEnsure that:\n- TTPs are tagged with the tactic, technique name, and technique ID.\n- Remediation steps are specific and actionable.\n- Historical data includes related alerts and notable trends.\n- External links are relevant to the observed behavior.\n"}}, "typeVersion": 1.7}, {"id": "67c52944-b616-4ea6-9507-e9fb6fcdbe2b", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-740, 580], "parameters": {"model": "gpt-4o", "options": {}}, "credentials": {"openAiApi": {"id": "QpFZ2EiM3WGl6Zr3", "name": "Marketing OpenAI"}}, "typeVersion": 1}, {"id": "55f6c16a-51ed-45e4-a1ab-aaaf1d7b5733", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [-720, 1220], "parameters": {"options": {}, "fieldToSplitOut": "data"}, "typeVersion": 1}, {"id": "46a5b8c6-3d34-4e9b-b812-23135f28c278", "name": "Embeddings OpenAI1", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [-580, 1420], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "QpFZ2EiM3WGl6Zr3", "name": "Marketing OpenAI"}}, "typeVersion": 1.2}, {"id": "561b0737-26d5-450d-bd9e-08e0a608d6f9", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [-460, 1440], "parameters": {"options": {"metadata": {"metadataValues": [{"name": "id", "value": "={{ $json.id }}"}, {"name": "name", "value": "={{ $json.name }}"}, {"name": "killchain", "value": "={{ $json.kill_chain_phases }}"}, {"name": "external", "value": "={{ $json.external_references }}"}]}}, "jsonData": "={{ $json.description }}", "jsonMode": "expressionData"}, "typeVersion": 1}, {"id": "6e8a4aed-7e8c-492a-b816-6ab1a98c312a", "name": "Token Splitter1", "type": "@n8n/n8n-nodes-langchain.textSplitterTokenSplitter", "position": [-460, 1620], "parameters": {}, "typeVersion": 1}, {"id": "0c54049e-b5e8-448f-b864-39aeb274de3e", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [-580, 580], "parameters": {}, "typeVersion": 1.3}, {"id": "96b776a0-10da-4f70-99d0-ad6b6ee8fcca", "name": "Embeddings OpenAI2", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [-460, 720], "parameters": {"model": "text-embedding-3-large", "options": {"dimensions": 1536}}, "credentials": {"openAiApi": {"id": "QpFZ2EiM3WGl6Zr3", "name": "Marketing OpenAI"}}, "typeVersion": 1.2}, {"id": "695fba89-8f42-47c3-9d86-73f4ea0e72df", "name": "Extract from File", "type": "n8n-nodes-base.extractFromFile", "position": [-920, 1220], "parameters": {"options": {}, "operation": "fromJson"}, "typeVersion": 1}, {"id": "0b9897b0-149b-43ce-b66c-e78552729aa5", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-1360, 1220], "parameters": {}, "typeVersion": 1}, {"id": "d8c29a14-0389-4748-a9de-686bf9a682c5", "name": "AI Agent1", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-540, -440], "parameters": {"text": "=Siem Alert Data:\nAlert: {{ $json.raw_subject }}\nDescription: {{ $json.description }}", "options": {"systemMessage": "You are a cybersecurity expert trained on MITRE ATT&CK and enterprise incident response. Your job is to:\n1. Extract TTP information from SIEM data.\n2. Provide actionable remediation steps tailored to the alert.\n3. Cross-reference historical patterns and related alerts.\n4. Recommend external resources for deeper understanding.\n\nEnsure that:\n- TTPs are tagged with the tactic, technique name, and technique ID.\n- Remediation steps are specific and actionable.\n- Historical data includes related alerts and notable trends.\n- External links are relevant to the observed behavior.\n\nPlease output your response in html format, but do not include ```html at the beginning \n"}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.7}, {"id": "55d0b00a-5046-45fa-87cb-cb0257caae87", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-600, -220], "parameters": {"model": "gpt-4o", "options": {}}, "credentials": {"openAiApi": {"id": "QpFZ2EiM3WGl6Zr3", "name": "Marketing OpenAI"}}, "typeVersion": 1}, {"id": "9b53566b-e021-403d-9d78-28504c5c1dfa", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [-320, -40], "parameters": {"model": "text-embedding-3-large", "options": {"dimensions": 1536}}, "credentials": {"openAiApi": {"id": "QpFZ2EiM3WGl6Zr3", "name": "Marketing OpenAI"}}, "typeVersion": 1.2}, {"id": "f3b44ef5-e928-4662-81ef-4dd044829607", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [-940, -440], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "cc572b71-65c9-460c-bdcd-1d20feb15b32", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1460, 940], "parameters": {"color": 7, "width": 1380, "height": 820, "content": "![n8n](https://uploads.n8n.io/templates/qdrantlogo.png)\n## Embed your Vector Store\nTo provide data for your Vector store, you need to pass it in as JSON, and ensure it's setup correctly. This flow pulls the JSON file from Google Drive and extracts the JSON data and then passes it into the qdrant collection. "}, "typeVersion": 1}, {"id": "d5052d52-bec2-4b70-b460-6d5789c28d2c", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-1460, 220], "parameters": {"color": 7, "width": 1380, "height": 680, "content": "![n8n](https://uploads.n8n.io/templates/n8n.png)\n## Talk to your Vector Store\nNow that your vector store has been updated with the embedded data, \nyou can use the n8n chat interface to talk to your data using OpenAI, \nOllama, or any of our supported LLMs."}, "typeVersion": 1}, {"id": "5cb478f6-17f3-4d7a-9b66-9e0654bd1dc9", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-1460, -700], "parameters": {"color": 7, "width": 2140, "height": 900, "content": "![Servicenow](https://uploads.n8n.io/templates/zendesk.png)\n## Deploy your Vector Store\nThis flow adds contextual information to your tickets using the Mitre Attack framework to help contextualize the ticket data."}, "typeVersion": 1}, {"id": "71ee28f5-84a2-4c6c-855a-6c7c09b2d62a", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [0, -160], "parameters": {"jsonSchemaExample": "{\n \"ttp_identification\": {\n \"alert_summary\": \"The alert indicates a check-in from the NetSupport RAT, a known Remote Access Trojan, suggesting command and control (C2) communication.\",\n \"mitre_attack_ttps\": [\n {\n \"tactic\": \"Command and Control\",\n \"technique\": \"Protocol or Service Impersonation\",\n \"technique_id\": \"T1001.003\",\n \"description\": \"The RAT's check-in over port 443 implies potential masquerading of its traffic as legitimate SSL/TLS traffic, a tactic often used to blend C2 communications with normal web traffic.\",\n \"reference\": \"https://attack.mitre.org/techniques/T1001/003/\"\n }\n ]\n },\n \"remediation_steps\": {\n \"network_segmentation\": {\n \"action\": \"Isolate the affected host\",\n \"target\": \"************\",\n \"reason\": \"Prevents further C2 communication or lateral movement.\"\n },\n \"endpoint_inspection\": {\n \"action\": \"Perform a thorough inspection\",\n \"target\": \"Impacted endpoint\",\n \"method\": \"Use endpoint detection and response (EDR) tools to check for additional persistence mechanisms.\"\n },\n \"network_traffic_analysis\": {\n \"action\": \"Investigate and block unusual traffic\",\n \"target\": \"IP **************\",\n \"method\": \"Implement blocks for the IP across the firewall or IDS/IPS systems.\"\n },\n \"system_patching\": {\n \"action\": \"Ensure all systems are updated\",\n \"method\": \"Apply the latest security patches to mitigate vulnerabilities exploited by RAT malware.\"\n },\n \"ioc_hunting\": {\n \"action\": \"Search for Indicators of Compromise (IoCs)\",\n \"method\": \"Check for NetSupport RAT IoCs across other endpoints within the network.\"\n }\n },\n \"historical_patterns\": {\n \"network_anomalies\": \"Past alerts involving similar attempts to use standard web ports (e.g., 80, 443) for non-standard applications could suggest a broader attempt to blend malicious traffic into legitimate streams.\",\n \"persistence_tactics\": \"Any detection of anomalies in task scheduling or shortcut modifications may indicate persistence methods similar to those used by RATs.\"\n },\n \"external_resources\": [\n {\n \"title\": \"ESET Report on Okrum and Ketrican\",\n \"description\": \"Discusses similar tactics involving protocol impersonation and C2.\",\n \"url\": \"https://www.eset.com/int/about/newsroom/research/okrum-ketrican/\"\n },\n {\n \"title\": \"Malleable C2 Profiles\",\n \"description\": \"Document on crafting custom C2 traffic profiles similar to the targeting methods used by NetSupport RAT.\",\n \"url\": \"https://www.cobaltstrike.com/help-malleable-c2\"\n },\n {\n \"title\": \"MITRE ATT&CK Technique Overview\",\n \"description\": \"Overview of Protocol or Service Impersonation tactics.\",\n \"url\": \"https://attack.mitre.org/techniques/T1001/003/\"\n }\n ]\n}\n"}, "typeVersion": 1.2}, {"id": "3aeb973d-22e5-4eaf-8fe8-fae3447909e1", "name": "<PERSON>ull Mitre Data From Gdrive", "type": "n8n-nodes-base.googleDrive", "position": [-1140, 1220], "parameters": {"fileId": {"__rl": true, "mode": "list", "value": "1oWBLO5AlIqbgo9mKD1hNtx92HdC6O28d", "cachedResultUrl": "https://drive.google.com/file/d/1oWBLO5AlIqbgo9mKD1hNtx92HdC6O28d/view?usp=drivesdk", "cachedResultName": "cleaned_mitre_attack_data.json"}, "options": {}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "AVa7MXBLiB9NYjuO", "name": "<PERSON>"}}, "typeVersion": 3}, {"id": "3b35633c-de80-4062-8497-cb65092d5708", "name": "Embed JSON in Qdrant Collection", "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "position": [-520, 1220], "parameters": {"mode": "insert", "options": {}, "qdrantCollection": {"__rl": true, "mode": "id", "value": "mitre"}}, "credentials": {"qdrantApi": {"id": "u0qre50aar6iqyxu", "name": "<PERSON> Demo Cluster"}}, "typeVersion": 1}, {"id": "5f7f2fd8-276f-4b3a-ae88-1f1765967883", "name": "Query Qdrant Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "position": [-480, 580], "parameters": {"mode": "retrieve-as-tool", "options": {}, "toolName": "mitre_attack_vector_store", "toolDescription": "The mitre_attack_vector_store is a knowledge base trained on the MITRE ATT&CK framework. It is designed to help identify, correlate, and provide context for cybersecurity incidents based on textual descriptions of alerts, events, or behaviors. This tool leverages precomputed embeddings of attack techniques, tactics, and procedures (TTPs) to map user queries (such as SIEM-generated alerts or JIRA ticket titles) to relevant MITRE ATT&CK techniques.\n\nBy analyzing input text, the vector store can:\n\nRetrieve the most relevant MITRE ATT&CK entries (e.g., techniques, tactics, descriptions, external references).\nProvide structured context about potential adversary behaviors.\nSuggest remediation actions or detection methods based on the input.", "qdrantCollection": {"__rl": true, "mode": "list", "value": "mitre", "cachedResultName": "mitre"}}, "credentials": {"qdrantApi": {"id": "u0qre50aar6iqyxu", "name": "<PERSON> Demo Cluster"}}, "typeVersion": 1}, {"id": "298ffc29-1d60-4c05-92c6-a61071629a3f", "name": "Qdrant Vector Store query", "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "position": [-320, -200], "parameters": {"mode": "retrieve-as-tool", "options": {}, "toolName": "mitre_attack_vector_store", "toolDescription": "The mitre_attack_vector_store is a knowledge base trained on the MITRE ATT&CK framework. It is designed to help identify, correlate, and provide context for cybersecurity incidents based on textual descriptions of alerts, events, or behaviors. This tool leverages precomputed embeddings of attack techniques, tactics, and procedures (TTPs) to map user queries (such as SIEM-generated alerts or JIRA ticket titles) to relevant MITRE ATT&CK techniques.\n\nBy analyzing input text, the vector store can:\n\nRetrieve the most relevant MITRE ATT&CK entries (e.g., techniques, tactics, descriptions, external references).\nProvide structured context about potential adversary behaviors.\nSuggest remediation actions or detection methods based on the input.", "qdrantCollection": {"__rl": true, "mode": "list", "value": "mitre", "cachedResultName": "mitre"}}, "credentials": {"qdrantApi": {"id": "u0qre50aar6iqyxu", "name": "<PERSON> Demo Cluster"}}, "typeVersion": 1}, {"id": "c47f0ae6-106d-46da-afc3-f7afb86923ff", "name": "Get all Zendesk Tickets", "type": "n8n-nodes-base.zendesk", "position": [-1180, -440], "parameters": {"options": {}, "operation": "getAll"}, "credentials": {"zendeskApi": {"id": "ROx0ipJapRomRxEX", "name": "Zendesk Demo Access"}}, "typeVersion": 1}, {"id": "0ec2c505-5721-41af-91c8-1b0b55826d9e", "name": "Update Zendesk with Mitre Data", "type": "n8n-nodes-base.zendesk", "position": [0, -360], "parameters": {"id": "={{ $('Loop Over Items').item.json.id }}", "operation": "update", "updateFields": {"internalNote": "=Summary: {{ $json.output.ttp_identification.alert_summary }}\n\n", "customFieldsUi": {"customFieldsValues": [{"id": 34479547176212, "value": "={{ $json.output.ttp_identification.mitre_attack_ttps[0].technique_id }}"}, {"id": 34479570659732, "value": "={{ $json.output.ttp_identification.mitre_attack_ttps[0].tactic }}"}]}}}, "credentials": {"zendeskApi": {"id": "ROx0ipJapRomRxEX", "name": "Zendesk Demo Access"}}, "typeVersion": 1}, {"id": "6a74a6d4-610a-4a13-afe4-7bb03d83d4c8", "name": "Move on to next ticket", "type": "n8n-nodes-base.noOp", "position": [360, -80], "parameters": {}, "typeVersion": 1}], "pinData": {}, "connections": {"AI Agent": {"main": [[]]}, "AI Agent1": {"main": [[{"node": "Update Zendesk with Mitre Data", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Embed JSON in Qdrant Collection", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "AI Agent1", "type": "main", "index": 0}]]}, "Token Splitter1": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Qdrant Vector Store query", "type": "ai_embedding", "index": 0}]]}, "Extract from File": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Embeddings OpenAI1": {"ai_embedding": [[{"node": "Embed JSON in Qdrant Collection", "type": "ai_embedding", "index": 0}]]}, "Embeddings OpenAI2": {"ai_embedding": [[{"node": "Query Qdrant Vector Store", "type": "ai_embedding", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "AI Agent1", "type": "ai_languageModel", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Embed JSON in Qdrant Collection", "type": "ai_document", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Move on to next ticket": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Get all Zendesk Tickets": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "AI Agent1", "type": "ai_outputParser", "index": 0}]]}, "Qdrant Vector Store query": {"ai_tool": [[{"node": "AI Agent1", "type": "ai_tool", "index": 0}]]}, "Query Qdrant Vector Store": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Pull Mitre Data From Gdrive": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "Update Zendesk with Mitre Data": {"main": [[{"node": "Move on to next ticket", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "<PERSON>ull Mitre Data From Gdrive", "type": "main", "index": 0}]]}}}