{"meta": {"instanceId": "b41148c809c7896d124743d940fc0964703e540af66564ef95e25a4ceea61c77", "templateCredsSetupCompleted": true}, "nodes": [{"id": "e87d3723-7e7a-4ff3-bffb-b2bd2096bd34", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1080, 260], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "uFPD9I4pWJ4xUVf7", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "d25bf3ea-0de4-4317-9205-651f8a1a6ba8", "name": "Basic LLM Chain", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1060, 40], "parameters": {"text": "={{ $json.text }}", "messages": {"messageValues": [{"message": "=Okay, I've further refined the system prompt to include only the \"content\" and \"priority\" fields in the JSON output for the Todoist tool. Here's the updated prompt:\n\n**System Prompt:**\n\n```\nYou are an AI agent acting as a project management assistant. The user will provide you with a task or project description. Your job is to break down this task or project into smaller, manageable sub-tasks. You will then format each sub-task into a JSON object suitable for input to the \"Todoist\" tool and provide these JSON objects in a list.\n\n**Requirements:**\n\n1.  **Sub-Task Decomposition:** Break down the task or project provided by the user into logical and actionable sub-tasks. Each sub-task should be self-contained, completable, and measurable.\n2.  **JSON Format for Todoist:** Format each sub-task as a JSON object with the following structure:\n\n    ```json\n    {\n      \"content\": \"[Task Description]\",\n      \"priority\": [Priority Level (1-4, where 4 is highest)]\n    }\n    ```\n\n    *   `content`: A clear and concise description of the task.\n    *   `priority`: An integer representing the task priority, ranging from 1 (lowest) to 4 (highest). Consider the importance and urgency of the task when assigning the priority.\n\n3.  **Tool Usage - Todoist JSON Output:** After decomposing the project into sub-tasks, you **MUST** format each sub-task into the JSON structure specified above and present all the JSON objects in a Python list. This list will be the direct input to the \"Todoist\" tool.\n\n4.  **Contextual Understanding:** Fully understand the context of the task or project provided by the user. If necessary, ask for additional information or clarification to resolve any ambiguities.\n\n5.  **Limitations:**\n\n    *   Avoid very general or abstract sub-tasks.\n    *   Ensure that each sub-task is completable and measurable.\n    *   When creating sub-tasks, consider the user's skills and resources.\n    *   Ensure all the output is valid JSON format within a python list\n\n**User Input:**\n\nThe user will provide you with a task or project description in the following format:\n\n```\nProject Description: [User's Entered Task or Project Description]\n```\n\n**Example:**\n\n**User Input:**\n\n```\nProject Description: Plan a team offsite.\n```\n\n**LLM Response:**\n\n```python\n[\n  {\n    \"content\": \"Research potential offsite locations.\",\n    \"priority\": 3\n  },\n  {\n    \"content\": \"Determine the budget for the offsite.\",\n    \"priority\": 4\n  },\n  {\n    \"content\": \"Send out a survey to gather team preferences.\",\n    \"priority\": 3\n  },\n  {\n    \"content\": \"Book the chosen venue.\",\n    \"priority\": 4\n  },\n  {\n    \"content\": \"Plan team-building activities.\",\n    \"priority\": 2\n  }\n]\n```\n\n**Key Changes and Explanations:**\n\n*   **Simplified JSON Structure:** The JSON object now only includes `content` and `priority`.\n*   **Example Updated:** The example response reflects the simplified JSON format.\n*   **Conciseness:** The prompt is now more concise, focusing only on the necessary fields.\n\n**Jinja2 Template Version**\n\n```python\nfrom jinja2 import Template\n\ntemplate_string = \"\"\"\nYou are an AI agent acting as a project management assistant. The user will provide you with a task or project description. Your job is to break down this task or project into smaller, manageable sub-tasks. You will then format each sub-task into a JSON object suitable for input to the \"Todoist\" tool and provide these JSON objects in a list.\n\n**Requirements:**\n\n1.  **Sub-Task Decomposition:** Break down the task or project provided by the user into logical and actionable sub-tasks. Each sub-task should be self-contained, completable, and measurable.\n2.  **JSON Format for Todoist:** Format each sub-task as a JSON object with the following structure:\n\n    ```json\n    {\n      \"content\": \"[Task Description]\",\n      \"priority\": [Priority Level (1-4, where 4 is highest)]\n    }\n    ```\n\n    *   `content`: A clear and concise description of the task.\n    *   `priority`: An integer representing the task priority, ranging from 1 (lowest) to 4 (highest). Consider the importance and urgency of the task when assigning the priority.\n\n3.  **Tool Usage - Todoist JSON Output:** After decomposing the project into sub-tasks, you **MUST** format each sub-task into the JSON structure specified above and present all the JSON objects in a Python list. This list will be the direct input to the \"Todoist\" tool.\n\n4.  **Contextual Understanding:** Fully understand the context of the task or project provided by the user. If necessary, ask for additional information or clarification to resolve any ambiguities.\n\n5.  **Limitations:**\n\n    *   Avoid very general or abstract sub-tasks.\n    *   Ensure that each sub-task is completable and measurable.\n    *   When creating sub-tasks, consider the user's skills and resources.\n    *   Ensure all the output is valid JSON format within a python list\n\n**User Input:**\n\nThe user will provide you with a task or project description in the following format:\n\n```\nProject Description: {{ project_description }}\n```\n\n**Example:**\n\n**User Input:**\n\n```\nProject Description: Plan a team offsite.\n```\n\n**LLM Response:**\n\n```python\n[\n  {\n    \"content\": \"Research potential offsite locations.\",\n    \"priority\": 3\n  },\n  {\n    \"content\": \"Determine the budget for the offsite.\",\n    \"priority\": 4\n  },\n  {\n    \"content\": \"Send out a survey to gather team preferences.\",\n    \"priority\": 3\n  },\n  {\n    \"content\": \"Book the chosen venue.\",\n    \"priority\": 4\n  },\n  {\n    \"content\": \"Plan team-building activities.\",\n    \"priority\": 2\n  }\n]\n```\n\"\"\"\n\ntemplate = Template(template_string)\n\n# Example Usage\nproject_description = \"Plan a team offsite.\"\nprompt = template.render(project_description=project_description)\n\nprint(prompt)\n```\n \n"}]}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.5}, {"id": "ddfe59c5-574c-470b-b2cc-efa05da74972", "name": "Receive Telegram Messages", "type": "n8n-nodes-base.telegramTrigger", "position": [-220, -100], "webhookId": "4e2cd560-ae4e-4ed7-a8ea-984518404e51", "parameters": {"updates": ["message"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "lff3pLERRdQmkmeV", "name": "Telegram account"}}, "typeVersion": 1.1}, {"id": "23f2cedd-bcd2-4a94-acc1-8829b30553dc", "name": "Voice or Text?", "type": "n8n-nodes-base.switch", "position": [140, -20], "parameters": {"rules": {"values": [{"outputKey": "Audio", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "af30c479-4542-405f-b315-37c50c4e2bef", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.message.voice.file_id }}", "rightValue": ""}]}, "renameOutput": true}, {"outputKey": "Text", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a3ca8cd4-fbb2-40b5-829a-24724f2fbc85", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.message.text || \"\" }}", "rightValue": ""}]}, "renameOutput": true}, {"outputKey": "Error", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "9bcfdee0-2f09-4037-a7b9-689ef392371d", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "error", "rightValue": ""}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "128e8268-a256-4256-8757-9ece8be86d75", "name": "Fetch Voice Message", "type": "n8n-nodes-base.telegram", "position": [500, -120], "webhookId": "********-4943-4c32-b18c-97c410cc3409", "parameters": {"fileId": "={{ $json.message.voice.file_id }}", "resource": "file"}, "credentials": {"telegramApi": {"id": "lff3pLERRdQmkmeV", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "d8219ba5-bb33-44f5-a9a2-65fd16be335b", "name": "Transcribe Voice to Text", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [720, -120], "parameters": {"options": {}, "resource": "audio", "operation": "translate"}, "credentials": {"openAiApi": {"id": "uFPD9I4pWJ4xUVf7", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "0c5f5568-fd14-4c65-8661-ebc5803158ce", "name": "Prepare for LLM", "type": "n8n-nodes-base.set", "position": [620, 100], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "b324a329-3c49-4f7f-b683-74331b7fe7f8", "name": "=text", "type": "string", "value": "={{$json.message.text}}"}]}}, "typeVersion": 3.4}, {"id": "76ed8f5c-59f7-4cb9-9e59-25ac7e9e8c60", "name": "Extract Tasks", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [1220, 260], "parameters": {"jsonSchemaExample": "  {\n    \"content\": \"Send out invitations.\",\n    \"priority\": 3\n  }"}, "typeVersion": 1.2}, {"id": "7d0dbcb7-aac1-4eea-8f0b-6173148bfd3f", "name": "Create Todoist Tasks", "type": "n8n-nodes-base.todoist", "position": [1620, 40], "parameters": {"content": "={{ $json.output.content }}", "options": {"priority": "={{ $json.output.priority }}"}, "project": {"__rl": true, "mode": "list", "value": "**********", "cachedResultName": "Task"}}, "credentials": {"todoistApi": {"id": "yqSn5VBXyA4R6hgt", "name": "Todoist account"}}, "typeVersion": 2.1}, {"id": "544b3f63-8ac1-4f81-9c24-943df16d9324", "name": "Send Confirmation", "type": "n8n-nodes-base.telegram", "position": [1880, 40], "webhookId": "5699aecd-e061-4b7f-af7b-4a23eb7201c6", "parameters": {"text": "=Task : {{ $json.content }} Task Link :{{ $json.url }}", "chatId": "={{ $('Receive Telegram Messages').item.json.message.chat.id }}", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "lff3pLERRdQmkmeV", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "b244f935-3047-4581-84ac-b01b2f962c1d", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-260, -240], "parameters": {"width": 260, "height": 320, "content": " \n**This workflow listens for incoming voice or text messages from Telegram users.** "}, "typeVersion": 1}, {"id": "fa99930d-8e75-4f1e-aa9b-47c38e611538", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [440, -220], "parameters": {"width": 460, "height": 260, "content": " **Voice messages are fetched from Telegram and transcribed into text using OpenAI's Whisper API.**  "}, "typeVersion": 1}, {"id": "beb460c9-0412-40c4-a3cf-76660eb0e1b8", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1000, -60], "parameters": {"width": 380, "height": 440, "content": " \n**The LLM (OpenAI Chat Model) analyzes the text and breaks it down into tasks and sub-tasks, formatted for Todoist.**  "}, "typeVersion": 1}], "pinData": {}, "connections": {"Extract Tasks": {"ai_outputParser": [[{"node": "Basic LLM Chain", "type": "ai_outputParser", "index": 0}]]}, "Voice or Text?": {"main": [[{"node": "Fetch Voice Message", "type": "main", "index": 0}], [{"node": "Prepare for LLM", "type": "main", "index": 0}]]}, "Basic LLM Chain": {"main": [[{"node": "Create Todoist Tasks", "type": "main", "index": 0}]]}, "Prepare for LLM": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "Fetch Voice Message": {"main": [[{"node": "Transcribe Voice to Text", "type": "main", "index": 0}]]}, "Create Todoist Tasks": {"main": [[{"node": "Send Confirmation", "type": "main", "index": 0}]]}, "Transcribe Voice to Text": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "Receive Telegram Messages": {"main": [[{"node": "Voice or Text?", "type": "main", "index": 0}]]}}}