{"meta": {"instanceId": "cb484ba7b742928a2048bf8829668bed5b5ad9787579adea888f05980292a4a7", "templateCredsSetupCompleted": true}, "nodes": [{"id": "ffcd401c-3c2d-4fc5-92d7-bd55394e2dc9", "name": "Check if Competitor Data Found", "type": "n8n-nodes-base.if", "position": [2780, 240], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "loose"}, "combinator": "and", "conditions": [{"id": "9d720bd5-f29b-4ac8-92b4-ee2d5df074fe", "operator": {"type": "array", "operation": "lengthGte", "rightType": "number"}, "leftValue": "={{ $('Execute Workflow Trigger').item.json.AIoutput.Competitors }}", "rightValue": 1}]}, "looseTypeValidation": true}, "typeVersion": 2.2}, {"id": "65d9cd7a-d0a7-4892-a199-40f4d35a0240", "name": "Check if Integration Data Found", "type": "n8n-nodes-base.if", "position": [2800, -400], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "9d720bd5-f29b-4ac8-92b4-ee2d5df074fe", "operator": {"type": "array", "operation": "lengthGte", "rightType": "number"}, "leftValue": "={{ $json.AIoutput.Integrations }}", "rightValue": 1}]}}, "typeVersion": 2.2}, {"id": "c48921de-d1f8-48a0-b076-6cd6ff2442f6", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [2280, -400], "parameters": {}, "typeVersion": 1}, {"id": "74a0d14a-2020-4993-890a-1581da809060", "name": "Check if objection found", "type": "n8n-nodes-base.if", "position": [2900, -1020], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "0fb03927-4c00-4479-b8e3-6298afa4d41e", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.AIoutput.Objection.Nature }}", "rightValue": "null"}]}}, "typeVersion": 2.2}, {"id": "3e0fad7e-1a08-44ac-8212-ccc96f61669d", "name": "Create Competitors in DB", "type": "n8n-nodes-base.notion", "position": [3580, 80], "parameters": {"title": "={{ $('Execute Workflow Trigger').item.json.metaData.title }}", "options": {"icon": "🤺"}, "resource": "databasePage", "databaseId": {"__rl": true, "mode": "list", "value": "1375b6e0-c94f-8010-9161-c65e2c0f093a", "cachedResultUrl": "https://www.notion.so/1375b6e0c94f80109161c65e2c0f093a", "cachedResultName": "Competitors Database"}, "propertiesUi": {"propertyValues": [{"key": "Competitor Status|select", "selectValue": "={{ $json['aiResponse.Competitors'].Tag }}"}, {"key": "Summary|rich_text", "textContent": "={{ $json['aiResponse.Competitors'].Reason }}"}, {"key": "Pricing Summary|rich_text", "textContent": "={{ $json['aiResponse.Competitors'].Pricing ? $json['aiResponse.Competitors'].Pricing : 'None Found' }}"}, {"key": "Competitor Tracked?|checkbox", "checkboxValue": "={{ typeof $json['aiResponse.Competitors'].Known === 'string' ? false : $json['aiResponse.Competitors'].Known }}"}, {"key": "Name|title", "title": "={{ $json['aiResponse.Competitors'].Name }}"}, {"key": "Sales Call Summaries|relation", "relationValue": ["={{ $('Execute Workflow Trigger').item.json.notionData[0].id }}"]}, {"key": "Competitor Sentiment|multi_select", "multiSelectValue": "={{ $json['aiResponse.Competitors'].Sentiment }}"}, {"key": "Mentioned Date|date", "date": "={{ $('Execute Workflow Trigger').item.json.metaData.started }}"}]}}, "credentials": {"notionApi": {"id": "80", "name": "Notion david-internal"}}, "retryOnFail": true, "typeVersion": 2.2, "waitBetweenTries": 3000}, {"id": "7f917a42-a25b-41bb-b904-0fd44e9c2b78", "name": "Create Integrations", "type": "n8n-nodes-base.notion", "position": [3400, -560], "parameters": {"title": "={{ $('Execute Workflow Trigger').item.json.metaData.title }}", "options": {"icon": "⚙️"}, "resource": "databasePage", "databaseId": {"__rl": true, "mode": "list", "value": "1375b6e0-c94f-80f2-a449-c47795337c3d", "cachedResultUrl": "https://www.notion.so/1375b6e0c94f80f2a449c47795337c3d", "cachedResultName": "Integrations Database"}, "propertiesUi": {"propertyValues": [{"key": "Name|title", "title": "={{ $json[\"AIoutput.Integrations\"].IntegrationName }}"}, {"key": "IntegrationStatus|select", "selectValue": "={{ $json[\"AIoutput.Integrations\"].IntegrationStatus }}"}, {"key": "Summary|rich_text", "textContent": "={{ $json[\"AIoutput.Integrations\"].SummaryOfUse }}"}, {"key": "UsageStatus|select", "selectValue": "={{ $json[\"AIoutput.Integrations\"].UsageStatus }}"}, {"key": "Sales Call Summaries|relation", "relationValue": ["={{ $('Execute Workflow Trigger').item.json.notionData[0].id }}"]}]}}, "credentials": {"notionApi": {"id": "80", "name": "Notion david-internal"}}, "retryOnFail": true, "typeVersion": 2.2, "waitBetweenTries": 3000}, {"id": "54585eef-41b9-4ee5-ba1b-6ed502b2b7f9", "name": "Update Call with Objection Summary", "type": "n8n-nodes-base.notion", "position": [4320, -1180], "parameters": {"pageId": {"__rl": true, "mode": "id", "value": "={{ $('Execute Workflow Trigger').item.json.notionData[0].id }}"}, "options": {}, "resource": "databasePage", "operation": "update", "propertiesUi": {"propertyValues": [{"key": "Objection Summary|rich_text", "textContent": "={{ $('Execute Workflow Trigger').item.json.AIoutput.Objection.Nature }}"}]}}, "credentials": {"notionApi": {"id": "80", "name": "Notion david-internal"}}, "retryOnFail": true, "typeVersion": 2.2, "waitBetweenTries": 3000}, {"id": "031a430a-1ac1-4aec-ae21-de7450f03855", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [2720, -760], "parameters": {"color": 7, "width": 1480, "height": 620, "content": "## Integration Data Processing\nIf it's found, we add it to Notion. "}, "typeVersion": 1}, {"id": "ee4e0519-0969-4cb2-a4fd-fdba6313192d", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [2720, -120], "parameters": {"color": 7, "width": 1480, "height": 620, "content": "## Competitor Data Processing"}, "typeVersion": 1}, {"id": "9518c874-1788-415c-965a-6c90321e501d", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [2140, -760], "parameters": {"color": 7, "width": 560, "height": 620, "content": "## Receives AI Data from other workflow\n"}, "typeVersion": 1}, {"id": "951ba353-afef-4061-91b1-5fe8c6cd9b38", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [1780, -960], "parameters": {"width": 340, "height": 820, "content": "![Callforge](https://uploads.n8n.io/templates/callforgeshadow.png)\n## CallForge - The AI Gong Sales Call Processor\nCallForge allows you to extract important information for different departments from your Sales Gong Calls. \n\n### AI Output Processor\nOnce the AI data is generated, it is then added (or not!) to the Notion Database here. This is also where the Pipedrive or Salesforce integration will be added once that portion is complete. "}, "typeVersion": 1}, {"id": "24bde4bf-24bb-4c76-80fc-6bd80806b76a", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [2720, -1260], "parameters": {"color": 7, "width": 2140, "height": 480, "content": "## Objection Data Processing\n"}, "typeVersion": 1}, {"id": "b271cddb-2dc4-4b68-91c0-60bae45c0f63", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [2720, 520], "parameters": {"color": 7, "width": 1480, "height": 620, "content": "## Use Case Processing"}, "typeVersion": 1}, {"id": "5277d26b-ca62-466c-b0fd-88f0a55dbe6c", "name": "Check for use cases", "type": "n8n-nodes-base.if", "position": [2860, 960], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "7f182ff7-b5cf-44d0-9645-9200bb7afa24", "operator": {"type": "array", "operation": "lengthGte", "rightType": "number"}, "leftValue": "={{ $json.AIoutput.UseCases }}", "rightValue": 1}]}}, "typeVersion": 2.2}, {"id": "ba4f7b4c-613a-42fe-9228-c9c12d2677d8", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [2720, -1640], "parameters": {"color": 7, "width": 420, "height": 360, "content": "## Core Data Processing\n"}, "typeVersion": 1}, {"id": "85880852-03e8-4d7e-bdc6-ed381f16d425", "name": "Update Notion Call object with AI data", "type": "n8n-nodes-base.notion", "position": [2840, -1520], "parameters": {"pageId": {"__rl": true, "mode": "id", "value": "={{ $('Execute Workflow Trigger').item.json.notionData[0].id }}"}, "options": {}, "resource": "databasePage", "operation": "update", "propertiesUi": {"propertyValues": [{"key": "Call Summary|rich_text", "textContent": "={{ $json.AIoutput.CallSummary }}"}, {"key": "Next Steps|rich_text", "textContent": "={{ $json.AIoutput.NextSteps.map(step => `• ${step}`).join('\\n\\n') }}"}, {"key": "Sentiment|select", "selectValue": "={{ $json.AIoutput.Sentiment }}"}, {"key": "Customer Pain Points|rich_text", "textContent": "={{ $json.AIoutput.CustomerPainPoints.map(painPoint => `• ${painPoint}`).join('\\n\\n') }}"}]}}, "credentials": {"notionApi": {"id": "80", "name": "Notion david-internal"}}, "retryOnFail": true, "typeVersion": 2.2, "waitBetweenTries": 3000}, {"id": "74185c7e-8872-47f3-ace5-256e102d043b", "name": "Split Out all objections", "type": "n8n-nodes-base.splitOut", "position": [3360, -1180], "parameters": {"include": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options": {}, "fieldToSplitOut": "AIoutput.Objection.ObjectionTags", "fieldsToInclude": "id"}, "typeVersion": 1}, {"id": "41ecf92b-d6ee-4111-8684-afbfe45d760c", "name": "Format Tag Data for notion", "type": "n8n-nodes-base.set", "position": [3560, -1180], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d1b2c0c4-71de-429e-9220-d5ece2030180", "name": "tag", "type": "object", "value": "={\"name\": \"{{ $json[\"AIoutput.Objection.ObjectionTags\"] }}\"}"}]}}, "typeVersion": 3.4}, {"id": "c0aa2bcc-0dee-4eeb-a93c-7aeb76ed3acf", "name": "Bundle all Tags", "type": "n8n-nodes-base.aggregate", "position": [3720, -1180], "parameters": {"options": {"mergeLists": false}, "fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "tag"}]}}, "typeVersion": 1}, {"id": "dca88d3e-9dc2-45be-b0d7-728d4f7ed641", "name": "Add Objection Tag to Notion", "type": "n8n-nodes-base.httpRequest", "position": [3920, -1180], "parameters": {"url": "=https://api.notion.com/v1/pages/{{ $('Execute Workflow Trigger').item.json.notionData[0].id }}", "method": "PATCH", "options": {}, "jsonBody": "={\n    \"object\": \"page\",\n    \"id\": \"{{ $('Execute Workflow Trigger').item.json.notionData[0].id }}\",\n    \"properties\": {\n    \"Objections\": {\n      \"multi_select\": {{ $json.tag.toJsonString() }}\n    }\n  }\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{"name": "Notion-Version", "value": "2022-06-28"}]}, "nodeCredentialType": "notionApi"}, "credentials": {"notionApi": {"id": "80", "name": "Notion david-internal"}}, "retryOnFail": true, "typeVersion": 4.2, "waitBetweenTries": 3000}, {"id": "85816770-807e-4cbb-98ab-ddf20c2066a0", "name": "Wait for rate limiting again", "type": "n8n-nodes-base.wait", "position": [4100, -1180], "webhookId": "d5d6be58-6adb-4cff-a05a-c96b5647b72e", "parameters": {"amount": 4}, "typeVersion": 1.1}, {"id": "99fd790e-9ba1-4a8c-86cd-981ecef6d4f4", "name": "Merge <PERSON> Threads", "type": "n8n-nodes-base.set", "position": [4660, -1000], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d8fc65ad-2b05-40c1-84c7-7bda819f0f1f", "name": "aiResponse", "type": "object", "value": "={{ $('Execute Workflow Trigger').item.json.aiResponse }}"}]}}, "typeVersion": 3.4}, {"id": "f71aa765-c10f-4ace-8051-8416b7cfefce", "name": "Wait for rate limiting - Integration", "type": "n8n-nodes-base.wait", "position": [3000, -560], "webhookId": "9b4c5462-04ef-44ec-a7cf-a061e8c9e108", "parameters": {"amount": 3}, "typeVersion": 1.1}, {"id": "df0772d8-8612-4830-ae66-ba9db77e654e", "name": "Split Out Integration data", "type": "n8n-nodes-base.splitOut", "position": [3200, -560], "parameters": {"include": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options": {}, "fieldToSplitOut": "AIoutput.Integrations", "fieldsToInclude": "id"}, "typeVersion": 1}, {"id": "40775bc5-2678-41c3-a651-000edba9920f", "name": "Bundle Integration Data to 1 object", "type": "n8n-nodes-base.aggregate", "position": [3600, -560], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "tagdata"}, "typeVersion": 1}, {"id": "b4f2a517-a685-4bca-a994-7d395c2743e5", "name": "Merge Integration Thread", "type": "n8n-nodes-base.set", "position": [3860, -380], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "513c8505-75dd-4ecf-9bd7-4f51cbb12f81", "name": "AIoutput", "type": "string", "value": "={{ $json.AIoutput }}"}]}}, "typeVersion": 3.4}, {"id": "c606e481-b664-4a64-923d-ffa7d0c50cb9", "name": "Wait for rate limiting - Competitors", "type": "n8n-nodes-base.wait", "position": [3000, 80], "webhookId": "1cf8f53b-5981-4ba1-84b4-8f1921b9b417", "parameters": {"amount": 3}, "typeVersion": 1.1}, {"id": "1f75b7ab-a452-4f36-92d9-ec0358144f0d", "name": "Wait for rate limiting - Objection", "type": "n8n-nodes-base.wait", "position": [3160, -1180], "webhookId": "5dae7747-7d12-4e66-990c-bca7ce6fc0d2", "parameters": {"amount": 3}, "typeVersion": 1.1}, {"id": "6a2f1dd6-e5a5-4b68-9b62-98491621e47d", "name": "Wait for rate limiting - Use Cases", "type": "n8n-nodes-base.wait", "position": [3080, 800], "webhookId": "ba9f17ac-4fec-47c1-80e4-31e6d81e47f6", "parameters": {"amount": 3}, "typeVersion": 1.1}, {"id": "6cb3ba0d-733d-4f51-bfbf-57e350e4ab93", "name": "Split Out Use Cases", "type": "n8n-nodes-base.splitOut", "position": [3280, 800], "parameters": {"options": {}, "fieldToSplitOut": "AIoutput.UseCases"}, "typeVersion": 1}, {"id": "4a669d54-ea1b-44a8-a7de-afa79d171467", "name": "Create Use Cases", "type": "n8n-nodes-base.notion", "position": [3500, 800], "parameters": {"title": "={{ $('Execute Workflow Trigger').item.json.metaData.title }}", "options": {"icon": "💼"}, "resource": "databasePage", "databaseId": {"__rl": true, "mode": "list", "value": "17c5b6e0-c94f-80e8-bf90-f290e8c78f9d", "cachedResultUrl": "https://www.notion.so/17c5b6e0c94f80e8bf90f290e8c78f9d", "cachedResultName": "Use Cases"}, "propertiesUi": {"propertyValues": [{"key": "Department Tag|multi_select", "multiSelectValue": "={{ $json.DepartmentTags }}"}, {"key": "Industry Tag|multi_select", "multiSelectValue": "={{ $json.IndustryTags }}"}, {"key": "Summary|title", "title": "={{ $json.Summary }}"}, {"key": "Sales Call Summaries|relation", "relationValue": ["={{ $('Execute Workflow Trigger').item.json.notionData[0].id }}"]}, {"key": "Implementation|select", "selectValue": "={{ $json.ImplementationStatus }}"}]}}, "credentials": {"notionApi": {"id": "2B3YIiD4FMsF9Rjn", "name": "Angelbot Notion"}}, "retryOnFail": true, "typeVersion": 2.2, "waitBetweenTries": 3000}, {"id": "e07e56a3-5f2f-4bc6-995a-af3944c72983", "name": "Bundle Use Case Data to 1 object", "type": "n8n-nodes-base.aggregate", "position": [3700, 800], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "tagdata"}, "typeVersion": 1}, {"id": "76f2d3cb-9bf9-4f8c-994c-45766080dbcb", "name": "Bundle Competitor Data to 1 object", "type": "n8n-nodes-base.aggregate", "position": [3800, 80], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "tagdata"}, "typeVersion": 1}, {"id": "9ddf3b34-8b9e-4149-8946-51e208973c37", "name": "Split Out Competitor Data", "type": "n8n-nodes-base.splitOut", "position": [3360, 80], "parameters": {"include": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options": {}, "fieldToSplitOut": "aiResponse.Competitors", "fieldsToInclude": "id"}, "typeVersion": 1}, {"id": "ac4879fc-21a1-49e5-a388-67550032bcd4", "name": "Get AI Response - Competitors", "type": "n8n-nodes-base.set", "position": [3180, 80], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d8fc65ad-2b05-40c1-84c7-7bda819f0f1f", "name": "aiResponse", "type": "object", "value": "={{ $('Execute Workflow Trigger').item.json.AIoutput }}"}]}}, "typeVersion": 3.4}, {"id": "063bdedc-fa58-4c05-9ba4-523b4297c4f2", "name": "Merge Competitor Thread", "type": "n8n-nodes-base.set", "position": [4000, 260], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d8fc65ad-2b05-40c1-84c7-7bda819f0f1f", "name": "aiResponse", "type": "object", "value": "={{ $('Execute Workflow Trigger').item.json.AIoutput }}"}]}}, "typeVersion": 3.4}, {"id": "c2f253ec-8659-4ead-aed3-e319fb165b90", "name": "Merge Use Case Thread", "type": "n8n-nodes-base.set", "position": [4000, 980], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d8fc65ad-2b05-40c1-84c7-7bda819f0f1f", "name": "aiResponse", "type": "object", "value": "={{ $('Execute Workflow Trigger').item.json.aiResponse }}"}]}}, "typeVersion": 3.4}], "pinData": {}, "connections": {"Bundle all Tags": {"main": [[{"node": "Add Objection Tag to Notion", "type": "main", "index": 0}]]}, "Create Use Cases": {"main": [[{"node": "Bundle Use Case Data to 1 object", "type": "main", "index": 0}]]}, "Check for use cases": {"main": [[{"node": "Wait for rate limiting - Use Cases", "type": "main", "index": 0}], [{"node": "Merge Use Case Thread", "type": "main", "index": 0}]]}, "Create Integrations": {"main": [[{"node": "Bundle Integration Data to 1 object", "type": "main", "index": 0}]]}, "Split Out Use Cases": {"main": [[{"node": "Create Use Cases", "type": "main", "index": 0}]]}, "Merge Competitor Thread": {"main": [[]]}, "Merge Objection Threads": {"main": [[]]}, "Check if objection found": {"main": [[{"node": "Wait for rate limiting - Objection", "type": "main", "index": 0}], [{"node": "Merge <PERSON> Threads", "type": "main", "index": 0}]]}, "Create Competitors in DB": {"main": [[{"node": "Bundle Competitor Data to 1 object", "type": "main", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "Check if Integration Data Found", "type": "main", "index": 0}, {"node": "Check if Competitor Data Found", "type": "main", "index": 0}, {"node": "Check if objection found", "type": "main", "index": 0}, {"node": "Update Notion Call object with AI data", "type": "main", "index": 0}, {"node": "Check for use cases", "type": "main", "index": 0}]]}, "Merge Integration Thread": {"main": [[]]}, "Split Out all objections": {"main": [[{"node": "Format Tag Data for notion", "type": "main", "index": 0}]]}, "Split Out Competitor Data": {"main": [[{"node": "Create Competitors in DB", "type": "main", "index": 0}]]}, "Format Tag Data for notion": {"main": [[{"node": "Bundle all Tags", "type": "main", "index": 0}]]}, "Split Out Integration data": {"main": [[{"node": "Create Integrations", "type": "main", "index": 0}]]}, "Add Objection Tag to Notion": {"main": [[{"node": "Wait for rate limiting again", "type": "main", "index": 0}]]}, "Wait for rate limiting again": {"main": [[{"node": "Update Call with Objection Summary", "type": "main", "index": 0}]]}, "Get AI Response - Competitors": {"main": [[{"node": "Split Out Competitor Data", "type": "main", "index": 0}]]}, "Check if Competitor Data Found": {"main": [[{"node": "Wait for rate limiting - Competitors", "type": "main", "index": 0}], [{"node": "Merge Competitor Thread", "type": "main", "index": 0}]]}, "Check if Integration Data Found": {"main": [[{"node": "Wait for rate limiting - Integration", "type": "main", "index": 0}], [{"node": "Merge Integration Thread", "type": "main", "index": 0}]]}, "Bundle Use Case Data to 1 object": {"main": [[{"node": "Merge Use Case Thread", "type": "main", "index": 0}]]}, "Bundle Competitor Data to 1 object": {"main": [[{"node": "Merge Competitor Thread", "type": "main", "index": 0}]]}, "Update Call with Objection Summary": {"main": [[{"node": "Merge <PERSON> Threads", "type": "main", "index": 0}]]}, "Wait for rate limiting - Objection": {"main": [[{"node": "Split Out all objections", "type": "main", "index": 0}]]}, "Wait for rate limiting - Use Cases": {"main": [[{"node": "Split Out Use Cases", "type": "main", "index": 0}]]}, "Bundle Integration Data to 1 object": {"main": [[{"node": "Merge Integration Thread", "type": "main", "index": 0}]]}, "Wait for rate limiting - Competitors": {"main": [[{"node": "Get AI Response - Competitors", "type": "main", "index": 0}]]}, "Wait for rate limiting - Integration": {"main": [[{"node": "Split Out Integration data", "type": "main", "index": 0}]]}}}