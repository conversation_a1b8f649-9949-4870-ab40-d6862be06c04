{"id": "ZdGZh4qmOqTQe1oq", "meta": {"instanceId": "da824ad45fda1b156c8390a3c35cdfbb10059e671c074c19429dac59c5ae98f6"}, "name": "MONDAY GET FULL ITEM", "tags": [{"id": "uKg1PU2D27Vsr8ud", "name": "MONDAY", "createdAt": "2023-12-05T07:54:13.266Z", "updatedAt": "2023-12-05T07:54:13.266Z"}], "nodes": [{"id": "20299349-bc2c-4aa8-b083-db31cb9aa278", "name": "GET ALL COLUMNS", "type": "n8n-nodes-base.code", "position": [1840, -600], "parameters": {"jsCode": "function createColumnValuesArray(data) {\n  const result = {};\n  data.forEach(item => {\n    const name = item.id;\n    result[name] = item;\n  });\n\n  return result;\n}\n\ncolumns = $input.last().json.column_values\ndata1 = { \"name\" : $input.last().json.name, \"id\" : $input.last().json.id }\ndata2 = createColumnValuesArray(columns)\n\nconst combinedData = { \"item\" : data1, columnValuesById: data2}\n\nreturn (combinedData)\n\n\n"}, "typeVersion": 2}, {"id": "04c2550e-41d8-46f4-a131-2ea99dd4258a", "name": "GET ALL RELATIONS", "type": "n8n-nodes-base.code", "position": [1860, -220], "parameters": {"jsCode": "var data = $input.last().json.columnValuesById;\ni = 0;\nrelations = [];\nfor (var key in data) {\n    if (data[key].type == \"board_relation\") {\n      relations[i] = data[key];\n      i++\n    }\n}\n\nreturn relations;\n\n"}, "typeVersion": 2}, {"id": "5796cb17-199b-4838-ae9c-c3636824bd13", "name": "PULL LINKEDPULSE1", "type": "n8n-nodes-base.mondayCom", "position": [1720, -40], "parameters": {"itemId": "=\n{{ $json.linkedPulse.linkedPulseId }}", "resource": "boardItem", "operation": "get"}, "credentials": {"mondayComApi": {"id": "5nd48DKapWBLcUBx", "name": "Monday.com account"}}, "notesInFlow": true, "typeVersion": 1}, {"id": "67a8a151-5875-4ec7-8fda-f797f3d3b198", "name": "GET LINKEDPULSES1", "type": "n8n-nodes-base.code", "position": [1340, -40], "parameters": {"mode": "runOnceForEachItem", "jsCode": "data = $input.item.json.value\nid = $input.item.json.id\nname = $input.item.json.column.title\n\nconst linkedPulseID = JSON.parse(data).linkedPulseIds\n\nreturn { \"linkedPulse\": linkedPulseID, \"id\" : id, \"name\": name }\n"}, "typeVersion": 2}, {"id": "5dbe451d-ec23-48bf-9193-55a03b8752a4", "name": "SPLIT LINKED PULSES1", "type": "n8n-nodes-base.splitOut", "position": [1540, -40], "parameters": {"include": "=", "options": {}, "fieldToSplitOut": "linkedPulse"}, "typeVersion": 1}, {"id": "536897b1-ed71-4888-9761-cb4a363f0a86", "name": "SPLIT SUBITEMS1", "type": "n8n-nodes-base.splitOut", "position": [1540, 200], "parameters": {"include": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options": {}, "fieldToSplitOut": "linkedPulseIds", "fieldsToInclude": "linkedPulseIds[0].linkedPulseId"}, "typeVersion": 1}, {"id": "57777d0c-77d0-4652-a798-2d347b12cfb4", "name": "GET EACH SUBITEM1", "type": "n8n-nodes-base.mondayCom", "position": [1700, 200], "parameters": {"itemId": "=\n{{ $json.linkedPulseIds.linkedPulseId }}", "resource": "boardItem", "operation": "get"}, "credentials": {"mondayComApi": {"id": "5nd48DKapWBLcUBx", "name": "Monday.com account"}}, "notesInFlow": true, "typeVersion": 1}, {"id": "1a7574db-16bb-4d69-b91f-33b20e52c794", "name": "GET ALL COLUMNS1", "type": "n8n-nodes-base.code", "position": [1880, 200], "parameters": {"mode": "runOnceForEachItem", "jsCode": "function createColumnValuesArray(data) {\n  const result = {};\n  data.forEach(item => {\n    const name = item.id;\n    result[name] = item;\n  });\n\n  return result;\n}\n\ncolumns = $input.item.json.column_values\ndata1 = { \"name\" : $input.item.json.name, \"id\" : $input.item.json.id }\ndata2 = createColumnValuesArray(columns)\n\nconst combinedData = { ...data1, ...data2 }\n\nreturn (combinedData)\n\n\n"}, "typeVersion": 2}, {"id": "ba95aef3-49b3-4a3e-a5fd-51ec04691949", "name": "GET ALL COLUMNS2", "type": "n8n-nodes-base.code", "position": [1840, -420], "parameters": {"jsCode": "function createColumnValuesArray(data) {\n  const result = {};\n  data.forEach(item => {\n  if (item.type != \"subtasks\") {\n    const name = item.column.title;\n    result[name] = item;\n  }\n  });\n\n  return result;\n}\n\ncolumns = $input.last().json.column_values\ndata = createColumnValuesArray(columns)\nreturn {\"columnValuesByName\": data}\n\n\n"}, "typeVersion": 2}, {"id": "8c475537-efe6-4417-b293-e47abe817f7a", "name": "Aggregate1", "type": "n8n-nodes-base.aggregate", "onError": "continueRegularOutput", "position": [2180, 100], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "subitems"}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "f96f5fb7-1701-4cf4-b572-2ed3d8376232", "name": "PULL SUBITEMS", "type": "n8n-nodes-base.code", "position": [1320, 200], "parameters": {"jsCode": "//Search for \"Subitems\" column\nconst columnName = \"Subitems\"\nfunction getColumnValue(item, columnId) {\n    const column = item.column_values.find(column => column.column.title === columnId);\n    if (column) {\n          return column\n    } else {\n        return null;\n    }\n}\nconst columnValue = getColumnValue($input.last().json, columnName);\nreturn JSON.parse(columnValue.value);\n\n//ALT OPTION - direct access by column_values[0]\n//var ids = $input.last().json['column_values'][0]['value'];\n//return JSON.parse(ids)"}, "typeVersion": 2}, {"id": "aa96e7e9-6c2a-46d4-95af-124609a7b524", "name": "GET ITEM", "type": "n8n-nodes-base.mondayCom", "position": [1180, -600], "parameters": {"itemId": "=\n{{ $input.item.json.pulse }}", "resource": "boardItem", "operation": "get"}, "credentials": {"mondayComApi": {"id": "5nd48DKapWBLcUBx", "name": "Monday.com account"}}, "notesInFlow": true, "typeVersion": 1}, {"id": "da23cad1-77f9-4035-8ad3-b322dadba853", "name": "GET ALL COLUMNS3", "type": "n8n-nodes-base.code", "position": [1880, -40], "parameters": {"mode": "runOnceForEachItem", "jsCode": "function createColumnValuesArray(data) {\n  const result = {};\n  data.forEach(item => {\n    const name = item.id;\n    result[name] = item;\n  });\n\n  return result;\n}\n\ncolumns = $input.item.json.column_values\ndata1 = { \"name\" : $input.item.json.name, \"id\" : $input.item.json.id }\ndata2 = createColumnValuesArray(columns)\n\nconst combinedData = { \"item\" : data1, columnValuesById: data2}\n\nreturn (combinedData)\n\n\n"}, "typeVersion": 2}, {"id": "9c27b7af-2568-4b07-b526-9c18ca52649f", "name": "Merge4", "type": "n8n-nodes-base.merge", "position": [2180, -100], "parameters": {"mode": "combine", "options": {}, "combinationMode": "mergeByPosition"}, "typeVersion": 2.1, "alwaysOutputData": true}, {"id": "6f0008fd-b8f5-4161-8fbf-363b3d5a7794", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "onError": "continueRegularOutput", "position": [2340, -100], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "boardrelations"}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "d95b1e2a-405e-417b-8618-89af85b10350", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "onError": "continueRegularOutput", "position": [2540, 0], "parameters": {"mode": "combine", "options": {}, "combinationMode": "mergeByPosition"}, "typeVersion": 2.1, "alwaysOutputData": true}, {"id": "e9d7977d-1fd3-4be8-ad90-b42a93bc1ea4", "name": "Merge2", "type": "n8n-nodes-base.merge", "onError": "continueRegularOutput", "position": [2480, -240], "parameters": {"mode": "combine", "options": {}, "combinationMode": "mergeByPosition"}, "typeVersion": 2.1, "alwaysOutputData": true}, {"id": "72e04613-f953-49b0-ad13-4bd5464cc55e", "name": "Merge1", "type": "n8n-nodes-base.merge", "position": [2680, -160], "parameters": {"mode": "combine", "options": {}, "combinationMode": "mergeByPosition"}, "typeVersion": 2.1}, {"id": "0846e8ee-8b58-40c2-8d3f-1e33f519bf55", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [980, -600], "parameters": {}, "typeVersion": 1}, {"id": "1c753c05-3541-4579-a815-b3465f26d51c", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1300, -240], "parameters": {"width": 752.1995067108865, "height": 335.74971164936585, "content": "PULL ALL BOARDRELATION COLUMNS AND THEIR DATA"}, "typeVersion": 1}, {"id": "79c61e1f-6cbf-45ca-b0d3-31250fb7be18", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1300, 120], "parameters": {"width": 748.2468880082052, "height": 237.44804034647325, "content": "PULL ALL SUBITEMS AND THEIR COLUMN DATA\n"}, "typeVersion": 1}, {"id": "b5da95d7-d0d8-4ad1-ab93-e06e6c823ed2", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1720, -640], "parameters": {"color": 4, "width": 325.58246828143024, "height": 352.5605536332179, "content": "PULL ALL COLUMN DATA AND INDEX BY ID AND NAME\n"}, "typeVersion": 1}, {"id": "02125cbf-aa28-4cf1-a0ef-cf3cf45e76c2", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [2140, -298.05978270268713], "parameters": {"color": 5, "width": 677.0818915801614, "height": 605.5742002344051, "content": "COMBINE ALL DATA INTO ONE JSON OUTPUT\n"}, "typeVersion": 1}, {"id": "e96deeef-6fb5-4130-b422-752e0e0dc9c5", "name": "Execute Workflow", "type": "n8n-nodes-base.executeWorkflow", "position": [1180, -780], "parameters": {"options": {"waitForSubWorkflow": true}, "workflowId": "ZdGZh4qmOqTQe1oq"}, "typeVersion": 1}, {"id": "955d8a6e-931c-411f-a26a-17f547370fd9", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [980, -780], "parameters": {"fields": {"values": [{"name": "pulse", "stringValue": "4030768878"}]}, "options": {}}, "typeVersion": 3.2}, {"id": "f508d0cd-448c-482e-9eeb-d569f26dbaab", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [940, -920], "parameters": {"color": 6, "width": 418.4714893828877, "height": 302.08861782546296, "content": "HOW TO USE\n-Copy these nodes into another workflow, and update the workflow id in the execute workflow node\n-Using the Edit Fields nodes, define the “pulse” variable which will tell the workflow which monday item to pull data from.\n"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "dd22e2e2-0699-41d1-b6ad-001073624540", "connections": {"Merge": {"main": [[{"node": "Merge1", "type": "main", "index": 1}]]}, "Merge2": {"main": [[{"node": "Merge1", "type": "main", "index": 0}]]}, "Merge4": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "GET ITEM": {"main": [[{"node": "GET ALL COLUMNS", "type": "main", "index": 0}, {"node": "GET ALL COLUMNS2", "type": "main", "index": 0}, {"node": "PULL SUBITEMS", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Aggregate1": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Edit Fields": {"main": [[{"node": "Execute Workflow", "type": "main", "index": 0}]]}, "PULL SUBITEMS": {"main": [[{"node": "SPLIT SUBITEMS1", "type": "main", "index": 0}]]}, "GET ALL COLUMNS": {"main": [[{"node": "GET ALL RELATIONS", "type": "main", "index": 0}, {"node": "Merge2", "type": "main", "index": 0}]]}, "SPLIT SUBITEMS1": {"main": [[{"node": "GET EACH SUBITEM1", "type": "main", "index": 0}]]}, "GET ALL COLUMNS1": {"main": [[{"node": "Aggregate1", "type": "main", "index": 0}]]}, "GET ALL COLUMNS2": {"main": [[{"node": "Merge2", "type": "main", "index": 1}]]}, "GET ALL COLUMNS3": {"main": [[{"node": "Merge4", "type": "main", "index": 1}]]}, "GET ALL RELATIONS": {"main": [[{"node": "GET LINKEDPULSES1", "type": "main", "index": 0}, {"node": "Merge4", "type": "main", "index": 0}]]}, "GET EACH SUBITEM1": {"main": [[{"node": "GET ALL COLUMNS1", "type": "main", "index": 0}]]}, "GET LINKEDPULSES1": {"main": [[{"node": "SPLIT LINKED PULSES1", "type": "main", "index": 0}]]}, "PULL LINKEDPULSE1": {"main": [[{"node": "GET ALL COLUMNS3", "type": "main", "index": 0}]]}, "SPLIT LINKED PULSES1": {"main": [[{"node": "PULL LINKEDPULSE1", "type": "main", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "GET ITEM", "type": "main", "index": 0}]]}}}