{"nodes": [{"id": "41e0d0a9-9bd4-4ece-a204-5e1bf507b0eb", "meta": {"instanceId": "cb9c144f2050b3f9b30bf379399398f9061341e3665eb2faf2b1092a42b38b14"}, "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [820, 400], "parameters": {}, "typeVersion": 1}, {"id": "aa373efa-d493-44cd-91ee-e07630309675", "name": "Customer Datastore (n8n training)", "type": "n8n-nodes-base.n8nTrainingCustomerDatastore", "position": [1040, 400], "parameters": {"operation": "getAllPeople"}, "typeVersion": 1}, {"id": "29555ae0-ad6c-4888-8865-c1e097b3b44e", "name": "Set", "type": "n8n-nodes-base.set", "position": [1260, 400], "parameters": {"values": {"number": [{"name": "itemCount", "value": "={{ $input.all().length }}"}]}, "options": {}, "keepOnlySet": true}, "executeOnce": true, "typeVersion": 1}], "connections": {"When clicking \"Execute Workflow\"": {"main": [[{"node": "Customer Datastore (n8n training)", "type": "main", "index": 0}]]}, "Customer Datastore (n8n training)": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}}