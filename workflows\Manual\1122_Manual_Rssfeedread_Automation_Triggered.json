{"id": "7604ck94MeYXMHpN", "meta": {"instanceId": "bd0e051174def82b88b5cd547222662900558d74b239c4048ea0f6b7ed61c642"}, "name": "Read RSS feed from two different sources", "tags": [], "nodes": [{"id": "fa8717e5-092a-4359-89cc-57cc8fa2bf25", "name": "RSS Feed Read", "type": "n8n-nodes-base.rssFeedRead", "position": [1080, 180], "parameters": {"url": "={{ $json.url }}", "options": {}}, "typeVersion": 1}, {"id": "62ce6cf3-fb83-4013-b288-40d179f35f99", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [520, 100], "parameters": {}, "typeVersion": 1}, {"id": "81496a04-b986-4e13-b884-23562f953a37", "name": "Code", "type": "n8n-nodes-base.code", "position": [700, 100], "parameters": {"jsCode": "return [\n  {\n    json: {\n      url: 'https://medium.com/feed/n8n-io',\n    }\n  },\n  {\n    json: {\n      url: 'https://dev.to/feed/n8n',\n    }\n  }\n];"}, "typeVersion": 1}, {"id": "6e3a444f-fec3-4a7f-a063-d5b152c5f43a", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [880, 100], "parameters": {"options": {}}, "typeVersion": 3}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "8ad423d4-cf25-4b30-85c0-c50a26238e81", "connections": {"Code": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "RSS Feed Read": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "RSS Feed Read", "type": "main", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}}}