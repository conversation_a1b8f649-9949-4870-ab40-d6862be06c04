{"id": "1U5Jf4NMQEw9LtxY", "meta": {"instanceId": "885b4fb4a6a9c2cb5621429a7b972df0d05bb724c20ac7dac7171b62f1c7ef40"}, "name": "Capture Website Screenshots with Bright Data Web Unlocker and Save to Disk", "tags": [{"id": "Kujft2FOjmOVQAmJ", "name": "Engineering", "createdAt": "2025-04-09T01:31:00.558Z", "updatedAt": "2025-04-09T01:31:00.558Z"}], "nodes": [{"id": "d61cb066-1d5f-47d5-a4dd-4534f3d3c6d8", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-520, -160], "parameters": {}, "typeVersion": 1}, {"id": "eb99305b-0375-4cdd-8682-637d281598a0", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-540, -500], "parameters": {"width": 360, "height": 260, "content": "## Note\n\nThe \"**Set URL, Filename and Bright Data Zone**\" node must be updated with the appropriate url, file name and **Bright Data Proxies & Infrastructure** zone.\n\nThe \"**Write a file to disk**\" node has the location to download the website screenshot. Please make sure to set the path"}, "typeVersion": 1}, {"id": "205f64e9-5b31-4c76-912a-307eccde159e", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-160, -240], "parameters": {"color": 4, "width": 260, "height": 280, "content": "## Website Screenshot"}, "typeVersion": 1}, {"id": "e7705941-2ae8-4c38-93cb-2cb865314872", "name": "Write a file to disk", "type": "n8n-nodes-base.readWriteFile", "position": [140, -160], "parameters": {"options": {}, "fileName": "={{ \"c:\\\\\"+ $json.filename }}", "operation": "write", "dataPropertyName": "={{ $json.filename }}"}, "typeVersion": 1}, {"id": "167ff255-da5b-43c1-a22f-e00c4cc166d8", "name": "Capture a screenshot", "type": "n8n-nodes-base.httpRequest", "position": [-80, -160], "parameters": {"url": "https://api.brightdata.com/request", "method": "POST", "options": {"response": {"response": {"responseFormat": "file", "outputPropertyName": "={{ $json.filename }}"}}, "allowUnauthorizedCerts": true}, "sendBody": true, "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "zone", "value": "={{ $json.zone }}"}, {"name": "url", "value": "={{ $json.url }}"}, {"name": "format", "value": "raw"}, {"name": "data_format", "value": "screenshot"}]}, "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{}]}}, "credentials": {"httpHeaderAuth": {"id": "kdbqXuxIR8qIxF7y", "name": "Header Auth account"}}, "typeVersion": 4.2}, {"id": "1c5c3d72-f20d-4d06-a6f2-461d043c4a01", "name": "Set URL, Filename and Bright Data Zone", "type": "n8n-nodes-base.set", "position": [-300, -160], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c9de0c3e-609a-4e87-b6ab-b4312be026a9", "name": "url", "type": "string", "value": "https://dev.to/"}, {"id": "408ed65a-0d66-4f98-b2eb-0d5e066e3250", "name": "filename", "type": "string", "value": "devto.png"}, {"id": "ee10fcb0-a610-4987-8a4e-dfab077aee0e", "name": "zone", "type": "string", "value": "web_unlocker1"}]}}, "typeVersion": 3.4}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "d3ae63f2-efcf-478b-aadf-8a3fac2af02a", "connections": {"Capture a screenshot": {"main": [[{"node": "Write a file to disk", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set URL, Filename and Bright Data Zone", "type": "main", "index": 0}]]}, "Set URL, Filename and Bright Data Zone": {"main": [[{"node": "Capture a screenshot", "type": "main", "index": 0}]]}}}