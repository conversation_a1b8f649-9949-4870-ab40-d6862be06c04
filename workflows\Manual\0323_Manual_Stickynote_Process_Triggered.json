{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "1116cae7-c7f3-424d-8b87-06ecbac0539f", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [1040, -260], "parameters": {}, "typeVersion": 1}, {"id": "c01d02c0-a41b-445e-b006-8b46ad1c437d", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [2000, 260], "parameters": {"height": 264.69900963477494, "content": "### Parser which defines the output format and which gets used to validate the output"}, "typeVersion": 1}, {"id": "97f977e2-eb78-4ad9-ab21-816ff94c8f0c", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1600, 260], "parameters": {"height": 266.9506012398238, "content": "### The LLM which gets used to try to autofix the output in case it was not valid"}, "typeVersion": 1}, {"id": "5325a0d4-9422-445c-bd21-3290c2b14415", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1320, -40], "parameters": {"height": 245.56048099185898, "content": "### The LLM to process the original prompt"}, "typeVersion": 1}, {"id": "55e78fdb-1e08-4f13-be0d-7e476aced21b", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1740, -40], "parameters": {"width": 348, "height": 253, "content": "### Autofixing parser which tries to fix invalid outputs with the help of an LLM"}, "typeVersion": 1}, {"id": "622183c2-9d57-4e1c-a7bd-c5320ef42668", "name": "Basic LLM Chain", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1480, -260], "parameters": {"hasOutputParser": true}, "typeVersion": 1.5}, {"id": "314739fe-0ab3-40a1-b192-6e09b548b92f", "name": "Prompt", "type": "n8n-nodes-base.set", "position": [1260, -260], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "6f09dac7-429c-4e8f-af32-8e0112efc8c2", "name": "chatInput", "type": "string", "value": "Return the 5 largest states by area in the USA with their 3 largest cities and their population."}]}}, "typeVersion": 3.4}, {"id": "e76f5ac7-e185-46d4-aa26-971c8fe03c76", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1400, 60], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "5306e68a-cce0-4298-a50a-33727e2186c5", "name": "Auto-fixing Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserAutofixing", "position": [1800, 80], "parameters": {"options": {"prompt": "Instructions:\n--------------\n{instructions}\n--------------\nCompletion:\n--------------\n{completion}\n--------------\n\nAbove, the Completion did not satisfy the constraints given in the Instructions.\nError:\n--------------\n{error}\n--------------\n\nPlease try again. Please only respond with an answer that satisfies the constraints laid out in the Instructions:"}}, "typeVersion": 1}, {"id": "d5642767-69f6-4a09-92da-195a25a17dd1", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1680, 400], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "dc708b80-8d48-40cb-9af3-692ddd566b9f", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [2080, 380], "parameters": {"schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"state\": {\n      \"type\": \"string\"\n    },\n    \"cities\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"name\": \"string\",\n          \"population\": \"number\"\n        }\n      }\n    }\n  }\n}"}, "typeVersion": 1.2}], "pinData": {}, "connections": {"Prompt": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Auto-fixing Output Parser", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Auto-fixing Output Parser", "type": "ai_outputParser", "index": 0}]]}, "Auto-fixing Output Parser": {"ai_outputParser": [[{"node": "Basic LLM Chain", "type": "ai_outputParser", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "Prompt", "type": "main", "index": 0}]]}}}