{"meta": {"instanceId": "27b4a6a8d6961d7c3fc76935cbb847cc60b06fde7d9f2077fe73e1a9efa7a010"}, "nodes": [{"id": "cfb41f0c-9dd3-46c8-aae1-2f6caaf1a1e3", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-2440, 220], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.2}, {"id": "07d78dcb-1a2d-45f4-b595-734e301c25ee", "name": "<PERSON>", "type": "n8n-nodes-base.set", "onError": "continueRegularOutput", "position": [-1440, 220], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "f3530e8d-0694-4b73-bd9f-f4ce763c059b", "name": "id", "type": "string", "value": "={{ $json.link }}"}, {"id": "e829100d-7301-4ee3-9e8e-782b476b98c3", "name": "title", "type": "string", "value": "={{ $json.title }}"}, {"id": "637000e7-a294-4656-b3b2-36d3ff42ce8d", "name": "output", "type": "string", "value": "={{ $json.content }}"}, {"id": "6626b922-4ac9-4a04-a55d-d02cebeee7f2", "name": "pubDate", "type": "string", "value": "={{ $json.pubDate }}"}, {"id": "134b45eb-3048-40c8-9c1c-2b9d45959de4", "name": "tags", "type": "string", "value": "={{ $json.categories }}"}]}}, "typeVersion": 3.4}, {"id": "7c7d0915-dfc2-4041-ae0f-6af6e008eab1", "name": "Code", "type": "n8n-nodes-base.code", "onError": "continueRegularOutput", "position": [-1180, 220], "parameters": {"jsCode": "const now = new Date();\nconst setdays = 3; // Edit the Days, if you need the News from more the 3 Days\nconst cutoffDate = new Date();\ncutoffDate.setDate(now.getDate() - setdays); \nreturn $input.all().filter(item => {\n    const pubDate = new Date(Date.parse(item.json.pubDate));\n    return !isNaN(pubDate.getTime()) && pubDate >= cutoffDate;\n});"}, "typeVersion": 2}, {"id": "d5ac9f75-60a4-4bde-b4c1-ccb2f940d5f8", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.markdown", "onError": "continueRegularOutput", "position": [-900, 460], "parameters": {"html": "={{ $json.output }}", "options": {}, "destinationKey": "output"}, "typeVersion": 1}, {"id": "e7ec484d-f667-4123-acb4-60e0cbdb62e0", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "onError": "continueRegularOutput", "position": [-920, 220], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "0065ce76-9840-48b2-860c-c4a2928479a8", "name": "Wait", "type": "n8n-nodes-base.wait", "onError": "continueRegularOutput", "position": [-900, 880], "webhookId": "85941a4b-202f-4368-a331-dbfdf018326b", "parameters": {"amount": 2.5}, "typeVersion": 1.1, "alwaysOutputData": true}, {"id": "06edc173-5352-4810-bc9f-cc24cc263ee6", "name": "Loop Over Items1", "type": "n8n-nodes-base.splitInBatches", "onError": "continueRegularOutput", "position": [-1820, 220], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "069bc633-9273-471c-a2c7-1559c62eb370", "name": "RSS", "type": "n8n-nodes-base.rssFeedRead", "onError": "continueRegularOutput", "position": [-1800, 480], "parameters": {"url": "={{ $json.Links }}", "options": {}}, "typeVersion": 1.1}, {"id": "6575e570-0a1b-49db-b0b1-938dd2732dd6", "name": "Code1", "type": "n8n-nodes-base.code", "onError": "continueRegularOutput", "position": [-100, 220], "parameters": {"jsCode": "const now = new Date();\nconst setdays = 3; // Edit the Days, if you need the News from more the 3 Days\nconst cutoffDate = new Date();\ncutoffDate.setDate(now.getDate() - setdays);  \n\nconst oldRows = $input.all().filter(item => {\n    const pubDate = new Date(item.json.pubDate);\n    return pubDate < cutoffDate;\n});\noldRows.sort((a, b) => b.json.row_number - a.json.row_number);\nreturn oldRows.map(item => ({ json: { rowNumber: item.json.row_number } }));\n"}, "typeVersion": 2}, {"id": "d68a46d3-58ea-4cc7-a1f7-ef014f600908", "name": "Loop Over Items2", "type": "n8n-nodes-base.splitInBatches", "onError": "continueRegularOutput", "position": [240, 220], "parameters": {"options": {}}, "typeVersion": 3, "alwaysOutputData": true}, {"id": "ed132a67-67fa-42e1-9f73-0b645005a332", "name": "Wait1", "type": "n8n-nodes-base.wait", "onError": "continueRegularOutput", "position": [260, 680], "webhookId": "69b1b17d-85d1-4681-8887-85e2644f4752", "parameters": {"amount": 25}, "typeVersion": 1.1}, {"id": "61d7aae8-2d3f-4425-9e4b-247aa5fd2cea", "name": "Wait2", "type": "n8n-nodes-base.wait", "onError": "continueRegularOutput", "position": [-660, 220], "webhookId": "69b1b17d-85d1-4681-8887-85e2644f4752", "parameters": {"unit": "minutes", "amount": 1}, "executeOnce": true, "typeVersion": 1.1}, {"id": "8cf26c46-a2e1-4ea0-8c8b-a948bb77e286", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-2480, -120], "parameters": {"width": 500, "height": 1340, "content": "## Timer starts the Update every 24 hours and Read the Links out of a Google Sheets File (RSS-Links)"}, "typeVersion": 1}, {"id": "b7eb361f-7ff1-436c-82d1-0c348e652a26", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-1960, -120], "parameters": {"width": 440, "height": 1340, "content": "## Each individual link is scanned and retrieved"}, "typeVersion": 1}, {"id": "2037525d-afbe-4aba-9dd4-b00df8560706", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-1500, -120], "parameters": {"width": 480, "height": 1340, "content": "## Everything older than 3 days is removed"}, "typeVersion": 1}, {"id": "0e7eff1a-10e0-4f7a-8e2d-4046248878bc", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-1000, -120], "parameters": {"width": 300, "height": 1340, "content": "## Each entry is saved individually with a waiting time in the Google Sheets file (RSS-Feeds), the waiting time is necessary as Google Sheets would otherwise receive too many hits and block access!"}, "typeVersion": 1}, {"id": "8cd16f67-ebc8-4009-81a1-54da9aac47ef", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-680, -120], "parameters": {"width": 420, "height": 1340, "content": "## Reading the saved entries in the Google Sheets file (RSS-Feeds)"}, "typeVersion": 1}, {"id": "ed9d8dc7-e8e7-4441-a43c-8c86a7e0be52", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-240, -120], "parameters": {"width": 360, "height": 1340, "content": "## Everything that is younger than 3 days will be removed, as we only want to delete the older entries!"}, "typeVersion": 1}, {"id": "fbb3391c-bac7-4e2c-8231-a8f48d70c21c", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [140, -120], "parameters": {"width": 360, "height": 1340, "content": "## All entries older than 3 days are deleted here, again with a timer to prevent a Google API block! (RSS-Feeds)"}, "typeVersion": 1}, {"id": "3a800065-971b-4f37-bc3f-9c8ade78217e", "name": "Delete News", "type": "n8n-nodes-base.googleSheets", "onError": "continueRegularOutput", "position": [260, 460], "parameters": {"operation": "delete", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1iFwBIRDfUEZFACoL4bXfeT4Ot2i5vWfEew69fYRfz0A/edit#gid=0", "cachedResultName": "Tabellenblatt1"}, "documentId": {"__rl": true, "mode": "list", "value": "1iFwBIRDfUEZFACoL4bXfeT4Ot2i5vWfEew69fYRfz0A", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1iFwBIRDfUEZFACoL4bXfeT4Ot2i5vWfEew69fYRfz0A/edit?usp=drivesdk", "cachedResultName": "RSS-Feeds"}, "startIndex": "={{ $json.rowNumber }}"}, "credentials": {"googleSheetsOAuth2Api": {"id": "pmmVpF25NsJia8r0", "name": "Google Sheets account"}}, "typeVersion": 4.5, "alwaysOutputData": false}, {"id": "31d13f16-c9ad-4b08-b129-bb5ed51d4657", "name": "Read News", "type": "n8n-nodes-base.googleSheets", "onError": "continueErrorOutput", "position": [-440, 220], "parameters": {"options": {"outputFormatting": {"values": {"date": "FORMATTED_STRING", "general": "FORMATTED_VALUE"}}, "dataLocationOnSheet": {"values": {"rangeDefinition": "detectAutomatically"}}}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1iFwBIRDfUEZFACoL4bXfeT4Ot2i5vWfEew69fYRfz0A/edit#gid=0", "cachedResultName": "Tabellenblatt1"}, "documentId": {"__rl": true, "mode": "list", "value": "1iFwBIRDfUEZFACoL4bXfeT4Ot2i5vWfEew69fYRfz0A", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1iFwBIRDfUEZFACoL4bXfeT4Ot2i5vWfEew69fYRfz0A/edit?usp=drivesdk", "cachedResultName": "RSS-Feeds"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "pmmVpF25NsJia8r0", "name": "Google Sheets account"}}, "typeVersion": 4.5, "alwaysOutputData": false}, {"id": "13bb0584-96bb-4184-9698-509b34f6be25", "name": "Save News", "type": "n8n-nodes-base.googleSheets", "onError": "continueRegularOutput", "position": [-900, 680], "parameters": {"columns": {"value": {"id": "={{ $json.id }}", "title": "={{ $json.title }}", "output": "={{ $json.output }}", "pubDate": "={{ $json.pubDate }}", "Category": "={{ $json.tags }}"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "id", "defaultMatch": true, "canBeUsedToMatch": true}, {"id": "title", "type": "string", "display": true, "required": false, "displayName": "title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "output", "type": "string", "display": true, "required": false, "displayName": "output", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "pubDate", "type": "string", "display": true, "required": false, "displayName": "pubDate", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Category", "type": "string", "display": true, "required": false, "displayName": "Category", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["id"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {"useAppend": true}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1iFwBIRDfUEZFACoL4bXfeT4Ot2i5vWfEew69fYRfz0A/edit#gid=0", "cachedResultName": "Tabellenblatt1"}, "documentId": {"__rl": true, "mode": "list", "value": "1iFwBIRDfUEZFACoL4bXfeT4Ot2i5vWfEew69fYRfz0A", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1iFwBIRDfUEZFACoL4bXfeT4Ot2i5vWfEew69fYRfz0A/edit?usp=drivesdk", "cachedResultName": "RSS-Feeds"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "pmmVpF25NsJia8r0", "name": "Google Sheets account"}}, "typeVersion": 4.5, "alwaysOutputData": true}, {"id": "d3224fc9-d7a7-47c1-9db2-8615499e1124", "name": "Read Links", "type": "n8n-nodes-base.googleSheets", "onError": "continueErrorOutput", "position": [-2200, 220], "parameters": {"options": {"outputFormatting": {"values": {"date": "FORMATTED_STRING", "general": "FORMATTED_VALUE"}}, "dataLocationOnSheet": {"values": {"rangeDefinition": "detectAutomatically"}}}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/12p3M0Umh_Xlpm4Y04IpOFqE8YOJcCd97wPJNv80X8u4/edit#gid=0", "cachedResultName": "Tabellenblatt1"}, "documentId": {"__rl": true, "mode": "list", "value": "12p3M0Umh_Xlpm4Y04IpOFqE8YOJcCd97wPJNv80X8u4", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/12p3M0Umh_Xlpm4Y04IpOFqE8YOJcCd97wPJNv80X8u4/edit?usp=drivesdk", "cachedResultName": "RSS-Links"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "pmmVpF25NsJia8r0", "name": "Google Sheets account"}}, "typeVersion": 4.5}], "pinData": {}, "connections": {"RSS": {"main": [[{"node": "Loop Over Items1", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Code1": {"main": [[{"node": "Loop Over Items2", "type": "main", "index": 0}]]}, "Wait1": {"main": [[{"node": "Loop Over Items2", "type": "main", "index": 0}]]}, "Wait2": {"main": [[{"node": "Read News", "type": "main", "index": 0}]]}, "Markdown": {"main": [[{"node": "Save News", "type": "main", "index": 0}]]}, "Read News": {"main": [[{"node": "Code1", "type": "main", "index": 0}], []]}, "Save News": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Read Links": {"main": [[{"node": "Loop Over Items1", "type": "main", "index": 0}], []]}, "Delete News": {"main": [[{"node": "Wait1", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Wait2", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Loop Over Items1": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}], [{"node": "RSS", "type": "main", "index": 0}]]}, "Loop Over Items2": {"main": [[], [{"node": "Delete News", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Read Links", "type": "main", "index": 0}]]}}}