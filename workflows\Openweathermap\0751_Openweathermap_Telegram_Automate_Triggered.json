{"id": "2", "name": "Telegram Weather Workflow", "nodes": [{"name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "position": [270, 220], "parameters": {"updates": ["message"]}, "credentials": {"telegramApi": "Telegram"}, "typeVersion": 1}, {"name": "OpenWeatherMap", "type": "n8n-nodes-base.openWeatherMap", "position": [480, 220], "parameters": {"cityName": "berlin,de"}, "credentials": {"openWeatherMapApi": "OpenWeatherMap"}, "typeVersion": 1}, {"name": "Telegram", "type": "n8n-nodes-base.telegram", "position": [670, 220], "parameters": {"text": "=Right now, we have {{$node[\"OpenWeatherMap\"].json[\"weather\"][0][\"description\"]}}. The temperature is {{$node[\"OpenWeatherMap\"].json[\"main\"][\"temp\"]}}°C but it really feels like {{$node[\"OpenWeatherMap\"].json[\"main\"][\"feels_like\"]}}°C 🙂", "chatId": "={{$node[\"Telegram Trigger\"].json[\"message\"][\"chat\"][\"id\"]}}", "additionalFields": {}}, "credentials": {"telegramApi": "Telegram"}, "typeVersion": 1}], "active": true, "settings": {}, "connections": {"OpenWeatherMap": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "Telegram Trigger": {"main": [[{"node": "OpenWeatherMap", "type": "main", "index": 0}]]}}}