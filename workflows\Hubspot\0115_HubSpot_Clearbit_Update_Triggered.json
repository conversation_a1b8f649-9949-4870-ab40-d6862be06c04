{"nodes": [{"name": "Clearbit", "type": "n8n-nodes-base.clearbit", "position": [850, 300], "parameters": {"email": "={{$json[\"properties\"][\"email\"][\"value\"]}}", "resource": "person", "additionalFields": {}}, "credentials": {"clearbitApi": {"id": "296", "name": "Clearbit account"}}, "typeVersion": 1}, {"name": "<PERSON><PERSON><PERSON>gger", "type": "n8n-nodes-base.hubspotTrigger", "position": [450, 300], "webhookId": "b9c442e0-6f98-4d6f-8170-7135c4dbd850", "parameters": {"eventsUi": {"eventValues": [{}]}, "additionalFields": {}}, "credentials": {"hubspotDeveloperApi": {"id": "295", "name": "Hubspot Developer account"}}, "typeVersion": 1}, {"name": "Get Contact", "type": "n8n-nodes-base.hubspot", "position": [650, 300], "parameters": {"resource": "contact", "contactId": "={{$json[\"contactId\"]}}", "operation": "get", "authentication": "oAuth2", "additionalFields": {}}, "credentials": {"hubspotOAuth2Api": {"id": "268", "name": "HubSpot@Test Account"}}, "typeVersion": 1}, {"name": "Update Contact", "type": "n8n-nodes-base.hubspot", "position": [1050, 300], "parameters": {"email": "={{$json[\"email\"]}}", "resource": "contact", "authentication": "oAuth2", "additionalFields": {"city": "={{$json[\"geo\"][\"city\"]}}", "jobTitle": "={{$json[\"employment\"][\"title\"]}}", "companyName": "={{$json[\"employment\"][\"name\"]}}"}}, "credentials": {"hubspotOAuth2Api": {"id": "268", "name": "HubSpot@Test Account"}}, "typeVersion": 1}], "connections": {"Clearbit": {"main": [[{"node": "Update Contact", "type": "main", "index": 0}]]}, "Get Contact": {"main": [[{"node": "Clearbit", "type": "main", "index": 0}]]}, "Hubspot Trigger": {"main": [[{"node": "Get Contact", "type": "main", "index": 0}]]}}}