{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "SecurityScorecard", "type": "n8n-nodes-base.securityScorecard", "position": [450, 300], "parameters": {"report": "full-scorecard-json", "resource": "report", "operation": "generate", "scorecardIdentifier": "n8n.io"}, "credentials": {"securityScorecardApi": "SecurityScorecard Credentials"}, "typeVersion": 1}, {"name": "SecurityScorecard1", "type": "n8n-nodes-base.securityScorecard", "position": [650, 300], "parameters": {"limit": 1, "resource": "report"}, "credentials": {"securityScorecardApi": "SecurityScorecard Credentials"}, "typeVersion": 1}, {"name": "SecurityScorecard2", "type": "n8n-nodes-base.securityScorecard", "position": [850, 300], "parameters": {"url": "={{$json[\"download_url\"]}}", "resource": "report", "operation": "download"}, "credentials": {"securityScorecardApi": "SecurityScorecard Credentials"}, "typeVersion": 1}], "connections": {"SecurityScorecard": {"main": [[{"node": "SecurityScorecard1", "type": "main", "index": 0}]]}, "SecurityScorecard1": {"main": [[{"node": "SecurityScorecard2", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "SecurityScorecard", "type": "main", "index": 0}]]}}}