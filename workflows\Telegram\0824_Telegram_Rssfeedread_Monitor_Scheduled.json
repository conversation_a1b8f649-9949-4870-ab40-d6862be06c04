{"id": "33", "name": "n8n_check", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [-520, 250], "parameters": {}, "typeVersion": 1}, {"name": "RSS Feed Read", "type": "n8n-nodes-base.rssFeedRead", "position": [-320, 260], "parameters": {"url": "https://github.com/n8n-io/n8n/releases.atom"}, "typeVersion": 1}, {"name": "IF", "type": "n8n-nodes-base.if", "position": [70, 260], "parameters": {"conditions": {"string": [{"value1": "={{$node[\"Filter by current day\"].json[\"data\"]}}", "value2": "/.+/", "operation": "regex"}]}}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [-520, 421], "parameters": {"triggerTimes": {"item": [{"mode": "custom", "cronExpression": "0 0 10,14,18 * * *"}]}}, "typeVersion": 1}, {"name": "Filter by current day", "type": "n8n-nodes-base.function", "position": [-120, 260], "parameters": {"functionCode": "var d = new Date();\nvar year = d.getFullYear();\nvar month = d.getMonth() + 1;\nvar day = d.getDate();\nvar hour = d.getHours() - 4;//Publication in last 4 hours\n\nmonth = month < 10 ? \"0\" + month : month;\nday = day < 10 ? \"0\" + day : day;\nhour = hour < 10 ? \"0\" + hour : hour;\n\nvar lines = items.filter(function(item) {\n  //var str = year + \"-\" + month + \"-\" + day + \"T\" + hour;\n  var str = year + \"-\" + month + \"-\" + day + \"T\" + hour;\n  //return true;//item.json.pubDate.indexOf(str) !== -1 && item.json.title.indexOf(\"n8n@\") !== -1;\n  return item.json.pubDate.indexOf(str) !== -1 && item.json.title.indexOf(\"n8n@\") !== -1 && item.json.title.indexOf(\".0\") !== -1;\n}).map(function(item) {\n  return item.json.title;\n}).join(\"\\n\");\n\n\nreturn [\n  {\n  json: {\n    date: year + \"-\" + month + \"-\" + day + \" \" + hour,\n    data: lines && lines.length ? \"New release on n8n:\\n\" + lines : \"\"\n   }\n  }\n]"}, "typeVersion": 1}, {"name": "Telegram", "type": "n8n-nodes-base.telegram", "position": [300, 280], "parameters": {"text": "={{$node[\"Filter by current day\"].json[\"data\"]}}", "chatId": "-1001235337538", "additionalFields": {"parse_mode": "HTML"}}, "credentials": {"telegramApi": "it-killia-bot"}, "typeVersion": 1}, {"name": "AWS SES", "type": "n8n-nodes-base.awsSes", "position": [300, 110], "parameters": {"body": "={{$node[\"Filter by current day\"].json[\"data\"]}}", "subject": "New n8n version", "fromEmail": "<EMAIL>", "isBodyHtml": true, "toAddresses": ["<EMAIL>"], "additionalFields": {}}, "credentials": {"aws": "ses"}, "typeVersion": 1}], "active": true, "settings": {}, "connections": {"IF": {"main": [[{"node": "Telegram", "type": "main", "index": 0}, {"node": "AWS SES", "type": "main", "index": 0}]]}, "Cron": {"main": [[{"node": "RSS Feed Read", "type": "main", "index": 0}]]}, "RSS Feed Read": {"main": [[{"node": "Filter by current day", "type": "main", "index": 0}]]}, "Filter by current day": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "RSS Feed Read", "type": "main", "index": 0}]]}}}