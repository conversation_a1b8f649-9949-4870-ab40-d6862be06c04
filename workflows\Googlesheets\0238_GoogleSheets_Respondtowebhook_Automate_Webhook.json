{"nodes": [{"name": "Read from Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [460, 300], "parameters": {"options": {}, "sheetId": "1uFISwZJ1rzkOnOSNocX-_n-ASSAznWGdpcPK3_KCvVo"}, "credentials": {"googleSheetsOAuth2Api": {"id": "19", "name": "<PERSON>'s Google Sheets account"}}, "typeVersion": 2}, {"name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [900, 300], "parameters": {"options": {"responseHeaders": {"entries": [{"name": "Content-Type", "value": "text/html; charset=UTF-8"}]}}, "respondWith": "text", "responseBody": "={{$json[\"html\"]}}"}, "typeVersion": 1}, {"name": "Build HTML", "type": "n8n-nodes-base.function", "position": [680, 300], "parameters": {"functionCode": "const columns = Object.keys(items[0].json);\n\nconst html = `\n<!doctype html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n    <title>HTML Table Example</title>\n    <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.2.0/dist/css/bootstrap.min.css\" rel=\"stylesheet\" integrity=\"sha384-gH2yIJqKdNHPEq0n4Mqa/HGKIhSkIHeL5AyhkYV8i59U5AR6csBvApHHNl/vI1Bx\" crossorigin=\"anonymous\">\n  </head>\n  <body>\n    <div class=\"container\">\n      <div class=\"row\">\n        <div class=\"col\">\n          <h1>HTML Table Example</h1>\n          <table class=\"table\">\n            <thead>\n              <tr>\n                ${columns.map(e => '<th scope=\"col\">' + e + '</th>').join('\\n')}\n              </tr>\n            </thead>\n            <tbody>\n              ${items.map(e => '<tr>' + columns.map(ee => '<td>' + e.json[ee] + '</td>').join('\\n') + '</tr>').join('\\n')}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n    <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.2.0/dist/js/bootstrap.bundle.min.js\" integrity=\"sha384-A3rJD856KowSb7dwlZdYEkO39Gagi7vIsF0jrRAoQmDKKtQBHUuLZ9AsSv4jD4Xa\" crossorigin=\"anonymous\"></script>\n  </body>\n</html>\n`;\n\nreturn [{\n  json: {\n    html: html\n  }\n}];"}, "typeVersion": 1}, {"name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [240, 300], "webhookId": "bbcd9487-54f9-449d-8246-49f3f61f44fc", "parameters": {"path": "bbcd9487-54f9-449d-8246-49f3f61f44fc", "options": {}, "responseMode": "responseNode"}, "typeVersion": 1}], "connections": {"Webhook": {"main": [[{"node": "Read from Google Sheets", "type": "main", "index": 0}]]}, "Build HTML": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Read from Google Sheets": {"main": [[{"node": "Build HTML", "type": "main", "index": 0}]]}}}