{"meta": {"instanceId": "257476b1ef58bf3cb6a46e65fac7ee34a53a5e1a8492d5c6e4da5f87c9b82833", "templateId": "2141"}, "nodes": [{"id": "ec952e64-698b-4e3a-a82d-4474a3bf8b6b", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [900, 460], "webhookId": "0f85cfa2-29d7-48c8-bea1-298a61a07b77", "parameters": {"path": "slack-trigger", "options": {}, "httpMethod": "POST"}, "typeVersion": 1}, {"id": "c6bc7004-9bec-48a3-99f2-e0d89e32730c", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [380, 240], "parameters": {"color": 7, "width": 446, "height": 321, "content": "## Needed pre-work: Add a Slack App\n1. Visit https://api.slack.com/apps, click on `New App` and choose a name and workspace.\n2. Click on `OAuth & Permissions` and scroll down to Scopes -> Bot token Scopes\n3. Add the `chat:write` scope\n4. Head over to `Slash Commands` and click on `Create New Command`\n5. Use `/idea` as the command\n6. Copy the test URL from the **Webhook** node into `Request URL`\n7. Add whatever feels best to the description and usage hint\n8. Go to `Install app` and click install"}, "typeVersion": 1}, {"id": "e8850a88-b91a-4496-b8d2-a391f17e67ad", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1100, 198.48837209302332], "parameters": {"color": 5, "width": 331, "height": 404.36834060988355, "content": "## Setup\n1. Create a Google Sheets document with the columns `Name` and `Creator`\n2. Add your Google Sheets credentials \n3. Fill the setup node below\n4. Create your Slack app (*see other sticky*)\n5. Click `Test` workflow and use the `/idea` comment in Slack\n6. Activate the workflow and exchange the Request URL with the production URL from the webhook"}, "typeVersion": 1}, {"id": "f1814a18-9301-4e86-9023-e16c5704db65", "name": "Set me up", "type": "n8n-nodes-base.set", "position": [1220, 460], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9bcc3fa7-a09e-48f0-b4ff-2c78264dec2d", "name": "Google Sheets URL", "type": "string", "value": "https://docs.google.com/spreadsheets/d/17mugx8JYjbxaTMK9aqDkJywbc0NlNmStGYq-M5fKmG8/edit#gid=0"}]}}, "typeVersion": 3.3}, {"id": "4824c23f-6477-4ee7-a6a0-2b83eaf61430", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1460, 380], "parameters": {"color": 7, "height": 224.48192284396475, "content": "You can easily support more commands, like `/bug` or `/pain` here"}, "typeVersion": 1}, {"id": "f8966efa-0576-48b9-89fe-bf49f10d703b", "name": "Switch", "type": "n8n-nodes-base.switch", "position": [1520, 460], "parameters": {"rules": {"values": [{"outputKey": "New idea", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $('Webhook').item.json.body.command }}", "rightValue": "/idea"}]}, "renameOutput": true}, {"outputKey": "Add more here", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "25221a2c-18e9-47f6-a112-0edc85b63cda", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Webhook').item.json.body.command }}", "rightValue": "/some-other-command"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3}, {"id": "1caf810e-8b40-4430-8840-8e17a176b67a", "name": "Add to Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [1780, 360], "parameters": {"columns": {"value": {"Name": "={{ $('Webhook').item.json.body.text }}", "Creator": "={{ $('Webhook').item.json.body.user_name }}"}, "schema": [{"id": "Name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Creator", "type": "string", "display": true, "required": false, "displayName": "Creator", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["Name"]}, "options": {"cellFormat": "USER_ENTERED"}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/17mugx8JYjbxaTMK9aqDkJywbc0NlNmStGYq-M5fKmG8/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "url", "value": "={{ $json['Google Sheets URL'] }}"}}, "typeVersion": 4.3}, {"id": "51f80b29-4b8c-4e2a-9da9-a7409763af0c", "name": "Hidden message to <PERSON><PERSON><PERSON> to add feature details", "type": "n8n-nodes-base.httpRequest", "position": [2000, 360], "parameters": {"url": "={{ $('Webhook').item.json.body.response_url }}", "method": "POST", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "=Thanks for adding the idea `{{ $('Webhook').item.json[\"body\"][\"text\"] }}` <@{{$('Webhook').item.json[\"body\"][\"user_id\"]}}> :rocket: Please make sure to add some details and a hypothesis to it to make it easier for us to work with it.\n\n:point_right: <{{ $('Set me up').item.json[\"Google Sheets URL\"] }}|Add your details here>"}]}}, "typeVersion": 3}], "pinData": {}, "connections": {"Switch": {"main": [[{"node": "Add to Google Sheets", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Set me up", "type": "main", "index": 0}]]}, "Set me up": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Add to Google Sheets": {"main": [[{"node": "Hidden message to <PERSON><PERSON><PERSON> to add feature details", "type": "main", "index": 0}]]}}}