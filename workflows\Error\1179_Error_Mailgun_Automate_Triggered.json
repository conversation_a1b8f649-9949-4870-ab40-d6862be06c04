{"nodes": [{"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.errorTrigger", "position": [250, 500], "parameters": {}, "typeVersion": 1}, {"name": "Mailgun", "type": "n8n-nodes-base.mailgun", "position": [450, 500], "parameters": {"text": "=Error: {{$node[\"Error Trigger\"].data[\"execution\"][\"error\"][\"message\"]}}\n\nStack Trace:\n{{$node[\"Error Trigger\"].data[\"execution\"][\"error\"][\"stack\"]}}", "subject": "=Workflow Error:  {{$node[\"Error Trigger\"].data[\"workflow\"][\"name\"]}}", "toEmail": "", "fromEmail": ""}, "credentials": {"mailgunApi": ""}, "typeVersion": 1}], "connections": {"Error Trigger": {"main": [[{"node": "Mailgun", "type": "main", "index": 0}]]}}}