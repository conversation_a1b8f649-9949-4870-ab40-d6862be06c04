{"nodes": [{"id": "84460a1f-50e7-4d16-8701-ebc1a86a0ef1", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-360, -40], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "gpt-4o"}, "options": {}}, "credentials": {"openAiApi": {"id": "8kKub5m50fH8NRfv", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "221bbae2-0920-46b4-8b25-bb654439e567", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-580, -220], "webhookId": "61927fdb-5d6e-47c2-aa73-bb48e46d41ad", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "dd0a9a82-9ad5-4116-a738-81334c58a0f2", "name": "Basic SSH commands", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [-160, -40], "parameters": {"url": "https://www.hostinger.com/tutorials/linux-commands", "toolDescription": "Get basic SSH commands"}, "typeVersion": 1.1}, {"id": "428f2694-26fd-4ce1-b423-f9a734395b08", "name": "Execute SSH", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [40, -40], "parameters": {"name": "SSH", "source": "parameter", "description": "Call this tool to execute the bash command on external VPS.\nTo pass a command to execute, you should only pass the command itself.\n", "workflowJson": "{\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"workflowInputs\": {\n          \"values\": [\n            {\n              \"name\": \"query\"\n            }\n          ]\n        }\n      },\n      \"type\": \"n8n-nodes-base.executeWorkflowTrigger\",\n      \"typeVersion\": 1.1,\n      \"position\": [\n        0,\n        0\n      ],\n      \"id\": \"29e380c2-2ecd-465e-a784-f31b1c204b38\",\n      \"name\": \"When Executed by Another Workflow\"\n    },\n    {\n      \"parameters\": {\n        \"command\": \"={{ $json.query }}\"\n      },\n      \"type\": \"n8n-nodes-base.ssh\",\n      \"typeVersion\": 1,\n      \"position\": [\n        220,\n        0\n      ],\n      \"id\": \"81a147e8-e8c8-4c98-8a9b-24de4e0152a0\",\n      \"name\": \"SSH\",\n      \"alwaysOutputData\": true,\n      \"credentials\": {\n        \"sshPassword\": {\n          \"id\": \"VMCCUQkaq46q3CpB\",\n          \"name\": \"SSH Password account\"\n        }\n      },\n      \"onError\": \"continueErrorOutput\"\n    }\n  ],\n  \"pinData\": {},\n  \"connections\": {\n    \"When Executed by Another Workflow\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"SSH\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  }\n}"}, "credentials": {"sshPassword": {"id": "VMCCUQkaq46q3CpB", "name": "SSH Password account"}}, "typeVersion": 2}, {"id": "1cd5280c-f16f-4195-9cdc-1649893ea16c", "name": "AI SysAdmin", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-340, -220], "parameters": {"text": "=You are an AI Linux System Administrator Agent expert designed to help manage Linux VPS systems.\nThe user will communicate with you as a fellow colleague. You must understand their final intention and act accordingly.\nYou can execute single-line bash commands inside a VPS using the SSH tool.\nTo pass a command to execute, you should only pass the command itself.\nReplacing null with a command you want to execute.\n\n\nYour objectives are:\n\n### **1. Understand User Intent**\n- Parse user requests related to Linux operations.\n- Accurately interpret the intent to generate valid Linux commands.\n- Accurately interpret the response you receive from a VPS.\n- Provide the user with an interpreted response.\n\n### **2. Refer to tools**\n- **Basic SSH commands**\n- **SSH**\n\n### **3. Restrictions**\n- Do not do destructive actions without confirmation from the user.\n- Under no circumstance execute \"rm -rf\" command.\n\n### **4. Behavior Guidelines**\n- Be concise, precise, and consistent.\n- Ensure all generated commands are compatible with Linux SSH.\n- Rely on system defaults when user input is incomplete.\n- For unknown or unrelated queries, clearly indicate invalid input.\n\n\nUser Prompt \nHere is a request from user: {{ $json.chatInput }}", "agent": "reActAgent", "options": {}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "fc8b89d9-36eb-400a-8c25-cd89056efc64", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [20, -180], "parameters": {"width": 360, "height": 260, "content": "## SSH login credentials\nMake sure to provide the correct SSH credentials ID in this embedded workflow under \"sshPassword\".\n\n"}, "typeVersion": 1}], "pinData": {}, "connections": {"Execute SSH": {"ai_tool": [[{"node": "AI SysAdmin", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI SysAdmin", "type": "ai_languageModel", "index": 0}]]}, "Basic SSH commands": {"ai_tool": [[{"node": "AI SysAdmin", "type": "ai_tool", "index": 0}]]}, "When chat message received": {"main": [[{"node": "AI SysAdmin", "type": "main", "index": 0}]]}}}