{"meta": {"instanceId": "84ba6d895254e080ac2b4916d987aa66b000f88d4d919a6b9c76848f9b8a7616", "templateId": "2403"}, "nodes": [{"id": "15739f4e-3267-4655-9118-d3c617652f23", "name": "Set snapshot attributes", "type": "n8n-nodes-base.set", "position": [3460, 840], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "71a51067-08ed-4300-a831-48f1d7d2ada2", "name": "data[0].snapshotId", "type": "string", "value": "={{ $json.data[0].snapshotId }}"}, {"id": "00161e54-f324-4f6e-a5df-d27d1c4b7706", "name": "displayName", "type": "string", "value": "={{ $json.displayName }}"}, {"id": "b4fbf9e6-c634-4dc7-b75e-44aa048b2e32", "name": "instanceId", "type": "number", "value": "={{ $json.instanceId }}"}]}}, "typeVersion": 3.4}, {"id": "c82ed7b8-c723-46eb-b0fc-1d9da7265a1a", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [540, 660], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.2}, {"id": "68b9319b-00b8-480b-b8bc-447e78d4e983", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [540, 880], "parameters": {}, "typeVersion": 1}, {"id": "657f763f-fc10-4d71-aabd-c9af6c041f4f", "name": "Credential", "type": "n8n-nodes-base.set", "position": [1260, 780], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "135cfcc3-050e-4128-b6b0-b8905d160498", "name": "CLIENT_ID", "type": "string", "value": ""}, {"id": "b05aa3e9-80c6-474f-b653-8e49654e3da7", "name": "CLIENT_SECRET", "type": "string", "value": ""}, {"id": "72c345ba-674e-4db2-946e-bb4a9e6f8763", "name": "API_USER", "type": "string", "value": ""}, {"id": "7d1d03e3-86cc-4fd1-9d2a-a3771d913565", "name": "API_PASSWORD", "type": "string", "value": ""}]}}, "typeVersion": 3.4}, {"id": "c74b0511-0eaf-4ae5-b6f2-8dd06186e826", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1200, 480], "parameters": {"width": 427, "height": 519, "content": "## Credential\n\nInformation required to access Contabo API\n\n- CLIENT_ID\n- CLIENT_SECRET\n- API_USER\n- API_PASSWORD\n\n[Contabo Credential](https://my.contabo.com/api/details)\n\n[Contabo API Doc](https://api.contabo.com/)"}, "typeVersion": 1}, {"id": "4d991799-ba21-4154-886b-d25901245176", "name": "Authorization", "type": "n8n-nodes-base.httpRequest", "position": [1460, 780], "parameters": {"url": "https://auth.contabo.com/auth/realms/contabo/protocol/openid-connect/token", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "bodyParameters": {"parameters": [{"name": "client_id", "value": "={{ $json.CLIENT_ID }}"}, {"name": "client_secret", "value": "={{ $json.CLIENT_SECRET }}"}, {"name": "username", "value": "={{ $json.API_USER }}"}, {"name": "password", "value": "={{ $json.API_PASSWORD }}"}, {"name": "grant_type", "value": "password"}]}, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/x-www-form-urlencoded"}]}}, "typeVersion": 4.2}, {"id": "c9bd2ce9-5587-4155-b3ca-192caa48be4c", "name": "List instances", "type": "n8n-nodes-base.httpRequest", "position": [2240, 780], "parameters": {"url": "https://api.contabo.com/v1/compute/instances", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Authorization", "value": "={{ $('Authorization').item.json['token_type'] }} {{ $('Authorization').item.json['access_token'] }}"}, {"name": "x-request-id", "value": "={{ $('UUID').item.json['data'] }}"}, {"name": "x-trace-id", "value": "={{ $('TRACE ID').item.json['data'] }}"}]}}, "typeVersion": 4.2}, {"id": "52caf65c-d46a-4ff0-8b01-5ced05fd083d", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [2440, 780], "parameters": {"options": {}, "fieldToSplitOut": "data"}, "typeVersion": 1}, {"id": "e152da50-7067-4f9d-91a0-************", "name": "UUID", "type": "n8n-nodes-base.httpRequest", "position": [1740, 780], "parameters": {"url": "https://www.uuidgenerator.net/api/version4", "options": {}}, "typeVersion": 4.2}, {"id": "8c86a299-d8b3-4806-b885-37d67e9ba8a4", "name": "TRACE ID", "type": "n8n-nodes-base.httpRequest", "position": [1960, 780], "parameters": {"url": "https://www.uuidgenerator.net/api/version4", "options": {}}, "typeVersion": 4.2}, {"id": "6ab188c6-dfc3-4e9e-83b8-32bc778917e4", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1720, 477.28137513294257], "parameters": {"width": 411.2199570815453, "height": 521.9218381008977, "content": "## get UUID\n\nGenerates the UUIDs that will be used in the 'x-request-id' and 'x-trace-id'\n\n[uuidgenerator](https://www.uuidgenerator.net/api)"}, "typeVersion": 1}, {"id": "41364273-55db-411b-a59a-0faf01857806", "name": "List snapshots", "type": "n8n-nodes-base.httpRequest", "position": [2860, 680], "parameters": {"url": "=https://api.contabo.com/v1/compute/instances/{{ $('Split Out').item.json['instanceId'] }}/snapshots", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Authorization", "value": "={{ $('Authorization').item.json['token_type'] }} {{ $('Authorization').item.json['access_token'] }}"}, {"name": "x-request-id", "value": "={{ $('UUID1').item.json['data'] }}"}, {"name": "x-trace-id", "value": "={{ $('TRACE ID').item.json['data'] }}"}]}}, "typeVersion": 4.2}, {"id": "919fed8d-704e-4b1f-9358-f2a4422b7132", "name": "UUID1", "type": "n8n-nodes-base.httpRequest", "position": [2680, 680], "parameters": {"url": "https://www.uuidgenerator.net/api/version4", "options": {}}, "typeVersion": 4.2}, {"id": "630975b0-41f6-4025-9b7e-a464c2b5f4fa", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [2200, 480], "parameters": {"width": 384, "height": 279, "content": "## List your instances     "}, "typeVersion": 1}, {"id": "f1a39319-**************-5d2b51746682", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [2640, 480], "parameters": {"width": 733.0237288135586, "height": 467.2593220338978, "content": "## List existing Snapshots\n\n- Generates a new UUID for the request\n\n- Checks if the instance already has a Snapshot"}, "typeVersion": 1}, {"id": "5f453f4f-f509-4613-9692-c16e1a8d3c53", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [3040, 820], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3}, {"id": "2e12506f-4e8d-4053-b662-d1ff9d33ecf7", "name": "get Date & Time", "type": "n8n-nodes-base.dateTime", "position": [840, 780], "parameters": {"options": {"timezone": "America/Sao_Paulo"}}, "retryOnFail": true, "typeVersion": 2}, {"id": "c06bf642-253f-4cec-8c0f-97edff450c1b", "name": "Delete existing snapshot", "type": "n8n-nodes-base.httpRequest", "position": [3780, 840], "parameters": {"url": "=https://api.contabo.com/v1/compute/instances/{{ $('Set snapshot attributes').item.json['instanceId'] }}/snapshots/{{ $('Set snapshot attributes').item.json['data'][0].snapshotId }}", "method": "DELETE", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Authorization", "value": "={{ $('Authorization').item.json['token_type'] }} {{ $('Authorization').item.json['access_token'] }}"}, {"name": "x-request-id", "value": "={{ $('UUID3').item.json['data'] }}"}, {"name": "x-trace-id", "value": "={{ $('TRACE ID').item.json['data'] }}"}]}}, "retryOnFail": true, "typeVersion": 4.2}, {"id": "f7a1d94d-c10d-400a-b9ed-7135def2d809", "name": "Create a new snapshot", "type": "n8n-nodes-base.httpRequest", "position": [4360, 660], "parameters": {"url": "=https://api.contabo.com/v1/compute/instances/{{ $('set snapshot attributes').item.json['instanceId'] }}/snapshots", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "bodyParameters": {"parameters": [{"name": "name", "value": "={{ $('Formatted Date').item.json['formattedDate'] }}"}, {"name": "description", "value": "={{ $('set snapshot attributes').item.json['displayName'] }} {{ $('Formatted Date').item.json['formattedDate'] }}"}]}, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Authorization", "value": "={{ $('Authorization').item.json['token_type'] }} {{ $('Authorization').item.json['access_token'] }}"}, {"name": "x-request-id", "value": "={{ $('UUID2').item.json['data'] }}"}, {"name": "x-trace-id", "value": "={{ $('TRACE ID').item.json['data'] }}"}]}}, "retryOnFail": true, "typeVersion": 4.2}, {"id": "dfc5ba84-89e3-4567-a466-************", "name": "Create a new snapshot1", "type": "n8n-nodes-base.httpRequest", "position": [4200, 840], "parameters": {"url": "=https://api.contabo.com/v1/compute/instances/{{ $('Set snapshot attributes').item.json['instanceId'] }}/snapshots", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "bodyParameters": {"parameters": [{"name": "name", "value": "={{ $('Formatted Date').item.json['formattedDate'] }}"}, {"name": "description", "value": "={{ $('Set snapshot attributes').item.json['displayName'] }} {{ $('Formatted Date').item.json['formattedDate'] }}"}]}, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Authorization", "value": "={{ $('Authorization').item.json['token_type'] }} {{ $('Authorization').item.json['access_token'] }}"}, {"name": "x-request-id", "value": "={{ $('UUID4').item.json['data'] }}"}, {"name": "x-trace-id", "value": "={{ $('TRACE ID').item.json['data'] }}"}]}}, "retryOnFail": true, "typeVersion": 4.2}, {"id": "d6b69037-14a9-4e01-be34-a7975503554d", "name": "UUID2", "type": "n8n-nodes-base.httpRequest", "position": [4200, 660], "parameters": {"url": "https://www.uuidgenerator.net/api/version4", "options": {}}, "typeVersion": 4.2}, {"id": "1d80d4d5-1f5f-4cf1-9540-8a4b84edda38", "name": "UUID3", "type": "n8n-nodes-base.httpRequest", "position": [3620, 840], "parameters": {"url": "https://www.uuidgenerator.net/api/version4", "options": {}}, "typeVersion": 4.2}, {"id": "f3ede6e8-5ca2-418b-8e60-03b817857cf2", "name": "UUID4", "type": "n8n-nodes-base.httpRequest", "position": [4020, 840], "parameters": {"url": "https://www.uuidgenerator.net/api/version4", "options": {}}, "typeVersion": 4.2}, {"id": "d1995aef-531f-471e-a16d-7c75b1f3ae4c", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [3440, 480], "parameters": {"width": 486.8901611698841, "height": 467.87473554386463, "content": "## Delete existing snapshot by id\n"}, "typeVersion": 1}, {"id": "8b408e6d-9574-4fa4-bc7e-7a543aa0bad6", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [3980, 480], "parameters": {"width": 576.6684015952959, "height": 468.61270146235483, "content": "## Create a new snapshot"}, "typeVersion": 1}, {"id": "a41e7b87-8e46-44ea-8ca5-08f7d1a36f47", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [380, 240], "parameters": {"width": 769.2098244001793, "height": 415.52346358766624, "content": "## Contabo Backups Workflow\nThis workflow will automatically backup (snapshot) your VPS's hosted on Contabo every day at midnight.\n\n### Setup\nOpen **Credential** and update the values ​​below\n\n- **CLIENT_ID**\n- **CLIENT_SECRET**\n- **API_USER**\n- **API_PASSWORD**\n\nYou will find this information in the [Customer Control Panel.](https://my.contabo.com/api/details)\n\nWorkflow created by [<PERSON>](https://www.linkedin.com/in/compromitto/)\n[Linkedin](https://www.linkedin.com/in/compromitto/)\n[GitHub](https://github.com/dubcom) 🇧🇷"}, "typeVersion": 1}, {"id": "eb412eff-cf8b-44e8-b0ad-21798504f11d", "name": "Formatted Date", "type": "n8n-nodes-base.dateTime", "position": [1020, 780], "parameters": {"date": "={{ $json.currentDate }}", "format": "custom", "options": {}, "operation": "formatDate", "customFormat": "dd-MM-yyyy"}, "typeVersion": 2}, {"id": "67413ee0-38db-4917-adcd-be9c6cb4f5cc", "name": "Whether snapshot there is no snapshot", "type": "n8n-nodes-base.if", "position": [3220, 820], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "2bd58580-020f-411b-b25d-e63467d615bc", "operator": {"type": "array", "operation": "empty", "singleValue": true}, "leftValue": "={{ $('List snapshots').item.json['data'] }}", "rightValue": ""}]}}, "typeVersion": 2}, {"id": "464f52f5-bc1d-402c-81d2-4db24f675871", "name": "set snapshot attributes", "type": "n8n-nodes-base.set", "position": [4020, 660], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "b9af7eb7-bd87-4949-ac95-e40025c3c419", "name": "instanceId", "type": "string", "value": "={{ $json.instanceId }}"}, {"id": "3d8cb230-4512-4b65-be3a-6ea59cb80ddd", "name": "displayName", "type": "string", "value": "={{ $json.displayName }}"}]}}, "typeVersion": 3.4}], "pinData": {}, "connections": {"UUID": {"main": [[{"node": "TRACE ID", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "Whether snapshot there is no snapshot", "type": "main", "index": 0}]]}, "UUID1": {"main": [[{"node": "List snapshots", "type": "main", "index": 0}]]}, "UUID2": {"main": [[{"node": "Create a new snapshot", "type": "main", "index": 0}]]}, "UUID3": {"main": [[{"node": "Delete existing snapshot", "type": "main", "index": 0}]]}, "UUID4": {"main": [[{"node": "Create a new snapshot1", "type": "main", "index": 0}]]}, "TRACE ID": {"main": [[{"node": "List instances", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "UUID1", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Credential": {"main": [[{"node": "Authorization", "type": "main", "index": 0}]]}, "Authorization": {"main": [[{"node": "UUID", "type": "main", "index": 0}]]}, "Formatted Date": {"main": [[{"node": "Credential", "type": "main", "index": 0}]]}, "List instances": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "List snapshots": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "get Date & Time": {"main": [[{"node": "Formatted Date", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "get Date & Time", "type": "main", "index": 0}]]}, "Set snapshot attributes": {"main": [[{"node": "UUID3", "type": "main", "index": 0}]]}, "set snapshot attributes": {"main": [[{"node": "UUID2", "type": "main", "index": 0}]]}, "Delete existing snapshot": {"main": [[{"node": "UUID4", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "get Date & Time", "type": "main", "index": 0}]]}, "Whether snapshot there is no snapshot": {"main": [[{"node": "set snapshot attributes", "type": "main", "index": 0}], [{"node": "Set snapshot attributes", "type": "main", "index": 0}]]}}}