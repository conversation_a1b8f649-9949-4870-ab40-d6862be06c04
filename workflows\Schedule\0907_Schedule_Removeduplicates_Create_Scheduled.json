{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "423f3d03-ffe8-419c-8842-95fcda213eb3", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-1540, -400], "parameters": {"rule": {"interval": [{"field": "minutes"}]}}, "typeVersion": 1.2}, {"id": "34ed006b-ecef-4bae-8493-ae43d4927dc3", "name": "Get Open Tickets", "type": "n8n-nodes-base.jira", "position": [-1340, -400], "parameters": {"limit": 10, "options": {"jql": "Project = 'SUPPORT' AND status = 'To Do'", "fields": "*navigable"}, "operation": "getAll"}, "credentials": {"jiraSoftwareCloudApi": {"id": "IH5V74q6PusewNjD", "name": "Jira SW Cloud account"}}, "typeVersion": 1}, {"id": "4c7d6b23-23d5-410e-92f0-5c9244eb190f", "name": "Simplify Ticket", "type": "n8n-nodes-base.set", "position": [-800, -400], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "267918d5-5193-48c8-8e3a-6542c8edf77c", "name": "projectKey", "type": "string", "value": "={{ $json.fields.project.key }}"}, {"id": "8c44b6b1-a5e7-4312-b96c-727b24a82ac2", "name": "issue<PERSON><PERSON>", "type": "string", "value": "={{ $json.key }}"}, {"id": "3451a39f-1907-4406-beb9-fd4feebbf4c2", "name": "issueType", "type": "string", "value": "={{ $json.fields.issuetype.name }}"}, {"id": "99f33276-50ad-424a-b307-2ed69094bc43", "name": "createdAt", "type": "string", "value": "={{ $json.fields.created }}"}, {"id": "5463ff2e-4d42-4602-8278-555f369a37e5", "name": "status", "type": "string", "value": "={{ $json.fields.status.name }}"}, {"id": "1df0588e-7916-4c4d-95f1-7c6d58ba094f", "name": "summary", "type": "string", "value": "={{ $json.fields.summary }}"}, {"id": "ecf69a9b-18c9-4b04-9d6e-b77391728f33", "name": "description", "type": "string", "value": "={{ $json.fields.description }}"}, {"id": "8f7b0876-4d6f-42b3-bc12-34396ce824ed", "name": "reportedBy", "type": "string", "value": "={{ $json.fields.creator.displayName }}"}, {"id": "********-6ecd-4b32-866f-0e336c669650", "name": "reportedByEmail<PERSON><PERSON><PERSON>", "type": "string", "value": "={{ $json.fields.creator.emailAddress }}"}]}}, "typeVersion": 3.4}, {"id": "********-4e64-415f-b388-6e029997942d", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-620, -220], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "8724cd2d-7f4f-4f8d-beef-23d0360f2487", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [-420, -220], "parameters": {"schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"labels\": {\n      \"type\": \"array\",\n      \"items\": { \"type\": \"string\" }\n    },\n    \"priority\": { \"type\": \"number\" },\n    \"summary\": { \"type\": \"string\" },\n    \"description\": { \"type\": \"string\" }\n  }\n}"}, "typeVersion": 1.2}, {"id": "26a7d493-73e4-4ce3-aff1-0357ba5a1df2", "name": "Label, Prioritize & Rewrite", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [-600, -400], "parameters": {"text": "=Reported by {{ $json.reportedBy }} <{{ $json.reportedByEmailAddress }}>\nReported at: {{ $json.createdAt }}\nIssue Key: {{ $json.issueKey }}\nSummary: {{ $json.summary }}\nDescription: {{ $json.description }}", "messages": {"messageValues": [{"message": "=Your are JIRA triage assistant who's task is to\n1) classify and label the given issue.\n2) Prioritise the given issue.\n3) Rewrite the issue summary and description.\n\n## Labels\nUse one or more. Use words wrapped in \"[]\" (square brackets):\n* Technical\n* Account\n* Access\n* Billing\n* Product\n* Training\n* Feedback\n* Complaints\n* Security\n* Privacy\n\n## Priority\n* 1 - highest\n* 2 - high\n* 3 - medium\n* 4 - low\n* 5 - lowest\n\n## Rewriting Summary and Description\n* Remove emotional and anedotal phrases or information\n* Keep to the facts of the matter\n* Highlight what was attempted and is/was failing"}]}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.6}, {"id": "909d4901-2c77-44aa-8a21-97a604351b22", "name": "Update Labels, Priority and Description", "type": "n8n-nodes-base.jira", "position": [-240, -400], "parameters": {"issueKey": "={{ $('Simplify Ticket').item.json.issueKey }}", "operation": "update", "updateFields": {"labels": "={{ $json.output.labels }}", "priority": {"__rl": true, "mode": "id", "value": "={{ $json.output.priority.toString() }}"}, "description": "={{ $json.output.description }}\n\n---\nOriginal Message:\n{{ $('Simplify Ticket').item.json.description }}"}}, "credentials": {"jiraSoftwareCloudApi": {"id": "IH5V74q6PusewNjD", "name": "Jira SW Cloud account"}}, "typeVersion": 1}, {"id": "2365cb60-ec67-4d1e-9b8d-1749cf925800", "name": "Get Recent Similar Issues Resolved", "type": "n8n-nodes-base.jira", "position": [120, -360], "parameters": {"limit": 5, "options": {"jql": "=key != {{ $('Simplify Ticket').item.json.issueKey }}\nAND status in (\"Resolved\", \"Closed\", \"Done\")\nAND resolutiondate >= startOfMonth(-1)\nAND labels in ({{\n  $('Label, Prioritize & Rewrite').item.json.output.labels\n  .map(label => `\"${label}\"`)\n  .join(',')\n}})"}, "operation": "getAll"}, "credentials": {"jiraSoftwareCloudApi": {"id": "IH5V74q6PusewNjD", "name": "Jira SW Cloud account"}}, "typeVersion": 1}, {"id": "a6e8937c-c26c-4659-809a-33ab4b2e7da6", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [340, -360], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "eec2ee2b-12ab-4cd3-9eb9-e300b5c27e81", "name": "Issue Ref", "type": "n8n-nodes-base.noOp", "position": [560, -200], "parameters": {}, "typeVersion": 1}, {"id": "3f33f567-baa0-4ca8-8a05-05302b0807aa", "name": "Get Comments", "type": "n8n-nodes-base.jira", "position": [1000, -200], "parameters": {"options": {"orderBy": "-created"}, "issueKey": "={{ $json.issueKey }}", "resource": "issueComment", "operation": "getAll"}, "credentials": {"jiraSoftwareCloudApi": {"id": "IH5V74q6PusewNjD", "name": "Jira SW Cloud account"}}, "typeVersion": 1}, {"id": "a631d8d7-8bcd-4a9b-a89b-5f3b7e7ba181", "name": "Simplify Comments", "type": "n8n-nodes-base.set", "position": [1220, -200], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "faba7ffd-4f3a-4394-9bed-01014ddc12c1", "name": "author", "type": "string", "value": "={{ $json.author.displayName }}"}, {"id": "76ed191c-6c43-47e7-bbaf-104bdde26993", "name": "comment", "type": "string", "value": "={{ $json.body.content.map(item => item.content[0].text).join('\\n') }}"}]}}, "typeVersion": 3.4}, {"id": "a0047017-0dd4-49d1-bda7-4ed94b3b6400", "name": "Summarise Resolution", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1660, -200], "parameters": {"text": "=## Issue\n{{ $('Simplify Issue').item.json.issueKey }} {{ $('Simplify Issue').item.json.summary }}\n{{ $('Simplify Issue').item.json.description }}\n\n## Comments\n{{ $json.comments.map((item,idx) => `${idx+1}. ${item.comment.replaceAll('\\n', ' ')}`).join('\\n') }}", "messages": {"messageValues": [{"message": "Analyse the given issue and its comments. Your task is to summarise the resolution of this issue."}]}, "promptType": "define"}, "typeVersion": 1.6}, {"id": "6bb5d668-062e-417a-a874-9f10a334a19b", "name": "Simplify Issue", "type": "n8n-nodes-base.set", "position": [780, -200], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "267918d5-5193-48c8-8e3a-6542c8edf77c", "name": "projectKey", "type": "string", "value": "={{ $json.fields.project.key }}"}, {"id": "8c44b6b1-a5e7-4312-b96c-727b24a82ac2", "name": "issue<PERSON><PERSON>", "type": "string", "value": "={{ $json.key }}"}, {"id": "3451a39f-1907-4406-beb9-fd4feebbf4c2", "name": "issueType", "type": "string", "value": "={{ $json.fields.issuetype.name }}"}, {"id": "99f33276-50ad-424a-b307-2ed69094bc43", "name": "createdAt", "type": "string", "value": "={{ $json.fields.created }}"}, {"id": "5463ff2e-4d42-4602-8278-555f369a37e5", "name": "status", "type": "string", "value": "={{ $json.fields.status.name }}"}, {"id": "1df0588e-7916-4c4d-95f1-7c6d58ba094f", "name": "summary", "type": "string", "value": "={{ $json.fields.summary }}"}, {"id": "ecf69a9b-18c9-4b04-9d6e-b77391728f33", "name": "description", "type": "string", "value": "={{ $json.fields.description }}"}, {"id": "8f7b0876-4d6f-42b3-bc12-34396ce824ed", "name": "reportedBy", "type": "string", "value": "={{ $json.fields.creator.displayName }}"}, {"id": "********-6ecd-4b32-866f-0e336c669650", "name": "reportedByEmail<PERSON><PERSON><PERSON>", "type": "string", "value": "={{ $json.fields.creator.emailAddress }}"}]}}, "typeVersion": 3.4}, {"id": "410c40a1-2f06-4c84-bbd7-1cb3dc5e93af", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "position": [1440, -200], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "comments"}, "typeVersion": 1}, {"id": "1cb3d1ac-1084-417f-a39a-1fbc04b10915", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1760, -40], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "0f8325eb-4d23-4a05-9f76-7ef111b8d2d6", "name": "Return Fields", "type": "n8n-nodes-base.set", "position": [2020, -80], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "ae761d50-f4b9-4baa-beec-ca1a91614d1c", "name": "issue<PERSON><PERSON>", "type": "string", "value": "={{ $('Simplify Issue').item.json.issueKey }}"}, {"id": "963b12a4-cf60-4380-9f71-4b9885e9f9b5", "name": "summary", "type": "string", "value": "={{ $('Simplify Issue').item.json.summary }}"}, {"id": "62a6c941-ccd4-4d71-8685-e5a1144395ca", "name": "description", "type": "string", "value": "={{ $('Simplify Issue').item.json.description }}"}, {"id": "47d26f5c-d360-4ca3-b48a-d36ea1746a3b", "name": "resolution", "type": "string", "value": "={{ $json.text }}"}]}}, "typeVersion": 3.4}, {"id": "26a32cba-b0b1-4434-b915-6a879eb511e2", "name": "Attempt to Resolve Issue", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1100, -600], "parameters": {"text": "=## Current Issue\nReported by: {{ $('Simplify Ticket').item.json.reportedBy }}\n{{ $('Simplify Ticket').item.json.issueKey }} {{ $('Simplify Ticket').item.json.summary }}\n{{ $('Simplify Ticket').item.json.description }}\n\n## Previously resolved Issues\n{{ $json.resolved_issues.toJsonString() }}", "messages": {"messageValues": [{"message": "=Using the previously resolved issues, attempt to suggest a resolution for the current issue for the reporter. Please address your answer to the reporter. Assume the report is non-technical and simplify your response as much as possible. Do not sign off your message."}]}, "promptType": "define"}, "typeVersion": 1.6}, {"id": "66929c27-6bc2-43a9-8419-554dbbb33849", "name": "Aggregate1", "type": "n8n-nodes-base.aggregate", "position": [900, -600], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "resolved_issues"}, "typeVersion": 1}, {"id": "bbf5bb39-a933-49f1-abb6-143b0bce7d08", "name": "OpenAI Chat Model2", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1200, -440], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "f7802fbc-96b0-4dcb-98c6-ef9051959728", "name": "Add Comment to Issue", "type": "n8n-nodes-base.jira", "position": [1460, -600], "parameters": {"comment": "={{ $json.text }}", "options": {}, "issueKey": "={{ $('Simplify Ticket').item.json.issueKey }}", "resource": "issueComment"}, "credentials": {"jiraSoftwareCloudApi": {"id": "IH5V74q6PusewNjD", "name": "Jira SW Cloud account"}}, "typeVersion": 1}, {"id": "a294329c-8ff6-4b2f-aa38-e9661d797f7e", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1620, -600], "parameters": {"color": 7, "width": 680, "height": 460, "content": "## 1. Get Open Tickets\n[Read more about the Scheduled Trigger node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.scheduletrigger)\n\nWe can use a scheduled trigger to aggressively check for newly open tickets in our JIRA support queue. The \"remove duplicates\" node (ie. <PERSON> as Seen) is used so that we don't process any issues more than once."}, "typeVersion": 1}, {"id": "d201a66e-b64b-4b55-b785-9ab2d78f5362", "name": "<PERSON> as Seen", "type": "n8n-nodes-base.removeDuplicates", "position": [-1140, -400], "parameters": {"options": {}, "operation": "removeItemsSeenInPreviousExecutions", "dedupeValue": "={{ $json.key }}"}, "typeVersion": 2}, {"id": "72446f8f-07f8-4d06-afe7-ea7ca905183b", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-900, -600], "parameters": {"color": 7, "width": 860, "height": 540, "content": "## 2. Automate Triaging of Ticket\n[Read more about the Basic LLM node](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.chainllm)\n\nNew tickets always need to be properly labelled and prioritised but it's not always possible to get to update all incoming tickets if you're light on hands. Using an AI is a great use-case for triaging of tickets as its contextual understanding helps automates this step."}, "typeVersion": 1}, {"id": "8b17aa91-afcb-4106-9987-c380fcb414b6", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [0, -600], "parameters": {"color": 7, "width": 760, "height": 600, "content": "## 3. Attempt to Resolve Ticket Using Previously Resolved Issues\n[Learn more about the JIRA node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.jira)\n\nThere are a number of approaches to also automate issue resolution. Here, we can search for similar tickets in the \"Done\" or resolved state and using the accepted answers of those tickets, provide context for an AI agent to suggest some ideas back to the user - best case, the fix is found and worst case, the user can add more debugging information through failed attempts."}, "typeVersion": 1}, {"id": "ea4b420e-7e93-46e6-a94c-96ff96ce38f0", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [800, -800], "parameters": {"color": 7, "width": 860, "height": 520, "content": "## 4. Suggest a Resolution via Comment\n[Learn more about the JIRA node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.jira)\n\nFinally, we provide the context of past resolved tickets for the agent to suggest a few resolution ideas back to the user. Be sure to format the answer to match your company tone of voice as without, AI may sound quite technical and robotic!"}, "typeVersion": 1}, {"id": "de26a64a-73dc-4952-946b-c45af9d712ce", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-2100, -1040], "parameters": {"width": 440, "height": 1100, "content": "## Try It Out!\n### This n8n template automates triaging of newly opened support tickets and issue resolution via JIRA.\n\nIf your organisation deals with a large number of support requests daily, automating triaging is a great use-case for introducing AI to your support teams. Extending the idea, we can also get AI to give a first attempt at resolving the issue intelligently.\n\n### How it works\n* A scheduled trigger picks up newly opened JIRA support tickets from the queue and discards any seen before.\n* An AI agent analyses the open ticket to add labels, priority on the seriousness of the issue and simplifies the description for better readability and understanding for human support.\n* Next, the agent attempts to address and resolve the issue by finding similar issues (by tags) which have been resolved.\n* Each similar issue has its comments analysed and summarised to identify the actual resolution and facts.\n* These summarises are then used as context for the AI agent to suggest a fix to the open ticket.\n\n### How to use\n* Simply connect your JIRA instance to the workflow and activate to start watching for open tickets. Depending on frequency, you may need to increase for decrease the intervals.\n* Define labels to use in the agent's system prompt.\n* Restrict to certain projects or issue types to suit your organisation.\n\n### Requirements\n* JIRA for issue management and support portal\n* OpenAI for LLM\n\n### Customising this workflow\n* Not using JIRA? Try swapping out the nodes for Linear or your issue management system of choice.\n* Try a different approach for issue resolution. You might want to try RAG approach where a knowledge base is used.\n\n### Need Help?\nJoin the [Discord](https://discord.com/invite/XPKeKXeB7d) or ask in the [Forum](https://community.n8n.io/)!\n\nHappy Hacking!"}, "typeVersion": 1}], "pinData": {}, "connections": {"Aggregate": {"main": [[{"node": "Summarise Resolution", "type": "main", "index": 0}]]}, "Issue Ref": {"main": [[{"node": "Simplify Issue", "type": "main", "index": 0}]]}, "Aggregate1": {"main": [[{"node": "Attempt to Resolve Issue", "type": "main", "index": 0}]]}, "Get Comments": {"main": [[{"node": "Simplify Comments", "type": "main", "index": 0}]]}, "Mark as Seen": {"main": [[{"node": "Simplify Ticket", "type": "main", "index": 0}]]}, "Return Fields": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Simplify Issue": {"main": [[{"node": "Get Comments", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Aggregate1", "type": "main", "index": 0}], [{"node": "Issue Ref", "type": "main", "index": 0}]]}, "Simplify Ticket": {"main": [[{"node": "Label, Prioritize & Rewrite", "type": "main", "index": 0}]]}, "Get Open Tickets": {"main": [[{"node": "<PERSON> as Seen", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Get Open Tickets", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Label, Prioritize & Rewrite", "type": "ai_languageModel", "index": 0}]]}, "Simplify Comments": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Summarise Resolution", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model2": {"ai_languageModel": [[{"node": "Attempt to Resolve Issue", "type": "ai_languageModel", "index": 0}]]}, "Summarise Resolution": {"main": [[{"node": "Return Fields", "type": "main", "index": 0}]]}, "Attempt to Resolve Issue": {"main": [[{"node": "Add Comment to Issue", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Label, Prioritize & Rewrite", "type": "ai_outputParser", "index": 0}]]}, "Label, Prioritize & Rewrite": {"main": [[{"node": "Update Labels, Priority and Description", "type": "main", "index": 0}]]}, "Get Recent Similar Issues Resolved": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Update Labels, Priority and Description": {"main": [[{"node": "Get Recent Similar Issues Resolved", "type": "main", "index": 0}]]}}}