{"nodes": [{"id": "aa84d631-c14f-45c2-a659-454605e83c30", "name": "On Error", "type": "n8n-nodes-base.errorTrigger", "position": [880, 900], "parameters": {}, "typeVersion": 1}, {"id": "abffce17-cc93-4c6a-8955-de2d0f4cc885", "name": "Set message", "type": "n8n-nodes-base.set", "position": [1140, 900], "parameters": {"values": {"string": [{"name": "message", "value": "=⚠️ Workflow `{{$json[\"workflow\"][\"name\"]}}` failed to run! [execution]({{ $json.execution.url }})"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"id": "1e5c4af6-30ae-45b8-bca7-048a656ce9bd", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [800, 700], "parameters": {"color": 5, "width": 424.4907862645661, "height": 154.7766688696994, "content": "### 👨‍🎤 Setup\n1. Add Telegram creds\n2. Set chat id in **Telegram** node\n2. Add this error workflow to other workflows\nhttps://docs.n8n.io/flow-logic/error-handling/#create-and-set-an-error-workflow"}, "typeVersion": 1}, {"id": "845ddf26-2d40-4cc6-843b-ccb3b365fbfb", "name": "Telegram", "type": "n8n-nodes-base.telegram", "position": [1360, 900], "parameters": {"text": "={{ $json.message }}", "chatId": "1688282582", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "6", "name": "mymontsbot token"}}, "typeVersion": 1.1}, {"id": "90db96b8-0e43-4977-b455-3e6813211640", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1380, 1080], "parameters": {"width": 241, "height": 80, "content": "### 👆🏽 Set chat id here"}, "typeVersion": 1}], "pinData": {}, "connections": {"On Error": {"main": [[{"node": "Set message", "type": "main", "index": 0}]]}, "Set message": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}}}