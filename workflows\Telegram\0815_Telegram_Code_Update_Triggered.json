{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "5d6a5a45-8aa8-4c34-aa10-5dd85f05de9d", "name": "Human Handoff using Send and Wait", "type": "n8n-nodes-base.telegram", "position": [1580, 1040], "webhookId": "d2bbc82f-0509-470a-af4d-9d92cfed4d5f", "parameters": {"chatId": "=<human chat id>", "message": "=chatId: {{ $('Handoff Subworkflow').first().json.chatId }}\nsessionId: {{ $('Handoff Subworkflow').first().json.sessionId }}\nuser: {{ $('Handoff Subworkflow').first().json.username }} ({{ $('Handoff Subworkflow').item.json.userId }})\n\nSummary:\n{{ $('Handoff Subworkflow').item.json.summary }}\n\n---\nThis user has now been handed off to a human.\nClick the button to return user to bot.", "options": {}, "operation": "sendAndWait", "responseType": "freeText"}, "credentials": {"telegramApi": {"id": "XVBXGXSsaCjU2DOS", "name": "jim<PERSON>uk_handoff_bot"}}, "typeVersion": 1.2}, {"id": "5d2f73ff-b233-4456-b34d-d5a5454dabda", "name": "Set Interaction to <PERSON><PERSON>", "type": "n8n-nodes-base.redis", "position": [1780, 1040], "parameters": {"key": "=handoff_{{ $('Handoff Subworkflow').first().json.sessionId }}_state", "value": "bot", "keyType": "string", "operation": "set"}, "credentials": {"redis": {"id": "zU4DA70qSDrZM1El", "name": "Redis account (localhost)"}}, "typeVersion": 1}, {"id": "f799e213-f3ed-4479-aade-7a7c38eb5792", "name": "Set Interaction to Human", "type": "n8n-nodes-base.redis", "position": [1380, 1040], "parameters": {"key": "=handoff_{{ $json.sessionId }}_state", "value": "=human", "keyType": "string", "operation": "set"}, "credentials": {"redis": {"id": "zU4DA70qSDrZM1El", "name": "Redis account (localhost)"}}, "typeVersion": 1}, {"id": "059c9b8c-ba50-4bdd-8969-6f5f35bba304", "name": "Get Interaction State", "type": "n8n-nodes-base.redis", "position": [-800, 820], "parameters": {"key": "=handoff_{{ $json.message.chat.id }}_state", "options": {}, "operation": "get", "propertyName": "data"}, "credentials": {"redis": {"id": "zU4DA70qSDrZM1El", "name": "Redis account (localhost)"}}, "typeVersion": 1}, {"id": "8afd48ff-478d-4092-ac88-fb2e435ded00", "name": "Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-280, 640], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "19c5ef66-410d-47fd-9b2a-19776c53a681", "name": "Memory", "type": "@n8n/n8n-nodes-langchain.memoryRedisChat", "position": [-160, 640], "parameters": {"sessionKey": "=handoff_{{ $('Telegram Trigger').first().json.message.chat.id }}_chat", "sessionIdType": "customKey"}, "credentials": {"redis": {"id": "zU4DA70qSDrZM1El", "name": "Redis account (localhost)"}}, "typeVersion": 1.4}, {"id": "814e464d-a2e0-4ae1-83d1-df3732d3b430", "name": "Handoff Subworkflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [980, 1040], "parameters": {"workflowInputs": {"values": [{"name": "chatId"}, {"name": "sessionId"}, {"name": "userId"}, {"name": "username"}, {"name": "summary"}]}}, "typeVersion": 1.1}, {"id": "53c311ca-b7ad-4705-97c9-f6438a6c6b4e", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "position": [-1000, 820], "webhookId": "e905dd99-eefc-48ff-a62d-c7078e9bb216", "parameters": {"updates": ["message"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "XVBXGXSsaCjU2DOS", "name": "jim<PERSON>uk_handoff_bot"}}, "typeVersion": 1.1}, {"id": "ea54c9f7-8ebc-4146-a4ec-6d05957f340d", "name": "Send Response", "type": "n8n-nodes-base.telegram", "position": [1400, 340], "webhookId": "55ecb0ba-72c8-4f16-a6c8-0c0bb8582554", "parameters": {"text": "=Thank you. I've now transferred you to a human agent who will reach out to you. Once complete, you can come back to this chat to finish the process.", "chatId": "={{ $('<PERSON><PERSON><PERSON> Trigger').first().json.message.chat.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "XVBXGXSsaCjU2DOS", "name": "jim<PERSON>uk_handoff_bot"}}, "typeVersion": 1.2}, {"id": "7f895e2a-7aee-40be-a8b7-b2b0ff469b2d", "name": "Switch Interaction", "type": "n8n-nodes-base.switch", "position": [-620, 820], "parameters": {"rules": {"values": [{"outputKey": "Human", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "6bc61072-f7cf-4a6d-af81-36abede59d47", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.data }}", "rightValue": "human"}]}, "renameOutput": true}, {"outputKey": "Bot", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "6882db64-b522-4895-8949-d98d87b53b49", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.data }}", "rightValue": "bot"}]}, "renameOutput": true}]}, "options": {"fallbackOutput": "extra", "renameFallbackOutput": "Onboarding"}}, "typeVersion": 3.2}, {"id": "df203358-e6c7-4014-8e2b-ef934e331215", "name": "Information Extractor", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [440, 460], "parameters": {"text": "={{\n$json.data\n  .reverse()\n  .slice(0, 5)\n  .map(item => item.parseJson())\n  .map(item => `${item.type}: ${item.data.content}`)\n  .join('\\n')\n}}", "options": {"systemPromptTemplate": "Analyse the conversation history and extract the required customer details. If not found, leave blank."}, "attributes": {"attributes": [{"name": "firstname", "required": true, "description": "first name of customer"}, {"name": "lastname", "required": true, "description": "last name of customer"}, {"name": "address_and_postcode", "required": true, "description": "address and postcode of customer"}, {"name": "reason_for_call", "required": true, "description": "a summary of the reason for the call"}]}}, "typeVersion": 1}, {"id": "35bc11d2-6024-425b-ac67-a61ff26b9478", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [420, 640], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "20d71c5f-6e84-4b45-b324-0706ecd3a681", "name": "With Defaults", "type": "n8n-nodes-base.code", "position": [760, 460], "parameters": {"mode": "runOnceForEachItem", "jsCode": "return {\n  \"firstname\": \"\",\n  \"lastname\": \"\",\n  \"address_and_postcode\": \"\",\n  \"reason_for_call\": \"\",\n  ...$input.item.json.output,\n}"}, "typeVersion": 2}, {"id": "76344f79-1afa-4fd9-9c01-ca3818f62f37", "name": "Has All Criteria?", "type": "n8n-nodes-base.if", "position": [920, 460], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "96d4a43f-aa0f-486a-b692-0105196d099a", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ Object.values($json).every(val => Boolean(val)) }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "f76c1032-036b-46ad-91da-023d5af931fd", "name": "Onboarding Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-240, 460], "parameters": {"text": "={{ $('<PERSON><PERSON><PERSON> Trigger').first().json.message.text }}", "options": {"systemMessage": "=You are a prescreen assistant whose task is to get the following details from a customer before handing them over to a human agent.\n* first name\n* last name\n* address and postcode\n* reason for calling - capture exact wording if possible.\n\nOnce all data is gathered, hand the user off to a human agent."}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "0a5b19a5-9c19-436c-b1d3-23d962efd23e", "name": "Get Onboarding Chat History", "type": "n8n-nodes-base.redis", "position": [280, 460], "parameters": {"key": "=handoff_{{ $('Telegram Trigger').first().json.message.chat.id }}_chat", "options": {}, "operation": "get", "propertyName": "data"}, "credentials": {"redis": {"id": "zU4DA70qSDrZM1El", "name": "Redis account (localhost)"}}, "typeVersion": 1}, {"id": "4f247eb3-78f0-4ec1-8713-1373da07b5f3", "name": "After Sales Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [340, 1000], "parameters": {"text": "={{ $('<PERSON><PERSON><PERSON> Trigger').item.json.message.text }}", "options": {"systemMessage": "You are an aftersales agent helping the user answer questions about their recent purchase."}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "bce26253-f7da-49e8-952b-52e14eb4f9b5", "name": "Memory1", "type": "@n8n/n8n-nodes-langchain.memoryRedisChat", "position": [420, 1180], "parameters": {"sessionKey": "=handoff_{{ $('Telegram Trigger').first().json.message.chat.id }}_chat2", "sessionIdType": "customKey", "contextWindowLength": 30}, "credentials": {"redis": {"id": "zU4DA70qSDrZM1El", "name": "Redis account (localhost)"}}, "typeVersion": 1.4}, {"id": "51c4549b-559c-400d-9951-dde0953ade4c", "name": "Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [300, 1180], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "63b8d9e8-7af2-4103-a365-ca5471bd7b36", "name": "Handoff Tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [540, 1180], "parameters": {"name": "handoff_to_human", "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "description": "Call this tool to handoff or defer to a human agent at the request of the customer. Ensure a summary of the reason for the handoff is obtained first.", "workflowInputs": {"value": {"chatId": "={{ $('Tel<PERSON>ram Trigger').first().json.message.chat.id.toString() }}", "userId": "={{ $('Telegram Trigger').first().json.message.from.id.toString() }}", "summary": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('summary', `Reason for human hand-off`, 'string') }}", "username": "={{ $('Telegram Trigger').first().json.message.from.username }}", "sessionId": "={{ $('Tel<PERSON>ram Trigger').first().json.message.chat.id.toString() }}"}, "schema": [{"id": "chatId", "type": "string", "display": true, "removed": false, "required": false, "displayName": "chatId", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "sessionId", "type": "string", "display": true, "removed": false, "required": false, "displayName": "sessionId", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "userId", "type": "string", "display": true, "removed": false, "required": false, "displayName": "userId", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "username", "type": "string", "display": true, "removed": false, "required": false, "displayName": "username", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "summary", "type": "string", "display": true, "removed": false, "required": false, "displayName": "summary", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2}, {"id": "9a2b9077-0ead-4f2c-9e83-05ee341ff697", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1080, 580], "parameters": {"color": 7, "width": 660, "height": 460, "content": "## 1. Check Interaction State For Incoming Message\n[Learn more about the telegram trigger](https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.telegramtrigger/)\n\nThis is an example of a state-based agent - the technique commonly known as a finite-state machine. This is a great way to really control the flow and direction of the conversation where there are requirements to collect data or perform steps in sequence. To manage the session state, we can use Redis and the session key will be the user's number."}, "typeVersion": 1}, {"id": "ccca9a5c-595a-4c58-9316-933a8234ab91", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-380, 260], "parameters": {"color": 7, "width": 560, "height": 520, "content": "## 2. Onboarding Agent to Validate Customers\n[Read more about Agents](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.agent)\n\nThis agent unlike the common multi-tasking examples out there, is only tasked to handle the user's onboarding. In this way, we trade convenience for clutter but ensure the agent is less likely to stray from the desired path."}, "typeVersion": 1}, {"id": "2d859430-1326-4cbc-a5cc-3af0d091e5c0", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [200, 260], "parameters": {"color": 7, "width": 880, "height": 520, "content": "## 3. Extract Customer Details from Conversation\n[Learn more about the Information Extractor](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.information-extractor)\n\nTo collect the user's details in a structured format, we can analyse the current conversation and extract the data with the Information Extractor node. This allows the conversation to remain free-form and avoids asking a question for each field. We use a code node to check if all details are captured."}, "typeVersion": 1}, {"id": "754f0859-8af7-4a15-8e46-c9ad62b55bf3", "name": "Handoff Subworkflow1", "type": "n8n-nodes-base.executeWorkflow", "position": [1220, 340], "parameters": {"mode": "each", "options": {"waitForSubWorkflow": false}, "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "workflowInputs": {"value": {"chatId": "={{ $('Tel<PERSON>ram Trigger').first().json.message.chat.id.toString() }}", "userId": "={{ $('Telegram Trigger').first().json.message.from.id.toString() }}", "summary": "=name: {{ $json.firstname }} {{ $json.lastname }}\naddress: {{ $json.address_and_postcode }}\nsummary: {{ $json.reason_for_call }}", "username": "={{ $('Telegram Trigger').first().json.message.from.username }}", "sessionId": "={{ $('Tel<PERSON>ram Trigger').first().json.message.chat.id.toString() }}"}, "schema": [{"id": "chatId", "type": "string", "display": true, "required": false, "displayName": "chatId", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "sessionId", "type": "string", "display": true, "required": false, "displayName": "sessionId", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "userId", "type": "string", "display": true, "required": false, "displayName": "userId", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "username", "type": "string", "display": true, "required": false, "displayName": "username", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "summary", "type": "string", "display": true, "required": false, "displayName": "summary", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": true}}, "typeVersion": 1.2}, {"id": "6f92128f-14dc-4b0a-b098-3d9535ac11e3", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1100, 140], "parameters": {"color": 7, "width": 800, "height": 640, "content": "## 4. Human Handoff when Criteria Met\n[Learn more about subworkflows](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.executeworkflow)\n\nHere, we initiate the hand-off which calls our hand-off subworkflow. Subworkflows can be a great way to run actions concurrently and without blocking the reply to the user. At this point, the session state would be set to \"human\" which means the human agent should take over."}, "typeVersion": 1}, {"id": "14500c4f-da43-460f-bf4d-80d0a2f4537b", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-380, 800], "parameters": {"color": 7, "width": 520, "height": 440, "content": "## 5. Defer to Human\n\nWhen session state is \"human\", no further user messages can reach the agent. This is to ensure there isn't a parallel conversation happening which may confuse the human and agent."}, "typeVersion": 1}, {"id": "4d74d479-8525-4b64-8291-e2b3058edeaa", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [160, 800], "parameters": {"color": 7, "width": 720, "height": 520, "content": "## 6. Interaction Switched Back to Bo<PERSON>\n[Read more about the Customer Workflow Tool](https://docs.n8n.io/integrations/builtin/cluster-nodes/sub-nodes/n8n-nodes-langchain.toolworkflow/)\n\nWhen the session state is \"bot\", it signals the human agent has \"transferred\" the user back to the bot and so can continue processing their messages. A Custom Workflow Tool is used to retrigger the hand-off process whenever the user requests for it."}, "typeVersion": 1}, {"id": "a1c064d9-cc74-475d-b233-9deb4dfa81d7", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [920, 820], "parameters": {"color": 7, "width": 1580, "height": 560, "content": "## 7. Interaction Subworkflow\n[Learn more about n8n's human-in-the-loop feature](https://docs.n8n.io/advanced-ai/examples/human-fallback/)\n\nThe hand-off implementation here involves a \"human-in-the-loop\" feature of n8n - a feature which \"pauses\" an execution whilst running until a reply or action is sent back by the human. Sounds complicated but good to note that n8n handles this interaction seemlessly! Here, we're using it to allow the human agent to reliquish control of the conversation back to the AI agent. Additionally, the human agent's feedback is captured and added to the agent's memory to better assist the user afterwards."}, "typeVersion": 1}, {"id": "33684768-2f21-4f30-9e56-934171b215fc", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-1580, -260], "parameters": {"width": 440, "height": 1320, "content": "## Try it out\n### This n8n template demonstrates an approach to perform bot-to-human handoff using Human-in-the-loop functionality as a switch.\n\nIn this experiment, we play with the idea of states we want our agent to be in which controls it's interacton with the user.\n* **First state** - the agent is onboarding the user by collecting their details for a sales inquiry. After which, they are handed-off / transferred to a human to continue the call.\n* **Second state** - the agent is essentially \"deactivated\" as further messages to the bot will not reach it. Instead, a canned response is given to the user. The human agent must \"reactivate\" the bot by completing the human-in-the-loop form and give a summary of their conversation with the user.\n* **Third state** - the agent is \"reactivated\" with context of the human-to-user conversation and is set to provide after sales assistance. An tool is made available to the agent to again delegate back to the human agent when requested.\n\n### How it works\n* This template uses telegram to handle the interaction between the user and the agent.\n* Each user message is checked for a session state to ensure it is guided to the right stage of the conversation. For this, we can use Redis as a simple key-value store.\n* When no state is set, the user is directed through an onboarding step to attain their details. Once complete, the agent will \"transfer\" the user to a human agent - technically, all this involves is an update to the session state and a message to another chat forwarding the user's details.\n* During this \"human\" state, the agent cannot reply to the user and must wait until the human \"transfers\" the conversation back. The human can do this by replying to \"human-in-the-loop\" message with a summary of their conversation with the user. This session state now changes to \"bot\" and the context is implanted in the agent's memory so that the agent can respond to future questions.\n* At this stage of the conversation, the agent is now expected to handle and help the user with after-sales questions. The user can at anytime request transfer back to the human agent, repeating the previous steps as necessary.\n\n#### How to use\n* Plan your user journey! Here is a very basic example of a sales inquiry with at most 3 states. More thought should be developed when many more states are involved.\n* You may want to better log and manage session states so no user is left in limbo. Try connecting the user and sessions to your CRM.\n* Note, the Onboarding agent and After-Sales agent have separate chat memories. When adding more agents, it is recommend to continue having separate chat memories to help focus between states.\n\n### Need Help?\nJoin the [Discord](https://discord.com/invite/XPKeKXeB7d) or ask in the [Forum](https://community.n8n.io/)!\n\nHappy Hacking!"}, "typeVersion": 1}, {"id": "187ca5ef-c804-4aca-8ad9-3c4b11676fbf", "name": "Send Response2", "type": "n8n-nodes-base.telegram", "position": [2320, 1040], "webhookId": "55ecb0ba-72c8-4f16-a6c8-0c0bb8582554", "parameters": {"text": "=Hello again! I'm now ready to answer any remaining questions you may have.", "chatId": "={{ $('Handoff Subworkflow').first().json.chatId }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "XVBXGXSsaCjU2DOS", "name": "jim<PERSON>uk_handoff_bot"}}, "typeVersion": 1.2}, {"id": "ab036106-84f2-4cf7-a5ad-7706a9e5ea0a", "name": "Memory2", "type": "@n8n/n8n-nodes-langchain.memoryRedisChat", "position": [1980, 1200], "parameters": {"sessionKey": "=handoff_{{ $('Handoff Subworkflow').first().json.chatId }}_chat2", "sessionIdType": "customKey", "contextWindowLength": 30}, "credentials": {"redis": {"id": "zU4DA70qSDrZM1El", "name": "Redis account (localhost)"}}, "typeVersion": 1.5}, {"id": "9d9d1aee-5632-499c-968d-63a555cc58d8", "name": "Update Agent Memory", "type": "@n8n/n8n-nodes-langchain.memoryManager", "position": [1980, 1040], "parameters": {"mode": "insert", "messages": {"messageValues": [{"type": "ai", "message": "=Report from human agent says \"{{ $('Human Handoff using Send and Wait').first().json.data.text }}\"", "hideFromUI": true}, {"type": "ai", "message": "Hello again! I'm now ready to answer any remaining questions you may have."}]}}, "typeVersion": 1.1}, {"id": "8da2fefa-e51f-4258-8ded-e0f20db87641", "name": "Send Response3", "type": "n8n-nodes-base.telegram", "position": [680, 1000], "webhookId": "55ecb0ba-72c8-4f16-a6c8-0c0bb8582554", "parameters": {"text": "={{ $json.output }}", "chatId": "={{ $('<PERSON><PERSON><PERSON> Trigger').first().json.message.chat.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "XVBXGXSsaCjU2DOS", "name": "jim<PERSON>uk_handoff_bot"}}, "typeVersion": 1.2}, {"id": "f027afe0-0fd2-47a2-a0f8-f0d78f8fbc18", "name": "Get Canned Response", "type": "n8n-nodes-base.telegram", "position": [-180, 1000], "webhookId": "55ecb0ba-72c8-4f16-a6c8-0c0bb8582554", "parameters": {"text": "=This conversation has been handed-off to a human agent who will be in contact soon if not done so already. I cannot respond to your query until the human agent transfers you back to me.", "chatId": "={{ $('<PERSON><PERSON><PERSON> Trigger').first().json.message.chat.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "XVBXGXSsaCjU2DOS", "name": "jim<PERSON>uk_handoff_bot"}}, "typeVersion": 1.2}, {"id": "a1ada0c7-779e-44bc-bdbd-1d5285bedf3c", "name": "Notify user", "type": "n8n-nodes-base.telegram", "position": [1180, 1040], "webhookId": "55ecb0ba-72c8-4f16-a6c8-0c0bb8582554", "parameters": {"text": "=You have now been transferred to a human. Unfortunately, I will not be able to respond until the human agent transfers you back to me.", "chatId": "={{ $('<PERSON><PERSON><PERSON> Trigger').first().json.message.chat.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "XVBXGXSsaCjU2DOS", "name": "jim<PERSON>uk_handoff_bot"}}, "typeVersion": 1.2}, {"id": "31287a38-e7a7-4518-9f54-6e6c9b2486fe", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [1520, 1020], "parameters": {"width": 220, "height": 320, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n### Add Human Chat ID\nThis is needed to notify the human agent."}, "typeVersion": 1}, {"id": "a58a82b9-2c37-4137-9b3e-17479c78a2d2", "name": "Memory3", "type": "@n8n/n8n-nodes-langchain.memoryRedisChat", "position": [1580, 500], "parameters": {"sessionKey": "=handoff_{{ $('Telegram Trigger').first().json.message.chat.id }}_chat2", "sessionIdType": "customKey", "contextWindowLength": 30}, "credentials": {"redis": {"id": "zU4DA70qSDrZM1El", "name": "Redis account (localhost)"}}, "typeVersion": 1.5}, {"id": "4d3d5b46-6e82-4b97-9e5f-76f02ff79ce5", "name": "Update Agent Memory1", "type": "@n8n/n8n-nodes-langchain.memoryManager", "position": [1580, 340], "parameters": {"mode": "insert", "messages": {"messageValues": [{"type": "ai", "message": "=The person I'm speaking to has the following details:\nfirstname: {{ $('Handoff Subworkflow1').first().json.firstname }}\nlastname: {{ $('Handoff Subworkflow1').first().json.firstname }}\nreason for call: {{ $('Handoff Subworkflow1').first().json.reason_for_call }}", "hideFromUI": true}]}}, "typeVersion": 1.1}, {"id": "ced46068-440b-46b4-aa51-b5bb9d59a004", "name": "Continue Conversation", "type": "n8n-nodes-base.telegram", "position": [1220, 580], "webhookId": "55ecb0ba-72c8-4f16-a6c8-0c0bb8582554", "parameters": {"text": "={{ $('Onboarding Agent').first().json.output }}", "chatId": "={{ $('<PERSON><PERSON><PERSON> Trigger').first().json.message.chat.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "XVBXGXSsaCjU2DOS", "name": "jim<PERSON>uk_handoff_bot"}}, "typeVersion": 1.2}], "pinData": {}, "connections": {"Model": {"ai_languageModel": [[{"node": "Onboarding Agent", "type": "ai_languageModel", "index": 0}]]}, "Memory": {"ai_memory": [[{"node": "Onboarding Agent", "type": "ai_memory", "index": 0}]]}, "Model1": {"ai_languageModel": [[{"node": "After Sales Agent", "type": "ai_languageModel", "index": 0}]]}, "Memory1": {"ai_memory": [[{"node": "After Sales Agent", "type": "ai_memory", "index": 0}]]}, "Memory2": {"ai_memory": [[{"node": "Update Agent Memory", "type": "ai_memory", "index": 0}]]}, "Memory3": {"ai_memory": [[{"node": "Update Agent Memory1", "type": "ai_memory", "index": 0}]]}, "Notify user": {"main": [[{"node": "Set Interaction to Human", "type": "main", "index": 0}]]}, "Handoff Tool": {"ai_tool": [[{"node": "After Sales Agent", "type": "ai_tool", "index": 0}]]}, "Send Response": {"main": [[{"node": "Update Agent Memory1", "type": "main", "index": 0}]]}, "With Defaults": {"main": [[{"node": "Has All Criteria?", "type": "main", "index": 0}]]}, "Onboarding Agent": {"main": [[{"node": "Get Onboarding Chat History", "type": "main", "index": 0}]]}, "Telegram Trigger": {"main": [[{"node": "Get Interaction State", "type": "main", "index": 0}]]}, "After Sales Agent": {"main": [[{"node": "Send Response3", "type": "main", "index": 0}]]}, "Has All Criteria?": {"main": [[{"node": "Handoff Subworkflow1", "type": "main", "index": 0}], [{"node": "Continue Conversation", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Information Extractor", "type": "ai_languageModel", "index": 0}]]}, "Switch Interaction": {"main": [[{"node": "Get Canned Response", "type": "main", "index": 0}], [{"node": "After Sales Agent", "type": "main", "index": 0}], [{"node": "Onboarding Agent", "type": "main", "index": 0}]]}, "Handoff Subworkflow": {"main": [[{"node": "Notify user", "type": "main", "index": 0}]]}, "Update Agent Memory": {"main": [[{"node": "Send Response2", "type": "main", "index": 0}]]}, "Handoff Subworkflow1": {"main": [[{"node": "Send Response", "type": "main", "index": 0}]]}, "Get Interaction State": {"main": [[{"node": "Switch Interaction", "type": "main", "index": 0}]]}, "Information Extractor": {"main": [[{"node": "With Defaults", "type": "main", "index": 0}]]}, "Set Interaction to Bot": {"main": [[{"node": "Update Agent Memory", "type": "main", "index": 0}]]}, "Set Interaction to Human": {"main": [[{"node": "Human Handoff using Send and Wait", "type": "main", "index": 0}]]}, "Get Onboarding Chat History": {"main": [[{"node": "Information Extractor", "type": "main", "index": 0}]]}, "Human Handoff using Send and Wait": {"main": [[{"node": "Set Interaction to <PERSON><PERSON>", "type": "main", "index": 0}]]}}}