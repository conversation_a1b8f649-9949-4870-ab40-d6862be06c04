{"meta": {"instanceId": "e634e668fe1fc93a75c4f2a7fc0dad807ca318b79654157eadb9578496acbc76", "templateCredsSetupCompleted": true}, "nodes": [{"id": "58c6003f-3311-448b-a949-4fbc22b38e2e", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-560, 80], "parameters": {}, "typeVersion": 1}, {"id": "67e4f66c-256f-4e45-b98e-d2872a416ff5", "name": "Get all Users", "type": "n8n-nodes-base.httpRequest", "position": [80, 100], "parameters": {"url": "={{ $json.n8n_url }}", "options": {"pagination": {"pagination": {"parameters": {"parameters": [{"name": "cursor", "value": "={{ $response.body.nextCursor }}"}]}, "completeExpression": "={{ !$response.body.nextCursor }}", "paginationCompleteWhen": "other"}}}, "sendQuery": true, "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "limit", "value": "5"}]}, "nodeCredentialType": "n8nApi"}, "credentials": {"n8nApi": {"id": "dzYjDgtEXtpRPKhe", "name": "n8n account"}, "httpHeaderAuth": {"id": "iiLmD473RYjGLbCA", "name": "Squarespace API key - Apps script"}}, "typeVersion": 4.2}, {"id": "2a66ddc7-5fde-4e2b-9ad6-7c68968214ae", "name": "Get all rows", "type": "n8n-nodes-base.googleSheets", "position": [80, -180], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/15A3ZWzIBfONL4U_1XGJvtsS8HtMQ69qrpxd5C5L6Akg/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "15A3ZWzIBfONL4U_1XGJvtsS8HtMQ69qrpxd5C5L6Akg", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/15A3ZWzIBfONL4U_1XGJvtsS8HtMQ69qrpxd5C5L6Akg/edit?usp=drivesdk", "cachedResultName": "n8n-submission"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JgI9maibw5DnBXRP", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "f220c6db-eafb-4bb5-9cbe-43edcf563a67", "name": "Get non-users", "type": "n8n-nodes-base.merge", "position": [620, -100], "parameters": {"mode": "combine", "options": {}, "advanced": true, "joinMode": "keepNonMatches", "mergeByFields": {"values": [{"field1": "Email Address", "field2": "email"}]}, "outputDataFrom": "input1"}, "typeVersion": 3}, {"id": "906e8dde-4c58-4e93-9e07-3064a5dd60dd", "name": "Invite Users", "type": "n8n-nodes-base.httpRequest", "position": [1100, -100], "parameters": {"url": "={{ $('Edit Fields').item.json.n8n_url }}", "method": "POST", "options": {}, "jsonBody": "={{ [$json] }}", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "n8nApi"}, "credentials": {"n8nApi": {"id": "dzYjDgtEXtpRPKhe", "name": "n8n account"}, "httpHeaderAuth": {"id": "iiLmD473RYjGLbCA", "name": "Squarespace API key - Apps script"}}, "typeVersion": 4.2}, {"id": "195d0c33-611a-4a16-b62c-8ba1f4f31e19", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-560, -160], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.2}, {"id": "dd453b5b-f238-43b1-8c44-2c3ed3a3d7ba", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [-220, -20], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c3a7a1ee-d1a2-4a29-b4b3-dcadf0fc16e2", "name": "n8n_url", "type": "string", "value": "https://{n8n-url}/api/v1/users"}]}}, "typeVersion": 3.4}, {"id": "07e678c7-7c98-4f09-89d8-5e4d7d442a8f", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-280, -160], "parameters": {"color": 4, "width": 230, "height": 300, "content": "## Edit this node 👇\nChange n8n_url to your instance URL\nhttps://docs.n8n.io/api/authentication/#call-the-api-using-your-key"}, "typeVersion": 1}, {"id": "2bfb10b6-220b-4c73-a15f-190412f2dda2", "name": "Create users list", "type": "n8n-nodes-base.set", "position": [880, -100], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "36282722-07ec-47b1-ab08-c649b7901ed7", "name": "email", "type": "string", "value": "={{ $json['Email Address'] }}"}, {"id": "9b073e1d-8c16-45b1-b333-97dfe635eb73", "name": "role", "type": "string", "value": "global:member"}]}}, "typeVersion": 3.4}, {"id": "221ca946-e305-4283-bca1-4289b8a7db28", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-1000, -300], "parameters": {"color": 4, "width": 371.1995072042308, "height": 600.88409546716, "content": "## Invite users to n8n from Google sheets\nThis workflow will get all Users from n8n and compare against the rows from Google sheets and create new users\n\nInvitation emails will be sent once the new users created\n\nYou can run the workflow on demand or by schedule\n\n## Spreadsheet template\n\nThe sheet columns are inspire from Squarespace newsletter block connection, but you can change the node to adapt new columns format\n\nClone the [sample sheet here](https://docs.google.com/spreadsheets/d/1wi2Ucb4b35e0-fuf-96sMnyzTft0ADz3MwdE_cG_WnQ/edit?usp=sharing)\n- Submitted On\t\n- Email Address\t\n- Name"}, "typeVersion": 1}, {"id": "c956e102-7fe3-4ee4-90e0-32cb11556c2c", "name": "Combine all paginated results", "type": "n8n-nodes-base.code", "position": [320, 100], "parameters": {"jsCode": "let results = [];\nfor (let i = 0; i < $input.all().length; i++) {\n  results = results.concat($input.all()[i].json.data);\n}\n\nreturn results;"}, "typeVersion": 2}], "pinData": {}, "connections": {"Edit Fields": {"main": [[{"node": "Get all rows", "type": "main", "index": 0}, {"node": "Get all Users", "type": "main", "index": 0}]]}, "Get all rows": {"main": [[{"node": "Get non-users", "type": "main", "index": 0}]]}, "Get all Users": {"main": [[{"node": "Combine all paginated results", "type": "main", "index": 0}]]}, "Get non-users": {"main": [[{"node": "Create users list", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Create users list": {"main": [[{"node": "Invite Users", "type": "main", "index": 0}]]}, "Combine all paginated results": {"main": [[{"node": "Get non-users", "type": "main", "index": 1}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}}}