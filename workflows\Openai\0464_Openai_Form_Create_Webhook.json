{"meta": {"instanceId": "96cab4456c8d5d47ff3acba57e93f1f3750005103b819e4580442bcd2bb6cc4d"}, "nodes": [{"id": "8b6d8462-1fe5-478b-aa15-7d10ff799aae", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [980, 900], "parameters": {"fields": {"values": [{"name": "prompt", "stringValue": "\"\"\"PROMPT: The above is a n8n workflow json, please create workflow documentation for the user:. you are the master of brevity, be as concise and brief as possible, output generated documentation only.      ## Guidelines for the documentation  - **Provide a detailed description**: Provide a concise and informative description outlining the template's functionality and expected outcomes. Include a brief setup guide for user convenience. A detailed description not only clarifies the template's purpose but also enhances its discoverability through SEO. It’s advised to use these sections in your description:     - Who is this for?     - What problem is this workflow solving? / use case     - What this workflow does     - Setup     - How to customize this workflow to your needs   Here is an example  ``` # Who is this template for?  This workflow template is designed for **Sales** and **Customer Success** professionals seeking alerts when potential high-value users, prospects, or existing customers register for a Discourse community. Leveraging Clearbit, it retrieves enriched data for the new member to assess their value.  ### Example result in Slack  ![Screenshot ******** at 13.51.29.png](https://n8niostorageaccount.blob.core.windows.net/n8nio-strapi-blobs-prod/assets/Screenshot_2024_02_21_at_13_51_29_030961dc1b.png)  # How it works  - Each time a new member is created in Discourse, the workflow runs (powered by Discourse's native Webhooks feature). - After filtering out popular private email accounts, we run the member's email through Clearbit to fetch available information on the member as well as their organization. - If the enriched data meets certain criteria, we send a Slack message to a channel. This message has a few quick actions: `Open LinkedIn profile` and `Email member`  # Set up instructions  Overview is below. Watch this [🎥 quick set up video](https://www.loom.com/share/d379895004374ddc85dc9171ca37c139?sid=bb28df29-bc91-4d32-a657-0bfbaaf50cc7) for detailed instructions on how to get the template running, as well as how to customize it.  1. Complete the `Set up credentials` step when you first open the workflow. You'll need a Discourse (admin user), Clearbit, and Slack account. 2. Set up the Webhook in Discourse, linking the `On new Discourse user` Trigger with your Discourse community. 3. Set the correct channel to send to in the `Post message in channel` step 4. After testing your workflow, swap the Test URL to Production URL in Discourse and activate your workflow  Template was created in n8n `v1.29.1`  ``` \"\"\""}]}, "options": {}}, "typeVersion": 3.2}, {"id": "9d1e972c-e737-4221-bd8b-dfd8115b9948", "name": "OpenAI", "type": "n8n-nodes-base.openAi", "position": [1400, 900], "parameters": {"prompt": {"messages": [{"content": "={{ $json.input }}"}]}, "options": {}, "resource": "chat", "chatModel": "gpt-4-1106-preview"}, "credentials": {"openAiApi": {"id": "GrqJccjcTot1xZLv", "name": "OpenAi account"}}, "typeVersion": 1.1}, {"id": "3071e7e7-e0d6-4fad-a6ee-fbb5b722f344", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [1620, 900], "parameters": {"options": {"responseCode": 200, "responseHeaders": {"entries": [{"name": "Content-Type", "value": "text/html; charset=UTF-8"}]}}, "respondWith": "text", "responseBody": "=<!DOCTYPE html>\n<!DOCTYPE html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"UTF-8\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n    <title>Markdown to HTML</title>\n    <style>\n      main {\n        font-family: Arial, sans-serif;\n        margin: 0;\n        display: flex;\n        justify-content: center;\n        height: 100vh;\n        background-color: #f5f5f5;\n        font-size: 24px;\n      }\n\n      .content-container {\n        text-align: center;\n        border: 1px solid #ddd;\n        border-radius: 8px;\n        padding: 20px;\n        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n        background-color: #fff;\n        max-width: 600px;\n        width: 100%;\n        margin: 20px;\n      }\n\n      #markdown-content {\n        text-align: left;\n        margin-top: 20px;\n      }\n    </style>\n  </head>\n  <body>\n    <main>\n      <div class=\"content-container\">\n        <div id=\"markdown-content\"> {{ $json.message.content?.replace(/\\n/g,'<br/>') }}</div>\n      </div>\n    </main>\n  </body>\n</html>\n"}, "typeVersion": 1}, {"id": "1740cef8-d25b-46f2-a63d-50b86599dbf2", "name": "n8n Form Trigger", "type": "n8n-nodes-base.formTrigger", "position": [760, 900], "webhookId": "c61492e5-73ce-40d4-b758-d5f09da0fb6c", "parameters": {"path": "c61492e5-73ce-40d4-b758-d5f09da0fb6c", "formTitle": "Workflow Documenter", "formFields": {"values": [{"fieldLabel": "Workflow Title", "requiredField": true}, {"fieldLabel": "Workflow Json", "requiredField": true}]}, "responseMode": "responseNode", "formDescription": "Automatically document your n8n workflow"}, "typeVersion": 2}, {"id": "fde56941-46a8-4340-b099-f7e75950b336", "name": "Create input to open ai", "type": "n8n-nodes-base.set", "position": [1180, 900], "parameters": {"fields": {"values": [{"name": "input", "stringValue": "=Workflow Title:  {{ $json['Workflow Title'] }}\n\nWofklow JSON: ```{{ $json['Workflow Json'] }}```\n\n{{ $json.prompt }}  "}]}, "options": {}}, "typeVersion": 3.2}], "pinData": {}, "connections": {"OpenAI": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Create input to open ai", "type": "main", "index": 0}]]}, "n8n Form Trigger": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Create input to open ai": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}}}