{"id": "ReXF4z8ZKcEd6Kea", "meta": {"instanceId": "10e1a946d44026e16f5f1b336bb25f918a0e45738f9dee8024757614581d7d73"}, "name": "dub.co URL Shortener", "tags": [], "nodes": [{"id": "63170148-b769-43b6-9a7a-02baa9f76b02", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1480, 1100], "parameters": {"color": 4, "width": 346.4519761795601, "height": 227.3959699655325, "content": "## Dub.co API Limits:\nDub’s API is capped at 10 requests per second per user."}, "typeVersion": 1}, {"id": "defd82ef-25a0-4aa4-8681-d352e5fe8275", "name": "When clicking \"Test Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [1231, 560], "parameters": {}, "typeVersion": 1}, {"id": "fec7fb8a-3f88-4de9-8392-5c7929a712e7", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "disabled": true, "position": [1480, 460], "parameters": {"color": 4, "width": 826.4578951225271, "height": 605.7992490141105, "content": "## Dub.co API\n**Create** Link. [Based on their API docs](https://dub.co/docs/api-reference/endpoint/create-a-new-link)"}, "typeVersion": 1}, {"id": "8c87aad8-519c-491b-b272-f21dfd5b069f", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1171, 460], "parameters": {"height": 870.5323777622334, "content": "## Control Stack"}, "typeVersion": 1}, {"id": "a6cfe224-95d9-47d6-a900-667eed065264", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [486, 460], "parameters": {"width": 655.6800599837106, "height": 462.29577922809585, "content": "# README\n\n## Dub.co API Workflow Configuration\n| Required | Input Field           | Description                                      |\n|----------|-----------------------|--------------------------------------------------|\n|✓| **`Dub API Key`**     | _API Key for Dub.co integration._                 |\n|✓| **`Long URL`**               | _The long URL to be shortened._                    |\n| | **`Custom Slug`**           | _Slug is the path of shortened URL - default is random 7 characters._               |\n|✓| **`Project Slug`**                  | _Enter Your Dub project slug, The slug for the project to create links for. E.g. for app.dub.co/acme, the project slug is 'acme'._                   |\n| | **`Custom Domain`** | _Custom domain linked to Dub.co._                 |\n\n\n\n\n\n\n\nYou'll need to add the details listed above in the \"API Auth\" node by clicking on it and filling the fields ==>"}, "typeVersion": 1}, {"id": "f3164150-9730-4d20-9aef-9ae7f84e73fc", "name": "API Auth", "type": "n8n-nodes-base.set", "position": [1240, 820], "parameters": {"fields": {"values": [{"name": "Dub API Key", "stringValue": "=<Required: Get your Dub API Key from https://app.dub.co/settings/tokens>"}, {"name": "Long URL", "stringValue": "https://n8n.io"}, {"name": "Custom Slug", "stringValue": "=<Optional: Slug is the path of shortened URL - default is random 7 characters>"}, {"name": "Project Slug", "stringValue": "=<Required: Enter Your Dub project slug, The slug for the project to create links for. E.g. for app.dub.co/acme, the project slug is 'acme'.>"}, {"name": "Custom Domain", "stringValue": "=<Optional: The domain of the short link. If not provided, the primary domain for the project will be used (or dub.sh if the project has no domains)>"}]}, "options": {}}, "typeVersion": 3.2}, {"id": "a2279ab1-6550-43ef-a41d-c24c669bb2b6", "name": "CREATE", "type": "n8n-nodes-base.httpRequest", "notes": "Create Link", "position": [1540, 560], "parameters": {"url": "https://api.dub.co/links", "method": "POST", "options": {"batching": {"batch": {"batchSize": 10, "batchInterval": 60000}}, "redirect": {"redirect": {}}, "response": {"response": {"neverError": true, "fullResponse": true}}, "allowUnauthorizedCerts": true}, "jsonBody": "={\n{{ $ifEmpty(`\"domain\": \"${$json[\"Custom Domain\"] || \"undefined\"}\",`, \"\").replace('\"domain\": \"<Optional: The domain of the short link. If not provided, the primary domain for the project will be used (or dub.sh if the project has no domains)>\",', \"\").replace('\"domain\": \"undefined\",', \"\") }}\n{{ $ifEmpty(`\"key\": \"${$json[\"Custom Slug\"] || \"undefined\"}\",`, \"\").replace('\"key\": \"<Optional: Slug is the path of shortened URL - default is random 7 characters>\",', \"\").replace('\"key\": \"undefined\",', \"\") }}\n  \"url\": \"{{ $json[\"Long URL\"] }}\",\n  \"comments\": \"Updated using N8N.io workflow: {{$workflow.name}}\"\n}", "sendBody": true, "sendQuery": true, "sendHeaders": true, "specifyBody": "json", "queryParameters": {"parameters": [{"name": "projectSlug", "value": "={{ $json[\"Project Slug\"] }}"}]}, "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{ $json['Dub API Key'] }}"}]}}, "notesInFlow": true, "typeVersion": 4.1, "alwaysOutputData": true}, {"id": "970d062f-9206-4ec6-acdd-3fc1cc87db69", "name": "IF Slug available", "type": "n8n-nodes-base.if", "position": [1760, 560], "parameters": {"conditions": {"string": [{"value1": "={{ $json.statusCode }}", "value2": "200", "operation": "regex"}]}}, "typeVersion": 1}, {"id": "d25eedfd-95a7-4c17-8a76-1cdfae6670d1", "name": "RETRIEVE", "type": "n8n-nodes-base.httpRequest", "notes": "Retrieve the link id", "position": [1540, 840], "parameters": {"url": "=https://api.dub.co/links/info", "options": {"batching": {"batch": {"batchSize": 10, "batchInterval": 60000}}, "redirect": {"redirect": {}}, "response": {"response": {"neverError": true, "fullResponse": true}}, "allowUnauthorizedCerts": true}, "sendQuery": true, "sendHeaders": true, "queryParameters": {"parameters": [{"name": "projectSlug", "value": "={{ $('API Auth').item.json[\"Project Slug\"] }}"}, {"name": "key", "value": "={{ $('API Auth').item.json[\"Custom Slug\"] }}"}, {"name": "domain", "value": "={{ $('API Auth').item.json[\"Custom Domain\"] }}"}]}, "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{ $('API Auth').item.json[\"Dub API Key\"] }}"}]}}, "notesInFlow": true, "typeVersion": 4.1, "alwaysOutputData": true}, {"id": "18bf5187-422c-486e-84c4-c2f79855ba25", "name": "UPDATE", "type": "n8n-nodes-base.httpRequest", "notes": "Update Link", "position": [1780, 840], "parameters": {"url": "=https://api.dub.co/links/{{ $json.body.id }}", "method": "PUT", "options": {"batching": {"batch": {"batchSize": 10, "batchInterval": 60000}}, "redirect": {"redirect": {}}, "response": {"response": {"neverError": true, "fullResponse": true}}, "allowUnauthorizedCerts": true}, "jsonBody": "={\n    {{ $ifEmpty(`\"domain\": \"${$('API Auth').item.json[\"Custom Domain\"] || \"undefined\"}\",`, \"\").replace('\"domain\": \"<Optional: The domain of the short link. If not provided, the primary domain for the project will be used (or dub.sh if the project has no domains)>\",', \"\").replace('\"domain\": \"undefined\",', \"\") }}\n{{ $ifEmpty(`\"key\": \"${$('API Auth').item.json[\"Custom Slug\"] || \"undefined\"}\",`, \"\").replace('\"key\": \"<Optional: Slug is the path of shortened URL - default is random 7 characters>\",', \"\").replace('\"key\": \"undefined\",', \"\") }}\n\n  \"url\": \"{{ $('API Auth').item.json[\"Long URL\"] }}\",\n  \"comments\": \"Updated using N8N.io workflow: {{$workflow.name}}\"\n}", "sendBody": true, "sendQuery": true, "sendHeaders": true, "specifyBody": "json", "queryParameters": {"parameters": [{"name": "projectSlug", "value": "={{ $('API Auth').item.json[\"Project Slug\"] }}"}]}, "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{ $('API Auth').item.json[\"Dub API Key\"] }}"}]}}, "notesInFlow": true, "typeVersion": 4.1, "alwaysOutputData": true}, {"id": "c16dc19b-f807-4784-a323-5d790cebe718", "name": "Shortened URL", "type": "n8n-nodes-base.set", "position": [2120, 840], "parameters": {"values": {"string": [{"name": "Shortened URL", "value": "={{ $json.body.shortLink }}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 2}, {"id": "44d31a47-dd84-4b07-a606-2da99a73cad1", "name": "Done", "type": "n8n-nodes-base.set", "position": [1240, 1060], "parameters": {"options": {}}, "typeVersion": 3.2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "3b5edd9c-e373-4dc1-95d0-b320beb47020", "connections": {"CREATE": {"main": [[{"node": "IF Slug available", "type": "main", "index": 0}]]}, "UPDATE": {"main": [[{"node": "Shortened URL", "type": "main", "index": 0}]]}, "API Auth": {"main": [[{"node": "CREATE", "type": "main", "index": 0}]]}, "RETRIEVE": {"main": [[{"node": "UPDATE", "type": "main", "index": 0}]]}, "Shortened URL": {"main": [[{"node": "Done", "type": "main", "index": 0}]]}, "IF Slug available": {"main": [[{"node": "Shortened URL", "type": "main", "index": 0}], [{"node": "RETRIEVE", "type": "main", "index": 0}]]}, "When clicking \"Test Workflow\"": {"main": [[{"node": "API Auth", "type": "main", "index": 0}]]}}}