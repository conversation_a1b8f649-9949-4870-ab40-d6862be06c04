{"id": "82", "name": "Send daily weather updates via a push notification using the Pushcut node", "nodes": [{"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.pushcut", "position": [1050, 420], "parameters": {"additionalFields": {"text": "=Hey! The temperature outside is {{$node[\"OpenWeatherMap\"].json[\"main\"][\"temp\"]}}°C.", "title": "Today's Weather Update"}, "notificationName": "n8n"}, "credentials": {"pushcutApi": "Pushcut Credentials"}, "typeVersion": 1}, {"name": "OpenWeatherMap", "type": "n8n-nodes-base.openWeatherMap", "position": [850, 420], "parameters": {"cityName": "berlin"}, "credentials": {"openWeatherMapApi": "open-weather-map"}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [650, 420], "parameters": {"triggerTimes": {"item": [{"hour": 9}]}}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Cron": {"main": [[{"node": "OpenWeatherMap", "type": "main", "index": 0}]]}, "OpenWeatherMap": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}}}