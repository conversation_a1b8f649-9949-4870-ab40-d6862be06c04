{"id": "PtPKIqDlz5xrrvHP", "meta": {"instanceId": "a2434c94d549548a685cca39cc4614698e94f527bcea84eefa363f1037ae14cd"}, "name": "Sync Todoist tasks to Notion", "tags": [], "nodes": [{"id": "0122196d-e051-4154-9e39-3ddbfe26858f", "name": "On schedule", "type": "n8n-nodes-base.scheduleTrigger", "position": [640, 280], "parameters": {"rule": {"interval": [{"field": "seconds"}]}}, "typeVersion": 1.1}, {"id": "1a15e1cc-cdd5-4a49-aa7a-a0779f858e69", "name": "Get all tasks with specific label", "type": "n8n-nodes-base.todoist", "position": [860, 280], "parameters": {"filters": {"labelId": "send-to-notion"}, "operation": "getAll", "authentication": "oAuth2"}, "credentials": {"todoistOAuth2Api": {"id": "E6PTOAR6ysBeLwCB", "name": "Todoist account"}}, "typeVersion": 2}, {"id": "35b13f4a-da38-4d63-9fbf-7c36c97cbc11", "name": "Add to Notion database", "type": "n8n-nodes-base.notion", "position": [1080, 280], "parameters": {"title": "={{ $json.content }}", "options": {}, "resource": "databasePage", "databaseId": {"__rl": true, "mode": "list", "value": "5a98bd24-dd2b-41a3-b7e2-3b8a9ee21d41", "cachedResultUrl": "https://www.notion.so/5a98bd24dd2b41a3b7e23b8a9ee21d41", "cachedResultName": "My Todoist Tasks"}, "propertiesUi": {"propertyValues": [{"key": "Todoist ID|number", "numberValue": "={{ parseInt($json.id) }}"}]}}, "credentials": {"notionApi": {"id": "5hfWkRpcWCS4KGk5", "name": "n8n-demo-3"}}, "typeVersion": 2.1}, {"id": "f3144751-28b0-48e1-9331-f25f55a5ddf6", "name": "Replace label on task", "type": "n8n-nodes-base.todoist", "position": [1300, 280], "parameters": {"taskId": "={{ $('Get all tasks with specific label').item.json.id }}", "operation": "update", "updateFields": {"labels": ["sent"], "description": "=Notion Link:  {{ $json.url }}\n\n{{ $('Get all tasks with specific label').item.json.description }}"}, "authentication": "oAuth2"}, "credentials": {"todoistOAuth2Api": {"id": "E6PTOAR6ysBeLwCB", "name": "Todoist account"}}, "typeVersion": 2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "14cd25c2-0a7b-45d0-b81e-173052ebdde7", "connections": {"On schedule": {"main": [[{"node": "Get all tasks with specific label", "type": "main", "index": 0}]]}, "Add to Notion database": {"main": [[{"node": "Replace label on task", "type": "main", "index": 0}]]}, "Get all tasks with specific label": {"main": [[{"node": "Add to Notion database", "type": "main", "index": 0}]]}}}