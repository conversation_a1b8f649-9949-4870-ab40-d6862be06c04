{"id": "CoYwFuZTq5kUuiba", "meta": {"instanceId": "14e4c77104722ab186539dfea5182e419aecc83d85963fe13f6de862c875ebfa"}, "name": "Post new Google Calendar events to Telegram", "tags": [], "nodes": [{"id": "be284a6b-7daf-48c8-99af-e939ecb96f32", "name": "Google Calendar Trigger", "type": "n8n-nodes-base.googleCalendarTrigger", "position": [100, 80], "parameters": {"options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "eventCreated", "calendarId": {"__rl": true, "mode": "list", "value": "", "cachedResultName": ""}}, "credentials": {"googleCalendarOAuth2Api": {"id": "", "name": ""}}, "typeVersion": 1}, {"id": "978e80b6-9b18-4fec-87e8-17fa2335ef48", "name": "Telegram", "type": "n8n-nodes-base.telegram", "position": [400, 80], "webhookId": "dbb6a96e-db3b-4827-9455-a91007b89616", "parameters": {"text": "=Event Name:  {{ $json.summary }}\nDescription: {{ $json.description }}\nEvent Location: {{ $json.location }}\nStart Date: {{ $json.start.dateTime }}\nEnd Date: {{ $json.end.dateTime }}\nCreator: {{ $json.creator.email }}\n\n", "chatId": "", "additionalFields": {"appendAttribution": false}}, "typeVersion": 1.2}, {"id": "f8027fbe-2b57-4b5a-a29b-22b9af27c67c", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [0, 0], "parameters": {"color": 6, "width": 640, "height": 260, "content": "## Post new Google Calendar events to Telegram\n"}, "typeVersion": 1}, {"id": "fd1e60e1-5c4a-439b-84fb-26e5da20ba13", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [0, 280], "parameters": {"color": 6, "width": 640, "content": "## Description\nThis n8n workflow automatically sends a Telegram message whenever a new event is added to Google Calendar. It extracts key event details such as event name, description, event creator, start date, end date, and location and forwards them to a specified Telegram chat. This ensures you stay updated on all newly scheduled events directly from Telegram."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "9620d3f6-6324-49f8-b40e-da313f5044fb", "connections": {"Google Calendar Trigger": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}}}