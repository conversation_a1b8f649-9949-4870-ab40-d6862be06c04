{"id": "H95uJY2gjSOsxRps", "meta": {"instanceId": "885b4fb4a6a9c2cb5621429a7b972df0d05bb724c20ac7dac7171b62f1c7ef40", "templateCredsSetupCompleted": true}, "name": "Extract Amazon Best Seller Electronic Information with Bright Data and Google Gemini", "tags": [{"id": "Kujft2FOjmOVQAmJ", "name": "Engineering", "createdAt": "2025-04-09T01:31:00.558Z", "updatedAt": "2025-04-09T01:31:00.558Z"}, {"id": "ddPkw7Hg5dZhQu2w", "name": "AI", "createdAt": "2025-04-13T05:38:08.053Z", "updatedAt": "2025-04-13T05:38:08.053Z"}], "nodes": [{"id": "328c84bb-eef0-41cc-a392-8e58e3599f9b", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [340, -440], "parameters": {}, "typeVersion": 1}, {"id": "b0e35bc1-473f-44b7-8959-9683adabb8b7", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [820, -640], "parameters": {"width": 420, "height": 140, "content": "## LLM Usages\n\nGoogle Gemini Flash Exp model is being used.\n\nInformation Extraction for building the structured data"}, "typeVersion": 1}, {"id": "a0f32fbd-a7d0-4aeb-b930-59480066e87b", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1088, -220], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "YeO7dHZnuGBVQKVZ", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "3e64fe4a-7746-4b56-b845-3359e38648d9", "name": "HTTP Request to fetch the Amazon Best Seller Products", "type": "n8n-nodes-base.httpRequest", "position": [780, -440], "parameters": {"url": "https://api.brightdata.com/request", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "zone", "value": "={{ $json.zone }}"}, {"name": "url", "value": "={{ $json.url }}"}, {"name": "format", "value": "raw"}]}, "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{}]}}, "credentials": {"httpHeaderAuth": {"id": "kdbqXuxIR8qIxF7y", "name": "Header Auth account"}}, "typeVersion": 4.2}, {"id": "8e27bd37-b879-4fb2-bf80-1cd609170600", "name": "Structured Data Extractor", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [1000, -440], "parameters": {"text": "={{ $json.data }}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}, "schemaType": "manual", "inputSchema": "{\n  \"$schema\": \"http://json-schema.org/draft-07/schema#\",\n  \"title\": \"Amazon Bestsellers - Smartphones & Basic Mobiles\",\n  \"type\": \"object\",\n  \"properties\": {\n    \"category\": {\n      \"type\": \"string\",\n      \"example\": \"Smartphones & Basic Mobiles\"\n    },\n    \"description\": {\n      \"type\": \"string\",\n      \"example\": \"Our most popular products based on sales. Updated frequently.\"\n    },\n    \"page\": {\n      \"type\": \"string\",\n      \"example\": \"Page 1 of 2\"\n    },\n    \"bestsellers\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"rank\": {\n            \"type\": \"integer\",\n            \"example\": 1\n          },\n          \"title\": {\n            \"type\": \"string\",\n            \"example\": \"OnePlus Nord CE4 (Dark Chrome, 8GB RAM, 256GB Storage)\"\n          },\n          \"image\": {\n            \"type\": \"string\",\n            \"format\": \"uri\",\n            \"example\": \"https://images-eu.ssl-images-amazon.com/images/I/61g1pqSjAhL._AC_UL300_SR300,200_.jpg\"\n          },\n          \"rating\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"stars\": {\n                \"type\": \"number\",\n                \"example\": 4.2\n              },\n              \"total_ratings\": {\n                \"type\": \"integer\",\n                \"example\": 8051\n              }\n            },\n            \"required\": [\"stars\", \"total_ratings\"]\n          },\n          \"offer\": {\n            \"type\": \"string\",\n            \"example\": \"1 offer from ₹23,998.00\"\n          },\n          \"product_url\": {\n            \"type\": \"string\",\n            \"format\": \"uri\",\n            \"example\": \"https://www.amazon.in/Oneplus-Nord-Chrome-256GB-Storage/dp/B0CX5BZXLF/\"\n          }\n        },\n        \"required\": [\"rank\", \"title\", \"image\", \"rating\", \"offer\", \"product_url\"]\n      }\n    }\n  },\n  \"required\": [\"category\", \"description\", \"page\", \"bestsellers\"]\n}\n"}, "typeVersion": 1}, {"id": "584944a3-a5a8-456f-bd5a-57fc1fd5d69f", "name": "Set Amazon URL with the Bright Data Zone", "type": "n8n-nodes-base.set", "notes": "Set the URL which you are interested to scrap the data", "position": [560, -440], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "1c132dd6-31e4-453b-a8cf-cad9845fe55b", "name": "url", "type": "string", "value": "https://www.amazon.in/gp/bestsellers/electronics/1389432031?product=unlocker&method=api"}, {"id": "0fa387df-2511-4228-b6aa-237cceb3e9c7", "name": "zone", "type": "string", "value": "web_unlocker1"}]}}, "notesInFlow": true, "typeVersion": 3.4}, {"id": "bc2507a8-a9b6-4b47-9bce-2cef9a0d1e3e", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [340, -720], "parameters": {"width": 400, "height": 220, "content": "## Note\n\nDeals with the Amazon Best Seller Electronic data extraction using the Bright Data and LLM for Information Extraction.\n\n**Please make sure to update the \"Set Amazon URL with the Bright Data Zone\" and the Webhook Notification URL**"}, "typeVersion": 1}, {"id": "36a14230-e791-4996-8457-311a496833cd", "name": "Webhook Notifier for structured data extractor", "type": "n8n-nodes-base.httpRequest", "position": [1376, -440], "parameters": {"url": "https://webhook.site/bc804ce5-4a45-4177-a68a-99c80e5c86e6", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "summary", "value": "={{ $json.output }}"}]}}, "typeVersion": 4.2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "2e44bd30-9100-43a9-b177-b15867b8488b", "connections": {"Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Structured Data Extractor", "type": "ai_languageModel", "index": 0}]]}, "Structured Data Extractor": {"main": [[{"node": "Webhook Notifier for structured data extractor", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set Amazon URL with the Bright Data Zone", "type": "main", "index": 0}]]}, "Set Amazon URL with the Bright Data Zone": {"main": [[{"node": "HTTP Request to fetch the Amazon Best Seller Products", "type": "main", "index": 0}]]}, "HTTP Request to fetch the Amazon Best Seller Products": {"main": [[{"node": "Structured Data Extractor", "type": "main", "index": 0}]]}}}