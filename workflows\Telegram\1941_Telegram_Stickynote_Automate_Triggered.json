{"id": "o8HjmolfMilbaEkk", "meta": {"instanceId": "fb924c73af8f703905bc09c9ee8076f48c17b596ed05b18c0ff86915ef8a7c4a"}, "name": "Telegram echo-bot", "tags": [], "nodes": [{"id": "5c7c9e78-60d0-4f6a-929a-a4e77f5e0851", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1000, 120], "parameters": {"width": 727, "height": 391, "content": "## This is a workflow for a Telegram-echo bot\n1. Add your Telegram bot credentials for both nodes\n2. Activate the workflow\n3. Send something to the bot (i.e. a message, a forwarded message, sticker, emoji, voice, file, an image...)\n4. Second node will fetch the incoming JSON object, format it and send back\n\n#### This bot is useful for debugging and learning purposes of the Telegram platform"}, "typeVersion": 1}, {"id": "9f64943e-35a4-4d9f-a77e-ff76cae8bb84", "name": "Listen for incoming events", "type": "n8n-nodes-base.telegramTrigger", "position": [1040, 340], "webhookId": "322dce18-f93e-4f86-b9b1-3305519b7834", "parameters": {"updates": ["*"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "70", "name": "Telegram sdfsdfsdfsdfsfd_bot"}}, "typeVersion": 1}, {"id": "5b890d30-f47e-4cf0-9747-ae9eb14cedff", "name": "Send back the JSON content of the message", "type": "n8n-nodes-base.telegram", "position": [1260, 340], "parameters": {"text": "=```\n{{ JSON.stringify($json, null, 2) }}\n```", "chatId": "={{ $json.message.from.id }}", "additionalFields": {"parse_mode": "<PERSON><PERSON>"}}, "credentials": {"telegramApi": {"id": "70", "name": "Telegram sdfsdfsdfsdfsfd_bot"}}, "typeVersion": 1.1}], "active": true, "pinData": {}, "settings": {"callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1", "saveManualExecutions": true, "saveDataSuccessExecution": "all"}, "versionId": "14d0925e-4b1b-4183-8584-04c9ab715998", "connections": {"Listen for incoming events": {"main": [[{"node": "Send back the JSON content of the message", "type": "main", "index": 0}]]}}}