{"meta": {"instanceId": "cb484ba7b742928a2048bf8829668bed5b5ad9787579adea888f05980292a4a7"}, "nodes": [{"id": "1f506d0f-e999-409c-8456-d77d1771a2f3", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [740, 120], "webhookId": "a8877bd7-8364-4868-9f88-d9080cce0cb1", "parameters": {"path": "slack-trigger", "options": {}, "httpMethod": "POST"}, "typeVersion": 1}, {"id": "d5bdebab-cb97-44b5-8f85-e2bc71c0b7fb", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [220, -100], "parameters": {"color": 7, "width": 446, "height": 321, "content": "## Needed pre-work: Add a Slack App\n1. Visit https://api.slack.com/apps, click on `New App` and choose a name and workspace.\n2. Click on `OAuth & Permissions` and scroll down to Scopes -> Bot token Scopes\n3. Add the `chat:write` scope\n4. Head over to `Slash Commands` and click on `Create New Command`\n5. Use `/idea` as the command\n6. Copy the test URL from the **Webhook** node into `Request URL`\n7. Add whatever feels best to the description and usage hint\n8. Go to `Install app` and click install"}, "typeVersion": 1}, {"id": "fa0734a5-6794-4ba8-9675-b54ba9ddf6e8", "name": "Notion", "type": "n8n-nodes-base.notion", "position": [1620, 20], "parameters": {"title": "={{ $json.body.text }}", "options": {}, "resource": "databasePage", "databaseId": {"__rl": true, "mode": "url", "value": "={{ $('Set me up').first().json['Notion URL'] }}"}, "propertiesUi": {"propertyValues": [{"key": "Creator|rich_text", "textContent": "={{ $json.body.user_name }}"}]}}, "credentials": {"notionApi": {"id": "1exvaAn7wzyBgkXZ", "name": "<PERSON>'s <PERSON><PERSON>red"}}, "typeVersion": 2.1}, {"id": "28116568-f19c-47b3-9cd2-e08032db4dd5", "name": "Switch", "type": "n8n-nodes-base.switch", "position": [1360, 120], "parameters": {"rules": {"values": [{"outputKey": "New idea", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.body.command }}", "rightValue": "/idea"}]}, "renameOutput": true}, {"outputKey": "Add more here", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "25221a2c-18e9-47f6-a112-0edc85b63cda", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.body.command }}", "rightValue": "/some-other-command"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3}, {"id": "8a153fab-dd1a-4108-8522-766b09b4caf3", "name": "Hidden message to add feature details", "type": "n8n-nodes-base.httpRequest", "position": [1840, 20], "parameters": {"url": "={{ $('Webhook').item.json.body.response_url }}", "method": "POST", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "=Thanks for adding the idea `{{ $('Webhook').item.json[\"body\"][\"text\"] }}` <@{{$('Webhook').item.json[\"body\"][\"user_id\"]}}> :rocket: Please make sure to add some details and a hypothesis to it to make it easier for us to work with it.\n\n:point_right: <{{$json[\"url\"]}}|Add your details here>"}]}}, "typeVersion": 3}, {"id": "68d6136b-291f-4e17-b07f-da3672b6622f", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [920, -315], "parameters": {"color": 5, "width": 331, "height": 422.85671270290686, "content": "## Setup\n1. Add a Database in Notion with the columns `Name` and `Creator`\n2. Add your Notion credentials and add the integration to your Notion page.\n3. Fill the setup node below\n4. Create your slack app (*see other sticky*)\n5. Click `Test` workflow and use the `/idea` comment in Slack\n6. Activate the workflow and exchange the Request URL with the production URL from the webhook"}, "typeVersion": 1}, {"id": "4a2d6224-352a-4625-b4ae-bc856b2602fd", "name": "Set me up", "type": "n8n-nodes-base.set", "position": [1020, -40], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9bcc3fa7-a09e-48f0-b4ff-2c78264dec2d", "name": "Notion URL", "type": "string", "value": "https://www.notion.so/n8n/12f1bb41e54345f6bdbe85085a67a5a9?v=72d337e995204017a24aa648edb5e7cc"}]}}, "typeVersion": 3.3}, {"id": "89dc4c0d-7fab-4a6f-b8e9-65a0701c7d49", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1300, 40], "parameters": {"color": 7, "height": 237.2740046838409, "content": "You can easily support more commands, like `/bug` or `/pain` here"}, "typeVersion": 1}], "pinData": {"Webhook": [{"body": {"text": "Some name", "token": "OROQZiopO3NiQVLFg0muEISq", "command": "/idea", "team_id": "TG9695PUK", "user_id": "U047V1J0E7J", "user_name": "<PERSON><PERSON><PERSON>", "api_app_id": "A06MQ8S7QM6", "channel_id": "C04KYPACRJA", "trigger_id": "6718698191332.553213193971.2b472ec4e6e0fb9094507f09a98d01e7", "team_domain": "n8nio", "channel_name": "nik-wf-testing", "response_url": "https://hooks.slack.com/commands/TG9695PUK/6701685292247/8Q0IUAqaVcycw4skTzdYLSx3", "is_enterprise_install": "false"}, "query": {}, "params": {}, "headers": {"host": "internal.users.n8n.cloud", "accept": "application/json,*/*", "x-real-ip": "**********", "user-agent": "Slackbot 1.0 (+https://api.slack.com/robots)", "content-type": "application/x-www-form-urlencoded", "content-length": "420", "accept-encoding": "gzip,deflate", "x-forwarded-for": "**********", "x-forwarded-host": "internal.users.n8n.cloud", "x-forwarded-port": "443", "x-forwarded-proto": "https", "x-slack-signature": "v0=9fb3ff0c0b84fd7ec95a0847b38c365124c8294b451dd29941d8fcd85fbd0eb9", "x-forwarded-server": "3d9f11a36e52", "x-slack-request-timestamp": "1709130534"}}]}, "connections": {"Notion": {"main": [[{"node": "Hidden message to add feature details", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Notion", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Set me up", "type": "main", "index": 0}, {"node": "Switch", "type": "main", "index": 0}]]}}}