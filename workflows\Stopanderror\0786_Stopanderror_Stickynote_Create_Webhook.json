{"meta": {"instanceId": "568298fde06d3db80a2eea77fe5bf45f0c7bb898dea20b769944e9ac7c6c5a80"}, "nodes": [{"id": "99bbf837-2834-4cae-af13-37b6cdf963bb", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [-480, -40], "webhookId": "83f4e1de-2011-487c-a9f7-be6ccbac0782", "parameters": {"path": "83f4e1de-2011-487c-a9f7-be6ccbac0782", "options": {}, "httpMethod": "POST"}, "typeVersion": 2}, {"id": "0f7c13ea-196b-40a7-bf1e-4829caaaba4c", "name": "Stop and Error", "type": "n8n-nodes-base.stopAndError", "position": [180, 60], "parameters": {"errorMessage": "Invalid verification token"}, "typeVersion": 1}, {"id": "7ddb4cfc-5917-4b19-acf8-c7db3eaab56a", "name": "Donation", "type": "n8n-nodes-base.set", "position": [400, -420], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "67607a8e-55e2-46ec-92f5-1c8ef3addf9c", "name": "from_name", "type": "string", "value": "={{ $json.body.from_name }}"}, {"id": "3e6e86ac-b6c2-4b5f-9e33-22367b9fb9e5", "name": "message", "type": "string", "value": "={{ $json.body.message }}"}, {"id": "4973525a-21b0-442c-8919-24c312f3ff0c", "name": "amount", "type": "string", "value": "={{ $json.body.amount }}"}, {"id": "b7e2d9e1-61c2-4ad1-9cbd-d8a754993fbe", "name": "url", "type": "string", "value": "={{ $json.body.email }}"}, {"id": "da26860f-c1c4-4918-9447-ed080e921fe7", "name": "currency", "type": "string", "value": "={{ $json.body.currency }}"}, {"id": "380dbd53-eb04-4659-aa54-b6bde0ba7034", "name": "is_public", "type": "string", "value": "={{ $json.body.is_public }}"}, {"id": "4fd65c21-3043-4513-96b3-d2e11656e94a", "name": "timestamp", "type": "string", "value": "={{ $json.body.timestamp }}"}]}}, "typeVersion": 3.4}, {"id": "9c29ae0e-d80c-4613-ba11-535cc59d5603", "name": "Subscription", "type": "n8n-nodes-base.set", "position": [400, -140], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "886b9cca-15b1-49b4-a123-6f3ceb46279e", "name": "timestamp", "type": "string", "value": "={{ $json.body.timestamp }}"}, {"id": "3c4d9c0e-3cd6-41d2-8223-c48f1dcccedc", "name": "from_name", "type": "string", "value": "={{ $json.body.from_name }}"}, {"id": "7199ad4d-06ad-4bed-939b-e97d6d118d9b", "name": "message", "type": "string", "value": "={{ $json.body.message }}"}, {"id": "270eeac1-b7f9-4cfb-9ac8-b0799b36482e", "name": "amount", "type": "string", "value": "={{ $json.body.amount }}"}, {"id": "dbf3a671-715c-4e29-96d5-2767b6a620d8", "name": "url", "type": "string", "value": "={{ $json.body.url }}"}, {"id": "79ae8427-e5fe-470f-bdc0-df0d8bcbad00", "name": "email", "type": "string", "value": "={{ $json.body.email }}"}, {"id": "90c73e4d-197a-4ba3-b6fb-b7b79b62e69c", "name": "currency", "type": "string", "value": "={{ $json.body.currency }}"}, {"id": "3e23aaad-70f4-429b-bee1-3ae0156aaa86", "name": "is_first_subscription_payment", "type": "string", "value": "={{ $json.body.is_first_subscription_payment }}"}, {"id": "3ca5ca22-d8b7-4a90-a97e-aaed032ef705", "name": "tier_name", "type": "string", "value": "={{ $json.body.tier_name }}"}, {"id": "1347258b-1f6d-4d44-bd64-95fb948cbff9", "name": "is_public", "type": "string", "value": "={{ $json.body.is_public }}"}]}}, "typeVersion": 3.4}, {"id": "a050d88e-bbe8-4ee1-b1c6-31782b5b2f20", "name": "Shop Order", "type": "n8n-nodes-base.set", "position": [400, 140], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9f2d01f6-d172-4aea-b2ea-e64857592191", "name": "from_name", "type": "string", "value": "={{ $json.body.from_name }}"}, {"id": "bbc1fcba-ec29-4599-9d78-4e1a37ecede3", "name": "amount", "type": "string", "value": "={{ $json.body.amount }}"}, {"id": "2ea190ac-700b-4682-9baa-7d733f89b819", "name": "email", "type": "string", "value": "={{ $json.body.email }}"}, {"id": "eb0af9d5-9650-4457-b0fd-44f679972a79", "name": "currency", "type": "string", "value": "={{ $json.body.currency }}"}, {"id": "9acec88b-61d5-4520-bf51-f71b4e2e26f6", "name": "shop_items", "type": "array", "value": "={{ $json.body.shop_items }}"}, {"id": "2e1fb035-b32c-492f-9705-7159ef0b2c5d", "name": "url", "type": "string", "value": "={{ $json.body.url }}"}]}}, "typeVersion": 3.4}, {"id": "f9926fda-9a37-467e-b86b-ce9dbb051a88", "name": "Is new subscriber?", "type": "n8n-nodes-base.if", "position": [620, -140], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "87fbcc71-a0a4-4820-bb67-9551d44e0500", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.is_first_subscription_payment }}", "rightValue": "true"}]}}, "typeVersion": 2.2}, {"id": "b158a59d-5166-4469-b999-5235768855c0", "name": "Prepare", "type": "n8n-nodes-base.set", "position": [-260, -40], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "aabaa149-d74b-464c-bbb6-12e2e8a884d9", "name": "verificationToken", "type": "string", "value": "7dd9d4ef-8412-4add-a0d0-b548ad4564b9"}, {"id": "c2f7a7ce-99b0-44c7-b2cf-1ebf85e0917d", "name": "body", "type": "object", "value": "={{ $json.body.data }}"}]}}, "typeVersion": 3.4}, {"id": "a4fd9607-6d5b-4fbd-a4ce-53e9960887e0", "name": "Check token", "type": "n8n-nodes-base.if", "position": [-40, -40], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "439af86e-c768-4165-ae64-86cd32a07084", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.body.verification_token }}", "rightValue": "={{ $json.verificationToken }}"}]}}, "typeVersion": 2.2}, {"id": "6c34228e-434c-4ab3-b0ce-0b5c1721ffc8", "name": "Check type", "type": "n8n-nodes-base.switch", "position": [180, -140], "parameters": {"rules": {"values": [{"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "edd5eaa2-60c7-459a-9846-b952b390b1db", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.body.type }}", "rightValue": "Donation"}]}}, {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "0cc7f0bf-4d1b-45a5-88ed-5b84050222f8", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.body.type }}", "rightValue": "Subscription"}]}}, {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a1b74233-7700-434b-be5c-76129c4cd88c", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.body.type }}", "rightValue": "Shop Order"}]}}]}, "options": {}}, "typeVersion": 3.2}, {"id": "87fd0134-5eb4-4c47-acb5-137f24e819f4", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-300, -140], "parameters": {"color": 6, "width": 200, "height": 260, "content": "### Set verification token\nSet your Ko-fi  verification token in this node. Available [here](https://ko-fi.com/manage/webhooks)."}, "typeVersion": 1}, {"id": "78ac15cb-1336-424e-a235-6e7a66090b9d", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-520, -140], "parameters": {"color": 6, "width": 200, "height": 260, "content": "### Setup your webhook\nFind your webhook URL in this node and set it  [here](https://ko-fi.com/manage/webhooks)."}, "typeVersion": 1}, {"id": "30892b66-66fb-4926-b817-e280cbadf5ea", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [360, -520], "parameters": {"color": 7, "width": 540, "height": 260, "content": "### We received a donation\nDo your thing."}, "typeVersion": 1}, {"id": "3b849d93-ffa5-4dfe-8743-d43ca7d06e60", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [360, -240], "parameters": {"color": 7, "width": 540, "height": 260, "content": "### We received a payment for a subscription\nDo your thing."}, "typeVersion": 1}, {"id": "bff1913d-1c18-4447-8448-aa059d7b020f", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [360, 40], "parameters": {"color": 7, "width": 540, "height": 260, "content": "### We received a shop order\nDo your thing."}, "typeVersion": 1}, {"id": "07da890d-5cc8-4f51-b0c2-c50831c84a64", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-520, -560], "parameters": {"width": 860, "height": 400, "content": "## Receive and handle Ko-fi payment webhooks \nThis workflow receives [Ko-fi payment webhooks](https://ko-fi.com/manage/webhooks), checks the verification token and then check what kind of payment it is.\n\n### Set up\n1. Edit the `Webhook` node and find your webhook URL.\n2. Go to your [Ko-fi webhooks settings](https://ko-fi.com/manage/webhooks) and set your URL\n3. Get your `verification token` from the same page (under advanced) and set it in the `Prepare` node\n4. Enable your workflow and test it from [Ko-fi webhooks settings](https://ko-fi.com/manage/webhooks)\n5. Profit 🎉\n\n**👋 Hello! I'm Audun / xqus** \n🔗 My work: [xqus.com](https://xqus.com)\n💸 n8n shop: [xqus.gumroad.com](https://xqus.gumroad.com)\n\n### Want to trigger my workflow?\nSupport my work on [Ko-fi](https://ko-fi.com/xquscom) 💸"}, "typeVersion": 1}], "pinData": {}, "connections": {"Prepare": {"main": [[{"node": "Check token", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Prepare", "type": "main", "index": 0}]]}, "Check type": {"main": [[{"node": "Donation", "type": "main", "index": 0}], [{"node": "Subscription", "type": "main", "index": 0}], [{"node": "Shop Order", "type": "main", "index": 0}]]}, "Check token": {"main": [[{"node": "Check type", "type": "main", "index": 0}], [{"node": "Stop and Error", "type": "main", "index": 0}]]}, "Subscription": {"main": [[{"node": "Is new subscriber?", "type": "main", "index": 0}]]}}}