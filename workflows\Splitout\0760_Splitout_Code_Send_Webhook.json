{"meta": {"instanceId": "cb484ba7b742928a2048bf8829668bed5b5ad9787579adea888f05980292a4a7", "templateCredsSetupCompleted": true}, "nodes": [{"id": "e893e48c-1b69-413a-90d7-ad6ce5987e7c", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-180, -60], "parameters": {}, "typeVersion": 1}, {"id": "1c42e95b-705d-43ae-91ce-1029334b9e9a", "name": "Retrieve detailed call data", "type": "n8n-nodes-base.httpRequest", "position": [60, 40], "parameters": {"url": "https://api.gong.io/v2/calls/extensive", "options": {"fullResponse": true}, "requestMethod": "POST", "authentication": "genericCredentialType", "jsonParameters": true, "genericAuthType": "httpHeaderAuth", "bodyParametersJson": "={\n  \"contentSelector\": {\n    \"context\": \"Extended\",\n    \"contextTiming\": [\"Now\", \"TimeOfCall\"],\n    \"exposedFields\": {\n      \"collaboration\": {\n        \"publicComments\": true\n      },\n      \"content\": {\n        \"pointsOfInterest\": true,\n        \"structure\": true,\n        \"topics\": true,\n        \"trackers\": true\n      },\n      \"interaction\": {\n        \"personInteractionStats\": true,\n        \"questions\": true,\n        \"speakers\": true,\n        \"video\": true\n      },\n      \"media\": false,\n      \"parties\": true\n    }\n  },\n  \"filter\": {\n    \"callIds\": [\"{{ $json['calldata[0].calls'].id }}\"]\n  }\n}"}, "credentials": {"httpHeaderAuth": {"id": "Bz7PHFY0lgEhLsC0", "name": "G<PERSON>lio <PERSON>"}}, "typeVersion": 2}, {"id": "69c9ef1a-9ef4-4c3f-ab62-a5c9b2a10a4e", "name": "Get transcript", "type": "n8n-nodes-base.httpRequest", "position": [60, -140], "parameters": {"url": "https://api.gong.io/v2/calls/transcript?callIds=1807130744801961509", "options": {"fullResponse": true}, "requestMethod": "POST", "authentication": "genericCredentialType", "jsonParameters": true, "genericAuthType": "httpHeaderAuth", "bodyParametersJson": "={\"filter\":{\"callIds\":[\"{{ $json['calldata[0].calls'].id }}\"]}}"}, "credentials": {"httpHeaderAuth": {"id": "Bz7PHFY0lgEhLsC0", "name": "G<PERSON>lio <PERSON>"}}, "typeVersion": 2}, {"id": "a9643d2c-6245-4c40-92ee-49eb667e3348", "name": "Join Transcript to String", "type": "n8n-nodes-base.set", "position": [260, -140], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c9828e0c-fce4-487d-b5cb-bff625cb7c8e", "name": "Conversation", "type": "array", "value": "={{ $jmespath($json.body.callTranscripts, '[].transcript[].{\"speaker\": speakerId, \"text\": sentences[].text}') }}"}]}}, "typeVersion": 3.4}, {"id": "ce7cce2a-95b2-4d74-865d-d1af028e16de", "name": "Isolate Notion Data", "type": "n8n-nodes-base.set", "position": [2720, -100], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "ee14c39a-1590-4262-b5ab-36640a6e3c31", "name": "metaData.CompanyName", "type": "string", "value": "={{ $json.sfOpp[0].Name }}"}, {"id": "0d323985-076c-456f-bf4c-d9520b07f73d", "name": "Attendees.internal", "type": "array", "value": "={{ $jmespath($json.gongData[0].parties, '[?affiliation==`Internal`].emailAddress') }}"}, {"id": "ee040180-fce4-4d68-a406-26a88a383c14", "name": "metaData.title", "type": "string", "value": "={{ $json.gongData[0].metaData.title }}"}, {"id": "dea503f9-d575-4804-bbe7-0dcf7d5fbea4", "name": "metaData.started", "type": "string", "value": "={{ $json.gongData[0].metaData.started }}"}, {"id": "91fa2545-6a02-43e6-b893-4d3133540a5c", "name": "metaData.GongCallID", "type": "string", "value": "={{ $json.gongData[0].metaData.id }}"}, {"id": "c0cbfa8b-40d1-4838-a375-88ea8eb85170", "name": "metaData.url", "type": "string", "value": "={{ $json.gongData[0].metaData.url }}"}, {"id": "d10a0184-f17c-4fd6-aed5-72656e15f856", "name": "Conversation", "type": "string", "value": "={{ $json.gongData[0].conversationText }}"}, {"id": "02eb0113-7e52-4931-bd10-3f2bee87d984", "name": "Attendees.external", "type": "array", "value": "={{ $jmespath($json.gongData[0].parties, '[?affiliation==`External` || affiliation==`Unknown`].emailAddress') }}"}, {"id": "c2183c7b-d552-4a16-bb08-c9ed247f8111", "name": "Attendees.externalNames", "type": "array", "value": "={{ $jmespath($json.gongData[0].parties, '[?affiliation==`External` || affiliation==`Unknown`].name') }}"}, {"id": "a232bd40-ae56-4c12-8b3f-9062d4880415", "name": "Attendees.internalNames", "type": "array", "value": "={{ $jmespath($json.gongData[0].parties, '[?affiliation==`Internal`].name') }}"}, {"id": "99f7143e-af6c-45d2-b3a1-c5169c6632eb", "name": "metaData.Integrations", "type": "string", "value": "={{ $('Execute Workflow Trigger').item.json['calldata[1].integrations'] }}"}, {"id": "7fe14a89-5fda-4594-8b5a-6fbd8a519db9", "name": "metaData.Competitors", "type": "string", "value": "={{ $('Execute Workflow Trigger').item.json['calldata[2].competitors'] }}"}, {"id": "29fb3dbe-071c-4b02-9dd9-afa4c3a4ad8f", "name": "metaData.domain", "type": "string", "value": "={{ \n  (() => {\n    // List of known free email domains\n    const freeEmailDomains = [\n      'gmail.com',\n      'yahoo.com',\n      'hotmail.com',\n      'outlook.com',\n      'aol.com',\n      'icloud.com',\n      'mail.com',\n      'yandex.com',\n      'protonmail.com'\n    ];\n\n    // Extract email addresses using JMESPath\n    const emailAddresses = $jmespath($json.gongData[0].parties, '[?affiliation==`External` || affiliation==`Unknown`].emailAddress');\n\n    // Function to extract the domain from an email address\n    const extractDomain = (email) => email.match(/@([\\w.-]+)/)?.[1];\n\n    // Filter out free email domains\n    const companyDomains = emailAddresses\n      .map(extractDomain)\n      .filter(domain => domain && !freeEmailDomains.includes(domain.toLowerCase()));\n\n    // Return the first non-free domain or \"Unknown\" if none are found\n    return companyDomains[0] || 'Unknown';\n  })()\n}}"}, {"id": "b28eb61e-6052-4022-9d31-447dbf877982", "name": "sfOpp", "type": "array", "value": "={{ $json.sfOpp }}"}]}}, "typeVersion": 3.4}, {"id": "38574bd1-82f3-4499-9369-9241e41b35d1", "name": "Join Affiliation", "type": "n8n-nodes-base.code", "position": [740, -120], "parameters": {"jsCode": "// Retrieve input data from all items\nconst inputData = $input.all();\nconst originalJson = inputData[0].json; // Get the original JSON data\nconst conversation = originalJson.Conversation;\nconst parties = originalJson.parties;\n\n// Create a mapping of speakerId to affiliation\nconst affiliationMap = {};\nparties.forEach(party => {\n  affiliationMap[party.speakerId] = party.affiliation;\n});\n\n// Replace speakerId with affiliation in the conversation data\nconst updatedConversation = conversation.map(entry => {\n  const affiliation = affiliationMap[entry.speaker] || 'Unknown'; // Fallback to 'Unknown' if not found\n  return {\n    ...entry,\n    speaker: affiliation, // Replace speakerId with affiliation\n  };\n});\n\n// Return the updated conversation along with the original JSON data\nreturn [{ json: { ...originalJson, updatedConversation } }];\n"}, "typeVersion": 2}, {"id": "15809205-cb1d-4d83-8c67-35ab486071b2", "name": "Join conversation", "type": "n8n-nodes-base.code", "position": [940, -120], "parameters": {"jsCode": "// Retrieve the original JSON data\nconst originalJson = $json;\nconst conversation = originalJson.updatedConversation;\n\n// Create an array to hold the formatted lines\nconst formattedLines = [];\n\n// Iterate over each entry in the conversation\nconversation.forEach(entry => {\n  const speaker = entry.speaker;\n  const texts = entry.text;\n\n  // Iterate over each text item and format it as \"speaker: text\"\n  texts.forEach(line => {\n    formattedLines.push(`${speaker}: ${line}`);\n  });\n});\n\n// Join the formatted lines with newline characters\nconst result = formattedLines.join('\\n');\n\n// Return the original JSON data along with the new conversationText field\nreturn [{ json: { ...originalJson, conversationText: result } }];\n"}, "typeVersion": 2}, {"id": "1ac9e862-ddf2-4cd5-9339-c69061182231", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-600, -500], "parameters": {"width": 340, "height": 820, "content": "![Callforge](https://uploads.n8n.io/templates/callforgeshadow.png)\n## CallForge - The AI Gong Sales Call Processor\nCallForge allows you to extract important information for different departments from your Sales Gong Calls. \n\n### Transcript PreProcessor\nThis workflow preps the call transcripts to pass into the call processor. It starts by using the code node to separate the different speakers into either Internal or External speaker. It also pulls data from Salesforce to enrich the call data by pulling things such as company name. "}, "typeVersion": 1}, {"id": "7d8f99e2-13c7-4bf2-becc-c7b5c663028d", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-240, -340], "parameters": {"color": 7, "width": 720, "height": 660, "content": "## Get Gong Transcript and Call Details\nThe transcript is to pass into the AI prompt, but needs to be transformed first. The Call details provide the Prompt with metadata."}, "typeVersion": 1}, {"id": "1454276d-46e6-40b2-9494-c9c380f3eaa1", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [500, -340], "parameters": {"color": 7, "width": 580, "height": 660, "content": "## Format Call Transcript \nHere we join the call transcript together and then set the speaker as either Internal (for our sales team) or External (for our customers). "}, "typeVersion": 1}, {"id": "d7fa6f56-8234-4995-b559-4809095efcb4", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1100, -340], "parameters": {"color": 7, "width": 1320, "height": 780, "content": "## Enrich Call Data\nHere we get the Pipedrive ID using the email domain and use that to search pipedrive for the customer. We also pass the domain into the People Data Labs api to get location data. "}, "typeVersion": 1}, {"id": "b5274357-4e45-4d8b-938d-b3c66f98c82f", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [2440, -340], "parameters": {"color": 7, "width": 480, "height": 660, "content": "## Extract Final Data Blob\nHere we merge the final outputs and get rid of anything we don't need for the final AI prompt. "}, "typeVersion": 1}, {"id": "a940a941-f9e2-4449-895f-3268e2203a1e", "name": "Extract SF Opp Data", "type": "n8n-nodes-base.set", "position": [1700, 80], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "64f7f8ec-3c1c-4743-9e5b-6bb5d385e9d2", "name": "SFOppId", "type": "string", "value": "={{ $json.Id }}"}, {"id": "85629904-617a-4a5f-87a3-72f2349cdf99", "name": "OppType", "type": "string", "value": "={{ $json.Type }}"}, {"id": "f6ec091d-0784-4000-ad49-3bb6ece375ca", "name": "LeadSource", "type": "string", "value": "={{ $json.LeadSource }}"}, {"id": "a3fd520e-3577-4c2d-a09a-ad3bc76e0bd7", "name": "IsClosed", "type": "boolean", "value": "={{ $json.IsClosed }}"}, {"id": "8a1fac85-5f1b-4ab2-86ea-586df1e2af2b", "name": "IsWon", "type": "boolean", "value": "={{ $json.<PERSON>on }}"}, {"id": "0f86f2a2-94bb-412a-b831-974f2528fca3", "name": "sfStage", "type": "string", "value": "={{ $json.StageName }}"}, {"id": "f455d38b-d48a-483c-b0d9-def9514741ef", "name": "companyAccountId", "type": "string", "value": "={{ $json.AccountId }}"}, {"id": "1eb560db-3dd8-46cb-993d-0e370e25222f", "name": "usingn8n", "type": "string", "value": "={{ $json.n8n_experience__c }}"}, {"id": "e1d251e3-40e5-4b63-bbc3-c45e503bb108", "name": "ForecastCategory", "type": "string", "value": "={{ $json.ForecastCategory }}"}]}}, "typeVersion": 3.4}, {"id": "0b2b5078-96b5-423c-82d1-278f013ecdff", "name": "Extract SF Opp Data1", "type": "n8n-nodes-base.set", "position": [1880, 260], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "261c0f53-82d1-4deb-ae52-09ea342d0f88", "name": "Employees", "type": "string", "value": "={{ $json.Employees_Bucket__c }}"}, {"id": "ca1c9890-4a7d-43c6-b7ad-bf1d522574a7", "name": "Name", "type": "string", "value": "={{ $json.Name }}"}]}}, "typeVersion": 3.4}, {"id": "fec915a1-10ea-4be6-a15f-cea0ae837633", "name": "Get Opp Data", "type": "n8n-nodes-base.salesforce", "position": [1460, 80], "parameters": {"resource": "opportunity", "operation": "get", "opportunityId": "={{ $('Execute Workflow Trigger').item.json[\"calldata[0].calls\"].sfOpp }}"}, "credentials": {"salesforceOAuth2Api": {"id": "Ykybxuyh0jK0o3qH", "name": "Angel SF Creds v3"}}, "typeVersion": 1}, {"id": "793127ea-d1c7-4f29-a536-c87ece9d6601", "name": "Get account data", "type": "n8n-nodes-base.salesforce", "position": [1700, 260], "parameters": {"resource": "account", "accountId": "={{ $json.AccountId }}", "operation": "get"}, "credentials": {"salesforceOAuth2Api": {"id": "Ykybxuyh0jK0o3qH", "name": "Angel SF Creds v3"}}, "typeVersion": 1}, {"id": "249ef11d-47b3-415c-aac0-13437c1fd5c8", "name": "Extract Call Data", "type": "n8n-nodes-base.splitOut", "position": [260, 40], "parameters": {"options": {}, "fieldToSplitOut": "body.calls"}, "typeVersion": 1}, {"id": "a572d7e8-6613-4f46-8abf-9a254f22cfc1", "name": "Merge call and transcript Data", "type": "n8n-nodes-base.merge", "position": [540, -120], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3}, {"id": "1bcbafc1-5ef5-43a4-af2a-9689888fc086", "name": "Aggregate Gong Call Transcript", "type": "n8n-nodes-base.aggregate", "position": [1720, -120], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "gongData"}, "typeVersion": 1}, {"id": "df307a52-512d-4397-8d22-a8a51a06fe21", "name": "Get External Attendees Emails", "type": "n8n-nodes-base.set", "position": [1280, 80], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "0a813814-2e7d-40e0-961f-ba59baf5ece5", "name": "externalAttendees", "type": "array", "value": "={{ $jmespath($json.parties, '[?affiliation==`External` || affiliation==`Unknown`].emailAddress') }}"}]}}, "typeVersion": 3.4}, {"id": "a4c7450e-5ad6-4e2f-ab72-0f56ae1390c1", "name": "Combine Salesforce Opp Data", "type": "n8n-nodes-base.merge", "position": [2060, 100], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3}, {"id": "8c201ee7-16f7-4c05-8f6c-d3543c4445e0", "name": "Aggregate Salesforce data", "type": "n8n-nodes-base.aggregate", "position": [2260, 100], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "sfOpp"}, "typeVersion": 1}, {"id": "735173b9-cec1-43b3-94c5-13dc368473dd", "name": "Merge Enriched Transcript Data", "type": "n8n-nodes-base.merge", "position": [2520, -100], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3}], "pinData": {}, "connections": {"Get Opp Data": {"main": [[{"node": "Extract SF Opp Data", "type": "main", "index": 0}, {"node": "Get account data", "type": "main", "index": 0}]]}, "Get transcript": {"main": [[{"node": "Join Transcript to String", "type": "main", "index": 0}]]}, "Get account data": {"main": [[{"node": "Extract SF Opp Data1", "type": "main", "index": 0}]]}, "Join Affiliation": {"main": [[{"node": "Join conversation", "type": "main", "index": 0}]]}, "Extract Call Data": {"main": [[{"node": "Merge call and transcript Data", "type": "main", "index": 1}]]}, "Join conversation": {"main": [[{"node": "Get External Attendees Emails", "type": "main", "index": 0}, {"node": "Aggregate Gong Call Transcript", "type": "main", "index": 0}]]}, "Extract SF Opp Data": {"main": [[{"node": "Combine Salesforce Opp Data", "type": "main", "index": 0}]]}, "Extract SF Opp Data1": {"main": [[{"node": "Combine Salesforce Opp Data", "type": "main", "index": 1}]]}, "Execute Workflow Trigger": {"main": [[{"node": "Get transcript", "type": "main", "index": 0}, {"node": "Retrieve detailed call data", "type": "main", "index": 0}]]}, "Aggregate Salesforce data": {"main": [[{"node": "Merge Enriched Transcript Data", "type": "main", "index": 1}]]}, "Join Transcript to String": {"main": [[{"node": "Merge call and transcript Data", "type": "main", "index": 0}]]}, "Combine Salesforce Opp Data": {"main": [[{"node": "Aggregate Salesforce data", "type": "main", "index": 0}]]}, "Retrieve detailed call data": {"main": [[{"node": "Extract Call Data", "type": "main", "index": 0}]]}, "Get External Attendees Emails": {"main": [[{"node": "Get Opp Data", "type": "main", "index": 0}]]}, "Aggregate Gong Call Transcript": {"main": [[{"node": "Merge Enriched Transcript Data", "type": "main", "index": 0}]]}, "Merge Enriched Transcript Data": {"main": [[{"node": "Isolate Notion Data", "type": "main", "index": 0}]]}, "Merge call and transcript Data": {"main": [[{"node": "Join Affiliation", "type": "main", "index": 0}]]}}}