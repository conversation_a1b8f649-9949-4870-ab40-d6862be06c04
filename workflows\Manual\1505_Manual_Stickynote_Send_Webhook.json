{"id": "IjQRdNu2ItwNnGL2", "meta": {"instanceId": "18735a589159672fb9dbd8b6f953d0efdca888157c3b8b26943fb0e0e7c1edbb", "templateCredsSetupCompleted": true}, "name": "[hiroshidigital.com] Send Message In Larksuite", "tags": [{"id": "96KbUn85yy8jivyf", "name": "Creator", "createdAt": "2024-10-22T04:22:34.463Z", "updatedAt": "2024-10-22T04:22:34.463Z"}], "nodes": [{"id": "9fd838b3-18f7-4056-bbb9-8a9fd843590b", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [240, 220], "parameters": {}, "typeVersion": 1}, {"id": "7afa44a2-28ec-4e3d-a8bd-a63721648eb2", "name": "Get Lark Token", "type": "n8n-nodes-base.httpRequest", "position": [640, 220], "parameters": {"url": "https://open.larksuite.com/open-apis/auth/v3/tenant_access_token/internal", "method": "POST", "options": {}, "jsonBody": "={\n  \"app_id\": \"{{ $json.app_id }}\",\n  \"app_secret\": \"{{ $json.app_secret }}\"\n} ", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "typeVersion": 4.1}, {"id": "4e1fdbef-b881-445c-90ce-95bc9b745772", "name": "Input", "type": "n8n-nodes-base.set", "position": [440, 220], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "322bfa44-ee2a-4ddf-b747-0f7f3405e294", "name": "app_id", "type": "string", "value": "cli_8cdb09dec256ca40"}, {"id": "c8faab22-235b-412c-8dc8-8142c6e2e0c4", "name": "app_secret", "type": "string", "value": "H5SEZr8O67zuqdIdBKSPhTkoeEBCRNy4"}, {"id": "121fcf72-2a13-4082-a66b-47d56bd4a675", "name": "chat_id", "type": "string", "value": "oc_1d97ee99bffdce243a95b4ebe3ddef7a"}, {"id": "c22bf4f3-eac7-4c04-8b2e-8c0e5011bc1e", "name": "text", "type": "string", "value": "https://hiroshidigital.com/"}]}}, "typeVersion": 3.4}, {"id": "92a62eef-e8ec-4e31-b70e-a80dd83d3bba", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [380, 40], "parameters": {"content": "You can get app_id and app_secret in Lark here: https://open.larksuite.com/"}, "typeVersion": 1}, {"id": "9cde6452-7221-4d43-9e68-afa70fdebc27", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [760, 40], "parameters": {"content": "You can get chat_id https://open.larksuite.com/api-explorer/"}, "typeVersion": 1}, {"id": "87d2cc29-6318-4fb7-b430-f4b825649133", "name": "Send Message", "type": "n8n-nodes-base.httpRequest", "position": [840, 220], "parameters": {"url": "https://open.larksuite.com/open-apis/message/v4/send/", "method": "POST", "options": {}, "jsonBody": "={\n  \"chat_id\": \"{{ $('Input').item.json.chat_id }}\",\n  \"msg_type\": \"text\",\n  \"content\": {\n      \"text\": \"{{ $('Input').item.json.text }}\"\n  }\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "srBVlMVQpuZrtnXn", "name": "Header <PERSON>"}}, "typeVersion": 4.1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "ecf9cc74-9aa6-4fa0-b887-f41dc47f5632", "connections": {"Input": {"main": [[{"node": "Get Lark Token", "type": "main", "index": 0}]]}, "Get Lark Token": {"main": [[{"node": "Send Message", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Input", "type": "main", "index": 0}]]}}}