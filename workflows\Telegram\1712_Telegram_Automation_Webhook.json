{"name": "Ultimate Personal Assistant", "nodes": [{"parameters": {"promptType": "define", "text": "={{ $json.text }}", "options": {"systemMessage": "=# Overview\nYou are the ultimate personal assistant. Your job is to send the user's query to the correct tool. You should never be writing emails, or creating even summaries, you just need to call the correct tool.\n\n## Tools\n- emailAgent: Use this tool to take action in email\n- calendarAgent: Use this tool to take action in calendar\n- contactAgent: Use this tool to get, update, or add contacts\n- contentCreator: Use this tool to create blog posts\n- Tavily: Use this tool to search the web\n\n## Rules\n- Some actions require you to look up contact information first. For the following actions, you must get contact information and send that to the agent who needs it:\n  - sending emails\n  - drafting emails\n  - creating calendar event with attendee\n\n## Examples\n1) \n- Input: send an email to nate herkelman asking him what time he wants to leave\n  - Action: Use contactAgent to get nate herkelman's email\n  - Action: Use emailAgent to send the email. You will pass the tool a query like \"send nate herkelman an email to ask what time he wants to leave. here is his email: [email address]\n- Output: The email has been sent to <PERSON>. Anything else I can help you with?\n\n\n## Final Reminders\nHere is the current date/time: {{ $now }}"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [440, -100], "id": "f1344298-a586-4a63-a113-b9581ae93c45", "name": "Ultimate Assistant"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "gpt-4o"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [120, 200], "id": "46def81a-7dcd-4a07-8642-e6035af87a7d", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "BP9v81AwJlpYGStD", "name": "OpenAi account"}}}, {"parameters": {"name": "emailAgent", "description": "Call this tool for any email actions.", "workflowId": {"__rl": true, "value": "C3hLlOS4O6ZJtVFy", "mode": "list", "cachedResultName": "🤖Email Agent"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [340, 360], "id": "4c445d15-99d3-465f-a8bc-610729fa0f65", "name": "Email Agent"}, {"parameters": {"name": "contactAgent", "description": "Call this tool for any contact related actions.", "workflowId": {"__rl": true, "value": "IsSUyrla7wc1cDLE", "mode": "list", "cachedResultName": "🤖Contact Agent"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [620, 380], "id": "a4de4692-0a12-438a-826b-0eb45fa0bb0b", "name": "Contact Agent"}, {"parameters": {"name": "contentCreator", "description": "Call this tool to create blog posts.", "workflowId": {"__rl": true, "value": "WWSu94V939ATcqvi", "mode": "list", "cachedResultName": "🤖Content Creator Agent"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [740, 340], "id": "ad8218b3-3a89-455a-a49a-dbf847a442fc", "name": "Content Creator Agent"}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('Telegram Trigger').item.json.message.chat.id }}"}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [220, 300], "id": "88bf60e1-303b-4c0f-91df-340f9d33ae59", "name": "Window Buffer Memory"}, {"parameters": {"toolDescription": "Use this tool to search the internet", "method": "POST", "url": "https://api.tavily.com/search", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n    \"api_key\": \"your-api-key\",\n    \"query\": \"{searchTerm}\",\n    \"search_depth\": \"basic\",\n    \"include_answer\": true,\n    \"topic\": \"news\",\n    \"include_raw_content\": true,\n    \"max_results\": 3\n} ", "placeholderDefinitions": {"values": [{"name": "searchTerm", "description": "What the user has requested to search the internet for", "type": "string"}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [860, 280], "id": "174c3435-fcb6-4579-92b1-37ea24c5e4aa", "name": "<PERSON><PERSON>"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolCalculator", "typeVersion": 1, "position": [960, 200], "id": "b7920e6a-f44b-4f3c-893c-b3643628261e", "name": "Calculator"}, {"parameters": {"name": "calendarAgent", "description": "Call this tool for any calendar action.", "workflowId": {"__rl": true, "value": "0NtlJ41IozGhtFa6", "mode": "list", "cachedResultName": "🤖Calendar Agent"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [480, 380], "id": "88bd92de-d580-40c8-bc3c-44215004e8cc", "name": "Calendar Agent"}, {"parameters": {"updates": ["message"], "additionalFields": {}}, "id": "29656d2a-6561-482d-8eb4-316666626cef", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.1, "position": [-240, -100], "webhookId": "99eab1a0-569d-4f0f-a49e-578a02abfe63", "credentials": {"telegramApi": {"id": "9jQWan3cOz3tE62s", "name": "Telegram account 2"}}}, {"parameters": {"assignments": {"assignments": [{"id": "fe7ecc99-e1e8-4a5e-bdd6-6fce9757b234", "name": "text", "value": "={{ $json.message.text }}", "type": "string"}]}, "options": {}}, "id": "1eb55d45-2431-4315-9d3b-f794c6466d34", "name": "Set 'Text'", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [180, -40]}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.message.voice.file_id }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Voice"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "8c844924-b2ed-48b0-935c-c66a8fd0c778", "leftValue": "={{ $json.message.text }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Text"}]}, "options": {}}, "id": "e76366db-6cb2-464a-8997-fd21d275795f", "name": "Switch", "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-80, -100]}, {"parameters": {"chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "text": "={{ $json.output }}", "additionalFields": {"appendAttribution": false}}, "id": "49d41b42-7ce7-42c6-b10e-7767f27b7c17", "name": "Response", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [900, -100], "webhookId": "5dced4b9-5066-4036-a4d4-14fc07edd53c", "credentials": {"telegramApi": {"id": "9jQWan3cOz3tE62s", "name": "Telegram account 2"}}}, {"parameters": {"resource": "file", "fileId": "={{ $json.message.voice.file_id }}"}, "id": "add76827-0115-43f9-b292-93f942fdf4ab", "name": "Download File", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [120, -200], "webhookId": "83bb7385-33f6-4105-8294-1a91c0ebbee5", "credentials": {"telegramApi": {"id": "9jQWan3cOz3tE62s", "name": "Telegram account 2"}}}, {"parameters": {"resource": "audio", "operation": "transcribe", "options": {}}, "id": "b01fcf5f-3dfa-420f-a5d6-706adc545a5f", "name": "Transcribe", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.6, "position": [240, -200], "credentials": {"openAiApi": {"id": "BP9v81AwJlpYGStD", "name": "OpenAi account"}}}], "pinData": {}, "connections": {"OpenAI Chat Model": {"ai_languageModel": [[{"node": "Ultimate Assistant", "type": "ai_languageModel", "index": 0}]]}, "Email Agent": {"ai_tool": [[{"node": "Ultimate Assistant", "type": "ai_tool", "index": 0}]]}, "Contact Agent": {"ai_tool": [[{"node": "Ultimate Assistant", "type": "ai_tool", "index": 0}]]}, "Content Creator Agent": {"ai_tool": [[{"node": "Ultimate Assistant", "type": "ai_tool", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "Ultimate Assistant", "type": "ai_memory", "index": 0}]]}, "Tavily": {"ai_tool": [[{"node": "Ultimate Assistant", "type": "ai_tool", "index": 0}]]}, "Calculator": {"ai_tool": [[{"node": "Ultimate Assistant", "type": "ai_tool", "index": 0}]]}, "Calendar Agent": {"ai_tool": [[{"node": "Ultimate Assistant", "type": "ai_tool", "index": 0}]]}, "Telegram Trigger": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Download File", "type": "main", "index": 0}], [{"node": "Set 'Text'", "type": "main", "index": 0}]]}, "Set 'Text'": {"main": [[{"node": "Ultimate Assistant", "type": "main", "index": 0}]]}, "Ultimate Assistant": {"main": [[{"node": "Response", "type": "main", "index": 0}]]}, "Download File": {"main": [[{"node": "Transcribe", "type": "main", "index": 0}]]}, "Transcribe": {"main": [[{"node": "Ultimate Assistant", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "31076a3e-169a-4a4e-ad8e-30527b3630ac", "meta": {"templateCredsSetupCompleted": true, "instanceId": "95e5a8c2e51c83e33b232ea792bbe3f063c094c33d9806a5565cb31759e1ad39"}, "id": "NJ5zK0UP9WFl8ckM", "tags": []}