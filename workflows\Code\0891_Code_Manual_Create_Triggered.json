{"meta": {"instanceId": "4de3652773c3e67f6210deb1e1d390d75b23715f2e2cca0340008f99419607e6"}, "nodes": [{"id": "4c9256c8-8dd7-4e81-8aef-0789e6808808", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-260, 80], "parameters": {}, "typeVersion": 1}, {"id": "1935ad6a-ade4-4073-9205-0c3dd1091c0f", "name": "Set parameters for next run", "type": "n8n-nodes-base.code", "position": [1520, 460], "parameters": {"mode": "runOnceForEachItem", "jsCode": "const desired_path = $('Create desired path').item.json.desired_path;\ndesired_path.shift();\n\nreturn {\n  desired_path: desired_path,\n  google_drive_folder_id: $json.id,\n}"}, "typeVersion": 2}, {"id": "5d99a9c4-57c6-4052-b093-fb0c32d9ff56", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-40, 460], "parameters": {}, "typeVersion": 1}, {"id": "879b92ae-edab-4d73-96d0-4df36d12fbb2", "name": "Dummy input data", "type": "n8n-nodes-base.set", "position": [-40, 80], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "041e1077-f4dc-476f-b75a-6d60d9c8d0b9", "name": "google_drive_folder_id", "type": "string", "value": "root"}, {"id": "843e3a7f-c59e-48c1-80f8-c9995515e340", "name": "desired_path", "type": "string", "value": "testXavier/2024/Q4/03 Documenten"}]}}, "typeVersion": 3.4}, {"id": "822d45f1-149d-430c-8daf-183998c01166", "name": "Split the desired path", "type": "n8n-nodes-base.code", "position": [340, 260], "parameters": {"mode": "runOnceForEachItem", "jsCode": "// Add a new field called 'myNewField' to the JSON of the item\n$input.item.json.desired_path = $input.item.json.desired_path.split('/');\n\nreturn $input.item;"}, "typeVersion": 2}, {"id": "e2aba13a-fec6-4d1e-aa1c-af95d3f957ad", "name": "Create desired path", "type": "n8n-nodes-base.code", "position": [580, 260], "parameters": {"mode": "runOnceForEachItem", "jsCode": "return {\n  google_drive_folder_id: $json.google_drive_folder_id,\n  desired_path: $json.desired_path,\n};"}, "typeVersion": 2}, {"id": "aa3f9b95-3197-4b89-bcb2-9e723b8496a0", "name": "Check if top folder exists", "type": "n8n-nodes-base.googleDrive", "position": [800, 260], "parameters": {"filter": {"folderId": {"__rl": true, "mode": "id", "value": "={{ $json.google_drive_folder_id }}"}, "whatToSearch": "folders"}, "options": {}, "resource": "fileFolder", "queryString": "={{ $json.desired_path[0] }}"}, "credentials": {"googleDriveOAuth2Api": {"id": "Xk1mfDiQRaqwWUaU", "name": "Google Drive account 2"}}, "typeVersion": 3, "alwaysOutputData": true}, {"id": "969b7823-2720-45c5-b98c-1cc659fe62df", "name": "If top folder doesn't exist", "type": "n8n-nodes-base.if", "position": [1040, 260], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "59e55ba1-5db4-455e-95a1-bb8e4c1d0d31", "operator": {"type": "object", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "2cd3932d-b066-438a-b968-4078dfc9dbe7", "name": "Create new subfolder", "type": "n8n-nodes-base.googleDrive", "position": [1340, 240], "parameters": {"name": "={{ $('Create desired path').item.json.desired_path[0] }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "id", "value": "={{ $('Create desired path').item.json.google_drive_folder_id }}"}, "resource": "folder"}, "credentials": {"googleDriveOAuth2Api": {"id": "Xk1mfDiQRaqwWUaU", "name": "Google Drive account 2"}}, "typeVersion": 3}, {"id": "f9322682-b77f-4bad-8bbc-13868c126063", "name": "If path has been completely created", "type": "n8n-nodes-base.if", "position": [1740, 460], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "d95b4b2e-68c5-4d82-84af-a46fbb84035c", "operator": {"type": "array", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json.desired_path }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "94c4694b-0a32-4681-b977-c01e3232d9e8", "name": "Return the ID of the last folder", "type": "n8n-nodes-base.set", "position": [2040, 440], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "692a23db-71c8-4154-af87-a0177045b63d", "name": "google_drive_folder_id", "type": "string", "value": "={{ $('Set parameters for next run').item.json.google_drive_folder_id }}"}]}}, "typeVersion": 3.4}, {"id": "5e9f327d-61bb-46af-b16b-21499f5c22e0", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-820, -80], "parameters": {"width": 480, "height": 880, "content": "# Create Google Drive Folders by Path\nThis workflow created nested Google Drive folder from a path string and returns the ID of the final folder for immediate use.\n\nUse this workflow in your other flows by calling it directly with the following data:\n- `google_drive_folder_id` -> The ID of the folder where you want to create additional folders in. You can use \"root\" if you want to begin at root level of your Drive.\n- `desired_path` -> The folder structure you'd like to create in Google Drive. Each folder is separated by a slash, eg: `Projects/Clients/Reports`"}, "typeVersion": 1}, {"id": "35b3741f-465a-4846-9f62-4dedc40ca884", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-280, -20], "parameters": {"color": 5, "width": 500, "height": 80, "content": "## Test data for the workflow\nUse this in case you want to test the workflow."}, "typeVersion": 1}, {"id": "3b7fe210-d966-4988-aaf4-5e07567b3054", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-280, 320], "parameters": {"color": 5, "width": 500, "height": 120, "content": "## Triggered from another workflow\nThis workflow is intended to be triggered by other workflows. Don't copy/paste this workflow as it will be more difficult to maintain and keep up-to-date."}, "typeVersion": 1}, {"id": "16477e77-656e-4bff-914f-633d61477d38", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [560, 80], "parameters": {"color": 5, "width": 1320, "height": 120, "content": "## Main loop\nTake the desired_path and split it into parts. Eg: `Projects/Clients/Reports` will turn into 3 parts: Projects, Clients, Reports.\nWe then check if the top folder exists and create it if not. We repeat this process until all subfolders have been created and correctly nested."}, "typeVersion": 1}, {"id": "57404f59-28b8-4969-b483-fb8a3320a592", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1980, 80], "parameters": {"color": 5, "width": 280, "height": 120, "content": "## Rerturn data\nHere we return the ID of the last folder in the path, so you can start uploading new files to it."}, "typeVersion": 1}], "pinData": {}, "connections": {"Dummy input data": {"main": [[{"node": "Split the desired path", "type": "main", "index": 0}]]}, "Create desired path": {"main": [[{"node": "Check if top folder exists", "type": "main", "index": 0}]]}, "Create new subfolder": {"main": [[{"node": "Set parameters for next run", "type": "main", "index": 0}]]}, "Split the desired path": {"main": [[{"node": "Create desired path", "type": "main", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "Split the desired path", "type": "main", "index": 0}]]}, "Check if top folder exists": {"main": [[{"node": "If top folder doesn't exist", "type": "main", "index": 0}]]}, "If top folder doesn't exist": {"main": [[{"node": "Create new subfolder", "type": "main", "index": 0}], [{"node": "Set parameters for next run", "type": "main", "index": 0}]]}, "Set parameters for next run": {"main": [[{"node": "If path has been completely created", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Dummy input data", "type": "main", "index": 0}]]}, "If path has been completely created": {"main": [[{"node": "Return the ID of the last folder", "type": "main", "index": 0}], [{"node": "Create desired path", "type": "main", "index": 0}]]}}}