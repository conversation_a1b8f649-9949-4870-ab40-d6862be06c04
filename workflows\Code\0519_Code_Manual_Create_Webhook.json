{"meta": {"instanceId": "1dd912a1610cd0376bae7bb8f1b5838d2b601f42ac66a48e012166bb954fed5a", "templateId": "2314"}, "nodes": [{"id": "3409b6e3-aef1-4eb4-acfb-72a73101e109", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [380, 240], "parameters": {}, "typeVersion": 1}, {"id": "4942cdfc-bc9a-43ac-a60d-06e1ddf52d07", "name": "Write Result File to Disk", "type": "n8n-nodes-base.readWriteFile", "position": [1360, 240], "parameters": {"options": {}, "fileName": "document.pdf", "operation": "write", "dataPropertyName": "=data"}, "typeVersion": 1}, {"id": "1467a9ab-144d-48cc-a52f-3dca86ca0e8b", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [880, 100], "parameters": {"width": 218, "height": 132, "content": "## Authentication\nConversion requests must be authenticated. Please create \n[ConvertAPI account to get authentication secret](https://www.convertapi.com/a/signin)"}, "typeVersion": 1}, {"id": "4d85a311-8e39-48ce-868e-95efec509247", "name": "Create HTML", "type": "n8n-nodes-base.set", "position": [580, 240], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "ad325c1b-1597-45ab-98cd-1801da32e3f1", "name": "data", "type": "string", "value": "=<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>ConvertAPI Test Document</title>\n</head>\n<body>\n    <h1>ConvertAPI Test Document</h1>\n    <p>This is a minimal HTML5 document used for testing ConvertAPI's conversion capabilities.</p>\n    <section>\n        <h2>Section Title</h2>\n        <p>This is a section within the document.</p>\n    </section>\n    <footer>\n        <p>&copy; 2024 Test Document</p>\n    </footer>\n</body>\n</html>"}]}}, "typeVersion": 3.3}, {"id": "a0e4e17a-097f-4127-9b60-c6ae637816a0", "name": "Convert HTML to File", "type": "n8n-nodes-base.code", "position": [760, 240], "parameters": {"jsCode": "const text = $node[\"Create HTML\"].json[\"data\"]\nconst buffer = Buffer.from(text, 'utf8');\nconst binaryData = {\n  data: buffer.toString('base64'),\n  mimeType: 'application/octet-stream',\n  fileName: 'file.html',\n};\nitems[0].binary = { data: binaryData };\nreturn items;\n"}, "typeVersion": 2}, {"id": "653b21eb-dae5-44e0-858a-a2905f495911", "name": "Convert File to PDF", "type": "n8n-nodes-base.httpRequest", "position": [940, 240], "parameters": {"url": "https://v2.convertapi.com/convert/html/to/pdf", "method": "POST", "options": {"response": {"response": {"responseFormat": "file"}}}, "sendBody": true, "contentType": "multipart-form-data", "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "file", "parameterType": "formBinaryData", "inputDataFieldName": "data"}]}, "genericAuthType": "httpQueryAuth", "headerParameters": {"parameters": [{"name": "Accept", "value": "application/octet-stream"}]}}, "credentials": {"httpQueryAuth": {"id": "WdAklDMod8fBEMRk", "name": "Query Auth account"}}, "notesInFlow": true, "typeVersion": 4.2}], "pinData": {}, "connections": {"Create HTML": {"main": [[{"node": "Convert HTML to File", "type": "main", "index": 0}]]}, "Convert File to PDF": {"main": [[{"node": "Write Result File to Disk", "type": "main", "index": 0}]]}, "Convert HTML to File": {"main": [[{"node": "Convert File to PDF", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Create HTML", "type": "main", "index": 0}]]}}}