{"id": "QCbb7Bm12gDIH0mI", "meta": {"instanceId": "d189560122cb823898b8eca8996614abf14798d923f2ff7c4d7220fb10f8e6f7", "templateCredsSetupCompleted": true}, "name": "Keep discord clean", "tags": [{"id": "CgBu2Sxr4mqipxlK", "name": "template", "createdAt": "2025-01-08T19:56:24.079Z", "updatedAt": "2025-01-08T19:56:24.079Z"}], "nodes": [{"id": "dde530b8-edd5-4f1d-a3c8-326925c97269", "name": "Loop Over Channels", "type": "n8n-nodes-base.splitInBatches", "position": [560, 400], "parameters": {"options": {}}, "retryOnFail": false, "typeVersion": 3}, {"id": "3e2684b1-08ad-41bd-930f-cbb229e16617", "name": "Loop Over Messages", "type": "n8n-nodes-base.splitInBatches", "position": [1260, 320], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "430d13be-670f-4e5d-acdf-ffe1a65a49e3", "name": "Every day at 9pm", "type": "n8n-nodes-base.scheduleTrigger", "position": [-160, 400], "parameters": {"rule": {"interval": [{"triggerAtHour": 21}]}}, "typeVersion": 1.2}, {"id": "6d40f036-c87a-4b68-9ec6-523a3372447c", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [40, 600], "parameters": {"color": 6, "width": 294, "height": 80, "content": "**Note ☝️**\nDon’t forget to setup an error workflow to get notified if something goes wrong"}, "typeVersion": 1}, {"id": "93290300-084b-4f91-95bc-f34c1aef93cd", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [420, -60], "parameters": {"color": 5, "width": 327, "height": 152, "content": "### 👨‍🎤 Setup\n1. Add your **Discord** credentials\n2. Change the server in each **Discord** node to the correct one\n3. Click the Test Workflow button\n3. Activate the workflow to run on a schedule"}, "typeVersion": 1}, {"id": "5fead80a-de3a-4f45-a524-5228def7b4ad", "name": "Cool down Discord API rate limits", "type": "n8n-nodes-base.wait", "position": [280, 400], "webhookId": "cea120e2-5bb9-45cf-83e6-55fd458d6cf4", "parameters": {"amount": 2}, "typeVersion": 1.1}, {"id": "5a8d6402-458c-4c24-b379-6a41908a5af3", "name": "Get all Discord channels", "type": "n8n-nodes-base.discord", "position": [40, 400], "webhookId": "a77d1495-df40-4afd-ad0a-8f5b851b16da", "parameters": {"guildId": {"__rl": true, "mode": "id", "value": ""}, "options": {"filter": [0, 2]}, "operation": "getAll", "returnAll": true}, "typeVersion": 2}, {"id": "a2b1d905-849d-4392-95db-e545f542ba78", "name": "Cool down Message deletion API rate limits", "type": "n8n-nodes-base.wait", "position": [1680, 340], "webhookId": "fcd9f62a-f08b-44bc-afa3-87d960fdc380", "parameters": {"amount": 1}, "typeVersion": 1.1}, {"id": "8c134cfe-dcb1-400d-a518-17ed3f1cbf62", "name": "Cool down Get messages API rate limits", "type": "n8n-nodes-base.wait", "position": [1000, 480], "webhookId": "5d8de5eb-8445-4a64-8b8b-8577ffa52ef0", "parameters": {"amount": 2}, "typeVersion": 1.1}, {"id": "18ba10df-dcec-4d27-8ecf-06171939b7eb", "name": "Get messages from Channel", "type": "n8n-nodes-base.discord", "onError": "continueRegularOutput", "position": [800, 480], "webhookId": "b36f85bb-1237-415d-81bb-598703d3d4cd", "parameters": {"guildId": {"__rl": true, "mode": "id", "value": ""}, "options": {}, "resource": "message", "channelId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "operation": "getAll", "returnAll": true}, "retryOnFail": false, "typeVersion": 2, "alwaysOutputData": true, "waitBetweenTries": 5000}, {"id": "57f2395a-b624-41d3-aada-4107b21a3359", "name": "Delete Message", "type": "n8n-nodes-base.discord", "onError": "continueRegularOutput", "position": [1500, 340], "webhookId": "4b43cc2e-59db-46c9-ae4c-9716146c25bf", "parameters": {"guildId": {"__rl": true, "mode": "id", "value": ""}, "resource": "message", "channelId": {"__rl": true, "mode": "id", "value": "={{ $json.channel_id }}"}, "messageId": "={{ $json.id }}", "operation": "deleteMessage"}, "retryOnFail": false, "typeVersion": 2, "alwaysOutputData": true, "waitBetweenTries": 5000}, {"id": "c224ef25-57d8-4fe6-b14a-b09131ce8c1c", "name": "Filter Messages older than 7 days", "type": "n8n-nodes-base.filter", "position": [1000, 320], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "2864fc65-1d9d-433f-bd61-766278a7e54c", "operator": {"type": "dateTime", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.timestamp }}", "rightValue": ""}, {"id": "a05636ea-8663-4398-8a55-a03ab34f83a5", "operator": {"type": "dateTime", "operation": "before"}, "leftValue": "={{ $json.timestamp }}", "rightValue": "={{ $today.minus({days: 7}) }}"}]}}, "typeVersion": 2.2}, {"id": "c3654c25-6318-4652-9f76-82770cc28324", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [40, 300], "parameters": {"color": 6, "width": 194, "height": 80, "content": "**Tip 👇**\nOAuth2 Authentication is very easy to setup"}, "typeVersion": 1}, {"id": "f3d8b35e-6b13-4df9-bd33-2d44381e6fc5", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-200, -60], "parameters": {"color": 4, "width": 600, "height": 280, "content": "# Nightly Discord Channel Cleanup\n### This workflow runs every day at 9:00 p.m. and:\n- Retrieves all Discord channels using your provided credentials.\n- Pauses briefly to respect Discord API rate limits.\n- Loops through each channel and fetches messages.\n- Filters out messages older than seven days.\n- Deletes those older messages, again pausing to stay within deletion rate limits.\n\nBy setting up this workflow on a schedule, you can automatically keep Discord channels tidy and compliant with retention policies."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"callerPolicy": "workflowsFromSameOwner", "errorWorkflow": "C3NlThVZU7vFr4Um", "executionOrder": "v1", "saveManualExecutions": true}, "versionId": "a4b9f5d2-d905-4c86-9fa6-2a274909ecce", "connections": {"Delete Message": {"main": [[{"node": "Cool down Message deletion API rate limits", "type": "main", "index": 0}]]}, "Every day at 9pm": {"main": [[{"node": "Get all Discord channels", "type": "main", "index": 0}]]}, "Loop Over Channels": {"main": [[{"node": "Filter Messages older than 7 days", "type": "main", "index": 0}], [{"node": "Get messages from Channel", "type": "main", "index": 0}]]}, "Loop Over Messages": {"main": [[], [{"node": "Delete Message", "type": "main", "index": 0}]]}, "Get all Discord channels": {"main": [[{"node": "Cool down Discord API rate limits", "type": "main", "index": 0}]]}, "Get messages from Channel": {"main": [[{"node": "Cool down Get messages API rate limits", "type": "main", "index": 0}]]}, "Cool down Discord API rate limits": {"main": [[{"node": "Loop Over Channels", "type": "main", "index": 0}]]}, "Filter Messages older than 7 days": {"main": [[{"node": "Loop Over Messages", "type": "main", "index": 0}]]}, "Cool down Get messages API rate limits": {"main": [[{"node": "Loop Over Channels", "type": "main", "index": 0}]]}, "Cool down Message deletion API rate limits": {"main": [[{"node": "Loop Over Messages", "type": "main", "index": 0}]]}}}