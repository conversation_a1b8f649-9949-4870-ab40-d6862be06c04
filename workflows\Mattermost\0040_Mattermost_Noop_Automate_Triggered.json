{"nodes": [{"name": "<PERSON><PERSON> Trigger", "type": "n8n-nodes-base.notionTrigger", "position": [270, 350], "parameters": {"event": "pageAddedToDatabase", "pollTimes": {"item": [{"mode": "everyHour"}]}, "databaseId": "6ea34c0d-67e8-4614-ad5c-68c665a34763"}, "credentials": {"notionApi": ""}, "typeVersion": 1}, {"name": "IF", "type": "n8n-nodes-base.if", "position": [470, 350], "parameters": {"conditions": {"string": [{"value1": "={{$json[\"Team\"]}}", "value2": "Marketing"}]}}, "typeVersion": 1}, {"name": "Mattermost", "type": "n8n-nodes-base.mattermost", "position": [670, 250], "parameters": {"message": "=New meeting notes were added.\nAgenda: {{$json[\"Agenda\"]}}\nDate: {{$json[\"Date\"][\"start\"]}}\nLink: https://notion.so/{{$json[\"id\"].replace(/-/g,'')}}", "channelId": "64cae1bh6pggtcupfd4ztwby4r", "attachments": [], "otherOptions": {}}, "credentials": {"mattermostApi": ""}, "typeVersion": 1}, {"name": "NoOp", "type": "n8n-nodes-base.noOp", "position": [668, 495], "parameters": {}, "typeVersion": 1}], "connections": {"IF": {"main": [[{"node": "Mattermost", "type": "main", "index": 0}], [{"node": "NoOp", "type": "main", "index": 0}]]}, "Notion Trigger": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}}}