{"nodes": [{"id": "79c06432-9d3f-4a93-b613-24bdaedfb71d", "name": "Could not find user", "type": "n8n-nodes-base.noOp", "position": [1940, 640], "parameters": {}, "typeVersion": 1}, {"id": "07d23ef6-8c93-40de-9e95-ea3d56811fa0", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [540, 200], "parameters": {"width": 330.0463186838765, "height": 609.3353028064989, "content": "## On User created in HubSpot\n\n1. Setup Oauth2 creds using n8n docs\nhttps://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.hubspottrigger\n\n### Be careful with scopes. Scopes must be exactly as defined in the docs"}, "typeVersion": 1}, {"id": "bad1ea1c-9b03-4419-ac37-272b5740a7c4", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1660, 200], "parameters": {"color": 3, "width": 343.28654189681333, "height": 609.3353028064989, "content": "## Enrich with data from ExactBuyer\n\n1. Add api key from Exact buyer\n2. Use email as identifier to match user\n\nUse API Guide here https://docs.exactbuyer.com/contact-enrichment/enrichment"}, "typeVersion": 1}, {"id": "d24ae8e3-c5fe-4cde-bade-66f60cda943d", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [940, 240], "parameters": {"color": 6, "width": 352.**************, "height": 499.*************, "content": "## Get user in Hubspot\n\n1. Setup Oauth2 creds using n8n docs\nhttps://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.hubspottrigger/\n\n### Be careful with scopes. Scopes must be exactly as defined in the docs\n### Will need to be a different cred from the trigger one"}, "typeVersion": 1}, {"id": "5e0775c9-178a-460e-a478-b9a69aaf80cd", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [820, -320], "parameters": {"color": 4, "width": 629.6175493462406, "height": 473.03355645575084, "content": "# Enrich new Hubspot contacts with contact and company data from ExactBuyer\n\n## This workflow aims to enrich new contacts in HubSpot. The more relevant the HubSpot profile, the more useful it is. Once active, this n8n workflow will update the social profiles, contact data (phone, email) as well as location data from ExactBuyer.\n"}, "typeVersion": 1}, {"id": "73f7c19a-8145-4ad3-bbf0-0a4a0d70be33", "name": "Enrich user from ExactBuyer", "type": "n8n-nodes-base.httpRequest", "onError": "continueErrorOutput", "position": [1740, 480], "parameters": {"url": "https://api.exactbuyer.com/v1/enrich", "options": {"redirect": {"redirect": {}}}, "sendQuery": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "queryParameters": {"parameters": [{"name": "email", "value": "={{ $json.email }}"}, {"name": "required", "value": "work_email,personal_email,email"}]}}, "credentials": {"httpHeaderAuth": {"id": "kyMNOdXZX3ugPihF", "name": "ExactBuyer Api key"}}, "typeVersion": 4.1}, {"id": "7a0c2b14-0f22-475f-84c2-49ff1c1a1fad", "name": "Set keys", "type": "n8n-nodes-base.set", "position": [1320, 500], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "211cd22a-a55c-4018-8ba6-3272097d502c", "name": "user_id", "type": "string", "value": "={{ $json.vid }}"}, {"id": "f3c7d0b9-717c-437b-ab45-1f35c194d412", "name": "email", "type": "string", "value": "={{ $json.properties.email?.value }}"}]}}, "typeVersion": 3.3}, {"id": "4da6204d-2717-41fb-8f79-3bb094db9e71", "name": "if found email", "type": "n8n-nodes-base.if", "position": [1480, 500], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "dbd1042e-dfe7-40ff-84ca-c3d289decb78", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.email }}", "rightValue": ""}]}}, "typeVersion": 2}, {"id": "24ef009a-c05c-4ca3-afd5-ef44b2349cfb", "name": "Update contact from Hubspot", "type": "n8n-nodes-base.hubspot", "position": [2200, 460], "parameters": {"email": "={{ $('Set keys').item.json.email }}", "options": {}, "authentication": "oAuth2", "additionalFields": {"gender": "={{ $json.result.gender }}", "school": "={{ $json.result.education?.[0]?.school?.name }}", "country": "={{ $json.result.location?.country }}", "jobTitle": "={{ $json.result.employment?.job?.title }}", "lastName": "={{ $json.result.last_name }}", "firstName": "={{ $json.result.first_name }}", "companyName": "={{ $json.result.employment?.name }}", "companySize": "={{ $json.result.employment.size }}", "phoneNumber": "={{ $json.result.phone_numbers?.[0]?.E164 }}"}}, "credentials": {"hubspotOAuth2Api": {"id": "Gxwfj6z9NwaEC3P5", "name": "HubSpot account 3"}}, "typeVersion": 2}, {"id": "1adda76c-39cb-43e7-a1dd-301dfd530c77", "name": "Get HubSpot contact", "type": "n8n-nodes-base.hubspot", "position": [1060, 500], "parameters": {"contactId": {"__rl": true, "mode": "id", "value": "={{ $json.contactId }}"}, "operation": "get", "authentication": "oAuth2", "additionalFields": {}}, "credentials": {"hubspotOAuth2Api": {"id": "Gxwfj6z9NwaEC3P5", "name": "HubSpot account 3"}}, "typeVersion": 2}, {"id": "7aa3b2bc-f564-4160-adb6-e0d977993540", "name": "On contact created", "type": "n8n-nodes-base.hubspotTrigger", "position": [740, 500], "webhookId": "0c93331f-6d07-4cfe-b9b7-8a724c6c8c28", "parameters": {"eventsUi": {"eventValues": [{}]}, "additionalFields": {}}, "credentials": {"hubspotDeveloperApi": {"id": "5VJ26ST8DdVyAfEZ", "name": "HubSpot Developer account 3"}}, "typeVersion": 1}, {"id": "e8b386bf-0a86-44a0-82e6-1189bc3c5619", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [2120, 260], "parameters": {"color": 6, "width": 352.**************, "height": 499.*************, "content": "## Update user in Hubspot\n\n### Same cred as in getting the contact in Hubspot"}, "typeVersion": 1}], "pinData": {"On contact created": [{"appId": 2930857, "eventId": *********, "portalId": ********, "sourceId": "2931120", "contactId": 251, "changeFlag": "CREATED", "occurredAt": *************, "changeSource": "INTEGRATION", "attemptNumber": 0, "subscriptionId": 2538662, "subscriptionType": "contact.creation"}]}, "connections": {"Set keys": {"main": [[{"node": "if found email", "type": "main", "index": 0}]]}, "if found email": {"main": [[{"node": "Enrich user from ExactBuyer", "type": "main", "index": 0}]]}, "On contact created": {"main": [[{"node": "Get HubSpot contact", "type": "main", "index": 0}]]}, "Get HubSpot contact": {"main": [[{"node": "Set keys", "type": "main", "index": 0}]]}, "Enrich user from ExactBuyer": {"main": [[{"node": "Update contact from Hubspot", "type": "main", "index": 0}], [{"node": "Could not find user", "type": "main", "index": 0}]]}}}