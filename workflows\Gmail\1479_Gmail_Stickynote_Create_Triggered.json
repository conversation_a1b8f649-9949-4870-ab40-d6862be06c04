{"id": "aOQANirVMuWrH0ZD", "meta": {"instanceId": "b78ce2d06ac74b90a581919cf44503cf07404c11eda5c3847597226683145618"}, "name": "Gmail AI auto-responder: create draft replies to incoming emails", "tags": [], "nodes": [{"id": "2a9ff08f-919a-41a8-980b-8c2bca3059e4", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.gmailTrigger", "position": [-332.************, 566.*************], "parameters": {"simple": false, "filters": {"q": "-from:me"}, "options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}}, "credentials": {"gmailOAuth2": {"id": "ofvBTX8A0aWfQb2O", "name": "Gmail account"}}, "typeVersion": 1}, {"id": "3ef14615-0045-404f-a21b-2c65a52f4be8", "name": "If Needs Reply", "type": "n8n-nodes-base.if", "position": [240, 560], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "********-ad32-4845-9976-9f9688f5a6f2", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.needsReply }}", "rightValue": "true"}]}}, "typeVersion": 2}, {"id": "36968dd5-8d51-4184-a05a-587b6c95aa82", "name": "JSON Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [100, 720], "parameters": {"jsonSchema": "{\n \"type\": \"object\",\n \"properties\": {\n \"needsReply\": {\n \"type\": \"boolean\"\n }\n },\n \"required\": [\"needsReply\"]\n}\n"}, "typeVersion": 1}, {"id": "2a64dce8-e2f0-475e-a366-a02084293aad", "name": "OpenAI Chat", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-92.************, 726.*************], "parameters": {"model": "gpt-4o", "options": {"temperature": 0, "responseFormat": "json_object"}}, "credentials": {"openAiApi": {"id": "13ffkrNMlQMfvbZy", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "be892ff8-0981-4b34-9c93-7674ddd90360", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-429.************, 461.*************6], "parameters": {"width": 304.**************, "height": 394.**************, "content": "## When I receive an Email\n"}, "typeVersion": 1}, {"id": "9d92839a-9ff2-436c-8abb-2f43e07c1ace", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-112.************, 460.*************6], "parameters": {"width": 556, "height": 397, "content": "## ... that Needs a Reply\n"}, "typeVersion": 1}, {"id": "3cd77609-684c-44e2-9cdc-9479cfd836bd", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [460, 460], "parameters": {"width": 333.**************, "height": 400.*************6, "content": "## Generate a Reply"}, "typeVersion": 1}, {"id": "b123cf31-767d-48bb-a0ba-79a69f6da585", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [807.************, 461.*************6], "parameters": {"width": 326, "height": 395, "content": "## ...as a Draft in the conversation"}, "typeVersion": 1}, {"id": "1a87c416-6b1c-4526-a2b6-20468c95ea0e", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [480, 680], "parameters": {"model": "gpt-4-turbo", "options": {}}, "credentials": {"openAiApi": {"id": "13ffkrNMlQMfvbZy", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "84b4d516-252e-444e-b998-2d4aa0f89653", "name": "Gmail - Create Draft", "type": "n8n-nodes-base.gmail", "position": [900, 560], "parameters": {"message": "={{ $json.text.replace(/\\n/g, \"<br />\\n\") }}", "options": {"sendTo": "={{ $('Gmail Trigger').item.json.headers.from }}", "threadId": "={{ $('Gmail Trigger').item.json.threadId }}"}, "subject": "=Re: {{ $('Gmail Trigger').item.json.headers.subject }}", "resource": "draft", "emailType": "html"}, "credentials": {"gmailOAuth2": {"id": "ofvBTX8A0aWfQb2O", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "86017ff4-9c57-4b2a-9cd9-f62571a05ffd", "name": "Assess if message needs a reply", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [-92.************, 566.*************], "parameters": {"prompt": "=Subject: {{ $json.subject }}\nMessage:\n{{ $json.textAsHtml }} ", "messages": {"messageValues": [{"message": "Your task is to assess if the message requires a response. Return in JSON format true if it does, false otherwise.\nMarketing emails don't require a response."}]}}, "typeVersion": 1.3}, {"id": "cab1e7e5-93dc-4850-a471-e285cdbe2058", "name": "Generate email reply", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [500, 520], "parameters": {"text": "=Subject: {{ $('Gmail Trigger').item.json.subject }}\nMessage: {{ $('Gmail Trigger').item.json.textAsHtml }}", "messages": {"messageValues": [{"message": "You're a helpful personal assistant and your task is to draft replies on my behalf to my incoming emails. Whenever I provide some text from an email, return an appropriate draft reply for it and nothing else.\nEnsure that the reply is suitable for a professional email setting and addresses the topic in a clear, structured, and detailed manner.\nDo not make things up.\n\nDetailed instructions:\n- Be concise and maintain a business casual tone.\n- Start with \"Hello,\", and end with \"Best,\"\n- When replying to yes-no questions, draft 2 responses: one affirmative and one negative separated by \" - - - - - - - OR - - - - - - - \"\n- If you don't know an answer, you can leave placeholders like \"[YOUR_ANSWER_HERE]\".\n- Don't use any special formatting, only plain text.\n- Reply in the same language as the inbound email."}]}, "promptType": "define"}, "typeVersion": 1.4}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "c4448c34-1f75-4479-805e-20d8a69a7e00", "connections": {"JSON Parser": {"ai_outputParser": [[{"node": "Assess if message needs a reply", "type": "ai_outputParser", "index": 0}]]}, "OpenAI Chat": {"ai_languageModel": [[{"node": "Assess if message needs a reply", "type": "ai_languageModel", "index": 0}]]}, "Gmail Trigger": {"main": [[{"node": "Assess if message needs a reply", "type": "main", "index": 0}]]}, "If Needs Reply": {"main": [[{"node": "Generate email reply", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Generate email reply", "type": "ai_languageModel", "index": 0}]]}, "Generate email reply": {"main": [[{"node": "Gmail - Create Draft", "type": "main", "index": 0}]]}, "Assess if message needs a reply": {"main": [[{"node": "If Needs Reply", "type": "main", "index": 0}]]}}}