{"id": 95, "name": "Scrape Twitter for mentions of company", "nodes": [{"name": "Filter Tweet Data", "type": "n8n-nodes-base.set", "position": [1260, 300], "parameters": {"values": {"string": [{"name": "Tweet", "value": "={{$node[\"Get last 50 mentions\"].json[\"text\"]}}"}, {"name": "Tweet URL", "value": "=https://twitter.com/{{$node[\"Get last 50 mentions\"].json[\"user\"][\"screen_name\"]}}/status/{{$node[\"Get last 50 mentions\"].json[\"id_str\"]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "Run Every 10 Minutes", "type": "n8n-nodes-base.cron", "position": [260, 320], "parameters": {"triggerTimes": {"item": [{"mode": "everyX", "unit": "minutes", "value": 10}]}}, "typeVersion": 1}, {"name": "Now - 10 minutes", "type": "n8n-nodes-base.dateTime", "position": [620, 320], "parameters": {"value": "={{Date()}}", "action": "calculate", "options": {}, "duration": "={{$node[\"Run Every 10 Minutes\"].parameter[\"triggerTimes\"][\"item\"][0][\"value\"]}}", "timeUnit": "={{$node[\"Run Every 10 Minutes\"].parameter[\"triggerTimes\"][\"item\"][0][\"unit\"]}}", "operation": "subtract"}, "typeVersion": 1}, {"name": "Get last 50 mentions", "type": "n8n-nodes-base.twitter", "position": [820, 320], "parameters": {"operation": "search", "searchText": "={{$node[\"Setup\"].parameter[\"values\"][\"string\"][1][\"value\"]}}", "additionalFields": {}}, "credentials": {"twitterOAuth1Api": {"id": "27", "name": "86-88 Twitter"}}, "typeVersion": 1}, {"name": "Created since last run?", "type": "n8n-nodes-base.if", "position": [1020, 320], "parameters": {"conditions": {"dateTime": [{"value1": "={{$json[\"created_at\"]}}", "value2": "={{$items(\"Now - 10 minutes\", 0, 0)[0].json.data}}"}]}}, "typeVersion": 1}, {"name": "Setup", "type": "n8n-nodes-base.set", "position": [440, 320], "parameters": {"values": {"string": [{"name": "slackChannel", "value": "#recent-tweets"}, {"name": "twitterSearchValue", "value": "@n8n_io"}]}, "options": {}}, "typeVersion": 1}, {"name": "Post to Slack", "type": "n8n-nodes-base.slack", "position": [1440, 300], "parameters": {"text": "=New Mention!: {{$node[\"Filter Tweet Data\"].json[\"Tweet\"]}}.\nSee it here: {{$node[\"Filter Tweet Data\"].json[\"Tweet URL\"]}}", "channel": "={{$node[\"Setup\"].parameter[\"values\"][\"string\"][0][\"value\"]}}", "attachments": [], "otherOptions": {}}, "credentials": {"slackApi": {"id": "53", "name": "Slack Access Token"}}, "typeVersion": 1}], "active": true, "settings": {}, "connections": {"Setup": {"main": [[{"node": "Now - 10 minutes", "type": "main", "index": 0}]]}, "Now - 10 minutes": {"main": [[{"node": "Get last 50 mentions", "type": "main", "index": 0}]]}, "Filter Tweet Data": {"main": [[{"node": "Post to Slack", "type": "main", "index": 0}]]}, "Get last 50 mentions": {"main": [[{"node": "Created since last run?", "type": "main", "index": 0}]]}, "Run Every 10 Minutes": {"main": [[{"node": "Setup", "type": "main", "index": 0}]]}, "Created since last run?": {"main": [[{"node": "Filter Tweet Data", "type": "main", "index": 0}]]}}}