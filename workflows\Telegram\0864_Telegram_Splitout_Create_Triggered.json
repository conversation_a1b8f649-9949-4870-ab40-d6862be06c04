{"nodes": [{"id": "cab4467e-449e-4823-abe5-eb0368883e9c", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [740, 180], "webhookId": "231e8ee3-320f-47c7-8368-03965732d709", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "a32a646b-80f2-46a4-81c2-7e3b5a4a192c", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [940, 140], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {"temperature": 0}}, "credentials": {"openAiApi": {"id": "1IOLtYX7aTspCAN8", "name": "OpenAI Pollup"}}, "typeVersion": 1.2}, {"id": "3d934326-ad89-477f-9ab6-b97c04960597", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [1160, 140], "parameters": {"jsonSchemaExample": "\n[{\"name\": \"total Protein\",\n  \"quantity\": 86,\n  \"unit\": \"gr\"\n},\n  {\"name\": \"total lipids\",\n  \"quantity\": 86,\n  \"unit\": \"gr\"\n},\n  {\"name\": \"total carbohydrats\",\n  \"quantity\": 86,\n  \"unit\": \"gr\"\n},\n  {\"name\": \"total potassium\",\n  \"quantity\": 86,\n  \"unit\": \"gr\"\n},\n  {\"name\": \"total magnesium\",\n  \"quantity\": 86,\n  \"unit\": \"gr\"\n},\n  {\"name\": \"total sodium\",\n  \"quantity\": 86,\n  \"unit\": \"gr\"\n},\n  {\"name\": \"total kcal\",\n  \"quantity\": 248,\n  \"unit\": \"kcal\"\n},\n  {\n    \"reasoning\": \"this is my reasoning\"\n  }\n]"}, "typeVersion": 1.2}, {"id": "5a086fb6-6f12-40b6-aa82-64bb2d76b730", "name": "Get Audio File", "type": "n8n-nodes-base.telegram", "position": [300, -280], "webhookId": "36dfe00d-6f05-419a-a80a-f6c7321e9a7d", "parameters": {"fileId": "={{ $json.message.voice.file_id }}", "resource": "file"}, "credentials": {"telegramApi": {"id": "ynY4cqTMvfHfi0bc", "name": "Mes repas bot"}}, "typeVersion": 1.2}, {"id": "f72d4182-26e2-4026-8097-7e4cef50bfed", "name": "Transcribe Recording", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [520, -280], "parameters": {"options": {}, "resource": "audio", "operation": "transcribe", "binaryPropertyName": "=data"}, "credentials": {"openAiApi": {"id": "1IOLtYX7aTspCAN8", "name": "OpenAI Pollup"}}, "typeVersion": 1.6}, {"id": "0f3b227f-b15a-410c-9333-a40c3e1b95ee", "name": "Limit", "type": "n8n-nodes-base.limit", "position": [1996, -80], "parameters": {}, "typeVersion": 1}, {"id": "fddaae3c-d7e6-4cb4-bb23-f734dcbefb85", "name": "Receive Telegram message", "type": "n8n-nodes-base.telegramTrigger", "position": [-140, -180], "webhookId": "34756bf0-27bd-4384-9e46-549473c307a0", "parameters": {"updates": ["message", "channel_post"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "ynY4cqTMvfHfi0bc", "name": "Mes repas bot"}}, "typeVersion": 1.2}, {"id": "9c6c00ca-e6f6-4f0b-b120-2249978379aa", "name": "If it's a voice message", "type": "n8n-nodes-base.if", "position": [80, -180], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "fb7a6885-6149-4666-bd3a-5eebde28d601", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.message.voice }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "97e56ec8-3a71-4e0d-a626-07a5113b09b7", "name": "Set chatInput from message", "type": "n8n-nodes-base.set", "position": [740, -80], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "3af0daa0-795f-45e8-ae10-fca10950b855", "name": "chatInput", "type": "string", "value": "={{ $json.message.text }}"}]}}, "typeVersion": 3.4}, {"id": "e1609f06-f769-4dfe-98e4-a56e95217307", "name": "Set chatInput from voice", "type": "n8n-nodes-base.set", "position": [740, -280], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "3af0daa0-795f-45e8-ae10-fca10950b855", "name": "chatInput", "type": "string", "value": "={{ $json.text }}"}]}}, "typeVersion": 3.4}, {"id": "42acb130-91f7-4d94-8b6b-c6b6b79f59f9", "name": "List of Ingredients and nutrients", "type": "@n8n/n8n-nodes-langchain.agent", "position": [960, -80], "parameters": {"text": "=\n*\"Approximate the kcals, protein, carbohydrates, lipids (fats), and electrolyte (sodium, potassium, magnesium, Zinc and Iron) content in the following dietary intake statement:  \n\n{{ $json.chatInput }}\n\nProvide estimates for each component based on typical nutritional values. Break down the contributions from each food item (steak, salad, vinaigrette) and give a total number for each nutrient.  \n\nGive the total result as a json, with as name, the name of the nutrient, as quantity, the total summed value, and as unit the unit that been chosen (gr, mg).\nPut the reasoning in another variable called \"reasonning\"\n", "options": {"systemMessage": "You are a nutrition expert."}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.8}, {"id": "b27195f2-cb45-45d8-ab87-60e76828f7c4", "name": "Explode the list", "type": "n8n-nodes-base.splitOut", "position": [1336, -80], "parameters": {"include": "={{ $json.output }}", "options": {}, "fieldToSplitOut": "output"}, "typeVersion": 1}, {"id": "cf95c6a1-8ed4-45ee-90d0-fe87300c2968", "name": "Add date", "type": "n8n-nodes-base.code", "position": [1556, -80], "parameters": {"mode": "runOnceForEachItem", "jsCode": "let entry = $input.item.json.output\nlet my_date = new Date()\n\nlet my_date_f = (my_date.getTime() / 86400000) + 25569;\nentry.my_date = my_date_f\nreturn {json: entry}"}, "typeVersion": 2}, {"id": "a0fd85ad-5cce-4419-af80-a6ff50a93631", "name": "Store in sheet", "type": "n8n-nodes-base.googleSheets", "position": [1776, -80], "parameters": {"columns": {"value": {}, "schema": [{"id": "name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "quantity", "type": "string", "display": true, "removed": false, "required": false, "displayName": "quantity", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "unit", "type": "string", "display": true, "removed": false, "required": false, "displayName": "unit", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "my_date", "type": "string", "display": true, "removed": false, "required": false, "displayName": "my_date", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1HdL1iwHvIhN44yW_HzRbjC--ZxItCfyX-wObjWzZHAc/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1HdL1iwHvIhN44yW_HzRbjC--ZxItCfyX-wObjWzZHAc", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1HdL1iwHvIhN44yW_HzRbjC--ZxItCfyX-wObjWzZHAc/edit?usp=drivesdk", "cachedResultName": "<PERSON><PERSON> repas"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "gdLmm513ROUyH6oU", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "06ee2513-0622-450b-b195-84cdef13cd27", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-200, -380], "parameters": {"color": 4, "height": 340, "content": "## Send a Telegram message\n1. To your channel with the list of what you ate during your last meal. \nYour input can be a written or a voice message."}, "typeVersion": 1}, {"id": "fc4a2f5b-5565-4644-bd9f-74da73818698", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-680, -380], "parameters": {"color": 5, "width": 460, "content": "## Setup\n1. Create a telegram Bo<PERSON> by following the instructions [here](https://docs.n8n.io/integrations/builtin/credentials/telegram/).\n2. Create an empty Google sheet and set it in \"Store in sheet\" along with your credentials\n3. Set your creadential for your OpenAI account\n"}, "typeVersion": 1}, {"id": "9e4e2ead-0c19-4799-8004-adaf30a5e0b1", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1700, -220], "parameters": {"color": 4, "height": 320, "content": "## Check your data\n- to see if it seems correct \n- you can filter and sum your nutrients to check if you had enough! "}, "typeVersion": 1}, {"id": "e57b8eef-d72d-46b2-ae1b-5d61a2c22d01", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [260, -380], "parameters": {"color": 4, "width": 640, "height": 260, "content": "## If it's an Audio file\nTRanscript it using openAI "}, "typeVersion": 1}, {"id": "9bd1447e-bcde-4b4c-92c9-76120c9a42d2", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [560, 80], "parameters": {"color": 4, "width": 340, "height": 260, "content": "## Testing\nYou can chat with the workflow by clicking on \"open chat\" to test your input and the response"}, "typeVersion": 1}, {"id": "ac3a7c88-782a-4943-a332-2509287df840", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [920, -220], "parameters": {"color": 4, "width": 340, "height": 320, "content": "## Personalize the prompt!!\n- It's a very simple one, you can of course make it better!"}, "typeVersion": 1}, {"id": "58642d48-d97e-4a3d-84ea-a4de580e4c25", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [2140, -220], "parameters": {"color": 4, "height": 320, "content": "## Personalize the response message\nYou can send the response of the Agent\nOr just be more polite! "}, "typeVersion": 1}, {"id": "c6b8206c-bcb0-4034-8f99-9a4165770709", "name": "Respond message", "type": "n8n-nodes-base.telegram", "position": [2216, -80], "webhookId": "8e646f8a-1f21-4719-b5f2-0cc5fad144df", "parameters": {"text": "Your meal has been saved", "chatId": "={{ $('If it's a voice message').item.json.message.chat.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "ynY4cqTMvfHfi0bc", "name": "Mes repas bot"}}, "typeVersion": 1.2}, {"id": "414d1da4-94e0-454d-acf9-a8344d1168b4", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-680, -200], "parameters": {"width": 460, "height": 260, "content": "## Contact me\n- If you need any modification to this workflow\n- if you need some help with this workflow\n- Or if you need any workflow in n8n, Make, or Langchain / Langgraph\n\nWrite to me: [<EMAIL>](mailto:<EMAIL>)\n\nThis a light version of My Meals. I have a working \"Pro\" version with searches in the USDA database for each ingredients that return ALL the Nutrients.\n"}, "typeVersion": 1}], "connections": {"Limit": {"main": [[{"node": "Respond message", "type": "main", "index": 0}]]}, "Add date": {"main": [[{"node": "Store in sheet", "type": "main", "index": 0}]]}, "Get Audio File": {"main": [[{"node": "Transcribe Recording", "type": "main", "index": 0}]]}, "Store in sheet": {"main": [[{"node": "Limit", "type": "main", "index": 0}]]}, "Explode the list": {"main": [[{"node": "Add date", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "List of Ingredients and nutrients", "type": "ai_languageModel", "index": 0}]]}, "Transcribe Recording": {"main": [[{"node": "Set chatInput from voice", "type": "main", "index": 0}]]}, "If it's a voice message": {"main": [[{"node": "Get Audio File", "type": "main", "index": 0}], [{"node": "Set chatInput from message", "type": "main", "index": 0}]]}, "Receive Telegram message": {"main": [[{"node": "If it's a voice message", "type": "main", "index": 0}]]}, "Set chatInput from voice": {"main": [[{"node": "List of Ingredients and nutrients", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "List of Ingredients and nutrients", "type": "ai_outputParser", "index": 0}]]}, "Set chatInput from message": {"main": [[{"node": "List of Ingredients and nutrients", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "List of Ingredients and nutrients", "type": "main", "index": 0}]]}, "List of Ingredients and nutrients": {"main": [[{"node": "Explode the list", "type": "main", "index": 0}]]}}}