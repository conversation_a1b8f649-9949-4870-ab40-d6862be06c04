{"meta": {"instanceId": "fb924c73af8f703905bc09c9ee8076f48c17b596ed05b18c0ff86915ef8a7c4a"}, "nodes": [{"id": "ef32b2b5-9739-4622-aa50-ac9e6a93c43c", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [720, 360], "parameters": {}, "typeVersion": 1}, {"id": "d39d67b6-3f3c-4296-affc-cfc786b877c3", "name": "Show 16 random products", "type": "n8n-nodes-base.mySql", "position": [900, 360], "parameters": {"query": "SELECT * from products\nORDER BY RAND()\nLIMIT 16;", "options": {}, "operation": "execute<PERSON>uery"}, "credentials": {"mySql": {"id": "EEPqCgKBDiRRZ3ua", "name": "db4free MySQL"}}, "typeVersion": 2.1}, {"id": "8ca3e11c-d1b0-4ee6-94f0-b16c46b804d7", "name": "Define file structure", "type": "n8n-nodes-base.set", "position": [1120, 200], "parameters": {"values": {"string": [{"name": "Product.Code", "value": "={{ $json.productCode }}"}, {"name": "Product.Name", "value": "={{ $json.productName }}"}, {"name": "Product.Line", "value": "={{ $json.productLine }}"}, {"name": "Product.Scale", "value": "={{ $json.productScale }}"}, {"name": "Product.Price", "value": "={{ $json.MSRP }}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 2}, {"id": "6ee087c9-afd6-405a-b922-61aae33f5a1e", "name": "Concatenate Items", "type": "n8n-nodes-base.itemLists", "position": [1300, 200], "parameters": {"aggregate": "aggregateAllItemData", "operation": "concatenateItems", "destinationFieldName": "Products"}, "typeVersion": 3}, {"id": "0aadf35f-9bec-4d27-9122-2ac350f609f7", "name": "Convert to XML", "type": "n8n-nodes-base.xml", "position": [1480, 200], "parameters": {"mode": "jsonToxml", "options": {"headless": false}}, "typeVersion": 1}, {"id": "5eed6fe8-b7ba-483f-8362-f371e3c678af", "name": "Move Binary Data", "type": "n8n-nodes-base.moveBinaryData", "position": [1680, 200], "parameters": {"mode": "jsonToBinary", "options": {"fileName": "simple.xml", "mimeType": "text/xml", "keepSource": false, "useRawData": true}, "convertAllData": false}, "typeVersion": 1}, {"id": "eb2240a4-cccd-4a4d-a807-1e2bddb0dc89", "name": "Define file structure1", "type": "n8n-nodes-base.set", "position": [1120, 520], "parameters": {"values": {"string": [{"name": "Product.Name", "value": "={{ $json.productName }}"}, {"name": "Product.Line", "value": "={{ $json.productLine }}"}, {"name": "Product.Scale", "value": "={{ $json.productScale }}"}, {"name": "Product.$.Price", "value": "={{ $json.MSRP }}"}, {"name": "Product.$.Code", "value": "={{ $json.productCode }}"}, {"name": "Product.Description", "value": "={{ $json.productDescription }}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 2}, {"id": "06a739b9-72fd-4d9c-9dd2-36086fafae7a", "name": "Concatenate Items1", "type": "n8n-nodes-base.itemLists", "position": [1300, 520], "parameters": {"aggregate": "aggregateAllItemData", "operation": "concatenateItems", "destinationFieldName": "Products"}, "typeVersion": 3}, {"id": "082008c2-d13b-453d-87c2-f551467c6aec", "name": "Convert to XML1", "type": "n8n-nodes-base.xml", "position": [1480, 520], "parameters": {"mode": "jsonToxml", "options": {"attrkey": "$", "headless": false}}, "typeVersion": 1}, {"id": "e66df48f-4c75-4316-910c-0eb8be0bcf9c", "name": "Move Binary Data1", "type": "n8n-nodes-base.moveBinaryData", "position": [1680, 520], "parameters": {"mode": "jsonToBinary", "options": {"fileName": "intermediate.xml", "mimeType": "text/xml", "keepSource": false, "useRawData": true}, "convertAllData": false}, "typeVersion": 1}, {"id": "1ece7a61-04d8-4d0b-aa33-1085a5732a92", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1080, 132], "parameters": {"width": 830, "height": 226, "content": "## Simple conversion to XML"}, "typeVersion": 1}, {"id": "89cca26f-adde-426a-8033-b66224e9b934", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1080, 460], "parameters": {"width": 830, "height": 231, "content": "## XML tags with attributes"}, "typeVersion": 1}, {"id": "0c01e6cf-da59-460a-b1ab-6977ca88c1cc", "name": "Write Binary File", "type": "n8n-nodes-base.writeBinaryFile", "position": [1980, 360], "parameters": {"options": {}, "fileName": "=/home/<USER>/.n8n/{{ $binary.data.fileName }}"}, "typeVersion": 1}], "connections": {"Convert to XML": {"main": [[{"node": "Move Binary Data", "type": "main", "index": 0}]]}, "Convert to XML1": {"main": [[{"node": "Move Binary Data1", "type": "main", "index": 0}]]}, "Move Binary Data": {"main": [[{"node": "Write Binary File", "type": "main", "index": 0}]]}, "Concatenate Items": {"main": [[{"node": "Convert to XML", "type": "main", "index": 0}]]}, "Move Binary Data1": {"main": [[{"node": "Write Binary File", "type": "main", "index": 0}]]}, "Concatenate Items1": {"main": [[{"node": "Convert to XML1", "type": "main", "index": 0}]]}, "Define file structure": {"main": [[{"node": "Concatenate Items", "type": "main", "index": 0}]]}, "Define file structure1": {"main": [[{"node": "Concatenate Items1", "type": "main", "index": 0}]]}, "Show 16 random products": {"main": [[{"node": "Define file structure", "type": "main", "index": 0}, {"node": "Define file structure1", "type": "main", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "Show 16 random products", "type": "main", "index": 0}]]}}}