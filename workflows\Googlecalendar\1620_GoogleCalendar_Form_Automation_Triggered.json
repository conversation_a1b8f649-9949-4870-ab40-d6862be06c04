{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9"}, "nodes": [{"id": "76589d1c-45f3-4a89-906f-8ef300d34964", "name": "n8n Form Trigger", "type": "n8n-nodes-base.formTrigger", "position": [-2520, -280], "webhookId": "5e7637dd-d222-4786-8cdc-7b66cebc1481", "parameters": {"path": "schedule_appointment", "options": {"ignoreBots": true, "appendAttribution": true, "useWorkflowTimezone": true}, "formTitle": "Schedule an Appointment", "formFields": {"values": [{"fieldLabel": "Your Name", "placeholder": "eg. <PERSON>", "requiredField": true}, {"fieldType": "email", "fieldLabel": "Email", "placeholder": "eg. <EMAIL>", "requiredField": true}, {"fieldType": "textarea", "fieldLabel": "Enquiry", "placeholder": "eg. I'm looking for...", "requiredField": true}]}, "formDescription": "Welcome to <PERSON>'s Appointment Form.\nBefore we set a date, please tell me a little about yourself and how I can help."}, "typeVersion": 2.1}, {"id": "194b7073-fa33-4e75-85ed-c02724c8075c", "name": "Form End", "type": "n8n-nodes-base.form", "position": [-420, -260], "webhookId": "8fcc907b-bc2e-4fdf-a829-82c83e677724", "parameters": {"options": {"formTitle": "Appointment Request Sent!"}, "operation": "completion", "completionTitle": "Appointment Request Sent!", "completionMessage": "=Thank you for submitting an appointment request. A confirmation of this request will be sent to your inbox. I'll get back to you shortly with a confirmation of the appointment.\n\nHere is the summary of the appointment request.\n\nName: {{ $('Get Form Values').item.json.name }}\nDate & Time: {{ DateTime.fromISO($('Get Form Values').item.json.dateTime).format('EEE, dd MMM @ t') }}\nEnquiry: {{ $('Get Form Values').item.json.enquiry.trim() }}\n"}, "typeVersion": 1}, {"id": "688ea2cc-b595-4b6f-9214-d5dfd3893172", "name": "Enter Date & Time", "type": "n8n-nodes-base.form", "position": [-1260, -320], "webhookId": "0cd03415-66f8-4c82-8069-5bfd8ea310bd", "parameters": {"options": {"formTitle": "Enter a Date & Time", "formDescription": "=Please select a date and time"}, "defineForm": "json", "jsonOutput": "={{\n[\n {\n \"fieldLabel\":\"Date\",\n \"requiredField\":true,\n \"fieldType\": \"dropdown\",\n \"fieldOptions\":\n Array(5).fill(0)\n .map((_,idx) => $now.plus(idx+1, 'day'))\n .filter(d => !d.isWeekend)\n .map(d => ({ option: d.format('EEE, d MMM') }))\n },\n {\n \"fieldLabel\": \"Time\",\n \"requiredField\": true,\n \"fieldType\": \"dropdown\",\n \"fieldOptions\": [\n { \"option\": \"9:00 am\" },\n { \"option\": \"10:00 am\" },\n { \"option\": \"11:00 am\" },\n { \"option\": \"12:00 pm\" },\n { \"option\": \"1:00 pm\" },\n { \"option\": \"2:00 pm\" },\n { \"option\": \"3:00 pm\" },\n { \"option\": \"4:00 pm\" },\n { \"option\": \"5:00 pm\" },\n { \"option\": \"6:00 pm\" }\n ]\n }\n]\n}}"}, "typeVersion": 1}, {"id": "602c40f9-ab11-4908-aab3-1a199126e097", "name": "Get Form Values", "type": "n8n-nodes-base.set", "position": [-900, -260], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "={{\n{\n name: $('n8n Form Trigger').first().json['Your Name'],\n email: $('n8n Form Trigger').first().json.Email,\n enquiry: $('n8n Form Trigger').first().json.Enquiry,\n dateTime: DateTime.fromFormat(`${$json.Date} ${$json.Time}`, \"EEE, dd MMM t\"),\n submittedAt: $('n8n Form Trigger').first().json.submittedAt,\n}\n}}"}, "typeVersion": 3.4}, {"id": "21f93645-5e27-4e9f-a72c-47a39e42a79c", "name": "Terms & Conditions", "type": "n8n-nodes-base.form", "position": [-1680, -240], "webhookId": "dcf32f99-8fb7-457a-8a58-ac1a018b1873", "parameters": {"options": {"formTitle": "Before we continue...", "formDescription": "=Terms and Conditions for Booking an Appointment\n\nNon-Binding Nature of Discussions:\nAny information shared, discussed, or agreed upon during the call is non-binding and provisional. No agreement, service, or commitment shall be considered confirmed unless explicitly documented and agreed to in writing.\n\nProhibition of Recording and Note-Taking Tools:\nBy proceeding with the appointment, the user agrees not to use AI assistants, note-taking applications, recording devices, or any other technology to record or transcribe the conversation, whether manually or automatically. This is to ensure confidentiality and respect for the integrity of the discussion.\n\nConfirmation of Understanding:\nBy booking this appointment, you acknowledge and accept these terms and conditions in full."}, "formFields": {"values": [{"fieldType": "dropdown", "fieldLabel": "Please select", "multiselect": true, "fieldOptions": {"values": [{"option": "I accept the terms and conditions"}]}, "requiredField": true}]}}, "typeVersion": 1}, {"id": "22e03fec-bd56-4fc3-864a-f1e81a864cb5", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-2340, -140], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "8b4e9bba-cd57-46af-8042-4b47e5ebcd82", "name": "Has Accepted?", "type": "n8n-nodes-base.if", "position": [-1500, -240], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "bc7c3e99-e610-4997-82a7-4851f2c04c19", "operator": {"type": "string", "operation": "startsWith"}, "leftValue": "={{ $json[\"Please select\"] }}", "rightValue": "I accept"}]}}, "typeVersion": 2.2}, {"id": "627a4c00-e831-4a77-8aad-f417f0f8e6dd", "name": "Send Receipt", "type": "n8n-nodes-base.gmail", "position": [-580, -260], "webhookId": "5f590407-4ab9-4ae6-bb85-38dbe41d6dce", "parameters": {"sendTo": "={{ $('Get Form Values').first().json.email }}", "message": "=<p>Dear {{ $('Get Form Values').first().json.name }},</p>\n<p>Thanks for requesting an appointment. We will review and get back to you shortly.</p>\n<p>Here is the summary of the request that was sent:</p>\n<p>\nName: {{ $('Get Form Values').first().json.name }}<br/>\nEmail: {{ $('Get Form Values').first().json.email }}<br/>\nEnquiry: {{ $('Get Form Values').first().json.enquiry }}<br/>\nSubmitted at: {{ $('Get Form Values').first().json.submittedAt }}\n</p>\n", "options": {}, "subject": "=Appointment Request Received for {{ DateTime.fromISO($('Get Form Values').first().json.dateTime).format('EEE, dd MMM @ t') }}"}, "credentials": {"gmailOAuth2": {"id": "Sf5Gfl9NiFTNXFWb", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "91d3dd7d-53f8-4f8e-9af2-ec54cf7f42ad", "name": "Wait for Approval", "type": "n8n-nodes-base.gmail", "position": [340, -260], "webhookId": "ab9c6c5e-334d-44bb-a8fd-a58140bc680d", "parameters": {"sendTo": "=<EMAIL>", "message": "=<h2>A new appointment request was submitted!</h2>\n<p>\nRequesting appointment date is <strong>{{ DateTime.fromISO($('Execute Workflow Trigger').item.json.dateTime).format('EEE, dd MMM @ t') }}</strong>.\n</p>\n<p>\nName: {{ $('Execute Workflow Trigger').first().json.name }}<br/>\nEmail: {{ $('Execute Workflow Trigger').first().json.email }}<br/>\nEnquiry Summary: {{ $json.text }}<br/>\nSubmitted at: {{ $('Execute Workflow Trigger').first().json.submittedAt }}\n</p>", "subject": "New Appointment Request!", "operation": "sendAndWait", "approvalOptions": {"values": {"approvalType": "double", "approveLabel": "Confirm"}}}, "credentials": {"gmailOAuth2": {"id": "Sf5Gfl9NiFTNXFWb", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "7a02b57b-b9b1-45b1-9b3d-aebb84259875", "name": "Has Approval?", "type": "n8n-nodes-base.if", "position": [520, -260], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "e5e37acb-9e9d-4a9e-bf59-a35dfc035886", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.data.approved }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "96aab8be-4c5e-4e14-a6ea-6d2b743551be", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [0, -120], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "6f2b5454-70a3-4391-b785-bb871c3e2081", "name": "Create Appointment", "type": "n8n-nodes-base.googleCalendar", "position": [720, -340], "parameters": {"end": "={{ DateTime.fromISO($('Execute Workflow Trigger').first().json.dateTime).plus(30, 'minute').toISO() }}", "start": "={{ $('Execute Workflow Trigger').first().json.dateTime }}", "calendar": {"__rl": true, "mode": "list", "value": "<EMAIL>", "cachedResultName": "n8n-events"}, "additionalFields": {"summary": "=Appointment Scheduled - {{ $('Execute Workflow Trigger').item.json.name }} & Jim", "attendees": ["={{ $('Execute Workflow Trigger').item.json.email }}"], "description": "={{ $('Summarise Enquiry').first().json.text }}\n\nOriginal message:\n> {{ $('Execute Workflow Trigger').item.json.enquiry }}", "conferenceDataUi": {"conferenceDataValues": {"conferenceSolution": "hangouts<PERSON><PERSON><PERSON>"}}}}, "credentials": {"googleCalendarOAuth2Api": {"id": "kWMxmDbMDDJoYFVK", "name": "Google Calendar account"}}, "typeVersion": 1.2}, {"id": "e6881867-5b3c-4b85-b06a-a0a3c01be227", "name": "Send Rejection", "type": "n8n-nodes-base.gmail", "position": [720, -180], "webhookId": "5f590407-4ab9-4ae6-bb85-38dbe41d6dce", "parameters": {"sendTo": "={{ $('Execute Workflow Trigger').first().json.email }}", "message": "=<p>Dear {{ $('Execute Workflow Trigger').first().json.name }},</p>\n<p>Unfortunately, we cannot schedule the requested appointment at the requested time.</p>\n<p>Kind regards</p>\n", "options": {}, "subject": "=Appointment Request Rejected for {{ DateTime.fromISO($('Execute Workflow Trigger').first().json.dateTime).format('EEE, dd MMM @ t') }}"}, "credentials": {"gmailOAuth2": {"id": "Sf5Gfl9NiFTNXFWb", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "40785eca-943c-45f6-b4a9-0c95538621ed", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-2580, -555.*************], "parameters": {"color": 7, "width": 763.*************, "height": 611.************, "content": "## 1. Qualify Enquiries Using AI\n[Learn more about the text classifier](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.text-classifier/)\n\nWith n8n's multi-forms, you’re no longer stuck creating long, overwhelming forms. Instead, you have more flexibility and control to design smarter, more engaging form experiences.\n\nIn this demo, we’ll explore an appointment request scenario where a user wants to schedule a call to discuss their inquiry. However, not all inquiries require a meeting, making it a perfect use case for AI to pre-qualify the request. We can handle this validation using the text classifier node."}, "typeVersion": 1}, {"id": "985be8d1-e77a-475b-9ac2-dba163dbd950", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-1800, -549.8684464902185], "parameters": {"color": 7, "width": 781.472405063291, "height": 606.0718987341766, "content": "## 2. Split Form For Better User Experience\n[Learn more about the forms](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.form)\n\nOnboarding is a great reason to split your big form into smaller ones. Taking the user through a step by step process ensures a smooth experience and keeps them engaged throughout.\n\nHere, we take the opportunity of the extra context space to display a terms and conditions which the user must agree to making their request. The next form then asks for desired date and time of the event."}, "typeVersion": 1}, {"id": "9b0a3f0e-e15d-4d0e-b620-1acc78bf812c", "name": "Decline", "type": "n8n-nodes-base.form", "position": [-2020, -160], "webhookId": "4353eadb-b7a0-45f2-8dd8-5f6cd882d8d8", "parameters": {"options": {}, "operation": "completion", "completionTitle": "Send me a DM Instead!", "completionMessage": "Thanks for your enquiry but it may not necessarily need an appointment. Please feel free to email me <NAME_EMAIL>."}, "typeVersion": 1}, {"id": "fcd3eb7d-6389-4c07-97cc-275ae387c963", "name": "Decline1", "type": "n8n-nodes-base.form", "position": [-1260, -160], "webhookId": "4353eadb-b7a0-45f2-8dd8-5f6cd882d8d8", "parameters": {"options": {}, "operation": "completion", "completionTitle": "Send me a DM Instead!", "completionMessage": "Thanks for your enquiry but it may not necessarily need an appointment. Please feel free to email me <NAME_EMAIL>."}, "typeVersion": 1}, {"id": "d89427cb-fffb-4aa4-b55c-b315fa0e92be", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-1000, -498.80432681242814], "parameters": {"color": 7, "width": 792.9401150747982, "height": 497.4250863060987, "content": "## 3. Send Acknowledgement to User and Start Approval Process\n[Learn more about the Gmail node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.gmail/)\n\nOnce all form steps are concluded, we can send a notification to the requester via email and in the background, trigger another email to the admin to initiate the approval process. The approval process works in a separate execution so doesn't interrupt the user's form experience."}, "typeVersion": 1}, {"id": "041081e1-ee98-4b40-aa14-1980b23f4031", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [-160, -620], "parameters": {"color": 7, "width": 609.4228768699652, "height": 287.178089758343, "content": "## 4. Approve or Decline Appointment\n[Learn more about the Waiting for Approval](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.gmail/message-operations/#send-a-message-and-wait-for-approval)\n\nThe Wait for Approval feature for Gmail is a special operation which allows for human-in-the-loop interaction in n8n workflows. In this example, the human interaction is the approval of the appointment request. The feature will put the workflow in a waiting state where a message is sent to the admin with 2 buttons: confirm and decline.\n\nWhen the admin clicks on the confirm button, the workflow resumes from the Gmail node and a meeting event is created for the requesting user in Google Calendar.\n\nWhen declined, a rejection email is sent to the requester instead."}, "typeVersion": 1}, {"id": "d6af0f50-234f-46ca-aa41-7f3891aff8a3", "name": "Trigger Approval Process", "type": "n8n-nodes-base.executeWorkflow", "position": [-740, -260], "parameters": {"mode": "each", "options": {"waitForSubWorkflow": false}, "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}}, "typeVersion": 1.1}, {"id": "e524d6df-9b6d-4d61-8e71-08a0d3a751d7", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-160, -260], "parameters": {}, "typeVersion": 1}, {"id": "74dccbc1-7728-4336-a18a-2541007fd369", "name": "Summarise Enquiry", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [0, -260], "parameters": {"text": "=The enquiry is as follows:\n{{ $('Execute Workflow Trigger').first().json.enquiry.substring(0, 500) }}", "messages": {"messageValues": [{"message": "Summarise the given enquiry"}]}, "promptType": "define"}, "typeVersion": 1.5}, {"id": "b74f0f5a-39f0-4db3-beba-03caf981c5d2", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [-3080, -640], "parameters": {"width": 468.6766398158801, "height": 690.6653164556957, "content": "## Try it out!\n\n### This n8n template is a simple appointment scheduling workflow using n8n forms with AI thrown in the mix for good measure. It also uses n8n's wait for approval feature which allows the ability to confirm appointment requests and create events in Google Calendar.\n\n### How it works\n* We start with a form trigger which asks for the purpose of the appointment.\n* Instantly, we can qualify this by using a text classifier node which uses AI's contextual understanding to ensure the appointment is worthwhile. If not, an alternative is suggested instead.\n* Multi-page forms are then used to set the terms of the appointment and ask the user for a desired date and time.\n* An acknowledgement is sent to the user while an approval by email process is triggered in the background.\n* In a subworkflow, we use Gmail with the wait for approval operation to send an approval form to the admin user who can either confirm or decline the appointment request.\n* When approved, a Google Calendar event is created. When declined, the user is notified via email that the appointment request was declined.\n\n### Need Help?\nJoin the [Discord](https://discord.com/invite/XPKeKXeB7d) or ask in the [Forum](https://community.n8n.io/)!\n\nHappy Hacking!\n"}, "typeVersion": 1}, {"id": "d3c87dfa-d6e5-402a-89e5-6d8f93b824a6", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [299, -280], "parameters": {"width": 177.66444188722656, "height": 257.56869965477557, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n### 🚨 Set your admin email here!"}, "typeVersion": 1}, {"id": "6351121d-6ebe-432d-b370-13296fd58e1a", "name": "Enquiry Classifier", "type": "@n8n/n8n-nodes-langchain.textClassifier", "position": [-2340, -280], "parameters": {"options": {"fallback": "other"}, "inputText": "={{ $json.Enquiry }}", "categories": {"categories": [{"category": "relevant enquiry", "description": "Enquire about AI, automation, digital products and product engineering."}]}}, "typeVersion": 1}], "pinData": {}, "connections": {"Send Receipt": {"main": [[{"node": "Form End", "type": "main", "index": 0}]]}, "Has Accepted?": {"main": [[{"node": "Enter Date & Time", "type": "main", "index": 0}], [{"node": "Decline1", "type": "main", "index": 0}]]}, "Has Approval?": {"main": [[{"node": "Create Appointment", "type": "main", "index": 0}], [{"node": "Send Rejection", "type": "main", "index": 0}]]}, "Get Form Values": {"main": [[{"node": "Trigger Approval Process", "type": "main", "index": 0}]]}, "n8n Form Trigger": {"main": [[{"node": "Enquiry Classifier", "type": "main", "index": 0}]]}, "Enter Date & Time": {"main": [[{"node": "Get Form Values", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Enquiry Classifier", "type": "ai_languageModel", "index": 0}]]}, "Summarise Enquiry": {"main": [[{"node": "Wait for Approval", "type": "main", "index": 0}]]}, "Wait for Approval": {"main": [[{"node": "Has Approval?", "type": "main", "index": 0}]]}, "Enquiry Classifier": {"main": [[{"node": "Terms & Conditions", "type": "main", "index": 0}], [{"node": "Decline", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Summarise Enquiry", "type": "ai_languageModel", "index": 0}]]}, "Terms & Conditions": {"main": [[{"node": "Has Accepted?", "type": "main", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "Summarise Enquiry", "type": "main", "index": 0}]]}, "Trigger Approval Process": {"main": [[{"node": "Send Receipt", "type": "main", "index": 0}]]}}}