{"id": "15", "name": "Tools / Backup Gitlab", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 400], "parameters": {}, "typeVersion": 1}, {"name": "Export Workflows", "type": "n8n-nodes-base.executeCommand", "position": [450, 300], "parameters": {"command": "npx n8n export:workflow --backup --output repo/workflows/"}, "typeVersion": 1}, {"name": "Export Credentials", "type": "n8n-nodes-base.executeCommand", "position": [600, 300], "parameters": {"command": "npx n8n export:credentials --backup --output repo/credentials/"}, "typeVersion": 1}, {"name": "git add", "type": "n8n-nodes-base.executeCommand", "position": [750, 300], "parameters": {"command": "git -C repo add ."}, "typeVersion": 1}, {"name": "git commit", "type": "n8n-nodes-base.executeCommand", "position": [900, 300], "parameters": {"command": "=git -C repo commit -m \"Auto backup ({{ new Date().toISOString() }})\""}, "typeVersion": 1}, {"name": "git push", "type": "n8n-nodes-base.executeCommand", "position": [1050, 300], "parameters": {"command": "git -C repo push"}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [250, 200], "parameters": {"triggerTimes": {"item": [{"hour": 0}, {"hour": 12}, {"hour": 6}, {"hour": 18}]}}, "typeVersion": 1}], "active": true, "settings": {}, "connections": {"Cron": {"main": [[{"node": "Export Workflows", "type": "main", "index": 0}]]}, "git add": {"main": [[{"node": "git commit", "type": "main", "index": 0}]]}, "git commit": {"main": [[{"node": "git push", "type": "main", "index": 0}]]}, "Export Workflows": {"main": [[{"node": "Export Credentials", "type": "main", "index": 0}]]}, "Export Credentials": {"main": [[{"node": "git add", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Export Workflows", "type": "main", "index": 0}]]}}}