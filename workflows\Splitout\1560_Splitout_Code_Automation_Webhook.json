{"id": "NGwD3pIHXBU0w5hC", "meta": {"instanceId": "ae2372ebbc56db2b55a9a46ac3affa802af144b84fd97c2796c22342aba529bd"}, "name": "[n8n] - Shopify Orders to D365 Business Central Sales Orders / Sales Invoices", "tags": [{"id": "2RJGhx5RHCJdXr52", "name": "d365 business central", "createdAt": "2023-08-08T23:10:56.527Z", "updatedAt": "2023-08-08T23:10:56.527Z"}, {"id": "OPc1YLQyTimMr498", "name": "shopify", "createdAt": "2023-07-22T15:30:38.620Z", "updatedAt": "2023-07-22T15:30:38.620Z"}], "nodes": [{"id": "92db12db-d96d-4076-a9cd-441c4bdfe212", "name": "GetFufillmentOrders", "type": "n8n-nodes-base.httpRequest", "position": [840, 300], "parameters": {"url": "=https://integrocloud.myshopify.com/admin/api/2024-01/orders/{{ $json.id }}/fulfillment_orders.json", "options": {}, "sendHeaders": true, "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "nodeCredentialType": "shopifyAccessTokenApi"}, "credentials": {"httpHeaderAuth": {"id": "BkNv57yQW9PSPr6p", "name": "Shopify HTTP Token Auth"}, "shopifyAccessTokenApi": {"id": "9P9B0Hcwyj2CpqeA", "name": "Shopify Access Token account"}}, "typeVersion": 4.1}, {"id": "60e0bd37-a2d1-48c5-8b47-830094d5e2ae", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [780, 140], "parameters": {"width": 730.*************, "height": 394.*************, "content": "## Shopify Line Locations\nFor multi-location Shopify accounts, these group of nodes get the active location id for each order line."}, "typeVersion": 1}, {"id": "1e91817c-26bf-46f8-8185-696f07daa28c", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [400, 140], "parameters": {"width": 354.**************, "height": 398.*************, "content": "## Get Shopify Orders\n1.- Get Shopify Orders created/updated since one day prior. The Flow will get every order created or updated on the last 24 hours.\n\n2.- Filter to get paid orders."}, "typeVersion": 1}, {"id": "89f633a1-ac8f-4480-934b-e429717cb09f", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [1557, 140], "parameters": {"width": 974.*************, "height": 520.*************, "content": "## Existing Customer Lookup (Business Central)\nLookup for existing customer in Business Central based on the logic defined in the URI, if a customer exist then that id is used, otherwhise a new customer will be created\n"}, "typeVersion": 1}, {"id": "c973b647-c1c6-43dc-9b80-46e34d051fc4", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [-640, 140], "parameters": {"color": 3, "width": 509.9611*********, "height": 705.*************, "content": "## Workflow Information 📌\n\n### Purpose 🎯\nThe intention of this workflow is to integrate New Shopify Orders into MS Dynamics Business Central:\n\n- **Point-of-Sale (POS):** POS orders will be created in Business Central as Sales Invoices given no fulfillment is expected.\n- **Web Orders:** This type of orders will be created as Business Central Sales Orders.\n\n### How to use it 🚀\n1. Edit the \"D365 BC Environment Settings\" node with your own account values (Company Id, Tenanant Id, Tax & Discount Items).\n2. Go to the \"Shopify\" node and edit the connection with your environment. More help [here](https://docs.n8n.io/integrations/builtin/credentials/shopify/).\n3. Go to the \"Lookup Customers\" node to edit the Business Central connection details with your environment settings.\n4. Set the required filters on the \"Shopify Order Filter\" node.\n5. Edit the \"Schedule Trigger\" node with the required frequency.\n\n### Useful Workflow Links 📚\n1. [Step-by-step Guide/ Integro Cloud Solutions](https://z0v4z2m6gixudcjglfbe.guidejar.xyz/categories/business-central)\n2. [Business Central REST API Documentation](https://learn.microsoft.com/en-us/dynamics365/business-central/dev-itpro/api-reference/v2.0/)\n3. [Video Demo](https://www.loom.com/share/9e218cd53cf14a93bcb55d7b3d47ec45?sid=5fdfb8ab-8205-468a-b514-67193abac455)\n\n\n### Need Help?\nContact me at:\n✉️<EMAIL>\n📥 https://www.linkedin.com/in/greg-lopez-08b5071b/\n\n\n\n"}, "typeVersion": 1}, {"id": "e7c4bf60-e040-4f41-9c8a-7729ffed88fd", "name": "Shopify", "type": "n8n-nodes-base.shopify", "position": [440, 300], "parameters": {"options": {"status": "any", "updatedAtMin": "={{$now.minus({days: 1})}}"}, "operation": "getAll", "returnAll": true, "authentication": "accessToken"}, "credentials": {"shopifyAccessTokenApi": {"id": "9P9B0Hcwyj2CpqeA", "name": "Shopify Access Token account"}}, "typeVersion": 1}, {"id": "274b710d-e642-4bc6-bf8d-5852a721f037", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1560, 730.*************], "parameters": {"width": 978.*************, "height": 502.*************, "content": "## Existing Order Lookup (Business Central)\n\n1.- This logic will avoid duplication of Business Central Sales Orders/Sales Invoices validating if an order with the same external Id exist already.\n\n2.- If a match is found then the order is ignored\n\n3.- The source of the order is evaluated, if the order was placed on the Point-of-Sale a Sales Invoice is created else a Sales Order will be created."}, "typeVersion": 1}, {"id": "ccf15ee2-b805-4fa7-88ab-12bf3c864415", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [2600, 700], "parameters": {"width": 1330.*************, "height": 434.**************, "content": "## Sales Order Creation\n\n1.- Map on the \"Sales Order Mapping\" node any requiered fields to be integrated into Business Central.\n\n2- The HTTP Node will perform a POST call to Business Central REST API to create the Sales Order.\n\n3. After the Sales Order gets created, all line items will be added into the Order. \n\n4. If there are any while creating line items, the Sales Order will be deleted.\n"}, "typeVersion": 1}, {"id": "5a78d974-d950-4e26-87cf-a42e4633a5d8", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [-68.32736133691077, 140], "parameters": {"width": 442.73662194943114, "height": 398.*************, "content": "## Configure Business Central Environment Variables\n1.- Enter your BC tenantId,companyId, name.\n2.- Set the SKU number for the Items to be used for Taxes and Discounts."}, "typeVersion": 1}, {"id": "95f15005-6c9b-46ae-9cb3-a89887189aed", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-20, 340], "parameters": {"rule": {"interval": [{"field": "minutes"}]}}, "typeVersion": 1.1}, {"id": "0c2ee7ac-3b27-4e6f-9e65-f1bba3ee494b", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [1620, 260], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "58a528fe-f9f3-4522-bc4d-0fc91fbbf656", "name": "New Customer?", "type": "n8n-nodes-base.if", "position": [2020, 260], "parameters": {"conditions": {"number": [{"value1": "={{ $json.value.length }}", "value2": 1}]}}, "typeVersion": 1}, {"id": "e5222a97-002c-4433-aa7a-dbc6426b2a25", "name": "Lookup Customers", "type": "n8n-nodes-base.httpRequest", "position": [1840, 260], "parameters": {"url": "=https://api.businesscentral.dynamics.com/v2.0/{{ $('D365 BC Environment Settings').item.json[\"tenantId\"] }}/{{ $('D365 BC Environment Settings').item.json[\"environmentName\"] }}/api/v2.0/companies({{ $('D365 BC Environment Settings').item.json[\"companyId\"] }})/customers?$filter=email eq '{{ $json.customer.email }}' and contains(email,'@')&$select=id,number,email", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "oAuth2Api"}, "credentials": {"oAuth2Api": {"id": "s8gGHYzOwlhE9yot", "name": "PROD_businessCentral_integro"}}, "typeVersion": 4.1, "continueOnFail": true}, {"id": "8d5ba820-04a7-413e-bb9e-8385dee5e78b", "name": "SelectFields", "type": "n8n-nodes-base.set", "position": [1080, 300], "parameters": {"values": {"string": [{"name": "id", "value": "={{ $('Filter').item.json.id }}"}, {"name": "name", "value": "={{ $('Filter').item.json.name }}"}, {"name": "source_name", "value": "={{ $('Filter').item.json.source_name }}"}, {"name": "shipping_address", "value": "={{ $('Filter').item.json.shipping_address }}"}, {"name": "billing_address", "value": "={{ $('Filter').item.json.billing_address }}"}, {"name": "customer", "value": "={{ $('Filter').item.json.customer }}"}, {"name": "discount_codes", "value": "={{ $('Filter').item.json.discount_codes}}"}, {"name": "shippingcost", "value": "={{ $('Filter').item.json.total_shipping_price_set.shop_money.amount }}"}, {"name": "line_items", "value": "={{ $('Filter').item.json.line_items }}"}, {"name": "fulfillment_orders", "value": "={{ $json.fulfillment_orders }}"}, {"name": "currency", "value": "={{ $('Filter').item.json.currency }}"}, {"name": "=created_at", "value": "={{ $('Filter').item.json.created_at }}"}, {"name": "gateway", "value": "={{ $('Filter').item.json.payment_gateway_names[0] }}"}, {"name": "total_tax", "value": "={{ $('Filter').item.json.total_tax }}"}, {"name": "total_discounts", "value": "={{ $('Filter').item.json.total_discounts*-1 }}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 2}, {"id": "d5120009-2efe-4ef1-9450-eedd475f95c7", "name": "orderPreprocessing", "type": "n8n-nodes-base.code", "position": [1340, 300], "parameters": {"mode": "runOnceForEachItem", "jsCode": "const orderJson = $input.item.json;\n\n// Create a map of line_item_id to assigned_location_id\nconst lineItemToLocationMap = {};\norderJson.fulfillment_orders.forEach(fulfillmentOrder => {\n  fulfillmentOrder.line_items.forEach(lineItem => {\n    lineItemToLocationMap[lineItem.line_item_id] = fulfillmentOrder.assigned_location_id;\n  });\n});\n\n// Update the line_items array with assigned_location_id\norderJson.line_items.forEach(lineItem => {\n  const assignedLocationId = lineItemToLocationMap[lineItem.id];\n  if (assignedLocationId !== undefined) {\n    lineItem.assigned_location_id = assignedLocationId;\n  }\n});\n\n// Add a new property 'pairedItem' to orderJson with the value of $itemIndex\norderJson.pairedItem = $itemIndex;\n\n// Add a new line item with specified fields for taxes if taxesAsLineItem is true\nif ($('D365 BC Environment Settings').item.json.taxesAsLineItem) {\n  const newLineItem = {\n    \"sku\": $('D365 BC Environment Settings').item.json.taxItemSku,\n    \"price\": orderJson.total_tax,\n    \"quantity\": 1\n  };\n  orderJson.line_items.push(newLineItem);\n}\n\n// Add a new line item with specified fields for discount\nif ($('D365 BC Environment Settings').item.json.discountsAsLineItem) {\nconst newDiscountLineItem = {\n  \"sku\": $('D365 BC Environment Settings').item.json.discountItemSku,\n  \"price\": orderJson.total_discounts,\n  \"quantity\": 1\n};\norderJson.line_items.push(newDiscountLineItem);\n}\n\n// Return the modified orderJson\nreturn orderJson;\n"}, "typeVersion": 2}, {"id": "28f0b15a-e1a6-4055-90ed-f3051f043792", "name": "Create Customer", "type": "n8n-nodes-base.httpRequest", "position": [2240, 320], "parameters": {"url": "=https://api.businesscentral.dynamics.com/v2.0/{{ $('D365 BC Environment Settings').item.json[\"tenantId\"] }}/{{ $('D365 BC Environment Settings').item.json[\"environmentName\"] }}/api/v2.0/companies({{ $('D365 BC Environment Settings').item.json[\"companyId\"] }})/customers", "method": "POST", "options": {"response": {"response": {}}}, "sendBody": true, "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "displayName", "value": "={{ $('orderPreprocessing').item.json.customer.first_name }} {{ $('orderPreprocessing').item.json.customer.last_name }}"}, {"name": "type", "value": "Person"}, {"name": "email", "value": "={{ $('Loop Over Items').item.json.customer.email }}"}, {"name": "taxLiable", "value": "true"}, {"name": "currencyCode", "value": "={{ $('Loop Over Items').item.json.currency }}"}, {"name": "addressLine1", "value": "={{ $('Loop Over Items').item.json.shipping_address.address1 }}"}, {"name": "addressLine2", "value": "={{ $('Loop Over Items').item.json.shipping_address.address2 }}"}, {"name": "city", "value": "={{ $('Loop Over Items').item.json.shipping_address.city }}"}, {"name": "state", "value": "={{ $('Loop Over Items').item.json.shipping_address.province }}"}, {"name": "country", "value": "={{ $('Loop Over Items').item.json.shipping_address.country_code }}"}, {"name": "postalCode", "value": "={{ $('Loop Over Items').item.json.shipping_address.zip }}"}, {"name": "phoneNumber", "value": "={{ $('Loop Over Items').item.json.shipping_address.phone }}"}]}, "genericAuthType": "oAuth2Api", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "credentials": {"oAuth2Api": {"id": "s8gGHYzOwlhE9yot", "name": "PROD_businessCentral_integro"}}, "notesInFlow": true, "typeVersion": 4.1, "continueOnFail": true, "alwaysOutputData": false}, {"id": "990fec80-30e3-44f8-a95d-f9afb2e495c5", "name": "Set Business Central Customer Id", "type": "n8n-nodes-base.set", "position": [1780, 500], "parameters": {"values": {"string": [{"name": "order", "value": "={{ $('orderPreprocessing').item.json }}"}, {"name": "bc_customer.id", "value": "={{ $json.value.isEmpty() ? $json.id : $json.value[0].id}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 2}, {"id": "313ac019-aedb-4d64-833d-c3582153e2c0", "name": "Create Order Lines", "type": "n8n-nodes-base.httpRequest", "onError": "continueErrorOutput", "position": [3440, 900], "parameters": {"url": "=https://api.businesscentral.dynamics.com/v2.0/{{ $('D365 BC Environment Settings').item.json.tenantId }}/{{ $('D365 BC Environment Settings').item.json.environmentName }}/api/v2.0/companies({{ $('D365 BC Environment Settings').item.json.companyId }})/salesOrders({{ $json.so_id }})/salesOrderLines", "method": "POST", "options": {"batching": {"batch": {"batchSize": 0}}, "response": {"response": {}}}, "sendBody": true, "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "lineObjectNumber", "value": "={{ $json.line_items.sku }}"}, {"name": "quantity", "value": "={{ $json.line_items.quantity }}"}, {"name": "description", "value": "={{ $json.line_items.title }}"}, {"name": "lineType", "value": "<PERSON><PERSON>"}, {"name": "unitPrice", "value": "={{ $json.line_items.price*1 }}"}]}, "genericAuthType": "oAuth2Api", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "credentials": {"oAuth2Api": {"id": "s8gGHYzOwlhE9yot", "name": "PROD_businessCentral_integro"}}, "typeVersion": 4.1}, {"id": "e0d63859-a480-4f69-bb30-77c9615777b6", "name": "End", "type": "n8n-nodes-base.noOp", "position": [3720, 840], "parameters": {}, "typeVersion": 1}, {"id": "8c756b31-0b7f-4f9a-a7a3-5fccdcfcf8b8", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [3260, 900], "parameters": {"include": "allOtherFields", "options": {}, "fieldToSplitOut": "=line_items"}, "typeVersion": 1}, {"id": "e2ddcd9a-7502-49ae-9d5c-50f6c3084570", "name": "DELETE Sales Order", "type": "n8n-nodes-base.httpRequest", "onError": "continueRegularOutput", "position": [3720, 980], "parameters": {"url": "=https://api.businesscentral.dynamics.com/v2.0/{{ $('D365 BC Environment Settings').item.json.tenantId }}/Production/api/v2.0/companies({{ $('D365 BC Environment Settings').item.json.companyId }})/salesOrders({{ $json.so_id }})", "method": "DELETE", "options": {"batching": {"batch": {"batchSize": 1}}, "response": {"response": {}}}, "sendBody": true, "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{}]}, "genericAuthType": "oAuth2Api", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "credentials": {"oAuth2Api": {"id": "s8gGHYzOwlhE9yot", "name": "PROD_businessCentral_integro"}}, "typeVersion": 4.1}, {"id": "b5303dc5-bf56-4b1c-a231-15a322f26ac8", "name": "D365 BC Environment Settings", "type": "n8n-nodes-base.set", "position": [180, 340], "parameters": {"values": {"string": [{"name": "tenantId", "value": "{tenandId}"}, {"name": "environmentName", "value": "Production"}, {"name": "companyId", "value": "{CompanyId}"}, {"name": "discountItemSku", "value": "N8N_DISCOUNT"}, {"name": "taxItemSku", "value": "N8N_TAX_AMOUNT"}], "boolean": [{"name": "taxesAsLineItem", "value": true}, {"name": "discountsAsLineItem", "value": true}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 2}, {"id": "e7925d36-5b30-4822-8eb1-a5a076a77669", "name": "Create Sales Order", "type": "n8n-nodes-base.httpRequest", "notes": "Create Sales Order Header", "onError": "continueErrorOutput", "position": [2880, 920], "parameters": {"url": "=https://api.businesscentral.dynamics.com/v2.0/{{ $('D365 BC Environment Settings').item.json.tenantId }}/{{ $('D365 BC Environment Settings').item.json.environmentName }}/api/v2.0/companies({{ $('D365 BC Environment Settings').item.json.companyId }})/salesOrders", "method": "POST", "options": {"batching": {"batch": {"batchSize": 5, "batchInterval": 5000}}, "response": {"response": {}}}, "jsonBody": "={{$json}}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "oAuth2Api", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "credentials": {"oAuth2Api": {"id": "s8gGHYzOwlhE9yot", "name": "PROD_businessCentral_integro"}}, "notesInFlow": true, "typeVersion": 4.1}, {"id": "fce6d383-c372-4f65-83b5-d681dfa16323", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [2600, 140], "parameters": {"width": 1330.*************, "height": 434.**************, "content": "## Sales Order Creation\n\n1. Map on the \"Sales Invoice Mapping\" node any requiered fields to be integrated into Business Central.\n\n2. The HTTP Node will perform a POST call to Business Central REST API to create the Sales Invoice.\n\n3. After the Sales Invoice gets created, all line items will be added into the Invoice. \n\n4. If there are any while creating line items, the Sales Invoice will be deleted.\n"}, "typeVersion": 1}, {"id": "29d43335-713f-4bfc-a416-be41f7cc1311", "name": "Set Lines Invoice", "type": "n8n-nodes-base.set", "position": [3100, 360], "parameters": {"values": {"string": [{"name": "so_id", "value": "={{ $json.id }}"}, {"name": "line_items", "value": "={{ $('Set Business Central Customer Id').item.json.order.line_items }}"}]}, "options": {"dotNotation": true}, "keepOnlySet": true}, "typeVersion": 2}, {"id": "7d05a3d7-5bac-4099-8561-61b520c75e91", "name": "Set Lines SO", "type": "n8n-nodes-base.set", "position": [3100, 900], "parameters": {"values": {"string": [{"name": "so_id", "value": "={{ $json.id }}"}, {"name": "line_items", "value": "={{ $('Set Business Central Customer Id').item.json.order.line_items }}"}]}, "options": {"dotNotation": true}, "keepOnlySet": true}, "typeVersion": 2}, {"id": "e39a775a-4c89-47f0-8da9-e5cb33d03228", "name": "Split Out Invoice", "type": "n8n-nodes-base.splitOut", "position": [3260, 360], "parameters": {"include": "allOtherFields", "options": {}, "fieldToSplitOut": "=line_items"}, "typeVersion": 1}, {"id": "ff21ea27-091c-4ba7-bedb-1e26561ff042", "name": "Create Invoice Lines", "type": "n8n-nodes-base.httpRequest", "onError": "continueErrorOutput", "position": [3440, 360], "parameters": {"url": "=https://api.businesscentral.dynamics.com/v2.0/{{ $('D365 BC Environment Settings').item.json.tenantId }}/{{ $('D365 BC Environment Settings').item.json.environmentName }}/api/v2.0/companies({{ $('D365 BC Environment Settings').item.json.companyId }})/salesInvoices({{ $json.so_id }})/salesInvoiceLines", "method": "POST", "options": {"batching": {"batch": {"batchSize": 0}}, "response": {"response": {}}}, "sendBody": true, "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "lineObjectNumber", "value": "={{ $json.line_items.sku }}"}, {"name": "quantity", "value": "={{ $json.line_items.quantity }}"}, {"name": "description", "value": "={{ $json.line_items.title }}"}, {"name": "lineType", "value": "<PERSON><PERSON>"}, {"name": "unitPrice", "value": "={{ $json.line_items.price*1 }}"}]}, "genericAuthType": "oAuth2Api", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "credentials": {"oAuth2Api": {"id": "s8gGHYzOwlhE9yot", "name": "PROD_businessCentral_integro"}}, "typeVersion": 4.1}, {"id": "becfe8a8-9655-4386-b891-977271b26c7e", "name": "Filter", "type": "n8n-nodes-base.filter", "position": [620, 300], "parameters": {"conditions": {"string": [{"value1": "={{ $json.financial_status }}", "value2": "paid"}]}}, "typeVersion": 1}, {"id": "48edfcb3-5864-4794-8876-40bb7bec31f6", "name": "Create Sales Invoice", "type": "n8n-nodes-base.httpRequest", "notes": "Create Sales Order Header", "onError": "continueErrorOutput", "position": [2900, 380], "parameters": {"url": "=https://api.businesscentral.dynamics.com/v2.0/{{ $('D365 BC Environment Settings').item.json.tenantId }}/{{ $('D365 BC Environment Settings').item.json.environmentName }}/api/v2.0/companies({{ $('D365 BC Environment Settings').item.json.companyId }})/salesInvoices", "method": "POST", "options": {"batching": {"batch": {"batchSize": 5, "batchInterval": 5000}}, "response": {"response": {}}}, "jsonBody": "={{$json}}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "oAuth2Api", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "credentials": {"oAuth2Api": {"id": "s8gGHYzOwlhE9yot", "name": "PROD_businessCentral_integro"}}, "notesInFlow": true, "typeVersion": 4.1}, {"id": "a2879c0f-55d2-442a-be5e-cb249af88561", "name": "End1", "type": "n8n-nodes-base.noOp", "position": [3720, 300], "parameters": {}, "typeVersion": 1}, {"id": "4c979801-ab23-41a7-bc14-179d128c2bf7", "name": "Sales Invoice", "type": "n8n-nodes-base.set", "position": [2700, 380], "parameters": {"fields": {"values": [{"name": "customerId", "stringValue": "={{ $('Set Business Central Customer Id').item.json.bc_customer.id }}"}, {"name": "invoiceDate", "stringValue": "={{ DateTime.fromISO($('Set Business Central Customer Id').item.json.order.created_at).toFormat('yyyy-MM-dd').toString() }}"}, {"name": "externalDocumentNumber", "stringValue": "={{ $('Set Business Central Customer Id').item.json.order.id.toString() }}"}, {"name": "currencyCode", "stringValue": "={{ $('Set Business Central Customer Id').item.json.order.currency }}"}, {"name": "sellToAddressLine1", "stringValue": "={{ $('Set Business Central Customer Id').item.json.order.shipping_address.address1 }}"}, {"name": "sellToAddressLine2", "stringValue": "={{ $('Set Business Central Customer Id').item.json.order.shipping_address.address2 }}"}, {"name": "sellToCity", "stringValue": "={{ $('Set Business Central Customer Id').item.json.order.shipping_address.city }}"}, {"name": "sellToCountry", "stringValue": "={{ $('Set Business Central Customer Id').item.json.order.shipping_address.country_code }}"}, {"name": "sellToState", "stringValue": "={{ $('Set Business Central Customer Id').item.json.order.shipping_address.province }}"}, {"name": "sellToPostCode", "stringValue": "={{ $('Set Business Central Customer Id').item.json.order.shipping_address.zip }}"}]}, "include": "none", "options": {}}, "typeVersion": 3.2}, {"id": "022de50a-0030-434d-95b8-edef8eacb481", "name": "Lookup Sales Order", "type": "n8n-nodes-base.httpRequest", "position": [2120, 1080], "parameters": {"url": "=https://api.businesscentral.dynamics.com/v2.0/{{ $('D365 BC Environment Settings').item.json[\"tenantId\"] }}/{{ $('D365 BC Environment Settings').item.json[\"environmentName\"] }}/api/v2.0/companies({{ $('D365 BC Environment Settings').item.json[\"companyId\"] }})/salesOrders?$filter=externalDocumentNumber eq '{{ $json.order.id.toString() }}'&$select=id,number,externalDocumentNumber", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "oAuth2Api"}, "credentials": {"oAuth2Api": {"id": "s8gGHYzOwlhE9yot", "name": "PROD_businessCentral_integro"}}, "typeVersion": 4.1, "continueOnFail": true}, {"id": "8e646fe3-5a28-4bf4-8cc1-d31a13294218", "name": "Sales Order Mapping", "type": "n8n-nodes-base.set", "position": [2660, 920], "parameters": {"fields": {"values": [{"name": "customerId", "stringValue": "={{ $('Set Business Central Customer Id').item.json.bc_customer.id }}"}, {"name": "OrderDate", "stringValue": "={{ DateTime.fromISO($('Set Business Central Customer Id').item.json.order.created_at).toFormat('yyyy-MM-dd').toString() }}"}, {"name": "externalDocumentNumber", "stringValue": "={{ $('Set Business Central Customer Id').item.json.order.id.toString() }}"}, {"name": "currencyCode", "stringValue": "={{ $('Set Business Central Customer Id').item.json.order.currency }}"}, {"name": "sellToAddressLine1", "stringValue": "={{ $('Set Business Central Customer Id').item.json.order.shipping_address.address1 }}"}, {"name": "sellToAddressLine2", "stringValue": "={{ $('Set Business Central Customer Id').item.json.order.shipping_address.address2 }}"}, {"name": "sellToCity", "stringValue": "={{ $('Set Business Central Customer Id').item.json.order.shipping_address.city }}"}, {"name": "sellToCountry", "stringValue": "={{ $('Set Business Central Customer Id').item.json.order.shipping_address.country_code }}"}, {"name": "sellToState", "stringValue": "={{ $('Set Business Central Customer Id').item.json.order.shipping_address.province }}"}, {"name": "sellToPostCode", "stringValue": "={{ $('Set Business Central Customer Id').item.json.order.shipping_address.zip }}"}]}, "include": "none", "options": {}}, "typeVersion": 3.2}, {"id": "3839e2f2-54a2-4bbb-b0af-e10d5da0da82", "name": "New SO?", "type": "n8n-nodes-base.if", "position": [2300, 1080], "parameters": {"conditions": {"number": [{"value1": "={{ $json.value.length }}", "operation": "smallerEqual"}]}}, "typeVersion": 1}, {"id": "54292ca1-8d99-4ef8-8f46-a17633bd8a9d", "name": "Lookup Sales Invoice", "type": "n8n-nodes-base.httpRequest", "position": [2120, 920], "parameters": {"url": "=https://api.businesscentral.dynamics.com/v2.0/{{ $('D365 BC Environment Settings').item.json[\"tenantId\"] }}/{{ $('D365 BC Environment Settings').item.json[\"environmentName\"] }}/api/v2.0/companies({{ $('D365 BC Environment Settings').item.json[\"companyId\"] }})/salesInvoices?$filter=externalDocumentNumber eq '{{ $json.order.id.toString() }}'&$select=id,number,externalDocumentNumber", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "oAuth2Api"}, "credentials": {"oAuth2Api": {"id": "s8gGHYzOwlhE9yot", "name": "PROD_businessCentral_integro"}}, "typeVersion": 4.1, "continueOnFail": true}, {"id": "6c531d1b-a605-4212-88c9-292b818ca5d4", "name": "New Invoice?", "type": "n8n-nodes-base.if", "position": [2300, 920], "parameters": {"conditions": {"number": [{"value1": "={{ $json.value.length }}", "operation": "smallerEqual"}]}}, "typeVersion": 1}, {"id": "6d7517eb-b32d-471d-93e3-4bbee0b7f06a", "name": "POS?", "type": "n8n-nodes-base.if", "position": [1920, 1000], "parameters": {"conditions": {"string": [{"value1": "={{ $json.order.source_name }}", "value2": "pos"}]}}, "typeVersion": 1}, {"id": "b17c61c4-ce32-4b0f-88ac-aad47e5d785d", "name": "DELETE Sales Invoice", "type": "n8n-nodes-base.httpRequest", "onError": "continueRegularOutput", "position": [3720, 440], "parameters": {"url": "=https://api.businesscentral.dynamics.com/v2.0/{{ $('D365 BC Environment Settings').item.json.tenantId }}/Production/api/v2.0/companies({{ $('D365 BC Environment Settings').item.json.companyId }})/salesOrders({{ $json.so_id }})", "method": "DELETE", "options": {"batching": {"batch": {"batchSize": 1}}, "response": {"response": {}}}, "sendBody": true, "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{}]}, "genericAuthType": "oAuth2Api", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "credentials": {"oAuth2Api": {"id": "s8gGHYzOwlhE9yot", "name": "PROD_businessCentral_integro"}}, "typeVersion": 4.1}], "active": false, "pinData": {}, "settings": {"callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1", "executionTimeout": 300, "saveManualExecutions": false, "saveExecutionProgress": true, "saveDataSuccessExecution": "none"}, "versionId": "82aaad0b-396d-4d9a-9550-731340124a18", "connections": {"POS?": {"main": [[{"node": "Lookup Sales Invoice", "type": "main", "index": 0}], [{"node": "Lookup Sales Order", "type": "main", "index": 0}]]}, "Filter": {"main": [[{"node": "GetFufillmentOrders", "type": "main", "index": 0}]]}, "New SO?": {"main": [[{"node": "Sales Order Mapping", "type": "main", "index": 0}]]}, "Shopify": {"main": [[{"node": "Filter", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Create Order Lines", "type": "main", "index": 0}]]}, "New Invoice?": {"main": [[{"node": "Sales Invoice", "type": "main", "index": 0}]]}, "SelectFields": {"main": [[{"node": "orderPreprocessing", "type": "main", "index": 0}]]}, "Set Lines SO": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "New Customer?": {"main": [[{"node": "Create Customer", "type": "main", "index": 0}], [{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Sales Invoice": {"main": [[{"node": "Create Sales Invoice", "type": "main", "index": 0}]]}, "Create Customer": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Set Business Central Customer Id", "type": "main", "index": 0}], [{"node": "Lookup Customers", "type": "main", "index": 0}]]}, "Lookup Customers": {"main": [[{"node": "New Customer?", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "D365 BC Environment Settings", "type": "main", "index": 0}]]}, "Set Lines Invoice": {"main": [[{"node": "Split Out Invoice", "type": "main", "index": 0}]]}, "Split Out Invoice": {"main": [[{"node": "Create Invoice Lines", "type": "main", "index": 0}]]}, "Create Order Lines": {"main": [[{"node": "End", "type": "main", "index": 0}], [{"node": "DELETE Sales Order", "type": "main", "index": 0}]]}, "Create Sales Order": {"main": [[{"node": "Set Lines SO", "type": "main", "index": 0}]]}, "Lookup Sales Order": {"main": [[{"node": "New SO?", "type": "main", "index": 0}]]}, "orderPreprocessing": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "GetFufillmentOrders": {"main": [[{"node": "SelectFields", "type": "main", "index": 0}]]}, "Sales Order Mapping": {"main": [[{"node": "Create Sales Order", "type": "main", "index": 0}]]}, "Create Invoice Lines": {"main": [[{"node": "End1", "type": "main", "index": 0}], [{"node": "DELETE Sales Invoice", "type": "main", "index": 0}]]}, "Create Sales Invoice": {"main": [[{"node": "Set Lines Invoice", "type": "main", "index": 0}]]}, "Lookup Sales Invoice": {"main": [[{"node": "New Invoice?", "type": "main", "index": 0}]]}, "D365 BC Environment Settings": {"main": [[{"node": "Shopify", "type": "main", "index": 0}]]}, "Set Business Central Customer Id": {"main": [[{"node": "POS?", "type": "main", "index": 0}]]}}}