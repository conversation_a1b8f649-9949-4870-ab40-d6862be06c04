{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [20, 720], "parameters": {}, "typeVersion": 1}, {"name": "Customer Datastore", "type": "n8n-nodes-base.n8nTrainingCustomerDatastore", "position": [220, 720], "parameters": {"operation": "getAllPeople"}, "typeVersion": 1}, {"name": "Note", "type": "n8n-nodes-base.stickyNote", "position": [500, 600], "parameters": {"width": 520, "height": 280, "content": "## 2. If with And/Or conditions\nSet the **Combine** field to: \n`ALL` for `AND` condition\n`ANY` for `OR` condition"}, "typeVersion": 1}, {"name": "Note1", "type": "n8n-nodes-base.stickyNote", "position": [500, 920], "parameters": {"width": 520, "height": 360, "content": "## 3. Multiple branches\nWe use the `Switch` when there more than 2 possible outcomes to the filtering. We do that by specifying the condition under **Routing rules** inside the node.\n\nIn this example we send all **US-based** customers data to route 0, **customers from CO** to route 1, **customers from the UK** to route 2, and all the rest to route 3 as a fallback"}, "typeVersion": 1}, {"name": "Note2", "type": "n8n-nodes-base.stickyNote", "position": [500, 300], "parameters": {"width": 520, "height": 260, "content": "## 1. Single condition If\nFilter out data that you don't want or send data to different branches"}, "typeVersion": 1}, {"name": "Note3", "type": "n8n-nodes-base.stickyNote", "position": [-520, 660], "parameters": {"width": 480, "height": 240, "content": "## The `If` and the `Switch` nodes are the key nodes to set conditional logic for filtering and routing data\n\n\n### Click `Execute Workflow` button and double click on the nodes to see the input and output items when you click on each node."}, "typeVersion": 1}, {"name": "Country equals US", "type": "n8n-nodes-base.if", "position": [540, 420], "parameters": {"conditions": {"string": [{"value1": "={{$json[\"country\"]}}", "value2": "US"}]}}, "typeVersion": 1}, {"name": "Country is empty or Name contains 'Max'", "type": "n8n-nodes-base.if", "position": [540, 720], "parameters": {"conditions": {"string": [{"value1": "={{$json[\"country\"]}}", "operation": "isEmpty"}, {"value1": "={{$json[\"name\"]}}", "value2": "Max", "operation": "contains"}]}, "combineOperation": "any"}, "typeVersion": 1}, {"name": "Country based branching", "type": "n8n-nodes-base.switch", "position": [540, 1120], "parameters": {"rules": {"rules": [{"value2": "US"}, {"output": 1, "value2": "CO"}, {"output": 2, "value2": "UK"}]}, "value1": "={{$json[\"country\"]}}", "dataType": "string", "fallbackOutput": 3}, "typeVersion": 1}], "connections": {"Customer Datastore": {"main": [[{"node": "Country is empty or Name contains 'Max'", "type": "main", "index": 0}, {"node": "Country based branching", "type": "main", "index": 0}, {"node": "Country equals US", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Customer Datastore", "type": "main", "index": 0}]]}}}