{"meta": {"instanceId": "8eadf351d49a11e77d3a57adf374670f06c5294af8b1b7c86a1123340397e728"}, "nodes": [{"id": "e033bb47-6d34-487b-9cb4-952d002f387e", "name": "Google Sheets Trigger", "type": "n8n-nodes-base.googleSheetsTrigger", "position": [-320, 540], "parameters": {"options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1rzuojNGTaBvaUEON6cakQRDva3ueGg5kNu9v12aaSP4/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "url", "value": "https://docs.google.com/spreadsheets/d/1rzuojNGTaBvaUEON6cakQRDva3ueGg5kNu9v12aaSP4/edit#gid=0"}}, "credentials": {"googleSheetsTriggerOAuth2Api": {"id": "o5WqBpa5EJcKTpKq", "name": "5@gmail"}}, "typeVersion": 1}, {"id": "1b6af5fd-90ee-44e7-8afe-cb3c844491f7", "name": "Remove Duplicates", "type": "n8n-nodes-base.removeDuplicates", "position": [120, 520], "parameters": {"compare": "<PERSON><PERSON><PERSON>s", "options": {}, "fieldsToCompare": "Email"}, "typeVersion": 1}, {"id": "a6c952c3-bc17-44cf-b661-898068914480", "name": "Verify your emails", "type": "n8n-nodes-base.httpRequest", "onError": "continueRegularOutput", "position": [280, 520], "parameters": {"url": "https://email.effibotics.com/api", "method": "POST", "options": {}, "sendBody": true, "contentType": "form-urlencoded", "sendHeaders": true, "bodyParameters": {"parameters": [{"name": "email", "value": "={{ $json.Email }}"}]}, "headerParameters": {"parameters": [{"name": "api_key", "value": "9Q6H0QETRF=GS1"}]}}, "retryOnFail": true, "typeVersion": 4.1}, {"id": "b5887e19-e5c2-4896-bbda-1835a51e1e1b", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-400, 440], "parameters": {"color": 4, "width": 1083.1212624694333, "height": 364.82606941347825, "content": "## Check email deliverability "}, "typeVersion": 1}, {"id": "c350ff47-d30e-4aa1-8470-9808525111b7", "name": "Update data to google sheets", "type": "n8n-nodes-base.googleSheets", "position": [500, 520], "parameters": {"columns": {"value": {}, "schema": [{"id": "Email", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Email", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": ["Email"]}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1rzuojNGTaBvaUEON6cakQRDva3ueGg5kNu9v12aaSP4/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1rzuojNGTaBvaUEON6cakQRDva3ueGg5kNu9v12aaSP4", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1rzuojNGTaBvaUEON6cakQRDva3ueGg5kNu9v12aaSP4/edit?usp=drivesdk", "cachedResultName": "n8n Template-Email Validation"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "uv83GKvueqitpnrd", "name": "5@gmail"}}, "typeVersion": 4.3}, {"id": "3bbdd0f1-66b6-4774-bb93-44336b827d3e", "name": "If Email Exists", "type": "n8n-nodes-base.if", "position": [-100, 540], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "84aff430-6fe2-4c39-940a-178d2dcd1d09", "operator": {"type": "string", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json.Status }}", "rightValue": ""}]}}, "typeVersion": 2}], "pinData": {}, "connections": {"If Email Exists": {"main": [[{"node": "Remove Duplicates", "type": "main", "index": 0}]]}, "Remove Duplicates": {"main": [[{"node": "Verify your emails", "type": "main", "index": 0}]]}, "Verify your emails": {"main": [[{"node": "Update data to google sheets", "type": "main", "index": 0}]]}, "Google Sheets Trigger": {"main": [[{"node": "If Email Exists", "type": "main", "index": 0}]]}}}