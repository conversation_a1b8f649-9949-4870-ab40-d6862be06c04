{"meta": {"instanceId": "21754f977ce20b07e6fe64be3fbc663f6e6f730423d6e46c6cd2bf5b5e70a383"}, "nodes": [{"id": "0c8b3a80-00e1-4d69-aac9-df41a464914a", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-246.5549467302767, -396.60463598587717], "parameters": {"width": 2260.4312974923314, "height": 1739.059401992624, "content": ""}, "typeVersion": 1}, {"id": "74ee38b2-2d8a-40bf-8dad-e20125f000f7", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-220, -340], "parameters": {"color": 5, "width": 644.910132006371, "height": 655.8676264589326, "content": "### Project Benefit 🎧🌟\n\nThe goal of this awesome project is to turn those \"CATEGORY_PROMOTIONS\" emails into a super cool audio podcast! 🎙️ This way, users can kick back and enjoy the promotional content without having to squint at their screens. By listening instead of reading, users can soak in the info in a fun and easy way.\n\nThis project rocks a workflow using n8n to automate tasks like a boss. Each node in the workflow plays its part in a smooth operation. Check out the main players:\n\n1. **Gmail trigger1 Node**: Kicks off the action every minute for those \"CATEGORY_PROMOTIONS\" emails.\n   \n2. **Get message content1 Node**: Grabs the email content for some magic.\n      \n3. **Summarization Chain3 Node**: Whips up some sweet summaries using fancy chunking methods.\n   \n4. **Delete the unnecessary items Node**: Clears out the clutter from the email content.\n   \n5. **Text to Free TTS Node**: Turns the summary into speech using Free TTS magic.\n   \n6. **Convert from base64 to File Node**: Changes the audio into a file format.\n   \n7. **Merge Text with Audio Node**: Mixes the text and audio together for a cool combo.\n   \n8. **Aggregate in same cell Node**: Puts all the data together for more awesomeness.\n   \n10. **Send Message to Telegram Node**: Sends the final audio message with a caption to a special Telegram chat ID.\n\nThis workflow is like a well-oiled machine, with each step flowing seamlessly into the next. By automating these tasks, this project aims to make communication a breeze and bring joy to all involved! 🌈✨🚀\n"}, "typeVersion": 1}, {"id": "07a4dc07-0109-464e-a661-d5a4bb7b4a1c", "name": "Get message content1", "type": "n8n-nodes-base.gmail", "position": [640, 460], "parameters": {"simple": false, "options": {}, "messageId": "={{ $json.id }}", "operation": "get"}, "credentials": {"gmailOAuth2": {"id": "UJx4Tiq8WRtxWEIP", "name": "<PERSON><PERSON>"}}, "typeVersion": 2.1}, {"id": "283dcd8b-80a8-4e49-aba1-fabec333def3", "name": "OpenAI Chat Model3", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1120, 640], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "6u6TSayQDxci71Wb", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "********-5ee8-4f15-bc4e-26a987b79505", "name": "Delete the unnecessary items", "type": "n8n-nodes-base.code", "position": [880, 460], "parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nfor (const item of $input.all()) {\n delete item.json.threadId;\n delete item.json.labelIds;\n delete item.json.sizeEstimate;\n delete item.json.headers;\n delete item.json.html;\n delete item.json.to;\n delete item.json.cc;\n delete item.json.replyTo;\n delete item.json.messageId;\n delete item.json.id;\n delete item.json.textAsHtml;\n delete item.json.date;\n\n}\n\nreturn $input.all();"}, "typeVersion": 2}, {"id": "187704ba-ddc1-447e-99f6-8335b039dca3", "name": "Aggregate in same cell", "type": "n8n-nodes-base.aggregate", "position": [1400, 660], "parameters": {"options": {"includeBinaries": true}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}, {"id": "a8cba2a0-e751-4dc4-8cc1-9b91c587b1bc", "name": "Gmail trigger", "type": "n8n-nodes-base.gmailTrigger", "position": [440, 460], "parameters": {"simple": false, "filters": {"labelIds": ["CATEGORY_PROMOTIONS"]}, "options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}}, "credentials": {"gmailOAuth2": {"id": "UJx4Tiq8WRtxWEIP", "name": "<PERSON><PERSON>"}}, "typeVersion": 1}, {"id": "7d170a4c-601e-49da-a834-2a40f992feff", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1380, -340], "parameters": {"color": 5, "width": 478.42665735513924, "height": 651.7534899914576, "content": "### This API allows automatic text-to-speech generation.\nYou can utilize this API by sending a POST request to the specified link and including JSON data containing the text you want to convert to speech, along with selecting your preferred voice.\n\nWhen using this API, make sure to include the `Content-Type` header with the value `application/json` to ensure proper interpretation of the request data.\n\nThe API offers a user-friendly interface where you can simply submit the desired text and choose the appropriate voice, then receive an audio file containing the generated speech.\n\nUsing this API can be beneficial for quickly generating audio clips for texts in an efficient manner.\n\nYou can access this API at [https://tiktok-tts.weilnet.workers.dev/api/generation](https://tiktok-tts.weilnet.workers.dev/api/generation) or keep it as is without changing anything as provided on [https://tiktokvoicegenerator.com/](https://tiktokvoicegenerator.com/). \n"}, "typeVersion": 1}, {"id": "f0809138-4bde-4132-97b2-0810b920ed7a", "name": "Convert from base64 to File", "type": "n8n-nodes-base.convertToFile", "position": [1660, 140], "parameters": {"options": {}, "operation": "toBinary", "sourceProperty": "data"}, "typeVersion": 1.1}, {"id": "2efdd685-57fe-4f5c-b295-183dddfeb0d6", "name": "Merge Text with Audio", "type": "n8n-nodes-base.merge", "position": [1720, 440], "parameters": {}, "typeVersion": 2.1}, {"id": "c59a00fd-c7c7-4dc5-91d1-492bd7715731", "name": "Send Message to Telegram", "type": "n8n-nodes-base.telegram", "position": [1720, 660], "parameters": {"chatId": "53739339", "operation": "sendAudio", "binaryData": true, "additionalFields": {"caption": "={{ $json.data[1].response.text }}", "fileName": "New Message on Gmail"}, "binaryPropertyName": "=data"}, "credentials": {"telegramApi": {"id": "inUwZEIEWHK1poKe", "name": "aqsati services"}}, "typeVersion": 1.1}, {"id": "3f3a1209-9787-41c3-af10-3f3e44a89c9b", "name": "Summarization Chain3", "type": "@n8n/n8n-nodes-langchain.chainSummarization", "position": [1120, 460], "parameters": {"options": {"summarizationMethodAndPrompts": {"values": {"prompt": "Craft a concise newsletter using the given content. Include emojis, avoid starting with the subject word, summarize linked articles briefly, and ensure it's under 247 characters for easy TTS readability, and after that chick if it's very short to pass it:\n\n\n\n\"{text}\"\n\n\n", "combineMapPrompt": "Craft a concise newsletter using the given content. Include emojis, avoid starting with the subject word, summarize linked articles briefly, and ensure it's under 247 characters for easy TTS readability, and after that chick if it's very short to pass it:\n\n\n\"{text}\"\n\n\n"}}}, "chunkingMode": "advanced"}, "typeVersion": 2}, {"id": "f1e063a5-0e0e-4f8e-b8bc-e940db622843", "name": "Text to  TTS", "type": "n8n-nodes-base.httpRequest", "onError": "continueErrorOutput", "position": [1460, 140], "parameters": {"url": "https://tiktok-tts.weilnet.workers.dev/api/generation", "method": "POST", "options": {"allowUnauthorizedCerts": true}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "={{ $json.response.text }}"}, {"name": "voice", "value": "en_us_001"}]}}, "retryOnFail": false, "typeVersion": 4.1, "alwaysOutputData": false}, {"id": "c6f9e191-31a0-4ec7-aa11-8f615074b884", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-220, 340], "parameters": {"color": 5, "width": 821.8034694793512, "height": 987.2767141363915, "content": "### The Gmail Trigger:\nThe Gmail Trigger node in your N8N workflow is set to poll for new emails every minute and is configured to filter emails with the label \"CATEGORY_PROMOTIONS\" before triggering the workflow.\n\n### Steps to Use Filters Inside the Gmail Trigger Node:\n1. **Add Gmail Trigger Node**:\n   - Drag and drop a Gmail Trigger node onto your workflow canvas.\n\n\\```javascript\n// Add Gmail Trigger node\n\\```\n\n2. **Configure Gmail Trigger Node**:\n   - In the node configuration:\n     - Set \"Poll Times\" to \"Every Minute\" to check for new emails at regular intervals.\n     - Enable the \"Simple\" toggle if you want to simplify the node interface.\n     - Under \"Filters\", specify the label IDs you want to filter by. In this case, it's set to \"CATEGORY_PROMOTIONS\".\n     - Adjust any additional options as needed.\n\n\\```javascript\n// Configure Gmail Trigger node\npollTimes: {\n  item: [\n    {\n      mode: \"everyMinute\"\n    }\n  ]\n},\nsimple: false,\nfilters: {\n  labelIds: [\n    \"CATEGORY_PROMOTIONS\"\n  ]\n},\noptions: {}\n\\```\n\n3. **Provide Credentials**:\n   - Ensure that you have set up the necessary Gmail credentials. In this case, it's using Gmail OAuth2 with the ID \"UJx4Tiq8WRtxWEIP\" and the name \"Gmail Omar\".\n\n4. **Save and Execute**:\n   - Save your workflow and execute it to start monitoring your Gmail account for new emails with the specified label filter.\n\nBy following these steps, your workflow will effectively trigger based on new emails that match the \"CATEGORY_PROMOTIONS\" label in your Gmail account.\n"}, "typeVersion": 1}], "pinData": {}, "connections": {"Text to  TTS": {"main": [[{"node": "Convert from base64 to File", "type": "main", "index": 0}], [{"node": "Summarization Chain3", "type": "main", "index": 0}]]}, "Gmail trigger": {"main": [[{"node": "Get message content1", "type": "main", "index": 0}]]}, "OpenAI Chat Model3": {"ai_languageModel": [[{"node": "Summarization Chain3", "type": "ai_languageModel", "index": 0}]]}, "Get message content1": {"main": [[{"node": "Delete the unnecessary items", "type": "main", "index": 0}]]}, "Summarization Chain3": {"main": [[{"node": "Merge Text with Audio", "type": "main", "index": 1}, {"node": "Text to  TTS", "type": "main", "index": 0}]]}, "Merge Text with Audio": {"main": [[{"node": "Aggregate in same cell", "type": "main", "index": 0}]]}, "Aggregate in same cell": {"main": [[{"node": "Send Message to Telegram", "type": "main", "index": 0}]]}, "Convert from base64 to File": {"main": [[{"node": "Merge Text with Audio", "type": "main", "index": 0}]]}, "Delete the unnecessary items": {"main": [[{"node": "Summarization Chain3", "type": "main", "index": 0}]]}}}