{"id": "HbjZ9cBPgDdnIRjG", "meta": {"instanceId": "558d88703fb65b2d0e44613bc35916258b0f0bf983c5d4730c00c424b77ca36a", "templateCredsSetupCompleted": true}, "name": "MiniBear Webhook", "tags": [{"id": "0xpEHcJjNRRRMtEj", "name": "lin", "createdAt": "2025-03-12T05:06:24.112Z", "updatedAt": "2025-03-12T05:06:24.112Z"}, {"id": "IhTa6egt1w8uqn9Z", "name": "_ACTIVE", "createdAt": "2025-03-12T05:07:05.060Z", "updatedAt": "2025-03-12T05:07:05.060Z"}, {"id": "Q0IWVCdrzoxXDC7z", "name": "error_linlinmhee_line", "createdAt": "2025-03-12T06:37:16.225Z", "updatedAt": "2025-03-12T06:37:16.225Z"}, {"id": "U1ozjO3iXQZWUyfG", "name": "_Blueprint", "createdAt": "2025-03-12T06:24:40.268Z", "updatedAt": "2025-03-12T06:24:40.268Z"}], "nodes": [{"id": "b1f42cbd-952e-4704-9233-788891e1894d", "name": "Line Webhook", "type": "n8n-nodes-base.webhook", "position": [-260, -20], "webhookId": "4ef1a53c-a1ec-4a63-a7a5-469423502333", "parameters": {"path": "minibear", "options": {}, "httpMethod": "POST"}, "typeVersion": 2}, {"id": "ae4a46d6-0f34-484b-8be5-dbc07d5de92e", "name": "Line Loading Animation", "type": "n8n-nodes-base.httpRequest", "position": [120, -20], "parameters": {"url": "https://api.line.me/v2/bot/chat/loading/start", "method": "POST", "options": {}, "jsonBody": "={\n    \"chatId\": \"{{ $json.body.events[0].source.userId }}\",\n    \"loadingSeconds\": 60\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "lKd3b2nc8uNJ148Z", "name": "Line @271dudsw MiniBear"}}, "typeVersion": 4.2}, {"id": "802eb4b2-ed1c-4cbc-9cf9-9bd8fec74b82", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-380, -100], "parameters": {"color": 4, "width": 360, "height": 560, "content": "**Webhook from Line**\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nYou need to set-up this webhook at Line Manager or Line Developer Console\n\nYou'll need to copy Webhook URL from this node to put in Line Console\n\nAlso, don't forget to remove 'test' part when going for production\n\nhttps://developers.line.biz/en/docs/messaging-api/receiving-messages/\n"}, "typeVersion": 1}, {"id": "965612b6-bd04-44e9-9b95-d777f92e9acf", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [0, -100], "parameters": {"color": 4, "width": 360, "height": 560, "content": "**Line Loading Animation**\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nThis node is to only give ... loading animation back in Line.\n\nIt seems stupid but it actually tells user that the workflow is running and you are not left waiting without hope\n\nTo authorize, you can fill in the Line Token in the node here, or you can you header authorization (shown at the 'reply message' node)\n\nhttps://developers.line.biz/en/docs/messaging-api/use-loading-indicator/"}, "typeVersion": 1}, {"id": "92953054-43a6-44a3-8069-6147cbb837c3", "name": "Switch", "type": "n8n-nodes-base.switch", "position": [500, 80], "parameters": {"rules": {"values": [{"outputKey": "Task", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "6f9aef97-cf2f-4f8e-bbc5-c17069a24c57", "operator": {"type": "string", "operation": "startsWith"}, "leftValue": "={{ $('Line Webhook').item.json.body.events[0].message.text }}", "rightValue": "T "}]}, "renameOutput": true}, {"outputKey": "text", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "9f8075cf-8f3f-419f-ae0a-833ee29fc063", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $('Line Webhook').item.json.body.events[0].message.type }}", "rightValue": "text"}]}, "renameOutput": true}, {"outputKey": "img", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "b7770f5b-dfb5-4b7a-8dc1-4404337dbfde", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Line Webhook').item.json.body.events[0].message.type }}", "rightValue": "image"}]}, "renameOutput": true}, {"outputKey": "audio", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "9faa9dd4-32ce-4287-b7e5-885a42a62e32", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Line Webhook').item.json.body.events[0].message.type }}", "rightValue": "audio"}]}, "renameOutput": true}, {"outputKey": "else", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "f4dbfa6a-a7f8-4c32-a94d-da384f37c0d1", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": true, "rightValue": ""}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "ae9e08a3-0106-4e49-85b3-84eb9696673c", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [380, -100], "parameters": {"color": 5, "width": 360, "height": 560, "content": "**Router for Tasks (Text started with 'T'), other texts, images and others**"}, "typeVersion": 1}, {"id": "933b7da4-95fd-4bb2-ac46-3eac62d0dcaa", "name": "Get Image", "type": "n8n-nodes-base.httpRequest", "position": [900, 80], "parameters": {"url": "=https://api-data.line.me/v2/bot/message/{{ $('Line Webhook').item.json.body.events[0].message.id }}/content", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "lKd3b2nc8uNJ148Z", "name": "Line @271dudsw MiniBear"}}, "typeVersion": 4.2}, {"id": "d1160e8d-b84a-4e76-b5cc-f8c960a6070b", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [820, 460], "parameters": {"color": 4, "width": 360, "height": 480, "content": "**Line Reply**\nTo reply that message is not supported\n\n\n\n\n\n\n\n\n\n\n"}, "typeVersion": 1}, {"id": "b169657d-5348-4662-bdcf-6617416ec9f7", "name": "Line Reply (image)", "type": "n8n-nodes-base.httpRequest", "position": [3140, 200], "parameters": {"url": "https://api.line.me/v2/bot/message/reply", "method": "POST", "options": {}, "jsonBody": "={\n  \"replyToken\": \"{{ $('Line Webhook').item.json.body.events[0].replyToken }}\",\n  \"messages\": [\n    {\n      \"type\": \"text\",\n      \"text\": \"[ Message Saved in Zac&Lin > Notes ]\"\n    }\n  ]\n} ", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "lKd3b2nc8uNJ148Z", "name": "Line @271dudsw MiniBear"}}, "typeVersion": 4.2}, {"id": "b0f63685-e8df-484e-85ea-bedabd80b61e", "name": "Line Reply (Text)", "type": "n8n-nodes-base.httpRequest", "position": [1300, -340], "parameters": {"url": "https://api.line.me/v2/bot/message/reply", "method": "POST", "options": {}, "jsonBody": "={\n  \"replyToken\": \"{{ $('Line Webhook').item.json.body.events[0].replyToken }}\",\n  \"messages\": [\n    {\n      \"type\": \"text\",\n      \"text\": \"[ Message Saved in Zac&Lin > Notes ]\" \n    }\n  ]\n} ", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "lKd3b2nc8uNJ148Z", "name": "Line @271dudsw MiniBear"}}, "typeVersion": 4.2}, {"id": "b2ee3bb5-7cdf-46de-868e-f6b81a2e0ec0", "name": "Line Reply (Not Supported 2)", "type": "n8n-nodes-base.httpRequest", "position": [940, 740], "parameters": {"url": "https://api.line.me/v2/bot/message/reply", "method": "POST", "options": {}, "jsonBody": "={\n  \"replyToken\": \"{{ $('Line Webhook').item.json.body.events[0].replyToken }}\",\n  \"messages\": [\n    {\n      \"type\": \"text\",\n      \"text\": \"Please try again. Message type is not supported\"\n    }\n  ]\n} ", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "3IEOzxKOUr6OEXyU", "name": "Line @405jtfqs LazyChinese"}}, "typeVersion": 4.2}, {"id": "04f298c3-e952-4d85-aba7-0971d2f6a8b0", "name": "Line Reply (Not Supported 1)", "type": "n8n-nodes-base.httpRequest", "position": [940, 540], "parameters": {"url": "https://api.line.me/v2/bot/message/reply", "method": "POST", "options": {}, "jsonBody": "={\n  \"replyToken\": \"{{ $('Line Webhook').item.json.body.events[0].replyToken }}\",\n  \"messages\": [\n    {\n      \"type\": \"text\",\n      \"text\": \"Please try again. Message type is not supported\"\n    }\n  ]\n} ", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "3IEOzxKOUr6OEXyU", "name": "Line @405jtfqs LazyChinese"}}, "typeVersion": 4.2}, {"id": "1d6d7c55-59ba-48c4-a877-6f260ede7bf5", "name": "OpenRouter <PERSON>", "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "position": [1060, 200], "parameters": {"model": "openai/gpt-4o", "options": {}}, "credentials": {"openRouterApi": {"id": "iQS3GMHjRv36CWYD", "name": "n8n Lin"}}, "typeVersion": 1}, {"id": "dae07428-733a-4976-9553-cba198736403", "name": "Image Router", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1040, 80], "parameters": {"text": "You'll identify the image\n01 Namecard\n02 Text on screen or handwritten note\n03 Others\n\nYou'll answer with only 01 02 or 03", "options": {"passthroughBinaryImages": true}, "promptType": "define"}, "typeVersion": 1.8, "alwaysOutputData": true}, {"id": "1810b10b-d326-4626-836c-4fb706deff20", "name": "Microsoft Teams", "type": "n8n-nodes-base.microsoftTeams", "position": [1020, -340], "webhookId": "3a9c75de-5207-4e9b-a558-6c2fd622fb5f", "parameters": {"teamId": {"__rl": true, "mode": "list", "value": "ebfd67d4-df6b-4ea2-9faf-81ec059170ad", "cachedResultName": "Zac&Lin"}, "message": "={{ $('Line Webhook').item.json.body.events[0].message.text.replace('\\n\\n', '<br><br>').replace('\\n', '<br>') }}", "options": {}, "resource": "channelMessage", "channelId": {"__rl": true, "mode": "list", "value": "19:c2966307089a4f4b98ca06b5f160999a@thread.tacv2", "cachedResultUrl": "https://teams.microsoft.com/l/channel/19%3Ac2966307089a4f4b98ca06b5f160999a%40thread.tacv2/Notes?groupId=ebfd67d4-df6b-4ea2-9faf-81ec059170ad&tenantId=77e73351-d19d-4855-9380-82ca9b459c87&allowXTenantAccess=True&ngc=True", "cachedResultName": "Notes"}, "contentType": "html"}, "credentials": {"microsoftTeamsOAuth2Api": {"id": "3oENQ6chN2T1DR2x", "name": "Microsoft Teams account"}}, "typeVersion": 2}, {"id": "b98955e9-1c4c-473f-ac51-a8d73747ae63", "name": "Microsoft To Do", "type": "n8n-nodes-base.microsoftToDo", "position": [1020, -600], "parameters": {"title": "={{ $('Line Webhook').item.json.body.events[0].message.text.replace('T ','') }}", "operation": "create", "taskListId": "AQMkAGE1NDhhM2UxLTA3ZTQtNGIyYS1iOWFjLTlkNDAyYzkzNjE2YQAuAAADCPe-x7aF5Eqh4_vJsot6MAEAG9nUtifnkkavLabXdl_kugAAAgESAAAA", "additionalFields": {}}, "credentials": {"microsoftToDoOAuth2Api": {"id": "Pag1nTvXIzYpG5V1", "name": "Microsoft To Do account"}}, "typeVersion": 1}, {"id": "********-34c6-4580-8039-fca99c9467ca", "name": "Line Reply (Text)1", "type": "n8n-nodes-base.httpRequest", "position": [1300, -600], "parameters": {"url": "https://api.line.me/v2/bot/message/reply", "method": "POST", "options": {}, "jsonBody": "={\n  \"replyToken\": \"{{ $('Line Webhook').item.json.body.events[0].replyToken }}\",\n  \"messages\": [\n    {\n      \"type\": \"text\",\n      \"text\": \"[ Task : {{ $('Line Webhook').item.json.body.events[0].message.text.replace('T ','') }} created successfully in Private Task ]\" \n    }\n  ]\n} ", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "lKd3b2nc8uNJ148Z", "name": "Line @271dudsw MiniBear"}}, "typeVersion": 4.2}, {"id": "525f0a8b-0c03-4182-ab2b-ff97ba6ad50d", "name": "If namecard", "type": "n8n-nodes-base.if", "position": [1480, 80], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "85dc209c-a217-46a7-8289-b3e98c128d05", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.output }}", "rightValue": "01"}]}}, "typeVersion": 2.2}, {"id": "113ab6ea-d66d-4b8d-ae81-eee73439c90e", "name": "Other Images", "type": "@n8n/n8n-nodes-langchain.agent", "position": [2440, 200], "parameters": {"text": "=If the image is handwritten notes or text on screen in thai or english, you'll extract the text.\n\nEl<PERSON>, you'll describe the image", "options": {"passthroughBinaryImages": true}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "596a643e-757a-4b83-878b-f5a0f1c42886", "name": "Microsoft Teams1", "type": "n8n-nodes-base.microsoftTeams", "position": [2880, 200], "webhookId": "3a9c75de-5207-4e9b-a558-6c2fd622fb5f", "parameters": {"teamId": {"__rl": true, "mode": "list", "value": "ebfd67d4-df6b-4ea2-9faf-81ec059170ad", "cachedResultName": "Zac&Lin"}, "message": "={{ $json.output.replace('\\n\\n', '<br><br>').replace('\\n', '<br>') }}\n<br><br>\n<img src=\"{{ $('Get Image2').item.json['@microsoft.graph.downloadUrl'] }}\">\n</img>", "options": {}, "resource": "channelMessage", "channelId": {"__rl": true, "mode": "list", "value": "19:c2966307089a4f4b98ca06b5f160999a@thread.tacv2", "cachedResultUrl": "https://teams.microsoft.com/l/channel/19%3Ac2966307089a4f4b98ca06b5f160999a%40thread.tacv2/Notes?groupId=ebfd67d4-df6b-4ea2-9faf-81ec059170ad&tenantId=77e73351-d19d-4855-9380-82ca9b459c87&allowXTenantAccess=True&ngc=True", "cachedResultName": "Notes"}, "contentType": "html"}, "credentials": {"microsoftTeamsOAuth2Api": {"id": "3oENQ6chN2T1DR2x", "name": "Microsoft Teams account"}}, "typeVersion": 2}, {"id": "be5637d9-50a7-4508-b6f9-d32ac37bb2c2", "name": "OpenRouter Chat Model2", "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "position": [2460, 320], "parameters": {"model": "openai/gpt-4o", "options": {}}, "credentials": {"openRouterApi": {"id": "iQS3GMHjRv36CWYD", "name": "n8n Lin"}}, "typeVersion": 1}, {"id": "a7114805-80bf-428a-a717-b35cb6eb3312", "name": "Microsoft OneDrive", "type": "n8n-nodes-base.microsoftOneDrive", "position": [1820, 200], "parameters": {"fileName": "testtest.jpg", "parentId": "01I7MG5Y2G7ELINW2YLJBLHHF5KDBNJDPF", "binaryData": true}, "credentials": {"microsoftOneDriveOAuth2Api": {"id": "pM363KMLOo6btGCp", "name": "Microsoft Drive account"}}, "typeVersion": 1}, {"id": "7a6f14a2-938f-40f8-a294-564fb1185de3", "name": "Microsoft OneDrive1", "type": "n8n-nodes-base.microsoftOneDrive", "position": [2000, 200], "parameters": {"itemId": "={{ $json.id }}", "newName": "={{ $('Line Webhook').item.json.body.events[0].message.id }}.jpg", "operation": "rename"}, "credentials": {"microsoftOneDriveOAuth2Api": {"id": "pM363KMLOo6btGCp", "name": "Microsoft Drive account"}}, "typeVersion": 1}, {"id": "864fe7c5-f8f9-429e-94b7-2b760af09cd4", "name": "Get Image2", "type": "n8n-nodes-base.httpRequest", "position": [2260, 200], "parameters": {"url": "=https://api-data.line.me/v2/bot/message/{{ $('Line Webhook').item.json.body.events[0].message.id }}/content", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "lKd3b2nc8uNJ148Z", "name": "Line @271dudsw MiniBear"}}, "typeVersion": 4.2}, {"id": "f1108c52-9e9c-4611-9a2a-40f28871632a", "name": "Microsoft To Do1", "type": "n8n-nodes-base.microsoftToDo", "position": [2400, -260], "parameters": {"title": "=Follow-up Namecard {{ $json.output.Email }}", "operation": "create", "taskListId": "AQMkAGE1NDhhM2UxLTA3ZTQtNGIyYS1iOWFjLTlkNDAyYzkzNjE2YQAuAAADCPe-x7aF5Eqh4_vJsot6MAEAG9nUtifnkkavLabXdl_kugAAAgESAAAA", "additionalFields": {}}, "credentials": {"microsoftToDoOAuth2Api": {"id": "Pag1nTvXIzYpG5V1", "name": "Microsoft To Do account"}}, "typeVersion": 1}, {"id": "2afe30ad-ef70-45e8-9e3c-546bd3cf91a8", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [2960, -260], "parameters": {"url": "https://hook.us2.make.com/46263sznm3didxdkcuqvnlfqv2fv2l7q", "method": "POST", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "MessageID", "value": "={{ $('Line Webhook').item.json.body.events[0].message.id }}"}, {"name": "Content", "value": "={{ $('NamecardExtract').item.json.output }}"}, {"name": "ReplyToken", "value": "={{ $('Line Webhook').item.json.body.events[0].replyToken }}"}]}}, "typeVersion": 4.2}, {"id": "a94bcdea-f8cc-4eb9-847a-66eb3808ed5b", "name": "OpenRouter Chat Model3", "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "position": [2000, -140], "parameters": {"model": "openai/gpt-4o", "options": {}}, "credentials": {"openRouterApi": {"id": "iQS3GMHjRv36CWYD", "name": "n8n Lin"}}, "typeVersion": 1}, {"id": "f138bdcf-1b46-4c14-b443-db2f1e3055f4", "name": "NamecardExtract", "type": "@n8n/n8n-nodes-langchain.agent", "position": [2000, -260], "parameters": {"text": "=You'll extract the data in JSON format \n\n--- \n{   \"Nickname\": \"\",   \"FirstName\": \"\",   \"LastName\": \"\",   \"CompanyFull\": \"\",   \"Department\": \"\",   \"JobTitle\": \"\",   \"Mobile\": \"\",   \"Mobile2\": \"\",   \"Email\": \"\",   \"SocialMedia\": \"\",   \"Address\": \"\",   \"Remark\": \"\",   \"NameTH\": \"\" } \n--- \nFor Nickname, you'll see if there's any short name in the namecard. For Name TH, you'll see if there's thai name on the namecard. \n", "options": {"passthroughBinaryImages": true}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.8}, {"id": "df565b73-3cfe-464e-b5d2-36f2240da218", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [2180, -140], "parameters": {"jsonSchemaExample": "{   \"Nickname\": \"\",   \"FirstName\": \"\",   \"LastName\": \"\",   \"CompanyFull\": \"\",   \"Department\": \"\",   \"JobTitle\": \"\",   \"Mobile\": \"\",   \"Mobile2\": \"\",   \"Email\": \"\",   \"SocialMedia\": \"\",   \"Address\": \"\",   \"Remark\": \"\",   \"NameTH\": \"\" } "}, "typeVersion": 1.2}, {"id": "c7b2b87f-3507-4a0f-a1d0-7ab9a131a619", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1220, -680], "parameters": {"color": 4, "width": 260, "height": 240, "content": "**Line Reply**\nTo send feedback that the task has been added\n"}, "typeVersion": 1}, {"id": "6448ffd8-4a5d-4cb7-826a-b594952c6773", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1220, -420], "parameters": {"color": 4, "width": 260, "height": 240, "content": "**Line Reply**\nTo send feedback message has been saved"}, "typeVersion": 1}, {"id": "f222d983-26a5-4806-8f86-1eb32982f558", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [940, -680], "parameters": {"color": 2, "width": 260, "height": 240, "content": "**Tasks**\nTo add in MS 'To Do' List"}, "typeVersion": 1}, {"id": "d13b3145-d9c5-41bb-a384-996fcdbbc19c", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [940, -420], "parameters": {"color": 3, "width": 260, "height": 240, "content": "**MS Teams**\nSave this message in MS Teams"}, "typeVersion": 1}, {"id": "001dd5c9-b56e-47f1-bd71-31c3b2a7810c", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [2320, -340], "parameters": {"color": 2, "width": 260, "height": 240, "content": "**Tasks**\nTo add in MS 'To Do' List to follow-up with this namecard"}, "typeVersion": 1}, {"id": "94519356-9e65-473f-bfad-f89d0aecc7ff", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [2600, -340], "parameters": {"color": 4, "width": 260, "height": 240, "content": "**Line Reply**\nTo send feedback message has been saved"}, "typeVersion": 1}, {"id": "28c490ad-5fa9-435c-8d06-d3bb21c8d454", "name": "Line Reply Namecard", "type": "n8n-nodes-base.httpRequest", "position": [2680, -260], "parameters": {"url": "https://api.line.me/v2/bot/message/reply", "method": "POST", "options": {}, "jsonBody": "={\n  \"replyToken\": \"{{ $('Line Webhook').item.json.body.events[0].replyToken }}\",\n  \"messages\": [\n    {\n      \"type\": \"text\",\n      \"text\": \"[ Namecard Extraction ] /n/n {{ $('NamecardExtract').item.json.output }}\" \n    }\n  ]\n} ", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "lKd3b2nc8uNJ148Z", "name": "Line @271dudsw MiniBear"}}, "typeVersion": 4.2}, {"id": "67dd6ba3-6382-465b-a3a9-2d438e67aaf3", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [2880, -340], "parameters": {"color": 4, "width": 260, "height": 240, "content": "**HTTP Request**\nThis is to trigger another workflow to add new rows in MS Excel 365"}, "typeVersion": 1}, {"id": "e8fb90be-1371-4bf1-a945-e6fcedacb8fb", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [3080, 120], "parameters": {"color": 4, "width": 260, "height": 240, "content": "**Line Reply**\nTo send feedback message has been saved"}, "typeVersion": 1}, {"id": "c43ec591-b882-4eba-95f3-827153af5890", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [2800, 120], "parameters": {"color": 3, "width": 260, "height": 240, "content": "**MS Teams**\nSave this message in MS Teams"}, "typeVersion": 1}, {"id": "150c2ea3-aca6-40da-879a-ee7204f00a5f", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [840, -40], "parameters": {"color": 6, "width": 520, "height": 400, "content": "**Identify Image**\nFirst we'll get the image from Line and we will use Tool Agent to query OpenRouter to identify whether this is namecard or not?"}, "typeVersion": 1}, {"id": "694d260b-1570-430b-9a5b-5b4962af9b8b", "name": "Sticky Note14", "type": "n8n-nodes-base.stickyNote", "position": [1780, -380], "parameters": {"color": 6, "width": 520, "height": 400, "content": "**Namecard Information Extraction**\nFirst we'll get the image from Line and we will use Tool Agent to query OpenRouter to extract the namecard information in the structured format"}, "typeVersion": 1}, {"id": "c437c0d5-3022-4469-ac3c-ceea9aaf1689", "name": "Get Image3", "type": "n8n-nodes-base.httpRequest", "position": [1860, -260], "parameters": {"url": "=https://api-data.line.me/v2/bot/message/{{ $('Line Webhook').item.json.body.events[0].message.id }}/content", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "lKd3b2nc8uNJ148Z", "name": "Line @271dudsw MiniBear"}}, "typeVersion": 4.2}, {"id": "91cf0e34-831a-4c56-92bb-e26319713874", "name": "Sticky Note15", "type": "n8n-nodes-base.stickyNote", "position": [1400, -40], "parameters": {"color": 5, "width": 280, "height": 400, "content": "**Router Namecard or not**"}, "typeVersion": 1}, {"id": "65261cba-e104-404a-8af6-3c2b6cf85c2a", "name": "Sticky Note16", "type": "n8n-nodes-base.stickyNote", "position": [2220, 80], "parameters": {"color": 6, "width": 520, "height": 400, "content": "**Text Extraction**\nFirst we'll get the image from Line and we will use Tool Agent to query OpenRouter to identify image content such as what is written"}, "typeVersion": 1}, {"id": "1170527e-0ac7-4a8b-8ae8-420713a046fa", "name": "Sticky Note17", "type": "n8n-nodes-base.stickyNote", "position": [1740, 80], "parameters": {"width": 440, "height": 340, "content": "**Upload to OneDrive**\nThis is to upload the file to OneDrive. Due to some bug I faced, we need to rename the file again."}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"timezone": "Asia/Bangkok", "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": "A8HoJ5iCrAMPbsLh", "executionOrder": "v1"}, "versionId": "49efe864-6f48-4c6c-853b-7e8542a7ea2f", "connections": {"Switch": {"main": [[{"node": "Microsoft To Do", "type": "main", "index": 0}], [{"node": "Microsoft Teams", "type": "main", "index": 0}], [{"node": "Get Image", "type": "main", "index": 0}], [{"node": "Line Reply (Not Supported 1)", "type": "main", "index": 0}], [{"node": "Line Reply (Not Supported 2)", "type": "main", "index": 0}]]}, "Get Image": {"main": [[{"node": "Image Router", "type": "main", "index": 0}]]}, "Get Image2": {"main": [[{"node": "Other Images", "type": "main", "index": 0}]]}, "Get Image3": {"main": [[{"node": "NamecardExtract", "type": "main", "index": 0}]]}, "If namecard": {"main": [[{"node": "Get Image3", "type": "main", "index": 0}], [{"node": "Microsoft OneDrive", "type": "main", "index": 0}]]}, "Image Router": {"main": [[{"node": "If namecard", "type": "main", "index": 0}]]}, "Line Webhook": {"main": [[{"node": "Line Loading Animation", "type": "main", "index": 0}]]}, "Other Images": {"main": [[{"node": "Microsoft Teams1", "type": "main", "index": 0}]]}, "Microsoft Teams": {"main": [[{"node": "Line Reply (Text)", "type": "main", "index": 0}]]}, "Microsoft To Do": {"main": [[{"node": "Line Reply (Text)1", "type": "main", "index": 0}]]}, "NamecardExtract": {"main": [[{"node": "Microsoft To Do1", "type": "main", "index": 0}]]}, "Microsoft Teams1": {"main": [[{"node": "Line Reply (image)", "type": "main", "index": 0}]]}, "Microsoft To Do1": {"main": [[{"node": "Line Reply Namecard", "type": "main", "index": 0}]]}, "Microsoft OneDrive": {"main": [[{"node": "Microsoft OneDrive1", "type": "main", "index": 0}]]}, "Line Reply Namecard": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "Microsoft OneDrive1": {"main": [[{"node": "Get Image2", "type": "main", "index": 0}]]}, "OpenRouter Chat Model": {"ai_languageModel": [[{"node": "Image Router", "type": "ai_languageModel", "index": 0}]]}, "Line Loading Animation": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "OpenRouter Chat Model2": {"ai_languageModel": [[{"node": "Other Images", "type": "ai_languageModel", "index": 0}]]}, "OpenRouter Chat Model3": {"ai_languageModel": [[{"node": "NamecardExtract", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "NamecardExtract", "type": "ai_outputParser", "index": 0}]]}}}