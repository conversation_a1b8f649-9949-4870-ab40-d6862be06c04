{"nodes": [{"id": "1e89a8ad-90cf-4040-b59e-1b4933ea4e69", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1740, -80], "parameters": {"color": 4, "width": 982.895112064014, "height": 248.06218763804304, "content": "MOVE CURRENT BACKUPS TO OLD FOLDER"}, "typeVersion": 1}, {"id": "f998e295-eafd-420a-9ba9-69571b4ab005", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1740, 500], "parameters": {"width": 980.8812626356395, "height": 188.38611225559103, "content": "PURGE BACKUPS OLDER THEN 30 DAYS\n"}, "typeVersion": 1}, {"id": "a94facb5-c0df-4ba4-8620-3427aca24333", "name": "Move Binary Data", "type": "n8n-nodes-base.moveBinaryData", "position": [2000, 280], "parameters": {"mode": "jsonToBinary", "options": {"fileName": "={{ $json.name }}-{{ $json.active === false ? 'inactive' : $json.active === true ? 'active' : 'unknown' }}", "useRawData": true}}, "typeVersion": 1}, {"id": "049ac29e-36f2-4a14-9d3a-6fd9c9d8a744", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [260, -80], "parameters": {"color": 2, "width": 1003.460056384994, "height": 755.833854865218, "content": "## get Google Drive folders"}, "typeVersion": 1}, {"id": "e830c989-815d-4c79-806e-136a82a18d72", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1300, -80], "parameters": {"color": 6, "width": 427.1093081837156, "height": 753.2799109651138, "content": "## Ignore any other folders other than: n8n_backups\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n    (it is important that you created this folder)"}, "typeVersion": 1}, {"id": "4197519c-0cf7-49dc-be45-a5c0ab7598c2", "name": "IGNORE FILES", "type": "n8n-nodes-base.filter", "position": [1440, 40], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "98415e9e-5354-4223-9107-ef3ade30c2f0", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $node[\"GET CURRENT FOLDER\"].json.name }}", "rightValue": "n8n_backups"}]}}, "typeVersion": 2.2}, {"id": "d3f6191a-80c6-43dd-923f-e98f9ade02f4", "name": "Create n8n_backups", "type": "n8n-nodes-base.googleDrive", "position": [1000, 340], "parameters": {"name": "n8n_backups", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "root", "cachedResultName": "/ (Root folder)"}, "resource": "folder"}, "credentials": {"googleDriveOAuth2Api": {"id": "o1CgNemxQmc5Fyzd", "name": "Google Drive Alejandro <PERSON>"}}, "typeVersion": 3}, {"id": "b0ff6563-4ad5-4615-844a-aea766cf0d40", "name": "Create n8n_old", "type": "n8n-nodes-base.googleDrive", "position": [1000, 500], "parameters": {"name": "n8n_old", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "root", "cachedResultName": "/ (Root folder)"}, "resource": "folder"}, "credentials": {"googleDriveOAuth2Api": {"id": "o1CgNemxQmc5Fyzd", "name": "Google Drive Alejandro <PERSON>"}}, "typeVersion": 3}, {"id": "d22a25ea-e1fd-4434-b050-480760f6ba11", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [300, 540], "parameters": {"color": 6, "width": 355.73762189847923, "height": 105.6805438265643, "content": "## Contact me \n**By Mail**. [Send Mail](mailto:<EMAIL>)"}, "typeVersion": 1}, {"id": "b34e1e76-a8b8-4e0d-921b-1a773192e027", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [900, 220], "parameters": {"color": 5, "width": 327.6965514381564, "height": 451.756147757587, "content": "## Since the folder does not exist, it creates a new one.\nn8n_backups\nn8n_old"}, "typeVersion": 1}, {"id": "f0796631-ecb8-4603-838f-0ac1d1bf0a7b", "name": "GET CURRENT FOLDER", "type": "n8n-nodes-base.googleDrive", "onError": "continueRegularOutput", "position": [320, 240], "parameters": {"filter": {"whatToSearch": "folders"}, "options": {}, "resource": "fileFolder", "returnAll": true}, "credentials": {"googleDriveOAuth2Api": {"id": "o1CgNemxQmc5Fyzd", "name": "Google Drive Alejandro <PERSON>"}}, "executeOnce": true, "notesInFlow": true, "retryOnFail": true, "typeVersion": 3, "alwaysOutputData": true}, {"id": "8afbde8d-ae70-427c-8883-ffd49aea7ba7", "name": "Code", "type": "n8n-nodes-base.code", "position": [500, 240], "parameters": {"jsCode": "const items = $input.all();\nconst requiredNames = [\"n8n_old\", \"n8n_backups\"];\n\n// Filtrar los nombres de la entrada\nconst folderNames = items.map(item => item.json.name);\n\n// Encontrar los nombres que faltan\nconst missingNames = requiredNames.filter(name => !folderNames.includes(name));\n\nif (missingNames.length === 0) {\n  return [{ json: { message: \"ok\" } }];\n} else {\n  return [{ json: { message: `Faltan los siguientes: ${missingNames.join(', ')}` } }];\n}\n"}, "typeVersion": 2}, {"id": "2130d3d8-23e4-48d6-b3a0-7eab5971a71d", "name": "If n8n_old", "type": "n8n-nodes-base.if", "position": [680, 360], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "43bd468e-1018-4b45-9448-c51835ed65bc", "operator": {"type": "string", "operation": "contains"}, "leftValue": "={{ $json.message }}", "rightValue": "n8n_old"}]}}, "typeVersion": 2.2}, {"id": "76a4ab52-b260-4a1e-be77-a7246a06b963", "name": "If1 n8n_backups", "type": "n8n-nodes-base.if", "position": [680, 120], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "43bd468e-1018-4b45-9448-c51835ed65bc", "operator": {"type": "string", "operation": "contains"}, "leftValue": "={{ $json.message }}", "rightValue": "n8n_backups"}]}}, "typeVersion": 2.2}, {"id": "0a215059-a7bf-4892-b584-1f037b42a59c", "name": "GET CURRENT FOLDER CREATES", "type": "n8n-nodes-base.googleDrive", "onError": "continueRegularOutput", "position": [1100, 40], "parameters": {"filter": {"whatToSearch": "folders"}, "options": {}, "resource": "fileFolder", "returnAll": true}, "credentials": {"googleDriveOAuth2Api": {"id": "o1CgNemxQmc5Fyzd", "name": "Google Drive Alejandro <PERSON>"}}, "executeOnce": true, "notesInFlow": true, "retryOnFail": true, "typeVersion": 3, "alwaysOutputData": true}, {"id": "653d641c-b56f-4a02-b3bf-990b4f6b99f3", "name": "Merge mensage", "type": "n8n-nodes-base.merge", "position": [920, 40], "parameters": {"mode": "combine", "options": {}, "combinationMode": "mergeByPosition"}, "typeVersion": 2.1}, {"id": "ae940b77-107a-4e6f-a635-a69876b342ea", "name": "GET CURRENT BACKUPS1", "type": "n8n-nodes-base.googleDrive", "position": [1800, 0], "parameters": {"filter": {"folderId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}}, "options": {"fields": ["name", "id"]}, "resource": "fileFolder", "returnAll": true, "queryString": ".json"}, "credentials": {"googleDriveOAuth2Api": {"id": "o1CgNemxQmc5Fyzd", "name": "Google Drive Alejandro <PERSON>"}}, "typeVersion": 3}, {"id": "7caa0190-9bd5-4572-80e3-e3f3b34885a6", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [640, -40], "parameters": {"color": 7, "width": 203.08765089203305, "height": 542.95115693689, "content": "## Does a folder exist?, if it does not exist it creates it"}, "typeVersion": 1}, {"id": "1a77a0fd-dfdd-456d-adfc-6da34a4ccbab", "name": "MOVE INTO OLD FOLDER", "type": "n8n-nodes-base.googleDrive", "onError": "continueRegularOutput", "position": [2480, -20], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "driveId": {"__rl": true, "mode": "list", "value": "My Drive", "cachedResultUrl": "https://drive.google.com/drive/my-drive", "cachedResultName": "My Drive"}, "folderId": {"__rl": true, "mode": "id", "value": "={{ $('GET CURRENT FOLDER').item.json.id }}"}, "operation": "move"}, "credentials": {"googleDriveOAuth2Api": {"id": "o1CgNemxQmc5Fyzd", "name": "Google Drive Alejandro <PERSON>"}}, "typeVersion": 3, "alwaysOutputData": true}, {"id": "f9fad351-8e82-49a3-a7da-7a43b0735c34", "name": "UPLOAD WORKFLOWS", "type": "n8n-nodes-base.googleDrive", "position": [2480, 260], "parameters": {"name": "={{ $('Split In Batches').item.binary.data.fileName }}-{{ $node[\"n8n\"].json[\"updatedAt\"] }}.json\n\n", "options": {}, "parents": ["={{ $('IGNORE FILES').item.json.id }}"], "binaryData": true, "authentication": "oAuth2"}, "credentials": {"googleDriveOAuth2Api": {"id": "o1CgNemxQmc5Fyzd", "name": "Google Drive Alejandro <PERSON>"}}, "typeVersion": 1}, {"id": "c8496ac4-b767-4fc3-bda3-12c0550763c4", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-180, -80], "parameters": {"color": 3, "width": 397.**************, "height": 759.*************, "content": "## Description\nThis template creates a nightly backup of all n8n workflows and saves them to a GitHub folder. Each night, the previous night's backups are moved to an “n8n_old” folder and renamed with the corresponding date.\n\nBackups older than a specified age are automatically deleted (this feature is active for 30 days, you can remove it if you don't want the backups to be deleted).\n\n## Prerequisites\n\n- Google Drive account and credentials **Get** from the following link. [Link](https://console.cloud.google.com/apis/credentials/oauthclient/)\n- n8n version 1.67.1 or higher\n- N8n api key **Guide** from the following link. [Link](https://witmovil.com/where-to-create-the-api-key-in-n8n/)\n- A destination folder for backups:\n“n8n_old”\n“n8n_backups”\n(if it doesn't exist, create it)\n\n## Configuration\nUpdate all Google Drive nodes with your credentials.\nEdit the Schedule Trigger node with the desired time to run the backup.\nIf you want to automatically purge old backups.\n\nEdit the “PURGE DAYS” node to specify the age of the backups you want to delete.\nEnable the “PURGE DAYS” node and the 3 subsequent nodes.\nEnable the workflow to run on the specified schedule."}, "typeVersion": 1}, {"id": "4654d857-8436-4922-aa9a-9f00d357e581", "name": "Item Lists", "type": "n8n-nodes-base.itemLists", "position": [2000, 0], "parameters": {"options": {}, "fieldToSplitOut": "id"}, "typeVersion": 3}, {"id": "9e9cc97d-1eff-40ea-9a1d-896681330b5e", "name": "Split In Batches2", "type": "n8n-nodes-base.splitInBatches", "position": [2220, 0], "parameters": {"options": {"reset": false}, "batchSize": 1}, "typeVersion": 2}, {"id": "1bd963e2-6533-4d71-8790-fa840af822ab", "name": "Split In Batches", "type": "n8n-nodes-base.splitInBatches", "position": [2220, 280], "parameters": {"options": {"reset": false}, "batchSize": 1}, "typeVersion": 2}, {"id": "aa9a5b1c-2c6b-4aff-af66-f15271eed643", "name": "n8n", "type": "n8n-nodes-base.n8n", "position": [1800, 280], "parameters": {"filters": {}, "returnAll": false, "requestOptions": {}}, "credentials": {"n8nApi": {"id": "vPlm2YAtWv47eJLp", "name": "n8n witmovil"}}, "typeVersion": 1}, {"id": "d6455261-c3af-4f5a-8f7e-0dd57c5306e5", "name": "LIST OLD BACKUPS", "type": "n8n-nodes-base.googleDrive", "position": [1960, 520], "parameters": {"filter": {"folderId": {"__rl": true, "mode": "list", "value": "1UcusrWKnbFl3cJYIjaDdp1VCgreg2oeV", "cachedResultUrl": "https://drive.google.com/drive/folders/1UcusrWKnbFl3cJYIjaDdp1VCgreg2oeV", "cachedResultName": "n8n_old"}}, "options": {"fields": ["name", "id"]}, "resource": "fileFolder", "returnAll": true, "queryString": ".json"}, "credentials": {"googleDriveOAuth2Api": {"id": "o1CgNemxQmc5Fyzd", "name": "Google Drive Alejandro <PERSON>"}}, "typeVersion": 3}, {"id": "aa1878bd-b90e-4f37-bf2e-bb4fd72b4571", "name": "DELETE OLD BACKUP", "type": "n8n-nodes-base.googleDrive", "onError": "continueRegularOutput", "position": [2560, 500], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "options": {"deletePermanently": true}, "operation": "deleteFile"}, "credentials": {"googleDriveOAuth2Api": {"id": "o1CgNemxQmc5Fyzd", "name": "Google Drive Alejandro <PERSON>"}}, "typeVersion": 3, "alwaysOutputData": true}, {"id": "bde79076-3fb4-4f03-a907-fc492f88a17e", "name": "Item Lists old", "type": "n8n-nodes-base.itemLists", "position": [2160, 520], "parameters": {"options": {}, "fieldToSplitOut": "id"}, "typeVersion": 3}, {"id": "0bd6da8c-99ed-47ea-bb26-11e08e2f76e1", "name": "Split In Batches old", "type": "n8n-nodes-base.splitInBatches", "position": [2360, 520], "parameters": {"options": {"reset": false}, "batchSize": 1}, "typeVersion": 2}, {"id": "fa6fb3be-baba-4bbe-9900-b0949507d164", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [1320, 380], "parameters": {"color": 3, "width": 344.2988945561964, "height": 232.84367238845454, "content": "## Bug fixes v3:\n* Fixed move section now detects more than 13 files and moves them to n8n_old folder\n* Changed file filtering\n* In the next version \"Split In Batches\" will be changed to \"Loop Over Items\""}, "typeVersion": 1}, {"id": "cf2d27b7-8601-466a-8331-c767b9c0c25a", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1740, 220], "parameters": {"color": 5, "width": 984.8018228465335, "height": 267.23574473121596, "content": "BACKUP ALL CURRENT WORKFLOWS limit 100 (Change)"}, "typeVersion": 1}, {"id": "484b37a9-8b21-4887-9443-bcb8ca34b57d", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [320, 20], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.1}, {"id": "40a6f21f-f044-4bb5-8d01-1fbdc4185eae", "name": "Schedule Trigger1", "type": "n8n-nodes-base.scheduleTrigger", "position": [1760, 560], "parameters": {"rule": {"interval": [{"daysInterval": 30}]}}, "typeVersion": 1.1}], "pinData": {}, "connections": {"n8n": {"main": [[{"node": "Move Binary Data", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "If n8n_old", "type": "main", "index": 0}, {"node": "If1 n8n_backups", "type": "main", "index": 0}]]}, "If n8n_old": {"main": [[{"node": "Create n8n_old", "type": "main", "index": 0}], [{"node": "Merge mensage", "type": "main", "index": 1}]]}, "Item Lists": {"main": [[{"node": "Split In Batches2", "type": "main", "index": 0}]]}, "IGNORE FILES": {"main": [[{"node": "GET CURRENT BACKUPS1", "type": "main", "index": 0}, {"node": "n8n", "type": "main", "index": 0}]]}, "Merge mensage": {"main": [[{"node": "GET CURRENT FOLDER CREATES", "type": "main", "index": 0}]]}, "Create n8n_old": {"main": [[{"node": "GET CURRENT FOLDER", "type": "main", "index": 0}]]}, "Item Lists old": {"main": [[{"node": "Split In Batches old", "type": "main", "index": 0}]]}, "If1 n8n_backups": {"main": [[{"node": "Create n8n_backups", "type": "main", "index": 0}], [{"node": "Merge mensage", "type": "main", "index": 0}]]}, "LIST OLD BACKUPS": {"main": [[{"node": "Item Lists old", "type": "main", "index": 0}]]}, "Move Binary Data": {"main": [[{"node": "Split In Batches", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "GET CURRENT FOLDER", "type": "main", "index": 0}]]}, "Split In Batches": {"main": [[{"node": "UPLOAD WORKFLOWS", "type": "main", "index": 0}]]}, "UPLOAD WORKFLOWS": {"main": [[{"node": "Split In Batches", "type": "main", "index": 0}]]}, "DELETE OLD BACKUP": {"main": [[{"node": "Split In Batches old", "type": "main", "index": 0}]]}, "Split In Batches2": {"main": [[{"node": "MOVE INTO OLD FOLDER", "type": "main", "index": 0}]]}, "Create n8n_backups": {"main": [[{"node": "GET CURRENT FOLDER", "type": "main", "index": 0}]]}, "GET CURRENT FOLDER": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "GET CURRENT BACKUPS1": {"main": [[{"node": "Item Lists", "type": "main", "index": 0}]]}, "MOVE INTO OLD FOLDER": {"main": [[{"node": "Split In Batches2", "type": "main", "index": 0}]]}, "Split In Batches old": {"main": [[{"node": "DELETE OLD BACKUP", "type": "main", "index": 0}]]}, "GET CURRENT FOLDER CREATES": {"main": [[{"node": "IGNORE FILES", "type": "main", "index": 0}]]}}}