{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "<PERSON>", "type": "n8n-nodes-base.hunter", "position": [450, 300], "parameters": {"email": "<EMAIL>", "operation": "emailVerifier"}, "credentials": {"hunterApi": "hunter api creds"}, "typeVersion": 1}], "connections": {"On clicking 'execute'": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}}}