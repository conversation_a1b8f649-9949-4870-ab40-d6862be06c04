{"id": "cZPEH5aMMZNy61xs", "meta": {"instanceId": "3378b0d68c3b7ebfc71b79896d94e1a044dec38e99a1160aed4e9c323910fbe2", "templateCredsSetupCompleted": true}, "name": "template in store", "tags": [], "nodes": [{"id": "14f93cdb-72cb-419a-b8d7-a68ae9383290", "name": "Google Drive Trigger", "type": "n8n-nodes-base.googleDriveTrigger", "position": [440, 320], "parameters": {"event": "fileCreated", "options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "mode": "list", "value": "18m0i341QLQuyWuHv_FBdz8-r-QDtofYm", "cachedResultUrl": "https://drive.google.com/drive/folders/18m0i341QLQuyWuHv_FBdz8-r-QDtofYm", "cachedResultName": "Influencersde"}}, "credentials": {"googleDriveOAuth2Api": {"id": "2TbhWtnbRfSloGxX", "name": "Google Drive account"}}, "typeVersion": 1}, {"id": "d4ab0d11-b110-46fa-9cd2-6091737c302e", "name": "Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [620, 320], "parameters": {"fileId": {"__rl": true, "mode": "", "value": "={{ $json.id || $json.data[0].id }}"}, "options": {}, "operation": "download", "authentication": "oAuth2"}, "credentials": {"googleDriveOAuth2Api": {"id": "2TbhWtnbRfSloGxX", "name": "Google Drive account"}}, "retryOnFail": true, "typeVersion": 1, "waitBetweenTries": 5000}, {"id": "fde9df88-3f9e-4732-bb1c-72eb33ce6826", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.errorTrigger", "position": [840, 660], "parameters": {}, "typeVersion": 1}, {"id": "ecfe1ad1-6887-492b-a2f7-f9b6c43f9b91", "name": "Telegram", "type": "n8n-nodes-base.telegram", "position": [1180, 640], "webhookId": "f6729386-9905-45f1-800f-4fe01a06ac9c", "parameters": {"text": "=🔔 ERROR SUBIENDO VIDEOS", "chatId": "-**********", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "vzA62UXRgiFICuPP", "name": "Telegram account"}}, "retryOnFail": true, "typeVersion": 1.2, "waitBetweenTries": 5000}, {"id": "6ed274c7-726f-40aa-92b0-70768dc053a5", "name": "If", "type": "n8n-nodes-base.if", "position": [980, 660], "parameters": {"options": {}, "conditions": {"options": {"version": 1, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "9fadb3fd-2547-42bd-8f40-f410a97dcf57", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.trigger.error.message }}", "rightValue": "The DNS server returned an error, perhaps the server is offline"}]}}, "typeVersion": 2.1}, {"id": "dd4b2dfa-ccba-45d8-b388-755888343b4c", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [0, 0], "parameters": {"width": 860, "height": 260, "content": "## Description\nThis automation allows you to upload a video to a configured Google Drive folder, and it will automatically create descriptions and upload it to Instagram and TikTok.\n\n## How to Use\n1. Generate an API token at upload-post.com and add to Upload to Tiktok and Upload to Instagram nodes\n2. Configure your Google Drive folder\n3. Customize the OpenAI prompt for your specific use case\n4. Optional: Configure Telegram for error notifications\n\n## Requirements\n- upload-post.com account\n- Google Drive account\n- OpenAI API key\n"}, "typeVersion": 1}, {"id": "299e3e95-dbcb-4798-b843-a4424ce3f3bf", "name": "Get Audio from Video", "type": "@n8n/n8n-nodes-langchain.openAi", "notes": "Extract the audio from video for generate the description", "position": [1080, 320], "parameters": {"options": {}, "resource": "audio", "operation": "transcribe"}, "credentials": {"openAiApi": {"id": "XJdxgMSXFgwReSsh", "name": "n8n key"}}, "notesInFlow": true, "retryOnFail": true, "typeVersion": 1, "waitBetweenTries": 5000}, {"id": "da9048ce-542e-44e0-ba67-ab853822c428", "name": "Read video from Google Drive", "type": "n8n-nodes-base.writeBinaryFile", "position": [800, 320], "parameters": {"options": {}, "fileName": "={{ $json.originalFilename.replaceAll(\" \", \"_\") }}"}, "typeVersion": 1}, {"id": "5977baf1-d4a2-439f-aafe-14745201d3d8", "name": "Generate Description for Videos in Tiktok and Instagram", "type": "@n8n/n8n-nodes-langchain.openAi", "notes": "Request to OpenAi for generate description with the audio extracted from the video", "position": [1280, 320], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "GPT-4O"}, "options": {}, "messages": {"values": [{"role": "system", "content": "You are an expert assistant in creating engaging social media video titles."}, {"content": "=I'm going to upload a video to social media. Here are some examples of descriptions that have worked well on Instagram:\n\nFollow and save for later. Discover InfluencersDe, the AI tool that automates TikTok creation and publishing to drive traffic to your website. Perfect for entrepreneurs and brands.\n#digitalmarketing #ugc #tiktok #ai #influencersde #contentcreation\n\nDiscover the video marketing revolution with InfluencersDe!\n.\n.\n.\n#socialmedia #videomarketing #ai #tiktok #influencersde #growthhacking\n\nDon't miss InfluencersDe, the tool that transforms your marketing strategy with just one click!\n.\n.\n.\n#ugc #ai #tiktok #digitalmarketing #influencersde #branding\n\nCan you create another title for the Instagram post based on this recognized audio from the video?\n\nAudio: {{ $('Get Audio from Video').item.json.text }}\n\nIMPORTANT: Reply only with the description, don't add anything else."}]}}, "credentials": {"openAiApi": {"id": "XJdxgMSXFgwReSsh", "name": "n8n key"}}, "notesInFlow": true, "retryOnFail": true, "typeVersion": 1.4, "waitBetweenTries": 5000}, {"id": "a139c8b0-b934-492b-8f85-e42c9c345af4", "name": "Read Video from Google Drive", "type": "n8n-nodes-base.readBinaryFile", "position": [1840, 100], "parameters": {"filePath": "={{ $('Read video from Google Drive').item.json.originalFilename.replaceAll(\" \", \"_\") }}", "dataPropertyName": "datavideo"}, "typeVersion": 1}, {"id": "63230edb-8346-4441-929f-1f6403507501", "name": "Read Video from Google Drive2", "type": "n8n-nodes-base.readBinaryFile", "position": [1840, 460], "parameters": {"filePath": "={{ $('Read video from Google Drive').item.json.originalFilename.replaceAll(\" \", \"_\") }}", "dataPropertyName": "datavideo"}, "typeVersion": 1}, {"id": "5d6e26ef-1bb4-43d6-a282-151c95856905", "name": "Upload Video and Description to Tiktok", "type": "n8n-nodes-base.httpRequest", "notes": "Generate in upload-post.com the token and add to the credentials in the header-> Authorization: Apikey (token here)", "position": [2100, 100], "parameters": {"url": "https://api.upload-post.com/api/upload", "method": "POST", "options": {}, "sendBody": true, "contentType": "multipart-form-data", "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "title", "value": "={{ $('Generate Description for Videos in Tiktok and Instagram').item.json.message.content.replaceAll(\"\\\"\", \"\") }}"}, {"name": "platform[]", "value": "tiktok"}, {"name": "video", "parameterType": "formBinaryData", "inputDataFieldName": "datavideo"}, {"name": "user", "value": "Add user generated in upload-post"}]}, "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "47dO31ED0WIaJkR6", "name": "Header Auth account"}}, "notesInFlow": true, "typeVersion": 4.2}, {"id": "ed785663-50e4-43cc-9dc0-a340d0360b38", "name": "Upload Video and Description to Instagram", "type": "n8n-nodes-base.httpRequest", "notes": "Generate in upload-post.com the token and add to the credentials in the header-> Authorization: Apikey (token here)", "position": [2100, 460], "parameters": {"url": "https://api.upload-post.com/api/upload", "method": "POST", "options": {}, "sendBody": true, "contentType": "multipart-form-data", "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "title", "value": "={{ $('Generate Description for Videos in Tiktok and Instagram').item.json.message.content.replaceAll(\"\\\"\", \"\") }}"}, {"name": "platform[]", "value": "instagram"}, {"name": "video", "parameterType": "formBinaryData", "inputDataFieldName": "datavideo"}, {"name": "user", "value": "Add user generated in upload-post"}]}, "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "47dO31ED0WIaJkR6", "name": "Header Auth account"}}, "notesInFlow": true, "typeVersion": 4.2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "fdcd0643-0958-426c-ab1d-16fb061b4e38", "connections": {"If": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "Google Drive": {"main": [[{"node": "Read video from Google Drive", "type": "main", "index": 0}]]}, "Error Trigger": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Get Audio from Video": {"main": [[{"node": "Generate Description for Videos in Tiktok and Instagram", "type": "main", "index": 0}]]}, "Google Drive Trigger": {"main": [[{"node": "Google Drive", "type": "main", "index": 0}]]}, "Read Video from Google Drive": {"main": [[{"node": "Upload Video and Description to Tiktok", "type": "main", "index": 0}]]}, "Read video from Google Drive": {"main": [[{"node": "Get Audio from Video", "type": "main", "index": 0}]]}, "Read Video from Google Drive2": {"main": [[{"node": "Upload Video and Description to Instagram", "type": "main", "index": 0}]]}, "Generate Description for Videos in Tiktok and Instagram": {"main": [[{"node": "Read Video from Google Drive", "type": "main", "index": 0}, {"node": "Read Video from Google Drive2", "type": "main", "index": 0}]]}}}