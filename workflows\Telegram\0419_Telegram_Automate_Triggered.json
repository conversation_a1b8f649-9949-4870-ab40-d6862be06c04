{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "0f5aae97-3819-4704-ada2-abbcf14cea5f", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [20, 380], "parameters": {"text": "=Respond to this as a helpful assistant with emojis:  {{ $json.message.text }}", "options": {}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "9f828f62-b587-43be-a47f-b2500e36bd9c", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "position": [-220, 380], "webhookId": "9bf61652-efa6-47b0-9f52-e0c3362d93e5", "parameters": {"updates": ["message"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "XVBXGXSsaCjU2DOS", "name": "jim<PERSON>uk_handoff_bot"}}, "typeVersion": 1.1}, {"id": "abb92991-faee-4678-9038-7555f694acb1", "name": "Telegram", "type": "n8n-nodes-base.telegram", "position": [380, 380], "webhookId": "5babdcad-dabe-4c8e-8f84-6957e9f1aa15", "parameters": {"text": "={{ $json.output }}", "chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "XVBXGXSsaCjU2DOS", "name": "jim<PERSON>uk_handoff_bot"}}, "typeVersion": 1.2}, {"id": "b20244ba-e15d-4f7f-939f-1c9d8474743a", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-80, 600], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}], "pinData": {}, "connections": {"AI Agent": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "Telegram Trigger": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}}}