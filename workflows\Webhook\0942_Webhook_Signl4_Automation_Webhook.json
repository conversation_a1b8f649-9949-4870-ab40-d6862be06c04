{"id": 3, "name": "TheHive", "nodes": [{"name": "TheHive Create Al<PERSON>", "type": "n8n-nodes-base.theHive", "position": [500, 360], "parameters": {"date": "2022-04-25T08:53:18.000Z", "tags": "tlp:pwhite", "type": "misp", "title": "TheHive Alert", "source": "1311", "sourceRef": "1330", "description": "Security issue detected on server A2. Please check and take care.", "additionalFields": {}}, "credentials": {"theHiveApi": {"id": "1", "name": "The Hive account"}}, "typeVersion": 1}, {"name": "TheHive Read Alerts", "type": "n8n-nodes-base.theHive", "position": [500, 200], "parameters": {"filters": {}, "options": {}, "operation": "getAll"}, "credentials": {"theHiveApi": {"id": "1", "name": "The Hive account"}}, "typeVersion": 1}, {"name": "IF", "type": "n8n-nodes-base.if", "position": [280, 540], "parameters": {"conditions": {"boolean": [{"value1": "={{$node[\"TheHive Webhook Request\"].json[\"body\"][\"object\"][\"stage\"]}}", "value2": "=Closed", "operation": "notEqual"}]}}, "typeVersion": 1}, {"name": "SIGNL4 Send Alert", "type": "n8n-nodes-base.signl4", "position": [500, 520], "parameters": {"message": "={{$node[\"TheHive Webhook Request\"].json[\"body\"][\"details\"][\"description\"]}}", "additionalFields": {"title": "={{$node[\"TheHive Webhook Request\"].json[\"body\"][\"details\"][\"title\"]}}", "externalId": "={{$node[\"TheHive Webhook Request\"].json[\"body\"][\"objectId\"]}}"}}, "credentials": {"signl4Api": {"id": "2", "name": "SIGNL4 Webhook account"}}, "typeVersion": 1}, {"name": "TheHive Webhook Request", "type": "n8n-nodes-base.webhook", "position": [80, 540], "webhookId": "22c76955-3f52-469e-a8ae-3f62e8e87ebe", "parameters": {"path": "22c76955-3f52-469e-a8ae-3f62e8e87ebe", "options": {}, "httpMethod": "POST"}, "typeVersion": 1}, {"name": "Start (Testing)", "type": "n8n-nodes-base.manualTrigger", "position": [80, 200], "parameters": {}, "typeVersion": 1}, {"name": "SIGNL4 Resolve Alert", "type": "n8n-nodes-base.signl4", "position": [500, 720], "parameters": {"operation": "resolve", "externalId": "={{$node[\"TheHive Webhook Request\"].json[\"body\"][\"objectId\"]}}"}, "credentials": {"signl4Api": {"id": "2", "name": "SIGNL4 Webhook account"}}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"IF": {"main": [[{"node": "SIGNL4 Send Alert", "type": "main", "index": 0}], [{"node": "SIGNL4 Resolve Alert", "type": "main", "index": 0}]]}, "Start (Testing)": {"main": [[{"node": "TheHive Create Al<PERSON>", "type": "main", "index": 0}]]}, "TheHive Webhook Request": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}}}