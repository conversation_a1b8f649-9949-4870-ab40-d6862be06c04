{"nodes": [{"id": "82fd6023-2cc3-416e-83b7-fda24d07d77a", "name": "Issues to List", "type": "n8n-nodes-base.splitOut", "position": [40, -100], "parameters": {"options": {}, "fieldToSplitOut": "data.issues.nodes"}, "typeVersion": 1}, {"id": "9cc77786-e14f-47c6-a3cf-60c2830612e6", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [360, 80], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "821d4a60-81a4-4915-9c13-3d978cc0114b", "name": "Combine Sentiment Analysis", "type": "n8n-nodes-base.set", "position": [700, -80], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "={{\n{\n ...$('Issues to List').item.json,\n ...$json.output\n}\n}}"}, "typeVersion": 3.4}, {"id": "fe6560f6-2e1b-4442-a2af-bd5a1623f213", "name": "Sentiment over Issue Comments", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [360, -80], "parameters": {"text": "={{\n$json.comments.nodes.map(node => [\n `${node.user.displayName} commented on ${node.createdAt}:`,\n node.body\n].join('\\n')).join('---\\n')\n}}", "options": {}, "attributes": {"attributes": [{"name": "sentiment", "required": true, "description": "One of positive, negative or neutral"}, {"name": "sentimentSummary", "description": "Describe the sentiment of the conversation"}]}}, "typeVersion": 1}, {"id": "4fd0345d-e5bf-426d-8403-e2217e19bbea", "name": "Copy of Issue", "type": "n8n-nodes-base.set", "position": [1200, -60], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "={{ $json }}"}, "typeVersion": 3.4}, {"id": "6d103d67-451e-4780-8f52-f4dba4b42860", "name": "For Each Issue...", "type": "n8n-nodes-base.splitInBatches", "position": [1020, -60], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "032702d9-27d8-4735-b978-20b55bc1a74f", "name": "Get Existing Sentiment", "type": "n8n-nodes-base.airtable", "position": [1380, -60], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appViDaeaFw4qv9La", "cachedResultUrl": "https://airtable.com/appViDaeaFw4qv9La", "cachedResultName": "Sentiment Analysis over Issue Comments"}, "table": {"__rl": true, "mode": "list", "value": "tblhO0sfRhKP6ibS8", "cachedResultUrl": "https://airtable.com/appViDaeaFw4qv9La/tblhO0sfRhKP6ibS8", "cachedResultName": "Table 1"}, "options": {"fields": ["Issue ID", "Current Sentiment"]}, "operation": "search", "filterByFormula": "={Issue ID} = '{{ $json.identifier || 'XYZ' }}'"}, "credentials": {"airtableTokenApi": {"id": "Und0frCQ6SNVX3VV", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1, "alwaysOutputData": true}, {"id": "f2ded6fa-8b0f-4a34-868c-13c19f725c98", "name": "Update Row", "type": "n8n-nodes-base.airtable", "position": [1560, -60], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appViDaeaFw4qv9La", "cachedResultUrl": "https://airtable.com/appViDaeaFw4qv9La", "cachedResultName": "Sentiment Analysis over Issue Comments"}, "table": {"__rl": true, "mode": "list", "value": "tblhO0sfRhKP6ibS8", "cachedResultUrl": "https://airtable.com/appViDaeaFw4qv9La/tblhO0sfRhKP6ibS8", "cachedResultName": "Table 1"}, "columns": {"value": {"Summary": "={{ $('Copy of Issue').item.json.sentimentSummary || '' }}", "Assigned": "={{ $('Copy of Issue').item.json.assignee.name }}", "Issue ID": "={{ $('Copy of Issue').item.json.identifier }}", "Issue Title": "={{ $('Copy of Issue').item.json.title }}", "Issue Created": "={{ $('Copy of Issue').item.json.createdAt }}", "Issue Updated": "={{ $('Copy of Issue').item.json.updatedAt }}", "Current Sentiment": "={{ $('Copy of Issue').item.json.sentiment.toSentenceCase() }}", "Previous Sentiment": "={{ !$json.isEmpty() ? $json['Current Sentiment'] : 'N/A' }}"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "Issue ID", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Issue ID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Previous Sentiment", "type": "options", "display": true, "options": [{"name": "Positive", "value": "Positive"}, {"name": "Negative", "value": "Negative"}, {"name": "Neutral", "value": "Neutral"}, {"name": "N/A", "value": "N/A"}], "removed": false, "readOnly": false, "required": false, "displayName": "Previous Sentiment", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Current Sentiment", "type": "options", "display": true, "options": [{"name": "Positive", "value": "Positive"}, {"name": "Negative", "value": "Negative"}, {"name": "Neutral", "value": "Neutral"}, {"name": "N/A", "value": "N/A"}], "removed": false, "readOnly": false, "required": false, "displayName": "Current Sentiment", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Summary", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Issue Title", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Issue Title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Issue Created", "type": "dateTime", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Issue Created", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Issue Updated", "type": "dateTime", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Issue Updated", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Assigned", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Assigned", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Created", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Created", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Last Modified", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Last Modified", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["Issue ID"]}, "options": {}, "operation": "upsert"}, "credentials": {"airtableTokenApi": {"id": "Und0frCQ6SNVX3VV", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "e6fb0b8f-2469-4b66-b9e2-f4f3c0a613af", "name": "Airtable Trigger", "type": "n8n-nodes-base.airtableTrigger", "position": [1900, -40], "parameters": {"baseId": {"__rl": true, "mode": "id", "value": "appViDaeaFw4qv9La"}, "tableId": {"__rl": true, "mode": "id", "value": "tblhO0sfRhKP6ibS8"}, "pollTimes": {"item": [{"mode": "everyHour"}]}, "triggerField": "Current Sentiment", "authentication": "airtableTokenApi", "additionalFields": {}}, "credentials": {"airtableTokenApi": {"id": "Und0frCQ6SNVX3VV", "name": "Airtable Personal Access Token account"}}, "typeVersion": 1}, {"id": "669762c4-860b-43ad-b677-72d4564e1c29", "name": "Sentiment Transition", "type": "n8n-nodes-base.switch", "position": [2080, -40], "parameters": {"rules": {"values": [{"outputKey": "NON-NEGATIVE to NEGATIVE", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.fields[\"Previous Sentiment\"] !== 'Negative' && $json.fields[\"Current Sentiment\"] === 'Negative' }}", "rightValue": ""}]}, "renameOutput": true}]}, "options": {"fallbackOutput": "none"}}, "typeVersion": 3.2}, {"id": "2fbcfbea-3989-459b-8ca7-b65c130a479b", "name": "Fetch Active Linear Issues", "type": "n8n-nodes-base.graphql", "position": [-140, -100], "parameters": {"query": "=query (\n $filter: IssueFilter\n) {\n issues(\n filter: $filter\n ) {\n nodes {\n id\n identifier\n title\n description\n url\n createdAt\n updatedAt\n assignee {\n name\n }\n comments {\n nodes {\n id\n createdAt\n user {\n displayName\n }\n body\n }\n }\n }\n }\n}", "endpoint": "https://api.linear.app/graphql", "variables": "={{\n{\n \"filter\": {\n updatedAt: { gte: $now.minus(30, 'minutes').toISO() }\n }\n}\n}}", "requestFormat": "json", "authentication": "headerAuth"}, "credentials": {"httpHeaderAuth": {"id": "XME2Ubkuy9hpPEM5", "name": "Linear.app (heightio)"}}, "typeVersion": 1}, {"id": "aaf1c25e-c398-4715-88bf-bd98daafc10f", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-340, -100], "parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 30}]}}, "typeVersion": 1.2}, {"id": "b3e2df39-90ce-4ebf-aa68-05499965ec30", "name": "Deduplicate Notifications", "type": "n8n-nodes-base.removeDuplicates", "position": [2280, -40], "parameters": {"options": {}, "operation": "removeItemsSeenInPreviousExecutions", "dedupeValue": "={{ $json.fields[\"Issue ID\"] }}:{{ $json.fields['Last Modified'] }}"}, "typeVersion": 2}, {"id": "2a116475-32cd-4c9d-bfc1-3bd494f79a49", "name": "Report Issue Negative Transition", "type": "n8n-nodes-base.slack", "position": [2480, -40], "webhookId": "612f1001-3fcc-480b-a835-05f9e2d56a5f", "parameters": {"text": "={{ $('Deduplicate Notifications').all().length }} Issues have transitions to Negative Sentiment", "select": "channel", "blocksUi": "={{\n{\n \"blocks\": [\n {\n \"type\": \"section\",\n \"text\": {\n \"type\": \"mrkdwn\",\n \"text\": \":rotating_light: The following Issues transitioned to Negative Sentiment\"\n }\n },\n {\n \"type\": \"divider\"\n },\n ...($('Deduplicate Notifications').all().map(item => (\n {\n \"type\": \"section\",\n \"text\": {\n \"type\": \"mrkdwn\",\n \"text\": `*<https://linear.app/myOrg/issue/${$json.fields['Issue ID']}|${$json.fields['Issue ID']} ${$json.fields['Issue Title']}>*\\n${$json.fields.Summary}`\n }\n }\n )))\n ]\n}\n}}", "channelId": {"__rl": true, "mode": "list", "value": "C0749JVFERK", "cachedResultName": "n8n-tickets"}, "messageType": "block", "otherOptions": {}}, "credentials": {"slackApi": {"id": "VfK3js0YdqBdQLGP", "name": "Slack account"}}, "executeOnce": true, "typeVersion": 2.3}, {"id": "1f3d30b6-de31-45a8-a872-554c339f112f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-420, -320], "parameters": {"color": 7, "width": 660, "height": 440, "content": "## 1. Continuously Monitor Active Linear Issues\n[Learn more about the GraphQL node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.graphql)\n\nTo keep up with the latest changes in our active Linear tickets, we'll need to use Linear's GraphQL endpoint because filtering is currently unavailable in the official Linear.app node.\n\nFor this demonstration, we'll check for updated tickets every 30mins."}, "typeVersion": 1}, {"id": "9024512d-5cb9-4e9f-b6e1-495d1a32118a", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [260, -320], "parameters": {"color": 7, "width": 640, "height": 560, "content": "## 2. Sentiment Analysis on Current Issue Activity\n[Learn more about the Information Extractor node](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.information-extractor)\n\nWith our recently updated posts, we can use our AI to perform a quick sentiment analysis on the ongoing conversation to check the overall mood of the support issue. This is a great way to check how things are generally going in the support queue; positive should be normal but negative could indicate some uncomfortableness or even frustration."}, "typeVersion": 1}, {"id": "233ebd6d-38cb-4f2d-84b5-29c97d30d77b", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [920, -320], "parameters": {"color": 7, "width": 840, "height": 560, "content": "## 3. Capture and Track Results in Airtable\n[Learn more about the Airtable node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.airtable)\n\nNext, we can capture this analysis in our insights database as means for human review. When the issue is new, we can create a new row but if the issue exists, we will update it's existing row instead.\n\nWhen updating an existing row, we move its previous \"current sentiment\" value into the \"previous sentiment\" column and replace with our new current sentiment. This gives us a \"sentiment transition\" which will be useful in the next step.\n\nCheck out the Airtable here: https://airtable.com/appViDaeaFw4qv9La/shrq6HgeYzpW6uwXL"}, "typeVersion": 1}, {"id": "a2229225-b580-43cb-b234-4f69cb5924fd", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1800, -320], "parameters": {"color": 7, "width": 920, "height": 560, "content": "## 4. Get Notified when Sentiment becomes Negative\n[Learn more about the Slack node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.slack/)\n\nA good use-case for tracking sentiment transitions could be to be alerted if ever an issue moves from a non-negative sentiment to a negative one. This could be a signal of issue handling troubles which may require attention before it escalates.\n\nIn this demonstration, we use the Airtable trigger to catch rows which have their sentiment column updated and check for the non-negative-to-negative sentiment transition using the switch node. For those matching rows, we combine add send a notification via slack. A cool trick is to use the \"remove duplication\" node to prevent repeat notifications for the same updates - here we combine the Linear issue key and the row's last modified date."}, "typeVersion": 1}, {"id": "6f26769e-ec5d-46d0-ae0a-34148b24e6a2", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-940, -720], "parameters": {"width": 480, "height": 840, "content": "## Try It Out!\n### This n8n template performs continous monitoring on Linear Issue conversations performing sentiment analysis and alerting when the sentiment becomes negative.\nThis is helpful to quickly identify difficult customer support situations early and prioritising them before they get out of hand.\n\n## How it works\n* A scheduled trigger is used to fetch recently updated issues in Linear using the GraphQL node.\n* Each issue's comments thread is passed into a simple Information Extractor node to identify the overall sentiment.\n* The resulting sentiment analysis combined with the some issue details are uploaded to Airtable for review.\n* When the template is re-run at a later date, each issue is re-analysed for sentiment\n* Each issue's new sentiment state is saved to the airtable whilst its previous state is moved to the \"previous sentiment\" column.\n* An Airtable trigger is used to watch for recently updated rows\n* Each matching Airtable row is filtered to check if it has a previous non-negative state but now has a negative state in its current sentiment.\n* The results are sent via notification to a team slack channel for priority.\n\n**Check out the sample Airtable here**: https://airtable.com/appViDaeaFw4qv9La/shrq6HgeYzpW6uwXL\n\n## How to use\n* Modify the GraphQL filter to fetch issues to a relevant issue type, team or person.\n* Update the Slack channel to ensure messages are sent to the correct location.\n\n### Need Help?\nJoin the [Discord](https://discord.com/invite/XPKeKXeB7d) or ask in the [Forum](https://community.n8n.io/)!\n\nHappy Hacking!"}, "typeVersion": 1}], "pinData": {}, "connections": {"Update Row": {"main": [[{"node": "For Each Issue...", "type": "main", "index": 0}]]}, "Copy of Issue": {"main": [[{"node": "Get Existing Sentiment", "type": "main", "index": 0}]]}, "Issues to List": {"main": [[{"node": "Sentiment over Issue Comments", "type": "main", "index": 0}]]}, "Airtable Trigger": {"main": [[{"node": "Sentiment Transition", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Fetch Active Linear Issues", "type": "main", "index": 0}]]}, "For Each Issue...": {"main": [[], [{"node": "Copy of Issue", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Sentiment over Issue Comments", "type": "ai_languageModel", "index": 0}]]}, "Sentiment Transition": {"main": [[{"node": "Deduplicate Notifications", "type": "main", "index": 0}]]}, "Get Existing Sentiment": {"main": [[{"node": "Update Row", "type": "main", "index": 0}]]}, "Deduplicate Notifications": {"main": [[{"node": "Report Issue Negative Transition", "type": "main", "index": 0}]]}, "Combine Sentiment Analysis": {"main": [[{"node": "For Each Issue...", "type": "main", "index": 0}]]}, "Fetch Active Linear Issues": {"main": [[{"node": "Issues to List", "type": "main", "index": 0}]]}, "Sentiment over Issue Comments": {"main": [[{"node": "Combine Sentiment Analysis", "type": "main", "index": 0}]]}}}