{"meta": {"instanceId": "95b3ab5a70ab1c8c1906357a367f1b236ef12a1409406fd992f60255f0f95f85"}, "nodes": [{"id": "8f3fd206-b47f-4eae-a968-dc44ac0e6976", "name": "Convert and set quiz data", "type": "n8n-nodes-base.set", "notes": "This node formats the data received from the Jotform submission, ensuring it is correctly formatted for further processing at the KlickTipp API endpoint.", "position": [-1160, 680], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "f1263cb6-654a-4d07-9073-c015b720e6b7", "name": "mobile_number", "type": "string", "value": "={{ \n// Converts a phone number to numeric-only format with international code prefixed by \"00\"\n$json.Mobilrufnummer \n    ? $json.Mobilrufnummer\n        .replace(/^\\+/, '00')   // Replace leading \"+\" with \"00\"\n        .replace(/[^0-9]/g, '') // Remove non-numeric characters\n    : ''\n}}"}, {"id": "b09cc146-e614-478a-8f33-324d813e0120", "name": "birthday", "type": "string", "value": "={{ \n// Converts a date to a UNIX timestamp (in seconds)\nMath.floor(\n    new Date($json.Geburtstag + 'T00:00:00').getTime() / 1000\n)\n}}"}, {"id": "1c455eb9-0750-4d69-9dab-390847a3d582", "name": "question1_klicktipp_use", "type": "string", "value": "={{ \n// Joins the values from the array into a comma-separated string\n$json['Wofür wird KlickTipp genutzt?'] \n    ? $json['Wofür wird KlickTipp genutzt?'].join(', ') \n    : '' \n}}"}, {"id": "e375b10b-b05f-413e-93ed-b835e009dd91", "name": "question3_amount_cht_members", "type": "string", "value": "={{\n// Multiplies the decimalnumber value by 100\n$json['Wie viele Mitarbeiter hat das KlickTipp Customer Happiness Team?'] * 100 }}"}]}}, "notesInFlow": true, "typeVersion": 3.4}, {"id": "c807913b-dd90-49a2-b4ad-9f56a261fa04", "name": "Subscribe contact in KlickTipp", "type": "n8n-nodes-klicktipp.klicktipp", "notes": "Adds the contact to <PERSON><PERSON><PERSON><PERSON><PERSON> using the transformed quiz data.", "position": [-940, 680], "parameters": {"email": "={{ $('New quiz sumbmission via Typeform').item.json['E-Mail Adresse'] }}", "fields": {"dataFields": [{"fieldId": "fieldFirstName", "fieldValue": "={{ $('New quiz sumbmission via Typeform').item.json.Vorname }}"}, {"fieldId": "fieldLastName", "fieldValue": "={{ $('New quiz sumbmission via Typeform').item.json.Nachname }}"}, {"fieldId": "fieldBirthday", "fieldValue": "={{ $json.birthday }}"}, {"fieldId": "field214474", "fieldValue": "={{ $('New quiz sumbmission via Typeform').item.json['LinkedIn URL'] }}"}, {"fieldId": "field214475", "fieldValue": "={{ $json.question1_klicktipp_use }}"}, {"fieldId": "field214476", "fieldValue": "={{ $('New quiz sumbmission via Typeform').item.json['Wo ist der Firmensitz der Klick-Tipp Limited?'] }}"}, {"fieldId": "field214477", "fieldValue": "={{ $json.question3_amount_cht_members }}"}]}, "listId": "358895", "resource": "subscriber", "operation": "subscribe", "smsNumber": "={{ $json.mobile_number }}"}, "credentials": {"klickTippApi": {"id": "K9JyBdCM4SZc1cXl", "name": "DEMO KlickTipp account"}}, "notesInFlow": true, "typeVersion": 2}, {"id": "55656b0f-6fb4-435c-82be-750b557384b4", "name": "New quiz sumbmission via Typeform", "type": "n8n-nodes-base.typeformTrigger", "notes": "Triggers the workflow when a new quiz submission is received on Type Form.", "position": [-1380, 680], "webhookId": "37b98062-04ab-49be-b0f7-0fee3841bbd6", "parameters": {"formId": "nRFO0o92"}, "credentials": {"typeformApi": {"id": "1AUCqB2W8UDCVKhX", "name": "Ricardo's Typeform account"}}, "notesInFlow": true, "typeVersion": 1.1}, {"id": "92cf733f-f655-4302-b092-94d33399c8bd", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-700, 900], "parameters": {"width": 860.*************, "height": 1166.************, "content": "### Introduction\nThis workflow facilitates seamless integration between Typeform and KlickTipp, automating the process of handling quiz responses. By transforming raw quiz data into a format compatible with KlickTipp’s API, it eliminates manual data entry and ensures accurate, consistent information. \n\n### Benefits\n- **Efficient lead generation**: Contacts from forms are automatically imported into KlickTipp and can be used immediately, saving time and increasing the conversion rate.\n- **Automated processes**: Experts can start workflows directly, such as welcome emails or course admissions, reducing administrative effort.\n- **Error-free data management**: The template ensures precise data mapping, avoids manual corrections, and reinforces a professional appearance.\n\n### Key Features\n- **Typeform Trigger**: Captures new quiz submissions, including user details and quiz responses.\n- **Data Processing and Transformation**:\n  - Formats phone numbers to numeric-only format with international prefixes.\n  - Converts dates (e.g., birthdays) to UNIX timestamps.\n  - Maps multiple-choice quiz answers to string values for API compatibility.\n  - Scales numeric quiz responses for tailored use cases.\n- **Subscriber Management in KlickTipp**: Adds or updates participants as subscribers in KlickTipp. Includes custom field mappings and tags, such as:\n  - Personal details (e.g., name, email, phone number, birthday).\n  - Quiz responses (e.g., intended usage of KlickTipp, company location, and team size).\n  - Contact segmentation: Creates new tags based on form submission if necessary and adds these dynamic tags as well as fixed tags to contacts.\n- **Error Handling**: Handles empty or malformed data gracefully, ensuring clean submissions to KlickTipp.\n\n### Setup Instructions\n1. Set up the Typeform and KlickTipp nodes in your n8n instance.\n2. Connect your Typeform webhook to capture quiz responses and authenticate your KlickTipp account.\n3. Create the necessary custom fields to match the data structure:\n4. Verify and customize field mappings in the workflow to align with your specific form and subscriber list setup.\n\n![Source example](https://mail.cdndata.io/user/images/kt1073234/share_link_TypeForms_fields.png#full-width)\n\n### Testing and Deployment\n1. Test the workflow by submitting a quiz through Typeform.\n2. Verify that the data is correctly processed and updated in KlickTipp.\n\n- **Customization**: Update field mappings within the KlickTipp nodes to ensure alignment with your specific account setup.  "}, "typeVersion": 1}, {"id": "81efd56c-43e7-4598-a9ab-e7578406b227", "name": "Get list of all existing tags", "type": "n8n-nodes-klicktipp.klicktipp", "notes": "This node fetches all tags that already exist in KlickTipp.", "position": [-500, 700], "parameters": {}, "credentials": {"klickTippApi": {"id": "K9JyBdCM4SZc1cXl", "name": "DEMO KlickTipp account"}}, "notesInFlow": true, "typeVersion": 2}, {"id": "4e2de2e8-e0df-476a-aa2e-ff4b00ce7037", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "notes": "This node merges the tags which are fetched via the form with the existing tags we requested in order to identify if new tags need to be created.", "position": [-80, 580], "parameters": {"mode": "combineBySql", "query": "SELECT \n    input1.tags AS name,  -- Extracts the tag name from input1\n    IF(input2.value IS NOT NULL, true, false) AS exist, -- Checks if the tag exists in input2 (returns true if found, false otherwise)\n    input2.id AS tag_id  -- Retrieves the ID of the tag from input2 if it exists, otherwise returns NULL\nFROM \n    input1\nLEFT JOIN \n    input2 \nON \n    input1.tags = input2.value  -- Matches tags from input1 with values in input2"}, "notesInFlow": true, "typeVersion": 3}, {"id": "fd4b0ed3-08cb-4e6b-8538-1fe7a391bd25", "name": "Define Array of tags from Typeform", "type": "n8n-nodes-base.set", "notes": "This node defines tags based on the form submission, such as the webinar selection, date, and reminder interval, and saves them as an array for further processing.", "position": [-500, 500], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "814576c1-ba16-4546-9815-2b7dec324f94", "name": "tags", "type": "array", "value": "={{ \n  Array.from([\n    // Every line represents one of the dynamic values that are received from the form submission.\n    // These values are extracted from Typeform responses.\n    $('New quiz sumbmission via Typeform').item.json['Wofür wird KlickTipp genutzt?'],\n    $('New quiz sumbmission via Typeform').item.json['Wo ist der Firmensitz der Klick-Tipp Limited?'],\n    $('New quiz sumbmission via Typeform').item.json['Wie viele Mitarbeiter hat das KlickTipp Customer Happiness Team?']\n  ].flat()) // .flat() ensures that any nested arrays are merged into a single-level array.\n}}"}]}}, "notesInFlow": true, "typeVersion": 3.4}, {"id": "feab2eb3-28b8-4aa5-87b4-999c144fbdeb", "name": "Split Out Typeform tags", "type": "n8n-nodes-base.splitOut", "notes": "In this node we split the created array again into items so we can merge them with the existing tags we request from KlickTipp.", "position": [-320, 500], "parameters": {"options": {}, "fieldToSplitOut": "tags"}, "notesInFlow": true, "typeVersion": 1}, {"id": "0073c5fb-3eb1-4eab-b572-dce0161afaf1", "name": "Tag creation check", "type": "n8n-nodes-base.if", "notes": "This node checks the result of the tag comparison and branches the workflow accordingly in order to directly tag the contact or to create the tag first and to then follow through with the tagging.", "position": [140, 580], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "d9567816-9236-434d-b46e-e47f4d36f289", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.exist }}", "rightValue": ""}]}}, "notesInFlow": true, "typeVersion": 2.2}, {"id": "2d6bb138-7b5e-4e51-b18a-cfbec85396d2", "name": "Create the tag in KlickTipp", "type": "n8n-nodes-klicktipp.klicktipp", "notes": "Creates a new tag in KlickTipp if it does not already exist.", "position": [440, 660], "parameters": {"name": "={{ $json.name }}", "operation": "create"}, "credentials": {"klickTippApi": {"id": "K9JyBdCM4SZc1cXl", "name": "DEMO KlickTipp account"}}, "notesInFlow": true, "typeVersion": 2}, {"id": "9045b890-07c3-4432-a900-6296e49904d3", "name": "Aggregate tags to add to contact", "type": "n8n-nodes-base.aggregate", "notes": "This node aggregates all IDs of the existing tags to a list.", "position": [460, 460], "parameters": {"options": {}, "fieldsToAggregate": {"fieldToAggregate": [{"renameField": true, "outputFieldName": "tag_ids", "fieldToAggregate": "tag_id"}]}}, "notesInFlow": true, "typeVersion": 1}, {"id": "e9217f44-f004-4460-87ad-fc0fbd63624c", "name": "Tag contact directly in KlickTipp", "type": "n8n-nodes-klicktipp.klicktipp", "notes": "Applies existing tags to a subscriber in KlickTipp. This enables the use of specific signatures, sign out automations as well as the automation of emails and campaigns or other automations.", "position": [720, 460], "parameters": {"email": "={{ $('New quiz sumbmission via Typeform').item.json['E-Mail Adresse'] }}", "tagId": "={{$json.tag_ids}}", "resource": "contact-tagging"}, "credentials": {"klickTippApi": {"id": "K9JyBdCM4SZc1cXl", "name": "DEMO KlickTipp account"}}, "notesInFlow": true, "typeVersion": 2}, {"id": "031ffca6-c94d-484f-b798-1beeb62a6ea5", "name": "Aggregate array of created tags", "type": "n8n-nodes-base.aggregate", "notes": "This node aggregates all IDs of the newly created tags to a list.", "position": [640, 660], "parameters": {"options": {}, "fieldsToAggregate": {"fieldToAggregate": [{"renameField": true, "outputFieldName": "tag_ids", "fieldToAggregate": "id"}]}}, "notesInFlow": true, "typeVersion": 1}, {"id": "bedf795b-0dbf-4d57-b0db-7d3bfaaffbaf", "name": "Tag contact KlickTipp after trag creation", "type": "n8n-nodes-klicktipp.klicktipp", "notes": "Associates a specific tag with a subscriber in KlickTipp using their email address. This enables the use of specific signatures, signout automations as well as the automation of emails and campaigns or other automations.", "position": [840, 660], "parameters": {"email": "={{ $('New quiz sumbmission via Typeform').item.json['E-Mail Adresse'] }}", "tagId": "={{$json.tag_ids}}", "resource": "contact-tagging"}, "credentials": {"klickTippApi": {"id": "K9JyBdCM4SZc1cXl", "name": "DEMO KlickTipp account"}}, "notesInFlow": true, "typeVersion": 2}], "pinData": {}, "connections": {"Merge": {"main": [[{"node": "Tag creation check", "type": "main", "index": 0}]]}, "Tag creation check": {"main": [[{"node": "Aggregate tags to add to contact", "type": "main", "index": 0}], [{"node": "Create the tag in KlickTipp", "type": "main", "index": 0}]]}, "Split Out Typeform tags": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Convert and set quiz data": {"main": [[{"node": "Subscribe contact in KlickTipp", "type": "main", "index": 0}]]}, "Create the tag in KlickTipp": {"main": [[{"node": "Aggregate array of created tags", "type": "main", "index": 0}]]}, "Get list of all existing tags": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Subscribe contact in KlickTipp": {"main": [[{"node": "Define Array of tags from Typeform", "type": "main", "index": 0}, {"node": "Get list of all existing tags", "type": "main", "index": 0}]]}, "Aggregate array of created tags": {"main": [[{"node": "Tag contact KlickTipp after trag creation", "type": "main", "index": 0}]]}, "Aggregate tags to add to contact": {"main": [[{"node": "Tag contact directly in KlickTipp", "type": "main", "index": 0}]]}, "New quiz sumbmission via Typeform": {"main": [[{"node": "Convert and set quiz data", "type": "main", "index": 0}]]}, "Define Array of tags from Typeform": {"main": [[{"node": "Split Out Typeform tags", "type": "main", "index": 0}]]}}}