{"nodes": [{"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [100, 160], "parameters": {"triggerTimes": {"item": [{"hour": 5, "mode": "everyWeek"}]}}, "typeVersion": 1}, {"name": "MySQL - insert", "type": "n8n-nodes-base.mySql", "position": [500, 160], "parameters": {"table": "books", "columns": "title, price", "options": {"ignore": true, "priority": "LOW_PRIORITY"}}, "credentials": {"mySql": {"id": "82", "name": "MySQL account"}}, "typeVersion": 1}, {"name": "Google Sheets - read", "type": "n8n-nodes-base.googleSheets", "position": [300, 160], "parameters": {"options": {}, "sheetId": "qwertz", "authentication": "oAuth2"}, "credentials": {"googleSheetsOAuth2Api": {"id": "2", "name": "google_sheets_oauth"}}, "typeVersion": 1}], "connections": {"Cron": {"main": [[{"node": "Google Sheets - read", "type": "main", "index": 0}]]}, "Google Sheets - read": {"main": [[{"node": "MySQL - insert", "type": "main", "index": 0}]]}}}