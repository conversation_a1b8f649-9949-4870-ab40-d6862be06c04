{"nodes": [{"id": "3890c4a4-0649-457f-9dd7-1e688f4a0024", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [320, 160], "parameters": {"color": 5, "width": 731, "height": 210.61602497398542, "content": "### 👨‍🎤 Setup\n1. Add you **<PERSON><PERSON>udu**, **Hunter**, and **Gmail** credentials \n2. Setup your **HubSpot** Oauth2 creds using [n8n docs](https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.hubspottrigger/)\n3. Set the email content and subject\n4. Click the Test Workflow button, enter your email and check the Slack channel\n5. Activate the workflow and use the form trigger production URL to collect your leads in a smart way "}, "typeVersion": 1}, {"id": "77a064c2-852b-4526-aa8b-9a25d3a56844", "name": "n8n Form Trigger", "type": "n8n-nodes-base.formTrigger", "position": [380, 420], "webhookId": "09f63412-7c4a-4752-93cd-ff1c87774226", "parameters": {"path": "0bf8840f-1cc4-46a9-86af-a3fa8da80608", "options": {}, "formTitle": "Contact us", "formFields": {"values": [{"fieldLabel": "What's your business email?"}]}, "formDescription": "We'll get back to you soon"}, "typeVersion": 2}, {"id": "9e0ea519-0022-4b9b-853a-6d627f2d427b", "name": "Check if the email is valid", "type": "n8n-nodes-base.if", "position": [800, 420], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "54d84c8a-63ee-40ed-8fb2-301fff0194ba", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "valid"}]}}, "typeVersion": 2}, {"id": "431c30eb-f9c8-4487-b75c-ee5fcf51a401", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [380, 560], "parameters": {"color": 7, "width": 162, "height": 139, "content": "👆 You can exchange this with any form you like (*e.g. Typeform, Google forms, Survey Monkey...*)"}, "typeVersion": 1}, {"id": "eed9a59d-f0a7-4c0c-a36d-1258580f4fac", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1360, 500], "parameters": {"color": 7, "width": 162, "height": 84, "content": "👆 Adjust the fit as you see necessary"}, "typeVersion": 1}, {"id": "e57ed336-8c89-453a-8632-1ea6ec6c5617", "name": "Email is not valid, do nothing", "type": "n8n-nodes-base.noOp", "position": [1140, 560], "parameters": {}, "typeVersion": 1}, {"id": "7588462f-7e6c-4273-bead-9ae3d362e341", "name": "Score lead with <PERSON><PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.httpRequest", "position": [1140, 320], "parameters": {"url": "=https://api.madkudu.com/v1/persons?email={{ $json.email }}", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "71W5Bt9g1G9GOhVL", "name": "<PERSON><PERSON><PERSON><PERSON> Lead score"}}, "typeVersion": 4.1}, {"id": "eac6b41d-fe4f-4cf4-92f5-6a00dcb68c4d", "name": "Verify email with <PERSON>", "type": "n8n-nodes-base.hunter", "position": [600, 420], "parameters": {"email": "={{ $json['What\\'s your business email?'] }}", "operation": "emailVerifier"}, "credentials": {"hunterApi": {"id": "ecwmdHFSBU5GGnV1", "name": "Hunter account"}}, "typeVersion": 1}, {"id": "51c86cbb-2a43-4ced-9787-6c95db62f390", "name": "Not interesting enough", "type": "n8n-nodes-base.noOp", "position": [1660, 460], "parameters": {}, "typeVersion": 1}, {"id": "9f0daa67-6685-44b5-a802-8b70f8b91985", "name": "if customer fit score > 60", "type": "n8n-nodes-base.if", "position": [1380, 320], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "c23d7b34-a4ae-421f-bd7a-6a3ebb05aafe", "operator": {"type": "number", "operation": "gt"}, "leftValue": "={{ $json.properties.customer_fit.score }}", "rightValue": 60}]}}, "typeVersion": 2}, {"id": "57bf0a97-bf58-43cc-8ab5-e3f298eac5f6", "name": "Send outreach email", "type": "n8n-nodes-base.gmail", "position": [2100, 120], "parameters": {"sendTo": "={{ $json.to }}", "message": "={{ $json.html }}", "options": {"senderName": "<PERSON><PERSON><PERSON> from n8n", "appendAttribution": false}, "subject": "={{ $json.subject }}"}, "credentials": {"gmailOAuth2": {"id": "rd2agqPeJBD2377h", "name": "Work Gmail"}}, "typeVersion": 2.1}, {"id": "a93ee7c1-4b77-45ed-9eb8-2757fbeb2b78", "name": "Set keys", "type": "n8n-nodes-base.set", "position": [1840, 260], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "f3ecc873-2d60-4f2d-bc40-81f9379c725b", "name": "html", "type": "string", "value": "=Hello,\n\nThank you for filling out our form. We are excited to be able to help you.\n\nFeel free to schedle a 30-minute call with me here: www.calendly.com/schedule/mutasem\n\n<PERSON>,\n\nMutasem"}, {"id": "9f4f5b68-984b-415e-a110-a35ded22dd41", "name": "subject", "type": "string", "value": "Why n8n?"}, {"id": "5362aa67-f3fa-4a6e-b6e8-4c50cc7a3192", "name": "to", "type": "string", "value": "={{ $json.properties.email.value }}"}, {"id": "ec4ccaef-374f-4fbc-9dda-3b743a00a146", "name": "id", "type": "number", "value": "={{ $json.vid }}"}]}}, "typeVersion": 3.3}, {"id": "f04e7151-b4c9-4294-8e3e-24830e601d6b", "name": "Record engagement in HubSpot", "type": "n8n-nodes-base.hubspot", "position": [2100, 420], "parameters": {"type": "email", "metadata": {"html": "={{ $json.html }}", "subject": "={{ $json.subject }}", "toEmail": ["={{ $json.to }}"], "firstName": "Mu<PERSON><PERSON>", "fromEmail": "<EMAIL>"}, "resource": "engagement", "authentication": "oAuth2", "additionalFields": {"associations": {"contactIds": "={{ $json.id }}"}}}, "credentials": {"hubspotOAuth2Api": {"id": "Gxwfj6z9NwaEC3P5", "name": "HubSpot account 3"}}, "typeVersion": 2}, {"id": "92bd62d3-7180-4da6-a52b-31048b48b983", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1820, 160], "parameters": {"color": 7, "width": 162, "height": 84, "content": "👇🏽  Change the content here"}, "typeVersion": 1}, {"id": "cab7bcd9-67af-4cf0-988d-fb2b37b3db8f", "name": "HubSpot", "type": "n8n-nodes-base.hubspot", "position": [1620, 180], "parameters": {"email": "={{ $json.email }}", "options": {}, "authentication": "oAuth2", "additionalFields": {}}, "credentials": {"hubspotOAuth2Api": {"id": "Gxwfj6z9NwaEC3P5", "name": "HubSpot account 3"}}, "typeVersion": 2}], "pinData": {"n8n Form Trigger": [{"formMode": "test", "submittedAt": "2024-02-22T13:59:54.709Z", "What's your business email?": "<EMAIL>"}]}, "connections": {"HubSpot": {"main": [[{"node": "Set keys", "type": "main", "index": 0}]]}, "Set keys": {"main": [[{"node": "Send outreach email", "type": "main", "index": 0}, {"node": "Record engagement in HubSpot", "type": "main", "index": 0}]]}, "n8n Form Trigger": {"main": [[{"node": "Verify email with <PERSON>", "type": "main", "index": 0}]]}, "Score lead with MadKudu": {"main": [[{"node": "if customer fit score > 60", "type": "main", "index": 0}]]}, "Verify email with Hunter": {"main": [[{"node": "Check if the email is valid", "type": "main", "index": 0}]]}, "if customer fit score > 60": {"main": [[{"node": "HubSpot", "type": "main", "index": 0}], [{"node": "Not interesting enough", "type": "main", "index": 0}]]}, "Check if the email is valid": {"main": [[{"node": "Score lead with <PERSON><PERSON><PERSON><PERSON>", "type": "main", "index": 0}], [{"node": "Email is not valid, do nothing", "type": "main", "index": 0}]]}}}