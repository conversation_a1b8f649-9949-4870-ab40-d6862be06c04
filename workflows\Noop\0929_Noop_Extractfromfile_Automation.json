{"id": "39KuujB1fbOvx8Al", "meta": {"instanceId": "0a5638e14e0c728ef975d18d109cfb41edae575e3d911724f4f1eccde06a729f"}, "name": "OpenAI e-mail classification - application", "tags": [], "nodes": [{"id": "6156844f-d1ba-413d-9ab2-02148bef5bf0", "name": "Email trigger", "type": "n8n-nodes-base.emailReadImap", "position": [-440, 120], "parameters": {"format": "resolved", "options": {}, "postProcessAction": "nothing", "dataPropertyAttachmentsPrefixName": "attachment"}, "credentials": {"imap": {"id": "il5dS1iQxJvOMWbE", "name": "IMAP account"}}, "typeVersion": 2}, {"id": "1aedaa56-d988-469b-86b9-61d50e707950", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [0, 0], "parameters": {"height": 200, "content": "### Change or add any category you want\nEach category can be assigned it's own specific workflow"}, "typeVersion": 1}, {"id": "d41ba844-2b99-42bb-80df-cff1b97dcbb9", "name": "Classify email", "type": "@n8n/n8n-nodes-langchain.textClassifier", "position": [0, 120], "parameters": {"options": {}, "inputText": "={{ $('Email trigger').first().json.text }}\n\nattachment:\n{{ $('Extract data from attachment').first().json.text }}\n", "categories": {"categories": [{"category": "job_application", "description": "for job applications"}, {"category": "inbound_lead", "description": "for sales inquiries or requests for more information about our products/services"}, {"category": "invoice", "description": "for invoices"}, {"category": "other", "description": "for all other sorts of emails"}]}}, "typeVersion": 1}, {"id": "b63a864f-f968-4e7e-9da4-d704f3ffa022", "name": "Extract variables - email & attachment", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [440, 20], "parameters": {"text": "={{ $('Email trigger').first().json.text }}\n\nResume:\n{{ $('Extract data from attachment').first().json.text }}\n", "options": {}, "attributes": {"attributes": [{"name": "first_name", "description": "first name of the applicant"}, {"name": "last_name", "description": "last name of the applicant"}, {"name": "age", "description": "age of the applicant"}, {"name": "residence", "description": "residence of the applicant"}, {"name": "study", "description": "relevant completed study of the applicant"}, {"name": "work_experience", "description": "relevant work experience of the applicant"}, {"name": "personal_character", "description": "personal characteristics of the applicant"}]}}, "typeVersion": 1}, {"id": "398b9240-0d9c-416e-af3b-31ba9e1ac9b2", "name": "Extract data from attachment", "type": "n8n-nodes-base.extractFromFile", "onError": "continueRegularOutput", "position": [-220, 120], "parameters": {"options": {}, "operation": "pdf", "binaryPropertyName": "attachment0"}, "typeVersion": 1, "alwaysOutputData": false}, {"id": "9f949aac-1681-4f04-983e-8bd5c949987a", "name": "OpenAI Chat Model 2", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [660, 200], "parameters": {"model": "gpt-4o", "options": {}}, "credentials": {"openAiApi": {"id": "by5xbXU1Yz36JahE", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "c7a61afe-d68d-407e-8653-46cb123877e9", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [100, 320], "parameters": {"model": "gpt-4o", "options": {}}, "credentials": {"openAiApi": {"id": "by5xbXU1Yz36JahE", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "5a22e81b-8b60-443e-985b-47d493724389", "name": "Workflow 2", "type": "n8n-nodes-base.noOp", "position": [440, 180], "parameters": {}, "typeVersion": 1}, {"id": "808e4f35-604e-4354-ab8b-3ba68940016b", "name": "Workflow 3", "type": "n8n-nodes-base.noOp", "position": [600, 360], "parameters": {}, "typeVersion": 1}, {"id": "d793675d-c68d-4f73-a99d-6451be5bea30", "name": "workflow 4", "type": "n8n-nodes-base.noOp", "position": [440, 360], "parameters": {}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"errorWorkflow": "rkMQmtrfcSF3XpMF", "executionOrder": "v1"}, "versionId": "28448ab7-6d45-41df-9de3-aad0e187edc5", "connections": {"Email trigger": {"main": [[{"node": "Extract data from attachment", "type": "main", "index": 0}]]}, "Classify email": {"main": [[{"node": "Extract variables - email & attachment", "type": "main", "index": 0}], [{"node": "Workflow 2", "type": "main", "index": 0}], [{"node": "Workflow 3", "type": "main", "index": 0}], [{"node": "workflow 4", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Classify email", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model 2": {"ai_languageModel": [[{"node": "Extract variables - email & attachment", "type": "ai_languageModel", "index": 0}]]}, "Extract data from attachment": {"main": [[{"node": "Classify email", "type": "main", "index": 0}]]}, "Extract variables - email & attachment": {"main": [[]]}}}