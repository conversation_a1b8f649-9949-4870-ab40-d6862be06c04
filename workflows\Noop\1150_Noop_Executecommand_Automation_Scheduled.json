{"id": "81", "name": "Execute a command that gives the hard disk memory used on the host machine", "nodes": [{"name": "Execute Command", "type": "n8n-nodes-base.executeCommand", "position": [670, 300], "parameters": {"command": "df -k / | tail -1 | awk '{print $5}'"}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [470, 300], "parameters": {"triggerTimes": {"item": [{"hour": 9}, {"hour": 16}]}}, "typeVersion": 1}, {"name": "IF", "type": "n8n-nodes-base.if", "position": [870, 300], "parameters": {"conditions": {"number": [{"value1": "={{parseInt($node[\"Execute Command\"].json[\"stdout\"])}}", "value2": 80, "operation": "larger"}], "string": []}}, "typeVersion": 1}, {"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.twilio", "position": [1070, 200], "parameters": {"to": "+12345", "from": "+123", "message": "=Your hard disk space is filling up fast! Your hard disk is {{$node[\"Execute Command\"].json[\"stdout\"]}} full."}, "credentials": {"twilioApi": "twilio-credentials"}, "typeVersion": 1}, {"name": "NoOp", "type": "n8n-nodes-base.noOp", "position": [1070, 400], "parameters": {}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"IF": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}], [{"node": "NoOp", "type": "main", "index": 0}]]}, "Cron": {"main": [[{"node": "Execute Command", "type": "main", "index": 0}]]}, "Execute Command": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}}}