{"id": "184", "name": "Send updates about the position of the ISS every minute to a topic in RabbitMQ", "nodes": [{"name": "RabbitMQ", "type": "n8n-nodes-base.rabbitmq", "position": [1300, 540], "parameters": {"queue": "iss-position", "options": {}}, "credentials": {"rabbitmq": "RabbitMQ Credentials"}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [1110, 540], "parameters": {"values": {"number": [{"name": "Latitude", "value": "={{$node[\"HTTP Request\"].json[\"0\"][\"latitude\"]}}"}, {"name": "Longitude", "value": "={{$node[\"HTTP Request\"].json[\"0\"][\"longitude\"]}}"}, {"name": "Timestamp", "value": "={{$node[\"HTTP Request\"].json[\"0\"][\"timestamp\"]}}"}], "string": [{"name": "Name", "value": "={{$node[\"HTTP Request\"].json[\"0\"][\"name\"]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [910, 540], "parameters": {"url": "https://api.wheretheiss.at/v1/satellites/25544/positions", "options": {}, "queryParametersUi": {"parameter": [{"name": "timestamps", "value": "={{Date.now();}}"}]}}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [710, 540], "parameters": {"triggerTimes": {"item": [{"mode": "everyMinute"}]}}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Set": {"main": [[{"node": "RabbitMQ", "type": "main", "index": 0}]]}, "Cron": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}}