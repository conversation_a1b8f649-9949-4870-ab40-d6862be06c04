{"meta": {"instanceId": "6a5e68bcca67c4cdb3e0b698d01739aea084e1ec06e551db64aeff43d174cb23"}, "nodes": [{"id": "53b36910-966f-45ba-a425-a3260a55059f", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [340, 480], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "typeVersion": 1.2}, {"id": "177235e8-c925-43d0-9695-10f072e26350", "name": "AI Control Tower Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [380, 240], "parameters": {"options": {"systemMessage": "=You are an AI-powered SQL assistant specialized in supply chain analytics. \nYour role is to execute SQL queries on BigQuery and return only the results in a structured format.\n\nToday we are May 31, 2021.\n\n### **Behavior & Rules**\n1️⃣ **Query Execution:**\n   - Your only task is to process user requests and return **direct results** from BigQuery.\n   - Do **not** display the SQL query.\n   - Only return structured **data** as output.\n\n2️⃣ **Data Presentation:**\n   - Format the results as a **table** whenever possible.\n   - If results are numerical (counts, percentages, aggregates), return them **clearly and concisely**.\n   - If results contain multiple rows, return **only the first 10** for preview, unless the user specifies otherwise.\n\n3️⃣ **Handling Large Datasets:**\n   - If the user asks for many rows, show the first **100 rows max** unless specified.\n   - Provide a **summary** when dealing with large data instead of showing everything.\n\n4️⃣ **Response Format:**\n   - ✅ **For counts & metrics:**  \n     `\"There were 5,432 delayed shipments in the last 21 days.\"`\n   - ✅ **For tables:**  \n     | ShipmentID | City  | Store  | Order Date | Delivery Date | On Time? |\n     |-----------|-------|--------|------------|--------------|----------|\n     | 12345     | NYC   | ST1    | 2024-03-10 | 2024-03-15   | No       |\n     | 67890     | Paris | ST4    | 2024-03-11 | 2024-03-16   | Yes      |\n\n5️⃣ **Clarifying Unclear Requests:**\n   - If the user request is **too broad**, ask for clarification instead of running an expensive query.\n\n---\n\n### Schema Awareness\nAll SQL queries must use the BigQuery table:  \n`transport.shipments`  \n\nThis table includes fields such as:\n- `Shipment ID`, `City`, `Store`, `Order Date`, `Delivery Date`, `On Time Delivery`\n- As well as operational timestamps: `Transmission`, `Loading`, `Airport Arrival`, etc.\n- And status flags: `Transmission OnTime`, `Loading OnTime`, `Airport OnTime`, `Store Open`\n\nUse these fields appropriately when analyzing shipment performance.\n\n---\n\n### Tool Usage Instruction (for \"bigquery_tool\")\n\nWhenever you need to run a SQL query, use the tool called `bigquery_tool`.\n\nYou must provide the query in the following format:\n```json\n{\n  \"query\": \"SELECT COUNT(*) FROM `transport.shipments` WHERE `On Time Delivery` = FALSE\"\n}\n"}}, "typeVersion": 1.8}, {"id": "5366cc5f-85d3-44d2-9b1b-62febfcb44e3", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-100, -120], "parameters": {"color": 7, "width": 200, "height": 520, "content": "### 1. Workflow Trigger with Chat\nThis workflow uses a simple chat window as a trigger. You can replace it with Telegram, Slack, Teams or a webhook trigger linked to your chat.\n\n#### How to setup?\n*Nothing to do.*\n"}, "typeVersion": 1}, {"id": "4218a062-12f8-437d-ab22-5a653a3089b2", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [140, -120], "parameters": {"color": 7, "width": 700, "height": 740, "content": "### 2. AI Agent equipped with the query tool\nIn order to have more control on the input of the BigQuery node, we don't use the BigQuery tool. Instead we have a set of nodes to retrieve the SQL query, clean it and send it to a BigQuery Node.\n\n#### How to setup?\n- **AI Agent with the Chat Model**:\n   1. Add a **chat model** with the required credentials *(Example: Open AI 4o-mini)*\n   2. Adapt the **name of your BigQuery table** in the system prompt *(Example: transports.shipments)*\n   3. Adapt the **tables fields explanation** in the system prompt\n  [Learn more about the AI Agent Node](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.agent)\n- Copy and past the **nodes in the yellow sticker** in another workflow. Point the query tool to this workflow.\n[Learn more about the Custom n8n Workflow Tool node](https://docs.n8n.io/integrations/builtin/cluster-nodes/sub-nodes/n8n-nodes-langchain.toolworkflow)"}, "typeVersion": 1}, {"id": "c5967f58-00e8-4f03-9110-913547f7ab9c", "name": "Call Query Tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [640, 440], "parameters": {"name": "bigquery_tool", "workflowId": {"__rl": true, "mode": "list", "value": "4Os7DoxHjFuTwWio", "cachedResultName": "🔨 Big Query Tool"}, "description": "=Use this tool to run an SQL query and fetch the result from the BigQuery database.\n\nThe tool expects input in the following format:\n{\n  \"query\": \"SELECT COUNT(*) FROM `transport.shipments` WHERE `On Time Delivery` = FALSE\"\n}\n\nOnly provide the SQL query as a string inside the 'query' key. Do not include code formatting (like ```sql), comments, or explanations. The tool will return only the raw result from the database.\n", "workflowInputs": {"value": {"query": "={{ $fromAI(\"query\", \"SQL query to run\") }}"}, "schema": [{"id": "query", "type": "string", "display": true, "removed": false, "required": false, "displayName": "query", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["query"], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2}, {"id": "429813c8-b07f-4551-aeea-1744a1225449", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [900, -120], "parameters": {"width": 760, "height": 460, "content": "### 3. Big Query Workflow\nExecute the SQL query generated by the AI agent in Big Query. Retrieve the results and send them back to the AI Agent.\n\n### How to set up?\n- Paste these nodes in a separate workflow so you can use it with multiple agents.\n- **Google BigQuery API**:\n   1. Add your Google Translate API credentials\n   2. The project in which your table is located\n  [Learn more about the Google BigQuery Node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googlebigquery)\n"}, "typeVersion": 1}, {"id": "bede0624-8923-4af0-8adc-8be22d556066", "name": "Query Database", "type": "n8n-nodes-base.googleBigQuery", "position": [1520, 180], "parameters": {"options": {}, "sqlQuery": "={{ $json.query }}", "projectId": {"__rl": true, "mode": "list", "value": "=", "cachedResultUrl": "=", "cachedResultName": "="}}, "notesInFlow": true, "typeVersion": 2.1}, {"id": "137e4dbc-db8d-4ec7-a3e0-478dde6ef27c", "name": "<PERSON>gger Executed by the AI Tool", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [960, 180], "parameters": {"workflowInputs": {"values": [{"name": "query"}]}}, "typeVersion": 1.1}, {"id": "42a2801e-582e-4340-83af-ef0041eab4f9", "name": "Sanitising the Query", "type": "n8n-nodes-base.code", "position": [1240, 180], "parameters": {"jsCode": "return [\n  {\n    json: {\n      query: $input.first().json.query.replace(/```sql|```/g, \"\").trim()\n    }\n  }\n];\n"}, "typeVersion": 2}, {"id": "7c86fda0-116c-47ad-aaf5-8b83d2c083c6", "name": "Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [480, 480], "parameters": {}, "typeVersion": 1.3}, {"id": "e1408ac1-24da-4d38-8fdf-c110a54d3f55", "name": "Chat with the User", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-60, 240], "webhookId": "ee7c418b-d7d6-41f9-8e87-0f71b8ae1cf9", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "bc49829b-45f2-4910-9c37-907271982f14", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [900, 380], "parameters": {"width": 780, "height": 540, "content": "### 4. Do you need more details?\nFind a step-by-step guide in this tutorial\n![Guide](https://www.samirsaci.com/content/images/2025/04/image.png)\n[🎥 Watch My Tutorial](https://www.loom.com/share/50271f9d50214d7184830985497a75ec?sid=d0c410dc-29f1-488f-b89a-4011de0ded07)"}, "typeVersion": 1}], "pinData": {}, "connections": {"Chat Memory": {"ai_memory": [[{"node": "AI Control Tower Agent", "type": "ai_memory", "index": 0}]]}, "Call Query Tool": {"ai_tool": [[{"node": "AI Control Tower Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Control Tower Agent", "type": "ai_languageModel", "index": 0}]]}, "Chat with the User": {"main": [[{"node": "AI Control Tower Agent", "type": "main", "index": 0}]]}, "Sanitising the Query": {"main": [[{"node": "Query Database", "type": "main", "index": 0}]]}, "Trigger Executed by the AI Tool": {"main": [[{"node": "Sanitising the Query", "type": "main", "index": 0}]]}}}