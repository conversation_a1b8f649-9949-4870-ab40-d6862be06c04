{"meta": {"instanceId": "38d37c49298b42c645e6a7693766d7c3522b24e54454034f955422b5d7af611c"}, "nodes": [{"id": "dd9e2f9c-225a-4b6b-9904-293206a477e4", "name": "Get advice articles from a Google search", "type": "n8n-nodes-base.httpRequest", "position": [1040, 360], "parameters": {"url": "=https://www.google.com/search?q=site%3Alinkedin.com%2Fadvice+{{ $json.Topic }}", "options": {"batching": {"batch": {"batchSize": 25}}}}, "typeVersion": 4.2}, {"id": "0e2bcaeb-65a0-400a-a15e-0840723d8144", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [980, 320], "parameters": {"color": 2, "width": 621.7044818991839, "height": 566.8592254014303, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n## 1. Get advice articles from a Google search**\nUses an HTTP request to perform a Google search for LinkedIn advice articles based on a predefined query.\n\n## 2. Extract Article links for LinkedIn advice articles\nThis Code node extracts LinkedIn article URLs from the Google search results by using a regular expression. It pulls all article links related to LinkedIn advice.\n\n## 3. Split Out all links for LinkedIn advice articles\nSplits the list of extracted LinkedIn article links into individual items. This allows each article to be processed one at a time in the following steps.\n"}, "typeVersion": 1}, {"id": "68eefc93-6c82-4687-bb4d-52345e5a5094", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [520, 80], "parameters": {}, "typeVersion": 1}, {"id": "af3fdd03-d28a-4f49-b213-8202b1d154df", "name": "Merge data and keep unique google search results", "type": "n8n-nodes-base.merge", "position": [1700, 200], "parameters": {"mode": "combine", "options": {}, "joinMode": "keepNonMatches", "mergeByFields": {"values": [{"field1": "URL", "field2": "matches"}]}, "outputDataFrom": "input2"}, "typeVersion": 2.1}, {"id": "632c54cc-b1d7-4034-93bf-82dd206761f0", "name": "Extract Article links for LinkedIn advice articles", "type": "n8n-nodes-base.code", "position": [1240, 360], "parameters": {"jsCode": "// n8n Code node script\nconst text = $json.data;\n\n// Define the regex pattern\nconst regexPattern = /https:\\/\\/www\\.linkedin\\.com\\/advice\\/[^%&\\s\"']+/g;\n\n// Execute the regex pattern on the text\nconst matches = text.match(regexPattern);\n\n// Output the matches\nreturn {\n  matches: matches || []\n};\n\n\n"}, "typeVersion": 2}, {"id": "81f0a962-fef8-4a46-a709-21cc2db02e55", "name": "Split Out all links for LinkedIn advice articles", "type": "n8n-nodes-base.splitOut", "position": [1440, 360], "parameters": {"options": {}, "fieldToSplitOut": "matches"}, "typeVersion": 1}, {"id": "65e4efa0-c746-4e77-9ccb-01c8afc5860c", "name": "Schedule Trigger Every Monday, @ 08:00am", "type": "n8n-nodes-base.scheduleTrigger", "position": [520, 280], "parameters": {"rule": {"interval": [{"field": "weeks", "triggerAtDay": [1], "triggerAtHour": 8}]}}, "typeVersion": 1.2}, {"id": "86fe3695-c1fd-4154-b1ba-f0737406da4a", "name": "LinkedIn Contribution Writer", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [2360, 200], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {"temperature": 0.7}, "messages": {"values": [{"content": "=Read the following collaborative article and provide your own helpful collaboration. The article has various topics that each need to be answered. Write me a paragraph of helpful advice for each topic and format your response as outlined in the template below.\n\n-------------\nARTICLE TITLE\n{{ $json.ArticleTitle }}\n\nTOPICS WITHIN THE LINKEDIN ARTICLE:\n{{ $json.ArticleTopics }}\n\nOTHER CONTRIBUTIONS TO THE LINKEDIN ARTICLE:\n{{ $json.ArticleContributions }}\n-------------\n\nYour advice must be unique and something that no one else has recommended before on the article, or in any of the topics. The response needs to be raw and genuine to elicit conversation and engagement.\n\nFormat your output in text and follow the template below. Only populate the template with as many topics as were provided in the original request \n\ni.e: if there were only 4 topics in the original request then only provide 4 pieces of advice:\n\nOUTPUT TEMPLATE\n\n1. [Topic #1 from Article]\n[Advice for Topic]\n\n2. [Topic #2 from Article]\n[Advice for Topic]\n\n3. [Topic #3 from Article]\n[Advice for Topic]\n\n4. [Topic #4 from Article]\n[Advice for Topic]\n\n5. [Topic #5 from Article]\n[Advice for Topic]\n\n6. [Topic #6 from Article]\n[Advice for Topic]"}]}}, "credentials": {"openAiApi": {"id": "t5MoHQt5nn0nWWnw", "name": "OpenAi Account (<EMAIL>)"}}, "typeVersion": 1.4}, {"id": "aaeba3e6-2d74-463a-8ba7-9f84826fee1b", "name": "Post new LinkedIn contributions to NocoDB (CreateRows)", "type": "n8n-nodes-base.nocoDb", "position": [3020, 200], "parameters": {"table": "mpagw9n92ran52o", "fieldsUi": {"fieldValues": [{"fieldName": "Post Title", "fieldValue": "={{ $('HTML extract LinkedIn article & other users contributions').item.json.ArticleTitle }}"}, {"fieldName": "URL", "fieldValue": "={{ $('Merge data and keep unique google search results').item.json.matches }}"}, {"fieldName": "Contribution", "fieldValue": "={{ $('LinkedIn Contribution Writer').item.json.message.content }}"}, {"fieldName": "Topic", "fieldValue": "Lead Generation"}, {"fieldName": "Person", "fieldValue": "<PERSON>"}]}, "operation": "create", "projectId": "psdqqm1bzphkodc", "authentication": "nocoDbApiToken"}, "credentials": {"nocoDbApiToken": {"id": "5PYJKB4ihzHtKLqx", "name": "NocoDB Account (<EMAIL>)"}}, "typeVersion": 3}, {"id": "4d6bca6e-2392-48c1-906f-ff5f439f4897", "name": "Post new LinkedIn contributions to Slack channel", "type": "n8n-nodes-base.slack", "position": [2740, 200], "parameters": {"text": "=↓ 📝 ARTICLE:\n{{ $('HTML extract LinkedIn article & other users contributions').item.json.ArticleTitle }}\n{{ $('Merge data and keep unique google search results').item.json.matches }}\n\n↓ 💡 ADVICE:\n{{ $json.message.content }}\n------------------------------------------------------", "select": "channel", "channelId": {"__rl": true, "mode": "list", "value": "C07CFN279HT", "cachedResultName": "cass-linkedin-advice"}, "otherOptions": {"mrkdwn": true, "unfurl_links": true, "includeLinkToWorkflow": false}, "authentication": "oAuth2"}, "credentials": {"slackOAuth2Api": {"id": "xkCA23zAF89RcovP", "name": "Slack Account (OAuth)  (<EMAIL>)"}}, "typeVersion": 2.2}, {"id": "ffc7984b-7199-421a-9fe1-8ffe2aa8e7b3", "name": "Get all LinkedIn contributions from database NocoDB (GetRows)", "type": "n8n-nodes-base.nocoDb", "position": [1240, 80], "parameters": {"table": "mpagw9n92ran52o", "options": {}, "operation": "getAll", "projectId": "psdqqm1bzphkodc", "returnAll": true, "authentication": "nocoDbApiToken"}, "credentials": {"nocoDbApiToken": {"id": "5PYJKB4ihzHtKLqx", "name": "NocoDB Account (<EMAIL>)"}}, "typeVersion": 3}, {"id": "a9cd9135-e6d8-4350-861d-87af50413297", "name": "HTML extract LinkedIn article & other users contributions", "type": "n8n-nodes-base.html", "position": [2160, 200], "parameters": {"options": {}, "operation": "extractHtmlContent", "extractionValues": {"values": [{"key": "ArticleTitle", "cssSelector": ".pulse-title"}, {"key": "ArticleTopics", "cssSelector": ".article-main__content"}, {"key": "ArticleContributions", "cssSelector": ".contribution__text"}]}}, "typeVersion": 1}, {"id": "5496fe68-6c77-4520-9479-141a4a20643f", "name": "HTTP Request to get LinkedIn advice articles", "type": "n8n-nodes-base.httpRequest", "position": [1960, 200], "parameters": {"url": "={{ $json.matches }}", "options": {}}, "typeVersion": 4.2}, {"id": "b7235009-6bbb-4701-aeb4-c287b2782a88", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-365, -33], "parameters": {"color": 7, "width": 366.**************, "height": 473.**************, "content": "## What this workflow does\n1. **`Triggers Weekly`**: The workflow is set to run every Monday at 8:00 AM.\n2. **`Search Google for LinkedIn Advice Articles`**: Uses a predefined Google search URL to find the latest LinkedIn advice articles based on the user's area of expertise.\n3. **`Extract LinkedIn Article Links`**: A code node extracts all LinkedIn advice article links from the search results.\n4. **`Retrieve Article Content`**: For each article link, the workflow retrieves the HTML content and extracts the article title, topics, and existing contributions.\n5. **`Generate AI-Powered Contributions`**: The workflow sends the extracted article content to an AI model, which generates unique, helpful advice for each topic within the article.\n6. **`Post to Slack & NocoDB`**: The AI-generated contributions, along with the article links, are posted to a designated Slack channel and stored in a NocoDB database for future reference."}, "typeVersion": 1}, {"id": "6aff94a1-1a65-4d24-ab87-b8ff72ea33b5", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [20, -33], "parameters": {"color": 6, "width": 396.6384066163515, "height": 282.5799404564392, "content": "### Get More Templates Like This 👇\n[![Video Thumbnail](https://onlinethinking.io/wp-content/uploads/2024/10/Lets-Automate-It-Community.png)](http://onlinethinking.io/community)\n"}, "typeVersion": 1}, {"id": "89d13f57-4a7d-4071-8089-c28b5708c122", "name": "Sticky Note14", "type": "n8n-nodes-base.stickyNote", "position": [-364, 460], "parameters": {"width": 366.**************, "height": 329.*************, "content": "## Setup\n1. **`Google Search URL`**: Update the Google search URL with the relevant LinkedIn advice query for your field (e.g., \"site:linkedin.com/advice 'marketing automation'\").\n\n2. **`Slack Integration`**: Connect your Slack account and specify the Slack channel where you want the contributions to be posted.\n\n3. **`NocoDB Integration`**: Set up your NocoDB project to store the generated contributions along with the article titles and links."}, "typeVersion": 1}, {"id": "11ca526c-2512-4c66-8dbf-0f9cdec13d9f", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [-380, -200], "parameters": {"color": 7, "width": 812.*************, "height": 1198.*************, "content": "## Become A LinkedIn Top Voice with AI\nBuilt for the [Let's Automate It Community](http://onlinethinking.io/community) by [Optimus Agency](https://optimus01.co.za/)\n\nThis workflow helps users maintain a consistent presence on LinkedIn by automating the discovery of new advice articles and generating unique contributions using AI. It is ideal for professionals who want to engage with LinkedIn content regularly without spending too much time manually searching and drafting responses."}, "typeVersion": 1}, {"id": "9536318f-46a5-4ef4-bffc-395d3d2d1af8", "name": "Sticky Note15", "type": "n8n-nodes-base.stickyNote", "position": [-364, 810], "parameters": {"color": 7, "width": 781.0904623817446, "height": 169.84805961144036, "content": "## How to customize this workflow\n- **`Change Search Terms`**: Modify the Google search URL to focus on a different LinkedIn topic or expertise area.\n- **`Adjust Trigger Frequency`**: The workflow is set to run weekly, but you can adjust the frequency by changing the schedule trigger.\n- **`Enhance Contribution Quality`**: Customize the AI model's prompt to generate contributions that align with your brand voice or content strategy."}, "typeVersion": 1}, {"id": "5fab6cb9-5191-46a1-81ef-10b330f11b8b", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1086, -200], "parameters": {"color": 6, "width": 419.095339329518, "height": 463.432215862633, "content": "## Get all LinkedIn contributions from database NocoDB (GetRows)\nThis node retrieves all LinkedIn contributions stored in a specified NocoDB table. It performs a \"getAll\" operation to fetch all rows from the\n\n\n"}, "typeVersion": 1}, {"id": "0c2e26c9-be23-4755-81db-dd5167b84f52", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1620, -60], "parameters": {"color": 7, "width": 253.48029435813578, "height": 446.9376941946034, "content": "## Merge data and keep unique google search results\nThis node merges and filters the extracted article links, ensuring that only unique LinkedIn article URLs are processed. It prevents duplicate article links from being handled.\n"}, "typeVersion": 1}, {"id": "f086bb56-9cff-4dc0-a345-868eca20b12c", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1895.9759156157297, 160], "parameters": {"color": 5, "width": 426.673961735047, "height": 550.9285363859362, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n## 1. HTTP Request to get LinkedIn advice articles\nSends an HTTP request to retrieve the HTML content of each LinkedIn article link. This node fetches the actual web page content from LinkedIn articles.\n\n## 2. HTML extract LinkedIn article & other users contributions\nThis node extracts relevant information from the HTML of LinkedIn articles, including the article title, topics discussed, and contributions made by other users.\n"}, "typeVersion": 1}, {"id": "3d44a074-55a5-4eb3-b18a-40564f452646", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [2674, -56], "parameters": {"color": 3, "width": 242.07228127555214, "height": 451.5087489779234, "content": "## Post new LinkedIn contributions to Slack channel\nPosts the AI-generated LinkedIn contributions to a specified Slack channel. This allows the contributions to be shared with a team or for record-keeping.\n"}, "typeVersion": 1}, {"id": "cb052b4e-51a8-45be-8684-bd46f48b8017", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [2940, -55], "parameters": {"color": 6, "width": 280.61885357253936, "height": 570.1315791275019, "content": "## Post new LinkedIn contributions to NocoDB (CreateRows)\nStores the AI-generated LinkedIn contributions in a NocoDB database. It saves the article title, link, and the contribution itself for future reference and tracking."}, "typeVersion": 1}, {"id": "d1bbbc22-4913-4558-8bea-faa437c27e0b", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [2951, 399], "parameters": {"color": 7, "width": 259.5924775143092, "height": 104.96722916838547, "content": "### `NocoDB` can be swapped with another service like `Airtable` or `Google Sheets`"}, "typeVersion": 1}, {"id": "343da68f-09a7-4602-91e9-3ee47e23a936", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [1100, -40], "parameters": {"color": 7, "width": 392.21847914963246, "height": 80, "content": "### `NocoDB` can be swapped with another service like `Airtable` or `Google Sheets`"}, "typeVersion": 1}, {"id": "ed17e693-da43-49b9-bc4b-cae8a8503ee8", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [2344, -56], "parameters": {"width": 309.45427591228105, "height": 447.75689268844843, "content": "## LinkedIn Contribution Writer\nUses an AI model to generate unique contributions based on the extracted content from LinkedIn articles. The generated advice is tailored for each topic within the article.\n"}, "typeVersion": 1}, {"id": "653d839f-ea48-4e3c-a4a8-09dbeea59ed6", "name": "Airtable", "type": "n8n-nodes-base.airtable", "position": [80, 627], "parameters": {"options": {}, "resource": "base", "authentication": "airtableOAuth2Api"}, "credentials": {"airtableOAuth2Api": {"id": "goKNRHmMmQG5kexN", "name": "Airtable Account (<EMAIL>)"}}, "typeVersion": 2.1}, {"id": "4b4ba215-5a51-45dc-81ba-80b789ffe269", "name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [260, 627], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "list", "value": *********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1C7R_Xb5pfWlctEtgpOrXTz2O1I59VOBNIQJb2mWDWiI/edit#gid=*********", "cachedResultName": "Appointments (Smile)"}, "documentId": {"__rl": true, "mode": "list", "value": "1C7R_Xb5pfWlctEtgpOrXTz2O1I59VOBNIQJb2mWDWiI", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1C7R_Xb5pfWlctEtgpOrXTz2O1I59VOBNIQJb2mWDWiI/edit?usp=drivesdk", "cachedResultName": "Orthodontist - <PERSON><PERSON>"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "G62pZQANOhZoAYVs", "name": "Google Sheets Account (<EMAIL>)"}}, "typeVersion": 4.5}, {"id": "b98516e8-897f-4bf1-aa1a-1783f6b2d957", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [21, 270], "parameters": {"color": 7, "width": 394.**************, "height": 521.*************, "content": "## Tools That Are Interchangeable\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n### `NocoDB` can be swapped with another service like `Airtable` or `Google Sheets`"}, "typeVersion": 1}, {"id": "********-db4d-44ab-aea2-224d4c6bfd77", "name": "Get all LinkedIn contributions from database NocoDB (GetRows)1", "type": "n8n-nodes-base.nocoDb", "position": [160, 347], "parameters": {"table": "mpagw9n92ran52o", "options": {}, "operation": "getAll", "projectId": "psdqqm1bzphkodc", "returnAll": true, "authentication": "nocoDbApiToken"}, "credentials": {"nocoDbApiToken": {"id": "5PYJKB4ihzHtKLqx", "name": "NocoDB Account (<EMAIL>)"}}, "typeVersion": 3}, {"id": "0af1eb81-9592-4d5d-a628-18f7895e5401", "name": "Set Topic for Google search", "type": "n8n-nodes-base.set", "position": [800, 360], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "cf3ef4d0-2688-4fe1-9801-a8519bd293f7", "name": "Topic", "type": "string", "value": "Paid Advertising"}]}}, "typeVersion": 3.4}, {"id": "493d93d3-d426-4d8d-9b18-ec5855ee891a", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [740, 320], "parameters": {"color": 7, "width": 221.**************, "height": 399.**************, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n## Set Topic for Google search\nThis node sets a specific topic to be used in subsequent steps of the workflow. "}, "typeVersion": 1}, {"id": "e8b12df1-32b5-4f8f-b3d0-9fc68366f9a8", "name": "Sticky Note16", "type": "n8n-nodes-base.stickyNote", "position": [738.*************, 732.*************], "parameters": {"color": 4, "width": 223.**************, "height": 80, "content": "## 👆 EDIT THE FIELD HERE "}, "typeVersion": 1}], "pinData": {}, "connections": {"Set Topic for Google search": {"main": [[{"node": "Get advice articles from a Google search", "type": "main", "index": 0}]]}, "LinkedIn Contribution Writer": {"main": [[{"node": "Post new LinkedIn contributions to Slack channel", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Get all LinkedIn contributions from database NocoDB (GetRows)", "type": "main", "index": 0}, {"node": "Set Topic for Google search", "type": "main", "index": 0}]]}, "Get advice articles from a Google search": {"main": [[{"node": "Extract Article links for LinkedIn advice articles", "type": "main", "index": 0}]]}, "Schedule Trigger Every Monday, @ 08:00am": {"main": [[{"node": "Get all LinkedIn contributions from database NocoDB (GetRows)", "type": "main", "index": 0}, {"node": "Set Topic for Google search", "type": "main", "index": 0}]]}, "HTTP Request to get LinkedIn advice articles": {"main": [[{"node": "HTML extract LinkedIn article & other users contributions", "type": "main", "index": 0}]]}, "Merge data and keep unique google search results": {"main": [[{"node": "HTTP Request to get LinkedIn advice articles", "type": "main", "index": 0}]]}, "Post new LinkedIn contributions to Slack channel": {"main": [[{"node": "Post new LinkedIn contributions to NocoDB (CreateRows)", "type": "main", "index": 0}]]}, "Split Out all links for LinkedIn advice articles": {"main": [[{"node": "Merge data and keep unique google search results", "type": "main", "index": 1}]]}, "Extract Article links for LinkedIn advice articles": {"main": [[{"node": "Split Out all links for LinkedIn advice articles", "type": "main", "index": 0}]]}, "HTML extract LinkedIn article & other users contributions": {"main": [[{"node": "LinkedIn Contribution Writer", "type": "main", "index": 0}]]}, "Get all LinkedIn contributions from database NocoDB (GetRows)": {"main": [[{"node": "Merge data and keep unique google search results", "type": "main", "index": 0}]]}}}