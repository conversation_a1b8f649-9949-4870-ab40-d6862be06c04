{"id": "my335cY3wVwMqvqy", "meta": {"instanceId": "2ee8293be0fa6380527ab247a1eb95264d17c994507730562aa1c31ddb264f82", "templateCredsSetupCompleted": true}, "name": "Reservation Medcin", "tags": [], "nodes": [{"id": "32fe7a8b-aa1a-4517-a167-41972f77d69b", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-360, -40], "webhookId": "8f427031-1110-4ea3-aef7-5d06ba7d5bce", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "3510bb5a-3c8b-4978-a6c5-5c077be74f3f", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-20, -60], "parameters": {"options": {"systemMessage": "=🎯 Role of the Assistant\nYou are a virtual assistant specializing in appointment management for Dr. <PERSON><PERSON>. Your goal is to schedule consultations accurately, ensuring real availability while providing a smooth experience for patients.\n\n🕒 Office Hours\nMonday - Friday: 9:00 AM - 8:00 PM\nSaturday: 9:00 AM - 1:00 PM\nSunday: ❌ Closed\nConsultation Duration: 1 hour\nBreak Between Patients: 15 minutes\n\n📅 Booking Process\n\n1️⃣ Request Patient Information (Mandatory):\nFull Name\nPhone Number\nDesired Date and Time\n2️⃣ Availability Check:\nIf the requested time is outside office hours → offer only available slots.\nIf the requested time is available, ask for confirmation and book it.\nIf the requested time is unavailable, apologize and suggest the actual available slots on the requested day (between 9:00 AM and 8:00 PM, respecting breaks).\n\n##Example:\nIf a patient requests an appointment at 10:00 AM, check Google Calendar to confirm availability between 9:00 AM and 8:00 PM, considering the consultation duration (1 hour) and the 15-minute breaks.\n\n🚨 Do not confirm the appointment immediately—you must receive the patient's confirmation first.\n\n3️⃣ Confirmation & Updates:\nConfirm availability with the patient before finalizing.\nUpdate Google Calendar & Google Sheets after every booking.\nGoogle Calendar Event Title: \"Patient Name - Phone Number\".\nFor modifications or cancellations, free the slot and update the schedule.\n\n##Tools:\nUse \"Cheek Avilability\" to check available slots.\nUse \"Creat event\" to book the appointment.\nUse \"Add Data\" to record patient information.\n\n💬 Communication\n✅ Respond clearly, professionally, and in a friendly manner.\n✅ Always confirm the final date and time with the patient.\n✅ Ensure Google Calendar and Google Sheets are updated after every booking.\n\n📅 Today's date: {{ $now }}."}}, "typeVersion": 1.7}, {"id": "fea932f2-c99e-4e1a-83bc-b06abf6cce41", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-80, 160], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "x0tQpNXNP6v5Ovtd", "name": "OpenAi account 2"}}, "typeVersion": 1.2}, {"id": "05bfbeb4-d2a4-4372-b763-6da636ed4393", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [60, 160], "parameters": {"sessionKey": "={{ $('When chat message received').item.json.sessionId }}", "sessionIdType": "customKey"}, "typeVersion": 1.3}, {"id": "********-daf8-4fc6-a61a-98504b239d83", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [20, -220], "parameters": {"color": 7, "width": 194, "height": 141, "content": "**AI Agent 👇**\nThe Prompt is already there, You just need to setup the prompt user message with your text message."}, "typeVersion": 1}, {"id": "947c5aa3-549e-49f1-b136-030cbd3ca6ff", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-120, 300], "parameters": {"color": 7, "width": 150, "height": 80, "content": "**Chat Model ☝️**\nAdd your Open Ai API Key "}, "typeVersion": 1}, {"id": "cac840df-644e-4092-b678-af2fdf3fc378", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [240, 300], "parameters": {"color": 7, "width": 190, "height": 80, "content": "**Gpoogle Calendar ☝️**\nConnect to Google Calendar"}, "typeVersion": 1}, {"id": "f474ee97-ba38-4100-bfc7-0d01d0a4c599", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [500, 300], "parameters": {"color": 7, "width": 190, "height": 80, "content": "**Google Sheets ☝️**\nConnect to Google Sheets"}, "typeVersion": 1}, {"id": "51fcd961-7b0b-4435-a315-17d4ddc1ed30", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [40, 300], "parameters": {"color": 7, "width": 150, "height": 80, "content": "**Memory ☝️**\nAdd the Session ID "}, "typeVersion": 1}, {"id": "398fdf7a-508d-4a0a-8c2c-1f0075b6ad56", "name": "Check Availability", "type": "n8n-nodes-base.googleCalendarTool", "position": [200, 160], "parameters": {"options": {}, "timeMax": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('End_Time', ``, 'string') }}", "timeMin": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Start_Time', ``, 'string') }}", "calendar": {"__rl": true, "mode": "list", "value": "<EMAIL>", "cachedResultName": "Prise de rendez vous pour les Medcins "}, "resource": "calendar"}, "credentials": {"googleCalendarOAuth2Api": {"id": "NtT31ekfbGzWyc9k", "name": "Google Calendar account"}}, "typeVersion": 1.3}, {"id": "********-ffb6-4d38-8fb0-975eb47976f6", "name": "Creat event", "type": "n8n-nodes-base.googleCalendarTool", "position": [360, 160], "parameters": {"end": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('End', ``, 'string') }}", "start": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Start', ``, 'string') }}", "calendar": {"__rl": true, "mode": "list", "value": "<EMAIL>", "cachedResultName": "Prise de rendez vous pour les Medcins "}, "additionalFields": {}}, "credentials": {"googleCalendarOAuth2Api": {"id": "NtT31ekfbGzWyc9k", "name": "Google Calendar account"}}, "typeVersion": 1.3}, {"id": "650b5d36-7b52-4bb2-953a-d9ee278a35eb", "name": "Add data", "type": "n8n-nodes-base.googleSheetsTool", "position": [500, 160], "parameters": {"columns": {"value": {"Nom complet": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Nom_complet', ``, 'string') }}", "Date / heure ": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Date___heure_', ``, 'string') }}", "Numéro de telephone": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Num_ro_de_telephone', ``, 'string') }}"}, "schema": [{"id": "Nom complet", "type": "string", "display": true, "required": false, "displayName": "Nom complet", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Numéro de telephone", "type": "string", "display": true, "required": false, "displayName": "Numéro de telephone", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Date / heure ", "type": "string", "display": true, "required": false, "displayName": "Date / heure ", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "display": true, "removed": true, "required": false, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1JAbg-TJZr7fqiRMAjQY6baDAkQoigzUd4YqbTPoQqWE/edit#gid=0", "cachedResultName": "Feuille 1"}, "documentId": {"__rl": true, "mode": "list", "value": "1JAbg-TJZr7fqiRMAjQY6baDAkQoigzUd4YqbTPoQqWE", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1JAbg-TJZr7fqiRMAjQY6baDAkQoigzUd4YqbTPoQqWE/edit?usp=drivesdk", "cachedResultName": "RDV Medcin"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "Q4J5dsFmt1OSnjNV", "name": "Google Sheets account"}}, "typeVersion": 4.5}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "39048f71-6a4c-4181-947e-5e2545c4dc1e", "connections": {"AI Agent": {"main": [[]]}, "Add data": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Creat event": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Check Availability": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}}}