{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "e12611f4-37d2-48f9-8a60-ddcf4ff34cfc", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-480, 380], "parameters": {"color": 7, "width": 1118.3459011229047, "height": 775.3931210698682, "content": "### Sub-workflow: Custom tool\nThe agent above can call this workflow. It checks if the user has supplied an email address. If they haven't it prompts them to provide one. If they have, it messages a customer support channel for help."}, "typeVersion": 1}, {"id": "72dbee3e-fe3b-4354-9b02-2fe52af23035", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-480, -180], "parameters": {"color": 7, "width": 927.5, "height": 486.5625, "content": "### Main workflow: AI agent using custom tool"}, "typeVersion": 1}, {"id": "a1c9660d-84b1-418a-bbb3-88d79cdd79d3", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [80, 60], "parameters": {"color": 5, "width": 197.45572294791873, "height": 179.21380662202682, "content": "**This tool calls the sub-workflow below**"}, "typeVersion": 1}, {"id": "b4ffb76d-c44f-46d6-b2d9-4e5d551adee1", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-280, 40], "parameters": {"color": 2, "width": 150, "height": 213.44323866265472, "content": "**Set your credentials**"}, "typeVersion": 1}, {"id": "8a9187f1-3cf9-479f-aa7f-5581880394d0", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [40, 540], "parameters": {"color": 2, "width": 178.0499248677781, "height": 250.57252651663197, "content": "**Set your credentials and Slack details**"}, "typeVersion": 1}, {"id": "3f72d117-e07a-4af4-aa1b-92ce04bf0b3c", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-640, -120], "parameters": {"color": 4, "width": 185.9375, "height": 214.8397420554627, "content": "## Try it out\n\nSelect **Chat** at the bottom and enter:\n\n_Hi! Please respond to this as if you don't know the answer to my query._"}, "typeVersion": 1}, {"id": "b85eff07-ee3c-4aeb-871e-b25a131a7afb", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [360, 900], "parameters": {"height": 145, "content": "## Next steps\n\nLearn more about [Advanced AI in n8n](https://docs.n8n.io/advanced-ai/)"}, "typeVersion": 1}, {"id": "feb1e50d-5044-4ea6-8719-72e176581e27", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-400, -120], "webhookId": "e0e69202-32e8-41b5-963b-50905dd93e88", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "6af81471-7cd4-4517-9677-b634b59620b4", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-240, 120], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "37604c8d-5c70-4a81-a1d0-eafe42ce612d", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [-60, 120], "parameters": {}, "typeVersion": 1.3}, {"id": "fd404bb5-0703-4d08-8b9b-4a8b01fd2bff", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-380, 740], "parameters": {"workflowInputs": {"values": [{"name": "chatInput"}]}}, "typeVersion": 1.1}, {"id": "a807ca29-65bf-4d97-b89f-5ce16cd05347", "name": "Check if user has provided email", "type": "n8n-nodes-base.if", "position": [-200, 740], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "e6dce436-5e85-4722-a7e4-0ceb940a5477", "operator": {"type": "string", "operation": "regex"}, "leftValue": "={{ $('When Executed by Another Workflow').item.json.chatInput }}", "rightValue": "=/([a-zA-Z0-9._-]+@[a-zA-Z0-9._-]+\\.[a-zA-Z0-9_-]+)/gi"}]}}, "typeVersion": 2.2}, {"id": "b9c552ce-4c58-48dd-b168-5e277de89954", "name": "Message Slack for help", "type": "n8n-nodes-base.slack", "position": [80, 620], "webhookId": "c54bea4c-bdb6-4f42-9f82-525857df5a9a", "parameters": {"text": "={{ \"A user had a question the bot couldn't answer. Here's their message: \" + $('When Executed by Another Workflow').first().json.chatInput }}", "select": "channel", "channelId": {"__rl": true, "mode": "name", "value": "#general"}, "otherOptions": {}}, "credentials": {"slackApi": {"id": "VfK3js0YdqBdQLGP", "name": "Slack account"}}, "typeVersion": 2.3}, {"id": "644a05fc-ac7e-4ea9-ab03-3b6fbf7a3654", "name": "Confirm that we've messaged a human", "type": "n8n-nodes-base.code", "position": [300, 620], "parameters": {"jsCode": "const response = {\"response\": \"Thank you for getting in touch. I've messaged a human to help.\"}\nreturn response;"}, "typeVersion": 2}, {"id": "38e81aa5-30b3-48f9-88e8-1039f607f3e7", "name": "Prompt the user to provide an email", "type": "n8n-nodes-base.code", "position": [80, 860], "parameters": {"jsCode": "const response = {\"response\":\"I'm sorry I don't know the answer. Please repeat your question and include your email address so I can request help.\"};\nreturn response;"}, "typeVersion": 2}, {"id": "61ddb25a-f7f2-4691-94d5-3f32c183ec46", "name": "Not sure?", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [140, 120], "parameters": {"name": "dont_know_tool", "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "description": "Use this tool if you don't know the answer to the user's question, or if you're not very confident about your answer.", "workflowInputs": {"value": {"chatInput": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('chatInput', ``, 'string') }}"}, "schema": [{"id": "chatInput", "type": "string", "display": true, "removed": false, "required": false, "displayName": "chatInput", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2}, {"id": "395349d1-1715-4550-a0c8-1388d17b4386", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-180, -120], "parameters": {"options": {}}, "typeVersion": 1.8}], "pinData": {}, "connections": {"Not sure?": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Message Slack for help": {"main": [[{"node": "Confirm that we've messaged a human", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Check if user has provided email": {"main": [[{"node": "Message Slack for help", "type": "main", "index": 0}], [{"node": "Prompt the user to provide an email", "type": "main", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Check if user has provided email", "type": "main", "index": 0}]]}}}