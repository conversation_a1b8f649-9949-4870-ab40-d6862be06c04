{"meta": {"instanceId": "03e9d14e9196363fe7191ce21dc0bb17387a6e755dcc9acc4f5904752919dca8"}, "nodes": [{"id": "a9a92b8a-05cf-4d9e-ae01-be3b17346893", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.set", "position": [-560, 660], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "e63f9299-a19d-4ba1-93b0-59f458769fb2", "name": "response", "type": "object", "value": "={{ $json.body.payload }}"}]}}, "typeVersion": 3.3}, {"id": "f999011b-e54d-4514-94ec-4d544af4d145", "name": "Close Mo<PERSON>up", "type": "n8n-nodes-base.respondToWebhook", "position": [-160, 1120], "parameters": {"options": {}, "respondWith": "noData"}, "typeVersion": 1.1}, {"id": "a16d64a0-fe07-4cae-b458-a91937e57a4e", "name": "Route Message", "type": "n8n-nodes-base.switch", "position": [-380, 660], "parameters": {"rules": {"values": [{"outputKey": "Request Modal", "conditions": {"options": {"version": 1, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.response.callback_id }}", "rightValue": "search_recent_incidents"}]}, "renameOutput": true}, {"outputKey": "Submit Data", "conditions": {"options": {"version": 1, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "65daa75f-2e17-4ba0-8fd8-2ac2159399e3", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.response.type }}", "rightValue": "view_submission"}]}, "renameOutput": true}, {"outputKey": "Block Actions", "conditions": {"options": {"version": 1, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "c242cee2-7274-4e02-bfbe-d0e999d30ea7", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.response.type }}", "rightValue": "block_actions"}]}, "renameOutput": true}]}, "options": {"fallbackOutput": "none"}}, "typeVersion": 3}, {"id": "54fa31d5-7259-4c19-8891-8b559af87959", "name": "ServiceNow Modal", "type": "n8n-nodes-base.httpRequest", "position": [260, 560], "parameters": {"url": "https://slack.com/api/views.open", "method": "POST", "options": {}, "jsonBody": "=  {\n    \"trigger_id\": \"{{ $('Parse Webhook').item.json['response']['trigger_id'] }}\",\n    \"external_id\": \"Search SNOW Incidents\",\n    \"view\": {\n\t\"title\": {\n\t\t\"type\": \"plain_text\",\n\t\t\"text\": \"Search SNOW Incidents\",\n\t\t\"emoji\": true\n\t},\n\t\"submit\": {\n\t\t\"type\": \"plain_text\",\n\t\t\"text\": \"Search\",\n\t\t\"emoji\": true\n\t},\n\t\"type\": \"modal\",\n\t\"external_id\": \"search_snow_incidents\",\n\t\"close\": {\n\t\t\"type\": \"plain_text\",\n\t\t\"text\": \"Cancel\",\n\t\t\"emoji\": true\n\t},\n\t\"blocks\": [\n\t\t{\n\t\t\t\"type\": \"section\",\n\t\t\t\"block_id\": \"greeting_section\",\n\t\t\t\"text\": {\n\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\"text\": \":wave: Hey there!\\n\\nUse this form to search ServiceNow for incidents based on their priority and state. Both of these properties are required to search incidents properly.\",\n\t\t\t\t\"emoji\": true\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\t\"type\": \"divider\",\n\t\t\t\"block_id\": \"divider_1\"\n\t\t},\n\t\t{\n\t\t\t\"type\": \"input\",\n\t\t\t\"block_id\": \"priority_selector\",\n\t\t\t\"element\": {\n\t\t\t\t\"type\": \"external_select\",\n\t\t\t\t\"placeholder\": {\n\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\"text\": \"Priority of Incidents to Search\",\n\t\t\t\t\t\"emoji\": true\n\t\t\t\t},\n\t\t\t\t\"action_id\": \"priority_select\",\n\t\t\t\t\"min_query_length\": 0\n\t\t\t},\n\t\t\t\"label\": {\n\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\"text\": \"Priority Selector\",\n\t\t\t\t\"emoji\": true\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\t\"type\": \"input\",\n\t\t\t\"block_id\": \"state_selector\",\n\t\t\t\"element\": {\n\t\t\t\t\"type\": \"external_select\",\n\t\t\t\t\"placeholder\": {\n\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\"text\": \"State of Incidents to Search\",\n\t\t\t\t\t\"emoji\": true\n\t\t\t\t},\n\t\t\t\t\"action_id\": \"state_select\",\n\t\t\t\t\"min_query_length\": 0\n\t\t\t},\n\t\t\t\"label\": {\n\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\"text\": \"State Selector\",\n\t\t\t\t\"emoji\": true\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\t\"type\": \"section\",\n\t\t\t\"text\": {\n\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\"text\": \"Please select a channel where the results will be posted.\"\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\t\"type\": \"actions\",\n\t\t\t\"elements\": [\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"channels_select\",\n\t\t\t\t\t\"placeholder\": {\n\t\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\t\"text\": \"Select a channel\",\n\t\t\t\t\t\t\"emoji\": true\n\t\t\t\t\t},\n\t\t\t\t\t\"action_id\": \"actionId-1\"\n\t\t\t\t}\n\t\t\t]\n\t\t}\n\t]\n}\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "nodeCredentialType": "slackApi"}, "credentials": {"slackApi": {"id": "K04E2FxPZozHux9J", "name": "ServiceNow Bot"}}, "typeVersion": 4.2}, {"id": "d16de218-b99b-4d13-9655-8fe1a329e01f", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [-760, 660], "webhookId": "e03c7d39-1dce-4ac5-8db8-2b4511a85a07", "parameters": {"path": "e03c7d39-1dce-4ac5-8db8-2b4511a85a07", "options": {}, "httpMethod": "POST", "responseMode": "responseNode"}, "typeVersion": 2}, {"id": "57ee358a-d409-42e7-8200-4475c4c59263", "name": "Send 200", "type": "n8n-nodes-base.respondToWebhook", "position": [-160, 1660], "parameters": {"options": {"responseCode": 200}}, "typeVersion": 1.1}, {"id": "86b0fd85-b3d5-456c-8f59-0f29f283969f", "name": "ServiceNow", "type": "n8n-nodes-base.serviceNow", "position": [100, 1120], "parameters": {"options": {"sysparm_query": "=incident_state={{ $json.response.view.state.values.state_selector.state_select.selected_option.value }}^priority={{ $json.response.view.state.values.priority_selector.priority_select.selected_option.value }}", "sysparm_display_value": "all"}, "resource": "incident", "operation": "getAll", "returnAll": true, "authentication": "basicAuth"}, "credentials": {"serviceNowBasicApi": {"id": "wjkWiUNQxo5PzTIb", "name": "ServiceNow Basic Auth account"}}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "95fcd7f1-ac3a-4128-8b4a-84b636487d9e", "name": "Channel - Notify User no Incidents Matched", "type": "n8n-nodes-base.slack", "position": [960, 1360], "webhookId": "5d1ecba8-d03b-47cc-9d30-fd631e7816c1", "parameters": {"text": "=No incidents were found with a state of {{ $('Parse Webhook').item.json.response.view.state.values.state_selector.state_select.selected_option.text.text }} and priority of {{ $('Parse Webhook').item.json.response.view.state.values.priority_selector.priority_select.selected_option.text.text }}.", "select": "channel", "channelId": {"__rl": true, "mode": "id", "value": "={{ $('Parse Webhook').item.json.response.view.state.values.pWqkN['actionId-1'].selected_channel }}"}, "otherOptions": {"includeLinkToWorkflow": false}}, "credentials": {"slackApi": {"id": "K04E2FxPZozHux9J", "name": "ServiceNow Bot"}}, "typeVersion": 2.2}, {"id": "7f638817-6f97-42a9-9027-dd0d5fb6f560", "name": "DM - Notify User no Incidents Matched", "type": "n8n-nodes-base.slack", "position": [960, 1600], "webhookId": "5d1ecba8-d03b-47cc-9d30-fd631e7816c1", "parameters": {"text": "=No incidents were found with a state of {{ $('Parse Webhook').item.json.response.view.state.values.state_selector.state_select.selected_option.text.text }} and priority of {{ $('Parse Webhook').item.json.response.view.state.values.priority_selector.priority_select.selected_option.text.text }}.", "user": {"__rl": true, "mode": "id", "value": "={{ $('Parse Webhook').item.json.response.user.id }}"}, "select": "user", "otherOptions": {"includeLinkToWorkflow": false}}, "credentials": {"slackApi": {"id": "K04E2FxPZozHux9J", "name": "ServiceNow Bot"}}, "typeVersion": 2.2}, {"id": "f3a21223-9e74-4066-af9f-6b94f69cb01f", "name": "Were Incidents Found?", "type": "n8n-nodes-base.if", "position": [360, 1120], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "fcdf9a8e-6359-4a3e-bf4e-e1834945727b", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $('ServiceNow').item.json.number.value }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "e27438cb-ba24-4a3b-8fe8-52b7d39cb1e0", "name": "No Matches - Was a Channel Selected?", "type": "n8n-nodes-base.if", "position": [580, 1480], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a0b79298-b93f-4ed3-b53b-5c28dfdb2699", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $('Parse Webhook').item.json.response.view.state.values.pWqkN['actionId-1'].selected_channel }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "de7d3155-1c6d-43a1-9cc0-4900d176fd3e", "name": "Sort by Most Recent", "type": "n8n-nodes-base.sort", "position": [580, 580], "parameters": {"options": {}, "sortFieldsUi": {"sortField": [{"order": "descending", "fieldName": "number.value"}]}}, "typeVersion": 1}, {"id": "19f529c7-bfe6-4713-8ed3-d80ecc0078de", "name": "Retain First 5 Incidents", "type": "n8n-nodes-base.limit", "position": [740, 580], "parameters": {"maxItems": 5}, "typeVersion": 1}, {"id": "9b095ad5-dedc-43e6-9ab3-947b90e7145d", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [920, 580], "parameters": {"options": {"reset": false}}, "typeVersion": 3}, {"id": "9a236a69-3ea5-46f5-8f2d-f7421bff638a", "name": "Format Incident Details", "type": "n8n-nodes-base.set", "position": [1240, 680], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "62388dab-28d4-40fa-a9f9-90d68c5dc491", "name": "incident_details", "type": "string", "value": "={\n\t\t\t\"type\": \"section\",\n\t\t\t\"text\": {\n\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\"text\": \"<https://dev206761.service-now.com/nav_to.do?uri=incident.do?sys_id={{ $json.sys_id.value }}|*{{ $json.task_effective_number.value }}*>\\n{{ $json.short_description.display_value }}\\n*Opened by:* {{ $json.caller_id.display_value }}\\n*Date Opened:* {{ $json.opened_at.display_value }}\\n*Severity:* {{ $json.severity.display_value }}\\n*Priority:* {{ $json.priority.display_value }}\\n*State:* {{ $json.incident_state.display_value }}\\n*Category:* {{ $json.category.display_value }}\"\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\t\"type\": \"divider\"\n\t\t}"}]}}, "typeVersion": 3.4}, {"id": "6e15b991-d9d5-4244-a3db-dbd37c248303", "name": "Format Slack Message", "type": "n8n-nodes-base.set", "position": [1320, 500], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "90720996-88cc-4e47-b5bb-d5570c15f95c", "name": "slack_output", "type": "string", "value": "={\n\t\"blocks\": [\n\t\t{\n\t\t\t\"type\": \"section\",\n\t\t\t\"block_id\": \"greeting_section\",\n\t\t\t\"text\": {\n\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\"text\": \":wave: Hey there!\\n\\nHere are the incident summaries you requested with a state of {{ $('Parse Webhook').item.json.response.view.state.values.state_selector.state_select.selected_option.text.text }} and priority of {{ $('Parse Webhook').item.json.response.view.state.values.priority_selector.priority_select.selected_option.text.text }}.\\nA total of {{ $('ServiceNow').all().length }} incident(s) were found. If more than 5 were found only the 5 most recent will be listed. You can <https://dev206761.service-now.com/now/nav/ui/classic/params/target/incident_list.do%3Fsysparm_query%3Dincident_state%253D{{ $('Parse Webhook').item.json.response.view.state.values.state_selector.state_select.selected_option.value }}%255Epriority%253D{{ $('Parse Webhook').item.json.response.view.state.values.priority_selector.priority_select.selected_option.value }}%26sysparm_first_row%3D1%26sysparm_view%3Dess|click here> to view all of the matching incidents in ServiceNow.\"\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\t\"type\": \"divider\"\n\t\t},\n\t\t{{ $('Concatenate Incident Details').item.json.concatenated_incident_details }}\n\t]\n}"}]}}, "typeVersion": 3.4}, {"id": "08182589-800d-4ce6-8654-fc53d2ee56c3", "name": "Concatenate Incident Details", "type": "n8n-nodes-base.summarize", "position": [1140, 500], "parameters": {"options": {}, "fieldsToSummarize": {"values": [{"field": "incident_details", "aggregation": "concatenate"}]}}, "typeVersion": 1}, {"id": "86b698d2-2854-4393-8ee8-76f8e7b01586", "name": "DM - Send Matching Incidents", "type": "n8n-nodes-base.slack", "position": [1880, 720], "webhookId": "5d1ecba8-d03b-47cc-9d30-fd631e7816c1", "parameters": {"text": "=", "user": {"__rl": true, "mode": "id", "value": "={{ $('Parse Webhook').item.json.response.user.id }}"}, "select": "user", "blocksUi": "={{ $('Format Slack Message').item.json.slack_output }}", "messageType": "block", "otherOptions": {"includeLinkToWorkflow": false}}, "credentials": {"slackApi": {"id": "K04E2FxPZozHux9J", "name": "ServiceNow Bot"}}, "typeVersion": 2.2}, {"id": "cbd8fbc3-d589-4625-99b5-a98e6a41d4bb", "name": "Channel - Send Matching Incidents", "type": "n8n-nodes-base.slack", "position": [1880, 520], "webhookId": "5d1ecba8-d03b-47cc-9d30-fd631e7816c1", "parameters": {"text": "=", "select": "channel", "blocksUi": "={{ $('Format Slack Message').item.json.slack_output }}", "channelId": {"__rl": true, "mode": "id", "value": "={{ $('Parse Webhook').item.json.response.view.state.values.pWqkN['actionId-1'].selected_channel }}"}, "messageType": "block", "otherOptions": {"includeLinkToWorkflow": false}}, "credentials": {"slackApi": {"id": "K04E2FxPZozHux9J", "name": "ServiceNow Bot"}}, "typeVersion": 2.2}, {"id": "c3ed618f-b65e-4df2-80c0-90b2e2be3783", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [-200, -709.4873251551015], "parameters": {"color": 7, "width": 709.3965558024038, "height": 887.8719128264411, "content": "![Slack](https://uploads.n8n.io/templates/servicenowmodalinterface.png)\n## Slack Modal Interface\n\nWhen triggered, Slack will display this interface to allow Slack users to search ServiceNow for tickets based on priority and state, and then allow you to choose which channel to output the results. If no channel is found, the response will be sent to the Slack user via DM. "}, "typeVersion": 1}, {"id": "69f47cb7-84c6-4037-b3ba-8364ec572fde", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-798.751282964615, 190.55356752462308], "parameters": {"color": 7, "width": 579.6865154062818, "height": 647.0013506366993, "content": "![Slack](https://uploads.n8n.io/templates/slack.png)\n## Receive and Parse Slack Event via Webhook\n\nThis section begins with the `Webhook` node, which captures events from Slack, such as modal submissions or button presses. The payload from Slack is then processed by the `Parse Webhook` node to extract relevant details like callback IDs, user inputs (e.g., priority and state), and additional metadata. Once the data is parsed, it is passed to the `Route Message` node, which evaluates the callback ID or action type using a `Switch` node. Depending on the conditions, the workflow routes the data to specific paths: handling modal requests, processing data submissions, or responding to button actions. This setup ensures seamless handling of different Slack interactions and prepares the data for subsequent steps."}, "typeVersion": 1}, {"id": "622f63e4-fd03-4a76-bb2d-04a2daea9a46", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [-200, 188.05676141451897], "parameters": {"color": 7, "width": 710.3172669178614, "height": 563.0861092667175, "content": "![Slack](https://uploads.n8n.io/templates/slack.png)\n## Respond to Modal request\n\nThis section starts with the `Respond to Slack Webhook`, which sends an acknowledgment to Slack after a modal interaction is triggered. This ensures the Slack interface remains error-free and provides a smooth user experience. Following this, the `ServiceNow Modal` node is used to open a Slack modal via the Slack API. The modal allows users to input search parameters for ServiceNow incidents, such as priority and state. Additionally, users can select the Slack channel where the results will be posted. This integration ensures a seamless connection between Slack and ServiceNow, enabling users to perform detailed searches directly from Slack.\n"}, "typeVersion": 1}, {"id": "16d7f224-7792-4e9f-ae5c-1c0b6a39e703", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-200, 760], "parameters": {"color": 7, "width": 709.0896745965773, "height": 550.5825149622945, "content": "![Servicenow](https://uploads.n8n.io/templates/servicenow.png)\n## Close Modal and Search Service Now\n\nThis section starts with the `Close Modal Popup` node, which sends a response to Slack to close the modal after user input has been captured. Once the modal is closed, the workflow moves to the `ServiceNow` node. This node performs an API query to retrieve incidents from ServiceNow that match the specified state and priority provided by the user in the modal form. The query results are then evaluated by the `Were Incidents Found`? node, an If node that checks if any incidents were returned by the query. This section ensures a smooth transition from user input in Slack to backend data retrieval in ServiceNow, facilitating the identification of relevant incidents."}, "typeVersion": 1}, {"id": "3f52816d-db59-4574-b82e-8a9ca854e049", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [526.5720643091352, 908.7025500703817], "parameters": {"color": 7, "width": 714.3631681325317, "height": 911.8420872184945, "content": "![Slack](https://uploads.n8n.io/templates/slack.png)\n## No Incidents found, respond to Slack\n\nThis section begins with the `No Matches - Was a Channel Selected?` node, which evaluates whether the user selected a specific Slack channel for receiving notifications. If a channel was selected, the workflow proceeds to the `Channel - Notify User no Incidents Matched` node, which sends a message to the designated channel informing users that no incidents were found matching the specified criteria of state and priority.\n\nIf no channel was selected, the workflow uses the `DM - Notify User no Incidents Matched` node to send a direct message to the user who initiated the query. This message includes details about the search parameters, ensuring the user is informed of the results regardless of the outcome. This step ensures transparent and efficient communication, whether via a public channel or a private direct message."}, "typeVersion": 1}, {"id": "22943cc9-c79c-4465-ac9e-040d5f49a879", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-200, 1328.507039277291], "parameters": {"color": 7, "width": 709.4188646504804, "height": 492.8100521251637, "content": "![Slack](https://uploads.n8n.io/templates/slack.png)\n## Respond to Slack Button Press with 200 Response\n\nThis section uses the `Send 200` node to send a 200 HTTP response back to Slack whenever a button press event is triggered in a Slack message. This response is crucial for preventing Slack from showing errors in its Block Kit user interface, ensuring a seamless and professional interaction for the user. By handling these button press events gracefully, this step maintains a positive user experience and avoids unnecessary confusion or interruptions."}, "typeVersion": 1}, {"id": "95d4d5e1-5f6b-4107-a55d-51e70c25c055", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [528.3624557345836, 26.66938195987973], "parameters": {"color": 7, "width": 956.6393374313541, "height": 870.8771447693905, "content": "![n8n](https://uploads.n8n.io/templates/n8n.png)\n## Sort and format Results for block kit\n\nThis section begins by organizing the incident data retrieved from ServiceNow. The `Sort by Most Recent` node arranges the incidents in descending order, ensuring that the latest ones are processed first. Next, the `Retain First 5 Incidents` node limits the output to the five most recent incidents for clarity and focus.\n\nThe `Loop Over Items` node iterates through each incident, allowing the workflow to process them individually. During each loop, the `Format Incident Details` node structures the details of each incident into a format compatible with Slack’s Block Kit, ensuring readability and a professional appearance.\n\nOnce all incidents are formatted, the `Concatenate Incident Details` node aggregates the results into a single, cohesive message. Finally, the `Format Slack Message` node prepares the Slack message with a friendly greeting, summary details, and links to view the full incidents in ServiceNow. This section ensures that incident information is not only organized but also presented in a visually appealing and actionable manner within Slack."}, "typeVersion": 1}, {"id": "2d55c76a-f7ba-46ec-acc1-13f54b22b2ee", "name": "Was a Channel Selected?", "type": "n8n-nodes-base.if", "position": [1580, 580], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a0b79298-b93f-4ed3-b53b-5c28dfdb2699", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $('Parse Webhook').item.json.response.view.state.values.pWqkN['actionId-1'].selected_channel }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "70792926-097f-4f2b-b3b4-afc7bad60ea6", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1500, 27.218710650838375], "parameters": {"color": 7, "width": 657.1120966423081, "height": 870.9953951550463, "content": "![Slack](https://uploads.n8n.io/templates/slack.png)\n## Check if Slack channel selected and send Incident results in block kit format\n\nThis section begins with the `Was a Channel Selected?` node, which checks whether the user specified a Slack channel to receive the output. If a channel is selected, the workflow proceeds to the `Channel - Send Matching Incidents` node, which sends the formatted incident details to the chosen Slack channel using Block Kit. The message includes key information such as incident summaries, priorities, and state details, ensuring effective communication to the target audience.\n\nIf no channel was selected, the workflow uses the `DM - Send Matching Incidents` node to deliver the same information directly to the user via a Slack direct message. By dynamically adjusting the delivery method based on the user's input, this step ensures the incident results are communicated efficiently, whether to a broader audience or privately to the user."}, "typeVersion": 1}, {"id": "0ae7f5bf-7a78-42f6-95aa-c0f685e63c40", "name": "Respond to Slack Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [-100, 560], "parameters": {"options": {}, "respondWith": "noData"}, "typeVersion": 1.1}], "pinData": {}, "connections": {"Webhook": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "ServiceNow": {"main": [[{"node": "Were Incidents Found?", "type": "main", "index": 0}]]}, "Parse Webhook": {"main": [[{"node": "Route Message", "type": "main", "index": 0}]]}, "Route Message": {"main": [[{"node": "Respond to Slack Webhook", "type": "main", "index": 0}], [{"node": "Close Mo<PERSON>up", "type": "main", "index": 0}], [{"node": "Send 200", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Concatenate Incident Details", "type": "main", "index": 0}], [{"node": "Format Incident Details", "type": "main", "index": 0}]]}, "Close Modal Popup": {"main": [[{"node": "ServiceNow", "type": "main", "index": 0}]]}, "Sort by Most Recent": {"main": [[{"node": "Retain First 5 Incidents", "type": "main", "index": 0}]]}, "Format Slack Message": {"main": [[{"node": "Was a Channel Selected?", "type": "main", "index": 0}]]}, "Were Incidents Found?": {"main": [[{"node": "Sort by Most Recent", "type": "main", "index": 0}], [{"node": "No Matches - Was a Channel Selected?", "type": "main", "index": 0}]]}, "Format Incident Details": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Was a Channel Selected?": {"main": [[{"node": "Channel - Send Matching Incidents", "type": "main", "index": 0}], [{"node": "DM - Send Matching Incidents", "type": "main", "index": 0}]]}, "Respond to Slack Webhook": {"main": [[{"node": "ServiceNow Modal", "type": "main", "index": 0}]]}, "Retain First 5 Incidents": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Concatenate Incident Details": {"main": [[{"node": "Format Slack Message", "type": "main", "index": 0}]]}, "No Matches - Was a Channel Selected?": {"main": [[{"node": "Channel - Notify User no Incidents Matched", "type": "main", "index": 0}], [{"node": "DM - Notify User no Incidents Matched", "type": "main", "index": 0}]]}}}