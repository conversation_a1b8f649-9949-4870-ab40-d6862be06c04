{"nodes": [{"name": "<PERSON>", "type": "n8n-nodes-base.wise", "position": [470, 320], "parameters": {"amount": 500, "resource": "quote", "operation": "create", "profileId": ********, "sourceCurrency": "EUR", "targetCurrency": "EUR", "targetAccountId": *********}, "credentials": {"wiseApi": "Wise API Credentials"}, "typeVersion": 1}, {"name": "Wise1", "type": "n8n-nodes-base.wise", "position": [660, 320], "parameters": {"quoteId": "={{$json[\"id\"]}}", "resource": "transfer", "operation": "create", "profileId": ********, "targetAccountId": *********, "additionalFields": {"reference": "Thank you for the contribution"}}, "credentials": {"wiseApi": "Wise API Credentials"}, "typeVersion": 1}, {"name": "Wise2", "type": "n8n-nodes-base.wise", "position": [870, 320], "parameters": {"resource": "transfer", "operation": "execute", "profileId": ********, "transferId": "={{$json[\"id\"]}}"}, "credentials": {"wiseApi": "Wise API Credentials"}, "typeVersion": 1}, {"name": "Wise3", "type": "n8n-nodes-base.wise", "position": [1070, 320], "parameters": {"resource": "transfer", "transferId": "={{$node[\"Wise1\"].json[\"id\"]}}"}, "credentials": {"wiseApi": "Wise API Credentials"}, "typeVersion": 1}], "connections": {"Wise": {"main": [[{"node": "Wise1", "type": "main", "index": 0}]]}, "Wise1": {"main": [[{"node": "Wise2", "type": "main", "index": 0}]]}, "Wise2": {"main": [[{"node": "Wise3", "type": "main", "index": 0}]]}}}