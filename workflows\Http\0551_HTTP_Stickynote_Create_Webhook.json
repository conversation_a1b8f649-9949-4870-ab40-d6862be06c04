{"meta": {"instanceId": "f9c40bccfbfb973b8ba2bfd7b70b906c2376bb9900216d1ce424582c3097fb66"}, "nodes": [{"id": "89a2f8d1-a2fd-452b-8187-aec9e72efba5", "name": "Systeme | Get all contacts", "type": "n8n-nodes-base.httpRequest", "position": [480, 80], "parameters": {"url": "https://api.systeme.io/api/contacts", "options": {"pagination": {"pagination": {"parameters": {"parameters": [{"name": "startingAfter", "value": "={{ $response.body.items.last().id }}"}]}, "requestInterval": 1000, "completeExpression": "={{ $response.body.hasMore == false }}", "paginationCompleteWhen": "other"}}}, "sendQuery": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "queryParameters": {"parameters": [{"name": "limit", "value": "100"}]}}, "retryOnFail": true, "typeVersion": 4.2}, {"id": "56ad906f-0309-469a-8509-96ea6d56c0ba", "name": "Split Out2", "type": "n8n-nodes-base.splitOut", "position": [680, 80], "parameters": {"options": {}, "fieldToSplitOut": "items"}, "typeVersion": 1}, {"id": "b2ffb152-c3f2-4d74-a25e-9ec3162b8dbe", "name": "Systeme | Get All tags", "type": "n8n-nodes-base.httpRequest", "position": [480, 340], "parameters": {"url": "https://api.systeme.io/api/tags", "options": {"pagination": {"pagination": {"parameters": {"parameters": [{"name": "startingAfter", "value": "={{ $response.body.items.last().id }}"}]}, "requestInterval": 1000, "completeExpression": "={{ $response.body.hasMore == false }}", "paginationCompleteWhen": "other"}}}, "sendQuery": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "queryParameters": {"parameters": [{"name": "limit", "value": "100"}]}}, "typeVersion": 4.2}, {"id": "0e284595-ae1c-4f48-a276-d5059319226b", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [680, 340], "parameters": {"options": {}, "fieldToSplitOut": "items"}, "typeVersion": 1}, {"id": "b7b231c7-11e6-4dbd-aa0a-720ce1ba418b", "name": "Split Out3", "type": "n8n-nodes-base.splitOut", "position": [680, 580], "parameters": {"options": {}, "fieldToSplitOut": "items"}, "typeVersion": 1}, {"id": "bed54e99-ceaa-4a3a-a3b1-403a1573ba4d", "name": "Systeme | Get contacts with tag", "type": "n8n-nodes-base.httpRequest", "position": [480, 580], "parameters": {"url": "https://api.systeme.io/api/contacts", "options": {"pagination": {"pagination": {"parameters": {"parameters": [{"name": "startingAfter", "value": "={{ $response.body.items.last().id }}"}]}, "requestInterval": 1000, "completeExpression": "={{ $response.body.hasMore == false }}", "paginationCompleteWhen": "other"}}}, "sendQuery": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "queryParameters": {"parameters": [{"name": "limit", "value": "100"}, {"name": "tags", "value": "1012751"}]}}, "retryOnFail": true, "typeVersion": 4.2}, {"id": "725bd82d-22fd-4276-906b-273c8e3ce0e6", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [220, 80], "parameters": {"color": 7, "width": 233.**************, "height": 80, "content": "### Use this to get all your contacts 👉"}, "typeVersion": 1}, {"id": "830d9509-1fc2-4ea5-9061-bdc9f41aacd6", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-240, 340], "parameters": {"color": 7, "width": 254.*************, "height": 214.**************, "content": "All these nodes take the API rate limits and pagination into consideration.\n\nThis allows you to:\n- always get all the data from your account\n- perform many requests without reaching the rate limit"}, "typeVersion": 1}, {"id": "a8dcd1dc-9c70-4cb1-a01d-b537063bb67d", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [220, 340], "parameters": {"color": 7, "width": 233.**************, "height": 80, "content": "### Use this to get all your tags 👉"}, "typeVersion": 1}, {"id": "358bd219-2fd3-4d3b-8901-0ce1a8bd6328", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [220, 580], "parameters": {"color": 7, "width": 203.************, "height": 255.**************, "content": "### Use this to get only the contacts that have a certain tag 👉\n\nTo filter by more than one tag, just add more tag IDs to the tags parameter, like this:\n\n1012751,1012529"}, "typeVersion": 1}, {"id": "3b1f6f68-baf0-4357-9f05-74cda41037e3", "name": "Systeme | Add contact", "type": "n8n-nodes-base.httpRequest", "position": [480, 1000], "parameters": {"url": "https://api.systeme.io/api/contacts", "method": "POST", "options": {"batching": {"batch": {"batchSize": 9}}}, "jsonBody": "={\n  \"email\": \"{{ $json.emails }}\",\n  \"fields\": [\n    {\n      \"slug\": \"utm_source\",\n      \"value\": \"API\"\n    }\n  ]\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "retryOnFail": true, "typeVersion": 4.2}, {"id": "d4ae7c37-9044-4623-8051-2b0ef557ce57", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [220, 1000], "parameters": {"color": 7, "width": 203.************, "height": 396.06618898998505, "content": "### Use this to add many contacts at once 👉\n\nAdding thousands of contacts can be tricky, specially if you have many fields to add.\n\nThis node is an alternative to the native import functionality from Systeme.io.\n\nIf you need some custom data added to your leads, maybe using the API will be better than using the import tool they provide in Systeme."}, "typeVersion": 1}], "pinData": {}, "connections": {"Systeme | Get All tags": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Systeme | Get all contacts": {"main": [[{"node": "Split Out2", "type": "main", "index": 0}]]}, "Systeme | Get contacts with tag": {"main": [[{"node": "Split Out3", "type": "main", "index": 0}]]}}}