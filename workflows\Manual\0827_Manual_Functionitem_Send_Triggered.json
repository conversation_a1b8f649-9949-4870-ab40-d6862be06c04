{"id": "340", "name": "Email body parser by aprenden8n.com", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.functionItem", "position": [670, 300], "parameters": {"functionCode": "var obj = {};\nvar labels = item.labels.split(\",\");\nitem.labels.split(\",\").forEach(function(label) {\n  var re = labels.indexOf(label) === labels.length - 1 ? \"\\\\b\" + label + \"\\\\b[: ]+([^$]+)\" : \"\\\\b\" + label + \"\\\\b[: ]+([^\\\\n$]+)\";\n  var found = item.body.match(new RegExp(re, \"i\"));\n  if (found && found.length > 1) {\n    obj[label] = found[1].trim();\n  }\n});\n\nreturn obj;"}, "typeVersion": 1}, {"name": "Set values", "type": "n8n-nodes-base.set", "position": [460, 300], "parameters": {"values": {"number": [], "string": [{"name": "body", "value": "Name: <PERSON><PERSON>ail: <EMAIL>\nSubject: Welcome aboard\nMessage: <PERSON> <PERSON><PERSON>!\n\nThank you for your signup!"}, {"name": "labels", "value": "Name,Email,Subject,Message"}]}, "options": {}}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Set values": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Set values", "type": "main", "index": 0}]]}}}