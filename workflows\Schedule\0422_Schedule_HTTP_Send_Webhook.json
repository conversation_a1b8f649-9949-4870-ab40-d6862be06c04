{"nodes": [{"id": "3a0ba7f4-ac41-49b0-a055-b993c82f2680", "name": "Every day at 9 am", "type": "n8n-nodes-base.scheduleTrigger", "position": [340, 1280], "parameters": {"rule": {"interval": [{"triggerAtHour": 9}]}}, "typeVersion": 1.1}, {"id": "5fa47f36-3206-44b9-965a-0d27b10acc21", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [740, 980], "parameters": {"width": 348.2877732355713, "height": 595.2986206729652, "content": "## Search for all contacts that last contact date for is known\n\n1. Setup Oauth2 creds using n8n docs\nhttps://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.hubspottrigger/\n\n### Be careful with scopes. Scopes must be exactly as defined in the n8n docs\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n### To make this more effective, we sort ascending by last contact date."}, "typeVersion": 1}, {"id": "16b6fadf-ff1d-4670-b148-151cfbd242d5", "name": "Send outreach email", "type": "n8n-nodes-base.gmail", "position": [2040, 1060], "parameters": {"sendTo": "={{ $json.to }}", "message": "={{ $json.html }}", "options": {"senderName": "<PERSON><PERSON><PERSON> from n8n", "appendAttribution": false}, "subject": "={{ $json.subject }}"}, "credentials": {"gmailOAuth2": {"id": "rd2agqPeJBD2377h", "name": "Work Gmail"}}, "typeVersion": 2.1}, {"id": "a89ec3bd-7bb0-4dde-a9eb-800842073fc9", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [2000, 1300], "parameters": {"color": 3, "width": 289.74216745960825, "height": 402.1775107197669, "content": "## Record engagement in Hu<PERSON>pot\n\nOnce engagement is added, last contact date is updated and won't show up in search results for another month.\n"}, "typeVersion": 1}, {"id": "687509ed-4d25-4597-bade-1802348e42c9", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [260, 980], "parameters": {"color": 5, "width": 407.25356360335365, "height": 242.51175804432177, "content": "## Send followup email using Gmail to Hubspot contacts \n\nFollowing up at the right time is one of the most important parts of sales. This workflow uses Gmail to send outreach emails to Hubspot contacts that have already been contacted only once more than a month ago, and records the engagement in Hubspot. "}, "typeVersion": 1}, {"id": "************************************", "name": "Record engagement in HubSpot", "type": "n8n-nodes-base.hubspot", "position": [2080, 1500], "parameters": {"type": "email", "metadata": {"html": "={{ $json.html }}", "subject": "={{ $json.subject }}", "toEmail": ["={{ $json.to }}"], "firstName": "Mu<PERSON><PERSON>", "fromEmail": "<EMAIL>"}, "resource": "engagement", "authentication": "oAuth2", "additionalFields": {"associations": {"contactIds": "={{ $json.id }}"}}}, "credentials": {"hubspotOAuth2Api": {"id": "Gxwfj6z9NwaEC3P5", "name": "HubSpot account 3"}}, "typeVersion": 2}, {"id": "f90770cf-aa6c-4148-b471-2b28ed978f72", "name": "Get previously HubSpot contacts", "type": "n8n-nodes-base.hubspot", "position": [840, 1280], "parameters": {"operation": "search", "authentication": "oAuth2", "filterGroupsUi": {"filterGroupsValues": [{"filtersUi": {"filterValues": [{"operator": "HAS_PROPERTY", "propertyName": "notes_last_contacted|datetime"}]}}]}, "additionalFields": {"sortBy": "notes_last_contacted", "direction": "ASCENDING", "properties": ["firstname", "lastname", "email", "notes_last_contacted"]}}, "credentials": {"hubspotOAuth2Api": {"id": "Gxwfj6z9NwaEC3P5", "name": "HubSpot account 3"}}, "typeVersion": 2}, {"id": "751fd345-9fec-4c7c-b20b-1db86ce6df10", "name": "if last contacted before a month", "type": "n8n-nodes-base.if", "position": [1120, 1280], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "8d9ad7ef-6e1a-486c-9fac-419ad2660ace", "operator": {"type": "dateTime", "operation": "before"}, "leftValue": "={{ $json.properties.notes_last_contacted }}", "rightValue": "={{ DateTime.now().minus({days: 30}) }}"}]}}, "typeVersion": 2}, {"id": "********-ef7f-4dd7-98e7-6c3aaf6c2853", "name": "Get Hubspot Contact to engagement", "type": "n8n-nodes-base.httpRequest", "position": [1340, 1280], "parameters": {"url": "=https://api.hubapi.com/crm-associations/v1/associations/{{ $json.id }}/HUBSPOT_DEFINED/9", "options": {}, "authentication": "predefinedCredentialType", "nodeCredentialType": "hubspotOAuth2Api"}, "credentials": {"hubspotOAuth2Api": {"id": "Gxwfj6z9NwaEC3P5", "name": "HubSpot account 3"}}, "typeVersion": 4.1}, {"id": "fe0bc120-8bee-41fa-a896-3c9ff8cf3a29", "name": "if there has been only one engagement", "type": "n8n-nodes-base.if", "position": [1560, 1280], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "07c7e29c-eed1-4872-a9f7-b833bb0cafca", "operator": {"type": "number", "operation": "equals"}, "leftValue": "={{ $json.results.length }}", "rightValue": 1}]}}, "typeVersion": 2}, {"id": "512909c7-104b-4507-b91e-aa5b5a9410e5", "name": "Set email keys", "type": "n8n-nodes-base.set", "position": [1820, 1280], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "f3ecc873-2d60-4f2d-bc40-81f9379c725b", "name": "html", "type": "string", "value": "=Hey {{ $json.properties.firstname }},\n\nJust want to follow up on my previous email, since I have not \n heard from you. Have you had the chance to consider n8n? \n\nCheers,\n\n<PERSON><PERSON><PERSON>"}, {"id": "9f4f5b68-984b-415e-a110-a35ded22dd41", "name": "subject", "type": "string", "value": "Follow up on n8n"}, {"id": "5362aa67-f3fa-4a6e-b6e8-4c50cc7a3192", "name": "to", "type": "string", "value": "={{ $('Get previously HubSpot contacts').item.json.properties.email }}"}, {"id": "5b11e503-868d-4fca-bb44-59bb44d597a8", "name": "id", "type": "string", "value": "={{ $('Get previously HubSpot contacts').item.json.id }}"}]}}, "typeVersion": 3.3}, {"id": "c0e5472d-5208-4df7-89e8-380c2dab9642", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1300, 1060], "parameters": {"color": 4, "width": 490.3275896931988, "height": 496.3776986502359, "content": "## Get pervious engagements. Avoid sending follow ups if first eng\n\n### Here we simply follow up if there has only been outreach email before.\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n### We could the engagements here and their types and perform more advanced filtering. "}, "typeVersion": 1}], "pinData": {}, "connections": {"Set email keys": {"main": [[{"node": "Record engagement in HubSpot", "type": "main", "index": 0}, {"node": "Send outreach email", "type": "main", "index": 0}]]}, "Every day at 9 am": {"main": [[{"node": "Get previously HubSpot contacts", "type": "main", "index": 0}]]}, "Get previously HubSpot contacts": {"main": [[{"node": "if last contacted before a month", "type": "main", "index": 0}]]}, "if last contacted before a month": {"main": [[{"node": "Get Hubspot Contact to engagement", "type": "main", "index": 0}]]}, "Get Hubspot Contact to engagement": {"main": [[{"node": "if there has been only one engagement", "type": "main", "index": 0}]]}, "if there has been only one engagement": {"main": [[{"node": "Set email keys", "type": "main", "index": 0}]]}}}