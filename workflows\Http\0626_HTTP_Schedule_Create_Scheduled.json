{"nodes": [{"id": "2654751b-aa66-40ce-b8a0-79063aa710ad", "name": "Generate OAuth Token", "type": "n8n-nodes-base.httpRequest", "position": [820, 460], "parameters": {"url": "=https://accounts.accesscontrol.windows.net/{{ $json.tenant_id }}/tokens/oAuth/2", "options": {}, "requestMethod": "POST", "bodyParametersUi": {"parameter": [{"name": "grant_type", "value": "client_credentials"}, {"name": "client_id", "value": "{{client_id}}"}, {"name": "client_secret", "value": "{{client_secret}}"}, {"name": "resource", "value": "https://{your-sharepoint-domain}.sharepoint.com"}]}}, "typeVersion": 2}, {"id": "6f713c65-8fbd-4d05-bbef-9b4a1f6248e9", "name": "Fetch SharePoint List", "type": "n8n-nodes-base.httpRequest", "position": [1160, 460], "parameters": {"url": "https://{your-sharepoint-domain}.sharepoint.com/_api/web/lists/getbytitle('YourListTitle')/items", "options": {}, "headerParametersUi": {"parameter": [{"name": "Accept", "value": "application/json;odata=nometadata"}, {"name": "Content-Type", "value": "application/json;odata=verbose"}, {"name": "Authorization", "value": "Bearer {{<PERSON><PERSON>}}"}]}}, "typeVersion": 2}, {"id": "d11e9e92-2468-485c-87f5-6de7da7f9589", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [380, 460], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.2}, {"id": "8539f52c-2218-4a47-9678-3e3e8e9fd4c8", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.set", "position": [600, 460], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "399d42f3-41e0-4043-9a57-85771bf5cd07", "name": "tenant_id", "type": "string", "value": ""}]}}, "typeVersion": 3.4}, {"id": "5a4fa41c-0726-4528-99a3-b5e0c47c1960", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [580, 220], "parameters": {"color": 7, "width": 458, "height": 404, "content": "## Never expose or hard code below values \n**tenant_id,client_id,client_secret** \n\nAlways save these either in secure vault like hashicorp or GCP Secret Manager."}, "typeVersion": 1}], "pinData": {}, "connections": {"setTenant": {"main": [[{"node": "Generate OAuth Token", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "<PERSON><PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Generate OAuth Token": {"main": [[{"node": "Fetch SharePoint List", "type": "main", "index": 0}]]}}}