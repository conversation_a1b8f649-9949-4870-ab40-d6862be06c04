{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 200], "parameters": {}, "typeVersion": 1}, {"name": "Webflow", "type": "n8n-nodes-base.webflow", "position": [450, 200], "parameters": {"siteId": "601788abebf7aa35c1b038a1", "fieldsUi": {"fieldValues": [{"fieldId": "name", "fieldValue": "n8n"}, {"fieldId": "slug", "fieldValue": "n8n"}, {"fieldId": "_archived", "fieldValue": "false"}, {"fieldId": "_draft", "fieldValue": "false"}]}, "operation": "create", "collectionId": "601788ab33a62ac6a2a0284c"}, "credentials": {"webflowApi": "Webflow Credentials"}, "typeVersion": 1}, {"name": "Webflow2", "type": "n8n-nodes-base.webflow", "position": [650, 200], "parameters": {"itemId": "={{$json[\"_id\"]}}", "siteId": "601788abebf7aa35c1b038a1", "fieldsUi": {"fieldValues": [{"fieldId": "name", "fieldValue": "={{$json[\"name\"]}}"}, {"fieldId": "slug", "fieldValue": "={{$json[\"slug\"]}}"}, {"fieldId": "_archived", "fieldValue": "={{$json[\"_archived\"]}}"}, {"fieldId": "_draft", "fieldValue": "={{$json[\"_draft\"]}}"}, {"fieldId": "avatar", "fieldValue": "https://n8n.io/n8n-logo.png"}]}, "operation": "update", "collectionId": "601788ab33a62ac6a2a0284c"}, "credentials": {"webflowApi": "Webflow Credentials"}, "typeVersion": 1}, {"name": "Webflow1", "type": "n8n-nodes-base.webflow", "position": [850, 200], "parameters": {"itemId": "={{$json[\"_id\"]}}", "siteId": "601788abebf7aa35c1b038a1", "collectionId": "601788ab33a62ac6a2a0284c"}, "credentials": {"webflowApi": "Webflow Credentials"}, "typeVersion": 1}], "connections": {"Webflow": {"main": [[{"node": "Webflow2", "type": "main", "index": 0}]]}, "Webflow2": {"main": [[{"node": "Webflow1", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Webflow", "type": "main", "index": 0}]]}}}