{"id": "151", "name": "Receive a Mattermost message when new data gets added to Airtable", "nodes": [{"name": "Airtable Trigger", "type": "n8n-nodes-base.airtableTrigger", "position": [550, 340], "parameters": {"baseId": "", "tableId": "Data", "pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerField": "Created", "additionalFields": {}}, "credentials": {"airtableApi": "Airtable Credentials n8n"}, "typeVersion": 1}, {"name": "Mattermost", "type": "n8n-nodes-base.mattermost", "position": [750, 340], "parameters": {"message": "=New Data was added to Airtable.\nID:{{$node[\"Airtable Trigger\"].json[\"fields\"][\"id\"]}}\nName: {{$node[\"Airtable Trigger\"].json[\"fields\"][\"name\"]}}", "channelId": "", "attachments": [], "otherOptions": {}}, "credentials": {"mattermostApi": "mattermost"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Airtable Trigger": {"main": [[{"node": "Mattermost", "type": "main", "index": 0}]]}}}