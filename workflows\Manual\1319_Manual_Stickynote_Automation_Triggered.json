{"meta": {"instanceId": "62b3b6db4f4d3641a1fa1da6dfb9699a19380a1f60cbc18fc75d6d145f35552b"}, "nodes": [{"id": "40bb5497-d1d2-4eb7-b683-78b88c8d9230", "name": "Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [496.*************4, 520], "parameters": {"fileId": {"__rl": true, "mode": "url", "value": "https://drive.google.com/file/d/11Koq9q53nkk0F5Y8eZgaWJUVR03I4-MM/view"}, "options": {}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "20", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "1323d520-1528-4a5a-9806-8f4f45306098", "name": "Recursive Character Text Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "position": [996.*************, 920], "parameters": {"chunkSize": 3000, "chunkOverlap": 200}, "typeVersion": 1}, {"id": "796b155a-64e6-4a52-9168-a37c68077d99", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [836.*************, 740], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "JCgD7807AQpe8Xge", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "dbe42c28-6f0b-4999-8372-0b42f6fb5916", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [260, 420], "parameters": {"color": 7, "width": 978.*************, "height": 806.*************, "content": "### Load data into database\nFetch file from Google Drive, split it into chunks and insert into Pinecone index"}, "typeVersion": 1}, {"id": "43dc3736-834d-4322-8fd2-7826b0208c4b", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1520, 420], "parameters": {"color": 7, "width": 654.*************, "height": 806.*************, "content": "### Chat with database\nEmbed the incoming chat message and use it retrieve relevant chunks from the vector store. These are passed to the model to formulate an answer "}, "typeVersion": 1}, {"id": "53b18460-8ad6-425a-a01f-c2295cfddde8", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [996.*************, 740], "parameters": {"options": {}, "dataType": "binary"}, "typeVersion": 1}, {"id": "e729a021-eab3-48fa-a818-457efcaeebb2", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-20, 740], "parameters": {"height": 264.61498034081166, "content": "## Try me out\n1. In Pinecone, create an index with 1536 dimensions and select it in *both* Pinecone nodes\n2. Click 'test workflow' at the bottom of the canvas to load data into the vector store\n3. <PERSON>lick 'chat' at the bottom of the canvas to ask questions about the data"}, "typeVersion": 1}, {"id": "3e17c89c-620d-4892-b944-d792e48e3772", "name": "Question and Answer Chain", "type": "@n8n/n8n-nodes-langchain.chainRetrievalQa", "position": [1560, 521], "parameters": {}, "typeVersion": 1.2}, {"id": "516507f9-d0d9-4975-85d0-a7852ee41518", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1560, 741], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "JCgD7807AQpe8Xge", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "8b0a5d26-a60a-40ab-8200-72f542532096", "name": "Embeddings OpenAI2", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [1700, 1081], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "JCgD7807AQpe8Xge", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "07f61d20-cf50-48e8-9d34-92244af436cb", "name": "Vector Store Retriever", "type": "@n8n/n8n-nodes-langchain.retrieverVectorStore", "position": [1760, 741], "parameters": {}, "typeVersion": 1}, {"id": "0777de17-99a0-499a-b71f-245d5f76642e", "name": "Read Pinecone Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "position": [1700, 921], "parameters": {"options": {}, "pineconeIndex": {"__rl": true, "mode": "list", "value": "test-index", "cachedResultName": "test-index"}}, "credentials": {"pineconeApi": {"id": "Pp5aPt4JWBkDOGqZ", "name": "PineconeApi account"}}, "typeVersion": 1}, {"id": "cc5e6897-9d0b-4352-a882-5dc23104bf97", "name": "Insert into Pinecone vector store", "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "position": [856.*************, 520], "parameters": {"mode": "insert", "options": {"clearNamespace": true}, "pineconeIndex": {"__rl": true, "mode": "list", "value": "test-index", "cachedResultName": "test-index"}}, "credentials": {"pineconeApi": {"id": "Pp5aPt4JWBkDOGqZ", "name": "PineconeApi account"}}, "typeVersion": 1}, {"id": "c358aa73-b60f-453f-a3ef-539faa98c9b5", "name": "When clicking 'Chat' button below", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [1360, 521], "webhookId": "e259b6fe-b2a9-4dbc-98a4-9a160e7ac10c", "parameters": {}, "typeVersion": 1}, {"id": "d35db9e1-4efc-4980-9814-55fbe65e08fd", "name": "When clicking 'Test Workflow' button", "type": "n8n-nodes-base.manualTrigger", "position": [76.*************4, 520], "parameters": {}, "typeVersion": 1}, {"id": "4c04f576-e834-467d-98b4-38a2d501d82f", "name": "Set Google Drive file URL", "type": "n8n-nodes-base.set", "position": [296, 520], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "50025ff5-1b53-475f-b150-2aafef1c4c21", "name": "file_url", "type": "string", "value": "https://drive.google.com/file/d/11Koq9q53nkk0F5Y8eZgaWJUVR03I4-MM/view"}]}}, "typeVersion": 3.3}], "pinData": {}, "connections": {"Google Drive": {"main": [[{"node": "Insert into Pinecone vector store", "type": "main", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Insert into Pinecone vector store", "type": "ai_embedding", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Question and Answer Chain", "type": "ai_languageModel", "index": 0}]]}, "Embeddings OpenAI2": {"ai_embedding": [[{"node": "Read Pinecone Vector Store", "type": "ai_embedding", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Insert into Pinecone vector store", "type": "ai_document", "index": 0}]]}, "Vector Store Retriever": {"ai_retriever": [[{"node": "Question and Answer Chain", "type": "ai_retriever", "index": 0}]]}, "Set Google Drive file URL": {"main": [[{"node": "Google Drive", "type": "main", "index": 0}]]}, "Read Pinecone Vector Store": {"ai_vectorStore": [[{"node": "Vector Store Retriever", "type": "ai_vectorStore", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "When clicking 'Chat' button below": {"main": [[{"node": "Question and Answer Chain", "type": "main", "index": 0}]]}, "When clicking 'Test Workflow' button": {"main": [[{"node": "Set Google Drive file URL", "type": "main", "index": 0}]]}}}