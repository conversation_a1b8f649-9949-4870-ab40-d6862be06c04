{"name": "", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [450, 450], "parameters": {}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.wekan", "position": [650, 450], "parameters": {"owner": "c4nzTEvSwGPBxKTCc", "title": "Documentation", "resource": "board", "additionalFields": {}}, "credentials": {"wekanApi": "wekan-trial"}, "typeVersion": 1}, {"name": "Wekan1", "type": "n8n-nodes-base.wekan", "position": [850, 450], "parameters": {"title": "To Do", "boardId": "={{$node[\"Wekan\"].json[\"_id\"]}}", "resource": "list"}, "credentials": {"wekanApi": "wekan-trial"}, "typeVersion": 1}, {"name": "Wekan2", "type": "n8n-nodes-base.wekan", "position": [1050, 450], "parameters": {"title": "Done", "boardId": "={{$node[\"Wekan\"].json[\"_id\"]}}", "resource": "list"}, "credentials": {"wekanApi": "wekan-trial"}, "typeVersion": 1}, {"name": "Wekan3", "type": "n8n-nodes-base.wekan", "position": [1250, 450], "parameters": {"title": "Document Wekan node", "listId": "={{$node[\"Wekan1\"].json[\"_id\"]}}", "boardId": "={{$node[\"Wekan\"].json[\"_id\"]}}", "authorId": "c4nzTEvSwGPBxKTCc", "swimlaneId": "LDTcBp9fvmjSsSB69", "additionalFields": {}}, "credentials": {"wekanApi": "wekan-trial"}, "typeVersion": 1}, {"name": "Wekan4", "type": "n8n-nodes-base.wekan", "position": [1450, 450], "parameters": {"cardId": "={{$node[\"Wekan3\"].json[\"_id\"]}}", "listId": "={{$node[\"Wekan1\"].json[\"_id\"]}}", "boardId": "={{$node[\"Wekan\"].json[\"_id\"]}}", "operation": "update", "updateFields": {"listId": "={{$node[\"Wekan2\"].json[\"_id\"]}}"}}, "credentials": {"wekanApi": "wekan-trial"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Wekan": {"main": [[{"node": "Wekan1", "type": "main", "index": 0}]]}, "Wekan1": {"main": [[{"node": "Wekan2", "type": "main", "index": 0}]]}, "Wekan2": {"main": [[{"node": "Wekan3", "type": "main", "index": 0}]]}, "Wekan3": {"main": [[{"node": "Wekan4", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}}}