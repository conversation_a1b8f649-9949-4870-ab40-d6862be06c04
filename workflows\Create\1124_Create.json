{"nodes": [{"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.function", "position": [670, 371], "parameters": {"functionCode": "return [\n  {\n    json:[\n      {\n        id: 1,\n        name: \"<PERSON>\"\n      }, \n      {\n        id: 2,\n        name: \"<PERSON>\"\n      },\n      {\n        id: 3,\n        name: \"<PERSON>\"\n      }\n    ]\n  }\n];"}, "typeVersion": 1}, {"name": "Create JSON-items", "type": "n8n-nodes-base.function", "position": [910, 371], "parameters": {"functionCode": "return items[0].json.map(item => { \n  return {\n    json: item,\n  }\n})\n"}, "typeVersion": 1}], "connections": {"Mock Data": {"main": [[{"node": "Create JSON-items", "type": "main", "index": 0}]]}}}