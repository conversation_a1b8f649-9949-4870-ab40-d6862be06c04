{"meta": {"instanceId": "dd130a849d7b29e5541b05d2f7f86a4acd4f1ec598c1c9438783f56bc4f0ff80", "templateCredsSetupCompleted": true}, "nodes": [{"id": "07433551-9fa9-421c-a0bf-721fa1624304", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [-1380, -320], "parameters": {"color": 4, "width": 1075, "height": 736, "content": "## Developed by <PERSON><PERSON>\n\nThank you for using this workflow template. It has taken me countless hours of hard work, research, and dedication to develop, and I sincerely hope it adds value to your work.\n\nIf you find this template helpful, I kindly ask you to consider supporting my efforts. Your support will help me continue improving and creating more valuable resources.\n\nYou can contribute via PayPal here:\n\nhttp://paypal.me/pmptraining\n\nFor Full Course about ERPNext or Automation using AI follow below link\n\nhttp://lms.syncbricks.com\n\nAdditionally, when sharing this template, I would greatly appreciate it if you include my original information to ensure proper credit is given.\n\nThank you for your generosity and support!\nEmail : <EMAIL>\nhttps://linkedin.com/in/amjidali\nhttps://syncbricks.com\nhttps://youtube.com/@syncbricks"}, "typeVersion": 1}, {"id": "31a0c5e9-c6f6-4921-8f92-be84cc669869", "name": "On form submission", "type": "n8n-nodes-base.formTrigger", "position": [-220, -80], "webhookId": "syncbricks-youtube-meta-automation", "parameters": {"options": {"buttonLabel": "Update Youtube Video"}, "formTitle": "Syncbricks Youtube", "formFields": {"values": [{"fieldLabel": "Youtube Video Link", "requiredField": true}, {"fieldLabel": "Video Transcript", "requiredField": true}, {"fieldLabel": "Focus Keywords", "placeholder": "Focus Keywords (Optional)"}]}, "formDescription": "Generate Youtube Video Title, Description, Tags and Hashtags"}, "typeVersion": 2.2}, {"id": "d3c5df7c-2b57-4136-a790-cff68a03a2f1", "name": "syncbricks information", "type": "n8n-nodes-base.googleDocsTool", "position": [240, 260], "parameters": {"operation": "get", "documentURL": "15lN3FJ3iXABf_bd061-F7j-gGx2WBH8Jr6fjBLa3tis", "descriptionType": "manual", "toolDescription": "affiliate links, course links, social media links and other relevant links related to syncbricks"}, "typeVersion": 2}, {"id": "e31ea741-3b99-4b3b-9b44-9dcca69f6384", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [40, 200], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "typeVersion": 1.2}, {"id": "07b23889-9c41-4202-a41b-350cca850e63", "name": "Extract Video ID", "type": "n8n-nodes-base.set", "position": [540, -80], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "f7b24e9b-cc50-4e4e-8073-aa555aaa5a03", "name": "=videoID", "type": "string", "value": "={{ $('On form submission').item.json['Youtube Video Link'].replace(\"https://youtu.be/\",\"\") }}"}]}}, "typeVersion": 3.4}, {"id": "44226e96-2429-497d-b84b-f3752f441b8b", "name": "Youtube Meta Generator", "type": "@n8n/n8n-nodes-langchain.agent", "position": [120, -80], "parameters": {"text": "=You are an AI content generator specialized in crafting high-converting YouTube metadata for videos related to stores, shops, memberships, and business promotions. Your task is to generate a **structured JSON output** optimized for YouTube SEO and audience engagement based on the provided video transcript and focus keywords. Use \"syncbricks information\" tool to collect relevant social media, courses, website and affilite links and ensure to add the relavent links in description. Major course or affilite link should be used as hook in the beginning of the description.\n\n### **Output Requirements:**\n1. **Title:** A compelling, SEO-friendly title optimized for search and audience interest.\n2. **Description:** A detailed, keyword-rich summary of the video, incorporating relevant keywords naturally and including a clear value proposition.\n3. **Keywords:** Single line of all possible keywords with  at least 450 characters in total with comma in between each keyword relevant to the video content to enhance discoverability.\n4. **Hashtags:** Single line of 5 to 10 relevant hashtags, without the comma that align with the video's theme.\n5. **Affiliate Links:** Contextually relevant affiliate links that match the video content. Only provide link don't add unnessary boxes.\n7. **Call to Action (CTA):** A persuasive CTA encouraging viewers to **subscribe, like, share, visit a store, or sign up for membership**.\n8. **Additional Promotional Links:** Gather and include relevant **course links, business website links, or related references** that add value to the audience.\n9. **Channel Hashtags:** Append **#EnterpriseIT #BusinessIntelligence #TechSolutions #ITInsights #HomeLab #Gadgets #TechReview #ITTips #SyncBricks #AmjidAli** at the end of the description.\n\n### **Instructions:**\n- Ensure that **affiliate links are directly related** to the video topic.\n- Use **natural language and avoid keyword stuffing** to maintain a user-friendly tone.\n- Don't add social media profiles, and syncbricks websit link, only add the affilaite and promotion links\n- The description should be **at least 150 words properly formatted with lines and paragraphs** for better YouTube SEO.\n - Avoid adding [] brackets\n- Structure the output in a **well-formatted JSON format** for automation.\n\n##Example of Affialite and promotion Links ##\nn8n : https://n8n.syncbricks.com\nFull Course : https://proxmox.syncbricks.com or udemy link\n\n\n### **Here is the existing Video Details:**\n- **Transcript:** {{ $json['Video Transcript'] }}\n- **Focus Keywords:** {{ $json['Focus Keywords'] }}", "options": {"maxIterations": 10}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.7}, {"id": "727cdc7f-e783-4d98-8476-a1623310a1fc", "name": "YouTube", "type": "n8n-nodes-base.youTube", "position": [740, 60], "parameters": {"title": "={{ $('Youtube Meta Generator').item.json.output.youtube_metadata.title }}", "videoId": "={{ $('Extract Video ID').item.json.videoID }}", "resource": "video", "operation": "update", "categoryId": "28", "regionCode": "OM", "updateFields": {"tags": "={{ $json.formatted_tags }}", "description": "={{ $('Youtube Meta Generator').item.json.output.youtube_metadata.description }}\n\nConnect with us : \nFacebook: https://www.facebook.com/syncbricks\nLinkedIn : https://linkedin.com/company/syncbricks\nInstagram : https://instagram.com/syncbricks_com\n\nSubscribe to youtube Channel : https://www.youtube.com/channel/UC1ORA3oNGYuQ8yQHrC7MzBg?sub_confirmation=1\n\nWebsite : \nSync Bricks: https://syncbricks.com/\n\nContact : <EMAIL>\n\n{{ $('Youtube Meta Generator').item.json.output.youtube_metadata.call_to_action }}\n\n{{ $('Youtube Meta Generator').item.json.output.youtube_metadata.hashtags }}\n\n\n"}}, "typeVersion": 1}, {"id": "631fbe64-2851-42f0-8657-ddd501abcd34", "name": "Format Tags", "type": "n8n-nodes-base.set", "position": [540, 160], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "10cbc535-36a3-4973-a038-ead1b3525a7c", "name": "formatted_tags", "type": "string", "value": "={{ $('Youtube Meta Generator').item.json.output.youtube_metadata.tags.join() }}"}]}}, "typeVersion": 3.4}, {"id": "e75c1fe1-eb58-4fb1-bcc9-ed969eb62a99", "name": "Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [400, 240], "parameters": {"schemaType": "manual", "inputSchema": "{\n\t\"type\": \"object\",\n\t\"properties\": {\n\t\t\"video_title\": {\n\t\t\t\"type\": \"string\"\n\t\t},\n\t\t\"video_description\": {\n\t\t\t\"type\": \"string\"\n\t\t},\n\t\t\"youtube_metadata\": {\n\t\t\t\"type\": \"object\",\n\t\t\t\"properties\": {\n\t\t\t\t\"title\": {\n\t\t\t\t\t\"type\": \"string\"\n\t\t\t\t},\n\t\t\t\t\"description\": {\n\t\t\t\t\t\"type\": \"string\"\n\t\t\t\t},\n\t\t\t\t\"tags\": {\n\t\t\t\t\t\"type\": \"array\",\n\t\t\t\t\t\"items\": {\n\t\t\t\t\t\t\"type\": \"string\"\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t\"hashtags\": {\n\t\t\t\t\t\"type\": \"array\",\n\t\t\t\t\t\"items\": {\n\t\t\t\t\t\t\"type\": \"string\"\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t\"call_to_action\": {\n\t\t\t\t\t\"type\": \"string\"\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t\"additional_notes\": {\n\t\t\t\"type\": \"string\"\n\t\t}\n\t}\n}\n"}, "typeVersion": 1.2}, {"id": "91327309-8d6a-4e46-8516-726916acd3f4", "name": "Form", "type": "n8n-nodes-base.form", "position": [940, 60], "webhookId": "6557e699-8774-475d-a1df-7b0b24e4cb3b", "parameters": {"options": {}, "operation": "completion", "completionTitle": "={{ $json.snippet.title }}", "completionMessage": "=Video is updated with Title : {{ $json.snippet.title }} and below is the video link\n{{ $('On form submission').item.json['Youtube Video Link'] }}"}, "typeVersion": 1}, {"id": "3ac5dc27-ccb4-470e-b49c-95198bba91e0", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-260, -320], "parameters": {"color": 3, "width": 1435, "height": 736, "content": "##Youtube Meta Generator \n\nCustomize it for yoru own youtube channel\n\n\n"}, "typeVersion": 1}], "pinData": {}, "connections": {"YouTube": {"main": [[{"node": "Form", "type": "main", "index": 0}]]}, "Format Tags": {"main": [[{"node": "YouTube", "type": "main", "index": 0}]]}, "Output Parser": {"ai_outputParser": [[{"node": "Youtube Meta Generator", "type": "ai_outputParser", "index": 0}]]}, "Extract Video ID": {"main": [[{"node": "Format Tags", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Youtube Meta Generator", "type": "ai_languageModel", "index": 0}]]}, "On form submission": {"main": [[{"node": "Youtube Meta Generator", "type": "main", "index": 0}]]}, "Youtube Meta Generator": {"main": [[{"node": "Extract Video ID", "type": "main", "index": 0}]]}, "syncbricks information": {"ai_tool": [[{"node": "Youtube Meta Generator", "type": "ai_tool", "index": 0}]]}}}