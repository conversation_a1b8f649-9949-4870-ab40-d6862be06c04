{"meta": {"instanceId": "03e9d14e9196363fe7191ce21dc0bb17387a6e755dcc9acc4f5904752919dca8"}, "nodes": [{"id": "71dd0b5e-7bb0-4d06-9769-753156d1acf3", "name": "TheH<PERSON> Trigger", "type": "n8n-nodes-base.theHiveProjectTrigger", "position": [20, 300], "webhookId": "23c014ae-1191-4775-9c00-69e5e014b11d", "parameters": {"events": ["case_create"], "options": {}}, "typeVersion": 1}, {"id": "659339a5-3b3e-4f57-8aec-4c368b01890c", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [280, 520], "parameters": {"color": 7, "width": 455.1635858314854, "height": 538.2816200839441, "content": "![slack](https://uploads.n8n.io/templates/slack.png)\n## Events Webhook Trigger\nThe first node receives all messages from Slack API via Subscription Events API. You can find more information about setting up the subscription events API by [clicking here](https://api.slack.com/apis/connections/events-api). The second node extracts the payload from slack into an object that n8n can understand.  "}, "typeVersion": 1}, {"id": "c3ec7525-41f5-426c-a1c9-63b4596c8632", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-160, -60], "parameters": {"color": 7, "width": 543.689530484159, "height": 516.5011820330969, "content": "![theHive](https://uploads.n8n.io/templates/thehive.png)\n## TheHive Trigger\nTo setup TheHive 5's triggers, visit the Settings in TheHive and add a webhook using the url found TheHive node."}, "typeVersion": 1}, {"id": "d754ebe1-8a9e-4ae2-9a51-b6d359866ec3", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [400, -60], "parameters": {"color": 7, "width": 686.8181233897246, "height": 516.5011820330969, "content": "![Slack](https://uploads.n8n.io/templates/slack.png)\n## Post TheHive Case to Slack\nThe Case data is then mapped to Slack Block Kit and pushed to Slack. In Slack, it lives as a database object, mapping the Case Id to the block kit values themselves, so they can be updated in TheHive. "}, "typeVersion": 1}, {"id": "6ac8fc89-6640-4799-93cb-eb1afa0e35c9", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [580, 880], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "e63f9299-a19d-4ba1-93b0-59f458769fb2", "name": "response", "type": "object", "value": "={{ $json.body.payload }}"}, {"id": "d95dda30-0a5c-4c63-8b4a-6b4e0a2bcb59", "name": "dictionary", "type": "object", "value": "{\"PAP\":{\"WHITE\":\"🕊️\",\"GREEN\":\"🟢\",\"AMBER\":\"🟠\",\"RED\":\"🔴\"},\"Severity\":{\"LOW\":\"🟢\",\"MEDIUM\":\"🟡\",\"HIGH\":\"🟠\",\"CRITICAL\":\"🔴\"},\"Severityid\":{\"1\":\"LOW\",\"2\":\"MEDIUM\",\"3\":\"HIGH\",\"4\":\"CRITICAL\"},\"TLP\":{\"WHITE\":\"⚪\",\"GREEN\":\"🟢\",\"AMBER\":\"🟠\",\"RED\":\"🔴\"},\"STATUS\":{\"1\":\"New\",\"2\":\"InProgress\",\"3\":\"Duplicated\",\"4\":\"FalsePositive\",\"5\":\"Indeterminate\",\"6\":\"Other\",\"7\":\"TruePositive\"},\"STATUSEMOJI\":{\"1\":\"🆕\",\"2\":\"🔄\",\"3\":\"📑\",\"4\":\"❎\",\"5\":\"❓\",\"6\":\"🟣\",\"7\":\"🔴\"}}"}, {"id": "2303d403-1329-47b4-9b74-4f679a2cc192", "name": "theHiveUrl", "type": "string", "value": "http://37.27.1.230:9000"}]}}, "typeVersion": 3.3}, {"id": "86f90f83-c115-4561-8c0f-432c2ebb18b5", "name": "Task Modal", "type": "n8n-nodes-base.httpRequest", "position": [1800, 3040], "parameters": {"url": "https://slack.com/api/views.open", "method": "POST", "options": {}, "jsonBody": "=  {\n    \"trigger_id\": \"{{ $('Edit Fields').item.json['response']['trigger_id'] }}\",\n    \"external_id\": \"TheHive Task Adder\",\n    \"view\": {\n\t\"type\": \"modal\",\n\t\"callback_id\": \"add_task_modal\",\n\t\"title\": {\n\t\t\"type\": \"plain_text\",\n\t\t\"text\": \"Add a Task to Case\",\n\t\t\"emoji\": true\n\t},\n\t\"submit\": {\n\t\t\"type\": \"plain_text\",\n\t\t\"text\": \"Submit\",\n\t\t\"emoji\": true\n\t},\n\t\"close\": {\n\t\t\"type\": \"plain_text\",\n\t\t\"text\": \"Cancel\",\n\t\t\"emoji\": true\n\t},\n\t\"blocks\": [\n\t\t{\n\t\t\t\"type\": \"context\",\n\t\t\t\"block_id\": \"case_number_context\",\n\t\t\t\"elements\": [\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\t\"text\": \"{{ $('Edit Fields').item.json[\"response\"][\"message\"][\"blocks\"][1][\"text\"][\"text\"] }}\"\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\"text\": \"{{ $('Edit Fields').item.json.response.message.blocks[0].alt_text }}\",\n\t\t\t\t\t\"emoji\": true\n\t\t\t\t}\n\t\t\t]\n\t\t},\n\t\t{\n\t\t\t\"type\": \"input\",\n\t\t\t\"block_id\": \"title_block\",\n\t\t\t\"element\": {\n\t\t\t\t\"type\": \"plain_text_input\",\n\t\t\t\t\"action_id\": \"title_input\",\n\t\t\t\t\"placeholder\": {\n\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\"text\": \"Enter the task title\"\n\t\t\t\t}\n\t\t\t},\n\t\t\t\"label\": {\n\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\"text\": \"Title\",\n\t\t\t\t\"emoji\": true\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\t\"type\": \"input\",\n\t\t\t\"block_id\": \"description_block\",\n\t\t\t\"element\": {\n\t\t\t\t\"type\": \"plain_text_input\",\n\t\t\t\t\"action_id\": \"description_input\",\n\t\t\t\t\"multiline\": true,\n\t\t\t\t\"placeholder\": {\n\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\"text\": \"Enter a description for the task\"\n\t\t\t\t}\n\t\t\t},\n\t\t\t\"label\": {\n\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\"text\": \"Description\",\n\t\t\t\t\"emoji\": true\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\t\"type\": \"input\",\n\t\t\t\"block_id\": \"group_block\",\n\t\t\t\"element\": {\n\t\t\t\t\"type\": \"plain_text_input\",\n\t\t\t\t\"action_id\": \"group_input\",\n\t\t\t\t\"placeholder\": {\n\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\"text\": \"Enter the group name\"\n\t\t\t\t}\n\t\t\t},\n\t\t\t\"label\": {\n\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\"text\": \"Group\",\n\t\t\t\t\"emoji\": true\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\t\"type\": \"actions\",\n\t\t\t\"block_id\": \"case-options\",\n\t\t\t\"elements\": [\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"checkboxes\",\n\t\t\t\t\t\"options\": [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"text\": {\n\t\t\t\t\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\t\t\t\t\"text\": \":exclamation: *Mandatory*\"\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\"description\": {\n\t\t\t\t\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\t\t\t\t\"text\": \"Make this case Mandatory\"\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\"value\": \"mandatory\"\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"text\": {\n\t\t\t\t\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\t\t\t\t\"text\": \":triangular_flag_on_post: *Flagged*\"\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\"description\": {\n\t\t\t\t\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\t\t\t\t\"text\": \"Flag this case\"\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\"value\": \"flagged\"\n\t\t\t\t\t\t}\n\t\t\t\t\t],\n\t\t\t\t\t\"action_id\": \"submit-task-option\"\n\t\t\t\t}\n\t\t\t]\n\t\t},\n\t\t{\n\t\t\t\"type\": \"input\",\n\t\t\t\"block_id\": \"due_date_block\",\n\t\t\t\"element\": {\n\t\t\t\t\"type\": \"datepicker\",\n\t\t\t\t\"action_id\": \"due_date_input\",\n\t\t\t\t\"placeholder\": {\n\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\"text\": \"Select a due date and time\"\n\t\t\t\t},\n\t\t\t\t\"initial_date\": \"{{$today.format('yyyy-MM-dd')}}\"\n\t\t\t},\n\t\t\t\"label\": {\n\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\"text\": \"Due Date\",\n\t\t\t\t\"emoji\": true\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\t\"type\": \"input\",\n\t\t\t\"block_id\": \"assignee_block\",\n\t\t\t\"element\": {\n\t\t\t\t\"type\": \"users_select\",\n\t\t\t\t\"action_id\": \"assignee_input\",\n\t\t\t\t\"placeholder\": {\n\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\"text\": \"Select an assignee\"\n\t\t\t\t}\n\t\t\t},\n\t\t\t\"label\": {\n\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\"text\": \"Assignee\",\n\t\t\t\t\"emoji\": true\n\t\t\t}\n\t\t}\n\t]\n}\n}", "sendBody": true, "jsonQuery": "{\n  \"Content-type\": \"application/json\"\n}", "sendQuery": true, "specifyBody": "json", "specifyQuery": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "slackApi"}, "credentials": {"slackApi": {"id": "hOkN2lZmH8XimxKh", "name": "TheHive Slack App"}}, "typeVersion": 4.2}, {"id": "e2874195-4b4e-4288-937f-201e4a361438", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [460, 300], "parameters": {"url": "https://slack.com/api/users.lookupByEmail", "options": {}, "sendQuery": true, "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "email", "value": "={{ $('TheHive Trigger').item.json[\"body\"][\"object\"][\"assignee\"] }}"}]}, "nodeCredentialType": "slackApi"}, "credentials": {"slackApi": {"id": "hOkN2lZmH8XimxKh", "name": "TheHive Slack App"}}, "typeVersion": 4.2}, {"id": "9fbf2b59-e6c3-45b5-aceb-5e98ebad9814", "name": "Formatting Dictionaries", "type": "n8n-nodes-base.set", "position": [240, 300], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "4a0f7bf9-f346-4ccf-8026-79c984cdedb5", "name": "dictionary", "type": "object", "value": "{\"PAP\":{\"CLEAR\":\"🕊️\",\"GREEN\":\"🟢\",\"AMBER\":\"🟠\",\"RED\":\"🔴\"},\"Severity\":{\"LOW\":\"🟢\",\"MEDIUM\":\"🟡\",\"HIGH\":\"🟠\",\"CRITICAL\":\"🔴\"},\"TLP\":{\"CLEAR\":\"⚪\",\"GREEN\":\"🟢\",\"AMBER\":\"🟠\",\"RED\":\"🔴\"},\"STATUS\":{\"NEW\":\"🆕\",\"inprogress\":\"🔄\",\"Duplicated\":\"📑\",\"Falsepositive\":\"❎\",\"Indeterminate\":\"❓\",\"Other\":\"🟣\",\"Truepositive\":\"🔴\"}}"}, {"id": "32a165d5-cd94-454e-bcf9-9254decb63cb", "name": "theHiveUrl", "type": "string", "value": "=http://37.27.1.230:9000"}]}}, "typeVersion": 3.3}, {"id": "f5ab276e-809b-41df-ba86-020c1b3681e1", "name": "Pre<PERSON> Fields For Slack", "type": "n8n-nodes-base.set", "position": [680, 300], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "0001aed3-a3fb-4229-8fa1-1941f0ee8a12", "name": "title", "type": "string", "value": "=:computer: *Case Title:*\\n {{ $('TheHive Trigger').item.json.body.details.title }}"}, {"id": "6f40871c-ea6f-4c61-9272-9342c99637e8", "name": "datecreated", "type": "string", "value": "=*:date: Date Created:*\\n{{DateTime.fromMillis($('TheHive Trigger').item.json[\"body\"][\"context\"][\"_createdAt\"]).toLocaleString({month: 'long', day: 'numeric', year: 'numeric'})}}"}, {"id": "2ea20d66-380f-4a0f-a1ea-c4740293f48b", "name": "number", "type": "string", "value": "=:hash: Case #{{ $('TheHive Trigger').item.json.body.details.number }} created on TheHive "}, {"id": "a22dd686-ac38-4e73-a0ec-051dce57f9fa", "name": "severity", "type": "string", "value": "=*Severity:* {{ $('TheHive Trigger').item.json[\"body\"][\"context\"][\"severityLabel\"] }}"}, {"id": "1c210cfb-2a03-4e81-a8c5-2db739d98226", "name": "tlp", "type": "string", "value": "=*Traffic Light Protocol(TLP):* {{ $('TheHive Trigger').item.json[\"body\"][\"context\"][\"tlpLabel\"] }}"}, {"id": "53e1d1a9-055a-48c2-80de-b694871c6620", "name": "pap", "type": "string", "value": "=*Permissible Actions Protocol(PAP):* {{ $('TheHive Trigger').item.json[\"body\"][\"context\"][\"papLabel\"] }}"}, {"id": "ee630583-a6ad-4bab-ad78-7846df3ac093", "name": "description", "type": "string", "value": "=:spiral_note_pad: *Case Description:*\\n{{ $('TheHive Trigger').item.json[\"body\"][\"context\"][\"description\"] }}"}, {"id": "48ac7d92-5177-46be-b01e-83493f18ee09", "name": "assignee", "type": "string", "value": "={{ $json.user.profile.real_name }}"}, {"id": "f9c06ecf-d3d5-490c-a848-02c88fbd3ab4", "name": "profilepic", "type": "string", "value": "={{ $json.user.profile.image_32 }}"}, {"id": "bb4ec29d-fac2-4eb9-a177-fca9d2798514", "name": "caseid", "type": "string", "value": "={{ $('TheHive Trigger').item.json[\"body\"][\"objectId\"] }}"}, {"id": "7a485f44-b855-4633-ba51-82e7490d7166", "name": "tags", "type": "string", "value": "=:label:  *Tags:*\\n{{ $('TheHive Trigger').item.json[\"body\"][\"context\"][\"tags\"].join(', ') }}"}, {"id": "a99d49b4-af10-4e04-a9c6-2b17a14643ff", "name": "status_emoji", "type": "string", "value": "={{ $(`Formatting Dictionaries`).item.json.dictionary.STATUS[$('TheHive Trigger').item.json[\"body\"][\"context\"][\"status\"].toUpperCase()] }}"}, {"id": "a4884fc5-2d92-4c34-af9c-61240bc564d5", "name": "status", "type": "string", "value": "={{ $('TheHive Trigger').item.json[\"body\"][\"context\"][\"status\"] }}"}, {"id": "7efb073b-1f1a-4d11-97a2-d61a9baeb52f", "name": "tlp_emoji", "type": "string", "value": "={{ $(`Formatting Dictionaries`).item.json.dictionary.TLP[$('TheHive Trigger').item.json[\"body\"][\"context\"][\"tlpLabel\"].toUpperCase()] }}"}, {"id": "19f5bc67-97c3-47bc-8210-738057f30c1f", "name": "pap_emoji", "type": "string", "value": "={{ $(`Formatting Dictionaries`).item.json.dictionary.PAP[$('TheHive Trigger').item.json[\"body\"][\"context\"][\"papLabel\"].toUpperCase()] }}"}, {"id": "d31504be-e4ef-4704-8b1b-cb1a8072298d", "name": "severity_emoji", "type": "string", "value": "={{ $(`Formatting Dictionaries`).item.json.dictionary.Severity[$('TheHive Trigger').item.json[\"body\"][\"context\"][\"severityLabel\"].toUpperCase()] }}"}]}}, "typeVersion": 3.3}, {"id": "b7747c0c-52ec-415b-aa55-ce399b53054b", "name": "Update Message with new As<PERSON>ee", "type": "n8n-nodes-base.httpRequest", "position": [3200, 1160], "parameters": {"url": "https://slack.com/api/chat.update", "method": "POST", "options": {}, "sendBody": true, "sendQuery": true, "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "channel", "value": "={{ $('Edit Fields').item.json.response.channel.id }}"}, {"name": "ts", "value": "={{ $('Edit Fields').item.json.response.container.message_ts }}"}, {"name": "blocks", "value": "={{ JSON.stringify($json.blocks) }}"}]}, "queryParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "nodeCredentialType": "slackApi"}, "credentials": {"slackApi": {"id": "hOkN2lZmH8XimxKh", "name": "TheHive Slack App"}}, "typeVersion": 4.2}, {"id": "b548c331-1612-40a9-806c-8136c1eb6dbe", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1140, 385.7161688656365], "parameters": {"color": 7, "width": 706.9954015609856, "height": 467.56934570944634, "content": "![Slack](https://uploads.n8n.io/templates/slack.png)\n## Assign Case to Others\nSince slack does not send the slack user email, we must take the extra step \nof passing the user ID back to slack, and getting back the email address of \nthe person selecting the buttons. For this to work correctly, TheHive users \nemails and Slack user emails must be the same. "}, "typeVersion": 1}, {"id": "34dc3eb9-e19a-46c1-947d-65c9c5be9993", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1140, 860], "parameters": {"color": 7, "width": 1400.2199299541444, "height": 1720.4067726820667, "content": "![theHive](https://uploads.n8n.io/templates/thehive.png)\n# Streamlined Case Updates via Slack for SOC Analysts\n\n## This workflow segment is crucial for enhancing the responsiveness and efficiency of SOC operations by allowing analysts to update TheHive case attributes directly from Slack. By using form inputs and buttons embedded within Slack messages, analysts can quickly modify case details without switching to TheHive. This integration significantly speeds up case handling and improves the management of security incidents.\n\n\n**Key Operations:**\n- **Dynamic Slack Blocks**: Utilizes custom-built Slack block elements to present actionable items directly within the communication platform.\n- **Immediate Actions**: Enables actions like closing a case as a false positive, updating case severity, TLP (Traffic Light Protocol), PAP (Permissible Action Protocol), and more with simple clicks.\n- **Automated Updates**: Upon interaction, the workflow automatically updates the respective fields in TheHive, ensuring that all case information is current and accurately reflected.\n- **Feedback Loop**: After updating, a confirmation is sent back to Slack, confirming the action taken, which helps in maintaining clear communication and audit trails.\n\n\n**Benefits for SOC Analysts:**\n- **Efficiency**: Reduces the time spent switching between tools and streamlines the case update process.\n- **Accuracy**: Minimizes human error by automating data entry from Slack to TheHive.\n- **Visibility**: Keeps the entire team informed about case status updates in real-time, enhancing collaborative efforts.\n\n\n**Setup Note:**\n- Ensure that your TheHive and Slack integration permissions are configured to allow updates and that users are familiar with the operational workflow.\n- The last set of \"Set\" nodes on the far right of the workflow are designed to split the Slack block kit action into two parts. This split facilitates easier customization of the action options at the bottom of the Slack message. While customization is possible, a good understanding of JSON is desirable to modify these settings effectively.\n\n\nThis integration not only speeds up response times but also leverages the collaborative environment of Slack to keep the SOC team agile and well-informed."}, "typeVersion": 1}, {"id": "233924c6-eceb-4618-90fa-2633f0e3cf84", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [1135, 3242], "parameters": {"color": 7, "width": 988.1220012094333, "height": 613.5055433060886, "content": "![slack](https://uploads.n8n.io/templates/slack.png)\n## Add a task to a Case - Process the task details\nOnce the modal window is submitted, this sections then processes the data sent in. Certain fields send data back to the server so the no operation check simply ignores certain types of inputs in the modal until the whole modal is filled out. "}, "typeVersion": 1}, {"id": "c32929b9-9f57-4f70-86da-e038c786d360", "name": "Check if Case Options", "type": "n8n-nodes-base.if", "position": [1175, 3560], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "de563083-5735-42d3-8765-5b072b10ff9c", "operator": {"type": "string", "operation": "notEquals"}, "leftValue": "={{ $json.response.actions[0].action_id }}", "rightValue": "submit-task-option"}]}}, "typeVersion": 2}, {"id": "5887da0e-36eb-4bd3-8dba-6422dc8fa70a", "name": "Case Slack Block Rebuild", "type": "n8n-nodes-base.set", "position": [2340, 680], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c1fccf83-8223-42a2-837e-11eafe5d23fd", "name": "slackblocks", "type": "string", "value": "={\"type\":\"image\",\"block_id\":\"image_block\",\"image_url\":\"https://i.imgur.com/y2Yw1ZP.png\",\"alt_text\":\"{{ $('Prep Fields For Slack - Assign').item.json[\"caseid\"]}}\"},{\"type\":\"header\",\"block_id\":\"header_case_created\",\"text\":{\"type\":\"plain_text\",\"text\":\"{{ $('Prep Fields For Slack - Assign').item.json[\"number\"]}}\",\"emoji\":true}},{\"type\":\"divider\",\"block_id\":\"divider_1\"},{\"type\":\"section\",\"block_id\":\"section_case_details\",\"fields\":[{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Slack - Assign').item.json[\"title\"].replace(\"\\n\",\"\\\\n\")}}\"},{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Slack - Assign').item.json[\"datecreated\"].replace(\"\\n\",\"\\\\n\")}}\"},{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Slack - Assign').item.json[\"tags\"].replace(\"\\n\",\"\\\\n\")}}\"},{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Slack - Assign').item.json[\"description\"].replace(\"\\n\",\"\\\\n\")}}\"}]},{\"type\":\"actions\",\"block_id\":\"actions_case_options\",\"elements\":[{\"type\":\"button\",\"text\":{\"type\":\"plain_text\",\"text\":\"Close Case as False Positive\",\"emoji\":true},\"style\":\"danger\",\"value\":\"{{ $('Prep Fields For Slack - Assign').item.json[\"caseid\"]}}\",\"action_id\":\"close_case\"},{\"type\":\"button\",\"text\":{\"type\":\"plain_text\",\"text\":\"Add a Task\",\"emoji\":true},\"value\":\"{{ $('Prep Fields For Slack - Assign').item.json[\"caseid\"]}}\",\"action_id\":\"add_task\"}]},{\"type\":\"divider\",\"block_id\":\"divider_2\"},{\"type\":\"header\",\"block_id\":\"header_current_assignment\",\"text\":{\"type\":\"plain_text\",\"text\":\"🔍 Case Details:\",\"emoji\":true}},{\"type\":\"context\",\"block_id\":\"context_status\",\"elements\":[{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Slack - Assign').item.json[\"status\"]}}\"}]},{\"type\":\"context\",\"block_id\":\"context_severity\",\"elements\":[{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Slack - Assign').item.json[\"severity\"]}}\"}]},{\"type\":\"context\",\"block_id\":\"context_tlp\",\"elements\":[{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Slack - Assign').item.json[\"tlp\"]}}\"}]},{\"type\":\"context\",\"block_id\":\"context_pap\",\"elements\":[{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Slack - Assign').item.json[\"pap\"]}}\"}]},{\"type\":\"context\",\"block_id\":\"context_assignee\",\"elements\":[{\"type\":\"image\",\"image_url\":\"{{ $('Get Slack User\\'s Email From Slack').item.json.profile.image_32 }}\",\"alt_text\":\"Profile Pic\"},{\"type\":\"mrkdwn\",\"text\":\"Assignee: {{ $('Get Slack User\\'s Email From Slack').item.json.profile.real_name }}\"}]}"}]}}, "typeVersion": 3.3}, {"id": "0dc10f84-c087-45ab-8b1d-f770725f400b", "name": "Close Case Block Rebuild", "type": "n8n-nodes-base.set", "position": [2400, 1700], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c1fccf83-8223-42a2-837e-11eafe5d23fd", "name": "slackblocks", "type": "string", "value": "={\"type\":\"image\",\"block_id\":\"image_block\",\"image_url\":\"https://i.imgur.com/y2Yw1ZP.png\",\"alt_text\":\"{{ $('Prep Fields For Slack - Close').item.json[\"caseid\"]}}\"},{\"type\":\"header\",\"block_id\":\"header_case_created\",\"text\":{\"type\":\"plain_text\",\"text\":\"{{ $('Prep Fields For Slack - Close').item.json[\"number\"]}}\",\"emoji\":true}},{\"type\":\"divider\",\"block_id\":\"divider_1\"},{\"type\":\"section\",\"block_id\":\"section_case_details\",\"fields\":[{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Slack - Close').item.json[\"title\"].replace(\"\\n\",\"\\\\n\")}}\"},{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Slack - Close').item.json[\"datecreated\"].replace(\"\\n\",\"\\\\n\")}}\"},{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Slack - Close').item.json[\"tags\"].replace(\"\\n\",\"\\\\n\")}}\"},{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Slack - Close').item.json[\"description\"].replace(\"\\n\",\"\\\\n\")}}\"}]},{\"type\":\"actions\",\"block_id\":\"actions_case_options\",\"elements\":[{\"type\":\"button\",\"text\":{\"type\":\"plain_text\",\"text\":\"Close Case as False Positive\",\"emoji\":true},\"style\":\"danger\",\"value\":\"{{ $('Prep Fields For Slack - Close').item.json[\"caseid\"]}}\",\"action_id\":\"close_case\"},{\"type\":\"button\",\"text\":{\"type\":\"plain_text\",\"text\":\"Add a Task\",\"emoji\":true},\"value\":\"{{ $('Prep Fields For Slack - Close').item.json[\"caseid\"]}}\",\"action_id\":\"add_task\"}]},{\"type\":\"divider\",\"block_id\":\"divider_2\"},{\"type\":\"header\",\"block_id\":\"header_current_assignment\",\"text\":{\"type\":\"plain_text\",\"text\":\"🔍 Case Details:\",\"emoji\":true}},{\"type\":\"context\",\"block_id\":\"context_status\",\"elements\":[{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Slack - Close').item.json[\"status\"]}}\"}]},{\"type\":\"context\",\"block_id\":\"context_severity\",\"elements\":[{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Slack - Close').item.json[\"severity\"]}}\"}]},{\"type\":\"context\",\"block_id\":\"context_tlp\",\"elements\":[{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Slack - Close').item.json[\"tlp\"]}}\"}]},{\"type\":\"context\",\"block_id\":\"context_pap\",\"elements\":[{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Slack - Close').item.json[\"pap\"]}}\"}]},{\"type\":\"context\",\"block_id\":\"context_assignee\",\"elements\":[{\"type\":\"image\",\"image_url\":\"{{ $('Prep Fields For Slack - Close').item.json[\"profilepic\"]}}\",\"alt_text\":\"Profile Pic\"},{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Slack - Close').item.json[\"assignee\"] }}\"}]}"}]}}, "typeVersion": 3.3}, {"id": "3b95146f-d842-4c69-b537-bad5603ba0d0", "name": "Severity Case Block Rebuild1", "type": "n8n-nodes-base.set", "position": [2400, 1880], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c1fccf83-8223-42a2-837e-11eafe5d23fd", "name": "slackblocks", "type": "string", "value": "={\"type\":\"image\",\"block_id\":\"image_block\",\"image_url\":\"https://i.imgur.com/y2Yw1ZP.png\",\"alt_text\":\"{{ $('Prep Fields For Slack - Severity').item.json['caseid']}}\"},{\"type\":\"header\",\"block_id\":\"header_case_created\",\"text\":{\"type\":\"plain_text\",\"text\":\"{{ $('Prep Fields For Slack - Severity').item.json['number']}}\",\"emoji\":true}},{\"type\":\"divider\",\"block_id\":\"divider_1\"},{\"type\":\"section\",\"block_id\":\"section_case_details\",\"fields\":[{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Slack - Severity').item.json['title'].replace('\\n','\\\\n')}}\"},{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Slack - Severity').item.json['datecreated'].replace('\\n','\\\\n')}}\"},{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Slack - Severity').item.json['tags'].replace('\\n','\\\\n')}}\"},{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Slack - Severity').item.json['description'].replace('\\n','\\\\n')}}\"}]},{\"type\":\"actions\",\"block_id\":\"actions_case_options\",\"elements\":[{\"type\":\"button\",\"text\":{\"type\":\"plain_text\",\"text\":\"Close Case as False Positive\",\"emoji\":true},\"style\":\"danger\",\"value\":\"{{ $('Prep Fields For Slack - Severity').item.json['caseid']}}\",\"action_id\":\"close_case\"},{\"type\":\"button\",\"text\":{\"type\":\"plain_text\",\"text\":\"Add a Task\",\"emoji\":true},\"value\":\"{{ $('Prep Fields For Slack - Severity').item.json['caseid']}}\",\"action_id\":\"add_task\"}]},{\"type\":\"divider\",\"block_id\":\"divider_2\"},{\"type\":\"header\",\"block_id\":\"header_current_assignment\",\"text\":{\"type\":\"plain_text\",\"text\":\"🔍 Case Details:\",\"emoji\":true}},{\"type\":\"context\",\"block_id\":\"context_status\",\"elements\":[{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Slack - Severity').item.json['status']}}\"}]},{\"type\":\"context\",\"block_id\":\"context_severity\",\"elements\":[{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Slack - Severity').item.json['severity']}}\"}]},{\"type\":\"context\",\"block_id\":\"context_tlp\",\"elements\":[{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Slack - Severity').item.json['tlp']}}\"}]},{\"type\":\"context\",\"block_id\":\"context_pap\",\"elements\":[{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Slack - Severity').item.json['pap']}}\"}]},{\"type\":\"context\",\"block_id\":\"context_assignee\",\"elements\":[{\"type\":\"image\",\"image_url\":\"{{ $('Prep Fields For Slack - Severity').item.json['profilepic']}}\",\"alt_text\":\"Profile Pic\"},{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Slack - Severity').item.json['assignee']}}\"}]}"}]}}, "typeVersion": 3.3}, {"id": "be1fe770-aaa2-4ed2-9ff1-5baa00423d76", "name": "PAP Case Block Rebuild", "type": "n8n-nodes-base.set", "position": [2400, 2060], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c1fccf83-8223-42a2-837e-11eafe5d23fd", "name": "slackblocks", "type": "string", "value": "={\"type\":\"image\",\"block_id\":\"image_block\",\"image_url\":\"https://i.imgur.com/y2Yw1ZP.png\",\"alt_text\":\"{{ $('Prep Fields For PAP Slack').item.json[\"caseid\"]}}\"},{\"type\":\"header\",\"block_id\":\"header_case_created\",\"text\":{\"type\":\"plain_text\",\"text\":\"{{ $('Prep Fields For PAP Slack').item.json[\"number\"]}}\",\"emoji\":true}},{\"type\":\"divider\",\"block_id\":\"divider_1\"},{\"type\":\"section\",\"block_id\":\"section_case_details\",\"fields\":[{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For PAP Slack').item.json[\"title\"].replace(\"\\n\",\"\\\\n\")}}\"},{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For PAP Slack').item.json[\"datecreated\"].replace(\"\\n\",\"\\\\n\")}}\"},{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For PAP Slack').item.json[\"tags\"].replace(\"\\n\",\"\\\\n\")}}\"},{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For PAP Slack').item.json[\"description\"].replace(\"\\n\",\"\\\\n\")}}\"}]},{\"type\":\"actions\",\"block_id\":\"actions_case_options\",\"elements\":[{\"type\":\"button\",\"text\":{\"type\":\"plain_text\",\"text\":\"Close Case as False Positive\",\"emoji\":true},\"style\":\"danger\",\"value\":\"{{ $('Prep Fields For PAP Slack').item.json[\"caseid\"]}}\",\"action_id\":\"close_case\"},{\"type\":\"button\",\"text\":{\"type\":\"plain_text\",\"text\":\"Add a Task\",\"emoji\":true},\"value\":\"{{ $('Prep Fields For PAP Slack').item.json[\"caseid\"]}}\",\"action_id\":\"add_task\"}]},{\"type\":\"divider\",\"block_id\":\"divider_2\"},{\"type\":\"header\",\"block_id\":\"header_current_assignment\",\"text\":{\"type\":\"plain_text\",\"text\":\"🔍 Case Details:\",\"emoji\":true}},{\"type\":\"context\",\"block_id\":\"context_status\",\"elements\":[{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For PAP Slack').item.json[\"status\"]}}\"}]},{\"type\":\"context\",\"block_id\":\"context_severity\",\"elements\":[{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For PAP Slack').item.json[\"severity\"]}}\"}]},{\"type\":\"context\",\"block_id\":\"context_tlp\",\"elements\":[{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For PAP Slack').item.json[\"tlp\"]}}\"}]},{\"type\":\"context\",\"block_id\":\"context_pap\",\"elements\":[{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For PAP Slack').item.json[\"pap\"]}}\"}]},{\"type\":\"context\",\"block_id\":\"context_assignee\",\"elements\":[{\"type\":\"image\",\"image_url\":\"{{ $('Prep Fields For PAP Slack').item.json[\"profilepic\"]}}\",\"alt_text\":\"Profile Pic\"},{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For PAP Slack').item.json[\"assignee\"] }}\"}]}"}]}}, "typeVersion": 3.3}, {"id": "0d9a2036-6a4c-4ffd-95a8-34b6861894b9", "name": "Prep Fields For PAP Slack", "type": "n8n-nodes-base.set", "position": [1600, 2060], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "0001aed3-a3fb-4229-8fa1-1941f0ee8a12", "name": "title", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[3].fields[0].text }}"}, {"id": "6f40871c-ea6f-4c61-9272-9342c99637e8", "name": "datecreated", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[3].fields[1].text }}"}, {"id": "2ea20d66-380f-4a0f-a1ea-c4740293f48b", "name": "number", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[1].text.text }}"}, {"id": "a22dd686-ac38-4e73-a0ec-051dce57f9fa", "name": "severity", "type": "string", "value": "={{ $('Edit Fields').item.json[\"response\"][\"message\"][\"blocks\"][8][\"elements\"][0][\"text\"] }}"}, {"id": "1c210cfb-2a03-4e81-a8c5-2db739d98226", "name": "tlp", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[9].elements[0].text }}"}, {"id": "53e1d1a9-055a-48c2-80de-b694871c6620", "name": "pap", "type": "string", "value": "={{ $('Edit Fields').item.json[\"dictionary\"][\"PAP\"][$('Edit Fields').item.json[\"response\"][\"state\"][\"values\"][\"actions_assignment_options\"][\"update_pap\"][\"selected_option\"][\"text\"][\"text\"].toUpperCase()] }} *Permissible Actions Protocol(PAP):* {{ $('Edit Fields').item.json[\"response\"][\"state\"][\"values\"][\"actions_assignment_options\"][\"update_pap\"][\"selected_option\"][\"text\"][\"text\"].toUpperCase() }}"}, {"id": "ee630583-a6ad-4bab-ad78-7846df3ac093", "name": "description", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[3].fields[3].text }}"}, {"id": "48ac7d92-5177-46be-b01e-83493f18ee09", "name": "assignee", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[11].elements[1].text }}"}, {"id": "f9c06ecf-d3d5-490c-a848-02c88fbd3ab4", "name": "profilepic", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[11].elements[0].image_url }}"}, {"id": "bb4ec29d-fac2-4eb9-a177-fca9d2798514", "name": "caseid", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[0].alt_text }}"}, {"id": "7a485f44-b855-4633-ba51-82e7490d7166", "name": "tags", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[3].fields[2].text.replaceAll(\"\\n\",\"\\\\n\").replaceAll(\"**\",\"*\") }}"}, {"id": "a4884fc5-2d92-4c34-af9c-61240bc564d5", "name": "status", "type": "string", "value": "={{ $json.response.message.blocks[7].elements[0].text }}"}, {"id": "449c052a-05b4-4c03-99a4-b5fe7ec0e102", "name": "newassign<PERSON>", "type": "string", "value": "={{ $json.response.state.values.actions_assignment_options['change-assignee'].selected_user }}"}, {"id": "8278ead1-85c0-4921-ac36-9abfd44f99c8", "name": "papId", "type": "number", "value": "={{ $json.response.state.values.actions_assignment_options.update_pap.selected_option.value }}"}]}}, "typeVersion": 3.3}, {"id": "574caf88-eef7-4b06-9f04-7a468ef56325", "name": "Map Actions", "type": "n8n-nodes-base.set", "position": [2700, 1160], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "7d88f9ff-3046-491f-a31c-642839fba7b4", "name": "actions", "type": "string", "value": "={{ $json.slackblocks }}"}, {"id": "5cde079d-1b30-41b0-948d-f3b8bacb88bf", "name": "buttons", "type": "string", "value": "={\"type\":\"actions\",\"block_id\":\"actions_assignment_options\",\"elements\":[{\"type\":\"users_select\",\"placeholder\":{\"type\":\"plain_text\",\"text\":\"Change Assignee\",\"emoji\":true},\"action_id\":\"change-assignee\"},{\"type\":\"static_select\",\"placeholder\":{\"type\":\"plain_text\",\"text\":\"Change Status\",\"emoji\":true},\"action_id\":\"update-status\",\"options\":[{\"text\":{\"type\":\"plain_text\",\"text\":\"🆕 New\",\"emoji\":true},\"value\":\"1\"},{\"text\":{\"type\":\"plain_text\",\"text\":\"🔄 In progress\",\"emoji\":true},\"value\":\"2\"},{\"text\":{\"type\":\"plain_text\",\"text\":\"📑 Duplicated\",\"emoji\":true},\"value\":\"3\"},{\"text\":{\"type\":\"plain_text\",\"text\":\"❎ False positive\",\"emoji\":true},\"value\":\"4\"},{\"text\":{\"type\":\"plain_text\",\"text\":\"❓ Indeterminate\",\"emoji\":true},\"value\":\"5\"},{\"text\":{\"type\":\"plain_text\",\"text\":\"🔵 Other\",\"emoji\":true},\"value\":\"6\"},{\"text\":{\"type\":\"plain_text\",\"text\":\"✅ True positive\",\"emoji\":true},\"value\":\"7\"}]},{\"type\":\"static_select\",\"placeholder\":{\"type\":\"plain_text\",\"text\":\"Change severity\",\"emoji\":true},\"action_id\":\"update_severity\",\"options\":[{\"text\":{\"type\":\"plain_text\",\"text\":\"Low\",\"emoji\":true},\"value\":\"1\"},{\"text\":{\"type\":\"plain_text\",\"text\":\"Medium\",\"emoji\":true},\"value\":\"2\"},{\"text\":{\"type\":\"plain_text\",\"text\":\"High\",\"emoji\":true},\"value\":\"3\"},{\"text\":{\"type\":\"plain_text\",\"text\":\"Critical\",\"emoji\":true},\"value\":\"4\"}]},{\"type\":\"static_select\",\"placeholder\":{\"type\":\"plain_text\",\"text\":\"Change TLP\",\"emoji\":true},\"action_id\":\"update_tlp\",\"options\":[{\"text\":{\"type\":\"plain_text\",\"text\":\"White\",\"emoji\":true},\"value\":\"0\"},{\"text\":{\"type\":\"plain_text\",\"text\":\"Green\",\"emoji\":true},\"value\":\"1\"},{\"text\":{\"type\":\"plain_text\",\"text\":\"Amber\",\"emoji\":true},\"value\":\"2\"},{\"text\":{\"type\":\"plain_text\",\"text\":\"Red\",\"emoji\":true},\"value\":\"3\"}]},{\"type\":\"static_select\",\"placeholder\":{\"type\":\"plain_text\",\"text\":\"Change PAP\",\"emoji\":true},\"action_id\":\"update_pap\",\"options\":[{\"text\":{\"type\":\"plain_text\",\"text\":\"White\",\"emoji\":true},\"value\":\"0\"},{\"text\":{\"type\":\"plain_text\",\"text\":\"Green\",\"emoji\":true},\"value\":\"1\"},{\"text\":{\"type\":\"plain_text\",\"text\":\"Amber\",\"emoji\":true},\"value\":\"2\"},{\"text\":{\"type\":\"plain_text\",\"text\":\"Red\",\"emoji\":true},\"value\":\"3\"}]}]},{\"type\":\"section\",\"text\":{\"type\":\"mrkdwn\",\"text\":\":bee: View the case in TheHive\"},\"accessory\":{\"type\":\"button\",\"style\":\"primary\",\"text\":{\"type\":\"plain_text\",\"text\":\"View Case\",\"emoji\":true},\"value\":\"click_me_123\",\"url\":\"{{ $('Edit Fields').item.json.theHiveUrl }}/cases/{{ $('Edit Fields').item.json.response.message.blocks[0].alt_text }}/details\",\"action_id\":\"viewlink\"}}"}]}}, "typeVersion": 3.3}, {"id": "d0e2d26f-9ab1-4f4f-a700-d686dda8dbef", "name": "Build Final Block", "type": "n8n-nodes-base.set", "position": [2920, 1160], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "={\"blocks\": [{{ $json.actions }},{{ $json.buttons }}]}"}, "typeVersion": 3.3}, {"id": "e189e865-664a-47c1-b877-59788967c852", "name": "Prep Fields For TLP Slack", "type": "n8n-nodes-base.set", "position": [1600, 2240], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "0001aed3-a3fb-4229-8fa1-1941f0ee8a12", "name": "title", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[3].fields[0].text }}"}, {"id": "6f40871c-ea6f-4c61-9272-9342c99637e8", "name": "datecreated", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[3].fields[1].text }}"}, {"id": "2ea20d66-380f-4a0f-a1ea-c4740293f48b", "name": "number", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[1].text.text }}"}, {"id": "a22dd686-ac38-4e73-a0ec-051dce57f9fa", "name": "severity", "type": "string", "value": "={{ $('Edit Fields').item.json[\"response\"][\"message\"][\"blocks\"][8][\"elements\"][0][\"text\"] }}"}, {"id": "1c210cfb-2a03-4e81-a8c5-2db739d98226", "name": "tlp", "type": "string", "value": "={{ $('Edit Fields').item.json[\"dictionary\"][\"TLP\"][$('Edit Fields').item.json[\"response\"][\"state\"][\"values\"][\"actions_assignment_options\"][\"update_tlp\"][\"selected_option\"][\"text\"][\"text\"].toUpperCase()] }} *Traffic Light Protocol(TLP):* {{ $('Edit Fields').item.json[\"response\"][\"state\"][\"values\"][\"actions_assignment_options\"][\"update_tlp\"][\"selected_option\"][\"text\"][\"text\"].toUpperCase() }}"}, {"id": "53e1d1a9-055a-48c2-80de-b694871c6620", "name": "pap", "type": "string", "value": "={{ $json.response.message.blocks[10].elements[0].text }}"}, {"id": "ee630583-a6ad-4bab-ad78-7846df3ac093", "name": "description", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[3].fields[3].text }}"}, {"id": "48ac7d92-5177-46be-b01e-83493f18ee09", "name": "assignee", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[11].elements[1].text }}"}, {"id": "f9c06ecf-d3d5-490c-a848-02c88fbd3ab4", "name": "profilepic", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[11].elements[0].image_url }}"}, {"id": "bb4ec29d-fac2-4eb9-a177-fca9d2798514", "name": "caseid", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[0].alt_text }}"}, {"id": "7a485f44-b855-4633-ba51-82e7490d7166", "name": "tags", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[3].fields[2].text.replaceAll(\"\\n\",\"\\\\n\").replaceAll(\"**\",\"*\") }}"}, {"id": "a4884fc5-2d92-4c34-af9c-61240bc564d5", "name": "status", "type": "string", "value": "={{ $json.response.message.blocks[7].elements[0].text }}"}, {"id": "449c052a-05b4-4c03-99a4-b5fe7ec0e102", "name": "newassign<PERSON>", "type": "string", "value": "={{ $json.response.state.values.actions_assignment_options['change-assignee'].selected_user }}"}, {"id": "8278ead1-85c0-4921-ac36-9abfd44f99c8", "name": "tlpId", "type": "number", "value": "={{ $('Edit Fields').item.json.response.state.values.actions_assignment_options.update_tlp.selected_option.value }}"}]}}, "typeVersion": 3.3}, {"id": "286a5624-a546-4a7f-a416-0b4ef66a99d7", "name": "Prep Fields For Status Slack", "type": "n8n-nodes-base.set", "position": [1600, 2420], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "0001aed3-a3fb-4229-8fa1-1941f0ee8a12", "name": "title", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[3].fields[0].text }}"}, {"id": "6f40871c-ea6f-4c61-9272-9342c99637e8", "name": "datecreated", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[3].fields[1].text }}"}, {"id": "2ea20d66-380f-4a0f-a1ea-c4740293f48b", "name": "number", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[1].text.text }}"}, {"id": "a22dd686-ac38-4e73-a0ec-051dce57f9fa", "name": "severity", "type": "string", "value": "={{ $('Edit Fields').item.json[\"response\"][\"message\"][\"blocks\"][8][\"elements\"][0][\"text\"] }}"}, {"id": "1c210cfb-2a03-4e81-a8c5-2db739d98226", "name": "tlp", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[9].elements[0].text }}"}, {"id": "53e1d1a9-055a-48c2-80de-b694871c6620", "name": "pap", "type": "string", "value": "={{ $json.response.message.blocks[10].elements[0].text }}"}, {"id": "ee630583-a6ad-4bab-ad78-7846df3ac093", "name": "description", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[3].fields[3].text }}"}, {"id": "48ac7d92-5177-46be-b01e-83493f18ee09", "name": "assignee", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[11].elements[1].text }}"}, {"id": "f9c06ecf-d3d5-490c-a848-02c88fbd3ab4", "name": "profilepic", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[11].elements[0].image_url }}"}, {"id": "bb4ec29d-fac2-4eb9-a177-fca9d2798514", "name": "caseid", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[0].alt_text }}"}, {"id": "7a485f44-b855-4633-ba51-82e7490d7166", "name": "tags", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[3].fields[2].text.replaceAll(\"\\n\",\"\\\\n\").replaceAll(\"**\",\"*\") }}"}, {"id": "a4884fc5-2d92-4c34-af9c-61240bc564d5", "name": "status", "type": "string", "value": "={{ $('Edit Fields').item.json[\"dictionary\"][\"STATUSEMOJI\"][$('Edit Fields').item.json[\"response\"][\"state\"][\"values\"][\"actions_assignment_options\"][\"update-status\"][\"selected_option\"][\"value\"].toUpperCase()] }} *Status:* {{ $('Edit Fields').item.json[\"dictionary\"][\"STATUS\"][$('Edit Fields').item.json[\"response\"][\"state\"][\"values\"][\"actions_assignment_options\"][\"update-status\"][\"selected_option\"][\"value\"].toUpperCase()] }}"}, {"id": "8278ead1-85c0-4921-ac36-9abfd44f99c8", "name": "statusvalue", "type": "string", "value": "={{ $('Edit Fields').item.json[\"dictionary\"][\"STATUS\"][$('Edit Fields').item.json[\"response\"][\"state\"][\"values\"][\"actions_assignment_options\"][\"update-status\"][\"selected_option\"][\"value\"].toUpperCase()] }}"}]}}, "typeVersion": 3.3}, {"id": "bdaa7347-dd4f-4183-9f21-979557320603", "name": "Update Status in TheHive", "type": "n8n-nodes-base.theHiveProject", "position": [2060, 2420], "parameters": {"resource": "case", "operation": "update", "caseUpdateFields": {"value": {"id": "={{ $json.caseid }}", "flag": false, "status": "={{ $json.statusvalue }}"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ID", "defaultMatch": true, "canBeUsedToMatch": true}, {"id": "title", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "description", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "severity", "type": "options", "display": true, "options": [{"name": "Low", "value": 1}, {"name": "Medium", "value": 2}, {"name": "High", "value": 3}, {"name": "Critical", "value": 4}], "removed": true, "required": false, "displayName": "Severity (Severity of information)", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "startDate", "type": "dateTime", "display": true, "removed": true, "required": false, "displayName": "Start Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "endDate", "type": "dateTime", "display": true, "removed": true, "required": false, "displayName": "End Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "tags", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Tags", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "flag", "type": "boolean", "display": true, "removed": true, "required": false, "displayName": "Flag", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "tlp", "type": "options", "display": true, "options": [{"name": "White", "value": 0}, {"name": "Green", "value": 1}, {"name": "Amber", "value": 2}, {"name": "Red", "value": 3}], "removed": true, "required": false, "displayName": "TLP (Confidentiality of information)", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "pap", "type": "options", "display": true, "options": [{"name": "White", "value": 0}, {"name": "Green", "value": 1}, {"name": "Amber", "value": 2}, {"name": "Red", "value": 3}], "removed": true, "required": false, "displayName": "PAP (Level of exposure of information)", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "status", "type": "options", "display": true, "options": [{"name": "Duplicated", "value": "Duplicated", "description": "Stage: Closed"}, {"name": "FalsePositive", "value": "FalsePositive", "description": "Stage: Closed"}, {"name": "Indeterminate", "value": "Indeterminate", "description": "Stage: Closed"}, {"name": "InProgress", "value": "InProgress", "description": "Stage: InProgress"}, {"name": "New", "value": "New", "description": "Stage: New"}, {"name": "Other", "value": "Other", "description": "Stage: Closed"}, {"name": "TruePositive", "value": "TruePositive", "description": "Stage: Closed"}], "removed": false, "required": false, "displayName": "Status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "assignee", "type": "options", "display": true, "options": [{"name": "Angel", "value": "<EMAIL>"}, {"name": "<PERSON>", "value": "<EMAIL>"}], "removed": true, "required": false, "displayName": "Assignee", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "impactStatus", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Impact Status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "taskRule", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Task Rule", "defaultMatch": false, "canBeUsedToMatch": false}, {"id": "observableRule", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Observable Rule", "defaultMatch": false, "canBeUsedToMatch": false}, {"id": "addTags", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Add Tags", "defaultMatch": false, "canBeUsedToMatch": false}, {"id": "removeTags", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Remove Tags", "defaultMatch": false, "canBeUsedToMatch": false}], "mappingMode": "defineBelow", "matchingColumns": ["id"]}}, "credentials": {"theHiveProjectApi": {"id": "6O5aPdkMaQmc8I9B", "name": "The Hive 5 account"}}, "typeVersion": 1}, {"id": "06320f47-c22d-4285-9885-0f3148702dd9", "name": "Close Case as <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.theHiveProject", "position": [2060, 1700], "parameters": {"resource": "case", "operation": "update", "caseUpdateFields": {"value": {"id": "={{ $json.caseid }}", "flag": false, "status": "FalsePositive"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ID", "defaultMatch": true, "canBeUsedToMatch": true}, {"id": "title", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "description", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "severity", "type": "options", "display": true, "options": [{"name": "Low", "value": 1}, {"name": "Medium", "value": 2}, {"name": "High", "value": 3}, {"name": "Critical", "value": 4}], "removed": true, "required": false, "displayName": "Severity (Severity of information)", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "startDate", "type": "dateTime", "display": true, "removed": true, "required": false, "displayName": "Start Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "endDate", "type": "dateTime", "display": true, "removed": true, "required": false, "displayName": "End Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "tags", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Tags", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "flag", "type": "boolean", "display": true, "removed": true, "required": false, "displayName": "Flag", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "tlp", "type": "options", "display": true, "options": [{"name": "White", "value": 0}, {"name": "Green", "value": 1}, {"name": "Amber", "value": 2}, {"name": "Red", "value": 3}], "removed": true, "required": false, "displayName": "TLP (Confidentiality of information)", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "pap", "type": "options", "display": true, "options": [{"name": "White", "value": 0}, {"name": "Green", "value": 1}, {"name": "Amber", "value": 2}, {"name": "Red", "value": 3}], "removed": true, "required": false, "displayName": "PAP (Level of exposure of information)", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "status", "type": "options", "display": true, "options": [{"name": "Duplicated", "value": "Duplicated", "description": "Stage: Closed"}, {"name": "FalsePositive", "value": "FalsePositive", "description": "Stage: Closed"}, {"name": "Indeterminate", "value": "Indeterminate", "description": "Stage: Closed"}, {"name": "InProgress", "value": "InProgress", "description": "Stage: InProgress"}, {"name": "New", "value": "New", "description": "Stage: New"}, {"name": "Other", "value": "Other", "description": "Stage: Closed"}, {"name": "TruePositive", "value": "TruePositive", "description": "Stage: Closed"}], "removed": false, "required": false, "displayName": "Status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "assignee", "type": "options", "display": true, "options": [{"name": "Angel", "value": "<EMAIL>"}, {"name": "<PERSON>", "value": "<EMAIL>"}], "removed": true, "required": false, "displayName": "Assignee", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "impactStatus", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Impact Status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "taskRule", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Task Rule", "defaultMatch": false, "canBeUsedToMatch": false}, {"id": "observableRule", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Observable Rule", "defaultMatch": false, "canBeUsedToMatch": false}, {"id": "addTags", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Add Tags", "defaultMatch": false, "canBeUsedToMatch": false}, {"id": "removeTags", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Remove Tags", "defaultMatch": false, "canBeUsedToMatch": false}], "mappingMode": "defineBelow", "matchingColumns": ["id"]}}, "credentials": {"theHiveProjectApi": {"id": "6O5aPdkMaQmc8I9B", "name": "The Hive 5 account"}}, "typeVersion": 1}, {"id": "14d2eebc-17f7-4004-b4d2-17f38e1c6aaa", "name": "Status Case Block Rebuild", "type": "n8n-nodes-base.set", "position": [2400, 2420], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c1fccf83-8223-42a2-837e-11eafe5d23fd", "name": "slackblocks", "type": "string", "value": "={\"type\":\"image\",\"block_id\":\"image_block\",\"image_url\":\"https://i.imgur.com/y2Yw1ZP.png\",\"alt_text\":\"{{ $('Prep Fields For Status Slack').item.json[\"caseid\"]}}\"},{\"type\":\"header\",\"block_id\":\"header_case_created\",\"text\":{\"type\":\"plain_text\",\"text\":\"{{ $('Prep Fields For Status Slack').item.json[\"number\"]}}\",\"emoji\":true}},{\"type\":\"divider\",\"block_id\":\"divider_1\"},{\"type\":\"section\",\"block_id\":\"section_case_details\",\"fields\":[{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Status Slack').item.json[\"title\"].replace(\"\\n\",\"\\\\n\")}}\"},{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Status Slack').item.json[\"datecreated\"].replace(\"\\n\",\"\\\\n\")}}\"},{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Status Slack').item.json[\"tags\"].replace(\"\\n\",\"\\\\n\")}}\"},{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Status Slack').item.json[\"description\"].replace(\"\\n\",\"\\\\n\")}}\"}]},{\"type\":\"actions\",\"block_id\":\"actions_case_options\",\"elements\":[{\"type\":\"button\",\"text\":{\"type\":\"plain_text\",\"text\":\"Close Case as False Positive\",\"emoji\":true},\"style\":\"danger\",\"value\":\"{{ $('Prep Fields For Status Slack').item.json[\"caseid\"]}}\",\"action_id\":\"close_case\"},{\"type\":\"button\",\"text\":{\"type\":\"plain_text\",\"text\":\"Add a Task\",\"emoji\":true},\"value\":\"{{ $('Prep Fields For Status Slack').item.json[\"caseid\"]}}\",\"action_id\":\"add_task\"}]},{\"type\":\"divider\",\"block_id\":\"divider_2\"},{\"type\":\"header\",\"block_id\":\"header_current_assignment\",\"text\":{\"type\":\"plain_text\",\"text\":\"🔍 Case Details:\",\"emoji\":true}},{\"type\":\"context\",\"block_id\":\"context_status\",\"elements\":[{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Status Slack').item.json[\"status\"]}}\"}]},{\"type\":\"context\",\"block_id\":\"context_severity\",\"elements\":[{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Status Slack').item.json[\"severity\"]}}\"}]},{\"type\":\"context\",\"block_id\":\"context_tlp\",\"elements\":[{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Status Slack').item.json[\"tlp\"]}}\"}]},{\"type\":\"context\",\"block_id\":\"context_pap\",\"elements\":[{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Status Slack').item.json[\"pap\"]}}\"}]},{\"type\":\"context\",\"block_id\":\"context_assignee\",\"elements\":[{\"type\":\"image\",\"image_url\":\"{{ $('Prep Fields For Status Slack').item.json[\"profilepic\"]}}\",\"alt_text\":\"Profile Pic\"},{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For Status Slack').item.json[\"assignee\"] }}\"}]}"}]}}, "typeVersion": 3.3}, {"id": "eff9d872-a2e0-42ff-ba18-645d044a95b7", "name": "TLP Case Block Rebuild", "type": "n8n-nodes-base.set", "position": [2400, 2240], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c1fccf83-8223-42a2-837e-11eafe5d23fd", "name": "slackblocks", "type": "string", "value": "={\"type\":\"image\",\"block_id\":\"image_block\",\"image_url\":\"https://i.imgur.com/y2Yw1ZP.png\",\"alt_text\":\"{{ $('Prep Fields For TLP Slack').item.json[\"caseid\"]}}\"},{\"type\":\"header\",\"block_id\":\"header_case_created\",\"text\":{\"type\":\"plain_text\",\"text\":\"{{ $('Prep Fields For TLP Slack').item.json[\"number\"]}}\",\"emoji\":true}},{\"type\":\"divider\",\"block_id\":\"divider_1\"},{\"type\":\"section\",\"block_id\":\"section_case_details\",\"fields\":[{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For TLP Slack').item.json[\"title\"].replace(\"\\n\",\"\\\\n\")}}\"},{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For TLP Slack').item.json[\"datecreated\"].replace(\"\\n\",\"\\\\n\")}}\"},{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For TLP Slack').item.json[\"tags\"].replace(\"\\n\",\"\\\\n\")}}\"},{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For TLP Slack').item.json[\"description\"].replace(\"\\n\",\"\\\\n\")}}\"}]},{\"type\":\"actions\",\"block_id\":\"actions_case_options\",\"elements\":[{\"type\":\"button\",\"text\":{\"type\":\"plain_text\",\"text\":\"Close Case as False Positive\",\"emoji\":true},\"style\":\"danger\",\"value\":\"{{ $('Prep Fields For TLP Slack').item.json[\"caseid\"]}}\",\"action_id\":\"close_case\"},{\"type\":\"button\",\"text\":{\"type\":\"plain_text\",\"text\":\"Add a Task\",\"emoji\":true},\"value\":\"{{ $('Prep Fields For TLP Slack').item.json[\"caseid\"]}}\",\"action_id\":\"add_task\"}]},{\"type\":\"divider\",\"block_id\":\"divider_2\"},{\"type\":\"header\",\"block_id\":\"header_current_assignment\",\"text\":{\"type\":\"plain_text\",\"text\":\"🔍 Case Details:\",\"emoji\":true}},{\"type\":\"context\",\"block_id\":\"context_status\",\"elements\":[{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For TLP Slack').item.json[\"status\"]}}\"}]},{\"type\":\"context\",\"block_id\":\"context_severity\",\"elements\":[{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For TLP Slack').item.json[\"severity\"]}}\"}]},{\"type\":\"context\",\"block_id\":\"context_tlp\",\"elements\":[{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For TLP Slack').item.json[\"tlp\"]}}\"}]},{\"type\":\"context\",\"block_id\":\"context_pap\",\"elements\":[{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For TLP Slack').item.json[\"pap\"]}}\"}]},{\"type\":\"context\",\"block_id\":\"context_assignee\",\"elements\":[{\"type\":\"image\",\"image_url\":\"{{ $('Prep Fields For TLP Slack').item.json[\"profilepic\"]}}\",\"alt_text\":\"Profile Pic\"},{\"type\":\"mrkdwn\",\"text\":\"{{ $('Prep Fields For TLP Slack').item.json[\"assignee\"] }}\"}]}"}]}}, "typeVersion": 3.3}, {"id": "ddf72a31-3655-423d-bf47-c405563ebf46", "name": "No Action Needed", "type": "n8n-nodes-base.noOp", "position": [1520, 4140], "parameters": {}, "typeVersion": 1}, {"id": "7e3155d1-69d0-4773-97f7-61cdc55d04ad", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [1140, 2600], "parameters": {"color": 7, "width": 792.317043267064, "height": 631.3919967555308, "content": "![slack](https://uploads.n8n.io/templates/slack.png)\n## Add a task to a Case - Display Modal Popup\nThis section of the workflow focuses on enhancing task management directly within Slack, allowing users to interactively add tasks to cases through a modal popup. \n\nThe workflow acknowledges the modal request using a specialized node that prepares the system for a dynamic interaction, then leverages an HTTP request node to open a modal in Slack via the `https://slack.com/api/views.open` API endpoint. \n\nThis modal is populated with various fields such as task title, description, due date, assignee, and options like marking the task as mandatory or flagged, which are meticulously structured in JSON format to facilitate user input. \n\nEach element in the modal, from text inputs and date pickers to user-select dropdowns, is designed for ease of use, ensuring that task details are comprehensive and contextually relevant. "}, "typeVersion": 1}, {"id": "90fc2f6d-8e22-477e-ab6e-3282d1d1e6f0", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [1856.0596905369107, 385.2537459507854], "parameters": {"color": 7, "width": 691.0596729578308, "height": 465.39159795232865, "content": "![theHive](https://uploads.n8n.io/templates/thehive.png)\n## Assign Case to Others\nLastly the assignee data is passed into TheHive in \nthe correct format. We then use the Set node to \nbegin the process of building our updated Slack \nmessage. "}, "typeVersion": 1}, {"id": "50435336-d159-4477-aeb4-228bdfcb1003", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [2640, 449.39422080427823], "parameters": {"color": 7, "width": 952.9348524840666, "height": 895.6351014818621, "content": "![slack](https://uploads.n8n.io/templates/slack.png)\n# Dynamic Slack Message Updates in Workflow\n\nThis section of the workflow focuses on dynamically updating Slack messages to reflect changes in TheHive case attributes directly from Slack. The process begins by mapping user actions to Slack interactive elements, which are then composed into a final message block. This updated block is sent back to Slack to modify the original message, ensuring all changes are visible in real-time.\n\n**Key Components:**\n- **Map Actions Node**: This node maps user actions from Slack, like changing assignees or case status, into JSON format. It sets the foundation for creating interactive message blocks that users can interact with.\n- **Build Final Block Node**: Combines all interactive elements into a single JSON object, preparing it for posting back to Slack.\n- **Update Message with new Assignee Node**: Utilizes an HTTP request to post the updated message blocks back to Slack using the chat.update API endpoint. This action modifies the original Slack message to display the new, interactive elements.\n\n**Workflow Efficiency:**\n- By automating the update of Slack messages, this process eliminates the need for manual updates and ensures that all stakeholders are viewing the most current case details directly within Slack.\n- Isolating action elements in separate nodes allows for easier modifications and ensures that any changes in the workflow logic are consistently applied.\n\n**Setup Considerations:**\n- Ensure that the Slack API credentials are correctly configured to allow message updates.\n- Familiarize yourself with JSON structure if you need to customize the interactive elements further, as modifications require a good understanding of JSON formatting.\n\nThis method not only enhances the interactivity of Slack communications but also ensures that updates are seamlessly integrated into the workflow, promoting efficiency and ensuring data consistency."}, "typeVersion": 1}, {"id": "97b8bae8-1711-4c11-81f0-d2e0c50d10e6", "name": "Sticky Note14", "type": "n8n-nodes-base.stickyNote", "position": [2135, 3243], "parameters": {"color": 7, "width": 409.70915573152797, "height": 570.3745712311052, "content": "![Imgur](https://uploads.n8n.io/templates/thehive.png)\n## Add a task to a Case - Process the task details\nThe data is then processed in TheHive using the native n8n node. "}, "typeVersion": 1}, {"id": "1c7c5f11-d8f0-4623-b303-1ebf4adb3b79", "name": "Sticky Note15", "type": "n8n-nodes-base.stickyNote", "position": [751.1129156240361, 520], "parameters": {"color": 7, "width": 374.56596023868, "height": 628.1281535316614, "content": "![n8n](https://uploads.n8n.io/templates/n8n.png)\n## n8n Switch Node\nThis node checks the messages from Slack and routes them down the appropriate route in the workflow. \n\nIt is an invaluable tool that simplifies our logic immensely. "}, "typeVersion": 1}, {"id": "4fd498e7-31cf-4d9f-b888-ede79e42e79f", "name": "Post New Case To Slack", "type": "n8n-nodes-base.slack", "position": [880, 300], "parameters": {"select": "channel", "blocksUi": "={\n\t\"blocks\": [\n\t\t{\n\t\t\t\"type\": \"image\",\n\t\t\t\"block_id\": \"image_block\",\n\t\t\t\"image_url\": \"https://i.imgur.com/y2Yw1ZP.png\",\n\t\t\t\"alt_text\": \"{{ $json['caseid'] }}\"\n\t\t},\n\t\t{\n\t\t\t\"type\": \"header\",\n\t\t\t\"block_id\": \"header_case_created\",\n\t\t\t\"text\": {\n\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\"text\": \"{{ $json['number'] }}\",\n\t\t\t\t\"emoji\": true\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\t\"type\": \"divider\",\n\t\t\t\"block_id\": \"divider_1\"\n\t\t},\n\t\t{\n\t\t\t\"type\": \"section\",\n\t\t\t\"block_id\": \"section_case_details\",\n\t\t\t\"fields\": [\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\t\"text\": \"{{ $json['title'] }}\"\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\t\"text\": \"{{ $json['datecreated'] }}\"\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\t\"text\": \"{{ $json['description'].replaceAll(\"\\n\",\"\\\\n\").replaceAll(\"**\",\"*\")}}\"\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\t\"text\": \"{{ $json['tags'] }}\"\n\t\t\t\t}\n\t\t\t]\n\t\t},\n\t\t{\n\t\t\t\"type\": \"actions\",\n\t\t\t\"block_id\": \"actions_case_options\",\n\t\t\t\"elements\": [\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"button\",\n\t\t\t\t\t\"text\": {\n\t\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\t\"text\": \"Close Case as False Positive\",\n\t\t\t\t\t\t\"emoji\": true\n\t\t\t\t\t},\n\t\t\t\t\t\"style\": \"danger\",\n\t\t\t\t\t\"value\": \"{{ $json['caseid'] }}\",\n\t\t\t\t\t\"action_id\": \"close_case\"\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"button\",\n\t\t\t\t\t\"text\": {\n\t\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\t\"text\": \"Add a Task\",\n\t\t\t\t\t\t\"emoji\": true\n\t\t\t\t\t},\n\t\t\t\t\t\"value\": \"{{ $json['caseid'] }}\",\n\t\t\t\t\t\"action_id\": \"add_task\"\n\t\t\t\t}\n\t\t\t]\n\t\t},\n\t\t{\n\t\t\t\"type\": \"divider\",\n\t\t\t\"block_id\": \"divider_2\"\n\t\t},\n\t\t{\n\t\t\t\"type\": \"header\",\n\t\t\t\"block_id\": \"header_current_assignment\",\n\t\t\t\"text\": {\n\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\"text\": \"🔍 Case Details:\",\n\t\t\t\t\"emoji\": true\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\t\"type\": \"context\",\n\t\t\t\"block_id\": \"context_status\",\n\t\t\t\"elements\": [\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\t\"text\": \"{{ $json['status_emoji'] }} *Status:* {{ $json['status'] }}\"\n\t\t\t\t}\n\t\t\t]\n\t\t},\n\t\t{\n\t\t\t\"type\": \"context\",\n\t\t\t\"block_id\": \"context_severity\",\n\t\t\t\"elements\": [\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\t\"text\": \"{{ $json['severity_emoji'] }} {{ $json['severity'] }}\"\n\t\t\t\t}\n\t\t\t]\n\t\t},\n\t\t{\n\t\t\t\"type\": \"context\",\n\t\t\t\"block_id\": \"context_tlp\",\n\t\t\t\"elements\": [\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\t\"text\": \"{{ $json['tlp_emoji'] }} {{ $json['tlp'] }}\"\n\t\t\t\t}\n\t\t\t]\n\t\t},\n\t\t{\n\t\t\t\"type\": \"context\",\n\t\t\t\"block_id\": \"context_pap\",\n\t\t\t\"elements\": [\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\t\"text\": \"{{ $json['pap_emoji'] }} {{ $json['pap'] }}\"\n\t\t\t\t}\n\t\t\t]\n\t\t},\n\t\t{\n\t\t\t\"type\": \"context\",\n\t\t\t\"block_id\": \"context_assignee\",\n\t\t\t\"elements\": [\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"image\",\n\t\t\t\t\t\"image_url\": \"{{ $json['profilepic'] }}\",\n\t\t\t\t\t\"alt_text\": \"Profile Pic\"\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\t\"text\": \"Assignee: {{ $json['assignee'] }}\"\n\t\t\t\t}\n\t\t\t]\n\t\t},\n\t\t{\n\t\t\t\"type\": \"actions\",\n\t\t\t\"block_id\": \"actions_assignment_options\",\n\t\t\t\"elements\": [\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"users_select\",\n\t\t\t\t\t\"placeholder\": {\n\t\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\t\"text\": \"Change Assignee\",\n\t\t\t\t\t\t\"emoji\": true\n\t\t\t\t\t},\n\t\t\t\t\t\"action_id\": \"change-assignee\"\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"static_select\",\n\t\t\t\t\t\"placeholder\": {\n\t\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\t\"text\": \"Change Status\",\n\t\t\t\t\t\t\"emoji\": true\n\t\t\t\t\t},\n\t\t\t\t\t\"action_id\": \"update-status\",\n\t\t\t\t\t\"options\": [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"text\": {\n\t\t\t\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\t\t\t\"text\": \"🆕 New\",\n\t\t\t\t\t\t\t\t\"emoji\": true\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\"value\": \"1\"\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"text\": {\n\t\t\t\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\t\t\t\"text\": \"🔄 In progress\",\n\t\t\t\t\t\t\t\t\"emoji\": true\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\"value\": \"2\"\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"text\": {\n\t\t\t\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\t\t\t\"text\": \"📑 Duplicated\",\n\t\t\t\t\t\t\t\t\"emoji\": true\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\"value\": \"3\"\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"text\": {\n\t\t\t\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\t\t\t\"text\": \"❎ False positive\",\n\t\t\t\t\t\t\t\t\"emoji\": true\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\"value\": \"4\"\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"text\": {\n\t\t\t\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\t\t\t\"text\": \"❓ Indeterminate\",\n\t\t\t\t\t\t\t\t\"emoji\": true\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\"value\": \"5\"\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"text\": {\n\t\t\t\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\t\t\t\"text\": \"🔵 Other\",\n\t\t\t\t\t\t\t\t\"emoji\": true\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\"value\": \"6\"\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"text\": {\n\t\t\t\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\t\t\t\"text\": \"✅ True positive\",\n\t\t\t\t\t\t\t\t\"emoji\": true\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\"value\": \"7\"\n\t\t\t\t\t\t}\n\t\t\t\t\t]\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"static_select\",\n\t\t\t\t\t\"placeholder\": {\n\t\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\t\"text\": \"Change severity\",\n\t\t\t\t\t\t\"emoji\": true\n\t\t\t\t\t},\n\t\t\t\t\t\"action_id\": \"update_severity\",\n\t\t\t\t\t\"options\": [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"text\": {\n\t\t\t\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\t\t\t\"text\": \"Low\",\n\t\t\t\t\t\t\t\t\"emoji\": true\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\"value\": \"1\"\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"text\": {\n\t\t\t\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\t\t\t\"text\": \"Medium\",\n\t\t\t\t\t\t\t\t\"emoji\": true\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\"value\": \"2\"\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"text\": {\n\t\t\t\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\t\t\t\"text\": \"High\",\n\t\t\t\t\t\t\t\t\"emoji\": true\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\"value\": \"3\"\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"text\": {\n\t\t\t\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\t\t\t\"text\": \"Critical\",\n\t\t\t\t\t\t\t\t\"emoji\": true\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\"value\": \"4\"\n\t\t\t\t\t\t}\n\t\t\t\t\t]\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"static_select\",\n\t\t\t\t\t\"placeholder\": {\n\t\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\t\"text\": \"Change TLP\",\n\t\t\t\t\t\t\"emoji\": true\n\t\t\t\t\t},\n\t\t\t\t\t\"action_id\": \"update_tlp\",\n\t\t\t\t\t\"options\": [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"text\": {\n\t\t\t\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\t\t\t\"text\": \"White\",\n\t\t\t\t\t\t\t\t\"emoji\": true\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\"value\": \"0\"\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"text\": {\n\t\t\t\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\t\t\t\"text\": \"Amber\",\n\t\t\t\t\t\t\t\t\"emoji\": true\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\"value\": \"1\"\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"text\": {\n\t\t\t\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\t\t\t\"text\": \"Green\",\n\t\t\t\t\t\t\t\t\"emoji\": true\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\"value\": \"2\"\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"text\": {\n\t\t\t\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\t\t\t\"text\": \"Red\",\n\t\t\t\t\t\t\t\t\"emoji\": true\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\"value\": \"3\"\n\t\t\t\t\t\t}\n\t\t\t\t\t]\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"static_select\",\n\t\t\t\t\t\"placeholder\": {\n\t\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\t\"text\": \"Change PAP\",\n\t\t\t\t\t\t\"emoji\": true\n\t\t\t\t\t},\n\t\t\t\t\t\"action_id\": \"update_pap\",\n\t\t\t\t\t\"options\": [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"text\": {\n\t\t\t\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\t\t\t\"text\": \"White\",\n\t\t\t\t\t\t\t\t\"emoji\": true\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\"value\": \"0\"\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"text\": {\n\t\t\t\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\t\t\t\"text\": \"Green\",\n\t\t\t\t\t\t\t\t\"emoji\": true\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\"value\": \"1\"\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"text\": {\n\t\t\t\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\t\t\t\"text\": \"Amber\",\n\t\t\t\t\t\t\t\t\"emoji\": true\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\"value\": \"2\"\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"text\": {\n\t\t\t\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\t\t\t\"text\": \"Red\",\n\t\t\t\t\t\t\t\t\"emoji\": true\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\"value\": \"3\"\n\t\t\t\t\t\t}\n\t\t\t\t\t]\n\t\t\t\t}\n\t\t\t]\n\t\t},\n\t\t{\n\t\t\t\"type\": \"section\",\n\t\t\t\"text\": {\n\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\"text\": \":bee: View the case in TheHive\"\n\t\t\t},\n\t\t\t\"accessory\": {\n\t\t\t\t\"type\": \"button\",\n\t\t\t\t\"style\": \"primary\",\n\t\t\t\t\"text\": {\n\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\"text\": \"View Case\",\n\t\t\t\t\t\"emoji\": true\n\t\t\t\t},\n\t\t\t\t\"value\": \"click_me_123\",\n\t\t\t\t\"url\": \"{{ $('Formatting Dictionaries').item.json.theHiveUrl }}/cases/{{ $json['caseid'] }}/details\",\n\t\t\t\t\"action_id\": \"viewlink\"\n\t\t\t}\n\t\t}\n\t]\n}", "channelId": {"__rl": true, "mode": "list", "value": "C05LAN72WJK", "cachedResultName": "alerts-cyber"}, "messageType": "block", "otherOptions": {}}, "credentials": {"slackApi": {"id": "hOkN2lZmH8XimxKh", "name": "TheHive Slack App"}}, "typeVersion": 2.1}, {"id": "94f38341-660a-451b-a02a-7dc2d327e323", "name": "Pre<PERSON> Fields For Slack - Close", "type": "n8n-nodes-base.set", "position": [1600, 1700], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "0001aed3-a3fb-4229-8fa1-1941f0ee8a12", "name": "title", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[3].fields[0].text }}"}, {"id": "6f40871c-ea6f-4c61-9272-9342c99637e8", "name": "datecreated", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[3].fields[1].text }}"}, {"id": "2ea20d66-380f-4a0f-a1ea-c4740293f48b", "name": "number", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[1].text.text }}"}, {"id": "a22dd686-ac38-4e73-a0ec-051dce57f9fa", "name": "severity", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[8].elements[0].text }}"}, {"id": "1c210cfb-2a03-4e81-a8c5-2db739d98226", "name": "tlp", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[9].elements[0].text }}"}, {"id": "53e1d1a9-055a-48c2-80de-b694871c6620", "name": "pap", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[10].elements[0].text }}"}, {"id": "ee630583-a6ad-4bab-ad78-7846df3ac093", "name": "description", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[3].fields[3].text }}"}, {"id": "48ac7d92-5177-46be-b01e-83493f18ee09", "name": "assignee", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[11].elements[1].text }}"}, {"id": "f9c06ecf-d3d5-490c-a848-02c88fbd3ab4", "name": "profilepic", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[11].elements[0].image_url }}"}, {"id": "bb4ec29d-fac2-4eb9-a177-fca9d2798514", "name": "caseid", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[0].alt_text }}"}, {"id": "7a485f44-b855-4633-ba51-82e7490d7166", "name": "tags", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[3].fields[2].text }}"}, {"id": "a4884fc5-2d92-4c34-af9c-61240bc564d5", "name": "status", "type": "string", "value": "=:negative_squared_cross_mark: *Status* Closed - False Positive"}, {"id": "449c052a-05b4-4c03-99a4-b5fe7ec0e102", "name": "newassign<PERSON>", "type": "string", "value": "={{ $json.response.state.values.actions_assignment_options['change-assignee'].selected_user }}"}, {"id": "827c585d-ae23-48d5-b905-81f8260db61e", "name": "severitylabel", "type": "string", "value": ""}]}}, "typeVersion": 3.3}, {"id": "6a6644ec-0391-40e3-88aa-0d2c4c9a4a41", "name": "<PERSON><PERSON> Fields For Slack - Assign", "type": "n8n-nodes-base.set", "position": [1460, 680], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "0001aed3-a3fb-4229-8fa1-1941f0ee8a12", "name": "title", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[3].fields[0].text }}"}, {"id": "6f40871c-ea6f-4c61-9272-9342c99637e8", "name": "datecreated", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[3].fields[1].text }}"}, {"id": "2ea20d66-380f-4a0f-a1ea-c4740293f48b", "name": "number", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[1].text.text }}"}, {"id": "a22dd686-ac38-4e73-a0ec-051dce57f9fa", "name": "severity", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[8].elements[0].text }}"}, {"id": "1c210cfb-2a03-4e81-a8c5-2db739d98226", "name": "tlp", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[9].elements[0].text }}"}, {"id": "53e1d1a9-055a-48c2-80de-b694871c6620", "name": "pap", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[10].elements[0].text }}"}, {"id": "ee630583-a6ad-4bab-ad78-7846df3ac093", "name": "description", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[3].fields[3].text }}"}, {"id": "48ac7d92-5177-46be-b01e-83493f18ee09", "name": "assignee", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[11].elements[1].text }}"}, {"id": "f9c06ecf-d3d5-490c-a848-02c88fbd3ab4", "name": "profilepic", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[11].elements[0].image_url }}"}, {"id": "bb4ec29d-fac2-4eb9-a177-fca9d2798514", "name": "caseid", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[0].alt_text }}"}, {"id": "7a485f44-b855-4633-ba51-82e7490d7166", "name": "tags", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[3].fields[2].text }}"}, {"id": "a4884fc5-2d92-4c34-af9c-61240bc564d5", "name": "status", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[7].elements[0].text }}"}, {"id": "449c052a-05b4-4c03-99a4-b5fe7ec0e102", "name": "newassign<PERSON>", "type": "string", "value": "={{ $json.response.state.values.actions_assignment_options['change-assignee'].selected_user }}"}]}}, "typeVersion": 3.3}, {"id": "f3d194d1-9548-43ce-a7b6-a1d6b4c1c5cd", "name": "Prep Fields For Slack - Severity", "type": "n8n-nodes-base.set", "position": [1600, 1880], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "0001aed3-a3fb-4229-8fa1-1941f0ee8a12", "name": "title", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[3].fields[0].text }}"}, {"id": "6f40871c-ea6f-4c61-9272-9342c99637e8", "name": "datecreated", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[3].fields[1].text }}"}, {"id": "2ea20d66-380f-4a0f-a1ea-c4740293f48b", "name": "number", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[1].text.text }}"}, {"id": "a22dd686-ac38-4e73-a0ec-051dce57f9fa", "name": "severity", "type": "string", "value": "={{ $('Edit Fields').item.json[\"dictionary\"][\"Severity\"][$('Edit Fields').item.json.response.state.values.actions_assignment_options.update_severity.selected_option.text.text.toUpperCase()] }} *Severity:* {{ $('Edit Fields').item.json.response.state.values.actions_assignment_options.update_severity.selected_option.text.text.toUpperCase() }}"}, {"id": "1c210cfb-2a03-4e81-a8c5-2db739d98226", "name": "tlp", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[9].elements[0].text }}"}, {"id": "53e1d1a9-055a-48c2-80de-b694871c6620", "name": "pap", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[10].elements[0].text }}"}, {"id": "ee630583-a6ad-4bab-ad78-7846df3ac093", "name": "description", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[3].fields[3].text }}"}, {"id": "48ac7d92-5177-46be-b01e-83493f18ee09", "name": "assignee", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[11].elements[1].text }}"}, {"id": "f9c06ecf-d3d5-490c-a848-02c88fbd3ab4", "name": "profilepic", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[11].elements[0].image_url }}"}, {"id": "bb4ec29d-fac2-4eb9-a177-fca9d2798514", "name": "caseid", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[0].alt_text }}"}, {"id": "7a485f44-b855-4633-ba51-82e7490d7166", "name": "tags", "type": "string", "value": "={{ $('Edit Fields').item.json.response.message.blocks[3].fields[2].text.replaceAll(\"\\n\",\"\\\\n\").replaceAll(\"**\",\"*\") }}"}, {"id": "a4884fc5-2d92-4c34-af9c-61240bc564d5", "name": "status", "type": "string", "value": "={{ $json.response.message.blocks[7].elements[0].text }}"}, {"id": "449c052a-05b4-4c03-99a4-b5fe7ec0e102", "name": "newassign<PERSON>", "type": "string", "value": "={{ $json.response.state.values.actions_assignment_options['change-assignee'].selected_user }}"}, {"id": "8278ead1-85c0-4921-ac36-9abfd44f99c8", "name": "severityId", "type": "number", "value": "={{ $('Edit Fields').item.json.response.state.values.actions_assignment_options.update_severity.selected_option.value }}"}]}}, "typeVersion": 3.3}, {"id": "bc66064d-dc16-485a-8e95-19bead7e5ec7", "name": "Update Case Severity", "type": "n8n-nodes-base.theHiveProject", "position": [2060, 1880], "parameters": {"resource": "case", "operation": "update", "caseUpdateFields": {"value": {"id": "={{ $json.caseid }}", "flag": false, "severity": "={{ $json.severityId }}"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ID", "defaultMatch": true, "canBeUsedToMatch": true}, {"id": "title", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "description", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "severity", "type": "options", "display": true, "options": [{"name": "Low", "value": 1}, {"name": "Medium", "value": 2}, {"name": "High", "value": 3}, {"name": "Critical", "value": 4}], "removed": false, "required": false, "displayName": "Severity (Severity of information)", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "startDate", "type": "dateTime", "display": true, "removed": true, "required": false, "displayName": "Start Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "endDate", "type": "dateTime", "display": true, "removed": true, "required": false, "displayName": "End Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "tags", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Tags", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "flag", "type": "boolean", "display": true, "removed": true, "required": false, "displayName": "Flag", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "tlp", "type": "options", "display": true, "options": [{"name": "White", "value": 0}, {"name": "Green", "value": 1}, {"name": "Amber", "value": 2}, {"name": "Red", "value": 3}], "removed": true, "required": false, "displayName": "TLP (Confidentiality of information)", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "pap", "type": "options", "display": true, "options": [{"name": "White", "value": 0}, {"name": "Green", "value": 1}, {"name": "Amber", "value": 2}, {"name": "Red", "value": 3}], "removed": true, "required": false, "displayName": "PAP (Level of exposure of information)", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "status", "type": "options", "display": true, "options": [{"name": "Duplicated", "value": "Duplicated", "description": "Stage: Closed"}, {"name": "FalsePositive", "value": "FalsePositive", "description": "Stage: Closed"}, {"name": "Indeterminate", "value": "Indeterminate", "description": "Stage: Closed"}, {"name": "InProgress", "value": "InProgress", "description": "Stage: InProgress"}, {"name": "New", "value": "New", "description": "Stage: New"}, {"name": "Other", "value": "Other", "description": "Stage: Closed"}, {"name": "TruePositive", "value": "TruePositive", "description": "Stage: Closed"}], "removed": true, "required": false, "displayName": "Status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "assignee", "type": "options", "display": true, "options": [{"name": "Angel", "value": "<EMAIL>"}, {"name": "<PERSON>", "value": "<EMAIL>"}], "removed": true, "required": false, "displayName": "Assignee", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "impactStatus", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Impact Status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "taskRule", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Task Rule", "defaultMatch": false, "canBeUsedToMatch": false}, {"id": "observableRule", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Observable Rule", "defaultMatch": false, "canBeUsedToMatch": false}, {"id": "addTags", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Add Tags", "defaultMatch": false, "canBeUsedToMatch": false}, {"id": "removeTags", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Remove Tags", "defaultMatch": false, "canBeUsedToMatch": false}], "mappingMode": "defineBelow", "matchingColumns": ["id"]}}, "credentials": {"theHiveProjectApi": {"id": "6O5aPdkMaQmc8I9B", "name": "The Hive 5 account"}}, "typeVersion": 1}, {"id": "5f4eae8d-16e2-4a3a-b8dd-7af9f7f0bb4b", "name": "Update Case PAP", "type": "n8n-nodes-base.theHiveProject", "position": [2060, 2060], "parameters": {"resource": "case", "operation": "update", "caseUpdateFields": {"value": {"id": "={{ $json.caseid }}", "pap": "={{ $json.papId }}", "flag": false}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ID", "defaultMatch": true, "canBeUsedToMatch": true}, {"id": "title", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "description", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "severity", "type": "options", "display": true, "options": [{"name": "Low", "value": 1}, {"name": "Medium", "value": 2}, {"name": "High", "value": 3}, {"name": "Critical", "value": 4}], "removed": true, "required": false, "displayName": "Severity (Severity of information)", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "startDate", "type": "dateTime", "display": true, "removed": true, "required": false, "displayName": "Start Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "endDate", "type": "dateTime", "display": true, "removed": true, "required": false, "displayName": "End Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "tags", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Tags", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "flag", "type": "boolean", "display": true, "removed": true, "required": false, "displayName": "Flag", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "tlp", "type": "options", "display": true, "options": [{"name": "White", "value": 0}, {"name": "Green", "value": 1}, {"name": "Amber", "value": 2}, {"name": "Red", "value": 3}], "removed": true, "required": false, "displayName": "TLP (Confidentiality of information)", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "pap", "type": "options", "display": true, "options": [{"name": "White", "value": 0}, {"name": "Green", "value": 1}, {"name": "Amber", "value": 2}, {"name": "Red", "value": 3}], "removed": false, "required": false, "displayName": "PAP (Level of exposure of information)", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "status", "type": "options", "display": true, "options": [{"name": "Duplicated", "value": "Duplicated", "description": "Stage: Closed"}, {"name": "FalsePositive", "value": "FalsePositive", "description": "Stage: Closed"}, {"name": "Indeterminate", "value": "Indeterminate", "description": "Stage: Closed"}, {"name": "InProgress", "value": "InProgress", "description": "Stage: InProgress"}, {"name": "New", "value": "New", "description": "Stage: New"}, {"name": "Other", "value": "Other", "description": "Stage: Closed"}, {"name": "TruePositive", "value": "TruePositive", "description": "Stage: Closed"}], "removed": true, "required": false, "displayName": "Status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "assignee", "type": "options", "display": true, "options": [{"name": "Angel", "value": "<EMAIL>"}, {"name": "<PERSON>", "value": "<EMAIL>"}], "removed": true, "required": false, "displayName": "Assignee", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "impactStatus", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Impact Status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "taskRule", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Task Rule", "defaultMatch": false, "canBeUsedToMatch": false}, {"id": "observableRule", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Observable Rule", "defaultMatch": false, "canBeUsedToMatch": false}, {"id": "addTags", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Add Tags", "defaultMatch": false, "canBeUsedToMatch": false}, {"id": "removeTags", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Remove Tags", "defaultMatch": false, "canBeUsedToMatch": false}], "mappingMode": "defineBelow", "matchingColumns": ["id"]}}, "credentials": {"theHiveProjectApi": {"id": "6O5aPdkMaQmc8I9B", "name": "The Hive 5 account"}}, "typeVersion": 1}, {"id": "c95539f5-e365-4404-897b-65af116c89cb", "name": "Update Case TLP", "type": "n8n-nodes-base.theHiveProject", "position": [2060, 2240], "parameters": {"resource": "case", "operation": "update", "caseUpdateFields": {"value": {"id": "={{ $json.caseid }}", "tlp": "={{ $json.tlpId }}", "flag": false}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ID", "defaultMatch": true, "canBeUsedToMatch": true}, {"id": "title", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "description", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "severity", "type": "options", "display": true, "options": [{"name": "Low", "value": 1}, {"name": "Medium", "value": 2}, {"name": "High", "value": 3}, {"name": "Critical", "value": 4}], "removed": true, "required": false, "displayName": "Severity (Severity of information)", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "startDate", "type": "dateTime", "display": true, "removed": true, "required": false, "displayName": "Start Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "endDate", "type": "dateTime", "display": true, "removed": true, "required": false, "displayName": "End Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "tags", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Tags", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "flag", "type": "boolean", "display": true, "removed": true, "required": false, "displayName": "Flag", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "tlp", "type": "options", "display": true, "options": [{"name": "White", "value": 0}, {"name": "Green", "value": 1}, {"name": "Amber", "value": 2}, {"name": "Red", "value": 3}], "removed": false, "required": false, "displayName": "TLP (Confidentiality of information)", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "pap", "type": "options", "display": true, "options": [{"name": "White", "value": 0}, {"name": "Green", "value": 1}, {"name": "Amber", "value": 2}, {"name": "Red", "value": 3}], "removed": true, "required": false, "displayName": "PAP (Level of exposure of information)", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "status", "type": "options", "display": true, "options": [{"name": "Duplicated", "value": "Duplicated", "description": "Stage: Closed"}, {"name": "FalsePositive", "value": "FalsePositive", "description": "Stage: Closed"}, {"name": "Indeterminate", "value": "Indeterminate", "description": "Stage: Closed"}, {"name": "InProgress", "value": "InProgress", "description": "Stage: InProgress"}, {"name": "New", "value": "New", "description": "Stage: New"}, {"name": "Other", "value": "Other", "description": "Stage: Closed"}, {"name": "TruePositive", "value": "TruePositive", "description": "Stage: Closed"}], "removed": true, "required": false, "displayName": "Status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "assignee", "type": "options", "display": true, "options": [{"name": "Angel", "value": "<EMAIL>"}, {"name": "<PERSON>", "value": "<EMAIL>"}], "removed": true, "required": false, "displayName": "Assignee", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "impactStatus", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Impact Status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "taskRule", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Task Rule", "defaultMatch": false, "canBeUsedToMatch": false}, {"id": "observableRule", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Observable Rule", "defaultMatch": false, "canBeUsedToMatch": false}, {"id": "addTags", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Add Tags", "defaultMatch": false, "canBeUsedToMatch": false}, {"id": "removeTags", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Remove Tags", "defaultMatch": false, "canBeUsedToMatch": false}], "mappingMode": "defineBelow", "matchingColumns": ["id"]}}, "credentials": {"theHiveProjectApi": {"id": "6O5aPdkMaQmc8I9B", "name": "The Hive 5 account"}}, "typeVersion": 1}, {"id": "edfedf66-df2a-421e-a53d-11ee32623be0", "name": "Acknowledge Close Case to Slack", "type": "n8n-nodes-base.respondToWebhook", "position": [1180, 1700], "parameters": {"options": {"responseCode": 200}, "respondWith": "noData"}, "typeVersion": 1}, {"id": "791fea8b-67b3-4dd8-943f-29f750485a10", "name": "Acknowledge Severity Update to Slack", "type": "n8n-nodes-base.respondToWebhook", "position": [1180, 1880], "parameters": {"options": {"responseCode": 200}, "respondWith": "noData"}, "typeVersion": 1}, {"id": "14f59582-7a18-44ab-bb1e-19d5141dace5", "name": "Acknowledge PAP Update to Slack", "type": "n8n-nodes-base.respondToWebhook", "position": [1180, 2060], "parameters": {"options": {"responseCode": 200}, "respondWith": "noData"}, "typeVersion": 1}, {"id": "55b3ecfa-2be6-41f5-818c-5980f896832f", "name": "Acknowledge TLP Update to Slack", "type": "n8n-nodes-base.respondToWebhook", "position": [1180, 2240], "parameters": {"options": {"responseCode": 200}, "respondWith": "noData"}, "typeVersion": 1}, {"id": "4a4ef31e-de26-4e4e-b0f5-bef77c8c2801", "name": "Acknowledge Status Update to Slack", "type": "n8n-nodes-base.respondToWebhook", "position": [1180, 2420], "parameters": {"options": {"responseCode": 200}, "respondWith": "noData"}, "typeVersion": 1}, {"id": "cb7647b0-199c-4aa8-84d3-748bb52f1e7e", "name": "Acknowledge Modal Request to Slack", "type": "n8n-nodes-base.respondToWebhook", "position": [1180, 3040], "parameters": {"options": {"responseCode": 200}, "respondWith": "noData"}, "typeVersion": 1}, {"id": "20f028c6-1e15-4d3c-a4ad-5ae716fdc1db", "name": "Sticky Note16", "type": "n8n-nodes-base.stickyNote", "position": [1940, 2680], "parameters": {"color": 5, "width": 431.4429614751546, "height": 532.2137131625435, "content": "![Imgur](https://uploads.n8n.io/templates/hivemodal.png)"}, "typeVersion": 1}, {"id": "661e067b-c11c-44e1-9767-a7b5776a2c4c", "name": "Sticky Note17", "type": "n8n-nodes-base.stickyNote", "position": [1100, -83.7829059126309], "parameters": {"color": 5, "width": 431.4429614751546, "height": 453.21603562878454, "content": "![Imgur](https://uploads.n8n.io/templates/thehivereport.png)\n### New cases will appear in slack in this format with the ability to quickly change variables in TheHive with quick actions baked in. "}, "typeVersion": 1}, {"id": "08c174f4-b486-4a18-b627-2d64229d9148", "name": "Parse Message Type", "type": "n8n-nodes-base.switch", "position": [860, 900], "parameters": {"rules": {"values": [{"outputKey": "Assign to User", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.response.actions[0].action_id }}", "rightValue": "change-assignee"}]}, "renameOutput": true}, {"outputKey": "Close Case", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "d5e3230c-d60a-41a5-815c-117f9e8d7598", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.response.actions[0].action_id }}", "rightValue": "close_case"}]}, "renameOutput": true}, {"outputKey": "Update Severity", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "d0f5a4b9-c03c-4dc4-a6bc-41de387b0059", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.response.actions[0].action_id }}", "rightValue": "update_severity"}]}, "renameOutput": true}, {"outputKey": "Add Task", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "8cd2f467-0683-46af-98b4-1476c9fded2b", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.response.actions[0].action_id }}", "rightValue": "add_task"}]}, "renameOutput": true}, {"outputKey": "Task Modal Details", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "374c13a4-2590-4ae4-b7fb-e01116ba714c", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.response.view.type }}", "rightValue": "modal"}]}, "renameOutput": true}, {"outputKey": "Update Pap", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "5bda6be6-cd63-448d-9f2f-c6ac47fdc024", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.response.actions[0].action_id }}", "rightValue": "update_pap"}]}, "renameOutput": true}, {"outputKey": "Update Status", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "7789be18-aba0-4fa8-a4d8-792e1b53fcc5", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.response.actions[0].action_id }}", "rightValue": "update-status"}]}, "renameOutput": true}, {"outputKey": "Update TLP", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "aff9e422-c8e6-416c-9c80-e2757ed97da1", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.response.actions[0].action_id }}", "rightValue": "update_tlp"}]}, "renameOutput": true}, {"outputKey": "View Link", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "d35e11f3-0e5f-48a9-98f7-66d0ed676b53", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.response.actions[0].action_id }}", "rightValue": "viewlink"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3}, {"id": "1f489dfa-d1d4-450c-853d-6d006581018a", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1140, 3860], "parameters": {"color": 7, "width": 839.546315272846, "height": 472.04545691749547, "content": "![slack](https://uploads.n8n.io/templates/slack.png)\n## 🤝Responding to Slack when someone clicks a link \nThis section of the workflow ensures that when a user interacts with a Slack message that invokes an n8n webhook, the server promptly acknowledges this with a \"200\" or \"204\" HTTP status response. This sticky note underscores the importance of a quick and positive response to maintain seamless interactions and ensure <PERSON><PERSON><PERSON> receives the necessary feedback to conclude the user's action successfully."}, "typeVersion": 1}, {"id": "ad83c6b0-cde4-48b6-b505-8f9e1ded2066", "name": "Respond positive to <PERSON><PERSON><PERSON> when someone clicks a link", "type": "n8n-nodes-base.respondToWebhook", "position": [1180, 4140], "parameters": {"options": {"responseCode": 204}, "respondWith": "noData"}, "typeVersion": 1}, {"id": "f2668b62-cd3c-41d6-ada3-37e6ba331b90", "name": "Respond 204 to Slack", "type": "n8n-nodes-base.respondToWebhook", "position": [1380, 3720], "parameters": {"options": {"responseCode": 204}, "respondWith": "noData"}, "typeVersion": 1}, {"id": "a46648ce-f125-4de6-95cf-9c61c466e43c", "name": "Close Modal with 204 response", "type": "n8n-nodes-base.respondToWebhook", "position": [1420, 3540], "parameters": {"options": {"responseCode": 204}, "respondWith": "noData"}, "typeVersion": 1}, {"id": "aa35e086-ace4-413a-9cd8-bed51dad94e7", "name": "Get Slack User's Email From Slack", "type": "n8n-nodes-base.slack", "position": [1700, 680], "parameters": {"user": {"__rl": true, "mode": "id", "value": "={{ $json.newassignee }}"}, "resource": "user"}, "credentials": {"slackApi": {"id": "hOkN2lZmH8XimxKh", "name": "TheHive Slack App"}}, "typeVersion": 2.1}, {"id": "717a650d-250f-4538-92cb-fa2f7368a9cd", "name": "Update TheHive Case with new As<PERSON>ee", "type": "n8n-nodes-base.theHiveProject", "position": [2020, 680], "parameters": {"resource": "case", "operation": "update", "caseUpdateFields": {"value": {"id": "={{ $('Prep Fields For Slack - Assign').item.json.caseid }}", "flag": false, "assignee": "={{ $json.profile.email }}"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ID", "defaultMatch": true, "canBeUsedToMatch": true}, {"id": "title", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "description", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "severity", "type": "options", "display": true, "options": [{"name": "Low", "value": 1}, {"name": "Medium", "value": 2}, {"name": "High", "value": 3}, {"name": "Critical", "value": 4}], "removed": true, "required": false, "displayName": "Severity (Severity of information)", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "startDate", "type": "dateTime", "display": true, "removed": true, "required": false, "displayName": "Start Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "endDate", "type": "dateTime", "display": true, "removed": true, "required": false, "displayName": "End Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "tags", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Tags", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "flag", "type": "boolean", "display": true, "removed": true, "required": false, "displayName": "Flag", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "tlp", "type": "options", "display": true, "options": [{"name": "White", "value": 0}, {"name": "Green", "value": 1}, {"name": "Amber", "value": 2}, {"name": "Red", "value": 3}], "removed": true, "required": false, "displayName": "TLP (Confidentiality of information)", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "pap", "type": "options", "display": true, "options": [{"name": "White", "value": 0}, {"name": "Green", "value": 1}, {"name": "Amber", "value": 2}, {"name": "Red", "value": 3}], "removed": true, "required": false, "displayName": "PAP (Level of exposure of information)", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "status", "type": "options", "display": true, "options": [{"name": "Duplicated", "value": "Duplicated", "description": "Stage: Closed"}, {"name": "FalsePositive", "value": "FalsePositive", "description": "Stage: Closed"}, {"name": "Indeterminate", "value": "Indeterminate", "description": "Stage: Closed"}, {"name": "InProgress", "value": "InProgress", "description": "Stage: InProgress"}, {"name": "New", "value": "New", "description": "Stage: New"}, {"name": "Other", "value": "Other", "description": "Stage: Closed"}, {"name": "TruePositive", "value": "TruePositive", "description": "Stage: Closed"}], "removed": true, "required": false, "displayName": "Status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "assignee", "type": "options", "display": true, "options": [{"name": "Angel", "value": "<EMAIL>"}, {"name": "<PERSON>", "value": "<EMAIL>"}], "removed": false, "required": false, "displayName": "Assignee", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "impactStatus", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Impact Status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "taskRule", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Task Rule", "defaultMatch": false, "canBeUsedToMatch": false}, {"id": "observableRule", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Observable Rule", "defaultMatch": false, "canBeUsedToMatch": false}, {"id": "addTags", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Add Tags", "defaultMatch": false, "canBeUsedToMatch": false}, {"id": "removeTags", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Remove Tags", "defaultMatch": false, "canBeUsedToMatch": false}], "mappingMode": "defineBelow", "matchingColumns": ["id"]}}, "credentials": {"theHiveProjectApi": {"id": "6O5aPdkMaQmc8I9B", "name": "The Hive 5 account"}}, "typeVersion": 1}, {"id": "1915c609-edf2-4bfe-95db-88ab31ae4fa6", "name": "Respond to Slack with 200 response", "type": "n8n-nodes-base.respondToWebhook", "position": [1220, 680], "parameters": {"options": {"responseCode": 200}, "respondWith": "noData"}, "typeVersion": 1}, {"id": "2b9b03cd-2f70-429a-b27f-9eb26663101a", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [1660, 460], "parameters": {"color": 5, "width": 171.**************, "height": 165.**************, "content": "### 📧 GDPR Warning\nThis workflow requires additional permissions from your Slack app that may not be available in your region."}, "typeVersion": 1}, {"id": "eb76195a-42ef-4716-9b94-ba52f3746e3c", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [2220, 420], "parameters": {"color": 5, "width": 306.**************, "height": 207.**************, "content": "### 📧 Ensure the Slack users emails match those in TheHive\nThis assumes that your TheHive user's emails are the same as the same user's email in Slack. Please note that there is no error handling for case's assigned user, so if you assign someone that has an email in Slack that does not match the user in TheHive, it will fail. "}, "typeVersion": 1}, {"id": "1da14415-e66a-41bc-8f6e-f57c1180924c", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [-420, 500], "parameters": {"width": 670.2591130283372, "height": 1196.0687210874733, "content": "![theHive](https://uploads.n8n.io/templates/thehive.png)\n# Streamline Case Management in TheHive via Slack!\n\n## Our **TheHive Slack Integration** empowers SOC analysts by allowing them to efficiently manage and update case attributes directly within Slack, reducing the need to switch contexts and enhancing response time.\n\n\n**Key Features:**\n- **Direct Case Management**: Modify case details such as assignee, severity, status, and more through intuitive form inputs embedded within Slack messages.\n- **Seamless Integration**: Assumes matching email addresses between TheHive and Slack users for straightforward assignee updates. Note: Ensure email consistency to avoid assignment errors.\n- **Instant Case Actions**: Quickly close cases as false positives or adjust threat levels with minimal clicks, directly impacting case status in TheHive and reflecting updates immediately in Slack.\n- **Task Management**: Add tasks to cases through a user-friendly modal popup, fostering better task tracking and delegation within your team.\n\n\n**Operational Benefits:**\n- **Efficiency**: Enables analysts to perform multiple case actions without leaving Slack, streamlining workflows and saving valuable time.\n- **Accuracy**: Reduces the chances of human error by providing a controlled interface for case updates.\n- **Agility**: Enhances the SOC team's agility by providing tools for rapid response and case management, crucial for effective security operations.\n\n\n**Setup Tips:**\n- Verify that all SOC team members have matching email IDs in TheHive and Slack.\n- Familiarize your team with the Slack form inputs and ensure they understand the importance of accurate data entry.\n- Regularly review and update the integration settings to accommodate any changes in your security operations protocols.\n\n\n**Need Help?**\n- For detailed setup instructions or troubleshooting, refer to our [Integration Guide](https://docs.thehive-project.org) or reach out on our [Support Forum](https://community.thehive-project.org).\n\n\nLeverage this integration to maximize your SOC team's efficiency and responsiveness, ensuring that case management is as streamlined and effective as possible.\n"}, "typeVersion": 1}, {"id": "3c56a4a9-36ac-41d3-a4ff-425b93a26341", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [1600, 3720], "parameters": {"color": 5, "width": 342.34463660857455, "height": 106.35161810996954, "content": "### 📧 GDPR Warning\nThis workflow requires additional permissions from your Slack app that may not be available in your region or organization."}, "typeVersion": 1}, {"id": "c87cdbc8-9f86-46a1-9789-7ad992b6f01d", "name": "Get <PERSON>ail From Slack to assign the task to in TheHive", "type": "n8n-nodes-base.slack", "position": [1700, 3540], "parameters": {"user": {"__rl": true, "mode": "id", "value": "={{ $json.response.user.id }}"}, "resource": "user"}, "credentials": {"slackApi": {"id": "hOkN2lZmH8XimxKh", "name": "TheHive Slack App"}}, "typeVersion": 2.1}, {"id": "f6e6512f-60d8-4733-833a-b88edf6c978c", "name": "Add a task to TheHive", "type": "n8n-nodes-base.theHiveProject", "position": [2280, 3540], "parameters": {"caseId": {"__rl": true, "mode": "id", "value": "={{ $('Edit Fields').item.json.response.view.blocks[0].elements[1].text }}"}, "resource": "task", "taskFields": {"value": {"flag": "={{ $('Edit Fields').item.json[\"response\"][\"view\"][\"state\"][\"values\"][\"case-options\"][\"submit-task-option\"]['selected_options'].some(option => option.value === 'flagged') }}", "group": "={{ $('Edit Fields').item.json.response.view.state.values.group_block.group_input.value }}", "title": "={{ $('Edit Fields').item.json.response.view.state.values.title_block.title_input.value }}", "dueDate": "={{ $('Edit Fields').item.json.response.view.state.values.due_date_block.due_date_input.selected_date }}", "assignee": "={{ $json.profile.email }}", "mandatory": "={{ $('Edit Fields').item.json[\"response\"][\"view\"][\"state\"][\"values\"][\"case-options\"][\"submit-task-option\"]['selected_options'].some(option => option.value === 'mandatory') }}", "description": "={{ $('Edit Fields').item.json.response.view.state.values.description_block.description_input.value }}"}, "schema": [{"id": "title", "type": "string", "display": true, "removed": false, "required": true, "displayName": "Title", "defaultMatch": false}, {"id": "description", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Description", "defaultMatch": false}, {"id": "group", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Group", "defaultMatch": false}, {"id": "status", "type": "stirng", "display": true, "removed": true, "required": false, "displayName": "Status", "defaultMatch": false}, {"id": "flag", "type": "boolean", "display": true, "removed": false, "required": false, "displayName": "Flag", "defaultMatch": false}, {"id": "startDate", "type": "dateTime", "display": true, "removed": true, "required": false, "displayName": "Start Date", "defaultMatch": false}, {"id": "dueDate", "type": "dateTime", "display": true, "removed": false, "required": false, "displayName": "Due Date", "defaultMatch": false}, {"id": "endDate", "type": "dateTime", "display": true, "removed": true, "required": false, "displayName": "End Date", "defaultMatch": false}, {"id": "assignee", "type": "options", "display": true, "options": [{"name": "Angel", "value": "<EMAIL>"}, {"name": "<PERSON>", "value": "<EMAIL>"}], "removed": false, "required": false, "displayName": "Assignee", "defaultMatch": false}, {"id": "mandatory", "type": "boolean", "display": true, "removed": false, "required": false, "displayName": "Mandatory", "defaultMatch": false}, {"id": "order", "type": "number", "display": true, "removed": true, "required": false, "displayName": "Order", "defaultMatch": false}], "mappingMode": "defineBelow", "matchingColumns": []}}, "credentials": {"theHiveProjectApi": {"id": "6O5aPdkMaQmc8I9B", "name": "The Hive 5 account"}}, "typeVersion": 1}, {"id": "ebcd8020-b157-4c0f-a726-80dd4864cbcf", "name": "Receive Button Press", "type": "n8n-nodes-base.webhook", "position": [360, 880], "webhookId": "99db3e73-57d8-4107-ab02-5b7e713894ad", "parameters": {"path": "slackthehivewebhook", "options": {}, "httpMethod": "POST", "responseMode": "responseNode"}, "typeVersion": 2}], "pinData": {}, "connections": {"Edit Fields": {"main": [[{"node": "Parse Message Type", "type": "main", "index": 0}]]}, "Map Actions": {"main": [[{"node": "Build Final Block", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Pre<PERSON> Fields For Slack", "type": "main", "index": 0}]]}, "TheHive Trigger": {"main": [[{"node": "Formatting Dictionaries", "type": "main", "index": 0}]]}, "Update Case PAP": {"main": [[{"node": "PAP Case Block Rebuild", "type": "main", "index": 0}]]}, "Update Case TLP": {"main": [[{"node": "TLP Case Block Rebuild", "type": "main", "index": 0}]]}, "Build Final Block": {"main": [[{"node": "Update Message with new As<PERSON>ee", "type": "main", "index": 0}]]}, "Parse Message Type": {"main": [[{"node": "Respond to Slack with 200 response", "type": "main", "index": 0}], [{"node": "Acknowledge Close Case to Slack", "type": "main", "index": 0}], [{"node": "Acknowledge Severity Update to Slack", "type": "main", "index": 0}], [{"node": "Acknowledge Modal Request to Slack", "type": "main", "index": 0}], [{"node": "Check if Case Options", "type": "main", "index": 0}], [{"node": "Acknowledge PAP Update to Slack", "type": "main", "index": 0}], [{"node": "Acknowledge Status Update to Slack", "type": "main", "index": 0}], [{"node": "Acknowledge TLP Update to Slack", "type": "main", "index": 0}], [{"node": "Respond positive to <PERSON><PERSON><PERSON> when someone clicks a link", "type": "main", "index": 0}]]}, "Receive Button Press": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Update Case Severity": {"main": [[{"node": "Severity Case Block Rebuild1", "type": "main", "index": 0}]]}, "Check if Case Options": {"main": [[{"node": "Close Modal with 204 response", "type": "main", "index": 0}], [{"node": "Respond 204 to Slack", "type": "main", "index": 0}]]}, "Prep Fields For Slack": {"main": [[{"node": "Post New Case To Slack", "type": "main", "index": 0}]]}, "PAP Case Block Rebuild": {"main": [[{"node": "Map Actions", "type": "main", "index": 0}]]}, "TLP Case Block Rebuild": {"main": [[{"node": "Map Actions", "type": "main", "index": 0}]]}, "Formatting Dictionaries": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "Case Slack Block Rebuild": {"main": [[{"node": "Map Actions", "type": "main", "index": 0}]]}, "Close Case Block Rebuild": {"main": [[{"node": "Map Actions", "type": "main", "index": 0}]]}, "Update Status in TheHive": {"main": [[{"node": "Status Case Block Rebuild", "type": "main", "index": 0}]]}, "Prep Fields For PAP Slack": {"main": [[{"node": "Update Case PAP", "type": "main", "index": 0}]]}, "Prep Fields For TLP Slack": {"main": [[{"node": "Update Case TLP", "type": "main", "index": 0}]]}, "Status Case Block Rebuild": {"main": [[{"node": "Map Actions", "type": "main", "index": 0}]]}, "Close Case as False Positive": {"main": [[{"node": "Close Case Block Rebuild", "type": "main", "index": 0}]]}, "Prep Fields For Status Slack": {"main": [[{"node": "Update Status in TheHive", "type": "main", "index": 0}]]}, "Severity Case Block Rebuild1": {"main": [[{"node": "Map Actions", "type": "main", "index": 0}]]}, "Close Modal with 204 response": {"main": [[{"node": "Get <PERSON>ail From Slack to assign the task to in TheHive", "type": "main", "index": 0}]]}, "Prep Fields For Slack - Close": {"main": [[{"node": "Close Case as <PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Prep Fields For Slack - Assign": {"main": [[{"node": "Get Slack User's Email From Slack", "type": "main", "index": 0}]]}, "Acknowledge Close Case to Slack": {"main": [[{"node": "Pre<PERSON> Fields For Slack - Close", "type": "main", "index": 0}]]}, "Acknowledge PAP Update to Slack": {"main": [[{"node": "Prep Fields For PAP Slack", "type": "main", "index": 0}]]}, "Acknowledge TLP Update to Slack": {"main": [[{"node": "Prep Fields For TLP Slack", "type": "main", "index": 0}]]}, "Prep Fields For Slack - Severity": {"main": [[{"node": "Update Case Severity", "type": "main", "index": 0}]]}, "Get Slack User's Email From Slack": {"main": [[{"node": "Update TheHive Case with new As<PERSON>ee", "type": "main", "index": 0}]]}, "Acknowledge Modal Request to Slack": {"main": [[{"node": "Task Modal", "type": "main", "index": 0}]]}, "Acknowledge Status Update to Slack": {"main": [[{"node": "Prep Fields For Status Slack", "type": "main", "index": 0}]]}, "Respond to Slack with 200 response": {"main": [[{"node": "<PERSON><PERSON> Fields For Slack - Assign", "type": "main", "index": 0}]]}, "Acknowledge Severity Update to Slack": {"main": [[{"node": "Prep Fields For Slack - Severity", "type": "main", "index": 0}]]}, "Update TheHive Case with new Assignee": {"main": [[{"node": "Case Slack Block Rebuild", "type": "main", "index": 0}]]}, "Respond positive to Slack when someone clicks a link": {"main": [[{"node": "No Action Needed", "type": "main", "index": 0}]]}, "Get Email From Slack to assign the task to in TheHive": {"main": [[{"node": "Add a task to TheHive", "type": "main", "index": 0}]]}}}