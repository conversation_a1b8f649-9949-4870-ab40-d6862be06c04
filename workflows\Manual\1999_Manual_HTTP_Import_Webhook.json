{"id": "wGv0NPBA0QLp4rQ6", "meta": {"instanceId": "b3c467df4053d13fe31cc98f3c66fa1d16300ba750506bfd019a0913cec71ea3"}, "name": "Upload video to drive via google script", "tags": [], "nodes": [{"id": "b89e494d-f85d-4ad5-b0ba-5699f59a58d5", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-300, -40], "parameters": {}, "typeVersion": 1}, {"id": "061597f1-d57d-4733-bc9f-3a3070bd5e95", "name": "Rename Uploaded Video", "type": "n8n-nodes-base.googleDrive", "position": [180, -40], "parameters": {"fileId": {"__rl": true, "mode": "url", "value": "={{ $json.driveUrl }}"}, "options": {}, "operation": "update", "newUpdatedFileName": "Music Video 1"}, "credentials": {"googleDriveOAuth2Api": {"id": "l8Cc2MEVE7foBfbK", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "7e8ff194-fdb7-43e4-afde-bba466ac9dd3", "name": "Send URL to GDrive Script and Upload", "type": "n8n-nodes-base.httpRequest", "position": [-60, -40], "parameters": {"url": "\"your_google_script_web_app_url\"", "method": "POST", "options": {}, "jsonBody": "{\n  \"videoUrl\": \"https://example.com/path/to/your.mp4\",\n  \"secret\": \"your-strong-secret-here\"\n}", "sendBody": true, "specifyBody": "json"}, "typeVersion": 4.2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "b554bac3-27d2-498a-9e5a-b98cde9ea593", "connections": {"When clicking ‘Test workflow’": {"main": [[{"node": "Send URL to GDrive Script and Upload", "type": "main", "index": 0}]]}, "Send URL to GDrive Script and Upload": {"main": [[{"node": "Rename Uploaded Video", "type": "main", "index": 0}]]}}}