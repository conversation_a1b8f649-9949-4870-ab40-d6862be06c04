{"meta": {"instanceId": "38d37c49298b42c645e6a7693766d7c3522b24e54454034f955422b5d7af611c"}, "nodes": [{"id": "d2e53ca7-07e1-499b-8f29-9a2a1de10824", "name": "Filter incomplete tasks only", "type": "n8n-nodes-base.filter", "position": [220, 380], "parameters": {"conditions": {"string": [{"value1": "={{ $json.Status }}", "value2": "Done", "operation": "notEqual"}]}}, "typeVersion": 1}, {"id": "2ff58ec6-58a3-4bf0-adba-d2d0ae87944e", "name": "Get All Slack Users (Darryn)", "type": "n8n-nodes-base.slack", "position": [440, 380], "parameters": {"resource": "user", "operation": "getAll", "authentication": "oAuth2"}, "credentials": {"slackOAuth2Api": {"id": "xkCA23zAF89RcovP", "name": "Slack Account (OAuth)  (<EMAIL>)"}}, "executeOnce": false, "typeVersion": 1}, {"id": "ff9a6853-b254-4a4f-aa8d-89546e78de0b", "name": "Get To Dos from Tasks Database", "type": "n8n-nodes-base.notion", "position": [20, 380], "parameters": {"options": {}, "resource": "databasePage", "operation": "getAll", "databaseId": {"__rl": true, "mode": "list", "value": "1197be57-c54f-815f-8d3b-fdbbb741b19c", "cachedResultUrl": "https://www.notion.so/1197be57c54f815f8d3bfdbbb741b19c", "cachedResultName": "Tasks "}}, "credentials": {"notionApi": {"id": "n1DsMuDcWjPxXlfD", "name": "Notion Account (<EMAIL>)"}}, "typeVersion": 1}, {"id": "c9856834-1a6b-4e2e-bb77-9b3c74c34ccf", "name": "Schedule Mon - Friday @ 09:00am", "type": "n8n-nodes-base.cron", "position": [-600, 380], "parameters": {"triggerTimes": {"item": [{"mode": "custom", "cronExpression": "0 9 * * 1,2,3,4,5"}]}}, "typeVersion": 1}, {"id": "41e67948-1d4a-4654-8817-dbcf61eb061d", "name": "Set Notion User Emails", "type": "n8n-nodes-base.set", "position": [-380, 380], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "********-c288-446a-96df-ddfc3fe332f0", "name": "User 1 Notion Email", "type": "string", "value": "<EMAIL>"}, {"id": "bed7739d-640a-43cc-9fb5-4472844ccc09", "name": "User 2 Notion Email", "type": "string", "value": "<EMAIL>"}]}}, "typeVersion": 3.4}, {"id": "3a59b653-dd65-4443-b2d0-44cce3e780dd", "name": "Set Slack User Full Name", "type": "n8n-nodes-base.set", "position": [-180, 380], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "********-c288-446a-96df-ddfc3fe332f0", "name": "User 1 Slack Full Name", "type": "string", "value": "<PERSON><PERSON>"}, {"id": "bed7739d-640a-43cc-9fb5-4472844ccc09", "name": "User 2 Slack Full Name", "type": "string", "value": "<PERSON>"}]}}, "typeVersion": 3.4}, {"id": "340af945-5e22-408f-86de-b4e4160ec751", "name": "Send a Direct Message to User 1", "type": "n8n-nodes-base.slack", "position": [1200, 260], "parameters": {"text": "# TO DO", "channel": "={{ $json.id }}", "attachments": [{"title": "=☑️  {{ $('Filter incomplete tasks only').item.json.Task }} (Due: {{ $('Filter incomplete tasks only').item.json.Due.start }})"}], "otherOptions": {"mrkdwn": true}, "authentication": "oAuth2"}, "credentials": {"slackOAuth2Api": {"id": "xkCA23zAF89RcovP", "name": "Slack Account (OAuth)  (<EMAIL>)"}}, "typeVersion": 1}, {"id": "df9bc0b5-7a34-407d-a412-dd4022943e41", "name": "Send a Direct Message to User 2", "type": "n8n-nodes-base.slack", "position": [1200, 500], "parameters": {"text": "# TO DO", "channel": "={{ $json.id }}", "attachments": [{"title": "=☑️  {{ $('Filter incomplete tasks only').item.json.Task }} (Due: {{ $('Filter incomplete tasks only').item.json.Due.start }})"}], "otherOptions": {"mrkdwn": true}, "authentication": "oAuth2"}, "credentials": {"slackOAuth2Api": {"id": "xkCA23zAF89RcovP", "name": "Slack Account (OAuth)  (<EMAIL>)"}}, "typeVersion": 1}, {"id": "f6ab26d3-27d9-4b06-886d-64bbaf5d4f92", "name": "Switch for Notion Users Emails", "type": "n8n-nodes-base.switch", "position": [720, 380], "parameters": {"rules": {"values": [{"outputKey": "User 1 Notion Tasks", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "contains"}, "leftValue": "={{ $('Filter incomplete tasks only').item.json['Notion User'].toString() }}", "rightValue": "={{ $('Set Notion User Emails').item.json['User 1 Notion Email'] }}"}]}, "renameOutput": true}, {"outputKey": "User 2 Notion Tasks", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "d0bf512b-15e4-4dd6-b468-50cec25c3e2c", "operator": {"type": "string", "operation": "contains"}, "leftValue": "={{ $('Filter incomplete tasks only').item.json['Notion User'].toString() }}", "rightValue": "={{ $('Set Notion User Emails').item.json['User 2 Notion Email'] }}"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "4492bc68-e8ef-4417-b3d2-d7fb9418db17", "name": "Filter Slack User 1 Full Name", "type": "n8n-nodes-base.filter", "position": [980, 260], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "6aafbbd7-065c-4253-b905-07c7df9210c1", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.profile.real_name }}", "rightValue": "={{ $('Set Slack User Full Name').item.json['User 1 Slack Full Name'] }}"}]}}, "typeVersion": 2.2}, {"id": "159b3436-9141-4769-a495-14e9fdd37f1d", "name": "Filter Slack User 2 Full Name", "type": "n8n-nodes-base.filter", "position": [980, 500], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "6aafbbd7-065c-4253-b905-07c7df9210c1", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.profile.real_name }}", "rightValue": "={{ $('Set Slack User Full Name').item.json['User 2 Slack Full Name'] }}"}]}}, "typeVersion": 2.2}, {"id": "5b863aea-a4d5-486e-82a9-a4f2b713f8f8", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-670.7551894447033, 180], "parameters": {"color": 7, "width": 232.28640473083397, "height": 395.93315440190497, "content": "## Schedule Mon - Friday @ 09:00am\nTriggers the workflow every weekday at 9:00 AM. This ensures that the reminders are sent at the start of the day.\n"}, "typeVersion": 1}, {"id": "420236d0-5139-4faf-9b2e-dca35b15a6b9", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-424.62240527764834, 180], "parameters": {"color": 7, "width": 377.1025213664834, "height": 397.4539493179217, "content": "## Set Notion User Emails and Slack User Full Name\nStores the email addresses of Notion users, and full names of the Slack users to be matched later in the workflow."}, "typeVersion": 1}, {"id": "751c8fb7-0b38-4a83-bf55-82be400f59a7", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-33.06639208352749, 180], "parameters": {"width": 400.70229197070614, "height": 397.31352154531925, "content": "## Get To Dos from Tasks Database and Filter incomplete tasks only\nRetrieves all tasks from the specified Notion database and filters out tasks that are marked as \"Done,\" ensuring that only incomplete tasks are processed."}, "typeVersion": 1}, {"id": "4e352442-ce25-4e36-b334-c6b1e0896d06", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [384.62240527764834, 180], "parameters": {"color": 3, "width": 240.32164378964495, "height": 398.1826056622561, "content": "## Get All Slack Users\nFetches all users from Slack to enable proper identification of who should receive the reminders.\n"}, "typeVersion": 1}, {"id": "5dde41f6-b66f-4abb-8bc6-9226b06e9331", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [640, 180], "parameters": {"width": 267.*************, "height": 398.**************, "content": "## Switch for Notion Users Emails\nDetermines which user (User 1 or User 2) is assigned the task in Notion by comparing email addresses, routing the workflow accordingly.\n"}, "typeVersion": 1}, {"id": "3babdb0f-29d7-4ff7-9174-3ae0b5a4979d", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [920, 83.**************], "parameters": {"color": 3, "width": 455.**************, "height": 592.************, "content": "## Filter Slack User and Send a Direct Message to User\nFilters Slack users to identify User 1 based on their full name and sends a direct Slack message to User with the details of their incomplete tasks.\n"}, "typeVersion": 1}, {"id": "43e36d12-b477-49fa-aed0-e28304310894", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [-1140, 260], "parameters": {"color": 6, "width": 396.*************, "height": 282.*************, "content": "### Get More Templates Like This 👇\n[![Video Thumbnail](https://onlinethinking.io/wp-content/uploads/2024/10/Lets-Automate-It-Community.png)](http://onlinethinking.io/community)\n"}, "typeVersion": 1}, {"id": "6eefe33e-0dc9-4ee8-8ad4-f61078e74227", "name": "Sticky Note14", "type": "n8n-nodes-base.stickyNote", "position": [-1520, 620], "parameters": {"width": 777.*************, "height": 216.**************, "content": "## Setup\n1. **`Connect Notion`**: You will need to connect your Notion account and specify the database containing tasks.\n2. **`Connect Slack`**: Authenticate with S<PERSON>ck using OAuth to allow the workflow to send messages on your behalf.\n3. **`Notion user Email Address mapping`**: Ensure that the Notion users’ email addresses are correctly mapped to their corresponding Notion user profiles.\n4. **`Slack user Full Name mapping`**: Ensure that the Slack users’ full names are correctly mapped to their corresponding Slack user profiles.\n5. **`Adjust schedule`**: If needed, modify the schedule node to run at a different time or frequency.\n"}, "typeVersion": 1}, {"id": "8a91c90e-a9b6-4948-beb4-773e8c9f91f7", "name": "Sticky Note15", "type": "n8n-nodes-base.stickyNote", "position": [-1520, 860], "parameters": {"color": 7, "width": 777.*************, "height": 179.*************, "content": "## How to customize this workflow\n- **`Change the Notion Tasks database`**: You can adjust the workflow to pull tasks from a different Notion database by modifying the \"Get To Dos from Tasks Database\" node.\n- **`Add more users`**: The workflow currently supports two users, but you can expand it to support more by adding additional logic in the \"Switch for Notion Users Emails\" node.\n- **`Modify the message format`**: The Slack message content can be customized further to include more task details or change the message format.\n"}, "typeVersion": 1}, {"id": "9e03e28e-f4ce-4c75-85ab-e7ffe0f1bfd7", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [-1520, 220], "parameters": {"color": 7, "width": 366.75796434038665, "height": 379.6332969008185, "content": "## What this workflow does\n1. **`Triggers every weekday at 9:00 AM`**: The workflow runs at 9:00 AM, Monday through Friday.\n2. **`Fetches tasks from Notion`**: It retrieves tasks from a Notion database.\n3. **`Filters incomplete tasks`**: The workflow filters tasks that are not marked as \"Done.\"\n4. **`Fetches Slack users`**: It retrieves all Slack users to ensure that the reminders are sent to the correct user.\n5. **`Matches tasks to the correct user`**: It checks the Notion task assignee and matches it with the appropriate Slack user.\n6. **`Sends Slack reminders`**: Sends a Slack direct message to each user with their incomplete tasks and due dates."}, "typeVersion": 1}, {"id": "eb0942b9-d18f-46a2-bea0-23eb07bb1d85", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [-1535, 58], "parameters": {"color": 7, "width": 809.515353297114, "height": 999.58820121335, "content": "## Automated Notion Task Reminders via Slack\nBuilt for the [Let's Automate It Community](http://onlinethinking.io/community) by [Optimus Agency](https://optimus01.co.za/)\n\nThis workflow automates sending task reminders from a Notion database to Slack users. By running every weekday morning, it ensures that users receive timely reminders of their incomplete tasks, helping them stay organized and efficient."}, "typeVersion": 1}, {"id": "f4334588-60dd-456a-839f-6e5610ce18b8", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-400, 32.55329198368918], "parameters": {"color": 4, "width": 314.0627775112129, "height": 133.34123489274947, "content": "# EDIT THE FIELDS HERE 👇"}, "typeVersion": 1}, {"id": "50bd2206-7b97-454e-9b21-be6e8af7eb7d", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-671.0639503804273, 33.191851141281106], "parameters": {"color": 7, "width": 231.9017050322621, "height": 132.26101263924207, "content": "## 💡 Tip\n[<PERSON><PERSON><PERSON><PERSON> <PERSON>](https://crontab.guru/) is a simple and intuitive web-based tool that helps users create, edit, and understand cron schedules. "}, "typeVersion": 1}], "pinData": {}, "connections": {"Set Notion User Emails": {"main": [[{"node": "Set Slack User Full Name", "type": "main", "index": 0}]]}, "Set Slack User Full Name": {"main": [[{"node": "Get To Dos from Tasks Database", "type": "main", "index": 0}]]}, "Filter incomplete tasks only": {"main": [[{"node": "Get All Slack Users (Darryn)", "type": "main", "index": 0}]]}, "Get All Slack Users (Darryn)": {"main": [[{"node": "Switch for Notion Users Emails", "type": "main", "index": 0}]]}, "Filter Slack User 1 Full Name": {"main": [[{"node": "Send a Direct Message to User 1", "type": "main", "index": 0}]]}, "Filter Slack User 2 Full Name": {"main": [[{"node": "Send a Direct Message to User 2", "type": "main", "index": 0}]]}, "Get To Dos from Tasks Database": {"main": [[{"node": "Filter incomplete tasks only", "type": "main", "index": 0}]]}, "Switch for Notion Users Emails": {"main": [[{"node": "Filter Slack User 1 Full Name", "type": "main", "index": 0}], [{"node": "Filter Slack User 2 Full Name", "type": "main", "index": 0}]]}, "Schedule Mon - Friday @ 09:00am": {"main": [[{"node": "Set Notion User Emails", "type": "main", "index": 0}]]}}}