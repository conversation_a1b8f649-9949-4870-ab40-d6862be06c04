{"meta": {"instanceId": "1dd912a1610cd0376bae7bb8f1b5838d2b601f42ac66a48e012166bb954fed5a", "templateId": "2306"}, "nodes": [{"id": "1ef81384-b424-49bc-a6b5-922d1b0f5a7b", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [340, 240], "parameters": {}, "typeVersion": 1}, {"id": "3052f841-9e65-4284-a84d-3bb5d0c146ea", "name": "Write Result File to Disk", "type": "n8n-nodes-base.readWriteFile", "position": [1200, 240], "parameters": {"options": {}, "fileName": "document.pdf", "operation": "write", "dataPropertyName": "=data"}, "typeVersion": 1}, {"id": "852e30be-e145-4e73-b646-94e2ceec536c", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [720, 100], "parameters": {"width": 218, "height": 132, "content": "## Authentication\nConversion requests must be authenticated. Please create \n[ConvertAPI account to get authentication secret](https://www.convertapi.com/a/signin)"}, "typeVersion": 1}, {"id": "69f4d125-8990-4c98-9743-9f877325c958", "name": "Download PDF File", "type": "n8n-nodes-base.httpRequest", "position": [580, 240], "parameters": {"url": "https://cdn.convertapi.com/public/files/demo.pdf", "options": {"response": {"response": {"responseFormat": "file"}}}}, "typeVersion": 4.2}, {"id": "ff47b32c-37de-4f95-a0f0-37a7ea6f6bcd", "name": "Protect File with Password", "type": "n8n-nodes-base.httpRequest", "position": [780, 240], "parameters": {"url": "https://v2.convertapi.com/convert/pdf/to/protect", "method": "POST", "options": {"response": {"response": {"responseFormat": "file"}}}, "sendBody": true, "contentType": "multipart-form-data", "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "file", "parameterType": "formBinaryData", "inputDataFieldName": "=data"}, {"name": "UserPassword", "value": "mypassword"}]}, "genericAuthType": "httpQueryAuth", "headerParameters": {"parameters": [{"name": "Accept", "value": "application/octet-stream"}]}}, "credentials": {"httpQueryAuth": {"id": "WdAklDMod8fBEMRk", "name": "Query Auth account"}}, "notesInFlow": true, "typeVersion": 4.2}, {"id": "4b3f082d-ad08-4609-88b6-bf25ff660c09", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [720, 400], "parameters": {"width": 220, "height": 140, "content": "## Set Password\nSet the password in the parameter **UserPassword**"}, "typeVersion": 1}, {"id": "79d5896e-4d5b-4dd9-8fc2-466197b5d61f", "name": "Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [1180, 440], "parameters": {"name": "test-password.pdf", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "root", "cachedResultName": "/ (Root folder)"}}, "credentials": {"googleDriveOAuth2Api": {"id": "ylpqxmWWSllOKhVO", "name": "Google Drive account"}}, "typeVersion": 3}], "pinData": {}, "connections": {"Download PDF File": {"main": [[{"node": "Protect File with Password", "type": "main", "index": 0}]]}, "Protect File with Password": {"main": [[{"node": "Write Result File to Disk", "type": "main", "index": 0}, {"node": "Google Drive", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Download PDF File", "type": "main", "index": 0}]]}}}