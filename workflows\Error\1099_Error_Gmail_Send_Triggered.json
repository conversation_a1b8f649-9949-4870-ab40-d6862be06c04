{"nodes": [{"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.errorTrigger", "position": [450, 300], "parameters": {}, "typeVersion": 1}, {"name": "Gmail", "type": "n8n-nodes-base.gmail", "position": [650, 300], "parameters": {"toList": ["<EMAIL>"], "message": "=Workflow: {{$json[\"workflow\"][\"name\"]}}\nError: {{$json[\"execution\"][\"error\"][\"message\"]}}\nLast node executed: {{$json[\"execution\"][\"lastNodeExecuted\"]}}\nExecution URL: {{$json[\"execution\"][\"url\"]}}\nStacktrace:\n{{$json[\"execution\"][\"error\"][\"stack\"]}}", "subject": "=n8n Workflow Failure:  {{$json[\"workflow\"][\"name\"]}}", "resource": "message", "additionalFields": {}}, "credentials": {"gmailOAuth2": "TBD"}, "typeVersion": 1}], "connections": {"Error Trigger": {"main": [[{"node": "Gmail", "type": "main", "index": 0}]]}}}