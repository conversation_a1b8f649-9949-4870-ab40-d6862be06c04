{"id": "TBiW9x7O4ijo4yOX", "meta": {"instanceId": "255b605d49a6677a536746e05401de51bb4c62e65036d9acdb9908f6567f0361"}, "name": "Bitrix24 Open Chanel RAG Chatbot Application Workflow example with Webhook Integration", "tags": [{"id": "2ziILYLz4IbTkFf5", "name": "Tech demo", "createdAt": "2025-02-17T08:43:26.445Z", "updatedAt": "2025-02-17T08:43:26.445Z"}, {"id": "BedOB2iRpKR26bcZ", "name": "<PERSON><PERSON><PERSON>", "createdAt": "2025-02-17T08:43:26.436Z", "updatedAt": "2025-02-17T08:43:26.436Z"}, {"id": "DvSHJwHwuObn0cxx", "name": "Open Channels", "createdAt": "2025-03-04T07:27:28.499Z", "updatedAt": "2025-03-04T07:27:28.499Z"}, {"id": "YJcjKoBRFN1HXH5e", "name": "Bitrix24", "createdAt": "2025-02-17T08:43:26.424Z", "updatedAt": "2025-02-17T08:43:26.424Z"}], "nodes": [{"id": "dbd7b2c0-2b27-4c23-beb7-eec128da0787", "name": "Bitrix24 Handler", "type": "n8n-nodes-base.webhook", "position": [-1280, 620], "webhookId": "bde38660-2604-4e00-afc0-5ebceebb7f0a", "parameters": {"path": "bitrix24/openchannel-rag-bothandler.php", "options": {}, "httpMethod": "POST", "responseMode": "responseNode"}, "typeVersion": 1}, {"id": "0ead4d82-4d9b-4392-af4c-2c315068b983", "name": "Credentials", "type": "n8n-nodes-base.set", "position": [-1040, 620], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "030f8f90-2669-4c20-9eab-c572c4b7c70c", "name": "CLIENT_ID", "type": "string", "value": "local.67c8f9e81cb353.30162021"}, {"id": "de9bbb7a-b782-4540-b259-527625db8490", "name": "CLIENT_SECRET", "type": "string", "value": "Db5943DCy4JhYq4oU0yNb21Hx8WimQeThczOYk03uJrVroc8R4"}, {"id": "86b7aff7-1e25-4b12-a366-23cf34e5a405", "name": "application_token", "type": "string", "value": "={{ $json.body['auth[application_token]'] }}"}, {"id": "69bbcb1f-ba6e-42eb-be8a-ee0707ce997d", "name": "domain", "type": "string", "value": "={{ $json.body['auth[domain]'] }}\n"}, {"id": "dc1b0515-f06a-4731-b0dc-912a8d04e56b", "name": "access_token", "type": "string", "value": "={{ $json.body['auth[access_token]'] }}"}, {"id": "94fdeed8-9437-417e-9c2a-fa853620a340", "name": "storageName", "type": "string", "value": "Shared drive"}, {"id": "8564e421-dfce-437c-a7c3-ac6a180594b8", "name": "folderName", "type": "string", "value": "Open line chat bot documents"}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "67b225b8-c2c2-4570-81cb-4c533ae75465", "name": "Validate Token", "type": "n8n-nodes-base.if", "position": [-820, 620], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "or", "conditions": [{"id": "da73d0ba-6eeb-405e-89fe-9d041fd2e0cd", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.CLIENT_ID }}", "rightValue": "={{ $json.application_token }}"}, {"id": "4ba90f7b-0299-4097-9ae7-6e4dee428a74", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "1", "rightValue": "1"}]}}, "typeVersion": 2.2}, {"id": "4fee1441-4e30-4070-b596-15e121ca7320", "name": "Route Event", "type": "n8n-nodes-base.switch", "position": [-620, 520], "parameters": {"rules": {"values": [{"outputKey": "ONIMBOTMESSAGEADD", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.body.event }}", "rightValue": "ONIMBOTMESSAGEADD"}]}, "renameOutput": true}, {"outputKey": "ONIMBOTJOINCHAT", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "e9125f57-129e-4026-86ff-746d40b92b04", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.body.event }}", "rightValue": "ONIMBOTJOINCHAT"}]}, "renameOutput": true}, {"outputKey": "ONAPPINSTALL", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "2db7bed5-fd88-4900-b8d2-e27b49c2fcca", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.body.event }}", "rightValue": "ONAPPINSTALL"}]}, "renameOutput": true}, {"outputKey": "ONIMBOTDELETE", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "b708d339-fd46-470d-b0d5-ff2eb405f5ce", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.body.event }}", "rightValue": "ONIMBOTDELETE"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "c21f6d64-0543-4958-9f64-501dce37893f", "name": "Process Message", "type": "n8n-nodes-base.function", "position": [-420, 400], "parameters": {"functionCode": "// Process Message Node\nconst items = $input.all();\nconst item = items[0];\n\n// Get message data from the correct path\nconst message = item.json.body['data[PARAMS][MESSAGE]'];\nconst dialogId = item.json.body['data[PARAMS][DIALOG_ID]'];\n\nconst sessionId = item.json.body['data[PARAMS][CHAT_ENTITY_DATA_1]'].split(\"|\")[5];\n\nconst botId = Object.keys(item.json.body)\n  .filter(key => key.startsWith(\"data[BOT]\") && key.endsWith(\"[BOT_ID]\"))\n  .map(key => $json.body[key])\n  .shift() || null;\nconst userId = item.json.body['data[USER][ID]'];\n\n// Get auth data\nconst auth = {\n  access_token: item.json.access_token,\n  domain: item.json.domain\n};\n\nif (message.toLowerCase() === \"what's hot\") {\n  return {\n    json: {\n      DIALOG_ID: dialogId,\n      SESSION_ID: sessionId,\n      BOT_ID: botId,\n      USER_ID: userId,\n      MESSAGE_ORI: message,\n      MESSAGE: \"Hi! I am an example-bot.\\nI repeat what you say\",\n      AUTH: auth.access_token,\n      DOMAIN: auth.domain\n    }\n  };\n} else {\n  return {\n    json: {\n      DIALOG_ID: dialogId,\n      SESSION_ID: sessionId,\n      BOT_ID: botId,\n      USER_ID: userId,\n      MESSAGE_ORI: message,\n      MESSAGE: `You said:\\n${message}`,\n      AUTH: auth.access_token,\n      DOMAIN: auth.domain\n    }\n  };\n}"}, "typeVersion": 1}, {"id": "06a59835-2999-44b6-81bd-0601e5570113", "name": "Process Join", "type": "n8n-nodes-base.function", "position": [-420, 780], "parameters": {"functionCode": "// Process Join Node\nconst items = $input.all();\nconst item = items[0];\n\n// Get dialog ID from the correct path\nconst dialogId = item.json.body['data[PARAMS][DIALOG_ID]'];\n\n// Get auth data\nconst auth = {\n  access_token: item.json.access_token,\n  domain: item.json.domain\n};\n\nconst message = \n  'ITR Menu:\\n' +\n  '[send=1]1. find out more about me[/send]\\n' +\n  '[send=0]0. wait for operator response[/send]';\n\nreturn {\n  json: {\n    DIALOG_ID: dialogId,\n    MESSAGE: message,\n    AUTH: auth.access_token,\n    DOMAIN: auth.domain\n  }\n};"}, "typeVersion": 1}, {"id": "40accf33-c217-497c-8172-6106eb15800f", "name": "Process Install", "type": "n8n-nodes-base.function", "position": [-420, 940], "parameters": {"functionCode": "// Process Install Node\nconst items = $input.all();\nconst item = items[0];\n\n// Get the webhook URL from input\nconst handlerBackUrl = item.json.webhookUrl;\n\n// Get auth data directly from item.json\nconst auth = {\n  access_token: item.json.access_token,\n  application_token: item.json.application_token,\n  domain: item.json.domain\n};\n\nreturn {\n  json: {\n    handler_back_url: handlerBackUrl,\n    CODE: 'OpenChanelExampleBot',\n    TYPE: 'O',\n    OPENLINE: 'Y',\n    EVENT_MESSAGE_ADD: handlerBackUrl,\n    EVENT_WELCOME_MESSAGE: handlerBackUrl,\n    EVENT_BOT_DELETE: handlerBackUrl,\n    PROPERTIES: {\n      NAME: 'Open chanel Bot',\n      LAST_NAME: 'Example',\n      COLOR: 'AQUA',\n      EMAIL: '<EMAIL>',\n      PERSONAL_BIRTHDAY: '2020-07-18',\n      WORK_POSITION: 'Report on affairs',\n      PERSONAL_GENDER: 'M'\n    },\n    // Use the auth data from item.json\n    AUTH: auth.access_token,\n    CLIENT_ID: auth.application_token,\n    DOMAIN: auth.domain\n  }\n};"}, "typeVersion": 1}, {"id": "22a7c363-ab5a-4adc-9de4-268f3f3739f3", "name": "Register Bot", "type": "n8n-nodes-base.httpRequest", "position": [-220, 940], "parameters": {"url": "=https://{{ $json.DOMAIN }}/rest/imbot.register?auth={{$json.AUTH}}", "method": "POST", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "CODE", "value": "OpenChanelExampleBot"}, {"name": "TYPE", "value": "O"}, {"name": "EVENT_MESSAGE_ADD", "value": "={{$json.handler_back_url}}"}, {"name": "EVENT_WELCOME_MESSAGE", "value": "={{$json.handler_back_url}}"}, {"name": "EVENT_BOT_DELETE", "value": "={{$json.handler_back_url}}"}, {"name": "PROPERTIES", "value": "={{ {\n  NAME: 'Bot',\n  LAST_NAME: 'Example',\n  COLOR: 'AQUA',\n  EMAIL: '<EMAIL>',\n  PERSONAL_BIRTHDAY: '2020-07-18',\n  WORK_POSITION: 'Report on affairs',\n  PERSONAL_GENDER: 'M'\n} }}"}, {"name": "CLIENT_ID", "value": "={{ $json.CLIENT_ID }}"}, {"name": "CLIENT_SECRET", "value": "={{ $json.AUTH }}"}, {"name": "OPENLINE", "value": "Y"}]}}, "typeVersion": 4.2}, {"id": "e728c7eb-b169-4757-b4f4-b99ec4db0184", "name": "Send Message", "type": "n8n-nodes-base.httpRequest", "position": [740, 420], "parameters": {"url": "=https://{{ $json.data.DOMAIN }}/rest/imbot.message.add?auth={{ $json.data.AUTH }}", "method": "POST", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "DIALOG_ID", "value": "={{ $json.data.DIALOG_ID }}"}, {"name": "MESSAGE", "value": "={{ $json.data.MESSAGE }}"}, {"name": "AUTH", "value": "={{ $json.data.AUTH }}"}]}}, "typeVersion": 4.2}, {"id": "1a1fae0e-d74f-48b9-8ec8-4e926763de28", "name": "Send Join Message", "type": "n8n-nodes-base.httpRequest", "position": [-220, 780], "parameters": {"url": "=https://{{$json.DOMAIN}}/rest/imbot.message.add?auth={{$json.AUTH}}", "method": "POST", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "DIALOG_ID", "value": "={{ $json.DIALOG_ID }}"}, {"name": "MESSAGE", "value": "={{ $json.MESSAGE }}"}, {"name": "AUTH", "value": "={{ $json.AUTH }}"}]}}, "typeVersion": 4.2}, {"id": "f377d8eb-2a90-4ca5-8bd8-122c8df2ced3", "name": "Process Delete", "type": "n8n-nodes-base.noOp", "position": [-420, 1100], "parameters": {}, "typeVersion": 1}, {"id": "faa4c61e-faf4-4bd7-b096-706d3c5cf366", "name": "Success Response", "type": "n8n-nodes-base.respondToWebhook", "position": [1200, 700], "parameters": {"options": {"responseCode": 200}, "respondWith": "json", "responseBody": "={\n  \"result\": true\n}"}, "typeVersion": 1.1}, {"id": "ade154b4-64d9-4ecd-8a83-328002c98569", "name": "Error Response", "type": "n8n-nodes-base.respondToWebhook", "position": [-820, 780], "parameters": {"options": {"responseCode": 401}, "respondWith": "json", "responseBody": "={{\n  \"result\": false,\n  \"error\": \"Invalid application token\"\n}}"}, "typeVersion": 1.1}, {"id": "a5866396-3a25-4bbf-81b2-56a8d35fc63b", "name": "Merge parameters for Subworkflow", "type": "n8n-nodes-base.merge", "position": [-180, 1340], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineAll"}, "typeVersion": 3}, {"id": "feb3e1c0-4556-4d77-9e5e-42eb2c10a5f8", "name": "Get a list of available storages", "type": "n8n-nodes-base.httpRequest", "position": [-860, 2080], "parameters": {"url": "=https://{{ $json.domain }}/rest/disk.storage.getlist.json?auth={{ $json.access_token }}", "method": "POST", "options": {}, "jsonBody": "={\n\"filter\": {\n\t\t\t\t\"ENTITY_TYPE\": \"common\",\n\t\t\t\t\"%NAME\": \"{{ $json.storageName }}\"\n  }\n}\n", "sendBody": true, "specifyBody": "json"}, "typeVersion": 4.2}, {"id": "7bb0412e-57ab-4a63-aec2-5db64b83ef7e", "name": "Get a list of List of Files and Folders", "type": "n8n-nodes-base.httpRequest", "position": [-640, 2080], "parameters": {"url": "=https://{{ $('Execute Workflow Trigger').item.json.domain }}/rest/disk.storage.getchildren.json?auth={{ $('Execute Workflow Trigger').item.json.access_token }}", "method": "POST", "options": {}, "jsonBody": "={\n\"id\": {{ $json.result[0].ID }},\n\"filter\": {\n\t\t\t\t\"TYPE\": \"folder\",\n\t\t\t\t\"%NAME\": \"{{ $('Execute Workflow Trigger').item.json.folderName }}\"\n  }\n}\n", "sendBody": true, "specifyBody": "json"}, "typeVersion": 4.2}, {"id": "5528a3be-375a-4077-a346-2eb77cf9160f", "name": "Get a list of Folders files", "type": "n8n-nodes-base.httpRequest", "position": [-420, 2080], "parameters": {"url": "=https://{{ $('Execute Workflow Trigger').item.json.domain }}/rest/disk.folder.getchildren.json?auth={{ $('Execute Workflow Trigger').item.json.access_token }}", "method": "POST", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "id", "value": "={{ $json.result[0].ID }}"}]}}, "typeVersion": 4.2}, {"id": "f713eda1-f51e-484d-a98a-f359bc7ce654", "name": "Download file", "type": "n8n-nodes-base.httpRequest", "position": [280, 2080], "parameters": {"url": "={{ $json.DOWNLOAD_URL }}", "options": {}}, "typeVersion": 4.2}, {"id": "3b2c67ea-bb6c-49c8-b55f-ffde7d1d8e83", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [700, 2280], "parameters": {"loader": "pdf<PERSON><PERSON>der", "options": {"splitPages": true}, "dataType": "binary"}, "typeVersion": 1}, {"id": "0b5c2963-3692-469d-a1ce-66fe598dc25f", "name": "Recursive Character Text Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "position": [840, 2460], "parameters": {"options": {}, "chunkOverlap": 100}, "typeVersion": 1}, {"id": "52bfdf6b-0fb1-4e85-b31d-ec6c9ef912d8", "name": "Split Out folder files and folders", "type": "n8n-nodes-base.splitOut", "position": [-180, 2080], "parameters": {"options": {}, "fieldToSplitOut": "result"}, "typeVersion": 1}, {"id": "fd53fca7-d11d-4254-be56-9a62b6f0fadf", "name": "Filter for files", "type": "n8n-nodes-base.filter", "position": [40, 2080], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "6e68a8be-c155-41c7-ace4-bf76bfd362fc", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.TYPE }}", "rightValue": "file"}]}}, "typeVersion": 2.2}, {"id": "748a65d3-dceb-4787-a068-b364371b392b", "name": "Move files to Vector stored folder", "type": "n8n-nodes-base.httpRequest", "position": [520, 1860], "parameters": {"url": "=https://{{ $('Execute Workflow Trigger').item.json.domain }}/rest/disk.file.moveto.json?auth={{ $('Execute Workflow Trigger').item.json.access_token }}", "method": "POST", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "id", "value": "={{ $json.ID }}"}, {"name": "targetFolderId", "value": "={{ $('Get a list of Folders files').item.json.result[0].ID }}"}]}}, "executeOnce": false, "typeVersion": 4.2}, {"id": "47f3aec5-c3cb-4d3e-97bc-8b708ccc0db5", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-1080, 2080], "parameters": {}, "typeVersion": 1}, {"id": "9d3eb788-96cf-4c01-af5f-2beb1f6fa7b8", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1160, 1780], "parameters": {"width": 2168.7691983135305, "height": 818.1434255918864, "content": "Subworkflow for Register Bot\nHere are files vector stored for Open line chanel bot\nAfter files are stored they are moved to subfolder"}, "typeVersion": 1}, {"id": "91c26264-a61d-426c-a044-e2b287e54de0", "name": "Qdrant Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "position": [580, 2080], "parameters": {"mode": "insert", "options": {"collectionConfig": ""}, "qdrantCollection": {"__rl": true, "mode": "list", "value": "bitrix-docs", "cachedResultName": "bitrix-docs"}}, "typeVersion": 1}, {"id": "0dd69952-402e-4d9e-a44f-7cf96ab4055e", "name": "Embeddings <PERSON><PERSON><PERSON>", "type": "@n8n/n8n-nodes-langchain.embeddingsOllama", "position": [500, 2280], "parameters": {"model": "nomic-embed-text:latest"}, "typeVersion": 1}, {"id": "fcfbfe53-da04-48f2-9a62-204a8f1f06a8", "name": "Vector Store Retriever", "type": "@n8n/n8n-nodes-langchain.retrieverVectorStore", "position": [140, 240], "parameters": {"topK": 10}, "typeVersion": 1}, {"id": "22d3be40-b74a-453f-8f52-8974b1527d49", "name": "Question and Answer Chain", "type": "@n8n/n8n-nodes-langchain.chainRetrievalQa", "position": [0, 0], "parameters": {"text": "={{ $json.MESSAGE_ORI }}", "options": {"systemPromptTemplate": "=Use the following pieces of context to answer the user's question.\nIf you don't know the answer, just say that you don't know, don't try to make up an answer.\n\n----------------\n{context}\n\nYour response must contain **only** the following key-value pairs:\n- `\"DIALOG_ID\"`: **Use exactly** this value from the input: `{{ $json.DIALOG_ID }}`\n- `\"AUTH\"`: **Use exactly** this value from the input: `{{ $json.AUTH }}`\n- `\"DOMAIN\"`: **Use exactly** this value from the input: `{{ $json.DOMAIN }}`\n- `\"MESSAGE\"`: **Your AI-generated response**, based on the conversation history and the user's input.\n\n**Do not modify** the values of `\"DIALOG_ID\"`, `\"AUTH\"`, or `\"DOMAIN\"`. They must remain exactly as received from the input.  \nThe `\"MESSAGE\"` field must contain a relevant and clear response.\n\nIf the user asks **\"find out more about me\"**, respond with:  \n*\"I am a Retrieval-Augmented Generation (RAG) system that answers questions based on uploaded documents and provided context.\"*"}, "promptType": "define"}, "typeVersion": 1.4}, {"id": "fb19f0b5-72cb-4ccd-ac0e-75ea3a32c9cc", "name": "Prepare output parameters", "type": "n8n-nodes-base.set", "position": [440, 60], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "ef09b5f8-2111-4731-8317-e338885a10c3", "name": "data", "type": "object", "value": "={{ $json.response.text.removeMarkdown().replace(/`+$/, '')}}"}]}}, "typeVersion": 3.4}, {"id": "52429ac1-d738-49d7-81a8-725df6587312", "name": "Embeddings Ollama1", "type": "@n8n/n8n-nodes-langchain.embeddingsOllama", "position": [360, 600], "parameters": {"model": "nomic-embed-text:latest"}, "typeVersion": 1}, {"id": "bade110b-d252-4633-9ecf-44a42772b8d5", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [-60, 300], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash"}, "typeVersion": 1}, {"id": "41eb5179-6195-485b-848b-46bb997de38e", "name": "Qdrant Vector Store1", "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "position": [160, 420], "parameters": {"options": {}, "qdrantCollection": {"__rl": true, "mode": "list", "value": "bitrix-docs", "cachedResultName": "bitrix-docs"}}, "typeVersion": 1}, {"id": "484cbc38-4c6f-4d3d-9409-b04df2c7e102", "name": "Execute subworkflow", "type": "n8n-nodes-base.executeWorkflow", "position": [200, 1340], "parameters": {"options": {}, "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}}, "typeVersion": 1.1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "e3e24337-997c-4ce2-b8c1-3e6f8b9eb85c", "connections": {"Credentials": {"main": [[{"node": "Validate Token", "type": "main", "index": 0}, {"node": "Merge parameters for Subworkflow", "type": "main", "index": 1}]]}, "Route Event": {"main": [[{"node": "Process Message", "type": "main", "index": 0}], [{"node": "Process Join", "type": "main", "index": 0}], [{"node": "Process Install", "type": "main", "index": 0}], [{"node": "Process Delete", "type": "main", "index": 0}]]}, "Process Join": {"main": [[{"node": "Send Join Message", "type": "main", "index": 0}]]}, "Register Bot": {"main": [[{"node": "Success Response", "type": "main", "index": 0}, {"node": "Merge parameters for Subworkflow", "type": "main", "index": 0}]]}, "Send Message": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}, "Download file": {"main": [[{"node": "Move files to Vector stored folder", "type": "main", "index": 0}, {"node": "Qdrant Vector Store", "type": "main", "index": 0}]]}, "Process Delete": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}, "Validate Token": {"main": [[{"node": "Route Event", "type": "main", "index": 0}], [{"node": "Error Response", "type": "main", "index": 0}]]}, "Process Install": {"main": [[{"node": "Register Bot", "type": "main", "index": 0}]]}, "Process Message": {"main": [[{"node": "Question and Answer Chain", "type": "main", "index": 0}]]}, "Bitrix24 Handler": {"main": [[{"node": "Credentials", "type": "main", "index": 0}]]}, "Filter for files": {"main": [[{"node": "Download file", "type": "main", "index": 0}]]}, "Embeddings Ollama": {"ai_embedding": [[{"node": "Qdrant Vector Store", "type": "ai_embedding", "index": 0}]]}, "Send Join Message": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}, "Embeddings Ollama1": {"ai_embedding": [[{"node": "Qdrant Vector Store1", "type": "ai_embedding", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Qdrant Vector Store", "type": "ai_document", "index": 0}]]}, "Execute subworkflow": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}, "Qdrant Vector Store1": {"ai_vectorStore": [[{"node": "Vector Store Retriever", "type": "ai_vectorStore", "index": 0}]]}, "Vector Store Retriever": {"ai_retriever": [[{"node": "Question and Answer Chain", "type": "ai_retriever", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "Get a list of available storages", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Question and Answer Chain", "type": "ai_languageModel", "index": 0}]]}, "Prepare output parameters": {"main": [[{"node": "Send Message", "type": "main", "index": 0}]]}, "Question and Answer Chain": {"main": [[{"node": "Prepare output parameters", "type": "main", "index": 0}]]}, "Get a list of Folders files": {"main": [[{"node": "Split Out folder files and folders", "type": "main", "index": 0}]]}, "Get a list of available storages": {"main": [[{"node": "Get a list of List of Files and Folders", "type": "main", "index": 0}]]}, "Merge parameters for Subworkflow": {"main": [[{"node": "Execute subworkflow", "type": "main", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Split Out folder files and folders": {"main": [[{"node": "Filter for files", "type": "main", "index": 0}]]}, "Get a list of List of Files and Folders": {"main": [[{"node": "Get a list of Folders files", "type": "main", "index": 0}]]}}}