{"id": "160", "name": "Analyze a URL and get the job details using the Cortex node", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [370, 220], "parameters": {}, "typeVersion": 1}, {"name": "<PERSON>rtex", "type": "n8n-nodes-base.cortex", "position": [570, 220], "parameters": {"analyzer": "f4abc1b633b80f45af165970793fd4fd::Abuse_Finder_3_0", "observableType": "url", "observableValue": "https://n8n.io", "additionalFields": {}}, "credentials": {"cortexApi": "cortex"}, "typeVersion": 1}, {"name": "Cortex1", "type": "n8n-nodes-base.cortex", "position": [770, 220], "parameters": {"jobId": "={{$node[\"Cortex\"].json[\"_id\"]}}", "resource": "job"}, "credentials": {"cortexApi": "cortex"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Cortex": {"main": [[{"node": "Cortex1", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "<PERSON>rtex", "type": "main", "index": 0}]]}}}