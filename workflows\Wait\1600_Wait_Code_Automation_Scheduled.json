{"id": "8EmNhftXznAGV3dR", "meta": {"instanceId": "03e9d14e9196363fe7191ce21dc0bb17387a6e755dcc9acc4f5904752919dca8"}, "name": "Phishing_analysis__URLScan_io_and_Virustotal_", "tags": [{"id": "GCHVocImoXoEVnzP", "name": "🛠️ In progress", "createdAt": "2023-10-31T02:17:21.618Z", "updatedAt": "2023-10-31T02:17:21.618Z"}, {"id": "QPJKatvLSxxtrE8U", "name": "Secops", "createdAt": "2023-10-31T02:15:11.396Z", "updatedAt": "2023-10-31T02:15:11.396Z"}], "nodes": [{"id": "f170068a-4540-4fbd-bd17-00a6367989d1", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [-1760, 560], "parameters": {}, "typeVersion": 1}, {"id": "5a1e0490-6971-4490-a806-46da5e226365", "name": "sends slack message", "type": "n8n-nodes-base.slack", "position": [-360, 1280], "parameters": {"text": "=*Email Analysis*\n\nSubject: {{ $('Microsoft Outlook').item.json[\"subject\"] }}\nFrom: {{ $('Microsoft Outlook').item.json[\"sender\"][\"emailAddress\"][\"address\"] }}\nDate: {{ $('Microsoft Outlook').item.json[\"sentDateTime\"] }}\n\nReport:\n\n*URLScan Report URL:* {{ $('urlscan.io').item.json.result ? $('urlscan.io').item.json.result : \"N/A\" }}\n*Virustotal report:* https://www.virustotal.com/gui/url/{{ $json[\"data\"][\"id\"].split(\"-\")[1] }}\n*Virustotal Verdict:* {{ $json.data.attributes.stats.malicious + $json.data.attributes.stats.suspicious }} / {{ Object.keys($json.data.attributes.results).length }}", "select": "channel", "channelId": {"__rl": true, "mode": "name", "value": "test-giulio-public"}, "otherOptions": {}}, "credentials": {"slackApi": {"id": "252", "name": "Slack Bot Token | <PERSON><PERSON><PERSON> [✅ Share ok]"}}, "typeVersion": 2}, {"id": "65e70f8a-7514-455e-97bf-b11514b4eec2", "name": "Split In Batches", "type": "n8n-nodes-base.splitInBatches", "position": [-1020, 480], "parameters": {"options": {}, "batchSize": 1}, "typeVersion": 2}, {"id": "35084825-f3b2-4a01-b953-712c099a6451", "name": "<PERSON> as read", "type": "n8n-nodes-base.microsoftOutlook", "position": [-1300, 560], "parameters": {"messageId": "={{ $json.id }}", "operation": "update", "updateFields": {"isRead": true}}, "credentials": {"microsoftOutlookOAuth2Api": {"id": "Zeu3LbjDbkwiCUik", "name": "Microsoft Outlook | Giulio [✅ Share ok]"}}, "typeVersion": 1}, {"id": "62098c94-5735-4eed-a712-3f9887e0400f", "name": "VirusTotal: Scan U<PERSON>", "type": "n8n-nodes-base.httpRequest", "position": [-220, 700], "parameters": {"url": "https://www.virustotal.com/api/v3/urls", "method": "POST", "options": {}, "sendQuery": true, "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "url", "value": "={{ $json.domain }}"}]}, "nodeCredentialType": "virusTotalApi"}, "credentials": {"virusTotalApi": {"id": "hVTFFXxLV4oWPOb0", "name": "Virus Total | <PERSON><PERSON><PERSON> [✅ Share ok]"}}, "typeVersion": 4.1}, {"id": "55b7ce97-3609-4a16-b998-8bec77cffb59", "name": "VirusTotal: Get report", "type": "n8n-nodes-base.httpRequest", "position": [200, 700], "parameters": {"url": "=https://www.virustotal.com/api/v3/analyses/{{ $json.data.id }}", "options": {}, "sendQuery": true, "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "resource", "value": "https://developers.virustotal.com/v2.0/reference/url-report"}]}, "nodeCredentialType": "virusTotalApi"}, "credentials": {"virusTotalApi": {"id": "hVTFFXxLV4oWPOb0", "name": "Virus Total | <PERSON><PERSON><PERSON> [✅ Share ok]"}}, "typeVersion": 4.1}, {"id": "7bf3c7a0-94f9-410b-b0fe-e0d15d0ba895", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-1760, 380], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.1}, {"id": "741f3221-bb73-4004-801e-e9c3030740f8", "name": "Find indicators of compromise", "type": "n8n-nodes-base.code", "position": [-780, 440], "parameters": {"language": "python", "pythonCode": "try:\n  from ioc_finder import find_iocs\nexcept ImportError:\n  import micropip\n  await micropip.install(\"ioc-finder\")\n  from ioc_finder import find_iocs\n\ntext = _input.first().json['body']['content']\nprint(text)\n\niocs = find_iocs(text)\n\nreturn [{\"json\": { \"domain\": item }} for item in iocs[\"urls\"]]"}, "typeVersion": 2, "alwaysOutputData": true}, {"id": "bf8ba285-e824-4104-82e0-fa2dba80f301", "name": "URLScan: Get report", "type": "n8n-nodes-base.urlScanIo", "position": [640, 60], "parameters": {"scanId": "={{ $json.scanId }}", "operation": "get"}, "credentials": {"urlScanIoApi": {"id": "eva7ViJyyrpmJDe3", "name": "urlscan.io | <PERSON><PERSON><PERSON> [✅ Share ok]"}}, "typeVersion": 1}, {"id": "eb3b06e8-ffe3-4472-a70c-08fb2555e0fb", "name": "URLScan: <PERSON><PERSON>", "type": "n8n-nodes-base.urlScanIo", "position": [-100, 120], "parameters": {"url": "={{ $json.domain }}", "additionalFields": {}}, "credentials": {"urlScanIoApi": {"id": "eva7ViJyyrpmJDe3", "name": "urlscan.io | <PERSON><PERSON><PERSON> [✅ Share ok]"}}, "typeVersion": 1, "continueOnFail": true}, {"id": "34157694-635a-481b-b7d2-dcd4628b26fe", "name": "Has URL?", "type": "n8n-nodes-base.if", "position": [-520, 440], "parameters": {"conditions": {"string": [{"value1": "={{ $json.domain }}", "operation": "isNotEmpty"}]}}, "typeVersion": 1}, {"id": "33cad369-0598-433e-90f8-0e7333ec5e92", "name": "No error?", "type": "n8n-nodes-base.if", "position": [240, 120], "parameters": {"conditions": {"string": [{"value1": "={{ $json.error }}", "operation": "isNotEmpty"}]}}, "typeVersion": 1}, {"id": "cba20d52-a56c-4ac0-99f2-d9b54adb342e", "name": "Not empty?", "type": "n8n-nodes-base.filter", "position": [-640, 1280], "parameters": {"conditions": {"string": [{"value1": "={{ $json.data }}", "operation": "isNotEmpty"}]}}, "typeVersion": 1}, {"id": "449c31e3-e098-43ec-a31b-1e383c6add57", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-2051.228008430503, -251.94391274976795], "parameters": {"width": 474.5187061049208, "height": 1008.8561536646063, "content": "![Scheduled](https://i.imgur.com/PcOuvAL.png)\n## Workflow Overview\n\nThis n8n workflow is engineered to enhance cybersecurity measures by analyzing potential phishing URLs using URLScan.io and VirusTotal. \n\nIt is designed to automatically process and evaluate URLs from incoming messages for malicious content.\n\nThis workflow is tuned specifically for `Outlook`, but you can replace outlook with your mail provider of choice. \n\nThe workflow can be initiated manually or scheduled to run automatically, ensuring consistent checks against phishing threats. By integrating with leading cybersecurity tools, it provides a comprehensive analysis, strengthening your organization's defense against phishing attacks.\n\n## Execution Schedule\n\nIt can be triggered at will by clicking \"Execute Workflow\" or set to run on a schedule. To align with your operational needs, customize the `Schedule Trigger` to your preferred frequency, ensuring continuous monitoring for phishing attempts."}, "typeVersion": 1}, {"id": "a8921212-aec4-422d-9f04-f402d7591475", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-1560, 107], "parameters": {"width": 397.3953488372091, "height": 647.1076277970203, "content": "![Outlook](https://i.imgur.com/R3Bhd8I.png)\n## Email Processing for Phishing Analysis\nThis segment of the workflow interfaces with Microsoft Outlook to retrieve and process `all messages marked as unread`. This section can be replaced with any mail provider.\n\nOnce an email is fetched, the `Get all unread messages` node captures the details, while the `Mark as read` node updates the message's status. \n\nThis ensures that each email is only processed once, maintaining a clean and organized inbox, and preventing reprocessing of the same messages."}, "typeVersion": 1}, {"id": "fbad734e-4502-4d1b-8890-b05c486a1f70", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-1140, 15.062288067451163], "parameters": {"width": 859.9418604651164, "height": 836.8098049558043, "content": "![python](https://i.imgur.com/S2TZ3u6.png)\n## Indicator of Compromise Detection Loop\nThis workflow section leverages n8n's `Split In Batches` node, a powerful feature for iterative processing. It is set to dissect the batch of emails one by one, allowing for individual examination of each message's content for potential threats.\n\nWith the `Find indicators of compromise` node, the workflow employs Python code to parse the email content and extract URLs, which are common indicators of compromise (IoCs) in phishing attempts. By utilizing the ioc-finder library, it systematically scans for and isolates these IoCs from the email body.\n\nThe `Has URL?` node then checks if the email contains any urls. If no URLs are found, then the loop moves on to the next email, as there is nothing to scan. If it does find one, it allows the email to flow to the next sections. \n\nThe splitting of batches is key to the workflow's efficiency, enabling the loop to handle vast quantities of emails methodically. This step is crucial in pinpointing and extracting suspicious elements from each email, highlighting the workflow's meticulous approach to security analysis."}, "typeVersion": 1}, {"id": "8603fe5b-ad6b-4980-a28b-01531c6629f3", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-260, -313.5039999999999], "parameters": {"width": 1099.116279069767, "height": 618.8295813953489, "content": "![urlscan](https://i.imgur.com/RjaMt6c.png)\n## URL Scanning and Verification\nThis portion of the workflow engages with URLScan.io, a tool for scanning and analyzing websites for potential security threats.\n\nThe `URLScan: Scan URL` node begins the process by submitting the URL extracted from the email content. It's configured to continue even if an error occurs, which allows us to then do an error check in the `No error?` node instead. \n\nThis is because if the `URLScan: Scan URL` node fails, the whole workflow will grind to a stop. This is not good because in theory, we maybe processing another email after this one, and we need to ensure the workflow moves on to the next email. \n\nFollowing the submission, the `Wait 1 Minute` node pauses the workflow, giving URLScan.io adequate time to perform the scan and generate a report. This wait ensures that the subsequent retrieval of the report reflects the most recent and comprehensive analysis."}, "typeVersion": 1}, {"id": "33299274-9f02-4ea0-af60-5dee53db2c34", "name": "Wait 1 Minute", "type": "n8n-nodes-base.wait", "position": [480, 60], "webhookId": "469a8b5e-8b5a-4360-bc9d-3b253cc0ae24", "parameters": {"unit": "seconds", "amount": 60}, "typeVersion": 1}, {"id": "757ad81d-ae24-4b26-98ba-a571670be2a3", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-260, 318.64011851851865], "parameters": {"width": 1435.7278194659766, "height": 540.6919228251508, "content": ""}, "typeVersion": 1}, {"id": "8e2cbf69-6c9e-4a98-ba5e-29b93eb2742f", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-680, 880], "parameters": {"width": 1213.8313506082789, "height": 575.5779026440933, "content": "![Slack](https://i.imgur.com/iKyMV0N.png)\n## Final Reporting on Phishing Analysis\nIn the concluding phase of the workflow, we consolidate the analysis into actionable intelligence and report through Slack.\n\nThe `Not empty?` node filters the data, ensuring that only URLs with a completed analysis proceed to the reporting stage. This step is crucial to avoid alerting on incomplete data, which could lead to misinformed decisions.\n\nThe `sends slack message` node is the final touchpoint of the workflow, where it compiles a detailed report and posts it on Slack. The message includes the `subject, sender, and date` of the analyzed email, along with the URLScan and VirusTotal reports. It provides a concise verdict by tallying the number of malicious and suspicious flags against the total checks performed, offering a clear indication of the potential threat level.\n\nThis Slack notification serves as a prompt for the cybersecurity team to take appropriate action, completing the workflow's aim of providing streamlined, accurate, and timely phishing threat analysis."}, "typeVersion": 1}, {"id": "a2a0dc81-b1f0-4d7b-b818-71bae58512a9", "name": "Get all unread messages", "type": "n8n-nodes-base.microsoftOutlook", "position": [-1520, 560], "parameters": {"operation": "getAll", "additionalFields": {"filter": "isRead eq false"}}, "credentials": {"microsoftOutlookOAuth2Api": {"id": "Zeu3LbjDbkwiCUik", "name": "Microsoft Outlook | Giulio [✅ Share ok]"}}, "typeVersion": 1}, {"id": "a5793014-9575-4e05-b467-f295a09f0945", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-260, 320], "parameters": {"width": 615.527819465977, "height": 540.6919228251508, "content": "![VirusTotal](https://upload.wikimedia.org/wikipedia/commons/thumb/b/b7/VirusTotal_logo.svg/320px-VirusTotal_logo.svg.png)\n## Phishing URL Analysis with VirusTotal\nThis segment of the workflow deploys VirusTotal's capabilities to scrutinize URLs for signs of phishing.\n\nThe `VirusTotal: Scan URL` node initiates the process by sending the URL to VirusTotal for analysis. Once the scan is triggered, the workflow moves on to the `VirusTotal: Get report` node, which retrieves the detailed analysis report after a certain interval, ensuring that the data received includes all findings from the scan.\n\nFinally, the `Merge Reports` node combines the results from both URLScan.io and VirusTotal, aligning the data side by side for a comprehensive view. This merging by position is vital as it correlates the analysis from different sources, providing a layered security assessment of the URL in question."}, "typeVersion": 1}, {"id": "c8d5c248-77ba-4a7f-ab21-19ff8d60ed55", "name": "Merge Reports", "type": "n8n-nodes-base.merge", "position": [1040, 680], "parameters": {"mode": "combine", "options": {}, "combinationMode": "mergeByPosition"}, "typeVersion": 2.1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "02ba918c-5fee-4d3e-824f-1160881716b6", "connections": {"Has URL?": {"main": [[{"node": "URLScan: <PERSON><PERSON>", "type": "main", "index": 0}, {"node": "VirusTotal: Scan U<PERSON>", "type": "main", "index": 0}], [{"node": "Split In Batches", "type": "main", "index": 0}]]}, "No error?": {"main": [[{"node": "Merge Reports", "type": "main", "index": 0}], [{"node": "Wait 1 Minute", "type": "main", "index": 0}]]}, "Not empty?": {"main": [[{"node": "sends slack message", "type": "main", "index": 0}]]}, "Mark as read": {"main": [[{"node": "Split In Batches", "type": "main", "index": 0}]]}, "Merge Reports": {"main": [[{"node": "Split In Batches", "type": "main", "index": 0}]]}, "Wait 1 Minute": {"main": [[{"node": "URLScan: Get report", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Get all unread messages", "type": "main", "index": 0}]]}, "Split In Batches": {"main": [[{"node": "Find indicators of compromise", "type": "main", "index": 0}], [{"node": "Not empty?", "type": "main", "index": 0}]]}, "URLScan: Scan URL": {"main": [[{"node": "No error?", "type": "main", "index": 0}]]}, "URLScan: Get report": {"main": [[{"node": "Merge Reports", "type": "main", "index": 0}]]}, "VirusTotal: Scan URL": {"main": [[{"node": "VirusTotal: Get report", "type": "main", "index": 0}]]}, "VirusTotal: Get report": {"main": [[{"node": "Merge Reports", "type": "main", "index": 1}]]}, "Get all unread messages": {"main": [[{"node": "<PERSON> as read", "type": "main", "index": 0}]]}, "Find indicators of compromise": {"main": [[{"node": "Has URL?", "type": "main", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "Get all unread messages", "type": "main", "index": 0}]]}}}