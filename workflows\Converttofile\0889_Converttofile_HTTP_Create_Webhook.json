{"meta": {"instanceId": "05da4424857d12101f50fff429f8deac0b96048b0ed4cdf3b1b3691af23f7345"}, "nodes": [{"id": "68c2216d-7393-4d64-a6e4-7b5e384389a4", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [420, 1020], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4.1-mini", "cachedResultName": "gpt-4.1-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "DVUm005uVd1yUYSL", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "849df02a-cd4c-4c1a-80c9-84852eccd7d7", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [840, 500], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3.1}, {"id": "b1fe6bd4-f20b-4e13-83ce-58aa80372fe5", "name": "Read Image URLs", "type": "n8n-nodes-base.googleSheets", "position": [-300, 480], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/17zQUytFekDK305wvgxYdEYm4N5QEQ1mrwsfccNn872I/edit#gid=0", "cachedResultName": "Product Images"}, "documentId": {"__rl": true, "mode": "list", "value": "17zQUytFekDK305wvgxYdEYm4N5QEQ1mrwsfccNn872I", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/17zQUytFekDK305wvgxYdEYm4N5QEQ1mrwsfccNn872I/edit?usp=drivesdk", "cachedResultName": "Image Generation"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "LZ3LlQvYNg4X6eWJ", "name": "<PERSON><PERSON><PERSON>"}}, "typeVersion": 4.5}, {"id": "3c69465c-e3c7-4536-80ae-70f2bac53414", "name": "Download Images", "type": "n8n-nodes-base.httpRequest", "position": [-100, 480], "parameters": {"url": "={{ $json['Image-URL'] }}", "options": {}}, "typeVersion": 4.2}, {"id": "8f099961-42bd-43c2-8258-64e12a2b9f4b", "name": "Analyze Images", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [260, 820], "parameters": {"text": "Briefly explain in less than 5 words what this image is about.", "modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "resource": "image", "inputType": "base64", "operation": "analyze"}, "credentials": {"openAiApi": {"id": "DVUm005uVd1yUYSL", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "9ec41380-5297-4786-8216-140255285edb", "name": "Product Photography Prompt", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [460, 820], "parameters": {"text": "=Image description: {{ $json.content }}", "messages": {"messageValues": [{"message": "=Create a short prompt for an AI image generator that receives a photo of a product to ultimately produce professional product photography.\n\nIf the product is wearable, it must be worn by a human model with visible face; if it's not wearable, it must be held or interacted with by a model.\n\nThe product must ALWAYS be shown together with a human model with the model's face visible.\n\nEnsure that instructions for optimal realism, best lighting, best angle, best colors, best model positioning, etc. are included according to the product type.\n\nAlways formulate the prompt to refer to the product as \"this [PRODUCT]\" so the AI image generator knows that an input photo of the product is being submitted.\n\nAlways add subtle grain for a cinematic look.\nThe description of the product will be sent to you. Respond exclusively with the final prompt, nothing else, not even quotation marks."}]}, "promptType": "define"}, "typeVersion": 1.6}, {"id": "e5fbd22f-4081-4f51-9906-4b0f2d58fa81", "name": "Send Image with Prompt to OpenAI", "type": "n8n-nodes-base.httpRequest", "position": [1100, 500], "parameters": {"url": "https://api.openai.com/v1/images/edits", "method": "POST", "options": {}, "sendBody": true, "contentType": "multipart-form-data", "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "model", "value": "gpt-image-1"}, {"name": "prompt", "value": "={{ $json.text }}"}, {"name": "image[]", "parameterType": "formBinaryData", "inputDataFieldName": "data"}, {"name": "quality", "value": "high"}, {"name": "size", "value": "1536x1024"}]}, "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "DVUm005uVd1yUYSL", "name": "OpenAi account"}}, "typeVersion": 4.2}, {"id": "4812c3d5-d5eb-4ee0-97cb-786d2a3a9da5", "name": "Convert Base64 to File", "type": "n8n-nodes-base.convertToFile", "position": [1300, 500], "parameters": {"options": {}, "operation": "toBinary", "sourceProperty": "data[0].b64_json"}, "typeVersion": 1.1}, {"id": "b6cb024c-1f67-4df2-8bb1-1a3740212b4d", "name": "Upload to Drive", "type": "n8n-nodes-base.googleDrive", "position": [1600, 500], "parameters": {"name": "={{ $('Analyze Images').item.json.content }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "1mAV3g0eR5XZ2wknZTbcfZOkLlq8GZryP", "cachedResultUrl": "https://drive.google.com/drive/folders/1mAV3g0eR5XZ2wknZTbcfZOkLlq8GZryP", "cachedResultName": "Product Images"}}, "credentials": {"googleDriveOAuth2Api": {"id": "cGjALhySclQE3yCC", "name": "<PERSON><PERSON><PERSON>"}}, "typeVersion": 3}, {"id": "7e855dc6-0a1b-44f3-83b8-64d76693de87", "name": "Insert Image URL in Table", "type": "n8n-nodes-base.googleSheets", "position": [1820, 500], "parameters": {"columns": {"value": {"Output": "={{ $json.webViewLink }}", "Prompt": "={{ $('Product Photography Prompt').item.json.text }}", "Image-URL": "={{ $('Read Image URLs').item.json['Image-URL'] }}"}, "schema": [{"id": "Image-URL", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Image-URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Prompt", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Prompt", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Output", "type": "string", "display": true, "required": false, "displayName": "Output", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["Image-URL"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/17zQUytFekDK305wvgxYdEYm4N5QEQ1mrwsfccNn872I/edit#gid=0", "cachedResultName": "Product Images"}, "documentId": {"__rl": true, "mode": "list", "value": "17zQUytFekDK305wvgxYdEYm4N5QEQ1mrwsfccNn872I", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/17zQUytFekDK305wvgxYdEYm4N5QEQ1mrwsfccNn872I/edit?usp=drivesdk", "cachedResultName": "Image Generation"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "LZ3LlQvYNg4X6eWJ", "name": "<PERSON><PERSON><PERSON>"}}, "typeVersion": 4.5}, {"id": "611b6d08-5a55-4085-840a-53a1b4eb24ed", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-540, 380], "parameters": {"width": 600, "height": 360, "content": "## Extract Product Images from Template"}, "typeVersion": 1}, {"id": "e27aa751-41d4-40a9-a72c-90e327388257", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [180, 720], "parameters": {"color": 4, "width": 600, "height": 360, "content": "## Analyze Images and Create Prompt for Product Photography"}, "typeVersion": 1}, {"id": "ea5e9556-0485-4be9-a35f-32be69ed2de0", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1020, 380], "parameters": {"color": 5, "width": 460, "height": 360, "content": "## gpt-image-1 creates the Product Photography"}, "typeVersion": 1}, {"id": "9869ab24-02db-4b88-8429-b0f7f5a5bf2d", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1520, 380], "parameters": {"color": 3, "width": 520, "height": 360, "content": "## Output is uploaded to Drive and the Image URLs are saved in the table"}, "typeVersion": 1}, {"id": "05c2e7af-6e3e-4171-ac28-444bec1eef49", "name": "When clicking 'Test workflow'", "type": "n8n-nodes-base.manualTrigger", "position": [-500, 480], "parameters": {}, "typeVersion": 1}, {"id": "88c861e1-6b7c-4597-899a-e0f13ad7994a", "name": "Convert to File", "type": "n8n-nodes-base.convertToFile", "position": [-80, -120], "parameters": {"options": {}, "operation": "toBinary", "sourceProperty": "data[0].b64_json"}, "typeVersion": 1.1}, {"id": "0edb4268-9e9e-41a9-9e6e-9bed3a73f0d9", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-540, -220], "parameters": {"color": 6, "width": 660, "height": 260, "content": "## Simple Image Generation\n### Don't forget the manual trigger ;)"}, "typeVersion": 1}, {"id": "81b1385a-4a94-475c-9ee8-31dd5efb8dc7", "name": "Generate Image", "type": "n8n-nodes-base.httpRequest", "position": [-260, -120], "parameters": {"url": "https://api.openai.com/v1/images/generations", "method": "POST", "options": {}, "sendBody": true, "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "model", "value": "gpt-image-1"}, {"name": "prompt", "value": "A childrens book drawing of a veterinarian using a stethoscope to listen to the heartbeat of a baby otter."}]}, "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "DVUm005uVd1yUYSL", "name": "OpenAi account"}}, "typeVersion": 4.2}], "pinData": {"Read Image URLs": [{"Output": "", "Prompt": "", "Image-URL": "https://www.chamelo.com/cdn/shop/files/image_143.png?v=**********", "row_number": 2}, {"Output": "", "Prompt": "", "Image-URL": "https://encrypted-tbn3.gstatic.com/shopping?q=tbn:ANd9GcQLTiQY-Gk_H9uIqBRFFx_C_R8qQqwh2Ob1wWyUnEaLPMlrKxlu1OmQA_zfFWeoSLIFwRUZoNUlcABIZ9VUCx6dJ6ce455OHY2wn7khZdr0BKuFpvgoM6SlFg", "row_number": 3}, {"Output": "", "Prompt": "", "Image-URL": "https://www.spierandmackay.com/files/catalog/PRODUCT_IMAGES/<PERSON><PERSON><PERSON>&<PERSON>-JSBH2110-3-<PERSON><PERSON>%20-%20Wool%20Scarf%20(3).jpg", "row_number": 4}], "When clicking 'Test workflow'": [{}]}, "connections": {"Merge": {"main": [[{"node": "Send Image with Prompt to OpenAI", "type": "main", "index": 0}]]}, "Analyze Images": {"main": [[{"node": "Product Photography Prompt", "type": "main", "index": 0}]]}, "Generate Image": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "Download Images": {"main": [[{"node": "Analyze Images", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Read Image URLs": {"main": [[{"node": "Download Images", "type": "main", "index": 0}]]}, "Upload to Drive": {"main": [[{"node": "Insert Image URL in Table", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Product Photography Prompt", "type": "ai_languageModel", "index": 0}]]}, "Convert Base64 to File": {"main": [[{"node": "Upload to Drive", "type": "main", "index": 0}]]}, "Product Photography Prompt": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "When clicking 'Test workflow'": {"main": [[{"node": "Read Image URLs", "type": "main", "index": 0}]]}, "Send Image with Prompt to OpenAI": {"main": [[{"node": "Convert Base64 to File", "type": "main", "index": 0}]]}}}