{"id": "Qpxx8UnnACBONNJu", "meta": {"instanceId": "a4bfc93e975ca233ac45ed7c9227d84cf5a2329310525917adaf3312e10d5462", "templateCredsSetupCompleted": true}, "name": "The Easiest Way to Send SMS Worldwide", "tags": [], "nodes": [{"id": "807bfde2-a20e-41ad-87c5-70bcd31e3dcc", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-300, -100], "parameters": {}, "typeVersion": 1}, {"id": "369069a8-0b79-4e5a-9a89-4bccafdea247", "name": "Send SMS", "type": "n8n-nodes-base.httpRequest", "position": [140, -100], "parameters": {"url": "https://rest.clicksend.com/v3/sms/send", "method": "POST", "options": {}, "jsonBody": "={\n    \"messages\": [\n        {\n            \"source\": \"php\",\n            \"body\": \"{{ $json.sms }}\",\n            \"to\": \"{{ $json.to }}\"\n        }\n    ]\n}\n", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth", "headerParameters": {"parameters": [{"name": "Content-Type", "value": " application/json"}]}}, "credentials": {"httpBasicAuth": {"id": "UwsDe2JxT39eWIvY", "name": "ClickSend API"}, "httpHeaderAuth": {"id": "M8w41EP0NFUSBShY", "name": "ClickSend API"}}, "typeVersion": 4.2}, {"id": "41989686-d6ca-4bb9-bece-4ef8ed37f485", "name": "Set SMS data", "type": "n8n-nodes-base.set", "position": [-80, -100], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "b4c4e62d-b09d-4f71-a48a-c1ea451aed6e", "name": "sms", "type": "string", "value": "Hi, this is my first message"}, {"id": "371aff94-147e-4241-823a-a5f6e7f7e68e", "name": "to", "type": "string", "value": "+39xxxxxxxx"}]}}, "typeVersion": 3.4}, {"id": "dd218cae-e928-4a63-953f-faae5ac794bf", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-300, -520], "parameters": {"width": 400, "height": 180, "content": "## STEP 1\n[Register here to ClickSend](https://clicksend.com/?u=586989) and obtain your API Key and 2 € of free credits\n\nIn the node \"Send SMS\" create a \"Basic Auth\" with the username you registered and the API Key provided as your password"}, "typeVersion": 1}, {"id": "61665d97-5ccc-4206-8577-95acf3e4c0d4", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-300, -300], "parameters": {"width": 400, "content": "## STEP 2\n\nIn the node \"Set SMS data\" add the text and recipient of the message including the international prefix (eg. +39) and the phone number without spaces"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "840e10d6-a142-4897-9a10-9164a6b7e2c2", "connections": {"Set SMS data": {"main": [[{"node": "Send SMS", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set SMS data", "type": "main", "index": 0}]]}}}