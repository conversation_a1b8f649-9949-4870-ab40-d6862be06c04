{"meta": {"instanceId": "257476b1ef58bf3cb6a46e65fac7ee34a53a5e1a8492d5c6e4da5f87c9b82833", "templateId": "2130"}, "nodes": [{"id": "10e83e54-7043-4894-bc92-be1fb0cfba04", "name": "if company does not exist on CRM", "type": "n8n-nodes-base.if", "position": [2120, 140], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "19bf6d06-76f4-479a-a9d8-2157414190b3", "operator": {"type": "object", "operation": "empty", "singleValue": true}, "leftValue": "={{ $input.item.json }}", "rightValue": ""}]}}, "typeVersion": 2}, {"id": "0afb1099-c0b8-4316-84ad-0b1d718bf07d", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [240, 240], "parameters": {"width": 257.64008049230523, "height": 255.97404402400312, "content": "## Setup\n1. Add `Clearbit`, `Hubspot`, and `ConvertKit` credentials\n2. Click on `Test workflow`\n3. Subscribe user to form/list so the event starts the workflow"}, "typeVersion": 1}, {"id": "4b7f0086-49cc-4662-8fba-a31abb25a76a", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [2340, 40], "parameters": {"color": 4, "width": 219.1588560076235, "height": 260.45841271216835, "content": "Map all data found about the company that you interested in"}, "typeVersion": 1}, {"id": "868af061-52ca-4c3b-870c-21b954da7c64", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [920, 240], "parameters": {"color": 4, "width": 233.74765680228705, "height": 260.45841271216835, "content": "Make sure to map the email field from the data your email automation tool provides"}, "typeVersion": 1}, {"id": "d8a8082b-ec9e-4295-b675-0bbf346e5831", "name": "Enrich company", "type": "n8n-nodes-base.clearbit", "notes": "Enrich company", "position": [1560, 140], "parameters": {"domain": "={{ $json.employment.domain }}", "additionalFields": {}}, "notesInFlow": false, "typeVersion": 1}, {"id": "ccbe7caf-a256-4273-bedb-ff6b59e1843f", "name": "Search company", "type": "n8n-nodes-base.hubspot", "position": [1860, 140], "parameters": {"limit": 1, "domain": "={{ $json.domain }}", "options": {}, "resource": "company", "operation": "searchByDomain", "authentication": "oAuth2"}, "typeVersion": 2, "alwaysOutputData": true}, {"id": "8e7ee0cd-7b23-4077-a4c9-3b4e40de0695", "name": "Upsert lead", "type": "n8n-nodes-base.hubspot", "position": [1560, 440], "parameters": {"email": "={{ $('Enrich email').item.json.email }}", "options": {}, "authentication": "oAuth2", "additionalFields": {"lastName": "={{ $('Enrich email').item.json.name.familyName }}", "firstName": "={{ $('Enrich email').item.json.name.fullName }}"}}, "typeVersion": 2}, {"id": "d7dde1e3-cd14-4977-8065-a2ec23e97d55", "name": "Create company", "type": "n8n-nodes-base.hubspot", "position": [2400, 120], "parameters": {"name": "={{ $('Enrich company').item.json.name }}", "resource": "company", "authentication": "oAuth2", "additionalFields": {"twitterBio": "={{ $('Enrich company').item.json.twitter.bio }}", "description": "={{ $('Enrich company').item.json.description }}", "yearFounded": "={{ $('Enrich company').item.json.foundedYear }}", "countryRegion": "={{ $('Enrich company').item.json.geo.country }}", "twitterHandle": "={{ $('Enrich company').item.json.twitter.handle }}", "totalMoneyRaised": "={{ $('Enrich company').item.json.metrics.raised }}", "twitterFollowers": "={{ $('Enrich company').item.json.twitter.followers }}", "companyDomainName": "={{ $('Enrich company').item.json.domain }}", "numberOfEmployees": "={{ $('Enrich company').item.json.metrics.employees }}"}}, "typeVersion": 2, "alwaysOutputData": true}, {"id": "c2f3015c-24ce-47ad-bce5-81f85145ef5c", "name": "Upsert contact", "type": "n8n-nodes-base.hubspot", "position": [2660, 120], "parameters": {"email": "={{ $('Enrich email').item.json.email }}", "options": {"resolveData": true}, "authentication": "oAuth2", "additionalFields": {"associatedCompanyId": "={{ $json.companyId }}"}}, "typeVersion": 2}, {"id": "fe33b776-b30f-44b8-b0db-77c2fd198fc7", "name": "Update company", "type": "n8n-nodes-base.hubspot", "position": [2400, 420], "parameters": {"resource": "company", "companyId": {"__rl": true, "mode": "id", "value": "={{ $json.companyId }}"}, "operation": "update", "updateFields": {"twitterBio": "={{ $('Enrich company').item.json.twitter.bio }}", "description": "={{ $('Enrich company').item.json.description }}", "countryRegion": "={{ $('Enrich company').item.json.geo.country }}", "twitterHandle": "={{ $('Enrich company').item.json.twitter.handle }}", "totalMoneyRaised": "={{ $('Enrich company').item.json.metrics.raised }}", "twitterFollowers": "={{ $('Enrich company').item.json.twitter.followers }}", "numberOfEmployees": "={{ $('Enrich company').item.json.metrics.employees }}"}, "authentication": "oAuth2"}, "typeVersion": 2}, {"id": "b7432f23-eb19-48bd-a76b-916c072bfb76", "name": "ConvertKit <PERSON>gger", "type": "n8n-nodes-base.convertKitTrigger", "position": [580, 340], "webhookId": "f0a3fa8a-a364-47c3-a261-ed71ba7abb8c", "parameters": {"event": "formSubscribe", "formId": 6242124}, "typeVersion": 1}, {"id": "97376217-0388-43fd-af20-06ef790e652c", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [520, 240], "parameters": {"color": 4, "width": 225.41119920533646, "height": 260.45841271216835, "content": "Replace this node with your email automation tool of choice"}, "typeVersion": 1}, {"id": "e19ad9e9-781a-47a6-9a8e-f27d0b0167f1", "name": "Filter out personal emails1", "type": "n8n-nodes-base.filter", "position": [780, 340], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "df6da257-7ec4-4433-9d29-2f12f6f11944", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.subscriber.email_address }}", "rightValue": "@gmail.com"}, {"id": "6a66410c-a2e8-494b-b972-751116e49418", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.subscriber.email_address }}", "rightValue": "@yahoo.com"}, {"id": "378fbe41-0e37-4756-93ca-bf81bfe8b258", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.subscriber.email_address }}", "rightValue": "@outlook.com"}, {"id": "fd05b842-3c11-4e1a-9226-0b0fd359ccab", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.subscriber.email_address }}", "rightValue": "@hotmail.com"}, {"id": "6040ea5d-3c15-4513-915b-47a55c24e8a7", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.subscriber.email_address }}", "rightValue": "@icloud.com"}, {"id": "ce67ed8b-34f9-4ba2-83d4-cc04cea090bb", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.subscriber.email_address }}", "rightValue": "@mail.com"}, {"id": "92c043ae-72de-41d8-887b-9e94755a9060", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.subscriber.email_address }}", "rightValue": "@aol.com"}, {"id": "377bcc07-e5a1-4e3a-a4da-4446f316a0b2", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.subscriber.email_address }}", "rightValue": "@zoho.com"}, {"id": "c09c7057-2833-4085-8cb9-d2f28d853724", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.subscriber.email_address }}", "rightValue": "@gmx"}]}}, "typeVersion": 2}, {"id": "b5258a3e-966c-4ac8-ab30-e1727f22db5a", "name": "Contact not found, do nothing1", "type": "n8n-nodes-base.noOp", "position": [1260, 540], "parameters": {}, "typeVersion": 1}, {"id": "bd5fc02e-eded-4e67-b880-e94678d7d728", "name": "Enrich email", "type": "n8n-nodes-base.clearbit", "notes": "Enrich email", "onError": "continueErrorOutput", "position": [980, 340], "parameters": {"email": "={{ $json.subscriber.email_address }}", "resource": "person", "additionalFields": {}}, "notesInFlow": false, "typeVersion": 1}, {"id": "972a4bee-6fd5-4c49-9a69-9f4f203e8285", "name": "If person has company", "type": "n8n-nodes-base.if", "position": [1260, 340], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "1a7aad55-5f4c-4bbc-a098-90f00a29be85", "operator": {"type": "string", "operation": "notEquals"}, "leftValue": "={{ $json.employment.domain }}", "rightValue": "={{ null }}"}]}}, "typeVersion": 2}], "pinData": {}, "connections": {"Enrich email": {"main": [[{"node": "If person has company", "type": "main", "index": 0}], [{"node": "Contact not found, do nothing1", "type": "main", "index": 0}]]}, "Create company": {"main": [[{"node": "Upsert contact", "type": "main", "index": 0}]]}, "Enrich company": {"main": [[{"node": "Search company", "type": "main", "index": 0}]]}, "Search company": {"main": [[{"node": "if company does not exist on CRM", "type": "main", "index": 0}]]}, "ConvertKit Trigger": {"main": [[{"node": "Filter out personal emails1", "type": "main", "index": 0}]]}, "If person has company": {"main": [[{"node": "Enrich company", "type": "main", "index": 0}], [{"node": "Upsert lead", "type": "main", "index": 0}]]}, "Filter out personal emails1": {"main": [[{"node": "Enrich email", "type": "main", "index": 0}]]}, "if company does not exist on CRM": {"main": [[{"node": "Create company", "type": "main", "index": 0}], [{"node": "Update company", "type": "main", "index": 0}]]}}}