{"meta": {"instanceId": "c911aed9995230b93fd0d9bc41c258d697c2fe97a3bab8c02baf85963eeda618"}, "nodes": [{"id": "fe599878-c955-4354-bbd0-a24fc1e3e933", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-340, -40], "parameters": {}, "typeVersion": 1}, {"id": "e03c7cef-4897-4234-b285-7be69e3c970d", "name": "Create Transcript1", "type": "n8n-nodes-base.httpRequest", "position": [100, -40], "parameters": {"url": "https://api.elevenlabs.io/v1/speech-to-text", "method": "POST", "options": {}, "sendBody": true, "contentType": "multipart-form-data", "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "file", "parameterType": "formBinaryData", "inputDataFieldName": "data"}, {"name": "model_id", "value": "scribe_v1"}]}, "genericAuthType": "httpCustomAuth", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "multipart/form-data"}]}}, "credentials": {"httpCustomAuth": {"id": "rDkSKjIA0mjmEv5k", "name": "Eleven Labs"}}, "typeVersion": 4.2}, {"id": "ea48aabf-1d93-41b4-84a0-53115aba53b4", "name": "Read/Write Files from Disk", "type": "n8n-nodes-base.readWriteFile", "position": [-120, -40], "parameters": {"options": {}, "fileSelector": "/files/tmp/tst1.mp4"}, "typeVersion": 1}], "pinData": {}, "connections": {"Read/Write Files from Disk": {"main": [[{"node": "Create Transcript1", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Read/Write Files from Disk", "type": "main", "index": 0}]]}}}