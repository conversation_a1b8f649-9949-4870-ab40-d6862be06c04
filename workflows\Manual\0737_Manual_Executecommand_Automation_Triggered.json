{"id": "29", "name": "N8N Español - Ejemplos", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "Minúsculas", "type": "n8n-nodes-base.executeCommand", "color": "#E31515", "notes": ".toLowerCase()", "position": [650, -10], "parameters": {"command": "=echo Minúsuclas: {{$node[\"Set\"].json[\"mensaje\"].toLowerCase()}}"}, "notesInFlow": true, "typeVersion": 1}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.executeCommand", "color": "#0BA1ED", "notes": ".toUpperCase()", "position": [800, 90], "parameters": {"command": "=echo Mayúsculas: {{$node[\"Set\"].json[\"mensaje\"].toUpperCase()}}"}, "notesInFlow": true, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [440, 180], "parameters": {"values": {"string": [{"name": "mensaje", "value": "Un León pasea por la Sabana Africana"}]}, "options": {}}, "typeVersion": 1}, {"name": "Replace", "type": "n8n-nodes-base.executeCommand", "color": "#0BA1ED", "notes": ".replace - .replace('<PERSON> León', '<PERSON> Ji<PERSON>')", "position": [800, 290], "parameters": {"command": "=echo Replace: {{$node[\"Set\"].json[\"mensaje\"].replace('<PERSON> León', '<PERSON>')}}"}, "notesInFlow": true, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "notes": "Junta las salidas", "position": [960, 10], "parameters": {}, "notesInFlow": true, "typeVersion": 1}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.merge", "color": "#F41C0D", "notes": "Junta las salidas", "position": [1070, 240], "parameters": {}, "notesInFlow": true, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Set": {"main": [[{"node": "Minúsculas", "type": "main", "index": 0}, {"node": "<PERSON><PERSON><PERSON><PERSON>", "type": "main", "index": 0}, {"node": "Replace", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "<PERSON><PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Replace": {"main": [[{"node": "<PERSON><PERSON><PERSON><PERSON>", "type": "main", "index": 1}]]}, "Mayúsculas": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Minúsculas": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}}