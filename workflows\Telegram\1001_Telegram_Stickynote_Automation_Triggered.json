{"id": "52pBJt8swWgtdY54", "meta": {"instanceId": "a4bfc93e975ca233ac45ed7c9227d84cf5a2329310525917adaf3312e10d5462", "templateCredsSetupCompleted": true}, "name": "MCP Client with Brave and Telegram", "tags": [], "nodes": [{"id": "af9b297d-0f8f-408f-a4d6-7545a94e8a38", "name": "List Brave Tools", "type": "n8n-nodes-mcp.mcpClient", "position": [560, -40], "parameters": {}, "credentials": {"mcpClientApi": {"id": "YEgJcPwvAlBOCEDA", "name": "MCP Client (STDIO) Brave"}}, "typeVersion": 1}, {"id": "c3265586-a376-4d02-8f33-828bbba6d221", "name": "Exec Brave tool", "type": "n8n-nodes-mcp.mcpClient", "position": [800, -40], "parameters": {"toolName": "={{ $json.tools[0].name }}", "operation": "executeTool", "toolParameters": "={\n  \"query\":\"{{ $('Clean query').item.json.query }}\"\n}"}, "credentials": {"mcpClientApi": {"id": "YEgJcPwvAlBOCEDA", "name": "MCP Client (STDIO) Brave"}}, "typeVersion": 1}, {"id": "adbfe84e-ab4a-4640-bb52-fcb06f9d1450", "name": "Clean query", "type": "n8n-nodes-base.code", "position": [300, -40], "parameters": {"jsCode": "for (const item of $input.all()) {\n  const originalText = item.json.text;\n\n  const query = originalText.replace(\"/brave \", \"\");\n\n  item.json.query = query;\n}\n\nreturn $input.all();\n"}, "typeVersion": 2}, {"id": "9905cad4-e847-44be-8cc4-69fd427ce8a1", "name": "Send message", "type": "n8n-nodes-base.telegram", "position": [1040, -40], "webhookId": "b48bb09b-e019-46a2-994b-8058f65e6442", "parameters": {"text": "={{ $json.result.content[0].text }}", "chatId": "={{ $('Get Message').item.json.message.from.id }}", "additionalFields": {"parse_mode": "HTML"}}, "credentials": {"telegramApi": {"id": "rQ5q95W7uKesMDx4", "name": "Telegram account Fastewb"}}, "typeVersion": 1.2}, {"id": "bf0e7c48-bbc8-4efd-9083-2fa965902453", "name": "Get Message", "type": "n8n-nodes-base.telegramTrigger", "position": [-440, -20], "webhookId": "07c09a64-758b-40ea-8c24-d999048781c3", "parameters": {"updates": ["message"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "rQ5q95W7uKesMDx4", "name": "Telegram account Fastewb"}}, "typeVersion": 1.1}, {"id": "b37c6f84-bceb-476c-9a7c-5682a4e69f8d", "name": "Search with Brave?", "type": "n8n-nodes-base.if", "position": [-180, -20], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "9c5ea127-cbbb-4304-8a93-b47b5c09b837", "operator": {"type": "string", "operation": "startsWith"}, "leftValue": "={{ $json.message.text }}", "rightValue": "/brave "}]}}, "typeVersion": 2.2}, {"id": "e879ea50-83f9-4a87-856c-a06a628ae31c", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-440, -860], "parameters": {"color": 6, "width": 480, "content": "## PRELIMINARY STEPS\n- Access to an n8n self-hosted instance and install the Community node \"n8n-nodes-mcp\". Please see this [easy guide](https://github.com/nerding-io/n8n-nodes-mcp)\n- Get your Brave Search API Key: https://brave.com/search/api/\n- Telegram Bot Access Token\n\n\n"}, "typeVersion": 1}, {"id": "754e62d1-efdb-460d-bdb1-2369d633a804", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-440, -660], "parameters": {"color": 6, "width": 480, "height": 420, "content": "## SET MCP BRAVE TOOL\nIn \"List Brave Tools\" create new credential as shown in  this image\n![image](https://github.com/nerding-io/n8n-nodes-mcp/raw/main/assets/credentials-envs.png)\n\nIn Environment field set this value:\nBRAVE_API_KEY=your-api-key"}, "typeVersion": 1}, {"id": "073eb8d2-9026-4031-af01-850342a5c5ca", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-240, -120], "parameters": {"height": 260, "content": "the search only occurs when the command \"/brave\" is present in the message"}, "typeVersion": 1}, {"id": "eb76fbed-0ba0-4a56-a1fe-65e4bfb38ea8", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [240, -120], "parameters": {"width": 220, "height": 260, "content": "I clean the message by removing the \"/brave\" command"}, "typeVersion": 1}, {"id": "980bf4e1-15cf-4276-b746-343850ec4b6f", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [520, -120], "parameters": {"width": 180, "height": 260, "content": "Get all available Brave search tools"}, "typeVersion": 1}, {"id": "2c712ec4-2184-4136-bd21-f17e19fb029e", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [760, -120], "parameters": {"width": 180, "height": 260, "content": "I get the search results"}, "typeVersion": 1}, {"id": "226a396a-e152-422d-b4e2-670a39117f57", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-440, -1180], "parameters": {"color": 3, "width": 480, "height": 280, "content": "## MCP-based Brave Search Engine on Telegram \n\nThis workflow is a powerful tool for automating interactions with Brave tools through Telegram, providing users with quick and easy access to information directly in their chat.\n\nThis n8n workflow enables users to perform web searches directly from Telegram using the Brave search engine. By simply sending the command /brave followed by a query, the workflow retrieves search results from Brave and returns them as a Telegram message."}, "typeVersion": 1}, {"id": "7c526a9e-f3a2-433c-aeb1-ced2e5af6a12", "name": "Get Text", "type": "n8n-nodes-base.set", "position": [80, -40], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "029f4e7e-b367-4aa9-863e-e372694940fb", "name": "text", "type": "string", "value": "={{ $json.message.text }}"}]}}, "typeVersion": 3.4}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "4566dd53-d373-43da-91c5-213ca5f335c6", "connections": {"Get Text": {"main": [[{"node": "Clean query", "type": "main", "index": 0}]]}, "Clean query": {"main": [[{"node": "List Brave Tools", "type": "main", "index": 0}]]}, "Get Message": {"main": [[{"node": "Search with Brave?", "type": "main", "index": 0}]]}, "Exec Brave tool": {"main": [[{"node": "Send message", "type": "main", "index": 0}]]}, "List Brave Tools": {"main": [[{"node": "Exec Brave tool", "type": "main", "index": 0}]]}, "Search with Brave?": {"main": [[{"node": "Get Text", "type": "main", "index": 0}]]}}}