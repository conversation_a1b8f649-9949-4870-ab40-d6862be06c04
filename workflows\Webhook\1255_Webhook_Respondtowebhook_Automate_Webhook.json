{"id": "tMiRJYDrXzpKysTX", "meta": {"instanceId": "2723a3a635131edfcb16103f3d4dbaadf3658e386b4762989cbf49528dccbdbd", "templateId": "1960"}, "name": "Stock Q&A Workflow", "tags": [], "nodes": [{"id": "ec3b86be-4113-4fd5-8365-02adb67693e9", "name": "Embeddings OpenAI1", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [1960, 720], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "fOF5kro9BJ6KMQ7n", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "42fd8020-3861-4d0f-a7a2-70e2c35f0bed", "name": "On new manual Chat Message", "type": "@n8n/n8n-nodes-langchain.manualChatTrigger", "disabled": true, "position": [1620, 240], "parameters": {}, "typeVersion": 1}, {"id": "a9b48d04-691e-4537-90f8-d7a4aa6153af", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1560, 120], "parameters": {"color": 6, "width": 903.*************, "height": 733.*************, "content": "## Step 2: Setup the Q&A \n### The incoming message from the webhook is queried from the Supabase Vector Store. The response is provided in the response webhook. "}, "typeVersion": 1}, {"id": "472b4800-745a-4337-9545-163247f7e9ae", "name": "Retrieval QA Chain", "type": "@n8n/n8n-nodes-langchain.chainRetrievalQa", "position": [1880, 240], "parameters": {"query": "={{ $json.body.input }}"}, "typeVersion": 1}, {"id": "e58bd82d-abc6-44ed-8e93-ec5436126d66", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [2280, 240], "parameters": {"options": {}, "respondWith": "text", "responseBody": "={{ $json.response.text }}"}, "typeVersion": 1}, {"id": "04bbf01e-8269-47c7-897d-4ea94a1bd1c0", "name": "Vector Store Retriever", "type": "@n8n/n8n-nodes-langchain.retrieverVectorStore", "position": [2020, 440], "parameters": {"topK": 5}, "typeVersion": 1}, {"id": "feee6d68-2e0d-4d40-897e-c1d833a13bf2", "name": "Webhook1", "type": "n8n-nodes-base.webhook", "position": [1620, 420], "webhookId": "679f4afb-189e-4f04-9dc0-439eec2ec5f1", "parameters": {"path": "19f5499a-3083-4783-93a0-e8ed76a9f742", "options": {}, "httpMethod": "POST", "responseMode": "responseNode"}, "typeVersion": 1.1}, {"id": "1b8d251f-7069-4d7d-b6d6-4bfa683d4ad1", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [280, 260], "parameters": {}, "typeVersion": 1}, {"id": "b746a7a4-ed94-4332-bf7b-65aadcf54130", "name": "Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [580, 260], "parameters": {"fileId": {"__rl": true, "mode": "list", "value": "1LZezppYrWpMStr4qJXtoIX-Dwzvgehll", "cachedResultUrl": "https://drive.google.com/file/d/1LZezppYrWpMStr4qJXtoIX-Dwzvgehll/view?usp=drivesdk", "cachedResultName": "crowdstrike.pdf"}, "options": {}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "1tsDIpjUaKbXy0be", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "83a7d470-f934-436d-ba3f-1ae7c776f5a5", "name": "Binary to Document", "type": "@n8n/n8n-nodes-langchain.documentBinaryInputLoader", "position": [860, 480], "parameters": {"loader": "pdf<PERSON><PERSON>der", "options": {}}, "typeVersion": 1}, {"id": "b52b4a90-99a1-49cc-a6f0-7551d6754496", "name": "Recursive Character Text Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "position": [860, 640], "parameters": {"options": {}, "chunkSize": 3000, "chunkOverlap": 200}, "typeVersion": 1}, {"id": "b525e130-2029-4f55-a603-1fdc05a19c17", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [1160, 480], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "fOF5kro9BJ6KMQ7n", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "5358c53f-55f9-431d-8956-c6bae7ad25bc", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [540, 120], "parameters": {"color": 6, "width": 772.*************, "height": 732.*************, "content": "## Step 1: Upserting the PDF\n### Fetch file from Google Drive, split it into chunks and insert into Supabase index\n\n"}, "typeVersion": 1}, {"id": "fb91e2da-0eeb-47a5-aa49-65bf56986857", "name": "Qdrant Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "position": [940, 260], "parameters": {"mode": "insert", "options": {}, "qdrantCollection": {"__rl": true, "mode": "id", "value": "=crowd"}}, "credentials": {"qdrantApi": {"id": "U5CpjAgFeXziP3I1", "name": "QdrantApi account"}}, "typeVersion": 1}, {"id": "89e14837-d1fc-4b1e-9ebc-7cf3e7fd9a70", "name": "Qdrant Vector Store1", "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "position": [1980, 600], "parameters": {"qdrantCollection": {"__rl": true, "mode": "id", "value": "={{ $json.body.company }}"}}, "credentials": {"qdrantApi": {"id": "U5CpjAgFeXziP3I1", "name": "QdrantApi account"}}, "typeVersion": 1}, {"id": "c619245b-5ea0-4354-974d-21ec6b8efa93", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1880, 460], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "fOF5kro9BJ6KMQ7n", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "e4aa780d-8069-4308-a61f-82ed876af71a", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-560, 120], "parameters": {"color": 6, "width": 710.*************, "height": 726.*************, "content": "## Start here: Step-by Step Youtube Tutorial :star:\n\n[![Building an AI Crew to Analyze Financial Data with CrewAI and n8n](https://img.youtube.com/vi/pMvizUx5n1g/sddefault.jpg)](https://www.youtube.com/watch?v=pMvizUx5n1g)\n"}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {}, "versionId": "463aec94-26a6-436d-8732-fc01d637c6ae", "connections": {"Webhook1": {"main": [[{"node": "Retrieval QA Chain", "type": "main", "index": 0}]]}, "Google Drive": {"main": [[{"node": "Qdrant Vector Store", "type": "main", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Qdrant Vector Store", "type": "ai_embedding", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Retrieval QA Chain", "type": "ai_languageModel", "index": 0}]]}, "Binary to Document": {"ai_document": [[{"node": "Qdrant Vector Store", "type": "ai_document", "index": 0}]]}, "Embeddings OpenAI1": {"ai_embedding": [[{"node": "Qdrant Vector Store1", "type": "ai_embedding", "index": 0}]]}, "Retrieval QA Chain": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Qdrant Vector Store1": {"ai_vectorStore": [[{"node": "Vector Store Retriever", "type": "ai_vectorStore", "index": 0}]]}, "Vector Store Retriever": {"ai_retriever": [[{"node": "Retrieval QA Chain", "type": "ai_retriever", "index": 0}]]}, "On new manual Chat Message": {"main": [[{"node": "Retrieval QA Chain", "type": "main", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "Google Drive", "type": "main", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Binary to Document", "type": "ai_textSplitter", "index": 0}]]}}}