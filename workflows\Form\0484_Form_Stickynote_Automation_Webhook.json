{"name": "Dynamic credentials using expressions", "nodes": [{"id": "cc6f2b1e-0ed0-4d22-8a44-d7223ba283b4", "name": "n8n Form Trigger", "type": "n8n-nodes-base.formTrigger", "position": [560, 520], "webhookId": "da4071f2-7550-4dae-aa48-8bced4291643", "parameters": {"path": "da4071f2-7550-4dae-aa48-8bced4291643", "formTitle": "Test dynamic credentials", "formFields": {"values": [{"fieldLabel": "Enter your NASA API key", "requiredField": true}]}, "responseMode": "responseNode", "formDescription": "This form is for testing an n8n workflow that demonstrates setting credentials with expressions."}, "typeVersion": 2}, {"id": "ef336bae-3d4f-419c-ab5c-b9f0de89f170", "name": "NASA", "type": "n8n-nodes-base.nasa", "position": [900, 520], "parameters": {"additionalFields": {}}, "credentials": {"nasaApi": {"id": "QDDBOZOD6k3ijL5t", "name": "NASA account"}}, "typeVersion": 1}, {"id": "143bcdb6-aca0-4dd8-9204-9777271cd230", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [1220, 520], "parameters": {"options": {}, "redirectURL": "={{ $json.url }}", "respondWith": "redirect"}, "typeVersion": 1}, {"id": "0a0dee23-fa16-4f09-b5e0-856f47fb53d0", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [120, 140], "parameters": {"color": 4, "width": 322, "height": 564, "content": "This workflow shows how to set credentials dynamically using expressions.\n\n\nFirst, set up your NASA credential: \n\n1. Create a new NASA credential.\n1. <PERSON>ver over **API Key**.\n1. Toggle **Expression** on.\n1. In the **API Key** field, enter `{{ $json[\"Enter your NASA API key\"] }}`.\n\n\nThen, test the workflow:\n\n1. Get an [API key from NASA](https://api.nasa.gov/)\n2. Select **Test workflow**\n3. Enter your key using the form.\n4. The workflow runs and sends you to the NASA picture of the day.\n\n\nFor more information on expressions, refer to [n8n documentation | Expressions](https://docs.n8n.io/code/expressions/)."}, "typeVersion": 1}, {"id": "dd766e32-334d-4e46-9daa-7800b134a3a5", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [500, 380], "parameters": {"height": 319, "content": "User submits an API key using the form"}, "typeVersion": 1}, {"id": "3d8f02e6-e029-41dc-89ad-0f5cffe09348", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [820, 380], "parameters": {"color": 5, "height": 319, "content": "The workflow passes the key to the NASA node. You can reference the value using the expression `$json[\"Enter your NASA API key\"]`. This is also available to the node credential. "}, "typeVersion": 1}, {"id": "096eb6ab-c276-4687-9dc0-50e16a8f709a", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1140, 380], "parameters": {"height": 319, "content": "The Respond to Webhook node controls the form response (in this example, redirecting the user to an image)"}, "typeVersion": 1}], "pinData": {}, "connections": {"NASA": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "n8n Form Trigger": {"main": [[{"node": "NASA", "type": "main", "index": 0}]]}}}