{"meta": {"instanceId": "1dd912a1610cd0376bae7bb8f1b5838d2b601f42ac66a48e012166bb954fed5a", "templateId": "2304"}, "nodes": [{"id": "6882e5c9-a468-4089-bffa-c8c04d28d8aa", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [380, 240], "parameters": {}, "typeVersion": 1}, {"id": "5688dfe6-aeba-4c00-8626-396eb1a5d695", "name": "Write Result File to Disk", "type": "n8n-nodes-base.readWriteFile", "position": [980, 240], "parameters": {"options": {}, "fileName": "document.pdf", "operation": "write", "dataPropertyName": "=data"}, "typeVersion": 1}, {"id": "fde98636-e4a2-4950-9b82-015ff841f24b", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [720, 100], "parameters": {"width": 218, "height": 132, "content": "## Authentication\nConversion requests must be authenticated. Please create \n[ConvertAPI account to get authentication secret](https://www.convertapi.com/a/signin)"}, "typeVersion": 1}, {"id": "c322b7d4-0858-45de-a5ed-0efddb2608c9", "name": "Download XLSX File", "type": "n8n-nodes-base.httpRequest", "position": [580, 240], "parameters": {"url": "https://cdn.convertapi.com/public/files/demo.xlsx", "options": {"response": {"response": {"responseFormat": "file"}}}}, "typeVersion": 4.2}, {"id": "3f3d190e-0c39-4a99-a65e-cb7c5e1e0f65", "name": "File conversion to PDF", "type": "n8n-nodes-base.httpRequest", "position": [780, 240], "parameters": {"url": "https://v2.convertapi.com/convert/xlsx/to/pdf", "method": "POST", "options": {"response": {"response": {"responseFormat": "file"}}}, "sendBody": true, "contentType": "multipart-form-data", "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "file", "parameterType": "formBinaryData", "inputDataFieldName": "=data"}]}, "genericAuthType": "httpQueryAuth", "headerParameters": {"parameters": [{"name": "Accept", "value": "application/octet-stream"}]}}, "credentials": {"httpQueryAuth": {"id": "WdAklDMod8fBEMRk", "name": "Query Auth account"}}, "notesInFlow": true, "typeVersion": 4.2}], "pinData": {}, "connections": {"Download XLSX File": {"main": [[{"node": "File conversion to PDF", "type": "main", "index": 0}]]}, "File conversion to PDF": {"main": [[{"node": "Write Result File to Disk", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Download XLSX File", "type": "main", "index": 0}]]}}}