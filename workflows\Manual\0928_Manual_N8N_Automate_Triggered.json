{"nodes": [{"id": "142277c6-73a5-4b99-9e94-72655bbe0ea8", "name": "n8n", "type": "n8n-nodes-base.n8n", "position": [-420, -120], "parameters": {"filters": {}, "requestOptions": {}}, "credentials": {"n8nApi": {"id": "4v19HuBPwx43oswi", "name": "n8n account"}}, "typeVersion": 1}, {"id": "6adf03cb-4194-4616-99d0-6495a660c283", "name": "TAG? Auto start", "type": "n8n-nodes-base.if", "position": [-180, -120], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "03241d00-9ec1-4215-8036-2d219a7874cb", "operator": {"type": "array", "operation": "contains", "rightType": "any"}, "leftValue": "={{ $json.tags.map((obj) => obj.name) }}", "rightValue": "Auto start"}]}}, "typeVersion": 2.2}, {"id": "8bd4868a-6dec-48b9-8593-36badf42d7ff", "name": "n8n1", "type": "n8n-nodes-base.n8n", "position": [100, -120], "parameters": {"operation": "activate", "workflowId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "requestOptions": {}}, "credentials": {"n8nApi": {"id": "4v19HuBPwx43oswi", "name": "n8n account"}}, "typeVersion": 1}, {"id": "c2b7a716-ab5f-4e49-b340-eab6721c52e4", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-640, -120], "parameters": {}, "typeVersion": 1}, {"id": "0090a343-73fd-4c53-b80b-27dd2789a849", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-680, -580], "parameters": {"color": 5, "width": 620, "height": 420, "content": "# Auto Starter\n\nOn importing workflows these will not be auto started, even if the old version was running. To fix this we created this workflow that can be run after n8n starts. It fits in our auto deploy pipeline and modified n8n container that will import workflows, start n8n and start the tagged workflows.\n\n- Start this workflow after n8n starts.\n- It will get all workflows in the running n8n instance.\n- If the files have a tag **'Auto start'** the workflow will be started.\n\n\n**Configuration**\n- You need a a **n8n api key** configured."}, "typeVersion": 1}], "connections": {"n8n": {"main": [[{"node": "TAG? Auto start", "type": "main", "index": 0}]]}, "TAG? Auto start": {"main": [[{"node": "n8n1", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "n8n", "type": "main", "index": 0}]]}}}