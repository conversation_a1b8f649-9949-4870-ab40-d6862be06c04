{"id": "7ZIG5xxEACMBgj4Z", "meta": {"instanceId": "1b1e85a338c6ce950207b3b471d43405c7b292e6b980ee5b66c1a9e5af2a50f8"}, "name": "Create Threads on <PERSON><PERSON>", "tags": [{"id": "f3JGorUk16BX0hZI", "name": "<PERSON><PERSON>", "createdAt": "2025-01-19T09:37:40.989Z", "updatedAt": "2025-01-19T09:37:40.989Z"}, {"id": "hTHZamkzaTBmF3yo", "name": "Template", "createdAt": "2025-01-16T04:45:44.377Z", "updatedAt": "2025-01-16T04:45:44.377Z"}], "nodes": [{"id": "5fea442d-80e7-4e9c-9214-12fa8bc98a71", "name": "C<PERSON> <PERSON><PERSON>", "type": "n8n-nodes-base.httpRequest", "position": [-2160, 540], "parameters": {"url": "https://bsky.social/xrpc/com.atproto.server.createSession", "method": "POST", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "identifier", "value": "={{ $json.Bluesky<PERSON>le }}"}, {"name": "password", "value": "={{ $json.BlueskyAppPassword }}"}]}}, "typeVersion": 4.2}, {"id": "8339e67d-87f8-48a5-a9c9-48d90d9baf49", "name": "Create Reply", "type": "n8n-nodes-base.httpRequest", "position": [-1200, 540], "parameters": {"url": "https://bsky.social/xrpc/com.atproto.repo.createRecord", "method": "POST", "options": {}, "jsonBody": "={{ $('Create Reply Text').item.json.toJsonString() }}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{ $('Create Bluesky Session').item.json.accessJwt}}"}]}}, "typeVersion": 4.2}, {"id": "16fa4a6c-ab93-4ea1-9a9b-2f9e9804e25a", "name": "Run Daily at 9 AM", "type": "n8n-nodes-base.scheduleTrigger", "position": [-2640, 540], "parameters": {"rule": {"interval": [{"triggerAtHour": 9}]}}, "typeVersion": 1.2}, {"id": "7c06e67d-a524-457b-a6ce-955aab353352", "name": "<PERSON> Credentials", "type": "n8n-nodes-base.set", "position": [-2380, 540], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "ec07f538-0164-40c5-a199-45e2a8a4604a", "name": "Blues<PERSON><PERSON><PERSON>le", "type": "string", "value": "[enter your bluesky handle here]"}, {"id": "463e906c-c49b-41e0-9176-04bd2c175d0b", "name": "BlueskyAppPassword", "type": "string", "value": "[enter your app password here]"}]}}, "typeVersion": 3.4}, {"id": "156da8f4-5cc7-4a58-9a6c-75b1bd6df4cd", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-2440, 340], "parameters": {"color": 3, "width": 440, "height": 360, "content": "## Bluesky Authentication\nSet your Bluesky social link and also your App Password."}, "typeVersion": 1}, {"id": "2bd742a8-3955-4452-95c8-9c9a7b8071e2", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-1960, 340], "parameters": {"color": 4, "width": 440, "height": 360, "content": "## Initial Post [A]\nWhen the first post is created two identifiers are returned:\n- URI (an at:// link to the post)\n- CID (a content-hash of the post)"}, "typeVersion": 1}, {"id": "e6e258e8-e4a7-4e33-bd7c-40dd4eb8842f", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-1480, 340], "parameters": {"color": 5, "width": 460, "height": 360, "content": "## First Reply Post [B]\nHere we set the 'ROOT' and the 'PARENT' values.\n\nWe use both URI and CID as ROOT and PARENT, as this is the first child of the root post (Initial Post [A]).\n\nWe receive a new URI and CID in return."}, "typeVersion": 1}, {"id": "8e3808d3-ec7d-4f46-89b7-9b27350801de", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-2440, 740], "parameters": {"width": 440, "height": 380, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n## Sibling Post [C]\nSet 'ROOT' using URI/CID from the root post (Initial Post [A]).\n\nFor the PARENT, we use the URI and CID returned by the preceeding post (First Reply Post [B])."}, "typeVersion": 1}, {"id": "0b9d8329-2dde-4b3b-bd9e-42d5aa367225", "name": "Create Reply Text", "type": "n8n-nodes-base.code", "position": [-1420, 540], "parameters": {"jsCode": "// Create the reply text\nconst replyText = \"[reply post - hidden]\";\n\n// Calculate timestamp 1 second from now\nconst futureDate = new Date(Date.now() + 1000);\n\n// Create the reply post object\nconst replyPostData = {\n    repo: $('Set Bluesky Credentials').first().json.BlueskyHandle,\n    collection: \"app.bsky.feed.post\",\n    record: {\n        \"$type\": \"app.bsky.feed.post\",\n        text: replyText,\n        reply: {\n            root: {\n                cid: $('Create Initial Post').first().json.cid,\n                uri: $('Create Initial Post').first().json.uri\n            },\n            parent: {\n                cid: $('Create Initial Post').first().json.cid,\n                uri: $('Create Initial Post').first().json.uri\n            }\n        },\n        createdAt: futureDate.toISOString()\n    }\n};\n\nreturn replyPostData;"}, "typeVersion": 2}, {"id": "abfbef84-1b94-4ec4-ae96-345d0ea888ce", "name": "Create Sibling Text", "type": "n8n-nodes-base.code", "position": [-2380, 780], "parameters": {"jsCode": "// Create the sibling text\nconst siblingText = \"[first sibling - hidden]\";\n\n// Calculate timestamp 2 seconds from now\nconst futureDate = new Date(Date.now() + 2000);\n\n// Create the sibling post object\nconst siblingPostData = {\n    repo: $('Set Bluesky Credentials').first().json.BlueskyHandle,\n    collection: \"app.bsky.feed.post\",\n    record: {\n        \"$type\": \"app.bsky.feed.post\",\n        text: siblingText,\n        reply: {\n            root: {\n                cid: $('Create Initial Post').first().json.cid,\n                uri: $('Create Initial Post').first().json.uri\n            },\n            parent: {\n                cid: $('Create Reply').first().json.cid,\n                uri: $('Create Reply').first().json.uri\n            }\n        },\n        createdAt: futureDate.toISOString()\n    }\n};\n\nreturn siblingPostData;"}, "typeVersion": 2}, {"id": "f554f5bc-bd81-4b09-887b-6c4167e8f5f1", "name": "Create Sibling", "type": "n8n-nodes-base.httpRequest", "position": [-2160, 780], "parameters": {"url": "https://bsky.social/xrpc/com.atproto.repo.createRecord", "method": "POST", "options": {}, "jsonBody": "={{ $('Create Sibling Text').item.json.toJsonString() }}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{ $('Create Bluesky Session').item.json.accessJwt}}"}]}}, "typeVersion": 4.2}, {"id": "7a7025fe-4b35-44db-8974-2bc81c59eead", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-2700, 340], "parameters": {"color": 7, "width": 220, "height": 360, "content": "## Trigger"}, "typeVersion": 1}, {"id": "097767bc-fbb2-4e71-af68-b87d354b796e", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-1960, 740], "parameters": {"color": 6, "width": 940, "height": 380, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n## Sibling Posts using Loop node [D]\nHere we set the 'ROOT' using both URI and CID from the root post (Initial Post [A]), and for all future siblings.\n\nFor the PARENT, we use the URI and CID returned by the preceeding post.\nSo the first loop iteration gets it from the 'Create Sibling' node, and after that from the 'Create Post' node."}, "typeVersion": 1}, {"id": "5f8e88ef-0f56-4d81-921f-17dbfea41eec", "name": "Loop Posts", "type": "n8n-nodes-base.splitInBatches", "position": [-1720, 780], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "449a0269-61cb-477c-b315-943daada65ba", "name": "Create Initial Post", "type": "n8n-nodes-base.httpRequest", "position": [-1680, 540], "parameters": {"url": "https://bsky.social/xrpc/com.atproto.repo.createRecord", "method": "POST", "options": {}, "jsonBody": "={{ $('Create Post Text').item.json.toJsonString() }}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{ $('Create Bluesky Session').item.json.accessJwt}}"}]}}, "typeVersion": 4.2}, {"id": "e29aa109-2b11-44c7-9a85-b5199ef4923c", "name": "Create Post", "type": "n8n-nodes-base.httpRequest", "position": [-1360, 780], "parameters": {"url": "https://bsky.social/xrpc/com.atproto.repo.createRecord", "method": "POST", "options": {}, "jsonBody": "={{ $('Create Sibling Text (Loop)').item.json.toJsonString() }}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{ $('Create Bluesky Session').item.json.accessJwt}}"}]}}, "typeVersion": 4.2}, {"id": "51c05a08-797b-448b-b291-753be14d7c78", "name": "Wait", "type": "n8n-nodes-base.wait", "position": [-1200, 780], "webhookId": "0414c5a9-938c-427d-98a2-1295eb02380d", "parameters": {"amount": 2}, "typeVersion": 1.1}, {"id": "b9f1bd23-e8f2-472b-ab61-05e85ffece12", "name": "Create Post Text", "type": "n8n-nodes-base.code", "position": [-1900, 540], "parameters": {"mode": "runOnceForEachItem", "jsCode": "// Create the initial post text\nconst postText = \"[initial post - visible]\";\n\n// Create the parent post object\nconst postData = {\n    repo: $('Set Bluesky Credentials').first().json.BlueskyHandle,\n    collection: \"app.bsky.feed.post\",\n    record: {\n        $type: \"app.bsky.feed.post\",\n        text: postText,\n        createdAt: $now\n    }\n};\n\nreturn postData;"}, "typeVersion": 2}, {"id": "6c1e26df-564e-4b49-8aff-bc6e5bedcbb8", "name": "Create Sibling Array", "type": "n8n-nodes-base.code", "position": [-1900, 780], "parameters": {"jsCode": "const items = [\n    { id: 2, name: '[sibling two - hidden]' },\n    { id: 3, name: '[sibling three - hidden]' },\n    { id: 4, name: '[sibling four - hidden]' },\n    { id: 5, name: '[sibling five - hidden]' },\n    { id: 6, name: '[sibling six - hidden]' },\n    { id: 7, name: '[sibling seven - hidden]' },\n    { id: 8, name: '[sibling eight - hidden]' },\n    { id: 9, name: '[sibling nine - visible]' },\n    { id: 10, name: '[sibling ten - visible]' }\n];\n\nreturn items;"}, "typeVersion": 2}, {"id": "5a91aff4-1b9d-4c69-beed-fa906c2a133b", "name": "Create Sibling Text (Loop)", "type": "n8n-nodes-base.code", "position": [-1540, 780], "parameters": {"mode": "runOnceForEachItem", "jsCode": "// Create the sibling text\nconst siblingText = `[${$json.name}]`;\n\n// For the first iteration, use the parent IDs from Create Sibling node\n// For subsequent iterations, use the Create Post node\nconst isFirstIteration = $runIndex === 0;\nconst cid = isFirstIteration \n    ? $('Create Sibling').first().json.cid \n    : $('Create Post').first().json.cid;\nconst uri = isFirstIteration \n    ? $('Create Sibling').first().json.uri \n    : $('Create Post').first().json.uri;\n\n// Calculate timestamp 2 seconds from now\nconst futureDate = new Date(Date.now() + 2000);\n\n// Create the sibling post object\nconst siblingPostData = {\n    repo: $('Set Bluesky Credentials').first().json.BlueskyHandle,\n    collection: \"app.bsky.feed.post\",\n    record: {\n        \"$type\": \"app.bsky.feed.post\",\n        text: siblingText,\n        reply: {\n            root: {\n                cid: $('Create Initial Post').first().json.cid,\n                uri: $('Create Initial Post').first().json.uri\n            },\n            parent: {\n                cid: cid,\n                uri: uri\n            }\n        },\n        createdAt: futureDate.toISOString()\n    }\n};\n\nreturn siblingPostData;"}, "typeVersion": 2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "d0c40145-fbf4-46b5-9df0-5b5c9c896d9c", "connections": {"Wait": {"main": [[{"node": "Loop Posts", "type": "main", "index": 0}]]}, "Loop Posts": {"main": [[], [{"node": "Create Sibling Text (Loop)", "type": "main", "index": 0}]]}, "Create Post": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Create Reply": {"main": [[{"node": "Create Sibling Text", "type": "main", "index": 0}]]}, "Create Sibling": {"main": [[{"node": "Create Sibling Array", "type": "main", "index": 0}]]}, "Create Post Text": {"main": [[{"node": "Create Initial Post", "type": "main", "index": 0}]]}, "Create Reply Text": {"main": [[{"node": "Create Reply", "type": "main", "index": 0}]]}, "Run Daily at 9 AM": {"main": [[{"node": "<PERSON> Credentials", "type": "main", "index": 0}]]}, "Create Initial Post": {"main": [[{"node": "Create Reply Text", "type": "main", "index": 0}]]}, "Create Sibling Text": {"main": [[{"node": "Create Sibling", "type": "main", "index": 0}]]}, "Create Sibling Array": {"main": [[{"node": "Loop Posts", "type": "main", "index": 0}]]}, "Create Bluesky Session": {"main": [[{"node": "Create Post Text", "type": "main", "index": 0}]]}, "Set Bluesky Credentials": {"main": [[{"node": "C<PERSON> <PERSON><PERSON>", "type": "main", "index": 0}]]}, "Create Sibling Text (Loop)": {"main": [[{"node": "Create Post", "type": "main", "index": 0}]]}}}