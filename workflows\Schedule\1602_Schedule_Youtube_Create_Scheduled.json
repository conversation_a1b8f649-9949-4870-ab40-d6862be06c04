{"id": "O9FXr8iXzhSgYKaL", "meta": {"instanceId": "d8bbc8c5a59875a8be9f3c7142d858bc46c4b8e36a11781a25e945fcf9a5767a"}, "name": "Post New YouTube Videos to X", "tags": [], "nodes": [{"id": "576be5c4-1ed0-4d01-a980-cb2fc31e2223", "name": "Post to X", "type": "n8n-nodes-base.twitter", "position": [1280, 380], "parameters": {"text": "={{ $json.message.content }}", "additionalFields": {}}, "credentials": {"twitterOAuth2Api": {"id": "FjHOuF0APzoMqIjG", "name": "X account"}}, "typeVersion": 2}, {"id": "3b87cf2a-51d5-4589-9729-bb1fe3cf<PERSON>ca", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [620, 254.**************], "parameters": {"color": 3, "width": 221.**************, "height": 308.*************, "content": "🆔 Ensure you enter your YouTube Channel ID in the \"Channel ID\" field of this node. You can find your [Channel ID here](https://youtube.com/account_advanced)."}, "typeVersion": 1}, {"id": "912e631c-aa43-4e02-9816-b35fe6e62dd8", "name": "Generate Post for X with ChatGPT", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [900, 380], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-3.5-turbo", "cachedResultName": "GPT-3.5-TURBO"}, "options": {}, "messages": {"values": [{"content": "=Write an engaging post about my latest YouTube video for X (Twitter) of no more than 140 characters in length. Link to the video at https://youtu.be/{{ $json.id.videoId }} use this title and description: {{ $json.snippet.title }} {{ $json.snippet.description }}"}]}}, "credentials": {"openAiApi": {"id": "UpdYKqoR9wsGBnaA", "name": "OpenAi account"}}, "typeVersion": 1.3}, {"id": "841ee140-7e37-4e9c-8ab2-2a3ee941d255", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [360, 254.*************], "parameters": {"width": 244.**************, "height": 102.**************, "content": "**Use AI to Promote Your New YouTube Videos on X**\n\n🎬 Watch the [Setup Video Here](https://mrc.fm/ai2x)"}, "typeVersion": 1}, {"id": "583b7d5d-e5dc-4183-92ee-8135ce6095a8", "name": "Fetch Latest Videos", "type": "n8n-nodes-base.youTube", "position": [680, 380], "parameters": {"limit": 1, "filters": {"channelId": "UC08Fah8EIryeOZRkjBRohcQ", "publishedAfter": "={{ new Date(new Date().getTime() - 30 * 60000).toISOString() }}"}, "options": {}, "resource": "video"}, "credentials": {"youTubeOAuth2Api": {"id": "cVI5wEqeFEeJ81nk", "name": "YouTube account"}}, "typeVersion": 1}, {"id": "6e391007-10e2-4e67-9db6-e13d5d2bef11", "name": "Check Every 30 Min", "type": "n8n-nodes-base.scheduleTrigger", "position": [460, 380], "parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 30}]}}, "typeVersion": 1.2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "a321d863-1a58-4100-bf8f-d2af08f11382", "connections": {"Check Every 30 Min": {"main": [[{"node": "Fetch Latest Videos", "type": "main", "index": 0}]]}, "Fetch Latest Videos": {"main": [[{"node": "Generate Post for X with ChatGPT", "type": "main", "index": 0}]]}, "Generate Post for X with ChatGPT": {"main": [[{"node": "Post to X", "type": "main", "index": 0}]]}}}