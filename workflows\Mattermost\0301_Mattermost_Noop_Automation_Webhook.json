{"id": "18", "name": "Gender Inclusive Language", "nodes": [{"name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [150, 450], "parameters": {"path": "webhook", "options": {}, "httpMethod": "POST"}, "typeVersion": 1}, {"name": "Mattermost1", "type": "n8n-nodes-base.mattermost", "position": [550, 300], "parameters": {"message": "May I suggest \"folks\" or “y'all”? We use gender inclusive language here. 😄", "channelId": "={{$node[\"Webhook\"].json[\"body\"][\"channel_id\"]}}", "attachments": [], "otherOptions": {}}, "credentials": {"mattermostApi": "n8n Mattermost - Bot"}, "typeVersion": 1}, {"name": "IF", "type": "n8n-nodes-base.if", "position": [340, 450], "parameters": {"conditions": {"string": [{"value1": "={{$node[\"Webhook\"].json[\"body\"][\"text\"]}}", "value2": "guys", "operation": "contains"}, {"value1": "={{$node[\"Webhook\"].json[\"body\"][\"text\"]}}", "value2": "Guys", "operation": "contains"}, {"value1": "={{$node[\"Webhook\"].json[\"body\"][\"text\"]}}", "value2": "bros", "operation": "contains"}, {"value1": "={{$node[\"Webhook\"].json[\"body\"][\"text\"]}}", "value2": "Bros", "operation": "contains"}, {"value1": "={{$node[\"Webhook\"].json[\"body\"][\"text\"]}}", "value2": "dudes", "operation": "contains"}, {"value1": "={{$node[\"Webhook\"].json[\"body\"][\"text\"]}}", "value2": "Dudes", "operation": "contains"}, {"value1": "={{$node[\"Webhook\"].json[\"body\"][\"text\"]}}", "value2": "gals", "operation": "contains"}, {"value1": "={{$node[\"Webhook\"].json[\"body\"][\"text\"]}}", "value2": "Gals", "operation": "contains"}]}, "combineOperation": "any"}, "typeVersion": 1}, {"name": "NoOp", "type": "n8n-nodes-base.noOp", "position": [550, 550], "parameters": {}, "typeVersion": 1}], "active": true, "settings": {}, "connections": {"IF": {"main": [[{"node": "Mattermost1", "type": "main", "index": 0}], [{"node": "NoOp", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}}}