{"meta": {"instanceId": "6b6a2db47bdf8371d21090c511052883cc9a3f6af5d0d9d567c702d74a18820e"}, "nodes": [{"id": "f4570aad-db25-4dcd-8589-b1c8335935de", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [20, 5220], "parameters": {}, "typeVersion": 1}, {"id": "675243b0-080f-4d5e-a0ca-a0fe0e7c04a9", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [185, 5140], "parameters": {"color": 7, "width": 426.32566328767217, "height": 260.3707944299243, "content": "**Find and replace image in docx. Connect to a datasource with an image URL you want to insert into the Docx file**"}, "typeVersion": 1}, {"id": "16bbf5da-5ebc-4e9c-8b3c-80d0077c51b8", "name": "Find Image ID in Docx", "type": "n8n-nodes-base.httpRequest", "position": [900, 5220], "parameters": {"url": "=https://docs.googleapis.com/v1/documents/{{$json.documentId}}", "options": {}, "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDocsOAuth2Api"}, "notesInFlow": true, "typeVersion": 4.2}, {"id": "60325192-4730-4410-ae33-9127ff8cc5f7", "name": "Make file shareable publically (optional)", "type": "n8n-nodes-base.googleDrive", "position": [1360, 5220], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $json.documentId }}"}, "options": {}, "operation": "share", "permissionsUi": {"permissionsValues": {"role": "writer", "type": "anyone"}}}, "typeVersion": 3}, {"id": "6f254810-3ab8-4ec1-b964-8b399472acf3", "name": "Image URL", "type": "n8n-nodes-base.set", "notes": "Define Image URL", "position": [440, 5220], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "cc2c6af0-68d3-49eb-85fe-3288d2ed0f6b", "name": "url", "type": "string", "value": "https://picsum.photos/id/400/300/300"}]}, "includeOtherFields": true}, "notesInFlow": true, "typeVersion": 3.4}, {"id": "d33a913a-2d98-4922-ba8d-5d325b114572", "name": "Find & Copy Docx Template", "type": "n8n-nodes-base.googleDrive", "position": [660, 5220], "parameters": {"name": "Chosen filename", "fileId": {"__rl": true, "mode": "list", "value": "1RQAX2CszNqw79gZxeocEZU0-KquTq3RQc2-5Uv1mgd0", "cachedResultUrl": "https://docs.google.com/document/d/1RQAX2CszNqw79gZxeocEZU0-KquTq3RQc2-5Uv1mgd0/edit?usp=drivesdk", "cachedResultName": "Marketing Plan (template)"}, "options": {}, "operation": "copy"}, "notesInFlow": true, "typeVersion": 3}, {"id": "1f43d321-eddf-4008-99e2-9338cc85bad2", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [180, 5420], "parameters": {"color": 3, "width": 415.45208033736463, "height": 105.04337297263078, "content": "**REQUIRED**\nConnect to your database of image urls to input. Name the column `url` like in the `Image URL` node. This flow works with an image URL only, not a physical image file"}, "typeVersion": 1}, {"id": "0e1bb319-8429-4bde-88a3-9fd69df7c986", "name": "Own datasource", "type": "n8n-nodes-base.noOp", "position": [240, 5220], "parameters": {}, "typeVersion": 1}, {"id": "d534cc1f-e651-4c06-860b-ce3d3c648964", "name": "Sticky Note43", "type": "n8n-nodes-base.stickyNote", "position": [620, 5420], "parameters": {"color": 2, "width": 415.45208033736463, "height": 222.*************, "content": "**OPTIONAL**\nIf you want to create multiple documents with multiple images then create a template in your GDrive folder with a placeholder image that you want to replace. This template will be copied each time you run the flow, and the ID of that new document will be passed to `find image ID in Docx` to find the relevant image to replace with your image url. If you are just doing this for a single document then remove the `find and copy docx template` node. If you do this step, follow the [n8n guide](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googledrive/) on how to connect to your GDrive account."}, "typeVersion": 1}, {"id": "b6a22eb4-0b13-4eb5-be40-ed2dfedf99b5", "name": "<PERSON><PERSON> Note44", "type": "n8n-nodes-base.stickyNote", "position": [620, 5120], "parameters": {"color": 7, "width": 169.**************, "height": 278.*************, "content": "**Optional - create and copy a template document in your GDrive folder**"}, "typeVersion": 1}, {"id": "25d2a7c0-cef7-4aaf-9bb8-fe9c83d73731", "name": "Sticky Note45", "type": "n8n-nodes-base.stickyNote", "position": [853, 5120], "parameters": {"color": 7, "width": 435.**************, "height": 278.*************, "content": "**Sends request to Google API to retrieve document details (including image ID) in the template document**"}, "typeVersion": 1}, {"id": "f8dcb709-5505-4c63-afe9-83a0dfb608e9", "name": "Replace Image in Docx", "type": "n8n-nodes-base.httpRequest", "position": [1100, 5220], "parameters": {"url": "=https://docs.googleapis.com/v1/documents/{{$json.documentId}}:batchUpdate", "method": "POST", "options": {}, "jsonBody": "={\n  \"requests\": [\n    {\n      \"replaceImage\": {\n        \"imageObjectId\": {{ $json.body.content[1].paragraph.elements[0].inlineObjectElement.inlineObjectId }},\n        \"uri\": \"{{ $('Image URL').item.json.url }}\",\n        \"imageReplaceMethod\": \"CENTER_CROP\"\n      }\n    }\n  ]\n} ", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDocsOAuth2Api"}, "notesInFlow": true, "typeVersion": 4.2}, {"id": "f800131b-e8d3-4741-8e8d-ad208e53ebe7", "name": "Sticky Note46", "type": "n8n-nodes-base.stickyNote", "position": [1060, 5420], "parameters": {"color": 3, "width": 297.15093794343295, "height": 373.0766632058715, "content": "**REQUIRED**\n- Update the Google Docs OAuth 2 API credentials [with your own](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googledocs/)\n- Ensure Document ID is passing correctly to `Find Image ID in Docx` if you removed the previous step\n- Ensure the `imageObjectId` in the `Replace Image in Docx` node is correct, if you only have a single image in the document it should use the `\"{{ $json.body.content[1].paragraph.elements[0].inlineObjectElement.inlineObjectId }}\"` returned from the previous node. If you have more than one image then id will be nested in the `\"body.content[1].paragraph.positionedObjectIds[0]\"` object from the previous node "}, "typeVersion": 1}, {"id": "0cdad7ae-e407-4c21-b454-8b2824e2b6d4", "name": "Sticky Note47", "type": "n8n-nodes-base.stickyNote", "position": [1380, 5420], "parameters": {"color": 2, "width": 747.*************, "height": 222.*************, "content": "**OPTIONAL**\n- Make the file publically shareable (anyone can view/edit)\n- Download the file as a Docx\n- Download the file as a PDF\n\n\nAll of the above requires authenticating with your GDrive account, ensuring the `documentID` is pulling correctly from previous nodes and choosing a `filename` if relevant"}, "typeVersion": 1}, {"id": "fa6a214e-b6c5-403a-884a-d915f5a1362f", "name": "Download File - Docx", "type": "n8n-nodes-base.googleDrive", "position": [1580, 5220], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $json.documentId }}"}, "options": {}, "operation": "download"}, "notesInFlow": true, "typeVersion": 3}, {"id": "399e08ae-864d-4ffa-bc18-d82e03e30f7c", "name": "Download File - PDF", "type": "n8n-nodes-base.googleDrive", "position": [1800, 5220], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $json.documentId }}"}, "options": {"fileName": "=filename - PDF", "googleFileConversion": {"conversion": {"docsToFormat": "application/pdf"}}}, "operation": "download"}, "notesInFlow": true, "typeVersion": 3}, {"id": "0855b49a-dca3-4da8-8e22-4294523798d7", "name": "Sticky Note48", "type": "n8n-nodes-base.stickyNote", "position": [1320, 5120], "parameters": {"color": 7, "width": 683.4764182113373, "height": 278.*************, "content": "**Optional - make the file shareable, and download in docx and PDF format**"}, "typeVersion": 1}, {"id": "d4a2e23d-9c14-4b0c-8fd3-99a981d2f39b", "name": "Sticky Note49", "type": "n8n-nodes-base.stickyNote", "position": [-800, 4960], "parameters": {"color": 4, "width": 773.6179704580734, "height": 875.8289847608302, "content": "## Replace Images in Google Docs Documents and Download as PDF/Docx\n\n## Use Case\nAutomate image replacement in Google Docs:\n- You need to update document images dynamically\n- You want to create multiple versions of a template with different images\n- You need to batch process document images from a URL database\n- You want to generate shareable documents with custom images\n\n## What this Workflow Does\nThe workflow automates image replacement in Google Docs:\n- Accepts image URLs from your database\n- Finds and replaces images in template documents\n- Creates new document copies with updated images\n- Optionally converts to PDF and makes documents shareable\n\n## Setup\n1. Connect your image URL database (column name must be \"url\")\n2. Set up [Google Docs OAuth 2 API credentials](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googledocs/)\n3. Optional: Create a template document in Google Drive with placeholder images\n4. Optional: Configure [Google Drive authentication](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googledrive/) for additional features\n\n## How to Adjust it to Your Needs\n- Remove template copying for single document processing\n- Adjust image ID selection for documents with multiple images\n- Configure sharing settings and download formats\n- Customize file naming and storage location\n\n\nMade by <PERSON> @ [automake.io](https://automake.io)"}, "typeVersion": 1}], "pinData": {}, "connections": {"Image URL": {"main": [[{"node": "Find & Copy Docx Template", "type": "main", "index": 0}]]}, "Own datasource": {"main": [[{"node": "Image URL", "type": "main", "index": 0}]]}, "Download File - Docx": {"main": [[{"node": "Download File - PDF", "type": "main", "index": 0}]]}, "Find Image ID in Docx": {"main": [[{"node": "Replace Image in Docx", "type": "main", "index": 0}]]}, "Replace Image in Docx": {"main": [[{"node": "Make file shareable publically (optional)", "type": "main", "index": 0}]]}, "Find & Copy Docx Template": {"main": [[{"node": "Find Image ID in Docx", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Own datasource", "type": "main", "index": 0}]]}, "Make file shareable publically (optional)": {"main": [[{"node": "Download File - Docx", "type": "main", "index": 0}]]}}}