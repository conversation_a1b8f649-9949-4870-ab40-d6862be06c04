{"id": "1V1gcK6vyczRqdZC", "meta": {"instanceId": "d868e3d040e7bda892c81b17cf446053ea25d2556fcef89cbe19dd61a3e876e9", "templateCredsSetupCompleted": true}, "name": "Printify Automation - Update Title and Description - AlexK1919", "tags": [{"id": "NBHymnfw5EIluMXO", "name": "Printify", "createdAt": "2024-11-27T18:26:34.584Z", "updatedAt": "2024-11-27T18:26:34.584Z"}, {"id": "QsH2EXuw2e7YCv0K", "name": "OpenAI", "createdAt": "2024-11-15T04:05:20.872Z", "updatedAt": "2024-11-15T04:05:20.872Z"}], "nodes": [{"id": "313b16dc-2583-42f3-a0f7-487e75d7a7ec", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-700, -100], "parameters": {}, "typeVersion": 1}, {"id": "fd59c09f-64cd-4e8a-80b1-d1abd9a52a5c", "name": "Printify - Get Shops", "type": "n8n-nodes-base.httpRequest", "position": [-60, -100], "parameters": {"url": "https://api.printify.com/v1/shops.json", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "vBaDp4RbmXnEx2rj", "name": "AlexK1919 Printify Head<PERSON>"}}, "typeVersion": 4.2}, {"id": "8fa6a094-02f5-46c4-90d4-c17de302b004", "name": "Printify - Get Products", "type": "n8n-nodes-base.httpRequest", "position": [140, -100], "parameters": {"url": "=https://api.printify.com/v1/shops/{{ $json.id }}/products.json", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "vBaDp4RbmXnEx2rj", "name": "AlexK1919 Printify Head<PERSON>"}}, "typeVersion": 4.2}, {"id": "00cdd85f-75ef-480b-aa58-d732b764337f", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [340, -100], "parameters": {"options": {}, "fieldToSplitOut": "data"}, "typeVersion": 1}, {"id": "564b02c3-38ce-411d-b1ca-e1a4b75310e4", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [540, -100], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "95ea265f-7043-46ef-8513-67cf9407bda5", "name": "Split - id, title, desc", "type": "n8n-nodes-base.splitOut", "position": [740, -100], "parameters": {"include": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options": {}, "fieldToSplitOut": "id", "fieldsToInclude": "title, description"}, "typeVersion": 1}, {"id": "93ec8766-6ab3-4331-91fd-9aad24b587e9", "name": "Calculator", "type": "@n8n/n8n-nodes-langchain.toolCalculator", "position": [2240, 80], "parameters": {}, "typeVersion": 1}, {"id": "a9adf75e-bce3-4e0a-af44-e5e23b16b2f6", "name": "Wikipedia", "type": "@n8n/n8n-nodes-langchain.toolWikipedia", "position": [2120, 80], "parameters": {}, "typeVersion": 1}, {"id": "36272d91-a100-498d-8f24-2e93f2a1bb5b", "name": "Printify - Update Product", "type": "n8n-nodes-base.httpRequest", "position": [2080, 500], "parameters": {"url": "=https://api.printify.com/v1/shops/{{ $json.id }}/products/{{ $('Google Sheets Trigger').item.json.product_id }}.json", "method": "PUT", "options": {}, "sendBody": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "=title", "value": "={{ $('Google Sheets Trigger').item.json.product_title }}"}, {"name": "description", "value": "={{ $('Google Sheets Trigger').item.json.product_desc }}"}]}, "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "vBaDp4RbmXnEx2rj", "name": "AlexK1919 Printify Head<PERSON>"}}, "typeVersion": 4.2}, {"id": "63f9c4f5-cf6a-444a-af47-ea0e45b506ac", "name": "Brand Guidelines + Custom Instructions", "type": "n8n-nodes-base.set", "position": [-420, -100], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "887815dd-21d5-41d7-b429-5f4361cf93b3", "name": "brand_name", "type": "string", "value": "AlexK1919"}, {"id": "cbaa3dc0-825c-44e4-8a27-061f49daf249", "name": "brand_tone", "type": "string", "value": "informal, instructional, trustoworthy"}, {"id": "0bd1358e-4586-407e-848e-8257923ed1b8", "name": "custom_instructions", "type": "string", "value": "re-write for the coming Christmas season"}]}}, "typeVersion": 3.4}, {"id": "8e99d571-753c-4aca-bdd5-0a8dfb6f5aca", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [-1000, -340], "parameters": {"color": 6, "width": 250, "height": 1066.0405523297766, "content": "# AlexK1919 \n![<PERSON>](https://media.licdn.com/dms/image/v2/D5603AQFOYMkqCPl6Sw/profile-displayphoto-shrink_400_400/profile-displayphoto-shrink_400_400/0/1718309808352?e=1736985600&v=beta&t=pQKm7lQfUU1ytuC2Gq1PRxNY-XmROFWbo-BjzUPxWOs)\n\n#### I’m <PERSON>, an AI-Native Workflow Automation Architect Building Solutions to Optimize your Personal and Professional Life.\n\n\n### About Me\nhttps://beacons.ai/alexk1919\n\n### Products Used \n[OpenAI](https://openai.com)\n[Printify](https://printify.com/)\n\n[Google Sheets Template for this Workflow](https://docs.google.com/spreadsheets/d/12Y7M5YSUW1e8UUOjupzctOrEtgMK-0Wb32zcVpNcfjk/edit?gid=0#gid=0)"}, "typeVersion": 1}, {"id": "59ad5fd5-8960-421e-9d8b-1da34dd54b92", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [-120, -340], "parameters": {"color": 4, "width": 1020.0792140594992, "height": 1064.4036342575048, "content": "# ![Printify](https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTb2gV-cjThU_5xJRxtjDx7Uh9xXCN5Uo1GGA&s)\nYou can swap out the API calls to similar services like Printful, Vistaprint, etc."}, "typeVersion": 1}, {"id": "25faf7eb-c83d-4740-b3a9-762b652f67d6", "name": "Google Sheets Trigger", "type": "n8n-nodes-base.googleSheetsTrigger", "position": [1480, 500], "parameters": {"event": "rowUpdate", "options": {"columnsToWatch": ["upload"]}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1A6Phr6QwnMltm1_O6dVGAzmSPlOwuwp7RbCiLSvd9l0/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1A6Phr6QwnMltm1_O6dVGAzmSPlOwuwp7RbCiLSvd9l0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1A6Phr6QwnMltm1_O6dVGAzmSPlOwuwp7RbCiLSvd9l0/edit?usp=drivesdk", "cachedResultName": "Printify - AlexK1919"}}, "credentials": {"googleSheetsTriggerOAuth2Api": {"id": "qrn9YcLkT3BSPIPA", "name": "AlexK191 Google Sheets Trigger account"}}, "typeVersion": 1}, {"id": "c1f3a7f5-ddc5-4d3d-a5ae-8663c31e7376", "name": "Printify - Get Shops1", "type": "n8n-nodes-base.httpRequest", "position": [1880, 500], "parameters": {"url": "https://api.printify.com/v1/shops.json", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "vBaDp4RbmXnEx2rj", "name": "AlexK1919 Printify Head<PERSON>"}}, "typeVersion": 4.2}, {"id": "b38cdb40-9784-43d6-b1d2-4d30340d2c1f", "name": "GS - Add Product Option", "type": "n8n-nodes-base.googleSheets", "position": [1880, -100], "parameters": {"columns": {"value": {"xid": "={{ Math.random().toString(36).substr(2, 12) }}", "date": "={{ new Date().toISOString().split('T')[0] }}", "time": "={{ new Date().toLocaleTimeString('en-US', { hour12: false }) }}", "status": "Product Processing"}, "schema": [{"id": "xid", "type": "string", "display": true, "required": false, "displayName": "xid", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "status", "type": "string", "display": true, "required": false, "displayName": "status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date", "type": "string", "display": true, "required": false, "displayName": "date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "time", "type": "string", "display": true, "required": false, "displayName": "time", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "product_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "product_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "original_title", "type": "string", "display": true, "removed": false, "required": false, "displayName": "original_title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "product_title", "type": "string", "display": true, "removed": false, "required": false, "displayName": "product_title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "original_desc", "type": "string", "display": true, "removed": false, "required": false, "displayName": "original_desc", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "product_desc", "type": "string", "display": true, "removed": false, "required": false, "displayName": "product_desc", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "product_url", "type": "string", "display": true, "removed": false, "required": false, "displayName": "product_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "image_url", "type": "string", "display": true, "removed": false, "required": false, "displayName": "image_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "video_url", "type": "string", "display": true, "removed": false, "required": false, "displayName": "video_url", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": []}, "options": {"useAppend": true}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Ql9TGAzZCSdSqrHvkZLcsBPoNMAjNpPVsELkumP2heM/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1A6Phr6QwnMltm1_O6dVGAzmSPlOwuwp7RbCiLSvd9l0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1A6Phr6QwnMltm1_O6dVGAzmSPlOwuwp7RbCiLSvd9l0/edit?usp=drivesdk", "cachedResultName": "Printify - AlexK1919"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "IpY8N9VFCXJLC1hv", "name": "AlexK1919 Google Sheets account"}}, "typeVersion": 4.3}, {"id": "da735862-b67d-443e-8f45-e425ef518145", "name": "Update Product Option", "type": "n8n-nodes-base.googleSheets", "position": [2440, -100], "parameters": {"columns": {"value": {"xid": "={{ $('GS - Add Product Option').item.json.xid }}", "status": "Option added", "keyword": "={{ $json.message.content.keyword }}", "product_id": "={{ $('Split - id, title, desc').item.json.id }}", "product_desc": "={{ $json.message.content.description }}", "original_desc": "={{ $('Split - id, title, desc').item.json.description }}", "product_title": "={{ $json.message.content.title }}", "original_title": "={{ $('Split - id, title, desc').item.json.title }}"}, "schema": [{"id": "xid", "type": "string", "display": true, "removed": false, "required": false, "displayName": "xid", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "status", "type": "string", "display": true, "required": false, "displayName": "status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "upload", "type": "string", "display": true, "removed": false, "required": false, "displayName": "upload", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date", "type": "string", "display": true, "required": false, "displayName": "date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "time", "type": "string", "display": true, "required": false, "displayName": "time", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "product_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "product_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "keyword", "type": "string", "display": true, "removed": false, "required": false, "displayName": "keyword", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "original_title", "type": "string", "display": true, "removed": false, "required": false, "displayName": "original_title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "product_title", "type": "string", "display": true, "required": false, "displayName": "product_title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "original_desc", "type": "string", "display": true, "removed": false, "required": false, "displayName": "original_desc", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "product_desc", "type": "string", "display": true, "required": false, "displayName": "product_desc", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "product_url", "type": "string", "display": true, "required": false, "displayName": "product_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "image_url", "type": "string", "display": true, "required": false, "displayName": "image_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "video_url", "type": "string", "display": true, "required": false, "displayName": "video_url", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["xid"]}, "options": {}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1A6Phr6QwnMltm1_O6dVGAzmSPlOwuwp7RbCiLSvd9l0/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1A6Phr6QwnMltm1_O6dVGAzmSPlOwuwp7RbCiLSvd9l0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1A6Phr6QwnMltm1_O6dVGAzmSPlOwuwp7RbCiLSvd9l0/edit?usp=drivesdk", "cachedResultName": "Printify - AlexK1919"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "IpY8N9VFCXJLC1hv", "name": "AlexK1919 Google Sheets account"}}, "typeVersion": 4.5}, {"id": "b8eeb5b9-e048-4844-8712-b9fed848c041", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [927.*************, -340], "parameters": {"color": 5, "width": 454.**************, "height": 1064.*************, "content": "# Set the Number of Options you'd like for the Title and Description"}, "typeVersion": 1}, {"id": "0e705827-9fc9-42d7-9c6a-7597de767acb", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1409, -340], "parameters": {"color": 4, "width": 1429.*************, "height": 692.*************, "content": "# Process Title and Description Options"}, "typeVersion": 1}, {"id": "c0a829b4-6902-4a8d-81a8-70fb1fdf4634", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [-560, -340], "parameters": {"color": 5, "width": 410, "height": 1067.***********, "content": "# Update your Brand Guidelines before running this workflow\nYou can also add custom instructions for the AI node."}, "typeVersion": 1}, {"id": "6c50977f-6245-4d57-9cde-8ed8a572af21", "name": "If1", "type": "n8n-nodes-base.if", "position": [1680, -100], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "22bf0855-c742-4a72-99c9-5ed72a96969a", "operator": {"type": "number", "operation": "equals"}, "leftValue": "={{ $json.result }}", "rightValue": 0}]}}, "typeVersion": 2.2}, {"id": "82e2812b-59e6-4ac7-9238-7ee44052843b", "name": "Number of Options", "type": "n8n-nodes-base.set", "position": [1100, -100], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "e65d9a41-d8a0-40b8-82e6-7f4dd90f0aa7", "name": "number_of_options", "type": "string", "value": "3"}]}}, "typeVersion": 3.4}, {"id": "0476bdb9-6979-41a2-bbe2-63b41ea5ce80", "name": "Calculate Options", "type": "n8n-nodes-base.code", "position": [1480, -100], "parameters": {"mode": "runOnceForEachItem", "jsCode": "// Get the input data from the previous node\nconst inputData = $json[\"number_of_options\"]; // Fetch the \"number_of_options\" field\n\n// Convert the input to an integer\nconst initialValue = parseInt(inputData, 10);\n\n// Add 1 to retain the initial value and calculate the new value\nconst numberOfOptions = initialValue + 1;\nconst result = numberOfOptions - 1;\n\n// Return both values\nreturn {\n  number_of_options: numberOfOptions,\n  result,\n};\n"}, "typeVersion": 2}, {"id": "d0e57d93-26f3-43c2-8663-5ef22706fd60", "name": "Remember Options", "type": "n8n-nodes-base.set", "position": [2680, 40], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "e47b9073-6b83-4444-9fde-3a70326fde1f", "name": "number_of_options", "type": "number", "value": "={{ $('Calculate Options').item.json.result - 1 }}"}]}}, "typeVersion": 3.4}, {"id": "e6ce46c9-0339-449f-8f38-c6fbe26a7a96", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1409.6877789299706, 380], "parameters": {"color": 4, "width": 1429.*************, "height": 342.36777743061157, "content": "# Update Title and Description"}, "typeVersion": 1}, {"id": "14233023-2e76-4cd4-a6fa-e8f67cac3e59", "name": "Generate Title and Desc", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [2080, -100], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "messages": {"values": [{"content": "=Write an engaging product title and description for this product: \nTitle: {{ $('Split - id, title, desc').item.json.title }}\nDescription: {{ $('Split - id, title, desc').item.json.description }}\n\nDefine a keyword for this product and use it to write the new Title and Description.\n\nThis product will be listed via Printify and posted across various sales channels such as Shopfiy, Etsy, Amazon, and TikTok Shops. This product will be promoted across social media channels."}, {"role": "assistant", "content": "Be witty. Humanize the content. No emojis."}, {"role": "system", "content": "You are an ecommerce master and excel at creating content for products."}, {"role": "assistant", "content": "=Brand Guidelines:\nBrand Name: {{ $('Brand Guidelines + Custom Instructions').item.json.brand_name }}\nBrand Tone: {{ $('Brand Guidelines + Custom Instructions').item.json.brand_tone }}"}, {"role": "system", "content": "={{ $('Brand Guidelines + Custom Instructions').item.json.custom_instructions }}"}, {"role": "system", "content": "Output:\nKeyword\nTitle\nDescription"}]}, "jsonOutput": true}, "credentials": {"openAiApi": {"id": "ysxujEYFiY5ozRTS", "name": "AlexK OpenAi Key"}}, "typeVersion": 1.3}, {"id": "41391fd2-d0b9-436f-a44b-29bd1db9bc72", "name": "If", "type": "n8n-nodes-base.if", "position": [1680, 500], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "d9c78fa8-c2ba-4c08-b5d2-848112caa1cc", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.upload }}", "rightValue": "yes"}]}}, "typeVersion": 2.2}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "62c1c130-55a2-4a4c-8695-8b59a626f1fe", "connections": {"If": {"main": [[{"node": "Printify - Get Shops1", "type": "main", "index": 0}]]}, "If1": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}], [{"node": "GS - Add Product Option", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Wikipedia": {"ai_tool": [[{"node": "Generate Title and Desc", "type": "ai_tool", "index": 0}]]}, "Calculator": {"ai_tool": [[{"node": "Generate Title and Desc", "type": "ai_tool", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Split - id, title, desc", "type": "main", "index": 0}]]}, "Remember Options": {"main": [[{"node": "Calculate Options", "type": "main", "index": 0}]]}, "Calculate Options": {"main": [[{"node": "If1", "type": "main", "index": 0}]]}, "Number of Options": {"main": [[{"node": "Calculate Options", "type": "main", "index": 0}]]}, "Printify - Get Shops": {"main": [[{"node": "Printify - Get Products", "type": "main", "index": 0}]]}, "Google Sheets Trigger": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Printify - Get Shops1": {"main": [[{"node": "Printify - Update Product", "type": "main", "index": 0}]]}, "Update Product Option": {"main": [[{"node": "Remember Options", "type": "main", "index": 0}]]}, "GS - Add Product Option": {"main": [[{"node": "Generate Title and Desc", "type": "main", "index": 0}]]}, "Generate Title and Desc": {"main": [[{"node": "Update Product Option", "type": "main", "index": 0}]]}, "Printify - Get Products": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Split - id, title, desc": {"main": [[{"node": "Number of Options", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Brand Guidelines + Custom Instructions", "type": "main", "index": 0}]]}, "Brand Guidelines + Custom Instructions": {"main": [[{"node": "Printify - Get Shops", "type": "main", "index": 0}]]}}}