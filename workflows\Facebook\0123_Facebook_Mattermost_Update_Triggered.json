{"id": "131", "name": "Receive a Mattermost message when a user updates their profile on Facebook", "nodes": [{"name": "Facebook Trigger", "type": "n8n-nodes-base.facebookTrigger", "position": [590, 260], "webhookId": "14ba2eea-04a1-4659-b83e-0090ba480452", "parameters": {"appId": "", "options": {"includeValues": true}}, "credentials": {"facebookGraphAppApi": "facebook"}, "typeVersion": 1}, {"name": "Mattermost", "type": "n8n-nodes-base.mattermost", "position": [790, 260], "parameters": {"message": "=The user with uid {{$node[\"Facebook Trigger\"].json[\"uid\"]}} changed their {{$node[\"Facebook Trigger\"].json[\"changes\"][0][\"field\"]}} to {{$node[\"Facebook Trigger\"].json[\"changes\"][0][\"value\"][\"page\"]}}.", "channelId": "13fx8838gtbj3d41a6a7c1w7fe", "attachments": [], "otherOptions": {}}, "credentials": {"mattermostApi": "mattermost"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Facebook Trigger": {"main": [[{"node": "Mattermost", "type": "main", "index": 0}]]}}}