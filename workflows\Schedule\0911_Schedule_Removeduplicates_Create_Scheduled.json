{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "154458b0-dde3-4224-9fa8-d38a025aa0d3", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-640, -140], "parameters": {"rule": {"interval": [{"field": "hours"}]}}, "typeVersion": 1.2}, {"id": "0fc88546-50ef-4183-8fb2-dcea939f3bcf", "name": "Get Recent Messages", "type": "n8n-nodes-base.microsoftOutlook", "position": [-440, -140], "webhookId": "48619a9a-d7a5-47af-983d-146e377d8767", "parameters": {"fields": ["body", "categories", "conversationId", "from", "hasAttachments", "internetMessageId", "sender", "subject", "toRecipients", "receivedDateTime", "webLink"], "output": "fields", "options": {}, "filtersUI": {"values": {"filters": {"receivedAfter": "={{ $now.minus({ \"hour\": 1 }).toISO() }}"}}}, "operation": "getAll"}, "credentials": {"microsoftOutlookOAuth2Api": {"id": "EWg6sbhPKcM5y3Mr", "name": "Microsoft Outlook account"}}, "typeVersion": 2}, {"id": "d056be7e-43ed-4fea-8aef-36579c656633", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [280, 40], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "e4b6fd9d-2506-45bf-bd80-a81a2c04306b", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [480, 40], "parameters": {"schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"labels\": {\n      \"type\": \"array\",\n      \"items\": { \"type\": \"string\" }\n    },\n    \"priority\": { \"type\": \"number\" },\n    \"summary\": { \"type\": \"string\" },\n    \"description\": { \"type\": \"string\" }\n  }\n}"}, "typeVersion": 1.2}, {"id": "3cef25fc-2581-4556-bf54-7704815d98b3", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [0, -340], "parameters": {"color": 7, "width": 700, "height": 540, "content": "## 2. Automate Generation and Triaging of Ticket\n[Read more about the Basic LLM node](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.chainllm)\n\nNew tickets always need to be properly labelled and prioritised but it's not always possible to get to update all incoming tickets if you're light on hands. Using an AI is a great use-case for triaging of tickets as its contextual understanding helps automates this step."}, "typeVersion": 1}, {"id": "d6ba8c9b-3e39-442f-8b79-cafe11c15a18", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.markdown", "position": [100, -140], "parameters": {"html": "={{ $json.body.content }}", "options": {}}, "typeVersion": 1}, {"id": "fb7c6d7c-df30-43de-8f37-9e394a8ad7aa", "name": "Create Issue", "type": "n8n-nodes-base.jira", "position": [900, -140], "parameters": {"project": {"__rl": true, "mode": "id", "value": "10000"}, "summary": "={{ $json.output.summary }}", "issueType": {"__rl": true, "mode": "id", "value": "10000"}, "additionalFields": {"labels": "={{ $json.output.labels }}", "priority": {"__rl": true, "mode": "id", "value": "={{ $json.output.priority }}"}, "description": "={{ $json.output.description }}"}}, "credentials": {"jiraSoftwareCloudApi": {"id": "IH5V74q6PusewNjD", "name": "Jira SW Cloud account"}}, "typeVersion": 1}, {"id": "9e26f402-36da-40e1-a736-db4fe16de54a", "name": "<PERSON> as Seen", "type": "n8n-nodes-base.removeDuplicates", "position": [-240, -140], "parameters": {"options": {}, "operation": "removeItemsSeenInPreviousExecutions", "dedupeValue": "={{ $json.id }}"}, "typeVersion": 2}, {"id": "b5f49877-e494-4712-a937-1f348198700e", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-740, -340], "parameters": {"color": 7, "width": 720, "height": 540, "content": "## 1. Watch Outlook Inbox for Support Emails\n[Learn more about the Outlook node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.microsoftoutlook/)\n\n**This template assumes a shared inbox specifically for support tickets!** If you have a general inbox, you may need to classify and filter each message which might become costly. The \"remove duplicates\" node (ie. \"Mark as seen\") ensures we only process each email exactly once."}, "typeVersion": 1}, {"id": "b9d08834-14ad-4cdf-bc20-411033eee5b7", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [720, -340], "parameters": {"color": 7, "width": 460, "height": 440, "content": "## 3. Create Issue in JIRA\n[Read more about the JIRA node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.jira/)\n\nThis is only a simple example to create an issue in JIRA but easily extendable to add much more!"}, "typeVersion": 1}, {"id": "e6942a39-1893-44cf-a846-c6b4d9c37e92", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-1160, -720], "parameters": {"width": 380, "height": 940, "content": "## Try It Out!\n### This n8n template watches an outlook shared inbox for support messages and creates an equivalent issue item in JIRA.\n\n### How it works\n* A scheduled trigger fetches recent Outlook messages from an shared inbox which collects support requests.\n* These support requests are filtered to ensure they are only processed once and their HTML body is converted to markdown for easier parsing.\n* Each support request is then triaged via an AI Agent which adds appropriate labels, assesses priority and summarises a title and description of the original request.\n* Finally, the AI generated values are used to create an issue in JIRA to be actioned.\n\n### How to use\n* Ensure the messages fetched are solely support requests otherwise you'll need to classify messages before processing them.\n* Specify the labels and priorities to use in the system prompt of the AI agent.\n\n### Requirements\n* Outlook for incoming support\n* OpenAI for LLM\n* JIRA for issue management\n\n### Customising this workflow\n* Consider automating more steps after the issue is created such as attempting issue resolution or capacity planning.\n\n\n### Need Help?\nJoin the [Discord](https://discord.com/invite/XPKeKXeB7d) or ask in the [Forum](https://community.n8n.io/)!\n\nHappy Hacking!"}, "typeVersion": 1}, {"id": "71a906b2-7b01-43a8-aa82-7d9810d95e23", "name": "Generate Issue From Support Request", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [300, -140], "parameters": {"text": "=Reported by {{ $json.from.emailAddress.name }} <{{ $json.from.emailAddress.address }}>\nReported at: {{ $now.toISO() }}\nSummary: {{ $json.subject }}\nDescription:\n{{ $json.data.replaceAll('\\n', ' ') }}", "messages": {"messageValues": [{"message": "=Your are JIRA triage assistant who's task is to\n1) classify and label the given issue.\n2) Prioritise the given issue.\n3) Rewrite the issue summary and description.\n\n## Labels\nUse one or more. Use words wrapped in \"[]\" (square brackets):\n* Technical\n* Account\n* Access\n* Billing\n* Product\n* Training\n* Feedback\n* Complaints\n* Security\n* Privacy\n\n## Priority\n* 1 - highest\n* 2 - high\n* 3 - medium\n* 4 - low\n* 5 - lowest\n\n## Write Summary and Description\n* Remove emotional and anedotal phrases or information\n* Keep to the facts of the matter\n* Highlight what was attempted and is/was failing"}]}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.6}], "pinData": {}, "connections": {"Markdown": {"main": [[{"node": "Generate Issue From Support Request", "type": "main", "index": 0}]]}, "Mark as Seen": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Get Recent Messages", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Generate Issue From Support Request", "type": "ai_languageModel", "index": 0}]]}, "Get Recent Messages": {"main": [[{"node": "<PERSON> as Seen", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Generate Issue From Support Request", "type": "ai_outputParser", "index": 0}]]}, "Generate Issue From Support Request": {"main": [[{"node": "Create Issue", "type": "main", "index": 0}]]}}}