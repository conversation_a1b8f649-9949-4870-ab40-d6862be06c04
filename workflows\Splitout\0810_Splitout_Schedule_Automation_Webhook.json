{"meta": {"instanceId": "e634e668fe1fc93a75c4f2a7fc0dad807ca318b79654157eadb9578496acbc76"}, "nodes": [{"id": "754006f5-1a7e-4e29-9850-e38b1d0c0d09", "name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [360, 80], "parameters": {}, "typeVersion": 1}, {"id": "6b7b0d05-38cc-4c2d-8a71-874ff5ad29d9", "name": "Split Out Order ", "type": "n8n-nodes-base.splitOut", "position": [1080, 200], "parameters": {"options": {}, "fieldToSplitOut": "result"}, "typeVersion": 1}, {"id": "1494f1ff-f377-4d56-8da7-274f0c182588", "name": "Globals", "type": "n8n-nodes-base.set", "position": [600, 200], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "7411b768-9861-414c-aeaa-2743b3d61a3b", "name": "api-version", "type": "string", "value": "1.0"}, {"id": "6cf546c5-5737-4dbd-851b-17d68e0a3780", "name": "modifiedAfter", "type": "string", "value": ""}, {"id": "452efa28-2dc6-4ea3-a7a2-c35d100d0382", "name": "modifiedBefore", "type": "string", "value": ""}, {"id": "81c4dc54-86bf-4432-a23f-22c7ea831e74", "name": "cursor", "type": "string", "value": ""}, {"id": "fa31a552-0d2d-4eb3-8476-44024e1fdc81", "name": "fulfillmentStatus", "type": "string", "value": "PENDING"}, {"id": "489ff3e6-7bc3-4940-9312-e4ace8e1db9f", "name": "maxPage", "type": "number", "value": -1}]}}, "notesInFlow": true, "typeVersion": 3.4}, {"id": "01557e82-9f89-4030-af0f-6663ea945191", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [540, 80], "parameters": {"color": 4, "width": 150, "height": 80, "content": "## Edit this node 👇"}, "typeVersion": 1}, {"id": "9d9d361a-dd12-4c57-9f76-7c4738b5af1e", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [360, 340], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.2}, {"id": "a3e41614-ca4e-4730-a4ab-1e9933ef71d5", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [0, 0], "parameters": {"width": 320, "height": 660, "content": "## Squarespace Fulfillment Automation with n8n\nRetrieves all Squarespace Orders and mark them as fulfilled automatically Squarespace Commerce API\n\n### Setup\nOpen `Globals` node and update the values below 👇\n\n- **api-version** (string, required) – The current API version (see Squarespace Orders API documentation).\n- **modifiedAfter**={a-datetime} (string, conditional) – Fetch orders modified after a specific date (ISO 8601 format).\n- **modifiedBefore**={b-datetime} (string, conditional) – Fetch orders modified before a specific date (ISO 8601 format).\n- **cursor**={c} (string, conditional) – Used for pagination, cannot be combined with other filters.\n- **fulfillmentStatus**: PENDING, FULFILLED, or CANCELED.\n- **maxPage** – Set -1 to enables infinite pagination to fetch all available orders.\n\n"}, "typeVersion": 1}, {"id": "bc78dac5-3fa9-4b65-a5c3-2196ed53a81c", "name": "Query pending Orders", "type": "n8n-nodes-base.httpRequest", "position": [840, 200], "parameters": {"url": "=https://api.squarespace.com/{{ $json[\"api-version\"] }}/commerce/orders", "options": {"pagination": {"pagination": {"parameters": {"parameters": [{"name": "cursor", "value": "={{ $response.body.pagination.nextPageCursor }}"}]}, "maxRequests": "={{ $json.maxPage === -1 ? Infinity : $json.maxPage }}", "limitPagesFetched": true, "completeExpression": "={{ !$response.body.pagination.nextPageCursor }}", "paginationCompleteWhen": "other"}}}, "sendQuery": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "queryParameters": {"parameters": [{"name": "modifiedAfter", "value": "={{ $json.modifiedAfter }}"}, {"name": "=modifiedBefore", "value": "={{ $json.modifiedBefore }}"}, {"name": "cursor", "value": "={{ $json.cursor }}"}, {"name": "=fulfillmentStatus", "value": "={{ $json.fulfillmentStatus }}"}]}}, "credentials": {"oAuth2Api": {"id": "5eAFOixVzslPr99y", "name": "Squarespace OAuth 2.0"}, "httpHeaderAuth": {"id": "iiLmD473RYjGLbCA", "name": "Squarespace API key - Apps script"}}, "typeVersion": 4.2}, {"id": "a5723a03-41d1-49a9-9baa-c7482fdf82a3", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [1640, 200], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "7e656389-ca9c-4ff4-9db1-68f84a13e605", "name": "Fulfill Order", "type": "n8n-nodes-base.httpRequest", "position": [1940, 200], "parameters": {"url": "=https://api.squarespace.com/{{ $('Globals').item.json[\"api-version\"] }}/commerce/orders/{{ $('Filter Orders').item.json.id }}/fulfillments", "method": "POST", "options": {}, "jsonBody": "{\n  \"shouldSendNotification\": true\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "oAuth2Api"}, "credentials": {"oAuth2Api": {"id": "5eAFOixVzslPr99y", "name": "Squarespace OAuth 2.0"}, "httpHeaderAuth": {"id": "iiLmD473RYjGLbCA", "name": "Squarespace API key - Apps script"}}, "typeVersion": 4.2}, {"id": "b14b6db8-a027-41c2-a030-aa09b0003d73", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1880, 0], "parameters": {"width": 232, "height": 346, "content": "## Create fulfillment  👇\n\n[Fulfill an order](https://developers.squarespace.com/commerce-apis/fulfill-order)\n- `shouldSendNotification` to send notifications to customer"}, "typeVersion": 1}, {"id": "effd0876-0003-4e3f-ad61-cfe3d4391e67", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1280, -80], "parameters": {"height": 440, "content": "## Filtering orders for fulfillment 👇\nFilter the valid orders for programatically fulfillments\n\n- you exclusively sell digital downloads or digital gift cards\n- you use fulfillment services for all your products\n"}, "typeVersion": 1}, {"id": "ac8538f5-b93b-43c5-9100-33fe3f6cd70b", "name": "Filter Orders", "type": "n8n-nodes-base.filter", "position": [1340, 200], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "298103c1-a5b4-407e-aba6-bee37463422f", "operator": {"type": "number", "operation": "gt"}, "leftValue": "={{ (new Date().getTime() - new Date($json.createdOn).getTime()) / (1000 * 60 * 60) }}", "rightValue": 24}]}}, "typeVersion": 2.2}], "pinData": {}, "connections": {"Globals": {"main": [[{"node": "Query pending Orders", "type": "main", "index": 0}]]}, "Filter Orders": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Fulfill Order": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Fulfill Order", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Globals", "type": "main", "index": 0}]]}, "Split Out Order ": {"main": [[{"node": "Filter Orders", "type": "main", "index": 0}]]}, "Query pending Orders": {"main": [[{"node": "Split Out Order ", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Globals", "type": "main", "index": 0}]]}}}