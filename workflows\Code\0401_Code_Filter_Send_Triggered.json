{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "f3f7546a-8bb3-484c-b0a1-750a8d7d3a74", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [0, 520], "parameters": {"color": 7, "width": 1549, "height": 612, "content": "### Sub-workflow: Custom tool\nThis can be called by the agent above. It returns three different types of data from the Google Sheet, which can be used together for more complex queries without returning the whole sheet (which might be too big for GPT to handle)"}, "typeVersion": 1}, {"id": "a5afaa40-0b68-4d7c-8f78-5cb176fde81d", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [0, -40], "parameters": {"color": 7, "width": 1068, "height": 547, "content": "### Main workflow: AI agent using custom tool"}, "typeVersion": 1}, {"id": "2dc0ce2f-a09c-4804-9962-f542ec78eb87", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-160, 40], "parameters": {"width": 185.9375, "height": 183.85014518022527, "content": "## Try me out\n\nClick the 'Chat' button at the bottom and enter:\n\n_Which is our biggest customer?_"}, "typeVersion": 1}, {"id": "8fc97d52-1c18-47ed-ba6f-4c7c9ce8b7d4", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [480, 240], "parameters": {"color": 7, "width": 572, "height": 219, "content": "These tools all call the sub-workflow below"}, "typeVersion": 1}, {"id": "60cafb69-818f-49fe-b75e-e770304a6aa9", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [260, 740], "parameters": {"width": 179.99762227826224, "height": 226.64416053838073, "content": "Change the URL of the Google Sheet here"}, "typeVersion": 1}, {"id": "cd8d92fa-f7ef-47d8-a1a7-5b5aa6faaa96", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [120, 40], "webhookId": "7668b567-b983-479f-a3e0-ad945707ae6b", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "72ac3bdb-56dc-4634-a0ab-0b1c82e2b23d", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [160, 320], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "37e5bca6-4274-4714-a9e7-a8e4b7d732e5", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [340, 320], "parameters": {}, "typeVersion": 1.3}, {"id": "81bf7de8-c4c6-49ce-a2cf-4afca5dce7e2", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [80, 800], "parameters": {"workflowInputs": {"values": [{"name": "operation"}, {"name": "query"}]}}, "typeVersion": 1.1}, {"id": "24e5a49d-8f1f-4569-8072-c35f059ebd46", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [480, 40], "parameters": {"options": {}}, "typeVersion": 1.8}, {"id": "8cc35cb9-3371-4034-86b6-3d125d70d81b", "name": "List columns tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [540, 320], "parameters": {"name": "list_columns", "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "description": "List all column names in customer data\n\nCall this tool to find out what data is available for each customer. It should be called first at the beginning to understand which columns are available for querying.", "workflowInputs": {"value": {"query": "none", "operation": "column_names"}, "schema": [{"id": "query", "type": "string", "display": true, "removed": false, "required": false, "displayName": "query", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "operation", "type": "string", "display": true, "removed": false, "required": false, "displayName": "operation", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2}, {"id": "acf19978-dd84-4cfb-8034-eb9f14dd9c86", "name": "Get column values tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [720, 320], "parameters": {"name": "column_values", "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "description": "Get the specified column value for all customers\n\nUse this tool to find out which customers have a certain value for a given column. Returns an array of JSON objects, one per customer. Each JSON object includes the column being requested plus the row_number column. Input should be a single string representing the name of the column to fetch.\n", "workflowInputs": {"value": {"query": "none", "operation": "column_values"}, "schema": [{"id": "operation", "type": "string", "display": true, "removed": false, "required": false, "displayName": "operation", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "query", "type": "string", "display": true, "removed": false, "required": false, "displayName": "query", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["operation"], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2}, {"id": "acd8dad6-2ce5-4a53-9909-da7bc03cf0e2", "name": "Get customer tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [900, 320], "parameters": {"name": "get_customer", "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}", "cachedResultName": "={{ $workflow.id }}"}, "description": "Get all columns for a given customer\n\nThe input should be a stringified row number of the customer to fetch; only single string inputs are allowed. Returns a JSON object with all the column names and their values.", "workflowInputs": {"value": {"query": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('query', ``, 'string') }}", "operation": "row"}, "schema": [{"id": "operation", "type": "string", "display": true, "removed": false, "required": false, "displayName": "operation", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "query", "type": "string", "display": true, "removed": false, "required": false, "displayName": "query", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["operation"], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2}, {"id": "43988dff-2270-4d09-a091-8b719c281faf", "name": "Set Google Sheet URL", "type": "n8n-nodes-base.set", "position": [300, 800], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "a96650f2-3a0c-45cb-afdd-ef7ca90b21cc", "name": "sheetUrl", "type": "string", "value": "https://docs.google.com/spreadsheets/d/1GjFBV8HpraNWG_JyuaQAgTb3zUGguh0S_25nO0CMd8A/edit#gid=*********"}]}}, "typeVersion": 3.4}, {"id": "536225e9-e33c-4819-a2dc-994d8f7c3f30", "name": "Get Google sheet contents", "type": "n8n-nodes-base.googleSheets", "position": [520, 800], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "name", "value": "customer_data"}, "documentId": {"__rl": true, "mode": "url", "value": "={{ $json.sheetUrl }}"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "XHvC7jIRR8A2TlUl", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "a5668364-8824-44cc-8a0f-516906d8f821", "name": "Check operation", "type": "n8n-nodes-base.switch", "position": [740, 800], "parameters": {"rules": {"values": [{"outputKey": "Column Names", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "db07e0a3-0a1d-44bd-84a7-59a9442e63a6", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $('When Executed by Another Workflow').item.json.operation }}", "rightValue": "column_names"}]}, "renameOutput": true}, {"outputKey": "Column Values", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "96c82351-de4a-4299-903d-8b9b3a3bb931", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('When Executed by Another Workflow').item.json.operation }}", "rightValue": "column_values"}]}, "renameOutput": true}, {"outputKey": "Rows", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "fbc2afd0-361f-4181-94e3-4addc65a9086", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('When Executed by Another Workflow').item.json.operation }}", "rightValue": "row"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "46c3a8c4-ffaf-4373-b421-4d7ee65567b2", "name": "Get column names", "type": "n8n-nodes-base.set", "position": [1040, 620], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "36a7be13-e792-4c0a-9997-f61fe2a7b225", "name": "response", "type": "array", "value": "={{ Object.keys($json) }}"}]}}, "typeVersion": 3.4}, {"id": "190c48e8-4d4b-4e95-a694-49ab76b730da", "name": "Prepare column data", "type": "n8n-nodes-base.set", "position": [1040, 800], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "bcb75b32-3253-4a31-9771-4faaf12cc2ed", "name": "row_number", "type": "number", "value": "={{ $json.row_number }}"}]}}, "typeVersion": 3.4}, {"id": "9c65e5e6-a3ac-4ed7-8546-b7f2b09b4f96", "name": "Filter", "type": "n8n-nodes-base.filter", "position": [1040, 980], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "dbe89d36-e411-4765-8d4e-91a6425350ac", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.row_number.toString() }}", "rightValue": "={{ $('When Executed by Another Workflow').item.json.query }}"}]}}, "typeVersion": 2.2}, {"id": "39265f3e-a2ea-4174-952a-3665747ff856", "name": "Prepare output", "type": "n8n-nodes-base.code", "position": [1340, 800], "parameters": {"jsCode": "return {\n  'response': JSON.stringify($input.all().map(x => x.json))\n}"}, "executeOnce": true, "typeVersion": 2}], "pinData": {}, "connections": {"Filter": {"main": [[{"node": "Prepare output", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Check operation": {"main": [[{"node": "Get column names", "type": "main", "index": 0}], [{"node": "Prepare column data", "type": "main", "index": 0}], [{"node": "Filter", "type": "main", "index": 0}]]}, "Get column names": {"main": [[{"node": "Prepare output", "type": "main", "index": 0}]]}, "Get customer tool": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "List columns tool": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Prepare column data": {"main": [[{"node": "Prepare output", "type": "main", "index": 0}]]}, "Set Google Sheet URL": {"main": [[{"node": "Get Google sheet contents", "type": "main", "index": 0}]]}, "Get column values tool": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Get Google sheet contents": {"main": [[{"node": "Check operation", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Set Google Sheet URL", "type": "main", "index": 0}]]}}}