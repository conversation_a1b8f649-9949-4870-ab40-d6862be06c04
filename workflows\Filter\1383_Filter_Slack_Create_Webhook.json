{"meta": {"instanceId": "cb484ba7b742928a2048bf8829668bed5b5ad9787579adea888f05980292a4a7"}, "nodes": [{"id": "8920dc6e-b2fb-4446-8cb3-f3f6d626dcb3", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.linearTrigger", "position": [420, 360], "webhookId": "a02faf62-684f-44bb-809f-e962c9ede70d", "parameters": {"teamId": "7a330c36-4b39-4bf1-922e-b4ceeb91850a", "resources": ["issue"], "authentication": "oAuth2"}, "credentials": {"linearOAuth2Api": {"id": "02MqKUMdPxr9t3mX", "name": "Nik's <PERSON><PERSON>"}}, "typeVersion": 1}, {"id": "61214884-62f9-4a00-9517-e2d51b44d0ae", "name": "Only tickets that need to be classified", "type": "n8n-nodes-base.filter", "position": [1000, 360], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "bc3a756d-b2b6-407b-91c9-a1cd9da004e0", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $('Linear Trigger').item.json.data.description }}", "rightValue": "Add a description here"}, {"id": "f3d8d0fc-332d-41a6-aef8-1f221bf30c0e", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Linear Trigger').item.json.data.state.id }}", "rightValue": "6b9a8eec-82dc-453a-878b-50f4c98d3e53"}, {"id": "9cdb55b2-3ca9-43bd-84b0-ef025b59ce18", "operator": {"type": "number", "operation": "gt"}, "leftValue": "={{ $('Linear Trigger').item.json.data.labels.filter(label => label.id === 'f2b6e3e9-b42d-4106-821c-6a08dcb489a9').length }}", "rightValue": 0}]}}, "typeVersion": 2}, {"id": "da4d8e0c-895b-4a84-8319-438f971af403", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1000, 111.31510859283728], "parameters": {"color": 7, "height": 219.68489140716272, "content": "### When does this fire?\nIn our setup we have a general team in Linear where we post new tickets to. Additionally, the bug needs to have a certain label and the description needs to be filled. \nYou're of course free to adjust this to your needs\n👇"}, "typeVersion": 1}, {"id": "b7e3a328-96c4-4082-93a9-0cb331367190", "name": "Update team", "type": "n8n-nodes-base.linear", "position": [2160, 280], "parameters": {"issueId": "={{ $('Linear Trigger').item.json.data.id }}", "operation": "update", "updateFields": {"teamId": "={{ $json.teamId }}"}}, "credentials": {"linearApi": {"id": "oYIZvhmcNt5JWTCP", "name": "Nik's Linear Key"}}, "typeVersion": 1}, {"id": "858764ce-cd24-4399-88ce-cf69e676beaa", "name": "Get all linear teams", "type": "n8n-nodes-base.httpRequest", "position": [1300, 540], "parameters": {"url": "https://api.linear.app/graphql", "method": "POST", "options": {}, "sendBody": true, "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "query", "value": "{ teams { nodes { id name } } }"}]}, "nodeCredentialType": "linearOAuth2Api"}, "credentials": {"linearOAuth2Api": {"id": "02MqKUMdPxr9t3mX", "name": "Nik's <PERSON><PERSON>"}}, "typeVersion": 3}, {"id": "167f0c66-5bfb-4dd7-a345-81f4d62df2c4", "name": "Set team ID", "type": "n8n-nodes-base.set", "position": [2000, 280], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "a46c4476-b851-4112-ac72-e805308c5ab7", "name": "teamId", "type": "string", "value": "={{ $('Get all linear teams').first().json.data.teams.nodes.find(team => team.name === $json.message.content).id }}"}]}}, "typeVersion": 3.3}, {"id": "36363240-2b03-4af8-8987-0db95094403b", "name": "Set me up", "type": "n8n-nodes-base.set", "position": [700, 360], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "a56f24c8-0a28-4dd2-885a-cb6a081a5bf4", "name": "teams", "type": "string", "value": "- [Adore][Is responsible for every persona that is not Enterprise. This includes signup journeys, trials, n8n Cloud, the Canvas building experience and more, the nodes detail view (NDV), the nodes panel, the workflows list and the executions view] \n- [Payday][Is responsible for the Enterprise persona. This includes making sure n8n is performant, the enterprise features SSO, LDAP, SAML, Log streaming, environments, queue mode, version control, external storage. Additionally the team looks out for the execution logic in n8n and how branching works] \n- [Nodes][This team is responsible for everything that is related to a specific node in n8n] \n- [Other][This is a placeholder if you don't know to which team something belongs]"}, {"id": "d672cb59-72be-4fc8-9327-2623795f225d", "name": "slackChannel", "type": "string", "value": "#yourChannelName"}]}}, "typeVersion": 3.3}, {"id": "49f2a157-b037-46d9-a6d7-97f8a72ee093", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [581.3284642016245, 85.15358950105212], "parameters": {"color": 5, "width": 349.85308830334156, "height": 439.62604295396085, "content": "## Setup\n1. Add your Linear and OpenAi credentials\n2. Change the team in the `Linear Trigger` to match your needs\n3. Customize your teams and their areas of responsibility in the `Set me up` node. Please use the format `[Teamname][Description/Areas of responsibility]`. Also make sure that the teamnames match the names in Linear exactly.\n4. Change the Slack channel in the `Set me up` node to your Slack channel of choice."}, "typeVersion": 1}, {"id": "8cdb3d0d-4fd3-4ea2-957f-daf746934728", "name": "Check if AI was able to find a team", "type": "n8n-nodes-base.if", "position": [1780, 380], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "86bfb688-3ecc-4360-b83a-d706bb11c8f9", "operator": {"type": "string", "operation": "notEquals"}, "leftValue": "={{ $json.message.content }}", "rightValue": "Other"}]}}, "typeVersion": 2}, {"id": "a4cb20ca-658a-4b30-9185-5af9a32a7e20", "name": "Notify in Slack", "type": "n8n-nodes-base.slack", "position": [2000, 460], "parameters": {"text": "The AI was not able to identify a fitting team for a bug", "select": "channel", "channelId": {"__rl": true, "mode": "name", "value": "={{ $('Set me up').first().json.slackChannel }}"}, "otherOptions": {}}, "credentials": {"slackApi": {"id": "376", "name": "<PERSON><PERSON>"}}, "typeVersion": 2.1}, {"id": "393b2392-80be-4a68-9240-dc1065e0081a", "name": "Merge data", "type": "n8n-nodes-base.merge", "position": [1600, 380], "parameters": {"mode": "chooseBranch"}, "typeVersion": 2.1}, {"id": "f25da511-b255-4a53-ba4e-5765916e90be", "name": "OpenAI", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1220, 360], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4-32k-0314", "cachedResultName": "GPT-4-32K-0314"}, "options": {}, "messages": {"values": [{"role": "system", "content": "I need you to classify a bug ticket and tell me which team should work on it"}, {"role": "system", "content": "All possible teams will be described in the following format: [Teamname][Areas of responsibility] "}, {"role": "system", "content": "=The possible teams are the following:\n {{ $('Set me up').first().json.teams }}"}, {"role": "system", "content": "=This is the bug that we're trying to classify:\nTitle: {{ $('Linear Trigger').first().json.data.title }}\nDescription: {{ $('Linear Trigger').first().json.data.description }}"}, {"content": "Which team should work on this bug?"}, {"role": "system", "content": "Do not respond with anything else than the name of the team from the list you were given"}]}}, "credentials": {"openAiApi": {"id": "VQtv7frm7eLiEDnd", "name": "OpenAi account 7"}}, "typeVersion": 1}], "pinData": {"Linear Trigger": [{"url": "https://linear.app/n8n/issue/N8N-6945/cannot-scroll-the-canvas-after-duplicating-or-pausing-a-note", "data": {"id": "94a4b770-3c80-4099-9376-ffe951f633db", "url": "https://linear.app/n8n/issue/N8N-6945/cannot-scroll-the-canvas-after-duplicating-or-pausing-a-note", "team": {"id": "7a330c36-4b39-4bf1-922e-b4ceeb91850a", "key": "N8N", "name": "Engineering"}, "state": {"id": "6b9a8eec-82dc-453a-878b-50f4c98d3e53", "name": "Triage", "type": "triage", "color": "#FC7840"}, "title": "cannot scroll the canvas after duplicating or pausing a note", "labels": [{"id": "f2b6e3e9-b42d-4106-821c-6a08dcb489a9", "name": "type/bug", "color": "#eb5757"}], "number": 6945, "teamId": "7a330c36-4b39-4bf1-922e-b4ceeb91850a", "cycleId": null, "dueDate": null, "stateId": "6b9a8eec-82dc-453a-878b-50f4c98d3e53", "trashed": null, "botActor": {"name": "Unknown", "type": "<PERSON><PERSON><PERSON><PERSON>"}, "estimate": null, "labelIds": ["f2b6e3e9-b42d-4106-821c-6a08dcb489a9"], "parentId": null, "priority": 0, "createdAt": "2023-09-12T12:51:41.696Z", "creatorId": "49ae7598-ae5d-42e6-8a03-9f6038a0d37a", "projectId": null, "sortOrder": -154747, "startedAt": null, "triagedAt": null, "updatedAt": "2024-02-29T16:00:27.794Z", "archivedAt": null, "assigneeId": null, "boardOrder": 0, "canceledAt": null, "identifier": "N8N-6945", "completedAt": null, "description": "## Description\n\nAfter using the canvas for a while I always had issues where the scrolling would stop working. I finally found a way to reproduce the issue reliably.\n\n## Expected\n\nI would like to always be able to scroll the canvas using CMD + click\n\n## Actual\n\nSometimes when using the app the scrolling stops working and you have to refresh to get it back to work.\n\n## Steps or workflow to reproduce (with screenshots/recordings)\n\n**n8n version:** \\[Deployment type\\] \\[version\\]\n\n1. Add any nodes to the canvas\n2. Click either the Duplicate or Pause buttons that appear when hovering over a node\n3. Try scrolling using CMD/CTRL + Click. Scrolling should no longer work while it should still work\n\nCreated by <PERSON>", "snoozedById": null, "autoClosedAt": null, "slaStartedAt": null, "priorityLabel": "No priority", "slaBreachesAt": null, "subscriberIds": ["49ae7598-ae5d-42e6-8a03-9f6038a0d37a"], "autoArchivedAt": null, "snoozedUntilAt": null, "descriptionData": "{\"type\":\"doc\",\"content\":[{\"type\":\"heading\",\"attrs\":{\"level\":2,\"id\":\"d836020f-77f5-4ae0-9d6e-a69bd4567656\"},\"content\":[{\"type\":\"text\",\"text\":\"Description\"}]},{\"type\":\"paragraph\",\"content\":[{\"type\":\"text\",\"text\":\"After using the canvas for a while I always had issues where the scrolling would stop working. I finally found a way to reproduce the issue reliably.\"}]},{\"type\":\"heading\",\"attrs\":{\"level\":2,\"id\":\"4125614d-17b0-4530-bfc0-384d43bf80f9\"},\"content\":[{\"type\":\"text\",\"text\":\"Expected\"}]},{\"type\":\"paragraph\",\"content\":[{\"type\":\"text\",\"text\":\"I would like to always be able to scroll the canvas using CMD + click\"}]},{\"type\":\"heading\",\"attrs\":{\"level\":2,\"id\":\"3e8caaae-c152-46c1-a604-f0f9c75fb8c9\"},\"content\":[{\"type\":\"text\",\"text\":\"Actual\"}]},{\"type\":\"paragraph\",\"content\":[{\"type\":\"text\",\"text\":\"Sometimes when using the app the scrolling stops working and you have to refresh to get it back to work.\"}]},{\"type\":\"heading\",\"attrs\":{\"level\":2,\"id\":\"73e4d549-a030-4b0c-b7d8-bcfa69d1b832\"},\"content\":[{\"type\":\"text\",\"text\":\"Steps or workflow to reproduce (with screenshots/recordings)\"}]},{\"type\":\"paragraph\",\"content\":[{\"type\":\"text\",\"text\":\"n8n version:\",\"marks\":[{\"type\":\"strong\",\"attrs\":{}}]},{\"type\":\"text\",\"text\":\" [Deployment type] [version]\"}]},{\"type\":\"ordered_list\",\"attrs\":{\"order\":1},\"content\":[{\"type\":\"list_item\",\"content\":[{\"type\":\"paragraph\",\"content\":[{\"type\":\"text\",\"text\":\"Add any nodes to the canvas\"}]}]},{\"type\":\"list_item\",\"content\":[{\"type\":\"paragraph\",\"content\":[{\"type\":\"text\",\"text\":\"Click either the Duplicate or Pause buttons that appear when hovering over a node\"}]}]},{\"type\":\"list_item\",\"content\":[{\"type\":\"paragraph\",\"content\":[{\"type\":\"text\",\"text\":\"Try scrolling using CMD/CTRL + Click. Scrolling should no longer work while it should still work\"}]}]}]},{\"type\":\"paragraph\",\"content\":[{\"type\":\"text\",\"text\":\"Created by Omar\"}]}]}", "startedTriageAt": "2023-09-12T12:51:41.825Z", "subIssueSortOrder": null, "projectMilestoneId": null, "previousIdentifiers": [], "externalUserCreatorId": null, "lastAppliedTemplateId": null}, "type": "Issue", "actor": {"id": "49ae7598-ae5d-42e6-8a03-9f6038a0d37a", "name": "<PERSON><PERSON>"}, "action": "update", "createdAt": "2024-02-29T16:00:27.794Z", "webhookId": "2120ca07-c896-413a-ab8d-a270e14c1d9e", "updatedFrom": {"updatedAt": "2024-02-29T16:00:27.794Z", "description": "## Description\n\nAfter using the canvas for a while I always had issues where the scrolling would stop working. I finally found a way to reproduce the issue reliably.\n\n## Expected\n\nI would like to always be able to scroll the canvas using CMD + click\n\n## Actual\n\nSometimes when using the app the scrolling stops working and you have to refresh to get it back to work.\n\n## Steps or workflow to reproduce (with screenshots/recordings)\n\n**n8n version:** \\[Deployment type\\] \\[version\\]\n\n1. Add any nodes to the canvas\n2. Click either the Duplicate or Pause buttons that appear when hovering over a node\n3. Try scrolling using CMD/CTRL + Click. Scrolling should no longer work while it should still work\n\nCreated by: <PERSON>", "descriptionData": "{\"type\":\"doc\",\"content\":[{\"type\":\"heading\",\"attrs\":{\"id\":\"d836020f-77f5-4ae0-9d6e-a69bd4567656\",\"level\":2},\"content\":[{\"text\":\"Description\",\"type\":\"text\"}]},{\"type\":\"paragraph\",\"content\":[{\"text\":\"After using the canvas for a while I always had issues where the scrolling would stop working. I finally found a way to reproduce the issue reliably.\",\"type\":\"text\"}]},{\"type\":\"heading\",\"attrs\":{\"id\":\"4125614d-17b0-4530-bfc0-384d43bf80f9\",\"level\":2},\"content\":[{\"text\":\"Expected\",\"type\":\"text\"}]},{\"type\":\"paragraph\",\"content\":[{\"text\":\"I would like to always be able to scroll the canvas using CMD + click\",\"type\":\"text\"}]},{\"type\":\"heading\",\"attrs\":{\"id\":\"3e8caaae-c152-46c1-a604-f0f9c75fb8c9\",\"level\":2},\"content\":[{\"text\":\"Actual\",\"type\":\"text\"}]},{\"type\":\"paragraph\",\"content\":[{\"text\":\"Sometimes when using the app the scrolling stops working and you have to refresh to get it back to work.\",\"type\":\"text\"}]},{\"type\":\"heading\",\"attrs\":{\"id\":\"73e4d549-a030-4b0c-b7d8-bcfa69d1b832\",\"level\":2},\"content\":[{\"text\":\"Steps or workflow to reproduce (with screenshots/recordings)\",\"type\":\"text\"}]},{\"type\":\"paragraph\",\"content\":[{\"text\":\"n8n version:\",\"type\":\"text\",\"marks\":[{\"type\":\"strong\",\"attrs\":{}}]},{\"text\":\" [Deployment type] [version]\",\"type\":\"text\"}]},{\"type\":\"ordered_list\",\"attrs\":{\"order\":1},\"content\":[{\"type\":\"list_item\",\"content\":[{\"type\":\"paragraph\",\"content\":[{\"text\":\"Add any nodes to the canvas\",\"type\":\"text\"}]}]},{\"type\":\"list_item\",\"content\":[{\"type\":\"paragraph\",\"content\":[{\"text\":\"Click either the Duplicate or Pause buttons that appear when hovering over a node\",\"type\":\"text\"}]}]},{\"type\":\"list_item\",\"content\":[{\"type\":\"paragraph\",\"content\":[{\"text\":\"Try scrolling using CMD/CTRL + Click. Scrolling should no longer work while it should still work\",\"type\":\"text\"}]}]}]},{\"type\":\"paragraph\",\"content\":[{\"text\":\"Created by: Omar\",\"type\":\"text\"}]}]}"}, "organizationId": "1c35bbc6-9cd4-427e-8bc5-e5d370a9869f", "webhookTimestamp": 1709222430026}]}, "connections": {"OpenAI": {"main": [[{"node": "Merge data", "type": "main", "index": 0}]]}, "Set me up": {"main": [[{"node": "Only tickets that need to be classified", "type": "main", "index": 0}]]}, "Merge data": {"main": [[{"node": "Check if AI was able to find a team", "type": "main", "index": 0}]]}, "Set team ID": {"main": [[{"node": "Update team", "type": "main", "index": 0}]]}, "Linear Trigger": {"main": [[{"node": "Set me up", "type": "main", "index": 0}]]}, "Get all linear teams": {"main": [[{"node": "Merge data", "type": "main", "index": 1}]]}, "Check if AI was able to find a team": {"main": [[{"node": "Set team ID", "type": "main", "index": 0}], [{"node": "Notify in Slack", "type": "main", "index": 0}]]}, "Only tickets that need to be classified": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}, {"node": "Get all linear teams", "type": "main", "index": 0}]]}}}