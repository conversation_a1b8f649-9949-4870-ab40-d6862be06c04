{"id": "ghfbOYrOSiQVAbl5", "meta": {"instanceId": "558d88703fb65b2d0e44613bc35916258b0f0bf983c5d4730c00c424b77ca36a"}, "name": "Chatbot AI", "tags": [], "nodes": [{"id": "6eec6665-eea6-4aaa-8ae5-2fc7bf0c4746", "name": "Loading Animation", "type": "n8n-nodes-base.httpRequest", "position": [-520, 340], "parameters": {"url": "https://api.line.me/v2/bot/chat/loading/start", "body": "={\n    \"chatId\": \"{{ $json.body.events[0].source.userId }}\",\n    \"loadingSeconds\": 60\n}", "method": "POST", "options": {}, "sendBody": true, "contentType": "raw", "authentication": "genericCredentialType", "rawContentType": "application/json", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "PQI3F0ibV3txKWfv", "name": "Talking Therapy Line@"}}, "typeVersion": 4.2}, {"id": "72ff06e5-e1d8-47e7-be15-888ec9171c72", "name": "ReplyMessage - Not supported", "type": "n8n-nodes-base.httpRequest", "position": [100, 760], "parameters": {"url": "https://api.line.me/v2/bot/message/reply", "body": "={\n    \"replyToken\":\"{{ $('Line Chatbot').item.json.body.events[0].replyToken }}\",\n    \"messages\":[\n        {\n            \"type\":\"text\",\n            \"text\":\"Currently, the input of image or other type are not supported.\"\n        }\n    ]\n}", "method": "POST", "options": {}, "sendBody": true, "contentType": "raw", "sendHeaders": true, "rawContentType": "application/json", "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer /lQWKI4dp71pOMWZu2q18mL8P+zwf9iIOBzUDQOPMqLGMMIg88J6jPcFGfZ2ntsFfaiwCKTEcAsMjliZYXrV5E4lsjioJmv2hS7XYbh8lxmuyz1vXegKwAT66hTIBjQ1zf4l6yKugYNsUmwSYfCSQgdB04t89/1O/w1cDnyilFU="}]}}, "typeVersion": 4.2}, {"id": "3a4eb71f-033d-4aff-a4fd-2ed14ea80c6c", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [40, 80], "parameters": {"text": "={{ $('Line Chatbot').item.json.body.events[0].message.text }}", "options": {"systemMessage": "You're CBT therapist. You'll help the user find the answer to their problems using CBT. but you will not tell them that you're using CBT\n\nCBT is a talking therapy. Talking therapies are also known as psychotherapies. You can find out more about these on our information page on psychotherapies and psychological treatments.\n\nCBT helps you to learn more helpful ways of thinking and reacting in everyday situations. Changing the way you think, and what you do, can help you to feel better.\n\nUnlike some other talking therapies, CBT focuses on your current challenges rather than on your past experiences. It aims to improve your state of mind by teaching you to spot the links between your thoughts, actions and feelings.\n\nC stands for ‘cognitive’ (what you think) – In CBT, you learn to notice when you are thinking negatively. You work to challenge negative or unhelpful thoughts, for example:\n-‘I’m useless’ or\n-‘It’s all going to go wrong’.\nInstead, you work to develop more useful, realistic thoughts, for example:\n-‘What’s the evidence this is true?’\n-‘What’s another way to think about this?’ or\n-‘What advice would I give a friend in my situation?’\n\nB stands for ‘behaviour’ (what you do) – Your behaviour is what you do and how you act. CBT can help you to deal with things you have been avoiding or have fears around. When doing CBT, you might keep a daily diary of activities, and set goals to try things that you are afraid of doing. Writing down your goals and actions can give you a sense of achievement and help you to mark your progress.\n\nT stands for ‘therapy’ (what you learn) – Through CBT you learn new skills that you can then practise as ‘homework’. After you have finished receiving CBT you can continue to practise these skills, which can give you the tools to stay well in the future.\n\n\nCBT can help you to make sense of overwhelming problems by breaking them down into smaller parts. This makes it easier to see how they are connected and how they affect you. These parts are:\n\nA situation – for example, an activity, or something that happens to you that you find difficult\nFrom this can follow:\n-Thoughts\n-Emotions\n-Physical feelings\n-Actions\n\nTypes of CBT \n- Cognitive therapy : Spot unhelpful thoughts and beliefs. Keep a record and try out more useful and realistic ways of thinking and reacting.\n- Behaviour therapy (e.g., graded exposure) : Change unhelpful behaviours, like avoiding, checking, or getting reassurance. Gradually face situations, thoughts, or memories you’ve been avoiding.\n- Behavioural activation: Get more active and involved in life by doing things that give a sense of pleasure or achievement. Keep a diary and schedule in positive activities.\n- Problem-solving therapy: Identify the problem, come up with ways of solving it, pick one solution, and put it into practice.\n- Motivational interviewing: Look at the pros and cons of a habit. Set goals for change.\n- Mindfulness : Pay attention to your thoughts and surroundings in the here and now without reacting to them.\n- Compassionate mind therapy : Be kinder and less critical of yourself and others, helping you to feel safer and more content.\n- Acceptance and commitment therapy (ACT, pronounced ‘act’) : Accept unpleasant thoughts and feelings rather than fight them or get upset.\n- Dialectical behaviour therapy (DBT) :Manage strong feelings and sudden mood changes to overcome relationship difficulties. Combines one-to-one CBT with group therapy.\n- Cognitive analytic therapy : Understand past causes for current difficulties and find new ways of coping. Combines CBT with analytic therapy.\n\nReference: https://www.rcpsych.ac.uk/mental-health/treatments-and-wellbeing/cognitive-behavioural-therapy-(cbt)?spm=5aebb161.59ab0a80.0.0.3380c921WQnNWN\n\nYou'll keep the character limit under 500"}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "7a92aeaf-3496-410f-a6fd-4be5172b650e", "name": "Azure OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatAzureOpenAi", "position": [80, 200], "parameters": {"model": "4o", "options": {}}, "credentials": {"azureOpenAiApi": {"id": "5AjoWhww5SQi2VXd", "name": "Azure Open AI account"}}, "typeVersion": 1}, {"id": "36df34d5-4232-40c8-b0ca-de7e30807adc", "name": "ReplyMessage - Line", "type": "n8n-nodes-base.httpRequest", "position": [920, 80], "parameters": {"url": "https://api.line.me/v2/bot/message/reply", "method": "POST", "options": {}, "jsonBody": "={\n  \"replyToken\": \"{{ $('Line Chatbot').item.json.body.events[0].replyToken }}\",\n  \"messages\": [\n    {\n      \"type\": \"text\",\n      \"text\": \"{{ $json.output }}\"\n    }\n  ]} ", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "PQI3F0ibV3txKWfv", "name": "Talking Therapy Line@"}}, "typeVersion": 4.2}, {"id": "c3e227dd-3306-4259-ad7a-c1911c3c5176", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [0, 680], "parameters": {"color": 4, "width": 320, "height": 260, "content": "For non-text, we do not process and just provide user that it's not supported right now"}, "typeVersion": 1}, {"id": "fba0b833-896e-4332-97e5-fa09a3838191", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-1000, 280], "parameters": {"color": 4, "width": 340, "height": 560, "content": "**Webhook from Line**\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nYou need to set-up this webhook at Line Manager or Line Developer Console\n\nYou'll need to copy Webhook URL from this node to put in Line Console\n\nAlso, don't forget to remove 'test' part when going for production\n\nhttps://developers.line.biz/en/docs/messaging-api/receiving-messages/\n"}, "typeVersion": 1}, {"id": "f58d7af5-70c4-412a-a8f6-6cfceaf65ade", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-640, 280], "parameters": {"color": 4, "width": 340, "height": 560, "content": "**Line Loading Animation**\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nThis node is to only give ... loading animation back in Line.\n\nIt seems stupid but it actually tells user that the workflow is running and you are not left waiting without hope\n\nTo authorize, use header authorization \n\nhttps://developers.line.biz/en/docs/messaging-api/use-loading-indicator/"}, "typeVersion": 1}, {"id": "7c67d79d-e2b8-453c-8adc-cb66e6ef290c", "name": "Line Chatbot", "type": "n8n-nodes-base.webhook", "position": [-900, 340], "webhookId": "c69b940a-5a44-45e3-b9b4-04abda6462b2", "parameters": {"path": "AIChatbot", "options": {}, "httpMethod": "POST"}, "typeVersion": 2}, {"id": "a44319cf-d985-4bbf-be99-ac479406c369", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [820, 0], "parameters": {"color": 4, "width": 320, "height": 600, "content": "**Reply Message**\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nYou can send anything with reply-token without using your broadcast quota.\n\nTo use header auth: \n- select generic > header auth\n- add new \n- name = Authorization\n- value = Bearer <your token>\n- you can rename this credential on top\n\nhttps://developers.line.biz/en/docs/messaging-api/sending-messages/"}, "typeVersion": 1}, {"id": "1cfa159b-57c6-424a-a9e2-4b237a0bcbb5", "name": "Check Message Type IsText?", "type": "n8n-nodes-base.if", "position": [-220, 340], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "e44288a5-18de-48b3-9bb1-0e18f6491043", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Line Chatbot').item.json.body.events[0].message.type }}", "rightValue": "text"}]}}, "typeVersion": 2.2}, {"id": "48363222-487e-4d4a-a424-4406aacc7f74", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [400, 0], "parameters": {"color": 2, "width": 320, "height": 320, "content": "The output from AI-Agent is not properly formatted for JSON to send via reply. So you need to edit it a bit\n"}, "typeVersion": 1}, {"id": "d7b7d3ca-c685-4a02-8b73-a5b24aa663d4", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [0, 0], "parameters": {"color": 5, "width": 320, "height": 620, "content": "**Chat Model (LLM)**\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTo use chat LLM, you need to have AI Agent or LLM Chain. Then you can connect the model to the node.\n- edit system prompt on the mother node. \n- edit model parameters eg. temperature at the AI node (closer to 1 = more creative)\n\nAzure OpenAI Ref : https://davoy.tech/how-to-use-azure-openai-2/\n\nOr you can choose different models"}, "typeVersion": 1}, {"id": "8cb1b56a-15dd-4936-b343-c2350b2a6a48", "name": "Format Reply", "type": "n8n-nodes-base.set", "position": [500, 80], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "15bd9ebd-ba6b-4ee5-9f4b-185260e51b0a", "name": "output", "type": "string", "value": "={{ $json.output.replaceAll(\"\\n\",\"\\\\n\").replaceAll(\"\\n\",\"\").removeMarkdown().removeTags().replaceAll('\"',\"\") }}"}]}}, "typeVersion": 3.4}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "cca20e40-0b31-4e64-9953-610dc6b569d7", "connections": {"AI Agent": {"main": [[{"node": "Format Reply", "type": "main", "index": 0}]]}, "Format Reply": {"main": [[{"node": "ReplyMessage - Line", "type": "main", "index": 0}]]}, "Line Chatbot": {"main": [[{"node": "Loading Animation", "type": "main", "index": 0}]]}, "Loading Animation": {"main": [[{"node": "Check Message Type IsText?", "type": "main", "index": 0}]]}, "Azure OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Check Message Type IsText?": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}], [{"node": "ReplyMessage - Not supported", "type": "main", "index": 0}]]}}}