{"id": "114", "name": "Verify phone numbers", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [440, 510], "parameters": {}, "typeVersion": 1}, {"name": "Create Phone Item", "type": "n8n-nodes-base.functionItem", "position": [640, 510], "parameters": {"functionCode": "item.phone = \"+34605281220\";\nreturn item;"}, "typeVersion": 1}, {"name": "Parse and Validate Phone", "type": "n8n-nodes-base.uproc", "position": [850, 510], "parameters": {"tool": "getPhoneParsed", "phone": "={{$node[\"Create Phone Item\"].json[\"phone\"]}}", "additionalOptions": {}}, "credentials": {"uprocApi": "miquel-uproc"}, "typeVersion": 1}, {"name": "Phone is Valid?", "type": "n8n-nodes-base.if", "position": [1050, 510], "parameters": {"conditions": {"string": [{"value1": "={{$node[\"Parse and Validate Phone\"].json[\"message\"][\"valid\"]+\"\"}}", "value2": "true"}]}}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Create Phone Item": {"main": [[{"node": "Parse and Validate Phone", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Create Phone Item", "type": "main", "index": 0}]]}, "Parse and Validate Phone": {"main": [[{"node": "Phone is Valid?", "type": "main", "index": 0}]]}}}