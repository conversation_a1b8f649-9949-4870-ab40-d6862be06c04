{"id": "96", "name": "Create, update and get a subscriber using the MailerLite node", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [310, 300], "parameters": {}, "typeVersion": 1}, {"name": "MailerLite", "type": "n8n-nodes-base.mailerLite", "position": [510, 300], "parameters": {"email": "<EMAIL>", "additionalFields": {"name": "<PERSON><PERSON><PERSON>"}}, "credentials": {"mailerLiteApi": "mailerlite"}, "typeVersion": 1}, {"name": "MailerLite1", "type": "n8n-nodes-base.mailerLite", "position": [710, 300], "parameters": {"operation": "update", "subscriberId": "={{$node[\"MailerLite\"].json[\"email\"]}}", "updateFields": {"customFieldsUi": {"customFieldsValues": [{"value": "Berlin", "fieldId": "city"}]}}}, "credentials": {"mailerLiteApi": "mailerlite"}, "typeVersion": 1}, {"name": "MailerLite2", "type": "n8n-nodes-base.mailerLite", "position": [910, 300], "parameters": {"operation": "get", "subscriberId": "={{$node[\"MailerLite\"].json[\"email\"]}}"}, "credentials": {"mailerLiteApi": "mailerlite"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"MailerLite": {"main": [[{"node": "MailerLite1", "type": "main", "index": 0}]]}, "MailerLite1": {"main": [[{"node": "MailerLite2", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "MailerLite", "type": "main", "index": 0}]]}}}