{"meta": {"instanceId": ""}, "nodes": [{"id": "9ede57d1-57de-44d5-bf64-38632e54dd73", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.set", "position": [580, 80], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d492ffce-8ece-443d-9fc3-caa9a7a90744", "name": "base_currency", "type": "string", "value": "={{ $json.base_code }}"}, {"id": "33e67974-8cee-4d1f-b144-c4c07b149bab", "name": "time_last_update_utc", "type": "string", "value": "={{ new Date($json[\"time_last_update_utc\"]).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric', timeZone: 'UTC' }) + ' at ' + new Date($json[\"time_last_update_utc\"]).toISOString().substring(11, 16) + ' UTC' }}\n"}]}}, "notesInFlow": true, "retryOnFail": true, "typeVersion": 3.4}, {"id": "7bb820ce-b6aa-46b6-9546-0a3d7f30fa54", "name": "Final Outputs", "type": "n8n-nodes-base.merge", "position": [860, 180], "parameters": {"mode": "combineBySql"}, "notesInFlow": true, "typeVersion": 3}, {"id": "4eb99392-7b5c-4ec8-8eeb-56b01d5778f6", "name": "USD Query", "type": "n8n-nodes-base.httpRequest", "position": [300, 180], "parameters": {"url": "https://v6.exchangerate-api.com/v6/<YOUR_API_KEY>/latest/USD", "options": {}}, "notesInFlow": true, "retryOnFail": true, "typeVersion": 4.2}, {"id": "bc33414a-36db-41d3-881f-870d40bb929e", "name": "Update Rate Sheet", "type": "n8n-nodes-base.googleSheets", "position": [1080, 240], "parameters": {"columns": {"value": {}, "schema": [{"id": "base_currency", "type": "string", "display": true, "removed": false, "required": false, "displayName": "base_currency", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "time_last_update_utc", "type": "string", "display": true, "removed": false, "required": false, "displayName": "time_last_update_utc", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "USD", "type": "string", "display": true, "removed": false, "required": false, "displayName": "USD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "AED", "type": "string", "display": true, "removed": false, "required": false, "displayName": "AED", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "AFN", "type": "string", "display": true, "removed": false, "required": false, "displayName": "AFN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ALL", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ALL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "AMD", "type": "string", "display": true, "removed": false, "required": false, "displayName": "AMD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ANG", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ANG", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "AOA", "type": "string", "display": true, "removed": false, "required": false, "displayName": "AOA", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ARS", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ARS", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "AUD", "type": "string", "display": true, "removed": false, "required": false, "displayName": "AUD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "AWG", "type": "string", "display": true, "removed": false, "required": false, "displayName": "AWG", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "AZN", "type": "string", "display": true, "removed": false, "required": false, "displayName": "AZN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BAM", "type": "string", "display": true, "removed": false, "required": false, "displayName": "BAM", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BBD", "type": "string", "display": true, "removed": false, "required": false, "displayName": "BBD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BDT", "type": "string", "display": true, "removed": false, "required": false, "displayName": "BDT", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BGN", "type": "string", "display": true, "removed": false, "required": false, "displayName": "BGN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BHD", "type": "string", "display": true, "removed": false, "required": false, "displayName": "BHD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BIF", "type": "string", "display": true, "removed": false, "required": false, "displayName": "BIF", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BMD", "type": "string", "display": true, "removed": false, "required": false, "displayName": "BMD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BND", "type": "string", "display": true, "removed": false, "required": false, "displayName": "BND", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BOB", "type": "string", "display": true, "removed": false, "required": false, "displayName": "BOB", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BRL", "type": "string", "display": true, "removed": false, "required": false, "displayName": "BRL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BSD", "type": "string", "display": true, "removed": false, "required": false, "displayName": "BSD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BTN", "type": "string", "display": true, "removed": false, "required": false, "displayName": "BTN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BWP", "type": "string", "display": true, "removed": false, "required": false, "displayName": "BWP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BYN", "type": "string", "display": true, "removed": false, "required": false, "displayName": "BYN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BZD", "type": "string", "display": true, "removed": false, "required": false, "displayName": "BZD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CAD", "type": "string", "display": true, "removed": false, "required": false, "displayName": "CAD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CDF", "type": "string", "display": true, "removed": false, "required": false, "displayName": "CDF", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CHF", "type": "string", "display": true, "removed": false, "required": false, "displayName": "CHF", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CLP", "type": "string", "display": true, "removed": false, "required": false, "displayName": "CLP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CNY", "type": "string", "display": true, "removed": false, "required": false, "displayName": "CNY", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "COP", "type": "string", "display": true, "removed": false, "required": false, "displayName": "COP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CRC", "type": "string", "display": true, "removed": false, "required": false, "displayName": "CRC", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CUP", "type": "string", "display": true, "removed": false, "required": false, "displayName": "CUP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CVE", "type": "string", "display": true, "removed": false, "required": false, "displayName": "CVE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CZK", "type": "string", "display": true, "removed": false, "required": false, "displayName": "CZK", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "DJF", "type": "string", "display": true, "removed": false, "required": false, "displayName": "DJF", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "DKK", "type": "string", "display": true, "removed": false, "required": false, "displayName": "DKK", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "DOP", "type": "string", "display": true, "removed": false, "required": false, "displayName": "DOP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "DZD", "type": "string", "display": true, "removed": false, "required": false, "displayName": "DZD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "EGP", "type": "string", "display": true, "removed": false, "required": false, "displayName": "EGP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ERN", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ERN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ETB", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ETB", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "EUR", "type": "string", "display": true, "removed": false, "required": false, "displayName": "EUR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "FJD", "type": "string", "display": true, "removed": false, "required": false, "displayName": "FJD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "FKP", "type": "string", "display": true, "removed": false, "required": false, "displayName": "FKP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "FOK", "type": "string", "display": true, "removed": false, "required": false, "displayName": "FOK", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "GBP", "type": "string", "display": true, "removed": false, "required": false, "displayName": "GBP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "GEL", "type": "string", "display": true, "removed": false, "required": false, "displayName": "GEL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "GGP", "type": "string", "display": true, "removed": false, "required": false, "displayName": "GGP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "GHS", "type": "string", "display": true, "removed": false, "required": false, "displayName": "GHS", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "GIP", "type": "string", "display": true, "removed": false, "required": false, "displayName": "GIP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "GMD", "type": "string", "display": true, "removed": false, "required": false, "displayName": "GMD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "GNF", "type": "string", "display": true, "removed": false, "required": false, "displayName": "GNF", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "GTQ", "type": "string", "display": true, "removed": false, "required": false, "displayName": "GTQ", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "GYD", "type": "string", "display": true, "removed": false, "required": false, "displayName": "GYD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "HKD", "type": "string", "display": true, "removed": false, "required": false, "displayName": "HKD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "HNL", "type": "string", "display": true, "removed": false, "required": false, "displayName": "HNL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "HRK", "type": "string", "display": true, "removed": false, "required": false, "displayName": "HRK", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "HTG", "type": "string", "display": true, "removed": false, "required": false, "displayName": "HTG", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "HUF", "type": "string", "display": true, "removed": false, "required": false, "displayName": "HUF", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "IDR", "type": "string", "display": true, "removed": false, "required": false, "displayName": "IDR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ILS", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ILS", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "IMP", "type": "string", "display": true, "removed": false, "required": false, "displayName": "IMP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "INR", "type": "string", "display": true, "removed": false, "required": false, "displayName": "INR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "IQD", "type": "string", "display": true, "removed": false, "required": false, "displayName": "IQD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "IRR", "type": "string", "display": true, "removed": false, "required": false, "displayName": "IRR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ISK", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ISK", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "JEP", "type": "string", "display": true, "removed": false, "required": false, "displayName": "JEP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "JMD", "type": "string", "display": true, "removed": false, "required": false, "displayName": "JMD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "JOD", "type": "string", "display": true, "removed": false, "required": false, "displayName": "JOD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "JPY", "type": "string", "display": true, "removed": false, "required": false, "displayName": "JPY", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "KES", "type": "string", "display": true, "removed": false, "required": false, "displayName": "KES", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "KGS", "type": "string", "display": true, "removed": false, "required": false, "displayName": "KGS", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "KHR", "type": "string", "display": true, "removed": false, "required": false, "displayName": "KHR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "KID", "type": "string", "display": true, "removed": false, "required": false, "displayName": "KID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "KMF", "type": "string", "display": true, "removed": false, "required": false, "displayName": "KMF", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "KRW", "type": "string", "display": true, "removed": false, "required": false, "displayName": "KRW", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "KWD", "type": "string", "display": true, "removed": false, "required": false, "displayName": "KWD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "KYD", "type": "string", "display": true, "removed": false, "required": false, "displayName": "KYD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "KZT", "type": "string", "display": true, "removed": false, "required": false, "displayName": "KZT", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "LAK", "type": "string", "display": true, "removed": false, "required": false, "displayName": "LAK", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "LBP", "type": "string", "display": true, "removed": false, "required": false, "displayName": "LBP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "LKR", "type": "string", "display": true, "removed": false, "required": false, "displayName": "LKR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "LRD", "type": "string", "display": true, "removed": false, "required": false, "displayName": "LRD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "LSL", "type": "string", "display": true, "removed": false, "required": false, "displayName": "LSL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "LYD", "type": "string", "display": true, "removed": false, "required": false, "displayName": "LYD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MAD", "type": "string", "display": true, "removed": false, "required": false, "displayName": "MAD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MDL", "type": "string", "display": true, "removed": false, "required": false, "displayName": "MDL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MGA", "type": "string", "display": true, "removed": false, "required": false, "displayName": "MGA", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MKD", "type": "string", "display": true, "removed": false, "required": false, "displayName": "MKD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MMK", "type": "string", "display": true, "removed": false, "required": false, "displayName": "MMK", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MNT", "type": "string", "display": true, "removed": false, "required": false, "displayName": "MNT", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MOP", "type": "string", "display": true, "removed": false, "required": false, "displayName": "MOP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MRU", "type": "string", "display": true, "removed": false, "required": false, "displayName": "MRU", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MUR", "type": "string", "display": true, "removed": false, "required": false, "displayName": "MUR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MVR", "type": "string", "display": true, "removed": false, "required": false, "displayName": "MVR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MWK", "type": "string", "display": true, "removed": false, "required": false, "displayName": "MWK", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MXN", "type": "string", "display": true, "removed": false, "required": false, "displayName": "MXN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MYR", "type": "string", "display": true, "removed": false, "required": false, "displayName": "MYR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MZN", "type": "string", "display": true, "removed": false, "required": false, "displayName": "MZN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "NAD", "type": "string", "display": true, "removed": false, "required": false, "displayName": "NAD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "NGN", "type": "string", "display": true, "removed": false, "required": false, "displayName": "NGN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "NIO", "type": "string", "display": true, "removed": false, "required": false, "displayName": "NIO", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "NOK", "type": "string", "display": true, "removed": false, "required": false, "displayName": "NOK", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "NPR", "type": "string", "display": true, "removed": false, "required": false, "displayName": "NPR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "NZD", "type": "string", "display": true, "removed": false, "required": false, "displayName": "NZD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "OMR", "type": "string", "display": true, "removed": false, "required": false, "displayName": "OMR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "PAB", "type": "string", "display": true, "removed": false, "required": false, "displayName": "PAB", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "PEN", "type": "string", "display": true, "removed": false, "required": false, "displayName": "PEN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "PGK", "type": "string", "display": true, "removed": false, "required": false, "displayName": "PGK", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "PHP", "type": "string", "display": true, "removed": false, "required": false, "displayName": "PHP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "PKR", "type": "string", "display": true, "removed": false, "required": false, "displayName": "PKR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "PLN", "type": "string", "display": true, "removed": false, "required": false, "displayName": "PLN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "PYG", "type": "string", "display": true, "removed": false, "required": false, "displayName": "PYG", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "QAR", "type": "string", "display": true, "removed": false, "required": false, "displayName": "QAR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "RON", "type": "string", "display": true, "removed": false, "required": false, "displayName": "RON", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "RSD", "type": "string", "display": true, "removed": false, "required": false, "displayName": "RSD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "RUB", "type": "string", "display": true, "removed": false, "required": false, "displayName": "RUB", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "RWF", "type": "string", "display": true, "removed": false, "required": false, "displayName": "RWF", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SAR", "type": "string", "display": true, "removed": false, "required": false, "displayName": "SAR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SBD", "type": "string", "display": true, "removed": false, "required": false, "displayName": "SBD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SCR", "type": "string", "display": true, "removed": false, "required": false, "displayName": "SCR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SDG", "type": "string", "display": true, "removed": false, "required": false, "displayName": "SDG", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SEK", "type": "string", "display": true, "removed": false, "required": false, "displayName": "SEK", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SGD", "type": "string", "display": true, "removed": false, "required": false, "displayName": "SGD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SHP", "type": "string", "display": true, "removed": false, "required": false, "displayName": "SHP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SLE", "type": "string", "display": true, "removed": false, "required": false, "displayName": "SLE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SLL", "type": "string", "display": true, "removed": false, "required": false, "displayName": "SLL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SOS", "type": "string", "display": true, "removed": false, "required": false, "displayName": "SOS", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SRD", "type": "string", "display": true, "removed": false, "required": false, "displayName": "SRD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SSP", "type": "string", "display": true, "removed": false, "required": false, "displayName": "SSP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "STN", "type": "string", "display": true, "removed": false, "required": false, "displayName": "STN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SYP", "type": "string", "display": true, "removed": false, "required": false, "displayName": "SYP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SZL", "type": "string", "display": true, "removed": false, "required": false, "displayName": "SZL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "THB", "type": "string", "display": true, "removed": false, "required": false, "displayName": "THB", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TJS", "type": "string", "display": true, "removed": false, "required": false, "displayName": "TJS", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TMT", "type": "string", "display": true, "removed": false, "required": false, "displayName": "TMT", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TND", "type": "string", "display": true, "removed": false, "required": false, "displayName": "TND", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TOP", "type": "string", "display": true, "removed": false, "required": false, "displayName": "TOP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TRY", "type": "string", "display": true, "removed": false, "required": false, "displayName": "TRY", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TTD", "type": "string", "display": true, "removed": false, "required": false, "displayName": "TTD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TVD", "type": "string", "display": true, "removed": false, "required": false, "displayName": "TVD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TWD", "type": "string", "display": true, "removed": false, "required": false, "displayName": "TWD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TZS", "type": "string", "display": true, "removed": false, "required": false, "displayName": "TZS", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "UAH", "type": "string", "display": true, "removed": false, "required": false, "displayName": "UAH", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "UGX", "type": "string", "display": true, "removed": false, "required": false, "displayName": "UGX", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "UYU", "type": "string", "display": true, "removed": false, "required": false, "displayName": "UYU", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "UZS", "type": "string", "display": true, "removed": false, "required": false, "displayName": "UZS", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "VES", "type": "string", "display": true, "removed": false, "required": false, "displayName": "VES", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "VND", "type": "string", "display": true, "removed": false, "required": false, "displayName": "VND", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "VUV", "type": "string", "display": true, "removed": false, "required": false, "displayName": "VUV", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "WST", "type": "string", "display": true, "removed": false, "required": false, "displayName": "WST", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "XAF", "type": "string", "display": true, "removed": false, "required": false, "displayName": "XAF", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "XCD", "type": "string", "display": true, "removed": false, "required": false, "displayName": "XCD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "XCG", "type": "string", "display": true, "removed": false, "required": false, "displayName": "XCG", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "XDR", "type": "string", "display": true, "removed": false, "required": false, "displayName": "XDR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "XOF", "type": "string", "display": true, "removed": false, "required": false, "displayName": "XOF", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "XPF", "type": "string", "display": true, "removed": false, "required": false, "displayName": "XPF", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "YER", "type": "string", "display": true, "removed": false, "required": false, "displayName": "YER", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ZAR", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ZAR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ZMW", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ZMW", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ZWL", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ZWL", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": ["base_currency"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "=", "value": "gid=0", "cachedResultUrl": "=", "cachedResultName": "="}, "documentId": {"__rl": true, "mode": "=", "value": "=", "cachedResultUrl": "=", "cachedResultName": "="}}, "notesInFlow": true, "typeVersion": 4.5}, {"id": "51c05637-b9c0-4fa5-9942-42b5abb870db", "name": "Archive Rates", "type": "n8n-nodes-base.googleSheets", "position": [1080, 100], "parameters": {"columns": {"value": {}, "schema": [{"id": "base_currency", "type": "string", "display": true, "required": false, "displayName": "base_currency", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "time_last_update_utc", "type": "string", "display": true, "required": false, "displayName": "time_last_update_utc", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "USD", "type": "string", "display": true, "required": false, "displayName": "USD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "AED", "type": "string", "display": true, "required": false, "displayName": "AED", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "AFN", "type": "string", "display": true, "required": false, "displayName": "AFN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ALL", "type": "string", "display": true, "required": false, "displayName": "ALL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "AMD", "type": "string", "display": true, "required": false, "displayName": "AMD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ANG", "type": "string", "display": true, "required": false, "displayName": "ANG", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "AOA", "type": "string", "display": true, "required": false, "displayName": "AOA", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ARS", "type": "string", "display": true, "required": false, "displayName": "ARS", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "AUD", "type": "string", "display": true, "required": false, "displayName": "AUD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "AWG", "type": "string", "display": true, "required": false, "displayName": "AWG", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "AZN", "type": "string", "display": true, "required": false, "displayName": "AZN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BAM", "type": "string", "display": true, "required": false, "displayName": "BAM", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BBD", "type": "string", "display": true, "required": false, "displayName": "BBD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BDT", "type": "string", "display": true, "required": false, "displayName": "BDT", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BGN", "type": "string", "display": true, "required": false, "displayName": "BGN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BHD", "type": "string", "display": true, "required": false, "displayName": "BHD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BIF", "type": "string", "display": true, "required": false, "displayName": "BIF", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BMD", "type": "string", "display": true, "required": false, "displayName": "BMD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BND", "type": "string", "display": true, "required": false, "displayName": "BND", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BOB", "type": "string", "display": true, "required": false, "displayName": "BOB", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BRL", "type": "string", "display": true, "required": false, "displayName": "BRL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BSD", "type": "string", "display": true, "required": false, "displayName": "BSD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BTN", "type": "string", "display": true, "required": false, "displayName": "BTN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BWP", "type": "string", "display": true, "required": false, "displayName": "BWP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BYN", "type": "string", "display": true, "required": false, "displayName": "BYN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BZD", "type": "string", "display": true, "required": false, "displayName": "BZD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CAD", "type": "string", "display": true, "required": false, "displayName": "CAD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CDF", "type": "string", "display": true, "required": false, "displayName": "CDF", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CHF", "type": "string", "display": true, "required": false, "displayName": "CHF", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CLP", "type": "string", "display": true, "required": false, "displayName": "CLP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CNY", "type": "string", "display": true, "required": false, "displayName": "CNY", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "COP", "type": "string", "display": true, "required": false, "displayName": "COP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CRC", "type": "string", "display": true, "required": false, "displayName": "CRC", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CUP", "type": "string", "display": true, "required": false, "displayName": "CUP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CVE", "type": "string", "display": true, "required": false, "displayName": "CVE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CZK", "type": "string", "display": true, "required": false, "displayName": "CZK", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "DJF", "type": "string", "display": true, "required": false, "displayName": "DJF", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "DKK", "type": "string", "display": true, "required": false, "displayName": "DKK", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "DOP", "type": "string", "display": true, "required": false, "displayName": "DOP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "DZD", "type": "string", "display": true, "required": false, "displayName": "DZD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "EGP", "type": "string", "display": true, "required": false, "displayName": "EGP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ERN", "type": "string", "display": true, "required": false, "displayName": "ERN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ETB", "type": "string", "display": true, "required": false, "displayName": "ETB", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "EUR", "type": "string", "display": true, "required": false, "displayName": "EUR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "FJD", "type": "string", "display": true, "required": false, "displayName": "FJD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "FKP", "type": "string", "display": true, "required": false, "displayName": "FKP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "FOK", "type": "string", "display": true, "required": false, "displayName": "FOK", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "GBP", "type": "string", "display": true, "required": false, "displayName": "GBP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "GEL", "type": "string", "display": true, "required": false, "displayName": "GEL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "GGP", "type": "string", "display": true, "required": false, "displayName": "GGP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "GHS", "type": "string", "display": true, "required": false, "displayName": "GHS", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "GIP", "type": "string", "display": true, "required": false, "displayName": "GIP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "GMD", "type": "string", "display": true, "required": false, "displayName": "GMD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "GNF", "type": "string", "display": true, "required": false, "displayName": "GNF", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "GTQ", "type": "string", "display": true, "required": false, "displayName": "GTQ", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "GYD", "type": "string", "display": true, "required": false, "displayName": "GYD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "HKD", "type": "string", "display": true, "required": false, "displayName": "HKD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "HNL", "type": "string", "display": true, "required": false, "displayName": "HNL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "HRK", "type": "string", "display": true, "required": false, "displayName": "HRK", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "HTG", "type": "string", "display": true, "required": false, "displayName": "HTG", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "HUF", "type": "string", "display": true, "required": false, "displayName": "HUF", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "IDR", "type": "string", "display": true, "required": false, "displayName": "IDR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ILS", "type": "string", "display": true, "required": false, "displayName": "ILS", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "IMP", "type": "string", "display": true, "required": false, "displayName": "IMP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "INR", "type": "string", "display": true, "required": false, "displayName": "INR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "IQD", "type": "string", "display": true, "required": false, "displayName": "IQD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "IRR", "type": "string", "display": true, "required": false, "displayName": "IRR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ISK", "type": "string", "display": true, "required": false, "displayName": "ISK", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "JEP", "type": "string", "display": true, "required": false, "displayName": "JEP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "JMD", "type": "string", "display": true, "required": false, "displayName": "JMD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "JOD", "type": "string", "display": true, "required": false, "displayName": "JOD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "JPY", "type": "string", "display": true, "required": false, "displayName": "JPY", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "KES", "type": "string", "display": true, "required": false, "displayName": "KES", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "KGS", "type": "string", "display": true, "required": false, "displayName": "KGS", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "KHR", "type": "string", "display": true, "required": false, "displayName": "KHR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "KID", "type": "string", "display": true, "required": false, "displayName": "KID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "KMF", "type": "string", "display": true, "required": false, "displayName": "KMF", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "KRW", "type": "string", "display": true, "required": false, "displayName": "KRW", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "KWD", "type": "string", "display": true, "required": false, "displayName": "KWD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "KYD", "type": "string", "display": true, "required": false, "displayName": "KYD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "KZT", "type": "string", "display": true, "required": false, "displayName": "KZT", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "LAK", "type": "string", "display": true, "required": false, "displayName": "LAK", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "LBP", "type": "string", "display": true, "required": false, "displayName": "LBP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "LKR", "type": "string", "display": true, "required": false, "displayName": "LKR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "LRD", "type": "string", "display": true, "required": false, "displayName": "LRD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "LSL", "type": "string", "display": true, "required": false, "displayName": "LSL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "LYD", "type": "string", "display": true, "required": false, "displayName": "LYD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MAD", "type": "string", "display": true, "required": false, "displayName": "MAD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MDL", "type": "string", "display": true, "required": false, "displayName": "MDL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MGA", "type": "string", "display": true, "required": false, "displayName": "MGA", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MKD", "type": "string", "display": true, "required": false, "displayName": "MKD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MMK", "type": "string", "display": true, "required": false, "displayName": "MMK", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MNT", "type": "string", "display": true, "required": false, "displayName": "MNT", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MOP", "type": "string", "display": true, "required": false, "displayName": "MOP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MRU", "type": "string", "display": true, "required": false, "displayName": "MRU", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MUR", "type": "string", "display": true, "required": false, "displayName": "MUR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MVR", "type": "string", "display": true, "required": false, "displayName": "MVR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MWK", "type": "string", "display": true, "required": false, "displayName": "MWK", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MXN", "type": "string", "display": true, "required": false, "displayName": "MXN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MYR", "type": "string", "display": true, "required": false, "displayName": "MYR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MZN", "type": "string", "display": true, "required": false, "displayName": "MZN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "NAD", "type": "string", "display": true, "required": false, "displayName": "NAD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "NGN", "type": "string", "display": true, "required": false, "displayName": "NGN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "NIO", "type": "string", "display": true, "required": false, "displayName": "NIO", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "NOK", "type": "string", "display": true, "required": false, "displayName": "NOK", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "NPR", "type": "string", "display": true, "required": false, "displayName": "NPR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "NZD", "type": "string", "display": true, "required": false, "displayName": "NZD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "OMR", "type": "string", "display": true, "required": false, "displayName": "OMR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "PAB", "type": "string", "display": true, "required": false, "displayName": "PAB", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "PEN", "type": "string", "display": true, "required": false, "displayName": "PEN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "PGK", "type": "string", "display": true, "required": false, "displayName": "PGK", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "PHP", "type": "string", "display": true, "required": false, "displayName": "PHP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "PKR", "type": "string", "display": true, "required": false, "displayName": "PKR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "PLN", "type": "string", "display": true, "required": false, "displayName": "PLN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "PYG", "type": "string", "display": true, "required": false, "displayName": "PYG", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "QAR", "type": "string", "display": true, "required": false, "displayName": "QAR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "RON", "type": "string", "display": true, "required": false, "displayName": "RON", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "RSD", "type": "string", "display": true, "required": false, "displayName": "RSD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "RUB", "type": "string", "display": true, "required": false, "displayName": "RUB", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "RWF", "type": "string", "display": true, "required": false, "displayName": "RWF", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SAR", "type": "string", "display": true, "required": false, "displayName": "SAR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SBD", "type": "string", "display": true, "required": false, "displayName": "SBD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SCR", "type": "string", "display": true, "required": false, "displayName": "SCR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SDG", "type": "string", "display": true, "required": false, "displayName": "SDG", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SEK", "type": "string", "display": true, "required": false, "displayName": "SEK", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SGD", "type": "string", "display": true, "required": false, "displayName": "SGD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SHP", "type": "string", "display": true, "required": false, "displayName": "SHP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SLE", "type": "string", "display": true, "required": false, "displayName": "SLE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SLL", "type": "string", "display": true, "required": false, "displayName": "SLL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SOS", "type": "string", "display": true, "required": false, "displayName": "SOS", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SRD", "type": "string", "display": true, "required": false, "displayName": "SRD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SSP", "type": "string", "display": true, "required": false, "displayName": "SSP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "STN", "type": "string", "display": true, "required": false, "displayName": "STN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SYP", "type": "string", "display": true, "required": false, "displayName": "SYP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SZL", "type": "string", "display": true, "required": false, "displayName": "SZL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "THB", "type": "string", "display": true, "required": false, "displayName": "THB", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TJS", "type": "string", "display": true, "required": false, "displayName": "TJS", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TMT", "type": "string", "display": true, "required": false, "displayName": "TMT", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TND", "type": "string", "display": true, "required": false, "displayName": "TND", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TOP", "type": "string", "display": true, "required": false, "displayName": "TOP", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TRY", "type": "string", "display": true, "required": false, "displayName": "TRY", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TTD", "type": "string", "display": true, "required": false, "displayName": "TTD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TVD", "type": "string", "display": true, "required": false, "displayName": "TVD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TWD", "type": "string", "display": true, "required": false, "displayName": "TWD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TZS", "type": "string", "display": true, "required": false, "displayName": "TZS", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "UAH", "type": "string", "display": true, "required": false, "displayName": "UAH", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "UGX", "type": "string", "display": true, "required": false, "displayName": "UGX", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "UYU", "type": "string", "display": true, "required": false, "displayName": "UYU", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "UZS", "type": "string", "display": true, "required": false, "displayName": "UZS", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "VES", "type": "string", "display": true, "required": false, "displayName": "VES", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "VND", "type": "string", "display": true, "required": false, "displayName": "VND", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "VUV", "type": "string", "display": true, "required": false, "displayName": "VUV", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "WST", "type": "string", "display": true, "required": false, "displayName": "WST", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "XAF", "type": "string", "display": true, "required": false, "displayName": "XAF", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "XCD", "type": "string", "display": true, "required": false, "displayName": "XCD", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "XCG", "type": "string", "display": true, "required": false, "displayName": "XCG", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "XDR", "type": "string", "display": true, "required": false, "displayName": "XDR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "XOF", "type": "string", "display": true, "required": false, "displayName": "XOF", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "XPF", "type": "string", "display": true, "required": false, "displayName": "XPF", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "YER", "type": "string", "display": true, "required": false, "displayName": "YER", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ZAR", "type": "string", "display": true, "required": false, "displayName": "ZAR", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ZMW", "type": "string", "display": true, "required": false, "displayName": "ZMW", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ZWL", "type": "string", "display": true, "required": false, "displayName": "ZWL", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": 249536536, "cachedResultUrl": "=", "cachedResultName": "Archives"}, "documentId": {"__rl": true, "mode": "=", "value": "=", "cachedResultUrl": "=", "cachedResultName": "="}}, "notesInFlow": true, "typeVersion": 4.5}, {"id": "7e27ded4-f201-4c45-9221-23a4dbdaada1", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [740, -280], "parameters": {"color": 7, "width": 540, "height": 680, "content": "### 2. Update Rates and Record Data\nThe results are recorded in two sheets\n- The invoice template sheet with the exchange rate and the update date\n- A record sheet that includes all the conversions from the base currency to any target currency\n\n#### How to setup?\n- **Update Results in Google Sheets**:\n 0. Copy and paste the template of Google Sheet: [Template Sheet](https://docs.google.com/spreadsheets/d/1SjzMb2q-6-byx9qmHbkrLseBWj9jEGduinH_5xi-c7g/edit?usp=sharing)\n   1. Add your Google Sheet API credentials to access the Google Sheet file\n   2. Select the file using the list, an URL or an ID\n   3. Select the sheet in which the vocabulary list is stored\n  [Learn more about the Google Sheet Node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googlesheets)"}, "typeVersion": 1}, {"id": "062fc991-7c1d-4aee-a85a-7b961e310c33", "name": "Format Output to JSON", "type": "n8n-nodes-base.code", "position": [580, 280], "parameters": {"jsCode": "const rates = items[0].json.conversion_rates;\n\nreturn [\n  {\n    json: rates\n  }\n];\n"}, "typeVersion": 2}, {"id": "2379c1ca-2348-4d19-b47e-8cbe5955e759", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [20, -280], "parameters": {"color": 7, "width": 480, "height": 640, "content": "### 1. Setup the Trigger and Exchange API Call\nThe scheduler will trigger the workflow every morning at 08:00. It starts with an API call to collect the exchange rate of USD to all currency.\n\n#### How to setup?\n- **Put your API Key for exchange rate**:\n   1. Sign up for a free tier and get your API key: [API Documentation](https://www.exchangerate-api.com/docs/overview)\n   2. Replace the placeholder in the HTTP request node <YOUR_API_KEY> with your key"}, "typeVersion": 1}, {"id": "f383eca8-bc1f-40f7-a450-7dbbbcec4c49", "name": "Trigger - 08:00 am", "type": "n8n-nodes-base.scheduleTrigger", "position": [100, 180], "parameters": {"rule": {"interval": [{"triggerAtHour": 7}]}}, "typeVersion": 1.2}, {"id": "64cd2e3b-2ca3-4df5-b855-b4d37912d643", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1300, -280], "parameters": {"width": 780, "height": 540, "content": "### 3. Do you need more details?\nFind a step-by-step guide in this tutorial\n![Guide](https://www.samirsaci.com/content/images/2025/04/temp-6.png)\n[🎥 Watch My Tutorial](https://www.youtube.com/watch?v=T8UFxu8Y9zA)"}, "typeVersion": 1}], "pinData": {}, "connections": {"USD Query": {"main": [[{"node": "Format Output to JSON", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Filter Fields": {"main": [[{"node": "Final Outputs", "type": "main", "index": 0}]]}, "Final Outputs": {"main": [[{"node": "Update Rate Sheet", "type": "main", "index": 0}, {"node": "Archive Rates", "type": "main", "index": 0}]]}, "Trigger - 08:00 am": {"main": [[{"node": "USD Query", "type": "main", "index": 0}]]}, "Format Output to JSON": {"main": [[{"node": "Final Outputs", "type": "main", "index": 1}]]}}}