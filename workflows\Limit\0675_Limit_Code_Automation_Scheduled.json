{"nodes": [{"id": "fc128eed-1666-46b8-8feb-e6ddf05e85d1", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [380, 240], "parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 2}]}}, "typeVersion": 1.2}, {"id": "830708eb-197b-4bf7-95da-893d78329ab2", "name": "Strava", "type": "n8n-nodes-base.strava", "position": [380, 480], "parameters": {"limit": 10, "operation": "getAll"}, "typeVersion": 1.1}, {"id": "de776ebf-3ad5-4c4c-b0c8-7bc74cba5446", "name": "Code", "type": "n8n-nodes-base.code", "position": [380, 740], "parameters": {"jsCode": "// Obtén los items del nodo \"Strava\"\nconst stravaItems = $('strava_last').all();\n\n// Obtén los items del nodo \"ultimas_id\"\nconst ultimasGuardadasItems = $('saved_last').all();\n\n// Extrae las referencias guardadas en un Set, asegurando el formato como cadena\nconst referenciasGuardadas = new Set(\n    ultimasGuardadasItems.map(item => String(item.json.id))\n);\n\n// Filtra los items de \"Strava\" cuyos IDs no estén en las referencias guardadas\nconst filteredItems = stravaItems.filter(item => {\n    // Convertir el ID actual de Strava a cadena para comparar correctamente\n    return !referenciasGuardadas.has(String(item.json.id));\n});\n\n// Depuración: imprime las referencias y los resultados\nconsole.log('Referencias guardadas:', [...referenciasGuardadas]);\nconsole.log('Items filtrados:', filteredItems);\n\n// Devuelve los items filtrados\nreturn filteredItems;\n\n\n"}, "typeVersion": 2}, {"id": "c8a93e6e-67fc-4f6d-bcde-83d3a885c622", "name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [900, 740], "parameters": {"columns": {"value": {"Kms": "={{ $json.distancia }}", "Ref": "={{ $json.id }}", "Fecha": "={{ $json.fecha }}", "Track": "=http://www.strava.com/activities/{{ $json.id }}", "Tiempo": "={{ $json.tiempo }}", "Desnivel": "={{ $json.elevacion }}"}, "schema": [{"id": "<PERSON><PERSON>", "type": "string", "display": true, "required": false, "displayName": "<PERSON><PERSON>", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Kms", "type": "string", "display": true, "required": false, "displayName": "Kms", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Tiempo", "type": "string", "display": true, "required": false, "displayName": "Tiempo", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Ref", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Ref", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Track", "type": "string", "display": true, "required": false, "displayName": "Track", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Bicicleta", "type": "string", "display": true, "required": false, "displayName": "Bicicleta", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Terreno", "type": "string", "display": true, "required": false, "displayName": "Terreno", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON><PERSON>", "type": "string", "display": true, "required": false, "displayName": "<PERSON><PERSON><PERSON>", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": []}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": 419561402, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/159k8cDL8hZooz-dsHE6ueWf68mBkHhxVCKnWm-lYLqs/edit#gid=419561402", "cachedResultName": "n8n"}, "documentId": {"__rl": true, "mode": "list", "value": "159k8cDL8hZooz-dsHE6ueWf68mBkHhxVCKnWm-lYLqs", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/159k8cDL8hZooz-dsHE6ueWf68mBkHhxVCKnWm-lYLqs/edit?usp=drivesdk", "cachedResultName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "tyg7FJlIITkSazyi", "name": "<PERSON>'s Google"}}, "typeVersion": 4.5}, {"id": "0ce07d54-97af-4e88-9d27-452191a0b3ba", "name": "strava_last", "type": "n8n-nodes-base.set", "position": [1420, 480], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "423ae4b8-287c-4dc1-b32b-d1b6f1f45efa", "name": "id", "type": "number", "value": "={{ $json.id }}"}, {"id": "595802d2-17d0-40be-9e43-d655ffbf4ce0", "name": "fecha", "type": "string", "value": "={{ DateTime.fromISO($json.start_date_local).toFormat('d/M/yyyy') }}"}, {"id": "4b39d783-19f2-4a7e-b0e6-dbe2b98f1ae0", "name": "distancia", "type": "number", "value": "={{ Math.round($json.distance / 100) / 10 }}"}, {"id": "2f321dc0-435f-4b4d-866c-091ff9eaf9df", "name": "elevacion", "type": "number", "value": "={{ Math.round($json.total_elevation_gain) }}"}, {"id": "ba1bb089-5ae7-4e42-ac65-07323c4e1842", "name": "tiempo", "type": "string", "value": "={{ `${Math.floor($json.moving_time / 3600)}:${Math.floor(($json.moving_time % 3600) / 60).toString().padStart(2, '0')}:${($json.moving_time % 60).toString().padStart(2, '0')}` }}\n"}]}}, "typeVersion": 3.4}, {"id": "490f7be9-73c9-4431-8b83-fcdbbcc283eb", "name": "Remove Duplicates", "type": "n8n-nodes-base.removeDuplicates", "position": [900, 480], "parameters": {"compare": "<PERSON><PERSON><PERSON>s", "options": {}, "fieldsToCompare": "id"}, "typeVersion": 2}, {"id": "2d1c4dc5-2baa-4c89-a312-4b40381d4e5d", "name": "activities", "type": "n8n-nodes-base.googleSheets", "position": [660, 240], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "list", "value": 419561402, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/159k8cDL8hZooz-dsHE6ueWf68mBkHhxVCKnWm-lYLqs/edit#gid=419561402", "cachedResultName": "n8n"}, "documentId": {"__rl": true, "mode": "list", "value": "159k8cDL8hZooz-dsHE6ueWf68mBkHhxVCKnWm-lYLqs", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/159k8cDL8hZooz-dsHE6ueWf68mBkHhxVCKnWm-lYLqs/edit?usp=drivesdk", "cachedResultName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "tyg7FJlIITkSazyi", "name": "<PERSON>'s Google"}}, "typeVersion": 4.5}, {"id": "2c7b7939-4ca1-4868-92bf-5fd7384a1103", "name": "sort_saved", "type": "n8n-nodes-base.sort", "position": [900, 240], "parameters": {"options": {}, "sortFieldsUi": {"sortField": [{"fieldName": "Ref"}]}}, "typeVersion": 1}, {"id": "4e1d9064-6dda-4a01-af48-f278792f8b6b", "name": "last_saved", "type": "n8n-nodes-base.limit", "position": [1160, 240], "parameters": {"keep": "lastItems", "maxItems": 10}, "typeVersion": 1}, {"id": "6eb2053a-1101-477b-86e9-113813be2d92", "name": "saved_last", "type": "n8n-nodes-base.set", "position": [1420, 240], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "50097932-ab91-4af7-9412-925fab1982f0", "name": "id", "type": "string", "value": "={{ $json.Ref }}"}]}}, "typeVersion": 3.4}, {"id": "afd986f1-0c49-4a69-b948-aefcbff1010f", "name": "sort_strava", "type": "n8n-nodes-base.sort", "position": [660, 480], "parameters": {"options": {}, "sortFieldsUi": {"sortField": [{"fieldName": "id"}]}}, "typeVersion": 1}, {"id": "16094d29-f35b-492c-9d93-3145dab30cd3", "name": "last_strava", "type": "n8n-nodes-base.limit", "position": [1160, 480], "parameters": {"keep": "lastItems", "maxItems": 10}, "typeVersion": 1}, {"id": "9986360b-fcd3-42f4-ad13-aea69f6d1a80", "name": "sort_results", "type": "n8n-nodes-base.sort", "position": [660, 740], "parameters": {"options": {}, "sortFieldsUi": {"sortField": [{"fieldName": "id"}]}}, "typeVersion": 1}], "pinData": {}, "connections": {"Code": {"main": [[{"node": "sort_results", "type": "main", "index": 0}]]}, "Strava": {"main": [[{"node": "sort_strava", "type": "main", "index": 0}]]}, "activities": {"main": [[{"node": "sort_saved", "type": "main", "index": 0}]]}, "last_saved": {"main": [[{"node": "saved_last", "type": "main", "index": 0}]]}, "saved_last": {"main": [[{"node": "Strava", "type": "main", "index": 0}]]}, "sort_saved": {"main": [[{"node": "last_saved", "type": "main", "index": 0}]]}, "last_strava": {"main": [[{"node": "strava_last", "type": "main", "index": 0}]]}, "sort_strava": {"main": [[{"node": "Remove Duplicates", "type": "main", "index": 0}]]}, "strava_last": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "sort_results": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "activities", "type": "main", "index": 0}]]}, "Remove Duplicates": {"main": [[{"node": "last_strava", "type": "main", "index": 0}]]}}}