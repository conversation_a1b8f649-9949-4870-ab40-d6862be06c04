{"id": "2DzQ1FH11S3Gp6wn", "meta": {"instanceId": "558d88703fb65b2d0e44613bc35916258b0f0bf983c5d4730c00c424b77ca36a", "templateCredsSetupCompleted": true}, "name": "YogiAI", "tags": [], "nodes": [{"id": "2afc390e-d774-4db4-a52f-138f13837646", "name": "Azure OpenAI Chat Model2", "type": "@n8n/n8n-nodes-langchain.lmChatAzureOpenAi", "position": [1040, 0], "parameters": {"model": "4o", "options": {"temperature": 0.8}}, "credentials": {"azureOpenAiApi": {"id": "5AjoWhww5SQi2VXd", "name": "Azure Open AI account"}}, "typeVersion": 1}, {"id": "529d9ed9-3ae5-41cb-983e-874aa37ee1c7", "name": "YogaLog", "type": "n8n-nodes-base.googleSheets", "position": [2240, -100], "parameters": {"columns": {"value": {"Date": "={{ $('Trigger 2130 YogaPosesToday').first().json.timestamp }}", "JSON": "={{ $('CombineAll').item.json.LineBody }}", "Text": "={{ $('RewritePosesToday').item.json.text }}"}, "schema": [{"id": "Date", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Text", "type": "string", "display": true, "required": false, "displayName": "Text", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "JSON", "type": "string", "display": true, "removed": false, "required": false, "displayName": "JSON", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["Date"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": *********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1s_yzDNbbtXhfoOKUlmBHwhgWkR2FuoiKz4WQOu4tQmk/edit#gid=*********", "cachedResultName": "YogaLog"}, "documentId": {"__rl": true, "mode": "list", "value": "1s_yzDNbbtXhfoOKUlmBHwhgWkR2FuoiKz4WQOu4tQmk", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1s_yzDNbbtXhfoOKUlmBHwhgWkR2FuoiKz4WQOu4tQmk/edit?usp=drivesdk", "cachedResultName": "SerenityAI"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "TKSdrVOdpgxWBVk8", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "d491b5c3-31ab-49b2-abc3-8c2a67cf9571", "name": "Azure OpenAI Chat Model3", "type": "@n8n/n8n-nodes-langchain.lmChatAzureOpenAi", "position": [2360, 120], "parameters": {"model": "4o", "options": {}}, "credentials": {"azureOpenAiApi": {"id": "5AjoWhww5SQi2VXd", "name": "Azure Open AI account"}}, "typeVersion": 1}, {"id": "3b1ebdcb-9e6b-437c-8f51-944218c0c276", "name": "Auto-fixing Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserAutofixing", "position": [2520, 80], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "3fc67522-501c-4e43-bf9d-b367d57ad4f9", "name": "Azure OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatAzureOpenAi", "position": [2540, 120], "parameters": {"model": "4o", "options": {}}, "credentials": {"azureOpenAiApi": {"id": "5AjoWhww5SQi2VXd", "name": "Azure Open AI account"}}, "typeVersion": 1}, {"id": "19bd208b-a5c5-47b3-a2cc-e92a71444be7", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [2640, 220], "parameters": {"schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"yogaPoses\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"sequence\": { \"type\": \"integer\" },\n          \"name\": { \"type\": \"string\" }\n        },\n        \"required\": [\"sequence\", \"name\"],\n        \"additionalProperties\": false\n      }\n    }\n  },\n  \"required\": [\"yogaPoses\"]\n}"}, "typeVersion": 1.2}, {"id": "2bf3f3d3-84c7-4fd4-b1b9-8c0fb7df44b1", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [2400, -60], "parameters": {"text": "=You'll change this into properly format of JSON without having emoji. You'll also make sure the name is matched the data in googlesheet\n\n {{ $json.Text }}", "options": {}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.7}, {"id": "fa3fc89e-54d8-4706-af59-72dbd80fbef4", "name": "PosesDatabase1", "type": "n8n-nodes-base.googleSheetsTool", "position": [2480, 220], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "list", "value": **********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1s_yzDNbbtXhfoOKUlmBHwhgWkR2FuoiKz4WQOu4tQmk/edit#gid=**********", "cachedResultName": "Yoga"}, "documentId": {"__rl": true, "mode": "list", "value": "1s_yzDNbbtXhfoOKUlmBHwhgWkR2FuoiKz4WQOu4tQmk", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1s_yzDNbbtXhfoOKUlmBHwhgWkR2FuoiKz4WQOu4tQmk/edit?usp=drivesdk", "cachedResultName": "SerenityAI"}, "descriptionType": "manual", "toolDescription": "Yoga Poses Database to read\n"}, "credentials": {"googleSheetsOAuth2Api": {"id": "TKSdrVOdpgxWBVk8", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "ff7bd540-c89b-43d1-bb07-bb060a6b4ba6", "name": "YogaLog2", "type": "n8n-nodes-base.googleSheets", "position": [2980, 20], "parameters": {"columns": {"value": {"Date": "={{ $('Trigger 2130 YogaPosesToday').first().json.timestamp }}", "Pose": "={{ $json.name }}", "Sequence": "={{ $json.sequence }}"}, "schema": [{"id": "Date", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Sequence", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Sequence", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Pose", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Pose", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["Date"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": **********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1s_yzDNbbtXhfoOKUlmBHwhgWkR2FuoiKz4WQOu4tQmk/edit#gid=**********", "cachedResultName": "YogaLog2"}, "documentId": {"__rl": true, "mode": "list", "value": "1s_yzDNbbtXhfoOKUlmBHwhgWkR2FuoiKz4WQOu4tQmk", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1s_yzDNbbtXhfoOKUlmBHwhgWkR2FuoiKz4WQOu4tQmk/edit?usp=drivesdk", "cachedResultName": "SerenityAI"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "TKSdrVOdpgxWBVk8", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "f649c5b9-fad1-412c-8389-ed53b95e5583", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [2740, -120], "parameters": {"options": {}, "fieldToSplitOut": "output.yoga<PERSON><PERSON>s"}, "typeVersion": 1}, {"id": "8194e695-fa9e-4555-9da5-b7dbdc1b0e4a", "name": "Trigger 2130 YogaPosesToday", "type": "n8n-nodes-base.scheduleTrigger", "position": [-200, -20], "parameters": {"rule": {"interval": [{"triggerAtHour": 21, "triggerAtMinute": 30}]}}, "typeVersion": 1.2}, {"id": "3b5706d8-4968-4b9c-a255-7d1f806d85dc", "name": "Azure OpenAI Chat Model5", "type": "@n8n/n8n-nodes-langchain.lmChatAzureOpenAi", "position": [1340, 180], "parameters": {"model": "4o", "options": {"temperature": 0.9}}, "credentials": {"azureOpenAiApi": {"id": "5AjoWhww5SQi2VXd", "name": "Azure Open AI account"}}, "typeVersion": 1}, {"id": "acee6e43-f094-4f30-bffb-6c56b0425327", "name": "Get PoseName", "type": "n8n-nodes-base.googleSheets", "position": [40, -20], "parameters": {"options": {"dataLocationOnSheet": {"values": {"range": "B18:D28", "rangeDefinition": "specifyRangeA1"}}}, "sheetName": {"__rl": true, "mode": "list", "value": **********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1s_yzDNbbtXhfoOKUlmBHwhgWkR2FuoiKz4WQOu4tQmk/edit#gid=**********", "cachedResultName": "NotePad"}, "documentId": {"__rl": true, "mode": "list", "value": "1s_yzDNbbtXhfoOKUlmBHwhgWkR2FuoiKz4WQOu4tQmk", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1s_yzDNbbtXhfoOKUlmBHwhgWkR2FuoiKz4WQOu4tQmk/edit?usp=drivesdk", "cachedResultName": "SerenityAI"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "TKSdrVOdpgxWBVk8", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "6be3a88c-4e0f-44e6-97c1-eafa13230ae7", "name": "WritePoses<PERSON><PERSON>y", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1120, -160], "parameters": {"text": "=Let's calm down and focus on these poses today.\n\n{{ $('Code').item.json.poseNamesOnly }}\n\nhave a great practice!", "messages": {"messageValues": [{"message": "=You're experienced yoga instructor. You'll say the topic and asking the student to focus on practice today. You'll later give the yoga poses list for practicing today. You will also include related pose or variation from the list. You'll make sure to include all the poses from the list.\n"}]}, "promptType": "define"}, "retryOnFail": true, "typeVersion": 1.5}, {"id": "8d9cdf4c-a432-44ff-a0a3-133fbc8e9daa", "name": "RewritePosesToday", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1180, 60], "parameters": {"text": "={{ $json.text }}", "messages": {"messageValues": [{"message": "=You'll format and add emoji before the poses name to make it chat-friendly to send via Line. You will only return the message to be sent.\n\nIf the message is too long, you'll split by ====== to 3 messages\n"}]}, "promptType": "define"}, "retryOnFail": true, "typeVersion": 1.5}, {"id": "2e419654-1f83-48df-8ac0-9ec621444cc2", "name": "Azure OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatAzureOpenAi", "position": [480, 100], "parameters": {"model": "4o", "options": {"temperature": 0.9}}, "credentials": {"azureOpenAiApi": {"id": "5AjoWhww5SQi2VXd", "name": "Azure Open AI account"}}, "typeVersion": 1}, {"id": "ad1bf966-114e-4bb8-abff-f5768e907aff", "name": "WriteJSONflex", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [580, -80], "parameters": {"text": "={{ $json.outputText }}", "messages": {"messageValues": [{"message": "=You are JSON parser, you'll write <PERSON><PERSON><PERSON> in this format for all the row in 'GetPoseName' You'll notice the differnet between uri and url.\n\n{\n\"type\": \"bubble\",\n\"hero\": {\n\"type\": \"image\",\n\"url\": \"https://pocketyoga.com/assets/images/thumbnails146/SupineAngle-tn146.png \", \n\"size\": \"full\",\n\"aspectRatio\": \"20:13\",\n\"aspectMode\": \"fit\",\n\"action\": {\n\"type\": \"uri\",\n\"uri\": \"https://pocketyoga.com/pose/SupineAngle \"\n}\n},\n\"body\": {\n\"type\": \"box\",\n\"layout\": \"vertical\",\n\"contents\": [\n{\n\"type\": \"text\",\n\"text\": \"<PERSON>pine Angle (supta konasana)\",\n\"size\": \"lg\",\n\"wrap\": true,\n\"action\": {\n\"type\": \"message\",\n\"label\": \"action\",\n\"text\": \"<PERSON>pine Angle (supta konasana)\"\n}\n}\n]\n}\n},\n{\n\"type\": \"bubble\",\n\"hero\": {\n\"type\": \"image\",\n\"url\": \"https://pocketyoga.com/assets/images/thumbnails146/SupineAngle-tn146.png \",\n\"size\": \"full\",\n\"aspectRatio\": \"20:13\",\n\"aspectMode\": \"fit\",\n\"action\": {\n\"type\": \"uri\",\n\"uri\": \"https://pocketyoga.com/pose/SupineAngle \"\n}\n},\n\"body\": {\n\"type\": \"box\",\n\"layout\": \"vertical\",\n\"contents\": [\n{\n\"type\": \"text\",\n\"text\": \"Supine Angle (supta konasana)\",\n\"size\": \"lg\",\n\"wrap\": true,\n\"action\": {\n\"type\": \"message\",\n\"label\": \"action\",\n\"text\": \"Supine Angle (supta konasana)\"\n}\n}\n]\n}\n}"}]}, "promptType": "define", "hasOutputParser": true}, "retryOnFail": true, "typeVersion": 1.5}, {"id": "1e6af9e5-675d-4d9a-aba6-304d218ea138", "name": "Structured Output Parser1", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [780, 220], "parameters": {"jsonSchemaExample": "[\n  {\n    \"type\": \"bubble\",\n    \"hero\": {\n      \"type\": \"image\",\n      \"url\": \"https://pocketyoga.com/assets/images/thumbnails146/SupineAngle-tn146.png\",\n      \"size\": \"full\",\n      \"aspectRatio\": \"20:13\",\n      \"aspectMode\": \"fit\",\n      \"action\": {\n        \"type\": \"uri\",\n        \"uri\": \"https://pocketyoga.com/pose/SupineAngle\"\n      }\n    },\n    \"body\": {\n      \"type\": \"box\",\n      \"layout\": \"vertical\",\n      \"contents\": [\n        {\n          \"type\": \"text\",\n          \"text\": \"<PERSON>pine Angle (supta konasana)\",\n          \"size\": \"lg\",\n          \"wrap\": true,\n          \"action\": {\n            \"type\": \"message\",\n            \"label\": \"action\",\n            \"text\": \"<PERSON><PERSON>le (supta konasana)\"\n          }\n        }\n      ]\n    }\n  },\n  {\n    \"type\": \"bubble\",\n    \"hero\": {\n      \"type\": \"image\",\n      \"url\": \"https://pocketyoga.com/assets/images/thumbnails146/SupineAngle-tn146.png\",\n      \"size\": \"full\",\n      \"aspectRatio\": \"20:13\",\n      \"aspectMode\": \"fit\",\n      \"action\": {\n        \"type\": \"uri\",\n        \"uri\": \"https://pocketyoga.com/pose/SupineAngle\"\n      }\n    },\n    \"body\": {\n      \"type\": \"box\",\n      \"layout\": \"vertical\",\n      \"contents\": [\n        {\n          \"type\": \"text\",\n          \"text\": \"Supine Angle (supta konasana)\",\n          \"size\": \"lg\",\n          \"wrap\": true,\n          \"action\": {\n            \"type\": \"message\",\n            \"label\": \"action\",\n            \"text\": \"Supine Angle (supta konasana)\"\n          }\n        }\n      ]\n    }\n  }\n]"}, "typeVersion": 1.2}, {"id": "5559c5b9-6c2c-4adb-9544-79be3f1f85d1", "name": "Auto-fixing Output Parser1", "type": "@n8n/n8n-nodes-langchain.outputParserAutofixing", "position": [680, 80], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "2335f74e-3fe6-4720-bb88-1bbda320ae8b", "name": "Azure OpenAI Chat Model6", "type": "@n8n/n8n-nodes-langchain.lmChatAzureOpenAi", "position": [640, 200], "parameters": {"model": "4o", "options": {}}, "credentials": {"azureOpenAiApi": {"id": "5AjoWhww5SQi2VXd", "name": "Azure Open AI account"}}, "typeVersion": 1}, {"id": "83d02971-bdf6-4c45-b705-f2f49fa49525", "name": "Azure OpenAI Chat Model4", "type": "@n8n/n8n-nodes-langchain.lmChatAzureOpenAi", "position": [1780, 200], "parameters": {"model": "4o", "options": {"temperature": 0.5}}, "credentials": {"azureOpenAiApi": {"id": "5AjoWhww5SQi2VXd", "name": "Azure Open AI account"}}, "typeVersion": 1}, {"id": "5e5c1c11-cf3d-47f4-91ce-14d7e3f493fb", "name": "Code", "type": "n8n-nodes-base.code", "position": [240, -20], "parameters": {"jsCode": "const items = $input.all();\n\nlet outputText = \"\";\nlet poseNamesList = []; // New list to store only PoseNames\n\nitems.forEach(item => {\n  const { PoseName, uri, url } = item.json;\n  outputText += `Name: ${PoseName}\\nuri: ${uri}\\nurl: ${url}\\n\\n`;\n  poseNamesList.push(PoseName); // Add PoseName to the list\n});\n\nreturn [\n  {\n    json: {\n      outputText,  // Original formatted text\n      poseNamesOnly: poseNamesList.join('\\n') // New: PoseNames as text list\n    }\n  }\n];"}, "typeVersion": 2}, {"id": "864e2fbb-a9dc-43ba-918e-0197821de598", "name": "Line Push with Flex Bubble", "type": "n8n-nodes-base.httpRequest", "position": [1980, -80], "parameters": {"url": "https://api.line.me/v2/bot/message/push", "method": "POST", "options": {}, "jsonBody": "={{ $json.text }}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "yiPG7xPwvDzsY0Qd", "name": "Line @511dizji"}}, "retryOnFail": false, "typeVersion": 4.2}, {"id": "91e50734-8899-4d23-9a4f-ce637d9e5ed1", "name": "CombineAll", "type": "n8n-nodes-base.set", "position": [1640, -100], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9c82e62c-dfbc-4b09-899d-f4d1581e1c15", "name": "LineBody", "type": "string", "value": "={\n  \"to\": \"Ue9cc622e33e5333e3784298412ec9aed\",\n  \"messages\": [\n    {\n      \"type\": \"text\",\n      \"text\": \"{{ $json.text.replaceAll(\"\\n\",\"\\\\n\").replaceAll(\"\\n\",\"\").removeMarkdown().removeTags().replaceAll('\"',\"\") }}\"\n    },\n    {\n      \"type\": \"flex\",\n      \"altText\": \"Yoga Poses Images\",\n      \"contents\": {\n        \"type\": \"carousel\",\n        \"contents\": [  {{ $('WriteJSONflex').all().flatMap(item => JSON.stringify(item.json.output)).join(',') }}\n\n          ] \n      }\n    }\n  ]\n}"}]}}, "typeVersion": 3.4}, {"id": "dc6d5dfe-66ad-49ca-b246-ee52f270269d", "name": "Fix JSON", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1720, 120], "parameters": {"text": "=Fix this JSON\n\n{{ $json.LineBody }}", "messages": {"messageValues": [{"message": "=You are JSON formatter, You'll fix the JSON and return only the JSON that has been fixed. Do not explain or write anything else"}]}, "promptType": "define", "hasOutputParser": true}, "retryOnFail": true, "typeVersion": 1.5}, {"id": "a062cf06-f438-4d1e-9c0c-d2fc00f40071", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-300, -380], "parameters": {"color": 5, "width": 260, "height": 240, "content": "## YogiAI\n\nThis YogiAI is to provide daily reminder and pose of the day to the user via Line Push Message\n\nThe data will be generated from GoogleSheet Weighted Random Poses and Push to your Line at the scheduled time\n\n"}, "typeVersion": 1}, {"id": "8d2184a9-af30-4b1e-826b-69a8f37d8256", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [0, -320], "parameters": {"color": 4, "width": 400, "height": 500, "content": "## Get the Data\nThis is to get the data from GoogleSheet \n\nIn the range we got, we'll have <PERSON><PERSON><PERSON><PERSON>, uri (image link), and url (link when clicked) \n\nThe sample is here \nhttps://docs.google.com/spreadsheets/d/1eqLJsUL_QkOMy_qPzNCrUCZdx36asC8P1i3PowTQqLY/edit?usp=sharing\n\nThe data is from https://pocketyoga.com/pose/\n\n***YOU SHOULD UPDATE IT WITH YOUR OWN DATA***"}, "typeVersion": 1}, {"id": "6d688a1c-90aa-4c3a-a868-946c61cec7cf", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [440, -320], "parameters": {"color": 2, "width": 540, "height": 660, "content": "## Write FlexMessage for Images\n\nTo send the information in Line, we need to write a JSON for Flex Message meaning that it can slides to show the images of the pose\n\nWe use auto-parser here to make sure the JSON followed the required format\n\nhttps://developers.line.biz/en/docs/messaging-api/using-flex-messages/\n\nYou can also use https://developers.line.biz/flex-simulator/?status=success to simulate the format "}, "typeVersion": 1}, {"id": "e5b92f32-e282-49cd-8084-68e380572ee9", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1000, -320], "parameters": {"color": 2, "width": 540, "height": 660, "content": "## Write Text for Poses today \n\nThis node we want to have user friendly text such as with emojis, etc. So, we give Azure OpenAI the poses of today and ask it to rewrie"}, "typeVersion": 1}, {"id": "38cc9d2f-be2c-4448-9746-5d533108df7c", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1600, -320], "parameters": {"color": 3, "width": 540, "height": 660, "content": "## Combine the result and push it via Line\n\n1) We used 'Edit Field' to combine all the output\n(Hint: you can have input_txt and output_txt to debug your script here)\n2) To make sure that our JSON is proper, we asked AI to fix it again. \n3) Use Line Push >> Please replace \"to\" to your own UID and create the header authorization with the channel you have\n\nhttps://developers.line.biz/en/docs/messaging-api/sending-messages/\n"}, "typeVersion": 1}, {"id": "b88d6f78-ce54-4b83-b009-e4e22e518c7c", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [2200, -320], "parameters": {"color": 6, "width": 1020, "height": 660, "content": "## Write back the data into Log and Log2 \n\nWe used log2 to count how many time we send each poses and weighted this back into the 'Yoga' Sheet to make the random more random ;)\n\nTo put the data back, we also want to extract from the output and split it out to put back to google sheet"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"timezone": "Asia/Bangkok", "callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1"}, "versionId": "8d3482ff-25e6-479f-a33b-b33d1aeb51fc", "connections": {"Code": {"main": [[{"node": "WriteJSONflex", "type": "main", "index": 0}]]}, "YogaLog": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Fix JSON": {"main": [[{"node": "Line Push with Flex Bubble", "type": "main", "index": 0}]]}, "YogaLog2": {"main": [[]]}, "Split Out": {"main": [[{"node": "YogaLog2", "type": "main", "index": 0}]]}, "CombineAll": {"main": [[{"node": "Fix JSON", "type": "main", "index": 0}]]}, "Get PoseName": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "WriteJSONflex": {"main": [[{"node": "WritePoses<PERSON><PERSON>y", "type": "main", "index": 0}]]}, "PosesDatabase1": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "WritePosesToday": {"main": [[{"node": "RewritePosesToday", "type": "main", "index": 0}]]}, "RewritePosesToday": {"main": [[{"node": "CombineAll", "type": "main", "index": 0}]]}, "Azure OpenAI Chat Model": {"ai_languageModel": [[{"node": "WriteJSONflex", "type": "ai_languageModel", "index": 0}]]}, "Azure OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Auto-fixing Output Parser", "type": "ai_languageModel", "index": 0}]]}, "Azure OpenAI Chat Model2": {"ai_languageModel": [[{"node": "WritePoses<PERSON><PERSON>y", "type": "ai_languageModel", "index": 0}]]}, "Azure OpenAI Chat Model3": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Azure OpenAI Chat Model4": {"ai_languageModel": [[{"node": "Fix JSON", "type": "ai_languageModel", "index": 0}]]}, "Azure OpenAI Chat Model5": {"ai_languageModel": [[{"node": "RewritePosesToday", "type": "ai_languageModel", "index": 0}]]}, "Azure OpenAI Chat Model6": {"ai_languageModel": [[{"node": "Auto-fixing Output Parser1", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Auto-fixing Output Parser", "type": "ai_outputParser", "index": 0}]]}, "Auto-fixing Output Parser": {"ai_outputParser": [[{"node": "AI Agent", "type": "ai_outputParser", "index": 0}]]}, "Structured Output Parser1": {"ai_outputParser": [[{"node": "Auto-fixing Output Parser1", "type": "ai_outputParser", "index": 0}]]}, "Auto-fixing Output Parser1": {"ai_outputParser": [[{"node": "WriteJSONflex", "type": "ai_outputParser", "index": 0}]]}, "Line Push with Flex Bubble": {"main": [[{"node": "YogaLog", "type": "main", "index": 0}]]}, "Trigger 2130 YogaPosesToday": {"main": [[{"node": "Get PoseName", "type": "main", "index": 0}]]}}}