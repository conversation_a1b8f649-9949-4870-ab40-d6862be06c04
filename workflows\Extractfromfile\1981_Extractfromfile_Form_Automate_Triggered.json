{"id": "t1P14FvfibKYCh3E", "meta": {"instanceId": "a4bfc93e975ca233ac45ed7c9227d84cf5a2329310525917adaf3312e10d5462", "templateCredsSetupCompleted": true}, "name": "HR-focused automation pipeline with AI", "tags": [], "nodes": [{"id": "b1092f93-502c-4af0-962e-2b69311b92a3", "name": "On form submission", "type": "n8n-nodes-base.formTrigger", "position": [-520, -200], "webhookId": "2a87705d-8ba1-41f1-80ef-85f364ce253e", "parameters": {"options": {}, "formTitle": "Send CV", "formFields": {"values": [{"fieldLabel": "Name", "placeholder": "Name", "requiredField": true}, {"fieldType": "email", "fieldLabel": "Email", "placeholder": "Email", "requiredField": true}, {"fieldType": "file", "fieldLabel": "CV", "requiredField": true, "acceptFileTypes": ".pdf"}]}}, "typeVersion": 2.2}, {"id": "77edfe2a-4c6a-48c8-8dc9-b275491be090", "name": "Extract from File", "type": "n8n-nodes-base.extractFromFile", "position": [-160, -200], "parameters": {"options": {}, "operation": "pdf", "binaryPropertyName": "CV"}, "typeVersion": 1}, {"id": "ebf2e194-3515-4c0a-8745-790b63bf336f", "name": "Qualifications", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [160, -100], "parameters": {"text": "={{ $json.text }}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}, "attributes": {"attributes": [{"name": "Educational qualification", "required": true, "description": "Summary of your academic career. Focus on your high school and university studies. Summarize in 100 words maximum and also include your grade if applicable."}, {"name": "Job History", "required": true, "description": "Work history summary. Focus on your most recent work experiences. Summarize in 100 words maximum"}, {"name": "Skills", "required": true, "description": "Extract the candidate’s technical skills. What software and frameworks they are proficient in. Make a bulleted list."}]}}, "typeVersion": 1}, {"id": "4f40404c-1d47-4bde-9b4b-16367cf11e4f", "name": "Summarization Chain", "type": "@n8n/n8n-nodes-langchain.chainSummarization", "position": [900, -220], "parameters": {"options": {"summarizationMethodAndPrompts": {"values": {"prompt": "=Write a concise summary of the following:\n\nCity: {{ $json.output.city }}\nBirthdate: {{ $json.output.birthdate }}\nEducational qualification: {{ $json.output[\"Educational qualification\"] }}\nJob History: {{ $json.output[\"Job History\"] }}\nSkills: {{ $json.output.Skills }}\n\nUse 100 words or less. Be concise and conversational.", "combineMapPrompt": "=Write a concise summary of the following:\n\nCity: {{ $json.output.city }}\nBirthdate: {{ $json.output.birthdate }}\nEducational qualification: {{ $json.output[\"Educational qualification\"] }}\nJob History: {{ $json.output[\"Job History\"] }}\nSkills: {{ $json.output.Skills }}\n\nUse 100 words or less. Be concise and conversational."}}}}, "typeVersion": 2}, {"id": "9f9c5f16-1dc2-4928-aef8-284daeb6be51", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [660, -220], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineAll"}, "typeVersion": 3}, {"id": "51bd14cc-2c54-4f72-b162-255f7e277aff", "name": "Profile Wanted", "type": "n8n-nodes-base.set", "position": [1300, -220], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "a3d049b0-5a70-4e7b-a6f2-81447da5282a", "name": "profile_wanted", "type": "string", "value": "We are a web agency and we are looking for a full-stack web developer who knows how to use PHP, Python and Javascript. He has experience in the sector and lives in Northern Italy."}]}}, "typeVersion": 3.4}, {"id": "4a120e5d-b849-4a29-b7f3-12c653552367", "name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [1960, -220], "parameters": {"columns": {"value": {"CITY": "={{ $('Merge').item.json.output.city }}", "DATA": "={{ $now.format('dd/LL/yyyy') }}", "NAME": "={{ $('On form submission').item.json.Nome }}", "VOTE": "={{ $json.output.vote }}", "EMAIL": "={{ $('On form submission').item.json.Email }}", "SKILLS": "={{ $('Merge').item.json.output.Skills }}", "TELEFONO": "={{ $('Merge').item.json.output.telephone }}", "SUMMARIZE": "={{ $('Summarization Chain').item.json.response.text }}", "EDUCATIONAL": "={{ $('Merge').item.json.output[\"Educational qualification\"] }}", "JOB HISTORY": "={{ $('Merge').item.json.output[\"Job History\"] }}", "DATA NASCITA": "={{ $('Merge').item.json.output.birthdate }}", "CONSIDERATION": "={{ $json.output.consideration }}"}, "schema": [{"id": "DATA", "type": "string", "display": true, "required": false, "displayName": "DATA", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "NAME", "type": "string", "display": true, "required": false, "displayName": "NAME", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "PHONE", "type": "string", "display": true, "removed": false, "required": false, "displayName": "PHONE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CITY", "type": "string", "display": true, "required": false, "displayName": "CITY", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "EMAIL", "type": "string", "display": true, "required": false, "displayName": "EMAIL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "DATA NASCITA", "type": "string", "display": true, "required": false, "displayName": "DATA NASCITA", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "EDUCATIONAL", "type": "string", "display": true, "required": false, "displayName": "EDUCATIONAL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "JOB HISTORY", "type": "string", "display": true, "required": false, "displayName": "JOB HISTORY", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SKILLS", "type": "string", "display": true, "required": false, "displayName": "SKILLS", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SUMMARIZE", "type": "string", "display": true, "required": false, "displayName": "SUMMARIZE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "VOTE", "type": "string", "display": true, "removed": false, "required": false, "displayName": "VOTE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CONSIDERATION", "type": "string", "display": true, "required": false, "displayName": "CONSIDERATION", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ssz5RvN1Hr20Q31pnYnbjCLu1MGBvoLttBAjXunMRQE/edit#gid=0", "cachedResultName": "Foglio1"}, "documentId": {"__rl": true, "mode": "list", "value": "1ssz5RvN1Hr20Q31pnYnbjCLu1MGBvoLttBAjXunMRQE", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ssz5RvN1Hr20Q31pnYnbjCLu1MGBvoLttBAjXunMRQE/edit?usp=drivesdk", "cachedResultName": "Ricerca WebDev"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "a154d8a5-9f85-45bb-b082-f702c13c3507", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [1720, -20], "parameters": {"schemaType": "manual", "inputSchema": "{\n\t\"type\": \"object\",\n\t\"properties\": {\n\t\t\"vote\": {\n\t\t\t\"type\": \"string\"\n\t\t},\n\t\t\"consideration\": {\n\t\t\t\"type\": \"string\"\n\t\t}\n\t}\n}"}, "typeVersion": 1.2}, {"id": "037ac851-7885-4b78-ac75-dfa0ebb6003d", "name": "HR Expert", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1560, -220], "parameters": {"text": "=Profilo ricercato:\n{{ $json.profile_wanted }}\n\nCandidato:\n{{ $('Summarization Chain').item.json.response.text }}", "messages": {"messageValues": [{"message": "Sei un esperto HR e devi capire se il candidato è in linea con il profilo ricercato dall'azienda.\n\nDevi dare un voto da 1 a 10 dove 1 significa che il candidato non è in linea con quanto richiesto mentre 10 significa che è il candidato ideale perchè rispecchia in toto il profilo cercato.\n\nInoltre nel campo \"consideration\" motiva il perchè hai dato quel voto. "}]}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.5}, {"id": "ed5744c4-df06-4a01-a103-af4dd470d482", "name": "Personal Data", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [160, -280], "parameters": {"text": "={{ $json.text }}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}, "schemaType": "manual", "inputSchema": "{\n\t\"type\": \"object\",\n\t\"properties\": {\n\t\t\"telephone\": {\n\t\t\t\"type\": \"string\"\n\t\t},\n      \"city\": {\n\t\t\t\"type\": \"string\"\n\t\t},\n      \"birthdate\": {\n\t\t\t\"type\": \"string\"\n\t\t}\n\t}\n}"}, "typeVersion": 1}, {"id": "181c1249-b05c-4c35-8cac-5f9738cc1fe6", "name": "Upload CV", "type": "n8n-nodes-base.googleDrive", "position": [-160, -380], "parameters": {"name": "=CV-{{ $now.format('yyyyLLdd') }}-{{ $json.CV[0].filename }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "1tzeSpx4D3EAGXa3Wg-gqGbdaUk6LIZTV", "cachedResultUrl": "https://drive.google.com/drive/folders/1tzeSpx4D3EAGXa3Wg-gqGbdaUk6LIZTV", "cachedResultName": "CV"}, "inputDataFieldName": "CV"}, "credentials": {"googleDriveOAuth2Api": {"id": "HEy5EuZkgPZVEa9w", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "d31ee1c4-e4be-41d9-8f36-e6fb797ced8e", "name": "OpenAI", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [920, 240], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "CDX6QM4gLYanh0P4", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "0290cb72-a581-4aff-8b5d-1aa63e0a630f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-560, -680], "parameters": {"color": 3, "width": 540, "content": "## HR Expert \nThis workflow automates the process of handling job applications by extracting relevant information from submitted CVs, analyzing the candidate's qualifications against a predefined profile, and storing the results in a Google Sheet"}, "typeVersion": 1}, {"id": "361084ff-9735-4a56-8988-be573391838b", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-240, -460], "parameters": {"width": 300, "height": 420, "content": "The CV is uploaded to Google Drive and converted so that it can be processed\n"}, "typeVersion": 1}, {"id": "4b6f004f-c77b-4522-99d4-737a68f6cfac", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [120, -380], "parameters": {"width": 360, "height": 440, "content": "The essential information for evaluating the candidate is collected in two different chains"}, "typeVersion": 1}, {"id": "73e11af9-65e3-4fcd-bb99-8a3f212ce9fb", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [860, -300], "parameters": {"width": 320, "height": 240, "content": "Summary of relevant information useful for classifying the candidate"}, "typeVersion": 1}, {"id": "606711d1-8e6d-44b3-91ac-c047d8a4054f", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1240, -300], "parameters": {"width": 220, "height": 240, "content": "Characteristics of the profile sought by the company that intends to hire the candidate"}, "typeVersion": 1}, {"id": "89c3210c-c599-41dc-97a3-bf8df2beb751", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1500, -300], "parameters": {"width": 360, "height": 240, "content": "Candidate evaluation with vote and considerations of the HR agent relating the profile sought with the candidate's skills"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "594728c0-b842-404d-8810-c6f7f3f4631d", "connections": {"Merge": {"main": [[{"node": "Summarization Chain", "type": "main", "index": 0}]]}, "OpenAI": {"ai_languageModel": [[{"node": "Qualifications", "type": "ai_languageModel", "index": 0}, {"node": "Summarization Chain", "type": "ai_languageModel", "index": 0}, {"node": "HR Expert", "type": "ai_languageModel", "index": 0}, {"node": "Personal Data", "type": "ai_languageModel", "index": 0}]]}, "HR Expert": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}, "Upload CV": {"main": [[]]}, "Personal Data": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Profile Wanted": {"main": [[{"node": "HR Expert", "type": "main", "index": 0}]]}, "Qualifications": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Extract from File": {"main": [[{"node": "Qualifications", "type": "main", "index": 0}, {"node": "Personal Data", "type": "main", "index": 0}]]}, "On form submission": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}, {"node": "Upload CV", "type": "main", "index": 0}]]}, "Summarization Chain": {"main": [[{"node": "Profile Wanted", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "HR Expert", "type": "ai_outputParser", "index": 0}]]}}}