{"meta": {"instanceId": "7599ed929ea25767a019b87ecbc83b90e16a268cb51892887b450656ac4518a2"}, "nodes": [{"id": "f3001828-f10b-41d5-a056-5327e1f694f3", "name": "HTML to PDF", "type": "@custom-js/n8n-nodes-pdf-toolkit.html2Pdf", "position": [-500, 380], "parameters": {"htmlInput": "<h1>Hello World</h1>"}, "credentials": {"customJsApi": {"id": "h29wo2anYKdANAzm", "name": "CustomJS account"}}, "typeVersion": 1}, {"id": "f3141220-b384-4efe-84f5-0a896b09a887", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-720, 460], "parameters": {}, "typeVersion": 1}, {"id": "cee26b9b-7109-4336-8d7e-762cf40b4d8b", "name": "Convert PDF into PNG1", "type": "@custom-js/n8n-nodes-pdf-toolkit.PdfToPng", "position": [-280, 540], "parameters": {"resource": "url", "field_name": "={{ $json.path }}"}, "credentials": {"customJsApi": {"id": "h29wo2anYKdANAzm", "name": "CustomJS account"}}, "typeVersion": 1}, {"id": "46f47df0-a301-41a9-8d3a-f98977b56eda", "name": "Convert PDF into PNG", "type": "@custom-js/n8n-nodes-pdf-toolkit.PdfToPng", "position": [-280, 380], "parameters": {}, "credentials": {"customJsApi": {"id": "h29wo2anYKdANAzm", "name": "CustomJS account"}}, "typeVersion": 1}, {"id": "e9932fd1-6325-4670-93ea-b31fcfacdaf7", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-560, 280], "parameters": {"color": 4, "width": 220, "height": 240, "content": "### HTML to PDF\n- Request HTML Data.\n- Convert HTML to PDF."}, "typeVersion": 1}, {"id": "f9c860c6-a648-4929-b15f-b9131aa987fe", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-340, 280], "parameters": {"color": 6, "height": 240, "content": "### Convert PDF into PNG \n- Convert the generated PNG from PDF"}, "typeVersion": 1}, {"id": "54c4cf3d-4a8a-405e-b32e-8b7a2d86b577", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-560, 520], "parameters": {"color": 3, "width": 220, "height": 240, "content": "\n\n\n\n\n\n\n\n\n\n\n\n### Set PDF URL\n- Request PDF from URL"}, "typeVersion": 1}, {"id": "ac8e1497-233c-4e42-8739-f161e4014a7f", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-340, 520], "parameters": {"color": 2, "height": 240, "content": "\n\n\n\n\n\n\n\n\n\n\n\n### Convert PDF into PNG\n- Convert the generated PNG from PDF"}, "typeVersion": 1}, {"id": "98dfdf38-6b1c-4fd3-b956-8d59f62b280d", "name": "Set PDF URL", "type": "n8n-nodes-base.code", "position": [-500, 540], "parameters": {"jsCode": "return {\"json\": {\"path\": \"https://www.nlbk.niedersachsen.de/download/164891/Test-pdf_3.pdf.pdf\"}};"}, "typeVersion": 2}], "pinData": {}, "connections": {"HTML to PDF": {"main": [[{"node": "Convert PDF into PNG", "type": "main", "index": 0}]]}, "Set PDF URL": {"main": [[{"node": "Convert PDF into PNG1", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "HTML to PDF", "type": "main", "index": 0}, {"node": "Set PDF URL", "type": "main", "index": 0}]]}}}