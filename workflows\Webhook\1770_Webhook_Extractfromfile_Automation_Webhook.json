{"id": "YKZBEx4DTf0KGEBR", "meta": {"instanceId": "f5267db717c7383a3924a6083f6b9950be64cf36e2b4e9421d42eb2121922a14"}, "name": "Image-Based Data Extraction API using Gemini AI", "tags": [], "nodes": [{"id": "e3448003-5c62-4da6-8fcc-6817915dcbb8", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [40, 40], "webhookId": "18118afb-7fd2-47a5-a474-50813c5b20c8", "parameters": {"path": "data-extractor", "options": {}, "responseMode": "responseNode"}, "typeVersion": 2}, {"id": "3682c6bf-3442-4fba-ab6c-ae29e361ef93", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [1180, 40], "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "bfa352d0-68a9-4f33-be54-254a5df22664", "name": "Get image from URL", "type": "n8n-nodes-base.httpRequest", "position": [280, 40], "parameters": {"url": "={{ $json.body.image_url }}", "options": {}}, "typeVersion": 4.2}, {"id": "c6c8de12-08dc-42e8-9c0e-86e04c7cacc0", "name": "Call Gemini API (Flash Lite) with Image", "type": "n8n-nodes-base.httpRequest", "position": [760, 40], "parameters": {"url": "=https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent", "method": "POST", "options": {}, "jsonBody": "={\n  \"contents\": [\n    {\n      \"role\": \"user\",\n      \"parts\": [\n        {\n          \"inlineData\": {\n            \"data\": \"{{$json.data1}}\",\n            \"mimeType\": \"image/jpeg\"\n          }\n        }\n      ]\n    },\n    {\n      \"role\": \"user\",\n      \"parts\": [\n        {\n          \"text\": \"check this\"\n        }\n      ]\n    }\n  ],\n  \"systemInstruction\": {\n    \"role\": \"user\",\n    \"parts\": [\n      {\n        \"text\": \"{{ $('Webhook').first().json.body.Requirement}}\"\n      }\n    ]\n  },\n  \"generationConfig\": {\n    \"temperature\": 1,\n    \"topK\": 40,\n    \"topP\": 0.95,\n    \"maxOutputTokens\": 8192,\n    \"responseMimeType\": \"application/json\",\n    \"responseSchema\": {\n      \"type\": \"object\",\n      \"properties\": {{ $('Webhook').first().json.body.properties.toJsonString()}}\n    }\n  }\n}\n", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "googlePalmApi"}, "credentials": {"googlePalmApi": {"id": "MhMVz0OkKPSPX2Wn", "name": "Gemini API Srinivasan Online"}}, "typeVersion": 4.2}, {"id": "06b0f807-aeba-44d6-bb1d-dfa1d50e1082", "name": "Edit fields to output required data alone", "type": "n8n-nodes-base.set", "position": [980, 40], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "4a2f1343-4b5d-4de8-b04b-5640e0a38d27", "name": "result", "type": "string", "value": "={{ $json.candidates[0].content.parts[0].text.parseJson()}}"}]}}, "typeVersion": 3.4}, {"id": "8c69dba2-f67c-4f8b-be18-02a414fd2ead", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [20, 280], "parameters": {"color": 5, "width": 820, "height": 420, "content": "## Sample API Call (cURL) \n```\ncurl --request GET \\\n  --url https://your_domain.com/webhook/data-extractor \\\n  --data '{\n  \"image_url\":\"https://www.immihelp.com/nri/images/sample-pan-card-front.jpg\",\n  \"Requirement\":\"extract the details from the image\",\n  \"properties\": {\n        \"PAN Number\": {\n          \"type\": \"string\"\n        },\n        \"Name\": {\n          \"type\": \"string\"\n        },\n        \"Date of Birth\": {\n          \"type\": \"string\"\n        },\n        \"Valid\": {\n          \"type\": \"boolean\"\n        }\n      }\n}'\n```"}, "typeVersion": 1}, {"id": "8839f0d7-306f-4dc2-aca5-6ca529e1a2ff", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [20, 740], "parameters": {"color": 5, "width": 1240, "height": 140, "content": "## Sample Output\n```\n{\n  \"result\": \"{\\\"Date of Birth\\\":\\\"23/11/1974\\\",\\\"Name\\\":\\\"RAHUL GUPTA\\\",\\\"PAN Number\\\":\\\"**********\\\",\\\"Valid\\\":true}\"\n}\n```"}, "typeVersion": 1}, {"id": "df733e11-f194-4878-a514-47ddc9811281", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [40, -520], "parameters": {"width": 940, "height": 440, "content": "## Convert the workflow into an Endpoint\n\nThis n8n workflow provides a ready-to-use API endpoint for extracting structured data from images. The API takes an image URL as input, processes it using an AI-powered OCR model, and returns relevant extracted details in a structured JSON format.\n\n- The workflow converts the image to base64 before processing.\n- It utilizes an AI-powered model (Gemini API) for text extraction.\n- The output is formatted to include only the required fields.\n- You can customize the extraction criteria by modifying the request parameters.\n- Supports integration with various applications for automated data entry and processing.\n\nIt can be used for various use cases, such as:\n\n- Document OCR (ID cards, invoices, receipts)\n- Text Extraction from Images\n- Automated Form Processing\n- Business Card Data Extraction\n\nSimply send a GET request with an image URL, define the extraction requirements, and receive structured JSON data in response.\n\n"}, "typeVersion": 1}, {"id": "aecf7331-6341-411e-8906-e42fc0ef264a", "name": "Transform image to base64", "type": "n8n-nodes-base.extractFromFile", "position": [520, 40], "parameters": {"options": {"encoding": "ascii"}, "operation": "binaryToPropery", "destinationKey": "data1"}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "b1fad586-998c-47ce-9921-e59527da029a", "connections": {"Webhook": {"main": [[{"node": "Get image from URL", "type": "main", "index": 0}]]}, "Get image from URL": {"main": [[{"node": "Transform image to base64", "type": "main", "index": 0}]]}, "Transform image to base64": {"main": [[{"node": "Call Gemini API (Flash Lite) with Image", "type": "main", "index": 0}]]}, "Call Gemini API (Flash Lite) with Image": {"main": [[{"node": "Edit fields to output required data alone", "type": "main", "index": 0}]]}, "Edit fields to output required data alone": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}}}