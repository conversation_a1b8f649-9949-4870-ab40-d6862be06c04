{"id": "7", "name": "Coffee Bot (Mattermost)", "nodes": [{"name": "Divide into groups", "type": "n8n-nodes-base.function", "position": [1060, 300], "parameters": {"functionCode": "const ideal_group_size = 3;\nlet groups = [];\nlet data_as_array = [];\nlet newItems = [];\n\n// Take all the users and add them to an array\nfor (let j = 0; j < items.length; j++) {\n  data_as_array.push({username: items[j].json.username, email: items[j].json.email});\n}\n\n// <PERSON> (aka <PERSON><PERSON><PERSON>) Shuffle\nfunction shuffle(array) {\n  var currentIndex = array.length, temporaryValue, randomIndex;\n\n  // While there remain elements to shuffle...\n  while (0 !== currentIndex) {\n\n    // Pick a remaining element...\n    randomIndex = Math.floor(Math.random() * currentIndex);\n    currentIndex -= 1;\n\n    // And swap it with the current element.\n    temporaryValue = array[currentIndex];\n    array[currentIndex] = array[randomIndex];\n    array[randomIndex] = temporaryValue;\n  }\n\n  return array;\n}\n\n// Randomize the sequence of names in the array\ndata_as_array = shuffle(data_as_array);\n\n// Create groups of ideal group size (3)\nfor (let i = 0; i < data_as_array.length; i += ideal_group_size) {\n  groups.push(data_as_array.slice(i, i + ideal_group_size));\n}\n\n// Make sure that no group has just one person. If it does, take\n// one from previous group and add it to that group \nfor (let k = 0; k < groups.length; k++) {\n  if (groups[k].length === 1) {\n    groups[k].push(groups[k-1].shift());\n  }\n}\n\nfor (let l = 0; l < groups.length; l++) {\n    newItems.push({json: {groupsUsername: groups[l].map(a=> a.username), groupsEmail: groups[l].map(b=> b.email)}})\n}\n\nreturn newItems;"}, "typeVersion": 1}, {"name": "Greetings", "type": "n8n-nodes-base.mattermost", "position": [650, 300], "parameters": {"message": "👋 Happy Monday\n\nGroups for this week's virtual coffee are:", "channelId": "Enter Your Channel ID", "attachments": [], "otherOptions": {}}, "credentials": {"mattermostApi": "Mattermost Cred"}, "typeVersion": 1}, {"name": "Weekly trigger on monday", "type": "n8n-nodes-base.cron", "position": [450, 300], "parameters": {"triggerTimes": {"item": [{"hour": 10, "mode": "everyWeek"}]}}, "typeVersion": 1}, {"name": "Announce groups", "type": "n8n-nodes-base.mattermost", "position": [1250, 200], "parameters": {"message": "=☀️ {{$node[\"Divide into groups\"].json[\"groupsUsername\"].join(', ')}}", "channelId": "=", "attachments": [], "otherOptions": {}}, "credentials": {"mattermostApi": "Mattermost Cred"}, "typeVersion": 1}, {"name": "Employees in coffee chat channel", "type": "n8n-nodes-base.mattermost", "position": [850, 300], "parameters": {"resource": "user", "operation": "getAll", "additionalFields": {"inChannel": "={{$node[\"Greetings\"].parameter[\"channelId\"]}}"}}, "credentials": {"mattermostApi": "Mattermost Cred"}, "typeVersion": 1}, {"name": "Send calendar invites", "type": "n8n-nodes-base.googleCalendar", "position": [1250, 400], "parameters": {"end": "2020-12-17T18:38:49.000Z", "start": "2020-12-17T18:08:49.000Z", "calendar": "Enter Your Google Calendar", "additionalFields": {"summary": "n8n coffee catchup", "attendees": ["={{$node[\"Divide into groups\"].json[\"groupsEmail\"].join(',')}}"], "guestsCanModify": true, "conferenceDataUi": {"conferenceDataValues": {"conferenceSolution": "hangouts<PERSON><PERSON><PERSON>"}}}}, "credentials": {"googleCalendarOAuth2Api": "Google Calendar Cred"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Greetings": {"main": [[{"node": "Employees in coffee chat channel", "type": "main", "index": 0}]]}, "Divide into groups": {"main": [[{"node": "Announce groups", "type": "main", "index": 0}, {"node": "Send calendar invites", "type": "main", "index": 0}]]}, "Weekly trigger on monday": {"main": [[{"node": "Greetings", "type": "main", "index": 0}]]}, "Employees in coffee chat channel": {"main": [[{"node": "Divide into groups", "type": "main", "index": 0}]]}}}