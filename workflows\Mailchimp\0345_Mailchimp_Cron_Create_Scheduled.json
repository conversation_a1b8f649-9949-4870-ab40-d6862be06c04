{"id": "1", "name": "Create entry in Mailchimp from Airtable", "nodes": [{"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [450, 450], "parameters": {"triggerTimes": {"item": [{}]}}, "typeVersion": 1}, {"name": "Airtable", "type": "n8n-nodes-base.airtable", "position": [650, 450], "parameters": {"table": "Users", "operation": "list", "application": "=apprZs8g4tIGDUtqQ", "additionalOptions": {"fields": ["Name", "Email", "Interest"]}}, "credentials": {"airtableApi": "claudia<PERSON><PERSON><PERSON>@gmail.com"}, "typeVersion": 1}, {"name": "Mailchimp", "type": "n8n-nodes-base.mailchimp", "position": [840, 450], "parameters": {"list": "777b2643d4", "email": "={{$node[\"Airtable\"].json[\"fields\"][\"Email\"]}}", "status": "subscribed", "options": {"tags": "Interest"}, "mergeFieldsUi": {"mergeFieldsValues": [{"name": "FNAME", "value": "={{$node[\"Airtable\"].json[\"fields\"][\"Name\"]}}"}]}}, "credentials": {"mailchimpApi": "claudia<PERSON><PERSON><PERSON>@gmail.com"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Cron": {"main": [[{"node": "Airtable", "type": "main", "index": 0}]]}, "Airtable": {"main": [[{"node": "Mailchimp", "type": "main", "index": 0}]]}}}