{"id": "o8iTqIh2sVvnuWz5", "meta": {"instanceId": "b9faf72fe0d7c3be94b3ebff0778790b50b135c336412d28fd4fca2cbbf8d1f5"}, "name": "RAG & GenAI App With WordPress Content", "tags": [], "nodes": [{"id": "c3738490-ed39-4774-b337-bf5ee99d0c72", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [500, 940], "parameters": {}, "typeVersion": 1}, {"id": "3ab719bd-3652-433f-a597-9cd28f8cfcea", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [2580, 1320], "parameters": {"model": "text-embedding-3-small", "options": {}}, "typeVersion": 1}, {"id": "e8639569-2091-44de-a84d-c3fc3ce54de4", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [2800, 1260], "parameters": {"options": {"metadata": {"metadataValues": [{"name": "title", "value": "={{ $json.title }}"}, {"name": "url", "value": "={{ $json.url }}"}, {"name": "content_type", "value": "={{ $json.content_type }}"}, {"name": "publication_date", "value": "={{ $json.publication_date }}"}, {"name": "modification_date", "value": "={{ $json.modification_date }}"}, {"name": "id", "value": "={{ $json.id }}"}]}}, "jsonData": "={{ $json.data }}", "jsonMode": "expressionData"}, "typeVersion": 1}, {"id": "e7f858eb-4dca-40ea-9da9-af953687e63d", "name": "Token Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterTokenSplitter", "position": [2900, 1480], "parameters": {"chunkSize": 300, "chunkOverlap": 30}, "typeVersion": 1}, {"id": "27585104-5315-4c11-b333-4b5d27d9bae4", "name": "Embeddings OpenAI1", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [1400, 2340], "parameters": {"model": "text-embedding-3-small", "options": {}}, "typeVersion": 1}, {"id": "35269a98-d905-4e4f-ae5b-dadad678f260", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [2800, 2300], "parameters": {"model": "gpt-4o-mini", "options": {}}, "typeVersion": 1}, {"id": "cd26b6fa-a8bb-4139-9bec-8656d90d8203", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "position": [2920, 2300], "parameters": {"tableName": "website_chat_histories"}, "typeVersion": 1.1}, {"id": "7c718e1b-1398-49f3-ba67-f970a82983e0", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [3380, 2060], "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "f91f18e0-7a04-4218-8490-bff35dfbf7a8", "name": "Set fields", "type": "n8n-nodes-base.set", "position": [2360, 2060], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "6888175b-853b-457a-96f7-33dfe952a05d", "name": "documents", "type": "string", "value": "={{ \n  JSON.stringify(\n    $json.documents.map(doc => ({\n      metadata: \n        'URL: ' + doc.metadata.url.replaceAll('&rsquo;', \"'\").replaceAll(/[\"]/g, '') + '\\n' +\n        'Publication Date: ' + doc.metadata.publication_date.replaceAll(/[\"]/g, '') + '\\n' +\n        'Modification Date: ' + doc.metadata.modification_date.replaceAll(/[\"]/g, '') + '\\n' +\n        'Content Type: ' + doc.metadata.content_type.replaceAll(/[\"]/g, '') + '\\n' +\n        'Title: ' + doc.metadata.title.replaceAll('&rsquo;', \"'\").replaceAll(/[\"]/g, '') + '\\n',\n      \n      page_content: doc.pageContent\n    }))\n  ).replaceAll(/[\\[\\]{}]/g, '')\n}}"}, {"id": "ae310b77-4560-4f44-8c4e-8d13f680072e", "name": "sessionId", "type": "string", "value": "={{ $('When chat message received').item.json.sessionId }}"}, {"id": "8738f4de-b3c3-45ad-af4b-8311c8105c35", "name": "chatInput", "type": "string", "value": "={{ $('When chat message received').item.json.chatInput }}"}]}}, "typeVersion": 3.4}, {"id": "7f392a40-e353-4bb2-9ecf-3ee330110b95", "name": "Embeddings OpenAI2", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [6400, 860], "parameters": {"model": "text-embedding-3-small", "options": {}}, "typeVersion": 1}, {"id": "9e045857-5fcd-4c4b-83ee-ceda28195b76", "name": "Default Data Loader1", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [6500, 860], "parameters": {"options": {"metadata": {"metadataValues": [{"name": "title", "value": "={{ $json.title }}"}, {"name": "url", "value": "={{ $json.url }}"}, {"name": "content_type", "value": "={{ $json.content_type }}"}, {"name": "publication_date", "value": "={{ $json.publication_date }}"}, {"name": "modification_date", "value": "={{ $json.modification_date }}"}, {"name": "id", "value": "={{ $json.id }}"}]}}, "jsonData": "={{ $json.data }}", "jsonMode": "expressionData"}, "typeVersion": 1}, {"id": "d0c1144b-4542-470e-8cbe-f985e839d9d0", "name": "Token Splitter1", "type": "@n8n/n8n-nodes-langchain.textSplitterTokenSplitter", "position": [6500, 980], "parameters": {"chunkSize": 300, "chunkOverlap": 30}, "typeVersion": 1}, {"id": "ec7cf1b2-f56f-45da-bb34-1dc8a66a7de6", "name": "Markdown1", "type": "n8n-nodes-base.markdown", "position": [6240, 900], "parameters": {"html": "={{ $json.content }}", "options": {}}, "typeVersion": 1}, {"id": "8399976b-340a-49ce-a5b6-f7339957aa9d", "name": "Postgres", "type": "n8n-nodes-base.postgres", "position": [4260, 900], "parameters": {"query": "select max(created_at) as last_workflow_execution from n8n_website_embedding_histories", "options": {}, "operation": "execute<PERSON>uery"}, "typeVersion": 2.5}, {"id": "88e79403-06df-4f18-9e4c-a4c4e727aa17", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "position": [3300, 900], "parameters": {"options": {}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}, {"id": "db7241e8-1c3a-4f91-99b7-383000f41afe", "name": "Aggregate1", "type": "n8n-nodes-base.aggregate", "position": [6800, 680], "parameters": {"options": {}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}, {"id": "94bbba31-d83b-427f-a7dc-336725238294", "name": "Aggregate2", "type": "n8n-nodes-base.aggregate", "position": [7180, 1160], "parameters": {"options": {}, "fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "metadata.id"}]}}, "typeVersion": 1}, {"id": "52a110fa-cdd6-4b1d-99fe-394b5dfa0a1f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [440, 600], "parameters": {"color": 5, "width": 3308.2687575224263, "height": 1015.3571428571431, "content": "# Workflow 1 : Initial Embedding \n## Use this workflow to create the initial embedding for your WordPress website content\n\n"}, "typeVersion": 1}, {"id": "4cbf8135-a52b-4a54-b7b0-15ea27ce7ae3", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [3812, 605], "parameters": {"color": 5, "width": 3785.6673412474183, "height": 1020.4528919414245, "content": "# Workflow 2 : Upsert\n## Use this workflow to upsert embeddings for documents stored in the Supabase vector table\n"}, "typeVersion": 1}, {"id": "f6e954e0-a37a-45ac-9882-20f4f1944b70", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [440, 1820], "parameters": {"color": 5, "width": 3235.199999999999, "height": 817.9199999999992, "content": "# Workflow 3 : Use this workflow to enable chat functionality with your website content. The chat can be embedded into your website to enhance user experience"}, "typeVersion": 1}, {"id": "acbdd54b-f02a-41aa-a0ce-8642db560151", "name": "Wordpress - Get all posts", "type": "n8n-nodes-base.wordpress", "position": [1260, 880], "parameters": {"options": {}, "operation": "getAll", "returnAll": true}, "typeVersion": 1}, {"id": "94fce59d-9336-4d49-a378-17335ec02e52", "name": "Wordpress - Get all pages", "type": "n8n-nodes-base.wordpress", "position": [1260, 1060], "parameters": {"options": {}, "resource": "page", "operation": "getAll", "returnAll": true}, "typeVersion": 1}, {"id": "b00c92e5-1765-4fd9-9981-e01053992a0a", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1157, 727], "parameters": {"width": 1108.3519999999999, "height": 561.4080000000004, "content": "## Use filters to create embeddings only for content that you want to include in your GenAI application"}, "typeVersion": 1}, {"id": "f8a22739-898d-456b-93f8-79f74b60a00c", "name": "Set fields1", "type": "n8n-nodes-base.set", "position": [2320, 900], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "de6711dc-d03c-488c-bef4-0a853e2d0a14", "name": "publication_date", "type": "string", "value": "={{ $json.date }}"}, {"id": "f8e35dcc-c96c-4554-b6bc-8e5d7eca90e3", "name": "modification_date", "type": "string", "value": "={{ $json.modified }}"}, {"id": "f6a6e3de-fe39-4cfc-ab07-c4ccfaef78f5", "name": "content_type", "type": "string", "value": "={{ $json.type }}"}, {"id": "b0428598-073f-4560-9a0c-01caf3708921", "name": "title", "type": "string", "value": "={{ $json.title.rendered }}"}, {"id": "534f51b4-b43a-40d3-8120-58df8043d909", "name": "url", "type": "string", "value": "={{ $json.link }}"}, {"id": "dbe0c559-90bd-49f8-960e-0d85d5ed4f5e", "name": "content", "type": "string", "value": "={{ $json.content.rendered }}"}, {"id": "892be7c6-b032-4129-b285-1986ed4ee046", "name": "protected", "type": "boolean", "value": "={{ $json.excerpt.protected }}"}, {"id": "06fac885-4431-41ff-a43b-6eb84ca57401", "name": "status", "type": "string", "value": "={{ $json.status }}"}, {"id": "43b1aea7-895e-41da-a0a6-2f1cec1f1b97", "name": "id", "type": "number", "value": "={{ $json.id }}"}]}}, "typeVersion": 3.4}, {"id": "404db031-f470-4e42-a3b3-66b849a86174", "name": "Filter - Only published &  unprotected content", "type": "n8n-nodes-base.filter", "position": [2520, 900], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "1f708587-f3d3-487a-843a-b6a2bfad2ca9", "operator": {"type": "boolean", "operation": "false", "singleValue": true}, "leftValue": "={{ $json.protected }}", "rightValue": ""}, {"id": "04f47269-e112-44c3-9014-749898aca8bd", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "publish"}]}}, "typeVersion": 2.2}, {"id": "05bb6091-515e-4f22-a3fd-d25b2046a03d", "name": "HTML To Markdown", "type": "n8n-nodes-base.markdown", "position": [2740, 900], "parameters": {"html": "={{ $json.content}}", "options": {}}, "typeVersion": 1}, {"id": "391e9ea7-71dd-42ae-bee7-badcae32427c", "name": "Supabase - Store workflow execution", "type": "n8n-nodes-base.supabase", "position": [3520, 900], "parameters": {"tableId": "n8n_website_embedding_histories", "fieldsUi": {"fieldValues": [{"fieldId": "id", "fieldValue": "={{ $executionId }}"}]}}, "typeVersion": 1}, {"id": "47dad096-efc8-4bdd-9c22-49562325d8a0", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [460, 1320], "parameters": {"width": 851.1898437499999, "height": 275.2000000000001, "content": "## Run these two nodes if the \"documents\" table on Supabase and the \"n8n_website_embedding_histories\" table do not exist"}, "typeVersion": 1}, {"id": "d19f3a5f-fa42-46d0-a366-4c5a5d09f559", "name": "Every 30 seconds", "type": "n8n-nodes-base.scheduleTrigger", "position": [3940, 900], "parameters": {"rule": {"interval": [{"field": "seconds"}]}}, "typeVersion": 1.2}, {"id": "a22ab0dd-1da8-4fc2-8106-6130bf7938c8", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [3820, 740], "parameters": {"width": 336.25, "height": 292.5, "content": "## Set this node to match the frequency of publishing and updating on your website"}, "typeVersion": 1}, {"id": "ba25135b-6e6e-406b-b18a-f532a6e37276", "name": "Wordpress - Get posts modified after last workflow execution", "type": "n8n-nodes-base.httpRequest", "position": [4600, 840], "parameters": {"url": "https://mydomain.com/wp-json/wp/v2/posts", "options": {}, "sendQuery": true, "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "modified_after", "value": "={{ $json.last_workflow_execution }}"}]}, "nodeCredentialType": "wordpressApi"}, "typeVersion": 4.2}, {"id": "a1d8572e-2b0d-40a1-a898-bbd563a6b190", "name": "Wordpress - Get posts modified after last workflow execution1", "type": "n8n-nodes-base.httpRequest", "position": [4600, 1060], "parameters": {"url": "https://mydomain.com/wp-json/wp/v2/pages", "options": {}, "sendQuery": true, "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "modified_after", "value": "={{ $json.last_workflow_execution }}"}]}, "nodeCredentialType": "wordpressApi"}, "typeVersion": 4.2}, {"id": "c0839aaa-8ba7-47ff-8fa9-dc75e1c4da84", "name": "Set fields2", "type": "n8n-nodes-base.set", "position": [5420, 920], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "de6711dc-d03c-488c-bef4-0a853e2d0a14", "name": "publication_date", "type": "string", "value": "={{ $json.date }}"}, {"id": "f8e35dcc-c96c-4554-b6bc-8e5d7eca90e3", "name": "modification_date", "type": "string", "value": "={{ $json.modified }}"}, {"id": "f6a6e3de-fe39-4cfc-ab07-c4ccfaef78f5", "name": "content_type", "type": "string", "value": "={{ $json.type }}"}, {"id": "b0428598-073f-4560-9a0c-01caf3708921", "name": "title", "type": "string", "value": "={{ $json.title.rendered }}"}, {"id": "534f51b4-b43a-40d3-8120-58df8043d909", "name": "url", "type": "string", "value": "={{ $json.link }}"}, {"id": "dbe0c559-90bd-49f8-960e-0d85d5ed4f5e", "name": "content", "type": "string", "value": "={{ $json.content.rendered }}"}, {"id": "892be7c6-b032-4129-b285-1986ed4ee046", "name": "protected", "type": "boolean", "value": "={{ $json.content.protected }}"}, {"id": "06fac885-4431-41ff-a43b-6eb84ca57401", "name": "status", "type": "string", "value": "={{ $json.status }}"}, {"id": "43b1aea7-895e-41da-a0a6-2f1cec1f1b97", "name": "id", "type": "number", "value": "={{ $json.id }}"}]}}, "typeVersion": 3.4}, {"id": "15b1d30a-5861-4380-89d5-0eef65240503", "name": "Filter - Only published and unprotected content", "type": "n8n-nodes-base.filter", "position": [5760, 920], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "c2b25d74-91d7-44ea-8598-422100947b07", "operator": {"type": "boolean", "operation": "false", "singleValue": true}, "leftValue": "={{ $json.protected }}", "rightValue": ""}, {"id": "3e63bf79-25ca-4ccf-aa86-ff5f90e1ece1", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "publish"}]}}, "typeVersion": 2.2}, {"id": "0990f503-8d6f-44f6-8d04-7e2f7d74301a", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [6040, 920], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "6cc4e46e-3884-4259-b7ed-51c5552cc3e0", "name": "Set fields3", "type": "n8n-nodes-base.set", "position": [7400, 1160], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "de6711dc-d03c-488c-bef4-0a853e2d0a14", "name": "publication_date", "type": "string", "value": "={{ $('Loop Over Items').item.json.publication_date }}"}, {"id": "f8e35dcc-c96c-4554-b6bc-8e5d7eca90e3", "name": "modification_date", "type": "string", "value": "={{ $('Loop Over Items').item.json.modification_date }}"}, {"id": "f6a6e3de-fe39-4cfc-ab07-c4ccfaef78f5", "name": "content_type", "type": "string", "value": "={{ $('Loop Over Items').item.json.content_type }}"}, {"id": "b0428598-073f-4560-9a0c-01caf3708921", "name": "title", "type": "string", "value": "={{ $('Loop Over Items').item.json.title }}"}, {"id": "534f51b4-b43a-40d3-8120-58df8043d909", "name": "url", "type": "string", "value": "={{ $('Loop Over Items').item.json.url }}"}, {"id": "dbe0c559-90bd-49f8-960e-0d85d5ed4f5e", "name": "content", "type": "string", "value": "={{ $('Loop Over Items').item.json.content }}"}, {"id": "892be7c6-b032-4129-b285-1986ed4ee046", "name": "protected", "type": "boolean", "value": "={{ $('Loop Over Items').item.json.protected }}"}, {"id": "06fac885-4431-41ff-a43b-6eb84ca57401", "name": "status", "type": "string", "value": "={{ $('Loop Over Items').item.json.status }}"}, {"id": "43b1aea7-895e-41da-a0a6-2f1cec1f1b97", "name": "id", "type": "number", "value": "={{ $('Loop Over Items').item.json.id }}"}]}}, "typeVersion": 3.4}, {"id": "24f47982-a803-4848-8390-c400a8cebcee", "name": "Set fields4", "type": "n8n-nodes-base.set", "position": [6680, 1400], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "de6711dc-d03c-488c-bef4-0a853e2d0a14", "name": "publication_date", "type": "string", "value": "={{ $('Loop Over Items').item.json.publication_date }}"}, {"id": "f8e35dcc-c96c-4554-b6bc-8e5d7eca90e3", "name": "modification_date", "type": "string", "value": "={{ $('Loop Over Items').item.json.modification_date }}"}, {"id": "f6a6e3de-fe39-4cfc-ab07-c4ccfaef78f5", "name": "content_type", "type": "string", "value": "={{ $('Loop Over Items').item.json.content_type }}"}, {"id": "b0428598-073f-4560-9a0c-01caf3708921", "name": "title", "type": "string", "value": "={{ $('Loop Over Items').item.json.title }}"}, {"id": "534f51b4-b43a-40d3-8120-58df8043d909", "name": "url", "type": "string", "value": "={{ $('Loop Over Items').item.json.url }}"}, {"id": "dbe0c559-90bd-49f8-960e-0d85d5ed4f5e", "name": "content", "type": "string", "value": "={{ $('Loop Over Items').item.json.content }}"}, {"id": "892be7c6-b032-4129-b285-1986ed4ee046", "name": "protected", "type": "boolean", "value": "={{ $('Loop Over Items').item.json.protected }}"}, {"id": "06fac885-4431-41ff-a43b-6eb84ca57401", "name": "status", "type": "string", "value": "={{ $('Loop Over Items').item.json.status }}"}, {"id": "43b1aea7-895e-41da-a0a6-2f1cec1f1b97", "name": "id", "type": "number", "value": "={{ $('Loop Over Items').item.json.id }}"}]}}, "typeVersion": 3.4}, {"id": "5f59ebbf-ca17-4311-809c-85b74ce624cc", "name": "Store documents on Supabase", "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "position": [6380, 680], "parameters": {"mode": "insert", "options": {"queryName": "match_documents"}, "tableName": {"__rl": true, "mode": "list", "value": "documents", "cachedResultName": "documents"}}, "typeVersion": 1}, {"id": "2422562e-9c95-4d77-ae8c-485b06f9234e", "name": "Store workflow execution id and timestamptz", "type": "n8n-nodes-base.supabase", "position": [7060, 680], "parameters": {"tableId": "n8n_website_embedding_histories"}, "typeVersion": 1}, {"id": "5013f3a1-f7fb-4fa7-9ef2-3599f77f5fc8", "name": "Aggregate documents", "type": "n8n-nodes-base.aggregate", "position": [1960, 2060], "parameters": {"options": {}, "fieldsToAggregate": {"fieldToAggregate": [{"renameField": true, "outputFieldName": "documents", "fieldToAggregate": "document"}]}}, "typeVersion": 1}, {"id": "26532217-3206-4be3-b186-733bc364913b", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [1220, 1980], "parameters": {"width": 665.78125, "height": 507.65625, "content": "## Retrieve documents from Supabase immediately after chat input to send metadata to OpenAI"}, "typeVersion": 1}, {"id": "78d2806c-8d13-44b8-bd6d-866fa794edae", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [6375, 1090], "parameters": {"width": 1198.9843749999998, "height": 515.4687499999998, "content": "## Switch:\n- **If the document exists and has been updated:** delete rows and insert new embedding\n- **If it’s a new document:** insert embedding"}, "typeVersion": 1}, {"id": "3b5ffada-ae2a-45a2-a76c-69732b05761c", "name": "Postgres - Create documents table", "type": "n8n-nodes-base.postgres", "position": [560, 1440], "parameters": {"query": "-- Enable the pgvector extension to work with embedding vectors\nCREATE EXTENSION vector;\n\n-- Create a table to store your documents with default RLS\nCREATE TABLE\n  documents (\n    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,\n    CONTENT TEXT, -- corresponds to Document.pageContent\n    metadata jsonb, -- corresponds to Document.metadata\n    embedding vector (1536) -- 1536 works for OpenAI embeddings, change if needed\n  );\n\n-- Enable Row Level Security on the documents table\nALTER TABLE documents ENABLE ROW LEVEL SECURITY;\n\n-- Create a function to search for documents\nCREATE FUNCTION match_documents (\n  query_embedding vector (1536),\n  match_count INT DEFAULT NULL,\n  FILTER jsonb DEFAULT '{}'\n) RETURNS TABLE (\n  id BIGINT,\n  CONTENT TEXT,\n  metadata jsonb,\n  similarity FLOAT\n) LANGUAGE plpgsql AS $$\n#variable_conflict use_column\nBEGIN\n  RETURN QUERY\n  SELECT\n    id,\n    content,\n    metadata,\n    1 - (documents.embedding <=> query_embedding) AS similarity\n  FROM documents\n  WHERE metadata @> filter\n  ORDER BY documents.embedding <=> query_embedding\n  LIMIT match_count;\nEND;\n$$;", "options": {}, "operation": "execute<PERSON>uery"}, "typeVersion": 2.5}, {"id": "632a7b44-a062-472e-a777-805ee74a4bd6", "name": "Postgres - Create workflow execution history table", "type": "n8n-nodes-base.postgres", "position": [920, 1440], "parameters": {"query": "CREATE TABLE\n  n8n_website_embedding_histories (\n    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,\n    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n  );", "options": {}, "operation": "execute<PERSON>uery"}, "typeVersion": 2.5}, {"id": "7c55e08b-e116-4e22-bd1d-e4bec5107d89", "name": "Merge Wordpress Posts and Pages", "type": "n8n-nodes-base.merge", "position": [1660, 900], "parameters": {}, "typeVersion": 3}, {"id": "4520db6c-2e68-45ff-9439-6fd95f95dc85", "name": "Merge retrieved WordPress posts and pages", "type": "n8n-nodes-base.merge", "position": [5120, 920], "parameters": {}, "typeVersion": 3}, {"id": "d547a063-6b76-4bfd-ba0a-165181c4af19", "name": "Postgres - Filter on existing documents", "type": "n8n-nodes-base.postgres", "position": [6260, 1180], "parameters": {"query": "SELECT *\nFROM documents\nWHERE (metadata->>'id')::integer = {{ $json.id }};\n", "options": {}, "operation": "execute<PERSON>uery"}, "typeVersion": 2.5, "alwaysOutputData": true}, {"id": "03456a81-d512-4fd8-842a-27b6d8b3f94e", "name": "Supabase - Delete row if documents exists", "type": "n8n-nodes-base.supabase", "position": [6900, 1160], "parameters": {"tableId": "documents", "operation": "delete", "filterType": "string", "filterString": "=metadata->>id=like.{{ $json.metadata.id }}"}, "executeOnce": false, "typeVersion": 1, "alwaysOutputData": false}, {"id": "72e5bf4b-c413-4fb7-acb8-59e7abee60f7", "name": "Switch", "type": "n8n-nodes-base.switch", "position": [6580, 1180], "parameters": {"rules": {"values": [{"outputKey": "existing_documents", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "number", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.metadata.id }}", "rightValue": ""}]}, "renameOutput": true}, {"outputKey": "new_documents", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "696d1c1b-8674-4549-880e-e0d0ff681905", "operator": {"type": "number", "operation": "notExists", "singleValue": true}, "leftValue": "={{ $json.metadata.id }}", "rightValue": ""}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "6c5d8f6a-569e-4f1e-99a6-07ec492575ff", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [660, 2060], "webhookId": "4e762668-c19f-40ec-83bf-302bb9fc6527", "parameters": {"mode": "webhook", "public": true, "options": {}}, "typeVersion": 1.1}, {"id": "9a2f17ba-902f-4528-9eef-f8c0e4ddf516", "name": "Supabase - Retrieve documents from chatinput", "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "position": [1380, 2060], "parameters": {"mode": "load", "prompt": "={{ $json.chatInput }}", "options": {}, "tableName": {"__rl": true, "mode": "list", "value": "documents", "cachedResultName": "documents"}}, "typeVersion": 1}, {"id": "43607f23-d33f-4aca-b478-f20ba8c218cf", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [2780, 2060], "parameters": {"text": "=Visitor's question : {{ $json.chatInput }}\nDocuments found: {{ $json.documents }}", "agent": "conversationalAgent", "options": {"systemMessage": "You are an assistant tasked with answering questions from visitors to the website {{your_website_url}}.\n\nInput:\nVisitor's question: The question posed by the visitor.\nDocuments found: A selection of documents from the vector database that match the visitor's question. These documents are accompanied by the following metadata:\nurl: The URL of the page or blog post found.\ncontent_type: The type of content (e.g., page or blog article).\npublication_date: The publication date of the document.\nmodification_date: The last modification date of the document.\nObjective:\nProvide a helpful answer using the relevant information from the documents found.\nIMPORTANT : You must always include all metadata (url, content_type, publication_date, and modification_date) directly in the main answer to the visitor to indicate the source of the information. These should not be separated from the main answer, and must be naturally integrated into the response.\nIf multiple documents are used in your response, mention each one with its respective metadata.\nIf no relevant documents are found, or if the documents are insufficient, clearly indicate this in your response.\nImportant: Respond in the language used by the visitor who asked the question.\nExample of forced metadata integration:\n\"The cost of a home charging station for an electric vehicle varies depending on several factors. According to [title of the page](https://example.com/charging-point-price), published on April 8, 2021, and updated on July 24, 2022, the price for a 7kW station is €777.57 including VAT. This page provides further details about the price range and installation considerations.\""}, "promptType": "define"}, "typeVersion": 1.6}, {"id": "cd4107cb-e521-4c1e-88e2-3417a12fd585", "name": "Supabase Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "position": [2940, 900], "parameters": {"mode": "insert", "options": {"queryName": "match_documents"}, "tableName": {"__rl": true, "mode": "list", "value": "documents", "cachedResultName": "documents"}}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "fe2a25f4-04b3-462c-97cd-a173b4a0631b", "connections": {"Switch": {"main": [[{"node": "Supabase - Delete row if documents exists", "type": "main", "index": 0}], [{"node": "Set fields4", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Postgres": {"main": [[{"node": "Wordpress - Get posts modified after last workflow execution", "type": "main", "index": 0}, {"node": "Wordpress - Get posts modified after last workflow execution1", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "Supabase - Store workflow execution", "type": "main", "index": 0}]]}, "Markdown1": {"main": [[{"node": "Store documents on Supabase", "type": "main", "index": 0}]]}, "Aggregate1": {"main": [[{"node": "Store workflow execution id and timestamptz", "type": "main", "index": 0}]]}, "Aggregate2": {"main": [[{"node": "Set fields3", "type": "main", "index": 0}]]}, "Set fields": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Set fields1": {"main": [[{"node": "Filter - Only published &  unprotected content", "type": "main", "index": 0}]]}, "Set fields2": {"main": [[{"node": "Filter - Only published and unprotected content", "type": "main", "index": 0}]]}, "Set fields3": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Set fields4": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Token Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Markdown1", "type": "main", "index": 0}], [{"node": "Postgres - Filter on existing documents", "type": "main", "index": 0}]]}, "Token Splitter1": {"ai_textSplitter": [[{"node": "Default Data Loader1", "type": "ai_textSplitter", "index": 0}]]}, "Every 30 seconds": {"main": [[{"node": "Postgres", "type": "main", "index": 0}]]}, "HTML To Markdown": {"main": [[{"node": "Supabase Vector Store", "type": "main", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Supabase Vector Store", "type": "ai_embedding", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Embeddings OpenAI1": {"ai_embedding": [[{"node": "Supabase - Retrieve documents from chatinput", "type": "ai_embedding", "index": 0}]]}, "Embeddings OpenAI2": {"ai_embedding": [[{"node": "Store documents on Supabase", "type": "ai_embedding", "index": 0}]]}, "Aggregate documents": {"main": [[{"node": "Set fields", "type": "main", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Supabase Vector Store", "type": "ai_document", "index": 0}]]}, "Default Data Loader1": {"ai_document": [[{"node": "Store documents on Supabase", "type": "ai_document", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Supabase Vector Store": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Wordpress - Get all pages": {"main": [[{"node": "Merge Wordpress Posts and Pages", "type": "main", "index": 1}]]}, "Wordpress - Get all posts": {"main": [[{"node": "Merge Wordpress Posts and Pages", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "Supabase - Retrieve documents from chatinput", "type": "main", "index": 0}]]}, "Store documents on Supabase": {"main": [[{"node": "Aggregate1", "type": "main", "index": 0}]]}, "Merge Wordpress Posts and Pages": {"main": [[{"node": "Set fields1", "type": "main", "index": 0}]]}, "Postgres - Create documents table": {"main": [[{"node": "Postgres - Create workflow execution history table", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Wordpress - Get all posts", "type": "main", "index": 0}, {"node": "Wordpress - Get all pages", "type": "main", "index": 0}]]}, "Postgres - Filter on existing documents": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Merge retrieved WordPress posts and pages": {"main": [[{"node": "Set fields2", "type": "main", "index": 0}]]}, "Supabase - Delete row if documents exists": {"main": [[{"node": "Aggregate2", "type": "main", "index": 0}]]}, "Supabase - Retrieve documents from chatinput": {"main": [[{"node": "Aggregate documents", "type": "main", "index": 0}]]}, "Filter - Only published &  unprotected content": {"main": [[{"node": "HTML To Markdown", "type": "main", "index": 0}]]}, "Filter - Only published and unprotected content": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Wordpress - Get posts modified after last workflow execution": {"main": [[{"node": "Merge retrieved WordPress posts and pages", "type": "main", "index": 0}]]}, "Wordpress - Get posts modified after last workflow execution1": {"main": [[{"node": "Merge retrieved WordPress posts and pages", "type": "main", "index": 1}]]}}}