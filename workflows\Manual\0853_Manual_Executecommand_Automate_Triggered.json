{"meta": {"instanceId": "a6d5191e58fd6be87222f47435e6f9df8f98ec0d945d3e7b7f6373c59a6c3f37", "templateCredsSetupCompleted": true}, "nodes": [{"id": "fcf1064e-557f-4514-9109-bb10ac837f8b", "name": "Run python script", "type": "n8n-nodes-base.executeCommand", "position": [-100, 20], "parameters": {"command": "=python C:\\KOKORO\\voicegen.py \"{{ $json.text }}\" \"{{ $json.voice }}\" 1\n"}, "typeVersion": 1}, {"id": "199a3212-69c0-4314-92c8-783573f165d7", "name": "Passing variables", "type": "n8n-nodes-base.set", "position": [-320, 20], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "{\n  \"voice\": \"af_sarah\",\n  \"text\": \"Hello world!\"\n}\n"}, "typeVersion": 3.4}, {"id": "deb008d0-53ae-4348-a555-9e54b6e0efd4", "name": "Start", "type": "n8n-nodes-base.manualTrigger", "position": [-540, 20], "parameters": {}, "typeVersion": 1}, {"id": "ffa1b2bf-abc3-45d8-8b7b-de4c0780a609", "name": "Play sound", "type": "n8n-nodes-base.readBinaryFiles", "position": [120, 20], "parameters": {"fileSelector": "D:/output.mp3"}, "typeVersion": 1, "alwaysOutputData": false}], "pinData": {}, "connections": {"Start": {"main": [[{"node": "Passing variables", "type": "main", "index": 0}]]}, "Passing variables": {"main": [[{"node": "Run python script", "type": "main", "index": 0}]]}, "Run python script": {"main": [[{"node": "Play sound", "type": "main", "index": 0}]]}}}