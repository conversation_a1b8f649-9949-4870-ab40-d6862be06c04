{"meta": {"instanceId": "cb484ba7b742928a2048bf8829668bed5b5ad9787579adea888f05980292a4a7", "templateCredsSetupCompleted": true}, "nodes": [{"id": "a810abc1-4cbf-49a8-8c4e-227ad572d137", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [2280, -400], "parameters": {}, "typeVersion": 1}, {"id": "02f98d43-05d9-4d70-a94a-1af7e2ad10cf", "name": "Create Marketing Insight Data", "type": "n8n-nodes-base.notion", "position": [3500, -480], "parameters": {"title": "={{ $('Execute Workflow Trigger').item.json.metaData.title }}", "options": {"icon": "🎯"}, "resource": "databasePage", "databaseId": {"__rl": true, "mode": "list", "value": "1395b6e0-c94f-802d-9a63-c524a1769699", "cachedResultUrl": "https://www.notion.so/1395b6e0c94f802d9a63c524a1769699", "cachedResultName": "Marketing Insights"}, "propertiesUi": {"propertyValues": [{"key": "Name|title", "title": "={{ $json.Summary }}"}, {"key": "Marketing Tags|multi_select", "multiSelectValue": "={{ $json.Tag }}"}, {"key": "Sales Call Summaries|relation", "relationValue": ["={{ $('Execute Workflow Trigger').item.json.notionData[0].id }}"]}, {"key": "Date Mentioned|date", "date": "={{ $('Execute Workflow Trigger').item.json.metaData.started }}"}]}}, "credentials": {"notionApi": {"id": "80", "name": "Notion david-internal"}}, "retryOnFail": true, "typeVersion": 2.2, "waitBetweenTries": 3000}, {"id": "426e8b9d-7542-473e-8124-45cf70adf035", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [2720, -600], "parameters": {"color": 7, "width": 1480, "height": 480, "content": "## Marketing Insights Processing"}, "typeVersion": 1}, {"id": "2946cd22-e7e6-40eb-a375-0f5233d66260", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [2140, -760], "parameters": {"color": 7, "width": 560, "height": 620, "content": "## Receives AI Data from other workflow\n"}, "typeVersion": 1}, {"id": "3a0be813-8556-44cc-bd1a-9cdcf9b7aa55", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [1780, -960], "parameters": {"width": 340, "height": 820, "content": "![Callforge](https://uploads.n8n.io/templates/callforgeshadow.png)\n## CallForge - The AI Gong Sales Call Processor\nCallForge allows you to extract important information for different departments from your Sales Gong Calls. \n\n### AI Output Processor\nOnce the AI data is generated, it is then added (or not!) to the Notion Database here. This is also where the Pipedrive or Salesforce integration will be added once that portion is complete. "}, "typeVersion": 1}, {"id": "35aa0491-8135-4f69-a213-8a578bf0c405", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [2720, -100], "parameters": {"color": 7, "width": 1480, "height": 440, "content": "## Actionable Insights"}, "typeVersion": 1}, {"id": "8035c35e-0073-4d66-8630-8f3f2d6fea72", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [2720, -1060], "parameters": {"color": 7, "width": 1480, "height": 440, "content": "## Recurring Topics"}, "typeVersion": 1}, {"id": "c18b2e0d-3c7f-4caa-bf12-5d5058d6d6e2", "name": "Check if Recurring Topics Found", "type": "n8n-nodes-base.if", "position": [2820, -820], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "7f182ff7-b5cf-44d0-9645-9200bb7afa24", "operator": {"type": "array", "operation": "lengthGte", "rightType": "number"}, "leftValue": "={{ $json.AIoutput.MarketingInsights }}", "rightValue": 1}]}}, "typeVersion": 2.2}, {"id": "8130e673-247c-4e99-89aa-59c1e76e8cc4", "name": "Wait for rate limiting - Recurring", "type": "n8n-nodes-base.wait", "position": [3060, -980], "webhookId": "9aa5f1eb-1ca7-4d69-9783-8d4a21b32db3", "parameters": {"amount": 3}, "typeVersion": 1.1}, {"id": "25838252-28ad-4f9a-b360-7a0e8e2e39bf", "name": "Split Out Recurring Topics", "type": "n8n-nodes-base.splitOut", "position": [3280, -980], "parameters": {"options": {}, "fieldToSplitOut": "AIoutput.RecurringTopics"}, "typeVersion": 1}, {"id": "f48e333b-9dd3-47a5-a874-a14ab6710c59", "name": "Create Recurring Topics Data", "type": "n8n-nodes-base.notion", "position": [3500, -980], "parameters": {"title": "={{ $('Execute Workflow Trigger').item.json.metaData.title }}", "options": {"icon": "🔁"}, "resource": "databasePage", "databaseId": {"__rl": true, "mode": "list", "value": "17c5b6e0-c94f-80f4-9bf0-e52c7b0ef947", "cachedResultUrl": "https://www.notion.so/17c5b6e0c94f80f49bf0e52c7b0ef947", "cachedResultName": "Recurring Topics"}, "propertiesUi": {"propertyValues": [{"key": "Context|rich_text", "textContent": "={{ $json.Context }}"}, {"key": "Mentions|number", "numberValue": "={{ $json.Mentions }}"}, {"key": "Topic|title", "title": "={{ $json.Topic }}"}, {"key": "Sales Call Summaries|relation", "relationValue": ["={{ $('Execute Workflow Trigger').item.json.notionData[0].id }}"]}]}}, "credentials": {"notionApi": {"id": "2B3YIiD4FMsF9Rjn", "name": "Angelbot Notion"}}, "retryOnFail": true, "typeVersion": 2.2, "waitBetweenTries": 3000}, {"id": "f0f39472-35e7-42a0-a0c6-c4414deacfb9", "name": "Bundle Recurring Topics Data to 1 object", "type": "n8n-nodes-base.aggregate", "position": [3700, -980], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "tagdata"}, "typeVersion": 1}, {"id": "290d14a5-f255-4a15-bcce-b879f6c45300", "name": "<PERSON><PERSON> Recurring Topics Thread", "type": "n8n-nodes-base.set", "position": [4000, -800], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d8fc65ad-2b05-40c1-84c7-7bda819f0f1f", "name": "aiResponse", "type": "object", "value": "={{ $('Execute Workflow Trigger').item.json.aiResponse }}"}]}}, "typeVersion": 3.4}, {"id": "784209f5-d9cf-47b6-b222-e90eab0e9c42", "name": "Check if Marketing Insight Data Found", "type": "n8n-nodes-base.if", "position": [2820, -320], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "7f182ff7-b5cf-44d0-9645-9200bb7afa24", "operator": {"type": "array", "operation": "lengthGte", "rightType": "number"}, "leftValue": "={{ $json.AIoutput.MarketingInsights }}", "rightValue": 1}]}}, "typeVersion": 2.2}, {"id": "f9c10abd-cab4-4cfa-909c-b7a4b9d132ec", "name": "Wait for rate limiting - Marketing Insights", "type": "n8n-nodes-base.wait", "position": [3060, -480], "webhookId": "264a15ce-478f-4b69-b46c-21bf8ec4bcd2", "parameters": {"amount": 3}, "typeVersion": 1.1}, {"id": "805c464a-38d1-4bee-a65a-a7dca32fc7f5", "name": "Split out Insights", "type": "n8n-nodes-base.splitOut", "position": [3280, -480], "parameters": {"options": {}, "fieldToSplitOut": "AIoutput.MarketingInsights"}, "typeVersion": 1}, {"id": "424eb472-9280-4fc8-b7e2-ba55e0b5d9b9", "name": "Bundle Marketing Insights Data to 1 object", "type": "n8n-nodes-base.aggregate", "position": [3700, -480], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "tagdata"}, "typeVersion": 1}, {"id": "a7942700-6feb-494e-b013-ecaafa03bc9c", "name": "Merge Marketing Insights Thread", "type": "n8n-nodes-base.set", "position": [4000, -300], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d8fc65ad-2b05-40c1-84c7-7bda819f0f1f", "name": "aiResponse", "type": "object", "value": "={{ $('Execute Workflow Trigger').item.json.aiResponse }}"}]}}, "typeVersion": 3.4}, {"id": "355724f6-bd52-470d-8346-a7482bbd4a86", "name": "Check if Actionable Insights Data Found", "type": "n8n-nodes-base.if", "position": [2820, 140], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "7f182ff7-b5cf-44d0-9645-9200bb7afa24", "operator": {"type": "array", "operation": "lengthGte", "rightType": "number"}, "leftValue": "={{ $json.AIoutput.ActionableInsights }}", "rightValue": 1}]}}, "typeVersion": 2.2}, {"id": "4ffd7478-3f04-49bb-9773-8c870007b64b", "name": "Wait for rate limiting - Actionable Insights", "type": "n8n-nodes-base.wait", "position": [3060, -20], "webhookId": "8156cdcc-e8d6-4fdb-92f9-6b70d9c671fd", "parameters": {"amount": 3}, "typeVersion": 1.1}, {"id": "31305dc7-4362-4039-a43e-c38bdd9ad1ce", "name": "Split Out Actionable Insights", "type": "n8n-nodes-base.splitOut", "position": [3280, -20], "parameters": {"options": {}, "fieldToSplitOut": "AIoutput.ActionableInsights"}, "typeVersion": 1}, {"id": "733cd058-bb92-41f4-88e7-9970f71b3ad0", "name": "Create Actionable Insights Data", "type": "n8n-nodes-base.notion", "position": [3500, -20], "parameters": {"title": "={{ $('Execute Workflow Trigger').item.json.metaData.title }}", "options": {"icon": "🎬"}, "resource": "databasePage", "databaseId": {"__rl": true, "mode": "list", "value": "17c5b6e0-c94f-809f-b5ee-e890f3ab3be9", "cachedResultUrl": "https://www.notion.so/17c5b6e0c94f809fb5eee890f3ab3be9", "cachedResultName": "Actionable Insights"}, "propertiesUi": {"propertyValues": [{"key": "Rationale|rich_text", "textContent": "={{ $json.Rationale }}"}, {"key": "Recommendation Type|rich_text", "textContent": "={{ $json.RecommendationType }}"}, {"key": "Title|rich_text", "textContent": "={{ $json.Title }}"}, {"key": "Topic|title", "title": "={{ $json.Topic }}"}, {"key": "Sales Call Summaries|relation", "relationValue": ["={{ $('Execute Workflow Trigger').item.json.notionData[0].id }}"]}]}}, "credentials": {"notionApi": {"id": "2B3YIiD4FMsF9Rjn", "name": "Angelbot Notion"}}, "retryOnFail": true, "typeVersion": 2.2, "waitBetweenTries": 3000}, {"id": "ae519285-cf01-4f4b-9574-131ec2487ce7", "name": "Bundle Actionable Insights Data to 1 object", "type": "n8n-nodes-base.aggregate", "position": [3700, -20], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "tagdata"}, "typeVersion": 1}, {"id": "45812c79-be23-4139-987b-14bc674fbfc1", "name": "Merge Actionable Insights Thread", "type": "n8n-nodes-base.set", "position": [4000, 160], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d8fc65ad-2b05-40c1-84c7-7bda819f0f1f", "name": "aiResponse", "type": "object", "value": "={{ $('Execute Workflow Trigger').item.json.aiResponse }}"}]}}, "typeVersion": 3.4}], "pinData": {}, "connections": {"Split out Insights": {"main": [[{"node": "Create Marketing Insight Data", "type": "main", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "Check if Marketing Insight Data Found", "type": "main", "index": 0}, {"node": "Check if Recurring Topics Found", "type": "main", "index": 0}, {"node": "Check if Actionable Insights Data Found", "type": "main", "index": 0}]]}, "Split Out Recurring Topics": {"main": [[{"node": "Create Recurring Topics Data", "type": "main", "index": 0}]]}, "Create Recurring Topics Data": {"main": [[{"node": "Bundle Recurring Topics Data to 1 object", "type": "main", "index": 0}]]}, "Create Marketing Insight Data": {"main": [[{"node": "Bundle Marketing Insights Data to 1 object", "type": "main", "index": 0}]]}, "Split Out Actionable Insights": {"main": [[{"node": "Create Actionable Insights Data", "type": "main", "index": 0}]]}, "Check if Recurring Topics Found": {"main": [[{"node": "Wait for rate limiting - Recurring", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON> Recurring Topics Thread", "type": "main", "index": 0}]]}, "Create Actionable Insights Data": {"main": [[{"node": "Bundle Actionable Insights Data to 1 object", "type": "main", "index": 0}]]}, "Wait for rate limiting - Recurring": {"main": [[{"node": "Split Out Recurring Topics", "type": "main", "index": 0}]]}, "Check if Marketing Insight Data Found": {"main": [[{"node": "Wait for rate limiting - Marketing Insights", "type": "main", "index": 0}], [{"node": "Merge Marketing Insights Thread", "type": "main", "index": 0}]]}, "Check if Actionable Insights Data Found": {"main": [[{"node": "Wait for rate limiting - Actionable Insights", "type": "main", "index": 0}], [{"node": "Merge Actionable Insights Thread", "type": "main", "index": 0}]]}, "Bundle Recurring Topics Data to 1 object": {"main": [[{"node": "<PERSON><PERSON> Recurring Topics Thread", "type": "main", "index": 0}]]}, "Bundle Marketing Insights Data to 1 object": {"main": [[{"node": "Merge Marketing Insights Thread", "type": "main", "index": 0}]]}, "Bundle Actionable Insights Data to 1 object": {"main": [[{"node": "Merge Actionable Insights Thread", "type": "main", "index": 0}]]}, "Wait for rate limiting - Marketing Insights": {"main": [[{"node": "Split out Insights", "type": "main", "index": 0}]]}, "Wait for rate limiting - Actionable Insights": {"main": [[{"node": "Split Out Actionable Insights", "type": "main", "index": 0}]]}}}