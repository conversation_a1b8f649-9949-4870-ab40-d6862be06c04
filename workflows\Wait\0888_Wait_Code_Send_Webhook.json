{"meta": {"instanceId": "4359279a248a64f23ddf72d3bc2de4dead8a687e643e9296f8a007dd65120396"}, "nodes": [{"id": "74a81d54-6cc9-4c17-88fe-aca27d491b73", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [640, 40], "webhookId": "1d3d0c06-f979-4573-b644-1a5b13153471", "parameters": {"path": "paypal-NVP-SOAP-Webhook", "options": {}, "httpMethod": "POST"}, "typeVersion": 2}, {"id": "59caade5-a67d-4d22-822c-bec8bf9baf69", "name": "Event Capture Type", "type": "n8n-nodes-base.switch", "position": [1160, 0], "parameters": {"rules": {"values": [{"outputKey": "Payment", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "68917137-6042-4e47-9432-d006dca17872", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.body.event_type }}", "rightValue": "=PAYMENT.CAPTURE.COMPLETED"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "cba1ef91-2e34-4bd5-9972-565296137851", "name": "Get Order Details", "type": "n8n-nodes-base.httpRequest", "position": [1360, 0], "parameters": {"url": "=https://api.paypal.com/v2/checkout/orders/{{ $json.body.resource.supplementary_data.related_ids.order_id }}", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "oAuth2Api"}, "typeVersion": 4.2}, {"id": "ecab1f76-8c53-459c-8c5f-26356ec9e675", "name": "Email Data", "type": "n8n-nodes-base.set", "position": [1540, 0], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "8d56c774-9adb-4981-9295-6f6f2ec59749", "name": "First Name", "type": "string", "value": "={{ $json.payment_source.paypal.name.given_name }}"}, {"id": "0f6136eb-f5e1-47b9-a829-f42dff2b7c9e", "name": "Last Name", "type": "string", "value": "={{ $json.payment_source.paypal.name.surname }}"}, {"id": "f4da90dc-b4d5-4951-91b8-2ef4b2bf870d", "name": "EmaiID", "type": "string", "value": "={{ $json.payment_source.paypal.email_address }}"}, {"id": "f7a31ec1-4305-4df0-8791-0f59a04f0c7e", "name": "Product Purchased", "type": "string", "value": "={{ $json.purchase_units[0].items[0].name }}"}]}}, "typeVersion": 3.4}, {"id": "211fbba0-67b1-4ece-b6a7-79b7c5cd0f7a", "name": "Wait", "type": "n8n-nodes-base.wait", "position": [920, 40], "webhookId": "16debf49-5196-473a-8b55-b2450b9b575a", "parameters": {}, "typeVersion": 1.1}, {"id": "c4b9bcab-42ab-4fca-b064-ab262cdcf05e", "name": "getJSON", "type": "n8n-nodes-base.httpRequest", "position": [2060, 0], "parameters": {"url": "https://your-json-template-in-ase-you-are-sellig.json", "options": {}}, "typeVersion": 4.2}, {"id": "b92f72a4-25c2-4c6d-9cc1-366cd1dc2dd1", "name": "Event Capture Type1", "type": "n8n-nodes-base.switch", "position": [1760, 0], "parameters": {"rules": {"values": [{"outputKey": "SocialMedia", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "68917137-6042-4e47-9432-d006dca17872", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json[\"Product Purchased\"] }}", "rightValue": "=AI-Powered Social Media Content Generator & Publisher"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "10f88f6c-1062-48c5-8a90-116c18954d95", "name": "Conver to File", "type": "n8n-nodes-base.code", "position": [2280, 0], "parameters": {"jsCode": "const content = JSON.stringify($json, null, 2); // Pretty-print JSON\n\nreturn [\n  {\n    binary: {\n      data: {\n        data: Buffer.from(content).toString('base64'),\n        mimeType: 'application/json',\n        fileName: 'data.json'\n      }\n    }\n  }\n];\n"}, "typeVersion": 2}, {"id": "4c95905c-0c77-488a-8fb3-e8f4f4b83e54", "name": "Send Email", "type": "n8n-nodes-base.emailSend", "position": [2600, 0], "webhookId": "e2895df8-6c42-44ff-ba08-fbf7a9df93c6", "parameters": {"html": "=<!DOCTYPE html>\n<html>\n<head>\n  <meta charset=\"UTF-8\">\n  <title>{{ $('Event Capture Type1').item.json['Product Purchased'] }}</title>\n</head>\n<body style=\"margin:0; padding:0; font-family: Arial, sans-serif; background-color: #f9f9f9;\">\n  <table align=\"center\" width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" style=\"max-width:600px; background-color:#ffffff; margin:20px auto; border-radius:8px; box-shadow:0 0 10px rgba(0,0,0,0.05);\">\n    <tr>\n      <td style=\"padding:30px; text-align:center;\">\n        <h2 style=\"color:#333;\">Hi {{ $('Event Capture Type1').item.json['First Name'] }} {{ $('Event Capture Type1').item.json['Last Name'] }} ,</h2>\n        <p style=\"font-size:16px; color:#555;\">Thank you for purchasing <strong> {{ $('Event Capture Type1').item.json['Product Purchased'] }}  - n8n workflow template</strong> from <strong>SyncBricks</strong>! 🚀</p>\n        <p style=\"font-size:16px; color:#555;\">Your template is attached with this email. We hope it helps you build powerful automations with ease.</p>\n        <hr style=\"margin:30px 0; border:none; border-top:1px solid #eee;\">\n        <p style=\"font-size:16px; color:#555;\">Here are some helpful resources to take things further:</p>\n        <ul style=\"list-style:none; padding:0; font-size:16px; color:#333;\">\n          <li style=\"margin-bottom:10px;\"><a href=\"https://www.udemy.com/course/mastering-n8n-ai-agents-api-automation-webhooks-no-code/?referralCode=0309FD70BE2D72630C09\" style=\"color:#0066cc; text-decoration:none;\">📘 Enroll for the n8n Mastery Course</a></li>\n          <li style=\"margin-bottom:10px;\"><a href=\"https://lms.syncbricks.com/books/n8n/\" style=\"color:#0066cc; text-decoration:none;\">📖 Get the Book: Mastering n8n</a></li>\n          <li style=\"margin-bottom:10px;\"><a href=\"https://n8n.syncbricks.com\" style=\"color:#0066cc; text-decoration:none;\">☁️ Try n8n Cloud – Use Code <strong>AMJID10</strong> for Discount</a></li>\n        </ul>\n\n<p style=\"font-size:16px; color:#555;\">🎥 Watch a quick guide on how to use your template:</p>\n<a href=\"https://www.youtube.com/watch?v=-Oc_HfreJJE\" target=\"_blank\" style=\"display:inline-block; text-decoration:none;\">\n  <img src=\"https://img.youtube.com/vi/-Oc_HfreJJE/hqdefault.jpg\" alt=\"Watch on YouTube\" style=\"width:100%; max-width:500px; border-radius:6px; box-shadow:0 0 10px rgba(0,0,0,0.1); margin:10px 0;\">\n</a>\n\n\n        <p style=\"font-size:14px; color:#999; margin-top:40px;\">Need help or have questions? Just reply to this email .</p>\n      </td>\n    </tr>\n  </table>\n</body>\n</html>\n", "options": {"attachments": "data", "appendAttribution": false}, "subject": "=Your Order : {{ $('Get Order Details').item.json.purchase_units[0].items[0].name }}", "toEmail": "={{ $('Email Data').item.json.EmaiID }}", "fromEmail": "Syncbricks <<EMAIL>>"}, "typeVersion": 2.1}, {"id": "d859f5b9-db4f-4df8-a806-a806349092ee", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [580, -160], "parameters": {"width": 520, "height": 500, "content": "## Paypal  Webhook\n**Go to Paypal Developer\nClick on Apps and Credentails\nGo to NVP SOAP Webhooks\nAdd this Webhook in Paypal\n\n- Wait node is to ensure that Transaction is completed\n"}, "typeVersion": 1}, {"id": "f4c545d8-f978-4b49-8cdb-3b6b427544f8", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [-400, -180], "parameters": {"color": 4, "width": 955, "height": 516, "content": "## Developed by <PERSON><PERSON>\n\nThank you for using this workflow template. It has taken me countless hours of hard work, research, and dedication to develop, and I sincerely hope it adds value to your work.\n\nIf you find this template helpful, I kindly ask you to consider supporting my efforts. Your support will help me continue improving and creating more valuable resources.\n\nYou can contribute via PayPal here:\n\nhttp://paypal.me/pmptraining\n\nFor Full Course about ERPNext or Automation using AI follow below link\n\nhttp://lms.syncbricks.com\n\nAdditionally, when sharing this template, I would greatly appreciate it if you include my original information to ensure proper credit is given.\n\nThank you for your generosity and support!\nEmail : <EMAIL>\nhttps://linkedin.com/in/amjidali\nhttps://syncbricks.com\nhttps://youtube.com/@syncbricks"}, "typeVersion": 1}, {"id": "769fc3b3-0cbe-4334-a990-83c86a0e5fc2", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1120, -160], "parameters": {"width": 580, "height": 500, "content": "## Payment Detials are Selected\n**The webhook gets all the Events but here we are filtering only the payments that we got agains the order.\n** It will get the customer name, Email Address and the Product customer has bought"}, "typeVersion": 1}, {"id": "835a6eb1-0b73-4d52-92b1-253fb1f150ac", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1720, -160], "parameters": {"width": 280, "height": 500, "content": "## Filter the Product \n** Each Product can have multiple Product Links on successful Purchase"}, "typeVersion": 1}, {"id": "54a284f2-566b-4dea-9e5f-6e55f5faefa4", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [2020, -160], "parameters": {"width": 420, "height": 500, "content": "## n8n Template Sale\n** as I am selling n8n template, it will download json file and will convert that into binay file"}, "typeVersion": 1}, {"id": "20645f67-af3d-4ebf-bab1-488937182205", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [2460, -160], "parameters": {"color": 4, "width": 420, "height": 500, "content": "## Send Email to Custoer\n** The download file will be attafched and my Custom Email will be sent to Customer"}, "typeVersion": 1}], "pinData": {}, "connections": {"Wait": {"main": [[{"node": "Event Capture Type", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "getJSON": {"main": [[{"node": "Conver to File", "type": "main", "index": 0}]]}, "Email Data": {"main": [[{"node": "Event Capture Type1", "type": "main", "index": 0}]]}, "Conver to File": {"main": [[{"node": "Send Email", "type": "main", "index": 0}]]}, "Get Order Details": {"main": [[{"node": "Email Data", "type": "main", "index": 0}]]}, "Event Capture Type": {"main": [[{"node": "Get Order Details", "type": "main", "index": 0}]]}, "Event Capture Type1": {"main": [[{"node": "getJSON", "type": "main", "index": 0}]]}}}