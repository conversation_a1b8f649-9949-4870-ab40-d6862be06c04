{"meta": {"instanceId": "b41148c809c7896d124743d940fc0964703e540af66564ef95e25a4ceea61c77", "templateCredsSetupCompleted": true}, "nodes": [{"id": "070fd7b4-58ca-4372-a347-6f60f590e20b", "name": "Receive Telegram Messages", "type": "n8n-nodes-base.telegramTrigger", "position": [40, 140], "webhookId": "4e2cd560-ae4e-4ed7-a8ea-984518404e51", "parameters": {"updates": ["message"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "lff3pLERRdQmkmeV", "name": "Telegram account"}}, "typeVersion": 1.1}, {"id": "0e0f3a32-fbde-42a9-aa7f-70fda7b05357", "name": "Voice or Text?", "type": "n8n-nodes-base.switch", "position": [400, 220], "parameters": {"rules": {"values": [{"outputKey": "Audio", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "af30c479-4542-405f-b315-37c50c4e2bef", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.message.voice.file_id }}", "rightValue": ""}]}, "renameOutput": true}, {"outputKey": "Text", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a3ca8cd4-fbb2-40b5-829a-24724f2fbc85", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.message.text || \"\" }}", "rightValue": ""}]}, "renameOutput": true}, {"outputKey": "Error", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "9bcfdee0-2f09-4037-a7b9-689ef392371d", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "error", "rightValue": ""}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "b01dde88-bede-4500-974f-b2dc203ff841", "name": "Fetch Voice Message", "type": "n8n-nodes-base.telegram", "position": [760, 120], "webhookId": "********-4943-4c32-b18c-97c410cc3409", "parameters": {"fileId": "={{ $json.message.voice.file_id }}", "resource": "file"}, "credentials": {"telegramApi": {"id": "lff3pLERRdQmkmeV", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "fe91414e-3b10-482e-b8dd-d55266828dd7", "name": "Transcribe Voice to Text", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [980, 120], "parameters": {"options": {}, "resource": "audio", "operation": "translate"}, "credentials": {"openAiApi": {"id": "uFPD9I4pWJ4xUVf7", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "********-fd4d-4824-a561-944f2f536b9b", "name": "Prepare for LLM", "type": "n8n-nodes-base.set", "position": [880, 340], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "b324a329-3c49-4f7f-b683-74331b7fe7f8", "name": "=text", "type": "string", "value": "={{$json.message.text}}"}]}}, "typeVersion": 3.4}, {"id": "886246ad-7127-462a-a2b2-b4281f369d8b", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-40, 0], "parameters": {"width": 260, "height": 320, "content": " \n**This workflow listens for incoming voice or text messages from Telegram users.** "}, "typeVersion": 1}, {"id": "d052bd49-dc23-4ec5-b153-a9eb305f0641", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [700, 20], "parameters": {"width": 460, "height": 260, "content": " **Voice messages are fetched from Telegram and transcribed into text using OpenAI's Whisper API.**  "}, "typeVersion": 1}, {"id": "156580f1-adf5-43ba-b54d-89b84ca87818", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1460, 320], "parameters": {"text": "={{$json.text}}", "options": {"systemMessage": " \n**1. AI Agent Goal Prompt (Overall Task)**\n\n*   **Purpose:** To define the agent's overall objective, removing the image creation step.\n\n```\nYou are an AI social media content creator. Your task is to research a given topic using SerpAPI, create engaging and SEO-optimized social media content (800-1000 characters), and generate a detailed image prompt. The content must be factually accurate and engaging. Prioritize factual accuracy and engaging storytelling in your content. The generated image prompt should be detailed and specific enough to be used with an image generation tool like DALL-E or Stable Diffusion.\n```\n\n**2. SerpAPI Tool Prompt (Research Phase)**\n\n*   **Purpose:** To instruct the agent on how to use SerpAPI to effectively gather information. (No change from previous version)\n\n```\nUse the SerpAPI tool to research the following topic: [TOPIC]. Focus on identifying key facts, trends, and interesting angles relevant for social media. Extract information from the top search results. Return a summary of the information you found focusing on key data and facts.\n```\n\n*   **Explanation:**\n    *   `[TOPIC]` is a variable that will be replaced with the specific topic.\n    *   Focuses the agent on extracting key facts and trends rather than just providing a list of results.\n    *   Limits the scope to the top search results to maintain efficiency.\n\n**3. Social Media Content Creation Prompt**\n\n*   **Purpose:** To guide the agent in creating engaging and SEO-friendly content based on the research. (No change from previous version)\n\n```\nBased on the following research summary: [RESEARCH_SUMMARY], create a social media post that is:\n\n*   Engaging and attention-grabbing\n*   Factually accurate\n*   Optimized for SEO (include relevant keywords naturally)\n*   Within 800-1000 characters\n*   Clearly and concisely written.\n*   Avoid jargon and technical terms.\n*    Include a call to action.\n\nThe tone should be informative but also enthusiastic and easily understandable.\n```\n\n*   **Explanation:**\n    *   `[RESEARCH_SUMMARY]` will be replaced with the output from the SerpAPI tool.\n    *   Specific instructions on tone, length, and SEO optimization.\n    *   Explicitly asks for clear and concise writing, avoiding jargon.\n    *   Added \"Include a call to action\" to make the content more actionable\n\n**4. Image Generation Prompt (for hypothetical image generation tool)**\n\n*   **Purpose:** To create a prompt that generates a detailed and descriptive image prompt for an image generation tool.  This prompt should now be the *final output* related to the image.\n\n```\nBased on the following topic: [TOPIC] and social media content: [SOCIAL_MEDIA_CONTENT], generate a detailed image prompt for a photorealistic image that visually represents the topic and complements the content. The image should be:\n\n*   Photorealistic and high-quality.\n*   Visually appealing and attention-grabbing.\n*   Relevant to the topic and content.\n*   Appropriate for social media.\n\nThe prompt should be exceptionally detailed and specific, providing precise instructions for an image generation tool like DALL-E or Stable Diffusion. Include details about the subject, setting, style, lighting, camera angles, and any other relevant visual elements.  Aim for a prompt that leaves no room for misinterpretation by the image generation AI.  Mention specific artists or photographic styles to emulate if appropriate.\n```\n\n*   **Explanation:**\n    *   `[TOPIC]` and `[SOCIAL_MEDIA_CONTENT]` are variables that will be replaced with the topic and the created social media content, respectively.\n    *   Focuses on *photorealism*, relevance, and visual appeal.\n    *   Emphasizes the need for an *exceptionally detailed and specific* prompt for the image generation tool.\n    *  Explicitly mentions DALL-E and Stable Diffusion as target tools.\n    *   Advises the inclusion of artist styles or photographic techniques to guide the image generation.\n\n**5. JSON Output Instruction**\n\n*   **Purpose:** To ensure the AI agent provides the output in the correct format.  The `image_url` field is replaced with `image_prompt`.\n\n```\nAfter generating the social media content and the image prompt, output the results in the following JSON format:\n\n```json\n{\n\"content\": \"[SOCIAL_MEDIA_CONTENT]\",\n\"image_prompt\": \"[IMAGE_PROMPT]\"\n}\n```\n\n`[SOCIAL_MEDIA_CONTENT]` is the social media content you created.\n`[IMAGE_PROMPT]` is the detailed image prompt you generated.\n```\n\n**Example Usage:**\n\nLet's say the topic is still \"The Benefits of Regular Exercise.\"\n\n1.  **SerpAPI Tool:** The agent uses SerpAPI to find information about the benefits of exercise.\n2.  **Social Media Content:** The agent generates content like: \"Boost your mood & health! 💪 Regular exercise reduces stress, improves sleep, and lowers disease risk. Get moving today! #exercise #healthylifestyle #fitness\"\n3.  **Image Prompt:** The agent generates an image prompt like: \"A photorealistic image of a diverse group of people happily participating in various forms of exercise in a vibrant outdoor setting. Some are jogging in a park with lush green trees, others are doing yoga poses on a grassy field, and a few are cycling on a paved path. The lighting is warm and golden, as if it's early morning or late afternoon. The style should be reminiscent of a National Geographic photograph, emphasizing the natural beauty of the scene and the healthy glow of the people. Use a shallow depth of field to blur the background slightly, drawing focus to the subjects. Camera angle: slightly low, capturing the energy and movement of the scene. Consider influences from the photographic style of Steve McCurry.\"\n4.  **JSON Output:**\n\n```json\n{\n\"content\": \"Boost your mood & health! 💪 Regular exercise reduces stress, improves sleep, and lowers disease risk. Get moving today! #exercise #healthylifestyle #fitness\",\n\"image_prompt\": \"A photorealistic image of a diverse group of people happily participating in various forms of exercise in a vibrant outdoor setting. Some are jogging in a park with lush green trees, others are doing yoga poses on a grassy field, and a few are cycling on a paved path. The lighting is warm and golden, as if it's early morning or late afternoon. The style should be reminiscent of a National Geographic photograph, emphasizing the natural beauty of the scene and the healthy glow of the people. Use a shallow depth of field to blur the background slightly, drawing focus to the subjects. Camera angle: slightly low, capturing the energy and movement of the scene. Consider influences from the photographic style of Steve McCurry.\"\n}\n```\n\n**Key Improvements and Techniques Used (Beyond the Previous Version):**\n\n*   **Focus on Photorealism:**  The image prompt now explicitly aims for photorealistic results.\n*   **Detailed Image Prompting:** The prompt emphasizes extreme detail and specificity in the image prompt.\n*   **Tool Agnostic:** The prompt mentions DALL-E and Stable Diffusion as example tools, but is designed to be usable with other image generation AIs.\n*   **Artist Style Guidance:** The prompt encourages the inclusion of artist or photographic style references.\n\n \n"}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.7}, {"id": "74f99543-d7e6-4d9b-8af1-9d86e0566ddc", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1460, 560], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "uFPD9I4pWJ4xUVf7", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "62b9bb5f-0c87-4df4-ac1c-70b96a0a5cc4", "name": "SerpAPI", "type": "@n8n/n8n-nodes-langchain.toolSerpApi", "position": [1580, 560], "parameters": {"options": {}}, "credentials": {"serpApi": {"id": "AuYW6wcagKBXR214", "name": "SerpAPI account"}}, "typeVersion": 1}, {"id": "3782a100-3210-4c87-9e6a-5808cd488601", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [1700, 560], "parameters": {"jsonSchemaExample": "{\n\"content\": \"[SOCIAL_MEDIA_CONTENT]\",\n\"image_prompt\": \"[IMAGE_PROMPT]\"\n}"}, "typeVersion": 1.2}, {"id": "87bf4b00-4f75-4098-993b-b4bd168339c2", "name": "Extract from File", "type": "n8n-nodes-base.extractFromFile", "position": [2380, 320], "parameters": {"options": {}, "operation": "binaryToPropery"}, "typeVersion": 1}, {"id": "9a273f0b-bcb0-4ed8-93f5-6161d192e3ef", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1460, 160], "parameters": {"width": 280, "height": 140, "content": " **The AI agent uses the OpenAI Chat Model and SerpAPI tool to conduct research and generate social media content and an image prompt based on the user request.**"}, "typeVersion": 1}, {"id": "c907aa15-1ccf-475e-94da-3a81e54b3746", "name": "Prepare Final Output", "type": "n8n-nodes-base.set", "position": [2740, 320], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "df5eb034-ef40-44a3-a620-48981efd1a69", "name": "content", "type": "string", "value": "={{ $('AI Agent').item.json.output.content }}"}, {"id": "9ed8afc9-a957-4aea-8554-8c67017ef0e6", "name": "image", "type": "string", "value": "={{ $json.data }}"}]}}, "typeVersion": 3.4}, {"id": "0893eac1-e72b-4a95-8c3c-4803aaaed9b9", "name": "Generate Image", "type": "n8n-nodes-base.httpRequest", "position": [2020, 320], "parameters": {"url": "https://router.huggingface.co/hf-inference/models/stabilityai/stable-diffusion-3.5-large", "method": "POST", "options": {}, "sendBody": true, "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "inputs", "value": "={{ $json.output.image_prompt }}"}]}, "nodeCredentialType": "huggingFace<PERSON>pi"}, "credentials": {"httpHeaderAuth": {"id": "ERi7DgDYlifAQg7i", "name": "Header Auth account"}, "huggingFaceApi": {"id": "2koOz09ZdzCYUNif", "name": "HuggingFaceApi account"}}, "typeVersion": 4.2}, {"id": "b99a090f-73de-4703-b569-8992df36132f", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1960, 240], "parameters": {"width": 220, "height": 240, "content": " **An image is generated using the image prompt**  "}, "typeVersion": 1}], "pinData": {}, "connections": {"SerpAPI": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Generate Image", "type": "main", "index": 0}]]}, "Generate Image": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "Voice or Text?": {"main": [[{"node": "Fetch Voice Message", "type": "main", "index": 0}], [{"node": "Prepare for LLM", "type": "main", "index": 0}]]}, "Prepare for LLM": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "Prepare Final Output", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Fetch Voice Message": {"main": [[{"node": "Transcribe Voice to Text", "type": "main", "index": 0}]]}, "Prepare Final Output": {"main": [[]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "AI Agent", "type": "ai_outputParser", "index": 0}]]}, "Transcribe Voice to Text": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Receive Telegram Messages": {"main": [[{"node": "Voice or Text?", "type": "main", "index": 0}]]}}}