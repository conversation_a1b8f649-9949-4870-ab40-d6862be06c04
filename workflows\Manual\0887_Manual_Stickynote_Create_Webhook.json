{"meta": {"instanceId": "96b96d0aa1e4ff5d5b6779332b149e3ef3364191562d79083d0309cf3ddfa53e"}, "nodes": [{"id": "0e78a29e-87ba-4665-84c1-a413c45e25dc", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-420, -40], "parameters": {}, "typeVersion": 1}, {"id": "fe7b054a-e0c9-4642-a97f-6bec60a1ec55", "name": "Edit Image (OpenAI)", "type": "n8n-nodes-base.httpRequest", "position": [500, -40], "parameters": {"url": "https://api.openai.com/v1/images/edits", "method": "POST", "options": {}, "sendBody": true, "contentType": "multipart-form-data", "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "image", "parameterType": "formBinaryData", "inputDataFieldName": "data"}, {"name": "prompt", "value": "add a mask with horns"}, {"name": "model", "value": "gpt-image-1"}, {"name": "n", "value": "1"}, {"name": "size", "value": "1024x1024"}, {"name": "quality", "value": "high"}]}, "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "JyI0PkPec1FrpMkt", "name": "OpenAi AIFB account"}}, "typeVersion": 4.2}, {"id": "1e1df05c-d8f9-4033-87ee-70be344ab961", "name": "Create image call", "type": "n8n-nodes-base.httpRequest", "position": [-120, -40], "parameters": {"url": "https://api.openai.com/v1/images/generations", "method": "POST", "options": {}, "sendBody": true, "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "model", "value": "gpt-image-1"}, {"name": "prompt", "value": "A cute red panda like dark super hero"}, {"name": "n", "value": "={{Number(1)}}"}, {"name": "size", "value": "1024x1024"}, {"name": "moderation", "value": "low"}, {"name": "background", "value": "auto"}]}, "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "JyI0PkPec1FrpMkt", "name": "OpenAi AIFB account"}}, "typeVersion": 4.2}, {"id": "4c44da91-0d12-4e7f-bc89-5accddd837d7", "name": "Convert json binary to File", "type": "n8n-nodes-base.convertToFile", "position": [200, -40], "parameters": {"options": {"fileName": "name_example", "mimeType": "image/png"}, "operation": "toBinary", "sourceProperty": "data[0].b64_json"}, "typeVersion": 1.1}, {"id": "3b8936b7-f0a2-4776-b10a-f06ceb9af31d", "name": "Convert json binary to File final", "type": "n8n-nodes-base.convertToFile", "position": [820, -40], "parameters": {"options": {"fileName": "", "mimeType": "image/png"}, "operation": "toBinary", "sourceProperty": "data[0].b64_json"}, "typeVersion": 1.1}, {"id": "3d3238d5-6040-4b74-8e6a-9e1e64198099", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-500, -200], "parameters": {"height": 320, "content": "### 🧪 Manual Trigger\nStarts the workflow manually. Ideal for testing and debugging purposes.\n"}, "typeVersion": 1}, {"id": "c3378100-f688-4199-a038-83b9220afa91", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-200, -320], "parameters": {"color": 3, "width": 280, "height": 440, "content": "### 🎨 Image Generation (OpenAI)\nSends a POST request to the OpenAI `/v1/images/generations` endpoint.\n\n- Uses `gpt-image-1` model\n- Generates an image from a given prompt\n- Returns a base64-encoded image (`b64_json`)\n\n📌 Output: `data[0].b64_json`\n"}, "typeVersion": 1}, {"id": "82a880de-74de-44e5-8448-f487c9376d0e", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [100, -200], "parameters": {"color": 5, "width": 280, "height": 320, "content": "### 🧾 Convert base64 to File\nConverts the `b64_json` field into a binary PNG file to use in the next step.\n\n📤 Output: Binary image under the `data` field\n"}, "typeVersion": 1}, {"id": "42ccb29f-b820-4791-9683-4eb0f00ff2d3", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [420, -320], "parameters": {"color": 3, "width": 280, "height": 440, "content": "### ✏️ Image Editing (OpenAI)\nSends the binary image to OpenAI’s `/v1/images/edits` endpoint with a descriptive prompt.\n\n- Model: `gpt-image-1`\n- Format: `multipart/form-data`\n- Requires a real file (not base64)\n- Supports optional `mask` input\n\n📥 Input: Binary image from `data`\n📤 Output: Edited image in `b64_json`\n"}, "typeVersion": 1}, {"id": "4c8846ab-b3f2-4c7c-9283-5a40a55b816d", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [740, -240], "parameters": {"color": 5, "width": 280, "height": 360, "content": "### 🧾 Final Conversion (base64 → File)\nConverts the edited image (`b64_json`) into a downloadable or previewable PNG file.\n\n📤 Output: Final binary image\n"}, "typeVersion": 1}, {"id": "2b2533f8-b7aa-4499-970e-9b0546b73c0e", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-1240, -860], "parameters": {"color": 6, "width": 700, "height": 980, "content": "## 🧠 Image AI Workflow Overview\n\nThis workflow uses OpenAI's image generation and editing APIs with the `gpt-image-1` model.\n\n### 🔑 Requirements:\n- You **must use your own OpenAI API key** from https://platform.openai.com/account/api-keys\n- Create a credential in n8n called `OpenAi AIFB account` (or use your own name)\n\n---\n\n### 💰 Cost Warning:\n- This model is **powerful but expensive**.\n- Each image costs **$0.020 to $0.190** depending on resolution and type.\n- Always monitor your usage via the [OpenAI dashboard](https://platform.openai.com/account/usage)\n\n---\n\n### 🔍 Why use `gpt-image-1`?\n- Unmatched **semantic control**: you can edit specific parts of images with detailed prompts.\n- Supports **multiple input images**, coherent edits, and future multi-modal tasks.\n- Editing works with or without a transparency `mask`.\n\n---\n\n### 🔧 Suggested Nodes to Expand Workflow:\n- **Webhook** (trigger via your frontend or app)\n- **Telegram / Slack** (prompt image generation from chat)\n- **Set node** (inject dynamic prompts or user context)\n- **IF / Switch** (change behavior depending on prompt type)\n- **Merge** (combine multiple image generations)\n- **HTTP Request** (send generated images to external APIs or CMS)\n\n---\n\n### 💡 Example Use Cases:\n- Marketing teams: generate product visuals on demand\n- Designers: edit and re-style illustrations without Photoshop\n- E-commerce: dynamic generation of themed mockups\n- Content creators: create blog and social thumbnails in bulk\n\n---\n\n> ⚠️ Don't forget to add rate limiting or batch controls if generating large volumes!\n"}, "typeVersion": 1}], "pinData": {}, "connections": {"Create image call": {"main": [[{"node": "Convert json binary to File", "type": "main", "index": 0}]]}, "Edit Image (OpenAI)": {"main": [[{"node": "Convert json binary to File final", "type": "main", "index": 0}]]}, "Convert json binary to File": {"main": [[{"node": "Edit Image (OpenAI)", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Create image call", "type": "main", "index": 0}]]}}}