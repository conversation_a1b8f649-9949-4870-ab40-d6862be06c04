{"meta": {"instanceId": "6045c639951d83c8706b0dd8d6330164bda01fe58f103cedc2c276bf1f9c11f1"}, "nodes": [{"id": "ab8e653f-a60c-497c-b732-6dea355aa985", "name": "Compare the two Datasets", "type": "n8n-nodes-base.compareDatasets", "position": [900, 160], "parameters": {"options": {}, "mergeByFields": {"values": [{"field1": "Playlist avant a<PERSON>t", "field2": "Nouvelle pistes"}]}}, "typeVersion": 2.3}, {"id": "606aa397-efd6-4f6b-bfa6-946523ed80f2", "name": "Extract the spotify track ID", "type": "n8n-nodes-base.set", "position": [580, 80], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "dd3db6c8-ecf5-4595-ac4b-559965b6e507", "name": "Playlist avant a<PERSON>t", "type": "string", "value": "={{ $json.track.id }}"}]}}, "typeVersion": 3.4}, {"id": "75e48bf0-5003-4904-b8c7-0cca005bacd7", "name": "Extract the Spotify Track ID", "type": "n8n-nodes-base.set", "position": [580, 260], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "a9593caf-e403-4626-a96f-499e9f78465e", "name": "Nouvelle pistes", "type": "string", "value": "={{ $json.id }}"}]}}, "typeVersion": 3.4}, {"id": "c536f1fb-cfbe-4a22-8f8f-37422629cc2b", "name": "Find the returned tracks on Spotify", "type": "n8n-nodes-base.spotify", "position": [580, 440], "parameters": {"limit": "={{ 1 }}", "query": "={{ $json.snippet.title }}", "filters": {}, "resource": "track", "operation": "search"}, "credentials": {"spotifyOAuth2Api": {"id": "sJyANc6jgR7IWZ20", "name": "Spotify account"}}, "typeVersion": 1}, {"id": "6be6eb69-0e90-46d8-9e74-92372c9ed5b8", "name": "Get my tracks inside my playlist", "type": "n8n-nodes-base.youTube", "position": [160, 280], "parameters": {"part": ["snippet"], "options": {}, "resource": "playlistItem", "operation": "getAll", "returnAll": true, "playlistId": "=******************"}, "credentials": {"youTubeOAuth2Api": {"id": "QhzjhQ4w5yvTdBIN", "name": "YouTube account"}}, "executeOnce": true, "typeVersion": 1}, {"id": "8a2d297f-748c-4e59-a935-fecc944060aa", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [360, 280], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "677e635b-8ae6-48b4-8687-0615a044739c", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-80, 180], "parameters": {}, "typeVersion": 1}, {"id": "d7e52845-2279-40a5-82d3-5a923ead191c", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-640, -40], "parameters": {"width": 517.*************, "height": 654.*************, "content": "## Workflow Overview\n\nThis workflow automates the process of updating a Spotify playlist with tracks from a YouTube playlist, ensuring no duplicates are added.\n\n## Key Components\n\n1. **Manual Trigger**: Starts the workflow when you click ‘Test workflow’.\n   \n2. **Spotify Integration**: Retrieves tracks from a specified Spotify playlist.\n\n3. **YouTube Integration**: Fetches tracks from a designated YouTube playlist.\n\n4. **Batch Processing**: Processes tracks in batches to handle multiple items efficiently.\n\n5. **Track Search**: Searches for YouTube tracks on Spotify to find corresponding IDs.\n\n6. **Comparison**: Compares existing Spotify tracks with YouTube tracks to identify which ones to add.\n\n7. **Track Addition**: Adds new Spotify tracks to the playlist that are not already included.\n\nIf you have any questions or need clarification, feel free to ask!\n"}, "typeVersion": 1}, {"id": "cd92585a-6c56-4a35-8714-96d2c73444bd", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [60, 0], "parameters": {"color": 5, "width": 251.**************, "height": 468.*************, "content": "### Retrieve the playlists you want to synchronise "}, "typeVersion": 1}, {"id": "a0ec1b4c-2422-4daa-92d6-4c84a1cecbf6", "name": "Get tracks inside the Spotify Playlist", "type": "n8n-nodes-base.spotify", "position": [160, 80], "parameters": {"id": "5SY22gVudzaD31v5rq5jcH", "resource": "playlist", "operation": "getTracks", "returnAll": true}, "credentials": {"spotifyOAuth2Api": {"id": "sJyANc6jgR7IWZ20", "name": "Spotify account"}}, "typeVersion": 1}, {"id": "accba86b-6786-412e-8e87-17be458f6255", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [320, 620], "parameters": {"color": 6, "width": 414.**************, "height": 80, "content": "### Search for the tracks on spotify one-by-one"}, "typeVersion": 1}, {"id": "062e4341-bb5c-4302-85f6-dedb03481e64", "name": "Add tracks not already in playlist", "type": "n8n-nodes-base.spotify", "position": [1120, 300], "parameters": {"id": "spotify:playlist:5SY22gVudzaD31v5rq5jcH", "trackID": "=spotify:track:{{ $json['Nouvelle pistes'] }}", "resource": "playlist", "additionalFields": {}}, "credentials": {"spotifyOAuth2Api": {"id": "sJyANc6jgR7IWZ20", "name": "Spotify account"}}, "typeVersion": 1}], "pinData": {}, "connections": {"Loop Over Items": {"main": [[{"node": "Extract the Spotify Track ID", "type": "main", "index": 0}], [{"node": "Find the returned tracks on Spotify", "type": "main", "index": 0}]]}, "Compare the two Datasets": {"main": [null, null, null, [{"node": "Add tracks not already in playlist", "type": "main", "index": 0}]]}, "Extract the Spotify Track ID": {"main": [[{"node": "Compare the two Datasets", "type": "main", "index": 1}]]}, "Extract the spotify track ID": {"main": [[{"node": "Compare the two Datasets", "type": "main", "index": 0}]]}, "Get my tracks inside my playlist": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Get my tracks inside my playlist", "type": "main", "index": 0}, {"node": "Get tracks inside the Spotify Playlist", "type": "main", "index": 0}]]}, "Find the returned tracks on Spotify": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Get tracks inside the Spotify Playlist": {"main": [[{"node": "Extract the spotify track ID", "type": "main", "index": 0}]]}}}