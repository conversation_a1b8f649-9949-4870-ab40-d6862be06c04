{"meta": {"instanceId": "dbd43d88d26a9e30d8aadc002c9e77f1400c683dd34efe3778d43d27250dde50"}, "nodes": [{"id": "6d80ce75-833e-4565-9b03-0280e29d5b47", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "notes": "Format response", "position": [920, 600], "parameters": {"options": {"destinationFieldName": "Transfers"}, "fieldToSplitOut": "token_transfers"}, "notesInFlow": true, "typeVersion": 1}, {"id": "a6a299cf-4c43-46c1-98a8-e4ce6cd3ca48", "name": "<PERSON>", "type": "n8n-nodes-base.set", "notes": "<PERSON>et Config", "position": [640, 600], "parameters": {"fields": {"values": [{"name": "Your Wallet Address", "stringValue": "<Your TRC20 Wallet Address here>"}, {"name": "Number of transactions to retrieve per request", "type": "numberValue", "numberValue": "20"}]}, "options": {}}, "notesInFlow": true, "typeVersion": 3.2}, {"id": "e4e91632-bccb-468f-bbb4-a918a5815bbe", "name": "Filter", "type": "n8n-nodes-base.filter", "notes": "Received in last 15m only", "position": [1060, 600], "parameters": {"conditions": {"number": [{"value1": "={{ $json.Transfers.block_ts }}", "value2": "={{ Date.now() - 900000 }}", "operation": "smallerEqual"}], "string": [{"value1": "={{ $json.Transfers.to_address }}", "value2": "={{ $('Edit Fields').item.json['Your Wallet Address'] }}", "operation": "regex"}]}}, "notesInFlow": true, "typeVersion": 1}, {"id": "1f9f2f52-bbbe-4d4c-a309-64424f9392c0", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [460, 460], "parameters": {"color": 4, "width": 1120.1887804878038, "height": 353.65439024390236, "content": "## USDT TRC20 Wallet Tracker\n**This workflow** Is a basic concept of integrating your TRC20 wallet with n8n nodes.\n\n"}, "typeVersion": 1}, {"id": "31c8c3db-8f48-4cd7-ae1b-9caf579ebb9b", "name": "TronScan API", "type": "n8n-nodes-base.httpRequest", "notes": "Request Wallet Status", "position": [780, 600], "parameters": {"url": "https://apilist.tronscanapi.com/api/filter/trc20/transfers", "options": {"timeout": 10000, "redirect": {"redirect": {}}, "allowUnauthorizedCerts": true}, "sendQuery": true, "sendHeaders": true, "queryParameters": {"parameters": [{"name": "limit", "value": "={{ $json['Number of transactions to retrieve'] | '20' }}"}, {"name": "start", "value": "0"}, {"name": "sort", "value": "-timestamp"}, {"name": "count", "value": "true"}, {"name": "filterTokenValue", "value": "0"}, {"name": "relatedAddress", "value": "={{ $json['Your Wallet Address']}}"}]}, "headerParameters": {"parameters": [{"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:122.0) Gecko/20100101 Firefox/122.0"}, {"name": "Accept", "value": "application/json, text/plain, */*"}, {"name": "Accept-Language", "value": "en-US,en;q=0.5"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Origin", "value": "https://tronscan.org"}, {"name": "DNT", "value": "1"}, {"name": "Connection", "value": "keep-alive"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://tronscan.org/"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "Sec-GPC", "value": "1"}, {"name": "Pragma", "value": "no-cache"}, {"name": "Cache-Control", "value": "no-cache"}]}}, "notesInFlow": true, "typeVersion": 4.1}, {"id": "d9e1df8b-0bd7-41c4-a4a9-5df909821534", "name": "Final Results", "type": "n8n-nodes-base.set", "notes": "Keep only required fields", "position": [1220, 600], "parameters": {"fields": {"values": [{"name": "Coin", "stringValue": "={{ $json.Transfers.tokenInfo.tokenName }} ({{ $json.Transfers.tokenInfo.tokenAbbr }})"}, {"name": "Network", "stringValue": "={{ $json.Transfers.tokenInfo.tokenType }}"}, {"name": "From Address", "stringValue": "={{ $json.Transfers.from_address.replace($('Edit Fields').item.json['Your Wallet Address'],\"Your Wallet Address\") || $json.Transfers.from_address_tag.from_address_tag }}"}, {"name": "To Address", "stringValue": "={{ $json.Transfers.to_address.replace($('Edit Fields').item.json['Your Wallet Address'],\"Your Wallet Address\") }}"}, {"name": "Amount", "stringValue": "={{ ($('Filter').item.json[\"Transfers\"][\"tokenInfo\"][\"tokenAbbr\"]+' ' + (($json.Transfers.trigger_info.parameter._value || $json.Transfers.quant) / Math.pow(10, $json.Transfers.tokenInfo.tokenDecimal)).toFixed(2)).replace('USDT ','\\$') }}"}, {"name": "Record Type", "stringValue": "={{ $json.Transfers.event_type }}"}, {"name": "Record Status", "stringValue": "={{ ($json.Transfers.confirmed+'').replace('true','Confirmed').replace('false','Not confirmed yet.') }}"}, {"name": "Transaction Result", "stringValue": "={{ $json.Transfers.finalResult.replace('SUCCESS','Received') }}"}, {"name": "Record URL", "stringValue": "=https://tronscan.org/#/transaction/{{ $json.Transfers.transaction_id }}"}]}, "include": "none", "options": {}}, "notesInFlow": true, "typeVersion": 3.2}, {"id": "e6fcf3ba-ac81-49ce-86b5-a51df76dbf00", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "notes": "Run Every 15 minutes", "position": [500, 600], "parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 15}]}}, "notesInFlow": true, "typeVersion": 1.1}, {"id": "5149f131-a87e-40ed-88df-7fb0591fe31c", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "notes": "Combine records into one list", "position": [1400, 600], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "Transactions"}, "notesInFlow": true, "typeVersion": 1, "alwaysOutputData": false}], "pinData": {}, "connections": {"Filter": {"main": [[{"node": "Final Results", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Filter", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "TronScan API", "type": "main", "index": 0}]]}, "TronScan API": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Final Results": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}}}