{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "5a421900-20d7-4d64-a064-3211c3338676", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-520, -820], "parameters": {"width": 432, "height": 397, "content": "## Self-coded LLM Chain Node"}, "typeVersion": 1}, {"id": "93e3641b-d365-456d-b939-11fd92da8155", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [-1060, -740], "parameters": {}, "typeVersion": 1}, {"id": "235e436f-353f-4bb4-a619-35ebb17011d0", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-300, -100], "parameters": {"width": 320.2172923777021, "height": 231, "content": "## Self-coded Tool Node"}, "typeVersion": 1}, {"id": "4265a9d3-7c7e-4511-9a41-fa5a940f8869", "name": "Set2", "type": "n8n-nodes-base.set", "position": [-820, -740], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "6c3d9c41-58b0-4d0d-8892-0b1a96428da3", "name": "chatInput", "type": "string", "value": "Tell me a joke"}]}}, "typeVersion": 3.4}, {"id": "b78b6d50-53be-43a1-889c-773726443bfb", "name": "Custom - LLM Chain Node1", "type": "@n8n/n8n-nodes-langchain.code", "position": [-440, -740], "parameters": {"code": {"execute": {"code": "const { PromptTemplate } = require('@langchain/core/prompts');\n\nconst query = $input.item.json.chatInput;\nconst prompt = PromptTemplate.fromTemplate(query);\nconst llm = await this.getInputConnectionData('ai_languageModel', 0);\nlet chain = prompt.pipe(llm);\nconst output = await chain.invoke();\nreturn [ {json: { output } } ];"}}, "inputs": {"input": [{"type": "main", "required": true, "maxConnections": 1}, {"type": "ai_languageModel", "required": true, "maxConnections": 1}]}, "outputs": {"output": [{"type": "main"}]}}, "typeVersion": 1}, {"id": "cc27654f-92bd-48f5-80d9-1d4f9c83ecb5", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-420, -580], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "e64b5510-efd9-4a8b-aa3c-4312219cb2f0", "name": "Set3", "type": "n8n-nodes-base.set", "position": [-820, -440], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "6c3d9c41-58b0-4d0d-8892-0b1a96428da3", "name": "chatInput", "type": "string", "value": "What year was <PERSON> born?"}]}}, "typeVersion": 3.4}, {"id": "77f8bff3-8868-43ca-8739-7cc16d15dd80", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-440, -340], "parameters": {"options": {}}, "typeVersion": 1.8}, {"id": "d6e943df-ee88-4d0b-bca4-68b9f249dd00", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-460, -120], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "a4b19037-399a-4d0b-abe0-378d8d81c536", "name": "Custom - Wikipedia1", "type": "@n8n/n8n-nodes-langchain.toolCode", "position": [-180, -20], "parameters": {"name": "wikipedia_tool", "jsCode": "console.log('Custom Wikipedia Node runs');\nconst { WikipediaQueryRun } = require(\"@n8n/n8n-nodes-langchain/node_modules/@langchain/community/tools/wikipedia_query_run.cjs\");\n\nconst tool = new WikipediaQueryRun({\n  topKResults: 3,\n  maxDocContentLength: 4000,\n});\n\nreturn await tool.invoke(query);", "description": "Call this tool to research a topic on wikipedia."}, "typeVersion": 1.1}], "pinData": {}, "connections": {"Set2": {"main": [[{"node": "Custom - LLM Chain Node1", "type": "main", "index": 0}]]}, "Set3": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Custom - LLM Chain Node1", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Custom - Wikipedia1": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "Set3", "type": "main", "index": 0}, {"node": "Set2", "type": "main", "index": 0}]]}}}