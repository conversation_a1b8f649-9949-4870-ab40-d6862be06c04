{"nodes": [{"id": "678e86bc-2755-4c79-97d6-fa4da1ed9ff9", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.postgresTrigger", "disabled": true, "position": [500, 480], "parameters": {"schema": {"__rl": true, "mode": "list", "value": "computed", "cachedResultName": "computed"}, "firesOn": "UPDATE", "tableName": {"__rl": true, "mode": "list", "value": "users", "cachedResultName": "users"}, "additionalFields": {}}, "credentials": {"postgres": {"id": "8", "name": "Postgres Product Analytics"}}, "typeVersion": 1}, {"id": "accecdfc-283c-4119-9b23-4cf44bc5e68c", "name": "Filter", "type": "n8n-nodes-base.filter", "notes": "Filter out @n8n.io emails", "position": [980, 540], "parameters": {"conditions": {"string": [{"value1": "={{ $json.email }}", "value2": "n8n.io", "operation": "notContains"}]}}, "notesInFlow": true, "typeVersion": 1}, {"id": "d16d7ae7-0c60-48f0-97fe-c7618cab73d3", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [0, 380], "parameters": {"width": 424, "height": 559, "content": "## 👋 How to use this template\nThis template shows how to sync data from one service to another. In this example we're saving a new qualified lead to a Google Sheets file. Here's how you can test the template:\n\n1. Duplicate our [Google Sheets](https://docs.google.com/spreadsheets/d/1gVfyernVtgYXD-oPboxOSJYQ-HEfAguEryZ7gTtK0V8/edit?usp=sharing) file\n2. Double click the `Google Sheets` node and create a credential by signing in.\n3. Select the correct Google Sheets document and sheet.\n4. Click the `Execute Workflow` button and double click the nodes to see the input and output data\n\n### To customize it to you needs, just do the following:\n1. Enable or exchange the `Postgres trigger` with any service that fits your use case.\n2. Change the `Filter` to fit your needs\n3. Adjust the Google Sheets node as described above\n4. Disable or remove the `On clicking \"Execute Node\"` and `Code` node\n"}, "typeVersion": 1}, {"id": "8bc7439e-d814-4960-8b75-fc77805f74c7", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [460, 380], "parameters": {"width": 344, "height": 562, "content": "### 1. <PERSON><PERSON> step listens for new events\n\n"}, "typeVersion": 1}, {"id": "63b2bc4c-8e33-4432-af4b-4595b2012ce1", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [840, 460], "parameters": {"width": 462, "height": 407, "content": "### 2. Filter and transform your data\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nIn this case, we only want to save qualified users that don't have `@n8n.io` in their email address.\n\nTo edit the filter, simply drag and drop input data into the fields or change the values directly. **Besides filters, n8n has other powerful transformation nodes like [Set](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.set/?utm_source=n8n_app&utm_medium=node_settings_modal-credential_link&utm_campaign=n8n-nodes-base.set), [ItemList](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.itemlists/?utm_source=n8n_app&utm_medium=node_settings_modal-credential_link&utm_campaign=n8n-nodes-base.itemLists), [Code](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.code/?utm_source=n8n_app&utm_medium=node_settings_modal-credential_link&utm_campaign=n8n-nodes-base.code) and many more.**"}, "typeVersion": 1}, {"id": "448e2c49-aa75-405b-ba51-3acbce0fb758", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1340, 460], "parameters": {"width": 342.52886836027733, "height": 407.43618112665195, "content": "### 3. Save the user in a Google Sheet\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nFor simplicity, we're saving our qualified user in a Google Sheet.\n\n**You can replace this node with any service like [Excel](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.microsoftexcel/?utm_source=n8n_app&utm_medium=node_settings_modal-credential_link&utm_campaign=n8n-nodes-base.microsoftExcel), [HubSpot](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.hubspot/?utm_source=n8n_app&utm_medium=node_settings_modal-credential_link&utm_campaign=n8n-nodes-base.hubspot), [Pipedrive](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.pipedrive/?utm_source=n8n_app&utm_medium=node_settings_modal-credential_link&utm_campaign=n8n-nodes-base.pipedrive), [Zendesk](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.zendesk/?utm_source=n8n_app&utm_medium=node_settings_modal-credential_link&utm_campaign=n8n-nodes-base.zendesk) etc.**"}, "typeVersion": 1}, {"id": "c0ee182d-4c31-488b-a547-5f2d2ba8786e", "name": "On clicking \"Execute Node\"", "type": "n8n-nodes-base.manualTrigger", "notes": "For testing the workflow", "position": [500, 680], "parameters": {}, "notesInFlow": true, "typeVersion": 1}, {"id": "87f2a11e-f704-4c9e-ac8b-ee1f057cd347", "name": "Code", "type": "n8n-nodes-base.code", "notes": "<PERSON><PERSON>", "position": [680, 680], "parameters": {"jsCode": "return [\n  {\n    \"id\": 1,\n    \"username\": \"max_mustermann\",\n    \"email\": \"<EMAIL>\",\n    \"company_size\": \"500-999\",\n    \"role\": \"Sales\",\n    \"users\": 50\n  }\n]"}, "notesInFlow": true, "typeVersion": 1}, {"id": "0992077f-b6d3-47d2-94d2-c612dfbf5062", "name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "notes": "Add to \"Users to contact\"", "position": [1400, 540], "parameters": {"columns": {"value": {"id": "={{ $json.id }}", "email": "={{ $json.email }}", "username": "={{ $json.username }}"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "id", "defaultMatch": true, "canBeUsedToMatch": true}, {"id": "username", "type": "string", "display": true, "removed": false, "required": false, "displayName": "username", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "email", "type": "string", "display": true, "removed": false, "required": false, "displayName": "email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "contacted", "type": "string", "display": true, "removed": true, "required": false, "displayName": "contacted", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["id"]}, "options": {"cellFormat": "USER_ENTERED"}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1gVfyernVtgYXD-oPboxOSJYQ-HEfAguEryZ7gTtK0V8/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1gVfyernVtgYXD-oPboxOSJYQ-HEfAguEryZ7gTtK0V8", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1gVfyernVtgYXD-oPboxOSJYQ-HEfAguEryZ7gTtK0V8/edit?usp=drivesdk", "cachedResultName": "Qualified leads to contact"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "9", "name": "Google Sheets account"}}, "notesInFlow": true, "typeVersion": 4}], "connections": {"Code": {"main": [[{"node": "Filter", "type": "main", "index": 0}]]}, "Filter": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}, "Postgres Trigger": {"main": [[{"node": "Filter", "type": "main", "index": 0}]]}, "On clicking \"Execute Node\"": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}}}