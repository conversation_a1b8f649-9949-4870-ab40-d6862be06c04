{"id": "8jDt77Y4FaV6ARYG", "meta": {"instanceId": "31e69f7f4a77bf465b805824e303232f0227212ae922d12133a0f96ffeab4fef"}, "name": "🤖 Telegram Messaging Agent for Text/Audio/Images", "tags": [], "nodes": [{"id": "1656be7a-7a27-47f3-b511-3634a65a97a2", "name": "Check User & Chat ID", "type": "n8n-nodes-base.if", "position": [100, 160], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "5fe3c0d8-bd61-4943-b152-9e6315134520", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Listen for Telegram Events').item.json.body.message.from.first_name }}", "rightValue": "={{ $json.first_name }}"}, {"id": "98a0ea91-0567-459c-bbce-06abc14a49ce", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Listen for Telegram Events').item.json.body.message.from.last_name }}", "rightValue": "={{ $json.last_name }}"}, {"id": "18a96c1f-f2a0-4a2a-b789-606763df4423", "operator": {"type": "number", "operation": "equals"}, "leftValue": "={{ $('Listen for Telegram Events').item.json.body.message.from.id }}", "rightValue": "={{ $json.id }}"}]}, "looseTypeValidation": "="}, "typeVersion": 2.2}, {"id": "73b0fedb-eb82-4464-a08f-397a3fe69480", "name": "Error message", "type": "n8n-nodes-base.telegram", "position": [320, 440], "parameters": {"text": "=Unable to process your message.", "chatId": "={{ $json.body.message.chat.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "a3dc143b-cf3c-4416-bf43-0ca75cbde6c9", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-380, -20], "parameters": {"width": 929, "height": 652, "content": "# Receive Telegram Message with Webhook"}, "typeVersion": 1}, {"id": "c80dae1e-dd20-4632-a00c-9c6290540f22", "name": "Listen for Telegram Events", "type": "n8n-nodes-base.webhook", "position": [-320, 160], "webhookId": "b4ed4c80-a655-4ff2-87d6-febd5280d343", "parameters": {"path": "your-endpoint", "options": {"binaryPropertyName": "data"}, "httpMethod": "POST"}, "typeVersion": 2}, {"id": "6010dacf-1ed6-413c-adf9-146397e16b09", "name": "Set Webhook Test URL", "type": "n8n-nodes-base.httpRequest", "position": [260, -260], "parameters": {"url": "=https://api.telegram.org/{{ $json.token }}/setWebhook", "options": {}, "sendQuery": true, "queryParameters": {"parameters": [{"name": "url", "value": "={{ $json.test_url }}"}]}}, "typeVersion": 4.2}, {"id": "65f8d945-12bb-4ae3-bd83-3b892a36afb9", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-380, -580], "parameters": {"color": 3, "width": 1638, "height": 532, "content": "# Telegram Webhook Tools\n\n## Setting your Telegram Bot WebHook the Easy Way\n"}, "typeVersion": 1}, {"id": "8e3268e9-dc7c-4edd-b5e8-716de5d2ffb3", "name": "Get Telegram Webhook Info", "type": "n8n-nodes-base.httpRequest", "position": [-240, -260], "parameters": {"url": "=https://api.telegram.org/{{ $json.token }}/getWebhookInfo", "options": {}}, "typeVersion": 4.2}, {"id": "e31e176f-2ebd-4cd1-a160-2cc5f254ca6d", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [580, -20], "parameters": {"color": 4, "width": 1113, "height": 429, "content": "# Process Audio"}, "typeVersion": 1}, {"id": "b8b10cd9-7a41-4b21-853c-b2123918ab8d", "name": "Image Schema", "type": "n8n-nodes-base.set", "position": [660, 1060], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "17989eb0-feca-4631-b5c8-34b1d4a6c72b", "name": "image_file_id", "type": "string", "value": "={{ $json.body.message.photo.last().file_id }}"}, {"id": "9317d7ae-dffd-4b1f-9a9c-b3cc4f1e0dd3", "name": "caption", "type": "string", "value": "={{ $json.body.message.caption }}"}]}}, "typeVersion": 3.4}, {"id": "9a7b9e4c-7a81-451a-887a-b7b3f658ae6e", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [580, 900], "parameters": {"color": 6, "width": 1289, "height": 432, "content": "# Process Image"}, "typeVersion": 1}, {"id": "800da6c7-8d03-4932-a081-f35ce01c8dd7", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-1200, -580], "parameters": {"color": 7, "width": 800, "height": 860, "content": "# How to set up a Telegram Bot WebHook\n\n## WebHook Setup Process\n\n**Basic Concept**\nA WebHook allows your Telegram bot to automatically receive updates instead of manually polling the Bot API.\n\n**Setup Method**\nTo set a WebHook, make a GET request using this URL format:\n```\nhttps://api.telegram.org/bot{my_bot_token}/setWebhook?url={url_to_send_updates_to}\n```\nWhere:\n- `my_bot_token`: Your bot token from BotFather\n- `url_to_send_updates_to`: Your HTTPS endpoint that handles bot updates\n\n\n**Verification**\nTo verify the WebHook setup, use:\n```\nhttps://api.telegram.org/bot{my_bot_token}/getWebhookInfo\n```\n\nA successful response looks like:\n```json\n{\n \"ok\": true,\n \"result\": {\n \"url\": \"https://www.example.com/my-telegram-bot/\",\n \"has_custom_certificate\": false,\n \"pending_update_count\": 0,\n \"max_connections\": 40\n }\n}\n```\n\n\nThis method provides a simple and efficient way to handle Telegram bot updates automatically through webhooks rather than manual polling."}, "typeVersion": 1}, {"id": "cd09daf9-ac74-4e86-9d74-875d78f466f0", "name": "gpt-4o-mini", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1080, 260], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "4c69533c-e4e7-4667-baf8-7ca1ed36b150", "name": "Get Audio File", "type": "n8n-nodes-base.telegram", "position": [660, 100], "parameters": {"fileId": "={{ $json.body.message.voice.file_id }}", "resource": "file"}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "0b15b158-88ec-45ba-ae70-fd55a9a72ea3", "name": "Get Image", "type": "n8n-nodes-base.telegram", "position": [860, 1060], "parameters": {"fileId": "={{ $json.image_file_id }}", "resource": "file"}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "081ec871-6cac-4945-9c1b-97bb87489688", "name": "Analyze Image", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1460, 1060], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "resource": "image", "inputType": "base64", "operation": "analyze"}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1.6}, {"id": "072c21fc-d125-4078-b151-9c2fd5a4802c", "name": "Transcribe Recording", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [860, 100], "parameters": {"options": {}, "resource": "audio", "operation": "transcribe", "binaryPropertyName": "=data"}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1.6}, {"id": "b74e2181-8bf2-43a5-b4d4-d24112989b81", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [580, 440], "parameters": {"color": 5, "width": 1113, "height": 429, "content": "# Process Text"}, "typeVersion": 1}, {"id": "8f44b159-07ff-4805-82ad-d8aeed1f9f68", "name": "gpt-4o-mini1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1080, 720], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "666ed1b9-475e-44bf-a884-1ddf58c6c6af", "name": "Test Webhook Status", "type": "n8n-nodes-base.telegram", "position": [460, -260], "parameters": {"text": "={{ $json.description }} for Testing", "chatId": "=**********", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "2a1174a2-2eae-4cf5-ba48-a58a479956bf", "name": "Production Webhook Status", "type": "n8n-nodes-base.telegram", "position": [980, -260], "parameters": {"text": "={{ $json.description }} for Production", "chatId": "=**********", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "210b6df9-e799-409f-b78f-953bffbb37db", "name": "Set Webhook Production URL", "type": "n8n-nodes-base.httpRequest", "position": [780, -260], "parameters": {"url": "=https://api.telegram.org/{{ $json.token }}/setWebhook", "options": {}, "sendQuery": true, "queryParameters": {"parameters": [{"name": "url", "value": "={{ $json.production_url }}"}]}}, "typeVersion": 4.2}, {"id": "5dc6642c-3557-47bb-b012-b353a0d10ca0", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [860, 560], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "b37b48ba-8fef-4e6c-bbca-73e6c2e1e0a8", "name": "text", "type": "string", "value": "={{ $json.body.message.text }}"}]}}, "typeVersion": 3.4}, {"id": "cd715b79-765e-4605-84d6-963d9889c922", "name": "Audio Task Message", "type": "n8n-nodes-base.telegram", "position": [1460, 40], "parameters": {"text": "=Task message: <i>{{ $json.text }}</i>", "chatId": "={{ $('Listen for Telegram Events').item.json.body.message.chat.id }}", "additionalFields": {"parse_mode": "HTML", "appendAttribution": false}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "9845b3e6-8c0f-4194-8442-5648147f905e", "name": "Audio Other Message", "type": "n8n-nodes-base.telegram", "position": [1460, 220], "parameters": {"text": "=Other message: <i>{{ $json.text }}</i>", "chatId": "={{ $('Listen for Telegram Events').item.json.body.message.chat.id }}", "additionalFields": {"parse_mode": "HTML", "appendAttribution": false}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "0184b872-27a1-48dd-8e37-4fdaae7241cd", "name": "Text Task Message", "type": "n8n-nodes-base.telegram", "position": [1460, 500], "parameters": {"text": "=Task message: <i>{{ $json.text }}</i>", "chatId": "={{ $('Listen for Telegram Events').item.json.body.message.chat.id }}", "additionalFields": {"parse_mode": "HTML", "appendAttribution": false}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "7d90fb9b-b2b5-48eb-a6f2-7f953fe6ee52", "name": "Text Other Message", "type": "n8n-nodes-base.telegram", "position": [1460, 680], "parameters": {"text": "=Other message: <i>{{ $json.text }}</i>", "chatId": "={{ $('Listen for Telegram Events').item.json.body.message.chat.id }}", "additionalFields": {"parse_mode": "HTML", "appendAttribution": false}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "c9b9f6d2-c4c4-44b9-a929-9bc0552e8e45", "name": "Image Message", "type": "n8n-nodes-base.telegram", "position": [1660, 1060], "parameters": {"text": "={{ $json.content }}", "chatId": "={{ $('Listen for Telegram Events').item.json.body.message.chat.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "bfc69b30-4bab-459d-bbe1-42e540275582", "name": "Convert to Image File", "type": "n8n-nodes-base.convertToFile", "position": [1260, 1060], "parameters": {"options": {"fileName": "={{ $json.result.file_path }}"}, "operation": "toBinary", "sourceProperty": "data"}, "typeVersion": 1.1}, {"id": "f78d54c3-aa00-4e82-bfb1-f3131182940c", "name": "Extract from File to Base64", "type": "n8n-nodes-base.extractFromFile", "position": [1060, 1060], "parameters": {"options": {}, "operation": "binaryToPropery"}, "typeVersion": 1}, {"id": "735bb735-6b24-4bbd-8d3f-aec6cd383383", "name": "Text Classifier Audio", "type": "@n8n/n8n-nodes-langchain.textClassifier", "position": [1060, 100], "parameters": {"options": {}, "inputText": "={{ $json.text }}", "categories": {"categories": [{"category": "task", "description": "If the message is about about creating a task/todo"}, {"category": "other", "description": "If the message is not about creating a task/todo "}]}}, "typeVersion": 1}, {"id": "be7f49da-f88e-4803-95ef-fb7e2ff2d2ed", "name": "Text Classifier", "type": "@n8n/n8n-nodes-langchain.textClassifier", "position": [1060, 560], "parameters": {"options": {}, "inputText": "={{ $json.text }}", "categories": {"categories": [{"category": "task", "description": "If the message is about about creating a task/todo"}, {"category": "other", "description": "If the message is not about creating a task/todo "}]}}, "typeVersion": 1}, {"id": "33eab7d8-5b90-4533-8799-fb4ae32fc6c5", "name": "Telegram Token & Webhooks", "type": "n8n-nodes-base.set", "position": [380, -540], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "87811892-85f5-4578-a149-3edd94d3815a", "name": "token", "type": "string", "value": "bot[your-telegram-bot-token]"}, {"id": "d2b9ab83-44ad-4741-aac9-1feed974c015", "name": "test_url", "type": "string", "value": "https://[your-url]/webhook-test/[your-endpoint]"}, {"id": "0c671fbf-aa2c-42ef-9e8b-398ac38358d0", "name": "production_url", "type": "string", "value": "https://[your-url]/webhook/[your-endpoint]"}]}}, "typeVersion": 3.4}, {"id": "65d9568e-0504-4c7d-ac05-0b7b4c52a6b2", "name": "Get Webhook Status", "type": "n8n-nodes-base.telegram", "position": [-40, -260], "parameters": {"text": "={{ JSON.stringify($json.result, null, 2) }}", "chatId": "=**********", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "04669db1-3a74-4404-9b5f-9b8554b1059e", "name": "Validation", "type": "n8n-nodes-base.set", "position": [-100, 160], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "0cea6da1-652a-4c1e-94c3-30608ced90f8", "name": "first_name", "type": "string", "value": "First Name"}, {"id": "b90280c6-3e36-49ca-9e7e-e15c42d256cc", "name": "last_name", "type": "string", "value": "Last Name"}, {"id": "f6d86283-16ca-447e-8427-7d3d190babc0", "name": "id", "type": "number", "value": ***********}]}}, "typeVersion": 3.4}, {"id": "7f9935cb-4ca6-40cf-99c5-96c5a1f4ca91", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-160, 100], "parameters": {"color": 7, "width": 420, "height": 260, "content": "## Validate Telegram User\n"}, "typeVersion": 1}, {"id": "fa6c87eb-5f96-4e26-a1bb-60dae902186c", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-320, -320], "parameters": {"color": 7, "width": 460, "height": 240, "content": "## Webhook Status"}, "typeVersion": 1}, {"id": "96536ad2-e607-448e-a368-e4e8c7578b57", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [200, -320], "parameters": {"color": 7, "width": 460, "height": 240, "content": "## Set Webhook for Testing"}, "typeVersion": 1}, {"id": "a58c16d5-0c08-4ee6-a3fe-b9fdbd62eb8b", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [720, -320], "parameters": {"color": 7, "width": 480, "height": 240, "content": "## Set Webhook for Production"}, "typeVersion": 1}, {"id": "158bf4d2-aac9-4a1a-b319-1a4766c<PERSON>ca", "name": "Message Router", "type": "n8n-nodes-base.switch", "position": [320, 160], "parameters": {"rules": {"values": [{"outputKey": "audio", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.body.message.voice }}", "rightValue": ""}]}, "renameOutput": true}, {"outputKey": "text", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "342f0883-d959-44a2-b80d-379e39c76218", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.body.message.text }}", "rightValue": ""}]}, "renameOutput": true}, {"outputKey": "image", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "ded3a600-f861-413a-8892-3fc5ea935ecb", "operator": {"type": "array", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.body.message.photo }}", "rightValue": ""}]}, "renameOutput": true}]}, "options": {"fallbackOutput": "extra"}}, "typeVersion": 3.2}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "91b5de12-0ada-4125-b5ce-3ffb4dc9fa9b", "connections": {"Get Image": {"main": [[{"node": "Extract from File to Base64", "type": "main", "index": 0}]]}, "Validation": {"main": [[{"node": "Check User & Chat ID", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Text Classifier", "type": "main", "index": 0}]]}, "gpt-4o-mini": {"ai_languageModel": [[{"node": "Text Classifier Audio", "type": "ai_languageModel", "index": 0}]]}, "Image Schema": {"main": [[{"node": "Get Image", "type": "main", "index": 0}]]}, "gpt-4o-mini1": {"ai_languageModel": [[{"node": "Text Classifier", "type": "ai_languageModel", "index": 0}]]}, "Analyze Image": {"main": [[{"node": "Image Message", "type": "main", "index": 0}]]}, "Image Message": {"main": [[]]}, "Get Audio File": {"main": [[{"node": "Transcribe Recording", "type": "main", "index": 0}]]}, "Message Router": {"main": [[{"node": "Get Audio File", "type": "main", "index": 0}], [{"node": "<PERSON>", "type": "main", "index": 0}], [{"node": "Image Schema", "type": "main", "index": 0}], [{"node": "Error message", "type": "main", "index": 0}]]}, "Text Classifier": {"main": [[{"node": "Text Task Message", "type": "main", "index": 0}], [{"node": "Text Other Message", "type": "main", "index": 0}]]}, "Check User & Chat ID": {"main": [[{"node": "Message Router", "type": "main", "index": 0}], [{"node": "Error message", "type": "main", "index": 0}]]}, "Set Webhook Test URL": {"main": [[{"node": "Test Webhook Status", "type": "main", "index": 0}]]}, "Transcribe Recording": {"main": [[{"node": "Text Classifier Audio", "type": "main", "index": 0}]]}, "Convert to Image File": {"main": [[{"node": "Analyze Image", "type": "main", "index": 0}]]}, "Text Classifier Audio": {"main": [[{"node": "Audio Task Message", "type": "main", "index": 0}], [{"node": "Audio Other Message", "type": "main", "index": 0}]]}, "Get Telegram Webhook Info": {"main": [[{"node": "Get Webhook Status", "type": "main", "index": 0}]]}, "Telegram Token & Webhooks": {"main": [[{"node": "Set Webhook Production URL", "type": "main", "index": 0}, {"node": "Set Webhook Test URL", "type": "main", "index": 0}, {"node": "Get Telegram Webhook Info", "type": "main", "index": 0}]]}, "Listen for Telegram Events": {"main": [[{"node": "Validation", "type": "main", "index": 0}]]}, "Set Webhook Production URL": {"main": [[{"node": "Production Webhook Status", "type": "main", "index": 0}]]}, "Extract from File to Base64": {"main": [[{"node": "Convert to Image File", "type": "main", "index": 0}]]}}}