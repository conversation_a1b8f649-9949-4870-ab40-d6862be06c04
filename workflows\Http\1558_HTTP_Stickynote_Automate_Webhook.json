{"id": "Q63cSgFlcqz291ec", "meta": {"instanceId": "31e69f7f4a77bf465b805824e303232f0227212ae922d12133a0f96ffeab4fef", "templateCredsSetupCompleted": true}, "name": "✨📊Multi-AI Agent Chatbot for Postgres/Supabase DB and QuickCharts + Tool Router", "tags": [], "nodes": [{"id": "3a332532-a56e-42f5-a114-4a7e138b5e0f", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-180, -1420], "webhookId": "faddb40a-7048-4398-a0f9-d239a19c32ce", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "6c707ee7-95e9-4ebd-9373-a2dac0ea73a7", "name": "Execute SQL Query", "type": "n8n-nodes-base.postgresTool", "position": [460, -500], "parameters": {"query": "{{ $fromAI(\"sql_query\", \"SQL Query\") }}", "options": {}, "operation": "execute<PERSON>uery", "descriptionType": "manual", "toolDescription": "Use this tool to query the database with SQL queries"}, "credentials": {"postgres": {"id": "wZnget4L3P3bnlfh", "name": "Postgres account"}}, "typeVersion": 2.5}, {"id": "1d5572e1-de5a-4e67-8ba1-82196bd62e9b", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-480, -360], "parameters": {"workflowInputs": {"values": [{"name": "user_prompt"}, {"name": "route"}, {"name": "db_records"}]}}, "typeVersion": 1.1}, {"id": "e3caa1b3-7bdb-43c1-a749-74ae02912d84", "name": "query_db_tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [720, -1100], "parameters": {"name": "query_database_tool", "workflowId": {"__rl": true, "mode": "id", "value": "={{  $workflow.id }}"}, "description": "Use this tool to query the database", "workflowInputs": {"value": {"route": "query_database_tool", "user_prompt": "={{ $('When chat message received').item.json.chatInput }}"}, "schema": [{"id": "user_prompt", "type": "string", "display": true, "removed": false, "required": false, "displayName": "user_prompt", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "route", "type": "string", "display": true, "removed": false, "required": false, "displayName": "route", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["user_prompt"], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2}, {"id": "594e6fd3-084a-4100-ac16-1cc4068e03c1", "name": "generate_quickchart_tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [980, -1100], "parameters": {"name": "generate_chart_tool", "workflowId": {"__rl": true, "mode": "id", "value": "={{  $workflow.id }}"}, "description": "Use this tool to generate a chart with QuickChart", "workflowInputs": {"value": {"route": "generate_chart_tool", "db_records": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('db_records', `The database records`, 'string') }}", "user_prompt": "={{ $('When chat message received').item.json.chatInput }}"}, "schema": [{"id": "user_prompt", "type": "string", "display": true, "removed": false, "required": false, "displayName": "user_prompt", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "route", "type": "string", "display": true, "removed": false, "required": false, "displayName": "route", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "db_records", "type": "string", "display": true, "removed": false, "required": false, "displayName": "db_records", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["user_prompt"], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2}, {"id": "580426ac-0fb7-4f14-af7b-e497b7cb08f8", "name": "Create <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.httpRequest", "position": [780, -140], "parameters": {"url": "={{ encodeURI($json.url) }}", "options": {}}, "typeVersion": 4.2}, {"id": "b7085357-ebe9-4564-868d-b31fc2e9734a", "name": "Quick<PERSON>hart Object Sc<PERSON>a", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [460, 140], "parameters": {"jsonSchemaExample": "{\n  \"type\": \"bar\",\n  \"data\": {\n    \"labels\": [\"R270684\", \"R274295\", \"R276352\", \"R277914\", \"R280108\"],\n    \"datasets\": [\n      {\n        \"label\": \"List Price\",\n        \"data\": [2149000, 924900, 924900, 1288000, 1198000],\n        \"backgroundColor\": \"#FF6384\"\n      },\n      {\n        \"label\": \"Days On Market\",\n        \"data\": [101, 91, 123, 136, 185],\n        \"backgroundColor\": \"#36A2EB\"\n      }\n    ]\n  },\n  \"options\": {\n    \"scales\": {\n      \"y\": {\n        \"min\": 0,\n        \"max\": 2200000\n      }\n    }\n  }\n}"}, "typeVersion": 1.2}, {"id": "b1037112-f5af-4e61-8bd1-0d9b0a9ad2e1", "name": "gpt-4o-mini", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [200, -1100], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {"responseFormat": "text"}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "5ea5ba56-2368-4e95-a98f-2c7548a66a9b", "name": "gpt-4o-mini-2", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [200, 140], "parameters": {"model": {"__rl": true, "mode": "id", "value": "=gpt-4o-mini"}, "options": {"responseFormat": "text"}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "4b8f9978-9d46-48f5-93ca-002ade008887", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [80, -1500], "parameters": {"color": 5, "width": 1100, "height": 600, "content": "## 🤖Primary AI Manager Agent"}, "typeVersion": 1}, {"id": "f6534374-cc07-429a-b27c-b38835c96465", "name": "🤖Primary Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [480, -1420], "parameters": {"options": {"systemMessage": "You are a helpful assistant that answers the users questions by using the tools provided.\n\n## TOOLS\n- query_database_tool: Use this tool to query the database\n- generate_chart_tool: Use this tool to generate a chart with Quick<PERSON>hart\n\nAlways provide the results of the database query and the link for the chart when applicable."}}, "typeVersion": 1.7}, {"id": "cf246e46-b3e4-4c9f-96f9-be1b91c0a3eb", "name": "🤖Secondary Postgres Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [480, -780], "parameters": {"text": "={{ $json.user_prompt }}", "options": {"systemMessage": "You are a helpful assistant with tools for querying a SQL database.  Use the tools provided to query the database."}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "02d741d3-4b6e-4e19-8aca-3e15175e9667", "name": "🤖Secondary QuickChart Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [240, -140], "parameters": {"text": "=Your task is to generate a Chart.js configuration object with the following specifications:\n- Chart type: bar unless otherwise indicated\n- Labels: Use the ML # from each record unless otherwise indicated\n- Show bar for list price if not otherwise indicated\n- Return only the raw JSON object without code fences or explanations\n\nThis is the user prompt: {{ $json.user_prompt }}\nThis is the result of the SQL query: {{ $json.db_records }}", "options": {}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.7}, {"id": "4f41690b-313a-4ce3-ba65-a2ce2c3ee9b9", "name": "🔀Tool Agent Router", "type": "n8n-nodes-base.switch", "position": [-180, -360], "parameters": {"rules": {"values": [{"outputKey": "🔍query", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "35b1e13e-6157-48d0-85af-3cd33260eae1", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.route }}", "rightValue": "=query_database_tool"}]}, "renameOutput": true}, {"outputKey": "📊chart", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "ff5f97fb-0f18-4bf9-b16c-3d0b3bc3c7f4", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.route }}", "rightValue": "=generate_chart_tool"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "27c30d2c-3af3-4d05-aadb-9f18751fb9ce", "name": "Table Definitions", "type": "n8n-nodes-base.postgresTool", "position": [980, -500], "parameters": {"query": "select\n  c.column_name,\n  c.data_type,\n  c.is_nullable,\n  c.column_default,\n  tc.constraint_type,\n  ccu.table_name AS referenced_table,\n  ccu.column_name AS referenced_column\nfrom\n  information_schema.columns c\nLEFT join\n  information_schema.key_column_usage kcu\n  ON c.table_name = kcu.table_name\n  AND c.column_name = kcu.column_name\nLEFT join\n  information_schema.table_constraints tc\n  ON kcu.constraint_name = tc.constraint_name\n  AND tc.constraint_type = 'FOREIGN KEY'\nLEFT join\n  information_schema.constraint_column_usage ccu\n  ON tc.constraint_name = ccu.constraint_name\nwhere\n  c.table_name = '{{ $fromAI(\"table_name\") }}'\n  AND c.table_schema = '{{ $fromAI(\"schema_name\") }}'\norder by\n  c.ordinal_position", "options": {}, "operation": "execute<PERSON>uery", "descriptionType": "manual", "toolDescription": "Use this tool to get table definition to find all columns and types"}, "credentials": {"postgres": {"id": "wZnget4L3P3bnlfh", "name": "Postgres account"}}, "typeVersion": 2.5}, {"id": "59dfd315-fb02-425c-a6ca-2d63167c2e24", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "position": [460, -1100], "parameters": {"tableName": "={{ $workflow.id }}_chat_history"}, "credentials": {"postgres": {"id": "wZnget4L3P3bnlfh", "name": "Postgres account"}}, "typeVersion": 1.3}, {"id": "81dd8b73-0809-4f30-a867-bf98ff6a80bc", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [900, -1180], "parameters": {"color": 7, "height": 240, "content": "## <PERSON><PERSON><PERSON>"}, "typeVersion": 1}, {"id": "1a08bc08-510b-4cda-b96e-179bd3abe164", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [640, -1180], "parameters": {"color": 7, "height": 240, "content": "## Postgres Tool"}, "typeVersion": 1}, {"id": "9cc5e958-0f93-42ad-813e-83c8b5bb126e", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [120, -1180], "parameters": {"color": 7, "height": 240, "content": "## LLM"}, "typeVersion": 1}, {"id": "920f9fe4-e182-4159-9152-e81586ec5304", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [380, -1180], "parameters": {"color": 7, "height": 240, "content": "## Chat Memory"}, "typeVersion": 1}, {"id": "d2a24dce-1add-49d6-8d2f-6547fca35bfb", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-300, -1500], "parameters": {"color": 4, "width": 340, "height": 280, "content": "## 👍Start Here"}, "typeVersion": 1}, {"id": "d0df3ea6-9765-4f4d-a8f7-b2356ea1cf26", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [80, -860], "parameters": {"color": 6, "width": 1100, "height": 560, "content": "## ⚒️🤖Secondary Postgres Tool Agent "}, "typeVersion": 1}, {"id": "fe990853-fce6-4cbe-9cc2-fc04f53742e8", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [120, -580], "parameters": {"color": 7, "height": 240, "content": ""}, "typeVersion": 1}, {"id": "c5086736-4dfe-44a3-a5dc-6cace0593334", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [640, -580], "parameters": {"color": 7, "height": 240, "content": ""}, "typeVersion": 1}, {"id": "6b848863-10b2-42e4-8bd5-ed4663b9e5cb", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [900, -580], "parameters": {"color": 7, "height": 240, "content": ""}, "typeVersion": 1}, {"id": "75231fcf-8314-4ee6-96a7-99d842b6ee4f", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [380, -580], "parameters": {"color": 7, "height": 240, "content": ""}, "typeVersion": 1}, {"id": "c54439c7-ccb1-41a4-ad76-8ed39f7fc5e6", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [-300, -460], "parameters": {"color": 3, "width": 340, "height": 320, "content": "## Tool Agent Router 🔀"}, "typeVersion": 1}, {"id": "b51ffe20-2ae4-478b-9573-fc5456935483", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [80, -260], "parameters": {"color": 6, "width": 1100, "height": 600, "content": "## ⚒️🤖Secondary <PERSON><PERSON><PERSON> Tool Agent"}, "typeVersion": 1}, {"id": "ffb9da23-f79c-4d20-81a3-656471bdda33", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [380, 60], "parameters": {"color": 7, "height": 240, "content": ""}, "typeVersion": 1}, {"id": "61532d90-1f65-48d0-a408-2e6edd9511b1", "name": "Sticky Note15", "type": "n8n-nodes-base.stickyNote", "position": [120, 60], "parameters": {"color": 7, "height": 240, "content": ""}, "typeVersion": 1}, {"id": "5b125fa6-8017-437e-bb20-b9deb5d52c63", "name": "Final QuickChart URL", "type": "n8n-nodes-base.set", "position": [980, -140], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "63bab42a-9b9b-4756-88d2-f41cff9a1ded", "name": "quickchart_url", "type": "string", "value": "={{ encodeURI($json.url) }}"}]}}, "typeVersion": 3.4}, {"id": "2c66055d-1ca2-40f3-a082-57232402fdd1", "name": "QuickChart GET URL", "type": "n8n-nodes-base.set", "position": [580, -140], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d69995ae-413e-49e7-b6ec-17e9e034e4b6", "name": "url", "type": "string", "value": "={{ \"https://quickchart.io/chart?width=250&height=150&chart=\" + $json.output.toJsonString() }}"}]}}, "typeVersion": 3.4}, {"id": "0a0f024d-24fa-43e9-bfa6-b841902b5e5e", "name": "Sticky Note14", "type": "n8n-nodes-base.stickyNote", "position": [-540, -1600], "parameters": {"color": 7, "width": 1760, "height": 1980, "content": "# ✨📊Multi-AI Agent Chatbot for Postgres/Supabase DB and QuickCharts + Tool Router"}, "typeVersion": 1}, {"id": "a50b4d31-7e9e-4cd0-84b9-f4755deb6efd", "name": "Sticky Note16", "type": "n8n-nodes-base.stickyNote", "position": [-500, -1180], "parameters": {"width": 540, "height": 240, "content": "## Setup\n\n1. Create a Postgres compatible database (Supabase)\n\n2. Add your Postgres and OpenAI credentials\n\n3. Click Chat button and start chatting with your database and creating QuickChart to visualize the results\n"}, "typeVersion": 1}, {"id": "3df0304a-323b-4bd4-96ce-2907367ede8d", "name": "Sticky Note17", "type": "n8n-nodes-base.stickyNote", "position": [-500, -860], "parameters": {"width": 542, "height": 296, "content": "## Postgres Tools Used\n\n1. **Execute SQL Query** \nUsed to execute any query generated by the agent.\n\n2. **DB Schema and Tables** \nReturns the list of all the tables with its schema name.\n\n3. **Table Definition** \nReturns table details like column names, foreign keys and more of a particular table in a schema."}, "typeVersion": 1}, {"id": "feaeaf6d-f5b8-4dbe-a39b-d07882142ead", "name": "Sticky Note19", "type": "n8n-nodes-base.stickyNote", "position": [-500, -40], "parameters": {"width": 542, "height": 376, "content": "## Generate a Quickchart\n\n**Secondary QuickChart Agent Tool**\nThis section handles the chart generation process through several steps by sending the database records and user prompt to OpenAI to create a JSON object based on Chart.js and QuickChart.io definitions\n\n**QuickChart GET URL node**\nThis sections adds chart definitions to a QuickChart.io URL\n\n**Create QuickChart node**\nThis sections sends chart queries to QuickCharts with a defined JSON format\n\n\n\nThis integration allows you to dynamically generate charts based on data queries, with AI assistance for formatting and optimization.\n\n\n"}, "typeVersion": 1}, {"id": "6c9be4d2-46e9-44ae-8751-f4f4964323e9", "name": "DB Schema and Tables", "type": "n8n-nodes-base.postgresTool", "position": [720, -500], "parameters": {"query": "SELECT \n    table_schema,\n    table_name\nFROM information_schema.tables\nWHERE table_type = 'BASE TABLE'\n    AND table_schema NOT IN ('pg_catalog', 'information_schema')\nORDER BY table_schema, table_name;", "options": {}, "operation": "execute<PERSON>uery", "descriptionType": "manual", "toolDescription": "Use this tool to get a list of all tables with their schema in the database"}, "credentials": {"postgres": {"id": "wZnget4L3P3bnlfh", "name": "Postgres account"}}, "typeVersion": 2.5}, {"id": "4e779b6f-963c-4efb-af43-bec0e5c3228c", "name": "gpt-40-mini-1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [200, -500], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {"responseFormat": "text"}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "a6cc9a4e-b016-47f6-9f9f-9c77a15c2be2", "name": "Sticky Note18", "type": "n8n-nodes-base.stickyNote", "position": [640, 180], "parameters": {"width": 440, "height": 120, "content": "## QuickChart Schema\nAdjust the QuickChart Schema to match your use case.\n\nhttps://quickchart.io/documentation/"}, "typeVersion": 1}, {"id": "9855ba6a-d46b-4461-a397-02108023abc5", "name": "Sticky Note20", "type": "n8n-nodes-base.stickyNote", "position": [640, 60], "parameters": {"width": 440, "height": 100, "content": "## Chart Size\nAdjust the chart size in the QuickChart GET URL node.\n"}, "typeVersion": 1}], "active": false, "pinData": {"When Executed by Another Workflow": [{"json": {"route": "generate_chart_tool", "db_records": "[{\"mls_num\":\"R292309\",\"list_price\":1148000},{\"mls_num\":\"R292302\",\"list_price\":1298000},{\"mls_num\":\"R294786\",\"list_price\":1280000},{\"mls_num\":\"V17840\",\"list_price\":939000},{\"mls_num\":\"V10178\",\"list_price\":420000},{\"mls_num\":\"V18007\",\"list_price\":296500},{\"mls_num\":\"V18136\",\"list_price\":379000},{\"mls_num\":\"V18643\",\"list_price\":329000},{\"mls_num\":\"V17755\",\"list_price\":236000},{\"mls_num\":\"V19126\",\"list_price\":245500}]", "user_prompt": "provide a bar chart showing mls# and list price"}}]}, "settings": {"executionOrder": "v1"}, "versionId": "9781f576-38ef-4e18-9fbe-8383565cb032", "connections": {"gpt-4o-mini": {"ai_languageModel": [[{"node": "🤖Primary Agent", "type": "ai_languageModel", "index": 0}]]}, "gpt-40-mini-1": {"ai_languageModel": [[{"node": "🤖Secondary Postgres Agent", "type": "ai_languageModel", "index": 0}]]}, "gpt-4o-mini-2": {"ai_languageModel": [[{"node": "🤖Secondary QuickChart Agent", "type": "ai_languageModel", "index": 0}]]}, "query_db_tool": {"ai_tool": [[{"node": "🤖Primary Agent", "type": "ai_tool", "index": 0}]]}, "Create QuickChart": {"main": [[{"node": "Final QuickChart URL", "type": "main", "index": 0}]]}, "Execute SQL Query": {"ai_tool": [[{"node": "🤖Secondary Postgres Agent", "type": "ai_tool", "index": 0}]]}, "Table Definitions": {"ai_tool": [[{"node": "🤖Secondary Postgres Agent", "type": "ai_tool", "index": 0}]]}, "🤖Primary Agent": {"main": [[]]}, "QuickChart GET URL": {"main": [[{"node": "Create <PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "DB Schema and Tables": {"ai_tool": [[{"node": "🤖Secondary Postgres Agent", "type": "ai_tool", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "🤖Primary Agent", "type": "ai_memory", "index": 0}]]}, "🔀Tool Agent Router": {"main": [[{"node": "🤖Secondary Postgres Agent", "type": "main", "index": 0}], [{"node": "🤖Secondary QuickChart Agent", "type": "main", "index": 0}]]}, "QuickChart Object Schema": {"ai_outputParser": [[{"node": "🤖Secondary QuickChart Agent", "type": "ai_outputParser", "index": 0}]]}, "generate_quickchart_tool": {"ai_tool": [[{"node": "🤖Primary Agent", "type": "ai_tool", "index": 0}]]}, "When chat message received": {"main": [[{"node": "🤖Primary Agent", "type": "main", "index": 0}]]}, "🤖Secondary Postgres Agent": {"main": [[]]}, "🤖Secondary QuickChart Agent": {"main": [[{"node": "QuickChart GET URL", "type": "main", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "🔀Tool Agent Router", "type": "main", "index": 0}]]}}}