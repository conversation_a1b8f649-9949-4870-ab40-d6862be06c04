{"id": "82", "name": "Create a new list, add a new contact to the list, update the contact, and get all contacts in the list", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [290, 260], "parameters": {}, "typeVersion": 1}, {"name": "Automizy", "type": "n8n-nodes-base.automizy", "position": [490, 260], "parameters": {"name": "n8n-docs", "resource": "list"}, "credentials": {"automizyApi": "automizy"}, "typeVersion": 1}, {"name": "Automizy1", "type": "n8n-nodes-base.automizy", "position": [690, 260], "parameters": {"email": "<EMAIL>", "listId": "={{$node[\"Automizy\"].json[\"id\"]}}", "additionalFields": {"status": "ACTIVE"}}, "credentials": {"automizyApi": "automizy"}, "typeVersion": 1}, {"name": "Automizy2", "type": "n8n-nodes-base.automizy", "position": [890, 260], "parameters": {"email": "={{$node[\"Automizy1\"].json[\"email\"]}}", "operation": "update", "updateFields": {"tags": ["reviewer"]}}, "credentials": {"automizyApi": "automizy"}, "typeVersion": 1}, {"name": "Automizy3", "type": "n8n-nodes-base.automizy", "position": [1090, 260], "parameters": {"listId": "={{$node[\"Automizy\"].json[\"id\"]}}", "operation": "getAll", "returnAll": true, "additionalFields": {}}, "credentials": {"automizyApi": "automizy"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Automizy": {"main": [[{"node": "Automizy1", "type": "main", "index": 0}]]}, "Automizy1": {"main": [[{"node": "Automizy2", "type": "main", "index": 0}]]}, "Automizy2": {"main": [[{"node": "Automizy3", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Automizy", "type": "main", "index": 0}]]}}}