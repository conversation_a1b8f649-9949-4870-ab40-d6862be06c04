{"id": "39", "meta": {"instanceId": "a2434c94d549548a685cca39cc4614698e94f527bcea84eefa363f1037ae14cd"}, "name": "PostgreSQL export to CSV", "tags": [], "nodes": [{"id": "ed94b34e-9ae6-4925-b292-b64a7e0bd602", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [660, 420], "parameters": {}, "typeVersion": 1}, {"id": "f5ada70d-c186-4d28-a64b-3847e2625c8d", "name": "Spreadsheet File", "type": "n8n-nodes-base.spreadsheetFile", "position": [1260, 420], "parameters": {"options": {}, "operation": "toFile", "fileFormat": "csv"}, "typeVersion": 1}, {"id": "4e06ae2b-ef42-4ef4-b7b2-56eb70738a03", "name": "TableName", "type": "n8n-nodes-base.set", "position": [840, 420], "parameters": {"values": {"string": [{"name": "TableName", "value": "booksRead"}]}, "options": {}}, "typeVersion": 1}, {"id": "457ed549-507d-422a-bd14-1736252bd2e9", "name": "Postgres", "type": "n8n-nodes-base.postgres", "position": [1060, 420], "parameters": {"query": "=SELECT * FROM {{ $json[\"TableName\"] }}", "operation": "execute<PERSON>uery", "additionalFields": {}}, "credentials": {"postgres": {"id": "33", "name": "Postgres account"}}, "typeVersion": 1}], "active": false, "pinData": {"Postgres": [{"json": {"book_id": 1, "read_date": "2022-09-08", "book_title": "Demons", "book_author": "<PERSON><PERSON><PERSON>"}}, {"json": {"book_id": 2, "read_date": "2022-05-06", "book_title": "<PERSON>", "book_author": "<PERSON>"}}, {"json": {"book_id": 3, "read_date": "2023-01-04", "book_title": "Catch-22", "book_author": "<PERSON>"}}, {"json": {"book_id": 4, "read_date": "2023-01-21", "book_title": "The Bell Jar", "book_author": "<PERSON>"}}, {"json": {"book_id": 5, "read_date": "2023-02-14", "book_title": "Frankenstein", "book_author": "<PERSON>"}}], "Spreadsheet File": [{"json": {"book_id": 1, "read_date": "2022-09-08", "book_title": "Demons", "book_author": "<PERSON><PERSON><PERSON>"}}, {"json": {"book_id": 2, "read_date": "2022-05-06", "book_title": "<PERSON>", "book_author": "<PERSON>"}}, {"json": {"book_id": 3, "read_date": "2023-01-04", "book_title": "Catch-22", "book_author": "<PERSON>"}}, {"json": {"book_id": 4, "read_date": "2023-01-21", "book_title": "The Bell Jar", "book_author": "<PERSON>"}}, {"json": {"book_id": 5, "read_date": "2023-02-14", "book_title": "Frankenstein", "book_author": "<PERSON>"}}]}, "settings": {}, "versionId": "586e2a98-69a0-4a40-8c92-89380a7cca73", "connections": {"Postgres": {"main": [[{"node": "Spreadsheet File", "type": "main", "index": 0}]]}, "TableName": {"main": [[{"node": "Postgres", "type": "main", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "TableName", "type": "main", "index": 0}]]}}}