{"meta": {"instanceId": "03e9d14e9196363fe7191ce21dc0bb17387a6e755dcc9acc4f5904752919dca8"}, "nodes": [{"id": "1bad6bfc-9ec9-48a5-b8f7-73c4de3d08cf", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.gmailTrigger", "position": [1480, 160], "parameters": {"simple": false, "filters": {}, "options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}}, "credentials": {"gmailOAuth2": {"id": "kkhNhqKpZt6IUZd0", "name": " Gmail"}}, "typeVersion": 1.2}, {"id": "9ac747a1-4fd8-46ba-b4c1-75fd17aab2ed", "name": "Microsoft Outlook Trigger", "type": "n8n-nodes-base.microsoftOutlookTrigger", "disabled": true, "position": [1480, 720], "parameters": {"fields": ["body", "toRecipients", "subject", "bodyPreview"], "output": "fields", "filters": {}, "options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}}, "credentials": {"microsoftOutlookOAuth2Api": {"id": "vTCK0oVQ0WjFrI5H", "name": " Outlook Credential"}}, "typeVersion": 1}, {"id": "5bf9b0e8-b84e-44a2-aad2-45dde3e4ab1b", "name": "Screenshot HTML", "type": "n8n-nodes-base.httpRequest", "position": [2520, 480], "parameters": {"url": "https://hcti.io/v1/image", "method": "POST", "options": {}, "sendBody": true, "sendQuery": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "html", "value": "={{ $json.htmlBody }}"}]}, "genericAuthType": "httpBasicAuth", "queryParameters": {"parameters": [{}]}}, "credentials": {"httpBasicAuth": {"id": "8tm8mUWmPvtmPFPk", "name": "hcti.io"}}, "typeVersion": 4.2}, {"id": "fc770d1d-6c18-4d14-8344-1dc042464df6", "name": "Retrieve Screenshot", "type": "n8n-nodes-base.httpRequest", "position": [2700, 480], "parameters": {"url": "={{ $json.url }}", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth"}, "credentials": {"httpBasicAuth": {"id": "8tm8mUWmPvtmPFPk", "name": "hcti.io"}}, "typeVersion": 4.2}, {"id": "2f3e5cc0-24e8-450a-898b-71e2d6f7bb58", "name": "Set Outlook Variables", "type": "n8n-nodes-base.set", "position": [2020, 720], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "38bd3db2-1a8d-4c40-a2dd-336e0cc84224", "name": "htmlBody", "type": "string", "value": "={{ $('Microsoft Outlook Trigger').item.json.body.content }}"}, {"id": "13bdd95b-ef02-486e-b38b-d14bd05a4a8a", "name": "headers", "type": "string", "value": "={{ $json}}"}, {"id": "20566ad4-7eb7-42b1-8a0d-f8b759610f10", "name": "subject", "type": "string", "value": "={{ $('Microsoft Outlook Trigger').item.json.subject }}"}, {"id": "7171998f-a5a2-4e23-946a-9c1ad75710e7", "name": "recipient", "type": "string", "value": "={{ $('Microsoft Outlook Trigger').item.json.toRecipients[0].emailAddress.address }}"}, {"id": "cc262634-2470-4524-8319-abe2518a6335", "name": "textBody", "type": "string", "value": "={{ $('Retrieve Headers of Email').item.json.body.content }}"}]}}, "typeVersion": 3.4}, {"id": "374e5b16-a666-4706-9fd2-762b2927012d", "name": "Set Gmail Variables", "type": "n8n-nodes-base.set", "position": [2040, 160], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "38bd3db2-1a8d-4c40-a2dd-336e0cc84224", "name": "htmlBody", "type": "string", "value": "={{ $json.html }}"}, {"id": "18fbcf78-6d3c-4036-b3a2-fb5adf22176a", "name": "headers", "type": "string", "value": "={{ $json.headers }}"}, {"id": "1d690098-be2a-4604-baf8-62f314930929", "name": "subject", "type": "string", "value": "={{ $json.subject }}"}, {"id": "8009f00a-547f-4eb1-b52d-2e7305248885", "name": "recipient", "type": "string", "value": "={{ $json.to.text }}"}, {"id": "1932e97d-b03b-4964-b8bc-8262aaaa1f7a", "name": "textBody", "type": "string", "value": "={{ $json.text }}"}]}}, "typeVersion": 3.4}, {"id": "3166738e-d0a3-475b-8b19-51afd519ee3a", "name": "Retrieve Headers of Email", "type": "n8n-nodes-base.httpRequest", "position": [1680, 720], "parameters": {"url": "=https://graph.microsoft.com/v1.0/me/messages/{{ $json.id }}?$select=internetMessageHeaders,body", "options": {}, "sendHeaders": true, "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "Prefer", "value": "outlook.body-content-type=\"text\""}]}, "nodeCredentialType": "microsoftOutlookOAuth2Api"}, "credentials": {"microsoftOutlookOAuth2Api": {"id": "vTCK0oVQ0WjFrI5H", "name": " Outlook Credential"}}, "typeVersion": 4.2}, {"id": "25ae222c-088f-4565-98d6-803c8c1b0826", "name": "Format Headers", "type": "n8n-nodes-base.code", "position": [1860, 720], "parameters": {"jsCode": "const input = $('Retrieve Headers of Email').item.json.internetMessageHeaders;\n\nconst result = input.reduce((acc, { name, value }) => {\n  if (!acc[name]) acc[name] = [];\n  acc[name].push(value);\n  return acc;\n}, {});\n\nreturn result;"}, "typeVersion": 2}, {"id": "8f14f267-1074-43ea-968d-26a6ab36fd7b", "name": "Set Email Variables", "type": "n8n-nodes-base.set", "position": [2360, 480], "parameters": {"options": {}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "45d156aa-91f4-483c-91d4-c9de4a4f595d", "name": "ChatGPT Analysis", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [3100, 480], "parameters": {"text": "=Describe this image. Determine if the email could be a phishing email. The message headers are as follows:\n{{ $('Set Email Variables').item.json.headers }}\n\nFormat the response for <PERSON><PERSON> who uses a wiki-style renderer. Do not include ``` around your response.", "modelId": {"__rl": true, "mode": "list", "value": "chatgpt-4o-latest", "cachedResultName": "CHATGPT-4O-LATEST"}, "options": {"maxTokens": 1500}, "resource": "image", "inputType": "base64", "operation": "analyze"}, "credentials": {"openAiApi": {"id": "76", "name": "OpenAi account"}}, "typeVersion": 1.6}, {"id": "62ca591b-6627-496c-96a7-95cb0081480d", "name": "Create <PERSON><PERSON>", "type": "n8n-nodes-base.jira", "position": [3500, 480], "parameters": {"project": {"__rl": true, "mode": "list", "value": "10001", "cachedResultName": "Support"}, "summary": "=Phishing Email Reported: \"{{ $('Set Email Variables').item.json.subject }}\"", "issueType": {"__rl": true, "mode": "list", "value": "10008", "cachedResultName": "Task"}, "additionalFields": {"description": "=A phishing email was reported by {{ $('Set Email Variables').item.json.recipient }} with the subject line \"{{ $('Set Email Variables').item.json.subject }}\" and body:\n{{ $('Set Email Variables').item.json.textBody }}\n\\\\\n\\\\\n\\\\\nh2. Here is ChatGPT's analysis of the email:\n{{ $json.content }}"}}, "credentials": {"jiraSoftwareCloudApi": {"id": "BZmmGUrNIsgM9fDj", "name": "New Jira Cloud"}}, "typeVersion": 1}, {"id": "071380c8-8070-4f8f-86c6-87c4ee3bc261", "name": "Rename Screenshot", "type": "n8n-nodes-base.code", "position": [3680, 480], "parameters": {"mode": "runOnceForEachItem", "jsCode": "$('Retrieve Screenshot').item.binary.data.fileName = 'emailScreenshot.png'\n\nreturn $('Retrieve Screenshot').item;"}, "typeVersion": 2}, {"id": "05c57490-c1ee-48f0-9e38-244c9a995e22", "name": "Upload Screenshot of Email to Jira", "type": "n8n-nodes-base.jira", "position": [3860, 480], "parameters": {"issueKey": "={{ $('Create Jira Ticket').item.json.key }}", "resource": "issueAttachment"}, "credentials": {"jiraSoftwareCloudApi": {"id": "BZmmGUrNIsgM9fDj", "name": "New  Jira Cloud"}}, "typeVersion": 1}, {"id": "be02770d-a943-41f5-98a9-5c433a6a3dbf", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1420, -107.**************], "parameters": {"color": 7, "width": 792.*************, "height": 426.************, "content": "![Gmail](https://uploads.n8n.io/templates/gmail.png)\n## Gmail Integration and Data Extraction\n\nThis section of the workflow connects to a Gmail account using the **Gmail Trigger** node, capturing incoming emails in real-time, with checks performed every minute. Once an email is detected, its key components—such as the subject, recipient, body, and headers—are extracted and assigned to variables using the **Set Gmail Variables** node. These variables are structured for subsequent analysis and processing in later steps."}, "typeVersion": 1}, {"id": "c1d2f691-669a-46de-9ef8-59ce4e6980c5", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1420, 380.*************], "parameters": {"color": 7, "width": 792.*************, "height": 532.*************, "content": "![Gmail](https://uploads.n8n.io/templates/outlook.png)\n## Microsoft Outlook Integration and Email Header Processing\n\nThis section connects to a Microsoft Outlook account to monitor incoming emails using the **Microsoft Outlook Trigger** node, which checks for new messages every minute. Emails are then processed to retrieve detailed headers and body content via the **Retrieve Headers of Email** node. The headers are structured into a user-friendly format using the **Format Headers** code node, ensuring clarity for further analysis. Key details, including the email's subject, recipient, and body content, are assigned to variables with the **Set Outlook Variables** node for streamlined integration into subsequent workflow steps."}, "typeVersion": 1}, {"id": "c189e2e0-9f51-4bc0-a483-8b7f0528be70", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [2287.*************, 46.**************], "parameters": {"color": 7, "width": 580.*************, "height": 615.************, "content": "![hctiapi](https://uploads.n8n.io/templates/hctiapi.png)\n## HTML Screenshot Generation and Email Visualization\n\nThis section processes an email’s HTML content to create a visual representation, useful for documentation or phishing detection workflows. The **Set Email Variables** node organizes the email's HTML body into a format ready for processing. The **Screenshot HTML** node sends this HTML content to the **hcti.io** API, which generates a screenshot of the email's layout. The **Retrieve Screenshot** node then fetches the image URL for further use in the workflow. This setup ensures that the email's appearance is preserved in a visually accessible format, simplifying review and reporting. Keep in mind however that this exposes the email content to a third party. If you self host n8n, you can deploy a cli tool to rasterize locally instead."}, "typeVersion": 1}, {"id": "9076f9e9-f4fb-409a-9580-1ae459094c31", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [2880, 123.72476075009968], "parameters": {"color": 7, "width": 507.82894736842223, "height": 537.9199760920052, "content": "![hctiapi](https://uploads.n8n.io/templates/openai.png)\n## AI-Powered Email Analysis with ChatGPT\n\nThis section leverages AI to analyze email content and headers for phishing indicators. The **ChatGPT Analysis** node utilizes the ChatGPT-4 model to review the email screenshot and associated metadata, including message headers. It generates a detailed report indicating whether the email might be a phishing attempt. The output is formatted specifically for <PERSON><PERSON>’s wiki-style renderer, making it ready for seamless integration into ticketing workflows. This ensures thorough and automated email threat assessments."}, "typeVersion": 1}, {"id": "ca2488af-e787-4675-802a-8b4f2d845376", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [3400, 122.88662032580646], "parameters": {"color": 7, "width": 692.434210526317, "height": 529.5475902005091, "content": "![hctiapi](https://uploads.n8n.io/templates/jira.png)\n## Automated Jira Ticket Creation for Phishing Reports\n\nThis section streamlines the process of reporting phishing emails by automatically creating detailed Jira tickets. The **Create Jira Ticket** node compiles email information, including the subject, recipient, body text, and ChatGPT's phishing analysis, into a structured ticket. The **Rename Screenshot** node ensures that the email screenshot file is appropriately labeled for attachment. Finally, the **Upload Screenshot of Email to Jira** node attaches the email’s visual representation to the ticket, providing additional context for the security team. This integration ensures that phishing reports are logged with all necessary details, enabling efficient tracking and resolution."}, "typeVersion": 1}], "pinData": {}, "connections": {"Gmail Trigger": {"main": [[{"node": "Set Gmail Variables", "type": "main", "index": 0}]]}, "Format Headers": {"main": [[{"node": "Set Outlook Variables", "type": "main", "index": 0}]]}, "Screenshot HTML": {"main": [[{"node": "Retrieve Screenshot", "type": "main", "index": 0}]]}, "ChatGPT Analysis": {"main": [[{"node": "Create <PERSON><PERSON>", "type": "main", "index": 0}]]}, "Rename Screenshot": {"main": [[{"node": "Upload Screenshot of Email to Jira", "type": "main", "index": 0}]]}, "Create Jira Ticket": {"main": [[{"node": "Rename Screenshot", "type": "main", "index": 0}]]}, "Retrieve Screenshot": {"main": [[{"node": "ChatGPT Analysis", "type": "main", "index": 0}]]}, "Set Email Variables": {"main": [[{"node": "Screenshot HTML", "type": "main", "index": 0}]]}, "Set Gmail Variables": {"main": [[{"node": "Set Email Variables", "type": "main", "index": 0}]]}, "Set Outlook Variables": {"main": [[{"node": "Set Email Variables", "type": "main", "index": 0}]]}, "Microsoft Outlook Trigger": {"main": [[{"node": "Retrieve Headers of Email", "type": "main", "index": 0}]]}, "Retrieve Headers of Email": {"main": [[{"node": "Format Headers", "type": "main", "index": 0}]]}}}