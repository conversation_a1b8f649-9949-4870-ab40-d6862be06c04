{"id": "okMME97B70fXzK5U", "meta": {"instanceId": "2ed3d505bdd5b40b1b0ebc27913bd00ac94bca7476c7375336a42d5c4724f977", "templateCredsSetupCompleted": true}, "name": "send file to kindle through telegram bot", "tags": [], "nodes": [{"id": "6e581437-28dc-4573-88f2-ce44ec497819", "name": "receive file message from telegram bot", "type": "n8n-nodes-base.telegramTrigger", "position": [460, 520], "webhookId": "5d8d9b97-672d-461a-83c5-1665136494dd", "parameters": {"updates": ["message"], "additionalFields": {"download": true}}, "typeVersion": 1.1}, {"id": "6eb48c62-69a9-4bd2-a6ab-cffb5cde03df", "name": "check if there is a file in the message", "type": "n8n-nodes-base.if", "position": [680, 520], "parameters": {"options": {"ignoreCase": false}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "4ad69b1f-c19f-436d-8af3-203722f4dd4c", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.message.document }}", "rightValue": ""}]}}, "typeVersion": 2, "alwaysOutputData": false}, {"id": "5dec7f0b-e650-4249-a5fb-e31cebda8e81", "name": "reply to warn that file is missing", "type": "n8n-nodes-base.telegram", "position": [900, 720], "parameters": {"text": "There is no file in message.Please check.", "chatId": "={{ $('receive file message from telegram bot').item.json.message.chat.id }}", "additionalFields": {"reply_to_message_id": "={{ $('receive file message from telegram bot').item.json.message.message_id }}"}}, "typeVersion": 1.1}, {"id": "79f49881-6cb0-4207-8143-11f021e71083", "name": "rename file to as attachment out email", "type": "n8n-nodes-base.code", "position": [900, 320], "parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nfor (const item of $input.all()) {\n  item.binary.data.fileName = item.json.message.document.file_name;\n}\n\nreturn $input.all();"}, "typeVersion": 2}, {"id": "059babdc-eb35-416a-9ff4-31a34bd6a4f9", "name": "send email with the file as attchament to kindle", "type": "n8n-nodes-base.microsoftOutlook", "position": [1160, 320], "parameters": {"subject": "book from telegram bot", "bodyContent": "=This is a book named  {{ $json.message.document.file_name }} from telegram bot.", "toRecipients": "<EMAIL>", "additionalFields": {"attachments": {"attachments": [{"binaryPropertyName": "data"}]}}}, "notesInFlow": false, "typeVersion": 2}, {"id": "8c927ee3-5b65-4aeb-861c-fe459db1e4c9", "name": "reply to telegram chat that the file has been sent successfully", "type": "n8n-nodes-base.telegram", "position": [1380, 320], "parameters": {"text": "file  is sent to kindle successfully!", "chatId": "={{ $('receive file message from telegram bot').item.json.message.chat.id }}", "additionalFields": {"reply_to_message_id": "={{ $('receive file message from telegram bot').item.json.message.message_id }}"}}, "typeVersion": 1.1}, {"id": "ceba7af1-b23c-426e-a1e7-5fd996021ffe", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [360, 200], "parameters": {"width": 252, "height": 229, "content": "## preparations\n1. create a new telegram bot through bot father and save the credential on n8n\n2. save your email credential on n8n\n3. setup your kindle on amazon to allow your email address send to your kindle."}, "typeVersion": 1}, {"id": "989939ec-b7ea-4903-b375-6f0fc6c4cee1", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1080, 140], "parameters": {"content": "## email setup\nmake sure you have allowed your email address as the sender to kindle on amazon. And use the kindle address as the email receiver"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"callerPolicy": "workflowsFromSameOwner", "errorWorkflow": "XNlgpSG3BUF32PIk", "executionOrder": "v1", "saveManualExecutions": true}, "versionId": "624798e9-5f62-4c14-9bf3-5ad92b8713e6", "connections": {"receive file message from telegram bot": {"main": [[{"node": "check if there is a file in the message", "type": "main", "index": 0}]]}, "rename file to as attachment out email": {"main": [[{"node": "send email with the file as attchament to kindle", "type": "main", "index": 0}]]}, "check if there is a file in the message": {"main": [[{"node": "rename file to as attachment out email", "type": "main", "index": 0}], [{"node": "reply to warn that file is missing", "type": "main", "index": 0}]]}, "send email with the file as attchament to kindle": {"main": [[{"node": "reply to telegram chat that the file has been sent successfully", "type": "main", "index": 0}]]}}}