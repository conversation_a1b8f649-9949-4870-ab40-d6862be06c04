{"meta": {"instanceId": "1dd912a1610cd0376bae7bb8f1b5838d2b601f42ac66a48e012166bb954fed5a", "templateId": "2301"}, "nodes": [{"id": "a6d8c7aa-c75c-4aaa-8fe2-e23f3da2b8f5", "name": "get node types", "type": "n8n-nodes-base.httpRequest", "position": [820, 240], "parameters": {"url": "={{ $json.instanceBaseUrl }}/types/nodes.json", "options": {}, "authentication": "predefinedCredentialType", "nodeCredentialType": "n8nApi"}, "credentials": {"n8nApi": {"id": "xhyxmtPC3UwZ7HmL", "name": "n8n account"}}, "typeVersion": 4.2}, {"id": "55bedad2-0096-4a59-8818-9bdbe9799230", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [380, 240], "parameters": {}, "typeVersion": 1}, {"id": "dc37402e-558d-4c6c-883e-450f161d5766", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [1040, 480], "parameters": {"include": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options": {"destinationFieldName": "node"}, "fieldToSplitOut": "nodes", "fieldsToInclude": "name, id"}, "typeVersion": 1}, {"id": "dcaec125-684a-4b50-8cb8-fcce9763929b", "name": "If", "type": "n8n-nodes-base.if", "position": [1240, 480], "parameters": {"options": {"looseTypeValidation": true}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "loose"}, "combinator": "and", "conditions": [{"id": "1c65a9cf-dd60-4d3f-8fe6-05e5877ab58a", "operator": {"type": "boolean", "operation": "notEquals"}, "leftValue": "={{ !!$('Aggregate').first().json.data.find(n => n.name === $json.node.type) }}", "rightValue": false}, {"id": "dbc80785-274f-424c-9862-bed0ec7e4b63", "operator": {"type": "number", "operation": "lt"}, "leftValue": "={{ $json.node.typeVersion }}", "rightValue": "={{ $('Aggregate').first().json.data.find(n => n.name === $json.node.type).version }}"}]}}, "typeVersion": 2}, {"id": "da0d6443-81c8-4d0a-bd2d-300ce83726ad", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "position": [1240, 240], "parameters": {"options": {}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}, {"id": "df683591-1342-4140-9505-359320c08ec0", "name": "extract name and latest version", "type": "n8n-nodes-base.code", "position": [1040, 240], "parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nfor (const item of $input.all()) {\n  item.json.myNewField = 1;\n}\n\nreturn $input.all().map(({json}) => {\n  const typeVersion = Array.isArray(json.version) ? Math.max(...json.version) : json.version;\n  return {\n    name: json.name,\n    version: typeVersion\n  }\n})"}, "typeVersion": 2}, {"id": "cfa7c46e-4292-4d56-8311-a4659ed519fa", "name": "Summarize", "type": "n8n-nodes-base.summarize", "position": [820, 720], "parameters": {"options": {}, "fieldsToSplitBy": "workflowName, workflowId", "fieldsToSummarize": {"values": [{"field": "info", "aggregation": "append"}]}}, "typeVersion": 1}, {"id": "10ba8fe4-bab4-4c5f-a6ed-cd5bcf0b8b04", "name": "get all workflows", "type": "n8n-nodes-base.n8n", "position": [600, 480], "parameters": {"filters": {}, "requestOptions": {}}, "credentials": {"n8nApi": {"id": "xhyxmtPC3UwZ7HmL", "name": "n8n account"}}, "typeVersion": 1}, {"id": "a2fcba1a-866b-48d5-92e6-a3b98a8afbdc", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [553.*************, 420], "parameters": {"width": 433.**************, "height": 205.*************, "content": "Check information for all workflows or a single workflow, activate corresponding node"}, "typeVersion": 1}, {"id": "7a1216f0-5d25-46d1-9965-023d9eedbe6c", "name": "prettify output", "type": "n8n-nodes-base.set", "position": [1040, 720], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "e24c81f9-fca3-4b74-bdc1-50d6933b56e7", "name": "workflow", "type": "string", "value": "={{ $json.workflowName }}"}, {"id": "79c3faaa-5707-49a6-8b9c-7290bcf066bb", "name": "Id", "type": "string", "value": "={{ $json.workflowId }}"}, {"id": "6c7732db-84bb-4a54-85ce-05ce60553208", "name": "outdated_nodes", "type": "array", "value": "={{ $json.appended_info }}"}]}}, "typeVersion": 3.4}, {"id": "99d2fda2-ec3d-4d03-95cb-96c2a04b43d6", "name": "instance base url", "type": "n8n-nodes-base.set", "position": [600, 240], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "ad3ffe8a-2a48-45ad-9171-bd6bffa02488", "name": "instanceBaseUrl", "type": "string", "value": "http://localhost:5432"}]}}, "typeVersion": 3.4}, {"id": "906e1743-1f52-4d7b-b796-75f2a9c5a131", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [548.1243191057811, 152.6859339432964], "parameters": {"width": 228.883554909967, "height": 240.99660770750089, "content": "Set your instance URL here, it should not include API and version"}, "typeVersion": 1}, {"id": "e9a330ae-df1f-4830-9420-afdf4ca9bbbe", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [800, 159.81134247982766], "parameters": {"width": 192.26610453220889, "height": 238.**************, "content": "Get n8n API key in settings > n8n API"}, "typeVersion": 1}, {"id": "b85366ba-ecbd-493e-a1c7-a081e51d0eb2", "name": "get single workflow", "type": "n8n-nodes-base.n8n", "disabled": true, "position": [820, 480], "parameters": {"operation": "get", "workflowId": {"__rl": true, "mode": "list", "value": "03L3B0pAuGRa8cfx", "cachedResultName": "My workflow 40 (#03L3B0pAuGRa8cfx)"}, "requestOptions": {}}, "credentials": {"n8nApi": {"id": "xhyxmtPC3UwZ7HmL", "name": "n8n account"}}, "typeVersion": 1}, {"id": "669b3b8c-e835-455b-a3f8-c1c5ba411020", "name": "node names that needs update", "type": "n8n-nodes-base.set", "position": [600, 720], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "01a01bc8-ffd8-4985-bd01-8ffb4dbaee6c", "name": "workflowName", "type": "string", "value": "={{ $json.name }}"}, {"id": "dc199eab-92b1-46bd-8999-38d64ca37623", "name": "info", "type": "object", "value": "=\n{\n\"name\": \"{{ $json.node.name }}\",\n\"type\": \"{{ $json.node.type }}\",\n\"version\": {{ $json.node.typeVersion }},\n\"latestVersion\": {{ $('Aggregate').first().json.data.find(n => n.name === $json.node.type).version }}\n}"}, {"id": "fe268266-f0ab-47d8-bb6d-a9fefe82f527", "name": "workflowId", "type": "string", "value": "={{ $json.id }}"}]}}, "typeVersion": 3.4}], "pinData": {}, "connections": {"If": {"main": [[{"node": "node names that needs update", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "get all workflows", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Summarize": {"main": [[{"node": "prettify output", "type": "main", "index": 0}]]}, "get node types": {"main": [[{"node": "extract name and latest version", "type": "main", "index": 0}]]}, "get all workflows": {"main": [[{"node": "get single workflow", "type": "main", "index": 0}]]}, "instance base url": {"main": [[{"node": "get node types", "type": "main", "index": 0}]]}, "get single workflow": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "node names that needs update": {"main": [[{"node": "Summarize", "type": "main", "index": 0}]]}, "extract name and latest version": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "instance base url", "type": "main", "index": 0}]]}}}