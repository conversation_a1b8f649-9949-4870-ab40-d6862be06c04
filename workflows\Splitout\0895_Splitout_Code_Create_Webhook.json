{"nodes": [{"id": "18813eed-27a8-4338-8e71-abb270d43082", "name": "Split by object", "type": "n8n-nodes-base.splitOut", "position": [-260, 540], "parameters": {"options": {}, "fieldToSplitOut": "object"}, "typeVersion": 1}, {"id": "ee610ddd-5bd7-4d97-82a9-b688c71616d8", "name": "Fetch properties from Hubspot", "type": "n8n-nodes-base.httpRequest", "position": [-40, 540], "parameters": {"url": "=https://api.hubapi.com/crm/v3/properties/{{ $json.object }}", "options": {}, "authentication": "predefinedCredentialType", "nodeCredentialType": "hubspotOAuth2Api"}, "credentials": {"hubspotOAuth2Api": {"id": "JxzF93M0SJ00jDD9", "name": "HubSpot account"}, "hubspotDeveloperApi": {"id": "EN6KsMdrxSVNfhyz", "name": "HubSpot Developer account"}}, "typeVersion": 4.2}, {"id": "********-97ae-4e59-a70e-3b858e8e632d", "name": "Define crm_type", "type": "n8n-nodes-base.code", "position": [180, 540], "parameters": {"mode": "runOnceForEachItem", "jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\n\nfor (let result of $json.results) {\n  result.crm_type = $('Split by object').item.json.object;\n  delete result.options\n}\nreturn {results: $json.results};\n\n"}, "typeVersion": 2}, {"id": "abfdd971-1112-4dd4-9513-13f12f3e92f2", "name": "Split results", "type": "n8n-nodes-base.splitOut", "position": [400, 540], "parameters": {"include": "allOtherFields", "options": {}, "fieldToSplitOut": "results"}, "typeVersion": 1}, {"id": "94c2b864-e88d-4da8-9ca3-a34d1ca8b15b", "name": "Transforms the results", "type": "n8n-nodes-base.code", "position": [840, 540], "parameters": {"mode": "runOnceForEachItem", "jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nlet test = []\nlet entry = $input.item.json.results\nreturn {json: entry}\n"}, "typeVersion": 2}, {"id": "ddebf521-ed07-446b-ac2a-e21be46ee2c5", "name": "Append to Google sheet", "type": "n8n-nodes-base.googleSheets", "position": [1060, 540], "parameters": {"columns": {"value": {}, "schema": [{"id": "updatedAt", "type": "string", "display": true, "removed": false, "required": false, "displayName": "updatedAt", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "createdAt", "type": "string", "display": true, "removed": false, "required": false, "displayName": "createdAt", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "label", "type": "string", "display": true, "removed": false, "required": false, "displayName": "label", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "type", "type": "string", "display": true, "removed": false, "required": false, "displayName": "type", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "fieldType", "type": "string", "display": true, "removed": false, "required": false, "displayName": "fieldType", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "description", "type": "string", "display": true, "removed": false, "required": false, "displayName": "description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "groupName", "type": "string", "display": true, "removed": false, "required": false, "displayName": "groupName", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "displayOrder", "type": "string", "display": true, "removed": false, "required": false, "displayName": "displayOrder", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "calculated", "type": "string", "display": true, "removed": false, "required": false, "displayName": "calculated", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "externalOptions", "type": "string", "display": true, "removed": false, "required": false, "displayName": "externalOptions", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "hasUniqueValue", "type": "string", "display": true, "removed": false, "required": false, "displayName": "hasUniqueValue", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "hidden", "type": "string", "display": true, "removed": false, "required": false, "displayName": "hidden", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "hubspotDefined", "type": "string", "display": true, "removed": false, "required": false, "displayName": "hubspotDefined", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "formField", "type": "string", "display": true, "removed": false, "required": false, "displayName": "formField", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "dataSensitivity", "type": "string", "display": true, "removed": false, "required": false, "displayName": "dataSensitivity", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "crm_type", "type": "string", "display": true, "removed": false, "required": false, "displayName": "crm_type", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "showCurrencySymbol", "type": "string", "display": true, "removed": false, "required": false, "displayName": "showCurrencySymbol", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "calculationFormula", "type": "string", "display": true, "removed": false, "required": false, "displayName": "calculationFormula", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "referencedObjectType", "type": "string", "display": true, "removed": false, "required": false, "displayName": "referencedObjectType", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "createdUserId", "type": "string", "display": true, "removed": false, "required": false, "displayName": "createdUserId", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "updatedUserId", "type": "string", "display": true, "removed": false, "required": false, "displayName": "updatedUserId", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "archived", "type": "string", "display": true, "removed": false, "required": false, "displayName": "archived", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "dateDisplayHint", "type": "string", "display": true, "removed": false, "required": false, "displayName": "dateDisplayHint", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "options", "type": "string", "display": true, "removed": false, "required": false, "displayName": "options", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "modificationMetadata", "type": "string", "display": true, "removed": false, "required": false, "displayName": "modificationMetadata", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {"useAppend": false}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1NdvtXADHaSBleSkvVxf6Y6yo3VmHmilLEBuWbrik32w/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1NdvtXADHaSBleSkvVxf6Y6yo3VmHmilLEBuWbrik32w", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1NdvtXADHaSBleSkvVxf6Y6yo3VmHmilLEBuWbrik32w/edit?usp=drivesdk", "cachedResultName": "Properties for Hubspot"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "gdLmm513ROUyH6oU", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "dfd3d16b-b7d6-49ba-a38b-076960a8a184", "name": "Erase Google sheet", "type": "n8n-nodes-base.googleSheets", "position": [-700, 540], "parameters": {"operation": "clear", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1NdvtXADHaSBleSkvVxf6Y6yo3VmHmilLEBuWbrik32w/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1NdvtXADHaSBleSkvVxf6Y6yo3VmHmilLEBuWbrik32w", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1NdvtXADHaSBleSkvVxf6Y6yo3VmHmilLEBuWbrik32w/edit?usp=drivesdk", "cachedResultName": "Properties for Hubspot"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "gdLmm513ROUyH6oU", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "d39acf68-f809-4a4b-bb5e-5f80a7fddfbc", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1000, 460], "parameters": {"color": 7, "width": 2280, "height": 460, "content": "## Update the properties by object Workflow\n"}, "typeVersion": 1}, {"id": "99ce38cb-937c-44f4-8e21-cceb8c5fa000", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-1000, -300], "parameters": {"color": 7, "width": 3200, "height": 700, "content": "## Import workflow\n"}, "typeVersion": 1}, {"id": "3b231f69-ca9b-40a4-b894-24cece123855", "name": "Define array of objects", "type": "n8n-nodes-base.set", "position": [-480, 540], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d6c05100-fc13-4969-90e5-bcc398a79006", "name": "object", "type": "array", "value": "[\"companies\",\"contacts\", \"deals\", \"leads\", \"tickets\"]"}]}}, "typeVersion": 3.4}, {"id": "d3eff9e3-1fae-4228-bcd9-525854f3f440", "name": "Start here to update your field list", "type": "n8n-nodes-base.manualTrigger", "position": [-920, 540], "parameters": {}, "typeVersion": 1}, {"id": "b1a4d238-9d55-4bff-a1b4-3942dbe37fdb", "name": "File upload form", "type": "n8n-nodes-base.formTrigger", "position": [-920, 20], "webhookId": "fc3523af-1d0f-4dfb-8869-b29cfdde1a06", "parameters": {"options": {}, "formTitle": "title", "formFields": {"values": [{"fieldType": "file", "fieldLabel": "data", "multipleFiles": false, "requiredField": true, "acceptFileTypes": ".csv"}, {"fieldType": "dropdown", "fieldLabel": "Type of import", "fieldOptions": {"values": [{"option": "Companies"}, {"option": "Contacts"}, {"option": "Leads"}, {"option": "Deals"}, {"option": "Tickets"}]}}]}, "formDescription": "provide me a file"}, "typeVersion": 2.2}, {"id": "44f4ffe7-ff9f-4716-82ef-fc3c44dc48ca", "name": "To get the first line of file", "type": "n8n-nodes-base.extractFromFile", "position": [-700, 120], "parameters": {"options": {}, "operation": "text"}, "typeVersion": 1}, {"id": "351604db-d9e9-4994-8c1c-f543c13aead9", "name": "Set the real fields", "type": "n8n-nodes-base.set", "position": [-480, 120], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "69a042d8-9543-4a81-bbf8-07e9d7ae2c0d", "name": "real_fields", "type": "array", "value": "={{ $json.data.split(\"\\n\")[0].split(\";\") }}"}]}}, "typeVersion": 3.4}, {"id": "a61d6de1-005e-41ad-a71e-3eafde83afc7", "name": "Get the fields from the sheet", "type": "n8n-nodes-base.googleSheets", "position": [-40, 20], "parameters": {"options": {}, "filtersUI": {"values": [{"lookupValue": "={{ $('File upload form').first().json['Type of import'].toLowerCase() }}", "lookupColumn": "crm_type"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1NdvtXADHaSBleSkvVxf6Y6yo3VmHmilLEBuWbrik32w/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1NdvtXADHaSBleSkvVxf6Y6yo3VmHmilLEBuWbrik32w", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1NdvtXADHaSBleSkvVxf6Y6yo3VmHmilLEBuWbrik32w/edit?usp=drivesdk", "cachedResultName": "Properties for Hubspot"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "gdLmm513ROUyH6oU", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "617d572a-53a9-4fe8-9f73-06689c706006", "name": "Merge fields and data", "type": "n8n-nodes-base.merge", "position": [-260, 20], "parameters": {}, "typeVersion": 3.1}, {"id": "f2be6bfb-ac32-43d0-924c-d8f20a401b2f", "name": "Check if all fields from input are defined", "type": "n8n-nodes-base.code", "position": [180, 20], "parameters": {"jsCode": "// \nlet type = $('File upload form').first().json['Type of import']\n// Get first line of json\nlet first_line = $('Set the real fields').first().json.real_fields\nlet keys = Object.values(first_line)\nlet props = []\n\nfor (let realField of $input.all()) {\n  props.push(realField.json.name)\n}\nlet response = true\nfor (let key of keys) {\n if(!props.includes(key.trim())) {\n    console.log(props, key)\n    response = false\n }\n}\n\nreturn {response, keys, props}"}, "typeVersion": 2}, {"id": "8a2e23a3-c044-48ac-b66c-7205e34ad3bd", "name": "If all fields are defined", "type": "n8n-nodes-base.if", "position": [400, 20], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "3bb457eb-aef5-43f6-b268-1baaad0698e3", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.response }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "123f6190-600a-410a-b943-a6e67d4f0a86", "name": "Creates the correspondance table", "type": "n8n-nodes-base.code", "position": [620, 120], "parameters": {"jsCode": "\nlet ret = []\nlet fields = {}\nfor (let key of $input.first().json.keys) {\n  if (!$input.first().json.props.includes(key)) {\n    let fieldName = `Set the correct field for '${key}'`\n    fields[fieldName] = key\n    // console.log(key)\n    ret.push(\n      {\n      \"fieldLabel\":key,\n     \"fieldType\": \"dropdown\",\n     \"fieldOptions\": {\n      \"values\": $input.first().json.props.map(x => {return {\"option\": x}})\n\t\t},\n      \"requiredField\":false\n   }\n    )\n  }\n}\n\nreturn {ret, fields}"}, "typeVersion": 2}, {"id": "c7348c9a-e4c3-4af2-9224-5338799ed7aa", "name": "Form to set the correponding field for each input field", "type": "n8n-nodes-base.form", "position": [840, 120], "webhookId": "8bdb6e07-1112-4923-a1a3-a0fbb83c806e", "parameters": {"options": {"formTitle": "=Correspondance for fields", "formDescription": "=Set the correct equivalent for each field.\nYou don't have to do it for all fields."}, "defineForm": "json", "jsonOutput": "={{$json.ret}}"}, "executeOnce": true, "typeVersion": 1}, {"id": "2ba6be51-2508-4d34-b447-2f326fb692b5", "name": "Get the content of file", "type": "n8n-nodes-base.extractFromFile", "onError": "continueRegularOutput", "position": [-480, -80], "parameters": {"options": {"encoding": "utf-8", "delimiter": ";", "headerRow": true}}, "typeVersion": 1}, {"id": "3bae9532-81d5-4694-b2cd-40c2b8207b22", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-960, -220], "parameters": {"color": 4, "width": 840, "height": 500, "content": "## Form uploader\n- Choose  file to import. The CSV file has \",\" as delimiters, is encoded in UTF8 and has the name of the fields as header. You can change all that in \"Get content of the file\"\n- Set the type of object you want to import"}, "typeVersion": 1}, {"id": "2836df7d-4307-485c-857e-30b0bb4cf59b", "name": "Split all records to import", "type": "n8n-nodes-base.splitOut", "position": [1280, 20], "parameters": {"include": "allOtherFields", "options": {}, "fieldToSplitOut": "out"}, "typeVersion": 1}, {"id": "5d4481f4-0157-42d4-8223-1259f45a1846", "name": "Define properties", "type": "n8n-nodes-base.set", "position": [1500, 20], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "bc1ad698-c75a-49e5-843c-03c1c64a21b1", "name": "def.properties", "type": "object", "value": "={{ $json.out }}"}]}}, "typeVersion": 3.4}, {"id": "b765d44e-6b13-4031-b188-e827578b9bee", "name": "Uploads to Hubspot", "type": "n8n-nodes-base.httpRequest", "position": [1720, 20], "parameters": {"url": "https://api.hubapi.com/crm/v3/objects/companies", "method": "POST", "options": {}, "jsonBody": "={{ $json.def }}", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "hubspotOAuth2Api"}, "credentials": {"hubspotOAuth2Api": {"id": "JxzF93M0SJ00jDD9", "name": "HubSpot account"}, "hubspotDeveloperApi": {"id": "EN6KsMdrxSVNfhyz", "name": "HubSpot Developer account"}}, "typeVersion": 4.2}, {"id": "f95862b2-555b-44a7-b318-cb3316d33594", "name": "Form response", "type": "n8n-nodes-base.form", "position": [1940, 20], "webhookId": "980c195f-9ea2-4f38-a869-6ac946b9552d", "parameters": {"options": {"formTitle": ""}, "operation": "completion", "completionTitle": "Your Data has been imported successfully"}, "typeVersion": 1}, {"id": "75275b15-24e3-4fee-9d71-b4e7a2479c11", "name": "Remove hidden and starting with hs_ props fields", "type": "n8n-nodes-base.filter", "position": [620, 540], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "14ed0cde-e546-4b13-9405-16834831a7b4", "operator": {"type": "string", "operation": "notStartsWith"}, "leftValue": "={{ $json.results.name }}", "rightValue": "hs_"}, {"id": "********-8aba-404c-b6e0-99fcd60e1d84", "operator": {"type": "boolean", "operation": "false", "singleValue": true}, "leftValue": "={{ $json.results.hidden }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "3b131ff9-ff8c-4b4c-8f48-7603e2f4e29c", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-100, -220], "parameters": {"color": 4, "width": 660, "height": 500, "content": "## Properties procesor\n- Get the list of properties defined by \"Update the properties by object\" for the choosen object in \"Form uploader\"\n- Check if all fields fro the file have their name in this list\n- If not, go to the correspondance form\n- if yes goes on to processing"}, "typeVersion": 1}, {"id": "75d465db-f0df-489b-a596-ed9e5a6b97ea", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [580, -220], "parameters": {"color": 4, "width": 640, "height": 500, "content": "## Set the values for each property\n"}, "typeVersion": 1}, {"id": "16869a28-c6c1-4f88-ae7a-6ca74ad97a31", "name": "Set the values for each field", "type": "n8n-nodes-base.code", "position": [1060, -80], "parameters": {"jsCode": "\nfunction findKeyByValue(obj, value) {\n  return Object.keys(obj).find(key => obj[key] === value);\n}\n\nlet out = []\nconst data = $('Get the content of file').all().map(x => x.json)\nconsole.log(data)\n\nfor (const item of data) {\n  console.log(item)\n  let elt = {}\n  \n  for (const prop of $('Check if all fields from input are defined').first().json.props) {\n      elt[prop] = item[prop]\n  }\n\n  out.push(elt)\n}\n\nreturn {out}"}, "typeVersion": 2}, {"id": "c7f51291-91df-497e-8466-031ac031384a", "name": "Set the values for each field1", "type": "n8n-nodes-base.code", "position": [1060, 120], "parameters": {"jsCode": "\nfunction findKeyByValue(obj, value) {\n  return Object.keys(obj).find(key => obj[key] === value);\n}\n\nlet out = []\nconst data = $('Get the content of file').all().map(x => x.json)\n// console.log(form_fields)\n\nfor (const item of data) {\n  let elt = {}\n  for (const prop of $('Check if all fields from input are defined').first().json.props) {\n    let equival = findKeyByValue($input.all()[0].json, prop)\n    if(equival) {\n      elt[prop] = item[equival]\n    } else {\n      elt[prop] = item[prop]\n    }\n  }\n  \n  out.push(elt)\n}\n\nreturn {out}"}, "typeVersion": 2}, {"id": "6aafe2ff-e4c7-4e07-8a39-d5bed120fdf7", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1240, -220], "parameters": {"color": 4, "width": 640, "height": 500, "content": "## Import the values in Hubspot\n"}, "typeVersion": 1}, {"id": "************************************", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-1000, -520], "parameters": {"width": 460, "height": 200, "content": "## Contact me\n- If you need any modification to this workflow\n- if you need some help with this workflow\n- Or if you need any workflow in n8n, Make, or Langchain / Langgraph\n\nWrite to me: [<EMAIL>](mailto:<EMAIL>)\nCheck out my other templates [here](https://n8n.io/creators/zeerobug/)"}, "typeVersion": 1}, {"id": "5cf4f276-54e4-4e31-af1c-c2808802afda", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-540, 520], "parameters": {"color": 4, "height": 380, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n## List of objects\nDefine Here the list of the objects you would like to import in Hubspot"}, "typeVersion": 1}, {"id": "************************************", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [560, 520], "parameters": {"color": 4, "height": 380, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n## Filter the list of properties here"}, "typeVersion": 1}, {"id": "ae9d2dee-1c07-40eb-b8aa-020cde8534df", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [-760, 520], "parameters": {"color": 4, "width": 200, "height": 380, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n## Create an empty Google Sheet\nIf you run this part, and set it here and in the last node"}, "typeVersion": 1}], "connections": {"Split results": {"main": [[{"node": "Remove hidden and starting with hs_ props fields", "type": "main", "index": 0}]]}, "Define crm_type": {"main": [[{"node": "Split results", "type": "main", "index": 0}]]}, "Split by object": {"main": [[{"node": "Fetch properties from Hubspot", "type": "main", "index": 0}]]}, "File upload form": {"main": [[{"node": "To get the first line of file", "type": "main", "index": 0}, {"node": "Get the content of file", "type": "main", "index": 0}]]}, "Define properties": {"main": [[{"node": "Uploads to Hubspot", "type": "main", "index": 0}]]}, "Erase Google sheet": {"main": [[{"node": "Define array of objects", "type": "main", "index": 0}]]}, "Uploads to Hubspot": {"main": [[{"node": "Form response", "type": "main", "index": 0}]]}, "Set the real fields": {"main": [[{"node": "Merge fields and data", "type": "main", "index": 1}]]}, "Merge fields and data": {"main": [[{"node": "Get the fields from the sheet", "type": "main", "index": 0}]]}, "Append to Google sheet": {"main": [[]]}, "Transforms the results": {"main": [[{"node": "Append to Google sheet", "type": "main", "index": 0}]]}, "Define array of objects": {"main": [[{"node": "Split by object", "type": "main", "index": 0}]]}, "Get the content of file": {"main": [[{"node": "Merge fields and data", "type": "main", "index": 0}]]}, "If all fields are defined": {"main": [[{"node": "Set the values for each field", "type": "main", "index": 0}], [{"node": "Creates the correspondance table", "type": "main", "index": 0}]]}, "Split all records to import": {"main": [[{"node": "Define properties", "type": "main", "index": 0}]]}, "Fetch properties from Hubspot": {"main": [[{"node": "Define crm_type", "type": "main", "index": 0}]]}, "Get the fields from the sheet": {"main": [[{"node": "Check if all fields from input are defined", "type": "main", "index": 0}]]}, "Set the values for each field": {"main": [[{"node": "Split all records to import", "type": "main", "index": 0}]]}, "To get the first line of file": {"main": [[{"node": "Set the real fields", "type": "main", "index": 0}]]}, "Set the values for each field1": {"main": [[{"node": "Split all records to import", "type": "main", "index": 0}]]}, "Creates the correspondance table": {"main": [[{"node": "Form to set the correponding field for each input field", "type": "main", "index": 0}]]}, "Start here to update your field list": {"main": [[{"node": "Erase Google sheet", "type": "main", "index": 0}]]}, "Check if all fields from input are defined": {"main": [[{"node": "If all fields are defined", "type": "main", "index": 0}]]}, "Remove hidden and starting with hs_ props fields": {"main": [[{"node": "Transforms the results", "type": "main", "index": 0}]]}, "Form to set the correponding field for each input field": {"main": [[{"node": "Set the values for each field1", "type": "main", "index": 0}]]}}}