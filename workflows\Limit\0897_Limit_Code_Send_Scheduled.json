{"meta": {"instanceId": "937602287d3b666a0823bdd18262071b517e6d94e73b786e71216e87cc17b79b", "templateCredsSetupCompleted": true}, "nodes": [{"id": "d74c545f-17ab-47f7-bb2a-93c9e9673bab", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [460, -20], "parameters": {"rule": {"interval": [{"triggerAtHour": 1, "triggerAtMinute": 30}]}}, "typeVersion": 1.2}, {"id": "fc54b674-dc64-49ad-819d-66a4e416efc2", "name": "Get all n8n Workflows", "type": "n8n-nodes-base.n8n", "position": [680, -20], "parameters": {"filters": {}, "requestOptions": {}}, "credentials": {"n8nApi": {"id": "WR8oA7tQqdurDv3Y", "name": "n8n account"}}, "typeVersion": 1}, {"id": "b23cd260-8e68-42e7-935c-a658ae35cccd", "name": "Backup to Google Drive2", "type": "n8n-nodes-base.googleDrive", "onError": "continueErrorOutput", "position": [1260, 400], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "options": {}, "operation": "update", "changeFileContent": true, "newUpdatedFileName": "={{  $('Workflow Data').item.json.name + \"_\" + $('Workflow Data').item.json.id+ \".json\"}}"}, "credentials": {"googleDriveOAuth2Api": {"id": "FsjSbb8sdqbZm9dM", "name": "Out"}}, "retryOnFail": true, "typeVersion": 3}, {"id": "29a69d92-f416-489d-9a96-3a22844556e0", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [920, -20], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "ddee56fd-8610-4cae-9ae0-76e58e7fd111", "name": "Backup to Google Drive4", "type": "n8n-nodes-base.googleDrive", "onError": "continueErrorOutput", "position": [1380, 720], "parameters": {"name": "={{  $('Workflow Data').item.json.name + \"_\" + $('Workflow Data').item.json.id+ \".json\"}}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "13clPf8pnv_-GLeeNXLhuVzQiqnKo_7Ev", "cachedResultUrl": "https://drive.google.com/drive/folders/13clPf8pnv_-GLeeNXLhuVzQiqnKo_7Ev", "cachedResultName": "n8nWorkflows"}}, "credentials": {"googleDriveOAuth2Api": {"id": "FsjSbb8sdqbZm9dM", "name": "Out"}}, "retryOnFail": true, "typeVersion": 3}, {"id": "8fdf83b1-5884-45a2-8710-e9012c07ccca", "name": "ifDriveEmpty", "type": "n8n-nodes-base.if", "position": [680, 420], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "5ec1b850-e0ce-4bd6-a8be-504e01825c00", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{$('getDriveFileData').item.json.name}}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "01437168-bb55-4308-a83c-a26c0f9c1843", "name": "firstWorkflowJson", "type": "n8n-nodes-base.set", "position": [1000, 720], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "={{ $('Workflow Data').item.json.toJsonString() }}\n"}, "typeVersion": 3.4}, {"id": "7bcb95db-b13b-4bef-9a34-acd1194f6d96", "name": "JsonToFile", "type": "n8n-nodes-base.code", "position": [1180, 720], "parameters": {"jsCode": "return items.map(item => {\n  const jsonData = JSON.stringify(item.json);\n  const binaryData = Buffer.from(jsonData).toString('base64');\n  item.binary = {\n    data: {\n      data: binaryData,\n      mimeType: 'application/json',\n      fileName: 'data.json'\n    }\n  };\n  return item;\n});"}, "typeVersion": 2}, {"id": "efdb7ea6-f4bf-4553-993c-448cd7bb2039", "name": "CodeJsonToFile1", "type": "n8n-nodes-base.code", "position": [1080, 400], "parameters": {"jsCode": "return items.map(item => {\n  const jsonData = JSON.stringify( $('Workflow Data').item.json);\n  const binaryData = Buffer.from(jsonData).toString('base64');\n  item.binary = {\n    data: {\n      data: binaryData,\n      mimeType: 'application/json',\n      fileName: 'data.json'\n    }\n  };\n  return item;\n});"}, "typeVersion": 2}, {"id": "411b1585-4be1-4a92-a54b-64965f0d529d", "name": "Limit", "type": "n8n-nodes-base.limit", "position": [1100, -40], "parameters": {}, "typeVersion": 1}, {"id": "dcd2e2ee-fc18-47bc-9210-b1b42c270961", "name": "Workflow Data", "type": "n8n-nodes-base.executionData", "position": [-140, 420], "parameters": {}, "typeVersion": 1}, {"id": "d243a474-9139-4af4-8134-df815a4af806", "name": "successEmail", "type": "n8n-nodes-base.gmail", "position": [1360, -40], "webhookId": "b6cdbf4b-3abf-4eda-aa49-c19012e3133b", "parameters": {"sendTo": "your email address", "message": "={{ $now.format('yyyy-MM-dd HH:mm') }} workflow backup success.", "options": {}, "subject": "google drive workflow backup success", "emailType": "text"}, "credentials": {"gmailOAuth2": {"id": "3QEYg96F002cbPmf", "name": "out account"}}, "typeVersion": 2.1}, {"id": "306a1d38-27ef-4249-956a-cfec30d898b1", "name": "failureEmail", "type": "n8n-nodes-base.gmail", "position": [1620, 420], "webhookId": "f38fba13-3970-43a5-8afd-ea873289015b", "parameters": {"sendTo": "your email address", "message": "={{ $now }} {{ $('Workflow Data').item.json.name }} workflow backup .", "options": {}, "subject": "google drive workflow backup error", "emailType": "text"}, "credentials": {"gmailOAuth2": {"id": "3QEYg96F002cbPmf", "name": "out account"}}, "typeVersion": 2.1}, {"id": "544cb91c-4f96-4a84-8db2-9c88e758a1e3", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [600, -80], "parameters": {"color": 5, "width": 260, "height": 220, "content": "## Set n8n API"}, "typeVersion": 1}, {"id": "84d6b3e9-9f01-40b8-980d-acd2f95d30fe", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [600, -180], "parameters": {"color": 4, "width": 150, "height": 80, "content": "## Edit this node 👇"}, "typeVersion": 1}, {"id": "a3f1669b-41da-4256-af2c-e556738eabf1", "name": "getDriveFileData", "type": "n8n-nodes-base.googleDrive", "position": [300, 420], "parameters": {"filter": {"folderId": {"__rl": true, "mode": "url", "value": "={{ $('Parameters').item.json.directory }}"}, "whatToSearch": "files"}, "options": {}, "resource": "fileFolder", "returnAll": true, "queryString": "={{  $('Workflow Data').item.json.name + \"_\" + $('Workflow Data').item.json.id+ \".json\"}}"}, "credentials": {"googleDriveOAuth2Api": {"id": "FsjSbb8sdqbZm9dM", "name": "Out"}}, "retryOnFail": true, "typeVersion": 3, "alwaysOutputData": true}, {"id": "f1771f9e-4153-4595-bbd9-22abfef23c54", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-300, 420], "parameters": {"inputSource": "passthrough"}, "typeVersion": 1.1}, {"id": "7110911a-c6c6-4ef6-888f-f640784d077b", "name": "Execute Workflow", "type": "n8n-nodes-base.executeWorkflow", "position": [1100, 100], "parameters": {"options": {}, "workflowId": {"__rl": true, "mode": "id", "value": "DfMF9CmVw6FU4hYm"}, "workflowInputs": {"value": {}, "schema": [], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": true}}, "typeVersion": 1.2}, {"id": "cb5cc7fb-f24f-48be-a175-c24bf830dce2", "name": "Parameters", "type": "n8n-nodes-base.set", "position": [20, 420], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "1b65def6-4984-497d-a4bc-232af22927ad", "name": "directory", "type": "string", "value": "https://drive.google.com/drive/folders/13clPf8pnv_-GLeeNXLhuVzQiqnKo_7Ev?usp=share_link"}, {"id": "c8c98f88-9f22-4574-88b8-1db99f6e4ec4", "name": "parentdrive", "type": "string", "value": "https://drive.google.com/drive/u/0/my-drive"}]}}, "typeVersion": 3.4}, {"id": "0ab79967-aa4e-4914-abbd-8a60057b083d", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-20, 300], "parameters": {"color": 4, "width": 150, "height": 80, "content": "## Edit this node 👇"}, "typeVersion": 1}, {"id": "948c9276-88a7-4d02-85dc-525c4e8b0c01", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [1240, -100], "parameters": {"width": 340, "height": 220, "content": "## Send complete message"}, "typeVersion": 1}, {"id": "cabf9b91-5a2f-4c8f-ae07-318fca57b54f", "name": "Discord", "type": "n8n-nodes-base.discord", "position": [1360, 80], "webhookId": "65ce702c-8f03-4016-b192-a2503a7fbca7", "parameters": {"content": "={{ $now.format('yyyy-MM-dd HH:mm') }} Google Drive workflow backup success.", "guildId": {"__rl": true, "mode": "list", "value": ""}, "options": {"tts": false}, "resource": "message", "channelId": {"__rl": true, "mode": "list", "value": "1365663078880116756", "cachedResultUrl": "https://discord.com/channels/1365624783781494854/1365663078880116756", "cachedResultName": "backup-status"}}, "credentials": {"discordBotApi": {"id": "hm4HwPUEF07pmkj0", "name": "Discord Bot account"}}, "typeVersion": 2}, {"id": "ea46a8fb-5a43-400e-85d6-602ef1c68c5e", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [20, -280], "parameters": {"color": 7, "width": 370, "height": 480, "content": "## 重要！ 👇\n\n**啟動前： **\n\n### 作者資訊與資源\n作者：<PERSON><PERSON><PERSON> Chang\nYouTube 頻道：[HC AI說人話](https://www.youtube.com/channel/UCvGfUB-wBdG4i_TdDGBCwJg)\n說明影片連結： https://youtu.be/PA15H5qunC0\n\n1.  **更新認證：** 確認已設定「Google Drive OAuth2 API」認證。\n2.  **設定參數：** 編輯「參數」節點，設定所需的 Google Drive 資料夾 URL。\n3.  **設定電子郵件：** 使用您的收件者電子郵件地址更新「Gmail」節點。\n\n\n參考工作流：https://n8n.io/workflows/3112-backup-n8n-workflows-to-google-drive/"}, "typeVersion": 1}, {"id": "af7f9d0e-6ce4-4277-801d-92bf05a424d6", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [-380, -280], "parameters": {"color": 7, "width": 390, "height": 480, "content": "### IMPORTANT! 👇\n\n**Before activating:**\n### Author Information and Resources\nCreator：<PERSON><PERSON><PERSON>\nYouTube 頻道：[HC HumanizeAI](www.youtube.com/@HC-HumanizeAI)\nExplanation Video Link: https://youtu.be/PA15H5qunC0\n\n1.  **Update Credentials:** Ensure 'Google Drive OAuth2 API' credentials are set up.\n2.  **Configure Parameters:** Edit the 'Parameters' node to set your desired Google Drive folder URL.\n3.  **Set Email:** Update the 'Gmail' nodes with your recipient email address.\n\n\nBase on: https://n8n.io/workflows/3112-backup-n8n-workflows-to-google-drive/"}, "typeVersion": 1}, {"id": "f24c9b0c-1bf4-40dc-9492-8c452e5d9905", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [920, 600], "parameters": {"color": 3, "width": 620, "height": 300, "content": "## 新工作流上傳\n## New Workflow upload👇"}, "typeVersion": 1}, {"id": "dac5bca9-9d7f-4131-a563-9115bf0528cc", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [920, 300], "parameters": {"color": 6, "width": 620, "height": 280, "content": "## 現有工作流更新\n## existing Workflow update👇"}, "typeVersion": 1}, {"id": "6d126534-8b9b-4935-bce7-471ecc931e83", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [140, 300], "parameters": {"width": 440, "height": 280, "content": "## 取得 Google Drive 現有的檔案資訊\n## Get Google Drive existing file info👇"}, "typeVersion": 1}, {"id": "2ed623f4-faca-40ea-9ace-ab6d0933f6ba", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [580, 300], "parameters": {"color": 4, "width": 320, "height": 280, "content": "## 確認是否為第一次備份\n## Only for initialing👇"}, "typeVersion": 1}, {"id": "46f0dbdc-4023-426c-b87b-3431817981c0", "name": "Sticky Note14", "type": "n8n-nodes-base.stickyNote", "disabled": true, "position": [-380, -380], "parameters": {"color": 6, "width": 760, "height": 80, "content": "# HC AI 說人話"}, "typeVersion": 1}], "pinData": {}, "connections": {"Limit": {"main": [[{"node": "successEmail", "type": "main", "index": 0}, {"node": "Discord", "type": "main", "index": 0}]]}, "JsonToFile": {"main": [[{"node": "Backup to Google Drive4", "type": "main", "index": 0}]]}, "Parameters": {"main": [[{"node": "getDriveFileData", "type": "main", "index": 0}]]}, "ifDriveEmpty": {"main": [[{"node": "CodeJsonToFile1", "type": "main", "index": 0}], [{"node": "firstWorkflowJson", "type": "main", "index": 0}]]}, "Workflow Data": {"main": [[{"node": "Parameters", "type": "main", "index": 0}]]}, "CodeJsonToFile1": {"main": [[{"node": "Backup to Google Drive2", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Limit", "type": "main", "index": 0}], [{"node": "Execute Workflow", "type": "main", "index": 0}]]}, "Execute Workflow": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Get all n8n Workflows", "type": "main", "index": 0}]]}, "getDriveFileData": {"main": [[{"node": "ifDriveEmpty", "type": "main", "index": 0}]]}, "firstWorkflowJson": {"main": [[{"node": "JsonToFile", "type": "main", "index": 0}]]}, "Get all n8n Workflows": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Backup to Google Drive2": {"main": [[], [{"node": "failureEmail", "type": "main", "index": 0}]]}, "Backup to Google Drive4": {"main": [[], [{"node": "failureEmail", "type": "main", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Workflow Data", "type": "main", "index": 0}]]}}}