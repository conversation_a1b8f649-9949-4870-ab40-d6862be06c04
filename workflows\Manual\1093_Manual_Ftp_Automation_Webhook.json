{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 350], "parameters": {}, "typeVersion": 1}, {"name": "FTP", "type": "n8n-nodes-base.ftp", "position": [650, 350], "parameters": {"path": "/upload/n8n_logo.png", "operation": "upload"}, "credentials": {"ftp": "ftp_creds"}, "typeVersion": 1}, {"name": "FTP1", "type": "n8n-nodes-base.ftp", "position": [850, 350], "parameters": {"path": "/upload/", "operation": "list"}, "credentials": {"ftp": "ftp_creds"}, "typeVersion": 1}, {"name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [450, 350], "parameters": {"url": "https://n8n.io/n8n-logo.png", "options": {}, "responseFormat": "file"}, "typeVersion": 1}], "connections": {"FTP": {"main": [[{"node": "FTP1", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "FTP", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}}}