{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [270, 300], "parameters": {}, "typeVersion": 1}, {"name": "Discourse", "type": "n8n-nodes-base.discourse", "position": [470, 300], "parameters": {"title": "[Created] Discourse node", "content": "Thank you <PERSON> for creating the Discourse node.", "additionalFields": {"category": 4}}, "credentials": {"discourseApi": "n8n Discourse Credentials"}, "typeVersion": 1}, {"name": "Discourse1", "type": "n8n-nodes-base.discourse", "position": [670, 300], "parameters": {"postId": "={{$json[\"id\"]}}", "content": "Thank you <PERSON> for creating the Discourse node. We can now create, update and retrieve posts using n8n.", "operation": "update", "updateFields": {}}, "credentials": {"discourseApi": "n8n Discourse Credentials"}, "typeVersion": 1}, {"name": "Discourse2", "type": "n8n-nodes-base.discourse", "position": [870, 300], "parameters": {"postId": "={{$json[\"id\"]}}", "operation": "get"}, "credentials": {"discourseApi": "n8n Discourse Credentials"}, "typeVersion": 1}], "connections": {"Discourse": {"main": [[{"node": "Discourse1", "type": "main", "index": 0}]]}, "Discourse1": {"main": [[{"node": "Discourse2", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Discourse", "type": "main", "index": 0}]]}}}