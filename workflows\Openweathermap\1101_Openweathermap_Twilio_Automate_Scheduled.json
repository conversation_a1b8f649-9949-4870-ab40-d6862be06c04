{"id": "69", "name": "Creating your first workflow", "nodes": [{"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [240, 250], "parameters": {"triggerTimes": {"item": [{}]}}, "typeVersion": 1}, {"name": "OpenWeatherMap", "type": "n8n-nodes-base.openWeatherMap", "position": [450, 250], "parameters": {"cityName": "berlin,de"}, "credentials": {"openWeatherMapApi": "Weather"}, "typeVersion": 1}, {"name": "IF", "type": "n8n-nodes-base.if", "position": [650, 250], "parameters": {"conditions": {"number": [{"value1": "={{$node[\"OpenWeatherMap\"].json[\"main\"][\"feels_like\"]}}", "value2": 18}]}}, "typeVersion": 1}, {"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.twilio", "position": [850, 150], "parameters": {"to": "", "from": "", "message": "=Wear a sweater today, it is {{$node[\"OpenWeatherMap\"].json[\"main\"][\"feels_like\"]}}°C outside right now."}, "credentials": {"twilioApi": "<PERSON><PERSON><PERSON>"}, "typeVersion": 1}, {"name": "NoOp", "type": "n8n-nodes-base.noOp", "position": [850, 350], "parameters": {}, "typeVersion": 1}], "active": true, "settings": {}, "connections": {"IF": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}], [{"node": "NoOp", "type": "main", "index": 0}]]}, "Cron": {"main": [[{"node": "OpenWeatherMap", "type": "main", "index": 0}]]}, "OpenWeatherMap": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}}}