{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "8f203423-b063-4918-a6ec-dad3ac7d1a20", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [860, -100], "webhookId": "c82193c7-163c-4556-942f-81c80037e0ea", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "d9f2e90f-128b-458b-b3cf-79db2ec08633", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1000, 100], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "4f752502-8589-4e31-bbe1-4b8395e7325a", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [1160, 100], "parameters": {}, "typeVersion": 1.3}, {"id": "61ca5a4b-3661-4330-ac4c-e09e75dd764c", "name": "Acuity Support Search API", "type": "n8n-nodes-base.httpRequest", "position": [1840, 80], "parameters": {"url": "https://2al21hjwoz-dsn.algolia.net/1/indexes/*/queries?x-algolia-agent=Algolia%20for%20JavaScript%20(3.35.1)%3B%20Browser%20(lite)%3B%20instantsearch.js%201.12.1%3B%20Zendesk%20Integration%20(2.32.0)%3B%20JS%20Helper%20(2.28.1)&x-algolia-application-id=2AL21HJWOZ&x-algolia-api-key=********************************", "method": "POST", "options": {}, "jsonBody": "={{\n{\n  \"requests\":[\n    {\n      \"indexName\":\"Zendesk 4-25\",\n      \"params\": \"query=\" + $json.query + \"&hitsPerPage=5&page=0&facets=%5B%22locale.locale%22%2C%22label_names%22%2C%22category.title%22%5D&tagFilters=&facetFilters=%5B%22locale.locale%3Aen-us%22%5D\"\n    }\n  ]\n}\n}}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "Accept-Language", "value": "en"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Origin", "value": "https://help.acuityscheduling.com"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://help.acuityscheduling.com/"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36"}, {"name": "accept", "value": "application/json"}]}}, "typeVersion": 4.2}, {"id": "8ecd6287-982c-4754-9300-4c6d54202273", "name": "Extract Relevant Fields", "type": "n8n-nodes-base.set", "position": [2560, 80], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "a6973f14-e17d-46b0-9c5b-c6d9967dbf99", "name": "title", "type": "string", "value": "={{ $json.title }}"}, {"id": "88092adb-7f63-4daa-8c7a-cbd85750e180", "name": "body", "type": "string", "value": "={{ $json.body_safe }}"}, {"id": "12718897-a73d-4c3a-bcfb-b17c890458ec", "name": "url", "type": "string", "value": "=https://help.acuityscheduling.com/hc/en-us/articles/{{ $json.id }}"}]}}, "typeVersion": 3.4}, {"id": "bf5855b2-8e73-4c29-b277-adee63e8bf59", "name": "Results to Items", "type": "n8n-nodes-base.splitOut", "position": [2360, 80], "parameters": {"options": {}, "fieldToSplitOut": "results[0].hits"}, "typeVersion": 1}, {"id": "c9329816-bbe0-4de7-b6fb-fa87783f6a5c", "name": "Has Results?", "type": "n8n-nodes-base.if", "position": [2040, 80], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "f5d7e890-f00a-4252-8588-c6662e71790c", "operator": {"type": "array", "operation": "lengthGt", "rightType": "number"}, "leftValue": "={{ $json.results[0]?.hits ?? [] }}", "rightValue": 0}]}}, "typeVersion": 2.2}, {"id": "860a178a-d500-4291-acfc-9c9f4638d6c7", "name": "Empty Response", "type": "n8n-nodes-base.set", "position": [2360, 260], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "0ce36950-83d9-4964-8763-f329a4cda5a8", "name": "response", "type": "array", "value": "[]"}]}}, "typeVersion": 3.4}, {"id": "c9f2a08b-88c2-4287-994c-f7af58e98301", "name": "Aggregate Response", "type": "n8n-nodes-base.aggregate", "position": [2760, 80], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "response"}, "typeVersion": 1}, {"id": "5f1f8874-7022-4ea1-b0a7-de42c4f800a1", "name": "Knowledgebase Tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [1320, 100], "parameters": {"name": "acuity_support_search", "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "description": "Call this tool to query AcuityScheduling's Support Center Search API.", "workflowInputs": {"value": {"query": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('query', ``, 'string') }}"}, "schema": [{"id": "query", "type": "string", "display": true, "removed": false, "required": false, "displayName": "query", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2.1}, {"id": "3913ddaa-852e-4463-a072-fe8be22bc184", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [720, -300], "parameters": {"color": 7, "width": 780, "height": 580, "content": "## 1. Simple Chatbot with Knowledgebase Tool\n[Learn more about AI agents](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.agent)\n\nThe AI agent node is the simplest and recommended way to create user-friendly chatbots in n8n. Here, we'll define a support agent which can answer AcuityScheduling.com questions. To ensure the answers are accurate and up-to-date, we'll connect it to the support knowledgebase via a custom workflow tool."}, "typeVersion": 1}, {"id": "e24d75f9-6d3c-4bca-b67f-33737ee969ee", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1540, -140], "parameters": {"color": 7, "width": 700, "height": 440, "content": "## 2. Use your Existing Help Portal Search\n[Read more about the HTTP request tool](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest)\n\nThe concept of RAG need to be synonymous with vector stores! In truth, many companies with a decent enough support website are able to leverage this existing knowledgebase for support agents. This saves time, money and effort and additional avoids maintenance of a vector store where syncs and updates are common."}, "typeVersion": 1}, {"id": "f5feebf1-fd6d-4558-a868-7ea4f852386c", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [2260, -140], "parameters": {"color": 7, "width": 720, "height": 600, "content": "## 3. Clean up the Results to Optimise Tokens\n[Read more about the aggregate node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.aggregate)\n\nOf course, the results are intended for the website format but by using the custom workflow tool, we can edit it down to suit our chat scenario and save LLM costs (in terms of tokens) whilst we're at it. "}, "typeVersion": 1}, {"id": "8132de59-9b47-460a-9cb9-f2ec83123a3f", "name": "AcuityScheduling Support Chatbot", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1060, -100], "parameters": {"options": {"systemMessage": "You are a support assistant for the SaaS company, AcuityScheduling.com. Your task is to openly help the user with any questions regarding the AcuityScheduling service however, you are restricted to only this service. If the user asks questions unrelated to AcuityScheduling, you may ask them for clarification, explain you are not able to help them out of scope or redirect <NAME_EMAIL>. Be factual in your answer, tap into the resources or tools available and do not rely on your training data (which might be out-of-date). When returning a response to the user, you are encouraged to share the URL of the knowledgebase page where the user can explore the documentation for themselves."}}, "typeVersion": 1.8}, {"id": "564bde38-25ea-4969-aa3f-bff66ec2782f", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [260, -840], "parameters": {"width": 440, "height": 1120, "content": "## Try it Out!\n### This n8n template demonstrates how you can leverage existing support site search to power your Support Chatbots and agents.\n\nBuilding a support chatbot need not be complicated! If building and indexing vector stores or duplicating data isn't necessarily your thing, an alternative implementation of the [RAG](https://www.databricks.com/glossary/retrieval-augmented-generation-rag) approach is to leverage existing knowledge-bases such as support portals.\n\n### How it works\n* A simple AI agent is connected with chat trigger to receive user queries.\n* The AI agent is instructed to fetch information from the knowledge-base via the attached custom workflow tool (aka \"knowledgebase tool\").\n* There is no step to replicate the entire support articles database into a vector store. You may choose not too because of time, cost and maintainence involved.\n* Instead, the tool leverages the existing support portal's search API to retrieve knowledge-base articles.\n* Finally, the search results are formatted before sending an aggregated response back to the agent.\n\n### How to use?\n* Customise the subworkflow to work with your own support portal API and format accordingly.\n* Try the following queries\n  * How do I connect my icloud to acuityScheduling?\n  * How do I download past invoices for my Acuity account?\n\n### Requirements\n* OpenAI for LLM.\n* If your organisation's APIs require authorisation, you may need to add custom credentials as necessary.\n\n### Customising this workflow\n* Add additional tools to reach other parts of your internal knowledgebase.\n* Not using OpenAI? Feel free to swap but ensure the LLM has tools/function calling support.\n\n\n### Need Help?\nJoin the [Discord](https://discord.com/invite/XPKeKXeB7d) or ask in the [Forum](https://community.n8n.io/)!\n\nHappy Hacking!"}, "typeVersion": 1}, {"id": "a918718f-915d-4d5c-a7c2-a015b8a84bbb", "name": "KnowledgeBase Tool Subworkflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [1620, 80], "parameters": {"workflowInputs": {"values": [{"name": "query"}]}}, "typeVersion": 1.1}], "pinData": {}, "connections": {"Has Results?": {"main": [[{"node": "Results to Items", "type": "main", "index": 0}], [{"node": "Empty Response", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AcuityScheduling Support Chatbot", "type": "ai_memory", "index": 0}]]}, "Results to Items": {"main": [[{"node": "Extract Relevant Fields", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AcuityScheduling Support Chatbot", "type": "ai_languageModel", "index": 0}]]}, "Knowledgebase Tool": {"ai_tool": [[{"node": "AcuityScheduling Support Chatbot", "type": "ai_tool", "index": 0}]]}, "Extract Relevant Fields": {"main": [[{"node": "Aggregate Response", "type": "main", "index": 0}]]}, "Acuity Support Search API": {"main": [[{"node": "Has Results?", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "AcuityScheduling Support Chatbot", "type": "main", "index": 0}]]}, "KnowledgeBase Tool Subworkflow": {"main": [[{"node": "Acuity Support Search API", "type": "main", "index": 0}]]}}}