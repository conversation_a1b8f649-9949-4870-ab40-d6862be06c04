{"meta": {"instanceId": "a2434c94d549548a685cca39cc4614698e94f527bcea84eefa363f1037ae14cd"}, "nodes": [{"id": "713d2864-efd0-4938-871e-1d37a7c58b67", "name": "On schedule", "type": "n8n-nodes-base.scheduleTrigger", "position": [1280, 840], "parameters": {"rule": {"interval": [{"field": "minutes"}]}}, "typeVersion": 1.1}, {"id": "0cedfde1-6ae1-485c-bd2c-b6114f6e4deb", "name": "Try get database page", "type": "n8n-nodes-base.notion", "position": [2160, 900], "parameters": {"filters": {"conditions": [{"key": "Event ID|rich_text", "condition": "equals", "richTextValue": "={{ $json.id }}"}]}, "options": {}, "resource": "databasePage", "operation": "getAll", "returnAll": true, "databaseId": {"__rl": true, "mode": "list", "value": "6318457d-052d-4107-9c5b-8041f530fa03", "cachedResultUrl": "https://www.notion.so/6318457d052d41079c5b8041f530fa03", "cachedResultName": "Outlook Calendar"}, "filterType": "manual"}, "credentials": {"notionApi": {"id": "18", "name": "[UPDATE ME]"}}, "typeVersion": 2, "alwaysOutputData": true}, {"id": "92ebdd55-0950-471c-aa44-2fed31b17870", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [2380, 780], "parameters": {"mode": "combine", "options": {}, "joinMode": "enrichInput1", "mergeByFields": {"values": [{"field1": "id", "field2": "property_event_id"}]}}, "typeVersion": 2.1}, {"id": "d38e4228-b3ab-443f-bfac-ffd0bc10fd08", "name": "If database page not found", "type": "n8n-nodes-base.if", "position": [2600, 840], "parameters": {"conditions": {"string": [{"value1": "={{ $json.property_event_id }}", "operation": "isEmpty"}]}}, "typeVersion": 1}, {"id": "6ef0f18c-51fe-42e7-9e42-fd6ca8564e6e", "name": "Create database page", "type": "n8n-nodes-base.notion", "position": [2820, 740], "parameters": {"title": "={{ $json.subject }}", "options": {"icon": "https://avatars.githubusercontent.com/u/45487711?s=280&v=4", "iconType": "file"}, "resource": "databasePage", "databaseId": {"__rl": true, "mode": "list", "value": "6318457d-052d-4107-9c5b-8041f530fa03", "cachedResultUrl": "https://www.notion.so/6318457d052d41079c5b8041f530fa03", "cachedResultName": "Outlook Calendar"}, "propertiesUi": {"propertyValues": [{"key": "Date|date", "range": true, "dateEnd": "={{ $json.end.dateTime }}", "timezone": "={{ $json.start.timeZone }}", "dateStart": "={{ $json.start.dateTime }}"}, {"key": "Event ID|rich_text", "textContent": "={{ $json.id }}"}, {"key": "Link|url", "urlValue": "={{ $json.webLink }}"}]}}, "credentials": {"notionApi": {"id": "18", "name": "[UPDATE ME]"}}, "typeVersion": 2}, {"id": "2d324002-348b-4f23-bffe-57f685a8a761", "name": "Update database page", "type": "n8n-nodes-base.notion", "position": [2820, 940], "parameters": {"pageId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "resource": "databasePage", "operation": "update", "propertiesUi": {"propertyValues": [{"key": "Date|date", "range": true, "dateEnd": "={{ $json.end.dateTime }}", "timezone": "={{ $json.start.timeZone }}", "dateStart": "={{ $json.start.dateTime }}"}, {"key": "Link|url", "urlValue": "={{ $json.webLink }}"}, {"key": "Name|title", "title": "={{ $json.subject }}"}]}}, "credentials": {"notionApi": {"id": "18", "name": "[UPDATE ME]"}}, "typeVersion": 2}, {"id": "ee4792c4-d71c-4fd3-a8a3-babae5ff3479", "name": "X days into the future", "type": "n8n-nodes-base.dateTime", "position": [1500, 840], "parameters": {"duration": 365, "magnitude": "={{ $json.timestamp }}", "operation": "addToDate", "outputFieldName": "Future date"}, "typeVersion": 2}, {"id": "00b53a21-97c7-4293-a5eb-8321afddd4bc", "name": "Split out items", "type": "n8n-nodes-base.itemLists", "position": [1940, 840], "parameters": {"options": {}, "fieldToSplitOut": "value"}, "typeVersion": 2.2}, {"id": "a7541bb9-0c0d-48b5-a39e-57e5681330da", "name": "Get Outlook Calendar events", "type": "n8n-nodes-base.httpRequest", "position": [1720, 840], "parameters": {"url": "https://graph.microsoft.com/v1.0/me/calendarview", "options": {}, "sendQuery": true, "authentication": "genericCredentialType", "genericAuthType": "oAuth2Api", "queryParameters": {"parameters": [{"name": "startdatetime", "value": "={{ new Date($('On schedule').item.json.timestamp).toISOString() }}"}, {"name": "enddatetime", "value": "={{ new Date($json['Future date']).toISOString() }}"}]}}, "credentials": {"oAuth2Api": {"id": "dxBfWhTrnERPMHGs", "name": "REPLACE ME"}}, "typeVersion": 4.1}], "connections": {"Merge": {"main": [[{"node": "If database page not found", "type": "main", "index": 0}]]}, "On schedule": {"main": [[{"node": "X days into the future", "type": "main", "index": 0}]]}, "Split out items": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}, {"node": "Try get database page", "type": "main", "index": 0}]]}, "Try get database page": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "X days into the future": {"main": [[{"node": "Get Outlook Calendar events", "type": "main", "index": 0}]]}, "If database page not found": {"main": [[{"node": "Create database page", "type": "main", "index": 0}], [{"node": "Update database page", "type": "main", "index": 0}]]}, "Get Outlook Calendar events": {"main": [[{"node": "Split out items", "type": "main", "index": 0}]]}}}