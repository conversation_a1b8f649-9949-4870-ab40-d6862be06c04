{"id": "5", "name": "Slack-GitHub User Info", "nodes": [{"name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [300, 300], "webhookId": "dacd64a7-a83e-4492-b8fe-363453906d0d", "parameters": {"path": "dacd64a7-a83e-4492-b8fe-363453906d0d", "options": {}, "httpMethod": "POST"}, "typeVersion": 1}, {"name": "GraphQL", "type": "n8n-nodes-base.graphql", "position": [500, 300], "parameters": {"query": "=query {\nuser(login:\"{{$node[\"Webhook\"].json[\"body\"][\"text\"]}}\"){\nname\ncompany\nlocation\navatarUrl\nemail\npullRequests(last: 25) {\nedges {\nnode {\ncommits(last:25) {\nnodes {\ncommit {\nauthor {\nemail\nname\n}\n}\n}\n}\n}\n}\n}\n}\n}", "endpoint": "https://api.github.com/graphql", "requestFormat": "json", "responseFormat": "string", "headerParametersUi": {"parameter": [{"name": "User-Agent", "value": "n8n"}, {"name": "Authorization", "value": "bearer <Personal Token>"}]}}, "typeVersion": 1}, {"name": "Function", "type": "n8n-nodes-base.function", "position": [700, 300], "parameters": {"functionCode": "let emails = [];\nlet tempEmails = [];\nconst name = $node[\"GraphQL\"].json[\"data\"][\"data\"][\"user\"][\"name\"];\nconst publicEmail = $node[\"GraphQL\"].json[\"data\"][\"data\"][\"user\"][\"email\"];\nconst username = $node[\"Webhook\"].json[\"body\"][\"text\"];\nconst nameRegex = new RegExp(name,\"g\")\n\nif(publicEmail){\n// if public email address exists, push it to the tempEmails array\n  tempEmails.push(publicEmail)\n}\n\n// looping through the pull requests\nfor(const edge of items[0].json.data.data.user.pullRequests.edges){\n // looping through the commits\n  for(node of edge.node.commits.nodes){\n\n    // Checks the name associated with the email address\n    if(nameRegex.test(node.commit.author.name)|| node.commit.author.name == username) {\n     // if name equals to contributors name or username, push the email address in tempEmails\n      tempEmails.push(node.commit.author.email)\n    }\n  }\n}\n\n// Remove duplicates\nemails = [...new Set(tempEmails)]\n\n// RegEx Pattern for email address generated by GitHub\nlet re = /^\\w+(.)*@users.noreply.github.com/\n\n// Remove the email addresses Generated by GitHub\nemails = emails.filter(email => !re.test(email))\n\n\nreturn [{json:{emails,}}]\n"}, "typeVersion": 1}, {"name": "<PERSON><PERSON>ck", "type": "n8n-nodes-base.slack", "position": [900, 300], "parameters": {"channel": "={{$node[\"Webhook\"].json[\"body\"][\"channel_id\"]}}", "attachments": [{"title": "=GitHub Details for: {{$node[\"Webhook\"].json[\"body\"][\"text\"]}}"}, {"text": "=*Name:*  {{$node[\"GraphQL\"].json[\"data\"][\"data\"][\"user\"][\"name\"]}}\n*Email:* {{$node[\"Function\"].json[\"emails\"].join(', ')}}\n*Company:* {{$node[\"GraphQL\"].json[\"data\"][\"data\"][\"user\"][\"company\"]}}\n*Location:* {{$node[\"GraphQL\"].json[\"data\"][\"data\"][\"user\"][\"location\"]}}"}, {"thumb_url": "={{$node[\"GraphQL\"].json[\"data\"][\"data\"][\"user\"][\"avatarUrl\"]}}"}], "otherOptions": {}, "authentication": "oAuth2"}, "credentials": {"slackOAuth2Api": "Slack OAuth2"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"GraphQL": {"main": [[{"node": "Function", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "GraphQL", "type": "main", "index": 0}]]}, "Function": {"main": [[{"node": "<PERSON><PERSON>ck", "type": "main", "index": 0}]]}}}