{"id": "wZBgoWrBZveMmzYi", "meta": {"instanceId": "147b9b53621dbd6fca5f762b57fc3fabf293d676d0c08463ec52c91332ab391f", "templateCredsSetupCompleted": true}, "name": "Turn YouTube Videos into Summaries, Transcripts, and Visual Insights", "tags": [], "nodes": [{"id": "61c56de7-0d8e-44fe-baf3-3e33ddd35b21", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-1340, 120], "parameters": {}, "typeVersion": 1}, {"id": "4e43030c-16cd-4b77-8c58-c3b703646a16", "name": "Set: Define Initial Variables", "type": "n8n-nodes-base.set", "position": [-840, 120], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "72590fa0-cf12-4249-80fc-7aaec9992390", "name": "automationID", "type": "string", "value": "optional-enter-reference-tracking-identifier"}, {"id": "24e9b1c3-2955-4e0b-9b4b-a6b9d046fb72", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "value": "enter-your-api-key-here"}, {"id": "b6600a42-1b8d-486a-a51d-0868bc45452e", "name": "youtubeURL", "type": "string", "value": "https://www.youtube.com/watch?v=Ovg_KfKxnC8"}, {"id": "ce9a9a40-5ae4-4106-ae61-0daba2ec185f", "name": "promptType", "type": "string", "value": "transcript"}]}}, "typeVersion": 3.4}, {"id": "add611c6-c053-464c-b12b-f0f20b4c3c4f", "name": "Switch: What kind of question do we want to ask?", "type": "n8n-nodes-base.switch", "onError": "continueRegularOutput", "position": [-200, 60], "parameters": {"rules": {"values": [{"outputKey": "Transcript", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "4ba139e4-2fd7-473f-869d-f27a1a2f3823", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.promptType.toLowerCase() }}", "rightValue": "transcript"}]}, "renameOutput": true}, {"outputKey": "Transcript with Timestamps", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "486f3c1c-7203-4bc5-a796-87939d4360c5", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.promptType.toLowerCase() }}", "rightValue": "timestamps"}]}, "renameOutput": true}, {"outputKey": "Summary", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "814597ad-12ff-450d-a4bc-cd2eb2836d8f", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.promptType.toLowerCase() }}", "rightValue": "summary"}]}, "renameOutput": true}, {"outputKey": "Scene Description", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "759debf2-3dfb-4bdd-b41f-7ef0a709e25e", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.promptType.toLowerCase() }}", "rightValue": "scene"}]}, "renameOutput": true}, {"outputKey": "Clips", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "5c66a390-2566-461d-b9bc-b0e7ebdc4af8", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.promptType.toLowerCase() }}", "rightValue": "clips"}]}, "renameOutput": true}]}, "options": {"fallbackOutput": "extra"}}, "typeVersion": 3.2, "alwaysOutputData": false}, {"id": "96b6a6c7-3b4f-412d-b200-526971782346", "name": "Set: Scene Prompt", "type": "n8n-nodes-base.set", "position": [480, 220], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "8be68e95-53df-492f-a88a-14c06f51f531", "name": "prompt", "type": "string", "value": "=Please provide a detailed description of the scene in the video, including:\n\nSetting: Where the scene takes place (e.g., indoors, outdoors, specific location). Be specific - is it a forest, a city street, a living room?\n\nObjects: Prominent objects visible in the scene (e.g., furniture, vehicles, natural elements). Include details like color, size, and material if discernible.\n\nPeople: Description of any people present, including their appearance (clothing, hair, etc.), approximate age, and any actions they are performing.\n\nLighting: The overall lighting of the scene (e.g., bright, dim, natural, artificial). Note any specific light sources (lamps, sunlight).\n\nColors: Dominant colors and color palettes used in the scene.\n\nCamera Angle/Movement: Describe the camera perspective (e.g., close-up, wide shot, aerial view) and any camera movement (panning, zooming, static).\n\nStart output directly with the response -- do not include any introductory text or explanations."}, {"id": "bfa3b421-643d-4a52-93e9-0830f794140b", "name": "model", "type": "string", "value": "gemini-1.5-flash"}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "7506b5fa-be12-4d34-8583-027bc782db0d", "name": "Set: Summarize Prompt", "type": "n8n-nodes-base.set", "position": [480, 20], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "8be68e95-53df-492f-a88a-14c06f51f531", "name": "prompt", "type": "string", "value": "=Provide a concise summary of the main points in nested bullets, using quotes only when absolutely essential for clarity. Start output directly with the response."}, {"id": "ccc44699-0918-4837-89a6-c763b157fc8c", "name": "model", "type": "string", "value": "gemini-1.5-flash"}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "2aaf482d-af31-4cd8-9014-7b77efbf3021", "name": "Set: Transcript Prompt", "type": "n8n-nodes-base.set", "position": [480, -380], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "8be68e95-53df-492f-a88a-14c06f51f531", "name": "prompt", "type": "string", "value": "=Transcribe the video. Return only the spoken dialogue, verbatim. Omit any additional text or descriptions."}, {"id": "b445c2da-934f-4a78-a57b-f383d8950f8a", "name": "model", "type": "string", "value": "gemini-1.5-flash"}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "75a6ac10-00c2-45ab-88b7-dfec3cd58bf2", "name": "Set: Fallback", "type": "n8n-nodes-base.set", "position": [480, 620], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "8be68e95-53df-492f-a88a-14c06f51f531", "name": "prompt", "type": "string", "value": "=Summarize this YouTube video with a focus on actionable insights. Use nested bullets and include relevant quotes. Specifically, highlight any recommended tools, strategies, or resources mentioned."}, {"id": "da626eb8-a097-4a65-834d-4d1709aed260", "name": "model", "type": "string", "value": "gemini-1.5-flash"}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "29ef7f9f-34c9-46f5-96f9-17b42feea381", "name": "Set: Transcript with Timestamps Prompt", "type": "n8n-nodes-base.set", "position": [480, -180], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "8be68e95-53df-492f-a88a-14c06f51f531", "name": "prompt", "type": "string", "value": "=Generate a timestamped transcript of the video. Each line must follow this format precisely:  [hh:mm:ss] Dialogue. Return only the timestamp and spoken content; omit any other text or formatting.  "}, {"id": "98ca7af8-0fbd-4ba8-8fc8-f914e5bbe48a", "name": "model", "type": "string", "value": "gemini-1.5-flash"}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "e10524ed-09dd-4c0f-86b0-9161fa347821", "name": "Set: Scene Clips", "type": "n8n-nodes-base.set", "position": [480, 420], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "8be68e95-53df-492f-a88a-14c06f51f531", "name": "prompt", "type": "string", "value": "=Extract shareable clips for social media. Each clip must include:\n\n* **Timestamp:** [hh:mm:ss]-[hh:mm:ss]\n* **Transcript:** Verbatim dialogue/text within the clip.\n* **Rationale:**  A concise explanation (under 20 words) of the clip's social media appeal (e.g., \"humorous,\" \"controversial,\" \"inspiring,\" \"informative\").  Focus on virality, engagement potential (shares, likes, comments).\n\nStart output directly with the response -- do not include any introductory text or explanations."}, {"id": "0493ffa7-4fef-4e48-b9d7-7f0891660325", "name": "model", "type": "string", "value": "gemini-1.5-flash"}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "be66d323-9035-4123-8086-dde14e528dc8", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-2340, -600], "parameters": {"width": 780, "height": 820, "content": "# How to Use This Workflow\n\nVideo Overview: [https://www.youtube.com/watch?v=Ovg_KfKxnC8](https://www.youtube.com/watch?v=Ovg_KfKxnC8)\n\nUse this workflow to analyze YouTube videos effortlessly. Start by requesting a summary or transcript—then refine the prompts to fit your needs and the type of content you're working with.\n\nEven more impressive? You can ask specific questions about what’s happening in a video—identifying people, scenes, and visual details. The ability to extract this level of insight is game-changing.\n\nThis workflow is highly adaptable—actions adjust based on the values you set. With a single flow, you can generate transcripts, create detailed YouTube descriptions, and draft summary blog posts.\n\nTriggers are flexible, too. Run it manually, connect it to a webhook, or automate it through Airtable or another system. The output can go anywhere -- Notion, Google Docs, CMS platforms -- or simply stay in n8n for on-the-fly analysis.\n\nThis workflow was created using n8n 1.82.3.\n\n## Requirements\n* [Google API key](https://console.developers.google.com/) (or obtain via [AI Studio](https://aistudio.google.com/apikey))\n\n## Testing\n* This workflow was tested on an assortment of public YouTube videos from shorts up to about an hour in length.\n\n## Future-proofing\n* This workflow was created for use with gemini-1.5-flash. In the future, the set nodes for prompts could be updated to use different models and possibly add in what API endpoint should be used in the http node.\n\n## Documentation\n* [Explore vision capabilities with the Gemini API](https://ai.google.dev/gemini-api/docs/vision?lang=python)\n* [Pricing](https://ai.google.dev/gemini-api/docs/pricing)"}, "typeVersion": 1}, {"id": "a8f5541c-d267-4998-bb31-32f2684b0874", "name": "Set Fields: Define Outcome", "type": "n8n-nodes-base.set", "position": [2800, 120], "parameters": {"include": "except", "options": {}, "assignments": {"assignments": [{"id": "300bfbe7-8d13-41ba-8828-17bba1d0eabe", "name": "answerAIGenerated", "type": "string", "value": "={{ $json.candidates?.[0]?.content?.parts?.[0]?.text ? $json.candidates[0].content.parts[0].text : ($json.error ? $json.error : \"No content or error found\") }}"}, {"id": "a54eeb5d-e4de-4fd9-a15c-be51cc414c46", "name": "promptTokenCount", "type": "string", "value": "={{ $json.usageMetadata?.promptTokenCount ? $json.usageMetadata.promptTokenCount : ($json.error ? $json.error : \"No content or error found\") }}"}, {"id": "dd590788-069e-48d9-adb2-6a5d10f8af2f", "name": "candidatesTokenCount", "type": "string", "value": "={{ $json.usageMetadata?.candidatesTokenCount ? $json.usageMetadata.candidatesTokenCount : ($json.error ? $json.error : \"No content or error found\") }}"}, {"id": "23766495-25df-4d3d-bc62-9e79a860ee19", "name": "totalTokenCount", "type": "string", "value": "={{ $json.usageMetadata?.totalTokenCount ? $json.usageMetadata.totalTokenCount : ($json.error ? $json.error : \"No content or error found\") }}"}, {"id": "cfad5ba2-a2c5-48ff-b64b-9131a51fd23c", "name": "modelVersionUsed", "type": "string", "value": "={{ $json.modelVersion ? $json.modelVersion : ($json.error ? $json.error : \"No content or error found\") }}"}]}, "excludeFields": "candidates, usageMetadata", "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "062b5d41-27bb-4e67-8ffa-6e3e392fb437", "name": "HTTP Request to Google", "type": "n8n-nodes-base.httpRequest", "onError": "continueRegularOutput", "position": [1740, 120], "parameters": {"url": "=https://generativelanguage.googleapis.com/v1beta/models/{{ $json.model }}:generateContent?key={{ $json.apiKey }}", "method": "POST", "options": {}, "jsonBody": "={\n  \"contents\": [{\n    \"parts\": [\n      { \"text\": {{ JSON.stringify($json.prompt) }} },\n      { \"file_data\": { \"file_uri\": \"{{ $json.youtubeURL }}\" } }\n    ]\n  }]\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "typeVersion": 4.2, "alwaysOutputData": true}, {"id": "0a204e60-674a-453d-81e1-9eb59a3214e2", "name": "Set: Merged Values", "type": "n8n-nodes-base.set", "position": [1380, 120], "parameters": {"options": {}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "48ed5162-61e1-4077-a13a-b2cf135fc11e", "name": "If: Was an error detected?", "type": "n8n-nodes-base.if", "disabled": true, "position": [2560, 780], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "148e9a72-f826-468c-86a3-471873717ed4", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.error }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "f9c0ef37-da99-4100-bd04-b5f2ac93a694", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-2340, 280], "parameters": {"width": 780, "height": 3580, "content": "## Prompt Inspiration Ideas\n\nUse these prompts as inspiration for your workflow. A quick way to iterate on ideas is to try them in [https://aistudio.google.com/](https://aistudio.google.com/).\n\n### 📝 Essential Video Summarizer (Quick Content Digestion)\nProvide a concise summary of the main points of this YouTube video in nested bullets, using quotes only when absolutely essential for clarity.\n\nStart output directly with the response -- do not include any introductory text or explanations.\n\n### 🚀 Complete YouTube SEO Package Generator (Content Marketing)\n\nGenerate a complete YouTube package (Title, Description, Keywords, and Timestamps) based on the following transcript:\n\n**Instructions:**\n\n* **Title:** Craft a concise and engaging title (under 60 characters) that accurately reflects the video's content and will attract viewers.  \n* **Description:** Write a detailed description (around 150-200 words) summarizing the key topics covered in the video.  This description should:\n    * Clearly outline the value proposition for the viewer (what they will learn or gain).\n    * Include relevant keywords to improve searchability.\n    * Feature a strong call to action (e.g., subscribe, visit a website, follow on social media).\n    * Optionally include a brief, intriguing hook at the beginning to grab the viewer's attention.\n* **Keywords:** Generate a list of relevant keywords and tags (around 10-15) that accurately describe the video's content and will help viewers find it in search results.  Consider:\n    * Specific topics discussed\n    * Related terms and concepts\n    * Industry jargon (if applicable)\n    * Names of people or products mentioned\n* **Timestamps:**  Generate timestamps for key topics or sections within the video. These should be formatted clearly for easy navigation.  For example:\n    * 0:00 Introduction\n    * 1:15 Topic 1\n    * 3:45 Topic 2 ...etc.  \n\n**Important Considerations:**\n\n* **Target Audience:** Assume the target audience is [Describe your target audience – e.g., beginners learning Python, experienced marketers, people interested in gardening].\n* **Overall Tone:** The tone of the entire package should be [Specify the desired tone – e.g., informative and educational, enthusiastic and engaging, humorous and lighthearted].\n* **Video's Purpose:** The primary goal of the video is to [State the video's objective – e.g., teach a skill, provide information, entertain, persuade].\n\n\n**Optional:**\n* If the video promotes a product, service, or other content, include relevant links in the description.\n* If there are any specific phrases or branding elements that should be included, specify them here.\n\nStart output directly with the response -- do not include any introductory text or explanations.\n\n### 🗺️ Structured Video Content Map (Educational Organization)\n\nSummarize the key points of this YouTube video with no introductory text. Follow this structured format:\n\nMain Topics\n* Organize content using Level 2 headers (##) based on key themes.\n\nUnder each header:\n* List only the most essential concepts using concise bullet points.\n* Ensure technical accuracy while maintaining clarity.\n\nOrganize sections in this order when applicable:\n* Overview & Context\n* Core Concepts & Features\n* How It Works\n* Benefits & Drawbacks\n\nFormatting Rules:\n* Use Markdown for structured readability.\n* Keep bullet points simple (avoid nesting).\n* No summaries, conclusions, or extra explanations -- just structured content.\n\n### 🎯 Goal-Oriented Learning Digest (Personal Development)\n\nI'm trying to learn about [Specific Goal, e.g., \"how to improve my public speaking skills,\" \"the latest trends in web development\"]. Summarize the relevant information using nested bullets and quotes, focusing on how it helps me achieve this goal.\n\nStart output directly with the response -- do not include any introductory text or explanations.\n\n### ⚙️ Actionable Insights Framework (Implementation Planning)\n\nExtract actionable insights with a focus on practical applications and recommendations.\n\nStart output directly with the response—do not include any introductory text or explanations.\n\nFormat the summary using markdown and follow this structure:\n\nProblem Statement\n* Summarize the core issue or topic addressed.\n\nKey Solutions & Recommendations\n* Use separate ## headers for each major solution or recommendation.\n\nImplementation Steps:\n* Under each solution, list key steps using bullet points.\n\nExpected Outcomes & Benefits:\n* Describe the potential impact and advantages of each solution.\n\nResources & Tools\n* List any relevant tools, frameworks, or resources mentioned.\n\n### 💡 Concept Extractor with Supporting Quotes (Deep Content Analysis)\n\nExtract the core concepts presented using nested bullets. Include supporting quotes for each concept. Focus on [Specific Topic/Area, e.g., \"the impact of AI on marketing,\" \"the principles of effective communication,\" etc.].\n\nStart output directly with the response -- do not include any introductory text or explanations.\n\n\n### 👥 Audience-Targeted Video Analysis (Professional Development)\n\nAnalyze this video for a target audience of [Specify Target Audience, e.g., \"software engineers,\" \"marketing professionals,\" \"general readers\"].\n\nFormat the summary using markdown and structure it as follows:\n\nMain Topics\nOrganize key points under Level 2 headers (##), focusing on essential insights.\nSuggested sections (adjust as needed):\nOverview (context & background)\nKey Concepts & Features\nHow It Works (technical or practical explanation)\nBenefits & Challenges\nKey Instructions:\n\nTailor content to the [Target Audience] and their expected level of expertise.\nUse bullet points for clarity and conciseness.\nBold key terms to emphasize important ideas.\nEnsure technical accuracy, simplifying complex ideas only when needed.\nUse tables when making comparisons.\nLength Guidance:\n\nSummarize in approximately [Specify Desired Length, e.g., \"200 words,\" \"500 words,\" \"one page\"].\n\nStart output directly with the response -- do not include any introductory text or explanations.\n\n### 🧠 Knowledge Extension Analyzer (Advanced Learning & Analysis)\n\nAssuming I already understand [briefly state relevant background knowledge], provide specific insights that build upon this knowledge. Use nested bullets and relevant quotes.\n\nStart output directly with the response -- do not include any introductory text or explanations.\n\n### 🔍 Argument Analysis Blueprint (Critical Thinking)\n\nAnalyze the argument presented in this video. Outline the main claims and supporting evidence using nested bullets. Include direct quotes to illustrate key points. Identify any potential counterarguments or weaknesses in the reasoning.\n\nStart output directly with the response -- do not include any introductory text or explanations.\n\n### ⚖️ YouTube Argument Analyzer (Debate & Rhetoric)\n\nAnalyze the argument presented in this YouTube video. Outline the main claims and supporting evidence using nested bullets. Include direct quotes to illustrate key points. Identify any potential counterarguments or weaknesses in the reasoning.\n\nStart output directly with the response -- do not include any introductory text or explanations.\n\n### 🎭 Rhetorical Technique Evaluator (Communication Analysis)\n\nAnalyze the speaker's [e.g., presentation style, persuasive techniques, use of rhetoric]. Use nested bullets and include specific quotes to illustrate your observations. Focus on how these techniques contribute to (or detract from) the video's overall message.\n\nStart output directly with the response -- do not include any introductory text or explanations.\n\n"}, "typeVersion": 1}, {"id": "d15f5fa4-731d-4d63-a065-e81a5248e8d8", "name": "Code: Merge data from prior nodes with HTTP Output", "type": "n8n-nodes-base.code", "position": [2340, 120], "parameters": {"mode": "runOnceForEachItem", "jsCode": "return {\n  json: {\n    ...$json, // Keep data from http request\n    ...$('Set: Merged Values').item.json, // Keep data from before http request\n  }\n};\n\n\n\n"}, "typeVersion": 2}, {"id": "eb3b45bc-c59e-45f7-a352-97f62ae079f2", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-1080, -80], "parameters": {"width": 560, "height": 520, "content": "## ✏️ Set Values For Use in Flow\n1. automationID: Use this to reference the automation. Useful for troubleshooting when you have lots of flows running.\n2. apiKey: API key from Google\n3. youtubeURL: Public URL for video to be processed\n4. promptType: Used by switch node and determines which prompt is sent as part of API call."}, "typeVersion": 1}, {"id": "440539da-e658-404f-8f85-9e28e9acffc9", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-460, -80], "parameters": {"width": 560, "height": 520, "content": "## ℹ️ Determine which prompt will be passed in API call based on promptType value\n"}, "typeVersion": 1}, {"id": "ba3f3e35-d691-449e-9f62-d2246223ff5e", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [280, -580], "parameters": {"width": 560, "height": 1520, "content": "## ✏️ Set Values For Prompts & Model\n1. prompt: What do you want to know about a video\n2. model: Which model to use (gemini-1.5-flash)"}, "typeVersion": 1}, {"id": "b33de065-16d5-4b29-8117-91cfd9d9034a", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [1300, -60], "parameters": {"width": 260, "height": 520, "content": "## ℹ️ Making it easier to reference values in the http node\n"}, "typeVersion": 1}, {"id": "eb6788d8-4b99-42e5-b0c1-0430a8d786e6", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [2420, 580], "parameters": {"width": 560, "height": 520, "content": "## ℹ️ If you want to add special processing when errors occur (Optional)\n\n"}, "typeVersion": 1}, {"id": "432ef0bf-8c85-4f72-a771-42b3b2173094", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [1680, -60], "parameters": {"width": 260, "height": 520, "content": "## ℹ️ Makes call to Google endpoint using values set in earlier nodes\n\n"}, "typeVersion": 1}, {"id": "57f29827-7f1f-4b73-b700-51af8bd1e582", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [2240, -60], "parameters": {"width": 300, "height": 520, "content": "## ℹ️ Merges data from returned by Google with values set in prior nodes so that earlier data isn't lost"}, "typeVersion": 1}, {"id": "c42daf8b-2d9e-4258-91be-97b830c4eff4", "name": "Sticky Note14", "type": "n8n-nodes-base.stickyNote", "position": [2700, -60], "parameters": {"width": 300, "height": 520, "content": "## ℹ️ Gives returned data meaningful names; Simplifies amount of data available to follow-on nodes"}, "typeVersion": 1}, {"id": "fb246216-645d-4c01-adc7-9b2be2920bcd", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1460, 280], "parameters": {"width": 360, "content": "#### 💡 Trigger Ideas\nYou can change the trigger to meet your use case. If you need to run it once in awhile, can leave in current format. Try one of n8n's YouTube nodes, a form, a webhook, etc. for running more frequently as part of an automation."}, "typeVersion": 1}, {"id": "ba228de2-c2de-475c-95c3-20ef712edcc9", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [3120, 100], "parameters": {"width": 360, "height": 240, "content": "#### 💡 Next Step Ideas\nUse a webhook to send data to another destination or use one of n8n's other nodes to send to Airtable, Notion, etc."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "5d83e29e-caf0-4c3a-be04-9da966904a5a", "connections": {"Set: Fallback": {"main": [[{"node": "Set: Merged Values", "type": "main", "index": 0}]]}, "Set: Scene Clips": {"main": [[{"node": "Set: Merged Values", "type": "main", "index": 0}]]}, "Set: Scene Prompt": {"main": [[{"node": "Set: Merged Values", "type": "main", "index": 0}]]}, "Set: Merged Values": {"main": [[{"node": "HTTP Request to Google", "type": "main", "index": 0}]]}, "Set: Summarize Prompt": {"main": [[{"node": "Set: Merged Values", "type": "main", "index": 0}]]}, "HTTP Request to Google": {"main": [[{"node": "Code: Merge data from prior nodes with HTTP Output", "type": "main", "index": 0}, {"node": "If: Was an error detected?", "type": "main", "index": 0}]]}, "Set: Transcript Prompt": {"main": [[{"node": "Set: Merged Values", "type": "main", "index": 0}]]}, "Set: Define Initial Variables": {"main": [[{"node": "Switch: What kind of question do we want to ask?", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set: Define Initial Variables", "type": "main", "index": 0}]]}, "Set: Transcript with Timestamps Prompt": {"main": [[{"node": "Set: Merged Values", "type": "main", "index": 0}]]}, "Switch: What kind of question do we want to ask?": {"main": [[{"node": "Set: Transcript Prompt", "type": "main", "index": 0}], [{"node": "Set: Transcript with Timestamps Prompt", "type": "main", "index": 0}], [{"node": "Set: Summarize Prompt", "type": "main", "index": 0}], [{"node": "Set: Scene Prompt", "type": "main", "index": 0}], [{"node": "Set: Scene Clips", "type": "main", "index": 0}], [{"node": "Set: Fallback", "type": "main", "index": 0}]]}, "Code: Merge data from prior nodes with HTTP Output": {"main": [[{"node": "Set Fields: Define Outcome", "type": "main", "index": 0}]]}}}