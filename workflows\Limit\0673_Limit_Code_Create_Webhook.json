{"meta": {"instanceId": "03e9d14e9196363fe7191ce21dc0bb17387a6e755dcc9acc4f5904752919dca8"}, "nodes": [{"id": "363be6de-5e8d-46b2-a31f-6f7bc204c986", "name": "Trigger on New Email", "type": "n8n-nodes-base.microsoftOutlookTrigger", "disabled": true, "position": [-760, 1400], "parameters": {"output": "raw", "filters": {"foldersToInclude": ["AQMkADM5MWVmZWEwLTQ4OTMtNGMyYy1iOWUxLTQ4N2M1YmU0ODJjNQAuAAADWJOZOf0oRkGpsGIMN2VBCwEAbttrza1gUEiEMFJJPqIeZQAAAgEMAAAA"]}, "options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}}, "credentials": {"microsoftOutlookOAuth2Api": {"id": "vTCK0oVQ0WjFrI5H", "name": "Outlook Credential"}}, "typeVersion": 1}, {"id": "0da8b1ab-6dbe-41b7-92f1-6e8637d082cb", "name": "Retrieve Headers of Email", "type": "n8n-nodes-base.httpRequest", "position": [-560, 1400], "parameters": {"url": "=https://graph.microsoft.com/v1.0/me/messages/{{ $json.id }}?$select=internetMessageHeaders", "options": {}, "sendHeaders": true, "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}]}, "nodeCredentialType": "microsoftOutlookOAuth2Api"}, "credentials": {"microsoftOutlookOAuth2Api": {"id": "vTCK0oVQ0WjFrI5H", "name": "Outlook Credential"}}, "typeVersion": 4.2}, {"id": "2f28e47d-f010-4f0b-bbe4-074bbdf39a45", "name": "Extract Received Headers", "type": "n8n-nodes-base.code", "position": [80, 1460], "parameters": {"jsCode": "// Extract the headers array from the JSON\nconst headers = $('Set Headers').item.json.headers;\n\n// Filter headers where the name is \"Received\"\nconst receivedHeaders = headers.filter(header => header.name === \"Received\");\n\n// Return the filtered headers\nreturn receivedHeaders;\n"}, "executeOnce": false, "typeVersion": 2}, {"id": "530fd9c3-94c2-4d5e-a686-57738cc10ae6", "name": "Remove Extra Received Headers", "type": "n8n-nodes-base.limit", "position": [300, 1460], "parameters": {"keep": "lastItems"}, "typeVersion": 1}, {"id": "9942704e-e0ac-42e9-b714-b2bdb3117c02", "name": "Extract Original From IP", "type": "n8n-nodes-base.set", "position": [500, 1460], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "5f740d1f-de62-4fe0-aa20-625063344c07", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "value": "={{ $json.value.replace(/\\b(127\\.(?:\\d{1,3}\\.){2}\\d{1,3})|(10\\.(?:\\d{1,3}\\.){2}\\d{1,3})|(172\\.(?:1[6-9]|2[0-9]|3[0-1])\\.\\d{1,3}\\.\\d{1,3})|(192\\.168\\.\\d{1,3}\\.\\d{1,3})\\b/g, \"\").match(/(\\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])(\\.(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])(\\.(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])(\\.(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])(\\.(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])(\\.(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])(\\.(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])(\\.(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])){3}))|:)))(%.+)?\\s*)|(\\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)[.]){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\b)/)[0] }}"}]}}, "typeVersion": 3.4}, {"id": "6093bcd2-1101-4685-8d2c-751dd451afc4", "name": "Query IP Quality Score API", "type": "n8n-nodes-base.httpRequest", "position": [980, 1360], "parameters": {"url": "=https://ipqualityscore.com/api/json/ip/********************************/{{ $('Extract Original From IP').item.json.extractedfromip }}?strictness=1&allow_public_access_points=true&lighter_penalties=true", "options": {}}, "typeVersion": 4.2}, {"id": "feb4203c-4f9b-456c-9640-82ce8f6f550f", "name": "Query IP API", "type": "n8n-nodes-base.httpRequest", "position": [1180, 1360], "parameters": {"url": "=http://ip-api.com/json/{{ $('Extract Original From IP').item.json.extractedfromip }}", "options": {}}, "typeVersion": 4.2}, {"id": "f628e421-4cb5-4612-83c2-bde0f4f57367", "name": "Authentication-Results Header?", "type": "n8n-nodes-base.if", "position": [1440, 1600], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "ead2b640-ad80-4189-a692-ae454723fd85", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $('Set Headers').item.json.headers.some(header => header.name === \"Authentication-Results\") }}", "rightValue": "true"}]}}, "typeVersion": 2.2}, {"id": "8616ecd3-1c71-49ff-a32c-4b09f3214edb", "name": "Extract Authentication-Results Header", "type": "n8n-nodes-base.code", "position": [1720, 1360], "parameters": {"jsCode": "// Extract the headers array from the JSON\nconst headers = $('Set Headers').item.json.headers;\n\n// Filter headers where the name is \"Received\"\nconst receivedHeaders = headers.filter(header => header.name === \"Authentication-Results\");\n\n// Return the filtered headers\nreturn receivedHeaders;\n"}, "executeOnce": false, "typeVersion": 2}, {"id": "7d3a37dc-6bbe-4c3b-9c2c-c9d2c1c24213", "name": "Received-<PERSON><PERSON>?", "type": "n8n-nodes-base.if", "position": [1700, 2220], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a38ebc9b-f896-4432-81fb-4f3db98f3409", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $('Set Headers').item.json.headers.some(header => header.name === \"Received-SPF\") }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "f1ca55fb-07d8-4825-8850-f5a3c58e358a", "name": "DKIM-Signature Header?", "type": "n8n-nodes-base.if", "position": [1700, 2620], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a38ebc9b-f896-4432-81fb-4f3db98f3409", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $('Set Headers').item.json.headers.some(header => header.name === \"DKIM-Signature\") }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "df19f38c-b263-4b97-bd22-adc8ff44f631", "name": "Set SPF Value", "type": "n8n-nodes-base.set", "position": [2480, 2140], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "179c48eb-97e5-48ab-82b8-ef4269f11366", "name": "spfvalue", "type": "string", "value": "={{ $json.data.last().value.toLowerCase().includes('fail') ? \"fail\" : $json.data.last().value.toLowerCase().includes('pass') ? \"pass\" : \"unknown\"}}"}]}}, "typeVersion": 3.4}, {"id": "1613d276-3ec4-44b2-91ca-f76985e1b4c2", "name": "Extract Received-<PERSON><PERSON>", "type": "n8n-nodes-base.code", "position": [1940, 2140], "parameters": {"jsCode": "// Extract the headers array from the JSON\nconst headers = $('Set Headers').item.json.headers;\n\n// Filter headers where the name is \"Received\"\nconst receivedHeaders = headers.filter(header => header.name === \"Received-SPF\");\n\n// Return the filtered headers\nreturn receivedHeaders;\n"}, "executeOnce": false, "typeVersion": 2}, {"id": "47697f60-99e7-4c91-ab7c-7f966b1b5307", "name": "DKIM Signature Found", "type": "n8n-nodes-base.set", "position": [2480, 2520], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "ae3158bf-3d91-4a61-a58c-c151362e52d7", "name": "dkimvalue", "type": "string", "value": "=found"}]}}, "typeVersion": 3.4}, {"id": "2383e7b4-fe13-4c36-80a3-67ba3f02ce1d", "name": "DMARC Header?", "type": "n8n-nodes-base.if", "position": [1700, 3060], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a38ebc9b-f896-4432-81fb-4f3db98f3409", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $('Set Headers').item.json.headers.some(header => header.name === \"dmarc\") }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "2c41e06d-0dc1-474e-a13f-302fc3e4d4ad", "name": "No DMARC Header", "type": "n8n-nodes-base.set", "position": [2480, 3160], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "ae3158bf-3d91-4a61-a58c-c151362e52d7", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "string", "value": "=not found"}]}}, "typeVersion": 3.4}, {"id": "81bd5082-634b-4f0e-951f-1374573fc6c0", "name": "Extract DMARC Header", "type": "n8n-nodes-base.code", "position": [2120, 2960], "parameters": {"jsCode": "// Extract the headers array from the JSON\nconst headers = $('Set Headers').item.json.headers;\n\n// Filter headers where the name is \"Received\"\nconst receivedHeaders = headers.filter(header => header.name === \"dmarc\");\n\n// Return the filtered headers\nreturn receivedHeaders;\n"}, "executeOnce": false, "typeVersion": 2}, {"id": "55a5745c-2c73-492c-b63c-20936043b0b6", "name": "Set DMARC Value", "type": "n8n-nodes-base.set", "position": [2480, 2960], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "179c48eb-97e5-48ab-82b8-ef4269f11366", "name": "spfvalue", "type": "string", "value": "={{ $json.value.toLowerCase().includes('pass') ? \"pass\" : $json.value.toLowerCase().includes('fail') ? \"fail\" : \"unknown\"}}"}]}}, "typeVersion": 3.4}, {"id": "48a5b283-7aa4-4e10-b784-fcce25465fc0", "name": "Original IP Found?", "type": "n8n-nodes-base.if", "position": [700, 1460], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "1c27e7ba-d243-4673-b1cc-608c35951168", "operator": {"type": "boolean", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.extractedfromip?.toBoolean() }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "75818bdc-3ffb-42a7-a0a3-93fc413b757f", "name": "No DKIM Signature Found", "type": "n8n-nodes-base.set", "position": [2480, 2720], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "ae3158bf-3d91-4a61-a58c-c151362e52d7", "name": "dkimvalue", "type": "string", "value": "not found"}]}}, "typeVersion": 3.4}, {"id": "17bc160b-618f-4893-80c8-4e4c2638adc3", "name": "Determine Auth Values", "type": "n8n-nodes-base.set", "position": [2040, 1360], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "cd0b3f49-fe38-4686-a1f5-bc03a145adef", "name": "spfvalue", "type": "string", "value": "={{ $json.value.toLowerCase().includes('spf=pass') ? \"pass\" : $json.value.toLowerCase().includes('spf=fail') ? \"fail\" : $json.value.toLowerCase().includes('spf=neutral') ? \"neutral\" : \"unknown\" }}"}, {"id": "6aa90f4d-773e-475f-8cbc-fe5c4fe93653", "name": "dkimvalue", "type": "string", "value": "={{ $json.value.toLowerCase().includes('dkim=pass') ? \"pass\" : $json.value.toLowerCase().includes('dkim=fail') ? \"fail\" : $json.value.toLowerCase().includes('dkim=temperror') ? \"error\" : \"unknown\" }}"}, {"id": "d3b7b0c1-0680-4cb9-b376-d365e5602a29", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "string", "value": "={{ $json.value.toLowerCase().includes('dmarc=pass') ? \"pass\" : $json.value.toLowerCase().includes('dmarc=fail') ? \"fail\" : \"unknown\" }}"}]}}, "typeVersion": 3.4}, {"id": "8ee70aff-0907-44f5-b675-1de26660c2e3", "name": "No SPF Found", "type": "n8n-nodes-base.set", "position": [2480, 2320], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "ae3158bf-3d91-4a61-a58c-c151362e52d7", "name": "spfvalue", "type": "string", "value": "not found"}]}}, "typeVersion": 3.4}, {"id": "a658b7d1-ec0e-40c9-a6c6-1f81e776fcfb", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [2840, 1600], "parameters": {"numberInputs": 3}, "typeVersion": 3}, {"id": "bb688aec-d7ae-4e5a-ac38-a8d9554966bd", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "position": [3000, 1600], "parameters": {"options": {}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}, {"id": "e393c3b1-b756-44a8-ac3c-b2d9e15f4f47", "name": "No Operation, do nothing", "type": "n8n-nodes-base.noOp", "position": [980, 1600], "parameters": {}, "typeVersion": 1}, {"id": "d651412c-9e58-4ef6-a6eb-6556647a7223", "name": "Format Webhook Output", "type": "n8n-nodes-base.set", "position": [3400, 1460], "parameters": {"options": {}, "assignments": {"assignments": []}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "03c70339-8e92-4d62-b346-7e669c83d338", "name": "Format Individual Auth Outputs", "type": "n8n-nodes-base.set", "position": [3180, 1600], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "1f466a9d-e8a1-4095-918c-89fd8e3dae57", "name": "spf", "type": "string", "value": "={{ $json.data[0].spfvalue }}"}, {"id": "797b0e35-9a2e-4261-8741-a8d636e0d1ae", "name": "dkim", "type": "string", "value": "={{ $json.data[1].dkimvalue }}"}, {"id": "8b6f9dda-081d-45b6-98a9-04a96642800b", "name": "dmarc", "type": "string", "value": "={{ $json.data[2].dmarcvalue }}"}, {"id": "6d24a794-0d06-4f12-8bfb-cc3c71720a1b", "name": "initialIP", "type": "string", "value": "={{ $('Extract Original From IP').item.json.extractedfromip || 'Originating IP Not Found'}}"}, {"id": "e9ec6f54-0ef7-451b-bbeb-8bb9291e4bcd", "name": "organization", "type": "string", "value": "={{ $('Query IP API').item.json.org || \"No Organization Found\" }}"}, {"id": "719b8414-72e1-4916-855b-00abdfc8e776", "name": "country", "type": "string", "value": "={{ $('Query IP API').item.json.country || \"No Country Found\" }}"}, {"id": "ab0dc08c-ba54-4e2c-b4df-9f23d36cb350", "name": "city", "type": "string", "value": "={{ $('Query IP API').item.json.city || \"No City Found\" }}"}, {"id": "f8214eea-dfb6-4fe1-8e45-e0b8d3d44ee3", "name": "recentSpamActivity", "type": "string", "value": "={{ $('Query IP Quality Score API').item.json.fraud_score>=85 ? \"Identified spam in the last 48 hours\" : $('Query IP Quality Score API').item.json.fraud_score>=75 ? \"Identified spam in the last month\" : \"Not associated with recent spam\" }}"}, {"id": "fe3488b2-ad00-45ad-b947-ca2dc4242363", "name": "ipSenderReputation", "type": "string", "value": "={{ $('Query IP Quality Score API').item.json.fraud_score>=85 ? \"Bad\" : $('Query IP Quality Score API').item.json.fraud_score>=75 ? \"Poor\" : $('Gmail - Query IP Quality Score API').item.json.fraud_score>=50 ? \"Suspicious\" : $('Query IP Quality Score API').item.json.fraud_score>=11 ? \"OK\" : $('Query IP Quality Score API').item.json.fraud_score<11 ? \"Good\" : \"Unknown\"}}"}]}}, "typeVersion": 3.4}, {"id": "762153b7-0364-498f-9dba-547d676b9d74", "name": "Format Combined Auth Output", "type": "n8n-nodes-base.set", "position": [2400, 1360], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "1f466a9d-e8a1-4095-918c-89fd8e3dae57", "name": "spf", "type": "string", "value": "={{ $json.spfvalue }}"}, {"id": "797b0e35-9a2e-4261-8741-a8d636e0d1ae", "name": "dkim", "type": "string", "value": "={{ $json.dkimvalue }}"}, {"id": "8b6f9dda-081d-45b6-98a9-04a96642800b", "name": "dmarc", "type": "string", "value": "={{ $json.dmarcvalue }}"}, {"id": "6d24a794-0d06-4f12-8bfb-cc3c71720a1b", "name": "initialIP", "type": "string", "value": "={{ $('Extract Original From IP').item.json.extractedfromip || 'Originating IP Not Found'}}"}, {"id": "e9ec6f54-0ef7-451b-bbeb-8bb9291e4bcd", "name": "organization", "type": "string", "value": "={{ $('Query IP API').item.json.org || \"No Organization Found\" }}"}, {"id": "719b8414-72e1-4916-855b-00abdfc8e776", "name": "country", "type": "string", "value": "={{ $('Query IP API').item.json.country || \"No Country Found\" }}"}, {"id": "ab0dc08c-ba54-4e2c-b4df-9f23d36cb350", "name": "city", "type": "string", "value": "={{ $('Query IP API').item.json.city || \"No City Found\" }}"}, {"id": "f8214eea-dfb6-4fe1-8e45-e0b8d3d44ee3", "name": "recentSpamActivity", "type": "string", "value": "={{ $('Query IP Quality Score API').item.json.fraud_score>=85 ? \"Identified spam in the last 48 hours\" : $('Query IP Quality Score API').item.json.fraud_score>=75 ? \"Identified spam in the last month\" : \"Not associated with recent spam\" }}"}, {"id": "fe3488b2-ad00-45ad-b947-ca2dc4242363", "name": "ipSenderReputation", "type": "string", "value": "={{ $('Query IP Quality Score API').item.json.fraud_score>=85 ? \"Bad\" : $('Query IP Quality Score API').item.json.fraud_score>=75 ? \"Poor\" : $('Query IP Quality Score API').item.json.fraud_score>=50 ? \"Suspicious\" : $('Query IP Quality Score API').item.json.fraud_score>=11 ? \"OK\" : $('Query IP Quality Score API').item.json.fraud_score<11 ? \"Good\" : \"Unknown\"}}"}]}}, "typeVersion": 3.4}, {"id": "391615b6-4996-4687-a07c-3f9af1246840", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [3620, 1460], "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "ff28eb77-d095-440e-a95f-9f3727a3c219", "name": "Webhook1", "type": "n8n-nodes-base.webhook", "position": [-780, 2140], "webhookId": "da28e0c6-ebe2-43e7-92fe-dde3278746a9", "parameters": {"path": "da28e0c6-ebe2-43e7-92fe-dde3278746a8", "options": {}, "httpMethod": "POST", "responseMode": "responseNode"}, "typeVersion": 2}, {"id": "80d4ce98-c26b-4f14-9058-6dda098f4f14", "name": "Set Headers", "type": "n8n-nodes-base.set", "position": [-100, 1460], "parameters": {"options": {}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "fddadcd8-ecaf-4fb3-bd38-12d6e48124be", "name": "Aggregate Received-SPF Headers", "type": "n8n-nodes-base.aggregate", "position": [2140, 2140], "parameters": {"options": {}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}, {"id": "175f81f1-f5ff-4170-9496-7adae5351ff4", "name": "Set Headers Here", "type": "n8n-nodes-base.set", "position": [-360, 1400], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "5bf15ec1-a009-4473-a3da-fca15a6cd29a", "name": "headers", "type": "array", "value": "={{ $json.internetMessageHeaders }}"}]}}, "typeVersion": 3.4}, {"id": "6aa1040e-1c57-4ef3-9a06-9e25ca66247f", "name": "Set Webhook Headers Here", "type": "n8n-nodes-base.set", "position": [-380, 2140], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "80d3bf91-ce79-44b7-b8d6-a612ef810891", "name": "headers", "type": "array", "value": "={{ $json.body.headers }}"}]}}, "typeVersion": 3.4}, {"id": "6d177ff6-333f-40af-87ee-28f5808b90b6", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-840, 849.3566000559811], "parameters": {"color": 7, "width": 635.*************, "height": 738.7992581051316, "content": "![](https://uploads.n8n.io/templates/outlook.png)\n## **Testing Email Header Analysis Workflow**\n\nThis section of the workflow is designed for testing purposes to ensure that the setup functions correctly with your Outlook email client before deploying it as an API for third-party platforms. The process begins with the `Trigger on New Email` node, which monitors a specified folder in your Outlook mailbox and triggers the workflow whenever a new email arrives. Configured to poll every minute, it ensures timely detection and processing of incoming emails.\n\nOnce an email is detected, the `Retrieve Headers of Email` node uses the Microsoft Graph API to fetch the detailed headers of the new email. These headers contain critical metadata, such as routing information and authentication results, essential for the analysis of the email's origin and legitimacy.\n\nFinally, the `Set Headers Here` node extracts and organizes the email headers into a standardized format as an array called `headers`. This structured format prepares the email data for further processing in the subsequent sections of the workflow. By validating these steps, you can confirm the workflow is functioning correctly before integrating it into broader use cases."}, "typeVersion": 1}, {"id": "4347e3ac-6268-4f47-9ffa-d6cfdb9db6fe", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-840, 1597.*************], "parameters": {"color": 7, "width": 635.*************, "height": 722.************, "content": "![](https://uploads.n8n.io/templates/n8n.png)\n## **Webhook Integration for Production**\n\nThis section transitions the workflow into production, enabling it to function as an API for analyzing email headers received from third-party platforms. To utilize this webhook functionality, it is essential to **activate the workflow**, as the webhook will only respond when the workflow is live.\n\nThe `Webhook1` node listens for incoming HTTP POST requests at the specified path. When the webhook is triggered, it receives and processes the payload containing email data, including headers sent by the third-party platform. This enables the workflow to operate dynamically with external systems.\n\nThe `Set Webhook Headers Here` node takes the received email data and extracts the `headers` array from the payload's body. This ensures the incoming data is formatted correctly and ready for further processing in subsequent steps of the workflow.\n\nBy activating the workflow and integrating it with external systems, users can automate the analysis of email headers seamlessly in a production environment."}, "typeVersion": 1}, {"id": "166afae1-13f7-4c61-b605-751e2692f272", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-195.35026277953466, 1001.1991904481583], "parameters": {"color": 7, "width": 869.3564073187465, "height": 626.9566677129526, "content": "![](https://uploads.n8n.io/templates/n8n.png)\n## **Extract and Process Email Headers**\n\nThis section processes the headers from incoming email data to extract critical information, particularly focusing on the originating IP address. The workflow begins with the `Set Headers` node, which takes the headers provided from the previous nodes and prepares them for analysis.\n\nThe `Extract Received Headers` node filters through the headers and isolates those labeled as \"Received.\" These headers document the servers through which the email has passed, providing a traceable path of its journey. Next, the `Remove Extra Received Headers` node narrows the focus to the most recent \"Received\" header, which typically contains the originating IP address of the email sender.\n\nUsing the `Extract Original From IP` node, the workflow applies a regular expression to extract the IP address from the retained header, removing any internal or private IP addresses that might be present. This ensures that only the relevant external IP address is identified."}, "typeVersion": 1}, {"id": "a676cc11-c48d-4160-a60f-5a2cce1ecc94", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [686.9090848322476, 800.8639469405958], "parameters": {"color": 7, "width": 922.1859426288208, "height": 965.2875565450952, "content": "![](https://uploads.n8n.io/templates/ipqualityscoretemplate.png)\n## **Analyze IP Address and Check Authentication Results**\n\nThis section focuses on analyzing the originating IP address and verifying the presence of essential email authentication headers. The workflow begins with the `Original IP Found?` node, which evaluates whether the extracted IP address is valid and non-empty. If a valid IP address is found, the workflow proceeds; otherwise, it triggers the `No Operation, do nothing` node to halt further processing.\n\nThe `Query IP Quality Score API` node interacts with the IP Quality Score service, evaluating the IP’s reputation. This analysis identifies whether the IP is associated with spam, fraud, or other malicious activities. The results help determine the sender's trustworthiness.\n\nNext, the `Query IP API` node provides additional contextual information about the IP address, including geographical details (e.g., country, city) and the organization associated with the IP. This information enriches the analysis, offering insights into the sender’s origin.\n\nThe `Authentication-Results Header?` node checks for the presence of the \"Authentication-Results\" header in the email. This header indicates the results of SPF, DKIM, and DMARC checks performed by the receiving email server. If present, the workflow proceeds to analyze the header further in subsequent sections.\n\nBy validating the IP address and analyzing its reputation, this section ensures a comprehensive understanding of the email's legitimacy before moving forward in the workflow."}, "typeVersion": 1}, {"id": "999b7855-b515-45b7-a560-55882555a2c2", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1622.1779104636253, 911.7549500344078], "parameters": {"color": 7, "width": 1016.1357697283069, "height": 619.3441192962306, "content": "![](https://uploads.n8n.io/templates/n8n.png)\n## **Extract and Evaluate Authentication Results**\n\nIf the header is found, the workflow proceeds to the `Extract Authentication-Results Header` node, which isolates the relevant header and extracts its contents. This allows the workflow to parse the authentication results systematically.\n\nNext, the `Determine Auth Values` node processes the extracted data, determining the status of SPF, DKIM, and DMARC. It categorizes each result as `pass`, `fail`, `neutral`, `error`, or `unknown` based on the information present in the header. This step ensures a clear understanding of the email's adherence to authentication protocols.\n\nFinally, the `Format Combined Auth Output` node aggregates the authentication results with other relevant metadata, such as the originating IP, sender's organization, and geographical location, obtained from previous steps. Additionally, it evaluates the IP's reputation and recent spam activity using the data from the IP Quality Score API. This structured output provides a comprehensive overview of the email's security and legitimacy, making it ready for integration with external systems or reporting tools."}, "typeVersion": 1}, {"id": "f2fefb66-8325-4d00-932b-292b353f7b2f", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [2660, 890.7472796133279], "parameters": {"color": 7, "width": 1285.8545784346588, "height": 909.4741259295762, "content": "![](https://uploads.n8n.io/templates/n8n.png)\n## **Combine Results and Respond to Webhook**\n\nThis final section consolidates the results from previous nodes and prepares the data for delivery via a webhook response. It ensures all authentication checks and metadata are aggregated into a cohesive output.\n\nThe process begins with the `Merge` node, which combines data streams from SPF, DKIM, and DMARC evaluations. The aggregated data is then processed by the `Aggregate` node, which organizes the results into a unified dataset.\n\nNext, the `Format Individual Auth Outputs` node formats the consolidated data into a structured JSON object. This output includes the SPF, DKIM, and DMARC results, as well as additional metadata such as the originating IP address, sender’s organization, geographical location, IP reputation, and recent spam activity. Each field is clearly labeled to ensure compatibility with external systems.\n\nThe formatted output is passed to the `Format Webhook Output` node, which finalizes the response structure for the webhook. The `Respond to Webhook` node then sends this structured response back to the calling system. This enables seamless integration with third-party platforms, allowing them to use the results for further analysis or automation.\n\nBy combining and formatting all authentication data, this section ensures that the workflow delivers clear, actionable insights to the consuming system, completing the email analysis pipeline."}, "typeVersion": 1}, {"id": "4c2592a3-3550-428c-9622-b1e95ad28d4f", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [1620, 1540], "parameters": {"color": 7, "width": 1016.1357697283069, "height": 1788.2607166792513, "content": "![](https://uploads.n8n.io/templates/n8n.png)\n## **Evaluate SPF, DKIM, and DMARC Compliance**\n\nThis section focuses on detailed analysis and validation of SPF, DKIM, and DMARC headers. Each authentication mechanism is evaluated to determine its status, providing critical insights into the email’s legitimacy and adherence to security protocols.\n\nThe workflow begins with the `Received-SPF Header?` node, which checks if the \"Received-SPF\" header exists. If found, the workflow proceeds to the `Extract Received-SPF Header` node to isolate the SPF validation results. These results are aggregated and analyzed using the `Aggregate Received-SPF Headers` node, with the final outcome recorded by the `Set SPF Value` node. If no SPF header is found, the workflow instead records this absence using the `No SPF Found` node.\n\nThe `DKIM-Signature Header?` node performs a similar function for DKIM validation, checking for the presence of a DKIM signature. If the header is found, the `DKIM Signature Found` node confirms its presence, while the `No DKIM Signature Found` node handles its absence.\n\nThe `DMARC Header?` node checks for the presence of a DMARC header, indicating compliance with the domain’s published DMARC policy. If present, the workflow extracts and evaluates it via the `Extract DMARC Header` and `Set DMARC Value` nodes. If the header is missing, the `No DMARC Header` node records this information.\n\nBy systematically evaluating these headers, the workflow provides a comprehensive understanding of the email's authentication status. This granular analysis strengthens email security by detecting potential spoofing or misconfigurations in the sender’s authentication setup."}, "typeVersion": 1}], "pinData": {}, "connections": {"Merge": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Webhook1": {"main": [[{"node": "Set Webhook Headers Here", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "Format Individual Auth Outputs", "type": "main", "index": 0}]]}, "Set Headers": {"main": [[{"node": "Extract Received Headers", "type": "main", "index": 0}]]}, "No SPF Found": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Query IP API": {"main": [[{"node": "Authentication-Results Header?", "type": "main", "index": 0}]]}, "DMARC Header?": {"main": [[{"node": "Extract DMARC Header", "type": "main", "index": 0}], [{"node": "No DMARC Header", "type": "main", "index": 0}]]}, "Set SPF Value": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "No DMARC Header": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 2}]]}, "Set DMARC Value": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 2}]]}, "Set Headers Here": {"main": [[{"node": "Set Headers", "type": "main", "index": 0}]]}, "Original IP Found?": {"main": [[{"node": "Query IP Quality Score API", "type": "main", "index": 0}], [{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "DKIM Signature Found": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Extract DMARC Header": {"main": [[{"node": "Set DMARC Value", "type": "main", "index": 0}]]}, "Received-SPF Header?": {"main": [[{"node": "Extract Received-<PERSON><PERSON>", "type": "main", "index": 0}], [{"node": "No SPF Found", "type": "main", "index": 0}]]}, "Trigger on New Email": {"main": [[{"node": "Retrieve Headers of Email", "type": "main", "index": 0}]]}, "Determine Auth Values": {"main": [[{"node": "Format Combined Auth Output", "type": "main", "index": 0}]]}, "Format Webhook Output": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "DKIM-Signature Header?": {"main": [[{"node": "DKIM Signature Found", "type": "main", "index": 0}], [{"node": "No DKIM Signature Found", "type": "main", "index": 0}]]}, "No DKIM Signature Found": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Extract Original From IP": {"main": [[{"node": "Original IP Found?", "type": "main", "index": 0}]]}, "Extract Received Headers": {"main": [[{"node": "Remove Extra Received Headers", "type": "main", "index": 0}]]}, "No Operation, do nothing": {"main": [[{"node": "Authentication-Results Header?", "type": "main", "index": 0}]]}, "Set Webhook Headers Here": {"main": [[{"node": "Set Headers", "type": "main", "index": 0}]]}, "Retrieve Headers of Email": {"main": [[{"node": "Set Headers Here", "type": "main", "index": 0}]]}, "Query IP Quality Score API": {"main": [[{"node": "Query IP API", "type": "main", "index": 0}]]}, "Extract Received-SPF Header": {"main": [[{"node": "Aggregate Received-SPF Headers", "type": "main", "index": 0}]]}, "Format Combined Auth Output": {"main": [[{"node": "Format Webhook Output", "type": "main", "index": 0}]]}, "Remove Extra Received Headers": {"main": [[{"node": "Extract Original From IP", "type": "main", "index": 0}]]}, "Aggregate Received-SPF Headers": {"main": [[{"node": "Set SPF Value", "type": "main", "index": 0}]]}, "Authentication-Results Header?": {"main": [[{"node": "Extract Authentication-Results Header", "type": "main", "index": 0}], [{"node": "Received-<PERSON><PERSON>?", "type": "main", "index": 0}, {"node": "DKIM-Signature Header?", "type": "main", "index": 0}, {"node": "DMARC Header?", "type": "main", "index": 0}]]}, "Format Individual Auth Outputs": {"main": [[{"node": "Format Webhook Output", "type": "main", "index": 0}]]}, "Extract Authentication-Results Header": {"main": [[{"node": "Determine Auth Values", "type": "main", "index": 0}]]}}}