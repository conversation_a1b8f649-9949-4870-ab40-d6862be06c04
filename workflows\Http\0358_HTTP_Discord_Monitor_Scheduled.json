{"id": "1", "name": "Website check", "nodes": [{"name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [400, 300], "parameters": {"url": "", "options": {}, "responseFormat": "string"}, "typeVersion": 1}, {"name": "IF", "type": "n8n-nodes-base.if", "position": [550, 300], "parameters": {"conditions": {"string": [{"value1": "={{$node[\"HTTP Request\"].json[\"data\"]}}", "value2": "Out Of Stock", "operation": "contains"}]}}, "typeVersion": 1}, {"name": "Discord", "type": "n8n-nodes-base.discord", "position": [700, 300], "parameters": {"text": "value found", "webhookUri": ""}, "typeVersion": 1}, {"name": "Discord1", "type": "n8n-nodes-base.discord", "position": [700, 450], "parameters": {"text": "value not found", "webhookUri": ""}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [210, 300], "parameters": {"triggerTimes": {"item": [{"mode": "everyHour"}]}}, "typeVersion": 1}], "active": false, "settings": {"timezone": "America/Los_Angeles"}, "connections": {"IF": {"main": [[], [{"node": "Discord1", "type": "main", "index": 0}]]}, "Cron": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}}}