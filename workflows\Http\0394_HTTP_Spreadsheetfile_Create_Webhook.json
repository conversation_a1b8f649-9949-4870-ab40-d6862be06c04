{"meta": {"instanceId": "257476b1ef58bf3cb6a46e65fac7ee34a53a5e1a8492d5c6e4da5f87c9b82833", "templateId": "2071"}, "nodes": [{"id": "577fb3b7-b0a6-4f2b-9b53-36d1f77de5a0", "name": "Get File ID", "type": "n8n-nodes-base.airtable", "position": [1120, 1120], "parameters": {"id": "={{ $node[\"New Upload\"].json[\"id\"] }}", "base": {"__rl": true, "mode": "id", "value": "={{ $item(\"0\").$node[\"Airtable Base IDs\"].json[\"Base ID\"] }}"}, "table": {"__rl": true, "mode": "id", "value": "={{ $item(\"0\").$node[\"Airtable Base IDs\"].json[\"Upload Table ID\"] }}"}, "options": {}, "operation": "get"}, "credentials": {"airtableTokenApi": {"id": "b1TkvXJM6AdmupUh", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2}, {"id": "a287658f-50e0-4d08-9342-a5143dc20ff2", "name": "Status Failed", "type": "n8n-nodes-base.httpRequest", "position": [2820, 1180], "parameters": {"url": "=https://api.airtable.com/v0/{{ $item(\"0\").$node[\"Airtable Base IDs\"].json[\"Base ID\"] }}/{{ $item(\"0\").$node[\"Airtable Base IDs\"].json[\"Upload Table ID\"] }}", "method": "PATCH", "options": {}, "jsonBody": "={\n\"records\":[{\n\"id\":\"{{ $item(\"0\").$node[\"Get File ID\"].json[\"record_id\"] }}\",\n\"fields\":{\n\"Status\":\"Failed\"\n}\n}\n]\n}", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "airtableTokenApi"}, "credentials": {"airtableTokenApi": {"id": "b1TkvXJM6AdmupUh", "name": "Airtable Personal Access Token account"}}, "executeOnce": true, "typeVersion": 4.1}, {"id": "e3aae523-4803-4f69-9697-ab677c3f216d", "name": "Status Uploaded", "type": "n8n-nodes-base.httpRequest", "position": [2820, 1020], "parameters": {"url": "=https://api.airtable.com/v0/{{ $item(\"0\").$node[\"Airtable Base IDs\"].json[\"Base ID\"] }}/{{ $item(\"0\").$node[\"Airtable Base IDs\"].json[\"Upload Table ID\"] }}", "method": "PATCH", "options": {}, "jsonBody": "={\n\"records\":[{\n\"id\":\"{{ $item(\"0\").$node[\"Get File ID\"].json[\"record_id\"] }}\",\n\"fields\":{\n\"Status\":\"Uploaded\"\n}\n}\n]\n}", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "airtableTokenApi"}, "credentials": {"airtableTokenApi": {"id": "b1TkvXJM6AdmupUh", "name": "Airtable Personal Access Token account"}}, "executeOnce": true, "typeVersion": 4.1}, {"id": "833515af-bf3a-4bc7-b79c-a6c1731f4714", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [2280, 820], "parameters": {"width": 319.*************, "height": 538.*************, "content": "## Confirm the key names and column references\n\n\n\n\nWhen adapting this to your own base and Google Sheets (CSV) template, make sure to modify this node accordingly, as key values you will need to set the Airtable Fields, and the Expressions need to match the Read File column names\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nMake sure that the fields have the correct data type (Strings, Integers (numbers), etc)"}, "typeVersion": 1}, {"id": "e3cfcf21-3210-455c-b539-2dcacda3172a", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [840, 920], "parameters": {"height": 416.**************, "content": "### Input your Airtables relevant ID's. These will be used in the API Calls"}, "typeVersion": 1}, {"id": "c244d6fe-21bf-4488-9780-32b56baa9998", "name": "Campaign is Not Empty", "type": "n8n-nodes-base.if", "position": [1880, 1120], "parameters": {"conditions": {"string": [{"value1": "={{ $item(\"0\").$node[\"Get File ID\"].json[\"Campaign\"][\"0\"] }}", "operation": "isNotEmpty"}]}}, "typeVersion": 1}, {"id": "ced8a7f4-4ccc-4fcf-8c13-c1b8f099283e", "name": "Campaign Not Empty", "type": "n8n-nodes-base.set", "position": [2120, 1020], "parameters": {"fields": {"values": [{"name": "Campaign", "stringValue": "=\"Campaigns\":[\"{{ $item(\"0\").$node[\"Get File ID\"].json[\"Campaign\"][\"0\"] }}\"],"}]}, "options": {}}, "typeVersion": 3.2}, {"id": "23e0a41c-cbbd-401d-88b4-a4b190dbcd72", "name": "Campaign Not Empty1", "type": "n8n-nodes-base.set", "position": [2120, 1200], "parameters": {"fields": {"values": [{"name": "Campaign"}]}, "options": {}}, "typeVersion": 3.2}, {"id": "f6c40cf2-4893-42ee-859c-f430b4dc5cf1", "name": "Read File", "type": "n8n-nodes-base.spreadsheetFile", "position": [1660, 1120], "parameters": {"options": {"headerRow": true}, "binaryPropertyName": "=data"}, "typeVersion": 2}, {"id": "b7495a65-32bf-430d-9998-483582bbe6ef", "name": "Airtable Base IDs", "type": "n8n-nodes-base.set", "position": [900, 1120], "parameters": {"fields": {"values": [{"name": "Base ID", "stringValue": "=appZ0qelhmC2Y9igI"}, {"name": "Upload Table ID", "stringValue": "tblDzSabZcP47sIMp"}, {"name": "Lead Table ID", "stringValue": "tblnsXKf3TBztlIPV"}]}, "include": "none", "options": {}}, "typeVersion": 3.2}, {"id": "9fa8f822-f611-4af6-a2a4-7baaf2efa82d", "name": "Status Processing", "type": "n8n-nodes-base.httpRequest", "position": [1280, 1120], "parameters": {"url": "=https://api.airtable.com/v0/{{ $item(\"0\").$node[\"Airtable Base IDs\"].json[\"Base ID\"] }}/{{ $item(\"0\").$node[\"Airtable Base IDs\"].json[\"Upload Table ID\"] }}", "method": "PATCH", "options": {}, "jsonBody": "={\n\"records\":[{\n\"id\":\"{{ $node[\"Get File ID\"].json[\"record_id\"] }}\",\n\"fields\":{\n\"Status\":\"Processing\"\n}\n}\n]\n}", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "airtableTokenApi"}, "credentials": {"airtableTokenApi": {"id": "b1TkvXJM6AdmupUh", "name": "Airtable Personal Access Token account"}}, "typeVersion": 4.1}, {"id": "af23a338-a9a0-49db-88de-d6eb68af2be9", "name": "Download File", "type": "n8n-nodes-base.httpRequest", "position": [1460, 1120], "parameters": {"url": "={{ $node[\"Get File ID\"].json[\"File\"][\"0\"][\"url\"] }}", "options": {"response": {"response": {"responseFormat": "file"}}}}, "typeVersion": 4.1}, {"id": "4428cdc4-1ffd-4f6f-8d96-49d20b80bfba", "name": "Create Records", "type": "n8n-nodes-base.httpRequest", "onError": "continueErrorOutput", "position": [2380, 1120], "parameters": {"url": "=https://api.airtable.com/v0/{{ $item(\"0\").$node[\"Airtable Base IDs\"].json[\"Base ID\"] }}/{{ $item(\"0\").$node[\"Airtable Base IDs\"].json[\"Lead Table ID\"] }}", "method": "POST", "options": {"batching": {"batch": {"batchSize": 8}}}, "jsonBody": "={\n    \"records\": [\n        {\n            \"fields\": {\n                \"FirstName\": \"{{ $json[\"FirstName\"] }}\",\n                \"LastName\": \"{{ $json[\"LastName\"] || \"\"}}\",\n                \"Email\": \"{{ $json[\"Email\"] || \"\" }}\",\n                \"Phone\": \"{{ $json[\"Phone\"] || \"\" }}\",\n                \"Company\": \"{{ $json[\"Company\"] || \"\" }}\",\n                \"Title\": \"{{ $json[\"Title\"] || \"\" }}\",\n                \"Country\": \"{{ $json[\"Country\"] || \"\" }}\",\n                \"City\": \"{{ $json[\"City\"] || \"\" }}\",\n                \"Website\": \"{{ $json[\"Website\"] || \"\" }}\",\n                \"LeadSource\": \"{{ $json[\"LeadSource\"] || \"\" }}\",\n                \"LeadStatus\": \"{{ $json[\"LeadStatus\"] || \"\" }}\",\n                {{ $json[\"Campaign\"] }}\n                \"InterestLevel\": \"{{ $json[\"InterestLevel\"] || \"\" }}\",\n                \"LastContactDate\": \"{{ $json[\"LastContactDate\"] || \"\" }}\"\n\n\n            }\n        }\n    ]\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "nodeCredentialType": "airtableTokenApi"}, "credentials": {"airtableTokenApi": {"id": "b1TkvXJM6AdmupUh", "name": "Airtable Personal Access Token account"}}, "typeVersion": 4.1}, {"id": "e7a2cf60-099f-4c32-b9f0-ad2dd3d6e282", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [380, 240], "parameters": {"width": 1608.************, "height": 349.**************, "content": "# Bulk Upload Contacts Through CSV | Airtable Interface & Airtable Grid\n\n\n## Airtable Template - https://www.airtable.com/universe/expkxniTpHDg4Y4Ni/interfaces-upload-bulk-records-from-csv\n## Google Sheets Template - https://docs.google.com/spreadsheets/d/1SEwOGCfekc1h_ZfZ8PDQY6oGgOGSzSgtD7pEliEGaZ0/edit?usp=sharing"}, "typeVersion": 1}, {"id": "dd8b54fa-15fb-4df5-b94f-8286dae7026b", "name": "New Upload", "type": "n8n-nodes-base.airtableTrigger", "position": [660, 1120], "parameters": {"baseId": {"__rl": true, "mode": "id", "value": "appZ0qelhmC2Y9igI"}, "tableId": {"__rl": true, "mode": "id", "value": "tblDzSabZcP47sIMp"}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerField": "Created At", "authentication": "airtableTokenApi", "additionalFields": {"viewId": ""}}, "credentials": {"airtableTokenApi": {"id": "b1TkvXJM6AdmupUh", "name": "Airtable Personal Access Token account"}}, "typeVersion": 1}, {"id": "32f6ec9b-3f23-4d58-9cc2-b41fd9246091", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1980, 240], "parameters": {"width": 879.*************, "height": 224.**************, "content": "## Walkthrough and Overview\n\n### https://www.youtube.com/watch?v=LgYxS1O-rbs"}, "typeVersion": 1}, {"id": "********-c1c2-4bf0-ba04-a48403cca0cb", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [60, 820], "parameters": {"width": 558.*************, "height": 768.*************, "content": "# Setup Checklist\n\n### 1.Go to the Airtable Template and copy the latest version of the base\n### \n### 2. From your new Airtable base URL, get and replace your base and tables id's into this workflow's trigger node.\n### 3. Input your Airtable Id's in the second node \"Airtable Base ID's\"\n### 4. Make sure to add a Personal Access Token for Airtable Integration. It should, as minimum have enabled scopes for \"data.record:read\", \"data.record:write\", \"schema.bases:read\"\n### 5. Any file uploads can now be done from the Interface Form\n\n#After Setup\n\n### - Make sure you that if you add, remove or modify fields (or field names), those changes should also be applied to the \"Create Record\" node\n### - Make sure that the CSV upload header row, matches the Airtable Leads field names\n### - If you modify any field type (Text to Number, or Number to Text), those changes should also be applied to the \"Create Records\" value (Numbers go without double quotes / strings, dates and the rest of the data types go with double quotes) [JSON Syntax]"}, "typeVersion": 1}], "pinData": {}, "connections": {"Read File": {"main": [[{"node": "Campaign is Not Empty", "type": "main", "index": 0}]]}, "New Upload": {"main": [[{"node": "Airtable Base IDs", "type": "main", "index": 0}]]}, "Get File ID": {"main": [[{"node": "Status Processing", "type": "main", "index": 0}]]}, "Download File": {"main": [[{"node": "Read File", "type": "main", "index": 0}]]}, "Create Records": {"main": [[{"node": "Status Uploaded", "type": "main", "index": 0}], [{"node": "Status Failed", "type": "main", "index": 0}]]}, "Airtable Base IDs": {"main": [[{"node": "Get File ID", "type": "main", "index": 0}]]}, "Status Processing": {"main": [[{"node": "Download File", "type": "main", "index": 0}]]}, "Campaign Not Empty": {"main": [[{"node": "Create Records", "type": "main", "index": 0}]]}, "Campaign Not Empty1": {"main": [[{"node": "Create Records", "type": "main", "index": 0}]]}, "Campaign is Not Empty": {"main": [[{"node": "Campaign Not Empty", "type": "main", "index": 0}], [{"node": "Campaign Not Empty1", "type": "main", "index": 0}]]}}}