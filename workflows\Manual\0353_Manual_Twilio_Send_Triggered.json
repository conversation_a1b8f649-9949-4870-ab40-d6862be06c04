{"id": "1", "name": "Send SMS to numbers stored in Airtable with Twilio", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "Airtable", "type": "n8n-nodes-base.airtable", "position": [450, 300], "parameters": {"table": "", "operation": "list", "application": "", "additionalOptions": {}}, "credentials": {"airtableApi": ""}, "typeVersion": 1}, {"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.twilio", "position": [650, 300], "parameters": {"to": "={{$node[\"Airtable\"].json[\"fields\"][\"Number\"]}}", "from": "", "message": "=Hello, {{$node[\"Airtable\"].json[\"fields\"][\"Name\"]}}!\nSending this SMS from n8n!"}, "credentials": {"twilioApi": ""}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Twilio": {"main": [[]]}, "Airtable": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Airtable", "type": "main", "index": 0}]]}}}