{"meta": {"instanceId": "e122e4b90b0dc212c47b42e306cb84c993d082784105d7fe578eca9a9d068de0", "templateCredsSetupCompleted": true}, "nodes": [{"id": "c3f63a01-1450-4f97-ab2d-16414613f50c", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [400, 320], "parameters": {}, "typeVersion": 1}, {"id": "bc725e33-353d-4b3d-b65f-eb295053e5cc", "name": "Get Wordpress Posts", "type": "n8n-nodes-base.wordpress", "position": [640, 320], "parameters": {"options": {"status": "publish"}, "operation": "getAll", "returnAll": true}, "credentials": {"wordpressApi": {"id": "xIzhb0T5dm53dkod", "name": "Wordpress account"}}, "typeVersion": 1}, {"id": "07ed3f2a-c2b6-4e3c-80d7-425adc6ad36d", "name": "Adjust Fields", "type": "n8n-nodes-base.set", "position": [860, 320], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "39ade710-ebe5-4c4d-9bc8-5ad86a3c76b5", "name": "id", "type": "number", "value": "={{ $json.id }}"}, {"id": "2714c21d-5ad3-408b-b91d-aa4513f384f3", "name": "title", "type": "string", "value": "={{ $json.title.rendered }}"}, {"id": "********-c5c6-4bf0-8a33-5aa88d02ddf4", "name": "link", "type": "string", "value": "={{ $json.link }}"}, {"id": "69b5c680-965e-4078-809d-74b10da1a29f", "name": "content", "type": "string", "value": "={{ $json.content.rendered }}"}]}}, "typeVersion": 3.4}, {"id": "234d6755-e862-4277-b0b7-1ac65cd87c12", "name": "Convert to CSV File", "type": "n8n-nodes-base.convertToFile", "position": [1080, 320], "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "49901cd8-5ef5-41b5-87c3-a5979cf11644", "name": "Upload to Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [1300, 320], "parameters": {"name": "Wordpress-Posts.csv", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "root", "cachedResultUrl": "https://drive.google.com/drive", "cachedResultName": "/ (Root folder)"}, "authentication": "serviceAccount"}, "credentials": {"googleApi": {"id": "1", "name": "Google Account"}}, "typeVersion": 3}, {"id": "a36bccd7-9298-4c96-8f4e-83b9096e53dd", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [800, 160], "parameters": {"height": 140, "content": "### Adjust fields\nYou can add more fields to the CSV file by editing this node"}, "typeVersion": 1}, {"id": "5d86d3be-dd69-454a-b739-17ded5636ee1", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [100, 220], "parameters": {"height": 260, "content": "### Export WordPress Posts to CSV and Upload to Google Drive\n\nSteps:\n- Set your WordPress credentials in the \"Get WordPress Posts\" node\n- Set your Google Drive access in the Drive node\n- Click Test workflow"}, "typeVersion": 1}], "pinData": {}, "connections": {"Adjust Fields": {"main": [[{"node": "Convert to CSV File", "type": "main", "index": 0}]]}, "Convert to CSV File": {"main": [[{"node": "Upload to Google Drive", "type": "main", "index": 0}]]}, "Get Wordpress Posts": {"main": [[{"node": "Adjust Fields", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Get Wordpress Posts", "type": "main", "index": 0}]]}}}