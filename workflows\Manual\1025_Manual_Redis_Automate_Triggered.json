{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [270, 320], "parameters": {}, "typeVersion": 1}, {"name": "Redis", "type": "n8n-nodes-base.redis", "position": [470, 320], "parameters": {"key": "hello", "options": {}, "operation": "get"}, "credentials": {"redis": "redis-docker_creds"}, "typeVersion": 1}], "connections": {"On clicking 'execute'": {"main": [[{"node": "Redis", "type": "main", "index": 0}]]}}}