{"name": "Generate Image Workflow", "tags": [], "nodes": [{"id": "0a657f21-f0fe-4521-be7f-aa245f86f5d3", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [340, -200], "parameters": {}, "typeVersion": 1}, {"id": "54ead951-03fb-4741-9e66-bffa0ff42302", "name": "Fetch Image from API", "type": "n8n-nodes-base.httpRequest", "position": [780, -200], "parameters": {"url": "=https://dummyjson.com/image/{{ $json.size }}/{{ $json.backgroundColor }}/{{ $json.textColor }}", "options": {}, "sendQuery": true, "queryParameters": {"parameters": [{"name": "text", "value": "={{ $json.text }}"}, {"name": "fontSize", "value": "={{ $json.fontSize }}"}, {"name": "type", "value": "={{ $json.type }}"}, {"name": "fontFamily", "value": "={{ $json.fontFamily }}"}]}}, "typeVersion": 4.2}, {"id": "9b60f208-7bbc-4c35-9303-797aabef478d", "name": "Set Image Properties", "type": "n8n-nodes-base.set", "position": [560, -200], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "25b4c572-4ba6-4719-b547-8d3787ba557b", "name": "size", "type": "string", "value": "600x400"}, {"id": "a6689fdb-b212-4c88-b80f-64aabe61daa1", "name": "backgroundColor", "type": "string", "value": "cc22e3"}, {"id": "f9dcc452-4dd5-46fc-948b-39194bf0637d", "name": "textColor", "type": "string", "value": "ffffff"}, {"id": "89842462-d3ac-4267-a40a-3e98e8823ef3", "name": "text", "type": "string", "value": "Generated!"}, {"id": "59eb064d-1cc3-4b7d-92ec-594dadbd38cd", "name": "fontSize", "type": "string", "value": "100"}, {"id": "ccbae0db-559a-4de2-be63-4238feca6498", "name": "fontFamily", "type": "string", "value": "pacifico"}, {"id": "ab88695a-d223-4f26-9ded-3e4c965ca28c", "name": "type", "type": "string", "value": "png"}]}}, "typeVersion": 3.4}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "c2d9939a-6766-4b7c-8331-63a655946208", "connections": {"Set Image Properties": {"main": [[{"node": "Fetch Image from API", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set Image Properties", "type": "main", "index": 0}]]}}}