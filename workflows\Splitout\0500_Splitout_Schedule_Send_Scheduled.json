{"meta": {"instanceId": "84ba6d895254e080ac2b4916d987aa66b000f88d4d919a6b9c76848f9b8a7616", "templateId": "2278", "templateCredsSetupCompleted": true}, "nodes": [{"id": "4074dda0-993b-4b63-8502-3db09a920e42", "name": "Send Gmail", "type": "n8n-nodes-base.gmail", "position": [1520, 260], "parameters": {"sendTo": "<EMAIL>", "message": "={{ $json.html }}", "options": {"appendAttribution": true}, "subject": "new stable version of n8n released"}, "credentials": {"gmailOAuth2": {"id": "nx3IJyQ7TuVxI0y2", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "********-5482-416f-abab-32ddbb9c3123", "name": "Fetch Github Repo Releases", "type": "n8n-nodes-base.httpRequest", "position": [840, 260], "parameters": {"url": "https://api.github.com/repos/n8n-io/n8n/releases/latest", "options": {}}, "typeVersion": 4.2}, {"id": "fb96efc1-e615-4378-80c5-1ddafff3a4e8", "name": "Split Out Content", "type": "n8n-nodes-base.splitOut", "position": [1080, 260], "parameters": {"options": {}, "fieldToSplitOut": "body"}, "typeVersion": 1}, {"id": "ca540fee-a2dc-4780-95d1-6a0a5b8dc6fa", "name": "Convert Markdown to HTML", "type": "n8n-nodes-base.markdown", "position": [1260, 260], "parameters": {"mode": "markdownToHtml", "options": {}, "markdown": "={{ $json.body }}", "destinationKey": "html"}, "typeVersion": 1}, {"id": "e0e74830-bb7b-4b1f-9a1e-e7eb01a5cecb", "name": "Daily Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [580, 260], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.2}, {"id": "d90f0d15-de3b-4997-9a36-f6ee08d5aea2", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [740, 160], "parameters": {"width": 288, "height": 300, "content": "Change **url** for Github Repo here"}, "typeVersion": 1}, {"id": "f3b1af79-9946-4588-bae9-839f712a7d12", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1440, 160], "parameters": {"width": 288, "height": 300, "content": "Change **to Email** here"}, "typeVersion": 1}], "pinData": {}, "connections": {"Daily Trigger": {"main": [[{"node": "Fetch Github Repo Releases", "type": "main", "index": 0}]]}, "Split Out Content": {"main": [[{"node": "Convert Markdown to HTML", "type": "main", "index": 0}]]}, "Convert Markdown to HTML": {"main": [[{"node": "Send Gmail", "type": "main", "index": 0}]]}, "Fetch Github Repo Releases": {"main": [[{"node": "Split Out Content", "type": "main", "index": 0}]]}}}