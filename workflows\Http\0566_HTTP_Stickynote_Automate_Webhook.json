{"meta": {"instanceId": "cb484ba7b742928a2048bf8829668bed5b5ad9787579adea888f05980292a4a7"}, "nodes": [{"id": "f1142274-898d-43da-a7ff-2b2e03f2dc73", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [1220, 840], "parameters": {}, "typeVersion": 1}, {"id": "1f407421-2dd6-4e0c-bc74-cfb291e475ed", "name": "Query Confluence", "type": "n8n-nodes-base.httpRequest", "position": [1640, 840], "parameters": {"url": "https://n8n-labs.atlassian.net/wiki/rest/api/search", "options": {}, "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth", "queryParameters": {"parameters": [{"name": "cql", "value": "=text ~ \"{{ $json.query }}\""}]}, "headerParameters": {"parameters": [{"name": "accept", "value": "application/json"}]}}, "credentials": {"httpBasicAuth": {"id": "B1Cj4Uh9d9WKWxBO", "name": "Confluence API Key"}}, "typeVersion": 4.2}, {"id": "f1ab7e79-6bd8-4b87-b6dc-96f9d46cdd16", "name": "Return Tool Response", "type": "n8n-nodes-base.set", "position": [2040, 840], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c1d46e59-9340-43f3-bc2a-fbd4e0def74f", "name": "response", "type": "string", "value": "=\"Title\": \"{{ $json.results[0].content.title }}\"\n\"Link\": \"{{ $json._links.base }}{{ $json.results[0].content._links.webui }}\"\n\"Content\": {{ $json[\"results\"][0][\"excerpt\"] }}\nWhen users request the password, make sure to send them the link above to reset it in markdown. "}]}}, "typeVersion": 3.3}, {"id": "19be50a2-4835-48a6-b06a-7996231c519d", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1037.1879432624112, 466.2978723404259], "parameters": {"color": 7, "width": 460.26595744680884, "height": 598.588007755415, "content": "![n8n](https://i.imgur.com/lKnBNnH.png)\n## Receive Query from Parent Workflow\nThis node receives input from the AI Agent in the top level workflow where it passes just the Slack Message directly to this workflow."}, "typeVersion": 1}, {"id": "0012feaa-89f5-40a4-86d6-98e0e9648bd5", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1520, 469.2511978555872], "parameters": {"color": 7, "width": 350.94680851063845, "height": 588.3931371954408, "content": "![confluence](https://i.imgur.com/rM48yHY.png)\n## Search Confluence\nThe newly created prompt is then sent into Confluence's API as a search string. \n\nTo replace this with your own KB tool, find the Endpoint that allows search, and replace this HTTP Request node with your own HTTP Request or Built in n8n node and pass the search variable into the search input. "}, "typeVersion": 1}, {"id": "6982692e-61c5-47fc-9946-ada32d5fa2a1", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1900, 460], "parameters": {"color": 7, "width": 648.2749545725208, "height": 597.2865893156994, "content": "![n8n](https://i.imgur.com/lKnBNnH.png)\n## Respond to Parent Workflow with Confluence Results\nThe final output is then sent to the Parent workflow to be used in the final AI Agent API call to the LLM of your choice as part of the final output. Here is the prompt output: \n```\n\"Title\": \"Title of content so AI Agent will know the name of the content\"\n\"Link\": \"Link to URL of KB article. Great for giving back to user to self help\"\n\"Content\": Truncated output of content so that the large language model will have more context in it's final response. \nWhen users request the password, make sure to send them the link above to reset it in markdown. \n```"}, "typeVersion": 1}, {"id": "9570ee97-8508-4c7f-a2da-a327fbc7db46", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [460, 460], "parameters": {"width": 543.0233137166141, "height": 854.6009864319319, "content": "![n8n](https://i.imgur.com/qXWqiOd.png)\n## Enhance Query Resolution with the Knowledge Base Tool!\n\nOur **Knowledge Base Tool** is crafted to seamlessly integrate into the IT Department Q&A Workflow, enhancing the IT support process by enabling sophisticated search and response capabilities via Slack.\n\n**Workflow Functionality:**\n- **Receive Queries**: Directly accepts user queries from the main workflow, initiating a dynamic search process.\n- **AI-Powered Query Transformation**: Utilizes OpenAI's GPT-4 to refine user queries into searchable keywords that are most likely to retrieve relevant information from the Knowledge Base.\n- **Confluence Integration**: Executes searches within Confluence using the refined keywords to find the most applicable articles and information.\n- **Deliver Accurate Responses**: Gathers essential details from the Confluence results, including article titles, links, and summaries, preparing them to be sent back to the parent workflow for final user response.\n\n\n**Quick Setup Guide:**\n- Ensure correct configurations are set for OpenAI and Confluence API integrations.\n- Customize query transformation logic as per your specific Knowledge Base structure to improve search accuracy.\n\n\n**Need Help?**\n- Dive into our [Documentation](https://docs.n8n.io) or get support from the [Community Forum](https://community.n8n.io)!\n\n\nDeploy this tool to provide precise and informative responses, significantly boosting the efficiency and reliability of your IT support workflow.\n"}, "typeVersion": 1}], "pinData": {}, "connections": {"Query Confluence": {"main": [[{"node": "Return Tool Response", "type": "main", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "Query Confluence", "type": "main", "index": 0}]]}}}