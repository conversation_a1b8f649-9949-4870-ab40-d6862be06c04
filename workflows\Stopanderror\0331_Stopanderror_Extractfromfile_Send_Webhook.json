{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "9681490a-68f1-4c6a-86ea-bf2331c3125d", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [-600, 1040], "parameters": {}, "typeVersion": 1}, {"id": "f665f0c6-7694-456f-b877-5f8d69b9f503", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-680, 920], "parameters": {"width": 715.3278290432247, "height": 315.32782904322477, "content": "## Get and prepare Dummy Data"}, "typeVersion": 1}, {"id": "79a9ece6-daa5-4cc0-bfb8-5cf8c9e81296", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [340, 480], "parameters": {"width": 520.9323109877616, "height": 577.5426854600692, "content": "## Let GPT do the heavy work\n\nFor the prompt we follow the one-shot'ish principle. Also I've decided to **_NOT_** give the AI the personal data. Keeps it simpler regarding data privacy.\n\nThe AI-Chain will generate a **Headline** and the **Text** for the Email and even **decides** if we should send the user a **Coupon**."}, "typeVersion": 1}, {"id": "51e1bc15-0b9e-4d53-9b99-0ec8ed5e00f8", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [2240, 620], "parameters": {"width": 358, "height": 324, "content": "## HTML Email-Template without Coupon"}, "typeVersion": 1}, {"id": "ee29375a-77fe-4d13-a453-c8b62f0884a7", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1100, 880], "parameters": {"width": 447, "height": 465, "content": "## Make sure we have what we need\nWe do not want to sent empty messages to our customers"}, "typeVersion": 1}, {"id": "37e09224-3649-43e0-a40f-f8177aa93cda", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [2240, 1140], "parameters": {"width": 369.917435648372, "height": 330.56011245057107, "content": "## HTML Email-Template with Coupon"}, "typeVersion": 1}, {"id": "5147fe48-606d-4dad-9977-2713f40fc8e6", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1880, 1140], "parameters": {"width": 319.84249777513367, "height": 330.6656654860422, "content": "## Mocked: Fake a Coupon Code\nFor a real life scenario add the automated coupon generation here"}, "typeVersion": 1}, {"id": "6a3ee9b0-540e-4242-a6ac-535e2b23ea3a", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-680, 300], "parameters": {"width": 534.1315466553021, "height": 566.556517486655, "content": "# Documentation\n\nThis Workflow is for the n8n AI / Langchain Competition.\n\nIt solves the Problem: Personalizing marketing emails based on customer purchase history.\n\nI've found it a bit ambiguous and decided to go the \"Convert unhappy customers with a Coupon\"-Route.\n\nSo this workflow utilizes the new LangChain Node for generating personalized E-Mail campaigns and decide if the user might need a coupon to be satisfied. Classic Rebound stuff. \n\nThere is also a Node \"Some Options...\" which can be adjusted to quickly change the direction this Campaign should go.\n\nAdditionally we use n8n to generate the HTML Mails by two different Templates. One with simple text and another for that Coupon handling.\n\n![Image](https://let-the-work-flow.com/logo-64.png)\nEnjoy the Workflow! ❤️ \nhttps://let-the-work-flow.com\n"}, "typeVersion": 1}, {"id": "01cf3e60-c280-46c1-9971-ccf63a28ab9a", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [3040, 760], "parameters": {"width": 326.9476248855971, "height": 414.15459581943776, "content": "## Send the Email to the Customer\n\nAlthough it's cool that n8n allows sending emails via SMPT I would recommend to stick to your newsletter tool for that to keep track of opt-outs and stuff."}, "typeVersion": 1}, {"id": "6c458bf6-ea7b-43b5-bc65-d9ae68542a8c", "name": "Extract from File", "type": "n8n-nodes-base.extractFromFile", "position": [-160, 1040], "parameters": {"options": {}, "operation": "xls"}, "typeVersion": 1}, {"id": "780dd707-4493-4679-9064-acc3c59011f8", "name": "Some Options for the Campaign", "type": "n8n-nodes-base.set", "position": [140, 1040], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "8ef766db-4ad1-43c7-b621-8ea3ed0a44b2", "name": "Campaign Target", "type": "string", "value": "Engage the Customer"}, {"id": "9f9ce88a-a24a-4a27-8b25-25ee85e730d6", "name": "Flavour", "type": "string", "value": "be friendly and witty but also cool and direct. Critique is valuable and embrace the feedback."}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "3b152bdc-acb8-4f37-8b91-1ab02c0e9532", "name": "Information Extractor", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [480, 700], "parameters": {"text": "=Item Purchased: {{ $json['Item Purchased'] }} \nFeedback: {{ $json.Feedback }}\nShould we send a coupon to make the customer happy? Yes/No", "options": {"systemPromptTemplate": "=Determine the sentiment of the given product feedback. Then generate a Headline and Text without salutation or any greeting for a personalized Email Campagin after a User gave a product review. If the user seems not happy, tell them that you have a Coupon for them. The User finds the Coupon Code below this E-mail. \nThe target of the campagin: {{ $json['Campaign Target'] }}.\nRemember: {{ $json['Flavour'] }}. Avoid any greeting.\n"}, "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"required\": [\"Headline\",\"Body\",\"SendCoupon\"],\n  \"properties\": {\n    \"Headline\": {\n      \"type\": \"string\"\n    },\n    \"Body\": {\n      \"type\": \"string\"\n    },\n    \"SendCoupon\": {\n      \"type\": \"boolean\"\n    }\n  }\n}"}, "typeVersion": 1}, {"id": "f597a54e-27e9-46e8-b9d5-46dd54406803", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [480, 880], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "716e4281-cf18-4cc7-b5ed-4de0308bf9aa", "name": "AI did fail us1", "type": "n8n-nodes-base.stopAndError", "position": [1380, 1180], "parameters": {"errorMessage": "Unexpected Langchain Output"}, "typeVersion": 1}, {"id": "1dc51ad5-e605-4cad-9a5b-3b20eabd9797", "name": "Fake coupon", "type": "n8n-nodes-base.set", "position": [1980, 1280], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "73989d0e-667f-4227-ab41-4eb1e8c1c10e", "name": "Coupon", "type": "string", "value": "F4k3ItT1llY0uM4k3It"}, {"id": "4d86d8c8-1be3-40b0-b4fd-09f9ffc24386", "name": "Coupon Value", "type": "string", "value": "20% of any purchase"}, {"id": "f73b8a70-5bf6-45c2-8061-d10f95b199a8", "name": "Coupon Terms", "type": "string", "value": "=Valid until {{ $today.plus({days: 14}).format(\"d. MMM. y\") }} | minimum purchase amount: 20$ "}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "dfa6b376-dd66-40f1-8626-0f3f04e4c4bd", "name": "Download dummy data", "type": "n8n-nodes-base.httpRequest", "position": [-380, 1040], "parameters": {"url": "https://let-the-work-flow.com/dummy/n8n-contest-merch.xlsx", "options": {}}, "typeVersion": 4.2}, {"id": "a95ce7c4-c592-40c7-9dfa-db0e37d5b71f", "name": "AI Output + Prev Data", "type": "n8n-nodes-base.merge", "position": [940, 1040], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3}, {"id": "bb0474a1-425c-4a02-a13e-385272091189", "name": "Is the result valid?", "type": "n8n-nodes-base.if", "position": [1160, 1040], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "9b4ced26-dd86-4ae4-8f69-6177ec42c827", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "Headline", "rightValue": ""}, {"id": "7723102c-43d2-48df-82f6-5bb45ddf615c", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "Body", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "b39e0b98-6824-4265-94a0-fe12154f2ad4", "name": "Coupon them or not to Coupon them", "type": "n8n-nodes-base.if", "position": [1620, 1040], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "967f37a1-a600-46a2-82cf-f340dd3c7a96", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.SendCoupon }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "13c4426f-f522-4127-b899-7e6397e18182", "name": "Html Template for our Email", "type": "n8n-nodes-base.html", "position": [2360, 740], "parameters": {"html": "<!DOCTYPE html>\n<html>\n<head>\n  <meta charset=\"UTF-8\" />\n  <title>{{ $json['Headline'] }}</title>\n</head>\n<body>\n  <div class=\"container\">\n    <img class=\"logo\" src=\"https://img.logoipsum.com/264.svg\"/>\n    <h1>Hey {{ $json['Custome Name'] ? $json['Custome Name']+', ' : '!' }}</h1>\n    <p>{{ $json['Body'] }}</p>\n    \n  <div class=\"footer\">\n   <p>\n    Definitely not a real company Lmt.<br>\n    Also not a real street 123<br>\n    Unreal Town\n   </p>  \n</div> \n </div>\n \n  \n</body>\n</html>\n\n<style>\n.logo {\n  margin-top: 20px;\n }\n.container {\n  background-color: #ffffff;\n  font-family: sans-serif;\n  padding: 16px;\n  border-radius: 8px;\n}\n\nh1 {\n  color: #ff6d5a;\n  font-size: 24px;\n  font-weight: bold;\n  margin-top: 30px;\n}\n\np {\n  color: #606060;\n  line-height: 1.6;\n}\n\nh2 {\n  color: #909399;\n  font-size: 20px;\n  font-weight: bold;\n  padding: 8px;\n}\n\n.footer {\n  margin-top: 30px;\n}\n\n.footer > p {\n    font-size: 14px;\n  color: #ccc;\n }\n\n</style>"}, "typeVersion": 1.2}, {"id": "71e36c09-6e24-4eb2-9b1a-4fb3bb4b4536", "name": "The composed E-Mail + Prev Data", "type": "n8n-nodes-base.merge", "position": [2740, 860], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3}, {"id": "a2b6ec8e-1bcf-4216-b9b6-476c0d82f706", "name": "Html Template for our Email with a Coupon", "type": "n8n-nodes-base.html", "position": [2360, 1280], "parameters": {"html": "<!DOCTYPE html>\n<html>\n<head>\n  <meta charset=\"UTF-8\" />\n  <title>{{ $json.output['Headline'] }}</title>\n</head>\n<body>\n  <div class=\"container\">\n    <img class=\"logo\" src=\"https://img.logoipsum.com/264.svg\"/>\n    <h1>Hey {{ $json['Custome Name'] ? $json['Custome Name']+', ' : '!' }}</h1>\n    <p>{{ $json.output['Body'] }}</p>\n    \n    <div class=\"coupon\">\n        <h3>Here's a Coupon for you!<br>\n        {{ $json['Coupon Value'] }}</h3>\n        <h4 class=\"code\">{{ $json['Coupon'] }}</h4>\n        <p>{{ $json['Coupon Terms'] }}</p>\n    </div>\n  <div class=\"footer\">\n   <p>\n    Definitely not a real company Lmt.<br>\n    Also not a real street 123<br>\n    Unreal Town\n   </p>  \n</div> \n </div>\n \n  \n</body>\n</html>\n\n<style>\n.logo {\n  margin-top: 20px;\n }\n.container {\n  background-color: #ffffff;\n  font-family: sans-serif;\n  padding: 16px;\n  border-radius: 8px;\n}\n\nh1 {\n  color: #ff6d5a;\n  font-size: 24px;\n  font-weight: bold;\n  margin-top: 30px;\n}\n\np {\n  color: #606060;\n  line-height: 1.6;\n}\n\nh2 {\n  color: #909399;\n  font-size: 20px;\n  font-weight: bold;\n  padding: 8px;\n}\n\n.coupon {\n  background: #ff6d5a;\n  color: #fff;\n  padding: 20px;\n}\n.coupon p {\n  color: #fff;\n}\n  \n.coupon .code {\n  font-weight: bold;\n  font-size: 24px;\n  font-family: monospace;\n }\n\n.footer {\n  margin-top: 30px;\n}\n\n.footer > p {\n    font-size: 14px;\n  color: #ccc;\n }\n\n</style>"}, "typeVersion": 1.2}, {"id": "2d5dd858-cf61-4136-b405-e6ad4a372725", "name": "The composed E-Mail with Coupon + Prev Data", "type": "n8n-nodes-base.merge", "position": [2740, 1040], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3}, {"id": "5b1606b4-903a-4e90-8cf6-01fd92006195", "name": "Send Email", "type": "n8n-nodes-base.emailSend", "position": [3140, 960], "webhookId": "a155d7b3-39b1-4a96-adc5-4f8e984506ec", "parameters": {"html": "={{ $json.html }}", "options": {}, "subject": "={{ $json.output.Headline }}", "toEmail": "={{ $json.Email }}", "fromEmail": "<EMAIL>"}, "credentials": {"smtp": {"id": "EagS3depRLAKo3Sw", "name": "Greenmail SMTP account (<EMAIL>)"}}, "typeVersion": 2.1}], "pinData": {}, "connections": {"Fake coupon": {"main": [[{"node": "Html Template for our Email with a Coupon", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "Some Options for the Campaign", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Information Extractor", "type": "ai_languageModel", "index": 0}]]}, "Download dummy data": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "Is the result valid?": {"main": [[{"node": "Coupon them or not to Coupon them", "type": "main", "index": 0}], [{"node": "AI did fail us1", "type": "main", "index": 0}]]}, "AI Output + Prev Data": {"main": [[{"node": "Is the result valid?", "type": "main", "index": 0}]]}, "Information Extractor": {"main": [[{"node": "AI Output + Prev Data", "type": "main", "index": 0}]]}, "Html Template for our Email": {"main": [[{"node": "The composed E-Mail + Prev Data", "type": "main", "index": 0}]]}, "Some Options for the Campaign": {"main": [[{"node": "Information Extractor", "type": "main", "index": 0}, {"node": "AI Output + Prev Data", "type": "main", "index": 1}]]}, "The composed E-Mail + Prev Data": {"main": [[{"node": "Send Email", "type": "main", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "Download dummy data", "type": "main", "index": 0}]]}, "Coupon them or not to Coupon them": {"main": [[{"node": "Html Template for our Email", "type": "main", "index": 0}, {"node": "The composed E-Mail + Prev Data", "type": "main", "index": 1}], [{"node": "Fake coupon", "type": "main", "index": 0}, {"node": "The composed E-Mail with Coupon + Prev Data", "type": "main", "index": 0}]]}, "Html Template for our Email with a Coupon": {"main": [[{"node": "The composed E-Mail with Coupon + Prev Data", "type": "main", "index": 1}]]}, "The composed E-Mail with Coupon + Prev Data": {"main": [[{"node": "Send Email", "type": "main", "index": 0}]]}}}