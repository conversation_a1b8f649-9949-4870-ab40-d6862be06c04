{"nodes": [{"name": "Notion", "type": "n8n-nodes-base.notion", "position": [1050, 300], "parameters": {"blockUi": {"blockValues": [{"textContent": "=Name: {{$json[\"display_name\"]}}\nPersonality: {{$json[\"personality_analysis\"][\"summary\"][\"ocean\"][\"description\"].join(', ')}}, {{$json[\"personality_analysis\"][\"summary\"][\"disc\"][\"description\"].join(', ')}}\nOpenness: {{$json[\"personality_analysis\"][\"ocean_assessment\"][\"openness\"][\"level\"]}} {{$json[\"personality_analysis\"][\"ocean_assessment\"][\"openness\"][\"score\"]}}\nCalculativeness: {{$json[\"personality_analysis\"][\"disc_assessment\"][\"calculativeness\"][\"level\"]}} {{$json[\"personality_analysis\"][\"disc_assessment\"][\"calculativeness\"][\"score\"]}}"}]}, "resource": "databasePage", "databaseId": "", "propertiesUi": {"propertyValues": [{"key": "Name|title", "title": "={{$json[\"display_name\"]}}"}]}}, "credentials": {"notionApi": ""}, "typeVersion": 1}, {"name": "Humantic AI", "type": "n8n-nodes-base.humanticAi", "position": [650, 300], "parameters": {"userId": "={{$json[\"payload\"][\"questions_and_responses\"][\"1_response\"]}}"}, "credentials": {"humanticAiApi": "humantic"}, "typeVersion": 1}, {"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.calendlyTrigger", "position": [450, 300], "webhookId": "6d38c1f6-42ee-4f44-b424-20943075087b", "parameters": {"events": ["invitee.created"]}, "credentials": {"calendlyApi": ""}, "typeVersion": 1}, {"name": "Humantic AI1", "type": "n8n-nodes-base.humanticAi", "position": [850, 300], "parameters": {"userId": "={{$json[\"results\"][\"userid\"]}}", "options": {}, "operation": "get"}, "credentials": {"humanticAiApi": ""}, "typeVersion": 1}], "connections": {"Humantic AI": {"main": [[{"node": "Humantic AI1", "type": "main", "index": 0}]]}, "Humantic AI1": {"main": [[{"node": "Notion", "type": "main", "index": 0}]]}, "Calendly Trigger": {"main": [[{"node": "Humantic AI", "type": "main", "index": 0}]]}}}