{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "Function", "type": "n8n-nodes-base.function", "position": [450, 300], "parameters": {"functionCode": "return [\n  {\n    json: {\n      id: 0,\n    }\n  },\n  {\n    json: {\n      id: 1,\n    }\n  }\n];"}, "typeVersion": 1}, {"name": "IF", "type": "n8n-nodes-base.if", "position": [650, 300], "parameters": {"conditions": {"number": [{"value1": "={{$node[\"Function\"].json[\"id\"]}}", "operation": "equal"}]}}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [850, 200], "parameters": {"values": {"string": [{"name": "name", "value": "n8n"}]}, "options": {}}, "typeVersion": 1}, {"name": "Set1", "type": "n8n-nodes-base.set", "position": [850, 400], "parameters": {"values": {"string": [{"name": "name", "value": "nodemation"}]}, "options": {}}, "typeVersion": 1}], "connections": {"IF": {"main": [[{"node": "Set", "type": "main", "index": 0}], [{"node": "Set1", "type": "main", "index": 0}]]}, "Function": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Function", "type": "main", "index": 0}]]}}}