{"id": "Q8On8rR6BkmPzDUd", "meta": {"instanceId": "f57770b08f6a574802832e927ed1b0063c627ffc5b95965abf0d4a7396150138"}, "name": "chrome extension backend with AI", "tags": [], "nodes": [{"id": "0f38fe62-36d9-43da-a992-a3981377e89e", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [-220, -20], "webhookId": "e9a97dd5-f1e7-4d5b-a6f1-be5f0c9eb96c", "parameters": {"path": "e9a97dd5-f1e7-4d5b-a6f1-be5f0c9eb96c", "options": {}, "httpMethod": "POST", "responseMode": "responseNode"}, "typeVersion": 2}, {"id": "83959562-edf5-4d37-bd11-47186c6a31c7", "name": "OpenAI", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [-40, -20], "parameters": {"text": "You are an expert financial analyst tasked with providing an advanced technical analyses of a stock or crypto currency chart provided. Your analysis will be based on various technical indicators and will provide simple insights for novice traders. Just explain to traders were you expect the market is moving. Also warn them this is not a binding advice. Make sure to explain everything in infant language.", "modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "resource": "image", "inputType": "base64", "operation": "analyze"}, "credentials": {"openAiApi": {"id": "8MS1muoK4z86fxUs", "name": "OpenAi account"}}, "typeVersion": 1.7}, {"id": "c6f1f833-7ba3-49c5-86df-f586e6bb5975", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [140, -20], "parameters": {"options": {}, "respondWith": "text", "responseBody": "={{ $json.content }}"}, "typeVersion": 1.1}, {"id": "e3a38a76-283b-4567-a8da-315ef1e2bc4f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-260, -140], "parameters": {"width": 620, "height": 300, "content": "## N8N en OpenAI image analyser"}, "typeVersion": 1}, {"id": "8e7e26db-8767-4727-ab0c-900b50a73411", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-80, 180], "parameters": {"color": 5, "height": 340, "content": "## AI prompt\nYou are an expert financial analyst tasked with providing an advanced technical analyses of a stock or crypto currency chart provided. Your analysis will be based on various technical indicators and will provide simple insights for novice traders. Just explain to traders were you expect the market is moving. Also warn them this is not a binding advice. Make sure to explain everything in infant language."}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "caf32442-e9c5-466a-8888-9abd2c1b3449", "connections": {"OpenAI": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}}}