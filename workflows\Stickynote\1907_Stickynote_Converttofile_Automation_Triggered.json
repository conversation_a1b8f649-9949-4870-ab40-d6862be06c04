{"id": "lStrENIdqN2WyGqW", "meta": {"instanceId": "7dad74485e3e05b018ebcb1de30d0069d50b5085ff62446e7e84ef96b35d0508", "templateCredsSetupCompleted": true}, "name": "Business Canvas Generator", "tags": [], "nodes": [{"id": "f9083875-e460-46ba-8f86-f2c66402e161", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-300, -1280], "webhookId": "5ac01b33-5538-4c54-b1a1-33ecc9c40a29", "parameters": {"public": true, "options": {}, "initialMessages": "Hi there! 👋\nPlease tell me everything about your business, and I will help you create the business canvas."}, "typeVersion": 1.1}, {"id": "ff613255-761e-472f-a09b-58e6181571f1", "name": "Key Partners Generator", "type": "@n8n/n8n-nodes-langchain.agent", "position": [400, -500], "parameters": {"text": "=Act as a strategic business analyst. Based on the idea and goals I give you, list 10 key insights for the \"Key Partners\" section of the Business Model Canvas. Be sure to cover the following questions:\n\n- Who are our key partners?\n- Who are our key suppliers?\n- Which key resources are we acquiring from partners?\n- Which key activities do our partners perform?\n- Motivation for partnerships: Optimization, risk reduction, resource access\n\nFormat your answer as bullet points, separated by a pipe symbol. Write only the points without numbering or explanations or titling, just body bullet points, and each item of bullet points should be less than 10 words, preferably 4-5 words (each of the should be meaningfull.\n\nthe only acceptable output structure is like this:\npoint1 | point2 | point3 ...\n\ndo not include header or title \n\nThis is the goal and idea of the project : \n{{ $json.chatInput }}", "options": {}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "97ffa849-cdaf-492e-a978-425d6a50f0d0", "name": "Key Activities Generator", "type": "@n8n/n8n-nodes-langchain.agent", "position": [400, -160], "parameters": {"text": "=Act as a strategic business analyst. Based on the business idea I gave you, generate 6-7 insights for the \"Key Activities\" section of the Business Model Canvas. Consider the following questions:\n\n- What key activities do our value propositions require?\n- What key activities are needed for our distribution channels?\n- What activities support customer relationships?\n- What activities support our revenue streams?\n\nAlso include examples based on activity type:\n- Production\n- Problem Solving\n- Platform/Network\n\nFormat your answer as bullet points, separated by a pipe symbol. Write only the points without numbering or explanations or titling, just body bullet points, and each item of bullet points should be less than 10 words, preferably 4-5 words (each of the should be meaningfull.\n\nthe only acceptable output structure is like this:\npoint1 | point2 | point3 ...\n\ndo not include header or title, do not use any enter (\\n)\n\nThis is the goal and idea of the project : \n{{ $json.chatInput }}", "options": {}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "64c94d6a-9ed1-4335-ac4d-03a69a434245", "name": "Value Proposition Generator", "type": "@n8n/n8n-nodes-langchain.agent", "position": [400, 140], "parameters": {"text": "=You're a value-driven strategist. Based on the provided business idea, write 6-7 concise bullet points that define the \"Value Proposition\" section of the Business Model Canvas. Address these key questions:\n\n- What value do we deliver to the customer?\n- What customer problems are we solving?\n- What bundles of products/services are we offering?\n- What needs are we satisfying?\n\nUse these attributes to shape your ideas:\n- Newness, Performance, Customization, Job completion, Design, Brand/Status\n- Price, Cost reduction, Risk reduction, Accessibility, Convenience\n\nFormat your answer as bullet points, separated by a pipe symbol. Write only the points without numbering or explanations or titling, just body bullet points, and each item of bullet points should be less than 10 words, preferably 4-5 words (each of the should be meaningfull.\n\nthe only acceptable output structure is like this:\npoint1 | point2 | point3 ...\n\ndo not include header or title, do not use any enter (\\n)\n\nThis is the goal and idea of the project : \n{{ $json.chatInput }}", "options": {}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "abcc2cd2-a87d-443a-90b1-5d107016bb0f", "name": "Customer Relationship Generator", "type": "@n8n/n8n-nodes-langchain.agent", "position": [400, 440], "parameters": {"text": "=As a customer relationship strategist, provide 6-7 key approaches for the \"Customer Relationship\" section of the Business Model Canvas, based on the business idea. Reflect on these questions:\n\n- What type of relationship does each customer segment expect?\n- What relationships have we already established?\n- How are these relationships integrated with the rest of our business model?\n- What is the cost of maintaining these relationships?\n\nYou may use formats like:\n- Personal assistance, Self-service, Automated services, Communities, Co-creation\n\nFormat your answer as bullet points, separated by a pipe symbol. Write only the points without numbering or explanations or titling, just body bullet points, and each item of bullet points should be less than 10 words, preferably 4-5 words (each of the should be meaningfull.\n\nthe only acceptable output structure is like this:\npoint1 | point2 | point3 ...\n\ndo not include header or title, do not use any enter (\\n)\nThis is the goal and idea of the project : \n{{ $json.chatInput }}", "options": {}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "d3fa4f7f-0184-43bb-a3d8-62bd04d3b620", "name": "Customer Segments Generator", "type": "@n8n/n8n-nodes-langchain.agent", "position": [400, 740], "parameters": {"text": "=Act as a segmentation expert. Based on the business idea provided, define 6-7 customer segments for the \"Customer Segments\" section of the Business Model Canvas. Make sure your suggestions address:\n\n- For whom are we creating value?\n- Who are our most important customers?\n\nIncorporate examples like:\n- Mass market, Niche market, Segmented, Diversified, Multi-sided platforms\n\nFormat your answer as bullet points, separated by a pipe symbol. Write only the points without numbering or explanations or titling, just body bullet points, and each item of bullet points should be less than 10 words, preferably 4-5 words (each of the should be meaningfull.\n\nthe only acceptable output structure is like this:\npoint1 | point2 | point3 ...\n\ndo not include header or title, do not use any enter (\\n)\n\nThis is the goal and idea of the project : \n{{ $json.chatInput }}", "options": {}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "1772d38c-e464-4b05-9276-67a982520ab1", "name": "<PERSON><PERSON><PERSON> Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOllama", "position": [-260, 2560], "parameters": {"model": "llama3.1:latest", "options": {}}, "credentials": {"ollamaApi": {"id": "FQ4BFsQ96rFv3C4V", "name": "Ollama account"}}, "typeVersion": 1}, {"id": "8b9e484a-7710-4447-b007-93ea3a7af39d", "name": "Key Resources Generator", "type": "@n8n/n8n-nodes-langchain.agent", "position": [400, 1040], "parameters": {"text": "=You're an operational strategist. Based on the business idea, generate 6-7 bullet points for the \"Key Resources\" section of the Business Model Canvas. Answer:\n\n- What key resources does our value proposition require?\n- What resources are needed for our distribution channels, customer relationships, revenue streams?\n\nConsider:\n- Physical, Intellectual (e.g. patents, data), Human, Financial\n\nFormat your answer as bullet points, separated by a pipe symbol. Write only the points without numbering or explanations or titling, just body bullet points, and each item of bullet points should be less than 10 words, preferably 4-5 words (each of the should be meaningfull.\n\nthe only acceptable output structure is like this:\npoint1 | point2 | point3 ...\n\ndo not include header or title \n\nThis is the goal and idea of the project : \n{{ $json.chatInput }}", "options": {}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "89f21f63-0f82-4cef-b6ac-231dc0262406", "name": "Channels Generator", "type": "@n8n/n8n-nodes-langchain.agent", "position": [400, 1380], "parameters": {"text": "=You're a marketing strategist. Provide 6-7 channel strategies for the \"Channels\" section of the Business Model Canvas, using the business idea. Answer:\n\n- Through which channels do customers want to be reached?\n- How are we reaching them now?\n- How are channels integrated?\n- Which channels work best?\n- Which are most cost-efficient?\n- How are we integrating them with customer routines?\n\nFormat your answer as bullet points, separated by a pipe symbol. Write only the points without numbering or explanations or titling, just body bullet points, and each item of bullet points should be less than 10 words, preferably 4-5 words (each of the should be meaningfull.\n\nthe only acceptable output structure is like this:\npoint1 | point2 | point3 ...\n\ndo not include header or title, do not use any enter (\\n)\n\nThis is the goal and idea of the project : \n{{ $json.chatInput }}", "options": {}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "b1617750-2264-4199-8ded-277dae92ae2b", "name": "Cost Structure Generator", "type": "@n8n/n8n-nodes-langchain.agent", "position": [400, 1680], "parameters": {"text": "=You're a finance strategist. Based on the business idea, provide 6-7 main cost drivers for the \"Cost Structure\" section of the Business Model Canvas. Include insights related to:\n\n- Most important costs in our business model\n- Most expensive key resources\n- Most expensive key activities\n\n\n\n\nFormat your answer as bullet points, separated by a pipe symbol. Write only the points without numbering or explanations or titling, just body bullet points, and each item of bullet points should be less than 10 words, preferably 4-5 words (each of the should be meaningfull.\n\nthe only acceptable output structure is like this:\npoint1 | point2 | point3 ...\n\ndo not include header or title, do not use any enter (\\n)\n\nThis is the goal and idea of the project : \n{{ $json.chatInput }}", "options": {}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "3b7d55d4-3f50-428b-a018-620765e530fb", "name": "Title Generator", "type": "@n8n/n8n-nodes-langchain.agent", "position": [400, -820], "parameters": {"text": "=Make a simple business name with this idea (maximum 5 words)\n{{ $json.chatInput }}\n\nonly write the name, do not add anything to the output.", "options": {}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "134a828c-ae3a-49b8-b337-52d3cf5f35d7", "name": "Revenue Streams Generator", "type": "@n8n/n8n-nodes-langchain.agent", "position": [400, 1980], "parameters": {"text": "=Act as a monetization expert. Based on the business idea, generate 6-7 revenue strategies for the \"Revenue Streams\" section of the Business Model Canvas. Reflect on:\n\n- What value are customers willing to pay for?\n- What are they paying for now?\n- How do they pay?\n- How would they prefer to pay?\n- What is the contribution of each stream?\n\nFormat your answer as bullet points, separated by a pipe symbol. Write only the points without numbering or explanations or titling, just body bullet points, and each item of bullet points should be less than 10 words, preferably 4-5 words (each of the should be meaningfull.\n\nthe only acceptable output structure is like this:\npoint1 | point2 | point3 ...\n\ndo not include header or title, do not use any enter (\\n)\nThis is the goal and idea of the project : \n{{ $json.chatInput }}", "options": {}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "8b2f5b1a-32fa-4fbd-a6b1-4147e3b64ed5", "name": "Key Partners HTML Transformer", "type": "n8n-nodes-base.code", "position": [760, -500], "parameters": {"jsCode": "function formatToHtmlList(inputString) {\n  const items = inputString.split('|').map(item => item.trim());\n\n  let htmlOutput = '';\n  for (let i = 0; i < items.length; i++) {\n    if (items[i]) {\n      htmlOutput += `<p>• ${items[i]}</p>`;\n    }\n  }\n\n  return htmlOutput;\n}\n\nconst input = $input.first().json.output\n\nconst result = formatToHtmlList(input);\nreturn {\n  \"key_partners\":result\n}"}, "typeVersion": 2}, {"id": "64e98d28-ef36-433d-b114-4fa33ad646d4", "name": "Key Activities HTML Transformer", "type": "n8n-nodes-base.code", "position": [760, -160], "parameters": {"jsCode": "function formatToHtmlList(inputString) {\n  const items = inputString.split('|').map(item => item.trim());\n\n  let htmlOutput = '';\n  for (let i = 0; i < items.length; i++) {\n    if (items[i]) {\n      htmlOutput += `<p>• ${items[i]}</p>`;\n    }\n  }\n\n  return htmlOutput;\n}\n\nconst input = $input.first().json.output\n\nconst result = formatToHtmlList(input);\nreturn {\n  \"key_activities\":result\n}"}, "typeVersion": 2}, {"id": "2f929221-7e20-4ceb-b715-79b434d6357e", "name": "Values proposition HTML Transformer", "type": "n8n-nodes-base.code", "position": [760, 140], "parameters": {"jsCode": "function formatToHtmlList(inputString) {\n  const items = inputString.split('|').map(item => item.trim());\n\n  let htmlOutput = '';\n  for (let i = 0; i < items.length; i++) {\n    if (items[i]) {\n      htmlOutput += `<p>• ${items[i]}</p>`;\n    }\n  }\n\n  return htmlOutput;\n}\n\nconst input = $input.first().json.output\n\nconst result = formatToHtmlList(input);\nreturn {\n  \"value_proposition\":result\n}"}, "typeVersion": 2}, {"id": "4befcdf5-0fd1-44fa-8d68-9b82e50d1f57", "name": "Customer Relationship HTML Transformer", "type": "n8n-nodes-base.code", "position": [760, 440], "parameters": {"jsCode": "function formatToHtmlList(inputString) {\n  const items = inputString.split('|').map(item => item.trim());\n\n  let htmlOutput = '';\n  for (let i = 0; i < items.length; i++) {\n    if (items[i]) {\n      htmlOutput += `<p>${items[i]}</p>`;\n    }\n  }\n\n  return htmlOutput;\n}\n\nconst input = $input.first().json.output\n\nconst result = formatToHtmlList(input);\nreturn {\n  \"customer_relationship\":result\n}"}, "typeVersion": 2}, {"id": "6700656b-0946-497c-9fe5-a55f75455e4e", "name": "Customer Segments HTML Transformer", "type": "n8n-nodes-base.code", "position": [760, 740], "parameters": {"jsCode": "function formatToHtmlList(inputString) {\n  const items = inputString.split('|').map(item => item.trim());\n\n  let htmlOutput = '';\n  for (let i = 0; i < items.length; i++) {\n    if (items[i]) {\n      htmlOutput += `<p>• ${items[i]}</p>`;\n    }\n  }\n\n  return htmlOutput;\n}\n\nconst input = $input.first().json.output\n\nconst result = formatToHtmlList(input);\nreturn {\n  \"customer_segments\":result\n}"}, "typeVersion": 2}, {"id": "3e0797c8-6fa3-40d8-b895-6250f952abfa", "name": "Key Resources HTML Transformer", "type": "n8n-nodes-base.code", "position": [760, 1040], "parameters": {"jsCode": "function formatToHtmlList(inputString) {\n  const items = inputString.split('|').map(item => item.trim());\n\n  let htmlOutput = '';\n  for (let i = 0; i < items.length; i++) {\n    if (items[i]) {\n      htmlOutput += `<p>• ${items[i]}</p>`;\n    }\n  }\n\n  return htmlOutput;\n}\n\nconst input = $input.first().json.output\n\nconst result = formatToHtmlList(input);\nreturn {\n  \"key_resources\":result\n}"}, "typeVersion": 2}, {"id": "16542342-8a05-40c9-b5e7-5b0824da7850", "name": "Channels HTML Transformer", "type": "n8n-nodes-base.code", "position": [760, 1380], "parameters": {"jsCode": "function formatToHtmlList(inputString) {\n  const items = inputString.split('|').map(item => item.trim());\n\n  let htmlOutput = '';\n  for (let i = 0; i < items.length; i++) {\n    if (items[i]) {\n      htmlOutput += `<p>• ${items[i]}</p>`;\n    }\n  }\n\n  return htmlOutput;\n}\n\nconst input = $input.first().json.output\n\nconst result = formatToHtmlList(input);\nreturn {\n  \"channels\":result\n}"}, "typeVersion": 2}, {"id": "2513f7c3-57da-4d1c-a492-230e3124d5d9", "name": "Revenue streams HTML Transformer", "type": "n8n-nodes-base.code", "position": [760, 1980], "parameters": {"jsCode": "function formatToHtmlList(inputString) {\n  const items = inputString.split('|').map(item => item.trim());\n\n  let htmlOutput = '';\n  for (let i = 0; i < items.length; i++) {\n    if (items[i]) {\n      htmlOutput += `<p>• ${items[i]}</p>`;\n    }\n  }\n\n  return htmlOutput;\n}\n\nconst input = $input.first().json.output\n\nconst result = formatToHtmlList(input);\nreturn {\n  \"revenue_streams\":result\n}"}, "typeVersion": 2}, {"id": "e79ac1ac-fac8-43a9-9e7a-41f92f40df26", "name": "Code1", "type": "n8n-nodes-base.code", "position": [760, -820], "parameters": {"jsCode": "\nconst input = $input.first().json.output.replaceAll(\"\\n\",\"\")\nreturn {\n  \"title\":input\n}"}, "typeVersion": 2}, {"id": "4ba8158c-44f5-4dbb-a143-cd227fadb08e", "name": "Cost Structure HTML Transformer", "type": "n8n-nodes-base.code", "position": [760, 1680], "parameters": {"jsCode": "function formatToHtmlList(inputString) {\n  const items = inputString.split('|').map(item => item.trim());\n\n  let htmlOutput = '';\n  for (let i = 0; i < items.length; i++) {\n    if (items[i]) {\n      htmlOutput += `<p>• ${items[i]}</p>`;\n    }\n  }\n\n  return htmlOutput;\n}\n\nconst input = $input.first().json.output\n\nconst result = formatToHtmlList(input);\nreturn {\n  \"cost_structure\":result\n}"}, "typeVersion": 2}, {"id": "f80c65a9-5f1d-4979-9228-fdb8f3e6bc71", "name": "Turn to HTML", "type": "n8n-nodes-base.code", "position": [1740, 620], "parameters": {"jsCode": "const input = $input.all()\n// Simple merge\n\nconst output = {\n  title: input[0].json.title,\n  key_partners: input[1].json.key_partners,\n  key_activities: input[2].json.key_activities, // combining both\n  value_proposition: input[3].json.value_proposition,\n  customer_relationship: input[4].json.customer_relationship,\n  customer_segments: input[5].json.customer_segments,\n  key_resources: input[6].json.key_resources,\n  channels: input[7].json.channels,\n  cost_structure : input[8].json.cost_structure,\n  revenue_streams: input[9].json.revenue_streams\n};\n\n\nconsole.log(output);\nreturn {\n  \"final_html\": `<!DOCTYPE html> <html lang=\"en\"> <head> <meta charset=\"utf-8\" /> <title>Business Model Canvas</title> <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" /> <link href=\"https://fonts.googleapis.com/css?family=Headland+One\" rel=\"stylesheet\" /> <style> body { font-family: sans-serif; margin-top: 2.5vh; padding: 10px; background-color: #f4f4f4; display: flex; justify-content: center; align-items: center; height: 95vh; /* Full viewport height */ overflow: hidden; } .container { height: 100%; /* Fit the height of the screen */ aspect-ratio: 297 / 210; /* Maintain A4 aspect ratio (landscape) */ background: #fff; margin-bottom: 20px; padding: 10px 10px 80px; /* Added extra padding at the bottom */ box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); border-radius: 8px; box-sizing: border-box; overflow: hidden; } h1 { text-align: center; font-size: 24px; /* Reduced font size */ margin-bottom: 20px; color: #333; } p { padding-top: 2px; } table { width: 100%; height: 100%; border-collapse: collapse; table-layout: fixed; } table td { border: 1px solid #ddd; padding: 5px; /* Reduced padding */ vertical-align: top; font-size: 14px; /* Reduced font size */ word-wrap: break-word; background-color: transparent; /* No background color */ } table td h4 { padding: 5px; margin: 0 0 5px; /* Reduced margin */ font-size: 17px; /* Reduced font size */ color: #1b1b1b; } table td p { margin: 3px 0; /* Reduced margin */ line-height: 1.2; /* Reduced line height */ color: #555; } table td p strong { color: #000; } /* Adjust row heights */ #business-model-canvas tr:first-child { height: 30%; /* Reduced height for the upper part */ } #business-model-canvas tr:nth-child(2) { height: 25%; /* Reduced height for the second row */ } #business-model-canvas tr:last-child { height: 25%; /* Increased height for the bottom part */ } /* Print-specific styles */ @media print { body { background: none; margin: 0; } .container { box-shadow: none; margin: 0; padding: 0; } table td { font-size: 11px; /* Further reduced font size for print */ } } </style> </head> <body> <div class=\"container\"> <h1>`+output.title+` Business Model Canvas</h1> <!-- Canvas --> <table id=\"business-model-canvas\" cellspacing=\"0\"> <!-- Upper part --> <tr> <td id=\"key-partners\" colspan=\"2\" rowspan=\"2\"> <h4>Key Partners</h4> `+output.key_partners+` </td> <td id=\"key-activities\" colspan=\"2\"> <h4>Key Activities</h4> `+output.key_activities+` </td> <td id=\"value-propositions\" colspan=\"2\" rowspan=\"2\"> <h4>Value Proposition</h4> `+output.value_proposition+` </td> <td id=\"customer-relationships\" colspan=\"2\"> <h4>Customer Relationships</h4> `+output.customer_relationship+` </td> <td id=\"customer-segments\" colspan=\"2\" rowspan=\"2\"> <h4>Customer Segments</h4> `+output.customer_segments+` </td> </tr> <!-- Lower part --> <tr> <td id=\"key-resources\" colspan=\"2\"> <h4>Key Resources</h4> `+output.key_resources+` </td> <td id=\"channels\" colspan=\"2\"> <h4>Channels</h4> `+output.channels+` </td> </tr> <tr> <td id=\"cost-structure\" colspan=\"5\"> <h4>Cost Structure</h4> `+output.cost_structure+` </td> <td id=\"revenue-streams\" colspan=\"5\"> <h4>Revenue Streams</h4> `+output.revenue_streams+` </td> </tr> </table> <!-- /Canvas --> </div> </body> </html>`,\n  \"title\" : $input.first().json.title\n}"}, "typeVersion": 2}, {"id": "7a5b2b06-75fc-4c27-971d-df3817100000", "name": "HTML code to HTML file", "type": "n8n-nodes-base.convertToFile", "position": [2020, 620], "parameters": {"options": {"fileName": "={{ $json.title }} BMC.html"}, "operation": "toText", "sourceProperty": "final_html", "binaryPropertyName": "="}, "typeVersion": 1.1}, {"id": "f565cf6a-ebd5-45ee-8e62-9aa6e1585e36", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-740, 2500], "parameters": {"color": 5, "width": 420, "height": 220, "content": "## 🔁 Changeable LLM Model\n\nThis template is powered by **Ollama LLM (LLaMA 3.1)** by default — but it’s fully flexible.\n\nYou can easily swap in any other LLM (like OpenAI, Claude, etc.) by updating the AI Agent nodes. No changes are required in the logic or formatting nodes — the system will work seamlessly."}, "typeVersion": 1}, {"id": "218ca6c2-8524-4f16-b23c-a744067b717f", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1840, 240], "parameters": {"color": 6, "width": 500, "height": 320, "content": "## How to get the output? \n\nOnce all nodes have finished processing, your complete Business Model Canvas will be available as a downloadable HTML file.\n\nSimply navigate to the final node titled **“HTML code to HTML file”** (at the end of the workflow). There, you’ll see two options:\n\n• **View** : to preview the HTML output directly in your browser\n\n• **Download** : to save the file locally for printing or sharing\n\n👉 Click on the **“Download”** button to retrieve your fully generated canvas file."}, "typeVersion": 1}, {"id": "361cf369-647e-4b89-9452-a822725f74cb", "name": "Merge All Data", "type": "n8n-nodes-base.merge", "position": [1520, 460], "parameters": {"numberInputs": 10}, "executeOnce": false, "typeVersion": 3.1, "alwaysOutputData": false}, {"id": "717d59b3-8986-4193-b8fb-7125c9cbb10a", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-760, 2780], "parameters": {"width": 460, "height": 200, "content": "## I'm here to help!\n\nIf you need assistance customizing the model or have feedback to share, please don’t hesitate to reach out. Your thoughts are important to me, and I'm eager to support you in any way I can. \n\n**📩 sinamirsha<PERSON><EMAIL>**"}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "782619e6-0ab1-4a22-b224-98b080614647", "connections": {"Code1": {"main": [[{"node": "Merge All Data", "type": "main", "index": 0}]]}, "Turn to HTML": {"main": [[{"node": "HTML code to HTML file", "type": "main", "index": 0}]]}, "Merge All Data": {"main": [[{"node": "Turn to HTML", "type": "main", "index": 0}]]}, "Title Generator": {"main": [[{"node": "Code1", "type": "main", "index": 0}]]}, "Ollama Chat Model": {"ai_languageModel": [[{"node": "Key Partners Generator", "type": "ai_languageModel", "index": 0}, {"node": "Key Activities Generator", "type": "ai_languageModel", "index": 0}, {"node": "Value Proposition Generator", "type": "ai_languageModel", "index": 0}, {"node": "Customer Relationship Generator", "type": "ai_languageModel", "index": 0}, {"node": "Customer Segments Generator", "type": "ai_languageModel", "index": 0}, {"node": "Key Resources Generator", "type": "ai_languageModel", "index": 0}, {"node": "Channels Generator", "type": "ai_languageModel", "index": 0}, {"node": "Cost Structure Generator", "type": "ai_languageModel", "index": 0}, {"node": "Title Generator", "type": "ai_languageModel", "index": 0}, {"node": "Revenue Streams Generator", "type": "ai_languageModel", "index": 0}]]}, "Channels Generator": {"main": [[{"node": "Channels HTML Transformer", "type": "main", "index": 0}]]}, "HTML code to HTML file": {"main": [[]]}, "Key Partners Generator": {"main": [[{"node": "Key Partners HTML Transformer", "type": "main", "index": 0}]]}, "Key Resources Generator": {"main": [[{"node": "Key Resources HTML Transformer", "type": "main", "index": 0}]]}, "Cost Structure Generator": {"main": [[{"node": "Cost Structure HTML Transformer", "type": "main", "index": 0}]]}, "Key Activities Generator": {"main": [[{"node": "Key Activities HTML Transformer", "type": "main", "index": 0}]]}, "Channels HTML Transformer": {"main": [[{"node": "Merge All Data", "type": "main", "index": 7}]]}, "Revenue Streams Generator": {"main": [[{"node": "Revenue streams HTML Transformer", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "Key Partners Generator", "type": "main", "index": 0}, {"node": "Value Proposition Generator", "type": "main", "index": 0}, {"node": "Customer Relationship Generator", "type": "main", "index": 0}, {"node": "Customer Segments Generator", "type": "main", "index": 0}, {"node": "Key Resources Generator", "type": "main", "index": 0}, {"node": "Channels Generator", "type": "main", "index": 0}, {"node": "Cost Structure Generator", "type": "main", "index": 0}, {"node": "Revenue Streams Generator", "type": "main", "index": 0}, {"node": "Title Generator", "type": "main", "index": 0}, {"node": "Key Activities Generator", "type": "main", "index": 0}]]}, "Customer Segments Generator": {"main": [[{"node": "Customer Segments HTML Transformer", "type": "main", "index": 0}]]}, "Value Proposition Generator": {"main": [[{"node": "Values proposition HTML Transformer", "type": "main", "index": 0}]]}, "Key Partners HTML Transformer": {"main": [[{"node": "Merge All Data", "type": "main", "index": 1}]]}, "Key Resources HTML Transformer": {"main": [[{"node": "Merge All Data", "type": "main", "index": 6}]]}, "Cost Structure HTML Transformer": {"main": [[{"node": "Merge All Data", "type": "main", "index": 8}]]}, "Customer Relationship Generator": {"main": [[{"node": "Customer Relationship HTML Transformer", "type": "main", "index": 0}]]}, "Key Activities HTML Transformer": {"main": [[{"node": "Merge All Data", "type": "main", "index": 2}]]}, "Revenue streams HTML Transformer": {"main": [[{"node": "Merge All Data", "type": "main", "index": 9}]]}, "Customer Segments HTML Transformer": {"main": [[{"node": "Merge All Data", "type": "main", "index": 5}]]}, "Values proposition HTML Transformer": {"main": [[{"node": "Merge All Data", "type": "main", "index": 3}]]}, "Customer Relationship HTML Transformer": {"main": [[{"node": "Merge All Data", "type": "main", "index": 4}]]}}}