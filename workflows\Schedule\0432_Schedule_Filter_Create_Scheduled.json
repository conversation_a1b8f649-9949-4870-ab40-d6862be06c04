{"meta": {"instanceId": "70aa73f4d8fa35284109864e170a85d0977ad8121a6294c5c305fbaee0d9e028"}, "nodes": [{"id": "8b2dad6b-9771-4351-bddc-819746cb04c1", "name": "Get leads created last 5 minutes", "type": "n8n-nodes-base.hubspot", "position": [160, 500], "parameters": {"resource": "company", "operation": "getRecentlyCreatedUpdated", "returnAll": true, "authentication": "oAuth2", "additionalFields": {"since": "={{ $now.minus({ \"minutes\": 5 }) }}"}}, "credentials": {"hubspotOAuth2Api": {"id": "KTv7XIF0qMaGfg8O", "name": "HubSpot account"}}, "typeVersion": 2}, {"id": "************************************", "name": "Notify sales rep", "type": "n8n-nodes-base.slack", "position": [700, 500], "parameters": {"text": "=New high-quality lead 🤑\n*Company Name*: {{ $json.properties.name.value }} \n*Website*: {{ $json.properties.website.value }}\n*Revenue*: {{ $json.properties.annualrevenue.value }}\n*Number of employees*: {{ $json.properties.numberofemployees.value }}", "user": {"__rl": true, "mode": "list", "value": "U0361884CU9", "cachedResultName": "ricardo"}, "select": "user", "otherOptions": {}, "authentication": "oAuth2"}, "credentials": {"slackOAuth2Api": {"id": "wpCG1E4YH2xiwWxK", "name": "Slack account"}}, "typeVersion": 2.1}, {"id": "2b12fb75-ec81-4d2c-a8bb-12ff2bb7e935", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-420, 460], "parameters": {"width": 257.**************, "height": 176.**************, "content": "## Setup\n1. Add `<PERSON><PERSON><PERSON>` and `Slack` credentials.\n2. Adjust polling time.\n3. Enable the workflow."}, "typeVersion": 1}, {"id": "5c4235de-c7fe-43fc-a351-69e928ba2673", "name": "Trigger every 5 minutes", "type": "n8n-nodes-base.scheduleTrigger", "position": [-100, 500], "parameters": {"rule": {"interval": [{"field": "minutes"}]}}, "typeVersion": 1.1}, {"id": "7af59e19-893a-477a-ba21-4c1c151ffea4", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [100, 400], "parameters": {"color": 7, "width": 225.**************, "height": 282.*************, "content": "`Since` parameter should match the polling time."}, "typeVersion": 1}, {"id": "123ad2e5-f4f2-4411-bf03-5668124b8757", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [380, 400], "parameters": {"color": 7, "width": 223.7628185364029, "height": 276.3308728978709, "content": "Adjust condition to filter leads by your desire condition. e.g, revenue, number of employees, etc."}, "typeVersion": 1}, {"id": "4263cd25-dcf3-4521-b716-0ce48d3b2c26", "name": "Keep the ones that make more than $5M in revenue", "type": "n8n-nodes-base.filter", "position": [440, 500], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "1b31b826-e87d-425f-a65d-370b4b20f7e1", "operator": {"type": "number", "operation": "gt"}, "leftValue": "={{ $json.properties.annualrevenue.value.toInt() }}", "rightValue": 5000000}]}}, "typeVersion": 2}, {"id": "ba1a4a6c-a734-45c7-bb05-df0810a2253b", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [640, 400], "parameters": {"color": 7, "width": 223.7628185364029, "height": 276.3308728978709, "content": "Send notification to slack with the desired information."}, "typeVersion": 1}], "pinData": {}, "connections": {"Trigger every 5 minutes": {"main": [[{"node": "Get leads created last 5 minutes", "type": "main", "index": 0}]]}, "Get leads created last 5 minutes": {"main": [[{"node": "Keep the ones that make more than $5M in revenue", "type": "main", "index": 0}]]}, "Keep the ones that make more than $5M in revenue": {"main": [[{"node": "Notify sales rep", "type": "main", "index": 0}]]}}}