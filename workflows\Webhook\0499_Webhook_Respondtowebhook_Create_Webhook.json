{"meta": {"instanceId": "dbd43d88d26a9e30d8aadc002c9e77f1400c683dd34efe3778d43d27250dde50"}, "nodes": [{"id": "646662d1-92dc-406a-8dc6-581a4a6d69cd", "name": "Customer Datastore (n8n training)", "type": "n8n-nodes-base.n8nTrainingCustomerDatastore", "position": [580, 660], "parameters": {"operation": "getAllPeople"}, "typeVersion": 1}, {"id": "4926678b-cd17-4e7a-b8af-db649f17e442", "name": "insert into variable", "type": "n8n-nodes-base.set", "position": [880, 660], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "de2360fb-1b29-4524-a035-1a76abf4ae2e", "name": "students", "type": "object", "value": "={{ $json }}"}]}}, "typeVersion": 3.3}, {"id": "43c716b1-626e-47cd-b1df-1c7ca486fcd4", "name": "Aggregate variable", "type": "n8n-nodes-base.aggregate", "position": [1060, 660], "parameters": {"options": {}, "fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "students"}]}}, "typeVersion": 1}, {"id": "325b44ba-5297-496a-8351-4cc00b34e2f2", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [220, 540], "parameters": {"color": 4, "width": 218.82012248136226, "height": 321.21203744835316, "content": "### Flow starts when receiving a get http call"}, "typeVersion": 1}, {"id": "a57c08ca-60bd-43e5-aefa-269b05bc0f01", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [480, 540], "parameters": {"color": 7, "width": 314.179182099464, "height": 320.43858635231027, "content": "### Here you can change to your database node"}, "typeVersion": 1}, {"id": "becb82a0-d2bc-40d3-a293-7f75939a8878", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [840, 540], "parameters": {"color": 7, "width": 364.9476455365474, "height": 318.43858635231027, "content": "### Step required to transform data for response to flutterflow"}, "typeVersion": 1}, {"id": "d76acd26-5c0c-4b1e-b673-b63697c9c98a", "name": "On new flutterflow call", "type": "n8n-nodes-base.webhook", "position": [280, 660], "webhookId": "203c3219-5089-405b-8704-3718f7158220", "parameters": {"path": "203c3219-5089-405b-8704-3718f7158220", "options": {}, "responseMode": "responseNode"}, "typeVersion": 2}, {"id": "05a1efd1-beb2-4953-90c7-6e1df98b74f8", "name": "Respond to flutterflow", "type": "n8n-nodes-base.respondToWebhook", "position": [1280, 660], "parameters": {"options": {}, "respondWith": "json", "responseBody": "={{ $json }}"}, "typeVersion": 1.1}, {"id": "c4272529-1d96-48b9-b390-6bf847af7454", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [220, 300], "parameters": {"width": 457, "height": 201, "content": "## Low-code API for Flutterflow apps\n### Set up\n1. <PERSON><PERSON> the Webhook URL from `On new flutterflow call` step. This is the URL you will make a GET request to in FlutterFlow.\n2. Replace the \"Customer Datastore\" step with your own data source or any other necessary workflow steps to complete your API endpoint's task."}, "typeVersion": 1}], "pinData": {}, "connections": {"Aggregate variable": {"main": [[{"node": "Respond to flutterflow", "type": "main", "index": 0}]]}, "insert into variable": {"main": [[{"node": "Aggregate variable", "type": "main", "index": 0}]]}, "On new flutterflow call": {"main": [[{"node": "Customer Datastore (n8n training)", "type": "main", "index": 0}]]}, "Customer Datastore (n8n training)": {"main": [[{"node": "insert into variable", "type": "main", "index": 0}]]}}}