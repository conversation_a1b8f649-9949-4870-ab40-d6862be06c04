{"nodes": [{"name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [590, 400], "webhookId": "822cce61-ff5f-4cea-b8ba-1822651786e3", "parameters": {"path": "822cce61-ff5f-4cea-b8ba-1822651786e3", "options": {}, "httpMethod": "POST"}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [780, 400], "parameters": {"values": {"string": [{"name": "<PERSON><PERSON><PERSON>", "value": "n8n-rocks"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "Mattermost", "type": "n8n-nodes-base.mattermost", "position": [990, 400], "parameters": {"message": "=Join me in a video call:", "channelId": "={{$node[\"Webhook\"].json[\"body\"][\"channel_id\"]}}", "attachments": [{"title": "=https://whereby.com/{{$json[$node[\"Webhook\"].json[\"body\"][\"user_name\"]]}}", "title_link": "=https://whereby.com/{{$json[$node[\"Webhook\"].json[\"body\"][\"user_name\"]]}}"}], "otherOptions": {}}, "credentials": {"mattermostApi": "mm_creds"}, "typeVersion": 1}], "connections": {"Set": {"main": [[{"node": "Mattermost", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}}