{"name": "Create an Onfleet task when a file in Google Drive is updated", "nodes": [{"name": "Google Drive Trigger", "type": "n8n-nodes-base.googleDriveTrigger", "position": [460, 300], "parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFile", "fileToWatch": "<some_id>"}, "typeVersion": 1}, {"name": "Onfleet", "type": "n8n-nodes-base.onfleet", "position": [680, 300], "parameters": {"operation": "create", "additionalFields": {}}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Google Drive Trigger": {"main": [[{"node": "Onfleet", "type": "main", "index": 0}]]}}}