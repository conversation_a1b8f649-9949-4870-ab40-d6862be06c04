{"id": "166", "name": "Receive messages from a topic and send an SMS", "nodes": [{"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.kafkaTrigger", "position": [490, 260], "parameters": {"topic": "topic_test", "groupId": "n8n", "options": {"jsonParseMessage": true}}, "credentials": {"kafka": "kafka"}, "typeVersion": 1}, {"name": "IF", "type": "n8n-nodes-base.if", "position": [690, 260], "parameters": {"conditions": {"number": [{"value1": "={{$node[\"Kafka Trigger\"].json[\"message\"][\"temp\"]}}", "value2": 50, "operation": "larger"}]}}, "typeVersion": 1}, {"name": "Vonage", "type": "n8n-nodes-base.von<PERSON>", "position": [890, 160], "parameters": {"from": "Vonage APIs", "message": "=Alert!\nThe value of temp is {{$node[\"Kafka Trigger\"].json[\"message\"][\"temp\"]}}.", "additionalFields": {}}, "credentials": {"vonageApi": "vonage"}, "typeVersion": 1}, {"name": "NoOp", "type": "n8n-nodes-base.noOp", "position": [890, 360], "parameters": {}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"IF": {"main": [[{"node": "Vonage", "type": "main", "index": 0}], [{"node": "NoOp", "type": "main", "index": 0}]]}, "Kafka Trigger": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}}}