{"id": "UuuCIDvTNnloIlvq", "meta": {"instanceId": "885b4fb4a6a9c2cb5621429a7b972df0d05bb724c20ac7dac7171b62f1c7ef40", "templateCredsSetupCompleted": true}, "name": "Automate Etsy Data Mining with Bright Data Scrape & Google Gemini", "tags": [{"id": "Kujft2FOjmOVQAmJ", "name": "Engineering", "createdAt": "2025-04-09T01:31:00.558Z", "updatedAt": "2025-04-09T01:31:00.558Z"}, {"id": "ddPkw7Hg5dZhQu2w", "name": "AI", "createdAt": "2025-04-13T05:38:08.053Z", "updatedAt": "2025-04-13T05:38:08.053Z"}], "nodes": [{"id": "f369feaf-4782-4411-9d08-fe91b9ffd97e", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [200, -555], "parameters": {}, "typeVersion": 1}, {"id": "231bae3c-c27e-49fc-b878-2d5cc1e14c5a", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [200, -1020], "parameters": {"width": 400, "height": 300, "content": "## Note\n\nDeals with the Esty web scraping by utilizing the Bright Data Web Unlocker Product.\n\nThe Information Extraction node being used to demonstrate the usage of the N8N AI capabilities.\n\n**Please make sure to set the Indeed search query and update the Webhook Notification URL**"}, "typeVersion": 1}, {"id": "f568de40-b389-41f9-afe9-5e09a291c367", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [640, -1020], "parameters": {"width": 480, "height": 300, "content": "## LLM Usages\n\nGoogle Gemini Flash Exp model is being used.\n\nBasic LLM Chain Data Extractor."}, "typeVersion": 1}, {"id": "4f1db865-a0cb-4978-9c7d-fde448bd978a", "name": "Set Esty Search Query", "type": "n8n-nodes-base.set", "position": [420, -555], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "3aedba66-f447-4d7a-93c0-8158c5e795f9", "name": "url", "type": "string", "value": "https://www.etsy.com/search?q=wall+art+for+mum&order=date_desc&page=1&ref=pagination"}, {"id": "4e7ee31d-da89-422f-8079-2ff2d357a0ba", "name": "zone", "type": "string", "value": "web_unlocker1"}]}}, "typeVersion": 3.4}, {"id": "4cb51368-bb69-4d99-a0b6-e8e8013f1dfd", "name": "Perform Esty Web Request", "type": "n8n-nodes-base.httpRequest", "position": [640, -680], "parameters": {"url": "https://api.brightdata.com/request", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "zone", "value": "={{ $json.zone }}"}, {"name": "url", "value": "={{ $json.url }}?product=unlocker&method=api"}, {"name": "format", "value": "raw"}, {"name": "data_format", "value": "markdown"}]}, "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{}]}}, "credentials": {"httpHeaderAuth": {"id": "kdbqXuxIR8qIxF7y", "name": "Header Auth account"}}, "typeVersion": 4.2}, {"id": "9fb7bdc5-ba64-4df4-89b4-a3207e7f6d0e", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [948, -460], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "YeO7dHZnuGBVQKVZ", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "1f95576d-e243-481d-9d5f-308764d8ea4b", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [1460, -680], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "47f23aa1-63ee-49e3-a465-283c7ab71b76", "name": "Perform Esty web request over the loop", "type": "n8n-nodes-base.httpRequest", "position": [1680, -560], "parameters": {"url": "https://api.brightdata.com/request", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "zone", "value": "=web_unlocker1"}, {"name": "url", "value": "={{ $json.url }}&product=unlocker"}, {"name": "format", "value": "raw"}, {"name": "data_format", "value": "markdown"}]}, "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{}]}}, "credentials": {"httpHeaderAuth": {"id": "kdbqXuxIR8qIxF7y", "name": "Header Auth account"}}, "typeVersion": 4.2}, {"id": "0b5ea206-a5a0-49b5-8f53-10b4dec5806c", "name": "Initiate a Webhook Notification for the extracted data", "type": "n8n-nodes-base.httpRequest", "position": [2320, -560], "parameters": {"url": "https://webhook.site/3c36d7d1-de1b-4171-9fd3-643ea2e4dd76", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "summary", "value": "={{ $json.output }}"}]}}, "typeVersion": 4.2}, {"id": "a164b90b-f44c-4862-b010-d515926774c7", "name": "Extract Item List with the Product Info", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [1920, -560], "parameters": {"text": "=Extract the product info in JSON\n\n{{ $json.data }}", "options": {}, "schemaType": "fromJson", "jsonSchemaExample": "[{\n    \"image\": \"https://i.etsystatic.com/********/r/il/8f3bba/**********/il_fullxfull.**********_n9el.jpg\",\n    \"name\": \"Custom Coffee Mug with Photo\",\n    \"url\": \"https://www.etsy.com/listing/**********/custom-coffee-mug-with-photo\",\n    \"brand\": {\n        \"@type\": \"Brand\",\n        \"name\": \"TheGiftBucks\"\n    },\n    \"offers\": {\n        \"@type\": \"Offer\",\n        \"price\": \"14.99\",\n        \"priceCurrency\": \"USD\"\n    }\n}]"}, "typeVersion": 1}, {"id": "c3798c64-ac53-44c8-ba91-8fe33377113d", "name": "Google Gemini Chat Model for product info", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [2000, -300], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "YeO7dHZnuGBVQKVZ", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "11e4ae42-d2e1-4a4b-adcf-382f9e494431", "name": "Extract Paginated Resultset", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [860, -680], "parameters": {"text": "=Analyze and Extract the below content. Make sure to produce a unique resultset. Exclude page_numbers which are not numbers.\n\n {{ $json.data }}", "options": {}, "schemaType": "manual", "inputSchema": "{\n  \"$schema\": \"http://json-schema.org/schema#\",\n  \"title\": \"PagedResultSetSchema\",\n  \"type\": \"array\",\n  \"items\": {\n    \"type\": \"object\",\n    \"properties\": {\n      \"page_number\": {\n        \"type\": \"string\",\n        \"description\": \"Page number, typically a string (e.g., '1', '2', 'next').\"\n      },\n      \"url\": {\n        \"type\": \"string\",\n        \"format\": \"uri\",\n        \"description\": \"URL pointing to the page.\"\n      }\n    },\n    \"required\": [\"page_number\", \"url\"],\n    \"additionalProperties\": false\n  }\n}\n"}, "typeVersion": 1}, {"id": "28c1822b-d51c-4f8e-b98e-2e12324397be", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1400, -780], "parameters": {"color": 5, "width": 1340, "height": 620, "content": "## Loop and Perform Paginated Esty Data Extraction\n"}, "typeVersion": 1}, {"id": "d4f18f2b-9825-4320-addb-c02bfdc4da97", "name": "Write the scraped content to disk", "type": "n8n-nodes-base.readWriteFile", "position": [2560, -760], "parameters": {"options": {}, "fileName": "=d:\\Esty-Scraped-Content-{{ $('Loop Over Items').item.json.page_number }}.json", "operation": "write"}, "typeVersion": 1}, {"id": "5555407d-c7dd-4e5c-83ab-ef6ba9c46da3", "name": "Create a binary data", "type": "n8n-nodes-base.function", "position": [2360, -760], "parameters": {"functionCode": "items[0].binary = {\n  data: {\n    data: new Buffer(JSON.stringify(items[0].json, null, 2)).toString('base64')\n  }\n};\nreturn items;"}, "typeVersion": 1}, {"id": "2f7a5fab-a2f4-422e-8f83-ce50fbe2a738", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [1240, -680], "parameters": {"options": {}, "fieldToSplitOut": "output"}, "typeVersion": 1}, {"id": "3d7a8992-b8d4-4a86-b60b-a92a7d63e31b", "name": "Extract Paginated Resultset With OpenAI", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [880, -120], "parameters": {"text": "=Analyze and Extract the below content. Make sure to produce a unique resultset. Exclude page_numbers which are not numbers.\n\n {{ $json.data }}", "options": {}, "schemaType": "manual", "inputSchema": "{\n  \"$schema\": \"http://json-schema.org/schema#\",\n  \"title\": \"PagedResultSetSchema\",\n  \"type\": \"array\",\n  \"items\": {\n    \"type\": \"object\",\n    \"properties\": {\n      \"page_number\": {\n        \"type\": \"string\",\n        \"description\": \"Page number, typically a string (e.g., '1', '2', 'next').\"\n      },\n      \"url\": {\n        \"type\": \"string\",\n        \"format\": \"uri\",\n        \"description\": \"URL pointing to the page.\"\n      }\n    },\n    \"required\": [\"page_number\", \"url\"],\n    \"additionalProperties\": false\n  }\n}\n"}, "typeVersion": 1}, {"id": "aa42d335-67bc-4dc5-a68a-4ce93e05464a", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [880, 80], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "vPKynKbDzJ5ZU4cU", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "82df0ccc-3065-4bb5-a48e-90e4dbf2162f", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [640, -260], "parameters": {"color": 6, "width": 660, "height": 460, "content": "## Open AI Extraction (Optional)\nNote - Replace the above workflow with the Open AI Chat Model if needed\nPlease make sure to set the OpenAI Chat Model -> Credential to connect with **OpenAi Account**"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "40a1bbd5-05b2-41c2-8b3c-72e3f16fd13a", "connections": {"Split Out": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Perform Esty web request over the loop", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Extract Paginated Resultset With OpenAI", "type": "ai_languageModel", "index": 0}]]}, "Create a binary data": {"main": [[{"node": "Write the scraped content to disk", "type": "main", "index": 0}]]}, "Set Esty Search Query": {"main": [[{"node": "Perform Esty Web Request", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Extract Paginated Resultset", "type": "ai_languageModel", "index": 0}]]}, "Perform Esty Web Request": {"main": [[{"node": "Extract Paginated Resultset", "type": "main", "index": 0}]]}, "Extract Paginated Resultset": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set Esty Search Query", "type": "main", "index": 0}]]}, "Perform Esty web request over the loop": {"main": [[{"node": "Extract Item List with the Product Info", "type": "main", "index": 0}]]}, "Extract Item List with the Product Info": {"main": [[{"node": "Initiate a Webhook Notification for the extracted data", "type": "main", "index": 0}, {"node": "Create a binary data", "type": "main", "index": 0}]]}, "Google Gemini Chat Model for product info": {"ai_languageModel": [[{"node": "Extract Item List with the Product Info", "type": "ai_languageModel", "index": 0}]]}, "Initiate a Webhook Notification for the extracted data": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}}}