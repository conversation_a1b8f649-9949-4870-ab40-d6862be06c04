{"id": "mbgpq1PH1SFkHi6w", "meta": {"instanceId": "00430fabba021bdf53a110b354e0e10bcfb5ee2de4556eb52b6d49f481ac083e"}, "name": "Add new clients from Notion to Clockify", "tags": [], "nodes": [{"id": "f58df180-644e-4e59-a32d-b6b316b8ff97", "name": "Add client to Clockify", "type": "n8n-nodes-base.clockify", "position": [240, -320], "parameters": {"name": "={{ $json.Name }}", "resource": "client", "workspaceId": "5da1c2995e326c429dbe6e31"}, "credentials": {"clockifyApi": {"id": "U7trUA4hFkSWHagH", "name": "Clockify account"}}, "typeVersion": 1}, {"id": "1f723b4b-30c7-45c9-b3b5-b55211597a93", "name": "Notion Trigger on new client", "type": "n8n-nodes-base.notionTrigger", "position": [-140, -320], "parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "databaseId": {"__rl": true, "mode": "id", "value": ""}}, "credentials": {"notionApi": {"id": "ogjRdz4QQPvdkxqo", "name": "Notion account privat"}}, "typeVersion": 1}, {"id": "f0f1e554-c2f5-4e41-8e9b-86b5ffcab64c", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-240, -580], "parameters": {"width": 300, "height": 460, "content": "## Notion\n### Poll for new clients\n**To-dos**:\n1. Connect your Notion account\n2. Set your polling interval\n3. Select your client Notion database "}, "typeVersion": 1}, {"id": "aab21f54-e577-4f01-9005-0113f83beca0", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [140, -580], "parameters": {"width": 300, "height": 460, "content": "## Clockify\n### Add new client\n**To-dos**:\n1. Connect your Clockify account\n2. Select your Clockify workspace\n3. Map your Notion client name column to the Clockify \"Client Name\" field"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "5edc08ae-df38-4c7f-9367-36dac7578351", "connections": {"Add client to Clockify": {"main": [[]]}, "Notion Trigger on new client": {"main": [[{"node": "Add client to Clockify", "type": "main", "index": 0}]]}}}