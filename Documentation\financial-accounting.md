# Financial & Accounting - N8N Workflows

## Overview
This document catalogs the **Financial & Accounting** workflows from the n8n Community Workflows repository.

**Category:** Financial & Accounting  
**Total Workflows:** 13  
**Generated:** 2025-07-27  
**Source:** https://scan-might-updates-postage.trycloudflare.com/api

---

## Workflows

### Create a new customer in Chargebee
**Filename:** `0018_Manual_Chargebee_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Chargebee to create new records. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Chargebee,  

---

### Receive updates for events in Chargebee
**Filename:** `0041_Chargebee_Update_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Chargebee to update existing data. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** <PERSON><PERSON>,  

---

### Update Crypto Values
**Filename:** `0177_Coingecko_Cron_Update_Scheduled.json`  
**Description:** Scheduled automation that connects Airtable and Coingecko to update existing data. Uses 8 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (8 nodes)  
**Integrations:** Airtable,Coingecko,  

---

### Create a QuickBooks invoice on a new Onfleet Task creation
**Filename:** `0186_Quickbooks_Onfleet_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Quickbooks and Onfleet to create new records. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** Quickbooks,Onfleet,  

---

### Manual Paypal Automation Triggered
**Filename:** `0957_Manual_Paypal_Automation_Triggered.json`  
**Description:** Manual workflow that integrates with PayPal for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** PayPal,  

---

### Receive updates when a billing plan is activated in PayPal
**Filename:** `0965_Paypal_Update_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with PayPal to update existing data. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** PayPal,  

---

### Manual Invoiceninja Automate Triggered
**Filename:** `1003_Manual_Invoiceninja_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Invoiceninja for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Invoiceninja,  

---

### Invoiceninja Automate Triggered
**Filename:** `1004_Invoiceninja_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Invoiceninja for data processing. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Invoiceninja,  

---

### Manual Xero Automate Triggered
**Filename:** `1011_Manual_Xero_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Xero for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Xero,  

---

### Create a coupon on Paddle
**Filename:** `1019_Manual_Paddle_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Paddle to create new records. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Paddle,  

---

### Quickbooks Automate
**Filename:** `1208_Quickbooks_Automate.json`  
**Description:** Manual workflow that integrates with Quickbooks for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Quickbooks,  

---

### Wise Automate
**Filename:** `1229_Wise_Automate.json`  
**Description:** Manual workflow that integrates with Wise for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Wise,  

---

### Wise Airtable Automate Triggered
**Filename:** `1230_Wise_Airtable_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that connects Wise and Airtable for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Wise,Airtable,  

---


## Summary

**Total Financial & Accounting workflows:** 13  
**Documentation generated:** 2025-07-27 14:35:54  
**API Source:** https://scan-might-updates-postage.trycloudflare.com/api  

This documentation was automatically generated using the n8n workflow API endpoints.
