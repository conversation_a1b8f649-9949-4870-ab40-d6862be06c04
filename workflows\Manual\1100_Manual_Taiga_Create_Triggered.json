{"id": "69", "name": "Create, update, and get an issue on Taiga", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [430, 260], "parameters": {}, "typeVersion": 1}, {"name": "Taiga", "type": "n8n-nodes-base.taiga", "position": [630, 260], "parameters": {"subject": "n8n-docs", "projectId": 385605, "additionalFields": {}}, "credentials": {"taigaCloudApi": "taiga"}, "typeVersion": 1}, {"name": "Taiga1", "type": "n8n-nodes-base.taiga", "position": [830, 260], "parameters": {"issueId": "={{$node[\"Taiga\"].json[\"id\"]}}", "operation": "update", "projectId": "={{$node[\"Taiga\"].json[\"project\"]}}", "updateFields": {"description": "This ticket is for the documentation for the Taiga node"}}, "credentials": {"taigaCloudApi": "taiga"}, "typeVersion": 1}, {"name": "Taiga2", "type": "n8n-nodes-base.taiga", "position": [1030, 260], "parameters": {"issueId": "={{$node[\"Taiga\"].json[\"id\"]}}", "operation": "get"}, "credentials": {"taigaCloudApi": "taiga"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Taiga": {"main": [[{"node": "Taiga1", "type": "main", "index": 0}]]}, "Taiga1": {"main": [[{"node": "Taiga2", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Taiga", "type": "main", "index": 0}]]}}}