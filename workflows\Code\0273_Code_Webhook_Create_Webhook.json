{"meta": {"instanceId": "237600ca44303ce91fa31ee72babcdc8493f55ee2c0e8aa2b78b3b4ce6f70bd9"}, "nodes": [{"id": "b220e0c7-3c34-4221-8fee-11c133a5345b", "name": "Get ticket", "type": "n8n-nodes-base.zendesk", "position": [740, 540], "parameters": {"id": "={{$node[\"On new Zendesk ticket\"].json[\"body\"][\"id\"]}}", "operation": "get"}, "credentials": {"zendeskApi": {"id": "24", "name": "[UPDATE ME]"}}, "typeVersion": 1}, {"id": "e58834a7-1a94-429f-a50c-2e27293c32a0", "name": "IF", "type": "n8n-nodes-base.if", "position": [1140, 540], "parameters": {"conditions": {"string": [{"value1": "={{$node[\"Determine\"].json[\"Slack Thread ID\"]}}", "operation": "isNotEmpty"}]}}, "typeVersion": 1}, {"id": "c6f82ab9-54f4-4f91-a4d9-018739c6519d", "name": "Update ticket", "type": "n8n-nodes-base.zendesk", "notes": "Update the Zendesk ticket by adding the Jira issue key to the \"Jira Issue Key\" field.", "position": [1540, 640], "parameters": {"id": "={{$node[\"On new Zendesk ticket\"].json[\"body\"][\"id\"]}}", "operation": "update", "updateFields": {"customFieldsUi": {"customFieldsValues": [{"id": 7022397804317, "value": "={{$node[\"Create thread\"].json[\"ts\"]}}"}]}}}, "credentials": {"zendeskApi": {"id": "24", "name": "[UPDATE ME]"}}, "notesInFlow": true, "typeVersion": 1}, {"id": "74d93ba5-d82d-4cc4-a177-bd86dbc18534", "name": "On new Zendesk ticket", "type": "n8n-nodes-base.webhook", "position": [540, 540], "webhookId": "b7845b15-0a44-4be5-b513-f4f4bb8989a6", "parameters": {"path": "b7845b15-0a44-4be5-b513-f4f4bb8989a6", "options": {}, "httpMethod": "POST"}, "typeVersion": 1}, {"id": "65d387cd-5c7a-4567-9a3c-9fa033f98ac9", "name": "Create thread", "type": "n8n-nodes-base.slack", "position": [1340, 640], "parameters": {"text": "={{$node[\"Get ticket\"].json[\"subject\"]}}", "channel": "={{$node[\"Configure\"].parameter[\"values\"][\"string\"][0][\"value\"]}}", "attachments": [], "otherOptions": {}, "authentication": "oAuth2"}, "credentials": {"slackOAuth2Api": {"id": "28", "name": "[UPDATE ME]"}}, "typeVersion": 1}, {"id": "50f5aa84-70bc-4b08-a9cc-576fbed72636", "name": "Create reply on existing thread", "type": "n8n-nodes-base.slack", "position": [1340, 440], "parameters": {"text": "={{$node[\"On new Zendesk ticket\"].json[\"body\"][\"comment\"]}}", "channel": "={{$node[\"Configure\"].parameter[\"values\"][\"string\"][0][\"value\"]}}", "attachments": [], "otherOptions": {"thread_ts": "={{$node[\"Determine\"].json[\"Slack Thread ID\"]}}"}, "authentication": "oAuth2"}, "credentials": {"slackOAuth2Api": {"id": "28", "name": "[UPDATE ME]"}}, "typeVersion": 1}, {"id": "6d5e8df0-4b0b-487c-81be-93359976dd90", "name": "Configure", "type": "n8n-nodes-base.set", "position": [540, 360], "parameters": {"values": {"string": [{"name": "Slack channel", "value": "#zendesk-updates"}]}, "options": {"dotNotation": false}}, "typeVersion": 1}, {"id": "934b95bb-2ffa-40a4-a2ca-02cfd646dd78", "name": "Note", "type": "n8n-nodes-base.stickyNote", "position": [-20, 360], "parameters": {"width": 469.4813676974197, "height": 268.2900466166276, "content": "## Sync Zendesk tickets to Slack threads\n### Setup\n1. Add your [Zendesk credential](https://docs.n8n.io/integrations/builtin/credentials/zendesk/) to the `Get ticket` and `Update ticket` nodes.\n2. Add your [Slack credential](https://docs.n8n.io/integrations/builtin/credentials/slack/) to `Create Thread` and `Create reply on existing thread` nodes.\n3. Open `Configure` node and change \"Slack channel\" value to your slack channel (like #zendesk-updates).\n4. Activate the workflow so it runs automatically each time a Zendesk ticket is created."}, "typeVersion": 1}, {"id": "b582f7ff-7cc6-48dc-89fc-bc8bde13b06e", "name": "Code", "type": "n8n-nodes-base.code", "notes": "if thread was created already in Slack", "position": [940, 540], "parameters": {"jsCode": "/* configure here =========================================================== */\n/*  Zendesk field ID which represents the \"Slack Thread ID\" field.\n*/\nconst ISSUE_KEY_FIELD_ID = 7022397804317;\n\n/* ========================================================================== */\nnew_items = [];\n\nfor (item of $items(\"Get ticket\")) {\n    \n    // instantiate a new variable for status\n    var custom_fields = item.json[\"custom_fields\"];\n    var slack_thread_id = \"\";\n    for (var i = 0; i < custom_fields.length; i++) {\n        if (custom_fields[i].id == ISSUE_KEY_FIELD_ID) {\n            slack_thread_id = custom_fields[i].value;\n            break;\n        }\n    }\n\n    // push the new item to the new_items array\n    new_items.push({\n        \"Slack Thread ID\": slack_thread_id\n    });\n}\n\nreturn new_items;"}, "notesInFlow": true, "typeVersion": 1}], "connections": {"IF": {"main": [[{"node": "Create reply on existing thread", "type": "main", "index": 0}], [{"node": "Create thread", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}, "Get ticket": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Create thread": {"main": [[{"node": "Update ticket", "type": "main", "index": 0}]]}, "On new Zendesk ticket": {"main": [[{"node": "Get ticket", "type": "main", "index": 0}]]}}}