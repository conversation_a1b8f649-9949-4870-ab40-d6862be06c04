{"id": "cMccNWyyvptrhRt6", "meta": {"instanceId": "7d362a334cd7fabe145eb8ec1b9c6b483cd4fa9315ab54f45d181e73340a0ebc", "templateCredsSetupCompleted": true}, "name": "Baserow markdown to html", "tags": [], "nodes": [{"id": "57d42202-e74b-4103-b872-fbd4ea151e41", "name": "Get single record from baserow", "type": "n8n-nodes-base.baserow", "position": [1660, 1200], "parameters": {"rowId": "={{ $('Baserow sync video description').item.json.query.rec }}", "tableId": 260956, "operation": "get", "databaseId": 94671}, "credentials": {"baserowApi": {"id": "ZtSVpTPWpIusSF9B", "name": "baserowCloud"}}, "typeVersion": 1}, {"id": "eaa051ad-1644-4c5b-b0bd-35d55d93b83a", "name": "Update single record in baserow", "type": "n8n-nodes-base.baserow", "position": [2100, 1200], "parameters": {"rowId": "={{ $json.id }}", "tableId": 260956, "fieldsUi": {"fieldValues": [{"fieldId": 2314683, "fieldValue": "={{ $json.data }}"}]}, "operation": "update", "databaseId": 94671}, "credentials": {"baserowApi": {"id": "ZtSVpTPWpIusSF9B", "name": "baserowCloud"}}, "typeVersion": 1}, {"id": "5c52950c-eab3-48e7-84be-2dfef26f798c", "name": "Update all records in baserow", "type": "n8n-nodes-base.baserow", "position": [2100, 1420], "parameters": {"rowId": "={{ $json.id }}", "tableId": 260956, "fieldsUi": {"fieldValues": [{"fieldId": 2314683, "fieldValue": "={{ $json.data }}"}]}, "operation": "update", "databaseId": 94671}, "credentials": {"baserowApi": {"id": "ZtSVpTPWpIusSF9B", "name": "baserowCloud"}}, "typeVersion": 1}, {"id": "eea44e7c-7dcc-4e46-a378-e4efded207b0", "name": "Check if it's 1 record or all records - Baserow", "type": "n8n-nodes-base.if", "position": [1460, 1220], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "bb614e16-f239-4ced-b50f-15be13493099", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.query.rec }}", "rightValue": ""}]}}, "typeVersion": 2}, {"id": "b8e66259-2a04-471a-8886-20de5793c9ad", "name": "Get all records from baserow", "type": "n8n-nodes-base.baserow", "position": [1660, 1420], "parameters": {"tableId": 260956, "returnAll": true, "databaseId": 94671, "additionalOptions": {}}, "credentials": {"baserowApi": {"id": "ZtSVpTPWpIusSF9B", "name": "baserowCloud"}}, "typeVersion": 1}, {"id": "3a5e6b2b-8cbd-41e0-9452-b60647554db6", "name": "Baserow sync video description", "type": "n8n-nodes-base.webhook", "position": [1240, 1220], "webhookId": "d4858ac8-2d80-41c5-a9d9-06b8e1a14347", "parameters": {"path": "d4858ac8-2d80-41c5-a9d9-06b8e1a14347", "options": {}}, "typeVersion": 2}, {"id": "6853027d-4b66-4cf8-a521-6e1869a47b03", "name": "Convert markdown to HTML (single)", "type": "n8n-nodes-base.markdown", "position": [1880, 1200], "parameters": {"mode": "markdownToHtml", "options": {"emoji": true, "simpleLineBreaks": true, "backslashEscapesHTMLTags": true}, "markdown": "={{ $json['📥 Video Description'] }}"}, "typeVersion": 1}, {"id": "a9c35d29-dc0e-432d-8116-6b52d64a8a34", "name": "Convert markdown to HTML (all records)", "type": "n8n-nodes-base.markdown", "position": [1880, 1420], "parameters": {"mode": "markdownToHtml", "options": {}, "markdown": "={{ $json['📥 Video Description'] }}"}, "typeVersion": 1}, {"id": "ff0893ca-6bd0-4ab3-b526-6347702815ff", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1240, 1000], "parameters": {"content": "# Tutorial\n[Youtube video](https://www.youtube.com/watch?v=PAoxZjICd7o)"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "7172dabc-5b15-478f-b956-9ac736af4745", "connections": {"Get all records from baserow": {"main": [[{"node": "Convert markdown to HTML (all records)", "type": "main", "index": 0}]]}, "Baserow sync video description": {"main": [[{"node": "Check if it's 1 record or all records - Baserow", "type": "main", "index": 0}]]}, "Get single record from baserow": {"main": [[{"node": "Convert markdown to HTML (single)", "type": "main", "index": 0}]]}, "Convert markdown to HTML (single)": {"main": [[{"node": "Update single record in baserow", "type": "main", "index": 0}]]}, "Convert markdown to HTML (all records)": {"main": [[{"node": "Update all records in baserow", "type": "main", "index": 0}]]}, "Check if it's 1 record or all records - Baserow": {"main": [[{"node": "Get single record from baserow", "type": "main", "index": 0}], [{"node": "Get all records from baserow", "type": "main", "index": 0}]]}}}