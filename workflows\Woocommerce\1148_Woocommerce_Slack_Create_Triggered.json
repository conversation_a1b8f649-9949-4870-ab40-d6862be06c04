{"id": 80, "name": "New WooCommerce product to Slack", "nodes": [{"name": "Product Created", "type": "n8n-nodes-base.wooCommerceTrigger", "position": [320, 500], "webhookId": "267c4855-6227-4d33-867e-74600097473e", "parameters": {"event": "product.created"}, "credentials": {"wooCommerceApi": {"id": "48", "name": "WooCommerce account"}}, "typeVersion": 1}, {"name": "Send to Slack", "type": "n8n-nodes-base.slack", "position": [540, 500], "parameters": {"text": ":new: A new product has been added! :new:", "channel": "woo-commerce", "blocksUi": {"blocksValues": []}, "attachments": [{"color": "#66FF00", "fields": {"item": [{"short": false, "title": "Name", "value": "={{$json[\"name\"]}}"}, {"short": true, "title": "Price", "value": "={{$json[\"regular_price\"]}}"}, {"short": true, "title": "Sale Price", "value": "={{$json[\"sale_price\"]}}"}, {"short": false, "title": "Link", "value": "={{$json[\"permalink\"]}}"}]}, "footer": "=Added: {{$json[\"date_created\"]}}"}], "otherOptions": {}}, "credentials": {"slackApi": {"id": "53", "name": "Slack Access Token"}}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Product Created": {"main": [[{"node": "Send to Slack", "type": "main", "index": 0}]]}}}