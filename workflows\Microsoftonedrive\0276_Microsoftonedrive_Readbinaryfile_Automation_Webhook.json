{"meta": {"instanceId": "8c8c5237b8e37b006a7adce87f4369350c58e41f3ca9de16196d3197f69eabcd"}, "nodes": [{"id": "05bd643c-6dd0-4f36-a586-3a06cc26893c", "name": "Note", "type": "n8n-nodes-base.stickyNote", "position": [200, 780], "parameters": {"width": 476.4578377639565, "height": 299.6468819708682, "content": "## Working with Excel files\n1. Load the spreadsheet file into the workflow (.xls, .xlsx, .csv).\n2. Convert the file with **Spreadsheet File** node. This allows other nodes to access the data.\n3. Transform and manipulate the spreadsheet data as needed\n4. [Optional] Convert back to a spreadsheet file\n5. [Optional] Save file locally or upload to a server\n\n\n\nℹ️ This template shows how to work with spreadsheet files themselves. Use the **Microsoft Excel 365** node to interact with the Microsoft Office 365 cloud platform. "}, "typeVersion": 1}, {"id": "84db705b-b45f-447f-b3e6-ac9650816e3b", "name": "Note1", "type": "n8n-nodes-base.stickyNote", "position": [840, 800], "parameters": {"width": 261.5285597588645, "height": 244.71805702217537, "content": "### 1A. From a public URL"}, "typeVersion": 1}, {"id": "92b8375b-92a3-41ca-874e-d9c4567e21d4", "name": "Read Binary File", "type": "n8n-nodes-base.readBinaryFile", "notes": "Fetches a local file", "disabled": true, "position": [920, 1140], "parameters": {"filePath": "/files/customer-datastore.xlsx"}, "notesInFlow": true, "typeVersion": 1}, {"id": "e595db63-8556-4e5e-89df-9895691ed4bb", "name": "Note7", "type": "n8n-nodes-base.stickyNote", "position": [840, 680], "parameters": {"width": 332.13093980992585, "height": 80, "content": "## 1. Load file into workflow"}, "typeVersion": 1}, {"id": "66ae38b6-01e6-486b-aae1-d696d22fb2cf", "name": "Note8", "type": "n8n-nodes-base.stickyNote", "position": [840, 1380], "parameters": {"width": 263.20908130939836, "height": 475.9602777402797, "content": "### 1C. From a cloud platform"}, "typeVersion": 1}, {"id": "c2e2cc7e-01a2-4138-ba6f-344be3dd91f3", "name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [500, 1140], "parameters": {}, "typeVersion": 1}, {"id": "050bdd2e-6fe5-4145-8a0b-c1b4b8870c53", "name": "Note2", "type": "n8n-nodes-base.stickyNote", "disabled": true, "position": [2060, 680], "parameters": {"width": 326.8935002375224, "height": 302.0190073917633, "content": "## 4. [Optional] Convert node data back to .xls file"}, "typeVersion": 1}, {"id": "3822a521-c1f4-40a9-bbb6-540a2bb4651b", "name": "Note4", "type": "n8n-nodes-base.stickyNote", "disabled": true, "position": [1640, 680], "parameters": {"width": 359.63512407276517, "height": 304.93769799366413, "content": "## 3. Manipulate or transform your spreadsheet data \n\n\n\n\n\n"}, "typeVersion": 1}, {"id": "a90ef806-62a7-492d-b493-337d796c677a", "name": "Note5", "type": "n8n-nodes-base.stickyNote", "position": [2460, 1080], "parameters": {"width": 253.5004831258875, "height": 243.48423158332457, "content": "### 4B. To a webserver via (S)FTP"}, "typeVersion": 1}, {"id": "a5419c12-4be4-4fdf-8b9f-f6c73104477a", "name": "Write Binary File", "type": "n8n-nodes-base.writeBinaryFile", "position": [2520, 860], "parameters": {"options": {}, "fileName": "=/tmp/{{$binary.data.fileName}}"}, "typeVersion": 1}, {"id": "3d3474ee-298f-48ee-b7b4-2dd64729c747", "name": "Note6", "type": "n8n-nodes-base.stickyNote", "disabled": true, "position": [1280, 680], "parameters": {"width": 279.5841955487948, "height": 309.4318901795142, "content": "## 2. Convert the file into JSON format\nJSON data can be used by nodes\n\n\n\n\n\n"}, "typeVersion": 1}, {"id": "93cd3132-460b-4a67-b627-b417bbd74012", "name": "Note9", "type": "n8n-nodes-base.stickyNote", "disabled": true, "position": [2460, 680], "parameters": {"width": 332.13093980992585, "height": 86.72208620213638, "content": "## 5. Save or upload new file\n### [Optional]"}, "typeVersion": 1}, {"id": "4ca7e58c-2d8f-463f-86f9-f87f47a7364b", "name": "Note10", "type": "n8n-nodes-base.stickyNote", "position": [2460, 800], "parameters": {"width": 253.5004831258875, "height": 245.22344655940856, "content": "### 4A. To a local filesystem"}, "typeVersion": 1}, {"id": "db8f95b3-db71-4111-b5a4-a53cdfeea896", "name": "Note11", "type": "n8n-nodes-base.stickyNote", "disabled": true, "position": [2460, 1380], "parameters": {"width": 253.5004831258875, "height": 480.2511652360096, "content": "### 4C. To a cloud service"}, "typeVersion": 1}, {"id": "ae1a1cdf-4670-41da-8bc5-aa6817ce08bc", "name": "Note3", "type": "n8n-nodes-base.stickyNote", "position": [840, 1080], "parameters": {"width": 263.20908130939836, "height": 244.71805702217537, "content": "### 1B. From the local filesystem"}, "typeVersion": 1}, {"id": "529b03fb-b81d-40f3-bade-684cc9776cba", "name": "Download from Google Drive", "type": "n8n-nodes-base.googleDrive", "disabled": true, "position": [920, 1440], "parameters": {"fileId": {"__rl": true, "mode": "list", "value": "1ffuj8v-s0h8LeEmrA2hBk-b7qKF_c9uT", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ffuj8v-s0h8LeEmrA2hBk-b7qKF_c9uT/edit?usp=drivesdk&ouid=112909978107527312058&rtpof=true&sd=true", "cachedResultName": "customer-datastore.xlsx"}, "options": {}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "148", "name": "FPS"}}, "typeVersion": 2}, {"id": "b63c9748-0c7d-4d2a-aa5b-db76d31af957", "name": "Download from Microsoft OneDrive", "type": "n8n-nodes-base.microsoftOneDrive", "disabled": true, "position": [920, 1640], "parameters": {"fileId": "549D14658E697C62!2087", "operation": "download"}, "credentials": {"microsoftOneDriveOAuth2Api": {"id": "88", "name": "Microsoft Drive account"}}, "typeVersion": 1}, {"id": "6333d0b5-d58b-4a19-af9a-0e5ea4fa15e8", "name": "Download Excel File", "type": "n8n-nodes-base.httpRequest", "notes": "Fetches file from server", "position": [920, 860], "parameters": {"url": "https://internal.users.n8n.cloud/webhook/709a234d-add7-41d2-9326-8d981f58120b", "options": {}}, "notesInFlow": true, "typeVersion": 3}, {"id": "88b24dbb-dc9f-4f03-a5b3-71ba89295346", "name": "Work out Age", "type": "n8n-nodes-base.set", "position": [1760, 820], "parameters": {"values": {"string": [{"name": "age", "value": "={{ Math.trunc($today.diff(DateTime.fromFormat($json[\"created\"], 'yyyy-MM-dd'), 'years').toObject().years) }}"}]}, "options": {}}, "typeVersion": 1}, {"id": "2f1f2fa9-4995-46c9-a415-3768a0895e88", "name": "Upload to SFTP", "type": "n8n-nodes-base.ftp", "disabled": true, "position": [2520, 1140], "parameters": {"path": "=/home/<USER>/{{$binary.data.fileName}}", "protocol": "sftp", "operation": "upload"}, "credentials": {"sftp": {"id": "8", "name": "SFTP"}}, "typeVersion": 1}, {"id": "81c06f12-83f1-4973-a1ec-6d58e26eb8c9", "name": "Upload to Google Drive", "type": "n8n-nodes-base.googleDrive", "disabled": true, "position": [2520, 1440], "parameters": {"name": "={{$binary.data.fileName}}", "options": {}, "binaryData": true}, "credentials": {"googleDriveOAuth2Api": {"id": "148", "name": "FPS"}}, "typeVersion": 2}, {"id": "a0ef4740-8716-4fab-8498-c13ee32842cb", "name": "Upload to Microsoft OneDrive", "type": "n8n-nodes-base.microsoftOneDrive", "disabled": true, "position": [2520, 1640], "parameters": {"fileName": "={{$binary.data.fileName}}", "parentId": "root", "binaryData": true}, "credentials": {"microsoftOneDriveOAuth2Api": {"id": "88", "name": "Microsoft Drive account"}}, "typeVersion": 1}, {"id": "01e6575d-bb92-4f32-82b4-acfe7448a364", "name": "Read Spreadsheet File", "type": "n8n-nodes-base.spreadsheetFile", "position": [1360, 820], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "ed09f502-109f-42dc-a62c-6b6f54aad46e", "name": "Write Spreadsheet File", "type": "n8n-nodes-base.spreadsheetFile", "position": [2160, 820], "parameters": {"options": {"fileName": "=customer-datastore_{{$today.toFormat('yyyyMMdd')}}.xlsx"}, "operation": "toFile", "fileFormat": "xlsx"}, "typeVersion": 1}], "connections": {"Work out Age": {"main": [[{"node": "Write Spreadsheet File", "type": "main", "index": 0}]]}, "Read Binary File": {"main": [[{"node": "Read Spreadsheet File", "type": "main", "index": 0}]]}, "Download Excel File": {"main": [[{"node": "Read Spreadsheet File", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Read Binary File", "type": "main", "index": 0}, {"node": "Download Excel File", "type": "main", "index": 0}, {"node": "Download from Google Drive", "type": "main", "index": 0}, {"node": "Download from Microsoft OneDrive", "type": "main", "index": 0}]]}, "Read Spreadsheet File": {"main": [[{"node": "Work out Age", "type": "main", "index": 0}]]}, "Write Spreadsheet File": {"main": [[{"node": "Upload to SFTP", "type": "main", "index": 0}, {"node": "Upload to Google Drive", "type": "main", "index": 0}, {"node": "Write Binary File", "type": "main", "index": 0}, {"node": "Upload to Microsoft OneDrive", "type": "main", "index": 0}]]}, "Download from Google Drive": {"main": [[{"node": "Read Spreadsheet File", "type": "main", "index": 0}]]}, "Download from Microsoft OneDrive": {"main": [[{"node": "Read Spreadsheet File", "type": "main", "index": 0}]]}}}