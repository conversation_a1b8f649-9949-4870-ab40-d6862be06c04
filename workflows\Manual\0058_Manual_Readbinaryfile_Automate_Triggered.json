{"id": 112, "name": "Standup <PERSON><PERSON> - <PERSON> Config", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [240, 300], "parameters": {}, "typeVersion": 1}, {"name": "Read Config File", "type": "n8n-nodes-base.readBinaryFile", "position": [420, 300], "parameters": {"filePath": "/home/<USER>/.n8n/standup-bot-config.json", "dataPropertyName": "config"}, "typeVersion": 1}, {"name": "Convert to JSON", "type": "n8n-nodes-base.moveBinaryData", "position": [600, 300], "parameters": {"options": {"encoding": "utf8"}, "sourceKey": "config"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Read Config File": {"main": [[{"node": "Convert to JSON", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Read Config File", "type": "main", "index": 0}]]}}}