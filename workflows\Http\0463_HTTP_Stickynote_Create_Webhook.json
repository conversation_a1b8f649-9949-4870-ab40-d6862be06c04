{"meta": {"instanceId": "041bccf206a3546a759ec4c0a3bf1256e62051945bb270c48f91f3acb13dc080"}, "nodes": [{"id": "401dbfb3-5475-4b00-b2df-3aa685815b05", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [340, -260], "parameters": {"width": 747, "height": 428, "content": "## Purpose \nTo verify the mailing address for new contacts in HighLevel. \n\nWhenever I add a new contact to HighLevel, I run this automation to ensure I have a valid mailing address. It also helps me check for misspellings if the contact address was manually entered.\n\nQuick Video Overview:\nhttps://www.loom.com/share/8995ca0b41ce473ebbad9c1973109c0f\n"}, "typeVersion": 1}, {"id": "abca87a6-91ca-4597-aec7-28913c3a33b8", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1480, -180], "parameters": {"color": 5, "width": 515, "height": 763, "content": "Update HighLevel to indicate if the address is deliverable.\nYou could: \n- Add Tag\n- Start Automation\n- Update a Field\n\nFor Deliverable Addresses - I apply a tag that the address was verified.\n\nFor Non Deliverable Addresses - I apply a tag, which triggers an automation for my team to manually verify the address. You could also trigger an automation to reach out to the contact to verify their address.\n\n"}, "typeVersion": 1}, {"id": "0f21121c-c7fb-4697-9663-8ecf03ca76a5", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [520, 200], "parameters": {"color": 4, "height": 339, "content": "Receive a webhook from your CRM with the contact address fields"}, "typeVersion": 1}, {"id": "47c9e17d-0b30-41a3-bf83-eb4558fa7b85", "name": "Set Address Fields", "type": "n8n-nodes-base.set", "position": [840, 280], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "8216105e-23ad-4c5c-8f4a-4f97658e0947", "name": "address", "type": "string", "value": "={{ $json.address }}"}, {"id": "111da971-2473-4c5e-a106-22589cf47daf", "name": "address2", "type": "string", "value": ""}, {"id": "ed62cf39-10f1-42f6-b18f-bfa58b4fe646", "name": "city", "type": "string", "value": "={{ $json.city }}"}, {"id": "d9550200-04ac-4cf4-b7e6-cd40b793ce97", "name": "state", "type": "string", "value": "={{ $json.state }}"}, {"id": "62269d11-c98c-4016-83ef-291176f2fc12", "name": "zip", "type": "string", "value": "={{ $json.zip_code }}"}]}, "includeOtherFields": true}, "typeVersion": 3.3}, {"id": "1ee9fabf-a456-4877-8f2c-1150b8e43c7a", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1000, 480], "parameters": {"color": 3, "width": 430, "height": 216, "content": "1. Create an Account a LOB.com\n2. Create API Key (https://help.lob.com/account-management/api-keys)\n3. Update Node with your Credentials (Basic Auth)"}, "typeVersion": 1}, {"id": "6bc67404-b292-4211-a8f9-568802e12786", "name": "CRM Webhook Trigger", "type": "n8n-nodes-base.webhook", "position": [620, 280], "webhookId": "912a0210-7d6a-4517-9055-b8633c59a631", "parameters": {"path": "727deb6f-9d10-4492-92e6-38f3292510b0", "options": {}, "httpMethod": "POST"}, "typeVersion": 1.1}, {"id": "9ab388c0-8e84-45da-9475-9b83d3f2852d", "name": "Address Verification", "type": "n8n-nodes-base.httpRequest", "position": [1060, 280], "parameters": {"url": "https://api.lob.com/v1/us_verifications", "method": "POST", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "primary_line", "value": "={{ $json.address }}"}, {"name": "secondary_line", "value": "={{ $json.address2 }}"}, {"name": "city", "value": "={{ $json.city }}"}, {"name": "state", "value": "={{ $json.state }}"}, {"name": "zip_code", "value": "={{ $json.zip_code }}"}]}}, "typeVersion": 4.1}, {"id": "50921e14-2fdf-4bac-8ef7-06fcb9e73176", "name": "Update HighLevel - Deliverable", "type": "n8n-nodes-base.highLevel", "position": [1580, 160], "parameters": {"email": "={{ $('CRM Webhook Trigger').item.json.email }}", "phone": "={{ $('CRM Webhook Trigger').item.json.phone }}", "additionalFields": {"tags": "Mailing Address Deliverable"}}, "credentials": {"highLevelApi": {"id": "qJqOS89WQuqj4wXh", "name": "Test"}}, "typeVersion": 1}, {"id": "c81889cb-aeff-4afe-ae1c-747b30a4b6b1", "name": "Update HighLevel - NOT Deliverable", "type": "n8n-nodes-base.highLevel", "position": [1580, 380], "parameters": {"email": "={{ $('CRM Webhook Trigger').item.json.email }}", "phone": "={{ $('CRM Webhook Trigger').item.json.phone }}", "additionalFields": {"tags": "Mailing Address NOT Deliverable"}}, "credentials": {"highLevelApi": {"id": "qJqOS89WQuqj4wXh", "name": "Test"}}, "typeVersion": 1}, {"id": "9f896b41-eeb9-4cde-9fc8-e1ba000a2b61", "name": "Switch", "type": "n8n-nodes-base.switch", "position": [1280, 280], "parameters": {"rules": {"rules": [{"value2": "=deliverable", "outputKey": "deliverable"}, {"value2": "deliverable", "operation": "notEqual", "outputKey": "NOT deliverable"}]}, "value1": "={{ $json.deliverability }}", "dataType": "string"}, "typeVersion": 2}], "pinData": {"CRM Webhook Trigger": [{"city": "Washington", "email": "<EMAIL>", "phone": "************", "state": "DC", "address": "1600 Pennsylvania Avenue NW", "zip_code": "20500", "contact_id": "5551212"}]}, "connections": {"Switch": {"main": [[{"node": "Update HighLevel - Deliverable", "type": "main", "index": 0}], [{"node": "Update HighLevel - NOT Deliverable", "type": "main", "index": 0}]]}, "Set Address Fields": {"main": [[{"node": "Address Verification", "type": "main", "index": 0}]]}, "CRM Webhook Trigger": {"main": [[{"node": "Set Address Fields", "type": "main", "index": 0}]]}, "Address Verification": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}}}