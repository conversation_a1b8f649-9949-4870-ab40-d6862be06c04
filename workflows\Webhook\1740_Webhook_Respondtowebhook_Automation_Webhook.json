{"id": "W1ugowsjzt1SC4hH", "meta": {"instanceId": "04ab549d8bbb435ec33b81e4e29965c46cf6f0f9e7afe631018b5e34c8eead58"}, "name": "Validate Seatable Webhooks with HMAC SHA256 Authentication", "tags": [], "nodes": [{"id": "ec4bdb4f-3c3e-4405-af80-2ad7ab3d57fc", "name": "200", "type": "n8n-nodes-base.respondToWebhook", "position": [420, -20], "parameters": {"options": {"responseCode": 200}, "respondWith": "noData"}, "typeVersion": 1}, {"id": "1b6c9f8c-1b5b-499d-abb5-bb1059b73ce7", "name": "403", "type": "n8n-nodes-base.respondToWebhook", "position": [420, 180], "parameters": {"options": {"responseCode": 403}, "respondWith": "noData"}, "typeVersion": 1}, {"id": "e3976bf3-60e0-4c1c-bfdb-22ad336760a5", "name": "Calculate sha256", "type": "n8n-nodes-base.crypto", "position": [-20, -20], "parameters": {"type": "SHA256", "action": "hmac", "binaryData": true, "dataPropertyName": "seatable-signature"}, "typeVersion": 1}, {"id": "5e74ba50-e0fe-41e0-9b84-7078f1d150a3", "name": "Seatable Webhook", "type": "n8n-nodes-base.webhook", "position": [-240, -20], "webhookId": "8c9d8c0f-d5ea-469d-afc9-d4e8a352f1a4", "parameters": {"path": "s0m3-d4nd0m-1d", "options": {"rawBody": true}, "httpMethod": "POST", "responseMode": "responseNode"}, "typeVersion": 1}, {"id": "dbfcc59f-5411-4d99-8cde-26ae91cdd6af", "name": "Add nodes for processing", "type": "n8n-nodes-base.noOp", "position": [420, -220], "parameters": {}, "typeVersion": 1}, {"id": "a508534f-abb4-4455-b47a-1aaf56ce1124", "name": "hash matches", "type": "n8n-nodes-base.if", "position": [200, -20], "parameters": {"conditions": {"string": [{"value1": "={{ String($json['seatable-signature']) }}", "value2": "={{ String($json.headers['x-seatable-signature'].replace(\"sha256=\", \"\")) }}"}]}}, "typeVersion": 1}, {"id": "1495d5c1-3467-4639-a32d-51a6497aed51", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-400, -660], "parameters": {"width": 720, "height": 580, "content": "## 📌 Validate Seatable Webhooks with HMAC SHA256 Authentication\n\nThis mini workflow is designed to **securely validate incoming Seatable webhooks** using HMAC SHA256 signature verification.\n\n### 🔐 What it does:\n- Listens for incoming Seatable webhook requests.\n- Calculates a SHA256 HMAC hash of the raw request body using your shared secret.\n- Compares the computed hash with the `x-seatable-signature` header (after removing the `sha256=` prefix).\n- If the hashes match: responds with **200 OK** and forwards the request to subsequent nodes.\n- If the hashes don’t match: responds with **403 Forbidden**.\n\n### ⚠️ Important Notes:\nThis workflow is provided as a **template** and is not intended to work standalone. **Please duplicate it** and integrate it with your custom logic at the \"Add nodes for processing\" node.\n\nConfiguration steps:\n- Set your **secret key** in the “Calculate sha256” crypto node (replace the placeholder).\n- Adjust the webhook path to suit your environment (or set it to \"manual\" for testing).\n- Connect your actual logic after the verification step.\n"}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "8da47cde-25ce-459e-a74d-91ba0d5173e3", "connections": {"hash matches": {"main": [[{"node": "200", "type": "main", "index": 0}, {"node": "Add nodes for processing", "type": "main", "index": 0}], [{"node": "403", "type": "main", "index": 0}]]}, "Calculate sha256": {"main": [[{"node": "hash matches", "type": "main", "index": 0}]]}, "Seatable Webhook": {"main": [[{"node": "Calculate sha256", "type": "main", "index": 0}]]}}}