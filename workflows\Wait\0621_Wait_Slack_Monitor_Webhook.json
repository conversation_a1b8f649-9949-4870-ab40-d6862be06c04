{"meta": {"instanceId": "03e9d14e9196363fe7191ce21dc0bb17387a6e755dcc9acc4f5904752919dca8"}, "nodes": [{"id": "1de0b08b-585a-43a9-bf32-34cdd763fbb0", "name": "Global Variables", "type": "n8n-nodes-base.set", "position": [1180, 500], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "6a8a0cbf-bf3e-4702-956e-a35966d8b9c5", "name": "base_url", "type": "string", "value": "https://qualysapi.qg3.apps.qualys.com"}]}, "includeOtherFields": true}, "typeVersion": 3.3}, {"id": "cc10e116-1a16-4bd9-bdbb-27baa680dc91", "name": "Fetch Report IDs", "type": "n8n-nodes-base.httpRequest", "position": [1400, 500], "parameters": {"": "", "url": "={{ $json.base_url }}/msp/report_template_list.php", "method": "GET", "options": {}, "sendBody": false, "sendQuery": false, "curlImport": "", "infoMessage": "", "sendHeaders": false, "authentication": "predefinedCredentialType", "httpVariantWarning": "", "nodeCredentialType": "qualys<PERSON>pi", "provideSslCertificates": false}, "credentials": {"qualysApi": {"id": "KdkmNjVYkDUzHAvw", "name": "Qualys account"}}, "typeVersion": 4.2, "extendsCredential": "qualys<PERSON>pi"}, {"id": "69e097c2-ba05-4964-af82-ce07fb2a6535", "name": "Convert XML To JSON", "type": "n8n-nodes-base.xml", "position": [1580, 500], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "d2a2001a-4df8-4482-9ecf-62a7aed90a9c", "name": "Launch Report", "type": "n8n-nodes-base.httpRequest", "position": [1760, 500], "parameters": {"": "", "url": "={{ $('Global Variables').item.json[\"base_url\"] }}/api/2.0/fo/report/", "method": "POST", "options": {}, "sendBody": true, "sendQuery": true, "curlImport": "", "contentType": "multipart-form-data", "infoMessage": "", "sendHeaders": true, "specifyQuery": "keypair", "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "template_id", "value": "={{ $jmespath($json[\"REPORT_TEMPLATE_LIST\"][\"REPORT_TEMPLATE\"], \"[?TITLE == '\"+$('Global Variables').item.json.template_name+\"'].ID\") | [0] }}", "parameterType": "formData"}, {"name": "=output_format", "value": "={{ $('Global Variables').item.json.output_format }}", "parameterType": "formData"}, {"name": "report_title", "value": "={{ $('Global Variables').item.json.report_title }}", "parameterType": "formData"}]}, "specifyHeaders": "keypair", "queryParameters": {"parameters": [{"name": "action", "value": "launch"}]}, "headerParameters": {"parameters": [{"name": "X-Requested-With", "value": "n8n"}]}, "httpVariantWarning": "", "nodeCredentialType": "qualys<PERSON>pi", "provideSslCertificates": false}, "credentials": {"qualysApi": {"id": "KdkmNjVYkDUzHAvw", "name": "Qualys account"}}, "typeVersion": 4.2, "extendsCredential": "qualys<PERSON>pi"}, {"id": "3f525e48-2866-42ba-a09d-05b8f5aa092d", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [2200, 480], "parameters": {"options": {"reset": true}}, "typeVersion": 3}, {"id": "e202aab9-f9fe-4f6e-ac50-4d4b3b30c1f4", "name": "Wait 1 Minute", "type": "n8n-nodes-base.wait", "position": [2400, 500], "webhookId": "b99241f2-8b9b-4699-a006-9a3e8457c42c", "parameters": {"unit": "minutes", "amount": 1}, "typeVersion": 1.1}, {"id": "eb8db4f0-eacb-4d3d-ae8c-77c096bbb289", "name": "Check Status of Report", "type": "n8n-nodes-base.httpRequest", "position": [2560, 500], "parameters": {"": "", "url": "={{ $('Global Variables').item.json.base_url }}/api/2.0/fo/report", "method": "GET", "options": {}, "sendBody": false, "sendQuery": true, "curlImport": "", "infoMessage": "", "sendHeaders": false, "specifyQuery": "keypair", "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "action", "value": "list"}, {"name": "id", "value": "={{ $('Convert Report Launch XML to JSON').item.json[\"SIMPLE_RETURN\"][\"RESPONSE\"][\"ITEM_LIST\"][\"ITEM\"][\"VALUE\"] }}"}]}, "httpVariantWarning": "", "nodeCredentialType": "qualys<PERSON>pi", "provideSslCertificates": false}, "credentials": {"qualysApi": {"id": "KdkmNjVYkDUzHAvw", "name": "Qualys account"}}, "typeVersion": 4.2, "extendsCredential": "qualys<PERSON>pi"}, {"id": "7cfcaa0c-7b0e-4704-8268-d5869677a58e", "name": "Is Report Finished?", "type": "n8n-nodes-base.if", "position": [2900, 500], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "97935da6-84fa-4756-83e1-4fbf5861baec", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.REPORT_LIST_OUTPUT.RESPONSE.REPORT_LIST.REPORT.STATUS.STATE }}", "rightValue": "Finished"}]}}, "typeVersion": 2}, {"id": "b1a1f2bf-ddb1-4343-be2e-929128ed502c", "name": "Download Report", "type": "n8n-nodes-base.httpRequest", "position": [3080, 500], "parameters": {"": "", "url": "={{ $('Global Variables').item.json.base_url }}/api/2.0/fo/report/", "method": "GET", "options": {}, "sendBody": false, "sendQuery": true, "curlImport": "", "infoMessage": "", "sendHeaders": false, "specifyQuery": "keypair", "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "action", "value": "fetch"}, {"name": "id", "value": "={{ $('Convert Report Launch XML to JSON').item.json.SIMPLE_RETURN.RESPONSE.ITEM_LIST.ITEM.VALUE }}"}]}, "httpVariantWarning": "", "nodeCredentialType": "qualys<PERSON>pi", "provideSslCertificates": false}, "credentials": {"qualysApi": {"id": "KdkmNjVYkDUzHAvw", "name": "Qualys account"}}, "typeVersion": 4.2, "extendsCredential": "qualys<PERSON>pi"}, {"id": "aa1bb6b0-12db-4624-a682-d719e7463bdb", "name": "<PERSON><PERSON>ck", "type": "n8n-nodes-base.slack", "position": [3400, 540], "parameters": {"options": {"channelId": "=C05LAN72WJK", "initialComment": "=📊 *Test Report* (Scan) by `aztec3am1` is ready!\n\n- *ID:* {{ $('Download Report').item.json[\"REPORT_LIST_OUTPUT\"][\"RESPONSE\"][\"REPORT_LIST\"][\"REPORT\"][\"ID\"] }}\n- *Launch Time:* {{ $('Download Report').item.json[\"REPORT_LIST_OUTPUT\"][\"RESPONSE\"][\"REPORT_LIST\"][\"REPORT\"][\"LAUNCH_DATETIME\"] }}\n- *Output Format:* {{ $('Download Report').item.json[\"REPORT_LIST_OUTPUT\"][\"RESPONSE\"][\"REPORT_LIST\"][\"REPORT\"][\"OUTPUT_FORMAT\"] }}\n- *Size:* {{ $('Download Report').item.binary.data.fileSize }}\n- *Status:* ✅ Finished\n- *Expiration Time:* {{ $('Download Report').item.json[\"REPORT_LIST_OUTPUT\"][\"RESPONSE\"][\"REPORT_LIST\"][\"REPORT\"][\"EXPIRATION_DATETIME\"] }}\n"}, "resource": "file"}, "credentials": {"slackApi": {"id": "hOkN2lZmH8XimxKh", "name": "TheHive Slack App"}}, "typeVersion": 2.2}, {"id": "3ab2cc79-9634-4a8a-ac72-c8e32370572a", "name": "Convert Report Launch XML to JSON", "type": "n8n-nodes-base.xml", "position": [1980, 500], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "c24e8997-8594-4abc-8313-0198abfc7f5d", "name": "Convert Report List to JSON", "type": "n8n-nodes-base.xml", "position": [2740, 500], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "33fa7420-b65f-4af1-8dad-19840b43e8cc", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [860, 500], "parameters": {}, "typeVersion": 1}, {"id": "2c8b286a-0e00-49e1-81c2-e94ef5b7725e", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [820.9673276258711, 38.56257011400896], "parameters": {"color": 7, "width": 489.3146851921929, "height": 655.6477214487218, "content": "![Slack](https://uploads.n8n.io/templates/slack.png)\n## Triggered from Slack Parent Workflow\n\nThis section is triggered by the parent n8n workflow, `Qualys Slack Shortcut Bot`. It is triggered when a user fills out the slack modal popup with data and hits the submit button. \n\nThese modals can be customized to perform various actions and are designed to be mobile-friendly, ensuring flexibility and ease of use. "}, "typeVersion": 1}, {"id": "96cd5a16-f12d-4373-be7b-9ebe1549ccb8", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [1320, 40], "parameters": {"color": 7, "width": 816.4288734746297, "height": 662.0100319801938, "content": "![Qualys](https://uploads.n8n.io/templates/qualys.png)\n## Report ID are retrieved and the Scan report is requested from Qualys\nIn this section, the process begins with the \"Fetch Report IDs\" node, which performs an HTTP GET request to retrieve a list of available report templates. \n\nThis request utilizes predefined API credentials and the output, in XML format, is then converted to JSON by the \"Convert XML to JSON\" node for easier manipulation. Following this, the \"Launch Report\" node sends an HTTP POST request to Qualys to initiate the generation of a report based on parameters like the template ID, output format, and report title, which are dynamically sourced from global variables. \n\nThis node also includes additional configurations such as query parameters and headers to tailor the request. Finally, the \"Convert Report Launch XML to JSON\" node processes the XML response from the report launch, converting it into JSON format. This sequence ensures a streamlined and automated handling of report generation tasks within Qualys, facilitating efficient data processing and integration within the workflow."}, "typeVersion": 1}, {"id": "ec51d524-4cef-4d78-a5d0-38dbe6c53825", "name": "Sticky Note15", "type": "n8n-nodes-base.stickyNote", "position": [2140, 33.01345938069812], "parameters": {"color": 7, "width": 391.7799748314626, "height": 664.948136798539, "content": "![n8n](https://uploads.n8n.io/templates/n8n.png)\n\n## n8n Loop Node\n\nThis node queries the report status at regular intervals (every minute) until the report is marked as finished. Once the report is complete, the loop ends, and the results are posted to Slack as a PDF attachment, ensuring the team is promptly informed. \n\nFor a SOC, continuous monitoring ensures timely updates, while automation of the waiting period frees up analysts' time for other tasks. Prompt notifications to Slack enable quick action on the completed reports, enhancing overall efficiency."}, "typeVersion": 1}, {"id": "894b9ea3-ab3b-4459-8576-49fd107d4c7f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [2540, 36.092592419318635], "parameters": {"color": 7, "width": 670.8185951020379, "height": 655.5577875573053, "content": "![Qualys](https://uploads.n8n.io/templates/qualys.png)\n## Check Status of Report in Qualys API\n\nThis node checks the status of the report in the Qualys API. After parsing the XML response to ensure the report is complete, it submits the report details to Slack. \n\nThis step is crucial for maintaining an automated and efficient workflow. For SOCs, automated monitoring reduces the need for manual checking, ensuring that only completed reports are processed further, which maintains data integrity. \n\nAdditionally, integrating with Slack streamlines operations by seamlessly communicating report statuses."}, "typeVersion": 1}, {"id": "24a96b8a-1ed9-42ee-802b-952000f3cfab", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [3220, 40], "parameters": {"color": 7, "width": 473.6487484083029, "height": 650.1491670103001, "content": "![Slack](https://uploads.n8n.io/templates/slack.png)\n## Upload Report to Slack\n\nThis node automates the process of uploading the generated report to a designated Slack channel. \n\nBy ensuring that the report, whether in PDF or HTML format, is easily accessible to the team, it streamlines communication and enhances collaboration. \n\nFor a Security Operations Center (SOC), this feature significantly improves accessibility, as team members can quickly access the latest reports directly from Slack. \n\nIt also enhances collaboration by sharing reports in a common communication platform and provides real-time updates, allowing for timely review and action."}, "typeVersion": 1}, {"id": "c179e45b-37a8-423f-a542-74e6166b09f0", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [160, 80], "parameters": {"width": 646.7396383244529, "height": 1327.6335333503064, "content": "![n8n](https://uploads.n8n.io/templates/n8n.png)\n# Create Qualys Scan Slack Report Subworkflow\n\n## Introducing the Qualys Create Report Workflow—a robust solution designed to automate the generation and retrieval of security reports from the Qualys API.\n\nThis workflow is a sub workflow of the `Qualys Slack Shortcut Bot` workflow. It is triggered when someone fills out the modal popup in slack generated by the `Qualys Slack Shortcut Bot`.\n\nWhen deploying this workflow, use the Demo Data node to simulate the data that is input via the Execute Workflow Trigger. That data flows into the Global Variables Node which is then referenced by the rest of the workflow. \n\nIt includes nodes to Fetch the Report IDs and then Launch a report, and then check the report status periodically and download the completed report, which is then posted to Slack for easy access. \n\nFor Security Operations Centers (SOCs), this workflow provides significant benefits by automating tedious tasks, ensuring timely updates, and facilitating efficient data handling.\n\n**How It Works:**\n\n- **Fetch Report Templates:** The \"Fetch Report IDs\" node retrieves a list of available report templates from Qualys. This automated retrieval saves time and ensures that the latest templates are used, enhancing the accuracy and relevance of reports.\n  \n- **Convert XML to JSON:** The response is converted to JSON format for easier manipulation. This step simplifies data handling, making it easier for SOC analysts to work with the data and integrate it into other tools or processes.\n  \n- **Launch Report:** A POST request is sent to Qualys to initiate report generation using specified parameters like template ID and report title. Automating this step ensures consistency and reduces the chance of human error, improving the reliability of the reports generated.\n  \n- **Loop and Check Status:** The workflow loops every minute to check if the report generation is complete. Continuous monitoring automates the waiting process, freeing up SOC analysts to focus on higher-priority tasks while ensuring they are promptly notified when reports are ready.\n  \n- **Download Report:** Once the report is ready, it is downloaded from Qualys. Automated downloading ensures that the latest data is always available without manual intervention, improving efficiency.\n  \n- **Post to Slack:** The final report is posted to a designated Slack channel for quick access. This integration with Slack ensures that the team can promptly access and review the reports, facilitating swift action and decision-making.\n\n\n**Get Started:**\n\n- Ensure your [Slack](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.slack/?utm_source=n8n_app&utm_medium=node_settings_modal-credential_link&utm_campaign=n8n-nodes-base.slack) and [Qualys](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/?utm_source=n8n_app&utm_medium=node_settings_modal-credential_link&utm_campaign=n8n-creds-base.qualysApi) integrations are properly set up.\n- Customize the workflow to fit your specific reporting needs.\n\n\n**Need Help?**\n\n- Join the discussion on our Forum or check out resources on Discord!\n\n\nDeploy this workflow to streamline your security report generation process, improve response times, and enhance the efficiency of your security operations."}, "typeVersion": 1}, {"id": "32479679-791d-4c1d-b0c8-9102c3b879a5", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1420, 700], "parameters": {"color": 5, "width": 532.5097590794944, "height": 726.1144174692245, "content": "![Qualys](https://uploads.n8n.io/templates/qualysscanreport.png)\n### 🔄This workflow is triggered by this slack modal. The Report Template Dropdown is powered by another Sub Workflow"}, "typeVersion": 1}, {"id": "0340d311-8b41-4c3e-a023-9ea50301247c", "name": "Demo Data", "type": "n8n-nodes-base.set", "position": [1020, 500], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "47cd1502-3039-4661-a6b1-e20a74056550", "name": "report_title", "type": "string", "value": "Test Report"}, {"id": "9a15f4db-f006-4ad8-a2c0-4002dd3e2655", "name": "output_format", "type": "string", "value": "pdf"}, {"id": "13978e05-7e7f-42e9-8645-d28803db8cc9", "name": "template_name", "type": "string", "value": "Technical Report"}]}}, "typeVersion": 3.3}, {"id": "f007312a-ea15-4188-8461-2f69550d9214", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [820, 700], "parameters": {"color": 5, "width": 596.6847639718076, "height": 438.8903816479826, "content": "![Qualys](https://uploads.n8n.io/templates/qualysscanshortcut.png)\n### 🤖 Triggering this workflow is as easy as typing a backslash in Slack and filling out the modal on the right"}, "typeVersion": 1}], "pinData": {}, "connections": {"Demo Data": {"main": [[{"node": "Global Variables", "type": "main", "index": 0}]]}, "Launch Report": {"main": [[{"node": "Convert Report Launch XML to JSON", "type": "main", "index": 0}]]}, "Wait 1 Minute": {"main": [[{"node": "Check Status of Report", "type": "main", "index": 0}]]}, "Download Report": {"main": [[{"node": "<PERSON><PERSON>ck", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [null, [{"node": "Wait 1 Minute", "type": "main", "index": 0}]]}, "Fetch Report IDs": {"main": [[{"node": "Convert XML To JSON", "type": "main", "index": 0}]]}, "Global Variables": {"main": [[{"node": "Fetch Report IDs", "type": "main", "index": 0}]]}, "Convert XML To JSON": {"main": [[{"node": "Launch Report", "type": "main", "index": 0}]]}, "Is Report Finished?": {"main": [[{"node": "Download Report", "type": "main", "index": 0}], [{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Check Status of Report": {"main": [[{"node": "Convert Report List to JSON", "type": "main", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "Demo Data", "type": "main", "index": 0}]]}, "Convert Report List to JSON": {"main": [[{"node": "Is Report Finished?", "type": "main", "index": 0}]]}, "Convert Report Launch XML to JSON": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}}}