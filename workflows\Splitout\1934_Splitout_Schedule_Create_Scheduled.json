{"id": "ni6SfqC3kthAlPtX", "meta": {"instanceId": "a2eaba9e45ad7aab18b25cf863df1e910fb6dd3b85279bde97d9bae4a72f6862", "templateCredsSetupCompleted": true}, "name": "Personalized AI Tech Newsletter Using RSS, OpenAI and Gmail", "tags": [], "nodes": [{"id": "5cc6bfe1-dbaa-4196-ac52-27e3d5b7e91d", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [440, 0], "parameters": {"options": {}, "fieldToSplitOut": "rss"}, "typeVersion": 1}, {"id": "6d2b402d-22e0-4cc5-a070-8b4169f18a99", "name": "Normalize Fields", "type": "n8n-nodes-base.set", "position": [880, 0], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "e9f27ceb-c5f2-4997-8cb1-67576a7bb337", "name": "title", "type": "string", "value": "={{ $json.title }}"}, {"id": "4c4f9417-40f2-4fb0-9976-d09f5984680f", "name": "content", "type": "string", "value": "={{ $json['content:encodedSnippet'] ?? $json.contentSnippet}}"}, {"id": "e1986bac-054e-4240-ba50-536dbcd27337", "name": "date", "type": "string", "value": "={{ $json.isoDate}}"}]}}, "typeVersion": 3.4}, {"id": "c696de41-aeb1-4e2c-9e7e-8b04f7800bdb", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [1080, 220], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "DyPIpHdVscqT5xeY", "name": "OpenAI Templates Account"}}, "typeVersion": 1.2}, {"id": "4b127a8f-14b3-4a0e-86f6-3157c59bc09c", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [1200, 220], "parameters": {"options": {"metadata": {"metadataValues": [{"name": "title", "value": "={{ $json.title }}"}, {"name": "=createDate", "value": "={{ $now.toISO() }}"}, {"name": "publishDate", "value": "={{ $json.date }}"}]}}, "jsonData": "=# {{ $json.title }}\n{{ $json.content }}", "jsonMode": "expressionData"}, "typeVersion": 1}, {"id": "c32d87cd-28ee-4b28-ad53-43320169b6df", "name": "Recursive Character Text Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "position": [1280, 420], "parameters": {"options": {}, "chunkSize": 3000}, "typeVersion": 1}, {"id": "c912148b-1142-4713-9769-1588ff308c62", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [180, 580], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "gpt-4o"}, "options": {}}, "credentials": {"openAiApi": {"id": "DyPIpHdVscqT5xeY", "name": "OpenAI Templates Account"}}, "typeVersion": 1.2}, {"id": "ba7aef6b-efec-4c35-a9d6-b2b8afb6b6c4", "name": "Get Articles Daily", "type": "n8n-nodes-base.scheduleTrigger", "position": [0, 0], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.2}, {"id": "0827bf1b-1322-4e4a-8c5b-0da90382b202", "name": "Send Weekly Summary", "type": "n8n-nodes-base.scheduleTrigger", "position": [-260, 420], "parameters": {"rule": {"interval": [{"field": "weeks", "triggerAtDay": [1], "triggerAtHour": 5}]}}, "typeVersion": 1.2}, {"id": "b1625ec0-fd2f-4098-ba79-1f522123cb86", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-80, -160], "parameters": {"color": 7, "width": 1620, "height": 740, "content": "## 1. Save news in a vector store (runs daily)"}, "typeVersion": 1}, {"id": "a4abb100-e11f-4ed5-abc3-4587b3a8dcee", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-680, -260], "parameters": {"color": 4, "width": 520, "height": 180, "content": "## Let AI read the tech news for you\n\nThis workflow fetches news via [RSS feeds](https://en.wikipedia.org/wiki/RSS) from selected tech websites, stores them in a vector database and uses an AI agent to send you a weekly, personalized newsletter - keeping you informed without daily distractions."}, "typeVersion": 1}, {"id": "7edbdba1-43ac-4754-91ae-d506ee38e8ff", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-320, 260], "parameters": {"color": 7, "width": 1300, "height": 600, "content": "## 2. Send a summary (runs once a week)"}, "typeVersion": 1}, {"id": "e166715b-f579-4d22-bf2f-9318e4e86f2a", "name": "News reader AI", "type": "@n8n/n8n-nodes-langchain.agent", "position": [200, 360], "parameters": {"text": "=Summarize last week's news.", "options": {"systemMessage": "=Only get last week's news. Act as a tech news aggregator and write in plain, easy-to-understand English. Prioritize news related to the following topics: {{ $json.Interests }}.\nIf none of those topics are mentioned in the news, use your best judgment to highlight the most newsworthy, frequently mentioned and relevant events in technology.\n\nProvide a total of {{ $json['Number of news items to include'] }} news items."}, "promptType": "define"}, "typeVersion": 1.9}, {"id": "c88c6c60-493e-41cf-b08d-3eef48e7cbc4", "name": "Send Newsletter", "type": "n8n-nodes-base.gmail", "position": [760, 360], "webhookId": "0de8b6e8-8611-48a9-ba25-1d023698f577", "parameters": {"sendTo": "<EMAIL>", "message": "={{ $json.data }}", "options": {}, "subject": "Weekly tech newsletter"}, "credentials": {"gmailOAuth2": {"id": "VVLm2UzmGbMNTTNO", "name": "Gmail account 2"}}, "typeVersion": 2.1}, {"id": "8e303102-f68c-4cf8-a2bb-4538830610e6", "name": "Convert Response to an Email-Friendly Format", "type": "n8n-nodes-base.markdown", "position": [560, 360], "parameters": {"mode": "markdownToHtml", "options": {}, "markdown": "={{ $json.output }}"}, "typeVersion": 1}, {"id": "3f90c79c-a04d-4537-b426-33900acfcb8a", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-100, 360], "parameters": {"color": 3, "width": 220, "height": 240, "content": "### Edit this:"}, "typeVersion": 1}, {"id": "de315b7c-065c-45a7-be50-5d7a4eedeeaf", "name": "Your topics of interest", "type": "n8n-nodes-base.set", "position": [-40, 420], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "018882ca-37c3-45af-944f-2081b0605065", "name": "Interests", "type": "string", "value": "AI, games, gadgets"}, {"id": "4cfdafc1-47a4-41cc-9eb8-72880ea34511", "name": "Number of news items to include", "type": "string", "value": "15"}]}}, "typeVersion": 3.4}, {"id": "8a1d6ac3-6fda-4916-a021-3d5db7d413e0", "name": "Store News Articles", "type": "@n8n/n8n-nodes-langchain.vectorStoreInMemory", "position": [1100, 0], "parameters": {"mode": "insert", "memoryKey": "news_store_key"}, "typeVersion": 1.1}, {"id": "b7fd5c59-3ed7-4706-bdd7-a62c62cd65af", "name": "Set Tech News RSS Feeds", "type": "n8n-nodes-base.set", "position": [220, 0], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "b8c00469-890b-4b5b-8e2e-2ad9ec2d0815", "name": "rss", "type": "array", "value": "=[\n  \"https://www.engadget.com/rss.xml\",\n  \"https://feeds.arstechnica.com/arstechnica/index\",\n  \"https://www.theverge.com/rss/index.xml\",\n  \"https://www.wired.com/feed/rss\",\n  \"https://www.technologyreview.com/topnews.rss\",\n  \"https://techcrunch.com/feed/\"\n]\n"}]}}, "typeVersion": 3.4}, {"id": "77f5f3bc-8ecd-481a-a570-6e49e4fda01b", "name": "Read RSS News Feeds", "type": "n8n-nodes-base.rssFeedRead", "position": [660, 0], "parameters": {"url": "={{ $json.rss }}", "options": {"ignoreSSL": false}}, "typeVersion": 1.1}, {"id": "540f55b3-10d1-4f7e-bbdf-793ae6524fd7", "name": "Get News Articles", "type": "@n8n/n8n-nodes-langchain.vectorStoreInMemory", "position": [320, 560], "parameters": {"mode": "retrieve-as-tool", "topK": 20, "toolName": "get_news", "memoryKey": "news_store_key", "toolDescription": "Call this tool to get the latest news articles."}, "typeVersion": 1.1}, {"id": "f5e37288-ef4c-41ea-87bd-1e9ee1e9ab0f", "name": "Embeddings OpenAI2", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [420, 700], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "DyPIpHdVscqT5xeY", "name": "OpenAI Templates Account"}}, "typeVersion": 1.2}, {"id": "f6e050de-8dc1-41dd-a18f-225a2f5f68ad", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [160, -60], "parameters": {"color": 3, "width": 220, "height": 240, "content": "### Edit this:"}, "typeVersion": 1}, {"id": "4d773ce7-cbca-4568-bd40-0f9914e835bb", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-760, -40], "parameters": {"width": 400, "height": 900, "content": "\n### How it works\n\n* A **daily scheduled trigger** fetches articles from multiple popular tech RSS feeds like Wired, TechCrunch, and The Verge.\n\n* Fetched articles are:\n  * **Normalized** to extract titles, summaries, and publish dates.\n  * **Converted to vector embeddings** via OpenAI and stored in memory for fast semantic querying.\n\n* A **weekly scheduled trigger** activates the AI summarization flow:\n  * The AI is provided with your interests (e.g., *AI, games, gadgets*) and the desired number of items (e.g., 15).\n  * It queries the vector store to retrieve relevant articles and summarizes the most newsworthy stories.\n  * The summary is converted into a clean, email-friendly format and sent to your inbox.\n\n---\n\n### How to use\n1. Connect your **OpenAI** and **Gmail** accounts to n8n.\n2. Customize the list of RSS feeds in the “Set Tech News RSS Feeds” node.\n3. Update your interests and number of desired news items in the “Your Topics of Interest” node.\n4. Activate the workflow and let the automation run on schedule.\n\n---\n\n### Requirements\n* **OpenAI** credentials for embeddings and summarization\n* **Gmail** (or another email service) for sending the newsletter"}, "typeVersion": 1}, {"id": "796c2a13-c168-4bc9-b79b-fc80c31274c1", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [680, 620], "parameters": {"color": 5, "width": 520, "height": 280, "content": "### 💡 Customizing this workflow\n\n* Want to use different sources? Swap in your own RSS feeds, or use an API-based news aggregator.\n* Replace the in-memory vector store with **Pinecone**, **Weaviate**, or another persistent vector DB for longer-term storage.\n* Adjust the agent's summarization style to suit internal updates, industry-specific briefings, or even entertainment recaps.\nHere’s an additional bullet point to include under **Customizing this workflow**:\n* Prefer chat over email? Replace the email node with a **Telegram bot** to receive your personalized tech newsletter directly in a Telegram chat.\n\n"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "82cd36c7-4a97-4813-8b2f-8a4d44ccc2da", "connections": {"Split Out": {"main": [[{"node": "Read RSS News Feeds", "type": "main", "index": 0}]]}, "News reader AI": {"main": [[{"node": "Convert Response to an Email-Friendly Format", "type": "main", "index": 0}]]}, "Send Newsletter": {"main": [[]]}, "Normalize Fields": {"main": [[{"node": "Store News Articles", "type": "main", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Store News Articles", "type": "ai_embedding", "index": 0}]]}, "Get News Articles": {"ai_tool": [[{"node": "News reader AI", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "News reader AI", "type": "ai_languageModel", "index": 0}]]}, "Embeddings OpenAI2": {"ai_embedding": [[{"node": "Get News Articles", "type": "ai_embedding", "index": 0}]]}, "Get Articles Daily": {"main": [[{"node": "Set Tech News RSS Feeds", "type": "main", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Store News Articles", "type": "ai_document", "index": 0}]]}, "Read RSS News Feeds": {"main": [[{"node": "Normalize Fields", "type": "main", "index": 0}]]}, "Send Weekly Summary": {"main": [[{"node": "Your topics of interest", "type": "main", "index": 0}]]}, "Set Tech News RSS Feeds": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Your topics of interest": {"main": [[{"node": "News reader AI", "type": "main", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Convert Response to an Email-Friendly Format": {"main": [[{"node": "Send Newsletter", "type": "main", "index": 0}]]}}}