{"id": "CNOMivCLJRGfZnUM", "meta": {"instanceId": "31e69f7f4a77bf465b805824e303232f0227212ae922d12133a0f96ffeab4fef", "templateCredsSetupCompleted": true}, "name": "🦜✨Use OpenAI to Transcribe Audio + Summarize with AI + Save to Google Drive", "tags": [], "nodes": [{"id": "3918995a-a587-40c1-828c-97e75b988a9f", "name": "Gmail User for Approval", "type": "n8n-nodes-base.gmail", "disabled": true, "position": [360, -20], "webhookId": "c46cf421-ddb6-45a8-b83b-80b381666f0e", "parameters": {"sendTo": "={{ $env.EMAIL_ADDRESS_JOE }} ", "message": "=A new was just created in the Audio Recordings folder on Google Drive.  Would you like to continue the workflow and Transcribe the audio file and generate reports.", "options": {"limitWaitTime": {"values": {"resumeUnit": "minutes", "resumeAmount": 45}}}, "subject": "=💡New Audio File Created - Approve Transcription Service", "operation": "sendAndWait", "approvalOptions": {"values": {"approvalType": "double"}}}, "credentials": {"gmailOAuth2": {"id": "1xpVDEQ1yx8gV022", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "44aa6e99-9b4a-4af4-93e3-4b1a50fc7628", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-320, 680], "parameters": {"color": 3, "width": 260, "height": 280, "content": "## 3️⃣ Transcribe Audio"}, "typeVersion": 1}, {"id": "cbf765b5-b888-4e22-b4a2-1d430b557109", "name": "Set Config", "type": "n8n-nodes-base.set", "position": [0, 780], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "2f5cef95-a26b-46ff-ab9a-501187ce4211", "name": "text", "type": "string", "value": "={{ $json.text }}"}, {"id": "ac623698-1263-4b83-8c59-159863d950b9", "name": "datetime", "type": "string", "value": "={{ $now }}"}]}}, "typeVersion": 3.4}, {"id": "bd9cd4aa-6afc-4875-a487-df4f0d3a4a29", "name": "Transcribe with OpenAI", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [-240, 780], "parameters": {"options": {}, "resource": "audio", "operation": "transcribe"}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "4d91f6f7-a89e-44d9-9433-4d9a1df368a2", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [180, 580], "parameters": {"color": 5, "width": 1560, "height": 280, "content": "## 4️⃣ Process Transcript and Generate Structured JSON Report"}, "typeVersion": 1}, {"id": "64421d13-0aff-46f8-bf3e-5fac89ec9c46", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [180, 900], "parameters": {"color": 6, "width": 1560, "height": 280, "content": "## 5️⃣ Process Transcript and Generate Structured JSON -> Markdown Report"}, "typeVersion": 1}, {"id": "769ca5a4-2a54-4ed8-85af-4359b97755bc", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1000, 1220], "parameters": {"color": 2, "width": 460, "height": 280, "content": "## 6️⃣ Save Raw Transcript to Google Drive"}, "typeVersion": 1}, {"id": "aa612f37-f7cd-4cf7-919c-87564f03eef1", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-100, 240], "parameters": {"color": 4, "width": 300, "height": 300, "content": "## 1️⃣ Start Transcription Service"}, "typeVersion": 1}, {"id": "1e520267-6275-4d07-bba8-9ebaf0afbc68", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-100, -140], "parameters": {"width": 700, "height": 340, "content": "## Wait for Google Drive Trigger and Send for User Approval to Proceed (Human in the Loop)\n(optional)"}, "typeVersion": 1}, {"id": "1f07fa9c-ecfc-4534-a0b2-569eca1a3092", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [680, 240], "parameters": {"color": 2, "width": 880, "height": 300, "content": "## 2️⃣ Search and Download Audio File from Google Drive\n💡Note:  Adjust Filter and Limit settings for your needs"}, "typeVersion": 1}, {"id": "b769d523-6b1f-45f2-98b1-4d0f8eb2d7f4", "name": "Filter by .m4a extension", "type": "n8n-nodes-base.filter", "position": [980, 340], "parameters": {"options": {"ignoreCase": true}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": false, "typeValidation": "loose"}, "combinator": "and", "conditions": [{"id": "420e1a9c-2145-4845-b4b0-31a82855a78c", "operator": {"type": "string", "operation": "endsWith"}, "leftValue": "={{ $json.name }}", "rightValue": ".m4a"}]}, "looseTypeValidation": true}, "typeVersion": 2.2}, {"id": "5a67182e-4f13-4b6f-a3e2-863e18af31b0", "name": "Limit to last file", "type": "n8n-nodes-base.limit", "position": [1180, 340], "parameters": {"keep": "lastItems"}, "typeVersion": 1}, {"id": "f42b4efc-6e04-49a1-8bd8-252ebd3dbf42", "name": "Download audio file", "type": "n8n-nodes-base.googleDrive", "position": [1380, 340], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "options": {}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "UhdXGYLTAJbsa0xX", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "4313e19f-ca7a-4982-b3b4-1680c674e696", "name": "Search Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [780, 340], "parameters": {"filter": {"folderId": {"__rl": true, "mode": "list", "value": "1Wqd4zEEb847gFYKoDBbNnXsWEc-kCAm2", "cachedResultUrl": "https://drive.google.com/drive/folders/1Wqd4zEEb847gFYKoDBbNnXsWEc-kCAm2", "cachedResultName": "Audio Recordings"}, "whatToSearch": "files"}, "options": {}, "resource": "fileFolder"}, "credentials": {"googleDriveOAuth2Api": {"id": "UhdXGYLTAJbsa0xX", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "e25655b0-9d30-40d4-9051-bffe38fb41e0", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [2020, 700], "parameters": {"width": 660, "height": 480, "content": "## 7️⃣ Send Transcription Report Links to User"}, "typeVersion": 1}, {"id": "38ff9906-41af-430a-9de9-0500577826a5", "name": "Send Telegram Message", "type": "n8n-nodes-base.telegram", "position": [2460, 980], "webhookId": "bb40ede7-03cf-493d-b051-196b96725925", "parameters": {"text": "=Audio Transcribed and Reports Generated\n{{ $json.id_json.webViewLink }}\n{{ $json.id_markdown.webViewLink }}", "chatId": "={{ $env.TELEGRAM_CHAT_ID }}", "additionalFields": {"parse_mode": "HTML", "appendAttribution": false}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "5b6aedaa-897f-4b76-84d8-3ca18d06cc5c", "name": "Send Gmail Message", "type": "n8n-nodes-base.gmail", "position": [2460, 800], "webhookId": "0a81b95a-cd82-465d-8450-cf38518a4cbb", "parameters": {"sendTo": "={{ $env.EMAIL_ADDRESS_JOE }} ", "message": "={{ $json.message.content }}", "options": {"appendAttribution": false}, "subject": "Audio Transcribed and Reports Generated"}, "credentials": {"gmailOAuth2": {"id": "1xpVDEQ1yx8gV022", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "4500efdb-7a70-40da-97b6-e4668af21a19", "name": "Email Content Formatter", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [2100, 800], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "messages": {"values": [{"content": "=Prapare this HTML template using the following: \n{{ $json.id_json.toJsonString() }}\n{{ $json.id_markdown.toJsonString() }}\n\nEnsure that the 'webViewLink' is always provided.\n\nRespond only with HTML and avoid any preamble or further explanation.  Remove all ``` or ```html from final response.\n\n<style type=\"text/css\">\n    /* Reset styles */\n    body { margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }\n    .wrapper { max-width: 600px; margin: 0 auto; }\n    .content { padding: 30px; line-height: 1.6; color: #333333; }\n    .divider { border-top: 1px solid #eeeeee; margin: 25px 0; }\n    .button { display: inline-block; padding: 12px 24px; background-color: #2563eb; color: white; text-decoration: none; border-radius: 6px; }\n</style>\n</head>\n<body>\n    <div class=\"wrapper\">\n        <div class=\"content\">\n            <h2 style=\"color: #1f2937; margin-bottom: 20px;\">Your Documents</h2>\n            \n            <div style=\"margin-bottom: 30px;\">\n                <h3 style=\"color: #374151; margin-bottom: 12px;\">[name]</h3>\n                <a href=\"[webViewLink]\" class=\"button\" style=\"color: white;\">View Document</a>\n            </div>\n\n            <div class=\"divider\"></div>\n\n            [continue the pattern ...]\n\n        </div>\n    </div>\n</body>"}, {"role": "system"}]}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "287fcc26-69be-43d8-a855-c57658e92ac4", "name": "Summarize to Structured JSON", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [240, 660], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "messages": {"values": [{"content": "=\"Today is \" {{ $now }}  \"Transcript: \" {{  $json.text }}"}, {"role": "system", "content": "## ROLE\nYou are an expert at summarizing long transcripts.\n\n## TASK\nSummarize the provided transcript into a structured JSON format.\n\n## RULES\nReturn only valid JSON in this example format:\n\"transcript_report\": {\n\"title\": \"Notion Buttons\",\n\"summary\": \"A collection of buttons for Notion\",\n\"main_points\": [\"item 1\", \"item 2\", \"item 3\"],\n\"action_items\": [\"item 1\", \"item 2\", \"item 3\"],\n\"follow_up\": [\"item 1\", \"item 2\", \"item 3\"],\n\"stories\": [\"item 1\", \"item 2\", \"item 3\"],\n\"references\": [\"item 1\", \"item 2\", \"item 3\"],\n\"arguments\": [\"item 1\", \"item 2\", \"item 3\"],\n\"related_topics\": [\"item 1\", \"item 2\", \"item 3\"],\n\"sentiment\": \"positive\"\n}"}]}, "jsonOutput": true}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "7b5b751f-a7fa-4983-a0e4-1a79e5c4286c", "name": "Summarize to JSON", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [240, 980], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "messages": {"values": [{"content": "=Transcript: {{  $json.text }}"}, {"role": "system", "content": "=## ROLE: Expert Transcript Analyst\n**Current Date:** {{ $now }}\n**Specialization:** Technical documentation, executive reporting, and information architecture\n\n\n## TASK: Create Structured Transcript Summary\nTransform verbose transcripts into well-organized technical documents using this 3-step process:\n1. Extract key information\n2. Identify natural thematic groupings\n3. Structure for optimal scannability\n\n\n## FORMAT REQUIREMENTS\n[Document Title] - {{ $now }}\n\nExecutive Summary (3-5 sentences)\n- Core purpose of discussion\n- Key decision points\n- Actionable outcomes\n\nDetailed Analysis\n[Topic 1: Clear Section Name]\n- Key statements\n- Supporting data points\n- Action items\n\n[Topic 2: Specific Category]\n-Decision rationale\n-Contradictions/agreements\n-Follow-up requirements\n\n(...continue pattern...)\n\nAdditional Observations\n- Unresolved questions\n- Technical terminology glossary\n- Participant sentiment trends\n\n\n## RULES\n1. **Content Fidelity**\n   - Never add external knowledge\n   - Preserve quantitative data exactly\n   - Maintain speaker intent through paraphrasing\n\n2. **Structural Requirements**\n   - Use H2/H3 headers only\n   - Apply consistent tense (prefer present)\n   - Include timestamps for critical points [00:00]\n\n3. **Style Guidelines**\n   - Technical > conversational tone\n   - Active voice required\n   - Bullet points for lists\n   - **Bold** key decisions\n\n4. **Validation**\n   - Self-check for topic overlap\n   - Verify chronological accuracy\n   - Confirm all action items are highlighted"}]}, "jsonOutput": true}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "e92d8de3-8287-4ecd-9ca6-6f8d18b8942e", "name": "Convert JSO<PERSON> to Markdown", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [620, 980], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "messages": {"values": [{"content": "=Today is: {{ $now }}\nTranscript: {{ $json.message.content.toJsonString() }}"}, {"role": "system", "content": "Convert this transcript summary to a markdown document.  Only respond with text and remove all ``` or ```markdown."}]}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "419e050f-3a9b-4fc0-99f2-2e478f080e06", "name": "Get Filename for JSON", "type": "n8n-nodes-base.set", "position": [980, 660], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "a0c31c80-87c9-41e5-ae07-8639bf52870c", "name": "filename", "type": "string", "value": "={{ $('Download audio file').item.json.id }}-{{ $('Download audio file').item.json.name }}-{{ $('Set Config').item.json.datetime }}.json"}]}}, "typeVersion": 3.4}, {"id": "1307431e-80ea-404c-925f-d3ad5bdaa27c", "name": "Get Filename for <PERSON><PERSON>", "type": "n8n-nodes-base.set", "position": [980, 980], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "a0c31c80-87c9-41e5-ae07-8639bf52870c", "name": "filename", "type": "string", "value": "={{ $('Download audio file').item.json.id }} - {{ $('Download audio file').item.json.name }}- {{ $('Set Config').item.json.datetime }}.md"}]}}, "typeVersion": 3.4}, {"id": "c87a9dcb-3edb-47ba-a059-6e7a6c63c287", "name": "Save JSON file to Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [1180, 660], "parameters": {"name": "={{ $json.filename }}", "content": "={{ $('Summarize to Structured JSON').item.json.message.content.toJsonString() }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "1Wqd4zEEb847gFYKoDBbNnXsWEc-kCAm2", "cachedResultUrl": "https://drive.google.com/drive/folders/1Wqd4zEEb847gFYKoDBbNnXsWEc-kCAm2", "cachedResultName": "Audio Recordings"}, "operation": "createFromText"}, "credentials": {"googleDriveOAuth2Api": {"id": "UhdXGYLTAJbsa0xX", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "2f0c30b5-1c99-4c06-9c1b-8a18033e6921", "name": "Save Markdown file to Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [1180, 980], "parameters": {"name": "={{ $json.filename }}", "content": "={{ $('Convert JSO<PERSON> to <PERSON><PERSON>').item.json.message.content }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "1Wqd4zEEb847gFYKoDBbNnXsWEc-kCAm2", "cachedResultUrl": "https://drive.google.com/drive/folders/1Wqd4zEEb847gFYKoDBbNnXsWEc-kCAm2", "cachedResultName": "Audio Recordings"}, "operation": "createFromText"}, "credentials": {"googleDriveOAuth2Api": {"id": "UhdXGYLTAJbsa0xX", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "abc070ee-c276-410b-a3cf-a8504bf992a1", "name": "Get JSON File Meta", "type": "n8n-nodes-base.googleDrive", "position": [1380, 660], "parameters": {"filter": {"whatToSearch": "files"}, "options": {"fields": ["id", "webViewLink", "name"]}, "resource": "fileFolder", "queryString": "={{ $('Get Filename for JSON').item.json.filename }}"}, "credentials": {"googleDriveOAuth2Api": {"id": "UhdXGYLTAJbsa0xX", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "0961424a-f3a3-4469-962b-6b2a7affd66d", "name": "Get Markdown File Meta", "type": "n8n-nodes-base.googleDrive", "position": [1380, 980], "parameters": {"filter": {"whatToSearch": "files"}, "options": {"fields": ["id", "webViewLink", "name"]}, "resource": "fileFolder", "queryString": "={{ $('Get Filename for Markdown').item.json.filename }}"}, "credentials": {"googleDriveOAuth2Api": {"id": "UhdXGYLTAJbsa0xX", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "bddd4298-7570-48df-a16b-5352909f6530", "name": "Prepare Response JSON", "type": "n8n-nodes-base.set", "position": [1580, 660], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c89d0613-9b1e-4906-a4d2-ecc5fe585f5b", "name": "id_json", "type": "object", "value": "={{ $json }}"}]}}, "typeVersion": 3.4}, {"id": "d3a77222-96ac-4d2e-a98c-b185524bebe0", "name": "Prepare Response Markdown", "type": "n8n-nodes-base.set", "position": [1580, 980], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9e23ce26-bdf5-46c8-9099-02179cd29fc5", "name": "id_markdown", "type": "object", "value": "={{ $json }}"}]}}, "typeVersion": 3.4}, {"id": "41e6a279-426e-4fcd-ad6d-182fbc111d28", "name": "Merge All Paths", "type": "n8n-nodes-base.merge", "position": [1840, 980], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition", "numberInputs": 3}, "typeVersion": 3}, {"id": "e2bb4ed5-f9cd-4807-950c-8f9a4bef1a24", "name": "Save Raw Transcript to Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [1180, 1300], "parameters": {"name": "={{ $('Download audio file').item.json.id }} - {{ $('Download audio file').item.json.name }}- {{ $('Set Config').item.json.datetime }}.txt", "content": "={{  $('Transcribe with OpenAI').item.json.text }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "1Wqd4zEEb847gFYKoDBbNnXsWEc-kCAm2", "cachedResultUrl": "https://drive.google.com/drive/folders/1Wqd4zEEb847gFYKoDBbNnXsWEc-kCAm2", "cachedResultName": "Audio Recordings"}, "operation": "createFromText"}, "credentials": {"googleDriveOAuth2Api": {"id": "UhdXGYLTAJbsa0xX", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "fb2cced9-8c42-43de-b4a3-5dd66ddd24b3", "name": "Start Workflow", "type": "n8n-nodes-base.manualTrigger", "position": [0, 340], "parameters": {}, "typeVersion": 1}, {"id": "c2ef200f-4e45-42ba-a5f2-f7eb47df8f2f", "name": "On File Created Trigger", "type": "n8n-nodes-base.googleDriveTrigger", "disabled": true, "position": [0, -20], "parameters": {"event": "fileCreated", "options": {"fileType": "application/vnd.google-apps.audio"}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "mode": "list", "value": "1Wqd4zEEb847gFYKoDBbNnXsWEc-kCAm2", "cachedResultUrl": "https://drive.google.com/drive/folders/1Wqd4zEEb847gFYKoDBbNnXsWEc-kCAm2", "cachedResultName": "Audio Recordings"}}, "credentials": {"googleDriveOAuth2Api": {"id": "UhdXGYLTAJbsa0xX", "name": "Google Drive account"}}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "a477e29e-d516-439c-ac41-16281dcca559", "connections": {"Set Config": {"main": [[{"node": "Summarize to JSON", "type": "main", "index": 0}, {"node": "Summarize to Structured JSON", "type": "main", "index": 0}, {"node": "Save Raw Transcript to Google Drive", "type": "main", "index": 0}]]}, "Start Workflow": {"main": [[{"node": "Search Google Drive", "type": "main", "index": 0}]]}, "Merge All Paths": {"main": [[{"node": "Send Telegram Message", "type": "main", "index": 0}, {"node": "Email Content Formatter", "type": "main", "index": 0}]]}, "Summarize to JSON": {"main": [[{"node": "Convert JSO<PERSON> to Markdown", "type": "main", "index": 0}]]}, "Get JSON File Meta": {"main": [[{"node": "Prepare Response JSON", "type": "main", "index": 0}]]}, "Limit to last file": {"main": [[{"node": "Download audio file", "type": "main", "index": 0}]]}, "Download audio file": {"main": [[{"node": "Transcribe with OpenAI", "type": "main", "index": 0}]]}, "Search Google Drive": {"main": [[{"node": "Filter by .m4a extension", "type": "main", "index": 0}]]}, "Get Filename for JSON": {"main": [[{"node": "Save JSON file to Google Drive", "type": "main", "index": 0}]]}, "Prepare Response JSON": {"main": [[{"node": "Merge All Paths", "type": "main", "index": 0}]]}, "Get Markdown File Meta": {"main": [[{"node": "Prepare Response Markdown", "type": "main", "index": 0}]]}, "Transcribe with OpenAI": {"main": [[{"node": "Set Config", "type": "main", "index": 0}]]}, "Email Content Formatter": {"main": [[{"node": "Send Gmail Message", "type": "main", "index": 0}]]}, "Gmail User for Approval": {"main": [[{"node": "Search Google Drive", "type": "main", "index": 0}]]}, "On File Created Trigger": {"main": [[{"node": "Gmail User for Approval", "type": "main", "index": 0}]]}, "Convert JSON to Markdown": {"main": [[{"node": "Get Filename for <PERSON><PERSON>", "type": "main", "index": 0}]]}, "Filter by .m4a extension": {"main": [[{"node": "Limit to last file", "type": "main", "index": 0}]]}, "Get Filename for Markdown": {"main": [[{"node": "Save Markdown file to Google Drive", "type": "main", "index": 0}]]}, "Prepare Response Markdown": {"main": [[{"node": "Merge All Paths", "type": "main", "index": 1}]]}, "Summarize to Structured JSON": {"main": [[{"node": "Get Filename for JSON", "type": "main", "index": 0}]]}, "Save JSON file to Google Drive": {"main": [[{"node": "Get JSON File Meta", "type": "main", "index": 0}]]}, "Save Markdown file to Google Drive": {"main": [[{"node": "Get Markdown File Meta", "type": "main", "index": 0}]]}, "Save Raw Transcript to Google Drive": {"main": [[{"node": "Merge All Paths", "type": "main", "index": 2}]]}}}