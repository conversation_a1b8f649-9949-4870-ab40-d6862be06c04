{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [0, 300], "parameters": {}, "typeVersion": 1}, {"name": "Function", "type": "n8n-nodes-base.function", "position": [200, 300], "parameters": {"functionCode": "return [\n  {\n    json: {\n      id: 0,\n    }\n  },\n  {\n    json: {\n      id: 1,\n    }\n  },\n  {\n    json: {\n      id: 2,\n    }\n  }\n];\n"}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [600, 90], "parameters": {"values": {"string": [{"name": "name", "value": "n8n"}]}, "options": {}}, "typeVersion": 1}, {"name": "Set1", "type": "n8n-nodes-base.set", "position": [600, 230], "parameters": {"values": {"string": [{"name": "name", "value": "nodemation"}]}, "options": {}}, "typeVersion": 1}, {"name": "Switch", "type": "n8n-nodes-base.switch", "position": [400, 300], "parameters": {"rules": {"rules": [{"operation": "equal"}, {"output": 1, "value2": 1, "operation": "equal"}, {"output": 2, "value2": 2, "operation": "equal"}]}, "value1": "={{$node[\"Function\"].json[\"id\"]}}", "fallbackOutput": 3}, "typeVersion": 1}, {"name": "Set2", "type": "n8n-nodes-base.set", "position": [600, 370], "parameters": {"values": {"string": [{"name": "name", "value": "nathan"}]}, "options": {}}, "typeVersion": 1}, {"name": "NoOp", "type": "n8n-nodes-base.noOp", "position": [600, 510], "parameters": {}, "typeVersion": 1}], "connections": {"Switch": {"main": [[{"node": "Set", "type": "main", "index": 0}], [{"node": "Set1", "type": "main", "index": 0}], [{"node": "Set2", "type": "main", "index": 0}], [{"node": "NoOp", "type": "main", "index": 0}]]}, "Function": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Function", "type": "main", "index": 0}]]}}}