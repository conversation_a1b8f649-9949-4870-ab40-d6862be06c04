{"meta": {"instanceId": "6b6a2db47bdf8371d21090c511052883cc9a3f6af5d0d9d567c702d74a18820e"}, "nodes": [{"id": "f4570aad-db25-4dcd-8589-b1c8335935de", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [480, 1800], "parameters": {}, "typeVersion": 1}, {"id": "1c1be9d6-3fd5-44c2-a7dd-d291b9efe65b", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-260, 1360], "parameters": {"color": 4, "width": 657.3293805248016, "height": 843.3412992154545, "content": "## Generate SEO Keyword Search Volume Data using Google API\n\n## Use Case\nGenerate accurate search volume data for SEO keyword research:\n- You have a list of potential keywords to target for your website SEO but don't know their actual search volume\n- You need historical data to identify seasonal trends in keyword popularity\n- You want to assess keyword difficulty to prioritize your content strategy\n- You need data-driven insights for planning your SEO campaigns\n\n## What this Workflow Does\nThe workflow connects to Google's Keyword Planner API to retrieve keyword metrics for your SEO research:\n\n- Fetches monthly search volume for each keyword\n- Provides historical trends data for the past 12 months\n- Calculates keyword difficulty scores\n- Delivers competition metrics from Google Ads\n\n\n## Setup\n1. Fill the `Set 20 Keywords` with up to 20 Keywords of your choosing in an array e.g. [\"keyword 1\", \"keyword 2\",...]\n2. Create a Google Ads API account and add credentials to `Get Search Data` node\n3. Replace the `Connect to your own database` with your own database for the output\n\n\n## How to Adjust it to Your Needs\n- Change the `Set 20 Keywords` node input to a source of your choosing e.g. Airtable database with 20 keywords\n- Connect to output source of your choosing \n\n\nMade by <PERSON> @ automake.io"}, "typeVersion": 1}, {"id": "adbbe4ee-d671-4b9b-b619-47f7522e2af4", "name": "Split Out by KW", "type": "n8n-nodes-base.splitOut", "position": [1180, 1800], "parameters": {"options": {}, "fieldToSplitOut": "results"}, "notesInFlow": true, "typeVersion": 1}, {"id": "654c95b4-1018-496e-a0eb-75fddfd98d68", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [622.*************, 1740], "parameters": {"color": 7, "width": 250.**************, "height": 249.**************, "content": "**Set up to 20 keywords** "}, "typeVersion": 1}, {"id": "0ddcd5f2-fb3b-425c-95d3-f22b9b99c3c4", "name": "Sticky Note25", "type": "n8n-nodes-base.stickyNote", "position": [1400, 1740], "parameters": {"color": 7, "width": 231.51775697271012, "height": 213.62075341687063, "content": "**Update record in own Database**"}, "typeVersion": 1}, {"id": "dca7e597-4aa9-440b-8861-2453a5e455fe", "name": "<PERSON><PERSON> Note26", "type": "n8n-nodes-base.stickyNote", "position": [891.5919235222407, 1740], "parameters": {"color": 7, "width": 475.3228796552902, "height": 250.67161641737852, "content": "**POST request to Google API for Keyword Data**"}, "typeVersion": 1}, {"id": "217565a9-0c8b-4725-bbda-bcd1968567ac", "name": "Sticky Note19", "type": "n8n-nodes-base.stickyNote", "position": [620, 2000], "parameters": {"color": 3, "width": 248.59379819295242, "height": 94.39142091152823, "content": "**REQUIRED**\nRemove pinned data in 'Set >= 20 Keywords' to test and connect to own datasource if desired"}, "typeVersion": 1}, {"id": "a836e364-0526-47aa-938a-d32cc47efbd8", "name": "Sticky Note20", "type": "n8n-nodes-base.stickyNote", "position": [880, 2000], "parameters": {"color": 3, "width": 723.************, "height": 217.*************, "content": "**REQUIRED**\nAt this time 15/10/2024 this API endpoint is the latest, it will need to be updated as it changes\nhttps://developers.google.com/google-ads/api/docs/concepts/call-structure\n\n**Replace the following in the HTTP request with your own account values**\n- URL >> customer_id must be your own account customer id e.g. '**********' in https://googleads.googleapis.com/v16/customers/**********:generateKeywordHistoricalMetrics\n- developer-token\n- login-customer-id"}, "typeVersion": 1}, {"id": "3dac2fe3-8710-49cc-87ed-918972d00354", "name": "Sticky Note21", "type": "n8n-nodes-base.stickyNote", "position": [1400, 1640], "parameters": {"color": 3, "width": 284.**************, "height": 80, "content": "**REQUIRED**\nConnect to your own database / GSheet / Airtable base to output these"}, "typeVersion": 1}, {"id": "806fd20d-4bc4-41a3-9ef7-77561e2cfc0c", "name": "Set >=20 Keywords", "type": "n8n-nodes-base.set", "notes": "Insert up to 20 keywords to test", "position": [680, 1800], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "973e949e-1afd-4378-8482-d2168532eff6", "name": "Keyword", "type": "array", "value": "=[\"workflow automation software\", \"enterprise workflow automation\", \"finance automation software\", \"saas automation platform\", \"automation roi calculator\", \"hr process automation\", \"data synchronization software\", \"n8n workflow automation\", \"scalable business operations\", \"n8n vs zapier\", \"lead generation automation\", \"automation consulting services\", \"n8n automation\", \"marketing automation tools\", \"custom automation solutions\", \"ecommerce automation solutions\", \"business process automation\", \"small business automation\", \"no code automation\", \"crm automation integration\"] "}]}}, "notesInFlow": true, "typeVersion": 3.4}, {"id": "430d4950-1e49-460e-bb9b-56e0e825e621", "name": "Connect to your own database.", "type": "n8n-nodes-base.noOp", "position": [1460, 1800], "parameters": {}, "typeVersion": 1}, {"id": "464cfe3f-3a3f-4ec0-882d-861e48916e0b", "name": "Get Search Data", "type": "n8n-nodes-base.httpRequest", "notes": "Seed KW with Vol & Comp\n\nhttps://developers.google.com/google-ads/api/docs/concepts/call-structure Google API call structure", "position": [960, 1800], "parameters": {"url": "https://googleads.googleapis.com/v16/customers/{customer_id}:generateKeywordHistoricalMetrics", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "keywords", "value": "={{ $json.Keyword }}"}, {"name": "keywordPlanNetwork", "value": "GOOGLE_SEARCH"}]}, "headerParameters": {"parameters": [{"name": "content-type", "value": "application/json"}, {"name": "developer-token", "value": "replace-with-value"}, {"name": "login-customer-id", "value": "replace-with-value"}]}, "nodeCredentialType": "googleAdsOAuth2Api"}, "credentials": {"googleAdsOAuth2Api": {"id": "1Htz9e3PoJufbctg", "name": "Google Ads account"}}, "notesInFlow": false, "retryOnFail": true, "typeVersion": 4.2}], "pinData": {"Set >=20 Keywords": [{"Keyword": ["workflow automation software", "enterprise workflow automation", "finance automation software", "saas automation platform", "automation roi calculator", "hr process automation", "data synchronization software", "n8n workflow automation", "scalable business operations", "n8n vs zapier", "lead generation automation", "automation consulting services", "n8n automation", "marketing automation tools", "custom automation solutions", "ecommerce automation solutions", "business process automation", "small business automation", "no code automation", "crm automation integration"]}]}, "connections": {"Get Search Data": {"main": [[{"node": "Split Out by KW", "type": "main", "index": 0}]]}, "Split Out by KW": {"main": [[{"node": "Connect to your own database.", "type": "main", "index": 0}]]}, "Set >=20 Keywords": {"main": [[{"node": "Get Search Data", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set >=20 Keywords", "type": "main", "index": 0}]]}}}