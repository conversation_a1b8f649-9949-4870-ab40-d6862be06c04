{"meta": {"instanceId": "cb484ba7b742928a2048bf8829668bed5b5ad9787579adea888f05980292a4a7"}, "nodes": [{"id": "06fee9d0-e11e-44f1-949f-94abb476e493", "name": "Telegram", "type": "n8n-nodes-base.telegram", "position": [2100, 1020], "parameters": {"text": "={{  $json.message.join(\"\\n\") }}", "additionalFields": {}}, "typeVersion": 1.1}, {"id": "cd51fa93-700e-4d86-a95b-6e65e7eaf616", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [1080, 1020], "parameters": {"rule": {"interval": [{"daysInterval": 7}]}}, "typeVersion": 1.1}, {"id": "720ca9d2-456f-49a0-85df-d38d1ebdf8e1", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [700, 560], "parameters": {"color": 5, "width": 453.88352097764886, "height": 160.98843357558172, "content": "### 👨‍🎤 Setup\nYou will need:\n1. API token to your n8n instance (settings)\n2. Paste the API token in new n8n credentials\n3. Add telegram credentials as well"}, "typeVersion": 1}, {"id": "c168ca04-cd47-4d68-b719-7c9bb4e98920", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [660, 400], "parameters": {"color": 7, "width": 721.389633253837, "height": 432.41702029585565, "content": "# Weekly failures report\n\nThis workflow will check for past executions of a given workflow and will compile and send you a list of failures which happened in the last 7 days.\n"}, "typeVersion": 1}, {"id": "e06a3f4f-db0c-429b-aeee-c6db84a260c7", "name": "Filter for executions of the week", "type": "n8n-nodes-base.filter", "position": [1480, 1018], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "31745f1d-793a-4674-80ab-77afede449d6", "operator": {"type": "dateTime", "operation": "after"}, "leftValue": "={{ $json.startedAt }}", "rightValue": "={{ DateTime.fromMillis(DateTime.now() -  1000 * 60 * 60 * 24 * 7) }}"}, {"id": "0f3e54a2-2bed-4769-8443-c2b0b6e762a9", "operator": {"type": "boolean", "operation": "false", "singleValue": true}, "leftValue": "={{ $json.finished }}", "rightValue": ""}]}}, "typeVersion": 2, "alwaysOutputData": false}, {"id": "93a65d99-f3c7-45c8-acec-8fc30444f363", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1300, 1238], "parameters": {"width": 241, "height": 80, "content": "### 👆🏽 Set credentials to n8n here and select workflow"}, "typeVersion": 1}, {"id": "768980da-6dcc-4f77-bc36-78ee37b4c5f8", "name": "Get all previous executions", "type": "n8n-nodes-base.n8n", "position": [1280, 1018], "parameters": {"filters": {"workflowId": {"__rl": true, "mode": "list", "value": ""}}, "options": {"activeWorkflows": false}, "resource": "execution", "returnAll": true}, "typeVersion": 1}, {"id": "a13d93cc-75ae-4d94-a649-3bece3ad5c34", "name": "Set a message for each failed execution", "type": "n8n-nodes-base.set", "position": [1680, 1018], "parameters": {"include": "selected", "options": {}, "assignments": {"assignments": [{"id": "f7698326-2df6-4fea-b129-e56b108bdc20", "name": "message", "type": "string", "value": "=⚠️ Workflow `{{ $json.workflowData.name }}` failed to run! [execution]({{ $json.id }}) [date]({{ $json.startedAt }})"}]}, "includeOtherFields": true}, "typeVersion": 3.3}, {"id": "0e86db26-099b-421d-b90d-3a51d3c5aae3", "name": "Aggregate all messages", "type": "n8n-nodes-base.aggregate", "position": [1880, 1018], "parameters": {"options": {}, "fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "message"}]}}, "typeVersion": 1}, {"id": "3b794e81-4b9b-460e-820f-d615c816b0fe", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [2120, 1240], "parameters": {"width": 241, "height": 80, "content": "### 👆🏽 Set credentials to Telegram here as well as chat-id"}, "typeVersion": 1}], "pinData": {}, "connections": {"Schedule Trigger": {"main": [[{"node": "Get all previous executions", "type": "main", "index": 0}]]}, "Aggregate all messages": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "Get all previous executions": {"main": [[{"node": "Filter for executions of the week", "type": "main", "index": 0}]]}, "Filter for executions of the week": {"main": [[{"node": "Set a message for each failed execution", "type": "main", "index": 0}]]}, "Set a message for each failed execution": {"main": [[{"node": "Aggregate all messages", "type": "main", "index": 0}]]}}}