{"id": "107", "name": "Get a volume and add it to your bookshelf", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [260, 300], "parameters": {}, "typeVersion": 1}, {"name": "Google Books", "type": "n8n-nodes-base.googleBooks", "position": [460, 300], "parameters": {"resource": "volume", "volumeId": "XxUJ2U2FXtYC", "authentication": "oAuth2"}, "credentials": {"googleBooksOAuth2Api": "google-books"}, "typeVersion": 1}, {"name": "Google Books1", "type": "n8n-nodes-base.googleBooks", "position": [660, 300], "parameters": {"shelfId": "2", "resource": "bookshelfVolume", "volumeId": "={{$node[\"Google Books\"].json[\"id\"]}}", "operation": "add", "authentication": "oAuth2"}, "credentials": {"googleBooksOAuth2Api": "google-books"}, "typeVersion": 1}, {"name": "Google Books2", "type": "n8n-nodes-base.googleBooks", "position": [860, 300], "parameters": {"shelfId": "={{$node[\"Google Books1\"].parameter[\"shelfId\"]}}", "resource": "bookshelfVolume", "myLibrary": true, "authentication": "oAuth2"}, "credentials": {"googleBooksOAuth2Api": "google-books"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Google Books": {"main": [[{"node": "Google Books1", "type": "main", "index": 0}]]}, "Google Books1": {"main": [[{"node": "Google Books2", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Google Books", "type": "main", "index": 0}]]}}}