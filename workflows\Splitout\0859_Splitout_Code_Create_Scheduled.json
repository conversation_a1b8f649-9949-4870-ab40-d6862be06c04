{"meta": {"instanceId": "6c3d8936583f8a98fa8ebe06f510117c0e8fff2df771e73deba4126a853eb55e", "templateCredsSetupCompleted": true}, "nodes": [{"id": "6d0b95c8-db4f-4bc1-b51b-87da0b1cbca9", "name": "Data Collection", "type": "n8n-nodes-base.stickyNote", "position": [820, 3860], "parameters": {"width": 380, "height": 620, "content": "# Data Collection\nFetches latest news articles from two RSS sources: Calcalist and Mako"}, "typeVersion": 1}, {"id": "62a73f4d-229f-44fa-891d-c36dc50bad99", "name": "Data Processing", "type": "n8n-nodes-base.stickyNote", "position": [1260, 3860], "parameters": {"width": 740, "height": 360, "content": "# Data Processing\nFilters, sorts and prepares news articles for AI selection"}, "typeVersion": 1}, {"id": "13092778-b6a3-4436-b69d-f67245999ffe", "name": "AI Selection", "type": "n8n-nodes-base.stickyNote", "position": [2020, 3860], "parameters": {"width": 400, "height": 360, "content": "# AI Selection\nUses GPT-4o to select the top 5 most relevant articles for a senior executive"}, "typeVersion": 1}, {"id": "b1b25c3b-976e-41eb-a82d-e0571ba9b2f2", "name": "Email Generation", "type": "n8n-nodes-base.stickyNote", "position": [1260, 4260], "parameters": {"width": 1160, "height": 520, "content": "# Email Generation\nCreates and sends formatted HTML digest email with selected articles"}, "typeVersion": 1}, {"id": "d846f068-37c2-48d2-96cb-991a42ecadf4", "name": "Send Daily News", "type": "n8n-nodes-base.emailSend", "position": [2220, 4620], "webhookId": "0de4d8cd-3519-4a4a-a05b-a9c973b64141", "parameters": {"html": "={{ $json.html }}", "options": {}, "subject": "=סקירה ה-AI היומית שלך: {{ $json.date_today }}", "toEmail": "<PERSON><PERSON> <<EMAIL>>", "fromEmail": "Elay's AI Assistant <<EMAIL>>"}, "credentials": {"smtp": {"id": "583PMpoYf46gbncd", "name": "SMTP account"}}, "executeOnce": false, "typeVersion": 2.1}, {"id": "1c4ae1dd-bf0e-4726-b106-6b1b868aae2e", "name": "Get Date", "type": "n8n-nodes-base.function", "position": [1300, 4640], "parameters": {"functionCode": "const now = new Date();\nconst options = {\n  timeZone: 'Asia/Jerusalem',\n  day: '2-digit',\n  month: '2-digit',\n  year: 'numeric'\n};\n\n// Format date according to Israeli format\nconst dateToday = new Intl.DateTimeFormat('en-GB', options).format(now);\n\n// Keep the item\nitems[0].json.date_today = dateToday; // 12/04/2025\n\nreturn items;"}, "typeVersion": 1}, {"id": "162bce34-bf3f-4f05-a9eb-dd2c3f6068de", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [1480, 4620], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "executeOnce": true, "typeVersion": 3.1}, {"id": "6444d6ad-efc1-4fec-be03-f9822624b5a6", "name": "Create HTML", "type": "n8n-nodes-base.html", "position": [2220, 4360], "parameters": {"html": "<!DOCTYPE html>\n\n<html dir=\"rtl\">\n<head>\n  <meta charset=\"UTF-8\" />\n</head>\n<body style=\"margin: 0; padding: 20px; background-color: #f4f6fa; font-family: '<PERSON><PERSON><PERSON>', 'Assistant', sans-serif; color: #2c3e50; direction: rtl; text-align: right;\">\n  <div style=\"max-width: 750px; margin: auto; background-color: #ffffff; padding: 30px; border-radius: 14px; box-shadow: 0 6px 20px rgba(0,0,0,0.05); border: 1px solid #e0e6ed;\">\n\n    <h2 style=\"color: #0a3d62; font-size: 26px; font-weight: 700; margin-top: 0; margin-bottom: 24px;\">\n      סקירה ה-AI היומית שלך \"אל תבזבז זמן – תתמקד רק במה שחשוב באמת\"\n    </h2>\n\n    <p style=\"font-size: 16.5px; line-height: 1.8; margin-bottom: 36px; color: #3a3a3a;\">\nלהלן חמשת המאמרים המרכזיים שהתפרסמו ביממה האחרונה, המלווים בתקציר מקצועי שיסייע לך להתעדכן בהתפתחויות הבולטות ביותר בתחומי הכלכלה, הטכנולוגיה והאסטרטגיה.\n    </p>\n\n    <!-- Article 1 -->\n    <div style=\"margin-bottom: 35px;\">\n      <h3 style=\"color: #1e5f74; font-size: 19px; margin-bottom: 12px; font-weight: 600;\">\n        1. <span style=\"font-weight: 700;\">{{ $json.data[0].title }}</span>\n      </h3>\n      <p style=\"font-size: 16px; line-height: 1.7; margin-bottom: 10px; color: #444;\">{{ $json.data[0].summary }}</p>\n      <div style=\"text-align: left;\">\n        <a href=\"{{ $json.data[0].url }}\" style=\"display: inline-block; margin-top: 10px; padding: 10px 20px; background-color: #1e5f74; color: white; text-decoration: none; border-radius: 8px; font-size: 14px;\">לקריאה המלאה</a>\n      </div>\n    </div>\n\n    <!-- Article 2 -->\n    <div style=\"margin-bottom: 35px;\">\n      <h3 style=\"color: #1e5f74; font-size: 19px; margin-bottom: 12px; font-weight: 600;\">\n        2. <span style=\"font-weight: 700;\">{{ $json.data[1].title }}</span>\n      </h3>\n      <p style=\"font-size: 16px; line-height: 1.7; margin-bottom: 10px; color: #444;\">{{ $json.data[1].summary }}</p>\n      <div style=\"text-align: left;\">\n        <a href=\"{{ $json.data[1].url }}\" style=\"display: inline-block; margin-top: 10px; padding: 10px 20px; background-color: #1e5f74; color: white; text-decoration: none; border-radius: 8px; font-size: 14px;\">לקריאה המלאה</a>\n      </div>\n    </div>\n\n    <!-- Article 3 -->\n    <div style=\"margin-bottom: 35px;\">\n      <h3 style=\"color: #1e5f74; font-size: 19px; margin-bottom: 12px; font-weight: 600;\">\n        3. <span style=\"font-weight: 700;\">{{ $json.data[2].title }}</span>\n      </h3>\n      <p style=\"font-size: 16px; line-height: 1.7; margin-bottom: 10px; color: #444;\">{{ $json.data[2].summary }}</p>\n      <div style=\"text-align: left;\">\n        <a href=\"{{ $json.data[2].url }}\" style=\"display: inline-block; margin-top: 10px; padding: 10px 20px; background-color: #1e5f74; color: white; text-decoration: none; border-radius: 8px; font-size: 14px;\">לקריאה המלאה</a>\n      </div>\n    </div>\n\n\n    <!-- Article 4 -->\n    <div style=\"margin-bottom: 35px;\">\n      <h3 style=\"color: #1e5f74; font-size: 19px; margin-bottom: 12px; font-weight: 600;\">\n        4. <span style=\"font-weight: 700;\">{{ $json.data[3].title }}</span>\n      </h3>\n      <p style=\"font-size: 16px; line-height: 1.7; margin-bottom: 10px; color: #444;\">{{ $json.data[3].summary }}</p>\n      <div style=\"text-align: left;\">\n        <a href=\"{{ $json.data[3].url }}\" style=\"display: inline-block; margin-top: 10px; padding: 10px 20px; background-color: #1e5f74; color: white; text-decoration: none; border-radius: 8px; font-size: 14px;\">לקריאה המלאה</a>\n      </div>\n    </div>\n\n    <!-- Article 5 -->\n    <div style=\"margin-bottom: 35px;\">\n      <h3 style=\"color: #1e5f74; font-size: 19px; margin-bottom: 12px; font-weight: 600;\">\n        5. <span style=\"font-weight: 700;\">{{ $json.data[4].title }}</span>\n      </h3>\n      <p style=\"font-size: 16px; line-height: 1.7; margin-bottom: 10px; color: #444;\">{{ $json.data[4].summary }}</p>\n      <div style=\"text-align: left;\">\n        <a href=\"{{ $json.data[4].url }}\" style=\"display: inline-block; margin-top: 10px; padding: 10px 20px; background-color: #1e5f74; color: white; text-decoration: none; border-radius: 8px; font-size: 14px;\">לקריאה המלאה</a>\n      </div>\n    </div>\n\n\n    <!-- Footer -->\n    <div style=\"margin-top: 50px; font-size: 14px; color: #7f8c8d; border-top: 1px solid #e0e6ed; padding-top: 20px; direction: lrt; text-align: left;\">\n      ✨ This daily Israeli economic newsletter was automatically built for you by <b>n8n AI Agent</b> – because technology can work for you\n    </div>\n\n  </div>\n</body>\n</html>"}, "typeVersion": 1.2}, {"id": "cfac2998-11ba-4665-9457-1a0669bf42b0", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "position": [2040, 4360], "parameters": {"options": {}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}, {"id": "dd36ab14-61dc-4b85-af3b-796be18a3169", "name": "Clean Text", "type": "n8n-nodes-base.set", "position": [1860, 4360], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "7b337b47-a1c6-470e-881f-0c038b4917e5", "name": "title", "type": "string", "value": "={{ $('Split Out').item.json.article }}"}, {"id": "ca820521-4fff-4971-84b5-e6e2dbd8bb7a", "name": "summary", "type": "string", "value": "={{ $json['data-calcalist'] }} {{ $json['data-mako'] }}"}, {"id": "0fd9b5e3-44dd-49a3-82c1-3a4aa4698376", "name": "url", "type": "string", "value": "={{ $('Split Out').item.json.link }}"}]}}, "typeVersion": 3.4}, {"id": "ce8a5da1-9ad0-4eca-8fcc-ea744738ac4e", "name": "Extract Text", "type": "n8n-nodes-base.html", "position": [1680, 4360], "parameters": {"options": {}, "operation": "extractHtmlContent", "extractionValues": {"values": [{"key": "data-calcalist", "cssSelector": ".calcalistArticleHeader .subTitle"}, {"key": "data-mako", "cssSelector": ".article-header header h2"}]}}, "typeVersion": 1.2}, {"id": "c8f061f1-57ad-4594-8ff1-baa7f0ef1427", "name": "Fetch HTML", "type": "n8n-nodes-base.httpRequest", "position": [1480, 4360], "parameters": {"url": "={{ $json.link }}", "options": {}}, "typeVersion": 4.2}, {"id": "95b33857-9f20-4ba4-aae0-67a3899c222a", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [1300, 4360], "parameters": {"options": {}, "fieldToSplitOut": "message.content.articles"}, "typeVersion": 1}, {"id": "7433ab1d-e162-469e-951d-af241c714e34", "name": "ChatGPT 4o", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [2060, 4060], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "GPT-4O"}, "options": {}, "messages": {"values": [{"role": "system", "content": "\nYou've received a list of headlines and links to 50 recently published articles.  \nSelect the five most important and relevant articles for a senior CEO of a large company who updates daily on economic, technological and strategic topics.\n\nUse article titles to understand the content of the articles.\n\nAt least one article must be about current affairs and security (not economic topics).\n\nYour output should be in JSON format:\n{\n\"article\": \"\",\n\"link\": \"\"\n}"}, {"role": "system", "content": "=Article list:\n\n{{ $json.chatgpt_input }}"}]}, "jsonOutput": true}, "credentials": {"openAiApi": {"id": "2m1HH5crgPAhTJlv", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "28daaadd-426b-485a-b128-4660491ed6a9", "name": "Clean List", "type": "n8n-nodes-base.code", "position": [1860, 4060], "parameters": {"jsCode": "// Input: items[] - each one is an article\n\n// Step 1: Remove duplicates by link\nconst uniqueMap = new Map();\nfor (const item of items) {\n  const link = item.json.link;\n  if (!uniqueMap.has(link)) {\n    uniqueMap.set(link, item.json);\n  }\n}\n\n// Step 2: Sort by publication date from newest to oldest\nconst uniqueArticles = Array.from(uniqueMap.values());\nuniqueArticles.sort((a, b) => b.pubDate - a.pubDate);\n\n// Step 3: Take the top 50 most recent articles\nconst top20 = uniqueArticles.slice(0, 50);\n\n// Step 4: Build clean, readable, efficient text\nconst formatted = top20.map((article, index) => {\n  const title = article.title?.replace(/\\(\\)$/, '').trim() || 'No Title';\n  const link = article.link || '';\n  return `${index + 1}. ${title}\\n${link}`;\n});\n\nreturn [\n  {\n    json: {\n      chatgpt_input: formatted.join('\\n\\n') // Paragraphs separated by newlines\n    }\n  }\n];"}, "typeVersion": 2}, {"id": "9e041ef2-b440-447e-b3f3-fc3e846cf669", "name": "Sort List", "type": "n8n-nodes-base.sort", "position": [1680, 4060], "parameters": {"options": {}, "sortFieldsUi": {"sortField": [{"order": "descending", "fieldName": "pubDate"}]}}, "typeVersion": 1}, {"id": "781cc3bd-b78b-4a17-8886-e0fbb82c378a", "name": "Remove NaN", "type": "n8n-nodes-base.filter", "position": [1480, 4060], "parameters": {"options": {"ignoreCase": true}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": false, "typeValidation": "strict"}, "combinator": "or", "conditions": [{"id": "046f5bde-6d2c-4dfd-b29b-17be6c34cc1b", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.pubDate }}\n\n", "rightValue": "=NaN"}]}}, "typeVersion": 2.2}, {"id": "d0084e60-4c9d-4f3e-944c-a81e7dabae9c", "name": "Merged RSS", "type": "n8n-nodes-base.merge", "position": [1300, 4060], "parameters": {}, "typeVersion": 3}, {"id": "8178972f-e0c7-462a-8d66-853118756545", "name": "<PERSON> Fields - <PERSON><PERSON>", "type": "n8n-nodes-base.set", "position": [1060, 4040], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "11b653ae-6a43-4e6d-86b8-066384eaa7d6", "name": "title", "type": "string", "value": "={{ $json.title.replace(/\\[PACK\\].*/, \"\").replace(/\\[.*?\\]/g, \"\").trim() }}"}, {"id": "e300ad1b-6b93-45f7-a964-294abbebfd95", "name": "link", "type": "string", "value": "={{ $json.link.replace(/\\/torrent\\/download\\/(\\d+)\\..*/, \"/torrents/$1\") }}"}, {"id": "bd548580-e879-4671-ad4e-603d2496362e", "name": "pubDate", "type": "string", "value": "={{ new Date($json.pubDate).getTime() }}"}]}}, "typeVersion": 3.4}, {"id": "2c8f4766-5338-4319-98f9-1ab9b460b4e5", "name": "<PERSON> - Calcalist", "type": "n8n-nodes-base.set", "position": [1060, 4320], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d0002dd0-3a5a-4f1a-ba6e-d359549f5a1e", "name": "title", "type": "string", "value": "={{$json.title.replace(/^\\[PACK\\] /, \"\").replace(/1080p .*/, \"\")}} ({{$json.content.match(/<strong>Size<\\/strong>:\\s([\\d.]+\\s[KMGT]iB)/)[1]}})"}, {"id": "cd7b2be1-a52e-430b-98a1-2fb30b5cb8c7", "name": "link", "type": "string", "value": "={{ $json.link.replace(/\\/torrent\\/download\\/(\\d+)\\..*/, \"/torrents/$1\") }}"}, {"id": "3b9d50a8-0d46-4a8f-94e9-454bc5153280", "name": "pubDate", "type": "string", "value": "={{ new Date($json.pubDate).getTime() }}"}]}}, "typeVersion": 3.4}, {"id": "cd6173fc-2bb7-40b2-950b-8f09b0be442f", "name": "RSS - Calcalist", "type": "n8n-nodes-base.rssFeedRead", "onError": "continueRegularOutput", "position": [840, 4320], "parameters": {"url": "https://www.calcalist.co.il/GeneralRSS/0,16335,L-8,00.xml", "options": {"ignoreSSL": false}}, "executeOnce": false, "typeVersion": 1.1}, {"id": "06c96a26-485b-4ca8-ab9e-d45da69f9d3d", "name": "RSS - Mako", "type": "n8n-nodes-base.rssFeedRead", "onError": "continueRegularOutput", "position": [840, 4040], "parameters": {"url": "https://storage.googleapis.com/mako-sitemaps/rss-hp.xml", "options": {"ignoreSSL": false}}, "executeOnce": false, "typeVersion": 1.1}, {"id": "a3fef1a0-8e27-4d55-865b-daea95fe2b71", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [500, 4320], "parameters": {"rule": {"interval": [{"triggerAtHour": 20, "triggerAtMinute": null}]}}, "typeVersion": 1.2}], "pinData": {}, "connections": {"Merge": {"main": [[{"node": "Send Daily News", "type": "main", "index": 0}]]}, "Get Date": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Aggregate": {"main": [[{"node": "Create HTML", "type": "main", "index": 0}]]}, "Sort List": {"main": [[{"node": "Clean List", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Fetch HTML", "type": "main", "index": 0}]]}, "ChatGPT 4o": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Clean List": {"main": [[{"node": "ChatGPT 4o", "type": "main", "index": 0}]]}, "Clean Text": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Fetch HTML": {"main": [[{"node": "Extract Text", "type": "main", "index": 0}]]}, "Merged RSS": {"main": [[{"node": "Remove NaN", "type": "main", "index": 0}]]}, "RSS - Mako": {"main": [[{"node": "<PERSON> Fields - <PERSON><PERSON>", "type": "main", "index": 0}]]}, "Remove NaN": {"main": [[{"node": "Sort List", "type": "main", "index": 0}]]}, "Create HTML": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Extract Text": {"main": [[{"node": "Clean Text", "type": "main", "index": 0}]]}, "RSS - Calcalist": {"main": [[{"node": "<PERSON> - Calcalist", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "RSS - Mako", "type": "main", "index": 0}, {"node": "Get Date", "type": "main", "index": 0}, {"node": "RSS - Calcalist", "type": "main", "index": 0}]]}, "Edit Fields - Mako": {"main": [[{"node": "Merged RSS", "type": "main", "index": 0}]]}, "Edit Fields - Calcalist": {"main": [[{"node": "Merged RSS", "type": "main", "index": 1}]]}}}