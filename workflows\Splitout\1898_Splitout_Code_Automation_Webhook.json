{"id": "kJMoiGRorIlsTYZv", "meta": {"instanceId": "558d88703fb65b2d0e44613bc35916258b0f0bf983c5d4730c00c424b77ca36a", "templateCredsSetupCompleted": true}, "name": "Amazon keywords", "tags": [], "nodes": [{"id": "ac4b8cad-b8a3-4cc0-a848-1b6976c1d78a", "name": "Clean Keywords", "type": "n8n-nodes-base.set", "position": [380, 620], "parameters": {"options": {"ignoreConversionErrors": true}, "assignments": {"assignments": [{"id": "fb95058f-0c20-4249-8a45-7b935fde1874", "name": "Keywords", "type": "array", "value": "={{ $json.value }}"}]}}, "typeVersion": 3.3}, {"id": "62575572-e4d2-43e8-9339-d4737961883e", "name": "Get airtable data", "type": "n8n-nodes-base.airtable", "position": [-220, 620], "parameters": {"id": "={{ $json.query.q }}", "base": {"__rl": true, "mode": "list", "value": "appGZ14ny5J2PYbq8", "cachedResultUrl": "https://airtable.com/appGZ14ny5J2PYbq8", "cachedResultName": "Amazon keyword"}, "table": {"__rl": true, "mode": "list", "value": "tblvK8Nq4Jqb2Ubun", "cachedResultUrl": "https://airtable.com/appGZ14ny5J2PYbq8/tblvK8Nq4Jqb2Ubun", "cachedResultName": "Table 1"}, "options": {}}, "credentials": {"airtableTokenApi": {"id": "FV1F34pRcGoKZ8GY", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "e165df91-c212-4c47-8b79-2e637d0fcb7b", "name": "Get Amazon keywords", "type": "n8n-nodes-base.httpRequest", "position": [0, 620], "parameters": {"url": "=https://completion.amazon.com/api/2017/suggestions?mid=ATVPDKIKX0DER&alias=aps&prefix={{ $json.Keyword }}", "options": {}}, "typeVersion": 4.1}, {"id": "49fca0c4-7d1b-4369-9274-2c0b2bb81c8b", "name": "Format output", "type": "n8n-nodes-base.splitOut", "position": [200, 620], "parameters": {"options": {}, "fieldToSplitOut": "suggestions"}, "typeVersion": 1}, {"id": "cb00c467-49dd-4504-b5bb-d512baf55bfd", "name": "Aggregate keywords", "type": "n8n-nodes-base.aggregate", "position": [600, 620], "parameters": {"options": {}, "fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "Keywords"}]}}, "typeVersion": 1}, {"id": "0b04d232-488d-4420-b991-d12b511d5fde", "name": "Combine into string", "type": "n8n-nodes-base.code", "position": [800, 620], "parameters": {"jsCode": "return [{\n  json: {\n    keywords: items[0].json.Keywords.join(\", \")\n  }\n}];"}, "typeVersion": 2}, {"id": "dae32617-6d15-4f30-a27f-894787c137e2", "name": "Save keywords", "type": "n8n-nodes-base.airtable", "position": [1000, 620], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appGZ14ny5J2PYbq8", "cachedResultUrl": "https://airtable.com/appGZ14ny5J2PYbq8", "cachedResultName": "Amazon keyword"}, "table": {"__rl": true, "mode": "list", "value": "tblvK8Nq4Jqb2Ubun", "cachedResultUrl": "https://airtable.com/appGZ14ny5J2PYbq8/tblvK8Nq4Jqb2Ubun", "cachedResultName": "Table 1"}, "columns": {"value": {"id": "={{ $('Get airtable data').item.json.id }}", "Keyword output": "={{ $json.keywords }}"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "Keyword", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Keyword", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON>", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "<PERSON><PERSON>", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Keyword output", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Keyword output", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["id"]}, "options": {}, "operation": "update"}, "credentials": {"airtableTokenApi": {"id": "FV1F34pRcGoKZ8GY", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "aa451c9b-cfc7-4a9a-9ab5-1e6690039eb6", "name": "Receive Keyword", "type": "n8n-nodes-base.webhook", "position": [-460, 620], "webhookId": "e1df17af-e8b8-4261-ba45-aba7106c65bd", "parameters": {"path": "e1df17af-e8b8-4261-ba45-aba7106c65bd", "options": {}, "responseMode": "lastNode"}, "typeVersion": 1.1}, {"id": "8dc19b86-ac56-487d-9678-04c9f8306786", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-560, 140], "parameters": {"width": 589.*************, "height": 163.*************, "content": "## How to build your own Amazon keywords tool with n8n (For free and no coding)\n\nThis workflow gives you Amazon keywords for your Amazon FBA business.\n\n[💡 You can read more about this workflow here](https://rumjahn.com/how-to-build-your-own-amazon-keywords-tool-with-n8n-for-free-and-no-coding/)"}, "typeVersion": 1}, {"id": "c1341984-f1a7-4c7e-8a23-46adea6d2afe", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-520, 420], "parameters": {"color": 4, "width": 239.99999999999977, "height": 389.08073541167073, "content": "## Send keywords \nYou need to send the workflow a keyword through webhook. You can get my airtable example to see how to send keyword.\n[Download airtable here.](https://airtable.com/invite/l?inviteId=invgv9FzNB258bm5Z&inviteToken=6f820e142d3324318254c1768fa57809b3ef0bcb7212ea27730fd2d140c69ad5&utm_medium=email&utm_source=product_team&utm_content=transactional-alerts)"}, "typeVersion": 1}, {"id": "17c13b36-a350-4031-bb9b-a6f8dabd1b90", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-60, 418.41726618705036], "parameters": {"color": 6, "width": 218.65707434052769, "height": 386.2350119904079, "content": "## Send to Amazon\nAmazon has a completion API that gives you keyword data."}, "typeVersion": 1}, {"id": "3deef28b-90b9-4357-a46d-78d750126b65", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [960, 400], "parameters": {"color": 4, "width": 181.6626698641084, "height": 389.08073541167073, "content": "## Save keywords \nDownload my airtable example to save the keywords.\n[Download airtable here.](https://airtable.com/invite/l?inviteId=invgv9FzNB258bm5Z&inviteToken=6f820e142d3324318254c1768fa57809b3ef0bcb7212ea27730fd2d140c69ad5&utm_medium=email&utm_source=product_team&utm_content=transactional-alerts)"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "6db9ae9c-6c1f-48e0-8bb0-b18db21809bf", "connections": {"Format output": {"main": [[{"node": "Clean Keywords", "type": "main", "index": 0}]]}, "Clean Keywords": {"main": [[{"node": "Aggregate keywords", "type": "main", "index": 0}]]}, "Receive Keyword": {"main": [[{"node": "Get airtable data", "type": "main", "index": 0}]]}, "Get airtable data": {"main": [[{"node": "Get Amazon keywords", "type": "main", "index": 0}]]}, "Aggregate keywords": {"main": [[{"node": "Combine into string", "type": "main", "index": 0}]]}, "Combine into string": {"main": [[{"node": "Save keywords", "type": "main", "index": 0}]]}, "Get Amazon keywords": {"main": [[{"node": "Format output", "type": "main", "index": 0}]]}}}