{"nodes": [{"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [440, 440], "parameters": {"triggerTimes": {"item": [{}]}}, "typeVersion": 1}, {"name": "Airtable2", "type": "n8n-nodes-base.airtable", "notes": "Grab our list of chats from Airtable to send a random recipe", "position": [660, 440], "parameters": {"table": "Table 1", "operation": "list", "application": "your_sheet_id", "additionalOptions": {}}, "credentials": {"airtableApi": {"id": "5", "name": "Airtable account"}}, "notesInFlow": true, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [860, 600], "parameters": {"values": {"number": [{"name": "chatid", "value": "={{$node[\"Airtable2\"].json[\"fields\"][\"chatid\"]}}"}], "string": []}, "options": {}}, "typeVersion": 1}, {"name": "Recipe Photo", "type": "n8n-nodes-base.telegram", "position": [1240, 440], "parameters": {"file": "={{$node[\"Get recipes from API\"].json[\"recipes\"][0][\"image\"]}}", "chatId": "={{$node[\"Set\"].json[\"chatid\"]}}", "operation": "sendPhoto", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "1", "name": "Telegram account"}}, "typeVersion": 1, "continueOnFail": true}, {"name": "Recipe URL", "type": "n8n-nodes-base.telegram", "position": [1420, 440], "parameters": {"text": "=\n{{$node[\"Get recipes from API\"].json[\"recipes\"][0][\"title\"]}}\n\n{{$node[\"Get recipes from API\"].json[\"recipes\"][0][\"sourceUrl\"]}}", "chatId": "={{$node[\"Set\"].json[\"chatid\"]}}", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "1", "name": "Telegram account"}}, "typeVersion": 1, "continueOnFail": true}, {"name": "IF", "type": "n8n-nodes-base.if", "notes": "If the chat ID isn't in our airtable, we add it. This is to send a new recipe daily. ", "position": [860, -80], "parameters": {"conditions": {"number": [], "string": [{"value1": "= {{$node[\"Airtable1\"].parameter[\"fields\"][1]}}", "value2": "= {{$node[\"Airtable1\"].parameter[\"fields\"][0]}}", "operation": "notEqual"}], "boolean": []}}, "notesInFlow": true, "typeVersion": 1}, {"name": "Airtable", "type": "n8n-nodes-base.airtable", "position": [620, -80], "parameters": {"table": "Table 1", "operation": "list", "application": "your_sheet_id", "additionalOptions": {}}, "credentials": {"airtableApi": {"id": "5", "name": "Airtable account"}}, "typeVersion": 1}, {"name": "Airtable1", "type": "n8n-nodes-base.airtable", "position": [1340, -100], "parameters": {"table": "Table 1", "fields": ["chatid", "={{$node[\"Telegram Trigger - people join bot\"].json[\"message\"][\"chat\"][\"id\"]}}", "Name", "={{$node[\"Telegram Trigger - people join bot\"].json[\"message\"][\"from\"][\"first_name\"]}}"], "options": {}, "operation": "append", "application": "your_sheet_id", "addAllFields": false}, "credentials": {"airtableApi": {"id": "5", "name": "Airtable account"}}, "typeVersion": 1}, {"name": "Telegram Recipe Image", "type": "n8n-nodes-base.telegram", "position": [980, 180], "parameters": {"file": "={{$node[\"Get recipes\"].json[\"recipes\"][0][\"image\"]}}", "chatId": "={{$node[\"Telegram Trigger - people join bot\"].json[\"message\"][\"chat\"][\"id\"]}}", "operation": "sendPhoto", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "1", "name": "Telegram account"}}, "typeVersion": 1}, {"name": "Telegram Recipe URL", "type": "n8n-nodes-base.telegram", "position": [1180, 180], "parameters": {"text": "=\n{{$node[\"Get recipes\"].json[\"recipes\"][0][\"title\"]}}\n\n{{$node[\"Get recipes\"].json[\"recipes\"][0][\"sourceUrl\"]}}", "chatId": "={{$node[\"Telegram Trigger - people join bot\"].json[\"message\"][\"chat\"][\"id\"]}}", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "1", "name": "Telegram account"}}, "typeVersion": 1}, {"name": "Set1", "type": "n8n-nodes-base.set", "position": [1120, -100], "parameters": {"values": {"string": [{"name": "chatid", "value": "={{$node[\"Telegram Trigger - people join bot\"].json[\"message\"][\"chat\"][\"id\"]}}"}, {"name": "Name", "value": "={{$node[\"Telegram Trigger - people join bot\"].json[\"message\"][\"from\"][\"first_name\"]}}"}]}, "options": {}}, "typeVersion": 1}, {"name": "Get recipes from API", "type": "n8n-nodes-base.httpRequest", "notes": "https://spoonacular.com/food-api/docs", "position": [1080, 440], "parameters": {"url": "https://api.spoonacular.com/recipes/random?apiKey=APIKEYHERE&number=1&tags=vegan", "options": {"fullResponse": false}, "queryParametersUi": {"parameter": []}}, "typeVersion": 1}, {"name": "Get recipes", "type": "n8n-nodes-base.httpRequest", "notes": "https://spoonacular.com/food-api/docs", "position": [800, 180], "parameters": {"url": "https://api.spoonacular.com/recipes/random?apiKey=APIKEYHERE&number=1&tags=vegan", "options": {"fullResponse": false}, "queryParametersUi": {"parameter": []}}, "typeVersion": 1}, {"name": "Telegram Trigger - people join bot", "type": "n8n-nodes-base.telegramTrigger", "position": [420, 140], "webhookId": "your_bot_id", "parameters": {"updates": ["message"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "1", "name": "Telegram account"}}, "typeVersion": 1}, {"name": "Telegram - Welcome Message", "type": "n8n-nodes-base.telegram", "position": [620, 180], "parameters": {"text": "=Welcome! This bot will send you one vegan recipe a day. Here is your first recipe!", "chatId": "={{$node[\"Telegram Trigger - people join bot\"].json[\"message\"][\"chat\"][\"id\"]}}", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "1", "name": "Telegram account"}}, "typeVersion": 1}], "connections": {"IF": {"main": [[{"node": "Set1", "type": "main", "index": 0}]]}, "Set": {"main": [[{"node": "Get recipes from API", "type": "main", "index": 0}]]}, "Cron": {"main": [[{"node": "Airtable2", "type": "main", "index": 0}]]}, "Set1": {"main": [[{"node": "Airtable1", "type": "main", "index": 0}]]}, "Airtable": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}, "Airtable2": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}, "Get recipes": {"main": [[{"node": "Telegram Recipe Image", "type": "main", "index": 0}]]}, "Recipe Photo": {"main": [[{"node": "Recipe URL", "type": "main", "index": 0}]]}, "Get recipes from API": {"main": [[{"node": "Recipe Photo", "type": "main", "index": 0}]]}, "Telegram Recipe Image": {"main": [[{"node": "Telegram Recipe URL", "type": "main", "index": 0}]]}, "Telegram - Welcome Message": {"main": [[{"node": "Get recipes", "type": "main", "index": 0}]]}, "Telegram Trigger - people join bot": {"main": [[{"node": "Airtable", "type": "main", "index": 0}, {"node": "Telegram - Welcome Message", "type": "main", "index": 0}]]}}}