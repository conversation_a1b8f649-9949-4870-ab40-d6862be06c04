{"meta": {"instanceId": "26ba763460b97c249b82942b23b6384876dfeb9327513332e743c5f6219c2b8e"}, "nodes": [{"id": "2b3112a9-046e-4aae-8fcc-95bddf3bb02e", "name": "<PERSON><PERSON>ck", "type": "n8n-nodes-base.slack", "position": [828, 327], "parameters": {"limit": 10, "query": "in:#n8n-tickets has::ticket:", "options": {}, "operation": "search"}, "credentials": {"slackApi": {"id": "VfK3js0YdqBdQLGP", "name": "Slack account"}}, "typeVersion": 2.2}, {"id": "65fd6821-4d19-436c-81d9-9bdb0f5efddd", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1920, 480], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "********-7363-40de-af84-f267f8c7e919", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [2100, 480], "parameters": {"jsonSchema": "{\n \"type\": \"object\",\n \"properties\": {\n \"title\": { \"type\": \"string\" },\n \"summary\": { \"type\": \"string\" },\n \"ideas\": {\n \"type\": \"array\",\n \"items\": { \"type\": \"string\" }\n },\n \"priority\": { \"type\": \"string\" }\n }\n}"}, "typeVersion": 1.1}, {"id": "eda8851a-1929-4f2f-9149-627c0fe62fbc", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [628, 327], "parameters": {"rule": {"interval": [{"field": "minutes"}]}}, "typeVersion": 1.2}, {"id": "ad0d56b5-5caf-4fc0-bdbb-4e6207e4eb03", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [580, 112.**************], "parameters": {"color": 7, "width": 432.*************, "height": 427.**************, "content": "## 1. Query Slack for Messages \n[Read more about the Slack Trigger](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.slack)\n\nSlack API search uses the same search syntax found in the app. Here, we'll use it to filter the latest messages with the ticket emoji within our designated channel called #n8n-tickets. "}, "typeVersion": 1}, {"id": "d4ebe5b3-6d9a-4547-8af8-0985206c4ca4", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1040, 180.44851541532478], "parameters": {"color": 7, "width": 711.6907825442045, "height": 632.7258798316449, "content": "## 2. Decide If We Need to Create a New Ticket \n[Read more about using Linear](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.linear)\n\nFor generated issues, we add the message id to the description of the message so that we can check them at this point in the workflow to avoid duplicates."}, "typeVersion": 1}, {"id": "b2920271-6698-47a4-8cac-ea4cec7b47d6", "name": "Get Values", "type": "n8n-nodes-base.set", "position": [1100, 360], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "={\n \"id\": \"#{{ $json.permalink.split('/').last() }}\",\n \"type\": \"{{ $json.type }}\",\n \"title\": \"__NOT_SET__\",\n \"channel\": \"{{ $json.channel.name }}\",\n \"user\": \"{{ $json.username }} ({{ $json.user }})\",\n \"ts\": \"{{ $json.ts }}\",\n \"permalink\": \"{{ $json.permalink }}\",\n \"message\": \"{{ $json.text.replaceAll('\"','\\\\\"').replaceAll('\\n', '\\\\n') }}\"\n}"}, "typeVersion": 3.3}, {"id": "c4a4db2a-5d1c-4726-8c98-aef57fdcfaa6", "name": "Create New Ticket?", "type": "n8n-nodes-base.if", "position": [1600, 360], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "c11109b6-ee45-4b52-adc3-4be5fe420202", "operator": {"type": "boolean", "operation": "false", "singleValue": true}, "leftValue": "={{ Boolean(($json.hashes ?? []).includes($json.id)) }}", "rightValue": "=false"}]}}, "typeVersion": 2}, {"id": "46acb0de-1df1-4116-8aaf-704ec6644d7c", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1780, 80], "parameters": {"color": 7, "width": 530.6864600881105, "height": 578.3950618708791, "content": "## 3. Use AI to Generate Ticket Contents\n[Read more about using Basic LLM Chain](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.chainllm)\n\nFor this demo, we've instructed the AI to do the following:\n* Generate a descriptive title of the issue\n* Summarise the user message into an actionable request.\n* Determine a prority based on tone and context of the user message. \n* Can offer possible fixes through use of tools or RAG. (not implemented)\n"}, "typeVersion": 1}, {"id": "503d4ae7-9d5b-4dab-94a2-da28bc0e49da", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [200, 120], "parameters": {"width": 359.6648027457353, "height": 400.4748439127683, "content": "## Try It Out!\n### This workflow does the following:\n* Monitors a Slack channel for new user messages asking for assistance\n* Only user messages which are tagged with the ticket(🎫) emoji are processed.\n* Linear is first checked to see if a ticket was created for the user message.\n* User messages are sent to ChatGPT to generate title, description and priority.\n* Support ticket is created in Linear.\n\n### Need Help?\nJoin the [Discord](https://discord.com/invite/XPKeKXeB7d) or ask in the [Forum](https://community.n8n.io/)!\n\nHappy Hacking!"}, "typeVersion": 1}, {"id": "11e423a4-36b6-4ecd-8bf7-58a7d4a1aa9a", "name": "Get Existing Issues", "type": "n8n-nodes-base.linear", "position": [1260, 360], "parameters": {"operation": "getAll"}, "credentials": {"linearApi": {"id": "Nn0F7T9FtvRUtEbe", "name": "Linear account"}}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "413fde96-346a-468e-80b7-d465bd8add14", "name": "Generate Ticket Using ChatGPT", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1920, 320], "parameters": {"text": "=The \"user issue\" is enclosed by 3 backticks:\n```\n{{ $('Get Values').item.json.message }}\n```\nYou will complete the following 4 tasks:\n1. Generate a title intended for a support ticket based on the user issue only. Be descriptive but use no more than 10 words.\n2. Summarise the user issue only by identifying the key expectations and steps that were taken to reach the conclusion.\n3. Offer at most 3 suggestions to debug or resolve the user issue only. ignore the previous issues for this task.\n4. Identify the urgency of the user issue only and denote the priority as one of \"low\", \"medium\", \"high\" or \"urgent\". If you cannot determine the urgency of the issue, then assign the \"low\" priority. Also consider that requests which require action either today or tomorrow should be prioritised as \"high\".", "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.4}, {"id": "66aecf53-6e8a-4ee8-88c3-be6b7d8d0527", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [2340, 206], "parameters": {"color": 7, "width": 374.7406065828194, "height": 352.3865785298774, "content": "## 4. Create New Ticket in Linear\n[Read more about using Linear](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.linear)\n\nWith our ticket contents generated, we can now create our ticket in Linear for support to handle.\n"}, "typeVersion": 1}, {"id": "f7898b7b-f60a-4315-a870-8c8ec4ad848f", "name": "Create Ticket", "type": "n8n-nodes-base.linear", "position": [2480, 380], "parameters": {"title": "={{ $json.output.title }}", "teamId": "1c721608-321d-4132-ac32-6e92d04bb487", "additionalFields": {"stateId": "92962324-3d1f-4cf8-993b-0c982cc95245", "priorityId": "={{ { 'urgent': 1, 'high': 2, 'medium': 3, 'low': 4 }[$json.output.priority.toLowerCase()] ?? 0 }}", "description": "=## {{ $json.output.summary }}\n\n### Suggestions\n{{ $json.output.ideas.map(idea => '* ' + idea).join('\\n') }}\n\n## Original Message\n{{ $('Get Values').item.json[\"user\"] }} asks:\n> {{ $('Get Values').item.json[\"message\"] }}\n\n### Metadata\nchannel: {{ $('Get Values').item.json.channel }}\nts: {{ $('Get Values').item.json.ts }}\npermalink: {{ $('Get Values').item.json.permalink }}\nhash: {{ $('Get Values').item.json.id }}\n"}}, "credentials": {"linearApi": {"id": "Nn0F7T9FtvRUtEbe", "name": "Linear account"}}, "typeVersion": 1}, {"id": "0b706c12-6ce0-41af-ad4b-9d98d7d03a41", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [1440, 360], "parameters": {"mode": "combine", "options": {}, "combinationMode": "multiplex"}, "typeVersion": 2.1}, {"id": "d5b30127-f237-459d-860a-2589e3b54fb8", "name": "Get Hashes Only", "type": "n8n-nodes-base.set", "position": [1260, 640], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9b0e8527-ea17-4b1e-ba62-287111f4b37e", "name": "hashes", "type": "array", "value": "={{ $json.descriptions.map(desc => desc.match(/hash\\:\\s([\\w#]+)/i)[1]) }}"}]}}, "typeVersion": 3.3}, {"id": "9de103e1-b6a4-4454-b1b9-73eff730fcb6", "name": "Collect Descriptions", "type": "n8n-nodes-base.aggregate", "position": [1260, 500], "parameters": {"options": {}, "fieldsToAggregate": {"fieldToAggregate": [{"renameField": true, "outputFieldName": "descriptions", "fieldToAggregate": "description"}]}}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "af34916f-7888-4d41-aee6-752b78e88c0c", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [780, 300], "parameters": {"width": 204.96868508214473, "height": 296.735132421306, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n🚨**Required**\n* Set the Slack channel to monitor here."}, "typeVersion": 1}, {"id": "58ab44f7-5fe5-4804-8bf1-36f351d86528", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [2440, 360], "parameters": {"width": 183.49787916474958, "height": 296.735132421306, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n🚨**Required**\n* Set the Linear Team Name or ID here."}, "typeVersion": 1}], "pinData": {}, "connections": {"Merge": {"main": [[{"node": "Create New Ticket?", "type": "main", "index": 0}]]}, "Slack": {"main": [[{"node": "Get Values", "type": "main", "index": 0}]]}, "Get Values": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}, {"node": "Get Existing Issues", "type": "main", "index": 0}]]}, "Get Hashes Only": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Schedule Trigger": {"main": [[{"node": "<PERSON><PERSON>ck", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Generate Ticket Using ChatGPT", "type": "ai_languageModel", "index": 0}]]}, "Create New Ticket?": {"main": [[{"node": "Generate Ticket Using ChatGPT", "type": "main", "index": 0}]]}, "Get Existing Issues": {"main": [[{"node": "Collect Descriptions", "type": "main", "index": 0}]]}, "Collect Descriptions": {"main": [[{"node": "Get Hashes Only", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Generate Ticket Using ChatGPT", "type": "ai_outputParser", "index": 0}]]}, "Generate Ticket Using ChatGPT": {"main": [[{"node": "Create Ticket", "type": "main", "index": 0}]]}}}