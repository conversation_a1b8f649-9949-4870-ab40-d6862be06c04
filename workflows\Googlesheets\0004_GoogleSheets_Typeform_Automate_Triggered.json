{"id": "1001", "name": "typeform feedback workflow", "nodes": [{"name": "Typeform Trigger", "type": "n8n-nodes-base.typeformTrigger", "notes": "course feedback", "position": [450, 300], "webhookId": "1234567890", "parameters": {"formId": "yxcvbnm"}, "credentials": {"typeformApi": "typeform"}, "notesInFlow": true, "typeVersion": 1}, {"name": "IF", "type": "n8n-nodes-base.if", "notes": "filter feedback", "position": [850, 300], "parameters": {"conditions": {"number": [{"value1": "={{$json[\"usefulness\"]}}", "value2": 3, "operation": "largerEqual"}], "string": [], "boolean": []}}, "notesInFlow": true, "typeVersion": 1}, {"name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "notes": "positive feedback", "position": [1050, 200], "parameters": {"range": "positive_feedback!A:C", "options": {}, "sheetId": "asdfghjklöä", "operation": "append", "authentication": "oAuth2"}, "credentials": {"googleSheetsOAuth2Api": "google_sheets_oauth"}, "notesInFlow": true, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "notes": "capture typeform data", "position": [650, 300], "parameters": {"values": {"number": [{"name": "usefulness", "value": "={{$json[\"How useful was the course?\"]}}"}], "string": [{"name": "opinion", "value": "={{$json[\"Your opinion on the course:\"]}}"}], "boolean": []}, "options": {}, "keepOnlySet": true}, "notesInFlow": true, "typeVersion": 1}, {"name": "Google Sheets1", "type": "n8n-nodes-base.googleSheets", "notes": "negative feedback", "position": [1050, 400], "parameters": {"range": "negative_feedback!A:C", "keyRow": 1, "options": {}, "sheetId": "q<PERSON><PERSON><PERSON><PERSON>", "operation": "append", "authentication": "oAuth2"}, "credentials": {"googleSheetsOAuth2Api": "google_sheets_oauth"}, "notesInFlow": true, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"IF": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}], [{"node": "Google Sheets1", "type": "main", "index": 0}]]}, "Set": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}, "Typeform Trigger": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}}