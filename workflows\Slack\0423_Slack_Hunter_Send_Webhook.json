{"nodes": [{"id": "1a461b8a-090e-4dc4-a3d7-bf976a49828e", "name": "<PERSON><PERSON>ck", "type": "n8n-nodes-base.slack", "position": [1660, 200], "parameters": {"text": "=⭐ Got a hot lead for you  {{ $json.properties.first_name }} {{ $json.properties.last_name }} from  {{ $json.company.properties.name }} ({{ $json.company.properties.domain }}) based out of {{ $json.company.properties.location.state }}, {{ $json.company.properties.location.country }}.\n\n\n{{ $('Score lead with MadKudu').item.json.properties.customer_fit.top_signals_formatted }}", "select": "channel", "channelId": {"__rl": true, "mode": "name", "value": "#interesting_leads"}, "otherOptions": {}}, "credentials": {"slackApi": {"id": "241", "name": "<PERSON>"}}, "typeVersion": 2.1}, {"id": "bcd8e7dc-cb7f-4e2b-a0c6-2d154cb58938", "name": "n8n Form Trigger", "type": "n8n-nodes-base.formTrigger", "position": [380, 420], "webhookId": "0bf8840f-1cc4-46a9-86af-a3fa8da80608", "parameters": {"path": "0bf8840f-1cc4-46a9-86af-a3fa8da80608", "options": {}, "formTitle": "Contact us", "formFields": {"values": [{"fieldLabel": "What's your business email?"}]}, "formDescription": "We'll get back to you soon"}, "typeVersion": 2}, {"id": "c20c626f-fd58-497f-942f-5d10f198f36d", "name": "Check if the email is valid", "type": "n8n-nodes-base.if", "position": [800, 420], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "54d84c8a-63ee-40ed-8fb2-301fff0194ba", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "valid"}]}}, "typeVersion": 2}, {"id": "9c55911c-06b7-4291-a91d-30c0cb87b7f2", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [380, 220], "parameters": {"color": 5, "width": 547, "height": 158, "content": "### 👨‍🎤 Setup\n1. Add you **<PERSON><PERSON><PERSON>u**, **Hunter**, and **Slack** credentials \n2. Set the Slack channel\n3. Click the Test Workflow button, enter your email and check the Slack channel\n4. Activate the workflow and use the form trigger production URL to collect your leads in a smart way "}, "typeVersion": 1}, {"id": "c96096f2-6505-4955-bb1b-c4f903428b1d", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [380, 560], "parameters": {"color": 7, "width": 162, "height": 139, "content": "👆 You can exchange this with any form you like (*e.g. Typeform, Google forms, Survey Monkey...*)"}, "typeVersion": 1}, {"id": "751458aa-7b63-48ab-881e-d68df94a3390", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1360, 500], "parameters": {"color": 7, "width": 162, "height": 84, "content": "👆 Adjust the fit as you see necessary"}, "typeVersion": 1}, {"id": "6416c2ee-59a0-4496-bd62-0a3af06986b7", "name": "Email is not valid, do nothing", "type": "n8n-nodes-base.noOp", "position": [1140, 560], "parameters": {}, "typeVersion": 1}, {"id": "b9ce2ee8-b816-497a-99af-faffdc99ee5f", "name": "Score lead with <PERSON><PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.httpRequest", "position": [1140, 320], "parameters": {"url": "=https://api.madkudu.com/v1/persons?email={{ $json.email }}", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "71W5Bt9g1G9GOhVL", "name": "<PERSON><PERSON><PERSON><PERSON> Lead score"}}, "typeVersion": 4.1}, {"id": "0720ab51-5222-46fe-8a1a-31e25b81920c", "name": "Verify email with <PERSON>", "type": "n8n-nodes-base.hunter", "position": [600, 420], "parameters": {"email": "={{ $json['What\\'s your business email?'] }}", "operation": "emailVerifier"}, "credentials": {"hunterApi": {"id": "ecwmdHFSBU5GGnV1", "name": "Hunter account"}}, "typeVersion": 1}, {"id": "95ec00d2-d926-49ff-a604-1f2d0b291b6f", "name": "Not interesting enough", "type": "n8n-nodes-base.noOp", "position": [1660, 460], "parameters": {}, "typeVersion": 1}, {"id": "5dc270d5-29fd-4620-8ca4-84532cf49c34", "name": "if customer fit score > 60", "type": "n8n-nodes-base.if", "position": [1380, 320], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "c23d7b34-a4ae-421f-bd7a-6a3ebb05aafe", "operator": {"type": "number", "operation": "gt"}, "leftValue": "={{ $json.properties.customer_fit.score }}", "rightValue": 60}]}}, "typeVersion": 2}], "pinData": {"n8n Form Trigger": [{"formMode": "test", "submittedAt": "2024-02-22T13:59:54.709Z", "What's your business email?": "<EMAIL>"}]}, "connections": {"n8n Form Trigger": {"main": [[{"node": "Verify email with <PERSON>", "type": "main", "index": 0}]]}, "Score lead with MadKudu": {"main": [[{"node": "if customer fit score > 60", "type": "main", "index": 0}]]}, "Verify email with Hunter": {"main": [[{"node": "Check if the email is valid", "type": "main", "index": 0}]]}, "if customer fit score > 60": {"main": [[{"node": "<PERSON><PERSON>ck", "type": "main", "index": 0}], [{"node": "Not interesting enough", "type": "main", "index": 0}]]}, "Check if the email is valid": {"main": [[{"node": "Score lead with <PERSON><PERSON><PERSON><PERSON>", "type": "main", "index": 0}], [{"node": "Email is not valid, do nothing", "type": "main", "index": 0}]]}}}