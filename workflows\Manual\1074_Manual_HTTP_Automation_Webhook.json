{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [290, 300], "parameters": {}, "typeVersion": 1}, {"name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [540, 300], "parameters": {"url": "https://reqres.in/api/users", "options": {}}, "typeVersion": 1}, {"name": "HTTP Request1", "type": "n8n-nodes-base.httpRequest", "position": [790, 300], "parameters": {"url": "https://reqres.in/api/users", "options": {}, "requestMethod": "POST", "bodyParametersUi": {"parameter": [{"name": "name", "value": "Neo"}, {"name": "job", "value": "Programmer"}]}}, "typeVersion": 1}, {"name": "HTTP Request2", "type": "n8n-nodes-base.httpRequest", "position": [1050, 300], "parameters": {"url": "https://reqres.in/api/users/2", "options": {}, "requestMethod": "PATCH", "bodyParametersUi": {"parameter": [{"name": "job", "value": "The Chosen One"}]}}, "typeVersion": 1}], "connections": {"HTTP Request": {"main": [[{"node": "HTTP Request1", "type": "main", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "HTTP Request2", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}}}