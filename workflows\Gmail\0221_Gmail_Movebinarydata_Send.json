{"nodes": [{"name": "Gmail", "type": "n8n-nodes-base.gmail", "notes": "Get email with JSON file", "position": [620, 140], "parameters": {"limit": 1, "operation": "getAll", "additionalFields": {}}, "credentials": {"gmailOAuth2": {"id": "16", "name": "gmail"}}, "notesInFlow": true, "typeVersion": 1}, {"name": "write spreadsheet file", "type": "n8n-nodes-base.spreadsheetFile", "position": [980, 140], "parameters": {"options": {"fileName": "users_spreadsheet.csv"}, "operation": "toFile", "fileFormat": "csv"}, "typeVersion": 1}, {"name": "move binary data ", "type": "n8n-nodes-base.moveBinaryData", "position": [800, 140], "parameters": {"options": {}}, "typeVersion": 1}, {"name": "Note6", "type": "n8n-nodes-base.stickyNote", "position": [200, 160], "parameters": {"width": 320, "height": 80, "content": "## JSON file > Sheets"}, "typeVersion": 1}], "connections": {"Gmail": {"main": [[{"node": "move binary data ", "type": "main", "index": 0}]]}, "move binary data ": {"main": [[{"node": "write spreadsheet file", "type": "main", "index": 0}]]}}}