{"id": "mE7Zvhv1lOd4Q3xY", "meta": {"instanceId": "a5283507e1917a33cc3ae615b2e7d5ad2c1e50955e6f831272ddd5ab816f3fb6"}, "name": "CoinMarketCap_AI_Data_Analyst_Agent", "tags": [], "nodes": [{"id": "1eab0bd5-8f9c-4bc4-92b7-50779baa505c", "name": "Telegram Send Message", "type": "n8n-nodes-base.telegram", "position": [1180, 0], "webhookId": "0eeae020-ed6f-4900-ae38-d646d893171d", "parameters": {"text": "={{ $json.output }}", "chatId": "={{ $('Telegram Input').item.json.message.chat.id }}", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "R3vpGq0SURbvEw2Z", "name": "Telegram account"}}, "typeVersion": 1}, {"id": "fd89fa7e-c4e1-4559-a0cc-42beaeccefb4", "name": "Adds SessionId", "type": "n8n-nodes-base.set", "position": [280, 0], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "b5c25cd4-226b-4778-863f-79b13b4a5202", "name": "sessionId", "type": "string", "value": "={{ $json.message.chat.id }}"}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "aea9adc8-8215-4459-9bf0-5a6b6364ffcc", "name": "CoinMarketCap AI Data Analyst Agent", "type": "@n8n/n8n-nodes-langchain.agent", "notes": "{{ $json.sessionId }}", "position": [660, 0], "parameters": {"text": "={{ $json.message.text }}", "options": {"systemMessage": "You are the **CoinMarketCap AI Data Analyst**, a powerful, multi-source crypto intelligence system that integrates three specialized agents:  \n- **CoinMarketCap Crypto Agent**  \n- **CoinMarketCap Exchange & Community Agent**  \n- **CoinMarketCap DEXScan Agent**\n\nYour job is to provide accurate, real-time, and strategic insights into the cryptocurrency landscape across centralized and decentralized platforms.\n\n---\n\n### 🛠️ Tools and Agent Capabilities\n\nYou have access to a suite of **live CoinMarketCap APIs** organized by sub-agents. Each tool is mapped to an endpoint and validated for parameter safety to avoid 400 errors.\n\n---\n\n#### 🔹 1. CoinMarketCap Crypto Agent\n\n**Focus:** Cryptocurrency-level data, listings, quotes, and conversions.\n\n**Tools:**\n- `Crypto Map` – Get coin IDs, names, symbols  \n- `Crypto Info` – Metadata like whitepapers, socials  \n- `Crypto Listings` – Top market cap coins  \n- `Quotes Latest` – Live price, volume, market cap  \n- `Global Metrics` – Total market stats, BTC dominance  \n- `Price Conversion` – Convert one asset to another  \n\n✅ Use for:  \n“Top 10 coins by market cap,” “Convert 5 ETH to USD,” “BTC volume today,” “Whitepaper for SOL”\n\n---\n\n#### 🔹 2. CoinMarketCap Exchange & Community Agent\n\n**Focus:** Exchange intel, community sentiment, and market behavior.\n\n**Tools:**\n- `Exchange Map` – Discover exchanges and get IDs  \n- `Exchange Info` – Metadata like launch date, country, links  \n- `Exchange Assets` – Exchange token holdings & wallets  \n- `CMC 100 Index` – Latest CMC 100 index constituents  \n- `Fear and Greed Index` – Market sentiment tracker  \n\n✅ Use for:  \n“Which tokens does Binance hold?” “Current crypto sentiment” “Top 100 CMC coins”\n\n---\n\n#### 🔹 3. CoinMarketCap DEXScan Agent\n\n**Focus:** Decentralized trading data (spot pairs, pools, liquidity, OHLCV, trades).\n\n**Tools:**\n- `DEX Metadata` – Info for any DEX (logo, date, description)  \n- `DEX Networks List` – All blockchain networks  \n- `DEX Listings Quotes` – DEXs with live trading stats  \n- `DEX Pair Quotes Latest` – Live price/liquidity for spot pairs  \n- `DEX OHLCV Historical` – Historical OHLCV (e.g., 1h, 1d)  \n- `DEX OHLCV Latest` – Real-time OHLCV for current UTC day  \n- `DEX Trades Latest` – Up to 100 recent trades  \n- `DEX Spot Pairs Latest` – All active spot pairs with filters  \n\n✅ Use for:  \n“Price history of USDT/ETH on Uniswap,” “Show DEXs with highest volume,” “Get liquidity of token pair,” “Security scan for PancakeSwap pools”\n\n---\n\n### ⚙️ Multi-Agent Coordination (Advanced Multi-Query Reasoning)\n\nYou are empowered with **advanced multi-query analysis** capabilities:\n- Chain data between agents (e.g., map → quote → historical chart)\n- Use outputs from one tool as inputs for another\n- Automatically fetch required IDs (e.g., exchange ID, contract address) before making a final API call\n- Combine centralized (CEX) and decentralized (DEX) insights into one unified response\n- Filter and compare across timeframes, assets, exchanges, and networks\n\n---\n\n### ⚠️ Validation & Error Prevention Guidelines\n\nTo prevent 400 Bad Request errors:\n- Always include at least **one required field** per endpoint  \n- Use **valid slugs, symbols, or CoinMarketCap IDs**  \n- Don’t use `convert` and `convert_id` together  \n- Use **comma-separated lists** for multi-inputs (if allowed)  \n- Use documented `aux`, `sort`, `interval` fields only  \n- Handle pagination via `scroll_id` or `start/limit` properly  \n\nIf output is too large:\n> ⚠️ “The requested data exceeds the model’s context limit. Please reduce the scope using filters, limits, or sort.”\n\n---\n\n### ✅ Example Tasks You Can Perform\n- “Get liquidity and 24h volume for ETH/USDC on Polygon”\n- “Compare BTC price on Binance vs Uniswap”\n- “Show top 5 DEXs by volume and their top pairs”\n- “Analyze historical price of SHIBA on Ethereum over last 7 days”\n- “Get CoinMarketCap’s sentiment index and top index coins”\n- “List active spot pairs on Arbitrum with volume > $1M and return price, liquidity, and last 24h % change”\n\n---\n\nYou are a **real-time, multi-source AI analyst** purpose-built to extract deep insights from CoinMarketCap’s centralized and decentralized datasets. Use your agents intelligently, validate your queries, and return precise, structured results.\n\nLet’s analyze the crypto world. 🌍📊🧠\n"}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "955f82c6-ce76-4d56-9714-4926a4936cbf", "name": "CoinMarketCap Agent Brain", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [420, 280], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "yUizd8t0sD5wMYVG", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "2c253e1f-5a34-4334-8a8a-98c1e9e937cd", "name": "CoinMarketCap Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [580, 280], "parameters": {}, "typeVersion": 1.3}, {"id": "0878a84b-14a3-4f8e-b94d-339b1c759f4d", "name": "CoinMarketCap Crypto Agent Tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [740, 280], "parameters": {"name": "CoinMarketCap_Crypto_Agent_Tool", "workflowId": {"__rl": true, "mode": "list", "value": "R4EuB1gx1IpMXCJM", "cachedResultName": "JayaFamily Assistant — CoinMarketCap_Crypto_Agent_Tool"}, "workflowInputs": {"value": {"message": "={{ $fromAI(\"message\",\"Populate this with a relevant message to this subagent\")}}", "sessionId": "={{ $json.sessionId }}"}, "schema": [{"id": "message", "type": "string", "display": true, "removed": false, "required": false, "displayName": "message", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "sessionId", "type": "string", "display": true, "removed": false, "required": false, "displayName": "sessionId", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2.1}, {"id": "4a6e4ae9-5ba5-48ab-8198-a7cd8c84b0ee", "name": "CoinMarketCap Exchange and Community Agent Tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [900, 280], "parameters": {"name": "CoinMarketCap_Exchange_and_Community_Agent_Tool", "workflowId": {"__rl": true, "mode": "list", "value": "kbJb4VMD3SZlcS2u", "cachedResultName": "JayaFamily Assistant — CoinMarketCap_Exchange_and_Community_Agent_Tool"}, "workflowInputs": {"value": {"message": "={{ $fromAI(\"message\",\"Populate this with a relevant message to this subagent\")}}", "sessionId": "={{ $json.sessionId }}"}, "schema": [{"id": "sessionId", "type": "string", "display": true, "removed": false, "required": false, "displayName": "sessionId", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "message", "type": "string", "display": true, "removed": false, "required": false, "displayName": "message", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2.1}, {"id": "77ffefe3-9671-4155-baed-d782035b6079", "name": "CoinMarketCap DEXScan Agent Tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [1080, 280], "parameters": {"name": "CoinMarketCap_DEXScan_Agent_Tool", "workflowId": {"__rl": true, "mode": "list", "value": "ImiznkEUWCkKbg1w", "cachedResultName": "JayaFamily Assistant — CoinMarketCap_DEXScan_Agent_Tool"}, "workflowInputs": {"value": {"message": "={{ $fromAI(\"message\",\"Populate this with a relevant message to this subagent\")}}", "sessionId": "={{ $json.sessionId }}"}, "schema": [{"id": "sessionId", "type": "string", "display": true, "removed": false, "required": false, "displayName": "sessionId", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "message", "type": "string", "display": true, "removed": false, "required": false, "displayName": "message", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2.1}, {"id": "d3fc4697-478b-4e6e-8d42-8138ec614748", "name": "Telegram Input", "type": "n8n-nodes-base.telegramTrigger", "position": [-220, 0], "webhookId": "b33d2025-01c2-4386-b677-206a87a1856b", "parameters": {"updates": ["message"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "R3vpGq0SURbvEw2Z", "name": "Telegram account"}}, "typeVersion": 1.1}, {"id": "d1108256-43c3-403f-bb7d-181c6de62f2a", "name": "CMC Multi-Agent Guide", "type": "n8n-nodes-base.stickyNote", "position": [-1600, -1600], "parameters": {"width": 1180, "height": 1960, "content": "# 📊 CoinMarketCap AI Analyst Agent (n8n Workflow)\n\n## 🧠 Multi-Agent System Overview\nThis is the **primary supervisor agent** for the **CoinMarketCap AI Analyst Workflow**, designed using **modular AI agent architecture** in **n8n**.\n\n⚠️ **This workflow requires 3 external tool workflows to function properly.** You must download, install, and connect the following:\n\n### 🔌 Required Sub-Agent Tools:\n1. **CoinMarketCap_Crypto_Agent_Tool** – <PERSON><PERSON> cryptocurrency quotes, listings, conversions\n2. **CoinMarketCap_Exchange_and_Community_Agent_Tool** – Handles exchanges, trending tokens, Fear & Greed Index\n3. **CoinMarketCap_DEXScan_Agent_Tool** – Handles decentralized liquidity, pair quotes, OHLCV analysis\n\nOnce installed, these agents enable advanced capabilities:\n\n### ✅ Key AI Functions:\n- Analyze market caps, volumes, supply metrics across coins\n- Track new listings and top gainers/losers\n- Evaluate trading pairs and liquidity in CEX and DEX markets\n- Retrieve sentiment indicators and trending discussions\n\n---\n\n## 🧠 Node Structure Summary\n\n### **1️⃣ Analyst Brain**\n- **Model**: GPT-4o Mini\n- **Function**: Understands user queries, delegates tasks to agents\n\n### **2️⃣ Memory Buffer**\n- Stores session state and context between prompts\n\n### **3️⃣ Tool Triggers**\n- **toolWorkflow()** function calls: \n   - `CoinMarketCap_Crypto_Agent_Tool`\n   - `CoinMarketCap_Exchange_and_Community_Agent_Tool`\n   - `CoinMarketCap_DEXScan_Agent_Tool`\n\n---\n\n## ⚠️ Notes:\n- 📎 Make sure API credentials are installed and valid for each agent\n- 📍 Each tool runs independently but feeds results to the supervisor for synthesis\n- 🧩 Use `message` and `sessionId` parameters consistently in every sub-agent call\n\n# 📊 CoinMarketCap AI Analyst Agent Tools (n8n Workflow) Guide\n\n## 🚀 Workflow Overview\nThe **CoinMarketCap AI Analyst Agent** is a modular AI-powered system built on **n8n** to deliver **real-time crypto market insights**. It connects directly to CoinMarketCap APIs across three specialized agents:\n\n- **Cryptocurrency Agent** – Market listings, quotes, conversions, and token info.\n- **Exchange & Community Agent** – Trending topics, exchange performance, and sentiment.\n- **DEXScan Agent** – Liquidity, trading volume, and OHLC data on decentralized markets.\n\n### 🎯 **Key Capabilities**:\n- Fetch latest token listings and rank movements\n- Track real-time price quotes and convert values between currencies\n- Compare metrics like market cap, volume, and dominance\n- Monitor exchange market pairs and volume\n- Analyze community sentiment and Fear & Greed Index\n- Visualize DEX liquidity and historical trading trends\n\n---\n\n## 🔗 Node Architecture Summary\n\n### **1️⃣ AI Analyst Brain**\n- **Type**: GPT-4o Mini\n- **Function**: Interprets prompts and queries, routes requests to proper sub-agent.\n\n### **2️⃣ Session Memory**\n- **Type**: Memory Buffer\n- **Function**: Maintains query context during conversation.\n\n### **3️⃣ Tool Agents**\n- **Type**: Tool Workflow\n- Cryptocurrency / Exchange / DEXScan agent endpoints trigger APIs with mapped params.\n\n"}, "typeVersion": 1}, {"id": "5800cdc3-7d4b-4385-8401-b5913a43a28d", "name": "CMC Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1260, -1600], "parameters": {"color": 3, "width": 680, "height": 600, "content": "## ⚠️ Error Handling Guide\n\n| **Error Code** | **Meaning** |\n|---------------|------------|\n| `200` | Success |\n| `400` | Bad Request (invalid query/params) |\n| `401` | Unauthorized (missing or invalid API key) |\n| `429` | Rate Limit Exceeded |\n| `500` | CoinMarketCap server error |\n\n### 🔍 Common Fixes\n- Ensure `symbol`, `slug`, or `id` match valid CoinMarketCap entries\n- Use correct `timestamp`, `network`, and pagination parameters\n- Rate-limit high-frequency queries to avoid 429 errors\n\n---\n\n## 🚀 Need Help?\nFor custom CoinMarketCap agent support, dashboards, or token data automation, connect:\n\n🌐 **Don <PERSON> — LinkedIn**  \n🔗 [http://linkedin.com/in/donjayamahajr](http://linkedin.com/in/donjayamahajr)\n\n© 2025 Treasurium Capital Limited Company. All rights reserved.\nThis AI workflow architecture, including logic, design, and prompt structures, is the intellectual property of Treasurium Capital Limited Company. Unauthorized reproduction, redistribution, or resale is prohibited under U.S. copyright law. Licensed use only.\n"}, "typeVersion": 1}, {"id": "068e7732-d92e-4a1d-a4b5-c0ee6363f3fb", "name": "CMC Sticky Note", "type": "n8n-nodes-base.stickyNote", "position": [0, -1600], "parameters": {"color": 5, "width": 900, "height": 1500, "content": "## 📌 How to Use the Workflow\n\n### ✅ Step 1: Provide Inputs\n- Use token `symbol`, `slug`, or `ID`\n- Set timestamps (`before`, `after`) in **Unix format** for historical data\n- Use `chain`, `limit`, and `start` for pagination when needed\n\n### ✅ Step 2: Execute API Tools\n- The AI routes queries to sub-agents: **Cryptocurrency**, **Exchange**, or **DEXScan**\n\n### ✅ Step 3: Get Response & Output\n- Results can be output to Telegram, dashboards, or n8n HTTP Response nodes\n\n---\n\n## 🗣️ Example Questions to Ask the CMC AI Analyst\n\n### 💬 Market Intelligence\n- \"What are the top 5 tokens by trading volume right now?\"\n- \"Which coins gained the most in the last 24 hours?\"\n- \"What’s the total crypto market cap today?\"\n\n### 💬 Token Insights\n- \"What’s the price of SOL in USD?\"\n- \"How much is 1000 USDT in BTC?\"\n- \"Show me the description and whitepaper link for Dogecoin.\"\n\n### 💬 Exchange & Sentiment\n- \"What’s the Fear & Greed index today?\"\n- \"List exchanges with the highest asset holdings.\"\n- \"Give me info about Binance – when was it launched?\"\n\n### 💬 DEX Data\n- \"Show me the top DEX spot pairs on Ethereum.\"\n- \"What’s the OHLCV data for SOL-USDT on Solana over the last 7 days?\"\n- \"What trades just occurred on PancakeSwap?\"\n\n---\n\n## ⚠️ Example API Queries\n\n### 1️⃣ Get Top 5 Tokens by Volume\n```plaintext\nGET /v1/cryptocurrency/listings/latest?sort=volume_24h&limit=5\n```\n\n### 2️⃣ Convert 1000 USDT to BTC\n```plaintext\nGET /v1/tools/price-conversion?amount=1000&symbol=USDT&convert=BTC\n```\n\n### 3️⃣ Check Fear & Greed Index\n```plaintext\nGET /v3/fear-and-greed/latest\n```\n\n### 4️⃣ Get OHLCV of DEX Pair\n```plaintext\nGET /v4/dex/pairs/ohlcv/historical?network=solana&pair=SOL-USDT&interval=1d\n```\n\n---"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "ed2f29c5-293a-4796-8986-9c5f9980c6c6", "connections": {"Adds SessionId": {"main": [[{"node": "CoinMarketCap AI Data Analyst Agent", "type": "main", "index": 0}]]}, "Telegram Input": {"main": [[{"node": "Adds SessionId", "type": "main", "index": 0}]]}, "CoinMarketCap Memory": {"ai_memory": [[{"node": "CoinMarketCap AI Data Analyst Agent", "type": "ai_memory", "index": 0}]]}, "CoinMarketCap Agent Brain": {"ai_languageModel": [[{"node": "CoinMarketCap AI Data Analyst Agent", "type": "ai_languageModel", "index": 0}]]}, "CoinMarketCap Crypto Agent Tool": {"ai_tool": [[{"node": "CoinMarketCap AI Data Analyst Agent", "type": "ai_tool", "index": 0}]]}, "CoinMarketCap DEXScan Agent Tool": {"ai_tool": [[{"node": "CoinMarketCap AI Data Analyst Agent", "type": "ai_tool", "index": 0}]]}, "CoinMarketCap AI Data Analyst Agent": {"main": [[{"node": "Telegram Send Message", "type": "main", "index": 0}]]}, "CoinMarketCap Exchange and Community Agent Tool": {"ai_tool": [[{"node": "CoinMarketCap AI Data Analyst Agent", "type": "ai_tool", "index": 0}]]}}}