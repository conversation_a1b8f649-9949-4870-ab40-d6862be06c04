{"id": "eHuvG2I1vOYj0U6k", "meta": {"instanceId": "1c7be698fdfa769249b0c65dcf8862b184efc981b9cec697fe71be1be502c151"}, "name": "My workflow", "tags": [{"id": "isKwzRd30jBHOwft", "name": "AI", "createdAt": "2025-03-20T12:19:54.225Z", "updatedAt": "2025-03-20T12:19:54.225Z"}, {"id": "14BO5kV7hwR3aVmH", "name": "OAuth", "createdAt": "2025-03-20T12:19:58.622Z", "updatedAt": "2025-03-20T12:19:58.622Z"}, {"id": "hzAAB0A7DmXlEfor", "name": "Service", "createdAt": "2025-03-20T12:20:03.063Z", "updatedAt": "2025-03-20T12:20:03.063Z"}], "nodes": [{"id": "6503d6be-e4f3-4a06-b027-9fb210788a30", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [80, 340], "parameters": {"inputSource": "jsonExample", "jsonExample": "{\n  \"name\" : \"Atlassian\",\n  \"audience\" : \"api.atlassian.com\"\n}"}, "typeVersion": 1.1}, {"id": "d6246380-096b-458f-a52c-b263c1e4b800", "name": "LLM Bus", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [400, 340], "parameters": {"text": "You are an AI agent tasked with identifying the (pretty-print) OAuth service name, audience, authorization URI, and token URI.\nThe input is only a name bearing on the OAUth service, e.g.:\n1. <PERSON><PERSON>.  The name <PERSON><PERSON> must be resolved to the correlated API service, in this case, Atlassian.  OAuth information can be gleaned from https://developer.atlassian.com/.\n2. Sage. This is potentially a vague name.  However, in the context of API and OAuth, it is probably Sage300 the ERP system.  OAuth information can be gleaned from https://developer.sage.com.\n3. SAP. This can be the SAP HANA Cloud Platform. Authorization is usually be dedicated URL, e.g., https://<host_name>:<port_number>/sap/bc/sec/oauth2/client/grant/authorization?\n4. Google. This can be the Google API, e.g., https://accounts.google.com/o/oauth2/v2/auth? with audience as project-id-random-value.apps.googleusercontent.com.\n\nObtaining these details by just knowing the pretty-name of the service might be cumbersome.  Therefore a confidence score, as a probabilistic indication your confidence\nof the data must be calculated.  Express your confidence score on a scale of 1 (absolute certainty) down to almost zero (least certain), i.e., confidence NUMERIC(3, 2) CHECK (confidence >= 0.1 AND confidence <= 1.0).\nIf you can't obtain information, invent the data, but justify your improvisation by assigning a very low confidence score.  You must always return a result, no matter\nhow low your confidence.\n\nThese Instructions comprise a Context Understanding, Information Retrieval, Output Format, an Example, with Accuracy and Verification.  \n1. Context Understanding: The name (as input) value represents the target API or service. You need to identify the service name, audience, authorization URI, and token URI based on the name value. \n2. Information Retrieval: Use reputable sources and official documentation to find the correct service name, audience, authorization URI and token URI.\n3. Output Format: Service Name: [Service Name], [Audience], [Authorization URI], [Token URI], [Details]: (Your choice rationale in about 100 words to justify your answer), and lastly the [Confidence] (probability factor) as a numeric value 0<x≤1andx∈{0.01,0.02,…,1.00}.\n4. Example 1: If the name is Sage, the service is probably something like sage300.yourdomain.com (where the yourdomain is clearly a proprietary name), the authorization and token uri follow the same pattern, e.g., \na. Service Name: Sage 300,\nb. Audience: sage300.yourdomain.com\nc. Authorization URI: https://sage300.yourdomain.com/oauth/authorize?, \nd. Token URI: https://sage300.yourdomain.com/oauth/token, \ne. Details: Your domain is embedded in the standard presentation of the audience, authorization uri, and the token uri.  Therefore I substituted the standard representations of the OAuth pattern. \nf. Confidence: 0.90 (=> 0<x≤1andx∈{0.01,0.02,…,1.00})\n5. Example 2: If the name is Jira, the API service is probably Atlassian the elements: \na. Service Name: Atlassian, \nb. Audience: api.atlassian.com\nc. Authorization URI: https://auth.atlassian.com/authorize?, \nd. Token URI: https://auth.atlassian.com/oauth/token, \ne. Details: I have referenced the Atlassian online API documentation and retrieved the standard presentation of the audience, authorization uri, and the token uri from their documentation. \nf. Confidence: 1 (=> 0<x≤1andx∈{0.01,0.02,…,1.00})\n6. Accuracy and Verification: Double-check the information to ensure it is correct and up-to-date. If the name value is ambiguous or not well-documented, provide the best possible match based on available information, or improvise an answer based on the patterns of OAuth, but assign a low Confidence.\n7. Improvisation when OAuth elements relating to the provided {{ $json.name }} (API service name) cannot be determined with high confidence you should specify common patterns or fallback options for OAuth and perform additional searches to cross-reference multiple sources to improve accuracy.\n", "messages": {"messageValues": [{"type": "HumanMessagePromptTemplate", "message": "=The OAuth requester wants you to define the:  OAuth Service (pretty print) Name, the audience (parameter in the authorization uri), the aurhorization_uri (for the API OAuth authorization call), the token_uri (for the token call), your explanation for choosing these values, and your Confidence about the information you provided. \nOutput the service_name, audience, authorization_uri, token_uri, details, and the Confidence factor.\nOAuth Service for which to obtain configuration: {{ $json.name }}.  \nProvide the requester with the pretty-print OAuth Service Name, the audience (authorization parameter) the authorization_uri, the token_uri, your rationale (in n more than 75 words) for choosing the values, and the Confidence factor pertaining."}]}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.5}, {"id": "90cdcbfa-cf7e-4123-b241-dafaea12d1a4", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [640, 580], "parameters": {"schemaType": "manual", "inputSchema": "{\n  \"$schema\": \"http://json-schema.org/draft-07/schema#\",\n  \"title\": \"Generated schema for Root\",\n  \"type\": \"object\",\n  \"properties\": {\n    \"action\": {\n      \"type\": \"string\"\n    },\n    \"text\": {\n      \"type\": \"string\"\n    }\n  },\n  \"required\": [\n    \"action\",\n    \"text\"\n  ]\n}"}, "typeVersion": 1.2}, {"id": "5f8cb1e1-39e7-4617-9b56-e0b41bfee466", "name": "OpenRouter <PERSON>", "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "position": [340, 580], "parameters": {"model": "latitudegames/wayfarer-large-70b-llama-3.3", "options": {"topP": 0.9, "maxTokens": 2500, "maxRetries": 2, "temperature": 0.5, "responseFormat": "json_object", "presencePenalty": 0.6, "frequencyPenalty": 0.5}}, "credentials": {"openRouterApi": {"id": "QRSxlMSE2Tacaxcl", "name": "OpenRouter account"}}, "typeVersion": 1}, {"id": "********-1408-4dbc-9108-************", "name": "Conform JSON", "type": "n8n-nodes-base.code", "position": [900, 340], "parameters": {"jsCode": "// Extract the relevant information from the original output\nconst items =$input.all();\n// Extract the relevant information from the input\nconst originalText = items[0].json.output.text;\n\n// Parse the text to extract the required fields\nconst lines = originalText.split('\\n');\nconst service_name = lines[0].split(': ')[1];\nconst audience = lines[1].split(': ')[1];\nconst authorization_uri = lines[2].split(': ')[1];\nconst token_uri = lines[3].split(': ')[1];\nconst details = lines[4].split(': ')[1];\nconst confidence = parseFloat(lines[5].split(': ')[1]);\n\n// Return the transformed output\nreturn [\n  {\n    json: {\n      output: {\n        service_name,\n        audience,\n        authorization_uri,\n        token_uri,\n        details,\n        confidence\n      }\n    }\n  }\n];"}, "typeVersion": 2}, {"id": "8d4d10a4-ac75-4fc7-a607-19d91cde6ac6", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [0, -200], "parameters": {"color": 4, "width": 1100, "height": 360, "content": "## OAuth2 Settings Finder with OpenRouter Chat Model and Llama 3.3\n\n**Overview:**\nThe AI agent identifies:\n- Authorization URI\n- Token URI\n- Audience\n\n**Methodology:**\nConfidence scoring is utilized to assess the trustworthiness of extracted data:\n- Score Range: 0 < x ≤ 1\n- Score Granularity: 0.01 increments\n\n**Model Details:**\nLeveraging the Wayfarer Large 70b Llama 3.3 model."}, "typeVersion": 1}, {"id": "e3abf498-e9ca-482e-9e75-4a4db0bbb813", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [0, 180], "parameters": {"width": 280, "height": 560, "content": "## Start\n**Trigger** input from the calling process."}, "typeVersion": 1}, {"id": "2359628b-7f58-4f04-ac94-7d33f6bf9b0e", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [300, 180], "parameters": {"color": 5, "height": 560, "content": "## AI Agent\n**Prompt** input to find data."}, "typeVersion": 1}, {"id": "e6977545-9559-45e5-a9ee-c131cc6f021b", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [560, 180], "parameters": {"color": 5, "height": 560, "content": "## Output\n**Parser** to grab the AI results into a JSON structure, according to the specified schema."}, "typeVersion": 1}, {"id": "9216c88f-2c79-458e-9583-9fc718a78ea2", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [820, 180], "parameters": {"color": 7, "width": 280, "height": 560, "content": "## Conform\n**Output** to your process expectation."}, "typeVersion": 1}, {"id": "18b261ec-e2c2-4ce8-a61d-72397ecb328d", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-1520, -200], "parameters": {"color": 7, "width": 1500, "height": 940, "content": "## Purpose\nThis template is designed to assist users in obtaining OAuth2 settings using AI-powered insights. It is ideal for developers, IT professionals, or anyone working with APIs that require OAuth2 authentication. By leveraging the AI agent, users can simplify the process of extracting and validating key details such as the `authorization_url`, `token_url`, and `audience`.\n\n## Value \nObtaining OAuth2 details via AI saves time and reduces the risk of human error. The confidence scoring system provides an indication of the trustworthiness of the results, empowering users to make informed decisions.\n## Setup Instructions\n### 1. Configuration Nodes\n- **Structured Output Node**: Parses the AI model's output using a predefined JSON schema. This ensures the data is structured for downstream processing.\n- **Code Node**:  If the AI model’s output does not match the required format, use the Code node to re-arrange and transform the data. Example code snippets are provided below for common scenarios.\n### 2. AI Model Prompt\nThe prompt for the AI model includes:\n- A detailed structure and objectives of the query.\n- Flexibility for the model to improvise when accurate results cannot be determined.\n### 3. Confidence Scoring\nThe AI model assigns a confidence score (0 < x ≤ 1) to indicate the reliability of the extracted data. Scores are provided in increments of 0.01 for granularity.\n\n## Configuration Example\nThis is an example of the Code node can be configured to reformat the data:\n```const items =$input.all();\nconst originalText = items[0].json.output.text;\nconst lines = originalText.split('\\n');\nconst service_name = lines[0].split(': ')[1];\nconst audience = lines[1].split(': ')[1];\nconst authorization_uri = lines[2].split(': ')[1];\nconst token_uri = lines[3].split(': ')[1];\nconst details = lines[4].split(': ')[1];\nconst confidence = parseFloat(lines[5].split(': ')[1]);\nreturn [\n  {\n    json: {\n      output: {\n        service_name,\n        audience,\n        authorization_uri,\n        token_uri,\n        details,\n        confidence\n      }\n    }\n  }\n];"}, "typeVersion": 1}, {"id": "05edd9e4-e2ad-4dc5-930e-44aa814c07b2", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-1520, 760], "parameters": {"color": 6, "width": 2620, "content": "## Adaptability\n**Customize** this template:\n* Update the AI model prompt with details specific to your API or OAuth2 setup.\n* Adjust the JSON schema in the Structured Output node to match the data format.\n* Modify the Code logic to suit the application's requirements. "}, "typeVersion": 1}], "active": false, "pinData": {"When Executed by Another Workflow": [{"json": {"name": "Atlassian"}}]}, "settings": {"executionOrder": "v1"}, "versionId": "c1321677-4ea5-4c8c-8742-02ffd4c8ef70", "connections": {"LLM Bus": {"main": [[{"node": "Conform JSON", "type": "main", "index": 0}]]}, "OpenRouter Chat Model": {"ai_languageModel": [[{"node": "LLM Bus", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "LLM Bus", "type": "ai_outputParser", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "LLM Bus", "type": "main", "index": 0}]]}}}