{"meta": {"instanceId": "cb484ba7b742928a2048bf8829668bed5b5ad9787579adea888f05980292a4a7"}, "nodes": [{"id": "8916c4a2-00a1-4c57-a661-0433a71ab316", "name": "Filter out common personal emails", "type": "n8n-nodes-base.filter", "notes": "Saves on Enrichment credits", "position": [1000, 360], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "or", "conditions": [{"id": "31a3f64f-cce1-44c3-938c-f18e85c670f3", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.body.user.email }}", "rightValue": "@gmail."}, {"id": "60087832-5f76-4dcc-bfe6-c899b3623af7", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.body.user.email }}", "rightValue": "@yahoo."}, {"id": "7fd74d31-ef3d-4a06-aee1-202ade476c10", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.body.user.email }}", "rightValue": "@hotmail."}, {"id": "4f86817c-53ec-4a80-a961-b4c9bd0c8f7c", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.body.user.email }}", "rightValue": "@proton."}]}}, "notesInFlow": true, "typeVersion": 2}, {"id": "bf4c1985-71ae-4bb2-86e0-8f795f89620e", "name": "No clearbit enrichment available", "type": "n8n-nodes-base.noOp", "position": [1520, 520], "parameters": {}, "typeVersion": 1}, {"id": "0ee7d23a-f6c7-4651-9f4c-b4b041326145", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1720, 540], "parameters": {"color": 7, "width": 314.8866754976157, "height": 119.43509109499996, "content": "**👈 Optional**\nIf the workflow ends here, the email wasn't found in Clearbit. Consider checking with another enrichment service or sending a Slack message for manual verification."}, "typeVersion": 1}, {"id": "93d1b345-2954-4bb2-8c13-510bb48f730a", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1660, 120], "parameters": {"color": 7, "width": 194, "height": 101, "content": "**Optional 👇**\nChange filter criteria here to determine what \"high value\" means for you "}, "typeVersion": 1}, {"id": "ad2823cf-861f-4302-8c8a-51700fbe9602", "name": "Enrich user with Clearbit", "type": "n8n-nodes-base.clearbit", "notes": " Clearbit returns a 404 error (and in n8n empty output payload) when email is not found", "onError": "continueErrorOutput", "position": [1220, 360], "parameters": {"email": "={{ $json.body.user.email }}", "resource": "person", "additionalFields": {}}, "credentials": {"clearbitApi": {"id": "fJAEKGUyTHSl0EpC", "name": "<EMAIL> (use carefully!)"}}, "notesInFlow": true, "typeVersion": 1}, {"id": "60c5727e-3e9e-48dd-8534-8b89ede90594", "name": "Get company info", "type": "n8n-nodes-base.clearbit", "position": [1500, 280], "parameters": {"domain": "={{ $json.employment.domain }}", "additionalFields": {}}, "credentials": {"clearbitApi": {"id": "fJAEKGUyTHSl0EpC", "name": "<EMAIL> (use carefully!)"}}, "typeVersion": 1}, {"id": "f5abe4a5-bbe9-4024-a448-c855d7bd8f54", "name": "Filter for high value leads", "type": "n8n-nodes-base.filter", "position": [1700, 280], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "90e62611-28d9-465a-b98e-bf866589363e", "operator": {"type": "number", "operation": "gte"}, "leftValue": "={{ $json.metrics.employees }}", "rightValue": 30}, {"id": "06c5fd69-1423-4442-bd8f-f61e5c119a39", "operator": {"type": "number", "operation": "lte"}, "leftValue": "={{ $json.metrics.alexaGlobalRank }}", "rightValue": 100000}]}}, "typeVersion": 2}, {"id": "fd6cab51-0146-401d-b2ef-75780b6e8bd7", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [600, 11.812931088613368], "parameters": {"color": 6, "width": 312.11707638995097, "height": 309.60234316641487, "content": "### Enrich new Discourse members then notify in Slack for high value leads\n### [🎥 Watch set up video (~2min)](https://www.loom.com/share/d379895004374ddc85dc9171ca37c139?sid=0996f0d2-aff2-45a7-aae9-c62df4fb0799)\n![Example result in Slack](https://i.ibb.co/s9MfsjV/Screenshot-2024-02-21-at-13-51-29.png#full-width)\n\n"}, "typeVersion": 1}, {"id": "5a2e98f1-c681-4a2e-b88c-473069a12b9a", "name": "On new Discourse user", "type": "n8n-nodes-base.webhook", "position": [640, 360], "webhookId": "06e900e8-9a4f-4786-bd79-928459c36e68", "parameters": {"path": "abde7a49-208b-4bce-bcb9-910c4e529b06", "options": {}, "httpMethod": "POST"}, "typeVersion": 1.1}, {"id": "baa0ed59-75a2-4cdb-a540-073bf505bc43", "name": "Post message in Channel", "type": "n8n-nodes-base.slack", "position": [1900, 280], "parameters": {"text": "Test message!", "select": "channel", "blocksUi": "={\n\t\"blocks\": [\n\t\t{\n\t\t\t\"type\": \"section\",\n\t\t\t\"text\": {\n\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\"text\": \"A high value lead just signed up on our Discourse community 👇\"\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\t\"type\": \"context\",\n\t\t\t\"elements\": [\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"image\",\n\t\t\t\t\t\"image_url\": \"{{ $('Enrich user with Clearbit').item.json[\"avatar\"] }}\",\n\t\t\t\t\t\"alt_text\": \"User's profile avatar\"\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\t\"text\": \"*{{ $('Enrich user with Clearbit').item.json[\"name\"][\"fullName\"] }}*, *{{ $('Enrich user with Clearbit').item.json[\"employment\"][\"title\"] }}* at *{{ $('Enrich user with Clearbit').item.json[\"employment\"][\"name\"] }}* ({{ $json[\"category\"][\"industry\"] }})\"\n\t\t\t\t}\n\t\t\t]\n\t\t},\n\t\t{\n\t\t\t\"type\": \"divider\"\n\t\t},\n\t\t{\n\t\t\t\"type\": \"actions\",\n\t\t\t\"block_id\": \"actionblock789\",\n\t\t\t\"elements\": [\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"button\",\n\t\t\t\t\t\"text\": {\n\t\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\t\"text\": \"Open LinkedIn Profile\"\n\t\t\t\t\t},\n\t\t\t\t\t\"style\": \"primary\",\n\t\t\t\t\t\"url\": \"https://www.linkedin.com/{{ $('Enrich user with Clearbit').item.json[\"linkedin\"][\"handle\"]}}\"\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"button\",\n\t\t\t\t\t\"text\": {\n\t\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\t\"text\": \"Email {{ $('Enrich user with Clearbit').item.json[\"name\"][\"givenName\"] }} \"\n\t\t\t\t\t},\n\t\t\t\t\t\"url\": \"mailto:{{ $('On new Discourse user').item.json[\"body\"][\"user\"][\"email\"] }}\"\n\t\t\t\t}\n\t\t\t]\n\t\t}\n\t]\n}", "channelId": {"__rl": true, "mode": "name", "value": "#team-design"}, "messageType": "block", "otherOptions": {}}, "credentials": {"slackApi": {"id": "114", "name": "n8n Slack"}}, "typeVersion": 2.1}, {"id": "3f9687f4-0bb4-4e46-9beb-e37eb632bb95", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [600, 520], "parameters": {"color": 7, "width": 330.80402248919853, "height": 159.6208789325232, "content": "**1. ☝️ Set up `On new Discourse user` <PERSON><PERSON> and Webhook in Discourse**\n\nOpen `https://{Your discourse root domain}/admin/api/web_hooks/new/edit` to create a new Webhook in Discourse. See detailed instructions in [🎥 set up video](https://www.loom.com/share/d379895004374ddc85dc9171ca37c139?t=32&sid=da64c668-f7f5-4d49-982e-d1e72fb77fcc)\n"}, "typeVersion": 1}, {"id": "3223fcb7-dddb-4566-a6c3-340bd1a8b3e3", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1900, 120], "parameters": {"color": 7, "width": 249.15806405688022, "height": 124.16286220264169, "content": "**2. 👇 Set up `Post message in Channel`  node**\nChange `Channel` parameter to your channel like `#sales` or `#townsquare`\n"}, "typeVersion": 1}], "pinData": {}, "connections": {"Get company info": {"main": [[{"node": "Filter for high value leads", "type": "main", "index": 0}]]}, "On new Discourse user": {"main": [[{"node": "Filter out common personal emails", "type": "main", "index": 0}]]}, "Enrich user with Clearbit": {"main": [[{"node": "Get company info", "type": "main", "index": 0}], [{"node": "No clearbit enrichment available", "type": "main", "index": 0}]]}, "Filter for high value leads": {"main": [[{"node": "Post message in Channel", "type": "main", "index": 0}]]}, "Filter out common personal emails": {"main": [[{"node": "Enrich user with Clearbit", "type": "main", "index": 0}]]}}}