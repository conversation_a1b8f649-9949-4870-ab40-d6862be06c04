{"nodes": [{"name": "Autopilot", "type": "n8n-nodes-base.autopilot", "position": [470, 320], "parameters": {"name": "n8n-docs", "resource": "list"}, "credentials": {"autopilotApi": "Autopilot API Credentials"}, "typeVersion": 1}, {"name": "Autopilot1", "type": "n8n-nodes-base.autopilot", "position": [670, 320], "parameters": {"email": "", "additionalFields": {"autopilotList": "={{$json[\"list_id\"]}}"}}, "credentials": {"autopilotApi": "Autopilot API Credentials"}, "typeVersion": 1}, {"name": "Autopilot2", "type": "n8n-nodes-base.autopilot", "position": [870, 320], "parameters": {"email": "={{$node[\"Autopilot1\"].parameter[\"email\"]}}", "additionalFields": {"Company": "n8n"}}, "credentials": {"autopilotApi": "Autopilot API Credentials"}, "typeVersion": 1}, {"name": "Autopilot3", "type": "n8n-nodes-base.autopilot", "position": [1070, 320], "parameters": {"listId": "={{$node[\"Autopilot\"].json[\"list_id\"]}}", "resource": "contactList", "operation": "getAll", "returnAll": true}, "credentials": {"autopilotApi": "Autopilot API Credentials"}, "typeVersion": 1}], "connections": {"Autopilot": {"main": [[{"node": "Autopilot1", "type": "main", "index": 0}]]}, "Autopilot1": {"main": [[{"node": "Autopilot2", "type": "main", "index": 0}]]}, "Autopilot2": {"main": [[{"node": "Autopilot3", "type": "main", "index": 0}]]}}}