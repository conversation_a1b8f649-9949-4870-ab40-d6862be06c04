{"id": "ODZpSQqCxkISEqv8", "meta": {"instanceId": "a4bfc93e975ca233ac45ed7c9227d84cf5a2329310525917adaf3312e10d5462", "templateCredsSetupCompleted": true}, "name": "WooCommerce AI Chatbot Workflow for Post-Sales Support", "tags": [{"id": "NFwUyKypVupFwAM4", "name": "WooCommerce", "createdAt": "2024-12-13T15:34:20.174Z", "updatedAt": "2024-12-13T15:34:20.174Z"}, {"id": "paTcf5QZDJsC2vKY", "name": "OpenAI", "createdAt": "2024-12-04T16:52:10.768Z", "updatedAt": "2024-12-04T16:52:10.768Z"}], "nodes": [{"id": "25024dde-431b-4dad-b5ed-9be77cdf3db2", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-860, 420], "webhookId": "bb9b597a-25ed-43de-9ebb-14f166af789b", "parameters": {"mode": "webhook", "public": true, "options": {"responseMode": "responseNode"}}, "typeVersion": 1.1}, {"id": "96139b63-75aa-4d16-91b1-2fe0ca624137", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [-520, 720], "parameters": {}, "typeVersion": 1.3}, {"id": "f7901f97-50c5-4f56-bae3-21c575aa22cc", "name": "get_order", "type": "n8n-nodes-base.wooCommerceTool", "position": [80, 600], "parameters": {"orderId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Order_ID', `Order Details Retrieval Process:\n1. Request Order Number\n   - Explicitly ask customer for the complete order number\n\n2. Identity Verification\n   - Ask for the email address associated with the order\n   - Strictly verify that the provided email matches the order record\n   - If email does NOT match the order:\n     * Immediately halt the process\n     * Inform customer that the email is incorrect\n     * Do NOT provide the correct email\n     * Prevent access to order details\n\n3. Verification Criteria\n   - Exact match of email to order record is mandatory\n   - No exceptions or workarounds\n   - Customer must provide the precise, correct email`, 'string') }}", "resource": "order", "operation": "get"}, "credentials": {"wooCommerceApi": {"id": "nCgFKMfBO95v38Mp", "name": "WooCommerce (magnanigioielli.com)"}}, "typeVersion": 1}, {"id": "b448fb28-1bba-4743-923f-4198bdea0165", "name": "get_orders", "type": "n8n-nodes-base.wooCommerceTool", "position": [180, 600], "parameters": {"options": {"search": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Search', ``, 'string') }}"}, "resource": "order", "operation": "getAll", "returnAll": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Return_All', ``, 'boolean') }}"}, "credentials": {"wooCommerceApi": {"id": "nCgFKMfBO95v38Mp", "name": "WooCommerce (magnanigioielli.com)"}}, "typeVersion": 1}, {"id": "4b386cd5-1f42-45b9-bfa6-e8caf50d49b3", "name": "get_user", "type": "n8n-nodes-base.wooCommerceTool", "position": [280, 600], "parameters": {"filters": {"email": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Email', ``, 'string') }}"}, "resource": "customer", "operation": "getAll", "returnAll": true}, "credentials": {"wooCommerceApi": {"id": "nCgFKMfBO95v38Mp", "name": "WooCommerce (magnanigioielli.com)"}}, "typeVersion": 1}, {"id": "2235e0c5-ad5e-42d3-a6da-19513dcba181", "name": "Calculator", "type": "@n8n/n8n-nodes-langchain.toolCalculator", "position": [-400, 720], "parameters": {}, "typeVersion": 1}, {"id": "e3b8fd50-2f47-4866-acc7-88e819b9d2ce", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-980, -500], "parameters": {}, "typeVersion": 1}, {"id": "7ac0573f-fcc0-426e-b412-1f4a1e38b9ee", "name": "Create collection", "type": "n8n-nodes-base.httpRequest", "position": [-680, -640], "parameters": {"url": "https://QDRANTURL/collections/COLLECTION", "method": "POST", "options": {}, "jsonBody": "{\n  \"filter\": {}\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "credentials": {"httpHeaderAuth": {"id": "qhny6r5ql9wwotpn", "name": "Qdrant API (Hetzner)"}}, "typeVersion": 4.2}, {"id": "15de11a6-27eb-4431-9105-a7f07f103d44", "name": "Refresh collection", "type": "n8n-nodes-base.httpRequest", "position": [-680, -380], "parameters": {"url": "https://QDRANTURL/collections/COLLECTION/points/delete", "method": "POST", "options": {}, "jsonBody": "{\n  \"filter\": {}\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "credentials": {"httpHeaderAuth": {"id": "qhny6r5ql9wwotpn", "name": "Qdrant API (Hetzner)"}}, "typeVersion": 4.2}, {"id": "aab947f3-3e7f-4068-8183-a6003754dc16", "name": "Get folder", "type": "n8n-nodes-base.googleDrive", "position": [-460, -380], "parameters": {"filter": {"driveId": {"__rl": true, "mode": "list", "value": "My Drive", "cachedResultUrl": "https://drive.google.com/drive/my-drive", "cachedResultName": "My Drive"}, "folderId": {"__rl": true, "mode": "id", "value": "=test-whatsapp"}}, "options": {}, "resource": "fileFolder"}, "credentials": {"googleDriveOAuth2Api": {"id": "HEy5EuZkgPZVEa9w", "name": "Google Drive account (n3w.it)"}}, "typeVersion": 3}, {"id": "091b9080-eae0-433a-80e6-d1c553b4912b", "name": "Download Files", "type": "n8n-nodes-base.googleDrive", "position": [-240, -380], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "options": {"googleFileConversion": {"conversion": {"docsToFormat": "text/plain"}}}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "HEy5EuZkgPZVEa9w", "name": "Google Drive account (n3w.it)"}}, "typeVersion": 3}, {"id": "400fcdde-ef42-49bf-807d-e2ed029aae1f", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [160, -180], "parameters": {"options": {}, "dataType": "binary"}, "typeVersion": 1}, {"id": "b3f39ac8-ebbc-42c5-8368-2fedd8d133f3", "name": "Token Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterTokenSplitter", "position": [140, 0], "parameters": {"chunkSize": 300, "chunkOverlap": 30}, "typeVersion": 1}, {"id": "a17ed192-cd39-456d-b35a-ee7d8532fb7c", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-480, -700], "parameters": {"color": 6, "width": 880, "height": 220, "content": "# STEP 1\n\n## Create Qdrant Collection\nChange:\n- QDRANTURL\n- COLLECTION"}, "typeVersion": 1}, {"id": "ce12df1d-357a-4f8f-91cf-f92c0aa6bcd7", "name": "Qdrant Vector Store1", "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "position": [0, -380], "parameters": {"mode": "insert", "options": {}, "qdrantCollection": {"__rl": true, "mode": "id", "value": "=COLLECTION"}}, "credentials": {"qdrantApi": {"id": "iyQ6MQiVaF3VMBmt", "name": "QdrantApi account"}}, "typeVersion": 1}, {"id": "e440121d-6aa8-4cc7-920c-d28d93717d99", "name": "Embeddings OpenAI1", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [0, -160], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "CDX6QM4gLYanh0P4", "name": "OpenAi account"}}, "typeVersion": 1.1}, {"id": "c6f89301-3ae0-4068-8279-de149859f6a5", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-700, -440], "parameters": {"color": 4, "width": 620, "height": 400, "content": "# STEP 2\n\n\n\n\n\n\n\n\n\n\n\n\n## Documents vectorization with Qdrant and Google Drive\nChange:\n- QDRANTURL\n- COLLECTION"}, "typeVersion": 1}, {"id": "7ae502f5-bf12-49bd-afd0-d8ad050c1d8b", "name": "Qdrant Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "position": [-140, 960], "parameters": {"options": {}, "qdrantCollection": {"__rl": true, "mode": "id", "value": "=COLLECTION"}}, "credentials": {"qdrantApi": {"id": "iyQ6MQiVaF3VMBmt", "name": "QdrantApi account"}}, "typeVersion": 1}, {"id": "b3bc247f-7fcb-4c17-8995-ec4ba26b65ee", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [-160, 1140], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "CDX6QM4gLYanh0P4", "name": "OpenAi account"}}, "typeVersion": 1.1}, {"id": "7c110a8b-5e66-4824-ac9f-a42fabbe3929", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [160, 960], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "CDX6QM4gLYanh0P4", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "b9a62833-a124-439b-b6ca-eb6061afa74d", "name": "ToS", "type": "@n8n/n8n-nodes-langchain.toolVectorStore", "position": [-40, 780], "parameters": {"name": "company", "description": "Rispondi alle domande relative ai termini e condizioni e spedizioni"}, "typeVersion": 1}, {"id": "667e8756-78a6-4cd1-bf24-00af8188fe50", "name": "get_tracking", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [-240, 600], "parameters": {"name": "get_tracking", "workflowId": {"__rl": true, "mode": "list", "value": "AcPE4PXeFOiOW48H", "cachedResultName": "WooCommerce Get Tracking Number"}, "description": "Get tracking number for a specific order by providing the order number. The tool retrieves the unique tracking code that allows customers to monitor their shipment's current status and location.", "workflowInputs": {"value": {"order_number": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('order_number', ``, 'string') }}"}, "schema": [{"id": "order_number", "type": "string", "display": true, "removed": false, "required": false, "displayName": "order_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["order_number"], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2.1}, {"id": "305ea876-80b9-45c4-b548-6d1df093bd94", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [560, 480], "parameters": {"inputSource": "jsonExample", "jsonExample": "{\n  \"order_number\": \"order number\"\n}"}, "typeVersion": 1.1}, {"id": "4f7547c1-1d21-45f6-a409-09e55c4b9477", "name": "Post-Sales Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-540, 420], "parameters": {"options": {"systemMessage": "=## Role and Primary Objective\nYou are a customer support AI agent for an online clothing store, specializing in post-sales assistance. Your primary goals are to:\n- Help customers track their orders\n- Provide information about past and current orders\n- Offer clear and concise support\n\n## Communication Guidelines\n1. Always be professional, helpful, and precise\n2. Use available tools to retrieve accurate information\n3. Verify customer identity before sharing order details\n4. Protect customer privacy and data confidentiality\n\n## Tool Usage Instructions\n\n### Order Information Retrieval\n- Use `get_order` to fetch details for a single order\n  - REQUIRED: Complete user information including request, email, and order number\n- Use `get_orders` to retrieve multiple orders for a single customer\n- Use `get_user` to obtain customer profile information\n\n### Tracking and Support\n- Use `get_tracking` to obtain tracking information for an order using the order number\n- If a customer cannot find what they need, use `human*assistance`\n  - Synthesize the request clearly\n  - Include associated user email\n  - Provide concise, relevant details\n\n### General Information\n- Use `ToS` tool to answer questions about:\n  - Terms and Conditions\n  - Shipping information\n  - General store policies\n\n## Critical Verification Rules\n- ALWAYS verify that the email provided matches the order number\n- If the email does not match the order:\n  - DO NOT provide the correct email\n  - Inform the customer that the email address is incorrect\n  - Request the correct email associated with the order\n\n## Prohibited Actions\n- Never disclose sensitive customer information\n- Do not share full order details without proper verification\n- Avoid providing speculative or unconfirmed information\n\n## Communication Style\n- Be direct and helpful\n- Use clear, professional language\n- Focus on solving the customer's specific query\n- Provide step-by-step guidance when necessary\n\n## Escalation Protocol\n- If unable to resolve the customer's issue using available tools\n- Use `human_assistance` to escalate complex or unresolvable queries\n- Ensure clear, concise problem description\n\nToday is {{$now}}"}}, "typeVersion": 1.8}, {"id": "a5b49dbf-f041-43ec-b621-a89f65675705", "name": "human_assistence", "type": "n8n-nodes-base.telegramTool", "position": [-80, 600], "webhookId": "1f9cad22-45e3-4c6b-bec8-f421060d14d9", "parameters": {"text": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Text', ``, 'string') }}", "chatId": "CHAT_ID", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "rQ5q95W7uKesMDx4", "name": "Telegram account Fastewb"}}, "typeVersion": 1.2}, {"id": "46110bcd-dae7-4c33-8936-cc4867c9abde", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-880, 240], "parameters": {"width": 1220, "height": 140, "content": "# STEP 3\n\n- Add your Telegram CHAT_ID in the \"human_assistance\" tool"}, "typeVersion": 1}, {"id": "9c45d20d-f4d3-4bde-9e3e-749c64a123bb", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [540, 240], "parameters": {"color": 5, "width": 580, "height": 200, "content": "# STEP 4\n\nThe tracking code request is made through the most popular WooCommerce tracking plugin: \"YITH WooCommerce Order & Shipment Tracking\". The free version can be [downloaded here](https://wordpress.org/plugins/yith-woocommerce-order-tracking/)\n- Create a new workflow and change URL in the node \"Http Request\" with your WooCommerce shop url"}, "typeVersion": 1}, {"id": "f16ee312-8434-4958-b111-d80384ae869c", "name": "Get tracking", "type": "n8n-nodes-base.httpRequest", "position": [780, 480], "parameters": {"url": "=https://URL/wp-json/wc/v3/orders/{{ $json.order_number }}", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth"}, "credentials": {"httpBasicAuth": {"id": "nIitKwSJa9EpgG8K", "name": "WP (magnanigioielli.com)"}}, "typeVersion": 4.2}, {"id": "e5de4ad6-3469-4720-828e-618db5c4f3ae", "name": "Set tracking code", "type": "n8n-nodes-base.set", "position": [1020, 480], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "19b33abc-5191-4449-b682-4466f1975ff2", "name": "tracking_code", "type": "string", "value": "={{ $json[\"meta_data\"].find(item => item.key === \"ywot_tracking_code\").value }}"}, {"id": "2e18b337-e3e8-4669-a902-ecc2ba027a1a", "name": "carrier_url", "type": "string", "value": "={{ $json[\"meta_data\"].find(item => item.key === \"ywot_carrier_url\").value }}"}, {"id": "ae834f65-67b2-4e95-9a49-5172e36fc5b9", "name": "pick_up", "type": "string", "value": "={{ $json[\"meta_data\"].find(item => item.key === \"ywot_pick_up_date\").value }}"}]}}, "typeVersion": 3.4}, {"id": "b889e33b-5243-4b5b-80ef-023df779e673", "name": "GPT 4o-mini", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-700, 720], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "CDX6QM4gLYanh0P4", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "c9863b08-f6aa-4502-85a4-7782a090d532", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-1000, -1040], "parameters": {"color": 3, "width": 1400, "height": 240, "content": "# WooCommerce AI Chatbot Workflow for Post-Sales Support\n\nThis WooCommerce-integrated chatbot is designed to transform post-sales customer support by combining automation and artificial intelligence to deliver fast, secure, and personalized assistance. By connecting directly to the WooCommerce database, the chatbot retrieves real-time order information—including shipping details and tracking numbers—after verifying the customer's identity through a strict email-based authentication system. This ensures maximum data security, preventing leaks of sensitive information.  \n\nBeyond order management, the chatbot answers frequently asked questions about return policies, delivery times, and terms of service using a vector database that provides accurate, context-aware responses. If a request is too complex, the system seamlessly escalates it to a human operator via Telegram, guaranteeing no customer query goes unresolved. \n"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "bace4bf0-d6b4-46f8-81d1-26dbc1120d85", "connections": {"ToS": {"ai_tool": [[{"node": "Post-Sales Agent", "type": "ai_tool", "index": 0}]]}, "get_user": {"ai_tool": [[{"node": "Post-Sales Agent", "type": "ai_tool", "index": 0}]]}, "get_order": {"ai_tool": [[{"node": "Post-Sales Agent", "type": "ai_tool", "index": 0}]]}, "Calculator": {"ai_tool": [[{"node": "Post-Sales Agent", "type": "ai_tool", "index": 0}]]}, "Get folder": {"main": [[{"node": "Download Files", "type": "main", "index": 0}]]}, "get_orders": {"ai_tool": [[{"node": "Post-Sales Agent", "type": "ai_tool", "index": 0}]]}, "GPT 4o-mini": {"ai_languageModel": [[{"node": "Post-Sales Agent", "type": "ai_languageModel", "index": 0}]]}, "Get tracking": {"main": [[{"node": "Set tracking code", "type": "main", "index": 0}]]}, "get_tracking": {"ai_tool": [[{"node": "Post-Sales Agent", "type": "ai_tool", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "Post-Sales Agent", "type": "ai_memory", "index": 0}]]}, "Download Files": {"main": [[{"node": "Qdrant Vector Store1", "type": "main", "index": 0}]]}, "Token Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Post-Sales Agent": {"main": [[]]}, "human_assistence": {"ai_tool": [[{"node": "Post-Sales Agent", "type": "ai_tool", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Qdrant Vector Store", "type": "ai_embedding", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "ToS", "type": "ai_languageModel", "index": 0}]]}, "Embeddings OpenAI1": {"ai_embedding": [[{"node": "Qdrant Vector Store1", "type": "ai_embedding", "index": 0}]]}, "Refresh collection": {"main": [[{"node": "Get folder", "type": "main", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Qdrant Vector Store1", "type": "ai_document", "index": 0}]]}, "Qdrant Vector Store": {"ai_vectorStore": [[{"node": "ToS", "type": "ai_vectorStore", "index": 0}]]}, "When chat message received": {"main": [[{"node": "Post-Sales Agent", "type": "main", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Get tracking", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Create collection", "type": "main", "index": 0}, {"node": "Refresh collection", "type": "main", "index": 0}]]}}}