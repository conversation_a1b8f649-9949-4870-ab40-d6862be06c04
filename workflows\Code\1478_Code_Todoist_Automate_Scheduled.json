{"id": "Glb4VNoQI44GT0p9", "meta": {"instanceId": "a1f3364de0f3da48758a2641efb07c3b0d216a3a7cc93596fbed2316d6dea4ad", "templateCredsSetupCompleted": true}, "name": "My workflow 4", "tags": [], "nodes": [{"id": "909a08a4-4cec-4987-9379-d4cdc2d92a53", "name": "RSS Feed: Times of India", "type": "n8n-nodes-base.rssFeedRead", "position": [680, 240], "parameters": {"url": "https://timesofindia.indiatimes.com/rssfeeds/-**********.cms", "options": {}}, "typeVersion": 1.1}, {"id": "471cc8ab-0074-4e25-b952-1899574398a9", "name": "Gmail: <PERSON><PERSON> Emails", "type": "n8n-nodes-base.gmail", "position": [700, 440], "webhookId": "********-07e5-418b-b029-44bb9825ac9b", "parameters": {"filters": {}, "operation": "getAll"}, "credentials": {"gmailOAuth2": {"id": "WbGCG42FAaeECe0u", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "07a33739-0181-4ead-87bd-c1f0c3fc4999", "name": "TodoList: Fetch Tasks", "type": "n8n-nodes-base.todoist", "position": [700, 620], "parameters": {"limit": 5, "filters": {}, "operation": "getAll"}, "credentials": {"todoistApi": {"id": "q3NiAT93rPChns6G", "name": "Todoist account"}}, "typeVersion": 2.1}, {"id": "af295aad-f7e7-4d38-80e5-b79b79637b5f", "name": "Format Digest: Merge & Style Data", "type": "n8n-nodes-base.code", "position": [1280, 440], "parameters": {"jsCode": "const newsItems = $input.all().map(item => item.json);\nconst emails = $(\"Gmail: Fetch Emails\").all().map(item => item.json);\nconst tasks = $(\"TodoList: Fetch Tasks\").all().map(item => item.json);\n\n// Select top 5 items from each\nconst topNews = newsItems.slice(0, 5).map(item => ({\n  title: item.title,\n  link: item.link\n}));\n\nconst latestEmails = emails.slice(0, 5).map(item => ({\n  subject: item.Subject,\n  snippet: item.snippet\n}));\n\nconst topTasks = tasks.slice(0, 5).map(task => ({\n  content: task.content,\n  url: task.url,\n  emoji: task.emoji || '🔴',\n  due: task.due\n}));\n\n// Create the final JSON object with email subject and a formatted email body with inline CSS\nconst result = {\n  meta: {\n    generated_at: new Date().toISOString(),\n    time_emoji: \"🌞\"\n  },\n  email: {\n    subject: `🌞 Daily Digest • 📋 ${topTasks.length} Tasks ⚠️ • 📰 ${topNews.length} News Updates`,\n    body: `\n      <div style=\"max-width:600px; margin:0 auto; font-family:'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; color:#333; background:#f4f7f9; padding:20px; border:1px solid #e1e8ed; border-radius:8px;\">\n         <div style=\"text-align:center; padding-bottom:20px;\">\n             <h1 style=\"margin:0; font-size:28px; color:#0073e6;\">Daily Digest</h1>\n             <p style=\"margin:10px 0 0; font-size:16px; color:#666;\">Your automated daily summary</p>\n         </div>\n         <hr style=\"border:none; border-top:1px solid #ddd; margin:20px 0;\">\n         <div style=\"margin-bottom:20px;\">\n             <h2 style=\"font-size:20px; color:#0073e6; margin-bottom:10px;\">Tasks (${topTasks.length})</h2>\n             <ul style=\"list-style:none; padding:0;\">\n               ${topTasks.map(task => `\n                 <li style=\"margin-bottom:10px; padding:10px; background:#fff; border:1px solid #e1e8ed; border-radius:4px;\">\n                   <span style=\"font-size:18px; margin-right:10px;\">${task.emoji}</span> \n                   <span style=\"font-size:16px;\">${task.content}</span> \n                   <span style=\"color:#999; font-size:14px; margin-left:5px;\">(Due: ${task.due})</span>\n                   <a href=\"${task.url}\" style=\"text-decoration:none; color:#0073e6; float:right;\">View Task</a>\n                 </li>\n               `).join('')}\n             </ul>\n         </div>\n         <div style=\"margin-bottom:20px;\">\n             <h2 style=\"font-size:20px; color:#0073e6; margin-bottom:10px;\">News (${topNews.length})</h2>\n             <ul style=\"list-style:none; padding:0;\">\n               ${topNews.map(news => `\n                 <li style=\"margin-bottom:10px; padding:10px; background:#fff; border:1px solid #e1e8ed; border-radius:4px;\">\n                   <a href=\"${news.link}\" style=\"text-decoration:none; font-size:16px; color:#0073e6;\">${news.title}</a>\n                 </li>\n               `).join('')}\n             </ul>\n         </div>\n         <div style=\"margin-bottom:20px;\">\n             <h2 style=\"font-size:20px; color:#0073e6; margin-bottom:10px;\">Emails (${latestEmails.length})</h2>\n             <ul style=\"list-style:none; padding:0;\">\n               ${latestEmails.map(email => `\n                 <li style=\"margin-bottom:10px; padding:10px; background:#fff; border:1px solid #e1e8ed; border-radius:4px;\">\n                   <strong style=\"font-size:16px; color:#0073e6;\">${email.subject}</strong>\n                   <p style=\"margin:5px 0 0; font-size:14px; color:#666;\">${email.snippet}</p>\n                 </li>\n               `).join('')}\n             </ul>\n         </div>\n         <div style=\"text-align:center; font-size:12px; color:#aaa; margin-top:20px;\">\n             <p>Digest generated at: ${new Date().toLocaleString()}</p>\n         </div>\n      </div>\n    `\n  },\n  tasks: topTasks,\n  news: topNews,\n  emails: latestEmails\n};\n\nreturn [{ json: result }];\n"}, "typeVersion": 2}, {"id": "5399bee1-d0e7-4ed7-af7f-d0ddccb00b4d", "name": "Gmail: Send Digest", "type": "n8n-nodes-base.gmail", "position": [1540, 440], "webhookId": "3cd541af-51d4-465e-803d-a74572a15d83", "parameters": {"sendTo": "<EMAIL>", "message": "={{ $json.email.body }}", "options": {}, "subject": "={{ $json.email.subject }}"}, "credentials": {"gmailOAuth2": {"id": "WbGCG42FAaeECe0u", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "9f398bc2-e84c-4df4-8958-aaa1d7c2ed37", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [0, 60], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.2}, {"id": "9984d3c0-7469-4b79-8d31-1a06b8dd23b6", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [1020, 440], "parameters": {"numberInputs": 3}, "typeVersion": 3}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "550f65e6-68ec-449a-9fb5-241acba42455", "connections": {"Merge": {"main": [[{"node": "Format Digest: Merge & Style Data", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "RSS Feed: Times of India", "type": "main", "index": 0}, {"node": "Gmail: <PERSON><PERSON> Emails", "type": "main", "index": 0}, {"node": "TodoList: Fetch Tasks", "type": "main", "index": 0}]]}, "Gmail: Fetch Emails": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "TodoList: Fetch Tasks": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 2}]]}, "RSS Feed: Times of India": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Format Digest: Merge & Style Data": {"main": [[{"node": "Gmail: Send Digest", "type": "main", "index": 0}]]}}}