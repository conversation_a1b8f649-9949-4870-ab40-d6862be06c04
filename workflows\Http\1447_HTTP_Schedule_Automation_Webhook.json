{"id": "F2AEknC2Kc3ujuX4", "meta": {"instanceId": "8437bf0b955ff2039c820e1d56f4a2d7ce67e59f0897cc8ac064cfea1d9dbec6"}, "name": "URL <PERSON>", "tags": [], "nodes": [{"id": "5b3b5251-d460-4eae-a931-e4772749a927", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [900, 460], "parameters": {"options": {"destinationFieldName": "url"}, "fieldToSplitOut": "urls"}, "typeVersion": 1}, {"id": "b19bec9b-de09-42a7-8576-2cef3e0f9288", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [460, 460], "parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 15}]}}, "typeVersion": 1.2}, {"id": "584a4340-7053-4afd-ae3e-f0c1f2de2586", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "onError": "continueRegularOutput", "position": [1100, 460], "parameters": {"url": "={{ $json.url }}", "options": {}}, "typeVersion": 4.2}, {"id": "d53b8c24-7408-4e09-8360-f13ecfa5deca", "name": "URLs List", "type": "n8n-nodes-base.set", "position": [680, 460], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9e5e8792-c5ee-4ce2-9a9a-0b3ad274cae6", "name": "urls", "type": "array", "value": "={{ ['http://firsturl.com', 'https://secondurl.com', 'https://thirdurl.com'] }}"}]}}, "typeVersion": 3.3}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "71356023-fe84-4b30-9df8-3c5dc25fbcca", "connections": {"Split Out": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "URLs List": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "URLs List", "type": "main", "index": 0}]]}}}