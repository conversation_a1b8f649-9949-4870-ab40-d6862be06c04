{"meta": {"instanceId": "6a2a7715680b8313f7cb4676321c5baa46680adfb913072f089f2766f42e43bd"}, "nodes": [{"id": "0f3b39af-2802-462c-ac54-a7bccf5b78c5", "name": "Extract Document PDF", "type": "n8n-nodes-base.extractFromFile", "position": [520, 400], "parameters": {"options": {}, "operation": "pdf"}, "typeVersion": 1, "alwaysOutputData": false}, {"id": "6f76e3a6-a3be-4f9f-a0db-3f002eafc2ad", "name": "Download File", "type": "n8n-nodes-base.httpRequest", "position": [340, 400], "parameters": {"url": "={{ $json.file_url }}", "options": {}}, "typeVersion": 4.2}, {"id": "2c4e0b0f-28c7-48f5-b051-6e909ac878d2", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-20, 400], "parameters": {}, "typeVersion": 1}, {"id": "a70d972b-ceb4-4f4d-8737-f0be624d6234", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [120, 280], "parameters": {"width": 187.37066290133808, "height": 80, "content": "**Add direct link to CV and Job description**"}, "typeVersion": 1}, {"id": "9fdff1be-14cf-4167-af2d-7c5e60943831", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-800, 140], "parameters": {"color": 7, "width": 280.2462120317618, "height": 438.5821431288714, "content": "### Setup\n\n1. **Download File**: Fetch the CV using its direct URL.\n2. **Extract Data**: Use N8N’s PDF or text extraction nodes to retrieve text from the CV.\n3. **Send to OpenAI**:\n   - **URL**: POST to OpenAI’s API for analysis.\n   - **Parameters**:\n     - Include the extracted CV data and job description.\n     - Use JSON Schema to structure the response.\n4. **Save Results**:\n   - Store the extracted data and OpenAI's analysis in Supabase for further use."}, "typeVersion": 1}, {"id": "b1ce4a61-270f-480b-a716-6618e6034581", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-800, -500], "parameters": {"color": 7, "width": 636.2128494576581, "height": 598.6675280064023, "content": "![5min Logo](https://cflobdhpqwnoisuctsoc.supabase.co/storage/v1/object/public/my_storage/Untitled%20(1500%20x%20300%20px).png)\n## CV Screening with OpenAI\n**Made by [<PERSON>](https://www.linkedin.com/in/marklowcoding/) from community [5minAI](https://www.skool.com/5minai-2861)**\n\nThis workflow is ideal for recruitment agencies, HR professionals, and hiring managers looking to automate the initial screening of CVs. It is especially useful for organizations handling large volumes of applications and seeking to streamline their recruitment process.\n\nThis workflow automates the resume screening process using OpenAI for analysis and Supabase for structured data storage. It provides a matching score, a summary of candidate suitability, and key insights into why the candidate fits (or doesn’t fit) the job. \n\n1. **Retrieve Resume**: The workflow downloads CVs from a direct link (e.g., Supabase storage or Dropbox).\n2. **Extract Data**: Extracts text data from PDF or DOC files for analysis.\n3. **Analyze with OpenAI**: Sends the extracted data and job description to OpenAI to:\n   - Generate a matching score.\n   - Summarize candidate strengths and weaknesses.\n   - Provide actionable insights into their suitability for the job.\n4. **Store Results in Supabase**: Saves the analysis and raw data in a structured format for further processing or integration into other tools.\n"}, "typeVersion": 1}, {"id": "747591cd-76b1-417e-ab9d-0a3935d3db03", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-500, 140], "parameters": {"color": 7, "width": 330.5152611046425, "height": 240.6839895136402, "content": "### ... or watch set up video [8 min]\n[![Youtube Thumbnail](https://cflobdhpqwnoisuctsoc.supabase.co/storage/v1/object/public/my_storage/11.png)](https://youtu.be/TWuI3dOcn0E)\n"}, "typeVersion": 1}, {"id": "051d8cb0-2557-4e35-9045-c769ec5a34f9", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [660, 280], "parameters": {"width": 187.37066290133808, "height": 80, "content": "**Replace OpenAI connection**"}, "typeVersion": 1}, {"id": "865f4f69-e13d-49c1-8bb4-9f98facbf75c", "name": "OpenAI - Analyze CV", "type": "n8n-nodes-base.httpRequest", "position": [700, 400], "parameters": {"url": "=https://api.openai.com/v1/chat/completions", "method": "POST", "options": {}, "jsonBody": "={\n    \"model\": \"gpt-4o-mini\",\n    \"messages\": [\n      {\n        \"role\": \"system\",\n        \"content\": \"{{ $('Set Variables').item.json.prompt }}\"\n      },\n      {\n        \"role\": \"user\",\n        \"content\": {{ JSON.stringify(encodeURIComponent($json.text))}}\n      }\n    ],\n  \"response_format\":{ \"type\": \"json_schema\", \"json_schema\":  {{ $('Set Variables').item.json.json_schema }}\n\n }\n  }", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "SphXAX7rlwRLkiox", "name": "Test club key"}}, "typeVersion": 4.2}, {"id": "68b7fc08-506d-4816-9a8f-db7ab89e4589", "name": "Set Variables", "type": "n8n-nodes-base.set", "position": [160, 400], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "83274f6f-c73e-4d5e-946f-c6dfdf7ed1c4", "name": "file_url", "type": "string", "value": "https://cflobdhpqwnoisuctsoc.supabase.co/storage/v1/object/public/my_storage/software_engineer_resume_example.pdf"}, {"id": "6e44f3e5-a0df-4337-9f7e-7cfa91b3cc37", "name": "job_description", "type": "string", "value": "Melange is a venture-backed startup building a brand new search infrastructure for the patent system. Leveraging recent and ongoing advancements in machine learning and natural language processing, we are building systems to conduct patent search faster and more accurately than any human currently can. We are a small team with a friendly, mostly-remote culture\\n\\nAbout the team\\nMelange is currently made up of 9 people. We are remote but headquartered in Brooklyn, NY. We look for people who are curious and earnest.\\n\\nAbout the role\\nJoin the team at Melange, a startup with a focus on revolutionizing patent search through advanced technology. As a software engineer in this role, you will be responsible for developing conversation graphs, integrating grammar processes, and maintaining a robust codebase. The ideal candidate will have experience shipping products, working with cloud platforms, and have familiarity with containerization tools. Additionally, experience with prompting tools, NLP packages, and cybersecurity is a plus.\\n\\nCandidate location - the US. Strong preference if they're in NYC, Boston or SF but open to anywhere else but needs to be rockstar\\n\\nYou will \\n\\n* Ship high-quality products.\\n* Utilize prompting libraries such as Langchain and Langgraph to develop conversation graphs and evaluation flows.\\n* Collaborate with linguists to integrate our in-house grammar and entity mapping processes into an iterable patent search algorithm piloted by AI patent agents.\\n* Steward the codebase, ensuring that it remains robust as it scales.\\n\\n\\nCandidate requirements\\nMinimum requirements a candidate must meet\\nHad ownership over aspects of product development in both small and large organizations at differing points in your career.\\n\\nHave used Langchain, LangGraph, or other prompting tools in production or for personal projects.\\n\\nFamiliarity with NLP packages such as Spacy, Stanza, PyTorch, and/or Tensorflow.\\n\\nShipped a working product to users, either as part of a team or on your own. \\nThis means you have: \\nproficiency with one of AWS, Azure, or Google Cloud, \\nfamiliarity with containerization and orchestration tools like Docker and Kubernetes, and \\nbuilt and maintained CI/CD pipelines.\\n5+ years of experience as a software engineer\\n\\nNice-to-haves\\nWhat could make your candidate stand out\\nExperience with cybersecurity.\\n\\nIdeal companies\\nSuccessful b2b growth stage startups that have a strong emphasis on product and design. Orgs with competent management where talent is dense and protected.\\n\\nRamp, Rippling, Brex, Carta, Toast, Asana, Airtable, Benchling, Figma, Gusto, Stripe, Plaid, Monday.com, Smartsheet, Bill.com, Freshworks, Intercom, Sprout Social, Sisense, InsightSquared, DocuSign, Dropbox, Slack, Trello, Qualtrics, Datadog, HubSpot, Shopify, Zendesk, SurveyMonkey, Squarespace, Mixpanel, Github, Atlassian, Zapier, PagerDuty, Box, Snowflake, Greenhouse, Lever, Pendo, Lucidchart, Asana, New Relic, Kajabi, Veeva Systems, Adyen, Twilio, Workday, ServiceNow, Confluent.\\n"}, {"id": "c597c502-9a3c-48e6-a5f5-8a2a8be7282c", "name": "prompt", "type": "string", "value": "You are the recruiter in recruiting agency, you are strict and you pay extra attention on details in a resume. You work with companies and find talents for their jobs. You asses any resume really attentively and critically. If the candidate is a jumper, you notice that and say us.   You need to say if the candidate from out base is suitable for this job.  Return 4 things: 1. Percentage (10% step) of matching candidate resume with job. 2. Short summary - should use simple language and be short. Provide final decision on candidate based on matching percentage and candidate skills vs job requirements. 3. Summary why this candidate suits this jobs. 4. Summary why this candidate doesn't suit this jobs."}, {"id": "1884eed1-9111-4ce1-8d07-ed176611f2d8", "name": "json_schema", "type": "string", "value": "{   \"name\": \"candidate_evaluation\",   \"description\": \"Structured data for evaluating a candidate based on experience and fit\",   \"strict\": true,   \"schema\": {     \"type\": \"object\",     \"properties\": {       \"percentage\": {         \"type\": \"integer\",         \"description\": \"Overall suitability percentage score for the candidate\"       },       \"summary\": {         \"type\": \"string\",         \"description\": \"A brief summary of the candidate's experience, personality, and any notable strengths or concerns\"       },       \"reasons-suit\": {         \"type\": \"array\",         \"items\": {           \"type\": \"object\",           \"properties\": {             \"name\": { \"type\": \"string\", \"description\": \"Title of the strength or reason for suitability\" },             \"text\": { \"type\": \"string\", \"description\": \"Description of how this experience or skill matches the job requirements\" }           },           \"required\": [\"name\", \"text\"],           \"additionalProperties\": false         },         \"description\": \"List of reasons why the candidate is suitable for the position\"       },       \"reasons-notsuit\": {         \"type\": \"array\",         \"items\": {           \"type\": \"object\",           \"properties\": {             \"name\": { \"type\": \"string\", \"description\": \"Title of the concern or reason for unsuitability\" },             \"text\": { \"type\": \"string\", \"description\": \"Description of how this factor may not align with the job requirements\" }           },           \"required\": [\"name\", \"text\"],           \"additionalProperties\": false         },         \"description\": \"List of reasons why the candidate may not be suitable for the position\"       }     },     \"required\": [\"percentage\", \"summary\", \"reasons-suit\", \"reasons-notsuit\"],     \"additionalProperties\": false   } }"}]}}, "typeVersion": 3.4}, {"id": "22dedac7-c44b-430f-b9c7-57d0c55328fa", "name": "Parsed JSON", "type": "n8n-nodes-base.set", "position": [880, 400], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "83274f6f-c73e-4d5e-946f-c6dfdf7ed1c4", "name": "json_parsed", "type": "object", "value": "={{ JSON.parse($json.choices[0].message.content) }}"}]}}, "typeVersion": 3.4}], "pinData": {}, "connections": {"Download File": {"main": [[{"node": "Extract Document PDF", "type": "main", "index": 0}]]}, "Set Variables": {"main": [[{"node": "Download File", "type": "main", "index": 0}]]}, "OpenAI - Analyze CV": {"main": [[{"node": "Parsed JSON", "type": "main", "index": 0}]]}, "Extract Document PDF": {"main": [[{"node": "OpenAI - Analyze CV", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set Variables", "type": "main", "index": 0}]]}}}