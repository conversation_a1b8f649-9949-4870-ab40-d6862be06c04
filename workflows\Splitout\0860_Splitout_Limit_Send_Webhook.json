{"meta": {"instanceId": "89c9c2dbc29ad74e9e02caaf3e27ce718c567278274962e355a9a9679d5f3af7"}, "nodes": [{"id": "33e94ee1-4244-4075-bb4b-93a99a2cacd9", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [20, 560], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "typeVersion": 1.2}, {"id": "dd97266d-a039-4d8f-bc7d-fb439ad5a6d7", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [-180, 0], "parameters": {}, "typeVersion": 1}, {"id": "c4d4a979-3182-46c9-b145-fa4e6ba57011", "name": "Fetch Essay List", "type": "n8n-nodes-base.httpRequest", "position": [80, 0], "parameters": {"url": "http://www.paulgraham.com/articles.html", "options": {}}, "typeVersion": 4.2}, {"id": "2e2913f9-d01a-41e8-b1b8-9a981910db7b", "name": "Extract essay names", "type": "n8n-nodes-base.html", "position": [280, 0], "parameters": {"options": {}, "operation": "extractHtmlContent", "extractionValues": {"values": [{"key": "essay", "attribute": "href", "cssSelector": "table table a", "returnArray": true, "returnValue": "attribute"}]}}, "typeVersion": 1.2}, {"id": "c121dc65-37e3-49d4-b449-f28491e19a6f", "name": "Split out into items", "type": "n8n-nodes-base.splitOut", "position": [480, 0], "parameters": {"options": {}, "fieldToSplitOut": "essay"}, "typeVersion": 1}, {"id": "5644c48d-62b6-4e2d-ad25-013b55f5ec71", "name": "Fetch essay texts", "type": "n8n-nodes-base.httpRequest", "position": [880, 0], "parameters": {"url": "=http://www.paulgraham.com/{{ $json.essay }}", "options": {}}, "typeVersion": 4.2}, {"id": "cd84596e-4046-4d33-9f43-cf464e5c5c01", "name": "Limit to first 3", "type": "n8n-nodes-base.limit", "position": [680, 0], "parameters": {"maxItems": 3}, "typeVersion": 1}, {"id": "318aeeed-fcce-4de2-aa04-92033ef01f28", "name": "Extract Text Only", "type": "n8n-nodes-base.html", "position": [1200, 0], "parameters": {"options": {}, "operation": "extractHtmlContent", "extractionValues": {"values": [{"key": "data", "cssSelector": "body", "skipSelectors": "img,nav"}]}}, "typeVersion": 1.2}, {"id": "0668851e-a31f-4e6e-8966-4544092e318e", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [0, -120], "parameters": {"width": 1071.752021563343, "height": 285.66037735849045, "content": "## <PERSON><PERSON>e latest <PERSON> essays"}, "typeVersion": 1}, {"id": "cf9af24c-9e08-4f27-ad4e-509f72e54a9b", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1120, -120], "parameters": {"width": 625, "height": 607, "content": "## Load into Milvus vector store"}, "typeVersion": 1}, {"id": "95e9a59d-1832-4eb7-b58d-ba391c1acb1c", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-200, 380], "webhookId": "cd2703a7-f912-46fe-8787-3fb83ea116ab", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "0076ea3d-e667-4df2-83c3-9de0d3de0498", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-380, -160], "parameters": {"width": 280, "height": 180, "content": "## Step 1\n1. Set up a Milvus server based on [this guide](https://milvus.io/docs/install_standalone-docker-compose.md). And then create a collection named `my_collection`.\n2. Click this workflow to load scrape and load <PERSON> essays to Milvus collection.\n"}, "typeVersion": 1}, {"id": "e90a069e-cfd8-49f1-8fe6-a334bb920027", "name": "Milvus Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStoreMilvus", "position": [1420, 0], "parameters": {"mode": "insert", "options": {"clearCollection": true}, "milvusCollection": {"__rl": true, "mode": "list", "value": "my_collection", "cachedResultName": "my_collection"}}, "typeVersion": 1.1}, {"id": "d786c471-d564-4f25-beab-f1c7f4559f7a", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [1460, 220], "parameters": {"options": {}, "jsonData": "={{ $('Extract Text Only').item.json.data }}", "jsonMode": "expressionData"}, "typeVersion": 1}, {"id": "26730b7b-2bb9-46f8-83c3-3d4ffdfdef57", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [1320, 240], "parameters": {"options": {}}, "typeVersion": 1.2}, {"id": "de836110-4073-44d5-bbf3-d57f57525f69", "name": "Recursive Character Text Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "position": [1540, 340], "parameters": {"options": {}, "chunkSize": 6000}, "typeVersion": 1}, {"id": "ddaa936e-416a-40e4-adf6-cf7ebfb8b094", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-380, 280], "parameters": {"width": 280, "height": 120, "content": "## Step 2\nChat with this QA Chain with <PERSON><PERSON><PERSON><PERSON> retriever\n"}, "typeVersion": 1}, {"id": "f5b7410f-37c7-40ff-b841-12ed04252317", "name": "Embeddings OpenAI1", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [80, 860], "parameters": {"options": {}}, "typeVersion": 1.2}, {"id": "7a5d1b3f-9b2c-4943-9b40-2a213e30159c", "name": "Milvus Vector Store1", "type": "@n8n/n8n-nodes-langchain.vectorStoreMilvus", "position": [120, 720], "parameters": {"milvusCollection": {"__rl": true, "mode": "list", "value": "my_collection", "cachedResultName": "my_collection"}}, "typeVersion": 1.1}, {"id": "2402387f-e147-4239-9128-34af296e0012", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-20, 360], "parameters": {"color": 7, "width": 574, "height": 629, "content": ""}, "typeVersion": 1}, {"id": "3665ef25-e464-496a-84d6-980b96e78e9a", "name": "Q&A Chain to Retrieve from Milvus and Answer Question", "type": "@n8n/n8n-nodes-langchain.chainRetrievalQa", "position": [120, 380], "parameters": {"options": {}}, "typeVersion": 1.5}, {"id": "10bf4a2c-ee2b-4185-b1e5-29b8664078fb", "name": "Milvus Vector Store Retriever", "type": "@n8n/n8n-nodes-langchain.retrieverVectorStore", "position": [260, 580], "parameters": {}, "typeVersion": 1}], "pinData": {}, "connections": {"Fetch Essay List": {"main": [[{"node": "Extract essay names", "type": "main", "index": 0}]]}, "Limit to first 3": {"main": [[{"node": "Fetch essay texts", "type": "main", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Milvus Vector Store", "type": "ai_embedding", "index": 0}]]}, "Extract Text Only": {"main": [[{"node": "Milvus Vector Store", "type": "main", "index": 0}]]}, "Fetch essay texts": {"main": [[{"node": "Extract Text Only", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Q&A Chain to Retrieve from Milvus and Answer Question", "type": "ai_languageModel", "index": 0}]]}, "Embeddings OpenAI1": {"ai_embedding": [[{"node": "Milvus Vector Store1", "type": "ai_embedding", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Milvus Vector Store", "type": "ai_document", "index": 0}]]}, "Extract essay names": {"main": [[{"node": "Split out into items", "type": "main", "index": 0}]]}, "Milvus Vector Store1": {"ai_vectorStore": [[{"node": "Milvus Vector Store Retriever", "type": "ai_vectorStore", "index": 0}]]}, "Split out into items": {"main": [[{"node": "Limit to first 3", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "Q&A Chain to Retrieve from Milvus and Answer Question", "type": "main", "index": 0}]]}, "Milvus Vector Store Retriever": {"ai_retriever": [[{"node": "Q&A Chain to Retrieve from Milvus and Answer Question", "type": "ai_retriever", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "Fetch Essay List", "type": "main", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}}}