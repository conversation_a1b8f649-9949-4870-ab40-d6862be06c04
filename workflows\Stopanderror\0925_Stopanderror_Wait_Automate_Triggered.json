{"name": "Prevent concurrent workflow runs using Redis", "nodes": [{"id": "e0729c93-e192-42fb-9ba0-c465d5a38089", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-60, 720], "parameters": {"workflowInputs": {"values": [{"name": "action"}, {"name": "value"}, {"name": "key"}, {"name": "timeout"}]}}, "typeVersion": 1.1}, {"id": "caee6ecf-76ee-4014-8e85-a2463dcbba86", "name": "Switch", "type": "n8n-nodes-base.switch", "position": [380, 720], "parameters": {"rules": {"values": [{"outputKey": "get", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "97af18cf-bcf5-4b86-86fd-1ee82e9adba9", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.action }}", "rightValue": "get"}]}, "renameOutput": true}, {"outputKey": "set", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "439b4586-bd92-40b7-afbc-9e651032b390", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.action }}", "rightValue": "set"}]}, "renameOutput": true}, {"outputKey": "unset", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "56457547-42b6-4e0a-8d1e-cb953f3d6a9d", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.action }}", "rightValue": "unset"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "7427686e-6e17-45d2-b79d-cbf22ce2a839", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-700, 1260], "parameters": {}, "typeVersion": 1}, {"id": "922a8778-b800-4fc8-8da9-de3077289fda", "name": "If2", "type": "n8n-nodes-base.if", "position": [420, 220], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "210ed6f8-0b42-4382-9f42-1deb14cab551", "operator": {"type": "string", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json.output }}", "rightValue": "[null]"}]}}, "typeVersion": 2.2}, {"id": "3422b389-6f3f-4ff4-8170-48eb284a34ce", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-120, -20], "parameters": {"width": 860, "height": 420, "content": "## Check if working\nWill output status if something is going on.\n\nInputs:\n- action -> \"get\"\n- key -> a string used for tracking\n\n* Remove unused inputs when calling workflow *"}, "typeVersion": 1}, {"id": "78a21b91-d11c-4c85-814a-9074cce64383", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [780, -20], "parameters": {"width": 1080, "height": 420, "content": "## Set workflow start\n\nInputs:\n- action -> \"set\"\n- key -> a string used for tracking\n- value -> \"working\" by default but you can use whatever, you can use this multiple times in combination with get to track progress of a execution.\n\n* Remove unused inputs when calling workflow *"}, "typeVersion": 1}, {"id": "c200a3f4-df46-44a4-8747-180d3df841ab", "name": "Is Workflow Active", "type": "n8n-nodes-base.executeWorkflow", "position": [200, 220], "parameters": {"options": {}, "workflowId": {"__rl": true, "mode": "list", "value": "Fx8Ov5MblG2cmRWR", "cachedResultName": "template - cocurrent workflow"}, "workflowInputs": {"value": {"key": "some_workflow_key", "action": "get"}, "schema": [{"id": "action", "type": "string", "display": true, "required": false, "displayName": "action", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "value", "type": "string", "display": true, "removed": true, "required": false, "displayName": "value", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "key", "type": "string", "display": true, "required": false, "displayName": "key", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "timeout", "type": "string", "display": true, "removed": true, "required": false, "displayName": "timeout", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": true}}, "typeVersion": 1.2}, {"id": "e56202e7-5227-4002-9d92-861cf76f1840", "name": "Set Workflow Active", "type": "n8n-nodes-base.executeWorkflow", "position": [1360, 220], "parameters": {"options": {}, "workflowId": {"__rl": true, "mode": "list", "value": "Fx8Ov5MblG2cmRWR", "cachedResultName": "template - cocurrent workflow"}, "workflowInputs": {"value": {"key": "some_workflow_key", "value": "working", "action": "set"}, "schema": [{"id": "action", "type": "string", "display": true, "required": false, "displayName": "action", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "value", "type": "string", "display": true, "required": false, "displayName": "value", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "key", "type": "string", "display": true, "required": false, "displayName": "key", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "timeout", "type": "string", "display": true, "removed": true, "required": false, "displayName": "timeout", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": true}}, "typeVersion": 1.2}, {"id": "2324d74c-b519-462b-af5c-13aa9e02ef88", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1040, 440], "parameters": {"width": 820, "height": 640, "content": "## Set workflow end\nThis mark workflow end by unsetting the key.\n\nInputs:\n- action -> \"unset\"\n- key -> a string used for tracking\n\n* Remove unused inputs when calling workflow *"}, "typeVersion": 1}, {"id": "cfa05881-6b6d-474f-8b5b-9ca1ea4b68dc", "name": "Set Workflow Finished", "type": "n8n-nodes-base.executeWorkflow", "position": [1420, 780], "parameters": {"options": {}, "workflowId": {"__rl": true, "mode": "list", "value": "Fx8Ov5MblG2cmRWR", "cachedResultName": "template - cocurrent workflow"}, "workflowInputs": {"value": {"key": "some_workflow_key", "action": "unset"}, "schema": [{"id": "action", "type": "string", "display": true, "required": false, "displayName": "action", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "value", "type": "string", "display": true, "removed": true, "required": false, "displayName": "value", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "key", "type": "string", "display": true, "required": false, "displayName": "key", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "timeout", "type": "string", "display": true, "removed": true, "required": false, "displayName": "timeout", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": true}}, "typeVersion": 1.2}, {"id": "33b00dc4-ae18-49e3-9566-592cba18874c", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-120, 440], "parameters": {"color": 3, "width": 1120, "height": 640, "content": "## Redis logic\n\nModify Set Timeout to adjust timeout (in seconds)\n\n"}, "typeVersion": 1}, {"id": "5a659a3b-87e9-4652-91e2-11e8742dbf62", "name": "Get Key", "type": "n8n-nodes-base.redis", "position": [600, 520], "parameters": {"key": "=process_status_{{ $json.key }}", "options": {}, "operation": "get", "propertyName": "=output"}, "credentials": {"redis": {"id": "Z86e3zGaVJ8EUt7c", "name": "Redis account"}}, "typeVersion": 1}, {"id": "494155e9-8461-462c-a441-7eca4385c0b6", "name": "Set Key", "type": "n8n-nodes-base.redis", "position": [600, 720], "parameters": {"key": "=process_status_{{ $json.key }}", "ttl": "={{ $json.timeout }}", "value": "={{ $json.value }}", "expire": true, "operation": "set"}, "credentials": {"redis": {"id": "Z86e3zGaVJ8EUt7c", "name": "Redis account"}}, "typeVersion": 1}, {"id": "a692e5a3-d781-4293-8d82-2a63ef771d27", "name": "UnSet Key", "type": "n8n-nodes-base.redis", "position": [600, 920], "parameters": {"key": "=process_status_{{ $json.key }}", "operation": "delete"}, "credentials": {"redis": {"id": "Z86e3zGaVJ8EUt7c", "name": "Redis account"}}, "typeVersion": 1}, {"id": "ce67d06c-1778-4783-b739-2bdc79ec341e", "name": "Set Timeout", "type": "n8n-nodes-base.set", "position": [160, 720], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "ec322ae8-d486-4c8e-a175-3b908054ded9", "name": "timeout", "type": "number", "value": 600}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "afda543f-247d-4286-ba65-b0f4c4b3b747", "name": "set continue", "type": "n8n-nodes-base.set", "position": [820, 720], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "ae33a17b-8db0-47bc-85fc-4fde4be335de", "name": "ok", "type": "string", "value": "true"}]}}, "typeVersion": 3.4}, {"id": "1c026eab-dc40-48dd-972d-dee7f6c0f05b", "name": "If", "type": "n8n-nodes-base.if", "position": [340, 1360], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "210ed6f8-0b42-4382-9f42-1deb14cab551", "operator": {"type": "string", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json.output }}", "rightValue": "[null]"}]}}, "typeVersion": 2.2}, {"id": "b603bad0-7f89-429d-a373-37d52ed11a04", "name": "Is Workflow Active1", "type": "n8n-nodes-base.executeWorkflow", "position": [120, 1360], "parameters": {"options": {}, "workflowId": {"__rl": true, "mode": "list", "value": "Fx8Ov5MblG2cmRWR", "cachedResultName": "template - cocurrent workflow"}, "workflowInputs": {"value": {"key": "some_workflow_key", "action": "get"}, "schema": [{"id": "action", "type": "string", "display": true, "required": false, "displayName": "action", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "value", "type": "string", "display": true, "removed": true, "required": false, "displayName": "value", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "key", "type": "string", "display": true, "required": false, "displayName": "key", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "timeout", "type": "string", "display": true, "removed": true, "required": false, "displayName": "timeout", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": true}}, "typeVersion": 1.2}, {"id": "e73d4271-1844-4b34-9e9b-a2154c3c27ee", "name": "Stop and Error", "type": "n8n-nodes-base.stopAndError", "position": [620, 1460], "parameters": {"errorMessage": "Already Executing"}, "typeVersion": 1}, {"id": "5307c6f7-7533-4f22-ab1a-a324ba2c9836", "name": "No Operation, do nothing", "type": "n8n-nodes-base.noOp", "position": [840, 1260], "parameters": {}, "typeVersion": 1}, {"id": "201ffd2d-0c4f-417a-9a82-5035e1530be7", "name": "Set Workflow Active1", "type": "n8n-nodes-base.executeWorkflow", "position": [620, 1260], "parameters": {"options": {}, "workflowId": {"__rl": true, "mode": "list", "value": "Fx8Ov5MblG2cmRWR", "cachedResultName": "template - cocurrent workflow"}, "workflowInputs": {"value": {"key": "some_workflow_key", "value": "working", "action": "set"}, "schema": [{"id": "action", "type": "string", "display": true, "required": false, "displayName": "action", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "value", "type": "string", "display": true, "required": false, "displayName": "value", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "key", "type": "string", "display": true, "required": false, "displayName": "key", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "timeout", "type": "string", "display": true, "removed": true, "required": false, "displayName": "timeout", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": true}}, "typeVersion": 1.2}, {"id": "71969764-4a75-44b6-a7da-969d8ab73686", "name": "Set Workflow Finished1", "type": "n8n-nodes-base.executeWorkflow", "position": [1560, 1260], "parameters": {"options": {}, "workflowId": {"__rl": true, "mode": "list", "value": "Fx8Ov5MblG2cmRWR", "cachedResultName": "template - cocurrent workflow"}, "workflowInputs": {"value": {"key": "some_workflow_key", "action": "unset"}, "schema": [{"id": "action", "type": "string", "display": true, "required": false, "displayName": "action", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "value", "type": "string", "display": true, "removed": true, "required": false, "displayName": "value", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "key", "type": "string", "display": true, "required": false, "displayName": "key", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "timeout", "type": "string", "display": true, "removed": true, "required": false, "displayName": "timeout", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": true}}, "typeVersion": 1.2}, {"id": "be5729c1-c867-4364-a406-ccbb4b829de6", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [760, 1160], "parameters": {"color": 7, "width": 740, "height": 260, "content": "## Your logic that takes time\nIf a process is ran at the same moment, it will raise a error"}, "typeVersion": 1}, {"id": "1d16d72c-0678-4808-8f7d-e7a6e7a20497", "name": "Wait", "type": "n8n-nodes-base.wait", "position": [1060, 1260], "webhookId": "e87487db-c8b2-44e2-8d90-0ed189a8116b", "parameters": {}, "typeVersion": 1.1}, {"id": "ddfbb5c1-6632-423f-9a0a-0e18497d4c3b", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-120, 1120], "parameters": {"color": 4, "width": 1980, "height": 500, "content": "## Example 1"}, "typeVersion": 1}, {"id": "ce9d5b6d-0353-46b9-bee4-84c977bca8de", "name": "If1", "type": "n8n-nodes-base.if", "position": [180, 1920], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "210ed6f8-0b42-4382-9f42-1deb14cab551", "operator": {"type": "string", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json.output }}", "rightValue": "[null]"}]}}, "typeVersion": 2.2}, {"id": "1e1bc1cc-5f95-4a1c-892a-90303745bb53", "name": "Is Workflow Active2", "type": "n8n-nodes-base.executeWorkflow", "position": [-20, 1920], "parameters": {"options": {}, "workflowId": {"__rl": true, "mode": "list", "value": "Fx8Ov5MblG2cmRWR", "cachedResultName": "template - cocurrent workflow"}, "workflowInputs": {"value": {"key": "some_workflow_key", "action": "get"}, "schema": [{"id": "action", "type": "string", "display": true, "required": false, "displayName": "action", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "value", "type": "string", "display": true, "removed": true, "required": false, "displayName": "value", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "key", "type": "string", "display": true, "required": false, "displayName": "key", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "timeout", "type": "string", "display": true, "removed": true, "required": false, "displayName": "timeout", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": true}}, "typeVersion": 1.2}, {"id": "95699b33-61b7-4c3e-8bf9-c716456a387d", "name": "Stop and Error1", "type": "n8n-nodes-base.stopAndError", "position": [480, 2020], "parameters": {"errorMessage": "Already Executing"}, "typeVersion": 1}, {"id": "c8388974-e355-43dd-8d54-f3213998257f", "name": "Set Workflow Finished2", "type": "n8n-nodes-base.executeWorkflow", "position": [1720, 1820], "parameters": {"options": {}, "workflowId": {"__rl": true, "mode": "list", "value": "Fx8Ov5MblG2cmRWR", "cachedResultName": "template - cocurrent workflow"}, "workflowInputs": {"value": {"key": "some_workflow_key", "action": "unset"}, "schema": [{"id": "action", "type": "string", "display": true, "required": false, "displayName": "action", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "value", "type": "string", "display": true, "removed": true, "required": false, "displayName": "value", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "key", "type": "string", "display": true, "required": false, "displayName": "key", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "timeout", "type": "string", "display": true, "removed": true, "required": false, "displayName": "timeout", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": true}}, "typeVersion": 1.2}, {"id": "6465bf4b-f17d-47c7-a547-a24d4e1cb6a1", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [420, 1720], "parameters": {"color": 7, "width": 380, "height": 260, "content": "## Process step 2"}, "typeVersion": 1}, {"id": "92d8920c-a267-4306-8a16-3a77de1a1399", "name": "Wait1", "type": "n8n-nodes-base.wait", "position": [660, 1820], "webhookId": "e87487db-c8b2-44e2-8d90-0ed189a8116b", "parameters": {}, "typeVersion": 1.1}, {"id": "3ac2a634-6eed-4909-aa24-baf606a90bd8", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-120, 1680], "parameters": {"color": 4, "width": 1980, "height": 1120, "content": "## Example 2\nTrack progress\n"}, "typeVersion": 1}, {"id": "499e79d9-6553-42c0-ade6-168dc24f5486", "name": "Wait2", "type": "n8n-nodes-base.wait", "position": [1080, 1820], "webhookId": "e87487db-c8b2-44e2-8d90-0ed189a8116b", "parameters": {}, "typeVersion": 1.1}, {"id": "91c07ab3-68fc-4a2b-8829-f0b12759e861", "name": "Wait3", "type": "n8n-nodes-base.wait", "position": [1500, 1820], "webhookId": "e87487db-c8b2-44e2-8d90-0ed189a8116b", "parameters": {}, "typeVersion": 1.1}, {"id": "725a0e54-5c5b-447f-af1f-1a77ac4f9fd4", "name": "Set Workflow \"started\"", "type": "n8n-nodes-base.executeWorkflow", "position": [480, 1820], "parameters": {"options": {}, "workflowId": {"__rl": true, "mode": "list", "value": "Fx8Ov5MblG2cmRWR", "cachedResultName": "template - cocurrent workflow"}, "workflowInputs": {"value": {"key": "some_workflow_key", "value": "started", "action": "set"}, "schema": [{"id": "action", "type": "string", "display": true, "required": false, "displayName": "action", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "value", "type": "string", "display": true, "required": false, "displayName": "value", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "key", "type": "string", "display": true, "required": false, "displayName": "key", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "timeout", "type": "string", "display": true, "removed": true, "required": false, "displayName": "timeout", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": true}}, "typeVersion": 1.2}, {"id": "17c92181-e9ed-4431-938e-49a5b75f761d", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [840, 1720], "parameters": {"color": 7, "width": 380, "height": 260, "content": "## Process step 2\n"}, "typeVersion": 1}, {"id": "765177ce-5f13-4144-be26-cc532a27cc7c", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [1260, 1720], "parameters": {"color": 7, "width": 380, "height": 260, "content": "## Process step 3"}, "typeVersion": 1}, {"id": "a84cebe2-57af-4983-b5b5-e33afc478f46", "name": "Set Workflow \"finishing\"", "type": "n8n-nodes-base.executeWorkflow", "position": [1320, 1820], "parameters": {"options": {}, "workflowId": {"__rl": true, "mode": "list", "value": "Fx8Ov5MblG2cmRWR", "cachedResultName": "template - cocurrent workflow"}, "workflowInputs": {"value": {"key": "some_workflow_key", "value": "finishing", "action": "set"}, "schema": [{"id": "action", "type": "string", "display": true, "required": false, "displayName": "action", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "value", "type": "string", "display": true, "required": false, "displayName": "value", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "key", "type": "string", "display": true, "required": false, "displayName": "key", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "timeout", "type": "string", "display": true, "removed": true, "required": false, "displayName": "timeout", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": true}}, "typeVersion": 1.2}, {"id": "d5cbfac4-847a-4655-93fd-481127dc8a1c", "name": "Set Workflow \"loading\"", "type": "n8n-nodes-base.executeWorkflow", "position": [900, 1820], "parameters": {"options": {}, "workflowId": {"__rl": true, "mode": "list", "value": "Fx8Ov5MblG2cmRWR", "cachedResultName": "template - cocurrent workflow"}, "workflowInputs": {"value": {"key": "some_workflow_key", "value": "loading", "action": "set"}, "schema": [{"id": "action", "type": "string", "display": true, "required": false, "displayName": "action", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "value", "type": "string", "display": true, "required": false, "displayName": "value", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "key", "type": "string", "display": true, "required": false, "displayName": "key", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "timeout", "type": "string", "display": true, "removed": true, "required": false, "displayName": "timeout", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": true}}, "typeVersion": 1.2}, {"id": "cd70a02f-e95c-4bda-9b89-50a20600787c", "name": "Is Workflow Active3", "type": "n8n-nodes-base.executeWorkflow", "position": [40, 2400], "parameters": {"options": {}, "workflowId": {"__rl": true, "mode": "list", "value": "Fx8Ov5MblG2cmRWR", "cachedResultName": "template - cocurrent workflow"}, "workflowInputs": {"value": {"key": "some_workflow_key", "action": "get"}, "schema": [{"id": "action", "type": "string", "display": true, "required": false, "displayName": "action", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "value", "type": "string", "display": true, "removed": true, "required": false, "displayName": "value", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "key", "type": "string", "display": true, "required": false, "displayName": "key", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "timeout", "type": "string", "display": true, "removed": true, "required": false, "displayName": "timeout", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": true}}, "typeVersion": 1.2}, {"id": "6e2578e3-05e0-47fd-94da-77f32612b01c", "name": "Switch1", "type": "n8n-nodes-base.switch", "position": [260, 2380], "parameters": {"rules": {"values": [{"outputKey": "started", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "e4bb7e4f-2f4a-419c-8618-e4fa01afc472", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.output }}", "rightValue": "started"}]}, "renameOutput": true}, {"outputKey": "loading", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "4c621e66-a4fe-45be-9d8a-4a2b180e94e7", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.output }}", "rightValue": "loading"}]}, "renameOutput": true}, {"outputKey": "finished", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "5b373ed7-550e-4d9c-b2cf-838ca08435ff", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.output }}", "rightValue": "finished"}]}, "renameOutput": true}]}, "options": {"fallbackOutput": "extra"}}, "typeVersion": 3.2}, {"id": "9403b00f-f6d5-47d4-b06f-8e0c7c39ed5b", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [0, 2280], "parameters": {"color": 7, "width": 660, "height": 340, "content": "## Check status of execution\n"}, "typeVersion": 1}, {"id": "c3639987-57e6-484a-9094-7f8e99493954", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [-800, 1140], "parameters": {"color": 5, "width": 400, "height": 320, "content": "## Connect this anywhere to test !"}, "typeVersion": 1}], "pinData": {}, "connections": {"If": {"main": [[{"node": "Set Workflow Active1", "type": "main", "index": 0}], [{"node": "Stop and Error", "type": "main", "index": 0}]]}, "If1": {"main": [[{"node": "Set Workflow \"started\"", "type": "main", "index": 0}], [{"node": "Stop and Error1", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "Set Workflow Finished1", "type": "main", "index": 0}]]}, "Wait1": {"main": [[{"node": "Set Workflow \"loading\"", "type": "main", "index": 0}]]}, "Wait2": {"main": [[{"node": "Set Workflow \"finishing\"", "type": "main", "index": 0}]]}, "Wait3": {"main": [[{"node": "Set Workflow Finished2", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Get Key", "type": "main", "index": 0}], [{"node": "Set Key", "type": "main", "index": 0}], [{"node": "UnSet Key", "type": "main", "index": 0}]]}, "Set Key": {"main": [[{"node": "set continue", "type": "main", "index": 0}]]}, "UnSet Key": {"main": [[{"node": "set continue", "type": "main", "index": 0}]]}, "Set Timeout": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Is Workflow Active": {"main": [[{"node": "If2", "type": "main", "index": 0}]]}, "Is Workflow Active1": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Is Workflow Active2": {"main": [[{"node": "If1", "type": "main", "index": 0}]]}, "Is Workflow Active3": {"main": [[{"node": "Switch1", "type": "main", "index": 0}]]}, "Set Workflow Active1": {"main": [[{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "Set Workflow \"loading\"": {"main": [[{"node": "Wait2", "type": "main", "index": 0}]]}, "Set Workflow \"started\"": {"main": [[{"node": "Wait1", "type": "main", "index": 0}]]}, "No Operation, do nothing": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Set Workflow \"finishing\"": {"main": [[{"node": "Wait3", "type": "main", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Set Timeout", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[]]}}}