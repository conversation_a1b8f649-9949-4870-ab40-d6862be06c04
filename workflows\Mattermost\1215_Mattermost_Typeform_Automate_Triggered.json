{"nodes": [{"name": "Mattermost", "type": "n8n-nodes-base.mattermost", "position": [810, 300], "parameters": {"message": "=You got new feedback with a score of {{$json[\"SentimentScore\"][\"Negative\"]}}. Here is what it says:{{$node[\"Typeform Trigger\"].json[\"What did you think about the event?\"]}}", "channelId": "h7cxrd1cefr13x689enzyw7xhc", "attachments": [], "otherOptions": {}}, "credentials": {"mattermostApi": "Mattermost Credentials"}, "typeVersion": 1}, {"name": "NoOp", "type": "n8n-nodes-base.noOp", "position": [800, 500], "parameters": {}, "typeVersion": 1}, {"name": "IF", "type": "n8n-nodes-base.if", "position": [600, 400], "parameters": {"conditions": {"number": [], "string": [{"value1": "={{$json[\"Sentiment\"]}}", "value2": "NEGATIVE"}]}}, "typeVersion": 1}, {"name": "AWS Comprehend", "type": "n8n-nodes-base.awsComprehend", "position": [400, 400], "parameters": {"text": "={{$json[\"What did you think about the event?\"]}}", "operation": "detectSentiment"}, "credentials": {"aws": "AWS Comprehend Credentials"}, "typeVersion": 1}, {"name": "Typeform Trigger", "type": "n8n-nodes-base.typeformTrigger", "position": [200, 400], "webhookId": "ad8a87ef-d293-4e48-8d36-838d69ebce0f", "parameters": {"formId": "DuJHEGW5"}, "credentials": {"typeformApi": "typeform"}, "typeVersion": 1}], "connections": {"IF": {"main": [[{"node": "Mattermost", "type": "main", "index": 0}], [{"node": "NoOp", "type": "main", "index": 0}]]}, "AWS Comprehend": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}, "Typeform Trigger": {"main": [[{"node": "AWS Comprehend", "type": "main", "index": 0}]]}}}