{"nodes": [{"id": "061bf344-8e0d-46df-a097-dfc000b63bbd", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [1240, 1020], "parameters": {}, "typeVersion": 1}, {"id": "3d66c4ce-e150-4f51-a9f9-4e7f61981ba4", "name": "Set link to audio", "type": "n8n-nodes-base.set", "position": [1440, 1020], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "745e32ae-61e6-429b-a107-f20c9b17e65e", "name": "url", "type": "string", "value": "https://cflobdhpqwnoisuctsoc.supabase.co/storage/v1/object/public/5minai/OUTBOUNDSAMPLE_01.mp3"}]}}, "typeVersion": 3.4}, {"id": "f6fe5922-1140-499f-b38b-3f0bc0b398cc", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1620, 895], "parameters": {"width": 224.61210598590444, "height": 80, "content": "**Replace API key in <PERSON><PERSON>, webhook from 2nd scenario, change settings if needed**"}, "typeVersion": 1}, {"id": "37454706-c012-45b1-83ca-a618a28c27d5", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1400, 900], "parameters": {"width": 167.86310443717323, "height": 80, "content": "**Replace this link with direct link on your audio**"}, "typeVersion": 1}, {"id": "a52fc9df-20b5-4b2e-956d-66604be12660", "name": "AssemblyAI - Transcribe", "type": "n8n-nodes-base.httpRequest", "position": [1680, 1020], "parameters": {"url": "=https://api.assemblyai.com/v2/transcript", "method": "POST", "options": {}, "sendBody": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "audio_url", "value": "={{ $json.url }}"}, {"name": "language_code", "value": "en_us"}, {"name": "speaker_labels", "value": "={{ true }}"}, {"name": "speakers_expected", "value": "={{ 2 }}"}, {"name": "webhook_url", "value": "https://n8n.lowcoding.dev/webhook/d1e5fdd0-b51d-4447-8af3-6754017d240b"}]}, "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "l5EqDIMpaRMfONeN", "name": "Assembly"}}, "typeVersion": 4.2}, {"id": "89af9ab5-d59b-4224-af00-7b55c4905022", "name": "If", "type": "n8n-nodes-base.if", "position": [1580, 1500], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "1e41aa3d-7ea3-4e9f-8d99-e8199e7ec449", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Webhook').item.json.body.status }}", "rightValue": "completed"}]}}, "typeVersion": 2.2}, {"id": "27653ac0-6703-4d25-bc0f-53b93b293b05", "name": "AssemblyAI - Get transcription", "type": "n8n-nodes-base.httpRequest", "position": [1800, 1500], "parameters": {"url": "=https://api.assemblyai.com/v2/transcript/{{ $('Webhook').item.json.body.transcript_id }}", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "l5EqDIMpaRMfONeN", "name": "Assembly"}}, "typeVersion": 4.2}, {"id": "50166503-27a2-435d-a455-1ea33384585f", "name": "OpenAI - Analyze call", "type": "n8n-nodes-base.httpRequest", "position": [1980, 1500], "parameters": {"url": "=https://api.openai.com/v1/chat/completions", "method": "POST", "options": {}, "jsonBody": "={\n    \"model\": \"gpt-4o-2024-08-06\",\n    \"messages\": [\n      {\n        \"role\": \"system\",\n        \"content\": \"{{ $('Set vars').item.json.prompt }}\"\n      },\n      {\n        \"role\": \"user\",\n        \"content\": {{ JSON.stringify($json.utterances.map(u => `Speaker ${u.speaker}: ${u.text}`).join(\"\\n\\n\"))}}\n      }\n    ],\n  \"response_format\":{ \"type\": \"json_schema\", \"json_schema\":   {{ JSON.stringify($('Set vars').item.json.json_schema) }} }\n  }", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "SphXAX7rlwRLkiox", "name": "Test club key"}}, "typeVersion": 4.2}, {"id": "266a2e6a-e0cc-4130-a8f0-038d2ba5992c", "name": "Set vars", "type": "n8n-nodes-base.set", "position": [1400, 1500], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c93d6cfb-cd36-4d8b-acc5-b3718bfc82c8", "name": "prompt", "type": "string", "value": "Analyze this call with a focus on sales performance and potential upselling opportunities. Use the following metrics to evaluate the effectiveness of the interaction from a sales perspective. Be specific in identifying areas of strength and those needing improvement, and provide actionable insights for future calls.\\r\\n\\r\\nClient Intent: Identify the client\\u2019s primary intent and motivation for engaging in the call. Is there a clear indication of their needs or desired outcomes? How well does the agent align with or address these intentions?\\r\\n\\r\\nInterest Score (0-100): Evaluate the level of genuine interest shown by the client based on their responses, tone, and any probing questions they asked. Assign a score and explain the reasoning behind it.\\r\\n\\r\\nService Presentation Score (0-100): Assess how effectively the agent presented the service options. Did they clarify benefits that align with the client\\u2019s needs? Rate the agent\\u2019s performance in this area and provide a brief explanation.\\r\\n\\r\\nUpsell Opportunity Identified: Indicate whether any upselling opportunities were identified (e.g., suggesting advanced programs, certifications, or additional services). If opportunities were missed, explain how they could have been approached.\\r\\n\\r\\nObjection Handling Score (0-100): If the client expressed objections or hesitations, analyze how effectively the agent addressed them. Did they provide relevant solutions or reassurances? Assign a score and justify it with specific examples from the call.\\r\\n\\r\\nConversion Probability (0-100): Based on the client\\u2019s interest level and engagement, estimate the likelihood of conversion. What signals from the client suggest a high or low probability of moving forward?\\r\\n\\r\\nCall Outcome: Summarize the outcome of the call (e.g., \\u201Cclient expressed strong interest,\\u201D \\u201Cclient requested more information,\\u201D or \\u201Cclient expressed no interest\\u201D). Was a clear next step established?\\r\\n\\r\\nKey Client Needs: Highlight the client\\u2019s specific needs, preferences, or pain points mentioned during the call. How could these be addressed in future interactions to improve alignment with the client\\u2019s goals?\\r\\n\\r\\nInsights for Agent Improvement: Provide constructive feedback for the agent\\u2019s performance, focusing on areas such as empathy, product knowledge, and upsell strategy. Suggest improvements that could enhance their approach to increase client engagement.\\r\\n\\r\\nNext Steps Recommended: Recommend specific follow-up actions, such as providing customized information, sending relevant resources, or setting up a consultation. Focus on actions that will nurture the client relationship and increase conversion chances.\\r\\n\\r\\nIn your analysis, emphasize clarity and actionable feedback that leverages sales best practices. Highlight how well the agent built rapport, presented the product, and worked towards a favorable outcome for both the client and company"}, {"id": "4305764d-202c-4ab9-ae1e-d753cd68b7be", "name": "json_schema", "type": "object", "value": "={{   {   \"name\": \"sales_call_analysis\",   \"description\": \"Analysis of customer engagement, interest, and potential sales conversion based on call data\",   \"strict\": true,   \"schema\": {     \"$schema\": \"http://json-schema.org/draft-04/schema#\",     \"type\": \"object\",     \"properties\": {       \"client_intent\": {         \"type\": \"string\",         \"description\": \"Client's primary intent for engaging (e.g., 'interest in education', 'general inquiry')\"       },       \"interest_score\": {         \"type\": \"integer\",         \"description\": \"Score indicating client's interest level based on responses (0-100)\"       },       \"service_presentation_score\": {         \"type\": \"integer\",         \"description\": \"Score for how effectively the agent presented the educational opportunities (0-100)\"       },       \"upsell_opportunity_identified\": {         \"type\": \"boolean\",         \"description\": \"Indicates if any upsell opportunity was identified\"       },       \"objection_handling_score\": {         \"type\": \"integer\",         \"description\": \"Score for handling client objections (0-100)\"       },       \"conversion_probability\": {         \"type\": \"integer\",         \"description\": \"Estimated likelihood of conversion based on call interaction (0-100)\"       },       \"call_outcome\": {         \"type\": \"string\",         \"description\": \"Outcome of the call (e.g., 'interest confirmed', 'no interest', 'appointment set')\"       },       \"key_client_needs\": {         \"type\": \"string\",         \"description\": \"Identified needs or requirements of the client, useful for customization or follow-up\"       },       \"insights_for_agent_improvement\": {         \"type\": \"string\",         \"description\": \"Insights and tips for the agent to improve performance\"       },       \"next_steps_recommended\": {         \"type\": \"string\",         \"description\": \"Suggested follow-up actions to improve conversion potential\"       }     },     \"additionalProperties\": false,     \"required\": [       \"client_intent\",       \"interest_score\",       \"service_presentation_score\",       \"upsell_opportunity_identified\",       \"objection_handling_score\",       \"conversion_probability\",       \"call_outcome\",       \"key_client_needs\",       \"insights_for_agent_improvement\",       \"next_steps_recommended\"     ]   } }       }}"}]}}, "typeVersion": 3.4}, {"id": "60dca514-d064-41e9-8ea6-ac3bb29edb74", "name": "Create record", "type": "n8n-nodes-base.supabase", "position": [2180, 1500], "parameters": {"tableId": "demo_calls", "fieldsUi": {"fieldValues": [{"fieldId": "output", "fieldValue": "={{ JSON.parse($json.choices[0].message.content)  }}"}, {"fieldId": "input", "fieldValue": "={{ {\"text\":  JSON.stringify($('AssemblyAI - Get transcription').item.json.utterances.map(u => `Speaker ${u.speaker}: ${u.text}`).join(\"\\n\\n\")) , \"audio_url\": $('AssemblyAI - Get transcription').item.json.audio_url, \"transcription_id\":$('AssemblyAI - Get transcription').item.json.id } }}"}]}}, "credentials": {"supabaseApi": {"id": "iVKNf5qv3ZFhq0ZV", "name": "Supabase 5minAI"}}, "typeVersion": 1}, {"id": "f4dbdeed-03e4-499c-b960-8f70459feb70", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1760, 1400], "parameters": {"width": 167.86310443717323, "height": 80, "content": "**Replace API key and webhook**"}, "typeVersion": 1}, {"id": "5f54be84-cf62-4ce6-bd21-bef8332ab898", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1960, 1400], "parameters": {"width": 150, "height": 80, "content": "**Replace OpenAI connection**"}, "typeVersion": 1}, {"id": "65827237-6611-4ec0-ac62-1d1b4626443f", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-120, 1000], "parameters": {"color": 7, "width": 280.2462120317618, "height": 438.5821431288714, "content": "#### Scenario 1: Transcribe Call with AssemblyAI\n1. **Set Up Request**:\n    - **Header Authentication**: Set `Authorization` with AssemblyAI API key.\n    - **URL**: POST to `https://api.assemblyai.com/v2/transcript/`.\n    - **Parameters**:\n        - `audio_url`: Direct URL of the audio file.\n        - `webhook_url`: URL for an N8N webhook to receive the transcription result.\n        - **Additional Settings**:\n            - `speaker_labels` (true/false): Enables speaker diarization.\n            - `speakers_expected`: Specify expected number of speakers.\n            - `language_code`: Set language (default: `en_us`).\n\n#### Scenario 2: Process Transcription with OpenAI\n1. **Webhook Configuration**: Set up a POST webhook to receive AssemblyAI’s transcription data.\n2. **Get Transcription**:\n    - **Header Authentication**: Set `Authorization` with AssemblyAI API key.\n    - **URL**: GET `https://api.assemblyai.com/v2/transcript/<transcript_id>`.\n3. **Send to OpenAI**:\n    - **URL**: POST to `https://api.openai.com/v1/chat/completions`.\n    - **Header Authentication**: Set `Authorization` with OpenAI API key.\n    - **Body Parameters**:\n        - **Model**: Use `gpt-4o-2024-08-06` for JSON Schema support, or `gpt-4o-mini` for a less costly option.\n        - **Messages**:\n            - `system`: Contains the main analysis prompt.\n            - `user`: Combined speakers’ utterances to analyze in text format.\n        - **Response Format**:\n            - `type`: `json_schema`.\n            - `json_schema`: JSON schema for structured responses.\n\n4. **Save Results in Supabase**:\n    - **Operation**: Create a new record.\n    - **Table Name**: `demo_calls`.\n    - **Fields**:\n        - **Input**: Transcription text, audio URL, and transcription ID.\n        - **Output**: Parsed JSON response from OpenAI’s analysis."}, "typeVersion": 1}, {"id": "1f9883a3-36a3-4bef-9837-7965322cfc12", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-120, 376.29540128507955], "parameters": {"color": 7, "width": 636.2128494576581, "height": 598.6675280064023, "content": "![5min Logo](https://cflobdhpqwnoisuctsoc.supabase.co/storage/v1/object/public/my_storage/Untitled%20(1500%20x%20300%20px).png)\n## Call analyzer with AssemblyAI transcription and OpenAI assistant integration\n**Made by [<PERSON>](https://www.linkedin.com/in/marklowcoding/) from community [5minAI](https://www.skool.com/5minai-2861)**\n\nMany follow-up sales calls lack structured analysis, making it challenging to identify client needs, gauge interest levels, or uncover upsell opportunities. This workflow enables automated call transcription and AI-driven analysis to generate actionable insights, helping teams improve sales performance, refine client communication, and streamline upselling strategies.\n\nThis workflow transcribes and analyzes sales calls using AssemblyAI, OpenAI, and Supabase to store structured data. The workflow processes recorded calls as follows:\n\n1. **Transcribe Call with AssemblyAI**: Converts audio into text with speaker labels for clarity.\n2. **Analyze Transcription with OpenAI**: Using a predefined JSON schema, OpenAI analyzes the transcription to extract metrics like client intent, interest score, upsell opportunities, and more.\n3. **Store and Access Results in Supabase**: Stores both transcription and analysis data in a Supabase database for further use and display in interfaces.\n\n"}, "typeVersion": 1}, {"id": "97fc85cf-f9dc-4659-bcc6-ab7ffbecd4b5", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [180, 1000], "parameters": {"color": 7, "width": 330.5152611046425, "height": 240.6839895136402, "content": "### ... or watch set up video [5 min]\n[![Youtube Thumbnail](https://cflobdhpqwnoisuctsoc.supabase.co/storage/v1/object/public/my_storage/OPENAI%20(8).png)](https://www.youtube.com/watch?v=kS41gut8l0g)\n"}, "typeVersion": 1}, {"id": "dcaf9ed5-face-4199-ae8c-dc70c97588f6", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [1220, 1500], "webhookId": "d1e5fdd0-b51d-4447-8af3-6754017d240b", "parameters": {"path": "d1e5fdd0-b51d-4447-8af3-6754017d240b", "options": {}, "httpMethod": "POST"}, "typeVersion": 2}, {"id": "c7c01657-3b8d-4a1b-a33b-9c9404f17423", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [920, 1520], "parameters": {"color": 5, "height": 80, "content": "## Scenario 2"}, "typeVersion": 1}, {"id": "a8a9cbbf-f581-4f0c-8325-d4b84c0778f8", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [920, 1040], "parameters": {"color": 5, "height": 80, "content": "## Scenario 1"}, "typeVersion": 1}, {"id": "a92ff422-3440-48ea-8311-533315e74d07", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [1380, 1400], "parameters": {"width": 167.86310443717323, "height": 80, "content": "**Replace prompt and JSON schema**"}, "typeVersion": 1}, {"id": "329b3b74-1bab-4cea-ab71-43e94b008f3d", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [1860, 1020], "parameters": {"color": 3, "width": 224.61210598590444, "height": 92.99888586957334, "content": "**If you change speaker_labels to false it will require change user message in OpenAI and Supabase nodes**"}, "typeVersion": 1}, {"id": "04275f30-23e3-4c28-afe7-07e8c1c2455c", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [2160, 1400], "parameters": {"width": 167.86310443717323, "height": 80, "content": "**Replace Supabase connection**"}, "typeVersion": 1}], "pinData": {"Webhook": [{"body": {"status": "completed", "transcript_id": "44a669c1-2586-47c3-9c5c-259bb9942b90"}, "query": {}, "params": {}, "headers": {}, "webhookUrl": "", "executionMode": "production"}], "Set vars": [{"prompt": "Analyze this call with a focus on sales performance and potential upselling opportunities. Use the following metrics to evaluate the effectiveness of the interaction from a sales perspective. Be specific in identifying areas of strength and those needing improvement, and provide actionable insights for future calls.\\r\\n\\r\\nClient Intent: Identify the client\\u2019s primary intent and motivation for engaging in the call. Is there a clear indication of their needs or desired outcomes? How well does the agent align with or address these intentions?\\r\\n\\r\\nInterest Score (0-100): Evaluate the level of genuine interest shown by the client based on their responses, tone, and any probing questions they asked. Assign a score and explain the reasoning behind it.\\r\\n\\r\\nService Presentation Score (0-100): Assess how effectively the agent presented the service options. Did they clarify benefits that align with the client\\u2019s needs? Rate the agent\\u2019s performance in this area and provide a brief explanation.\\r\\n\\r\\nUpsell Opportunity Identified: Indicate whether any upselling opportunities were identified (e.g., suggesting advanced programs, certifications, or additional services). If opportunities were missed, explain how they could have been approached.\\r\\n\\r\\nObjection Handling Score (0-100): If the client expressed objections or hesitations, analyze how effectively the agent addressed them. Did they provide relevant solutions or reassurances? Assign a score and justify it with specific examples from the call.\\r\\n\\r\\nConversion Probability (0-100): Based on the client\\u2019s interest level and engagement, estimate the likelihood of conversion. What signals from the client suggest a high or low probability of moving forward?\\r\\n\\r\\nCall Outcome: Summarize the outcome of the call (e.g., \\u201Cclient expressed strong interest,\\u201D \\u201Cclient requested more information,\\u201D or \\u201Cclient expressed no interest\\u201D). Was a clear next step established?\\r\\n\\r\\nKey Client Needs: Highlight the client\\u2019s specific needs, preferences, or pain points mentioned during the call. How could these be addressed in future interactions to improve alignment with the client\\u2019s goals?\\r\\n\\r\\nInsights for Agent Improvement: Provide constructive feedback for the agent\\u2019s performance, focusing on areas such as empathy, product knowledge, and upsell strategy. Suggest improvements that could enhance their approach to increase client engagement.\\r\\n\\r\\nNext Steps Recommended: Recommend specific follow-up actions, such as providing customized information, sending relevant resources, or setting up a consultation. Focus on actions that will nurture the client relationship and increase conversion chances.\\r\\n\\r\\nIn your analysis, emphasize clarity and actionable feedback that leverages sales best practices. Highlight how well the agent built rapport, presented the product, and worked towards a favorable outcome for both the client and company", "json_schema": {"name": "sales_call_analysis", "schema": {"type": "object", "$schema": "http://json-schema.org/draft-04/schema#", "required": ["client_intent", "interest_score", "service_presentation_score", "upsell_opportunity_identified", "objection_handling_score", "conversion_probability", "call_outcome", "key_client_needs", "insights_for_agent_improvement", "next_steps_recommended"], "properties": {"call_outcome": {"type": "string", "description": "Outcome of the call (e.g., 'interest confirmed', 'no interest', 'appointment set')"}, "client_intent": {"type": "string", "description": "Client's primary intent for engaging (e.g., 'interest in education', 'general inquiry')"}, "interest_score": {"type": "integer", "description": "Score indicating client's interest level based on responses (0-100)"}, "key_client_needs": {"type": "string", "description": "Identified needs or requirements of the client, useful for customization or follow-up"}, "conversion_probability": {"type": "integer", "description": "Estimated likelihood of conversion based on call interaction (0-100)"}, "next_steps_recommended": {"type": "string", "description": "Suggested follow-up actions to improve conversion potential"}, "objection_handling_score": {"type": "integer", "description": "Score for handling client objections (0-100)"}, "service_presentation_score": {"type": "integer", "description": "Score for how effectively the agent presented the educational opportunities (0-100)"}, "upsell_opportunity_identified": {"type": "boolean", "description": "Indicates if any upsell opportunity was identified"}, "insights_for_agent_improvement": {"type": "string", "description": "Insights and tips for the agent to improve performance"}}, "additionalProperties": false}, "strict": true, "description": "Analysis of customer engagement, interest, and potential sales conversion based on call data"}}], "Create record": [{"id": "6e406951-c9fc-4cdb-89ca-369aad59744d", "call_outcome": "interest confirmed", "date_created": "2024-10-30T12:21:08.909539+00:00", "client_intent": "Interest in computer engineering degree", "interest_score": 75, "key_client_needs": "Interest in computer engineering as a career switch, preference for on-campus learning but open to online, current GED holder with plumbing experience", "conversion_probability": 70, "next_steps_recommended": "Arrange for a follow-up call with a specific school counselor who can provide detailed program information and potential financial aid options relevant to the client's situation. Sending personalized emails with program highlights that match <PERSON>’s interest in computer engineering can also increase engagement and conversion likelihood.", "objection_handling_score": 80, "service_presentation_score": 60, "upsell_opportunity_identified": false, "insights_for_agent_improvement": "The agent should focus on showcasing specific schools or programs during the call to better align with the client's goals. While basic qualification and interest data were collected, more detailed discussions about potential school options or educational paths could be introduced earlier to increase engagement. Additionally, identifying and promoting potential certifications or advanced degrees beyond the associate level could capitalize on upselling opportunities."}], "Set link to audio": [{"url": "https://cflobdhpqwnoisuctsoc.supabase.co/storage/v1/object/public/5minai/OUTBOUNDSAMPLE_01.mp3"}], "OpenAI - Analyze call": [{"id": "chatcmpl-AO2cPpBA0jbTapqlHD0WlRpmafwz0", "model": "gpt-4o-2024-08-06", "usage": {"total_tokens": 2130, "prompt_tokens": 1941, "completion_tokens": 189, "prompt_tokens_details": {"cached_tokens": 0}, "completion_tokens_details": {"reasoning_tokens": 0}}, "object": "chat.completion", "choices": [{"index": 0, "message": {"role": "assistant", "content": "{\"client_intent\":\"Interest in earning a computer engineering degree\",\"interest_score\":85,\"service_presentation_score\":70,\"upsell_opportunity_identified\":true,\"objection_handling_score\":75,\"conversion_probability\":80,\"call_outcome\":\"Interest confirmed, details verified\",\"key_client_needs\":\"Desire for campus-based education, not satisfied with current plumbing career, seeking entry into computer engineering\",\"insights_for_agent_improvement\":\"The agent should improve in areas such as probing to understand more about the client's career transition motivations and discussing potential career paths with a degree in computer engineering. Engaging more with the client's aspirations could open up upselling opportunities for higher degrees in the future.\",\"next_steps_recommended\":\"Agent should ensure a follow-up from a counselor who can discuss specific computer engineering programs, entry requirements, and potential career paths. Additionally, providing information on associations and networking opportunities within the field of computer engineering could benefit <PERSON>.\"}", "refusal": null}, "logprobs": null, "finish_reason": "stop"}], "created": 1730293301, "system_fingerprint": "fp_90354628f2"}], "AssemblyAI - Transcribe": [{"id": "2208fe53-c9ef-4529-a6de-3d33138072dd", "text": null, "words": null, "status": "queued", "topics": [], "summary": null, "chapters": null, "entities": null, "audio_url": "https://cflobdhpqwnoisuctsoc.supabase.co/storage/v1/object/public/5minai/OUTBOUNDSAMPLE_01.mp3", "punctuate": true, "throttled": false, "confidence": null, "is_deleted": null, "redact_pii": false, "utterances": null, "word_boost": [], "boost_param": null, "format_text": true, "speed_boost": false, "webhook_url": "https://n8n.lowcoding.dev/webhook/d1e5fdd0-b51d-4447-8af3-6754017d240b", "audio_end_at": null, "disfluencies": false, "dual_channel": false, "multichannel": false, "speech_model": null, "summary_type": null, "webhook_auth": false, "auto_chapters": false, "custom_topics": false, "language_code": "en_us", "summarization": false, "summary_model": null, "acoustic_model": "assemblyai_default", "audio_duration": null, "content_safety": false, "iab_categories": false, "language_model": "assemblyai_default", "redact_pii_sub": null, "speaker_labels": true, "auto_highlights": false, "custom_spelling": null, "audio_start_from": null, "entity_detection": false, "filter_profanity": false, "redact_pii_audio": false, "speech_threshold": null, "speakers_expected": 2, "language_detection": false, "sentiment_analysis": false, "language_confidence": null, "redact_pii_policies": null, "webhook_status_code": null, "content_safety_labels": {}, "custom_topics_results": null, "iab_categories_result": {}, "auto_highlights_result": null, "redact_pii_audio_quality": null, "webhook_auth_header_name": null, "sentiment_analysis_results": null, "language_confidence_threshold": null}], "AssemblyAI - Get transcription": [{"id": "2208fe53-c9ef-4529-a6de-3d33138072dd", "text": "Hello. May I, please, <PERSON>. Hello, this is <PERSON>. Hi, <PERSON>. My name is <PERSON>, and I'm calling on behalf of education experts from a quality monitor line, and here that you recently filled the form on the Internet indicating an interest in earning a degree. Yes. Correct. Yes. I only need a few moments of her time to mention the most appropriate schools. Are you at least 18 years of age? Yeah, I'm 29. 29. Okay. And do you currently have a high school diploma or a ged? Yes, I do. Okay, thank you, <PERSON>. And if we can find a school for you that meets your needs, would you be interested in furthering your education in the next six months? Yeah, of course. The course I'd like to take up would be computer engineering. Computer engineering. Okay. And, <PERSON>, I only need a few moments of your time, okay, to verify your information. Your first name is <PERSON>, and your last name is <PERSON>. Is this correct? Yes. Okay, <PERSON>, now, if I may ask, if we can find school for you that meets your needs, would you be interested in furthering your education? Yeah, in the next six months? Definitely. Thank you, <PERSON>. Okay, could you please verify your complete address, including the city, state, and the zip code? All right, it's 1905 Bramblewood Drive, St. Cloud, Florida, 34769. Okay. Is the street number 1905? Yeah, 1905. And the street name is Ramblewood. Right. Is that correct? That's correct. Okay. Okay, so it's 1905 Bramblewood Drive, St. Cloud, Florida, 34769. Yes. Correct. Yep. Okay, and could you please verify your email address? It'<NAME_EMAIL>. Thank you so much for the verification. Yeah. Now, you mentioned computer engineering, right? Mm. May I ask, what degree type were you looking to obtain? Is it associate or whatever I need to do? If I get in the first door, I do associate. Probably move my way up the ladder, you know? Okay, so get my first one and then keep on going. Okay, so would be associate degree for the moment? Yep. Okay. <PERSON>, you've mentioned that you're 21. I'm sorry, 29 years old now, if I may ask. Well, what's the highest level of education? I dropped out when I was in the 11th grade, and I started doing plumbing. I've actually been doing plumbing for 13 years. I'm a registered apprentice. I actually only have to take the test to become a journeyman's. A journeyman to open my own company. And I'm not too fond of plumbing, you know, saying I need something, I want to do something else besides plumbing. For the rest of my life. Okay. And do you have a diploma or a GED? I have a GED. GED. Okay. And what year did you obtain your GED? 1999. Okay, and for your class type reference, would it be online, on campus or. No, probably campus. Probably would be campus. Campus. Okay. Just in case we'll not be able to find a campus based school, would you be okay with an online school? Yeah. Okay. And are you a United States citizen? Yes, I am. Thank you. And are you associated with the United States military? What was that? I'm sorry? Are you associated with the United States military? No, I'm not. Okay. And what would be the best time for a school enrollment counselor to contact you in the morning, afternoon, or evening? Pretty much any time of the day. And what is your exact date of birth? 10, 1580. So that would be October 15th, 1980? That's correct. Okay. Okay, Anthony, if we can find school for you that meets your needs, school enrollment counselors will be contacting you in the near future, either by phone or by email, and they can answer any questions you may have regarding financial aid, which assistance, their program requirements and policies. And so with that, I would just like to thank you for your time. Okay. Once again, we thank you for choosing education experts. And thank you. You're welcome. All right, you too. Bye.", "words": [{"end": 1736, "text": "Hello.", "start": 1480, "speaker": "A", "confidence": 0.94271}, {"end": 1872, "text": "May", "start": 1736, "speaker": "A", "confidence": 0.6092}, {"end": 1944, "text": "I,", "start": 1872, "speaker": "A", "confidence": 0.81608}, {"end": 2400, "text": "please,", "start": 1944, "speaker": "A", "confidence": 0.98526}, {"end": 3113, "text": "<PERSON>.", "start": 2489, "speaker": "A", "confidence": 0.79453}, {"end": 4025, "text": "Hello,", "start": 3249, "speaker": "B", "confidence": 0.3434}, {"end": 4353, "text": "this", "start": 4145, "speaker": "B", "confidence": 0.99092}, {"end": 4521, "text": "is", "start": 4369, "speaker": "B", "confidence": 0.9984}, {"end": 5165, "text": "<PERSON>.", "start": 4553, "speaker": "B", "confidence": 0.5279}, {"end": 6065, "text": "Hi,", "start": 5665, "speaker": "A", "confidence": 0.98595}, {"end": 6553, "text": "<PERSON>.", "start": 6105, "speaker": "A", "confidence": 0.95117}, {"end": 6833, "text": "My", "start": 6649, "speaker": "A", "confidence": 0.98171}, {"end": 6953, "text": "name", "start": 6849, "speaker": "A", "confidence": 0.99951}, {"end": 7121, "text": "is", "start": 6969, "speaker": "A", "confidence": 0.97658}, {"end": 7449, "text": "<PERSON>,", "start": 7153, "speaker": "A", "confidence": 0.47833}, {"end": 7633, "text": "and", "start": 7497, "speaker": "A", "confidence": 0.9914}, {"end": 7777, "text": "I'm", "start": 7649, "speaker": "A", "confidence": 0.91787}, {"end": 8121, "text": "calling", "start": 7801, "speaker": "A", "confidence": 0.65072}, {"end": 8345, "text": "on", "start": 8153, "speaker": "A", "confidence": 0.99949}, {"end": 8745, "text": "behalf", "start": 8385, "speaker": "A", "confidence": 0.70579}, {"end": 9033, "text": "of", "start": 8785, "speaker": "A", "confidence": 0.99746}, {"end": 9593, "text": "education", "start": 9089, "speaker": "A", "confidence": 0.999}, {"end": 10233, "text": "experts", "start": 9729, "speaker": "A", "confidence": 0.99709}, {"end": 10457, "text": "from", "start": 10289, "speaker": "A", "confidence": 0.96937}, {"end": 10593, "text": "a", "start": 10481, "speaker": "A", "confidence": 0.72221}, {"end": 10929, "text": "quality", "start": 10609, "speaker": "A", "confidence": 0.9818}, {"end": 11361, "text": "monitor", "start": 10977, "speaker": "A", "confidence": 0.56758}, {"end": 12001, "text": "line,", "start": 11433, "speaker": "A", "confidence": 0.54956}, {"end": 12705, "text": "and", "start": 12153, "speaker": "A", "confidence": 0.57481}, {"end": 13225, "text": "here", "start": 12825, "speaker": "A", "confidence": 0.74568}, {"end": 13545, "text": "that", "start": 13305, "speaker": "A", "confidence": 0.73898}, {"end": 13737, "text": "you", "start": 13585, "speaker": "A", "confidence": 0.38875}, {"end": 14041, "text": "recently", "start": 13761, "speaker": "A", "confidence": 0.99362}, {"end": 14361, "text": "filled", "start": 14113, "speaker": "A", "confidence": 0.98643}, {"end": 14585, "text": "the", "start": 14393, "speaker": "A", "confidence": 0.71449}, {"end": 14897, "text": "form", "start": 14625, "speaker": "A", "confidence": 0.9993}, {"end": 15137, "text": "on", "start": 14961, "speaker": "A", "confidence": 0.98255}, {"end": 15321, "text": "the", "start": 15161, "speaker": "A", "confidence": 0.95742}, {"end": 15953, "text": "Internet", "start": 15353, "speaker": "A", "confidence": 0.95742}, {"end": 16569, "text": "indicating", "start": 16089, "speaker": "A", "confidence": 0.95638}, {"end": 16777, "text": "an", "start": 16617, "speaker": "A", "confidence": 0.98952}, {"end": 17057, "text": "interest", "start": 16801, "speaker": "A", "confidence": 0.97961}, {"end": 17297, "text": "in", "start": 17121, "speaker": "A", "confidence": 0.9872}, {"end": 17505, "text": "earning", "start": 17321, "speaker": "A", "confidence": 0.93147}, {"end": 17697, "text": "a", "start": 17545, "speaker": "A", "confidence": 0.99152}, {"end": 18385, "text": "degree.", "start": 17721, "speaker": "A", "confidence": 0.9097}, {"end": 18937, "text": "Yes.", "start": 18545, "speaker": "B", "confidence": 0.54822}, {"end": 19625, "text": "Correct.", "start": 19001, "speaker": "A", "confidence": 0.61381}, {"end": 20525, "text": "Yes.", "start": 19785, "speaker": "B", "confidence": 0.5177}, {"end": 21657, "text": "I", "start": 21345, "speaker": "A", "confidence": 0.99611}, {"end": 21841, "text": "only", "start": 21681, "speaker": "A", "confidence": 0.99857}, {"end": 22041, "text": "need", "start": 21873, "speaker": "A", "confidence": 0.99871}, {"end": 22169, "text": "a", "start": 22073, "speaker": "A", "confidence": 0.99842}, {"end": 22297, "text": "few", "start": 22177, "speaker": "A", "confidence": 0.99964}, {"end": 22545, "text": "moments", "start": 22321, "speaker": "A", "confidence": 0.94492}, {"end": 22737, "text": "of", "start": 22585, "speaker": "A", "confidence": 0.88045}, {"end": 22921, "text": "her", "start": 22761, "speaker": "A", "confidence": 0.52082}, {"end": 23169, "text": "time", "start": 22953, "speaker": "A", "confidence": 0.99957}, {"end": 23401, "text": "to", "start": 23217, "speaker": "A", "confidence": 0.97503}, {"end": 23897, "text": "mention", "start": 23433, "speaker": "A", "confidence": 0.56777}, {"end": 24193, "text": "the", "start": 24001, "speaker": "A", "confidence": 0.65662}, {"end": 24409, "text": "most", "start": 24209, "speaker": "A", "confidence": 0.99193}, {"end": 24961, "text": "appropriate", "start": 24457, "speaker": "A", "confidence": 0.78553}, {"end": 25649, "text": "schools.", "start": 25033, "speaker": "A", "confidence": 0.98905}, {"end": 25993, "text": "Are", "start": 25777, "speaker": "A", "confidence": 0.93187}, {"end": 26137, "text": "you", "start": 26009, "speaker": "A", "confidence": 0.99905}, {"end": 26297, "text": "at", "start": 26161, "speaker": "A", "confidence": 0.99623}, {"end": 26601, "text": "least", "start": 26321, "speaker": "A", "confidence": 0.99414}, {"end": 27127, "text": "18", "start": 26673, "speaker": "A", "confidence": 0.95011}, {"end": 27632, "text": "years", "start": 27228, "speaker": "A", "confidence": 0.95011}, {"end": 28137, "text": "of", "start": 27733, "speaker": "A", "confidence": 0.95011}, {"end": 28642, "text": "age?", "start": 28238, "speaker": "A", "confidence": 0.95011}, {"end": 29147, "text": "Yeah,", "start": 28743, "speaker": "B", "confidence": 0.95011}, {"end": 29652, "text": "I'm", "start": 29248, "speaker": "B", "confidence": 0.95011}, {"end": 30157, "text": "29.", "start": 29753, "speaker": "B", "confidence": 0.95011}, {"end": 30713, "text": "29.", "start": 30258, "speaker": "A", "confidence": 0.95011}, {"end": 31001, "text": "Okay.", "start": 30769, "speaker": "B", "confidence": 0.68301}, {"end": 31153, "text": "And", "start": 31033, "speaker": "A", "confidence": 0.91268}, {"end": 31249, "text": "do", "start": 31169, "speaker": "A", "confidence": 0.75558}, {"end": 31401, "text": "you", "start": 31257, "speaker": "A", "confidence": 0.99112}, {"end": 31673, "text": "currently", "start": 31433, "speaker": "A", "confidence": 0.99463}, {"end": 31897, "text": "have", "start": 31729, "speaker": "A", "confidence": 0.9993}, {"end": 32033, "text": "a", "start": 31921, "speaker": "A", "confidence": 0.98565}, {"end": 32177, "text": "high", "start": 32049, "speaker": "A", "confidence": 0.99845}, {"end": 32385, "text": "school", "start": 32201, "speaker": "A", "confidence": 0.99979}, {"end": 32817, "text": "diploma", "start": 32425, "speaker": "A", "confidence": 0.86589}, {"end": 33081, "text": "or", "start": 32881, "speaker": "A", "confidence": 0.99886}, {"end": 33257, "text": "a", "start": 33113, "speaker": "A", "confidence": 0.97091}, {"end": 33961, "text": "ged?", "start": 33281, "speaker": "A", "confidence": 0.95546}, {"end": 34377, "text": "Yes,", "start": 34113, "speaker": "B", "confidence": 0.9898}, {"end": 34513, "text": "I", "start": 34401, "speaker": "B", "confidence": 0.99684}, {"end": 35085, "text": "do.", "start": 34529, "speaker": "B", "confidence": 0.99955}, {"end": 36505, "text": "Okay,", "start": 36105, "speaker": "A", "confidence": 0.839}, {"end": 36673, "text": "thank", "start": 36545, "speaker": "A", "confidence": 0.96607}, {"end": 36817, "text": "you,", "start": 36689, "speaker": "A", "confidence": 0.99926}, {"end": 37161, "text": "<PERSON>.", "start": 36841, "speaker": "A", "confidence": 0.59546}, {"end": 37441, "text": "And", "start": 37233, "speaker": "A", "confidence": 0.9426}, {"end": 37593, "text": "if", "start": 37473, "speaker": "A", "confidence": 0.99034}, {"end": 37713, "text": "we", "start": 37609, "speaker": "A", "confidence": 0.99775}, {"end": 37881, "text": "can", "start": 37729, "speaker": "A", "confidence": 0.98007}, {"end": 38081, "text": "find", "start": 37913, "speaker": "A", "confidence": 0.99483}, {"end": 38233, "text": "a", "start": 38113, "speaker": "A", "confidence": 0.95744}, {"end": 38425, "text": "school", "start": 38249, "speaker": "A", "confidence": 0.99911}, {"end": 38617, "text": "for", "start": 38465, "speaker": "A", "confidence": 0.99705}, {"end": 38825, "text": "you", "start": 38641, "speaker": "A", "confidence": 0.99922}, {"end": 39041, "text": "that", "start": 38865, "speaker": "A", "confidence": 0.99841}, {"end": 39297, "text": "meets", "start": 39073, "speaker": "A", "confidence": 0.9695}, {"end": 39481, "text": "your", "start": 39321, "speaker": "A", "confidence": 0.99707}, {"end": 39993, "text": "needs,", "start": 39513, "speaker": "A", "confidence": 0.99941}, {"end": 40353, "text": "would", "start": 40129, "speaker": "A", "confidence": 0.99802}, {"end": 40497, "text": "you", "start": 40369, "speaker": "A", "confidence": 0.99752}, {"end": 40657, "text": "be", "start": 40521, "speaker": "A", "confidence": 0.99856}, {"end": 41001, "text": "interested", "start": 40681, "speaker": "A", "confidence": 0.99824}, {"end": 41201, "text": "in", "start": 41033, "speaker": "A", "confidence": 0.99511}, {"end": 41577, "text": "furthering", "start": 41233, "speaker": "A", "confidence": 0.9905}, {"end": 41857, "text": "your", "start": 41601, "speaker": "A", "confidence": 0.99821}, {"end": 42337, "text": "education", "start": 41921, "speaker": "A", "confidence": 0.99982}, {"end": 42633, "text": "in", "start": 42441, "speaker": "A", "confidence": 0.94603}, {"end": 42753, "text": "the", "start": 42649, "speaker": "A", "confidence": 0.99584}, {"end": 42897, "text": "next", "start": 42769, "speaker": "A", "confidence": 0.99827}, {"end": 43057, "text": "six", "start": 42921, "speaker": "A", "confidence": 0.98351}, {"end": 43241, "text": "months?", "start": 43081, "speaker": "A", "confidence": 0.82268}, {"end": 43417, "text": "Yeah,", "start": 43273, "speaker": "A", "confidence": 0.57062}, {"end": 43553, "text": "of", "start": 43441, "speaker": "A", "confidence": 0.99322}, {"end": 44153, "text": "course.", "start": 43569, "speaker": "A", "confidence": 0.99982}, {"end": 44617, "text": "The", "start": 44329, "speaker": "B", "confidence": 0.99793}, {"end": 44801, "text": "course", "start": 44641, "speaker": "B", "confidence": 0.9996}, {"end": 45033, "text": "I'd", "start": 44833, "speaker": "B", "confidence": 0.92853}, {"end": 45153, "text": "like", "start": 45049, "speaker": "B", "confidence": 0.99828}, {"end": 45297, "text": "to", "start": 45169, "speaker": "B", "confidence": 0.99628}, {"end": 45457, "text": "take", "start": 45321, "speaker": "B", "confidence": 0.99598}, {"end": 45641, "text": "up", "start": 45481, "speaker": "B", "confidence": 0.99608}, {"end": 45793, "text": "would", "start": 45673, "speaker": "B", "confidence": 0.97223}, {"end": 45985, "text": "be", "start": 45809, "speaker": "B", "confidence": 0.99686}, {"end": 46449, "text": "computer", "start": 46025, "speaker": "B", "confidence": 0.99806}, {"end": 47205, "text": "engineering.", "start": 46497, "speaker": "B", "confidence": 0.57812}, {"end": 48265, "text": "Computer", "start": 47705, "speaker": "A", "confidence": 0.82846}, {"end": 48697, "text": "engineering.", "start": 48305, "speaker": "A", "confidence": 0.82065}, {"end": 49405, "text": "Okay.", "start": 48761, "speaker": "A", "confidence": 0.80027}, {"end": 51129, "text": "And,", "start": 50745, "speaker": "A", "confidence": 0.88487}, {"end": 51845, "text": "<PERSON>,", "start": 51177, "speaker": "A", "confidence": 0.85582}, {"end": 54077, "text": "I", "start": 53765, "speaker": "A", "confidence": 0.95493}, {"end": 54261, "text": "only", "start": 54101, "speaker": "A", "confidence": 0.98905}, {"end": 54461, "text": "need", "start": 54293, "speaker": "A", "confidence": 0.78539}, {"end": 54613, "text": "a", "start": 54493, "speaker": "A", "confidence": 0.94105}, {"end": 54757, "text": "few", "start": 54629, "speaker": "A", "confidence": 0.99921}, {"end": 55029, "text": "moments", "start": 54781, "speaker": "A", "confidence": 0.94268}, {"end": 55237, "text": "of", "start": 55077, "speaker": "A", "confidence": 0.95695}, {"end": 55469, "text": "your", "start": 55261, "speaker": "A", "confidence": 0.96808}, {"end": 56085, "text": "time,", "start": 55517, "speaker": "A", "confidence": 0.99949}, {"end": 56985, "text": "okay,", "start": 56245, "speaker": "A", "confidence": 0.75219}, {"end": 58077, "text": "to", "start": 57765, "speaker": "A", "confidence": 0.99411}, {"end": 58485, "text": "verify", "start": 58101, "speaker": "A", "confidence": 0.99141}, {"end": 58797, "text": "your", "start": 58525, "speaker": "A", "confidence": 0.995}, {"end": 59277, "text": "information.", "start": 58861, "speaker": "A", "confidence": 0.97821}, {"end": 59597, "text": "Your", "start": 59381, "speaker": "A", "confidence": 0.99356}, {"end": 59829, "text": "first", "start": 59621, "speaker": "A", "confidence": 0.99886}, {"end": 60037, "text": "name", "start": 59877, "speaker": "A", "confidence": 0.99846}, {"end": 60221, "text": "is", "start": 60061, "speaker": "A", "confidence": 0.83584}, {"end": 60645, "text": "<PERSON>,", "start": 60253, "speaker": "A", "confidence": 0.48309}, {"end": 60941, "text": "and", "start": 60725, "speaker": "A", "confidence": 0.99}, {"end": 61141, "text": "your", "start": 60973, "speaker": "A", "confidence": 0.99364}, {"end": 61317, "text": "last", "start": 61173, "speaker": "A", "confidence": 0.98439}, {"end": 61429, "text": "name", "start": 61341, "speaker": "A", "confidence": 0.99703}, {"end": 61581, "text": "is", "start": 61437, "speaker": "A", "confidence": 0.97459}, {"end": 61997, "text": "<PERSON>.", "start": 61613, "speaker": "A", "confidence": 0.52605}, {"end": 62133, "text": "Is", "start": 62021, "speaker": "A", "confidence": 0.92666}, {"end": 62373, "text": "this", "start": 62149, "speaker": "A", "confidence": 0.79455}, {"end": 63005, "text": "correct?", "start": 62429, "speaker": "A", "confidence": 0.58959}, {"end": 63905, "text": "Yes.", "start": 63165, "speaker": "B", "confidence": 0.99415}, {"end": 65461, "text": "Okay,", "start": 64765, "speaker": "A", "confidence": 0.80903}, {"end": 66101, "text": "<PERSON>,", "start": 65573, "speaker": "A", "confidence": 0.56535}, {"end": 66437, "text": "now,", "start": 66213, "speaker": "A", "confidence": 0.9032}, {"end": 66573, "text": "if", "start": 66461, "speaker": "A", "confidence": 0.99703}, {"end": 66693, "text": "I", "start": 66589, "speaker": "A", "confidence": 0.9876}, {"end": 66861, "text": "may", "start": 66709, "speaker": "A", "confidence": 0.99084}, {"end": 67133, "text": "ask,", "start": 66893, "speaker": "A", "confidence": 0.99027}, {"end": 67309, "text": "if", "start": 67189, "speaker": "A", "confidence": 0.98722}, {"end": 67413, "text": "we", "start": 67317, "speaker": "A", "confidence": 0.99812}, {"end": 67533, "text": "can", "start": 67429, "speaker": "A", "confidence": 0.998}, {"end": 67773, "text": "find", "start": 67549, "speaker": "A", "confidence": 0.6915}, {"end": 68021, "text": "school", "start": 67829, "speaker": "A", "confidence": 0.99117}, {"end": 68149, "text": "for", "start": 68053, "speaker": "A", "confidence": 0.99587}, {"end": 68301, "text": "you", "start": 68157, "speaker": "A", "confidence": 0.99587}, {"end": 68477, "text": "that", "start": 68333, "speaker": "A", "confidence": 0.99746}, {"end": 68693, "text": "meets", "start": 68501, "speaker": "A", "confidence": 0.60683}, {"end": 68837, "text": "your", "start": 68709, "speaker": "A", "confidence": 0.98079}, {"end": 69117, "text": "needs,", "start": 68861, "speaker": "A", "confidence": 0.99726}, {"end": 69309, "text": "would", "start": 69181, "speaker": "A", "confidence": 0.99722}, {"end": 69437, "text": "you", "start": 69317, "speaker": "A", "confidence": 0.99738}, {"end": 69573, "text": "be", "start": 69461, "speaker": "A", "confidence": 0.99653}, {"end": 69837, "text": "interested", "start": 69589, "speaker": "A", "confidence": 0.99916}, {"end": 69997, "text": "in", "start": 69861, "speaker": "A", "confidence": 0.98841}, {"end": 70421, "text": "furthering", "start": 70021, "speaker": "A", "confidence": 0.9476}, {"end": 70645, "text": "your", "start": 70453, "speaker": "A", "confidence": 0.97724}, {"end": 71265, "text": "education?", "start": 70685, "speaker": "A", "confidence": 0.99981}, {"end": 72021, "text": "Yeah,", "start": 71685, "speaker": "A", "confidence": 0.22025}, {"end": 72173, "text": "in", "start": 72053, "speaker": "A", "confidence": 0.8425}, {"end": 72293, "text": "the", "start": 72189, "speaker": "A", "confidence": 0.90172}, {"end": 72461, "text": "next", "start": 72309, "speaker": "A", "confidence": 0.99852}, {"end": 72613, "text": "six", "start": 72493, "speaker": "A", "confidence": 0.97384}, {"end": 73185, "text": "months?", "start": 72629, "speaker": "A", "confidence": 0.78673}, {"end": 74205, "text": "Definitely.", "start": 73485, "speaker": "B", "confidence": 0.48811}, {"end": 74533, "text": "Thank", "start": 74325, "speaker": "A", "confidence": 0.94507}, {"end": 74677, "text": "you,", "start": 74549, "speaker": "A", "confidence": 0.99923}, {"end": 75345, "text": "<PERSON>.", "start": 74701, "speaker": "A", "confidence": 0.69785}, {"end": 76525, "text": "Okay,", "start": 75725, "speaker": "A", "confidence": 0.65007}, {"end": 76933, "text": "could", "start": 76685, "speaker": "A", "confidence": 0.99445}, {"end": 77053, "text": "you", "start": 76949, "speaker": "A", "confidence": 0.99718}, {"end": 77221, "text": "please", "start": 77069, "speaker": "A", "confidence": 0.99617}, {"end": 77685, "text": "verify", "start": 77253, "speaker": "A", "confidence": 0.57569}, {"end": 77901, "text": "your", "start": 77725, "speaker": "A", "confidence": 0.89864}, {"end": 78459, "text": "complete", "start": 77933, "speaker": "A", "confidence": 0.64758}, {"end": 79055, "text": "address,", "start": 78557, "speaker": "A", "confidence": 0.87938}, {"end": 79503, "text": "including", "start": 79175, "speaker": "A", "confidence": 0.98521}, {"end": 79751, "text": "the", "start": 79559, "speaker": "A", "confidence": 0.99351}, {"end": 80239, "text": "city,", "start": 79783, "speaker": "A", "confidence": 0.9994}, {"end": 80679, "text": "state,", "start": 80367, "speaker": "A", "confidence": 0.9963}, {"end": 80863, "text": "and", "start": 80727, "speaker": "A", "confidence": 0.99894}, {"end": 80983, "text": "the", "start": 80879, "speaker": "A", "confidence": 0.96519}, {"end": 81167, "text": "zip", "start": 80999, "speaker": "A", "confidence": 0.75273}, {"end": 81815, "text": "code?", "start": 81191, "speaker": "A", "confidence": 0.67151}, {"end": 82095, "text": "All", "start": 81935, "speaker": "B", "confidence": 0.86469}, {"end": 82255, "text": "right,", "start": 82095, "speaker": "B", "confidence": 0.56205}, {"end": 82583, "text": "it's", "start": 82295, "speaker": "B", "confidence": 0.99176}, {"end": 83819, "text": "1905", "start": 82639, "speaker": "B", "confidence": 0.88977}, {"end": 85131, "text": "Bramblewood", "start": 84081, "speaker": "B", "confidence": 0.88977}, {"end": 86443, "text": "Drive,", "start": 85393, "speaker": "B", "confidence": 0.88977}, {"end": 87754, "text": "St.", "start": 86705, "speaker": "B", "confidence": 0.88977}, {"end": 89066, "text": "Cloud,", "start": 88017, "speaker": "B", "confidence": 0.88977}, {"end": 90378, "text": "Florida,", "start": 89328, "speaker": "B", "confidence": 0.88977}, {"end": 91689, "text": "34769.", "start": 90640, "speaker": "B", "confidence": 0.88977}, {"end": 93001, "text": "Okay.", "start": 91952, "speaker": "B", "confidence": 0.88977}, {"end": 94313, "text": "Is", "start": 93264, "speaker": "A", "confidence": 0.88977}, {"end": 95625, "text": "the", "start": 94575, "speaker": "A", "confidence": 0.88977}, {"end": 96936, "text": "street", "start": 95887, "speaker": "A", "confidence": 0.88977}, {"end": 98248, "text": "number", "start": 97199, "speaker": "A", "confidence": 0.88977}, {"end": 99560, "text": "1905?", "start": 98510, "speaker": "A", "confidence": 0.88977}, {"end": 100872, "text": "Yeah,", "start": 99822, "speaker": "B", "confidence": 0.88977}, {"end": 102315, "text": "1905.", "start": 101134, "speaker": "B", "confidence": 0.88977}, {"end": 103087, "text": "And", "start": 102775, "speaker": "A", "confidence": 0.99815}, {"end": 103199, "text": "the", "start": 103111, "speaker": "A", "confidence": 0.97988}, {"end": 103399, "text": "street", "start": 103207, "speaker": "A", "confidence": 0.60564}, {"end": 103655, "text": "name", "start": 103447, "speaker": "A", "confidence": 0.55876}, {"end": 103925, "text": "is", "start": 103695, "speaker": "A", "confidence": 0.79552}, {"end": 104841, "text": "Ram<PERSON>wood.", "start": 103975, "speaker": "A", "confidence": 0.16168}, {"end": 105441, "text": "Right.", "start": 104953, "speaker": "A", "confidence": 0.30768}, {"end": 105753, "text": "Is", "start": 105553, "speaker": "A", "confidence": 0.95258}, {"end": 105945, "text": "that", "start": 105769, "speaker": "A", "confidence": 0.88056}, {"end": 106497, "text": "correct?", "start": 105985, "speaker": "A", "confidence": 0.9288}, {"end": 107001, "text": "That's", "start": 106641, "speaker": "B", "confidence": 0.99523}, {"end": 107249, "text": "correct.", "start": 107033, "speaker": "B", "confidence": 0.99961}, {"end": 107761, "text": "Okay.", "start": 107297, "speaker": "A", "confidence": 0.83842}, {"end": 108177, "text": "Okay,", "start": 107873, "speaker": "A", "confidence": 0.80582}, {"end": 108337, "text": "so", "start": 108201, "speaker": "A", "confidence": 0.99681}, {"end": 108569, "text": "it's", "start": 108361, "speaker": "A", "confidence": 0.82291}, {"end": 109476, "text": "1905", "start": 108617, "speaker": "A", "confidence": 0.86642}, {"end": 110432, "text": "Bramblewood", "start": 109667, "speaker": "A", "confidence": 0.86642}, {"end": 111387, "text": "Drive,", "start": 110623, "speaker": "A", "confidence": 0.86642}, {"end": 112343, "text": "St.", "start": 111578, "speaker": "A", "confidence": 0.86642}, {"end": 113298, "text": "Cloud,", "start": 112534, "speaker": "A", "confidence": 0.86642}, {"end": 114254, "text": "Florida,", "start": 113489, "speaker": "A", "confidence": 0.86642}, {"end": 115305, "text": "34769.", "start": 114445, "speaker": "A", "confidence": 0.86642}, {"end": 115825, "text": "Yes.", "start": 115465, "speaker": "A", "confidence": 0.28854}, {"end": 116417, "text": "Correct.", "start": 115865, "speaker": "A", "confidence": 0.49466}, {"end": 117065, "text": "Yep.", "start": 116561, "speaker": "B", "confidence": 0.50213}, {"end": 117729, "text": "Okay,", "start": 117145, "speaker": "A", "confidence": 0.90179}, {"end": 118097, "text": "and", "start": 117857, "speaker": "A", "confidence": 0.98822}, {"end": 118233, "text": "could", "start": 118121, "speaker": "A", "confidence": 0.99041}, {"end": 118377, "text": "you", "start": 118249, "speaker": "A", "confidence": 0.99812}, {"end": 118561, "text": "please", "start": 118401, "speaker": "A", "confidence": 0.99786}, {"end": 119025, "text": "verify", "start": 118593, "speaker": "A", "confidence": 0.64862}, {"end": 119337, "text": "your", "start": 119065, "speaker": "A", "confidence": 0.99971}, {"end": 119673, "text": "email", "start": 119401, "speaker": "A", "confidence": 0.99799}, {"end": 120325, "text": "address?", "start": 119729, "speaker": "A", "confidence": 0.9999}, {"end": 121169, "text": "It's", "start": 120625, "speaker": "B", "confidence": 0.96819}, {"end": 122049, "text": "pella", "start": 121257, "speaker": "B", "confidence": 0.51185}, {"end": 124725, "text": "<EMAIL>.", "start": 122177, "speaker": "B", "confidence": 0.72893}, {"end": 127409, "text": "Thank", "start": 127145, "speaker": "A", "confidence": 0.68775}, {"end": 127465, "text": "you", "start": 127417, "speaker": "A", "confidence": 0.80303}, {"end": 127529, "text": "so", "start": 127465, "speaker": "A", "confidence": 0.75208}, {"end": 127633, "text": "much", "start": 127537, "speaker": "A", "confidence": 0.99188}, {"end": 127753, "text": "for", "start": 127649, "speaker": "A", "confidence": 0.80284}, {"end": 127897, "text": "the", "start": 127769, "speaker": "A", "confidence": 0.97582}, {"end": 128937, "text": "verification.", "start": 127921, "speaker": "A", "confidence": 0.60764}, {"end": 129847, "text": "Yeah.", "start": 129121, "speaker": "B", "confidence": 0.43517}, {"end": 130695, "text": "Now,", "start": 130001, "speaker": "A", "confidence": 0.97821}, {"end": 131371, "text": "you", "start": 131035, "speaker": "A", "confidence": 0.9945}, {"end": 131667, "text": "mentioned", "start": 131403, "speaker": "A", "confidence": 0.98598}, {"end": 132059, "text": "computer", "start": 131691, "speaker": "A", "confidence": 0.99831}, {"end": 132595, "text": "engineering,", "start": 132107, "speaker": "A", "confidence": 0.51139}, {"end": 133107, "text": "right?", "start": 132675, "speaker": "A", "confidence": 0.99382}, {"end": 133895, "text": "Mm.", "start": 133211, "speaker": "B", "confidence": 0.33569}, {"end": 135147, "text": "May", "start": 134835, "speaker": "A", "confidence": 0.91497}, {"end": 135331, "text": "I", "start": 135171, "speaker": "A", "confidence": 0.996}, {"end": 135675, "text": "ask,", "start": 135363, "speaker": "A", "confidence": 0.9894}, {"end": 135971, "text": "what", "start": 135755, "speaker": "A", "confidence": 0.99857}, {"end": 136299, "text": "degree", "start": 136003, "speaker": "A", "confidence": 0.95628}, {"end": 136611, "text": "type", "start": 136347, "speaker": "A", "confidence": 0.99582}, {"end": 136763, "text": "were", "start": 136643, "speaker": "A", "confidence": 0.89465}, {"end": 136931, "text": "you", "start": 136779, "speaker": "A", "confidence": 0.99415}, {"end": 137131, "text": "looking", "start": 136963, "speaker": "A", "confidence": 0.9992}, {"end": 137307, "text": "to", "start": 137163, "speaker": "A", "confidence": 0.99666}, {"end": 137611, "text": "obtain?", "start": 137331, "speaker": "A", "confidence": 0.99158}, {"end": 137843, "text": "Is", "start": 137683, "speaker": "B", "confidence": 0.75595}, {"end": 138083, "text": "it", "start": 137859, "speaker": "B", "confidence": 0.92502}, {"end": 139055, "text": "associate", "start": 138139, "speaker": "B", "confidence": 0.7714}, {"end": 140211, "text": "or", "start": 139635, "speaker": "B", "confidence": 0.79261}, {"end": 140699, "text": "whatever", "start": 140323, "speaker": "B", "confidence": 0.9978}, {"end": 140907, "text": "I", "start": 140747, "speaker": "B", "confidence": 0.99664}, {"end": 141091, "text": "need", "start": 140931, "speaker": "B", "confidence": 0.99762}, {"end": 141243, "text": "to", "start": 141123, "speaker": "B", "confidence": 0.99774}, {"end": 141819, "text": "do?", "start": 141259, "speaker": "B", "confidence": 0.99666}, {"end": 142243, "text": "If", "start": 141987, "speaker": "B", "confidence": 0.99301}, {"end": 142435, "text": "I", "start": 142259, "speaker": "B", "confidence": 0.99651}, {"end": 142627, "text": "get", "start": 142475, "speaker": "B", "confidence": 0.99885}, {"end": 142811, "text": "in", "start": 142651, "speaker": "B", "confidence": 0.9592}, {"end": 142963, "text": "the", "start": 142843, "speaker": "B", "confidence": 0.99748}, {"end": 143155, "text": "first", "start": 142979, "speaker": "B", "confidence": 0.99822}, {"end": 143347, "text": "door,", "start": 143195, "speaker": "B", "confidence": 0.48133}, {"end": 143483, "text": "I", "start": 143371, "speaker": "B", "confidence": 0.9702}, {"end": 143651, "text": "do", "start": 143499, "speaker": "B", "confidence": 0.98132}, {"end": 144219, "text": "associate.", "start": 143683, "speaker": "B", "confidence": 0.49121}, {"end": 144627, "text": "Probably", "start": 144307, "speaker": "B", "confidence": 0.78938}, {"end": 144915, "text": "move", "start": 144691, "speaker": "B", "confidence": 0.95686}, {"end": 145131, "text": "my", "start": 144955, "speaker": "B", "confidence": 0.99935}, {"end": 145283, "text": "way", "start": 145163, "speaker": "B", "confidence": 0.99803}, {"end": 145403, "text": "up", "start": 145299, "speaker": "B", "confidence": 0.99131}, {"end": 145547, "text": "the", "start": 145419, "speaker": "B", "confidence": 0.97355}, {"end": 146011, "text": "ladder,", "start": 145571, "speaker": "B", "confidence": 0.82974}, {"end": 146219, "text": "you", "start": 146083, "speaker": "B", "confidence": 0.88974}, {"end": 146707, "text": "know?", "start": 146227, "speaker": "B", "confidence": 0.79785}, {"end": 147331, "text": "Okay,", "start": 146851, "speaker": "B", "confidence": 0.71177}, {"end": 147659, "text": "so", "start": 147403, "speaker": "B", "confidence": 0.85514}, {"end": 147843, "text": "get", "start": 147707, "speaker": "B", "confidence": 0.9397}, {"end": 148011, "text": "my", "start": 147859, "speaker": "B", "confidence": 0.99713}, {"end": 148211, "text": "first", "start": 148043, "speaker": "B", "confidence": 0.99974}, {"end": 148387, "text": "one", "start": 148243, "speaker": "B", "confidence": 0.99352}, {"end": 148499, "text": "and", "start": 148411, "speaker": "B", "confidence": 0.99333}, {"end": 148675, "text": "then", "start": 148507, "speaker": "B", "confidence": 0.96923}, {"end": 148867, "text": "keep", "start": 148715, "speaker": "B", "confidence": 0.99984}, {"end": 149075, "text": "on", "start": 148891, "speaker": "B", "confidence": 0.99761}, {"end": 149771, "text": "going.", "start": 149115, "speaker": "B", "confidence": 0.99938}, {"end": 150775, "text": "Okay,", "start": 149963, "speaker": "A", "confidence": 0.76853}, {"end": 152055, "text": "so", "start": 151315, "speaker": "A", "confidence": 0.98589}, {"end": 152787, "text": "would", "start": 152475, "speaker": "A", "confidence": 0.39448}, {"end": 152947, "text": "be", "start": 152811, "speaker": "A", "confidence": 0.88741}, {"end": 153683, "text": "associate", "start": 152971, "speaker": "A", "confidence": 0.7234}, {"end": 154603, "text": "degree", "start": 153819, "speaker": "A", "confidence": 0.84448}, {"end": 155043, "text": "for", "start": 154779, "speaker": "A", "confidence": 0.99192}, {"end": 155187, "text": "the", "start": 155059, "speaker": "A", "confidence": 0.99581}, {"end": 155807, "text": "moment?", "start": 155211, "speaker": "A", "confidence": 0.99793}, {"end": 156755, "text": "Yep.", "start": 155971, "speaker": "B", "confidence": 0.41607}, {"end": 159687, "text": "Okay.", "start": 159255, "speaker": "A", "confidence": 0.44256}, {"end": 159951, "text": "<PERSON>,", "start": 159711, "speaker": "A", "confidence": 0.99308}, {"end": 160143, "text": "you've", "start": 159983, "speaker": "A", "confidence": 0.71789}, {"end": 160383, "text": "mentioned", "start": 160159, "speaker": "A", "confidence": 0.76675}, {"end": 160503, "text": "that", "start": 160399, "speaker": "A", "confidence": 0.96366}, {"end": 160735, "text": "you're", "start": 160519, "speaker": "A", "confidence": 0.92629}, {"end": 161185, "text": "21.", "start": 160775, "speaker": "A", "confidence": 0.97708}, {"end": 161641, "text": "I'm", "start": 161276, "speaker": "A", "confidence": 0.97708}, {"end": 162097, "text": "sorry,", "start": 161732, "speaker": "A", "confidence": 0.97708}, {"end": 162599, "text": "29", "start": 162188, "speaker": "A", "confidence": 0.97708}, {"end": 162903, "text": "years", "start": 162647, "speaker": "A", "confidence": 0.99433}, {"end": 163559, "text": "old", "start": 162959, "speaker": "A", "confidence": 0.99948}, {"end": 164055, "text": "now,", "start": 163727, "speaker": "A", "confidence": 0.95362}, {"end": 164223, "text": "if", "start": 164095, "speaker": "A", "confidence": 0.99871}, {"end": 164367, "text": "I", "start": 164239, "speaker": "A", "confidence": 0.99784}, {"end": 164695, "text": "may", "start": 164391, "speaker": "A", "confidence": 0.99377}, {"end": 165395, "text": "ask.", "start": 164775, "speaker": "A", "confidence": 0.99392}, {"end": 167527, "text": "Well,", "start": 167215, "speaker": "A", "confidence": 0.48665}, {"end": 167727, "text": "what's", "start": 167551, "speaker": "A", "confidence": 0.51296}, {"end": 167887, "text": "the", "start": 167751, "speaker": "A", "confidence": 0.99931}, {"end": 168175, "text": "highest", "start": 167911, "speaker": "A", "confidence": 0.99691}, {"end": 168415, "text": "level", "start": 168215, "speaker": "A", "confidence": 0.99908}, {"end": 168727, "text": "of", "start": 168455, "speaker": "A", "confidence": 0.99782}, {"end": 169395, "text": "education?", "start": 168791, "speaker": "A", "confidence": 0.99883}, {"end": 170623, "text": "I", "start": 170215, "speaker": "B", "confidence": 0.99292}, {"end": 171031, "text": "dropped", "start": 170679, "speaker": "B", "confidence": 0.99033}, {"end": 171303, "text": "out", "start": 171063, "speaker": "B", "confidence": 0.99872}, {"end": 171719, "text": "when", "start": 171359, "speaker": "B", "confidence": 0.99573}, {"end": 172031, "text": "I", "start": 171807, "speaker": "B", "confidence": 0.99748}, {"end": 172255, "text": "was", "start": 172063, "speaker": "B", "confidence": 0.99913}, {"end": 172423, "text": "in", "start": 172295, "speaker": "B", "confidence": 0.99784}, {"end": 172615, "text": "the", "start": 172439, "speaker": "B", "confidence": 0.99009}, {"end": 173039, "text": "11th", "start": 172655, "speaker": "B", "confidence": 0.99645}, {"end": 173559, "text": "grade,", "start": 173087, "speaker": "B", "confidence": 0.94633}, {"end": 173927, "text": "and", "start": 173687, "speaker": "B", "confidence": 0.99756}, {"end": 174111, "text": "I", "start": 173951, "speaker": "B", "confidence": 0.99581}, {"end": 174335, "text": "started", "start": 174143, "speaker": "B", "confidence": 0.99943}, {"end": 174551, "text": "doing", "start": 174375, "speaker": "B", "confidence": 0.99734}, {"end": 174959, "text": "plumbing.", "start": 174583, "speaker": "B", "confidence": 0.90704}, {"end": 175247, "text": "I've", "start": 175007, "speaker": "B", "confidence": 0.99191}, {"end": 175479, "text": "actually", "start": 175271, "speaker": "B", "confidence": 0.99885}, {"end": 175663, "text": "been", "start": 175527, "speaker": "B", "confidence": 0.99932}, {"end": 175831, "text": "doing", "start": 175679, "speaker": "B", "confidence": 0.99715}, {"end": 176151, "text": "plumbing", "start": 175863, "speaker": "B", "confidence": 0.88204}, {"end": 176351, "text": "for", "start": 176183, "speaker": "B", "confidence": 0.99845}, {"end": 176695, "text": "13", "start": 176383, "speaker": "B", "confidence": 0.99888}, {"end": 177279, "text": "years.", "start": 176775, "speaker": "B", "confidence": 0.99906}, {"end": 177647, "text": "I'm", "start": 177407, "speaker": "B", "confidence": 0.96723}, {"end": 177783, "text": "a", "start": 177671, "speaker": "B", "confidence": 0.99403}, {"end": 178191, "text": "registered", "start": 177799, "speaker": "B", "confidence": 0.52987}, {"end": 178599, "text": "apprentice.", "start": 178223, "speaker": "B", "confidence": 0.57967}, {"end": 178807, "text": "I", "start": 178647, "speaker": "B", "confidence": 0.63548}, {"end": 179015, "text": "actually", "start": 178831, "speaker": "B", "confidence": 0.99128}, {"end": 179207, "text": "only", "start": 179055, "speaker": "B", "confidence": 0.86786}, {"end": 179319, "text": "have", "start": 179231, "speaker": "B", "confidence": 0.96855}, {"end": 179399, "text": "to", "start": 179327, "speaker": "B", "confidence": 0.99793}, {"end": 179551, "text": "take", "start": 179407, "speaker": "B", "confidence": 0.99883}, {"end": 179703, "text": "the", "start": 179583, "speaker": "B", "confidence": 0.99743}, {"end": 179871, "text": "test", "start": 179719, "speaker": "B", "confidence": 0.99823}, {"end": 180023, "text": "to", "start": 179903, "speaker": "B", "confidence": 0.9244}, {"end": 180215, "text": "become", "start": 180039, "speaker": "B", "confidence": 0.98657}, {"end": 180835, "text": "a", "start": 180255, "speaker": "B", "confidence": 0.58216}, {"end": 182617, "text": "journeyman's.", "start": 181785, "speaker": "B", "confidence": 0.24424}, {"end": 182833, "text": "A", "start": 182681, "speaker": "B", "confidence": 0.74641}, {"end": 183289, "text": "journeyman", "start": 182849, "speaker": "B", "confidence": 0.26816}, {"end": 183497, "text": "to", "start": 183337, "speaker": "B", "confidence": 0.58017}, {"end": 183681, "text": "open", "start": 183521, "speaker": "B", "confidence": 0.91849}, {"end": 183833, "text": "my", "start": 183713, "speaker": "B", "confidence": 0.99218}, {"end": 184025, "text": "own", "start": 183849, "speaker": "B", "confidence": 0.99696}, {"end": 184313, "text": "company.", "start": 184065, "speaker": "B", "confidence": 0.99955}, {"end": 184513, "text": "And", "start": 184369, "speaker": "B", "confidence": 0.99378}, {"end": 184969, "text": "I'm", "start": 184529, "speaker": "B", "confidence": 0.58914}, {"end": 185337, "text": "not", "start": 185097, "speaker": "B", "confidence": 0.99982}, {"end": 185545, "text": "too", "start": 185361, "speaker": "B", "confidence": 0.99869}, {"end": 185761, "text": "fond", "start": 185585, "speaker": "B", "confidence": 0.93926}, {"end": 185937, "text": "of", "start": 185793, "speaker": "B", "confidence": 0.9997}, {"end": 186425, "text": "plumbing,", "start": 185961, "speaker": "B", "confidence": 0.48964}, {"end": 186649, "text": "you", "start": 186505, "speaker": "B", "confidence": 0.98847}, {"end": 186873, "text": "know,", "start": 186657, "speaker": "B", "confidence": 0.99467}, {"end": 187313, "text": "saying", "start": 186929, "speaker": "B", "confidence": 0.81847}, {"end": 187569, "text": "I", "start": 187409, "speaker": "B", "confidence": 0.83563}, {"end": 187697, "text": "need", "start": 187577, "speaker": "B", "confidence": 0.97442}, {"end": 187881, "text": "something,", "start": 187721, "speaker": "B", "confidence": 0.35552}, {"end": 188033, "text": "I", "start": 187913, "speaker": "B", "confidence": 0.99414}, {"end": 188129, "text": "want", "start": 188049, "speaker": "B", "confidence": 0.99116}, {"end": 188233, "text": "to", "start": 188137, "speaker": "B", "confidence": 0.99786}, {"end": 188377, "text": "do", "start": 188249, "speaker": "B", "confidence": 0.99879}, {"end": 188585, "text": "something", "start": 188401, "speaker": "B", "confidence": 0.99904}, {"end": 188801, "text": "else", "start": 188625, "speaker": "B", "confidence": 0.9988}, {"end": 189201, "text": "besides", "start": 188833, "speaker": "B", "confidence": 0.5293}, {"end": 189497, "text": "plumbing.", "start": 189233, "speaker": "B", "confidence": 0.79511}, {"end": 189633, "text": "For", "start": 189521, "speaker": "B", "confidence": 0.99749}, {"end": 189705, "text": "the", "start": 189649, "speaker": "B", "confidence": 0.99694}, {"end": 189841, "text": "rest", "start": 189705, "speaker": "B", "confidence": 0.94323}, {"end": 189969, "text": "of", "start": 189873, "speaker": "B", "confidence": 0.99896}, {"end": 190121, "text": "my", "start": 189977, "speaker": "B", "confidence": 0.99953}, {"end": 190725, "text": "life.", "start": 190153, "speaker": "B", "confidence": 0.99882}, {"end": 192041, "text": "Okay.", "start": 191225, "speaker": "A", "confidence": 0.57747}, {"end": 192481, "text": "And", "start": 192193, "speaker": "A", "confidence": 0.98464}, {"end": 192633, "text": "do", "start": 192513, "speaker": "A", "confidence": 0.89127}, {"end": 192753, "text": "you", "start": 192649, "speaker": "A", "confidence": 0.99412}, {"end": 192897, "text": "have", "start": 192769, "speaker": "A", "confidence": 0.99934}, {"end": 193081, "text": "a", "start": 192921, "speaker": "A", "confidence": 0.9908}, {"end": 193513, "text": "diploma", "start": 193113, "speaker": "A", "confidence": 0.99546}, {"end": 193785, "text": "or", "start": 193569, "speaker": "A", "confidence": 0.99722}, {"end": 193953, "text": "a", "start": 193825, "speaker": "A", "confidence": 0.9566}, {"end": 194801, "text": "GED?", "start": 193969, "speaker": "A", "confidence": 0.81498}, {"end": 195273, "text": "I", "start": 194993, "speaker": "B", "confidence": 0.99487}, {"end": 195369, "text": "have", "start": 195289, "speaker": "B", "confidence": 0.99964}, {"end": 195473, "text": "a", "start": 195377, "speaker": "B", "confidence": 0.99541}, {"end": 196129, "text": "GED.", "start": 195489, "speaker": "B", "confidence": 0.90896}, {"end": 196713, "text": "GED.", "start": 196257, "speaker": "A", "confidence": 0.47753}, {"end": 197445, "text": "Okay.", "start": 196769, "speaker": "A", "confidence": 0.72202}, {"end": 200097, "text": "And", "start": 199785, "speaker": "A", "confidence": 0.98891}, {"end": 200281, "text": "what", "start": 200121, "speaker": "A", "confidence": 0.99727}, {"end": 200457, "text": "year", "start": 200313, "speaker": "A", "confidence": 0.97488}, {"end": 200617, "text": "did", "start": 200481, "speaker": "A", "confidence": 0.97678}, {"end": 200753, "text": "you", "start": 200641, "speaker": "A", "confidence": 0.99807}, {"end": 201017, "text": "obtain", "start": 200769, "speaker": "A", "confidence": 0.91441}, {"end": 201329, "text": "your", "start": 201081, "speaker": "A", "confidence": 0.9941}, {"end": 202125, "text": "GED?", "start": 201377, "speaker": "A", "confidence": 0.86067}, {"end": 203885, "text": "1999.", "start": 202505, "speaker": "B", "confidence": 0.97739}, {"end": 209189, "text": "Okay,", "start": 208765, "speaker": "A", "confidence": 0.85067}, {"end": 209397, "text": "and", "start": 209237, "speaker": "A", "confidence": 0.98019}, {"end": 209557, "text": "for", "start": 209421, "speaker": "A", "confidence": 0.99516}, {"end": 209741, "text": "your", "start": 209581, "speaker": "A", "confidence": 0.99586}, {"end": 209989, "text": "class", "start": 209773, "speaker": "A", "confidence": 0.9938}, {"end": 210285, "text": "type", "start": 210037, "speaker": "A", "confidence": 0.91132}, {"end": 210709, "text": "reference,", "start": 210325, "speaker": "A", "confidence": 0.33759}, {"end": 210917, "text": "would", "start": 210757, "speaker": "A", "confidence": 0.98097}, {"end": 211077, "text": "it", "start": 210941, "speaker": "A", "confidence": 0.98864}, {"end": 211309, "text": "be", "start": 211101, "speaker": "A", "confidence": 0.99909}, {"end": 211877, "text": "online,", "start": 211357, "speaker": "A", "confidence": 0.99955}, {"end": 212325, "text": "on", "start": 212021, "speaker": "A", "confidence": 0.98939}, {"end": 212909, "text": "campus", "start": 212365, "speaker": "A", "confidence": 0.90451}, {"end": 213373, "text": "or.", "start": 213037, "speaker": "A", "confidence": 0.64549}, {"end": 213621, "text": "No,", "start": 213429, "speaker": "B", "confidence": 0.41768}, {"end": 214053, "text": "probably", "start": 213653, "speaker": "B", "confidence": 0.80577}, {"end": 214621, "text": "campus.", "start": 214149, "speaker": "B", "confidence": 0.71762}, {"end": 214901, "text": "Probably", "start": 214693, "speaker": "B", "confidence": 0.6047}, {"end": 215077, "text": "would", "start": 214933, "speaker": "B", "confidence": 0.50529}, {"end": 215285, "text": "be", "start": 215101, "speaker": "B", "confidence": 0.99304}, {"end": 216025, "text": "campus.", "start": 215325, "speaker": "B", "confidence": 0.99594}, {"end": 217037, "text": "Campus.", "start": 216485, "speaker": "A", "confidence": 0.61174}, {"end": 217785, "text": "Okay.", "start": 217101, "speaker": "A", "confidence": 0.58966}, {"end": 221653, "text": "Just", "start": 221365, "speaker": "A", "confidence": 0.98495}, {"end": 221749, "text": "in", "start": 221669, "speaker": "A", "confidence": 0.99394}, {"end": 222305, "text": "case", "start": 221757, "speaker": "A", "confidence": 0.62228}, {"end": 222933, "text": "we'll", "start": 222605, "speaker": "A", "confidence": 0.31225}, {"end": 223053, "text": "not", "start": 222949, "speaker": "A", "confidence": 0.95683}, {"end": 223149, "text": "be", "start": 223069, "speaker": "A", "confidence": 0.85827}, {"end": 223277, "text": "able", "start": 223157, "speaker": "A", "confidence": 0.62228}, {"end": 223437, "text": "to", "start": 223301, "speaker": "A", "confidence": 0.99722}, {"end": 223621, "text": "find", "start": 223461, "speaker": "A", "confidence": 0.99667}, {"end": 223773, "text": "a", "start": 223653, "speaker": "A", "confidence": 0.9879}, {"end": 224069, "text": "campus", "start": 223789, "speaker": "A", "confidence": 0.99718}, {"end": 224349, "text": "based", "start": 224117, "speaker": "A", "confidence": 0.95412}, {"end": 224653, "text": "school,", "start": 224397, "speaker": "A", "confidence": 0.99708}, {"end": 224853, "text": "would", "start": 224709, "speaker": "A", "confidence": 0.9879}, {"end": 224997, "text": "you", "start": 224869, "speaker": "A", "confidence": 0.98456}, {"end": 225133, "text": "be", "start": 225021, "speaker": "A", "confidence": 0.99724}, {"end": 225357, "text": "okay", "start": 225149, "speaker": "A", "confidence": 0.94228}, {"end": 225541, "text": "with", "start": 225381, "speaker": "A", "confidence": 0.98791}, {"end": 225669, "text": "an", "start": 225573, "speaker": "A", "confidence": 0.98133}, {"end": 226085, "text": "online", "start": 225677, "speaker": "A", "confidence": 0.55794}, {"end": 226941, "text": "school?", "start": 226205, "speaker": "A", "confidence": 0.99959}, {"end": 227973, "text": "Yeah.", "start": 227133, "speaker": "B", "confidence": 0.97021}, {"end": 228945, "text": "Okay.", "start": 228149, "speaker": "A", "confidence": 0.92082}, {"end": 231213, "text": "And", "start": 230925, "speaker": "A", "confidence": 0.94594}, {"end": 231333, "text": "are", "start": 231229, "speaker": "A", "confidence": 0.99641}, {"end": 231477, "text": "you", "start": 231349, "speaker": "A", "confidence": 0.9914}, {"end": 231613, "text": "a", "start": 231501, "speaker": "A", "confidence": 0.94855}, {"end": 231853, "text": "United", "start": 231629, "speaker": "A", "confidence": 0.99885}, {"end": 232125, "text": "States", "start": 231909, "speaker": "A", "confidence": 0.99572}, {"end": 232865, "text": "citizen?", "start": 232165, "speaker": "A", "confidence": 0.65415}, {"end": 233627, "text": "Yes,", "start": 233315, "speaker": "B", "confidence": 0.90551}, {"end": 233787, "text": "I", "start": 233651, "speaker": "B", "confidence": 0.99451}, {"end": 234375, "text": "am.", "start": 233811, "speaker": "B", "confidence": 0.9955}, {"end": 235187, "text": "Thank", "start": 234875, "speaker": "A", "confidence": 0.84414}, {"end": 235371, "text": "you.", "start": 235211, "speaker": "A", "confidence": 0.99703}, {"end": 235547, "text": "And", "start": 235403, "speaker": "A", "confidence": 0.87713}, {"end": 235683, "text": "are", "start": 235571, "speaker": "A", "confidence": 0.9951}, {"end": 235827, "text": "you", "start": 235699, "speaker": "A", "confidence": 0.99856}, {"end": 236267, "text": "associated", "start": 235851, "speaker": "A", "confidence": 0.62095}, {"end": 236483, "text": "with", "start": 236331, "speaker": "A", "confidence": 0.99555}, {"end": 236603, "text": "the", "start": 236499, "speaker": "A", "confidence": 0.89029}, {"end": 236843, "text": "United", "start": 236619, "speaker": "A", "confidence": 0.93724}, {"end": 237139, "text": "States", "start": 236899, "speaker": "A", "confidence": 0.99461}, {"end": 238027, "text": "military?", "start": 237187, "speaker": "A", "confidence": 0.91072}, {"end": 238483, "text": "What", "start": 238211, "speaker": "B", "confidence": 0.99229}, {"end": 238627, "text": "was", "start": 238499, "speaker": "B", "confidence": 0.99465}, {"end": 239215, "text": "that?", "start": 238651, "speaker": "B", "confidence": 0.99534}, {"end": 239963, "text": "I'm", "start": 239635, "speaker": "A", "confidence": 0.78812}, {"end": 240171, "text": "sorry?", "start": 239979, "speaker": "A", "confidence": 0.989}, {"end": 240299, "text": "Are", "start": 240203, "speaker": "A", "confidence": 0.99377}, {"end": 240547, "text": "you", "start": 240307, "speaker": "A", "confidence": 0.99932}, {"end": 241195, "text": "associated", "start": 240611, "speaker": "A", "confidence": 0.72997}, {"end": 241491, "text": "with", "start": 241275, "speaker": "A", "confidence": 0.99874}, {"end": 241691, "text": "the", "start": 241523, "speaker": "A", "confidence": 0.98712}, {"end": 241963, "text": "United", "start": 241723, "speaker": "A", "confidence": 0.996}, {"end": 242283, "text": "States", "start": 242019, "speaker": "A", "confidence": 0.9994}, {"end": 243139, "text": "military?", "start": 242339, "speaker": "A", "confidence": 0.99968}, {"end": 243563, "text": "No,", "start": 243307, "speaker": "B", "confidence": 0.99334}, {"end": 243707, "text": "I'm", "start": 243579, "speaker": "B", "confidence": 0.96241}, {"end": 244295, "text": "not.", "start": 243731, "speaker": "B", "confidence": 0.99799}, {"end": 246175, "text": "Okay.", "start": 245355, "speaker": "A", "confidence": 0.47456}, {"end": 247747, "text": "And", "start": 247435, "speaker": "A", "confidence": 0.93575}, {"end": 247931, "text": "what", "start": 247771, "speaker": "A", "confidence": 0.99959}, {"end": 248083, "text": "would", "start": 247963, "speaker": "A", "confidence": 0.99887}, {"end": 248179, "text": "be", "start": 248099, "speaker": "A", "confidence": 0.99912}, {"end": 248331, "text": "the", "start": 248187, "speaker": "A", "confidence": 0.99863}, {"end": 248555, "text": "best", "start": 248363, "speaker": "A", "confidence": 0.99954}, {"end": 248747, "text": "time", "start": 248595, "speaker": "A", "confidence": 0.99532}, {"end": 248907, "text": "for", "start": 248771, "speaker": "A", "confidence": 0.99647}, {"end": 249067, "text": "a", "start": 248931, "speaker": "A", "confidence": 0.89935}, {"end": 249275, "text": "school", "start": 249091, "speaker": "A", "confidence": 0.99876}, {"end": 249659, "text": "enrollment", "start": 249315, "speaker": "A", "confidence": 0.886}, {"end": 250131, "text": "counselor", "start": 249707, "speaker": "A", "confidence": 0.62801}, {"end": 250355, "text": "to", "start": 250163, "speaker": "A", "confidence": 0.99182}, {"end": 250643, "text": "contact", "start": 250395, "speaker": "A", "confidence": 0.99083}, {"end": 251011, "text": "you", "start": 250699, "speaker": "A", "confidence": 0.71397}, {"end": 251219, "text": "in", "start": 251083, "speaker": "A", "confidence": 0.99682}, {"end": 251371, "text": "the", "start": 251227, "speaker": "A", "confidence": 0.99717}, {"end": 251955, "text": "morning,", "start": 251403, "speaker": "A", "confidence": 0.99991}, {"end": 252611, "text": "afternoon,", "start": 252115, "speaker": "A", "confidence": 0.99506}, {"end": 252859, "text": "or", "start": 252643, "speaker": "A", "confidence": 0.9988}, {"end": 253535, "text": "evening?", "start": 252907, "speaker": "A", "confidence": 0.70444}, {"end": 256019, "text": "Pretty", "start": 255755, "speaker": "B", "confidence": 0.99826}, {"end": 256171, "text": "much", "start": 256027, "speaker": "B", "confidence": 0.9997}, {"end": 256371, "text": "any", "start": 256203, "speaker": "B", "confidence": 0.99942}, {"end": 256547, "text": "time", "start": 256403, "speaker": "B", "confidence": 0.98795}, {"end": 256659, "text": "of", "start": 256571, "speaker": "B", "confidence": 0.89067}, {"end": 256787, "text": "the", "start": 256667, "speaker": "B", "confidence": 0.98761}, {"end": 257375, "text": "day.", "start": 256811, "speaker": "B", "confidence": 0.99823}, {"end": 260341, "text": "And", "start": 260005, "speaker": "A", "confidence": 0.96669}, {"end": 260517, "text": "what", "start": 260373, "speaker": "A", "confidence": 0.9995}, {"end": 260653, "text": "is", "start": 260541, "speaker": "A", "confidence": 0.99723}, {"end": 260821, "text": "your", "start": 260669, "speaker": "A", "confidence": 0.99908}, {"end": 261125, "text": "exact", "start": 260853, "speaker": "A", "confidence": 0.9982}, {"end": 261317, "text": "date", "start": 261165, "speaker": "A", "confidence": 0.95517}, {"end": 261477, "text": "of", "start": 261341, "speaker": "A", "confidence": 0.98827}, {"end": 262109, "text": "birth?", "start": 261501, "speaker": "A", "confidence": 0.56287}, {"end": 263059, "text": "10,", "start": 262277, "speaker": "B", "confidence": 0.91638}, {"end": 263928, "text": "1580.", "start": 263233, "speaker": "B", "confidence": 0.91638}, {"end": 264798, "text": "So", "start": 264102, "speaker": "A", "confidence": 0.91638}, {"end": 265667, "text": "that", "start": 264971, "speaker": "A", "confidence": 0.91638}, {"end": 266536, "text": "would", "start": 265841, "speaker": "A", "confidence": 0.91638}, {"end": 267406, "text": "be", "start": 266710, "speaker": "A", "confidence": 0.91638}, {"end": 268275, "text": "October", "start": 267579, "speaker": "A", "confidence": 0.91638}, {"end": 269144, "text": "15th,", "start": 268449, "speaker": "A", "confidence": 0.91638}, {"end": 270101, "text": "1980?", "start": 269318, "speaker": "A", "confidence": 0.91638}, {"end": 270605, "text": "That's", "start": 270253, "speaker": "B", "confidence": 0.9906}, {"end": 271225, "text": "correct.", "start": 270645, "speaker": "B", "confidence": 0.99958}, {"end": 273745, "text": "Okay.", "start": 272925, "speaker": "A", "confidence": 0.62638}, {"end": 274557, "text": "Okay,", "start": 274165, "speaker": "A", "confidence": 0.57247}, {"end": 275225, "text": "<PERSON>,", "start": 274581, "speaker": "A", "confidence": 0.89512}, {"end": 277093, "text": "if", "start": 276805, "speaker": "A", "confidence": 0.99007}, {"end": 277213, "text": "we", "start": 277109, "speaker": "A", "confidence": 0.99876}, {"end": 277309, "text": "can", "start": 277229, "speaker": "A", "confidence": 0.99845}, {"end": 277509, "text": "find", "start": 277317, "speaker": "A", "confidence": 0.69099}, {"end": 277765, "text": "school", "start": 277557, "speaker": "A", "confidence": 0.9978}, {"end": 277933, "text": "for", "start": 277805, "speaker": "A", "confidence": 0.97894}, {"end": 278077, "text": "you", "start": 277949, "speaker": "A", "confidence": 0.99546}, {"end": 278237, "text": "that", "start": 278101, "speaker": "A", "confidence": 0.97141}, {"end": 278469, "text": "meets", "start": 278261, "speaker": "A", "confidence": 0.17403}, {"end": 278597, "text": "your", "start": 278477, "speaker": "A", "confidence": 0.88687}, {"end": 278829, "text": "needs,", "start": 278621, "speaker": "A", "confidence": 0.91558}, {"end": 279085, "text": "school", "start": 278877, "speaker": "A", "confidence": 0.98342}, {"end": 279469, "text": "enrollment", "start": 279125, "speaker": "A", "confidence": 0.89723}, {"end": 280029, "text": "counselors", "start": 279517, "speaker": "A", "confidence": 0.84147}, {"end": 280237, "text": "will", "start": 280077, "speaker": "A", "confidence": 0.87938}, {"end": 280421, "text": "be", "start": 280261, "speaker": "A", "confidence": 0.99921}, {"end": 280925, "text": "contacting", "start": 280453, "speaker": "A", "confidence": 0.98847}, {"end": 281545, "text": "you", "start": 280965, "speaker": "A", "confidence": 0.99964}, {"end": 282133, "text": "in", "start": 281845, "speaker": "A", "confidence": 0.99912}, {"end": 282277, "text": "the", "start": 282149, "speaker": "A", "confidence": 0.99949}, {"end": 282485, "text": "near", "start": 282301, "speaker": "A", "confidence": 0.99946}, {"end": 282989, "text": "future,", "start": 282525, "speaker": "A", "confidence": 0.99994}, {"end": 283381, "text": "either", "start": 283117, "speaker": "A", "confidence": 0.90098}, {"end": 283677, "text": "by", "start": 283413, "speaker": "A", "confidence": 0.98733}, {"end": 284037, "text": "phone", "start": 283741, "speaker": "A", "confidence": 0.8926}, {"end": 284301, "text": "or", "start": 284101, "speaker": "A", "confidence": 0.999}, {"end": 284525, "text": "by", "start": 284333, "speaker": "A", "confidence": 0.99408}, {"end": 284837, "text": "email,", "start": 284565, "speaker": "A", "confidence": 0.99434}, {"end": 285053, "text": "and", "start": 284901, "speaker": "A", "confidence": 0.99324}, {"end": 285197, "text": "they", "start": 285069, "speaker": "A", "confidence": 0.9891}, {"end": 285405, "text": "can", "start": 285221, "speaker": "A", "confidence": 0.99882}, {"end": 285685, "text": "answer", "start": 285445, "speaker": "A", "confidence": 0.97402}, {"end": 285901, "text": "any", "start": 285725, "speaker": "A", "confidence": 0.97228}, {"end": 286197, "text": "questions", "start": 285933, "speaker": "A", "confidence": 0.99398}, {"end": 286357, "text": "you", "start": 286221, "speaker": "A", "confidence": 0.99566}, {"end": 286541, "text": "may", "start": 286381, "speaker": "A", "confidence": 0.9974}, {"end": 286765, "text": "have", "start": 286573, "speaker": "A", "confidence": 0.99941}, {"end": 287213, "text": "regarding", "start": 286805, "speaker": "A", "confidence": 0.53077}, {"end": 287605, "text": "financial", "start": 287269, "speaker": "A", "confidence": 0.99984}, {"end": 288255, "text": "aid,", "start": 287685, "speaker": "A", "confidence": 0.93794}, {"end": 288691, "text": "which", "start": 288405, "speaker": "A", "confidence": 0.81717}, {"end": 289467, "text": "assistance,", "start": 288723, "speaker": "A", "confidence": 0.33569}, {"end": 289859, "text": "their", "start": 289571, "speaker": "A", "confidence": 0.9891}, {"end": 290163, "text": "program", "start": 289907, "speaker": "A", "confidence": 0.99906}, {"end": 290635, "text": "requirements", "start": 290219, "speaker": "A", "confidence": 0.99348}, {"end": 290851, "text": "and", "start": 290675, "speaker": "A", "confidence": 0.99049}, {"end": 291575, "text": "policies.", "start": 290883, "speaker": "A", "confidence": 0.92975}, {"end": 293403, "text": "And", "start": 293115, "speaker": "A", "confidence": 0.70911}, {"end": 293571, "text": "so", "start": 293419, "speaker": "A", "confidence": 0.98589}, {"end": 293747, "text": "with", "start": 293603, "speaker": "A", "confidence": 0.99136}, {"end": 293907, "text": "that,", "start": 293771, "speaker": "A", "confidence": 0.9991}, {"end": 294019, "text": "I", "start": 293931, "speaker": "A", "confidence": 0.98231}, {"end": 294123, "text": "would", "start": 294027, "speaker": "A", "confidence": 0.9812}, {"end": 294267, "text": "just", "start": 294139, "speaker": "A", "confidence": 0.97861}, {"end": 294403, "text": "like", "start": 294291, "speaker": "A", "confidence": 0.63688}, {"end": 294547, "text": "to", "start": 294419, "speaker": "A", "confidence": 0.54609}, {"end": 294683, "text": "thank", "start": 294571, "speaker": "A", "confidence": 0.99765}, {"end": 294803, "text": "you", "start": 294699, "speaker": "A", "confidence": 0.98696}, {"end": 294923, "text": "for", "start": 294819, "speaker": "A", "confidence": 0.995}, {"end": 295091, "text": "your", "start": 294939, "speaker": "A", "confidence": 0.99348}, {"end": 295695, "text": "time.", "start": 295123, "speaker": "A", "confidence": 0.99925}, {"end": 297907, "text": "Okay.", "start": 297315, "speaker": "A", "confidence": 0.72501}, {"end": 298203, "text": "Once", "start": 298011, "speaker": "A", "confidence": 0.93621}, {"end": 298371, "text": "again,", "start": 298219, "speaker": "A", "confidence": 0.99541}, {"end": 298595, "text": "we", "start": 298403, "speaker": "A", "confidence": 0.82832}, {"end": 298787, "text": "thank", "start": 298635, "speaker": "A", "confidence": 0.99407}, {"end": 298947, "text": "you", "start": 298811, "speaker": "A", "confidence": 0.99865}, {"end": 299203, "text": "for", "start": 298971, "speaker": "A", "confidence": 0.99747}, {"end": 299643, "text": "choosing", "start": 299259, "speaker": "A", "confidence": 0.88575}, {"end": 300107, "text": "education", "start": 299699, "speaker": "A", "confidence": 0.99881}, {"end": 300971, "text": "experts.", "start": 300211, "speaker": "A", "confidence": 0.89988}, {"end": 301387, "text": "And", "start": 301123, "speaker": "B", "confidence": 0.94823}, {"end": 301547, "text": "thank", "start": 301411, "speaker": "B", "confidence": 0.95102}, {"end": 302135, "text": "you.", "start": 301571, "speaker": "B", "confidence": 0.99832}, {"end": 302963, "text": "You're", "start": 302635, "speaker": "A", "confidence": 0.624}, {"end": 303251, "text": "welcome.", "start": 302979, "speaker": "A", "confidence": 0.53102}, {"end": 303435, "text": "All", "start": 303323, "speaker": "B", "confidence": 0.59335}, {"end": 304027, "text": "right,", "start": 303435, "speaker": "B", "confidence": 0.95517}, {"end": 304507, "text": "you", "start": 304211, "speaker": "B", "confidence": 0.95607}, {"end": 305123, "text": "too.", "start": 304531, "speaker": "B", "confidence": 0.97318}, {"end": 305563, "text": "Bye.", "start": 305299, "speaker": "A", "confidence": 0.91029}], "status": "completed", "topics": [], "summary": null, "chapters": null, "entities": null, "audio_url": "https://cflobdhpqwnoisuctsoc.supabase.co/storage/v1/object/public/5minai/OUTBOUNDSAMPLE_01.mp3", "punctuate": true, "throttled": false, "confidence": 0.89525497, "is_deleted": null, "redact_pii": false, "utterances": [{"end": 3113, "text": "Hello. May I, please, <PERSON>.", "start": 1480, "words": [{"end": 1736, "text": "Hello.", "start": 1480, "speaker": "A", "confidence": 0.94271}, {"end": 1872, "text": "May", "start": 1736, "speaker": "A", "confidence": 0.6092}, {"end": 1944, "text": "I,", "start": 1872, "speaker": "A", "confidence": 0.81608}, {"end": 2400, "text": "please,", "start": 1944, "speaker": "A", "confidence": 0.98526}, {"end": 3113, "text": "<PERSON>.", "start": 2489, "speaker": "A", "confidence": 0.79453}], "speaker": "A", "confidence": 0.829556}, {"end": 5165, "text": "Hello, this is <PERSON>.", "start": 3249, "words": [{"end": 4025, "text": "Hello,", "start": 3249, "speaker": "B", "confidence": 0.3434}, {"end": 4353, "text": "this", "start": 4145, "speaker": "B", "confidence": 0.99092}, {"end": 4521, "text": "is", "start": 4369, "speaker": "B", "confidence": 0.9984}, {"end": 5165, "text": "<PERSON>.", "start": 4553, "speaker": "B", "confidence": 0.5279}], "speaker": "B", "confidence": 0.715155}, {"end": 18385, "text": "Hi, <PERSON>. My name is <PERSON>, and I'm calling on behalf of education experts from a quality monitor line, and here that you recently filled the form on the Internet indicating an interest in earning a degree.", "start": 5665, "words": [{"end": 6065, "text": "Hi,", "start": 5665, "speaker": "A", "confidence": 0.98595}, {"end": 6553, "text": "<PERSON>.", "start": 6105, "speaker": "A", "confidence": 0.95117}, {"end": 6833, "text": "My", "start": 6649, "speaker": "A", "confidence": 0.98171}, {"end": 6953, "text": "name", "start": 6849, "speaker": "A", "confidence": 0.99951}, {"end": 7121, "text": "is", "start": 6969, "speaker": "A", "confidence": 0.97658}, {"end": 7449, "text": "<PERSON>,", "start": 7153, "speaker": "A", "confidence": 0.47833}, {"end": 7633, "text": "and", "start": 7497, "speaker": "A", "confidence": 0.9914}, {"end": 7777, "text": "I'm", "start": 7649, "speaker": "A", "confidence": 0.91787}, {"end": 8121, "text": "calling", "start": 7801, "speaker": "A", "confidence": 0.65072}, {"end": 8345, "text": "on", "start": 8153, "speaker": "A", "confidence": 0.99949}, {"end": 8745, "text": "behalf", "start": 8385, "speaker": "A", "confidence": 0.70579}, {"end": 9033, "text": "of", "start": 8785, "speaker": "A", "confidence": 0.99746}, {"end": 9593, "text": "education", "start": 9089, "speaker": "A", "confidence": 0.999}, {"end": 10233, "text": "experts", "start": 9729, "speaker": "A", "confidence": 0.99709}, {"end": 10457, "text": "from", "start": 10289, "speaker": "A", "confidence": 0.96937}, {"end": 10593, "text": "a", "start": 10481, "speaker": "A", "confidence": 0.72221}, {"end": 10929, "text": "quality", "start": 10609, "speaker": "A", "confidence": 0.9818}, {"end": 11361, "text": "monitor", "start": 10977, "speaker": "A", "confidence": 0.56758}, {"end": 12001, "text": "line,", "start": 11433, "speaker": "A", "confidence": 0.54956}, {"end": 12705, "text": "and", "start": 12153, "speaker": "A", "confidence": 0.57481}, {"end": 13225, "text": "here", "start": 12825, "speaker": "A", "confidence": 0.74568}, {"end": 13545, "text": "that", "start": 13305, "speaker": "A", "confidence": 0.73898}, {"end": 13737, "text": "you", "start": 13585, "speaker": "A", "confidence": 0.38875}, {"end": 14041, "text": "recently", "start": 13761, "speaker": "A", "confidence": 0.99362}, {"end": 14361, "text": "filled", "start": 14113, "speaker": "A", "confidence": 0.98643}, {"end": 14585, "text": "the", "start": 14393, "speaker": "A", "confidence": 0.71449}, {"end": 14897, "text": "form", "start": 14625, "speaker": "A", "confidence": 0.9993}, {"end": 15137, "text": "on", "start": 14961, "speaker": "A", "confidence": 0.98255}, {"end": 15321, "text": "the", "start": 15161, "speaker": "A", "confidence": 0.95742}, {"end": 15953, "text": "Internet", "start": 15353, "speaker": "A", "confidence": 0.95742}, {"end": 16569, "text": "indicating", "start": 16089, "speaker": "A", "confidence": 0.95638}, {"end": 16777, "text": "an", "start": 16617, "speaker": "A", "confidence": 0.98952}, {"end": 17057, "text": "interest", "start": 16801, "speaker": "A", "confidence": 0.97961}, {"end": 17297, "text": "in", "start": 17121, "speaker": "A", "confidence": 0.9872}, {"end": 17505, "text": "earning", "start": 17321, "speaker": "A", "confidence": 0.93147}, {"end": 17697, "text": "a", "start": 17545, "speaker": "A", "confidence": 0.99152}, {"end": 18385, "text": "degree.", "start": 17721, "speaker": "A", "confidence": 0.9097}], "speaker": "A", "confidence": 0.87047136}, {"end": 18937, "text": "Yes.", "start": 18545, "words": [{"end": 18937, "text": "Yes.", "start": 18545, "speaker": "B", "confidence": 0.54822}], "speaker": "B", "confidence": 0.54822}, {"end": 19625, "text": "Correct.", "start": 19001, "words": [{"end": 19625, "text": "Correct.", "start": 19001, "speaker": "A", "confidence": 0.61381}], "speaker": "A", "confidence": 0.61381}, {"end": 20525, "text": "Yes.", "start": 19785, "words": [{"end": 20525, "text": "Yes.", "start": 19785, "speaker": "B", "confidence": 0.5177}], "speaker": "B", "confidence": 0.5177}, {"end": 28642, "text": "I only need a few moments of her time to mention the most appropriate schools. Are you at least 18 years of age?", "start": 21345, "words": [{"end": 21657, "text": "I", "start": 21345, "speaker": "A", "confidence": 0.99611}, {"end": 21841, "text": "only", "start": 21681, "speaker": "A", "confidence": 0.99857}, {"end": 22041, "text": "need", "start": 21873, "speaker": "A", "confidence": 0.99871}, {"end": 22169, "text": "a", "start": 22073, "speaker": "A", "confidence": 0.99842}, {"end": 22297, "text": "few", "start": 22177, "speaker": "A", "confidence": 0.99964}, {"end": 22545, "text": "moments", "start": 22321, "speaker": "A", "confidence": 0.94492}, {"end": 22737, "text": "of", "start": 22585, "speaker": "A", "confidence": 0.88045}, {"end": 22921, "text": "her", "start": 22761, "speaker": "A", "confidence": 0.52082}, {"end": 23169, "text": "time", "start": 22953, "speaker": "A", "confidence": 0.99957}, {"end": 23401, "text": "to", "start": 23217, "speaker": "A", "confidence": 0.97503}, {"end": 23897, "text": "mention", "start": 23433, "speaker": "A", "confidence": 0.56777}, {"end": 24193, "text": "the", "start": 24001, "speaker": "A", "confidence": 0.65662}, {"end": 24409, "text": "most", "start": 24209, "speaker": "A", "confidence": 0.99193}, {"end": 24961, "text": "appropriate", "start": 24457, "speaker": "A", "confidence": 0.78553}, {"end": 25649, "text": "schools.", "start": 25033, "speaker": "A", "confidence": 0.98905}, {"end": 25993, "text": "Are", "start": 25777, "speaker": "A", "confidence": 0.93187}, {"end": 26137, "text": "you", "start": 26009, "speaker": "A", "confidence": 0.99905}, {"end": 26297, "text": "at", "start": 26161, "speaker": "A", "confidence": 0.99623}, {"end": 26601, "text": "least", "start": 26321, "speaker": "A", "confidence": 0.99414}, {"end": 27127, "text": "18", "start": 26673, "speaker": "A", "confidence": 0.95011}, {"end": 27632, "text": "years", "start": 27228, "speaker": "A", "confidence": 0.95011}, {"end": 28137, "text": "of", "start": 27733, "speaker": "A", "confidence": 0.95011}, {"end": 28642, "text": "age?", "start": 28238, "speaker": "A", "confidence": 0.95011}], "speaker": "A", "confidence": 0.9141248}, {"end": 30157, "text": "Yeah, I'm 29.", "start": 28743, "words": [{"end": 29147, "text": "Yeah,", "start": 28743, "speaker": "B", "confidence": 0.95011}, {"end": 29652, "text": "I'm", "start": 29248, "speaker": "B", "confidence": 0.95011}, {"end": 30157, "text": "29.", "start": 29753, "speaker": "B", "confidence": 0.95011}], "speaker": "B", "confidence": 0.95011}, {"end": 30713, "text": "29.", "start": 30258, "words": [{"end": 30713, "text": "29.", "start": 30258, "speaker": "A", "confidence": 0.95011}], "speaker": "A", "confidence": 0.95011}, {"end": 31001, "text": "Okay.", "start": 30769, "words": [{"end": 31001, "text": "Okay.", "start": 30769, "speaker": "B", "confidence": 0.68301}], "speaker": "B", "confidence": 0.68301}, {"end": 33961, "text": "And do you currently have a high school diploma or a ged?", "start": 31033, "words": [{"end": 31153, "text": "And", "start": 31033, "speaker": "A", "confidence": 0.91268}, {"end": 31249, "text": "do", "start": 31169, "speaker": "A", "confidence": 0.75558}, {"end": 31401, "text": "you", "start": 31257, "speaker": "A", "confidence": 0.99112}, {"end": 31673, "text": "currently", "start": 31433, "speaker": "A", "confidence": 0.99463}, {"end": 31897, "text": "have", "start": 31729, "speaker": "A", "confidence": 0.9993}, {"end": 32033, "text": "a", "start": 31921, "speaker": "A", "confidence": 0.98565}, {"end": 32177, "text": "high", "start": 32049, "speaker": "A", "confidence": 0.99845}, {"end": 32385, "text": "school", "start": 32201, "speaker": "A", "confidence": 0.99979}, {"end": 32817, "text": "diploma", "start": 32425, "speaker": "A", "confidence": 0.86589}, {"end": 33081, "text": "or", "start": 32881, "speaker": "A", "confidence": 0.99886}, {"end": 33257, "text": "a", "start": 33113, "speaker": "A", "confidence": 0.97091}, {"end": 33961, "text": "ged?", "start": 33281, "speaker": "A", "confidence": 0.95546}], "speaker": "A", "confidence": 0.95236}, {"end": 35085, "text": "Yes, I do.", "start": 34113, "words": [{"end": 34377, "text": "Yes,", "start": 34113, "speaker": "B", "confidence": 0.9898}, {"end": 34513, "text": "I", "start": 34401, "speaker": "B", "confidence": 0.99684}, {"end": 35085, "text": "do.", "start": 34529, "speaker": "B", "confidence": 0.99955}], "speaker": "B", "confidence": 0.9953967}, {"end": 44153, "text": "Okay, thank you, <PERSON>. And if we can find a school for you that meets your needs, would you be interested in furthering your education in the next six months? Yeah, of course.", "start": 36105, "words": [{"end": 36505, "text": "Okay,", "start": 36105, "speaker": "A", "confidence": 0.839}, {"end": 36673, "text": "thank", "start": 36545, "speaker": "A", "confidence": 0.96607}, {"end": 36817, "text": "you,", "start": 36689, "speaker": "A", "confidence": 0.99926}, {"end": 37161, "text": "<PERSON>.", "start": 36841, "speaker": "A", "confidence": 0.59546}, {"end": 37441, "text": "And", "start": 37233, "speaker": "A", "confidence": 0.9426}, {"end": 37593, "text": "if", "start": 37473, "speaker": "A", "confidence": 0.99034}, {"end": 37713, "text": "we", "start": 37609, "speaker": "A", "confidence": 0.99775}, {"end": 37881, "text": "can", "start": 37729, "speaker": "A", "confidence": 0.98007}, {"end": 38081, "text": "find", "start": 37913, "speaker": "A", "confidence": 0.99483}, {"end": 38233, "text": "a", "start": 38113, "speaker": "A", "confidence": 0.95744}, {"end": 38425, "text": "school", "start": 38249, "speaker": "A", "confidence": 0.99911}, {"end": 38617, "text": "for", "start": 38465, "speaker": "A", "confidence": 0.99705}, {"end": 38825, "text": "you", "start": 38641, "speaker": "A", "confidence": 0.99922}, {"end": 39041, "text": "that", "start": 38865, "speaker": "A", "confidence": 0.99841}, {"end": 39297, "text": "meets", "start": 39073, "speaker": "A", "confidence": 0.9695}, {"end": 39481, "text": "your", "start": 39321, "speaker": "A", "confidence": 0.99707}, {"end": 39993, "text": "needs,", "start": 39513, "speaker": "A", "confidence": 0.99941}, {"end": 40353, "text": "would", "start": 40129, "speaker": "A", "confidence": 0.99802}, {"end": 40497, "text": "you", "start": 40369, "speaker": "A", "confidence": 0.99752}, {"end": 40657, "text": "be", "start": 40521, "speaker": "A", "confidence": 0.99856}, {"end": 41001, "text": "interested", "start": 40681, "speaker": "A", "confidence": 0.99824}, {"end": 41201, "text": "in", "start": 41033, "speaker": "A", "confidence": 0.99511}, {"end": 41577, "text": "furthering", "start": 41233, "speaker": "A", "confidence": 0.9905}, {"end": 41857, "text": "your", "start": 41601, "speaker": "A", "confidence": 0.99821}, {"end": 42337, "text": "education", "start": 41921, "speaker": "A", "confidence": 0.99982}, {"end": 42633, "text": "in", "start": 42441, "speaker": "A", "confidence": 0.94603}, {"end": 42753, "text": "the", "start": 42649, "speaker": "A", "confidence": 0.99584}, {"end": 42897, "text": "next", "start": 42769, "speaker": "A", "confidence": 0.99827}, {"end": 43057, "text": "six", "start": 42921, "speaker": "A", "confidence": 0.98351}, {"end": 43241, "text": "months?", "start": 43081, "speaker": "A", "confidence": 0.82268}, {"end": 43417, "text": "Yeah,", "start": 43273, "speaker": "A", "confidence": 0.57062}, {"end": 43553, "text": "of", "start": 43441, "speaker": "A", "confidence": 0.99322}, {"end": 44153, "text": "course.", "start": 43569, "speaker": "A", "confidence": 0.99982}], "speaker": "A", "confidence": 0.95480484}, {"end": 47205, "text": "The course I'd like to take up would be computer engineering.", "start": 44329, "words": [{"end": 44617, "text": "The", "start": 44329, "speaker": "B", "confidence": 0.99793}, {"end": 44801, "text": "course", "start": 44641, "speaker": "B", "confidence": 0.9996}, {"end": 45033, "text": "I'd", "start": 44833, "speaker": "B", "confidence": 0.92853}, {"end": 45153, "text": "like", "start": 45049, "speaker": "B", "confidence": 0.99828}, {"end": 45297, "text": "to", "start": 45169, "speaker": "B", "confidence": 0.99628}, {"end": 45457, "text": "take", "start": 45321, "speaker": "B", "confidence": 0.99598}, {"end": 45641, "text": "up", "start": 45481, "speaker": "B", "confidence": 0.99608}, {"end": 45793, "text": "would", "start": 45673, "speaker": "B", "confidence": 0.97223}, {"end": 45985, "text": "be", "start": 45809, "speaker": "B", "confidence": 0.99686}, {"end": 46449, "text": "computer", "start": 46025, "speaker": "B", "confidence": 0.99806}, {"end": 47205, "text": "engineering.", "start": 46497, "speaker": "B", "confidence": 0.57812}], "speaker": "B", "confidence": 0.95072275}, {"end": 63005, "text": "Computer engineering. Okay. And, <PERSON>, I only need a few moments of your time, okay, to verify your information. Your first name is <PERSON>, and your last name is <PERSON>. Is this correct?", "start": 47705, "words": [{"end": 48265, "text": "Computer", "start": 47705, "speaker": "A", "confidence": 0.82846}, {"end": 48697, "text": "engineering.", "start": 48305, "speaker": "A", "confidence": 0.82065}, {"end": 49405, "text": "Okay.", "start": 48761, "speaker": "A", "confidence": 0.80027}, {"end": 51129, "text": "And,", "start": 50745, "speaker": "A", "confidence": 0.88487}, {"end": 51845, "text": "<PERSON>,", "start": 51177, "speaker": "A", "confidence": 0.85582}, {"end": 54077, "text": "I", "start": 53765, "speaker": "A", "confidence": 0.95493}, {"end": 54261, "text": "only", "start": 54101, "speaker": "A", "confidence": 0.98905}, {"end": 54461, "text": "need", "start": 54293, "speaker": "A", "confidence": 0.78539}, {"end": 54613, "text": "a", "start": 54493, "speaker": "A", "confidence": 0.94105}, {"end": 54757, "text": "few", "start": 54629, "speaker": "A", "confidence": 0.99921}, {"end": 55029, "text": "moments", "start": 54781, "speaker": "A", "confidence": 0.94268}, {"end": 55237, "text": "of", "start": 55077, "speaker": "A", "confidence": 0.95695}, {"end": 55469, "text": "your", "start": 55261, "speaker": "A", "confidence": 0.96808}, {"end": 56085, "text": "time,", "start": 55517, "speaker": "A", "confidence": 0.99949}, {"end": 56985, "text": "okay,", "start": 56245, "speaker": "A", "confidence": 0.75219}, {"end": 58077, "text": "to", "start": 57765, "speaker": "A", "confidence": 0.99411}, {"end": 58485, "text": "verify", "start": 58101, "speaker": "A", "confidence": 0.99141}, {"end": 58797, "text": "your", "start": 58525, "speaker": "A", "confidence": 0.995}, {"end": 59277, "text": "information.", "start": 58861, "speaker": "A", "confidence": 0.97821}, {"end": 59597, "text": "Your", "start": 59381, "speaker": "A", "confidence": 0.99356}, {"end": 59829, "text": "first", "start": 59621, "speaker": "A", "confidence": 0.99886}, {"end": 60037, "text": "name", "start": 59877, "speaker": "A", "confidence": 0.99846}, {"end": 60221, "text": "is", "start": 60061, "speaker": "A", "confidence": 0.83584}, {"end": 60645, "text": "<PERSON>,", "start": 60253, "speaker": "A", "confidence": 0.48309}, {"end": 60941, "text": "and", "start": 60725, "speaker": "A", "confidence": 0.99}, {"end": 61141, "text": "your", "start": 60973, "speaker": "A", "confidence": 0.99364}, {"end": 61317, "text": "last", "start": 61173, "speaker": "A", "confidence": 0.98439}, {"end": 61429, "text": "name", "start": 61341, "speaker": "A", "confidence": 0.99703}, {"end": 61581, "text": "is", "start": 61437, "speaker": "A", "confidence": 0.97459}, {"end": 61997, "text": "<PERSON>.", "start": 61613, "speaker": "A", "confidence": 0.52605}, {"end": 62133, "text": "Is", "start": 62021, "speaker": "A", "confidence": 0.92666}, {"end": 62373, "text": "this", "start": 62149, "speaker": "A", "confidence": 0.79455}, {"end": 63005, "text": "correct?", "start": 62429, "speaker": "A", "confidence": 0.58959}], "speaker": "A", "confidence": 0.8946706}, {"end": 63905, "text": "Yes.", "start": 63165, "words": [{"end": 63905, "text": "Yes.", "start": 63165, "speaker": "B", "confidence": 0.99415}], "speaker": "B", "confidence": 0.99415}, {"end": 73185, "text": "Okay, <PERSON>, now, if I may ask, if we can find school for you that meets your needs, would you be interested in furthering your education? Yeah, in the next six months?", "start": 64765, "words": [{"end": 65461, "text": "Okay,", "start": 64765, "speaker": "A", "confidence": 0.80903}, {"end": 66101, "text": "<PERSON>,", "start": 65573, "speaker": "A", "confidence": 0.56535}, {"end": 66437, "text": "now,", "start": 66213, "speaker": "A", "confidence": 0.9032}, {"end": 66573, "text": "if", "start": 66461, "speaker": "A", "confidence": 0.99703}, {"end": 66693, "text": "I", "start": 66589, "speaker": "A", "confidence": 0.9876}, {"end": 66861, "text": "may", "start": 66709, "speaker": "A", "confidence": 0.99084}, {"end": 67133, "text": "ask,", "start": 66893, "speaker": "A", "confidence": 0.99027}, {"end": 67309, "text": "if", "start": 67189, "speaker": "A", "confidence": 0.98722}, {"end": 67413, "text": "we", "start": 67317, "speaker": "A", "confidence": 0.99812}, {"end": 67533, "text": "can", "start": 67429, "speaker": "A", "confidence": 0.998}, {"end": 67773, "text": "find", "start": 67549, "speaker": "A", "confidence": 0.6915}, {"end": 68021, "text": "school", "start": 67829, "speaker": "A", "confidence": 0.99117}, {"end": 68149, "text": "for", "start": 68053, "speaker": "A", "confidence": 0.99587}, {"end": 68301, "text": "you", "start": 68157, "speaker": "A", "confidence": 0.99587}, {"end": 68477, "text": "that", "start": 68333, "speaker": "A", "confidence": 0.99746}, {"end": 68693, "text": "meets", "start": 68501, "speaker": "A", "confidence": 0.60683}, {"end": 68837, "text": "your", "start": 68709, "speaker": "A", "confidence": 0.98079}, {"end": 69117, "text": "needs,", "start": 68861, "speaker": "A", "confidence": 0.99726}, {"end": 69309, "text": "would", "start": 69181, "speaker": "A", "confidence": 0.99722}, {"end": 69437, "text": "you", "start": 69317, "speaker": "A", "confidence": 0.99738}, {"end": 69573, "text": "be", "start": 69461, "speaker": "A", "confidence": 0.99653}, {"end": 69837, "text": "interested", "start": 69589, "speaker": "A", "confidence": 0.99916}, {"end": 69997, "text": "in", "start": 69861, "speaker": "A", "confidence": 0.98841}, {"end": 70421, "text": "furthering", "start": 70021, "speaker": "A", "confidence": 0.9476}, {"end": 70645, "text": "your", "start": 70453, "speaker": "A", "confidence": 0.97724}, {"end": 71265, "text": "education?", "start": 70685, "speaker": "A", "confidence": 0.99981}, {"end": 72021, "text": "Yeah,", "start": 71685, "speaker": "A", "confidence": 0.22025}, {"end": 72173, "text": "in", "start": 72053, "speaker": "A", "confidence": 0.8425}, {"end": 72293, "text": "the", "start": 72189, "speaker": "A", "confidence": 0.90172}, {"end": 72461, "text": "next", "start": 72309, "speaker": "A", "confidence": 0.99852}, {"end": 72613, "text": "six", "start": 72493, "speaker": "A", "confidence": 0.97384}, {"end": 73185, "text": "months?", "start": 72629, "speaker": "A", "confidence": 0.78673}], "speaker": "A", "confidence": 0.9096975}, {"end": 74205, "text": "Definitely.", "start": 73485, "words": [{"end": 74205, "text": "Definitely.", "start": 73485, "speaker": "B", "confidence": 0.48811}], "speaker": "B", "confidence": 0.48811}, {"end": 81815, "text": "Thank you, <PERSON>. Okay, could you please verify your complete address, including the city, state, and the zip code?", "start": 74325, "words": [{"end": 74533, "text": "Thank", "start": 74325, "speaker": "A", "confidence": 0.94507}, {"end": 74677, "text": "you,", "start": 74549, "speaker": "A", "confidence": 0.99923}, {"end": 75345, "text": "<PERSON>.", "start": 74701, "speaker": "A", "confidence": 0.69785}, {"end": 76525, "text": "Okay,", "start": 75725, "speaker": "A", "confidence": 0.65007}, {"end": 76933, "text": "could", "start": 76685, "speaker": "A", "confidence": 0.99445}, {"end": 77053, "text": "you", "start": 76949, "speaker": "A", "confidence": 0.99718}, {"end": 77221, "text": "please", "start": 77069, "speaker": "A", "confidence": 0.99617}, {"end": 77685, "text": "verify", "start": 77253, "speaker": "A", "confidence": 0.57569}, {"end": 77901, "text": "your", "start": 77725, "speaker": "A", "confidence": 0.89864}, {"end": 78459, "text": "complete", "start": 77933, "speaker": "A", "confidence": 0.64758}, {"end": 79055, "text": "address,", "start": 78557, "speaker": "A", "confidence": 0.87938}, {"end": 79503, "text": "including", "start": 79175, "speaker": "A", "confidence": 0.98521}, {"end": 79751, "text": "the", "start": 79559, "speaker": "A", "confidence": 0.99351}, {"end": 80239, "text": "city,", "start": 79783, "speaker": "A", "confidence": 0.9994}, {"end": 80679, "text": "state,", "start": 80367, "speaker": "A", "confidence": 0.9963}, {"end": 80863, "text": "and", "start": 80727, "speaker": "A", "confidence": 0.99894}, {"end": 80983, "text": "the", "start": 80879, "speaker": "A", "confidence": 0.96519}, {"end": 81167, "text": "zip", "start": 80999, "speaker": "A", "confidence": 0.75273}, {"end": 81815, "text": "code?", "start": 81191, "speaker": "A", "confidence": 0.67151}], "speaker": "A", "confidence": 0.8760053}, {"end": 93001, "text": "All right, it's 1905 Bramblewood Drive, St. Cloud, Florida, 34769. Okay.", "start": 81935, "words": [{"end": 82095, "text": "All", "start": 81935, "speaker": "B", "confidence": 0.86469}, {"end": 82255, "text": "right,", "start": 82095, "speaker": "B", "confidence": 0.56205}, {"end": 82583, "text": "it's", "start": 82295, "speaker": "B", "confidence": 0.99176}, {"end": 83819, "text": "1905", "start": 82639, "speaker": "B", "confidence": 0.88977}, {"end": 85131, "text": "Bramblewood", "start": 84081, "speaker": "B", "confidence": 0.88977}, {"end": 86443, "text": "Drive,", "start": 85393, "speaker": "B", "confidence": 0.88977}, {"end": 87754, "text": "St.", "start": 86705, "speaker": "B", "confidence": 0.88977}, {"end": 89066, "text": "Cloud,", "start": 88017, "speaker": "B", "confidence": 0.88977}, {"end": 90378, "text": "Florida,", "start": 89328, "speaker": "B", "confidence": 0.88977}, {"end": 91689, "text": "34769.", "start": 90640, "speaker": "B", "confidence": 0.88977}, {"end": 93001, "text": "Okay.", "start": 91952, "speaker": "B", "confidence": 0.88977}], "speaker": "B", "confidence": 0.8669691}, {"end": 99560, "text": "Is the street number 1905?", "start": 93264, "words": [{"end": 94313, "text": "Is", "start": 93264, "speaker": "A", "confidence": 0.88977}, {"end": 95625, "text": "the", "start": 94575, "speaker": "A", "confidence": 0.88977}, {"end": 96936, "text": "street", "start": 95887, "speaker": "A", "confidence": 0.88977}, {"end": 98248, "text": "number", "start": 97199, "speaker": "A", "confidence": 0.88977}, {"end": 99560, "text": "1905?", "start": 98510, "speaker": "A", "confidence": 0.88977}], "speaker": "A", "confidence": 0.88977}, {"end": 102315, "text": "Yeah, 1905.", "start": 99822, "words": [{"end": 100872, "text": "Yeah,", "start": 99822, "speaker": "B", "confidence": 0.88977}, {"end": 102315, "text": "1905.", "start": 101134, "speaker": "B", "confidence": 0.88977}], "speaker": "B", "confidence": 0.88977}, {"end": 106497, "text": "And the street name is Ramblewood. Right. Is that correct?", "start": 102775, "words": [{"end": 103087, "text": "And", "start": 102775, "speaker": "A", "confidence": 0.99815}, {"end": 103199, "text": "the", "start": 103111, "speaker": "A", "confidence": 0.97988}, {"end": 103399, "text": "street", "start": 103207, "speaker": "A", "confidence": 0.60564}, {"end": 103655, "text": "name", "start": 103447, "speaker": "A", "confidence": 0.55876}, {"end": 103925, "text": "is", "start": 103695, "speaker": "A", "confidence": 0.79552}, {"end": 104841, "text": "Ram<PERSON>wood.", "start": 103975, "speaker": "A", "confidence": 0.16168}, {"end": 105441, "text": "Right.", "start": 104953, "speaker": "A", "confidence": 0.30768}, {"end": 105753, "text": "Is", "start": 105553, "speaker": "A", "confidence": 0.95258}, {"end": 105945, "text": "that", "start": 105769, "speaker": "A", "confidence": 0.88056}, {"end": 106497, "text": "correct?", "start": 105985, "speaker": "A", "confidence": 0.9288}], "speaker": "A", "confidence": 0.716925}, {"end": 107249, "text": "That's correct.", "start": 106641, "words": [{"end": 107001, "text": "That's", "start": 106641, "speaker": "B", "confidence": 0.99523}, {"end": 107249, "text": "correct.", "start": 107033, "speaker": "B", "confidence": 0.99961}], "speaker": "B", "confidence": 0.99742}, {"end": 116417, "text": "Okay. Okay, so it's 1905 Bramblewood Drive, St. Cloud, Florida, 34769. Yes. Correct.", "start": 107297, "words": [{"end": 107761, "text": "Okay.", "start": 107297, "speaker": "A", "confidence": 0.83842}, {"end": 108177, "text": "Okay,", "start": 107873, "speaker": "A", "confidence": 0.80582}, {"end": 108337, "text": "so", "start": 108201, "speaker": "A", "confidence": 0.99681}, {"end": 108569, "text": "it's", "start": 108361, "speaker": "A", "confidence": 0.82291}, {"end": 109476, "text": "1905", "start": 108617, "speaker": "A", "confidence": 0.86642}, {"end": 110432, "text": "Bramblewood", "start": 109667, "speaker": "A", "confidence": 0.86642}, {"end": 111387, "text": "Drive,", "start": 110623, "speaker": "A", "confidence": 0.86642}, {"end": 112343, "text": "St.", "start": 111578, "speaker": "A", "confidence": 0.86642}, {"end": 113298, "text": "Cloud,", "start": 112534, "speaker": "A", "confidence": 0.86642}, {"end": 114254, "text": "Florida,", "start": 113489, "speaker": "A", "confidence": 0.86642}, {"end": 115305, "text": "34769.", "start": 114445, "speaker": "A", "confidence": 0.86642}, {"end": 115825, "text": "Yes.", "start": 115465, "speaker": "A", "confidence": 0.28854}, {"end": 116417, "text": "Correct.", "start": 115865, "speaker": "A", "confidence": 0.49466}], "speaker": "A", "confidence": 0.79323846}, {"end": 117065, "text": "Yep.", "start": 116561, "words": [{"end": 117065, "text": "Yep.", "start": 116561, "speaker": "B", "confidence": 0.50213}], "speaker": "B", "confidence": 0.50213}, {"end": 120325, "text": "Okay, and could you please verify your email address?", "start": 117145, "words": [{"end": 117729, "text": "Okay,", "start": 117145, "speaker": "A", "confidence": 0.90179}, {"end": 118097, "text": "and", "start": 117857, "speaker": "A", "confidence": 0.98822}, {"end": 118233, "text": "could", "start": 118121, "speaker": "A", "confidence": 0.99041}, {"end": 118377, "text": "you", "start": 118249, "speaker": "A", "confidence": 0.99812}, {"end": 118561, "text": "please", "start": 118401, "speaker": "A", "confidence": 0.99786}, {"end": 119025, "text": "verify", "start": 118593, "speaker": "A", "confidence": 0.64862}, {"end": 119337, "text": "your", "start": 119065, "speaker": "A", "confidence": 0.99971}, {"end": 119673, "text": "email", "start": 119401, "speaker": "A", "confidence": 0.99799}, {"end": 120325, "text": "address?", "start": 119729, "speaker": "A", "confidence": 0.9999}], "speaker": "A", "confidence": 0.94695777}, {"end": 124725, "text": "It'<NAME_EMAIL>.", "start": 120625, "words": [{"end": 121169, "text": "It's", "start": 120625, "speaker": "B", "confidence": 0.96819}, {"end": 122049, "text": "pella", "start": 121257, "speaker": "B", "confidence": 0.51185}, {"end": 124725, "text": "<EMAIL>.", "start": 122177, "speaker": "B", "confidence": 0.72893}], "speaker": "B", "confidence": 0.73632336}, {"end": 128937, "text": "Thank you so much for the verification.", "start": 127145, "words": [{"end": 127409, "text": "Thank", "start": 127145, "speaker": "A", "confidence": 0.68775}, {"end": 127465, "text": "you", "start": 127417, "speaker": "A", "confidence": 0.80303}, {"end": 127529, "text": "so", "start": 127465, "speaker": "A", "confidence": 0.75208}, {"end": 127633, "text": "much", "start": 127537, "speaker": "A", "confidence": 0.99188}, {"end": 127753, "text": "for", "start": 127649, "speaker": "A", "confidence": 0.80284}, {"end": 127897, "text": "the", "start": 127769, "speaker": "A", "confidence": 0.97582}, {"end": 128937, "text": "verification.", "start": 127921, "speaker": "A", "confidence": 0.60764}], "speaker": "A", "confidence": 0.8030057}, {"end": 129847, "text": "Yeah.", "start": 129121, "words": [{"end": 129847, "text": "Yeah.", "start": 129121, "speaker": "B", "confidence": 0.43517}], "speaker": "B", "confidence": 0.43517}, {"end": 133107, "text": "Now, you mentioned computer engineering, right?", "start": 130001, "words": [{"end": 130695, "text": "Now,", "start": 130001, "speaker": "A", "confidence": 0.97821}, {"end": 131371, "text": "you", "start": 131035, "speaker": "A", "confidence": 0.9945}, {"end": 131667, "text": "mentioned", "start": 131403, "speaker": "A", "confidence": 0.98598}, {"end": 132059, "text": "computer", "start": 131691, "speaker": "A", "confidence": 0.99831}, {"end": 132595, "text": "engineering,", "start": 132107, "speaker": "A", "confidence": 0.51139}, {"end": 133107, "text": "right?", "start": 132675, "speaker": "A", "confidence": 0.99382}], "speaker": "A", "confidence": 0.9103683}, {"end": 133895, "text": "Mm.", "start": 133211, "words": [{"end": 133895, "text": "Mm.", "start": 133211, "speaker": "B", "confidence": 0.33569}], "speaker": "B", "confidence": 0.33569}, {"end": 137611, "text": "May I ask, what degree type were you looking to obtain?", "start": 134835, "words": [{"end": 135147, "text": "May", "start": 134835, "speaker": "A", "confidence": 0.91497}, {"end": 135331, "text": "I", "start": 135171, "speaker": "A", "confidence": 0.996}, {"end": 135675, "text": "ask,", "start": 135363, "speaker": "A", "confidence": 0.9894}, {"end": 135971, "text": "what", "start": 135755, "speaker": "A", "confidence": 0.99857}, {"end": 136299, "text": "degree", "start": 136003, "speaker": "A", "confidence": 0.95628}, {"end": 136611, "text": "type", "start": 136347, "speaker": "A", "confidence": 0.99582}, {"end": 136763, "text": "were", "start": 136643, "speaker": "A", "confidence": 0.89465}, {"end": 136931, "text": "you", "start": 136779, "speaker": "A", "confidence": 0.99415}, {"end": 137131, "text": "looking", "start": 136963, "speaker": "A", "confidence": 0.9992}, {"end": 137307, "text": "to", "start": 137163, "speaker": "A", "confidence": 0.99666}, {"end": 137611, "text": "obtain?", "start": 137331, "speaker": "A", "confidence": 0.99158}], "speaker": "A", "confidence": 0.97520727}, {"end": 149771, "text": "Is it associate or whatever I need to do? If I get in the first door, I do associate. Probably move my way up the ladder, you know? Okay, so get my first one and then keep on going.", "start": 137683, "words": [{"end": 137843, "text": "Is", "start": 137683, "speaker": "B", "confidence": 0.75595}, {"end": 138083, "text": "it", "start": 137859, "speaker": "B", "confidence": 0.92502}, {"end": 139055, "text": "associate", "start": 138139, "speaker": "B", "confidence": 0.7714}, {"end": 140211, "text": "or", "start": 139635, "speaker": "B", "confidence": 0.79261}, {"end": 140699, "text": "whatever", "start": 140323, "speaker": "B", "confidence": 0.9978}, {"end": 140907, "text": "I", "start": 140747, "speaker": "B", "confidence": 0.99664}, {"end": 141091, "text": "need", "start": 140931, "speaker": "B", "confidence": 0.99762}, {"end": 141243, "text": "to", "start": 141123, "speaker": "B", "confidence": 0.99774}, {"end": 141819, "text": "do?", "start": 141259, "speaker": "B", "confidence": 0.99666}, {"end": 142243, "text": "If", "start": 141987, "speaker": "B", "confidence": 0.99301}, {"end": 142435, "text": "I", "start": 142259, "speaker": "B", "confidence": 0.99651}, {"end": 142627, "text": "get", "start": 142475, "speaker": "B", "confidence": 0.99885}, {"end": 142811, "text": "in", "start": 142651, "speaker": "B", "confidence": 0.9592}, {"end": 142963, "text": "the", "start": 142843, "speaker": "B", "confidence": 0.99748}, {"end": 143155, "text": "first", "start": 142979, "speaker": "B", "confidence": 0.99822}, {"end": 143347, "text": "door,", "start": 143195, "speaker": "B", "confidence": 0.48133}, {"end": 143483, "text": "I", "start": 143371, "speaker": "B", "confidence": 0.9702}, {"end": 143651, "text": "do", "start": 143499, "speaker": "B", "confidence": 0.98132}, {"end": 144219, "text": "associate.", "start": 143683, "speaker": "B", "confidence": 0.49121}, {"end": 144627, "text": "Probably", "start": 144307, "speaker": "B", "confidence": 0.78938}, {"end": 144915, "text": "move", "start": 144691, "speaker": "B", "confidence": 0.95686}, {"end": 145131, "text": "my", "start": 144955, "speaker": "B", "confidence": 0.99935}, {"end": 145283, "text": "way", "start": 145163, "speaker": "B", "confidence": 0.99803}, {"end": 145403, "text": "up", "start": 145299, "speaker": "B", "confidence": 0.99131}, {"end": 145547, "text": "the", "start": 145419, "speaker": "B", "confidence": 0.97355}, {"end": 146011, "text": "ladder,", "start": 145571, "speaker": "B", "confidence": 0.82974}, {"end": 146219, "text": "you", "start": 146083, "speaker": "B", "confidence": 0.88974}, {"end": 146707, "text": "know?", "start": 146227, "speaker": "B", "confidence": 0.79785}, {"end": 147331, "text": "Okay,", "start": 146851, "speaker": "B", "confidence": 0.71177}, {"end": 147659, "text": "so", "start": 147403, "speaker": "B", "confidence": 0.85514}, {"end": 147843, "text": "get", "start": 147707, "speaker": "B", "confidence": 0.9397}, {"end": 148011, "text": "my", "start": 147859, "speaker": "B", "confidence": 0.99713}, {"end": 148211, "text": "first", "start": 148043, "speaker": "B", "confidence": 0.99974}, {"end": 148387, "text": "one", "start": 148243, "speaker": "B", "confidence": 0.99352}, {"end": 148499, "text": "and", "start": 148411, "speaker": "B", "confidence": 0.99333}, {"end": 148675, "text": "then", "start": 148507, "speaker": "B", "confidence": 0.96923}, {"end": 148867, "text": "keep", "start": 148715, "speaker": "B", "confidence": 0.99984}, {"end": 149075, "text": "on", "start": 148891, "speaker": "B", "confidence": 0.99761}, {"end": 149771, "text": "going.", "start": 149115, "speaker": "B", "confidence": 0.99938}], "speaker": "B", "confidence": 0.91746074}, {"end": 155807, "text": "Okay, so would be associate degree for the moment?", "start": 149963, "words": [{"end": 150775, "text": "Okay,", "start": 149963, "speaker": "A", "confidence": 0.76853}, {"end": 152055, "text": "so", "start": 151315, "speaker": "A", "confidence": 0.98589}, {"end": 152787, "text": "would", "start": 152475, "speaker": "A", "confidence": 0.39448}, {"end": 152947, "text": "be", "start": 152811, "speaker": "A", "confidence": 0.88741}, {"end": 153683, "text": "associate", "start": 152971, "speaker": "A", "confidence": 0.7234}, {"end": 154603, "text": "degree", "start": 153819, "speaker": "A", "confidence": 0.84448}, {"end": 155043, "text": "for", "start": 154779, "speaker": "A", "confidence": 0.99192}, {"end": 155187, "text": "the", "start": 155059, "speaker": "A", "confidence": 0.99581}, {"end": 155807, "text": "moment?", "start": 155211, "speaker": "A", "confidence": 0.99793}], "speaker": "A", "confidence": 0.8433167}, {"end": 156755, "text": "Yep.", "start": 155971, "words": [{"end": 156755, "text": "Yep.", "start": 155971, "speaker": "B", "confidence": 0.41607}], "speaker": "B", "confidence": 0.41607}, {"end": 169395, "text": "Okay. <PERSON>, you've mentioned that you're 21. I'm sorry, 29 years old now, if I may ask. Well, what's the highest level of education?", "start": 159255, "words": [{"end": 159687, "text": "Okay.", "start": 159255, "speaker": "A", "confidence": 0.44256}, {"end": 159951, "text": "<PERSON>,", "start": 159711, "speaker": "A", "confidence": 0.99308}, {"end": 160143, "text": "you've", "start": 159983, "speaker": "A", "confidence": 0.71789}, {"end": 160383, "text": "mentioned", "start": 160159, "speaker": "A", "confidence": 0.76675}, {"end": 160503, "text": "that", "start": 160399, "speaker": "A", "confidence": 0.96366}, {"end": 160735, "text": "you're", "start": 160519, "speaker": "A", "confidence": 0.92629}, {"end": 161185, "text": "21.", "start": 160775, "speaker": "A", "confidence": 0.97708}, {"end": 161641, "text": "I'm", "start": 161276, "speaker": "A", "confidence": 0.97708}, {"end": 162097, "text": "sorry,", "start": 161732, "speaker": "A", "confidence": 0.97708}, {"end": 162599, "text": "29", "start": 162188, "speaker": "A", "confidence": 0.97708}, {"end": 162903, "text": "years", "start": 162647, "speaker": "A", "confidence": 0.99433}, {"end": 163559, "text": "old", "start": 162959, "speaker": "A", "confidence": 0.99948}, {"end": 164055, "text": "now,", "start": 163727, "speaker": "A", "confidence": 0.95362}, {"end": 164223, "text": "if", "start": 164095, "speaker": "A", "confidence": 0.99871}, {"end": 164367, "text": "I", "start": 164239, "speaker": "A", "confidence": 0.99784}, {"end": 164695, "text": "may", "start": 164391, "speaker": "A", "confidence": 0.99377}, {"end": 165395, "text": "ask.", "start": 164775, "speaker": "A", "confidence": 0.99392}, {"end": 167527, "text": "Well,", "start": 167215, "speaker": "A", "confidence": 0.48665}, {"end": 167727, "text": "what's", "start": 167551, "speaker": "A", "confidence": 0.51296}, {"end": 167887, "text": "the", "start": 167751, "speaker": "A", "confidence": 0.99931}, {"end": 168175, "text": "highest", "start": 167911, "speaker": "A", "confidence": 0.99691}, {"end": 168415, "text": "level", "start": 168215, "speaker": "A", "confidence": 0.99908}, {"end": 168727, "text": "of", "start": 168455, "speaker": "A", "confidence": 0.99782}, {"end": 169395, "text": "education?", "start": 168791, "speaker": "A", "confidence": 0.99883}], "speaker": "A", "confidence": 0.90174085}, {"end": 190725, "text": "I dropped out when I was in the 11th grade, and I started doing plumbing. I've actually been doing plumbing for 13 years. I'm a registered apprentice. I actually only have to take the test to become a journeyman's. A journeyman to open my own company. And I'm not too fond of plumbing, you know, saying I need something, I want to do something else besides plumbing. For the rest of my life.", "start": 170215, "words": [{"end": 170623, "text": "I", "start": 170215, "speaker": "B", "confidence": 0.99292}, {"end": 171031, "text": "dropped", "start": 170679, "speaker": "B", "confidence": 0.99033}, {"end": 171303, "text": "out", "start": 171063, "speaker": "B", "confidence": 0.99872}, {"end": 171719, "text": "when", "start": 171359, "speaker": "B", "confidence": 0.99573}, {"end": 172031, "text": "I", "start": 171807, "speaker": "B", "confidence": 0.99748}, {"end": 172255, "text": "was", "start": 172063, "speaker": "B", "confidence": 0.99913}, {"end": 172423, "text": "in", "start": 172295, "speaker": "B", "confidence": 0.99784}, {"end": 172615, "text": "the", "start": 172439, "speaker": "B", "confidence": 0.99009}, {"end": 173039, "text": "11th", "start": 172655, "speaker": "B", "confidence": 0.99645}, {"end": 173559, "text": "grade,", "start": 173087, "speaker": "B", "confidence": 0.94633}, {"end": 173927, "text": "and", "start": 173687, "speaker": "B", "confidence": 0.99756}, {"end": 174111, "text": "I", "start": 173951, "speaker": "B", "confidence": 0.99581}, {"end": 174335, "text": "started", "start": 174143, "speaker": "B", "confidence": 0.99943}, {"end": 174551, "text": "doing", "start": 174375, "speaker": "B", "confidence": 0.99734}, {"end": 174959, "text": "plumbing.", "start": 174583, "speaker": "B", "confidence": 0.90704}, {"end": 175247, "text": "I've", "start": 175007, "speaker": "B", "confidence": 0.99191}, {"end": 175479, "text": "actually", "start": 175271, "speaker": "B", "confidence": 0.99885}, {"end": 175663, "text": "been", "start": 175527, "speaker": "B", "confidence": 0.99932}, {"end": 175831, "text": "doing", "start": 175679, "speaker": "B", "confidence": 0.99715}, {"end": 176151, "text": "plumbing", "start": 175863, "speaker": "B", "confidence": 0.88204}, {"end": 176351, "text": "for", "start": 176183, "speaker": "B", "confidence": 0.99845}, {"end": 176695, "text": "13", "start": 176383, "speaker": "B", "confidence": 0.99888}, {"end": 177279, "text": "years.", "start": 176775, "speaker": "B", "confidence": 0.99906}, {"end": 177647, "text": "I'm", "start": 177407, "speaker": "B", "confidence": 0.96723}, {"end": 177783, "text": "a", "start": 177671, "speaker": "B", "confidence": 0.99403}, {"end": 178191, "text": "registered", "start": 177799, "speaker": "B", "confidence": 0.52987}, {"end": 178599, "text": "apprentice.", "start": 178223, "speaker": "B", "confidence": 0.57967}, {"end": 178807, "text": "I", "start": 178647, "speaker": "B", "confidence": 0.63548}, {"end": 179015, "text": "actually", "start": 178831, "speaker": "B", "confidence": 0.99128}, {"end": 179207, "text": "only", "start": 179055, "speaker": "B", "confidence": 0.86786}, {"end": 179319, "text": "have", "start": 179231, "speaker": "B", "confidence": 0.96855}, {"end": 179399, "text": "to", "start": 179327, "speaker": "B", "confidence": 0.99793}, {"end": 179551, "text": "take", "start": 179407, "speaker": "B", "confidence": 0.99883}, {"end": 179703, "text": "the", "start": 179583, "speaker": "B", "confidence": 0.99743}, {"end": 179871, "text": "test", "start": 179719, "speaker": "B", "confidence": 0.99823}, {"end": 180023, "text": "to", "start": 179903, "speaker": "B", "confidence": 0.9244}, {"end": 180215, "text": "become", "start": 180039, "speaker": "B", "confidence": 0.98657}, {"end": 180835, "text": "a", "start": 180255, "speaker": "B", "confidence": 0.58216}, {"end": 182617, "text": "journeyman's.", "start": 181785, "speaker": "B", "confidence": 0.24424}, {"end": 182833, "text": "A", "start": 182681, "speaker": "B", "confidence": 0.74641}, {"end": 183289, "text": "journeyman", "start": 182849, "speaker": "B", "confidence": 0.26816}, {"end": 183497, "text": "to", "start": 183337, "speaker": "B", "confidence": 0.58017}, {"end": 183681, "text": "open", "start": 183521, "speaker": "B", "confidence": 0.91849}, {"end": 183833, "text": "my", "start": 183713, "speaker": "B", "confidence": 0.99218}, {"end": 184025, "text": "own", "start": 183849, "speaker": "B", "confidence": 0.99696}, {"end": 184313, "text": "company.", "start": 184065, "speaker": "B", "confidence": 0.99955}, {"end": 184513, "text": "And", "start": 184369, "speaker": "B", "confidence": 0.99378}, {"end": 184969, "text": "I'm", "start": 184529, "speaker": "B", "confidence": 0.58914}, {"end": 185337, "text": "not", "start": 185097, "speaker": "B", "confidence": 0.99982}, {"end": 185545, "text": "too", "start": 185361, "speaker": "B", "confidence": 0.99869}, {"end": 185761, "text": "fond", "start": 185585, "speaker": "B", "confidence": 0.93926}, {"end": 185937, "text": "of", "start": 185793, "speaker": "B", "confidence": 0.9997}, {"end": 186425, "text": "plumbing,", "start": 185961, "speaker": "B", "confidence": 0.48964}, {"end": 186649, "text": "you", "start": 186505, "speaker": "B", "confidence": 0.98847}, {"end": 186873, "text": "know,", "start": 186657, "speaker": "B", "confidence": 0.99467}, {"end": 187313, "text": "saying", "start": 186929, "speaker": "B", "confidence": 0.81847}, {"end": 187569, "text": "I", "start": 187409, "speaker": "B", "confidence": 0.83563}, {"end": 187697, "text": "need", "start": 187577, "speaker": "B", "confidence": 0.97442}, {"end": 187881, "text": "something,", "start": 187721, "speaker": "B", "confidence": 0.35552}, {"end": 188033, "text": "I", "start": 187913, "speaker": "B", "confidence": 0.99414}, {"end": 188129, "text": "want", "start": 188049, "speaker": "B", "confidence": 0.99116}, {"end": 188233, "text": "to", "start": 188137, "speaker": "B", "confidence": 0.99786}, {"end": 188377, "text": "do", "start": 188249, "speaker": "B", "confidence": 0.99879}, {"end": 188585, "text": "something", "start": 188401, "speaker": "B", "confidence": 0.99904}, {"end": 188801, "text": "else", "start": 188625, "speaker": "B", "confidence": 0.9988}, {"end": 189201, "text": "besides", "start": 188833, "speaker": "B", "confidence": 0.5293}, {"end": 189497, "text": "plumbing.", "start": 189233, "speaker": "B", "confidence": 0.79511}, {"end": 189633, "text": "For", "start": 189521, "speaker": "B", "confidence": 0.99749}, {"end": 189705, "text": "the", "start": 189649, "speaker": "B", "confidence": 0.99694}, {"end": 189841, "text": "rest", "start": 189705, "speaker": "B", "confidence": 0.94323}, {"end": 189969, "text": "of", "start": 189873, "speaker": "B", "confidence": 0.99896}, {"end": 190121, "text": "my", "start": 189977, "speaker": "B", "confidence": 0.99953}, {"end": 190725, "text": "life.", "start": 190153, "speaker": "B", "confidence": 0.99882}], "speaker": "B", "confidence": 0.899314}, {"end": 194801, "text": "Okay. And do you have a diploma or a GED?", "start": 191225, "words": [{"end": 192041, "text": "Okay.", "start": 191225, "speaker": "A", "confidence": 0.57747}, {"end": 192481, "text": "And", "start": 192193, "speaker": "A", "confidence": 0.98464}, {"end": 192633, "text": "do", "start": 192513, "speaker": "A", "confidence": 0.89127}, {"end": 192753, "text": "you", "start": 192649, "speaker": "A", "confidence": 0.99412}, {"end": 192897, "text": "have", "start": 192769, "speaker": "A", "confidence": 0.99934}, {"end": 193081, "text": "a", "start": 192921, "speaker": "A", "confidence": 0.9908}, {"end": 193513, "text": "diploma", "start": 193113, "speaker": "A", "confidence": 0.99546}, {"end": 193785, "text": "or", "start": 193569, "speaker": "A", "confidence": 0.99722}, {"end": 193953, "text": "a", "start": 193825, "speaker": "A", "confidence": 0.9566}, {"end": 194801, "text": "GED?", "start": 193969, "speaker": "A", "confidence": 0.81498}], "speaker": "A", "confidence": 0.92019}, {"end": 196129, "text": "I have a GED.", "start": 194993, "words": [{"end": 195273, "text": "I", "start": 194993, "speaker": "B", "confidence": 0.99487}, {"end": 195369, "text": "have", "start": 195289, "speaker": "B", "confidence": 0.99964}, {"end": 195473, "text": "a", "start": 195377, "speaker": "B", "confidence": 0.99541}, {"end": 196129, "text": "GED.", "start": 195489, "speaker": "B", "confidence": 0.90896}], "speaker": "B", "confidence": 0.97472}, {"end": 202125, "text": "GED. Okay. And what year did you obtain your GED?", "start": 196257, "words": [{"end": 196713, "text": "GED.", "start": 196257, "speaker": "A", "confidence": 0.47753}, {"end": 197445, "text": "Okay.", "start": 196769, "speaker": "A", "confidence": 0.72202}, {"end": 200097, "text": "And", "start": 199785, "speaker": "A", "confidence": 0.98891}, {"end": 200281, "text": "what", "start": 200121, "speaker": "A", "confidence": 0.99727}, {"end": 200457, "text": "year", "start": 200313, "speaker": "A", "confidence": 0.97488}, {"end": 200617, "text": "did", "start": 200481, "speaker": "A", "confidence": 0.97678}, {"end": 200753, "text": "you", "start": 200641, "speaker": "A", "confidence": 0.99807}, {"end": 201017, "text": "obtain", "start": 200769, "speaker": "A", "confidence": 0.91441}, {"end": 201329, "text": "your", "start": 201081, "speaker": "A", "confidence": 0.9941}, {"end": 202125, "text": "GED?", "start": 201377, "speaker": "A", "confidence": 0.86067}], "speaker": "A", "confidence": 0.890464}, {"end": 203885, "text": "1999.", "start": 202505, "words": [{"end": 203885, "text": "1999.", "start": 202505, "speaker": "B", "confidence": 0.97739}], "speaker": "B", "confidence": 0.97739}, {"end": 213373, "text": "Okay, and for your class type reference, would it be online, on campus or.", "start": 208765, "words": [{"end": 209189, "text": "Okay,", "start": 208765, "speaker": "A", "confidence": 0.85067}, {"end": 209397, "text": "and", "start": 209237, "speaker": "A", "confidence": 0.98019}, {"end": 209557, "text": "for", "start": 209421, "speaker": "A", "confidence": 0.99516}, {"end": 209741, "text": "your", "start": 209581, "speaker": "A", "confidence": 0.99586}, {"end": 209989, "text": "class", "start": 209773, "speaker": "A", "confidence": 0.9938}, {"end": 210285, "text": "type", "start": 210037, "speaker": "A", "confidence": 0.91132}, {"end": 210709, "text": "reference,", "start": 210325, "speaker": "A", "confidence": 0.33759}, {"end": 210917, "text": "would", "start": 210757, "speaker": "A", "confidence": 0.98097}, {"end": 211077, "text": "it", "start": 210941, "speaker": "A", "confidence": 0.98864}, {"end": 211309, "text": "be", "start": 211101, "speaker": "A", "confidence": 0.99909}, {"end": 211877, "text": "online,", "start": 211357, "speaker": "A", "confidence": 0.99955}, {"end": 212325, "text": "on", "start": 212021, "speaker": "A", "confidence": 0.98939}, {"end": 212909, "text": "campus", "start": 212365, "speaker": "A", "confidence": 0.90451}, {"end": 213373, "text": "or.", "start": 213037, "speaker": "A", "confidence": 0.64549}], "speaker": "A", "confidence": 0.89801645}, {"end": 216025, "text": "No, probably campus. Probably would be campus.", "start": 213429, "words": [{"end": 213621, "text": "No,", "start": 213429, "speaker": "B", "confidence": 0.41768}, {"end": 214053, "text": "probably", "start": 213653, "speaker": "B", "confidence": 0.80577}, {"end": 214621, "text": "campus.", "start": 214149, "speaker": "B", "confidence": 0.71762}, {"end": 214901, "text": "Probably", "start": 214693, "speaker": "B", "confidence": 0.6047}, {"end": 215077, "text": "would", "start": 214933, "speaker": "B", "confidence": 0.50529}, {"end": 215285, "text": "be", "start": 215101, "speaker": "B", "confidence": 0.99304}, {"end": 216025, "text": "campus.", "start": 215325, "speaker": "B", "confidence": 0.99594}], "speaker": "B", "confidence": 0.7200057}, {"end": 226941, "text": "Campus. Okay. Just in case we'll not be able to find a campus based school, would you be okay with an online school?", "start": 216485, "words": [{"end": 217037, "text": "Campus.", "start": 216485, "speaker": "A", "confidence": 0.61174}, {"end": 217785, "text": "Okay.", "start": 217101, "speaker": "A", "confidence": 0.58966}, {"end": 221653, "text": "Just", "start": 221365, "speaker": "A", "confidence": 0.98495}, {"end": 221749, "text": "in", "start": 221669, "speaker": "A", "confidence": 0.99394}, {"end": 222305, "text": "case", "start": 221757, "speaker": "A", "confidence": 0.62228}, {"end": 222933, "text": "we'll", "start": 222605, "speaker": "A", "confidence": 0.31225}, {"end": 223053, "text": "not", "start": 222949, "speaker": "A", "confidence": 0.95683}, {"end": 223149, "text": "be", "start": 223069, "speaker": "A", "confidence": 0.85827}, {"end": 223277, "text": "able", "start": 223157, "speaker": "A", "confidence": 0.62228}, {"end": 223437, "text": "to", "start": 223301, "speaker": "A", "confidence": 0.99722}, {"end": 223621, "text": "find", "start": 223461, "speaker": "A", "confidence": 0.99667}, {"end": 223773, "text": "a", "start": 223653, "speaker": "A", "confidence": 0.9879}, {"end": 224069, "text": "campus", "start": 223789, "speaker": "A", "confidence": 0.99718}, {"end": 224349, "text": "based", "start": 224117, "speaker": "A", "confidence": 0.95412}, {"end": 224653, "text": "school,", "start": 224397, "speaker": "A", "confidence": 0.99708}, {"end": 224853, "text": "would", "start": 224709, "speaker": "A", "confidence": 0.9879}, {"end": 224997, "text": "you", "start": 224869, "speaker": "A", "confidence": 0.98456}, {"end": 225133, "text": "be", "start": 225021, "speaker": "A", "confidence": 0.99724}, {"end": 225357, "text": "okay", "start": 225149, "speaker": "A", "confidence": 0.94228}, {"end": 225541, "text": "with", "start": 225381, "speaker": "A", "confidence": 0.98791}, {"end": 225669, "text": "an", "start": 225573, "speaker": "A", "confidence": 0.98133}, {"end": 226085, "text": "online", "start": 225677, "speaker": "A", "confidence": 0.55794}, {"end": 226941, "text": "school?", "start": 226205, "speaker": "A", "confidence": 0.99959}], "speaker": "A", "confidence": 0.86613566}, {"end": 227973, "text": "Yeah.", "start": 227133, "words": [{"end": 227973, "text": "Yeah.", "start": 227133, "speaker": "B", "confidence": 0.97021}], "speaker": "B", "confidence": 0.97021}, {"end": 232865, "text": "Okay. And are you a United States citizen?", "start": 228149, "words": [{"end": 228945, "text": "Okay.", "start": 228149, "speaker": "A", "confidence": 0.92082}, {"end": 231213, "text": "And", "start": 230925, "speaker": "A", "confidence": 0.94594}, {"end": 231333, "text": "are", "start": 231229, "speaker": "A", "confidence": 0.99641}, {"end": 231477, "text": "you", "start": 231349, "speaker": "A", "confidence": 0.9914}, {"end": 231613, "text": "a", "start": 231501, "speaker": "A", "confidence": 0.94855}, {"end": 231853, "text": "United", "start": 231629, "speaker": "A", "confidence": 0.99885}, {"end": 232125, "text": "States", "start": 231909, "speaker": "A", "confidence": 0.99572}, {"end": 232865, "text": "citizen?", "start": 232165, "speaker": "A", "confidence": 0.65415}], "speaker": "A", "confidence": 0.93148}, {"end": 234375, "text": "Yes, I am.", "start": 233315, "words": [{"end": 233627, "text": "Yes,", "start": 233315, "speaker": "B", "confidence": 0.90551}, {"end": 233787, "text": "I", "start": 233651, "speaker": "B", "confidence": 0.99451}, {"end": 234375, "text": "am.", "start": 233811, "speaker": "B", "confidence": 0.9955}], "speaker": "B", "confidence": 0.9651733}, {"end": 238027, "text": "Thank you. And are you associated with the United States military?", "start": 234875, "words": [{"end": 235187, "text": "Thank", "start": 234875, "speaker": "A", "confidence": 0.84414}, {"end": 235371, "text": "you.", "start": 235211, "speaker": "A", "confidence": 0.99703}, {"end": 235547, "text": "And", "start": 235403, "speaker": "A", "confidence": 0.87713}, {"end": 235683, "text": "are", "start": 235571, "speaker": "A", "confidence": 0.9951}, {"end": 235827, "text": "you", "start": 235699, "speaker": "A", "confidence": 0.99856}, {"end": 236267, "text": "associated", "start": 235851, "speaker": "A", "confidence": 0.62095}, {"end": 236483, "text": "with", "start": 236331, "speaker": "A", "confidence": 0.99555}, {"end": 236603, "text": "the", "start": 236499, "speaker": "A", "confidence": 0.89029}, {"end": 236843, "text": "United", "start": 236619, "speaker": "A", "confidence": 0.93724}, {"end": 237139, "text": "States", "start": 236899, "speaker": "A", "confidence": 0.99461}, {"end": 238027, "text": "military?", "start": 237187, "speaker": "A", "confidence": 0.91072}], "speaker": "A", "confidence": 0.91466546}, {"end": 239215, "text": "What was that?", "start": 238211, "words": [{"end": 238483, "text": "What", "start": 238211, "speaker": "B", "confidence": 0.99229}, {"end": 238627, "text": "was", "start": 238499, "speaker": "B", "confidence": 0.99465}, {"end": 239215, "text": "that?", "start": 238651, "speaker": "B", "confidence": 0.99534}], "speaker": "B", "confidence": 0.99409336}, {"end": 243139, "text": "I'm sorry? Are you associated with the United States military?", "start": 239635, "words": [{"end": 239963, "text": "I'm", "start": 239635, "speaker": "A", "confidence": 0.78812}, {"end": 240171, "text": "sorry?", "start": 239979, "speaker": "A", "confidence": 0.989}, {"end": 240299, "text": "Are", "start": 240203, "speaker": "A", "confidence": 0.99377}, {"end": 240547, "text": "you", "start": 240307, "speaker": "A", "confidence": 0.99932}, {"end": 241195, "text": "associated", "start": 240611, "speaker": "A", "confidence": 0.72997}, {"end": 241491, "text": "with", "start": 241275, "speaker": "A", "confidence": 0.99874}, {"end": 241691, "text": "the", "start": 241523, "speaker": "A", "confidence": 0.98712}, {"end": 241963, "text": "United", "start": 241723, "speaker": "A", "confidence": 0.996}, {"end": 242283, "text": "States", "start": 242019, "speaker": "A", "confidence": 0.9994}, {"end": 243139, "text": "military?", "start": 242339, "speaker": "A", "confidence": 0.99968}], "speaker": "A", "confidence": 0.948112}, {"end": 244295, "text": "No, I'm not.", "start": 243307, "words": [{"end": 243563, "text": "No,", "start": 243307, "speaker": "B", "confidence": 0.99334}, {"end": 243707, "text": "I'm", "start": 243579, "speaker": "B", "confidence": 0.96241}, {"end": 244295, "text": "not.", "start": 243731, "speaker": "B", "confidence": 0.99799}], "speaker": "B", "confidence": 0.98458}, {"end": 253535, "text": "Okay. And what would be the best time for a school enrollment counselor to contact you in the morning, afternoon, or evening?", "start": 245355, "words": [{"end": 246175, "text": "Okay.", "start": 245355, "speaker": "A", "confidence": 0.47456}, {"end": 247747, "text": "And", "start": 247435, "speaker": "A", "confidence": 0.93575}, {"end": 247931, "text": "what", "start": 247771, "speaker": "A", "confidence": 0.99959}, {"end": 248083, "text": "would", "start": 247963, "speaker": "A", "confidence": 0.99887}, {"end": 248179, "text": "be", "start": 248099, "speaker": "A", "confidence": 0.99912}, {"end": 248331, "text": "the", "start": 248187, "speaker": "A", "confidence": 0.99863}, {"end": 248555, "text": "best", "start": 248363, "speaker": "A", "confidence": 0.99954}, {"end": 248747, "text": "time", "start": 248595, "speaker": "A", "confidence": 0.99532}, {"end": 248907, "text": "for", "start": 248771, "speaker": "A", "confidence": 0.99647}, {"end": 249067, "text": "a", "start": 248931, "speaker": "A", "confidence": 0.89935}, {"end": 249275, "text": "school", "start": 249091, "speaker": "A", "confidence": 0.99876}, {"end": 249659, "text": "enrollment", "start": 249315, "speaker": "A", "confidence": 0.886}, {"end": 250131, "text": "counselor", "start": 249707, "speaker": "A", "confidence": 0.62801}, {"end": 250355, "text": "to", "start": 250163, "speaker": "A", "confidence": 0.99182}, {"end": 250643, "text": "contact", "start": 250395, "speaker": "A", "confidence": 0.99083}, {"end": 251011, "text": "you", "start": 250699, "speaker": "A", "confidence": 0.71397}, {"end": 251219, "text": "in", "start": 251083, "speaker": "A", "confidence": 0.99682}, {"end": 251371, "text": "the", "start": 251227, "speaker": "A", "confidence": 0.99717}, {"end": 251955, "text": "morning,", "start": 251403, "speaker": "A", "confidence": 0.99991}, {"end": 252611, "text": "afternoon,", "start": 252115, "speaker": "A", "confidence": 0.99506}, {"end": 252859, "text": "or", "start": 252643, "speaker": "A", "confidence": 0.9988}, {"end": 253535, "text": "evening?", "start": 252907, "speaker": "A", "confidence": 0.70444}], "speaker": "A", "confidence": 0.9181268}, {"end": 257375, "text": "Pretty much any time of the day.", "start": 255755, "words": [{"end": 256019, "text": "Pretty", "start": 255755, "speaker": "B", "confidence": 0.99826}, {"end": 256171, "text": "much", "start": 256027, "speaker": "B", "confidence": 0.9997}, {"end": 256371, "text": "any", "start": 256203, "speaker": "B", "confidence": 0.99942}, {"end": 256547, "text": "time", "start": 256403, "speaker": "B", "confidence": 0.98795}, {"end": 256659, "text": "of", "start": 256571, "speaker": "B", "confidence": 0.89067}, {"end": 256787, "text": "the", "start": 256667, "speaker": "B", "confidence": 0.98761}, {"end": 257375, "text": "day.", "start": 256811, "speaker": "B", "confidence": 0.99823}], "speaker": "B", "confidence": 0.9802629}, {"end": 262109, "text": "And what is your exact date of birth?", "start": 260005, "words": [{"end": 260341, "text": "And", "start": 260005, "speaker": "A", "confidence": 0.96669}, {"end": 260517, "text": "what", "start": 260373, "speaker": "A", "confidence": 0.9995}, {"end": 260653, "text": "is", "start": 260541, "speaker": "A", "confidence": 0.99723}, {"end": 260821, "text": "your", "start": 260669, "speaker": "A", "confidence": 0.99908}, {"end": 261125, "text": "exact", "start": 260853, "speaker": "A", "confidence": 0.9982}, {"end": 261317, "text": "date", "start": 261165, "speaker": "A", "confidence": 0.95517}, {"end": 261477, "text": "of", "start": 261341, "speaker": "A", "confidence": 0.98827}, {"end": 262109, "text": "birth?", "start": 261501, "speaker": "A", "confidence": 0.56287}], "speaker": "A", "confidence": 0.93337625}, {"end": 263928, "text": "10, 1580.", "start": 262277, "words": [{"end": 263059, "text": "10,", "start": 262277, "speaker": "B", "confidence": 0.91638}, {"end": 263928, "text": "1580.", "start": 263233, "speaker": "B", "confidence": 0.91638}], "speaker": "B", "confidence": 0.91638}, {"end": 270101, "text": "So that would be October 15th, 1980?", "start": 264102, "words": [{"end": 264798, "text": "So", "start": 264102, "speaker": "A", "confidence": 0.91638}, {"end": 265667, "text": "that", "start": 264971, "speaker": "A", "confidence": 0.91638}, {"end": 266536, "text": "would", "start": 265841, "speaker": "A", "confidence": 0.91638}, {"end": 267406, "text": "be", "start": 266710, "speaker": "A", "confidence": 0.91638}, {"end": 268275, "text": "October", "start": 267579, "speaker": "A", "confidence": 0.91638}, {"end": 269144, "text": "15th,", "start": 268449, "speaker": "A", "confidence": 0.91638}, {"end": 270101, "text": "1980?", "start": 269318, "speaker": "A", "confidence": 0.91638}], "speaker": "A", "confidence": 0.91638}, {"end": 271225, "text": "That's correct.", "start": 270253, "words": [{"end": 270605, "text": "That's", "start": 270253, "speaker": "B", "confidence": 0.9906}, {"end": 271225, "text": "correct.", "start": 270645, "speaker": "B", "confidence": 0.99958}], "speaker": "B", "confidence": 0.99509}, {"end": 300971, "text": "Okay. Okay, <PERSON>, if we can find school for you that meets your needs, school enrollment counselors will be contacting you in the near future, either by phone or by email, and they can answer any questions you may have regarding financial aid, which assistance, their program requirements and policies. And so with that, I would just like to thank you for your time. Okay. Once again, we thank you for choosing education experts.", "start": 272925, "words": [{"end": 273745, "text": "Okay.", "start": 272925, "speaker": "A", "confidence": 0.62638}, {"end": 274557, "text": "Okay,", "start": 274165, "speaker": "A", "confidence": 0.57247}, {"end": 275225, "text": "<PERSON>,", "start": 274581, "speaker": "A", "confidence": 0.89512}, {"end": 277093, "text": "if", "start": 276805, "speaker": "A", "confidence": 0.99007}, {"end": 277213, "text": "we", "start": 277109, "speaker": "A", "confidence": 0.99876}, {"end": 277309, "text": "can", "start": 277229, "speaker": "A", "confidence": 0.99845}, {"end": 277509, "text": "find", "start": 277317, "speaker": "A", "confidence": 0.69099}, {"end": 277765, "text": "school", "start": 277557, "speaker": "A", "confidence": 0.9978}, {"end": 277933, "text": "for", "start": 277805, "speaker": "A", "confidence": 0.97894}, {"end": 278077, "text": "you", "start": 277949, "speaker": "A", "confidence": 0.99546}, {"end": 278237, "text": "that", "start": 278101, "speaker": "A", "confidence": 0.97141}, {"end": 278469, "text": "meets", "start": 278261, "speaker": "A", "confidence": 0.17403}, {"end": 278597, "text": "your", "start": 278477, "speaker": "A", "confidence": 0.88687}, {"end": 278829, "text": "needs,", "start": 278621, "speaker": "A", "confidence": 0.91558}, {"end": 279085, "text": "school", "start": 278877, "speaker": "A", "confidence": 0.98342}, {"end": 279469, "text": "enrollment", "start": 279125, "speaker": "A", "confidence": 0.89723}, {"end": 280029, "text": "counselors", "start": 279517, "speaker": "A", "confidence": 0.84147}, {"end": 280237, "text": "will", "start": 280077, "speaker": "A", "confidence": 0.87938}, {"end": 280421, "text": "be", "start": 280261, "speaker": "A", "confidence": 0.99921}, {"end": 280925, "text": "contacting", "start": 280453, "speaker": "A", "confidence": 0.98847}, {"end": 281545, "text": "you", "start": 280965, "speaker": "A", "confidence": 0.99964}, {"end": 282133, "text": "in", "start": 281845, "speaker": "A", "confidence": 0.99912}, {"end": 282277, "text": "the", "start": 282149, "speaker": "A", "confidence": 0.99949}, {"end": 282485, "text": "near", "start": 282301, "speaker": "A", "confidence": 0.99946}, {"end": 282989, "text": "future,", "start": 282525, "speaker": "A", "confidence": 0.99994}, {"end": 283381, "text": "either", "start": 283117, "speaker": "A", "confidence": 0.90098}, {"end": 283677, "text": "by", "start": 283413, "speaker": "A", "confidence": 0.98733}, {"end": 284037, "text": "phone", "start": 283741, "speaker": "A", "confidence": 0.8926}, {"end": 284301, "text": "or", "start": 284101, "speaker": "A", "confidence": 0.999}, {"end": 284525, "text": "by", "start": 284333, "speaker": "A", "confidence": 0.99408}, {"end": 284837, "text": "email,", "start": 284565, "speaker": "A", "confidence": 0.99434}, {"end": 285053, "text": "and", "start": 284901, "speaker": "A", "confidence": 0.99324}, {"end": 285197, "text": "they", "start": 285069, "speaker": "A", "confidence": 0.9891}, {"end": 285405, "text": "can", "start": 285221, "speaker": "A", "confidence": 0.99882}, {"end": 285685, "text": "answer", "start": 285445, "speaker": "A", "confidence": 0.97402}, {"end": 285901, "text": "any", "start": 285725, "speaker": "A", "confidence": 0.97228}, {"end": 286197, "text": "questions", "start": 285933, "speaker": "A", "confidence": 0.99398}, {"end": 286357, "text": "you", "start": 286221, "speaker": "A", "confidence": 0.99566}, {"end": 286541, "text": "may", "start": 286381, "speaker": "A", "confidence": 0.9974}, {"end": 286765, "text": "have", "start": 286573, "speaker": "A", "confidence": 0.99941}, {"end": 287213, "text": "regarding", "start": 286805, "speaker": "A", "confidence": 0.53077}, {"end": 287605, "text": "financial", "start": 287269, "speaker": "A", "confidence": 0.99984}, {"end": 288255, "text": "aid,", "start": 287685, "speaker": "A", "confidence": 0.93794}, {"end": 288691, "text": "which", "start": 288405, "speaker": "A", "confidence": 0.81717}, {"end": 289467, "text": "assistance,", "start": 288723, "speaker": "A", "confidence": 0.33569}, {"end": 289859, "text": "their", "start": 289571, "speaker": "A", "confidence": 0.9891}, {"end": 290163, "text": "program", "start": 289907, "speaker": "A", "confidence": 0.99906}, {"end": 290635, "text": "requirements", "start": 290219, "speaker": "A", "confidence": 0.99348}, {"end": 290851, "text": "and", "start": 290675, "speaker": "A", "confidence": 0.99049}, {"end": 291575, "text": "policies.", "start": 290883, "speaker": "A", "confidence": 0.92975}, {"end": 293403, "text": "And", "start": 293115, "speaker": "A", "confidence": 0.70911}, {"end": 293571, "text": "so", "start": 293419, "speaker": "A", "confidence": 0.98589}, {"end": 293747, "text": "with", "start": 293603, "speaker": "A", "confidence": 0.99136}, {"end": 293907, "text": "that,", "start": 293771, "speaker": "A", "confidence": 0.9991}, {"end": 294019, "text": "I", "start": 293931, "speaker": "A", "confidence": 0.98231}, {"end": 294123, "text": "would", "start": 294027, "speaker": "A", "confidence": 0.9812}, {"end": 294267, "text": "just", "start": 294139, "speaker": "A", "confidence": 0.97861}, {"end": 294403, "text": "like", "start": 294291, "speaker": "A", "confidence": 0.63688}, {"end": 294547, "text": "to", "start": 294419, "speaker": "A", "confidence": 0.54609}, {"end": 294683, "text": "thank", "start": 294571, "speaker": "A", "confidence": 0.99765}, {"end": 294803, "text": "you", "start": 294699, "speaker": "A", "confidence": 0.98696}, {"end": 294923, "text": "for", "start": 294819, "speaker": "A", "confidence": 0.995}, {"end": 295091, "text": "your", "start": 294939, "speaker": "A", "confidence": 0.99348}, {"end": 295695, "text": "time.", "start": 295123, "speaker": "A", "confidence": 0.99925}, {"end": 297907, "text": "Okay.", "start": 297315, "speaker": "A", "confidence": 0.72501}, {"end": 298203, "text": "Once", "start": 298011, "speaker": "A", "confidence": 0.93621}, {"end": 298371, "text": "again,", "start": 298219, "speaker": "A", "confidence": 0.99541}, {"end": 298595, "text": "we", "start": 298403, "speaker": "A", "confidence": 0.82832}, {"end": 298787, "text": "thank", "start": 298635, "speaker": "A", "confidence": 0.99407}, {"end": 298947, "text": "you", "start": 298811, "speaker": "A", "confidence": 0.99865}, {"end": 299203, "text": "for", "start": 298971, "speaker": "A", "confidence": 0.99747}, {"end": 299643, "text": "choosing", "start": 299259, "speaker": "A", "confidence": 0.88575}, {"end": 300107, "text": "education", "start": 299699, "speaker": "A", "confidence": 0.99881}, {"end": 300971, "text": "experts.", "start": 300211, "speaker": "A", "confidence": 0.89988}], "speaker": "A", "confidence": 0.9125292}, {"end": 302135, "text": "And thank you.", "start": 301123, "words": [{"end": 301387, "text": "And", "start": 301123, "speaker": "B", "confidence": 0.94823}, {"end": 301547, "text": "thank", "start": 301411, "speaker": "B", "confidence": 0.95102}, {"end": 302135, "text": "you.", "start": 301571, "speaker": "B", "confidence": 0.99832}], "speaker": "B", "confidence": 0.9658567}, {"end": 303251, "text": "You're welcome.", "start": 302635, "words": [{"end": 302963, "text": "You're", "start": 302635, "speaker": "A", "confidence": 0.624}, {"end": 303251, "text": "welcome.", "start": 302979, "speaker": "A", "confidence": 0.53102}], "speaker": "A", "confidence": 0.57751}, {"end": 305123, "text": "All right, you too.", "start": 303323, "words": [{"end": 303435, "text": "All", "start": 303323, "speaker": "B", "confidence": 0.59335}, {"end": 304027, "text": "right,", "start": 303435, "speaker": "B", "confidence": 0.95517}, {"end": 304507, "text": "you", "start": 304211, "speaker": "B", "confidence": 0.95607}, {"end": 305123, "text": "too.", "start": 304531, "speaker": "B", "confidence": 0.97318}], "speaker": "B", "confidence": 0.8694425}, {"end": 305563, "text": "Bye.", "start": 305299, "words": [{"end": 305563, "text": "Bye.", "start": 305299, "speaker": "A", "confidence": 0.91029}], "speaker": "A", "confidence": 0.91029}], "word_boost": [], "boost_param": null, "format_text": true, "speed_boost": false, "webhook_url": "https://n8n.lowcoding.dev/webhook/d1e5fdd0-b51d-4447-8af3-6754017d240b", "audio_end_at": null, "disfluencies": false, "dual_channel": false, "multichannel": null, "speech_model": null, "summary_type": null, "webhook_auth": false, "auto_chapters": false, "custom_topics": false, "language_code": "en_us", "summarization": false, "summary_model": null, "acoustic_model": "assemblyai_default", "audio_duration": 321, "content_safety": false, "iab_categories": false, "language_model": "assemblyai_default", "redact_pii_sub": null, "speaker_labels": true, "auto_highlights": false, "custom_spelling": null, "audio_start_from": null, "entity_detection": false, "filter_profanity": false, "redact_pii_audio": false, "speech_threshold": null, "speakers_expected": 2, "language_detection": false, "sentiment_analysis": false, "language_confidence": null, "redact_pii_policies": null, "webhook_status_code": 404, "content_safety_labels": {"status": "unavailable", "results": [], "summary": {}}, "custom_topics_results": null, "iab_categories_result": {"status": "unavailable", "results": [], "summary": {}}, "auto_highlights_result": null, "redact_pii_audio_quality": null, "webhook_auth_header_name": null, "sentiment_analysis_results": null, "language_confidence_threshold": null}]}, "connections": {"If": {"main": [[{"node": "AssemblyAI - Get transcription", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Set vars", "type": "main", "index": 0}]]}, "Set vars": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Set link to audio": {"main": [[{"node": "AssemblyAI - Transcribe", "type": "main", "index": 0}]]}, "OpenAI - Analyze call": {"main": [[{"node": "Create record", "type": "main", "index": 0}]]}, "AssemblyAI - Get transcription": {"main": [[{"node": "OpenAI - Analyze call", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set link to audio", "type": "main", "index": 0}]]}}}