{"id": "sUGieRWulZJ7scll", "meta": {"instanceId": "e634e668fe1fc93a75c4f2a7fc0dad807ca318b79654157eadb9578496acbc76"}, "name": "Fetch Squarespace Blog & Event Collections to Google Sheets  ", "tags": [{"id": "oIxDbURnjwrJFwau", "name": "Squarespace", "createdAt": "2025-03-06T05:49:51.612Z", "updatedAt": "2025-03-06T05:49:51.612Z"}], "nodes": [{"id": "43bb2c50-a9a9-4647-a470-612ad502283f", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-240, 420], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.2}, {"id": "410fa5ad-a8b8-4cde-b9a9-1d16db0880c9", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-240, 180], "parameters": {}, "typeVersion": 1}, {"id": "fa58fb9d-9292-4f25-8326-fad6e59a5513", "name": "Fetch Squarespace blog", "type": "n8n-nodes-base.httpRequest", "position": [20, 300], "parameters": {"url": "https://beyondspace.studio/blog", "options": {"pagination": {"pagination": {"parameters": {"parameters": [{"name": "offset", "value": "={{ $response.body.pagination.nextPageOffset }}"}, {"name": "format", "value": "json"}]}, "requestInterval": 200, "completeExpression": "={{ $response.body.pagination.nextPage !== true }}", "paginationCompleteWhen": "other"}}}, "sendQuery": true, "queryParameters": {"parameters": [{"name": "format", "value": "json"}, {"name": "offset"}]}}, "typeVersion": 4.2, "alwaysOutputData": false}, {"id": "4b558c29-3bea-4d5e-b88f-0433a81f4698", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-20, 100], "parameters": {"content": "## Change this node\nEdit the HTTP Request URL to your Squarespace blog URL\n\neg: https://beyondspace.studio/blog\n"}, "typeVersion": 1}, {"id": "1886c997-6ba2-42be-8ea0-9047a4cae2e7", "name": "Iterate the collection items", "type": "n8n-nodes-base.splitOut", "position": [260, 300], "parameters": {"options": {}, "fieldToSplitOut": "items"}, "typeVersion": 1}, {"id": "9382f7b6-e113-4e4c-ba04-e7cbd119b164", "name": "Squarespace collection Spreadsheet", "type": "n8n-nodes-base.googleSheets", "position": [520, 300], "parameters": {"columns": {"value": {"id": "={{ $json.id }}", "tags": "={{ $json.tags.join(\", \") }}", "title": "={{ $json.title }}", "urlId": "={{ $json. urlId }}", "addedOn": "={{ new Date($json.addedOn).toISOString().split(\"T\")[0] }}", "fullUrl": "={{ $json.fullUrl }}", "assetUrl": "={{ $json.fullUrl }}", "publishOn": "={{ new Date($json.publishOn).toISOString().split(\"T\")[0] }}", "categories": "={{ $json.categories.join(\", \") }}"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "id", "defaultMatch": true, "canBeUsedToMatch": true}, {"id": "addedOn", "type": "string", "display": true, "removed": false, "required": false, "displayName": "addedOn", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "tags", "type": "string", "display": true, "removed": false, "required": false, "displayName": "tags", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "categories", "type": "string", "display": true, "removed": false, "required": false, "displayName": "categories", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "publishOn", "type": "string", "display": true, "removed": false, "required": false, "displayName": "publishOn", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "urlId", "type": "string", "display": true, "removed": false, "required": false, "displayName": "urlId", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "title", "type": "string", "display": true, "removed": false, "required": false, "displayName": "title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "body", "type": "string", "display": true, "removed": false, "required": false, "displayName": "body", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "fullUrl", "type": "string", "display": true, "removed": false, "required": false, "displayName": "fullUrl", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "assetUrl", "type": "string", "display": true, "removed": false, "required": false, "displayName": "assetUrl", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["id"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1yf_RYZGFHpMyOvD3RKGSvIFY2vumvI4474Qm_1t4-jM/edit#gid=0", "cachedResultName": "Beyondspace blog"}, "documentId": {"__rl": true, "mode": "list", "value": "1yf_RYZGFHpMyOvD3RKGSvIFY2vumvI4474Qm_1t4-jM", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1yf_RYZGFHpMyOvD3RKGSvIFY2vumvI4474Qm_1t4-jM/edit?usp=drivesdk", "cachedResultName": "Make.com template"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JgI9maibw5DnBXRP", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "********-9daa-4985-afb8-eb1f259b23a0", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [480, 100], "parameters": {"content": "## Spreadsheet template\nClone this spreadsheet as reference\nhttps://docs.google.com/spreadsheets/d/1HGc7o4mqMY1t9fXT6LBhmZixjJYr0eapSUosXMA9v8E/edit?gid=0#gid=0"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "1a05b7b8-22fb-4f49-a079-583a26401a61", "connections": {"Schedule Trigger": {"main": [[{"node": "Fetch Squarespace blog", "type": "main", "index": 0}]]}, "Fetch Squarespace blog": {"main": [[{"node": "Iterate the collection items", "type": "main", "index": 0}]]}, "Iterate the collection items": {"main": [[{"node": "Squarespace collection Spreadsheet", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Fetch Squarespace blog", "type": "main", "index": 0}]]}}}