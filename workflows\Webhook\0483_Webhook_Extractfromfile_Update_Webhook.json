{"meta": {"instanceId": "257476b1ef58bf3cb6a46e65fac7ee34a53a5e1a8492d5c6e4da5f87c9b82833", "templateId": "2222"}, "nodes": [{"id": "a131803a-ab1d-4a89-b51d-8a875fa2caaf", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [440, 267.87369152409246], "parameters": {"width": 344, "height": 303, "content": "## Testing \n\nTesting can be done with CURL or similar.\n\nFor File posting using Form Data\ncurl -X POST -F file=@filepath.xml <WEBHOOK_URL>\n\nThis can also be tested using the Test workflow"}, "typeVersion": 1}, {"id": "f9ae7afb-48a6-45bf-9c55-0e5fd63afede", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1720, 747.8736915240925], "parameters": {"color": 4, "width": 496, "height": 256, "content": "## Response\nWhere possible we will be returning a JSON object.\n```\n{\n  \"status\": \"ok\",\n  \"data\": { // JSON DATA }\n}\n```\nIf there is an error\n```\n{\n  \"status\": \"error\",\n  \"data\": \"error message to display\"\n}\n```"}, "typeVersion": 1}, {"id": "f37712fb-88cc-4d5a-9c37-6b9d962052e2", "name": "Extract From File", "type": "n8n-nodes-base.extractFromFile", "onError": "continueErrorOutput", "position": [1080, 307.87369152409246], "parameters": {"options": {}, "operation": "xml", "destinationKey": "xml", "binaryPropertyName": "data0"}, "typeVersion": 1}, {"id": "e70c134d-a546-447d-a0cb-96c5573232e1", "name": "Error Response", "type": "n8n-nodes-base.respondToWebhook", "onError": "continueErrorOutput", "position": [1480, 1067.8736915240925], "parameters": {"options": {"responseCode": 500}, "respondWith": "json", "responseBody": "{\n  \"status\": \"error\",\n  \"data\": \"There was a problem converting your XML. Please refresh the page and try again.\"\n}"}, "typeVersion": 1}, {"id": "eacf0315-75fb-4461-b5d3-d8e7c5572492", "name": "POST", "type": "n8n-nodes-base.webhook", "position": [460, 587.8736915240925], "webhookId": "add125c9-1591-4e1c-b68c-8032b99b6010", "parameters": {"path": "tool/xml-to-json", "options": {"binaryPropertyName": "data"}, "httpMethod": "POST", "responseMode": "responseNode"}, "typeVersion": 1.1}, {"id": "37cb0178-2010-4cfb-8f12-84e8a45a3553", "name": "XML", "type": "n8n-nodes-base.xml", "onError": "continueErrorOutput", "position": [1380, 407.87369152409246], "parameters": {"options": {}, "dataPropertyName": "xml"}, "typeVersion": 1}, {"id": "4aa36858-f9ee-4653-81d5-7276347abcc2", "name": "Success Response", "type": "n8n-nodes-base.respondToWebhook", "onError": "continueErrorOutput", "position": [1500, 667.8736915240925], "parameters": {"options": {"responseCode": 200}, "respondWith": "json", "responseBody": "={\n  \"status\": \"OK\",\n  \"data\": {{ JSON.stringify($json) }}\n}"}, "typeVersion": 1}, {"id": "0425203d-8185-4b27-b7b5-3b4f0e775981", "name": "Already JSON", "type": "n8n-nodes-base.set", "position": [1080, 667.8736915240925], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "={{ $json.body }}\n"}, "typeVersion": 3.3}, {"id": "9ac12f08-a09b-45e9-8ebd-55ff6d8a63bd", "name": "Change Field", "type": "n8n-nodes-base.set", "onError": "continueErrorOutput", "position": [1080, 487.87369152409246], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "b2e3bec3-221e-4f1d-b439-f75174f68ed1", "name": "xml", "type": "string", "value": "={{ $json.body }}"}]}}, "typeVersion": 3.3}, {"id": "d722f969-f3d3-4f4a-9fbd-4e2d30556408", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [380, 240], "parameters": {"color": 7, "width": 1917.663445686706, "height": 1027.3921976438187, "content": ""}, "typeVersion": 1}, {"id": "7618bd02-6d56-44a1-aaa3-de805e1ef18d", "name": "Switch", "type": "n8n-nodes-base.switch", "position": [660, 587.8736915240925], "parameters": {"rules": {"values": [{"outputKey": "File", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "object", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $binary }}", "rightValue": ""}]}, "renameOutput": true}, {"outputKey": "Data", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "8930ce1a-a4cc-4094-b08f-a23a13dec40c", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.headers['content-type'] }}", "rightValue": "text/plain"}]}, "renameOutput": true}, {"outputKey": "appXML", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "e3108952-daa2-425c-8c70-7d2ce0949e0c", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.headers['content-type'] }}", "rightValue": "=application/xml"}]}, "renameOutput": true}]}, "options": {"fallbackOutput": "extra"}}, "typeVersion": 3}, {"id": "b8bde0ed-7d85-4582-89c4-08a0829c4df8", "name": "Send to Error Channel", "type": "n8n-nodes-base.slack", "position": [1760, 1067.8736915240925], "parameters": {"text": ":interrobang: Error in XML to JSON tool", "select": "channel", "blocksUi": "={\n\t\"blocks\": [\n{\n\t\t\t\"type\": \"section\",\n\t\t\t\"text\": {\n\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\"text\": \":interrobang: Error in XML to JSON tool\"\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\t\"type\": \"section\",\n\t\t\t\"text\": {\n\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\"text\": \"*Time:*\\n{{ $now.format('dd/MM/yyyy HH:mm:ss') }}\\n*Execution ID:*\\n{{ $execution.id }}\\n\"\n\t\t\t},\n\t\t\t\"accessory\": {\n\t\t\t\t\"type\": \"button\",\n\t\t\t\t\"text\": {\n\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\"text\": \"Go to Error\",\n\t\t\t\t\t\"emoji\": true\n\t\t\t\t},\n\t\t\t\t\"value\": \"error\",\n\t\t\t\t\"url\": \"https://internal.users.n8n.cloud/workflow/{{ $workflow.id }}/executions/{{ $execution.id }}\",\n\t\t\t\t\"action_id\": \"button-action\",\n\t\t\t\t\"style\": \"primary\"\n\t\t\t}\n\t\t}\n\t]\n}", "channelId": {"__rl": true, "mode": "name", "value": "#alerts-xml-to-json"}, "messageType": "block", "otherOptions": {}}, "credentials": {"slackApi": {"id": "6", "name": "<PERSON><PERSON>"}}, "typeVersion": 2.1}], "pinData": {}, "connections": {"XML": {"main": [[{"node": "Success Response", "type": "main", "index": 0}], [{"node": "Error Response", "type": "main", "index": 0}]]}, "POST": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Extract From File", "type": "main", "index": 0}], [{"node": "Change Field", "type": "main", "index": 0}], [{"node": "Already JSON", "type": "main", "index": 0}], [{"node": "Error Response", "type": "main", "index": 0}]]}, "Already JSON": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}, "Change Field": {"main": [[{"node": "XML", "type": "main", "index": 0}], [{"node": "Error Response", "type": "main", "index": 0}]]}, "Error Response": {"main": [[{"node": "Send to Error Channel", "type": "main", "index": 0}], [{"node": "Send to Error Channel", "type": "main", "index": 0}]]}, "Success Response": {"main": [null, [{"node": "Send to Error Channel", "type": "main", "index": 0}]]}, "Extract From File": {"main": [[{"node": "XML", "type": "main", "index": 0}], [{"node": "Error Response", "type": "main", "index": 0}]]}}}