{"id": "119", "name": "Create, update, and get an entry in Strapi", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.strapi", "position": [650, 300], "parameters": {"columns": "Title, Content, Description", "operation": "create", "contentType": "posts"}, "credentials": {"strapiApi": "strapi"}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [450, 300], "parameters": {"values": {"string": [{"name": "Title", "value": "Automate Strapi with n8n"}, {"name": "Content", "value": "Strapi is a headless CMS. We will use Strapi and n8n to automate our content creation workflows."}, {"name": "Description", "value": "Learn how to automate <PERSON><PERSON><PERSON> with n8n."}]}, "options": {}}, "typeVersion": 1}, {"name": "Strapi1", "type": "n8n-nodes-base.strapi", "position": [1050, 300], "parameters": {"columns": "slug", "operation": "update", "contentType": "={{$node[\"Strapi\"].parameter[\"contentType\"]}}"}, "credentials": {"strapiApi": "strapi"}, "typeVersion": 1}, {"name": "Set1", "type": "n8n-nodes-base.set", "position": [850, 300], "parameters": {"values": {"string": [{"name": "id", "value": "={{$node[\"Strapi\"].json[\"id\"]}}"}, {"name": "slug", "value": "automate-strapi-with-n8n"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "Strapi2", "type": "n8n-nodes-base.strapi", "position": [1250, 300], "parameters": {"entryId": "={{$node[\"Strapi1\"].json[\"id\"]}}", "contentType": "={{$node[\"Strapi\"].parameter[\"contentType\"]}}"}, "credentials": {"strapiApi": "strapi"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Set": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Set1": {"main": [[{"node": "Strapi1", "type": "main", "index": 0}]]}, "Strapi": {"main": [[{"node": "Set1", "type": "main", "index": 0}]]}, "Strapi1": {"main": [[{"node": "Strapi2", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}}