{"meta": {"instanceId": "257476b1ef58bf3cb6a46e65fac7ee34a53a5e1a8492d5c6e4da5f87c9b82833", "templateId": "2473"}, "nodes": [{"id": "1205b121-8aaa-4e41-874b-4e81aad6374e", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [120, 600], "parameters": {"color": 4, "width": 462.*************, "height": 315.*************, "content": "## Generate SEO Seed Keywords Using AI\n\nThis flow uses an AI node to generate Seed Keywords to focus SEO efforts on based on your ideal customer profile\n\n**Outputs:** \n- List of 20 Seed Keywords\n\n\n**Pre-requisites / Dependencies:**\n- You know your ideal customer profile (ICP)\n- An AI API account (either OpenAI or Anthropic recommended)"}, "typeVersion": 1}, {"id": "d2654d75-2b64-4ec3-b583-57d2b6b7b195", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "disabled": true, "position": [640, 920], "parameters": {"color": 7, "width": 287.*************, "height": 330.**************, "content": "**Generate draft seed <PERSON><PERSON> based on ICP**\n\n"}, "typeVersion": 1}, {"id": "d248a58e-3705-4b6f-99cb-e9187e56781c", "name": "Anthropic <PERSON>", "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "position": [680, 1120], "parameters": {"options": {}}, "typeVersion": 1.2}, {"id": "71517d83-59f5-441a-8a75-c35f4e06a8a2", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [980, 980], "parameters": {"options": {}, "fieldToSplitOut": "output.answer"}, "typeVersion": 1}, {"id": "1c68eff5-6478-4eba-9abe-3ccea2a17a5c", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "disabled": true, "position": [120, 920], "parameters": {"color": 7, "width": 492.16246201447336, "height": 213.62075341687063, "content": "**Get data from airtable and format** "}, "typeVersion": 1}, {"id": "53dcc524-ef3d-40b8-b79d-976517dce4e7", "name": "Sticky Note17", "type": "n8n-nodes-base.stickyNote", "disabled": true, "position": [960, 920], "parameters": {"color": 7, "width": 348.42891651921957, "height": 213.62075341687063, "content": "**Add data to database**"}, "typeVersion": 1}, {"id": "570495fe-3f1d-44ae-bea0-9fa4b2ce15ef", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [640, 820], "parameters": {"color": 6, "width": 393.46745700785266, "height": 80, "content": "**Costs to run**\nApprox. $0.02-0.05 for a run using Claude Sonnet 3.5"}, "typeVersion": 1}, {"id": "6e5e84c5-409f-4f37-931a-21a44aff7c5e", "name": "Set Ideal Customer Profile (ICP)", "type": "n8n-nodes-base.set", "position": [160, 980], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "973e949e-1afd-4378-8482-d2168532eff6", "name": "product", "type": "string", "value": "=**Replace this with a string detailing your intended product (if you have one)**"}, {"id": "ce9c0a8f-6157-4b46-8b77-133545dc71bd", "name": "pain points", "type": "string", "value": "=**Replace this with a string list of customer pain points**"}, {"id": "5abc858a-c412-4acf-acb9-488e4d992d2f", "name": "goals", "type": "string", "value": "=**Replace this with a string list of your customers key goals/objectives**"}, {"id": "fbdd1ef7-c1b9-48eb-b73e-a383f12b5ba1", "name": "current solutions", "type": "string", "value": "=**Replace this with a string detailing how your ideal customer currently solves their pain ppoints**"}, {"id": "2e5c8f48-266e-486c-956f-51f1449f6288", "name": "expertise level", "type": "string", "value": "=**Replace this with a string detailing customer level of expertise**"}]}}, "notesInFlow": true, "typeVersion": 3.4}, {"id": "bd5781f4-6f35-45d3-8182-12ea6712eddf", "name": "Aggregate for AI node", "type": "n8n-nodes-base.aggregate", "position": [380, 980], "parameters": {"options": {}, "aggregate": "aggregateAllItemData"}, "notesInFlow": true, "typeVersion": 1}, {"id": "244943bf-e4dd-40fc-9a43-7a5cd0da1c5b", "name": "Sticky Note14", "type": "n8n-nodes-base.stickyNote", "position": [640, 1260], "parameters": {"color": 3, "width": 284.87764467541297, "height": 80, "content": "**REQUIRED**\nConnect to your own AI API above"}, "typeVersion": 1}, {"id": "73c8f47a-4fdb-40c8-9062-890ef1265ab0", "name": "Sticky Note16", "type": "n8n-nodes-base.stickyNote", "position": [120, 1140], "parameters": {"color": 3, "width": 284.87764467541297, "height": 80, "content": "**REQUIRED**\nSet your Ideal Customer Profile before proceeding"}, "typeVersion": 1}, {"id": "a5b93e6d-44ab-4b6f-b86a-25dc621b52b0", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [660, 980], "parameters": {"text": "=User:\nHere are some important rules for you to follow:\n<rules>\n1. Analyze the ICP information carefully.\n2. Generate 15-20 seed keywords that are relevant to the ICP's needs, challenges, goals, and search behavior.\n3. Ensure the keywords are broad enough to be considered \"\"head\"\" terms, but specific enough to target the ICP effectively.\n4. Consider various aspects of the ICP's journey, including awareness, consideration, and decision stages.\n5. Include a mix of product-related, problem-related, and solution-related terms.\n6. Think beyond just the product itself - consider industry trends, related technologies, and broader business concepts that would interest the ICP.\n7. Avoid overly generic terms that might attract irrelevant traffic.\n8. Aim for a mix of keyword difficulties, including both competitive and less competitive terms.\n9. Include keywords that cover different search intents: informational, navigational, commercial, and transactional.\n10. Consider related tools or platforms that the ICP might use, and include relevant integration-related keywords.\n11. If applicable, include some location-specific keywords based on the ICP's geographic information.\n12. Incorporate industry-specific terminology or jargon that the ICP would likely use in their searches.\n13. Consider emerging trends or pain points in the ICP's industry that they might be searching for solutions to.\n13. Format the keywords in lowercase, without punctuation. Trim any leading or trailing white space.\n</rules>\n\nYour output should be an array of strings, each representing a seed keyword:\n<example>\n['b2b lead generation', 'startup marketing strategies', 'saas sales funnel', ...]\n</example>\n\nHere is the Ideal Customer Profile (ICP) information:\n<input>\n{{ $json.data[0].product }}\n</input>\n\nNow:\nBased on the provided ICP, generate an array of 15-20 seed keywords that will form the foundation of a comprehensive SEO strategy for this B2B SaaS company. These keywords should reflect a deep understanding of the ICP's needs, challenges, and search behavior, while also considering broader industry trends and related concepts.\n\nFirst, write out your ideas in {thoughts: } JSON as part of your analysis, then answer inside the {answer: } key in the JSON. ", "agent": "conversationalAgent", "options": {"systemMessage": "=System: You are an expert SEO strategist tasked with generating 15-20 key head search terms (seed keywords) for a B2B SaaS company. Your goal is to create a comprehensive list of keywords that will attract and engage the ideal customer profile (ICP) described."}, "promptType": "define"}, "typeVersion": 1.6}, {"id": "ca3c0bd5-7ef0-4e2b-9b5e-071773c32c85", "name": "Connect to your own database", "type": "n8n-nodes-base.noOp", "position": [1140, 980], "parameters": {}, "typeVersion": 1}, {"id": "94639a81-5e46-482a-851a-5443bfe9863c", "name": "Sticky Note15", "type": "n8n-nodes-base.stickyNote", "position": [1120, 1140], "parameters": {"color": 3, "width": 284.87764467541297, "height": 80, "content": "**REQUIRED**\nConnect to your own database / GSheet / Airtable base to output these"}, "typeVersion": 1}, {"id": "16498e92-c0d5-44f4-b993-c9c8930955bc", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-60, 980], "parameters": {}, "typeVersion": 1}], "pinData": {}, "connections": {"AI Agent": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Connect to your own database", "type": "main", "index": 0}]]}, "Anthropic Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Aggregate for AI node": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Set Ideal Customer Profile (ICP)": {"main": [[{"node": "Aggregate for AI node", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set Ideal Customer Profile (ICP)", "type": "main", "index": 0}]]}}}