{"meta": {"instanceId": "cb484ba7b742928a2048bf8829668bed5b5ad9787579adea888f05980292a4a7", "templateCredsSetupCompleted": true}, "nodes": [{"id": "15f8f140-66b6-4c83-8a33-cf8cafdc32d4", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-420, -740], "parameters": {"color": 7, "width": 1440, "height": 620, "content": "![voiceflow](https://uploads.n8n.io/templates/voiceflow.png)\n## Find Customer\nThis portion of the workflow queries the customer database and returns customer details to the AI agent if found. If not found, it returns NOT_FOUND for the customer details. "}, "typeVersion": 1}, {"id": "00b35d71-a6e6-43e8-a57b-a29a0c476b23", "name": "Check if user found", "type": "n8n-nodes-base.if", "position": [380, -380], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "bddfa253-24c6-430e-9a13-f07daf8c1513", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.Name }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "83d5be62-92f2-4018-bb5e-e4c068223370", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-420, -80], "parameters": {"color": 7, "width": 1460, "height": 540, "content": "![zendesk](https://uploads.n8n.io/templates/zendesk.png)\n## Create Zendesk Ticket \nCreates a Zendesk ticket on behalf of the customer when needed."}, "typeVersion": 1}, {"id": "19f99374-82a6-4978-a159-9b68accda619", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-420, 480], "parameters": {"color": 7, "width": 1920, "height": 700, "content": "![Gcal](https://uploads.n8n.io/templates/calendar.png)\n## Schedule a meeting\nThis gives availability for google calendar and schedules a meeting as well "}, "typeVersion": 1}, {"id": "ad83adab-eecb-49f7-ba95-b53889f8dded", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-420, 1220], "parameters": {"color": 7, "width": 900, "height": 540, "content": "![voiceflow](https://uploads.n8n.io/templates/airtable.png)\n## Give Product team transcripts for analysis\nNeed to help your product team out"}, "typeVersion": 1}, {"id": "4b224fd9-d056-4f2a-aa91-aa4cbee4c44a", "name": "Check if available", "type": "n8n-nodes-base.if", "position": [520, 800], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "d1c61a1f-0265-4eb5-918f-3111e916387f", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.available }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "60067000-0c90-4a9d-bf11-e22a864b0ad8", "name": "Check for malformed date", "type": "n8n-nodes-base.if", "position": [100, 880], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "f055e64e-a1d2-4213-9d5c-1d1069cb582c", "operator": {"type": "dateTime", "operation": "after"}, "leftValue": "={{ $json.availability }}", "rightValue": "={{ $now }}"}]}}, "typeVersion": 2.2}, {"id": "ad9f1857-2179-4d1d-ac25-04975b23390f", "name": "Create Ticket", "type": "n8n-nodes-base.httpRequest", "onError": "continueRegularOutput", "position": [340, 180], "parameters": {"url": "https://n8n8688.zendesk.com/api/v2/tickets", "method": "POST", "options": {"redirect": {"redirect": {}}}, "jsonBody": "={\n  \"ticket\": {\n    \"requester_id\": \"{{ $json.user.id }}\",\n    \"subject\": \"New TechFin Customer Ticket for {{ $json.user.name }}\",\n    \"comment\": {\n      \"body\": \"{{ $('Extract Zendesk Fields').item.json.body.summary }}\\n\\n{{ $('Extract Zendesk Fields').item.json.body.transcript.replace(/\\n/g, \"\\\\n\").replace(/\\\\'/g, \"\\\\'\").replace(/\\\\\"/g, '\\\\\"').replace(/\\\\&/g, \"\\\\&\").replace(/\\\\r/g, \"\\\\r\").replace(/\\\\t/g, \"\\\\t\").replace(/\\\\b/g, \"\\\\b\").replace(/\\\\f/g, \"\\\\f\") }}\"\n    }\n  }\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}]}, "nodeCredentialType": "zendeskApi"}, "credentials": {"zendeskApi": {"id": "ROx0ipJapRomRxEX", "name": "Zendesk Demo Access"}}, "typeVersion": 4.2}, {"id": "5b5c0f60-4495-4040-9926-194b672e6850", "name": "Create Customer in DB", "type": "n8n-nodes-base.httpRequest", "onError": "continueRegularOutput", "position": [120, 180], "parameters": {"url": "https://n8n8688.zendesk.com/api/v2/users/create_or_update", "method": "POST", "options": {}, "jsonBody": "={\n  \"user\": {\n    \"email\": \"{{ $json.body.email }}\",\n    \"identities\": [\n      {\n        \"type\": \"email\",\n        \"value\": \"{{ $json.body.email }}\"\n      }\n    ],\n    \"name\": \"{{ $json.body.name }}\"\n  }\n}", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "zendeskApi"}, "credentials": {"zendeskApi": {"id": "ROx0ipJapRomRxEX", "name": "Zendesk Demo Access"}}, "typeVersion": 4.2}, {"id": "3e5dd684-ee28-4431-849b-c8ab42bd93a0", "name": "Check if submitted succesfully", "type": "n8n-nodes-base.if", "position": [540, 180], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "1ff20679-9964-4ac9-933e-c370c2ef72c1", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.ticket.url }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "c2fac290-0e6e-4b36-864e-f6bb2d25753c", "name": "Ticket Created Successfully", "type": "n8n-nodes-base.respondToWebhook", "position": [800, 80], "parameters": {"options": {}, "respondWith": "json", "responseBody": "{\n  \"status\": \"SUCCESS_TICKET_SUBMITTED\"\n}"}, "typeVersion": 1.1}, {"id": "71a2c455-5de7-455c-9bed-b710a09c3d07", "name": "Error <PERSON>ting Ticket", "type": "n8n-nodes-base.respondToWebhook", "position": [800, 260], "parameters": {"options": {"responseCode": 400}, "respondWith": "json", "responseBody": "{\n  \"status\": \"TICKET_ERROR\"\n}"}, "typeVersion": 1.1}, {"id": "fcd6ceca-d231-4c49-bb1c-716d239dc0c4", "name": "Airtable Endpoint", "type": "n8n-nodes-base.webhook", "position": [-300, 1560], "webhookId": "9a52822c-0304-4dad-a86a-ae662161243c", "parameters": {"path": "9a52822c-0304-4dad-a86a-ae662161243c", "options": {}, "httpMethod": "POST"}, "typeVersion": 2}, {"id": "53783abf-8254-46e2-8b1f-c797f880fa1b", "name": "Gcal Endpoint", "type": "n8n-nodes-base.webhook", "position": [-340, 800], "webhookId": "c1020b94-603c-4981-ab48-51e208d17223", "parameters": {"path": "c1020b94-603c-4981-ab48-51e208d17223", "options": {}, "httpMethod": "POST", "responseMode": "responseNode"}, "typeVersion": 2}, {"id": "df6a885b-d9ea-4033-bb2d-6a222ca9153f", "name": "Zendesk Endpoint", "type": "n8n-nodes-base.webhook", "position": [-320, 180], "webhookId": "9c15c8ac-8f3a-40d3-8ad5-e40468388968", "parameters": {"path": "9c15c8ac-8f3a-40d3-8ad5-e40468388968", "options": {}, "httpMethod": "POST", "responseMode": "responseNode"}, "typeVersion": 2}, {"id": "993c83c7-c376-4bc8-8cb9-995de92d6c4e", "name": "Voiceflow Endpoint", "type": "n8n-nodes-base.webhook", "position": [-320, -380], "webhookId": "d9b20efe-9bb4-4d8b-b9aa-d568f43f78ea", "parameters": {"path": "d9b20efe-9bb4-4d8b-b9aa-d568f43f78ea", "options": {}, "httpMethod": "POST", "responseMode": "responseNode"}, "typeVersion": 2}, {"id": "399c2471-3a3d-458a-bb5d-a0a5e42f3121", "name": "Extract Phone Number", "type": "n8n-nodes-base.set", "position": [-100, -380], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "90de9dcb-1ab6-43d6-999e-6672fbd81f2a", "name": "query.phone_number", "type": "string", "value": "={{ $json.query.phone_number.replace(/^\\+/, '') }}"}]}}, "typeVersion": 3.4}, {"id": "3391ccd4-cf64-4f48-9cfc-9a02131c37b6", "name": "Extract Zendesk Fields", "type": "n8n-nodes-base.set", "position": [-100, 180], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "3490074c-304b-4424-b724-74afba26f71e", "name": "body.email", "type": "string", "value": "={{ $json.body.email }}"}, {"id": "0b5d31dc-029b-4055-91f3-55d820b43cdb", "name": "body.name", "type": "string", "value": "={{ $json.body.name }}"}, {"id": "79ba72a8-42ff-481e-9ca8-6d9237ba84d8", "name": "body.transcript", "type": "string", "value": "={{ $json.body.transcript }}"}, {"id": "77aeef5e-fb51-46d9-a66f-03bc7fe29ac7", "name": "body.summary", "type": "string", "value": "={{ $json.body.summary }}"}]}}, "typeVersion": 3.4}, {"id": "63789194-f11c-42f1-8075-849ba1fedfd7", "name": "Extract Gcal Data", "type": "n8n-nodes-base.set", "onError": "continueRegularOutput", "position": [-120, 800], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "b0e95ac3-08a7-48ad-917a-9736f6e07cd2", "name": "availability", "type": "string", "value": "={{ $json.query.datetime.toDateTime() }}"}, {"id": "ddc4b463-2acc-45bd-9d09-fee782ab1879", "name": "query.name", "type": "string", "value": "={{ $json.query.name }}"}, {"id": "3f4cbd33-fa49-4a0e-bd48-919ab7224684", "name": "query.email", "type": "string", "value": "={{ $json.query.email }}"}, {"id": "1027589d-b475-4330-9686-9b7a9e7ece8f", "name": "query.summary", "type": "string", "value": "={{ $json.query.summary }}"}]}}, "typeVersion": 3.4, "alwaysOutputData": true}, {"id": "813b50b7-2255-441a-8ad6-15cde81ec14d", "name": "Extract Airtable Data", "type": "n8n-nodes-base.set", "position": [-80, 1560], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "6f121f9b-6692-41fa-849b-f613d9fe9009", "name": "phone", "type": "string", "value": "={{ $json.query.phone }}"}, {"id": "acec6f45-271d-4f6a-bf29-e8ea1f5d9fa7", "name": "summary", "type": "string", "value": "={{ $json.query.summary }}"}, {"id": "e76573ec-3884-4fe8-b34f-19b77022ba31", "name": "transcript", "type": "string", "value": "={{ $json.query.transcript }}"}, {"id": "a3951676-739a-4641-99b1-ed7f5fcbf08d", "name": "type", "type": "string", "value": "={{ $json.query.type }}"}]}}, "typeVersion": 3.4}, {"id": "830ee1b3-2bc3-4809-a0e7-c9c966053fbe", "name": "Create Airtable Data", "type": "n8n-nodes-base.airtable", "position": [180, 1560], "parameters": {"base": {"__rl": true, "mode": "list", "value": "app9dFu44S5VTIFRV", "cachedResultUrl": "https://airtable.com/app9dFu44S5VTIFRV", "cachedResultName": "Product Customer Analysis"}, "table": {"__rl": true, "mode": "list", "value": "tblXGczD6wjZ6ZNko", "cachedResultUrl": "https://airtable.com/app9dFu44S5VTIFRV/tblXGczD6wjZ6ZNko", "cachedResultName": "Table 1"}, "columns": {"value": {"Phone": "={{ $json.phone }}", "Summary": "={{ $json.summary }}", "Transcript": "={{ $json.transcript }}", "Customer Type": "={{ $json.type }}"}, "schema": [{"id": "Phone", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Phone", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Summary", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Transcript", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Transcript", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Customer Type", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Customer Type", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "create"}, "credentials": {"airtableTokenApi": {"id": "ZwK7ZfD79dhUdsUc", "name": "n8n voiceflow demo"}}, "typeVersion": 2.1}, {"id": "42f007f1-3929-4d65-a3b5-c60693da3ad3", "name": "Query Google Sheets for Phone", "type": "n8n-nodes-base.googleSheets", "position": [160, -380], "parameters": {"options": {}, "filtersUI": {"values": [{"lookupValue": "={{ $json.query.phone_number }}", "lookupColumn": "Phone Number"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/17sPEb3d55tiqJwt1Dms6MG1-qAAF0k9fQzbTs1lI1Yw/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "17sPEb3d55tiqJwt1Dms6MG1-qAAF0k9fQzbTs1lI1Yw", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/17sPEb3d55tiqJwt1Dms6MG1-qAAF0k9fQzbTs1lI1Yw/edit?usp=drivesdk", "cachedResultName": "Customer Database"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "4ZBfVX71VUd6pRy3", "name": "Google Sheets Angel Access"}}, "typeVersion": 4.5, "alwaysOutputData": true}, {"id": "d0948a72-c8a8-48f6-b5d8-adb52a44a169", "name": "Respond to Webhook with Customer Data", "type": "n8n-nodes-base.respondToWebhook", "position": [640, -520], "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "12e0e2b3-90e1-4b77-a6bb-12dc4603d4f7", "name": "Respond to Webhook with <PERSON><PERSON>r", "type": "n8n-nodes-base.respondToWebhook", "position": [780, -280], "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "9de11b89-bbe8-4ce4-9838-27e497146966", "name": "Set Error Data", "type": "n8n-nodes-base.set", "position": [620, -280], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "1ec3d6bd-41d8-489b-8c52-73c88267e0bd", "name": "row_number", "type": "string", "value": "NOT_FOUND"}, {"id": "bfd6215e-f4ed-4d75-b9ae-d5925f87eb98", "name": "Name", "type": "string", "value": "NOT_FOUND"}, {"id": "e06fc869-57e7-4dc5-b563-4d093be654ab", "name": "Email Address", "type": "string", "value": "NOT_FOUND"}, {"id": "ae1a429b-1c21-4dcb-bf68-e0c01732ec5e", "name": "Tier", "type": "string", "value": "NOT_FOUND"}, {"id": "879369f3-79f7-4420-bcec-b72f67212297", "name": "Phone Number", "type": "string", "value": "NOT_FOUND"}]}}, "typeVersion": 3.4}, {"id": "37b2289b-bc9a-440f-9974-fffe433c2069", "name": "Check Calendar Availability", "type": "n8n-nodes-base.googleCalendar", "position": [300, 800], "parameters": {"options": {}, "timeMax": "={{ $json.availability.toDateTime().plus(30, 'minutes') }}", "timeMin": "={{ $json.availability }}", "calendar": {"__rl": true, "mode": "list", "value": "<EMAIL>", "cachedResultName": "<EMAIL>"}, "resource": "calendar"}, "credentials": {"googleCalendarOAuth2Api": {"id": "zjwDrLg8gSlOpOQI", "name": "Google Calendar account 9"}}, "typeVersion": 1.3}, {"id": "********-e120-4403-b623-e087a0005383", "name": "Create Calendar Event", "type": "n8n-nodes-base.googleCalendar", "position": [800, 680], "parameters": {"end": "={{ $('Extract Gcal Data').item.json.availability.toDateTime().plus(30, 'minutes') }}", "start": "={{ $('Extract Gcal Data').item.json.availability }}", "calendar": {"__rl": true, "mode": "list", "value": "<EMAIL>", "cachedResultName": "<EMAIL>"}, "additionalFields": {"summary": "=TechFin Enterprise Customer Success Meeting with {{ $('Extract Gcal Data').item.json.query.name }}", "attendees": ["={{ $('Extract Gcal Data').item.json.query.email }}"], "description": "={{ $('Extract Gcal Data').item.json.query.summary }}"}}, "credentials": {"googleCalendarOAuth2Api": {"id": "zjwDrLg8gSlOpOQI", "name": "Google Calendar account 9"}}, "typeVersion": 1.3}, {"id": "643a6c39-f70d-4453-be96-54eadb0b9667", "name": "Set Calendar Success Message", "type": "n8n-nodes-base.set", "position": [1020, 680], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "759c2ab4-bc39-4f7c-b585-212d6d6e9ab5", "name": "status", "type": "string", "value": "MEETING_BOOKED_SUCCESSFULLY"}]}}, "typeVersion": 3.4}, {"id": "10b7bdfc-15a9-4d3c-9c58-e7f0d7356d98", "name": "Respond with Success", "type": "n8n-nodes-base.respondToWebhook", "position": [1240, 680], "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "4c8de5b6-d11f-454e-b198-eb7ea4bf8151", "name": "Set Calendar Error Data", "type": "n8n-nodes-base.set", "position": [800, 920], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "759c2ab4-bc39-4f7c-b585-212d6d6e9ab5", "name": "status", "type": "string", "value": "CSM_UNAVAILABLE"}]}}, "typeVersion": 3.4}, {"id": "b8d54018-4f15-41d7-bc74-e5fa42c2eae8", "name": "Respond With Calendar Error data", "type": "n8n-nodes-base.respondToWebhook", "position": [1040, 920], "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "1be55353-b20d-41aa-bd82-c5a21ec1ca56", "name": "Set Invalid Data Error", "type": "n8n-nodes-base.set", "position": [320, 1020], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "759c2ab4-bc39-4f7c-b585-212d6d6e9ab5", "name": "status", "type": "string", "value": "INVALID_DATA_ERROR"}]}}, "typeVersion": 3.4}, {"id": "a4e01bfd-4f65-457e-bf84-8140f2d58168", "name": "Respond with <PERSON><PERSON>", "type": "n8n-nodes-base.respondToWebhook", "position": [500, 1020], "parameters": {"options": {}}, "typeVersion": 1.1}], "pinData": {}, "connections": {"Create Ticket": {"main": [[{"node": "Check if submitted succesfully", "type": "main", "index": 0}]]}, "Gcal Endpoint": {"main": [[{"node": "Extract Gcal Data", "type": "main", "index": 0}]]}, "Set Error Data": {"main": [[{"node": "Respond to Webhook with <PERSON><PERSON>r", "type": "main", "index": 0}]]}, "Zendesk Endpoint": {"main": [[{"node": "Extract Zendesk Fields", "type": "main", "index": 0}]]}, "Airtable Endpoint": {"main": [[{"node": "Extract Airtable Data", "type": "main", "index": 0}]]}, "Extract Gcal Data": {"main": [[{"node": "Check for malformed date", "type": "main", "index": 0}]]}, "Check if available": {"main": [[{"node": "Create Calendar Event", "type": "main", "index": 0}], [{"node": "Set Calendar Error Data", "type": "main", "index": 0}]]}, "Voiceflow Endpoint": {"main": [[{"node": "Extract Phone Number", "type": "main", "index": 0}]]}, "Check if user found": {"main": [[{"node": "Respond to Webhook with Customer Data", "type": "main", "index": 0}], [{"node": "Set Error Data", "type": "main", "index": 0}]]}, "Extract Phone Number": {"main": [[{"node": "Query Google Sheets for Phone", "type": "main", "index": 0}]]}, "Create Calendar Event": {"main": [[{"node": "Set Calendar Success Message", "type": "main", "index": 0}]]}, "Create Customer in DB": {"main": [[{"node": "Create Ticket", "type": "main", "index": 0}]]}, "Extract Airtable Data": {"main": [[{"node": "Create Airtable Data", "type": "main", "index": 0}]]}, "Extract Zendesk Fields": {"main": [[{"node": "Create Customer in DB", "type": "main", "index": 0}]]}, "Set Invalid Data Error": {"main": [[{"node": "Respond with <PERSON><PERSON>", "type": "main", "index": 0}]]}, "Set Calendar Error Data": {"main": [[{"node": "Respond With Calendar Error data", "type": "main", "index": 0}]]}, "Check for malformed date": {"main": [[{"node": "Check Calendar Availability", "type": "main", "index": 0}], [{"node": "Set Invalid Data Error", "type": "main", "index": 0}]]}, "Check Calendar Availability": {"main": [[{"node": "Check if available", "type": "main", "index": 0}]]}, "Set Calendar Success Message": {"main": [[{"node": "Respond with Success", "type": "main", "index": 0}]]}, "Query Google Sheets for Phone": {"main": [[{"node": "Check if user found", "type": "main", "index": 0}]]}, "Check if submitted succesfully": {"main": [[{"node": "Ticket Created Successfully", "type": "main", "index": 0}], [{"node": "Error <PERSON>ting Ticket", "type": "main", "index": 0}]]}}}