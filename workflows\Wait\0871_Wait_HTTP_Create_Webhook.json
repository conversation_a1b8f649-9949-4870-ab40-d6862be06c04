{"meta": {"instanceId": "1eadd5bc7c3d70c587c28f782511fd898c6bf6d97963d92e836019d2039d1c79"}, "nodes": [{"id": "58da2884-6dd9-446e-beca-cacae1e8df7c", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [4940, 340], "parameters": {"color": 4, "width": 1280, "height": 320, "content": "=======================================\n            WORKFLOW ASSISTANCE\n=======================================\nFor any questions or support, please contact:\n    <EMAIL>\n\nExplore more tips and tutorials here:\n   - YouTube: https://www.youtube.com/@YaronBeen/videos\n   - LinkedIn: https://www.linkedin.com/in/yaronbeen/\n=======================================\nBright Data Docs: https://docs.brightdata.com/introduction\n"}, "typeVersion": 1}, {"id": "d2aa5abc-6a8b-4ad3-9b87-1349f3dd80b9", "name": "Snapshot Progress", "type": "n8n-nodes-base.httpRequest", "position": [7540, 760], "parameters": {"url": "=https://api.brightdata.com/datasets/v3/progress/{{ $('HTTP Request- Post API call to Bright Data').item.json.snapshot_id }}", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer <YOUR_BRIGHT_DATA_API_KEY>"}]}}, "typeVersion": 4.2}, {"id": "fba84a88-1775-4bc9-85cb-1bda621b4c2c", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [8600, 540], "parameters": {"width": 195, "height": 646, "content": "In this workflow, I use Google Sheets to store the results. \n\nYou can use my template to get started faster:\n\n1. [Click on this link to get the template](https://docs.google.com/spreadsheets/d/1IR-yMQwEFTjbTCSPvVlQ54zQsnG0IRauTjPGoBWmR8U/edit?usp=sharing)\n2. Make a copy of the Sheets\n3. Add the URL to this node \n\n\n"}, "typeVersion": 1}, {"id": "4b235825-1445-42d1-a9fa-d017974819fe", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [6560, 840], "parameters": {"width": 220, "height": 440, "content": "Add your competitors Amazon link here.\n"}, "typeVersion": 1}, {"id": "d6a75b46-e968-4dab-962d-1f81b643b768", "name": "HTTP Request- Post API call to Bright Data", "type": "n8n-nodes-base.httpRequest", "position": [6920, 840], "parameters": {"url": "https://api.brightdata.com/datasets/v3/trigger", "method": "POST", "options": {}, "jsonBody": "=[\n  {\n    \"url\": \"{{ $json['Amazon Product URL'] }}\"\n  }\n]", "sendBody": true, "sendQuery": true, "sendHeaders": true, "specifyBody": "json", "queryParameters": {"parameters": [{"name": "dataset_id", "value": "gd_le8e811kzy4ggddlq"}, {"name": "include_errors", "value": "true"}]}, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer <YOUR_BRIGHT_DATA_API_KEY>"}]}}, "typeVersion": 4.2}, {"id": "50a6c73a-dd82-40af-ad5a-88ef4fd5fc7c", "name": "Wait - Polling Bright Data", "type": "n8n-nodes-base.wait", "position": [7300, 760], "webhookId": "8005a2b3-2195-479e-badb-d90e4240e699", "parameters": {"unit": "minutes", "amount": 1}, "executeOnce": false, "typeVersion": 1.1}, {"id": "8af8f713-6d5d-4113-ad5e-86b29fc8f441", "name": "If - Checking status of Snapshot - if data is ready or not", "type": "n8n-nodes-base.if", "position": [7740, 760], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "7932282b-71bb-4bbb-ab73-4978e554de7e", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "running"}]}}, "typeVersion": 2.2}, {"id": "98166378-3766-4c69-b891-48891a18e175", "name": "HTTP Request - Getting data from Bright Data", "type": "n8n-nodes-base.httpRequest", "position": [8020, 780], "parameters": {"url": "=https://api.brightdata.com/datasets/v3/snapshot/{{ $('HTTP Request- Post API call to Bright Data').item.json.snapshot_id }}", "options": {}, "sendQuery": true, "sendHeaders": true, "queryParameters": {"parameters": [{"name": "format", "value": "json"}]}, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer <YOUR_BRIGHT_DATA_API_KEY>"}]}}, "typeVersion": 4.2}, {"id": "217cc982-0550-4e27-afd5-12b46eafcd04", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [7240, 620], "parameters": {"color": 4, "width": 940, "height": 400, "content": "Bright Data Getting Reviews\n"}, "typeVersion": 1}, {"id": "5fd57531-25f4-4b10-9d95-bbeda1b336cf", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [9620, 1060], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "MX2lQOZcGpmRvdVD", "name": "OpenAi account 2"}}, "typeVersion": 1.2}, {"id": "d79c7504-0ccc-4491-bf7a-3697b31fa71a", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [9480, 600], "parameters": {"width": 360, "height": 820, "content": "Adjust This Prompt with:\n1. Add info about your company and offer.\n\n2. The template requires the LLM to generate a summary of recent reviews but you can adjust it\n\n\n"}, "typeVersion": 1}, {"id": "413669e5-2b75-499d-ba00-766b3cce0d69", "name": "Google Sheets - Adding All Reviews", "type": "n8n-nodes-base.googleSheets", "position": [8640, 840], "parameters": {"columns": {"value": {}, "schema": [{"id": "url", "type": "string", "display": true, "required": false, "displayName": "url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "product_name", "type": "string", "display": true, "required": false, "displayName": "product_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "product_rating", "type": "string", "display": true, "required": false, "displayName": "product_rating", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "product_rating_object", "type": "string", "display": true, "required": false, "displayName": "product_rating_object", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "product_rating_max", "type": "string", "display": true, "required": false, "displayName": "product_rating_max", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "rating", "type": "string", "display": true, "required": false, "displayName": "rating", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "author_name", "type": "string", "display": true, "required": false, "displayName": "author_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "asin", "type": "string", "display": true, "required": false, "displayName": "asin", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "product_rating_count", "type": "string", "display": true, "required": false, "displayName": "product_rating_count", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "review_header", "type": "string", "display": true, "required": false, "displayName": "review_header", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "review_id", "type": "string", "display": true, "required": false, "displayName": "review_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "review_text", "type": "string", "display": true, "required": false, "displayName": "review_text", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "author_id", "type": "string", "display": true, "required": false, "displayName": "author_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "author_link", "type": "string", "display": true, "required": false, "displayName": "author_link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "badge", "type": "string", "display": true, "required": false, "displayName": "badge", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "brand", "type": "string", "display": true, "required": false, "displayName": "brand", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "review_posted_date", "type": "string", "display": true, "required": false, "displayName": "review_posted_date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "review_country", "type": "string", "display": true, "required": false, "displayName": "review_country", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "review_images", "type": "string", "display": true, "required": false, "displayName": "review_images", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "helpful_count", "type": "string", "display": true, "required": false, "displayName": "helpful_count", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "is_amazon_vine", "type": "string", "display": true, "required": false, "displayName": "is_amazon_vine", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "is_verified", "type": "string", "display": true, "required": false, "displayName": "is_verified", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "variant_asin", "type": "string", "display": true, "required": false, "displayName": "variant_asin", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "variant_name", "type": "string", "display": true, "required": false, "displayName": "variant_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "videos", "type": "string", "display": true, "required": false, "displayName": "videos", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "timestamp", "type": "string", "display": true, "removed": false, "required": false, "displayName": "timestamp", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "input", "type": "string", "display": true, "removed": false, "required": false, "displayName": "input", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1IR-yMQwEFTjbTCSPvVlQ54zQsnG0IRauTjPGoBWmR8U/edit#gid=0", "cachedResultName": "input"}, "documentId": {"__rl": true, "mode": "list", "value": "1IR-yMQwEFTjbTCSPvVlQ54zQsnG0IRauTjPGoBWmR8U", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1IR-yMQwEFTjbTCSPvVlQ54zQsnG0IRauTjPGoBWmR8U/edit?usp=drivesdk", "cachedResultName": "NoFluff-N8N-Sheet-Template- AMAZON Reviews Scraping WIth Bright Data"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "4RJOMlGAcB9ZoYfm", "name": "Google Sheets account 2"}}, "typeVersion": 4.3, "alwaysOutputData": true}, {"id": "e1d58479-4008-4801-8523-fa2304ea9ef0", "name": "On form submission - Amazon Reviews", "type": "n8n-nodes-base.formTrigger", "position": [6620, 980], "webhookId": "8d0269c7-d1fc-45a1-a411-19634a1e0b82", "parameters": {"options": {}, "formTitle": "Please Paste The URL of the Amazon Product", "formFields": {"values": [{"fieldLabel": "Amazon Product URL", "placeholder": "https://www.amazon.com/Quencher-Cupholder-Compatible-Insulated-Stainless/dp/B0DCDQ1RFV", "requiredField": true}]}}, "typeVersion": 2.2}, {"id": "2d79e9d2-a867-447e-91f9-b90c2e56427a", "name": "Aggregating all reviews", "type": "n8n-nodes-base.aggregate", "position": [9140, 840], "parameters": {"options": {}, "fieldsToAggregate": {"fieldToAggregate": [{"renameField": true, "outputFieldName": "Aggregated_reviews", "fieldToAggregate": "review_text"}]}}, "typeVersion": 1}, {"id": "937ef1c4-32b3-4966-abb4-f4ae09aa12a7", "name": "Basic LLM Chain - Summary of Recent reviews", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [9520, 840], "parameters": {"text": "=Read the following reviews, these are reviews of our competitors:\n{{ $json.Aggregated_reviews }}\n\n---\nAfter reading them, summarize their weakest points.\nDon't mention the competitor name.\n\n", "promptType": "define"}, "typeVersion": 1.6}, {"id": "2ccf1e0f-a738-44ee-bd8f-65a02a92ca91", "name": "OpenAI- Generating image", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [10160, 840], "parameters": {"prompt": "={\n  \"ad_dimensions\": {\n    \"width\": 1080,\n    \"height\": 1080\n  },\n  \"target_audience\": \"B2C customer\",\n  \"pain_points_source\": \"choose one pain point based on this {{ $json.text }}\",\n  \"ad_requirements\": {\n    \"image_style\": \"weird-and-fun\",\n    \"weird_objects\": [\n      \"Fruit with Faces\",\n      \"Realistic People\"\n    ],\n    \"focus\": \"address the biggest pain point of competitors\",\n    \"avoid_naming_competitors\": true,\n    \"headline\": {\n      \"position\": \"No\",\n      \"text_only\": \"No\",\n      \"other_text\": \"No\"\n    },\n    \"background\": [\n      \"bold red\",\n      \"black\"\n    ]\n  }\n}", "options": {}, "resource": "image"}, "credentials": {"openAiApi": {"id": "MX2lQOZcGpmRvdVD", "name": "OpenAi account 2"}}, "typeVersion": 1.8}, {"id": "ebb11f25-66f5-495e-a7bc-4212c6db10d5", "name": "Gmail - Sending creative to Media Buyers", "type": "n8n-nodes-base.gmail", "position": [10580, 840], "webhookId": "41184a90-65fd-49a8-b0de-d838b94c790c", "parameters": {"sendTo": "<EMAIL>", "message": "=Hey, \n\nWe have analyzed our competitors recent reviews.\nAnalysis data:\n{{ $today }}\n\nHere's a summary:\n{{ $('Basic LLM Chain - Summary of Recent reviews').item.json.text }}\n\nPlease see attached an AI generated 1080x1080 image which you can use in Meta ads.\n\n", "options": {"attachmentsUi": {"attachmentsBinary": [{}]}}, "subject": "=Static Creatives Based on Our competitor {{ $now }}", "emailType": "text"}, "credentials": {"gmailOAuth2": {"id": "TLJ5oxgGtoxdGOTZ", "name": "Gmail account 2"}}, "typeVersion": 2.1}], "pinData": {"Basic LLM Chain - Summary of Recent reviews": [{"text": "The reviews highlight several common weaknesses among the products:\n\n1. **Quality Control Issues**: Some customers reported receiving cups with dents or damages upon arrival, raising concerns about packaging and quality assurance during shipping.\n\n2. **Durability Concerns**: While many praised the durability, a few mentioned that the cups could spill if tipped over, indicating that they might not be fully leak-proof.\n\n3. **Ease of Use**: Several users experienced difficulties with lids getting stuck or indicated that the tumblers are not spill-proof, particularly when used for non-water beverages.\n\n4. **Size and Weight**: A few reviewers commented on the heaviness of larger sizes, suggesting they may not be convenient for frequent carrying, especially for those with smaller bags or during outings.\n\n5. **Cleaning Issues**: Some users noted that certain models could be challenging to clean, particularly if not hand-washed to maintain appearance.\n\n6. **Authenticity Doubts**: There were instances where customers questioned the authenticity of the product based on packaging or markings, which could affect their overall satisfaction.\n\n7. **Price**: A few reviewers mentioned that while the products are of good quality, they are considered pricey, leading to questions about whether the value matches the cost. \n\nOverall, despite many positive comments, issues related to packaging, spillability, and price emerged as notable weaknesses."}]}, "connections": {"OpenAI Chat Model": {"ai_languageModel": [[{"node": "Basic LLM Chain - Summary of Recent reviews", "type": "ai_languageModel", "index": 0}]]}, "Snapshot Progress": {"main": [[{"node": "If - Checking status of Snapshot - if data is ready or not", "type": "main", "index": 0}]]}, "Aggregating all reviews": {"main": [[{"node": "Basic LLM Chain - Summary of Recent reviews", "type": "main", "index": 0}]]}, "OpenAI- Generating image": {"main": [[{"node": "Gmail - Sending creative to Media Buyers", "type": "main", "index": 0}]]}, "Wait - Polling Bright Data": {"main": [[{"node": "Snapshot Progress", "type": "main", "index": 0}]]}, "Google Sheets - Adding All Reviews": {"main": [[{"node": "Aggregating all reviews", "type": "main", "index": 0}]]}, "On form submission - Amazon Reviews": {"main": [[{"node": "HTTP Request- Post API call to Bright Data", "type": "main", "index": 0}]]}, "HTTP Request- Post API call to Bright Data": {"main": [[{"node": "Wait - Polling Bright Data", "type": "main", "index": 0}]]}, "Basic LLM Chain - Summary of Recent reviews": {"main": [[{"node": "OpenAI- Generating image", "type": "main", "index": 0}]]}, "HTTP Request - Getting data from Bright Data": {"main": [[{"node": "Google Sheets - Adding All Reviews", "type": "main", "index": 0}]]}, "If - Checking status of Snapshot - if data is ready or not": {"main": [[{"node": "Wait - Polling Bright Data", "type": "main", "index": 0}], [{"node": "HTTP Request - Getting data from Bright Data", "type": "main", "index": 0}]]}}}