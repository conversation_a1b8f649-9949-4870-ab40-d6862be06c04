{"meta": {"instanceId": "95b3ab5a70ab1c8c1906357a367f1b236ef12a1409406fd992f60255f0f95f85"}, "nodes": [{"id": "819491a0-14f8-4e46-a6a3-0bc84255ab68", "name": "Subscribe invitee booking in KlickTipp", "type": "n8n-nodes-klicktipp.klicktipp", "notes": "Adds the invitee to the KlickTipp subscriber list, associating them with the relevant booking details. In this step an array of the guests email addresses is saved in the record to navigate guest cancellations. In case of cancellations <PERSON><PERSON><PERSON> does not provide an array of guests and therefore this information needs to be read from the invitee record.", "position": [1700, 300], "parameters": {"email": "={{ $('New Calendly event').item.json.payload.email }}", "tagId": "********", "fields": {"dataFields": [{"fieldId": "fieldFirstName", "fieldValue": "={{ $('Convert data for KlickTipp').item.json.invitee_first_name }}"}, {"fieldId": "fieldLastName", "fieldValue": "={{ $('Convert data for KlickTipp').item.json.invitee_last_name }}"}, {"fieldId": "field213329", "fieldValue": "={{ $('New Calendly event').item.json.payload.scheduled_event.name }}"}, {"fieldId": "field213330", "fieldValue": "={{ $('New Calendly event').item.json.payload.scheduled_event.location.join_url }}"}, {"fieldId": "field213331", "fieldValue": "={{ $('New Calendly event').item.json.payload.reschedule_url }}"}, {"fieldId": "field213332", "fieldValue": "={{ $('New Calendly event').item.json.payload.cancel_url }}"}, {"fieldId": "field213333", "fieldValue": "={{ $('Convert data for KlickTipp').item.json.event_start_date_time }}"}, {"fieldId": "field213334", "fieldValue": "={{ $('Convert data for KlickTipp').item.json.event_end_date_time }}"}, {"fieldId": "field213335", "fieldValue": "={{ $('Convert data for KlickTipp').item.json.event_start_date_time }}"}, {"fieldId": "field213336", "fieldValue": "={{ $('Convert data for KlickTipp').item.json.event_end_date_time }}"}, {"fieldId": "field213337", "fieldValue": "={{ $('Convert data for KlickTipp').item.json.invitee_start_time_seconds }}"}, {"fieldId": "field213338", "fieldValue": "={{ $('Convert data for KlickTipp').item.json.invitee_end_time_seconds }}"}, {"fieldId": "field213339", "fieldValue": "={{ $('New Calendly event').item.json.payload.timezone }}"}, {"fieldId": "field214142", "fieldValue": "={{ $('Convert data for KlickTipp').item.json.guest_addresses }}"}]}, "listId": "358895", "resource": "subscriber", "operation": "subscribe", "smsNumber": "={{ $('Convert data for KlickTipp').item.json.invitee_mobile }}"}, "credentials": {"klickTippApi": {"id": "K9JyBdCM4SZc1cXl", "name": "DEMO KlickTipp account"}}, "notesInFlow": true, "typeVersion": 2}, {"id": "5bc59f89-b89f-4fa0-b481-b66bcc8698d6", "name": "Subscribe guest booking in KlickTipp", "type": "n8n-nodes-klicktipp.klicktipp", "notes": "Adds guests to the KlickTipp subscriber list for the associated booking.", "position": [2500, 200], "parameters": {"email": "={{ $json.guests.email }}", "tagId": "********", "fields": {"dataFields": [{"fieldId": "field213329", "fieldValue": "={{ $('New Calendly event').item.json.payload.scheduled_event.name }}"}, {"fieldId": "field213330", "fieldValue": "={{ $('New Calendly event').item.json.payload.scheduled_event.location.join_url }}"}, {"fieldId": "field213331", "fieldValue": "={{ $('New Calendly event').item.json.payload.scheduled_event.location.join_url }}"}, {"fieldId": "field213332", "fieldValue": "={{ $('New Calendly event').item.json.payload.cancel_url }}"}, {"fieldId": "field213333", "fieldValue": "={{ $('Convert data for KlickTipp').item.json.event_start_date_time }}"}, {"fieldId": "field213334", "fieldValue": "={{ $('Convert data for KlickTipp').item.json.event_end_date_time }}"}, {"fieldId": "field213335", "fieldValue": "={{ $('Convert data for KlickTipp').item.json.invitee_start_date }}"}, {"fieldId": "field213336", "fieldValue": "={{ $('Convert data for KlickTipp').item.json.invitee_end_date }}"}, {"fieldId": "field213337", "fieldValue": "={{ $('Convert data for KlickTipp').item.json.invitee_start_time_seconds }}"}, {"fieldId": "field213338", "fieldValue": "={{ $('Convert data for KlickTipp').item.json.invitee_end_time_seconds }}"}, {"fieldId": "field213339", "fieldValue": "={{ $('New Calendly event').item.json.payload.timezone }}"}]}, "listId": "358895", "resource": "subscriber", "operation": "subscribe"}, "credentials": {"klickTippApi": {"id": "K9JyBdCM4SZc1cXl", "name": "DEMO KlickTipp account"}}, "notesInFlow": true, "typeVersion": 2}, {"id": "aac23ac2-38de-42bf-b7d8-dfcffbd9f474", "name": "Subscribe guest cancellation in KlickTipp", "type": "n8n-nodes-klicktipp.klicktipp", "notes": "Handles cancellations by removing guests from the subscriber list in KlickTipp.", "position": [2500, 580], "parameters": {"email": "={{ $json.invitee_guests_addresses }}", "tagId": "********", "listId": "358895", "resource": "subscriber", "operation": "subscribe"}, "credentials": {"klickTippApi": {"id": "K9JyBdCM4SZc1cXl", "name": "DEMO KlickTipp account"}}, "notesInFlow": true, "typeVersion": 2}, {"id": "4f38122a-7cf0-427d-bd68-9e2fb4674bc3", "name": "Subscribe invitee cancellation in KlickTipp", "type": "n8n-nodes-klicktipp.klicktipp", "notes": "Handles cancellations by removing the invitee from the subscriber list in KlickTipp.", "position": [1700, 660], "parameters": {"email": "={{ $('New Calendly event').item.json.payload.email }}", "tagId": "********", "listId": "358895", "resource": "subscriber", "operation": "subscribe", "smsNumber": "={{ $('Convert data for KlickTipp').item.json.invitee_mobile }}"}, "credentials": {"klickTippApi": {"id": "K9JyBdCM4SZc1cXl", "name": "DEMO KlickTipp account"}}, "notesInFlow": true, "typeVersion": 2}, {"id": "63f9e951-d1e0-46ea-b189-1386be3dc9a4", "name": "Split Out guest bookings", "type": "n8n-nodes-base.splitOut", "notes": "Splits the guests into individual items for processing their bookings.", "position": [2300, 200], "parameters": {"include": "allOtherFields", "options": {}, "fieldToSplitOut": "guests"}, "notesInFlow": true, "typeVersion": 1}, {"id": "f411bc16-2478-4122-b0f5-e0a67c6cfa61", "name": "Split Out guest cancellations", "type": "n8n-nodes-base.splitOut", "notes": "Splits the guests into individual items for processing their cancellations.", "position": [2300, 580], "parameters": {"include": "allOtherFields", "options": {}, "fieldToSplitOut": "invitee_guests_addresses"}, "notesInFlow": true, "typeVersion": 1}, {"id": "52c157f4-4f7c-479b-9051-10a9557f4c02", "name": "Guests booking check", "type": "n8n-nodes-base.if", "notes": "Validates if there are any guests associated with the booking to process them separately.", "position": [1880, 300], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "0c2ae412-74af-4e9f-99b6-bda9ce59f27e", "operator": {"type": "array", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $('New Calendly event').item.json.payload.scheduled_event.event_guests }}", "rightValue": ""}]}}, "notesInFlow": true, "typeVersion": 2.2}, {"id": "dec38fda-52a1-45ef-9ad6-c3ba90c35683", "name": "Subscribe invitee to empty guest addresses field", "type": "n8n-nodes-klicktipp.klicktipp", "notes": "Writes \"null\" into the field which saves the array of the guests email addresses to prevent errors when rebooking.", "position": [2940, 660], "parameters": {"email": "={{ $('New Calendly event').item.json.payload.email }}", "tagId": "********", "fields": {"dataFields": [{"fieldId": "field214142", "fieldValue": "={{\n//Writes null into the field where the guests e-mail addresses are saved within the invitee contact/record.\nnull}}"}]}, "listId": "358895", "resource": "subscriber", "operation": "subscribe", "smsNumber": "={{ $('Convert data for KlickTipp').item.json.invitee_mobile }}"}, "credentials": {"klickTippApi": {"id": "K9JyBdCM4SZc1cXl", "name": "DEMO KlickTipp account"}}, "notesInFlow": true, "typeVersion": 2}, {"id": "c9eb8503-ab46-43b6-b8c0-c04e3bfad2c7", "name": "New Calendly event", "type": "n8n-nodes-base.calendlyTrigger", "notes": "This node triggers the workflow whenever an event is booked or canceled in Calendly.", "position": [980, 360], "webhookId": "f5440e40-1e7f-4ef1-b639-b8b65832a1a6", "parameters": {"events": ["invitee.created", "invitee.canceled"]}, "credentials": {"calendlyApi": {"id": "xDtep5NpxCyWRmzW", "name": "Ricardo <PERSON> account"}}, "notesInFlow": true, "typeVersion": 1}, {"id": "495adbe6-dc4b-4fdd-93da-da4cff573e8f", "name": "Convert data for KlickTipp", "type": "n8n-nodes-base.set", "notes": "Formats the timestamps provided by Calendly so they are within the format that KlickTipp expects. UNIX Timestamps for date and date&time values and the time fields expects to receive the time in amounts of seconds since midnight.", "position": [1200, 360], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "93769f47-287f-4e4c-8e8d-86b557baa9ac", "name": "event_start_date_time", "type": "string", "value": "={{ \n//Converts the date and time value to a Unix timestamp since this is the expected format for date&time values in KlickTipp.\nnew Date($('New Calendly event').item.json.payload.scheduled_event.start_time).getTime() / 1000 }}"}, {"id": "47f1638b-2c43-42c6-945c-e444bdd648bc", "name": "event_end_date_time", "type": "string", "value": "={{ \n//Converts the date and time value to a Unix timestamp since this is the expected format for date&time values in KlickTipp.\nnew Date($('New Calendly event').item.json.payload.scheduled_event.end_time).getTime() / 1000 }}"}, {"id": "ceeed6fa-3715-4bf0-9929-a93e465d291e", "name": "invitee_start_date", "type": "string", "value": "={{      \n// Converts the date into an UNIX timestamp since this is the expected format for date values in KlickTipp.\nnew Date(new Date($json.payload.scheduled_event.start_time).toLocaleString('en-US', { timeZone: $json.payload.timezone })).getTime() / 1000  }}"}, {"id": "86165bd2-6e2f-4995-872b-14768c28ee9b", "name": "invitee_end_date", "type": "string", "value": "={{      \n// Converts the date into an UNIX timestamp since this is the expected format for date values in KlickTipp.\nnew Date(new Date($json.payload.scheduled_event.end_time).toLocaleString('en-US', { timeZone: $json.payload.timezone })).getTime() / 1000  }}"}, {"id": "88535bfa-2fc1-4559-8e7c-a2391fcecac7", "name": "invitee_start_time_seconds", "type": "string", "value": "={{      \n// Converts the time to seconds since midnight since this is the expected format for time values in KlickTipp.\nnew Date(new Date($json.payload.scheduled_event.start_time).toLocaleString('en-US', { timeZone: $json.payload.timezone })).getHours() * 3600      + new Date(new Date($json.payload.scheduled_event.start_time).toLocaleString('en-US', { timeZone: $json.payload.timezone })).getMinutes() * 60      + new Date(new Date($json.payload.scheduled_event.start_time).toLocaleString('en-US', { timeZone: $json.payload.timezone })).getSeconds()  }}"}, {"id": "240171bf-c174-4922-aba2-a1014f4fd350", "name": "invitee_end_time_seconds", "type": "string", "value": "={{  \n// Converts the time to seconds since midnight since this is the expected format for time values in KlickTipp.\nnew Date(new Date($json.payload.scheduled_event.end_time).toLocaleString('en-US', { timeZone: $json.payload.timezone })).getHours() * 3600      + new Date(new Date($json.payload.scheduled_event.end_time).toLocaleString('en-US', { timeZone: $json.payload.timezone })).getMinutes() * 60      + new Date(new Date($json.payload.scheduled_event.end_time).toLocaleString('en-US', { timeZone: $json.payload.timezone })).getSeconds()  }}"}, {"id": "fbc2ce8b-ffc8-4b03-b869-7ab<PERSON><PERSON>e323", "name": "invitee_first_name", "type": "string", "value": "={{ \n  //Extracts first_name. If not available, extracts from name by taking all but the last word(s).\n\n  $json.payload.first_name // Use first_name directly if available\n    ? $json.payload.first_name \n    : $json.payload.name \n      ? $json.payload.name.split(' ').slice(0, -1).join(' ') // Extract all words except the last as first names\n      : '' // Default to empty string if both are missing\n}}\n"}, {"id": "e269a0dc-4c05-49f6-8595-e8ceb3701259", "name": "invitee_last_name", "type": "string", "value": "={{ \n  //Extracts last_name. If not available, extracts from name by taking the last word(s).\n  $json.payload.last_name // Use last_name directly if available\n    ? $json.payload.last_name \n    : $json.payload.name \n      ? $json.payload.name.split(' ').slice(-1).join('') // Extract the last word(s) as the last name\n      : '' // Default to empty string if both are missing\n}}"}, {"id": "3b69338b-1f62-4148-a640-25b2110da1d6", "name": "invitee_mobile", "type": "string", "value": "={{ \n  // Converts the phone number by replacing '+' with '00' and removing all spaces for standardization.\n  $('New Calendly event').item.json.payload.text_reminder_number\n    .replace('+', '00') // Replace '+' with '00'\n    .replace(/\\s+/g, '') // Remove all spaces\n}}\n"}, {"id": "57be44f3-fc01-4ab7-9917-ecd9a1d7a584", "name": "guest_addresses", "type": "string", "value": "={{ \n//Extracts the email addresses of the guests and creates a list of them.\n$('New Calendly event').item.json.payload.scheduled_event.event_guests.map(guest => guest.email) }}"}]}}, "notesInFlow": true, "typeVersion": 3.4}, {"id": "fb8e7feb-f8c3-4177-b8dd-c0ca5ff15626", "name": "Check event - booking or cancellation?", "type": "n8n-nodes-base.if", "notes": "Validates if an event booking or cancellation is being processed.", "position": [1440, 360], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "61a4200d-9660-488a-ad0a-ea03d37f69d3", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $('New Calendly event').item.json.payload.scheduled_event.status }}", "rightValue": "=active"}]}}, "notesInFlow": true, "typeVersion": 2.2}, {"id": "cb5665a9-8973-4a9c-b9df-f0cbbd5aaf45", "name": "List guests for booking", "type": "n8n-nodes-base.set", "notes": "Prepares the guest data for subscription into KlickTipp during booking.", "position": [2100, 200], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "67b36bb6-d82e-4631-9103-fde87217e556", "name": "guests", "type": "array", "value": "={{ $('New Calendly event').item.json.payload.scheduled_event.event_guests.map(guest => ({ email: guest.email })) }}"}]}}, "notesInFlow": true, "typeVersion": 3.4}, {"id": "f9b2d284-fcc1-4746-90eb-e1ecf004e3c0", "name": "List guests for cancellation", "type": "n8n-nodes-base.set", "notes": "Prepares the guest data for subscription removal in KlickTipp during cancellations.", "position": [2100, 580], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "a06f26f5-3246-425e-901a-22370133ce64", "name": "invitee_guests_addresses", "type": "array", "value": "={{ JSON.parse($json.field214142.replace(/&quot;/g, '\"')) }}"}]}}, "notesInFlow": true, "typeVersion": 3.4}, {"id": "b5cac1bc-f20a-4c66-a1e7-df0d5187e28d", "name": "Guests cancellation check", "type": "n8n-nodes-base.if", "notes": "Validates if there are guest email addresses within the result of the subscription process of the invitee cancellation so that the cancellations can be transmitted as well. Since <PERSON><PERSON><PERSON> does not provide a list of guests upon cancellation we store this information inside the invitee contact in KlickTipp and read it out.", "position": [1880, 660], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a41b92de-b135-43f6-9fd9-fb5fe5f596ae", "operator": {"type": "string", "operation": "contains"}, "leftValue": "={{ $json.field214142 }}", "rightValue": "@"}]}}, "notesInFlow": true, "typeVersion": 2.2}, {"id": "aa0fa3e7-72aa-49fe-b568-280b8686e71b", "name": "Rescheduling check", "type": "n8n-nodes-base.if", "notes": "This node checks whether the cancellation is due to a rescheduling of the original booking or not. In case it is a rescheduling, we are not overwriting the string of guest email addresses within the invitee record.", "position": [2720, 580], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "51e6485f-ea0a-42f7-b772-bb6513eb8615", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $('New Calendly event').item.json.payload.rescheduled }}", "rightValue": ""}]}}, "notesInFlow": true, "typeVersion": 2.2}, {"id": "b3db3b20-f579-42a7-ac09-c856725791ec", "name": "In<PERSON><PERSON> did not add guests to the booking", "type": "n8n-nodes-base.noOp", "position": [2100, 400], "parameters": {}, "typeVersion": 1}, {"id": "5ee8be1b-b4a1-4229-b191-b6034218527d", "name": "Event was rescheduled", "type": "n8n-nodes-base.noOp", "position": [2940, 500], "parameters": {}, "typeVersion": 1}, {"id": "fe8ed37b-cb1f-4ee0-99ac-7dfefdc0a670", "name": "No guest email addresses found", "type": "n8n-nodes-base.noOp", "notes": "If no guest E-Mail Addresses were found inside the invitee record there are no guest cancellations that must be processed as there were no guests involved in the original event booking.", "position": [2100, 760], "parameters": {}, "notesInFlow": true, "typeVersion": 1}, {"id": "90515b4f-8c56-4dd9-8935-9aa0913a234b", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1840, 960], "parameters": {"width": 1133.0384930384926, "height": 1689.5659295659311, "content": "### Introduction\nThis workflow streamlines the integration between Calendly and KlickTipp, managing bookings and cancellations dynamically while ensuring accurate data transformation and seamless synchronization. Input data is validated and formatted to meet KlickTipp’s API requirements, including handling guests, rescheduling, and cancellations.\n\n### Benefits\n- **Improved scheduling management**: Automatically processes bookings and cancellations in Calendly, saving time and reducing errors. Contacts are automatically imported into KlickTipp and can be used immediately, saving time and increasing the conversion rate.\n- **Automated processes**: Experts can start workflows directly, such as welcome emails or course admissions, reducing administrative effort.\n- **Error-free data management**: The template ensures precise data mapping, avoids manual corrections, and reinforces a professional appearance.\n\n### Key Features\n- **Calendly Trigger**: Captures booking and cancellation events, including invitee and guest details.\n- **Data Processing**: Validates and standardizes input fields:\n  - Converts dates to UNIX timestamps for API compatibility.\n  - Processes guests dynamically, splitting guest emails into individual records.\n  - Validates invitee email addresses to ensure accuracy.\n- **Subscriber Management in KlickTipp**: Adds or updates invitees and guests as subscribers in KlickTipp. Supports custom field mappings such as:\n  - Invitee information: Name, email, booking details.\n  - Event details: Start/end times, timezone, and guest emails.\n- **Error Handling**: Differentiates between cancellations and rescheduling, preventing redundant or incorrect updates.\n\n#### Setup Instructions\n1. Install the required nodes:\n   - Ensure the KlickTipp community node and its dependencies are installed.\n2. Authenticate your Calendly and KlickTipp accounts.\n3. Pre-create the following custom fields in KlickTipp to align with workflow requirements.\n4. Open each KlickTipp node and map the fields to align with your setup.\n\n![Screenshot Description](https://mail.cdndata.io/user/images/kt1073234/share_link_calendly_fields_v2.png)\n\n### Testing and Deployment\n1. Test the workflow by triggering a Calendly event.\n2. Verify that the invitee and guest data is updated accurately in KlickTipp.\n\n- **Customization**: Adjust field mappings within KlickTipp nodes to match your specific account setup.\n\n"}, "typeVersion": 1}], "pinData": {}, "connections": {"New Calendly event": {"main": [[{"node": "Convert data for KlickTipp", "type": "main", "index": 0}]]}, "Rescheduling check": {"main": [[{"node": "Event was rescheduled", "type": "main", "index": 0}], [{"node": "Subscribe invitee to empty guest addresses field", "type": "main", "index": 0}]]}, "Guests booking check": {"main": [[{"node": "List guests for booking", "type": "main", "index": 0}], [{"node": "In<PERSON><PERSON> did not add guests to the booking", "type": "main", "index": 0}]]}, "List guests for booking": {"main": [[{"node": "Split Out guest bookings", "type": "main", "index": 0}]]}, "Split Out guest bookings": {"main": [[{"node": "Subscribe guest booking in KlickTipp", "type": "main", "index": 0}]]}, "Guests cancellation check": {"main": [[{"node": "List guests for cancellation", "type": "main", "index": 0}], [{"node": "No guest email addresses found", "type": "main", "index": 0}]]}, "Convert data for KlickTipp": {"main": [[{"node": "Check event - booking or cancellation?", "type": "main", "index": 0}]]}, "List guests for cancellation": {"main": [[{"node": "Split Out guest cancellations", "type": "main", "index": 0}]]}, "Split Out guest cancellations": {"main": [[{"node": "Subscribe guest cancellation in KlickTipp", "type": "main", "index": 0}]]}, "Check event - booking or cancellation?": {"main": [[{"node": "Subscribe invitee booking in KlickTipp", "type": "main", "index": 0}], [{"node": "Subscribe invitee cancellation in KlickTipp", "type": "main", "index": 0}]]}, "Subscribe invitee booking in KlickTipp": {"main": [[{"node": "Guests booking check", "type": "main", "index": 0}]]}, "Subscribe guest cancellation in KlickTipp": {"main": [[{"node": "Rescheduling check", "type": "main", "index": 0}]]}, "Subscribe invitee cancellation in KlickTipp": {"main": [[{"node": "Guests cancellation check", "type": "main", "index": 0}]]}}}