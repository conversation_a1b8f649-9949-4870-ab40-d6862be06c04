{"id": "wng5xcxlYA6jFS6n", "meta": {"instanceId": "d2672089b9e343ad3bc90ba1f1f190228becae384176d65238d27962069ff47d", "templateCredsSetupCompleted": true}, "name": "MAIA - Health Check", "tags": [], "nodes": [{"id": "10335465-853d-47ea-aad7-34460c741b74", "name": "No Operation, do nothing", "type": "n8n-nodes-base.noOp", "position": [860, -20], "parameters": {}, "typeVersion": 1}, {"id": "ea7771ba-3d35-423a-9813-2a65448a15fc", "name": "Telegram", "type": "n8n-nodes-base.telegram", "position": [860, 160], "webhookId": "6c02772a-8f40-4d9b-8fe5-220aac63c34e", "parameters": {"text": "=Health Check :  {{ $json.URLS }}\n\n{{ $json.error.code }}", "chatId": "**********", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "QYefc34gGshZQURo", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "bae03dc7-e35b-4760-8de8-151d2d97391b", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [0, 0], "parameters": {"rule": {"interval": [{"field": "minutes"}]}}, "typeVersion": 1.2}, {"id": "a53fba9c-0f7e-4757-8bcd-e3622845e804", "name": "Fetch Urls", "type": "n8n-nodes-base.googleSheets", "position": [220, 0], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/17-tY9_wn-D2FV627Sx3-Z3abqFYvz794edej7es5J6w/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "id", "value": "17-tY9_wn-D2FV627Sx3-Z3abqFYvz794edej7es5J6w"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "rh63B66L9pJsButh", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "c96a2070-953b-4a03-a308-dae92d841851", "name": "Check URL", "type": "n8n-nodes-base.httpRequest", "onError": "continueErrorOutput", "position": [520, 0], "parameters": {"url": "={{ $json.URLS }}", "options": {}}, "typeVersion": 4.2, "alwaysOutputData": false}, {"id": "4fe54bbe-32ef-41d2-94f8-2a7d4ec175b6", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [160, -220], "parameters": {"content": "## Step 1\nCreate a new google sheet where A1 is a title, and then list in column A all the urls you want to check."}, "typeVersion": 1}, {"id": "96f8b3bb-d3e1-415a-a849-84b1d524acb5", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [800, 320], "parameters": {"content": "## Step 2\nTo use telegram, simply define chatid.\n\nYou can replace with any type of notification like slack, etc..."}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "704c7308-7759-4f31-ab94-c2c53e3c5ed7", "connections": {"Check URL": {"main": [[{"node": "No Operation, do nothing", "type": "main", "index": 0}], [{"node": "Telegram", "type": "main", "index": 0}]]}, "Fetch Urls": {"main": [[{"node": "Check URL", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Fetch Urls", "type": "main", "index": 0}]]}}}