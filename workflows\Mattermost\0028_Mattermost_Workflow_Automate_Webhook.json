{"nodes": [{"name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [490, 460], "webhookId": "c0345765-4488-4ac8-a9da-02f647dd2b90", "parameters": {"path": "c0345765-4488-4ac8-a9da-02f647dd2b90", "options": {}}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [690, 460], "parameters": {"values": {"string": [{"name": "Message", "value": "Hello!"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "Mattermost", "type": "n8n-nodes-base.mattermost", "position": [690, 610], "parameters": {"message": "=The workflow {{$workflow.name}}, was updated.", "channelId": "toyi3uoycf8rirtm7d5jm15sso", "attachments": [], "otherOptions": {}}, "credentials": {"mattermostApi": "Mattermost Credentials"}, "typeVersion": 1}, {"name": "Workflow Trigger", "type": "n8n-nodes-base.workflowTrigger", "position": [490, 610], "parameters": {"events": ["update"]}, "typeVersion": 1}], "connections": {"Webhook": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}, "Workflow Trigger": {"main": [[{"node": "Mattermost", "type": "main", "index": 0}]]}}}