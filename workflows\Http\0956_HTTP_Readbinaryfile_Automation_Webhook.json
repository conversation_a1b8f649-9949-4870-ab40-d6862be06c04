{"nodes": [{"name": "Read Binary File", "type": "n8n-nodes-base.readBinaryFile", "position": [450, 300], "parameters": {"filePath": "/data/demo1.wav"}, "typeVersion": 1}, {"name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [650, 300], "parameters": {"url": "https://api.wit.ai/speech?v=20200513", "options": {"bodyContentType": "raw"}, "requestMethod": "POST", "jsonParameters": true, "sendBinaryData": true, "headerParametersJson": "={{JSON.parse('{\"Authorization\":\"Bearer {your_token_goes_here}\", \"Content-Type\":\"audio/wav\"}')}}"}, "typeVersion": 1}], "connections": {"Read Binary File": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}}}