{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "b0224d75-763d-4f06-8aa3-3f1b4c5ca96d", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [800, 500], "parameters": {"workflowInputs": {"values": [{"name": "operation"}, {"name": "repo"}, {"name": "issueNumber"}, {"name": "text"}]}}, "typeVersion": 1.1}, {"id": "dd0e2ff0-af31-4503-a276-65682a3009a8", "name": "Operation", "type": "n8n-nodes-base.switch", "position": [980, 500], "parameters": {"rules": {"values": [{"outputKey": "getLatestIssues", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "81b134bc-d671-4493-b3ad-8df9be3f49a6", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.operation }}", "rightValue": "getLatestIssues"}]}, "renameOutput": true}, {"outputKey": "getIssueComments", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "8d57914f-6587-4fb3-88e0-aa1de6ba56c1", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.operation }}", "rightValue": "getIssueComments"}]}, "renameOutput": true}, {"outputKey": "addIssueComment", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "7c38f238-213a-46ec-aefe-22e0bcb8dffc", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.operation }}", "rightValue": "addIssueComment"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "bc35f181-e3a4-4aa4-8132-26cd4a6ced8a", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [0, 120], "parameters": {"color": 7, "width": 680, "height": 660, "content": "## 1. Set up an MCP Server Trigger\n[Read more about the MCP Server Trigger](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-langchain.mcptrigger)"}, "typeVersion": 1}, {"id": "e4c8d338-08ad-4c47-935b-b5ea53dc59d7", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [700, 120], "parameters": {"color": 7, "width": 560, "height": 300, "content": "## 2. Build Simple Support Tools with Github Node\n[Read more about the Github Node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.github)\n\nWhilst it may be easier to just let the Agent provide the full raw SQL statement,\nit may expose you or your organisation to a real security risk where in the worst\ncase, data may be unknowingly leaked or deleted.\n\nForcing the agent to provide only the parameters of the query\nmeans we can guard somewhat against this risk and also allows\nuse of query parameters as best practice against SQL injection attacks.\n"}, "typeVersion": 1}, {"id": "5d6a5f6d-24e8-48ed-8409-8cd24cc2e668", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [0, 0], "parameters": {"color": 5, "width": 380, "height": 100, "content": "### Always Authenticate Your Server!\nBefore going to production, it's always advised to enable authentication on your MCP server trigger."}, "typeVersion": 1}, {"id": "fd11a97d-cd3d-4356-81d3-4266f65ef606", "name": "Github MCP Server", "type": "@n8n/n8n-nodes-langchain.mcpTrigger", "position": [160, 300], "webhookId": "61848df7-3619-4ccf-831b-d6408e0d6519", "parameters": {"path": "61848df7-3619-4ccf-831b-d6408e0d6519"}, "typeVersion": 1}, {"id": "b8fd8431-71fa-44d1-abdb-b50e6a8a940f", "name": "Get Latest Issues", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [160, 540], "parameters": {"name": "getLatestIssues", "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "description": "Retrieves the latest issues from the github respository.", "workflowInputs": {"value": {"repo": "n8n-io/n8n", "text": "null", "operation": "getLatestIssues", "issueNumber": "null"}, "schema": [{"id": "operation", "type": "string", "display": true, "removed": false, "required": false, "displayName": "operation", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "repo", "type": "string", "display": true, "removed": false, "required": false, "displayName": "repo", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "issueNumber", "type": "string", "display": true, "removed": false, "required": false, "displayName": "issueNumber", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "text", "type": "string", "display": true, "removed": false, "required": false, "displayName": "text", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2.1}, {"id": "a11f7b8a-aaa9-41de-a693-6d0463e48d10", "name": "Add Issue Comment", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [480, 540], "parameters": {"name": "addIssueComment", "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "description": "Call this tool to add a comment to the github issue.", "workflowInputs": {"value": {"repo": "n8n-io/n8n", "text": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('text', ``, 'string') }}", "operation": "addIssueComment", "issueNumber": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('issueNumber', ``, 'string') }}"}, "schema": [{"id": "operation", "type": "string", "display": true, "removed": false, "required": false, "displayName": "operation", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "repo", "type": "string", "display": true, "removed": false, "required": false, "displayName": "repo", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "issueNumber", "type": "string", "display": true, "removed": false, "required": false, "displayName": "issueNumber", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "text", "type": "string", "display": true, "removed": false, "required": false, "displayName": "text", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2.1}, {"id": "57e8370b-caf0-4632-98e3-78316b2cb262", "name": "Simplify Issues", "type": "n8n-nodes-base.set", "position": [1500, 320], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "6d5eb037-7e52-4595-a2da-bb183674ea2a", "name": "issue_number", "type": "number", "value": "={{ $json.number }}"}, {"id": "3d365039-f012-444c-a383-c6c70fb93e9d", "name": "title", "type": "string", "value": "={{ $json.title }}"}, {"id": "20a1b658-c56c-4578-9b1f-350b454da2d2", "name": "url", "type": "string", "value": "={{ $json.url }}"}, {"id": "0eb6930d-2ea9-4a83-bab7-5f673e79c1d1", "name": "reported_by", "type": "string", "value": "={{ $json.user.login }}"}, {"id": "2d71c6de-ab54-4721-9e1c-5193350a5110", "name": "state", "type": "string", "value": "={{ $json.state }}"}, {"id": "474166aa-4bfa-4230-bce4-28df2de47bed", "name": "created_at", "type": "string", "value": "={{ $json.created_at }}"}, {"id": "e4784fc1-4438-4d7a-a2f5-86be077ae7ae", "name": "updated_at", "type": "string", "value": "={{ $json.updated_at }}"}, {"id": "e0639b60-4a08-406a-be8e-c3565a519f0c", "name": "body", "type": "string", "value": "={{ $json.body }}"}]}}, "typeVersion": 3.4}, {"id": "632b1286-7e4a-457b-8544-6ca8f2affb9f", "name": "Aggregate Results", "type": "n8n-nodes-base.aggregate", "position": [1680, 320], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "response"}, "typeVersion": 1}, {"id": "447327bc-0b42-47ec-80c0-14d6f521d047", "name": "Get Issue Comments", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [320, 600], "parameters": {"name": "getIssueComments", "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "description": "Retrieves the issue and associated comments and discussion", "workflowInputs": {"value": {"repo": "n8n-io/n8n", "text": "null", "operation": "getIssueComments", "issueNumber": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('issueNumber', ``, 'string') }}"}, "schema": [{"id": "operation", "type": "string", "display": true, "required": false, "displayName": "operation", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "repo", "type": "string", "display": true, "removed": false, "required": false, "displayName": "repo", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "issueNumber", "type": "string", "display": true, "removed": false, "required": false, "displayName": "issueNumber", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "text", "type": "string", "display": true, "removed": false, "required": false, "displayName": "text", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2.1}, {"id": "f5c59a05-54e4-4aa5-bef3-192e07adffb0", "name": "Get Comments", "type": "n8n-nodes-base.httpRequest", "position": [1500, 500], "parameters": {"url": "={{ $json.comments_url }}", "options": {}, "authentication": "predefinedCredentialType", "nodeCredentialType": "githubApi"}, "credentials": {"githubApi": {"id": "kA70YRmLeHDqZbXA", "name": "GitHub account"}}, "typeVersion": 4.2}, {"id": "3fe80456-9fb5-47bb-80d9-484123571a8f", "name": "Simplify Comments", "type": "n8n-nodes-base.set", "position": [1680, 500], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "6e09ed44-a72c-4915-84f4-0796b45158a7", "name": "id", "type": "number", "value": "={{ $json.id }}"}, {"id": "76c34251-7f40-42bc-bb98-17e7fe52d9ed", "name": "issue_url", "type": "string", "value": "={{ $json.issue_url }}"}, {"id": "1094dd36-d18d-4ada-ac49-5347f0f245ae", "name": "user", "type": "string", "value": "={{ $json.user.login }}"}, {"id": "59b50536-4e0a-46bc-919b-685066253f45", "name": "author_association", "type": "string", "value": "={{ $json.author_association }}"}, {"id": "6253bae9-aaff-4a88-9e5a-64126ed80cc4", "name": "body", "type": "string", "value": "={{ $json.body }}"}, {"id": "3944598d-8204-45a0-9e0b-448d3cfa5a87", "name": "created_at", "type": "string", "value": "={{ $json.created_at }}"}, {"id": "3f395b51-6e57-4d07-9cf9-9a03e7a40c51", "name": "updated_at", "type": "string", "value": "={{ $json.updated_at }}"}]}}, "typeVersion": 3.4}, {"id": "7926ae2d-5408-4b10-88f3-e6ebfe5f9619", "name": "Aggregate Comments", "type": "n8n-nodes-base.aggregate", "position": [1860, 500], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "response"}, "typeVersion": 1}, {"id": "af2b4c0f-4a83-44a2-bae8-b3c45861d820", "name": "Get Many Issues", "type": "n8n-nodes-base.github", "position": [1320, 320], "webhookId": "e08dcf3e-66bb-4ba5-a868-d8c41a98bc95", "parameters": {"limit": 10, "owner": {"__rl": true, "mode": "name", "value": "={{ $json.repo.split('/')[0] }}"}, "resource": "repository", "repository": {"__rl": true, "mode": "name", "value": "={{ $json.repo.split('/')[1] }}"}, "getRepositoryIssuesFilters": {"sort": "created"}}, "credentials": {"githubApi": {"id": "kA70YRmLeHDqZbXA", "name": "GitHub account"}}, "typeVersion": 1.1}, {"id": "********-5f46-4338-a799-a1854ebc425e", "name": "Get Single Issue", "type": "n8n-nodes-base.github", "position": [1320, 500], "webhookId": "e08dcf3e-66bb-4ba5-a868-d8c41a98bc95", "parameters": {"owner": {"__rl": true, "mode": "name", "value": "={{ $json.repo.split('/')[0] }}"}, "operation": "get", "repository": {"__rl": true, "mode": "name", "value": "={{ $json.repo.split('/')[1] }}"}, "issueNumber": "={{ $json.issueNumber }}"}, "credentials": {"githubApi": {"id": "kA70YRmLeHDqZbXA", "name": "GitHub account"}}, "typeVersion": 1.1}, {"id": "1a12fadd-e436-4731-ad66-b9d9cdb9c61c", "name": "Create Comment", "type": "n8n-nodes-base.github", "position": [1320, 680], "webhookId": "e08dcf3e-66bb-4ba5-a868-d8c41a98bc95", "parameters": {"body": "={{ $json.text }}", "owner": {"__rl": true, "mode": "name", "value": "={{ $json.repo.split('/')[0] }}"}, "operation": "createComment", "repository": {"__rl": true, "mode": "name", "value": "={{ $json.repo.split('/')[1] }}"}, "issueNumber": "={{ $json.issueNumber }}"}, "credentials": {"githubApi": {"id": "kA70YRmLeHDqZbXA", "name": "GitHub account"}}, "typeVersion": 1.1}, {"id": "b90acf56-c871-49de-95d0-1c6ceb1799f7", "name": "Get Response", "type": "n8n-nodes-base.set", "position": [1500, 680], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "65631bfa-7448-4188-8cc1-b812361ae9b1", "name": "response", "type": "string", "value": "ok"}]}}, "typeVersion": 3.4}, {"id": "da360f61-4251-4f0f-8081-3b502e9981c9", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-460, -480], "parameters": {"width": 440, "height": 1260, "content": "## Try It Out!\n### This n8n demonstrates how to build your own Github MCP server to personalise it to your organisation's repositories, issues and pull requests.\n\nThis n8n implementation, though not as fully featured as the official MCP server offered by Github, allows you to control precisely what access and/or functionality is granted to users which can make MCP use simpler and in some cases, more secure. The use-case in this template is to simply view and comment on issues within a specific repository but can be extended to meet the needs of your team.\n\nThis MCP example is based off an official MCP reference implementation which can be found here https://github.com/modelcontextprotocol/servers/tree/main/src/github\n\n### How it works\n* A MCP server trigger is used and connected to 3 custom workflow tools. We're using custom workflow tools as there is quite a few nodes required for each task.\n* Behind these tools are regular Github nodes although preconfigured with credentials and targeted repository.\n* The \"Get Issue Comments\" and \"Create Issue Comment\" tools depend on obtaining an Issue Number first. The agent should call the \"Get Latest Issues\" tool for this.\n\n### How to use\n* This Github MCP server allows any compatible MCP client to view and comment on Github Issues. You will need to have a Github account and repository access available before you can use this server.\n* Connect your MCP client by following the n8n guidelines here - https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-langchain.mcptrigger/#integrating-with-claude-desktop\n* Try the following queries in your MCP client:\n  * \"Can you get me the latest issues about MCP?\"\n  * \"What is the current progress on Issue 12345?\"\n  * \"Please can you add a comment to Issue 12345 that they should try installing the latest version and see if that works?\"\n\n### Requirements\n* Github for account and repository access. The repository need not be your own but you'll still need to ensure you have the correct permissions.\n* MCP Client or Agent for usage such as Claude Desktop - https://claude.ai/download\n\n### Customising this workflow\n* Extend this template to interactive with pull requests or workflows within your own company's Github repositories. Alternatively,  pull in metrics and generate reports for programme managers.\n* Remember to set the MCP server to require credentials before going to production and sharing this MCP server with others!"}, "typeVersion": 1}], "pinData": {}, "connections": {"Operation": {"main": [[{"node": "Get Many Issues", "type": "main", "index": 0}], [{"node": "Get Single Issue", "type": "main", "index": 0}], [{"node": "Create Comment", "type": "main", "index": 0}]]}, "Get Comments": {"main": [[{"node": "Simplify Comments", "type": "main", "index": 0}]]}, "Create Comment": {"main": [[{"node": "Get Response", "type": "main", "index": 0}]]}, "Get Many Issues": {"main": [[{"node": "Simplify Issues", "type": "main", "index": 0}]]}, "Simplify Issues": {"main": [[{"node": "Aggregate Results", "type": "main", "index": 0}]]}, "Get Single Issue": {"main": [[{"node": "Get Comments", "type": "main", "index": 0}]]}, "Add Issue Comment": {"ai_tool": [[{"node": "Github MCP Server", "type": "ai_tool", "index": 0}]]}, "Get Latest Issues": {"ai_tool": [[{"node": "Github MCP Server", "type": "ai_tool", "index": 0}]]}, "Simplify Comments": {"main": [[{"node": "Aggregate Comments", "type": "main", "index": 0}]]}, "Get Issue Comments": {"ai_tool": [[{"node": "Github MCP Server", "type": "ai_tool", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Operation", "type": "main", "index": 0}]]}}}