{"nodes": [{"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.lemlistTrigger", "position": [410, 160], "webhookId": "e1e29f99-7222-488a-826f-5af50ffe7505", "parameters": {"event": "emailsReplied", "options": {"campaignId": "cam_H5pYEryq6mRKBiy5v"}}, "credentials": {"lemlistApi": "Lemlist API Credentials"}, "typeVersion": 1}, {"name": "Mattermost", "type": "n8n-nodes-base.mattermost", "position": [610, 160], "parameters": {"message": "={{$json[\"firstName\"]}} has replied back to your {{$json[\"campaignName\"]}}. Below is the reply:\n> {{$json[\"text\"]}}", "channelId": "qx9yo1i9z3bg5qcy5a1oxnh69c", "attachments": [], "otherOptions": {}}, "credentials": {"mattermostApi": "Mattermost Credentials"}, "typeVersion": 1}], "connections": {"Lemlist Trigger": {"main": [[{"node": "Mattermost", "type": "main", "index": 0}]]}}}