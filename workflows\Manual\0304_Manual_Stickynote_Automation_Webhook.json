{"nodes": [{"id": "9d09405e-64a3-47ef-9d46-4942de51444b", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [400, 460], "parameters": {}, "typeVersion": 1}, {"id": "4fdc396b-07bd-471e-9e62-136300804809", "name": "Set URLs", "type": "n8n-nodes-base.code", "position": [620, 460], "parameters": {"jsCode": "return [{\n  json: {\n    url: \"https://static.thomasmartens.eu/n8n/file01.jpg\"\n  }\n}, {\n  json: {\n    url: \"https://static.thomasmartens.eu/n8n/file02.jpg\"\n  }\n}, {\n  json: {\n    url: \"https://static.thomasmartens.eu/n8n/file03.jpg\"\n  }\n}]"}, "typeVersion": 1}, {"id": "17482568-2117-4a8c-a307-ebf30dc9c560", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [840, 460], "parameters": {"url": "={{ $json.url }}", "options": {"response": {"response": {"responseFormat": "file"}}}}, "typeVersion": 4}, {"id": "de27f52b-8f7e-4b9c-a097-987db4cef5aa", "name": "Merge items", "type": "n8n-nodes-base.code", "position": [1060, 460], "parameters": {"jsCode": "let binaries = {}, binary_keys = [];\n\nfor (const [index, inputItem] of Object.entries($input.all())) {\n  binaries[`data_${index}`] = inputItem.binary.data;\n  binary_keys.push(`data_${index}`);\n}\n\nreturn [{\n    json: {\n        binary_keys: binary_keys.join(',')\n    },\n    binary: binaries\n}];\n"}, "typeVersion": 1}, {"id": "539fe99d-c557-4e51-bc88-011fb604e1f3", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [580, 320], "parameters": {"width": 394, "height": 304, "content": "## Example data\nThese nodes simply download some example files to work with."}, "typeVersion": 1}, {"id": "710fd054-2360-447a-b503-049507c0a3b2", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1000, 320], "parameters": {"width": 304, "height": 307, "content": "## Transformation\nThis is where the magic happens. Multiple items with one binary object each are being transformed into one item with multiple binary objects."}, "typeVersion": 1}], "connections": {"Set URLs": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Merge items", "type": "main", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "Set URLs", "type": "main", "index": 0}]]}}}