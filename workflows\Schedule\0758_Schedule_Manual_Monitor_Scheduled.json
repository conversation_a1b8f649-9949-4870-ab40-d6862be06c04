{"meta": {"instanceId": "cb484ba7b742928a2048bf8829668bed5b5ad9787579adea888f05980292a4a7"}, "nodes": [{"id": "692e2883-0d1b-4162-8472-6d15c12c8b43", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [0, 0], "parameters": {}, "typeVersion": 1}, {"id": "1b226699-d463-42c9-aab0-e328afdb73b9", "name": "Check if Primary Opportunity Contains Value", "type": "n8n-nodes-base.if", "position": [900, -60], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "e5aed92c-9a3e-4e05-8ce2-9a707abc3115", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.Gong__Primary_Opportunity__c }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "1ebe8eba-5a86-4d17-a629-aa8d2e932693", "name": "Check if Opportunity Stage is Meeting Booked or Discovery", "type": "n8n-nodes-base.if", "position": [660, 0], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "or", "conditions": [{"id": "8c39be67-f158-4d26-a1e9-cfdba686e272", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.Gong__Opp_Stage_Time_Of_Call__c }}", "rightValue": "Discovery"}, {"id": "4cacf9be-3d86-49d6-b7f6-672a57025f0e", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.Gong__Opp_Stage_Time_Of_Call__c }}", "rightValue": "Meeting Booked"}]}}, "typeVersion": 2.2}, {"id": "ee00437a-8586-449c-ab4f-04b91d5f247b", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-440, -360], "parameters": {"width": 340, "height": 820, "content": "![Callforge](https://uploads.n8n.io/templates/callforgeshadow.png)\n## CallForge\nCallForge allows you to extract important information for different departments from your Sales Gong Calls. \n\n### Salesforce Trigger\nThis workflow triggers the AI agent to run, processing calls every hour. It uses the Gong/Salesforce integration to look for new conversation objects in Salesforce which indicate that a new recording has synced to Salesforce. This allows us to filter calls based on internal milestones and metrics ensuring only calls that meet a certain criteria are processed. "}, "typeVersion": 1}, {"id": "2906d433-070d-4240-ba2f-a1669ce5ccc1", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-80, -360], "parameters": {"color": 7, "width": 1940, "height": 820, "content": "## Get Gong Transcript and Call Details\nThe transcript is to pass into the AI prompt, but needs to be transformed first. The Call details provide the Prompt with metadata."}, "typeVersion": 1}, {"id": "96cb8746-3605-4723-b8b5-33bbe8841eaa", "name": "Format call into correct JSON Object", "type": "n8n-nodes-base.set", "position": [1360, -140], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "881fab8b-2f6e-474e-a913-c4bde2b6bd2e", "name": "id", "type": "string", "value": "={{ $json.metaData.id }}"}, {"id": "29aad399-1bb7-49e3-8fc9-cf8a6353536a", "name": "url", "type": "string", "value": "={{ $json.metaData.url }}"}, {"id": "709d029e-6843-42e1-94cc-d01857918617", "name": "title", "type": "string", "value": "={{ $json.metaData.title }}"}, {"id": "39de0391-207b-46ec-9230-cf83667c42b8", "name": "scheduled", "type": "string", "value": "={{ $json.metaData.scheduled }}"}, {"id": "05e3a4a5-12a4-4e14-a8bf-4231e4b2c5b1", "name": "started", "type": "string", "value": "={{ $json.metaData.started }}"}, {"id": "19de15be-56e5-4935-807c-9530cb1da5a8", "name": "duration", "type": "number", "value": "={{ $json.metaData.duration }}"}, {"id": "5a15284b-7c7f-4174-ae6a-82a0dade0542", "name": "primaryUserId", "type": "string", "value": "={{ $json.metaData.primaryUserId }}"}, {"id": "aa58e20b-ddaa-4ed1-a0e2-06125103216f", "name": "direction", "type": "string", "value": "={{ $json.metaData.direction }}"}, {"id": "0f877bb4-a75f-4691-92b0-8b29b939a5b4", "name": "system", "type": "string", "value": "={{ $json.metaData.system }}"}, {"id": "05b3cb81-244d-4f42-a681-13aca1c1df0d", "name": "scope", "type": "string", "value": "={{ $json.metaData.scope }}"}, {"id": "2f9b87d1-e0bd-4170-88da-6966c00c7a2b", "name": "media", "type": "string", "value": "={{ $json.metaData.media }}"}, {"id": "86282040-ceea-4a88-ae47-d5e3fa7cb1a7", "name": "language", "type": "string", "value": "={{ $json.metaData.language }}"}, {"id": "6d8e4e35-5b84-4a1b-a2c1-605ea5e08e66", "name": "workspaceId", "type": "string", "value": "={{ $json.metaData.workspaceId }}"}, {"id": "85f50bb3-306e-4fb3-921b-ff0f61acecbd", "name": "sdrDisposition", "type": "string", "value": "={{ $json.metaData.sdrDisposition }}"}, {"id": "a779d6e8-0d07-4159-8b56-b3c2e49d1c19", "name": "clientUniqueId", "type": "string", "value": "={{ $json.metaData.clientUniqueId }}"}, {"id": "14718f26-69e1-4e4b-90b5-dd059af6459e", "name": "customData", "type": "string", "value": "={{ $json.metaData.customData }}"}, {"id": "4741d29d-0ad6-471d-8432-e7158daeb224", "name": "purpose", "type": "string", "value": "={{ $json.metaData.purpose }}"}, {"id": "7e390036-376e-430d-bd28-43d52ae8794b", "name": "meetingUrl", "type": "string", "value": "={{ $json.metaData.meetingUrl }}"}, {"id": "1ea1f639-8327-4e76-bb3b-f08182fdb87a", "name": "isPrivate", "type": "boolean", "value": "={{ $json.metaData.isPrivate }}"}, {"id": "855ceef1-6bae-44ea-b2af-cc4aa38d6a37", "name": "calendarEventId", "type": "string", "value": "={{ $json.metaData.calendarEventId }}"}, {"id": "f7c11074-70bb-46de-8e7b-2c6d095033c4", "name": "sfOpp", "type": "string", "value": "={{ $('Get all custom Salesforce Gong Objects').item.json.Gong__Primary_Opportunity__c }}"}]}}, "typeVersion": 3.4}, {"id": "5b5eb2c1-7f80-4211-b835-5188376c6df2", "name": "Pass to Gong Call Preprocessor", "type": "n8n-nodes-base.executeWorkflow", "position": [1580, -140], "parameters": {"options": {}, "workflowId": {"__rl": true, "mode": "list", "value": "6mL5jWOJfuzkpjzx", "cachedResultName": "Gong Call Preprocessor Demo"}}, "typeVersion": 1.1}, {"id": "025d3ed7-2bd8-4a88-8834-034036c533c6", "name": "Get <PERSON> Call", "type": "n8n-nodes-base.gong", "position": [1140, -140], "parameters": {"call": {"__rl": true, "mode": "id", "value": "={{ $json.Gong__Call_ID__c }}"}, "options": {}, "operation": "get", "requestOptions": {}}, "credentials": {"gongApi": {"id": "EchfvOC4rjw8MUkr", "name": "<PERSON>"}}, "typeVersion": 1}, {"id": "a4f63c5c-a23e-400f-9fa4-40c61756c321", "name": "Sort by date", "type": "n8n-nodes-base.sort", "position": [440, 0], "parameters": {"options": {}, "sortFieldsUi": {"sortField": [{"order": "descending", "fieldName": "CreatedDate"}]}}, "typeVersion": 1}, {"id": "aa24b82b-3d65-4d1e-be04-7e7d5e439587", "name": "Get all custom Salesforce Gong Objects", "type": "n8n-nodes-base.salesforce", "position": [220, 0], "parameters": {"options": {"fields": ["CreatedDate", "LastActivityDate", "Name", "Gong__Call_ID__c", "Gong__Talk_Time_Us__c", "Gong__Talk_Time_Them__c", "Gong__Title__c", "Gong__View_call__c", "Gong__Primary_Opportunity__c", "Gong__Opp_Stage_Time_Of_Call__c"], "conditionsUi": {"conditionValues": [{"field": "CreatedDate", "value": "={{ $now.minus(4, 'hours') }}", "operation": ">="}]}}, "resource": "customObject", "operation": "getAll", "customObject": "Gong__Gong_Call__c"}, "credentials": {"salesforceOAuth2Api": {"id": "Ykybxuyh0jK0o3qH", "name": "Angel SF Creds v3"}}, "typeVersion": 1}, {"id": "c46f7b03-8ce0-468d-ac84-fae9ae5b2466", "name": "Run Hourly", "type": "n8n-nodes-base.scheduleTrigger", "position": [0, -160], "parameters": {"rule": {"interval": [{"field": "hours"}]}}, "typeVersion": 1.2}], "pinData": {}, "connections": {"Run Hourly": {"main": [[{"node": "Get all custom Salesforce Gong Objects", "type": "main", "index": 0}]]}, "Sort by date": {"main": [[{"node": "Check if Opportunity Stage is Meeting Booked or Discovery", "type": "main", "index": 0}]]}, "Get Gong Call": {"main": [[{"node": "Format call into correct JSON Object", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Get all custom Salesforce Gong Objects", "type": "main", "index": 0}]]}, "Format call into correct JSON Object": {"main": [[{"node": "Pass to Gong Call Preprocessor", "type": "main", "index": 0}]]}, "Get all custom Salesforce Gong Objects": {"main": [[{"node": "Sort by date", "type": "main", "index": 0}]]}, "Check if Primary Opportunity Contains Value": {"main": [[{"node": "Get <PERSON> Call", "type": "main", "index": 0}]]}, "Check if Opportunity Stage is Meeting Booked or Discovery": {"main": [[{"node": "Check if Primary Opportunity Contains Value", "type": "main", "index": 0}]]}}}