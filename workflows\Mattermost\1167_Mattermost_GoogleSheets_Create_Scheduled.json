{"nodes": [{"name": "Send message", "type": "n8n-nodes-base.mattermost", "position": [910, 260], "parameters": {"message": "=New information was added to your Google Sheet.\nID: {{$json[\"id\"]}}\nName: {{$json[\"name\"]}}\nEmail: {{$json[\"email\"]}}", "attachments": [], "otherOptions": {}}, "credentials": {"mattermostApi": "Mattermost Credentials"}, "typeVersion": 1}, {"name": "Check if new data", "type": "n8n-nodes-base.function", "position": [710, 260], "parameters": {"functionCode": "const new_items = [];\n// Get static data stored with the workflow\n\nconst data = this.getWorkflowStaticData(\"node\");\ndata.ids = data.ids || [];\nfor (let i = items.length - 1; i >= 0; i--) {\n\n// Check if data is already present\n  if (data.ids.includes(items[i].json.ID)) {\n    break;\n  } else {\n\n// if new data then add it to an array\n    new_items.push({\n      json: {\n        id: items[i].json.ID,\n        name: items[i].json.Name,\n        email: items[i].json.Email\n      },\n    });\n  }\n}\ndata.ids = items.map((item) => item.json.ID);\n\n// return new items\nreturn new_items;\n"}, "typeVersion": 1}, {"name": "Read data", "type": "n8n-nodes-base.googleSheets", "position": [510, 260], "parameters": {"options": {}, "sheetId": "1PyC-U1lXSCbxVmHuwFbkKDF9e3PW_iUn8T-iAd_MYjQ", "authentication": "oAuth2"}, "credentials": {"googleSheetsOAuth2Api": "google-sheets"}, "typeVersion": 1}, {"name": "Execute every 45 mins", "type": "n8n-nodes-base.interval", "position": [310, 260], "parameters": {"unit": "minutes"}, "typeVersion": 1}], "connections": {"Read data": {"main": [[{"node": "Check if new data", "type": "main", "index": 0}]]}, "Check if new data": {"main": [[{"node": "Send message", "type": "main", "index": 0}]]}, "Execute every 45 mins": {"main": [[{"node": "Read data", "type": "main", "index": 0}]]}}}