{"nodes": [{"name": "Airtable", "type": "n8n-nodes-base.airtable", "position": [440, 320], "parameters": {"operation": "list", "additionalOptions": {}}, "credentials": {"airtableApi": "Airtable Credentials n8n"}, "typeVersion": 1}, {"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.lemlist", "position": [640, 320], "parameters": {"email": "={{$json[\"fields\"][\"Email\"]}}", "resource": "lead", "campaignId": "cam_H5pYEryq6mRKBiy5v", "additionalFields": {"firstName": "={{$json[\"fields\"][\"Name\"]}}"}}, "credentials": {"lemlistApi": "Lemlist API Credentials"}, "typeVersion": 1}, {"name": "Lemlist1", "type": "n8n-nodes-base.lemlist", "position": [840, 320], "parameters": {"email": "={{$node[\"Airtable\"].json[\"fields\"][\"Email\"]}}", "resource": "lead", "operation": "get"}, "credentials": {"lemlistApi": "Lemlist API Credentials"}, "typeVersion": 1}], "connections": {"Lemlist": {"main": [[{"node": "Lemlist1", "type": "main", "index": 0}]]}, "Airtable": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}}}