{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "Get Local Datetime", "type": "n8n-nodes-base.function", "position": [450, 300], "parameters": {"functionCode": "const moment = require('moment');\n\nlet date = moment().tz($env['GENERIC_TIMEZONE']);\n\nlet year = date.year();\nlet month = date.month(); // zero-indexed!\nlet day = date.date();\nlet hour = date.hours();\nlet minute = date.minutes();\nlet second = date.seconds();\nlet millisecond = date.millisecond();\nlet formatted = date.format('YYYY-MM-DD HH:mm:ss.SSS Z');\n\nreturn [\n  {\n    json: {\n      utc: date,\n      year: year,\n      month: month, // zero-indexed!\n      day: day,\n      hour: hour,\n      minute: minute,\n      second: second,\n      millisecond: millisecond,\n      formatted: formatted\n    }\n  }\n];\n"}, "typeVersion": 1}], "connections": {"On clicking 'execute'": {"main": [[{"node": "Get Local Datetime", "type": "main", "index": 0}]]}}}