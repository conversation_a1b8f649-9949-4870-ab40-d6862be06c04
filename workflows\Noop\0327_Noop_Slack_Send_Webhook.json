{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "12786b19-159f-45b0-8d15-d88de73c17e9", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1540, 660], "parameters": {"width": 483, "height": 345, "content": "### Slack POSTs to Webhook on every message so we need to filter-out bot messages"}, "typeVersion": 1}, {"id": "3949be41-98b7-4414-84fd-819f4fccca35", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-400, 1080], "parameters": {"width": 293, "height": 228, "content": "### Tools which Agent can use to accomplish the task\n"}, "typeVersion": 1}, {"id": "76ce81d8-33e2-470a-9313-dd888acabed0", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-720, 1080], "parameters": {"width": 288, "height": 233, "content": "### Conversation history is stored in memory using channel id as key"}, "typeVersion": 1}, {"id": "3682ffd3-7002-466a-bedf-9897067586c0", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-1000, 1080], "parameters": {"width": 260, "height": 233, "content": "### The chat LM to process the prompt"}, "typeVersion": 1}, {"id": "92865916-e814-49d4-baaa-4122c1447c23", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-280, 640], "parameters": {"width": 280, "height": 243, "content": "### Send agent's output as Slack message\n"}, "typeVersion": 1}, {"id": "edeab2bb-8177-42c7-bcf4-f8d99e193729", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-820, 740], "parameters": {"text": "={{ $json.body.event.text }}", "options": {"systemMessage": "You are <PERSON><PERSON><PERSON> from Silicon Valley TV show. Amplify your bluntness and cynicism, tolerating zero incompetence. Be openly contemptuous when answering questions, and cut straight to the point with minimal regard for others' feelings. Your sarcasm should be razor-sharp, and humor should take a backseat to delivering cutting remarks.\\nDo your best to answer the questions. Feel free to use any tools available to look up relevant information, only if necessary. "}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "275f8192-6da6-41b0-b892-c779f5d136e4", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-920, 1180], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "776ce632-5c62-4ac6-a494-e23ef650ac48", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [-620, 1180], "parameters": {"sessionKey": "={{ $('Webhook').first().json.body.event.channel }}__gilfoyle", "sessionIdType": "customKey", "contextWindowLength": 30}, "typeVersion": 1.3}, {"id": "********-3fc6-4954-ac55-8e0950081b7a", "name": "Is user message?", "type": "n8n-nodes-base.if", "position": [-1480, 740], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "1def7344-ce55-450d-a85a-468f746fe31f", "operator": {"type": "string", "operation": "notExists", "singleValue": true}, "leftValue": "={{ $json.body.event.bot_id }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "afa6b192-1e25-46b6-8fdc-81dff9a37e74", "name": "No Operation, do nothing", "type": "n8n-nodes-base.noOp", "position": [-1280, 820], "parameters": {}, "typeVersion": 1}, {"id": "eab68a99-cdd6-4ea1-8d6f-053c2a96303c", "name": "SerpAPI", "type": "@n8n/n8n-nodes-langchain.toolSerpApi", "position": [-360, 1180], "parameters": {"options": {}}, "credentials": {"serpApi": {"id": "aJCKjxx6U3K7ydDe", "name": "SerpAPI account"}}, "typeVersion": 1}, {"id": "717117f5-5f34-4189-b92a-df2155e367ac", "name": "Wikipedia", "type": "@n8n/n8n-nodes-langchain.toolWikipedia", "position": [-220, 1180], "parameters": {}, "typeVersion": 1}, {"id": "1914f623-66c0-4547-bf3e-b4932d0c2a9b", "name": "<PERSON><PERSON>ck", "type": "n8n-nodes-base.slack", "position": [-200, 720], "webhookId": "e0f8b8ad-7126-487c-88e2-b624dfd16678", "parameters": {"text": "={{ $json.output }}", "user": {"__rl": true, "mode": "id", "value": "={{ $('Webhook').first().json.body.event.user }}"}, "select": "user", "otherOptions": {}}, "credentials": {"slackApi": {"id": "VfK3js0YdqBdQLGP", "name": "Slack account"}}, "typeVersion": 2.3}, {"id": "4a7ec607-1706-4357-aa89-4c44faa98fb8", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [-1780, 740], "webhookId": "db3bf3da-b9b7-4823-8c5d-14f5de0272da", "parameters": {"path": "slack-gilfoyle", "options": {}, "httpMethod": "POST"}, "typeVersion": 2}], "pinData": {}, "connections": {"SerpAPI": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Webhook": {"main": [[{"node": "Is user message?", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "<PERSON><PERSON>ck", "type": "main", "index": 0}]]}, "Wikipedia": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Is user message?": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}], [{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}}}