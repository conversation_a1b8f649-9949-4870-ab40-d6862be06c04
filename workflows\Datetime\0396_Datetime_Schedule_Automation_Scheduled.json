{"meta": {"instanceId": "257476b1ef58bf3cb6a46e65fac7ee34a53a5e1a8492d5c6e4da5f87c9b82833", "templateId": "2075"}, "nodes": [{"id": "e3df7c90-fd1e-4e56-b4b8-ee2095720077", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [380, 240], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.1}, {"id": "fd37f3cc-b42c-43db-ba4c-8f760d620050", "name": "PURGE DAYS", "type": "n8n-nodes-base.dateTime", "position": [920, 460], "parameters": {"options": {}, "duration": 30, "magnitude": "={{ $now }}", "operation": "subtractFromDate"}, "typeVersion": 2}, {"id": "88d38a16-3dad-466f-adab-5c5ac846a65e", "name": "DELETE OLD BACKUPS", "type": "n8n-nodes-base.dropbox", "position": [1520, 460], "parameters": {"path": "={{ $json.pathDisplay }}", "operation": "delete", "authentication": "oAuth2"}, "credentials": {"dropboxOAuth2Api": {"id": "28", "name": "Dropbox account"}}, "typeVersion": 1}, {"id": "ff2b37de-8bc8-446a-8369-9bc52a54addd", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [820, -20], "parameters": {"width": 932.*************, "height": 223.**************, "content": "MOVE CURRENT BACKUPS TO OLD FOLDER"}, "typeVersion": 1}, {"id": "732eeb83-f552-4c4a-b0dc-e7e25e7a74cb", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [820, 220], "parameters": {"width": 931.*************, "height": 185.**************, "content": "BACKUP ALL CURRENT WORKFLOWS"}, "typeVersion": 1}, {"id": "fb8e941b-343a-47c0-9806-10f13a0e1c2d", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [817.************, 420], "parameters": {"width": 932.*************, "height": 203.**************, "content": "PURGE BACKUPS OLDER THEN 30 DAYS\n"}, "typeVersion": 1}, {"id": "cbf0c9a8-f188-499f-ba9b-68ea6bfdb38b", "name": "GET WORKFLOWS", "type": "n8n-nodes-base.n8n", "position": [1100, 260], "parameters": {"filters": {}}, "credentials": {"n8nApi": {"id": "9zn8iY4B9oVtPrcc", "name": "n8n account"}}, "typeVersion": 1}, {"id": "43436e4f-83e8-422c-8726-6257976dd9ab", "name": "MAKE JSON FILES", "type": "n8n-nodes-base.moveBinaryData", "position": [1300, 260], "parameters": {"mode": "jsonToBinary", "options": {"fileName": "={{ $json.name }}"}}, "notesInFlow": true, "typeVersion": 1}, {"id": "4a3df15e-3679-415a-bcfc-51b19961b08b", "name": "UPLOAD WORKFLOWS", "type": "n8n-nodes-base.dropbox", "position": [1520, 260], "parameters": {"path": "={{ $('DESTINATION FOLDER').last().json.folder }}{{ $('GET WORKFLOWS').item.json.name }}.json", "binaryData": true, "authentication": "oAuth2"}, "credentials": {"dropboxOAuth2Api": {"id": "28", "name": "Dropbox account"}}, "notesInFlow": true, "typeVersion": 1}, {"id": "1350580e-a6b8-4d18-b2f3-322f3dbefd0b", "name": "DESTINATION FOLDER", "type": "n8n-nodes-base.set", "position": [580, 240], "parameters": {"fields": {"values": [{"name": "folder", "stringValue": "/n8n_backups/"}]}, "include": "none", "options": {}}, "notesInFlow": true, "typeVersion": 3.2}, {"id": "920c837e-f328-47bc-ac01-da4584640e01", "name": "WAIT FOR MOVE TO FINISH", "type": "n8n-nodes-base.merge", "position": [900, 260], "parameters": {"mode": "chooseBranch", "output": "input2"}, "typeVersion": 2.1}, {"id": "8798f472-5a7f-442b-880e-3bffe3597d0b", "name": "GET CURRENT BACKUPS", "type": "n8n-nodes-base.dropbox", "onError": "continueRegularOutput", "position": [1100, 40], "parameters": {"path": "={{ $('DESTINATION FOLDER').last().json.folder }}", "limit": 250, "filters": {}, "resource": "folder", "operation": "list", "authentication": "oAuth2"}, "credentials": {"dropboxOAuth2Api": {"id": "28", "name": "Dropbox account"}}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "b524ac5f-08bf-4c87-9c53-8e9150068690", "name": "IGNORE FOLDERS", "type": "n8n-nodes-base.filter", "position": [1300, 40], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a13e9fd6-ef31-4e23-bde6-955ffab5849b", "operator": {"type": "string", "operation": "notEquals"}, "leftValue": "={{ $json.type }}", "rightValue": "folder"}]}}, "typeVersion": 2, "alwaysOutputData": true}, {"id": "7ca4c3d3-93dc-4da0-a4d0-c9282d0e7689", "name": "MOVE INTO OLD FOLDER", "type": "n8n-nodes-base.dropbox", "onError": "continueRegularOutput", "position": [1520, 40], "parameters": {"path": "={{ $json.pathDisplay }}", "toPath": "={{ $('DESTINATION FOLDER').last().json.folder }}old/{{ $json.name }}_{{ $('GET CURRENT DATE').last().json.formattedDate }}.json", "operation": "move", "authentication": "oAuth2"}, "credentials": {"dropboxOAuth2Api": {"id": "28", "name": "Dropbox account"}}, "executeOnce": false, "notesInFlow": true, "retryOnFail": false, "typeVersion": 1, "alwaysOutputData": true}, {"id": "********-821b-43e1-8aa0-6478955c5f3a", "name": "LIST OLD BACKUPS", "type": "n8n-nodes-base.dropbox", "onError": "continueRegularOutput", "position": [1100, 460], "parameters": {"path": "={{ $('DESTINATION FOLDER').last().json.folder }}old", "limit": 500, "filters": {}, "resource": "folder", "operation": "list", "authentication": "oAuth2"}, "credentials": {"dropboxOAuth2Api": {"id": "28", "name": "Dropbox account"}}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "ffab6a02-a9f9-4a91-b4f1-dbc157d079e7", "name": "CHECK DATES", "type": "n8n-nodes-base.if", "position": [1300, 460], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "e0aa83a7-a65b-4008-9010-bf4f14c0c398", "operator": {"type": "dateTime", "operation": "before"}, "leftValue": "={{ $json.lastModifiedServer }}", "rightValue": "={{ $('PURGE DAYS').item.json.newDate }}"}]}}, "typeVersion": 2}, {"id": "6bb40592-b599-4511-9e29-fdb1d374053f", "name": "GET CURRENT DATE", "type": "n8n-nodes-base.dateTime", "position": [900, 40], "parameters": {"date": "={{ $now }}", "format": "=yyyy-MM-dd_HHmm", "options": {}, "operation": "formatDate"}, "typeVersion": 2}], "pinData": {}, "connections": {"PURGE DAYS": {"main": [[{"node": "LIST OLD BACKUPS", "type": "main", "index": 0}]]}, "CHECK DATES": {"main": [[{"node": "DELETE OLD BACKUPS", "type": "main", "index": 0}]]}, "GET WORKFLOWS": {"main": [[{"node": "MAKE JSON FILES", "type": "main", "index": 0}]]}, "IGNORE FOLDERS": {"main": [[{"node": "MOVE INTO OLD FOLDER", "type": "main", "index": 0}]]}, "MAKE JSON FILES": {"main": [[{"node": "UPLOAD WORKFLOWS", "type": "main", "index": 0}]]}, "GET CURRENT DATE": {"main": [[{"node": "GET CURRENT BACKUPS", "type": "main", "index": 0}]]}, "LIST OLD BACKUPS": {"main": [[{"node": "CHECK DATES", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "DESTINATION FOLDER", "type": "main", "index": 0}]]}, "DESTINATION FOLDER": {"main": [[{"node": "GET CURRENT DATE", "type": "main", "index": 0}, {"node": "WAIT FOR MOVE TO FINISH", "type": "main", "index": 1}, {"node": "PURGE DAYS", "type": "main", "index": 0}]]}, "GET CURRENT BACKUPS": {"main": [[{"node": "IGNORE FOLDERS", "type": "main", "index": 0}]]}, "MOVE INTO OLD FOLDER": {"main": [[{"node": "WAIT FOR MOVE TO FINISH", "type": "main", "index": 0}]]}, "WAIT FOR MOVE TO FINISH": {"main": [[{"node": "GET WORKFLOWS", "type": "main", "index": 0}]]}}}