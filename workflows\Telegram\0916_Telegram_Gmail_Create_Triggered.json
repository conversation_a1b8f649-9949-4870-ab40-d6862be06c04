{"meta": {"instanceId": "45e293393b5dd8437fb351e5b1ef5511ef67e6e0826a1c10b9b68be850b67593", "templateCredsSetupCompleted": true}, "nodes": [{"id": "618c19de-7259-46f7-a02f-d8a4fc140bf3", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [700, 380], "parameters": {"jsonSchemaExample": "{\n\t\"response\": \"N\"\n}"}, "typeVersion": 1.2}, {"id": "7dae5a0e-353b-4a7b-a773-4bcc4ce580ed", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [540, 380], "parameters": {"options": {"baseURL": "https://api.openai.com/v1"}}, "credentials": {"openAiApi": {"id": "htEWFtfoajtuKpAT", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "bab7f1c9-25a8-4c64-b963-ea684afc2380", "name": "Text Email", "type": "n8n-nodes-base.telegram", "position": [1480, 180], "webhookId": "da6bb30b-cd00-47ee-8383-d39dcba33ca1", "parameters": {"text": "=Email ID: {{ $('New Email Received').item.json.id }}\nThread ID: {{ $('New Email Received').item.json.threadId }}\nFrom: {{ $('New Email Received').item.json.from.value[0].name }}\nEmail: {{ $('New Email Received').item.json.from.value[0].address }}\nSubject: {{ $('New Email Received').item.json.subject }}\n\n{{ $('New Email Received').item.json.text.replace(/_/g, '\\\\_')\n        .replace(/\\*/g, '\\\\*')\n        .replace(/\\[/g, '\\\\[')\n        .replace(/\\]/g, '\\\\]')\n        .replace(/\\(/g, '\\\\(')\n        .replace(/\\)/g, '\\\\)')\n        .replace(/~/g, '\\\\~')\n        .replace(/`/g, '\\\\`')\n        .replace(/>/g, '\\\\>')\n        .replace(/#/g, '\\\\#')\n        .replace(/\\+/g, '\\\\+')\n        .replace(/-/g, '\\\\-')\n        .replace(/=/g, '\\\\=')\n        .replace(/\\|/g, '\\\\|')\n        .replace(/\\{/g, '\\\\{')\n        .replace(/\\}/g, '\\\\}')\n        .replace(/\\./g, '\\\\.')\n        .replace(/!/g, '\\\\!')\n        .replace(/\\\\/g, '\\\\\\\\').substring(0, 100) + '...'; }}", "chatId": "={{ $json.chat_id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "iwigkJVzQ94wd6zp", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "0d2490f2-96be-46f2-aa1f-fd63e49c81f4", "name": "OpenAI", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [800, 820], "parameters": {"options": {}, "resource": "audio", "operation": "transcribe"}, "credentials": {"openAiApi": {"id": "htEWFtfoajtuKpAT", "name": "OpenAi account"}}, "typeVersion": 1.5}, {"id": "1911c739-07a4-42d4-aeb9-d90bb2cb2828", "name": "New Email Received", "type": "n8n-nodes-base.gmailTrigger", "position": [-100, 220], "parameters": {"simple": false, "filters": {}, "options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}}, "credentials": {"gmailOAuth2": {"id": "aXTuNMJaYuKFOKTa", "name": "Gmail account"}}, "typeVersion": 1.1}, {"id": "e37d6747-a63f-4aee-bd3d-30c02b6fdc15", "name": "In the Inbox?", "type": "n8n-nodes-base.if", "position": [120, 220], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "3f7094d8-2756-493d-8721-be7d4c83297b", "operator": {"type": "array", "operation": "contains", "rightType": "any"}, "leftValue": "={{ $json.labelIds }}", "rightValue": "INBOX"}]}}, "typeVersion": 2.2}, {"id": "c6eaa6bf-aa92-4dc2-93b9-9695b79c3047", "name": "Needs a response?", "type": "n8n-nodes-base.if", "position": [900, 200], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "8aa9d41a-a218-456c-8b46-70b2e4a1af03", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.output.response }}", "rightValue": "Y"}]}}, "typeVersion": 2.2}, {"id": "44369d4b-6271-4f01-af9b-3b022ab50fb0", "name": "Telegram Bot Message Received", "type": "n8n-nodes-base.telegramTrigger", "position": [-100, 840], "webhookId": "5dfd3832-9606-4b68-904c-0c3e9ef3d7a0", "parameters": {"updates": ["message"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "iwigkJVzQ94wd6zp", "name": "Telegram account"}}, "typeVersion": 1.1}, {"id": "f487f113-da38-41ab-8d1a-c6296fedb91e", "name": "It needs to be an audio message + a reply!", "type": "n8n-nodes-base.telegram", "position": [320, 940], "webhookId": "1d9ee2f9-fbdf-4929-b149-c537ddcde290", "parameters": {"text": "=Sorry, I didn't catch that! \n\nTo send your email for you, I need you to respond with a voice note in reply to one of my other messages.", "chatId": "={{ $json.message.chat.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "iwigkJVzQ94wd6zp", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "215fd988-7e02-4bae-9b57-284e59a7a467", "name": "Get Audio File", "type": "n8n-nodes-base.telegram", "position": [620, 820], "webhookId": "2a804883-546e-410d-bfef-ff91f9ce0b4a", "parameters": {"fileId": "={{ $json.message.voice.file_id }}", "resource": "file"}, "credentials": {"telegramApi": {"id": "iwigkJVzQ94wd6zp", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "a8b83c7a-442a-445a-a55b-2ad7fdd1674b", "name": "Create Email Draft", "type": "n8n-nodes-base.gmail", "position": [1400, 820], "webhookId": "66db4f27-f871-4600-b4a9-fb8bbbd0c8c8", "parameters": {"message": "={{ $json.text }}", "options": {"sendTo": "={{$('Telegram Bo<PERSON> Message Received').item.json.message.reply_to_message.text.match(/Email:\\s(.+?@.+?\\.\\w+)/i)[1]}}", "threadId": "={{$('Telegram Bo<PERSON> Message Received').item.json.message.reply_to_message.text.match(/Thread ID:\\s([a-f0-9]+)/i)[1]}}"}, "subject": "=RE:  {{ $('Telegram Bot Message Received').item.json.message.reply_to_message.text.match(/Subject:\\s(.+)/i)[1] }}", "resource": "draft"}, "credentials": {"gmailOAuth2": {"id": "aXTuNMJaYuKFOKTa", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "cf942bba-201b-4135-bac9-a5cdbd516749", "name": "Direct to Draft", "type": "n8n-nodes-base.telegram", "position": [1560, 820], "webhookId": "a49b47f9-994a-485d-86e4-1222bc192565", "parameters": {"text": "=Draft Created:\n\n{{ $('Write Polished Reply').item.json.output }}\n\n[View here](https://mail.google.com/mail/#all/{{ $json.message.threadId }})", "chatId": "={{ $('Telegram <PERSON> Message Received').item.json.message.reply_to_message.chat.id }}", "additionalFields": {"appendAttribution": false, "reply_to_message_id": "={{ $('Telegram Bot Message Received').item.json.message.message_id }}"}}, "credentials": {"telegramApi": {"id": "iwigkJVzQ94wd6zp", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "b21f66c4-544c-4401-96b6-b7e0239702e4", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-160, -20], "parameters": {"color": 7, "width": 560, "height": 580, "content": "## 1. <PERSON> <PERSON><PERSON> Received\n\nOur workflow is triggered when a new email comes in. \n\nWe use an IF node here to only run the automation on incoming emails to the INBOX - not in the SENT folder."}, "typeVersion": 1}, {"id": "595b7700-97b9-400a-a96b-516452c3db86", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [460, -20], "parameters": {"color": 7, "width": 740, "height": 580, "content": "## 2. Check If Em<PERSON> Needs a Response\n\nWe use ChatGPT to check if the email needs a reply. Anything sent with an \"unsubscribe\" button or \"manage preferences\" is ignored. Anything that comes from a company (e.g. sent from \"<EMAIL>\"), or has the format of a newsletter doesn't need a response.\n\nWe use an output parser so that we can use an IF node on the output."}, "typeVersion": 1}, {"id": "42551c14-0b70-42b7-a7ca-0bab580e050a", "name": "Does Email Need a Response?", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [520, 200], "parameters": {"text": "=Do you think the following email requires me to create a response or not? Your answer should be Y if yes, or N if not. Format your answer as a JSON as either { response: Y } or { response: N } Do not add anything else to your answer at all.\n\nCriteria for emails that require a reply:\n- Direct questions or requests for information, action, or confirmation.\n- Messages seeking clarification or feedback.\n- Invitations to meetings or events that need a confirmation or rejection.\n- Emails indicating follow-up is expected or explicitly asking for a reply.\n- Client/customer queries or feedback that require acknowledgment.\n- Personal emails from somebody who might be my friend\n\n<PERSON>rite<PERSON> for emails that do not require a reply:\n- The email address contains \"no-reply\" or \"noreply\"!\n- Informational or update emails with no explicit call for action or response.\n- Automated notifications (e.g., system alerts, newsletters, etc.).\n- CC/BCC emails where no direct response is expected.\nReplies that only acknowledge receipt (e.g., \"Thank you,\" \"Noted\").\n\nExamples:\n\nInput:\nFrom - \"Nutricals Men's Wellness\" <<EMAIL>>\nSubject - Copy of British Men Swear By This\nBody: Are You Ready to Try? 🚀\\n ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏ ͏­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­­ \\nFree Next Day Delivery Available In The UK\\n\\nBanner with 15% text\\n[https://cdn.shopify.com/s/files/1/0674/3552/8508/files/15OFF_Banner.jpg?v=1728477865]\\nhttps://nutricalsupps.co.uk/_t/c/********-17FCCB1E01B626BC-1AA5C384?l=AADVDennHNHTkxTcdbVb1tb0k0%2FTC%2Fc8r1oThucVEB90pLLtSPl7AIQ1Pb9xXOddTdS0LwJxcVWIvdCayYts30R9tSMtSJ%2BPvLGsZZseeSGN2rHePGVqDYgtLuJsY2HI69JX6WbRq1iYUlXSq%2BKNxtXanpxs7nnIJ3ZdkE13y0A15nlmHP9acPNXhMWS%2Bd9u6XHdQbNswPaahUU63LHAoPKnTC0%2BtsAtcEkCjy66DsXK6OI%2B5MIqszqgzLgeIZYZtJh6Y4WGQMYmTICOGiL3tMSKvfgo3H8UTK9vRVt2%2Bcb86vq9sMXwQuPQYYvuX7jlv0C5IHUH%2BTOSY80eeAAbFD0%2FqFQjHyHarU6SLBXX5UbqPRcTXVPYbNQVXuSQ02WHvKV3689adUNADNX6bg%3D%3D&c=AAB%2FvMTPfjPPwDmVhyk7kEi4pC%2FYi72OVsQsQVTXGWGtSevCqIphtQsYobAeSojbmQlyXUwnlrcaJnu4Dnbct%2BKoO94xvzo6cayXi3YxC90%2FoNS%2BjkilKKRWFCvt4li9bhq5f6GrnmCKm9EQq%2B3mZq%2FHl8NJIMVmoSdIlCLXcjlI2GUzp3JGBEJH2H1MFq67GlXN9iA2ZpwkPo%2BbqSD2HsGfmPQYaudt4kwI6lB5p9%2F08RLniPfvqPmWgkaIkWVmDfbOTTKGl0g%2FNaxSr6oisLnymr%2Bw37cjcBuyRjUhaspoj2weQ9XZbTzzpfkhpfJ3U0C0Roen1nozHk9o32hSefxSUVzGGXMtNSKzSmNVeolJZL9jggSV5NJDIALxxwF0kB1WlEyLGfwbwvTgfbDMH5Ql04aKTolL4K%2Bez10V9R2quqannt35jRahLJZy5cVMWAzPEwleOePSqwD%2BW9sjQcvyuGX%2F23JVrS2chinfmVdCCXWxpYvso3PbtYcgjZ2oAuUaxqhQRYYDxzfe0GZjzqqRmDeP%2BV78FAJESLRAeRoA23tCmQk7FASAY3FjmfbJXnB6%2B40JMthwQKAURDAbBO1ekx1tDdYd3OXZnFk3fDYQwzICVIo3MZah3e3cjRobzS2SSJlAVA%3D%3D\\nAs Seen on Nutricals\\n[https://cdn.shopify.com/s/files/1/0674/3552/8508/files/as-seen-on-Nutricals-MOBILE.png?v=**********]\\n\\n\\n\\n\\n\\n\\n\\nNutricals® UK\\n\\n128 City Road\\nLondon\\nEC1V 2NX\\nUnited Kingdom\\n\\n+************\\<EMAIL>\\n\\n\\n\\nUnsubscribe\\n[https://nutricalsupps.co.uk/account/unsubscribe?token=vkf5H_6XoktnfEKt8qUMcTJEZip6rqyFKGzuFs7BriPM7YFaHIHssoxhRIHW7iJqfzvyVTShmV7_NBTJ2ufoY41w69Mmx4mQ3uR6XBMx06s%3D&se_activity_id=************&utm_source=shopify_email&utm_medium=email&utm_campaign=Copy+of+British+Men+Swear+By+This_************]\\n\\n© 2023 Nutricals® UK\\n\\n[https://cdn.shopify.com/shopify-email/ivc5fufdfucnxibnsh0qhjn9b2ua.png]\\n\\n[https://nutricalsupps.co.uk/_t/open/********-17FCCB1E01B626BC-1AA5C384?en=AABXSr%2BWjAgDVQvPeN2wSOJLFNs1iBRZX%2FfKuzaUN3%2BpO6e9HC0oI9UQmUc2wVl%2F57dDk19OAdrS8hSegwp59%2B7vlk4odUc4YXTSf%2B5RHsfE2HlcuPcEpb9DiMUs7NDW67v2L9CiwR6%2FEZVFbTcCJhd0Gh5EYv%2BBeEwn6zXVIpzWpzWKhLMvA8YYxzZk%2BDxdPCI7%2F5vBO34YytHAupYfHYaj%2B2%2B7clAPN%2BYQHs9AzFfu0IHdXTWLAbdVcPA%2B1X71sJwwJMOvPaaLS60yJyYGuqha3qehHCQxTfPCeEGoCjTruLjdrFDOPr80jHate0BKUTEXxG%2FTXSdnnMJWQz%2F15NG%2FliPNXM9CTVFa%2Be3XMsMp2ZYpdPzqQ8YSuXNc6jZsRJm2oxTjqUoIT8Cd2DubOf7ZATCR5Aj%2FKUYoCLEydg7U7atW5ghJTJbtYmTLatPcPOqgGpyaZRygBarsNpZ%2B%2FACUGYLoIuSzYuKJGd%2B5I3CQAsY7POqE4FP%2BE6lk1wUSaSCl7GNEegZ0JaJ0e5QsMZQnpANFLNt3dAhYQdu0mPldxD6U0DmszqPsqJAJ80E7Z0jg9pb8BgYHi72QyqXMbjrzww%3D%3D]\n\nAnswer: \n{\n\t\"response\": \"N\"\n}\n\n-----\n\nInput:\nFrom - {{ $json.from.value[0].name }} <{{ $json.from.value[0].address }}>\nSubject - {{ $json.subject }}\nBody: {{ $json.text }}", "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.4}, {"id": "ac18470f-4884-43ca-80b5-c8936fc4d4cd", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1240, -20], "parameters": {"color": 7, "width": 520, "height": 580, "content": "## 3. Send Email to Telegram\n\nWe use a VoicerEmailer bot to send the email over a Telegram message to our account on Telegram."}, "typeVersion": 1}, {"id": "be53cc8e-c5dd-4b82-aa78-c7870bf7de7b", "name": "Is Type Audio Message + Reply?", "type": "n8n-nodes-base.if", "position": [100, 840], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "860f30dc-bfa7-46f5-a45d-b12c13194c41", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.message.reply_to_message }}", "rightValue": ""}, {"id": "9647524d-e0f2-4fff-9287-7e3752488343", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.message.voice }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "280d69b3-4cf5-4515-8ae8-c499b21e4d99", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-160, 600], "parameters": {"color": 7, "width": 680, "height": 580, "content": "## 4. <PERSON><PERSON><PERSON> Reply Received\n\nThis workflow is triggered when the Telegram bot receives a message. \n\nWe check that the message is a reply to a previous email message, and that the reply is an audio message. \n\nIf not, we send a message back telling them what they did wrong."}, "typeVersion": 1}, {"id": "168f9296-8ff4-4598-91ab-81967cf36dd2", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [560, 600], "parameters": {"color": 7, "width": 440, "height": 580, "content": "## 5. Audio Transcription\n\nWe get the audio file from the Telegram message and send it to OpenAI's Whisper API to get a transcription of the message."}, "typeVersion": 1}, {"id": "8e2731c6-c4ca-40f3-8c26-6421b57f95e2", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1120, 1000], "parameters": {"options": {"baseURL": "https://api.openai.com/v1"}}, "credentials": {"openAiApi": {"id": "htEWFtfoajtuKpAT", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "7c350b3d-1cad-4ae9-8330-8bb55fbcea15", "name": "Write Polished Reply", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1100, 820], "parameters": {"text": "=Received Email:\n{{ $('Telegram Bot Message Received').item.json.message.reply_to_message.text }}\n\nVoice Note Response:\n{{ $json.text }}", "messages": {"messageValues": [{"message": "=You are a helpful assistant who translates rough voicemail messages into polished emails.\n\nYou will be given an email which is expecting a reply, as well as a voice message transcription which address the email. You should output a reply.\n\nDon't include the subject line. Only include a rephrasing of the answer given in the voice note. Do not make up an answer to fit any questions in the original email. \n\nUse the same tone, and broadly the same phrasing, as the voice note. Include a sign-off.\n\nDon't include any other padding or explanation in your answer.\n\nExamples:\n\nUSER INPUT:\n\nReceived Email:\nEmail ID: 19272309c9c81678\nThread ID: 19272309c9c81678\nFrom: ulrike roesler (via tibet-core Mailing List)\nEmail: <EMAIL>\nSubject: Pre-term gathering this Friday, 7pm\n\nDear Adam,\n\nJust a brief reminder that the Tibetan & Himalayan Studies pre\\\\-term\ngathering will take place \\*this Friday from 7pm at the Royal Oak \\\\(Woodstock\nRoad\\\\)\\*\\\\. I have reserved a table for \"Tibetan Studies\"\\\\.\n\nWe will start by discussing the timetable, and those attending classes this\ncoming term are therefore asked to arrive at 7pm\\\\. Everyone else is welcome\nto join anytime during the evening\\\\.\n\nI attach a draft timetable, but please note that there may still be some\nsmall adjustments to the class times\\\\. The final version of the timetable\nwill be circulated after our meeting\\\\.\n\nI look forward to seeing you soon,\n\n<PERSON><PERSON><PERSON><PERSON>\n\n\\*\\*\\*\\*\\*\\*\\*\\*\\*\\*\\*\\*\\*\\*\\*\\*\\*\\*\\*\\*\\*\\*\\*\\*\\*\\*\\*\nUlrike Roesler\nProfessor of Tibetan and Himalayan Studies\nOriental Institute\nPusey Lane\nOxford, OX1 2LE\n\\\\+44\\\\-1865\\\\-278236\n\nVoice Note Response:\nHey Ulrike, sorry, I won't be there because I'm currently in San Marcos La Laguna in Guatemala So I can't be there for the Tibetan Studies gathering This coming Friday, I'm sorry\n\n---\n\nASSISTANT RESPONSE:\n\nHi Ulrike,\n\nThanks for letting me know about the pre-term gathering this Friday. \n\nUnfortunately, I won’t be able to attend, as I'm currently in San Marcos La Laguna, Guatemala. \n\nI'm sorry to miss out on the discussion.\n\nThanks,\n\nAdam"}]}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.4}, {"id": "37905767-3cca-4789-b016-2fec4f21e45b", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [1040, 600], "parameters": {"color": 7, "width": 720, "height": 580, "content": "## 5. Create Email Draft\n\nFinally, we get ChatGPT to write up a response, given the original email for context and our voice note reply. \n\nWe create a new draft in Gmail, which shows up in the same email thread. We sent a link to the newly created draft to the user via Telegram."}, "typeVersion": 1}, {"id": "a9942f9d-8896-45cd-b715-1228d8e3295c", "name": "Set Chat ID", "type": "n8n-nodes-base.set", "position": [1300, 180], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d2980bdf-c0c2-47a7-885c-6a1aea58396c", "name": "chat_id", "type": "string", "value": "=6963887105"}]}}, "typeVersion": 3.4}, {"id": "5a60bcd6-da89-4892-9a3f-1b04a2238ab6", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1280, 340], "parameters": {"width": 160, "height": 120, "content": "## Edit here\nAdd in your Chat ID here."}, "typeVersion": 1}], "pinData": {"Telegram Bot Message Received": [{"message": {"chat": {"id": 1981391864, "type": "private", "username": "clairebaker0", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "date": 1728670178, "from": {"id": 1981391864, "is_bot": false, "username": "clairebaker0", "last_name": "<PERSON>", "first_name": "<PERSON>", "is_premium": true, "language_code": "en"}, "voice": {"file_id": "AwACAgQAAxkBAANSZwlp4lXETIQffnMjGYNf_9KBCHEAAnwZAAKYI1FQZmfPK4JXJl82BA", "duration": 84, "file_size": 326514, "mime_type": "audio/ogg", "file_unique_id": "AgADfBkAApgjUVA"}, "message_id": 82, "reply_to_message": {"chat": {"id": 1981391864, "type": "private", "username": "clairebaker0", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "date": 1728632446, "from": {"id": 7199424210, "is_bot": true, "username": "Email12345Bot", "first_name": "EmailBot"}, "text": "Email ID: 1927a85634ae8e72\nThread ID: 1927a85634ae8e72\nFrom: <PERSON>\nEmail: <EMAIL>\nSubject: Re: October Updates ❤️\n\nHello lovelies\n\nThanks for these updates\\\\.\n\nSo interested in your decision to change the elemental ...", "entities": [{"type": "email", "length": 28, "offset": 81}], "message_id": 74}}, "update_id": 408806372}]}, "connections": {"OpenAI": {"main": [[{"node": "Write Polished Reply", "type": "main", "index": 0}]]}, "Set Chat ID": {"main": [[{"node": "Text Email", "type": "main", "index": 0}]]}, "In the Inbox?": {"main": [[{"node": "Does Email Need a Response?", "type": "main", "index": 0}]]}, "Get Audio File": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}, "Needs a response?": {"main": [[{"node": "Set Chat ID", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Does Email Need a Response?", "type": "ai_languageModel", "index": 0}]]}, "Create Email Draft": {"main": [[{"node": "Direct to Draft", "type": "main", "index": 0}]]}, "New Email Received": {"main": [[{"node": "In the Inbox?", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Write Polished Reply", "type": "ai_languageModel", "index": 0}]]}, "Write Polished Reply": {"main": [[{"node": "Create Email Draft", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Does Email Need a Response?", "type": "ai_outputParser", "index": 0}]]}, "Does Email Need a Response?": {"main": [[{"node": "Needs a response?", "type": "main", "index": 0}]]}, "Telegram Bot Message Received": {"main": [[{"node": "Is Type Audio Message + Reply?", "type": "main", "index": 0}]]}, "Is Type Audio Message + Reply?": {"main": [[{"node": "Get Audio File", "type": "main", "index": 0}], [{"node": "It needs to be an audio message + a reply!", "type": "main", "index": 0}]]}}}