{"meta": {"instanceId": "257476b1ef58bf3cb6a46e65fac7ee34a53a5e1a8492d5c6e4da5f87c9b82833"}, "nodes": [{"id": "45ae6e88-3fda-4e95-84db-085a895cc564", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [260, -100], "parameters": {}, "typeVersion": 1}, {"id": "09f71a7c-1219-426d-8563-fa05654cab44", "name": "Calculate ICP PersonScoring", "type": "n8n-nodes-base.airtop", "position": [700, -100], "parameters": {"url": "={{ $json['Linkedin_URL_Person'] }}", "prompt": "Please extract the following information from the LinkedIn profile page:\n\n1. **Full Name**: Extract the full name of the individual.\n2. **Current or Most Recent Job Title**: Identify the job title next to the logo of the current or last employer.\n3a. **Current or Most Recent Employer**: Extract the name of the first company in the employment experience block. \n3b. Linkedin Company URL of the Current or Most Recent Employer: Extract the link of the first company in the employment experience block\n4. **Location**: Extract the location of the individual.\n5. **Number of Connections**: Extract the number of connections the individual has.\n6. **Number of Followers**: Extract the number of followers the individual has.\n7. **About Section Text**: Extract the text from the 'About' section.\n8. **Interest Level in AI**: Determine the person's interest level in AI (e.g., beginner, intermediate, advanced, expert).\n9. **Seniority Level**: Determine the seniority level of the person (e.g., junior, mid-level, senior, executive).\n10. **Technical Depth**: Determine the technical depth of the person (e.g., basic, intermediate, advanced, expert).\n11. **ICP Score**: Calculate the ICP Score based on the following criteria:\n    - AI Interest: beginner-5 pts, intermediate-10 pts, advanced-25 pts, expert-35 pts\n    - Technical Depth: basic-5 pts, intermediate-15 pts, advanced-25 pts, expert-35 pts\n    - Seniority Level: junior-5 pts, mid-level-15 pts, senior-25 pts, executive-30 pts\n    - Sum the points to get the ICP Score.\n\nEnsure that the extracted information is accurate and formatted according to the specified output schema.\n\nFor example, if the LinkedIn profile is of a senior software engineer with a strong interest in AI, return the following output:\n{\n  \"full_name\": \"Jane Doe\",\n  \"current_or_last_employer\": \"Tech Innovations Inc.\",\n  \"current_or_last_title\": \"Senior Software Engineer\",\n  \"location\": \"San Francisco, CA\",\n  \"number_of_connections\": 500,\n  \"number_of_followers\": 300,\n  \"about_section_text\": \"Experienced software engineer with a passion for developing innovative programs that expedite the efficiency and effectiveness of organizational success.\",\n  \"ai_interest_level\": \"advanced\",\n  \"seniority_level\": \"senior\",\n  \"technical_depth\": \"advanced\",\n  \"icp_score\": 85\n}\n", "resource": "extraction", "operation": "query", "sessionMode": "new", "additionalFields": {"outputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"full_name\": {\n      \"type\": \"string\",\n      \"description\": \"The full name of the individual.\"\n    },\n    \"current_or_last_title\": {\n      \"type\": \"string\",\n      \"description\": \"The job title next to the logo of the current or last employer.\"\n    },\n    \"current_or_last_employer\": {\n      \"type\": \"string\",\n      \"description\": \"The name of the first company in the employment experience block.\"\n    },\n    \"linkedin_company_url\": {\n      \"type\": \"string\",\n      \"description\": \"The LinkedIn URL of the first company in the employment experience block.\"\n    },\n    \"location\": {\n      \"type\": \"string\",\n      \"description\": \"The location of the individual.\"\n    },\n    \"number_of_connections\": {\n      \"type\": \"integer\",\n      \"description\": \"The number of connections the individual has.\"\n    },\n    \"number_of_followers\": {\n      \"type\": \"integer\",\n      \"description\": \"The number of followers the individual has.\"\n    },\n    \"about_section_text\": {\n      \"type\": \"string\",\n      \"description\": \"The text from the 'About' section.\"\n    },\n    \"ai_interest_level\": {\n      \"type\": \"string\",\n      \"description\": \"The person's interest level in AI.\"\n    },\n    \"seniority_level\": {\n      \"type\": \"string\",\n      \"description\": \"The seniority level of the person.\"\n    },\n    \"technical_depth\": {\n      \"type\": \"string\",\n      \"description\": \"The technical depth of the person.\"\n    },\n    \"icp_score\": {\n      \"type\": \"integer\",\n      \"description\": \"The ICP Score calculated based on AI interest, technical depth, and seniority level.\"\n    }\n  },\n  \"required\": [\n    \"full_name\",\n    \"current_or_last_title\",\n    \"current_or_last_employer\",\n    \"linkedin_company_url\",\n    \"location\",\n    \"number_of_connections\",\n    \"number_of_followers\",\n    \"about_section_text\",\n    \"ai_interest_level\",\n    \"seniority_level\",\n    \"technical_depth\",\n    \"icp_score\"\n  ],\n  \"additionalProperties\": false,\n  \"$schema\": \"http://json-schema.org/draft-07/schema#\"\n}\n"}}, "typeVersion": 1}, {"id": "28c2c1d4-f43f-46c6-b21d-fbaf5fed4efa", "name": "Format response", "type": "n8n-nodes-base.code", "position": [900, -100], "parameters": {"mode": "runOnceForEachItem", "jsCode": "const row_number = $('Get person').item.json.row_number\nconst Linkedin_URL_Person = $('Get person').item.json.Linkedin_URL_Person\nconst ICP_Score_Person = JSON.parse($input.item.json.data.modelResponse).icp_score\n\nreturn { json: {\n  row_number,\n  Linkedin_URL_Person,\n  ICP_Score_Person\n}};"}, "typeVersion": 2}, {"id": "1646b60c-21f2-4222-bc4c-8660184fa46a", "name": "Update row", "type": "n8n-nodes-base.googleSheets", "position": [1120, -100], "parameters": {"columns": {"value": {}, "schema": [{"id": "Linkedin_URL_Person", "type": "string", "display": true, "required": false, "displayName": "Linkedin_URL_Person", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ICP_Score_Person", "type": "string", "display": true, "required": false, "displayName": "ICP_Score_Person", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": ["row_number"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1WC_awgb-Ohtb0f4o_OJgRcvunTLuS8kFQgk6l8fkR2Q/edit#gid=0", "cachedResultName": "Person"}, "documentId": {"__rl": true, "mode": "list", "value": "1WC_awgb-Ohtb0f4o_OJgRcvunTLuS8kFQgk6l8fkR2Q", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1WC_awgb-Ohtb0f4o_OJgRcvunTLuS8kFQgk6l8fkR2Q/edit?usp=drivesdk", "cachedResultName": "ICP Score for Template"}}, "typeVersion": 4.5}, {"id": "5a151773-1075-4a9f-9637-6241e7137638", "name": "Get person", "type": "n8n-nodes-base.googleSheets", "position": [480, -100], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1WC_awgb-Ohtb0f4o_OJgRcvunTLuS8kFQgk6l8fkR2Q/edit#gid=0", "cachedResultName": "Person"}, "documentId": {"__rl": true, "mode": "list", "value": "1WC_awgb-Ohtb0f4o_OJgRcvunTLuS8kFQgk6l8fkR2Q", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1WC_awgb-Ohtb0f4o_OJgRcvunTLuS8kFQgk6l8fkR2Q/edit?usp=drivesdk", "cachedResultName": "ICP Score for Template"}}, "typeVersion": 4.5}], "pinData": {}, "connections": {"Get person": {"main": [[{"node": "Calculate ICP PersonScoring", "type": "main", "index": 0}]]}, "Format response": {"main": [[{"node": "Update row", "type": "main", "index": 0}]]}, "Calculate ICP PersonScoring": {"main": [[{"node": "Format response", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Get person", "type": "main", "index": 0}]]}}}