{"id": "rYuhIChQyjpGNvuR", "meta": {"instanceId": "ecc960f484e18b0e09045fd93acf0d47f4cfff25cc212ea348a08ac3aae81850"}, "name": "Luma AI - Webhook Response v1 - AK", "tags": [{"id": "6rb8rVhKZj4t0Kne", "name": "Current", "createdAt": "2025-02-04T18:13:17.427Z", "updatedAt": "2025-02-04T18:13:17.427Z"}], "nodes": [{"id": "cb03e151-9931-4917-bf6f-2a1c9e06b896", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [-560, 120], "webhookId": "cea413b3-fa80-454e-b7c9-ec284a795984", "parameters": {"path": "luma-ai-response", "options": {}, "httpMethod": "POST"}, "typeVersion": 2}, {"id": "33a93e0d-3424-480b-9b55-9124d826b233", "name": "Video JSON", "type": "n8n-nodes-base.set", "position": [-360, 120], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "3280111c-573a-4ed0-8a7e-da263558f3d5", "name": "video_json", "type": "string", "value": "={{ $json }}"}, {"id": "63a091f2-5a4d-410c-87ec-2ad8f3db8480", "name": "luma_video", "type": "string", "value": "={{ $json.body.assets.video }}"}, {"id": "4425f709-12c7-4aeb-b957-c419f79eb5fd", "name": "luma_thumb", "type": "string", "value": "={{ $json.body.assets.image }}"}, {"id": "b1eb986c-76af-462f-a685-209bcdc14baa", "name": "gen_id", "type": "string", "value": "={{ $json.id }}"}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "103b3a52-dc99-46b4-9d8e-41fa413b7c7b", "name": "Execution Data", "type": "n8n-nodes-base.executionData", "position": [480, 20], "parameters": {}, "typeVersion": 1}, {"id": "90f163dd-1b59-4a6c-a5ca-00c52cffacdd", "name": "If", "type": "n8n-nodes-base.if", "position": [-160, 120], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "ac05d685-5af4-40cf-a4c6-3b717c36d8c5", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $('Video JSON').first().json.body.assets.video }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "9ace2252-e3e0-4321-92c5-1cfcf1b97ebf", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [0, -100], "parameters": {"color": 3, "width": 220, "content": "## Define your SETTINGS here"}, "typeVersion": 1}, {"id": "116048ff-d444-4808-b533-116614386c0c", "name": "Global SETTINGS", "type": "n8n-nodes-base.set", "position": [60, 20], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "5e5089e8-6b94-4d2a-aa51-2b8f9caca7c3", "name": "airtable_base", "type": "string", "value": "appvk87mtcwRve5p5"}, {"id": "26b5a452-7797-4c84-bd9e-285df13f7089", "name": "airtable_table_generated_videos", "type": "string", "value": "tblOzRFWgcsfttRWK"}, {"id": "0dc3ad30-cb06-47b0-8b03-5bd98ac377bf", "name": "airtable_table_article_writer", "type": "string", "value": "tblVTpv8JG5lZRiF2"}]}}, "typeVersion": 3.4}, {"id": "449983cc-ed22-4544-a3df-1e1f7087c810", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-820, 0], "parameters": {"color": 3, "width": 400, "content": "## Make sure this URL for the Webhook matches that in Part 1 of this series"}, "typeVersion": 1}, {"id": "eee48ffe-dddc-41c9-ae8d-ca75cd8ce31c", "name": "ADD Video and Thumbnail URL", "type": "n8n-nodes-base.airtable", "position": [280, 20], "parameters": {"base": {"__rl": true, "mode": "id", "value": "={{ $json.airtable_base }}"}, "table": {"__rl": true, "mode": "id", "value": "={{ $json.airtable_table_generated_videos }}"}, "columns": {"value": {"Status": "Done", "Thumb URL": "={{ $('If').first().json.body.assets.image }}", "Video URL": "={{ $('If').first().json.body.assets.video }}", "Generation ID": "={{ $('If').first().json.body.id }}"}, "schema": [{"id": "Generation ID", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Generation ID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Status", "type": "options", "display": true, "options": [{"name": "Todo", "value": "Todo"}, {"name": "In progress", "value": "In progress"}, {"name": "Done", "value": "Done"}], "removed": false, "readOnly": false, "required": false, "displayName": "Status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Content Title", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Content Title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Video URL", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Video URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Thumb URL", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Thumb URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Prompt", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Prompt", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Aspect", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Aspect", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Model", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Model", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Resolution", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Resolution", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Length", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Length", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["Generation ID"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update"}, "credentials": {"airtableTokenApi": {"id": "yqBrLbgHXLcwqH0p", "name": "AlexK Airtable Personal Access Token account"}}, "typeVersion": 2.1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "82de5303-0dcf-416e-8823-e2a7eff4c5f8", "connections": {"If": {"main": [[{"node": "Global SETTINGS", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Video JSON", "type": "main", "index": 0}]]}, "Video JSON": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Global SETTINGS": {"main": [[{"node": "ADD Video and Thumbnail URL", "type": "main", "index": 0}]]}, "ADD Video and Thumbnail URL": {"main": [[{"node": "Execution Data", "type": "main", "index": 0}]]}}}