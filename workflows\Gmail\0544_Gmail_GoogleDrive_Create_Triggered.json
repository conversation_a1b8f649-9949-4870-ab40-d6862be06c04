{"meta": {"instanceId": "1a23006df50de49624f69e85993be557d137b6efe723a867a7d68a84e0b32704"}, "nodes": [{"id": "3c7ae816-6ce2-4b6b-893e-75c6b8756555", "name": "Trigger - <PERSON> Email", "type": "n8n-nodes-base.gmailTrigger", "notes": "has:attachment", "position": [680, 300], "parameters": {"simple": false, "filters": {"q": "has:attachment"}, "options": {"downloadAttachments": true}, "pollTimes": {"item": [{"mode": "everyMinute"}]}}, "notesInFlow": true, "typeVersion": 1.1}, {"id": "b87b2211-03d3-4742-98c9-977ae4a8d581", "name": "attach binary data outputs", "type": "n8n-nodes-base.function", "position": [900, 300], "parameters": {"functionCode": "let results = [];\n\nfor (item of items) {\n    for (key of Object.keys(item.binary)) {\n        results.push({\n            json: {\n                fileName: item.binary[key].fileName\n            },\n            binary: {\n                data: item.binary[key],\n            }\n        });\n    }\n}\n\nreturn results;"}, "typeVersion": 1}, {"id": "f8e19c97-0983-4365-bc63-179605050ef2", "name": "upload files to google drive", "type": "n8n-nodes-base.googleDrive", "position": [1140, 300], "parameters": {"name": "={{ $json.fileName.split(\".\")[0] + \"-\" + $('<PERSON>gger - New Email').item.json.from.value[0].address + \".\" + $json.fileName.split(\".\")[1]}}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive", "cachedResultUrl": "https://drive.google.com/drive/my-drive", "cachedResultName": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "root", "cachedResultUrl": "https://drive.google.com/drive", "cachedResultName": "/ (Root folder)"}}, "typeVersion": 3}], "pinData": {}, "connections": {"Trigger - New Email": {"main": [[{"node": "attach binary data outputs", "type": "main", "index": 0}]]}, "attach binary data outputs": {"main": [[{"node": "upload files to google drive", "type": "main", "index": 0}]]}}}