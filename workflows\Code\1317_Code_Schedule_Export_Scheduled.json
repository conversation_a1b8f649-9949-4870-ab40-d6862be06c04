{"id": "As8TxF3PjyXygc0o", "meta": {"instanceId": "a059b3dfdab56aa587cc6a2c8635f6f2700cf0c7064dbfb5981c26f7ad9eab88"}, "name": "🧹 Archive (delete) duplicate items from a Notion database", "tags": [], "nodes": [{"id": "b758ce01-7f5e-4bdc-a4c3-6c00d6bc022a", "name": "Every day", "type": "n8n-nodes-base.scheduleTrigger", "position": [-180, 660], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.2}, {"id": "1ca45ba5-4635-4710-9807-26f22d535059", "name": "Get pages from database", "type": "n8n-nodes-base.notion", "position": [60, 560], "parameters": {"options": {}, "resource": "databasePage", "operation": "getAll", "returnAll": true, "databaseId": {"__rl": true, "mode": "list", "value": ""}}, "typeVersion": 2.2}, {"id": "ef8c8cfa-12fb-4fb9-8552-09f69f1f358d", "name": "Aggregate all items", "type": "n8n-nodes-base.aggregate", "position": [500, 560], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "pages"}, "typeVersion": 1}, {"id": "f1c3c0ad-f904-4d63-a131-0b045a21ce04", "name": "Format items properly", "type": "n8n-nodes-base.set", "position": [280, 560], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "309a1e9b-f3e9-41a0-aadb-aa74bc993fe9", "name": "id", "type": "string", "value": "={{ $json.id }}"}, {"id": "ad6e8fa9-9872-456d-971f-3cef940b7d8a", "name": "property_to_check", "type": "string", "value": "=\"SET YOUR PROPERTY HERE\""}]}}, "typeVersion": 3.4}, {"id": "5d39d3b7-604d-4aca-bf9a-3bb09bddad66", "name": "Filter duplicates", "type": "n8n-nodes-base.code", "position": [720, 560], "parameters": {"jsCode": "const inputData = $input.first().json.pages;\n\nconst seen = new Set();\nconst duplicates = new Map();\n\ninputData.forEach(item => {\n  const propertyValue = item.property_to_check;\n  if (seen.has(propertyValue)) {\n    duplicates.set(propertyValue, item);\n  } else {\n    seen.add(propertyValue);\n  }\n});\n\nconst output = Array.from(duplicates.values()).map(item => ({ json: item }));\n\nreturn output;"}, "typeVersion": 2}, {"id": "55a8f0eb-702b-4056-a28c-96a7ade7c2cd", "name": "Archive pages", "type": "n8n-nodes-base.notion", "position": [920, 560], "parameters": {"pageId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "operation": "archive"}, "typeVersion": 2.2}, {"id": "2c9655ea-401c-410b-a4b1-b001ae6dbe4b", "name": "When a page is added to the database", "type": "n8n-nodes-base.notionTrigger", "position": [-180, 460], "parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "databaseId": {"__rl": true, "mode": "list", "value": ""}}, "typeVersion": 1}, {"id": "672b647c-d009-45c3-b69e-6dfe85992e15", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [0, 0], "parameters": {"width": 860, "height": 460, "content": "## 🧹 Archive (delete) extra duplicate items from Notion database\n### ABOUT THIS WORKFLOW\nThis n8n workflow automatically gets duplicate database pages based on a property and \"archives\" them (equivalent to deleting them), leaving just one copy.\n\n### SETUP\n1. Create a Notion credential.\n2. Add it to the Notion nodes, selecting the appropriate database.\n3. In the \"Set\" node (\"Format items properly\"), specify a reference to the property you want to check for duplicates and assign it to the field \"property_to_check\". I recommend using the n8n property drag-and-drop feature.\n4. Enjoy!\n\n### ABOUT THE TRIGGERS\nThis workflow offers two possible triggers by default:\n- Run every time a page is added to the database.\n- Run every day.\n\n\nYou can enable, disable, or modify these triggers as you like."}, "typeVersion": 1}, {"id": "83881bd3-60e3-40be-a469-0b7acb21d2be", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-240, 400], "parameters": {"color": 5, "width": 220, "height": 420, "content": "## TRIGGERS"}, "typeVersion": 1}, {"id": "cd4b8717-19ae-42d6-ac87-bbdd071dd774", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [0, 480], "parameters": {"color": 6, "width": 860, "height": 340, "content": "## GET DUPLICATE PAGES"}, "typeVersion": 1}, {"id": "087fb844-2241-4ed9-976d-9bdc7ccd8aa5", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [880, 400], "parameters": {"color": 3, "width": 180, "height": 420, "content": "## ARCHIVE (DELETE)"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "fdd2e5ad-4ff5-4432-a5f9-ebbeb1a1a6cb", "connections": {"Every day": {"main": [[{"node": "Get pages from database", "type": "main", "index": 0}]]}, "Filter duplicates": {"main": [[{"node": "Archive pages", "type": "main", "index": 0}]]}, "Aggregate all items": {"main": [[{"node": "Filter duplicates", "type": "main", "index": 0}]]}, "Format items properly": {"main": [[{"node": "Aggregate all items", "type": "main", "index": 0}]]}, "Get pages from database": {"main": [[{"node": "Format items properly", "type": "main", "index": 0}]]}, "When a page is added to the database": {"main": [[{"node": "Get pages from database", "type": "main", "index": 0}]]}}}