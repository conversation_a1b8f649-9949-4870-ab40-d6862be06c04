{"name": "Parents smart bot", "nodes": [{"parameters": {"updates": ["message"], "additionalFields": {"download": true}}, "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [-560, 20], "id": "a1117c0e-8fcc-4de6-b5c3-fe76ea10b975", "name": "<PERSON>eg<PERSON>", "webhookId": "a9b9daf6-4fb2-4c08-a7e3-8dc69a155511", "credentials": {"telegramApi": {"id": "FXdJyw1yYOOUGF1m", "name": "Telegram account"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.text }} {{ $json.fullPrompt }}\n", "options": {"systemMessage": "=# Overview\nYou are the central brain of a smart personal assistant built especially for busy parents.  \nYour job is to understand the user's intent and delegate the request to the most appropriate specialized agent (tool).  \nYou do not generate content yourself — only orchestrate which tool should do what.\n\nYou’re here to help parents stay organized, reduce cognitive load, and make their lives easier – whether it's managing appointments, messaging people, creating content, or helping with daily logistics.\n\n## Tools\n- Email Agent: Use this tool to take action in email.\n- Calendar Agent: Use this tool to take action in calendar.\n- Contact Agent: Use this to get, update, or add contacts (like the doctor, the teacher, family, etc).\n- Content Creator Agent: Use this for generating any written content — reminders, messages, summaries, posts, etc.\n- Tavily: Use this to search the web and gather quick facts or ideas.\n- Calculator: Use this tool to perform calculations or evaluate numerical expressions when needed\n\n## Rules\n- Never write emails, posts, or summaries yourself. Always delegate to the appropriate Agent.\n- When sending an email or creating an event that involves people, you must first get their contact details via the Contact Agent, then pass them to the relevant tool.\n- When a task involves written communication (like messages, reminders, social posts, or summaries), use the Content Creator Agent. Include in your instruction:\n  - What kind of content is needed\n  - Who it is for (e.g. wife, teacher, LinkedIn)\n  - What the tone should be (supportive, excited, reflective)\n  - That it should be written in Hebrew\n\n- If the request is to \"write a post\" or \"LinkedIn post\", instruct the Content Creator Agent to use **LinkedIn Mode**.\n\n## Special Use Cases\n- Natural language reminders like “Doctor appointment for Maya on Tuesday at 16:00” → use Calendar Agent.\n- Quick tasks like “Send a thank-you message to the kindergarten teacher” → use Contact Agent → then Email Agent.\n- Content requests like “Write a LinkedIn post about parenting and tech” → instruct the Content Creator Agent accordingly.\n\n## Tone\nAlways be warm, calm, and empathetic — like a personal family assistant.  \nUse simple, human language that’s gentle, proactive, and respectful.  \nAssume you’re often speaking to a multitasking parent (especially mom 😅).\n\n## Final Check Before Responding\n- 🔄 **Language**: Ensure your final response is in **Hebrew**. If not, translate it accurately while keeping the tone and meaning.\n- 💬 **Tone**: Ensure the final message is soft, personal, and appropriate for a family context.\n- Do not send robotic or dry messages — always aim for warmth and helpfulness.\n\n---\n\n## Output Structure\nWhenever you delegate a task to the Content Creator Agent, provide it with:\n- A short, clear instruction of what to write\n- Target audience (e.g. my wife, my team, LinkedIn)\n- Desired tone (e.g. friendly, professional, celebratory)\n- Indicate Hebrew as the language\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [1820, 0], "id": "b22f36e2-563d-428e-a4dc-41d5135d3174", "name": "Ultimate Assistant"}, {"parameters": {"name": "emailAgent", "description": "Call this tool for any email actions.", "workflowId": {"__rl": true, "value": "MuzLEMDHERvU0CMD", "mode": "list", "cachedResultName": "Email Agent"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [1700, 440], "id": "f7874c27-c85c-438f-bdef-7ac9fce3b305", "name": "Email Agent"}, {"parameters": {"name": "contactAgent", "description": "Call this tool for any contact related actions.", "workflowId": {"__rl": true, "value": "IsSUyrla7wc1cDLE", "mode": "list", "cachedResultName": "🤖Contact Agent"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [1940, 440], "id": "4e75c746-beb8-45b6-bfbc-1edf184dc2d4", "name": "Contact Agent"}, {"parameters": {"name": "contentCreator", "description": "Call this tool whenever you need to create any kind of written content for the user.  \nThis includes but is not limited to: reminders, messages, summaries, emails, posts, or even fun or empathetic replies.  \n", "workflowId": {"__rl": true, "value": "MTO7R5h1y1XSuN5o", "mode": "list", "cachedResultName": "Content Agent"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [2080, 440], "id": "63f55d55-1a46-49d2-9113-2db2edf6682a", "name": "Content Creator Agent"}, {"parameters": {"toolDescription": "Use this tool to search the internet", "method": "POST", "url": "https://api.tavily.com/search", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n    \"api_key\": \"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\",\n    \"query\": \"{searchTerm}\",\n    \"search_depth\": \"basic\",\n    \"include_answer\": true,\n    \"topic\": \"news\",\n    \"include_raw_content\": true,\n    \"max_results\": 3\n} ", "placeholderDefinitions": {"values": [{"name": "searchTerm", "description": "What the user has requested to search the internet for", "type": "string"}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [2200, 440], "id": "cdd8d779-053b-4147-8f0b-85db923711bf", "name": "<PERSON><PERSON>"}, {"parameters": {"name": "calendarAgent", "description": "Call this tool for any calendar action.", "workflowId": {"__rl": true, "value": "QbIV4MIEmgQG4nUa", "mode": "list", "cachedResultName": "Calender Agent"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [1820, 440], "id": "aabea6d5-06b5-4c70-b140-78a6edc277ca", "name": "Calendar Agent"}, {"parameters": {"resource": "file", "fileId": "={{ $json.message.voice.file_id }}"}, "id": "5dc59107-b53e-47a3-9dc6-cdafe449f8da", "name": "Download File", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [0, -160], "webhookId": "83bb7385-33f6-4105-8294-1a91c0ebbee5", "credentials": {"telegramApi": {"id": "FXdJyw1yYOOUGF1m", "name": "Telegram account"}}}, {"parameters": {"resource": "audio", "operation": "transcribe", "options": {}}, "id": "9d7d1d10-4266-4b31-9f2f-cc249be68b65", "name": "Transcribe", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.6, "position": [200, -160], "credentials": {"openAiApi": {"id": "4dCIIzau81oRIK8h", "name": "OpenAi account"}}}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolCalculator", "typeVersion": 1, "position": [2320, 440], "id": "6ef56cfc-702f-4007-9d7a-d0778805b892", "name": "Calculator1"}, {"parameters": {"assignments": {"assignments": [{"id": "fe7ecc99-e1e8-4a5e-bdd6-6fce9757b234", "name": "text", "value": "={{ $json.message.text }}", "type": "string"}]}, "options": {}}, "id": "d4ce9e05-bbba-47e7-a23b-8a2aed82dc7b", "name": "Set 'Text'1", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [240, 20]}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.message.voice.file_id }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "id": "198d228f-dd5a-4a53-9cb9-de6fc2adff2b"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Voice"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "8c844924-b2ed-48b0-935c-c66a8fd0c778", "leftValue": "={{ $json.message.text }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Text"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "42930344-1ca2-4298-aa5e-4e8884602e5f", "leftValue": "", "rightValue": "", "operator": {"type": "object", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "File"}]}, "options": {}}, "id": "fde8c340-fcf2-4bc3-a23d-d669ec9a58b0", "name": "Switch1", "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-240, 20]}, {"parameters": {"chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "text": "={{ $json.output }}", "additionalFields": {"appendAttribution": false}}, "id": "f529779d-a9f8-4854-96ec-c8780a90c8ae", "name": "Response1", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [2320, 0], "webhookId": "5dced4b9-5066-4036-a4d4-14fc07edd53c", "credentials": {"telegramApi": {"id": "FXdJyw1yYOOUGF1m", "name": "Telegram account"}}}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolWikipedia", "typeVersion": 1, "position": [2440, 440], "id": "87dead2c-0287-44cf-9650-78004a35af4a", "name": "Wikipedia"}, {"parameters": {"mode": "insert", "qdrantCollection": {"__rl": true, "value": "family-memory", "mode": "list", "cachedResultName": "family-memory"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "typeVersion": 1.1, "position": [720, 460], "id": "40084dad-2aa7-4b51-af93-de38fcd585b5", "name": "Qdrant Vector Store", "credentials": {"qdrantApi": {"id": "8vZ7qiEVfdQtZNeK", "name": "QdrantApi account"}}}, {"parameters": {"jsonMode": "expressionData", "jsonData": "={{ $json.text }}\n", "options": {}}, "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [840, 660], "id": "91f01d4b-6afc-4698-91c9-83c06fe2a209", "name": "Default Data Loader"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "typeVersion": 1, "position": [860, 840], "id": "d34f1e08-2521-4fc2-aa06-017f148b57f7", "name": "Recursive Character Text Splitter"}, {"parameters": {"model": {"__rl": true, "value": "gpt-3.5-turbo", "mode": "list", "cachedResultName": "gpt-3.5-turbo"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [1720, 160], "id": "f0fbf5b5-32bc-4051-b1b1-4741899716e1", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "4dCIIzau81oRIK8h", "name": "OpenAi account"}}}, {"parameters": {"model": "text-embedding-ada-002", "options": {"stripNewLines": true}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [700, 720], "id": "e4b062ff-255f-42e8-b0c1-4c5545e71097", "name": "Embeddings OpenAI1", "credentials": {"openAiApi": {"id": "4dCIIzau81oRIK8h", "name": "OpenAi account"}}}, {"parameters": {"mode": "retrieve-as-tool", "toolName": "memory_store", "toolDescription": "Call this tool to Retrieve relevant family information to give context to the assistant's decisions. Includes names, roles, events, and personal preferences.\n", "qdrantCollection": {"__rl": true, "value": "family-memory", "mode": "list", "cachedResultName": "family-memory"}, "topK": 5, "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "typeVersion": 1.1, "position": [2580, 440], "id": "508156dc-fa2e-41f2-a8bf-633d4cab2a9f", "name": "Qdrant Vector Store1", "credentials": {"qdrantApi": {"id": "8vZ7qiEVfdQtZNeK", "name": "QdrantApi account"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [2560, 580], "id": "84db19a0-8eec-4aea-abfd-e94d88905a56", "name": "Embeddings OpenAI", "credentials": {"openAiApi": {"id": "4dCIIzau81oRIK8h", "name": "OpenAi account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('Telegram Trigger').item.json.message.chat.id }}"}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [1860, 200], "id": "4b5ea288-2d75-4bb8-91b0-e900c44dda1f", "name": "Window Buffer Memory"}, {"parameters": {"resource": "file", "fileId": "={{ $json.message.document.file_id }}\n"}, "id": "9c9c0578-d849-4250-9d99-8d500272031f", "name": "Download File1", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-240, 280], "webhookId": "83bb7385-33f6-4105-8294-1a91c0ebbee5", "credentials": {"telegramApi": {"id": "FXdJyw1yYOOUGF1m", "name": "Telegram account"}}}, {"parameters": {"jsCode": "const fileContent = JSON.parse(Buffer.from($input.first().binary.data.data, 'base64').toString());\nreturn fileContent.map(item => ({\n  json: {\n    id: item.id,\n    text: item.text,\n    metadata: item.metadata,\n  }\n}));\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-280, 560], "id": "b67a9ed8-2095-4402-ab93-c99363e5f496", "name": "Code", "retryOnFail": true, "onError": "continueErrorOutput"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "79412a1a-5b1e-48d9-85c0-f514590d4de6", "leftValue": "={{$json.text}}", "rightValue": "/(מיה|שוהם|נוי|גן|חוג|רופא|סבתא|שישי|שלישי|תזכורת|יש|ביום|בעבודה|בבית ספר)/i", "operator": {"type": "string", "operation": "regex"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [460, 20], "id": "f4fcbe6b-998f-44bb-bbcf-cc7829fc280a", "name": "If"}, {"parameters": {"assignments": {"assignments": [{"id": "e7ce88f3-4e65-46cd-9c10-800ce2f83ae4", "name": "text", "value": "={{ $json.text }}", "type": "string"}, {"id": "2e4f47c6-a94d-4d69-b5c0-b366a99914c1", "name": "metadata.source", "value": "\"user_message\"", "type": "string"}, {"id": "dbfd95ac-3101-4548-a1a1-441d34ee455c", "name": "metadata.timestamp", "value": "={{ $now }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [480, 260], "id": "03ce3bd2-768a-4e08-b985-dad287900fa9", "name": "<PERSON>"}, {"parameters": {"assignments": {"assignments": [{"id": "e6fad62f-160d-4bed-bcdf-ba4bcef72e23", "name": "fullPrompt", "value": "={{ $json.text }}{{ $json.pageContent ? '\\n\\nהקשר מזיכרון:\\n' + $json.pageContent + '\\n(נשמר בתאריך: ' + $json.metadata.timestamp + ')' : '' }}\n", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1260, 460], "id": "0537e668-1fd9-4b19-9d33-fc4d89fb89f9", "name": "Edit Fields1"}], "pinData": {}, "connections": {"Email Agent": {"ai_tool": [[{"node": "Ultimate Assistant", "type": "ai_tool", "index": 0}]]}, "Contact Agent": {"ai_tool": [[{"node": "Ultimate Assistant", "type": "ai_tool", "index": 0}]]}, "Content Creator Agent": {"ai_tool": [[{"node": "Ultimate Assistant", "type": "ai_tool", "index": 0}]]}, "Tavily": {"ai_tool": [[{"node": "Ultimate Assistant", "type": "ai_tool", "index": 0}]]}, "Calendar Agent": {"ai_tool": [[{"node": "Ultimate Assistant", "type": "ai_tool", "index": 0}]]}, "Ultimate Assistant": {"main": [[{"node": "Response1", "type": "main", "index": 0}]]}, "Download File": {"main": [[{"node": "Transcribe", "type": "main", "index": 0}]]}, "Transcribe": {"main": [[{"node": "Ultimate Assistant", "type": "main", "index": 0}]]}, "Calculator1": {"ai_tool": [[{"node": "Ultimate Assistant", "type": "ai_tool", "index": 0}]]}, "Set 'Text'1": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Switch1": {"main": [[{"node": "Download File", "type": "main", "index": 0}], [{"node": "Set 'Text'1", "type": "main", "index": 0}], [{"node": "Download File1", "type": "main", "index": 0}]]}, "Wikipedia": {"ai_tool": [[{"node": "Ultimate Assistant", "type": "ai_tool", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Qdrant Vector Store", "type": "ai_document", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Ultimate Assistant", "type": "ai_languageModel", "index": 0}]]}, "Embeddings OpenAI1": {"ai_embedding": [[{"node": "Qdrant Vector Store", "type": "ai_embedding", "index": 0}]]}, "Qdrant Vector Store1": {"ai_tool": [[{"node": "Ultimate Assistant", "type": "ai_tool", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Qdrant Vector Store1", "type": "ai_embedding", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "Ultimate Assistant", "type": "ai_memory", "index": 0}]]}, "Qdrant Vector Store": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Telegram Trigger": {"main": [[{"node": "Switch1", "type": "main", "index": 0}]]}, "Download File1": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "Qdrant Vector Store", "type": "main", "index": 0}], [{"node": "Ultimate Assistant", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}], [{"node": "Ultimate Assistant", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Qdrant Vector Store", "type": "main", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "Ultimate Assistant", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "4fe8a9b0-c21f-4441-a479-7cadd57c57a6", "meta": {"templateCredsSetupCompleted": true, "instanceId": "fe6ecd76466cab7a915757a3c6d3a0bd1630ec8396b6ebc38945ac2ef61f7eb1"}, "id": "gFI0IHQrprv83RUU", "tags": [{"createdAt": "2025-04-22T21:45:09.291Z", "updatedAt": "2025-04-22T21:45:09.291Z", "id": "5oVNN9LitL7XAvBg", "name": "Parents bot"}]}