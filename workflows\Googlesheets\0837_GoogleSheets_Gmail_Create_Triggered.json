{"meta": {"instanceId": "5e2cdd86a9e1ca2fc82cc63db38d1710d5d6a5c6fe352258a6f7112815bcd512"}, "nodes": [{"id": "13188ea7-7e66-4955-89d0-82ba4dc08dc9", "name": "Search For Folder", "type": "n8n-nodes-base.googleDrive", "position": [-2420, 500], "parameters": {"filter": {"folderId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}}, "options": {}, "resource": "fileFolder", "queryString": "={{$json.folderName}}"}, "credentials": {"googleDriveOAuth2Api": {"id": "VypmUgEf64twpmiZ", "name": "Google Drive account"}}, "typeVersion": 3, "alwaysOutputData": true}, {"id": "ed2ababb-7022-43e1-b638-0132c08ef701", "name": "Create Month Folder", "type": "n8n-nodes-base.googleDrive", "position": [-2060, 680], "parameters": {"name": "={{ $('YYYY/MM').first().json.folderName }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "id", "value": "={{ $('YYYY/MM').item.json.id }}"}, "resource": "folder"}, "credentials": {"googleDriveOAuth2Api": {"id": "VypmUgEf64twpmiZ", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "f5f2365d-0977-48b1-bd2e-29b7707839d9", "name": "Check If Folder Exists", "type": "n8n-nodes-base.if", "position": [-2240, 500], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "09b62415-cb8f-478e-b6d3-aa463fe70c81", "operator": {"type": "object", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "c27b0a9d-8ee2-4eae-963c-14256ffae0b8", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.gmailTrigger", "position": [-4400, 780], "parameters": {"simple": false, "filters": {"labelIds": ["Label_2"]}, "options": {}, "pollTimes": {"item": [{"mode": "everyX", "unit": "minutes", "value": 15}]}}, "credentials": {"gmailOAuth2": {"id": "HI2iZSvhvC5XOdpp", "name": "Gmail account 2"}}, "typeVersion": 1.2}, {"id": "3eac8c53-1b20-4511-9f2a-f5e838ca0fa0", "name": "Gmail", "type": "n8n-nodes-base.gmail", "position": [-1720, 460], "webhookId": "e62ae049-d968-4e6a-a62d-06963c8e592f", "parameters": {"simple": false, "options": {"downloadAttachments": true}, "messageId": "={{ $('Gmail Trigger').item.json.id }}", "operation": "get"}, "credentials": {"gmailOAuth2": {"id": "HI2iZSvhvC5XOdpp", "name": "Gmail account 2"}}, "typeVersion": 2.1}, {"id": "bfae9bb5-6915-4968-8b5e-e72dd46bda55", "name": "Split Up Binary Data1", "type": "n8n-nodes-base.function", "position": [-1560, 460], "parameters": {"functionCode": "let results = [];\n\nfor (item of items) {\n    for (key of Object.keys(item.binary)) {\n        results.push({\n            json: {\n                fileName: item.binary[key].fileName\n            },\n            binary: {\n                data: item.binary[key],\n            }\n        });\n    }\n}\n\nreturn results;"}, "typeVersion": 1}, {"id": "baf55ab9-511f-4404-a2cc-b1c848f6f5c5", "name": "Note2", "type": "n8n-nodes-base.stickyNote", "position": [-1800, 280], "parameters": {"color": 7, "width": 920, "height": 660, "content": "## Upload attachments to Drive\nIncoming files are split up into individual items, each with a single binary data object under the `data` key.\nFiles names are prefixed with the current timestamp"}, "typeVersion": 1}, {"id": "5d706d3a-db17-4f5f-9eac-ba91c470dbdd", "name": "YYYY/MM", "type": "n8n-nodes-base.set", "position": [-2600, 500], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "143b3b94-a8d7-46b6-8ea8-2e70c082f5b1", "name": "=folderName", "type": "string", "value": "={{\n  new Date($('Gmail Trigger').item.json.date).getUTCFullYear() \n  + '/' + \n  String(new Date($('Gmail Trigger').item.json.date).getUTCMonth() + 1).padStart(2, '0')\n}}\n"}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "b20a3833-f648-454d-999b-d799727e18e8", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [-1320, 460], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "bb8c0d21-de74-4abf-bf6c-5eef3f301513", "name": "Note3", "type": "n8n-nodes-base.stickyNote", "position": [-2680, 280], "parameters": {"color": 7, "width": 820, "height": 660, "content": "# Checks if YYYY/MM Folder exists\n## If the directory doesn't exist it is created"}, "typeVersion": 1}, {"id": "40971ca3-91d3-4651-8137-e973dbd2dbbd", "name": "Company Folder Exists", "type": "n8n-nodes-base.if", "position": [-3180, 500], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "09b62415-cb8f-478e-b6d3-aa463fe70c81", "operator": {"type": "object", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "086ff643-ca10-46ec-92b5-8a014fd3bf3f", "name": "Create Company Folder", "type": "n8n-nodes-base.googleDrive", "position": [-2920, 620], "parameters": {"name": "={{ $('Lookup in Sheets').item.json.company }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "18ry0AUtrpp3re6u3zQvvs0BQUGFmBKN9", "cachedResultUrl": "https://drive.google.com/drive/folders/18ry0AUtrpp3re6u3zQvvs0BQUGFmBKN9", "cachedResultName": "Invoices"}, "resource": "folder"}, "credentials": {"googleDriveOAuth2Api": {"id": "VypmUgEf64twpmiZ", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "7792afb7-61d9-402f-814b-f4625cd012bc", "name": "Note4", "type": "n8n-nodes-base.stickyNote", "position": [-3500, 120], "parameters": {"color": 7, "width": 760, "height": 820, "content": "# Checks if a folder with the company of the email exists\n## If it doesn't the directory is created"}, "typeVersion": 1}, {"id": "1f61ea45-49e6-4018-91ad-2144c1bbc19a", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-4120, 280], "parameters": {"color": 6, "width": 560, "height": 660, "content": "# 2. Google Sheets Whitelist Config\n\n## To filter contacts against a whitelist:\n### 1. Make a copy of [this spreadsheet](https://docs.google.com/spreadsheets/d/1tTz9BflstxVL18YG11Ny1eiDj3FcjvtZ619b_bHx8h4/edit?usp=sharing)\n**OR** create a Google Sheet with two columns:\n| **email**     | **company**      |\n\n\n### 2. Add whitelisted emails and their company as rows in the sheet and configure this node **Document** and **Sheet** to point to it.\n\n\n\n\n\n\n\n\n\n\n\n\n\n"}, "typeVersion": 1}, {"id": "f7009cc2-8194-40c9-98e9-edc4a29c5ce8", "name": "Lookup in Sheets", "type": "n8n-nodes-base.googleSheets", "position": [-3900, 780], "parameters": {"options": {}, "filtersUI": {"values": [{"lookupValue": "={{ $('Gmail Trigger').item.json.from.value[0].address }}", "lookupColumn": "email"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1gZE7EbLJqfMzQlPoCgE0eeqee_F1Lh9eIwhHsVmYKdw/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1gZE7EbLJqfMzQlPoCgE0eeqee_F1Lh9eIwhHsVmYKdw", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1gZE7EbLJqfMzQlPoCgE0eeqee_F1Lh9eIwhHsVmYKdw/edit?usp=drivesdk", "cachedResultName": "Contacts Whitelist"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "63dUs6P8a2b5ed5J", "name": "Google Sheets account"}}, "typeVersion": 4.5, "alwaysOutputData": false}, {"id": "932afe12-3341-4f77-88ab-0b558e0d6ee2", "name": "Search Company Folder1", "type": "n8n-nodes-base.googleDrive", "position": [-3440, 500], "parameters": {"filter": {"whatToSearch": "folders"}, "options": {}, "resource": "fileFolder", "queryString": "={{ $('Lookup in Sheets').item.json.company }}"}, "credentials": {"googleDriveOAuth2Api": {"id": "VypmUgEf64twpmiZ", "name": "Google Drive account"}}, "typeVersion": 3, "alwaysOutputData": true}, {"id": "b9e66cf4-365a-4d11-bff9-48bf28be9e96", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-4740, 280], "parameters": {"color": 6, "width": 560, "height": 660, "content": "# 1. Trigger Settings and Filters\n\n## Configure the interval to check for new emails and apply filters to process only some emails\n\n**For example**: To create a filter that applies a label to emails **with attachments** containing the words \"invoice\" or \"receipt,\" follow these steps:\n\n1. Open your Gmail and click on the burger menu button next to the search bar to open the search options.\n2. In the `Has the words` field type in 'invoice receipt'\n3. Check the `Has attachment` checkbox\n4. Click on the \"Create filter with this search\" option at the bottom of the search window.\n5. In the filter options, select the \"Apply the label\" option and choose or create a label for these emails.\n6. Click \"Create filter\" to save your new filter.\n\n\n\n\n\n\n\n\n\n\n\n"}, "typeVersion": 1}, {"id": "2a932450-d0e9-44b4-adfb-2254b8e6e547", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-3000, 220], "parameters": {"color": 6, "height": 540, "content": "# 3. Configure storage location\n## Set where to store files from the `parent folder` dropdown"}, "typeVersion": 1}, {"id": "247e4ed7-ebff-4392-adf2-4a63e80e04f4", "name": "Upload To Folder", "type": "n8n-nodes-base.googleDrive", "position": [-1100, 480], "parameters": {"name": "={{ Date.now();}}-{{ $('Loop Over Items').item.binary.data.fileName }} ", "driveId": {"__rl": true, "mode": "list", "value": "My Drive", "cachedResultUrl": "https://drive.google.com/drive/my-drive", "cachedResultName": "My Drive"}, "options": {"ocrLanguage": "en", "propertiesUi": {"propertyValues": [{"key": "sender", "value": "={{ $('Gmail').item.json.from.value[0].address }}"}, {"key": "time_received", "value": "={{ $('Gmail').item.json.date }}"}]}}, "folderId": {"__rl": true, "mode": "id", "value": "={{ $('Search For Folder').first().json.id || $('Create Month Folder').item.json.id }}"}, "inputDataFieldName": "=data"}, "credentials": {"googleDriveOAuth2Api": {"id": "VypmUgEf64twpmiZ", "name": "Google Drive account"}}, "typeVersion": 3}], "pinData": {}, "connections": {"Gmail": {"main": [[{"node": "Split Up Binary Data1", "type": "main", "index": 0}]]}, "YYYY/MM": {"main": [[{"node": "Search For Folder", "type": "main", "index": 0}]]}, "Gmail Trigger": {"main": [[{"node": "Lookup in Sheets", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Upload To Folder", "type": "main", "index": 0}]]}, "Lookup in Sheets": {"main": [[{"node": "Search Company Folder1", "type": "main", "index": 0}]]}, "Upload To Folder": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Search For Folder": {"main": [[{"node": "Check If Folder Exists", "type": "main", "index": 0}]]}, "Create Month Folder": {"main": [[{"node": "Gmail", "type": "main", "index": 0}]]}, "Company Folder Exists": {"main": [[{"node": "YYYY/MM", "type": "main", "index": 0}], [{"node": "Create Company Folder", "type": "main", "index": 0}]]}, "Create Company Folder": {"main": [[{"node": "YYYY/MM", "type": "main", "index": 0}]]}, "Split Up Binary Data1": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Check If Folder Exists": {"main": [[{"node": "Gmail", "type": "main", "index": 0}], [{"node": "Create Month Folder", "type": "main", "index": 0}]]}, "Search Company Folder1": {"main": [[{"node": "Company Folder Exists", "type": "main", "index": 0}]]}}}