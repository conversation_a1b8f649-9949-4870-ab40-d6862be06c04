{"id": "9nBQ1BfwxLhuzTcK", "meta": {"instanceId": "3378b0d68c3b7ebfc71b79896d94e1a044dec38e99a1160aed4e9c323910fbe2"}, "name": "google drive to instagram, tiktok and youtube", "tags": [], "nodes": [{"id": "b6c1d2f5-a8de-42dc-a164-3b1e80b2f19d", "name": "Google Drive Trigger", "type": "n8n-nodes-base.googleDriveTrigger", "position": [220, 320], "parameters": {"event": "fileCreated", "options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "mode": "list", "value": "18m0i341QLQuyWuHv_FBdz8-r-QDtofYm", "cachedResultUrl": "https://drive.google.com/drive/folders/18m0i341QLQuyWuHv_FBdz8-r-QDtofYm", "cachedResultName": "Influencersde"}}, "credentials": {"googleDriveOAuth2Api": {"id": "2TbhWtnbRfSloGxX", "name": "Google Drive account"}}, "typeVersion": 1}, {"id": "1dda484a-f6f5-4677-85a3-09b2a47e69c4", "name": "Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [400, 320], "parameters": {"fileId": {"__rl": true, "mode": "", "value": "={{ $json.id || $json.data[0].id }}"}, "options": {}, "operation": "download", "authentication": "oAuth2"}, "credentials": {"googleDriveOAuth2Api": {"id": "2TbhWtnbRfSloGxX", "name": "Google Drive account"}}, "retryOnFail": true, "typeVersion": 1, "waitBetweenTries": 5000}, {"id": "f9388923-b20e-40f0-ba10-fd00b463b1a7", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.errorTrigger", "position": [620, 660], "parameters": {}, "typeVersion": 1}, {"id": "eda45ad6-d976-4665-9b6d-dae4c3212191", "name": "Telegram", "type": "n8n-nodes-base.telegram", "position": [960, 640], "webhookId": "f6729386-9905-45f1-800f-4fe01a06ac9c", "parameters": {"text": "=🔔 ERROR SUBIENDO VIDEOS", "additionalFields": {"appendAttribution": false}}, "retryOnFail": true, "typeVersion": 1.2, "waitBetweenTries": 5000}, {"id": "7b1d6015-49b8-423c-be64-e905ff791574", "name": "If", "type": "n8n-nodes-base.if", "position": [760, 660], "parameters": {"options": {}, "conditions": {"options": {"version": 1, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "9fadb3fd-2547-42bd-8f40-f410a97dcf57", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.trigger.error.message }}", "rightValue": "The DNS server returned an error, perhaps the server is offline"}]}}, "typeVersion": 2.1}, {"id": "6e9882aa-b11f-4c1a-8600-eedda9d92046", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-220, 0], "parameters": {"width": 860, "height": 260, "content": "## Description\nThis automation allows you to upload a video to a configured Google Drive folder, and it will automatically create descriptions and upload it to Instagram and TikTok.\n\n## How to Use\n1. Generate an API token at upload-post.com and add to Upload to Tiktok and Upload to Instagram nodes\n2. Configure your Google Drive folder\n3. Customize the OpenAI prompt for your specific use case\n4. Optional: Configure Telegram for error notifications\n\n## Requirements\n- upload-post.com account\n- Google Drive account\n- OpenAI API key\n"}, "typeVersion": 1}, {"id": "b3eed1dc-8273-4593-ab07-8860fffa0907", "name": "Get Audio from Video", "type": "@n8n/n8n-nodes-langchain.openAi", "notes": "Extract the audio from video for generate the description", "position": [860, 320], "parameters": {"options": {}, "resource": "audio", "operation": "transcribe"}, "credentials": {"openAiApi": {"id": "XJdxgMSXFgwReSsh", "name": "n8n key"}}, "notesInFlow": true, "retryOnFail": true, "typeVersion": 1, "waitBetweenTries": 5000}, {"id": "b057fea0-087e-4c7f-b5ac-6d16ca658437", "name": "Read video from Google Drive", "type": "n8n-nodes-base.writeBinaryFile", "position": [580, 320], "parameters": {"options": {}, "fileName": "={{ $json.originalFilename.replaceAll(\" \", \"_\") }}"}, "typeVersion": 1}, {"id": "f9296b8f-b631-4df4-b8b5-aa7139dd65cd", "name": "Generate Description for Videos  in Tiktok and Instagram", "type": "@n8n/n8n-nodes-langchain.openAi", "notes": "Request to OpenAi for generate description with the audio extracted from the video", "position": [1060, 320], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "GPT-4O"}, "options": {}, "messages": {"values": [{"role": "system", "content": "You are an expert assistant in creating engaging social media video titles."}, {"content": "=I'm going to upload a video to social media. Here are some examples of descriptions that have worked well on Instagram:\n\nFollow and save for later. Discover InfluencersDe, the AI tool that automates TikTok creation and publishing to drive traffic to your website. Perfect for entrepreneurs and brands.\n#digitalmarketing #ugc #tiktok #ai #influencersde #contentcreation\n\nDiscover the video marketing revolution with InfluencersDe!\n.\n.\n.\n#socialmedia #videomarketing #ai #tiktok #influencersde #growthhacking\n\nDon't miss InfluencersDe, the tool that transforms your marketing strategy with just one click!\n.\n.\n.\n#ugc #ai #tiktok #digitalmarketing #influencersde #branding\n\nCan you create another title for the Instagram post based on this recognized audio from the video?\n\nAudio: {{ $('Get Audio from Video').item.json.text }}\n\nIMPORTANT: Reply only with the description, don't add anything else."}]}}, "credentials": {"openAiApi": {"id": "XJdxgMSXFgwReSsh", "name": "n8n key"}}, "notesInFlow": true, "retryOnFail": true, "typeVersion": 1.4, "waitBetweenTries": 5000}, {"id": "e80758fd-5532-48b0-b663-085629137fc0", "name": "Read Video from Google Drive", "type": "n8n-nodes-base.readBinaryFile", "position": [1620, 100], "parameters": {"filePath": "={{ $('Read video from Google Drive').item.json.originalFilename.replaceAll(\" \", \"_\") }}", "dataPropertyName": "datavideo"}, "typeVersion": 1}, {"id": "8f13c601-4282-4a44-8e8a-dc88e4165ee4", "name": "Read Video from Google Drive2", "type": "n8n-nodes-base.readBinaryFile", "position": [1620, 400], "parameters": {"filePath": "={{ $('Read video from Google Drive').item.json.originalFilename.replaceAll(\" \", \"_\") }}", "dataPropertyName": "datavideo"}, "typeVersion": 1}, {"id": "1b46976e-be37-49bd-b77b-e48d8e619954", "name": "Upload Video and Description to Tiktok", "type": "n8n-nodes-base.httpRequest", "notes": "Generate in upload-post.com the token and add to the credentials in the header-> Authorization: Apikey (token here)", "position": [1880, 100], "parameters": {"url": "https://api.upload-post.com/api/upload", "method": "POST", "options": {}, "sendBody": true, "contentType": "multipart-form-data", "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "title", "value": "={{ $('Generate Description for Videos  in Tiktok and Instagram').item.json.message.content.replaceAll(\"\\\"\", \"\") }}"}, {"name": "platform[]", "value": "tiktok"}, {"name": "video", "parameterType": "formBinaryData", "inputDataFieldName": "datavideo"}, {"name": "user", "value": "Add user generated in upload-post"}]}, "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "WNjAx7UqrEZ1JDrR", "name": "VituManco"}}, "notesInFlow": true, "typeVersion": 4.2}, {"id": "0404a57f-2c1a-4ccd-90df-893dd01acaa0", "name": "Upload Video and Description to Instagram", "type": "n8n-nodes-base.httpRequest", "notes": "Generate in upload-post.com the token and add to the credentials in the header-> Authorization: Apikey (token here)", "position": [1880, 400], "parameters": {"url": "https://api.upload-post.com/api/upload", "method": "POST", "options": {}, "sendBody": true, "contentType": "multipart-form-data", "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "title", "value": "={{ $('Generate Description for Videos  in Tiktok and Instagram').item.json.message.content.replaceAll(\"\\\"\", \"\") }}"}, {"name": "platform[]", "value": "instagram"}, {"name": "video", "parameterType": "formBinaryData", "inputDataFieldName": "datavideo"}, {"name": "user", "value": "Add user generated in upload-post"}]}, "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "47dO31ED0WIaJkR6", "name": "Header Auth account"}}, "notesInFlow": true, "typeVersion": 4.2}, {"id": "358da7b7-2d0a-475b-a10d-ffc499b5e99d", "name": "Read Video from Google Drive3", "type": "n8n-nodes-base.readBinaryFile", "position": [1620, 660], "parameters": {"filePath": "={{ $('Read video from Google Drive').item.json.originalFilename.replaceAll(\" \", \"_\") }}", "dataPropertyName": "datavideo"}, "typeVersion": 1}, {"id": "0e46ee9b-e466-4a5d-8916-3836eed4fc2d", "name": "Upload Video and Description to Youtube", "type": "n8n-nodes-base.httpRequest", "notes": "Generate in upload-post.com the token and add to the credentials in the header-> Authorization: Apikey (token here)", "position": [1880, 660], "parameters": {"url": "https://api.upload-post.com/api/upload", "method": "POST", "options": {}, "sendBody": true, "contentType": "multipart-form-data", "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "title", "value": "={{ $('Generate Description for Videos  in Tiktok and Instagram').item.json.message.content.replaceAll(\"\\\"\", \"\").substring(0, 70) }}\n"}, {"name": "platform[]", "value": "youtube"}, {"name": "video", "parameterType": "formBinaryData", "inputDataFieldName": "datavideo"}, {"name": "user", "value": "test2"}]}, "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "47dO31ED0WIaJkR6", "name": "Header Auth account"}}, "notesInFlow": true, "typeVersion": 4.2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "13975e04-a6c4-42d0-887c-e6c4ff219f42", "connections": {"If": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "Google Drive": {"main": [[{"node": "Read video from Google Drive", "type": "main", "index": 0}]]}, "Error Trigger": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Get Audio from Video": {"main": [[{"node": "Generate Description for Videos  in Tiktok and Instagram", "type": "main", "index": 0}]]}, "Google Drive Trigger": {"main": [[{"node": "Google Drive", "type": "main", "index": 0}]]}, "Read Video from Google Drive": {"main": [[{"node": "Upload Video and Description to Tiktok", "type": "main", "index": 0}]]}, "Read video from Google Drive": {"main": [[{"node": "Get Audio from Video", "type": "main", "index": 0}]]}, "Read Video from Google Drive2": {"main": [[{"node": "Upload Video and Description to Instagram", "type": "main", "index": 0}]]}, "Read Video from Google Drive3": {"main": [[{"node": "Upload Video and Description to Youtube", "type": "main", "index": 0}]]}, "Generate Description for Videos  in Tiktok and Instagram": {"main": [[{"node": "Read Video from Google Drive", "type": "main", "index": 0}, {"node": "Read Video from Google Drive2", "type": "main", "index": 0}, {"node": "Read Video from Google Drive3", "type": "main", "index": 0}]]}}}