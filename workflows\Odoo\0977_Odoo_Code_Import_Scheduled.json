{"id": "4aKofiCShqdDSsIS", "meta": {"instanceId": "05578cf7a897ec6100e0a45f52bd1e8b9130ac799ebd6a9ebe3531f9bd89fc01", "templateId": "3181"}, "name": "Import Odoo Product Images from Google Drive", "tags": [], "nodes": [{"id": "690beab3-2e3a-4426-9e90-fde834cb2c72", "name": "Filter Images", "type": "n8n-nodes-base.filter", "position": [820, 340], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "or", "conditions": [{"id": "bb0df6d8-525b-4054-9340-4400ddd40c81", "operator": {"type": "string", "operation": "endsWith"}, "leftValue": "={{ $json.name }}", "rightValue": ".png"}, {"id": "8ebcb3fb-dd64-40f6-94c9-5b13021847d9", "operator": {"type": "string", "operation": "endsWith"}, "leftValue": "={{ $json.name }}", "rightValue": ".jpg"}]}}, "typeVersion": 2.2}, {"id": "6fec7062-3f85-4ce0-86cd-6ac4f1169192", "name": "Find Files", "type": "n8n-nodes-base.googleDrive", "position": [600, 340], "parameters": {"filter": {"driveId": {"__rl": true, "mode": "list", "value": "0AGL-iqy2wxM8Uk9PVA", "cachedResultUrl": "https://drive.google.com/drive/folders/0AGL-iqy2wxM8Uk9PVA", "cachedResultName": "Middleware"}, "folderId": {"__rl": true, "mode": "list", "value": "1VG-7mRW8tsmJelW5FTeoj2jXeObMvan6", "cachedResultUrl": "https://drive.google.com/drive/folders/1VG-7mRW8tsmJelW5FTeoj2jXeObMvan6", "cachedResultName": "input"}}, "options": {}, "resource": "fileFolder", "returnAll": true}, "credentials": {"googleDriveOAuth2Api": {"id": "HTm4uAxSPW7DoxGv", "name": "Google Drive Administrator"}}, "typeVersion": 3}, {"id": "10eb5837-9808-4e71-9bfd-82eb788e036b", "name": "Decorate Images", "type": "n8n-nodes-base.code", "position": [1040, 340], "parameters": {"jsCode": "for (const item of $input.all()) {\n    let parts = item.json.name.split('.').slice(0, -1).join('.').split('_');\n    item.json.model = parts[0];\n    item.json.sku = parts.slice(1).join('_');\n}\n\nreturn $input.all();\n"}, "typeVersion": 2}, {"id": "dc2d4e62-2b34-4f07-8ae9-aa2d7b169085", "name": "Switch", "type": "n8n-nodes-base.switch", "position": [1260, 40], "parameters": {"rules": {"values": [{"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "e1d26dbe-1855-4d62-8061-43a7d56c2705", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.model }}", "rightValue": "template"}]}}, {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "b7c889f6-d84a-4573-b7ba-35e51405bf94", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.model }}", "rightValue": "product"}]}}]}, "options": {}}, "typeVersion": 3.2}, {"id": "1c7d98b0-ea85-4841-8764-e3d3b8369a11", "name": "Move Images", "type": "n8n-nodes-base.googleDrive", "position": [1260, 540], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "driveId": {"__rl": true, "mode": "list", "value": "0AAaxIiOTPGeCUk9PVA", "cachedResultUrl": "https://drive.google.com/drive/folders/0AAaxIiOTPGeCUk9PVA", "cachedResultName": "Middleware"}, "folderId": {"__rl": true, "mode": "list", "value": "1NqxzbwarAZ1BtkoyM-T8NNcO5m_cmO1V", "cachedResultUrl": "https://drive.google.com/drive/folders/1NqxzbwarAZ1BtkoyM-T8NNcO5m_cmO1V", "cachedResultName": "done"}, "operation": "move"}, "credentials": {"googleDriveOAuth2Api": {"id": "HTm4uAxSPW7DoxGv", "name": "Google Drive Administrator"}}, "typeVersion": 3}, {"id": "29444363-00f7-427c-b377-e3c453e80e8f", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [380, 440], "parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 10}]}}, "typeVersion": 1.2}, {"id": "fc675661-ee5c-47d6-abe5-40c15f92bcda", "name": "Sum Images", "type": "n8n-nodes-base.summarize", "position": [1480, 540], "parameters": {"options": {}, "fieldsToSummarize": {"values": [{"field": "id"}]}}, "typeVersion": 1.1}, {"id": "287704cf-b3bb-4ac7-9e37-5577eb33df8f", "name": "Announce", "type": "n8n-nodes-base.googleChat", "position": [1700, 540], "webhookId": "a1b21478-fbd9-49e7-9e0c-cdf86048d038", "parameters": {"spaceId": "spaces/AAAAt6xI1aY", "messageUi": {"text": "=Product images done onto Google Drive (total : {{ $json.count_id }})."}, "authentication": "oAuth2", "additionalFields": {}}, "credentials": {"googleChatOAuth2Api": {"id": "Gv5dSRXyRjQcwRph", "name": "Google Chat Administrator"}}, "typeVersion": 1}, {"id": "e41ebdd1-3841-482b-864d-6534db92ba74", "name": "Find Templates", "type": "n8n-nodes-base.odoo", "position": [1480, -60], "parameters": {"limit": 1, "options": {"fieldsList": ["id"]}, "resource": "custom", "operation": "getAll", "filterRequest": {"filter": [{"value": "={{ $json.sku }}", "fieldName": "default_code"}]}, "customResource": "product.template"}, "credentials": {"odooApi": {"id": "eTbK0f2MmAZsrOtT", "name": "Odoo AArtIntelligent"}}, "typeVersion": 1, "alwaysOutputData": false}, {"id": "86e0145e-9701-4af4-a5a6-d9f4f77d6115", "name": "Download Images Templates", "type": "n8n-nodes-base.googleDrive", "position": [1700, -60], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $('Filter Images').item.json.id }}"}, "options": {"binaryPropertyName": "data"}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "HTm4uAxSPW7DoxGv", "name": "Google Drive Administrator"}}, "typeVersion": 3}, {"id": "6132ae9b-d82d-4aa5-9f42-8a0e975b5485", "name": "Update Images Templates", "type": "n8n-nodes-base.odoo", "position": [2140, -60], "parameters": {"resource": "custom", "operation": "update", "customResource": "product.template", "customResourceId": "={{ $('Find Templates').item.json.id }}", "fieldsToCreateOrUpdate": {"fields": [{"fieldName": "image_1920", "fieldValue": "={{ $json.data }}"}, {"fieldName": "image_1024", "fieldValue": "={{ $json.data }}"}, {"fieldName": "image_512", "fieldValue": "={{ $json.data }}"}, {"fieldName": "image_256", "fieldValue": "={{ $json.data }}"}, {"fieldName": "image_128", "fieldValue": "={{ $json.data }}"}]}}, "credentials": {"odooApi": {"id": "eTbK0f2MmAZsrOtT", "name": "Odoo AArtIntelligent"}}, "typeVersion": 1}, {"id": "1dbfc15a-fec4-416f-8286-e16672a78e1f", "name": "Find Products", "type": "n8n-nodes-base.odoo", "position": [1480, 140], "parameters": {"limit": 1, "options": {"fieldsList": ["id"]}, "resource": "custom", "operation": "getAll", "filterRequest": {"filter": [{"value": "={{ $json.sku }}", "fieldName": "default_code"}]}, "customResource": "product.product"}, "credentials": {"odooApi": {"id": "eTbK0f2MmAZsrOtT", "name": "Odoo AArtIntelligent"}}, "typeVersion": 1}, {"id": "8963a175-6bf7-4101-8748-cd11e1a77e0a", "name": "Download Images Products", "type": "n8n-nodes-base.googleDrive", "position": [1700, 140], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $('Filter Images').item.json.id }}"}, "options": {"binaryPropertyName": "data"}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "HTm4uAxSPW7DoxGv", "name": "Google Drive Administrator"}}, "typeVersion": 3}, {"id": "8ee836a9-f962-426e-9fe2-c989b3da8a3b", "name": "Update Images Products", "type": "n8n-nodes-base.odoo", "position": [2140, 140], "parameters": {"resource": "custom", "operation": "update", "customResource": "product.product", "customResourceId": "={{ $('Find Products').item.json.id }}", "fieldsToCreateOrUpdate": {"fields": [{"fieldName": "image_1920", "fieldValue": "={{ $json.data }}"}, {"fieldName": "image_1024", "fieldValue": "={{ $json.data }}"}, {"fieldName": "image_512", "fieldValue": "={{ $json.data }}"}, {"fieldName": "image_256", "fieldValue": "={{ $json.data }}"}, {"fieldName": "image_128", "fieldValue": "={{ $json.data }}"}]}}, "credentials": {"odooApi": {"id": "eTbK0f2MmAZsrOtT", "name": "Odoo AArtIntelligent"}}, "typeVersion": 1}, {"id": "4c2d03c6-896a-4f5f-ae23-68717aa50697", "name": "Convert Base64 Images Templates", "type": "n8n-nodes-base.extractFromFile", "position": [1920, -60], "parameters": {"options": {}, "operation": "binaryToPropery"}, "typeVersion": 1}, {"id": "0a894d9e-8021-46c9-a9c1-399d7a56546d", "name": "Convert Base64 Images Products", "type": "n8n-nodes-base.extractFromFile", "position": [1920, 140], "parameters": {"options": {}, "operation": "binaryToPropery"}, "typeVersion": 1}, {"id": "a618d02d-fe52-42ab-9d62-1c263992ac24", "name": "Search Old Images", "type": "n8n-nodes-base.googleDrive", "position": [1260, 340], "parameters": {"filter": {"driveId": {"__rl": true, "mode": "list", "value": "0AAaxIiOTPGeCUk9PVA", "cachedResultUrl": "https://drive.google.com/drive/folders/0AAaxIiOTPGeCUk9PVA", "cachedResultName": "Middleware"}, "folderId": {"__rl": true, "mode": "list", "value": "1NqxzbwarAZ1BtkoyM-T8NNcO5m_cmO1V", "cachedResultUrl": "https://drive.google.com/drive/folders/1NqxzbwarAZ1BtkoyM-T8NNcO5m_cmO1V", "cachedResultName": "done"}}, "options": {}, "resource": "fileFolder", "queryString": "={{ $('Filter Images').item.json.name }}"}, "credentials": {"googleDriveOAuth2Api": {"id": "HTm4uAxSPW7DoxGv", "name": "Google Drive Administrator"}}, "typeVersion": 3}, {"id": "cd82a937-7129-4baf-9515-41ab5aef497d", "name": "Drop Old Images", "type": "n8n-nodes-base.googleDrive", "position": [1480, 340], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "options": {}, "operation": "deleteFile"}, "credentials": {"googleDriveOAuth2Api": {"id": "HTm4uAxSPW7DoxGv", "name": "Google Drive Administrator"}}, "typeVersion": 3}, {"id": "b134c298-989c-460e-8caf-497ccbea53cd", "name": "Click Manual", "type": "n8n-nodes-base.manualTrigger", "position": [380, 240], "parameters": {}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {}, "versionId": "b98c3b1d-52f1-4dd2-b204-892bb96b1b8a", "connections": {"Switch": {"main": [[{"node": "Find Templates", "type": "main", "index": 0}], [{"node": "Find Products", "type": "main", "index": 0}]]}, "Find Files": {"main": [[{"node": "Filter Images", "type": "main", "index": 0}]]}, "Sum Images": {"main": [[{"node": "Announce", "type": "main", "index": 0}]]}, "Move Images": {"main": [[{"node": "Sum Images", "type": "main", "index": 0}]]}, "Click Manual": {"main": [[{"node": "Find Files", "type": "main", "index": 0}]]}, "Filter Images": {"main": [[{"node": "Decorate Images", "type": "main", "index": 0}]]}, "Find Products": {"main": [[{"node": "Download Images Products", "type": "main", "index": 0}]]}, "Find Templates": {"main": [[{"node": "Download Images Templates", "type": "main", "index": 0}]]}, "Decorate Images": {"main": [[{"node": "Switch", "type": "main", "index": 0}, {"node": "Move Images", "type": "main", "index": 0}, {"node": "Search Old Images", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Find Files", "type": "main", "index": 0}]]}, "Search Old Images": {"main": [[{"node": "Drop Old Images", "type": "main", "index": 0}]]}, "Download Images Products": {"main": [[{"node": "Convert Base64 Images Products", "type": "main", "index": 0}]]}, "Download Images Templates": {"main": [[{"node": "Convert Base64 Images Templates", "type": "main", "index": 0}]]}, "Convert Base64 Images Products": {"main": [[{"node": "Update Images Products", "type": "main", "index": 0}]]}, "Convert Base64 Images Templates": {"main": [[{"node": "Update Images Templates", "type": "main", "index": 0}]]}}}