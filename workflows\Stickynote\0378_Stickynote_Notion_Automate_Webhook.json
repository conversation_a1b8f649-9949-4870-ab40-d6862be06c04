{"meta": {"instanceId": "257476b1ef58bf3cb6a46e65fac7ee34a53a5e1a8492d5c6e4da5f87c9b82833", "templateId": "2038"}, "nodes": [{"id": "0db90229-9929-4d48-93f0-2425c83993ea", "name": "POST", "type": "n8n-nodes-base.webhook", "position": [780, 280], "webhookId": "0626e4cc-e132-4024-9ab9-443a9ac7b133", "parameters": {"path": "1c04b027-39d2-491a-a9c6-194289fe400c", "options": {}, "httpMethod": "POST"}, "typeVersion": 1}, {"id": "5441fa4b-adea-4cdc-a224-b4240e3711ea", "name": "Notion", "type": "n8n-nodes-base.notion", "position": [1080, 280], "parameters": {"title": "={{ $json.body.url }}", "options": {}, "resource": "databasePage", "databaseId": {"__rl": true, "mode": "list", "value": "1420d3ae-bedc-4d23-a932-b402814db9d1", "cachedResultUrl": "https://www.notion.so/1420d3aebedc4d23a932b402814db9d1", "cachedResultName": "Bookmarks"}}, "typeVersion": 2.1}, {"id": "9cde5c9e-743a-4368-be53-d8fb57e2da01", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [720, 100], "parameters": {"color": 7, "width": 223, "height": 350, "content": "## Webhook Trigger\nThis node listens for the event on the bookmarklet we are going to create.\nThe settings for this should be POST "}, "typeVersion": 1}, {"id": "0763df72-8eb0-4fe5-9dbb-f5cc12445e46", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1000, 100], "parameters": {"color": 7, "width": 463, "height": 349, "content": "## Adding data to notion\nGo to your notion database and add a new database that shall be recording all your bookmarks. Make sure to add your application. (If you do not add this your bookmark wont be saved)\n\nTest the webhook with to see how the urls are formated in the database\n"}, "typeVersion": 1}], "pinData": {}, "connections": {"POST": {"main": [[{"node": "Notion", "type": "main", "index": 0}]]}}}