{"id": "vpZ1wpsniCvKYjCF", "meta": {"instanceId": "1e003a7ea4715b6b35e9947791386a7d07edf3b5bf8d4c9b7ee4fdcbec0447d7"}, "name": "General 3D Presentation", "tags": [], "nodes": [{"id": "629ef84e-ffb7-4143-b0af-764bcb86a7fa", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [360, 40], "parameters": {}, "typeVersion": 1}, {"id": "d2751dcb-fd03-4a87-a501-43f701d2704a", "name": "Prompt", "type": "n8n-nodes-base.httpRequest", "position": [580, 40], "parameters": {"url": "https://api.piapi.ai/api/v1/task", "method": "POST", "options": {}, "jsonBody": "{\n  \"model\": \"midjourney\",\n  \"task_type\": \"imagine\",\n  \"input\": {\n    \"prompt\": \"A blind box character design, in the chibi style, a super cute little girl wearing a white long-sleeved dress and pearl earrings with her head bowed in a prayer pose, facing upwards, wearing an oversized off-white dress with large round pearls on the shoulders, minimalist simple dress with Ruffles, against a beige background, a full-body shot in a three-quarter profile view, with a black, blue, and gray color scheme, soft lighting, 3D rendering, clay material, high detail, in the Pixar style. Clean white skin, brown renaissance braided bun. --ar 1:1 --niji 6\",\n    \"aspect_ratio\": \"2:3\",\n    \"process_mode\": \"turbo\",\n    \"skip_prompt_check\": false\n  }\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "x-api-key"}]}}, "typeVersion": 4.2}, {"id": "f3f4b801-4b2b-49bf-a9be-1f38e62720ac", "name": "Midjourney Generator", "type": "n8n-nodes-base.httpRequest", "position": [400, 280], "parameters": {"url": "=https://api.piapi.ai/api/v1/task/{{ $json.data.task_id }}", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-api-key"}]}}, "typeVersion": 4.2}, {"id": "a7ecf5f5-73fd-4d5b-b851-568dde2e797a", "name": "GPT-4o Image Generator", "type": "n8n-nodes-base.httpRequest", "position": [1060, 80], "parameters": {"url": "https://api.piapi.ai/v1/chat/completions", "method": "POST", "options": {}, "jsonBody": "={\n    \"model\": \"gpt-4o-image-preview\",\n    \"messages\": [\n        {\n            \"role\": \"user\",\n            \"content\": [\n                {\n                    \"type\": \"image_url\",\n                    \"image_url\": {\n                        \"url\": \"{{ $json.random_temp_url }}\"\n                    }\n                },\n                {\n                    \"type\": \"text\",\n                    \"text\": \"Convert this image into a 3D figurine image, with front view, with full details, profile. \"\n                }\n            ]\n        }\n    ],\n    \"stream\": true\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "Authorization"}]}}, "credentials": {"httpHeaderAuth": {"id": "fsJeCNd9BkJ1CIrt", "name": "Head<PERSON> Au<PERSON> account 2"}}, "typeVersion": 4.2}, {"id": "25de6846-9a45-4cb8-8328-b5cb4e36889d", "name": "Generate Kling Video", "type": "n8n-nodes-base.httpRequest", "position": [1600, 200], "parameters": {"url": "https://api.piapi.ai/api/v1/task", "method": "POST", "options": {}, "jsonBody": "={\n    \"model\": \"kling\",\n    \"task_type\": \"video_generation\",\n    \"input\": {\n        \"version\": \"1.6\",\n        \"aspect_ratio\": \"9:16\",\n        \"image_url\":\"{{ $json.image_url }}\",\n\n        \"prompt\": \"An anime character anchored mid-frame, gradually rotating to showcase 3D details\"\n       \n    }\n} ", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "x-api-key"}]}}, "typeVersion": 4.2}, {"id": "410c252e-4901-4159-b8c2-d9a2fe04371d", "name": "Get Video URL", "type": "n8n-nodes-base.httpRequest", "position": [1820, 200], "parameters": {"url": "=https://api.piapi.ai/api/v1/task/{{ $json.data.task_id }}", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-api-key", "value": "72858adea87ad16865d5b0a24c3d9b9f58a6e7b1a8a8a8a0d6b81a9f3a9812f3"}]}}, "typeVersion": 4.2}, {"id": "9a2188d8-8520-485e-99e4-0ff07e4b86e7", "name": "Wait for Image Generation", "type": "n8n-nodes-base.wait", "position": [620, 520], "webhookId": "f3bcf634-8c4b-4bf9-a7f2-d4ee369f5349", "parameters": {}, "typeVersion": 1.1}, {"id": "7f64087a-1567-4c62-a36d-d3b69d933d48", "name": "Check Generation Status", "type": "n8n-nodes-base.if", "position": [600, 280], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a0f8758e-d6fd-44f8-bd79-bc3c4dceddcf", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "completed"}]}}, "typeVersion": 2.2}, {"id": "838999b7-99f5-4f14-bfe2-4886bd565464", "name": "Get Image URL of Midjourney", "type": "n8n-nodes-base.code", "position": [840, 80], "parameters": {"jsCode": "// JavaScript Code for Function Node\nreturn {\n  random_temp_url: $input.all()[0].json.data.output.temporary_image_urls[\n    Math.floor(Math.random() * $input.all()[0].json.data.output.temporary_image_urls.length)\n  ]\n};"}, "typeVersion": 2}, {"id": "09824819-7a6c-4416-ba40-811e9d785678", "name": "Get Image URL of GPT-4o-image", "type": "n8n-nodes-base.code", "position": [1280, 80], "parameters": {"jsCode": "const chunks = $input.first().json.data.split('\\n\\n');\n\nlet imageUrl = null;\n\nfor (let i = chunks.length - 1; i >= 0; i--) {\n    const chunk = chunks[i];\n    \n    if (!chunk.startsWith('data: ')) continue;\n    \n    try {\n        const jsonStr = chunk.substring(6); \n        if (jsonStr.trim() === '[DONE]') continue;\n        \n        const data = JSON.parse(jsonStr);\n        \n    \n        if (data.choices && data.choices[0].delta.content) {\n            const content = data.choices[0].delta.content;\n            const urlMatch = content.match(/!\\[.*?\\]\\((https?:\\/\\/[^\\s]+)\\)/);\n            \n            if (urlMatch && urlMatch[1]) {\n                imageUrl = urlMatch[1];\n                break;\n            }\n        }\n    } catch (e) {\n        continue;\n    }\n}\n\nreturn {\n    image_url: imageUrl,\n    finish_reason: imageUrl ? \"success\" : \"not_found\"\n};"}, "typeVersion": 2}, {"id": "22c8f566-6a27-4773-b4ab-45be2fbad8de", "name": "Check Generation Status of GPT-4o", "type": "n8n-nodes-base.if", "position": [1260, 320], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "08a2ebe6-dc95-4b8a-ada1-1173645cc3f4", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.finish_reason }}", "rightValue": "not_found"}]}}, "typeVersion": 2.2}, {"id": "00ce5488-e96c-4ff1-8727-ed2d787ae0f5", "name": "Check Video Generation Status", "type": "n8n-nodes-base.if", "position": [2000, 200], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "f36fa981-22e0-46db-af8c-c2ac55242c27", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "completed"}, {"id": "637ea756-1ad9-434c-b6b2-b100ee4c3cad", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "6d53cac4-b35b-41b1-b60a-6faba78b4439", "name": "Wait for Video Generation", "type": "n8n-nodes-base.wait", "position": [1960, 420], "webhookId": "c7b2590d-96a3-4c7c-8821-3023fead254b", "parameters": {"amount": 20}, "typeVersion": 1.1}, {"id": "4cd00e7b-bfbd-45bc-b775-401055921b45", "name": "Fetch Final Video URL", "type": "n8n-nodes-base.code", "position": [2300, 300], "parameters": {"jsCode": "// Process the entire response\nreturn {\n  video_url: $input.all()[0].json.data.output.video_url,\n  watermark_free_url: $input.all()[0].json.data.output.works[0].video.resource_without_watermark\n};"}, "typeVersion": 2}, {"id": "617d6647-4c49-4e7f-91f3-5fd6d5e95fe7", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [340, -220], "parameters": {"width": 460, "height": 220, "content": "## General 3D Presentation\nThis workflow creates 360° or 180° spinning videos of high-quality 3D models with [PiAPI](https://piapi.ai) API. 🙋\n### Required Instruction: \n1. Fill in params of Prompt node.\n2. Fill in x-api-key in Mijdourney Generator node and Generate Kling Video node, fill in Header Parameters of GPT-4o Image Generator (e.g., Bearer + your X-API-Key)"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "c6a9b53c-8d6c-4ffa-b95c-e7cf88734cc9", "connections": {"Prompt": {"main": [[{"node": "Midjourney Generator", "type": "main", "index": 0}]]}, "Get Video URL": {"main": [[{"node": "Check Video Generation Status", "type": "main", "index": 0}]]}, "Generate Kling Video": {"main": [[{"node": "Get Video URL", "type": "main", "index": 0}]]}, "Midjourney Generator": {"main": [[{"node": "Check Generation Status", "type": "main", "index": 0}]]}, "GPT-4o Image Generator": {"main": [[{"node": "Get Image URL of GPT-4o-image", "type": "main", "index": 0}]]}, "Check Generation Status": {"main": [[{"node": "Get Image URL of Midjourney", "type": "main", "index": 0}], [{"node": "Wait for Image Generation", "type": "main", "index": 0}]]}, "Wait for Image Generation": {"main": [[{"node": "Midjourney Generator", "type": "main", "index": 0}]]}, "Wait for Video Generation": {"main": [[{"node": "Get Video URL", "type": "main", "index": 0}]]}, "Get Image URL of Midjourney": {"main": [[{"node": "GPT-4o Image Generator", "type": "main", "index": 0}]]}, "Check Video Generation Status": {"main": [[{"node": "Fetch Final Video URL", "type": "main", "index": 0}], [{"node": "Wait for Video Generation", "type": "main", "index": 0}]]}, "Get Image URL of GPT-4o-image": {"main": [[{"node": "Check Generation Status of GPT-4o", "type": "main", "index": 0}]]}, "Check Generation Status of GPT-4o": {"main": [[{"node": "GPT-4o Image Generator", "type": "main", "index": 0}], [{"node": "Generate Kling Video", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Prompt", "type": "main", "index": 0}]]}}}