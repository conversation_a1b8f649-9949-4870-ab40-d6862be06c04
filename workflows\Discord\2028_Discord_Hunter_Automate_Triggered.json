{"id": "yYjRbTWULZuNLXM0", "meta": {"instanceId": "616c00803b706b71f395da00f933102e3b493591ba0a653e82d0b9ed360368da"}, "name": "My workflow", "tags": [], "nodes": [{"id": "2125c56b-1c76-4219-847b-470f11865c01", "name": "n8n Form Trigger", "type": "n8n-nodes-base.formTrigger", "position": [180, 300], "webhookId": "5fb20488-aa11-4788-aa0f-73d40e7e4475", "parameters": {"path": "form", "options": {}, "formTitle": "Form Title", "formFields": {"values": [{"fieldLabel": "Name", "requiredField": true}, {"fieldLabel": "Email", "requiredField": true}, {"fieldType": "textarea", "fieldLabel": "Let us know your queries"}]}}, "typeVersion": 2}, {"id": "94f6684f-925b-4ded-a79f-ff44771ee992", "name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [1220, 280], "parameters": {"columns": {"value": {"Name": "={{ $json.Name }}", "Email": "={{ $json.Email }}", "Query": "={{ $json['Let us know your queries'] }}", "Submitted On": "={{ $json.submittedAt }}"}, "schema": [{"id": "Name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Email", "type": "string", "display": true, "required": false, "displayName": "Email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Query", "type": "string", "display": true, "required": false, "displayName": "Query", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Submitted On", "type": "string", "display": true, "required": false, "displayName": "Submitted On", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["Name"]}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1zvlIZNAVFZ7lg9hch_zsNEIbmAhInUuwhiK2zWq0snA/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1zvlIZNAVFZ7lg9hch_zsNEIbmAhInUuwhiK2zWq0snA", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1zvlIZNAVFZ7lg9hch_zsNEIbmAhInUuwhiK2zWq0snA/edit?usp=drivesdk", "cachedResultName": "Leads Data"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "7HR3jwkVoNgbw7fb", "name": "Google Sheets account"}}, "typeVersion": 4.2}, {"id": "4a1d8a68-c976-4bf6-956a-6a29affdaed4", "name": "Gmail", "type": "n8n-nodes-base.gmail", "position": [1220, -40], "parameters": {"sendTo": "<EMAIL>", "message": "=Name:   {{ $json.Name }} \n\nEmail:  {{ $json.Email }} \n\nQuery:  {{ $json['Let us know your queries'] }} \n\nSubmitted on:  {{ $json.submittedAt }}", "options": {}, "subject": "=New lead from {{ $json.Name }}", "emailType": "text"}, "credentials": {"gmailOAuth2": {"id": "DrjEhQ0S42VeRofT", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "126d0ee3-de81-41ed-88f6-ffdeefae5576", "name": "Discord", "type": "n8n-nodes-base.discord", "position": [1240, 620], "parameters": {"embeds": {"values": [{"color": "#FF00F2", "title": "=New Lead from  {{ $json.Name }}", "author": "N8N Automation", "description": "=Name:   {{ $json.Name }} \n\nEmail:  {{ $json.Email }} \n\nQuery:  {{ $json['Let us know your queries'] }} \n\nSubmitted on:  {{ $json.submittedAt }}"}]}, "content": "=", "options": {}, "authentication": "webhook"}, "credentials": {"discordWebhookApi": {"id": "kuEJsXFqZfG48TDJ", "name": "Discord Webhook account"}}, "typeVersion": 2}, {"id": "4cd07d01-6d9a-4d0a-9999-9d66d99fb624", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1080, -100], "parameters": {"width": 379.**************, "height": 211.*************, "content": "make sure to add To address so you can receive the notifications"}, "typeVersion": 1}, {"id": "4e8eebfa-df98-473c-8666-c7768f641694", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1070, 520], "parameters": {"width": 399.*************, "height": 246.**************, "content": "Sometimes the email might not reach your inbox, but it rarely happens but if you receive a lot of leads it's better to setup discord webhook and receive updates that way so that your inbox doesn't get filled with all the leads"}, "typeVersion": 1}, {"id": "caff8f87-4e07-4125-bfd7-62a912b4ada9", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1080, 220], "parameters": {"width": 377.*************, "height": 211.*************, "content": "Map the data to it's relevant fields/columns"}, "typeVersion": 1}, {"id": "c5e320e3-6489-4957-bb4e-e9873d001a66", "name": "If", "type": "n8n-nodes-base.if", "position": [640, 300], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "d8c112a3-377c-4ca2-90d9-05c19f895ddb", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.Email }}", "rightValue": "="}]}}, "typeVersion": 2}, {"id": "778ba29f-ed75-4706-830f-d906d28d45e3", "name": "<PERSON>", "type": "n8n-nodes-base.hunter", "position": [420, 300], "parameters": {"email": "={{ $json.Email }}", "operation": "emailVerifier"}, "typeVersion": 1}, {"id": "0021001b-0784-4983-a419-8bb491004133", "name": "No Operation, do nothing", "type": "n8n-nodes-base.noOp", "position": [640, 500], "parameters": {}, "typeVersion": 1}, {"id": "997da82a-618f-417a-be73-dd3cc0c70ee8", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [380, 219.7136799847175], "parameters": {"color": 4, "width": 456.2047033929433, "height": 435.9183833776615, "content": "Use this only if you receive high volume of leads and you want to avoid fake leads with fake emails"}, "typeVersion": 1}, {"id": "9b764ce3-66b5-44ff-8086-28812bc79db1", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [520, 440], "parameters": {"color": 3, "width": 314.12732687758046, "height": 209.4182179183868, "content": "Doesn't move forward if the email is not valid or if its fake email address"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1", "saveManualExecutions": true}, "versionId": "6455a6bd-0749-4c00-805b-a04ea6e34cc7", "connections": {"If": {"main": [[{"node": "Gmail", "type": "main", "index": 0}, {"node": "Google Sheets", "type": "main", "index": 0}, {"node": "Discord", "type": "main", "index": 0}], [{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "Gmail": {"main": [[]]}, "Hunter": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Discord": {"main": [[]]}, "n8n Form Trigger": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}}}