{"nodes": [{"id": "03301645-75e3-480f-bf06-d015fa252d7b", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-360, 260], "parameters": {}, "typeVersion": 1}, {"id": "88ac5990-1e33-404f-93c1-42355f3366e7", "name": "Set Loop", "type": "n8n-nodes-base.set", "position": [380, 260], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "51ce4f05-471b-4948-8bb7-da8baad394af", "name": "loop_max", "type": "number", "value": "={{ $json.meta.numItems/100 }}"}, {"id": "b8338050-c49f-4e9c-b7fc-b2074acd475a", "name": "loop_count", "type": "number", "value": "=0"}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "aff9eb6b-7d66-4eff-be8d-565ab6076a79", "name": "If", "type": "n8n-nodes-base.if", "position": [600, 260], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "f129d508-c97f-428e-83ee-1a47e1d10574", "operator": {"type": "number", "operation": "lt"}, "leftValue": "={{ $json.loop_count }}", "rightValue": "={{ $json.loop_max }}"}]}}, "typeVersion": 2.1}, {"id": "4ac0a39d-f6bf-487f-9dab-1898b18bd7a8", "name": "User ID", "type": "n8n-nodes-base.set", "notes": "Get from Zotero Web > Settings > Security:\n\nhttps://www.zotero.org/settings/security", "position": [-180, 260], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "2917cc56-2714-4f41-a394-0bb7a1cb788e", "name": "userid", "type": "string", "value": "FILL WITH USER ID"}]}}, "notesInFlow": true, "typeVersion": 3.4}, {"id": "30840c66-4efb-425f-b9ba-dbd053b594c1", "name": "Collections", "type": "n8n-nodes-base.httpRequest", "position": [0, 260], "parameters": {"url": "=https://api.zotero.org/users/{{ $json.userid }}/collections?v=3", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "typeVersion": 4.2}, {"id": "20ad21b1-d506-42ac-b14c-8b6b47ca60e9", "name": "Loop Discount", "type": "n8n-nodes-base.set", "position": [780, 420], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "51ce4f05-471b-4948-8bb7-da8baad394af", "name": "={{ $json.loop_count++ }}", "type": "number", "value": "="}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "bb56455d-eeb7-4a46-8589-efa052ed3e0c", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [1660, 260], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "0131ab75-e0f9-4a8d-9380-315f45c3590d", "name": "key", "type": "string", "value": "={{ $json.key }}"}, {"id": "13dc0799-fce8-4764-b50f-811fb0e64405", "name": "data.title", "type": "string", "value": "={{ $json.data.title }}"}, {"id": "f11fcb34-ec1b-4ac7-8939-481e5ffc4fe4", "name": "meta.creatorSummary", "type": "string", "value": "={{ $json.meta.creatorSummary }}"}]}}, "typeVersion": 3.4}, {"id": "30fc5d25-9500-416f-b1e9-9f0c879eb1bd", "name": "Select Collection", "type": "n8n-nodes-base.filter", "notes": "Select Collection", "position": [200, 260], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "9231d49f-03a0-40da-8daf-12d931284214", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.key }}", "rightValue": "FILL WITH COLLECTION KEY"}]}}, "typeVersion": 2.1}, {"id": "aebbfdcf-58ad-4b69-add9-aac2d98393cb", "name": "Filter", "type": "n8n-nodes-base.filter", "position": [1440, 260], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "5f9095f1-4ec9-4e94-bd95-b18d0b9543b0", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $('Get Articles').item.json.key }}", "rightValue": ""}]}}, "typeVersion": 2.1}, {"id": "f4fb234c-c711-493f-ab84-7d5a66b123c9", "name": "Get Articles", "type": "n8n-nodes-base.httpRequest", "position": [920, 240], "parameters": {"url": "=https://api.zotero.org/users/{{ $('User ID').item.json.userid }}/collections/{{ $json.key }}/items?start={{ $json.loop_count*100 }}&limit={{ $json.meta.numItems-100*$json.loop_count }}", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "typeVersion": 4.2}, {"id": "1c65de0f-6f7c-435e-af18-1c06a1d60cb2", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-180, 20], "parameters": {"width": 150, "height": 209.09090909090907, "content": "Get from Zotero Web > Settings > Security:\n\nhttps://www.zotero.org/settings/security\n"}, "typeVersion": 1}, {"id": "893af76c-c6b0-492e-aaba-277c952d3c0d", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [0, -20], "parameters": {"width": 150, "height": 233, "content": "On the same page, create an Application Key to setup the Header Auth inside the Collections Node:\nhttps://www.zotero.org/settings/security\n\nUse `Zotero-API-Key` as Header name"}, "typeVersion": 1}, {"id": "b2068109-a36e-48e7-919d-1782a61a17f0", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [180, 20], "parameters": {"width": 150, "height": 189.99999999999994, "content": "See the \"Table\" results, of previous nodes and replace the second value of \"IS EQUAL TO\" with your Collection KEY"}, "typeVersion": 1}, {"id": "418328ed-4b37-426c-b018-756636c2fd29", "name": "Merge 100+", "type": "n8n-nodes-base.merge", "position": [1220, 260], "parameters": {}, "typeVersion": 3}, {"id": "690d9498-c01e-4cfd-8be1-fce239d0c37c", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1440, 140], "parameters": {"width": 150, "height": 80, "content": "Optional Filter for Results"}, "typeVersion": 1}, {"id": "35efb061-d480-40f6-8118-8d48d0dbe67c", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1660, 140], "parameters": {"width": 150, "height": 80, "content": "Optional Edit Fields for Results"}, "typeVersion": 1}], "pinData": {}, "connections": {"If": {"main": [[{"node": "Get Articles", "type": "main", "index": 0}, {"node": "Loop Discount", "type": "main", "index": 0}]]}, "Filter": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "User ID": {"main": [[{"node": "Collections", "type": "main", "index": 0}]]}, "Set Loop": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Merge 100+": {"main": [[{"node": "Filter", "type": "main", "index": 0}]]}, "Collections": {"main": [[{"node": "Select Collection", "type": "main", "index": 0}]]}, "Get Articles": {"main": [[{"node": "Merge 100+", "type": "main", "index": 0}]]}, "Loop Discount": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Select Collection": {"main": [[{"node": "Set Loop", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "User ID", "type": "main", "index": 0}]]}}}