{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "Mailjet", "type": "n8n-nodes-base.mailjet", "position": [450, 300], "parameters": {"text": "This is a test message", "subject": "Sample Subject", "toEmail": "<EMAIL>", "fromEmail": "<EMAIL>", "additionalFields": {}}, "credentials": {"mailjetEmailApi": "mailjet creds"}, "typeVersion": 1}], "connections": {"On clicking 'execute'": {"main": [[{"node": "Mailjet", "type": "main", "index": 0}]]}}}