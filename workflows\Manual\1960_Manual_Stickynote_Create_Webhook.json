{"id": "q1DorytEoEw1QLGj", "meta": {"instanceId": "885b4fb4a6a9c2cb5621429a7b972df0d05bb724c20ac7dac7171b62f1c7ef40", "templateCredsSetupCompleted": true}, "name": "Generate Company Stories from LinkedIn with Bright Data & Google Gemini", "tags": [{"id": "ddPkw7Hg5dZhQu2w", "name": "AI", "createdAt": "2025-04-13T05:38:08.053Z", "updatedAt": "2025-04-13T05:38:08.053Z"}, {"id": "rKOa98eAi3IETrLu", "name": "HR", "createdAt": "2025-04-13T04:59:30.580Z", "updatedAt": "2025-04-13T04:59:30.580Z"}], "nodes": [{"id": "1424195e-79ec-48e8-9bb6-fbae072aca81", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-1440, 245], "parameters": {}, "typeVersion": 1}, {"id": "509519c2-efe9-4191-87af-9c5c782350d6", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "notes": "Gemini Experimental Model", "position": [696, 540], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-thinking-exp-01-21"}, "credentials": {"googlePalmApi": {"id": "YeO7dHZnuGBVQKVZ", "name": "Google Gemini(PaLM) Api account"}}, "notesInFlow": true, "typeVersion": 1}, {"id": "3be8be65-38c2-4500-8676-925bdf7844ac", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [816, 542.5], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "65b72f55-6424-487b-a622-879589d43344", "name": "Recursive Character Text Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "position": [904, 740], "parameters": {"options": {}, "chunkOverlap": 100}, "typeVersion": 1}, {"id": "4ab31927-5372-4a8f-83b5-355bcd6eaae2", "name": "If", "type": "n8n-nodes-base.if", "position": [-340, 170], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "6a7e5360-4cb5-4806-892e-5c85037fa71c", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $('Check Snapshot Status').item.json.status }}", "rightValue": "ready"}]}}, "typeVersion": 2.2}, {"id": "30382d3b-6ba8-4a96-93ce-9d22fc547793", "name": "Set Snapshot Id", "type": "n8n-nodes-base.set", "position": [-780, 245], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "2c3369c6-9206-45d7-9349-f577baeaf189", "name": "snapshot_id", "type": "string", "value": "={{ $json.snapshot_id }}"}]}}, "typeVersion": 3.4}, {"id": "a4867b6f-fa91-4b83-befc-9ce97c10228c", "name": "Download Snapshot", "type": "n8n-nodes-base.httpRequest", "position": [100, 120], "parameters": {"url": "=https://api.brightdata.com/datasets/v3/snapshot/{{ $json.snapshot_id }}", "options": {"timeout": 10000}, "sendQuery": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "queryParameters": {"parameters": [{"name": "format", "value": "json"}]}}, "credentials": {"httpHeaderAuth": {"id": "kdbqXuxIR8qIxF7y", "name": "Header Auth account"}}, "typeVersion": 4.2}, {"id": "16580d94-23fc-45d6-a282-640148b602d3", "name": "Set LinkedIn URL", "type": "n8n-nodes-base.set", "position": [-1220, 245], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "47f839a1-df2a-4972-9dad-597a8af0bf75", "name": "url", "type": "string", "value": "https://il.linkedin.com/company/bright-data"}]}}, "typeVersion": 3.4}, {"id": "be007904-269a-4823-bdd8-1ba5b4f69f5c", "name": "Google Gemini Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [408, 340], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "YeO7dHZnuGBVQKVZ", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "56a08c75-5122-483e-af0e-da1dd3e08eaf", "name": "Check on the errors", "type": "n8n-nodes-base.if", "position": [-120, 120], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "b267071c-7102-407b-a98d-f613bcb1a106", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.errors.toString() }}", "rightValue": "0"}]}}, "typeVersion": 2.2}, {"id": "6925a606-1108-4605-9124-c74d3df555ac", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1420, -100], "parameters": {"width": 400, "height": 280, "content": "## Note\n\nDeals with the LinkedIn data extraction using the Bright Data Web Scrapper API.\n\nThe information extraction and summarization are being used to demonstrate the usage of the N8N AI capabilities.\n\n**Please make sure to set the LinkedIn URL and Webhook Notification URL**"}, "typeVersion": 1}, {"id": "a5f977db-14e5-4652-b2d3-0a1b0470be9a", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-940, -100], "parameters": {"width": 420, "height": 280, "content": "## LLM Usages\n\nGoogle Gemini Flash Exp model is being used.\n\nInformation extraction is being used for formatting the LinkedIn response to produce a story.\n\nSummarization Chain is being used for summarization of the content"}, "typeVersion": 1}, {"id": "ae6377e2-6ca0-4218-affd-d3c81c16d996", "name": "Perform LinkedIn Web Request", "type": "n8n-nodes-base.httpRequest", "position": [-1000, 245], "parameters": {"url": "https://api.brightdata.com/datasets/v3/trigger", "method": "POST", "options": {}, "jsonBody": "=[\n  {\n    \"url\": \"{{ $json.url }}\"\n  }\n]", "sendBody": true, "sendQuery": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "queryParameters": {"parameters": [{"name": "dataset_id", "value": "gd_l1vikfnt1wgvvqz95w"}, {"name": "include_errors", "value": "true"}]}, "headerParameters": {"parameters": [{}]}}, "credentials": {"httpHeaderAuth": {"id": "kdbqXuxIR8qIxF7y", "name": "Header Auth account"}}, "typeVersion": 4.2}, {"id": "9a1e8d92-24a9-481c-b81f-5e37bca46fe2", "name": "Check Snapshot Status", "type": "n8n-nodes-base.httpRequest", "position": [-560, 245], "parameters": {"url": "=https://api.brightdata.com/datasets/v3/progress/{{ $json.snapshot_id }}", "options": {}, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{}]}}, "credentials": {"httpHeaderAuth": {"id": "kdbqXuxIR8qIxF7y", "name": "Header Auth account"}}, "typeVersion": 4.2}, {"id": "543d6087-c1d8-4f98-9b7c-fedbce9b0215", "name": "LinkedIn Data Extractor", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [320, 120], "parameters": {"text": "=Write a complete story of the provided company information in JSON. Use the following Company info to produce a story or a blog post. Make sure to incorporate all the provided company context.\n\nHere's the Company Info in JSON - {{ $json.input }}", "options": {"systemPromptTemplate": "You are an expert data formatter"}, "attributes": {"attributes": [{"name": "company_story", "required": true, "description": "Detailed Company Info"}]}}, "typeVersion": 1}, {"id": "d07c83f0-5adf-4d5a-976a-b344aa8a853e", "name": "Concise Summary Generator", "type": "@n8n/n8n-nodes-langchain.chainSummarization", "position": [712, 320], "parameters": {"options": {"summarizationMethodAndPrompts": {"values": {"prompt": "=Write a concise summary of the following:\n\n\n{{ $json.output.company_story }}\n\n", "combineMapPrompt": "=Write a concise summary of the following:\n\n\n\n\n\nCONCISE SUMMARY: {{ $json.output.company_story }}"}}}, "operationMode": "documentLoader"}, "typeVersion": 2}, {"id": "0867753e-c3ab-473e-960a-344573cdde29", "name": "Webhook Notifier for Data Extractor", "type": "n8n-nodes-base.httpRequest", "position": [834, -80], "parameters": {"url": "https://webhook.site/ce41e056-c097-48c8-a096-9b876d3abbf7", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "response", "value": "={{ $json.output }}"}]}}, "typeVersion": 4.2}, {"id": "d666cbb8-64bf-47b9-802a-d78ed5caa128", "name": "Webhook Notifier for Summary Generator", "type": "n8n-nodes-base.httpRequest", "position": [1192, 320], "parameters": {"url": "https://webhook.site/ce41e056-c097-48c8-a096-9b876d3abbf7", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "response", "value": "={{ $json.response.text }}"}]}}, "typeVersion": 4.2}, {"id": "fbd962be-5003-4039-b17e-fc0f16c2edf7", "name": "Wait for 30 seconds", "type": "n8n-nodes-base.wait", "position": [-120, 345], "webhookId": "f2aafd71-61f2-4aa4-8290-fa3bbe3d46b9", "parameters": {"amount": 30}, "typeVersion": 1.1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "0f4279a9-1593-421e-825e-850cdae1bb97", "connections": {"If": {"main": [[{"node": "Check on the errors", "type": "main", "index": 0}], [{"node": "Wait for 30 seconds", "type": "main", "index": 0}]]}, "Set Snapshot Id": {"main": [[{"node": "Check Snapshot Status", "type": "main", "index": 0}]]}, "Set LinkedIn URL": {"main": [[{"node": "Perform LinkedIn Web Request", "type": "main", "index": 0}]]}, "Download Snapshot": {"main": [[{"node": "LinkedIn Data Extractor", "type": "main", "index": 0}]]}, "Check on the errors": {"main": [[{"node": "Download Snapshot", "type": "main", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Concise Summary Generator", "type": "ai_document", "index": 0}]]}, "Wait for 30 seconds": {"main": [[{"node": "Check Snapshot Status", "type": "main", "index": 0}]]}, "Check Snapshot Status": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "LinkedIn Data Extractor": {"main": [[{"node": "Concise Summary Generator", "type": "main", "index": 0}, {"node": "Webhook Notifier for Data Extractor", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Concise Summary Generator", "type": "ai_languageModel", "index": 0}]]}, "Concise Summary Generator": {"main": [[{"node": "Webhook Notifier for Summary Generator", "type": "main", "index": 0}]]}, "Google Gemini Chat Model1": {"ai_languageModel": [[{"node": "LinkedIn Data Extractor", "type": "ai_languageModel", "index": 0}]]}, "Perform LinkedIn Web Request": {"main": [[{"node": "Set Snapshot Id", "type": "main", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set LinkedIn URL", "type": "main", "index": 0}]]}}}