{"id": "65", "name": "Get Product Feedback", "nodes": [{"name": "Typeform Trigger", "type": "n8n-nodes-base.typeformTrigger", "position": [170, 260], "webhookId": "0cf82c15-eeb8-4b24-bd67-5f4b54a58b6d", "parameters": {"formId": ""}, "credentials": {"typeformApi": "typeform-harshil"}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [370, 260], "parameters": {"values": {"number": [{"name": "Score", "value": "={{$node[\"Typeform Trigger\"].json[\"What score would you like to give?\"]}}"}], "string": [{"name": "Name", "value": "={{$node[\"Typeform Trigger\"].json[\"What is your name?\"]}}"}, {"name": "Email", "value": "={{$node[\"Typeform Trigger\"].json[\"What is your email address?\"]}}"}, {"name": "Description", "value": "={{$node[\"Typeform Trigger\"].json[\"Anything else you want to share?\"]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "Airtable", "type": "n8n-nodes-base.airtable", "position": [570, 260], "parameters": {"table": "<PERSON><PERSON><PERSON>", "options": {}, "operation": "append", "application": ""}, "credentials": {"airtableApi": "airtable-harshil"}, "typeVersion": 1}, {"name": "IF", "type": "n8n-nodes-base.if", "position": [770, 260], "parameters": {"conditions": {"number": [{"value1": "={{$node[\"Set\"].json[\"Score\"]}}", "value2": 7}]}}, "typeVersion": 1}, {"name": "Trello", "type": "n8n-nodes-base.trello", "position": [970, 160], "parameters": {"name": "=[{{$node[\"IF\"].json[\"fields\"][\"Score\"]}}] {{$node[\"IF\"].json[\"fields\"][\"Name\"]}}", "listId": "5fbb9e2eb1d5cc0a8a7ab8ac", "description": "=Name: {{$node[\"IF\"].json[\"fields\"][\"Name\"]}}\nEmail: {{$node[\"IF\"].json[\"fields\"][\"Email\"]}}\nScore: {{$node[\"IF\"].json[\"fields\"][\"Score\"]}}\nDescription: {{$node[\"IF\"].json[\"fields\"][\"Description\"]}}", "additionalFields": {}}, "credentials": {"trelloApi": "Trello Credentials"}, "typeVersion": 1}, {"name": "NoOp", "type": "n8n-nodes-base.noOp", "position": [970, 360], "parameters": {}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"IF": {"main": [[{"node": "Trello", "type": "main", "index": 0}], [{"node": "NoOp", "type": "main", "index": 0}]]}, "Set": {"main": [[{"node": "Airtable", "type": "main", "index": 0}]]}, "Airtable": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}, "Typeform Trigger": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}}