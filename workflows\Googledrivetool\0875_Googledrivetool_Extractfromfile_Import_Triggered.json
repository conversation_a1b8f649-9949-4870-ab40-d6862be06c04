{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "fb5b682b-5e30-497e-b465-c3369bb3c2e3", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-32, -20], "parameters": {"color": 7, "width": 680, "height": 660, "content": "## 1. Set up an MCP Server Trigger\n[Read more about the MCP Server Trigger](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-langchain.mcptrigger)"}, "typeVersion": 1}, {"id": "cfc2c7f1-a6ee-42a9-b955-e5bce012b6e1", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-40, -160], "parameters": {"color": 5, "width": 380, "height": 100, "content": "### Always Authenticate Your Server!\nBefore going to production, it's always advised to enable authentication on your MCP server trigger."}, "typeVersion": 1}, {"id": "79586d35-0582-4da8-91da-5bc8451c2089", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [800, 360], "parameters": {"workflowInputs": {"values": [{"name": "operation"}, {"name": "folderId"}, {"name": "fileId"}]}}, "typeVersion": 1.1}, {"id": "02aee033-58e8-4f33-a18d-b872840e81d8", "name": "Google Drive MCP Server", "type": "@n8n/n8n-nodes-langchain.mcpTrigger", "position": [160, 160], "webhookId": "a289c719-fb71-4b08-97c6-79d12645dc7e", "parameters": {"path": "a289c719-fb71-4b08-97c6-79d12645dc7e"}, "typeVersion": 1}, {"id": "e0e50653-d98a-4ad4-a2ed-e1b73332c380", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [680, -20], "parameters": {"color": 7, "width": 1340, "height": 860, "content": "## 2. Handle Multiple Binary Formats via Conversion and AI\n[Read more about the PostgreSQL Node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.postgres/)\n\nMCP clients (or rather, the AI agents) still expect and require text responses from our MCP server.\nN8N can provide the right conversion tools to parse most text formats such as PDF, CSV and XML.\nFor images, audio and video, consider using multimodal LLMs to describe or transcribe the file instead."}, "typeVersion": 1}, {"id": "6be1ff49-5edc-42d2-87de-09d207ee7733", "name": "Download File1", "type": "n8n-nodes-base.googleDrive", "position": [1160, 360], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $json.fileId }}"}, "options": {"googleFileConversion": {"conversion": {"docsToFormat": "text/plain", "slidesToFormat": "application/pdf"}}}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "yOwz41gMQclOadgu", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "91b0a549-0494-48a1-bdf3-6c2b91409d01", "name": "FileType", "type": "n8n-nodes-base.switch", "position": [1340, 320], "parameters": {"rules": {"values": [{"outputKey": "pdf", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "7b6958ce-d553-4379-a5d6-743f39b342d0", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $binary.data.mimeType }}", "rightValue": "application/pdf"}]}, "renameOutput": true}, {"outputKey": "csv", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "d0816a37-ac06-49e3-8d63-17fcd061e33f", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $binary.data.mimeType }}", "rightValue": "text/csv"}]}, "renameOutput": true}, {"outputKey": "image", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "589540e1-1439-41e3-ba89-b27f5e936190", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{\n[\n  'image/jpeg',\n  'image/jpg',\n  'image/png',\n  'image/gif'\n].some(mimeType => $binary.data.mimeType === mimeType)\n}}", "rightValue": ""}]}, "renameOutput": true}, {"outputKey": "audio", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "b8fc61a1-6057-4db3-960e-b8ddcbdd0f31", "operator": {"type": "string", "operation": "contains"}, "leftValue": "={{ $binary.data.mimeType }}", "rightValue": "audio"}]}, "renameOutput": true}, {"outputKey": "video", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "959d65a6-372f-4978-b2d1-f28aa1e372c6", "operator": {"type": "string", "operation": "contains"}, "leftValue": "={{ $binary.data.mimeType }}", "rightValue": "video"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "d88ed202-1121-41db-859d-b31d53d46292", "name": "Operation", "type": "n8n-nodes-base.switch", "position": [980, 360], "parameters": {"rules": {"values": [{"outputKey": "ReadFile", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "b03bb746-dc4e-469c-b8e6-a34c0aa8d0a6", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.operation }}", "rightValue": "readFile"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "7e8791e6-24c2-441a-8efb-7f4375f2519b", "name": "Extract from PDF", "type": "n8n-nodes-base.extractFromFile", "position": [1620, 80], "parameters": {"options": {}, "operation": "pdf"}, "typeVersion": 1}, {"id": "2b33623c-cea4-4a83-80ef-f852b9a3d126", "name": "Extract from CSV", "type": "n8n-nodes-base.extractFromFile", "position": [1620, 260], "parameters": {"options": {"encoding": "utf-8", "headerRow": false, "relaxQuotes": true, "includeEmptyCells": true}}, "typeVersion": 1}, {"id": "6ca2542d-225e-4a65-b5ce-3edafb11379c", "name": "Get PDF Response", "type": "n8n-nodes-base.set", "position": [1780, 80], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "a481cde3-b8ec-4d97-aa13-4668bd66c24d", "name": "response", "type": "string", "value": "={{ $json.text }}"}]}}, "typeVersion": 3.4}, {"id": "3d1c4aa6-cac1-4957-ab7e-3134368e4b53", "name": "Get CSV Response", "type": "n8n-nodes-base.set", "position": [1780, 260], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "a481cde3-b8ec-4d97-aa13-4668bd66c24d", "name": "response", "type": "string", "value": "={{\n$input.all()\n  .map(item => item.json.row.map(cell => `\"${cell}\"`).join(','))\n  .join('\\n')\n}}"}]}}, "executeOnce": true, "typeVersion": 3.4}, {"id": "141444f9-e937-41f9-ab97-09624646ddba", "name": "Read File From GDrive", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [400, 380], "parameters": {"name": "ReadFile", "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "description": "Call this tool to download and read the contents of a file within google drive.", "workflowInputs": {"value": {"fileId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('fileId', ``, 'string') }}", "folderId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('folderId', ``, 'string') }}", "operation": "readFile"}, "schema": [{"id": "operation", "type": "string", "display": true, "removed": false, "required": false, "displayName": "operation", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "folderId", "type": "string", "display": true, "removed": false, "required": false, "displayName": "folderId", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "fileId", "type": "string", "display": true, "removed": false, "required": false, "displayName": "fileId", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2.1}, {"id": "b5851527-0b57-447b-ac8c-10408a684862", "name": "Search Files from Gdrive", "type": "n8n-nodes-base.googleDriveTool", "position": [240, 380], "parameters": {"limit": 10, "filter": {"driveId": {"mode": "list", "value": "My Drive"}, "whatToSearch": "files"}, "options": {}, "resource": "fileFolder", "queryString": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Search_Query', ``, 'string') }}"}, "credentials": {"googleDriveOAuth2Api": {"id": "yOwz41gMQclOadgu", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "98197c91-c7e9-4fbb-a2b1-c16c873fa0a1", "name": "Analyse Image", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1620, 440], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "resource": "image", "inputType": "base64", "operation": "analyze"}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "b44a787a-c670-47e1-b87e-d880425ce610", "name": "Transcribe Audio", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1620, 620], "parameters": {"options": {}, "resource": "audio", "operation": "transcribe"}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "1e1a358d-769e-48c9-bf27-6a3cfaaacb14", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-500, -420], "parameters": {"width": 440, "height": 1060, "content": "## Try It Out!\n### This n8n demonstrates how to build a simple Google Drive MCP server to search and get contents of files from Google Drive.\n\nThis MCP example is based off an official MCP reference implementation which can be found here -https://github.com/modelcontextprotocol/servers/tree/main/src/gdrive\n\n### How it works\n* A MCP server trigger is used and connected to 1x Google Drive tool and 1x Custom Workflow tool.\n* The Google Drive tool is set to perform a search on files within our Google Drive folder.\n* The Custom Workflow tool downloads target files found in our drive and converts the binaries to their text representation. Eg. PDFs have only their text contents extracted and returned to the MCP client.\n\n### How to use\n* This Google Drive MCP server allows any compatible MCP client to manage a person or shared Google Drive. Simple select a drive or for better control, specify a folder within the drive to scope the operations to.\n* Connect your MCP client by following the n8n guidelines here - https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-langchain.mcptrigger/#integrating-with-claude-desktop\n* Try the following queries in your MCP client:\n  * \"Please help me search for last month's expense reports.\"\n * \"What does the company policy document say about cancellations and refunds?\"\n\n### Requirements\n* Google Drive for documents.\n* OpenAI for image and audio understanding.\n* MCP Client or Agent for usage such as <PERSON> - https://claude.ai/download\n\n### Customising this workflow\n* Add additional capabilities such as renaming, moving and/or deleting files.\n* Remember to set the MCP server to require credentials before going to production and sharing this MCP server with others!"}, "typeVersion": 1}], "pinData": {}, "connections": {"FileType": {"main": [[{"node": "Extract from PDF", "type": "main", "index": 0}], [{"node": "Extract from CSV", "type": "main", "index": 0}], [{"node": "Analyse Image", "type": "main", "index": 0}], [{"node": "Transcribe Audio", "type": "main", "index": 0}], []]}, "Operation": {"main": [[{"node": "Download File1", "type": "main", "index": 0}]]}, "Download File1": {"main": [[{"node": "FileType", "type": "main", "index": 0}]]}, "Extract from CSV": {"main": [[{"node": "Get CSV Response", "type": "main", "index": 0}]]}, "Extract from PDF": {"main": [[{"node": "Get PDF Response", "type": "main", "index": 0}]]}, "Read File From GDrive": {"ai_tool": [[{"node": "Google Drive MCP Server", "type": "ai_tool", "index": 0}]]}, "Search Files from Gdrive": {"ai_tool": [[{"node": "Google Drive MCP Server", "type": "ai_tool", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Operation", "type": "main", "index": 0}]]}}}