{"meta": {"instanceId": "f0a68da631efd4ed052a324b63ff90f7a844426af0398a68338f44245d1dd9e5"}, "nodes": [{"id": "0d901abb-f11b-4fdc-88d0-1bbd906ff332", "name": "Split results", "type": "n8n-nodes-base.itemLists", "position": [1040, 460], "parameters": {"options": {}, "fieldToSplitOut": "results"}, "typeVersion": 1}, {"id": "b522f5bc-480c-4a6a-a44b-55ca68c66ad5", "name": "Piloterr - Get Recent Fundraise - Serie A", "type": "n8n-nodes-base.httpRequest", "position": [740, 460], "parameters": {"url": "https://piloterr.com/api/v2/crunchbase/funding_rounds", "options": {}, "sendQuery": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "queryParameters": {"parameters": [{"name": "days_since_announcement", "value": "1"}, {"name": "investment_type", "value": "series_a"}]}}, "credentials": {"httpHeaderAuth": {"id": "123", "name": "<PERSON>r"}}, "typeVersion": 3}, {"id": "5965b7cd-66f4-4c5b-82a2-e9526fb4b366", "name": "Piloterr - Get Recent Fundraise - Serie B", "type": "n8n-nodes-base.httpRequest", "position": [740, 660], "parameters": {"url": "https://piloterr.com/api/v2/crunchbase/funding_rounds", "options": {}, "sendQuery": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "queryParameters": {"parameters": [{"name": "days_since_announcement", "value": "1"}, {"name": "investment_type", "value": "series_b"}]}}, "credentials": {"httpHeaderAuth": {"id": "123", "name": "<PERSON>r"}}, "typeVersion": 3}, {"id": "04ab7fe9-6422-45c3-b165-139577a0e27f", "name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [2360, 480], "parameters": {"columns": {"value": {"link": "={{ $json.link }}", "type": "={{ $json.type }}", "country": "={{ $json.country }}", "event_link": "={{ $json.event_link }}", "website_url": "={{ $json.website_url }}", "announced_on": "={{ $json.announced_on }}", "company_name": "={{ $json.company_name }}", "founded_date": "={{ $json.founded_date }}", "linkedin_url": "={{ $json.linkedin_url }}", "money_raised": "={{ $json.money_raised }}", "funding_total": "={{ $json.funding_total }}", "employee_count": "={{ $json.employee_count }}", "investment_type": "={{ $json.investment_type }}", "monthly_traffic_semrush": "={{ $json.monthly_traffic_semrush }}"}, "schema": [{"id": "company_name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "website_url", "type": "string", "display": true, "removed": false, "required": false, "displayName": "website_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "type", "type": "string", "display": true, "removed": false, "required": false, "displayName": "type", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "money_raised", "type": "string", "display": true, "removed": false, "required": false, "displayName": "money_raised", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedin_url", "type": "string", "display": true, "removed": false, "required": false, "displayName": "linkedin_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "announced_on", "type": "string", "display": true, "removed": false, "required": false, "displayName": "announced_on", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "funding_total", "type": "string", "display": true, "removed": false, "required": false, "displayName": "funding_total", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "link", "type": "string", "display": true, "removed": false, "required": false, "displayName": "link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "monthly_traffic_semrush", "type": "string", "display": true, "removed": false, "required": false, "displayName": "monthly_traffic_semrush", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "event_link", "type": "string", "display": true, "removed": false, "required": false, "displayName": "event_link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "employee_count", "type": "string", "display": true, "removed": false, "required": false, "displayName": "employee_count", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "country", "type": "string", "display": true, "removed": false, "required": false, "displayName": "country", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "founded_date", "type": "string", "display": true, "removed": false, "required": false, "displayName": "founded_date", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["event_link"]}, "options": {}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1IZ7BJUtBdezesDS5oBDzFeW-btiH7qB4gdIcwcC01xs/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "url", "value": "https://docs.google.com/spreadsheets/d/1IZ7BJUtBdezesDS5oBDzFeW-btiH7qB4gdIcwcC01xs/edit#gid=0", "__regex": "https:\\/\\/(?:drive|docs)\\.google\\.com\\/\\w+\\/d\\/([0-9a-zA-Z\\-_]+)(?:\\/.*|)"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "2", "name": "Google Sheets account lucas"}}, "typeVersion": 4}, {"id": "f88a862c-c413-4248-b061-2a449c6ee0fb", "name": "Piloterr - Get Recent Fundraise - Seed", "type": "n8n-nodes-base.httpRequest", "position": [740, 860], "parameters": {"url": "https://piloterr.com/api/v2/crunchbase/funding_rounds", "options": {}, "sendQuery": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "queryParameters": {"parameters": [{"name": "days_since_announcement", "value": "1"}, {"name": "investment_type", "value": "seed"}]}}, "credentials": {"httpHeaderAuth": {"id": "123", "name": "<PERSON>r"}}, "typeVersion": 3}, {"id": "********-d315-4bb3-bece-72ff64f602e8", "name": "Prepare data", "type": "n8n-nodes-base.set", "position": [1280, 460], "parameters": {"values": {"string": [{"name": "type", "value": "={{ $json.investment_type }}"}, {"name": "money_raised", "value": "={{ $json.money_raised.value_usd }}"}, {"name": "announced_on", "value": "={{ $json.announced_on }}"}, {"name": "company_name", "value": "={{ $json.funded_organization_identifier.value }}"}, {"name": "link", "value": "={{ $json.funded_organization_identifier.permalink }}"}, {"name": "event_link", "value": "={{ $json.identifier.permalink }}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 2}, {"id": "8fad9822-dfe3-4106-981f-f2c8163ce8a0", "name": "Piloterr - Enrich company", "type": "n8n-nodes-base.httpRequest", "position": [1520, 580], "parameters": {"url": "https://piloterr.com/api/v2/crunchbase/company/info", "options": {"batching": {"batch": {"batchSize": 3}}}, "sendQuery": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "queryParameters": {"parameters": [{"name": "query", "value": "=https://www.crunchbase.com/organization/{{ $json[\"link\"] }}"}]}}, "credentials": {"httpHeaderAuth": {"id": "123", "name": "<PERSON>r"}}, "typeVersion": 3, "continueOnFail": true}, {"id": "78289f0d-5721-4615-a883-38a1e48ebb34", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [2100, 480], "parameters": {"mode": "combine", "options": {}, "combinationMode": "mergeByPosition"}, "typeVersion": 2.1}, {"id": "d5e659d7-28ba-4cd7-a6bf-ea7b48d5f34c", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [20, 280], "parameters": {"width": 318.8857938718665, "height": 287.01949860724255, "content": "## Read me\n\nThis workflow will scrape recent fundraising events from Crunchbase, and add them in Google Sheets.\n\nFull guide here: https://lempire.notion.site/Get-recent-fundraising-in-Google-Sheets-dafbbda2635544b4925c4fb04abac8f5?pvs=74\n"}, "typeVersion": 1}, {"id": "888f5bf2-4a7f-4f84-95c8-4173fa8d8f83", "name": "Schedule Trigger - Run Workflow Every Day", "type": "n8n-nodes-base.scheduleTrigger", "position": [460, 460], "parameters": {"rule": {"interval": [{"triggerAtHour": 8}]}}, "typeVersion": 1}, {"id": "84f02477-b19c-405f-abde-3e32280208e9", "name": "Prepare data before importing to Gsheets", "type": "n8n-nodes-base.set", "position": [1860, 580], "parameters": {"values": {"string": [{"name": "website_url", "value": "={{ $json.website.match(/https?:\\/\\/(?:www\\.)?([^\\/]+)/)[1] }}"}, {"name": "monthly_traffic_semrush", "value": "={{ $json.semrush_summary.semrush_visits_latest_month }}"}, {"name": "funding_total", "value": "={{ $json.funding_rounds_headline.funding_total.value }}"}, {"name": "linkedin_url", "value": "={{ $json.linkedin_url }}"}, {"name": "employee_count", "value": "={{ $json.employee_count }}"}, {"name": "country", "value": "={{ $json.location[2].name }}"}, {"name": "founded_date", "value": "={{ $json.founded }}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 2}, {"id": "b4952b2f-7202-4b6a-81ec-7251b0d6c308", "name": "Get Linkedin URL from object", "type": "n8n-nodes-base.code", "position": [1680, 580], "parameters": {"mode": "runOnceForEachItem", "jsCode": "// Find the LinkedIn object\nlet linkedinObject = $json.social_networks.find(e => e.name === 'linkedin');\n\n// If the LinkedIn object exists, get the URL; otherwise, set to null or handle error\n$input.item.json.linkedin_url = linkedinObject ? linkedinObject.url : null;\n\n// Check if the URL was set\nif (!$input.item.json.linkedin_url) {\n    console.error('No LinkedIn URL found!');\n    // Handle the error as required for your application\n}\n\nreturn $input.item;"}, "typeVersion": 1}, {"id": "9e98198d-b9f1-42e4-b703-153f98ffce7c", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [680, 254.**************], "parameters": {"height": 818.************, "content": "Create an account at piloterr.com to get your API key\n\nFeel free to delete the node that are not useful to you. For instance \"Serie B\" and \"Seed\" if you want only to scrape Serie A events"}, "typeVersion": 1}], "pinData": {}, "connections": {"Merge": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}, "Prepare data": {"main": [[{"node": "Piloterr - Enrich company", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Split results": {"main": [[{"node": "Prepare data", "type": "main", "index": 0}]]}, "Piloterr - Enrich company": {"main": [[{"node": "Get Linkedin URL from object", "type": "main", "index": 0}]]}, "Get Linkedin URL from object": {"main": [[{"node": "Prepare data before importing to Gsheets", "type": "main", "index": 0}]]}, "Piloterr - Get Recent Fundraise - Seed": {"main": [[{"node": "Split results", "type": "main", "index": 0}]]}, "Prepare data before importing to Gsheets": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Piloterr - Get Recent Fundraise - Serie A": {"main": [[{"node": "Split results", "type": "main", "index": 0}]]}, "Piloterr - Get Recent Fundraise - Serie B": {"main": [[{"node": "Split results", "type": "main", "index": 0}]]}, "Schedule Trigger - Run Workflow Every Day": {"main": [[{"node": "Piloterr - Get Recent Fundraise - Serie A", "type": "main", "index": 0}, {"node": "Piloterr - Get Recent Fundraise - Serie B", "type": "main", "index": 0}, {"node": "Piloterr - Get Recent Fundraise - Seed", "type": "main", "index": 0}]]}}}