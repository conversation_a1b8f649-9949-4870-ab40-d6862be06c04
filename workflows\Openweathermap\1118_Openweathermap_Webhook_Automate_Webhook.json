{"nodes": [{"name": "Set", "type": "n8n-nodes-base.set", "position": [870, 300], "parameters": {"values": {"string": [{"name": "tempC", "value": "={{$json[\"main\"][\"temp\"]}}"}, {"name": "humidity", "value": "={{$json[\"main\"][\"humidity\"]}}"}, {"name": "windspeed", "value": "={{$json[\"wind\"][\"speed\"]}}"}, {"name": "description", "value": "={{$json[\"weather\"][0][\"description\"]}}"}, {"name": "city", "value": "={{$json[\"name\"]}}, {{$json[\"sys\"][\"country\"]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "OpenWeatherMap", "type": "n8n-nodes-base.openWeatherMap", "position": [650, 300], "parameters": {"cityName": "={{$json[\"body\"][\"city\"]}}"}, "credentials": {"openWeatherMapApi": "open-weather-map"}, "typeVersion": 1}, {"name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [450, 300], "webhookId": "39f1b81f-f538-4b94-8788-29180d5e4016", "parameters": {"path": "39f1b81f-f538-4b94-8788-29180d5e4016", "options": {}, "responseData": "allEntries", "responseMode": "lastNode"}, "typeVersion": 1}], "connections": {"Webhook": {"main": [[{"node": "OpenWeatherMap", "type": "main", "index": 0}]]}, "OpenWeatherMap": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}}