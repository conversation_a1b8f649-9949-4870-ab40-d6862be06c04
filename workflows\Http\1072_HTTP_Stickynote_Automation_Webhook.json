{"id": "5uapJIjLLhwnhX0n", "meta": {"instanceId": "2b69b24ad1a51b447e1a0d6f8c70b16aca715ccfaf123eb531f92865766fce1c", "templateCredsSetupCompleted": true}, "name": "Perplexity Researcher", "tags": [], "nodes": [{"id": "5790066d-4157-4844-aeaa-47706140ed7a", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "notes": "Find the latest content related to the field/knowledge you are interested in.\nIn-depth materials to prepare for the writing section", "position": [-60, -380], "parameters": {"inputSource": "passthrough"}, "typeVersion": 1.1}, {"id": "311eb2bf-3b79-46cf-abb1-9d90791167c3", "name": "Set Prompt Variables", "type": "n8n-nodes-base.set", "position": [220, -380], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "bab0ccff-a856-49d5-833b-80e65874475e", "name": "System", "type": "string", "value": "Assisstant is a language model. Assistant is designed to be able to assist with a wide range of task, form answering simple question to providing in-depth explanations and discussions on a wide range of topics.  As a language model, assistant is able to generate human-like text based on the imput it receives, allowing it to engage in natural-sounding evoling.  It’s able to process and understand large amounts of text,  and can use this knowledge to provide accurate and informative responses to a wide range of question. Additionally, Assistant is able to generate its own text based on the imput it receives, allowing it to engage in discussions and provide explanations and description on a wide range of topics. Overall, Assistant is a powerfull system that can help with a wide range of task and provide valuable insights and information on a wide range of topics. What you need help with a specific question or just want to have a conversation about a particular topic, Assistant is here to assist"}, {"id": "1a6d7638-e2a4-495c-92d4-e0626b676b18", "name": "User", "type": "string", "value": "={{ $json.query }}"}]}}, "typeVersion": 3.4}, {"id": "4385053f-c9c8-4aae-b0d2-4cf7a7817164", "name": "Extract API Response", "type": "n8n-nodes-base.set", "position": [620, -380], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c5869f36-70cb-439a-8ad0-0382b37f9798", "name": "Respone Message Content", "type": "string", "value": "={{ $json.choices[0].message.content }}"}]}}, "typeVersion": 3.4}, {"id": "b8e3f54b-5148-4e04-a8b1-e3003a0ee128", "name": "Workflow Overview", "type": "n8n-nodes-base.stickyNote", "position": [-160, -480], "parameters": {"width": 1080, "height": 300, "content": "## Perplexity Research Workflow Overview\nThis workflow takes a user query, formats it using a system prompt, and sends it to the Perplexity AI Sonar model for search.\nResponses are extracted and returned as clean output."}, "typeVersion": 1}, {"id": "7b77de3d-279a-4c33-b4c1-a796ab94a7fa", "name": "Perplexity Research Content1", "type": "n8n-nodes-base.httpRequest", "position": [420, -380], "parameters": {"url": "https://api.perplexity.ai/chat/completions", "method": "POST", "options": {}, "jsonBody": "={\n  \"model\": \"sonar\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"{{ $json.System }}\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"{{ $json.User || $json.query || $json.question || $json['Research Query'] || 'No input provided' }}\"\n    }\n  ],\n  \"max_tokens\": 4000,\n  \"temperature\": 0.2,\n  \"top_p\": 0.9,\n  \"return_citations\": true,\n  \"search_domain_filter\": [\n    \"perplexity.ai\"\n  ],\n  \"return_images\": false,\n  \"return_related_questions\": false,\n  \"search_recency_filter\": \"month\",\n  \"top_k\": 0,\n  \"stream\": false,\n  \"presence_penalty\": 0,\n  \"frequency_penalty\": 1\n}\n\n", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "XTRc36olCHOn9XQP", "name": "Head<PERSON> Au<PERSON> account 2"}}, "notesInFlow": false, "typeVersion": 4.2}], "active": false, "pinData": {}, "settings": {"callerPolicy": "any", "executionOrder": "v1"}, "versionId": "d506eade-acc3-40ed-9dfc-909cdf373969", "connections": {"When Executed by Another Workflow": {"main": [[{"node": "Set Prompt Variables", "type": "main", "index": 0}]]}}}