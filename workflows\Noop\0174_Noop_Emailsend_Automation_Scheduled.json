{"id": "14", "name": "Activity Encouragement", "nodes": [{"name": "Strava", "type": "n8n-nodes-base.strava", "position": [640, 300], "parameters": {"operation": "getAll", "returnAll": true}, "credentials": {"stravaOAuth2Api": "Strava OAuth2 Creds"}, "typeVersion": 1}, {"name": "Accountability Settings", "type": "n8n-nodes-base.set", "position": [450, 300], "parameters": {"values": {"number": [{"name": "moveTime", "value": 1800}], "string": [{"name": "actPartner1", "value": "<EMAIL>"}, {"name": "actPartner2", "value": "<EMAIL>"}, {"name": "actPartner3", "value": "<EMAIL>"}, {"name": "yourName", "value": "<PERSON>"}, {"name": "yourEmail", "value": "<EMAIL>"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "Check Activity Level", "type": "n8n-nodes-base.if", "position": [840, 300], "parameters": {"conditions": {"number": [{"value1": "={{$node[\"Strava\"].json[\"moving_time\"]}}", "value2": "={{$node[\"Accountability Settings\"].parameter[\"values\"][\"number\"][0][\"value\"]}}", "operation": "largerEqual"}]}}, "typeVersion": 1}, {"name": "Enough Activity", "type": "n8n-nodes-base.noOp", "position": [1050, 220], "parameters": {}, "typeVersion": 1}, {"name": "Send Email", "type": "n8n-nodes-base.emailSend", "position": [1050, 390], "parameters": {"text": "=Hey Accountability Team,\n\nLooks like {{$node[\"Accountability Settings\"].json[\"yourName\"]}} has been spending a bit too much time inactive! How about sending them a quick word of encouragement?\n\nThanks!\n{{$node[\"Accountability Settings\"].json[\"yourName\"]}}'s Heart", "options": {}, "toEmail": "={{$node[\"Accountability Settings\"].parameter[\"values\"][\"string\"][0][\"value\"]}}; {{$node[\"Accountability Settings\"].parameter[\"values\"][\"string\"][1][\"value\"]}}; {{$node[\"Accountability Settings\"].parameter[\"values\"][\"string\"][2][\"value\"]}}", "fromEmail": "={{$node[\"Accountability Settings\"].json[\"yourEmail\"]}}"}, "credentials": {"smtp": "<PERSON><PERSON>"}, "typeVersion": 1}, {"name": "Check Daily at 11:AM", "type": "n8n-nodes-base.cron", "position": [260, 300], "parameters": {"triggerTimes": {"item": [{"hour": 11}]}}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Strava": {"main": [[{"node": "Check Activity Level", "type": "main", "index": 0}]]}, "Check Activity Level": {"main": [[{"node": "Enough Activity", "type": "main", "index": 0}], [{"node": "Send Email", "type": "main", "index": 0}]]}, "Check Daily at 11:AM": {"main": [[{"node": "Accountability Settings", "type": "main", "index": 0}]]}, "Accountability Settings": {"main": [[{"node": "Strava", "type": "main", "index": 0}]]}}}