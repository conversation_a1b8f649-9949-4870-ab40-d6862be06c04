{"id": "3wbxkdT6hilhq0Na", "meta": {"instanceId": "d47f3738b860eed937a1b18d7345fa2c65cf4b4957554e29477cb064a7039870"}, "name": "Workflow Results to Markdown Notes in Your Obsidian Vault, via Google Drive", "tags": [], "nodes": [{"id": "be787ece-4118-4063-98b0-41672dd570c0", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [560, -480], "parameters": {"width": 440, "height": 680, "content": "## Connect folder to Obsidian Vault \n**Setup Instructions:**\n- Create a folder in your Google Drive that syncs with your desktop.\n- Configure the Google Drive node as follows:\n   - Assign the newly created folder as the parent-folder.\n   - Specify the filename, appending .md (e.g., `{{ $json.title }}.md`).\n   - Add Markdown content, including optional YAML Frontmatter, in the File Content field.\n- Establish a Symlink between the Google Drive folder and a new folder in your Obsidian Vault."}, "typeVersion": 1}, {"id": "a30f3fdc-95a1-44ff-844a-58353dc7e177", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-800, -480], "parameters": {"width": 440, "height": 680, "content": "## Workflow results to Obsidian Vault \nThis template automatically creates and updates notes in your Obsidian Vault in real-time from n8n workflow results. Markdown files and attachments saved in Google Drive instantly appear in your Obsidian Vault.\n\n**Send the output of any workflow to the Execute Workflow Trigger beow**"}, "typeVersion": 1}, {"id": "d9527913-dad1-4abc-8c86-8c76f53dd513", "name": "Save Markdown file", "type": "n8n-nodes-base.googleDrive", "position": [740, 0], "parameters": {"name": "={{ $json.title }}.md", "content": "=---\n{{ $json.frontmatter }}\n---\n{{ $json.content }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive", "cachedResultUrl": "https://drive.google.com/drive/my-drive", "cachedResultName": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "15dvUtfSjaCCXmnOVeIUfeyRd_raI3PnQ", "cachedResultUrl": "https://drive.google.com/drive/folders/15dvUtfSjaCCXmnOVeIUfeyRd_raI3PnQ", "cachedResultName": "clippings-attachments"}, "operation": "createFromText"}, "credentials": {"googleDriveOAuth2Api": {"id": "Vpmg4nRArCy8DHiE", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "6484937e-17fd-444c-916b-1527382927d4", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1020, -380], "parameters": {"color": 3, "width": 460, "height": 540, "content": "## Create Symlink\nCreate a symlink to integrate your Google Drive Desktop folder with your Obsidian Vault, ensuring that workflow-generated notes stored in Google Drive instantly appear and update in Obsidian.\n\n- **Open an Administrator Command Prompt:**\nPress `Win + S`, type `cmd`, right-click on Command Prompt, and select `<PERSON> as Administrator`.\n\n- **Get Folder Paths:**\nIdentify the source path: This is the existing Google Drive folder you want to link to.\nDecide on the target path: This is the folder in your Obsidian Vault where the symlink will be created.\nEnsure the Target Path Does Not Already Exist\n\n- **Run the mklink Command:**\nUse the following syntax to create a directory symbolic link:\n`mklink /D \"Target Path\" \"Source Path\"`\nThe target path is the location in your Vault where the symlink will be created. The source path is the Google Drive folder.\n\n- **Example Command:**\n`mklink /D \"C:\\Users\\<USER>\\Vault\\OtherFolder\" \"C:\\Users\\<USER>\\Google Drive\\MyFolder\"`"}, "typeVersion": 1}, {"id": "fe21a7c2-e8db-46be-87e7-63888bf6e9e7", "name": "Receive results from any workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-660, 0], "parameters": {}, "typeVersion": 1}, {"id": "8f2399ba-0bda-4a2e-b773-7e28df16e7c2", "name": "If the input has binary attachment", "type": "n8n-nodes-base.if", "position": [20, -160], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "9f56b367-2313-4a92-9572-b2d2687aba71", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{$json[\"binary\"]}}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "d7cae1d6-5bfe-4e69-8257-0f7947b51c96", "name": "Write Z<PERSON>lekasten note from input1", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-280, 240], "parameters": {"text": "={{ JSON.stringify($json) }}", "options": {"systemMessage": "You are an expert knowledge management assistant creating a Zettlekasten note from raw input data. Follow these precise steps:\n\n1. Extract key insights and meaningful connections from the provided JSON input.\n\n2. Structure the note using these Zettlekasten principles:\n- Create a clear, atomic central idea\n- Use precise, concise language\n- Link potential connections to other knowledge domains\n- Ensure the note can stand alone as a meaningful knowledge unit\n\n3. Note format:\n- Unique ID: Generate a unique identifier \n- Title: Concise, descriptive headline capturing core insight\n- Content: Synthesized information with clear reasoning\n- Tags: Relevant conceptual tags for future retrieval\n- References: Source of original data (optional)\n\n4. Prioritize intellectual clarity, semantic depth, and potential for future knowledge expansion.\n\nRespond ONLY with the completed Zettlekasten note in JSON format. Do not include any additional commentary or explanation."}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.7}, {"id": "303d6633-8e98-4fbc-8ee1-9f1075bcaa3e", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [-100, 420], "parameters": {"schemaType": "manual", "inputSchema": "{\n  \"title\": \"Concise, Descriptive Title\",\n  \"content\": \"Synthesized insights and key information\"\n}"}, "typeVersion": 1.2}, {"id": "62800f09-8659-47b8-9a85-7d3d2c07ec1a", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-300, 420], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "q8L9oWVM7QyzYEE5", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "df11dfcb-fb38-4796-9b28-eb1876f68261", "name": "Restructure JSON", "type": "n8n-nodes-base.set", "position": [400, 240], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c9061623-d0d0-4b63-a166-4766d88992aa", "name": "title", "type": "string", "value": "={{ $('Write Zettlekasten note from input1').item.json.output.title }}"}, {"id": "9f870307-3cbf-41b3-ba69-309610b2d020", "name": "content", "type": "string", "value": "={{ $('Write Zettlekasten note from input1').item.json.output.content }}"}, {"id": "1f40b120-00e4-479f-85b0-3fd903e629cb", "name": "frontmatter", "type": "string", "value": "={{ $json.output.frontmatter }}"}, {"id": "5b845683-5a25-486b-92b0-98990fcbf7af", "name": "references", "type": "string", "value": "={{ $('Write Zettlekasten note from input1').item.json.output.references }}"}]}}, "typeVersion": 3.4}, {"id": "2a701cf8-e59d-47ae-83c6-9ac7148bd2c8", "name": "Structured Output Parser1", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [240, 420], "parameters": {"jsonSchemaExample": "{\n\t\"frontmatter\": \"frontmatter here\"\n}"}, "typeVersion": 1.2}, {"id": "1e4da42e-e945-4be8-88ac-2579857ff3fa", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [60, 420], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "q8L9oWVM7QyzYEE5", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "af5494d8-a53f-48b1-b939-210c882485be", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-340, 100], "parameters": {"color": 4, "width": 880, "height": 460, "content": "## Optional - Use AI Agents for Note Composition\nInstead of directly using JSON parameters for the note's title, YAML frontmatter, and content, you can utilize AI agents to compose these elements. This approach involves inserting the AI-assisted workflow between the webhook and the Google Drive note, instead of the direct connection.\n"}, "typeVersion": 1}, {"id": "5d184ea4-88d0-4658-ab94-55246f3507fc", "name": "Write YAML Frontmatter", "type": "@n8n/n8n-nodes-langchain.agent", "position": [60, 240], "parameters": {"text": "={{ $json.output.content }}", "options": {"systemMessage": "=Generate comprehensive YAML frontmatter for an Obsidian note, focusing on metadata extraction and organization.\n\nOutput Format:\n```yaml\ntitle: \"{Extract a clear, concise title from input data}\"\ndate: {{ $now.toFormat('yyyy-MM-dd') }}\n\ntags:\n - {Derive 3-4 most relevant conceptual tags}\naliases:\n - {Alternative titles or key phrases}\nstatus: \"draft\"\nsource: \"{Infer original data source if possible}\""}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.7}, {"id": "d2b291be-97af-4bcb-8cc6-b21439bdcfb9", "name": "Save attachment", "type": "n8n-nodes-base.googleDrive", "position": [740, -180], "parameters": {"name": "=", "driveId": {"__rl": true, "mode": "list", "value": "My Drive", "cachedResultUrl": "https://drive.google.com/drive/my-drive", "cachedResultName": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "15dvUtfSjaCCXmnOVeIUfeyRd_raI3PnQ", "cachedResultUrl": "https://drive.google.com/drive/folders/15dvUtfSjaCCXmnOVeIUfeyRd_raI3PnQ", "cachedResultName": "clippings-attachments"}, "inputDataFieldName": "=data"}, "credentials": {"googleDriveOAuth2Api": {"id": "Vpmg4nRArCy8DHiE", "name": "Google Drive account"}}, "typeVersion": 3}], "active": false, "pinData": {"Write Zettlekasten note from input1": [{"json": {"output": {"id": "note-0235", "tags": ["Freelance", "Employment Trends", "Media Industry", "Permanent Contracts"], "title": "Shift from Freelancers to Permanent Contracts in Media", "content": "Recent developments in the media sector indicate a notable trend where freelancers are increasingly being offered permanent contracts, reflecting a shift in employment practices within the industry. This transition aligns with new leadership changes at prominent companies such as WPG Uitgevers and Mybusinessmedia, which may further influence operational dynamics. Additionally, the appointment of <PERSON> as the new editor-in-chief of Talpa Networks signifies fresh perspectives in media management, potentially impacting staffing strategies. This trend not only addresses the job security concerns of freelancers but also suggests a recalibration of talent acquisition by media organizations. Such evolutions warrant closer examination of the balance between flexibility and stability in the workforce.", "references": "Source: https://www.villamedia.nl/artikel/transfer-thursday-freelancers-naar-vast-contract-een-mooie-klus-bij-de-volkskrant-en-een-nieuwe-directeur-bij-wpg"}}}]}, "settings": {"executionOrder": "v1"}, "versionId": "c87bbecc-453d-4b8c-8b86-dcf7e1d6907b", "connections": {"Restructure JSON": {"main": [[]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Write Z<PERSON>lekasten note from input1", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Write YAML Frontmatter", "type": "ai_languageModel", "index": 0}]]}, "Save Markdown file": {"main": [[]]}, "Write YAML Frontmatter": {"main": [[{"node": "Restructure JSON", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Write Z<PERSON>lekasten note from input1", "type": "ai_outputParser", "index": 0}]]}, "Structured Output Parser1": {"ai_outputParser": [[{"node": "Write YAML Frontmatter", "type": "ai_outputParser", "index": 0}]]}, "Receive results from any workflow": {"main": [[{"node": "If the input has binary attachment", "type": "main", "index": 0}, {"node": "Save Markdown file", "type": "main", "index": 0}]]}, "If the input has binary attachment": {"main": [[{"node": "Save attachment", "type": "main", "index": 0}]]}, "Write Zettlekasten note from input1": {"main": [[{"node": "Write YAML Frontmatter", "type": "main", "index": 0}]]}}}