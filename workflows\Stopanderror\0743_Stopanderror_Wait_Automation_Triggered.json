{"id": "2NhqmUqW3KruEkaE", "meta": {"instanceId": "d868e3d040e7bda892c81b17cf446053ea25d2556fcef89cbe19dd61a3e876e9"}, "name": "Exponential Backoff for Google APIs", "tags": [{"id": "nezaWFCGa7eZsVKu", "name": "Utility", "createdAt": "2024-11-13T18:08:08.207Z", "updatedAt": "2024-11-13T18:08:08.207Z"}], "nodes": [{"id": "5d6b1730-33c5-401c-b73f-2b7ea8eedfe3", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-580, -80], "parameters": {}, "typeVersion": 1}, {"id": "6726b630-597c-46cf-8839-75cd80108f2f", "name": "Exponential Backoff", "type": "n8n-nodes-base.code", "position": [160, 120], "parameters": {"mode": "runOnceForEachItem", "jsCode": "// Define the retry count (coming from a previous node or set manually)\nconst retryCount = $json[\"retryCount\"] || 0;  // If not present, default to 0\nconst maxRetries = 5;  // Define the maximum number of retries\nconst initialDelay = 1;  // Initial delay in seconds (1 second)\n\n// If the retry count is less than the max retries, calculate the delay\nif (retryCount < maxRetries) {\n    const currentDelayInSeconds = initialDelay * Math.pow(2, retryCount);  // Exponential backoff delay in seconds\n    \n    // Log the delay time for debugging\n    console.log(`Waiting for ${currentDelayInSeconds} seconds before retry...`);\n    \n    return {\n        json: {\n            retryCount: retryCount + 1,  // Increment retry count\n            waitTimeInSeconds: currentDelayInSeconds, // Pass the delay time in seconds\n            status: 'retrying',\n        }\n    };\n} else {\n    // If max retries are exceeded, return a failure response\n    return {\n        json: {\n            error: 'Max retries exceeded',\n            retryCount: retryCount,\n            status: 'failed'\n        }\n    };\n}\n"}, "typeVersion": 2}, {"id": "605b8ff0-aa19-42dd-8dbb-aa12380ac4bc", "name": "Stop and Error", "type": "n8n-nodes-base.stopAndError", "position": [760, 120], "parameters": {"errorMessage": "Google Sheets API Limit has been triggered and the workflow has stopped"}, "typeVersion": 1}, {"id": "97818e8b-e0cc-4a49-8797-43e02535740f", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [-360, -80], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "0583eabd-bd97-4330-8a38-b2aed3a90c37", "name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "onError": "continueErrorOutput", "position": [-120, 20], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "name", "value": "Sheet1"}, "documentId": {"__rl": true, "mode": "url", "value": "https://docs.google.com/spreadsheets/d/1_gxZl6n_AYPHRFRTWfhy7TZnhEYuWzh8UvGdtWCD3sU/edit?gid=0#gid=0"}, "authentication": "serviceAccount"}, "credentials": {"googleApi": {"id": "lm7dPHYumCy6sP6k", "name": "AlexK1919 Google Service"}}, "typeVersion": 4.5}, {"id": "0d8023f8-f7ac-4303-b18e-821690cc9f94", "name": "Wait", "type": "n8n-nodes-base.wait", "position": [360, 120], "webhookId": "f1651aa1-6497-4496-9e07-240dcf1852f3", "parameters": {"amount": "={{ $json[\"waitTime\"] }}"}, "typeVersion": 1.1}, {"id": "72e0001e-f99b-4d57-9006-4a4dd5d3d8d5", "name": "Check Max Retries", "type": "n8n-nodes-base.if", "position": [560, 120], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "51e191cb-af20-423b-9303-8523caa4ae0d", "operator": {"type": "number", "operation": "gt"}, "leftValue": "={{ $('Exponential Backoff').item.json[\"retryCount\"] }}", "rightValue": 10}]}}, "typeVersion": 2.2}, {"id": "2ea14bb0-4313-4595-811d-729ca6d37420", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [100, -80], "parameters": {"color": 3, "width": 820, "height": 460, "content": "# Exponential Backoff for Google APIs \n## Connect these nodes to any Google API node such as the Google Sheets node example in this workflow"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "729e3a54-6238-4e4c-833e-8e37dba16dbb", "connections": {"Wait": {"main": [[{"node": "Check Max Retries", "type": "main", "index": 0}]]}, "Google Sheets": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}], [{"node": "Exponential Backoff", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Google Sheets", "type": "main", "index": 0}]]}, "Check Max Retries": {"main": [[{"node": "Stop and Error", "type": "main", "index": 0}], [{"node": "Google Sheets", "type": "main", "index": 0}]]}, "Exponential Backoff": {"main": [[{"node": "Wait", "type": "main", "index": 0}, {"node": "Check Max Retries", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}}}