{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9"}, "nodes": [{"id": "3a3bcb2d-cb94-40d8-8b9e-322ea9d27f6e", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1000, 640], "parameters": {"width": 300, "height": 185, "content": "### The conversation history(last 20 messages) is stored in a buffer memory"}, "typeVersion": 1}, {"id": "e279af43-b003-4499-b221-58716e735379", "name": "On new manual Chat Message", "type": "@n8n/n8n-nodes-langchain.manualChatTrigger", "position": [740, 340], "parameters": {}, "typeVersion": 1}, {"id": "f4f8bf03-a43e-4a1f-a592-cd0f8408f552", "name": "Chat OpenAI", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [840, 653], "parameters": {"model": "gpt-4o-mini", "options": {"temperature": 0.3}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "66b60f68-bae8-4958-ac81-03883f563ab3", "name": "Wikipedia", "type": "@n8n/n8n-nodes-langchain.toolWikipedia", "position": [1480, 693], "parameters": {}, "typeVersion": 1}, {"id": "70f6b43b-9290-4fbc-992f-0895d4578c9f", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1340, 633], "parameters": {"width": 300, "height": 185, "content": "### Tools which agent can use to accomplish the task"}, "typeVersion": 1}, {"id": "8696269f-6556-41f1-bbe4-5597e4e46e02", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [960, 260], "parameters": {"width": 422, "height": 211, "content": "### Conversational agent will utilise available tools to answer the prompt. "}, "typeVersion": 1}, {"id": "6814967b-4567-4cdd-bf09-6b1b5ed0c68e", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [1100, 700], "parameters": {"contextWindowLength": 20}, "typeVersion": 1}, {"id": "ce4358ac-c2cc-45ba-b950-247f8360b36c", "name": "SerpAPI", "type": "@n8n/n8n-nodes-langchain.toolSerpApi", "position": [1380, 693], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "de80add8-c37d-4d46-80ec-b43234e21150", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1040, 340], "parameters": {"text": "={{ $json.input }}", "options": {}, "promptType": "define"}, "typeVersion": 1.6}], "pinData": {}, "connections": {"SerpAPI": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Wikipedia": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Chat OpenAI": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "On new manual Chat Message": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}}}