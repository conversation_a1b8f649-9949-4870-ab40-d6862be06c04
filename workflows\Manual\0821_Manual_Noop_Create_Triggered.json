{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "93a8b03f-ff6b-4559-9cb1-9f439ff5e990", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-1180, 0], "parameters": {}, "typeVersion": 1}, {"id": "0aed449c-c60a-4309-91d2-4db9ed1f4ad2", "name": "Variables", "type": "n8n-nodes-base.set", "position": [-120, 0], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "a6c47778-33f4-46a3-a86a-fd1e75930d93", "name": "firstname", "type": "string", "value": "={{ $json.properties.firstname }}"}, {"id": "0e50b2bc-4bea-4fd0-95c0-46a87da69c19", "name": "lastname", "type": "string", "value": "={{ $json.properties.lastname }}"}, {"id": "ee15f298-77f6-4c4a-b03b-c2cf9a53bdc2", "name": "email", "type": "string", "value": "={{ $json.properties.email }}"}, {"id": "98a718f5-4372-4282-8a9a-46f2af39677a", "name": "product_to_sell", "type": "string", "value": "=AI partnerships: a consulting package of AI development and services. We help customers find a strong foothold on AI initiatives bringing them to life cost effectively and always with results."}]}}, "typeVersion": 3.4}, {"id": "f21c0147-dd18-4b06-9f58-258b8946977d", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [520, 160], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash"}, "credentials": {"googlePalmApi": {"id": "dSxo6ns5wn658r8N", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "27aaa070-4de5-479a-83eb-d2e0810a19da", "name": "Google Gemini Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1120, 160], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash"}, "credentials": {"googlePalmApi": {"id": "dSxo6ns5wn658r8N", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "b76ec237-3d90-4ed4-8746-36693775a39f", "name": "Create Draft Email For Review", "type": "n8n-nodes-base.gmail", "position": [1680, 180], "webhookId": "8b3d78e5-8cea-4205-a9db-c66ec01f9558", "parameters": {"message": "={{ $json.output.body }}", "options": {"sendTo": "={{ $('Variables').first().json.email }}"}, "subject": "={{ $json.output.subject }}", "resource": "draft", "emailType": "html"}, "credentials": {"gmailOAuth2": {"id": "Sf5Gfl9NiFTNXFWb", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "7d62abe5-9278-45f2-ba07-aba0f4353a00", "name": "Generate Sales Email", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [1040, 0], "parameters": {"text": "=# Profile of {{ $('Variables').first().json.firstname }} {{ $('Variables').first().json.lastname }}\n{{ Object.keys($json.output).map(key => `## ${key}\\n${$json.output[key]}`).join('\\n') }}", "options": {"systemPromptTemplate": "=You are a sales representative drafting an email to close a potential customer on the following product: <product>{{ $('Variables').first().json.product_to_sell }}</product>\n\nUse the provided profile to draft the a suitable email which reflects similar communication style and addresses their values, ultimately convinces the customer to inquire about and/or buy this product. Provide only the subject and body of the message as this text will go into a template. Omit the subject and signature."}, "attributes": {"attributes": [{"name": "subject", "required": true, "description": "the subject of the message"}, {"name": "body", "required": true, "description": "the body of the message with html styling"}]}}, "typeVersion": 1}, {"id": "71cd4b52-c3cd-413e-b495-f0ef511af9b1", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-220, -200], "parameters": {"color": 7, "width": 520, "height": 420, "content": "## 2. Research Customer via Emails\nEmails can be a great source of research on how a customer or potential customer thinks, behaves and communicates. This template does require some interaction beforehand but this should could be shared amongst colleagues or a CRM."}, "typeVersion": 1}, {"id": "f3cb9e8d-8d67-42a2-a9cd-7aae93a23816", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [320, -200], "parameters": {"color": 7, "width": 540, "height": 540, "content": "## 3. Build Persona Outline from Research\nOnce we gather all the emails, we can use AI to analyse and construct a quick persona on our customer. Personas are useful to understand the customer's position and how favourably they might respond to a product and/or service. The Information Extractor node is used to guide the LLM for attributes we're interested in."}, "typeVersion": 1}, {"id": "e0bdca91-e744-4717-ada6-5991e2d6c054", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [880, -200], "parameters": {"color": 7, "width": 560, "height": 540, "content": "## 4. Generate Sales Pitch based on <PERSON><PERSON>\nUsing the persona, we can again ask AI to generate the perfect sales email which takes into consideration the customer's beliefs, values and communication style. In this way, each sales email can be carefully written to improve its appeal to the customer."}, "typeVersion": 1}, {"id": "68be2c2c-5006-4041-b8ed-8c6b26d37251", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1480, -40], "parameters": {"color": 7, "width": 480, "height": 440, "content": "## 5. Create Draft for Human Review\nFinally, an email draft is created to store the generated sales pitch for human review. If given, a list of customers to target, a SDR can ensure customised outreach in minutes rather than hours or days. "}, "typeVersion": 1}, {"id": "893d42c3-c5fc-4cc3-acd2-5d847d4ebf1a", "name": "Analyse and Build Persona", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [440, 0], "parameters": {"text": "={{\n$input.all()\n  .map(item => `subject: ${item.json.subject}\ndate: ${$json.headers.date}\nmessage: ${item.json.text.substr(0, item.json.text.indexOf('> wrote:') ?? item.json.text.length).replace(/^On[\\w\\W]+$/im, '')}`\n  ).join('\\n---\\n')\n}}", "options": {"systemPromptTemplate": "=Your task is to build a persona of a customer or potential customer so that we may better serve them for our business. Analyse the recent correspondence of the user, {{ $('Variables').item.json.email }}, and extract the required attributes."}, "attributes": {"attributes": [{"name": "decision_making_style", "required": true, "description": "=Analytical vs. Intuitive: Do they rely on data or gut feelings?\n\nRisk Appetite: Conservative, calculated risk-taker, or bold?\n\nSpeed of Decision-Making: Quick and assertive or deliberate and methodical?"}, {"name": " communication_preferences", "required": true, "description": "=Preferred Medium: Email, phone calls, in-person meetings, messaging apps?\n\nDetail Orientation: High-level summaries or deep-dive explanations?\n\nTone & Formality: Casual vs. professional, direct vs. diplomatic?"}, {"name": "pain_points_challenges", "required": true, "description": "=Current Business Challenges: What problems are they actively trying to solve?\n\nIndustry Pressures: Competitive landscape, economic concerns, regulatory issues?\n\nOperational Bottlenecks: Efficiency, team structure, technology gaps?"}, {"name": "professional_goals_motivations", "required": true, "description": "=Personal Career Goals: Promotion, recognition, financial growth, legacy-building?\n\nBusiness Priorities: Revenue growth, innovation, market expansion, cost reduction?\n\nKey Performance Indicators (KPIs): How do they measure success?"}, {"name": "work_style_preferences", "required": true, "description": "=Collaboration vs. Independence: Do they prefer teamwork or autonomy?\n\nLevel of Involvement: Hands-on or delegate-and-review?\n\nResponse Time Expectation: Do they expect immediate follow-ups or are they flexible?"}, {"name": "personality_behavioral_traits", "required": true, "description": "=Big Five Traits: Are they open to new ideas, structured, agreeable, extroverted?\n\nConflict Resolution Style: Do they avoid, confront, or negotiate?\n\nTrust-Building Factors: Do they value reliability, transparency, exclusivity?"}, {"name": " buying_investment_behavior", "required": true, "description": "=Budget Sensitivity: Price-conscious or value-focused?\n\nBrand Loyalty vs. Openness: Do they stick with familiar providers or explore new options?\n\nDecision Influencers: Do they rely on peers, market research, gut instinct?"}, {"name": "preferred_business_culture_ethics", "required": true, "description": "=Formality vs. Informality: Corporate structure vs. entrepreneurial mindset?\n\nCore Values: Integrity, innovation, customer-first, sustainability?\n\nCultural Sensitivity: Are there cultural nuances to be aware of in their decision-making?"}, {"name": "industry_competitive_awareness", "required": true, "description": "=Market Trends Interest: Do they actively track industry shifts?\n\nCompetitor Awareness: Are they reactive to competitors, or focused on internal growth?\n\nTech Adoption: Do they embrace innovation, or are they slow adopters?"}]}}, "executeOnce": true, "typeVersion": 1}, {"id": "f27b7b8d-e9e8-445c-9209-25323bb40db4", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-1400, -860], "parameters": {"width": 480, "height": 1080, "content": "## Try it out\n### This n8n template uses existing emails from customers as context to customise and \"finetune\" outreach emails to them using AI.\n\nBy now, it should be common knowledge that we can leverage AI to generate unique emails but in a way, they can remain generic as the AI lacks the customer context to be truly personalised. One way to solve this is \n\n### How it works\n* Customers to target are pulled from Hubspot and each customer is then run in a loop. We're using a loop as the retrieved emails for each customer become separate items and a loop helps with item reference.\n* We connect to our Gmail account to pull all emails recieved from the customer.\n* The contents of the email will be suitable to build a short persona of the customer. We use the Information Extractor to get our AI model to pull out the key attributes of this persona such as decision making style and communication preferences.\n* With this persona, we can now pass this to our AI model to generate a personalised outreach email specifically for our customer.\n* Finally, a draft email is created for human review before sending. If you would rather send the email straight away, this is also possible.\n\n### How to use\n* Define the topic of the outreach email in the \"variables\" node. This directs the AI on what outreach email to generate.\n* Ensure the emails are pulled from the right account. If emails may contain sensitive data, adjust the filters and text parsing to ensure these are not leaked to the AI (which might then leak into the generated email).\n\n### Need Help?\nJoin the [Discord](https://discord.com/invite/XPKeKXeB7d) or ask in the [Forum](https://community.n8n.io/)!\n\nHappy Hacking!"}, "typeVersion": 1}, {"id": "72efcdea-3429-44e0-a29c-8ae0144783ae", "name": "Get All Customer's Correspondence", "type": "n8n-nodes-base.gmail", "position": [80, 0], "webhookId": "4d8c4b7a-da0b-49aa-bda8-7b1d89c62636", "parameters": {"limit": 20, "simple": false, "filters": {"q": "=from:{{ $json.email }}"}, "options": {}, "operation": "getAll"}, "credentials": {"gmailOAuth2": {"id": "Sf5Gfl9NiFTNXFWb", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "e73c8a55-c85f-45a1-9735-1cea61caff3e", "name": "Get Contacts", "type": "n8n-nodes-base.hubspot", "position": [-820, 0], "parameters": {"operation": "search", "authentication": "appToken", "filterGroupsUi": {"filterGroupsValues": [{"filtersUi": {"filterValues": [{"value": "DECISION_MAKER", "propertyName": "hs_buying_role|enumeration"}]}}]}, "additionalFields": {}}, "credentials": {"hubspotAppToken": {"id": "Qhag92BwOPZfXGfz", "name": "HubSpot account (Intrigued-Zoo)"}}, "typeVersion": 2.1}, {"id": "3579a71d-ce1f-4175-9118-87997158dcb6", "name": "For Each Contact", "type": "n8n-nodes-base.splitInBatches", "position": [-620, 0], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "********-3114-4742-9e7a-700d8d29eff6", "name": "Contact Ref", "type": "n8n-nodes-base.noOp", "position": [-420, 0], "parameters": {}, "typeVersion": 1}, {"id": "18594bbd-efc5-4fbf-8693-ffcdfcfd900f", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-880, -200], "parameters": {"color": 7, "width": 640, "height": 420, "content": "## 1. Get Targeted Existing Customers\nAs with all campaigns, it's good to have a targeted subset of customers to aim for to assess the response. Here, we can pull them out of a CRM like Hubspot if granular filtering is required for example but even a simple csv of contacts would also work."}, "typeVersion": 1}], "pinData": {}, "connections": {"Variables": {"main": [[{"node": "Get All Customer's Correspondence", "type": "main", "index": 0}]]}, "Contact Ref": {"main": [[{"node": "Variables", "type": "main", "index": 0}]]}, "Get Contacts": {"main": [[{"node": "For Each Contact", "type": "main", "index": 0}]]}, "For Each Contact": {"main": [[], [{"node": "Contact Ref", "type": "main", "index": 0}]]}, "Generate Sales Email": {"main": [[{"node": "Create Draft Email For Review", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Analyse and Build Persona", "type": "ai_languageModel", "index": 0}]]}, "Analyse and Build Persona": {"main": [[{"node": "Generate Sales Email", "type": "main", "index": 0}]]}, "Google Gemini Chat Model1": {"ai_languageModel": [[{"node": "Generate Sales Email", "type": "ai_languageModel", "index": 0}]]}, "Create Draft Email For Review": {"main": [[{"node": "For Each Contact", "type": "main", "index": 0}]]}, "Get All Customer's Correspondence": {"main": [[{"node": "Analyse and Build Persona", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Get Contacts", "type": "main", "index": 0}]]}}}