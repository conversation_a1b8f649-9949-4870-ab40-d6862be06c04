{"id": "5dcd71e5db772d996680f0be", "name": "Example - Backup n8n to Nextcloud", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [240, 310], "parameters": {}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [240, 180], "parameters": {"triggerTimes": {"item": [{"mode": "custom", "cronExpression": "* */6 * * *"}]}}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [770, 180], "parameters": {"mode": "mergeByIndex"}, "typeVersion": 1}, {"name": "Move Binary Data", "type": "n8n-nodes-base.moveBinaryData", "position": [1070, 180], "parameters": {"mode": "jsonToBinary", "options": {"useRawData": false}}, "typeVersion": 1}, {"name": "Map", "type": "n8n-nodes-base.function", "position": [520, 180], "parameters": {"functionCode": "return items[0].json.data.map(item => {\n  return {json: item}\n});"}, "typeVersion": 1}, {"name": "Get Workflow", "type": "n8n-nodes-base.httpRequest", "position": [640, 320], "parameters": {"url": "=http://localhost:5678/rest/workflows/{{$node[\"Map\"].data[\"id\"]}}", "options": {}}, "typeVersion": 1}, {"name": "Get Workflow List", "type": "n8n-nodes-base.httpRequest", "position": [380, 180], "parameters": {"url": "http://localhost:5678/rest/workflows", "options": {}}, "typeVersion": 1}, {"name": "FunctionItem", "type": "n8n-nodes-base.functionItem", "position": [920, 180], "parameters": {"functionCode": "item = item.data;\nreturn item;"}, "typeVersion": 1}, {"name": "NextCloud1", "type": "n8n-nodes-base.nextCloud", "position": [1210, 180], "parameters": {"path": "=/n8n/Backup/lacnet1/{{$node[\"Merge\"].data[\"name\"]}}.json", "binaryDataUpload": true}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Map": {"main": [[{"node": "Get Workflow", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Cron": {"main": [[{"node": "Get Workflow List", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "FunctionItem", "type": "main", "index": 0}]]}, "NextCloud1": {"main": [[]]}, "FunctionItem": {"main": [[{"node": "Move Binary Data", "type": "main", "index": 0}]]}, "Get Workflow": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Move Binary Data": {"main": [[{"node": "NextCloud1", "type": "main", "index": 0}]]}, "Get Workflow List": {"main": [[{"node": "Map", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Get Workflow List", "type": "main", "index": 0}]]}}}