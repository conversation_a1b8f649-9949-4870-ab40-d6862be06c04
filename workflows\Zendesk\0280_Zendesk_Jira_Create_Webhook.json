{"meta": {"instanceId": "237600ca44303ce91fa31ee72babcdc8493f55ee2c0e8aa2b78b3b4ce6f70bd9"}, "nodes": [{"id": "b374f136-0050-40ea-b889-03c1e20a161e", "name": "IF", "type": "n8n-nodes-base.if", "position": [1000, 300], "parameters": {"conditions": {"string": [{"value1": "={{$node[\"Determine\"].json[\"Jira issue key\"]}}", "operation": "isNotEmpty"}]}}, "typeVersion": 1}, {"id": "52e85300-9a2f-45e9-973e-0fda49a50bf1", "name": "Create issue", "type": "n8n-nodes-base.jira", "position": [1180, 400], "parameters": {"project": "10000", "summary": "={{$node[\"Get ticket\"].json[\"subject\"]}}", "issueType": "10003", "additionalFields": {"description": "=See Zendesk issue at: https://n8n.zendesk.com/agent/tickets/{{$node[\"Get ticket\"].json[\"id\"]}}"}}, "credentials": {"jiraSoftwareCloudApi": {"id": "23", "name": "[UPDATE ME]"}}, "typeVersion": 1}, {"id": "85c93002-95d3-434d-b9e9-10ef714432b1", "name": "Update ticket", "type": "n8n-nodes-base.zendesk", "notes": "Update the Zendesk ticket by adding the Jira issue key to the \"Jira Issue Key\" field.", "position": [1360, 400], "parameters": {"id": "={{$node[\"On new Zendesk ticket\"].json[\"body\"][\"id\"]}}", "operation": "update", "updateFields": {"customFieldsUi": {"customFieldsValues": [{"id": 6689934837021, "value": "={{$node[\"Create issue\"].json[\"key\"]}}"}]}}}, "credentials": {"zendeskApi": {"id": "24", "name": "[UPDATE ME]"}}, "notesInFlow": true, "typeVersion": 1}, {"id": "3aa0dff6-f4a5-47c1-9843-271b78bfbf36", "name": "Get ticket", "type": "n8n-nodes-base.zendesk", "position": [640, 300], "parameters": {"id": "={{$node[\"On new Zendesk ticket\"].json[\"body\"][\"id\"]}}", "operation": "get"}, "credentials": {"zendeskApi": {"id": "24", "name": "[UPDATE ME]"}}, "typeVersion": 1}, {"id": "efd1f838-226f-4869-83f8-086d31f8a9bc", "name": "Determine", "type": "n8n-nodes-base.function", "notes": "if issue was created already in Jira", "position": [820, 300], "parameters": {"functionCode": "/* configure here =========================================================== */\n/*  Zendesk field ID which represents the \"Jira Issue Key\" field.\n*/\nconst ISSUE_KEY_FIELD_ID = 6689934837021;\n\n/* ========================================================================== */\nnew_items = [];\n\nfor (item of $items(\"Get ticket\")) {\n    \n    // instantiate a new variable for status\n    var custom_fields = item.json[\"custom_fields\"];\n    var jira_issue_key = \"\";\n    for (var i = 0; i < custom_fields.length; i++) {\n        if (custom_fields[i].id == ISSUE_KEY_FIELD_ID) {\n            jira_issue_key = custom_fields[i].value;\n            break;\n        }\n    }\n\n    // push the new item to the new_items array\n    new_items.push({\n        \"Jira issue key\": jira_issue_key\n    });\n}\n\nreturn new_items;"}, "notesInFlow": true, "typeVersion": 1}, {"id": "41a1c04b-561b-41e3-be6e-fc953319abc1", "name": "Create comment to existing issue", "type": "n8n-nodes-base.jira", "position": [1180, 200], "parameters": {"comment": "={{$node[\"On new Zendesk ticket\"].json[\"body\"][\"comment\"]}}", "options": {}, "issueKey": "={{$node[\"Determine\"].json[\"Jira issue key\"]}}", "resource": "issueComment"}, "credentials": {"jiraSoftwareCloudApi": {"id": "23", "name": "[UPDATE ME]"}}, "typeVersion": 1}, {"id": "33e0121e-703d-4c60-b257-a89a99db771a", "name": "On new Zendesk ticket", "type": "n8n-nodes-base.webhook", "position": [460, 300], "webhookId": "d596c0c6-7377-4a17-9ed5-6ee953f072b9", "parameters": {"path": "d596c0c6-7377-4a17-9ed5-6ee953f072b9", "options": {}, "httpMethod": "POST"}, "typeVersion": 1}], "connections": {"IF": {"main": [[{"node": "Create comment to existing issue", "type": "main", "index": 0}], [{"node": "Create issue", "type": "main", "index": 0}]]}, "Determine": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}, "Get ticket": {"main": [[{"node": "Determine", "type": "main", "index": 0}]]}, "Create issue": {"main": [[{"node": "Update ticket", "type": "main", "index": 0}]]}, "On new Zendesk ticket": {"main": [[{"node": "Get ticket", "type": "main", "index": 0}]]}}}