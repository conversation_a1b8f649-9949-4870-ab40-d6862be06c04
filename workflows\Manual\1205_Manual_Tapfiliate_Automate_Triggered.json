{"nodes": [{"name": "Tapfiliate2", "type": "n8n-nodes-base.tapfiliate", "position": [870, 300], "parameters": {"resource": "programAffiliate", "programId": "testing-program-5", "affiliateId": "={{$node[\"Tapfiliate\"].json[\"id\"]}}", "additionalFields": {}}, "credentials": {"tapfiliateApi": "Tapfiliate API credentials"}, "typeVersion": 1}, {"name": "Tapfiliate1", "type": "n8n-nodes-base.tapfiliate", "position": [670, 300], "parameters": {"resource": "affiliateMetadata", "metadataUi": {"metadataValues": [{"key": "tag", "value": "n8n"}]}, "affiliateId": "={{$json[\"id\"]}}"}, "credentials": {"tapfiliateApi": "Tapfiliate API credentials"}, "typeVersion": 1}, {"name": "Tapfiliate", "type": "n8n-nodes-base.tapfiliate", "position": [470, 300], "parameters": {"email": "<EMAIL>", "lastname": "<PERSON>", "firstname": "<PERSON>", "additionalFields": {}}, "credentials": {"tapfiliateApi": "Tapfiliate API credentials"}, "typeVersion": 1}, {"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [270, 300], "parameters": {}, "typeVersion": 1}], "connections": {"Tapfiliate": {"main": [[{"node": "Tapfiliate1", "type": "main", "index": 0}]]}, "Tapfiliate1": {"main": [[{"node": "Tapfiliate2", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Tapfiliate", "type": "main", "index": 0}]]}}}