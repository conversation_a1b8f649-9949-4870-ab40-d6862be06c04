{"id": "30", "name": "N8N Español - NocodeBot", "nodes": [{"name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "notes": "<PERSON> los datos de Strapi", "position": [630, 350], "parameters": {"url": "=http://s.covid-remote.work:1337/nocodes?Name={{$json[\"message\"][\"text\"].toLowerCase()}}", "options": {}}, "notesInFlow": true, "typeVersion": 1}, {"name": "Telegram", "type": "n8n-nodes-base.telegram", "position": [950, 280], "parameters": {"text": "=------------------------------------------------       \n<b>{{$node[\"HTTP Request\"].json[\"0\"][\"Name\"].toUpperCase()}} </b>\n------------------------------------------------\n|-<b>Descripción:</b>\n|<pre>{{$node[\"Execute Command\"].json[\"stdout\"]}}</pre>", "chatId": "={{$node[\"Telegram Trigger\"].json[\"message\"][\"chat\"][\"id\"]}}", "additionalFields": {"parse_mode": "HTML"}}, "credentials": {"telegramApi": "NocodeTranslateBot"}, "typeVersion": 1}, {"name": "Telegram1", "type": "n8n-nodes-base.telegram", "position": [800, 130], "parameters": {"file": "={{$json[\"0\"][\"Img\"]}}", "chatId": "={{$node[\"Telegram Trigger\"].json[\"message\"][\"chat\"][\"id\"]}}", "operation": "sendPhoto", "additionalFields": {}}, "credentials": {"telegramApi": "NocodeTranslateBot"}, "typeVersion": 1}, {"name": "Execute Command", "type": "n8n-nodes-base.executeCommand", "position": [790, 390], "parameters": {"command": "=/usr/bin/translate --brief -t {{$node[\"Telegram Trigger\"].json[\"message\"][\"from\"][\"language_code\"]}} \"{{$json[\"0\"][\"Description\"]}}\""}, "typeVersion": 1}, {"name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "position": [290, 130], "webhookId": "9673bd65-53ef-4561-bfe1-a55fab0f77b0", "parameters": {"updates": ["*"], "additionalFields": {}}, "credentials": {"telegramApi": "NocodeTranslateBot"}, "typeVersion": 1}, {"name": "Saludos-IF", "type": "n8n-nodes-base.if", "position": [450, 270], "parameters": {"conditions": {"string": [{"value1": "={{$node[\"Telegram Trigger\"].json[\"message\"][\"text\"]}}", "value2": "/start"}]}}, "typeVersion": 1}, {"name": "S-Telegram2", "type": "n8n-nodes-base.telegram", "position": [630, 130], "parameters": {"text": "=Hola, **{{$json[\"message\"][\"chat\"][\"first_name\"]}}**  🙌\nEste bot ha sido desarrollado para @comunidadn8n\nPuedes escribir el nombre de alguna herramienta No-Code y si la tenemos registrada en nuestra Base de datos te responderemos con la descripción en tu idioma.\n\nPuedes probar escribiendo alguno de estos nombres:\n\n- Airtable\n- Stripe\n- Webflow", "chatId": "={{$node[\"Telegram Trigger\"].json[\"message\"][\"chat\"][\"id\"]}}", "additionalFields": {"parse_mode": "<PERSON><PERSON>"}}, "credentials": {"telegramApi": "NocodeTranslateBot"}, "typeVersion": 1}], "active": true, "settings": {}, "connections": {"Saludos-IF": {"main": [[{"node": "S-Telegram2", "type": "main", "index": 0}], [{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Telegram1", "type": "main", "index": 0}, {"node": "Execute Command", "type": "main", "index": 0}]]}, "Execute Command": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "Telegram Trigger": {"main": [[{"node": "Saludos-IF", "type": "main", "index": 0}]]}}}