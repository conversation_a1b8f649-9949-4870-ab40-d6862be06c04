{"id": "7Gw4IfHaVMDSj70o", "meta": {"instanceId": "e634e668fe1fc93a75c4f2a7fc0dad807ca318b79654157eadb9578496acbc76", "templateCredsSetupCompleted": true}, "name": "Convert Squarespace Profiles to Shopify Customers in Google Sheets", "tags": [], "nodes": [{"id": "17b7e952-ba9b-4067-9c98-a69ea09f7e69", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [-800, -420], "webhookId": "e09976b5-7525-422b-9834-3bc6e1c4a1b6", "parameters": {"path": "submit-profiles", "options": {"allowedOrigins": "*"}, "httpMethod": "POST"}, "typeVersion": 2}, {"id": "7e149be3-19da-4320-8910-40ef0900628a", "name": "Shopify Customers", "type": "n8n-nodes-base.googleSheets", "position": [420, -220], "parameters": {"columns": {"value": {"Tags": "=n8n, squarespace, {{ $json['Last Order Date'] ? \"ground-control,\" : \"\" }}", "Email": "={{ $json.Email }}", "Phone": "={{ $json['Billing Phone Number'] }}", "Last Name": "={{ $json['Last Name'] }}", "First Name": "={{ $json['First Name'] }}", "Default Address Zip": "={{ $json['Billing Zip'] }}", "Default Address City": "={{ $json['Billing City'] }}", "Default Address Phone": "={{ $json['Billing Phone Number'] }}", "Accepts Email Marketing": "yes", "Default Address Company": "={{ $json['Billing Name'] }}", "Default Address Address1": "={{ $json['Billing Address 1'] }}", "Default Address Address2": "={{ $json['Billing Address 2'] }}", "Default Address Country Code": "={{ $json['Billing Country'] }}", "Default Address Province Code": "={{ $json['Billing Province/State'] }}"}, "schema": [{"id": "First Name", "type": "string", "display": true, "required": false, "displayName": "First Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Last Name", "type": "string", "display": true, "required": false, "displayName": "Last Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Email", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Accepts Email Marketing", "type": "string", "display": true, "required": false, "displayName": "Accepts Email Marketing", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Default Address Company", "type": "string", "display": true, "required": false, "displayName": "Default Address Company", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "De<PERSON>ult Address Address1", "type": "string", "display": true, "required": false, "displayName": "De<PERSON>ult Address Address1", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "De<PERSON>ult Address Address2", "type": "string", "display": true, "required": false, "displayName": "De<PERSON>ult Address Address2", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Default Address City", "type": "string", "display": true, "required": false, "displayName": "Default Address City", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Default Address Province Code", "type": "string", "display": true, "required": false, "displayName": "Default Address Province Code", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Default Address Country Code", "type": "string", "display": true, "required": false, "displayName": "Default Address Country Code", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "De<PERSON>ult Address Zip", "type": "string", "display": true, "required": false, "displayName": "De<PERSON>ult Address Zip", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Default Address Phone", "type": "string", "display": true, "required": false, "displayName": "Default Address Phone", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone", "type": "string", "display": true, "required": false, "displayName": "Phone", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Accepts SMS Marketing", "type": "string", "display": true, "required": false, "displayName": "Accepts SMS Marketing", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Tags", "type": "string", "display": true, "required": false, "displayName": "Tags", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Note", "type": "string", "display": true, "required": false, "displayName": "Note", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Tax Exempt", "type": "string", "display": true, "required": false, "displayName": "Tax Exempt", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["Email"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "list", "value": ********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1yf_RYZGFHpMyOvD3RKGSvIFY2vumvI4474Qm_1t4-jM/edit#gid=********", "cachedResultName": "shopify_template"}, "documentId": {"__rl": true, "mode": "list", "value": "1yf_RYZGFHpMyOvD3RKGSvIFY2vumvI4474Qm_1t4-jM", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1yf_RYZGFHpMyOvD3RKGSvIFY2vumvI4474Qm_1t4-jM/edit?usp=drivesdk", "cachedResultName": "Make.com template"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JgI9maibw5DnBXRP", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "e04f9a9e-b699-4cf2-aa91-56e6bfa30faa", "name": "Read Squarespace profiles", "type": "n8n-nodes-base.googleSheets", "position": [-180, 0], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "list", "value": *********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1yf_RYZGFHpMyOvD3RKGSvIFY2vumvI4474Qm_1t4-jM/edit#gid=*********", "cachedResultName": "squarespace_profiles"}, "documentId": {"__rl": true, "mode": "list", "value": "1yf_RYZGFHpMyOvD3RKGSvIFY2vumvI4474Qm_1t4-jM", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1yf_RYZGFHpMyOvD3RKGSvIFY2vumvI4474Qm_1t4-jM/edit?usp=drivesdk", "cachedResultName": "Squarespace automation"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JgI9maibw5DnBXRP", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "ffebc458-bd3c-4145-a5ef-1677815f210e", "name": "Append Squarespace profiles", "type": "n8n-nodes-base.googleSheets", "position": [-200, -420], "parameters": {"columns": {"value": {"Email": "={{ $json.Email }}", "Last Name": "={{ $json['Last Name'] }}", "Created On": "={{ $json['Created On'] }}", "First Name": "={{ $json['First Name'] }}", "Billing Zip": "={{ $json['Billing Zip'] }}", "Order Count": "={{ $json['Order Count'] }}", "Total Spent": "={{ $json['Total Spent'] }}", "Billing City": "={{ $json['Billing City'] }}", "Billing Name": "={{ $json['Billing Name'] }}", "Shipping Zip": "={{ $json['Billing Zip'] }}", "Shipping City": "={{ $json['Billing City'] }}", "Shipping Name": "={{ $json['Billing Name'] }}", "Billing Country": "={{ $json['Billing Country'] }}", "Last Order Date": "={{ $json['Last Order Date'] }}", "Shipping Country": "={{ $json['Billing Country'] }}", "Subscriber Since": "={{ $json['Subscriber Since'] }}", "Billing Address 1": "={{ $json['Billing Address 1'] }}", "Billing Address 2": "={{ $json['Billing Address 2'] }}", "Subscriber Source": "={{ $json['Subscriber Source'] }}", "Shipping Address 1": "={{ $json['Billing Address 1'] }}", "Shipping Address 2": "={{ $json['Billing Address 2'] }}", "Billing Phone Number": "={{ $json['Billing Phone Number'] }}", "Shipping Phone Number": "={{ $json['Billing Phone Number'] }}", "Billing Province/State": "={{ $json['Billing Province/State'] }}", "Shipping Province/State": "={{ $json['Billing Province/State'] }}"}, "schema": [{"id": "Email", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "First Name", "type": "string", "display": true, "required": false, "displayName": "First Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Last Name", "type": "string", "display": true, "required": false, "displayName": "Last Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Created On", "type": "string", "display": true, "required": false, "displayName": "Created On", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Order Count", "type": "string", "display": true, "required": false, "displayName": "Order Count", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Last Order Date", "type": "string", "display": true, "required": false, "displayName": "Last Order Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Total Spent", "type": "string", "display": true, "required": false, "displayName": "Total Spent", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Member Since", "type": "string", "display": true, "required": false, "displayName": "Member Since", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Subscriber Since", "type": "string", "display": true, "required": false, "displayName": "Subscriber Since", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Subscriber Source", "type": "string", "display": true, "required": false, "displayName": "Subscriber Source", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Tags", "type": "string", "display": true, "required": false, "displayName": "Tags", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Mailing Lists", "type": "string", "display": true, "required": false, "displayName": "Mailing Lists", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Member Areas", "type": "string", "display": true, "required": false, "displayName": "Member Areas", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Donation Count", "type": "string", "display": true, "required": false, "displayName": "Donation Count", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Last Donation Date", "type": "string", "display": true, "required": false, "displayName": "Last Donation Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Total Donation Amount", "type": "string", "display": true, "required": false, "displayName": "Total Donation Amount", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Has Account", "type": "string", "display": true, "required": false, "displayName": "Has Account", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Customer Since", "type": "string", "display": true, "required": false, "displayName": "Customer Since", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Shipping Name", "type": "string", "display": true, "required": false, "displayName": "Shipping Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Shipping Address 1", "type": "string", "display": true, "required": false, "displayName": "Shipping Address 1", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Shipping Address 2", "type": "string", "display": true, "required": false, "displayName": "Shipping Address 2", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Shipping City", "type": "string", "display": true, "required": false, "displayName": "Shipping City", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Shipping Zip", "type": "string", "display": true, "required": false, "displayName": "Shipping Zip", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Shipping Province/State", "type": "string", "display": true, "required": false, "displayName": "Shipping Province/State", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Shipping Country", "type": "string", "display": true, "required": false, "displayName": "Shipping Country", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Shipping Phone Number", "type": "string", "display": true, "required": false, "displayName": "Shipping Phone Number", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Billing Name", "type": "string", "display": true, "required": false, "displayName": "Billing Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Billing Address 1", "type": "string", "display": true, "required": false, "displayName": "Billing Address 1", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Billing Address 2", "type": "string", "display": true, "required": false, "displayName": "Billing Address 2", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Billing City", "type": "string", "display": true, "required": false, "displayName": "Billing City", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Billing Zip", "type": "string", "display": true, "required": false, "displayName": "Billing Zip", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Billing Province/State", "type": "string", "display": true, "required": false, "displayName": "Billing Province/State", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Billing Country", "type": "string", "display": true, "required": false, "displayName": "Billing Country", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Billing Phone Number", "type": "string", "display": true, "required": false, "displayName": "Billing Phone Number", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Accepts Marketing", "type": "string", "display": true, "required": false, "displayName": "Accepts Marketing", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["Email"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "list", "value": *********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1yf_RYZGFHpMyOvD3RKGSvIFY2vumvI4474Qm_1t4-jM/edit#gid=*********", "cachedResultName": "squarespace_profiles"}, "documentId": {"__rl": true, "mode": "list", "value": "1yf_RYZGFHpMyOvD3RKGSvIFY2vumvI4474Qm_1t4-jM", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1yf_RYZGFHpMyOvD3RKGSvIFY2vumvI4474Qm_1t4-jM/edit?usp=drivesdk", "cachedResultName": "Squarespace automation"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JgI9maibw5DnBXRP", "name": "Google Sheets account"}}, "typeVersion": 4.5, "alwaysOutputData": true}, {"id": "dc5cfb81-0d9b-47fb-a97a-fa20c673283b", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [-360, -420], "parameters": {"options": {}, "batchSize": 1000}, "typeVersion": 3}, {"id": "b319cb05-6b8b-48cb-b7c9-4badee6bbf57", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1240, -580], "parameters": {"width": 340, "height": 280, "content": "## Convert Squarespace profiles\nConvert exported profile from Squarespace to compatible Shopify customers data in csv format\nSample Spreadsheet template\nhttps://docs.google.com/spreadsheets/d/1ZUP7RySMCjQUBAvlZhSE1rOul1FMVHvTSF0QexuV7mQ\n- Squarespace profiles\n- Shopify customers"}, "typeVersion": 1}, {"id": "8b9d6f85-af6e-43b8-a3f1-c63c7893e064", "name": "Extract items from webhook submission", "type": "n8n-nodes-base.extractFromFile", "position": [-580, -420], "parameters": {"options": {}, "binaryPropertyName": "file"}, "typeVersion": 1}, {"id": "ce82f958-081b-4a55-a0d1-8ffb69dee68b", "name": "Manual trigger", "type": "n8n-nodes-base.manualTrigger", "position": [-580, 0], "parameters": {}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "05db75a8-16f8-4191-b60b-d515d062bef9", "connections": {"Webhook": {"main": [[{"node": "Extract items from webhook submission", "type": "main", "index": 0}]]}, "Manual trigger": {"main": [[{"node": "Read Squarespace profiles", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Shopify Customers", "type": "main", "index": 0}], [{"node": "Append Squarespace profiles", "type": "main", "index": 0}]]}, "Read Squarespace profiles": {"main": [[{"node": "Shopify Customers", "type": "main", "index": 0}]]}, "Append Squarespace profiles": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Extract items from webhook submission": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}}}