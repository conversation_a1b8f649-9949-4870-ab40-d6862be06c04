{"meta": {"instanceId": "4eea70f6789129b82c5f438f374db25affb0eba28902cc3663e308cff7659044"}, "nodes": [{"id": "30d8dca1-8e70-443e-a5b0-a048d6e3dc1c", "name": "Every day at 07:00", "type": "n8n-nodes-base.cron", "position": [480, 300], "parameters": {"triggerTimes": {"item": [{"hour": 7}]}}, "typeVersion": 1}, {"id": "1e7b278f-7c6f-473c-acda-51fa5cf6bd00", "name": "Get new contacts", "type": "n8n-nodes-base.hubspot", "position": [700, 300], "parameters": {"resource": "contact", "operation": "search", "authentication": "oAuth2", "filterGroupsUi": {"filterGroupsValues": [{"filtersUi": {"filterValues": [{"value": "={{$today.minus({day:1}).toMillis()}}", "operator": "GTE", "propertyName": "createdate"}, {"value": "={{$today.toMillis()}}", "operator": "LT", "propertyName": "createdate"}]}}]}, "additionalFields": {}}, "credentials": {"hubspotOAuth2Api": {"id": "34", "name": "HubSpot account 2"}}, "typeVersion": 1}, {"id": "003da27c-752e-47b6-b263-c90060b677f5", "name": "Create member", "type": "n8n-nodes-base.mailchimp", "position": [920, 300], "parameters": {"list": "8965eba136", "email": "={{ $json[\"properties\"].email }}", "status": "subscribed", "options": {}, "mergeFieldsUi": {"mergeFieldsValues": [{"name": "FNAME", "value": "={{ $json[\"properties\"].firstname }}"}, {"name": "LNAME", "value": "={{ $json[\"properties\"].lastname }}"}]}, "authentication": "oAuth2"}, "credentials": {"mailchimpOAuth2Api": {"id": "25", "name": "Mailchimp account"}}, "typeVersion": 1}], "connections": {"Get new contacts": {"main": [[{"node": "Create member", "type": "main", "index": 0}]]}, "Every day at 07:00": {"main": [[{"node": "Get new contacts", "type": "main", "index": 0}]]}}}