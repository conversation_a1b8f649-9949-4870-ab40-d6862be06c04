{"meta": {"instanceId": "cb484ba7b742928a2048bf8829668bed5b5ad9787579adea888f05980292a4a7"}, "nodes": [{"id": "be49d136-6392-481d-8544-d4f4d4fd0357", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [90, 260], "parameters": {"color": 7, "width": 1000.427054367056, "height": 572.2734520891689, "content": "## Find WooCommerce User-ID\nUser-ID is required to query past orders"}, "typeVersion": 1}, {"id": "5932f77b-63e2-4991-aa16-2b8587b2b560", "name": "No email provided", "type": "n8n-nodes-base.set", "position": [400, 640], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9a06428b-4115-4eee-97f4-8e828c5a7e5a", "name": "response", "type": "string", "value": "No email address got provided."}]}}, "typeVersion": 3.3}, {"id": "909a9a47-8683-4a1f-a359-8f6a878f8cd7", "name": "If email provided", "type": "n8n-nodes-base.if", "position": [140, 460], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "13f7bd32-5760-4ac3-8292-c8beb131a4a5", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.email }}", "rightValue": ""}]}}, "typeVersion": 2}, {"id": "f5bd2098-090b-4537-8e61-90afb4c85ad8", "name": "If user found", "type": "n8n-nodes-base.if", "position": [620, 440], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "0e434771-6a63-420b-89fe-cf4d5b1d8127", "operator": {"type": "number", "operation": "gt"}, "leftValue": "={{ Object.keys($json).length }}", "rightValue": 0}]}}, "typeVersion": 2}, {"id": "cd2e8f95-363d-47fc-a18b-42eb93f89d0d", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-480, -346], "parameters": {"color": 6, "width": 1060.5591882039919, "height": 506.94921487705585, "content": "# Agent"}, "typeVersion": 1}, {"id": "411d5656-ace2-43a1-8672-0ffc9929f99b", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [2040, 265], "parameters": {"width": 2404.755367647059, "height": 559.608748371423, "content": "## Add DHL tracking information\nQueries the status of shipped orders from DHL.\n\nCan be skipped if order tracking should not be available or replaced with other services like UPS."}, "typeVersion": 1}, {"id": "2787a47f-d685-49b2-b4f9-15ed59525d63", "name": "No customer found", "type": "n8n-nodes-base.set", "position": [880, 640], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9a06428b-4115-4eee-97f4-8e828c5a7e5a", "name": "response", "type": "string", "value": "No customer with that email address could be found."}]}}, "typeVersion": 3.3}, {"id": "9ff271fb-5013-41a3-bc3c-84af3f36d079", "name": "If contains DHL data", "type": "n8n-nodes-base.if", "position": [2400, 400], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "674eff87-834b-4436-8666-66ccd11016d6", "operator": {"type": "array", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.tracking }}", "rightValue": ""}]}}, "typeVersion": 2}, {"id": "736adbe9-1141-47b1-9d17-b2a86e0285a3", "name": "Extract Tracking Data", "type": "n8n-nodes-base.set", "position": [2140, 400], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c378e8d4-3fdf-49f5-a766-6cfc1d7e898f", "name": "tracking", "type": "array", "value": "={{ $json.meta_data.filter(data => data.key === '_wc_shipment_tracking_items').flatMap(data => data.value) }}"}]}}, "typeVersion": 3.3}, {"id": "603584d5-85c7-4995-a7a4-1ecb07c9ce2b", "name": "Merge Orders", "type": "n8n-nodes-base.merge", "position": [3980, 500], "parameters": {}, "typeVersion": 2.1}, {"id": "a5f61962-99bd-4e6d-9c4f-4e0fa3685780", "name": "Merge Order and Tracking Data", "type": "n8n-nodes-base.merge", "position": [4300, 640], "parameters": {"mode": "combine", "options": {}, "combinationMode": "mergeByPosition"}, "typeVersion": 2.1}, {"id": "9aff6c2b-90f5-4cf6-8637-634c1d7f439d", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-480, -1280], "parameters": {"color": 3, "width": 478.2162464985994, "height": 558.6043670960834, "content": "# Setup\n## Generally\n- The environment variable `NODE_FUNCTION_ALLOW_BUILTIN` has to equal or include the value `crypto` (FYI: is default on n8n cloud) as it is required to run this workflow\n\n\n## To test the workflow\n1. Set a valid email address of a user that exists in WooCommerce in the Mock Node \"Mock Data\"\n2. Enable the node \"Mock Data\"\n3. Disable the node \"Decrypt email address\"\n4. Use the built-in chat by pressing the \"Chat\" button\n\n\n## For production use:\n1. Update the \"System Message\" in the node \"AI Agent\" for specific use case. At least the name of the shop should be changed\n2. Integrate the chat into the website. An example can be found in the box \"Example Website Implementation\"\n3. Disable or delete the node \"Mock Data\"\n4. Enable the node \"Decrypt email address\"\n5. Enable Workflow"}, "typeVersion": 1}, {"id": "901be36e-f68a-4052-ad40-2a3a6a596b56", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1140, 260], "parameters": {"width": 277.6742597550393, "height": 568.9672169306307, "content": "## Get Orders"}, "typeVersion": 1}, {"id": "7501d8f8-d91e-4cb3-835d-bf3cd0cac69c", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-492, 260], "parameters": {"color": 2, "width": 527.8197815634092, "height": 572.2734520891689, "content": "# WooCommerce Order Tool"}, "typeVersion": 1}, {"id": "0f2dc782-63c1-43bc-9347-33ebfe00af69", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [2680, 380], "parameters": {"options": {}, "fieldToSplitOut": "tracking"}, "typeVersion": 1}, {"id": "d9b180bc-b4d2-4b94-ac65-b73344a47ad8", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "position": [3600, 380], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "tracking"}, "typeVersion": 1}, {"id": "6d2a044e-4164-4f0f-a6ef-a1a7a347a0c3", "name": "Merge Tracking Data", "type": "n8n-nodes-base.merge", "position": [3360, 380], "parameters": {}, "typeVersion": 2.1}, {"id": "bca16467-9c24-4f36-b41f-d471d27ae465", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [260, 0], "parameters": {"sessionKey": "={{ $('Mock Data').item.json.sessionId }}", "sessionIdType": "customKey", "contextWindowLength": 10}, "typeVersion": 1.2}, {"id": "ff432439-2421-4769-bfc8-b58e56742275", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-340, 460], "parameters": {}, "typeVersion": 1}, {"id": "b8a156a0-bec2-43a5-b2c1-3474701c353b", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [1480, 260], "parameters": {"color": 7, "width": 492.*************, "height": 564.*************, "content": "## Check orders found"}, "typeVersion": 1}, {"id": "22f86e67-710f-49ae-a967-6e5f9345eab6", "name": "WooCommerce - Get User", "type": "n8n-nodes-base.wooCommerce", "position": [400, 440], "parameters": {"limit": 1, "filters": {"email": "={{ $json.email }}"}, "resource": "customer", "operation": "getAll"}, "credentials": {"wooCommerceApi": {"id": "Rm7eydPl5IQwnlhw", "name": "WooCommerce account"}}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "0c23aaa1-b1a4-4890-8df8-4440d32c2308", "name": "If order found", "type": "n8n-nodes-base.if", "position": [1520, 420], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "0e434771-6a63-420b-89fe-cf4d5b1d8127", "operator": {"type": "number", "operation": "gt"}, "leftValue": "={{ Object.keys($json).length }}", "rightValue": 0}]}}, "typeVersion": 2}, {"id": "63b155ef-6336-4938-890a-28050ffe5deb", "name": "WooCommerce Get Orders", "type": "n8n-nodes-base.httpRequest", "position": [1220, 420], "parameters": {"url": "https://woo-pleasantly-swag-werewolf3.wpcomstaging.com/wp-json/wc/v3/orders", "options": {}, "sendBody": true, "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "customer", "value": "={{ $json.id }}"}, {"name": "include", "value": "={{ $('If email provided').item.json.query.split(',').filter(data => !data.includes('@')).join(',') }}"}]}, "nodeCredentialType": "wooCommerceApi"}, "credentials": {"wooCommerceApi": {"id": "Rm7eydPl5IQwnlhw", "name": "WooCommerce account"}}, "typeVersion": 4.1, "alwaysOutputData": true}, {"id": "6cd01eed-7b28-4fe1-b3a2-33293a978843", "name": "No order found", "type": "n8n-nodes-base.set", "position": [1800, 620], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9a06428b-4115-4eee-97f4-8e828c5a7e5a", "name": "response", "type": "string", "value": "No order could be found."}]}}, "typeVersion": 3.3}, {"id": "bd45f21a-f30e-4dc2-be8b-527323016fae", "name": "Add Error Information", "type": "n8n-nodes-base.set", "position": [3120, 480], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "5fdb3524-6263-4e0b-a052-742cec8ceac1", "name": "Error", "type": "string", "value": "=No data about the parcel with the tracking ID \"{{ $('Split Out').item.json.tracking_id }}\" could be found."}]}}, "typeVersion": 3.3}, {"id": "********-8b8b-47d9-88b6-84fe1c35fd42", "name": "DHL", "type": "n8n-nodes-base.dhl", "onError": "continueErrorOutput", "position": [2880, 380], "parameters": {"options": {}, "trackingNumber": "={{ $json.tracking_number }}"}, "credentials": {"dhlApi": {"id": "AYAwLZA02lSGlGTd", "name": "DHL account Jan"}}, "typeVersion": 1}, {"id": "ea1a7ab3-d7b7-4651-9232-4724a1adc14f", "name": "Send Response", "type": "n8n-nodes-base.set", "position": [4600, 640], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "a2bb4a8a-40b0-4233-99a8-3b494fb84230", "name": "response", "type": "array", "value": "={{ $input.all().map(item => item.json) }}"}]}}, "typeVersion": 3.3}, {"id": "606f4731-00cc-4af0-a708-1d0d3d348dfa", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [640, -1280], "parameters": {"color": 4, "width": 676.*************, "height": 886.*************, "content": "## How to supply user email\nAs we want to ensure that customers can only query information about their own orders, the email address gets encrypted in the backend, and then decrypt again in this workflow. If the email was allowed to be set unencrypted, anyone could query information from other customers.\n\n### The email address get supplied in the chat script at the following location:\n```\ncreateChat({\n\twebhookUrl: '...',\n\tmetadata: {\n          email: 'ENCRYPTED_EMAIL_ADDRESS'\n    },\n});\n```\n\n\n## Example Scripts:\n\n### Encrypt email on the backend\nRun the following code in the backend of your website, send the data to the frontend, and then set it dynamically at the above defined location as email.\n\n\n\n\n\n\n\n\n\n\n\n\n\n### Decrypt email in workflow\nThis script is already used in this workflow and is only provided here again as an example.\n"}, "typeVersion": 1}, {"id": "adbd1b20-0b4e-44ee-9ecc-3fc746691a03", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [60, 0], "parameters": {"model": "gpt-4", "options": {}}, "credentials": {"openAiApi": {"id": "h7YcjvQLK5VrUYLz", "name": "OpenAi Jan"}}, "typeVersion": 1}, {"id": "b9076a7c-39b6-4205-9b05-90ed1f07115e", "name": "WooCommerce_Tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [440, 0], "parameters": {"name": "WooCommerce_Tool", "fields": {"values": [{"name": "email", "stringValue": "={{ $json.metadata.email }}"}]}, "workflowId": "={{ $workflow.id }}", "description": "Call this tool to retrieve the orders in JSON format (compatible with the WooCommerce API). The input should be a list of comma-separated order IDs or nothing at all for all orders. Supply nothing else than the order IDs!"}, "typeVersion": 1}, {"id": "c1f06bc7-04d3-4ad5-b46a-6baa509ee23d", "name": "<PERSON><PERSON>", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-440, -220], "webhookId": "3b63a62a-bfb7-4fb4-a6ec-4c40dcb4d9f6", "parameters": {"public": true, "options": {}}, "typeVersion": 1}, {"id": "1dcb818f-48d1-4314-8737-509c2484c8af", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [60, -1280], "parameters": {"color": 4, "width": 517.004057164405, "height": 555.1564335638465, "content": "## Example Website Implementation\nExample Code for a website can be found in node \"Respond to Webhook\".\n\nMore information about the embeddable chat can be found [here](https://github.com/n8n-io/n8n/tree/master/packages/%40n8n/chat#installation).\n\nRequired changes:\n- Change \"webhookUrl\" to the displayed in \"Chat Trigger\" node\n- Set the encrypted email address dynamically. The value has to be calculated in the backend to make it truly secure\n- Use a unique password for email en-/decryption and use it in the backend and this workflow (can be set in node \"Decrypt email address\")\n\n\nThe example page can be opened by calling the production Webhook-URL of the node \"Webhook Example Page\". It only works if the \"For production use\" steps on the left have been followed."}, "typeVersion": 1}, {"id": "e3a405a1-077d-4b72-bafa-26fd470f0f1c", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [360, -920], "parameters": {"options": {"responseHeaders": {"entries": [{"name": "content-type", "value": "text/html; charset=utf-8"}]}}, "respondWith": "text", "responseBody": "<doctype html>\n\t<html lang=\"en\">\n\t\t<head>\n\t\t\t<meta charset=\"utf-8\" />\n\t\t\t<meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />\n\t\t\t<title>Chat</title>\n\t\t\t<link\n\t\t\t\thref=\"https://cdn.jsdelivr.net/npm/normalize.css@8.0.1/normalize.min.css\"\n\t\t\t\trel=\"stylesheet\"\n\t\t\t/>\n\t\t\t<link href=\"https://cdn.jsdelivr.net/npm/@n8n/chat/style.css\" rel=\"stylesheet\" />\n\t\t</head>\n\t\t<body>\n\t\t\t<script type=\"module\">\n\t\t\t\timport { createChat } from 'https://cdn.jsdelivr.net/npm/@n8n/chat@latest/chat.bundle.es.js';\n\n\t\t\t\t(async function () {\n\t\t\t\t\tcreateChat({\n\t\t\t\t\t\tmode: 'window',\n\t\t\t\t\t\twebhookUrl: 'https://xxx.n8n.cloud/webhook/ea429912-869c-490b-9e04-4401ac9943b6/chat',\n\t\t\t\t\t\tloadPreviousSession: false,\n\t\t\t\t\t\tmetadata: {\n\t\t\t\t\t\t\temail: '352b16c74f73265441c55c37c9c22b04:4a8e614143c9cd31cc7e2389380943f3', // <EMAIL> encrypted\n\t\t\t\t\t\t},\n\t\t\t\t\t\twebhookConfig: {\n\t\t\t\t\t\t\theaders: {\n\t\t\t\t\t\t\t\t'Content-Type': 'application/json',\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t},\n\t\t\t\t\t});\n\t\t\t\t})();\n\t\t\t</script>\n\n\t\t\t<h1>WooCommerce Agent Example page</h1>\n\t\t\tClick on the bubble in the lower right corner to open the chat.\n\n\t\t</body>\n\t</html>\n</doctype>"}, "typeVersion": 1}, {"id": "3ee13508-9400-415f-b435-514131ab8c53", "name": "Webhook Example Page", "type": "n8n-nodes-base.webhook", "position": [140, -920], "webhookId": "18474f2d-9472-4a8d-8e63-8128fd2cbefc", "parameters": {"path": "website-chat-example", "options": {}, "responseMode": "responseNode"}, "typeVersion": 1.1}, {"id": "76bfe2b1-2c4a-45b9-a066-1287e735fafd", "name": "Decrypt email", "type": "n8n-nodes-base.code", "position": [860, -580], "parameters": {"jsCode": "// Loop over input items and add a new field called 'myNew<PERSON>ield' to the JSON of each one\n\nconst crypto = require('crypto');\n\nconst password = 'a random password';\n\nconst encryptedData = $input.first().json.email;\n\n\nfunction decrypt(encrypted, password) {\n  // Extract the IV and the encrypted text\n  const parts = encrypted.split(':');\n  const iv = Buffer.from(parts.shift(), 'hex');\n\n  // Create a key from the password\n  const key = crypto.scryptSync(password, 'salt', 32);\n\n  // Create a decipher\n  const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);\n\n  // Decrypt the text\n  let decrypted = decipher.update(parts.join(':'), 'hex', 'utf8');\n  decrypted += decipher.final('utf8');\n\n  // Return the decrypted text\n  return decrypted;\n}\n\nreturn [\n  {\n    json: {\n      email: decrypt(encryptedData, password),\n    }\n  }\n];"}, "typeVersion": 2}, {"id": "561cb422-955b-445b-9690-aa439dcd2455", "name": "Encrypt email", "type": "n8n-nodes-base.code", "position": [680, -840], "parameters": {"jsCode": "const crypto = require('crypto');\n\nconst password = 'a random password';\nconst email = '<EMAIL>';\n\n\nfunction encrypt(text, password) {\n  // Generate a secure random initialization vector\n  const iv = crypto.randomBytes(16);\n\n  // Create a key from the password\n  const key = crypto.scryptSync(password, 'salt', 32);\n\n  // Create a cipher\n  const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);\n\n  // Encrypt the text\n  let encrypted = cipher.update(text, 'utf8', 'hex');\n  encrypted += cipher.final('hex');\n\n  // Return the IV and the encrypted text\n  return `${iv.toString('hex')}:${encrypted}`;\n}\n\nreturn [\n  {\n    json: {\n      email: encrypt(email, password),\n    }\n  }\n];"}, "typeVersion": 2}, {"id": "eba004cb-4a40-432b-8fe2-d8526913c585", "name": "Example encrypted email", "type": "n8n-nodes-base.set", "position": [680, -580], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "fa8d71d3-8e60-44b0-8ef0-e0bfc6feaf0e", "name": "email", "type": "string", "value": "352b16c74f73265441c55c37c9c22b04:4a8e614143c9cd31cc7e2389380943f3"}]}}, "typeVersion": 3.3}, {"id": "d2fe7948-2ce5-4faa-91da-ea76f02aaf84", "name": "Decrypt email address", "type": "n8n-nodes-base.code", "disabled": true, "position": [-240, -220], "parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\n\nconst crypto = require('crypto');\n\nconst password = 'a random password';\nconst incomingData = $input.first().json;\n\n\nfunction decrypt(encrypted, password) {\n  // Extract the IV and the encrypted text\n  const parts = encrypted.split(':');\n  const iv = Buffer.from(parts.shift(), 'hex');\n\n  // Create a key from the password\n  const key = crypto.scryptSync(password, 'salt', 32);\n\n  // Create a decipher\n  const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);\n\n  // Decrypt the text\n  let decrypted = decipher.update(parts.join(':'), 'hex', 'utf8');\n  decrypted += decipher.final('utf8');\n\n  // Return the decrypted text\n  return decrypted;\n}\n\nreturn [\n  {\n    json: {\n      ...incomingData,\n      metadata: {\n        email: decrypt(incomingData.metadata.email, password),        \n      },\n    }\n  }\n];"}, "typeVersion": 2}, {"id": "26cb468c-5edf-4674-bec2-39270262fc00", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [140, -220], "parameters": {"options": {"systemMessage": "=The Assistant is tailored to support customers of Best Shirts Ltd. with inquiries related to their orders. It adheres to the following principles for optimal customer service:\n\n1. **Customer-Focused Communication**: The Assistant maintains a friendly and helpful tone throughout the interaction. It remains focused on the topic at hand, ensuring all responses are relevant to the customer's inquiries about their orders.\n\n2. **Objective and Factual**: In cases where specific information is unavailable, the Assistant clearly communicates the lack of information and refrains from speculating or providing unverified details.\n\n3. **Efficient Interaction**: Recognizing the importance of the customer's time, the Assistant is designed to remember previous interactions within the same session. This minimizes the need for customers to repeat information, streamlining the support process.\n\n4. **Strict Privacy Adherence**: The Assistant automatically has access to the customer's email address as \"{{ $json.email }}\", using it to assist with order-related inquiries. Customers are informed that it is not possible to use or inquire about a different email address. If a customer attempts to provide an alternate email, they are gently reminded of this limitation.\n\n5. **Transparency in Order Status**: The Assistant provides accurate information about order processing and delivery timelines. Orders are typically dispatched 1-2 days post-purchase, with an expected delivery period of 1-2 days following dispatch. If an order hasn't been sent out within 2 days, the Assistant acknowledges an unplanned delay and offers assistance accordingly.\n\n6. **Non-assumptive Approach to Delivery Confirmation**: The Assistant never presumes an order has been delivered based solely on its dispatch. It relies on explicit delivery confirmations or tracking information to inform customers about their order status.\n\n7. **Responsive to Specific Inquiries**: If a customer requests the email address used for their inquiry, the Assistant provides it directly, ensuring privacy and accuracy in communications.\n\nThis approach ensures that customers receive comprehensive, respectful, and efficient support for their order-related queries."}}, "typeVersion": 1.4}, {"id": "1088d613-4321-40ec-baba-deb0f3aa1078", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.set", "position": [-40, -220], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c591fa49-31b3-46e7-8108-2d3ad1fc895b", "name": "metadata.email", "type": "string", "value": "<EMAIL>"}]}, "includeOtherFields": true}, "typeVersion": 3.3}], "pinData": {}, "connections": {"DHL": {"main": [[{"node": "Merge Tracking Data", "type": "main", "index": 0}], [{"node": "Add Error Information", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "Merge Orders", "type": "main", "index": 0}]]}, "Mock Data": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "DHL", "type": "main", "index": 0}]]}, "Chat Trigger": {"main": [[{"node": "Decrypt email address", "type": "main", "index": 0}]]}, "Merge Orders": {"main": [[{"node": "Merge Order and Tracking Data", "type": "main", "index": 0}]]}, "If user found": {"main": [[{"node": "WooCommerce Get Orders", "type": "main", "index": 0}], [{"node": "No customer found", "type": "main", "index": 0}]]}, "If order found": {"main": [[{"node": "Extract Tracking Data", "type": "main", "index": 0}, {"node": "Merge Order and Tracking Data", "type": "main", "index": 1}], [{"node": "No order found", "type": "main", "index": 0}]]}, "WooCommerce_Tool": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "If email provided": {"main": [[{"node": "WooCommerce - Get User", "type": "main", "index": 0}], [{"node": "No email provided", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Merge Tracking Data": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "If contains DHL data": {"main": [[{"node": "Split Out", "type": "main", "index": 0}], [{"node": "Merge Orders", "type": "main", "index": 1}]]}, "Webhook Example Page": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Add Error Information": {"main": [[{"node": "Merge Tracking Data", "type": "main", "index": 1}]]}, "Decrypt email address": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Extract Tracking Data": {"main": [[{"node": "If contains DHL data", "type": "main", "index": 0}]]}, "WooCommerce - Get User": {"main": [[{"node": "If user found", "type": "main", "index": 0}]]}, "WooCommerce Get Orders": {"main": [[{"node": "If order found", "type": "main", "index": 0}]]}, "Example encrypted email": {"main": [[{"node": "Decrypt email", "type": "main", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "If email provided", "type": "main", "index": 0}]]}, "Merge Order and Tracking Data": {"main": [[{"node": "Send Response", "type": "main", "index": 0}]]}}}