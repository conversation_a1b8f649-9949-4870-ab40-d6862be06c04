{"id": "MfKB97VVSuXMo3Fm", "meta": {"instanceId": "1e003a7ea4715b6b35e9947791386a7d07edf3b5bf8d4c9b7ee4fdcbec0447d7"}, "name": "Create Animated Stories using GPT-4o-mini, Midjourney, Kling and Creatomate API", "tags": [], "nodes": [{"id": "4e9ed246-e4d7-4a9f-9bb1-cf74e16c3c6f", "name": "output1", "type": "n8n-nodes-base.set", "position": [3980, 260], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "0107a930-1d7a-459c-a3ab-ceb28b43b8b8", "name": "video1", "type": "string", "value": "={{ $json.data.output.video_url }}"}]}}, "typeVersion": 3.4}, {"id": "d696b66a-3726-4fe5-989e-9405b53c62e1", "name": "output2", "type": "n8n-nodes-base.set", "position": [4680, 240], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "0107a930-1d7a-459c-a3ab-ceb28b43b8b8", "name": "video2", "type": "string", "value": "={{ $json.data.output.video_url }}"}]}}, "typeVersion": 3.4}, {"id": "33e054fb-b747-4227-9884-f020e78ef5d5", "name": "Get Prompt", "type": "n8n-nodes-base.code", "position": [600, -80], "parameters": {"jsCode": "const content = $input.first().json.choices[0].message.content;\n\nconst prompts = JSON.parse(content);\n\nreturn { prompts };"}, "typeVersion": 2}, {"id": "af4dee94-7bb0-4cb9-9372-a2a1e96d0b19", "name": "Basic Params", "type": "n8n-nodes-base.set", "position": [220, -80], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "{\n  \"x-api-key\":\"\",\n  \"style\": \"a children’s book cover, ages 6-10. --s 500 --sref 4028286908  --niji 6\",\n  \"character\": \"A gentle girl and a fluffy rabbit explore a sunlit forest together, playing by a sparkling stream\",\n  \"situational_keywords\": \"Butterflies flutter around them as golden sunlight filters through green leaves. Warm and peaceful atmosphere\"\n}\n"}, "typeVersion": 3.4}, {"id": "c0890cdd-642a-48cd-b770-05fbae86abb4", "name": "Get Task ID of the First Image", "type": "n8n-nodes-base.set", "position": [380, 140], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d52d19d1-3a37-47bb-ad23-e809323c0c54", "name": "image1", "type": "string", "value": "={{ $('Midjourney Generator of the First Image').item.json.data.task_id }}"}]}}, "typeVersion": 3.4}, {"id": "a2d1e854-53c3-4115-a206-f55c2cff54f1", "name": "Midjourney Generator of the First Image", "type": "n8n-nodes-base.httpRequest", "position": [160, 140], "parameters": {"url": "https://api.piapi.ai/api/v1/task", "method": "POST", "options": {}, "jsonBody": "={\n  \"model\": \"midjourney\",\n  \"task_type\": \"imagine\",\n  \"input\": {\n    \"prompt\": \"{{ $('Basic Params').item.json.character }},{{ $json.prompts.prompt1 }},{{ $('Basic Params').item.json.style }}\",\n    \"aspect_ratio\": \"2:3\",\n    \"process_mode\": \"turbo\",\n    \"skip_prompt_check\": false\n  }\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "x-api-key", "value": "={{ $('Basic Params').item.json['x-api-key'] }}"}]}}, "typeVersion": 4.2}, {"id": "ac342912-7d94-4019-a0c8-43b555d8566f", "name": "Wait for the First Image Generation", "type": "n8n-nodes-base.wait", "position": [220, 400], "webhookId": "af79053d-1291-4dd2-889e-4593dbbb2512", "parameters": {}, "typeVersion": 1.1}, {"id": "cd774311-252a-43ec-aaea-28eb7d87ccc1", "name": "Verify the first image generation status", "type": "n8n-nodes-base.if", "position": [600, 400], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "or", "conditions": [{"id": "e97a02cc-8d1d-4500-bce5-0a296c792b76", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "completed"}, {"id": "50b63a7a-52b5-4766-a859-96ac1ff949ec", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "failed"}]}}, "typeVersion": 2.2}, {"id": "a4b8ce31-0ffd-4c9a-b102-162139fa553b", "name": "Get the First Image URL", "type": "n8n-nodes-base.set", "position": [1000, 200], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d52d19d1-3a37-47bb-ad23-e809323c0c54", "name": "image_urls1", "type": "array", "value": "={{ $json.data.output.temporary_image_urls }}"}, {"id": "f519111b-5cb4-4562-bb8c-944a500e2df3", "name": "", "type": "string", "value": ""}]}}, "typeVersion": 3.4}, {"id": "c28e46a7-999c-4a63-a3ae-aa813d7669cd", "name": "Midjourney Generator of the Second Image", "type": "n8n-nodes-base.httpRequest", "position": [1220, 200], "parameters": {"url": "https://api.piapi.ai/api/v1/task", "method": "POST", "options": {}, "jsonBody": "={\n  \"model\": \"midjourney\",\n  \"task_type\": \"imagine\",\n  \"input\": {\n    \"prompt\": \"{{ $('Basic Params').item.json.character }},{{ $('Get Prompt').item.json.prompts.prompt2 }},{{ $('Basic Params').item.json.style }}\",\n    \"aspect_ratio\": \"2:3\",\n    \"process_mode\": \"turbo\",\n    \"skip_prompt_check\": false\n  }\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "x-api-key", "value": "={{ $('Basic Params').item.json['x-api-key'] }}"}]}}, "typeVersion": 4.2}, {"id": "529f0561-d29f-4df2-b61d-0720d33bf6b9", "name": "Midjourney Generator of the Third Image", "type": "n8n-nodes-base.httpRequest", "position": [1920, 220], "parameters": {"url": "https://api.piapi.ai/api/v1/task", "method": "POST", "options": {}, "jsonBody": "={\n  \"model\": \"midjourney\",\n  \"task_type\": \"imagine\",\n  \"input\": {\n    \"prompt\": \"{{ $('Basic Params').item.json.character }},{{ $('Get Prompt').item.json.prompts.prompt3 }},{{ $('Basic Params').item.json.style }}\",\n    \"aspect_ratio\": \"2:3\",\n    \"process_mode\": \"turbo\",\n    \"skip_prompt_check\": false\n  }\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "x-api-key", "value": "={{ $('Basic Params').item.json['x-api-key'] }}"}]}}, "typeVersion": 4.2}, {"id": "a1a0b0f4-ac4e-4eca-9aa4-52f815731875", "name": "Get the First Image Generation Status", "type": "n8n-nodes-base.switch", "position": [800, 200], "parameters": {"rules": {"values": [{"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "5f61ee56-4ebe-411f-95e6-b47d9741e7a2", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "completed"}]}}]}, "options": {}}, "typeVersion": 3.2}, {"id": "6f2b83a9-2557-461e-9d5b-9f96440182f0", "name": "Get the Second Image Generation Status", "type": "n8n-nodes-base.switch", "position": [1480, 220], "parameters": {"rules": {"values": [{"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "5f61ee56-4ebe-411f-95e6-b47d9741e7a2", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "completed"}]}}]}, "options": {}}, "typeVersion": 3.2}, {"id": "10802f7a-d262-4f6f-aa48-0dce95c6fa41", "name": "Get the Third Image Generation Status", "type": "n8n-nodes-base.switch", "position": [2380, 220], "parameters": {"rules": {"values": [{"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "5f61ee56-4ebe-411f-95e6-b47d9741e7a2", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "completed"}]}}]}, "options": {}}, "typeVersion": 3.2}, {"id": "c968472d-29e6-428d-99bc-1d3f79b3f0ac", "name": "Get the Second Image URL", "type": "n8n-nodes-base.set", "position": [1720, 220], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d52d19d1-3a37-47bb-ad23-e809323c0c54", "name": "image_urls1", "type": "array", "value": "={{ $json.data.output.temporary_image_urls }}"}, {"id": "f519111b-5cb4-4562-bb8c-944a500e2df3", "name": "", "type": "string", "value": ""}]}}, "typeVersion": 3.4}, {"id": "44fe09ee-b16a-4c00-b589-4634a5f795d2", "name": "Get the Third Image URL", "type": "n8n-nodes-base.set", "position": [2560, 220], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d52d19d1-3a37-47bb-ad23-e809323c0c54", "name": "image_urls1", "type": "array", "value": "={{ $json.data.output.temporary_image_urls }}"}, {"id": "f519111b-5cb4-4562-bb8c-944a500e2df3", "name": "", "type": "string", "value": ""}]}}, "typeVersion": 3.4}, {"id": "472804c0-3f58-4ff3-aaca-b970bef425df", "name": "Wait for the Second Image Generation", "type": "n8n-nodes-base.wait", "position": [900, 440], "webhookId": "af79053d-1291-4dd2-889e-4593dbbb2512", "parameters": {}, "typeVersion": 1.1}, {"id": "f4004d5a-fd04-4d1c-9376-026d12d690d5", "name": "Get Task of the First Image", "type": "n8n-nodes-base.httpRequest", "position": [420, 400], "parameters": {"url": "=https://api.piapi.ai/api/v1/task/{{ $('Get Task ID of the First Image').item.json.image1 }}", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-api-key", "value": "={{ $('Basic Params').item.json['x-api-key'] }}"}]}}, "typeVersion": 4.2}, {"id": "ce09e4ec-97f7-4575-a133-a2c98a55b60e", "name": "Get Task of the Second Image", "type": "n8n-nodes-base.httpRequest", "position": [1100, 440], "parameters": {"url": "=https://api.piapi.ai/api/v1/task/{{ $json.data.task_id }}", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-api-key", "value": "={{ $('Basic Params').item.json['x-api-key'] }}"}]}}, "typeVersion": 4.2}, {"id": "2fa361f8-d580-4aa8-ae76-ca336669b0e1", "name": "Verify the second image generation status", "type": "n8n-nodes-base.if", "position": [1300, 440], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "or", "conditions": [{"id": "e97a02cc-8d1d-4500-bce5-0a296c792b76", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "completed"}, {"id": "50b63a7a-52b5-4766-a859-96ac1ff949ec", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "failed"}]}}, "typeVersion": 2.2}, {"id": "a01d33a5-8419-4919-ae53-d90005244734", "name": "Verify the third image generation status", "type": "n8n-nodes-base.if", "position": [2100, 460], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "or", "conditions": [{"id": "e97a02cc-8d1d-4500-bce5-0a296c792b76", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "completed"}, {"id": "50b63a7a-52b5-4766-a859-96ac1ff949ec", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "failed"}]}}, "typeVersion": 2.2}, {"id": "e2ed41b1-daf4-4da2-a320-b2c10d11e6b1", "name": "Get Task of the Third Image", "type": "n8n-nodes-base.httpRequest", "position": [1880, 460], "parameters": {"url": "=https://api.piapi.ai/api/v1/task/{{ $json.data.task_id }}", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-api-key", "value": "={{ $('Basic Params').item.json['x-api-key'] }}"}]}}, "typeVersion": 4.2}, {"id": "1d9a3356-2f3e-4829-af75-cdb6f02d3375", "name": "Wait for the Third Image Generation", "type": "n8n-nodes-base.wait", "position": [1680, 460], "webhookId": "af79053d-1291-4dd2-889e-4593dbbb2512", "parameters": {}, "typeVersion": 1.1}, {"id": "e7a4f69a-bf86-4469-8135-9fc5b18bf54d", "name": "Get three Image URLs", "type": "n8n-nodes-base.set", "position": [2740, 220], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d52d19d1-3a37-47bb-ad23-e809323c0c54", "name": "image_output1", "type": "string", "value": "={{ $('Get the First Image URL').item.json.image_urls1[0] }}"}, {"id": "333c0676-342a-40ea-bcc7-c13e9145818f", "name": "image_output2", "type": "string", "value": "={{ $('Get the Second Image URL').item.json.image_urls1[1] }}"}, {"id": "cc21180a-d88a-40ed-b5d1-0abcfd86bf86", "name": "image_output3", "type": "string", "value": "={{ $json.image_urls1[2] }}"}]}}, "typeVersion": 3.4}, {"id": "83c07d12-31c5-4364-9fff-f82d30530925", "name": "Generate the First Video", "type": "n8n-nodes-base.httpRequest", "position": [2960, 40], "parameters": {"url": "https://api.piapi.ai/api/v1/task", "method": "POST", "options": {}, "jsonBody": "={\n    \"model\": \"kling\",\n    \"task_type\": \"video_generation\",\n    \"input\": {\n        \"version\": \"1.6\",\n        \"mode\": \"pro\",\n        \"image_url\": \"{{ $json.image_output1 }}\",\n        \"prompt\": \"Natural swing\"\n    }\n} ", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "x-api-key", "value": "={{ $('Basic Params').first().json['x-api-key'] }}"}]}}, "typeVersion": 4.2}, {"id": "5bd0161c-c217-4234-b36b-4826f2ffc0c9", "name": "Generate the Second Video", "type": "n8n-nodes-base.httpRequest", "position": [3160, 40], "parameters": {"url": "https://api.piapi.ai/api/v1/task", "method": "POST", "options": {}, "jsonBody": "={\n    \"model\": \"kling\",\n    \"task_type\": \"video_generation\",\n    \"input\": {\n        \"version\": \"1.6\",\n        \"mode\": \"pro\",\n        \"image_url\": \"{{ $('Get three Image URLs').item.json.image_output2 }}\",\n        \"prompt\": \"Natural swing\"\n    }\n} ", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "x-api-key", "value": "={{ $('Basic Params').first().json['x-api-key'] }}"}]}}, "typeVersion": 4.2}, {"id": "bf701aa9-ccdc-48d8-885f-193c2b1042f3", "name": "Generate the Third Video", "type": "n8n-nodes-base.httpRequest", "position": [3360, 40], "parameters": {"url": "https://api.piapi.ai/api/v1/task", "method": "POST", "options": {}, "jsonBody": "={\n    \"model\": \"kling\",\n    \"task_type\": \"video_generation\",\n    \"input\": {\n        \"version\": \"1.6\",\n        \"mode\": \"pro\",\n        \"image_url\": \"{{ $('Get three Image URLs').item.json.image_output3 }}\",\n        \"prompt\": \"Natural swing\"\n    }\n} ", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "x-api-key", "value": "={{ $('Basic Params').first().json['x-api-key'] }}"}]}}, "typeVersion": 4.2}, {"id": "ebf87efe-f5b6-4c96-8c12-5c7a37680851", "name": "Final Video Combination", "type": "n8n-nodes-base.httpRequest", "position": [5700, 220], "parameters": {"url": "https://api.creatomate.com/v1/renders", "method": "POST", "options": {}, "jsonBody": "={\n  \"template_id\": \"c10c62b6-d515-4f36-a730-f4646d1b7ee2\",\n\"modifications\": {\n    \"Video-1.source\": \"{{ $('output1').item.json.video1 }}\",\n    \"Video-2.source\": \"{{ $('output2').item.json.video2 }}\",\n    \"Video-3.source\": \"{{ $('Get the Third Video URL').item.json.video3 }}\",\n    \"Text-1.text\": \"{{ $('Get Prompt').item.json.prompts.title }}\"\n  }\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer "}, {"name": "Content-Type", "value": "application/json"}]}}, "executeOnce": true, "typeVersion": 4.2}, {"id": "d73069fa-f24d-4972-87cb-24c20112f251", "name": "Get the Third Video URL", "type": "n8n-nodes-base.set", "position": [5480, 220], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "0107a930-1d7a-459c-a3ab-ceb28b43b8b8", "name": "video3", "type": "string", "value": "={{ $json.data.output.video_url }}"}]}}, "typeVersion": 3.4}, {"id": "cb26d84e-55aa-4de1-819e-7704dcc2b45d", "name": "Get Video Task IDs", "type": "n8n-nodes-base.set", "position": [3560, 40], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d20160df-8be0-4bd0-8960-f49ebb596a64", "name": "video_task1", "type": "string", "value": "={{ $('Generate the First Video').first().json.data.task_id }}"}, {"id": "1be26a8b-1ba3-4de3-a458-778a33f99730", "name": "video_task2", "type": "string", "value": "={{ $('Generate the Second Video').first().json.data.task_id }}"}, {"id": "9718a718-8e19-4001-a0c9-2d35710d904e", "name": "video_task3", "type": "string", "value": "={{ $json.data.task_id }}"}]}}, "typeVersion": 3.4}, {"id": "7b6def7e-2712-42c8-8e2b-ac5f70ef146f", "name": "Get the First Video Task", "type": "n8n-nodes-base.httpRequest", "position": [3380, 420], "parameters": {"url": "=https://api.piapi.ai/api/v1/task/{{ $('Get Video Task IDs').item.json.video_task1 }}", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-api-key", "value": "={{ $('Basic Params').first().json['x-api-key'] }}"}]}}, "typeVersion": 4.2}, {"id": "8c5a2fe8-75b1-42b5-b861-db3e11ba03d7", "name": "Verify the First Video Generation", "type": "n8n-nodes-base.if", "position": [3580, 420], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "or", "conditions": [{"id": "e97a02cc-8d1d-4500-bce5-0a296c792b76", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "completed"}, {"id": "50b63a7a-52b5-4766-a859-96ac1ff949ec", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "failed"}]}}, "typeVersion": 2.2}, {"id": "9b6925e2-3f9a-46b9-acea-06abbce4033a", "name": "Verify the Second Video Generation", "type": "n8n-nodes-base.if", "position": [4320, 420], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "or", "conditions": [{"id": "e97a02cc-8d1d-4500-bce5-0a296c792b76", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "completed"}, {"id": "50b63a7a-52b5-4766-a859-96ac1ff949ec", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "failed"}]}}, "typeVersion": 2.2}, {"id": "a591a522-65d8-4a4d-a326-64bc2e6463c5", "name": "Verify the Third Video Generation", "type": "n8n-nodes-base.if", "position": [5040, 420], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "or", "conditions": [{"id": "e97a02cc-8d1d-4500-bce5-0a296c792b76", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "completed"}, {"id": "50b63a7a-52b5-4766-a859-96ac1ff949ec", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "failed"}]}}, "typeVersion": 2.2}, {"id": "376176c5-20c5-4a16-9fd5-10f9f2560575", "name": "Wait for Video Generation for at Least 4 min", "type": "n8n-nodes-base.wait", "position": [3780, 40], "webhookId": "d7fc1084-fd2e-4aac-98aa-60c719d73c4c", "parameters": {"unit": "minutes", "amount": 4}, "typeVersion": 1.1}, {"id": "dba60967-840b-4c3e-8673-080eae901185", "name": "Wait for the First Video Generation", "type": "n8n-nodes-base.wait", "position": [3800, 440], "webhookId": "db4734c5-f793-4b3b-a660-ae87c516dfc1", "parameters": {}, "typeVersion": 1.1}, {"id": "b337b044-7da8-494d-9d55-3685b2585e93", "name": "Wait for the Second Video Generation", "type": "n8n-nodes-base.wait", "position": [4480, 460], "webhookId": "d9cde4b3-5755-4fde-b58e-9b5849a21c2b", "parameters": {}, "typeVersion": 1.1}, {"id": "372f89ed-b771-4c6f-9bc2-faa7e31bfc6d", "name": "Wait for the Third Video Generation", "type": "n8n-nodes-base.wait", "position": [5260, 460], "webhookId": "d077385d-bd8f-4d14-b332-22a974f98ea7", "parameters": {}, "typeVersion": 1.1}, {"id": "756d9cb7-da96-4515-a391-f152d3ca8ea9", "name": "Get the First Video Generation", "type": "n8n-nodes-base.switch", "position": [3800, 260], "parameters": {"rules": {"values": [{"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "5f61ee56-4ebe-411f-95e6-b47d9741e7a2", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "completed"}]}}]}, "options": {}}, "typeVersion": 3.2}, {"id": "7b96363c-55f3-4773-9d50-f2e71c7ac4eb", "name": "Get the Second Video Generation", "type": "n8n-nodes-base.switch", "position": [4520, 240], "parameters": {"rules": {"values": [{"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "5f61ee56-4ebe-411f-95e6-b47d9741e7a2", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "completed"}]}}]}, "options": {}}, "typeVersion": 3.2}, {"id": "eb19ddd9-1bd3-430b-997b-ad288a4731b9", "name": "Get the Third Video Generation", "type": "n8n-nodes-base.switch", "position": [5280, 220], "parameters": {"rules": {"values": [{"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "5f61ee56-4ebe-411f-95e6-b47d9741e7a2", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "completed"}]}}]}, "options": {}}, "typeVersion": 3.2}, {"id": "4a1468b4-d183-423f-8360-93044bcf323a", "name": "Get the Second Video Task", "type": "n8n-nodes-base.httpRequest", "position": [4160, 420], "parameters": {"url": "=https://api.piapi.ai/api/v1/task/{{ $('Get Video Task IDs').item.json.video_task2 }}", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-api-key", "value": "={{ $('Basic Params').first().json['x-api-key'] }}"}]}}, "typeVersion": 4.2}, {"id": "ff51a9b2-f17a-494d-afa7-a4fc0137d239", "name": "Get the Third Video Task", "type": "n8n-nodes-base.httpRequest", "position": [4840, 420], "parameters": {"url": "=https://api.piapi.ai/api/v1/task/{{ $('Get Video Task IDs').item.json.video_task3 }}", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-api-key", "value": "={{ $('Basic Params').first().json['x-api-key'] }}"}]}}, "typeVersion": 4.2}, {"id": "3db3e156-67ca-41cd-b9f2-5a03330ab33c", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [20, -280], "parameters": {"width": 360, "content": "## Basic Setting Instruction\nFill in **Basic Params** based on user's need. \nThe node concludes three section: style, character and situation_keyword.\nGPT-4o-mini will generate image description based on basic params mentioned above."}, "typeVersion": 1}, {"id": "31419df4-39d4-4393-8e80-7cdceb24778e", "name": "When clicking Test workflow", "type": "n8n-nodes-base.manualTrigger", "position": [20, -80], "parameters": {}, "typeVersion": 1}, {"id": "e0510fc2-4570-4cd6-8438-42a7acd26414", "name": "GPT-4o-mini Generate Image Scenario Prompt", "type": "n8n-nodes-base.httpRequest", "position": [420, -80], "parameters": {"url": "https://api.piapi.ai/v1/chat/completions", "method": "POST", "options": {}, "jsonBody": "={\n    \"model\": \"gpt-4o-mini\",\n    \"messages\": [\n       {\n          \"role\": \"system\",\n          \"content\": \"You are a helpful assistant.\"\n        },\n        {\n          \"role\": \"user\",\n          \"content\": \"Character is {{ $json.character }}, style is {{ $json.style }}, context keywords are {{ $json.situational_keywords }}, please generate a short story containing relevant elements and styles based on Character and context keywords. It should be concise and clear and divided into three sections. The return format is {'title':'','prompt1':'','prompt2':'','prompt3':''}, directly return json data format, not markdo2t include unusual characters such as hyphens,The key and value are enclosed in double quotes, but if you need to use quotes in the value string, please use single quotes.\"\n        }\n      ]\n  }", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{ $('Basic Params').first().json['x-api-key'] }}"}]}}, "typeVersion": 4.2}, {"id": "f94354e2-066d-4bf0-ada1-edade772852d", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [180, 660], "parameters": {"height": 120, "content": "### Generate the first image\nThis workflow section is uesd to generate the first Midjourney image. "}, "typeVersion": 1}, {"id": "86e4d54d-8046-4400-a327-b04e8f12a09c", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [880, 660], "parameters": {"height": 120, "content": "### Generate the second image\nThis workflow section is uesd to generate the second Midjourney image. "}, "typeVersion": 1}, {"id": "950a3dcb-83dc-4ee1-abb1-b33927edf1c2", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1660, 680], "parameters": {"height": 120, "content": "### Generate the third image\nThis workflow section is uesd to generate the third Midjourney image. "}, "typeVersion": 1}, {"id": "0a179107-17a4-4e38-998d-5845104199d0", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [2960, -120], "parameters": {"height": 120, "content": "### Generate three Kling videos\nThis section is used to generate three Kliong videos."}, "typeVersion": 1}, {"id": "dc589738-4130-4686-a4e2-cf07d9d22013", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [5640, 400], "parameters": {"content": "### Combine Videos in Creatomate\nSet a video template in Creatomate, then set the node with instruction."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "d16d0b20-c14e-4d0e-83ea-467e0d7c8280", "connections": {"output1": {"main": [[{"node": "Get the Second Video Task", "type": "main", "index": 0}]]}, "output2": {"main": [[{"node": "Get the Third Video Task", "type": "main", "index": 0}]]}, "Get Prompt": {"main": [[{"node": "Midjourney Generator of the First Image", "type": "main", "index": 0}]]}, "Basic Params": {"main": [[{"node": "GPT-4o-mini Generate Image Scenario Prompt", "type": "main", "index": 0}]]}, "Get Video Task IDs": {"main": [[{"node": "Wait for Video Generation for at Least 4 min", "type": "main", "index": 0}]]}, "Get three Image URLs": {"main": [[{"node": "Generate the First Video", "type": "main", "index": 0}]]}, "Get the First Image URL": {"main": [[{"node": "Midjourney Generator of the Second Image", "type": "main", "index": 0}]]}, "Get the Third Image URL": {"main": [[{"node": "Get three Image URLs", "type": "main", "index": 0}]]}, "Get the Third Video URL": {"main": [[{"node": "Final Video Combination", "type": "main", "index": 0}]]}, "Generate the First Video": {"main": [[{"node": "Generate the Second Video", "type": "main", "index": 0}]]}, "Generate the Third Video": {"main": [[{"node": "Get Video Task IDs", "type": "main", "index": 0}]]}, "Get the First Video Task": {"main": [[{"node": "Verify the First Video Generation", "type": "main", "index": 0}]]}, "Get the Second Image URL": {"main": [[{"node": "Midjourney Generator of the Third Image", "type": "main", "index": 0}]]}, "Get the Third Video Task": {"main": [[{"node": "Verify the Third Video Generation", "type": "main", "index": 0}]]}, "Generate the Second Video": {"main": [[{"node": "Generate the Third Video", "type": "main", "index": 0}]]}, "Get the Second Video Task": {"main": [[{"node": "Verify the Second Video Generation", "type": "main", "index": 0}]]}, "Get Task of the First Image": {"main": [[{"node": "Verify the first image generation status", "type": "main", "index": 0}]]}, "Get Task of the Third Image": {"main": [[{"node": "Verify the third image generation status", "type": "main", "index": 0}]]}, "When clicking Test workflow": {"main": [[{"node": "Basic Params", "type": "main", "index": 0}]]}, "Get Task of the Second Image": {"main": [[{"node": "Verify the second image generation status", "type": "main", "index": 0}]]}, "Get Task ID of the First Image": {"main": [[{"node": "Wait for the First Image Generation", "type": "main", "index": 0}]]}, "Get the First Video Generation": {"main": [[{"node": "output1", "type": "main", "index": 0}]]}, "Get the Third Video Generation": {"main": [[{"node": "Get the Third Video URL", "type": "main", "index": 0}]]}, "Get the Second Video Generation": {"main": [[{"node": "output2", "type": "main", "index": 0}]]}, "Verify the First Video Generation": {"main": [[{"node": "Get the First Video Generation", "type": "main", "index": 0}], [{"node": "Wait for the First Video Generation", "type": "main", "index": 0}]]}, "Verify the Third Video Generation": {"main": [[{"node": "Get the Third Video Generation", "type": "main", "index": 0}], [{"node": "Wait for the Third Video Generation", "type": "main", "index": 0}]]}, "Verify the Second Video Generation": {"main": [[{"node": "Get the Second Video Generation", "type": "main", "index": 0}], [{"node": "Wait for the Second Video Generation", "type": "main", "index": 0}]]}, "Wait for the First Image Generation": {"main": [[{"node": "Get Task of the First Image", "type": "main", "index": 0}]]}, "Wait for the First Video Generation": {"main": [[{"node": "Get the First Video Task", "type": "main", "index": 0}]]}, "Wait for the Third Image Generation": {"main": [[{"node": "Get Task of the Third Image", "type": "main", "index": 0}]]}, "Wait for the Third Video Generation": {"main": [[{"node": "Get the Third Video Task", "type": "main", "index": 0}]]}, "Wait for the Second Image Generation": {"main": [[{"node": "Get Task of the Second Image", "type": "main", "index": 0}]]}, "Wait for the Second Video Generation": {"main": [[{"node": "Get the Second Video Task", "type": "main", "index": 0}]]}, "Get the First Image Generation Status": {"main": [[{"node": "Get the First Image URL", "type": "main", "index": 0}]]}, "Get the Third Image Generation Status": {"main": [[{"node": "Get the Third Image URL", "type": "main", "index": 0}]]}, "Get the Second Image Generation Status": {"main": [[{"node": "Get the Second Image URL", "type": "main", "index": 0}]]}, "Midjourney Generator of the First Image": {"main": [[{"node": "Get Task ID of the First Image", "type": "main", "index": 0}]]}, "Midjourney Generator of the Third Image": {"main": [[{"node": "Wait for the Third Image Generation", "type": "main", "index": 0}]]}, "Midjourney Generator of the Second Image": {"main": [[{"node": "Wait for the Second Image Generation", "type": "main", "index": 0}]]}, "Verify the first image generation status": {"main": [[{"node": "Get the First Image Generation Status", "type": "main", "index": 0}], [{"node": "Wait for the First Image Generation", "type": "main", "index": 0}]]}, "Verify the third image generation status": {"main": [[{"node": "Get the Third Image Generation Status", "type": "main", "index": 0}], [{"node": "Wait for the Third Image Generation", "type": "main", "index": 0}]]}, "Verify the second image generation status": {"main": [[{"node": "Get the Second Image Generation Status", "type": "main", "index": 0}], [{"node": "Wait for the Second Image Generation", "type": "main", "index": 0}]]}, "GPT-4o-mini Generate Image Scenario Prompt": {"main": [[{"node": "Get Prompt", "type": "main", "index": 0}]]}, "Wait for Video Generation for at Least 4 min": {"main": [[{"node": "Get the First Video Task", "type": "main", "index": 0}]]}}}