{"id": "VaU41OXvni95OlAL", "meta": {"instanceId": "1bc0f4fa5e7d17ac362404cbb49337e51e5061e019cfa24022a8667c1f1ce287", "templateCredsSetupCompleted": true}, "name": "address validation", "tags": [], "nodes": [{"id": "c6e389ae-6db2-416b-8f6f-91749fbc860f", "name": "get order data", "type": "n8n-nodes-base.httpRequest", "position": [2500, 880], "parameters": {"url": "=https://api.billbee.io/api/v1/orders/{{ $json.orderID }}", "options": {}, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth", "headerParameters": {"parameters": [{"name": "X-<PERSON><PERSON>-Api-Key", "value": "={{ $json['X-Billbee-Api-Key'] }}"}]}}, "typeVersion": 4.1}, {"id": "1b27594b-af74-4c25-bb4f-27550bcd152e", "name": "Split Out Order Data", "type": "n8n-nodes-base.splitOut", "position": [2660, 880], "parameters": {"options": {}, "fieldToSplitOut": "Data.ShippingAddress.BillbeeId, Data.ShippingAddress.FirstName, Data.ShippingAddress.LastName, Data.ShippingAddress.Street, Data.ShippingAddress.HouseNumber, Data.ShippingAddress.Zip, Data.ShippingAddress.City, Data.ShippingAddress.CountryISO2, Data.ShippingAddress.Line2, Data.ShippingAddress.NameAddition"}, "typeVersion": 1}, {"id": "43808f6f-815d-419c-9e6f-c12d436108f2", "name": "Set Address Fields", "type": "n8n-nodes-base.set", "position": [2820, 880], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "dbda7791-09eb-4ae9-b1e8-7ce8582a5b4a", "name": "first_name", "type": "string", "value": "={{ $json['Data.ShippingAddress.FirstName'] }}"}, {"id": "1d13d702-b422-48c4-be04-db7869776897", "name": "family_name", "type": "string", "value": "={{ $json['Data.ShippingAddress.LastName'] }}"}, {"id": "9169466f-5639-4b58-80d3-c041ccea5e21", "name": "Street", "type": "string", "value": "={{ $json['Data.ShippingAddress.Street'] }}"}, {"id": "ea20b727-83c0-4c23-94c7-29f4f57eda78", "name": "housenumber", "type": "string", "value": "={{ $json['Data.ShippingAddress.HouseNumber'].replace(\"/\",\"\")}}"}, {"id": "81c3ebb0-6975-4b69-93f1-42dba7f2f60b", "name": "zip", "type": "string", "value": "={{ $json['Data.ShippingAddress.Zip'] }}"}, {"id": "2f1a6786-d48b-4475-805e-1db2fef2b5c3", "name": "location", "type": "string", "value": "={{ $json['Data.ShippingAddress.City'] }}"}, {"id": "4b6a4eb2-8867-4d9e-a4fb-32b66f466b58", "name": "Billbee<PERSON>", "type": "string", "value": "={{ $('Webhook').item.json.query.Id }}"}, {"id": "814513e9-9e56-4fb8-84bc-fd01456af443", "name": "BillbeeShippingAddressID", "type": "string", "value": "={{ $json['Data.ShippingAddress.BillbeeId'] }}"}, {"id": "bd45c9b8-d9fb-4d3f-be13-d202b8a3430d", "name": "CountryISO2", "type": "string", "value": "={{ $json['Data.ShippingAddress.CountryISO2'] }}"}, {"id": "c8e08606-d860-4482-8b4e-c68fe4c1974f", "name": "AddressLine2", "type": "string", "value": "={{ $json['Data.ShippingAddress.Line2'] }}"}, {"id": "fe1cb8a4-6c21-4505-8337-e31f07386a8c", "name": "NameAddition", "type": "string", "value": "={{ $json['Data.ShippingAddress.NameAddition'] }}"}]}}, "typeVersion": 3.3}, {"id": "3e5f3fc5-d3e2-4f4a-978e-795893e016cc", "name": "Check Address endereco api", "type": "n8n-nodes-base.httpRequest", "position": [4140, 760], "parameters": {"url": "https://endereco-service.de/rpc/v1", "method": "POST", "options": {}, "jsonBody": "={\n  \"jsonrpc\": \"2.0\",\n  \"id\": 1,\n  \"method\": \"addressCheck\",\n  \"params\": {\n    \"country\": \"{{ $json['CountryISO2']}}\",\n    \"language\": \"{{ $json[\"CountryISO2\"] }}\",\n    \"postCode\": \"{{ $json[\"zip\"] }}\",\n    \"cityName\": \"{{ $json[\"location\"] }}\",\n    \"street\": \"{{ $json[\"Street\"] }}\",\n    \"houseNumber\": \"{{ $json[\"housenumber\"] }}\"\n  }\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "X-Auth-Key", "value": "={{ $('ConfigNode').item.json['X-Auth-Key-Endereco'] }}"}, {"name": "Content-Type", "value": "application/json"}, {"name": "X-Transaction-Id", "value": "not_required"}, {"name": "X-Transaction-Referer", "value": "n8n"}, {"name": "X-<PERSON>", "value": "n8n"}]}}, "typeVersion": 4.2}, {"id": "3b02a11e-719b-41ef-b84f-b1e06d83854a", "name": "Split Out Corrected Address", "type": "n8n-nodes-base.splitOut", "position": [4600, 720], "parameters": {"options": {}, "fieldToSplitOut": "result.predictions"}, "typeVersion": 1}, {"id": "a3b6135a-6a0e-4c37-95ef-6e33f14c5e74", "name": "set new delivery address to billbee", "type": "n8n-nodes-base.httpRequest", "position": [4920, 720], "parameters": {"url": "=https://api.billbee.io/api/v1/customers/addresses/{{ $('Set Address Fields').item.json[\"BillbeeShippingAddressID\"] }}", "method": "PATCH", "options": {}, "sendBody": true, "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "Housenumber", "value": "={{ $json.houseNumber }}"}, {"name": "Street", "value": "={{ $json.street }}"}, {"name": "Zip", "value": "={{ $json.postCode }}"}, {"name": "City", "value": "={{ $json.cityName }}"}]}, "genericAuthType": "httpBasicAuth", "headerParameters": {"parameters": [{"name": "X-<PERSON><PERSON>-Api-Key", "value": "={{ $('ConfigNode').item.json['X-Billbee-Api-Key'] }}"}]}}, "typeVersion": 4.1}, {"id": "b170217c-b2d7-4514-b070-403e29964e4b", "name": "Wait", "type": "n8n-nodes-base.wait", "position": [2120, 960], "webhookId": "0f7b87d2-ec90-4f54-9971-31e564206980", "parameters": {"amount": 1}, "typeVersion": 1.1}, {"id": "17ea8895-05cd-4ffd-af31-aace970f8073", "name": "Wait1", "type": "n8n-nodes-base.wait", "position": [4760, 720], "webhookId": "b7a0738c-0890-45f5-a435-bc9d9a9062bb", "parameters": {"amount": 1}, "typeVersion": 1.1}, {"id": "d8d005e4-3b94-49d6-82dc-2919ca69dd2f", "name": "check if new address is not empty", "type": "n8n-nodes-base.if", "position": [4320, 760], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "2a9d055a-4607-4e87-bb6a-ecc1a31826e0", "operator": {"type": "array", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.result.predictions }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "3ad15e79-e4c8-4adf-90a5-aaf61cfe4825", "name": "set billbee tag", "type": "n8n-nodes-base.httpRequest", "position": [4580, 920], "parameters": {"url": "=https://api.billbee.io/api/v1/orders/{{ $('Set Address Fields').item.json[\"BillbeeID\"] }}/tags", "method": "POST", "options": {}, "jsonBody": "{\n  \"Tags\": [\n    \"endereco_address_failed\"\n  ]\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth", "headerParameters": {"parameters": [{"name": "X-<PERSON><PERSON>-Api-Key", "value": "={{ $('ConfigNode').item.json['X-Billbee-Api-Key'] }}"}]}}, "typeVersion": 4.1}, {"id": "6b0e5cfb-95d5-43a0-a665-6b8db6b6ad98", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [2260, 820], "parameters": {"color": 4, "width": 481, "height": 198, "content": "## Get and Prepare O<PERSON> Data"}, "typeVersion": 1}, {"id": "a2524895-b0dd-492b-b425-548ccbabf5c2", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [460, 460], "parameters": {"color": 7, "width": 1110.4301052736698, "height": 544.444950562247, "content": "# **Address Validation Workflow**\n### **Requirements**\n- **Billbee Developer API Key**: Request via email: `<EMAIL>`.\n- **Billbee User API Key**: Found in the **Billbee settings**.\n- **Endereco API Key**: Register at [Endereco API](https://www.endereco.de/en/integrations/address-api/). **30-Day Free Trial available**.\n## **About**\nThis workflow automates the process of validating client shipping addresses, saving time and reducing errors. Your **warehouse team** will appreciate it!\n## **How it Works**\n1. **Start**: The workflow is triggered by a **Webhook** sent from <PERSON><PERSON> (see configuration). The webhook provides the **Order ID**.\n2. **Retrieve Order Data**: Use the Order ID to call the **Billbee Order Endpoint**. This fetches the client's shipping address.\n3. **Data Mapping and Manipulation**: Rename and map the fields. Apply optional filters and data adjustments.\n3.1 **Validating the housenumber** (Most common error)\n4. **Validate Address**: Send the shipping address to the **Endereco API**. The API validates the address and, if necessary, suggests a corrected address.\n5. **Conditional Check**: **Was a new address suggested?** **Yes**: Update the client’s shipping address in Billbee. **No**: Add a **\"Validation Error\"** tag to the Billbee order.\n6. **Tag the Order**: For successfully validated orders, add a tag in Billbee to mark them as processed.\n### **Benefits**\n- **Time-Saving**: Automates address validation, reducing manual effort.\n- **Error Reduction**: Identifies incorrect addresses and suggests corrections automatically.\n- **Transparency**: Tracks the validation status with tags in Billbee.\n"}, "typeVersion": 1}, {"id": "31bb6e73-e702-4577-8b7f-a9850e80cbaf", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1060, 1040], "parameters": {"width": 276, "height": 219, "content": "## API Docs\n\nEndereco:\nhttps://github.com/Endereco/enderecoservice_api\n\nBillbee:\nhttps://app.billbee.io//swagger/ui/index\n"}, "typeVersion": 1}, {"id": "4bb9d0c1-0838-449b-bb1e-c4912173d9df", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [460, 1040], "parameters": {"color": 5, "width": 574.5277463210057, "height": 573.7065374509425, "content": "### Bilbee Setup \n### **Rule Settings**\n- **Name of the Rule:** Endereco Address Validation\n- **Comment:** *No comment provided.*\n- **Active:** ✅ Active\n- **Stop Rule Processing After This Rule:** ⬜ Disabled\n### **Trigger:**\n- **Event:** Order imported  \n- **Import Type:** All import types\n### **Conditions:**\n- **Restrict Shop:** Rule applies only if the order comes from one of the defined shops.\n### **Actions:**\n- **Call External URL:**  \n  - **URL:** `YOUR_N8N_WEBHOOK_LINK?Id={OrderId}`\nThis rule ensures that for every imported order from a specific shop, the external webhook URL is triggered with the `OrderId` as a parameter.\n\n### Option 2\n\nIf order state gets changed to \"gepackt\", \"In Fulfillment\" by this you can manually correct the orders within Billbee as most address are fine. Make sure that the State does not trigger a different automation"}, "typeVersion": 1}, {"id": "ad409a55-db7f-4699-9d56-98d7a2164afe", "name": "check if housenumer is not empty", "type": "n8n-nodes-base.if", "position": [3260, 880], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "5dbd8016-9c70-4cd8-9c7b-22b6779d7ae3", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.housenumber }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "0131cf4e-983b-4cc5-8305-e4a644f9e700", "name": "check if addressline 2 contains number", "type": "n8n-nodes-base.if", "position": [3420, 980], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "e758c0d9-caf6-40e8-9ceb-cd786e346709", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.AddressLine2.isNumeric() }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "14d67019-a6c2-4ad4-9c0e-383ee3e1f3e9", "name": "Filter", "type": "n8n-nodes-base.filter", "position": [1360, 1320], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "13c4f784-fb7a-4a61-b106-eb92dbc8f2d0", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "851d9d42-2b50-4a40-8d46-7d3decf897c2", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1080, 1300], "parameters": {"color": 3, "height": 239.63602562365423, "content": "## Include Filter \nYou want to filter out PickUp Shops or Parcel Stations for example in Germany:\n\n\"Postfiliale, Paketshop, Packstation\"\n\nThis can also be set up within Billbee"}, "typeVersion": 1}, {"id": "a07c7816-bcb6-457d-a621-ccfebcc384ad", "name": "set value of addressline2 as housenumber", "type": "n8n-nodes-base.set", "position": [3600, 900], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "7c21cf08-4ae8-4856-ae2f-0f25053aebde", "name": "housenumber", "type": "string", "value": "={{ $json.AddressLine2 }}"}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "c22fb34a-252f-4570-b576-089bb3243bfd", "name": "Filter Out PickUpShops", "type": "n8n-nodes-base.filter", "position": [3040, 880], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "or", "conditions": [{"id": "b6bf1576-9082-446b-9072-13130bf7d724", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.Street }}", "rightValue": "Postfiliale"}, {"id": "f7e18eb3-a3df-49df-adb4-d9c807963478", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.Street }}", "rightValue": "Packstation"}, {"id": "51c548d1-1eed-4caf-b32c-402b8ce73042", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.Street }}", "rightValue": "Paketshop"}]}}, "typeVersion": 2.2}, {"id": "b2b3c72a-d3d0-467f-8f60-17f40c7a3650", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [3020, 820], "parameters": {"color": 3, "width": 155.04025478630723, "height": 185.20127393153615, "content": "## Open ME!"}, "typeVersion": 1}, {"id": "ea2e9abf-1461-4754-b663-83e771207627", "name": "check if addressline 2 contains number and letter", "type": "n8n-nodes-base.if", "position": [3560, 1080], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "loose"}, "combinator": "and", "conditions": [{"id": "c82c2273-b34c-42e1-871d-31db72d2ad49", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json[\"AddressLine2\"].match(/^(?=.*\\d)(?=.*[A-Za-z]).+$/) !== null }}\n", "rightValue": ""}]}, "looseTypeValidation": true}, "typeVersion": 2.2}, {"id": "b532b22f-421e-4bd8-8241-ca559e77c3ca", "name": "set billbee tag manual check", "type": "n8n-nodes-base.httpRequest", "position": [3780, 1200], "parameters": {"url": "=https://api.billbee.io/api/v1/orders/{{ $('Set Address Fields').item.json[\"BillbeeID\"] }}/tags", "method": "POST", "options": {}, "jsonBody": "{\n  \"Tags\": [\n    \"manual_address_check\"\n  ]\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth", "headerParameters": {"parameters": [{"name": "X-<PERSON><PERSON>-Api-Key", "value": "={{ $('ConfigNode').item.json['X-Billbee-Api-Key'] }}"}]}}, "typeVersion": 4.1}, {"id": "bdb10514-2fa7-4727-a8e7-aa8394fced6f", "name": "set value of addressline2 as housenumber number+letter", "type": "n8n-nodes-base.set", "position": [3760, 1020], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "7c21cf08-4ae8-4856-ae2f-0f25053aebde", "name": "housenumber", "type": "string", "value": "={{ $json.AddressLine2 }}"}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "cf0a516f-f019-40f8-8d09-ff02a034781d", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [3000, 760], "parameters": {"color": 5, "width": 907.6568579769853, "height": 627.257034553087, "content": "## House Number Validation"}, "typeVersion": 1}, {"id": "82a16bec-77e9-4717-8111-69f8f068c925", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [4040, 700], "parameters": {"color": 4, "width": 1325.4150814203485, "height": 354.5727675883748, "content": "## Address Validation & Correction"}, "typeVersion": 1}, {"id": "07f54a0d-2b13-4996-95a4-4c225402abe1", "name": "set billbee success", "type": "n8n-nodes-base.httpRequest", "position": [5080, 720], "parameters": {"url": "=https://api.billbee.io/api/v1/orders/{{ $('Set Address Fields').item.json[\"BillbeeID\"] }}/tags", "method": "POST", "options": {}, "jsonBody": "{\n  \"Tags\": [\n    \"endereco_address_validated\"\n  ]\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth", "headerParameters": {"parameters": [{"name": "X-<PERSON><PERSON>-Api-Key", "value": "={{ $('ConfigNode').item.json['X-Billbee-Api-Key'] }}"}]}}, "typeVersion": 4.1}, {"id": "bd2f3340-f389-48e1-a90d-1625b6845556", "name": "ConfigNode", "type": "n8n-nodes-base.set", "position": [1860, 960], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c4d1415a-636b-4673-bba5-699168af2b2e", "name": "X-<PERSON><PERSON>-Api-Key", "type": "string", "value": "INSERT BILLBEE DEVELOPER API KEY"}, {"id": "69c630d7-d64c-49be-a594-88b05d44a091", "name": "X-Auth-Key-Endereco", "type": "string", "value": "INSERT ENDERECO API KEY"}, {"id": "75977810-a10a-45ea-b536-d4b8f0f59b15", "name": "orderID", "type": "string", "value": "={{ $json.query.Id }}"}]}}, "typeVersion": 3.4}, {"id": "7619e573-116a-4f87-b6d2-b652ee7a25b7", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [1800, 1120], "parameters": {"color": 3, "height": 251.61012258936577, "content": "### **Setup**\n\n**Create an Basic Auth for BillbeeUser**\n-E-Mail as Username, Password User API Key\n\nPaste your Billbee Developer Key (X Key) and Endereco API Key\n\n"}, "typeVersion": 1}, {"id": "31c17f6e-0e90-4db1-9048-4b13bd36cc90", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [2500, 1100], "parameters": {"color": 3, "width": 150, "height": 135.6842625042993, "content": "### **Setup**\n\nSelect your BillbeeUser Basic Auth \n\n"}, "typeVersion": 1}, {"id": "ca1e1a8b-6107-4e0c-81c4-2a3b715aed11", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [1640, 960], "webhookId": "786e8a93-9837-44e6-81ae-a173ce25a14f", "parameters": {"path": "786e8a93-9837-44e6-81ae-a173ce25a14f", "options": {}}, "typeVersion": 2}], "active": false, "pinData": {"Webhook": [{"json": {"body": {}, "query": {"Id": "300000221273261"}, "params": {}, "headers": {"host": "sfx-ecommerce.app.n8n.cloud", "x-real-ip": "************", "tracestate": "dd=s:-1", "traceparent": "00-00000000000000004c0234c4a8ce641b-3f1af42f856c7eb3-00", "accept-encoding": "gzip", "x-forwarded-for": "************", "x-forwarded-host": "sfx-ecommerce.app.n8n.cloud", "x-forwarded-port": "443", "x-forwarded-proto": "https", "x-datadog-trace-id": "5476998116086277147", "x-forwarded-server": "traefik-78bdf4fd45-vvczp", "x-datadog-parent-id": "4547215258723057331", "x-datadog-sampling-priority": "-1"}, "webhookUrl": "https://sfx-ecommerce.app.n8n.cloud/webhook/786e8a93-9837-44e6-81ae-a173ce25a14f", "executionMode": "production"}}]}, "settings": {"executionOrder": "v1"}, "versionId": "1f498c7a-468a-48c4-b044-64455eb51aa2", "connections": {"Wait": {"main": [[{"node": "get order data", "type": "main", "index": 0}]]}, "Wait1": {"main": [[{"node": "set new delivery address to billbee", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "ConfigNode", "type": "main", "index": 0}]]}, "ConfigNode": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "get order data": {"main": [[{"node": "Split Out Order Data", "type": "main", "index": 0}]]}, "Set Address Fields": {"main": [[{"node": "Filter Out PickUpShops", "type": "main", "index": 0}]]}, "Split Out Order Data": {"main": [[{"node": "Set Address Fields", "type": "main", "index": 0}]]}, "Filter Out PickUpShops": {"main": [[{"node": "check if housenumer is not empty", "type": "main", "index": 0}]]}, "Check Address endereco api": {"main": [[{"node": "check if new address is not empty", "type": "main", "index": 0}]]}, "Split Out Corrected Address": {"main": [[{"node": "Wait1", "type": "main", "index": 0}]]}, "check if housenumer is not empty": {"main": [[{"node": "Check Address endereco api", "type": "main", "index": 0}], [{"node": "check if addressline 2 contains number", "type": "main", "index": 0}]]}, "check if new address is not empty": {"main": [[{"node": "Split Out Corrected Address", "type": "main", "index": 0}], [{"node": "set billbee tag", "type": "main", "index": 0}]]}, "set new delivery address to billbee": {"main": [[{"node": "set billbee success", "type": "main", "index": 0}]]}, "check if addressline 2 contains number": {"main": [[{"node": "set value of addressline2 as housenumber", "type": "main", "index": 0}], [{"node": "check if addressline 2 contains number and letter", "type": "main", "index": 0}]]}, "set value of addressline2 as housenumber": {"main": [[{"node": "Check Address endereco api", "type": "main", "index": 0}]]}, "check if addressline 2 contains number and letter": {"main": [[{"node": "set value of addressline2 as housenumber number+letter", "type": "main", "index": 0}], [{"node": "set billbee tag manual check", "type": "main", "index": 0}]]}, "set value of addressline2 as housenumber number+letter": {"main": [[{"node": "Check Address endereco api", "type": "main", "index": 0}]]}}}