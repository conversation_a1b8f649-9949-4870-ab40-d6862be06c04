{"id": "744G7emgZe0pXaPB", "meta": {"instanceId": "d868e3d040e7bda892c81b17cf446053ea25d2556fcef89cbe19dd61a3e876e9"}, "name": "Hacker News to Video Template - AlexK1919", "tags": [{"id": "04PL2irdWYmF2Dg3", "name": "RunwayML", "createdAt": "2024-11-15T05:55:30.783Z", "updatedAt": "2024-11-15T05:55:30.783Z"}, {"id": "yrY6updwSCXMsT0z", "name": "Video", "createdAt": "2024-11-15T05:55:34.333Z", "updatedAt": "2024-11-15T05:55:34.333Z"}, {"id": "QsH2EXuw2e7YCv0K", "name": "OpenAI", "createdAt": "2024-11-15T04:05:20.872Z", "updatedAt": "2024-11-15T04:05:20.872Z"}, {"id": "lvPj9rYRsKOHCi4J", "name": "Creatomate", "createdAt": "2024-11-19T15:59:16.134Z", "updatedAt": "2024-11-19T15:59:16.134Z"}, {"id": "9LXACqpQLNtrM6or", "name": "Leonardo", "createdAt": "2024-11-19T15:59:21.368Z", "updatedAt": "2024-11-19T15:59:21.368Z"}], "nodes": [{"id": "c777c41b-842d-4504-a1a0-ccbb034a0fdd", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-320, 300], "parameters": {}, "typeVersion": 1}, {"id": "74fafd7c-55a4-46ec-b4a8-33d46f2b5b54", "name": "Hacker News", "type": "n8n-nodes-base.hackerNews", "position": [-20, 300], "parameters": {"resource": "all", "additionalFields": {}}, "typeVersion": 1}, {"id": "9cd87fd2-6a38-463a-a22e-e0c34910818f", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [440, 300], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "611b24cd-558b-4025-a0a8-ea355ba61988", "name": "OpenAI Chat Model3", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [720, 580], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "f814682c-cf6f-49a8-8ea0-48fbc64a3ebe", "name": "HTTP Request1", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [900, 580], "parameters": {"url": "={{ $json.url }}", "toolDescription": "grab the article for the ai agent to use"}, "typeVersion": 1.1}, {"id": "2a4bcf69-23f0-440d-a3b0-c8261e153c62", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [1080, 580], "parameters": {"schemaType": "manual", "inputSchema": "{\n\t\"type\": \"object\",\n\t\"properties\": {\n\t\t\"summary\": {\n\t\t\t\"type\": \"string\"\n\t\t},\n\t\t\"related\": {\n\t\t\t\"type\": \"string\"\n\t\t},\n        \"image urls\": {\n\t\t\t\"type\": \"string\"\n        }\n\t}\n}"}, "typeVersion": 1.2}, {"id": "83c3b8f0-8d67-48a2-a5ce-b777ea1d7b32", "name": "Upload to Minio", "type": "n8n-nodes-base.s3", "position": [4240, 1080], "parameters": {"operation": "upload", "bucketName": "=", "additionalFields": {"grantRead": true, "parentFolderKey": "="}}, "typeVersion": 1}, {"id": "05b972ff-ccab-415b-8787-aafabb3b7292", "name": "News1", "type": "n8n-nodes-base.set", "position": [2180, 320], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "ec8013d5-84b5-43c8-abcb-6986ef15939d", "name": "property_name", "type": "string", "value": "={{ $json.message.content['Article Title'] }}"}, {"id": "4d91c4fc-12a2-4fe2-a58e-02284314e1de", "name": "property_text", "type": "string", "value": "={{ $json.message.content['Article Blurb'] }}"}, {"id": "cad2b795-8b71-415f-a100-700d9ec62bbd", "name": "property_image_url", "type": "string", "value": "={{ $('If Topic').item.json.output['image urls'] }}"}]}}, "typeVersion": 3.4}, {"id": "d175d366-e672-4452-b78e-a06336ef242b", "name": "Leo - Improve Prompt", "type": "n8n-nodes-base.httpRequest", "position": [2720, 100], "parameters": {"url": "https://cloud.leonardo.ai/api/rest/v1/prompt/improve", "method": "POST", "options": {"response": {"response": {"fullResponse": true}}}, "jsonBody": "={\n  \"prompt\": \"{{ $('Article Prep').item.json.message.content['Image Prompt 1'] }}\"\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpCustomAuth", "headerParameters": {"parameters": [{"name": "accept", "value": "application/json"}]}}, "typeVersion": 4.2}, {"id": "d8da7879-1a67-4da1-86db-f70e50b4e9da", "name": "Leo - Get imageId", "type": "n8n-nodes-base.httpRequest", "position": [3320, 100], "parameters": {"url": "=https://cloud.leonardo.ai/api/rest/v1/generations/{{ $json.body.sdGenerationJob.generationId }}", "options": {"response": {"response": {"fullResponse": true}}}, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpCustomAuth", "headerParameters": {"parameters": [{"name": "content-type", "value": "application/json"}]}}, "typeVersion": 4.2}, {"id": "faf80246-3b1a-49c6-a277-0152428e46e1", "name": "Runway - Create Video", "type": "n8n-nodes-base.httpRequest", "position": [2520, 300], "parameters": {"url": "https://api.dev.runwayml.com/v1/image_to_video", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "promptImage", "value": "={{ $json.body.generations_by_pk.generated_images[0].url }}"}, {"name": "promptText", "value": "string"}, {"name": "model", "value": "gen3a_turbo"}]}, "genericAuthType": "httpCustomAuth", "headerParameters": {"parameters": [{"name": "X-Runway-Version", "value": "2024-11-06"}]}}, "typeVersion": 4.2}, {"id": "e91c1f01-7870-4063-9557-24a6ba1d3db3", "name": "Runway - Get Video", "type": "n8n-nodes-base.httpRequest", "position": [2920, 300], "parameters": {"url": "=https://api.dev.runwayml.com/v1/tasks/{{ $json.id }}", "options": {}, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpCustomAuth", "headerParameters": {"parameters": [{"name": "X-Runway-Version", "value": "2024-11-06"}]}}, "typeVersion": 4.2}, {"id": "41ee2665-e1aa-4d48-ade6-e37af568f211", "name": "Wait2", "type": "n8n-nodes-base.wait", "position": [2720, 300], "webhookId": "ddca5833-a40b-404a-9140-686cd4fa26cb", "parameters": {"unit": "minutes", "amount": 3}, "typeVersion": 1.1}, {"id": "091e9e07-89ba-4fe3-9fc5-278fc333dbff", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-160, -40], "parameters": {"color": 5, "width": 341, "height": 951, "content": "# Choose your data source \n## This can be swapped for any other data source of your choosing."}, "typeVersion": 1}, {"id": "9660a593-9966-4ebe-bfd7-f884dc185d56", "name": "If Topic", "type": "n8n-nodes-base.if", "position": [1100, 320], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "56219de5-244d-4b7f-a511-f3061572cf93", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.output.related }}", "rightValue": "yes"}]}}, "typeVersion": 2.2}, {"id": "e47140ac-20cc-417b-a6cd-30f780dc8289", "name": "Get Image", "type": "n8n-nodes-base.httpRequest", "position": [1500, 320], "parameters": {"url": "={{ $('Article Analysis').first().json.output['image urls'] }}", "options": {"response": {"response": {"fullResponse": true}}}}, "typeVersion": 4.2}, {"id": "26f80f71-2c3a-46fe-a960-21cdbc18ce34", "name": "Prompt Settings1", "type": "n8n-nodes-base.set", "position": [2520, 100], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "56c8f20d-d9d9-4be7-ac2a-38df6ffdd722", "name": "model", "type": "string", "value": "6b645e3a-d64f-4341-a6d8-7a3690fbf042"}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "ce697f6f-f8fc-4ba7-b776-17bbc2e870b7", "name": "Leo - Generate Image", "type": "n8n-nodes-base.httpRequest", "position": [2920, 100], "parameters": {"url": "https://cloud.leonardo.ai/api/rest/v1/generations", "method": "POST", "options": {"response": {"response": {"fullResponse": true}}}, "jsonBody": "={\n  \"alchemy\": true,\n  \"width\": 1024,\n  \"height\": 768,\n  \"modelId\": \"6b645e3a-d64f-4341-a6d8-7a3690fbf042\",\n  \"num_images\": 1,\n  \"presetStyle\": \"MONOCHROME\",\n  \"prompt\": \"{{ $json.body.promptGeneration.prompt }}; Use the rule of thirds, leading lines, & balance. DO NOT INCLUDE ANY WORDS OR LABELS.\",\n  \"guidance_scale\": 7,\n  \"highResolution\": true,\n  \"promptMagic\": false,\n  \"promptMagicStrength\": 0.5,\n  \"promptMagicVersion\": \"v3\",\n  \"public\": false,\n  \"ultra\": false,\n  \"photoReal\": false,\n  \"negative_prompt\": \"\"\n} ", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpCustomAuth", "headerParameters": {"parameters": [{"name": "accept", "value": "application/json"}]}}, "typeVersion": 4.2}, {"id": "e2067fe5-3fae-4f97-97c0-879967efd9b8", "name": "Wait1", "type": "n8n-nodes-base.wait", "position": [3120, 100], "webhookId": "256c3814-6a52-4eb1-969a-30f9f3b8e04e", "parameters": {"amount": 30}, "typeVersion": 1.1}, {"id": "f0ba57a5-1d27-4c75-a422-4bc0e2cead9d", "name": "Limit", "type": "n8n-nodes-base.limit", "position": [240, 300], "parameters": {"keep": "lastItems", "maxItems": 50}, "typeVersion": 1}, {"id": "e01152aa-961b-4e33-a1e3-186d47d81c55", "name": "Image Analysis", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1300, 320], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {"detail": "auto"}, "resource": "image", "imageUrls": "={{ $json.output['image urls'] }}", "operation": "analyze"}, "credentials": {"openAiApi": {"id": "ysxujEYFiY5ozRTS", "name": "AlexK OpenAi Key"}}, "typeVersion": 1.6}, {"id": "ab346129-c3d5-4f51-af5e-5d63cd154981", "name": "Wait3", "type": "n8n-nodes-base.wait", "disabled": true, "position": [3080, 1020], "webhookId": "6e4a0b8d-6c31-4a98-8ec3-2509aa2087e8", "parameters": {"unit": "minutes"}, "typeVersion": 1.1}, {"id": "872c35a3-bdd5-4eec-9bac-0959f3ff78e7", "name": "Article Analysis", "type": "@n8n/n8n-nodes-langchain.agent", "onError": "continueErrorOutput", "position": [740, 300], "parameters": {"text": "=Can you tell me if the article at {{ $json.url }} is related to automation or ai? \n\nthen, create a 250 word summary of the article\n\nAlso, list any image url's related to the article content from the url. Limit to 1 image url.", "options": {"systemMessage": "You are a helpful assistant in summarizing and identifying articles related to automation and ai. \nOutput the results as:\nsummary: \nrelated: yes or no\nimage urls: "}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.7}, {"id": "31c3a90e-10ee-4217-9b08-ff57bf17ea10", "name": "Dropbox", "type": "n8n-nodes-base.dropbox", "position": [3640, 1080], "parameters": {}, "typeVersion": 1}, {"id": "22ccd0a0-f7f6-40ca-bd09-40ed4a7fcde1", "name": "Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [3840, 1080], "parameters": {"fileId": {"__rl": true, "mode": "list", "value": ""}, "options": {}, "operation": "update"}, "credentials": {"googleDriveOAuth2Api": {"id": "m8K1mbAUn7yuiEwl", "name": "AlexK1919 Google Drive account"}}, "typeVersion": 3}, {"id": "ea75931d-c1ee-4139-9bdc-7901056ba016", "name": "Microsoft OneDrive", "type": "n8n-nodes-base.microsoftOneDrive", "position": [4040, 1080], "parameters": {}, "typeVersion": 1}, {"id": "********-3087-4e0a-81d6-cf4b9a5dd3dd", "name": "YouTube", "type": "n8n-nodes-base.youTube", "position": [3640, 1500], "parameters": {"options": {}, "resource": "video", "operation": "upload"}, "typeVersion": 1}, {"id": "55f3decc-f952-4d2a-804d-2aec44fb2755", "name": "X", "type": "n8n-nodes-base.twitter", "position": [3840, 1500], "parameters": {"additionalFields": {}}, "typeVersion": 2}, {"id": "54c8b762-444d-4790-97a9-a2f84518492f", "name": "Instagram", "type": "n8n-nodes-base.httpRequest", "position": [4240, 1500], "parameters": {"options": {}}, "typeVersion": 4.2}, {"id": "90040f15-95c0-4ebb-818f-dde508eb0689", "name": "LinkedIn", "type": "n8n-nodes-base.linkedIn", "position": [4040, 1500], "parameters": {"additionalFields": {}}, "typeVersion": 1}, {"id": "691eb779-5fae-4f65-89eb-b1b8e5488809", "name": "Leo - Improve Prompt2", "type": "n8n-nodes-base.httpRequest", "position": [2720, 500], "parameters": {"url": "https://cloud.leonardo.ai/api/rest/v1/prompt/improve", "method": "POST", "options": {"response": {"response": {"fullResponse": true}}}, "jsonBody": "={\n  \"prompt\": \"{{ $('Article Prep').item.json.message.content['Image Prompt 2'] }}\"\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpCustomAuth", "headerParameters": {"parameters": [{"name": "accept", "value": "application/json"}]}}, "credentials": {"httpCustomAuth": {"id": "hIzUsjbtHLmIe6uM", "name": "RunwayML Custom Auth"}}, "typeVersion": 4.2}, {"id": "076a745a-055b-459c-8af9-fa7b6740dc6f", "name": "Wait4", "type": "n8n-nodes-base.wait", "position": [2720, 700], "webhookId": "89b31515-b403-4644-a2c1-970e5e774008", "parameters": {"unit": "minutes", "amount": 3}, "typeVersion": 1.1}, {"id": "adc2c993-3f89-40df-96fc-eb3ff5eafb1c", "name": "Wait6", "type": "n8n-nodes-base.wait", "position": [3120, 500], "webhookId": "2efb873f-bcbd-41d9-99da-b2b57ef5ad93", "parameters": {"amount": 30}, "typeVersion": 1.1}, {"id": "156f5735-bc20-46a9-871c-143b0772ca45", "name": "Leo - Generate Image2", "type": "n8n-nodes-base.httpRequest", "position": [2920, 500], "parameters": {"url": "https://cloud.leonardo.ai/api/rest/v1/generations", "method": "POST", "options": {"response": {"response": {"fullResponse": true}}}, "jsonBody": "={\n  \"alchemy\": true,\n  \"width\": 1024,\n  \"height\": 768,\n  \"modelId\": \"6b645e3a-d64f-4341-a6d8-7a3690fbf042\",\n  \"num_images\": 1,\n  \"presetStyle\": \"MONOCHROME\",\n  \"prompt\": \"{{ $json.body.promptGeneration.prompt }}; Use the rule of thirds, leading lines, & balance. DO NOT INCLUDE ANY WORDS OR LABELS.\",\n  \"guidance_scale\": 7,\n  \"highResolution\": true,\n  \"promptMagic\": false,\n  \"promptMagicStrength\": 0.5,\n  \"promptMagicVersion\": \"v3\",\n  \"public\": false,\n  \"ultra\": false,\n  \"photoReal\": false,\n  \"negative_prompt\": \"\"\n} ", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpCustomAuth", "headerParameters": {"parameters": [{"name": "accept", "value": "application/json"}]}}, "typeVersion": 4.2}, {"id": "4f270fa8-4da2-44f0-927f-3509fd9f8f7d", "name": "Leo - Get imageId2", "type": "n8n-nodes-base.httpRequest", "position": [3320, 500], "parameters": {"url": "=https://cloud.leonardo.ai/api/rest/v1/generations/{{ $json.body.sdGenerationJob.generationId }}", "options": {"response": {"response": {"fullResponse": true}}}, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpCustomAuth", "headerParameters": {"parameters": [{"name": "content-type", "value": "application/json"}]}}, "typeVersion": 4.2}, {"id": "49c0e7ba-bf9c-4819-b479-61aa099ab9ab", "name": "Runway - Create Video2", "type": "n8n-nodes-base.httpRequest", "position": [2520, 700], "parameters": {"url": "https://api.dev.runwayml.com/v1/image_to_video", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "promptImage", "value": "={{ $json.body.generations_by_pk.generated_images[0].url }}"}, {"name": "promptText", "value": "string"}, {"name": "model", "value": "gen3a_turbo"}]}, "genericAuthType": "httpCustomAuth", "headerParameters": {"parameters": [{"name": "X-Runway-Version", "value": "2024-11-06"}]}}, "credentials": {"httpCustomAuth": {"id": "hIzUsjbtHLmIe6uM", "name": "RunwayML Custom Auth"}}, "typeVersion": 4.2}, {"id": "d03eb190-5fc0-4b7e-ad65-88ece3ab833d", "name": "Runway - Get Video2", "type": "n8n-nodes-base.httpRequest", "position": [2920, 700], "parameters": {"url": "=https://api.dev.runwayml.com/v1/tasks/{{ $json.id }}", "options": {}, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpCustomAuth", "headerParameters": {"parameters": [{"name": "X-Runway-Version", "value": "2024-11-06"}]}}, "typeVersion": 4.2}, {"id": "0072563d-b87d-47c5-80fd-ed3c051b3287", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [3580, 940], "parameters": {"color": 6, "width": 882, "height": 372, "content": "# Upload Assets\nYou can extend this workflow further by uploading the generated assets to your storage option of choice."}, "typeVersion": 1}, {"id": "a0b2377e-57ea-47e9-83c9-3e58372610e5", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [3580, 1360], "parameters": {"color": 6, "width": 882, "height": 372, "content": "# Post to Social Media\nYou can extend this workflow further by posting the generated assets to social media."}, "typeVersion": 1}, {"id": "708fe6a0-4899-462b-9a08-fadea7c7e195", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [2420, -40], "parameters": {"color": 4, "width": 1114, "height": 943, "content": "# Generate Images and Videos"}, "typeVersion": 1}, {"id": "5bbb6552-ec3a-42ea-a911-993f67a6c8dc", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [2420, 940], "parameters": {"color": 5, "width": 1114, "height": 372, "content": "# Stitch it all together"}, "typeVersion": 1}, {"id": "25f4cc09-fbff-4c10-b706-30df5840b794", "name": "Cre - Generate Video1", "type": "n8n-nodes-base.httpRequest", "position": [2880, 1020], "parameters": {"url": "https://api.creatomate.com/v1/renders", "method": "POST", "options": {"response": {"response": {"fullResponse": true}}}, "jsonBody": "={\n  \"max_width\": 480,\n  \"template_id\": \"enterTemplateID\",\n  \"modifications\": {\n    \"Scenes.elements\": [\n      {\n        \"name\": \"Intro Comp\",\n        \"type\": \"composition\",\n        \"track\": 1,\n        \"elements\": [\n          {\n            \"name\": \"Image-1\",\n            \"type\": \"image\",\n            \"source\": \"{{ $('Leo - Get imageId').item.json.body.generations_by_pk.generated_images[0].url }}\"\n          },\n          {\n            \"name\": \"Subtitles-1\",\n            \"type\": \"text\",\n            \"transcript_source\": \"Voiceover-1\",\n            \"width\": \"86.66%\",\n            \"height\": \"37.71%\",\n            \"x_alignment\": \"50%\",\n            \"y_alignment\": \"50%\",\n            \"fill_color\": \"#ffffff\",\n            \"stroke_color\": \"#333333\",\n            \"stroke_width\": \"1.05 vmin\",\n            \"font_family\": \"Inter\",\n            \"font_weight\": \"700\",\n            \"font_size\": \"8 vmin\",\n            \"background_color\": \"rgba(255,255,255,0.2)\",\n            \"background_x_padding\": \"26%\",\n            \"background_y_padding\": \"7%\",\n            \"background_border_radius\": \"28%\",\n            \"transcript_effect\": \"highlight\",\n            \"transcript_color\": \"#ff5900\"\n          },\n          {\n            \"name\": \"Voiceover-1\",\n            \"type\": \"audio\",\n            \"source\": \"{{ $('News1').item.json.property_name }}\",\n            \"provider\": \"openai model=tts-1 voice=onyx\"\n          }\n        ]\n      },\n      {\n        \"name\": \"Auto Scene Comp\",\n        \"type\": \"composition\",\n        \"track\": 1,\n        \"elements\": [\n          {\n            \"name\": \"Video-2\",\n            \"type\": \"video\",\n            \"source\": \"{{ $('Runway - Get Video').first().json.output[0] }}\",\n            \"loop\": true\n          },\n          {\n            \"name\": \"Subtitles-2\",\n            \"type\": \"text\",\n            \"transcript_source\": \"Voiceover-2\",\n            \"y\": \"78.2173%\",\n            \"width\": \"86.66%\",\n            \"height\": \"37.71%\",\n            \"x_alignment\": \"50%\",\n            \"y_alignment\": \"50%\",\n            \"fill_color\": \"#ffffff\",\n            \"stroke_color\": \"#333333\",\n            \"stroke_width\": \"1.05 vmin\",\n            \"font_family\": \"Inter\",\n            \"font_weight\": \"700\",\n            \"font_size\": \"8 vmin\",\n            \"background_color\": \"rgba(255,255,255,0.2)\",\n            \"background_x_padding\": \"26%\",\n            \"background_y_padding\": \"7%\",\n            \"background_border_radius\": \"28%\",\n            \"transcript_effect\": \"highlight\",\n            \"transcript_color\": \"#ff5900\"\n          },\n          {\n            \"name\": \"Voiceover-2\",\n            \"type\": \"audio\",\n            \"source\": \"{{ $('Article Prep').item.json.message.content['Summary Blurb 1'] }}\",\n            \"provider\": \"openai model=tts-1 voice=onyx\"\n          }\n        ]\n      },\n      {\n        \"name\": \"Auto Scene Comp\",\n        \"type\": \"composition\",\n        \"track\": 1,\n        \"elements\": [\n          {\n            \"name\": \"Video-3\",\n            \"type\": \"video\",\n            \"source\": \"{{ $('Runway - Get Video2').first().json.output[0] }}\",\n            \"loop\": true\n          },\n          {\n            \"name\": \"Subtitles-3\",\n            \"type\": \"text\",\n            \"transcript_source\": \"Voiceover-3\",\n            \"y\": \"78.2173%\",\n            \"width\": \"86.66%\",\n            \"height\": \"37.71%\",\n            \"x_alignment\": \"50%\",\n            \"y_alignment\": \"50%\",\n            \"fill_color\": \"#ffffff\",\n            \"stroke_color\": \"#333333\",\n            \"stroke_width\": \"1.05 vmin\",\n            \"font_family\": \"Inter\",\n            \"font_weight\": \"700\",\n            \"font_size\": \"8 vmin\",\n            \"background_color\": \"rgba(255,89,0,0.5)\",\n            \"background_x_padding\": \"26%\",\n            \"background_y_padding\": \"7%\",\n            \"background_border_radius\": \"28%\",\n            \"transcript_effect\": \"highlight\",\n            \"transcript_color\": \"#ff0040\"\n          },\n          {\n            \"name\": \"Voiceover-3\",\n            \"type\": \"audio\",\n            \"source\": \"{{ $('Article Prep').item.json.message.content['Summary Blurb 2'] }}\",\n            \"provider\": \"openai model=tts-1 voice=onyx\"\n          }\n        ]\n      }\n    ]\n  }\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpCustomAuth"}, "credentials": {"httpCustomAuth": {"id": "hIzUsjbtHLmIe6uM", "name": "RunwayML Custom Auth"}}, "typeVersion": 4.2}, {"id": "7093de7b-a4e3-4363-8038-1002f7b20fbc", "name": "Cre - Get Video", "type": "n8n-nodes-base.httpRequest", "position": [3280, 1020], "parameters": {"url": "=https://api.creatomate.com/v1/renders/{{ $json.body.body[0].id }}", "options": {"response": {"response": {"fullResponse": true}}}, "authentication": "genericCredentialType", "genericAuthType": "httpCustomAuth"}, "credentials": {"httpCustomAuth": {"id": "hIzUsjbtHLmIe6uM", "name": "RunwayML Custom Auth"}}, "typeVersion": 4.2}, {"id": "a57b719f-b299-431e-9c85-fa333e38b6a7", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [660, -40], "parameters": {"color": 3, "width": 1033, "height": 951, "content": "# Article Analysis - Is it the right topic?"}, "typeVersion": 1}, {"id": "60b879a0-8b7f-40f1-ae70-ac94e4675b38", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [1740, -40], "parameters": {"color": 3, "width": 630, "height": 947, "content": "# Prepare the article for content generation"}, "typeVersion": 1}, {"id": "afaf8437-ee52-434b-a267-8dbaff0e1922", "name": "Article Prep", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1820, 320], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "messages": {"values": [{"content": "=prepare the following summary for a newsletter where the article will be 1 of several presented in the newsletter:\n\n{{ $('Article Analysis').first().json.output.summary }}\n\nMake sure the Article Blurb lenght is less than 15 words.\n\nThen, create 2 Summary Blurbs, making sure each is less than 15 words.\n\nAlso create 2 image prompts that is less than 15 words long for each Summary Blurb"}, {"role": "system", "content": "Output in markdown format\nArticle Title\nArticle Blurb\nSummary Blurb 1\nSummary Blurb 2\nArticle Image\nImage Prompt 1\nImage Prompt 2"}]}, "jsonOutput": true}, "credentials": {"openAiApi": {"id": "ysxujEYFiY5ozRTS", "name": "AlexK OpenAi Key"}}, "typeVersion": 1.6}, {"id": "e7c95d56-86e1-4456-a6d3-9c8b9fc3a53c", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-620, -40], "parameters": {"color": 6, "width": 252, "height": 946, "content": "# AlexK1919 \n![<PERSON>](https://media.licdn.com/dms/image/v2/D5603AQFOYMkqCPl6Sw/profile-displayphoto-shrink_400_400/profile-displayphoto-shrink_400_400/0/1718309808352?e=1736985600&v=beta&t=pQKm7lQfUU1ytuC2Gq1PRxNY-XmROFWbo-BjzUPxWOs)\n\n#### I’m <PERSON>, an AI-Native Workflow Automation Architect Building Solutions to Optimize your Personal and Professional Life.\n\n### Workflow Overview Video\nhttps://youtu.be/XaKybLDUlLk\n\n### About Me\nhttps://beacons.ai/alexk1919\n\n### Product Used \n[Leonardo.ai](https://leonardo.ai)\n[RunwayML](https://runwayml.com/)\n[Creatomate](https://creatomate.com/)\n"}, "typeVersion": 1}, {"id": "32e2803e-bf7c-4da4-a4ae-c9b6fa5ae226", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [3280, 1180], "parameters": {"color": 7, "width": 180, "height": 100, "content": "Don't forget to connect this last node to the loop to process additional items"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "c7ab1ecd-50cb-4e4b-b2f7-aade804bbd63", "connections": {"X": {"main": [[{"node": "LinkedIn", "type": "main", "index": 0}]]}, "Limit": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "News1": {"main": [[{"node": "Prompt Settings1", "type": "main", "index": 0}]]}, "Wait1": {"main": [[{"node": "Leo - Get imageId", "type": "main", "index": 0}]]}, "Wait2": {"main": [[{"node": "Runway - Get Video", "type": "main", "index": 0}]]}, "Wait3": {"main": [[{"node": "Cre - Get Video", "type": "main", "index": 0}]]}, "Wait4": {"main": [[{"node": "Runway - Get Video2", "type": "main", "index": 0}]]}, "Wait6": {"main": [[{"node": "Leo - Get imageId2", "type": "main", "index": 0}]]}, "Dropbox": {"main": [[{"node": "Google Drive", "type": "main", "index": 0}]]}, "YouTube": {"main": [[{"node": "X", "type": "main", "index": 0}]]}, "If Topic": {"main": [[{"node": "Image Analysis", "type": "main", "index": 0}], [{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "LinkedIn": {"main": [[{"node": "Instagram", "type": "main", "index": 0}]]}, "Get Image": {"main": [[{"node": "Article Prep", "type": "main", "index": 0}]]}, "Hacker News": {"main": [[{"node": "Limit", "type": "main", "index": 0}]]}, "Article Prep": {"main": [[{"node": "News1", "type": "main", "index": 0}]]}, "Google Drive": {"main": [[{"node": "Microsoft OneDrive", "type": "main", "index": 0}]]}, "HTTP Request1": {"ai_tool": [[{"node": "Article Analysis", "type": "ai_tool", "index": 0}]]}, "Image Analysis": {"main": [[{"node": "Get Image", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Article Analysis", "type": "main", "index": 0}]]}, "Article Analysis": {"main": [[{"node": "If Topic", "type": "main", "index": 0}], [{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Prompt Settings1": {"main": [[{"node": "Leo - Improve Prompt", "type": "main", "index": 0}]]}, "Leo - Get imageId": {"main": [[{"node": "Runway - Create Video", "type": "main", "index": 0}]]}, "Leo - Get imageId2": {"main": [[{"node": "Runway - Create Video2", "type": "main", "index": 0}]]}, "Microsoft OneDrive": {"main": [[{"node": "Upload to Minio", "type": "main", "index": 0}]]}, "OpenAI Chat Model3": {"ai_languageModel": [[{"node": "Article Analysis", "type": "ai_languageModel", "index": 0}]]}, "Runway - Get Video": {"main": [[{"node": "Leo - Improve Prompt2", "type": "main", "index": 0}]]}, "Runway - Get Video2": {"main": [[{"node": "Cre - Generate Video1", "type": "main", "index": 0}]]}, "Leo - Generate Image": {"main": [[{"node": "Wait1", "type": "main", "index": 0}]]}, "Leo - Improve Prompt": {"main": [[{"node": "Leo - Generate Image", "type": "main", "index": 0}]]}, "Cre - Generate Video1": {"main": [[{"node": "Wait3", "type": "main", "index": 0}]]}, "Leo - Generate Image2": {"main": [[{"node": "Wait6", "type": "main", "index": 0}]]}, "Leo - Improve Prompt2": {"main": [[{"node": "Leo - Generate Image2", "type": "main", "index": 0}]]}, "Runway - Create Video": {"main": [[{"node": "Wait2", "type": "main", "index": 0}]]}, "Runway - Create Video2": {"main": [[{"node": "Wait4", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Article Analysis", "type": "ai_outputParser", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Hacker News", "type": "main", "index": 0}]]}}}