{"id": "32", "name": "Create a customer and add them to a segment in Customer.io", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [440, 260], "parameters": {}, "typeVersion": 1}, {"name": "CustomerIo", "type": "n8n-nodes-base.customerIo", "position": [650, 260], "parameters": {"id": "2", "additionalFields": {"customProperties": {"customProperty": [{"key": "Name", "value": "n8n"}]}}}, "credentials": {"customerIoApi": "cust"}, "typeVersion": 1}, {"name": "CustomerIo1", "type": "n8n-nodes-base.customerIo", "position": [840, 260], "parameters": {"resource": "segment", "customerIds": "={{$node[\"CustomerIo\"].json[\"id\"]}}"}, "credentials": {"customerIoApi": "cust"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"CustomerIo": {"main": [[{"node": "CustomerIo1", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "CustomerIo", "type": "main", "index": 0}]]}}}