{"id": "pdgNdag49lwoTxUP", "meta": {"instanceId": "46264913bc099c31e7222b2cfd112772e1c7867192afd7716e58254079b3333f", "templateCredsSetupCompleted": true}, "name": "Track Working Time and Pauses", "tags": [], "nodes": [{"id": "1ae951f1-acfa-4bd2-800e-22c7628e862d", "name": "Create new page", "type": "n8n-nodes-base.notion", "position": [1260, -120], "parameters": {"title": "Tracked Time via n8n", "options": {"icon": "🤖"}, "resource": "databasePage", "databaseId": {"__rl": true, "mode": "list", "value": "1117f2f5-baf9-8054-b33b-efb4d8a3c7ab", "cachedResultUrl": "https://www.notion.so/1117f2f5baf98054b33befb4d8a3c7ab", "cachedResultName": "Time Tracker"}, "propertiesUi": {"propertyValues": [{"key": "Start|date", "date": "={{ $now }}"}]}}, "credentials": {"notionApi": {"id": "03mmrqQX1rffebZp", "name": "<PERSON>ion <PERSON>"}}, "typeVersion": 2.2}, {"id": "490d1893-3828-4df6-8c0a-0a1476fc8727", "name": "Update page with end date", "type": "n8n-nodes-base.notion", "position": [1560, 780], "parameters": {"pageId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "options": {}, "resource": "databasePage", "operation": "update", "propertiesUi": {"propertyValues": [{"key": "End|date", "date": "={{ $now }}"}]}}, "credentials": {"notionApi": {"id": "03mmrqQX1rffebZp", "name": "<PERSON>ion <PERSON>"}}, "typeVersion": 2.2}, {"id": "daef6212-b852-45ba-8100-103e231837cb", "name": "If pause_in_minuten is empty", "type": "n8n-nodes-base.if", "position": [1540, 220], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "6ec8bb5f-d860-47a8-b631-c9535716ddc5", "operator": {"type": "number", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json.property_break }}", "rightValue": ""}]}}, "typeVersion": 2.1}, {"id": "ef724c03-885e-4066-b776-a84fe001a14a", "name": "If page responded", "type": "n8n-nodes-base.if", "position": [1260, 260], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "2130bdb4-54be-4d43-90bb-36f57826f2dc", "operator": {"type": "object", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json }}", "rightValue": ""}]}}, "typeVersion": 2.1}, {"id": "aa51a557-7eec-410a-9cdd-1dac3e2e104d", "name": "If page exist", "type": "n8n-nodes-base.if", "position": [980, -200], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "2130bdb4-54be-4d43-90bb-36f57826f2dc", "operator": {"type": "object", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json }}", "rightValue": ""}]}}, "typeVersion": 2.1}, {"id": "738aa465-bd04-4c4e-9846-02ae66139789", "name": "If page exist1", "type": "n8n-nodes-base.if", "position": [980, 840], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "2130bdb4-54be-4d43-90bb-36f57826f2dc", "operator": {"type": "object", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json }}", "rightValue": ""}]}}, "typeVersion": 2.1}, {"id": "4547c3fd-6373-401a-9d33-caa1e9f5545e", "name": "If", "type": "n8n-nodes-base.if", "position": [1260, 840], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "6ec8bb5f-d860-47a8-b631-c9535716ddc5", "operator": {"type": "object", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json.property_end }}", "rightValue": ""}]}}, "typeVersion": 2.1}, {"id": "70424282-dcce-4f1a-aba0-af0e7947a4fd", "name": "Set Break Duration", "type": "n8n-nodes-base.set", "position": [740, 260], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9261c98a-3099-4409-b697-8c28f6ec0c06", "name": "break_duration", "type": "number", "value": "={{ $json.body.duration }}"}]}}, "typeVersion": 3.4}, {"id": "ad1bd9ce-d7cc-4359-954b-8976f593c272", "name": "Update break duration for current day", "type": "n8n-nodes-base.notion", "position": [1820, 320], "parameters": {"pageId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "options": {}, "resource": "databasePage", "operation": "update", "propertiesUi": {"propertyValues": [{"key": "Break|number", "numberValue": "={{ $('Set Break Duration').item.json.break_duration }}"}]}}, "credentials": {"notionApi": {"id": "03mmrqQX1rffebZp", "name": "<PERSON>ion <PERSON>"}}, "typeVersion": 2.2}, {"id": "622eddf9-f719-412f-a07e-45e4d8390798", "name": "Set break duration for current day", "type": "n8n-nodes-base.notion", "position": [1820, 140], "parameters": {"pageId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "options": {}, "resource": "databasePage", "operation": "update", "propertiesUi": {"propertyValues": [{"key": "Break|number", "numberValue": "={{ $('Set Break Duration').item.json.break_duration }}"}]}}, "credentials": {"notionApi": {"id": "03mmrqQX1rffebZp", "name": "<PERSON>ion <PERSON>"}}, "typeVersion": 2.2}, {"id": "29a96903-43f4-439d-9d35-95d557f7c544", "name": "Get notion page by date", "type": "n8n-nodes-base.notion", "position": [980, 260], "parameters": {"limit": 1, "filters": {"conditions": [{"key": "Date|formula", "condition": "equals", "textValue": "={{ $now.format('dd.MM.yyyy') }}", "returnType": "text"}]}, "options": {}, "resource": "databasePage", "operation": "getAll", "databaseId": {"__rl": true, "mode": "list", "value": "1117f2f5-baf9-8054-b33b-efb4d8a3c7ab", "cachedResultUrl": "https://www.notion.so/1117f2f5baf98054b33befb4d8a3c7ab", "cachedResultName": "Time Tracker"}, "filterType": "manual"}, "credentials": {"notionApi": {"id": "03mmrqQX1rffebZp", "name": "<PERSON>ion <PERSON>"}}, "typeVersion": 2.2, "alwaysOutputData": true}, {"id": "bca3aff3-2d7d-4cc0-8842-5de6db67fccd", "name": "Set Message - End time already tracked", "type": "n8n-nodes-base.set", "position": [2080, 960], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "419d7570-d1ce-44b1-814c-7757da92a188", "name": "message", "type": "string", "value": "End time already tracked."}]}}, "typeVersion": 3.4}, {"id": "a774ac50-49f3-420b-96c5-e97f46857f02", "name": "Set Message - End time tracked", "type": "n8n-nodes-base.set", "position": [2080, 780], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "419d7570-d1ce-44b1-814c-7757da92a188", "name": "message", "type": "string", "value": "End Time Tracked!"}]}}, "typeVersion": 3.4}, {"id": "f50f5fc4-cf29-406b-940f-e8294c459b7f", "name": "Set Message - Start time not yet tracked", "type": "n8n-nodes-base.set", "position": [2080, 1140], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "419d7570-d1ce-44b1-814c-7757da92a188", "name": "message", "type": "string", "value": "Today's start time not yet tracked!"}]}}, "typeVersion": 3.4}, {"id": "0450fc1d-9c3b-4af6-b5a4-52dc971a1a2e", "name": "Set Message - Start not yet tracked", "type": "n8n-nodes-base.set", "position": [2080, 520], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "419d7570-d1ce-44b1-814c-7757da92a188", "name": "message", "type": "string", "value": "Today's start time not yet tracked!"}]}}, "typeVersion": 3.4}, {"id": "fa1855df-e3b5-4052-b26a-7be840bcaf0c", "name": "Set Message - Break time tracked", "type": "n8n-nodes-base.set", "position": [2080, 140], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "419d7570-d1ce-44b1-814c-7757da92a188", "name": "message", "type": "string", "value": "=Tracked {{ $('Set Break Duration').item.json.break_duration }} minutes as break time."}]}}, "typeVersion": 3.4}, {"id": "3ba94d34-18e8-49d8-8924-4837d815e183", "name": "Set Message - Break time updated", "type": "n8n-nodes-base.set", "position": [2080, 320], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "419d7570-d1ce-44b1-814c-7757da92a188", "name": "message", "type": "string", "value": "=Updated break time to {{ $('Set Break Duration').item.json.break_duration }} minutes."}]}}, "typeVersion": 3.4}, {"id": "42465160-7eec-43ed-93ad-1d73745911a0", "name": "Set Message - Start time already tracked", "type": "n8n-nodes-base.set", "position": [2080, -300], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "419d7570-d1ce-44b1-814c-7757da92a188", "name": "message", "type": "string", "value": "Start time already tracked."}]}}, "typeVersion": 3.4}, {"id": "8d35a443-f8ef-40fd-b7a0-8933a7a38b27", "name": "Set Message - Start time already tracked1", "type": "n8n-nodes-base.set", "position": [2080, -120], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "419d7570-d1ce-44b1-814c-7757da92a188", "name": "message", "type": "string", "value": "Start time tracked."}]}}, "typeVersion": 3.4}, {"id": "ee6e5e70-1d27-4edc-88d8-74b44611df39", "name": "Get notion page with todays date", "type": "n8n-nodes-base.notion", "position": [740, -200], "parameters": {"limit": 1, "filters": {"conditions": [{"key": "Date|formula", "condition": "equals", "textValue": "={{ $now.format('dd.MM.yyyy') }}", "returnType": "text"}]}, "options": {}, "resource": "databasePage", "operation": "getAll", "databaseId": {"__rl": true, "mode": "list", "value": "1117f2f5-baf9-8054-b33b-efb4d8a3c7ab", "cachedResultUrl": "https://www.notion.so/1117f2f5baf98054b33befb4d8a3c7ab", "cachedResultName": "Time Tracker"}, "filterType": "manual"}, "credentials": {"notionApi": {"id": "03mmrqQX1rffebZp", "name": "<PERSON>ion <PERSON>"}}, "typeVersion": 2.2, "alwaysOutputData": true}, {"id": "875f1969-4958-4450-87a5-aad1f65b3a9d", "name": "Switch", "type": "n8n-nodes-base.switch", "position": [260, 260], "parameters": {"rules": {"values": [{"outputKey": "Start", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.body.method }}", "rightValue": "start"}]}, "renameOutput": true}, {"outputKey": "Break", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "6ddd1f12-a0d8-42df-9776-dff0f44ba82c", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.body.method }}", "rightValue": "break"}]}, "renameOutput": true}, {"outputKey": "End", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "61550dac-65a2-4e4b-99a8-4df4a357cec0", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.body.method }}", "rightValue": "end"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.1}, {"id": "ab59018e-a41c-4c48-9aa7-33d123cb2215", "name": "Get notion page with todays date1", "type": "n8n-nodes-base.notion", "position": [740, 840], "parameters": {"limit": 1, "filters": {"conditions": [{"key": "Date|formula", "condition": "equals", "textValue": "={{ $now.format('dd.MM.yyyy') }}", "returnType": "text"}]}, "options": {}, "resource": "databasePage", "operation": "getAll", "databaseId": {"__rl": true, "mode": "list", "value": "1117f2f5-baf9-8054-b33b-efb4d8a3c7ab", "cachedResultUrl": "https://www.notion.so/1117f2f5baf98054b33befb4d8a3c7ab", "cachedResultName": "Time Tracker"}, "filterType": "manual"}, "credentials": {"notionApi": {"id": "03mmrqQX1rffebZp", "name": "<PERSON>ion <PERSON>"}}, "typeVersion": 2.2, "alwaysOutputData": true}, {"id": "8cbdbe7f-3c32-48bb-bce6-80b918a7c31e", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [480, -400], "parameters": {"width": 2127.3212174343475, "height": 469.85870733996774, "content": "## Track start time"}, "typeVersion": 1}, {"id": "e930da00-916b-4767-a34e-b985d97090ca", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [480, 108.01971369783018], "parameters": {"width": 2127.3212174343475, "height": 596.8497421429678, "content": "## Track break duration"}, "typeVersion": 1}, {"id": "6b77fcde-e3ed-4cfa-bd89-9d1cc6660591", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [480, 740], "parameters": {"width": 2127.3212174343475, "height": 627.9984865286092, "content": "## Track end time"}, "typeVersion": 1}, {"id": "85b40d37-3a55-4346-8c94-60eb2f8b6dba", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-104.58827842609037, -400], "parameters": {"color": 4, "width": 538.9177312302156, "height": 1760.750302860566, "content": "## API Endpoint Trigger"}, "typeVersion": 1}, {"id": "c09f93f1-a2c0-4146-8853-5283bd419a73", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-102.50502117110727, -680], "parameters": {"color": 6, "width": 534.5813587043364, "height": 247.95862766773985, "content": "## Setup instructions\nVisit our [Unitize Documents](https://docs.unitize.de) to create a copy of the Time Tracker database as well as download the iOS Shortcut."}, "typeVersion": 1}, {"id": "************************************", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [2780, 320], "parameters": {"options": {}, "respondWith": "text", "responseBody": "={{ $json.message }}"}, "typeVersion": 1.1}, {"id": "585efbae-ec10-4ef4-90a0-7bad55b4a150", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [2640, -400], "parameters": {"color": 4, "width": 415.1572200385813, "height": 1766.1026447605514, "content": "## Respond to iOS Shortcut"}, "typeVersion": 1}, {"id": "************************************", "name": "Webhook - Track Time", "type": "n8n-nodes-base.webhook", "position": [-20, 260], "webhookId": "752a7723-87b6-470f-a7d3-f627f6457e39", "parameters": {"path": "track-time", "options": {}, "httpMethod": "POST", "responseMode": "responseNode"}, "typeVersion": 2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "f0e95932-b61d-4fcf-a3a6-5cef415fd8fe", "connections": {"If": {"main": [[{"node": "Update page with end date", "type": "main", "index": 0}], [{"node": "Set Message - End time already tracked", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Get notion page with todays date", "type": "main", "index": 0}], [{"node": "Set Break Duration", "type": "main", "index": 0}], [{"node": "Get notion page with todays date1", "type": "main", "index": 0}]]}, "If page exist": {"main": [[{"node": "Set Message - Start time already tracked", "type": "main", "index": 0}], [{"node": "Create new page", "type": "main", "index": 0}]]}, "If page exist1": {"main": [[{"node": "If", "type": "main", "index": 0}], [{"node": "Set Message - Start time not yet tracked", "type": "main", "index": 0}]]}, "Create new page": {"main": [[{"node": "Set Message - Start time already tracked1", "type": "main", "index": 0}]]}, "If page responded": {"main": [[{"node": "If pause_in_minuten is empty", "type": "main", "index": 0}], [{"node": "Set Message - Start not yet tracked", "type": "main", "index": 0}]]}, "Set Break Duration": {"main": [[{"node": "Get notion page by date", "type": "main", "index": 0}]]}, "Webhook - Track Time": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Get notion page by date": {"main": [[{"node": "If page responded", "type": "main", "index": 0}]]}, "Update page with end date": {"main": [[{"node": "Set Message - End time tracked", "type": "main", "index": 0}]]}, "If pause_in_minuten is empty": {"main": [[{"node": "Set break duration for current day", "type": "main", "index": 0}], [{"node": "Update break duration for current day", "type": "main", "index": 0}]]}, "Set Message - End time tracked": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Get notion page with todays date": {"main": [[{"node": "If page exist", "type": "main", "index": 0}]]}, "Set Message - Break time tracked": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Set Message - Break time updated": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Get notion page with todays date1": {"main": [[{"node": "If page exist1", "type": "main", "index": 0}]]}, "Set break duration for current day": {"main": [[{"node": "Set Message - Break time tracked", "type": "main", "index": 0}]]}, "Set Message - Start not yet tracked": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Update break duration for current day": {"main": [[{"node": "Set Message - Break time updated", "type": "main", "index": 0}]]}, "Set Message - End time already tracked": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Set Message - Start time already tracked": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Set Message - Start time not yet tracked": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Set Message - Start time already tracked1": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}}}