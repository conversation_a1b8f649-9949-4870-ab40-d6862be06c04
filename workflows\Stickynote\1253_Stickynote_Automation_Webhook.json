{"meta": {"instanceId": "558d88703fb65b2d0e44613bc35916258b0f0bf983c5d4730c00c424b77ca36a", "templateId": "2931", "templateCredsSetupCompleted": true}, "nodes": [{"id": "100f23d3-cbe9-458a-9ef1-7cc5fcba8f3c", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [640, 540], "parameters": {"width": 300, "height": 205, "content": "### The conversation history(last 20 messages) is stored in a buffer memory"}, "typeVersion": 1}, {"id": "b48f989f-deb9-479c-b163-03f098d00c9c", "name": "On new manual Chat Message", "type": "@n8n/n8n-nodes-langchain.manualChatTrigger", "position": [380, 240], "parameters": {}, "typeVersion": 1}, {"id": "add8e8df-6b2a-4cbd-84e7-3b006733ef7d", "name": "Wikipedia", "type": "@n8n/n8n-nodes-langchain.toolWikipedia", "position": [1180, 640], "parameters": {}, "typeVersion": 1}, {"id": "a97454a8-001d-4986-9cb5-83176229ea70", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [980, 540], "parameters": {"width": 300, "height": 205, "content": "### Tools which agent can use to accomplish the task"}, "typeVersion": 1}, {"id": "52b57e72-8cc9-4865-9a00-d03b2e7f1b92", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [600, 160], "parameters": {"width": 422, "height": 211, "content": "### Conversational agent will utilise available tools to answer the prompt. "}, "typeVersion": 1}, {"id": "8f0653ab-376b-40b9-b876-e608defdeb89", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [740, 600], "parameters": {"contextWindowLength": 20}, "typeVersion": 1}, {"id": "13237945-e143-4f65-b034-785f5ebde5bb", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [680, 240], "parameters": {"text": "={{ $json.input }}", "options": {"systemMessage": "=You are a helpful assistant, with weather tool and wiki tool. find out the latitude and longitude information of a location then use the weather tool for current weather and weather forecast. For general info, use the wiki tool."}, "promptType": "define"}, "typeVersion": 1.6}, {"id": "ee06c0f4-b2de-4257-9735-3ec228f2b794", "name": "Weather HTTP Request", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1020, 620], "parameters": {"url": "https://api.open-meteo.com/v1/forecast", "sendQuery": true, "parametersQuery": {"values": [{"name": "latitude"}, {"name": "longitude"}, {"name": "forecast_days", "value": "1", "valueProvider": "fieldValue"}, {"name": "hourly", "value": "temperature_2m", "valueProvider": "fieldValue"}]}, "toolDescription": "Fetch current temperature for given coordinates."}, "notesInFlow": true, "typeVersion": 1.1}, {"id": "3e5608c8-281d-47e0-af9d-77707530fd6b", "name": "<PERSON><PERSON><PERSON> Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOllama", "position": [520, 620], "parameters": {"model": "llama3.2:latest", "options": {}}, "credentials": {"ollamaApi": {"id": "xHuYe0MDGOs9IpBW", "name": "Local Ollama service"}}, "typeVersion": 1}, {"id": "b3d794f4-37b5-46c8-9d7d-ad1087006ce5", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1040, 140], "parameters": {"color": 4, "height": 240, "content": "### In System Message, add the following.\n\n\"You are a helpful assistant, with weather tool and wiki tool. find out the latitude and longitude information of a location then use the weather tool for current weather and weather forecast. For general info, use the wiki tool.\""}, "typeVersion": 1}], "pinData": {}, "connections": {"Wikipedia": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Ollama Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Weather HTTP Request": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "On new manual Chat Message": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}}}