{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "Read Binary File", "type": "n8n-nodes-base.readBinaryFile", "position": [440, 300], "parameters": {"filePath": "/home/<USER>/filelist.txt"}, "typeVersion": 1}, {"name": "Move Binary Data", "type": "n8n-nodes-base.moveBinaryData", "position": [610, 300], "parameters": {"options": {}, "setAllData": false}, "typeVersion": 1}, {"name": "Function", "type": "n8n-nodes-base.function", "position": [810, 300], "parameters": {"functionCode": "items[0].json.arrData = items[0].json.data.split(\"\\n\");\nitems[0].json.data = {};\nitems[0].json.dataSize = items[0].json.arrData.length-2;\nreturn items;"}, "typeVersion": 1}, {"name": "Execute Command", "type": "n8n-nodes-base.executeCommand", "position": [1040, 300], "parameters": {"command": "=echo \"The file name is {{$node[\"Function\"].json[\"arrData\"][$runIndex]}}\" >> /home/<USER>/n8n-output.txt"}, "typeVersion": 1}, {"name": "IF", "type": "n8n-nodes-base.if", "position": [1250, 520], "parameters": {"conditions": {"number": [{"value1": "={{$node[\"Function\"].json[\"dataSize\"]}}", "value2": "={{$runIndex}}", "operation": "larger"}], "string": []}}, "typeVersion": 1}, {"name": "NoOp", "type": "n8n-nodes-base.noOp", "position": [1450, 540], "parameters": {}, "typeVersion": 1}], "connections": {"IF": {"main": [[{"node": "Execute Command", "type": "main", "index": 0}], [{"node": "NoOp", "type": "main", "index": 0}]]}, "Function": {"main": [[{"node": "Execute Command", "type": "main", "index": 0}]]}, "Execute Command": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}, "Move Binary Data": {"main": [[{"node": "Function", "type": "main", "index": 0}]]}, "Read Binary File": {"main": [[{"node": "Move Binary Data", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Read Binary File", "type": "main", "index": 0}]]}}}