{"id": "U9RofpXSIIUg12f9", "meta": {"instanceId": "a4bfc93e975ca233ac45ed7c9227d84cf5a2329310525917adaf3312e10d5462", "templateCredsSetupCompleted": true}, "name": "AI Social Media Publisher from WordPress", "tags": [{"id": "2VG6RbmUdJ2VZbrj", "name": "Google Drive", "createdAt": "2024-12-04T16:50:56.177Z", "updatedAt": "2024-12-04T16:50:56.177Z"}, {"id": "EtObDwELrdVvzOcI", "name": "OpenRouter", "createdAt": "2024-12-04T16:53:26.886Z", "updatedAt": "2024-12-04T16:53:26.886Z"}, {"id": "OVXRgoTzbRrrYmBB", "name": "X", "createdAt": "2025-02-24T15:47:38.855Z", "updatedAt": "2025-02-24T15:47:38.855Z"}, {"id": "PLbmcn8OyqnoHrYE", "name": "Instagram", "createdAt": "2025-02-24T15:47:48.325Z", "updatedAt": "2025-02-24T15:47:48.325Z"}, {"id": "QsjuqQbwRJaxuGB4", "name": "Facebook", "createdAt": "2025-02-24T15:47:42.574Z", "updatedAt": "2025-02-24T15:47:42.574Z"}, {"id": "oK8zaSe2Q5RG7qNe", "name": "Linkedin", "createdAt": "2025-02-24T15:47:45.129Z", "updatedAt": "2025-02-24T15:47:45.129Z"}, {"id": "paTcf5QZDJsC2vKY", "name": "OpenAI", "createdAt": "2024-12-04T16:52:10.768Z", "updatedAt": "2024-12-04T16:52:10.768Z"}], "nodes": [{"id": "f5bb7898-44eb-45bf-b199-2146c8e901f6", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-220, -80], "parameters": {}, "typeVersion": 1}, {"id": "b1977304-fbbf-4bb5-924a-e5db036bca91", "name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [0, -80], "parameters": {"options": {"returnFirstMatch": true}, "filtersUI": {"values": [{"lookupColumn": "TWITTER"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1suPQNdgoAzrklleN4ok2mZnsq0GK1dt59oIHv8JWX5U/edit#gid=0", "cachedResultName": "Foglio1"}, "documentId": {"__rl": true, "mode": "list", "value": "1suPQNdgoAzrklleN4ok2mZnsq0GK1dt59oIHv8JWX5U", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1suPQNdgoAzrklleN4ok2mZnsq0GK1dt59oIHv8JWX5U/edit?usp=drivesdk", "cachedResultName": "Social Media post"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "1451692d-8e8d-4da7-be3e-b4dd20319c7a", "name": "OpenRouter <PERSON>", "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "position": [400, 140], "parameters": {"model": "google/gemini-2.0-flash-exp:free", "options": {}}, "credentials": {"openRouterApi": {"id": "pb06rfB4xmxzVe3Q", "name": "OpenRouter"}}, "typeVersion": 1}, {"id": "6768f867-666d-4c41-b77a-1bfb389b0329", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [620, 140], "parameters": {"schemaType": "manual", "inputSchema": "{\n\t\"type\": \"object\",\n\t\"properties\": {\n\t\t\"twitter\": {\n\t\t\t\"type\": \"string\"\n\t\t},\n\t\t\"facebook\": {\n\t\t\t\"type\": \"string\"\n\t\t},\n        \"linkedin\": {\n\t\t\t\"type\": \"string\"\n\t\t},\n        \"instagram\": {\n\t\t\t\"type\": \"string\"\n\t\t}\n\t}\n}"}, "typeVersion": 1.2}, {"id": "05cf81ec-bb25-447a-9c54-183132b8804c", "name": "Image Instagram", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [940, 280], "parameters": {"prompt": "={{ $json.output.instagram }}", "options": {"size": "1024x1024", "returnImageUrls": true}, "resource": "image"}, "credentials": {"openAiApi": {"id": "CDX6QM4gLYanh0P4", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "2ae8be2c-28a3-4f55-aee3-2fff3517b149", "name": "Image Facebook e Linkedin", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [940, -140], "parameters": {"prompt": "={{ $json.output.facebook }}", "options": {"size": "1792x1024", "returnImageUrls": false}, "resource": "image"}, "credentials": {"openAiApi": {"id": "CDX6QM4gLYanh0P4", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "583a8459-3574-44e6-85bf-a33b9a4bf9f4", "name": "Social Media Manager", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [440, -80], "parameters": {"text": "=Generate social content from the following text with title \"{{ $json.title.rendered }}\" (in the same language):\n\n'''\n{{ $json.content.rendered }}\n'''", "messages": {"messageValues": [{"message": "=Your goal is to create engaging and informative content for LinkedIn, Instagram, Facebook, Twitter (X) while ensuring each post aligns with the platform’s audience, tone, and style. Content should reflect the company’s expertise, providing value-driven insights, tutorials, reviews, and discussions that resonate with tech professionals, enthusiasts, and businesses.\n\nEach post should be optimized for engagement, using platform-specific hashtags to improve reach and SEO. Maintain a professional yet approachable tone that promotes trust and authority in the tech space.\n\nContent Creation Best Practices:\nPost Optimization: Adapt content format, style, and hashtags based on each social media platform’s algorithm and audience engagement patterns.\nSEO & Hashtags: Use a balanced mix of general hashtags and platform-specific trending hashtags to maximize reach.\nEngagement-driven content: Focus on tech tutorials, IT industry updates, comparisons, reviews, and in-depth discussions that spark engagement.\nConsistency: Maintain a consistent tone and visual identity across all platforms, while tailoring content to each audience.\nData-driven trends: Regularly analyze post performance and adjust your hashtag strategy to reflect trending discussions within your topic. High-quality, relevant, and engaging copy that appeals to professionals, businesses, and enthusiasts of your topic must be provided.\n\n### Platform-specific requirements:\n1. **LinkedIn**:\n- Style: Professional and informative.\n- Tone: Focus on business results, employee gratitude, and community impact.\n- Content length: 3-4 sentences, detailed and insightful.\n- Hashtags: Create hashtags that are compatible with your topic.\n- Call to action: Engage businesses and professionals by encouraging comments or visits to <PERSON> Ansari's website.\n\n2. **Instagram**:\n- Style: Visual storytelling.\n- Tone: Inspiring and engaging.\n- Content length: 2-3 sentences, paired with creative captions and emojis.\n- Images: Highlight events, giveaways, and milestones (e.g., 50th anniversary keychains, eco-friendly tote bags).\n- Call to action: Use phrases like “Swipe to see more,” “Tag your coworkers,” or “Celebrate with us!”\n- Hashtags: Create hashtags that resonate with your topic\n\n3. **Facebook**:\n- Style: Friendly and relevant.\n- Tone: Community-focused, sharing employee stories, accomplishments, or event updates.\n- Content length: 2-3 sentences, slightly informal but still reflective of your company’s values.\n- Call to Action: Encourage likes, shares, and comments from connected families and communities\n\n4. **Twitter (X)**:\n- Style: Concise and impactful.\n- Tone: Crisp, engaging, and catchy.\n- Content Length: 150 characters or less, including hashtags.\n- Create hashtags that are compatible with the topic being discussed\n- Call to Action: Focus on rapid engagement through retweets, likes, and replies.\n\nWith this guide, generate posts for all platforms for the inputs below by inserting them into the specified json structure;"}]}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.5}, {"id": "634af127-cd08-4beb-99a8-daa626793736", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-60, -160], "parameters": {"width": 420, "height": 260, "content": "Get the Post ID of the Wordpress article on which you want to generate the caption for social media"}, "typeVersion": 1}, {"id": "dbdb39dc-02b2-4fe6-8b70-f38b86c9d9e3", "name": "Linkedin OK", "type": "n8n-nodes-base.googleSheets", "position": [1480, -260], "parameters": {"columns": {"value": {"LINKEDIN": "x", "row_number": "={{ $('Google Sheets').item.json.row_number }}"}, "schema": [{"id": "POST ID", "type": "string", "display": true, "required": false, "displayName": "POST ID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TESTO", "type": "string", "display": true, "required": false, "displayName": "TESTO", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TWITTER", "type": "string", "display": true, "required": false, "displayName": "TWITTER", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "FACEBOOK", "type": "string", "display": true, "removed": false, "required": false, "displayName": "FACEBOOK", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "INSTAGRAM", "type": "string", "display": true, "removed": false, "required": false, "displayName": "INSTAGRAM", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "LINKEDIN", "type": "string", "display": true, "removed": false, "required": false, "displayName": "LINKEDIN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["row_number"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1suPQNdgoAzrklleN4ok2mZnsq0GK1dt59oIHv8JWX5U/edit#gid=0", "cachedResultName": "Foglio1"}, "documentId": {"__rl": true, "mode": "list", "value": "1suPQNdgoAzrklleN4ok2mZnsq0GK1dt59oIHv8JWX5U", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1suPQNdgoAzrklleN4ok2mZnsq0GK1dt59oIHv8JWX5U/edit?usp=drivesdk", "cachedResultName": "Social Media post"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "07aba718-fb10-48d4-bedb-0c19315acfe1", "name": "Facebook Ok", "type": "n8n-nodes-base.googleSheets", "position": [1480, 0], "parameters": {"columns": {"value": {"FACEBOOK": "x", "row_number": "={{ $('Google Sheets').item.json.row_number }}"}, "schema": [{"id": "POST ID", "type": "string", "display": true, "required": false, "displayName": "POST ID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TESTO", "type": "string", "display": true, "required": false, "displayName": "TESTO", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TWITTER", "type": "string", "display": true, "required": false, "displayName": "TWITTER", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "FACEBOOK", "type": "string", "display": true, "removed": false, "required": false, "displayName": "FACEBOOK", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "INSTAGRAM", "type": "string", "display": true, "removed": false, "required": false, "displayName": "INSTAGRAM", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "LINKEDIN", "type": "string", "display": true, "removed": false, "required": false, "displayName": "LINKEDIN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["row_number"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1suPQNdgoAzrklleN4ok2mZnsq0GK1dt59oIHv8JWX5U/edit#gid=0", "cachedResultName": "Foglio1"}, "documentId": {"__rl": true, "mode": "list", "value": "1suPQNdgoAzrklleN4ok2mZnsq0GK1dt59oIHv8JWX5U", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1suPQNdgoAzrklleN4ok2mZnsq0GK1dt59oIHv8JWX5U/edit?usp=drivesdk", "cachedResultName": "Social Media post"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "7fda8841-4b46-4f3b-9ba0-aa728ba7943f", "name": "Instagram OK", "type": "n8n-nodes-base.googleSheets", "position": [1480, 280], "parameters": {"columns": {"value": {"INSTAGRAM": "x", "row_number": "={{ $('Google Sheets').item.json.row_number }}"}, "schema": [{"id": "POST ID", "type": "string", "display": true, "required": false, "displayName": "POST ID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TESTO", "type": "string", "display": true, "required": false, "displayName": "TESTO", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TWITTER", "type": "string", "display": true, "required": false, "displayName": "TWITTER", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "FACEBOOK", "type": "string", "display": true, "removed": false, "required": false, "displayName": "FACEBOOK", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "INSTAGRAM", "type": "string", "display": true, "removed": false, "required": false, "displayName": "INSTAGRAM", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "LINKEDIN", "type": "string", "display": true, "removed": false, "required": false, "displayName": "LINKEDIN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["row_number"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1suPQNdgoAzrklleN4ok2mZnsq0GK1dt59oIHv8JWX5U/edit#gid=0", "cachedResultName": "Foglio1"}, "documentId": {"__rl": true, "mode": "list", "value": "1suPQNdgoAzrklleN4ok2mZnsq0GK1dt59oIHv8JWX5U", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1suPQNdgoAzrklleN4ok2mZnsq0GK1dt59oIHv8JWX5U/edit?usp=drivesdk", "cachedResultName": "Social Media post"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "fe599e8f-ff45-4aec-b70e-f0dc75b929d5", "name": "Get Post", "type": "n8n-nodes-base.wordpress", "position": [220, -80], "parameters": {"postId": "={{ $json['POST ID'] }}", "options": {}, "operation": "get"}, "credentials": {"wordpressApi": {"id": "OE4AgquSkMWydRqn", "name": "Wordpress (wp.test.7hype.com)"}}, "typeVersion": 1}, {"id": "********-47dd-44ff-bcbd-f83d3bb3941d", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [380, -160], "parameters": {"width": 360, "height": 260, "content": "The SMM Chain analyses the content of the post and creates the most suitable caption based on the destination social network.\n"}, "typeVersion": 1}, {"id": "89a0deab-b55a-4eb6-8251-538f5f216d56", "name": "Publish on X", "type": "n8n-nodes-base.twitter", "position": [1220, -500], "parameters": {"text": "={{ $json.output.twitter }}", "additionalFields": {}}, "credentials": {"twitterOAuth2Api": {"id": "qex5DVfLnCShvOC4", "name": "X account"}}, "typeVersion": 2}, {"id": "d0a618f3-48e0-4ba1-9525-e15663b38d9f", "name": "X OK", "type": "n8n-nodes-base.googleSheets", "position": [1480, -500], "parameters": {"columns": {"value": {"TWITTER": "x", "row_number": "={{ $('Google Sheets').item.json.row_number }}"}, "schema": [{"id": "POST ID", "type": "string", "display": true, "required": false, "displayName": "POST ID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TESTO", "type": "string", "display": true, "required": false, "displayName": "TESTO", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TWITTER", "type": "string", "display": true, "required": false, "displayName": "TWITTER", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "FACEBOOK", "type": "string", "display": true, "removed": false, "required": false, "displayName": "FACEBOOK", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "INSTAGRAM", "type": "string", "display": true, "removed": false, "required": false, "displayName": "INSTAGRAM", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "LINKEDIN", "type": "string", "display": true, "removed": false, "required": false, "displayName": "LINKEDIN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["row_number"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1suPQNdgoAzrklleN4ok2mZnsq0GK1dt59oIHv8JWX5U/edit#gid=0", "cachedResultName": "Foglio1"}, "documentId": {"__rl": true, "mode": "list", "value": "1suPQNdgoAzrklleN4ok2mZnsq0GK1dt59oIHv8JWX5U", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1suPQNdgoAzrklleN4ok2mZnsq0GK1dt59oIHv8JWX5U/edit?usp=drivesdk", "cachedResultName": "Social Media post"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "8f19c32b-ab02-4098-a165-a3c9c6ff05c4", "name": "Publish on LinkedIn", "type": "n8n-nodes-base.linkedIn", "position": [1220, -260], "parameters": {"text": "={{ $('Social Media Manager').item.json.output.linkedin }}", "postAs": "organization", "organization": "xxxxxxx", "additionalFields": {}, "shareMediaCategory": "IMAGE"}, "credentials": {"linkedInOAuth2Api": {"id": "K5XMG4dK4jY43PJ3", "name": "LinkedIn account"}}, "typeVersion": 1}, {"id": "69324bfa-ce6e-42e7-9598-13d8dbd13ae7", "name": "Publish on Facebook", "type": "n8n-nodes-base.facebookGraphApi", "position": [1220, 0], "parameters": {"edge": "photos", "node": "***************", "options": {"queryParameters": {"parameter": [{"name": "message", "value": "={{ $('Social Media Manager').item.json.output.facebook }}"}, {"name": "link", "value": "={{ $('Get Post').item.json.link }}"}]}}, "sendBinaryData": true, "graphApiVersion": "v21.0", "httpRequestMethod": "POST", "binaryPropertyName": "data"}, "credentials": {"facebookGraphApi": {"id": "q5gZW6hdEvqTFPup", "name": "Facebook Graph account (Fantaera)"}}, "typeVersion": 1}, {"id": "ccc7bb23-2a26-42c6-9ac4-16a2cce77f66", "name": "Publish on Instagram", "type": "n8n-nodes-base.httpRequest", "position": [1220, 280], "parameters": {"url": "https://graph.facebook.com/v20.0/***************/media", "method": "POST", "options": {}, "sendQuery": true, "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "image_url", "value": "={{ $json.url }}"}, {"name": "caption", "value": "={{ $('Social Media Manager').item.json.output.instagram }}"}]}, "nodeCredentialType": "facebookGraphApi"}, "credentials": {"facebookGraphApi": {"id": "q5gZW6hdEvqTFPup", "name": "Facebook Graph account (Fantaera)"}}, "typeVersion": 4.2}, {"id": "687cf808-1355-4bec-aad3-2395efd5a41c", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-220, -680], "parameters": {"color": 3, "width": 960, "height": 320, "content": "## STEP 1\n\nThis workflow automates the process of creating and publishing social media posts across multiple platforms (Twitter/X, Facebook, LinkedIn, and Instagram) based on content from a WordPress post. It uses AI models to generate platform-specific captions and images, and integrates with Google Sheets, WordPress, and various social media APIs.\n\nGet the API Keys of the social networks you want to publish on\n- [X (Twitter)](https://docs.x.com/x-api/getting-started/getting-access)\n- [Linkedin](https://learn.microsoft.com/en-us/linkedin/marketing/community-management/shares/posts-api?view=li-lms-2025-02&tabs=http)\n- [Facebook](https://developers.facebook.com/docs/facebook-login/guides/access-tokens#portabletokens)\n- [Instagram](https://developers.facebook.com/docs/instagram-platform/instagram-api-with-facebook-login/content-publishing/)\n\nAuthenticate on social media nodes with the access tokens obtained"}, "typeVersion": 1}, {"id": "f74d071d-cb55-48f0-8cea-1cc24fa34061", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-220, -320], "parameters": {"color": 3, "width": 960, "height": 100, "content": "## STEP 2\nClone [this Sheet](https://docs.google.com/spreadsheets/d/1suPQNdgoAzrklleN4ok2mZnsq0GK1dt59oIHv8JWX5U/edit?usp=sharing) and set ONLY the WordPress Post ID id in the right column\n"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "31616cbf-fe93-445c-8224-ea0b3afc4696", "connections": {"Get Post": {"main": [[{"node": "Social Media Manager", "type": "main", "index": 0}]]}, "Publish on X": {"main": [[{"node": "X OK", "type": "main", "index": 0}]]}, "Google Sheets": {"main": [[{"node": "Get Post", "type": "main", "index": 0}]]}, "Image Instagram": {"main": [[{"node": "Publish on Instagram", "type": "main", "index": 0}]]}, "Publish on Facebook": {"main": [[{"node": "Facebook Ok", "type": "main", "index": 0}]]}, "Publish on LinkedIn": {"main": [[{"node": "Linkedin OK", "type": "main", "index": 0}]]}, "Publish on Instagram": {"main": [[{"node": "Instagram OK", "type": "main", "index": 0}]]}, "Social Media Manager": {"main": [[{"node": "Image Instagram", "type": "main", "index": 0}, {"node": "Image Facebook e Linkedin", "type": "main", "index": 0}, {"node": "Publish on X", "type": "main", "index": 0}]]}, "OpenRouter Chat Model": {"ai_languageModel": [[{"node": "Social Media Manager", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Social Media Manager", "type": "ai_outputParser", "index": 0}]]}, "Image Facebook e Linkedin": {"main": [[{"node": "Publish on LinkedIn", "type": "main", "index": 0}, {"node": "Publish on Facebook", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}}}