{"meta": {"instanceId": "568298fde06d3db80a2eea77fe5bf45f0c7bb898dea20b769944e9ac7c6c5a80", "templateCredsSetupCompleted": true}, "nodes": [{"id": "eb8bbb43-d6ca-48f9-9522-12ac7100961d", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-1360, 380], "parameters": {}, "typeVersion": 1}, {"id": "77bf0c40-b045-40f9-9401-d1b206938180", "name": "Convert to File", "type": "n8n-nodes-base.convertToFile", "position": [-360, -420], "parameters": {"options": {}, "operation": "to<PERSON><PERSON>", "binaryPropertyName": "=data"}, "typeVersion": 1.1}, {"id": "c2e870c2-52e8-4808-9091-e3dcf286eaa5", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [-660, 20], "parameters": {"options": {}, "fieldToSplitOut": "data"}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "2bcafdc1-94e0-4a3d-9ad5-a189973be980", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-1500, -340], "parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 15}]}}, "typeVersion": 1.2}, {"id": "a602fdf3-82d8-4bc1-806b-576b6fc904b7", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1520, -520], "parameters": {"width": 760, "content": "### Receive an alert when new breaches are added to haveibeenpwned.com\nThis workflow demonstrates how we can receive alerts when new breaches are added to haveibeenpwned.com.\nIt also demonstrates a simple method for caching data between executions."}, "typeVersion": 1}, {"id": "a53e7c76-0823-415f-91fd-920b354568d3", "name": "Request breaches", "type": "n8n-nodes-base.httpRequest", "position": [-1240, -340], "parameters": {"url": "https://haveibeenpwned.com/api/v3/latestbreach", "options": {}}, "typeVersion": 4.2}, {"id": "777c65aa-1bce-40eb-9de1-dd8fef4afd05", "name": "Read last breach", "type": "n8n-nodes-base.readWriteFile", "notes": "we alerted about.", "position": [-1020, -160], "parameters": {"options": {}, "fileSelector": "./cache.json"}, "notesInFlow": true, "typeVersion": 1, "alwaysOutputData": true}, {"id": "d6638b7b-6209-497a-a176-91751a10bab1", "name": "Get JSON from file", "type": "n8n-nodes-base.extractFromFile", "position": [-840, -80], "parameters": {"options": {}, "operation": "fromJson"}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "42103453-54db-4d18-8d2b-9b56f5d3a3dd", "name": "Check for content", "type": "n8n-nodes-base.if", "position": [-480, 20], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "6bf6a0bd-e9b3-4fde-a9cc-08f4d0e94fd6", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.lastItem }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "2dfa332e-9892-4385-a693-2ff2fc51f067", "name": "Set to none", "type": "n8n-nodes-base.set", "notes": "File was empty.", "position": [-300, 80], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "47736e3f-0961-4b73-b4d5-207792640e87", "name": "lastItem", "type": "string", "value": "none"}]}}, "notesInFlow": true, "typeVersion": 3.4}, {"id": "6653599a-db6a-4a01-af5c-d79a2d58202f", "name": "If - check for new", "type": "n8n-nodes-base.if", "position": [-840, -340], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "badd0a56-081f-49e2-92f4-7711f1cd9289", "operator": {"type": "string", "operation": "notEquals"}, "leftValue": "={{ $json.lastItem }}", "rightValue": "={{ $json.Name }}"}]}}, "typeVersion": 2.2}, {"id": "1f2cbeda-0c84-4b54-84ec-03a7b22f4471", "name": "Set breach name", "type": "n8n-nodes-base.set", "position": [-560, -420], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d0714936-9956-4af8-93f9-3c44ef7beb09", "name": "lastItem", "type": "string", "value": "={{ $json.Name }}"}]}}, "typeVersion": 3.4}, {"id": "85314f2d-98d7-461a-a565-5202006ddd39", "name": "Write breach name to file", "type": "n8n-nodes-base.readWriteFile", "position": [-180, -420], "parameters": {"options": {}, "fileName": "./cache.json", "operation": "write"}, "typeVersion": 1}, {"id": "e4cc122c-172f-4154-b534-c2c9268cf10d", "name": "New breach", "type": "n8n-nodes-base.noOp", "notes": "Send alert", "position": [-560, -680], "parameters": {}, "notesInFlow": true, "typeVersion": 1}, {"id": "80b7507f-d5f2-4a3d-9090-784f80770478", "name": "Old breach", "type": "n8n-nodes-base.noOp", "notes": "already alerted.", "position": [-560, -160], "parameters": {}, "notesInFlow": true, "typeVersion": 1}, {"id": "63f65fa4-fba1-4ab4-93ff-cd4df9068b19", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-600, -500], "parameters": {"color": 7, "width": 640, "height": 240, "content": "### Save the name of the breach\nWe will check it the next time the workflow runs to see if we have a new breach."}, "typeVersion": 1}, {"id": "5a8dd017-e3e4-445e-be0f-24a8033d7dac", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-600, -240], "parameters": {"color": 7, "width": 640, "height": 240, "content": "### This breach has been seen before\nIf we end up here it means that the latest breach has been seen before."}, "typeVersion": 1}, {"id": "eb563c4a-54f5-4583-8fb1-e5ee5a14ca43", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-600, -760], "parameters": {"color": 3, "width": 640, "height": 240, "content": "### This is a new breach - send alert\nIf we end up here it means that the latest breach is new. Time to send some alerts to <PERSON>lack, or Discord or something."}, "typeVersion": 1}, {"id": "45b58d9b-7172-447d-91ab-e91e3516c8d9", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-1220, 300], "parameters": {"color": 7, "width": 600, "height": 260, "content": "### Clean up the cache\nDelete the `./cache.json` file. This will make sure the alert is triggered on the next run."}, "typeVersion": 1}, {"id": "bb2401d2-716c-47eb-9797-5b69583058ee", "name": "Set empty json", "type": "n8n-nodes-base.set", "position": [-1180, 380], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "69f35659-fd32-4fa7-969e-6cf266519f5b", "name": "data", "type": "string", "value": "{}"}]}}, "typeVersion": 3.4}, {"id": "c3abbf86-50f2-4772-bc7c-9a57ac39d4a3", "name": "Write cache.json", "type": "n8n-nodes-base.readWriteFile", "position": [-840, 380], "parameters": {"options": {}, "fileName": "./cache.json", "operation": "write"}, "typeVersion": 1}, {"id": "69f03dd6-11f0-41e6-8871-9b17c44ef2fe", "name": "Convert json to file", "type": "n8n-nodes-base.convertToFile", "position": [-1000, 380], "parameters": {"options": {}, "operation": "to<PERSON><PERSON>"}, "typeVersion": 1.1}, {"id": "2cf6adf9-59e4-4450-b7f8-96907155da84", "name": "Add information about the last breach we alerted", "type": "n8n-nodes-base.merge", "position": [-1020, -340], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineAll"}, "typeVersion": 3}, {"id": "80e38061-140a-4c78-b49c-dcf796da1427", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-1480, -100], "parameters": {"color": 6, "width": 380, "height": 240, "content": "## Support My Work! ❤️\n\n**👋 Hello! I'm Audun / xqus** \n🔗 My work: [xqus.com](https://xqus.com)\n💸 n8n shop: [xqus.gumroad.com](https://xqus.gumroad.com)\n\n**If you find this workflow helpful**, consider downloading or purchasing it on [Gumroad](https://xqus.gumroad.com/l/hasgi).\n\nYour support helps me create more useful n8n workflows and resources for the community. \n-Thanks a lot! 🙌"}, "typeVersion": 1}], "pinData": {}, "connections": {"Split Out": {"main": [[{"node": "Check for content", "type": "main", "index": 0}]]}, "Set to none": {"main": [[{"node": "Add information about the last breach we alerted", "type": "main", "index": 1}]]}, "Set empty json": {"main": [[{"node": "Convert json to file", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[{"node": "Write breach name to file", "type": "main", "index": 0}]]}, "Set breach name": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "Read last breach": {"main": [[{"node": "Get JSON from file", "type": "main", "index": 0}]]}, "Request breaches": {"main": [[{"node": "Read last breach", "type": "main", "index": 0}, {"node": "Add information about the last breach we alerted", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Request breaches", "type": "main", "index": 0}]]}, "Check for content": {"main": [[{"node": "Add information about the last breach we alerted", "type": "main", "index": 1}], [{"node": "Set to none", "type": "main", "index": 0}]]}, "Get JSON from file": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "If - check for new": {"main": [[{"node": "Set breach name", "type": "main", "index": 0}, {"node": "New breach", "type": "main", "index": 0}], [{"node": "Old breach", "type": "main", "index": 0}]]}, "Convert json to file": {"main": [[{"node": "Write cache.json", "type": "main", "index": 0}]]}, "Write breach name to file": {"main": [[]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set empty json", "type": "main", "index": 0}]]}, "Add information about the last breach we alerted": {"main": [[{"node": "If - check for new", "type": "main", "index": 0}]]}}}