{"meta": {"instanceId": "4e19bc4d542ebb7cc77dcbf34c0c6dca5062ae1e34fd327b055beb054230d539"}, "nodes": [{"id": "beebd9ac-4021-4e45-9971-4205c37e3742", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-500, 40], "parameters": {}, "typeVersion": 1}, {"id": "61e48c99-fdd3-4db7-8e2e-67cb2c2dfd97", "name": "SIGNL4", "type": "n8n-nodes-base.signl4", "position": [60, -140], "parameters": {"message": "=Weather alert ❄️ Temperature: {{ $json.main.temp }} °C", "additionalFields": {"title": "<PERSON> Alert from n8n", "externalId": "weather-alert", "locationFieldsUi": {"locationFieldsValues": {"latitude": "={{ $json.coord.lat }}", "longitude": "={{ $json.coord.lon }}"}}}}, "credentials": {"signl4Api": {"id": "EAiJUjPUA6kiAnG9", "name": "SIGNL4 Webhook account"}}, "typeVersion": 1}, {"id": "739a31e4-d353-4c95-bb84-b36f6a5560cf", "name": "OpenWeatherMap", "type": "n8n-nodes-base.openWeatherMap", "position": [-320, -140], "parameters": {"cityName": "Berlin"}, "credentials": {"openWeatherMapApi": {"id": "oH1seTNeKu1d87wm", "name": "OpenWeatherMap account"}}, "typeVersion": 1}, {"id": "95aab17d-c0eb-439c-81ff-7452794a514a", "name": "If", "type": "n8n-nodes-base.if", "position": [-160, -140], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "b95ba4e6-0749-4dd0-9830-14a6b0b5edcf", "operator": {"type": "number", "operation": "lt"}, "leftValue": "={{ $json.main.temp }}", "rightValue": 25}]}}, "typeVersion": 2}, {"id": "ef813bf0-cd46-474e-9208-2efbd402782f", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-500, -140], "parameters": {"rule": {"interval": [{"triggerAtHour": 6, "triggerAtMinute": 15}]}}, "typeVersion": 1.2}], "pinData": {}, "connections": {"If": {"main": [[{"node": "SIGNL4", "type": "main", "index": 0}]]}, "OpenWeatherMap": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "OpenWeatherMap", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "OpenWeatherMap", "type": "main", "index": 0}]]}}}