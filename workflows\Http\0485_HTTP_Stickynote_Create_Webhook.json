{"meta": {"instanceId": "041bccf206a3546a759ec4c0a3bf1256e62051945bb270c48f91f3acb13dc080"}, "nodes": [{"id": "8a22d40f-5b09-485a-8d58-70f36821ee9f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [340, -260], "parameters": {"width": 747, "height": 428, "content": "## Purpose \nTo verify the mailing address for new contacts in Groundhogg CRM. \n\nWhenever I add a new contact to Groundhogg CRM, I run this automation to ensure I have a valid mailing address. It also helps me check for misspellings if the contact address was manually entered.\n\nQuick Video Overview:\n\nhttps://www.youtube.com/watch?v=nrV0P0Yz8FI"}, "typeVersion": 1}, {"id": "d59b4ac3-5b41-4913-8b41-401a4eac8cc0", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1480, -180], "parameters": {"color": 5, "width": 561.9462602626872, "height": 763, "content": "Update Groundhogg CRM to indicate if the address is deliverable.\n\nPossible Options: \n- Add Tag\n- Add Note\n- Start Automation\n- Update a Field\n\nFor Deliverable Addresses - I apply a tag that the address was verified.\n\nFor Non Deliverable Addresses - I apply a tag, which triggers an automation for my team to manually verify the address. You could also trigger an automation to reach out to the contact to verify their address.\n\n"}, "typeVersion": 1}, {"id": "6a5d3833-a256-4b57-96dc-6a08886b65ee", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [520, 200], "parameters": {"color": 4, "height": 339, "content": "Receive a webhook from your CRM with the contact address fields"}, "typeVersion": 1}, {"id": "4fe1597b-ea03-439a-898b-a968cbd84511", "name": "Set Address Fields", "type": "n8n-nodes-base.set", "position": [840, 280], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "8216105e-23ad-4c5c-8f4a-4f97658e0947", "name": "address", "type": "string", "value": "={{ $json.address }}"}, {"id": "111da971-2473-4c5e-a106-22589cf47daf", "name": "address2", "type": "string", "value": ""}, {"id": "ed62cf39-10f1-42f6-b18f-bfa58b4fe646", "name": "city", "type": "string", "value": "={{ $json.city }}"}, {"id": "d9550200-04ac-4cf4-b7e6-cd40b793ce97", "name": "state", "type": "string", "value": "={{ $json.state }}"}, {"id": "62269d11-c98c-4016-83ef-291176f2fc12", "name": "zip", "type": "string", "value": "={{ $json.zip_code }}"}]}, "includeOtherFields": true}, "typeVersion": 3.3}, {"id": "ac13a2be-07fa-4861-99ab-9af8fe797db3", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1000, 480], "parameters": {"color": 3, "width": 430, "height": 216, "content": "1. Create an Account a LOB.com\n2. Create API Key (https://help.lob.com/account-management/api-keys)\n3. Update Node with your Credentials (Basic Auth)"}, "typeVersion": 1}, {"id": "04c286c0-8862-40f9-960d-902b3c89a6ee", "name": "CRM Webhook Trigger", "type": "n8n-nodes-base.webhook", "position": [600, 280], "webhookId": "a2df5279-0c49-49c1-83a3-cb1179930e91", "parameters": {"path": "727deb6f-9d10-4492-92e6-38f3292510b0", "options": {}, "httpMethod": "POST"}, "typeVersion": 1.1}, {"id": "c091dc5f-5527-43ca-b257-c977d654c13b", "name": "Update Groundhogg - Deliverable", "type": "n8n-nodes-base.httpRequest", "position": [1800, 140], "parameters": {"url": "=webhook listener from Groundhogg funnel", "method": "POST", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "tag", "value": "Mailing Address Deliverable"}, {"name": "id", "value": "={{ $('CRM Webhook Trigger').item.json.id }}"}]}}, "typeVersion": 4.1}, {"id": "1449b6e2-1c4f-4f75-8280-aab3c993f0ac", "name": "Update Groundhogg - NOT Deliverable", "type": "n8n-nodes-base.httpRequest", "position": [1800, 360], "parameters": {"url": "=webhook listener from Groundhogg funnel", "method": "POST", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "tag", "value": "Mailing Address NOT Deliverable"}, {"name": "id", "value": "={{ $('CRM Webhook Trigger').item.json.id }}"}]}}, "typeVersion": 4.1}, {"id": "a79eb361-7dc8-4838-bb40-b34bf35c3102", "name": "Address Verification", "type": "n8n-nodes-base.httpRequest", "position": [1140, 280], "parameters": {"url": "https://api.lob.com/v1/us_verifications", "method": "POST", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "primary_line", "value": "={{ $json.address }}"}, {"name": "secondary_line", "value": "={{ $json.address2 }}"}, {"name": "city", "value": "={{ $json.city }}"}, {"name": "state", "value": "={{ $json.state }}"}, {"name": "zip_code", "value": "={{ $json.zip_code }}"}]}}, "typeVersion": 4.1}, {"id": "71c9199f-823c-451b-baf5-a2b5de1697c1", "name": "Switch", "type": "n8n-nodes-base.switch", "position": [1500, 280], "parameters": {"rules": {"rules": [{"value2": "=deliverable", "outputKey": "deliverable"}, {"value2": "deliverable", "operation": "notEqual", "outputKey": "NOT deliverable"}]}, "value1": "={{ $json.deliverability }}", "dataType": "string"}, "typeVersion": 2}], "pinData": {"CRM Webhook Trigger": [{"id": "5551212", "city": "Washington", "email": "<EMAIL>", "phone": "************", "state": "DC", "address": "1600 Pennsylvania Avenue NW", "zip_code": "20500"}]}, "connections": {"Switch": {"main": [[{"node": "Update Groundhogg - Deliverable", "type": "main", "index": 0}], [{"node": "Update Groundhogg - NOT Deliverable", "type": "main", "index": 0}]]}, "Set Address Fields": {"main": [[{"node": "Address Verification", "type": "main", "index": 0}]]}, "CRM Webhook Trigger": {"main": [[{"node": "Set Address Fields", "type": "main", "index": 0}]]}, "Address Verification": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}}}