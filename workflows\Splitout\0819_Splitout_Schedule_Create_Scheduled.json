{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "cce90ce3-5661-4c8b-9752-71bc0e643f01", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-1880, -180], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.2}, {"id": "9f39b744-e3d5-4cb8-9631-d41ccb311e57", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [-840, -260], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "fd6f3243-3c94-4208-b200-511eef53f2f7", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [-700, -260], "parameters": {"options": {"metadata": {"metadataValues": [{"name": "project_key", "value": "={{ $json.project_key }}"}, {"name": "issue_key", "value": "={{ $json.issue_key }}"}, {"name": "issue_type", "value": "={{ $json.issue_type }}"}, {"name": "created_at", "value": "={{ $json.created_date }}"}, {"name": "resolved_at", "value": "={{ $json.resolution_date }}"}, {"name": "assignee_id", "value": "={{ $json.assignee_id }}"}, {"name": "assignee_name", "value": "={{ $json.assignee_name }}"}, {"name": "issue_title", "value": "={{ $json.title }}"}]}}, "jsonData": "=# {{ $json.title }}\n- created {{ $json.created_date }}\n- resolved {{ $json.resolution_date }}\n\n## description\n{{ $json.description }}", "jsonMode": "expressionData"}, "typeVersion": 1}, {"id": "d577536e-bee5-45ea-929e-951f4a255462", "name": "Recursive Character Text Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "position": [-600, -140], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "5fb95703-27aa-4ae3-b220-b2cca3596e0d", "name": "Issues Similarity Database", "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "position": [-840, -440], "parameters": {"mode": "insert", "options": {}, "tableName": {"__rl": true, "mode": "list", "value": "documents", "cachedResultName": "documents"}}, "credentials": {"supabaseApi": {"id": "AkI6FZYwHrf8n5Zw", "name": "Supabase(jira-issues-similarity-database)"}}, "typeVersion": 1}, {"id": "94d53f32-7f01-487f-b1a3-0dc15f8dc673", "name": "Supabase Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "position": [-540, 500], "parameters": {"mode": "retrieve-as-tool", "topK": 20, "options": {}, "toolName": "get_similar_issues", "tableName": {"__rl": true, "mode": "list", "value": "documents", "cachedResultName": "documents"}, "toolDescription": "Call this tool to find similar issues but which are resolved and by whom."}, "credentials": {"supabaseApi": {"id": "AkI6FZYwHrf8n5Zw", "name": "Supabase(jira-issues-similarity-database)"}}, "typeVersion": 1}, {"id": "d1c88dcd-62ef-4bb8-86d4-1ef294bb063d", "name": "Embeddings OpenAI1", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [-460, 620], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "a23442f6-c701-4b47-aea4-8764adab3d8d", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-680, 500], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "d07139dd-9df4-4e95-a0b0-4e28054b62c9", "name": "Find Similar Issues + Assignees", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-660, 300], "parameters": {"text": "=# {{ $json.fields.summary }}\n\n## description\n{{ $json.fields.description }}", "options": {"systemMessage": "You are a project management assistant helping to assign stale JIRA issues to team members. To find out who best to assign the issue to, you must first find similar JIRA issues in terms of problem and context and attain the team members who resolved them. The logic is that these team members are likely to be best suited to take on the issue since they've tackled similar issues before.\n\nIn your response, for each matching issue, list the following:\n* issue_key\n* assignee_id\n* assignee_name"}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "c28e06eb-a8bd-4abc-821d-5efee7bbdf99", "name": "Check User Workflow", "type": "n8n-nodes-base.jira", "position": [880, 580], "parameters": {"options": {"jql": "=status = \"In Progress\"\nAND assignee = \"{{ $json.assignee_id }}\""}, "operation": "getAll"}, "credentials": {"jiraSoftwareCloudApi": {"id": "IH5V74q6PusewNjD", "name": "Jira SW Cloud account"}}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "6bcad177-dc47-42f3-893d-5d64f28b8d75", "name": "For Each User", "type": "n8n-nodes-base.splitInBatches", "position": [680, 380], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "5112d1da-464a-42e9-9d76-1e6064f1ebfc", "name": "Assign User to Ticket", "type": "n8n-nodes-base.jira", "position": [1520, 620], "parameters": {"issueKey": "={{ $('Issue Ref').item.json.key }}", "operation": "update", "updateFields": {"assignee": {"__rl": true, "mode": "id", "value": "={{ $json.assignee_id }}"}}}, "credentials": {"jiraSoftwareCloudApi": {"id": "IH5V74q6PusewNjD", "name": "Jira SW Cloud account"}}, "typeVersion": 1}, {"id": "4063032d-5103-4b24-b04c-db3e1ba1002f", "name": "Schedule Trigger1", "type": "n8n-nodes-base.scheduleTrigger", "position": [-1520, 300], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.2}, {"id": "44a07e61-6edd-4beb-b7e3-4c7474cb620f", "name": "Remove Duplicates", "type": "n8n-nodes-base.removeDuplicates", "position": [-1380, -220], "parameters": {"options": {}, "operation": "removeItemsSeenInPreviousExecutions", "dedupeValue": "={{ $json.key }}"}, "typeVersion": 2}, {"id": "1084ffd4-99a6-4a10-a209-1c6c83d0df02", "name": "Collect Fields", "type": "n8n-nodes-base.set", "position": [-1200, -220], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d68a1967-a68e-49cf-9a7c-bd2093dd953d", "name": "project_key", "type": "string", "value": "={{ $json.fields.project.key }}"}, {"id": "16dcfcff-4dc9-4cca-bd65-6631533e6438", "name": "issue_key", "type": "string", "value": "={{ $json.key }}"}, {"id": "645b7ba5-440d-45cc-9051-b58fac3cf8b6", "name": "issue_type", "type": "string", "value": "={{ $json.fields.issuetype.name }}"}, {"id": "26863d50-042a-41bb-9579-5af24ed291cb", "name": "created_date", "type": "string", "value": "={{ $json.fields.created }}"}, {"id": "231d153f-a189-4d16-a2c1-77a3de8bfba4", "name": "resolution_date", "type": "string", "value": "={{ $json.fields.resolutiondate }}"}, {"id": "46c67aaf-6731-4890-800b-7a3361b1c7f0", "name": "assignee_id", "type": "string", "value": "={{ $json.fields.assignee.accountId }}"}, {"id": "48103da0-3c14-442a-9b5b-711f720373c7", "name": "assignee_name", "type": "string", "value": "={{ $json.fields.assignee.displayName }}"}, {"id": "1b3de52c-c558-4b76-87dd-2a6874789254", "name": "title", "type": "string", "value": "={{ $json.fields.summary }}"}, {"id": "********-2d60-4345-8443-34e3a1d4dff0", "name": "description", "type": "string", "value": "={{ $json.fields.description }}"}]}}, "typeVersion": 3.4}, {"id": "5109b7f5-61e1-4634-b29c-276c9c4fff23", "name": "Get Unassigned Tickets more than 5 days", "type": "n8n-nodes-base.jira", "position": [-1340, 300], "parameters": {"options": {"jql": "=project = \"My Kanban Project\"\nAND status = \"To Do\"\nAND assignee IS EMPTY\nAND assignee CHANGED BEFORE -5d"}, "operation": "getAll"}, "credentials": {"jiraSoftwareCloudApi": {"id": "IH5V74q6PusewNjD", "name": "Jira SW Cloud account"}}, "typeVersion": 1}, {"id": "7fcd1b7e-4bcd-4d09-b306-dd5b5de685e0", "name": "For Each Issue", "type": "n8n-nodes-base.splitInBatches", "position": [-1040, 300], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "eed6d212-daae-49ee-81e9-0b550cb3a34c", "name": "Issue Ref", "type": "n8n-nodes-base.noOp", "position": [-840, 300], "parameters": {}, "typeVersion": 1}, {"id": "041949bc-ad09-45bb-acc0-915092cde6ad", "name": "Add Comment to Issue", "type": "n8n-nodes-base.jira", "position": [1700, 620], "parameters": {"comment": "=Auto-assigned to {{ $('Count Assigned Open Issues per User').item.json.assignee_name }} due to no assignee within past 5 days.", "options": {}, "issueKey": "={{ $('Issue Ref').item.json.key }}", "resource": "issueComment"}, "credentials": {"jiraSoftwareCloudApi": {"id": "IH5V74q6PusewNjD", "name": "Jira SW Cloud account"}}, "typeVersion": 1}, {"id": "ccb525d9-fc6e-47f3-ac2a-dde4c266962b", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1620, -460], "parameters": {"color": 7, "width": 580, "height": 460, "content": "## 1. Get Resolved Issues\n[Learn more about the JIRA node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.jira/)\n\nTo build our database of successfully resolved issues, we can pull them directly from JIRA with a JQL query. The remove duplicates node ensures we only add an issues into the database once."}, "typeVersion": 1}, {"id": "c4868156-e663-46b1-8979-b561dcb0620b", "name": "Last 50 Resolved", "type": "n8n-nodes-base.jira", "position": [-1560, -220], "parameters": {"options": {"jql": "=project = \"My Kanban Project\"\nAND status = \"Done\"\nAND assignee IS NOT EMPTY\nAND created >= -1d"}, "operation": "getAll"}, "credentials": {"jiraSoftwareCloudApi": {"id": "IH5V74q6PusewNjD", "name": "Jira SW Cloud account"}}, "typeVersion": 1}, {"id": "bdf6c882-7a91-485a-9f75-4c27ba5b936c", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-1000, -660], "parameters": {"color": 7, "width": 660, "height": 660, "content": "## 2. Create Search Index In Vector Database\n[Learn more about the Supabase Vector Store](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.vectorstoresupabase)\n\nSupabase is a third party database provider which serves traditional PostgreSQL but also supports Vector databases via the Pg-Vector extension. You will require some initial setup but easily done through Supabase's [Langchain quickstart method ](https://supabase.com/docs/guides/ai/langchain?database-method=sql)"}, "typeVersion": 1}, {"id": "806a7ac1-999b-49f7-94b4-386341a2a4e1", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-1620, 80], "parameters": {"color": 7, "width": 500, "height": 460, "content": "## 3. Watch for Stale Unassigned Issues\n[Read more about the Scheduled Trigger](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.scheduletrigger/)\n\nHere, we're using a scheduled trigger to watch for stale issues where stale means unassigned issues for more than 5 days. As to not let these fall through the cracks, let's see if we can auto-assign to a team member based on relevance."}, "typeVersion": 1}, {"id": "28c2f824-868d-4b0b-b362-7cfa31ad23d6", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-860, 80], "parameters": {"color": 7, "width": 1380, "height": 700, "content": "## 4. Find Similar Issues which have been Resolved\n[Learn more about AI Agents](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.agent/)\n\nOur first step is to find similar but resolved issues. The logic is that if we find these issues, the team member who resolved them will likely be the best person in terms of context and experience to address the current stale issue. Here, we tap back into our resolved issues vector store database for this purpose."}, "typeVersion": 1}, {"id": "91134c07-9df5-4a01-ab69-b9461698e260", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [540, 120], "parameters": {"color": 7, "width": 800, "height": 720, "content": "## 5. Work out which Knowledgeable Team Member has most Capacity\n[Learn more about the Summarize node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.summarize/)\n\nIf we've found similar resolved issues, we can then identify the last assignee of the issue as a possible candidate to assign the stale issue to. But before we do, we can do a quick check to see how many open issues the team member is currently assigned. We'll pick the team member with the least amount or in another way, the most capacity."}, "typeVersion": 1}, {"id": "3a7e0c75-148d-4bef-ab2a-ef2246c369d6", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1380, 360], "parameters": {"color": 7, "width": 560, "height": 480, "content": "## 6. Auto-assign Stale Issue to Team Member\n[Learn more about the JIRA node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.jira/)\n\nFinally, we'll auto-assign the team member to the stale issue and leave a comment. This continues until all stale issues that can be assigned, are assigned."}, "typeVersion": 1}, {"id": "9af64316-0380-4a23-8935-a58a829e9064", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-200, 440], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "f67f4290-b7f7-4034-9c78-3ff38cbb256f", "name": "Issues to Items", "type": "n8n-nodes-base.splitOut", "position": [20, 300], "parameters": {"options": {}, "fieldToSplitOut": "output"}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "********-7638-4b07-8aec-ad30412b2879", "name": "To Structured Output", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [-300, 300], "parameters": {"text": "={{ $json.output }}", "options": {}, "schemaType": "manual", "inputSchema": "{\n    \"type\": \"array\",\n    \"items\": {\n        \"type\": \"object\",\n        \"required\": [\"issue_key\",\"assignee_id\",\"assignee_name\"],\n        \"properties\": {\n            \"issue_key\": { \"type\": \"string\" },\n            \"assignee_id\": { \"type\": \"string\" },\n            \"assignee_name\": { \"type\": \"string\" }\n        }\n    }\n}"}, "typeVersion": 1}, {"id": "bd950805-811f-49d0-9a32-a54cf647e819", "name": "Count Assigned Open Issues per User", "type": "n8n-nodes-base.summarize", "position": [880, 380], "parameters": {"options": {}, "fieldsToSplitBy": "assignee_id", "fieldsToSummarize": {"values": [{"field": "in_progress"}]}}, "typeVersion": 1.1}, {"id": "fddbc5de-21a2-434e-ab1c-c6b06d96d2c7", "name": "Tally In-Progress Issues per User", "type": "n8n-nodes-base.set", "position": [1080, 580], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "48221b51-ef3a-4e62-ba13-8a305e8787e9", "name": "assignee_id", "type": "string", "value": "={{ $('For Each User').item.json.assignee_id }}"}, {"id": "60b212ff-8ad3-414b-8aac-e93dbeb1f359", "name": "in_progress", "type": "string", "value": "={{ $json.isNotEmpty() ? 1 : 2 }}"}]}}, "typeVersion": 3.4}, {"id": "1bde2079-2c61-4024-889e-178afede1bf4", "name": "Sort By Most Capacity", "type": "n8n-nodes-base.sort", "position": [1080, 380], "parameters": {"options": {}, "sortFieldsUi": {"sortField": [{"fieldName": "count_in_progress"}]}}, "typeVersion": 1}, {"id": "22691a79-fa71-40b6-b4f8-bcd82864dce5", "name": "If has Items?", "type": "n8n-nodes-base.if", "position": [180, 300], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "5366f6f7-68e6-4bd8-ba8e-030abdbf34e3", "operator": {"type": "object", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "8030303e-97ce-4ab2-8f3f-ae44f82c6815", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.noOp", "position": [340, 620], "parameters": {}, "typeVersion": 1}, {"id": "6245bd37-15ce-4c3c-9430-8708e5be5b13", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-60, 620], "parameters": {"color": 5, "width": 360, "height": 120, "content": "### What is no similar issues are found?\nThis is beyond the scope of this template so we'll skip the issue but in this situation, you may want to escalate to the project manager instead."}, "typeVersion": 1}, {"id": "8919a9c2-063e-4d69-977b-e0c3f1e28c50", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-2140, -1080], "parameters": {"width": 480, "height": 1080, "content": "## Try it out\n### This n8n template builds a simple automation to ensure no JIRA issues go unassigned for more than a week to prevent them falling through the cracks. It uses AI to perform searching tasks against a Supabase Vector Store.\nThis can be one way to help reduce the amount of manual work in managing the issue backlog for busy teams with little effort.\n\n### How it works\n* This template contains 2 separate flows which run continuously via schedule triggers.\n* The first populates our Supabase vector store with resolved issues within the last day. This helps keep our vector store up-to-date and relevant for the purpose of finding similar issues.\n* It does this by pulling the latest resolved issues from JIRA and populating the Supabase vectorstore with carefully chosen metadata. This will come in handy later.\n* The second flow watches for stale, unassigned issues for the purpose of aut-assigning to a relevant team member.\n* It does this by comparing the stale issue against our vector store of resolved issues with the goal of identifying which team member would have best context regarding the issue.\n* In a busy team, this may net a few team members as possible candidates to assign. Therefore, we can introduce additional logic to count each team member's assigned, in-progress issues. This is intended to not overload our busiest members.\n* The team member with the least assigned issues is pressumed to have the most capacity and therefore is assigned. A comennt is left in the issue to notify the team member that they've been auto-assigned due to age of issue.\n\n### How to use\n* Modify the project and interval parameters to match those of your use-case and team members.\n* Add additional criteria before assigning to a team member eg. department, as required.\n\n### Need Help?\nJoin the [Discord](https://discord.com/invite/XPKeKXeB7d) or ask in the [Forum](https://community.n8n.io/)!\n\nHappy Hacking!"}, "typeVersion": 1}], "pinData": {}, "connections": {"Skip": {"main": [[{"node": "For Each Issue", "type": "main", "index": 0}]]}, "Issue Ref": {"main": [[{"node": "Find Similar Issues + Assignees", "type": "main", "index": 0}]]}, "For Each User": {"main": [[{"node": "Count Assigned Open Issues per User", "type": "main", "index": 0}], [{"node": "Check User Workflow", "type": "main", "index": 0}]]}, "If has Items?": {"main": [[{"node": "For Each User", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Collect Fields": {"main": [[{"node": "Issues Similarity Database", "type": "main", "index": 0}]]}, "For Each Issue": {"main": [[], [{"node": "Issue Ref", "type": "main", "index": 0}]]}, "Issues to Items": {"main": [[{"node": "If has Items?", "type": "main", "index": 0}]]}, "Last 50 Resolved": {"main": [[{"node": "Remove Duplicates", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Last 50 Resolved", "type": "main", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Issues Similarity Database", "type": "ai_embedding", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Find Similar Issues + Assignees", "type": "ai_languageModel", "index": 0}]]}, "Remove Duplicates": {"main": [[{"node": "Collect Fields", "type": "main", "index": 0}]]}, "Schedule Trigger1": {"main": [[{"node": "Get Unassigned Tickets more than 5 days", "type": "main", "index": 0}]]}, "Embeddings OpenAI1": {"ai_embedding": [[{"node": "Supabase Vector Store", "type": "ai_embedding", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "To Structured Output", "type": "ai_languageModel", "index": 0}]]}, "Check User Workflow": {"main": [[{"node": "Tally In-Progress Issues per User", "type": "main", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Issues Similarity Database", "type": "ai_document", "index": 0}]]}, "Add Comment to Issue": {"main": [[{"node": "For Each Issue", "type": "main", "index": 0}]]}, "To Structured Output": {"main": [[{"node": "Issues to Items", "type": "main", "index": 0}]]}, "Assign User to Ticket": {"main": [[{"node": "Add Comment to Issue", "type": "main", "index": 0}]]}, "Sort By Most Capacity": {"main": [[{"node": "Assign User to Ticket", "type": "main", "index": 0}]]}, "Supabase Vector Store": {"ai_tool": [[{"node": "Find Similar Issues + Assignees", "type": "ai_tool", "index": 0}]]}, "Find Similar Issues + Assignees": {"main": [[{"node": "To Structured Output", "type": "main", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Tally In-Progress Issues per User": {"main": [[{"node": "For Each User", "type": "main", "index": 0}]]}, "Count Assigned Open Issues per User": {"main": [[{"node": "Sort By Most Capacity", "type": "main", "index": 0}]]}, "Get Unassigned Tickets more than 5 days": {"main": [[{"node": "For Each Issue", "type": "main", "index": 0}]]}}}