{"id": "lC8xkfCSTjIiUhpk", "meta": {"instanceId": "a1f3364de0f3da48758a2641efb07c3b0d216a3a7cc93596fbed2316d6dea4ad", "templateCredsSetupCompleted": true}, "name": "Google Drive Automation", "tags": [], "nodes": [{"id": "e7769ee7-a247-426e-b792-c095597ada54", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [320, 700], "parameters": {"text": "={{ $json.prompt }}", "options": {"systemMessage": "You are a knowledgeable and helpful assistant. Respond with clear, concise, and detailed answers formatted in markdown. Use proper markdown formatting including headings, bullet points, numbered lists, code blocks, and other structures as needed to improve readability and clarity."}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "72ca46ad-891f-42f2-81d7-00e04e1c6f5f", "name": "Monitor Google Drive for New Files", "type": "n8n-nodes-base.googleDriveTrigger", "position": [-520, -240], "parameters": {"event": "fileCreated", "options": {}, "pollTimes": {"item": [{}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "mode": "list", "value": "1RQvAHIw8cQbtwI9ZvdVV0k0x6TM6HZwP", "cachedResultUrl": "https://drive.google.com/drive/folders/1RQvAHIw8cQbtwI9ZvdVV0k0x6TM6HZwP", "cachedResultName": "RAG_Files"}}, "credentials": {"googleDriveOAuth2Api": {"id": "zj3v6gsTRb9CreKV", "name": "Google Drive account"}}, "typeVersion": 1}, {"id": "03e9dc61-bdba-49d7-859e-73b8adebae41", "name": "Download File from Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [-300, -240], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "options": {}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "zj3v6gsTRb9CreKV", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "782fc162-0c3f-40fc-af92-455c1250ede0", "name": "Extract PDF Content", "type": "n8n-nodes-base.extractFromFile", "position": [-80, -240], "parameters": {"options": {}, "operation": "pdf"}, "typeVersion": 1}, {"id": "b8da9cff-756b-419e-b39a-4ad1020092d0", "name": "Insert Document into Pinecone Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "position": [360, -240], "parameters": {"mode": "insert", "options": {}, "pineconeIndex": {"__rl": true, "mode": "list", "value": "n8n-rag-demo", "cachedResultName": "n8n-rag-demo"}}, "credentials": {"pineconeApi": {"id": "ldIxYWz8E9e0N4yV", "name": "PineconeApi account"}}, "typeVersion": 1}, {"id": "f5b93646-b466-4cd7-aec9-6fae62023fa3", "name": "Generate Document Embeddings (Google Gemini)", "type": "@n8n/n8n-nodes-langchain.embeddingsGoogleGemini", "position": [260, 20], "parameters": {"modelName": "models/text-embedding-004"}, "credentials": {"googlePalmApi": {"id": "prd6Qnbbj4UbNH75", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "b5277663-3120-4614-85e3-f7dc05c4e1c2", "name": "Clean and Normalize PDF Text", "type": "n8n-nodes-base.code", "position": [140, -240], "parameters": {"jsCode": "const rawData = $json[\"text\"];\nconst cleanedData = rawData\n  .replace(/(\\r\\n|\\n|\\r)/gm, \" \")   // remove line breaks\n  .trim()                           // remove extra spaces\n  .replace(/[^\\w\\s]/gi, \"\");         // remove special characters\nreturn { cleanedData };\n"}, "typeVersion": 2}, {"id": "68aa5515-6b58-4e98-ab08-4d9516e1f2a3", "name": "Load Document Data for Processing", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [480, 20], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "3f463338-c692-4b7b-a888-8c00d190c441", "name": "Split Document Text into Chunks", "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "position": [380, 240], "parameters": {"options": {}, "chunkSize": 3000, "chunkOverlap": 300}, "typeVersion": 1}, {"id": "9c4a7ec9-0808-443f-9e12-9ec12c7288b9", "name": "Chat Message Trigger", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-520, 700], "webhookId": "d36e67b9-a789-4801-b624-64bf8b88d702", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "ee62efc9-60b2-40ec-a10c-8897d24b1429", "name": "Retrieve Relevant Documents from Pinecone", "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "position": [-260, 700], "parameters": {"mode": "load", "prompt": "={{ $json.chatInput }}", "options": {}, "pineconeIndex": {"__rl": true, "mode": "list", "value": "n8n-rag-demo", "cachedResultName": "n8n-rag-demo"}}, "credentials": {"pineconeApi": {"id": "ldIxYWz8E9e0N4yV", "name": "PineconeApi account"}}, "typeVersion": 1}, {"id": "8d479b6b-3c87-40c6-8a68-4390e6bafac8", "name": "Generate Query Embeddings (Google Gemini)", "type": "@n8n/n8n-nodes-langchain.embeddingsGoogleGemini", "position": [-280, 940], "parameters": {"modelName": "models/text-embedding-004"}, "credentials": {"googlePalmApi": {"id": "prd6Qnbbj4UbNH75", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "f521d243-1b62-4bc5-972d-736c65c48818", "name": "Generate Chat Prompt with Context", "type": "n8n-nodes-base.code", "position": [100, 700], "parameters": {"jsCode": "const userQuery =  $('Chat Message Trigger').first().json.chatInput\n// Retrieve the user query from the previous node output.\n// Assuming the Pinecone node has passed an array of items where each item has a document and score:\nlet documents = items.map(item => {\n  return {\n    pageContent: item.json.document.pageContent,\n    score: item.json.score\n  };\n});\n\n// Sort the documents by their score in descending order.\ndocuments.sort((a, b) => b.score - a.score);\n\n// Pick the top 3 documents to use as context.\nconst topDocuments = documents.slice(0, 3);\n\n// Combine the top documents into one context string.\nconst contextContent = topDocuments\n  .map((doc, index) => `Document ${index + 1}:\\n${doc.pageContent}`)\n  .join(\"\\n\\n\");\n\n// Build the final prompt that combines the context with the user query.\nconst prompt = `Using the following context from documents:\\n\\n${contextContent}\\n\\nAnswer the following question:\\n${userQuery}\\n\\nAnswer:`;\n\n// Return the prompt so it can be passed to a Chat/AI node for further processing.\nreturn [{ json: { prompt } }];\n"}, "typeVersion": 2}, {"id": "208057c8-8672-41d2-9c99-89e52856a742", "name": "OpenRouter Chat Model Interface", "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "position": [280, 940], "parameters": {"model": "google/gemini-2.0-flash-exp:free", "options": {}}, "credentials": {"openRouterApi": {"id": "iTDRPtvPicVqeXaT", "name": "OpenRouter account"}}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "43fd0dd9-5ec1-401a-b1c2-368b15c9f0db", "connections": {"Extract PDF Content": {"main": [[{"node": "Clean and Normalize PDF Text", "type": "main", "index": 0}]]}, "Chat Message Trigger": {"main": [[{"node": "Retrieve Relevant Documents from Pinecone", "type": "main", "index": 0}]]}, "Clean and Normalize PDF Text": {"main": [[{"node": "Insert Document into Pinecone Vector Store", "type": "main", "index": 0}]]}, "Download File from Google Drive": {"main": [[{"node": "Extract PDF Content", "type": "main", "index": 0}]]}, "OpenRouter Chat Model Interface": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Split Document Text into Chunks": {"ai_textSplitter": [[{"node": "Load Document Data for Processing", "type": "ai_textSplitter", "index": 0}]]}, "Generate Chat Prompt with Context": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Load Document Data for Processing": {"ai_document": [[{"node": "Insert Document into Pinecone Vector Store", "type": "ai_document", "index": 0}]]}, "Monitor Google Drive for New Files": {"main": [[{"node": "Download File from Google Drive", "type": "main", "index": 0}]]}, "Generate Query Embeddings (Google Gemini)": {"ai_embedding": [[{"node": "Retrieve Relevant Documents from Pinecone", "type": "ai_embedding", "index": 0}]]}, "Retrieve Relevant Documents from Pinecone": {"main": [[{"node": "Generate Chat Prompt with Context", "type": "main", "index": 0}]]}, "Generate Document Embeddings (Google Gemini)": {"ai_embedding": [[{"node": "Insert Document into Pinecone Vector Store", "type": "ai_embedding", "index": 0}]]}}}