{"nodes": [{"name": "Airtable", "type": "n8n-nodes-base.airtable", "position": [1090, 340], "parameters": {"table": "Table 1", "options": {}, "operation": "append", "application": ""}, "credentials": {"airtableApi": "Airtable Credentials n8n"}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [890, 340], "parameters": {"values": {"string": [{"name": "Name", "value": "={{$json[\"contact_name\"]}}"}, {"name": "Email", "value": "={{$json[\"contact_email\"]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "GetResponse Trigger", "type": "n8n-nodes-base.getResponseTrigger", "position": [690, 340], "webhookId": "4bdfc1fa-44bc-4293-987c-fb512327e845", "parameters": {"events": ["subscribe"], "listIds": ["qtPk7"], "options": {}}, "credentials": {"getResponseApi": "GetResponse API Credentials"}, "typeVersion": 1}], "connections": {"Set": {"main": [[{"node": "Airtable", "type": "main", "index": 0}]]}, "GetResponse Trigger": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}}