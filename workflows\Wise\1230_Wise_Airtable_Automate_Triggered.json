{"nodes": [{"name": "<PERSON> Trigger", "type": "n8n-nodes-base.wiseTrigger", "position": [450, 280], "webhookId": "df8c0c06-7d40-4e57-aaff-60f458e6997c", "parameters": {"event": "tranferStateChange", "profileId": 16138858}, "credentials": {"wiseApi": "Wise API Credentials"}, "typeVersion": 1}, {"name": "<PERSON>", "type": "n8n-nodes-base.wise", "position": [650, 280], "parameters": {"resource": "transfer", "transferId": "={{$json[\"data\"][\"resource\"][\"id\"]}}"}, "credentials": {"wiseApi": "Wise API Credentials"}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [850, 280], "parameters": {"values": {"string": [{"name": "Transfer ID", "value": "={{$json[\"id\"]}}"}, {"name": "Date", "value": "={{$json[\"created\"]}}"}, {"name": "Reference", "value": "={{$json[\"reference\"]}}"}, {"name": "Amount", "value": "={{$json[\"sourceValue\"]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "Airtable", "type": "n8n-nodes-base.airtable", "position": [1050, 280], "parameters": {"table": "Table 1", "options": {}, "operation": "append", "application": ""}, "credentials": {"airtableApi": "Airtable Credentials n8n"}, "typeVersion": 1}], "connections": {"Set": {"main": [[{"node": "Airtable", "type": "main", "index": 0}]]}, "Wise": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}, "Wise Trigger": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}}}