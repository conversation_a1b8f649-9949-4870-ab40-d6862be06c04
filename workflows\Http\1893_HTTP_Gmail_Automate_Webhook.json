{"id": "jbTm6O9bLBMm6RWy", "meta": {"instanceId": "7b7fd5f72a378d0859f4d1cf8dd3c226094df4777ef6aca192ac32e815fe212a", "templateCredsSetupCompleted": true}, "name": "My workflow 3", "tags": [], "nodes": [{"id": "24be1991-3de5-49c2-91a1-c636fb721a87", "name": "Weekly Trigger (Monday 7AM)", "type": "n8n-nodes-base.cron", "position": [80, 180], "parameters": {}, "typeVersion": 1}, {"id": "43d7764d-fbd4-414b-be44-bcc80c068db2", "name": "Get SEO Data from GSC", "type": "n8n-nodes-base.httpRequest", "position": [300, 180], "parameters": {"url": "https://searchconsole.googleapis.com/webmasters/v3/sites/YOUR_SITE_URL/searchAnalytics/query", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth"}, "typeVersion": 2}, {"id": "92852fd3-7663-413e-b1a9-8c728dea9a23", "name": "Generate SEO Report", "type": "n8n-nodes-base.function", "position": [500, 180], "parameters": {"functionCode": "\n                const rows = items[0].json.rows || [];\n                const reportLines = rows.map((row, index) => {\n                    return `${index + 1}. ${row.keys[0]} - Clicks: ${row.clicks}, Impressions: ${row.impressions}, CTR: ${row.ctr.toFixed(2)}, Position: ${row.position.toFixed(2)}`;\n                });\n                return [{\n                    json: {\n                        report: `Top 10 Search Queries (Last 7 Days):\\n\\n${reportLines.join(\"\\n\")}`\n                    }\n                }];\n            "}, "typeVersion": 1}, {"id": "28d9f152-15a0-4a66-aa5e-aa6b9b4c1fa3", "name": "📌 Setup Instructions", "type": "n8n-nodes-base.stickyNote", "position": [-60, 60], "parameters": {"color": 6, "width": 280, "height": 320, "content": "\n"}, "typeVersion": 1}, {"id": "8e9551d4-27ab-4106-b0cd-b82d6a671ec7", "name": "📌 Google Search Console Config", "type": "n8n-nodes-base.stickyNote", "position": [240, 60], "parameters": {"color": 2, "height": 320, "content": ""}, "typeVersion": 1}, {"id": "c2aabd2e-0a2b-4b4b-a239-bf0927ad1e4d", "name": "📌 <PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [640, 40], "parameters": {"color": 5, "height": 360, "content": ""}, "typeVersion": 1}, {"id": "1b870a08-d53c-4a51-9a41-4d71a5c954f9", "name": "Send Weekly Report by Email", "type": "n8n-nodes-base.gmail", "position": [720, 180], "webhookId": "c9455684-b943-41a5-b2d7-adeafb985083", "parameters": {"sendTo": "<EMAIL>", "options": {}, "subject": "Send Weekly Report by Email"}, "credentials": {"gmailOAuth2": {"id": "6dONI23VTND78rYK", "name": "Gmail account"}}, "typeVersion": 2.1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "********-06bb-40fd-a300-b89a73676d8d", "connections": {"Generate SEO Report": {"main": [[{"node": "Send Weekly Report by Email", "type": "main", "index": 0}]]}, "Get SEO Data from GSC": {"main": [[{"node": "Generate SEO Report", "type": "main", "index": 0}]]}, "Weekly Trigger (Monday 7AM)": {"main": [[{"node": "Get SEO Data from GSC", "type": "main", "index": 0}]]}}}