{"meta": {"instanceId": "257476b1ef58bf3cb6a46e65fac7ee34a53a5e1a8492d5c6e4da5f87c9b82833"}, "nodes": [{"id": "bcd8e7dc-cb7f-4e2b-a0c6-2d154cb58938", "name": "n8n Form Trigger", "type": "n8n-nodes-base.formTrigger", "position": [820, 360], "webhookId": "0bf8840f-1cc4-46a9-86af-a3fa8da80608", "parameters": {"path": "0bf8840f-1cc4-46a9-86af-a3fa8da80608", "options": {}, "formTitle": "Contact us", "formFields": {"values": [{"fieldLabel": "What's your business email?"}]}, "formDescription": "We'll get back to you soon"}, "typeVersion": 2}, {"id": "0720ab51-5222-46fe-8a1a-31e25b81920c", "name": "<PERSON>", "type": "n8n-nodes-base.hunter", "position": [1040, 360], "parameters": {"email": "={{ $json['What\\'s your business email?'] }}", "operation": "emailVerifier"}, "credentials": {"hunterApi": {"id": "oIxKoEBTBJeT1UrT", "name": "Hunter account"}}, "typeVersion": 1}, {"id": "c20c626f-fd58-497f-942f-5d10f198f36d", "name": "Check if the email is valid", "type": "n8n-nodes-base.if", "position": [1240, 360], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "54d84c8a-63ee-40ed-8fb2-301fff0194ba", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "valid"}]}}, "typeVersion": 2}, {"id": "9c55911c-06b7-4291-a91d-30c0cb87b7f2", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [820, 180], "parameters": {"color": 5, "width": 547, "height": 132, "content": "### 👨‍🎤 Setup\n1. Add you **Hunter**, **Clearbit** and **Hubspot** credentials\n2. Click the Test Workflow button, enter your email and check your Hubspot\n3. Activate the workflow and use the form trigger production URL to collect your leads in a smart way "}, "typeVersion": 1}, {"id": "4e518b0c-20e6-4fb3-8be9-c0989c750fda", "name": "Enrich company", "type": "n8n-nodes-base.clearbit", "position": [1620, 300], "parameters": {"domain": "={{ $json.employment.domain }}", "additionalFields": {}}, "credentials": {"clearbitApi": {"id": "cKDImrinp9tg0ZHW", "name": "Clearbit account"}}, "typeVersion": 1}, {"id": "47e8324b-c455-40b5-8769-4d2c4718de75", "name": "Add lead to <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.hubspot", "position": [1940, 300], "parameters": {"email": "={{ $('Check if the email is valid').item.json.email }}", "options": {}, "authentication": "oAuth2", "additionalFields": {"jobTitle": "={{ $('Enrich person').item.json.employment.title }}", "lastName": "={{ $('Enrich person').item.json.name.familyName }}", "firstName": "={{ $('Enrich person').item.json.name.givenName }}", "companyName": "={{ $('Enrich person').item.json.employment.name }}", "companySize": "={{ $json.metrics.employees }}"}}, "credentials": {"hubspotOAuth2Api": {"id": "WEONgGVHLYPjIE6k", "name": "HubSpot account"}}, "typeVersion": 2}, {"id": "********-9283-44fd-a1b7-9b31bbe9cbd2", "name": "Enrich person", "type": "n8n-nodes-base.clearbit", "position": [1460, 300], "parameters": {"email": "={{ $json.email }}", "resource": "person", "additionalFields": {}}, "credentials": {"clearbitApi": {"id": "cKDImrinp9tg0ZHW", "name": "Clearbit account"}}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "c96096f2-6505-4955-bb1b-c4f903428b1d", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [820, 500], "parameters": {"color": 7, "width": 162, "height": 139, "content": "👆 You can exchange this with any form you like (*e.g. Typeform, Google forms, Survey Monkey...*)"}, "typeVersion": 1}, {"id": "751458aa-7b63-48ab-881e-d68df94a3390", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1940, 460], "parameters": {"color": 7, "width": 162, "height": 84, "content": "👆 Adjust the fields you need in your Hubspot here"}, "typeVersion": 1}, {"id": "************************************", "name": "Email is not valid, do nothing", "type": "n8n-nodes-base.noOp", "position": [1460, 480], "parameters": {}, "typeVersion": 1}, {"id": "32bc2dc2-7b5c-4fc4-bf9f-a1231c6512d0", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1740, 180], "parameters": {"color": 7, "width": 162, "height": 136.49297423887586, "content": "👇 Idea: You could add criteria on when to add a lead to your Hubspot here. For inspiration, take a look at [this template](https://n8n.io/workflows/2106-reach-out-via-email-to-new-form-submissions-that-meet-a-certain-criteria)"}, "typeVersion": 1}], "pinData": {"Hunter": [{"block": false, "email": "<EMAIL>", "score": 100, "regexp": true, "result": "deliverable", "status": "valid", "sources": [{"uri": "http://community.n8n.io/t/cant-send-email-with-multiple-attachments/22736/9", "domain": "community.n8n.io", "extracted_on": "2023-10-13", "last_seen_on": "2024-01-14", "still_on_page": true}, {"uri": "http://community.n8n.io/t/cant-send-email-with-multiple-attachments/22736", "domain": "community.n8n.io", "extracted_on": "2023-07-13", "last_seen_on": "2024-01-11", "still_on_page": true}], "webmail": false, "gibberish": false, "accept_all": false, "disposable": false, "mx_records": true, "smtp_check": true, "smtp_server": true, "_deprecation_notice": "Using result is deprecated, use status instead"}], "Enrich person": [{"id": "f679f5ef-f7a0-4cb1-8790-fe663a0c092f", "bio": null, "geo": {"lat": 53.5510846, "lng": 9.9936819, "city": "Hamburg", "state": "Hamburg", "country": "Germany", "stateCode": "HH", "countryCode": "DE"}, "name": {"fullName": "<PERSON><PERSON>", "givenName": "<PERSON><PERSON>", "familyName": "<PERSON><PERSON>"}, "site": null, "email": "<EMAIL>", "fuzzy": false, "avatar": null, "github": {"id": null, "blog": null, "avatar": null, "handle": null, "company": null, "followers": null, "following": null}, "twitter": {"id": null, "bio": null, "site": null, "avatar": null, "handle": null, "location": null, "statuses": null, "favorites": null, "followers": null, "following": null}, "facebook": {"handle": null}, "gravatar": {"urls": [], "avatar": null, "handle": null, "avatars": []}, "linkedin": {"handle": "in/niklashatje"}, "location": "Hamburg, HH, DE", "timeZone": "Europe/Berlin", "indexedAt": "2024-01-24T15:49:16.888Z", "utcOffset": 1, "employment": {"name": "n8n", "role": null, "title": "Senior Produktmanager", "domain": "n8n.io", "subRole": null, "seniority": "manager"}, "googleplus": {"handle": null}, "emailProvider": false}], "Enrich company": [{"id": "546ba3f6-a6b7-41a1-aed8-4f9bba4119e8", "geo": {"lat": 52.5297761, "lng": 13.3892831, "city": "Berlin", "state": "Berlin", "country": "Germany", "stateCode": "BE", "postalCode": "10115", "streetName": "Borsigstraße", "subPremise": null, "countryCode": "DE", "streetNumber": "27", "streetAddress": "27 Borsigstraße"}, "logo": "https://logo.clearbit.com/n8n.io", "name": "n8n", "site": {"phoneNumbers": [], "emailAddresses": []}, "tags": ["Information Technology & Services", "Computer Programming", "Software", "Professional Services", "Computers", "E-commerce", "Technology", "B2B", "B2C", "SAAS", "Mobile"], "tech": ["mailgun", "cloud_flare", "workable", "google_tag_manager", "google_apps", "typeform", "google_analytics", "facebook_advertiser", "stripe"], "type": "private", "phone": null, "domain": "n8n.io", "parent": {"domain": null}, "ticker": null, "metrics": {"raised": 13500000, "employees": 60, "marketCap": null, "alexaUsRank": null, "trafficRank": "high", "annualRevenue": null, "fiscalYearEnd": null, "employeesRange": "51-250", "alexaGlobalRank": 61323, "estimatedAnnualRevenue": "$10M-$50M"}, "twitter": {"id": "1068479892537384960", "bio": "n8n is an extendable workflow automation tool which enables you to connect anything to everything via its open, fair-code model.", "site": "https://t.co/F1fzJ95bij", "avatar": "https://pbs.twimg.com/profile_images/1536335358803251202/-gASF0c6_normal.png", "handle": "n8n_io", "location": "Berlin, Germany", "followers": 7238, "following": 1}, "category": {"sector": "Information Technology", "sicCode": "73", "gicsCode": "45102010", "industry": "Internet Software & Services", "naicsCode": "54", "sic4Codes": ["7371"], "naics6Codes": ["541511"], "subIndustry": "Internet Software & Services", "industryGroup": "Software & Services", "naics6Codes2022": ["541511"]}, "facebook": {"likes": null, "handle": null}, "linkedin": {"handle": "company/n8n"}, "location": "Borsigstraße 27, 10115 Berlin, Germany", "timeZone": "Europe/Berlin", "indexedAt": "2024-02-08T21:30:12.524Z", "legalName": null, "utcOffset": 1, "crunchbase": {"handle": null}, "description": "n8n.io is a powerful workflow automation tool that enables you to connect anything to everything. It is a free and open-source tool that can be installed on-premises, downloaded as a desktop app, or used as a cloud service. With n8n, you can automate b...", "foundedYear": 2019, "identifiers": {"usCIK": null, "usEIN": null}, "domainAliases": ["n8n.cloud", "n8n.com"], "emailProvider": false, "techCategories": ["email_delivery_service", "dns", "applicant_tracking_system", "tag_management", "productivity", "form_builder", "analytics", "advertising", "payment"], "ultimateParent": {"domain": null}}], "n8n Form Trigger": [{"formMode": "test", "submittedAt": "2024-02-21T18:59:22.964Z", "What's your business email?": "<EMAIL>"}]}, "connections": {"Hunter": {"main": [[{"node": "Check if the email is valid", "type": "main", "index": 0}]]}, "Enrich person": {"main": [[{"node": "Enrich company", "type": "main", "index": 0}]]}, "Enrich company": {"main": [[{"node": "Add lead to <PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "n8n Form Trigger": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Check if the email is valid": {"main": [[{"node": "Enrich person", "type": "main", "index": 0}], [{"node": "Email is not valid, do nothing", "type": "main", "index": 0}]]}}}