{"meta": {"instanceId": "21b41c2deb1c9e3f543253a0aa6a6e2c7bd7ef6bab90ffd478aa947c17d3b352", "templateCredsSetupCompleted": true}, "name": "Import Productboard Notes, Companies and Features into Snowflake", "tags": [{"id": "6Ek7V8f4xbM9vWLj", "name": "linear", "createdAt": "2024-11-08T12:12:15.330Z", "updatedAt": "2024-11-08T12:12:15.330Z"}, {"id": "XpcIJ8IHNenz3bWz", "name": "productboard", "createdAt": "2024-11-08T12:12:17.249Z", "updatedAt": "2024-11-08T12:12:17.249Z"}, {"id": "17", "name": "snowflake", "createdAt": "2023-09-18T17:05:02.756Z", "updatedAt": "2023-09-18T17:05:02.756Z"}], "nodes": [{"id": "adcb71e4-880b-4c19-acbb-0708ae4af95f", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [5620, 1440], "parameters": {"color": 5, "width": 442.66083354762577, "height": 155.09952210536395, "content": "## Preview Slack Message\n:productboard: Weekly Update in :snowflake_logo: Completed\n27 new insights added in the last 7 days.\n88 insights remain unprocessed.\nYou can view the updated :metabase: dashboard below:\n<link metabase>\n"}, "typeVersion": 1}, {"id": "8a590e59-cbcd-43f3-a0de-7c1391661fcf", "name": "Manual mapping feature", "type": "n8n-nodes-base.set", "position": [4380, -180], "parameters": {"fields": {"values": [{"name": "feature_id", "stringValue": "={{ $json.id }}"}, {"name": "feature_name", "stringValue": "={{ $json.name }}"}, {"name": "feature_status", "stringValue": "={{ $json.status.name }}"}, {"name": "feature_start_date", "stringValue": "={{ $json.timeframe.startDate }}"}, {"name": "feature_end_date", "stringValue": "={{ $json.timeframe.endDate }}"}, {"name": "feature_owner", "stringValue": "={{ $json.owner.email }}"}, {"name": "feature_created_at", "stringValue": "={{ $json.createdAt }}"}]}, "include": "none", "options": {}}, "typeVersion": 3.2}, {"id": "ca339c8f-71c0-432f-88ef-595b9bc24b98", "name": "get productboard companies", "type": "n8n-nodes-base.httpRequest", "position": [4060, 220], "parameters": {"url": "https://api.productboard.com/companies", "options": {"pagination": {"pagination": {"nextURL": "={{ $response.body[\"links\"][\"next\"] }}", "paginationMode": "responseContainsNextURL", "requestInterval": 2000, "completeExpression": "={{ $response.body[\"links\"][\"next\"] === null }}", "paginationCompleteWhen": "other"}}}, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-Version", "value": "1"}]}}, "credentials": {"httpHeaderAuth": {"id": "Z0ptr85smbBZBIYx", "name": "Productboard"}}, "typeVersion": 4.1, "alwaysOutputData": true}, {"id": "ba15244b-4311-4045-8087-47f05bea427e", "name": "Manual mapping companies", "type": "n8n-nodes-base.set", "position": [4760, 220], "parameters": {"fields": {"values": [{"name": "company_id", "stringValue": "={{ $json.id }}"}, {"name": "company_name", "stringValue": "={{ $json.name }}"}, {"name": "company_domain", "stringValue": "={{ $json.domain }}"}]}, "include": "none", "options": {}}, "typeVersion": 3.2}, {"id": "d7c491cf-6545-40e1-9ee5-429e4f6b8cb4", "name": "get productboard notes", "type": "n8n-nodes-base.httpRequest", "position": [4500, 640], "parameters": {"url": " https://api.productboard.com/notes", "options": {"pagination": {"pagination": {"parameters": {"parameters": [{"name": "pageCursor", "value": "={{ $response.body.pageCursor }}"}]}, "requestInterval": 2000, "completeExpression": "={{ $response.body.pageCursor === null }}", "paginationCompleteWhen": "other"}}}, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-Version", "value": "1"}]}}, "credentials": {"httpHeaderAuth": {"id": "Z0ptr85smbBZBIYx", "name": "Productboard"}}, "typeVersion": 4.1}, {"id": "beeb2cfc-c017-4691-b92f-ee10b943b08d", "name": "Manual mapping notes", "type": "n8n-nodes-base.set", "position": [5200, 640], "parameters": {"fields": {"values": [{"name": "note_id", "stringValue": "={{ $json.id }}"}, {"name": "note_title", "stringValue": "={{ $json.title }}"}, {"name": "note_state", "stringValue": "={{ $json.state }}"}, {"name": "note_company_id", "stringValue": "={{ $json.company.id }}"}, {"name": "note_source", "stringValue": "={{ $json.source.origin }}"}, {"name": "note_content", "stringValue": "={{ $json.content }}"}, {"name": "note_created_at", "stringValue": "={{ $json.createdAt }}"}, {"name": "note_created_by", "stringValue": "={{ $json.createdBy.name }}"}, {"name": "note_owner", "stringValue": "={{ $json.owner.name }}"}, {"name": "note_url", "stringValue": "={{ $json.displayUrl }}"}]}, "include": "none", "options": {}}, "typeVersion": 3.2}, {"id": "770df012-b5a0-49f9-9614-8988c2436c34", "name": "Split features", "type": "n8n-nodes-base.splitOut", "position": [3920, -180], "parameters": {"options": {}, "fieldToSplitOut": "data"}, "typeVersion": 1}, {"id": "910e27f0-b910-415e-a171-5c6cfce07dc4", "name": "Split companies", "type": "n8n-nodes-base.splitOut", "position": [4300, 220], "parameters": {"options": {}, "fieldToSplitOut": "data"}, "typeVersion": 1}, {"id": "f57f3865-8970-4771-aee6-2e656215b13e", "name": "Split notes", "type": "n8n-nodes-base.splitOut", "position": [4740, 640], "parameters": {"options": {}, "fieldToSplitOut": "data"}, "typeVersion": 1}, {"id": "d3939c15-9523-49c1-93ba-7942d37a0ec0", "name": "Split features in notes", "type": "n8n-nodes-base.splitOut", "position": [5400, 900], "parameters": {"include": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options": {}, "fieldToSplitOut": "features", "fieldsToInclude": "id"}, "typeVersion": 1}, {"id": "bde6dc0c-6104-4b84-8c09-33dbe0cfe69f", "name": "Combine Feature ID + Note ID", "type": "n8n-nodes-base.set", "position": [5640, 900], "parameters": {"fields": {"values": [{"name": "note_id", "stringValue": "={{ $json.id }}"}, {"name": "feature_id", "stringValue": "={{ $json.features.id }}"}]}, "include": "none", "options": {}}, "typeVersion": 3.2}, {"id": "b47db956-ec4f-4342-b973-aa3277e397f2", "name": "get productboard features", "type": "n8n-nodes-base.httpRequest", "position": [3680, -180], "parameters": {"url": "https://api.productboard.com/features", "options": {"pagination": {"pagination": {"nextURL": "={{ $response.body[\"links\"][\"next\"] }}", "paginationMode": "responseContainsNextURL", "requestInterval": 3000, "completeExpression": "={{ $response.body[\"links\"][\"next\"] === null }}", "paginationCompleteWhen": "other"}}}, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-Version", "value": "1"}]}}, "credentials": {"httpHeaderAuth": {"id": "Z0ptr85smbBZBIYx", "name": "Productboard"}}, "typeVersion": 4.1}, {"id": "ef3cd766-3887-4d6b-981b-d8e72a06a655", "name": "Update Productboard Notes", "type": "n8n-nodes-base.snowflake", "position": [5940, 660], "parameters": {"table": "PRODUCTBOARD_NOTES", "columns": "NOTE_ID,NOTE_TITLE,NOTE_STATE,NOTE_COMPANY_ID,NOTE_SOURCE,NOTE_CONTENT,NOTE_CREATED_BY,NOTE_OWNER,NOTE_CREATED_AT,NOTE_URL"}, "credentials": {"snowflake": {"id": "81", "name": "Snowflake"}}, "typeVersion": 1}, {"id": "8dc03797-1ac9-47a8-8e4c-e85e9539b091", "name": "Empty Table Productboard Notes", "type": "n8n-nodes-base.snowflake", "position": [4260, 640], "parameters": {"query": "TRUNCATE TABLE PRODUCTBOARD_NOTES;", "operation": "execute<PERSON>uery"}, "credentials": {"snowflake": {"id": "81", "name": "Snowflake"}}, "executeOnce": true, "typeVersion": 1}, {"id": "9d5a6d6f-a672-48b0-baf8-67b608690d28", "name": "[CREATE] PRODUCTBOARD_NOTES", "type": "n8n-nodes-base.snowflake", "position": [3280, 1140], "parameters": {"query": "CREATE OR <PERSON><PERSON>LACE TABLE PRODUCTBOARD_NOTES (\n    note_id STRING NOT NULL,\n    note_title STRING,\n    note_state STRING,\n    note_company_id STRING,\n    note_source STRING,\n    note_content STRING,\n    note_created_by STRING,\n    note_owner STRING,\n    note_url STRING,\n    note_created_at TIMESTAMP_NTZ\n);", "operation": "execute<PERSON>uery"}, "credentials": {"snowflake": {"id": "81", "name": "Snowflake"}}, "typeVersion": 1}, {"id": "ea27f38b-3199-46aa-959f-9c1502898696", "name": "[CREATE] PRODUCTBOARD_COMPANIES", "type": "n8n-nodes-base.snowflake", "position": [3520, 1140], "parameters": {"query": "CREATE OR R<PERSON>LACE TABLE PRODUCTBOARD_COMPANIES (\n    company_id STRING NOT NULL,\n    company_name STRING,\n    company_domain STRING\n);", "operation": "execute<PERSON>uery"}, "credentials": {"snowflake": {"id": "81", "name": "Snowflake"}}, "typeVersion": 1}, {"id": "7bb94678-d106-4b77-8a96-4c598b057d09", "name": "Update Productboard Companies", "type": "n8n-nodes-base.snowflake", "position": [5280, 220], "parameters": {"table": "PRODUCTBOARD_COMPANIES", "columns": "COMPANY_ID,COMPANY_NAME,COMPANY_DOMAIN"}, "credentials": {"snowflake": {"id": "81", "name": "Snowflake"}}, "typeVersion": 1}, {"id": "86128f9b-8b16-4dc0-bdf5-1bab946716e2", "name": "Manual mapping companies db", "type": "n8n-nodes-base.set", "position": [5020, 220], "parameters": {"fields": {"values": [{"name": "COMPANY_ID", "stringValue": "={{ $json.company_id }}"}, {"name": "COMPANY_NAME", "stringValue": "={{ $json.company_name }}"}, {"name": "COMPANY_DOMAIN", "stringValue": "={{ $json.company_domain }}"}]}, "include": "none", "options": {}}, "typeVersion": 3.2}, {"id": "dd2a3264-4171-43af-9409-ad2e79091bfb", "name": "Manual mapping notes db", "type": "n8n-nodes-base.set", "position": [5720, 660], "parameters": {"fields": {"values": [{"name": "NOTE_ID", "stringValue": "={{ $json.note_id }}"}, {"name": "NOTE_TITLE", "stringValue": "={{ $json.note_title }}"}, {"name": "NOTE_STATE", "stringValue": "={{ $json.note_state }}"}, {"name": "NOTE_COMPANY_ID", "stringValue": "={{ $json.note_company_id }}"}, {"name": "NOTE_CONTENT", "stringValue": "={{ $json.note_content }}"}, {"name": "NOTE_CREATED_BY", "stringValue": "={{ $json.note_created_by }}"}, {"name": "NOTE_CREATED_AT", "stringValue": "={{ $json.note_created_at }}"}, {"name": "NOTE_SOURCE", "stringValue": "={{ $json.note_source }}"}, {"name": "NOTE_OWNER", "stringValue": "={{ $json.note_owner }}"}, {"name": "NOTE_URL", "stringValue": "={{ $json.note_url }}"}]}, "include": "none", "options": {}}, "typeVersion": 3.2}, {"id": "d163879a-6020-4ace-b3ea-36c3d7b3675a", "name": "Empty Table Productboard Companies", "type": "n8n-nodes-base.snowflake", "position": [3820, 220], "parameters": {"query": "TRUNCATE TABLE PRODUCTBOARD_COMPANIES;", "operation": "execute<PERSON>uery"}, "credentials": {"snowflake": {"id": "81", "name": "Snowflake"}}, "executeOnce": true, "typeVersion": 1}, {"id": "0dbf1a3c-ae8b-4e7b-afb5-d1363d3d7634", "name": "[CREATE] PRODUCTBOARD_NOTES_FEATURES", "type": "n8n-nodes-base.snowflake", "position": [3760, 1140], "parameters": {"query": "CREATE OR <PERSON><PERSON>LACE TABLE PRODUCTBOARD_NOTES_FEATURES (\n    note_id STRING NOT NULL,\n    feature_id STRING\n)", "operation": "execute<PERSON>uery"}, "credentials": {"snowflake": {"id": "81", "name": "Snowflake"}}, "typeVersion": 1}, {"id": "fa9e8744-c348-481c-a6f9-083689ee8ea9", "name": "Manual mapping feature note IDs db", "type": "n8n-nodes-base.set", "position": [6160, 920], "parameters": {"fields": {"values": [{"name": "NOTE_ID", "stringValue": "={{ $json.note_id }}"}, {"name": "FEATURE_ID", "stringValue": "={{ $json.feature_id }}"}]}, "include": "none", "options": {}}, "typeVersion": 3.2}, {"id": "718f041a-dd02-4331-a704-fd1aa809212b", "name": "Update Productboard Note and Feature IDs", "type": "n8n-nodes-base.snowflake", "position": [6380, 920], "parameters": {"table": "PRODUCTBOARD_NOTES_FEATURES", "columns": "NOTE_ID,FEATURE_ID"}, "credentials": {"snowflake": {"id": "81", "name": "Snowflake"}}, "typeVersion": 1}, {"id": "51430e95-1eb9-4c47-a0cf-e05708e6d41b", "name": "Empty Table Productboard Note and Feature IDs", "type": "n8n-nodes-base.snowflake", "position": [4040, 640], "parameters": {"query": "TRUNCATE TABLE PRODUCTBOARD_NOTES_FEATURES;", "operation": "execute<PERSON>uery"}, "credentials": {"snowflake": {"id": "81", "name": "Snowflake"}}, "executeOnce": true, "typeVersion": 1}, {"id": "8c03178f-baf1-4ed8-94d8-91e90ef5cd26", "name": "Loop Over Items notes", "type": "n8n-nodes-base.splitInBatches", "position": [5460, 640], "parameters": {"options": {}, "batchSize": 100}, "typeVersion": 3}, {"id": "0c6a787f-48da-479c-b45a-8122b8fada3f", "name": "Loop Over Items features notes", "type": "n8n-nodes-base.splitInBatches", "position": [5900, 900], "parameters": {"options": {}, "batchSize": 100}, "typeVersion": 3}, {"id": "1adb9ff0-be18-4ceb-aae0-62186e75668f", "name": "[CREATE] PRODUCTBOARD_FEATURES", "type": "n8n-nodes-base.snowflake", "position": [3040, 1140], "parameters": {"query": "CREATE OR R<PERSON>LACE TABLE PRODUCTBOARD_FEATURES (\n    feature_id STRING NOT NULL,\n    feature_name STRING,\n    feature_status STRING,\n    feature_start_date STRING,\n    feature_end_date STRING,\n    feature_owner STRING,\n    feature_created_at STRING\n);", "operation": "execute<PERSON>uery"}, "credentials": {"snowflake": {"id": "81", "name": "Snowflake"}}, "typeVersion": 1}, {"id": "0357ba46-4934-4c3f-8f0a-676496a6eee6", "name": "Empty Table Productboard Features", "type": "n8n-nodes-base.snowflake", "position": [3440, -180], "parameters": {"query": "TRUNCATE TABLE PRODUCTBOARD_FEATURES;", "operation": "execute<PERSON>uery"}, "credentials": {"snowflake": {"id": "81", "name": "Snowflake"}}, "executeOnce": true, "typeVersion": 1}, {"id": "df076304-ce27-4801-8e0f-c268b313ef4e", "name": "Loop Over Items features", "type": "n8n-nodes-base.splitInBatches", "position": [4640, -180], "parameters": {"options": {}, "batchSize": 100}, "typeVersion": 3}, {"id": "40732e40-5ff2-4b1f-b300-b6b734e31637", "name": "Manual mapping features db", "type": "n8n-nodes-base.set", "position": [4900, -160], "parameters": {"fields": {"values": [{"name": "FEATURE_ID", "stringValue": "={{ $json.feature_id }}"}, {"name": "FEATURE_NAME", "stringValue": "={{ $json.feature_name }}"}, {"name": "FEATURE_STATUS", "stringValue": "={{ $json.feature_status }}"}, {"name": "FEATURE_START_DATE", "stringValue": "={{ $json.feature_start_date }}"}, {"name": "FEATURE_END_DATE", "stringValue": "={{ $json.feature_end_date }}"}, {"name": "FEATURE_OWNER", "stringValue": "={{ $json.feature_owner }}"}, {"name": "FEATURE_CREATED_AT", "stringValue": "={{ $json.feature_created_at }}"}]}, "include": "none", "options": {}}, "typeVersion": 3.2}, {"id": "59a838c4-fef0-4902-b6d6-418934ac986f", "name": "Update Productboard Features", "type": "n8n-nodes-base.snowflake", "position": [5140, -160], "parameters": {"table": "PRODUCTBOARD_FEATURES", "columns": "FEATURE_ID,FEATURE_NAME,FEATURE_STATUS,FEATURE_START_DATE,FEATURE_END_DATE,FEATURE_OWNER,FEATURE_CREATED_AT"}, "credentials": {"snowflake": {"id": "81", "name": "Snowflake"}}, "typeVersion": 1}, {"id": "110ebd3a-50ac-4e9f-9297-f64759dfdd18", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [2980, -180], "parameters": {"rule": {"interval": [{"field": "weeks", "triggerAtDay": [1], "triggerAtHour": 8}]}}, "typeVersion": 1.1}, {"id": "3eb88f88-8fad-4aaf-b6f9-6f7d87e30018", "name": "<PERSON><PERSON>ck", "type": "n8n-nodes-base.slack", "onError": "continueRegularOutput", "position": [5900, 1220], "parameters": {"text": "=", "select": "channel", "blocksUi": "={\n  \"blocks\": [\n    {\n      \"type\": \"section\",\n      \"text\": {\n        \"type\": \"mrkdwn\",\n        \"text\": \":productboard: Weekly Update in :snowflake_logo: Completed\\n\\n*{{ $json.NOTES_7_DAYS }}* new insights added in the last 7 days.\\n\\n*{{ $json.NOTES_UNPROCESSED }}* insights remain unprocessed.\\n\\nYou can view the updated :metabase: dashboard below:\"\n      }\n    },\n    {\n      \"type\": \"actions\",\n      \"elements\": [\n        {\n          \"type\": \"button\",\n          \"text\": {\n            \"type\": \"plain_text\",\n            \"text\": \"Open Dashboard\",\n            \"emoji\": true\n          },\n          \"url\": \"https://metabase.com\"\n        }\n      ]\n    }\n  ]\n}", "channelId": {"__rl": true, "mode": "name", "value": "#product-notifications"}, "messageType": "block", "otherOptions": {}}, "credentials": {"slackApi": {"id": "SG3oDwwLGpxwoJSO", "name": "<PERSON><PERSON><PERSON> bot"}}, "executeOnce": true, "retryOnFail": false, "typeVersion": 2.1}, {"id": "3a16d947-a218-4ec2-8081-19b676bb51c3", "name": "Count Notes Last 7 days and Unprocessed", "type": "n8n-nodes-base.snowflake", "position": [5660, 1220], "parameters": {"query": "SELECT\n    COUNT(DISTINCT CASE \n        WHEN DATEDIFF(DAY, NOTE_CREATED_AT, CURRENT_DATE()) <= 7 THEN note_id \n    END) AS notes_7_days,\n    COUNT(DISTINCT CASE \n        WHEN NOTE_STATE = 'unprocessed' THEN note_id \n    END) AS notes_unprocessed\nFROM PRODUCTBOARD_NOTES;\n", "operation": "execute<PERSON>uery"}, "credentials": {"snowflake": {"id": "81", "name": "Snowflake"}}, "executeOnce": true, "typeVersion": 1}, {"id": "2bdfb96c-1c38-444d-9507-ab74f3572129", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [2980, 1060], "parameters": {"color": 5, "width": 983.4896175671602, "height": 314.88047081122676, "content": "## Setup snowflake tables"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "", "connections": {"Split notes": {"main": [[{"node": "Manual mapping notes", "type": "main", "index": 0}, {"node": "Split features in notes", "type": "main", "index": 0}, {"node": "Count Notes Last 7 days and Unprocessed", "type": "main", "index": 0}]]}, "Split features": {"main": [[{"node": "Manual mapping feature", "type": "main", "index": 0}]]}, "Split companies": {"main": [[{"node": "Manual mapping companies", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Empty Table Productboard Features", "type": "main", "index": 0}]]}, "Manual mapping notes": {"main": [[{"node": "Loop Over Items notes", "type": "main", "index": 0}]]}, "Loop Over Items notes": {"main": [[], [{"node": "Manual mapping notes db", "type": "main", "index": 0}]]}, "Manual mapping feature": {"main": [[{"node": "Loop Over Items features", "type": "main", "index": 0}]]}, "get productboard notes": {"main": [[{"node": "Split notes", "type": "main", "index": 0}]]}, "Manual mapping notes db": {"main": [[{"node": "Update Productboard Notes", "type": "main", "index": 0}]]}, "Split features in notes": {"main": [[{"node": "Combine Feature ID + Note ID", "type": "main", "index": 0}]]}, "Loop Over Items features": {"main": [[{"node": "Empty Table Productboard Companies", "type": "main", "index": 0}], [{"node": "Manual mapping features db", "type": "main", "index": 0}]]}, "Manual mapping companies": {"main": [[{"node": "Manual mapping companies db", "type": "main", "index": 0}]]}, "Update Productboard Notes": {"main": [[{"node": "Loop Over Items notes", "type": "main", "index": 0}]]}, "get productboard features": {"main": [[{"node": "Split features", "type": "main", "index": 0}]]}, "Manual mapping features db": {"main": [[{"node": "Update Productboard Features", "type": "main", "index": 0}]]}, "get productboard companies": {"main": [[{"node": "Split companies", "type": "main", "index": 0}]]}, "Manual mapping companies db": {"main": [[{"node": "Update Productboard Companies", "type": "main", "index": 0}]]}, "Combine Feature ID + Note ID": {"main": [[{"node": "Loop Over Items features notes", "type": "main", "index": 0}]]}, "Update Productboard Features": {"main": [[{"node": "Loop Over Items features", "type": "main", "index": 0}]]}, "Update Productboard Companies": {"main": [[{"node": "Empty Table Productboard Note and Feature IDs", "type": "main", "index": 0}]]}, "Empty Table Productboard Notes": {"main": [[{"node": "get productboard notes", "type": "main", "index": 0}]]}, "Loop Over Items features notes": {"main": [[], [{"node": "Manual mapping feature note IDs db", "type": "main", "index": 0}]]}, "Empty Table Productboard Features": {"main": [[{"node": "get productboard features", "type": "main", "index": 0}]]}, "Empty Table Productboard Companies": {"main": [[{"node": "get productboard companies", "type": "main", "index": 0}]]}, "Manual mapping feature note IDs db": {"main": [[{"node": "Update Productboard Note and Feature IDs", "type": "main", "index": 0}]]}, "Count Notes Last 7 days and Unprocessed": {"main": [[{"node": "<PERSON><PERSON>ck", "type": "main", "index": 0}]]}, "Update Productboard Note and Feature IDs": {"main": [[{"node": "Loop Over Items features notes", "type": "main", "index": 0}]]}, "Empty Table Productboard Note and Feature IDs": {"main": [[{"node": "Empty Table Productboard Notes", "type": "main", "index": 0}]]}}}