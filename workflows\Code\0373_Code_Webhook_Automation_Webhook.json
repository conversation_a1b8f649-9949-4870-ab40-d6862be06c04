{"meta": {"instanceId": "dbd43d88d26a9e30d8aadc002c9e77f1400c683dd34efe3778d43d27250dde50"}, "nodes": [{"id": "174f80b5-6c84-47b3-a906-eeb4fc5207b8", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [-840, 620], "webhookId": "5dc2467c-0b39-43e9-bdbd-399231f69c4e", "parameters": {"path": "5dc2467c-0b39-43e9-bdbd-399231f69c4e", "options": {}, "httpMethod": "POST", "responseCode": null}, "typeVersion": 1}, {"id": "e03fc5ca-9446-44b7-9c0a-44c8696ec06a", "name": "Code", "type": "n8n-nodes-base.code", "position": [-540, 620], "parameters": {"jsCode": "\nconst available = items[0].json.body.available;\nconst inventory_item = items[0].json.body.inventory_item_id;\nconst lowInventory = available > 0 && available < 4;\nconst outOfStock = available === 0;\n\nreturn [\n  {\n    json: {\n      available: available,\n      inventory_tem: inventory_item,\n      low_inventory: lowInventory,\n      out_of_stock: outOfStock,\n    },\n  },\n];"}, "typeVersion": 1}, {"id": "2e8b6898-87aa-4e27-80df-647f022e7810", "name": "Low Inventory", "type": "n8n-nodes-base.if", "position": [-180, 500], "parameters": {"conditions": {"boolean": [{"value1": "={{ $json.low_inventory }}", "value2": "={{ true }}"}]}}, "typeVersion": 1}, {"id": "02c33a4d-e806-4447-a754-5d2027ebfc2b", "name": "Out of stock", "type": "n8n-nodes-base.if", "position": [-180, 780], "parameters": {"conditions": {"boolean": [{"value1": "={{ $json.out_of_stock }}", "value2": "={{ true }}"}]}}, "typeVersion": 1}, {"id": "ce6a4937-ce78-486e-adcb-a0d11a856cd9", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [560, 400], "parameters": {"body": "={\n  \"embeds\": [\n    {\n      \"title\":  \"{{ $json.data.inventoryItem.variant.product.title }}\",\n      \"description\": \"This product is running out of stock!\",\n      \"color\": 16776960,\n      \"fields\": [\n        {\n          \"name\": \"Remaining Inventory\",\n          \"value\": \"{{ $json.data.inventoryItem.variant.inventoryQuantity }}\",\n          \"inline\": false\n        },\n        {\n          \"name\": \"Product Variant\",\n          \"value\": \"{{ $json.data.inventoryItem.variant.title }}\",\n          \"inline\": true\n        }\n      ],\n      \"image\": {\n        \"url\": \"{{ $json.data.inventoryItem.variant.product.images.edges[0].node.originalSrc }}\"\n      },\n      \"footer\": {\n        \"text\": \"Alert from inventory management system\"\n      }\n    }\n  ]\n}", "method": "POST", "options": {}, "sendBody": true, "contentType": "raw", "authentication": "predefinedCredentialType", "rawContentType": "application/json", "nodeCredentialType": "discordBotApi"}, "credentials": {"discordBotApi": {"id": "opA36m6ZPvLM8V3I", "name": "Discord Bot account"}}, "typeVersion": 4.1}, {"id": "4a571564-03a1-44de-a06d-b5142911d6f4", "name": "HTTP Request1", "type": "n8n-nodes-base.httpRequest", "position": [560, 860], "parameters": {"body": "={\n  \"embeds\": [\n    {\n      \"title\":  \"{{ $json.data.inventoryItem.variant.product.title }}\",\n      \"description\": \"This product is sold out!\",\n      \"color\": ********,\n      \"fields\": [\n        {\n          \"name\": \"Remaining Inventory\",\n          \"value\": \"{{ $json.data.inventoryItem.variant.inventoryQuantity }}\",\n          \"inline\": false\n        },\n        {\n          \"name\": \"Product Variant\",\n          \"value\": \"{{ $json.data.inventoryItem.variant.title }}\",\n          \"inline\": true\n        }\n      ],\n      \"image\": {\n        \"url\": \"{{ $json.data.inventoryItem.variant.product.images.edges[0].node.originalSrc }}\"\n      },\n      \"footer\": {\n        \"text\": \"Alert from inventory management system\"\n      }\n    }\n  ]\n}", "method": "POST", "options": {}, "sendBody": true, "contentType": "raw", "authentication": "predefinedCredentialType", "rawContentType": "application/json", "nodeCredentialType": "discordBotApi"}, "credentials": {"discordBotApi": {"id": "opA36m6ZPvLM8V3I", "name": "Discord Bot account"}}, "typeVersion": 4.1}, {"id": "703b259c-e655-41e2-abb0-9ad80d2224a5", "name": "GraphQL1- shopify", "type": "n8n-nodes-base.graphql", "position": [180, 400], "parameters": {"query": "={\n  inventoryItem(id: \"gid://shopify/InventoryItem/{{ $json.inventory_tem }}\") {\n    id\n    variant {\n      id\n      title\n      inventoryQuantity  # This line adds the inventory quantity field\n      product {\n        id\n        title\n        images(first: 1) {\n          edges {\n            node {\n              originalSrc\n            }\n          }\n        }\n      }\n    }\n  }\n}", "endpoint": "https://store.myshopify.com/admin/api/2023-10/graphql.json", "authentication": "headerAuth"}, "typeVersion": 1}, {"id": "eb4c0d15-85b8-42cf-9c0d-d53e3e787cf9", "name": "GraphQL -  shopify", "type": "n8n-nodes-base.graphql", "position": [200, 860], "parameters": {"query": "={\n  inventoryItem(id: \"gid://shopify/InventoryItem/{{ $json.inventory_tem }}\") {\n    id\n    variant {\n      id\n      title\n      inventoryQuantity  # This line adds the inventory quantity field\n      product {\n        id\n        title\n        images(first: 1) {\n          edges {\n            node {\n              originalSrc\n            }\n          }\n        }\n      }\n    }\n  }\n}", "endpoint": "https://store.myshopify.com/admin/api/2023-10/graphql.json", "authentication": "headerAuth"}, "typeVersion": 1}, {"id": "b06a4e50-f640-48a3-92e1-f41584a2e89b", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1160, 600], "parameters": {"color": 7, "width": 253.05487804878055, "height": 376, "content": "### Webhook Node (Shopify Listener)\nSetup Requirement: First, add the \"Inventory Level Update\" event in Shopify\n\nPurpose: Listens for inventory updates from Shopify\n\nSetup: Configured in Shopify settings; linked to n8n URL\n\nAction: Triggers workflow on inventory level changes\n\nNote: Ensure correct URL setup in Shopify for accurate triggers"}, "typeVersion": 1}, {"id": "a4e7c588-56f2-4d4f-8531-8969f0667b79", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-600, 780], "parameters": {"color": 7, "width": 246.67682926829286, "height": 318, "content": "### Function Node (Inventory Check)\n\nPurpose: Processes inventory data from Shopify.\nAction: Extracts available inventory and item ID\n\nLogic: Determines if inventory is low (<4 items) or out of stock (0 items)\n\nNote: Adjust thresholds as needed for different stock levels"}, "typeVersion": 1}, {"id": "3e25dfbf-38b3-4206-891f-194f175db418", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-240, 400], "parameters": {"color": 7, "width": 185, "height": 80, "content": "Checks if low_inventory is true (almost out of stock)"}, "typeVersion": 1}, {"id": "2527ba84-ba49-4a08-a9d4-cb8af9b9723d", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-220, 920], "parameters": {"color": 7, "width": 180, "height": 80, "content": "Checks if out_of_stock is true (no stock left)"}, "typeVersion": 1}, {"id": "a879f649-abd0-4b72-86de-deac6b6b4dc6", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [120, 560], "parameters": {"color": 7, "width": 272, "height": 258.34634146341466, "content": "### Shopify graphql\n\nRetrieves product variant, title, inventory quantity, and image.\nUses Shopify's GraphQL API for detailed data retrieval.\n\nEndpoint to be customized: Replace store.myshopify.com in https://store.myshopify.com/admin/api/2023-10/graphql.json with your actual Shopify store's myshopify URL."}, "typeVersion": 1}, {"id": "5b7fa7ff-61e3-44c3-9bd3-2ac1c058df8c", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [520, 580], "parameters": {"color": 7, "width": 214, "height": 145, "content": "Discord1: Configured to send messages to Channel A\n\nDiscord2: Configured to send messages to Channel B."}, "typeVersion": 1}, {"id": "809838f1-70ee-46ab-9cf4-2a8cb4fe35a2", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-1160, 260], "parameters": {"width": 361.2353658536575, "height": 305.7548780487801, "content": "## Low Stock & Sold Out Watcher for Shopify\nThis n8n workflow automates the process of monitoring inventory levels for Shopify products, ensuring timely updates and efficient stock management. \n\nIt is designed to alert users when inventory levels are low or out of stock, integrating with Shopify's webhook system and providing notifications through Discord (can be changed to any messaging platform) with product images and details.\n"}, "typeVersion": 1}], "connections": {"Code": {"main": [[{"node": "Low Inventory", "type": "main", "index": 0}, {"node": "Out of stock", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Out of stock": {"main": [[{"node": "GraphQL -  shopify", "type": "main", "index": 0}]]}, "Low Inventory": {"main": [[{"node": "GraphQL1- shopify", "type": "main", "index": 0}]]}, "GraphQL1- shopify": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "GraphQL -  shopify": {"main": [[{"node": "HTTP Request1", "type": "main", "index": 0}]]}}}