{"id": "n8cwEZfJLGn15Lqx", "meta": {"instanceId": "d40a25503b797861fe81ffcf2649da2a83b8677ac1ef2ee6b6872aa9b52454b8", "templateCredsSetupCompleted": true}, "name": "ERP AI chatbot for Odoo sales module", "tags": [], "nodes": [{"id": "abe58519-f3fe-4438-b6d6-d67071c70f0b", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [1360, 700], "parameters": {"sessionKey": "={{ $('Chat Trigger').item.json.sessionId }}", "sessionIdType": "customKey"}, "typeVersion": 1.2}, {"id": "35b63108-e40d-494f-a0dc-5c8ea296c75f", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1240, 700], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "8F3dAS1qjFM6mYbD", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "91ff893c-917d-46c2-b27d-48e9799452a6", "name": "Calculator", "type": "@n8n/n8n-nodes-langchain.toolCalculator", "position": [1480, 700], "parameters": {}, "typeVersion": 1}, {"id": "b9c10744-c5b8-4949-a80f-d331746632fb", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [220, 180], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.2}, {"id": "4fa016bf-3f4c-4bfd-8c11-0270002de533", "name": "Convert to File", "type": "n8n-nodes-base.convertToFile", "position": [1480, 180], "parameters": {"options": {}, "operation": "toText", "sourceProperty": "response.text"}, "typeVersion": 1.1}, {"id": "f9f0f1ed-7ccf-4c97-8d28-91399b2a4440", "name": "Save Summary to File", "type": "n8n-nodes-base.readWriteFile", "position": [1700, 180], "parameters": {"options": {"append": false}, "fileName": "cache.txt", "operation": "write"}, "typeVersion": 1}, {"id": "e9715c9d-7ebb-4f8c-b44e-a2ee69bf9618", "name": "Get All Opportunities from Odoo", "type": "n8n-nodes-base.odoo", "position": [460, 180], "parameters": {"options": {"fieldsList": ["won_status", "description", "email_from", "contact_name", "expected_revenue"]}, "resource": "opportunity", "operation": "getAll", "returnAll": true}, "credentials": {"odooApi": {"id": "5XAxrqqPxY5dzcoP", "name": "Odoo account"}}, "typeVersion": 1}, {"id": "23b82fdb-656f-497f-84eb-4296581245ac", "name": "Read Summary From File", "type": "n8n-nodes-base.readWriteFile", "position": [540, 540], "parameters": {"options": {}, "fileSelector": "cache.txt"}, "typeVersion": 1, "alwaysOutputData": false}, {"id": "8d702c53-9001-4f71-80c4-17786384caf0", "name": "If Summary Exists", "type": "n8n-nodes-base.if", "position": [760, 540], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "c65a538f-f6c8-41ff-bad3-a631a5063cbb", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.fileName }}", "rightValue": ""}]}}, "typeVersion": 2}, {"id": "4007d6e3-2d6f-4edd-afee-7df3c7dd5236", "name": "Merge Opportunities", "type": "n8n-nodes-base.aggregate", "position": [700, 180], "parameters": {"options": {}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}, {"id": "604de791-8351-47ed-897d-2b7fe7f0aa99", "name": "Extract Text From File", "type": "n8n-nodes-base.extractFromFile", "position": [1020, 520], "parameters": {"options": {}, "operation": "text"}, "typeVersion": 1}, {"id": "ab68228c-7d02-4d36-8c43-10a387dc3085", "name": "AI Conversational Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1240, 520], "parameters": {"text": "={{ $('Chat Trigger').item.json.chatInput }}", "agent": "conversationalAgent", "options": {"humanMessage": "=TOOLS\n------\nAssistant can ask the user to use tools to look up information that may be helpful in answering the users original question. The tools the human can use are:\n\n{tools}\n\n{format_instructions}\n\nAnswer questions using only the following context: {{ $json.data }}.\n\n\nUSER'S INPUT\n--------------------\nHere is the user's input (remember to respond with a markdown code snippet of a json blob with a single action, and NOTHING else):\n\n{{input}} "}, "promptType": "define"}, "typeVersion": 1.6}, {"id": "117af37c-7dda-4bba-a008-d67c876efa9d", "name": "Summarize Opportunities", "type": "@n8n/n8n-nodes-langchain.chainSummarization", "position": [1020, 60], "parameters": {"options": {"summarizationMethodAndPrompts": {"values": {"prompt": "=Write a summary of the following:\n\n\n{{ JSON.stringify($json.data) }}\n\nInclude important information such as won status and expected revenue for each opportunity. Also include a short description of each oppotunity and keep opportunities separate.\n\nCONCISE SUMMARY: ", "combineMapPrompt": "=Write a summary of the following:\n\n{{ JSON.stringify($json.data) }}\n\nInclude important information such as won status and expected revenue for each opportunity. Also include a short description of each oppotunity and keep opportunities separate.\n\nCONCISE SUMMARY: "}}}}, "typeVersion": 2}, {"id": "e6ae17c7-15db-4c9d-9ce9-4748eaf84359", "name": "OpenAI Summarization Model", "type": "@n8n/n8n-nodes-langchain.lmOpenAi", "position": [1000, 220], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4-turbo", "cachedResultName": "gpt-4-turbo"}, "options": {}}, "credentials": {"openAiApi": {"id": "8F3dAS1qjFM6mYbD", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "0e3a0b55-62d0-43d0-a744-a7b7ab05c087", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [200, -160], "parameters": {"width": 446.**************, "height": 261.*************, "content": "# ERP chatbot for Odoo sales module\n\nSet up steps:\n* Configure the Odoo credentials\n* Configure OpenAI credentials\n* Toggle \"Make Chat Publicly Available\" from the Chat Trigger node."}, "typeVersion": 1}, {"id": "b9169b8d-7ff6-403f-b354-511c23d5da1c", "name": "<PERSON><PERSON>", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [220, 540], "webhookId": "09eea368-b78f-4209-9750-f28b706363c2", "parameters": {"public": true, "options": {}}, "typeVersion": 1}], "active": false, "pinData": {"Get All Opportunities from Odoo": [{"json": {"id": 6, "email_from": "<EMAIL>", "won_status": "won", "description": "<p data-last-history-steps=\"****************,***************\">\n<PERSON>, Procurement Manager at Innovative Solutions Inc., initially expressed strong interest in CloudConnect Pro for upcoming projects. They were impressed with its capabilities in cloud integration, data management, and flexibility. After successful discussions and negotiations, NovaTech Enterprises signed a contract for $19,000 to implement CloudConnect Pro for their enterprise-level needs. Project onboarding and deployment were completed successfully.\n\n<br></p>", "contact_name": false, "expected_revenue": 19000}}, {"json": {"id": 5, "email_from": "<EMAIL>", "won_status": "pending", "description": "<p><PERSON><PERSON>, Procurement Manager at Innovative Solutions Inc, is interested in incorporating CloudConnect Pro platform into their upcoming projects. They are impressed by its capabilities in cloud integration, data management, and flexibility. They request detailed information on pricing, implementation options, support services, and case studies for enterprise-level deployments. They are eager to learn more and hope for a mutually beneficial partnership. </p>", "contact_name": false, "expected_revenue": 17000}}]}, "settings": {"executionOrder": "v1"}, "versionId": "315972c0-e19d-4978-88ea-9fe721de3631", "connections": {"Calculator": {"ai_tool": [[{"node": "AI Conversational Agent", "type": "ai_tool", "index": 0}]]}, "Chat Trigger": {"main": [[{"node": "Read Summary From File", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[{"node": "Save Summary to File", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Get All Opportunities from Odoo", "type": "main", "index": 0}]]}, "If Summary Exists": {"main": [[{"node": "Extract Text From File", "type": "main", "index": 0}], [{"node": "Get All Opportunities from Odoo", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Conversational Agent", "type": "ai_languageModel", "index": 0}]]}, "Merge Opportunities": {"main": [[{"node": "Summarize Opportunities", "type": "main", "index": 0}]]}, "Save Summary to File": {"main": [[{"node": "Read Summary From File", "type": "main", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Conversational Agent", "type": "ai_memory", "index": 0}]]}, "Extract Text From File": {"main": [[{"node": "AI Conversational Agent", "type": "main", "index": 0}]]}, "Read Summary From File": {"main": [[{"node": "If Summary Exists", "type": "main", "index": 0}]]}, "Summarize Opportunities": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "OpenAI Summarization Model": {"ai_languageModel": [[{"node": "Summarize Opportunities", "type": "ai_languageModel", "index": 0}]]}, "Get All Opportunities from Odoo": {"main": [[{"node": "Merge Opportunities", "type": "main", "index": 0}]]}}}