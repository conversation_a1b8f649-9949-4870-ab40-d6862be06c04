{"nodes": [{"id": "4bf26356-9c59-4cee-8eb8-8553b23a172f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [560, -120], "parameters": {"width": 660, "height": 460, "content": "![](https://raw.githubusercontent.com/2innnnn0/30-Days-of-ChatGPT/refs/heads/main/datapopcorn_logo_50px.png)\n# Daily Cartoon (w/ AI Translate)\n\n### How it works\n- Automates the retrieval of <PERSON> and <PERSON><PERSON><PERSON> daily comics.\n- Extracts the comic image URL from the website.\n- Translates comic dialogues to English and Korean(Other Language)\n- Posts the comic and translations to Discord daily.\n\n### Set up steps\n- Estimated setup time: ~10-15 minutes.\n- Use a **Schedule Trigger** to automate the workflow at 9 AM daily.\n- Add nodes for parameter setup, HTTP request, data extraction, and integration with Discord.\n- Add detailed notes to each node in the workflow for easy understanding."}, "typeVersion": 1}, {"id": "52d19472-41b4-4d71-874e-064ef9d6f248", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [620, 380], "parameters": {"rule": {"interval": [{"triggerAtHour": 9}]}}, "typeVersion": 1.2}, {"id": "bcc15f37-c048-4d9a-83cd-367856470095", "name": "OpenAI", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1620, 380], "parameters": {"text": "Please write the original language and Korean together. \n\nEXAMPLE)\nCalvin: \"YOU'VE NEVER HAD AN OBLIGATION, AN ASSIGNMENT, OR A DEADLINE IN ALL YOUR LIFE! YOU HAVE NO RESPONSIBILITIES AT ALL! IT MUST BE NICE!\" (너는 평생 한 번도 의무, 과제, 혹은 마감일 없었잖아! 전혀 책임이 없다니! 정말 좋겠다!)\nHobbes: \"WIPE THAT INSOLENT SMIRK OFF YOUR FACE!\" (그 뻔뻔한 미소를 그만 지어!)\n", "modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "resource": "image", "imageUrls": "={{ $json.output.cartoon_image }}", "operation": "analyze"}, "credentials": {"openAiApi": {"id": "kYIZ8ZwQHS2d4GiD", "name": "(datapopcorn )OpenAi account"}}, "typeVersion": 1.6}, {"id": "35004d43-4061-476a-9af6-7d0b82ae86bd", "name": "param", "type": "n8n-nodes-base.set", "position": [840, 380], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "59d36aef-2991-4fd2-9fbe-dad9a701b40f", "name": "year", "type": "string", "value": "={{ $now.format('yyyy') }}"}, {"id": "b6b329f2-ba08-4516-bdb9-c5d124c02110", "name": "month", "type": "string", "value": "={{ $now.format('MM') }}"}, {"id": "3cba75d1-a281-4e14-9bf7-e0bc0cc7c768", "name": "day", "type": "string", "value": "={{ $now.format('dd') }}"}]}}, "typeVersion": 3.4}, {"id": "cf2c953f-1ff2-4abc-8abd-95e05603e64a", "name": "Discord", "type": "n8n-nodes-base.discord", "position": [1840, 380], "parameters": {"content": "=Daily Cartoon ({{ $('param').item.json.year }}/{{ $('param').item.json.month }}/{{ $('param').item.json.day }})\n{{ $('Information Extractor').item.json.output.cartoon_image }}\n\n{{ $json.content }}\n", "options": {}, "authentication": "webhook"}, "credentials": {"discordWebhookApi": {"id": "w82RWS7nmXLKDczt", "name": "n8n test webhook"}}, "typeVersion": 2}, {"id": "5eec9870-a509-4090-a540-76b22bb3eac9", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1260, 560], "parameters": {"model": "gpt-4o-mini-2024-07-18", "options": {}}, "credentials": {"openAiApi": {"id": "kYIZ8ZwQHS2d4GiD", "name": "(datapopcorn )OpenAi account"}}, "typeVersion": 1}, {"id": "352db81e-7571-47cb-b028-dec18e15ccce", "name": "Information Extractor", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [1260, 380], "parameters": {"text": "=Please just extract the src value in the <img class=\"img-fluid Lazyloaded\"> tag from HTML below. I don't need anything other than the value.\n\ne.g.)\nEXAMPLE INPUT)\n<img class=\"img-fluid lazyloaded\" srcset=\"https://assets.amuniversal.com/5ed526b06e94013bda88005056a9545d 900w\" data-srcset=\"https://assets.amuniversal.com/5ed526b06e94013bda88005056a9545d 900w\" sizes=\"\n (min-width: 992px) 900px,\n (min-width: 768px) 600px,\n (min-width: 576px) 300px,\n 900px\" width=\"100%\" alt=\"<PERSON> and Ho<PERSON>s Comic Strip for March 03, 2023 \" src=\"https://assets.amuniversal.com/5ed526b06e94013bda88005056a9545d\">\n\n\nEXAMPLE OUTPUT)\nhttps://assets.amuniversal.com/5ed526b06e94013bda88005056a9545d\n\n--\n(INPUT)\n{{ $json.data }}", "options": {}, "attributes": {"attributes": [{"name": "cartoon_image", "description": "EXAMPLE OUTPUT) https://assets.amuniversal.com/***"}]}}, "typeVersion": 1}, {"id": "517799ed-559c-4d17-b8aa-58bd4ee92ed3", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [1040, 380], "parameters": {"url": "=https://www.gocomics.com/calvinandhobbes/{{ $json.year }}/{{ $json.month }}/{{ $json.day }}", "options": {}}, "typeVersion": 4.2}], "pinData": {}, "connections": {"param": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "Discord", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Information Extractor", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "param", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Information Extractor", "type": "ai_languageModel", "index": 0}]]}, "Information Extractor": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}}}