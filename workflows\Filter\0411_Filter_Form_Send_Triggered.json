{"meta": {"instanceId": "257476b1ef58bf3cb6a46e65fac7ee34a53a5e1a8492d5c6e4da5f87c9b82833"}, "nodes": [{"id": "fec9c13e-a734-4d36-9d2b-b039da167d54", "name": "n8n Form Trigger", "type": "n8n-nodes-base.formTrigger", "position": [1060, 360], "webhookId": "1ad4ce6c-f29a-4371-a5b9-a17ce7939280", "parameters": {"path": "1ad4ce6c-f29a-4371-a5b9-a17ce7939280", "options": {}, "formTitle": "Contact us", "formFields": {"values": [{"fieldType": "dropdown", "fieldLabel": "What's your role?", "fieldOptions": {"values": [{"option": "Product"}, {"option": "Sales"}, {"option": "Marketing"}, {"option": "Other"}]}, "requiredField": true}, {"fieldLabel": "What's your business email?", "requiredField": true}]}, "formDescription": "Thanks for showing interest in our product. We'll come back to you soon!"}, "typeVersion": 2}, {"id": "0bc7cbfd-efb6-43b4-a1e2-64ee28087afa", "name": "Clearbit", "type": "n8n-nodes-base.clearbit", "position": [1660, 360], "parameters": {"email": "={{ $json['What\\'s your business email?'] }}", "resource": "person", "additionalFields": {}}, "credentials": {"clearbitApi": {"id": "cKDImrinp9tg0ZHW", "name": "Clearbit account"}}, "typeVersion": 1}, {"id": "7b9263c0-cd18-4c47-aa9b-9263be33aaec", "name": "Enrich Company", "type": "n8n-nodes-base.clearbit", "position": [1880, 360], "parameters": {"domain": "={{ $json.employment.domain }}", "additionalFields": {}}, "credentials": {"clearbitApi": {"id": "cKDImrinp9tg0ZHW", "name": "Clearbit account"}}, "typeVersion": 1}, {"id": "57cef084-97d3-4beb-8ff0-0d72396f2ae5", "name": "If B2B and > 499 employees", "type": "n8n-nodes-base.if", "position": [2100, 360], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a2c9c524-e3dc-411b-ad11-4dcd0f288016", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.tags.includes(\"B2B\") }}", "rightValue": ""}, {"id": "facfad29-ba3e-4111-90e1-8edf67746803", "operator": {"type": "number", "operation": "gt"}, "leftValue": "={{ $json.metrics.employees }}", "rightValue": 499}]}}, "typeVersion": 2}, {"id": "9de60599-0401-441e-a5c5-bed097ac23f2", "name": "Send Email", "type": "n8n-nodes-base.gmail", "position": [2340, 340], "parameters": {"sendTo": "={{ $('Map email field').item.json.email }}", "message": "=Hi {{ $('Clearbit').item.json.name.givenName }},\n\nthanks so much for contacting us. We'll get back to your soon.\nBest\nYour Name", "options": {}, "subject": "Thanks for contacting us", "emailType": "text"}, "credentials": {"gmailOAuth2": {"id": "3", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "9830deff-0611-4dae-bd4a-ff893caec257", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [737.*************, 300], "parameters": {"width": 272.*************, "height": 228.**************, "content": "### Setup\n1. Add the `Clearbit` and `Gmail` credentials\n2. Click on `Test Workflow`\n3. Enter your own email (which needs to be a business email to work) in the Form\n4. Check your email\n5. Once you're happy don't forget to activate this workflow"}, "typeVersion": 1}, {"id": "19780d88-b510-4390-a1af-5ee9f7ef042f", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1840, 300], "parameters": {"color": 5, "width": 190, "height": 232, "content": "Change the conditions in this node to your needs"}, "typeVersion": 1}, {"id": "5343deb5-f60a-458a-bcda-b24c74812307", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1020, 300], "parameters": {"color": 5, "width": 190, "height": 229.23497494011445, "content": "Replace this node with your form of choice"}, "typeVersion": 1}, {"id": "6c5e8306-a54f-49c3-b364-80e579162826", "name": "Map email field", "type": "n8n-nodes-base.set", "position": [1280, 360], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "32d5ada7-5cc1-42ad-aad2-7f7f0fb93ace", "name": "email", "type": "string", "value": "={{ $json['What\\'s your business email?'] }}"}]}}, "typeVersion": 3.3}, {"id": "87e26cfb-1f20-4c2d-b298-bab7b75ef415", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1240, 282.46994988022897], "parameters": {"color": 7, "width": 190, "height": 247.95993317363863, "content": "Make sure to map the email field of your form here when changing it"}, "typeVersion": 1}, {"id": "047e8c03-e2fe-4d4c-94af-99ddc28ac7ea", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [2300, 280], "parameters": {"color": 5, "width": 190, "height": 218, "content": "Adjust your message here"}, "typeVersion": 1}, {"id": "2da0e0a3-eb90-4514-a7dd-082a43c9871d", "name": "Submission does not match criteria, don't do anything", "type": "n8n-nodes-base.noOp", "position": [2340, 580], "parameters": {}, "typeVersion": 1}, {"id": "e9ef33e5-5a08-4fe3-9363-c0e537645147", "name": "Filter out personal emails", "type": "n8n-nodes-base.filter", "position": [1460, 360], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "df6da257-7ec4-4433-9d29-2f12f6f11944", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.email }}", "rightValue": "@gmail.com"}, {"id": "6a66410c-a2e8-494b-b972-751116e49418", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.email }}", "rightValue": "@yahoo.com"}, {"id": "378fbe41-0e37-4756-93ca-bf81bfe8b258", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.email }}", "rightValue": "@outlook.com"}, {"id": "fd05b842-3c11-4e1a-9226-0b0fd359ccab", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.email }}", "rightValue": "@hotmail.com"}, {"id": "6040ea5d-3c15-4513-915b-47a55c24e8a7", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.email }}", "rightValue": "@icloud.com"}, {"id": "ce67ed8b-34f9-4ba2-83d4-cc04cea090bb", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.email }}", "rightValue": "@mail.com"}, {"id": "92c043ae-72de-41d8-887b-9e94755a9060", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.email }}", "rightValue": "@aol.com"}, {"id": "377bcc07-e5a1-4e3a-a4da-4446f316a0b2", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.email }}", "rightValue": "@zoho.com"}, {"id": "c09c7057-2833-4085-8cb9-d2f28d853724", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.email }}", "rightValue": "@gmx"}]}}, "typeVersion": 2}], "pinData": {"Clearbit": [{"id": "f679f5ef-f7a0-4cb1-8790-fe663a0c092f", "bio": null, "geo": {"lat": 53.5510846, "lng": 9.9936819, "city": "Hamburg", "state": "Hamburg", "country": "Germany", "stateCode": "HH", "countryCode": "DE"}, "name": {"fullName": "<PERSON><PERSON>", "givenName": "<PERSON><PERSON>", "familyName": "<PERSON><PERSON>"}, "site": null, "email": "<EMAIL>", "fuzzy": false, "avatar": null, "github": {"id": null, "blog": null, "avatar": null, "handle": null, "company": null, "followers": null, "following": null}, "twitter": {"id": null, "bio": null, "site": null, "avatar": null, "handle": null, "location": null, "statuses": null, "favorites": null, "followers": null, "following": null}, "facebook": {"handle": null}, "gravatar": {"urls": [], "avatar": null, "handle": null, "avatars": []}, "linkedin": {"handle": "in/niklashatje"}, "location": "Hamburg, HH, DE", "timeZone": "Europe/Berlin", "indexedAt": "2024-01-24T15:49:16.888Z", "utcOffset": 1, "employment": {"name": "n8n", "role": null, "title": "Senior Produktmanager", "domain": "n8n.io", "subRole": null, "seniority": "manager"}, "googleplus": {"handle": null}, "emailProvider": false}], "Enrich Company": [{"id": "546ba3f6-a6b7-41a1-aed8-4f9bba4119e8", "geo": {"lat": 52.5297761, "lng": 13.3892831, "city": "Berlin", "state": "Berlin", "country": "Germany", "stateCode": "BE", "postalCode": "10115", "streetName": "Borsigstraße", "subPremise": null, "countryCode": "DE", "streetNumber": "27", "streetAddress": "27 Borsigstraße"}, "logo": "https://logo.clearbit.com/n8n.io", "name": "n8n", "site": {"phoneNumbers": [], "emailAddresses": []}, "tags": ["Information Technology & Services", "Computer Programming", "Software", "Professional Services", "Computers", "E-commerce", "Technology", "B2B", "B2C", "SAAS", "Mobile"], "tech": ["mailgun", "cloud_flare", "workable", "google_tag_manager", "google_apps", "typeform", "google_analytics", "facebook_advertiser", "stripe"], "type": "private", "phone": null, "domain": "n8n.io", "parent": {"domain": null}, "ticker": null, "metrics": {"raised": 13500000, "employees": 550, "marketCap": null, "alexaUsRank": null, "trafficRank": "high", "annualRevenue": null, "fiscalYearEnd": null, "employeesRange": "51-250", "alexaGlobalRank": 61323, "estimatedAnnualRevenue": "$10M-$50M"}, "twitter": {"id": "1068479892537384960", "bio": "n8n is an extendable workflow automation tool which enables you to connect anything to everything via its open, fair-code model.", "site": "https://t.co/F1fzJ95bij", "avatar": "https://pbs.twimg.com/profile_images/1536335358803251202/-gASF0c6_normal.png", "handle": "n8n_io", "location": "Berlin, Germany", "followers": 7238, "following": 1}, "category": {"sector": "Information Technology", "sicCode": "73", "gicsCode": "45102010", "industry": "Internet Software & Services", "naicsCode": "54", "sic4Codes": ["7371"], "naics6Codes": ["541511"], "subIndustry": "Internet Software & Services", "industryGroup": "Software & Services", "naics6Codes2022": ["541511"]}, "facebook": {"likes": null, "handle": null}, "linkedin": {"handle": "company/n8n"}, "location": "Borsigstraße 27, 10115 Berlin, Germany", "timeZone": "Europe/Berlin", "indexedAt": "2024-02-08T21:30:12.524Z", "legalName": null, "utcOffset": 1, "crunchbase": {"handle": null}, "description": "n8n.io is a powerful workflow automation tool that enables you to connect anything to everything. It is a free and open-source tool that can be installed on-premises, downloaded as a desktop app, or used as a cloud service. With n8n, you can automate b...", "foundedYear": 2019, "identifiers": {"usCIK": null, "usEIN": null}, "domainAliases": ["n8n.cloud", "n8n.com"], "emailProvider": false, "techCategories": ["email_delivery_service", "dns", "applicant_tracking_system", "tag_management", "productivity", "form_builder", "analytics", "advertising", "payment"], "ultimateParent": {"domain": null}}], "n8n Form Trigger": [{"formMode": "test", "submittedAt": "2024-02-21T10:06:56.002Z", "What's your role?": "Product", "What's your business email?": "<EMAIL>"}]}, "connections": {"Clearbit": {"main": [[{"node": "Enrich Company", "type": "main", "index": 0}]]}, "Enrich Company": {"main": [[{"node": "If B2B and > 499 employees", "type": "main", "index": 0}]]}, "Map email field": {"main": [[{"node": "Filter out personal emails", "type": "main", "index": 0}]]}, "n8n Form Trigger": {"main": [[{"node": "Map email field", "type": "main", "index": 0}]]}, "Filter out personal emails": {"main": [[{"node": "Clearbit", "type": "main", "index": 0}]]}, "If B2B and > 499 employees": {"main": [[{"node": "Send Email", "type": "main", "index": 0}], [{"node": "Submission does not match criteria, don't do anything", "type": "main", "index": 0}]]}}}