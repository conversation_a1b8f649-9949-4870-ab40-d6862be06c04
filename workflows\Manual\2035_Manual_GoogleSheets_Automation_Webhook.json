{"id": "ziJG3tgG91Gkbina", "meta": {"instanceId": "fddb3e91967f1012c95dd02bf5ad21f279fc44715f47a7a96a33433621caa253"}, "name": "n8n-農產品", "tags": [{"id": "YaVjRtdJOQvaEnU3", "name": "testing", "createdAt": "2024-12-29T07:47:44.069Z", "updatedAt": "2024-12-29T07:47:44.069Z"}], "nodes": [{"id": "07d7241d-480b-4d53-96ba-485d1dc469f6", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [0, 0], "parameters": {}, "typeVersion": 1}, {"id": "02dfaea7-be8c-49fd-a869-39cccf6e6dde", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [220, 0], "parameters": {"url": "https://data.moa.gov.tw/api/v1/SheepQuotation", "options": {}, "sendQuery": true, "sendHeaders": true, "queryParameters": {"parameters": [{"name": "Start_time", "value": "2024/12/01"}, {"name": "End_time", "value": "2024/12/31"}, {"name": "MarketName", "value": "台北二"}, {"name": "api_key", "value": "3AFID4BGE9PDQ2WTFDO1X61H4RNQLE"}]}, "headerParameters": {"parameters": [{"name": "accept", "value": "application/json"}]}}, "typeVersion": 4.2}, {"id": "69a1d5c6-a59f-4b4b-9e51-d75f319a75c6", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [440, 0], "parameters": {"options": {}, "fieldToSplitOut": "Data"}, "typeVersion": 1}, {"id": "082828e0-4cc6-465c-bfe4-561f8e4e3c50", "name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [660, 0], "parameters": {"columns": {"value": {}, "schema": [{"id": "TransDate", "type": "string", "display": true, "required": false, "displayName": "TransDate", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TcType", "type": "string", "display": true, "required": false, "displayName": "TcType", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CropCode", "type": "string", "display": true, "required": false, "displayName": "CropCode", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CropName", "type": "string", "display": true, "required": false, "displayName": "CropName", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MarketCode", "type": "string", "display": true, "required": false, "displayName": "MarketCode", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MarketName", "type": "string", "display": true, "required": false, "displayName": "MarketName", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Upper_Price", "type": "string", "display": true, "required": false, "displayName": "Upper_Price", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Middle_Price", "type": "string", "display": true, "required": false, "displayName": "Middle_Price", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Lower_Price", "type": "string", "display": true, "required": false, "displayName": "Lower_Price", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Avg_Price", "type": "string", "display": true, "required": false, "displayName": "Avg_Price", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Trans_Quantity", "type": "string", "display": true, "required": false, "displayName": "Trans_Quantity", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": []}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/17EJTOetBsfoGkzADCUHPoXaQW7FLQziYmQxKNJNnDIU/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "17EJTOetBsfoGkzADCUHPoXaQW7FLQziYmQxKNJNnDIU", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/17EJTOetBsfoGkzADCUHPoXaQW7FLQziYmQxKNJNnDIU/edit?usp=drivesdk", "cachedResultName": "n8n爬蟲-農產品"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "atsKA0m2aQXeL6i6", "name": "Google Sheets account"}}, "typeVersion": 4.5}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "b7991044-da7e-425f-a2ea-692e3d8d642b", "connections": {"Split Out": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}}}