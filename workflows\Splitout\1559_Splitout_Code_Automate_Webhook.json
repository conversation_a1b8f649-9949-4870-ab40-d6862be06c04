{"id": "NDCN2arRu5tLuP61", "meta": {"instanceId": "36147281c0732d54779505fe69cf0516d4b8760fdbbc308b1950e452edcf85e8", "templateCredsSetupCompleted": true}, "name": "Automate PDF Image Extraction & Analysis with GPT-4o and Google Drive", "tags": [], "nodes": [{"id": "78bb478a-721d-433f-a615-8f131ef1d87f", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-1180, 140], "parameters": {}, "typeVersion": 1}, {"id": "b1c2e97b-3539-4e16-89df-434a34c6a243", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-740, -440], "parameters": {"color": 3, "width": 360, "height": 480, "content": "###  Setup\n1.Set up your credentials when you first open the workflow. You’ll need accounts for OpenAI, Convert API, and Google Drive.\n2.Convert API does not rate-limit your API, sometimes you may receive 503 service unavailable error.\nNevertheless, it doesn’t mean that you cannot convert your file. It simply means that you should retry the conversion in a few seconds.\n3.Upload a PDF with images to Google Drive.\n4.Remove unnecessary parts and retrieve image-related information.\n5.Integrate image and image analysis information together.\n6.Analyze each image using the OPENAI GPT-4o model.\n7.Retrieve all image analysis content and image URL\n8.Integrate multiple image URLs and analysis content\n9.Output content to a .txt file.\n\nTemplate was created in n8n v1.83.2"}, "typeVersion": 1}, {"id": "3b2a81eb-19b4-4685-90a3-1b4096b2d3b7", "name": "Get pdf file", "type": "n8n-nodes-base.googleDrive", "position": [-1000, 40], "parameters": {"fileId": {"__rl": true, "mode": "list", "value": "1WoqaMgaCD-gChGWUqPRJ7-pxbTozEuXN", "cachedResultUrl": "https://drive.google.com/file/d/1WoqaMgaCD-gChGWUqPRJ7-pxbTozEuXN/view?usp=drivesdk", "cachedResultName": "Building Effective AI Agents _ Anthropic.pdf"}, "options": {}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "nxqV58j7kOaLFzhj", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "89208aa8-37d8-424c-a936-52539a9bc7ee", "name": "Get all img_url", "type": "n8n-nodes-base.set", "position": [-520, 160], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "7715e33a-c5cc-4a22-aa28-ac19a24bbd7c", "name": "url", "type": "string", "value": "={{ $json.Url }}"}]}}, "typeVersion": 3.4}, {"id": "5c1ece53-1910-42d6-a1e4-bfa6d5a83fe9", "name": "Analyze image", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [-360, 40], "parameters": {"text": "Please analyze the video in detail and provide a thorough explanation", "modelId": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "GPT-4O"}, "options": {}, "resource": "image", "simplify": false, "imageUrls": "={{ $json.url }}", "operation": "analyze"}, "credentials": {"openAiApi": {"id": "4wadssyBOfOAfo2P", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "9e09364d-fb82-4524-b6aa-b8a6040893ba", "name": "Extract pdf image", "type": "n8n-nodes-base.httpRequest", "position": [-840, 140], "parameters": {"url": "https://v2.convertapi.com/convert/pdf/to/extract-images", "method": "POST", "options": {}, "sendBody": true, "contentType": "multipart-form-data", "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "StoreFile", "value": "true"}, {"name": "ImageOutputFormat", "value": "jpg"}, {"name": "File", "parameterType": "formBinaryData", "inputDataFieldName": "data"}]}, "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{}]}}, "credentials": {"httpHeaderAuth": {"id": "5hUN8DpheywQE5v6", "name": "convertapi extract image"}}, "retryOnFail": true, "typeVersion": 4.2, "waitBetweenTries": 5000}, {"id": "8fd6e8ae-bea1-4d7f-8599-7bf6f4eee9e5", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-1080, 280], "parameters": {"color": 5, "width": 202, "height": 99, "content": "### You can exchange this with any trigger you like (*e.g. google drive trigger*)"}, "typeVersion": 1}, {"id": "b0ce7fdd-7328-49b2-8ec6-797205aa7ab5", "name": "Get image data", "type": "n8n-nodes-base.splitOut", "position": [-680, 40], "parameters": {"options": {}, "fieldToSplitOut": "Files"}, "typeVersion": 1}, {"id": "c5855876-41d9-46a4-bdec-e60effa116e8", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-1060, -220], "parameters": {"width": 300, "content": "### PDF Image Extraction and Analysis  with GPT-4o\nThis n8n workflow automates the process of extracting images from PDF files and analyzing them with AI, then compiling the results into a document."}, "typeVersion": 1}, {"id": "7cea9e1b-0094-4220-bdf6-f13ab795e394", "name": "Get image analyze content", "type": "n8n-nodes-base.set", "position": [-200, 160], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "2868a5bd-86a8-4962-a867-b4a354276181", "name": "content", "type": "string", "value": "={{ $('Get all img_url').item.json.url }}\n{{ $json.choices[0].message.content }}"}]}}, "typeVersion": 3.4}, {"id": "de4b6fab-d086-4bf3-81fc-a6f7b7eac24b", "name": "Integrate all content to a a content", "type": "n8n-nodes-base.code", "position": [-40, 40], "parameters": {"jsCode": "const mergedContent = items.map(item => item.json.content).join('\\n');\n\nreturn [\n  {\n    json: {\n      content: mergedContent\n    }\n  }\n];\n"}, "typeVersion": 2}, {"id": "e66f7c66-9096-4bf5-b1dc-02dafeaa62ee", "name": "Output content to a .txt file", "type": "n8n-nodes-base.convertToFile", "position": [140, 140], "parameters": {"options": {}, "operation": "toText", "sourceProperty": "content"}, "typeVersion": 1.1}], "active": false, "pinData": {}, "settings": {"timezone": "Asia/Taipei", "callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1"}, "versionId": "4c2771a6-f532-4bfd-bb98-3eae8b0ee85a", "connections": {"Get pdf file": {"main": [[{"node": "Extract pdf image", "type": "main", "index": 0}]]}, "Analyze image": {"main": [[{"node": "Get image analyze content", "type": "main", "index": 0}]]}, "Get image data": {"main": [[{"node": "Get all img_url", "type": "main", "index": 0}]]}, "Get all img_url": {"main": [[{"node": "Analyze image", "type": "main", "index": 0}]]}, "Extract pdf image": {"main": [[{"node": "Get image data", "type": "main", "index": 0}]]}, "Get image analyze content": {"main": [[{"node": "Integrate all content to a a content", "type": "main", "index": 0}]]}, "Output content to a .txt file": {"main": [[]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Get pdf file", "type": "main", "index": 0}]]}, "Integrate all content to a a content": {"main": [[{"node": "Output content to a .txt file", "type": "main", "index": 0}]]}}}