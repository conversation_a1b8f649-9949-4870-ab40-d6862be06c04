{"id": "mqindLlOy0A0e5aA", "meta": {"instanceId": "aae6cde6c06a77e3bf2445b060051b4c63107a8258d59cb57184495848d659de", "templateCredsSetupCompleted": true}, "name": "Outlook", "tags": [], "nodes": [{"id": "b2e6066f-a4c7-486c-aa0d-06a4c92aa745", "name": "Connect Outlook & Set Filter", "type": "n8n-nodes-base.microsoftOutlookTrigger", "position": [-240, -260], "parameters": {"output": "raw", "filters": {"sender": "<EMAIL>"}, "options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}}, "credentials": {"microsoftOutlookOAuth2Api": {"id": "nRYwUzhHrSBFtcSS", "name": "Microsoft Outlook account 2"}}, "typeVersion": 1}, {"id": "98f20649-4842-44b8-86c3-a153cd7f4ce2", "name": "Add OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [140, 100], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "sXujPvtyB7ZEzKZs", "name": "OpenAi account 2"}}, "typeVersion": 1.2}, {"id": "bf4cb647-91c6-4f3d-a685-ed928b431ef5", "name": "Add AI Agent Instructions", "type": "@n8n/n8n-nodes-langchain.agent", "position": [140, -120], "parameters": {"text": "=Write a reply to the following email, then save it as a draft to the email thread:\n<email>\nID: {{ $json.id }}\nFrom: {{ $json.from.emailAddress.address }}\nSubject: {{ $json.subject }}\nMessage: {{ $json.body.content }}\n</email>", "options": {"systemMessage": "#role\nYou are an AI assistant specializing in replying to incoming emails to [YOUR NAME] Outlook inbox.\n\n#capabilities and limitations\nYour reply will be limited to the current email message, not the email string. Do not hallucinate.\n\n#response\nReply in a casual, modern, professional, concise writing style. You should sound like [YOUR NAME HERE]. Here are examples of [YOUR NAME HERE] voice:\n<example>\n[COPY & PASTE REPLY SAMPLES FROM YOUR EMAIL]\n</example>\n<example>\n[COPY & PASTE REPLY SAMPLES FROM YOUR EMAIL]\n</example>\n<example>\n[ADD A VARIETY OF REPLY SAMPLES SO THE AGENT UNDERSTANDS YOUR TONE & STYLE]\n</example>"}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "8432a5b8-db95-4e1a-b573-6d4f6a026659", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-60, -320], "parameters": {"color": 5, "content": "Trigger Action\n1) Connect a Microsoft email account you can authenticate\n2) Trigger is set to \"message received\" and the output \"raw\"\n3) Add the email address(es) you want the AI agent to handle"}, "typeVersion": 1}, {"id": "72b652a4-cebe-48d6-81ad-d078f0655041", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-300, -80], "parameters": {"color": 5, "width": 340, "height": 440, "content": "Agent Instructions\n\n#role\nYou are an AI assistant specializing in replying to incoming emails to [YOUR NAME] Outlook inbox.\n\n#capabilities and limitations\nYour reply will be limited to the current email message, not the email string. Do not hallucinate.\n\n#response\nReply in a casual, modern, professional, concise writing style. You should sound like [YOUR NAME HERE]. Here are examples of [YOUR NAME HERE] voice:\n<example>\n[COPY & PASTE REPLY SAMPLES FROM YOUR EMAIL]\n</example>\n<example>\n[COPY & PASTE REPLY SAMPLES FROM YOUR EMAIL]\n</example>\n<example>\n[ADD A VARIETY OF REPLY SAMPLES SO THE AGENT UNDERSTANDS YOUR TONE & STYLE]\n</example>"}, "typeVersion": 1}, {"id": "e2e761a3-9744-439c-9764-ab4c68621c69", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [60, 260], "parameters": {"color": 5, "height": 100, "content": "Add AI Model\n1) OpenAI Account Credentials Required\n2) Select model (ie. gpt-4o-mini)"}, "typeVersion": 1}, {"id": "555f37e2-a8fe-4cd4-9a55-758c07b6a4c4", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [320, 180], "parameters": {"color": 5, "height": 180, "content": "Reply Settings\n1) Manually set the resource, operation, and message\n2) Toggle switch to reply only to sender\n3) Let the AI model define the \"message\"\n4) Additional fields for the \"reply to\" and \"subject\""}, "typeVersion": 1}, {"id": "9b1a5b51-e502-42f4-80ad-03d050a3c7cb", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [580, 120], "parameters": {"height": 140, "content": "Draft a Reply\n1. Follows all the same \"Reply to Email\" settings EXCEPT the email reply is saved in your DRAFTS folder\n2. This setting is great if you want a human checkpoint before sending"}, "typeVersion": 1}, {"id": "46f4b974-4da7-471b-ba49-3737685d123e", "name": "Reply to Email", "type": "n8n-nodes-base.microsoftOutlookTool", "position": [400, 40], "webhookId": "6d01cb74-0463-4042-8917-acd2f552d4b5", "parameters": {"message": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message', ``, 'string') }}", "options": {}, "messageId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "operation": "reply", "descriptionType": "manual", "additionalFields": {"replyTo": "={{ $json.sender.emailAddress.address }}", "subject": "={{ $json.subject }}"}, "replyToSenderOnly": true}, "credentials": {"microsoftOutlookOAuth2Api": {"id": "nRYwUzhHrSBFtcSS", "name": "Microsoft Outlook"}}, "typeVersion": 2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "fcd52e16-512c-4655-ae18-1a4b87190e0d", "connections": {"Reply to Email": {"ai_tool": [[{"node": "Add AI Agent Instructions", "type": "ai_tool", "index": 0}]]}, "Add OpenAI Chat Model": {"ai_languageModel": [[{"node": "Add AI Agent Instructions", "type": "ai_languageModel", "index": 0}]]}, "Draft a Reply [optional]": {"ai_tool": [[{"node": "Add AI Agent Instructions", "type": "ai_tool", "index": 0}]]}, "Connect Outlook & Set Filter": {"main": [[{"node": "Add AI Agent Instructions", "type": "main", "index": 0}]]}}}