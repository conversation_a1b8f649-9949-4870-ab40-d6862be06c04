{"id": "88", "name": "Get the current weather data for a city", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "OpenWeatherMap", "type": "n8n-nodes-base.openWeatherMap", "position": [450, 300], "parameters": {"cityName": "berlin,de"}, "credentials": {"openWeatherMapApi": ""}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"OpenWeatherMap": {"main": [[]]}, "On clicking 'execute'": {"main": [[{"node": "OpenWeatherMap", "type": "main", "index": 0}]]}}}