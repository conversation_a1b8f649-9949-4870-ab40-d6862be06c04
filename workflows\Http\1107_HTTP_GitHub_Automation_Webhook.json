{"id": "6", "name": "Dashboard", "nodes": [{"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [-290, 180], "parameters": {"triggerTimes": {"item": [{"mode": "everyMinute"}]}}, "typeVersion": 1}, {"name": "Dashboard Configuration", "type": "n8n-nodes-base.set", "color": "#FF0000", "notes": "Update project settings", "position": [-10, 180], "parameters": {"values": {"string": [{"name": "dashboardHostname", "value": "http://************:8080"}, {"name": "dashboardAuthToken", "value": "n8n-rocks!"}, {"name": "product_hunt_post_id", "value": "170391"}, {"name": "npm_package", "value": "n8n"}, {"name": "docker_name", "value": "n8nio"}, {"name": "docker_repository", "value": "n8n"}, {"name": "github_owner", "value": "n8n-io"}, {"name": "github_repo", "value": "n8n"}]}, "options": {}}, "notesInFlow": true, "typeVersion": 1}, {"name": "Retrieve Docker Data", "type": "n8n-nodes-base.httpRequest", "position": [260, 300], "parameters": {"url": "=https://hub.docker.com/v2/repositories/{{$node[\"Dashboard Configuration\"].json[\"docker_name\"]}}/{{$node[\"Dashboard Configuration\"].json[\"docker_repository\"]}}", "options": {}, "queryParametersUi": {"parameter": []}, "headerParametersUi": {"parameter": [{"name": "User-Agent", "value": "n8n"}]}}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.httpRequest", "position": [630, 220], "parameters": {"url": "={{$node[\"Dashboard Configuration\"].json[\"dashboardHostname\"]}}/widgets/docker_pulls", "options": {}, "requestMethod": "POST", "bodyParametersUi": {"parameter": [{"name": "auth_token", "value": "={{$node[\"Dashboard Configuration\"].json[\"dashboardAuthToken\"]}}"}, {"name": "current", "value": "={{$node[\"Massage Docker Data\"].json[\"pull_count\"]}}"}]}}, "typeVersion": 1, "alwaysOutputData": true}, {"name": "Docker Stars", "type": "n8n-nodes-base.httpRequest", "position": [630, 400], "parameters": {"url": "={{$node[\"Dashboard Configuration\"].json[\"dashboardHostname\"]}}/widgets/docker_stars", "options": {}, "requestMethod": "POST", "bodyParametersUi": {"parameter": [{"name": "auth_token", "value": "={{$node[\"Dashboard Configuration\"].json[\"dashboardAuthToken\"]}}"}, {"name": "current", "value": "={{$node[\"Massage Docker Data\"].json[\"star_count\"]}}"}]}}, "typeVersion": 1, "alwaysOutputData": true}, {"name": "Retrieve npm Data", "type": "n8n-nodes-base.httpRequest", "position": [250, 50], "parameters": {"url": "=https://api.npms.io/v2/package/{{$node[\"Dashboard Configuration\"].json[\"npm_package\"]}}", "options": {}, "headerParametersUi": {"parameter": [{"name": "User-Agent", "value": "n8n"}]}}, "typeVersion": 1}, {"name": "GitHub Watchers", "type": "n8n-nodes-base.httpRequest", "position": [820, 640], "parameters": {"url": "={{$node[\"Dashboard Configuration\"].json[\"dashboardHostname\"]}}/widgets/github_watchers", "options": {}, "requestMethod": "POST", "bodyParametersUi": {"parameter": [{"name": "auth_token", "value": "={{$node[\"Dashboard Configuration\"].json[\"dashboardAuthToken\"]}}"}, {"name": "current", "value": "={{$node[\"Massage GitHub Data\"].json[\"subscribers_count\"]}}"}]}}, "typeVersion": 1, "alwaysOutputData": true}, {"name": "GitHub Forks", "type": "n8n-nodes-base.httpRequest", "position": [820, 800], "parameters": {"url": "={{$node[\"Dashboard Configuration\"].json[\"dashboardHostname\"]}}/widgets/github_forks", "options": {}, "requestMethod": "POST", "bodyParametersUi": {"parameter": [{"name": "auth_token", "value": "={{$node[\"Dashboard Configuration\"].json[\"dashboardAuthToken\"]}}"}, {"name": "current", "value": "={{$node[\"Massage GitHub Data\"].json[\"forks_count\"]}}"}]}}, "typeVersion": 1, "alwaysOutputData": true}, {"name": "GitHub Open Issues ", "type": "n8n-nodes-base.httpRequest", "position": [620, 860], "parameters": {"url": "={{$node[\"Dashboard Configuration\"].json[\"dashboardHostname\"]}}/widgets/github_open_issues", "options": {}, "requestMethod": "POST", "bodyParametersUi": {"parameter": [{"name": "auth_token", "value": "={{$node[\"Dashboard Configuration\"].json[\"dashboardAuthToken\"]}}"}, {"name": "current", "value": "={{$node[\"Massage GitHub Data\"].json[\"open_issues_count\"]}}"}]}}, "typeVersion": 1, "alwaysOutputData": true}, {"name": "GitHub Stars", "type": "n8n-nodes-base.httpRequest", "position": [620, 560], "parameters": {"url": "={{$node[\"Dashboard Configuration\"].json[\"dashboardHostname\"]}}/widgets/github_stars", "options": {}, "requestMethod": "POST", "bodyParametersUi": {"parameter": [{"name": "auth_token", "value": "={{$node[\"Dashboard Configuration\"].json[\"dashboardAuthToken\"]}}"}, {"name": "current", "value": "={{$node[\"Massage GitHub Data\"].json[\"stargazers_count\"]}}"}]}}, "typeVersion": 1, "alwaysOutputData": true}, {"name": "npm Maintenance", "type": "n8n-nodes-base.httpRequest", "position": [830, -90], "parameters": {"url": "={{$node[\"Dashboard Configuration\"].json[\"dashboardHostname\"]}}/widgets/npm_maintenance", "options": {}, "requestMethod": "POST", "bodyParametersUi": {"parameter": [{"name": "auth_token", "value": "={{$node[\"Dashboard Configuration\"].json[\"dashboardAuthToken\"]}}"}, {"name": "value", "value": "={{$node[\"Massage npm Data\"].json[\"score\"][\"detail\"][\"maintenance\"]}}"}]}}, "typeVersion": 1, "alwaysOutputData": true}, {"name": "npm Popularity", "type": "n8n-nodes-base.httpRequest", "position": [1030, 0], "parameters": {"url": "={{$node[\"Dashboard Configuration\"].json[\"dashboardHostname\"]}}/widgets/npm_popularity", "options": {}, "requestMethod": "POST", "bodyParametersUi": {"parameter": [{"name": "auth_token", "value": "={{$node[\"Dashboard Configuration\"].json[\"dashboardAuthToken\"]}}"}, {"name": "value", "value": "={{$node[\"Massage npm Data\"].json[\"score\"][\"detail\"][\"popularity\"]}}"}]}}, "typeVersion": 1, "alwaysOutputData": true}, {"name": "npm Quality", "type": "n8n-nodes-base.httpRequest", "position": [1030, 150], "parameters": {"url": "={{$node[\"Dashboard Configuration\"].json[\"dashboardHostname\"]}}/widgets/npm_quality", "options": {}, "requestMethod": "POST", "bodyParametersUi": {"parameter": [{"name": "auth_token", "value": "={{$node[\"Dashboard Configuration\"].json[\"dashboardAuthToken\"]}}"}, {"name": "value", "value": "={{$node[\"Massage npm Data\"].json[\"score\"][\"detail\"][\"quality\"]}}"}]}}, "typeVersion": 1, "alwaysOutputData": true}, {"name": "npm Final", "type": "n8n-nodes-base.httpRequest", "position": [830, 190], "parameters": {"url": "={{$node[\"Dashboard Configuration\"].json[\"dashboardHostname\"]}}/widgets/npm_final", "options": {}, "requestMethod": "POST", "bodyParametersUi": {"parameter": [{"name": "auth_token", "value": "={{$node[\"Dashboard Configuration\"].json[\"dashboardAuthToken\"]}}"}, {"name": "value", "value": "={{$node[\"Massage npm Data\"].json[\"score\"][\"final\"]}}"}]}}, "typeVersion": 1, "alwaysOutputData": true}, {"name": "Product Hunt Rating", "type": "n8n-nodes-base.httpRequest", "position": [630, -510], "parameters": {"url": "={{$node[\"Dashboard Configuration\"].json[\"dashboardHostname\"]}}/widgets/prod_hunt_rating", "options": {}, "requestMethod": "POST", "bodyParametersUi": {"parameter": [{"name": "auth_token", "value": "={{$node[\"Dashboard Configuration\"].json[\"dashboardAuthToken\"]}}"}, {"name": "value", "value": "={{$node[\"Retrieve Product Hunt Data\"].json[\"data\"][\"post\"][\"reviewsRating\"]}}"}]}}, "typeVersion": 1, "alwaysOutputData": true}, {"name": "Product Hunt Reviews", "type": "n8n-nodes-base.httpRequest", "position": [830, -410], "parameters": {"url": "={{$node[\"Dashboard Configuration\"].json[\"dashboardHostname\"]}}/widgets/prod_hunt_reviews", "options": {}, "requestMethod": "POST", "bodyParametersUi": {"parameter": [{"name": "auth_token", "value": "={{$node[\"Dashboard Configuration\"].json[\"dashboardAuthToken\"]}}"}, {"name": "current", "value": "={{$node[\"Massage Product Hunt Data\"].json[\"data\"][\"post\"][\"reviewsCount\"]}}"}]}}, "typeVersion": 1, "alwaysOutputData": true}, {"name": "Product Hunt Votes", "type": "n8n-nodes-base.httpRequest", "position": [830, -260], "parameters": {"url": "={{$node[\"Dashboard Configuration\"].json[\"dashboardHostname\"]}}/widgets/prod_hunt_votes", "options": {}, "requestMethod": "POST", "bodyParametersUi": {"parameter": [{"name": "auth_token", "value": "={{$node[\"Dashboard Configuration\"].json[\"dashboardAuthToken\"]}}"}, {"name": "current", "value": "={{$node[\"Massage Product Hunt Data\"].json[\"data\"][\"post\"][\"votesCount\"]}}"}]}}, "typeVersion": 1, "alwaysOutputData": true}, {"name": "Product Hunt Comments", "type": "n8n-nodes-base.httpRequest", "position": [630, -210], "parameters": {"url": "={{$node[\"Dashboard Configuration\"].json[\"dashboardHostname\"]}}/widgets/prod_hunt_comments", "options": {}, "requestMethod": "POST", "bodyParametersUi": {"parameter": [{"name": "auth_token", "value": "={{$node[\"Dashboard Configuration\"].json[\"dashboardAuthToken\"]}}"}, {"name": "current", "value": "={{$node[\"Massage Product Hunt Data\"].json[\"data\"][\"post\"][\"commentsCount\"]}}"}]}}, "typeVersion": 1, "alwaysOutputData": true}, {"name": "GitHub", "type": "n8n-nodes-base.github", "color": "#FF0000", "position": [250, 710], "parameters": {"owner": "={{$node[\"Dashboard Configuration\"].json[\"github_owner\"]}}", "resource": "repository", "operation": "get", "repository": "={{$node[\"Dashboard Configuration\"].json[\"github_repo\"]}}"}, "credentials": {"githubApi": ""}, "typeVersion": 1}, {"name": "Retrieve Product Hunt Data", "type": "n8n-nodes-base.httpRequest", "color": "#FF0000", "notes": "Update authorization token", "position": [250, -360], "parameters": {"url": "https://api.producthunt.com/v2/api/graphql", "options": {}, "requestMethod": "POST", "queryParametersUi": {"parameter": [{"name": "query", "value": "={\n  post(id: {{$node[\"Dashboard Configuration\"].json[\"product_hunt_post_id\"]}}) {\n    commentsCount\n    votesCount\n    reviewsCount\n    reviewsRating\n    name\n  }\n}"}]}, "headerParametersUi": {"parameter": [{"name": "User-Agent", "value": "n8n"}, {"name": "authorization", "value": "Bearer <Enter Product Hunt token here>"}]}}, "notesInFlow": true, "typeVersion": 1}, {"name": "Massage npm Data", "type": "n8n-nodes-base.function", "position": [440, 50], "parameters": {"functionCode": "items[0].json.score.detail.maintenance = parseFloat(items[0].json.score.detail.maintenance.toFixed(2));\nitems[0].json.score.detail.popularity= parseFloat(items[0].json.score.detail.popularity.toFixed(2));\nitems[0].json.score.detail.quality= parseFloat(items[0].json.score.detail.quality.toFixed(2));\nitems[0].json.score.final= parseFloat(items[0].json.score.final.toFixed(2));\n\nreturn items;"}, "typeVersion": 1}, {"name": "Massage Product Hunt Data", "type": "n8n-nodes-base.function", "position": [440, -360], "parameters": {"functionCode": "items[0].json.data.post.commentsCount = items[0].json.data.post.commentsCount.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, \",\");\nitems[0].json.data.post.votesCount= items[0].json.data.post.votesCount.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, \",\");\nitems[0].json.data.post.reviewsCount= items[0].json.data.post.reviewsCount.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, \",\");\n\nreturn items;\n"}, "typeVersion": 1}, {"name": "Massage Docker Data", "type": "n8n-nodes-base.function", "position": [460, 300], "parameters": {"functionCode": "items[0].json.star_count = items[0].json.star_count.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, \",\");\nitems[0].json.pull_count = items[0].json.pull_count.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, \",\");\n\nreturn items;\n"}, "typeVersion": 1}, {"name": "Massage GitHub Data", "type": "n8n-nodes-base.function", "position": [450, 710], "parameters": {"functionCode": "items[0].json.stargazers_count = items[0].json.stargazers_count.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, \",\");\nitems[0].json.subscribers_count = items[0].json.subscribers_count.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, \",\");\nitems[0].json.forks_count = items[0].json.forks_count.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, \",\");\nitems[0].json.open_issues_count = items[0].json.open_issues_count.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, \",\");\n\nreturn items;"}, "typeVersion": 1}], "active": true, "settings": {}, "connections": {"Cron": {"main": [[{"node": "Dashboard Configuration", "type": "main", "index": 0}]]}, "GitHub": {"main": [[{"node": "Massage GitHub Data", "type": "main", "index": 0}]]}, "Massage npm Data": {"main": [[{"node": "npm Maintenance", "type": "main", "index": 0}, {"node": "npm Quality", "type": "main", "index": 0}, {"node": "npm Popularity", "type": "main", "index": 0}, {"node": "npm Final", "type": "main", "index": 0}]]}, "Retrieve npm Data": {"main": [[{"node": "Massage npm Data", "type": "main", "index": 0}]]}, "Massage Docker Data": {"main": [[{"node": "Docker Stars", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Massage GitHub Data": {"main": [[{"node": "GitHub Stars", "type": "main", "index": 0}, {"node": "GitHub Watchers", "type": "main", "index": 0}, {"node": "GitHub Forks", "type": "main", "index": 0}, {"node": "GitHub Open Issues ", "type": "main", "index": 0}]]}, "Retrieve Docker Data": {"main": [[{"node": "Massage Docker Data", "type": "main", "index": 0}]]}, "Dashboard Configuration": {"main": [[{"node": "Retrieve Product Hunt Data", "type": "main", "index": 0}, {"node": "Retrieve npm Data", "type": "main", "index": 0}, {"node": "Retrieve Docker Data", "type": "main", "index": 0}, {"node": "GitHub", "type": "main", "index": 0}]]}, "Massage Product Hunt Data": {"main": [[{"node": "Product Hunt Rating", "type": "main", "index": 0}, {"node": "Product Hunt Reviews", "type": "main", "index": 0}, {"node": "Product Hunt Votes", "type": "main", "index": 0}, {"node": "Product Hunt Comments", "type": "main", "index": 0}]]}, "Retrieve Product Hunt Data": {"main": [[{"node": "Massage Product Hunt Data", "type": "main", "index": 0}]]}}}