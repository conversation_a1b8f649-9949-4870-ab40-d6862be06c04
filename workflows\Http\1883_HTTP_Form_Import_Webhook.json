{"id": "i8nBvPOtFYWk5eoq", "meta": {"instanceId": "c5a9958b493899f1235322c2c0e4f007083d1c79bb2c9043ae90b75371e276c7"}, "name": "Get PDF with JSReport", "tags": [{"id": "2L2vOvQ2wUBVYeh1", "name": "Send", "createdAt": "2024-05-03T13:40:43.868Z", "updatedAt": "2024-05-03T13:40:43.868Z"}, {"id": "SBlaOF5ezhukSiIT", "name": "JSReport", "createdAt": "2024-05-03T13:40:38.379Z", "updatedAt": "2024-05-03T13:40:38.379Z"}, {"id": "vRTFSK4WW6nL2U7z", "name": "PDF", "createdAt": "2024-05-03T13:40:34.380Z", "updatedAt": "2024-05-03T13:40:34.380Z"}], "nodes": [{"id": "9514b49d-80f3-41d2-bcbc-8fa08e27cb64", "name": "Get PDF From JSReport", "type": "n8n-nodes-base.httpRequest", "notes": "Generating the document in JSReport", "position": [1040, 320], "parameters": {"url": "https://xxx.jsreportonline.net/api/report", "method": "POST", "options": {}, "jsonBody": "=   {\n      \"template\": { \"name\" : \"invoice-main\" },\n      \"data\" :{\n    \"number\": \"123\",\n    \"seller\": {\n        \"name\": \"Next Step Webs, Inc.\",\n        \"road\": \"12345 Sunny Road\",\n        \"country\": \"Sunnyville, TX 12345\"\n    },\n    \"buyer\": {\n        \"name\": \"{{ $json[\"buyer name\"] }}\",\n        \"road\": \"{{ $json[\"buyer road\"] }}\",\n        \"country\": \"{{ $json[\"buyer country\"] }}\"\n    },\n    \"items\": [{\n        \"name\": \"{{ $json[\"Item 1 Name\"] }}\",\n        \"price\": {{ $json[\"Item 1 Price\"] }}\n    }, {\n        \"name\": \"{{ $json[\"Item 2 Name\"] }}\",\n        \"price\": {{ $json[\"Item 2 Price\"] }}\n    }]\n}\n   }", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth"}, "credentials": {"httpBasicAuth": {"id": "oKwHNpbRnChEV8xq", "name": "Unnamed credential"}}, "notesInFlow": true, "typeVersion": 4.2}, {"id": "d33abb5b-50b0-44d9-8a92-e910bb180ea5", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [460, 240], "parameters": {"height": 372, "content": "##  Streamlining Billing Processes: From Data Input to Document Generation\n\nThis process presents the possibility of using a form, such as the one provided by n8n, to enter billing information, then calling JSReport to generate documents such as PDFs, Word, Excel, etc., and finally sending the invoice by email.\n"}, "typeVersion": 1}, {"id": "85981fc7-ecb5-49f3-9395-9866ded70257", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [903, 240], "parameters": {"color": 4, "width": 363, "height": 568, "content": "## Information for calling JSReport\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n### URL API : \nhttps://xxx.jsreportonline.net/api/report\n\n### Use :\nTo use JSReport, simply call the APIs with the base URL. You can create a free account here: https://jsreport.net/online.\n\nThe APIs are available here: https://jsreport.net/learn/api.\n\nIn this example, we're sending a sample body that you can find in your JSReport test space."}, "typeVersion": 1}, {"id": "94ae99b3-0ec9-4916-9bf4-19cfeb599966", "name": "Form Invoice", "type": "n8n-nodes-base.formTrigger", "notes": "Allows you to enter invoice information", "position": [740, 320], "webhookId": "1d0c5777-4033-4bf4-8d0e-8a2069d79c86", "parameters": {"path": "1d0c5777-4033-4bf4-8d0e-8a2069d79c86", "options": {}, "formTitle": "Create Facture", "formFields": {"values": [{"fieldLabel": "buyer name", "requiredField": true}, {"fieldLabel": "buyer road", "requiredField": true}, {"fieldLabel": "buyer country", "requiredField": true}, {"fieldLabel": "Item 1 Name"}, {"fieldType": "number", "fieldLabel": "Item 1 Price"}, {"fieldLabel": "Item 2 Name"}, {"fieldLabel": "Item 2 Price"}]}, "formDescription": "Create a PDF invoice from an n8n and JSReport form"}, "notesInFlow": true, "typeVersion": 2}, {"id": "142c4a45-1228-4be5-8172-9834bb9ca491", "name": "Send invoice", "type": "n8n-nodes-base.gmail", "notes": "Using GMAIL to send the invoice", "position": [1340, 320], "parameters": {"sendTo": "<EMAIL>", "message": "Good morning,  \n\nPlease find your invoice.  \n\nSince<PERSON>y,", "options": {"attachmentsUi": {"attachmentsBinary": [{}]}}, "subject": "New Facture"}, "credentials": {"gmailOAuth2": {"id": "N3pxr94UxrQSovu5", "name": "Gmail account"}}, "notesInFlow": true, "typeVersion": 2.1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "8e1b0f98-68ec-4300-a948-52439d00db66", "connections": {"Form Invoice": {"main": [[{"node": "Get PDF From JSReport", "type": "main", "index": 0}]]}, "Get PDF From JSReport": {"main": [[{"node": "Send invoice", "type": "main", "index": 0}]]}}}