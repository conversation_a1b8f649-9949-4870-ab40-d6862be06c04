{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "disabled": true, "position": [70, 140], "parameters": {}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [70, 320], "parameters": {"triggerTimes": {"item": [{"hour": 8}]}}, "typeVersion": 1}, {"name": "Telegram", "type": "n8n-nodes-base.telegram", "position": [620, 210], "parameters": {"text": "<PERSON>IM<PERSON><PERSON> PACOTES TRANSPORTE-RECEBIDO PONTO MIX", "chatId": "-*********", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "5", "name": "Telegram account"}}, "typeVersion": 1}, {"name": "Telegram1", "type": "n8n-nodes-base.telegram", "position": [620, 460], "parameters": {"text": "LIMPOU PACOTES TRANSPORTE-RECEBIDO OBJETIVA", "chatId": "-*********", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "5", "name": "Telegram account"}}, "typeVersion": 1}, {"name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [70, 480], "webhookId": "7ecb2d2f-5a09-44a5-a7bc-27f188c74e0b", "parameters": {"path": "limparPaco<PERSON>", "options": {}}, "typeVersion": 1}, {"name": "limparPacoteCliente1", "type": "n8n-nodes-base.mySql", "position": [380, 470], "parameters": {"query": "-- LIMPAR ETIQUETAS ANTIGAS \nwith t as (\nselect token from i_objeto where modulo = 'pacoteProduto' and situacao = 'TRANSPORTE-RECEBIDO' and data <= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)\n)\nupdate i_objeto \nset modulo = 'DELETE'\nwhere modulo = 'pacoteProduto' and token in (select token from t)", "operation": "execute<PERSON>uery"}, "credentials": {"mySql": {"id": "4", "name": "OBJ"}}, "typeVersion": 1}, {"name": "limpaPacoteCliente0", "type": "n8n-nodes-base.mySql", "position": [380, 210], "parameters": {"query": "-- LIMPAR ETIQUETAS ANTIGAS \nwith t as (\nselect token from i_objeto where modulo = 'pacoteProduto' and situacao = 'TRANSPORTE-RECEBIDO' and data <= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)\n)\nupdate i_objeto \nset modulo = 'DELETE'\nwhere modulo = 'pacoteProduto' and token in (select token from t)", "operation": "execute<PERSON>uery"}, "credentials": {"mySql": {"id": "3", "name": "PPM"}}, "typeVersion": 1}], "connections": {"Cron": {"main": [[{"node": "limpaPacoteCliente0", "type": "main", "index": 0}, {"node": "limparPacoteCliente1", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "limpaPacoteCliente0", "type": "main", "index": 0}, {"node": "limparPacoteCliente1", "type": "main", "index": 0}]]}, "limpaPacoteCliente0": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "limparPacoteCliente1": {"main": [[{"node": "Telegram1", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "limpaPacoteCliente0", "type": "main", "index": 0}, {"node": "limparPacoteCliente1", "type": "main", "index": 0}]]}}}