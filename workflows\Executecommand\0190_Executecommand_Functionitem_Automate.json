{"nodes": [{"name": "Execute Command", "type": "n8n-nodes-base.executeCommand", "position": [600, 350], "parameters": {"command": "echo \"{ \\\"value1\\\": true, \\\"value2\\\": 1 }\""}, "typeVersion": 1}, {"name": "IF", "type": "n8n-nodes-base.if", "position": [800, 450], "parameters": {"conditions": {"boolean": [{"value1": "={{JSON.parse($node[\"Execute Command\"].data[\"stdout\"]).value1}}", "value2": true}]}}, "typeVersion": 1}, {"name": "To Flow Data", "type": "n8n-nodes-base.functionItem", "position": [800, 250], "parameters": {"functionCode": "item = JSON.parse(item.stdout);\nreturn item;"}, "typeVersion": 1}], "connections": {"Execute Command": {"main": [[{"node": "To Flow Data", "type": "main", "index": 0}, {"node": "IF", "type": "main", "index": 0}]]}}}