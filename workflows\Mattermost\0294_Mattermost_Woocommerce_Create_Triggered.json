{"id": "188", "name": "Send a message on Mattermost when an order is created in WooCommerce", "nodes": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.wooCommerceTrigger", "position": [550, 260], "webhookId": "84960a7c-cb69-4dfb-a5ed-aac12e0efbf8", "parameters": {"event": "order.created"}, "credentials": {"wooCommerceApi": "woocommerce"}, "typeVersion": 1}, {"name": "Mattermost", "type": "n8n-nodes-base.mattermost", "position": [750, 260], "parameters": {"message": "={{$node[\"WooCommerce Trigger\"].json[\"billing\"][\"first_name\"]}} bought {{$node[\"WooCommerce Trigger\"].json[\"line_items\"][0][\"name\"]}}!", "channelId": "pj1p95ebei8g3ro5p84kxxuuio", "attachments": [], "otherOptions": {}}, "credentials": {"mattermostApi": "Mattermost Credentials"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"WooCommerce Trigger": {"main": [[{"node": "Mattermost", "type": "main", "index": 0}]]}}}