{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "f2ec712a-5120-44d8-9581-285d8b866322", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [-160, 320], "parameters": {}, "typeVersion": 1}, {"id": "b16a56a5-0b0c-43cc-952c-f6db1b63d1e9", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [20, 60], "parameters": {"color": 7, "width": 199.37543798209555, "height": 420.623805972039, "content": "1] In ElevenLabs, add a voice to your [voice lab](https://elevenlabs.io/voice-lab) and copy its ID. Open this node and add the ID there"}, "typeVersion": 1}, {"id": "08e26051-58e9-42c5-b198-7854ab3e58d6", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [240, 60], "parameters": {"color": 7, "width": 212, "height": 418, "content": "2] Get your ElevenLabs API key (click your name in the bottom-left of [ElevenLabs](https://elevenlabs.io/voice-lab) and choose ‘profile’)\n\nIn this node, create a new header auth cred. Set the name to `xi-api-key` and the value to your API key"}, "typeVersion": 1}, {"id": "de6eb950-862e-472b-8776-b45e3109561a", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [480, 60], "parameters": {"color": 7, "width": 392, "height": 415, "content": "3] In the 'credential' field of this node, create a new OpenAI cred with your [OpenAI API key](https://platform.openai.com/api-keys)"}, "typeVersion": 1}, {"id": "e1d8158a-ad82-4b65-a2a8-a8f86cafd970", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-280, 20], "parameters": {"color": 7, "width": 230.39134868652621, "height": 233.3354221029769, "content": "### About\nThis workflow takes some French text, and translates it into spoken audio.\n\nIt then transcribes that audio back into text, translates it into English and generates an audio file of the English text"}, "typeVersion": 1}, {"id": "9614bb02-3b9c-4c5d-b596-8f94704cdb8b", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [0, 0], "parameters": {"color": 7, "width": 906, "height": 498, "content": "### Setup steps"}, "typeVersion": 1}, {"id": "69ebcaf8-58ab-48ba-967a-a1f0497524bb", "name": "Transcribe audio", "type": "n8n-nodes-base.httpRequest", "position": [720, 320], "parameters": {"url": "https://api.openai.com/v1/audio/transcriptions", "method": "POST", "options": {"lowercaseHeaders": false}, "sendBody": true, "contentType": "multipart-form-data", "sendHeaders": true, "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "file", "parameterType": "formBinaryData", "inputDataFieldName": "data"}, {"name": "model", "value": "whisper-1"}]}, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "multipart/form-data"}]}, "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 4.2}, {"id": "4109bccb-2bfd-454b-accb-074bd6980897", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [940, 500], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "5178a17e-737c-4ad1-8f56-95707430e892", "name": "Set ElevenLabs voice ID and text", "type": "n8n-nodes-base.set", "position": [60, 320], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c0f610ff-e200-4e55-a140-e8a4d6fa0eed", "name": "voice_id", "type": "string", "value": "Xb7hH8MSUJpSbSDYk0k2"}, {"id": "6755d8ae-e3df-465c-97ef-4f0187c31824", "name": "text", "type": "string", "value": "=Après, on a fait la sieste, <PERSON> a travaillé pour French Today et j’ai étudié un peu, et puis Camille a proposé de suivre une visite guidée de l’Abbaye de Beauport qui commençait à 17 heures. On a marché environ vingt minutes, et je m’arrêtais souvent pour prendre des photos : la baie de Pa<PERSON> est si jolie ! Mais Camille m’a dit : « Dépêche-toi Sunny ! La visite guidée commence dans cinq minutes. » <PERSON><PERSON>, j’ai bougé mes fesses et on est arrivées à l’abbaye"}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "7aafe155-28da-43f4-8305-2dc834f0d95a", "name": "Generate French Audio", "type": "n8n-nodes-base.httpRequest", "position": [300, 320], "parameters": {"url": "=https://api.elevenlabs.io/v1/text-to-speech/{{ $json.voice_id }}", "method": "POST", "options": {"response": {"response": {"responseFormat": "file"}}}, "jsonBody": "={\"text\":\"{{ $json.text }}\",\"model_id\":\"eleven_multilingual_v2\",\"voice_settings\":{\"stability\":0.5,\"similarity_boost\":0.5}}", "sendBody": true, "sendQuery": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "queryParameters": {"parameters": [{"name": "optimize_streaming_latency", "value": "1"}]}, "headerParameters": {"parameters": [{"name": "accept", "value": "audio/mpeg"}]}}, "credentials": {"httpHeaderAuth": {"id": "wXsJ55OgKMW01nWm", "name": "ElevenLabs API Key"}}, "typeVersion": 4.2}, {"id": "a8e75db9-4164-4332-bec3-01f91f40127f", "name": "Translate Text to English", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [960, 320], "parameters": {"text": "=Translate to English:\n{{ $json.text }}", "promptType": "define"}, "typeVersion": 1.5}, {"id": "57ea8bc0-a372-41f8-9b7c-6aef0350d9eb", "name": "Translate English text to speech", "type": "n8n-nodes-base.httpRequest", "position": [1320, 320], "parameters": {"url": "=https://api.elevenlabs.io/v1/text-to-speech/{{ $('Set ElevenLabs voice ID and text').first().json.voice_id }}", "method": "POST", "options": {}, "jsonBody": "={\"text\":\"{{ $json[\"text\"].replaceAll('\"', '\\\\\"').trim() }}\",\"model_id\":\"eleven_multilingual_v2\",\"voice_settings\":{\"stability\":0.5,\"similarity_boost\":0.5}}", "sendBody": true, "sendQuery": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "queryParameters": {"parameters": [{"name": "optimize_streaming_latency", "value": "1"}]}, "headerParameters": {"parameters": [{"name": "accept", "value": "audio/mpeg"}]}}, "credentials": {"httpHeaderAuth": {"id": "wXsJ55OgKMW01nWm", "name": "ElevenLabs API Key"}}, "typeVersion": 4.2}, {"id": "d6bb0022-8c51-41f1-9159-139a45457201", "name": "Add Filename", "type": "n8n-nodes-base.code", "position": [540, 320], "parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nfor (const item of $input.all()) {\n  item.binary.data.fileName = \"audio.mp3\";\n}\n\nreturn $input.all();"}, "typeVersion": 2}], "pinData": {}, "connections": {"Add Filename": {"main": [[{"node": "Transcribe audio", "type": "main", "index": 0}]]}, "Transcribe audio": {"main": [[{"node": "Translate Text to English", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Translate Text to English", "type": "ai_languageModel", "index": 0}]]}, "Generate French Audio": {"main": [[{"node": "Add Filename", "type": "main", "index": 0}]]}, "Translate Text to English": {"main": [[{"node": "Translate English text to speech", "type": "main", "index": 0}]]}, "Set ElevenLabs voice ID and text": {"main": [[{"node": "Generate French Audio", "type": "main", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "Set ElevenLabs voice ID and text", "type": "main", "index": 0}]]}}}