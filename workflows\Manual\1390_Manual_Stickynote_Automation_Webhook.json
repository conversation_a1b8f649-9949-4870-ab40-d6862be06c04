{"id": "6Yzmlp5xF6oHo1VW", "meta": {"instanceId": "173f55e6572798fa42ea9c5c92623a3c3308080d3fcd2bd784d26d855b1ce820"}, "name": "Text to Speech (OpenAI)", "tags": [], "nodes": [{"id": "938fedbd-e34c-40af-af2f-b9c669e1a6e9", "name": "When clicking \"Test workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [380, 380], "parameters": {}, "typeVersion": 1}, {"id": "1d59db5d-8fe6-4292-a221-a0d0194c6e0c", "name": "Set input text and TTS voice", "type": "n8n-nodes-base.set", "position": [760, 380], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "{\n \"input_text\": \"The quick brown fox jumped over the lazy dog.\",\n \"voice\": \"alloy\"\n}\n"}, "typeVersion": 3.2}, {"id": "9d54de1d-59b7-4c1f-9e88-13572da5292c", "name": "Send HTTP Request to OpenAI's TTS Endpoint", "type": "n8n-nodes-base.httpRequest", "position": [1120, 380], "parameters": {"url": "https://api.openai.com/v1/audio/speech", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "model", "value": "tts-1"}, {"name": "input", "value": "={{ $json.input_text }}"}, {"name": "voice", "value": "={{ $json.voice }}"}]}, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer $OPENAI_API_KEY"}]}, "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "VokTSv2Eg5m5aDg7", "name": "OpenAi account"}}, "typeVersion": 4.1}, {"id": "1ce72c9c-aa6f-4a18-9d5a-3971686a51ec", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [280, 256], "parameters": {"width": 273, "height": 339, "content": "## Workflow Trigger\nYou can replace this manual trigger with another trigger type as required by your use case."}, "typeVersion": 1}, {"id": "eb487535-5f36-465e-aeee-e9ff62373e53", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [660, 257], "parameters": {"width": 273, "height": 335, "content": "## Manually Set OpenAI TTS Configuration\n"}, "typeVersion": 1}, {"id": "36b380bd-0703-4b60-83cb-c4ad9265864d", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1020, 260], "parameters": {"width": 302, "height": 335, "content": "## Send Request to OpenAI TTS API\n"}, "typeVersion": 1}, {"id": "ff35ff28-62b5-49c8-a657-795aa916b524", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [660, 620], "parameters": {"color": 4, "width": 273, "height": 278, "content": "### Configuration Options\n- \"input_text\" is the text you would like to be turned into speech, and can be replaced with a programmatic value for your use case. Bear in mind that the maximum number of tokens per API call is 4,000.\n\n- \"voice\" is the voice used by the TTS model. The default is alloy, other options can be found here: [OpenAI TTS Docs](https://platform.openai.com/docs/guides/text-to-speech)"}, "typeVersion": 1}, {"id": "5f7ef80e-b5c8-41df-9411-525fafc2d910", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1020, 620], "parameters": {"color": 4, "width": 299, "height": 278, "content": "### Output\nThe output returned by OpenAI's TTS endpoint is a .mp3 audio file (binary).\n\n\n### Credentials\nTo use this workflow, you'll have to configure and provide a valid OpenAI credential.\n"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "19d67805-e208-4f0e-af44-c304e66e8ce8", "connections": {"Set input text and TTS voice": {"main": [[{"node": "Send HTTP Request to OpenAI's TTS Endpoint", "type": "main", "index": 0}]]}, "When clicking \"Test workflow\"": {"main": [[{"node": "Set input text and TTS voice", "type": "main", "index": 0}]]}}}