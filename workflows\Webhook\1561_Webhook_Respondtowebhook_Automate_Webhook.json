{"id": "NLOITjwt4iZK16Qq", "meta": {"instanceId": "a4bfc93e975ca233ac45ed7c9227d84cf5a2329310525917adaf3312e10d5462", "templateCredsSetupCompleted": true}, "name": "Business WhatsApp AI RAG Chatbot", "tags": [], "nodes": [{"id": "5be03c5c-e02d-4770-b0db-795dff0bf84f", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [-60, 1140], "parameters": {"options": {}, "respondWith": "text", "responseBody": "={{ $json.query['hub.challenge'] }}"}, "typeVersion": 1.1}, {"id": "8e24d1bc-8e65-4562-8cc4-4ce9c917841b", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [480, 1480], "parameters": {"text": "={{ $('Respond').item.json.body.entry[0].changes[0].value.messages[0].text.body }}", "agent": "conversationalAgent", "options": {"systemMessage": "You are an AI-powered assistant for an electronics store. Your primary goal is to assist customers by providing accurate and helpful information about products, troubleshooting tips, and general support. Use the provided knowledge base (retrieved documents) to answer questions with precision and professionalism.\n\n**Guidelines**:\n1. **Product Information**:\n   - Provide detailed descriptions of products, including specifications, features, and compatibility.\n   - Highlight key selling points and differences between similar products.\n   - Mention availability, pricing, and promotions if applicable.\n\n2. **Technical Support**:\n   - Offer step-by-step troubleshooting guides for common issues.\n   - Suggest solutions for setup, installation, or configuration problems.\n   - If the issue is complex, recommend contacting the store’s support team for further assistance.\n\n3. **Customer Service**:\n   - Respond politely and professionally to all inquiries.\n   - If a question is unclear, ask for clarification to provide the best possible answer.\n   - For order-related questions (e.g., status, returns, or cancellations), guide customers on how to proceed using the store’s systems.\n\n4. **Knowledge Base Usage**:\n   - Always reference the provided knowledge base (retrieved documents) to ensure accuracy.\n   - If the knowledge base does not contain relevant information, inform the customer and suggest alternative resources or actions.\n\n5. **Tone and Style**:\n   - Use a friendly, approachable, and professional tone.\n   - Avoid technical jargon unless the customer demonstrates familiarity with the topic.\n   - Keep responses concise but informative.\n\n**Example Interactions**:\n1. **Product Inquiry**:\n   - Customer: \"What’s the difference between the XYZ Smartwatch and the ABC Smartwatch?\"\n   - AI: \"The XYZ Smartwatch features a longer battery life (up to 7 days) and built-in GPS, while the ABC Smartwatch has a brighter AMOLED display and supports wireless charging. Both are compatible with iOS and Android devices. Would you like more details on either product?\"\n\n2. **Technical Support**:\n   - Customer: \"My wireless router isn’t connecting to the internet.\"\n   - AI: \"Please try the following steps: 1) Restart your router and modem. 2) Ensure all cables are securely connected. 3) Check if the router’s LED indicators show a stable connection. If the issue persists, you may need to reset the router to factory settings. Would you like a detailed guide for resetting your router?\"\n\n3. **Customer Service**:\n   - Customer: \"How do I return a defective product?\"\n   - AI: \"To return a defective product, please visit our Returns Portal on our website and enter your order number. You’ll receive a return label and instructions. If you need further assistance, our support team is <NAME_EMAIL>.\"\n\n**Limitations**:\n- If the question is outside the scope of the knowledge base or requires human intervention, inform the customer and provide contact details for the appropriate department.\n- Do not provide speculative or unverified information. Always rely on the knowledge base or direct the customer to official resources."}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "22fe09e5-053c-4f80-9e44-71f533492e31", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-340, 1360], "parameters": {"width": 459, "height": 485, "content": "# STEP 4\n\n## RAG System\n\n\n\n\n\n\n\n\n\n\n\n\n\n* *Respond* webhook receives various POST Requests from Meta regarding WhatsApp messages (user messages + status notifications)\n* Check if the incoming JSON contains user message\n* Echo back the text message to the user. This is a custom message, not a WhatsApp Business template message\n"}, "typeVersion": 1}, {"id": "cfed3c49-be8a-4d1a-aa3a-5e60a19c00ac", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [480, 1680], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "CDX6QM4gLYanh0P4", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "55970db5-284d-40b9-ad6f-f43b513aac45", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-620, 200], "parameters": {}, "typeVersion": 1}, {"id": "99de11b0-ab4a-49fe-977b-b3102c9ff1cf", "name": "Qdrant Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "position": [360, 320], "parameters": {"mode": "insert", "options": {}, "qdrantCollection": {"__rl": true, "mode": "list", "value": ""}}, "credentials": {"qdrantApi": {"id": "iyQ6MQiVaF3VMBmt", "name": "QdrantApi account"}}, "typeVersion": 1}, {"id": "619d2d2f-7a1e-49ba-a3ae-24bf24287dd2", "name": "Create collection", "type": "n8n-nodes-base.httpRequest", "position": [-320, 60], "parameters": {"url": "https://QDRANTURL/collections/COLLECTION", "method": "POST", "options": {}, "jsonBody": "{\n  \"filter\": {}\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "credentials": {"httpHeaderAuth": {"id": "qhny6r5ql9wwotpn", "name": "Qdrant API (Hetzner)"}}, "typeVersion": 4.2}, {"id": "b61d5d74-14d2-4488-a0d6-3f7df9745329", "name": "Refresh collection", "type": "n8n-nodes-base.httpRequest", "position": [-320, 320], "parameters": {"url": "https://QDRANTURL/collections/COLLECTION/points/delete", "method": "POST", "options": {}, "jsonBody": "{\n  \"filter\": {}\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "credentials": {"httpHeaderAuth": {"id": "qhny6r5ql9wwotpn", "name": "Qdrant API (Hetzner)"}}, "typeVersion": 4.2}, {"id": "71c8817f-f5be-4900-aecc-14d483993c4c", "name": "Get folder", "type": "n8n-nodes-base.googleDrive", "position": [-100, 320], "parameters": {"filter": {"driveId": {"__rl": true, "mode": "list", "value": "My Drive", "cachedResultUrl": "https://drive.google.com/drive/my-drive", "cachedResultName": "My Drive"}, "folderId": {"__rl": true, "mode": "id", "value": "=test-whatsapp"}}, "options": {}, "resource": "fileFolder"}, "credentials": {"googleDriveOAuth2Api": {"id": "HEy5EuZkgPZVEa9w", "name": "Google Drive account (n3w.it)"}}, "typeVersion": 3}, {"id": "c14e570d-527d-4cc2-b0c0-2406b814ffc6", "name": "Download Files", "type": "n8n-nodes-base.googleDrive", "position": [120, 320], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "options": {"googleFileConversion": {"conversion": {"docsToFormat": "text/plain"}}}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "HEy5EuZkgPZVEa9w", "name": "Google Drive account (n3w.it)"}}, "typeVersion": 3}, {"id": "7f1ffbd5-7aa0-49d3-aa11-9568ac704d6e", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [340, 520], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "CDX6QM4gLYanh0P4", "name": "OpenAi account"}}, "typeVersion": 1.1}, {"id": "bdc58292-5880-41b9-bc55-d6437f037629", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [520, 520], "parameters": {"options": {}, "dataType": "binary"}, "typeVersion": 1}, {"id": "7df52ba0-011e-44a5-b25d-a4610f903ed9", "name": "Token Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterTokenSplitter", "position": [480, 680], "parameters": {"chunkSize": 300, "chunkOverlap": 30}, "typeVersion": 1}, {"id": "b3306890-d527-44d9-bd42-2decd61b35a2", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-880, 1240], "parameters": {"color": 3, "width": 405, "height": 177, "content": "## Important!\n### Configure the webhook nodes this way:\n* Make sure that both *Verify* and *Respond* have the same URL\n* *Verify* should have GET HTTP Method\n* *Respond* should have POST HTTP Method"}, "typeVersion": 1}, {"id": "2da39c54-1596-4674-99c5-8fdac7873ea3", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-340, 900], "parameters": {"color": 5, "width": 618, "height": 392, "content": "# STEP 3\n\n## Create Webhook\n* Go to your [Meta for Developers App page](https://developers.facebook.com/apps/), navigate to the App settings\n* Add a **production webhook URL** as a new Callback URL\n* *Verify* webhook receives a GET Request and sends back a verification code\n* After that you can delete this\n"}, "typeVersion": 1}, {"id": "9c8a18df-d2a9-4d91-a799-a8ee6c5160ba", "name": "Verify", "type": "n8n-nodes-base.webhook", "position": [-300, 1140], "webhookId": "f0d2e6f6-8fda-424d-b377-0bd191343c20", "parameters": {"path": "f0d2e6f6-8fda-424d-b377-0bd191343c20", "options": {}, "responseMode": "responseNode"}, "typeVersion": 2}, {"id": "1ca39545-9ec1-489d-bcaf-f6289163d3e0", "name": "Respond", "type": "n8n-nodes-base.webhook", "position": [-320, 1520], "webhookId": "f0d2e6f6-8fda-424d-b377-0bd191343c20", "parameters": {"path": "f0d2e6f6-8fda-424d-b377-0bd191343c20", "options": {}, "httpMethod": "POST"}, "typeVersion": 2}, {"id": "02ae9009-b34b-49a5-86f2-50e681125d77", "name": "is Message?", "type": "n8n-nodes-base.if", "position": [-100, 1520], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "loose"}, "combinator": "and", "conditions": [{"id": "959fbffc-876a-4235-87be-2dedba4926cd", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.body.entry[0].changes[0].value.messages[0] }}", "rightValue": ""}]}, "looseTypeValidation": true}, "typeVersion": 2.2}, {"id": "9f866e16-cedb-4c43-ab38-e0e53703402a", "name": "Only message", "type": "n8n-nodes-base.whatsApp", "position": [200, 1620], "parameters": {"textBody": "=You can only send text messages", "operation": "send", "phoneNumberId": "***************", "requestOptions": {}, "additionalFields": {}, "recipientPhoneNumber": "={{ $('Respond').item.json.body.entry[0].changes[0].value.contacts[0].wa_id }}"}, "credentials": {"whatsAppApi": {"id": "HDUOWQXeRXMVjo0Z", "name": "WhatsApp account"}}, "typeVersion": 1}, {"id": "3867b8c8-db5f-40f6-b3ae-edf1ab732395", "name": "Send", "type": "n8n-nodes-base.whatsApp", "position": [900, 1480], "parameters": {"textBody": "={{ $json.output }}", "operation": "send", "phoneNumberId": "***************", "requestOptions": {}, "additionalFields": {}, "recipientPhoneNumber": "={{ $('Respond').item.json.body.entry[0].changes[0].value.contacts[0].wa_id }}"}, "credentials": {"whatsAppApi": {"id": "HDUOWQXeRXMVjo0Z", "name": "WhatsApp account"}}, "typeVersion": 1}, {"id": "401a8204-4cea-4bd0-9ae7-8c5c6797c586", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [640, 1720], "parameters": {}, "typeVersion": 1.3}, {"id": "ff1ebe0d-b572-4b77-ad67-351c0ec17927", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-120, 0], "parameters": {"color": 6, "width": 880, "height": 220, "content": "# STEP 1\n\n## Create Qdrant Collection\nChange:\n- QDRANTURL\n- COLLECTION"}, "typeVersion": 1}, {"id": "df7bc44c-fb7d-4bc4-bc79-245f53e17eca", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-340, 260], "parameters": {"color": 4, "width": 620, "height": 400, "content": "# STEP 2\n\n\n\n\n\n\n\n\n\n\n\n\n## Documents vectorization with Qdrant and Google Drive\nChange:\n- QDRANTURL\n- COLLECTION"}, "typeVersion": 1}, {"id": "df4f90ab-1cbb-4335-893a-0f3e2a62be04", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [440, 1360], "parameters": {"width": 380, "height": 260, "content": "## Configure AI Agent\nSet System prompt and chat model. If you want you can set any tools"}, "typeVersion": 1}, {"id": "b0928ee4-2c6a-4bc0-a013-15504f157379", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [980, 1920], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "CDX6QM4gLYanh0P4", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "16ca729c-9492-4af1-a02f-9b3e5b4ebc43", "name": "Retrive Qdrant Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "position": [620, 1940], "parameters": {"options": {}, "qdrantCollection": {"__rl": true, "mode": "id", "value": "COLLECTION"}}, "credentials": {"qdrantApi": {"id": "iyQ6MQiVaF3VMBmt", "name": "QdrantApi account"}}, "typeVersion": 1}, {"id": "c950482d-23e3-4318-a878-f80f8cfee556", "name": "Embeddings OpenAI2", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [500, 2140], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "CDX6QM4gLYanh0P4", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "46347cfc-b4e7-4627-a991-3f30c12d7f42", "name": "RAG", "type": "@n8n/n8n-nodes-langchain.toolVectorStore", "position": [840, 1700], "parameters": {"name": "company_data", "description": "Retrive data about company knowledge from vector store"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "b760b44b-24d8-41c6-8251-c7e6ddac82c1", "connections": {"RAG": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Verify": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Respond": {"main": [[{"node": "is Message?", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Send", "type": "main", "index": 0}]]}, "Get folder": {"main": [[{"node": "Download Files", "type": "main", "index": 0}]]}, "is Message?": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}], [{"node": "Only message", "type": "main", "index": 0}]]}, "Download Files": {"main": [[{"node": "Qdrant Vector Store", "type": "main", "index": 0}]]}, "Token Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Qdrant Vector Store", "type": "ai_embedding", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Embeddings OpenAI2": {"ai_embedding": [[{"node": "Retrive Qdrant Vector Store", "type": "ai_embedding", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "RAG", "type": "ai_languageModel", "index": 0}]]}, "Refresh collection": {"main": [[{"node": "Get folder", "type": "main", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Qdrant Vector Store", "type": "ai_document", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Retrive Qdrant Vector Store": {"ai_vectorStore": [[{"node": "RAG", "type": "ai_vectorStore", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Create collection", "type": "main", "index": 0}, {"node": "Refresh collection", "type": "main", "index": 0}]]}}}