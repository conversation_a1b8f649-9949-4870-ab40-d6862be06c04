{"id": "MIA4ozGH71fC3KCe", "meta": {"instanceId": "7599ed929ea25767a019b87ecbc83b90e16a268cb51892887b450656ac4518a2"}, "name": "pdf to text", "tags": [], "nodes": [{"id": "d92f690d-c84d-451d-9ab8-da6f9356e0ca", "name": "Convert PDF into Text", "type": "@custom-js/n8n-nodes-pdf-toolkit.PdfToText", "position": [-120, 100], "parameters": {}, "credentials": {"customJsApi": {"id": "h29wo2anYKdANAzm", "name": "CustomJS account"}}, "typeVersion": 1}, {"id": "420cfac7-a621-4bf3-bd34-3fee569321e4", "name": "HTML to PDF", "type": "@custom-js/n8n-nodes-pdf-toolkit.html2Pdf", "position": [-340, 100], "parameters": {"htmlInput": "<h1>Hello World</h1>"}, "credentials": {"customJsApi": {"id": "h29wo2anYKdANAzm", "name": "CustomJS account"}}, "typeVersion": 1}, {"id": "83c05ec3-1225-41d0-b5b4-f9f6be7619ea", "name": "Convert PDF into Text1", "type": "@custom-js/n8n-nodes-pdf-toolkit.PdfToText", "position": [-120, 300], "parameters": {"resource": "url", "field_name": "={{ $json.path }}"}, "credentials": {"customJsApi": {"id": "h29wo2anYKdANAzm", "name": "CustomJS account"}}, "typeVersion": 1}, {"id": "787e9369-abb5-483e-ba43-8837b5c586f9", "name": "Code", "type": "n8n-nodes-base.code", "position": [-340, 300], "parameters": {"jsCode": "return {\"json\": {\"path\": \"https://www.nlbk.niedersachsen.de/download/164891/Test-pdf_3.pdf.pdf\"}};"}, "typeVersion": 2}, {"id": "df553684-dfa8-4af4-a57b-ebbc9ef2a33f", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-560, 200], "parameters": {}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "97b60904-2b34-4a77-b171-d02f87c17134", "connections": {"Code": {"main": [[{"node": "Convert PDF into Text1", "type": "main", "index": 0}]]}, "HTML to PDF": {"main": [[{"node": "Convert PDF into Text", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "HTML to PDF", "type": "main", "index": 0}, {"node": "Code", "type": "main", "index": 0}]]}}}