{"nodes": [{"id": "fcd82fb8-4ba9-4379-96fd-4dca17a35fa3", "name": "Document", "type": "n8n-nodes-base.set", "position": [-600, 240], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "dba7b91b-17e3-4096-92aa-a6e5fe60eb55", "name": "twitch", "type": "string", "value": "YOUR-TWITCH-USERNAME"}]}}, "typeVersion": 3.4}, {"id": "5c608f47-3d94-4c87-926f-36eb5564c778", "name": "Twitch GraphQL", "type": "n8n-nodes-base.graphql", "position": [-380, 240], "parameters": {"query": "={\n  user(login: \"{{ $('Document').item.json.twitch }}\") {\n    stream {\n      id\n      viewersCount\n      title\n      type\n      game {\n        id\n      }\n    }\n  }\n}", "endpoint": "https://gql.twitch.tv/gql", "variables": "=", "requestFormat": "json", "headerParametersUi": {"parameter": [{"name": "client-id", "value": "kimne78kx3ncx6brgo4mv6wki5h1ko"}]}}, "typeVersion": 1}, {"id": "fcc08d0d-33ea-427c-bdea-2e219baa7191", "name": "Is Online", "type": "n8n-nodes-base.if", "position": [-160, 240], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "311e3b31-03e7-4763-8b4a-ebc9a18b77fd", "operator": {"type": "object", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.data.user.stream }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "95dd5830-accb-41a6-9790-d43324da1156", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-860, 240], "parameters": {}, "typeVersion": 1}, {"id": "fa6b56b3-4ed5-4a3d-a549-654e226b535e", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-680, 40], "parameters": {"content": "The document node serves as sample source for `twitch` username to check\n"}, "typeVersion": 1}, {"id": "3b151013-eebd-4f9e-99f1-71d4c1d25774", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-460, 420], "parameters": {"content": "the value of `client-id` parameter is a fixed known value used by twitch for anonymous call used in their website\n"}, "typeVersion": 1}, {"id": "39578fdc-f0b8-449f-9246-980dd181d058", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-240, 40], "parameters": {"content": "we need only to check the value of `stream` if `null` to know if the user offline. Any value will denote the user is online"}, "typeVersion": 1}], "pinData": {}, "connections": {"Document": {"main": [[{"node": "Twitch GraphQL", "type": "main", "index": 0}]]}, "Twitch GraphQL": {"main": [[{"node": "Is Online", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Document", "type": "main", "index": 0}]]}}}