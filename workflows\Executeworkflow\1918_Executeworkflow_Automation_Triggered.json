{"id": "mNbQmMNEvpiZqASG", "meta": {"instanceId": "f80e038bf7b8c99e3db7e7d6a34de2c19f0af25e5d7445b15c36d79b6e8e9e55"}, "name": "Format US Phone Number", "tags": [], "nodes": [{"id": "bf150da4-5e01-4571-a606-10a0fb25004b", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [0, 275], "parameters": {"workflowInputs": {"values": [{"name": "Phone Number", "type": "any"}]}}, "typeVersion": 1.1}, {"id": "7c560ecf-c827-413f-a115-7b6bc8f21a41", "name": "Check if first digit is valid country code", "type": "n8n-nodes-base.if", "position": [660, 275], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "4d5c838e-9b08-4466-b00d-c695fd76d66d", "operator": {"type": "number", "operation": "equals"}, "leftValue": "={{ $json['Phone Number'].toString().slice(0,1).toNumber() }}", "rightValue": 1}]}}, "typeVersion": 2.2}, {"id": "783d8fd0-2a38-41fc-87c9-b0aec9933070", "name": "Add valid country code", "type": "n8n-nodes-base.set", "position": [880, 475], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "e47a1812-f69c-4182-bed5-cf037071cd9b", "name": "Phone Number", "type": "number", "value": "=1{{ $json['Phone Number'] }}"}]}}, "typeVersion": 3.4}, {"id": "a93923f3-5d8d-4617-a63d-50b66b3b1128", "name": "Strip phone number formatting", "type": "n8n-nodes-base.set", "position": [220, 275], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "91d348df-6937-4118-8f7b-c9d386eb5c21", "name": "Phone Number", "type": "number", "value": "={{ $json['Phone Number'].match(/[0-9]+/gmi).join('') }}"}]}}, "typeVersion": 3.4}, {"id": "58d6d280-ec86-4b69-a89c-e43571ce1035", "name": "Check number of digits in phone number", "type": "n8n-nodes-base.switch", "position": [440, 254], "parameters": {"rules": {"values": [{"outputKey": "Full Number", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "66c9d1e7-dc56-4ce8-b7e4-64274feb8750", "operator": {"type": "number", "operation": "gte"}, "leftValue": "={{ $json['Phone Number'].toString().length }}", "rightValue": 11}]}, "renameOutput": true}, {"outputKey": "Number", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "2b9be422-2c4d-402a-b598-e8ab55aa5196", "operator": {"type": "number", "operation": "equals"}, "leftValue": "={{ $json['Phone Number'].toString().length }}", "rightValue": 10}]}, "renameOutput": true}, {"outputKey": "Invalid Number", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a442130a-f2f8-4399-8edb-180d3607ec9b", "operator": {"type": "number", "operation": "lt"}, "leftValue": "={{ $json['Phone Number'].toString().length }}", "rightValue": 10}]}, "renameOutput": true}, {"outputKey": "Not a Number", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "efba5af9-dfe4-47f0-8e82-253accd4f238", "operator": {"type": "number", "operation": "notExists", "singleValue": true}, "leftValue": "={{ $json['Phone Number'] }}", "rightValue": ""}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "1f6a4aa7-0595-4db3-a9c3-dc7a72656597", "name": "Format phone numbers", "type": "n8n-nodes-base.set", "position": [1100, 325], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "402c8481-3dee-4b90-8a08-7e611156d012", "name": "Phone Number (Input)", "type": "string", "value": "={{ $('When Executed by Another Workflow').item.json['Phone Number'] }}"}, {"id": "9bc193b1-664f-40c0-8545-6792b5599777", "name": "Phone Number", "type": "number", "value": "={{ $json['Phone Number'].toString().slice(0,11).toNumber() }}"}, {"id": "a4944be5-bfd5-4804-aeb5-d84c59145485", "name": "=Phone Number (E-164)", "type": "string", "value": "={{ $json['Phone Number'] ? '+' + $json['Phone Number'] : '' }}"}, {"id": "3a8d506c-45ba-4843-b186-78bf877b7903", "name": "Phone Number (National)", "type": "string", "value": "={{ $json['Phone Number'] ? '(' + $json['Phone Number'].toString().slice(1,4) + ') ' + $json['Phone Number'].toString().slice(4,7) + '-' + $json['Phone Number'].toString().slice(7,11) : '' }}"}, {"id": "14daf876-5f94-44d7-915b-bc4a8d6afbc4", "name": "Phone Number (Full National)", "type": "string", "value": "={{ $json['Phone Number'] ? '1 (' + $json['Phone Number'].toString().slice(1,4) + ') ' + $json['Phone Number'].toString().slice(4,7) + '-' + $json['Phone Number'].toString().slice(7,11) : '' }}"}, {"id": "3270cc41-bfd7-4c5d-a05e-4af8da028bd5", "name": "Phone Number (International)", "type": "string", "value": "={{ $json['Phone Number'] ? '00-1-' + $json['Phone Number'].toString().slice(1,4) + '-' + $json['Phone Number'].toString().slice(4,7) + '-' + $json['Phone Number'].toString().slice(7,11) : '' }}"}, {"id": "a6bd3652-071f-41b6-b523-bca427ef54f5", "name": "Extension", "type": "number", "value": "={{ $json['Phone Number'].toString().slice(11).toNumber() }}"}, {"id": "7b808bfb-0d69-410c-b8f4-cbf2cafcf7e8", "name": "Extension (String)", "type": "string", "value": "={{ $json['Phone Number'].toString().slice(11).toNumber() > 0 ? $json['Phone Number'].toString().slice(11).toNumber() : '' }}"}]}}, "typeVersion": 3.4}, {"id": "7bc3c1a0-2056-48a0-b826-21a8c1bff31b", "name": "Clear invalid number", "type": "n8n-nodes-base.set", "position": [880, 125], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c8d90980-61c9-49c5-8769-32c445790328", "name": "Phone Number", "type": "string", "value": ""}]}, "includeOtherFields": true}, "typeVersion": 3.4}], "active": false, "pinData": {"When Executed by Another Workflow": [{"json": {"Phone Number": "**************"}}, {"json": {"Phone Number": "1.************"}}, {"json": {"Phone Number": "************"}}, {"json": {"Phone Number": "1.************1234"}}, {"json": {"Phone Number": "1(800)555-55"}}, {"json": {"Phone Number": "5(800)555-5555"}}, {"json": {"Phone Number": "1(800)555-5555 extension 1234"}}, {"json": {"Phone Number": "A string"}}]}, "settings": {"executionOrder": "v1"}, "versionId": "1b7626d5-b32b-41bd-989a-a79616769278", "connections": {"Clear invalid number": {"main": [[{"node": "Format phone numbers", "type": "main", "index": 0}]]}, "Format phone numbers": {"main": [[]]}, "Add valid country code": {"main": [[{"node": "Format phone numbers", "type": "main", "index": 0}]]}, "Strip phone number formatting": {"main": [[{"node": "Check number of digits in phone number", "type": "main", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Strip phone number formatting", "type": "main", "index": 0}]]}, "Check number of digits in phone number": {"main": [[{"node": "Check if first digit is valid country code", "type": "main", "index": 0}], [{"node": "Add valid country code", "type": "main", "index": 0}], [{"node": "Clear invalid number", "type": "main", "index": 0}], [{"node": "Clear invalid number", "type": "main", "index": 0}]]}, "Check if first digit is valid country code": {"main": [[{"node": "Format phone numbers", "type": "main", "index": 0}], [{"node": "Clear invalid number", "type": "main", "index": 0}]]}}}