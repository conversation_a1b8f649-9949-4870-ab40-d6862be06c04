{"nodes": [{"id": "70a44436-4b51-458a-ae93-60edeed170de", "name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [240, 300], "parameters": {}, "typeVersion": 1}, {"id": "d4c2dfa2-30bb-4f06-96c2-5811472302d2", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [240, 100], "parameters": {"triggerTimes": {"item": [{"mode": "custom", "cronExpression": "15 7 * * 1-6"}]}}, "typeVersion": 1}, {"id": "********-e4f2-4795-8e80-9e49626d2c42", "name": "Baserow", "type": "n8n-nodes-base.baserow", "position": [460, 200], "parameters": {"tableId": 680, "databaseId": 146, "additionalOptions": {}}, "credentials": {"baserowApi": {"id": "37", "name": "Baserow account"}}, "typeVersion": 1}, {"id": "36f2947b-67cf-47eb-891f-e7e3b5ba9eac", "name": "Fetch tradegate stock page", "type": "n8n-nodes-base.httpRequest", "position": [680, 200], "parameters": {"url": "https://www.tradegate.de/orderbuch.php", "options": {}, "responseFormat": "string", "queryParametersUi": {"parameter": [{"name": "isin", "value": "={{$json[\"ISIN\"]}}"}]}}, "typeVersion": 1}, {"id": "b516e751-d1d1-43a0-8f19-6787a5c56ddc", "name": "Parse tradegate stock page", "type": "n8n-nodes-base.htmlExtract", "position": [900, 200], "parameters": {"options": {}, "extractionValues": {"values": [{"key": "WKN", "cssSelector": "#col1_content > table > tbody > tr:nth-child(2) > td:nth-child(1)"}, {"key": "ISIN", "cssSelector": "#col1_content > table > tbody > tr:nth-child(2) > td:nth-child(3)"}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "cssSelector": "#col1_content > table > tbody > tr:nth-child(2) > td:nth-child(4)"}, {"key": "Name", "cssSelector": "#col1_content > h2"}, {"key": "Bid", "cssSelector": "#bid"}, {"key": "Ask", "cssSelector": "#ask"}]}}, "typeVersion": 1}, {"id": "e51556c7-3f3a-4e4d-96e9-942f436422af", "name": "Build HTML", "type": "n8n-nodes-base.function", "position": [1560, 200], "parameters": {"functionCode": "const columns = Object.keys(items[0].json);\n\n// Define the basic table structure\nlet table_header = `<table style=\"border: 1px solid black; border-collapse: collapse;\"><tr>${columns.map(e => '<th style=\"border: 1px solid black; border-collapse: collapse;\">' + e + '</th>').join('')}</tr>`;\nlet table_content = \"\";\nlet table_footer = '</table>';\n\n// Add content to our table\nfor (item of items) {\n    table_content += '<tr>'\n    for (column of columns) {\n        table_content += `<td style=\\\"border: 1px solid black; border-collapse: collapse;\\\">${item.json[column]}</td>`\n    }\n    table_content += '</tr>'\n}\n\n// Prepare HTML email body\nconst email_html = `<body style=\"font-family: Sans-Serif;\">\n<p>Investments as of ${$now.setZone(\"Europe/Dublin\").setLocale('ie').toFormat('fff')}:</p>\n${table_header}\n${table_content}\n${table_footer}\n<p>Total: ${items.map(e => parseFloat(e.json['Current Value'])).reduce((a, b) => a + b, 0).toFixed(2)}</p>\n<p><small>Workflow #${$workflow.id}</small></p>\n</body>`\n\n\nreturn [{\n    json: {\n        html: email_html\n    }\n}];"}, "typeVersion": 1}, {"id": "361bf8f2-298c-4b96-9f21-4f4620f1e9a9", "name": "Format result", "type": "n8n-nodes-base.set", "position": [1120, 200], "parameters": {"values": {"string": [{"name": "Name", "value": "={{ $node[\"Baserow\"].json[\"Name\"] }}"}, {"name": "ISIN", "value": "={{ $node[\"Baserow\"].json[\"ISIN\"] }}"}, {"name": "Count", "value": "={{ $node[\"Baserow\"].json[\"Count\"] }}"}, {"name": "Purchase Price", "value": "={{ $node[\"Baserow\"].json[\"Purchase Price\"] }}"}, {"name": "Current Value", "value": "={{ (parseFloat($json[\"Bid\"].replace(',', '.')) * parseFloat($node[\"Baserow\"].json[\"Count\"])).toFixed(2) }}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"id": "c2f329dc-3b97-402a-9d63-ed863c2aee84", "name": "Calculate change", "type": "n8n-nodes-base.set", "position": [1340, 200], "parameters": {"values": {"string": [{"name": "Change", "value": "={{ ( parseFloat($json[\"Current Value\"]) - parseFloat($json[\"Purchase Price\"]) ).toFixed(2) }}"}, {"name": "Change (%)", "value": "={{ ( ( ( parseFloat($json[\"Current Value\"]) - parseFloat($json[\"Purchase Price\"]) ) / parseFloat($json[\"Purchase Price\"]) ) * 100).toFixed(2) }}"}]}, "options": {}}, "typeVersion": 1}, {"id": "e0876374-c9f3-4253-8764-9aa78faa2193", "name": "SendGrid", "type": "n8n-nodes-base.sendGrid", "position": [1780, 200], "parameters": {"subject": "Investment report", "toEmail": "<EMAIL>", "resource": "mail", "fromEmail": "<EMAIL>", "contentType": "text/html", "contentValue": "={{ $json[\"html\"] }}", "additionalFields": {}}, "credentials": {"sendGridApi": {"id": "143", "name": "SendGrid account"}}, "typeVersion": 1}], "connections": {"Cron": {"main": [[{"node": "Baserow", "type": "main", "index": 0}]]}, "Baserow": {"main": [[{"node": "Fetch tradegate stock page", "type": "main", "index": 0}]]}, "Build HTML": {"main": [[{"node": "SendGrid", "type": "main", "index": 0}]]}, "Format result": {"main": [[{"node": "Calculate change", "type": "main", "index": 0}]]}, "Calculate change": {"main": [[{"node": "Build HTML", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Baserow", "type": "main", "index": 0}]]}, "Fetch tradegate stock page": {"main": [[{"node": "Parse tradegate stock page", "type": "main", "index": 0}]]}, "Parse tradegate stock page": {"main": [[{"node": "Format result", "type": "main", "index": 0}]]}}}