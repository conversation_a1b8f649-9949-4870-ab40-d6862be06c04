{"id": "AnbedV2Ntx97sfed", "meta": {"instanceId": "885b4fb4a6a9c2cb5621429a7b972df0d05bb724c20ac7dac7171b62f1c7ef40", "templateCredsSetupCompleted": true}, "name": "Extract & Summarize Bing Copilot Search Results with Gemini AI and Bright Data", "tags": [{"id": "Kujft2FOjmOVQAmJ", "name": "Engineering", "createdAt": "2025-04-09T01:31:00.558Z", "updatedAt": "2025-04-09T01:31:00.558Z"}, {"id": "ddPkw7Hg5dZhQu2w", "name": "AI", "createdAt": "2025-04-13T05:38:08.053Z", "updatedAt": "2025-04-13T05:38:08.053Z"}], "nodes": [{"id": "5f358132-63bd-4c66-80da-4fb9911f607f", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-1140, 400], "parameters": {}, "typeVersion": 1}, {"id": "43a157f6-2fb8-4c90-bf5d-92fc64c9df10", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "notes": "Gemini Experimental Model", "position": [760, 580], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-thinking-exp-01-21"}, "credentials": {"googlePalmApi": {"id": "YeO7dHZnuGBVQKVZ", "name": "Google Gemini(PaLM) Api account"}}, "notesInFlow": true, "typeVersion": 1}, {"id": "f2d34617-ea34-4163-b9d5-a35fed807dbb", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [940, 580], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "707fdb4a-f534-4984-b97d-1839db1afc03", "name": "Recursive Character Text Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "position": [1040, 800], "parameters": {"options": {}, "chunkOverlap": 100}, "typeVersion": 1}, {"id": "0440b1dd-ca72-467c-a27a-76609ae08fcf", "name": "If", "type": "n8n-nodes-base.if", "position": [-220, 400], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "6a7e5360-4cb5-4806-892e-5c85037fa71c", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $('Check Snapshot Status').item.json.status }}", "rightValue": "ready"}]}}, "typeVersion": 2.2}, {"id": "a23f3c86-200a-4d3c-a762-51cce158c4dd", "name": "Set Snapshot Id", "type": "n8n-nodes-base.set", "position": [-700, 400], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "2c3369c6-9206-45d7-9349-f577baeaf189", "name": "snapshot_id", "type": "string", "value": "={{ $json.snapshot_id }}"}]}}, "typeVersion": 3.4}, {"id": "cee238ff-f725-4a24-8117-540be1c66a56", "name": "Download Snapshot", "type": "n8n-nodes-base.httpRequest", "position": [140, 200], "parameters": {"url": "=https://api.brightdata.com/datasets/v3/snapshot/{{ $json.snapshot_id }}", "options": {"timeout": 10000}, "sendQuery": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "queryParameters": {"parameters": [{"name": "format", "value": "json"}]}}, "credentials": {"httpHeaderAuth": {"id": "kdbqXuxIR8qIxF7y", "name": "Header Auth account"}}, "typeVersion": 4.2}, {"id": "6bb33d11-7176-4dc7-89fe-1ee794793d3e", "name": "Google Gemini Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [380, 380], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "YeO7dHZnuGBVQKVZ", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "b2309938-eaaf-4d63-b8c8-53666cd57dac", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [540, 380], "parameters": {"jsonSchemaExample": "[{\n  \"city\": \"string\",\n  \"hotels\": [\n    {\n      \"name\": \"string\",\n      \"address\": \"string\",\n      \"description\": \"string\",\n      \"website\": \"string\",\n      \"area\": \"string (optional)\"\n    }\n  ]\n}\n]\n"}, "typeVersion": 1.2}, {"id": "747b1e50-1cae-4efb-86d3-9221438701cd", "name": "Check on the errors", "type": "n8n-nodes-base.if", "position": [-20, 20], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "b267071c-7102-407b-a98d-f613bcb1a106", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.errors.toString() }}", "rightValue": "0"}]}}, "typeVersion": 2.2}, {"id": "0bf63795-1f1d-4d6b-90c1-1effae83fd40", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1140, 80], "parameters": {"width": 400, "height": 220, "content": "## Note\n\nDeals with the Bing Copilot Search using the Bright Data Web Scraper API.\n\nThe Basic LLM Chain and summarization is done to demonstrate the usage of the N8N AI capabilities.\n\n**Please make sure to update the Webhook Notification URL**"}, "typeVersion": 1}, {"id": "3872fb7a-382a-446d-8cb0-6ac5a282a801", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-620, 80], "parameters": {"width": 420, "height": 220, "content": "## LLM Usages\n\nGoogle Gemini Flash Exp model is being used.\n\nBasic LLM Chain makes use of the Output formatter for formatting the response\n\nSummarization Chain is being used for summarization of the content"}, "typeVersion": 1}, {"id": "a1453c72-fef3-4cec-967a-858b28ba31d8", "name": "Check Snapshot Status", "type": "n8n-nodes-base.httpRequest", "position": [-460, 400], "parameters": {"url": "=https://api.brightdata.com/datasets/v3/progress/{{ $json.snapshot_id }}", "options": {}, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "kdbqXuxIR8qIxF7y", "name": "Header Auth account"}}, "typeVersion": 4.2}, {"id": "5750853b-a07d-455e-b630-977dd733613e", "name": "Structured Data Extractor", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [360, 200], "parameters": {"text": "=Extract the content as a structured JSON.\n\nHere's the content - {{ $json.answer_text }}", "messages": {"messageValues": [{"message": "You are an expert data formatter"}]}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.6}, {"id": "a86f935f-fe57-40ea-9197-5f20e3002899", "name": "Concise Summary Creator", "type": "@n8n/n8n-nodes-langchain.chainSummarization", "position": [760, 200], "parameters": {"options": {"summarizationMethodAndPrompts": {"values": {"prompt": "=Write a concise summary of the following:\n\n\n{{ $('Download Snapshot').item.json.answer_text }}\n\n", "combineMapPrompt": "=Write a concise summary of the following:\n\n\n\n\n\nCONCISE SUMMARY: {{ $('Download Snapshot').item.json.answer_text }}"}}}, "operationMode": "documentLoader"}, "typeVersion": 2}, {"id": "848ce4b1-0aed-4af2-bf55-bcdb30bbc88a", "name": "Wait for 30 seconds", "type": "n8n-nodes-base.wait", "position": [-280, 660], "webhookId": "f2aafd71-61f2-4aa4-8290-fa3bbe3d46b9", "parameters": {"amount": 30}, "typeVersion": 1.1}, {"id": "5467a870-0734-457b-909e-be425a432ebf", "name": "Structured Data Webhook Notifier", "type": "n8n-nodes-base.httpRequest", "position": [760, 0], "parameters": {"url": "https://webhook.site/bc804ce5-4a45-4177-a68a-99c80e5c86e6", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "response", "value": "={{ $json.output }}"}]}}, "typeVersion": 4.2}, {"id": "bf8a4868-ead7-411e-97ba-9faea308d836", "name": "Summary Webhook Notifier", "type": "n8n-nodes-base.httpRequest", "position": [1140, 200], "parameters": {"url": "https://webhook.site/bc804ce5-4a45-4177-a68a-99c80e5c86e6", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "response", "value": "={{ $json.output }}"}]}}, "typeVersion": 4.2}, {"id": "60a59b93-9a7c-4d22-ab66-2249fb9ed27e", "name": "Perform a Bing Copilot Request", "type": "n8n-nodes-base.httpRequest", "position": [-920, 400], "parameters": {"url": "https://api.brightdata.com/datasets/v3/trigger", "method": "POST", "options": {}, "jsonBody": "[\n  {\n    \"url\": \"https://copilot.microsoft.com/chats\",\n    \"prompt\": \"Top hotels in New York\"\n  }\n]", "sendBody": true, "sendQuery": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "queryParameters": {"parameters": [{"name": "dataset_id", "value": "gd_m7di5jy6s9geokz8w"}, {"name": "include_errors", "value": "true"}]}, "headerParameters": {"parameters": [{}]}}, "credentials": {"httpHeaderAuth": {"id": "kdbqXuxIR8qIxF7y", "name": "Header Auth account"}}, "typeVersion": 4.2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "4462ae6e-4ecd-4f64-aad8-4aa9e65982b6", "connections": {"If": {"main": [[{"node": "Check on the errors", "type": "main", "index": 0}], [{"node": "Wait for 30 seconds", "type": "main", "index": 0}]]}, "Set Snapshot Id": {"main": [[{"node": "Check Snapshot Status", "type": "main", "index": 0}]]}, "Download Snapshot": {"main": [[{"node": "Structured Data Extractor", "type": "main", "index": 0}]]}, "Check on the errors": {"main": [[{"node": "Download Snapshot", "type": "main", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Concise Summary Creator", "type": "ai_document", "index": 0}]]}, "Wait for 30 seconds": {"main": [[{"node": "Check Snapshot Status", "type": "main", "index": 0}]]}, "Check Snapshot Status": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Concise Summary Creator": {"main": [[{"node": "Summary Webhook Notifier", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Concise Summary Creator", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Structured Data Extractor", "type": "ai_outputParser", "index": 0}]]}, "Google Gemini Chat Model1": {"ai_languageModel": [[{"node": "Structured Data Extractor", "type": "ai_languageModel", "index": 0}]]}, "Structured Data Extractor": {"main": [[{"node": "Concise Summary Creator", "type": "main", "index": 0}, {"node": "Structured Data Webhook Notifier", "type": "main", "index": 0}]]}, "Perform a Bing Copilot Request": {"main": [[{"node": "Set Snapshot Id", "type": "main", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Perform a Bing Copilot Request", "type": "main", "index": 0}]]}}}