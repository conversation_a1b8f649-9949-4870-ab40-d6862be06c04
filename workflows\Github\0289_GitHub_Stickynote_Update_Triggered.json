{"meta": {"instanceId": "a2434c94d549548a685cca39cc4614698e94f527bcea84eefa363f1037ae14cd"}, "nodes": [{"id": "161c2837-6a3c-4492-93d0-c094b8788362", "name": "On any update in repository", "type": "n8n-nodes-base.githubTrigger", "position": [620, 520], "webhookId": "9f16fefe-dacf-48b8-a576-48ed0599e911", "parameters": {"owner": "dummy<PERSON><PERSON>", "events": ["*"], "repository": "DemoRepo"}, "credentials": {"githubApi": {"id": "20", "name": "[UPDATE ME]"}}, "typeVersion": 1}, {"id": "2703e869-60e0-4906-9fd2-35a5e54aae1f", "name": "Turn a light red", "type": "n8n-nodes-base.homeAssistant", "position": [840, 520], "parameters": {"domain": "light", "service": "turn_on", "resource": "service", "operation": "call", "serviceAttributes": {"attributes": [{"name": "entity_id", "value": "light.lamp"}, {"name": "rgb_color", "value": "={{[255,0,0]}}"}]}}, "credentials": {"homeAssistantApi": {"id": "21", "name": "home.david<PERSON>.me"}}, "typeVersion": 1}, {"id": "bbbd01eb-9409-414e-bc85-c615add05580", "name": "Note", "type": "n8n-nodes-base.stickyNote", "position": [160, 420], "parameters": {"width": 378, "height": 351, "content": "## Turn on a light to a specific color on any update in GitHub repository\nThis workflow turns a light red when an update is made to a GitHub repository. By default, updates include pull requests, issues, pushes just to name a few.\n\n### How it works\n1. Triggers off on the `On any update in repository` node.\n2. Uses Home Assistant to turn on a light and then configure the light to turn red."}, "typeVersion": 1}, {"id": "33dfde3b-a4b5-468d-8d13-9d3577563f9b", "name": "Note1", "type": "n8n-nodes-base.stickyNote", "position": [840, 700], "parameters": {"width": 315, "height": 248, "content": "### Configure light here\nIt is likely the name of the light that you want to turn a specific colour is not called `light.lamp`. In which case, please head to your Home Assistant instance and find the light taking note of it's `entity_id`. See discussion [here](https://community.home-assistant.io/t/find-the-entity-id-of-a-yeelight-light-in-manual-mode-or-automatic-mode-doesnt-work/165557) for help.\n\nIf you would also like to change the colour the light turns to, do so with an [RGB color picker](https://www.google.com/search?q=rgb+color+picker&oq=rgb+colo&aqs=chrome.0.0i67i433j69i57j0i67l4j0i512l4.6248j0j7&sourceid=chrome&ie=UTF-8). Default colour is red (255,0,0)."}, "typeVersion": 1}], "connections": {"On any update in repository": {"main": [[{"node": "Turn a light red", "type": "main", "index": 0}]]}}}