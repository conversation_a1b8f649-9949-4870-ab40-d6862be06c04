{"id": "hKkZYhJqBNir8amQ", "meta": {"instanceId": "a943fc71a4dfb51cc3424882233bcd72e7a73857958af1cf464f7c21580c726e"}, "name": "🎥 Gemini AI Video Analysis", "tags": [{"id": "bjzc8PEM2FgX8rUa", "name": "Marketing", "createdAt": "2025-04-18T13:34:48.192Z", "updatedAt": "2025-04-18T13:34:48.192Z"}, {"id": "OiWw6VmsJz6ZBAzz", "name": "AI", "createdAt": "2025-04-25T09:59:58.961Z", "updatedAt": "2025-04-25T09:59:58.961Z"}], "nodes": [{"id": "f5c9faf8-441a-49ef-a0de-0daa08c3bbfa", "name": "Wait", "type": "n8n-nodes-base.wait", "position": [400, 160], "webhookId": "7d0cd0c0-ce85-4372-b7a5-b0be061fc2b9", "parameters": {}, "typeVersion": 1.1}, {"id": "c0336074-a30f-4fc0-aa57-7142cea1a3da", "name": "Download video", "type": "n8n-nodes-base.httpRequest", "position": [-40, 160], "parameters": {"url": "={{ $json.video_url }}", "options": {}}, "typeVersion": 4.2}, {"id": "8b04c774-7a02-43ff-bac9-d19a427d514e", "name": "Upload video Gemini", "type": "n8n-nodes-base.httpRequest", "position": [180, 160], "parameters": {"url": "=https://generativelanguage.googleapis.com/upload/v1beta/files?key={{ $vars.<PERSON>Key }}", "method": "POST", "options": {}, "sendBody": true, "contentType": "binaryData", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Goog-Upload-Command", "value": "start, upload, finalize"}, {"name": "X-Goog-Upload-Header-Content-Length", "value": "={{ $binary.data.fileSize }}"}, {"name": "X-Goog-Upload-Header-Content-Type", "value": "=video/{{ $binary.data.fileExtension }}"}, {"name": "Content-Type", "value": "video/mp4"}]}, "inputDataFieldName": "=data"}, "typeVersion": 4.2}, {"id": "eacf4317-18bc-441a-a415-488b65bb9545", "name": "Analyze video Gemini", "type": "n8n-nodes-base.httpRequest", "position": [620, 160], "parameters": {"url": "=https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent?key={{ $vars.GeminiKey }}", "method": "POST", "options": {}, "jsonBody": "={\n    \"contents\": [\n        {\n            \"role\": \"user\",\n            \"parts\": [\n                {\n                    \"fileData\": {\n                        \"fileUri\": \"{{ $json.file.uri }}\",\n                        \"mimeType\": \"{{ $json.file.mimeType }}\"\n                    }\n                },\n                {\n                    \"text\": \"Describe in detail what is visually happening in the video, including key elements, actions, colors, and branding. Note the style, tone, and any notable creative techniques being used.\"\n                }\n            ]\n        }\n    ],\n    \"generationConfig\": {\n        \"temperature\": 1.4,\n        \"topK\": 40,\n        \"topP\": 0.95,\n        \"maxOutputTokens\": 8192,\n        \"responseModalities\": [\"Text\"]\n    }\n}\n", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "typeVersion": 4.2}, {"id": "ff204f3f-947e-4b6a-a9a3-822d6d57064b", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-480, 160], "parameters": {}, "typeVersion": 1}, {"id": "d842a85d-121d-46ed-9df7-44d2c7849c03", "name": "Set Input", "type": "n8n-nodes-base.set", "position": [-260, 160], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "6e1728e0-4749-47b9-92ae-4d1c0b7008c8", "name": "video_url", "type": "string", "value": "https://video-gru2-1.xx.fbcdn.net/v/t42.1790-2/469342405_958689216107669_4819692307529683812_n.mp4?_nc_cat=109&ccb=1-7&_nc_sid=c53f8f&_nc_ohc=DMM4-vR_LwoQ7kNvwGFIAOW&_nc_oc=AdkqAUzPHupjN-yAD8AGHbbnsMLQptad7NFTL-fuRa3Kq12boE6Ar_elagnzmgR87uU&_nc_zt=28&_nc_ht=video-gru2-1.xx&_nc_gid=ikICtUIUUCoHz775L2uRBw&oh=00_AfHlScWo8zXllEsqzl3wabxNva8z_qiFuA2g-hWzvnlVdg&oe=681596F3"}]}}, "typeVersion": 3.4}, {"id": "efb6ed9b-5f65-4bf3-8ea9-00430abdb247", "name": "Get Result", "type": "n8n-nodes-base.set", "position": [840, 160], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "1ea390b9-3371-4a3a-8741-bd6ec74dc64b", "name": "videoDescription", "type": "string", "value": "={{ $json.candidates[0].content.parts[0].text }}"}]}}, "typeVersion": 3.4}, {"id": "fc917016-b2f3-4d69-8924-0aa16b4b43bc", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-920, -380], "parameters": {"color": 7, "width": 560, "height": 520, "content": "## Video Analysis with Gemini AI\n\nThis workflow demonstrates how to analyze video content using Google's Gemini 2.0 Flash API:\n1. Download a video from a URL\n2. Upload it to Gemini's servers\n3. Process the video with AI to generate a detailed description\n4. Extract the analysis results\n\nUse cases: Content moderation, video cataloging, accessibility features, etc.\n\nOUTPUT: The workflow produces a detailed text description of the video content in the \"MediaDescription\" variable.\nYou can use this data for content tagging, searchable descriptions, accessibility, moderation, or cataloging.\n\n⚙️ **Before using this workflow**, make sure to set the `GeminiKey` environment variable with your Gemini API key.  \nThis ensures your API key is securely managed and not hardcoded in the workflow.\n\n__SECURITY NOTE__: This workflow contains an API key in the workflow data.  \nFor production use, store your API keys in Credentials or use environment variables (like `GeminiKey`) instead of hardcoding them.\n"}, "typeVersion": 1}, {"id": "8854f039-f23f-4174-8821-7acfbc5ecfab", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-320, 20], "parameters": {"color": 5, "width": 220, "height": 300, "content": "## Configuration\nDefine the video URL you want to analyze.\n"}, "typeVersion": 1}, {"id": "189ede99-f80f-4f41-8481-c9ba518fd0e7", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-70, -140], "parameters": {"color": 4, "width": 820, "height": 460, "content": "## Video Processing Pipeline\n\nThis section handles the complete video processing workflow:\n\n1. DOWNLOAD: First, we fetch the video from the provided URL, converting it to binary data that Gemini can process\n\n2. UPLOAD: Next, we send the binary video data to Gemini's servers where it's stored temporarily for AI processing\n\n3. ANALYZE: Finally, we request Gemini's AI to analyze the video content. You can customize the prompt in the \"Analyze video Gemini\" node to focus on specific aspects of the video content you're interested in\n\nThe Wait node ensures the video is fully processed before analysis begins.\n"}, "typeVersion": 1}], "active": false, "pinData": {"Upload video Gemini": [{"json": {"file": {"uri": "https://generativelanguage.googleapis.com/v1beta/files/7whopq8rwtt8", "name": "files/7whopq8rwtt8", "state": "PROCESSING", "source": "UPLOADED", "mimeType": "video/mp4", "sizeBytes": "933141", "createTime": "2025-04-28T18:12:40.864881Z", "sha256Hash": "MWQwYmQ2YWViYmRiNDNjZTYyY2I2ODhkOWRlNzdlMzkyZDJkMTU0NTM5NTE1OWM2MTJlMWRiNTNhNTIyZDVmZA==", "updateTime": "2025-04-28T18:12:40.864881Z", "expirationTime": "2025-04-30T18:12:40.834671218Z"}}}], "Analyze video Gemini": [{"json": {"candidates": [{"content": {"role": "model", "parts": [{"text": "Okay, here's a detailed description of the video:\n\n**Overview**\nThe video is a promotional piece for Advanced Sim Racing, featuring a high-end BMW-branded racing simulator setup at a BMW dealership event. It also highlights the presence of a well-known personality: <PERSON> (GSP). The video mixes detailed close-ups of the equipment, shots of people interacting with the simulator, and branding elements.\n\n**Detailed Description:**\n\n*   **0:00-0:01:** Opens with a dynamic shot of someone using the advanced racing simulator. We see triple monitors displaying a race, and the participant grips the steering wheel with intent. The seat is black, with \"OMP\" branded at the top, \"ADVANCED Sim Racing\" and a large BMW logo prominently displayed on its back.\n\n*   **0:01-0:03:** Tight focus on the steering wheel, emphasizing its sophisticated design and multiple buttons.  We see the brand name \"OMP\" on the wheel itself, while the backboard is adorned with the company’s slogan, “ADVANCED Sim Racing”.\n\n*   **0:03-0:07:** We shift to show the simulator being used at a BMW dealership.  A group of people gathers around with the hood open of a BMW car, seemingly intrigued and looking at the person experiencing the simulator.\n\n*   **0:07-0:11:** <PERSON> appears among the crowd.\n\n*   **0:11-0:13:** Close-up of the SimuCube motor\n\n*   **0:13-0:15:** <PERSON> gets onto the simulator while people assist him.\n\n*   **0:15-0:21:** Focus on the haptic feedback engine and the back of an employee’s t-shirt that says “ADVANCED Sim Racing DBOX SIMUCUBE”\n\n*   **0:22-0:24:** Small lego replica of the BMW 3 series next to one of the monitor’s along with the car’s front side view with distinct headlights.\n\n*   **0:24-0:25:** The outside of the event, labeled BMW Laval with the BMW logo, ice cream booth nearby.\n\n*   **0:25-0:27:** More participants getting on the simulator\n\n*   **0:27-0:29:** The ranking for different drivers\n\n*   **0:29-0:33:** View of the racing pedals and the race seat with the “OMP” brand.\n\n**Key Elements & Equipment**\n*   **Sim Racing Rig:** A highly advanced racing simulator is central to the video.  It is equipped with a racing seat, steering wheel, pedals, multiple monitors and a SimuCube haptic engine for feedback\n*   **BMW Branding:** The presence of the BMW logo and the incorporation of BMW vehicles underscore the affiliation.\n\n**Style & Tone:**\n*   **High-Tech:**  The video conveys a sense of sophisticated technology and realistic immersion in simulated racing.\n*   **Promotional/Enthusiastic:**  The music and the editing suggest excitement and interest in the product.\n*   **Stylish:** Use of slow-motion shots, close-ups and selective focus adds a modern, visually appealing look.\n\nI hope this helps!"}]}, "avgLogprobs": -1.1206862455033986, "finishReason": "STOP"}], "modelVersion": "gemini-2.0-flash-exp", "usageMetadata": {"totalTokenCount": 10149, "promptTokenCount": 9441, "promptTokensDetails": [{"modality": "TEXT", "tokenCount": 36}, {"modality": "AUDIO", "tokenCount": 825}, {"modality": "VIDEO", "tokenCount": 8580}], "candidatesTokenCount": 708, "candidatesTokensDetails": [{"modality": "TEXT", "tokenCount": 708}]}}}]}, "settings": {"executionOrder": "v1"}, "versionId": "5f8d8d62-091e-4883-bbb0-8087ebe7b501", "connections": {"Wait": {"main": [[{"node": "Analyze video Gemini", "type": "main", "index": 0}]]}, "Set Input": {"main": [[{"node": "Download video", "type": "main", "index": 0}]]}, "Download video": {"main": [[{"node": "Upload video Gemini", "type": "main", "index": 0}]]}, "Upload video Gemini": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Analyze video Gemini": {"main": [[{"node": "Get Result", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set Input", "type": "main", "index": 0}]]}}}