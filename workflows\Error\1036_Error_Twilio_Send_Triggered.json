{"id": "56", "name": "Send an SMS when a workflow fails", "nodes": [{"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.errorTrigger", "position": [550, 260], "parameters": {}, "typeVersion": 1}, {"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.twilio", "position": [750, 260], "parameters": {"to": "", "from": "", "message": "=Your workflow with ID: {{$node[\"Error Trigger\"].json[\"workflow\"][\"id\"]}} and name: {{$node[\"Error Trigger\"].json[\"workflow\"][\"name\"]}} failed to execute."}, "credentials": {"twilioApi": "twilio-credentials"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Error Trigger": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}}}