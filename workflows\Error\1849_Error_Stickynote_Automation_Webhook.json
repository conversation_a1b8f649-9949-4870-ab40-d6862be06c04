{"id": "ePnGZtZ8zLcf1UZZ", "meta": {"instanceId": "558d88703fb65b2d0e44613bc35916258b0f0bf983c5d4730c00c424b77ca36a", "templateCredsSetupCompleted": true}, "name": "n8n Error Report to Line", "tags": [{"id": "0xpEHcJjNRRRMtEj", "name": "lin", "createdAt": "2025-03-12T05:06:24.112Z", "updatedAt": "2025-03-12T05:06:24.112Z"}, {"id": "U1ozjO3iXQZWUyfG", "name": "_Blueprint", "createdAt": "2025-03-12T06:24:40.268Z", "updatedAt": "2025-03-12T06:24:40.268Z"}], "nodes": [{"id": "c45a01a5-289b-4927-8bba-4bb1029a7129", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.errorTrigger", "position": [-240, -80], "parameters": {}, "typeVersion": 1}, {"id": "1e3f7a7e-8be4-4fec-973f-befb477e6957", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [40, -80], "parameters": {"url": "https://api.line.me/v2/bot/message/push", "method": "POST", "options": {}, "jsonBody": "={\n    \"to\": \"<UID HERE>\",\n    \"messages\":[\n        {\n            \"type\":\"text\",\n            \"text\":\"🚨Your n8n flow is dead.🚨\\n\\nName: {{ $json.workflow.name }} \\nURL: {{ $json.execution.url }}\"\n        }\n    ]\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "lKd3b2nc8uNJ148Z", "name": "Line @271dudsw MiniBear"}}, "typeVersion": 4.2}, {"id": "5bcf04cc-2c3e-4e37-a134-fcc42326afc3", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-340, -400], "parameters": {"width": 660, "content": "## Error Handling\n\nYou can set this workflow as error workflow\n\nhttps://docs.n8n.io/flow-logic/error-handling/#create-and-set-an-error-workflow"}, "typeVersion": 1}, {"id": "22b66275-e111-45c8-b7bc-b6c03b55fd02", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-300, -220], "parameters": {"color": 5, "height": 300, "content": "## Error Trigger\n\nThis flow will get trigger when the error occur. You can set only one error flow for all your flows.\n"}, "typeVersion": 1}, {"id": "5a2c1b3b-2546-47e6-bb9f-b9b8d7c63d65", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-40, -220], "parameters": {"color": 4, "width": 320, "height": 300, "content": "## Send Line Message\n\nTo send message to notify you via Line Account -- Please replace <UID HERE> with your own UID\n"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "4a774ee1-96b8-4a81-858d-6c5b0d24ba03", "connections": {"Error Trigger": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}}}