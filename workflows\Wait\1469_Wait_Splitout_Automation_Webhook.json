{"id": "VlCgU5K9SYQbdxTa", "meta": {"instanceId": "d868e3d040e7bda892c81b17cf446053ea25d2556fcef89cbe19dd61a3e876e9"}, "name": "Content to 9:16 Aspect Image Generator v1", "tags": [{"id": "QsH2EXuw2e7YCv0K", "name": "OpenAI", "createdAt": "2024-11-15T04:05:20.872Z", "updatedAt": "2024-11-15T04:05:20.872Z"}, {"id": "04PL2irdWYmF2Dg3", "name": "RunwayML", "createdAt": "2024-11-15T05:55:30.783Z", "updatedAt": "2024-11-15T05:55:30.783Z"}, {"id": "yrY6updwSCXMsT0z", "name": "Video", "createdAt": "2024-11-15T05:55:34.333Z", "updatedAt": "2024-11-15T05:55:34.333Z"}, {"id": "lvPj9rYRsKOHCi4J", "name": "Creatomate", "createdAt": "2024-11-19T15:59:16.134Z", "updatedAt": "2024-11-19T15:59:16.134Z"}, {"id": "9LXACqpQLNtrM6or", "name": "Leonardo", "createdAt": "2024-11-19T15:59:21.368Z", "updatedAt": "2024-11-19T15:59:21.368Z"}, {"id": "2DYOnQD6moK2E2VF", "name": "App 2", "createdAt": "2024-12-19T04:43:15.771Z", "updatedAt": "2024-12-19T04:43:15.771Z"}], "nodes": [{"id": "be5c3e43-cc86-4081-aa98-e7af3d22267d", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [200, -960], "parameters": {}, "typeVersion": 1}, {"id": "28f70c3a-bc45-4f43-80a6-69b592c8ce2e", "name": "Sticky Note20", "type": "n8n-nodes-base.stickyNote", "position": [-180, -1200], "parameters": {"color": 6, "width": 290, "height": 1110, "content": "# AlexK1919 \n![<PERSON>](https://media.licdn.com/dms/image/v2/D5603AQFOYMkqCPl6Sw/profile-displayphoto-shrink_400_400/profile-displayphoto-shrink_400_400/0/1718309808352?e=1736985600&v=beta&t=pQKm7lQfUU1ytuC2Gq1PRxNY-XmROFWbo-BjzUPxWOs)\n\n#### I’m <PERSON>, an AI-Native Workflow Automation Architect Building Solutions to Optimize your Personal and Professional Life.\n\n### Example AirTable Base\nhttps://airtable.com/appRDq3E42JNtruIP/shrnc9EzlxpCq7Vxe\n\n### Link to my n8n Workflow Templates\nhttps://n8n.io/creators/alexk1919\n\n### Workflow Overview Video\nhttps://www.youtube.com/@alexk1919_\n\n### Products Used\n[AirTable](https://airtable.com)\n[OpenAI](https://openai.com/)\n[Leonardo.ai](https://app.leonardo.ai/?via=alexk1919)\n\n### About Me\nhttps://beacons.ai/alexk1919\n"}, "typeVersion": 1}, {"id": "334044d8-e9a6-497e-9a11-63134233c8fa", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [140, -1200], "parameters": {"color": 7, "width": 247, "height": 1111, "content": "# Triggers"}, "typeVersion": 1}, {"id": "fcdf5c1a-3ddd-44cf-9b6d-a9afdd1fc256", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [420, -1200], "parameters": {"color": 3, "width": 427, "height": 1111, "content": "# 1. Retrieve Brand Guidelines"}, "typeVersion": 1}, {"id": "a5a2bfcf-e2b7-4a9f-a766-7d08168c3d6f", "name": "Set Guidelines", "type": "n8n-nodes-base.set", "position": [680, -960], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "f803283a-f895-4794-87ad-46c63542ea4f", "name": "id", "type": "string", "value": "={{ $json.id }}"}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "ab26d351-144d-477c-8dd3-a010c3fce0ca", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [880, -1200], "parameters": {"color": 4, "width": 667, "height": 1111, "content": "# 2. Retrieve Blog Post/s"}, "typeVersion": 1}, {"id": "f8e46822-cf7e-4697-bee4-99221b6063a7", "name": "Get Brand Guidelines", "type": "n8n-nodes-base.airtable", "position": [480, -960], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appRDq3E42JNtruIP", "cachedResultUrl": "https://airtable.com/appRDq3E42JNtruIP", "cachedResultName": "Content Manager"}, "table": {"__rl": true, "mode": "list", "value": "tblF8Ye2g0gPdpsaI", "cachedResultUrl": "https://airtable.com/appRDq3E42JNtruIP/tblF8Ye2g0gPdpsaI", "cachedResultName": "Brand Guidelines"}, "options": {}, "operation": "search"}, "credentials": {"airtableTokenApi": {"id": "zS1BIbs19PvAC2d0", "name": "AlexK Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "c6609f11-d04d-4e83-8fc0-af3c0e2cc9bd", "name": "Get SEO Keywords", "type": "n8n-nodes-base.airtable", "position": [940, -960], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appRDq3E42JNtruIP", "cachedResultUrl": "https://airtable.com/appRDq3E42JNtruIP", "cachedResultName": "Content Manager"}, "table": {"__rl": true, "mode": "list", "value": "tblU1fgGH1LXwnWRb", "cachedResultUrl": "https://airtable.com/appRDq3E42JNtruIP/tblU1fgGH1LXwnWRb", "cachedResultName": "SEO Keywords"}, "options": {"fields": ["Keyword", "Related<PERSON><PERSON>nt"]}, "operation": "search"}, "credentials": {"airtableTokenApi": {"id": "zS1BIbs19PvAC2d0", "name": "AlexK Airtable Personal Access Token account"}}, "executeOnce": false, "typeVersion": 2.1}, {"id": "d9201fa0-05d9-492b-896d-2cdc26e84f2e", "name": "Remove Duplicates", "type": "n8n-nodes-base.removeDuplicates", "position": [1340, -960], "parameters": {"compare": "<PERSON><PERSON><PERSON>s", "options": {}, "fieldsToCompare": "id"}, "typeVersion": 2}, {"id": "68f2a29b-0e29-4a64-986b-9c1204c1d1ef", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [1080, -1040], "parameters": {"color": 3, "width": 220, "height": 240, "content": "## Set keyword filter"}, "typeVersion": 1}, {"id": "530dfb77-0aee-445d-8a1f-d8f2cbcd1640", "name": "Keyword Filter", "type": "n8n-nodes-base.filter", "position": [1140, -960], "parameters": {"options": {"ignoreCase": true}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": false, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "1b854a48-286a-486f-8a0f-4eb3b8d302ea", "operator": {"type": "string", "operation": "contains"}, "leftValue": "={{ $json.Keyword }}", "rightValue": "ai automation"}]}}, "typeVersion": 2.2}, {"id": "75da86d6-22d4-42d6-8451-ea75db76ae57", "name": "Get Content", "type": "n8n-nodes-base.airtable", "position": [1140, -740], "parameters": {"id": "={{ $json.RelatedContent }}", "base": {"__rl": true, "mode": "list", "value": "appRDq3E42JNtruIP", "cachedResultUrl": "https://airtable.com/appRDq3E42JNtruIP", "cachedResultName": "Content Manager"}, "table": {"__rl": true, "mode": "list", "value": "tblU1fgGH1LXwnWRb", "cachedResultUrl": "https://airtable.com/appRDq3E42JNtruIP/tblU1fgGH1LXwnWRb", "cachedResultName": "SEO Keywords"}, "options": {}}, "credentials": {"airtableTokenApi": {"id": "zS1BIbs19PvAC2d0", "name": "AlexK Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "7524c5d8-78bb-4a4e-9c56-af97b851b767", "name": "Split Out Content", "type": "n8n-nodes-base.splitOut", "position": [1340, -740], "parameters": {"include": "allOtherFields", "options": {}, "fieldToSplitOut": "id"}, "typeVersion": 1}, {"id": "45d55ea1-ad01-4771-a2b2-67bb0cd1f983", "name": "Split Out Keywords", "type": "n8n-nodes-base.splitOut", "position": [940, -740], "parameters": {"include": "allOtherFields", "options": {}, "fieldToSplitOut": "Related<PERSON><PERSON>nt"}, "typeVersion": 1}, {"id": "349c64c2-2085-4b61-b9d2-dc1f0d7f46f6", "name": "Limit", "type": "n8n-nodes-base.limit", "position": [1340, -520], "parameters": {}, "typeVersion": 1}, {"id": "3b689583-6f40-4a6b-9afc-af128b2d4fca", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [1580, -1200], "parameters": {"color": 5, "width": 727, "height": 1111, "content": "# 3. Prepare Short Form Video Content"}, "typeVersion": 1}, {"id": "bcdf8e9e-463c-43d4-a29e-7f90076815a1", "name": "Script Prep", "type": "@n8n/n8n-nodes-langchain.openAi", "onError": "continueErrorOutput", "position": [1640, -960], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "messages": {"values": [{"content": "=Prepare a script with 4 scenes for a short form video based on the following blog post:\n\nTitle:\n{{ $json.Title }}\n\nContent:\n{{ $json.Content }}\n\nThe video should be less than 30 seconds in length.\n\nAlso create image prompts for each scene within the script.\n\nThen output a image prompt for the video thmbnail.\n\nThe video will use a 9:16 aspect."}, {"role": "system", "content": "Output format:\nMake sure you number each script and image prompt.\n\nScene 1 - 4\n- script #\n- image prompt #\n\nThumbnail Prompt"}]}, "jsonOutput": true}, "credentials": {"openAiApi": {"id": "ysxujEYFiY5ozRTS", "name": "AlexK OpenAi Key"}}, "typeVersion": 1.6}, {"id": "be07f6e3-1e2d-4d4f-a8e0-1d642ca4b789", "name": "Split Out Scenes", "type": "n8n-nodes-base.splitOut", "position": [2060, -520], "parameters": {"options": {}, "fieldToSplitOut": "message.content.scenes"}, "typeVersion": 1}, {"id": "83b3213d-70db-46d4-8dcc-5f399a64467d", "name": "Split Out TN Prompt", "type": "n8n-nodes-base.splitOut", "position": [2060, -1020], "parameters": {"options": {}, "fieldToSplitOut": "message.content.thumbnail_prompt"}, "typeVersion": 1}, {"id": "d4f30eff-220a-4682-9072-f1bbbce3655c", "name": "Leo - Improve Prompt1", "type": "n8n-nodes-base.httpRequest", "position": [2600, -1020], "parameters": {"url": "https://cloud.leonardo.ai/api/rest/v1/prompt/improve", "method": "POST", "options": {"response": {"response": {"fullResponse": true}}}, "jsonBody": "={\n \"prompt\": \"{{ $json['message.content[\\'Thumbnail Prompt\\']'] }}\"\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpCustomAuth", "headerParameters": {"parameters": [{"name": "accept", "value": "application/json"}]}}, "credentials": {"httpCustomAuth": {"id": "pJguwbEclNjPgU6F", "name": "Leo Custom Auth account"}}, "typeVersion": 4.2}, {"id": "a1d32bae-67a0-487d-9583-0b53ab25d184", "name": "Leo - Get imageId1", "type": "n8n-nodes-base.httpRequest", "position": [3200, -1020], "parameters": {"url": "=https://cloud.leonardo.ai/api/rest/v1/generations/{{ $json.body.sdGenerationJob.generationId }}", "options": {"response": {"response": {"fullResponse": true}}}, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpCustomAuth", "headerParameters": {"parameters": [{"name": "content-type", "value": "application/json"}]}}, "credentials": {"httpCustomAuth": {"id": "pJguwbEclNjPgU6F", "name": "Leo Custom Auth account"}}, "typeVersion": 4.2}, {"id": "89436c9d-1898-4f32-abf4-1f4fef7473a8", "name": "Prompt Settings", "type": "n8n-nodes-base.set", "position": [2400, -1020], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "56c8f20d-d9d9-4be7-ac2a-38df6ffdd722", "name": "model", "type": "string", "value": "de7d3faf-762f-48e0-b3b7-9d0ac3a3fcf3"}, {"id": "dc66dd4a-9209-4790-b844-e19931accc39", "name": "additional", "type": "string", "value": "Use the rule of thirds, leading lines, & balance."}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "9791db0e-c9a5-4fd8-b3fc-fd92b65c6362", "name": "Leo - Generate Image1", "type": "n8n-nodes-base.httpRequest", "position": [2800, -1020], "parameters": {"url": "https://cloud.leonardo.ai/api/rest/v1/generations", "method": "POST", "options": {"response": {"response": {"fullResponse": true}}}, "jsonBody": "={\n \"alchemy\": true,\n \"width\": 768,\n \"height\": 1376,\n \"modelId\": \"{{ $('Prompt Settings').item.json.model }}\",\n \"num_images\": 1,\n \"presetStyle\": \"DYNAMIC\",\n \"prompt\": \"{{ $json.body.promptGeneration.prompt }};\",\n \"guidance_scale\": 7,\n \"highResolution\": true,\n \"promptMagic\": false,\n \"promptMagicStrength\": 0.5,\n \"promptMagicVersion\": \"v3\",\n \"public\": false,\n \"ultra\": false,\n \"photoReal\": false,\n \"negative_prompt\": \"\"\n} ", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpCustomAuth", "headerParameters": {"parameters": [{"name": "accept", "value": "application/json"}]}}, "credentials": {"httpCustomAuth": {"id": "pJguwbEclNjPgU6F", "name": "Leo Custom Auth account"}}, "typeVersion": 4.2}, {"id": "8b77e0b5-5ed5-401b-964f-e2a651b774ee", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [2340, -1200], "parameters": {"color": 6, "width": 1447, "height": 531, "content": "# 4. Generate Thumbnail Image"}, "typeVersion": 1}, {"id": "64c5d0f0-07ce-493d-b974-69051ed41e0d", "name": "Wait 30 Seconds", "type": "n8n-nodes-base.wait", "position": [3000, -1020], "webhookId": "08a6381f-bd3d-4cc1-8420-62c886406000", "parameters": {"amount": 30}, "typeVersion": 1.1}, {"id": "94e8f4a6-c22e-4938-bfc6-b5a040e3aa5e", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [2740, -1100], "parameters": {"color": 3, "width": 220, "height": 280, "content": "### Uses the latest Leonardo.ai Model: Phoenix 1.0"}, "typeVersion": 1}, {"id": "d418088f-cebd-483a-b413-09f62faac1b7", "name": "Leo - Improve Prompt", "type": "n8n-nodes-base.httpRequest", "position": [2800, -420], "parameters": {"url": "https://cloud.leonardo.ai/api/rest/v1/prompt/improve", "method": "POST", "options": {"response": {"response": {"fullResponse": true}}}, "jsonBody": "={\n \"prompt\": \"{{ $json.image_prompt }}\"\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpCustomAuth", "headerParameters": {"parameters": [{"name": "accept", "value": "application/json"}]}}, "credentials": {"httpCustomAuth": {"id": "pJguwbEclNjPgU6F", "name": "Leo Custom Auth account"}}, "typeVersion": 4.2}, {"id": "c77d1f84-8db8-4ca5-9bcf-854a4bda9cf5", "name": "Leo - Get imageId", "type": "n8n-nodes-base.httpRequest", "position": [3400, -420], "parameters": {"url": "=https://cloud.leonardo.ai/api/rest/v1/generations/{{ $json.body.sdGenerationJob.generationId }}", "options": {"response": {"response": {"fullResponse": true}}}, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpCustomAuth", "headerParameters": {"parameters": [{"name": "content-type", "value": "application/json"}]}}, "credentials": {"httpCustomAuth": {"id": "pJguwbEclNjPgU6F", "name": "Leo Custom Auth account"}}, "typeVersion": 4.2}, {"id": "3f8fef1e-c7ff-43d2-9385-4ab8a6dce553", "name": "Prompt Settings1", "type": "n8n-nodes-base.set", "position": [2600, -420], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "56c8f20d-d9d9-4be7-ac2a-38df6ffdd722", "name": "model", "type": "string", "value": "de7d3faf-762f-48e0-b3b7-9d0ac3a3fcf3"}, {"id": "dc66dd4a-9209-4790-b844-e19931accc39", "name": "additional", "type": "string", "value": "Use the rule of thirds, leading lines, & balance."}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "b6c43a16-29e8-4074-9dda-5661dfd3da5d", "name": "Leo - Generate Image", "type": "n8n-nodes-base.httpRequest", "position": [3000, -420], "parameters": {"url": "https://cloud.leonardo.ai/api/rest/v1/generations", "method": "POST", "options": {"response": {"response": {"fullResponse": true}}}, "jsonBody": "={\n \"alchemy\": false,\n \"width\": 768,\n \"height\": 1376,\n \"modelId\": \"{{ $('Prompt Settings1').item.json.model }}\",\n \"num_images\": 1,\n \"presetStyle\": \"DYNAMIC\",\n \"prompt\": \"{{ $json.body.promptGeneration.prompt }};\",\n \"guidance_scale\": 7,\n \"highResolution\": true,\n \"promptMagic\": false,\n \"promptMagicStrength\": 0.5,\n \"promptMagicVersion\": \"v3\",\n \"public\": false,\n \"ultra\": true,\n \"photoReal\": false,\n \"negative_prompt\": \"\"\n} ", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpCustomAuth", "headerParameters": {"parameters": [{"name": "accept", "value": "application/json"}]}}, "credentials": {"httpCustomAuth": {"id": "pJguwbEclNjPgU6F", "name": "Leo Custom Auth account"}}, "typeVersion": 4.2}, {"id": "c3910b17-9a27-4419-ab01-409cc7090c68", "name": "Wait 30 Seconds1", "type": "n8n-nodes-base.wait", "position": [3200, -420], "webhookId": "08a6381f-bd3d-4cc1-8420-62c886406000", "parameters": {"amount": 30}, "typeVersion": 1.1}, {"id": "85a320a3-7a06-41f9-a34a-de3fd1ce2950", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [2400, -520], "parameters": {"options": {"reset": false}}, "typeVersion": 3}, {"id": "8b3158d1-6be6-4446-8083-f3a9fa18e074", "name": "Sticky Note14", "type": "n8n-nodes-base.stickyNote", "position": [2340, -640], "parameters": {"color": 6, "width": 1447, "height": 551, "content": "# 4. Generate Scene Images"}, "typeVersion": 1}, {"id": "3ee7e646-8690-4a7c-9820-ce2985b02e7a", "name": "Add Asset Info", "type": "n8n-nodes-base.airtable", "position": [3400, -1020], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appRDq3E42JNtruIP", "cachedResultUrl": "https://airtable.com/appRDq3E42JNtruIP", "cachedResultName": "Content Manager"}, "table": {"__rl": true, "mode": "list", "value": "tblqoaJ7bRLBgENED", "cachedResultUrl": "https://airtable.com/appRDq3E42JNtruIP/tblqoaJ7bRLBgENED", "cachedResultName": "Assets"}, "columns": {"value": {"Asset URL": "={{ $json.body.generations_by_pk.generated_images[0].url }}", "File Size": 0, "Asset Name": "=TN - {{ $('Get Content').item.json.Title }}", "Asset Type": "Image"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "Asset Name", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Asset Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Asset Type", "type": "options", "display": true, "options": [{"name": "Image", "value": "Image"}, {"name": "Video", "value": "Video"}, {"name": "Document", "value": "Document"}], "removed": false, "readOnly": false, "required": false, "displayName": "Asset Type", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Upload Date", "type": "dateTime", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Upload Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "File Size", "type": "number", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "File Size", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Asset URL", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Asset URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Usage Rights", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Usage Rights", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "type": "array", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Associated Videos", "type": "array", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Associated Videos", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Associated Social Media Posts", "type": "array", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Associated Social Media Posts", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Associated Blog Posts", "type": "array", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Associated Blog Posts", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Related Campaigns", "type": "array", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Related Campaigns", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Schedules", "type": "array", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Schedules", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Content Calendar", "type": "array", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Content Calendar", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["id"]}, "options": {}, "operation": "create"}, "credentials": {"airtableTokenApi": {"id": "zS1BIbs19PvAC2d0", "name": "AlexK Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "88cd8514-9a54-47bf-b822-8c01ef05c08e", "name": "Add Asset Info1", "type": "n8n-nodes-base.airtable", "position": [3600, -420], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appRDq3E42JNtruIP", "cachedResultUrl": "https://airtable.com/appRDq3E42JNtruIP", "cachedResultName": "Content Manager"}, "table": {"__rl": true, "mode": "list", "value": "tblqoaJ7bRLBgENED", "cachedResultUrl": "https://airtable.com/appRDq3E42JNtruIP/tblqoaJ7bRLBgENED", "cachedResultName": "Assets"}, "columns": {"value": {"Asset URL": "={{ $json.body.generations_by_pk.generated_images[0].url }}", "File Size": 0, "Asset Name": "=Scene - {{ $('Loop Over Items').item.json.script }}", "Asset Type": "Image"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "Asset Name", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Asset Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Asset Type", "type": "options", "display": true, "options": [{"name": "Image", "value": "Image"}, {"name": "Video", "value": "Video"}, {"name": "Document", "value": "Document"}], "removed": false, "readOnly": false, "required": false, "displayName": "Asset Type", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Upload Date", "type": "dateTime", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Upload Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "File Size", "type": "number", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "File Size", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Asset URL", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Asset URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Usage Rights", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Usage Rights", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "type": "array", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Associated Videos", "type": "array", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Associated Videos", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Associated Social Media Posts", "type": "array", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Associated Social Media Posts", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Associated Blog Posts", "type": "array", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Associated Blog Posts", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Related Campaigns", "type": "array", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Related Campaigns", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Schedules", "type": "array", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Schedules", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Content Calendar", "type": "array", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Content Calendar", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["id"]}, "options": {}, "operation": "create"}, "credentials": {"airtableTokenApi": {"id": "zS1BIbs19PvAC2d0", "name": "AlexK Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "67cd2444-506d-4754-a75d-e725239d6f7c", "name": "Sticky Note15", "type": "n8n-nodes-base.stickyNote", "position": [2740, -500], "parameters": {"color": 3, "width": 220, "height": 280, "content": "### Uses the latest Leonardo.ai Model: Phoenix 1.0"}, "typeVersion": 1}, {"id": "1acc2d91-c4ba-4a26-bb74-a848875e9fac", "name": "Wikipedia", "type": "@n8n/n8n-nodes-langchain.toolWikipedia", "position": [1640, -740], "parameters": {}, "typeVersion": 1}, {"id": "1c5b5602-bf95-4534-8c73-69b8157765ee", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [2940, -840], "parameters": {"color": 7, "width": 400, "height": 80, "content": "### Optionally, you can modify the number of images generated to provide more options"}, "typeVersion": 1}, {"id": "79552e45-5dbc-4ddb-8543-039ca76dfe56", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [2940, -560], "parameters": {"color": 7, "width": 400, "height": 80, "content": "### Optionally, you can modify the number of images generated to provide more options"}, "typeVersion": 1}], "active": false, "pinData": {"Set Guidelines": [{"json": {"id": "rec3OS3As67j4mcGK", "Name": "Imagery Style", "createdTime": "2024-12-19T04:54:46.000Z"}}, {"json": {"id": "rec4h9vioTgCwE7f1", "Name": "Tagline", "Description": "Smart Automation. Smarter Results.", "createdTime": "2024-12-19T04:54:02.000Z"}}, {"json": {"id": "rec5P2oEOUPHjplm6", "Name": "Logo", "createdTime": "2024-12-19T04:54:23.000Z"}}, {"json": {"id": "rec8DMroyovGEp8lP", "Name": "Voice", "Description": "Assertive, friendly, and educational – \"We sound like a trusted expert who’s here to help. We’re confident but approachable, breaking down complex ideas in a simple, actionable way.\"", "createdTime": "2024-12-19T04:54:56.000Z"}}, {"json": {"id": "rec9BGsGreHnVAA6S", "Name": "Audience", "Description": "Primary Audience:\n\nMid-sized to enterprise businesses ($3M+ revenue)\nTech-savvy operations teams, AI enthusiasts, and digital creators\nSecondary Audience:\n\nWorkflow managers and automation specialists\nTech entrepreneurs and SaaS decision-makers\nPain Points Addressed:\n\nManual, repetitive tasks\nInefficient workflows slowing growth\nDifficulty leveraging AI and automation tools", "createdTime": "2024-12-19T04:54:14.000Z"}}, {"json": {"id": "recMgJO7MkxxyigTC", "Name": "Brand Don'ts", "Description": "Overcomplicate messaging with jargon-heavy language.\nStretch, distort, or recolor the logo.\nUse off-brand imagery (e.g., cluttered visuals, low-quality images).\nCreate content without a clear takeaway or action.\nBe too formal or robotic – keep it human and approachable.", "createdTime": "2024-12-19T04:55:24.000Z"}}, {"json": {"id": "recZYUAJk2TSaunVQ", "Name": "<PERSON><PERSON>", "Description": "Professional for enterprise communications (e.g., LinkedIn posts, newsletters).\nConversational and energetic for social media (e.g., YouTube, TikTok).\nInformative and engaging for tutorials and blogs.\n", "createdTime": "2024-12-19T04:55:04.000Z"}}, {"json": {"id": "recfPdhqOCPFKtwBC", "Name": "Vision", "Description": "To build a future where AI-driven automation seamlessly integrates into every business, unlocking creativity, efficiency, and growth for all.", "createdTime": "2024-12-19T04:50:31.000Z"}}, {"json": {"id": "recmVa42Fz4PI2h2E", "Name": "Mission", "Description": "To empower businesses and creators with smart, scalable automation and generative AI solutions that simplify workflows, save time, and drive innovation.", "createdTime": "2024-12-19T04:50:31.000Z"}}, {"json": {"id": "recmsAphrjMsPfbbD", "Name": "Brand Do's", "Description": "Use the logo and brand colors consistently.\nSpeak clearly and simply about complex AI workflows.\nBalance creativity with professionalism in visuals.\nFocus on solving real problems with examples and storytelling.\nUse clean, modern designs in all content.", "createdTime": "2024-12-19T04:55:15.000Z"}}, {"json": {"id": "recouwkSXqppzecEL", "Name": "Brand Story", "Description": "<PERSON><PERSON><PERSON><PERSON> was born from a love of AI, automation, and creativity. I saw businesses struggling with manual workflows and inefficiency, so I set out to build tools and share knowledge that simplify work and spark innovation. From branding to AI-native automations, the goal is simple: Empower businesses to work smarter, not harder.", "createdTime": "2024-12-19T04:54:09.000Z"}}, {"json": {"id": "recrJ6KO6JOuB4xXT", "Name": "Core Values", "Description": "Exploration – \"We explore the edge of technology to uncover solutions that redefine possibilities.\"\nEfficiency – \"Every workflow, every solution – designed to optimize time, resources, and results.\"\nInnovation – \"We embrace new ideas, take risks, and push the boundaries of automation and AI.\"\nHonesty – \"Transparency is key; we say what we do and do what we say.\"\nImpact – \"We focus on meaningful results that create value for businesses and their teams.\"", "createdTime": "2024-12-19T04:50:31.000Z"}}, {"json": {"id": "recvORk5EszN2Nopt", "Name": "Color Palette", "createdTime": "2024-12-19T04:54:31.000Z"}}, {"json": {"id": "reczwB6oMc7SGvboS", "Name": "Typography", "Description": "Inter", "createdTime": "2024-12-19T04:54:39.000Z"}}], "Leo - Get imageId1": [{"json": {"body": {"generations_by_pk": {"id": "40cf89f4-dc20-4546-b22c-26017f42d20f", "seed": 711149708, "ultra": false, "motion": null, "prompt": "Emerging from the computer screen, futuristic product designs intertwine with AI elements in a mesmerizing image of innovation. This digital creation depicts sleek, high-tech concepts in a dynamic and vibrant color palette. The visual is a highly detailed digital rendering that showcases cutting-edge technology and modern design aesthetics with impeccable precision. Each element exudes a sense of sophistication and creativity, capturing the essence of the boundary-pushing world of tech design.;", "public": false, "status": "COMPLETE", "modelId": "de7d3faf-762f-48e0-b3b7-9d0ac3a3fcf3", "createdAt": "2024-12-19T07:50:56.021", "photoReal": false, "scheduler": "EULER_DISCRETE", "sdVersion": "PHOENIX", "imageWidth": 768, "imageHeight": 1376, "motionModel": null, "presetStyle": "DYNAMIC", "promptMagic": false, "imageToVideo": null, "initStrength": null, "fantasyAvatar": null, "guidanceScale": 7, "inferenceSteps": 12, "motionStrength": null, "negativePrompt": "", "generated_images": [{"id": "03ae728b-f305-464d-9a61-92f624f50ee6", "url": "https://cdn.leonardo.ai/users/18c4756f-8bfb-4e43-ac59-cc3ced68c735/generations/40cf89f4-dc20-4546-b22c-26017f42d20f/Leonardo_Phoenix_10_Emerging_from_the_computer_screen_futurist_0.jpg", "nsfw": false, "likeCount": 0, "motionMP4URL": null, "generated_image_variation_generics": []}], "photoRealStrength": null, "promptMagicVersion": null, "prompt_moderations": [{"moderationClassification": []}], "generation_elements": [], "promptMagicStrength": null}}, "headers": {"date": "Thu, 19 Dec 2024 07:51:26 GMT", "cf-ray": "8f45ceb46b4b2d15-IAD", "server": "cloudflare", "connection": "close", "content-type": "application/json; charset=utf-8", "x-request-id": "424eed5a65fd0dabcfa9f97ca9ea9a6b", "content-length": "897", "cf-cache-status": "DYNAMIC", "referrer-policy": "strict-origin-when-cross-origin", "x-frame-options": "SAMEORIGIN", "x-xss-protection": "0", "x-content-type-options": "nosniff", "content-security-policy": "upgrade-insecure-requests", "strict-transport-security": "max-age=31536000; includeSubDomains"}, "statusCode": 200, "statusMessage": "OK"}}], "Get Brand Guidelines": [{"json": {"id": "rec3OS3As67j4mcGK", "Name": "Imagery Style", "createdTime": "2024-12-19T04:54:46.000Z"}}, {"json": {"id": "rec4h9vioTgCwE7f1", "Name": "Tagline", "Content": "Smart Automation. Smarter Results.", "createdTime": "2024-12-19T04:54:02.000Z"}}, {"json": {"id": "rec5P2oEOUPHjplm6", "Name": "Logo", "createdTime": "2024-12-19T04:54:23.000Z"}}, {"json": {"id": "rec8DMroyovGEp8lP", "Name": "Voice", "Content": "Assertive, friendly, and educational – \"We sound like a trusted expert who’s here to help. We’re confident but approachable, breaking down complex ideas in a simple, actionable way.\"", "createdTime": "2024-12-19T04:54:56.000Z"}}, {"json": {"id": "rec9BGsGreHnVAA6S", "Name": "Audience", "Content": "Primary Audience:\n\nMid-sized to enterprise businesses ($3M+ revenue)\nTech-savvy operations teams, AI enthusiasts, and digital creators\nSecondary Audience:\n\nWorkflow managers and automation specialists\nTech entrepreneurs and SaaS decision-makers\nPain Points Addressed:\n\nManual, repetitive tasks\nInefficient workflows slowing growth\nDifficulty leveraging AI and automation tools", "createdTime": "2024-12-19T04:54:14.000Z"}}, {"json": {"id": "recMgJO7MkxxyigTC", "Name": "Brand Don'ts", "Content": "Overcomplicate messaging with jargon-heavy language.\nStretch, distort, or recolor the logo.\nUse off-brand imagery (e.g., cluttered visuals, low-quality images).\nCreate content without a clear takeaway or action.\nBe too formal or robotic – keep it human and approachable.", "createdTime": "2024-12-19T04:55:24.000Z"}}, {"json": {"id": "recZYUAJk2TSaunVQ", "Name": "<PERSON><PERSON>", "Content": "Professional for enterprise communications (e.g., LinkedIn posts, newsletters).\nConversational and energetic for social media (e.g., YouTube, TikTok).\nInformative and engaging for tutorials and blogs.\n", "createdTime": "2024-12-19T04:55:04.000Z"}}, {"json": {"id": "recfPdhqOCPFKtwBC", "Name": "Vision", "Content": "To build a future where AI-driven automation seamlessly integrates into every business, unlocking creativity, efficiency, and growth for all.", "createdTime": "2024-12-19T04:50:31.000Z"}}, {"json": {"id": "recmVa42Fz4PI2h2E", "Name": "Mission", "Content": "To empower businesses and creators with smart, scalable automation and generative AI solutions that simplify workflows, save time, and drive innovation.", "createdTime": "2024-12-19T04:50:31.000Z"}}, {"json": {"id": "recmsAphrjMsPfbbD", "Name": "Brand Do's", "Content": "Use the logo and brand colors consistently.\nSpeak clearly and simply about complex AI workflows.\nBalance creativity with professionalism in visuals.\nFocus on solving real problems with examples and storytelling.\nUse clean, modern designs in all content.", "createdTime": "2024-12-19T04:55:15.000Z"}}, {"json": {"id": "recouwkSXqppzecEL", "Name": "Brand Story", "Content": "<PERSON><PERSON><PERSON><PERSON> was born from a love of AI, automation, and creativity. I saw businesses struggling with manual workflows and inefficiency, so I set out to build tools and share knowledge that simplify work and spark innovation. From branding to AI-native automations, the goal is simple: Empower businesses to work smarter, not harder.", "createdTime": "2024-12-19T04:54:09.000Z"}}, {"json": {"id": "recrJ6KO6JOuB4xXT", "Name": "Core Values", "Content": "Exploration – \"We explore the edge of technology to uncover solutions that redefine possibilities.\"\nEfficiency – \"Every workflow, every solution – designed to optimize time, resources, and results.\"\nInnovation – \"We embrace new ideas, take risks, and push the boundaries of automation and AI.\"\nHonesty – \"Transparency is key; we say what we do and do what we say.\"\nImpact – \"We focus on meaningful results that create value for businesses and their teams.\"", "createdTime": "2024-12-19T04:50:31.000Z"}}, {"json": {"id": "recvORk5EszN2Nopt", "Name": "Color Palette", "createdTime": "2024-12-19T04:54:31.000Z"}}, {"json": {"id": "reczwB6oMc7SGvboS", "Name": "Typography", "Content": "Inter", "createdTime": "2024-12-19T04:54:39.000Z"}}], "Leo - Generate Image1": [{"json": {"body": {"sdGenerationJob": {"generationId": "40cf89f4-dc20-4546-b22c-26017f42d20f", "apiCreditCost": 31}}, "headers": {"date": "Thu, 19 Dec 2024 07:50:56 GMT", "cf-ray": "8f45cdf2998557ea-IAD", "server": "cloudflare", "connection": "close", "content-type": "application/json; charset=utf-8", "x-request-id": "f8009585ede2caca5369318c9587883c", "cf-cache-status": "DYNAMIC", "referrer-policy": "strict-origin-when-cross-origin", "x-frame-options": "SAMEORIGIN", "x-xss-protection": "0", "transfer-encoding": "chunked", "x-content-type-options": "nosniff", "content-security-policy": "upgrade-insecure-requests", "strict-transport-security": "max-age=31536000; includeSubDomains"}, "statusCode": 200, "statusMessage": "OK"}}], "Leo - Improve Prompt1": [{"json": {"body": {"promptGeneration": {"prompt": "Emerging from the computer screen, futuristic product designs intertwine with AI elements in a mesmerizing image of innovation. This digital creation depicts sleek, high-tech concepts in a dynamic and vibrant color palette. The visual is a highly detailed digital rendering that showcases cutting-edge technology and modern design aesthetics with impeccable precision. Each element exudes a sense of sophistication and creativity, capturing the essence of the boundary-pushing world of tech design.", "apiCreditCost": 4}}, "headers": {"date": "Thu, 19 Dec 2024 07:50:01 GMT", "cf-ray": "8f45cc9b28d8e5f8-IAD", "server": "cloudflare", "connection": "close", "content-type": "application/json; charset=utf-8", "x-request-id": "345fea24320e4c2d091413164b66b1a3", "cf-cache-status": "DYNAMIC", "referrer-policy": "strict-origin-when-cross-origin", "x-frame-options": "SAMEORIGIN", "x-xss-protection": "0", "transfer-encoding": "chunked", "x-content-type-options": "nosniff", "content-security-policy": "upgrade-insecure-requests", "strict-transport-security": "max-age=31536000; includeSubDomains"}, "statusCode": 200, "statusMessage": "OK"}}]}, "settings": {"executionOrder": "v1"}, "versionId": "fbfa83f0-1109-4c7e-9cd7-abfabc810f48", "connections": {"Limit": {"main": [[{"node": "Script Prep", "type": "main", "index": 0}]]}, "Wikipedia": {"ai_tool": [[{"node": "Script Prep", "type": "ai_tool", "index": 0}]]}, "Get Content": {"main": [[{"node": "Split Out Content", "type": "main", "index": 0}]]}, "Script Prep": {"main": [[{"node": "Split Out TN Prompt", "type": "main", "index": 0}, {"node": "Split Out Scenes", "type": "main", "index": 0}]]}, "Keyword Filter": {"main": [[{"node": "Remove Duplicates", "type": "main", "index": 0}]]}, "Set Guidelines": {"main": [[{"node": "Get SEO Keywords", "type": "main", "index": 0}]]}, "Add Asset Info1": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Prompt Settings1", "type": "main", "index": 0}]]}, "Prompt Settings": {"main": [[{"node": "Leo - Improve Prompt1", "type": "main", "index": 0}]]}, "Wait 30 Seconds": {"main": [[{"node": "Leo - Get imageId1", "type": "main", "index": 0}]]}, "Get SEO Keywords": {"main": [[{"node": "Keyword Filter", "type": "main", "index": 0}]]}, "Prompt Settings1": {"main": [[{"node": "Leo - Improve Prompt", "type": "main", "index": 0}]]}, "Split Out Scenes": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Wait 30 Seconds1": {"main": [[{"node": "Leo - Get imageId", "type": "main", "index": 0}]]}, "Leo - Get imageId": {"main": [[{"node": "Add Asset Info1", "type": "main", "index": 0}]]}, "Remove Duplicates": {"main": [[{"node": "Split Out Keywords", "type": "main", "index": 0}]]}, "Split Out Content": {"main": [[{"node": "Limit", "type": "main", "index": 0}]]}, "Leo - Get imageId1": {"main": [[{"node": "Add Asset Info", "type": "main", "index": 0}]]}, "Split Out Keywords": {"main": [[{"node": "Get Content", "type": "main", "index": 0}]]}, "Split Out TN Prompt": {"main": [[{"node": "Prompt Settings", "type": "main", "index": 0}]]}, "Get Brand Guidelines": {"main": [[{"node": "Set Guidelines", "type": "main", "index": 0}]]}, "Leo - Generate Image": {"main": [[{"node": "Wait 30 Seconds1", "type": "main", "index": 0}]]}, "Leo - Improve Prompt": {"main": [[{"node": "Leo - Generate Image", "type": "main", "index": 0}]]}, "Leo - Generate Image1": {"main": [[{"node": "Wait 30 Seconds", "type": "main", "index": 0}]]}, "Leo - Improve Prompt1": {"main": [[{"node": "Leo - Generate Image1", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Get Brand Guidelines", "type": "main", "index": 0}]]}}}