{"nodes": [{"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.netlifyTrigger", "position": [450, 300], "webhookId": "0654820c-1960-4c8b-80fc-c0a66ab96577", "parameters": {"event": "deployFailed", "siteId": "ab52947e-a696-4498-a5a1-fae7fbe30c84"}, "credentials": {"netlifyApi": "Netlify account"}, "typeVersion": 1}, {"name": "<PERSON><PERSON>ck", "type": "n8n-nodes-base.slack", "position": [650, 300], "parameters": {"text": "=🚨 Deploy Failed 🚨\nDeploy for the site {{$json[\"name\"]}} failed.\nError Message: {{$json[\"error_message\"]}}\nYou can find more information here: https://app.netlify.com/sites/{{$json[\"name\"]}}/deploys/{{$json[\"id\"]}}", "channel": "general", "attachments": [], "otherOptions": {}}, "credentials": {"slackApi": "read-history"}, "typeVersion": 1}], "connections": {"Netlify Trigger": {"main": [[{"node": "<PERSON><PERSON>ck", "type": "main", "index": 0}]]}}}