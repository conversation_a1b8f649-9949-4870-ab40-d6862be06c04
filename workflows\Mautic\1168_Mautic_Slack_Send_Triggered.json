{"id": 86, "name": "Check for valid Mautic contact email", "nodes": [{"name": "If is not new contact", "type": "n8n-nodes-base.if", "position": [780, 460], "parameters": {"conditions": {"string": [{"value1": "={{$json[\"mautic.lead_post_save_new\"]}}", "operation": "isEmpty"}]}}, "typeVersion": 1}, {"name": "On Contact Identified", "type": "n8n-nodes-base.mauticTrigger", "position": [600, 460], "webhookId": "a3ee0f93-2870-44e2-bb2f-0175433263b3", "parameters": {"events": ["mautic.lead_post_save_new"], "authentication": "oAuth2"}, "credentials": {"mauticOAuth2Api": {"id": "54", "name": "Mautic account"}}, "typeVersion": 1}, {"name": "extract information", "type": "n8n-nodes-base.itemLists", "position": [980, 480], "parameters": {"options": {}, "fieldToSplitOut": "mautic.lead_post_save_new"}, "typeVersion": 1}, {"name": "validate email", "type": "n8n-nodes-base.oneSimpleApi", "position": [1180, 480], "parameters": {"resource": "utility", "emailAddress": "={{$json[\"lead\"][\"fields\"][\"core\"][\"email\"][\"value\"]}}"}, "credentials": {"oneSimpleApi": {"id": "33", "name": "One Simple account"}}, "typeVersion": 1}, {"name": "If the email is suspicious", "type": "n8n-nodes-base.if", "notes": "IF\ndeliverability is not good\nOR\nDomain is not valid\nOR\nEmail is Disposable", "position": [1360, 480], "parameters": {"conditions": {"string": [{"value1": "={{$json[\"deliverability\"]}}", "value2": "GOOD", "operation": "notEqual"}], "boolean": [{"value1": "={{$json[\"is_domain_valid\"]}}"}, {"value1": "={{$json[\"is_email_disposable\"]}}", "value2": true}]}, "combineOperation": "any"}, "typeVersion": 1}, {"name": "Send to Slack", "type": "n8n-nodes-base.slack", "position": [1560, 460], "parameters": {"text": "=:warning: New Contact with Suspicious Email :warning:\n*Name: * {{$node[\"extract information\"].json[\"contact\"][\"fields\"][\"core\"][\"firstname\"][\"normalizedValue\"]}} {{$node[\"extract information\"].json[\"contact\"][\"fields\"][\"core\"][\"lastname\"][\"normalizedValue\"]}}\n*Email: * {{$node[\"extract information\"].json[\"contact\"][\"fields\"][\"core\"][\"email\"][\"normalizedValue\"]}}\n*Link: * https://mautic.my.domain.com/s/contacts/view/{{$node[\"extract information\"].json[\"contact\"][\"id\"]}}\n*Creator: * {{$node[\"extract information\"].json[\"contact\"][\"createdByUser\"]}}", "channel": "#mautic-alerts", "attachments": [], "otherOptions": {}}, "credentials": {"slackApi": {"id": "53", "name": "Slack Access Token"}}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"validate email": {"main": [[{"node": "If the email is suspicious", "type": "main", "index": 0}]]}, "extract information": {"main": [[{"node": "validate email", "type": "main", "index": 0}]]}, "If is not new contact": {"main": [[], [{"node": "extract information", "type": "main", "index": 0}]]}, "On Contact Identified": {"main": [[{"node": "If is not new contact", "type": "main", "index": 0}]]}, "If the email is suspicious": {"main": [[{"node": "Send to Slack", "type": "main", "index": 0}]]}}}