{"id": "F7CfIF10XjXhqbGb", "meta": {"instanceId": "ba8f1362d8ed4c2ce84171d2f481098de4ee775241bdc1660d1dce80434ec7d4", "templateCredsSetupCompleted": true}, "name": "Play with Spotify from Telegram", "tags": [], "nodes": [{"id": "0395b3e4-94ef-49ea-9b4c-8f908e62f8c6", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "position": [-60, 20], "webhookId": "e7aa284b-5eef-4ac1-94bf-8e4d307a3b14", "parameters": {"updates": ["message"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "gblW5oACGEPuccja", "name": "Telegram account"}}, "typeVersion": 1.1}, {"id": "263edf45-58a0-45e8-91f8-601bc62c7d6f", "name": "OpenAI - Ask about a track", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [120, -120], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "messages": {"values": [{"content": "=get artist and song name from '{{ $json.message.text }}'. Reply only eg. 'track:song name artist:artist name'"}]}}, "credentials": {"openAiApi": {"id": "vDcge3EgslxfX3EC", "name": "OpenAi account"}}, "typeVersion": 1.6}, {"id": "086aef8b-533a-4c33-9952-29d5adb152c8", "name": "Search track", "type": "n8n-nodes-base.spotify", "onError": "continueErrorOutput", "position": [540, -200], "parameters": {"limit": 1, "query": "={{ $json.message.content }}", "filters": {}, "resource": "track", "operation": "search"}, "credentials": {"spotifyOAuth2Api": {"id": "wylKghFNQa8IKy1U", "name": "Spotify account"}}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "08af6055-ba52-4cb2-a561-ea04ac55279f", "name": "Add song", "type": "n8n-nodes-base.spotify", "onError": "continueErrorOutput", "position": [780, -240], "parameters": {"id": "=spotify:track:{{ $json.id }}"}, "credentials": {"spotifyOAuth2Api": {"id": "wylKghFNQa8IKy1U", "name": "Spotify account"}}, "typeVersion": 1}, {"id": "2dbdafa4-3b6f-4a14-813c-4e10da10abad", "name": "Next Song", "type": "n8n-nodes-base.spotify", "onError": "continueErrorOutput", "position": [980, -280], "parameters": {"operation": "nextSong"}, "credentials": {"spotifyOAuth2Api": {"id": "wylKghFNQa8IKy1U", "name": "Spotify account"}}, "typeVersion": 1}, {"id": "cb8d42aa-0c7e-45a5-90b5-b91e483dd13a", "name": "Resume play", "type": "n8n-nodes-base.spotify", "notes": "We don't have to stop here on error. An error is thrown from Spotify if the player is already playing.", "onError": "continueRegularOutput", "position": [1240, -380], "parameters": {"operation": "resume"}, "credentials": {"spotifyOAuth2Api": {"id": "wylKghFNQa8IKy1U", "name": "Spotify account"}}, "typeVersion": 1}, {"id": "089e1070-b013-454c-9f6c-55b909e06c1d", "name": "Currently Playing", "type": "n8n-nodes-base.spotify", "onError": "continueErrorOutput", "position": [1420, -300], "parameters": {"operation": "currentlyPlaying"}, "credentials": {"spotifyOAuth2Api": {"id": "wylKghFNQa8IKy1U", "name": "Spotify account"}}, "typeVersion": 1}, {"id": "e9df0dcf-b166-45a3-910b-787b3718bbcf", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [120, -300], "parameters": {"color": 5, "width": 254.**************, "content": "## Telegram to Spotify \nAsk AI about a track with artist and song name or if you can't remember describe it and AI does it's thing.\n"}, "typeVersion": 1}, {"id": "77bae9be-2d92-4028-ae78-7887b6a2d394", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [440, 220], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineAll"}, "typeVersion": 3}, {"id": "0d95000d-7efd-402a-9a34-47ababb2f53e", "name": "If", "type": "n8n-nodes-base.if", "position": [620, -440], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "02af5387-07d2-4a16-bd83-e1359d091165", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json?.id }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "363f89ad-34d0-4445-8ff3-693d991dad09", "name": "Message parser", "type": "n8n-nodes-base.set", "position": [1280, -40], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "93cd2545-c6e9-4717-96b7-d49eb056ac70", "name": "message", "type": "string", "value": "={{ $json.error }}"}]}}, "typeVersion": 3.4}, {"id": "8b80f80d-8c8e-44de-9838-6d05199bb734", "name": "Not found error message", "type": "n8n-nodes-base.set", "position": [880, -460], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "{\n  \"error\": \"Song not found\"\n}\n"}, "typeVersion": 3.4}, {"id": "f1785140-8e97-43e1-9d84-aedc8b8d5e06", "name": "Return message to Telegram", "type": "n8n-nodes-base.telegram", "position": [760, 220], "parameters": {"text": "={{ $('Message parser').item.json.message }}", "chatId": "={{ $json.message.chat.id }}", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "gblW5oACGEPuccja", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "e3e16535-094b-41bf-88c6-166bb6805d53", "name": "Define Now Playing", "type": "n8n-nodes-base.set", "notes": "We use the object \"error\" as a returned bject so we can re-use the Message Parser node.", "position": [1660, -240], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "={\n  \"error\": \"Now playing {{ $json.item.name }} - {{ $json.item.artists[0].name }} - {{ $json.item.album.name }}\"\n}\n"}, "typeVersion": 3.4}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "6f219c9e-f17a-45b1-ab8d-09d991fd8e34", "connections": {"If": {"main": [[{"node": "Add song", "type": "main", "index": 0}], [{"node": "Not found error message", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "Return message to Telegram", "type": "main", "index": 0}]]}, "Add song": {"main": [[{"node": "Next Song", "type": "main", "index": 0}], [{"node": "Message parser", "type": "main", "index": 0}]]}, "Next Song": {"main": [[{"node": "Resume play", "type": "main", "index": 0}], [{"node": "Message parser", "type": "main", "index": 0}]]}, "Resume play": {"main": [[{"node": "Currently Playing", "type": "main", "index": 0}], []]}, "Search track": {"main": [[{"node": "If", "type": "main", "index": 0}], [{"node": "Message parser", "type": "main", "index": 0}]]}, "Message parser": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Telegram Trigger": {"main": [[{"node": "OpenAI - Ask about a track", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Currently Playing": {"main": [[{"node": "Define Now Playing", "type": "main", "index": 0}], [{"node": "Message parser", "type": "main", "index": 0}]]}, "Define Now Playing": {"main": [[{"node": "Message parser", "type": "main", "index": 0}]]}, "Not found error message": {"main": [[{"node": "Message parser", "type": "main", "index": 0}]]}, "OpenAI - Ask about a track": {"main": [[{"node": "Search track", "type": "main", "index": 0}]]}}}