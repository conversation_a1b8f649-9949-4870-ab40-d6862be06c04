{"meta": {"instanceId": "84ba6d895254e080ac2b4916d987aa66b000f88d4d919a6b9c76848f9b8a7616", "templateId": "2234"}, "nodes": [{"id": "e0f68f60-f036-4103-a9fc-d6cb80b6f8a2", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1980, 1100], "parameters": {"model": "gpt-4-turbo", "options": {}}, "credentials": {"openAiApi": {"id": "kDo5LhPmHS2WQE0b", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "23779dea-c21d-42da-b493-09394bc64436", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [2420, 660], "parameters": {"model": "gpt-4-turbo", "options": {}}, "credentials": {"openAiApi": {"id": "kDo5LhPmHS2WQE0b", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "af59863e-12c5-414c-bf64-dd6712e3aa7b", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [1680, 960], "parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 12}]}}, "typeVersion": 1.1}, {"id": "bc2ad02b-72c9-4132-96e8-b64487f589f7", "name": "Recursive Character Text Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "position": [2160, 1140], "parameters": {"options": {}, "chunkSize": 500, "chunkOverlap": 300}, "typeVersion": 1}, {"id": "cb11a8bb-bdca-43cb-a586-7f93471d58f7", "name": "OpenAI Chat Model2", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [2420, 1300], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "kDo5LhPmHS2WQE0b", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "9d02b910-a467-4d4d-a2fa-32d1d3361d21", "name": "Create a Prompt for DALL-E", "type": "@n8n/n8n-nodes-langchain.chainSummarization", "position": [2400, 1080], "parameters": {"options": {"summarizationMethodAndPrompts": {"values": {"prompt": "Summarize the characters in this story based on their appearance and describe them if they are humans or animals and how they look like and what kind of are they, the prompt should be no-text in the picture.\n\n\n\n\n\"{text}\"\n\n\nCONCISE SUMMARY:", "summarizationMethod": "stuff"}}}}, "typeVersion": 2}, {"id": "4723dd65-96f5-41c1-9ff6-f1a344d96241", "name": "Generate an Image for the Story", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [2860, 1080], "parameters": {"prompt": "=Produce an image ensuring that no text is generated within the visual content. {{ $json.response.text }}", "options": {}, "resource": "image"}, "credentials": {"openAiApi": {"id": "kDo5LhPmHS2WQE0b", "name": "OpenAi account"}}, "typeVersion": 1.3}, {"id": "70b7f55a-31c4-456b-8273-8250bac74409", "name": "Generate Audio for the Story", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [2640, 820], "parameters": {"input": "={{ $json.response.text }}", "options": {}, "resource": "audio"}, "credentials": {"openAiApi": {"id": "kDo5LhPmHS2WQE0b", "name": "OpenAi account"}}, "executeOnce": true, "typeVersion": 1.3}, {"id": "c381dbe4-6112-441c-b213-8a2d218f4cc2", "name": "Send the Story To Channel", "type": "n8n-nodes-base.telegram", "position": [3160, 480], "parameters": {"text": "={{ $json.response.text }}", "chatId": "=-**********", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "k3RE6o9brmFRFE9p", "name": "Telegram account"}}, "typeVersion": 1.1}, {"id": "78289bfa-54b4-4acb-b513-7a0134a010f3", "name": "Send Image to the Channel", "type": "n8n-nodes-base.telegram", "position": [3180, 1080], "parameters": {"chatId": "=-**********", "operation": "sendPhoto", "binaryData": true, "additionalFields": {}}, "credentials": {"telegramApi": {"id": "k3RE6o9brmFRFE9p", "name": "Telegram account"}}, "typeVersion": 1.1}, {"id": "f779047b-6dec-4e4e-ae09-4dd91f961d08", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [380, 240], "parameters": {"width": 1224.*************, "height": 1282.************, "content": "# Template for Kids' Story in Arabic\n\nThe n8n template for creating kids' stories in Arabic provides a versatile platform for storytellers to captivate young audiences with educational and interactive tales. Along with its core functionalities, this template allows for customization to suit various use cases and can be set up effortlessly.\n\nCheck this example: [https://t.me/st0ries95](https://t.me/st0ries95)\n\n\n## Node Functionalities\n\n\n## Automated Storytelling Process\n\n\n## Use Cases\n1. **Educational Platforms**:\n Educational platforms can automate the creation and distribution of educational stories in Arabic for children using this template. By incorporating visual and auditory elements into the storytelling process, educational platforms can enhance learning experiences and engage young learners effectively.\n\n2. **Children's Libraries**:\n Children's libraries can utilize this template to curate and share a diverse collection of Arabic stories with young readers. The automated generation of visual content and audio files enhances the storytelling experience, encouraging children to immerse themselves in new worlds and characters through captivating narratives.\n\n3. **Language Learning Apps**:\n Language learning apps focused on Arabic can integrate this template to offer culturally rich storytelling experiences for children learning the language. By translating stories into Arabic and supplementing them with visual and auditory components, these apps can facilitate language acquisition in an enjoyable and interactive manner.\n\n## Configuration Guide for Nodes\n\n### OpenAI Chat Model Nodes:\n- **Credentials**: Provide the necessary API credentials for the OpenAI GPT-4 Turbo model.\n- **Options**: Configure any specific options required for the chat model.\n\n### Create a Prompt for DALL-E Node:\n- **Prompts Customization**: Customize prompts to generate relevant visual content for the stories.\n- **Summarization Method and Prompts**: Define the summarization method and prompts for generating visual content without text.\n\n### Generate an Image for the Story Node:\n- **Resource**: Specify the type of resource (image).\n- **Prompt**: Set up the prompt for producing an image without text within the visual content.\n\n### Generate Audio for the Story Node:\n- **Resource**: Select the type of resource (audio).\n- **Input**: Define the input text for generating audio files.\n\n### Translate the Story to Arabic Node:\n- **Chunking Mode**: Choose the chunking mode (advanced).\n- **Summarization Method and Prompts**: Set the summarization method and prompts for translating the story into Arabic.\n\n### Send the Story To Channel Node:\n- **Chat ID**: Provide the chat ID where the story text will be sent.\n- **Text**: Configure the text to be sent to the channel.\n\nBy configuring each node as per the guidelines above, users can effectively set up and customize the n8n template for kids' stories in Arabic, tailoring it to specific use cases and delivering a seamless and engaging storytelling experience for young audiences.\n"}, "typeVersion": 1}, {"id": "5ef92ebc-e4e4-4165-a7df-9f94802f8e27", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1620, 240], "parameters": {"width": 1811.9647367735226, "height": 1280.7253282813103, "content": ""}, "typeVersion": 1}, {"id": "76d2b256-8083-42d9-8465-63b2f9c73a67", "name": "Translate the Story to Arabic", "type": "@n8n/n8n-nodes-langchain.chainSummarization", "position": [2400, 480], "parameters": {"options": {"summarizationMethodAndPrompts": {"values": {"prompt": "Translate this story texts to \"Arabic\" and make it easy to understands for kids with easy words and moral lesson :\n\n\n\"{text}\"\n\n\n", "summarizationMethod": "stuff"}}}, "chunkingMode": "advanced"}, "executeOnce": true, "typeVersion": 2}, {"id": "126e463e-f1e8-4cd2-856d-aaaebc279797", "name": "Send Audio to the Channel", "type": "n8n-nodes-base.telegram", "position": [3180, 820], "parameters": {"chatId": "-**********", "operation": "sendAudio", "binaryData": true, "additionalFields": {"caption": "نهاية القصة ... "}}, "credentials": {"telegramApi": {"id": "k3RE6o9brmFRFE9p", "name": "Telegram account"}}, "typeVersion": 1.1}, {"id": "162049a0-620a-4044-966a-27b665827b60", "name": "Create a Story for Kids", "type": "@n8n/n8n-nodes-langchain.chainSummarization", "position": [1980, 960], "parameters": {"options": {"summarizationMethodAndPrompts": {"values": {"prompt": "Create a captivating short tale for kids, whisking them away to magical lands brimming with wisdom. Explore diverse themes in a fun and simple way, weaving in valuable messages. Dive into cultural adventures with lively language that sparks curiosity. Let your story inspire young minds through enchanting narratives that linger long after the last word. Begin crafting your imaginative tale now! (Approximately 900 characters)\n\n\n\"{text}\"\n\nCONCISE SUMMARY:", "summarizationMethod": "stuff"}}}, "chunkingMode": "advanced"}, "executeOnce": true, "typeVersion": 2}], "pinData": {}, "connections": {"Schedule Trigger": {"main": [[{"node": "Create a Story for Kids", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Create a Story for Kids", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Translate the Story to Arabic", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model2": {"ai_languageModel": [[{"node": "Create a Prompt for DALL-E", "type": "ai_languageModel", "index": 0}]]}, "Create a Story for Kids": {"main": [[{"node": "Translate the Story to Arabic", "type": "main", "index": 0}, {"node": "Create a Prompt for DALL-E", "type": "main", "index": 0}]]}, "Create a Prompt for DALL-E": {"main": [[{"node": "Generate an Image for the Story", "type": "main", "index": 0}]]}, "Generate Audio for the Story": {"main": [[{"node": "Send Audio to the Channel", "type": "main", "index": 0}]]}, "Translate the Story to Arabic": {"main": [[{"node": "Send the Story To Channel", "type": "main", "index": 0}, {"node": "Generate Audio for the Story", "type": "main", "index": 0}]]}, "Generate an Image for the Story": {"main": [[{"node": "Send Image to the Channel", "type": "main", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Create a Story for Kids", "type": "ai_textSplitter", "index": 0}]]}}}