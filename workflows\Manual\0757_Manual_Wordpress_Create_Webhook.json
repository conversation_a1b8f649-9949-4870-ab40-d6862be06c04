{"meta": {"instanceId": "c911aed9995230b93fd0d9bc41c258d697c2fe97a3bab8c02baf85963eeda618", "templateCredsSetupCompleted": true}, "nodes": [{"id": "468084ed-ce7d-45c5-bf27-ea9c91d5898a", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [0, 0], "parameters": {}, "typeVersion": 1}, {"id": "fbde6cfe-9fac-46d2-958a-f42c9ef383a3", "name": "Retrieve WordPress Article", "type": "n8n-nodes-base.wordpress", "position": [440, 0], "parameters": {"postId": "1032", "options": {}, "operation": "get"}, "credentials": {"wordpressApi": {"id": "T0ygUN7hNFQVztP2", "name": "Wordpress account 2"}}, "typeVersion": 1}, {"id": "54241e39-7a5f-45f4-9dab-72b5424f4061", "name": "Generate Summary or Transcription", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [680, 0], "parameters": {"text": "={{ $json.content }}", "messages": {"messageValues": [{"message": "Summarize or transcribe this article, depending on the workflow setting."}]}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.5}, {"id": "49cfaab6-a0c1-4319-904d-c1e0a2c6aa91", "name": "Generate Speech", "type": "n8n-nodes-base.httpRequest", "position": [1120, 0], "parameters": {"url": "https://api.elevenlabs.io/v1/text-to-speech/voice_id", "method": "POST", "options": {}, "sendBody": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "text", "value": "={{ $json.text }}"}, {"name": "model_id", "value": "eleven_multilingual_v2"}, {"name": "output_format", "value": "mp3_44100_128"}]}, "genericAuthType": "httpCustomAuth"}, "credentials": {"httpCustomAuth": {"id": "wUJksQ68RUH0XuTO", "name": "Custom Auth account"}}, "typeVersion": 4.2}, {"id": "899abf3f-4ab6-48bd-90ba-0502cb23348e", "name": "Upload MP3", "type": "n8n-nodes-base.httpRequest", "position": [2060, 0], "parameters": {"url": "={{ $('settings').item.json['site_url'] }}wp-json/wp/v2/media", "method": "POST", "options": {}, "sendBody": true, "contentType": "binaryData", "sendHeaders": true, "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{"name": "Content-Disposition", "value": "=attachment; filename=\"{{ $('Retrieve WordPress Article').item.json.slug }}.mp3\""}]}, "inputDataFieldName": "data", "nodeCredentialType": "wordpressApi"}, "credentials": {"wordpressApi": {"id": "T0ygUN7hNFQVztP2", "name": "Wordpress account 2"}}, "retryOnFail": true, "typeVersion": 4.2}, {"id": "590297c9-1f66-4071-8b47-230b08c379d4", "name": "Update WordPress Post", "type": "n8n-nodes-base.wordpress", "position": [2300, 0], "parameters": {"postId": "={{ $('Retrieve WordPress Article').item.json.id }}", "operation": "update", "updateFields": {"content": "=<!-- wp:audio {\"id\":{{ $json.id }}} -->\n<figure class=\"wp-block-audio\"><audio controls src=\"{{ $json.guid.rendered }}\"></audio><figcaption class=\"wp-element-caption\">🗣️ Listen to the summary or transcription. 👆</figcaption></figure>\n<!-- /wp:audio --><br>{{ $('Retrieve WordPress Article').item.json.content.rendered }}"}}, "credentials": {"wordpressApi": {"id": "T0ygUN7hNFQVztP2", "name": "Wordpress account 2"}}, "typeVersion": 1}, {"id": "5297d517-5dd9-4d4d-b201-0822af030c95", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1320, -340], "parameters": {"color": 6, "width": 660, "height": 1000, "content": "## 🎙️ Generate Text-to-Speech Using Eleven Labs via API\n\nSince there is no predefined node for Eleven Labs in n8n, we will use the **HTTP Request** module.\n\n### 🛠️ Prerequisites:\n1. **Get an API Key**: Visit [Eleven Labs](https://try.elevenlabs.io/text-audio) to obtain your API key.\n2. **Choose a Suitable Voice**: Test different voices on [this demo page](https://try.elevenlabs.io/text-audio) to find the best fit for your use case.\n3. **Select the Right Model**: For multilingual usage, use:  \n   ~~~json\n   \"model_id\": \"eleven_multilingual_v2\"\n   ~~~\n4. **Set Output Format**: You can adjust the quality by modifying `output_format`, for example:  \n   ~~~json\n   \"output_format\": \"mp3_44100_128\"\n   ~~~\n\n📖 Refer to the full API documentation: [API Reference - Eleven Labs](https://try.elevenlabs.io/api-reference-text-to-speech)\n\n---\n## 🚀 Step 1: Configure API Credentials in n8n\n\nAdd a custom authentication entry in n8n with the following structure: \n\n(Replace `\"your-elevenlabs-api-key\"` with your **actual API key**)\n\n~~~json\n{\n  \"headers\": {\n    \"xi-api-key\": \"your-elevenlabs-api-key\"\n  }\n}\n~~~\n---\n\n## 📩 Step 2: Send a POST Request to the API\n\nMake an HTTP POST request to the **webhook** of your workflow with the following parameters:\n\n- **`voice_id`**: The ID of the selected voice.\n- **`text`**: The text to convert into speech.\n\n---"}, "typeVersion": 1}, {"id": "8fecbb98-8120-4d94-82ce-15efa063394b", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [640, -340], "parameters": {"width": 460, "height": 280, "content": "# Modify This Prompt\n\nHere you can modify this prompt. It is interesting because the neutral node might return HTML, and using a ChatGPT node allows you to clean or customize the output before sending it to text-to-speech.\n\nIn the example provided, I requested a summary. However, you could ask for the benefits or product advantages when using it for e-commerce or affiliate marketing. You could also request the full transcription of the article."}, "typeVersion": 1}, {"id": "06e66119-2b95-416b-8167-41dccbbd8612", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [640, 220], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "yekgKa01FVKc8Etr", "name": "OpenAi account 2"}}, "typeVersion": 1.2}, {"id": "********-b8f5-45f3-8e37-66365ba62422", "name": "settings", "type": "n8n-nodes-base.set", "position": [220, 0], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "10c07d50-1310-4dd7-a143-b0c0e5cf1b70", "name": "site_url", "type": "string", "value": "https://mydomain.com/"}]}}, "typeVersion": 3.4}], "pinData": {}, "connections": {"settings": {"main": [[{"node": "Retrieve WordPress Article", "type": "main", "index": 0}]]}, "Upload MP3": {"main": [[{"node": "Update WordPress Post", "type": "main", "index": 0}]]}, "Generate Speech": {"main": [[{"node": "Upload MP3", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Generate Summary or Transcription", "type": "ai_languageModel", "index": 0}]]}, "Retrieve WordPress Article": {"main": [[{"node": "Generate Summary or Transcription", "type": "main", "index": 0}]]}, "Generate Summary or Transcription": {"main": [[{"node": "Generate Speech", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "settings", "type": "main", "index": 0}]]}}}