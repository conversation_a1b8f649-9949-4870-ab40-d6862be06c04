{"id": "eMxH0GjgfWEvBDic", "meta": {"instanceId": "be27b2af86ae3a5dc19ef2a1947644c0aec45fd8c88f29daa7dea6f0ce537691"}, "name": "HR Job Posting and Evaluation with AI", "tags": [{"id": "9ZApRtWeNXlymyQ6", "name": "HR", "createdAt": "2025-01-08T08:47:43.054Z", "updatedAt": "2025-01-08T08:47:43.054Z"}], "nodes": [{"id": "450e15b2-bddf-4853-b44e-822facaac14d", "name": "On form submission", "type": "n8n-nodes-base.formTrigger", "position": [-700, -80], "webhookId": "18f7428c-9990-413f-aff3-bdcca1bbbe2d", "parameters": {"options": {"path": "automation-specialist-application", "ignoreBots": false, "buttonLabel": "Submit", "appendAttribution": false, "useWorkflowTimezone": true}, "formTitle": "Job Application", "formFields": {"values": [{"fieldLabel": "First Name", "requiredField": true}, {"fieldLabel": "Last Name", "requiredField": true}, {"fieldType": "email", "fieldLabel": "Email", "requiredField": true}, {"fieldType": "number", "fieldLabel": "Phone", "requiredField": true}, {"fieldType": "number", "fieldLabel": "Years of experience", "requiredField": true}, {"fieldType": "file", "fieldLabel": "Upload your CV", "requiredField": true, "acceptFileTypes": ".pdf"}]}, "formDescription": "=Fill this for to apply for the role Automation Specialist:\n\nLocation: Remote\nExperience: Minimum 3 years\nEmployment Type: Full-time\n\nJob Description:\nWe are seeking a highly skilled Automation Specialist with at least 3 years of experience in designing and implementing workflow automation solutions. The ideal candidate will have expertise in tools such as n8n, Zapier, Make.com, or similar platforms, and a strong background in integrating APIs, streamlining processes, and enhancing operational efficiency.\n\nKey Responsibilities:\n\n    Develop and implement automated workflows to optimize business processes.\n    Integrate third-party APIs and systems to create seamless data flow.\n    Analyze, debug, and improve existing automation setups.\n    Collaborate with cross-functional teams to identify automation opportunities.\n    Monitor and maintain automation systems to ensure reliability.\n\nRequired Skills & Qualifications:\n\n    Proven 3+ years of experience in workflow automation and integration.\n    Proficiency with tools like n8n, Zapier, or Make.com.\n    Strong understanding of APIs, webhooks, and data transformation.\n    Familiarity with scripting languages (e.g., JavaScript or Python).\n    Excellent problem-solving and communication skills.\n\nPreferred Qualifications:\n\n    Experience with database management and cloud services.\n    Background in business process analysis or RPA tools.\n\nWhy Join Us?\n\n    Opportunity to work on cutting-edge automation projects.\n    Supportive and collaborative team environment.\n    Competitive salary and benefits package."}, "typeVersion": 2.2}, {"id": "5005e9ba-a68a-4795-8a65-22374a182bdb", "name": "Airtable", "type": "n8n-nodes-base.airtable", "position": [-60, -80], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appublMkWVQfHkZ09", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09", "cachedResultName": "Simple applicant tracker"}, "table": {"__rl": true, "mode": "list", "value": "tblllvQaRTSnEr17a", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09/tblllvQaRTSnEr17a", "cachedResultName": "Applicants"}, "columns": {"value": {"Name": "={{ $json.Name }}", "Phone": "={{ $json.Phone }}", "CV Link": "={{ $json[\"CV link\"] }}", "Applying for": "=[\"Automation Specialist\"]", "Email address": "={{ $json.email }}"}, "schema": [{"id": "Name", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Email address", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Email address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Phone", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Stage", "type": "options", "display": true, "options": [{"name": "No hire", "value": "No hire"}, {"name": "Interviewing", "value": "Interviewing"}, {"name": "Decision needed", "value": "Decision needed"}, {"name": "<PERSON>re", "value": "<PERSON>re"}], "removed": true, "readOnly": false, "required": false, "displayName": "Stage", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Applying for", "type": "array", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Applying for", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CV Link", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "CV Link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "JD CV score", "type": "options", "display": true, "options": [{"name": "0 – No hire", "value": "0 – No hire"}, {"name": "1 – Probably no hire", "value": "1 – Probably no hire"}, {"name": "2 – Worth consideration", "value": "2 – Worth consideration"}, {"name": "3 – Good candidate", "value": "3 – Good candidate"}, {"name": "4 – Please hire this person", "value": "4 – Please hire this person"}], "removed": true, "readOnly": false, "required": false, "displayName": "JD CV score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview", "type": "dateTime", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interviewer", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interviewer", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview score", "type": "options", "display": true, "options": [{"name": "0 – No hire", "value": "0 – No hire"}, {"name": "1 – Probably no hire", "value": "1 – Probably no hire"}, {"name": "2 – Worth consideration", "value": "2 – Worth consideration"}, {"name": "3 – Good candidate", "value": "3 – Good candidate"}, {"name": "4 – Please hire this person", "value": "4 – Please hire this person"}], "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview notes", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview", "type": "dateTime", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interviewer", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interviewer", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview score", "type": "options", "display": true, "options": [{"name": "0 – No hire", "value": "0 – No hire"}, {"name": "1 – Probably no hire", "value": "1 – Probably no hire"}, {"name": "2 – Worth consideration", "value": "2 – Worth consideration"}, {"name": "3 – Good candidate", "value": "3 – Good candidate"}, {"name": "4 – Please hire this person", "value": "4 – Please hire this person"}], "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview notes", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Attachments", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Attachments", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": []}, "options": {"typecast": true}, "operation": "create"}, "credentials": {"airtableTokenApi": {"id": "gQtK3HX661rFA6KW", "name": "gaturanjenga account"}}, "typeVersion": 2.1}, {"id": "b291527b-9937-4388-a712-2b60dd292f65", "name": "Upload CV to google drive", "type": "n8n-nodes-base.googleDrive", "position": [-480, -80], "parameters": {"name": "={{ $binary.Upload_your_CV.fileName }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "1u_YBpqSU5TjNsu72sQKFMIesb62JKHXz", "cachedResultUrl": "https://drive.google.com/drive/folders/1u_YBpqSU5TjNsu72sQKFMIesb62JKHXz", "cachedResultName": "HR Test"}, "inputDataFieldName": "Upload_your_CV"}, "credentials": {"googleDriveOAuth2Api": {"id": "MHcgKR744VHXSe3X", "name": "Drive n8n"}}, "typeVersion": 3}, {"id": "83a965f9-bdb1-42ca-9701-24a82438ea0e", "name": "applicant details", "type": "n8n-nodes-base.set", "position": [-260, -80], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "bffff778-859a-4bb8-b973-39237ce7486e", "name": "Name", "type": "string", "value": "={{ $('On form submission').item.json['First Name'] + \" \" + $('On form submission').item.json['Last Name'] }}"}, {"id": "cd6e7372-c65f-4e6f-9612-6ea513bb8e15", "name": "Phone", "type": "number", "value": "={{ $('On form submission').item.json.Phone }}"}, {"id": "eb19138e-7ff3-4f0c-ad95-ac33f8835717", "name": "email", "type": "string", "value": "={{ $('On form submission').item.json.Email }}"}, {"id": "25172db9-91fb-45da-b036-ee9aea1e8b09", "name": "Experience", "type": "number", "value": "={{ $('On form submission').item.json[\"Years of experience\"] }}"}, {"id": "64393285-3770-47e0-bbbb-3c5d5e14f1f4", "name": "Applied On", "type": "string", "value": "={{ $('On form submission').item.json.submittedAt }}"}, {"id": "dc052fd6-f57d-4da1-9976-67fcd9496e58", "name": "CV link", "type": "string", "value": "={{ $json.webViewLink }}"}]}}, "typeVersion": 3.4}, {"id": "41038c1c-876d-46a6-9dcc-f40c77e834df", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-720, -160], "parameters": {"color": 3, "width": 760, "height": 220, "content": "## Grab User Details and Update in Airtable\n"}, "typeVersion": 1}, {"id": "d0f85487-8e78-4cde-8ecb-a55ab94940cc", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [120, -180], "parameters": {"width": 820, "height": 460, "content": "## Download the CV and get the job description and requirements.\n- ### Send the details to ChatGPT to score the viability of the candidate"}, "typeVersion": 1}, {"id": "334c4580-a0e6-45f0-9b3a-3904eb80b3e8", "name": "download CV", "type": "n8n-nodes-base.googleDrive", "position": [140, -80], "parameters": {"fileId": {"__rl": true, "mode": "url", "value": "={{ $json.fields[\"CV Link\"] }}"}, "options": {}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "MHcgKR744VHXSe3X", "name": "Drive n8n"}}, "typeVersion": 3}, {"id": "b7d8013a-71bd-49a4-a58f-f63186e1b6d8", "name": "Extract from File", "type": "n8n-nodes-base.extractFromFile", "position": [360, -80], "parameters": {"options": {}, "operation": "pdf"}, "typeVersion": 1}, {"id": "22ba7844-9f20-41b1-96bb-f2e33e18d14a", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [580, -80], "parameters": {"text": "=Compare the following job description and resume. Assign a qualification score between 0 and 1, where 1 indicates the best match. Provide only the score and the reason for the score in less than 20 words.\nJob Description: Use Airtable tool to get the job description\nResume: \n{{ $json.text }}", "options": {}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.7}, {"id": "5f0317cb-35a5-4e57-938d-0d604c1f7f4f", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [500, 120], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "0Q6M4JEKewP9VKl8", "name": "Bulkbox"}}, "typeVersion": 1}, {"id": "d040091b-282b-4bb7-8a82-de3030c14b91", "name": "Airtable1", "type": "n8n-nodes-base.airtableTool", "position": [700, 120], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appublMkWVQfHkZ09", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09", "cachedResultName": "Simple applicant tracker"}, "table": {"__rl": true, "mode": "list", "value": "tbljhmLdPULqSya0d", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09/tbljhmLdPULqSya0d", "cachedResultName": "Positions"}, "options": {}, "operation": "search"}, "credentials": {"airtableTokenApi": {"id": "gQtK3HX661rFA6KW", "name": "gaturanjenga account"}}, "typeVersion": 2.1}, {"id": "fba48717-a068-44de-a776-6e0c14ebd667", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [820, 120], "parameters": {"jsonSchemaExample": "{\n  \"score\": 0.8,\n  \"reason\": \"Does not meet required number of experience in years\"\n}"}, "typeVersion": 1.2}, {"id": "2eef8181-3e4d-4c66-acd7-d440eb2f6748", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [960, -340], "parameters": {"color": 2, "width": 1200, "height": 600, "content": "## Update Airtable with score and reason for the score\n\n- ### if score is above 0.7, shortlist and continue flow.\n\n## Get questionnaires based on the JD and CV\n\n- ### Update the responses in Airtable"}, "typeVersion": 1}, {"id": "ed42fa6c-be05-4d62-aa1f-390b5fc471dd", "name": "shortlisted?", "type": "n8n-nodes-base.if", "position": [960, -80], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "7b4950b2-d218-4911-89cd-22a60b7465d8", "operator": {"type": "number", "operation": "gte"}, "leftValue": "={{ $json.output.score }}", "rightValue": 0.7}]}}, "typeVersion": 2.2}, {"id": "6df70bee-6a9f-43f6-8c39-46663b572f5c", "name": "Rejected", "type": "n8n-nodes-base.airtable", "position": [1240, 60], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appublMkWVQfHkZ09", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09", "cachedResultName": "Simple applicant tracker"}, "table": {"__rl": true, "mode": "list", "value": "tblllvQaRTSnEr17a", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09/tblllvQaRTSnEr17a", "cachedResultName": "Applicants"}, "columns": {"value": {"id": "={{ $('Airtable').item.json.id }}", "Stage": "No hire", "JD CV score": "={{ $json.output.score }}", "CV Score Notes": "={{ $json.output.reason }}"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "Name", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Email address", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Email address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone", "type": "number", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Stage", "type": "options", "display": true, "options": [{"name": "No hire", "value": "No hire"}, {"name": "Interviewing", "value": "Interviewing"}, {"name": "Decision needed", "value": "Decision needed"}, {"name": "<PERSON>re", "value": "<PERSON>re"}], "removed": false, "readOnly": false, "required": false, "displayName": "Stage", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Applying for", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Applying for", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CV Link", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "CV Link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "JD CV score", "type": "number", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "JD CV score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CV Score Notes", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "CV Score Notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview", "type": "dateTime", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interviewer", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interviewer", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview score", "type": "options", "display": true, "options": [{"name": "0 – No hire", "value": "0 – No hire"}, {"name": "1 – Probably no hire", "value": "1 – Probably no hire"}, {"name": "2 – Worth consideration", "value": "2 – Worth consideration"}, {"name": "3 – Good candidate", "value": "3 – Good candidate"}, {"name": "4 – Please hire this person", "value": "4 – Please hire this person"}], "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview notes", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview", "type": "dateTime", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interviewer", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interviewer", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview score", "type": "options", "display": true, "options": [{"name": "0 – No hire", "value": "0 – No hire"}, {"name": "1 – Probably no hire", "value": "1 – Probably no hire"}, {"name": "2 – Worth consideration", "value": "2 – Worth consideration"}, {"name": "3 – Good candidate", "value": "3 – Good candidate"}, {"name": "4 – Please hire this person", "value": "4 – Please hire this person"}], "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview notes", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Attachments", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Attachments", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["id"]}, "options": {}, "operation": "update"}, "credentials": {"airtableTokenApi": {"id": "gQtK3HX661rFA6KW", "name": "gaturanjenga account"}}, "typeVersion": 2.1}, {"id": "888869bb-6fca-4d91-8428-cf5159d410e3", "name": "Potential Hire", "type": "n8n-nodes-base.airtable", "position": [1240, -140], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appublMkWVQfHkZ09", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09", "cachedResultName": "Simple applicant tracker"}, "table": {"__rl": true, "mode": "list", "value": "tblllvQaRTSnEr17a", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09/tblllvQaRTSnEr17a", "cachedResultName": "Applicants"}, "columns": {"value": {"id": "={{ $('Airtable').item.json.id }}", "Stage": "Interviewing", "JD CV score": "={{ $json.output.score }}", "CV Score Notes": "={{ $json.output.reason }}"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "Name", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Email address", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Email address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone", "type": "number", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Stage", "type": "options", "display": true, "options": [{"name": "No hire", "value": "No hire"}, {"name": "Interviewing", "value": "Interviewing"}, {"name": "Decision needed", "value": "Decision needed"}, {"name": "<PERSON>re", "value": "<PERSON>re"}], "removed": false, "readOnly": false, "required": false, "displayName": "Stage", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Applying for", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Applying for", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CV Link", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "CV Link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "JD CV score", "type": "number", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "JD CV score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CV Score Notes", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "CV Score Notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview", "type": "dateTime", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interviewer", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interviewer", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview score", "type": "options", "display": true, "options": [{"name": "0 – No hire", "value": "0 – No hire"}, {"name": "1 – Probably no hire", "value": "1 – Probably no hire"}, {"name": "2 – Worth consideration", "value": "2 – Worth consideration"}, {"name": "3 – Good candidate", "value": "3 – Good candidate"}, {"name": "4 – Please hire this person", "value": "4 – Please hire this person"}], "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview notes", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview", "type": "dateTime", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interviewer", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interviewer", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview score", "type": "options", "display": true, "options": [{"name": "0 – No hire", "value": "0 – No hire"}, {"name": "1 – Probably no hire", "value": "1 – Probably no hire"}, {"name": "2 – Worth consideration", "value": "2 – Worth consideration"}, {"name": "3 – Good candidate", "value": "3 – Good candidate"}, {"name": "4 – Please hire this person", "value": "4 – Please hire this person"}], "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview notes", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Attachments", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Attachments", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["id"]}, "options": {}, "operation": "update"}, "credentials": {"airtableTokenApi": {"id": "gQtK3HX661rFA6KW", "name": "gaturanjenga account"}}, "typeVersion": 2.1}, {"id": "8f59889d-dff7-4eef-85f4-7c6d9e171c17", "name": "Airtable2", "type": "n8n-nodes-base.airtableTool", "position": [1560, 100], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appublMkWVQfHkZ09", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09", "cachedResultName": "Simple applicant tracker"}, "table": {"__rl": true, "mode": "list", "value": "tbljhmLdPULqSya0d", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09/tbljhmLdPULqSya0d", "cachedResultName": "Positions"}, "options": {}, "operation": "search"}, "credentials": {"airtableTokenApi": {"id": "gQtK3HX661rFA6KW", "name": "gaturanjenga account"}}, "typeVersion": 2.1}, {"id": "8358ab12-a0b9-4a21-b9eb-7054716b6f5b", "name": "generate questionnaires", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1460, -140], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "messages": {"values": [{"content": "=Given the following job description and candidate CV, create 5 insightful interview questions to gather more information about the candidate's suitability for the role. The questions should focus on:\n\n    Specific projects the candidate has worked on.\n    Key responsibilities and achievements in their previous roles.\n    Skills relevant to the job description.\n    Problem-solving abilities and how they handled challenges.\n    Alignment with the company’s goals and values.\n\nProvide the questions in a clear, concise format.\n\nJob Description:\nUse the airtable tool to get the job description\n\nCandidate CV:\n{{ $('Extract from File').item.json.text }}"}]}, "jsonOutput": true}, "credentials": {"openAiApi": {"id": "lcpI0YZU9bebg3uW", "name": "OpenAi account"}}, "typeVersion": 1.7}, {"id": "21ffd179-42d9-4da3-9f1b-e2bbeb9cdee7", "name": "questionnaires", "type": "n8n-nodes-base.form", "position": [1820, -140], "webhookId": "3f654280-b5d0-4392-824f-bc384d91a1df", "parameters": {"options": {"formTitle": "Questionnaires", "buttonLabel": "Submit", "formDescription": "Kindly fill in the following questions to proceed."}, "formFields": {"values": [{"fieldLabel": "={{ $json.message.content.interview_questions[0].question }}", "requiredField": true}, {"fieldLabel": "={{ $json.message.content.interview_questions[1].question }}", "requiredField": true}, {"fieldLabel": "={{ $json.message.content.interview_questions[2].question }}", "requiredField": true}, {"fieldLabel": "={{ $json.message.content.interview_questions[3].question }}", "requiredField": true}, {"fieldLabel": "={{ $json.message.content.interview_questions[4].question }}", "requiredField": true}]}}, "typeVersion": 1}, {"id": "29a228ca-6b8e-458f-a030-372b50151a94", "name": "update questionnaires", "type": "n8n-nodes-base.airtable", "position": [2040, -140], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appublMkWVQfHkZ09", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09", "cachedResultName": "Simple applicant tracker"}, "table": {"__rl": true, "mode": "list", "value": "tblllvQaRTSnEr17a", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09/tblllvQaRTSnEr17a", "cachedResultName": "Applicants"}, "columns": {"value": {"id": "={{ $('Airtable').item.json.id }}", "Questonnaires and responses": "={{ $('generate questionnaires').item.json.message.content.interview_questions[0].question }}: {{ $json['Can you describe one of the most complex automation projects you worked on, particularly detailing your role and the technologies you used?'] }}\n\n\n{{ $('generate questionnaires').item.json.message.content.interview_questions[1].question }}: {{ $json['What specific achievements in your previous roles do you believe demonstrate your ability to meet the responsibilities listed in the Automation Specialist position?'] }}\n\n\n{{ $('generate questionnaires').item.json.message.content.interview_questions[2].question }}: {{ $json['Given your experience with automation tools like n8n and APIs, can you provide an example of how you\\'ve successfully integrated different systems to improve operational efficiency?'] }}\n\n\n{{ $('generate questionnaires').item.json.message.content.interview_questions[3].question }}: {{ $json['Describe a challenging situation you faced during a project, how you approached the problem, and what the outcome was.'] }}\n\n\n{{ $('generate questionnaires').item.json.message.content.interview_questions[4].question }}: {{ $json['How do your values and career goals align with our company\\'s mission to optimize and enhance automation solutions?'] }}\n\n"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "Name", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Email address", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Email address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone", "type": "number", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Stage", "type": "options", "display": true, "options": [{"name": "No hire", "value": "No hire"}, {"name": "Interviewing", "value": "Interviewing"}, {"name": "Decision needed", "value": "Decision needed"}, {"name": "<PERSON>re", "value": "<PERSON>re"}], "removed": true, "readOnly": false, "required": false, "displayName": "Stage", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Applying for", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Applying for", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CV Link", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "CV Link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "JD CV score", "type": "number", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "JD CV score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CV Score Notes", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "CV Score Notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Questonnaires and responses", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Questonnaires and responses", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview", "type": "dateTime", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interviewer", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interviewer", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview score", "type": "options", "display": true, "options": [{"name": "0 – No hire", "value": "0 – No hire"}, {"name": "1 – Probably no hire", "value": "1 – Probably no hire"}, {"name": "2 – Worth consideration", "value": "2 – Worth consideration"}, {"name": "3 – Good candidate", "value": "3 – Good candidate"}, {"name": "4 – Please hire this person", "value": "4 – Please hire this person"}], "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview notes", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview", "type": "dateTime", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interviewer", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interviewer", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview score", "type": "options", "display": true, "options": [{"name": "0 – No hire", "value": "0 – No hire"}, {"name": "1 – Probably no hire", "value": "1 – Probably no hire"}, {"name": "2 – Worth consideration", "value": "2 – Worth consideration"}, {"name": "3 – Good candidate", "value": "3 – Good candidate"}, {"name": "4 – Please hire this person", "value": "4 – Please hire this person"}], "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview notes", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Attachments", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Attachments", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["id"]}, "options": {}, "operation": "update"}, "credentials": {"airtableTokenApi": {"id": "gQtK3HX661rFA6KW", "name": "gaturanjenga account"}}, "typeVersion": 2.1}, {"id": "9a72a172-4272-4715-8e57-75ca010bc0e5", "name": "job_posting", "type": "n8n-nodes-base.airtableTool", "position": [2300, 100], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appublMkWVQfHkZ09", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09", "cachedResultName": "Simple applicant tracker"}, "table": {"__rl": true, "mode": "list", "value": "tbljhmLdPULqSya0d", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09/tbljhmLdPULqSya0d", "cachedResultName": "Positions"}, "options": {}, "operation": "search"}, "credentials": {"airtableTokenApi": {"id": "gQtK3HX661rFA6KW", "name": "gaturanjenga account"}}, "typeVersion": 2.1}, {"id": "28c210c8-5684-4683-a168-5a02b39eb0f2", "name": "candidate_insights", "type": "n8n-nodes-base.airtableTool", "position": [2420, 100], "parameters": {"id": "={{ $('update questionnaires').item.json.id }}", "base": {"__rl": true, "mode": "list", "value": "appublMkWVQfHkZ09", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09", "cachedResultName": "Simple applicant tracker"}, "table": {"__rl": true, "mode": "list", "value": "tblllvQaRTSnEr17a", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09/tblllvQaRTSnEr17a", "cachedResultName": "Applicants"}, "options": {}}, "credentials": {"airtableTokenApi": {"id": "gQtK3HX661rFA6KW", "name": "gaturanjenga account"}}, "typeVersion": 2.1}, {"id": "6e6f43f4-43a7-426f-b3c7-264a7980c771", "name": "Personalize email", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [2260, -140], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "GPT-4O"}, "options": {}, "messages": {"values": [{"content": "=Craft a personalized email to the interviewee, expressing interest in continuing the conversation over a phone call. The email should mention strengths or achievements from their CV or questionnaire responses, and include a polite request to have the phone conversation. Ensure the tone is professional and warm.\n\nProvide an output of \nTo:\nSubject:\nEmail Content:\n\nInputs:\n\n    The candidate's CV.\n    The job description.\n    The candidate's questionnaire responses stored in Airtable.\n\n\nExample email:\nDear [Candidate's Name],\n\nThank you for submitting your application and responses to the questionnaire for the [Job Title] position. We were impressed by [specific strength or achievement from their CV or questionnaire, e.g., \"your experience in automating workflows using n8n, which aligns closely with our goals\"].\n\nWe’d love to continue the conversation to discuss your experience further. \n\nLooking forward to speaking with you soon.\n\n\n\nNOTE: \nSign off the email with\n\n<PERSON><PERSON>,\n<PERSON>"}]}, "jsonOutput": true}, "credentials": {"openAiApi": {"id": "lcpI0YZU9bebg3uW", "name": "OpenAi account"}}, "typeVersion": 1.7}, {"id": "ee3f1a4e-d262-461d-93c5-9aed81de9825", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [2620, -140], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "b3d6e85e-c478-452d-aafc-c325dfbe2c9b", "name": "To", "type": "string", "value": "={{ $json.message.content.To }}"}, {"id": "f24eb1d5-fa61-48ce-8685-a0b2022bf576", "name": "Subject", "type": "string", "value": "={{ $json.message.content.Subject }}"}, {"id": "25de1423-b66a-4389-906f-8b0c9c1d3826", "name": "Email Content", "type": "string", "value": "={{ $json.message.content['Email Content'] }}"}]}}, "typeVersion": 3.4}, {"id": "7454b4ea-1b43-4a4a-8623-7848c13298c7", "name": "Send Email", "type": "n8n-nodes-base.emailSend", "position": [2840, -140], "parameters": {"text": "={{ $json['Email Content'] }}", "options": {"appendAttribution": false}, "subject": "={{ $json.Subject }}", "toEmail": "={{ $json.To }}", "fromEmail": "<EMAIL>", "emailFormat": "text"}, "credentials": {"smtp": {"id": "FRchTiFJGPeC5YNE", "name": "SMTP account"}}, "typeVersion": 2.1}, {"id": "92be970b-8514-4842-bbc9-f6680681df60", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [2220, -280], "parameters": {"color": 5, "width": 1340, "height": 480, "content": "## Personalize email and send\n\n## Schedule Meeting and update meeting time in AIrtable"}, "typeVersion": 1}, {"id": "38a7f43b-f7b2-4dda-8dea-045d637870e8", "name": "Book Meeting", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [3060, -140], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "GPT-4O"}, "options": {}, "messages": {"values": [{"content": "=Check the interviewer's calendar for available 30-minute time slots within working hours (8 AM - 5 PM) the next day. Schedule the meeting and confirm the time with the candidate. Ensure that the meeting time is aligned with the candidate's and interviewer's availability.\n\nInputs:\n\n    The interviewer's calendar for scheduling.\n    Today's date: {{ $today }}\n\nUse the calendar tool to book the meeting\n\n\nGive back the follwoing information:\nStart time:\nEnd time:"}]}, "jsonOutput": true}, "credentials": {"openAiApi": {"id": "lcpI0YZU9bebg3uW", "name": "OpenAi account"}}, "typeVersion": 1.7}, {"id": "b6a94b8c-8c92-49f2-931b-44d23f627152", "name": "Google Calendar", "type": "n8n-nodes-base.googleCalendarTool", "position": [3160, 80], "parameters": {"end": "={{ $fromAI(\"end_time\", \"The end time for the meeting\", \"string\", \"2025-01-01T09:00:00Z\") }}", "start": "={{ $fromAI(\"start_time\", \"The start time for the meeting\", \"string\", \"2025-01-01T09:00:00Z\") }}\n", "calendar": {"__rl": true, "mode": "list", "value": "<EMAIL>", "cachedResultName": "<EMAIL>"}, "additionalFields": {"location": "=Online"}}, "credentials": {"googleCalendarOAuth2Api": {"id": "nzPOQoEN0ibAA9xT", "name": "Google Calendar account"}}, "typeVersion": 1.2}, {"id": "9ff2433f-c2f8-4716-aa22-92fb1e4028dd", "name": "update phone meeting time", "type": "n8n-nodes-base.airtable", "position": [3440, -140], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appublMkWVQfHkZ09", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09", "cachedResultName": "Simple applicant tracker"}, "table": {"__rl": true, "mode": "list", "value": "tblllvQaRTSnEr17a", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09/tblllvQaRTSnEr17a", "cachedResultName": "Applicants"}, "columns": {"value": {"id": "={{ $('update questionnaires').item.json.id }}", "Phone interview": "={{ $json.message.content['Start time'] }}"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "Name", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Email address", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Email address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone", "type": "number", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Stage", "type": "options", "display": true, "options": [{"name": "No hire", "value": "No hire"}, {"name": "Interviewing", "value": "Interviewing"}, {"name": "Decision needed", "value": "Decision needed"}, {"name": "<PERSON>re", "value": "<PERSON>re"}], "removed": true, "readOnly": false, "required": false, "displayName": "Stage", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Applying for", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Applying for", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CV Link", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "CV Link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "JD CV score", "type": "number", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "JD CV score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CV Score Notes", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "CV Score Notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Questonnaires and responses", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Questonnaires and responses", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview", "type": "dateTime", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Phone interview", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interviewer", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interviewer", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview score", "type": "options", "display": true, "options": [{"name": "0 – No hire", "value": "0 – No hire"}, {"name": "1 – Probably no hire", "value": "1 – Probably no hire"}, {"name": "2 – Worth consideration", "value": "2 – Worth consideration"}, {"name": "3 – Good candidate", "value": "3 – Good candidate"}, {"name": "4 – Please hire this person", "value": "4 – Please hire this person"}], "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview notes", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview", "type": "dateTime", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interviewer", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interviewer", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview score", "type": "options", "display": true, "options": [{"name": "0 – No hire", "value": "0 – No hire"}, {"name": "1 – Probably no hire", "value": "1 – Probably no hire"}, {"name": "2 – Worth consideration", "value": "2 – Worth consideration"}, {"name": "3 – Good candidate", "value": "3 – Good candidate"}, {"name": "4 – Please hire this person", "value": "4 – Please hire this person"}], "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview notes", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Attachments", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Attachments", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["id"]}, "options": {}, "operation": "update"}, "credentials": {"airtableTokenApi": {"id": "gQtK3HX661rFA6KW", "name": "gaturanjenga account"}}, "typeVersion": 2.1}, {"id": "a9233b89-c4a4-4c68-bb88-ce34381f9c99", "name": "Screening Questions", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [3660, -140], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "GPT-4O"}, "options": {}, "messages": {"values": [{"content": "=Given the  job description, along with the candidate's CV and their responses to the questionnaires, generate a list of screening questions that will help gauge the candidate's suitability for the role. The questions should focus on understanding the candidate’s relevant experience, skills, and cultural fit. The questions should take into account both the job description and the candidate's background and responses. Provide a minimum of 5 questions.\n\nUse the tools to get the job description and the applicant's responses to the questionnaires.\n\nApplicant's CV:\n{{ $('Extract from File').item.json.text }}\n\n\nGive the output as various sentences as a paragraph with every new question in a new line:\nScreening Questions:"}]}, "jsonOutput": true}, "credentials": {"openAiApi": {"id": "lcpI0YZU9bebg3uW", "name": "OpenAi account"}}, "typeVersion": 1.7}, {"id": "de53c452-bd8f-4bdb-88a9-152f287bd796", "name": "job_posting1", "type": "n8n-nodes-base.airtableTool", "position": [3680, 80], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appublMkWVQfHkZ09", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09", "cachedResultName": "Simple applicant tracker"}, "table": {"__rl": true, "mode": "list", "value": "tbljhmLdPULqSya0d", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09/tbljhmLdPULqSya0d", "cachedResultName": "Positions"}, "options": {}, "operation": "search"}, "credentials": {"airtableTokenApi": {"id": "gQtK3HX661rFA6KW", "name": "gaturanjenga account"}}, "typeVersion": 2.1}, {"id": "dcca85af-d194-427c-83a1-3ef686e4e4c4", "name": "candidate_insights1", "type": "n8n-nodes-base.airtableTool", "position": [3880, 80], "parameters": {"id": "={{ $('update questionnaires').item.json.id }}", "base": {"__rl": true, "mode": "list", "value": "appublMkWVQfHkZ09", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09", "cachedResultName": "Simple applicant tracker"}, "table": {"__rl": true, "mode": "list", "value": "tblllvQaRTSnEr17a", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09/tblllvQaRTSnEr17a", "cachedResultName": "Applicants"}, "options": {}}, "credentials": {"airtableTokenApi": {"id": "gQtK3HX661rFA6KW", "name": "gaturanjenga account"}}, "typeVersion": 2.1}, {"id": "092bc9a2-7d22-436c-a625-f182a55caf06", "name": "screening questions", "type": "n8n-nodes-base.airtable", "position": [4240, -140], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appublMkWVQfHkZ09", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09", "cachedResultName": "Simple applicant tracker"}, "table": {"__rl": true, "mode": "list", "value": "tblllvQaRTSnEr17a", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09/tblllvQaRTSnEr17a", "cachedResultName": "Applicants"}, "columns": {"value": {"id": "={{ $('update phone meeting time').item.json.id }}", "Phne interview screening questions": "={{ $json['Screening Questions'] }}"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "Name", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Email address", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Email address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone", "type": "number", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Stage", "type": "options", "display": true, "options": [{"name": "No hire", "value": "No hire"}, {"name": "Interviewing", "value": "Interviewing"}, {"name": "Decision needed", "value": "Decision needed"}, {"name": "<PERSON>re", "value": "<PERSON>re"}], "removed": true, "readOnly": false, "required": false, "displayName": "Stage", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Applying for", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Applying for", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CV Link", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "CV Link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "JD CV score", "type": "number", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "JD CV score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CV Score Notes", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "CV Score Notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Questonnaires and responses", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Questonnaires and responses", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phne interview screening questions", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Phne interview screening questions", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interviewer", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interviewer", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview score", "type": "options", "display": true, "options": [{"name": "0 – No hire", "value": "0 – No hire"}, {"name": "1 – Probably no hire", "value": "1 – Probably no hire"}, {"name": "2 – Worth consideration", "value": "2 – Worth consideration"}, {"name": "3 – Good candidate", "value": "3 – Good candidate"}, {"name": "4 – Please hire this person", "value": "4 – Please hire this person"}], "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview notes", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview", "type": "dateTime", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interviewer", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interviewer", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview score", "type": "options", "display": true, "options": [{"name": "0 – No hire", "value": "0 – No hire"}, {"name": "1 – Probably no hire", "value": "1 – Probably no hire"}, {"name": "2 – Worth consideration", "value": "2 – Worth consideration"}, {"name": "3 – Good candidate", "value": "3 – Good candidate"}, {"name": "4 – Please hire this person", "value": "4 – Please hire this person"}], "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview notes", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Attachments", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Attachments", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["id"]}, "options": {}, "operation": "update"}, "credentials": {"airtableTokenApi": {"id": "gQtK3HX661rFA6KW", "name": "gaturanjenga account"}}, "typeVersion": 2.1}, {"id": "c466c71b-ab9d-41f0-9467-975f62a80ad6", "name": "Edit Fields1", "type": "n8n-nodes-base.set", "position": [4020, -140], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d51edc4a-60cd-41fe-8cc3-afc3c266d588", "name": "Screening Questions", "type": "string", "value": "={{ $json.message.content['Screening Questions'] }}"}]}}, "typeVersion": 3.4}, {"id": "4bfab808-9353-4293-8e21-f8ca64095aaa", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [3640, -200], "parameters": {"width": 720, "height": 420, "content": "## Generate Screening Questions and post to Airtable"}, "typeVersion": 1}, {"id": "9635d334-8ff7-4c16-813e-d91a5765c252", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-1300, -300], "parameters": {"width": 580, "height": 460, "content": "## Actions\n- ### Change the `Form Description` with the job description you are hiring for.\n- ### Make sure to check and change the prompts if need be to suit your use case.\n- ### Use the Simple Applicant Tracker template on Airtable to set up the tables required."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"timezone": "Africa/Nairobi", "callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1"}, "versionId": "64ab9bc5-f060-49e7-aa78-819114c88f5b", "connections": {"AI Agent": {"main": [[{"node": "shortlisted?", "type": "main", "index": 0}]]}, "Airtable": {"main": [[{"node": "download CV", "type": "main", "index": 0}]]}, "Airtable1": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Airtable2": {"ai_tool": [[{"node": "generate questionnaires", "type": "ai_tool", "index": 0}]]}, "Send Email": {"main": [[{"node": "Book Meeting", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Send Email", "type": "main", "index": 0}]]}, "download CV": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "job_posting": {"ai_tool": [[{"node": "Personalize email", "type": "ai_tool", "index": 0}]]}, "Book Meeting": {"main": [[{"node": "update phone meeting time", "type": "main", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "screening questions", "type": "main", "index": 0}]]}, "job_posting1": {"ai_tool": [[{"node": "Screening Questions", "type": "ai_tool", "index": 0}]]}, "shortlisted?": {"main": [[{"node": "Potential Hire", "type": "main", "index": 0}], [{"node": "Rejected", "type": "main", "index": 0}]]}, "Potential Hire": {"main": [[{"node": "generate questionnaires", "type": "main", "index": 0}]]}, "questionnaires": {"main": [[{"node": "update questionnaires", "type": "main", "index": 0}]]}, "Google Calendar": {"ai_tool": [[{"node": "Book Meeting", "type": "ai_tool", "index": 0}]]}, "Extract from File": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Personalize email": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "applicant details": {"main": [[{"node": "Airtable", "type": "main", "index": 0}]]}, "On form submission": {"main": [[{"node": "Upload CV to google drive", "type": "main", "index": 0}]]}, "candidate_insights": {"ai_tool": [[{"node": "Personalize email", "type": "ai_tool", "index": 0}]]}, "Screening Questions": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "candidate_insights1": {"ai_tool": [[{"node": "Screening Questions", "type": "ai_tool", "index": 0}]]}, "update questionnaires": {"main": [[{"node": "Personalize email", "type": "main", "index": 0}]]}, "generate questionnaires": {"main": [[{"node": "questionnaires", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "AI Agent", "type": "ai_outputParser", "index": 0}]]}, "Upload CV to google drive": {"main": [[{"node": "applicant details", "type": "main", "index": 0}]]}, "update phone meeting time": {"main": [[{"node": "Screening Questions", "type": "main", "index": 0}]]}}}