{"nodes": [{"name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [450, 300], "webhookId": "213324b6-b84d-42f9-af3b-42804cc71cd1", "parameters": {"path": "213324b6-b84d-42f9-af3b-42804cc71cd1", "options": {}, "httpMethod": "POST"}, "typeVersion": 1}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.pagerDuty", "position": [650, 300], "parameters": {"email": "<EMAIL>", "operation": "update", "incidentId": "={{$json[\"body\"][\"context\"][\"pagerduty_incident\"]}}", "updateFields": {"status": "acknowledged"}}, "credentials": {"pagerDutyApi": "<PERSON>r<PERSON><PERSON><PERSON>s"}, "typeVersion": 1}, {"name": "Mattermost", "type": "n8n-nodes-base.mattermost", "position": [850, 300], "parameters": {"message": "💪🏼 Incident status has been changed to Acknowledged on PagerDuty.", "channelId": "={{$node[\"Webhook\"].json[\"body\"][\"channel_id\"]}}", "attachments": [], "otherOptions": {}}, "credentials": {"mattermostApi": "Mattermost Credentials"}, "typeVersion": 1}], "connections": {"Webhook": {"main": [[{"node": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "PagerDuty": {"main": [[{"node": "Mattermost", "type": "main", "index": 0}]]}}}