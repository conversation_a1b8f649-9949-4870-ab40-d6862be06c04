{"id": "60", "name": "Create a post and update the post in WordPress", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [570, 260], "parameters": {}, "typeVersion": 1}, {"name": "Wordpress", "type": "n8n-nodes-base.wordpress", "position": [770, 260], "parameters": {"title": "created from n8n", "additionalFields": {}}, "credentials": {"wordpressApi": "wordpress"}, "typeVersion": 1}, {"name": "Wordpress1", "type": "n8n-nodes-base.wordpress", "position": [970, 260], "parameters": {"postId": "={{$node[\"Wordpress\"].json[\"id\"]}}", "operation": "update", "updateFields": {"content": "This post was created using the n8n workflow."}}, "credentials": {"wordpressApi": "wordpress"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Wordpress": {"main": [[{"node": "Wordpress1", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Wordpress", "type": "main", "index": 0}]]}}}