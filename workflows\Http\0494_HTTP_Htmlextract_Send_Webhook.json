{"nodes": [{"name": "IF", "type": "n8n-nodes-base.if", "position": [1050, 500], "parameters": {"conditions": {"string": [{"value1": "={{$node[\"HTML Extract Data\"].data[\"title\"]}}", "value2": "Show HN:", "operation": "contains"}]}}, "typeVersion": 1}, {"name": "Send Email", "type": "n8n-nodes-base.emailSend", "position": [1450, 400], "parameters": {"text": "={{$node[\"Function\"].data[\"emailText\"]}}", "options": {}, "subject": "Trending Show HN"}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [250, 500], "parameters": {"triggerTimes": {"item": [{"hour": 13}]}}, "typeVersion": 1}, {"name": "Function", "type": "n8n-nodes-base.function", "position": [1250, 400], "parameters": {"functionCode": "let emailText = 'Currently trending \"Show HN\":\\n\\n';\n\nfor (let item of items) {\n  emailText += `${item.json.rank} ${item.json.title}\\n${item.json.url}\\n\\n`;\n}\n\nreturn [{json: {emailText}}]\n"}, "typeVersion": 1}, {"name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [450, 500], "parameters": {"url": "https://news.ycombinator.com/", "options": {}, "responseFormat": "string"}, "typeVersion": 1}, {"name": "HTML Extract Items", "type": "n8n-nodes-base.htmlExtract", "position": [650, 500], "parameters": {"options": {}, "extractionValues": {"values": [{"key": "item", "cssSelector": "tr.athing", "returnArray": true, "returnValue": "html"}]}}, "typeVersion": 1}, {"name": "HTML Extract Data", "type": "n8n-nodes-base.htmlExtract", "position": [850, 500], "parameters": {"options": {}, "dataPropertyName": "item", "extractionValues": {"values": [{"key": "title", "cssSelector": "a"}, {"key": "url", "attribute": "href", "cssSelector": "a.storylink", "returnValue": "attribute"}, {"key": "rank", "cssSelector": ".rank"}]}}, "typeVersion": 1}], "connections": {"IF": {"main": [[{"node": "Function", "type": "main", "index": 0}]]}, "Cron": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "Function": {"main": [[{"node": "Send Email", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "HTML Extract Items", "type": "main", "index": 0}]]}, "HTML Extract Data": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}, "HTML Extract Items": {"main": [[{"node": "HTML Extract Data", "type": "main", "index": 0}]]}}}