{"nodes": [{"name": "Airtable", "type": "n8n-nodes-base.airtable", "position": [470, 200], "parameters": {"table": "Table 1", "operation": "list", "additionalOptions": {}}, "credentials": {"airtableApi": ""}, "typeVersion": 1}, {"name": "Mailcheck", "type": "n8n-nodes-base.mailcheck", "position": [670, 200], "parameters": {"email": "={{$json[\"fields\"][\"Email\"]}}"}, "credentials": {"mailcheckApi": "Mailcheck API Credentials"}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [870, 200], "parameters": {"values": {"string": [{"name": "ID", "value": "={{$node[\"Airtable\"].json[\"id\"]}}"}], "boolean": [{"name": "<PERSON><PERSON>", "value": "={{$json[\"mxExists\"]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "Airtable1", "type": "n8n-nodes-base.airtable", "position": [1070, 200], "parameters": {"id": "={{$json[\"ID\"]}}", "table": "=Table 1", "fields": ["<PERSON><PERSON>"], "options": {}, "operation": "update", "application": "={{$node[\"Airtable\"].parameter[\"application\"]}}", "updateAllFields": false}, "credentials": {"airtableApi": "Airtable Credentials n8n"}, "typeVersion": 1}], "connections": {"Set": {"main": [[{"node": "Airtable1", "type": "main", "index": 0}]]}, "Airtable": {"main": [[{"node": "Mailcheck", "type": "main", "index": 0}]]}, "Mailcheck": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}}