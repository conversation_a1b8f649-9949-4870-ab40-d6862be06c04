{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "01730710-e299-4e66-93e9-6079fdf9b8b7", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [2120, 0], "parameters": {"color": 6, "width": 903.0896125323785, "height": 733.5099670584011, "content": "## Step 2: Setup the Q&A \n### The incoming message from the webhook is queried from the Supabase Vector Store.  The response is provided in the response webhook.  "}, "typeVersion": 1}, {"id": "66aed89e-fd72-4067-82bf-d480be27e5d6", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [840, 140], "parameters": {}, "typeVersion": 1}, {"id": "9dc8f2a7-eeff-4a35-be52-05c42b71eee4", "name": "Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [1140, 140], "parameters": {"fileId": {"__rl": true, "mode": "list", "value": "1LZezppYrWpMStr4qJXtoIX-Dwzvgehll", "cachedResultUrl": "https://drive.google.com/file/d/1LZezppYrWpMStr4qJXtoIX-Dwzvgehll/view?usp=drivesdk", "cachedResultName": "crowdstrike.pdf"}, "options": {}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "yOwz41gMQclOadgu", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "1dd3d3fd-6c2e-4e23-9c82-b0d07b199de3", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1100, 0], "parameters": {"color": 6, "width": 772.*************, "height": 732.*************, "content": "## Step 1: Upserting the PDF\n### Fetch file from Google Drive, split it into chunks and insert into Supabase index\n\n"}, "typeVersion": 1}, {"id": "4796124f-bc12-4353-b7ea-ec8cd7653e68", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [0, 0], "parameters": {"color": 6, "width": 710.*************, "height": 726.*************, "content": "## Start here: Step-by Step Youtube Tutorial :star:\n\n[![Building an AI Crew to Analyze Financial Data with CrewAI and n8n](https://img.youtube.com/vi/pMvizUx5n1g/sddefault.jpg)](https://www.youtube.com/watch?v=pMvizUx5n1g)\n"}, "typeVersion": 1}, {"id": "1e2ecc88-c8c7-4687-a2a1-b20b0da9b772", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [1400, 320], "parameters": {"options": {"splitPages": true}, "dataType": "binary"}, "typeVersion": 1}, {"id": "6dd8545d-df8c-49ff-acf6-f8c150723ee8", "name": "Recursive Character Text Splitter1", "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "position": [1400, 460], "parameters": {"options": {}, "chunkSize": 3000, "chunkOverlap": 200}, "typeVersion": 1}, {"id": "6899e2d6-965a-40cd-a34f-a61de8fd32ef", "name": "Qdrant Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "position": [1480, 140], "parameters": {"mode": "insert", "options": {}, "qdrantCollection": {"__rl": true, "mode": "id", "value": "crowd"}}, "credentials": {"qdrantApi": {"id": "NyinAS3Pgfik66w5", "name": "QdrantApi account"}}, "typeVersion": 1.1}, {"id": "6136c6fb-3d20-44a7-ab00-6c5671bafa10", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "disabled": true, "position": [2180, 120], "webhookId": "551107fb-b349-4e2b-a888-febe5e282734", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "c970f654-4c79-4637-bec0-73f79a01ab59", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [2180, 320], "webhookId": "55b825ad-8987-4618-ae92-d9b08966324b", "parameters": {"path": "19f5499a-3083-4783-93a0-e8ed76a9f742", "options": {}, "httpMethod": "POST", "responseMode": "responseNode"}, "typeVersion": 2}, {"id": "e05e9046-de17-4ca1-b1ac-2502ee123e5f", "name": "Retrieval QA Chain", "type": "@n8n/n8n-nodes-langchain.chainRetrievalQa", "position": [2420, 120], "parameters": {"text": "={{ $json.chatInput || $json.body.input }}", "options": {}, "promptType": "define"}, "typeVersion": 1.5}, {"id": "ecf0d248-a8a9-45ed-8786-8864547f79b6", "name": "Vector Store Retriever", "type": "@n8n/n8n-nodes-langchain.retrieverVectorStore", "position": [2580, 320], "parameters": {"topK": 5}, "typeVersion": 1}, {"id": "4fb1d8ac-bc6f-4f99-965f-7d38ea0680e0", "name": "Qdrant Vector Store1", "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "position": [2540, 460], "parameters": {"options": {}, "qdrantCollection": {"__rl": true, "mode": "id", "value": "={{ $json.body.company }}"}}, "credentials": {"qdrantApi": {"id": "NyinAS3Pgfik66w5", "name": "QdrantApi account"}}, "typeVersion": 1.1}, {"id": "********-39c9-4e76-99b9-a77bb613b248", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [2420, 340], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "f290f809-3b4e-42e3-bfb5-d505566d9275", "name": "Embeddings OpenAI1", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [2520, 580], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "c360f7b3-2ae4-4ebd-85ca-f64c3966e65d", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [1700, 320], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "9223d119-b5a7-40d4-b8da-f85951b52bde", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [2840, 120], "parameters": {"options": {}, "respondWith": "text", "responseBody": "={{ $json.response.text }}"}, "typeVersion": 1.1}], "pinData": {}, "connections": {"Webhook": {"main": [[{"node": "Retrieval QA Chain", "type": "main", "index": 0}]]}, "Google Drive": {"main": [[{"node": "Qdrant Vector Store", "type": "main", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Qdrant Vector Store", "type": "ai_embedding", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Retrieval QA Chain", "type": "ai_languageModel", "index": 0}]]}, "Embeddings OpenAI1": {"ai_embedding": [[{"node": "Qdrant Vector Store1", "type": "ai_embedding", "index": 0}]]}, "Retrieval QA Chain": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Qdrant Vector Store", "type": "ai_document", "index": 0}]]}, "Qdrant Vector Store1": {"ai_vectorStore": [[{"node": "Vector Store Retriever", "type": "ai_vectorStore", "index": 0}]]}, "Vector Store Retriever": {"ai_retriever": [[{"node": "Retrieval QA Chain", "type": "ai_retriever", "index": 0}]]}, "When chat message received": {"main": [[{"node": "Retrieval QA Chain", "type": "main", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "Google Drive", "type": "main", "index": 0}]]}, "Recursive Character Text Splitter1": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}}}