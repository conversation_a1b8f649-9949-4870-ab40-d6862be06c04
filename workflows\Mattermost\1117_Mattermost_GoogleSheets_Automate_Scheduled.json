{"nodes": [{"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [450, 300], "parameters": {"triggerTimes": {"item": [{"mode": "custom", "cronExpression": "0 0 17 28 9 *"}]}}, "typeVersion": 1}, {"name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [650, 300], "parameters": {"range": "Sessions!A:D", "options": {}, "sheetId": "1nlnsTQKGgQZN-Rtd07K9bn0ROm0aFBC2O4kzM2YaTBI", "authentication": "oAuth2"}, "credentials": {"googleSheetsOAuth2Api": "n8ndocsburner-googlesheets"}, "typeVersion": 1}, {"name": "Mattermost", "type": "n8n-nodes-base.mattermost", "position": [850, 300], "parameters": {"message": "= Hey @channel, we hope you had a great time at **{{$node[\"Google Sheets\"].json[\"Session\"]}}**.\nLet us know how we did by sharing your feedback with us on the link below!", "channelId": "={{$node[\"Google Sheets\"].json[\"Mattermost Channel ID\"]}}", "attachments": [{"title": "=Feedback Form - {{$node[\"Google Sheets\"].json[\"Session\"]}}", "title_link": "={{$node[\"Google Sheets\"].json[\"Feedback Form Link\"]}}"}], "otherOptions": {}}, "credentials": {"mattermostApi": "mm_failedmachine"}, "typeVersion": 1}], "connections": {"Cron": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}, "Google Sheets": {"main": [[{"node": "Mattermost", "type": "main", "index": 0}]]}}}