{"meta": {"instanceId": "cb484ba7b742928a2048bf8829668bed5b5ad9787579adea888f05980292a4a7", "templateCredsSetupCompleted": true}, "nodes": [{"id": "3af140c3-03eb-4eeb-ad31-71f94bc37790", "name": "Loop to next call", "type": "n8n-nodes-base.noOp", "position": [4820, 120], "parameters": {}, "typeVersion": 1}, {"id": "8904df21-c993-4c3d-84e6-4418990cb52f", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [700, -40], "parameters": {}, "typeVersion": 1}, {"id": "d85f05bd-c680-4b41-b67a-8126b3ed29b0", "name": "Create Notion DB Page", "type": "n8n-nodes-base.notion", "position": [3240, 60], "parameters": {"title": "={{ $json.metaData.title }}", "options": {"icon": "📞"}, "resource": "databasePage", "databaseId": {"__rl": true, "mode": "list", "value": "1a85b6e0-c94f-81a3-aa21-e3ccf8296d72", "cachedResultUrl": "https://www.notion.so/1a85b6e0c94f81a3aa21e3ccf8296d72", "cachedResultName": "Sales Call Summaries Demo"}, "propertiesUi": {"propertyValues": [{"key": "Call Date|date", "date": "={{ $json.metaData.started }}"}, {"key": "Recording URL|url", "urlValue": "={{ $json.metaData.url }}"}, {"key": "Company|rich_text", "textContent": "={{ $json.metaData.CompanyName }}"}, {"key": "Call Name|title", "title": "={{ $json.metaData.title }}"}, {"key": "Gong Call ID|rich_text", "textContent": "={{ $json.metaData.GongCallID }}"}, {"key": "SF Opp ID|rich_text", "textContent": "={{ $json.sfOpp[0].SFOppId }}"}, {"key": "SF Opp Stage|select", "selectValue": "={{ $json.sfOpp[0].sfStage }}"}, {"key": "SF Company ID|rich_text", "textContent": "={{ $json.sfOpp[0].companyAccountId }}"}, {"key": "SF Opp Won|checkbox", "checkboxValue": "={{ $json.sfOpp[0].IsWon }}"}, {"key": "SF Opp Closed|checkbox", "checkboxValue": "={{ $json.sfOpp[0].IsClosed }}"}, {"key": "Company Size|select", "selectValue": "={{ $json.sfOpp[0].Employees }}"}, {"key": "Sales Rep|multi_select", "multiSelectValue": "={{ $json.Attendees.internalNames }}"}, {"key": "SF Opp Link|url", "urlValue": "=https://data-drive-1632.lightning.force.com/lightning/r/Opportunity/{{ $json.sfOpp[0].SFOppId }}/view"}, {"key": "SF Company Link|url", "urlValue": "=https://data-drive-1632.lightning.force.com/lightning/r/Account/{{ $json.sfOpp[0].companyAccountId }}/view"}]}}, "credentials": {"notionApi": {"id": "2B3YIiD4FMsF9Rjn", "name": "Angelbot Notion"}}, "retryOnFail": true, "typeVersion": 2.2, "waitBetweenTries": 3000}, {"id": "739aaf26-6807-4f09-a7a5-50b9605e76cb", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [620, -280], "parameters": {"color": 7, "width": 1240, "height": 600, "content": "## Process Queue Logic\nIf the run fails for any reason, it can be rerun on only the remaining calls, allowing for greater resilisience in api calls. The main issue I ran into was Notion rate limiting."}, "typeVersion": 1}, {"id": "cb8ecb7b-6e90-4394-8161-5b327c17d9c5", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [2700, -280], "parameters": {"color": 7, "width": 1360, "height": 600, "content": "## Loop over calls for analysis and Create Parent \n## DB Object to relate other DB objects to\nThe output is a structured JSON object that is then \npassed into a subworkflow for processing in a linear fashion. "}, "typeVersion": 1}, {"id": "49b472b7-d47e-4057-9c43-4b471605059f", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [4080, -340], "parameters": {"color": 7, "width": 420, "height": 660, "content": "## Pass Parent Notion ID and Call data into AI Subworkflow for final prompt processing\nThis allows for multiple agents to process and generate structured data from the calls."}, "typeVersion": 1}, {"id": "b1c39cf4-b101-4e7f-9c74-da43e09769fd", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [4520, -340], "parameters": {"color": 7, "width": 520, "height": 660, "content": "## Alert on Progress\nIn Slack, a progress alert is generated and updated in real time to keep the company updated on the progress of the call processing. "}, "typeVersion": 1}, {"id": "0ed6b796-8817-461f-958f-49ad2b4157cb", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [3460, -600], "parameters": {"color": 7, "width": 600, "height": 300, "content": "## <PERSON><PERSON> Complete\nSince this runs in the background, this alerts the team that job finished successfully. "}, "typeVersion": 1}, {"id": "e537ba92-c909-4da6-b1b0-d5d1fb643bd3", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [260, -500], "parameters": {"color": 5, "width": 340, "height": 820, "content": "![Callforge](https://uploads.n8n.io/templates/callforgeshadow.png)\n## CallForge - The AI Gong Sales Call Processor\nCallForge allows you to extract important information for different departments from your Sales Gong Calls. \n\n### Call Processor\nThis is where the parent object in notion is generated to store the AI Call data once it's generated. This is done first so that it can be passed into multiple sub objects for storage. Once that's done, it's passed into the AI Processor."}, "typeVersion": 1}, {"id": "af52e980-56a5-4875-878a-495898b345ec", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [1880, -360], "parameters": {"color": 7, "width": 800, "height": 300, "content": "## <PERSON><PERSON> Started\nSince this runs in the background, this alerts the team that job has begun successfully."}, "typeVersion": 1}, {"id": "67d4605b-f6d5-41ff-bbe1-90e002456fc1", "name": "Post Slack Receipt", "type": "n8n-nodes-base.slack", "position": [2260, -220], "webhookId": "11dd0884-adc7-40f4-a8a3-f3082a0324fc", "parameters": {"text": "=Queu Started, Processing {{ $json.data.length }} calls.", "select": "channel", "channelId": {"__rl": true, "mode": "list", "value": "C080KBCK1TL", "cachedResultName": "project-call-forge-alerts"}, "otherOptions": {}}, "credentials": {"slackApi": {"id": "OfRxDxHFIqk1q44a", "name": "Knowledge Ninja n8n labs auth"}}, "typeVersion": 2.2}, {"id": "6d779b87-ce83-40bd-b068-9082f6849429", "name": "AI Team Processor", "type": "n8n-nodes-base.executeWorkflow", "position": [4160, -40], "parameters": {"options": {}, "workflowId": {"__rl": true, "mode": "list", "value": "4Uol9xlNKyNH213f", "cachedResultName": "AI Team Processor <PERSON>"}}, "typeVersion": 1.1}, {"id": "59848476-c4ec-47ec-9b1c-f206c0749b1e", "name": "Update Slack Progress", "type": "n8n-nodes-base.slack", "position": [4580, -40], "webhookId": "d69dcd59-add1-4fd1-99c0-eee5c6a7fc4f", "parameters": {"ts": "={{ $('Loop Over Calls').item.json.slackdata[0].message.ts }}", "text": "=Queu Started, Processing calls.\nProgress: {{$node[\"Loop Over Calls\"].context[\"currentRunIndex\"]+1;}}/{{ $('Reduce down to One object').item.json.data.length }}", "channelId": {"__rl": true, "mode": "id", "value": "C080KBCK1TL"}, "operation": "update", "otherOptions": {}, "updateFields": {}}, "credentials": {"slackApi": {"id": "OfRxDxHFIqk1q44a", "name": "Knowledge Ninja n8n labs auth"}}, "typeVersion": 2.2}, {"id": "32a2235e-cbdd-45e2-9cb4-991ea1397274", "name": "Merge call data and parent notion id", "type": "n8n-nodes-base.merge", "position": [3720, -40], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3}, {"id": "6f91bc31-3249-45f6-9114-7e1d8347cf89", "name": "Reduce down to 1 object", "type": "n8n-nodes-base.aggregate", "position": [980, 100], "parameters": {"options": {}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}, {"id": "1d23b540-696c-4d3e-8c23-fac6a84bc6f3", "name": "Get all older Calls", "type": "n8n-nodes-base.notion", "position": [1220, 100], "parameters": {"options": {}, "resource": "databasePage", "operation": "getAll", "returnAll": true, "databaseId": {"__rl": true, "mode": "list", "value": "1a85b6e0-c94f-81a3-aa21-e3ccf8296d72", "cachedResultUrl": "https://www.notion.so/1a85b6e0c94f81a3aa21e3ccf8296d72", "cachedResultName": "Sales Call Summaries Demo"}}, "credentials": {"notionApi": {"id": "2B3YIiD4FMsF9Rjn", "name": "Angelbot Notion"}}, "typeVersion": 2.2}, {"id": "50a3f35e-7637-4eb2-ae9e-11f214307dc0", "name": "Isolate Only Call IDs", "type": "n8n-nodes-base.set", "position": [1440, 100], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "328e6ac8-88f3-4c2f-b8e8-d4a0756efd24", "name": "Call ID", "type": "string", "value": "={{ $json.property_gong_call_id ? $json.property_gong_call_id : \"none\" }}"}]}}, "typeVersion": 3.4}, {"id": "fb5c0970-3a05-4c38-8568-6ed175520db5", "name": "Only Process New Calls", "type": "n8n-nodes-base.compareDatasets", "position": [1680, -40], "parameters": {"options": {}, "resolve": "preferInput1", "mergeByFields": {"values": [{"field1": "metaData.GongCallID", "field2": "Call ID"}]}}, "typeVersion": 2.3}, {"id": "e4c8d925-af53-4855-a002-cbc02c45a9c8", "name": "Reduce down to One object", "type": "n8n-nodes-base.aggregate", "position": [2020, -220], "parameters": {"options": {}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}, {"id": "b6fb9553-42f3-46ca-a0b5-a97288e99e17", "name": "Bundle Slack Message Data", "type": "n8n-nodes-base.aggregate", "position": [2480, -220], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "slackdata"}, "typeVersion": 1}, {"id": "ba121e87-d25f-4867-848d-37b353db7ddb", "name": "<PERSON><PERSON> and Call Data", "type": "n8n-nodes-base.merge", "position": [2800, -80], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineAll"}, "typeVersion": 3}, {"id": "bfd969e7-87a1-42cd-b23a-2b550772e171", "name": "Loop Over Calls", "type": "n8n-nodes-base.splitInBatches", "position": [3020, -80], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "1f7dea30-dffe-4cc2-a912-c73ed1c8db50", "name": "Bundle Notion Parent Object Data", "type": "n8n-nodes-base.aggregate", "position": [3440, 60], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "notionData"}, "typeVersion": 1}, {"id": "e2e2108c-00e0-48c8-8c5c-ef86edc93481", "name": "Bundle Processed Calls", "type": "n8n-nodes-base.aggregate", "position": [3540, -480], "parameters": {"options": {}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}, {"id": "21884d73-45fd-4bb0-b3b6-e225383b5f62", "name": "Post Completed Calls Message", "type": "n8n-nodes-base.slack", "position": [3840, -480], "webhookId": "9d4f5a56-5be9-4373-8961-3627498713dd", "parameters": {"text": "=Queu Processed, {{ $json.data.length }} calls successfully added to Database.", "select": "channel", "channelId": {"__rl": true, "mode": "list", "value": "C080KBCK1TL", "cachedResultName": "project-call-forge-alerts"}, "otherOptions": {}}, "credentials": {"slackApi": {"id": "OfRxDxHFIqk1q44a", "name": "Knowledge Ninja n8n labs auth"}}, "typeVersion": 2.2}], "pinData": {}, "connections": {"Loop Over Calls": {"main": [[{"node": "Bundle Processed Calls", "type": "main", "index": 0}], [{"node": "Merge call data and parent notion id", "type": "main", "index": 0}, {"node": "Create Notion DB Page", "type": "main", "index": 0}]]}, "AI Team Processor": {"main": [[{"node": "Update Slack Progress", "type": "main", "index": 0}]]}, "Loop to next call": {"main": [[{"node": "Loop Over Calls", "type": "main", "index": 0}]]}, "Post Slack Receipt": {"main": [[{"node": "Bundle Slack Message Data", "type": "main", "index": 0}]]}, "Get all older Calls": {"main": [[{"node": "Isolate Only Call IDs", "type": "main", "index": 0}]]}, "Create Notion DB Page": {"main": [[{"node": "Bundle Notion Parent Object Data", "type": "main", "index": 0}]]}, "Isolate Only Call IDs": {"main": [[{"node": "Only Process New Calls", "type": "main", "index": 1}]]}, "Update Slack Progress": {"main": [[{"node": "Loop to next call", "type": "main", "index": 0}]]}, "Bundle Processed Calls": {"main": [[{"node": "Post Completed Calls Message", "type": "main", "index": 0}]]}, "Only Process New Calls": {"main": [[{"node": "Reduce down to One object", "type": "main", "index": 0}, {"node": "<PERSON><PERSON> and Call Data", "type": "main", "index": 1}]]}, "Reduce down to 1 object": {"main": [[{"node": "Get all older Calls", "type": "main", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "Only Process New Calls", "type": "main", "index": 0}, {"node": "Reduce down to 1 object", "type": "main", "index": 0}]]}, "Bundle Slack Message Data": {"main": [[{"node": "<PERSON><PERSON> and Call Data", "type": "main", "index": 0}]]}, "Merge Slack and Call Data": {"main": [[{"node": "Loop Over Calls", "type": "main", "index": 0}]]}, "Reduce down to One object": {"main": [[{"node": "Post Slack Receipt", "type": "main", "index": 0}]]}, "Bundle Notion Parent Object Data": {"main": [[{"node": "Merge call data and parent notion id", "type": "main", "index": 1}]]}, "Merge call data and parent notion id": {"main": [[{"node": "AI Team Processor", "type": "main", "index": 0}]]}}}