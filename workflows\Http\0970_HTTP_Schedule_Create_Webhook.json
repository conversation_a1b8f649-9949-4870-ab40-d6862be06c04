{"id": "4AG83ybt0S3WQbkS", "meta": {"instanceId": "a943fc71a4dfb51cc3424882233bcd72e7a73857958af1cf464f7c21580c726e", "templateCredsSetupCompleted": true}, "name": "Daily AI News Translation & Summary with GPT-4 and Telegram Delivery", "tags": [{"id": "WuWMTipHMvadNrvh", "name": "Other", "createdAt": "2025-04-18T13:34:41.761Z", "updatedAt": "2025-04-18T13:34:41.761Z"}], "nodes": [{"id": "894ceed6-8fcd-484e-bf6f-9c3eee81119e", "name": "Workflow Overview", "type": "n8n-nodes-base.stickyNote", "position": [-40, 200], "parameters": {"color": 7, "width": 720, "height": 600, "content": "### Setup\n\n1. **Add NewsAPI and GNews API Keys**\n    - Register for accounts on [NewsAPI.org](https://newsapi.org/) and [GNews](https://gnews.io/) to obtain your API keys.\n    - Input your NewsAPI key directly into the `Fetch NewsAPI articles` node.\n    - Input your GNews API key into the `Fetch GNews articles` node.\n2. **Set up your Telegram Bot**\n    - Create a Telegram Bot via [BotFather](https://core.telegram.org/bots#6-botfather) and copy the generated Bot Token.\n    - In n8n, create Telegram Bot credentials using this token.\n    - In the `Send summary to Telegram` node, enter the chat ID of your target user, group, or channel to receive the messages.\n3. **Configure OpenAI Credentials**\n    - In n8n, create a new credential using your OpenAI API key.\n    - Assign this credential to the `GPT-4.1 Model` node (or equivalent OpenAI/AI nodes).\n\nAfter completing these steps, your workflow is fully configured to fetch, summarize, and deliver daily AI news to your selected Telegram chat automatically.\n\n### How to customize this workflow\n\n- **Change the topic:** Update the keywords in the NewsAPI and GNews nodes for other subjects (e.g., “blockchain”, “quantum computing”).\n- **Adjust delivery time:** Modify the scheduled trigger to your preferred hour.\n- **Tweak summary style or language:** Refine the prompt in the AI summarizer node for different tones or translate into other languages as needed."}, "typeVersion": 1}, {"id": "9de68856-a2e1-4b06-a738-92e8db23f9ea", "name": "Trigger at 8am daily", "type": "n8n-nodes-base.scheduleTrigger", "position": [760, 520], "parameters": {"rule": {"interval": [{"triggerAtHour": 8}]}}, "typeVersion": 1.2}, {"id": "d2a13562-9f21-4f99-8698-d5ba58245b02", "name": "Fetch GNews articles", "type": "n8n-nodes-base.httpRequest", "position": [980, 420], "parameters": {"url": "https://gnews.io/api/v4/search", "options": {}, "sendQuery": true, "queryParameters": {"parameters": [{"name": "q", "value": "AI"}, {"name": "lang", "value": "en"}, {"name": "apikey"}]}}, "typeVersion": 4.2}, {"id": "0895bda6-5268-4454-a49f-732a3025947b", "name": "Fetch NewsAPI articles", "type": "n8n-nodes-base.httpRequest", "position": [980, 620], "parameters": {"url": "https://newsapi.org/v2/everything", "options": {}, "sendQuery": true, "sendHeaders": true, "queryParameters": {"parameters": [{"name": "q", "value": "AI"}, {"name": "language", "value": "en"}, {"name": "sortBy", "value": "publishedAt"}, {"name": "pageSize", "value": "20"}]}, "headerParameters": {"parameters": [{"name": "X-Api-Key"}]}}, "typeVersion": 4.2}, {"id": "3cd42b1a-348a-486d-8217-592ce2b35e6c", "name": "GNews: Map to articles", "type": "n8n-nodes-base.set", "position": [1200, 420], "parameters": {"options": {}, "assignments": {"assignments": [{"name": "articles", "type": "string", "value": "={{ $json.articles }}"}]}}, "typeVersion": 3.4}, {"id": "40692e2f-9289-448b-a5cb-ce4846b20264", "name": "NewsAPI: Map to articles", "type": "n8n-nodes-base.set", "position": [1200, 620], "parameters": {"options": {}, "assignments": {"assignments": [{"name": "articles", "type": "string", "value": "={{ $json.articles }}"}]}}, "typeVersion": 3.4}, {"id": "d42b4e2d-87f4-4a0e-a6c3-ab1b3501bcfa", "name": "Merge GNews & NewsAPI", "type": "n8n-nodes-base.merge", "position": [1420, 520], "parameters": {}, "typeVersion": 3.1}, {"id": "985ec49b-b127-44b9-8f63-62486d0bf864", "name": "Sticky: News APIs", "type": "n8n-nodes-base.stickyNote", "position": [900, 210], "parameters": {"color": 5, "width": 480, "height": 570, "content": "### Data Source Nodes\n- `Fetch GNews articles` and `Fetch NewsAPI articles` get up to 20 latest AI-related English news each from two different APIs using your API keys.\n- Both sources are standardized to an `articles` property for merging.\n"}, "typeVersion": 1}, {"id": "430c8ddc-948e-4770-b816-591c6c43c617", "name": "AI summarizer & translator", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1640, 520], "parameters": {"text": "=You are an AI news assistant. Your tasks:\n1. Select the 15 most relevant articles on AI technology progress and applications from {{$json.articles}}.\n2. Translate them to accurate Traditional Chinese; don't translate commonly used technical English terms.\n3. Make sure to include the article URL for each item.\n4. Begin output with today's date (e.g., '早安，這是 {{ $now.format('yyyy/MM/dd') }} 的 AI 新聞：')\nOutput only the summary.", "options": {}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "5dfacf8a-25d4-43fd-9b96-a34eeed45d39", "name": "GPT-4.1 Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1728, 740], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4.1", "cachedResultName": "gpt-4.1"}, "options": {}}, "credentials": {"openAiApi": {"id": "RjawTJt2ILjgM4Wx", "name": "[Template] OpenAi account"}}, "typeVersion": 1.2}, {"id": "66fedd82-5fbf-4d17-a7f5-78c41d7d5949", "name": "Sticky: AI Processing", "type": "n8n-nodes-base.stickyNote", "position": [1568, 300], "parameters": {"color": 2, "width": 400, "height": 580, "content": "### AI Assistant Logic\nThe summarization uses the latest GPT-4.1 model to select, translate, and enrich the top 15 AI news links from both GNews and NewsAPI. Controlled by a tailored prompt for concise, readable output."}, "typeVersion": 1}, {"id": "7a742531-4a08-408e-8b2c-558be75c1a8f", "name": "Send summary to Telegram", "type": "n8n-nodes-base.telegram", "position": [2016, 520], "webhookId": "21eb8e1c-87de-45af-888d-699fbd443bc8", "parameters": {"text": "={{ $json.output }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "tpF8PHPxMfdld3NA", "name": "[Template] <PERSON><PERSON><PERSON>"}}, "typeVersion": 1.2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "deee909a-9cfe-409d-8201-b9b7194ec9bc", "connections": {"GPT-4.1 Model": {"ai_languageModel": [[{"node": "AI summarizer & translator", "type": "ai_languageModel", "index": 0}]]}, "Fetch GNews articles": {"main": [[{"node": "GNews: Map to articles", "type": "main", "index": 0}]]}, "Trigger at 8am daily": {"main": [[{"node": "Fetch GNews articles", "type": "main", "index": 0}, {"node": "Fetch NewsAPI articles", "type": "main", "index": 0}]]}, "Merge GNews & NewsAPI": {"main": [[{"node": "AI summarizer & translator", "type": "main", "index": 0}]]}, "Fetch NewsAPI articles": {"main": [[{"node": "NewsAPI: Map to articles", "type": "main", "index": 0}]]}, "GNews: Map to articles": {"main": [[{"node": "Merge GNews & NewsAPI", "type": "main", "index": 0}]]}, "NewsAPI: Map to articles": {"main": [[{"node": "Merge GNews & NewsAPI", "type": "main", "index": 1}]]}, "AI summarizer & translator": {"main": [[{"node": "Send summary to Telegram", "type": "main", "index": 0}]]}}}