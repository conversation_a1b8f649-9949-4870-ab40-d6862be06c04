{"nodes": [{"name": "Ack", "type": "n8n-nodes-base.webhook", "position": [-160, 1440], "webhookId": "d3025d6c-5956-439e-9c9a-db3ef524a24f", "parameters": {"path": "/ack", "options": {}, "httpMethod": "POST"}, "typeVersion": 1}, {"name": "Resolve", "type": "n8n-nodes-base.webhook", "position": [120, 1880], "webhookId": "92d7ddfa-20f9-49bc-976e-4f6c76c0b3b4", "parameters": {"path": "/resolve", "options": {}, "httpMethod": "POST"}, "typeVersion": 1}, {"name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [60, 1040], "webhookId": "9888d896-dd23-4e97-9d16-c12055b64133", "parameters": {"path": "9888d896-dd23-4e97-9d16-c12055b64133", "options": {}, "httpMethod": "POST"}, "typeVersion": 1}, {"name": "Jira1", "type": "n8n-nodes-base.jira", "position": [680, 1040], "parameters": {"project": "10016", "summary": "={{$node[\"Webhook\"].json[\"body\"][\"event\"][\"data\"][\"title\"]}}", "issueType": "10007", "additionalFields": {"assignee": "qwertz12345"}}, "credentials": {"jiraSoftwareCloudApi": {"id": "64", "name": "Jira SW Cloud account"}}, "typeVersion": 1}, {"name": "Jira2", "type": "n8n-nodes-base.jira", "position": [540, 1880], "parameters": {"issueKey": "={{$node[\"Resolve\"].json[\"body\"][\"context\"][\"jira_key\"]}}", "operation": "update", "updateFields": {"statusId": "31"}}, "credentials": {"jiraSoftwareCloudApi": {"id": "64", "name": "Jira SW Cloud account"}}, "typeVersion": 1}, {"name": "PagerDuty1", "type": "n8n-nodes-base.pagerDuty", "position": [60, 1440], "parameters": {"email": "<EMAIL>", "resource": "incident", "operation": "update", "incidentId": "={{$json[\"body\"][\"context\"][\"pagerduty_incident\"]}}", "updateFields": {"status": "acknowledged"}, "authentication": "apiToken", "conferenceBridgeUi": {}}, "credentials": {"pagerDutyApi": {"id": "65", "name": "PagerDuty account"}}, "typeVersion": 1}, {"name": "PagerDuty2", "type": "n8n-nodes-base.pagerDuty", "position": [340, 1880], "parameters": {"email": "<EMAIL>", "resource": "incident", "operation": "update", "incidentId": "={{$json[\"body\"][\"context\"][\"pagerduty_incident\"]}}", "updateFields": {"status": "resolved"}, "authentication": "apiToken", "conferenceBridgeUi": {}}, "credentials": {"pagerDutyApi": {"id": "65", "name": "PagerDuty account"}}, "typeVersion": 1}, {"name": "Mattermost5", "type": "n8n-nodes-base.mattermost", "position": [300, 1440], "parameters": {"message": "💪🏼 Incident status has been changed to Acknowledged on PagerDuty.", "channelId": "={{$node[\"Ack\"].json[\"body\"][\"channel_id\"]}}", "attachments": [], "otherOptions": {}}, "credentials": {"mattermostApi": {"id": "61", "name": "Mattermost account"}}, "typeVersion": 1}, {"name": "Mattermost6", "type": "n8n-nodes-base.mattermost", "position": [760, 1760], "parameters": {"message": "💪 This issue got closed in PagerDuty and Jira.", "channelId": "={{$node[\"Resolve\"].json[\"body\"][\"channel_id\"]}}", "attachments": [], "otherOptions": {}}, "credentials": {"mattermostApi": {"id": "61", "name": "Mattermost account"}}, "typeVersion": 1}, {"name": "Mattermost4", "type": "n8n-nodes-base.mattermost", "position": [900, 1180], "parameters": {"message": "=⚠️ {{$node[\"Webhook\"].json[\"body\"][\"messages\"][0][\"log_entries\"][0][\"incident\"][\"summary\"]}}\nPagerDuty incident: {{$node[\"Webhook\"].json[\"body\"][\"messages\"][0][\"log_entries\"][0][\"incident\"][\"html_url\"]}}\nJira issue: https://n8n.atlassian.net/browse/{{$json[\"key\"]}}", "channelId": "={{$node[\"Mattermost1\"].json[\"id\"]}}", "attachments": [{"actions": {"item": [{"name": "Acknowledge", "type": "button", "options": {}, "data_source": "custom", "integration": {"item": {"url": "https://username.app.n8n.cloud/webhook/ack", "context": {"property": [{"name": "pagerduty_incident", "value": "={{ $node[\"Webhook\"].json[\"body\"][\"event\"][\"data\"][\"id\"] }}"}]}}}}, {"name": "Resolve", "type": "button", "options": {}, "data_source": "custom", "integration": {"item": {"url": "https://username.app.n8n.cloud/webhook/resolve", "context": {"property": [{"name": "jira_key", "value": "={{$json[\"key\"]}}"}, {"name": "pagerduty_incident", "value": "={{ $node[\"Webhook\"].json[\"body\"][\"event\"][\"data\"][\"id\"] }}"}]}}}}]}}], "otherOptions": {}}, "credentials": {"mattermostApi": {"id": "61", "name": "Mattermost account"}}, "typeVersion": 1}, {"name": "Mattermost3", "type": "n8n-nodes-base.mattermost", "position": [900, 940], "parameters": {"message": "=🚨 New incident: \nAuxiliary Channel -> https://mattermost.internal.n8n.io/test/channels/{{$node[\"Mattermost1\"].json[\"name\"]}}\nPagerDuty Incident -> {{$node[\"Webhook\"].json[\"body\"][\"event\"][\"data\"][\"html_url\"]}}\nJira Issue -> https://n8n.atlassian.net/browse/{{$json[\"key\"]}}", "channelId": "qwertz12345", "attachments": [], "otherOptions": {}}, "credentials": {"mattermostApi": {"id": "61", "name": "Mattermost account"}}, "typeVersion": 1}, {"name": "Mattermost2", "type": "n8n-nodes-base.mattermost", "position": [480, 1040], "parameters": {"userId": "qwertz12345", "resource": "channel", "channelId": "={{$json[\"id\"]}}", "operation": "addUser"}, "credentials": {"mattermostApi": {"id": "61", "name": "Mattermost account"}}, "typeVersion": 1}, {"name": "Mattermost1", "type": "n8n-nodes-base.mattermost", "position": [280, 1040], "parameters": {"teamId": "qwertz12345", "channel": "={{$json[\"body\"][\"event\"][\"data\"][\"incident_key\"]}}", "resource": "channel", "displayName": "={{$json[\"body\"][\"event\"][\"data\"][\"title\"]}}"}, "credentials": {"mattermostApi": {"id": "61", "name": "Mattermost account"}}, "typeVersion": 1}, {"name": "Mattermost7", "type": "n8n-nodes-base.mattermost", "position": [760, 1980], "parameters": {"message": "=🎉 The incident ({{$node[\"PagerDuty2\"].json[\"summary\"]}}) was resolved by the lovely folks in the on-call team!", "channelId": "qwertz12345", "attachments": [], "otherOptions": {}}, "credentials": {"mattermostApi": {"id": "61", "name": "Mattermost account"}}, "typeVersion": 1}], "connections": {"Ack": {"main": [[{"node": "PagerDuty1", "type": "main", "index": 0}]]}, "Jira1": {"main": [[{"node": "Mattermost3", "type": "main", "index": 0}, {"node": "Mattermost4", "type": "main", "index": 0}]]}, "Jira2": {"main": [[{"node": "Mattermost6", "type": "main", "index": 0}, {"node": "Mattermost7", "type": "main", "index": 0}]]}, "Resolve": {"main": [[{"node": "PagerDuty2", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Mattermost1", "type": "main", "index": 0}]]}, "PagerDuty1": {"main": [[{"node": "Mattermost5", "type": "main", "index": 0}]]}, "PagerDuty2": {"main": [[{"node": "Jira2", "type": "main", "index": 0}]]}, "Mattermost1": {"main": [[{"node": "Mattermost2", "type": "main", "index": 0}]]}, "Mattermost2": {"main": [[{"node": "Jira1", "type": "main", "index": 0}]]}}}