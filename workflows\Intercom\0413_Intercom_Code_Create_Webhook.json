{"meta": {"instanceId": "cb484ba7b742928a2048bf8829668bed5b5ad9787579adea888f05980292a4a7"}, "nodes": [{"id": "1f578b25-ab5b-40f3-9ccc-7f5975959073", "name": "Could not find user", "type": "n8n-nodes-base.noOp", "position": [1620, 560], "parameters": {}, "typeVersion": 1}, {"id": "236436e0-1fc9-4411-b34c-946246ecbe19", "name": "On Webhook event from Intercom", "type": "n8n-nodes-base.webhook", "position": [700, 500], "webhookId": "11e21ebc-27ef-49b5-8c77-648faf3e86e0", "parameters": {"path": "11e21ebc-27ef-49b5-8c77-648faf3e86e0", "options": {}, "httpMethod": "POST"}, "typeVersion": 1.1}, {"id": "10e2e89c-6ea2-4888-99c8-2f5d99cb1a1d", "name": "set key fields", "type": "n8n-nodes-base.set", "position": [1120, 340], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "3631124b-89e3-49b5-a792-36264eb502f0", "name": "user_id", "type": "string", "value": "={{ $json.body.data.item.id }}"}, {"id": "3c2a877d-186b-4a26-86f2-d686e5630e42", "name": "email", "type": "string", "value": "={{ $json.body.data.item.email }}"}]}}, "typeVersion": 3.3}, {"id": "078945ff-7c7a-4b45-9f8b-773827b9eb30", "name": "Update data in Intercom", "type": "n8n-nodes-base.httpRequest", "position": [1900, 140], "parameters": {"url": "=https://api.intercom.io/contacts/{{ $('set key fields').item.json.user_id }}", "method": "PUT", "options": {}, "sendBody": true, "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "email", "value": "={{ $json.result.current_work_email }} {{ $json.result.full_name }}"}, {"name": "name", "value": "={{ $json.result.full_name }}"}, {"name": "phone", "value": "={{ $json.result.phone_numbers?.[0]?.E164 }}"}, {"name": "avatar", "value": "={{ $json.result.employment?.profile_pic_url }}"}, {"name": "social_profiles", "value": "={{ $json.social_profiles }}"}, {"name": "location", "value": "={{ $json.location }}"}]}, "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "Intercom-Version", "value": "2.10"}]}}, "credentials": {"httpHeaderAuth": {"id": "YYOMyAkbDgnGfggq", "name": "intercom api key"}}, "typeVersion": 4.1}, {"id": "4ca65df4-7244-485f-8c6c-bb1ae4cd84a8", "name": "Other event", "type": "n8n-nodes-base.noOp", "position": [1120, 680], "parameters": {}, "typeVersion": 1}, {"id": "87b12749-b81e-460b-97bb-b25b59f303df", "name": "Intercom", "type": "n8n-nodes-base.intercom", "disabled": true, "position": [1900, -60], "parameters": {"operation": "update", "additionalFields": {}}, "credentials": {"intercomApi": {"id": "sIILbFMUzkVWaBE6", "name": "Intercom account"}}, "typeVersion": 1}, {"id": "ddb1dbb6-3f20-480f-9f49-641d40d9953a", "name": "massage data", "type": "n8n-nodes-base.code", "position": [1660, 140], "parameters": {"mode": "runOnceForEachItem", "jsCode": "// Add social profiles\n$input.item.json.social_profiles = $input.item.json.result.social_profiles.map((profile) => {\n  return {\n    type: 'social_profile',\n    name : profile.network,\n    url: profile.url,\n  }\n});\n\n$input.item.json.location = {\n  country: $input.item.json.result.location?.country,\n  city: $input.item.json.result.location?.city,\n  region: $input.item.json.result.location?.region,\n}\n\nreturn $input.item;"}, "typeVersion": 2}, {"id": "c490bb0d-ec6a-4a9a-9915-dd65ab669873", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [660, 240], "parameters": {"width": 377.**************, "height": 609.*************, "content": "## On User created event in Intercom\n\n1. Setup webhook url in intercom\n2. Make sure `contact.user.created` is enabled"}, "typeVersion": 1}, {"id": "3e866480-4ec8-4a27-b946-43d6358edfec", "name": "On user created", "type": "n8n-nodes-base.switch", "position": [880, 500], "parameters": {"rules": {"values": [{"outputKey": "user created", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.body.topic }}", "rightValue": "contact.user.created"}]}, "renameOutput": true}]}, "options": {"fallbackOutput": "extra"}}, "typeVersion": 3}, {"id": "e9efb8f5-8f97-476f-9d60-09a24b4f656a", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1260, 20], "parameters": {"color": 3, "width": 275.71639586410623, "height": 609.*************, "content": "## Enrich data from ExactBuyer\n\n1. Add api key from Exact buyer\n2. Use email as identifier to match user\n\nUse API Guide here https://docs.exactbuyer.com/contact-enrichment/enrichment"}, "typeVersion": 1}, {"id": "4a86d237-0908-4a79-9ebf-42711f850b3e", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1600, -280], "parameters": {"width": 562.************, "height": 763.7223042836036, "content": "## Update user in Intercom\n\n1. Set Http node and generic header API Key using this guide https://developers.intercom.com/docs/build-an-integration/learn-more/authentication/\n2. Update data in intercom using this guide\nhttps://developers.intercom.com/docs/references/rest-api/api.intercom.io/Contacts/UpdateContact/"}, "typeVersion": 1}, {"id": "44eb44fc-9376-4c64-9561-3d9b585eb51f", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [540, -320], "parameters": {"color": 4, "width": 623.6113141433334, "height": 390.44782241577565, "content": "# Enrich new Intercom users with contact details from ExactBuyer\n\n## This workflow aims to enrich new contacts in Intercom. The more relevant the Intercom profile, the more useful it is. Once active, this n8n workflow will update the social profiles, contact data (phone, email) as well as location data from ExactBuyer.\n"}, "typeVersion": 1}, {"id": "fc9755ae-07ed-4151-bab0-4dc9b6c2408f", "name": "Enrich user from ExactBuyer", "type": "n8n-nodes-base.httpRequest", "onError": "continueErrorOutput", "position": [1340, 340], "parameters": {"url": "https://api.exactbuyer.com/v1/enrich", "options": {"redirect": {"redirect": {}}}, "sendQuery": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "queryParameters": {"parameters": [{"name": "email", "value": "={{ $json.email }}"}, {"name": "required", "value": "work_email,personal_email,email"}]}}, "credentials": {"httpHeaderAuth": {"id": "kyMNOdXZX3ugPihF", "name": "ExactBuyer Api key"}}, "typeVersion": 4.1}], "pinData": {"On Webhook event from Intercom": [{"body": {"id": "notif_6da6874a-0fc5-4d6e-9c50-a9656b741203", "data": {"item": {"id": "65d5de6eb71d82abcb782f68", "os": null, "name": "<PERSON><PERSON><PERSON> exact butyer", "role": "user", "tags": {"url": "/contacts/65d5de6eb71d82abcb782f68/tags", "data": [], "type": "list", "has_more": false, "total_count": 0}, "type": "contact", "email": "<EMAIL>", "notes": {"url": "/contacts/65d5de6eb71d82abcb782f68/notes", "data": [], "type": "list", "has_more": false, "total_count": 0}, "phone": null, "avatar": null, "browser": null, "location": {"city": null, "type": "location", "region": null, "country": null, "country_code": null, "continent_code": null}, "owner_id": null, "referrer": null, "utm_term": null, "companies": {"url": "/contacts/65d5de6eb71d82abcb782f68/companies", "data": [], "type": "list", "has_more": false, "total_count": 0}, "created_at": "2024-02-21T11:28:46.093+00:00", "ios_device": null, "updated_at": "2024-02-21T11:28:46.090+00:00", "utm_medium": null, "utm_source": null, "external_id": "eden_exact_buyer", "sms_consent": false, "utm_content": null, "ios_app_name": null, "last_seen_at": null, "signed_up_at": null, "utm_campaign": null, "workspace_id": "ub6mafvn", "android_device": null, "ios_os_version": null, "browser_version": null, "ios_app_version": null, "ios_sdk_version": null, "last_replied_at": null, "social_profiles": {"data": [], "type": "list"}, "android_app_name": null, "browser_language": null, "has_hard_bounced": false, "ios_last_seen_at": null, "custom_attributes": {}, "language_override": null, "last_contacted_at": null, "android_os_version": null, "android_app_version": null, "android_sdk_version": null, "android_last_seen_at": null, "last_email_opened_at": null, "marked_email_as_spam": false, "last_email_clicked_at": null, "unsubscribed_from_sms": false, "unsubscribed_from_emails": false, "opted_in_subscription_types": {"url": "/contacts/65d5de6eb71d82abcb782f68/subscriptions", "data": [], "type": "list", "has_more": false, "total_count": 0}, "opted_out_subscription_types": {"url": "/contacts/65d5de6eb71d82abcb782f68/subscriptions", "data": [], "type": "list", "has_more": false, "total_count": 0}}, "type": "notification_event_data"}, "self": null, "type": "notification_event", "links": {}, "topic": "contact.user.created", "app_id": "ub6mafvn", "created_at": 1708514926, "delivered_at": 0, "first_sent_at": 1708514926, "delivery_status": "pending", "delivery_attempts": 1}, "query": {}, "params": {}, "headers": {"host": "test.users.n8n.cloud"}}]}, "connections": {"massage data": {"main": [[{"node": "Update data in Intercom", "type": "main", "index": 0}]]}, "set key fields": {"main": [[{"node": "Enrich user from ExactBuyer", "type": "main", "index": 0}]]}, "On user created": {"main": [[{"node": "set key fields", "type": "main", "index": 0}], [{"node": "Other event", "type": "main", "index": 0}]]}, "Enrich user from ExactBuyer": {"main": [[{"node": "massage data", "type": "main", "index": 0}], [{"node": "Could not find user", "type": "main", "index": 0}]]}, "On Webhook event from Intercom": {"main": [[{"node": "On user created", "type": "main", "index": 0}]]}}}