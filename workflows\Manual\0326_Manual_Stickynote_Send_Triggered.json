{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "1f2bb917-6d65-4cfa-9474-fc3b19a8c3bd", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-440, -120], "parameters": {"color": 7, "width": 918, "height": 627, "content": "### Load data into database\nFetch file from Google Drive, split it into chunks and insert into Pinecone index"}, "typeVersion": 1}, {"id": "eabbc944-5b62-4959-8ea4-879f28e19ab8", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [740, -120], "parameters": {"color": 7, "width": 534, "height": 627, "content": "### Chat with database\nEmbed the incoming chat message and use it retrieve relevant chunks from the vector store. These are passed to the model to formulate an answer "}, "typeVersion": 1}, {"id": "ab577f4d-8906-4e0c-bc62-e8a4b2610551", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-720, 240], "parameters": {"height": 264.61498034081166, "content": "## Try me out\n1. In Pinecone, create an index with 1536 dimensions and select it in *both* Pinecone nodes\n2. Click 'test workflow' at the bottom of the canvas to load data into the vector store\n3. <PERSON>lick 'chat' at the bottom of the canvas to ask questions about the data"}, "typeVersion": 1}, {"id": "6f074b77-3441-4026-a13a-ed891a1c959b", "name": "When clicking 'Test Workflow' button", "type": "n8n-nodes-base.manualTrigger", "position": [-700, -20], "parameters": {}, "typeVersion": 1}, {"id": "0a6f8b88-9c62-4e3e-82cb-a7028bdcac45", "name": "Pinecone Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "position": [80, -20], "parameters": {"mode": "insert", "options": {"clearNamespace": true}, "pineconeIndex": {"__rl": true, "mode": "id", "value": "test-index"}}, "credentials": {"pineconeApi": {"id": "OHDlDbBkaPDgpnOY", "name": "PineconeApi account"}}, "typeVersion": 1}, {"id": "ae426fdc-0d58-46a6-bfe6-0f25c0e70cf1", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [560, -20], "webhookId": "dec328cc-f47e-4727-b1c5-7370be86a958", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "9388b413-f133-45a6-8066-cf71c0fb826c", "name": "Question & Answer", "type": "@n8n/n8n-nodes-langchain.agent", "position": [800, -20], "parameters": {"options": {}}, "typeVersion": 1.8}, {"id": "c50e8f9b-8254-495e-9e13-62f42d22c9b0", "name": "Set Google Drive file URL", "type": "n8n-nodes-base.set", "position": [-380, -20], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d08ef1f5-932b-4bbb-bb02-0cbdff26a636", "name": "file_url", "type": "string", "value": "https://drive.google.com/file/d/11Koq9q53nkk0F5Y8eZgaWJUVR03I4-MM/view"}]}}, "typeVersion": 3.4}, {"id": "d97920ad-6b36-4981-8b9d-9d470b5c769a", "name": "Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [-180, -20], "parameters": {"fileId": {"__rl": true, "mode": "url", "value": "={{ $json.file_url }}"}, "options": {}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "yOwz41gMQclOadgu", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "742beb54-8b89-49a3-afe5-fd7e73b37044", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [180, 200], "parameters": {"options": {}, "dataType": "binary"}, "typeVersion": 1}, {"id": "f75e31e9-f752-45d1-bc44-75097ec85ce6", "name": "Recursive Character Text Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "position": [260, 320], "parameters": {"options": {}, "chunkSize": 3000, "chunkOverlap": 200}, "typeVersion": 1}, {"id": "034a2b72-f728-4978-bc18-c950f0f2c24c", "name": "Embeddings OpenAI1", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [1000, 320], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "bac883c8-4c1f-466b-b20f-d0fdf6acfc42", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [60, 200], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "7b6cdba3-906b-44dd-85be-1d515337972b", "name": "Pinecone Vector Store1", "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "position": [920, 200], "parameters": {"mode": "retrieve-as-tool", "options": {}, "toolName": "bitcoin_paper", "pineconeIndex": {"__rl": true, "mode": "id", "value": "test-index"}, "toolDescription": "Call this tool to retrieve facts from the bitcoin whitepaper", "includeDocumentMetadata": false}, "credentials": {"pineconeApi": {"id": "OHDlDbBkaPDgpnOY", "name": "PineconeApi account"}}, "typeVersion": 1}, {"id": "cf9d18a9-4c1e-4a67-8149-961b3eee374d", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [800, 200], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}], "pinData": {}, "connections": {"Google Drive": {"main": [[{"node": "Pinecone Vector Store", "type": "main", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Pinecone Vector Store", "type": "ai_embedding", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Question & Answer", "type": "ai_languageModel", "index": 0}]]}, "Embeddings OpenAI1": {"ai_embedding": [[{"node": "Pinecone Vector Store1", "type": "ai_embedding", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Pinecone Vector Store", "type": "ai_document", "index": 0}]]}, "Pinecone Vector Store1": {"ai_tool": [[{"node": "Question & Answer", "type": "ai_tool", "index": 0}]]}, "Set Google Drive file URL": {"main": [[{"node": "Google Drive", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "Question & Answer", "type": "main", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "When clicking 'Test Workflow' button": {"main": [[{"node": "Set Google Drive file URL", "type": "main", "index": 0}]]}}}