{"nodes": [{"id": "a4c46baf-ff6d-489f-9c77-a5e4cfe6b580", "name": "Fetch Github Repo Releases", "type": "n8n-nodes-base.httpRequest", "position": [640, 240], "parameters": {"url": "https://api.github.com/repos/n8n-io/n8n/releases/latest", "options": {}}, "typeVersion": 4.2}, {"id": "aba391ad-eedc-4cf7-a770-646eba11e3fe", "name": "Split Out Content", "type": "n8n-nodes-base.splitOut", "position": [1100, 140], "parameters": {"options": {}, "fieldToSplitOut": "body"}, "typeVersion": 1}, {"id": "ea29ed9d-5b34-46f2-87c6-2bacf4b7d7bf", "name": "Convert Markdown to HTML", "type": "n8n-nodes-base.markdown", "position": [1280, 140], "parameters": {"mode": "markdownToHtml", "options": {}, "markdown": "={{ $json.body }}", "destinationKey": "html"}, "typeVersion": 1}, {"id": "53bf597d-3f64-4375-9632-c8aed38e88df", "name": "Daily Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [380, 240], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.2}, {"id": "14bf72aa-167b-44e4-ba6c-f20f1c366b93", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [540, 140], "parameters": {"width": 288, "height": 300, "content": "Change **url** for Github Repo here"}, "typeVersion": 1}, {"id": "c80704a9-f103-4977-b604-f07994d1d1f8", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1420, 60], "parameters": {"width": 288, "height": 300, "content": "Change **to Email** here"}, "typeVersion": 1}, {"id": "5b9ea851-df78-4366-a3e0-b5afb563e5ae", "name": "Send Email", "type": "n8n-nodes-base.emailSend", "position": [1520, 140], "parameters": {"html": "={{ $json.html }}", "options": {}, "subject": "New n8n release", "toEmail": "<EMAIL>", "fromEmail": "<EMAIL>"}, "credentials": {"smtp": {"id": "ybCScjWtYAxhpByf", "name": "SMTP account - internal use only"}}, "typeVersion": 2.1}, {"id": "775c38ba-7d29-4956-a913-a2136c317591", "name": "If new release in the last day", "type": "n8n-nodes-base.if", "position": [860, 240], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "77d364d3-a340-49d2-abf8-e38d7dceb8d6", "operator": {"type": "dateTime", "operation": "after"}, "leftValue": "={{ $json.published_at.toDateTime() }}", "rightValue": "={{ DateTime.utc().minus(1, 'days') }}"}]}}, "typeVersion": 2.2}], "pinData": {}, "connections": {"Send Email": {"main": [[]]}, "Daily Trigger": {"main": [[{"node": "Fetch Github Repo Releases", "type": "main", "index": 0}]]}, "Split Out Content": {"main": [[{"node": "Convert Markdown to HTML", "type": "main", "index": 0}]]}, "Convert Markdown to HTML": {"main": [[{"node": "Send Email", "type": "main", "index": 0}]]}, "Fetch Github Repo Releases": {"main": [[{"node": "If new release in the last day", "type": "main", "index": 0}]]}, "If new release in the last day": {"main": [[{"node": "Split Out Content", "type": "main", "index": 0}]]}}}