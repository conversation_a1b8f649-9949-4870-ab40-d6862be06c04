{"id": "WCh8N9PrO0UIwrqW", "meta": {"instanceId": "d75abd32ee1bd9a1c6026cb545a5cf11f7e37f192955e7c01497178aadb66427", "templateCredsSetupCompleted": true}, "name": "Automatizacion X", "tags": [], "nodes": [{"id": "a51d67d2-ef4a-47c3-8206-51f2c1067128", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [0, 0], "webhookId": "614cd783-fbc8-44ca-8db8-820679333e75", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "702d2f29-c9cb-46aa-bdc2-ccd68ab24a1c", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [200, 240], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "gpt-4o"}, "options": {}}, "typeVersion": 1.2}, {"id": "6d65d809-e2b3-4884-ad1a-7738ac9ebbb7", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [400, 240], "parameters": {}, "typeVersion": 1.3}, {"id": "2f247c72-8f90-49f9-9982-bf94c044b8bb", "name": "first tweet", "type": "n8n-nodes-base.twitterTool", "position": [560, 240], "parameters": {"text": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Text', ``, 'string') }}", "additionalFields": {}}, "typeVersion": 2}, {"id": "0c298eab-4a0c-4835-ab93-6ba44d81fb5c", "name": "hilo", "type": "n8n-nodes-base.twitterTool", "position": [740, 240], "parameters": {"text": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Text', ``, 'string') }}", "additionalFields": {"inReplyToStatusId": {"__rl": true, "mode": "id", "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Reply_to_Tweet', `<PERSON><PERSON> hacer reply justo al tweet anterior`, 'string') }}"}}}, "typeVersion": 2}, {"id": "26971067-45ac-43c4-aa8c-15976de81d31", "name": "Agente X", "type": "@n8n/n8n-nodes-langchain.agent", "position": [320, 0], "parameters": {"options": {"systemMessage": "=# Rol\nEres un redactor de tweets informtivos, redactados de manera amigable y entendible.\n\n# Herramientas\n- Util<PERSON> la herramienta *first tweet* para crear el primer tuit.\n- Utiliza la herramienta *hilo* para crear las respuestas a cada tuit anterior, formando un hilo coherente y continuo.\n- Cada tuit (tanto el primero como las respuestas) debe tener un máximo de 270 caracteres.\n- El estilo debe ser en primera persona, cercano y conversacional, como si fuera escrito por mí.\n- Mantén un tono natural y único, con posibles expresiones personales y un enfoque narrativo.\n- El contenido de cada tuit debe conectar de forma fluida con el anterior, para que se perciba como un hilo narrativo.\n\n#Objetivo:\nGenerar un hilo atractivo y coherente, que invite a la interacción.\n\n# Ejemplo de estructura:\nPrimer tuit (con first tweet): \n<PERSON><PERSON><PERSON> tuit (con hilo): Responde al primer tweet\nTercer tuit (con hilo): Responde al segundo tweet\n"}}, "typeVersion": 1.8}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "956762aa-46a5-42eb-bfcd-bf61548456ae", "connections": {"hilo": {"ai_tool": [[{"node": "Agente X", "type": "ai_tool", "index": 0}]]}, "first tweet": {"ai_tool": [[{"node": "Agente X", "type": "ai_tool", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "Agente X", "type": "ai_memory", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Agente X", "type": "ai_languageModel", "index": 0}]]}, "When chat message received": {"main": [[{"node": "Agente X", "type": "main", "index": 0}]]}}}