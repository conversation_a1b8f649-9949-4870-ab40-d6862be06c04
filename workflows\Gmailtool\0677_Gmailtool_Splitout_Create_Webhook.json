{"nodes": [{"id": "38972c5c-09f4-4120-a468-731e720914e1", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [900, -240], "parameters": {"text": "=Title: {{ $json.data.transcript.title }}\n\nParticipants: {{ $json.data.transcript.participants }}\n\nTranscript: {{ JSON.stringify($json.data.transcript.sentences) }}\n\nBullet gist:{{ $json.data.transcript.summary.bullet_gist }}", "agent": "openAiFunctionsAgent", "options": {"systemMessage": "=You get my calls' transcripts from Firefiles.\nThere can be meetings about projects. You can understand if it's about a project if meeting's title contains \"project\". If so - you need to:\n1. Analyze transcript, use tool \"Create Tasks\" to create tasks for me in my AirTable base.\n2. You need to use tool \"Notify Client About Tasks\" to nofity client about his tasks.\n3. If transcript contains info there's a call needed - you'll use \"Create Event\" tool to create call on Google Meet\nCurrent date: {{ $now }}"}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "db5c1bfa-b979-4749-84c8-8cd7d777748c", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [880, 40], "parameters": {"model": "gpt-4o", "options": {}}, "credentials": {"openAiApi": {"id": "9RivS2BmSh1DDBFm", "name": "OpenAi account 3"}}, "typeVersion": 1}, {"id": "334873ba-ec5c-42b3-b8d0-def79d07c0aa", "name": "Create Tasks", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [1040, 40], "parameters": {"name": "create_task", "schemaType": "manual", "workflowId": {"__rl": true, "mode": "list", "value": "Jo0BiizccacaChkH", "cachedResultName": "Firefiles AI Agent"}, "description": "=Use this tool to create a task. \nFor task creation use only action items for me [YOUR NAME HERE], don't use action items for other participants.", "inputSchema": "{\n    \"type\": \"object\",\n    \"properties\": {\n      \"items\": {\n        \"type\": \"array\",\n        \"description\": \"An array of tasks\",\n        \"items\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"name\": {\n              \"type\": \"string\",\n              \"description\": \"The name of the task\"\n            },\n            \"description\": {\n              \"type\": \"string\",\n              \"description\": \"A detailed description of the task\"\n            },\n            \"due_date\": {\n              \"type\": \"string\",\n              \"description\": \"Due Date\"\n            },\n            \"priority\": {\n              \"type\": \"string\",\n              \"description\": \"Priority. . Please capitalize first letter\"\n            },\n            \"project_name\": {\n              \"type\": \"string\",\n              \"description\": \"Name of the project. Word 'Project' shouldn't be included\"\n            }\n          },\n          \"required\": [\n            \"name\",\n            \"description\",\n            \"due_date\",\n            \"priority\"\n          ],\n          \"additionalProperties\": false\n        }\n      }\n    },\n    \"required\": [\n      \"items\"\n    ],\n    \"additionalProperties\": false\n}", "specifyInputSchema": true}, "typeVersion": 1.3}, {"id": "7fd03a80-71e9-4c47-9870-7a3ad4916149", "name": "Notify Client About Tasks", "type": "n8n-nodes-base.gmailTool", "position": [1180, 40], "webhookId": "519d9406-10ef-4ae1-a747-d278002cac9e", "parameters": {"sendTo": "={{ $fromAI(\"participant_email\",\"participant email \",\"string\") }}", "message": "=Summary:\n{{ $json.data.transcript.summary.bullet_gist }}\n\nAction Items:\n{{ $fromAI(\"participant_action_items\",\"participant action items \",\"string\") }}", "options": {"appendAttribution": false}, "subject": "Meeting Summary", "emailType": "text", "descriptionType": "manual", "toolDescription": "=Use the tool to notify a participant of the meeting with meeting summary and his tasks.\nIMPORTANT: \n1. Please notify participants except for me. My email: [YOUR EMAIL HERE]\n2. When working with tasks - please send only the participant's tasks."}, "credentials": {"gmailOAuth2": {"id": "LhdnHxP8WcSDEHw3", "name": "Gmail account 3"}}, "typeVersion": 2.1}, {"id": "094a0e52-a4fa-4078-9b96-80568acb9c51", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [460, 420], "parameters": {}, "typeVersion": 1}, {"id": "e59e5a29-4509-45cc-9130-181ea432553c", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [680, 420], "parameters": {"options": {}, "fieldToSplitOut": "query.items"}, "typeVersion": 1}, {"id": "dc664650-f74e-4574-95a0-dd4a9bf181a1", "name": "Create Task", "type": "n8n-nodes-base.airtable", "position": [900, 420], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appndgSF4faN4jPXi", "cachedResultUrl": "https://airtable.com/appndgSF4faN4jPXi", "cachedResultName": "Philipp's Base"}, "table": {"__rl": true, "mode": "list", "value": "tblaCSndQsSF3gq7Z", "cachedResultUrl": "https://airtable.com/appndgSF4faN4jPXi/tblaCSndQsSF3gq7Z", "cachedResultName": "Tasks"}, "columns": {"value": {"Name": "={{ $json.name }}", "Project": "={{ [$json.project_name] }}", "Due Date": "={{ $json.due_date }}", "Priority": "={{ $json.priority }}", "Description": "={{ $json.description }}"}, "schema": [{"id": "Name", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Description", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Priority", "type": "options", "display": true, "options": [{"name": "Low", "value": "Low"}, {"name": "Medium", "value": "Medium"}, {"name": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, {"name": "low", "value": "low"}, {"name": "medium", "value": "medium"}, {"name": "urgent", "value": "urgent"}], "removed": false, "readOnly": false, "required": false, "displayName": "Priority", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Due Date", "type": "dateTime", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Due Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Project", "type": "array", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Project", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": []}, "options": {"typecast": true}, "operation": "create"}, "credentials": {"airtableTokenApi": {"id": "XT7hvl1w201jtBhx", "name": "Philipp Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "6d6f9094-b0b3-495e-ade8-d80c03e727b0", "name": "Create Event", "type": "n8n-nodes-base.googleCalendarTool", "position": [1340, 40], "parameters": {"end": "={{ $fromAI(\"end_date_time\",\"Date and time of meeting end\",\"string\") }}", "start": "={{ $fromAI(\"start_date_time\",\"Date and time of meeting start\",\"string\") }}", "calendar": {"__rl": true, "mode": "list", "value": "<EMAIL>", "cachedResultName": "<EMAIL>"}, "descriptionType": "manual", "toolDescription": "=Use tool to create Google Calendar Event. Use this tool only when transcript contains information that call should be scheduled.", "additionalFields": {"summary": "={{ $fromAI(\"meeting_name\",\"Meeting name\",\"string\") }}", "attendees": ["={{ $fromAI(\"email\",\"client email\",\"string\") }}"], "conferenceDataUi": {"conferenceDataValues": {"conferenceSolution": "hangouts<PERSON><PERSON><PERSON>"}}}}, "credentials": {"googleCalendarOAuth2Api": {"id": "E5Ufn31vrZLKzh4n", "name": "Google Calendar account"}}, "typeVersion": 1.2}, {"id": "2406fc01-fd28-403c-9378-473e8748e0dd", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [480, -240], "webhookId": "df852a9f-5ea3-43f2-bd49-d045aba5e9c9", "parameters": {"path": "df852a9f-5ea3-43f2-bd49-d045aba5e9c9", "options": {}, "httpMethod": "POST"}, "typeVersion": 2}, {"id": "fe28fa98-4946-4379-970e-6df1a79e2a1e", "name": "Get Meeting Content", "type": "n8n-nodes-base.httpRequest", "position": [700, -240], "parameters": {"url": "https://api.fireflies.ai/graphql", "method": "POST", "options": {}, "jsonBody": "={\n  \"query\": \"query Transcript($transcriptId: String!) { transcript(id: $transcriptId) { title participants speakers { id name } sentences { speaker_name text } summary { bullet_gist } } }\",\n  \"variables\": {\n    \"transcriptId\": \"{{ $json.meetingId }}\"\n  }\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer [YOUR API KEY HERE]"}]}}, "typeVersion": 4.2}, {"id": "5eadd00a-9095-4bf3-80ed-e7bc5c49390d", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [620, -360], "parameters": {"color": 4, "height": 80, "content": "### Replace API key for Fireflies\n"}, "typeVersion": 1}, {"id": "93cee18c-2215-4a63-af7b-ddf45729f5e4", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1180, 200], "parameters": {"color": 4, "height": 80, "content": "### Replace connections for Airtable and Google\n"}, "typeVersion": 1}, {"id": "4d792723-4507-486f-9dc7-62bf1b927edd", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [380, 340], "parameters": {"width": 820, "height": 280, "content": "### Scenario 2 - Create Tasks tool"}, "typeVersion": 1}, {"id": "c5520210-86db-4639-9f8c-ac9055407232", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [380, -460], "parameters": {"width": 1100, "height": 760, "content": "### <PERSON><PERSON><PERSON> 1 - AI agent"}, "typeVersion": 1}, {"id": "48d47e44-b7bf-49b3-814b-6969ce97108d", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [800, 180], "parameters": {"color": 4, "height": 80, "content": "### Replace OpenAI connection\n"}, "typeVersion": 1}, {"id": "afe4bffa-8937-4c31-8513-0acc6b8858ce", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-360, 60], "parameters": {"color": 7, "width": 280, "height": 566, "content": "### Set up steps\n\n#### Preparation\n1. **Create Accounts**:\n   - [N8N](https://n8n.partnerlinks.io/2hr10zpkki6a): For workflow automation.\n   - [Airtable](https://airtable.com/): For database hosting and management.\n   - [Fireflies](https://fireflies.ai/): For recording meetings.\n\n#### N8N Workflow\n\n1. **Configure the Webhook**: \n   - Set up a webhook to capture meeting completion events and integrate it with Fireflies.\n\n2. **Retrieve Meeting Content**: \n   - Use GraphQL API requests to extract meeting details and transcripts, ensuring appropriate authentication through Bearer tokens.\n\n3. **AI Processing Setup**: \n   - Define system messages for AI tasks and configure connections to the AI chat model (e.g., OpenAI's GPT) to process transcripts.\n\n4. **Task Creation Logic**: \n   - Create structured tasks based on AI output, ensuring necessary details are captured and records are created in Airtable.\n\n5. **Client Notifications**: \n   - Use an email node to notify clients about their tasks, ensuring communications are client-specific.\n\n6. **Scheduling Follow-Up Calls**: \n   - Set up Google Calendar events if follow-up meetings are required, populating details from the original meeting context.\n\n7. **Final Testing**: \n   - Conduct tests to ensure each part of the workflow is functional and seamless, making adjustments as needed based on feedback."}, "typeVersion": 1}, {"id": "cbb81fa7-4a97-4a7e-82ce-05250b2c82cf", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-360, -460], "parameters": {"color": 7, "width": 636.2128494576581, "height": 497.1532689930921, "content": "![5min Logo](https://cflobdhpqwnoisuctsoc.supabase.co/storage/v1/object/public/my_storage/banner.png)\n## AI Agent for project management and meetings with Airtable and Fireflies\n**Made by [<PERSON>](https://www.linkedin.com/in/philipp-bekher-5437171a4/) from community [5minAI](https://www.skool.com/5minai-2861)**\n\nManaging action items from meetings can often lead to missed tasks and poor follow-up. This automation alleviates that issue by automatically generating tasks from meeting transcripts, keeping everyone informed about their responsibilities and streamlining communication.\n\nThe workflow leverages n8n to create a Smart Agent that listens for completed meeting transcripts, processes them using AI, and generates tasks in Airtable. Key functionalities include:\n- Capturing completed meeting events through webhooks.\n- Extracting relevant meeting details such as transcripts and participants using API calls.\n- Generating structured tasks from meeting discussions and sending notifications to clients.\n\n"}, "typeVersion": 1}, {"id": "6d367721-875d-4d43-bd55-9801796a0e9f", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-60, 60], "parameters": {"color": 7, "width": 330.5152611046425, "height": 239.5888196628349, "content": "### ... or watch set up video [10 min]\n[![Youtube Link](https://cflobdhpqwnoisuctsoc.supabase.co/storage/v1/object/public/my_storage/Video%2011%20-%20Fireflies%20Agent%20Blur.png)](https://www.youtube.com/watch?v=0TyX7G00x3A)\n"}, "typeVersion": 1}], "pinData": {}, "connections": {"Webhook": {"main": [[{"node": "Get Meeting Content", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[]]}, "Split Out": {"main": [[{"node": "Create Task", "type": "main", "index": 0}]]}, "Create Event": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Create Tasks": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Get Meeting Content": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Notify Client About Tasks": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}}}