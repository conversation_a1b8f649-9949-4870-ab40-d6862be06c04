{"id": "yRMCUm6oJEMknhbw", "meta": {"instanceId": "a5283507e1917a33cc3ae615b2e7d5ad2c1e50955e6f831272ddd5ab816f3fb6"}, "name": "OpenSea Analytics Agent Tool", "tags": [], "nodes": [{"id": "9478ead9-7f35-49b5-aff7-401ce9b8f4af", "name": "Analytics Agent Brain", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [300, 40], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "yUizd8t0sD5wMYVG", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "80910bd9-7889-4185-8cfd-31a6aca270ff", "name": "Analytics Agent Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [440, 40], "parameters": {}, "typeVersion": 1.3}, {"id": "b810757e-caa3-4987-be0a-66284e01d6b9", "name": "OpenSea Get Collection Stats", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [600, 40], "parameters": {"url": "https://api.opensea.io/api/v2/collections/{collection_slug}/stats", "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "toolDescription": "This tool retrieves statistics for a single NFT collection from OpenSea by collection slug.", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "3v99GVMGF4tKP5nM", "name": "OpenSea"}}, "typeVersion": 1.1}, {"id": "288220ab-4321-4916-8ea1-bd27495b3b57", "name": "OpenSea Analytics Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [600, -200], "parameters": {"text": "={{ $json.message }}", "options": {"systemMessage": "# **📢 OpenSea Analytics Agent – System Message**  \n\n## **🔹 Role & Purpose**\nThe **OpenSea Analytics Agent** is an advanced **AI-powered NFT data analyst** with direct access to **OpenSea’s API**. It specializes in **retrieving and analyzing NFT market data**, including:\n- Collection statistics (sales volume, floor prices, market cap, etc.)\n- Transaction histories (sales, bids, transfers, listings)\n- Event tracking for specific wallets, collections, and NFTs  \n- Market trends & price movements  \n\nThis agent **ensures all API calls follow OpenSea’s exact requirements**, preventing invalid queries and ensuring fast, accurate responses.  \n\n---\n\n## **⚡ Available Tools & How to Use Them**\nThe **Analytics Agent** integrates multiple **OpenSea API endpoints** to retrieve, process, and analyze NFT data.\n\n### **1️⃣ OpenSea Get Collection Stats**\n📍 **Endpoint**: `/api/v2/collections/{collection_slug}/stats`  \n🔹 **Description**: Retrieves **key statistics** for a specific NFT collection, including:\n  - Total sales volume (ETH)  \n  - Number of sales  \n  - Average price  \n  - Market cap  \n  - Number of owners  \n  - Floor price  \n\n🔹 **Required Parameter**:  \n  - `collection_slug` → The unique identifier of the NFT collection on OpenSea  \n\n🔹 **Example Query**:  \n  _\"Get stats for the Bored Ape Yacht Club collection.\"_  \n\n✅ **API Call Example:**  \n```plaintext\nGET https://api.opensea.io/api/v2/collections/boredapeyachtclub/stats\n```  \n\n---\n\n### **2️⃣ OpenSea Get Events**\n📍 **Endpoint**: `/api/v2/events`  \n🔹 **Description**: Retrieves **NFT-related events** (sales, transfers, listings, bids, and redemptions) that occurred within a specific timeframe.  \n\n🔹 **Optional Query Parameters:**  \n  - `after` → Fetch events occurring after this Unix timestamp.  \n  - `before` → Fetch events occurring before this Unix timestamp.  \n  - `event_type` → Filter by event types (`sale`, `transfer`, `redemption`).  \n  - `limit` → Number of results (1-50, default: 50).  \n  - `next` → Cursor for pagination.  \n\n🔹 **Example Query**:  \n  _\"Show me all NFT sales from the last 24 hours.\"_  \n\n✅ **API Call Example:**  \n```plaintext\nGET https://api.opensea.io/api/v2/events?event_type=sale&after=**********\n```  \n\n---\n\n### **3️⃣ OpenSea Get Events (by Account)**\n📍 **Endpoint**: `/api/v2/events/accounts/{address}`  \n🔹 **Description**: Retrieves **all events related to a specific wallet address**.  \n\n🔹 **Required Parameter**:  \n  - `address` → Wallet address of interest  \n\n🔹 **Optional Parameters:**  \n  - `chain` → Blockchain network (**must be valid, see list below**).  \n  - `event_type` → Filter events (`sale`, `transfer`, `redemption`).  \n  - `limit` → Number of results (1-50, default: 50).  \n  - `next` → Cursor for pagination.  \n\n🔹 **Example Query**:  \n  _\"Fetch all NFT transfers for wallet `0x123...abc` on Ethereum.\"_  \n\n✅ **API Call Example:**  \n```plaintext\nGET https://api.opensea.io/api/v2/events/accounts/0x123...abc?event_type=transfer&chain=ethereum\n```  \n\n---\n\n### **4️⃣ OpenSea Get Events (by Collection)**\n📍 **Endpoint**: `/api/v2/events/collection/{collection_slug}`  \n🔹 **Description**: Retrieves **all NFT events related to a specific collection**.  \n\n🔹 **Required Parameter**:  \n  - `collection_slug` → The unique identifier of the NFT collection  \n\n🔹 **Example Query**:  \n  _\"Get the latest 10 sales for Azuki NFTs.\"_  \n\n✅ **API Call Example:**  \n```plaintext\nGET https://api.opensea.io/api/v2/events/collection/azuki?event_type=sale&limit=10\n```  \n\n---\n\n### **5️⃣ OpenSea Get Events (by NFT)**\n📍 **Endpoint**: `/api/v2/events/chain/{chain}/contract/{address}/nfts/{identifier}`  \n🔹 **Description**: Retrieves **all historical events for a specific NFT** based on:\n  - **Blockchain**  \n  - **Smart contract address**  \n  - **Token ID**  \n\n🔹 **Required Parameters**:  \n  - `chain` → Blockchain network (**must be valid, see list below**).  \n  - `address` → Smart contract address of the NFT.  \n  - `identifier` → Unique NFT token ID.  \n\n🔹 **Example Query**:  \n  _\"Show me the last 5 transactions for CryptoPunk #9999.\"_  \n\n✅ **API Call Example:**  \n```plaintext\nGET https://api.opensea.io/api/v2/events/chain/ethereum/contract/******************************************/nfts/9999?limit=5\n```  \n\n---\n\n## **⚠️ Important Rules & Restrictions**\n### **🚨 1. Only Allowed Blockchain Inputs**\n✅ **Valid Blockchains for Queries**:\n- `amoy`\n- `ape_chain`\n- `ape_curtis`\n- `arbitrum`\n- `arbitrum_nova`\n- `arbitrum_sepolia`\n- `avalanche`\n- `avalanche_fuji`\n- `b3`\n- `b3_sepolia`\n- `baobab`\n- `base`\n- `base_sepolia`\n- `bera_chain`\n- `blast`\n- `blast_sepolia`\n- `ethereum`\n- `flow`\n- `flow_testnet`\n- `klaytn`\n- **`matic`** _(Use this instead of \"polygon\")_\n- `monad_testnet`\n- `mumbai`\n- `optimism`\n- `optimism_sepolia`\n- `sei_testnet`\n- `sepolia`\n- `shape`\n- `solana`\n- `soldev`\n- `soneium`\n- `soneium_minato`\n- `unichain`\n- `zora`\n- `zora_sepolia`\n\n🚨 **Critical Rule:**\n- ❌ `\"polygon\"` **is NOT a valid chain input** and **must be replaced with** `\"matic\"`.  \n- ❌ Using an unsupported blockchain **will cause an error**.  \n- ✅ Always verify blockchain names before executing a query.\n\n---\n\n## **📌 Example Queries**\n✅ _\"Get NFT sales data for the past 7 days.\"_  \n✅ _\"Fetch the top 5 trending collections by volume.\"_  \n✅ _\"Track all NFT transactions for my wallet `0xabc...xyz`.\"_  \n✅ _\"Show me the most expensive sale in the last 24 hours.\"_  \n\n---\n\n## **⚠️ Error Handling**\nIf an OpenSea API request fails, **check for errors**:\n- ✅ `200` → Success  \n- ❌ `400` → Bad Request (Invalid input format)  \n- ❌ `404` → Not Found (Incorrect `collection_slug`, `address`, or `identifier`)  \n- ❌ `500` → Server Error (OpenSea API issue)  \n\n---\n\n# **🚀 Conclusion**\nThe **OpenSea Analytics Agent** is a specialized **AI-driven NFT analyst** designed to track market trends, analyze transactions, and provide **real-time NFT insights**. Whether you're a **trader, investor, or collector**, this system ensures you stay ahead of the market with **accurate, structured, and powerful NFT analytics**.  \n\n🔥 **Follow all rules to ensure successful API queries!** 🔥"}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "c055762a-8fe7-4141-a639-df2372f30060", "name": "Workflow Input Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [140, -200], "parameters": {"workflowInputs": {"values": [{"name": "message"}, {"name": "sessionId"}]}}, "typeVersion": 1.1}, {"id": "ea5f7259-ff8b-48bc-9bde-01b7d2d42d2b", "name": "OpenSea Get Events", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [780, 40], "parameters": {"url": "https://api.opensea.io/api/v2/events", "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "parametersQuery": {"values": [{"name": "event_type", "valueProvider": "modelOptional"}, {"name": "after", "valueProvider": "modelOptional"}, {"name": "before", "valueProvider": "modelOptional"}, {"name": "limit", "valueProvider": "modelOptional"}, {"name": "next", "valueProvider": "modelOptional"}]}, "toolDescription": "This tool retrieves a list of OpenSea events based on timestamps, event type, and pagination.", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "3v99GVMGF4tKP5nM", "name": "OpenSea"}}, "typeVersion": 1.1}, {"id": "d18c5b15-bc5d-4053-8364-9ecedc36483a", "name": "OpenSea Get Events by Account", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [960, 40], "parameters": {"url": "https://api.opensea.io/api/v2/events/accounts/{address}", "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "parametersQuery": {"values": [{"name": "after", "valueProvider": "modelOptional"}, {"name": "before", "valueProvider": "modelOptional"}, {"name": "chain", "valueProvider": "modelOptional"}, {"name": "event_type", "valueProvider": "modelOptional"}, {"name": "limit", "valueProvider": "modelOptional"}, {"name": "next", "valueProvider": "modelOptional"}]}, "toolDescription": "This tool retrieves a list of OpenSea events for a specific account based on timestamps, chain, event type, and pagination.", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "3v99GVMGF4tKP5nM", "name": "OpenSea"}}, "typeVersion": 1.1}, {"id": "74b00939-5c0f-4974-8d6e-35cfb9dc5c79", "name": "OpenSea Get Events by Collection", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1160, 40], "parameters": {"url": "https://api.opensea.io/api/v2/events/collection/{collection_slug}", "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "parametersQuery": {"values": [{"name": "after", "valueProvider": "modelOptional"}, {"name": "before", "valueProvider": "modelOptional"}, {"name": "event_type", "valueProvider": "modelOptional"}, {"name": "limit", "valueProvider": "modelOptional"}, {"name": "next", "valueProvider": "modelOptional"}]}, "toolDescription": "This tool retrieves a list of OpenSea events for a specific collection based on timestamps, event type, and pagination.", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "3v99GVMGF4tKP5nM", "name": "OpenSea"}}, "typeVersion": 1.1}, {"id": "79af849c-af1b-40a2-861f-91c6217c7a99", "name": "OpenSea Get Events by NFT", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1360, 40], "parameters": {"url": "https://api.opensea.io/api/v2/events/chain/{chain}/contract/{address}/nfts/{identifier}", "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "parametersQuery": {"values": [{"name": "after", "valueProvider": "modelOptional"}, {"name": "before", "valueProvider": "modelOptional"}, {"name": "event_type", "valueProvider": "modelOptional"}, {"name": "limit", "valueProvider": "modelOptional"}, {"name": "next", "valueProvider": "modelOptional"}]}, "toolDescription": "This tool retrieves a list of OpenSea events for a single NFT based on chain, contract address, token ID, timestamps, and event type.", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "3v99GVMGF4tKP5nM", "name": "OpenSea"}}, "typeVersion": 1.1}, {"id": "c268e4cc-2a31-4d0d-b155-bf38c8bb8840", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1260, -1260], "parameters": {"color": 2, "width": 980, "height": 1320, "content": "# OpenSea Analytics Agent Tool (n8n Workflow) Guide\n\n## 🚀 Workflow Overview\nThe **OpenSea Analytics Agent Tool** is an AI-powered NFT analytics system built using **n8n**. It integrates directly with **OpenSea's API** to fetch and analyze market data, collection stats, wallet transactions, and event logs. This tool is designed to provide real-time insights into the NFT ecosystem.\n\n### 🎯 **Key Features**:\n- Retrieve **collection statistics** (volume, floor price, market cap, etc.).\n- Track **NFT events** (sales, transfers, listings, bids, redemptions).\n- Monitor **wallet transactions** (account-specific event tracking).\n- Fetch **NFT-specific historical transactions** by smart contract and token ID.\n- Ensure **API compliance**, preventing invalid queries and errors.\n\n---\n\n## 🔗 **Nodes & Functions**\nBelow is a breakdown of each node in the workflow and its function.\n\n### **1️⃣ Analytics Agent Brain**\n- **Type**: AI Language Model (GPT-4o Mini)\n- **Purpose**: Processes API requests and interprets OpenSea analytics queries.\n\n### **2️⃣ Analytics Agent Memory**\n- **Type**: AI Memory Buffer\n- **Purpose**: Stores session data to maintain context for multiple queries.\n\n### **3️⃣ OpenSea Get Collection Stats**\n- **Type**: API Request\n- **Endpoint**: `/api/v2/collections/{collection_slug}/stats`\n- **Function**: Fetches collection-wide statistics such as floor price, total volume, number of sales, and market cap.\n\n### **4️⃣ OpenSea Get Events**\n- **Type**: API Request\n- **Endpoint**: `/api/v2/events`\n- **Function**: Retrieves NFT-related events within a given timeframe, filtered by event type (sale, transfer, listing, etc.).\n\n### **5️⃣ OpenSea Get Events by Account**\n- **Type**: API Request\n- **Endpoint**: `/api/v2/events/accounts/{address}`\n- **Function**: Tracks all NFT events related to a specific wallet address.\n\n### **6️⃣ OpenSea Get Events by Collection**\n- **Type**: API Request\n- **Endpoint**: `/api/v2/events/collection/{collection_slug}`\n- **Function**: Fetches the latest events for a particular NFT collection.\n\n### **7️⃣ OpenSea Get Events by NFT**\n- **Type**: API Request\n- **Endpoint**: `/api/v2/events/chain/{chain}/contract/{address}/nfts/{identifier}`\n- **Function**: Retrieves all historical events for a single NFT based on blockchain, smart contract, and token ID.\n\n---\n\n"}, "typeVersion": 1}, {"id": "ef15cdff-2e09-4ae9-8c7f-a01119020a29", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-160, -1260], "parameters": {"color": 5, "width": 920, "height": 940, "content": "## 📌 **How to Use the Workflow**\n\n### ✅ **Step 1: Input Data**\n- Provide the necessary parameters like `collection_slug`, `address`, `event_type`, `chain`, and `identifier`.\n\n### ✅ **Step 2: API Calls Execution**\n- The workflow triggers API calls based on the input and retrieves structured NFT analytics data.\n\n### ✅ **Step 3: Data Processing & Output**\n- The AI-powered agent processes responses and formats the output.\n- Results can be sent to Telegram, saved in a database, or displayed in a dashboard.\n\n---\n\n## ⚠️ **Common API Queries & Examples**\n\n### **1️⃣ Get Collection Stats**\n```plaintext\nGET https://api.opensea.io/api/v2/collections/boredapeyachtclub/stats\n```\n\n### **2️⃣ Get Events (Last 24 Hours NFT Sales)**\n```plaintext\nGET https://api.opensea.io/api/v2/events?event_type=sale&after=**********\n```\n\n### **3️⃣ Get Events by Wallet Address**\n```plaintext\nGET https://api.opensea.io/api/v2/events/accounts/0x123...abc?event_type=transfer&chain=ethereum\n```\n\n### **4️⃣ Get Events by NFT**\n```plaintext\nGET https://api.opensea.io/api/v2/events/chain/ethereum/contract/******************************************/nfts/9999?limit=5\n```\n\n---\n\n"}, "typeVersion": 1}, {"id": "03ec28f4-c2bc-4cfe-a799-c0ad5190d77a", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [960, -1260], "parameters": {"color": 3, "width": 820, "height": 460, "content": "## ⚡ **Error Handling & Troubleshooting**\n| **Error Code** | **Description** |\n|--------------|----------------|\n| `200` | Success |\n| `400` | Bad Request (Invalid input) |\n| `404` | Not Found (Incorrect slug, address, or identifier) |\n| `500` | Server Error (OpenSea API issue) |\n\n### 🔹 **Fixing Common Errors**\n- Ensure correct `collection_slug` or `wallet address` is provided.\n- Check if the blockchain name is valid (`matic` instead of `polygon`).\n- If the OpenSea API is down, retry after some time.\n\n---\n\n## 🚀 **Connect with Me for Support**\nIf you need assistance, custom OpenSea insights, or automation support, feel free to connect with me on LinkedIn:\n\n🌐 **Don <PERSON> – LinkedIn**  \n🔗 [http://linkedin.com/in/donjayamahajr](http://linkedin.com/in/donjayamahajr)\n\n"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "59a62d76-59a4-4615-a546-6e3810ca81f2", "connections": {"OpenSea Get Events": {"ai_tool": [[{"node": "OpenSea Analytics Agent", "type": "ai_tool", "index": 0}]]}, "Analytics Agent Brain": {"ai_languageModel": [[{"node": "OpenSea Analytics Agent", "type": "ai_languageModel", "index": 0}]]}, "Analytics Agent Memory": {"ai_memory": [[{"node": "OpenSea Analytics Agent", "type": "ai_memory", "index": 0}]]}, "Workflow Input Trigger": {"main": [[{"node": "OpenSea Analytics Agent", "type": "main", "index": 0}]]}, "OpenSea Get Events by NFT": {"ai_tool": [[{"node": "OpenSea Analytics Agent", "type": "ai_tool", "index": 0}]]}, "OpenSea Get Collection Stats": {"ai_tool": [[{"node": "OpenSea Analytics Agent", "type": "ai_tool", "index": 0}]]}, "OpenSea Get Events by Account": {"ai_tool": [[{"node": "OpenSea Analytics Agent", "type": "ai_tool", "index": 0}]]}, "OpenSea Get Events by Collection": {"ai_tool": [[{"node": "OpenSea Analytics Agent", "type": "ai_tool", "index": 0}]]}}}