{"meta": {"instanceId": "257476b1ef58bf3cb6a46e65fac7ee34a53a5e1a8492d5c6e4da5f87c9b82833", "templateId": "2062"}, "nodes": [{"id": "aac9c0d2-a278-4ea3-acf1-1aca547e30da", "name": "HTML", "type": "n8n-nodes-base.html", "position": [1520, 480], "parameters": {"options": {}, "operation": "extractHtmlContent", "extractionValues": {"values": [{"key": "Title", "cssSelector": "h2.single-post-title"}, {"key": "Introduction", "cssSelector": ".kiwi-highlighter-content-area > p:nth-child(1)"}, {"key": "Header", "cssSelector": "div.kiwi-highlighter-content-area", "returnValue": "html"}, {"key": "={{ $json.data }}"}]}}, "typeVersion": 1}, {"id": "b0eb2240-ffa3-4e80-af7a-2aff470c02ee", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [660, 640], "parameters": {"url": "https://mailsafi.com/blog/", "options": {}}, "typeVersion": 4.1, "alwaysOutputData": true}, {"id": "e8dd3215-2cec-48ba-9a0b-7b3c01a4a637", "name": "HTML1", "type": "n8n-nodes-base.html", "position": [840, 640], "parameters": {"options": {}, "operation": "extractHtmlContent", "extractionValues": {"values": [{"key": "post", "cssSelector": ".entry-title > a", "returnArray": true}, {"key": "Link", "attribute": "href", "cssSelector": " .lae-read-more > a", "returnArray": true, "returnValue": "attribute"}]}}, "typeVersion": 1}, {"id": "5e408e44-7424-419f-9e24-1b619a96a1e0", "name": "Item Lists", "type": "n8n-nodes-base.itemLists", "position": [1000, 640], "parameters": {"options": {}, "fieldToSplitOut": "post , Link"}, "typeVersion": 3.1}, {"id": "d7c6088e-efa3-4fbb-a53f-a7fc7bebdb84", "name": "Medium", "type": "n8n-nodes-base.medium", "position": [1580, 700], "parameters": {"title": "={{ $json.Title }}", "content": "={{ $json.<PERSON>er }}", "contentFormat": "html", "additionalFields": {"tags": "Email Hosting, Email, Email Marketing", "publishStatus": "public", "notifyFollowers": false}}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "5004a96e-16df-4100-84ae-df0b3be3a008", "name": "HTTP Request1", "type": "n8n-nodes-base.httpRequest", "position": [1360, 480], "parameters": {"url": "={{ $json.Link }}", "options": {}}, "typeVersion": 4.1, "alwaysOutputData": true}, {"id": "a34c5b31-c6ba-4e87-9177-a078aa100157", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [1300, 640], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "ee3e4f81-fa05-4ad0-a46e-6452d2f3c521", "name": "Item Lists1", "type": "n8n-nodes-base.itemLists", "position": [1140, 640], "parameters": {"maxItems": 5, "operation": "limit"}, "typeVersion": 3.1}, {"id": "f472a15d-aa5a-4c40-b283-78d69a2a9b57", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [460, 640], "parameters": {}, "typeVersion": 1}, {"id": "f89e199d-72e5-4426-8e9d-82f6ce39ac42", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [380, 200], "parameters": {"width": 688.6526946107781, "height": 522.559880239521, "content": "## Usage \n**How to use me** This workflow gets all the posts from your WordPress site and sorts them into a clear format before publishing them to medium.\n\nStep 1. Set up the HTTP node and set the URL of the source destination. This will be the URL of the blog you want to use. We shall be using https://mailsafi.com/blog for this.\n\nStep 2. Extract the URLs of all the blogs on the page\nThis gets all the blog titles and their URLs. Its an easy way to sort ou which blogs to share and which not to share.\n\nStep 3. Split the entries for easy sorting or a cleaner view.\n\nStep 4. Set a new https node with all the blog URLs that we got from the previous steps. \n\nStep 5. Extract the contents of the blog\n\nStep 6. Add the medium node and then set the contents that you want to be shared out.\n\nExecute your work flow and you are good to go\n\n\n"}, "typeVersion": 1}], "pinData": {}, "connections": {"HTML": {"main": [[{"node": "Medium", "type": "main", "index": 0}]]}, "HTML1": {"main": [[{"node": "Item Lists", "type": "main", "index": 0}]]}, "Medium": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Item Lists": {"main": [[{"node": "Item Lists1", "type": "main", "index": 0}]]}, "Item Lists1": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "HTML1", "type": "main", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "HTML", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [null, [{"node": "HTTP Request1", "type": "main", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}}}