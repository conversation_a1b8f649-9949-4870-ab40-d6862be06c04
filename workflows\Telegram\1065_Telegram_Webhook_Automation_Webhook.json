{"id": "5", "name": "bash-dash telegram", "nodes": [{"name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [450, 450], "webhookId": "b43ae7e2-a058-4738-8d49-ac76db6e8166", "parameters": {"path": "telegram", "options": {"responsePropertyName": "response"}, "responseMode": "lastNode"}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [850, 450], "parameters": {"values": {"string": [{"name": "response", "value": "=Sent message to {{$node[\"Telegram\"].json[\"result\"][\"chat\"][\"first_name\"]}}: \"{{$node[\"Telegram\"].parameter[\"text\"]}}\""}]}, "options": {}}, "typeVersion": 1}, {"name": "Telegram", "type": "n8n-nodes-base.telegram", "position": [650, 450], "parameters": {"text": "={{$node[\"Webhook\"].json[\"query\"][\"parameter\"]}}", "chatId": "123456789", "additionalFields": {}}, "credentials": {"telegramApi": "telegram_bot"}, "typeVersion": 1}], "active": true, "settings": {}, "connections": {"Set": {"main": [[]]}, "Webhook": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "Telegram": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}}