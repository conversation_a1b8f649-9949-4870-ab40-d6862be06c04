{"nodes": [{"name": "Typeform Trigger", "type": "n8n-nodes-base.typeformTrigger", "position": [500, 520], "parameters": {"formId": ""}, "credentials": {"typeformApi": ""}, "typeVersion": 1}, {"name": "NextCloud", "type": "n8n-nodes-base.nextCloud", "position": [650, 300], "parameters": {"path": "examples/Problems.xls", "operation": "download"}, "credentials": {"nextCloudApi": ""}, "typeVersion": 1}, {"name": "Spreadsheet File", "type": "n8n-nodes-base.spreadsheetFile", "position": [800, 300], "parameters": {}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [1000, 470], "parameters": {}, "typeVersion": 1}, {"name": "Spreadsheet File1", "type": "n8n-nodes-base.spreadsheetFile", "position": [1150, 470], "parameters": {"operation": "toFile"}, "typeVersion": 1}, {"name": "NextCloud1", "type": "n8n-nodes-base.nextCloud", "position": [1300, 470], "parameters": {"path": "={{$node[\"NextCloud\"].parameter[\"path\"]}}", "binaryDataUpload": true}, "credentials": {"nextCloudApi": ""}, "typeVersion": 1}], "connections": {"Merge": {"main": [[{"node": "Spreadsheet File1", "type": "main", "index": 0}]]}, "NextCloud": {"main": [[{"node": "Spreadsheet File", "type": "main", "index": 0}]]}, "Spreadsheet File": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Typeform Trigger": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Spreadsheet File1": {"main": [[{"node": "NextCloud1", "type": "main", "index": 0}]]}}}