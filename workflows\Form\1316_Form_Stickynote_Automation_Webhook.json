{"id": "AqWXpCre4fsPEkAH", "meta": {"instanceId": "7dfa146768a036d27a67d125f90ea637bfb301bd4fd25d0086548016421d44bd"}, "name": "Simple OpenAI Image Generator", "tags": [], "nodes": [{"id": "526c24bc-3bc5-48c3-ae1e-5b0c0352d07f", "name": "Convert to File", "type": "n8n-nodes-base.convertToFile", "position": [440, 0], "parameters": {"options": {}, "operation": "toBinary", "sourceProperty": "data[0].b64_json"}, "typeVersion": 1.1}, {"id": "20fdcc11-5e8a-4788-b3a3-e556996b59f7", "name": "Prompt and options", "type": "n8n-nodes-base.formTrigger", "position": [0, 0], "webhookId": "b749da3f-836f-4996-a8ee-bc26f8677582", "parameters": {"options": {}, "formTitle": "OpenAI Image Generator", "formFields": {"values": [{"fieldLabel": "Prompt", "placeholder": "Snow-covered mountain village in the Alps", "requiredField": true}, {"fieldType": "dropdown", "fieldLabel": "Image size", "fieldOptions": {"values": [{"option": "1024x1024"}, {"option": "1024x1536"}, {"option": "1536x1024"}]}, "requiredField": true}]}}, "typeVersion": 2.2}, {"id": "eb220b1f-2091-492a-931f-1f2e344b32a6", "name": "OpenAI Image Generation", "type": "n8n-nodes-base.httpRequest", "position": [220, 0], "parameters": {"url": "https://api.openai.com/v1/images/generations", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "model", "value": "gpt-image-1"}, {"name": "prompt", "value": "={{ $json.Prompt }}"}, {"name": "n", "value": "={{ 1 }}"}, {"name": "size", "value": "={{ $json['Image size'] }}"}]}, "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "x1byAha0t8ltLIeW", "name": "OpenAi account"}}, "typeVersion": 4.2}, {"id": "********-490e-4d97-9b0c-1118e2ccdcb6", "name": "Return to form", "type": "n8n-nodes-base.form", "position": [660, 0], "webhookId": "745af4a8-ab3c-4267-aa8d-a8998cc534e5", "parameters": {"options": {"formTitle": "Result"}, "operation": "completion", "respondWith": "returnBinary", "completionTitle": "Result", "completionMessage": "Here is the created image:"}, "typeVersion": 1}, {"id": "a069f63f-139e-4157-a44a-448224f2c119", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-600, 0], "parameters": {"width": 500, "height": 620, "content": "# Welcome to my Simple OpenAI Image Generator Workflow!\n\nThis workflow creates an image with the new OpenAI image model \"GPT-Image-1\" based on a form input.\n\n## This workflow has the following sequence:\n\n1. Form trigger (image prompt and image size input)\n2. Generate the Image via OpenAI API.\n3. Return the image to the input form for download.\n\n## The following accesses are required for the workflow:\n- OpenAI API access: [Documentation](https://docs.n8n.io/integrations/builtin/credentials/openai/)\n\nYou can contact me via LinkedIn, if you have any questions: https://www.linkedin.com/in/friedemann-schuetz"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "d2376df0-9c26-4723-9e97-07fc226e7a53", "connections": {"Convert to File": {"main": [[{"node": "Return to form", "type": "main", "index": 0}]]}, "Prompt and options": {"main": [[{"node": "OpenAI Image Generation", "type": "main", "index": 0}]]}, "OpenAI Image Generation": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}}}