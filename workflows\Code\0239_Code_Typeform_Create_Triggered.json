{"meta": {"instanceId": "8c8c5237b8e37b006a7adce87f4369350c58e41f3ca9de16196d3197f69eabcd"}, "nodes": [{"id": "7917ccbb-ef43-4784-adb9-7347be1f1e20", "name": "Set", "type": "n8n-nodes-base.set", "position": [580, 560], "parameters": {"values": {"string": [{"name": "company", "value": "={{$json[\"What *company* are you contacting us from?\"]}}"}, {"name": "name", "value": "={{$json[\"Let's start with your *first and last name.*\"]}}"}, {"name": "email", "value": "={{$json[\"What *email address* can we reach you at?\"]}}"}, {"name": "n8nFamiliar", "value": "={{$json[\"How familiar are you with*  n8n*?\"]}}"}, {"name": "questions", "value": "={{$json[\"Do you have any *specific questions* about embedding n8n at this stage?\"]}}"}, {"name": "employees", "value": "={{$json[\"How many employees?\"]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"id": "c0cc18d0-fdd1-4ef8-aabe-33bd13667c7d", "name": "Note", "type": "n8n-nodes-base.stickyNote", "position": [540, 360], "parameters": {"width": 760, "height": 440, "content": "## Format Typeform inputs to Pipedrive\nIn this example, we ask for the number of employees at a company. \n\nTo map this to Pipedrive, we need the unique item number coming from Pipedrive for each of these sections. This is what the function node does. \n\nIn the Pipedrive: Organization, we map this under the custom property.\n\n\n\n\n\n\n\n\n"}, "typeVersion": 1}, {"id": "92646ffb-73fb-4fee-a2b4-5060c7e04b59", "name": "Create Organization", "type": "n8n-nodes-base.pipedrive", "position": [1060, 560], "parameters": {"name": "={{$node[\"Map company size\"].json[\"company\"]}}", "resource": "organization", "additionalFields": {"customProperties": {"property": [{"name": "eb7a7fb64081a9b9100c0622c696c159330cf3d2", "value": "={{$node[\"Map company size\"].json[\"pipedriveemployees\"]}}"}]}}}, "credentials": {"pipedriveApi": {"id": "96", "name": "Pipedrive account"}}, "typeVersion": 1}, {"id": "4c1b7376-cc1f-4974-9110-7e1481e3fdbe", "name": "Create Person", "type": "n8n-nodes-base.pipedrive", "position": [1400, 560], "parameters": {"name": "={{$node[\"Map company size\"].json[\"name\"]}}", "resource": "person", "additionalFields": {"email": ["={{$node[\"On form completion\"].json[\"What *email address* can we reach you at?\"]}}"], "org_id": "={{$json.id}}"}}, "credentials": {"pipedriveApi": {"id": "96", "name": "Pipedrive account"}}, "typeVersion": 1}, {"id": "5c463f99-38e0-4c2e-a34c-86fc199b9d1f", "name": "Create Lead", "type": "n8n-nodes-base.pipedrive", "position": [1600, 560], "parameters": {"title": "={{$node[\"Map company size\"].json[\"company\"]}} lead", "resource": "lead", "organization_id": "={{$node[\"Create Organization\"].json.id}}", "additionalFields": {"person_id": "={{$json.id}}"}}, "credentials": {"pipedriveApi": {"id": "96", "name": "Pipedrive account"}}, "typeVersion": 1}, {"id": "d63383ca-a71e-4384-a3fb-942c25d7fe01", "name": "Create Note", "type": "n8n-nodes-base.pipedrive", "position": [1800, 560], "parameters": {"content": "=Website form submitted\n\nQuestion:\n{{$node[\"Map company size\"].json[\"questions\"]}}\n\nCompany Size:\n{{$node[\"Set\"].json[\"employees\"]}}", "resource": "note", "additionalFields": {"lead_id": "={{$json.id}}"}}, "credentials": {"pipedriveApi": {"id": "96", "name": "Pipedrive account"}}, "typeVersion": 1}, {"id": "78568df6-1c6b-493d-b186-9f9246de518a", "name": "On form completion", "type": "n8n-nodes-base.typeformTrigger", "position": [380, 560], "webhookId": "[UPDATE ME]", "parameters": {"formId": "[UPDATE ME]"}, "credentials": {"typeformApi": {"id": "21", "name": "Typeform account"}}, "typeVersion": 1}, {"id": "6bc56059-6ae7-48bd-838c-08e717bd6bd4", "name": "Map company size", "type": "n8n-nodes-base.code", "position": [820, 560], "parameters": {"mode": "runOnceForEachItem", "jsCode": "switch ($input.item.json.employees) {\n  case '< 20':\n  // small\n    $input.item.json.pipedriveemployees='59' \n    break;\n  case '20 - 100':\n    // medium\n    $input.item.json.pipedriveemployees='60' \n    break;\n  case '101 - 500':\n    // large\n    $input.item.json.pipedriveemployees='73' \n    break;\n  case '501 - 1000':\n    // xlarge\n    $input.item.json.pipedriveemployees='74' \n    break;\n  case '1000+':\n    // Enterprise\n    $input.item.json.pipedriveemployees='61' \n    break;\n}\nreturn $input.item;\n"}, "typeVersion": 1}], "connections": {"Set": {"main": [[{"node": "Map company size", "type": "main", "index": 0}]]}, "Create Lead": {"main": [[{"node": "Create Note", "type": "main", "index": 0}]]}, "Create Person": {"main": [[{"node": "Create Lead", "type": "main", "index": 0}]]}, "Map company size": {"main": [[{"node": "Create Organization", "type": "main", "index": 0}]]}, "On form completion": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}, "Create Organization": {"main": [[{"node": "Create Person", "type": "main", "index": 0}]]}}}