{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [240, 300], "parameters": {}, "typeVersion": 1}, {"name": "Is new?", "type": "n8n-nodes-base.if", "position": [680, 300], "parameters": {"conditions": {"string": [{"value1": "={{$json[\"Processed\"]}}", "operation": "isEmpty"}]}}, "typeVersion": 1}, {"name": "Do something here", "type": "n8n-nodes-base.noOp", "position": [900, 100], "parameters": {}, "typeVersion": 1}, {"name": "<PERSON> as processed", "type": "n8n-nodes-base.googleSheets", "position": [1120, 300], "parameters": {"key": "ID", "options": {}, "sheetId": "1SdnwaIJ6xwaZl006FmK2j4f-b00tq7tT7iQgdfe7Qh4", "operation": "update", "authentication": "oAuth2"}, "credentials": {"googleSheetsOAuth2Api": {"id": "228", "name": "Google Sheets account"}}, "typeVersion": 1}, {"name": "Read sheet", "type": "n8n-nodes-base.googleSheets", "position": [460, 300], "parameters": {"options": {}, "sheetId": "1SdnwaIJ6xwaZl006FmK2j4f-b00tq7tT7iQgdfe7Qh4", "authentication": "oAuth2"}, "credentials": {"googleSheetsOAuth2Api": {"id": "228", "name": "Google Sheets account"}}, "typeVersion": 1}, {"name": "Set processed value", "type": "n8n-nodes-base.set", "position": [900, 300], "parameters": {"values": {"string": [{"name": "Processed", "value": "={{ $now.toISO() }}"}]}, "options": {}}, "typeVersion": 1}, {"name": "Run every 5 minutes", "type": "n8n-nodes-base.interval", "position": [240, 100], "parameters": {"unit": "minutes", "interval": 5}, "typeVersion": 1}], "connections": {"Is new?": {"main": [[{"node": "Do something here", "type": "main", "index": 0}, {"node": "Set processed value", "type": "main", "index": 0}]]}, "Read sheet": {"main": [[{"node": "Is new?", "type": "main", "index": 0}]]}, "Run every 5 minutes": {"main": [[{"node": "Read sheet", "type": "main", "index": 0}]]}, "Set processed value": {"main": [[{"node": "<PERSON> as processed", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Read sheet", "type": "main", "index": 0}]]}}}