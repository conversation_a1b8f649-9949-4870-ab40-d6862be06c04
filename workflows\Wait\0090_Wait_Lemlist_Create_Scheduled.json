{"id": 121, "name": "Create Email Campaign From LinkedIn Post Interactions", "nodes": [{"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [280, 500], "parameters": {"triggerTimes": {"item": [{"mode": "everyHour"}]}}, "typeVersion": 1}, {"name": "Exists ?", "type": "n8n-nodes-base.if", "position": [1700, 480], "parameters": {"conditions": {"string": [{"value1": "={{$node[\"Airtable - List\"].json[\"fields\"][\"Email\"]}}", "value2": "={{$node[\"Dropcontact - GET\"].json[\"data\"][0][\"email\"][0][\"email\"]}}"}]}}, "typeVersion": 1}, {"name": "Airtable - List", "type": "n8n-nodes-base.airtable", "position": [1500, 480], "parameters": {"table": "Contacts", "operation": "list", "additionalOptions": {"fields": []}}, "credentials": {"airtableApi": {"id": "", "name": ""}}, "typeVersion": 1}, {"name": "Airtable - Update", "type": "n8n-nodes-base.airtable", "position": [2100, 400], "parameters": {"id": "={{$node[\"Airtable - List\"].json[\"id\"]}}", "table": "Contacts", "options": {"typecast": true}, "operation": "update", "updateAllFields": false}, "credentials": {"airtableApi": {"id": "", "name": ""}}, "typeVersion": 1}, {"name": "Airtable - Create", "type": "n8n-nodes-base.airtable", "position": [2100, 580], "parameters": {"table": "Contacts", "options": {"typecast": true}, "operation": "append"}, "credentials": {"airtableApi": {"id": "", "name": ""}}, "typeVersion": 1}, {"name": "Set - Update", "type": "n8n-nodes-base.set", "position": [1900, 400], "parameters": {"values": {"string": [{"name": "=ID", "value": "={{$node[\"Airtable - List\"].json[\"id\"]}}"}, {"name": "Email", "value": "={{$node[\"Dropcontact - GET\"].json[\"data\"][0][\"email\"][0][\"email\"]}}"}, {"name": "Phone", "value": "={{$node[\"Dropcontact - GET\"].json[\"data\"][0][\"phone\"]}}"}, {"name": "LinkedIn", "value": "={{$node[\"Dropcontact - GET\"].json[\"data\"][0][\"linkedin\"]}}"}, {"name": "Account", "value": "={{$node[\"Dropcontact - GET\"].json[\"data\"][0][\"company\"]}}"}, {"name": "Company website", "value": "={{$node[\"Dropcontact - GET\"].json[\"data\"][0][\"website\"]}}"}]}, "options": {}}, "typeVersion": 1}, {"name": "Set - New", "type": "n8n-nodes-base.set", "position": [1900, 580], "parameters": {"values": {"string": [{"name": "Name", "value": "={{$node[\"Dropcontact - GET\"].json[\"data\"][0][\"full_name\"]}}"}, {"name": "Account", "value": "={{$node[\"Dropcontact - GET\"].json[\"data\"][0][\"company\"]}}"}, {"name": "Company website", "value": "={{$node[\"Dropcontact - GET\"].json[\"data\"][0][\"website\"]}}"}, {"name": "Email", "value": "={{$node[\"Dropcontact - GET\"].json[\"data\"][0][\"email\"][0][\"email\"]}}"}, {"name": "Phone", "value": "={{$node[\"Dropcontact - GET\"].json[\"data\"][0][\"phone\"]}}"}, {"name": "LinkedIn", "value": "={{$node[\"Dropcontact - GET\"].json[\"data\"][0][\"linkedin\"]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.lemlist", "position": [2300, 480], "parameters": {"email": "={{$node[\"Dropcontact - GET\"].json[\"data\"][0][\"email\"][0][\"email\"]}}", "resource": "lead", "campaignId": "", "additionalFields": {"lastName": "={{$node[\"Dropcontact - GET\"].json[\"data\"][0][\"last_name\"]}}", "firstName": "={{$node[\"Dropcontact - GET\"].json[\"data\"][0][\"first_name\"]}}", "companyName": "={{$node[\"Dropcontact - GET\"].json[\"data\"][0][\"company\"]}}"}}, "credentials": {"lemlistApi": {"id": "", "name": ""}}, "retryOnFail": false, "typeVersion": 1, "continueOnFail": true}, {"name": "Hubspot", "type": "n8n-nodes-base.hubspot", "position": [2700, 480], "parameters": {"email": "={{$node[\"Dropcontact - GET\"].json[\"data\"][0][\"email\"][0][\"email\"]}}", "resource": "contact", "additionalFields": {"city": "={{$node[\"Dropcontact - GET\"].json[\"data\"][0][\"siret_city\"]}}", "gender": "={{$node[\"Dropcontact - GET\"].json[\"data\"][0][\"civility\"]}}", "lastName": "={{$node[\"Dropcontact - GET\"].json[\"data\"][0][\"last_name\"]}}", "firstName": "={{$node[\"Dropcontact - GET\"].json[\"data\"][0][\"first_name\"]}}", "websiteUrl": "={{$node[\"Dropcontact - GET\"].json[\"data\"][0][\"website\"]}}", "companyName": "={{$node[\"Dropcontact - GET\"].json[\"data\"][0][\"company\"]}}", "phoneNumber": "={{$node[\"Dropcontact - GET\"].json[\"data\"][0][\"phone\"]}}", "originalSource": "SOCIAL_MEDIA"}}, "credentials": {"hubspotApi": {"id": "", "name": ""}}, "typeVersion": 1}, {"name": "LinkedIn Post Commenters", "type": "n8n-nodes-base.phantombuster", "position": [480, 400], "parameters": {"jsonParameters": true, "additionalFields": {"manualLaunch": true}}, "credentials": {"phantombusterApi": {"id": "", "name": ""}}, "typeVersion": 1}, {"name": "Get Comments", "type": "n8n-nodes-base.phantombuster", "position": [880, 400], "parameters": {"operation": "getOutput", "additionalFields": {}}, "credentials": {"phantombusterApi": {"id": "", "name": ""}}, "executeOnce": true, "typeVersion": 1}, {"name": "Dropcontact", "type": "n8n-nodes-base.dropcontact", "position": [1300, 480], "parameters": {"options": {}, "additionalFields": {"company": "=", "website": "", "linkedin": "", "last_name": "", "first_name": "="}}, "credentials": {"dropcontactApi": {"id": "", "name": ""}}, "typeVersion": 1}, {"name": "Phantombuster", "type": "n8n-nodes-base.phantombuster", "position": [2500, 480], "parameters": {"additionalFields": {}}, "credentials": {"phantombusterApi": {"id": "", "name": ""}}, "typeVersion": 1}, {"name": "LinkedIn Post Liker", "type": "n8n-nodes-base.phantombuster", "position": [480, 600], "parameters": {"jsonParameters": true, "additionalFields": {"manualLaunch": true}}, "credentials": {"phantombusterApi": {"id": "", "name": ""}}, "typeVersion": 1}, {"name": "Get Likers", "type": "n8n-nodes-base.phantombuster", "position": [880, 600], "parameters": {"operation": "getOutput", "additionalFields": {}}, "credentials": {"phantombusterApi": {"id": "", "name": ""}}, "executeOnce": true, "typeVersion": 1}, {"name": "Wait 30s", "type": "n8n-nodes-base.wait", "position": [680, 560], "webhookId": "de87cd0e-ea00-43d8-896c-836494094779", "parameters": {"unit": "seconds", "amount": 30}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Cron": {"main": [[{"node": "LinkedIn Post Commenters", "type": "main", "index": 0}, {"node": "LinkedIn Post Liker", "type": "main", "index": 0}]]}, "Lemlist": {"main": [[{"node": "Phantombuster", "type": "main", "index": 0}]]}, "Exists ?": {"main": [[{"node": "Set - Update", "type": "main", "index": 0}], [{"node": "Set - New", "type": "main", "index": 0}]]}, "Wait 30s": {"main": [[{"node": "Get Comments", "type": "main", "index": 0}]]}, "Set - New": {"main": [[{"node": "Airtable - Create", "type": "main", "index": 0}]]}, "Get Likers": {"main": [[{"node": "Dropcontact", "type": "main", "index": 0}]]}, "Dropcontact": {"main": [[{"node": "Airtable - List", "type": "main", "index": 0}]]}, "Get Comments": {"main": [[{"node": "Dropcontact", "type": "main", "index": 0}]]}, "Set - Update": {"main": [[{"node": "Airtable - Update", "type": "main", "index": 0}]]}, "Phantombuster": {"main": [[{"node": "Hubspot", "type": "main", "index": 0}]]}, "Airtable - List": {"main": [[{"node": "Exists ?", "type": "main", "index": 0}]]}, "Airtable - Create": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Airtable - Update": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "LinkedIn Post Commenters": {"main": [[{"node": "Wait 30s", "type": "main", "index": 0}]]}}}