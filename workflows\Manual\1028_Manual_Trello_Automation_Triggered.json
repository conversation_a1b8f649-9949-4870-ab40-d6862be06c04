{"id": "55", "name": "CFP Selection 2", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [400, 250], "parameters": {}, "typeVersion": 1}, {"name": "Airtable", "type": "n8n-nodes-base.airtable", "position": [600, 250], "parameters": {"table": "", "operation": "list", "application": "", "additionalOptions": {"filterByFormula": "{Total Score} > 15"}}, "credentials": {"airtableApi": "Airtable"}, "typeVersion": 1}, {"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.bannerbear", "position": [800, 250], "parameters": {"templateId": "", "modificationsUi": {"modificationsValues": [{"name": "talk title", "text": "={{$node[\"Airtable\"].json[\"fields\"][\"What's the title of your talk?\"]}}"}, {"name": "abstract", "text": "={{$node[\"Airtable\"].json[\"fields\"][\"Please share the abstract of your talk.\"]}}"}, {"name": "profile image", "imageUrl": "={{$node[\"Airtable\"].json[\"fields\"][\"Please share a URL of your profile picture.\"]}}"}, {"name": "username", "text": "={{$node[\"Airtable\"].json[\"fields\"][\"Your twitter handle\"]}}"}, {"name": "full name", "text": "={{$node[\"Airtable\"].json[\"fields\"][\"<PERSON>, can we get your full name?\"]}}"}]}, "additionalFields": {"waitForImage": true}}, "credentials": {"bannerbearApi": "<PERSON><PERSON><PERSON>"}, "typeVersion": 1}, {"name": "Trello", "type": "n8n-nodes-base.trello", "position": [1000, 250], "parameters": {"name": "={{$node[\"Airtable\"].json[\"fields\"][\"What's the title of your talk?\"]}}", "listId": "", "description": "=Abstract: {{$node[\"Airtable\"].json[\"fields\"][\"Please share the abstract of your talk.\"]}}\n\nName: {{$node[\"Airtable\"].json[\"fields\"][\"Great, can we get your full name?\"]}}\nBio: {{$node[\"Airtable\"].json[\"fields\"][\"Please share a bit of information about you.\"]}}\nEmail: {{$node[\"Airtable\"].json[\"fields\"][\"And what's your email address?\"]}}\nTwitter: {{$node[\"Airtable\"].json[\"fields\"][\"Your twitter handle\"]}}", "additionalFields": {"urlSource": "={{$node[\"Bannerbear\"].json[\"image_url\"]}}"}}, "credentials": {"trelloApi": "Trello"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Airtable": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Bannerbear": {"main": [[{"node": "Trello", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Airtable", "type": "main", "index": 0}]]}}}