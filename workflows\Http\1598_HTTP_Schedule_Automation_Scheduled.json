{"id": "PcVz6j5XLU7Z9MPN", "meta": {"instanceId": "7182053c6096cf2c9d8885665d447ff4ab0753f89cf41ab8a36a48ee405e4b1c", "templateCredsSetupCompleted": true}, "name": "AirQuality Scheduler", "tags": [], "nodes": [{"id": "ea677d9c-fa79-4897-be4d-6b9793050775", "name": "Get Air data", "type": "n8n-nodes-base.httpRequest", "position": [480, 0], "parameters": {"url": "https://api.ambeedata.com/latest/by-lat-lng", "options": {"redirect": {"redirect": {}}}, "sendQuery": true, "sendHeaders": true, "queryParameters": {"parameters": [{"name": "lat", "value": "={{ $('Set Your Location Coordinates').item.json.lat }}"}, {"name": "lng", "value": "={{ $('Set Your Location Coordinates').item.json.lng }}"}]}, "headerParameters": {"parameters": [{"name": "x-api-key"}]}}, "typeVersion": 4.2}, {"id": "1709ec3a-4306-4987-ada3-7b23ad50b432", "name": "Get Pollen data", "type": "n8n-nodes-base.httpRequest", "position": [720, 0], "parameters": {"url": "https://api.ambeedata.com/latest/pollen/by-lat-lng", "options": {}, "sendQuery": true, "sendHeaders": true, "queryParameters": {"parameters": [{"name": "lat", "value": "={{ $('Set Your Location Coordinates').item.json.lat }}"}, {"name": "lng", "value": "={{ $('Set Your Location Coordinates').item.json.lng }}"}]}, "headerParameters": {"parameters": [{"name": "x-api-key"}]}}, "typeVersion": 4.2}, {"id": "10dd46a2-fcdc-4246-a9be-1230527266b3", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [940, 0], "parameters": {"text": "Follow the prompt below", "options": {"systemMessage": "= Hey there! You're a kind and helpful assistant here to make environmental health information easy to understand and act on 💚\n\nYou'll receive two things:\n1️⃣ Real-time environmental data (air quality and pollen levels)  \n2️⃣ A short user profile (to help tailor your suggestions)\n\nYour job is to:\n✨ Summarize today’s environmental conditions  \n🌿 Give smart, caring suggestions based on who the user is  \n\n---\n\n📍 Here’s the environmental data you’ll get:\n<environmental_data>\n🌍 *Location:*  \n• Country: {{ $('Get Air data').item.json.stations[0].countryCode }}  \n• City: {{ $('Get Air data').item.json.stations[0].city }}  \n• Lat/Lng: {{ $('Get Air data').item.json.stations[0].lat }}, {{ $('Get Air data').item.json.stations[0].lng }}\n\n💨 *Air Quality:*  \n• PM2.5: {{ $('Get Air data').item.json.stations[0].PM25 }} µg/m³  \n• AQI: {{ $('Get Air data').item.json.stations[0].AQI }}  \n• Main pollutant: {{ $('Get Air data').item.json.stations[0].aqiInfo.pollutant }}  \n• Level: {{ $('Get Air data').item.json.stations[0].aqiInfo.category }}\n\n🌸 *Pollen Levels:*  \n• Tree pollen: {{ $json.data[0].Count.tree_pollen }} ({{ $json.data[0].Risk.tree_pollen }})  \n• Grass pollen: {{ $json.data[0].Count.grass_pollen }} ({{ $json.data[0].Risk.grass_pollen }})  \n• Weed pollen: {{ $json.data[0].Count.weed_pollen }} ({{ $json.data[0].Risk.weed_pollen }})\n</environmental_data>\n\n---\n\n👧 And here’s the person you’re helping today:\n<user_profile>   \n• Age:  {{ $('Set User Profile').item.json['Age '] }} \n• Health Sensitivity: {{ $('Set User Profile').item.json['Health sensitivities'] }}\n</user_profile>\n\n---\n\n💡 What to do:\n\n1. 📝 **Write a friendly summary**  \nExplain the overall environmental situation today in 2–3 warm, simple sentences.  \nBe sure to:\n- Mention if it’s generally a good or sensitive day to be outdoors.\n- Highlight anything unusually high (e.g., \"Tree pollen is very high today\" or \"Air quality is moderate\").\n- **Include the actual environmental values** (like pollen risk levels: grass_pollen = {{ $json.data[0].Risk.grass_pollen }}, tree_pollen = ..., and AQI = {{ $('Get Air data').item.json.stations[0].AQI }}) clearly in your response.  \nMake the summary sound supportive and easy to understand, like talking to a friend or parent.\n\n2. 🌟 **Give 3 to 5 helpful suggestions**  \nThink like someone who really cares.  \nKeep them practical, gentle, and specific to the user.  \nExamples: stay indoors, wear a mask, take medication, keep windows closed, use a purifier etc.....\n\n---\n\n📦 Format your response like this (with emojis and clarity!) of course ignore \n---\n3. Use the Mail Tool to send the message by email\n✨ Stay warm, helpful, and comforting.  \nEverything you say should feel like advice from someone who truly cares.  \nOnly use the data and profile provided — no guesses or outside info.\n"}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "a4db1c0e-b61b-40cf-a7e7-b2cc0b8be481", "name": "Think", "type": "@n8n/n8n-nodes-langchain.toolThink", "position": [1060, 240], "parameters": {}, "typeVersion": 1}, {"id": "86d89626-68e3-4718-b86c-84acc644a87d", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [900, 240], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4.1", "cachedResultName": "gpt-4.1"}, "options": {}}, "credentials": {"openAiApi": {"id": "bVTwohZmhBo54IXz", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "1bcaf417-dc1c-40a7-be01-f9bd64c4db46", "name": "Gmail", "type": "n8n-nodes-base.gmailTool", "position": [1180, 240], "webhookId": "bcf8b4a4-4adf-4e30-a962-683173e5b442", "parameters": {"sendTo": "<EMAIL>", "message": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message', ``, 'string') }}", "options": {}, "subject": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Subject', ``, 'string') }}", "emailType": "text"}, "credentials": {"gmailOAuth2": {"id": "cfzmH8MNbSo1rgbX", "name": "Gmail account 3"}}, "typeVersion": 2.1}, {"id": "a7ad5577-1f1d-4b69-a869-95fd5634fd7d", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-320, 0], "parameters": {"rule": {"interval": [{"triggerAtHour": 7}]}}, "typeVersion": 1.2}, {"id": "d8276f52-0850-4c93-a834-340acc55f273", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-820, -360], "parameters": {"width": 440, "height": 520, "content": "## How to Get Your Ambee API Key\nAmbee offers free API access, but you need to sign up using a work or university email address (e.g., <EMAIL>, <EMAIL>). Personal emails like Gmail or Outlook won't be accepted.\n\nSteps to get your key:\n\n1.Go to https://www.getambee.com\n\n2.Click “Try API for Free”\n\n3.Use your organization or school email when signing up\n\n4.Confirm your email and copy the key from your dashboard\n\n5.Paste it into the HTTP Request node headers:\n\nx-api-key: YOUR_KEY_HERE\n Tip: If you’re a student, your university email usually works just fine.\n\n"}, "typeVersion": 1}, {"id": "91f908f7-71e6-49f6-84f7-0fe00328c5e3", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-180, 220], "parameters": {"color": 4, "width": 480, "height": 300, "content": "## Set Your Location Coordinates \nLocation Coordinates (Latitude & Longitude)\nTo fetch accurate air and pollen data, you need to input the coordinates of the location you're monitoring.\n\nExample (Braunschweig, Germany):\n- lat: 52.267\n- lng: 10.533\n\nYou can find coordinates using Google Maps or any GPS service."}, "typeVersion": 1}, {"id": "68a7a76f-3154-443b-817f-6f284528c73b", "name": "Set Your Location Coordinates", "type": "n8n-nodes-base.set", "position": [0, 0], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "5a40fdf6-bd34-452c-8290-7583f025fc6b", "name": "lat", "type": "string", "value": "52.267"}, {"id": "4b47ebc4-f061-4906-9d15-36acb931035f", "name": "lng", "type": "string", "value": "10.533"}]}}, "typeVersion": 3.4}, {"id": "aa5fd195-2194-48f2-a07c-b263313ef98b", "name": "Set User Profile", "type": "n8n-nodes-base.set", "position": [240, 0], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "90a7552c-8c06-4ff5-b3c0-af992ef01f36", "name": "Age ", "type": "string", "value": "25"}, {"id": "20740f05-5b99-4e90-afaa-7ef49f62448f", "name": "Health sensitivities", "type": "string", "value": "Allergic to <PERSON>en"}]}}, "typeVersion": 3.4}, {"id": "96eb2b9b-dc91-4853-899a-3d6d729d28a4", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [240, -380], "parameters": {"color": 6, "width": 480, "height": 300, "content": "## Set  User Profile\nThis tells the AI what kind of user you're creating suggestions for.\nIt should include:\n-Age\n-Health sensitivities (e.g., asthma, allergy to pollen)\n\nyou can add more Infos, if you want.\n"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "b8c19f31-e844-4c25-8720-58679f240705", "connections": {"Gmail": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Think": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "AI Agent": {"main": [[]]}, "Get Air data": {"main": [[{"node": "Get Pollen data", "type": "main", "index": 0}]]}, "Get Pollen data": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Set Your Location Coordinates", "type": "main", "index": 0}]]}, "Set User Profile": {"main": [[{"node": "Get Air data", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Set Your Location Coordinates": {"main": [[{"node": "Set User Profile", "type": "main", "index": 0}]]}}}