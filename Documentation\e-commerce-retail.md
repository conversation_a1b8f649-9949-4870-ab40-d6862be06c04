# E-commerce & Retail - N8N Workflows

## Overview
This document catalogs the **E-commerce & Retail** workflows from the n8n Community Workflows repository.

**Category:** E-commerce & Retail  
**Total Workflows:** 11  
**Generated:** 2025-07-27  
**Source:** https://scan-might-updates-postage.trycloudflare.com/api

---

## Workflows

### Shopify Twitter Create Triggered
**Filename:** `0085_Shopify_Twitter_Create_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Shopify, Twitter/X, and Telegram to create new records. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Shopify,Twitter/X,Telegram,  

---

### Creating an Onfleet Task for a new Shopify Fulfillment
**Filename:** `0152_Shopify_Onfleet_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Shopify and Onfleet for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** Shopify,Onfleet,  

---

### Updating Shopify tags on Onfleet events
**Filename:** `0185_Shopify_Onfleet_Automation_Triggered.json`  
**Description:** Webhook-triggered automation that connects Shopify and Onfleet for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** Shopify,Onfleet,  

---

### Shopify Hubspot Create Triggered
**Filename:** `0265_Shopify_HubSpot_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Shopify and Hubspot to create new records. Uses 8 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (8 nodes)  
**Integrations:** Shopify,Hubspot,  

---

### Shopify Zendesk Create Triggered
**Filename:** `0268_Shopify_Zendesk_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Shopify and Zendesk to create new records. Uses 9 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (9 nodes)  
**Integrations:** Shopify,Zendesk,  

---

### Shopify Zendesk Create Triggered
**Filename:** `0269_Shopify_Zendesk_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Shopify and Zendesk to create new records. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Shopify,Zendesk,  

---

### Shopify Mautic Create Triggered
**Filename:** `0278_Shopify_Mautic_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Shopify and Mautic to create new records. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Shopify,Mautic,  

---

### Sync New Shopify Products to Odoo Product
**Filename:** `0961_Shopify_Filter_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Shopify and Odoo to synchronize data. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Shopify,Odoo,  

---

### Shopify Automate Triggered
**Filename:** `1015_Shopify_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Shopify for data processing. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Shopify,  

---

### Manual Shopify Automate Triggered
**Filename:** `1016_Manual_Shopify_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Shopify for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Shopify,  

---

### Sync New Shopify Customers to Odoo Contacts
**Filename:** `1786_Shopify_Filter_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Shopify and Odoo to synchronize data. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Shopify,Odoo,  

---


## Summary

**Total E-commerce & Retail workflows:** 11  
**Documentation generated:** 2025-07-27 14:35:49  
**API Source:** https://scan-might-updates-postage.trycloudflare.com/api  

This documentation was automatically generated using the n8n workflow API endpoints.
