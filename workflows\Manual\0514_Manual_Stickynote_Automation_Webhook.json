{"meta": {"instanceId": "1dd912a1610cd0376bae7bb8f1b5838d2b601f42ac66a48e012166bb954fed5a", "templateId": "2305"}, "nodes": [{"id": "853bd85f-66c8-4ed1-bd86-38f7bb24c02c", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [380, 240], "parameters": {}, "typeVersion": 1}, {"id": "0c06c484-7f84-48a7-803c-1788c15582d5", "name": "Write Result File to Disk", "type": "n8n-nodes-base.readWriteFile", "position": [980, 240], "parameters": {"options": {}, "fileName": "document.pdf", "operation": "write", "dataPropertyName": "=data"}, "typeVersion": 1}, {"id": "3d75bdd7-5b69-421a-a0e4-a2f123feca08", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [720, 100], "parameters": {"width": 218, "height": 132, "content": "## Authentication\nConversion requests must be authenticated. Please create \n[ConvertAPI account to get authentication secret](https://www.convertapi.com/a/signin)"}, "typeVersion": 1}, {"id": "ab417c81-d9ca-4fd2-9f39-d741738f47ee", "name": "Download PPTX File", "type": "n8n-nodes-base.httpRequest", "position": [580, 240], "parameters": {"url": "https://cdn.convertapi.com/public/files/demo.pptx", "options": {"response": {"response": {"responseFormat": "file"}}}}, "typeVersion": 4.2}, {"id": "8612be1b-9840-43aa-85c8-6ec1489a5e39", "name": "File conversion to PDF", "type": "n8n-nodes-base.httpRequest", "position": [780, 240], "parameters": {"url": "https://v2.convertapi.com/convert/pptx/to/pdf", "method": "POST", "options": {"response": {"response": {"responseFormat": "file"}}}, "sendBody": true, "contentType": "multipart-form-data", "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "file", "parameterType": "formBinaryData", "inputDataFieldName": "=data"}]}, "genericAuthType": "httpQueryAuth", "headerParameters": {"parameters": [{"name": "Accept", "value": "application/octet-stream"}]}}, "credentials": {"httpQueryAuth": {"id": "WdAklDMod8fBEMRk", "name": "Query Auth account"}}, "notesInFlow": true, "typeVersion": 4.2}], "pinData": {}, "connections": {"Download PPTX File": {"main": [[{"node": "File conversion to PDF", "type": "main", "index": 0}]]}, "File conversion to PDF": {"main": [[{"node": "Write Result File to Disk", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Download PPTX File", "type": "main", "index": 0}]]}}}