{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "ad5b12df-3bdf-4672-99a7-0034664f29ef", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1180, 120], "parameters": {"color": 4, "width": 420.4803040774015, "height": 189.69151356225348, "content": "## Reply draft with OpenAI Assistant\nThis workflow automatically transfers content of incoming email messages with specific labels into OpenAI Assitant and returns reply draft. After draft is composed, trigger label is deleted from the thread.\n\n**Please remember to configure your OpenAI Assistant first.**"}, "typeVersion": 1}, {"id": "80a93a48-0576-4dfb-817a-34cbc215307a", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-740, 120], "parameters": {"width": 451.41125086385614, "height": 313.3056033573073, "content": "### Schedule trigger and get emails\nRun the workflow in equal intervals and check for threads with specific labels (trigger labels)."}, "typeVersion": 1}, {"id": "0dba2603-8012-47d1-8c60-4068924b74cd", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-1180, 340], "parameters": {"color": 3, "width": 421.0932411886662, "height": 257.42916378714597, "content": "## ⚠️ Note\n\n1. Complete video guide for this workflow is available [on my YouTube](https://youtu.be/a8Dhj3Zh9vQ). \n2. Remember to add your credentials and configure nodes (covered in the video guide).\n3. If you like this workflow, please subscribe to [my YouTube channel](https://www.youtube.com/@workfloows) and/or [my newsletter](https://workfloows.com/).\n\n**Thank you for your support!**"}, "typeVersion": 1}, {"id": "4eb67d90-b834-48f6-8d8a-ed0a9d8321fd", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [520, 120], "parameters": {"width": 381.6458068293894, "height": 313.7892229150129, "content": "### Generate reply\nTransfer email content to OpenAI Assitant and return AI-generated reply.\n"}, "typeVersion": 1}, {"id": "fdb6e16b-5f42-4872-bf63-b18a14220cdf", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1160, 120], "parameters": {"width": 219.88389496558554, "height": 314.75072291501283, "content": "### Create HTML message\nConvert incoming Markdown from OpenAI Assistant into HTML content."}, "typeVersion": 1}, {"id": "111e4276-7f63-4b11-92be-fd9de7e23f05", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [1400, 120], "parameters": {"width": 461.3148409669012, "height": 314.75072291501283, "content": "### Build and encode message\nCreate raw message in RFC standard and encode it into base64 string (please see [Gmail API reference](https://developers.google.com/gmail/api/reference/rest/v1/users.drafts/create) for more details)."}, "typeVersion": 1}, {"id": "0d377266-7fa2-43c4-9259-e8611d52df41", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [1880, 120], "parameters": {"width": 219.88389496558554, "height": 314.75072291501283, "content": "### Insert reply draft\nAdd reply draft from OpenAI Assistant to specific Gmail thread."}, "typeVersion": 1}, {"id": "c743486b-82e0-42f4-bd41-fad6115ac520", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [2120, 120], "parameters": {"width": 219.88389496558554, "height": 314.75072291501283, "content": "### Remove label\nDelete trigger label from the Gmail thread."}, "typeVersion": 1}, {"id": "dfe99c2e-a8e6-48de-a11d-54adaf98a7fe", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [0, 0], "parameters": {"width": 219.88389496558554, "height": 314.75072291501283, "content": "### Return message content\nRetrieve content of the last message in the thread."}, "typeVersion": 1}, {"id": "6f228048-4067-494a-af44-080237c2555c", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [0, 340], "parameters": {"width": 470.88389496558545, "height": 314.75072291501283, "content": "### Get last message from thread\nReturn all messages for a single thread and pass for further processing only the last one."}, "typeVersion": 1}, {"id": "bea0ea14-6198-4022-8ddc-a0c9f895d46e", "name": "Remove AI label from email", "type": "n8n-nodes-base.gmail", "position": [2180, 260], "webhookId": "f19c59fe-49bd-4661-aff3-a50e43e5964a", "parameters": {"resource": "thread", "threadId": "={{ $('Map fields for further processing').item.json[\"threadId\"] }}", "operation": "<PERSON><PERSON><PERSON><PERSON>"}, "credentials": {"gmailOAuth2": {"id": "Sf5Gfl9NiFTNXFWb", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "dc9b5760-1669-4f29-b28d-73cd417775b4", "name": "Add email draft to thread", "type": "n8n-nodes-base.httpRequest", "position": [1940, 260], "parameters": {"url": "https://www.googleapis.com/gmail/v1/users/me/drafts", "method": "POST", "options": {}, "jsonBody": "={\"message\":{\"raw\":\"{{ $json.encoded }}\", \"threadId\": \"{{ $('Map fields for further processing').item.json[\"threadId\"] }}\"}}", "sendBody": true, "specifyBody": "json"}, "typeVersion": 4.2}, {"id": "dbbb607b-701f-49cd-b872-96522abde5b7", "name": "Convert raw to base64", "type": "n8n-nodes-base.code", "position": [1680, 260], "parameters": {"mode": "runOnceForEachItem", "jsCode": "const encoded = Buffer.from($json.raw).toString('base64');\n\nreturn { encoded };"}, "typeVersion": 2}, {"id": "9114d833-1c6a-47f1-bc8d-12fb8e17218e", "name": "Build email raw", "type": "n8n-nodes-base.set", "position": [1480, 260], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "6b6ece46-aeef-4ae5-9a0d-f472e7ced464", "name": "raw", "type": "string", "value": "=To: {{ $json.to }}\nSubject: {{ $json.subject }}\nContent-Type: text/html; charset=\"utf-8\"\n\n{{ $json.response }}"}]}}, "typeVersion": 3.4}, {"id": "67c1b6ca-d419-45a9-a1d5-2a6ac157454f", "name": "Convert response to HTML", "type": "n8n-nodes-base.markdown", "position": [1220, 260], "parameters": {"mode": "markdownToHtml", "options": {}, "markdown": "={{ $json.response }}"}, "typeVersion": 1}, {"id": "97be72d7-20dd-4829-bf1c-f738fb6a8a21", "name": "Map fields for further processing", "type": "n8n-nodes-base.set", "position": [980, 260], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d5022255-06d2-4322-b51e-ae80b7f6eef6", "name": "response", "type": "string", "value": "={{ $json.output }}"}, {"id": "f729def0-3905-4c7d-ad1d-86ef7b001e9c", "name": "threadId", "type": "string", "value": "={{ $('Get single message content').item.json[\"threadId\"] }}"}, {"id": "3ef18ad8-7328-4b97-bd6d-9d395a3c4a48", "name": "to", "type": "string", "value": "={{ $('Get single message content').item.json[\"from\"][\"text\"] }}"}, {"id": "b013770d-fce2-4030-b372-8d94f04b51e9", "name": "subject", "type": "string", "value": "={{ $('Get single message content').item.json[\"subject\"] }}"}, {"id": "69cc71b6-614d-4528-a598-69fbde1b5fd9", "name": "messageId", "type": "string", "value": "={{ $('Get threads with specific labels').item.json[\"id\"] }}"}]}}, "typeVersion": 3.4}, {"id": "545a4296-5307-42ee-8935-2886338e2518", "name": "Ask OpenAI Assistant", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [580, 260], "parameters": {"text": "={{ $json.text }}", "prompt": "define", "options": {}, "resource": "assistant", "assistantId": {"__rl": true, "mode": "list", "value": "asst_s32wsRpwU1HbLt40wRhghB6Y", "cachedResultName": "Eva"}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "4f326fec-e4bb-42dc-9f26-4a3d8ce86f24", "name": "Get single message content", "type": "n8n-nodes-base.gmail", "position": [60, 120], "webhookId": "1ddb410c-fcdd-4230-967a-cf7844727877", "parameters": {"messageId": "={{ $json.id }}", "operation": "get"}, "credentials": {"gmailOAuth2": {"id": "Sf5Gfl9NiFTNXFWb", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "faeb879f-db00-47b7-8995-9fb9025079cf", "name": "Return last message in thread", "type": "n8n-nodes-base.limit", "position": [280, 460], "parameters": {"keep": "lastItems"}, "typeVersion": 1}, {"id": "8085cb54-3c85-4450-9e42-825a9d467d6b", "name": "Get thread messages", "type": "n8n-nodes-base.gmail", "position": [60, 460], "webhookId": "d53aee55-4233-42e3-b0b7-2b4521956013", "parameters": {"options": {}, "resource": "thread", "threadId": "={{ $json.id }}", "operation": "get"}, "credentials": {"gmailOAuth2": {"id": "Sf5Gfl9NiFTNXFWb", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "8a3c1885-8a05-4357-85ea-03cb9e8b24fa", "name": "Loop over threads", "type": "n8n-nodes-base.splitInBatches", "position": [-200, 260], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "74197fae-823e-47bc-a1c9-b2fbe90b1171", "name": "Get threads with specific labels", "type": "n8n-nodes-base.gmail", "position": [-460, 260], "webhookId": "6b7faf91-cc60-482c-b661-dbd702cba2cc", "parameters": {"filters": {"labelIds": []}, "resource": "thread"}, "credentials": {"gmailOAuth2": {"id": "Sf5Gfl9NiFTNXFWb", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "a3360f69-b8e6-4ef8-933a-352243ab9125", "name": "Schedule trigger (1 min)", "type": "n8n-nodes-base.scheduleTrigger", "position": [-680, 260], "parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 1}]}}, "typeVersion": 1.2}], "pinData": {}, "connections": {"Build email raw": {"main": [[{"node": "Convert raw to base64", "type": "main", "index": 0}]]}, "Loop over threads": {"main": [[{"node": "Get single message content", "type": "main", "index": 0}], [{"node": "Get thread messages", "type": "main", "index": 0}]]}, "Get thread messages": {"main": [[{"node": "Return last message in thread", "type": "main", "index": 0}]]}, "Ask OpenAI Assistant": {"main": [[{"node": "Map fields for further processing", "type": "main", "index": 0}]]}, "Convert raw to base64": {"main": [[{"node": "Add email draft to thread", "type": "main", "index": 0}]]}, "Convert response to HTML": {"main": [[{"node": "Build email raw", "type": "main", "index": 0}]]}, "Schedule trigger (1 min)": {"main": [[{"node": "Get threads with specific labels", "type": "main", "index": 0}]]}, "Add email draft to thread": {"main": [[{"node": "Remove AI label from email", "type": "main", "index": 0}]]}, "Get single message content": {"main": [[{"node": "Ask OpenAI Assistant", "type": "main", "index": 0}]]}, "Return last message in thread": {"main": [[{"node": "Loop over threads", "type": "main", "index": 0}]]}, "Get threads with specific labels": {"main": [[{"node": "Loop over threads", "type": "main", "index": 0}]]}, "Map fields for further processing": {"main": [[{"node": "Convert response to HTML", "type": "main", "index": 0}]]}}}