{"meta": {"instanceId": "dbd43d88d26a9e30d8aadc002c9e77f1400c683dd34efe3778d43d27250dde50"}, "nodes": [{"id": "80b17b5c-6a05-45b9-bfa6-97fe84706687", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [940, 320], "parameters": {"url": "=https://yt.lemnoslife.com/videos?part=mostReplayed&id={{ $json.youtubeVideoID }}", "options": {}}, "typeVersion": 4.1}, {"id": "12b006e7-83f0-450e-98a8-3b5c3864fac4", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [1420, 260], "parameters": {"options": {}, "fieldToSplitOut": "items[0].mostReplayed.markers"}, "typeVersion": 1}, {"id": "cb4cdfe1-7601-43e9-b314-818556c4724b", "name": "has intensity data?", "type": "n8n-nodes-base.if", "position": [1160, 320], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "91f8b87d-228f-4877-ad25-5b9cef3a0f86", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.items[0].mostReplayed }}", "rightValue": ""}]}}, "typeVersion": 2}, {"id": "76614979-d1eb-4b9e-8b16-0f22705d0a0a", "name": "No intensity data available for video", "type": "n8n-nodes-base.noOp", "position": [1420, 500], "parameters": {}, "typeVersion": 1}, {"id": "7532185d-30c8-4fab-bb95-6aaa1e96c9f5", "name": "intensity > 0.6", "type": "n8n-nodes-base.filter", "position": [1620, 260], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "86716013-333d-4418-b516-f86f5098abca", "operator": {"type": "number", "operation": "gt"}, "leftValue": "={{ $json.intensityScoreNormalized }}", "rightValue": 0.6}]}}, "typeVersion": 2}, {"id": "6021cfc5-614c-41a5-b08d-557f6b2ceb94", "name": "Filter out moments close to each other", "type": "n8n-nodes-base.filter", "position": [2000, 260], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "7f682942-953b-4489-b892-811b0bec22ce", "operator": {"type": "number", "operation": "gt"}, "leftValue": "={{ $input.all()[ $itemIndex + 1].json.startSec }}", "rightValue": "={{ $input.all()[ $itemIndex ].json.startSec + 20 }}"}]}}, "typeVersion": 2}, {"id": "99e3d626-b394-48e6-925e-b5eca155720f", "name": "Input variables", "type": "n8n-nodes-base.set", "position": [720, 320], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "fcd7c7ef-8b06-45fa-8257-d44ed772cf08", "name": "youtubeVideoID", "type": "string", "value": "={{ $json.query.ytID }}"}]}}, "typeVersion": 3.3}, {"id": "50e81b17-4b82-4a8f-a559-aff6ee671c7f", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "position": [2400, 260], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "engagingMoments"}, "typeVersion": 1}, {"id": "5cfd4462-ff82-499a-9090-e233a6147af6", "name": "Create each moment (human readable)", "type": "n8n-nodes-base.set", "position": [2200, 260], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "2ad5088d-f42a-42f6-931e-bc11e5ce43da", "name": "humanReadableMessage", "type": "string", "value": "=Engaging moment #{{ $itemIndex +1 }}: https://youtu.be/{{ $('Input variables').first().json.youtubeVideoID }}?t={{ $json.startSec.round() - 3 }}\n"}, {"id": "dcbe5150-2aaa-46d4-960e-4cad0204dbf4", "name": "startSec", "type": "string", "value": "={{ $json.startSec.round() }}"}, {"id": "6a554773-9caf-4682-9e36-5d7dfee6d5f5", "name": "directYTURL", "type": "string", "value": "=https://youtu.be/{{ $('Input variables').first().json.youtubeVideoID }}?t={{ $json.startSec.round() - 3 }}"}]}, "includeOtherFields": true}, "typeVersion": 3.3}, {"id": "aa70beee-e6ed-4af4-892c-743f8150a57f", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [500, 320], "webhookId": "21504b31-88e6-4cd9-aaf3-7587427ca5c5", "parameters": {"path": "youtube-engaging-moments-extractor", "options": {}, "responseMode": "responseNode"}, "typeVersion": 1.1}, {"id": "7b55436e-45d7-4fd7-8a08-0127e8dfb299", "name": "millisecs to seconds", "type": "n8n-nodes-base.set", "position": [1800, 260], "parameters": {"include": "except", "options": {}, "assignments": {"assignments": [{"id": "8b350b84-b78f-46d4-adfb-7115b64494ba", "name": "startSec", "type": "number", "value": "={{ $json.startMillis / 1000 }}"}]}, "excludeFields": "startMillis", "includeOtherFields": true}, "typeVersion": 3.3}, {"id": "182da3ef-19c0-4356-866d-159d5aa8be16", "name": "prepare response", "type": "n8n-nodes-base.set", "position": [2620, 260], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "35261be7-c208-4025-bca1-0b41cf011c38", "name": "youtubeID", "type": "string", "value": "={{ $('Webhook').item.json.query.ytID }}"}]}, "includeOtherFields": true}, "typeVersion": 3.3}, {"id": "03830a21-13b3-426d-b972-43ded224b66f", "name": "Respond with \"no results\"", "type": "n8n-nodes-base.respondToWebhook", "position": [1660, 500], "parameters": {"options": {}, "respondWith": "json", "responseBody": "={\n  \"engagingMoments\": null,\n  \"youtubeID\": \"{{ $('Webhook').item.json.query.ytID }}\"\n}"}, "typeVersion": 1}, {"id": "d7e8441c-a429-490b-8993-c714fcbb61a2", "name": "Respond with moments", "type": "n8n-nodes-base.respondToWebhook", "position": [2860, 260], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "cfa06a1f-8e50-4e91-9a18-5b77e315a816", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1620, 480], "parameters": {"color": 3, "width": 307.626814098134, "height": 357.96212854181044, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\nExample response 👇\n![](https://i.ibb.co/7VZVFBh/error-response.png#full-width)"}, "typeVersion": 1}, {"id": "3c4b9ced-1713-4f02-8a95-519e2e4f2ce8", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [2800, 240], "parameters": {"color": 4, "width": 402.30435383552106, "height": 480.9199723565991, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nExample response 👇\n![](https://i.ibb.co/ssymRNt/success-response.png#full-width)"}, "typeVersion": 1}, {"id": "15e8201c-6b72-40f6-bdd2-441a74424aa3", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [500, -180], "parameters": {"color": 5, "width": 362.9578438147888, "height": 424.35936420179615, "content": "## Extract engaging moments from YouTube video\nThis template takes a YouTube video ID and returns potentially engaging moments, based on the \"intensity\" of a certain timestamp 👇\n\n![](https://i.ibb.co/Xz2CDnW/Screenshot-2024-02-28-at-15-51-02.png#full-width)"}, "typeVersion": 1}, {"id": "3edebeb4-c842-4366-a05a-d463fffe449f", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [880, 60], "parameters": {"color": 5, "width": 445.3395991706974, "height": 184.59156876295762, "content": "### How to use\n1. Open `Webhook` node and copy the `Production URL`\n2. Activate the workflow\n3. In a web browser, PostMan or n8n HTTP Request invoke the Production URL: `{prod url}?ytID={youtube ID}`. \ne.g. `{your instance URL}/webhook/youtube-engaging-moments-extractor?ytID=IZsQqarWXtYy`"}, "typeVersion": 1}], "pinData": {}, "connections": {"Webhook": {"main": [[{"node": "Input variables", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "prepare response", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "intensity > 0.6", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "has intensity data?", "type": "main", "index": 0}]]}, "Input variables": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "intensity > 0.6": {"main": [[{"node": "millisecs to seconds", "type": "main", "index": 0}]]}, "prepare response": {"main": [[{"node": "Respond with moments", "type": "main", "index": 0}]]}, "has intensity data?": {"main": [[{"node": "Split Out", "type": "main", "index": 0}], [{"node": "No intensity data available for video", "type": "main", "index": 0}]]}, "millisecs to seconds": {"main": [[{"node": "Filter out moments close to each other", "type": "main", "index": 0}]]}, "Create each moment (human readable)": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "No intensity data available for video": {"main": [[{"node": "Respond with \"no results\"", "type": "main", "index": 0}]]}, "Filter out moments close to each other": {"main": [[{"node": "Create each moment (human readable)", "type": "main", "index": 0}]]}}}