{"meta": {"instanceId": "8c8c5237b8e37b006a7adce87f4369350c58e41f3ca9de16196d3197f69eabcd"}, "nodes": [{"id": "f80aceed-b676-42aa-bf25-f7a44408b1bc", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [375, 115], "webhookId": "6f7b288e-1efe-4504-a6fd-660931327269", "parameters": {"path": "6f7b288e-1efe-4504-a6fd-660931327269", "options": {}, "responseMode": "responseNode"}, "typeVersion": 1}, {"id": "3b9ec913-0bbe-4906-bf8e-da352b556655", "name": "Note1", "type": "n8n-nodes-base.stickyNote", "position": [355, -25], "parameters": {"width": 600, "height": 280, "content": "## Create a simple API endpoint\n\nIn this workflow we show how to create a simple API endpoint with `Webhook` and `Respond to Webhook` nodes\n\n"}, "typeVersion": 1}, {"id": "9c36dae5-0700-450c-9739-e9f3eff31bfe", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [815, 115], "parameters": {"options": {}, "respondWith": "text", "responseBody": "=The URL of the Google search query for the term \"{{$node[\"Webhook\"].json[\"query\"][\"first_name\"]}} {{$node[\"Webhook\"].json[\"query\"][\"last_name\"]}}\" is: {{$json[\"product\"]}}"}, "typeVersion": 1}, {"id": "5a228fcb-78b9-4a28-95d2-d7c9fdf1d4ea", "name": "Create URL string", "type": "n8n-nodes-base.set", "position": [595, 115], "parameters": {"values": {"string": [{"name": "product", "value": "=https://www.google.com/search?q={{$json[\"query\"][\"first_name\"]}}+{{$json[\"query\"][\"last_name\"]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"id": "e7971820-45a8-4dc8-ba4c-b3220d65307a", "name": "Note3", "type": "n8n-nodes-base.stickyNote", "position": [355, 275], "parameters": {"width": 600, "height": 220, "content": "### How to use it\n1. Execute the workflow so that the webhook starts listening\n2. Make a test request by pasting, **in a new browser tab**, the test URL from the `Webhook` node and appending the following test at the end `?first_name=bob&last_name=dylan`\n\nYou will receive the following output in the new tab `The URL of the Google search query for the term \"bob dylan\" is: https://www.google.com/search?q=bob+dylan`\n\n"}, "typeVersion": 1}], "connections": {"Webhook": {"main": [[{"node": "Create URL string", "type": "main", "index": 0}]]}, "Create URL string": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}}}