{"meta": {"instanceId": "8eadf351d49a11e77d3a57adf374670f06c5294af8b1b7c86a1123340397e728"}, "nodes": [{"id": "2f7c95cb-2545-48b6-aa77-55a6619aa3b6", "name": "When clicking \"Test workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [140, 240], "parameters": {}, "typeVersion": 1}, {"id": "1cb42024-9743-4002-b0f5-180d3d95fc44", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [100, 22], "parameters": {"color": 4, "width": 818, "height": 446, "content": "## Email Validation and extract domain\n** This workflow is aimed at making email validation and domain extract using the native functionalities in n8n\n\n** Replace the debugger node with your actual data source to validate your own emails"}, "typeVersion": 1}, {"id": "215ff8f7-f94b-4999-a0db-c3ee93041001", "name": "Set these fields to extract domain", "type": "n8n-nodes-base.set", "position": [660, 240], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "be48e606-536b-48a0-a0b9-ba1ca0296e75", "name": "Valid Em<PERSON>Is email ", "type": "string", "value": "={{ $json.email.isEmail() }}"}, {"id": "68e983c1-3f12-45ab-a441-ca54444a1f42", "name": "Extract Domain", "type": "string", "value": "={{ $json.email.extractDomain() }}"}, {"id": "37447324-b80a-40cf-a41e-92c7550f3702", "name": "email", "type": "string", "value": "={{ $json.email }}"}]}}, "typeVersion": 3.3}, {"id": "e85e9445-2f43-4545-a41d-f9ced6e8c8d9", "name": "Generate random data", "type": "n8n-nodes-base.debugHelper", "position": [420, 240], "parameters": {"category": "randomData", "randomDataType": "email"}, "typeVersion": 1}, {"id": "d7bb0ffd-df07-4f1b-be68-1776fc3fe7e4", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [360, 160], "parameters": {"height": 253, "content": "\nMake sure you replace the Generate random data with your actual data"}, "typeVersion": 1}], "pinData": {"Generate random data": [{"email": "<PERSON><PERSON>@yahoo.com", "confirmed": true}, {"email": "<EMAIL>", "confirmed": true}, {"email": "<PERSON><PERSON>@yahoo.com", "confirmed": false}, {"email": "<PERSON><PERSON>.He<PERSON><EMAIL>", "confirmed": false}, {"email": "<PERSON>@hotmail.com", "confirmed": false}, {"email": "<EMAIL>", "confirmed": true}, {"email": "Constance.Markshotmail.com", "confirmed": false}, {"email": "<EMAIL>", "confirmed": true}, {"email": "<EMAIL>", "confirmed": true}, {"email": "<PERSON><PERSON>@hotmail.com", "confirmed": false}]}, "connections": {"Generate random data": {"main": [[{"node": "Set these fields to extract domain", "type": "main", "index": 0}]]}, "When clicking \"Test workflow\"": {"main": [[{"node": "Generate random data", "type": "main", "index": 0}]]}}}