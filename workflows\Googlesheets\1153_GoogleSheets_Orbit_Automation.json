{"name": "Moving metrics from Google Sheets to Orbit", "nodes": [{"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [1473, 426], "parameters": {"mode": "mergeByKey", "propertyName1": "GitHub Username", "propertyName2": "attributes.slug"}, "typeVersion": 1}, {"name": "Add Members", "type": "n8n-nodes-base.orbit", "position": [1073, 326], "parameters": {"operation": "upsert", "identityUi": {"identityValue": {"source": "github", "searchBy": "username", "username": "={{$json[\"GitHub\"]}}"}}, "workspaceId": "543", "additionalFields": {"name": "={{$json[\"Name\"]}}", "tShirt": "={{$json[\"T-Shirt Size\"]}}", "location": "={{$json[\"Location\"]}}", "tagsToAdd": "={{$json[\"Tags\"]}}"}}, "credentials": {"orbitApi": "Orbit Credentials"}, "typeVersion": 1}, {"name": "Get all members", "type": "n8n-nodes-base.orbit", "position": [1273, 526], "parameters": {"options": {}, "operation": "getAll", "returnAll": true, "workspaceId": "543"}, "credentials": {"orbitApi": "Orbit Credentials"}, "typeVersion": 1}, {"name": "Get Members", "type": "n8n-nodes-base.googleSheets", "position": [873, 326], "parameters": {"range": "Members!A:F", "options": {}, "sheetId": "1GiR5glinWBUJ-pw3w8LpcuwyOXst2z5nnFSak8DQrMQ", "authentication": "oAuth2"}, "credentials": {"googleSheetsOAuth2Api": "Google Sheets Credentials"}, "typeVersion": 1}, {"name": "Get Activities", "type": "n8n-nodes-base.googleSheets", "position": [1273, 326], "parameters": {"range": "Activities!A:D", "options": {"returnAllMatches": true}, "sheetId": "={{$node[\"Get Members\"].parameter[\"sheetId\"]}}", "operation": "lookup", "lookupValue": "={{$node[\"Get Members\"].json[\"GitHub\"]}}", "lookupColumn": "GitHub Username", "authentication": "oAuth2"}, "credentials": {"googleSheetsOAuth2Api": "Google Sheets Credentials"}, "typeVersion": 1}, {"name": "Add Activities", "type": "n8n-nodes-base.orbit", "position": [1673, 426], "parameters": {"title": "={{$json[\"Title\"]}}", "memberId": "={{$json[\"id\"]}}", "resource": "activity", "workspaceId": "543", "additionalFields": {"link": "={{$json[\"Activity Link\"]}}", "description": "={{$node[\"Merge\"].json[\"Description\"]}}"}}, "credentials": {"orbitApi": "Orbit Credentials"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Merge": {"main": [[{"node": "Add Activities", "type": "main", "index": 0}]]}, "Add Members": {"main": [[{"node": "Get Activities", "type": "main", "index": 0}]]}, "Get Members": {"main": [[{"node": "Add Members", "type": "main", "index": 0}]]}, "Get Activities": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Get all members": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}}}