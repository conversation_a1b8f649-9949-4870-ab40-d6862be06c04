{"id": "DnHvQ3KL8v8r5L5Z", "meta": {"instanceId": "ac63467607103d9c95dd644384984672b90b1cb03e07edbaf18fe72b2a6c45bb", "templateCredsSetupCompleted": true}, "name": "Telegram Chat with Buffering", "tags": [], "nodes": [{"id": "a3cc74e9-c696-48de-a04e-d48555641897", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1640, -800], "parameters": {"color": 7, "width": 220, "height": 280, "content": "## 1. Receive Message\n\n"}, "typeVersion": 1}, {"id": "ff18667d-0a31-4768-acf8-ed0d53b2f382", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [160, -840], "parameters": {"color": 7, "width": 600, "height": 520, "content": "## 3. AI Assistant\n"}, "typeVersion": 1}, {"id": "ce90f954-19b6-4224-ae88-b20c4da639e6", "name": "Reply", "type": "n8n-nodes-base.telegram", "position": [920, -700], "webhookId": "e3313c88-0d56-4d06-81cf-b48870dfe2fe", "parameters": {"text": "={{ $json.output }}", "chatId": "={{ $('Receive Message').item.json.message.chat.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "lvrGkOs0ywXp5agp", "name": "Telegram bsde.ai"}}, "typeVersion": 1.2}, {"id": "6f46d89b-034c-47ea-a217-8d007bec1531", "name": "Receive Message", "type": "n8n-nodes-base.telegramTrigger", "position": [-1580, -680], "webhookId": "5047a673-ca1d-4e87-b51b-893108de0a59", "parameters": {"updates": ["message"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "lvrGkOs0ywXp5agp", "name": "Telegram bsde.ai"}}, "typeVersion": 1.1}, {"id": "0f391daa-0e74-4058-8923-52f3c050c9ad", "name": "Wait 10 Seconds", "type": "n8n-nodes-base.wait", "position": [-1000, -580], "webhookId": "87994c9a-fd20-48b6-8dbe-9af36dc40b2f", "parameters": {"amount": 10}, "typeVersion": 1.1}, {"id": "8e6495d8-db6e-4692-ade5-45239049de34", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-1320, -760], "parameters": {"color": 7, "width": 1400, "height": 440, "content": "## 2. <PERSON><PERSON>er Incoming Messages"}, "typeVersion": 1}, {"id": "d4876fd2-2e0b-4f82-9dc3-553f926310bd", "name": "Add to Queued Messages", "type": "n8n-nodes-base.supabase", "position": [-1240, -680], "parameters": {"tableId": "message_queue", "fieldsUi": {"fieldValues": [{"fieldId": "user_id", "fieldValue": "={{ $json.message.chat.id }}"}, {"fieldId": "message", "fieldValue": "={{ $json.message.text }}"}, {"fieldId": "message_id", "fieldValue": "={{ $json.message.message_id }}"}]}}, "credentials": {"supabaseApi": {"id": "1iEg1EzFrF29iqp2", "name": "Supabase (bsde.ai)"}}, "typeVersion": 1}, {"id": "a2eeb77f-2d74-44ac-9812-c3659d2e2803", "name": "No Operation, do nothing", "type": "n8n-nodes-base.noOp", "position": [-340, -460], "parameters": {}, "typeVersion": 1}, {"id": "638fc82e-aba1-4deb-b506-33dcf4746896", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "position": [220, -700], "parameters": {"options": {}, "fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "message"}]}}, "typeVersion": 1}, {"id": "772f60e5-e52f-4779-aa03-e4d532ee4b5c", "name": "Delete Queued Messages", "type": "n8n-nodes-base.supabase", "position": [-100, -700], "parameters": {"filters": {"conditions": [{"keyName": "user_id", "keyValue": "={{ $json.user_id }}", "condition": "eq"}]}, "tableId": "message_queue", "operation": "delete"}, "credentials": {"supabaseApi": {"id": "1iEg1EzFrF29iqp2", "name": "Supabase (bsde.ai)"}}, "typeVersion": 1}, {"id": "16b46a70-85a0-4c8c-94ba-172ebe9aafa4", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [860, -780], "parameters": {"color": 7, "width": 280, "height": 260, "content": "## 4. Send Reply\n\n\n"}, "typeVersion": 1}, {"id": "9162f110-465f-4cd6-9f03-17751d7e43a4", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [380, -460], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "1OMpAMAKR9l3eUDI", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "b47ef0c9-725b-4837-b9e9-96a4ff2b3636", "name": "Sort by Message ID", "type": "n8n-nodes-base.sort", "position": [-580, -680], "parameters": {"options": {}, "sortFieldsUi": {"sortField": [{"fieldName": "message_id"}]}}, "typeVersion": 1}, {"id": "1aa80c99-eec8-4174-bcf3-c6873354ed0f", "name": "Get Queued Messages", "type": "n8n-nodes-base.supabase", "position": [-780, -680], "parameters": {"filters": {"conditions": [{"keyName": "user_id", "keyValue": "={{ $('Receive Message').item.json.message.from.id }}", "condition": "eq"}]}, "tableId": "message_queue", "operation": "getAll", "returnAll": true}, "credentials": {"supabaseApi": {"id": "1iEg1EzFrF29iqp2", "name": "Supabase (bsde.ai)"}}, "typeVersion": 1}, {"id": "********-b5aa-47fe-802c-7d9f31f225cb", "name": "Check Most Recent Message", "type": "n8n-nodes-base.if", "position": [-360, -680], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "loose"}, "combinator": "and", "conditions": [{"id": "8852bab7-230e-442a-a4a2-994e979c8f9f", "operator": {"type": "number", "operation": "equals"}, "leftValue": "={{ $input.last().json.message_id }}\n", "rightValue": "={{ $('Receive Message').item.json.message.message_id }}"}]}, "looseTypeValidation": true}, "typeVersion": 2.2}, {"id": "bed86d81-bb57-42ce-aaa7-4bdc21e1651c", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [420, -700], "parameters": {"text": "={{ $json.message.join(String.fromCharCode(10)) }}", "options": {}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "4f468a14-fbea-44ec-a2b8-e4b3785c0362", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "position": [560, -460], "parameters": {"sessionKey": "={{ $('Receive Message').item.json.message.chat.id }}", "sessionIdType": "customKey"}, "credentials": {"postgres": {"id": "tzLXHvhykxvYghPC", "name": "bsde.ai <PERSON> (Session Pooler)"}}, "typeVersion": 1.3}, {"id": "610516e8-d4ad-448e-ac97-17aad1a31862", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-2420, -820], "parameters": {"width": 700, "height": 420, "content": "## Allow Users to Send a Sequence of Messages to an AI Agent in Telegram with Supabase\n### Use Case\nWhen creating chatbots that interface through applications such as **Telegram** and **WhatsApp**, users can often sends multiple shorter messages in quick succession, in place of a single, longer message. This workflow accounts for this behaviour.\n### What it Does\nThis workflow allows users to send several messages in quick succession, treating them as one coherent conversation instead of separate messages requiring individual responses. \n### How it Works\n1. When messages arrive, they are stored in a **Supabase PostgreSQL** table\n2. The system waits briefly to see if additional messages arrive\n3. If no new messages arrive within the waiting period, all queued messages are:\n   - Combined and processed as a single conversation\n   - Responded to with one unified reply\n   - Deleted from the queue"}, "typeVersion": 1}, {"id": "c8bd8777-fb0f-4941-8674-f5bb7c264506", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-1640, -1060], "parameters": {"width": 520, "height": 220, "content": "### Setup\n1. Create a table in Supabase called **message_queue**. It needs to have the following columns: **user_id** (`uint8`), **message** (`text`), and **message_id** (`uint8`)\n2. Add your **Telegram**, **Supabase**, **OpenAI**, and **PostgreSQL** credentials\n3. Activate the workflow and test by sending multiple messages the Telegram bot in one go\n4. Wait ten seconds after which you will receive a single reply to all of your messages"}, "typeVersion": 1}, {"id": "24604fc7-7957-4e20-8303-b31f2ce1e257", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-1060, -700], "parameters": {"color": 5, "width": 220, "height": 280, "content": "### Modification\nChange the value of *Wait Amount* to vary the buffering window"}, "typeVersion": 1}, {"id": "24f388f3-5655-4bd4-9c30-978efb2dc400", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [180, -480], "parameters": {"color": 5, "width": 340, "height": 140, "content": "### Modification\nReplace this sub-node \nto use a different language\n model"}, "typeVersion": 1}, {"id": "3db12526-6b97-4e3a-b53d-987f5d20c46e", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [380, -800], "parameters": {"color": 5, "width": 340, "height": 240, "content": "### Modification\nAdd a **System Message** to tailor the chatbot to your use case"}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1"}, "versionId": "e415eb18-1bb9-426b-b759-0ba269db1f8f", "connections": {"AI Agent": {"main": [[{"node": "Reply", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Receive Message": {"main": [[{"node": "Add to Queued Messages", "type": "main", "index": 0}]]}, "Wait 10 Seconds": {"main": [[{"node": "Get Queued Messages", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Sort by Message ID": {"main": [[{"node": "Check Most Recent Message", "type": "main", "index": 0}]]}, "Get Queued Messages": {"main": [[{"node": "Sort by Message ID", "type": "main", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Add to Queued Messages": {"main": [[{"node": "Wait 10 Seconds", "type": "main", "index": 0}]]}, "Delete Queued Messages": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Check Most Recent Message": {"main": [[{"node": "Delete Queued Messages", "type": "main", "index": 0}], [{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}}}