{"id": 85, "name": "New WooCommerce Product to Twitter and Telegram", "nodes": [{"name": "Twitter", "type": "n8n-nodes-base.twitter", "position": [720, 300], "parameters": {"text": "=✨ New Product Announcement ✨\nWe have just added {{$json[\"name\"]}}, Head to {{$json[\"permalink\"]}} to find out more.", "additionalFields": {}}, "credentials": {"twitterOAuth1Api": {"id": "37", "name": "joffcom"}}, "typeVersion": 1}, {"name": "Telegram", "type": "n8n-nodes-base.telegram", "position": [720, 500], "parameters": {"text": "=✨ New Product Announcement ✨\nWe have just added {{$json[\"name\"]}}, Head to {{$json[\"permalink\"]}} to find out more.", "chatId": "123456", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "56", "name": "Telegram account"}}, "typeVersion": 1}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.wooCommerceTrigger", "position": [540, 400], "webhookId": "ab7b134b-9b2d-4e0d-b496-1aee30db0808", "parameters": {"event": "product.created"}, "credentials": {"wooCommerceApi": {"id": "48", "name": "WooCommerce account"}}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"WooCommerce Trigger": {"main": [[{"node": "Twitter", "type": "main", "index": 0}, {"node": "Telegram", "type": "main", "index": 0}]]}}}