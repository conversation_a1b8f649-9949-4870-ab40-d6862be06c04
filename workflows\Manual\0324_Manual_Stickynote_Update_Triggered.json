{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "9ec28e5e-8f1a-4f18-82bb-6c51a03f83e9", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [-940, 500], "parameters": {}, "typeVersion": 1}, {"id": "800f3bb9-09bb-41b6-84c5-d9d7abd6c7a8", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-560, 440], "parameters": {"width": 363, "height": 211.90203341144422, "content": "### Q&A on data returned from a workflow"}, "typeVersion": 1}, {"id": "278b573c-70cc-4439-85c0-e415bcf7c4ee", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-320, 700], "parameters": {"width": 262.67019427016413, "height": 255.8330939602389, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nReplace \"Workflow ID\" with the ID the Subworkflow got saved as"}, "typeVersion": 1}, {"id": "313d8f8b-b7d4-4aee-9725-005aa5c5a984", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [-720, 500], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "a3695a2f-e0bb-4277-886d-3f301f24794b", "name": "chatInput", "type": "string", "value": "What notes can you find for <PERSON> and what is his email address?"}]}}, "typeVersion": 3.4}, {"id": "74479c7b-3d64-4715-90e9-250560f3ea3d", "name": "Question and Answer Chain", "type": "@n8n/n8n-nodes-langchain.chainRetrievalQa", "position": [-500, 500], "parameters": {"options": {}}, "typeVersion": 1.4}, {"id": "912affaa-efea-435e-bad5-2f2ac31b7fd6", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-560, 760], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "6da9d2ae-83d6-4bde-b013-71d313fcbe9b", "name": "Workflow Retriever", "type": "@n8n/n8n-nodes-langchain.retrieverWorkflow", "position": [-240, 760], "parameters": {"workflowId": {"__rl": true, "mode": "id", "value": "QacfBRBnf1xOyckC"}}, "typeVersion": 1.1}], "pinData": {}, "connections": {"Edit Fields": {"main": [[{"node": "Question and Answer Chain", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Question and Answer Chain", "type": "ai_languageModel", "index": 0}]]}, "Workflow Retriever": {"ai_retriever": [[{"node": "Question and Answer Chain", "type": "ai_retriever", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}}}