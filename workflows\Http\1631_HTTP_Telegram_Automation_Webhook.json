{"id": "RLWjEhY8L4TORAIj", "meta": {"instanceId": "36399efc72267ed21ee0d3747f5abdd0ee139cb67749ff919ff09fcd65230079", "templateCredsSetupCompleted": true}, "name": "NeurochainAI Basic API Integration", "tags": [], "nodes": [{"id": "da34bd1a-4e4e-4133-acad-939d0cc96596", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "position": [-1740, 880], "webhookId": "05885608-5344-4dcf-81ad-4550b9a01241", "parameters": {"updates": ["*"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "VPtf3hBnwGucAQtu", "name": "TEMPLATE"}}, "typeVersion": 1.1}, {"id": "3b3f4b00-6b3b-4346-8fcc-7ab75bcfe838", "name": "Code", "type": "n8n-nodes-base.code", "notes": "Extract the URL from the previous node", "position": [80, 260], "parameters": {"jsCode": "// O valor vem como um array com uma string, então precisamos pegar o primeiro item do array\nconst rawUrl = $json.choices[0].text;\n\n// Remover colchetes e aspas (se existirem) e pegar o primeiro elemento do array\nconst imageUrl = JSON.parse(rawUrl)[0];\n\nreturn {\n  json: {\n    imageUrl: imageUrl\n  }\n};"}, "notesInFlow": true, "typeVersion": 2}, {"id": "ccb91a15-96b5-42aa-a6ae-ff7ae79d1e8f", "name": "HTTP Request3", "type": "n8n-nodes-base.httpRequest", "position": [240, 260], "parameters": {"url": "={{ $json.imageUrl }}", "options": {}}, "typeVersion": 4.2}, {"id": "588899b6-a68e-407e-b12f-f05c205674c5", "name": "Telegram2", "type": "n8n-nodes-base.telegram", "position": [-520, 500], "parameters": {"text": "⌛", "chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "replyMarkup": "inlineKeyboard", "additionalFields": {"appendAttribution": false, "reply_to_message_id": "={{ $('Telegram Trigger').item.json.message.message_id }}"}}, "credentials": {"telegramApi": {"id": "VPtf3hBnwGucAQtu", "name": "TEMPLATE"}}, "typeVersion": 1.2}, {"id": "e1534b69-d93d-4e8b-a3c4-adbc17c1dacd", "name": "Telegram1", "type": "n8n-nodes-base.telegram", "position": [440, 260], "parameters": {"chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "operation": "sendPhoto", "binaryData": true, "additionalFields": {"caption": "=*Prompt:* `{{ $('Code1').item.json.cleanMessage }}`", "parse_mode": "<PERSON><PERSON>", "reply_to_message_id": "={{ $('Telegram Trigger').item.json.message.message_id }}"}}, "credentials": {"telegramApi": {"id": "VPtf3hBnwGucAQtu", "name": "TEMPLATE"}}, "typeVersion": 1.2}, {"id": "88ba4ced-bdd0-408e-94e1-9e54ed4d1b5d", "name": "Telegram4", "type": "n8n-nodes-base.telegram", "position": [620, 260], "parameters": {"chatId": "={{ $('Telegram2').item.json.result.chat.id }}", "messageId": "={{ $('Telegram2').item.json.result.message_id }}", "operation": "deleteMessage"}, "credentials": {"telegramApi": {"id": "VPtf3hBnwGucAQtu", "name": "TEMPLATE"}}, "typeVersion": 1.2}, {"id": "251a026e-ebfa-44f5-9c80-f30e5c142e23", "name": "Telegram3", "type": "n8n-nodes-base.telegram", "position": [260, 700], "parameters": {"text": "={{ $json.error.message }}", "chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "🔄 Retry", "additionalFields": {"callback_data": "=response= Fluxretry: {{ $('Code1').item.json.cleanMessage }}"}}]}}]}, "additionalFields": {"appendAttribution": false, "reply_to_message_id": "={{ $('Telegram Trigger').item.json.message.message_id }}"}}, "credentials": {"telegramApi": {"id": "VPtf3hBnwGucAQtu", "name": "TEMPLATE"}}, "typeVersion": 1.2}, {"id": "fb71a62a-9cf8-4abf-baa4-885ae4b1a290", "name": "Telegram5", "type": "n8n-nodes-base.telegram", "position": [480, 700], "parameters": {"chatId": "={{ $('Telegram2').item.json.result.chat.id }}", "messageId": "={{ $('Telegram2').item.json.result.message_id }}", "operation": "deleteMessage"}, "credentials": {"telegramApi": {"id": "VPtf3hBnwGucAQtu", "name": "TEMPLATE"}}, "typeVersion": 1.2}, {"id": "0f9bcdf0-0008-447a-900c-6afe5b9d53fe", "name": "Telegram6", "type": "n8n-nodes-base.telegram", "position": [260, 520], "parameters": {"text": "=*Prompt too short*", "chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "replyMarkup": "inlineKeyboard", "additionalFields": {"parse_mode": "<PERSON><PERSON>", "appendAttribution": false, "reply_to_message_id": "={{ $('Telegram Trigger').item.json.message.message_id }}"}}, "credentials": {"telegramApi": {"id": "VPtf3hBnwGucAQtu", "name": "TEMPLATE"}}, "typeVersion": 1.2}, {"id": "d805548a-7379-456c-9bc3-f5fafeb86aed", "name": "Telegram7", "type": "n8n-nodes-base.telegram", "position": [480, 520], "parameters": {"chatId": "={{ $('Telegram2').item.json.result.chat.id }}", "messageId": "={{ $('Telegram2').item.json.result.message_id }}", "operation": "deleteMessage"}, "credentials": {"telegramApi": {"id": "VPtf3hBnwGucAQtu", "name": "TEMPLATE"}}, "typeVersion": 1.2}, {"id": "a3e521a3-aff0-4d31-9a69-626f70f86ae2", "name": "NeurochainAI - REST API", "type": "n8n-nodes-base.httpRequest", "onError": "continueErrorOutput", "position": [-680, 1280], "parameters": {"url": "https://ncmb.neurochain.io/tasks/message", "method": "POST", "options": {}, "jsonBody": "={\n  \"model\": \"Meta-Llama-3.1-8B-Instruct-Q6_K.gguf\",\n  \"prompt\": \"You must respond directly to the user's message, and the message the user sent you is the following message: {{ $('Telegram Trigger').item.json.message.text }}\",\n  \"max_tokens\": 1024,\n  \"temperature\": 0.6,\n  \"top_p\": 0.95,\n  \"frequency_penalty\": 0,\n  \"presence_penalty\": 1.1\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer YOUR-API-KEY-HERE"}, {"name": "Content-Type", "value": "application/json"}]}}, "typeVersion": 4.2, "alwaysOutputData": false}, {"id": "5fea3a8b-3e1b-4c69-b734-3f9dc7647e4b", "name": "TYPING - ACTION", "type": "n8n-nodes-base.telegram", "position": [-1100, 1280], "parameters": {"chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "operation": "sendChatAction"}, "credentials": {"telegramApi": {"id": "VPtf3hBnwGucAQtu", "name": "TEMPLATE"}}, "typeVersion": 1.2}, {"id": "ca183e3d-2bef-4d80-bbb7-c712a0290b2b", "name": "AI Response", "type": "n8n-nodes-base.telegram", "position": [-360, 1000], "parameters": {"text": "={{ $json.choices[0].text }}", "chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "additionalFields": {"parse_mode": "<PERSON><PERSON>", "appendAttribution": false, "reply_to_message_id": "={{ $('Telegram Trigger').item.json.message.message_id }}"}}, "credentials": {"telegramApi": {"id": "VPtf3hBnwGucAQtu", "name": "TEMPLATE"}}, "typeVersion": 1.2}, {"id": "27e65f30-e58e-457d-b3b7-2b74267554e1", "name": "No response", "type": "n8n-nodes-base.telegram", "position": [-140, 1240], "parameters": {"text": "=*No response from worker*", "chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "additionalFields": {"parse_mode": "<PERSON><PERSON>", "appendAttribution": false, "reply_to_message_id": "={{ $('Telegram Trigger').item.json.message.message_id }}"}}, "credentials": {"telegramApi": {"id": "VPtf3hBnwGucAQtu", "name": "TEMPLATE"}}, "typeVersion": 1.2}, {"id": "02cf4dfa-558f-4968-ad09-19f1e40735b0", "name": "Prompt too short", "type": "n8n-nodes-base.telegram", "position": [-140, 1400], "parameters": {"text": "=*Prompt too short*", "chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "replyMarkup": "inlineKeyboard", "additionalFields": {"parse_mode": "<PERSON><PERSON>", "appendAttribution": false, "reply_to_message_id": "={{ $('Telegram Trigger').item.json.message.message_id }}"}}, "credentials": {"telegramApi": {"id": "VPtf3hBnwGucAQtu", "name": "TEMPLATE"}}, "typeVersion": 1.2}, {"id": "943d31e4-3745-49ea-9669-8a560a486cc4", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-400, 1220], "parameters": {"color": 3, "width": 460.4333621829785, "height": 347.9769162173868, "content": "## ERROR"}, "typeVersion": 1}, {"id": "6b5d142f-8d8c-493f-81e7-cedb4e95cd31", "name": "Switch2", "type": "n8n-nodes-base.switch", "position": [-380, 1380], "parameters": {"rules": {"values": [{"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.error.message }}", "rightValue": "=500 - \"{\\\"error\\\":true,\\\"msg\\\":\\\"No response from worker\\\"}\""}]}}, {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "ef851d57-0618-4fe7-8469-a30971a05ee5", "operator": {"type": "string", "operation": "notEquals"}, "leftValue": "{{ $json.error.message }}", "rightValue": "400 - \"{\\\"error\\\":true,\\\"msg\\\":\\\"Prompt string is invalid\\\"}\""}]}}]}, "options": {}}, "typeVersion": 3.2}, {"id": "77651cb7-2530-46b2-89eb-7ac07f39a3ba", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-400, 860], "parameters": {"color": 4, "width": 459.0810102677459, "height": 350.68162004785273, "content": "## SUCCESS\nThis node will send the AI ​​response directly to the Telegram chat."}, "typeVersion": 1}, {"id": "5dce8414-fe7a-450a-a414-553d3e5e01cd", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-830.8527430805248, 861.5987888475245], "parameters": {"color": 5, "width": 411.78262099325127, "height": 705.0354263931183, "content": "## HTTP REQUEST\n\nReplace **MODEL** with the desired AI model from the NeurochainAI dashboard.\n\nReplace YOUR-API-KEY-HERE with your actual NeurochainAI API key.\n\n**Models:**\nMeta-Llama-3.1-8B-Instruct-Q8_0.gguf\nMeta-Llama-3.1-8B-Instruct-Q6_K.gguf\nMistral-7B-Instruct-v0.2-GPTQ-Neurochain-custom-io\nMistral-7B-Instruct-v0.2-GPTQ-Neurochain-custom\nMistral-7B-OpenOrca-GPTQ\nMistral-7B-Instruct-v0.1-gguf-q8_0.gguf\nMistral-7B-Instruct-v0.2-GPTQ\ningredient-extractor-mistral-7b-instruct-v0.1-gguf-q8_0.gguf"}, "typeVersion": 1}, {"id": "3540e1fa-01f8-4b5e-ad7a-1b1c5cd90d08", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-840, 220], "parameters": {"color": 6, "width": 236.80242230495116, "height": 535.7153791682382, "content": "## This node removes the /flux prefix."}, "typeVersion": 1}, {"id": "6720b734-c0ae-4c88-adb6-3931467c780d", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [220, 444], "parameters": {"color": 3, "width": 593.1328365275054, "height": 403.9345258807414, "content": "## ERROR"}, "typeVersion": 1}, {"id": "30332278-399d-4c8f-8470-dfb967764455", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-320, 220], "parameters": {"color": 5, "width": 384.60321058533617, "height": 538.7613862505775, "content": "## HTTP REQUEST\n\nReplace **MODEL** with the desired AI model from the NeurochainAI dashboard.\n\nReplace YOUR-API-KEY-HERE with your actual NeurochainAI API key.\n\n**Models:**\nsuper-flux1-schnell-gguf\nflux1-schnell-gguf"}, "typeVersion": 1}, {"id": "09f17d6a-6229-49ad-b77b-243712552f2b", "name": "Code1", "type": "n8n-nodes-base.code", "position": [-780, 480], "parameters": {"jsCode": "// Acessa a mensagem original que está em $json.message.text\nconst userMessage = $json.message.text;\n\n// Remover o prefixo '/flux' e qualquer espaço extra após o comando\nconst cleanMessage = userMessage.replace(/^\\/flux\\s*/, '');\n\n// Retornar a mensagem limpa\nreturn {\n  json: {\n    cleanMessage: cleanMessage\n  }\n};"}, "typeVersion": 2}, {"id": "0c809796-9776-4238-94b8-0779ad390bc6", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-580, 220], "parameters": {"height": 535.7153791682384, "content": "## This node sends an emoji to indicate that the prompt is being processed."}, "typeVersion": 1}, {"id": "19043710-a61a-46d0-9ab9-bcdf9c94f800", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [220, 80], "parameters": {"color": 4, "width": 596.5768511548468, "height": 350.68162004785273, "content": "## SUCCESS\nThis node will send the AI ​​response directly to the Telegram chat."}, "typeVersion": 1}, {"id": "e5715001-75a3-4da3-84bb-9aad193fe680", "name": "Switch", "type": "n8n-nodes-base.switch", "position": [-1420, 880], "parameters": {"rules": {"values": [{"outputKey": "Flux", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": false, "typeValidation": "loose"}, "combinator": "and", "conditions": [{"id": "f5df9de6-0650-42e4-9a6e-8d1becf16c51", "operator": {"type": "string", "operation": "startsWith"}, "leftValue": "={{ $json.message.text }}", "rightValue": "/flux"}]}, "renameOutput": true}, {"outputKey": "text", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": false, "typeValidation": "loose"}, "combinator": "and", "conditions": [{"id": "a49ecf63-3f68-4e21-a015-d0cbc227c230", "operator": {"type": "string", "operation": "contains"}, "leftValue": "={{ $json.message.text }}", "rightValue": "@NCNAI_BOT"}]}, "renameOutput": true}, {"outputKey": "DM Text", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": false, "typeValidation": "loose"}, "combinator": "and", "conditions": [{"id": "d5ac0c9f-858a-4040-b72e-ae7b522ff60e", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.message.chat.type }}", "rightValue": "private"}]}, "renameOutput": true}]}, "options": {"ignoreCase": true}, "looseTypeValidation": true}, "typeVersion": 3.2}, {"id": "0ebdea59-8518-4078-b07a-9aa24c5e79b5", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [-1840, 200], "parameters": {"width": 623.6530631885605, "height": 648.***********, "content": "## Instructions for Using the Template\nFollow these steps to set up and use this template:\n\n**Create a Telegram Bot**:\n- Open Telegram and search for Bot<PERSON><PERSON>.\n- Use the ``/newbot`` command to create your bot.\n- Follow the prompts and copy the Token provided at the end.\n-------------\n**Obtain a NeurochainAI API Key:**\n\n- Log in to the NeurochainAI Dashboard.\n- Generate an **API Key** under the Inference As Service section.\n- Ensure your account has sufficient credits for usage.\n-------------\n **Configure Telegram Nodes:**\n- Locate all Telegram nodes in the workflow and add your Telegram Bot Token to each node's credentials.\n-------------\n**Configure HTTP Request Nodes:**\n\n- Identify the NeurochainAI - Rest API and NeurochainAI - Flux nodes in the workflow.\nIn each node:\n- Enter your desired model in the Model field.\n- Replace ``YOUR-API-KEY-HERE`` with your API Key in the headers or configuration section.\n-------------\n**Save and Test:**\n- Save the workflow in N8N.\n- Test the workflow by interacting with your Telegram bot to trigger text and image generation tasks."}, "typeVersion": 1}, {"id": "06642d6b-f8e2-48b6-87e3-5f51af75d357", "name": "NeurochainAI - Flux", "type": "n8n-nodes-base.httpRequest", "onError": "continueErrorOutput", "position": [-180, 540], "parameters": {"url": "https://ncmb.neurochain.io/tasks/tti", "method": "POST", "options": {}, "jsonBody": "={\n  \"model\": \"flux1-schnell-gguf\",\n  \"prompt\": \"Generate an image that matches exactly this: {{ $('Code1').item.json.cleanMessage }}\",\n  \"size\": \"1024x1024\",\n  \"quality\": \"standard\",\n  \"n\": 1,\n  \"seed\": {{ Math.floor(Math.random() * 999) + 1 }}\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer YOUR-API-KEY-HERE"}, {"name": "Content-Type", "value": "application/json"}]}}, "typeVersion": 4.2, "alwaysOutputData": false}, {"id": "92820069-3e65-4385-8b79-9b04dd1d3b03", "name": "Switch1", "type": "n8n-nodes-base.switch", "position": [100, 600], "parameters": {"rules": {"values": [{"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.error.message }}", "rightValue": "400 - \"{\\\"error\\\":true,\\\"msg\\\":\\\"Prompt string is invalid\\\"}\""}]}}, {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "ef851d57-0618-4fe7-8469-a30971a05ee5", "operator": {"type": "string", "operation": "notEquals"}, "leftValue": "{{ $json.error.message }}", "rightValue": "400 - \"{\\\"error\\\":true,\\\"msg\\\":\\\"Prompt string is invalid\\\"}\""}]}}]}, "options": {}}, "typeVersion": 3.2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "ef6d73c3-5256-4bc0-9e10-1daf674c083e", "connections": {"Code": {"main": [[{"node": "HTTP Request3", "type": "main", "index": 0}]]}, "Code1": {"main": [[{"node": "Telegram2", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Code1", "type": "main", "index": 0}], [{"node": "TYPING - ACTION", "type": "main", "index": 0}], [{"node": "TYPING - ACTION", "type": "main", "index": 0}]]}, "Switch1": {"main": [[{"node": "Telegram6", "type": "main", "index": 0}], [{"node": "Telegram3", "type": "main", "index": 0}]]}, "Switch2": {"main": [[{"node": "No response", "type": "main", "index": 0}], [{"node": "Prompt too short", "type": "main", "index": 0}]]}, "Telegram1": {"main": [[{"node": "Telegram4", "type": "main", "index": 0}]]}, "Telegram2": {"main": [[{"node": "NeurochainAI - Flux", "type": "main", "index": 0}]]}, "Telegram3": {"main": [[{"node": "Telegram5", "type": "main", "index": 0}]]}, "Telegram6": {"main": [[{"node": "Telegram7", "type": "main", "index": 0}]]}, "HTTP Request3": {"main": [[{"node": "Telegram1", "type": "main", "index": 0}]]}, "TYPING - ACTION": {"main": [[{"node": "NeurochainAI - REST API", "type": "main", "index": 0}]]}, "Telegram Trigger": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "NeurochainAI - Flux": {"main": [[{"node": "Code", "type": "main", "index": 0}], [{"node": "Switch1", "type": "main", "index": 0}]]}, "NeurochainAI - REST API": {"main": [[{"node": "AI Response", "type": "main", "index": 0}], [{"node": "Switch2", "type": "main", "index": 0}]]}}}