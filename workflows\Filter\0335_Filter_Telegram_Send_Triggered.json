{"meta": {"instanceId": "82a17fa4a0b8e81bf77e5ab999d980f392150f2a9541fde626dc5f74857b1f54"}, "nodes": [{"id": "814ab819-7a0d-4647-a8e2-56d90616b4b2", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [962, 306], "parameters": {"width": 307, "height": 1003.1537835638735, "content": "### Switch depending on content\n0 = if command contains the word \"marketing\"\n1 = if command contains the word \"sales\""}, "typeVersion": 1}, {"id": "0c263242-1369-4cd5-83b7-4e2e8ffe99bb", "name": "Keep only messages from a specific chat id", "type": "n8n-nodes-base.filter", "position": [480, 520], "parameters": {"conditions": {"number": [{"value1": "={{ $json.message.chat.id }}", "value2": null, "operation": "equal"}]}}, "typeVersion": 1}, {"id": "8dd8b974-bfdc-4a80-bb94-3d5994872f70", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [660, 311], "parameters": {"height": 382, "content": "### Switch depending on command\n0 = /stop\n1 = /start"}, "typeVersion": 1}, {"id": "fd76d706-01df-453d-b8ad-d3ad1b379fb4", "name": "Deactivate the marketing workflow", "type": "n8n-nodes-base.n8n", "position": [1380, 480], "parameters": {"operation": "deactivate", "workflowId": {"__rl": true, "mode": "url", "value": ""}}, "credentials": {"n8nApi": {"id": "hHsMs7R7sstUSWGD", "name": "n8n account"}}, "typeVersion": 1}, {"id": "b2c976ca-e78f-4b0a-8337-45c66939d30c", "name": "Deactivate the sales workflow", "type": "n8n-nodes-base.n8n", "position": [1380, 680], "parameters": {"operation": "deactivate", "workflowId": {"__rl": true, "mode": "url", "value": ""}}, "credentials": {"n8nApi": {"id": "hHsMs7R7sstUSWGD", "name": "n8n account"}}, "typeVersion": 1}, {"id": "8187bb9d-685b-4955-b7e0-3375a9461bc8", "name": "Activate the marketing workflow", "type": "n8n-nodes-base.n8n", "position": [1380, 940], "parameters": {"operation": "activate", "workflowId": {"__rl": true, "mode": "url", "value": "", "__regex": ".*/workflow/([0-9a-zA-Z]{1,})"}}, "credentials": {"n8nApi": {"id": "hHsMs7R7sstUSWGD", "name": "n8n account"}}, "typeVersion": 1}, {"id": "87d219be-77d0-4e29-9137-d55bdfae4aa7", "name": "Switch depending on content (activate)", "type": "n8n-nodes-base.switch", "position": [1040, 960], "parameters": {"rules": {"rules": [{"value2": "usdc", "operation": "contains", "outputKey": "0"}, {"value2": "hsuite", "operation": "contains", "outputKey": "1"}]}, "value1": "={{ $json.message.text }}", "dataType": "string"}, "typeVersion": 2}, {"id": "fa5f346d-5ad2-4ef3-b715-e45ffb7dfd29", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [60, 740], "parameters": {"width": 846, "height": 575.*************, "content": "# Telegram N8N workflow (de)activator\n\n## What does it do?\nThis workflow helps you to quickly activate or deactivate a workflow through Telegram. Sometimes we are not able to access a PC to resolve an issue if something goes wrong with a workflow. If you, like me, use Telegram to send yourself error reports, you can quickly react in case of urgency. Just by sending '/stop' combined with the name you use for a workflow, you can deactivate a workflow, or reactivate it with '/start'. For example '/stop marketing'.\n\nWalkthrough: https://watch.screencastify.com/v/uWQ88gZKj57WTGOOqSW2 (6min)\n\n## Instructions\n1. Create a Telegram API key through botfather (https://t.me/botfather). Add it to the telegram credentials.\n2. For the N8N nodes, go to settings in your n8n instance. Then 'n8n API' and 'create an API key'. \n3. To ensure that only we can send commands to the bot, we need the chat ID of our DM with our newly created bot. Open the the Telegram trigger and click on 'listen to events'.\n4. Go to Telegram and send a direct message to the bot, this will trigger the Telegram node.\n5. Go to the filter node and fill in the chat id you want to filter for with the data you got from the test event in the Telegram node.\n6. In the first Switch node you can find the commands, in this case it is '/start' and '/stop'. When you send a message to your bot starting with either of those, it will go to the next switch nodes.\n7. Next it will check what other word it contains. As an example I have used the words 'marketing' and 'sales', both corresponding to a marketing and sales workflow. \n8. The last nodes will either activate or deactivate a workflow."}, "typeVersion": 1}, {"id": "d16753af-c1d7-4b60-89da-82432a0b06c1", "name": "Receive commands from Telegram", "type": "n8n-nodes-base.telegramTrigger", "position": [260, 520], "webhookId": "5fe48950-9a59-4b47-b568-6d2f4c624288", "parameters": {"updates": ["message"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "Wn8jg2h69jw2f9Pu", "name": "Telegram account 2"}}, "typeVersion": 1}, {"id": "83a5dc1b-00c9-46b2-9941-78f42d2e06e5", "name": "Activate the sales workflow", "type": "n8n-nodes-base.n8n", "position": [1380, 1160], "parameters": {"operation": "activate", "workflowId": {"__rl": true, "mode": "url", "value": "", "__regex": ".*/workflow/([0-9a-zA-Z]{1,})"}}, "credentials": {"n8nApi": {"id": "hHsMs7R7sstUSWGD", "name": "n8n account"}}, "typeVersion": 1}, {"id": "2bf6ebf2-f94e-4359-bea8-a041bf669644", "name": "Switch depending on command", "type": "n8n-nodes-base.switch", "position": [720, 520], "parameters": {"rules": {"rules": [{"value2": "/stop", "operation": "startsWith", "outputKey": "0"}, {"value2": "/start", "operation": "startsWith", "outputKey": "1"}]}, "value1": "={{ $json.message.text }}", "dataType": "string"}, "typeVersion": 2}, {"id": "a6888317-39b5-4b3d-97a8-c9bf0e90eddb", "name": "Switch depending on content (deactivate)", "type": "n8n-nodes-base.switch", "position": [1040, 500], "parameters": {"rules": {"rules": [{"value2": "marketing", "operation": "contains", "outputKey": "0"}, {"value2": "sales", "operation": "contains", "outputKey": "1"}]}, "value1": "={{ $json.message.text }}", "dataType": "string"}, "typeVersion": 2}], "connections": {"Switch depending on command": {"main": [[{"node": "Switch depending on content (deactivate)", "type": "main", "index": 0}], [{"node": "Switch depending on content (activate)", "type": "main", "index": 0}]]}, "Receive commands from Telegram": {"main": [[{"node": "Keep only messages from a specific chat id", "type": "main", "index": 0}]]}, "Switch depending on content (activate)": {"main": [[{"node": "Activate the marketing workflow", "type": "main", "index": 0}], [{"node": "Activate the sales workflow", "type": "main", "index": 0}]]}, "Switch depending on content (deactivate)": {"main": [[{"node": "Deactivate the marketing workflow", "type": "main", "index": 0}], [{"node": "Deactivate the sales workflow", "type": "main", "index": 0}]]}, "Keep only messages from a specific chat id": {"main": [[{"node": "Switch depending on command", "type": "main", "index": 0}]]}}}