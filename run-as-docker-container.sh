#!/bin/bash

docker compose up -d --build

# 使用 chrome-mcp-server 服务打开浏览器
if command -v npx &> /dev/null; then
  # 通过 MCP 协议启动 chrome-mcp-server 并打开页面
  echo "正在通过 chrome-mcp-server 打开浏览器..."
  npx @modelcontextprotocol/server-chrome --url http://localhost:8000 &
  sleep 2
elif command -v chrome-mcp-server &> /dev/null; then
  # 直接使用 chrome-mcp-server 命令
  chrome-mcp-server --url http://localhost:8000 &
elif [[ "$OSTYPE" == "darwin"* ]]; then
  # macOS 备用方案
  open -a Safari http://localhost:8000
elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" ]]; then
  # Windows 备用方案
  start chrome http://localhost:8000
else
  # 默认备用方案
  echo "chrome-mcp-server 未找到，请手动访问 http://localhost:8000"
fi

echo "应用已启动，访问地址: http://localhost:8000"
