{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "c537229c-ffdf-4a4b-8cdd-1d88621e58a0", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "position": [-1060, 2060], "webhookId": "1b9f2217-7c53-4440-b62b-aafcf1e1d45d", "parameters": {"updates": ["message"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "wJqs1eMdbjilZH1W", "name": "jimleuk_ai_survey_demo_bot"}}, "typeVersion": 1.1}, {"id": "90426896-35d8-4449-8607-70e8485441ed", "name": "Send Next Question", "type": "n8n-nodes-base.telegram", "position": [2560, 1900], "webhookId": "4306c719-0b05-4986-8ea8-8bcda06e0ad1", "parameters": {"text": "={{ $('Get Survey State1').first().json.next_question_idx }}. {{ Object.values($('Get Columns1').first().json)[$('Get Survey State1').first().json.next_question_idx+1] }}", "chatId": "={{ $('<PERSON><PERSON><PERSON> Trigger').first().json.message.chat.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "wJqs1eMdbjilZH1W", "name": "jimleuk_ai_survey_demo_bot"}}, "typeVersion": 1.2}, {"id": "614b6d06-7bca-4710-aebf-3a2a685ace09", "name": "Send Response", "type": "n8n-nodes-base.telegram", "position": [1540, 2540], "webhookId": "dd1c616f-891c-48d4-8828-e612b13bca63", "parameters": {"text": "={{ $('Interview Agent1').first().json.output }}", "chatId": "={{ $('<PERSON><PERSON><PERSON> Trigger').first().json.message.chat.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "wJqs1eMdbjilZH1W", "name": "jimleuk_ai_survey_demo_bot"}}, "typeVersion": 1.2}, {"id": "c7f7acc4-c3f7-4fae-98dd-25dca9caae8b", "name": "Has No Record?", "type": "n8n-nodes-base.if", "position": [60, 2260], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a9f08592-d870-44a4-a7f9-d70193cf721b", "operator": {"type": "object", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "207a2647-56eb-419a-8269-9a32edabe43a", "name": "Is Survey Continue?", "type": "n8n-nodes-base.if", "position": [1980, 2060], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "5685829b-a2b2-42c8-b6bc-22eaa03db1d2", "operator": {"type": "boolean", "operation": "false", "singleValue": true}, "leftValue": "={{ $('Increment Index1').item.json.is_survey_complete }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "a8e98579-6afa-4d7d-859d-9e635a0a1b0c", "name": "Get State2", "type": "n8n-nodes-base.redis", "position": [-520, 2060], "parameters": {"key": "={{ $json.cacheKey }}", "options": {}, "operation": "get", "propertyName": "data"}, "credentials": {"redis": {"id": "zU4DA70qSDrZM1El", "name": "Redis account (localhost)"}}, "typeVersion": 1}, {"id": "8074143c-eab7-4146-937c-16c44ef40333", "name": "Get Columns1", "type": "n8n-nodes-base.googleSheets", "position": [1440, 2060], "parameters": {"options": {"dataLocationOnSheet": {"values": {"firstDataRow": 1, "rangeDefinition": "specifyRange"}}}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1KMNqWWO36osZx7jF-i1UL53z2GZnCn9fiBjtlGpvWB4/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "id", "value": "={{ $('Set Variables1').first().json.gsheetId }}"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "XHvC7jIRR8A2TlUl", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "1f75f3fc-7893-444d-8a1e-59ec3d42ea1d", "name": "Set Variables1", "type": "n8n-nodes-base.set", "position": [-880, 2060], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "2140b769-2e90-4828-8022-1b0a9baf6e44", "name": "title", "type": "string", "value": "Product Satisfaction Survey: DJI Mini 2"}, {"id": "fb7110cb-82d3-4c95-81ba-ac97a26fe05e", "name": "gsheetId", "type": "string", "value": "1KMNqWWO36osZx7jF-i1UL53z2GZnCn9fiBjtlGpvWB4"}, {"id": "6d9c4b02-8326-49f4-ac22-24bdec7fdd67", "name": "cache<PERSON>ey", "type": "string", "value": "=survey_user_{{ $json.sessionId }}"}]}}, "typeVersion": 3.4}, {"id": "a0c06eb9-46d6-4888-85b0-e9a31a31337b", "name": "Message Type1", "type": "n8n-nodes-base.switch", "position": [-320, 2060], "parameters": {"rules": {"values": [{"outputKey": "is_bot_command", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "f5fa8b7c-fdda-4c6d-b610-e243d6299598", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ ['/start','/next', '/reset'].includes($('Telegram Trigger').first().json.message.text) }}", "rightValue": "="}]}, "renameOutput": true}]}, "options": {"fallbackOutput": "extra", "renameFallbackOutput": "is_normal_message"}}, "typeVersion": 3.2}, {"id": "315585fe-d2e4-4cb1-9d0e-a3e8bc053da3", "name": "Get Command1", "type": "n8n-nodes-base.set", "position": [-120, 1760], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "73335bd3-ce50-46c5-8bcf-f7b8cd992208", "name": "command", "type": "string", "value": "={{ $('Telegram Trigger').first().json.message.text.slice($('Telegram Trigger').first().json.message.entities[0].offset, $('Telegram Trigger').first().json.message.entities[0].length ) }}"}]}}, "typeVersion": 3.4}, {"id": "7036d250-0271-4c93-9f60-01b9ccaf8be3", "name": "Bot Actions1", "type": "n8n-nodes-base.switch", "position": [60, 1760], "parameters": {"rules": {"values": [{"outputKey": "new_session", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "6f427c86-090d-429a-bc99-d2dd7753c153", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $('Get Command1').first().json.command === '/start' }}", "rightValue": "/start"}]}, "renameOutput": true}, {"outputKey": "next_question", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "bca60df4-278d-4598-afc9-6e8ee61b7ac5", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $('Get Command1').first().json.command === '/next' }}", "rightValue": ""}]}, "renameOutput": true}]}, "options": {"fallbackOutput": "extra"}}, "typeVersion": 3.2}, {"id": "fa755815-29c2-419e-8e36-51102ee88681", "name": "Memory3", "type": "@n8n/n8n-nodes-langchain.memoryRedisChat", "position": [2220, 2060], "parameters": {"sessionKey": "={{ $('Set Variables1').first().json.cacheKey }}_history", "sessionIdType": "customKey", "contextWindowLength": 10}, "credentials": {"redis": {"id": "zU4DA70qSDrZM1El", "name": "Redis account (localhost)"}}, "typeVersion": 1.4}, {"id": "a231e026-e5bf-4864-a57b-29d01e32e1a8", "name": "Get Survey State1", "type": "n8n-nodes-base.set", "position": [1620, 2060], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "6836a5aa-2b51-48cc-8d8f-d64a85d8a52f", "name": "title", "type": "string", "value": "={{ Object.values($json)[1] }}"}, {"id": "7b2fdf1b-4f55-4b55-be54-a7b97dd094cb", "name": "num_questions", "type": "number", "value": "={{ Object.keys($json).length - 2 }}"}, {"id": "dc6d5c41-0f35-49c9-b9c9-b6b4f2ceb66c", "name": "next_question_idx", "type": "number", "value": "={{ (Number($('Get State3').first().json.data?.current_question_idx) || 0) + 1 }}"}, {"id": "8b17703f-fbff-40d2-a7bb-d450f2edfea3", "name": "is_survey_complete", "type": "boolean", "value": "={{ ((Number($('Get State3').first().json.data?.current_question_idx) || 0) + 1) >= (Object.keys($json).length - 1) }}"}]}}, "executeOnce": true, "typeVersion": 3.4}, {"id": "a3bb744d-c59f-45a9-8b9d-3c291fa8d860", "name": "Reset Agent Memory1", "type": "@n8n/n8n-nodes-langchain.memoryManager", "position": [2240, 1900], "parameters": {"mode": "insert", "messages": {"messageValues": [{"message": "=You are a curious and inquisitive researcher conducting a survey with a user.\n* In this conversation, you are only interested in one of the questions from the survey which you will ask.\n* When asking an open-ended question, converse with the user to dig deeper into their answer and reveal insights into the user's experience. Do not ask other questions which do not try to expand the user's answer to the initial question.\n* If this is closed-ended question, it is okay to receive a static response and move on to the next question.\n* Only the initial question is numbered, following questions or messages should not be numbered or bulletpointed.\n* If the user goes off-topic, doesn't want to answer the question or wants to do something else which is not related to the survey, then ignore what they say/ask and politely repeat the question."}, {"type": "ai", "message": "={{ $('Get Survey State1').first().json.next_question_idx }}. {{ Object.values($('Get Columns1').first().json)[$('Get Survey State1').first().json.next_question_idx+1] }}"}]}, "insertMode": "override"}, "typeVersion": 1.1}, {"id": "b9d693b7-c139-45d8-b0d8-fd8134c7ba84", "name": "Memory4", "type": "@n8n/n8n-nodes-langchain.memoryRedisChat", "position": [1120, 2700], "parameters": {"sessionKey": "={{ $('Set Variables1').first().json.cacheKey }}_history", "sessionIdType": "customKey", "contextWindowLength": 100}, "credentials": {"redis": {"id": "zU4DA70qSDrZM1El", "name": "Redis account (localhost)"}}, "typeVersion": 1.4}, {"id": "c8fd56f5-c4c0-40cb-b727-07a94b633657", "name": "Start Session1", "type": "n8n-nodes-base.redis", "position": [440, 1620], "parameters": {"key": "={{ $('Set Variables1').first().json.cacheKey }}", "value": "={{\n{\n  \"has_session\": true,\n  \"session_createdAt\": $now,\n  \"current_question_idx\": 0\n}\n}}", "keyType": "hash", "operation": "set"}, "credentials": {"redis": {"id": "zU4DA70qSDrZM1El", "name": "Redis account (localhost)"}}, "typeVersion": 1}, {"id": "02eaa2cf-512c-4526-948e-b68989b3a8f4", "name": "Get State3", "type": "n8n-nodes-base.redis", "position": [1260, 2060], "parameters": {"key": "={{ $('Set Variables1').first().json.cacheKey }}", "options": {}, "operation": "get", "propertyName": "data"}, "credentials": {"redis": {"id": "zU4DA70qSDrZM1El", "name": "Redis account (localhost)"}}, "typeVersion": 1}, {"id": "85631c2a-9317-4b14-92d1-895ac022eb70", "name": "Increment Index1", "type": "n8n-nodes-base.redis", "position": [1800, 2060], "parameters": {"key": "={{ $('Set Variables1').first().json.cacheKey }}", "value": "={{\n{\n  \"current_question_idx\": $json.next_question_idx < $json.num_questions\n    ? $json.next_question_idx\n    : $json.num_questions,\n  \"session_updated\": $now,\n}\n}}", "keyType": "hash", "operation": "set"}, "credentials": {"redis": {"id": "zU4DA70qSDrZM1El", "name": "Redis account (localhost)"}}, "typeVersion": 1}, {"id": "4464f6fe-d579-4e03-a8ca-918515d38b30", "name": "Interview Agent1", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1040, 2540], "parameters": {"text": "={{ $('<PERSON><PERSON><PERSON> Trigger').first().json.message.text }}", "options": {}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "11ec3d5a-5f89-4a84-96ef-291526ae7365", "name": "Get Record1", "type": "n8n-nodes-base.googleSheets", "position": [-120, 2260], "parameters": {"options": {"returnFirstMatch": true}, "filtersUI": {"values": [{"lookupValue": "={{ $('<PERSON><PERSON><PERSON> Trigger').first().json.message.from.id }}", "lookupColumn": "ID"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1KMNqWWO36osZx7jF-i1UL53z2GZnCn9fiBjtlGpvWB4/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "id", "value": "={{ $('Set Variables1').first().json.gsheetId }}"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "XHvC7jIRR8A2TlUl", "name": "Google Sheets account"}}, "typeVersion": 4.5, "alwaysOutputData": true}, {"id": "665d58f8-ba2f-4d87-ae56-260333c5ecdf", "name": "Append Responses1", "type": "n8n-nodes-base.set", "position": [440, 2440], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "8069d106-0918-4bc3-9f49-************", "name": "payload", "type": "string", "value": "={{\n[\n  Object.values($('Get Record1').first().json)[Number($('Get State2').first().json.data.current_question_idx || 0) + 1],\n  'User: ' + $('Telegram Trigger').first().json.message.text,\n].join('\\n\\n').trim()\n}}"}, {"id": "d25c278c-d3a9-4a5a-9f49-01ee3ca8285a", "name": "cell", "type": "string", "value": "={{\n'abcdefghijklmnopqrstuvxyz'.toUpperCase().split('')[$('Message Type1').first().json.data.current_question_idx]\n}}{{ $('Get Record1').first().json.row_number }}"}]}}, "typeVersion": 3.4}, {"id": "4771e9e4-8b79-4530-8c4a-6e7ab0623d98", "name": "Update Answer2", "type": "n8n-nodes-base.httpRequest", "position": [1360, 2540], "parameters": {"url": "=https://sheets.googleapis.com/v4/spreadsheets/{{ $('Set Variables1').first().json.gsheetId }}/values/Sheet1!{{ $('Append Responses1').first().json.cell }}", "method": "PUT", "options": {}, "jsonBody": "={{\n{\n  \"range\": `Sheet1!${$('Append Responses1').first().json.cell}`,\n  \"majorDimension\": \"ROWS\",\n  \"values\": [[\n    $('Append Responses1').first().json.payload + '\\n\\nAgent: ' + $('Interview Agent1').first().json.output]]\n}\n}}", "sendBody": true, "sendQuery": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "valueInputOption", "value": "RAW"}]}, "nodeCredentialType": "googleSheetsOAuth2Api"}, "credentials": {"googleSheetsOAuth2Api": {"id": "XHvC7jIRR8A2TlUl", "name": "Google Sheets account"}}, "typeVersion": 4.2}, {"id": "b7094d87-97ce-4f1a-8d30-4d6d57d24477", "name": "Get Last Bot Message1", "type": "n8n-nodes-base.redis", "position": [260, 2440], "parameters": {"key": "={{ $('Set Variables1').item.json.cacheKey }}_history", "options": {}, "operation": "get", "propertyName": "data"}, "credentials": {"redis": {"id": "zU4DA70qSDrZM1El", "name": "Redis account (localhost)"}}, "typeVersion": 1}, {"id": "6dc77f41-6c3b-4358-82bc-2ea299ef69b2", "name": "Update Answer3", "type": "n8n-nodes-base.httpRequest", "position": [1020, 2180], "parameters": {"url": "=https://sheets.googleapis.com/v4/spreadsheets/{{ $('Set Variables1').first().json.gsheetId }}/values/Sheet1!{{ $('Append Responses1').first().json.cell }}", "method": "PUT", "options": {}, "jsonBody": "={{\n{\n  \"range\": `Sheet1!${$('Append Responses1').first().json.cell}`,\n  \"majorDimension\": \"ROWS\",\n  \"values\": [[$('Append Responses1').first().json.payload]]\n}\n}}", "sendBody": true, "sendQuery": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "valueInputOption", "value": "RAW"}]}, "nodeCredentialType": "googleSheetsOAuth2Api"}, "credentials": {"googleSheetsOAuth2Api": {"id": "XHvC7jIRR8A2TlUl", "name": "Google Sheets account"}}, "typeVersion": 4.2}, {"id": "dd67ca61-b36c-4f9f-84fe-0b25d411e459", "name": "Model2", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1000, 2700], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "********-eea0-434e-af3a-4d49db4a7f15", "name": "Model3", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [600, 2600], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "bad16467-bb3c-4d50-9dd8-014bac25fd61", "name": "Should Follow Up?1", "type": "@n8n/n8n-nodes-langchain.textClassifier", "position": [620, 2440], "parameters": {"options": {}, "inputText": "=AI: {{ $('Get Last Bot Message1').item.json.data?.first().parseJson().data.content ?? ''}}\nUSER: {{ $('Telegram Trigger').first().json.message.text }}", "categories": {"categories": [{"category": "should_not_ask_followup_questions", "description": "=Either\n* The user was asked a close-ended question and gave an adequate static response\n* There are no further insights to be learned from the user's answer"}, {"category": "should_ask_followup_questions", "description": "=Either one of\n* There are possibly more insights to be learned from the user's answer if we enquire. hint: did the user explaied or justified their answer?\n* The user didn't answer the question"}]}}, "typeVersion": 1}, {"id": "********-9ff1-4815-bfaf-c4137fa00fda", "name": "Execution Data2", "type": "n8n-nodes-base.executionData", "position": [-700, 2060], "parameters": {"dataToSave": {"values": [{"key": "jobType", "value": "=state_message"}, {"key": "gsheetId", "value": "={{ $json.gsheetId }}"}, {"key": "title", "value": "={{ $json.title }}"}, {"key": "fromId", "value": "={{ $('Telegram Trigger').item.json.message.from.id }}"}]}}, "typeVersion": 1}, {"id": "ed16f03b-621e-4766-b8fd-f1f662b2dd0b", "name": "Create Record2", "type": "n8n-nodes-base.googleSheets", "position": [260, 2060], "parameters": {"columns": {"value": {"ID": "={{ $('<PERSON><PERSON><PERSON> Trigger').first().json.message.from.id }}"}, "schema": [{"id": "ID", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "What's your name?", "type": "string", "display": true, "removed": true, "required": false, "displayName": "What's your name?", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "What were the main factors that influenced your decision to purchase the DJI Mini 2?", "type": "string", "display": true, "removed": true, "required": false, "displayName": "What were the main factors that influenced your decision to purchase the DJI Mini 2?", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Did you compare the DJI Mini 2 with other drone models before purchasing? If yes, which other drone models were you considering? ", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Did you compare the DJI Mini 2 with other drone models before purchasing? If yes, which other drone models were you considering? ", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "What ultimately convinced you to choose the DJI Mini 2 over other options?", "type": "string", "display": true, "removed": true, "required": false, "displayName": "What ultimately convinced you to choose the DJI Mini 2 over other options?", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "How satisfied are you with the DJI Mini 2?", "type": "string", "display": true, "removed": true, "required": false, "displayName": "How satisfied are you with the DJI Mini 2?", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "What features do you like most about the DJI Mini 2?", "type": "string", "display": true, "removed": true, "required": false, "displayName": "What features do you like most about the DJI Mini 2?", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Are there any features you feel are missing or could be improved?", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Are there any features you feel are missing or could be improved?", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Have you experienced any technical issues with the DJI Mini 2?", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Have you experienced any technical issues with the DJI Mini 2?", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "How likely are you to recommend the DJI Mini 2 to others?", "type": "string", "display": true, "removed": true, "required": false, "displayName": "How likely are you to recommend the DJI Mini 2 to others?", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Where did you purchase your DJI Mini 2?", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Where did you purchase your DJI Mini 2?", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "How was your overall purchase experience?", "type": "string", "display": true, "removed": true, "required": false, "displayName": "How was your overall purchase experience?", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Did you find all the information you needed before purchasing?", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Did you find all the information you needed before purchasing?", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "If you interacted with DJI customer support, how would you rate the experience?", "type": "string", "display": true, "removed": true, "required": false, "displayName": "If you interacted with DJI customer support, how would you rate the experience?", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "How can DJI improve the Mini 2 or future drone models?", "type": "string", "display": true, "removed": true, "required": false, "displayName": "How can DJI improve the Mini 2 or future drone models?", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "How can DJI improve the overall shopping and customer service experience?", "type": "string", "display": true, "removed": true, "required": false, "displayName": "How can DJI improve the overall shopping and customer service experience?", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Any additional comments or feedback?", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Any additional comments or feedback?", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["ID"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1KMNqWWO36osZx7jF-i1UL53z2GZnCn9fiBjtlGpvWB4/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "id", "value": "={{ $('Set Variables1').first().json.gsheetId }}"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "XHvC7jIRR8A2TlUl", "name": "Google Sheets account"}}, "typeVersion": 4.5, "alwaysOutputData": true}, {"id": "f2119ef5-aef3-4b0f-80f5-1715a4afe3e0", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [-1120, 1820], "parameters": {"color": 7, "width": 760, "height": 500, "content": "## 1. Initiate Survey by Inviting User to Chat\n[Learn more about the chat trigger](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-langchain.chattrigger/)\n\nTo present the survey to the user, we'll use n8n's builtin hosted chat. Survey questions are predefined and pulled from Google Sheet. Finally, for state and session management such as tracking which question we're asking, we'll use Redis as it's a fast write and ready database suitable for our chat."}, "typeVersion": 1}, {"id": "9e7ca15c-0e31-4d78-b8af-31fd6260cd61", "name": "Send Start", "type": "n8n-nodes-base.telegram", "position": [600, 1620], "webhookId": "c5d13268-df58-450b-84c4-a61cd2e027ed", "parameters": {"text": "={{ $('Set Variables1').first().json.title }}.\nWelcome! Thank you for taking the time to participate in our survey.\n\nYou'll be asked a couple of pre-defined questions. For each question, we may ask follow-up questions to better understand your situation. If you want to skip to the next question at any time, simply reply with the \"/next\". Your responses will be recorded.\n\nTo start the survey, simply reply with \"/next\".", "chatId": "={{ $('<PERSON><PERSON><PERSON> Trigger').first().json.message.chat.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "wJqs1eMdbjilZH1W", "name": "jimleuk_ai_survey_demo_bot"}}, "typeVersion": 1.2}, {"id": "e8be2d00-39c8-4dd1-a597-813fc4bffe10", "name": "Send Start1", "type": "n8n-nodes-base.telegram", "position": [260, 1900], "webhookId": "c5d13268-df58-450b-84c4-a61cd2e027ed", "parameters": {"text": "=Sorry, that command is unrecognised. The available options are /start or /next.", "chatId": "={{ $('<PERSON><PERSON><PERSON> Trigger').first().json.message.chat.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "wJqs1eMdbjilZH1W", "name": "jimleuk_ai_survey_demo_bot"}}, "typeVersion": 1.2}, {"id": "153bbe8b-52bc-403c-ae5f-346060a5aac3", "name": "Completed Survey", "type": "n8n-nodes-base.telegram", "position": [2220, 2280], "webhookId": "409dbc48-4916-415b-8c1b-caf3b359e1e4", "parameters": {"text": "=Done! Thank you for completing our survey.\nTo start the survey again, use \"/start\".", "chatId": "={{ $('<PERSON><PERSON><PERSON> Trigger').first().json.message.chat.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "wJqs1eMdbjilZH1W", "name": "jimleuk_ai_survey_demo_bot"}}, "typeVersion": 1.2}, {"id": "c2e39206-f586-4c90-aeb0-d6a141e81d1d", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-180, 1540], "parameters": {"color": 7, "width": 420, "height": 380, "content": "## 2. <PERSON><PERSON>t Commands\n[Learn more about the switch node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.switch/)\n\nOur chatbot can be a mix of traditional chat mechanics and AI agent. Using bot commands, gives the user some control over the flow of the conversation such as skipping to the next question or starting over the survey."}, "typeVersion": 1}, {"id": "6b6241ae-17b7-4f0f-9c00-f5de4d62f692", "name": "Create Record1", "type": "n8n-nodes-base.googleSheets", "position": [280, 1620], "parameters": {"columns": {"value": {"ID": "={{ $('<PERSON><PERSON><PERSON> Trigger').first().json.message.from.id }}"}, "schema": [{"id": "ID", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "What's your name?", "type": "string", "display": true, "removed": true, "required": false, "displayName": "What's your name?", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "What were the main factors that influenced your decision to purchase the DJI Mini 2?", "type": "string", "display": true, "removed": true, "required": false, "displayName": "What were the main factors that influenced your decision to purchase the DJI Mini 2?", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Did you compare the DJI Mini 2 with other drone models before purchasing? If yes, which other drone models were you considering? ", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Did you compare the DJI Mini 2 with other drone models before purchasing? If yes, which other drone models were you considering? ", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "What ultimately convinced you to choose the DJI Mini 2 over other options?", "type": "string", "display": true, "removed": true, "required": false, "displayName": "What ultimately convinced you to choose the DJI Mini 2 over other options?", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "How satisfied are you with the DJI Mini 2?", "type": "string", "display": true, "removed": true, "required": false, "displayName": "How satisfied are you with the DJI Mini 2?", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "What features do you like most about the DJI Mini 2?", "type": "string", "display": true, "removed": true, "required": false, "displayName": "What features do you like most about the DJI Mini 2?", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Are there any features you feel are missing or could be improved?", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Are there any features you feel are missing or could be improved?", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Have you experienced any technical issues with the DJI Mini 2?", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Have you experienced any technical issues with the DJI Mini 2?", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "How likely are you to recommend the DJI Mini 2 to others?", "type": "string", "display": true, "removed": true, "required": false, "displayName": "How likely are you to recommend the DJI Mini 2 to others?", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Where did you purchase your DJI Mini 2?", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Where did you purchase your DJI Mini 2?", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "How was your overall purchase experience?", "type": "string", "display": true, "removed": true, "required": false, "displayName": "How was your overall purchase experience?", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Did you find all the information you needed before purchasing?", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Did you find all the information you needed before purchasing?", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "If you interacted with DJI customer support, how would you rate the experience?", "type": "string", "display": true, "removed": true, "required": false, "displayName": "If you interacted with DJI customer support, how would you rate the experience?", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "How can DJI improve the Mini 2 or future drone models?", "type": "string", "display": true, "removed": true, "required": false, "displayName": "How can DJI improve the Mini 2 or future drone models?", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "How can DJI improve the overall shopping and customer service experience?", "type": "string", "display": true, "removed": true, "required": false, "displayName": "How can DJI improve the overall shopping and customer service experience?", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Any additional comments or feedback?", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Any additional comments or feedback?", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["ID"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1KMNqWWO36osZx7jF-i1UL53z2GZnCn9fiBjtlGpvWB4/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "id", "value": "={{ $('Set Variables1').first().json.gsheetId }}"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "XHvC7jIRR8A2TlUl", "name": "Google Sheets account"}}, "typeVersion": 4.5, "alwaysOutputData": true}, {"id": "f8f6b582-75d7-40a1-95a5-c1b80fa41c64", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [520, 2180], "parameters": {"color": 7, "width": 440, "height": 420, "content": "## 3. Support to Follow-Up Questions\n[Learn more about the Text Classifier node](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.text-classifier/)\n\nDepending on whether the current question is open-ended or closed-ended, we may not require the Agent to spend too much time on the answer. Here we're using the text classifier node to make that judgement call - when closed-ended, we can skip to the next question and when open-ended, we can attempt to dig deeper into the answer."}, "typeVersion": 1}, {"id": "5aa8956b-787d-4b4a-b4dc-a2415434a331", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [980, 2340], "parameters": {"color": 7, "width": 700, "height": 500, "content": "## 4. Deeper Insights with a Conversational AI Agent\n[Learn more about the AI Agent](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.agent/)\n\nThe way we utilise the AI in this workflow is by allowing the agent to converse back-and-forth with the user on any given question of the survey. This means answers can be more than just single sentence and can expand to minute-long conversations, producting much deeper insights for the survey."}, "typeVersion": 1}, {"id": "aa304745-6d30-4fdb-8279-eb505ee06a61", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1200, 1820], "parameters": {"color": 7, "width": 740, "height": 380, "content": "## 5. Managing Conversational Flow with External State\n[Learn more about the Redis node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.redis/)\n\nWe use Redis as a quick and easy way to store and track the question index (or \"state\") as the user progresses through the survey. To calculate which question should be next or if the survey is complete, we query the spreadsheet row for the user's session and calculate our next state from there. "}, "typeVersion": 1}, {"id": "4ce7ce73-d771-4794-b1fb-87c15ccdbdaf", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [2160, 1680], "parameters": {"color": 7, "width": 620, "height": 520, "content": "## 6. Resetting Chat Memory for Every Question\n[Learn more about the Chat Memory Manager](https://docs.n8n.io/integrations/builtin/cluster-nodes/sub-nodes/n8n-nodes-langchain.memorymanager/)\n\nI discovered that to ensure my agent focused on a specific question when interacting with the user, I needed to clear all previous message context to prevent it from using past references. This is a really unconventional approach to controlling Agent behaviour but from my observation, greatly reduces hallucinations during the conversation."}, "typeVersion": 1}, {"id": "2b26317c-6ba6-4ff0-92a6-525cce9e725d", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-1740, 1300], "parameters": {"width": 580, "height": 1020, "content": "## Try It Out\n### This n8n template demonstrates how you can build a structured chat journey augmented with AI for your business.\n\nIn this scenario, a chatbot conducts a product satisfaction survey with a user using a predefined set of questions. For each question, the agent can dive deeper into the user's answers by asking follow-up questions.\n\nThis chatbot template isn't fully \"agentic\" which makes it a bit more complicated and scripted but ensures a more deterministic user journey; suitable when you need more guide-rails on the experience.\n\nSee example survey here: https://docs.google.com/spreadsheets/d/e/2PACX-1vQWcREg75CzbZd8loVI12s-DzSTj3NE_02cOCpAh7umj0urazzYCfzPpYvvh7jqICWZteDTALzBO46i/pubhtml?gid=0&single=true\n\n### How it works\n* A chat session is started with the user who needs to enter the bot command \"/next\" to start the survey.\n* Once started, the template pulls in questions from a google sheet to ask the user. Questions are asked in sequence from left to right.\n* When the user answers the question, a text classifier node is used to determine \nif a follow-up question could be asked.\n* If not, the survey proceeds to the next question. Otherwise, an AI Agent will generate a follow-up question based on the user's response.\n* All answers and AI conversations are recorded in the Google Sheet.\n* When all questions are answered, the template will stop the survey and give the user a chance to restart.\n\n### How to use\n* You'll need to setup a Telegram bot ([see docs](https://docs.n8n.io/integrations/builtin/credentials/telegram/))\n* Create a google sheet with an ID column. Populate the rest of the columns with your survey questions ([see sample](https://docs.google.com/spreadsheets/d/e/2PACX-1vQWcREg75CzbZd8loVI12s-DzSTj3NE_02cOCpAh7umj0urazzYCfzPpYvvh7jqICWZteDTALzBO46i/pubhtml?gid=0&single=true))\n* Ensure you have a Redis instance to capture state. Either self-host or sign-up to [Upstash](https://upstash.com?ref=jimleuk) for a free account.\n* Update the \"Set Variable\" node with your google sheet ID and survey title.\n* Share the bot with users to allow others to participate in your survey.\n\n### Can I use this for WhatsApp?\nYes you can! Swapping out all telegram nodes for WhatsApp nodes should produce the same result.\n\n\n### Need Help?\nJoin the [Discord](https://discord.com/invite/XPKeKXeB7d) or ask in the [Forum](https://community.n8n.io/)!"}, "typeVersion": 1}], "pinData": {}, "connections": {"Model2": {"ai_languageModel": [[{"node": "Interview Agent1", "type": "ai_languageModel", "index": 0}]]}, "Model3": {"ai_languageModel": [[{"node": "Should Follow Up?1", "type": "ai_languageModel", "index": 0}]]}, "Memory3": {"ai_memory": [[{"node": "Reset Agent Memory1", "type": "ai_memory", "index": 0}]]}, "Memory4": {"ai_memory": [[{"node": "Interview Agent1", "type": "ai_memory", "index": 0}]]}, "Get State2": {"main": [[{"node": "Message Type1", "type": "main", "index": 0}]]}, "Get State3": {"main": [[{"node": "Get Columns1", "type": "main", "index": 0}]]}, "Send Start": {"main": [[]]}, "Get Record1": {"main": [[{"node": "Has No Record?", "type": "main", "index": 0}]]}, "Send Start1": {"main": [[]]}, "Bot Actions1": {"main": [[{"node": "Create Record1", "type": "main", "index": 0}], [{"node": "Get State3", "type": "main", "index": 0}], [{"node": "Send Start1", "type": "main", "index": 0}]]}, "Get Columns1": {"main": [[{"node": "Get Survey State1", "type": "main", "index": 0}]]}, "Get Command1": {"main": [[{"node": "Bot Actions1", "type": "main", "index": 0}]]}, "Message Type1": {"main": [[{"node": "Get Command1", "type": "main", "index": 0}], [{"node": "Get Record1", "type": "main", "index": 0}]]}, "Send Response": {"main": [[]]}, "Create Record1": {"main": [[{"node": "Start Session1", "type": "main", "index": 0}]]}, "Create Record2": {"main": [[{"node": "Get State3", "type": "main", "index": 0}]]}, "Has No Record?": {"main": [[{"node": "Create Record2", "type": "main", "index": 0}], [{"node": "Get Last Bot Message1", "type": "main", "index": 0}]]}, "Set Variables1": {"main": [[{"node": "Execution Data2", "type": "main", "index": 0}]]}, "Start Session1": {"main": [[{"node": "Send Start", "type": "main", "index": 0}]]}, "Update Answer2": {"main": [[{"node": "Send Response", "type": "main", "index": 0}]]}, "Update Answer3": {"main": [[{"node": "Get State3", "type": "main", "index": 0}]]}, "Execution Data2": {"main": [[{"node": "Get State2", "type": "main", "index": 0}]]}, "Increment Index1": {"main": [[{"node": "Is Survey Continue?", "type": "main", "index": 0}]]}, "Interview Agent1": {"main": [[{"node": "Update Answer2", "type": "main", "index": 0}]]}, "Telegram Trigger": {"main": [[{"node": "Set Variables1", "type": "main", "index": 0}]]}, "Append Responses1": {"main": [[{"node": "Should Follow Up?1", "type": "main", "index": 0}]]}, "Get Survey State1": {"main": [[{"node": "Increment Index1", "type": "main", "index": 0}]]}, "Send Next Question": {"main": [[]]}, "Should Follow Up?1": {"main": [[{"node": "Update Answer3", "type": "main", "index": 0}], [{"node": "Interview Agent1", "type": "main", "index": 0}]]}, "Is Survey Continue?": {"main": [[{"node": "Reset Agent Memory1", "type": "main", "index": 0}], [{"node": "Completed Survey", "type": "main", "index": 0}]]}, "Reset Agent Memory1": {"main": [[{"node": "Send Next Question", "type": "main", "index": 0}]]}, "Get Last Bot Message1": {"main": [[{"node": "Append Responses1", "type": "main", "index": 0}]]}}}