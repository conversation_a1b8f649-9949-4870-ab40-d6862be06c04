{"nodes": [{"id": "db39e47c-df1f-4fec-86e5-fc391cce68da", "name": "Add Log in History", "type": "n8n-nodes-base.googleSheets", "position": [1360, 540], "parameters": {"columns": {"value": {}, "schema": [{"id": "price", "type": "string", "display": true, "removed": false, "required": false, "displayName": "price", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "link", "type": "string", "display": true, "removed": false, "required": false, "displayName": "link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "update", "type": "string", "display": true, "removed": false, "required": false, "displayName": "update", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": ["update"]}, "options": {}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "list", "value": 440562612, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1sM66Rk10ZOhQKbawVB-xZ2WYhBeSr6wnJqvX6Aspbkg/edit#gid=440562612", "cachedResultName": "Pricing History"}, "documentId": {"__rl": true, "mode": "list", "value": "1sM66Rk10ZOhQKbawVB-xZ2WYhBeSr6wnJqvX6Aspbkg", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1sM66Rk10ZOhQKbawVB-xZ2WYhBeSr6wnJqvX6Aspbkg/edit?usp=drivesdk", "cachedResultName": "Zalando Links"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "1nTQmlgxR0AJJDGA", "name": "n8ninja - Sheet"}}, "typeVersion": 4.3}, {"id": "56733bdb-8dc7-4c78-9df2-5ee147afe061", "name": "Update Products Infos", "type": "n8n-nodes-base.googleSheets", "position": [1360, 360], "parameters": {"columns": {"value": {}, "schema": [{"id": "link", "type": "string", "display": true, "removed": false, "required": false, "displayName": "link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "price", "type": "string", "display": true, "required": false, "displayName": "price", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "name", "type": "string", "display": true, "required": false, "displayName": "name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": ["link"]}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1sM66Rk10ZOhQKbawVB-xZ2WYhBeSr6wnJqvX6Aspbkg/edit#gid=0", "cachedResultName": "Links"}, "documentId": {"__rl": true, "mode": "list", "value": "1sM66Rk10ZOhQKbawVB-xZ2WYhBeSr6wnJqvX6Aspbkg", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1sM66Rk10ZOhQKbawVB-xZ2WYhBeSr6wnJqvX6Aspbkg/edit?usp=drivesdk", "cachedResultName": "Zalando Links"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "1nTQmlgxR0AJJDGA", "name": "n8ninja - Sheet"}}, "typeVersion": 4.3}, {"id": "f87196d7-26ec-41d6-af04-c4b57f3ea899", "name": "If price below price alert", "type": "n8n-nodes-base.if", "position": [1360, 740], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "0466f2d9-de7a-4017-933a-acda8fb84191", "operator": {"type": "number", "operation": "lte"}, "leftValue": "={{ parseFloat($json.price) }}", "rightValue": "={{ parseFloat($('List Products').item.json.price_alert) }}"}]}}, "typeVersion": 2}, {"id": "2663fb7a-08ab-45b7-b6d3-4eea495185c4", "name": "List Products", "type": "n8n-nodes-base.googleSheets", "position": [600, 540], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1sM66Rk10ZOhQKbawVB-xZ2WYhBeSr6wnJqvX6Aspbkg/edit#gid=0", "cachedResultName": "Links"}, "documentId": {"__rl": true, "mode": "list", "value": "1sM66Rk10ZOhQKbawVB-xZ2WYhBeSr6wnJqvX6Aspbkg", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1sM66Rk10ZOhQKbawVB-xZ2WYhBeSr6wnJqvX6Aspbkg/edit?usp=drivesdk", "cachedResultName": "Zalando Links"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "1nTQmlgxR0AJJDGA", "name": "n8ninja - Sheet"}}, "typeVersion": 4.3}, {"id": "5ae11ccd-ed5e-4e08-8f42-b3a1c45323df", "name": "Format Product", "type": "n8n-nodes-base.set", "position": [1060, 540], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "77ef948b-e4a4-4695-a5e6-bbb21b99f177", "name": "price", "type": "string", "value": "={{ parseFloat($json.data.split('\"twitter:data1\" content=\"')[1].split('\"')[0].split('&nbsp;')[1]) }}"}, {"id": "cf241232-d85d-40d7-bf5c-6d49336d6ce1", "name": "name", "type": "string", "value": "={{ $json.data.split('<title>')[1].split('</title>')[0].split('-')[0] }} {{ $json.data.split('<title>')[1].split('</title>')[0].split('-')[1] }} {{ $json.data.split('<title>')[1].split('</title>')[0].split('-')[2] }}"}, {"id": "ca07eca0-8bad-4997-a124-e8636eb07bd5", "name": "link", "type": "string", "value": "={{ $('List Products').item.json.link }}"}, {"id": "c2d92a0f-56cc-4b74-bbd3-d8a1bb0d6cd7", "name": "update", "type": "string", "value": "={{ $now.format('D') }}"}, {"id": "af33b7f1-8367-4f05-bc7e-03d119c3ac76", "name": "", "type": "string", "value": ""}]}}, "typeVersion": 3.3}, {"id": "ae11877a-e83e-44c9-b6f2-b5cd0c8a3c1e", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [380, 540], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.1}, {"id": "8361e609-0185-4939-92c4-07ec7826bdab", "name": "Scrap Product", "type": "n8n-nodes-base.httpRequest", "position": [820, 540], "parameters": {"url": "={{ $json.link }}", "options": {}}, "typeVersion": 4.1}, {"id": "6a98aad8-9475-4243-948a-b904cde4687a", "name": "Notify Price Reduction", "type": "n8n-nodes-base.gmail", "position": [1600, 720], "parameters": {"sendTo": "<EMAIL>", "message": "=<h3>Price reduction alert for {{ $('Format Product').item.json[\"name\"] }}</h3>\n\n<p>New price {{ $('Format Product').item.json[\"price\"] }} CHF is bellow {{ $('List Products').item.json[\"price_alert\"] }} CHF</p>\n\nView product: {{ $('List Products').item.json[\"link\"] }}\n\n\n", "options": {}, "subject": "=⚠️ Price Reduction: {{ $('Format Product').item.json.name }}\n"}, "credentials": {"gmailOAuth2": {"id": "DMcPDN0IHPwGmI7f", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "427f51a9-7ebf-42df-bb97-e0a17a37d2cb", "name": "Monitor Zalando Product", "type": "n8n-nodes-base.formTrigger", "position": [380, 280], "webhookId": "6da9a655-b46b-454d-bb96-32e203627a20", "parameters": {"path": "6da9a655-b46b-454d-bb96-32e203627a20", "options": {}, "formTitle": "Add Product", "formFields": {"values": [{"fieldLabel": "link", "requiredField": true}, {"fieldType": "number", "fieldLabel": "price_alert", "requiredField": true}]}, "formDescription": "Past in a Zalando URL and the price bellow you would like to be notified"}, "typeVersion": 2}, {"id": "39711d6f-d699-415b-9a1b-3971839e7e8a", "name": "Add Product", "type": "n8n-nodes-base.googleSheets", "position": [580, 280], "parameters": {"columns": {"value": {"link": "={{ $json.link }}", "price_alert": "={{ $json.price_alert }}"}, "schema": [{"id": "link", "type": "string", "display": true, "removed": false, "required": false, "displayName": "link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "price_alert", "type": "string", "display": true, "required": false, "displayName": "price_alert", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "price", "type": "string", "display": true, "removed": true, "required": false, "displayName": "price", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "name", "type": "string", "display": true, "removed": true, "required": false, "displayName": "name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "update", "type": "string", "display": true, "removed": true, "required": false, "displayName": "update", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["link"]}, "options": {}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1sM66Rk10ZOhQKbawVB-xZ2WYhBeSr6wnJqvX6Aspbkg/edit#gid=0", "cachedResultName": "Links"}, "documentId": {"__rl": true, "mode": "list", "value": "1sM66Rk10ZOhQKbawVB-xZ2WYhBeSr6wnJqvX6Aspbkg", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1sM66Rk10ZOhQKbawVB-xZ2WYhBeSr6wnJqvX6Aspbkg/edit?usp=drivesdk", "cachedResultName": "Zalando Links"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "1nTQmlgxR0AJJDGA", "name": "n8ninja - Sheet"}}, "typeVersion": 4.3, "alwaysOutputData": true}, {"id": "881acff3-1736-4d53-8c3b-3354ec2da07b", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [400, -191], "parameters": {"color": 6, "width": 275.01592825011585, "height": 439.37809705210145, "content": "# Setup\n### 1/ Add Your credentials\n[Google SHeet](https://docs.n8n.io/integrations/builtin/credentials/google/)\n\n### 2/ Create a Google Spreadsheet that will be your database.\nCopy this template: \nhttps://docs.google.com/spreadsheets/d/1sM66Rk10ZOhQKbawVB-xZ2WYhBeSr6wnJqvX6Aspbkg/edit?usp=sharing\n\n### 3/ Add products to monitor from this form \n# 👇"}, "typeVersion": 1}, {"id": "92ae590a-bf1d-4c17-a654-f281379fcee6", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1620, 600], "parameters": {"color": 6, "width": 275.01592825011585, "height": 93.37809705210145, "content": "### Fill with your email\n# 👇"}, "typeVersion": 1}, {"id": "8834da7f-7ca1-4cad-98b9-2335bfc81b8f", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [40, 580], "parameters": {"color": 6, "width": 275.01592825011585, "height": 136.37809705210145, "content": "### Change frequency 👉🏻\n\n(don't put less than once a day, or you will need to also add the hour in the format product node)\n"}, "typeVersion": 1}, {"id": "b1838412-6c3e-4519-8946-f6aeff0c9e8d", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [900, 340], "parameters": {"color": 7, "width": 202.64787116404852, "height": 85.79488430601403, "content": "### Crafted by the\n## [🥷 n8n.ninja](n8n.ninja)"}, "typeVersion": 1}], "pinData": {}, "connections": {"Add Product": {"main": [[{"node": "List Products", "type": "main", "index": 0}]]}, "List Products": {"main": [[{"node": "Scrap Product", "type": "main", "index": 0}]]}, "Scrap Product": {"main": [[{"node": "Format Product", "type": "main", "index": 0}]]}, "Format Product": {"main": [[{"node": "Add Log in History", "type": "main", "index": 0}, {"node": "Update Products Infos", "type": "main", "index": 0}, {"node": "If price below price alert", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "List Products", "type": "main", "index": 0}]]}, "Monitor Zalando Product": {"main": [[{"node": "Add Product", "type": "main", "index": 0}]]}, "If price below price alert": {"main": [[{"node": "Notify Price Reduction", "type": "main", "index": 0}]]}}}