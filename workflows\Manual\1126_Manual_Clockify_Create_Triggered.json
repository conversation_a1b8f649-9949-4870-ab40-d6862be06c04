{"id": "76", "name": "Create a project, tag, and time entry, and update the time entry in Clockify", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "Clockify", "type": "n8n-nodes-base.clockify", "position": [450, 300], "parameters": {"name": "n8n-docs", "workspaceId": "5f7af249d33ce12a712306dd", "additionalFields": {"note": "For n8n-docs", "color": "#0000FF", "isPublic": false}}, "credentials": {"clockifyApi": "clockify-burner"}, "typeVersion": 1}, {"name": "Clockify1", "type": "n8n-nodes-base.clockify", "position": [650, 300], "parameters": {"name": "docs", "resource": "tag", "workspaceId": "5f7af249d33ce12a712306dd"}, "credentials": {"clockifyApi": "clockify-burner"}, "typeVersion": 1}, {"name": "Clockify2", "type": "n8n-nodes-base.clockify", "position": [850, 300], "parameters": {"start": "2020-10-05T08:30:00.000Z", "resource": "timeEntry", "workspaceId": "5f7af249d33ce12a712306dd", "additionalFields": {"end": "2020-10-05T09:30:00.000Z", "tagIds": ["5f7afbfc73610f56b88ee9ef"], "description": "Added Clockify Docs"}}, "credentials": {"clockifyApi": "clockify-burner"}, "typeVersion": 1}, {"name": "Clockify3", "type": "n8n-nodes-base.clockify", "position": [1050, 300], "parameters": {"resource": "timeEntry", "operation": "update", "timeEntryId": "={{$node[\"Clockify2\"].json[\"id\"]}}", "workspaceId": "5f7af249d33ce12a712306dd", "updateFields": {"projectId": "={{$node[\"Clockify\"].json[\"id\"]}}"}}, "credentials": {"clockifyApi": "clockify-burner"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Clockify": {"main": [[{"node": "Clockify1", "type": "main", "index": 0}]]}, "Clockify1": {"main": [[{"node": "Clockify2", "type": "main", "index": 0}]]}, "Clockify2": {"main": [[{"node": "Clockify3", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Clockify", "type": "main", "index": 0}]]}}}