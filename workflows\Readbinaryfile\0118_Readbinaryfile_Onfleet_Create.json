{"id": 12, "name": "Create Onfleet tasks from Spreadsheets", "nodes": [{"name": "Onfleet", "type": "n8n-nodes-base.onfleet", "position": [900, 280], "parameters": {"operation": "create", "destination": {"destinationProperties": {"address": "={{$json[\"Address_Line1\"]}}, {{$json[\"Address_Line2\"]}}, {{$json[\"City/Town\"]}} {{$json[\"State/Province\"]}}, {{$json[\"Country\"]}}, {{$json[\"Postal_Code\"]}}", "unparsed": true, "addressNotes": "=", "addressApartment": "={{$json[\"Address_Line2\"]}}"}}, "additionalFields": {"notes": "={{$json[\"Task_Details\"]}}", "recipient": {"recipientProperties": {"recipientName": "={{$json[\"Recipient_Name\"]}}", "recipientNotes": "={{$json[\"Recipient_Notes\"]}}", "recipientPhone": "=+1{{$json[\"Recipient_Phone\"]}}"}}}}, "credentials": {"onfleetApi": {"id": "2", "name": "Onfleet API Key"}}, "typeVersion": 1}, {"name": "Read Binary File", "type": "n8n-nodes-base.readBinaryFile", "position": [500, 280], "parameters": {"filePath": "=/Users/<USER>/Downloads/Onfleet Import Google Sheet.xlsx"}, "typeVersion": 1}, {"name": "Spreadsheet File1", "type": "n8n-nodes-base.spreadsheetFile", "position": [700, 280], "parameters": {"options": {}}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Read Binary File": {"main": [[{"node": "Spreadsheet File1", "type": "main", "index": 0}]]}, "Spreadsheet File1": {"main": [[{"node": "Onfleet", "type": "main", "index": 0}]]}}}