{"id": "16", "name": "User Request Management", "nodes": [{"name": "ClickUp", "type": "n8n-nodes-base.clickUp", "position": [1180, 490], "parameters": {"list": "={{$json[\"ListID\"]}}", "name": "={{$node[\"Typeform Trigger\"].json[\"Give this request a short title.\"]}}", "team": "8583125", "space": "12732821", "folder": "25402375", "authentication": "oAuth2", "additionalFields": {"content": "={{$node[\"Typeform Trigger\"].json[\"Describe in detail what you would like to see happen for this request.\"]}}\n\nRequested by:\n{{$node[\"Typeform Trigger\"].json[\"Your full name\"]}}\n{{$node[\"Typeform Trigger\"].json[\"Your email address\"]}}", "priority": "={{$json[\"How urgent is this request?\"]}}"}}, "credentials": {"clickUpOAuth2Api": "ClickUp <PERSON>red"}, "typeVersion": 1}, {"name": "Typeform Trigger", "type": "n8n-nodes-base.typeformTrigger", "position": [530, 500], "webhookId": "80816cb6-d987-44b2-8981-f95d1af1f6a8", "parameters": {"formId": "LE36uLN1"}, "credentials": {"typeformApi": "Typeform"}, "typeVersion": 1}, {"name": "ListID 54684957", "type": "n8n-nodes-base.set", "position": [940, 560], "parameters": {"values": {"number": [{"name": "ListID", "value": 54684957}]}, "options": {}}, "typeVersion": 1}, {"name": "ListID 54685003", "type": "n8n-nodes-base.set", "position": [940, 280], "parameters": {"values": {"number": [{"name": "ListID", "value": 54685003}]}, "options": {}}, "typeVersion": 1}, {"name": "ListID 54685000", "type": "n8n-nodes-base.set", "position": [940, 420], "parameters": {"values": {"number": [{"name": "ListID", "value": 54685000}]}, "options": {}}, "typeVersion": 1}, {"name": "ListID 54684997", "type": "n8n-nodes-base.set", "position": [940, 700], "parameters": {"values": {"number": [{"name": "ListID", "value": 54684997}]}, "options": {}}, "typeVersion": 1}, {"name": "Pick List", "type": "n8n-nodes-base.switch", "position": [730, 500], "parameters": {"rules": {"rules": [{"value2": "Document Request"}, {"output": 1, "value2": "Presentation Request"}, {"output": 2, "value2": "Update Request"}, {"output": 3, "value2": "Workflow Request"}]}, "value1": "={{$node[\"Typeform Trigger\"].json[\"What type of a request are you making?\"]}}", "dataType": "string"}, "typeVersion": 1}], "active": true, "settings": {}, "connections": {"Pick List": {"main": [[{"node": "ListID 54685003", "type": "main", "index": 0}], [{"node": "ListID 54685000", "type": "main", "index": 0}], [{"node": "ListID 54684957", "type": "main", "index": 0}], [{"node": "ListID 54684997", "type": "main", "index": 0}]]}, "ListID 54684957": {"main": [[{"node": "ClickUp", "type": "main", "index": 0}]]}, "ListID 54684997": {"main": [[{"node": "ClickUp", "type": "main", "index": 0}]]}, "ListID 54685000": {"main": [[{"node": "ClickUp", "type": "main", "index": 0}]]}, "ListID 54685003": {"main": [[{"node": "ClickUp", "type": "main", "index": 0}]]}, "Typeform Trigger": {"main": [[{"node": "Pick List", "type": "main", "index": 0}]]}}}