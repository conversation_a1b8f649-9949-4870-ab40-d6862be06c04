{"meta": {"instanceId": "1eadd5bc7c3d70c587c28f782511fd898c6bf6d97963d92e836019d2039d1c79"}, "nodes": [{"id": "578905af-9355-47ba-97c0-05bc9e69876c", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [-420, -120], "parameters": {"color": 4, "width": 1280, "height": 320, "content": "=======================================\n            WORKFLOW ASSISTANCE\n=======================================\nFor any questions or support, please contact:\n    <EMAIL>\n\nExplore more tips and tutorials here:\n   - YouTube: https://www.youtube.com/@YaronBeen/videos\n   - LinkedIn: https://www.linkedin.com/in/yaronbeen/\n=======================================\nBright Data Docs: https://docs.brightdata.com/introduction\n"}, "typeVersion": 1}, {"id": "b54542b4-0f68-4076-9ae9-817c1aee0c14", "name": "Snapshot Progress", "type": "n8n-nodes-base.httpRequest", "position": [2180, 300], "parameters": {"url": "=https://api.brightdata.com/datasets/v3/progress/{{ $('HTTP Request- Post API call to Bright Data').item.json.snapshot_id }}", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer <YOUR_BRIGHT_DATA_API_KEY>"}]}}, "typeVersion": 4.2}, {"id": "8ffd290a-1cc7-4cc9-86a3-397108f8584b", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [3240, 80], "parameters": {"width": 195, "height": 646, "content": "In this workflow, I use Google Sheets to store the results. \n\nYou can use my template to get started faster:\n\n1. [Click on this link to get the template](https://docs.google.com/spreadsheets/d/1Zi758ds2_aWzvbDYqwuGiQNaurLgs-leS9wjLWWlbUU/edit?usp=sharing)\n2. Make a copy of the Sheets\n3. Add the URL to this node \n\n\n"}, "typeVersion": 1}, {"id": "d564fdb9-06f6-42c4-96d6-9512fa7217ca", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1200, 380], "parameters": {"width": 220, "height": 440, "content": "Add your competitors Trustpilot Link here.\n"}, "typeVersion": 1}, {"id": "8873b276-72db-42cd-8860-1327714d701b", "name": "On form submission - Discover Jobs", "type": "n8n-nodes-base.formTrigger", "position": [1260, 520], "webhookId": "8d0269c7-d1fc-45a1-a411-19634a1e0b82", "parameters": {"options": {}, "formTitle": "Please Paste The URL of Your Trustpilot competitor", "formFields": {"values": [{"fieldLabel": "Competitor TRUSTPILOT URL (include https://www.trsutpilot.com/review/", "placeholder": "https://www.trustpilot.com/review/www.nike.com", "requiredField": true}, {"fieldType": "dropdown", "fieldLabel": "Please select the time frame of reviews you'd like. If it's a big brand go with 30 days", "fieldOptions": {"values": [{"option": "Last 30 days"}, {"option": "Last 3 months"}, {"option": "Last 6 months"}, {"option": "Last 12 months"}]}}]}}, "typeVersion": 2.2}, {"id": "2396fb4f-e3da-4712-b6b5-93704fa69672", "name": "HTTP Request- Post API call to Bright Data", "type": "n8n-nodes-base.httpRequest", "position": [1560, 380], "parameters": {"url": "https://api.brightdata.com/datasets/v3/trigger", "method": "POST", "options": {}, "jsonBody": "=[\n  {\n    \"url\": \"{{ $json['Competitor TRUSTPILOT URL (include https://www.trsutpilot.com/review/'] }}\",\n    \"date_posted\": \"{{ $json['Please select the time frame of reviews you\\'d like. If it\\'s a big brand go with 30 days'] }}\"\n  }\n]", "sendBody": true, "sendQuery": true, "sendHeaders": true, "specifyBody": "json", "queryParameters": {"parameters": [{"name": "dataset_id", "value": "gd_lm5zmhwd2sni130p"}, {"name": "include_errors", "value": "true"}]}, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer <YOUR_BRIGHT_DATA_API_KEY>"}]}}, "typeVersion": 4.2}, {"id": "c90b0e25-c009-4321-9c38-7ce895d78f3f", "name": "Wait - Polling Bright Data", "type": "n8n-nodes-base.wait", "position": [1940, 300], "webhookId": "8005a2b3-2195-479e-badb-d90e4240e699", "parameters": {"unit": "minutes", "amount": 2}, "executeOnce": false, "typeVersion": 1.1}, {"id": "ac37b7e2-04fb-4f04-96f6-c77aa282dc8e", "name": "If - Checking status of Snapshot - if data is ready or not", "type": "n8n-nodes-base.if", "position": [2380, 300], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "7932282b-71bb-4bbb-ab73-4978e554de7e", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "running"}]}}, "typeVersion": 2.2}, {"id": "572ea592-8fd6-4be5-825b-83b0a7a11556", "name": "HTTP Request - Getting data from Bright Data", "type": "n8n-nodes-base.httpRequest", "position": [2660, 320], "parameters": {"url": "=https://api.brightdata.com/datasets/v3/snapshot/{{ $('HTTP Request- Post API call to Bright Data').item.json.snapshot_id }}", "options": {}, "sendQuery": true, "sendHeaders": true, "queryParameters": {"parameters": [{"name": "format", "value": "json"}]}, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer <YOUR_BRIGHT_DATA_API_KEY>"}]}}, "typeVersion": 4.2}, {"id": "03c7bfd2-6ae5-4455-8db9-df4858af9417", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1880, 160], "parameters": {"color": 4, "width": 940, "height": 360, "content": "Bright Data Getting Reviews\n"}, "typeVersion": 1}, {"id": "f68ece0c-6061-4204-8c90-b9dba3dae242", "name": "Basic LLM Chain", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [4160, 380], "parameters": {"text": "=Read the following bad reviews, these are reviews of our competitors:\n{{ $json.Aggregated_reviews }}\n\n---\nAfter reading them, summarize their weakest points.\nDon't mention the competitor name.\n\nWrite 3 different ads copy for our Facebook ads campaign, addressing these concerns", "promptType": "define"}, "typeVersion": 1.6}, {"id": "d07aa5c9-c0b0-440d-b9a8-21b5be269db3", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [4260, 600], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "MX2lQOZcGpmRvdVD", "name": "OpenAi account 2"}}, "typeVersion": 1.2}, {"id": "0dceb7f9-7133-40cd-87c7-7b786e104a2f", "name": "Send Summary To Marketers", "type": "n8n-nodes-base.gmail", "position": [4800, 400], "webhookId": "6787416d-689c-46ee-a7b5-97edd1fd1a00", "parameters": {"sendTo": "<EMAIL>", "message": "=Based on the following Trustpilot page: \n{{ $('On form submission - Discover Jobs').item.json['Competitor TRUSTPILOT URL (include https://www.trsutpilot.com/review/'] }}\n\nHere is a summary of recent complaints including ideas for ad copy:\n{{ $json.text }}\n-----------------------------\n\nI'm also attaching a break down of all recent complaints {{ $('Aggregating all filtered reviews').item.json.Aggregated_reviews }}\n", "options": {}, "subject": "=Summary of Complaints of competitor: {{ $('On form submission - Discover Jobs').item.json['Competitor TRUSTPILOT URL (include https://www.trsutpilot.com/review/'] }}", "emailType": "text"}, "credentials": {"gmailOAuth2": {"id": "TLJ5oxgGtoxdGOTZ", "name": "Gmail account 2"}}, "typeVersion": 2.1}, {"id": "********-fe16-4a1f-8ada-690a4188429d", "name": "Filtering only bad reviews", "type": "n8n-nodes-base.filter", "position": [3520, 380], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "loose"}, "combinator": "or", "conditions": [{"id": "7aaa3c61-27d5-4165-aaf3-4783d0ef0db0", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.review_rating }}", "rightValue": "1"}, {"id": "7aab561d-2454-4d4b-a5d6-51c0582ea85b", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.review_rating }}", "rightValue": "2"}]}, "looseTypeValidation": true}, "typeVersion": 2.2}, {"id": "a93f9763-4eaa-4654-9bb1-93a1c8b468f9", "name": "Aggregating all filtered reviews", "type": "n8n-nodes-base.aggregate", "position": [3780, 380], "parameters": {"options": {}, "fieldsToAggregate": {"fieldToAggregate": [{"renameField": true, "outputFieldName": "Aggregated_reviews", "fieldToAggregate": "review_content"}]}}, "typeVersion": 1}, {"id": "effec41f-a19f-48c7-a540-ec69968850ee", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [4120, 140], "parameters": {"width": 360, "height": 820, "content": "Adjust This Prompt with:\n1. Add info about your company and offer.\n\n2. The template requires the LLM to generate ad copy, but you can change it to any marketing material you'd like.\nExamples:\n- Suggest ideas for FAQ\n- Suggest copy for UGC scripts\n- Suggest copy for Add to cart email flow etc\n\n"}, "typeVersion": 1}, {"id": "e9bf2453-8f98-4d43-ac0c-f3e4b45787c9", "name": "Google Sheets - Adding All Reviews", "type": "n8n-nodes-base.googleSheets", "position": [3280, 380], "parameters": {"columns": {"value": {}, "schema": [{"id": "company_name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "review_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "review_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "review_date", "type": "string", "display": true, "removed": false, "required": false, "displayName": "review_date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "review_rating", "type": "string", "display": true, "removed": false, "required": false, "displayName": "review_rating", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "review_title", "type": "string", "display": true, "removed": false, "required": false, "displayName": "review_title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "review_content", "type": "string", "display": true, "removed": false, "required": false, "displayName": "review_content", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "is_verified_review", "type": "string", "display": true, "removed": false, "required": false, "displayName": "is_verified_review", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "review_date_of_experience", "type": "string", "display": true, "removed": false, "required": false, "displayName": "review_date_of_experience", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "reviewer_location", "type": "string", "display": true, "removed": false, "required": false, "displayName": "reviewer_location", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "reviews_posted_overall", "type": "string", "display": true, "removed": false, "required": false, "displayName": "reviews_posted_overall", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "review_replies", "type": "string", "display": true, "removed": false, "required": false, "displayName": "review_replies", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "review_useful_count", "type": "string", "display": true, "removed": false, "required": false, "displayName": "review_useful_count", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "reviewer_name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "reviewer_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_logo", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_logo", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "url", "type": "string", "display": true, "removed": false, "required": false, "displayName": "url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_overall_rating", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_overall_rating", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "is_verified_company", "type": "string", "display": true, "removed": false, "required": false, "displayName": "is_verified_company", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_total_reviews", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_total_reviews", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "5_star", "type": "string", "display": true, "removed": false, "required": false, "displayName": "5_star", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "4_star", "type": "string", "display": true, "removed": false, "required": false, "displayName": "4_star", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "3_star", "type": "string", "display": true, "removed": false, "required": false, "displayName": "3_star", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "2_star", "type": "string", "display": true, "removed": false, "required": false, "displayName": "2_star", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "1_star", "type": "string", "display": true, "removed": false, "required": false, "displayName": "1_star", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_about", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_about", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_phone", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_phone", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_location", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_location", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_country", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_country", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "breadcrumbs", "type": "string", "display": true, "removed": false, "required": false, "displayName": "breadcrumbs", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_category", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_category", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_website", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_website", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_other_categories", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_other_categories", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "review_url", "type": "string", "display": true, "removed": false, "required": false, "displayName": "review_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date_posted", "type": "string", "display": true, "removed": false, "required": false, "displayName": "date_posted", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "timestamp", "type": "string", "display": true, "removed": false, "required": false, "displayName": "timestamp", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "input", "type": "string", "display": true, "removed": false, "required": false, "displayName": "input", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Zi758ds2_aWzvbDYqwuGiQNaurLgs-leS9wjLWWlbUU/edit#gid=0", "cachedResultName": "input"}, "documentId": {"__rl": true, "mode": "list", "value": "1Zi758ds2_aWzvbDYqwuGiQNaurLgs-leS9wjLWWlbUU", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Zi758ds2_aWzvbDYqwuGiQNaurLgs-leS9wjLWWlbUU/edit?usp=drivesdk", "cachedResultName": "NoFluff-N8N-Sheet-Template- Trust PIlot Reviews Scraping WIth Bright Data"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "4RJOMlGAcB9ZoYfm", "name": "Google Sheets account 2"}}, "typeVersion": 4.3, "alwaysOutputData": true}, {"id": "a3911ad6-be39-4bba-9b1c-96c5a7017da4", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-400, 220], "parameters": {"width": 860, "height": 380, "content": "### Scrape Trustpilot Reviews Using Bright Data for Winning Ad Insights\n\nThis **n8n workflow** scrapes Trustpilot reviews of a specified competitor using **Bright Data's dataset API**. Users input the competitor's Trustpilot URL and select a timeframe (30 days, 3 months, 6 months, or 12 months) via an n8n form.\n\n**Workflow steps:**\n\n- Sends a request to Bright Data to fetch Trustpilot reviews based on user input.\n- Polls Bright Data until the dataset is ready.\n- Retrieves the reviews and logs them into a Google Sheet.\n- Filters the results to isolate negative reviews (ratings of 1 or 2 stars).\n- Aggregates negative reviews into summarized text.\n- Uses OpenAI's GPT-4o-mini to analyze competitor weaknesses and generate three Facebook ad copy variations addressing these pain points.\n- Emails the summary, including suggested ad copy and aggregated reviews, to the marketing team.\n"}, "typeVersion": 1}], "pinData": {}, "connections": {"Basic LLM Chain": {"main": [[{"node": "Send Summary To Marketers", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "Snapshot Progress": {"main": [[{"node": "If - Checking status of Snapshot - if data is ready or not", "type": "main", "index": 0}]]}, "Filtering only bad reviews": {"main": [[{"node": "Aggregating all filtered reviews", "type": "main", "index": 0}]]}, "Wait - Polling Bright Data": {"main": [[{"node": "Snapshot Progress", "type": "main", "index": 0}]]}, "Aggregating all filtered reviews": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "Google Sheets - Adding All Reviews": {"main": [[{"node": "Filtering only bad reviews", "type": "main", "index": 0}]]}, "On form submission - Discover Jobs": {"main": [[{"node": "HTTP Request- Post API call to Bright Data", "type": "main", "index": 0}]]}, "HTTP Request- Post API call to Bright Data": {"main": [[{"node": "Wait - Polling Bright Data", "type": "main", "index": 0}]]}, "HTTP Request - Getting data from Bright Data": {"main": [[{"node": "Google Sheets - Adding All Reviews", "type": "main", "index": 0}]]}, "If - Checking status of Snapshot - if data is ready or not": {"main": [[{"node": "Wait - Polling Bright Data", "type": "main", "index": 0}], [{"node": "HTTP Request - Getting data from Bright Data", "type": "main", "index": 0}]]}}}