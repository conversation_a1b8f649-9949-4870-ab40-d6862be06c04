{"nodes": [{"name": "Google Drive Trigger", "type": "n8n-nodes-base.googleDriveTrigger", "position": [250, 150], "parameters": {"event": "fileCreated", "options": {}, "triggerOn": "specificFolder", "folderToWatch": "1HwOAKkkgveLji8vVpW9Xrg1EsBskwMNb"}, "credentials": {"googleDriveOAuth2Api": {"id": "28", "name": "Google Drive account"}}, "typeVersion": 1}, {"name": "Send Email", "type": "n8n-nodes-base.emailSend", "position": [450, 150], "parameters": {"text": "=A file in your Google Drive file folder has been created: {{$json[\"name\"]}}", "options": {}, "subject": "File Update", "toEmail": "<EMAIL>", "fromEmail": "<EMAIL>"}, "credentials": {"smtp": {"id": "14", "name": "SMTP account"}}, "typeVersion": 1}], "connections": {"Google Drive Trigger": {"main": [[{"node": "Send Email", "type": "main", "index": 0}]]}}}