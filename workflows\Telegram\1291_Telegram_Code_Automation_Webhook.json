{"id": "ALg2eFzN4AsHIf3R", "meta": {"instanceId": "31e69f7f4a77bf465b805824e303232f0227212ae922d12133a0f96ffeab4fef", "templateCredsSetupCompleted": true}, "name": "✍️🌄 Your First Wordpress Content Creator - Quick Start", "tags": [], "nodes": [{"id": "********-10cb-419f-b86b-63155aeb4a55", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [180, -240], "parameters": {}, "typeVersion": 1}, {"id": "9df33245-102c-45e3-a99b-86be656d12e4", "name": "gpt-4o-mini", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [820, -40], "parameters": {"options": {"responseFormat": "json_object"}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "5c0cacb0-82de-42b0-903d-0b3718085ad8", "name": "Structured Output - JSON", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [1040, -40], "parameters": {"jsonSchemaExample": "{\n    \"title\": \"title\",\n    \"content\": \"content\"\n}"}, "typeVersion": 1.2}, {"id": "1ec2e58e-c775-47ab-9544-7c21521741a1", "name": "Separate Title & Content", "type": "n8n-nodes-base.code", "position": [1300, -340], "parameters": {"jsCode": "try {\n  // Check if input exists and has the expected structure\n  const input = $input.all();\n  if (!input || !input.length) {\n    throw new Error('No input data received');\n  }\n\n  const firstItem = input[0];\n  if (!firstItem || !firstItem.json || !firstItem.json.output || !firstItem.json.output.output) {\n    throw new Error('Invalid input structure: missing required properties');\n  }\n\n  const output = firstItem.json.output.output;\n  \n  // Validate title exists\n  if (!output.title) {\n    throw new Error('Missing title in output');\n  }\n\n  // Validate content exists\n  if (!output.content) {\n    throw new Error('Missing content in output');\n  }\n\n  const title = output.title;\n  const content = output.content.replace(/<h1>.*?<\\/h1>/s, '').trim();\n\n  // Validate final content is not empty after processing\n  if (!content) {\n    throw new Error('Content is empty after processing');\n  }\n\n  // console.log('Successfully processed content');\n\n  // console.log(title)\n  // console.log(content)\n  \n  return { title, content };\n\n} catch (error) {\n  // Log the error for debugging\n  console.error('Error processing content:', error.message);\n  \n  // Return a graceful failure object\n  return {\n    error: true,\n    message: error.message,\n    title: '',\n    content: '',\n    timestamp: new Date().toISOString()\n  };\n}"}, "typeVersion": 2}, {"id": "40f0ef7d-3c63-45ed-bbfb-8ed04772d214", "name": "gpt-4o-mini1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1760, 260], "parameters": {"model": "gpt-4o-mini-2024-07-18", "options": {"responseFormat": "json_object"}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "6b7326f8-7b88-4c4a-b5fc-29f68ca1d384", "name": "If1", "type": "n8n-nodes-base.if", "position": [2120, 60], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "aaf83c73-65f3-4a88-87f3-25b1acaf93ef", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $('Separate Title & Content').item.json.title }}", "rightValue": ""}, {"id": "d9af5bce-f0fb-4c20-8b6a-b01a3bf3e1d1", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.output }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "4674ff83-1076-4c0f-ad6c-1f0c9474520e", "name": "gpt-4o-mini2", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [920, 700], "parameters": {"options": {"responseFormat": "json_object"}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "3f869ae8-2638-445d-aec3-8c89c407badd", "name": "If2", "type": "n8n-nodes-base.if", "position": [1280, 500], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "aaf83c73-65f3-4a88-87f3-25b1acaf93ef", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.title }}", "rightValue": ""}, {"id": "d9af5bce-f0fb-4c20-8b6a-b01a3bf3e1d1", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.content }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "3f8a1f7f-c6fc-40f5-beb3-807adc71f797", "name": "gpt-4o-mini3", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [920, 1200], "parameters": {"options": {"responseFormat": "json_object"}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "0f72c53e-160d-4027-8b9c-5efa119f6039", "name": "If3", "type": "n8n-nodes-base.if", "position": [1280, 1000], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "aaf83c73-65f3-4a88-87f3-25b1acaf93ef", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.title }}", "rightValue": ""}, {"id": "d9af5bce-f0fb-4c20-8b6a-b01a3bf3e1d1", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.content }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "307ed55f-5602-46a5-b24d-39e9100ce038", "name": "Rewrite for Grade 5 Reading Level", "type": "@n8n/n8n-nodes-langchain.agent", "position": [920, 500], "parameters": {"text": "=Rewrite this article at a grade 5 reading level. Include some light humour and metaphorical examples that are age appropriate.  Ensure you retain all original content and only use the provided original content for the rewriting.  Provide final response in html format following these guidelines:\n\n## Formatting Guidelines\n- Use proper HTML tags throughout\n- Limit yourself to bold, italics, paragraphs and lists\n- Structure with <p> tags for paragraphs\n- Include appropriate spacing\n- Use <blockquote> for direct quotes\n- Maintain consistent formatting\n- Write in clear, professional tone\n- Break up long paragraphs\n- Use engaging subheadings\n- Include transitional phrases\n\n\n## Original content:  {{ $json.data }}", "agent": "conversationalAgent", "options": {}, "promptType": "define"}, "typeVersion": 1.6}, {"id": "6083344c-4497-43fe-b24b-5bef2b5cf90a", "name": "Rewrite for Grade 2 Reading Level", "type": "@n8n/n8n-nodes-langchain.agent", "position": [920, 1000], "parameters": {"text": "=Rewrite this article at a grade 2 reading level. Include some light humour and metaphorical examples that are age appropriate.  Ensure you retain all original content and only use the provided original content for the rewriting.  Provide final response in html format following these guidelines:\n\n## Formatting Guidelines\n- Use proper HTML tags throughout\n- Limit yourself to bold, italics, paragraphs and lists\n- Structure with <p> tags for paragraphs\n- Include appropriate spacing\n- Use <blockquote> for direct quotes\n- Maintain consistent formatting\n- Write in clear, professional tone\n- Break up long paragraphs\n- Use engaging subheadings\n- Include transitional phrases\n\n\n## Original content:  {{ $json.data }}", "agent": "conversationalAgent", "options": {}, "promptType": "define"}, "typeVersion": 1.6}, {"id": "58b109b0-270e-4a66-bb55-87a5427609d8", "name": "Rewrite for Grade 9 Reading Level", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1760, 60], "parameters": {"text": "=Rewrite this article at a grade 9 reading level using appropriate metaphors.  Ensure you retain all original content and only use the provided original content for the rewriting.  Do not create a Title.\n\nProvide final response in html format following these guidelines:\n\n## Formatting Guidelines\n- Use proper HTML tags throughout\n- Limit yourself to bold, italics, paragraphs and lists\n- Structure with <p> tags for paragraphs\n- Include appropriate spacing\n- Use <blockquote> for direct quotes\n- Maintain consistent formatting\n- Write in clear, professional tone\n- Break up long paragraphs\n- Use engaging subheadings\n- Include transitional phrases\n\n\n## Original content:  {{ $json.data }}", "agent": "conversationalAgent", "options": {}, "promptType": "define"}, "typeVersion": 1.6}, {"id": "aa2eacae-0409-4e18-987e-37605dd2bd89", "name": "Create Structured Blog Post", "type": "@n8n/n8n-nodes-langchain.agent", "position": [840, -240], "parameters": {"text": "={{ $json.topic }}", "agent": "conversationalAgent", "options": {"systemMessage": "=Analyze the provided PDF article and create a compelling blog post that will be returned in JSON format with two fields: \"title\" and \"content\". Follow these specifications:\n\n## Title Requirements\n- Create an engaging, SEO-friendly title under 10 words\n- Must not contain a colon\n- Should capture the article's essence\n- Will be formatted as an H1 in the content\n\n## Content Structure\n- Introduction (150-200 words)\n  * Compelling hook\n  * Topic context and importance\n  * Preview of main points\n\n- Main Content (6-8 chapters)\n  * Each chapter requires:\n    - Relevant H2 heading\n    - 300-400 words of unique content\n    - Specific topic focus\n    - Source material quotes/data\n    - Smooth transitions\n\n- Conclusion (200-250 words)\n  * Key takeaways\n  * Final thoughts/implications\n\n## Formatting Guidelines\n- Use proper HTML tags throughout\n- Limit yourself to bold, italics, paragraphs and lists\n- Structure with <p> tags for paragraphs\n- Include appropriate spacing\n- Use <blockquote> for direct quotes\n- Maintain consistent formatting\n- Write in clear, professional tone\n- Break up long paragraphs\n- Use engaging subheadings\n- Include transitional phrases\n\nThe content should be original, avoid direct copying, and maintain a consistent voice throughout. The final JSON response should contain only the title and content fields, with the content including all HTML formatting."}, "promptType": "define", "hasOutputParser": true}, "retryOnFail": true, "typeVersion": 1.6}, {"id": "69ebcaf7-6e1e-46c6-99f2-16e6b3c72a8e", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [740, -380], "parameters": {"color": 4, "width": 469, "height": 652, "content": "## Create Blog Post\nRefer to this workflow for help getting setup with DeepSeek\nhttps://n8n.io/workflows/2777-deepseek-v3-chat-and-r1-reasoning-quick-start/"}, "typeVersion": 1}, {"id": "e1a86c40-bcec-4891-bf74-4c3bdba0d07e", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [1660, -380], "parameters": {"color": 6, "width": 334, "height": 311, "content": "## Save Draft Blog Post to Google Drive"}, "typeVersion": 1}, {"id": "17b2a15a-f7e0-407f-ab55-9113ee7a9718", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [1660, -40], "parameters": {"color": 5, "width": 886, "height": 461, "content": "## Rewrite for Grade 9 Reading Level \nUpdate Agent prompt as required"}, "typeVersion": 1}, {"id": "d479bbe1-42cf-4592-820f-92803fc16f90", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [820, 400], "parameters": {"color": 3, "width": 726, "height": 461, "content": "## Rewrite for Grade 5 Reading Level \nUpdate Agent prompt as required"}, "typeVersion": 1}, {"id": "fa7dd661-f0ce-45e8-9a13-9967a5536ba3", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [820, 900], "parameters": {"width": 726, "height": 461, "content": "## Rewrite for Grade 2 Reading Level \nUpdate Agent prompt as required"}, "typeVersion": 1}, {"id": "b2638528-b0e0-46db-b921-b175ae54c3da", "name": "DeepSeek", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "disabled": true, "position": [820, 120], "parameters": {"model": "=deepseek-reasoner", "options": {}}, "credentials": {"openAiApi": {"id": "MSl7SdcvZe0SqCYI", "name": "deepseek"}}, "typeVersion": 1.1}, {"id": "c588d522-6a21-4aea-a872-f32bea6b1d94", "name": "Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [1760, -260], "parameters": {"name": "={{$('Separate Title & Content').item.json.title }}", "content": "={{ $json.data }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "root", "cachedResultName": "/ (Root folder)"}, "operation": "createFromText"}, "credentials": {"googleDriveOAuth2Api": {"id": "UhdXGYLTAJbsa0xX", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "f2c2a8e3-68d1-482b-b3a9-d62edfd8b327", "name": "Set Blog Topic", "type": "n8n-nodes-base.set", "position": [500, -240], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "3e8d2523-66aa-46fe-adcc-39dc78b9161e", "name": "topic", "type": "string", "value": "=Why Nostr is the and coming decentralized network."}]}}, "typeVersion": 3.4}, {"id": "2b2718f6-2ca8-4c50-9d0e-2705329ccdcc", "name": "pollinations.ai", "type": "n8n-nodes-base.httpRequest", "position": [2040, 700], "parameters": {"url": "=https://image.pollinations.ai/prompt/{{ $('Separate Title & Content').item.json.title }} and Avoid adding text and keep the image vibrant.", "options": {}}, "typeVersion": 4.2}, {"id": "62145d3b-1994-406e-9b3f-8c8f93b41153", "name": "Create Wordpress Post", "type": "n8n-nodes-base.wordpress", "position": [1760, 700], "parameters": {"title": "={{ $('Separate Title & Content').item.json.title }}", "additionalFields": {"status": "draft", "content": "={{ $json.output }}"}}, "credentials": {"wordpressApi": {"id": "cOkzd5eeOiHaOXI2", "name": "Wordpress account"}}, "typeVersion": 1}, {"id": "********-dd22-4237-910d-726717306f2b", "name": "Upload Image to Wordpress", "type": "n8n-nodes-base.httpRequest", "position": [2320, 700], "parameters": {"url": "https://[YOUR-WORDPRESS-SITE.com]/wp-json/wp/v2/media", "method": "POST", "options": {}, "sendBody": true, "contentType": "binaryData", "sendHeaders": true, "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{"name": "Content-Disposition", "value": "=attachment; filename=\"cover-image-{{ $('Create Wordpress Post').item.json.id }}.jpeg\""}]}, "inputDataFieldName": "data", "nodeCredentialType": "wordpressApi"}, "credentials": {"wordpressApi": {"id": "cOkzd5eeOiHaOXI2", "name": "Wordpress account"}}, "typeVersion": 4.2}, {"id": "9576157b-3687-4490-b435-6207f4fbf0de", "name": "Set Image on Wordpress Post", "type": "n8n-nodes-base.httpRequest", "position": [2520, 700], "parameters": {"url": "=https:/[YOUR-WORDPRESS-SITE.com]/wp-json/wp/v2/posts/{{ $('Create Wordpress Post').item.json.id }}", "method": "POST", "options": {}, "sendQuery": true, "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "featured_media", "value": "={{ $json.id }}"}]}, "nodeCredentialType": "wordpressApi"}, "credentials": {"wordpressApi": {"id": "cOkzd5eeOiHaOXI2", "name": "Wordpress account"}}, "typeVersion": 4.2}, {"id": "b84be0bc-af0a-409c-941e-a1c4381eaf59", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1660, 460], "parameters": {"color": 4, "width": 1066, "height": 701, "content": "## Create Wordpress Post and Add New Image\nhttps://docs.n8n.io/integrations/builtin/credentials/wordpress/"}, "typeVersion": 1}, {"id": "7450c5c4-df04-4ad2-8d1b-0a5f59814ddd", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1940, 560], "parameters": {"width": 300, "height": 340, "content": "## Create Post Image\nhttps://pollinations.ai/\nhttps://image.pollinations.ai/prompt/[your image description]"}, "typeVersion": 1}, {"id": "6595fbdc-6ef9-4fca-a06c-d89f244532eb", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [740, 320], "parameters": {"color": 7, "width": 880, "height": 1100, "content": "## Alternative Workflows for Various Reading Levels"}, "typeVersion": 1}, {"id": "49546a1d-5e94-4b39-bc21-058ef3e11c85", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [400, -380], "parameters": {"color": 5, "width": 300, "height": 360, "content": "## 🌟 Set Blog Topic"}, "typeVersion": 1}, {"id": "8523d441-a67f-4e34-a5c5-8781400c939e", "name": "HTML to Markdown", "type": "n8n-nodes-base.markdown", "position": [1500, -140], "parameters": {"html": "={{ $json.content }}", "options": {}}, "typeVersion": 1}, {"id": "eb0a1f38-8c6a-4b40-9d5e-13ce1fcd645f", "name": "Tiltle & Content Exist?", "type": "n8n-nodes-base.if", "position": [1300, -140], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "aaf83c73-65f3-4a88-87f3-25b1acaf93ef", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $('Separate Title & Content').item.json.title }}", "rightValue": ""}, {"id": "d9af5bce-f0fb-4c20-8b6a-b01a3bf3e1d1", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $('Separate Title & Content').item.json.content }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "ada832e8-4699-4c4a-bbd6-80b0f9de4202", "name": "Send Error Message", "type": "n8n-nodes-base.telegram", "position": [1300, 100], "webhookId": "382a3b43-b83f-47b1-a276-67c6b98a441a", "parameters": {"text": "=Error!  Title or Content Missing.  Workflow aborted at {{ $now }}", "chatId": "={{ $env.TELEGRAM_CHAT_ID }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "6c5f55b9-9fa5-4d86-b5a2-69966a0d5cdb", "name": "Send Error Message1", "type": "n8n-nodes-base.telegram", "position": [2320, 60], "webhookId": "382a3b43-b83f-47b1-a276-67c6b98a441a", "parameters": {"text": "=Error!  Title or Content Missing.  Workflow aborted at {{ $now }}", "chatId": "={{ $env.TELEGRAM_CHAT_ID }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "d0b2a677-fbd9-43f6-81c3-fcb7ea320d1d", "name": "Send Error Message2", "type": "n8n-nodes-base.telegram", "position": [1280, 680], "webhookId": "382a3b43-b83f-47b1-a276-67c6b98a441a", "parameters": {"text": "=Error!  Title or Content Missing.  Workflow aborted at {{ $now }}", "chatId": "={{ $env.TELEGRAM_CHAT_ID }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "********-0366-4fbe-b486-0b248b5b5ec0", "name": "Send Error Message3", "type": "n8n-nodes-base.telegram", "position": [1280, 1180], "webhookId": "382a3b43-b83f-47b1-a276-67c6b98a441a", "parameters": {"text": "=Error!  Title or Content Missing.  Workflow aborted at {{ $now }}", "chatId": "={{ $env.TELEGRAM_CHAT_ID }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "f7633c8b-b592-4566-90df-e7f475662b0c", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [100, -380], "parameters": {"color": 4, "width": 260, "height": 360, "content": "## 👍 Start Here"}, "typeVersion": 1}, {"id": "a622ecc7-4c7d-42d5-b00c-eff0f8b42916", "name": "Send Success Message", "type": "n8n-nodes-base.telegram", "position": [2520, 940], "webhookId": "382a3b43-b83f-47b1-a276-67c6b98a441a", "parameters": {"text": "=Success! Your blog post was created at {{ $now }}", "chatId": "={{ $env.TELEGRAM_CHAT_ID }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "58ee5b12-1b66-4aa3-8937-46e17f589f49", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [100, 20], "parameters": {"width": 600, "height": 1400, "content": "# ✍️🌄 WordPress + AI Content Creator\n\nThis workflow automates the creation and publishing of multi-reading-level content for WordPress blogs. It leverages AI to generate optimized articles, automatically creates featured images, and provides versions of the content at different reading levels (Grade 2, 5, and 9).\n\n## How It Works\n\n### Content Generation & Processing 🎯\n- Starts with a manual trigger and a user-defined blog topic\n- Uses AI to create a structured blog post with proper HTML formatting\n- Separates and validates the title and content components\n- Saves a draft version to Google Drive for backup\n\n### Multi-Reading Level Versions 📚\nAutomatically rewrites the content for different reading levels:\n- Grade 9: Sophisticated language with appropriate metaphors\n- Grade 5: Simplified with light humor and age-appropriate examples\n- Grade 2: Basic language with simple metaphors and child-friendly explanations\n\n### WordPress Integration 🌐\n- Creates a draft post in WordPress with the Grade 9 version\n- Generates a relevant featured image using Pollinations.ai\n- Automatically uploads and sets the featured image\n- Sends success/error notifications via Telegram\n\n## Setup Steps\n\n### Configure API Credentials 🔑\n- Set up WordPress API connection\n- Configure OpenAI API access\n- Set up Google Drive integration\n- Add Telegram bot credentials for notifications\n\n### Customize Content Parameters ⚙️\n- Adjust reading level prompts as needed\n- Modify image generation settings\n- Set WordPress post parameters\n\n### Test and Deploy 🚀\n- Run a test with a sample topic\n- Verify all reading level versions\n- Check WordPress draft creation\n- Confirm notification system\n\n\nThis workflow is perfect for content creators who need to maintain a consistent blog presence while catering to different audience reading levels. It's especially useful for educational content, news sites, or any platform that needs to communicate complex topics to diverse audiences."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"timezone": "America/Vancouver", "callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1", "executionTimeout": -1}, "versionId": "c03642b2-86f5-47ee-98b2-68cf891e8a58", "connections": {"If1": {"main": [[{"node": "Create Wordpress Post", "type": "main", "index": 0}], [{"node": "Send Error Message1", "type": "main", "index": 0}]]}, "If2": {"main": [[], [{"node": "Send Error Message2", "type": "main", "index": 0}]]}, "If3": {"main": [[], [{"node": "Send Error Message3", "type": "main", "index": 0}]]}, "gpt-4o-mini": {"ai_languageModel": [[{"node": "Create Structured Blog Post", "type": "ai_languageModel", "index": 0}]]}, "gpt-4o-mini1": {"ai_languageModel": [[{"node": "Rewrite for Grade 9 Reading Level", "type": "ai_languageModel", "index": 0}]]}, "gpt-4o-mini2": {"ai_languageModel": [[{"node": "Rewrite for Grade 5 Reading Level", "type": "ai_languageModel", "index": 0}]]}, "gpt-4o-mini3": {"ai_languageModel": [[{"node": "Rewrite for Grade 2 Reading Level", "type": "ai_languageModel", "index": 0}]]}, "Set Blog Topic": {"main": [[{"node": "Create Structured Blog Post", "type": "main", "index": 0}]]}, "pollinations.ai": {"main": [[{"node": "Upload Image to Wordpress", "type": "main", "index": 0}]]}, "HTML to Markdown": {"main": [[{"node": "Rewrite for Grade 9 Reading Level", "type": "main", "index": 0}, {"node": "Google Drive", "type": "main", "index": 0}]]}, "Create Wordpress Post": {"main": [[{"node": "pollinations.ai", "type": "main", "index": 0}]]}, "Tiltle & Content Exist?": {"main": [[{"node": "HTML to Markdown", "type": "main", "index": 0}], [{"node": "Send Error Message", "type": "main", "index": 0}]]}, "Separate Title & Content": {"main": [[{"node": "Tiltle & Content Exist?", "type": "main", "index": 0}]]}, "Structured Output - JSON": {"ai_outputParser": [[{"node": "Create Structured Blog Post", "type": "ai_outputParser", "index": 0}]]}, "Upload Image to Wordpress": {"main": [[{"node": "Set Image on Wordpress Post", "type": "main", "index": 0}]]}, "Create Structured Blog Post": {"main": [[{"node": "Separate Title & Content", "type": "main", "index": 0}]]}, "Set Image on Wordpress Post": {"main": [[{"node": "Send Success Message", "type": "main", "index": 0}]]}, "Rewrite for Grade 2 Reading Level": {"main": [[{"node": "If3", "type": "main", "index": 0}]]}, "Rewrite for Grade 5 Reading Level": {"main": [[{"node": "If2", "type": "main", "index": 0}]]}, "Rewrite for Grade 9 Reading Level": {"main": [[{"node": "If1", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set Blog Topic", "type": "main", "index": 0}]]}}}