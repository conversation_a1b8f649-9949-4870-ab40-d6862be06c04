{"meta": {"instanceId": "dbd383feb42b0206c833e3d762df280d0cce35cf96037fb2d6f3533c171dc540"}, "nodes": [{"id": "b605b875-85cf-4210-8027-ce7b9b7069b9", "name": "CRON", "type": "n8n-nodes-base.scheduleTrigger", "notes": "Every Monday @ 8:30am", "position": [820, 360], "parameters": {"rule": {"interval": [{"field": "cronExpression", "expression": "30 8 * * MON"}]}}, "notesInFlow": true, "typeVersion": 1.1}, {"id": "edd9d7b6-9ac4-4acf-8f4b-4cbe943dfd34", "name": "Get User's Playlists", "type": "n8n-nodes-base.spotify", "position": [1060, 360], "parameters": {"resource": "playlist", "operation": "getUserPlaylists", "returnAll": true}, "credentials": {"spotifyOAuth2Api": {"id": "", "name": "Placeholder"}}, "typeVersion": 1}, {"id": "98cf8fdc-556a-452a-8df6-16a98c17613e", "name": "Switch", "type": "n8n-nodes-base.switch", "notes": "Find Discover and Archive playlist by name", "position": [1280, 360], "parameters": {"rules": {"rules": [{"value2": "Discover Weekly", "outputKey": "disc"}, {"value2": "Discover Weekly Archive", "outputKey": "arch"}]}, "value1": "={{ $json.name }}", "dataType": "string"}, "notesInFlow": true, "typeVersion": 2}, {"id": "d27ee5c1-86d8-4a6a-adcb-90bf59280550", "name": "Get Discover Tracks", "type": "n8n-nodes-base.spotify", "position": [1560, 240], "parameters": {"id": "={{ $json.uri }}", "resource": "playlist", "operation": "getTracks", "returnAll": true}, "credentials": {"spotifyOAuth2Api": {"id": "", "name": "Placeholder"}}, "typeVersion": 1}, {"id": "e276dce6-d3d2-41a0-96b6-68e115ed198e", "name": "Get Archive Tracks", "type": "n8n-nodes-base.spotify", "position": [1560, 460], "parameters": {"id": "={{ $json.uri }}", "resource": "playlist", "operation": "getTracks", "returnAll": true}, "credentials": {"spotifyOAuth2Api": {"id": "", "name": "Placeholder"}}, "typeVersion": 1}, {"id": "c619085b-424d-4ff5-b3eb-fa0eed05ce0d", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [1840, 360], "parameters": {"mode": "combine", "options": {}, "joinMode": "keepNonMatches", "mergeByFields": {"values": [{"field1": "track.uri", "field2": "track.uri"}]}, "outputDataFrom": "input1"}, "typeVersion": 2.1, "alwaysOutputData": true}, {"id": "6981f3b3-e1d4-415b-b2c2-d05646271270", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1760, 180], "parameters": {"color": 7, "width": 262, "height": 354, "content": "#### Purpose\n\nCompares tracks using `Combine` operation to output **non-matching** Discover Weekly tracks to ensure that duplicates are not appended to the Archive playlist"}, "typeVersion": 1}, {"id": "65a9ef93-afac-4f86-9203-73e24bcdb500", "name": "Check if tracks to add is NOT empty", "type": "n8n-nodes-base.if", "position": [2100, 360], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "81f9e3a7-eef0-462c-9d82-db894b19a110", "operator": {"type": "object", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json }}", "rightValue": ""}]}}, "typeVersion": 2}, {"id": "604717ae-90ab-412b-bc83-15399d18c0d4", "name": "Add tracks to Archive playlist", "type": "n8n-nodes-base.spotify", "position": [2360, 220], "parameters": {"id": "={{ $('Switch').all(1).first().json.uri }}", "trackID": "={{ $json.track.uri }}", "resource": "playlist", "additionalFields": {}}, "credentials": {"spotifyOAuth2Api": {"id": "", "name": "Placeholder"}}, "typeVersion": 1}, {"id": "34ddb49e-ebfe-46ff-a348-959befe5e86c", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [2580, 280], "parameters": {"color": 7, "width": 634, "height": 190, "content": "### Do anything else here\n\nFor example, in my personal workflow, after the tracks have been added to to my Archive playlist, I send a `POST` request to my self-hosted [NTFY](https://docs.ntfy.sh) server to notify me that my Discover Weekly playlist has refreshed, and I also provide the links to easily open the playlists within the notification that is sent.\n\nThe possibilities are endless with n8n!"}, "typeVersion": 1}, {"id": "c5c18f22-2c1f-4e65-83fc-7f89c5e44828", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [820, -418.*************4], "parameters": {"color": 4, "width": 605.*************, "height": 733.*************, "content": "## README\n\nThis workflow will automatically archive your Spotify `Discover Weekly` playlist to a separate playlist. One additional caveat this workflow addresses is it will compare the refreshed `Discover Weekly` tracks against your archive playlist's existing tracks to ensure only unique tracks are added to your archive playlist. This helps reduce clutter within the archive playlist. \n\n### Setup Instructions (Required)\n1. **Within your Spotify account, create a new playlist**\n  You may name this playlist whatever you like. The default name within the workflow is `Discover Weekly Archive`. If you decide on another name, ensure you update the `Switch` node output with the key of `arch` to the name of the playlist you set.\n\n2. **Create your Spotify credential(s) and update each Spotify node with your created credential**\n  Follow the instructions within the [n8n docs](https://docs.n8n.io/integrations/builtin/credentials/spotify/) to create your Spotify credential, then select the credential in the `Get User's Playlists`, `Get Discover Tracks`, `Get Archive Tracks`, and `Add tracks to Archive playlist` nodes.\n\n3. **Activate the workflow to run workflow based on the Cron expression set in the `Schedule` trigger node.**\n\n### Optional Setup\n- Update the Cron expression within the `Schedule` trigger node with an earlier or later time if your `GENERIC_TIMEZONE` is set. You may also consider setting the workflow timezone as well. For assistance with Cron expressions, refer to the [n8n docs](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.scheduletrigger/#generate-a-custom-cron-expression).\n- Add nodes to the end of the workflow to send notifications, update a spreadsheet, or any other operation/action you'd like to perform in conjunction with archiving your Discover Weekly tracks\n- Setup a error workflow to handle any thing that goes wrong within the workflow. You can find an example [here](https://n8n.io/workflows/696-send-email-via-gmail-on-workflow-error/) or more information concerning handling errors within the [n8n docs](https://docs.n8n.io/courses/level-two/chapter-4/).\n"}, "typeVersion": 1}], "pinData": {}, "connections": {"CRON": {"main": [[{"node": "Get User's Playlists", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "Check if tracks to add is NOT empty", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Get Discover Tracks", "type": "main", "index": 0}], [{"node": "Get Archive Tracks", "type": "main", "index": 0}]]}, "Get Archive Tracks": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Get Discover Tracks": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Get User's Playlists": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Check if tracks to add is NOT empty": {"main": [[{"node": "Add tracks to Archive playlist", "type": "main", "index": 0}]]}}}