{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [270, 330], "parameters": {}, "typeVersion": 1}, {"name": "Merge Data", "type": "n8n-nodes-base.function", "position": [1230, 430], "parameters": {"functionCode": "const allData = []\n\nlet counter = 0;\ndo {\n  try {\n    const items = $items(\"RSS Feed Read\", 0, counter).map(item => item.json);\n    allData.push.apply(allData, items);\n  } catch (error) {\n    return [{json: {allData}}];  \n  }\n\n  counter++;\n} while(true);\n\n\n"}, "typeVersion": 1}, {"name": "Function", "type": "n8n-nodes-base.function", "position": [470, 330], "parameters": {"functionCode": "return [\n  {\n    json: {\n      url: 'https://medium.com/feed/n8n-io',\n    }\n  },\n  {\n    json: {\n      url: 'https://dev.to/feed/n8n',\n    }\n  }\n];"}, "typeVersion": 1}, {"name": "RSS Feed Read", "type": "n8n-nodes-base.rssFeedRead", "position": [870, 330], "parameters": {"url": "={{$json[\"url\"]}}"}, "typeVersion": 1}, {"name": "SplitInBatches", "type": "n8n-nodes-base.splitInBatches", "position": [670, 330], "parameters": {"options": {}, "batchSize": 1}, "typeVersion": 1}, {"name": "IF", "type": "n8n-nodes-base.if", "position": [1070, 520], "parameters": {"conditions": {"boolean": [{"value1": true, "value2": "={{$node[\"SplitInBatches\"].context[\"noItemsLeft\"]}}"}]}}, "typeVersion": 1}], "connections": {"IF": {"main": [[{"node": "Merge Data", "type": "main", "index": 0}], [{"node": "SplitInBatches", "type": "main", "index": 0}]]}, "Function": {"main": [[{"node": "SplitInBatches", "type": "main", "index": 0}]]}, "RSS Feed Read": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}, "SplitInBatches": {"main": [[{"node": "RSS Feed Read", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Function", "type": "main", "index": 0}]]}}}