{"nodes": [{"id": "2804a082-c17b-482f-828d-901dab7e7a11", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-160, 40], "parameters": {}, "typeVersion": 1}, {"id": "58d51340-5246-4089-ae63-f16ff4be184e", "name": "Get list of owners", "type": "n8n-nodes-base.httpRequest", "position": [280, 40], "parameters": {"url": "https://api.hubapi.com/crm/v3/owners", "options": {}, "authentication": "predefinedCredentialType", "nodeCredentialType": "hubspotOAuth2Api"}, "credentials": {"hubspotOAuth2Api": {"id": "qubiIFrowxvUdpu6", "name": "HubSpot account for node"}}, "typeVersion": 4.2}, {"id": "335ffd8c-68fa-4d55-85e9-462963a8a291", "name": "Get list of clients for owner", "type": "n8n-nodes-base.httpRequest", "position": [280, 575], "parameters": {"url": "https://api.hubapi.com/crm/v3/objects/contacts/search", "method": "POST", "options": {}, "jsonBody": "={\n  \"filterGroups\": [\n    {\n      \"filters\": [\n        {\n          \"propertyName\": \"hubspot_owner_id\",\n          \"operator\": \"EQ\",\n          \"value\": \"{{ $('When Executed by Another Workflow').item.json.ownerId }}\"\n        }\n      ]\n    }\n  ],\n  \"properties\": [\"firstname\", \"lastname\", \"email\", \"linkedinURL\", \"company\"],\n\"limit\": 200,\n\"after\": {{ $node['Edit'].json[\"sofar\"] }}\n} ", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "hubspotOAuth2Api"}, "credentials": {"hubspotOAuth2Api": {"id": "qubiIFrowxvUdpu6", "name": "HubSpot account for node"}}, "typeVersion": 4.2}, {"id": "5d116139-1764-4d3a-8696-d280fb7e9d8f", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-210, -260], "parameters": {"color": 4, "width": 420, "height": 460, "content": "## Settings\n- Set in \"Set data here\" the email you are registered with in Hubspot as an Owner, and the link of a Google sheet copied [from this one](https://docs.google.com/spreadsheets/d/1y17jIU6JnNPcmazWf2GsmRpdjBBMnkN41tRJnAO5KrQ/edit?usp=sharing)\n"}, "typeVersion": 1}, {"id": "a8a15bd4-5a46-4f70-87bd-4db7170b4928", "name": "If", "type": "n8n-nodes-base.if", "position": [720, 575], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "loose"}, "combinator": "and", "conditions": [{"id": "d5df6d6c-ff5f-46ad-a8d4-38e326d7415e", "operator": {"type": "number", "operation": "gte"}, "leftValue": "={{ $node['Edit'].json.sofar }}", "rightValue": "={{ $('Get list of clients for owner').item.json.total }}"}]}, "looseTypeValidation": true}, "typeVersion": 2.2}, {"id": "eda30bd9-95bb-43d4-8981-479036103dd1", "name": "Edit", "type": "n8n-nodes-base.set", "position": [60, 575], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "8a403dc5-2b05-430d-b1cc-39f70f5ac82d", "name": "sofar", "type": "number", "value": "=0"}, {"id": "dca65b15-f545-42f1-90df-37efb03e267d", "name": "results", "type": "array", "value": "[]"}]}}, "typeVersion": 3.4}, {"id": "4c6c8217-6610-413e-8b1c-185a96e44882", "name": "Increment Page", "type": "n8n-nodes-base.set", "position": [500, 500], "parameters": {"values": {"number": [{"name": "sofar", "value": "={{$node[\"Edit\"].json[\"sofar\"] = $node[\"Edit\"].json[\"sofar\"] + $('Get list of clients for owner').item.json.results.length}}"}]}, "options": {}}, "executeOnce": true, "typeVersion": 1}, {"id": "58f53fe6-36a4-4385-ba93-e15dd589c0a4", "name": "Split Out1", "type": "n8n-nodes-base.splitOut", "position": [1160, 575], "parameters": {"options": {}, "fieldToSplitOut": "results"}, "typeVersion": 1}, {"id": "c92983ba-bef3-463a-a6de-8f205822f359", "name": "Merge al the entries", "type": "n8n-nodes-base.code", "position": [940, 575], "parameters": {"jsCode": "let results = [],\n  i = 0;\n\ndo {\n  try {\n    results = results.concat($(\"Get list of clients for owner\").all(0, i));\n  } catch (error) {\n    console.log(results)\n    return results;\n  }\n  i++;\n} while (true);"}, "typeVersion": 2}, {"id": "68c51fbd-3845-4eb2-9204-d78cc30413bf", "name": "If linkedin url is empty", "type": "n8n-nodes-base.if", "position": [1820, 40], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "84952199-2e1d-4ea8-bfb8-d4aa975d6df1", "operator": {"type": "string", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json.linkedin_url }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "18e4e4bd-4039-4770-a3d0-13edafe6103c", "name": "if new post", "type": "n8n-nodes-base.if", "position": [3580, 40], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "48d6777d-5431-4cb9-9716-5059277bac5e", "operator": {"type": "string", "operation": "notEquals"}, "leftValue": "={{ $('Get rows from document').item.json['last post'] }}", "rightValue": "={{ $('Set last_post').item.json.last_post }}"}]}}, "typeVersion": 2.2}, {"id": "a5623af7-6fba-43b0-be50-c9d3c52aba32", "name": "Get list of clients", "type": "n8n-nodes-base.executeWorkflow", "position": [940, 40], "parameters": {"mode": "each", "options": {}, "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}", "cachedResultName": "={{ $workflow.id }}"}, "workflowInputs": {"value": {"ownerId": "={{ $json.id }}"}, "schema": [{"id": "ownerId", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ownerId", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["ownerId"], "attemptToConvertTypes": false, "convertFieldsToString": true}}, "typeVersion": 1.2}, {"id": "9f771153-6b83-4ac0-b642-ee2d4b65a41c", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-160, 575], "parameters": {"workflowInputs": {"values": [{"name": "ownerId"}]}}, "typeVersion": 1.1}, {"id": "437dc3e5-0340-41ce-aea1-36749bd054ad", "name": "Get last post", "type": "n8n-nodes-base.executeWorkflow", "position": [3140, 40], "parameters": {"mode": "each", "options": {}, "workflowId": {"__rl": true, "mode": "list", "value": "rnVcO8Bw0avTm4GB", "cachedResultName": "get personal posts for agent"}, "workflowInputs": {"value": {"maxItems": 1, "username": "={{ $json.username }}", "responseType": "detail"}, "schema": [{"id": "username", "type": "string", "display": true, "required": false, "displayName": "username", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "responseType", "type": "string", "display": true, "required": false, "displayName": "responseType", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "maxItems", "type": "number", "display": true, "required": false, "displayName": "maxItems", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "posted_after", "display": true, "required": false, "displayName": "posted_after", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": true}}, "typeVersion": 1.2}, {"id": "a0dda2f2-cb89-4557-8cfc-5e3a01e34637", "name": "Gmail", "type": "n8n-nodes-base.gmail", "position": [4680, 140], "webhookId": "eea16996-1d02-4861-b83d-6145cee90ac6", "parameters": {"sendTo": "={{ $('Set data here').first().json.email }}", "message": "={{ $json.text }}", "options": {"appendAttribution": false}, "subject": "Changes in your clients", "emailType": "text"}, "credentials": {"gmailOAuth2": {"id": "DLjspol9TLgpGaXa", "name": "Gmail account 2"}}, "typeVersion": 2.1}, {"id": "********-e603-454c-b533-2e387f2008c4", "name": "Search for user by link", "type": "n8n-nodes-base.httpRequest", "notes": "Search by Name and company", "position": [2920, 140], "parameters": {"url": "https://linkedin-api8.p.rapidapi.com/get-profile-data-by-url", "options": {}, "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "queryParameters": {"parameters": [{"name": "url", "value": "={{ $json.profileURL }}"}]}, "headerParameters": {"parameters": [{"name": "x-rapidapi-host", "value": "linkedin-api8.p.rapidapi.com"}]}}, "credentials": {"httpHeaderAuth": {"id": "nhoVFnkO31mejJrI", "name": "RapidAPI Key"}}, "executeOnce": false, "notesInFlow": true, "typeVersion": 4.2, "alwaysOutputData": false}, {"id": "903f6be4-b468-488c-aa41-50f60ee92bcb", "name": "Do nothing", "type": "n8n-nodes-base.noOp", "position": [2480, -160], "parameters": {}, "typeVersion": 1}, {"id": "695202d6-60bd-4788-b029-0c03a9e3c89a", "name": "Merge profileURL", "type": "n8n-nodes-base.code", "position": [2700, 140], "parameters": {"mode": "runOnceForEachItem", "jsCode": "// Add a new field called 'myNewField' to the JSON of the item\n$input.item.json.profileURL = $json.profileURL;\n\nreturn $input.item;"}, "typeVersion": 2}, {"id": "ead5d235-7f73-41e4-86d3-48ad7d4cfa8d", "name": "Set last_post", "type": "n8n-nodes-base.set", "position": [3360, 40], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "93be271c-22c8-4afe-a928-e9d2593b025d", "name": "last_post", "type": "string", "value": "={{ $json.text[0] }}"}]}}, "typeVersion": 3.4}, {"id": "a89746c8-5fb7-4c69-930f-d0f451bcef54", "name": "Set last_position", "type": "n8n-nodes-base.set", "position": [3360, 240], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "93be271c-22c8-4afe-a928-e9d2593b025d", "name": "last_position", "type": "string", "value": "={{ $json.position[0].title }}"}]}}, "typeVersion": 3.4}, {"id": "467aa5a3-c9f5-407f-8571-e9ba333109e2", "name": "if new position", "type": "n8n-nodes-base.if", "position": [3580, 240], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "48d6777d-5431-4cb9-9716-5059277bac5e", "operator": {"type": "string", "operation": "notEquals"}, "leftValue": "={{ $json.last_position }}", "rightValue": "={{ $('Get rows from document').item.json['current position'] }}"}]}}, "typeVersion": 2.2}, {"id": "f6456ca1-bb31-4bf3-8175-7f8b5f4a65bb", "name": "Set data here", "type": "n8n-nodes-base.set", "position": [60, 40], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "a43825a4-f9cd-4b38-975c-b1de771cebea", "name": "sheetLink", "type": "string", "value": "https://docs.google.com/spreadsheets/d/1y17jIU6JnNPcmazWf2GsmRpdjBBMnkN41tRJnAO5KrQ/edit?gid=0#gid=0"}, {"id": "ea837d0b-e8d6-4594-9861-550d30f05db0", "name": "email", "type": "string", "value": "<EMAIL>"}]}}, "typeVersion": 3.4}, {"id": "f904c3fb-9320-4b5e-92af-c7cc697eb9dc", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [220, -260], "parameters": {"color": 4, "width": 230, "height": 460, "content": "## Hubspot API\nCalls a Hubpot API endpoint to get the list of Owners\nYou have to select \"Predefined credential  type\" and choose \"Hubspot Oauth2 API\". Then folow the instructions [here](https://docs.n8n.io/integrations/builtin/credentials/hubspot/?utm_source=n8n_app&utm_medium=credential_settings&utm_campaign=create_new_credentials_modal#required-scopes-for-hubspot-trigger-node)"}, "typeVersion": 1}, {"id": "5a499d0c-bbe5-4041-a082-6111658bf155", "name": "Change this for testing", "type": "n8n-nodes-base.filter", "position": [1160, 40], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "or", "conditions": [{"id": "45031804-4846-4b03-8c8f-8f1a747986a4", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.properties.email }}", "rightValue": "=<EMAIL>"}]}}, "typeVersion": 2.2}, {"id": "089690f8-ddf5-46e4-933c-152095ea02ac", "name": "Create entry with email", "type": "n8n-nodes-base.googleSheets", "position": [1380, 40], "parameters": {"columns": {"value": {"email": "={{ $json.properties.email }}"}, "schema": [{"id": "email", "type": "string", "display": true, "removed": false, "required": false, "displayName": "email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedin_url", "type": "string", "display": true, "removed": true, "required": false, "displayName": "linkedin_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "last post", "type": "string", "display": true, "removed": true, "required": false, "displayName": "last post", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "current position", "type": "string", "display": true, "removed": true, "required": false, "displayName": "current position", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date", "type": "string", "display": true, "removed": true, "required": false, "displayName": "date", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["email"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "url", "value": "={{ $('Set data here').first().json.sheetLink }}"}, "documentId": {"__rl": true, "mode": "url", "value": "={{ $('Set data here').first().json.sheetLink }}"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "gdLmm513ROUyH6oU", "name": "Google Sheets account"}}, "typeVersion": 4.5, "alwaysOutputData": true}, {"id": "60617dc9-021b-41be-b7bf-0816bddba05e", "name": "Get rows from document", "type": "n8n-nodes-base.googleSheets", "position": [1600, 40], "parameters": {"options": {}, "filtersUI": {"values": [{"lookupValue": "={{ $json.email }}", "lookupColumn": "email"}]}, "sheetName": {"__rl": true, "mode": "url", "value": "={{ $('Set data here').first().json.sheetLink }}"}, "documentId": {"__rl": true, "mode": "url", "value": "={{ $('Set data here').first().json.sheetLink }}"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "gdLmm513ROUyH6oU", "name": "Google Sheets account"}}, "typeVersion": 4.5, "alwaysOutputData": true}, {"id": "21c2bbd5-75f1-4c80-a4ec-4c2870890fcf", "name": "Search for user profile by names", "type": "n8n-nodes-base.httpRequest", "notes": "Search by Name and company", "position": [2040, -60], "parameters": {"url": "https://linkedin-api8.p.rapidapi.com/search-people", "options": {}, "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "queryParameters": {"parameters": [{"name": "firstName", "value": "={{ $('Get list of clients').item.json.properties.firstname }}"}, {"name": "lastName", "value": "={{ $('Get list of clients').item.json.properties.lastname }}"}, {"name": "company", "value": "={{ $('Get list of clients').item.json.properties.company }}"}]}, "headerParameters": {"parameters": [{"name": "x-rapidapi-host", "value": "linkedin-api8.p.rapidapi.com"}]}}, "credentials": {"httpHeaderAuth": {"id": "nhoVFnkO31mejJrI", "name": "RapidAPI Key"}}, "executeOnce": false, "notesInFlow": true, "typeVersion": 4.2, "alwaysOutputData": false}, {"id": "1a6f6571-a89a-4e6e-82a5-c239661d6131", "name": "Profile URL not found?", "type": "n8n-nodes-base.if", "position": [2260, -60], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "c561dcaf-e164-46e5-8f44-8ebcd2943c78", "operator": {"type": "string", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json.data.items[0].profileURL }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "2811ca64-1e43-4934-abc2-f1a746b2cf68", "name": "Set the profile URL", "type": "n8n-nodes-base.set", "position": [2480, 40], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d6d3ebce-153c-44b0-a2f8-3c8ac41381cb", "name": "profileURL", "type": "string", "value": "={{ $json.data.items[0].profileURL }}"}]}}, "typeVersion": 3.4}, {"id": "d79a3896-e5b3-4b63-ad04-848f34db8100", "name": "Set the profile URL1", "type": "n8n-nodes-base.set", "position": [2480, 240], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d433d14f-b862-4f43-969e-5e911a138f8c", "name": "profileURL", "type": "string", "value": "={{ $('Get rows from document').item.json.linkedin_url }}"}]}}, "typeVersion": 3.4}, {"id": "73683bde-7431-47e2-b70f-8e0dd2725c84", "name": "Update last post", "type": "n8n-nodes-base.googleSheets", "position": [3800, 40], "parameters": {"columns": {"value": {"date": "= {{new Date().format('dd-MM-yyyy')}}", "email": "={{ $('Change this for testing').item.json.properties.email }}", "last post": "={{ $('Set last_post').item.json.last_post }}", "linkedin_url": "={{ $('Merge profileURL').item.json.profileURL }}", "current position": "={{ $json.last_position }}"}, "schema": [{"id": "email", "type": "string", "display": true, "removed": false, "required": false, "displayName": "email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedin_url", "type": "string", "display": true, "required": false, "displayName": "linkedin_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "last post", "type": "string", "display": true, "required": false, "displayName": "last post", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "current position", "type": "string", "display": true, "removed": false, "required": false, "displayName": "current position", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date", "type": "string", "display": true, "required": false, "displayName": "date", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["email"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "url", "value": "={{ $('Set data here').first().json.sheetLink }}"}, "documentId": {"__rl": true, "mode": "url", "value": "={{ $('Set data here').first().json.sheetLink }}"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "gdLmm513ROUyH6oU", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "71f66801-3b12-4633-a95b-c4c8788117bd", "name": "Updates last position", "type": "n8n-nodes-base.googleSheets", "position": [3800, 240], "parameters": {"columns": {"value": {"date": "= {{new Date().format('dd-MM-yyyy')}}", "email": "={{ $('Change this for testing').item.json.properties.email }}", "linkedin_url": "={{ $('Merge profileURL').item.json.profileURL }}", "current position": "={{ $json.last_position }}"}, "schema": [{"id": "email", "type": "string", "display": true, "removed": false, "required": false, "displayName": "email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedin_url", "type": "string", "display": true, "required": false, "displayName": "linkedin_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "last post", "type": "string", "display": true, "removed": true, "required": false, "displayName": "last post", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "current position", "type": "string", "display": true, "removed": false, "required": false, "displayName": "current position", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date", "type": "string", "display": true, "required": false, "displayName": "date", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["email"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "url", "value": "={{ $('Set data here').first().json.sheetLink }}"}, "documentId": {"__rl": true, "mode": "url", "value": "={{ $('Set data here').first().json.sheetLink }}"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "gdLmm513ROUyH6oU", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "d67c1106-5d91-4d00-980b-1b3578a1c86f", "name": "Set post_updated", "type": "n8n-nodes-base.set", "position": [4020, 40], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "71118d71-0ea6-4b01-ac4d-a3ae1129b0e7", "name": "post_updated", "type": "boolean", "value": true}, {"id": "f709a120-0db7-4b50-82e6-8f1f02352680", "name": "email", "type": "string", "value": "={{ $('Change this for testing').item.json.properties.email }}"}]}}, "typeVersion": 3.4}, {"id": "ad574631-3799-4f02-96ff-8683dc944331", "name": "Set position_updated", "type": "n8n-nodes-base.set", "position": [4020, 240], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "71118d71-0ea6-4b01-ac4d-a3ae1129b0e7", "name": "position_updated", "type": "boolean", "value": true}, {"id": "1f0e817d-2ad4-4330-b683-64e31cfa4741", "name": "email", "type": "string", "value": "={{ $('Change this for testing').item.json.properties.email }}"}]}}, "typeVersion": 3.4}, {"id": "157a5e8f-7b42-4115-91a2-2f204cd6d9f6", "name": "Generate the email text", "type": "n8n-nodes-base.code", "position": [4460, 140], "parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nlet client_post = []\nlet client_position = []\nfor (const item of $input.all()) {\n  if(item.json.position_updated) {\n    client_position.push(item.json.email)\n  }\n  if(item.json.post_updated) {\n    client_post.push(item.json.email)\n  }\n}\nlet text = ''\nif (client_post.length > 0) {\n  text = text + 'There has been a post update for the following clients:\\n'\n  text = text + client_post.join(\"\\n\") + \"\\n\";\n}\nif (client_position.length > 0) {\n  text = text + 'There has been a position update for the following clients:\\n'\n  text = text + client_position.join(\"\\n\") + \"\\n\";\n}\n\nreturn {text};"}, "typeVersion": 2}, {"id": "0735e123-47ed-4b3c-9aa8-cc7d2f982e4b", "name": "Merge on email", "type": "n8n-nodes-base.merge", "position": [4240, 140], "parameters": {"mode": "combine", "options": {}, "joinMode": "keepEverything", "fieldsToMatchString": "email"}, "typeVersion": 3.1}, {"id": "88717813-9e07-4984-8e8b-7772f7c7a0e7", "name": "Split Out owners", "type": "n8n-nodes-base.splitOut", "position": [500, 40], "parameters": {"options": {}, "fieldToSplitOut": "results"}, "typeVersion": 1}, {"id": "5799995c-659e-4732-a35b-893692d95509", "name": "Get current owner", "type": "n8n-nodes-base.filter", "position": [720, 40], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "7c6aec6e-66a9-4739-8a59-28f2ab1c4a26", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.email }}", "rightValue": "={{ $('Set data here').first().json.email }}"}]}}, "typeVersion": 2.2}, {"id": "637b821f-8051-4e91-a58e-85dc7e136467", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [880, -260], "parameters": {"color": 4, "width": 230, "height": 460, "content": "## Calling a Sub-workflow\nFor lisibility only\n"}, "typeVersion": 1}, {"id": "3bda132e-73a3-4af0-bb43-ff52f31f5a90", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1120, -260], "parameters": {"color": 4, "width": 190, "height": 460, "content": "## For testing\nWe recommend to filter a small number of clients here\n"}, "typeVersion": 1}, {"id": "13fb80f1-d685-4998-a534-41a0f944b6e8", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1320, -260], "parameters": {"color": 4, "width": 430, "height": 460, "content": "## Preparing the Google sheet\n- Create an entry for each client with his email\n- Get rows for later comparison\n\n"}, "typeVersion": 1}, {"id": "54a51950-a284-435e-8a2b-bdc0ebace3c9", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [3080, -240], "parameters": {"color": 4, "width": 1090, "height": 660, "content": "## Adding other tests\nHere you can set other tests like:\n- New comments in LinkedIn\n- Hubspot activities\n- Hubspot updates\netc.\n\n"}, "typeVersion": 1}, {"id": "4b197665-b31c-4fa2-8934-5b72468ca2e9", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [1960, -240], "parameters": {"color": 4, "width": 1090, "height": 660, "content": "## Searching for the LinkedIn URL\n- Set here you rapid API key\n- If we find the URL, searches for more info about the client\n"}, "typeVersion": 1}, {"id": "665fb602-dc0a-432f-a087-ce6097f58937", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [4200, -240], "parameters": {"color": 4, "width": 670, "height": 660, "content": "## Generating the alert email\n\n"}, "typeVersion": 1}, {"id": "d6789f6a-aa62-44fd-9314-fc0157b6dcd8", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [-220, 400], "parameters": {"color": 5, "width": 1570, "height": 460, "content": "## Get all clients for this owner\nAs we are limited to 200 clients by the Hubsot API, we have to loop to get all of them\n\n"}, "typeVersion": 1}, {"id": "d9a5cc09-7e4f-488a-a3dc-6a27361d9d0b", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [-200, -480], "parameters": {"width": 640, "height": 200, "content": "## Contact me\n- If you need any modification to this workflow\n- if you need some help with this workflow\n- Or if you need any workflow in n8n, Make, or Langchain / Langgraph\n\nWrite to me: [<EMAIL>](mailto:<EMAIL>)"}, "typeVersion": 1}], "connections": {"If": {"main": [[{"node": "Merge al the entries", "type": "main", "index": 0}], [{"node": "Get list of clients for owner", "type": "main", "index": 0}]]}, "Edit": {"main": [[{"node": "Get list of clients for owner", "type": "main", "index": 0}]]}, "Split Out1": {"main": [[]]}, "if new post": {"main": [[{"node": "Update last post", "type": "main", "index": 0}], []]}, "Get last post": {"main": [[{"node": "Set last_post", "type": "main", "index": 0}]]}, "Set data here": {"main": [[{"node": "Get list of owners", "type": "main", "index": 0}]]}, "Set last_post": {"main": [[{"node": "if new post", "type": "main", "index": 0}]]}, "Increment Page": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Merge on email": {"main": [[{"node": "Generate the email text", "type": "main", "index": 0}]]}, "if new position": {"main": [[{"node": "Updates last position", "type": "main", "index": 0}]]}, "Merge profileURL": {"main": [[{"node": "Search for user by link", "type": "main", "index": 0}]]}, "Set post_updated": {"main": [[{"node": "Merge on email", "type": "main", "index": 0}]]}, "Split Out owners": {"main": [[{"node": "Get current owner", "type": "main", "index": 0}]]}, "Update last post": {"main": [[{"node": "Set post_updated", "type": "main", "index": 0}]]}, "Get current owner": {"main": [[{"node": "Get list of clients", "type": "main", "index": 0}]]}, "Set last_position": {"main": [[{"node": "if new position", "type": "main", "index": 0}]]}, "Get list of owners": {"main": [[{"node": "Split Out owners", "type": "main", "index": 0}]]}, "Get list of clients": {"main": [[{"node": "Change this for testing", "type": "main", "index": 0}]]}, "Set the profile URL": {"main": [[{"node": "Merge profileURL", "type": "main", "index": 0}]]}, "Merge al the entries": {"main": [[{"node": "Split Out1", "type": "main", "index": 0}]]}, "Set position_updated": {"main": [[{"node": "Merge on email", "type": "main", "index": 1}]]}, "Set the profile URL1": {"main": [[{"node": "Merge profileURL", "type": "main", "index": 0}]]}, "Updates last position": {"main": [[{"node": "Set position_updated", "type": "main", "index": 0}]]}, "Get rows from document": {"main": [[{"node": "If linkedin url is empty", "type": "main", "index": 0}]]}, "Profile URL not found?": {"main": [[{"node": "Do nothing", "type": "main", "index": 0}], [{"node": "Set the profile URL", "type": "main", "index": 0}]]}, "Change this for testing": {"main": [[{"node": "Create entry with email", "type": "main", "index": 0}]]}, "Create entry with email": {"main": [[{"node": "Get rows from document", "type": "main", "index": 0}]]}, "Generate the email text": {"main": [[{"node": "Gmail", "type": "main", "index": 0}]]}, "Search for user by link": {"main": [[{"node": "Get last post", "type": "main", "index": 0}, {"node": "Set last_position", "type": "main", "index": 0}]]}, "If linkedin url is empty": {"main": [[{"node": "Search for user profile by names", "type": "main", "index": 0}], [{"node": "Set the profile URL1", "type": "main", "index": 0}]]}, "Get list of clients for owner": {"main": [[{"node": "Increment Page", "type": "main", "index": 0}]]}, "Search for user profile by names": {"main": [[{"node": "Profile URL not found?", "type": "main", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Edit", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set data here", "type": "main", "index": 0}]]}}}