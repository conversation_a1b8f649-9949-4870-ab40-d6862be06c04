{"nodes": [{"id": "5eeb368d-737a-4186-afef-3072d0e9a1c7", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "notes": "Execute WF on a schedule", "position": [940, 280], "parameters": {"rule": {"interval": [{}]}}, "notesInFlow": true, "typeVersion": 1.1}, {"id": "175f3ae0-6710-4934-b6c0-ebc21e26d0b5", "name": "Notion", "type": "n8n-nodes-base.notion", "disabled": true, "position": [1220, 80], "parameters": {"filters": {"conditions": [{"key": "Created time|created_time", "condition": "on_or_after", "createdTimeValue": "={{ $now.minus(7, 'days').toISOString() }}"}]}, "options": {}, "resource": "databasePage", "matchType": "allFilters", "operation": "getAll", "databaseId": {"__rl": true, "mode": "list", "value": "4ab15dec-f104-488e-936b-d14122106e7f", "cachedResultUrl": "https://www.notion.so/4ab15decf104488e936bd14122106e7f", "cachedResultName": "Product ideas list"}, "filterType": "manual"}, "credentials": {"notionApi": {"id": "5", "name": "Notion account"}}, "typeVersion": 2}, {"id": "8210582d-aae4-42b4-86d1-0513ad987c55", "name": "<PERSON><PERSON>ck", "type": "n8n-nodes-base.slack", "notes": "Post message in channel", "position": [2100, 280], "parameters": {"text": "=Yay, we added *{{ $json.unique_count_id }} new UX ideas* in the last 7 days :tada:", "select": "channel", "channelId": {"__rl": true, "mode": "name", "value": "#nik-wf-testing"}, "otherOptions": {}}, "credentials": {"slackApi": {"id": "6", "name": "<PERSON><PERSON>"}}, "notesInFlow": true, "typeVersion": 2}, {"id": "7db1f3c1-d1c9-4f41-a873-0f083543b4b4", "name": "Item Lists", "type": "n8n-nodes-base.itemLists", "position": [1800, 280], "parameters": {"options": {}, "operation": "summarize", "fieldsToSummarize": {"values": [{"field": "id", "aggregation": "countUnique"}]}}, "typeVersion": 2.2}, {"id": "f6856a5e-d57d-43f4-986b-cd8439e4caa0", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [400, 80], "parameters": {"width": 424, "height": 515.6050016413932, "content": "## 👋 How to use this template\nThis template shows how you can create reports on data in an app and share a summary in another app. Here's how to use it:\n\n1. Double click the `Slack` node and create a credential by signing in.\n2. Change the channel name in the `Slack` node to a channel you have in Slack.\n2. Click the `Execute Workflow` button and double click the nodes to see the input and output data\n\n### To customize it to you needs, just do the following:\n1. Enable or exchange the `Notion` node with any service that fits your use case.\n2. Change the `2. Filter and transform your data` section to fit your needs\n3. Adjust the Slack node or exchange it with any node that fits your use case\n4. Disable or remove the `Mock Data` node\n"}, "typeVersion": 1}, {"id": "13386afe-01c2-4f6e-b9d5-8fc485353ff9", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [2040, 220], "parameters": {"width": 317.52886836027733, "height": 373.04798303066787, "content": "### 3. Notify the right channel\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nFinally, we're sending a message to the `#ideas-overview` channel in Slack.\n\n**You can replace this node with any service like [Teams](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.microsoftteams/?utm_source=n8n_app&utm_medium=node_settings_modal-credential_link&utm_campaign=n8n-nodes-base.microsoftTeams), [Telegram](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.telegram/?utm_source=n8n_app&utm_medium=node_settings_modal-credential_link&utm_campaign=n8n-nodes-base.telegram), [Email](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.sendemail/?utm_source=n8n_app&utm_medium=node_settings_modal-credential_link&utm_campaign=n8n-nodes-base.emailSend) etc.**"}, "typeVersion": 1}, {"id": "1b4108e0-9e91-4b4e-a4cf-75366d7c82c0", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [860, 180], "parameters": {"width": 282, "height": 415.1692017070486, "content": "### 1. Define a trigger that should start your wofklow\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nWe added a `Schedule trigger` that starts the workflow once a week. \n\n**Double click the node to modify when it runs**"}, "typeVersion": 1}, {"id": "ba3eb63f-bdcd-4a58-949a-2d24d4c872c4", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1160, 0], "parameters": {"width": 348, "height": 597.3550016413941, "content": "### 2. Load your data\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nIn our example, we're getting all new entries from a `Notion` Database in which we save new product ideas.\n\n**You can replace product ideas with any data that you want to summarize any service you wish, like [Jira](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.jira/?utm_source=n8n_app&utm_medium=node_settings_modal-credential_link&utm_campaign=n8n-nodes-base.jira), [Airtable](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.airtable/?utm_source=n8n_app&utm_medium=node_settings_modal-credential_link&utm_campaign=n8n-nodes-base.airtable), [Google Sheets](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googlesheets/?utm_source=n8n_app&utm_medium=node_settings_modal-credential_link&utm_campaign=n8n-nodes-base.googleSheets) etc.**"}, "typeVersion": 1}, {"id": "9ec65fe7-3264-4437-a1bf-3bdec1c886fe", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [1540, 160], "parameters": {"width": 462, "height": 444.12384956830226, "content": "### 2. Filter and transform your data\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nWe only want to count the UX ideas of the team. We use the `Filter` node to filter out all other items, and use the `Item Lists` node to summarize the data for us.\n\nTo edit the nodes, simply drag and drop input data into the fields or change the values directly. **n8n comes with a set of powerful transformation and branching tools like [Set](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.set/?utm_source=n8n_app&utm_medium=node_settings_modal-credential_link&utm_campaign=n8n-nodes-base.set), [ItemList](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.itemlists/?utm_source=n8n_app&utm_medium=node_settings_modal-credential_link&utm_campaign=n8n-nodes-base.itemLists), [Code](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.code/?utm_source=n8n_app&utm_medium=node_settings_modal-credential_link&utm_campaign=n8n-nodes-base.code), [If](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.if/?utm_source=n8n_app&utm_medium=node_settings_modal-credential_link&utm_campaign=n8n-nodes-base.if)  and many more.**"}, "typeVersion": 1}, {"id": "5597d8bb-ae15-4ea0-be16-531d8a8f7018", "name": "Filter", "type": "n8n-nodes-base.filter", "notes": "Only keep UX ideas", "position": [1600, 280], "parameters": {"conditions": {"boolean": [{"value1": "={{ $json.property_type.includes(\"UX\") }}", "value2": true}]}}, "notesInFlow": true, "typeVersion": 1}, {"id": "e4a13d15-368f-42a5-b23a-736883e7c1aa", "name": "Code", "type": "n8n-nodes-base.code", "notes": "<PERSON><PERSON>", "position": [1220, 280], "parameters": {"jsCode": "return [\n  {\n    \"id\": \"32cb4a89-7735-497d-8862-fc66cb6383f2\",\n    \"name\": \"Promote credential test result to NDV, + run on NDV first open\",\n    \"url\": \"https://www.notion.so/Promote-credential-test-result-to-NDV-run-on-NDV-first-open-32cb4a897735497d8862fc66cb6383f2\",\n    \"property_tags\": [],\n    \"property_priority\": null,\n    \"property_type\": [\n      \"UX\",\n      \"Pain\",\n      \"UI\"\n    ],\n    \"property_deletion_time\": null,\n    \"property_complexity\": null,\n    \"property_area\": [\n      \"Credentials\",\n      \"Nodes\",\n      \"Node details view\"\n    ],\n    \"property_votes\": 2,\n    \"property_sync_time\": null,\n    \"property_consider_soon\": false,\n    \"property_last_edited\": \"2023-06-23T13:37:00.000Z\",\n    \"property_property\": \"\",\n    \"property_created_time\": \"2023-06-23T12:48:00.000Z\",\n    \"property_nodes_affected\": [],\n    \"property_linear_ticket\": null,\n    \"property_external_request_link\": null,\n    \"property_voters\": [\n      \"<EMAIL>\",\n      \"<EMAIL>\"\n    ],\n    \"property_published_time\": {\n      \"start\": \"2023-06-23T15:37:00.000+02:00\",\n      \"end\": null,\n      \"time_zone\": null\n    },\n    \"property_created_by\": [\n      \"<EMAIL>\"\n    ],\n    \"property_sub_area\": [],\n    \"property_impact\": null,\n    \"property_metric_to_improve\": [],\n    \"property_status\": null,\n    \"property_name\": \"Promote credential test result to NDV, + run on NDV first open\",\n    \"property_implementation_phase\": null,\n    \"property_timeline_status\": null\n  },\n  {\n    \"id\": \"c2ab7fe1-c7ff-4cf0-881d-a039ec90306e\",\n    \"name\": \"Add “Duplicate sticky” action\",\n    \"url\": \"https://www.notion.so/Add-Duplicate-sticky-action-c2ab7fe1c7ff4cf0881da039ec90306e\",\n    \"property_tags\": [],\n    \"property_priority\": null,\n    \"property_type\": [\n      \"Tweak\",\n      \"UX\"\n    ],\n    \"property_deletion_time\": null,\n    \"property_complexity\": null,\n    \"property_area\": [\n      \"Canvas\",\n      \"Stickies\"\n    ],\n    \"property_votes\": 3,\n    \"property_sync_time\": null,\n    \"property_consider_soon\": false,\n    \"property_last_edited\": \"2023-06-23T14:15:00.000Z\",\n    \"property_property\": \"\",\n    \"property_created_time\": \"2023-06-23T11:46:00.000Z\",\n    \"property_nodes_affected\": [],\n    \"property_linear_ticket\": null,\n    \"property_external_request_link\": null,\n    \"property_voters\": [\n      \"<EMAIL>\",\n      \"<EMAIL>\",\n      \"<EMAIL>\"\n    ],\n    \"property_published_time\": {\n      \"start\": \"2023-06-23T14:37:00.000+02:00\",\n      \"end\": null,\n      \"time_zone\": null\n    },\n    \"property_created_by\": [\n      \"<EMAIL>\"\n    ],\n    \"property_sub_area\": [],\n    \"property_impact\": null,\n    \"property_metric_to_improve\": [],\n    \"property_status\": null,\n    \"property_name\": \"Add “Duplicate sticky” action\",\n    \"property_implementation_phase\": null,\n    \"property_timeline_status\": null\n  },\n  {\n    \"id\": \"b3e99a3a-451b-4290-9a2b-5121755709d9\",\n    \"name\": \"Show “last used” (MVP: created) in cred dropdown; and sort by it\",\n    \"url\": \"https://www.notion.so/Show-last-used-MVP-created-in-cred-dropdown-and-sort-by-it-b3e99a3a451b42909a2b5121755709d9\",\n    \"property_tags\": [],\n    \"property_priority\": null,\n    \"property_type\": [\n      \"Tweak\"\n    ],\n    \"property_deletion_time\": null,\n    \"property_complexity\": null,\n    \"property_area\": [\n      \"Nodes\",\n      \"Credentials\"\n    ],\n    \"property_votes\": 2,\n    \"property_sync_time\": null,\n    \"property_consider_soon\": false,\n    \"property_last_edited\": \"2023-06-22T14:37:00.000Z\",\n    \"property_property\": \"\",\n    \"property_created_time\": \"2023-06-22T14:28:00.000Z\",\n    \"property_nodes_affected\": [],\n    \"property_linear_ticket\": null,\n    \"property_external_request_link\": null,\n    \"property_voters\": [\n      \"<EMAIL>\",\n      \"<EMAIL>\"\n    ],\n    \"property_published_time\": {\n      \"start\": \"2023-06-22T16:37:00.000+02:00\",\n      \"end\": null,\n      \"time_zone\": null\n    },\n    \"property_created_by\": [\n      \"<EMAIL>\"\n    ],\n    \"property_sub_area\": [],\n    \"property_impact\": null,\n    \"property_metric_to_improve\": [],\n    \"property_status\": null,\n    \"property_name\": \"Show “last used” (MVP: created) in cred dropdown; and sort by it\",\n    \"property_implementation_phase\": null,\n    \"property_timeline_status\": null\n  },\n  {\n    \"id\": \"a26efc3e-67fe-46d5-8d75-40f125a16e39\",\n    \"name\": \"Improve naming of Google Sheets actions (use “row” consistently)\",\n    \"url\": \"https://www.notion.so/Improve-naming-of-Google-Sheets-actions-use-row-consistently-a26efc3e67fe46d58d7540f125a16e39\",\n    \"property_tags\": [],\n    \"property_priority\": null,\n    \"property_type\": [\n      \"Tweak\",\n      \"UX\"\n    ],\n    \"property_deletion_time\": null,\n    \"property_complexity\": null,\n    \"property_area\": [\n      \"Nodes\"\n    ],\n    \"property_votes\": 1,\n    \"property_sync_time\": null,\n    \"property_consider_soon\": false,\n    \"property_last_edited\": \"2023-06-22T14:37:00.000Z\",\n    \"property_property\": \"\",\n    \"property_created_time\": \"2023-06-22T14:21:00.000Z\",\n    \"property_nodes_affected\": [\n      \"n8n-nodes-base.googleSheets\"\n    ],\n    \"property_linear_ticket\": null,\n    \"property_external_request_link\": null,\n    \"property_voters\": [\n      \"<EMAIL>\"\n    ],\n    \"property_published_time\": {\n      \"start\": \"2023-06-22T16:37:00.000+02:00\",\n      \"end\": null,\n      \"time_zone\": null\n    },\n    \"property_created_by\": [\n      \"<EMAIL>\"\n    ],\n    \"property_sub_area\": [],\n    \"property_impact\": null,\n    \"property_metric_to_improve\": [],\n    \"property_status\": null,\n    \"property_name\": \"Improve naming of Google Sheets actions (use “row” consistently)\",\n    \"property_implementation_phase\": null,\n    \"property_timeline_status\": null\n  },\n  {\n    \"id\": \"a4c72db2-1c0e-45a5-934a-eed187137bc0\",\n    \"name\": \"Change Notion trigger event “Page updated in database” to convey that it also fires for page creation\",\n    \"url\": \"https://www.notion.so/Change-Notion-trigger-event-Page-updated-in-database-to-convey-that-it-also-fires-for-page-creatio-a4c72db21c0e45a5934aeed187137bc0\",\n    \"property_tags\": [],\n    \"property_priority\": null,\n    \"property_type\": [\n      \"Tweak\"\n    ],\n    \"property_deletion_time\": null,\n    \"property_complexity\": null,\n    \"property_area\": [\n      \"Nodes\"\n    ],\n    \"property_votes\": 2,\n    \"property_sync_time\": null,\n    \"property_consider_soon\": false,\n    \"property_last_edited\": \"2023-06-22T14:37:00.000Z\",\n    \"property_property\": \"\",\n    \"property_created_time\": \"2023-06-22T14:16:00.000Z\",\n    \"property_nodes_affected\": [\n      \"n8n-nodes-base.notionTrigger\"\n    ],\n    \"property_linear_ticket\": null,\n    \"property_external_request_link\": null,\n    \"property_voters\": [\n      \"<EMAIL>\",\n      \"<EMAIL>\"\n    ],\n    \"property_published_time\": {\n      \"start\": \"2023-06-22T16:37:00.000+02:00\",\n      \"end\": null,\n      \"time_zone\": null\n    },\n    \"property_created_by\": [\n      \"<EMAIL>\"\n    ],\n    \"property_sub_area\": [],\n    \"property_impact\": null,\n    \"property_metric_to_improve\": [],\n    \"property_status\": null,\n    \"property_name\": \"Change Notion trigger event “Page updated in database” to convey that it also fires for page creation\",\n    \"property_implementation_phase\": null,\n    \"property_timeline_status\": null\n  },\n  {\n    \"id\": \"9cdaca54-eacb-4623-99e4-09e3957a75df\",\n    \"name\": \"Improve “no credential set” error in Google Sheets node\",\n    \"url\": \"https://www.notion.so/Improve-no-credential-set-error-in-Google-Sheets-node-9cdaca54eacb462399e409e3957a75df\",\n    \"property_tags\": [],\n    \"property_priority\": null,\n    \"property_type\": [\n      \"UX\",\n      \"Tweak\"\n    ],\n    \"property_deletion_time\": null,\n    \"property_complexity\": null,\n    \"property_area\": [\n      \"Nodes\",\n      \"Credentials\",\n      \"Error handling\"\n    ],\n    \"property_votes\": 1,\n    \"property_sync_time\": null,\n    \"property_consider_soon\": false,\n    \"property_last_edited\": \"2023-06-21T14:37:00.000Z\",\n    \"property_property\": \"\",\n    \"property_created_time\": \"2023-06-21T13:48:00.000Z\",\n    \"property_nodes_affected\": [\n      \"n8n-nodes-base.googleSheets\"\n    ],\n    \"property_linear_ticket\": null,\n    \"property_external_request_link\": null,\n    \"property_voters\": [\n      \"<EMAIL>\"\n    ],\n    \"property_published_time\": {\n      \"start\": \"2023-06-21T16:37:00.000+02:00\",\n      \"end\": null,\n      \"time_zone\": null\n    },\n    \"property_created_by\": [\n      \"<EMAIL>\"\n    ],\n    \"property_sub_area\": [],\n    \"property_impact\": null,\n    \"property_metric_to_improve\": [],\n    \"property_status\": null,\n    \"property_name\": \"Improve “no credential set” error in Google Sheets node\",\n    \"property_implementation_phase\": null,\n    \"property_timeline_status\": null\n  },\n  {\n    \"id\": \"dda6acab-2160-4570-b110-4f06e126af19\",\n    \"name\": \"Promote new features in docs\",\n    \"url\": \"https://www.notion.so/Promote-new-features-in-docs-dda6acab21604570b1104f06e126af19\",\n    \"property_tags\": [],\n    \"property_priority\": null,\n    \"property_type\": [\n      \"Growth\",\n      \"Monetization\"\n    ],\n    \"property_deletion_time\": null,\n    \"property_complexity\": null,\n    \"property_area\": [\n      \"Other\"\n    ],\n    \"property_votes\": 2,\n    \"property_sync_time\": null,\n    \"property_consider_soon\": false,\n    \"property_last_edited\": \"2023-06-21T09:38:00.000Z\",\n    \"property_property\": \"\",\n    \"property_created_time\": \"2023-06-21T09:03:00.000Z\",\n    \"property_nodes_affected\": [],\n    \"property_linear_ticket\": null,\n    \"property_external_request_link\": null,\n    \"property_voters\": [\n      \"<EMAIL>\",\n      \"<EMAIL>\"\n    ],\n    \"property_published_time\": {\n      \"start\": \"2023-06-21T11:37:00.000+02:00\",\n      \"end\": null,\n      \"time_zone\": null\n    },\n    \"property_created_by\": [\n      \"<EMAIL>\"\n    ],\n    \"property_sub_area\": [],\n    \"property_impact\": null,\n    \"property_metric_to_improve\": [],\n    \"property_status\": null,\n    \"property_name\": \"Promote new features in docs\",\n    \"property_implementation_phase\": null,\n    \"property_timeline_status\": null\n  }\n]"}, "notesInFlow": true, "typeVersion": 1}], "connections": {"Code": {"main": [[{"node": "Filter", "type": "main", "index": 0}]]}, "Filter": {"main": [[{"node": "Item Lists", "type": "main", "index": 0}]]}, "Notion": {"main": [[{"node": "Filter", "type": "main", "index": 0}]]}, "Item Lists": {"main": [[{"node": "<PERSON><PERSON>ck", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Code", "type": "main", "index": 0}, {"node": "Notion", "type": "main", "index": 0}]]}}}