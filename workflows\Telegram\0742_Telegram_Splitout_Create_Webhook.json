{"id": "2LFEJVoSkeZMndiM", "meta": {"instanceId": "d73587d68bda6969e611b1d966e9e2b0ae078a8d2666ab57d6d9dcd379a0ce36", "templateCredsSetupCompleted": true}, "name": "YT AI News Playlist Creator/AI News Form Updater", "tags": [], "nodes": [{"id": "a871e87e-dc02-4364-83b3-fe378ca60687", "name": "Read Channel Names", "type": "n8n-nodes-base.googleSheets", "position": [860, 100], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "list", "value": *********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1RNah4ZZsLxflQXvMq8AEn3BFpscOC2ygMZ1dPTlk-Kk/edit#gid=*********", "cachedResultName": "AI News Channels"}, "documentId": {"__rl": true, "mode": "list", "value": "1RNah4ZZsLxflQXvMq8AEn3BFpscOC2ygMZ1dPTlk-Kk", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1RNah4ZZsLxflQXvMq8AEn3BFpscOC2ygMZ1dPTlk-Kk/edit?usp=drivesdk", "cachedResultName": "Media Links"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "hVq7KRYH68lYmtEB", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "bcc83a11-e7e1-4bcb-a054-a2f0cc26c5f0", "name": "Get Videos", "type": "n8n-nodes-base.httpRequest", "position": [1020, 100], "parameters": {"url": "https://www.googleapis.com/youtube/v3/search", "options": {}, "sendQuery": true, "queryParameters": {"parameters": [{"name": "part", "value": "snippet"}, {"name": "publishedAfter", "value": "={{ $now.minus(1, 'day') }}"}, {"name": "maxResults", "value": "5"}, {"name": "channel_id", "value": "={{ $('Read Channel Names').item.json['Channel Id'] }}"}, {"name": "order", "value": "date"}, {"name": "key", "value": "AddYourAPIKeyHere"}]}}, "typeVersion": 4.2}, {"id": "6da4a908-1705-4d3a-8f1a-aa73e36866c7", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [1160, 100], "parameters": {"options": {}, "fieldToSplitOut": "items"}, "typeVersion": 1}, {"id": "1f7ab323-fb52-4a41-bf71-9594e4d1c78d", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [140, 0], "parameters": {"width": 220, "height": 260, "content": "## Initiation\nThis section starts the workflow sets the time."}, "typeVersion": 1}, {"id": "e17f2b65-3320-46aa-b360-2366691053cd", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [800, 0], "parameters": {"color": 5, "width": 660, "height": 260, "content": "## Getting the Videos\nThis section grabs the videos."}, "typeVersion": 1}, {"id": "d950c171-0993-4e51-8942-18dca557c70a", "name": "Create Playlist", "type": "n8n-nodes-base.youTube", "position": [440, 100], "parameters": {"title": "={{ $today.format('yyLLdd') }} AI News", "options": {}, "resource": "playlist", "operation": "create"}, "credentials": {"youTubeOAuth2Api": {"id": "alrF3L4QeYVd4Ckn", "name": "YouTube account"}}, "typeVersion": 1}, {"id": "1d292e23-4efc-4377-aacf-8c5b9c54e524", "name": "Delete Old Playlist", "type": "n8n-nodes-base.youTube", "position": [580, -220], "parameters": {"options": {}, "resource": "playlist", "operation": "delete", "playlistId": "={{ $json['New Playlist ID'] }}"}, "credentials": {"youTubeOAuth2Api": {"id": "alrF3L4QeYVd4Ckn", "name": "YouTube account"}}, "typeVersion": 1}, {"id": "26ddb0d4-4ae8-485c-8909-00c70230ce76", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [400, -340], "parameters": {"color": 3, "width": 380, "height": 280, "content": "## Delete Yesterday's Playlist\nThis section deletes the playlist created yesterday. (do not include this on your first run; or, your workflow will stop)"}, "typeVersion": 1}, {"id": "c4756eb6-c080-48dd-9941-511fbf405fbe", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [400, 0], "parameters": {"color": 4, "width": 360, "height": 260, "content": "## Create New AI News Playlist\nThis section creates today's playlist."}, "typeVersion": 1}, {"id": "33308ef0-fb86-4bce-a81f-0c5ddc4215a1", "name": "YouTube", "type": "n8n-nodes-base.youTube", "position": [1580, 100], "parameters": {"options": {}, "videoId": "={{ $('Split Out').item.json.id.videoId }}", "resource": "playlistItem", "playlistId": "={{ $('Create Playlist').item.json.id }}"}, "credentials": {"youTubeOAuth2Api": {"id": "alrF3L4QeYVd4Ckn", "name": "YouTube account"}}, "typeVersion": 1}, {"id": "2db4a5e2-f177-4c45-a890-8bf140971882", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1500, 0], "parameters": {"color": 6, "width": 280, "height": 260, "content": "## Add Videos to Playlist\nThis section adds videos to the playlist created today."}, "typeVersion": 1}, {"id": "7c2945de-9912-4db0-bd4f-6c222b8ebeaf", "name": "Filter Out Upcoming", "type": "n8n-nodes-base.filter", "position": [1300, 100], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "or", "conditions": [{"id": "8884d2e9-b06d-4347-9635-846d7dea168f", "operator": {"type": "string", "operation": "notEquals"}, "leftValue": "={{ $json.snippet.liveBroadcastContent }}", "rightValue": "upcoming"}]}}, "typeVersion": 2.2}, {"id": "d822a00b-acfc-4838-ae50-37103e581cbf", "name": "Save Playlist ID", "type": "n8n-nodes-base.googleSheets", "position": [600, 100], "parameters": {"columns": {"value": {"Playlist Group": "AI News", "New Playlist ID": "={{ $json.id }}"}, "schema": [{"id": "Playlist Group", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Playlist Group", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "New Playlist ID", "type": "string", "display": true, "removed": false, "required": false, "displayName": "New Playlist ID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["Playlist Group"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": **********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1RNah4ZZsLxflQXvMq8AEn3BFpscOC2ygMZ1dPTlk-Kk/edit#gid=**********", "cachedResultName": "PlaylistId"}, "documentId": {"__rl": true, "mode": "list", "value": "1RNah4ZZsLxflQXvMq8AEn3BFpscOC2ygMZ1dPTlk-Kk", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1RNah4ZZsLxflQXvMq8AEn3BFpscOC2ygMZ1dPTlk-Kk/edit?usp=drivesdk", "cachedResultName": "Media Links"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "hVq7KRYH68lYmtEB", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "bbbcbe5b-5594-44cb-bb1d-897498b61810", "name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [440, -220], "parameters": {"options": {}, "filtersUI": {"values": [{"lookupValue": "AI News", "lookupColumn": "Playlist Group"}]}, "sheetName": {"__rl": true, "mode": "list", "value": **********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1RNah4ZZsLxflQXvMq8AEn3BFpscOC2ygMZ1dPTlk-Kk/edit#gid=**********", "cachedResultName": "PlaylistId"}, "documentId": {"__rl": true, "mode": "list", "value": "1RNah4ZZsLxflQXvMq8AEn3BFpscOC2ygMZ1dPTlk-Kk", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1RNah4ZZsLxflQXvMq8AEn3BFpscOC2ygMZ1dPTlk-Kk/edit?usp=drivesdk", "cachedResultName": "Media Links"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "hVq7KRYH68lYmtEB", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "20d814e1-4f1e-4313-949b-961556cd40bf", "name": "Telegram", "type": "n8n-nodes-base.telegram", "position": [1880, 100], "webhookId": "5007b956-14f6-4275-ab8d-2c47050b6007", "parameters": {"text": "Your AI News YT Playlist has been updated.", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "FeG2VD9QbvSMvLxW", "name": "Dinar Newscaster"}}, "typeVersion": 1.2}, {"id": "b0cfab69-ad82-4d65-8106-0bd4b23dfdb3", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1820, 0], "parameters": {"color": 6, "width": 280, "height": 260, "content": "## Notification of Completion (optional)"}, "typeVersion": 1}, {"id": "57ef08c8-b7ca-4af6-963a-67a3d2b80176", "name": "0715 Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [180, 100], "parameters": {"rule": {"interval": [{"triggerAtHour": 7, "triggerAtMinute": 15}]}}, "typeVersion": 1.2}, {"id": "d3003e8a-aa46-437e-b246-b9030578ea49", "name": "Get Channels", "type": "n8n-nodes-base.httpRequest", "position": [800, -640], "parameters": {"url": "https://www.googleapis.com/youtube/v3/search", "options": {}, "sendQuery": true, "queryParameters": {"parameters": [{"name": "q", "value": "={{ $json['Channel User Name'] }}"}, {"name": "type", "value": "channel"}, {"name": "maxResults", "value": "1"}, {"name": "part", "value": "snippet"}, {"name": "key", "value": "AIzaSyARU7upVG5hzoaMHIMaBEXjcYtayo8vPJ4"}]}}, "typeVersion": 4.2}, {"id": "fde3bac7-77be-4322-9b74-2cb7b9ddd17c", "name": "Update Google Sheet", "type": "n8n-nodes-base.googleSheets", "position": [1000, -640], "parameters": {"columns": {"value": {"Link": "=https://www.youtube.com/{{ $('Read Channel Names1').item.json['Channel User Name'] }}", "Channel Id": "={{ $json.items[0].id.channelId }}", "row_number": "={{ $('Read Channel Names1').item.json.row_number }}", "Channel Name": "={{ $json.items[0].snippet.channelTitle }}"}, "schema": [{"id": "Channel Name", "type": "string", "display": true, "required": false, "displayName": "Channel Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Link", "type": "string", "display": true, "required": false, "displayName": "Link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Channel Id", "type": "string", "display": true, "required": false, "displayName": "Channel Id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Channel User Name", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Channel User Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["row_number"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": *********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1RNah4ZZsLxflQXvMq8AEn3BFpscOC2ygMZ1dPTlk-Kk/edit#gid=*********", "cachedResultName": "AI News Channels"}, "documentId": {"__rl": true, "mode": "list", "value": "1RNah4ZZsLxflQXvMq8AEn3BFpscOC2ygMZ1dPTlk-Kk", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1RNah4ZZsLxflQXvMq8AEn3BFpscOC2ygMZ1dPTlk-Kk/edit?usp=drivesdk", "cachedResultName": "Media Links"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "hVq7KRYH68lYmtEB", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "2b1e067b-436a-4536-ad9f-c55862d496c9", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [440, -640], "parameters": {}, "typeVersion": 1}, {"id": "1dd572c5-6762-40a0-88aa-d6a9fa2ca0a3", "name": "Read Channel Names1", "type": "n8n-nodes-base.googleSheets", "position": [620, -640], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "list", "value": *********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1RNah4ZZsLxflQXvMq8AEn3BFpscOC2ygMZ1dPTlk-Kk/edit#gid=*********", "cachedResultName": "AI News Channels"}, "documentId": {"__rl": true, "mode": "list", "value": "1RNah4ZZsLxflQXvMq8AEn3BFpscOC2ygMZ1dPTlk-Kk", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1RNah4ZZsLxflQXvMq8AEn3BFpscOC2ygMZ1dPTlk-Kk/edit?usp=drivesdk", "cachedResultName": "Media Links"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "hVq7KRYH68lYmtEB", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "43466e82-dc55-4d4e-a6ff-ff2ed977fb3c", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [380, -740], "parameters": {"width": 820, "height": 260, "content": "## Create your Channel List\nThis section needs to be put into it's own workflow: this workflow gathers information needed to gather videos for the playlist.  This workflow only needs to be run when a new channel name is added to the Google Sheet."}, "typeVersion": 1}, {"id": "149373af-ad35-49bc-b751-6ac919d218b0", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [1260, -740], "parameters": {"width": 820, "height": 700, "content": "## Instructions\n1. To set this up, you need to create a Google Sheet with the following headings in line 1:\n\n   Channel User Name\n   Channel Name\n   Channel Link\n   Channel ID\n\n2. Copy the 'Create your Channel List' into it's own workflow and link the Sheets links to your new sheet.\n\n3. To get the 'Create your Channel List' to work, you need to visit each channel's page that you want included in your playlist; you need to get the \"@\" name of the channel and add it to the 'Channel User Name' column of your Google Sheet.\n\n   For example: if you wanted to include this channel: Recruit Training Videos - Corporal Stock \n   You would look for this name, to add to the next available row of the 'Channel User Name' column: @CorporalStock\n\n4. Once you add all Channel User Names, run the 'Create your Channel list workflow, and it will fill in the remaining details.\n\n5. Now the 'YT Playlist Creator' can be run; but for the first time, disconnect the 'Delete Yesterday's Playlist' leg, or the workflow will error and stop (because there is no 'Yesterday's Playlist'.\n\nNote: this was made to create a playlist every day, delete yesterday's playlist, and only get the last 8 videos posted within the last 24 hours.  I choose to put the date (YYMMDD format) in front of the playlist, to ensure that it doesn't conflict with another playlist.\n\n   Also, I have it notifying me in Telegram, so I know that the new playlist is posted."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"timezone": "Asia/Manila", "callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1"}, "versionId": "c154607b-f3b1-4f41-bf77-faec36ce3716", "connections": {"YouTube": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Filter Out Upcoming", "type": "main", "index": 0}]]}, "Get Videos": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "0715 Trigger": {"main": [[{"node": "Create Playlist", "type": "main", "index": 0}, {"node": "Google Sheets", "type": "main", "index": 0}]]}, "Get Channels": {"main": [[{"node": "Update Google Sheet", "type": "main", "index": 0}]]}, "Google Sheets": {"main": [[{"node": "Delete Old Playlist", "type": "main", "index": 0}]]}, "Create Playlist": {"main": [[{"node": "Save Playlist ID", "type": "main", "index": 0}]]}, "Save Playlist ID": {"main": [[{"node": "Read Channel Names", "type": "main", "index": 0}]]}, "Read Channel Names": {"main": [[{"node": "Get Videos", "type": "main", "index": 0}]]}, "Filter Out Upcoming": {"main": [[{"node": "YouTube", "type": "main", "index": 0}]]}, "Read Channel Names1": {"main": [[{"node": "Get Channels", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Read Channel Names1", "type": "main", "index": 0}]]}}}