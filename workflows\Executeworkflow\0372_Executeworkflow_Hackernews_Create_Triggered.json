{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "b6836974-0d4b-482b-8a8a-c00f229f1136", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [100, 500], "parameters": {"color": 7, "width": 150, "height": 293, "content": "### Replace me\nwith any other service, e.g. fetching your own data"}, "typeVersion": 1}, {"id": "c0b8b657-24b8-4c0b-bfe9-d4fe2075dbe5", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-180, 420], "parameters": {"color": 7, "width": 927.5, "height": 406.875, "content": "### Sub-workflow: Custom tool\nThis can be called by the agent above. This example fetches the top 50 posts ever on Hacker News"}, "typeVersion": 1}, {"id": "a1dab7b1-b028-44a2-ab55-d8edee62e261", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-180, -120], "parameters": {"color": 7, "width": 927.5, "height": 486.5625, "content": "### Main workflow: AI agent using custom tool\nTry it out by clicking 'Chat' and entering 'What is the 5th most popular post ever on Hacker News?'"}, "typeVersion": 1}, {"id": "84d91346-6b1d-4808-a6b3-ce212cd122d0", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-340, -20], "parameters": {"width": 185.9375, "height": 218, "content": "## Try me out\n\nClick the 'Chat' button and enter:\n\n_What is the 5th most popular post ever on Hacker News?_"}, "typeVersion": 1}, {"id": "50c7208d-d2dc-4380-9f81-6d7f4dee40b3", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-40, 20], "webhookId": "34a31e43-46b8-4c4b-a47e-ef5ad6e66af0", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "6170523b-ac2d-4541-9186-0f2932829a36", "name": "Custom tool to call the wf below1", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [480, 220], "parameters": {"name": "hn_tool", "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "description": "Returns a list of the most popular posts ever on Hacker News, in json format", "workflowInputs": {"value": {}, "schema": [], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2}, {"id": "3f40434e-4055-4d8f-be26-051da2911aa1", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-120, 640], "parameters": {"inputSource": "passthrough"}, "typeVersion": 1.1}, {"id": "4a6cf195-6862-4007-b791-d021583a771e", "name": "Hacker News", "type": "n8n-nodes-base.hackerNews", "position": [120, 640], "parameters": {"limit": 50, "resource": "all", "additionalFields": {}}, "typeVersion": 1}, {"id": "03095e43-0d6e-47c0-8936-dceb2fb0dfb1", "name": "Clean up data", "type": "n8n-nodes-base.set", "position": [340, 640], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "171d5a66-fc72-42ab-9f6c-0c137f6b3415", "name": "title", "type": "string", "value": "={{ $json._highlightResult.title.value }}"}, {"id": "e6662f7e-8e44-43d6-8e8b-6162bfec34bc", "name": "points", "type": "string", "value": "={{ $json.points }}"}, {"id": "7415a9f0-7cd4-4bad-bbcf-1903520af155", "name": "url", "type": "string", "value": "={{ $json.url }}"}, {"id": "8b0c67a6-89b0-40de-85f2-b80c9298d81f", "name": "created_at", "type": "string", "value": "={{ $json.created_at }}"}, {"id": "b7847fbb-4428-4a5b-980e-08e6069b0ac4", "name": "author", "type": "string", "value": "={{ $json.author }}"}]}}, "typeVersion": 3.4}, {"id": "50ee96c0-36d6-4774-b5cf-b653f5b56868", "name": "Stringify", "type": "n8n-nodes-base.code", "position": [560, 640], "parameters": {"jsCode": "return {\n  'response': JSON.stringify($input.all().map(x => x.json))\n}"}, "typeVersion": 2}, {"id": "ba221fc3-5249-4295-b64e-2c7370c6dad4", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [260, 20], "parameters": {"options": {}}, "typeVersion": 1.8}, {"id": "e8c45847-cc26-47b3-898c-8063b9c4b3a9", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [220, 200], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}], "pinData": {}, "connections": {"Hacker News": {"main": [[{"node": "Clean up data", "type": "main", "index": 0}]]}, "Clean up data": {"main": [[{"node": "Stringify", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Custom tool to call the wf below1": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Hacker News", "type": "main", "index": 0}]]}}}