{"meta": {"instanceId": "dfdeafd1c3ed2ee08eeab8c2fa0c3f522066931ed8138ccd35dc20a1e69decd3"}, "nodes": [{"id": "f60e3d5f-4da5-4201-8c78-00f4f410b397", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [600, 300], "parameters": {}, "typeVersion": 1}, {"id": "724f285b-723e-4452-81a6-c066c6b6a0e4", "name": "TableName", "type": "n8n-nodes-base.set", "position": [780, 300], "parameters": {"values": {"string": [{"name": "TableName", "value": "concerts2"}]}, "options": {}}, "typeVersion": 1}, {"id": "637356f0-fcde-4555-982c-a28159ce6885", "name": "LoadMySQLData", "type": "n8n-nodes-base.mySql", "position": [960, 300], "parameters": {"query": "=SELECT * FROM {{ $json[\"TableName\"] }}", "operation": "execute<PERSON>uery"}, "credentials": {"mySql": {"id": "46", "name": "MySQL n8n articles"}}, "typeVersion": 1}, {"id": "b3270629-35de-4746-aa51-293e7d20660d", "name": "SaveSpreadsheet", "type": "n8n-nodes-base.spreadsheetFile", "position": [1160, 300], "parameters": {"options": {"fileName": "={{ $node[\"TableName\"].json[\"TableName\"] }}.{{ $parameter[\"fileFormat\"] }}", "headerRow": true, "sheetName": "={{ $node[\"TableName\"].json[\"TableName\"] }}"}, "operation": "toFile", "fileFormat": "xlsx"}, "typeVersion": 1}, {"id": "a7d04632-f47a-40e5-986e-1acf0b0af7c7", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1100, 157], "parameters": {"width": 450, "height": 305, "content": "## Save SQL table as a binary XLSX file\n### You can send it via e-mail, upload to the file storage or download on your computer.\n### Just connect one or two extra n8n Nodes here!"}, "typeVersion": 1}], "connections": {"TableName": {"main": [[{"node": "LoadMySQLData", "type": "main", "index": 0}]]}, "LoadMySQLData": {"main": [[{"node": "SaveSpreadsheet", "type": "main", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "TableName", "type": "main", "index": 0}]]}}}