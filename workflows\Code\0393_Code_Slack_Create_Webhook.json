{"meta": {"instanceId": "257476b1ef58bf3cb6a46e65fac7ee34a53a5e1a8492d5c6e4da5f87c9b82833", "templateId": "2070"}, "nodes": [{"id": "99daceb3-fb96-4324-ac87-4ffef333dc81", "name": "Get Automated Task", "type": "n8n-nodes-base.airtable", "position": [1040, 660], "parameters": {"id": "={{ $('Entered View  \"First Task - Create Task\"').item.json[\"id\"] }}", "base": {"__rl": true, "mode": "id", "value": "={{ $item(\"0\").$node[\"Airtable Base ID's\"].json[\"base_id\"] }}"}, "table": {"__rl": true, "mode": "id", "value": "={{ $item(\"0\").$node[\"Airtable Base ID's\"].json[\"table_automate_id\"] }}"}, "options": {}, "operation": "get"}, "typeVersion": 2}, {"id": "4a29d735-5039-4935-9803-66df6a67e590", "name": "Create Task", "type": "n8n-nodes-base.httpRequest", "position": [2140, 660], "parameters": {"url": "=https://api.airtable.com/v0/{{ $item(\"0\").$node[\"Airtable Base ID's\"].json[\"base_id\"] }}/{{ $item(\"0\").$node[\"Airtable Base ID's\"].json[\"table_task_id\"] }}", "method": "POST", "options": {}, "jsonBody": "={\n    \"records\": [\n        {\n            \"fields\": {\n                \"Status\": \"Todo\",\n                \"Task Name\": \"{{ $item(\"0\").$node[\"Get Task Template\"].json[\"Template Name\"] }}\",\n                \"Task Description\": \"{{ $('Get Task Template').item.json[\"Description\"].replace(/\\r?\\n/g, \"\\\\n\") }}\",\n                \"Kickoff Date\": \"{{ $('Calculate Dates').item.json[\"Kickoff Date\"] }}\",\n                \"Soft Due Date\": \"{{ $('Calculate Dates').item.json[\"Soft Due Date\"] }}\",\n                \"Hard Due Date\": \"{{ $('Calculate Dates').item.json[\"Hard Due Date\"] }}\",\n                \"Assignee\": [\n                    \"{{ $('Get Assignee').item.json[\"id\"] }}\"\n                ],\n                \"Template\": [\n                    \"{{ $('Get Task Template').item.json[\"id\"] }}\"\n                ],\n                \"Client\": [\n                    \"{{ $('Get Client').item.json[\"id\"] }}\"\n                ]\n            }\n        }\n    ]\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "nodeCredentialType": "airtableTokenApi"}, "typeVersion": 4.1}, {"id": "4d5e25f4-395f-4c47-8181-7dc7191b3b88", "name": "Get Task Template", "type": "n8n-nodes-base.airtable", "position": [1240, 660], "parameters": {"id": "={{ $item(\"0\").$node[\"Get Automated Task\"].json[\"Template\"][\"0\"] }}", "base": {"__rl": true, "mode": "id", "value": "={{ $item(\"0\").$node[\"Airtable Base ID's\"].json[\"base_id\"] }}"}, "table": {"__rl": true, "mode": "id", "value": "={{ $item(\"0\").$node[\"Airtable Base ID's\"].json[\"table_template_id\"] }}"}, "options": {}, "operation": "get"}, "typeVersion": 2}, {"id": "fe7a3c49-738b-46d2-9276-2398dff3a449", "name": "<PERSON>signee", "type": "n8n-nodes-base.airtable", "position": [1460, 660], "parameters": {"id": "={{ $item(\"0\").$node[\"Get Automated Task\"].json[\"Assigned Team Member\"][\"0\"] }}", "base": {"__rl": true, "mode": "id", "value": "={{ $item(\"0\").$node[\"Airtable Base ID's\"].json[\"base_id\"] }}"}, "table": {"__rl": true, "mode": "id", "value": "={{ $item(\"0\").$node[\"Airtable Base ID's\"].json[\"table_team_id\"] }}"}, "options": {}, "operation": "get"}, "typeVersion": 2}, {"id": "2b2e96a9-dd25-4d5f-a4db-aafd29fff907", "name": "Get Client", "type": "n8n-nodes-base.airtable", "position": [1660, 660], "parameters": {"id": "={{ $item(\"0\").$node[\"Get Automated Task\"].json[\"Client\"][\"0\"] }}", "base": {"__rl": true, "mode": "id", "value": "={{ $item(\"0\").$node[\"Airtable Base ID's\"].json[\"base_id\"] }}"}, "table": {"__rl": true, "mode": "id", "value": "={{ $item(\"0\").$node[\"Airtable Base ID's\"].json[\"table_clients_id\"] }}"}, "options": {}, "operation": "get"}, "typeVersion": 2}, {"id": "504b3d7a-339c-42b7-b2ef-4a180ccc0f78", "name": "Calculate Dates", "type": "n8n-nodes-base.code", "position": [1880, 660], "parameters": {"jsCode": "// Retrieve values from the previous node\nconst firstTaskCreated = $item(\"0\").$node[\"Get Automated Task\"].json[\"First Task Created\"];\nconst startDate = $item(\"0\").$node[\"Get Automated Task\"].json[\"Start Date\"];\nconst lastTaskCreated = $item(\"0\").$node[\"Get Automated Task\"].json[\"Last Task Created\"];\nconst timeValue = $item(\"0\").$node[\"Get Automated Task\"].json[\"Time Value\"];\nconst daysForSoftDueDate = $item(\"0\").$node[\"Get Automated Task\"].json[\"Days for Soft Due Date\"];\n\n// Helper function to add days to a date\nfunction addDays(date, days) {\n  let result = new Date(date);\n  result.setDate(result.getDate() + days);\n  return result;\n}\n\n// Helper function to format date in MM/DD/YYYY\nfunction formatDate(date) {\n  return (date.getMonth() + 1) + '/' + date.getDate() + '/' + date.getFullYear();\n}\n\n// Calculate Kickoff Date\nlet kickoffDate;\nif (firstTaskCreated === \"false\") {\n  kickoffDate = new Date(startDate);\n} else {\n  kickoffDate = addDays(new Date(lastTaskCreated), timeValue);\n}\n\n// Calculate Soft Due Date\nconst softDueDate = addDays(kickoffDate, timeValue - daysForSoftDueDate);\n\n// Calculate Hard Due Date\nconst hardDueDate = addDays(kickoffDate, timeValue);\n\n// Get today's date\nconst today = new Date();\n\n// Calculate Next Task Creation Date (Hard Due Date minus 1 day)\nconst nextTaskCreationDate = addDays(hardDueDate, -1);\n\n// Prepare the output\nreturn [{\n  json: {\n    \"Kickoff Date\": formatDate(kickoffDate),\n    \"Soft Due Date\": formatDate(softDueDate),\n    \"Hard Due Date\": formatDate(hardDueDate),\n    \"Today\": formatDate(today),\n    \"Next Task Creation Date\": formatDate(nextTaskCreationDate)\n  }\n}];\n"}, "typeVersion": 2}, {"id": "ba33b165-57cf-4e9a-8f86-52b248333a04", "name": "Update Automated Record", "type": "n8n-nodes-base.httpRequest", "position": [2420, 660], "parameters": {"url": "=https://api.airtable.com/v0/{{ $item(\"0\").$node[\"Airtable Base ID's\"].json[\"base_id\"] }}/{{ $item(\"0\").$node[\"Airtable Base ID's\"].json[\"table_automate_id\"] }}", "method": "PATCH", "options": {}, "jsonBody": "={\n    \"records\": [\n        {\n            \"id\": \"{{ $item(\"0\").$node[\"Get Automated Task\"].json[\"id\"] }}\",\n            \"fields\": {\n                \"First Task Created\": \"true\",\n                \"Last Task Created\": \"{{ $('Calculate Dates').item.json[\"Today\"] }}\",\n                \"Next Task Creation Date\": \"{{ $('Calculate Dates').item.json[\"Next Task Creation Date\"] }}\"\n            }\n        }\n    ]\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "nodeCredentialType": "airtableTokenApi"}, "typeVersion": 4.1}, {"id": "c2893a32-1b48-4974-8216-7fee6e6dd576", "name": "Notify Assignee", "type": "n8n-nodes-base.slack", "disabled": true, "position": [2680, 660], "parameters": {"select": "channel", "channelId": {"__rl": true, "mode": "list", "value": ""}, "otherOptions": {}}, "typeVersion": 2.1}, {"id": "734d5319-2f55-4f21-b4d3-dae9e9adbf19", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [380, 240], "parameters": {"width": 577.8258549588782, "height": 149.31896574204097, "content": "## Resources\nThe Airtable template can be found here - https://www.airtable.com/universe/expDZ9rbZ9ZwZuTmX/recurring-tasks-automation"}, "typeVersion": 1}, {"id": "fed9b237-3ff4-4c70-8e55-081b02f36d61", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1860, 520], "parameters": {"width": 519.2937872252622, "height": 478.35585536865557, "content": "### These nodes should be adapted to your custom Airtable Base. These nodes and the field names correspond to the template fields, but will not work if your tables field names, field type are different"}, "typeVersion": 1}, {"id": "038f9b3d-60ca-4196-8a48-009d8a696a33", "name": "Entered View  \"First Task - Create Task\"", "type": "n8n-nodes-base.airtableTrigger", "position": [500, 660], "parameters": {"baseId": {"__rl": true, "mode": "id", "value": "appPL3AkBc0iw5Z3x"}, "tableId": {"__rl": true, "mode": "id", "value": "tblp4KpAUGY9RqbMj"}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerField": "updated_at", "authentication": "airtableTokenApi", "additionalFields": {"viewId": "viwsays8X5yn5Xl7g"}}, "typeVersion": 1}, {"id": "140e4d5d-7d2a-4e3a-bf3b-de993c8a65a1", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [960, 240], "parameters": {"width": 408.1448240473296, "height": 146.75862096834132, "content": "## Walkthrough and Overview\n\n### https://www.youtube.com/watch?v=if3wr0tY-gk"}, "typeVersion": 1}, {"id": "263c7619-763d-476f-8fe4-d79edfa874bc", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [0, 520], "parameters": {"width": 400.220686283071, "height": 575.7793015940845, "content": "## Setup Checklist\n\n1. Go to the Airtable Template and copy the latest version of the base\n2. Go to the `Automate` table and open the view `First Task - Create Task`. From here, copy the BaseId, TableId and ViewId into the trigger. Make that the field \"updated_at\" is visible in the \"First Task - Create Task\" View\n3. Input your Airtable Id's in the second node \"Airtable Base ID's\"\n\n### The setup is now complete, now for testing:\n\n1. Go to the Airtable Interface Page called \"Automate a Template ⚙️\" and create an entry utilizing the dummy data.\n2. **Important** If you want to test the automation live, the Start Date should be set to TODAY. Please ensure your n8n automation is live."}, "typeVersion": 1}, {"id": "709f91d9-6028-41c3-91a1-2335b31e94b2", "name": "Airtable Base ID's", "type": "n8n-nodes-base.set", "position": [720, 660], "parameters": {"fields": {"values": [{"name": "base_id", "stringValue": "appVtUCDmP7LnG8bV"}, {"name": "table_task_id", "stringValue": "tblbkEKwqEAuY6kBW"}, {"name": "table_template_id", "stringValue": "tbl7f8iV3qLUvirPX"}, {"name": "table_clients_id", "stringValue": "tbljzJBlyrHwzEXXK"}, {"name": "table_team_id", "stringValue": "tblKlBfYzCWVzY0Mh"}, {"name": "table_automate_id", "stringValue": "tblvMBrTFj5CI1kUH"}]}, "include": "none", "options": {}}, "typeVersion": 3.2}], "pinData": {}, "connections": {"Get Client": {"main": [[{"node": "Calculate Dates", "type": "main", "index": 0}]]}, "Create Task": {"main": [[{"node": "Update Automated Record", "type": "main", "index": 0}]]}, "Get Assignee": {"main": [[{"node": "Get Client", "type": "main", "index": 0}]]}, "Calculate Dates": {"main": [[{"node": "Create Task", "type": "main", "index": 0}]]}, "Get Task Template": {"main": [[{"node": "<PERSON>signee", "type": "main", "index": 0}]]}, "Airtable Base ID's": {"main": [[{"node": "Get Automated Task", "type": "main", "index": 0}]]}, "Get Automated Task": {"main": [[{"node": "Get Task Template", "type": "main", "index": 0}]]}, "Update Automated Record": {"main": [[{"node": "Notify Assignee", "type": "main", "index": 0}]]}, "Entered View  \"First Task - Create Task\"": {"main": [[{"node": "Airtable Base ID's", "type": "main", "index": 0}]]}}}