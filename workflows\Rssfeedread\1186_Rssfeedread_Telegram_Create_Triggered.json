{"id": "8v4dynjkHSLVGJSG", "meta": {"instanceId": "a5283507e1917a33cc3ae615b2e7d5ad2c1e50955e6f831272ddd5ab816f3fb6", "templateCredsSetupCompleted": true}, "name": "Crypto News & Sentiment", "tags": [], "nodes": [{"id": "e10ed4da-ab3e-4ff0-b489-a3ed9e88e042", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-1340, 1520], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "yUizd8t0sD5wMYVG", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "edb76989-766a-43e6-bf49-3896a1d257dd", "name": "Set Query", "type": "n8n-nodes-base.set", "position": [-780, 1340], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9128e9e7-d1b8-4e89-8422-849b8dd519d8", "name": "query", "type": "string", "value": "={{ $json.output }}"}, {"id": "b6adc989-7d3c-4dbb-a659-603591cf1f58", "name": "sessionId", "type": "string", "value": "={{ $('Adds the sessionId').item.json.sessionId }}"}]}}, "typeVersion": 3.4}, {"id": "0ef0571a-d0c2-4f05-898e-1aed56b02d56", "name": "Crypto News & Sentiment Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-1280, 1340], "parameters": {"text": "={{ $('Send Crypto or Company Name for Analysis').item.json.message.text }}", "options": {"systemMessage": "Your job is to analyze the keyword of the question and output it as a single word."}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "8f7859fa-e80c-4c0e-b8b5-5593d5108f5f", "name": "RSS Cointelegraph", "type": "n8n-nodes-base.rssFeedRead", "position": [-160, 500], "parameters": {"url": "https://cointelegraph.com/rss", "options": {}}, "typeVersion": 1.1}, {"id": "5ce9dc61-de8e-4b3a-a0e2-4c077a28e0d4", "name": "RSS Bitcoinmagazine", "type": "n8n-nodes-base.rssFeedRead", "position": [-160, 660], "parameters": {"url": "https://bitcoinmagazine.com/.rss/full/", "options": {}}, "typeVersion": 1.1}, {"id": "d2491728-8a51-4384-adfa-8636f8d8cacd", "name": "RSS Coindesk", "type": "n8n-nodes-base.rssFeedRead", "position": [-160, 820], "parameters": {"url": "https://www.coindesk.com/arc/outboundfeeds/rss/", "options": {}}, "typeVersion": 1.1}, {"id": "60b17a68-b7b0-444f-8bbc-4d9ccfba6892", "name": "RSS Bitcoinist", "type": "n8n-nodes-base.rssFeedRead", "position": [-160, 980], "parameters": {"url": "https://bitcoinist.com/feed/", "options": {}}, "typeVersion": 1.1}, {"id": "82b50de0-68a7-4660-b6d9-fa01922c2492", "name": "RSS Newsbtc", "type": "n8n-nodes-base.rssFeedRead", "position": [-160, 1140], "parameters": {"url": "https://www.newsbtc.com/feed/", "options": {}}, "typeVersion": 1.1}, {"id": "90f9a5be-8eb2-4073-9c18-50478ba4890b", "name": "RSS Cryptopotato", "type": "n8n-nodes-base.rssFeedRead", "position": [-160, 1500], "parameters": {"url": "https://cryptopotato.com/feed/", "options": {}}, "typeVersion": 1.1}, {"id": "6428c19d-c66c-4577-b6ca-8c0c82a757fa", "name": "RSS 99bitcoins", "type": "n8n-nodes-base.rssFeedRead", "position": [-160, 1680], "parameters": {"url": "https://99bitcoins.com/feed/", "options": {}}, "typeVersion": 1.1}, {"id": "b2fc6584-cd64-4b90-91cf-b9bfdc483203", "name": "RSS Cryptobriefing", "type": "n8n-nodes-base.rssFeedRead", "position": [-160, 1860], "parameters": {"url": "https://cryptobriefing.com/feed/", "options": {}}, "typeVersion": 1.1}, {"id": "8be5859b-bb82-4ece-9aa5-5bfbc27e4cca", "name": "RSS Crypto.news", "type": "n8n-nodes-base.rssFeedRead", "position": [-160, 2020], "parameters": {"url": "https://crypto.news/feed/", "options": {}}, "typeVersion": 1.1}, {"id": "b5f393de-a3da-47a4-8a19-ebf53c29051d", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [520, 1180], "parameters": {"numberInputs": 10}, "typeVersion": 3.1}, {"id": "ce4da50f-ecff-4d13-a90d-e62db6bc3ef9", "name": "Filter by Query", "type": "n8n-nodes-base.code", "position": [760, 1320], "parameters": {"jsCode": "const term = $node[\"Set Query\"].json.query.toLowerCase();\nreturn items.filter(item => {\n  const j            = item.json;\n  const title        = (j.title || \"\").toLowerCase();\n  const snippet      = (j.contentSnippet || j.description || \"\").toLowerCase();\n  const fullContent  = (j.content || \"\").toLowerCase();\n  return title.includes(term)\n      || snippet.includes(term)\n      || fullContent.includes(term);\n});"}, "typeVersion": 2}, {"id": "566e842f-8e2c-491c-87b5-5f1d322a9de8", "name": "Build Prompt", "type": "n8n-nodes-base.code", "position": [1080, 1320], "parameters": {"jsCode": "const q   = $node[\"Set Query\"].json.query;\nconst list = items\n  .map(i => `- ${i.json.title} (${i.json.link})`)\n  .join(\"\\n\");\nconst prompt = `\nYou are a crypto-industry news analyst.\nSummarize current news and market sentiment for **${q}** based on these articles:\n${list}\n\nAnswer in 3 parts:\n1. Summary of News\n2. Market Sentiment\n3. Links to reference news articles\n`;\nreturn [{ json: { prompt } }];"}, "typeVersion": 2}, {"id": "2f5b2eb5-bc2e-432d-b0c8-44c6822b831b", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [2080, 1020], "parameters": {"color": 3, "width": 300, "height": 580, "content": "## Send Telegram Response\n\nSends the final AI-generated summary to the user.\n⚠️ Replace chatId with a dynamic value like << Telegram ID here >> to ensure it sends to the right user."}, "typeVersion": 1}, {"id": "d9f178d6-b834-4a18-b8a5-ab1261b99ca6", "name": "Adds the sessionId", "type": "n8n-nodes-base.set", "position": [-1740, 1340], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "87c63e75-94e4-432c-b15f-8762aa340215", "name": "sessionId", "type": "string", "value": "={{ $json.message.chat.id }}"}]}}, "typeVersion": 3.4}, {"id": "4dd3e531-ab2a-4930-a127-08adb5bde409", "name": "Summarize News & Sentiment (GPT-4o)", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1360, 1320], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "GPT-4O"}, "options": {}, "messages": {"values": [{"content": "={{ $node[\"Build Prompt\"].json.prompt }}"}, {"role": "assistant", "content": "You are a crypto‐industry news analyst. Summarize sentiment clearly and concisely."}]}}, "credentials": {"openAiApi": {"id": "yUizd8t0sD5wMYVG", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "b0da1d25-9c17-49a1-85f4-82425e7c9f91", "name": "Prepare Telegram Message", "type": "n8n-nodes-base.set", "position": [1780, 1320], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "565fb705-ac83-4a96-9343-2e29e348cc4f", "name": "summary", "type": "string", "value": "={{ $json.message.content }}"}]}}, "typeVersion": 3.4}, {"id": "95cb909b-51f4-4661-9db5-e8181a7efffe", "name": "Sends Response", "type": "n8n-nodes-base.telegram", "position": [2180, 1320], "webhookId": "8018c16f-426e-4a8b-8fbf-55cc013d1226", "parameters": {"text": "={{ $json.summary }}", "chatId": "<< Add Telegram ID here >>", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "YDlV4LtcNcmNqk4y", "name": "Crypto_News_and_Sentiment_bot"}}, "typeVersion": 1.2}, {"id": "a783bddf-7d70-4345-88f3-c474a50bdea0", "name": "Send Crypto or Company Name for Analysis", "type": "n8n-nodes-base.telegramTrigger", "position": [-2180, 1340], "webhookId": "37a1f055-1670-472d-9850-d89555b7ed47", "parameters": {"updates": ["message"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "YDlV4LtcNcmNqk4y", "name": "Crypto_News_and_Sentiment_bot"}}, "typeVersion": 1.2}, {"id": "d847a178-c0f2-482b-8773-ac0362435dfd", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-2320, 1200], "parameters": {"color": 4, "width": 380, "height": 360, "content": "## Telegram Setup \nThis bot listens for incoming messages in Telegram. To use it, create a bot with @BotFather and paste your bot token into the credentials."}, "typeVersion": 1}, {"id": "75ee14cf-6236-4f45-b864-b422f18c8214", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-1860, 1220], "parameters": {"width": 360, "height": 320, "content": "## Initialize Chat Session\nStores the user's chat ID as sessionId, which is used to manage conversation memory across steps."}, "typeVersion": 1}, {"id": "674f2555-752d-4ffc-894c-efd705f32887", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-1440, 1200], "parameters": {"color": 6, "width": 600, "height": 460, "content": "##  Extract Keyword \nThis AI agent parses the user input and returns a single-word keyword to help match relevant news articles."}, "typeVersion": 1}, {"id": "bece27b1-bc60-44f7-bdc9-6ec61f7a2e5b", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-320, 300], "parameters": {"color": 5, "width": 460, "height": 1980, "content": "## News Aggregators\nPulls articles from major crypto news sources. You can add more RSS feeds here to expand coverage."}, "typeVersion": 1}, {"id": "7f7a2949-4147-4ab9-bb52-99c1bb2205ed", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [440, 980], "parameters": {"color": 2, "width": 480, "height": 760, "content": "## Merge & Filter News\nCombines all RSS articles and filters them using the extracted keyword to match only relevant results."}, "typeVersion": 1}, {"id": "d19d53c0-7d23-4ccb-9a68-dc2855e5b73d", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [1000, 1140], "parameters": {"color": 7, "width": 260, "height": 360, "content": "## Prepare AI Prompt\nConstructs the input prompt for GPT-4o with a list of filtered articles. Output includes summary, sentiment, and article links."}, "typeVersion": 1}, {"id": "17607891-ed84-4cca-ad3d-c398e5652a8c", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [1320, 1100], "parameters": {"color": 3, "width": 340, "height": 440, "content": "## Summarize News & Sentiment (GPT-4o)\nUses OpenAI GPT-4o to generate a concise summary of the news, analyze market sentiment, and return formatted results.\n\n"}, "typeVersion": 1}, {"id": "9d47eb56-4469-4f42-84a1-6f76bb7fa598", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [1720, 1060], "parameters": {"color": 6, "height": 460, "content": "## Format for Telegram\n\nExtracts the AI summary and prepares it for Telegram delivery. Change the variable if you want to include other data.\n\n\n"}, "typeVersion": 1}, {"id": "7c02860b-f865-4ff9-baeb-0903a76278d9", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [1140, 320], "parameters": {"width": 520, "content": "## ✅ How to Use This Template\n \n🛠 1. Connect your Telegram bot\n🤖 2. Add your OpenAI credentials\n📰 3. Customize the RSS feeds (optional)\n✅ 4. Start your bot and send a query like “Bitcoin” or “NFT”"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "ae250264-5df8-4341-b198-5272<PERSON><PERSON><PERSON>38", "connections": {"Set Query": {"main": [[{"node": "RSS Cointelegraph", "type": "main", "index": 0}, {"node": "RSS Bitcoinmagazine", "type": "main", "index": 0}, {"node": "RSS Coindesk", "type": "main", "index": 0}, {"node": "RSS Bitcoinist", "type": "main", "index": 0}, {"node": "RSS Newsbtc", "type": "main", "index": 0}, {"node": "RSS Cryptopotato", "type": "main", "index": 0}, {"node": "RSS 99bitcoins", "type": "main", "index": 0}, {"node": "RSS Cryptobriefing", "type": "main", "index": 0}, {"node": "RSS Crypto.news", "type": "main", "index": 0}]]}, "RSS Newsbtc": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 4}]]}, "Build Prompt": {"main": [[{"node": "Summarize News & Sentiment (GPT-4o)", "type": "main", "index": 0}]]}, "RSS Coindesk": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 2}]]}, "RSS 99bitcoins": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 7}]]}, "RSS Bitcoinist": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 3}]]}, "Filter by Query": {"main": [[{"node": "Build Prompt", "type": "main", "index": 0}]]}, "RSS Crypto.news": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 9}]]}, "RSS Cryptopotato": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 6}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Crypto News & Sentiment Agent", "type": "ai_languageModel", "index": 0}]]}, "RSS Cointelegraph": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Adds the sessionId": {"main": [[{"node": "Crypto News & Sentiment Agent", "type": "main", "index": 0}]]}, "Merge All Articles": {"main": [[{"node": "Filter by Query", "type": "main", "index": 0}]]}, "RSS Cryptobriefing": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 8}]]}, "RSS Bitcoinmagazine": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Prepare Telegram Message": {"main": [[{"node": "Sends Response", "type": "main", "index": 0}]]}, "Crypto News & Sentiment Agent": {"main": [[{"node": "Set Query", "type": "main", "index": 0}]]}, "Summarize News & Sentiment (GPT-4o)": {"main": [[{"node": "Prepare Telegram Message", "type": "main", "index": 0}]]}, "Send Crypto or Company Name for Analysis": {"main": [[{"node": "Adds the sessionId", "type": "main", "index": 0}]]}}}