{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [440, 300], "parameters": {"url": "https://n8n.io/n8n-logo.png", "options": {}, "responseFormat": "file"}, "typeVersion": 1}, {"name": "LinkedIn", "type": "n8n-nodes-base.linkedIn", "position": [640, 300], "parameters": {"text": "this is a test image post", "person": "gZG0JALzuy", "postAs": "person", "additionalFields": {}, "shareMediaCategory": "IMAGE"}, "credentials": {"linkedInOAuth2Api": "linkedin_demo"}, "typeVersion": 1}], "connections": {"HTTP Request": {"main": [[{"node": "LinkedIn", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}}}