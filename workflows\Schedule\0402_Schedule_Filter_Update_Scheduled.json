{"meta": {"instanceId": "257476b1ef58bf3cb6a46e65fac7ee34a53a5e1a8492d5c6e4da5f87c9b82833", "templateId": "2088"}, "nodes": [{"id": "0e4c65ce-95e9-4a32-bc5c-0461cb339764", "name": "Filter Status (Waiting for sending)", "type": "n8n-nodes-base.filter", "position": [1670, 1110], "parameters": {"options": {"looseTypeValidation": true}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "loose"}, "combinator": "and", "conditions": [{"id": "401b79a0-a079-4ea0-805b-a963d9206031", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.Status }}", "rightValue": "Waiting for sending"}, {"id": "74ec18c7-e4cc-4d82-ba05-0ec4781cbb9f", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.Title }}", "rightValue": ""}, {"id": "6e293a16-48cd-40bb-9882-09b456a97d58", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.Subject }}", "rightValue": ""}, {"id": "a02d2518-e979-4a17-ab00-dda6723d9945", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.Email }}", "rightValue": ""}, {"id": "bea4e49e-cf8a-4f05-bd6f-bdce0c5d8533", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.Name }}", "rightValue": ""}, {"id": "e33eb064-34c6-4dea-b454-10f4fb7fe630", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.Date }}", "rightValue": ""}, {"id": "1abe48e3-ba4d-4318-900d-fd58097d55ec", "operator": {"type": "dateTime", "operation": "equals"}, "leftValue": "={{ DateTime.fromFormat($json[\"Date\"], 'yyyy/MM/dd').startOf('day')}}", "rightValue": "={{ $now.startOf('day')}}"}]}}, "typeVersion": 2}, {"id": "27f33448-a016-4ac8-aea3-2ca267fb1628", "name": "Set data", "type": "n8n-nodes-base.set", "position": [2290, 1090], "parameters": {"fields": {"values": [{"name": "email", "stringValue": "={{ $json.Email }}"}, {"name": "name", "stringValue": "={{ $json.Name }}"}, {"name": "ID", "stringValue": "={{ $json.ID }}"}]}, "include": "selected", "options": {}}, "typeVersion": 3.2}, {"id": "badf1d45-21e0-40a5-bcc4-c434f43c04d4", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [390, 250], "parameters": {"width": 922.8914139860617, "height": 1171.2381808131183, "content": "# Node Descriptions\n\n## Retrieve Customer Messages Data (Google Sheets Node ):\n**Purpose and Use Cases:**\nThe primary purpose of this node is to retrieve data from a Google Sheets document that contains information about messages to customers. This could be used in various scenarios, such as:\n\n- Compiling a list of customer contacts for outreach campaigns.\n- Gathering feedback or responses stored in a spreadsheet.\n- Automating the process of updating customer records or tracking communications.\n\n\n## Filter Items by Current Date Node:\n- **Purpose:** Filters input items based on comparison with the current date.\n- **Parameters:**\n  - **JavaScript Code:** Compares item dates with the current date for filtering.\n- **Functionality:** Ensures only items with dates matching the current date are passed along.\n\n ### Note : The date format should be in this form (year/month/day) to be accepted.\n\n## Gmail Node:\n- **Purpose:** Likely sends emails using Gmail based on filtered items.\n- **Parameters:**\n  - **Recipient:** Extracted from input data.\n  - **Subject:** Extracted from input data.\n  - **Email Content:** Extracted from input data.\n\n## Update Message Status(Google Sheets Node ):\n- **Purpose:** Updates specific columns in the Google Sheets document.\n- **Parameters:**\n  - **Operation:** Update mode.\n  - **Columns:** Defines which columns to update with values from input data.\n\n## Filter Status (Waiting for sending) Node:\n- **Purpose:** Filters items based on specific status criteria.\n- **Parameters:**\n  - **Conditions:** Filters based on status, title, subject, email, name, and date.\n\n## Set data Node:\n- **Purpose:** Sets specified fields with extracted values from input data.\n\n## Merge feild Node:\n- **Purpose:** Merges fields from different sources based on position.\n\nFeel free to utilize these detailed descriptions to understand and enhance the workflow further.\n"}, "typeVersion": 1}, {"id": "0f1cd01b-4cf0-4998-9c51-02c2c9c4aa2b", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1330, 870], "parameters": {"width": 1386.7301333853884, "height": 539.317352764935, "content": "# The Workflow"}, "typeVersion": 1}, {"id": "421b47fd-4707-41ec-97e4-7256be22b75d", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [380, 240], "parameters": {"color": 7, "width": 2358.9018586668417, "height": 1194.0044652590357, "content": "\n"}, "typeVersion": 1}, {"id": "dfb3d998-14fc-4d2a-af9e-19c7da8060f9", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1330, 255], "parameters": {"width": 342.9710297084147, "height": 601.2740375761717, "content": "## Copy this template to get started :\n- [Google Sheets](https://docs.google.com/spreadsheets/d/1efCCzfeUX0AETz2wsULQN90OBCOKK-gBoDaptzcBHdE/edit?usp=sharing)\n\n## Workflow Nodes Documentation:\n\n1. [Schedule Trigger](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.scheduletrigger/)\n2. [Filter Items by Current Date](https://docs.n8n.io/nodes/n8n-nodes-base.code.html)\n3. [Gmail](https://docs.n8n.io/nodes/n8n-nodes-base.gmail.html)\n4. [Google Sheets](https://docs.n8n.io/nodes/n8n-nodes-base.googleSheets.html)\n5. [Filter Status (Waiting for sending)](https://docs.n8n.io/nodes/n8n-nodes-base.filter.html)\n6. [Set data](https://docs.n8n.io/nodes/n8n-nodes-base.set.html)\n7. [Merge feild](https://docs.n8n.io/nodes/n8n-nodes-base.merge.html)\n"}, "typeVersion": 1}, {"id": "4ed5a195-fd49-465e-9a14-fa64cd96056b", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1690, 250], "parameters": {"width": 1024.157503378047, "height": 602.8437691253422, "content": "## Workflow Overview:\n\nThe workflow begins with the \"Google Sheets Trigger\" node, which monitors a specified Google Sheet for new row additions. Upon detection of a new row, the workflow progresses to the \"Filter Status (Waiting for sending)\" node, where items are filtered based on specific conditions.\n\nSubsequently, the workflow moves to the \"Filter Items by Current Date\" node, which filters items based on the current date. Items matching the current date are then processed further.\n\nThe filtered items are then forwarded to the \"Gmail\" node, where personalized emails are composed and sent to recipients based on the Google Sheet data. Finally, the workflow updates the Google Sheet using the \"Google Sheets\" node with the status of the sent emails and other relevant information."}, "typeVersion": 1}, {"id": "d156f0da-53e7-499b-b652-5ca0c3c9e237", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [1350, 1110], "parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 1}]}}, "typeVersion": 1.1}, {"id": "e398e809-b00f-4edb-af94-d236cfb80703", "name": "Retrieve Customer Messages Data", "type": "n8n-nodes-base.googleSheets", "position": [1510, 1110], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/126qQdkEWt_4Vkxvu6G80rBeFdIp_a8ISMz-898fa2D4/edit#gid=0", "cachedResultName": "Page"}, "documentId": {"__rl": true, "mode": "list", "value": "126qQdkEWt_4Vkxvu6G80rBeFdIp_a8ISMz-898fa2D4", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/126qQdkEWt_4Vkxvu6G80rBeFdIp_a8ISMz-898fa2D4/edit?usp=drivesdk", "cachedResultName": "Copy of Sending Messages to Customers"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "9", "name": "<PERSON>'s Google"}}, "typeVersion": 4.2}, {"id": "6de55876-3924-4fdc-aae7-b901a2ce72be", "name": "Update Message Status", "type": "n8n-nodes-base.googleSheets", "position": [2490, 1090], "parameters": {"columns": {"value": {"ID": "={{ $json.ID }}", "Status": "<PERSON><PERSON> successfully"}, "schema": [{"id": "ID", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Name", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Email", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Date", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Status", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Title", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Subject", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Subject", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["ID"]}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/126qQdkEWt_4Vkxvu6G80rBeFdIp_a8ISMz-898fa2D4/edit#gid=0", "cachedResultName": "Page"}, "documentId": {"__rl": true, "mode": "list", "value": "126qQdkEWt_4Vkxvu6G80rBeFdIp_a8ISMz-898fa2D4", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/126qQdkEWt_4Vkxvu6G80rBeFdIp_a8ISMz-898fa2D4/edit?usp=drivesdk", "cachedResultName": "Copy of Sending Messages to Customers"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "9", "name": "<PERSON>'s Google"}}, "typeVersion": 4.2}, {"id": "0401bfe7-de72-407a-a7f7-70b7da52e9c9", "name": "Send a message (Gmail)", "type": "n8n-nodes-base.gmail", "position": [1900, 980], "parameters": {"sendTo": "={{ $json.Email }}", "message": "={{ $json.Subject }}", "options": {}, "subject": "={{ $json.Title }}", "emailType": "text"}, "credentials": {"gmailOAuth2": {"id": "3", "name": "Gmail account"}}, "notesInFlow": false, "typeVersion": 2.1, "alwaysOutputData": false}, {"id": "94ff4e81-41c6-479e-bcb5-c9786935a954", "name": "Merge fields", "type": "n8n-nodes-base.merge", "position": [2110, 1090], "parameters": {"mode": "combine", "options": {}, "combinationMode": "mergeByPosition"}, "typeVersion": 2.1}], "pinData": {}, "connections": {"Set data": {"main": [[{"node": "Update Message Status", "type": "main", "index": 0}]]}, "Merge fields": {"main": [[{"node": "Set data", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Retrieve Customer Messages Data", "type": "main", "index": 0}]]}, "Send a message (Gmail)": {"main": [[{"node": "Merge fields", "type": "main", "index": 0}]]}, "Retrieve Customer Messages Data": {"main": [[{"node": "Filter Status (Waiting for sending)", "type": "main", "index": 0}]]}, "Filter Status (Waiting for sending)": {"main": [[{"node": "Merge fields", "type": "main", "index": 1}, {"node": "Send a message (Gmail)", "type": "main", "index": 0}]]}}}