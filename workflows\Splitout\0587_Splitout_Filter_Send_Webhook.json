{"meta": {"instanceId": "04ab549d8bbb435ec33b81e4e29965c46cf6f0f9e7afe631018b5e34c8eead58"}, "nodes": [{"id": "082d1828-72b1-48c0-8426-c8051c29f0db", "name": "Session", "type": "n8n-nodes-base.httpRequest", "position": [-20, -20], "parameters": {"url": "https://api.fastmail.com/jmap/session", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "3IRsYkeB2ofrwQjv", "name": "Fastmail"}}, "typeVersion": 4.2}, {"id": "d7dc4c50-c8fc-4999-918d-5d357567ed14", "name": "Get Mailbox IDs", "type": "n8n-nodes-base.httpRequest", "notes": "https://api.fastmail.com/.well-known/jmap\n\nhttps://api.fastmail.com/jmap/session", "position": [200, -20], "parameters": {"url": "https://api.fastmail.com/jmap/api/", "method": "POST", "options": {}, "jsonBody": "={\n    \"using\": [\"urn:ietf:params:jmap:core\", \"urn:ietf:params:jmap:mail\"],\n    \"methodCalls\": [\n      [\n        \"Mailbox/get\",\n        {\n          \"accountId\": \"{{ $('Session').item.json.primaryAccounts['urn:ietf:params:jmap:mail'] }}\"\n        },\n        \"c0\"\n      ]\n    ]\n  }", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Accept", "value": "application/json"}]}}, "credentials": {"httpHeaderAuth": {"id": "3IRsYkeB2ofrwQjv", "name": "Fastmail"}}, "typeVersion": 4.2}, {"id": "31be3c1c-f4c5-4309-92b3-2fd0a3fcecc6", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [400, -20], "parameters": {"options": {}, "fieldToSplitOut": "methodResponses[0][1].list"}, "typeVersion": 1}, {"id": "93de4dad-70d6-4e16-b351-7c540c3a4bfa", "name": "<PERSON><PERSON> (IMAP)", "type": "n8n-nodes-base.emailReadImap", "position": [-20, -240], "parameters": {"options": {"customEmailConfig": "[\"UNSEEN\"]"}, "postProcessAction": "nothing", "downloadAttachments": true}, "credentials": {"imap": {"id": "vFzz9hU9rTHVHs3I", "name": "IMAP"}}, "typeVersion": 2}, {"id": "41e77a60-622f-426c-a50c-e0df03c53208", "name": "Get fields from source email", "type": "n8n-nodes-base.set", "position": [200, -240], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "a9d425bd-e576-4e38-a251-b462240d3e2d", "name": "textPlain", "type": "string", "value": "={{ $json.textPlain }}"}, {"id": "7071a252-fcad-4aa1-953f-205c3e403497", "name": "from", "type": "string", "value": "={{ $json.from }}"}, {"id": "c4b0ed1b-590c-4d7f-b494-a0f34304cc1a", "name": "subject", "type": "string", "value": "={{ $json.subject }}"}, {"id": "7e0badd1-02be-4149-b9ff-286f0943f051", "name": "metadata['message-id']", "type": "string", "value": "={{ $json.metadata['message-id'] }}"}, {"id": "f87c7c15-c1d3-4696-bcd4-6677e5ddb240", "name": "metadata['reply-to']", "type": "string", "value": "={{ $json.metadata['reply-to'] }}"}]}}, "typeVersion": 3.4}, {"id": "f9d1a529-1377-456b-8357-d37fb3fe74f9", "name": "OpenAI", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [400, -240], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "GPT-4O"}, "options": {}, "messages": {"values": [{"content": "=Please analyze the following personal email and draft a casual response based solely on its content. Return only the response text without any additional introductions or formatting. The response should include appropriate greetings (e.g., \"<PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\" in German or \"Hi\", \"Hello\" in English) and sign-offs (e.g., \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON> Gruß\" in German or \"<PERSON>ards\" in English). Add a thanks if appropriate. Use \"Du\" only if appropriate; if the email contains \"Sie\", maintain the same formality.\n\nSubject: {{ $json.subject }}\nEmail Content: {{ $json.textPlain }}"}]}}, "credentials": {"openAiApi": {"id": "iW0ItIt1ZxCQrBqk", "name": "OpenAI"}}, "typeVersion": 1.5}, {"id": "c421ddc9-b230-499c-a11d-a20a68d30c5b", "name": "Filter for drafts folder", "type": "n8n-nodes-base.filter", "position": [560, -20], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "4e4c63d1-40fe-4314-bfe7-4fee62c78b88", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.role }}", "rightValue": "drafts"}]}}, "typeVersion": 2.2}, {"id": "ef19fde4-cf8c-4e19-912e-822611c18056", "name": "upload draft email", "type": "n8n-nodes-base.httpRequest", "notes": "https://api.fastmail.com/.well-known/jmap\n\nhttps://api.fastmail.com/jmap/session", "position": [1000, -120], "parameters": {"url": "https://api.fastmail.com/jmap/api/", "method": "POST", "options": {}, "jsonBody": "={\n  \"using\": [\"urn:ietf:params:jmap:core\", \"urn:ietf:params:jmap:mail\"],\n  \"methodCalls\": [\n    [\n      \"Email/set\",\n      {\n        \"accountId\": \"{{ $('Session').item.json.primaryAccounts['urn:ietf:params:jmap:mail'] }}\",\n        \"create\": {\n          \"newDraft\": {\n            \"mailboxIds\": {\n              \"{{ $json.draftsId }}\": true\n            },\n           \"keywords\": {\n              \"$draft\": true\n            },\n            \"inReplyTo\": [\"{{ $json.metadata['message-id'] }}\"],\n            \"references\": [\"{{ $json.metadata['message-id'] }}\"],\n            \"from\": [{\n              \"name\": \"\",\n              \"email\": \"{{ $('Session').item.json.username }}\"\n            }],\n            \"to\": [{\n              \"name\": \"{{ $json['to-friendly'] }}\",\n              \"email\": \"{{ $json.to }}\"\n            }],\n            \"subject\": \"{{ $json.subject }}\",\n            \"bodyValues\": {\n              \"textBody\": {\n                \"value\": \"{{ $json.message.content.replace(/\\n/g, '\\\\n') }}\"\n              }\n            },\n            \"bodyStructure\": {\n              \"partId\": \"textBody\"\n            }\n          }\n        }\n      },\n      \"c1\"\n    ]\n  ]\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Accept", "value": "application/json"}]}}, "credentials": {"httpHeaderAuth": {"id": "3IRsYkeB2ofrwQjv", "name": "Fastmail"}}, "typeVersion": 4.2}, {"id": "f4ecb64a-c978-4aa3-943e-c4a7f0592b91", "name": "gather data for draft email", "type": "n8n-nodes-base.set", "position": [800, -120], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "78885ad0-fa62-407e-82de-f297190265be", "name": "draftsId", "type": "string", "value": "={{ $json.id }}"}, {"id": "fcb31dde-0881-4b98-8bc2-e3e215148a5c", "name": "to-friendly", "type": "string", "value": "={{ $('Get fields from source email').item.json.from.match(/[^<]+/)[0].trim().replaceAll(/\\\"/g, \"\") }}"}, {"id": "84c80af6-68dd-44bd-97ba-fde78a42e88a", "name": "subject", "type": "string", "value": "=Re: {{ $('Get fields from source email').item.json.subject }}"}, {"id": "590e9856-9c6f-4d23-af42-8a0a1384ac00", "name": "message.content", "type": "string", "value": "={{ $('OpenAI').item.json.message.content }}"}, {"id": "4f24e071-24e3-4101-a423-ad5bbcca9fc7", "name": "metadata['message-id']", "type": "string", "value": "={{ $('Get fields from source email').item.json.metadata['message-id'] }}"}, {"id": "80c92734-0296-4299-9f98-15cc62e93d44", "name": "to", "type": "string", "value": "={{ $('Get fields from source email').item.json.metadata['reply-to'].match(/<([^>]+)>/)[1] ?? $('Get fields from source email').item.json.from.match(/<([^>]+)>/)[1] }}"}]}}, "typeVersion": 3.4}, {"id": "ca868672-85bd-4e2e-b2c6-6c6c69b78b24", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-580, -560], "parameters": {"width": 493.9330818092735, "height": 695.2489786026621, "content": "## Workflow Description:\nThis n8n workflow automates the drafting of email replies for Fastmail using OpenAI's GPT-4 model. Here’s the overall process:\n\n1. **Email Monitoring**: The workflow continuously monitors a specified IMAP inbox for new, unread emails.\n2. **Email Data Extraction**: When a new email is detected, it extracts relevant details such as the sender, subject, email body, and metadata.\n3. **AI Response Generation**: The extracted email content is sent to OpenAI's GPT-4, which generates a personalized draft response.\n4. **Get Fastmail Session and Mailbox IDs**: Connects to the Fastmail API to retrieve necessary session details and mailbox IDs.\n5. **Draft Identification**: Identifies the \"Drafts\" folder in the mailbox.\n6. **Draft Preparation**: Compiles all the necessary information to create the draft, including the generated response, original email details, and specified recipient.\n7. **Draft Uploading**: Uploads the prepared draft email to the \"Drafts\" folder in the Fastmail mailbox."}, "typeVersion": 1}, {"id": "c4273cc2-1ac2-43f4-bcd1-7f42d3109373", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-40, -560], "parameters": {"color": 3, "width": 722.************, "height": 285.*************, "content": "## Prerequisites:\n1. **IMAP Email Account**: You need to configure an IMAP email account in n8n to monitor incoming emails.\n2. **Fastmail API Credentials**: A Fastmail account with JMAP API enabled. You should set up HTTP Header authentication in n8n with your Fastmail API credentials.\n3. **OpenAI API Key**: An API key from OpenAI to access GPT-4. Make sure to configure the OpenAI credentials in n8n."}, "typeVersion": 1}], "pinData": {}, "connections": {"OpenAI": {"main": [[{"node": "Session", "type": "main", "index": 0}]]}, "Session": {"main": [[{"node": "Get Mailbox IDs", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Filter for drafts folder", "type": "main", "index": 0}]]}, "Get Mailbox IDs": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Email Trigger (IMAP)": {"main": [[{"node": "Get fields from source email", "type": "main", "index": 0}]]}, "Filter for drafts folder": {"main": [[{"node": "gather data for draft email", "type": "main", "index": 0}]]}, "gather data for draft email": {"main": [[{"node": "upload draft email", "type": "main", "index": 0}]]}, "Get fields from source email": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}}}