{"id": "34", "name": "Monitoring and alerting", "nodes": [{"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [250, 200], "parameters": {}, "typeVersion": 1}, {"name": "Postgres", "type": "n8n-nodes-base.postgres", "position": [450, 200], "parameters": {"query": "SELECT * FROM n8n WHERE value > 70 AND notification = false;", "operation": "execute<PERSON>uery"}, "credentials": {"postgres": "Postgres"}, "typeVersion": 1}, {"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.twilio", "position": [650, 200], "parameters": {"to": "", "from": "", "message": "=🚨 The Sensor ({{$node[\"Postgres\"].json[\"sensor_id\"]}}) showed a reading of {{$node[\"Postgres\"].json[\"value\"]}}."}, "credentials": {"twilioApi": "<PERSON><PERSON><PERSON>"}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [850, 200], "parameters": {"values": {"number": [{"name": "id", "value": "={{$node[\"Postgres\"].json[\"id\"]}}"}], "boolean": [{"name": "notification", "value": true}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "Postgres1", "type": "n8n-nodes-base.postgres", "position": [1050, 200], "parameters": {"table": "n8n", "columns": "notification", "operation": "update"}, "credentials": {"postgres": "Postgres"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Set": {"main": [[{"node": "Postgres1", "type": "main", "index": 0}]]}, "Cron": {"main": [[{"node": "Postgres", "type": "main", "index": 0}]]}, "Twilio": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}, "Postgres": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}}}