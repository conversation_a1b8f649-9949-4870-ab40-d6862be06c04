{"id": "GWXjIqENWvx6OqvX", "meta": {"instanceId": "94467bfa3af1aedd621d1940913d2d1a79e58bb9e7bbb0aa858d7f4a635296a5", "templateCredsSetupCompleted": true}, "name": "TEMPLATE - Multi Methods API Endpoint", "tags": [], "nodes": [{"id": "d5b5010f-97fb-4f80-871b-e9f04b3977a9", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [1220, -180], "parameters": {"options": {}, "respondWith": "allIncomingItems"}, "typeVersion": 1.1}, {"id": "46711e2f-6cd1-4947-9452-7a1484ae562f", "name": "Respond to Webhook1", "type": "n8n-nodes-base.respondToWebhook", "position": [1220, 860], "parameters": {"options": {"responseCode": 201}, "respondWith": "allIncomingItems"}, "typeVersion": 1.1}, {"id": "20489a88-39a5-4cf7-8c08-826e4e9a7f34", "name": "Respond to Webhook2", "type": "n8n-nodes-base.respondToWebhook", "position": [1220, 340], "parameters": {"options": {"responseCode": 200}, "respondWith": "allIncomingItems"}, "typeVersion": 1.1}, {"id": "04320a5f-29fe-42b0-9e01-31035f23b9dc", "name": "Respond to Webhook4", "type": "n8n-nodes-base.respondToWebhook", "position": [1220, 600], "parameters": {"options": {}, "respondWith": "allIncomingItems"}, "typeVersion": 1.1}, {"id": "45ef8f08-f765-440d-be85-12096b6b4105", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [940, 765.3897477624087], "parameters": {"color": 4, "width": 514, "height": 255.253864930838, "content": "#### Creation\nCreates a new record"}, "typeVersion": 1}, {"id": "2e820357-250c-41a7-9daa-4eb77e7eded6", "name": "Create", "type": "n8n-nodes-base.airtable", "position": [1000, 860], "parameters": {"base": {"__rl": true, "mode": "list", "value": "app662qLY5J8ys4fU", "cachedResultUrl": "https://airtable.com/app662qLY5J8ys4fU", "cachedResultName": "customers"}, "table": {"__rl": true, "mode": "list", "value": "tblwvA7Wrmvmv37rq", "cachedResultUrl": "https://airtable.com/app662qLY5J8ys4fU/tblwvA7Wrmvmv37rq", "cachedResultName": "Table 1"}, "columns": {"value": {"email": "={{ $json.query.email }}", "phone": "={{ $json.query.phone }}", "address": "={{ $json.query.address }}", "last_name": "={{ $json.query.last_name }}", "first_name": "={{ $json.query.first_name }}", "customer_id": "={{ $json.query.customer_id }}"}, "schema": [{"id": "customer_id", "type": "number", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "customer_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "first_name", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "first_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "last_name", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "last_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "email", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "phone", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "phone", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "address", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "address", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": []}, "options": {}, "operation": "create"}, "credentials": {"airtableTokenApi": {"id": "yX3WnQ0zNClN0JoN", "name": "Airtable giulio@n8n"}}, "typeVersion": 2.1}, {"id": "dceb7ad3-3c29-4cb9-b097-00c5ae1d2732", "name": "Get All", "type": "n8n-nodes-base.airtable", "position": [1000, 600], "parameters": {"base": {"__rl": true, "mode": "list", "value": "app662qLY5J8ys4fU", "cachedResultUrl": "https://airtable.com/app662qLY5J8ys4fU", "cachedResultName": "customers"}, "table": {"__rl": true, "mode": "list", "value": "tblwvA7Wrmvmv37rq", "cachedResultUrl": "https://airtable.com/app662qLY5J8ys4fU/tblwvA7Wrmvmv37rq", "cachedResultName": "Table 1"}, "options": {}, "operation": "search"}, "credentials": {"airtableTokenApi": {"id": "yX3WnQ0zNClN0JoN", "name": "Airtable giulio@n8n"}}, "typeVersion": 2.1}, {"id": "15a418ac-9de1-4c1d-ada7-057c280373df", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [940, 522.9617575264442], "parameters": {"color": 4, "width": 514, "height": 228.69080553295362, "content": "#### Get All\nRetrieves all records"}, "typeVersion": 1}, {"id": "9736394d-3298-485c-b907-19804bbd48fb", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [940, -260], "parameters": {"color": 4, "width": 514, "height": 228, "content": "#### Get\nRetrieves a single record"}, "typeVersion": 1}, {"id": "b5544fc2-10cf-47dd-815c-51e8044e073d", "name": "Get Single", "type": "n8n-nodes-base.airtable", "position": [1000, -180], "parameters": {"base": {"__rl": true, "mode": "list", "value": "app662qLY5J8ys4fU", "cachedResultUrl": "https://airtable.com/app662qLY5J8ys4fU", "cachedResultName": "customers"}, "limit": 1, "table": {"__rl": true, "mode": "list", "value": "tblwvA7Wrmvmv37rq", "cachedResultUrl": "https://airtable.com/app662qLY5J8ys4fU/tblwvA7Wrmvmv37rq", "cachedResultName": "Table 1"}, "options": {}, "operation": "search", "returnAll": false, "filterByFormula": "=({customer_id} = {{ $json.params.id }})"}, "credentials": {"airtableTokenApi": {"id": "yX3WnQ0zNClN0JoN", "name": "Airtable giulio@n8n"}}, "typeVersion": 2.1}, {"id": "0f08fcee-b892-47ec-b13c-639f7e5b4b91", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [940, 260], "parameters": {"color": 4, "width": 508.29454841334433, "height": 248.84784377542707, "content": "#### Update\nUpdates of an existing record"}, "typeVersion": 1}, {"id": "56ff1769-15fe-475d-96aa-9c0f1a9edf05", "name": "Airtable", "type": "n8n-nodes-base.airtable", "position": [1000, 340], "parameters": {"base": {"__rl": true, "mode": "list", "value": "app662qLY5J8ys4fU", "cachedResultUrl": "https://airtable.com/app662qLY5J8ys4fU", "cachedResultName": "customers"}, "table": {"__rl": true, "mode": "list", "value": "tblwvA7Wrmvmv37rq", "cachedResultUrl": "https://airtable.com/app662qLY5J8ys4fU/tblwvA7Wrmvmv37rq", "cachedResultName": "Table 1"}, "columns": {"value": {"email": "={{ $json.query.email }}", "phone": "={{ $json.query.phone }}", "address": "={{ $json.query.address }}", "last_name": "={{ $json.query.last_name }}", "first_name": "={{ $json.query.first_name }}", "customer_id": "={{ $json.query.customer_id }}"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "customer_id", "type": "number", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "customer_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "first_name", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "first_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "last_name", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "last_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "email", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "phone", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "phone", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "address", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "address", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["customer_id"]}, "options": {}, "operation": "update"}, "credentials": {"airtableTokenApi": {"id": "yX3WnQ0zNClN0JoN", "name": "Airtable giulio@n8n"}}, "typeVersion": 2.1}, {"id": "e20c0448-9688-47ae-873b-7cc5ac6e826a", "name": "Respond to Webhook5", "type": "n8n-nodes-base.respondToWebhook", "position": [1420, 80], "parameters": {"options": {"responseCode": 200}, "respondWith": "allIncomingItems"}, "typeVersion": 1.1}, {"id": "f13eb006-b576-4e65-9c04-7a8516dccb35", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [940, -20], "parameters": {"color": 4, "width": 737.8307567127741, "height": 267.43205858421476, "content": "#### Delete\nDeletes a record"}, "typeVersion": 1}, {"id": "0f434e52-2fda-41c0-9f40-38bf1977b8a6", "name": "Airtable1", "type": "n8n-nodes-base.airtable", "position": [1200, 80], "parameters": {"id": "={{ $json.id }}", "base": {"__rl": true, "mode": "list", "value": "app662qLY5J8ys4fU", "cachedResultUrl": "https://airtable.com/app662qLY5J8ys4fU", "cachedResultName": "customers"}, "table": {"__rl": true, "mode": "list", "value": "tblwvA7Wrmvmv37rq", "cachedResultUrl": "https://airtable.com/app662qLY5J8ys4fU/tblwvA7Wrmvmv37rq", "cachedResultName": "Table 1"}, "operation": "deleteRecord"}, "credentials": {"airtableTokenApi": {"id": "yX3WnQ0zNClN0JoN", "name": "Airtable giulio@n8n"}}, "typeVersion": 2.1}, {"id": "c58724ab-354b-43af-8a60-495837f8a4a2", "name": "Get Single1", "type": "n8n-nodes-base.airtable", "position": [1000, 80], "parameters": {"base": {"__rl": true, "mode": "list", "value": "app662qLY5J8ys4fU", "cachedResultUrl": "https://airtable.com/app662qLY5J8ys4fU", "cachedResultName": "customers"}, "limit": 1, "table": {"__rl": true, "mode": "list", "value": "tblwvA7Wrmvmv37rq", "cachedResultUrl": "https://airtable.com/app662qLY5J8ys4fU/tblwvA7Wrmvmv37rq", "cachedResultName": "Table 1"}, "options": {}, "operation": "search", "returnAll": false, "filterByFormula": "=({customer_id} = {{ $json.params.id }})"}, "credentials": {"airtableTokenApi": {"id": "yX3WnQ0zNClN0JoN", "name": "Airtable giulio@n8n"}}, "typeVersion": 2.1}, {"id": "1b8fc8af-4892-4804-85d0-8e84904a3cf0", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [500, 720], "webhookId": "580ccc56-f308-4b64-961d-38323501a170", "parameters": {"path": "customers", "options": {}, "responseMode": "responseNode", "multipleMethods": true}, "typeVersion": 2}, {"id": "7a8a9006-c2ea-4a87-8a94-fb925ed91abd", "name": "Webhook (with ID)", "type": "n8n-nodes-base.webhook", "position": [500, 80], "webhookId": "580ccc56-f308-4b64-961d-38323501a170", "parameters": {"path": "customers/:id", "options": {}, "httpMethod": ["GET", "DELETE", "PUT"], "responseMode": "responseNode", "multipleMethods": true}, "typeVersion": 2}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "b9009017-c9f6-4f8c-9592-350825e54476", "connections": {"Create": {"main": [[{"node": "Respond to Webhook1", "type": "main", "index": 0}]]}, "Get All": {"main": [[{"node": "Respond to Webhook4", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Get All", "type": "main", "index": 0}], [{"node": "Create", "type": "main", "index": 0}]]}, "Airtable": {"main": [[{"node": "Respond to Webhook2", "type": "main", "index": 0}]]}, "Airtable1": {"main": [[{"node": "Respond to Webhook5", "type": "main", "index": 0}]]}, "Get Single": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Get Single1": {"main": [[{"node": "Airtable1", "type": "main", "index": 0}]]}, "Webhook (with ID)": {"main": [[{"node": "Get Single", "type": "main", "index": 0}], [{"node": "Get Single1", "type": "main", "index": 0}], [{"node": "Airtable", "type": "main", "index": 0}]]}}}