{"nodes": [{"name": "Zoom", "type": "n8n-nodes-base.zoom", "position": [1340, 580], "parameters": {"topic": "New Meeting", "authentication": "oAuth2", "additionalFields": {"type": 3, "settings": {"muteUponEntry": true, "joinBeforeHost": true, "participantVideo": true}, "timeZone": "America/New_York"}}, "typeVersion": 1}, {"name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "notes": "Cron trigger to reset zoom meeting on the auto-redirect link", "position": [1120, 580], "parameters": {"rule": {"interval": [{"daysInterval": 360, "triggerAtHour": 3}]}}, "typeVersion": 1.2}, {"name": "Wordpress", "type": "n8n-nodes-base.wordpress", "position": [1560, 580], "parameters": {"pageId": "123 (Create a page in WP, copy the ID of the page, paste it here)", "resource": "page", "operation": "update", "updateFields": {"content": "=\n<meta http-equiv=\"refresh\" content=\"0;{{ $json.join_url }}\">\n<p>Redirecting, please wait a moment. Meeting will begin shortly&#8230;</p>"}}, "typeVersion": 1}, {"name": "<PERSON><PERSON>ck", "type": "n8n-nodes-base.slack", "position": [1780, 580], "parameters": {"text": "=Zoom recurring meeting updated!\n{{ $('Zoom').item.json.join_url }}", "select": "channel", "channelId": {"__rl": true, "mode": "list", "value": "abc123", "cachedResultName": "my-slack-channel"}, "otherOptions": {"includeLinkToWorkflow": true}}, "typeVersion": 2.2}], "pinData": {}, "connections": {"Zoom": {"main": [[{"node": "Wordpress", "type": "main", "index": 0}]]}, "Wordpress": {"main": [[{"node": "<PERSON><PERSON>ck", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Zoom", "type": "main", "index": 0}]]}}}