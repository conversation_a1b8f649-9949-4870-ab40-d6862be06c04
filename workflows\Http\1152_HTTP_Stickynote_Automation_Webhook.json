{"id": "81aN6oJGMho5kCvQ", "meta": {"instanceId": "32e39908afbcb49d79cc3b05576c030ecc2871395b7aec4e0fdc88778498f80e"}, "name": "OpenAI ImageGen1 Template", "tags": [], "nodes": [{"id": "179754ad-eae5-447a-b225-46145370e79b", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [-440, 80], "parameters": {"url": "https://api.openai.com/v1/images/edits", "method": "POST", "options": {}, "sendBody": true, "contentType": "multipart-form-data", "sendHeaders": true, "bodyParameters": {"parameters": [{"name": "image", "parameterType": "formBinaryData", "inputDataFieldName": "data0"}, {"name": "prompt", "value": "={{ $('When chat message received').item.json.chatInput }}"}, {"name": "model", "value": "gpt-image-1"}, {"name": "n", "value": "1"}, {"name": "size", "value": "1024x1024"}, {"name": "quality", "value": "high"}]}, "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{ $json.openAIKey }}"}]}}, "typeVersion": 4.2}, {"id": "0aca28af-1325-4391-bee6-3ab636c34f6a", "name": "Convert to File", "type": "n8n-nodes-base.convertToFile", "position": [-220, 80], "parameters": {"options": {}, "operation": "toBinary", "sourceProperty": "data[0].b64_json"}, "typeVersion": 1.1}, {"id": "7bc8dbf1-eb81-4f9b-9563-7ae568034221", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-860, 80], "webhookId": "449bbfbc-0523-406f-94a2-089bca9d7295", "parameters": {"options": {"allowFileUploads": true, "allowedFilesMimeTypes": "*"}}, "typeVersion": 1.1}, {"id": "79b3e008-758c-4c24-adac-eb514fedf2c8", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-820, -440], "parameters": {"width": 660, "height": 460, "content": "### 🖼️ Edit Images with the **OpenAI ImageGen v1** API\n\n1. **Verify Your Organization**  \n   Log in to the OpenAI Platform and confirm your org is verified:  \n   [OpenAI Settings → Organization](https://platform.openai.com/settings/organization/general)\n\n2. **Add Your API Key**  \n   In the n8n credentials, paste a valid **OpenAI secret key** into the `API_KEY` field.\n\n3. **Run “Open Chat”**  \n   Trigger the **`Open Chat`** node, supply your **text prompt** and **source image**, then execute.\n\n4. **Preview & Automate**  \n   The new image appears in the **`Convert to File`** node. From here you can:  \n   - Send it by email  \n   - Push to S3, Supabase, or any storage  \n   - Post straight to Slack, Discord, etc.\n\n> *Tip — chain additional n8n nodes to watermark, resize, or schedule social-media posts automatically.*\n"}, "typeVersion": 1}, {"id": "8b75f205-dcfb-4c43-b8bf-942419b96633", "name": "API KEY", "type": "n8n-nodes-base.set", "position": [-640, 80], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "b943d609-b213-4531-912f-e721db4d2cc7", "name": "openAIKey", "type": "string", "value": "sk-proj-..."}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "fb19daaf-a425-4d0c-9141-fefee17be117", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [40, -440], "parameters": {"color": 5, "width": 660, "height": 1380, "content": "[![AI-Image Cash Machine – banner](https://public-files.gumroad.com/r2rfepypwzxebylbzutm7lk577m7)](https://drauscher.gumroad.com/l/PremiumAISaaSTemplateBeginnerFriendlyCustomizable)\n\n\n\n### This is just the core of our bigger ⭐ AI Image Cash Machine Template ⭐\n\n## 🚀 Launch Your **AI-Image Cash Machine** This Weekend\n\n**Customizable · Beginner Friendly**\n\n💸 **Special Summer Deal — 10 % off with code `SUMMER25` (just €5+)**\n\n[Grab the template on Gumroad →](https://drauscher.gumroad.com/l/PremiumAISaaSTemplateBeginnerFriendlyCustomizable)\n\n---\n\n### Why You’ll Love It\n- **Plug-and-Play App** – Next.js front-end on Vercel, wired to Supabase, Stripe, n8n & OpenAI  \n- **No-Code Automation** – drag-drop n8n workflow delivers images instantly after payment  \n- **Built-In Payments** – Stripe keys + webhooks included, start charging the moment you deploy  \n- **Scalable Storage** – private Supabase bucket keeps every customer image secure  \n- **Own the Source** – MIT license lets you tweak, brand, even resell without lock-in  \n\n> **Try it live:** **Pixarify Online** – see the template in action!  \n\n---\n\n### What’s Inside\n- Production-ready **frontend UI** (Next.js + Tailwind)  \n- Pre-configured **n8n backend** triggered by Stripe webhook  \n- Step-by-step **PDF setup guide**  \n- Sample environment file (`.env.example`)  \n\n---\n\n### 3-Step Fast-Track Setup\n1. **Clone the repo** & run `vercel deploy` — live site in 5 min  \n2. **Paste your Stripe + OpenAI keys**  \n3. **Activate the n8n workflow** — start selling AI images immediately  \n\n"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "6e7f19b0-042a-4c63-9375-36d62290eb3e", "connections": {"API KEY": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[]]}, "When chat message received": {"main": [[{"node": "API KEY", "type": "main", "index": 0}]]}}}