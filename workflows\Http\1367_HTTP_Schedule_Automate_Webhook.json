{"id": "CCcz1G4G2yPwk1me", "meta": {"instanceId": "a2b23892dd6989fda7c1209b381f5850373a7d2b85609624d7c2b7a092671d44", "templateCredsSetupCompleted": true}, "name": "💥workflow n8n d'Auto-Post sur les réseaux sociaux - vide", "tags": [], "nodes": [{"id": "72df02b7-b426-4d79-970a-936e40d1a67d", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-360, -40], "parameters": {"rule": {"interval": [{"triggerAtHour": 22}]}}, "typeVersion": 1.2}, {"id": "d1f58a61-6a56-447b-9e34-d9abb58e50b8", "name": "Assign Social Media IDs", "type": "n8n-nodes-base.set", "position": [80, -40], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "{\n  \"instagram_id\": \"111\",\n  \"youtube_id\": \"222\",\n  \"tiktok_id\": \"333\",\n  \"facebook_id\": \"444\",\n  \"facebook_page_id\": \"555\",\n  \"threads_id\": \"666\",\n  \"twitter_id\": \"777\",\n  \"linkedin_id\": \"888\",\n  \"pinterest_id\": \"999\",\n  \"pinterest_board_id\": \"101010\",\n  \"bluesky_id\": \"111111\"\n}"}, "typeVersion": 3.4}, {"id": "6c4a3eb4-f166-4702-acb5-efeffc7e5754", "name": "Get my video", "type": "n8n-nodes-base.googleSheets", "position": [-140, -40], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "id", "value": "="}, "documentId": {"__rl": true, "mode": "id", "value": "="}}, "credentials": {"googleSheetsOAuth2Api": {"id": "51us92xkOlrvArhV", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "4c2d215a-48d7-426f-acd4-4a02a375094e", "name": "Upload Video to Blotato", "type": "n8n-nodes-base.httpRequest", "position": [300, -40], "parameters": {"url": "https://backend.blotato.com/v2/media", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "bodyParameters": {"parameters": [{"name": "url", "value": "={{ $('Get my video').item.json['URL VIDEO'] }}"}]}, "headerParameters": {"parameters": [{"name": "blotato-api-key"}]}}, "typeVersion": 4.2}, {"id": "a179cae8-128c-4ed5-b8cb-6e9fae29d742", "name": "INSTAGRAM", "type": "n8n-nodes-base.httpRequest", "position": [580, -280], "parameters": {"url": "https://backend.blotato.com/v2/posts", "method": "POST", "options": {}, "jsonBody": "={\n  \"post\": {\n    \"accountId\": \"{{ $('Assign Social Media IDs').item.json.instagram_id }}\",\n    \"target\": {\n      \"targetType\": \"instagram\"\n    },\n    \"content\": {\n      \"text\": \"{{ $('Get my video').item.json.DESCRIPTION }}\",\n      \"platform\": \"instagram\",\n      \"mediaUrls\": [\n        \"{{ $json.url }}\"\n      ]\n    }\n  }\n}\n\n", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "blotato-api-key"}]}}, "typeVersion": 4.2}, {"id": "d1c6965d-e347-471e-b400-8047a839de6d", "name": "YOUTUBE", "type": "n8n-nodes-base.httpRequest", "position": [800, -280], "parameters": {"url": "https://backend.blotato.com/v2/posts", "method": "POST", "options": {}, "jsonBody": "={\n  \"post\": {\n    \"accountId\": \"{{ $('Assign Social Media IDs').item.json.youtube_id }}\",\n    \"target\": {\n      \"targetType\": \"youtube\",\n      \"title\": \"{{ $('Get my video').item.json.Titre }}\",\n      \"privacyStatus\": \"unlisted\",\n      \"shouldNotifySubscribers\": \"false\"\n    },\n    \"content\": {\n      \"text\": \"{{ $('Get my video').item.json.DESCRIPTION }}\",\n      \"platform\": \"youtube\",\n      \"mediaUrls\": [\n        \"{{ $json.url }}\"\n      ]\n    }\n  }\n}\n", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "blotato-api-key"}]}}, "typeVersion": 4.2}, {"id": "598a9da7-3b10-44d0-a1d2-dbbda8ce1a51", "name": "TIKTOK", "type": "n8n-nodes-base.httpRequest", "position": [1000, -280], "parameters": {"url": "https://backend.blotato.com/v2/posts", "method": "POST", "options": {}, "jsonBody": "={\n  \"post\": {\n    \"accountId\": \"{{ $('Assign Social Media IDs').item.json.tiktok_id }}\",\n    \"target\": {\n      \"targetType\": \"tiktok\",\n      \"isYourBrand\": \"false\", \n      \"disabledDuet\": \"false\",\n      \"privacyLevel\": \"PUBLIC_TO_EVERYONE\",\n      \"isAiGenerated\": \"true\",\n      \"disabledStitch\": \"false\",\n      \"disabledComments\": \"false\",\n      \"isBrandedContent\": \"false\"\n      \n    },\n    \"content\": {\n      \"text\": \"{{ $('Get my video').item.json.DESCRIPTION }}\",\n      \"platform\": \"tiktok\",\n      \"mediaUrls\": [\n        \"{{ $json.url }}\"\n      ]\n    }\n  }\n}\n", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "blotato-api-key"}]}}, "typeVersion": 4.2}, {"id": "796e0a52-c431-4239-bbb5-baf2d000f60e", "name": "FACEBOOK", "type": "n8n-nodes-base.httpRequest", "position": [580, -40], "parameters": {"url": "https://backend.blotato.com/v2/posts", "method": "POST", "options": {}, "jsonBody": "={\n  \"post\": {\n    \"accountId\": \"{{ $('Assign Social Media IDs').item.json.facebook_id }}\",\n    \"target\": {\n      \"targetType\": \"facebook\",\n      \"pageId\": \"{{ $('Assign Social Media IDs').item.json.facebook_page_id }}\"\n\n      \n    },\n    \"content\": {\n      \"text\": \"{{ $('Get my video').item.json.DESCRIPTION }}\",\n      \"platform\": \"facebook\",\n      \"mediaUrls\": [\n        \"{{ $json.url }}\"\n      ]\n    }\n  }\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "blotato-api-key"}]}}, "typeVersion": 4.2}, {"id": "d88677f8-0a38-4aea-8825-0fef04b67af6", "name": "THREADS", "type": "n8n-nodes-base.httpRequest", "position": [800, -40], "parameters": {"url": "https://backend.blotato.com/v2/posts", "method": "POST", "options": {}, "jsonBody": "={\n  \"post\": {\n    \"accountId\": \"{{ $('Assign Social Media IDs').item.json.threads_id }}\",\n    \"target\": {\n      \"targetType\": \"threads\"\n      \n    },\n    \"content\": {\n      \"text\": \"{{ $('Get my video').item.json.DESCRIPTION }}\",\n      \"platform\": \"threads\",\n      \"mediaUrls\": [\n        \"{{ $json.url }}\"\n      ]\n    }\n  }\n}\n", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "blotato-api-key"}]}}, "typeVersion": 4.2}, {"id": "2f3b9b5f-09f0-4fd0-9c65-f6b3be566f59", "name": "TWETTER", "type": "n8n-nodes-base.httpRequest", "position": [1000, -40], "parameters": {"url": "https://backend.blotato.com/v2/posts", "method": "POST", "options": {}, "jsonBody": "={\n  \"post\": {\n    \"accountId\": \"{{ $('Assign Social Media IDs').item.json.twitter_id }}\",\n    \"target\": {\n      \"targetType\": \"twitter\"\n      \n    },\n    \"content\": {\n      \"text\": \"{{ $('Get my video').item.json.DESCRIPTION }}\",\n      \"platform\": \"twitter\",\n      \"mediaUrls\": [\n        \"{{ $json.url }}\"\n      ]\n    }\n  }\n}\n", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "blotato-api-key"}]}}, "typeVersion": 4.2}, {"id": "183f17ff-b8f4-472a-a956-61201dc36741", "name": "LINKEDIN", "type": "n8n-nodes-base.httpRequest", "position": [580, 200], "parameters": {"url": "https://backend.blotato.com/v2/posts", "method": "POST", "options": {}, "jsonBody": "={\n  \"post\": {\n    \"accountId\": \"{{ $('Assign Social Media IDs').item.json.linkedin_id }}\",\n    \"target\": {\n      \"targetType\": \"linkedin\"\n      \n    },\n    \"content\": {\n      \"text\": \"{{ $('Get my video').item.json.DESCRIPTION }}\",\n      \"platform\": \"linkedin\",\n      \"mediaUrls\": [\n        \"{{ $json.url }}\"\n      ]\n    }\n  }\n}\n", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "blotato-api-key"}]}}, "typeVersion": 4.2}, {"id": "34a4d037-d035-42fe-a4ea-e4560c811dbb", "name": "BLUESKY", "type": "n8n-nodes-base.httpRequest", "position": [800, 200], "parameters": {"url": "https://backend.blotato.com/v2/posts", "method": "POST", "options": {}, "jsonBody": "= {\n  \"post\": {\n    \"accountId\": \"{{ $('Assign Social Media IDs').item.json.bluesky_id }}\",\n    \"target\": {\n      \"targetType\": \"bluesky\"\n      \n    },\n    \"content\": {\n      \"text\": \"{{ $('Get my video').item.json.DESCRIPTION }}\",\n      \"platform\": \"bluesky\",\n      \"mediaUrls\": [\n        \"https://pbs.twimg.com/media/GE8MgIiWEAAfsK3.jpg\"\n      ]\n    }\n  }\n}\n", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "blotato-api-key"}]}}, "typeVersion": 4.2}, {"id": "f331b371-c71b-4bb6-a06d-f439b568f7ea", "name": "PINTEREST", "type": "n8n-nodes-base.httpRequest", "position": [1000, 200], "parameters": {"url": "https://backend.blotato.com/v2/posts", "method": "POST", "options": {}, "jsonBody": "={\n  \"post\": {\n    \"accountId\": \"{{ $('Assign Social Media IDs').item.json.pinterest_id }}\",\n    \"target\": {\n      \"targetType\": \"pinterest\",\n      \"boardId\": \"{{ $('Assign Social Media IDs').item.json.pinterest_board_id }}\"      \n    },\n    \"content\": {\n      \"text\": \"{{ $('Get my video').item.json.DESCRIPTION }}\",\n      \"platform\": \"pinterest\",\n      \"mediaUrls\": [\n        \"https://pbs.twimg.com/media/GE8MgIiWEAAfsK3.jpg\"\n      ]\n    }\n  }\n}\n\n", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "blotato-api-key"}]}}, "typeVersion": 4.2}, {"id": "59d608a5-d5c6-4160-96cc-c42534f99c0b", "name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [1180, -40], "parameters": {"columns": {"value": {"STATUS": "DONE", "row_number": "={{ $('Get my video').item.json.row_number }}"}, "schema": [{"id": "PROMPT", "type": "string", "display": true, "required": false, "displayName": "PROMPT", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "DESCRIPTION", "type": "string", "display": true, "required": false, "displayName": "DESCRIPTION", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URL VIDEO", "type": "string", "display": true, "required": false, "displayName": "URL VIDEO", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Titre", "type": "string", "display": true, "required": false, "displayName": "Titre", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "STATUS", "type": "string", "display": true, "required": false, "displayName": "STATUS", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["row_number"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "id", "value": "="}, "documentId": {"__rl": true, "mode": "id", "value": "="}}, "credentials": {"googleSheetsOAuth2Api": {"id": "51us92xkOlrvArhV", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "f15f4a2d-bf81-4fe4-91a0-2ff5c9662f46", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-420, -480], "parameters": {"color": 6, "width": 880, "height": 300, "content": "# Auto-Publish to 9 Social Platforms\n## Automates distribution using Blotato’s API.\n## The video is auto-published to Instagram, YouTube, TikTok, Facebook, \n## LinkedIn, Threads, Twitter (X), Bluesky, and Pinterest \n## — all in one go, with no manual work required.\n### ** Documentation : ** [Guide](https://automatisation.notion.site/Workflow-n8n-d-Auto-Post-sur-les-r-seaux-sociaux-1d33d6550fd980b7b43ac417e9a06a9b?pvs=4)"}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "f2540099-8de1-4ac6-9856-7d5989d5e7e3", "connections": {"FACEBOOK": {"main": [[]]}, "Get my video": {"main": [[{"node": "Assign Social Media IDs", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Get my video", "type": "main", "index": 0}]]}, "Assign Social Media IDs": {"main": [[{"node": "Upload Video to Blotato", "type": "main", "index": 0}]]}, "Upload Video to Blotato": {"main": [[{"node": "INSTAGRAM", "type": "main", "index": 0}, {"node": "YOUTUBE", "type": "main", "index": 0}, {"node": "TIKTOK", "type": "main", "index": 0}, {"node": "FACEBOOK", "type": "main", "index": 0}, {"node": "THREADS", "type": "main", "index": 0}, {"node": "TWITTER", "type": "main", "index": 0}, {"node": "LINKEDIN", "type": "main", "index": 0}, {"node": "BLUESKY", "type": "main", "index": 0}, {"node": "PINTEREST", "type": "main", "index": 0}, {"node": "Google Sheets", "type": "main", "index": 0}]]}}}