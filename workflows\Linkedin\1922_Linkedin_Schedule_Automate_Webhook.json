{"id": "mb2MU4xOaT3NrvqN", "meta": {"instanceId": "e7a28cc5c8c9de1976820e0f309940cf456344d9daf5360a4975186f3d8a107f", "templateCredsSetupCompleted": true}, "name": "Automate LinkedIn Posts with AI", "tags": [], "nodes": [{"id": "7e8ec5cc-0216-4897-8a40-c44f9bbe5a9b", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [580, 540], "parameters": {"rule": {"interval": [{"triggerAtHour": 15}]}}, "typeVersion": 1.2}, {"id": "dbde804d-9c84-4023-9e05-7506cd38a460", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [760, 225.26841303066982], "parameters": {"color": 6, "width": 652.1201853643956, "height": 542.0867486896091, "content": "## Fetch the day's post from my Notion database\nA Notion _\"database\"_ is just a table on a Notion Page.\nThis table will have various rows, for which a minimum of three columns are required:\n- Name\n- Status\n- Date\n\nThe Date column is the most important, which will dictate when that row from your Notion table containing the text should be posted.\n\nNOTE: each post is required to have a copy and pasted image!"}, "typeVersion": 1}, {"id": "95205e81-e28d-48f9-b3fb-bcf361f7799e", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1520, 220], "parameters": {"width": 860.9829802912225, "height": 540.7357881640437, "content": "## Format Post\nSend the post to OpenAI, where it will attempt to ask your assistant how to take the incoming blob of text, and soup it up into something more palpable for LinkedIn engagement."}, "typeVersion": 1}, {"id": "4bc2a550-a8ad-4b25-ac53-01413277e068", "name": "Set post status to \"Done\"", "type": "n8n-nodes-base.notion", "position": [2760, 540], "parameters": {"pageId": {"__rl": true, "mode": "url", "value": "={{ $('query entries from Notion table for today').item.json.url }}"}, "options": {}, "resource": "databasePage", "operation": "update", "propertiesUi": {"propertyValues": [{"key": "Status|status", "statusValue": "Done"}]}}, "credentials": {"notionApi": {"id": "nBu4zRArkldtNypO", "name": "Notion account"}}, "typeVersion": 2.2}, {"id": "31116f06-72ca-4219-9575-8efaefbff24b", "name": "Post on LinkedIn", "type": "n8n-nodes-base.linkedIn", "position": [2500, 540], "parameters": {"text": "={{ $json.output }}", "person": "_RmSSZc0jB", "additionalFields": {}, "shareMediaCategory": "IMAGE"}, "credentials": {"linkedInOAuth2Api": {"id": "fozSa4dLS6Jgbn4e", "name": "LinkedIn account 2"}}, "typeVersion": 1}, {"id": "1bf0540d-a180-457a-a7d7-fb74c8119a52", "name": "Combine text+image", "type": "n8n-nodes-base.merge", "position": [2100, 540], "parameters": {"mode": "combine", "options": {}, "combinationMode": "mergeByPosition"}, "typeVersion": 2.1}, {"id": "f1fdf6f7-a75c-451b-8bce-ea581b4b6197", "name": "Fetch image from post", "type": "n8n-nodes-base.httpRequest", "position": [1640, 620], "parameters": {"url": "={{ $json.url[0] }}", "options": {}}, "typeVersion": 4.2}, {"id": "00e2bbcb-bac0-4a7e-9892-59f41a26ce9d", "name": "Reformat Post Text", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1620, 440], "parameters": {"text": "=Thank you kindly for your help, please refer to the following LinkedIn post, and output a reformatted version employing thoroughly thought-out paragraph breaks, and lists if present:\n```\n{{ $json.content.join(\" \") }}\n```", "prompt": "define", "options": {}, "resource": "assistant", "assistantId": {"__rl": true, "mode": "list", "value": "asst_J1KuOx5wTLrjEHuy5q94jEgh", "cachedResultName": "LinkedIn Post Reviewer"}}, "credentials": {"openAiApi": {"id": "Gxn0kNMCREcTNGcB", "name": "OpenAi account 2"}}, "typeVersion": 1.3}, {"id": "119d7fc7-ed62-4a73-916e-8baf19ab1d86", "name": "get all content from post page", "type": "n8n-nodes-base.notion", "position": [1020, 540], "parameters": {"blockId": {"__rl": true, "mode": "url", "value": "={{ $json.url }}"}, "resource": "block", "operation": "getAll", "returnAll": true}, "credentials": {"notionApi": {"id": "nBu4zRArkldtNypO", "name": "Notion account"}}, "typeVersion": 2.2}, {"id": "461d4dd2-a91a-4219-bd20-6dd3398d4274", "name": "Pull together all text blocks + image", "type": "n8n-nodes-base.aggregate", "position": [1240, 540], "parameters": {"options": {}, "fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "content"}, {"fieldToAggregate": "image.file.url"}]}}, "typeVersion": 1}, {"id": "72052eec-c180-4da5-ba15-1a69a7ce6892", "name": "query entries from Notion table for today", "type": "n8n-nodes-base.notion", "position": [800, 540], "parameters": {"filters": {"conditions": [{"key": "Date|date", "date": "={{ $today.format(\"yyyy/mM/dd\") }}", "condition": "equals"}]}, "options": {}, "resource": "databasePage", "operation": "getAll", "databaseId": {"__rl": true, "mode": "list", "value": "9aba7f55-a7de-4329-9d5b-6d127937fa49", "cachedResultUrl": "https://www.notion.so/9aba7f55a7de43299d5b6d127937fa49", "cachedResultName": "LinkedIn Posts example"}, "filterType": "manual"}, "credentials": {"notionApi": {"id": "nBu4zRArkldtNypO", "name": "Notion account"}}, "typeVersion": 2.2}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "35f9b7b6-0e60-495f-826d-af7040e7de1f", "connections": {"Post on LinkedIn": {"main": [[{"node": "Set post status to \"Done\"", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "query entries from Notion table for today", "type": "main", "index": 0}]]}, "Combine text+image": {"main": [[{"node": "Post on LinkedIn", "type": "main", "index": 0}]]}, "Reformat Post Text": {"main": [[{"node": "Combine text+image", "type": "main", "index": 0}]]}, "Fetch image from post": {"main": [[{"node": "Combine text+image", "type": "main", "index": 1}]]}, "get all content from post page": {"main": [[{"node": "Pull together all text blocks + image", "type": "main", "index": 0}]]}, "Pull together all text blocks + image": {"main": [[{"node": "Fetch image from post", "type": "main", "index": 0}, {"node": "Reformat Post Text", "type": "main", "index": 0}]]}, "query entries from Notion table for today": {"main": [[{"node": "get all content from post page", "type": "main", "index": 0}]]}}}