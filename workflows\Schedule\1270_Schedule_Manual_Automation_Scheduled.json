{"meta": {"instanceId": "2b1cc1a8b0a2fb9caab11ab2d5eb3712f9973066051b2e898cf4041a1f2a7757", "templateId": "2324", "templateCredsSetupCompleted": true}, "nodes": [{"id": "71b06728-7f59-49e3-9365-3281189a6659", "name": "When clicking \"Test workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [920, 340], "parameters": {}, "typeVersion": 1}, {"id": "b37019e3-c7ab-4119-986d-c27d082a036e", "name": "Input", "type": "n8n-nodes-base.set", "position": [1340, 340], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "fcc97354-b9f6-4459-a004-46e87902c77c", "name": "company_input", "type": "string", "value": "={{ $json.input }}"}, {"id": "e5415c49-5204-45b1-a0e9-814157127b12", "name": "row_number", "type": "number", "value": "={{ $json.row_number }}"}]}}, "typeVersion": 3.3}, {"id": "7d5d53ac-6d3c-4b24-97c7-deb6b76749e5", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [2020, 660], "parameters": {"model": "gpt-4o", "options": {"temperature": 0.3}}, "credentials": {"openAiApi": {"id": "FMTQypGcsAwaRQdC", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "24e2f3b0-8b90-49a9-bde6-0fb0c2baf52a", "name": "Get website content", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [2580, 680], "parameters": {"name": "get_website_content", "source": "parameter", "description": "This tool will return the text from the given URL. ", "workflowJson": "{\n \"meta\": {\n \"templateCredsSetupCompleted\": true,\n \"instanceId\": \"2b1cc1a8b0a2fb9caab11ab2d5eb3712f9973066051b2e898cf4041a1f2a7757\"\n },\n \"nodes\": [\n {\n \"parameters\": {},\n \"id\": \"475eaf3c-7e11-457e-8b72-4d3e683e2f80\",\n \"name\": \"Execute Workflow Trigger\",\n \"type\": \"n8n-nodes-base.executeWorkflowTrigger\",\n \"typeVersion\": 1,\n \"position\": [\n 260,\n 340\n ]\n },\n {\n \"parameters\": {\n \"url\": \"={{ $json.query.url }}\",\n \"options\": {}\n },\n \"id\": \"321fbc74-d749-4f9b-954e-7cad37601ddf\",\n \"name\": \"Visit Website\",\n \"type\": \"n8n-nodes-base.httpRequest\",\n \"typeVersion\": 4.2,\n \"position\": [\n 440,\n 340\n ]\n },\n {\n \"parameters\": {\n \"operation\": \"extractHtmlContent\",\n \"extractionValues\": {\n \"values\": [\n {\n \"key\": \"body\",\n \"cssSelector\": \"html\",\n \"skipSelectors\": \"head\"\n }\n ]\n },\n \"options\": {\n \"cleanUpText\": true\n }\n },\n \"id\": \"6e51732a-4999-4805-838b-f692e9965197\",\n \"name\": \"HTML\",\n \"type\": \"n8n-nodes-base.html\",\n \"typeVersion\": 1.2,\n \"position\": [\n 620,\n 340\n ]\n }\n ],\n \"connections\": {\n \"Execute Workflow Trigger\": {\n \"main\": [\n [\n {\n \"node\": \"Visit Website\",\n \"type\": \"main\",\n \"index\": 0\n }\n ]\n ]\n },\n \"Visit Website\": {\n \"main\": [\n [\n {\n \"node\": \"HTML\",\n \"type\": \"main\",\n \"index\": 0\n }\n ]\n ]\n }\n },\n \"pinData\": {\n \"Execute Workflow Trigger\": [\n {\n \"query\": {\n \"url\": \"https://www.lemlist.com\"\n }\n }\n ]\n }\n}", "jsonSchemaExample": "{\n\t\"url\": \"https://www.lemlist.com\"\n}", "specifyInputSchema": true, "responsePropertyName": "body"}, "typeVersion": 1.1}, {"id": "ff7ab74c-dfc6-43ce-8c57-6edf935b4915", "name": "SerpAPI - Search Google", "type": "@n8n/n8n-nodes-langchain.toolSerpApi", "position": [2300, 660], "parameters": {"options": {}}, "credentials": {"serpApi": {"id": "ECK6FimAloRJOZMG", "name": "SerpAPI account"}}, "typeVersion": 1}, {"id": "4fe311f2-4983-4380-b4ed-a827a406fce5", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [2880, 660], "parameters": {"schemaType": "manual", "inputSchema": "{\n\t\"type\": \"object\",\n\t\"properties\": {\n\t\t\"case_study_link\": {\n\t\t\t\"type\":[\"string\", \"null\"]\n\t\t},\n \t\t\"domain\": {\n\t\t\t\"type\": [\"string\", \"null\"]\n\t\t},\n \"linkedinUrl\": {\n\t\t\t\"type\": [\"string\", \"null\"]\n\t\t},\n \t\"market\": {\n\t\t\t\"type\": [\"string\", \"null\"]\n\t\t},\n\t\t\"cheapest_plan\": {\n\t\t\t\"type\": [\"number\", \"null\"]\n\t\t},\n\t\"has_enterprise_plan\": {\n\t\t\t\"type\": [\"boolean\", \"null\"]\n\t\t},\n\t\"has_API\": {\n\t\t\t\"type\": [\"boolean\", \"null\"]\n\t\t},\n\t\"has_free_trial\": {\n\t\t\t\"type\": [\"boolean\", \"null\"]\n\t\t},\n\t\"integrations\": {\n\t\t\t\"type\": [\"array\",\"null\"],\n \"items\": {\n\t\t\t\t\"type\": \"string\"\n\t\t\t}\n\t\t}\n\t}\n}"}, "typeVersion": 1.2}, {"id": "89ed0723-4dbe-428d-b1a9-ebdf515e42bb", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [1600, 340], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "34ea3862-e8e5-4bf2-a9aa-2ad084376bb5", "name": "AI Researcher Output Data", "type": "n8n-nodes-base.set", "position": [2960, 340], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "4109ca11-1bb8-4f5c-8bec-a962f44b0746", "name": "domain", "type": "string", "value": "={{ $json.output.domain }}"}, {"id": "7f492768-375e-48fa-866b-644b2b5cbd68", "name": "linkedinUrl", "type": "string", "value": "={{ $json.output.linkedinUrl }}"}, {"id": "e30b0d07-68db-45a1-9593-fd6ce24a1d50", "name": "market", "type": "string", "value": "={{ $json.output.market }}"}, {"id": "0c03a51e-2c07-4583-85c6-d3d2ee81c5d1", "name": "cheapest_plan", "type": "number", "value": "={{ $json.output.cheapest_plan }}"}, {"id": "0c9622d0-8446-4663-9a94-964b5df851f1", "name": "has_enterprise_plan", "type": "boolean", "value": "={{ $json.output.has_enterprise_plan }}"}, {"id": "564cf6ea-457f-4762-bc19-6900b7d5743c", "name": "has_API", "type": "boolean", "value": "={{ $json.output.has_API }}"}, {"id": "7fd39897-65c3-45d6-9563-8254f55ecef0", "name": "has_free_trial", "type": "boolean", "value": "={{ $json.output.has_free_trial }}"}, {"id": "26477939-d407-4cae-92b2-9a9dc0f53a64", "name": "integrations", "type": "array", "value": "={{ $json.output.integrations }}"}, {"id": "f0cc61d1-6b6b-4142-8627-4a4c721b19a1", "name": "case_study_link", "type": "string", "value": "={{ $json.output.case_study_link }}"}]}}, "typeVersion": 3.3}, {"id": "ff1cb26d-6138-4ee1-9f28-4ecc80c1c8ae", "name": "Google Sheets - Update Row with data", "type": "n8n-nodes-base.googleSheets", "position": [3600, 700], "parameters": {"columns": {"value": {"domain": "={{ $json.domain }}", "market": "={{ $json.market }}", "row_number": "={{ $json.row_number }}", "linkedinUrl": "={{ $json.linkedinUrl }}", "integrations": "={{ $json.integrations }}", "cheapest_plan": "={{ $json.cheapest_plan }}", "has_free_trial": "={{ $json.has_free_trial }}", "enrichment_status": "done", "has_entreprise_plan": "={{ $json.has_enterprise_plan }}", "last_case_study_link": "={{ $json.case_study_link }}"}, "schema": [{"id": "input", "type": "string", "display": true, "removed": true, "required": false, "displayName": "input", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "domain", "type": "string", "display": true, "required": false, "displayName": "domain", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedinUrl", "type": "string", "display": true, "required": false, "displayName": "linkedinUrl", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "has_free_trial", "type": "string", "display": true, "required": false, "displayName": "has_free_trial", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "cheapest_plan", "type": "string", "display": true, "required": false, "displayName": "cheapest_plan", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "has_entreprise_plan", "type": "string", "display": true, "required": false, "displayName": "has_entreprise_plan", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "last_case_study_link", "type": "string", "display": true, "removed": false, "required": false, "displayName": "last_case_study_link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "market", "type": "string", "display": true, "required": false, "displayName": "market", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "integrations", "type": "string", "display": true, "required": false, "displayName": "integrations", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "enrichment_status", "type": "string", "display": true, "required": false, "displayName": "enrichment_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["row_number"]}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19U7gAgkUEz6mbFcnygf1zKDdGvY6OAdUqq3bZQWgjxE/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "19U7gAgkUEz6mbFcnygf1zKDdGvY6OAdUqq3bZQWgjxE", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19U7gAgkUEz6mbFcnygf1zKDdGvY6OAdUqq3bZQWgjxE/edit?usp=drivesdk", "cachedResultName": "Enrich companies using AI agents"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "GC2OQl3Jvy543LT2", "name": "Google Sheets account - perso"}}, "typeVersion": 4.3}, {"id": "6611f852-b4d6-4a07-9428-db206ef57cc3", "name": "Merge data", "type": "n8n-nodes-base.merge", "position": [3240, 180], "parameters": {"mode": "combine", "options": {}, "combinationMode": "mergeByPosition"}, "typeVersion": 2.1}, {"id": "2a19516b-33a1-4987-9b5f-242a084621e0", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [380, 240], "parameters": {"width": 409.*************, "height": 658.*************, "content": "## Read Me\n\nThis workflow allows you to do account research with the web using AI.\n\nThe advanced AI module has 2 capabilities: \n- Research Google using SerpAPI\n- Visit and get website content using a sub-workflow\n\n\nFrom an unstructured input like a domain or a company name. \n\nIt will return the following properties: \n- domain\n- company Linkedin Url\n- cheapest plan\n- has free trial\n- has entreprise plan\n- has API\n- market (B2B or B2C)\n\n\nThe strength of n8n here is that you can adapt this workflow to research whatever information you need.\n\nYou just have to precise it in the prompt and to precise the output format in the \"Strutured Output Parser\" module.\n\n[Click here to find more detailed instructions with video guide.](https://lempire.notion.site/AI-Web-research-with-n8n-a25aae3258d0423481a08bd102f16906)\n"}, "typeVersion": 1}, {"id": "67d485c9-3289-4bb3-9523-cd24c0b1aa05", "name": "Get rows to enrich", "type": "n8n-nodes-base.googleSheets", "position": [1140, 340], "parameters": {"options": {"returnAllMatches": "returnAllMatches"}, "filtersUI": {"values": [{"lookupColumn": "enrichment_status"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19U7gAgkUEz6mbFcnygf1zKDdGvY6OAdUqq3bZQWgjxE/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "19U7gAgkUEz6mbFcnygf1zKDdGvY6OAdUqq3bZQWgjxE", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19U7gAgkUEz6mbFcnygf1zKDdGvY6OAdUqq3bZQWgjxE/edit?usp=drivesdk", "cachedResultName": "Enrich companies using AI agents"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "GC2OQl3Jvy543LT2", "name": "Google Sheets account - perso"}}, "typeVersion": 4.3}, {"id": "eb0c95e7-2211-48d1-abaf-07cd0c76d3a6", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1540, 227.**************], "parameters": {"width": 300.**************, "height": 333.*************, "content": "### Process rows 1 by 1\nThis module will allow us to process rows 1 by 1"}, "typeVersion": 1}, {"id": "8bf0deae-dda7-4e27-9ac7-978db14cca19", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [2740, 560], "parameters": {"width": 300.**************, "height": 236.**************, "content": "Precise here the format in which you need the data to be "}, "typeVersion": 1}, {"id": "dc4f1550-1e3c-4175-a2b3-10153dc2fd77", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [2180, 200.*************], "parameters": {"width": 300.**************, "height": 279.*************, "content": "### Ask AI what are the information you are looking for about the company"}, "typeVersion": 1}, {"id": "70fc73a0-303b-46e1-822d-cebdbccf8e32", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [2220, 580], "parameters": {"height": 248.91749449109562, "content": "Get your free API key here https://serpapi.com/"}, "typeVersion": 1}, {"id": "0c1dafa9-28fe-4ef4-b80e-d4034e16f6c0", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [920, 580], "parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 2}]}}, "typeVersion": 1.2}, {"id": "8b5ebee9-f519-4621-bf2a-12891794f2c5", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [820, 240], "parameters": {"width": 266.12865147126786, "height": 627.5654650079845, "content": "Run the workflow manually or activate it to run it every 2 hours"}, "typeVersion": 1}, {"id": "d7db2452-ba3d-4adb-bd8b-d17a92d1bce5", "name": "AI company researcher", "type": "@n8n/n8n-nodes-langchain.agent", "position": [2200, 340], "parameters": {"text": "=This is the company I want you to research info about:\n{{ $json.company_input }}\n\nReturn me:\n- the linkedin URL of the company\n- the domain of the company. in this format ([domain].[tld])\n- market: if they are B2B or B2C. Only reply by \"B2B\" or \"B2B\"\n- the lowest paid plan the company is offering. If you are not sure, reply null.\n- the latest case study URL published on the website (find case study hub using google, and return the first case study link)\n- tell me if the company offer an API\n- tell me if the company has an enterprise plan\n- tell me if the company has a free trial mentionned in their homepage. reply false if you don't find strong evidence.\n- return an array with up to 5 tools the company is integrated with", "options": {"maxIterations": 10}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.6}, {"id": "f7896dbd-5c15-44e9-96ca-c695a66562cc", "name": "Search Google with ScrapingBee", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [2300, 1140], "parameters": {"name": "search_google", "source": "parameter", "description": "Call this tool to get results from a google search.", "workflowJson": "{\n \"meta\": {\n \"templateCredsSetupCompleted\": true,\n \"instanceId\": \"2b1cc1a8b0a2fb9caab11ab2d5eb3712f9973066051b2e898cf4041a1f2a7757\"\n },\n \"nodes\": [\n {\n \"parameters\": {},\n \"id\": \"fbb17d8d-e2dc-46ae-aba4-8c27cc9d8766\",\n \"name\": \"Execute Workflow Trigger\",\n \"type\": \"n8n-nodes-base.executeWorkflowTrigger\",\n \"typeVersion\": 1,\n \"position\": [\n 20,\n 460\n ]\n },\n {\n \"parameters\": {\n \"url\": \"https://app.scrapingbee.com/api/v1/store/google\",\n \"authentication\": \"genericCredentialType\",\n \"genericAuthType\": \"httpQueryAuth\",\n \"sendQuery\": true,\n \"queryParameters\": {\n \"parameters\": [\n {\n \"name\": \"search\",\n \"value\": \"={{ $json.query.google_search_query }}\"\n },\n {\n \"name\": \"language\",\n \"value\": \"en\"\n },\n {\n \"name\": \"nb_results\",\n \"value\": \"5\"\n }\n ]\n },\n \"options\": {}\n },\n \"id\": \"b938a2bd-030e-46d7-adee-4e3c85cfc1b3\",\n \"name\": \"Search Google\",\n \"type\": \"n8n-nodes-base.httpRequest\",\n \"typeVersion\": 4.2,\n \"position\": [\n 300,\n 460\n ],\n \"credentials\": {\n \"httpQueryAuth\": {\n \"id\": \"Pb2CIMT0tN838QPy\",\n \"name\": \"ScrapingBee\"\n }\n }\n },\n {\n \"parameters\": {\n \"assignments\": {\n \"assignments\": [\n {\n \"id\": \"096fee70-444e-4948-816c-752b20786062\",\n \"name\": \"response\",\n \"value\": \"={{ $json.organic_results }}\",\n \"type\": \"array\"\n }\n ]\n },\n \"options\": {}\n },\n \"id\": \"c5db1fb6-d875-47d2-97db-287777583f22\",\n \"name\": \"Response\",\n \"type\": \"n8n-nodes-base.set\",\n \"typeVersion\": 3.3,\n \"position\": [\n 520,\n 460\n ]\n }\n ],\n \"connections\": {\n \"Execute Workflow Trigger\": {\n \"main\": [\n [\n {\n \"node\": \"Search Google\",\n \"type\": \"main\",\n \"index\": 0\n }\n ]\n ]\n },\n \"Search Google\": {\n \"main\": [\n [\n {\n \"node\": \"Response\",\n \"type\": \"main\",\n \"index\": 0\n }\n ]\n ]\n }\n },\n \"pinData\": {\n \"Execute Workflow Trigger\": [\n {\n \"query\": {\n \"google_search_query\": \"site:lemlist.com pricing\"\n }\n }\n ]\n }\n}", "jsonSchemaExample": "{\n\t\"google_search_query\": \"site:lemlist.com pricing\"\n}", "specifyInputSchema": true}, "typeVersion": 1.1}, {"id": "7a89c803-8145-49c2-aafe-ec2aff0b2fbc", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [2220, 940], "parameters": {"height": 340.14969579315925, "content": "Instead of SERP API module, you can also use this custom module for ScrapingBee. It is more cost-efficient.\n\nGet your free API key here https://www.scrapingbee.com/"}, "typeVersion": 1}, {"id": "79eff129-790b-46da-bef3-899eb6db3ced", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [1100, -20], "parameters": {"width": 194.6864335083109, "height": 525.6560478822986, "content": "In this workflow, I use Google Sheets to store the results. \n\nYou can use my template to get started faster:\n\n1. [Click on this link to get the template](https://docs.google.com/spreadsheets/d/1vR6s2nlTwu01v3GP7wvSRWS5W49FJIh20ZF7AUkmMDo/edit?usp=sharing)\n2. Make a copy of the Sheets\n3. Add the URL to this node and the node **\"Google Sheets - Update Row with data\"**\n\n\n"}, "typeVersion": 1}], "pinData": {}, "connections": {"Input": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Merge data": {"main": [[{"node": "Google Sheets - Update Row with data", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [null, [{"node": "AI company researcher", "type": "main", "index": 0}, {"node": "Merge data", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Get rows to enrich", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI company researcher", "type": "ai_languageModel", "index": 0}]]}, "Get rows to enrich": {"main": [[{"node": "Input", "type": "main", "index": 0}]]}, "Get website content": {"ai_tool": [[{"node": "AI company researcher", "type": "ai_tool", "index": 0}]]}, "AI company researcher": {"main": [[{"node": "AI Researcher Output Data", "type": "main", "index": 0}]]}, "SerpAPI - Search Google": {"ai_tool": [[{"node": "AI company researcher", "type": "ai_tool", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "AI company researcher", "type": "ai_outputParser", "index": 0}]]}, "AI Researcher Output Data": {"main": [[{"node": "Merge data", "type": "main", "index": 1}]]}, "When clicking \"Test workflow\"": {"main": [[{"node": "Get rows to enrich", "type": "main", "index": 0}]]}, "Google Sheets - Update Row with data": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}}}