{"meta": {"instanceId": "854c212d3baca2d6108faeb1187a4f6d9a3e60117068e7e872ad5e663327af93"}, "nodes": [{"id": "c02e3038-96e8-4bfe-a4fa-925207fef0ee", "name": "Create data pix", "type": "n8n-nodes-base.set", "position": [220, 0], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "ab15b0f8-c40f-4874-8724-ddae8f99e646", "name": "data", "type": "string", "value": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAYdEVYdFNvZnR3YXJlAFBhaW50Lk5FVCA1LjEuMvu8A7YAAAC2ZVhJZklJKgAIAAAABQAaAQUAAQAAAEoAAAAbAQUAAQAAAFIAAAAoAQMAAQAAAAIAAAAxAQIAEAAAAFoAAABphwQAAQAAAGoAAAAAAAAAYAAAAAEAAABgAAAAAQAAAFBhaW50Lk5FVCA1LjEuMgADAACQBwAEAAAAMDIzMAGgAwABAAAAAQAAAAWgBAABAAAAlAAAAAAAAAACAAEAAgAEAAAAUjk4AAIABwAEAAAAMDEwMAAAAADp1fY4ytpsegAAAA1JREFUGFdjYGBgYAAAAAUAAYoz4wAAAAAASUVORK5CYII="}]}}, "typeVersion": 3.4}, {"id": "09573a6a-88e8-48c5-a78e-d45fb37a8b87", "name": "Create img bin", "type": "n8n-nodes-base.convertToFile", "position": [440, 0], "parameters": {"options": {"mimeType": "image/png"}, "operation": "toBinary", "sourceProperty": "data", "binaryPropertyName": "pixel"}, "typeVersion": 1.1}, {"id": "07c42dab-9b60-4f51-b8ab-78df26bc2cdd", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [660, 0], "parameters": {"options": {}, "respondWith": "binary"}, "typeVersion": 1.1}, {"id": "cb0df6bc-d733-4e07-9506-c413d390e482", "name": "Request img", "type": "n8n-nodes-base.webhook", "position": [0, 0], "webhookId": "db4880e7-2134-4994-94e5-a4a3aa120440", "parameters": {"path": "db4880e7-2134-4994-94e5-a4a3aa120440", "options": {}, "responseMode": "responseNode"}, "typeVersion": 2}, {"id": "b7153f9a-f635-48c4-b8fe-d9e93deb33ed", "name": "Do anything to log", "type": "n8n-nodes-base.noOp", "position": [660, 200], "parameters": {}, "typeVersion": 1}, {"id": "d5e4143c-f321-4632-9adf-e95ca718210f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-80, 360], "parameters": {"width": 980, "height": 1280, "content": "## 📬 Workflow: Transparent Tracking Pixel for Email Open Detection\n\n### 📌 Description\nThis workflow serves a **1x1 transparent PNG image** via a webhook, which can be embedded in an email to **track when the email is opened**. When the image is loaded by the recipient's email client, the webhook is triggered, optionally capturing a `userId` to identify who opened the email.\n\n---\n\n### 📂 Workflow Steps\n\n1. **Webhook Trigger** (`Request img`)\n   - **Path:** `/webhook/change-with-your-id`\n   - Triggered by an HTTP request (e.g. when the image is loaded in an email).\n   - Accepts a query parameter `id` to identify the recipient.\n\n2. **Set Base64 Data** (`Create data pix`)\n   - Creates a variable `data` containing a Base64-encoded transparent PNG image (1x1 pixel).\n\n3. **Convert to Binary** (`Create img bin`)\n   - Converts the Base64 `data` string into a binary file.\n   - Sets MIME type to `image/png`.\n\n4. **Respond to Webhook** (`Respond to Webhook`)\n   - Sends the binary image file in the HTTP response.\n\n5. **Logging** (`Do anything to log`)\n   - Placeholder node to log or process the `id` or request metadata.\n   - You can access the `id` using `{{$json[\"query\"][\"id\"]}}`.\n   - You can also use any parameter you want\n\n---\n\n### ✉️ How to Use in Emails\n\nEmbed the image in an HTML email like this:\n\n```html\n<img src=\"https://<your-n8n-instance>/webhook/db4880e7-2134-4994-94e5-a4a3aa120440?id=1234\" width=\"1\" height=\"1\" style=\"display:none;\" alt=\"\" />\n```\n\nWhen the email is opened and the image is loaded, the workflow will be triggered.\n\n---\n\n### 🛠️ Notes\n- Some email clients block images by default; this may prevent tracking.\n- You can enhance the workflow to store open events in a database, log the timestamp, IP, or user agent.\n- Make sure to comply with data privacy and consent regulations (e.g. GDPR)."}, "typeVersion": 1}], "pinData": {}, "connections": {"Request img": {"main": [[{"node": "Create data pix", "type": "main", "index": 0}]]}, "Create img bin": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}, {"node": "Do anything to log", "type": "main", "index": 0}]]}, "Create data pix": {"main": [[{"node": "Create img bin", "type": "main", "index": 0}]]}}}