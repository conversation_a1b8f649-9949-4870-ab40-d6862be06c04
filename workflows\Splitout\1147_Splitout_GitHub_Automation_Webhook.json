{"id": "7zRCNv7B5WFRg7ux", "meta": {"instanceId": "e634e668fe1fc93a75c4f2a7fc0dad807ca318b79654157eadb9578496acbc76"}, "name": "Restore your credentials from GitHub", "tags": [{"id": "2RWIfLUVCa0bnmGX", "name": "N8n", "createdAt": "2025-03-06T09:58:39.214Z", "updatedAt": "2025-03-06T09:58:39.214Z"}], "nodes": [{"id": "f8aff38c-3e40-4820-b8f5-50e3e1f878c8", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-640, -120], "parameters": {}, "typeVersion": 1}, {"id": "f838e0c6-36aa-4c0b-bdd2-ef096ffd3d1d", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-1020, -140], "parameters": {"width": 320, "height": 420, "content": "## Restore from GitHub \nThis workflow will restore all instance credentials from GitHub backups.\n\n\n### Setup\nOpen `Globals` node and update the values below 👇\n\n- **repo.owner:** your Github username\n- **repo.name:** the name of your repository\n- **repo.path:** the folder to use within the repository.\n\n\nIf your username was `john-doe` and your repository was called `n8n-backups` and you wanted the credentials to go into a `credentials` folder you would set:\n\n- repo.owner - john-doe\n- repo.name - n8n-backups\n- repo.path - credentials/\n"}, "typeVersion": 1}, {"id": "8f59b7b0-ea9d-4209-8c6b-d48fe9d8cf7b", "name": "Globals", "type": "n8n-nodes-base.set", "position": [-380, -120], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "6cf546c5-5737-4dbd-851b-17d68e0a3780", "name": "repo.owner", "type": "string", "value": "BeyondspaceStudio"}, {"id": "452efa28-2dc6-4ea3-a7a2-c35d100d0382", "name": "repo.name", "type": "string", "value": "n8n-backup"}, {"id": "81c4dc54-86bf-4432-a23f-22c7ea831e74", "name": "repo.path", "type": "string", "value": "credentials"}]}}, "typeVersion": 3.4}, {"id": "d72bf1a6-f3a0-4dc0-afc0-e39c7e8b16f3", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-440, -240], "parameters": {"color": 4, "width": 150, "height": 80, "content": "## Edit this node 👇"}, "typeVersion": 1}, {"id": "4eeb0ed5-7e90-4f09-8296-04c0349de49b", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [100, 20], "parameters": {"color": 4, "content": "## Skip credential\n- The empty json files\n- The n8n account api\n- ...edit this node at will"}, "typeVersion": 1}, {"id": "40856ade-3ff7-43ef-8c45-ec5a126a5787", "name": "Get all files in given path", "type": "n8n-nodes-base.httpRequest", "position": [-160, -120], "parameters": {"url": "=https://api.github.com/repos/{{ $json.repo.owner }}/{{ $json.repo.name }}/contents/{{ $json.repo.path }}", "options": {}, "authentication": "predefinedCredentialType", "nodeCredentialType": "githubApi"}, "credentials": {"githubApi": {"id": "3FYHiPFtycAFT8V0", "name": "GitHub account"}}, "typeVersion": 4.2}, {"id": "4284aadd-4840-4754-9416-6bb74a1df192", "name": "Split the result", "type": "n8n-nodes-base.splitOut", "position": [-600, 200], "parameters": {"options": {}, "fieldToSplitOut": "path"}, "typeVersion": 1}, {"id": "48a04e72-5f9e-4dc3-863d-a8bb30f1c8c2", "name": "Get file content from GitHub", "type": "n8n-nodes-base.github", "position": [-360, 200], "parameters": {"owner": {"__rl": true, "mode": "name", "value": "BeyondspaceStudio"}, "filePath": "={{ $('Get all files in given path').item.json.path }}", "resource": "file", "operation": "get", "repository": {"__rl": true, "mode": "name", "value": "n8n-backup"}, "additionalParameters": {}}, "credentials": {"githubApi": {"id": "3FYHiPFtycAFT8V0", "name": "GitHub account"}}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "507c8514-6acf-4568-83cc-bb07f06e6a96", "name": "Convert files to JSON", "type": "n8n-nodes-base.extractFromFile", "position": [-140, 200], "parameters": {"options": {}, "operation": "fromJson"}, "typeVersion": 1}, {"id": "084e7306-4c7b-4a9b-8f3e-f844ab340f6a", "name": "Restore n8n Credentials", "type": "n8n-nodes-base.n8n", "position": [380, 200], "parameters": {"data": "={{ JSON.stringify($json.data.data) }}", "name": "={{ $json.data.name }}", "resource": "credential", "requestOptions": {}, "credentialTypeName": "={{ $json.data.type }}"}, "credentials": {"n8nApi": {"id": "dzYjDgtEXtpRPKhe", "name": "n8n account"}}, "typeVersion": 1}, {"id": "f8267df1-eb0a-491e-bed4-01480583a535", "name": "Check for skipped Credentials", "type": "n8n-nodes-base.if", "position": [100, 200], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "or", "conditions": [{"id": "ad031296-4ac0-4087-bc35-7975a2cc25e6", "operator": {"type": "object", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json.data }}", "rightValue": ""}, {"id": "ca912a57-6a4b-4b9a-be0e-37b69d3e4917", "operator": {"type": "string", "operation": "contains"}, "leftValue": "={{ $json.data.name }}", "rightValue": "n8n account"}]}}, "typeVersion": 2.2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "8a89a054-697f-4705-89a8-5d3288936206", "connections": {"Globals": {"main": [[{"node": "Get all files in given path", "type": "main", "index": 0}]]}, "Split the result": {"main": [[{"node": "Get file content from GitHub", "type": "main", "index": 0}]]}, "Convert files to JSON": {"main": [[{"node": "Check for skipped Credentials", "type": "main", "index": 0}]]}, "Get all files in given path": {"main": [[{"node": "Split the result", "type": "main", "index": 0}]]}, "Get file content from GitHub": {"main": [[{"node": "Convert files to JSON", "type": "main", "index": 0}]]}, "Check for skipped Credentials": {"main": [[], [{"node": "Restore n8n Credentials", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Globals", "type": "main", "index": 0}]]}}}