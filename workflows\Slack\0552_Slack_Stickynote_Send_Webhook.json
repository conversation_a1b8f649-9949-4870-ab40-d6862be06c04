{"meta": {"instanceId": "84ba6d895254e080ac2b4916d987aa66b000f88d4d919a6b9c76848f9b8a7616", "templateId": "2370"}, "nodes": [{"id": "2ce91ec6-0a8c-438a-8a18-216001c9ee07", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [380, 240], "parameters": {"width": 407.6388140161723, "height": 490.24769122000794, "content": "## This is a POST Webhook endpoint\n\nMake sure to configure this webhook using a https:// wraper and dont use the default http://localhost:5678 as that will not be recognized by your slack webhook\n\n\nOnce the data has been sent to your webhook, the next step will be passing it via an AI Agent to process data based on the queries we pass to our agent.\n\nTo have some sort of a memory, be sure to set the slack token to the memory node. This way you can refer to other chats from the history.\n\nThe final message is relayed back to slack as a new message. Since we can not wait longer than 3000 ms for slack response, we will create anew message with reference to the input we passed.\n\nWe can advance this using the tools or data sources for it to be more custom tailored for your company.\n"}, "typeVersion": 1}, {"id": "7a0c84a8-90ef-4de8-b120-700c94c35a51", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1180, 560], "parameters": {"color": 4, "width": 221.73584905660368, "height": 233, "content": "### Conversation history is stored in memory using the body token as the chatsession id"}, "typeVersion": 1}, {"id": "9b843e0e-42a6-4125-8c59-a7d5620a15f7", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [942.5229110512129, 560], "parameters": {"color": 4, "width": 217.47708894878716, "height": 233, "content": "### The chat LLM to process the prompt. Use any AI model here"}, "typeVersion": 1}, {"id": "4efa968f-ebf5-42ec-80d3-907ef2622c61", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1020, 640], "parameters": {"options": {}, "modelName": "models/gemini-1.5-flash-latest"}, "typeVersion": 1}, {"id": "fd1efd7c-7cd0-4edf-960e-19bd4567293e", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [1260, 660], "parameters": {"sessionKey": "={{ $('Webhook to receive message').item.json.body.token }}", "sessionIdType": "customKey", "contextWindowLength": 10}, "typeVersion": 1.2}, {"id": "60d1eb77-492d-4a18-8cec-fa3f6ef8d707", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1467.5148247978436, 260], "parameters": {"color": 4, "width": 223.7196765498655, "height": 236.66152029520293, "content": "### Send the response from AI back to slack channel\n"}, "typeVersion": 1}, {"id": "186069c0-5c79-4738-9924-de33998658bc", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [840, 180], "parameters": {"color": 4, "width": 561.423180592992, "height": 340.09703504043114, "content": "## Receive a POST webhook, process data and return response"}, "typeVersion": 1}, {"id": "2bfce117-a769-46e1-a028-ed0c7ba62653", "name": "Send response back to slack channel", "type": "n8n-nodes-base.slack", "position": [1540, 320], "parameters": {"text": "={{ $('Webhook to receive message').item.json.body.user_name }}: {{ $('Webhook to receive message').item.json.body.text }}\n\nEffibotics Bot: {{ $json.output.removeMarkdown() }} ", "select": "channel", "channelId": {"__rl": true, "mode": "id", "value": "={{ $('Webhook to receive message').item.json.body.channel_id }}"}, "otherOptions": {"mrkdwn": true, "sendAsUser": "Effibotics Bot", "includeLinkToWorkflow": false}}, "typeVersion": 2.1}, {"id": "cfcf2bbc-8ed5-4a9f-8f35-cf2715686ebe", "name": "Webhook to receive message", "type": "n8n-nodes-base.webhook", "position": [880, 320], "webhookId": "28b84545-96aa-42f5-990b-aa8783a320ca", "parameters": {"path": "slack-bot", "options": {"responseData": ""}, "httpMethod": "POST"}, "typeVersion": 1}, {"id": "dc93e588-fc0b-4561-88a5-e1cccd48323f", "name": "Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1100, 320], "parameters": {"text": "={{ $json.body.text }}", "options": {"systemMessage": "You are Effibotics AI personal assistant. Your task will be to provide helpful assistance and advice related to automation and such tasks. "}}, "typeVersion": 1}], "pinData": {}, "connections": {"Agent": {"main": [[{"node": "Send response back to slack channel", "type": "main", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "Agent", "type": "ai_memory", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Agent", "type": "ai_languageModel", "index": 0}]]}, "Webhook to receive message": {"main": [[{"node": "Agent", "type": "main", "index": 0}]]}}}