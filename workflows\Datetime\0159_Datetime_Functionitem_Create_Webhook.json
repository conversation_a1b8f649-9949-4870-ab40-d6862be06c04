{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [240, 300], "parameters": {}, "typeVersion": 1}, {"name": "Item Lists", "type": "n8n-nodes-base.itemLists", "position": [1120, 300], "parameters": {"options": {}, "fieldToSplitOut": "post"}, "typeVersion": 1}, {"name": "Extract Posts", "type": "n8n-nodes-base.htmlExtract", "position": [900, 300], "parameters": {"options": {}, "extractionValues": {"values": [{"key": "post", "cssSelector": ".blog-listing__post-content", "returnArray": true, "returnValue": "html"}]}}, "typeVersion": 1}, {"name": "Fetch Website", "type": "n8n-nodes-base.httpRequest", "position": [680, 300], "parameters": {"url": "={{$json[\"base_domain\"]}}/blog/category/release", "options": {"timeout": 10000}, "responseFormat": "string"}, "typeVersion": 1}, {"name": "Set URL", "type": "n8n-nodes-base.set", "position": [460, 300], "parameters": {"values": {"string": [{"name": "base_domain", "value": "https://baserow.io"}]}, "options": {}}, "typeVersion": 1}, {"name": "Complete Link", "type": "n8n-nodes-base.set", "position": [240, 500], "parameters": {"values": {"string": [{"name": "link", "value": "={{$item(0).$node[\"Set URL\"].json[\"base_domain\"]}}{{$json[\"link\"]}}"}]}, "options": {}}, "typeVersion": 1}, {"name": "Format Date", "type": "n8n-nodes-base.dateTime", "position": [460, 500], "parameters": {"value": "={{$json[\"date\"]}}", "options": {}, "toFormat": "YYYY-MM-DD", "dataPropertyName": "date"}, "typeVersion": 1}, {"name": "Create RSS Items", "type": "n8n-nodes-base.functionItem", "position": [680, 500], "parameters": {"functionCode": "return {\n  rss_item: \n`<item>\n  <title>${item.title}</title>\n  <link>${item.link}</link>\n  <description>${item.description}</description>\n  <pubDate>${item.date}</pubDate>\n</item>`\n}"}, "typeVersion": 1}, {"name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [240, 100], "webhookId": "27c1e4db-568f-4bf9-9474-0898ce1173f7", "parameters": {"path": "baserow-releases", "options": {}, "responseMode": "responseNode"}, "typeVersion": 1}, {"name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [1120, 500], "parameters": {"options": {"responseHeaders": {"entries": [{"name": "content-type", "value": "application/xml"}]}}, "respondWith": "text", "responseBody": "={{$json[\"feed\"]}}"}, "typeVersion": 1}, {"name": "Prepare Response", "type": "n8n-nodes-base.function", "position": [900, 500], "parameters": {"functionCode": "let feed =\n`<?xml version=\"1.0\" encoding=\"UTF-8\" ?>\n<rss version=\"2.0\">\n\n<channel>\n  <title>Baserow Releases</title>\n  <link>https://baserow.io/blog/category/release</link>\n  <description>Stay up to date with the latest changes and updates of Baserow</description>\n  ${items.map(e => e.json.rss_item).join('\\n')}\n</channel>\n\n</rss>`;\n\nreturn [{\n  json: {\n    feed: feed\n  }\n}];"}, "typeVersion": 1}, {"name": "Extract Fields", "type": "n8n-nodes-base.htmlExtract", "position": [1340, 300], "parameters": {"options": {}, "dataPropertyName": "post", "extractionValues": {"values": [{"key": "date", "cssSelector": ".blog-listing__post-info > strong"}, {"key": "title", "cssSelector": ".blog-listing__post-title"}, {"key": "link", "attribute": "href", "cssSelector": ".blog-listing__post-title > a", "returnValue": "attribute"}, {"key": "description", "cssSelector": ".blog-listing__post-description"}]}}, "typeVersion": 1}], "connections": {"Set URL": {"main": [[{"node": "Fetch Website", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Set URL", "type": "main", "index": 0}]]}, "Item Lists": {"main": [[{"node": "Extract Fields", "type": "main", "index": 0}]]}, "Format Date": {"main": [[{"node": "Create RSS Items", "type": "main", "index": 0}]]}, "Complete Link": {"main": [[{"node": "Format Date", "type": "main", "index": 0}]]}, "Extract Posts": {"main": [[{"node": "Item Lists", "type": "main", "index": 0}]]}, "Fetch Website": {"main": [[{"node": "Extract Posts", "type": "main", "index": 0}]]}, "Extract Fields": {"main": [[{"node": "Complete Link", "type": "main", "index": 0}]]}, "Create RSS Items": {"main": [[{"node": "Prepare Response", "type": "main", "index": 0}]]}, "Prepare Response": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Set URL", "type": "main", "index": 0}]]}}}