{"id": "3", "name": "XML Conversion", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [510, 300], "parameters": {"values": {"string": [{"name": "xml", "value": "<?xml version=\"1.0\" encoding=\"utf-8\"?> <ORDERS05>   <IDOC BEGIN=\"1\">     <EDI_DC40 SEGMENT=\"1\">       <TABNAM>EDI_DC40</TABNAM>     </EDI_DC40>   </IDOC> </ORDERS05>"}]}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "XML", "type": "n8n-nodes-base.xml", "position": [740, 300], "parameters": {"options": {"attrkey": "$", "mergeAttrs": false, "explicitRoot": true}, "dataPropertyName": "xml"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Set": {"main": [[{"node": "XML", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}}