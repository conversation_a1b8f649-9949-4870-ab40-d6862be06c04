{"id": "FU3MrLkaTHmfdG4n", "meta": {"instanceId": "3294023dd650d95df294922b9d55d174ef26f4a2e6cce97c8a4ab5f98f5b8c7b", "templateCredsSetupCompleted": true}, "name": "Hugging Face  to Notion", "tags": [], "nodes": [{"id": "32d5bfee-97f1-4e92-b62e-d09bdd9c3821", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-2640, -300], "parameters": {"rule": {"interval": [{"field": "weeks", "triggerAtDay": [1, 2, 3, 4, 5], "triggerAtHour": 8}]}}, "typeVersion": 1.2}, {"id": "b1f4078e-ac77-47ec-995c-f52fd98fafef", "name": "If", "type": "n8n-nodes-base.if", "position": [-1360, -280], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "7094d6db-1fa7-4b59-91cf-6bbd5b5f067e", "operator": {"type": "object", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "afac08e1-b629-4467-86ef-907e4a5e8841", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [-1760, -300], "parameters": {"options": {"reset": false}}, "typeVersion": 3}, {"id": "807ba450-9c89-4f88-aa84-91f43e3adfc6", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [-1960, -300], "parameters": {"options": {}, "fieldToSplitOut": "url, url"}, "typeVersion": 1}, {"id": "08dd3f15-2030-48f2-ab0f-f85f797268e1", "name": "Request Hugging Face Paper", "type": "n8n-nodes-base.httpRequest", "position": [-2440, -300], "parameters": {"url": "https://huggingface.co/papers", "options": {}, "sendQuery": true, "queryParameters": {"parameters": [{"name": "date", "value": "={{ $now.minus(1,'days').format('yyyy-MM-dd') }}"}]}}, "typeVersion": 4.2}, {"id": "f37ba769-d881-4aad-927d-ca1f4a68b9a1", "name": "Extract Hugging Face Paper", "type": "n8n-nodes-base.html", "position": [-2200, -300], "parameters": {"options": {}, "operation": "extractHtmlContent", "extractionValues": {"values": [{"key": "url", "attribute": "href", "cssSelector": ".line-clamp-3", "returnArray": true, "returnValue": "attribute"}]}}, "typeVersion": 1.2}, {"id": "94ba99bf-a33b-4311-a4e6-86490e1bb9ad", "name": "Check Paper URL Existed", "type": "n8n-nodes-base.notion", "position": [-1540, -280], "parameters": {"filters": {"conditions": [{"key": "URL|url", "urlValue": "={{ 'https://huggingface.co'+$json.url }}", "condition": "equals"}]}, "options": {}, "resource": "databasePage", "operation": "getAll", "databaseId": {"__rl": true, "mode": "list", "value": "17b67aba-1fcc-80ae-baa1-d88ffda7ae83", "cachedResultUrl": "https://www.notion.so/17b67aba1fcc80aebaa1d88ffda7ae83", "cachedResultName": "huggingface-abstract"}, "filterType": "manual"}, "credentials": {"notionApi": {"id": "I5KdUzwhWnphQ862", "name": "notion"}}, "typeVersion": 2.2, "alwaysOutputData": true}, {"id": "ece8dee2-e444-4557-aad9-5bdcb5ecd756", "name": "Request Hugging Face Paper Detail", "type": "n8n-nodes-base.httpRequest", "position": [-1080, -300], "parameters": {"url": "={{ 'https://huggingface.co'+$('Split Out').item.json.url }}", "options": {}}, "typeVersion": 4.2}, {"id": "53b266fe-e7c4-4820-92eb-78a6ba7a6430", "name": "OpenAI Analysis Abstract", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [-640, -300], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-2024-11-20", "cachedResultName": "GPT-4O-2024-11-20"}, "options": {}, "messages": {"values": [{"role": "system", "content": "Extract the following key details from the paper abstract:\n\nCore Introduction: Summarize the main contributions and objectives of the paper, highlighting its innovations and significance.\nKeyword Extraction: List 2-5 keywords that best represent the research direction and techniques of the paper.\nKey Data and Results: Extract important performance metrics, comparison results, and the paper's advantages over other studies.\nTechnical Details: Provide a brief overview of the methods, optimization techniques, and datasets mentioned in the paper.\nClassification: Assign an appropriate academic classification based on the content of the paper.\n\n\nOutput as json：\n{\n  \"Core_Introduction\": \"PaSa is an advanced Paper Search agent powered by large language models that can autonomously perform a series of decisions (including invoking search tools, reading papers, and selecting relevant references) to provide comprehensive and accurate results for complex academic queries.\",\n  \"Keywords\": [\n    \"Paper Search Agent\",\n    \"Large Language Models\",\n    \"Reinforcement Learning\",\n    \"Academic Queries\",\n    \"Performance Benchmarking\"\n  ],\n  \"Data_and_Results\": \"PaSa outperforms existing baselines (such as Google, GPT-4, chatGPT) in tests using AutoScholarQuery (35k academic queries) and RealScholarQuery (real-world academic queries). For example, PaSa-7B exceeds Google with GPT-4o by 37.78% in recall@20 and 39.90% in recall@50.\",\n  \"Technical_Details\": \"PaSa is optimized using reinforcement learning with the AutoScholarQuery synthetic dataset, demonstrating superior performance in multiple benchmarks.\",\n  \"Classification\": [\n    \"Artificial Intelligence (AI)\",\n    \"Academic Search and Information Retrieval\",\n    \"Natural Language Processing (NLP)\",\n    \"Reinforcement Learning\"\n  ]\n}\n```"}, {"content": "={{ $json.abstract }}"}]}, "jsonOutput": true}, "credentials": {"openAiApi": {"id": "LmLcxHwbzZNWxqY6", "name": "Unnamed credential"}}, "typeVersion": 1.8}, {"id": "f491cd7f-598e-46fd-b80c-04cfa9766dfd", "name": "Store Abstract Notion", "type": "n8n-nodes-base.notion", "position": [-300, -300], "parameters": {"options": {}, "resource": "databasePage", "databaseId": {"__rl": true, "mode": "list", "value": "17b67aba-1fcc-80ae-baa1-d88ffda7ae83", "cachedResultUrl": "https://www.notion.so/17b67aba1fcc80aebaa1d88ffda7ae83", "cachedResultName": "huggingface-abstract"}, "propertiesUi": {"propertyValues": [{"key": "URL|url", "urlValue": "={{ 'https://huggingface.co'+$('Split Out').item.json.url }}"}, {"key": "title|title", "title": "={{ $('Extract Hugging Face Paper Abstract').item.json.title }}"}, {"key": "abstract|rich_text", "textContent": "={{ $('Extract Hugging Face Paper Abstract').item.json.abstract.substring(0,2000) }}"}, {"key": "scrap-date|date", "date": "={{  $today.format('yyyy-MM-dd')  }}", "includeTime": false}, {"key": "Classification|rich_text", "textContent": "={{ $json.message.content.Classification.join(',') }}"}, {"key": "Technical_Details|rich_text", "textContent": "={{ $json.message.content.Technical_Details }}"}, {"key": "Data_and_Results|rich_text", "textContent": "={{ $json.message.content.Data_and_Results }}"}, {"key": "keywords|rich_text", "textContent": "={{ $json.message.content.Keywords.join(',') }}"}, {"key": "Core Introduction|rich_text", "textContent": "={{ $json.message.content.Core_Introduction }}"}]}}, "credentials": {"notionApi": {"id": "I5KdUzwhWnphQ862", "name": "notion"}}, "typeVersion": 2.2}, {"id": "d5816a1c-d1fa-4be2-8088-57fbf68e6b43", "name": "Extract Hugging Face Paper Abstract", "type": "n8n-nodes-base.html", "position": [-840, -300], "parameters": {"options": {}, "operation": "extractHtmlContent", "extractionValues": {"values": [{"key": "abstract", "cssSelector": ".text-gray-700"}, {"key": "title", "cssSelector": ".text-2xl"}]}}, "typeVersion": 1.2}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "4b0ec2a3-253d-46d5-a4d4-1d9ff21ba4a3", "connections": {"If": {"main": [[{"node": "Request Hugging Face Paper Detail", "type": "main", "index": 0}], [{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Check Paper URL Existed", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Request Hugging Face Paper", "type": "main", "index": 0}]]}, "Store Abstract Notion": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Check Paper URL Existed": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "OpenAI Analysis Abstract": {"main": [[{"node": "Store Abstract Notion", "type": "main", "index": 0}]]}, "Extract Hugging Face Paper": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Request Hugging Face Paper": {"main": [[{"node": "Extract Hugging Face Paper", "type": "main", "index": 0}]]}, "Request Hugging Face Paper Detail": {"main": [[{"node": "Extract Hugging Face Paper Abstract", "type": "main", "index": 0}]]}, "Extract Hugging Face Paper Abstract": {"main": [[{"node": "OpenAI Analysis Abstract", "type": "main", "index": 0}]]}}}