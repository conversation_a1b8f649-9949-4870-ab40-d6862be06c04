{"meta": {"instanceId": "257476b1ef58bf3cb6a46e65fac7ee34a53a5e1a8492d5c6e4da5f87c9b82833"}, "nodes": [{"id": "332e7401-26ac-4ef0-a93c-1290454ffce4", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-3180, 820], "parameters": {"color": 5, "width": 654.1162790697673, "height": 144.52300171817149, "content": "### 👨‍🎤 Setup\n1. Add your **Hunter.io**, **Clearbit** and **Pipedrive** credentials \n2. Click the test workflow button\n3. Activate the workflow and use the form trigger production URL to collect your leads in a smart way "}, "typeVersion": 1}, {"id": "59c576d8-0fd2-45e4-849a-67a1125cf61e", "name": "n8n Form Trigger", "type": "n8n-nodes-base.formTrigger", "position": [-3120, 1000], "webhookId": "09f63412-7c4a-4752-93cd-ff1c87774226", "parameters": {"path": "0bf8840f-1cc4-46a9-86af-a3fa8da80608", "options": {}, "formTitle": "Contact us", "formFields": {"values": [{"fieldLabel": "What's your business email?"}]}, "formDescription": "We'll get back to you soon"}, "typeVersion": 2}, {"id": "963caa71-9919-4d14-837d-b879d41a8f93", "name": "Check if the email is valid", "type": "n8n-nodes-base.if", "position": [-2700, 1000], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "54d84c8a-63ee-40ed-8fb2-301fff0194ba", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "valid"}]}}, "typeVersion": 2}, {"id": "678db529-69f1-423a-b551-2321590b878a", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-3120, 1140], "parameters": {"color": 7, "width": 162, "height": 139, "content": "👆 You can exchange this with any form you like (*e.g. Typeform, Google forms, Survey Monkey...*)"}, "typeVersion": 1}, {"id": "bdd1848c-9b44-4476-9655-be7cb7ac377b", "name": "Email is not valid, do nothing", "type": "n8n-nodes-base.noOp", "position": [-2460, 1120], "parameters": {}, "typeVersion": 1}, {"id": "********-029c-4654-9fba-09463e639eaf", "name": "Verify email with <PERSON>", "type": "n8n-nodes-base.hunter", "position": [-2900, 1000], "parameters": {"email": "={{ $json['What\\'s your business email?'] }}", "operation": "emailVerifier"}, "credentials": {"hunterApi": {"id": "oIxKoEBTBJeT1UrT", "name": "Hunter account"}}, "typeVersion": 1}, {"id": "54ea0ab7-6c36-4724-a897-90f5786cc023", "name": "Clearbit", "type": "n8n-nodes-base.clearbit", "position": [-2040, 900], "parameters": {"email": "={{ $('Check if the email is valid').item.json.email }}", "resource": "person", "additionalFields": {}}, "credentials": {"clearbitApi": {"id": "cKDImrinp9tg0ZHW", "name": "Clearbit account"}}, "typeVersion": 1}, {"id": "31a7c331-73ab-4704-87ea-ce2d5e57bb7b", "name": "Person already exists in Pipedrive, do nothing", "type": "n8n-nodes-base.noOp", "position": [-2040, 1120], "parameters": {}, "typeVersion": 1}, {"id": "6add279c-0408-4df0-b382-b399a33f633a", "name": "Is this a new person?", "type": "n8n-nodes-base.if", "position": [-2240, 920], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "f1094c47-4084-4268-9026-ccc0335eeccf", "operator": {"type": "number", "operation": "notExists", "singleValue": true}, "leftValue": "={{ $json.id }}", "rightValue": ""}]}}, "executeOnce": true, "typeVersion": 2}, {"id": "ad3a1538-a5a9-4e94-9c69-557363ae9490", "name": "Search for person in Pipedrive", "type": "n8n-nodes-base.pipedrive", "position": [-2460, 920], "parameters": {"term": "={{ $json.email }}", "resource": "person", "operation": "search", "additionalFields": {}}, "credentials": {"pipedriveApi": {"id": "M3l7gIG8DdOex6wX", "name": "Pipedrive account"}}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "2515f2e1-0acd-43f8-9868-6a94830aaebd", "name": "Is this a new organization?", "type": "n8n-nodes-base.if", "position": [-1660, 900], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "f1094c47-4084-4268-9026-ccc0335eeccf", "operator": {"type": "number", "operation": "notExists", "singleValue": true}, "leftValue": "={{ $json.id }}", "rightValue": ""}]}}, "executeOnce": true, "typeVersion": 2}, {"id": "2933eba8-d5fa-4178-8c9e-b330f6f3a529", "name": "Create Organization", "type": "n8n-nodes-base.pipedrive", "position": [-1460, 780], "parameters": {"name": "={{ $('Clearbit').item.json.employment.name }}", "resource": "organization", "additionalFields": {}}, "credentials": {"pipedriveApi": {"id": "M3l7gIG8DdOex6wX", "name": "Pipedrive account"}}, "typeVersion": 1}, {"id": "8814f8f9-7dac-4cf3-8743-8ee9beb58b7c", "name": "Search for organization in Pipedrive", "type": "n8n-nodes-base.pipedrive", "position": [-1820, 900], "parameters": {"term": "={{ $json.employment.name }}", "resource": "organization", "operation": "search", "additionalFields": {}}, "credentials": {"pipedriveApi": {"id": "M3l7gIG8DdOex6wX", "name": "Pipedrive account"}}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "13af2942-ad5e-4ad4-8b2d-************", "name": "Create Person", "type": "n8n-nodes-base.pipedrive", "position": [-1280, 920], "parameters": {"name": "={{ $('Clearbit').item.json.name.fullName }}", "resource": "person", "additionalFields": {"email": ["={{ $('Clearbit').item.json.email }}"], "org_id": "={{ $json.id }}"}}, "credentials": {"pipedriveApi": {"id": "M3l7gIG8DdOex6wX", "name": "Pipedrive account"}}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "ed923d21-abfe-4b60-8d1b-5f976a56dbbe", "name": "Create lead", "type": "n8n-nodes-base.pipedrive", "position": [-1120, 920], "parameters": {"title": "={{ $json.name }} from {{ $json.org_id.name }}", "resource": "lead", "person_id": "={{ $json.id }}", "associateWith": "person", "additionalFields": {"organization_id": "={{ $json.org_id.value }}"}}, "credentials": {"pipedriveApi": {"id": "M3l7gIG8DdOex6wX", "name": "Pipedrive account"}}, "typeVersion": 1}], "pinData": {}, "connections": {"Clearbit": {"main": [[{"node": "Search for organization in Pipedrive", "type": "main", "index": 0}]]}, "Create Person": {"main": [[{"node": "Create lead", "type": "main", "index": 0}]]}, "n8n Form Trigger": {"main": [[{"node": "Verify email with <PERSON>", "type": "main", "index": 0}]]}, "Create Organization": {"main": [[{"node": "Create Person", "type": "main", "index": 0}]]}, "Is this a new person?": {"main": [[{"node": "Clearbit", "type": "main", "index": 0}], [{"node": "Person already exists in Pipedrive, do nothing", "type": "main", "index": 0}]]}, "Verify email with Hunter": {"main": [[{"node": "Check if the email is valid", "type": "main", "index": 0}]]}, "Check if the email is valid": {"main": [[{"node": "Search for person in Pipedrive", "type": "main", "index": 0}], [{"node": "Email is not valid, do nothing", "type": "main", "index": 0}]]}, "Is this a new organization?": {"main": [[{"node": "Create Organization", "type": "main", "index": 0}], [{"node": "Create Person", "type": "main", "index": 0}]]}, "Search for person in Pipedrive": {"main": [[{"node": "Is this a new person?", "type": "main", "index": 0}]]}, "Search for organization in Pipedrive": {"main": [[{"node": "Is this a new organization?", "type": "main", "index": 0}]]}}}