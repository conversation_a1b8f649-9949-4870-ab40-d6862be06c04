{"id": 1, "name": "Google Cal to Zoom meeting", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [0, 330], "parameters": {}, "typeVersion": 1}, {"name": "Zoom", "type": "n8n-nodes-base.zoom", "position": [380, 410], "parameters": {"topic": "=Meeting with {{$node[\"IF Zoom meeting\"].json[\"summary\"]}}", "authentication": "oAuth2", "additionalFields": {"duration": "={{(Date.parse($node[\"IF Zoom meeting\"].json[\"end\"][\"dateTime\"])-Date.parse($node[\"IF Zoom meeting\"].json[\"start\"][\"dateTime\"]))/(60*1000)}}", "settings": {}, "timeZone": "={{$node[\"IF Zoom meeting\"].json[\"start\"][\"timeZone\"]}}", "startTime": "={{$node[\"IF Zoom meeting\"].json[\"start\"][\"dateTime\"]}}"}}, "credentials": {"zoomOAuth2Api": {"id": "3", "name": "Zoom account"}}, "typeVersion": 1}, {"name": "Date & Time", "type": "n8n-nodes-base.dateTime", "position": [200, 230], "parameters": {"value": "={{new Date().toISOString()}}", "action": "calculate", "options": {}, "duration": 12, "timeUnit": "hours", "dataPropertyName": "later"}, "typeVersion": 1}, {"name": "Google Calendar", "type": "n8n-nodes-base.googleCalendar", "position": [350, 230], "parameters": {"options": {"timeMax": "={{$node[\"Date & Time\"].json[\"later\"]}}", "timeMin": "={{new Date(new Date().getTime() + (0 * 60 * 60 * 1000)).toISOString()}}", "singleEvents": true}, "calendar": "REPLACE_WITH_CALENDAR_ID", "operation": "getAll"}, "credentials": {"googleCalendarOAuth2Api": {"id": "1", "name": "Google Calendar account"}}, "typeVersion": 1}, {"name": "IF Zoom meeting", "type": "n8n-nodes-base.if", "notes": "filters out:\n- existing Zoom meetings made by <PERSON><PERSON><PERSON>\n- in person zoom meetings\n- signal meetings\n- canceled Calendly meetings (\"transparent\")", "position": [180, 430], "parameters": {"conditions": {"string": [{"value1": "={{$node[\"Google Calendar\"].json[\"transparency\"]}}", "value2": "transparent", "operation": "notContains"}, {"value1": "={{$node[\"Google Calendar\"].json[\"summary\"]}}", "value2": "=signal", "operation": "notContains"}, {"value1": "{{$node[\"Google Calendar\"].json[\"summary\"]}}", "value2": "minute meeting", "operation": "notContains"}, {"value1": "={{$node[\"Google Calendar\"].json[\"summary\"]}}", "value2": "in person", "operation": "notContains"}], "boolean": []}}, "typeVersion": 1}, {"name": "Cron Once a Day", "type": "n8n-nodes-base.cron", "position": [0, 170], "parameters": {"triggerTimes": {"item": [{"hour": 7}]}}, "typeVersion": 1}], "active": true, "settings": {}, "connections": {"Date & Time": {"main": [[{"node": "Google Calendar", "type": "main", "index": 0}]]}, "Cron Once a Day": {"main": [[{"node": "Date & Time", "type": "main", "index": 0}]]}, "Google Calendar": {"main": [[{"node": "IF Zoom meeting", "type": "main", "index": 0}]]}, "IF Zoom meeting": {"main": [[{"node": "Zoom", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Date & Time", "type": "main", "index": 0}]]}}}