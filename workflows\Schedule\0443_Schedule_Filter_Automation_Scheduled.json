{"nodes": [{"id": "b1afd02d-6edf-4540-bf32-09d87cb8a27b", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [320, 220], "parameters": {"color": 5, "width": 379, "height": 80, "content": "### 👨‍🎤 Setup\n1. Add your Gmail creds"}, "typeVersion": 1}, {"id": "3481e6c3-7706-4c7f-8ca6-c96f76d82021", "name": "At midnight every work day", "type": "n8n-nodes-base.scheduleTrigger", "position": [400, 340], "parameters": {"rule": {"interval": [{"field": "cronExpression", "expression": "0 0 * * 1-5"}]}}, "typeVersion": 1}, {"id": "3c74e4fd-e919-4acb-8092-658f2e71513b", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [380, 520], "parameters": {"color": 7, "width": 202, "height": 100, "content": "👆 Set your schedule. I use this for work emails. For personal emails, I run this daily."}, "typeVersion": 1}, {"id": "de421702-d012-4ea1-826e-1a4756ff4856", "name": "Get all emails in the last day", "type": "n8n-nodes-base.gmail", "position": [620, 340], "parameters": {"filters": {"q": "label:inbox", "receivedBefore": "={{ $now.minus({days: 1}) }}"}, "resource": "thread", "returnAll": true}, "credentials": {"gmailOAuth2": {"id": "8", "name": "Work Gmail account"}}, "retryOnFail": true, "typeVersion": 2}, {"id": "ef43b756-5f9c-4c8d-830a-8ccb71562618", "name": "Get the thread of each email", "type": "n8n-nodes-base.gmail", "position": [840, 340], "parameters": {"options": {}, "resource": "thread", "threadId": "={{ $json.id }}", "operation": "get"}, "credentials": {"gmailOAuth2": {"id": "8", "name": "Work Gmail account"}}, "retryOnFail": true, "typeVersion": 2}, {"id": "bfc3b7e1-651a-4eb5-8882-b21d120d982b", "name": "Keep only starred emails in inbox", "type": "n8n-nodes-base.filter", "position": [1060, 340], "parameters": {"conditions": {"boolean": [{"value1": "={{ JSON.stringify($json.messages).includes('STARRED') }}"}]}}, "typeVersion": 1}, {"id": "3d8145dc-577d-4e9b-83a7-fdf06afa1b96", "name": "for each message in the thread", "type": "n8n-nodes-base.itemLists", "position": [1480, 520], "parameters": {"options": {}, "fieldToSplitOut": "messages"}, "typeVersion": 2}, {"id": "1a9083a8-ffd2-403e-bf53-9b9eee87ff5b", "name": "Archive message (remove from inbox)", "type": "n8n-nodes-base.gmail", "position": [1700, 520], "parameters": {"labelIds": "=INBOX", "messageId": "={{ $json.id }}", "operation": "<PERSON><PERSON><PERSON><PERSON>"}, "credentials": {"gmailOAuth2": {"id": "8", "name": "Work Gmail account"}}, "retryOnFail": true, "typeVersion": 2}, {"id": "c51240d0-88cb-461b-82ba-929a2d8a9dde", "name": "Archive thread  (remove from inbox)", "type": "n8n-nodes-base.gmail", "position": [1340, 300], "parameters": {"labelIds": "=INBOX", "resource": "thread", "threadId": "={{ $json.id }}", "operation": "<PERSON><PERSON><PERSON><PERSON>"}, "credentials": {"gmailOAuth2": {"id": "8", "name": "Work Gmail account"}}, "retryOnFail": true, "typeVersion": 2}, {"id": "3ca7074f-c912-456c-92e4-08cac8833471", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1060, 520], "parameters": {"color": 7, "width": 202, "height": 100, "content": "⭐ Keep starred emails in inbox.. Archive everything else!"}, "typeVersion": 1}], "pinData": {}, "connections": {"At midnight every work day": {"main": [[{"node": "Get all emails in the last day", "type": "main", "index": 0}]]}, "Get the thread of each email": {"main": [[{"node": "Keep only starred emails in inbox", "type": "main", "index": 0}]]}, "Get all emails in the last day": {"main": [[{"node": "Get the thread of each email", "type": "main", "index": 0}]]}, "for each message in the thread": {"main": [[{"node": "Archive message (remove from inbox)", "type": "main", "index": 0}]]}, "Keep only starred emails in inbox": {"main": [[{"node": "Archive thread  (remove from inbox)", "type": "main", "index": 0}, {"node": "for each message in the thread", "type": "main", "index": 0}]]}}}