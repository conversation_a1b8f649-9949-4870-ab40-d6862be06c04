{"meta": {"instanceId": "1eadd5bc7c3d70c587c28f782511fd898c6bf6d97963d92e836019d2039d1c79"}, "nodes": [{"id": "bee233ee-7212-4fbd-b151-0bb49919ca42", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [0, 280], "parameters": {"color": 4, "width": 1289, "height": 4398, "content": "LinkedIn Job Data Scraper to Google Sheets\nScrape live job posts from LinkedIn via Bright Data, clean them, and send to Google Sheets. Use for:\n✅ Job Hunting — fresh, filtered roles\n✅ Sales Prospecting — find companies hiring (aka growing)\n⚙️ What's Used\nn8n Nodes: Form → HTTP Request → Wait → If → Code → Google Sheets\nExternal Tools:\n\nBright Data – Dataset API\nGoogle Sheets – Template Copy\n\n🔑 Setup – Credentials Needed\n\nBright Data API Key → Add to HTTP headers as: Authorization: Bearer YOUR_KEY\nGoogle Sheets OAuth2 → Connect account in n8n\n\n📝 Input Form – Fields\nUsed to define what job data to scrape.\nFieldDescriptionExampleLocationCity/regionNew York, BerlinKeywordRole to searchCMO, Data AnalystCountry2-letter ISO codeUS, UK, DETime RangeHow recent the jobs should be\"Past 24 hours\" or \"Last 7 days\" (recommended)Job TypeFull-time / Part-time / Contract(Optional)ExperienceEntry, Mid, Senior(Optional)RemoteFilter by remote-friendly roles(Optional)CompanyFilter by specific employer(Optional)\n🚀 Workflow Steps\n\nUser fills input form\nTrigger snapshot via Bright Data Dataset API\nWait node + polling checks when data is ready (~1–3 mins)\nCleanup step:\n\nFlattens nested fields (job_poster, base_salary, etc.)\nRemoves HTML from job descriptions\n\n\nSend to Google Sheet\n\nSheet is pre-linked, 1 job per row\nExample columns: job_title, company_name, location, salary_min, apply_link, job_description_plain\n\n\nYou use the data\n\nJob seekers → Apply fast\nSalespeople → Spot buyers & offer help\n\n\n\n💡 Pro Tips\n\nUse \"Past 24 hours\" or \"Last 7 days\" for fresher results\nLeave filters empty if unsure — Bright Data will return broader results\nUse cleaned data for:\n\nCold email personalization\nLinkedIn outreach\nBuilding ICP-based lead lists\n\n\n\n🧪 Example API Body\njson[ \n  { \n    \"location\": \"New York\", \n    \"keyword\": \"Marketing Manager\", \n    \"country\": \"US\", \n    \"time_range\": \"Past 24 hours\", \n    \"job_type\": \"Part-time\", \n    \"experience_level\": \"\", \n    \"remote\": \"\", \n    \"company\": \"\" \n  } \n]\n📄 Template & Resources\n\n📋 Google Sheet Template (Make a Copy)\n📘 Bright Data API Reference\n\n🛠️ Customize It\n\nAdd filters to HTTP Body (remote, experience_level, etc.)\nChange polling interval if Bright Data is slow\nAdd custom logic to score/prioritize listings\nSend filtered lists to CRM, Slack, etc.\n\nThis gives you a live stream of hiring signals — whether you're finding a job or pitching a service. One form. One click. Fully automated."}, "typeVersion": 1}, {"id": "0fa9d0fe-b3ba-48be-99b9-2bc3aeb18b43", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [0, -60], "parameters": {"color": 4, "width": 1300, "height": 320, "content": "=======================================\n            WORKFLOW ASSISTANCE\n=======================================\nFor any questions or support, please contact:\n    <EMAIL>\n\nExplore more tips and tutorials here:\n   - YouTube: https://www.youtube.com/@YaronBeen/videos\n   - LinkedIn: https://www.linkedin.com/in/yaronbeen/\n=======================================\n"}, "typeVersion": 1}, {"id": "33cb416e-a7ff-4b55-9701-9b9e95d76f12", "name": "Snapshot Progress", "type": "n8n-nodes-base.httpRequest", "position": [2840, 360], "parameters": {"url": "=https://api.brightdata.com/datasets/v3/progress/{{ $('HTTP Request- Post API call to Bright Data').item.json.snapshot_id }}", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer <YOUR_BRIGHT_DATA_API_KEY>"}]}}, "typeVersion": 4.2}, {"id": "6b8c9405-8f8c-4a24-85ca-343d33e06141", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [3680, 140], "parameters": {"width": 195, "height": 646, "content": "In this workflow, I use Google Sheets to store the results. \n\nYou can use my template to get started faster:\n\n1. [Click on this link to get the template](https://docs.google.com/spreadsheets/d/1_jbr5zBllTy_pGbogfGSvyv1_0a77I8tU-Ai7BjTAw4/edit?usp=sharing)\n2. Make a copy of the Sheets\n3. Add the URL to this node \n\n\n"}, "typeVersion": 1}, {"id": "3d3cd92a-9ea7-4a4f-a9b5-aae689f719e5", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1320, -60], "parameters": {"width": 480, "height": 2240, "content": "# 🔍 LinkedIn Jobs API – Parameter Guide\n\nUse this object to query LinkedIn job listings.  \nEach field lets you filter results based on different criteria.\n\n```json\n{\n  \"location\": \"{{ $json.Location }}\",\n  \"keyword\": \"{{ $json.Keyword }}\",\n  \"country\": \"{{ $json.Country }}\",\n  \"time_range\": \"Past 24 hours\",\n  \"job_type\": \"Part-time\",\n  \"experience_level\": \"\",\n  \"remote\": \"\",\n  \"company\": \"\"\n}\n```\n\n## 🧾 Field Explanations & Valid Options\n\n### 🗺️ location\nCity or region where the job is located.\nUse a city or region name.\n✅ Example: \"Berlin\", \"New York\"\n\n### 🧠 keyword\nJob title or search keywords.\nUse terms like role names or skills.\n✅ Example: \"Data Scientist\", \"Growth Marketing\"\n\n### 🌍 country\nCountry code in ISO 3166-1 alpha-2 format.\n✅ Example: \"US\", \"DE\", \"IL\"\n\n### ⏱️ time_range\nPosting date filter.\nLimits results based on how recently jobs were posted.\nAccepted values:\n- Any Time\n- Past 24 hours\n- Past Week\n- Past Month\n✅ Example: \"Past Week\"\n\n### 💼 job_type\nType of employment.\nUse a single value or comma-separated list.\nAccepted values:\n- Full-time\n- Part-time\n- Contract\n- Temporary\n- Internship\n- Volunteer\n- Other\n✅ Example: \"Full-time,Contract\"\n\n### 🎯 experience_level\nSeniority level of the job.\nAccepted values:\n- Internship\n- Entry level\n- Associate\n- Mid-Senior level\n- Director\n- Executive\n✅ Example: \"Mid-Senior level\"\n\n### 🌐 remote\nWorkplace type.\nAccepted values:\n- Remote\n- On-site\n- Hybrid\n- (leave blank for no preference)\n✅ Example: \"Remote\"\n\n### 🏢 company\nFilter by company name.\nOptional. Use plain text.\n✅ Example: \"Google\", \"Spotify\"\n\n## ✅ Full Example\n\n```json\n{\n  \"location\": \"New York\",\n  \"keyword\": \"UI Designer\",\n  \"country\": \"US\",\n  \"time_range\": \"Past Week\",\n  \"job_type\": \"Full-time,Contract\",\n  \"experience_level\": \"Mid-Senior level\",\n  \"remote\": \"Hybrid\",\n  \"company\": \"Spotify\"\n}\n```\n\n"}, "typeVersion": 1}, {"id": "1d7a7bb0-1531-4516-9373-5e85a090b143", "name": "On form submission - Discover Jobs", "type": "n8n-nodes-base.formTrigger", "position": [1700, 580], "webhookId": "8d0269c7-d1fc-45a1-a411-19634a1e0b82", "parameters": {"options": {}, "formTitle": "Linkedin High Intent Prospects And Job Post Hunt", "formFields": {"values": [{"fieldLabel": "Job Location", "placeholder": "example: new york", "requiredField": true}, {"fieldLabel": "Keyword", "placeholder": "example: <PERSON><PERSON>, AI architect", "requiredField": true}, {"fieldLabel": "Country (2 letters)", "placeholder": "example: US,UK,IL", "requiredField": true}]}, "formDescription": "This form lets you customize your job search / prospecting by choosing:\n\nLocation (city or region)\n\nJob title or keywords\n\nCountry code\n\nFilters like posting date, job type, experience level, and remote options\n\nYou can also optionally narrow results by company name.\n\n🧠 Tip: Leave fields blank if you want broader results."}, "typeVersion": 2.2}, {"id": "aea569df-eedd-441f-aba5-c3c26a50fa87", "name": "HTTP Request- Post API call to Bright Data", "type": "n8n-nodes-base.httpRequest", "position": [2260, 620], "parameters": {"url": "https://api.brightdata.com/datasets/v3/trigger", "method": "POST", "options": {}, "jsonBody": "=[\n  {\n    \"location\": \"{{ $json['Job Location'] }}\",\n    \"keyword\": \"{{ $json.Keyword }}\",\n    \"country\": \"{{ $json['Country (2 letters)'] }}\",\n    \"time_range\": \"Past 24 hours\",\n    \"job_type\": \"Part-time\",\n    \"experience_level\": \"\",\n    \"remote\": \"\",\n    \"company\": \"\"\n  }\n] ", "sendBody": true, "sendQuery": true, "sendHeaders": true, "specifyBody": "json", "queryParameters": {"parameters": [{"name": "dataset_id", "value": "gd_lpfll7v5hcqtkxl6l"}, {"name": "endpoint", "value": "https://yaron-nofluff.app.n8n.cloud/webhook-test/8c42463d-a631-4a17-a084-4bcbbb3bfc68"}, {"name": "notify", "value": "https://yaron-nofluff.app.n8n.cloud/webhook-test/8c42463d-a631-4a17-a084-4bcbbb3bfc68"}, {"name": "format", "value": "json"}, {"name": "uncompressed_webhook", "value": "true"}, {"name": "type", "value": "discover_new"}, {"name": "discover_by", "value": "=keyword"}]}, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer <YOUR_BRIGHT_DATA_API_KEY>"}]}}, "typeVersion": 4.2}, {"id": "8837f055-7243-44b6-87a2-e679d75839d0", "name": "Wait - Polling Bright Data", "type": "n8n-nodes-base.wait", "position": [2600, 360], "webhookId": "8005a2b3-2195-479e-badb-d90e4240e699", "parameters": {"unit": "minutes"}, "executeOnce": false, "typeVersion": 1.1}, {"id": "1f0ebefa-42a1-450c-b30a-64<PERSON>bdaedaf", "name": "If - Checking status of Snapshot - if data is ready or not", "type": "n8n-nodes-base.if", "position": [3040, 360], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "7932282b-71bb-4bbb-ab73-4978e554de7e", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "running"}]}}, "typeVersion": 2.2}, {"id": "e17b4da0-3f9c-45d5-acdf-ab634acfef97", "name": "HTTP Request - Getting data from Bright Data", "type": "n8n-nodes-base.httpRequest", "position": [3320, 380], "parameters": {"url": "=https://api.brightdata.com/datasets/v3/snapshot/{{ $json.snapshot_id }}", "options": {}, "sendQuery": true, "sendHeaders": true, "queryParameters": {"parameters": [{"name": "format", "value": "json"}]}, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer <YOUR_BRIGHT_DATA_API_KEY>"}]}}, "typeVersion": 4.2}, {"id": "b5bd6a55-f80d-46f9-a59a-beff28de9da7", "name": "Code - Cleaning Up", "type": "n8n-nodes-base.code", "position": [3600, 400], "parameters": {"jsCode": "// Helper function to strip HTML tags\nfunction stripHtml(html) {\n  return html\n    .replace(/<[^>]+>/g, '')    // remove all HTML tags\n    .replace(/&nbsp;/g, ' ')     // decode HTML entities\n    .replace(/&[a-z]+;/g, '')    // remove other HTML entities\n    .replace(/\\s+/g, ' ')        // normalize whitespace\n    .trim();\n}\n\nreturn items.map(item => {\n  const data = item.json;\n\n  // Flatten job_poster\n  if (data.job_poster) {\n    data.job_poster_name = data.job_poster.name || '';\n    data.job_poster_title = data.job_poster.title || '';\n    data.job_poster_url = data.job_poster.url || '';\n    delete data.job_poster;\n  }\n\n  // Flatten base_salary\n  if (data.base_salary) {\n    data.salary_min = data.base_salary.min_amount || '';\n    data.salary_max = data.base_salary.max_amount || '';\n    data.salary_currency = data.base_salary.currency || '';\n    data.salary_period = data.base_salary.payment_period || '';\n    delete data.base_salary;\n  }\n\n  // Clean up job description HTML\n  if (data.job_description_formatted) {\n    data.job_description_plain = stripHtml(data.job_description_formatted);\n  }\n\n  return { json: data };\n});\n"}, "typeVersion": 2}, {"id": "70f4a4a0-b9ce-4b7a-b232-86014a7f8a3f", "name": "Google Sheets - Adding All Job Posts", "type": "n8n-nodes-base.googleSheets", "position": [3940, 440], "parameters": {"columns": {"value": {"country_code": "={{ $json.country_code }}"}, "schema": [{"id": "url", "type": "string", "display": true, "removed": false, "required": false, "displayName": "url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_posting_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "job_posting_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_title", "type": "string", "display": true, "removed": false, "required": false, "displayName": "job_title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_location", "type": "string", "display": true, "removed": false, "required": false, "displayName": "job_location", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_description_plain", "type": "string", "display": true, "removed": false, "required": false, "displayName": "job_description_plain", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_poster_name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "job_poster_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_poster_title", "type": "string", "display": true, "removed": false, "required": false, "displayName": "job_poster_title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_poster_url", "type": "string", "display": true, "removed": false, "required": false, "displayName": "job_poster_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "salary_min", "type": "string", "display": true, "removed": false, "required": false, "displayName": "salary_min", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "salary_max", "type": "string", "display": true, "removed": false, "required": false, "displayName": "salary_max", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "salary_currency", "type": "string", "display": true, "removed": false, "required": false, "displayName": "salary_currency", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "salary_period", "type": "string", "display": true, "removed": false, "required": false, "displayName": "salary_period", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "application_availability", "type": "string", "display": true, "removed": false, "required": false, "displayName": "application_availability", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_posted_date", "type": "string", "display": true, "removed": false, "required": false, "displayName": "job_posted_date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_logo", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_logo", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "country_code", "type": "string", "display": true, "removed": false, "required": false, "displayName": "country_code", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "timestamp", "type": "string", "display": true, "removed": false, "required": false, "displayName": "timestamp", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_summary", "type": "string", "display": true, "removed": false, "required": false, "displayName": "job_summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_url", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_posted_time", "type": "string", "display": true, "removed": false, "required": false, "displayName": "job_posted_time", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_num_applicants", "type": "string", "display": true, "removed": false, "required": false, "displayName": "job_num_applicants", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "discovery_input", "type": "string", "display": true, "removed": false, "required": false, "displayName": "discovery_input", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "apply_link", "type": "string", "display": true, "removed": false, "required": false, "displayName": "apply_link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "title_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "title_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_description_formatted", "type": "string", "display": true, "removed": false, "required": false, "displayName": "job_description_formatted", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "input", "type": "string", "display": true, "removed": false, "required": false, "displayName": "input", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_seniority_level", "type": "string", "display": true, "removed": false, "required": false, "displayName": "job_seniority_level", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_function", "type": "string", "display": true, "removed": false, "required": false, "displayName": "job_function", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_employment_type", "type": "string", "display": true, "removed": false, "required": false, "displayName": "job_employment_type", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_industries", "type": "string", "display": true, "removed": false, "required": false, "displayName": "job_industries", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_base_pay_range", "type": "string", "display": true, "removed": false, "required": false, "displayName": "job_base_pay_range", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": ["row_number"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {"handlingExtraData": "insertInNewColumn"}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1_jbr5zBllTy_pGbogfGSvyv1_0a77I8tU-Ai7BjTAw4/edit#gid=0", "cachedResultName": "input"}, "documentId": {"__rl": true, "mode": "list", "value": "1_jbr5zBllTy_pGbogfGSvyv1_0a77I8tU-Ai7BjTAw4", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1_jbr5zBllTy_pGbogfGSvyv1_0a77I8tU-Ai7BjTAw4/edit?usp=drivesdk", "cachedResultName": "NoFluff-N8N-Sheet-Template-Job Scraping WIth Bright Data"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "4RJOMlGAcB9ZoYfm", "name": "Google Sheets account 2"}}, "typeVersion": 4.3, "alwaysOutputData": true}, {"id": "297d778f-afa5-4d2d-baea-3b1fb199f77c", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1940, -40], "parameters": {"width": 300, "height": 880, "content": "🧠 Bright Data Trigger – Customize Your Job Query\n\nThis HTTP Request sends a POST call to Bright Data to start a new dataset snapshot based on your filters.\n\n👋 If you don’t want to use the Form Trigger,\nyou can directly adjust the filters here in this node.\n\nYou can customize:\n\n\"location\" → city, region, or keyword (e.g. \"New York\", \"Remote\")\n\n\"keyword\" → job title or role (e.g. \"CMO\", \"AI Engineer\")\n\n\"country\" → 2-letter country code (e.g. \"US\", \"UK\")\n\n\"time_range\" → \"Past 24 hours\", \"Last 7 days\", etc.\n\n\"job_type\", \"experience_level\", \"remote\", \"company\" → optional filters\n\n📌 Tip:\nUse \"Past 24 hours\" or \"Last 7 days\" for the freshest results."}, "typeVersion": 1}, {"id": "54303791-b269-4930-85b5-33e50ae08f33", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [2320, 220], "parameters": {"color": 4, "width": 940, "height": 360, "content": "Bright Data Getting Jobs\n"}, "typeVersion": 1}, {"id": "cccb03cb-0432-43ff-9c3a-233de510a775", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [1920, 580], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "12067869-0249-4cd2-b9e2-8e4055a0d917", "name": "", "type": "string", "value": ""}]}}, "typeVersion": 3.4}], "pinData": {}, "connections": {"Edit Fields": {"main": [[{"node": "HTTP Request- Post API call to Bright Data", "type": "main", "index": 0}]]}, "Snapshot Progress": {"main": [[{"node": "If - Checking status of Snapshot - if data is ready or not", "type": "main", "index": 0}]]}, "Code - Cleaning Up": {"main": [[{"node": "Google Sheets - Adding All Job Posts", "type": "main", "index": 0}]]}, "Wait - Polling Bright Data": {"main": [[{"node": "Snapshot Progress", "type": "main", "index": 0}]]}, "On form submission - Discover Jobs": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "HTTP Request- Post API call to Bright Data": {"main": [[{"node": "Wait - Polling Bright Data", "type": "main", "index": 0}]]}, "HTTP Request - Getting data from Bright Data": {"main": [[{"node": "Code - Cleaning Up", "type": "main", "index": 0}]]}, "If - Checking status of Snapshot - if data is ready or not": {"main": [[{"node": "Wait - Polling Bright Data", "type": "main", "index": 0}], [{"node": "HTTP Request - Getting data from Bright Data", "type": "main", "index": 0}]]}}}