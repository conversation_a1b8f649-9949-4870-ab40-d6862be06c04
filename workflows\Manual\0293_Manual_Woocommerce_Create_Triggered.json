{"id": "187", "name": "Create, update and get a product from WooCommerce", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [220, 300], "parameters": {}, "typeVersion": 1}, {"name": "WooCommerce", "type": "n8n-nodes-base.wooCommerce", "position": [430, 300], "parameters": {"name": "n8n Sweatshirt", "imagesUi": {"imagesValues": []}, "metadataUi": {"metadataValues": []}, "additionalFields": {"description": "Stay warm with this sweatshirt!", "regularPrice": "30"}}, "credentials": {"wooCommerceApi": "woocommerce"}, "typeVersion": 1}, {"name": "WooCommerce1", "type": "n8n-nodes-base.wooCommerce", "position": [630, 300], "parameters": {"operation": "update", "productId": "={{$node[\"WooCommerce\"].json[\"id\"]}}", "updateFields": {"stockQuantity": 100}}, "credentials": {"wooCommerceApi": "woocommerce"}, "typeVersion": 1}, {"name": "WooCommerce2", "type": "n8n-nodes-base.wooCommerce", "position": [830, 300], "parameters": {"operation": "get", "productId": "={{$node[\"WooCommerce\"].json[\"id\"]}}"}, "credentials": {"wooCommerceApi": "woocommerce"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"WooCommerce": {"main": [[{"node": "WooCommerce1", "type": "main", "index": 0}]]}, "WooCommerce1": {"main": [[{"node": "WooCommerce2", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "WooCommerce", "type": "main", "index": 0}]]}}}