{"nodes": [{"name": "Function", "type": "n8n-nodes-base.function", "position": [230, 710], "parameters": {"functionCode": "// Code here will run only once, no matter how many input items there are.\n// More info and help: https://docs.n8n.io/nodes/n8n-nodes-base.function\n\n// Loop over inputs and add a new field called 'myNewField' to the JSON of each one\nfor (item of items) {\n  \nvar type = \"Status\";\n// Acknowledged\nif ((item.json.body.alert.statusCode == 2)  && (item.json.body.eventType == 201)) {\n    type = \"Acknowledged\";\n}\n// Closed\nif ((item.json.body.alert.statusCode == 4) & (item.json.body.eventType == 201)) {\n    type = \"Closed\";\n}\n// New Alert\nif ((item.json.body.alert.statusCode == 1) & (item.json.body.eventType == 200)) {\n    type = \"New Alert\";\n}\n\n// No one on duty\nif ((item.json.body.alert.statusCode == 16) & (item.json.body.eventType == 201)) {\n    type = \"No one on duty\";\n}\n \n// Annotation\nvar annotation = \"\";\nif ((item.json.body.eventType == 203) & (item.json.body.annotation != undefined) ) {\n    type = \"Annotated\";\n    annotation = item.json.body.annotation.message;\n}\nif (annotation != \"\") {\n    annotation = \": \" + annotation;\n}\n \nvar username = \"System\";\nif (item.json.body.user != undefined) {\n    username = item.json.body.user.username;\n}\n \nvar data = type + \" by \" + username + annotation;\n \nitem.json.s4Status = data; //  + \": \" + JSON.stringify(item.json);\n\n\nitem.json.s4Up = false;\nif (type == \"Closed\") {\n  item.json.s4Up = true;\n}\n\n}\n\n// You can write logs to the browser console\nconsole.log('Done!');\n\nreturn items;\n\n\n"}, "typeVersion": 1}, {"name": "<PERSON><PERSON> Trigger", "type": "n8n-nodes-base.notionTrigger", "disabled": true, "position": [230, 210], "parameters": {"event": "pageAddedToDatabase", "pollTimes": {"item": [{"mode": "everyX", "unit": "minutes", "value": 1}]}, "databaseId": "0f26823d-f509-43bb-b0e9-e9bb4ab91217"}, "credentials": {"notionApi": "Notion"}, "typeVersion": 1}, {"name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [50, 710], "webhookId": "95fd62c7-fc8c-4f6f-8441-bbf85a2da81a", "parameters": {"path": "95fd62c7-fc8c-4f6f-8441-bbf85a2da81a", "options": {}, "httpMethod": "POST"}, "typeVersion": 1}, {"name": "Function", "type": "n8n-nodes-base.function", "position": [230, 710], "parameters": {"functionCode": "// Code here will run only once, no matter how many input items there are.\n// More info and help: https://docs.n8n.io/nodes/n8n-nodes-base.function\n\n// Loop over inputs and add a new field called 'myNewField' to the JSON of each one\nfor (item of items) {\n  \nvar type = \"Status\";\n// Acknowledged\nif ((item.json.body.alert.statusCode == 2)  && (item.json.body.eventType == 201)) {\n    type = \"Acknowledged\";\n}\n// Closed\nif ((item.json.body.alert.statusCode == 4) & (item.json.body.eventType == 201)) {\n    type = \"Closed\";\n}\n// New Alert\nif ((item.json.body.alert.statusCode == 1) & (item.json.body.eventType == 200)) {\n    type = \"New Alert\";\n}\n\n// No one on duty\nif ((item.json.body.alert.statusCode == 16) & (item.json.body.eventType == 201)) {\n    type = \"No one on duty\";\n}\n \n// Annotation\nvar annotation = \"\";\nif ((item.json.body.eventType == 203) & (item.json.body.annotation != undefined) ) {\n    type = \"Annotated\";\n    annotation = item.json.body.annotation.message;\n}\nif (annotation != \"\") {\n    annotation = \": \" + annotation;\n}\n \nvar username = \"System\";\nif (item.json.body.user != undefined) {\n    username = item.json.body.user.username;\n}\n \nvar data = type + \" by \" + username + annotation;\n \nitem.json.s4Status = data; //  + \": \" + JSON.stringify(item.json);\n\n\nitem.json.s4Up = false;\nif (type == \"Closed\") {\n  item.json.s4Up = true;\n}\n\n}\n\n// You can write logs to the browser console\nconsole.log('Done!');\n\nreturn items;\n\n\n"}, "typeVersion": 1}, {"name": "Notion Update", "type": "n8n-nodes-base.notion", "position": [420, 710], "parameters": {"pageId": "={{$node[\"Webhook\"].json[\"body\"][\"alert\"][\"externalEventId\"]}}", "resource": "databasePage", "operation": "update", "propertiesUi": {"propertyValues": [{"key": "Description|rich_text", "peopleValue": [], "textContent": "={{$node[\"Function\"].json[\"s4Status\"]}}", "relationValue": [], "multiSelectValue": []}]}}, "credentials": {"notionApi": "Notion"}, "typeVersion": 1}, {"name": "Interval", "type": "n8n-nodes-base.interval", "position": [50, 380], "parameters": {"interval": 20}, "typeVersion": 1}, {"name": "SIGNL4 Resolve", "type": "n8n-nodes-base.signl4", "position": [420, 540], "parameters": {"operation": "resolve", "externalId": "={{$node[\"Notion Read Open\"].json[\"id\"]}}"}, "credentials": {"signl4Api": "SIGNL4"}, "typeVersion": 1}, {"name": "SIGNL4 Alert", "type": "n8n-nodes-base.signl4", "position": [420, 210], "parameters": {"message": "=Machine Alert: {{$node[\"Notion Trigger\"].json[\"Name\"]}}", "additionalFields": {"title": "n8n <PERSON><PERSON>", "externalId": "={{$node[\"Notion Trigger\"].json[\"id\"]}}", "locationFieldsUi": {"locationFieldsValues": {"latitude": "52.3992137", "longitude": "13.0583823"}}}}, "credentials": {"signl4Api": "SIGNL4"}, "typeVersion": 1}, {"name": "Notion Update Read", "type": "n8n-nodes-base.notion", "position": [570, 380], "parameters": {"pageId": "={{$node[\"Notion Read New\"].json[\"id\"]}}", "resource": "databasePage", "operation": "update", "propertiesUi": {"propertyValues": [{"key": "Read|checkbox", "peopleValue": [], "checkboxValue": true, "relationValue": [], "multiSelectValue": []}]}}, "credentials": {"notionApi": "Notion"}, "typeVersion": 1}, {"name": "Notion Read Open", "type": "n8n-nodes-base.notion", "position": [230, 540], "parameters": {"options": {"filter": {"multipleCondition": {"condition": {"and": [{"key": "Up|checkbox", "condition": "equals", "checkboxValue": true, "multiSelectValue": []}, {"key": "Read|checkbox", "condition": "equals", "checkboxValue": true, "multiSelectValue": []}]}}}}, "resource": "databasePage", "operation": "getAll", "databaseId": "0f26823d-f509-43bb-b0e9-e9bb4ab91217"}, "credentials": {"notionApi": "Notion"}, "typeVersion": 1}, {"name": "Notion Read New", "type": "n8n-nodes-base.notion", "position": [230, 380], "parameters": {"options": {"filter": {"multipleCondition": {"condition": {"and": [{"key": "Read|checkbox", "condition": "equals", "multiSelectValue": []}, {"key": "Up|checkbox", "condition": "equals", "multiSelectValue": []}]}}}}, "resource": "databasePage", "operation": "getAll", "databaseId": "0f26823d-f509-43bb-b0e9-e9bb4ab91217"}, "credentials": {"notionApi": "Notion"}, "typeVersion": 1}, {"name": "Notion Update Final", "type": "n8n-nodes-base.notion", "position": [570, 540], "parameters": {"pageId": "={{$node[\"Notion Read Open\"].json[\"id\"]}}", "resource": "databasePage", "operation": "update", "propertiesUi": {"propertyValues": [{"key": "Read|checkbox", "peopleValue": [], "relationValue": [], "multiSelectValue": []}]}}, "credentials": {"notionApi": "Notion"}, "typeVersion": 1}, {"name": "SIGNL4 Alert 2", "type": "n8n-nodes-base.signl4", "position": [420, 380], "parameters": {"message": "=Machine Alert: {{$node[\"Notion Read New\"].json[\"Name\"]}}", "additionalFields": {"title": "n8n <PERSON><PERSON>", "externalId": "={{$node[\"Notion Read New\"].json[\"id\"]}}", "locationFieldsUi": {"locationFieldsValues": {"latitude": "52.3992137", "longitude": "13.0583823"}}}}, "credentials": {"signl4Api": "SIGNL4"}, "typeVersion": 1}], "connections": {"Webhook": {"main": [[{"node": "Function", "type": "main", "index": 0}]]}, "Function": {"main": [[{"node": "Notion Update", "type": "main", "index": 0}]]}, "Interval": {"main": [[{"node": "Notion Read Open", "type": "main", "index": 0}, {"node": "Notion Read New", "type": "main", "index": 0}]]}, "Notion Trigger": {"main": [[{"node": "SIGNL4 Alert", "type": "main", "index": 0}]]}, "SIGNL4 Alert 2": {"main": [[{"node": "Notion Update Read", "type": "main", "index": 0}]]}, "SIGNL4 Resolve": {"main": [[{"node": "Notion Update Final", "type": "main", "index": 0}]]}, "Notion Read New": {"main": [[{"node": "SIGNL4 Alert 2", "type": "main", "index": 0}]]}, "Notion Read Open": {"main": [[{"node": "SIGNL4 Resolve", "type": "main", "index": 0}]]}}}