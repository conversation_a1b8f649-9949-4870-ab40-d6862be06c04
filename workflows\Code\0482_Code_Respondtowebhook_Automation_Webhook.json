{"meta": {}, "name": "[n8n] YouTube Channel Advanced RSS Feeds Generator", "tags": [{"id": "Q29tbWVudHBpY2tlcg", "name": "Commentpicker", "createdAt": "2024-04-16T14:29:17.942Z", "updatedAt": "2024-04-16T14:29:17.942Z"}, {"id": "Rm9ybVRyaWdnZXI", "name": "FormTrigger", "createdAt": "2024-04-16T14:29:17.942Z", "updatedAt": "2024-04-16T14:29:17.942Z"}, {"id": "SHR0cFJlcXVlc3Q", "name": "HttpRequest", "createdAt": "2024-04-16T14:29:17.942Z", "updatedAt": "2024-04-16T14:29:17.942Z"}, {"id": "QWdncmVnYXRl", "name": "Aggregate", "createdAt": "2024-04-16T14:29:17.942Z", "updatedAt": "2024-04-16T14:29:17.942Z"}, {"id": "UmVzcG9uZFRvV2ViaG9vaw", "name": "RespondToWebhook", "createdAt": "2024-04-16T14:29:17.942Z", "updatedAt": "2024-04-16T14:29:17.942Z"}, {"id": "Q29kZQ", "name": "Code", "createdAt": "2024-04-16T14:29:17.942Z", "updatedAt": "2024-04-16T14:29:17.942Z"}], "nodes": [{"name": "n8n Form Trigger", "type": "n8n-nodes-base.formTrigger", "position": [-300, -260], "webhookId": "68a70315-9f74-4cf5-9c68-828396b0f23b", "parameters": {"path": "Youtube", "formTitle": "Youtube RSS Generator", "formFields": {"values": [{"fieldLabel": "youtube Channel username or ID", "requiredField": true}]}, "responseMode": "responseNode", "formDescription": "=Youtube Username Example: @username\n\nYoutube ID Example: UCxxxxxxxxxxxxxxxxxx\n\nYoutube Video URL Example 1: https://www.youtube.com/watch?v=mn-br82ENxc\n\nYoutube Video URL Example 2: https://youtu.be/mn-br82ENxc\n\nYoutube Channel URL Example 1: https://www.youtube.com/@NewMedia_Life\n\nYoutube Channel URL Example 2: https://www.youtube.com/channel/UC_UDAiqQj-QfgTixKkW51qA"}, "typeVersion": 2}, {"name": "Get Channel ID", "type": "n8n-nodes-base.httpRequest", "notes": "3rd party API request", "position": [700, -440], "parameters": {"url": "https://commentpicker.com/actions/youtube-channel-id.php", "options": {"response": {"response": {"responseFormat": "json"}}}, "sendQuery": true, "sendHeaders": true, "queryParameters": {"parameters": [{"name": "url", "value": "=https://www.googleapis.com/youtube/v3/channels?part=id,snippet,statistics,contentDetails,status&forHandle={{ $item(\"0\").$node[\"Set Channel Username\"].json[\"channel name\"] }}"}, {"name": "token", "value": "={{ $item(\"0\").$node[\"Get Temporary Token\"].json[\"data\"] }}"}, {"name": "isPremium", "value": "false"}]}, "headerParameters": {"parameters": [{"name": "authority", "value": "commentpicker.com"}, {"name": "cookie", "value": "ezosuibasgeneris-1=690da322-c7c8-44e2-6154-8591a44d12aa; ezoab_186623=mod99-c; active_template::186623=pub_site.1711138973; lp_186623=https://commentpicker.com/youtube-channel-id.php; fontsLoaded=true; PHPSESSID=12ltjv3rr293h943c8h35nh3cg"}, {"name": "referer", "value": "https://commentpicker.com/youtube-channel-id.php"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}]}}, "notesInFlow": true, "typeVersion": 4.1}, {"name": "Set XML URL", "type": "n8n-nodes-base.set", "notes": "🤖Generate XML Feed URL", "position": [900, -440], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "bf0ea151-e325-4860-af02-76e51f692f2c", "name": "rss", "type": "string", "value": "=https://www.youtube.com/feeds/videos.xml?channel_id={{ $json.items[0].id }}"}]}}, "notesInFlow": true, "typeVersion": 3.3}, {"name": "Set Channel Username", "type": "n8n-nodes-base.set", "position": [520, -440], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "0837a847-4c6b-4b39-bb90-f200233bf7e1", "name": "channel name", "type": "string", "value": "={{ $item(\"0\").$node[\"Switch\"].json[\"value\"] }}"}]}}, "typeVersion": 3.3}, {"name": "Set XML Feed URL", "type": "n8n-nodes-base.set", "notes": "🤖Generate XML Feed URL", "position": [900, -260], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "bf0ea151-e325-4860-af02-76e51f692f2c", "name": "rss", "type": "string", "value": "=https://www.youtube.com/feeds/videos.xml?channel_id={{ $item(\"0\").$node[\"Switch\"].json[\"value\"] }}"}]}}, "notesInFlow": true, "typeVersion": 3.3}, {"name": "Set Video ID", "type": "n8n-nodes-base.set", "position": [520, -80], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "0837a847-4c6b-4b39-bb90-f200233bf7e1", "name": "Video ID", "type": "string", "value": "={{ $item(\"0\").$node[\"Switch\"].json[\"value\"] }}"}]}}, "typeVersion": 3.3}, {"name": "Get Video ID Channel ID", "type": "n8n-nodes-base.httpRequest", "notes": "3rd party API request", "position": [700, -80], "parameters": {"url": "https://commentpicker.com/actions/youtube-channel-id.php", "options": {"response": {"response": {"responseFormat": "json"}}}, "sendQuery": true, "sendHeaders": true, "queryParameters": {"parameters": [{"name": "url", "value": "=https://www.googleapis.com/youtube/v3/videos?part=snippet&id={{ $json[\"Video ID\"] }}"}, {"name": "token", "value": "={{ $item(\"0\").$node[\"GTT\"].json[\"data\"] }}"}, {"name": "isPremium", "value": "true"}]}, "headerParameters": {"parameters": [{"name": "authority", "value": "commentpicker.com"}, {"name": "cookie", "value": "ezosuibasgeneris-1=690da322-c7c8-44e2-6154-8591a44d12aa; ezoab_186623=mod99-c; active_template::186623=pub_site.1711138973; lp_186623=https://commentpicker.com/youtube-channel-id.php; fontsLoaded=true; PHPSESSID=12ltjv3rr293h943c8h35nh3cg"}, {"name": "referer", "value": "https://commentpicker.com/youtube-channel-id.php"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}]}}, "notesInFlow": true, "typeVersion": 4.1}, {"name": "Set XML Feed", "type": "n8n-nodes-base.set", "notes": "🤖Generate XML Feed URL", "position": [900, -80], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "bf0ea151-e325-4860-af02-76e51f692f2c", "name": "rss", "type": "string", "value": "=https://www.youtube.com/feeds/videos.xml?channel_id={{ $item(\"0\").$node[\"Get Video ID Channel ID\"].json[\"items\"][\"0\"][\"snippet\"][\"channelId\"] }}"}]}}, "notesInFlow": true, "typeVersion": 3.3}, {"name": "Get Temporary Token", "type": "n8n-nodes-base.httpRequest", "notes": "3rd party API request", "position": [320, -440], "parameters": {"url": "https://commentpicker.com/actions/token.php", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "authority", "value": "commentpicker.com"}, {"name": "cookie", "value": "ezosuibasgeneris-1=690da322-c7c8-44e2-6154-8591a44d12aa; fontsLoaded=true; PHPSESSID=12ltjv3rr293h943c8h35nh3cg; ezoab_186623=mod54-c; active_template::186623=pub_site.1711191989"}, {"name": "referer", "value": "https://commentpicker.com/youtube-channel-id.php"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}]}}, "notesInFlow": true, "typeVersion": 4.1}, {"name": "GTT", "type": "n8n-nodes-base.httpRequest", "notes": "3rd party API request", "position": [320, -80], "parameters": {"url": "https://commentpicker.com/actions/token.php", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "authority", "value": "commentpicker.com"}, {"name": "cookie", "value": "ezosuibasgeneris-1=690da322-c7c8-44e2-6154-8591a44d12aa; fontsLoaded=true; PHPSESSID=12ltjv3rr293h943c8h35nh3cg; ezoab_186623=mod54-c; active_template::186623=pub_site.1711191989"}, {"name": "referer", "value": "https://commentpicker.com/youtube-channel-id.php"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}]}}, "notesInFlow": true, "typeVersion": 4.1}, {"name": "Aggregate", "type": "n8n-nodes-base.aggregate", "notes": "🤖Combine results in one", "position": [1080, -260], "parameters": {"options": {}, "fieldsToAggregate": {"fieldToAggregate": [{"renameField": true, "outputFieldName": "rss url", "fieldToAggregate": "rss"}]}}, "notesInFlow": true, "typeVersion": 1}, {"name": "Youtube Channel Videos RSS Formats", "type": "n8n-nodes-base.set", "notes": "RSS Feed for channel Posts", "position": [1260, -180], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "6af1de72-9940-4843-9a98-94e36b2878a3", "name": "=Videos - HTML format response", "type": "string", "value": "=https://rss-bridge.org/bridge01/?action=display&bridge=YoutubeBridge&context=By+channel+id&c={{ $item(\"0\").$node[\"Aggregate\"].json[\"rss url\"][\"0\"].match(/channel_id=([^&?/]+)/)[1] }}&duration_min=&duration_max=&format=Html"}, {"id": "2b486723-1dff-4525-8169-6d977dee6862", "name": "Videos - ATOM format response", "type": "string", "value": "=https://rss-bridge.org/bridge01/?action=display&bridge=YoutubeBridge&context=By+channel+id&c={{ $item(\"0\").$node[\"Aggregate\"].json[\"rss url\"][\"0\"].match(/channel_id=([^&?/]+)/)[1] }}&duration_min=&duration_max=&format=Atom"}, {"id": "10b3c04a-2c8c-4533-944b-13123bd22743", "name": "Videos - JSON format response", "type": "string", "value": "=https://rss-bridge.org/bridge01/?action=display&bridge=YoutubeBridge&context=By+channel+id&c={{ $item(\"0\").$node[\"Aggregate\"].json[\"rss url\"][\"0\"].match(/channel_id=([^&?/]+)/)[1] }}&duration_min=&duration_max=&format=Json"}, {"id": "ee8910de-76ab-47a3-b23f-1a1e837fb885", "name": "Videos - MRSS format response", "type": "string", "value": "=https://rss-bridge.org/bridge01/?action=display&bridge=YoutubeBridge&context=By+channel+id&c={{ $item(\"0\").$node[\"Aggregate\"].json[\"rss url\"][\"0\"].match(/channel_id=([^&?/]+)/)[1] }}&duration_min=&duration_max=&format=Mrss"}, {"id": "8684437c-11f0-4cc2-b9b2-00ecb6768175", "name": "Videos - TEXT format response", "type": "string", "value": "=https://rss-bridge.org/bridge01/?action=display&bridge=YoutubeBridge&context=By+channel+id&c={{ $item(\"0\").$node[\"Aggregate\"].json[\"rss url\"][\"0\"].match(/channel_id=([^&?/]+)/)[1] }}&duration_min=&duration_max=&format=Plaintext"}, {"id": "a53d1d0a-bfd1-41f4-9ab3-edd1e20adaa2", "name": "Videos - SFEED format response", "type": "string", "value": "=https://rss-bridge.org/bridge01/?action=display&bridge=YoutubeBridge&context=By+channel+id&c={{ $item(\"0\").$node[\"Aggregate\"].json[\"rss url\"][\"0\"].match(/channel_id=([^&?/]+)/)[1] }}&duration_min=&duration_max=&format=Sfeed"}, {"id": "d17fd2e0-0e4a-45c9-bc60-86ca6a7940d4", "name": "Videos - XML format response", "type": "string", "value": "={{ $json[\"rss url\"][\"0\"] }}"}]}}, "notesInFlow": true, "typeVersion": 3.3}, {"name": "Youtube Channel Community RSS Formats", "type": "n8n-nodes-base.set", "notes": "RSS Feed for channel Posts", "position": [1260, -400], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "6af1de72-9940-4843-9a98-94e36b2878a3", "name": "=Community - HTML format response", "type": "string", "value": "=https://rss-bridge.org/bridge01/?action=display&bridge=YouTubeCommunityTabBridge&context=By+channel+ID&channel={{ $item(\"0\").$node[\"Aggregate\"].json[\"rss url\"][\"0\"].match(/channel_id=([^&?/]+)/)[1] }}&format=HTML"}, {"id": "2b486723-1dff-4525-8169-6d977dee6862", "name": "Community - ATOM format response", "type": "string", "value": "=https://rss-bridge.org/bridge01/?action=display&bridge=YouTubeCommunityTabBridge&context=By+channel+ID&channel={{ $item(\"0\").$node[\"Aggregate\"].json[\"rss url\"][\"0\"].match(/channel_id=([^&?/]+)/)[1] }}&format=Atom"}, {"id": "10b3c04a-2c8c-4533-944b-13123bd22743", "name": "Community - JSON format response", "type": "string", "value": "=https://rss-bridge.org/bridge01/?action=display&bridge=YouTubeCommunityTabBridge&context=By+channel+ID&channel={{ $item(\"0\").$node[\"Aggregate\"].json[\"rss url\"][\"0\"].match(/channel_id=([^&?/]+)/)[1] }}&format=Json"}, {"id": "ee8910de-76ab-47a3-b23f-1a1e837fb885", "name": "Community - MRSS format response", "type": "string", "value": "=https://rss-bridge.org/bridge01/?action=display&bridge=YouTubeCommunityTabBridge&context=By+channel+ID&channel={{ $item(\"0\").$node[\"Aggregate\"].json[\"rss url\"][\"0\"].match(/channel_id=([^&?/]+)/)[1] }}&format=Mrss"}, {"id": "8684437c-11f0-4cc2-b9b2-00ecb6768175", "name": "Community - TEXT format response", "type": "string", "value": "=https://rss-bridge.org/bridge01/?action=display&bridge=YouTubeCommunityTabBridge&context=By+channel+ID&channel={{ $item(\"0\").$node[\"Aggregate\"].json[\"rss url\"][\"0\"].match(/channel_id=([^&?/]+)/)[1] }}&format=Plaintext"}, {"id": "a53d1d0a-bfd1-41f4-9ab3-edd1e20adaa2", "name": "Community - SFEED format response", "type": "string", "value": "=https://rss-bridge.org/bridge01/?action=display&bridge=YouTubeCommunityTabBridge&context=By+channel+ID&channel={{ $item(\"0\").$node[\"Aggregate\"].json[\"rss url\"][\"0\"].match(/channel_id=([^&?/]+)/)[1] }}&format=Sfeed"}]}}, "notesInFlow": true, "typeVersion": 3.3}, {"name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "notes": "Reply to the webhook request with table", "position": [1900, -280], "parameters": {"options": {}, "respondWith": "text", "responseBody": "={{ $json[\"html\"] }}"}, "notesInFlow": true, "typeVersion": 1}, {"name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-360, 140], "parameters": {"color": 7, "width": 2425.409405354546, "height": 200.24482670360715, "content": "## ℹ️ **Workarounds And Information**\n\n### - **No need to acquire Google Cloud API** to retrieve channel data. I have implemented a free workaround method.\n### - The workflow code has been **tested and proven to work** with all YouTube methods, whether for videos or channels. Regardless of whether you input URLs or usernames, the result will always be the channel ID.\n### - Please be aware that the provided workarounds may become **obsolete or non-functional** in the future. I will ensure to stay updated; however, if this workflow does not work for you, please reach out to me on the n8n community.\n### - We have utilized a 3rd party method to generate **multiple syntaxes of RSS feeds** as outlined below. (*The mentioned source is also capable of constructing multi-channel YouTube RSS feeds*, which I will create later for BULK channel RSS.)"}, "typeVersion": 1}, {"name": "Switch", "type": "n8n-nodes-base.switch", "position": [60, -260], "parameters": {"rules": {"values": [{"outputKey": "Username", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.type }}", "rightValue": "channel username"}]}, "renameOutput": true}, {"outputKey": "Direct", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "5af4921b-6266-436a-901c-ab52de68aaf4", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.type }}", "rightValue": "=channel ID"}]}, "renameOutput": true}, {"outputKey": "Video-ID", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a5baa5e6-879f-484a-b521-af802b6d79a9", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.type }}", "rightValue": "=video ID"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-360, -520], "parameters": {"color": 3, "width": 2429.6732915601406, "height": 644.5128280596109, "content": "## 🌐 **Generate RSS Feeds for Public Youtube Channel (No API Or Administrator permissions Required 😉)**\n**``Yes, As you heard``** This Workflow using `3rd party` APIs & Solutions to get the job done. **``no need to setup anything``.**\n\n## Workflow Steps:\n- Run **`Test Workflow`**.\n- Enter Channel or Video URL or ID or Username.\n- Finally, the result will provide **``13 URLs (6x Community + 6x Videos + 1 XML)``**:\n  - 6 Formats Types is: `ATOM`, `JSON`, `MRSS`, `PLAINTEXT`, `SFEED`\n  - The **``13th URL``** is from YouTube Directly that contain XML file data.\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n[![N8N Creator Profile](https://cdn.statically.io/gh/Automations-Project/n8n-templates/main/stats.min.svg)](https://n8n.io/creators/nskha)"}, "typeVersion": 1}, {"name": "Validation Code", "type": "n8n-nodes-base.code", "notes": "🤓Validate the YouTube input", "position": [-120, -260], "parameters": {"jsCode": "// JavaScript code to extract YouTube channel ID, username, or video ID from a given input and return in n8n compatible format\n\n// Initialize an array to hold the output items\nconst items = [];\n\n// Extract the input value from the previous node's output using the $input API\nconst inputData = $input.all(); // Get all input data\n// Assuming 'youtube Channel username or ID' is the correct key, and it's in the first input item\nconst input = inputData.length > 0 ? inputData[0].json[\"youtube Channel username or ID\"] : null;\n\n// Check if input exists\nif (!input) {\n    throw new Error('Input is undefined or not provided');\n}\n\n// Regular expressions for different YouTube URL and input formats\nconst usernamePattern = /^@?([a-zA-Z0-9_-]+)$/;\nconst channelIdPattern = /^(UC[a-zA-Z0-9_-]{22})$/; // Ensure channel ID starts with \"UC\"\nconst videoUrlPattern1 = /(?:https?:\\/\\/)?www\\.youtube\\.com\\/watch\\?v=([a-zA-Z0-9_-]+)/;\nconst videoUrlPattern2 = /(?:https?:\\/\\/)?youtu\\.be\\/([a-zA-Z0-9_-]+)/;\nconst channelUrlPattern1 = /(?:https?:\\/\\/)?www\\.youtube\\.com\\/@([a-zA-Z0-9_-]+)/;\nconst channelUrlPattern2 = /(?:https?:\\/\\/)?www\\.youtube\\.com\\/channel\\/(UC[a-zA-Z0-9_-]{22})/;\nconst customChannelUrlPattern = /(?:https?:\\/\\/)?www\\.youtube\\.com\\/c\\/([a-zA-Z0-9_-]+)/; // Pattern for custom channel URLs\n\n// Function to determine the type and value of the input\nfunction determineTypeAndValue(input) {\n    if (channelIdPattern.test(input)) {\n        return { type: 'channel ID', value: input };\n    } else if (usernamePattern.test(input)) {\n        return { type: 'channel username', value: input };\n    } else if (videoUrlPattern1.test(input) || videoUrlPattern2.test(input)) {\n        const videoId = videoUrlPattern1.test(input) ? input.match(videoUrlPattern1)[1] : input.match(videoUrlPattern2)[1];\n        return { type: 'video ID', value: videoId };\n    } else if (channelUrlPattern1.test(input) || customChannelUrlPattern.test(input)) {\n        const username = channelUrlPattern1.test(input) ? input.match(channelUrlPattern1)[1] : input.match(customChannelUrlPattern)[1];\n        return { type: 'channel username', value: username };\n    } else if (channelUrlPattern2.test(input)) {\n        return { type: 'channel ID', value: input.match(channelUrlPattern2)[1] };\n    } else {\n        return { error: 'Invalid input or unsupported format.' };\n    }\n}\n\n// Process the input and add the result to the items array\nconst result = determineTypeAndValue(input);\nitems.push({ json: result });\n\nreturn items; // Return the array of items\n"}, "notesInFlow": true, "typeVersion": 2}, {"name": "Format response as HTML Table", "type": "n8n-nodes-base.code", "position": [1680, -280], "parameters": {"jsCode": "// Assuming inputData is dynamically retrieved as follows\nconst inputData = $item(\"0\").$node[\"Merga Data of Youtube & Community RSS\"].json;\n\n// Initialize HTML with a modern styled table\nlet html = `\n<style>\n  table {\n    width: 100%;\n    border-collapse: collapse;\n    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n    box-shadow: 0 2px 15px rgba(0,0,0,0.1);\n    border-radius: 8px;\n    overflow: hidden;\n    margin-top: 20px;\n  }\n  th, td {\n    border: 1px solid #ddd;\n    padding: 8px 16px;\n    text-align: left;\n    color: #333;\n  }\n  th {\n    background-color: #f0f0f0;\n    color: #000;\n    font-weight: 600;\n  }\n  tr:nth-child(even) {background-color: #f9f9f9;}\n  tr:hover {background-color: #f1f1f1;}\n  a {\n    text-decoration: none;\n    color: #0645AD;\n  }\n  a:hover {\n    text-decoration: underline;\n  }\n</style>\n<table>\n<tr>\n  <th>Type</th>\n  <th>Format</th>\n  <th>URL</th>\n</tr>`;\n\n// Function to process each item and add it to the HTML table\nObject.entries(inputData).forEach(([key, value]) => {\n  // Extract type and format from the key, assuming key format 'Category - Format'\n  const [type, format] = key.split(' - ');\n  html += `<tr>\n    <td>${type} RSS</td>\n    <td>${format}</td>\n    <td><a href=\"${value}\" target=\"_blank\">${value}</a></td>\n  </tr>`;\n});\n\n// Close the HTML table tag\nhtml += `</table>`;\n\n// Return the HTML string as output\nreturn [{json: {html: html}}];\n"}, "typeVersion": 2}, {"name": "Merga Data of Youtube & Community RSS", "type": "n8n-nodes-base.merge", "position": [1480, -280], "parameters": {"mode": "combine", "options": {}, "combinationMode": "multiplex"}, "typeVersion": 2.1}], "active": "false", "pinData": {}, "settings": {"timezone": "Asia/Baghdad", "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": "", "executionOrder": "v1", "executionTimeout": 600, "saveManualExecutions": true, "saveExecutionProgress": true}, "staticData": "", "connections": {"GTT": {"main": [[{"node": "Set Video ID", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Get Temporary Token", "type": "main", "index": 0}], [{"node": "Set XML Feed URL", "type": "main", "index": 0}], [{"node": "GTT", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "Youtube Channel Community RSS Formats", "type": "main", "index": 0}, {"node": "Youtube Channel Videos RSS Formats", "type": "main", "index": 0}]]}, "Set XML URL": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Set Video ID": {"main": [[{"node": "Get Video ID Channel ID", "type": "main", "index": 0}]]}, "Set XML Feed": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Get Channel ID": {"main": [[{"node": "Set XML URL", "type": "main", "index": 0}]]}, "Validation Code": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Set XML Feed URL": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "n8n Form Trigger": {"main": [[{"node": "Validation Code", "type": "main", "index": 0}]]}, "Get Temporary Token": {"main": [[{"node": "Set Channel Username", "type": "main", "index": 0}]]}, "Set Channel Username": {"main": [[{"node": "Get Channel ID", "type": "main", "index": 0}]]}, "Get Video ID Channel ID": {"main": [[{"node": "Set XML Feed", "type": "main", "index": 0}]]}, "Format response as HTML Table": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Youtube Channel Videos RSS Formats": {"main": [[{"node": "Merga Data of Youtube & Community RSS", "type": "main", "index": 1}]]}, "Merga Data of Youtube & Community RSS": {"main": [[{"node": "Format response as HTML Table", "type": "main", "index": 0}]]}, "Youtube Channel Community RSS Formats": {"main": [[{"node": "Merga Data of Youtube & Community RSS", "type": "main", "index": 0}]]}}}