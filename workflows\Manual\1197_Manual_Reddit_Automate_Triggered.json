{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [270, 340], "parameters": {}, "typeVersion": 1}, {"name": "Reddit", "type": "n8n-nodes-base.reddit", "position": [470, 340], "parameters": {"text": "This post was created using the Reddit node in n8n", "title": "Created from n8n", "subreddit": "n8n"}, "credentials": {"redditOAuth2Api": "Reddit OAuth Credentials"}, "typeVersion": 1}, {"name": "Reddit1", "type": "n8n-nodes-base.reddit", "position": [670, 340], "parameters": {"postId": "={{$json[\"id\"]}}", "operation": "get", "subreddit": "={{$node[\"Reddit\"].parameter[\"subreddit\"]}}"}, "credentials": {"redditOAuth2Api": "Reddit OAuth Credentials"}, "typeVersion": 1}, {"name": "Reddit2", "type": "n8n-nodes-base.reddit", "position": [870, 340], "parameters": {"postId": "={{$json[\"id\"]}}", "resource": "postComment", "commentText": "This comment was added from n8n!"}, "credentials": {"redditOAuth2Api": "Reddit OAuth Credentials"}, "typeVersion": 1}], "connections": {"Reddit": {"main": [[{"node": "Reddit1", "type": "main", "index": 0}]]}, "Reddit1": {"main": [[{"node": "Reddit2", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Reddit", "type": "main", "index": 0}]]}}}