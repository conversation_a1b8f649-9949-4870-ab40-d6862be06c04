{"id": "103", "name": "Create a new customer in Chargebee", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "Chargebee", "type": "n8n-nodes-base.chargebee", "position": [460, 300], "parameters": {"resource": "customer", "properties": {"last_name": "", "first_name": ""}}, "credentials": {"chargebeeApi": ""}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"On clicking 'execute'": {"main": [[{"node": "Chargebee", "type": "main", "index": 0}]]}}}