{"id": "1g8EAij2RwhNN70t", "meta": {"instanceId": "a4bfc93e975ca233ac45ed7c9227d84cf5a2329310525917adaf3312e10d5462", "templateCredsSetupCompleted": true}, "name": "xSend and check TTS (Text-to-speech) voice calls end email verification", "tags": [], "nodes": [{"id": "56842e20-266b-4770-b4cd-3106418caefa", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-480, -740], "parameters": {"width": 440, "height": 180, "content": "## STEP 1\n[Register here to ClickSend](https://clicksend.com/?u=586989) and obtain your API Key and 2 € of free credits\n\nIn the node \"Send Voice\" create a \"Basic Auth\" with the username you registered and the API Key provided as your password"}, "typeVersion": 1}, {"id": "9dfff5ae-fc04-4957-a7b6-6866e8ab0854", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-480, -320], "parameters": {"width": 440, "content": "## STEP 3\n\nSubmit the form and you will receive a call to the phone number you entered where the selected voice will tell you the content of the text you wrote."}, "typeVersion": 1}, {"id": "914666e8-1dc3-4d71-abf7-408b66a4508c", "name": "Send Voice", "type": "n8n-nodes-base.httpRequest", "position": [260, 0], "parameters": {"url": "https://rest.clicksend.com/v3/voice/send", "method": "POST", "options": {}, "jsonBody": "={\n  \"messages\": [\n    {\n      \"source\": \"n8n\",\n      \"body\": \"Your verification number is {{ $json.Code }}\",\n      \"to\": \"{{ $('On form submission').item.json.To }}\",\n      \"voice\": \"{{ $('On form submission').item.json.Voice }}\",\n      \"lang\": \"{{ $('On form submission').item.json.Lang }}\",\n      \"machine_detection\": 1\n    }\n  ]\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth", "headerParameters": {"parameters": [{"name": "Content-Type", "value": " application/json"}, {}]}}, "credentials": {"httpBasicAuth": {"id": "UwsDe2JxT39eWIvY", "name": "ClickSend API"}}, "typeVersion": 4.2}, {"id": "838266ee-33aa-4380-9335-5290cad30504", "name": "On form submission", "type": "n8n-nodes-base.formTrigger", "position": [-440, 0], "webhookId": "194f453a-1d86-4222-bd4d-117f03005560", "parameters": {"options": {}, "formTitle": "Send Voice Message", "formFields": {"values": [{"fieldLabel": "To", "placeholder": "+39xxxx", "requiredField": true}, {"fieldType": "dropdown", "fieldLabel": "Voice", "fieldOptions": {"values": [{"option": "male"}, {"option": "female"}]}, "requiredField": true}, {"fieldType": "dropdown", "fieldLabel": "<PERSON>", "fieldOptions": {"values": [{"option": "en-us \t"}, {"option": "it-it"}, {"option": "en-au"}, {"option": "en-gb"}, {"option": "de-de"}, {"option": "es-es"}, {"option": "fr-fr"}, {"option": "is-is"}, {"option": "da-dk"}, {"option": "nl-nl"}, {"option": "pl-pl"}, {"option": "pt-br"}, {"option": "ru-ru"}]}, "requiredField": true}, {"fieldType": "email", "fieldLabel": "Email", "placeholder": "Email", "requiredField": true}, {"fieldLabel": "Nome ", "placeholder": "Nome", "requiredField": true}]}}, "typeVersion": 2.2}, {"id": "aab0e353-0af0-4867-9178-4195c6ed045b", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-480, -1020], "parameters": {"color": 3, "width": 440, "height": 240, "content": "## Send and Check TTS (Text-to-Speech) Voice Calls with Email Verification\n\nThis workflow automates the process of sending voice calls for verification purposes and combines it with email verification. It uses the ClickSend API for voice calls and integrates with SMTP for email verification. \n"}, "typeVersion": 1}, {"id": "f4c3e305-be7e-43e7-a874-2767a0411624", "name": "Send Email", "type": "n8n-nodes-base.emailSend", "position": [1180, -100], "webhookId": "92aa0a80-8bea-47b7-86ef-bebc90435526", "parameters": {"html": "=Hi {{ $('On form submission').item.json['Nome '] }},<br>\nThe email verification code is <b>{{ $json['Code Email'] }}</b>", "options": {}, "subject": "Verify your code", "toEmail": "={{ $('On form submission').item.json['Email'] }}", "fromEmail": "EMAIL"}, "credentials": {"smtp": {"id": "hRjP3XbDiIQqvi7x", "name": "SMTP <EMAIL>"}}, "typeVersion": 2.1}, {"id": "5a3ff941-6d25-4479-bedc-c3cfa7c75e36", "name": "Code for voice", "type": "n8n-nodes-base.code", "position": [40, 0], "parameters": {"jsCode": "// Loop over input items and modify the 'Code' field to add spaces between characters\nfor (const item of $input.all()) {\n  const code = item.json.Code;\n\n  const spacedCode = code.split('').join(' ');\n\n  item.json.Code = spacedCode;\n}\n\nreturn $input.all();"}, "typeVersion": 2}, {"id": "14ccfe99-8fbd-4cde-9ca3-c73e541086b3", "name": "Set voice code", "type": "n8n-nodes-base.set", "position": [-220, 0], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "89fb63af-790e-4388-9495-5f1e517ee486", "name": "Code", "type": "string", "value": "12345"}]}}, "typeVersion": 3.4}, {"id": "b01e3604-5ca9-45cb-8a59-4f86f33d169b", "name": "Verify voice code", "type": "n8n-nodes-base.form", "position": [480, 0], "webhookId": "b4356cb9-4185-4c65-b7c4-1f1e00a50ce0", "parameters": {"options": {}, "formFields": {"values": [{"fieldLabel": "Verify", "placeholder": "Verify", "requiredField": true}]}}, "typeVersion": 1}, {"id": "9c013995-ce9d-4c65-9c19-2f1a410ada38", "name": "Fail voice code", "type": "n8n-nodes-base.form", "position": [940, 100], "webhookId": "330b8918-7890-485c-a4fb-b0a917c14edb", "parameters": {"options": {}, "operation": "completion", "completionTitle": "Oh no!", "completionMessage": "Sorry, the code entered is invalid. Verification has not been completed"}, "typeVersion": 1}, {"id": "3abbb31d-2ad0-4c2e-8891-e65e484e2ae4", "name": "Set email code", "type": "n8n-nodes-base.set", "position": [940, -100], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "33438b85-27f4-4264-ab88-e1d3ec8b1ae8", "name": "Code Email", "type": "string", "value": "56789"}]}}, "typeVersion": 3.4}, {"id": "3e453956-0056-4532-a096-a0a6de9702ae", "name": "Verify email code", "type": "n8n-nodes-base.form", "position": [1440, -100], "webhookId": "db9965d4-7660-4775-a5c6-772de7927e85", "parameters": {"options": {}, "formFields": {"values": [{"fieldLabel": "Verify email", "placeholder": "Verify email code", "requiredField": true}]}}, "typeVersion": 1}, {"id": "964528b3-f25f-4591-b5fe-6b405aaed0d2", "name": "Is email code correct?", "type": "n8n-nodes-base.if", "position": [1680, -100], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "14ee5cfc-2a21-413d-9099-e63ce12da323", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Set email code').item.json['Code Email'] }}", "rightValue": "={{ $json['Verify email'] }}"}]}}, "typeVersion": 2.2}, {"id": "df8f62cc-8f45-462e-84bf-0121cbf650c7", "name": "Is voice code correct?", "type": "n8n-nodes-base.if", "position": [700, 0], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "5aaaf956-3693-4930-b63e-dceb51857716", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{$('Set voice code').item.json.Code}}", "rightValue": "={{ $json.Verify }}"}]}}, "typeVersion": 2.2}, {"id": "1f7ff94c-9b29-481b-99cf-cef63714995c", "name": "Success", "type": "n8n-nodes-base.form", "position": [1920, -200], "webhookId": "3dfd4429-927f-4695-9b64-87f53b52c3f6", "parameters": {"options": {}, "operation": "completion", "completionTitle": "Great!", "completionMessage": "Your mobile number and email address have been verified successfully. Thank you!"}, "typeVersion": 1}, {"id": "2c6fbd06-30f9-47b8-afa0-042439ff92c6", "name": "Fail email code", "type": "n8n-nodes-base.form", "position": [1920, 0], "webhookId": "a26fc536-f976-4719-bb11-43111f7ec330", "parameters": {"options": {}, "operation": "completion", "completionTitle": "Oh no!", "completionMessage": "Sorry, the code entered is invalid. Verification has not been completed"}, "typeVersion": 1}, {"id": "632e4253-f4d1-4255-93d8-b7c3b8571e36", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-260, -80], "parameters": {"width": 180, "height": 240, "content": "Set the code that will be spoken in the verification phone call"}, "typeVersion": 1}, {"id": "37f3d155-cbb8-4c03-b8ae-43df4eec06d1", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [900, -180], "parameters": {"width": 180, "height": 240, "content": "Set the code that will be sent in the verification email"}, "typeVersion": 1}, {"id": "4c3a01a0-927f-499f-8bf2-e402b77050c4", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-480, -520], "parameters": {"width": 440, "content": "## STEP 2\n\nSet the verification code for this explanatory flow that will be set in the voice call and verification email.\n\nIn the node \"Send Email\" set the sender."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "3e26e024-4da6-4449-bc3f-8604c837396a", "connections": {"Send Email": {"main": [[{"node": "Verify email code", "type": "main", "index": 0}]]}, "Send Voice": {"main": [[{"node": "Verify voice code", "type": "main", "index": 0}]]}, "Code for voice": {"main": [[{"node": "Send Voice", "type": "main", "index": 0}]]}, "Set email code": {"main": [[{"node": "Send Email", "type": "main", "index": 0}]]}, "Set voice code": {"main": [[{"node": "Code for voice", "type": "main", "index": 0}]]}, "Verify email code": {"main": [[{"node": "Is email code correct?", "type": "main", "index": 0}]]}, "Verify voice code": {"main": [[{"node": "Is voice code correct?", "type": "main", "index": 0}]]}, "On form submission": {"main": [[{"node": "Set voice code", "type": "main", "index": 0}]]}, "Is email code correct?": {"main": [[{"node": "Success", "type": "main", "index": 0}], [{"node": "Fail email code", "type": "main", "index": 0}]]}, "Is voice code correct?": {"main": [[{"node": "Set email code", "type": "main", "index": 0}], [{"node": "Fail voice code", "type": "main", "index": 0}]]}}}