{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "AWS Lambda", "type": "n8n-nodes-base.awsLambda", "position": [450, 300], "parameters": {"function": "arn:aws:lambda:ap-south-1:100558637562:function:hello-world-sample"}, "credentials": {"aws": "am<PERSON>han-aws"}, "typeVersion": 1}], "connections": {"On clicking 'execute'": {"main": [[{"node": "AWS Lambda", "type": "main", "index": 0}]]}}}