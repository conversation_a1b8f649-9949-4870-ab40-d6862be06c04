{"meta": {"instanceId": "257476b1ef58bf3cb6a46e65fac7ee34a53a5e1a8492d5c6e4da5f87c9b82833"}, "nodes": [{"id": "f5c16b6d-b7b0-4b36-9e74-795a4f486604", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [360, 1700], "parameters": {}, "typeVersion": 1}, {"id": "0cc486d8-397f-44b1-a23b-04d0c142a48d", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [220, 1420], "parameters": {"height": 259, "content": "## Email search with Icypeas (bulk search)\n\n\nThis workflow demonstrates how to perform email searches (bulk search) using Icypeas. Visit https://icypeas.com to create your account."}, "typeVersion": 1}, {"id": "b932d050-4934-4f2f-a620-79f08b97c428", "name": "Authenticates to your Icypeas account", "type": "n8n-nodes-base.code", "position": [860, 1700], "parameters": {"jsCode": "const API_BASE_URL = \"https://app.icypeas.com/api\";\nconst API_PATH = \"/bulk-search\";\nconst METHOD = \"POST\";\n\n// Change here\nconst API_KEY = \"PUT_API_KEY_HERE\";\nconst API_SECRET = \"PUT_API_SECRET_HERE\";\nconst USER_ID = \"PUT_USER_ID_HERE\";\n////////////////\n\nconst genSignature = (\n    url,\n    method,\n    secret,\n    timestamp = new Date().toISOString()\n) => {\n    const Crypto = require('crypto');\n    const payload = `${method}${url}${timestamp}`.toLowerCase();\n    const sign = Crypto.createHmac(\"sha1\", secret).update(payload).digest(\"hex\");\n\n    return sign;\n};\n\nconst apiUrl = `${API_BASE_URL}${API_PATH}`;\n\nconst data = $input.all().map((x) => [x.json.firstname, x.json.lastname, x.json.company]);\n$input.first().json.data = data;\n$input.first().json.api = {\n  timestamp: new Date().toISOString(),\n  secret: API_SECRET,\n  key: API_KEY,\n  userId: USER_ID,\n  url: apiUrl,\n};\n\n$input.first().json.api.signature = genSignature(apiUrl, METHOD, API_SECRET, $input.first().json.api.timestamp);\nreturn $input.first();"}, "typeVersion": 1}, {"id": "35325df4-1d77-4200-9aca-a7f311f3857e", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [500, 1560], "parameters": {"height": 606.4963141641612, "content": "## Read your Google Sheet file\n\nThis node reads a Google Sheet. You need to create a sheet with :\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n**The first column** :\nHeader : lastname\n\n**The first column** :\nHeader : firstname\n\n**The first column** :\nHeader : company\n\n\nDon't forget to specify the path of your file in the node and your credentials."}, "typeVersion": 1}, {"id": "ca04cf0b-59b6-4836-902f-2e93b6cbc3f5", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [741.*************, 1458.***********], "parameters": {"width": 392.*************, "height": 1203.*************, "content": "## Authenticates to your Icypeas account\n\nThis code node utilizes your API key, API secret, and User ID to establish a connection with your Icypeas account.\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nOpen this node and insert your API Key, API secret, and User ID within the quotation marks. You can locate these credentials on your Icypeas profile at https://app.icypeas.com/bo/profile. Here is the extract of what you have to change :\n\nconst API_KEY = \"**PUT_API_KEY_HERE**\";\nconst API_SECRET = \"**PUT_API_SECRET_HERE**\";\nconst USER_ID = \"**PUT_USER_ID_HERE**\";\n\nDo not change any other line of the code.\n\nIf you are a self-hosted user, follow these steps to activate the crypto module :\n\n1.Access your n8n instance:\nLog in to your n8n instance using your web browser by navigating to the URL of your instance, for example: http://your-n8n-instance.com.\n\n2.Go to Settings:\nIn the top-right corner, click on your username, then select \"Settings.\"\n\n3.Select General Settings:\nIn the left menu, click on \"General.\"\n\n4.Enable the Crypto module:\nScroll down to the \"Additional Node Packages\" section. You will see an option called \"crypto\" with a checkbox next to it. Check this box to enable the Crypto module.\n\n5.Save the changes:\nAt the bottom of the page, click \"Save\" to apply the changes.\n\nOnce you've followed these steps, the Crypto module should be activated for your self-hosted n8n instance. Make sure to save your changes and optionally restart your n8n instance for the changes to take effect.\n\n\n\n\n\n"}, "typeVersion": 1}, {"id": "69e3246b-f490-43e7-94ae-566eb4faf6b9", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1133, 1460], "parameters": {"width": 328.8456933308303, "height": 869.114109302513, "content": "## Performs email searches (bulk).\n\n\nThis node executes an HTTP request (POST) to search for the email addresses.\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n### You need to create credentials in the HTTP Request node :\n\n➔ In the Credential for <PERSON><PERSON>, click on - Create new Credential -.\n➔ In the Name section, write “Authorization”\n➔ In the Value section, select expression (located just above the field on the right when you hover on top of it) and write {{ $json.api.key + ':' + $json.api.signature }} .\n➔ Then click on “Save” to save the changes.\n\n### To retrieve the results :\n\nAfter some time, the results, which are downloadable, will be available in the Icypeas application  in this section : https://app.icypeas.com/bo/bulksearch?task=email-search, and you will receive the search results via <NAME_EMAIL>, providing you with the results of your search.\n\n\n\n\n"}, "typeVersion": 1}, {"id": "56abf128-57b3-4038-a262-38b09b3e3faf", "name": "Reads lastname,firstname and company from your sheet", "type": "n8n-nodes-base.googleSheets", "position": [580, 1700], "parameters": {"sheetName": {"__rl": true, "mode": "list", "value": ""}, "documentId": {"__rl": true, "mode": "list", "value": ""}}, "typeVersion": 4.1}, {"id": "f256a8e7-c8c6-4177-810e-f7af4961db05", "name": "Run bulk search (email-search)", "type": "n8n-nodes-base.httpRequest", "position": [1200, 1700], "parameters": {"url": "={{ $json.api.url }}", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "task", "value": "=email-search"}, {"name": "name", "value": "Test"}, {"name": "user", "value": "={{ $json.api.userId }}"}, {"name": "data", "value": "={{ $json.data }}"}]}, "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "X-ROCK-TIMESTAMP", "value": "={{ $json.api.timestamp }}"}]}}, "typeVersion": 4.1}], "pinData": {}, "connections": {"When clicking \"Execute Workflow\"": {"main": [[{"node": "Reads lastname,firstname and company from your sheet", "type": "main", "index": 0}]]}, "Authenticates to your Icypeas account": {"main": [[{"node": "Run bulk search (email-search)", "type": "main", "index": 0}]]}, "Reads lastname,firstname and company from your sheet": {"main": [[{"node": "Authenticates to your Icypeas account", "type": "main", "index": 0}]]}}}