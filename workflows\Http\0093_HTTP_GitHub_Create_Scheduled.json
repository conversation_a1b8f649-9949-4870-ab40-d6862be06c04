{"nodes": [{"name": "GitHub Edit", "type": "n8n-nodes-base.github", "position": [1190, 610], "parameters": {"owner": "YOUR_USERNAME", "filePath": "={{$json[\"data\"][\"name\"]}}.json", "resource": "file", "operation": "edit", "repository": "REPO_NAME", "fileContent": "={{JSON.stringify($json[\"data\"])}}", "commitMessage": "=[N8N Backup] {{$json.data[\"name\"]}} ({{new Date(Date.now()).toLocaleDateString()}})"}, "credentials": {"githubApi": "GitHub@harshil1712"}, "typeVersion": 1}, {"name": "Get Files", "type": "n8n-nodes-base.github", "position": [200, 500], "parameters": {"owner": "YOUR_USERNAME", "filePath": "/", "resource": "file", "operation": "get", "repository": "REPO", "asBinaryProperty": false}, "credentials": {"githubApi": "GitHub@harshil1712"}, "executeOnce": true, "typeVersion": 1, "alwaysOutputData": false}, {"name": "Transform", "type": "n8n-nodes-base.function", "position": [400, 500], "parameters": {"functionCode": "return items[0].json.map(item => {\n  return {\n    json: item\n  }\n});\n"}, "typeVersion": 1}, {"name": "Create file", "type": "n8n-nodes-base.github", "position": [1240, 280], "parameters": {"owner": "YOUR_USERNAME", "filePath": "={{$json[\"data\"][\"name\"]}}.json", "resource": "file", "repository": "REPO", "fileContent": "={{JSON.stringify($node['Merge'].json[\"data\"])}}", "commitMessage": "=[N8N Backup] {{$json.data[\"name\"]}}.json ({{new Date(Date.now()).toLocaleDateString()}})"}, "credentials": {"githubApi": "GitHub@harshil1712"}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [930, 280], "parameters": {"mode": "removeKeyMatches", "propertyName1": "data.name", "propertyName2": "data.name"}, "typeVersion": 1}, {"name": "Get workflows", "type": "n8n-nodes-base.httpRequest", "position": [200, 300], "parameters": {"url": "http://localhost:5678/rest/workflows", "options": {}, "authentication": "basicAuth"}, "credentials": {"httpBasicAuth": "n8n instance auth"}, "typeVersion": 1}, {"name": "Get workflow data", "type": "n8n-nodes-base.httpRequest", "position": [600, 300], "parameters": {"url": "=http://localhost:5678/rest/workflows/{{$json[\"id\"]}}", "options": {}, "authentication": "basicAuth"}, "credentials": {"httpBasicAuth": "n8n instance auth"}, "typeVersion": 1}, {"name": "Download Raw Content", "type": "n8n-nodes-base.httpRequest", "position": [600, 500], "parameters": {"url": "={{$json[\"download_url\"]}}", "options": {}, "authentication": "headerAuth", "responseFormat": "string"}, "credentials": {"httpHeaderAuth": "GitHub Token"}, "typeVersion": 1}, {"name": "transform", "type": "n8n-nodes-base.function", "position": [390, 300], "parameters": {"functionCode": "const newItems = [];\nfor (item of items[0].json.data) {\n  newItems.push({json: item});\n}\nreturn newItems;"}, "typeVersion": 1}, {"name": "Daily at 23:59", "type": "n8n-nodes-base.cron", "position": [-20, 300], "parameters": {"triggerTimes": {"item": [{"hour": 23, "minute": 59}]}}, "typeVersion": 1}, {"name": "Merge1", "type": "n8n-nodes-base.merge", "position": [970, 610], "parameters": {"mode": "removeKeyMatches", "propertyName1": "data.updatedAt", "propertyName2": "data.updatedAt"}, "typeVersion": 1}], "connections": {"Merge": {"main": [[{"node": "Create file", "type": "main", "index": 0}]]}, "Merge1": {"main": [[{"node": "GitHub Edit", "type": "main", "index": 0}]]}, "Get Files": {"main": [[{"node": "Transform", "type": "main", "index": 0}]]}, "Transform": {"main": [[{"node": "Download Raw Content", "type": "main", "index": 0}]]}, "transform": {"main": [[{"node": "Get workflow data", "type": "main", "index": 0}]]}, "Get workflows": {"main": [[{"node": "transform", "type": "main", "index": 0}]]}, "Daily at 23:59": {"main": [[{"node": "Get workflows", "type": "main", "index": 0}]]}, "Get workflow data": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}, {"node": "Merge1", "type": "main", "index": 0}]]}, "Download Raw Content": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}, {"node": "Merge1", "type": "main", "index": 1}]]}}}