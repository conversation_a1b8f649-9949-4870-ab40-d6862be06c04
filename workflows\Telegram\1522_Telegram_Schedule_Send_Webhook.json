{"id": "JH0OhDnJCwPxBJZX", "meta": {"instanceId": "d00caf92aa0876c596905aea78b35fa33a722cc8e479133822c17064d15c2c1d", "templateId": "2694", "templateCredsSetupCompleted": true}, "name": "Template - SSL Expiry Alert System", "tags": [{"id": "C38hOXfGSlCQyKoZ", "name": "Tool", "createdAt": "2025-01-23T03:38:52.218Z", "updatedAt": "2025-01-23T03:38:52.218Z"}, {"id": "DUAUvp55JytZ6Yj9", "name": "Information Retrieval", "createdAt": "2025-03-26T23:05:13.973Z", "updatedAt": "2025-03-26T23:05:13.973Z"}, {"id": "eMfCVVNXtoE4ioDe", "name": "Utility", "createdAt": "2025-03-26T23:58:28.154Z", "updatedAt": "2025-03-26T23:58:28.154Z"}], "nodes": [{"id": "b1b8afac-d0c7-4db3-aae8-cdf90c9d319e", "name": "URLs to Monitor", "type": "n8n-nodes-base.googleSheets", "position": [-240, 960], "parameters": {"columns": {"value": {}, "schema": [{"id": "ID", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "version", "type": "string", "display": true, "removed": false, "required": false, "displayName": "version", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "app", "type": "string", "display": true, "removed": false, "required": false, "displayName": "app", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "host", "type": "string", "display": true, "removed": false, "required": false, "displayName": "host", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "response_time_sec", "type": "string", "display": true, "removed": false, "required": false, "displayName": "response_time_sec", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "status", "type": "string", "display": true, "removed": false, "required": false, "displayName": "status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "result", "type": "string", "display": true, "removed": false, "required": false, "displayName": "result", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": ["ID"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {"cellFormat": "RAW", "handlingExtraData": "insertInNewColumn"}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "list", "value": *********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1aCo3vrxgheNJChElzmf4pq8h5is7E-jz4sjfV8Quprg/edit#gid=*********", "cachedResultName": "certificate-data"}, "documentId": {"__rl": true, "mode": "list", "value": "1aCo3vrxgheNJChElzmf4pq8h5is7E-jz4sjfV8Quprg", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1aCo3vrxgheNJChElzmf4pq8h5is7E-jz4sjfV8Quprg/edit?usp=drivesdk", "cachedResultName": "Monitor SSL"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "UzZqCw0fHxPn7ugj", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "5ecc3df7-e262-479c-9bdc-0542e53e77bf", "name": "Weekly Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-1220, 1400], "parameters": {"rule": {"interval": [{"field": "weeks", "triggerAtDay": [1], "triggerAtHour": 8}]}}, "typeVersion": 1.2}, {"id": "1b1d98f0-20fa-4a33-ae60-f8d1ae85175a", "name": "Fetch URLs", "type": "n8n-nodes-base.googleSheets", "position": [-1000, 1400], "parameters": {"options": {"outputFormatting": {"values": {"date": "FORMATTED_STRING", "general": "UNFORMATTED_VALUE"}}}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1aCo3vrxgheNJChElzmf4pq8h5is7E-jz4sjfV8Quprg/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1aCo3vrxgheNJChElzmf4pq8h5is7E-jz4sjfV8Quprg", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1aCo3vrxgheNJChElzmf4pq8h5is7E-jz4sjfV8Quprg/edit?usp=drivesdk", "cachedResultName": "Monitor SSL"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "UzZqCw0fHxPn7ugj", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "f74997af-a60a-4548-862d-fabf4c30fdfe", "name": "Check SSL", "type": "n8n-nodes-base.httpRequest", "position": [-680, 1400], "parameters": {"url": "=https://ssl-checker.io/api/v1/check/{{ $json[\"URL\"].replace(/^https?:\\/\\//, \"\").replace(/\\/$/, \"\") }}", "options": {}}, "typeVersion": 4.2}, {"id": "64e97269-d21e-4395-abcb-9fb3267acca6", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-740, 1140], "parameters": {"color": 6, "width": 220, "height": 424, "content": "Uses SSL-Checker.io to verify the SSL certificate of each URL. Fetches details like the host, validity period, and days remaining until expiry."}, "typeVersion": 1}, {"id": "66ea2a30-5a86-484f-a291-4656e12a276e", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-280, 880], "parameters": {"color": 6, "width": 460, "height": 240, "content": "Updates the Google Sheet with SSL details, including the expiry date and certificate status."}, "typeVersion": 1}, {"id": "5b2be392-fbbe-4315-b538-029477810079", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-500, 1140], "parameters": {"color": 6, "width": 200, "height": 424, "content": "Checks certificates and groups into:\n- invalid (certificate invalid)\n- warning (expires in <30 days)\n- notice (expires in <60 days)\n- info (everything else)"}, "typeVersion": 1}, {"id": "10abe576-d706-4467-b3b3-06e1b6168e86", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-100, 1140], "parameters": {"color": 3, "width": 280, "height": 204, "content": "Invalid\n"}, "typeVersion": 1}, {"id": "bdb59028-9bd8-4c31-9110-ef6ddb2d73ad", "name": "Switch", "type": "n8n-nodes-base.switch", "position": [-440, 1380], "parameters": {"rules": {"values": [{"outputKey": "invalid", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "ba53088b-3c74-44eb-a8f8-3c0239358b50", "operator": {"type": "boolean", "operation": "false", "singleValue": true}, "leftValue": "={{ $json.result.cert_valid }}", "rightValue": ""}]}, "renameOutput": true}, {"outputKey": "warning", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a02376b0-4712-4d78-9170-1ac9561805fd", "operator": {"type": "number", "operation": "lte"}, "leftValue": "={{ $json.result.days_left }}", "rightValue": 30}]}, "renameOutput": true}, {"outputKey": "notice", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "3a64a7c7-c78e-4aea-aedc-2accb93e476a", "operator": {"type": "number", "operation": "lte"}, "leftValue": "={{ $json.result.days_left }}", "rightValue": 60}]}, "renameOutput": true}]}, "options": {"fallbackOutput": "extra", "renameFallbackOutput": "info"}}, "typeVersion": 3.2}, {"id": "75e2af9b-eb63-484c-8fd5-5411f3c93075", "name": "Send <PERSON><PERSON>ail2", "type": "n8n-nodes-base.gmail", "position": [-240, 1180], "webhookId": "294e3ea0-dd8f-4ca5-a402-d4fcac22f24d", "parameters": {"message": "=WARNING: SSL Expiry within one month - {{ $json.result.days_left }} Days Left - {{ $json.result.host }}", "options": {"appendAttribution": false}, "subject": "=WARNING: SSL Expiry within one month - {{ $json.result.days_left }} Days Left - {{ $json.result.host }}", "emailType": "text"}, "credentials": {"gmailOAuth2": {"id": "5HpRLZpdoq3wilbn", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "7784db6d-f00e-48c8-97d7-a9795c5f4d65", "name": "Send <PERSON><PERSON>ail4", "type": "n8n-nodes-base.gmail", "position": [60, 1180], "webhookId": "d0c61174-323b-4fe8-84f8-185c2be18d33", "parameters": {"message": "=WARNING: SSL Expiry within one month - {{ $json.result.days_left }} Days Left - {{ $json.result.host }}", "options": {"appendAttribution": false}, "subject": "=URGENT: SSL-certificate invalid, action required! - {{ $json.result.days_left }} Days Left - {{ $json.result.host }}", "emailType": "text"}, "credentials": {"gmailOAuth2": {"id": "5HpRLZpdoq3wilbn", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "b5581ba7-6eb9-4b75-9b9f-6070d66ca76e", "name": "Send <PERSON><PERSON>ail6", "type": "n8n-nodes-base.gmail", "position": [-80, 1400], "webhookId": "a26a0c4e-2f58-494e-8432-c75aa5525a4c", "parameters": {"message": "=INFO: SSL Expiry check completed, took no further actions - {{ $json.result.days_left }} Days Left - {{ $json.result.host }}", "options": {"appendAttribution": false}, "subject": "=INFO: SSL Expiry check completed, took no further actions - {{ $json.result.days_left }} Days Left - {{ $json.result.host }}", "emailType": "text"}, "credentials": {"gmailOAuth2": {"id": "5HpRLZpdoq3wilbn", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "39f3800b-7c0e-43cc-8a21-7eb8c93c2666", "name": "Ntfy4", "type": "n8n-nodes-ntfy.Ntfy", "position": [60, 1400], "parameters": {"tags": "=ssl,n8n,angie", "click": "={{ $json.result.host }}", "title": "=INFO: SSL Expiry check completed for {{ $json.result.host }}.", "topic": "n8n", "message": "=INFO: SSL Expiry check completed, took no further actions - {{ $json.result.days_left }} Days Left - {{ $json.result.host }}.", "priority": 1, "additional_fields": {"bearer_token": "", "alternate_url": ""}}, "typeVersion": 1}, {"id": "584f427c-cfb2-4bf0-b2ad-4f1296eddfcf", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [-1020, 1140], "parameters": {"color": 6, "width": 260, "height": 424, "content": "Pulls the list of URLs to monitor from the Google Sheet. Ensure you clone the Google Sheet worksheet and update this node with its URL.\n\nThis is the Sheet Layout:\n  A              B              C\n1 URL\n2 n8n.io\n3 amazon.com\n4 google.com\n5 chat.openai.com"}, "typeVersion": 1}, {"id": "04a97e64-7f7d-4e4e-a441-93c72ceabf5c", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [-1300, 1140], "parameters": {"color": 7, "width": 260, "height": 424, "content": "Triggers the workflow once a week."}, "typeVersion": 1}, {"id": "19e7ea5a-2b9d-45aa-8ab4-14d0fd181e89", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [-280, 1360], "parameters": {"color": 4, "width": 170, "height": 204, "content": "Notice\n"}, "typeVersion": 1}, {"id": "1aa248e8-58a2-415c-9de9-ec58994d2160", "name": "Send <PERSON><PERSON>ail1", "type": "n8n-nodes-base.gmail", "position": [-240, 1400], "webhookId": "039c4f5b-b91b-47b5-8dc2-49c003c18362", "parameters": {"message": "=SSL Expiry - {{ $json.result.days_left }} Days Left - {{ $json.result.host }}", "options": {"appendAttribution": false}, "subject": "=INFO: SSL Expiry - {{ $json.result.days_left }} Days Left - {{ $json.result.host }}", "emailType": "text"}, "credentials": {"gmailOAuth2": {"id": "5HpRLZpdoq3wilbn", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "0a2d85a2-f9ad-445f-9839-f4643d8389c7", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-280, 1140], "parameters": {"width": 170, "height": 204, "content": "Warning\n"}, "typeVersion": 1}, {"id": "b87be8c5-4b2a-4f1e-8f20-c7c341af647e", "name": "Telegram", "type": "n8n-nodes-base.telegram", "position": [-80, 1180], "webhookId": "********-9298-457e-bce8-391248f2e56c", "parameters": {"text": "=URGENT: SSL-certificate invalid, action required! - {{ $json.result.days_left }} Days Left - {{ $json.result.host }}", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "ZPSuzVOwshFnDrCl", "name": "Telegram account 4"}}, "typeVersion": 1.2}, {"id": "a7c9f536-5e02-4d03-98bc-e3b4e3844ffb", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-100, 1360], "parameters": {"color": 4, "width": 280, "height": 204, "content": "Info\n"}, "typeVersion": 1}, {"id": "68cc4856-6e1d-4cae-8075-0a880d5c1a0b", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-1300, 400], "parameters": {"color": 7, "width": 1000, "height": 720, "content": "## SSL Expiry Alert System\n\n### Who is this for?\nThis workflow is ideal for administrators or IT professionals responsible for monitoring SSL certificates of multiple websites to ensure they do not expire unexpectedly.\n\n### Problem\nSSL certificates play a crucial role in ensuring secure communication over the internet. However, if not monitored closely, they can expire, leading to potential security risks and service disruption. This workflow helps in proactively monitoring SSL certificate expiry dates.\n\n### Functionality\n1. Pulls URLs to monitor from a Google Sheet.\n2. Checks SSL certificates using SSL-Checker.io.\n3. Updates Google Sheet with SSL details such as expiry date and certificate status.\n4. Sends email alerts for SSL certificates nearing expiry (<30 days) or invalid certificates.\n\n### Setup\n1. Clone the provided Google Sheet and update the Google Sheet URL in the \"URLs to Monitor\" node.\n2. Set up Google Sheets and Gmail credentials in n8n.\n3. Configure the Discourse Trigger for weekly monitoring.\n4. Customize email alert settings as needed.\n\n### Customization\n- Modify the frequency of monitoring by adjusting the trigger interval in the \"Weekly Trigger\" node.\n- Customize email content and recipients in the \"Send Alert Email\" node.\n- Extend functionality by adding additional checks or actions based on SSL certificate status.\n\n### Note\nEnsure proper authentication and authorization for accessing Google Sheets, SSL-Checker.io, and Gmail accounts within the workflow."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "87e3614a-0720-47be-9224-dd5829c4f15c", "connections": {"Switch": {"main": [[{"node": "Send <PERSON><PERSON>ail4", "type": "main", "index": 0}, {"node": "Telegram", "type": "main", "index": 0}], [{"node": "Send <PERSON><PERSON>ail2", "type": "main", "index": 0}], [{"node": "Send <PERSON><PERSON>ail1", "type": "main", "index": 0}], [{"node": "Send <PERSON><PERSON>ail6", "type": "main", "index": 0}, {"node": "Ntfy4", "type": "main", "index": 0}]]}, "Check SSL": {"main": [[{"node": "URLs to Monitor", "type": "main", "index": 0}, {"node": "Switch", "type": "main", "index": 0}]]}, "Fetch URLs": {"main": [[{"node": "Check SSL", "type": "main", "index": 0}]]}, "Weekly Trigger": {"main": [[{"node": "Fetch URLs", "type": "main", "index": 0}]]}}}