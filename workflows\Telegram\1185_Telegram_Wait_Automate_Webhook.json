{"id": "8tusZTTtcyaiznEG", "meta": {"instanceId": "31e69f7f4a77bf465b805824e303232f0227212ae922d12133a0f96ffeab4fef", "templateCredsSetupCompleted": true}, "name": "🤖 AI Powered RAG Chatbot for Your Docs + Google Drive + Gemini + Qdrant", "tags": [], "nodes": [{"id": "7ad5796b-d1a0-4cc1-bed6-105ff499beeb", "name": "Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [620, 720], "parameters": {"options": {"metadata": {"metadataValues": [{"name": "file_id", "value": "={{ $json.id }}"}, {"name": "pubkey", "value": "={{ $json.name }}"}, {"name": "=overarching_theme", "value": "={{ $('Extract Meta Data').item.json.output.overarching_theme }}"}, {"name": "recurring_topics", "value": "={{ $('Extract Meta Data').item.json.output.recurring_topics }}"}, {"name": "pain_points", "value": "={{ $('Extract Meta Data').item.json.output.pain_points }}"}, {"name": "analytical_insights", "value": "={{ $('Extract Meta Data').item.json.output.analytical_insights }}"}, {"name": "conclusion", "value": "={{ $('Extract Meta Data').item.json.output.conclusion }}"}, {"name": "keywords", "value": "={{ $('Extract Meta Data').item.json.output.keywords }}"}]}}, "dataType": "binary", "binaryMode": "specificField"}, "typeVersion": 1}, {"id": "84986ee2-4d79-49e8-8778-b8a955cf2174", "name": "Token Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterTokenSplitter", "position": [720, 860], "parameters": {"chunkSize": 3000}, "typeVersion": 1}, {"id": "82aa7016-f3af-4de8-a410-b1c802041213", "name": "Qdrant Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "onError": "continueRegularOutput", "position": [540, 520], "parameters": {"mode": "insert", "options": {}, "qdrantCollection": {"__rl": true, "mode": "id", "value": "=nostr-damus-user-profiles"}}, "credentials": {"qdrantApi": {"id": "DJQ4hVAVdWZytjr2", "name": "QdrantApi account"}}, "executeOnce": false, "retryOnFail": true, "typeVersion": 1}, {"id": "07f4d14a-1864-406b-946b-09c776e3038b", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "onError": "continueRegularOutput", "position": [180, -140], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "b0f9b066-62de-41bc-afce-dcb4d20d100c", "name": "Wait", "type": "n8n-nodes-base.wait", "position": [1020, 840], "webhookId": "237d7f8a-aead-479a-b813-f407d1f37fa5", "parameters": {}, "typeVersion": 1.1}, {"id": "2a883003-f5ec-409e-97e4-809360ca11ed", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-1160, -280], "parameters": {}, "typeVersion": 1}, {"id": "cfbb6b77-142f-4689-b051-436f40ababe6", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1120, -20], "parameters": {"options": {"temperature": 0.4}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "L9UNQHflYlyF9Ngd", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "c9c50387-64a3-473f-9ea7-509a301836a3", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [820, 200], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineAll"}, "typeVersion": 3}, {"id": "ea143b9f-9bd8-4f3f-8979-5a0fd2691e9d", "name": "Extract Meta Data", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [1020, -180], "parameters": {"text": "={{ $json.data }}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}, "attributes": {"attributes": [{"name": "overarching_theme", "description": "Summarize the main theme(s) discussed in the \"Overarching Theme\" section."}, {"name": "recurring_topics", "description": "List the recurring topics mentioned in the \"Common Threads\" section as an array of strings."}, {"name": "pain_points", "description": "Summarize the user's frustrations or challenges mentioned in the \"Pain Points\" section as an array of strings."}, {"name": "analytical_insights", "description": "Extract a list of key analytical observations from the \"Analytical Insights\" section, including shifts in tone or behavior."}, {"name": "conclusion", "description": "Summarize the conclusions drawn about the user’s threads and their overall focus."}, {"name": "keywords", "description": "Generate a list of 10 keywords that capture the essence of the document (e.g., \"askNostr,\" \"decentralization,\" \"spam filtering\")."}]}}, "typeVersion": 1}, {"id": "2ddd778f-6676-4a4b-b592-536401831bc8", "name": "Get File Contents", "type": "n8n-nodes-base.extractFromFile", "position": [820, -180], "parameters": {"options": {}, "operation": "text"}, "typeVersion": 1}, {"id": "cdbb8bbc-967c-4f01-b844-4d31328153df", "name": "Download File From Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [500, -180], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "options": {}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "UhdXGYLTAJbsa0xX", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "399e196c-76ec-4077-aacc-f669173b0229", "name": "Find File Ids in Google Drive Folder", "type": "n8n-nodes-base.googleDrive", "position": [-620, -160], "parameters": {"filter": {"driveId": {"mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "mode": "id", "value": "={{ $json.folder_id }}"}}, "options": {}, "resource": "fileFolder", "returnAll": true}, "credentials": {"googleDriveOAuth2Api": {"id": "UhdXGYLTAJbsa0xX", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "3bc4f005-c6c8-4f73-816a-1573d6dfb62f", "name": "text-embeddings-3-large", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [480, 720], "parameters": {"model": "text-embedding-3-large", "options": {}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "5d42b34a-7156-4163-a2c4-a9cb3c984c96", "name": "Google Folder ID", "type": "n8n-nodes-base.set", "position": [-820, -160], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "e6f6188f-c895-4c8c-b39a-0ef55b490fd6", "name": "folder_id", "type": "string", "value": "[Your-Google-Folder-ID]"}]}}, "typeVersion": 3.4}, {"id": "7be310c2-d30f-42a2-8f57-33c7b184e429", "name": "gpt-4o-mini1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-400, 940], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "18aacfd9-3f6e-4fba-9f68-d8cc95bc667b", "name": "Delete Qdrant Points by File ID", "type": "@n8n/n8n-nodes-langchain.code", "position": [-500, 800], "parameters": {"code": {"execute": {"code": "const { QdrantVectorStore } = require(\"@langchain/qdrant\");\nconst { OpenAIEmbeddings } = require(\"@langchain/openai\");\n\n// Qdrant connection details\nconst url = \"http://localhost:6333/\";\nconst apiKey = \"\";\n\n// OpenAI API configuration\nconst openAIApiKey = \"[Your-OpenAI-API-Key]\";\n\n// Get input data\nconst items = this.getInputData()[0];\n// console.log(items)\n\nconst collectionName =  items.json.qdrant_collection_name;\n// console.log(collectionName)\n\nasync function deleteDocumentsFromQdrant() {\n  try {\n    // Initialize OpenAI embeddings\n    const embeddings = new OpenAIEmbeddings({\n      model: \"text-embedding-3-large\",\n      openAIApiKey: openAIApiKey\n    });\n\n    // Connect to existing Qdrant collection\n    const vectorStore = await QdrantVectorStore.fromExistingCollection(embeddings, {\n      url: url,\n      apiKey: apiKey,\n      collectionName: collectionName,\n    });\n\n    const fileIds = items.json.appended_id.map(item => item);\n\n    console.log(fileIds)\n\n    // Delete points by fileId\n    const deletionResults = await Promise.all(fileIds.map(async (file_id) => {\n      const filter = {\n        must: [\n          {\n            key: \"metadata.file_id\",\n            match: { value: file_id }\n          }\n        ]\n      };\n\n      try {\n        // Access the underlying Qdrant client to perform the deletion\n        await vectorStore.client.delete(collectionName, { filter });\n        return { file_id, status: \"deleted\" };\n      } catch (error) {\n        console.error(`Error deleting documents for fileId ${file_id}:`, error);\n        return { file_id, status: \"error\", message: error.message };\n      }\n    }));\n\n    return deletionResults;\n  } catch (error) {\n    console.error(\"An error occurred during the deletion process:\", error);\n    return error.message;\n  }\n}\n\n// Execute the deletion process\ntry {\n  const result = await deleteDocumentsFromQdrant();\n  console.log(\"Deletion process completed:\", result);\n  return [];\n} catch (error) {\n  console.error(\"Failed to execute deletion process:\", error);\n  return [{ json: { error } }];\n}\n\n"}}, "inputs": {"input": [{"type": "main", "required": true, "maxConnections": 1}, {"type": "ai_languageModel", "required": true, "maxConnections": 1}]}, "outputs": {"output": [{"type": "main"}]}}, "typeVersion": 1}, {"id": "329cec97-aac1-4714-97ce-add5915b1078", "name": "Qdrant Collection Name", "type": "n8n-nodes-base.set", "position": [-700, 100], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "11fa71e9-6cbc-4183-9439-e3379b2b970e", "name": "qdrant_collection_name", "type": "string", "value": "nostr-damus-user-profiles"}]}}, "typeVersion": 3.4}, {"id": "82e98da8-530d-4a3c-97e6-5e91ad644a46", "name": "File Id List", "type": "n8n-nodes-base.summarize", "position": [-700, 280], "parameters": {"options": {}, "fieldsToSummarize": {"values": [{"field": "id", "aggregation": "append"}]}}, "typeVersion": 1.1}, {"id": "f3def614-2709-4847-831d-d6a80c86ec1c", "name": "Merge1", "type": "n8n-nodes-base.merge", "position": [-340, 340], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3}, {"id": "cfe399fb-8560-4ac4-98f7-4f539ae6a52c", "name": "Merge2", "type": "n8n-nodes-base.merge", "position": [-80, -140], "parameters": {}, "typeVersion": 3}, {"id": "1059da47-2abd-4884-b9c2-6b7d1623e31d", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1280, 560], "parameters": {"color": 3, "width": 1180, "height": 760, "content": "## Prepare Qdrant Vector Store"}, "typeVersion": 1}, {"id": "2616e08f-070e-45b5-906f-40e832519aa2", "name": "Confirm Qdrant Delete Points", "type": "n8n-nodes-base.telegram", "position": [-1060, 760], "webhookId": "29aac1ac-9345-4e44-babf-ebcfae701d88", "parameters": {"chatId": "={{ $env.TELEGRAM_CHAT_ID }}", "message": "=WARNING - {{ $json.appended_id.length }} Records in the Qdrant vector store collection \"{{ $json.qdrant_collection_name }}\" will be deleted.  Are you sure you want to continue?  This action cannot be undone!", "options": {"limitWaitTime": {"values": {"resumeUnit": "minutes", "resumeAmount": 15}}}, "operation": "sendAndWait", "approvalOptions": {"values": {"approvalType": "double"}}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "9d167192-e87b-41be-87f2-d7ddf866bb8d", "name": "If", "type": "n8n-nodes-base.if", "position": [-1060, 1000], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "20f530d6-fd55-420d-b8a9-70e5303f688e", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.data.approved }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "1178f8cf-ece0-4b11-942d-18dd025da366", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [320, 420], "parameters": {"color": 7, "width": 920, "height": 640, "content": "## Perform Qdrant Vector Store Operations"}, "typeVersion": 1}, {"id": "cbf17a08-2f4c-4605-8418-5bf93ade7cf4", "name": "Send Declined Message", "type": "n8n-nodes-base.telegram", "position": [-740, 1120], "webhookId": "382a3b43-b83f-47b1-a276-67c6b98a441a", "parameters": {"text": "Qdrant vector store upsert declined", "chatId": "={{ $env.TELEGRAM_CHAT_ID }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "890f478a-e000-418d-8582-4fef946e44d8", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-900, -380], "parameters": {"width": 480, "height": 840, "content": "## 🌟Workflow Config\n\n- Google Drive Folder Id\n- Qdrant Collection Name\n- List of Google Drive File Id's"}, "typeVersion": 1}, {"id": "d2bc2fed-c134-4e54-bd9d-69996be966a3", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [740, -280], "parameters": {"color": 6, "width": 640, "height": 420, "content": "## Extract <PERSON><PERSON><PERSON> for Qdrant Hybrid Search"}, "typeVersion": 1}, {"id": "61f3c21e-bc2a-47bc-8ab1-678b1f425ee3", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [400, -280], "parameters": {"color": 2, "width": 300, "height": 320, "content": "## Google Drive"}, "typeVersion": 1}, {"id": "61e608e4-ac6a-4131-9ca8-c46a5134bff9", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [80, -360], "parameters": {"color": 5, "width": 1360, "height": 1480, "content": "## ✨ Save Documents to Qdrant Vector Store"}, "typeVersion": 1}, {"id": "410681cb-e757-42c0-89de-902edbe998e3", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-580, 680], "parameters": {"color": 5, "width": 420, "height": 400, "content": "## Delete From Qdrant Vector Store\nThis operation can not be undone!!!"}, "typeVersion": 1}, {"id": "238469ce-3710-4681-b1b2-200c1b699d5e", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-1200, 640], "parameters": {"color": 4, "width": 380, "height": 520, "content": "## Human In The Loop\nUser must verify deletion of points from Qdrant vector store"}, "typeVersion": 1}, {"id": "0b2f6ff4-dd02-4f0e-b2eb-60b1ce736cf5", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [-1280, -380], "parameters": {"color": 4, "width": 340, "height": 460, "content": "## 👍Start Here!"}, "typeVersion": 1}, {"id": "abde545b-1a23-4b3e-8046-335b9d0fa445", "name": "Webhook", "type": "n8n-nodes-base.webhook", "disabled": true, "position": [-1160, -100], "webhookId": "3a30557f-9264-4bc8-a253-a9356677c791", "parameters": {"path": "upsert", "options": {}}, "typeVersion": 2}, {"id": "897d29af-719d-4fe7-93a9-44c693cc6547", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-600, -1200], "parameters": {"text": "={{ $json.chatInput }}", "options": {"systemMessage": "=You are an intelligent assistant specialized in answering user questions using Nostr user profiles. Your primary goal is to provide precise, contextually relevant, and concise answers based on the tools and resources available.\n\n### TOOL\nUse the \"nostr_damus_user_profiles\" tool to:\n- perform semantic similarity searches and retrieve information from Nostr user profiles relevant to the user's query.\n- access detailed information about Nostr and/or Damus users when additional context or specifics are required.\n\n### Key Instructions\n1. **Response Guidelines**:\n   - Clearly explain how the retrieved information addresses the user's query, if applicable.\n   - If no relevant information is found, respond with: \"I cannot find the answer in the available resources.\"\n\n2. **Focus and Relevance**:\n   - Ensure all responses are directly aligned with the user's question.\n   - Avoid including extraneous details or relying solely on internal knowledge.\n"}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "316b0d8d-bbc7-4c40-b6d0-d0c762554fca", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [-580, -1000], "parameters": {"contextWindowLength": 40}, "typeVersion": 1.3}, {"id": "f1e342a8-d50e-48a8-9d7a-850f8be93cff", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-1160, -1200], "webhookId": "5f1c0c82-0ff9-40c7-9e2e-b1a96ffe24cd", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "12902fa9-5bcb-4e8b-ac13-7f4cb6e7e1d0", "name": "Google Gemini Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [-760, -1000], "parameters": {"options": {"maxOutputTokens": 8192}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "L9UNQHflYlyF9Ngd", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "1c5818d4-6251-4d14-8b4f-d1f3ddfbed38", "name": "text-embeddings-3-large1", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [-320, -840], "parameters": {"model": "text-embedding-3-large", "options": {}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "********-5d2b-4ac7-8e69-e75bd66a1ad4", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [-900, -1300], "parameters": {"color": 5, "width": 860, "height": 680, "content": "## 🤖Retrieve Content from Qdrant Vector Store"}, "typeVersion": 1}, {"id": "b45b18e4-7773-4eec-837b-f43fc0eeb90c", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [-1280, -1300], "parameters": {"color": 4, "width": 340, "height": 320, "content": "## 🗣️ Chat with Your Documents"}, "typeVersion": 1}, {"id": "8fe891dd-dee6-4abb-bc09-d90e6d7bf3cb", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [-1360, -480], "parameters": {"color": 7, "width": 2880, "height": 1880, "content": "# Step 1 - Save Documents to Qdrant Vector Store"}, "typeVersion": 1}, {"id": "4a9bf716-3fc3-4ccc-b831-19a1afdb4588", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [-1360, -1420], "parameters": {"color": 7, "width": 1780, "height": 880, "content": "# Step 2 - Chat with Your Documents from Qdrant Vector Store"}, "typeVersion": 1}, {"id": "fccedfde-c5aa-4237-9d1c-feb9ebabf4f3", "name": "Google Drive", "type": "n8n-nodes-base.googleDrive", "disabled": true, "position": [120, -780], "parameters": {"name": "=Nostr <PERSON> - Avatar - {{ $now }}", "content": "={{ $json.output }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "root", "cachedResultName": "/ (Root folder)"}, "operation": "createFromText"}, "credentials": {"googleDriveOAuth2Api": {"id": "UhdXGYLTAJbsa0xX", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "6d6813f6-4f2e-499a-9e5c-fe15624a91ef", "name": "Respond to User", "type": "n8n-nodes-base.set", "position": [120, -960], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "cd8f88e1-19c0-4b9e-914d-e2e7ba21d9fa", "name": "output", "type": "string", "value": "={{ $json.output }}"}]}}, "typeVersion": 3.4}, {"id": "1c73bae9-2bdd-4383-a6f8-3cf43d3c15be", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [0, -1300], "parameters": {"color": 4, "width": 340, "height": 300, "content": "## Save Chat History"}, "typeVersion": 1}, {"id": "db3dcc69-d1d0-48a1-b949-37f2de802b4a", "name": "Update Chat History", "type": "n8n-nodes-base.googleDocs", "position": [120, -1200], "parameters": {"actionsUi": {"actionFields": [{"text": "=\n-------------------------------\n\n{{ $now }}\n\n{{ $('When chat message received').item.json.chatInput  }}\n\n{{ $json.output }}", "action": "insert"}]}, "operation": "update", "documentURL": "1ej_qLolUFp1h4eZkrb99T3DWQ3JOwXVEMS3VUjWyVf0"}, "credentials": {"googleDocsOAuth2Api": {"id": "YWEHuG28zOt532MQ", "name": "Google Docs account"}}, "typeVersion": 2}, {"id": "2dd28eed-b71b-44fb-bb78-8cb50b0d0c93", "name": "Qdrant Vector Store Tool", "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "position": [-420, -1000], "parameters": {"mode": "retrieve-as-tool", "topK": 20, "options": {}, "toolName": "nostr_damus_user_profiles", "toolDescription": "Retrieve information about Nostr or Damus users", "qdrantCollection": {"__rl": true, "mode": "list", "value": "nostr-damus-user-profiles", "cachedResultName": "nostr-damus-user-profiles"}}, "credentials": {"qdrantApi": {"id": "DJQ4hVAVdWZytjr2", "name": "QdrantApi account"}}, "typeVersion": 1}, {"id": "3b130cf8-ab8e-4001-a3d0-a129194aee98", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "disabled": true, "position": [-760, -820], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "ccc64645-ad14-4b6b-a507-0b5fb0ac945f", "name": "Send Completed Message", "type": "n8n-nodes-base.telegram", "position": [500, 200], "webhookId": "382a3b43-b83f-47b1-a276-67c6b98a441a", "parameters": {"text": "Qdrant vector store upsert completed", "chatId": "={{ $env.TELEGRAM_CHAT_ID }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "885fef53-2246-45b1-a122-ab5b82374585", "name": "Sticky Note14", "type": "n8n-nodes-base.stickyNote", "position": [-2120, -480], "parameters": {"width": 700, "height": 1240, "content": "# 🤖 AI-Powered RAG Chatbot with Google Drive Integration\n\nThis workflow creates a powerful RAG (Retrieval-Augmented Generation) chatbot that can process, store, and interact with documents from Google Drive using Qdrant vector storage and Google's Gemini AI.\n\n## How It Works\n\n### Document Processing & Storage 📚\n- Retrieves documents from a specified Google Drive folder\n- Processes and splits documents into manageable chunks\n- Extracts metadata using AI for enhanced search capabilities\n- Stores document vectors in Qdrant for efficient retrieval\n\n### Intelligent Chat Interface 💬\n- Provides a conversational interface powered by Google Gemini\n- Uses RAG to retrieve relevant context from stored documents\n- Maintains chat history in Google Docs for reference\n- Delivers accurate, context-aware responses\n\n### Vector Store Management 🗄️\n- Features secure delete operations with human verification\n- Includes Telegram notifications for important operations\n- Maintains data integrity with proper version control\n- Supports batch processing of documents\n\n## Setup Steps\n\n1. Configure API Credentials:\n   - Set up Google Drive & Docs access\n   - Configure Gemini AI API\n   - Set up Qdrant vector store connection\n   - Add Telegram bot for notifications\n\n2. Configure Document Sources:\n   - Set Google Drive folder ID\n   - Define Qdrant collection name\n   - Set up document processing parameters\n\n3. Test and Deploy:\n   - Verify document processing\n   - Test chat functionality\n   - Confirm vector store operations\n   - Check notification system\n\n\nThis workflow is ideal for organizations needing to create intelligent chatbots that can access and understand large document repositories while maintaining context and providing accurate responses through RAG technology.\n"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"timezone": "America/Vancouver", "callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1"}, "versionId": "0c0d90e5-02f9-4169-b477-fd90c52ce44e", "connections": {"If": {"main": [[{"node": "Delete Qdrant Points by File ID", "type": "main", "index": 0}], [{"node": "Send Declined Message", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "Qdrant Vector Store", "type": "main", "index": 0}]]}, "Merge1": {"main": [[{"node": "Confirm Qdrant Delete Points", "type": "main", "index": 0}]]}, "Merge2": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Google Folder ID", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Update Chat History", "type": "main", "index": 0}, {"node": "Respond to User", "type": "main", "index": 0}]]}, "Data Loader": {"ai_document": [[{"node": "Qdrant Vector Store", "type": "ai_document", "index": 0}]]}, "File Id List": {"main": [[{"node": "Merge1", "type": "main", "index": 1}]]}, "gpt-4o-mini1": {"ai_languageModel": [[{"node": "Delete Qdrant Points by File ID", "type": "ai_languageModel", "index": 0}]]}, "Token Splitter": {"ai_textSplitter": [[{"node": "Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Send Completed Message", "type": "main", "index": 0}], [{"node": "Download File From Google Drive", "type": "main", "index": 0}]]}, "Respond to User": {"main": [[]]}, "Google Folder ID": {"main": [[{"node": "Find File Ids in Google Drive Folder", "type": "main", "index": 0}]]}, "Extract Meta Data": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Get File Contents": {"main": [[{"node": "Extract Meta Data", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[]]}, "Qdrant Vector Store": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Update Chat History": {"main": [[]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Qdrant Collection Name": {"main": [[{"node": "Merge1", "type": "main", "index": 0}]]}, "text-embeddings-3-large": {"ai_embedding": [[{"node": "Qdrant Vector Store", "type": "ai_embedding", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Extract Meta Data", "type": "ai_languageModel", "index": 0}]]}, "Qdrant Vector Store Tool": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]], "ai_vectorStore": [[]]}, "text-embeddings-3-large1": {"ai_embedding": [[{"node": "Qdrant Vector Store Tool", "type": "ai_embedding", "index": 0}]]}, "Google Gemini Chat Model1": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Confirm Qdrant Delete Points": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Delete Qdrant Points by File ID": {"main": [[{"node": "Merge2", "type": "main", "index": 1}]]}, "Download File From Google Drive": {"main": [[{"node": "Get File Contents", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Google Folder ID", "type": "main", "index": 0}]]}, "Find File Ids in Google Drive Folder": {"main": [[{"node": "File Id List", "type": "main", "index": 0}, {"node": "Qdrant Collection Name", "type": "main", "index": 0}, {"node": "Merge2", "type": "main", "index": 0}]]}}}