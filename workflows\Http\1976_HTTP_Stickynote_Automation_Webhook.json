{"id": "s6nTFZfg6xjWyJRX", "meta": {"instanceId": "4b761cc6ed5ba54435cd56551f1d8f4e82e89d5a18fc96f22d0649b94ad18c78", "templateCredsSetupCompleted": true}, "name": "React to PDFMonkey Callback", "tags": [], "nodes": [{"id": "bca61663-2317-4f5a-8117-e417ab9ffcb1", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-160, -380], "parameters": {"width": 860, "height": 500, "content": "# React to PDFMonkey Callback\nWhen a PDF is generated by PDFMonkey, retrieve the PDF file and use it as needed.\n\n### Configuration\nCopy the webhook URL and add it to your PDFMonkey Webhooks dashboard ([PDFMonkey Webhooks](https://dashboard.pdfmonkey.io/webhooks)) to define your N8N callback URL in your PDFMonkey account.\n\nFor more information, visit: [PDFMonkey Webhooks Documentation](https://docs.pdfmonkey.io/pdfmonkey-features/webhooks#defining-a-workspace-wide-webhook)\n\n\n### Usage\nOn success: Download the generated PDF.\nOn failure: Handle it as needed. 😉\n\n\n### Help\nNeed assistance? Reach out to us via chat on pdfmonkey.io, and we'll do our best to help you! 🚀"}, "typeVersion": 1}, {"id": "31ef2b09-e36f-4a9d-8eef-724211d7e2d4", "name": "On PDFMonkey generation process end", "type": "n8n-nodes-base.webhook", "position": [-140, 160], "webhookId": "ed9c1bf7-efdd-4d17-8c28-e74c22d017ce", "parameters": {"path": "ed9c1bf7-efdd-4d17-8c28-e74c22d017ce", "options": {}, "httpMethod": "POST"}, "typeVersion": 2}, {"id": "08cfde4f-637b-4cf4-a2c2-92e4e15ad6cc", "name": "Check if generation was successful", "type": "n8n-nodes-base.if", "position": [120, 160], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "68eaaea7-d94b-40fd-819f-331261843c67", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.body.document.status }}", "rightValue": "success"}]}}, "typeVersion": 2.2}, {"id": "051ec2f5-e96e-41dd-a753-db70cd1a1729", "name": "On success: download the PDF file", "type": "n8n-nodes-base.httpRequest", "position": [520, 140], "parameters": {"url": "={{ $json.body.document.download_url }}", "options": {}}, "typeVersion": 4.2}], "active": false, "pinData": {}, "settings": {"callerPolicy": "any", "executionOrder": "v1"}, "versionId": "56e711af-d87a-4822-9b49-bf7bebd373df", "connections": {"On success: download the PDF file": {"main": [[]]}, "Check if generation was successful": {"main": [[{"node": "On success: download the PDF file", "type": "main", "index": 0}]]}, "On PDFMonkey generation process end": {"main": [[{"node": "Check if generation was successful", "type": "main", "index": 0}]]}}}