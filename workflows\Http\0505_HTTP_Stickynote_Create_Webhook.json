{"meta": {"instanceId": "2490ba08907e49e216e6667acbe7f8867d372c76c9bd95e87bb8d210bd552e80"}, "nodes": [{"id": "3ebbf865-26f6-456f-83bd-33fa72bc09ea", "name": "Token SuiteCRM", "type": "n8n-nodes-base.httpRequest", "position": [480, 800], "parameters": {"url": "=https://SUITECRMURLSERVER/Api/access_token", "options": {}, "requestMethod": "POST", "bodyParametersUi": {"parameter": [{"name": "grant_type", "value": "client_credentials"}, {"name": "client_id", "value": "IDVALUE"}, {"name": "client_secret", "value": "PWDVALUE"}]}, "allowUnauthorizedCerts": true}, "notesInFlow": true, "typeVersion": 1}, {"id": "763bd0bc-7c08-496d-82b7-1fb021c1e6e1", "name": "Captain<PERSON><PERSON>", "type": "n8n-nodes-base.httpRequest", "position": [-360, 560], "parameters": {"url": "=https://api.captainverify.com/v2/verify?apikey=YOURAPIKEY&email={{ $json.body.data.fields[0].value }}", "options": {"response": {"response": {"responseFormat": "json"}}}}, "typeVersion": 4.2}, {"id": "9d1f03eb-4be2-4e72-bc86-723d92869888", "name": "If mail ok", "type": "n8n-nodes-base.if", "position": [220, 580], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "ea7e2b2b-35cc-469c-b01b-eeb4f0030aa5", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.result }}", "rightValue": "invalid"}]}}, "typeVersion": 2}, {"id": "03ffff8c-401a-4723-80c6-df702cda2ba5", "name": "If Credits OK", "type": "n8n-nodes-base.if", "position": [-180, 560], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "007b0ec4-870d-48d6-a961-adff23ceabd4", "operator": {"type": "number", "operation": "lt"}, "leftValue": "={{ $json.credits }}", "rightValue": 100}]}}, "typeVersion": 2}, {"id": "487b4746-48d3-40c2-a21c-0a3aa38ba780", "name": "<PERSON>y <PERSON>s Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-600, 560], "parameters": {}, "typeVersion": 1}, {"id": "2ff81440-ffb4-4d92-8fb0-0a46f6488a2e", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-420, 382.5935162094766], "parameters": {"width": 221.29675810473822, "height": 324.588528678304, "content": "## CaptainVerify \n**Verify your email !** To reduce bounce email for your future campains. [Link](https://captainverify.com)\n\nChange **YOURAPIKEY** with yours"}, "typeVersion": 1}, {"id": "73d00252-c081-451c-84df-67e44bf0bb11", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-60, 180], "parameters": {"color": 5, "width": 266.18453865336653, "height": 395.6608478802989, "content": "## Warning about your credits \nNotify with a message and level of credits in your NextCloud Discussion\n\nChange **URLSERVERNEXTCLOUD** with yours\nand **DISCUSSIONCODE** with the code of target discussion"}, "typeVersion": 1}, {"id": "da8758f6-82f6-481c-97cc-40292579d723", "name": "Notif Talk credits", "type": "n8n-nodes-base.httpRequest", "position": [20, 420], "parameters": {"url": "=https://URLSERVERNEXTCLOUD/ocs/v2.php/apps/spreed/api/v1/chat/DISCUSSIONCODE", "options": {"bodyContentType": "json", "bodyContentCustomMimeType": "application/json"}, "requestMethod": "POST", "authentication": "basicAuth", "jsonParameters": true, "bodyParametersJson": "={\n\"message\":\"Low credits for CaptainVerify Mail. Balance = {{ $json[\"credits\"] }}\"\n}", "headerParametersJson": "={\"OCS-APIRequest\":\"true\"}"}, "notesInFlow": true, "typeVersion": 1, "continueOnFail": true}, {"id": "569b9fd4-85d0-4300-8dc1-ab71fc5c2d09", "name": "Notif Talk bad email", "type": "n8n-nodes-base.httpRequest", "position": [420, 420], "parameters": {"url": "=https://URLSERVERNEXTCLOUD/ocs/v2.php/apps/spreed/api/v1/chat/DISCUSSIONCODE", "options": {"bodyContentType": "json", "bodyContentCustomMimeType": "application/json"}, "requestMethod": "POST", "authentication": "basicAuth", "jsonParameters": true, "bodyParametersJson": "={\n\"message\":\"Invalid mail on submission form for contact : {{ $('Execute Workflow Trigger').item.json[\"body\"][\"data\"][\"fields\"][1][\"value\"] }} et mail : {{ $('CaptainMail').item.json[\"email\"] }} \"\n}", "headerParametersJson": "={\"OCS-APIRequest\":\"true\"}"}, "notesInFlow": true, "typeVersion": 1, "continueOnFail": true}, {"id": "6b555580-b66d-485d-b1b7-dd9fbd580294", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [340, 180], "parameters": {"color": 5, "width": 266.18453865336653, "height": 395.6608478802989, "content": "## Warning bad email \nNotify with a message for contact with invalid mail in your NextCloud Discussion\n\nChange **URLSERVERNEXTCLOUD** with yours\nand **DISCUSSIONCODE** with the code of target discussion"}, "typeVersion": 1}, {"id": "fcc84bdb-9ae2-44c9-a038-c9282cfe1373", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [420, 600], "parameters": {"color": 3, "width": 226.00997506234387, "height": 358.40399002493757, "content": "## Auth SuiteCRM \n**Get Token** with V8 API. [Guide](https://docs.suitecrm.com/developer/api/developer-setup-guide/)\n\nChange **SUITECRMURLSERVER** with yours\nChange **IDVALUE** and **PWDVALUE** with a specific user in SuiteCRM"}, "typeVersion": 1}, {"id": "d9a96370-f545-4daf-a2e2-af7efd5fda42", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-680, 461.97007481296754], "parameters": {"color": 7, "height": 252.8428927680797, "content": "## WEBHOOK \n**TRIGGER** with the FormsTool of your choice."}, "typeVersion": 1}, {"id": "8e50db5a-5945-468c-ae92-239b8eb74f31", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1060, 120], "parameters": {"width": 221.29675810473822, "height": 80, "content": "## Captain<PERSON>eri<PERSON> \n"}, "typeVersion": 1}, {"id": "81deb53f-4161-42ef-9eec-d075e694aa04", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [1060, 220], "parameters": {"color": 5, "width": 220.39900249376552, "height": 80, "content": "## NextCloud\n"}, "typeVersion": 1}, {"id": "2aea4eaf-d7fa-4e87-ae75-e52bc3f385c2", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [660, 600], "parameters": {"color": 3, "width": 226.00997506234387, "height": 358.40399002493757, "content": "## Create Leads \nAdjust **Json** with your data\n\nChange **SUITECRMURLSERVER** with yours\nChange **IDVALUE** and **PWDVALUE** with a specific user in SuiteCRM"}, "typeVersion": 1}, {"id": "2550bf07-3d3b-497a-b14e-8626ab478659", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [1060, 320], "parameters": {"color": 3, "width": 223.46633416458826, "height": 80, "content": "## SuiteCRM \n"}, "typeVersion": 1}, {"id": "18324e1a-6873-466c-9eab-2292eb2fe1f4", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [920, 600], "parameters": {"color": 4, "height": 357.1321695760598, "content": "## Brevo\nCreate Contact with data and **Link with the id of SuiteCRM** Lead in a dedicated custom field in Brevo"}, "typeVersion": 1}, {"id": "df474fee-be22-4fda-9cfc-61e46492e30c", "name": "Create Lead SuiteCRM", "type": "n8n-nodes-base.httpRequest", "position": [720, 800], "parameters": {"url": "https://SUITECRMURLSERVER/Api/V8/module", "method": "POST", "options": {"response": {"response": {"responseFormat": "json"}}}, "jsonBody": "={\"data\": {\n\"type\": \"Leads\",\n\"attributes\": { \n\"last_name\": \"{{ $('Tally Forms Trigger').item.json[\"body\"][\"data\"][\"fields\"][1][\"value\"] }}\",\n\"status\": \"Hot\",\n\"email1\": \"{{ $('CaptainMail').item.json[\"email\"] }}\",\n\"lead_source\": \"FormsChoice\",\n\"assigned_user_id\": \"491cf554-4d5e-b06a-7a61-605210d85367\",\n\"lead_source_description\": \"FORMNAME Submission\"}\n}\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{$node[\"Token SuiteCRM\"].json[\"access_token\"]}}"}, {"name": "Content-Type", "value": "application/vnd.api+json"}]}}, "notesInFlow": true, "typeVersion": 3}, {"id": "635665d3-f35b-42b7-b9d5-427f46d1867f", "name": "Notif Talk Lead created", "type": "n8n-nodes-base.httpRequest", "position": [1260, 800], "parameters": {"url": "=https://URLSERVERNEXTCLOUD/ocs/v2.php/apps/spreed/api/v1/chat/DISCUSSIONCODE", "options": {"bodyContentType": "json", "bodyContentCustomMimeType": "application/json"}, "requestMethod": "POST", "authentication": "basicAuth", "jsonParameters": true, "bodyParametersJson": "={\n\"message\":\"Lead créé ! Saisie du Formulaire choix séance. Contact : {{ $('Tally Forms Trigger').item.json[\"body\"][\"data\"][\"fields\"][1][\"value\"] }} et mail : {{ $('CaptainMail').item.json[\"email\"] }} \"\n}", "headerParametersJson": "={\"OCS-APIRequest\":\"true\"}"}, "notesInFlow": true, "typeVersion": 1, "continueOnFail": true}, {"id": "84fda59b-5d9c-42aa-9ce6-2c3fc837e04e", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [1180, 600], "parameters": {"color": 5, "width": 266.18453865336653, "height": 357.50623441396476, "content": "## Notify lead created \nMessage for a lead created in your selected NextCloud discussion\n\nChange **URLSERVERNEXTCLOUD** with yours\nand **DISCUSSIONCODE** with the code of target discussion"}, "typeVersion": 1}, {"id": "2f55803e-bb3a-482a-9d12-5fdeef<PERSON>c6c", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [1060, 420], "parameters": {"color": 4, "width": 224.73815461346635, "height": 80, "content": "## Brevo"}, "typeVersion": 1}, {"id": "fbf39f60-e895-4477-9f62-9ec6965a84cc", "name": "Brevo Create Contact", "type": "n8n-nodes-base.sendInBlue", "position": [980, 800], "parameters": {"email": "{{ $('CaptainMail').item.json[\"email\"] }}", "resource": "contact", "createContactAttributes": {"attributesValues": [{"fieldName": "NOM", "fieldValue": "={{ $('Tally Forms Trigger').item.json.body.data.fields[1].value }}"}, {"fieldName": "PRENOM", "fieldValue": "={{ $('Tally Forms Trigger').item.json.body.data.fields[2].value }}"}, {"fieldName": "LEADS_ID", "fieldValue": "={{ $('Create Lead SuiteCRM').item.json.data.id }}"}]}}, "notesInFlow": true, "typeVersion": 1}], "pinData": {}, "connections": {"If mail ok": {"main": [[{"node": "Notif Talk bad email", "type": "main", "index": 0}], [{"node": "Token SuiteCRM", "type": "main", "index": 0}]]}, "CaptainMail": {"main": [[{"node": "If Credits OK", "type": "main", "index": 0}]]}, "If Credits OK": {"main": [[{"node": "Notif Talk credits", "type": "main", "index": 0}], [{"node": "If mail ok", "type": "main", "index": 0}]]}, "Token SuiteCRM": {"main": [[{"node": "Create Lead SuiteCRM", "type": "main", "index": 0}]]}, "Notif Talk credits": {"main": [[{"node": "If mail ok", "type": "main", "index": 0}]]}, "Tally Forms Trigger": {"main": [[{"node": "Captain<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Brevo Create Contact": {"main": [[{"node": "Notif Talk Lead created", "type": "main", "index": 0}]]}, "Create Lead SuiteCRM": {"main": [[{"node": "Brevo Create Contact", "type": "main", "index": 0}]]}}}