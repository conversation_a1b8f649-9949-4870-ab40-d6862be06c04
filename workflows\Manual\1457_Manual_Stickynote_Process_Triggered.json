{"id": "cKRViOHDPsosO7UX", "meta": {"instanceId": "ec7a5f4ffdb34436e59d23eaccb5015b5238de2a877e205b28572bf1ffecfe04"}, "name": "[<PERSON>/<PERSON><PERSON><PERSON><PERSON>] Output Parser 4", "tags": [], "nodes": [{"id": "3d669ba2-65b7-4502-92d9-645c4e51b26d", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [380, 240], "parameters": {}, "typeVersion": 1}, {"id": "9a509299-746d-4a3f-b379-8a4a9a92c75a", "name": "Prompt", "type": "n8n-nodes-base.set", "position": [600, 240], "parameters": {"values": {"string": [{"name": "input", "value": "Return the 5 largest states by area in the USA with their 3 largest cities and their population."}]}, "options": {}}, "typeVersion": 2}, {"id": "e2092fe6-d803-43e9-b2df-b0fc7aa83b02", "name": "LLM Chain", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1060, 240], "parameters": {}, "typeVersion": 1}, {"id": "711734d0-1003-4639-bdee-c160f6f976b3", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [1560, 900], "parameters": {"jsonSchema": "{\n \"type\": \"object\",\n \"properties\": {\n \"state\": {\n \"type\": \"string\"\n },\n \"cities\": {\n \"type\": \"array\",\n \"items\": {\n \"type\": \"object\",\n \"properties\": {\n \"name\": \"string\",\n \"population\": \"number\"\n }\n }\n }\n }\n}"}, "typeVersion": 1}, {"id": "f9b782f8-bb7b-4d65-be0d-d65c11de03d2", "name": "Auto-fixing Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserAutofixing", "position": [1260, 540], "parameters": {}, "typeVersion": 1}, {"id": "a26f034e-ea19-47ba-8fef-4f0a0d447c01", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1480, 795], "parameters": {"height": 264.69900963477494, "content": "### Parser which defines the output format and which gets used to validate the output"}, "typeVersion": 1}, {"id": "d902971a-e304-449c-a933-900c9c49ce55", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1080, 792], "parameters": {"height": 266.9506012398238, "content": "### The LLM which gets used to try to autofix the output in case it was not valid"}, "typeVersion": 1}, {"id": "b4c3b935-61b1-4243-b7df-ba4b7fd6e3ce", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [920, 440], "parameters": {"height": 245.56048099185898, "content": "### The LLM to process the original prompt"}, "typeVersion": 1}, {"id": "916d2998-cf0e-40f9-a373-149c609ed229", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1200, 449], "parameters": {"width": 348.*************, "height": 233.**************, "content": "### Autofixing parser which tries to fix invalid outputs with the help of an LLM"}, "typeVersion": 1}, {"id": "5cabf993-6bdd-4401-bb6d-fa20ff703127", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [980, 540], "parameters": {"options": {"temperature": 0}}, "credentials": {"openAiApi": {"id": "wJtZwsVKW5v6R2Iy", "name": "OpenAi account 2"}}, "typeVersion": 1}, {"id": "7f666edb-ecb7-4a6d-9dc7-ba67ef41d71f", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1140, 900], "parameters": {"options": {"temperature": 0}}, "credentials": {"openAiApi": {"id": "wJtZwsVKW5v6R2Iy", "name": "OpenAi account 2"}}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "976446d0-eb9d-478e-8178-69017329d736", "connections": {"Prompt": {"main": [[{"node": "LLM Chain", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Auto-fixing Output Parser", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Auto-fixing Output Parser", "type": "ai_outputParser", "index": 0}]]}, "Auto-fixing Output Parser": {"ai_outputParser": [[{"node": "LLM Chain", "type": "ai_outputParser", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "Prompt", "type": "main", "index": 0}]]}}}