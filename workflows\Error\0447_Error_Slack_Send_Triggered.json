{"nodes": [{"id": "eb305364-de39-4b9e-ad6e-eea54ebf712d", "name": "<PERSON><PERSON>ck", "type": "n8n-nodes-base.slack", "position": [740, 300], "parameters": {"text": "={{ $json.message }}", "select": "channel", "channelId": {"__rl": true, "mode": "name", "value": "#alerts-n8n-workflows"}, "otherOptions": {}}, "credentials": {"slackApi": {"id": "26", "name": "Cloudbot bot token"}}, "typeVersion": 2.1}, {"id": "9babcea6-ac7c-4a75-bd4c-f3d6a54c0ec7", "name": "On Error", "type": "n8n-nodes-base.errorTrigger", "position": [220, 300], "parameters": {}, "typeVersion": 1}, {"id": "134acca3-d4a7-485c-ab45-5a2721ed6a2c", "name": "Set message", "type": "n8n-nodes-base.set", "position": [480, 300], "parameters": {"values": {"string": [{"name": "message", "value": "=:warning: [prod] workflow `{{$json[\"workflow\"][\"name\"]}}` failed to run! <{{ $json.execution.url }}|execution>\n\nerror message from node: {{ $json.execution.lastNodeExecuted }}\n {{ $json.execution.error.message }}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"id": "b6dfce1e-95c0-43c4-8a81-098b33130232", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [140, 100], "parameters": {"color": 5, "width": 424.4907862645661, "height": 154.7766688696994, "content": "### 👨‍🎤 Setup\n1. Add Slack creds\n2. Add this error workflow to other workflows\nhttps://docs.n8n.io/flow-logic/error-handling/#create-and-set-an-error-workflow"}, "typeVersion": 1}, {"id": "619e2628-6860-47ca-9e6a-9294ea123f8f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [480, 480], "parameters": {"width": 241, "height": 80, "content": "### 👆🏽 Adjust error message here"}, "typeVersion": 1}], "pinData": {}, "connections": {"On Error": {"main": [[{"node": "Set message", "type": "main", "index": 0}]]}, "Set message": {"main": [[{"node": "<PERSON><PERSON>ck", "type": "main", "index": 0}]]}}}