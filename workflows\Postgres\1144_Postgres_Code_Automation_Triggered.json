{"id": "7gRbzEzCuOzQKn4M", "meta": {"instanceId": "edc0464b1050024ebda3e16fceea795e4fdf67b1f61187c4f2f3a72397278df0", "templateCredsSetupCompleted": true}, "name": "SHEETS RAG", "tags": [], "nodes": [{"id": "a073154f-53ad-45e2-9937-d0a4196c7838", "name": "create table query", "type": "n8n-nodes-base.code", "position": [1280, 2360], "parameters": {"jsCode": "// Helper function to check if a string is in MM/DD/YYYY format\nfunction isDateString(value) {\n  const dateRegex = /^\\d{2}\\/\\d{2}\\/\\d{4}$/;\n  if (typeof value !== 'string') return false;\n  if (!dateRegex.test(value)) return false;\n  const [month, day, year] = value.split('/').map(Number);\n  const date = new Date(year, month - 1, day);\n  return !isNaN(date.getTime());\n}\n\nconst tableName = `ai_table_${$('change_this').first().json.sheet_name}`;\nconst rows = $('fetch sheet data').all();\nconst allColumns = new Set();\n\n// Collect column names dynamically\nrows.forEach(row => {\n  Object.keys(row.json).forEach(col => allColumns.add(col));\n});\n\n// Ensure \"ai_table_identifier\" is always the first column\nconst originalColumns = [\"ai_table_identifier\", ...Array.from(allColumns)];\n\n// Function to detect currency type (unchanged)\nfunction detectCurrency(values) {\n  const currencySymbols = {\n    '₹': 'INR', '$': 'USD', '€': 'EUR', '£': 'GBP', '¥': 'JPY',\n    '₩': 'KRW', '฿': 'THB', 'zł': 'PLN', 'kr': 'SEK', 'R$': 'BRL',\n    'C$': 'CAD', 'A$': 'AUD'\n  };\n\n  let detectedCurrency = null;\n  for (const value of values) {\n    if (typeof value === 'string' && value.trim() !== '') {\n      for (const [symbol, code] of Object.entries(currencySymbols)) {\n        if (value.trim().startsWith(symbol)) {\n          detectedCurrency = code;\n          break;\n        }\n      }\n    }\n  }\n  return detectedCurrency;\n}\n\n// Function to generate consistent column names\nfunction generateColumnName(originalName, typeInfo) {\n  if (typeInfo.isCurrency) {\n    return `${originalName}_${typeInfo.currencyCode.toLowerCase()}`;\n  }\n  return originalName;\n}\n\n// Infer column types and transform names\nconst columnMapping = {};\noriginalColumns.forEach(col => {\n  let typeInfo = { type: \"TEXT\" };\n\n  if (col !== \"ai_table_identifier\") {\n    const sampleValues = rows\n      .map(row => row.json[col])\n      .filter(value => value !== undefined && value !== null);\n\n    // Check for currency first\n    const currencyCode = detectCurrency(sampleValues);\n    if (currencyCode) {\n      typeInfo = { type: \"DECIMAL(15,2)\", isCurrency: true, currencyCode };\n    }\n    // If all sample values match MM/DD/YYYY, treat the column as a date\n    else if (sampleValues.length > 0 && sampleValues.every(val => isDateString(val))) {\n      typeInfo = { type: \"TIMESTAMP\" };\n    }\n  }\n\n  const newColumnName = generateColumnName(col, typeInfo);\n  columnMapping[col] = { newName: newColumnName, typeInfo };\n});\n\n// Final column names\nconst mappedColumns = originalColumns.map(col => columnMapping[col]?.newName || col);\n\n// Define SQL columns – note that for simplicity, this example still uses TEXT for non-special types,\n// but you can adjust it so that TIMESTAMP columns are created with a TIMESTAMP type.\nconst columnDefinitions = [`\"ai_table_identifier\" UUID PRIMARY KEY DEFAULT gen_random_uuid()`]\n  .concat(mappedColumns.slice(1).map(col => {\n    // If the column was inferred as TIMESTAMP, use that type in the CREATE TABLE statement.\n    const originalCol = Object.keys(columnMapping).find(key => columnMapping[key].newName === col);\n    const inferredType = columnMapping[originalCol]?.typeInfo?.type;\n    return `\"${col}\" ${inferredType === \"TIMESTAMP\" ? \"TIMESTAMP\" : \"TEXT\"}`;\n  }))\n  .join(\", \");\n\nconst createTableQuery = `CREATE TABLE IF NOT EXISTS ${tableName} (${columnDefinitions});`;\n\nreturn [{ \n  query: createTableQuery,\n  columnMapping: columnMapping \n}];\n"}, "typeVersion": 2}, {"id": "2beb72c4-dab4-4058-b587-545a8ce8b86d", "name": "create insertion query", "type": "n8n-nodes-base.code", "position": [1660, 2360], "parameters": {"jsCode": "const tableName = `ai_table_${$('change_this').first().json.sheet_name}`;\nconst rows = $('fetch sheet data').all();\nconst allColumns = new Set();\n\n// Get column mapping from previous node\nconst columnMapping = $('create table query').first().json.columnMapping || {};\n\n// Collect column names dynamically\nrows.forEach(row => {\n  Object.keys(row.json).forEach(col => allColumns.add(col));\n});\n\nconst originalColumns = Array.from(allColumns);\nconst mappedColumns = originalColumns.map(col => \n  columnMapping[col] ? columnMapping[col].newName : col\n);\n\n// Helper function to check if a string is a valid timestamp\nfunction isValidTimestamp(value) {\n  const date = new Date(value);\n  return !isNaN(date.getTime());\n}\n\n// Helper to detect currency symbol (unchanged)\nfunction getCurrencySymbol(value) {\n  if (typeof value !== 'string') return null;\n  \n  const currencySymbols = ['₹', '$', '€', '£', '¥', '₩', '฿', 'zł', 'kr', 'R$', 'C$', 'A$'];\n  for (const symbol of currencySymbols) {\n    if (value.trim().startsWith(symbol)) {\n      return symbol;\n    }\n  }\n  return null;\n}\n\n// Helper to normalize currency values (unchanged)\nfunction normalizeCurrencyValue(value, currencySymbol) {\n  if (typeof value !== 'string') return null;\n  if (!currencySymbol) return value;\n  \n  const numericPart = value.replace(currencySymbol, '').replace(/,/g, '');\n  return !isNaN(parseFloat(numericPart)) ? parseFloat(numericPart) : null;\n}\n\n// Helper to normalize percentage values (unchanged)\nfunction normalizePercentageValue(value) {\n  if (typeof value !== 'string') return value;\n  if (!value.trim().endsWith('%')) return value;\n  \n  const numericPart = value.replace('%', '');\n  return !isNaN(parseFloat(numericPart)) ? parseFloat(numericPart) / 100 : null;\n}\n\n// Function to parse MM/DD/YYYY strings into ISO format\nfunction parseDateString(value) {\n  const dateRegex = /^\\d{2}\\/\\d{2}\\/\\d{4}$/;\n  if (typeof value === 'string' && dateRegex.test(value)) {\n    const [month, day, year] = value.split('/').map(Number);\n    const date = new Date(year, month - 1, day);\n    return !isNaN(date.getTime()) ? date.toISOString() : null;\n  }\n  return value;\n}\n\n// Format rows properly based on column mappings and types\nconst formattedRows = rows.map(row => {\n  const formattedRow = {};\n\n  originalColumns.forEach((col, index) => {\n    const mappedCol = mappedColumns[index];\n    let value = row.json[col];\n    const typeInfo = columnMapping[col]?.typeInfo || { type: \"TEXT\" };\n\n    if (value === \"\" || value === null || value === undefined) {\n      value = null;\n    } \n    else if (typeInfo.isCurrency) {\n      const symbol = getCurrencySymbol(value);\n      if (symbol) {\n        value = normalizeCurrencyValue(value, symbol);\n      } else {\n        value = null;\n      }\n    }\n    else if (typeInfo.isPercentage) {\n      if (typeof value === 'string' && value.trim().endsWith('%')) {\n        value = normalizePercentageValue(value);\n      } else {\n        value = !isNaN(parseFloat(value)) ? parseFloat(value) / 100 : null;\n      }\n    }\n    else if (typeInfo.type === \"DECIMAL(15,2)\" || typeInfo.type === \"INTEGER\") {\n      if (typeof value === 'string') {\n        const cleanedValue = value.replace(/,/g, '');\n        value = !isNaN(parseFloat(cleanedValue)) ? parseFloat(cleanedValue) : null;\n      } else if (typeof value === 'number') {\n        value = parseFloat(value);\n      } else {\n        value = null;\n      }\n    } \n    else if (typeInfo.type === \"BOOLEAN\") {\n      if (typeof value === 'string') {\n        const lowercased = value.toString().toLowerCase();\n        value = lowercased === \"true\" ? true : \n                lowercased === \"false\" ? false : null;\n      } else {\n        value = Boolean(value);\n      }\n    } \n    else if (typeInfo.type === \"TIMESTAMP\") {\n      // Check if the value is in MM/DD/YYYY format and parse it accordingly.\n      if (/^\\d{2}\\/\\d{2}\\/\\d{4}$/.test(value)) {\n        value = parseDateString(value);\n      } else if (isValidTimestamp(value)) {\n        value = new Date(value).toISOString();\n      } else {\n        value = null;\n      }\n    }\n    else if (typeInfo.type === \"TEXT\") {\n      value = value !== null && value !== undefined ? String(value) : null;\n    }\n\n    formattedRow[mappedCol] = value;\n  });\n\n  return formattedRow;\n});\n\n// Generate SQL placeholders dynamically\nconst valuePlaceholders = formattedRows.map((_, rowIndex) =>\n  `(${mappedColumns.map((_, colIndex) => `$${rowIndex * mappedColumns.length + colIndex + 1}`).join(\", \")})`\n).join(\", \");\n\n// Build the insert query string\nconst insertQuery = `INSERT INTO ${tableName} (${mappedColumns.map(col => `\"${col}\"`).join(\", \")}) VALUES ${valuePlaceholders};`;\n\n// Flatten parameter values for PostgreSQL query\nconst parameters = formattedRows.flatMap(row => mappedColumns.map(col => row[col]));\n\nreturn [\n  {\n    query: insertQuery,\n    parameters: parameters\n  }\n];\n"}, "typeVersion": 2}, {"id": "ba19c350-ffb7-4fe1-9568-2a619c914434", "name": "Google Drive Trigger", "type": "n8n-nodes-base.googleDriveTrigger", "position": [600, 2060], "parameters": {"pollTimes": {"item": [{}]}, "triggerOn": "specificFile", "fileToWatch": {"__rl": true, "mode": "list", "value": "1yGx4ODHYYtPV1WZFAtPcyxGT2brcXM6pl0KJhIM1f_c", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1yGx4ODHYYtPV1WZFAtPcyxGT2brcXM6pl0KJhIM1f_c/edit?usp=drivesdk", "cachedResultName": "Spreadsheet"}}, "credentials": {"googleDriveOAuth2Api": {"id": "zOt0lyWOZz1UlS67", "name": "Google Drive account"}}, "typeVersion": 1}, {"id": "dd2108fe-0cfe-453c-ac03-c0c5b10397e6", "name": "execute_query_tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [1340, 1720], "parameters": {"name": "query_executer", "schemaType": "manual", "workflowId": {"__rl": true, "mode": "list", "value": "oPWJZynrMME45ks4", "cachedResultName": "query_executer"}, "description": "Call this tool to execute a query. Remember that it should be in a postgreSQL query structure.", "inputSchema": "{\n\"type\": \"object\",\n\"properties\": {\n\t\"sql\": {\n\t\t\"type\": \"string\",\n\t\t\"description\": \"A SQL query based on the users question and database schema.\"\n\t\t}\n\t}\n}", "specifyInputSchema": true}, "typeVersion": 1.2}, {"id": "f2c110db-1097-4b96-830d-f028e08b6713", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [880, 1680], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash"}, "credentials": {"googlePalmApi": {"id": "Kr5lNqvdmtB0Ybyo", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "2460801c-5b64-41b3-93f7-4f2fbffabfd6", "name": "get_postgres_schema", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [1160, 1720], "parameters": {"name": "get_postgres_schema", "workflowId": {"__rl": true, "mode": "list", "value": "iNLPk34SeRGHaeMD", "cachedResultName": "get database schema"}, "description": "Call this tool to retrieve the schema of all the tables inside of the database. A string will be retrieved with the name of the table and its columns, each table is separated by \\n\\n.", "workflowInputs": {"value": {}, "schema": [], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2}, {"id": "4b43ff94-df0d-40f1-9f51-cf488e33ff68", "name": "change_this", "type": "n8n-nodes-base.set", "position": [800, 2060], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "908ed843-f848-4290-9cdb-f195d2189d7c", "name": "table_url", "type": "string", "value": "https://docs.google.com/spreadsheets/d/1yGx4ODHYYtPV1WZFAtPcyxGT2brcXM6pl0KJhIM1f_c/edit?gid=0#gid=0"}, {"id": "50f8afaf-0a6c-43ee-9157-79408fe3617a", "name": "sheet_name", "type": "string", "value": "product_list"}]}}, "typeVersion": 3.4}, {"id": "a27a47ff-9328-4eef-99e8-280452cff189", "name": "is not in database", "type": "n8n-nodes-base.if", "position": [1380, 2060], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "619ce84c-0a50-4f88-8e55-0ce529aea1fc", "operator": {"type": "boolean", "operation": "false", "singleValue": true}, "leftValue": "={{ $('table exists?').item.json.exists }}", "rightValue": "true"}]}}, "typeVersion": 2.2}, {"id": "8ad9bc36-08b1-408e-ba20-5618a801b4ed", "name": "table exists?", "type": "n8n-nodes-base.postgres", "position": [1000, 2060], "parameters": {"query": "SELECT EXISTS (\n    SELECT 1 \n    FROM information_schema.tables \n    WHERE table_name = 'ai_table_{{ $json.sheet_name }}'\n);\n", "options": {}, "operation": "execute<PERSON>uery"}, "credentials": {"postgres": {"id": "KQiQIZTArTBSNJH7", "name": "Postgres account"}}, "typeVersion": 2.5}, {"id": "f66b7ca7-ecb7-47fc-9214-2d2b37b0fbe4", "name": "fetch sheet data", "type": "n8n-nodes-base.googleSheets", "position": [1180, 2060], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "name", "value": "={{ $('change_this').item.json.sheet_name }}"}, "documentId": {"__rl": true, "mode": "url", "value": "={{ $('change_this').item.json.table_url }}"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "3au0rUsZErkG0zc2", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "11ba5da0-e7c4-49ee-8d35-24c8d3b9fea9", "name": "remove table", "type": "n8n-nodes-base.postgres", "position": [980, 2360], "parameters": {"query": "DROP TABLE IF EXISTS ai_table_{{ $('change_this').item.json.sheet_name }} CASCADE;", "options": {}, "operation": "execute<PERSON>uery"}, "credentials": {"postgres": {"id": "KQiQIZTArTBSNJH7", "name": "Postgres account"}}, "typeVersion": 2.5}, {"id": "3936ecb3-f084-4f86-bd5f-abab0957ebc0", "name": "create table", "type": "n8n-nodes-base.postgres", "position": [1460, 2360], "parameters": {"query": "{{ $json.query }}", "options": {}, "operation": "execute<PERSON>uery"}, "credentials": {"postgres": {"id": "KQiQIZTArTBSNJH7", "name": "Postgres account"}}, "typeVersion": 2.5}, {"id": "8a3ea239-f3fa-4c72-af99-31f4bd992b58", "name": "perform insertion", "type": "n8n-nodes-base.postgres", "position": [1860, 2360], "parameters": {"query": "{{$json.query}}", "options": {"queryReplacement": "={{$json.parameters}}"}, "operation": "execute<PERSON>uery"}, "credentials": {"postgres": {"id": "KQiQIZTArTBSNJH7", "name": "Postgres account"}}, "typeVersion": 2.5}, {"id": "********-b573-4753-a7ca-5a9c3aa8aa3e", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [1720, 1720], "parameters": {}, "typeVersion": 1}, {"id": "c94256a9-e44e-4800-82f8-90f85ba90bde", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1920, 1460], "parameters": {"color": 7, "width": 500, "height": 260, "content": "Place this in a separate workflow named:\n### query_executer"}, "typeVersion": 1}, {"id": "daec928e-58ee-43da-bd91-ba8bcd639a4a", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1920, 1840], "parameters": {"color": 7, "width": 500, "height": 280, "content": "place this in a separate workflow named: \n### get database schema"}, "typeVersion": 1}, {"id": "8908e342-fcbe-4820-b623-cb95a55ea5db", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.manualChatTrigger", "position": [640, 1540], "parameters": {}, "typeVersion": 1.1}, {"id": "d0ae90c2-169e-44d7-b3c2-4aff8e7d4be9", "name": "response output", "type": "n8n-nodes-base.set", "position": [2220, 1540], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "e2f94fb1-3deb-466a-a36c-e3476511d5f2", "name": "response", "type": "string", "value": "={{ $json }}"}]}}, "typeVersion": 3.4}, {"id": "81c58d9b-ded4-4b74-8227-849e665cbdff", "name": "sql query executor", "type": "n8n-nodes-base.postgres", "position": [2000, 1540], "parameters": {"query": "{{ $json.query.sql }}", "options": {}, "operation": "execute<PERSON>uery"}, "credentials": {"postgres": {"id": "KQiQIZTArTBSNJH7", "name": "Postgres account"}}, "typeVersion": 2.5}, {"id": "377d1727-4577-41bb-8656-38273fc4412b", "name": "schema finder", "type": "n8n-nodes-base.postgres", "position": [2000, 1920], "parameters": {"query": "SELECT \n    t.schemaname,\n    t.tablename,\n    c.column_name,\n    c.data_type\nFROM \n    pg_catalog.pg_tables t\nJOIN \n    information_schema.columns c\n    ON t.schemaname = c.table_schema\n    AND t.tablename = c.table_name\nWHERE \n    t.schemaname = 'public'\nORDER BY \n    t.tablename, c.ordinal_position;", "options": {}, "operation": "execute<PERSON>uery"}, "credentials": {"postgres": {"id": "KQiQIZTArTBSNJH7", "name": "Postgres account"}}, "typeVersion": 2.5}, {"id": "89d3c59c-2b67-454d-a8f3-e90e75a28a8c", "name": "schema to string", "type": "n8n-nodes-base.code", "position": [2220, 1920], "parameters": {"jsCode": "function transformSchema(input) {\n    const tables = {};\n    \n    input.forEach(({ json }) => {\n        if (!json) return;\n        \n        const { tablename, schemaname, column_name, data_type } = json;\n        \n        if (!tables[tablename]) {\n            tables[tablename] = { schema: schemaname, columns: [] };\n        }\n        tables[tablename].columns.push(`${column_name} (${data_type})`);\n    });\n    \n    return Object.entries(tables)\n        .map(([tablename, { schema, columns }]) => `Table ${tablename} (Schema: ${schema}) has columns: ${columns.join(\", \")}`)\n        .join(\"\\n\\n\");\n}\n\n// Example usage\nconst input = $input.all();\n\nconst transformedSchema = transformSchema(input);\n\nreturn { data: transformedSchema };"}, "typeVersion": 2}, {"id": "42d1b316-60ca-49db-959b-581b162ca1f9", "name": "AI Agent With SQL Query Prompt", "type": "@n8n/n8n-nodes-langchain.agent", "position": [900, 1540], "parameters": {"options": {"maxIterations": 5, "systemMessage": "=## Role\nYou are a **Database Query Assistant** specializing in generating PostgreSQL queries based on natural language questions. You analyze database schemas, construct appropriate SQL queries, and provide clear explanations of results.\n\n## Tools\n1. `get_postgres_schema`: Retrieves the complete database schema (tables and columns)\n2. `execute_query_tool`: Executes SQL queries with the following input format:\n   ```json\n   {\n     \"sql\": \"Your SQL query here\"\n   }\n   ```\n\n## Process Flow\n\n### 1. Analyze the Question\n- Identify the **data entities** being requested (products, customers, orders, etc.)\n- Determine the **query type** (COUNT, AVG, SUM, SELECT, etc.)\n- Extract any **filters** or **conditions** mentioned\n\n### 2. Fetch and Analyze Schema\n- Call `get_postgres_schema` to retrieve database structure\n- Identify relevant tables and columns that match the entities in the question\n- Prioritize exact matches, then semantic matches\n\n### 3. Query Construction\n- Build case-insensitive queries using `LOWER(column) LIKE LOWER('%value%')`\n- Filter out NULL or empty values with appropriate WHERE clauses\n- Use joins when information spans multiple tables\n- Apply aggregations (COUNT, SUM, AVG) as needed\n\n### 4. Query Execution\n- Execute query using the `execute_query_tool` with proper formatting\n- If results require further processing, perform calculations as needed\n\n### 5. Result Presentation\n- Format results in a conversational, easy-to-understand manner\n- Explain how the data was retrieved and any calculations performed\n- When appropriate, suggest further questions the user might want to ask\n\n## Best Practices\n- Use parameterized queries to prevent SQL injection\n- Implement proper error handling\n- Respond with \"NOT_ENOUGH_INFO\" when the question lacks specificity\n- Always verify table/column existence before attempting queries\n- Use explicit JOINs instead of implicit joins\n- Limit large result sets when appropriate\n\n## Numeric Validation (IMPORTANT)\nWhen validating or filtering numeric values in string columns:\n1. **AVOID** complex regular expressions with `~` operator as they cause syntax errors\n2. Use these safer alternatives instead:\n   ```sql\n   -- Simple numeric check without regex\n   WHERE column_name IS NOT NULL AND trim(column_name) != '' AND column_name NOT LIKE '%[^0-9.]%'\n   \n   -- For type casting with validation\n   WHERE column_name IS NOT NULL AND trim(column_name) != '' AND column_name ~ '[0-9]'\n   \n   -- Safe numeric conversion\n   WHERE CASE WHEN column_name ~ '[0-9]' THEN TRUE ELSE FALSE END\n   ```\n3. For simple pattern matching, use LIKE instead of regex when possible\n4. When CAST is needed, always guard against invalid values:\n   ```sql\n   SELECT SUM(CASE WHEN column_name ~ '[0-9]' THEN CAST(column_name AS NUMERIC) ELSE 0 END) AS total\n   ```\n\n## Response Structure\n1. **Analysis**: Brief mention of how you understood the question\n2. **Query**: The SQL statement used (in code block format)\n3. **Results**: Clear presentation of the data found\n4. **Explanation**: Simple description of how the data was retrieved\n\n## Examples\n\n### Example 1: Basic Counting Query\n**Question**: \"How many products are in the inventory?\"\n\n**Process**:\n1. Analyze schema to find product/inventory tables\n2. Construct a COUNT query on the relevant table\n3. Execute the query\n4. Present the count with context\n\n**SQL**:\n```sql\nSELECT COUNT(*) AS product_count \nFROM products \nWHERE quantity IS NOT NULL;\n```\n\n**Response**:\n\"There are 1,250 products currently in the inventory. This count includes all items with a non-null quantity value in the products table.\"\n\n### Example 2: Filtered Aggregation Query\n**Question**: \"What is the average order value for premium customers?\"\n\n**Process**:\n1. Identify relevant tables (orders, customers)\n2. Determine join conditions\n3. Apply filters for \"premium\" customers\n4. Calculate average\n\n**SQL**:\n```sql\nSELECT AVG(o.total_amount) AS avg_order_value\nFROM orders o\nJOIN customers c ON o.customer_id = c.id\nWHERE LOWER(c.customer_type) = LOWER('premium')\nAND o.total_amount IS NOT NULL;\n```\n\n**Response**:\n\"Premium customers spend an average of $85.42 per order. This was calculated by averaging the total_amount from all orders placed by customers with a 'premium' customer type.\"\n\n### Example 3: Numeric Calculation from String Column\n**Question**: \"What is the total of all ratings?\"\n\n**Process**:\n1. Find the ratings table and column\n2. Use safe numeric validation\n3. Sum the values\n\n**SQL**:\n```sql\nSELECT SUM(CASE WHEN rating ~ '[0-9]' THEN CAST(rating AS NUMERIC) ELSE 0 END) AS total_rating\nFROM ratings\nWHERE rating IS NOT NULL AND trim(rating) != '';\n```\n\n**Response**:\n\"The sum of all ratings is 4,285. This calculation includes all valid numeric ratings from the ratings table.\"\n\n### Example 4: Date Range Aggregation for Revenue  \n**Question**: \"How much did I make last week?\"  \n\n**Process**:  \n1. Identify the sales table and relevant columns (e.g., `sale_date` for dates and `revenue_amount` for revenue).  \n2. Use PostgreSQL date functions (`date_trunc` and interval arithmetic) to calculate the date range for the previous week.  \n3. Sum the revenue within the computed date range.  \n\n**SQL**:  \n```sql\nSELECT SUM(revenue_amount) AS total_revenue\nFROM sales_data\nWHERE sale_date >= date_trunc('week', CURRENT_DATE) - INTERVAL '1 week'\n  AND sale_date < date_trunc('week', CURRENT_DATE);\n```  \n\n**Response**:  \n\"Last week's total revenue is calculated by summing the `revenue_amount` for records where the `sale_date` falls within the previous week. This query uses date functions to dynamically determine the correct date range.\"\n\nToday's date: {{ $now }}"}}, "typeVersion": 1.7}, {"id": "368d68d0-1fe0-4dbf-9b24-ac28fd6e74c3", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [560, 1420], "parameters": {"color": 6, "width": 960, "height": 460, "content": "## Use a powerful LLM to correctly build the SQL queries, which will be identified from the get schema tool and then executed by the execute query tool."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "d8045db4-2852-4bbe-9b97-0d3c0acb53f7", "connections": {"change_this": {"main": [[{"node": "table exists?", "type": "main", "index": 0}]]}, "create table": {"main": [[{"node": "create insertion query", "type": "main", "index": 0}]]}, "remove table": {"main": [[{"node": "create table query", "type": "main", "index": 0}]]}, "schema finder": {"main": [[{"node": "schema to string", "type": "main", "index": 0}]]}, "table exists?": {"main": [[{"node": "fetch sheet data", "type": "main", "index": 0}]]}, "fetch sheet data": {"main": [[{"node": "is not in database", "type": "main", "index": 0}]]}, "create table query": {"main": [[{"node": "create table", "type": "main", "index": 0}]]}, "execute_query_tool": {"ai_tool": [[{"node": "AI Agent With SQL Query Prompt", "type": "ai_tool", "index": 0}]]}, "is not in database": {"main": [[{"node": "create table query", "type": "main", "index": 0}], [{"node": "remove table", "type": "main", "index": 0}]]}, "sql query executor": {"main": [[{"node": "response output", "type": "main", "index": 0}]]}, "get_postgres_schema": {"ai_tool": [[{"node": "AI Agent With SQL Query Prompt", "type": "ai_tool", "index": 0}]]}, "Google Drive Trigger": {"main": [[{"node": "change_this", "type": "main", "index": 0}]]}, "create insertion query": {"main": [[{"node": "perform insertion", "type": "main", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "sql query executor", "type": "main", "index": 0}, {"node": "schema finder", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI Agent With SQL Query Prompt", "type": "ai_languageModel", "index": 0}]]}, "When chat message received": {"main": [[{"node": "AI Agent With SQL Query Prompt", "type": "main", "index": 0}]]}}}