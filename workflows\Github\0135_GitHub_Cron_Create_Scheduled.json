{"nodes": [{"name": "Get latest release", "type": "n8n-nodes-base.github", "position": [540, 340], "parameters": {"limit": 1, "resource": "release", "operation": "getAll"}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [240, 500], "parameters": {"triggerTimes": {"item": [{"mode": "everyWeek"}]}}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [740, 420], "parameters": {}, "typeVersion": 1}, {"name": "No issue for release?", "type": "n8n-nodes-base.function", "position": [920, 420], "parameters": {"functionCode": "const _ = require('lodash')\n\n// differentiate merged inputs (didnt find a way to get both inputs into one function invocation)\nconst releases = _.filter(items, i => _.has(i, 'json.assets'))\nif (releases.length != 1) throw new Error(`Invalid release count: ${releases.length}`)\nconst release = releases[0]\nconst issues = _.without(items, release)\n//console.log({release,issues})\n\n// check if there's an issue for the release\nconst matchingIssue = _.find(issues, i => i.json.title.includes(release.json.tag_name))\n//console.log({release,issues,matchingIssue})\n\nif (matchingIssue)\n  return []\nelse\n  return [release]"}, "executeOnce": false, "typeVersion": 1}, {"name": "Create issue", "type": "n8n-nodes-base.gitlab", "position": [1100, 420], "parameters": {"body": "={{$json[\"url\"]}}\n\n{{$json[\"body\"]}}", "owner": "txlab", "title": "=Upstream release: {{$json[\"tag_name\"]}}", "labels": [], "repository": "docker-linkcheck", "assignee_ids": []}, "typeVersion": 1}, {"name": "List issues", "type": "n8n-nodes-base.gitlab", "position": [540, 500], "parameters": {"owner": "txlab", "resource": "repository", "repository": "docker-linkcheck", "getRepositoryIssuesFilters": {}}, "typeVersion": 1}], "connections": {"Cron": {"main": [[{"node": "Get latest release", "type": "main", "index": 0}, {"node": "List issues", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "No release for issue?", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Get latest release", "type": "main", "index": 0}, {"node": "List issues", "type": "main", "index": 0}]]}, "List issues": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Get latest release": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "No release for issue?": {"main": [[{"node": "Create issue", "type": "main", "index": 0}]]}}}