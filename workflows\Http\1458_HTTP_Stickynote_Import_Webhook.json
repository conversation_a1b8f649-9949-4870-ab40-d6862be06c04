{"id": "FpZJ8jaNQ3j2DO1L", "meta": {"instanceId": "cb484ba7b742928a2048bf8829668bed5b5ad9787579adea888f05980292a4a7"}, "name": "Optimise images uploaded to GDrive", "nodes": [{"id": "a6fac2bb-4079-4872-9cc9-17b1016d2fcc", "name": "Check GDrive for new images", "type": "n8n-nodes-base.googleDriveTrigger", "position": [500, 160], "parameters": {"event": "fileCreated", "options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "mode": "list", "value": "", "cachedResultUrl": "", "cachedResultName": ""}}, "credentials": {"googleDriveOAuth2Api": {"id": "", "name": ""}}, "typeVersion": 1}, {"id": "a0cae553-e4c1-408b-b11a-ceda4ff1aaa4", "name": "Download image", "type": "n8n-nodes-base.googleDrive", "position": [700, 160], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "options": {}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "", "name": ""}}, "typeVersion": 3}, {"id": "006ba31a-f42b-460c-87e1-66c5345fb6d7", "name": "Optimise - Send image to TinyPNG", "type": "n8n-nodes-base.httpRequest", "position": [940, 320], "parameters": {"url": "https://api.tinify.com/shrink", "method": "POST", "options": {"response": {"response": {"fullResponse": true}}}, "sendBody": true, "contentType": "binaryData", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Basic "}]}, "inputDataFieldName": "data"}, "typeVersion": 4.1}, {"id": "e380304e-1c94-4841-bc1c-73047e4c2501", "name": "Get optimised image from tinyPNG", "type": "n8n-nodes-base.httpRequest", "position": [1140, 320], "parameters": {"url": "={{ $json.headers.location }}", "options": {}}, "typeVersion": 4.1}, {"id": "f4db56cf-e362-41da-b2c2-da59b71a103f", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-60, -60], "parameters": {"color": 4, "width": 459.2991776576996, "height": 146.4269155371431, "content": "## Automatically optimise images uploaded to Google drive folder\nEach time an image is added to a google drive folder, this workflow will send it to tinypng.com to optimise the size and resave it to a google drive location of your choice.\n\n"}, "typeVersion": 1}, {"id": "b9e2dd81-245d-4328-adbc-a1f17100d590", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-60, 120], "parameters": {"color": 6, "width": 463.**************, "height": 176.*************, "content": "### 1. Pre-setup: Google Drive credentials\n\n**a.** Firstly you'll need to setup Google Drive credentials. Best thing is to [read n8n docs](https://docs.n8n.io/integrations/builtin/credentials/google/oauth-single-service/) to to do that.\n**b.** Once you're successfully connecting to your GDrive account, set all 3 of the Drive nodes to connect using that credential."}, "typeVersion": 1}, {"id": "285b5324-07d5-4f17-b6cc-9013e60644ad", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [480, -60], "parameters": {"color": 6, "width": 411.**************, "height": 189.*************, "content": "### 2. Choose the Google Drive folder n8n is going to watch for new files\n\n**a.** Go to Google Drive and create the folder you want n8n to watch for new images\n**b.** Then you need to select that folder in the Google Drive trigger node"}, "typeVersion": 1}, {"id": "8b574c32-baec-48ec-9cab-41d9f9813c6f", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [940, 100], "parameters": {"color": 6, "width": 322.************, "height": 189.*************, "content": "### 3. Create an API key for tinypng.com\n\n**a.** Visit [tinypng.com](https://tinypng.com/developers) and request an API key\n**b.** Update the \"Authorisation\" parameter value with your api key. It will be in the format of \"Basic YOUR_API_KEY_IN_BASE_64\""}, "typeVersion": 1}, {"id": "d3740bb8-f296-4b81-816e-ebc6e42927ad", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1380, 240], "parameters": {"color": 6, "width": 322.************, "height": 239.85571564814694, "content": "### 4. Choose your Google Drive folder to save your upload your optimised images to\n\n**a.** Finally, create and select the folder that you want your optimised images to be saved to\n**b.** OPTIONAL: You can also change the formatting of the name that you set. By default it will use the original file name then -optimised"}, "typeVersion": 1}, {"id": "b69a925f-9938-4672-9329-4f8895ea9c79", "name": "Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [1480, 520], "parameters": {"name": "name.png", "driveId": {"__rl": true, "mode": "list", "value": ""}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "", "cachedResultUrl": "", "cachedResultName": ""}}, "credentials": {"googleDriveOAuth2Api": {"id": "", "name": ""}}, "typeVersion": 3}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "7cdfcaa5-cbce-4582-9563-c72ba8d425b9", "connections": {"Download image": {"main": [[{"node": "Optimise - Send image to TinyPNG", "type": "main", "index": 0}]]}, "Check GDrive for new images": {"main": [[{"node": "Download image", "type": "main", "index": 0}]]}, "Get optimised image from tinyPNG": {"main": [[{"node": "Google Drive", "type": "main", "index": 0}]]}, "Optimise - Send image to TinyPNG": {"main": [[{"node": "Get optimised image from tinyPNG", "type": "main", "index": 0}]]}}}