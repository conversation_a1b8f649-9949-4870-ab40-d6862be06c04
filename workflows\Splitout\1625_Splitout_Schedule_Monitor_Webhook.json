{"id": "R6tFG45dQydBz63e", "meta": {"instanceId": "fb2ac1a770dc8dc4bb24d7e6a9ab7e89f53c6b6759adeb7ab76c09a3d8f6f4a9", "templateCredsSetupCompleted": true}, "name": "n8n Community Topic Tracker by Keyword", "tags": [], "nodes": [{"id": "b735226c-ce7f-4daf-8255-45ba80262aa5", "name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [760, 0], "parameters": {"columns": {"value": {"id": "={{ $json.id }}", "url": "=https://community.n8n.io/t/{{ $json.slug }}", "date": "={{ $json.created_at }}", "title": "={{ $json.title }}", "has_solution": "={{ $json.has_accepted_answer }}"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "id", "defaultMatch": true, "canBeUsedToMatch": true}, {"id": "date", "type": "string", "display": true, "required": false, "displayName": "date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "title", "type": "string", "display": true, "required": false, "displayName": "title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "url", "type": "string", "display": true, "removed": false, "required": false, "displayName": "url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "has_solution", "type": "string", "display": true, "removed": false, "required": false, "displayName": "has_solution", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["id"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "", "cachedResultName": ""}, "documentId": {"__rl": true, "mode": "list", "value": "", "cachedResultUrl": "", "cachedResultName": ""}}, "credentials": {"googleSheetsOAuth2Api": {"id": "", "name": ""}}, "notesInFlow": true, "typeVersion": 4.5}, {"id": "bbcf5797-c3dc-495f-85e9-178755d29c50", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-120, 0], "parameters": {"rule": {"interval": [{"field": "hours"}]}}, "typeVersion": 1.2}, {"id": "357975bc-9e13-494d-93da-c4238b42b5b3", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [60, -220], "parameters": {"width": 340, "height": 420, "content": "## Modify the Query Parameter\n\n**Double-click** the node to open it for editing.\n\nAdjust the value of the \"q\" parameter to match the keyword you wish to monitor.\n\n\n\n"}, "typeVersion": 1}, {"id": "f53b958d-71d4-49cb-9db2-48e8d12301a9", "name": "Get topics", "type": "n8n-nodes-base.splitOut", "position": [460, 0], "parameters": {"options": {}, "fieldToSplitOut": "topics"}, "typeVersion": 1}, {"id": "6fcd7991-4d3c-4705-a2f6-a85660cad80f", "name": "Get latest topics", "type": "n8n-nodes-base.httpRequest", "position": [180, 0], "parameters": {"url": "https://community.n8n.io/search", "options": {"response": {"response": {"responseFormat": "json"}}}, "sendQuery": true, "queryParameters": {"parameters": [{"name": "q", "value": "ADD-YOUR-KEYWORD-<PERSON>ERE"}, {"name": "order", "value": "latest"}]}}, "notesInFlow": true, "typeVersion": 4.2}, {"id": "2483ecbc-6795-4fed-bce3-23108bc7087a", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [640, -220], "parameters": {"width": 340, "height": 420, "content": "## Add your Google Sheets API credentials\n\n**Double-click** the node to open it for editing.\n\nSelect the document from the list. Please note to add columns \"id\", \"date\", \"title\", \"url\", \"has_solution\"\n\n\n\n\n"}, "typeVersion": 1}, {"id": "4791f99d-7bc2-4d85-8bd3-86a78475aed0", "name": "Google Sheets Trigger", "type": "n8n-nodes-base.googleSheetsTrigger", "position": [-80, 640], "parameters": {"options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1DDVOKXbRGM_2lHZSUm4bH_VqAZ9jKBMOARVyf3hE5kI/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1DDVOKXbRGM_2lHZSUm4bH_VqAZ9jKBMOARVyf3hE5kI", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1DDVOKXbRGM_2lHZSUm4bH_VqAZ9jKBMOARVyf3hE5kI/edit?usp=drivesdk", "cachedResultName": "n8n Community topic tracker based on keyword"}}, "credentials": {"googleSheetsTriggerOAuth2Api": {"id": "LGzWbSDkVxbOBOBT", "name": "Google Sheets Trigger account"}}, "typeVersion": 1}, {"id": "c1d43a4b-f681-40f6-9736-10ee3ad511f2", "name": "<PERSON><PERSON>ck", "type": "n8n-nodes-base.slack", "position": [220, 580], "webhookId": "aca9b9e2-e9d4-40eb-a2be-bd2a07b31ce8", "parameters": {"text": "New topics are available in n8n community", "otherOptions": {}}, "typeVersion": 2.3}, {"id": "cc531378-6341-43ea-87c5-03a048ff74a9", "name": "Send Email", "type": "n8n-nodes-base.emailSend", "position": [220, 760], "parameters": {"text": "New topics are available in n8n community.", "options": {}, "emailFormat": "text"}, "credentials": {"smtp": {"id": "tDSWM9BZ9H2FaedY", "name": "SMTP account 2"}}, "typeVersion": 2.1}, {"id": "2b025fc2-4e78-4120-9d36-0ca3f4fd5743", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-140, 360], "parameters": {"width": 580, "height": 600, "content": "## Send a message when Sheet is updated (Optional)\n\n### Delete these nodes if you don't want to be alerted.\n\nYou can configure channels you want to connect, when Google Sheet is updated, so that you receive a message instantly."}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "3cd62f18-29c4-4e14-b560-5c96e33650d4", "connections": {"Get topics": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Get latest topics", "type": "main", "index": 0}]]}, "Get latest topics": {"main": [[{"node": "Get topics", "type": "main", "index": 0}]]}, "Google Sheets Trigger": {"main": [[{"node": "<PERSON><PERSON>ck", "type": "main", "index": 0}, {"node": "Send Email", "type": "main", "index": 0}]]}}}