{"nodes": [{"id": "d0c92688-14fc-4393-a1d6-926eb867b81e", "name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [180, 240], "parameters": {}, "typeVersion": 1}, {"id": "0edbad78-249b-441c-877d-bac57fb44a91", "name": "Note", "type": "n8n-nodes-base.stickyNote", "position": [180, 31], "parameters": {"width": 436, "height": 169, "content": "## n8n version\n\nThis workflow was created using n8n version 0.197.1 and uses a new [expression syntax](https://docs.n8n.io/code-examples/methods-variables-reference/) as well as a new version of the Merge node. Make sure you're also using n8n version 0.197.1 or newer when running this workflow."}, "typeVersion": 1}, {"id": "251d893c-11cb-4702-a289-44f198581722", "name": "Download XML File", "type": "n8n-nodes-base.httpRequest", "position": [400, 240], "parameters": {"url": "https://www.w3schools.com/xml/simple.xml", "options": {}}, "typeVersion": 3}, {"id": "0973b302-1ba9-4faf-9d6c-2caca1b301f5", "name": "Parse XML content", "type": "n8n-nodes-base.xml", "position": [620, 240], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "********-27cb-40c1-b95e-14f91f89e9f1", "name": "Create new spreadsheet file", "type": "n8n-nodes-base.googleSheets", "position": [1060, 140], "parameters": {"title": "My XML Data", "options": {}, "resource": "spreadsheet"}, "credentials": {"googleSheetsOAuth2Api": {"id": "19", "name": "<PERSON>'s Google Sheets account"}}, "executeOnce": true, "typeVersion": 2}, {"id": "affbcb81-5873-406e-a51d-cd6fee682992", "name": "Define header row", "type": "n8n-nodes-base.set", "position": [1280, 140], "parameters": {"values": {"string": [{"name": "columns", "value": "={{ [ Object.keys($(\"Split out food items\").first().json) ] }}"}]}, "options": {}, "keepOnlySet": true}, "executeOnce": true, "typeVersion": 1}, {"id": "537aff03-ae08-4712-bfae-15f0e3a5e69a", "name": "Split out food items", "type": "n8n-nodes-base.itemLists", "position": [840, 240], "parameters": {"options": {}, "fieldToSplitOut": "breakfast_menu.food"}, "typeVersion": 1}, {"id": "b247f984-6ed2-4de0-8877-a61571863ff8", "name": "Write header row", "type": "n8n-nodes-base.googleSheets", "position": [1500, 140], "parameters": {"options": {}, "rawData": true, "sheetId": "={{ $(\"Create new spreadsheet file\").first().json[\"spreadsheetId\"] }}", "operation": "update", "dataProperty": "columns"}, "credentials": {"googleSheetsOAuth2Api": {"id": "19", "name": "<PERSON>'s Google Sheets account"}}, "typeVersion": 2}, {"id": "fc9e2c32-30b1-4162-a686-2d049e52e111", "name": "Wait for spreadsheet creation", "type": "n8n-nodes-base.merge", "position": [1720, 240], "parameters": {"mode": "chooseBranch", "output": "input2"}, "typeVersion": 2}, {"id": "fdc6d5d9-e08d-4086-a233-0edb3c11bc86", "name": "Write data to sheet", "type": "n8n-nodes-base.googleSheets", "position": [1940, 240], "parameters": {"options": {}, "sheetId": "={{ $(\"Create new spreadsheet file\").first().json[\"spreadsheetId\"] }}", "operation": "append"}, "credentials": {"googleSheetsOAuth2Api": {"id": "19", "name": "<PERSON>'s Google Sheets account"}}, "typeVersion": 2}], "connections": {"Write header row": {"main": [[{"node": "Wait for spreadsheet creation", "type": "main", "index": 0}]]}, "Define header row": {"main": [[{"node": "Write header row", "type": "main", "index": 0}]]}, "Download XML File": {"main": [[{"node": "Parse XML content", "type": "main", "index": 0}]]}, "Parse XML content": {"main": [[{"node": "Split out food items", "type": "main", "index": 0}]]}, "Split out food items": {"main": [[{"node": "Create new spreadsheet file", "type": "main", "index": 0}, {"node": "Wait for spreadsheet creation", "type": "main", "index": 1}]]}, "On clicking 'execute'": {"main": [[{"node": "Download XML File", "type": "main", "index": 0}]]}, "Create new spreadsheet file": {"main": [[{"node": "Define header row", "type": "main", "index": 0}]]}, "Wait for spreadsheet creation": {"main": [[{"node": "Write data to sheet", "type": "main", "index": 0}]]}}}