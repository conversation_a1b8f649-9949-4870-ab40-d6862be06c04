{"meta": {"instanceId": "95b3ab5a70ab1c8c1906357a367f1b236ef12a1409406fd992f60255f0f95f85", "templateCredsSetupCompleted": true}, "nodes": [{"id": "0b6d74c3-e034-40be-9f42-df42c2ffbb03", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1080, 1040], "parameters": {"width": 1219, "height": 674, "content": "### Introduction\nThis workflow streamlines the process of capturing leads via Facebook Lead Ads and transferring them automatically into KlickTipp. It ensures that contact data is accurately mapped and added to KlickTipp to trigger personalized email campaigns.\n\n### Benefits\n- **Automated lead import**: New leads from Facebook forms are automatically synced to KlickTipp without manual effort.\n- **Seamless campaign activation**: Tags can be assigned during the process, instantly triggering follow-up campaigns like welcome emails or webinar reminders.\n- **Reliable data structure**: Validated form entries are mapped to predefined custom fields, ensuring a high-quality contact base in KlickTipp.\n\n### Key Feature\n- **Facebook Lead Ads Trigger**: Captures form submissions from Facebook Ads in real-time.\n- **Data Processing**: Assigns and formats lead data based on field mappings:\n  - Maps standard Facebook fields (name, email) directly.\n  - Matches custom fields such as course selection, payment method, and comments to KlickTipp custom fields.\n- **Subscriber Management in KlickTipp**: Adds or updates contacts with structured mapping to custom fields. Tags can be dynamically added for segmentation:\n  - Personal data: First name, email address.\n  - Form responses: Selected course, payment method, comments.\n  - Tag-based segmentation for automated workflows.\n\n#### Setup Instructions\n1. Set up the Facebook Leads Ads (choose your form) and KlickTipp nodes (choose opt-in, tagging and field mapping) in your n8n instance.\n2. Authenticate your Facebook Lead Ads and KlickTipp accounts.\n3. Create the necessary custom fields to match the data structure\n4. Verify and customize field assignments in the workflow to align with your specific form and subscriber list setup.\n\nCustom Fields:\n   - `Facebook_Leads_Ads_Kommentar` (Text)\n   - `Facebook_Leads_Ads_Kursauswahl` (Text)\n   - `Facebook_Leads_Ads_Zahlungsweise` (Text)\n\n\n### Testing and Deployment\n1. Perform a test with the meta developer tool verify the transmission. (⚠️ Attention: KlickTipp rightfully rejects this <NAME_EMAIL> due to its validation rules, as it cannot receive emails. You can manipulate the output in the node for testing.)\n2. Confirm new subscribers appear in KlickTipp with mapped fields and tags.\n3. Launch your campaign in Facebook with full automation in place.\n\n- **Customization**: Adjust tag names and field mappings in the KlickTipp module of Make to fit your specific setup. Ensure any additional fields are created beforehand in KlickTipp to avoid sync errors."}, "typeVersion": 1}, {"id": "84d11f91-5a50-49a0-a511-93d83fa434f4", "name": "Facebook Lead <PERSON><PERSON>", "type": "n8n-nodes-base.facebookLeadAdsTrigger", "notes": "This node listens for new leads generated via Facebook Lead Ads. When a user submits a form on Facebook or Instagram, it triggers the workflow and captures the lead's details.", "position": [1460, 840], "webhookId": "04c33978-2df7-4ab1-a37c-3ab3c0a0d21f", "parameters": {"form": {"__rl": true, "mode": "list", "value": "***************", "cachedResultName": "Integrations Manual - Kursregistrierung"}, "page": {"__rl": true, "mode": "list", "value": "***************", "cachedResultUrl": "https://facebook.com/***************", "cachedResultName": "<PERSON>lick<PERSON><PERSON><PERSON>"}, "options": {}}, "credentials": {"facebookLeadAdsOAuth2Api": {"id": "bBzZPOu1M8YbIM9L", "name": "Facebook Lead Ads account 3"}}, "notesInFlow": true, "typeVersion": 1}, {"id": "e4532533-b447-4340-b750-6e3c47809cb8", "name": "Subscribe lead in KlickTipp", "type": "n8n-nodes-klicktipp.klicktipp", "notes": "Subscribes the incoming Facebook lead to the KlickTipp. This allows automatic follow-up, tagging, or integration with email campaigns.", "position": [1780, 840], "parameters": {"email": "={{ $json.data.email }}", "fields": {"dataFields": [{"fieldId": "fieldFirstName", "fieldValue": "={{ // Extracts the first name (the first part of the full name), which will be identified by the letters before the first empty space \" \". This implementation only supports the first name.\n$json[\"data\"][\"full name\"].split(\" \")[0] }}"}, {"fieldId": "fieldLastName", "fieldValue": "={{ // Extracts the last name (the last part of the full name), which will be identified by the letters after the last empty space \" \". This implementation does not support double names.\n$json[\"data\"][\"full name\"].split(\" \").pop() }}"}, {"fieldId": "field216784", "fieldValue": "={{ $json.data['hast_du_zusätzliche_kommentare_für_uns?'] }}"}, {"fieldId": "field216785", "fieldValue": "={{ $json.data['welcher_kurs_interessiert_dich?'] }}"}, {"fieldId": "field216786", "fieldValue": "={{ $json.data['was_ist_deine_bevorzugte_zahlungsweise?'] }}"}]}, "listId": "358895", "resource": "subscriber", "operation": "subscribe"}, "credentials": {"klickTippApi": {"id": "K9JyBdCM4SZc1cXl", "name": "DEMO KlickTipp account"}}, "notesInFlow": true, "typeVersion": 2}], "pinData": {}, "connections": {"Facebook Lead Ads Trigger": {"main": [[{"node": "Subscribe lead in KlickTipp", "type": "main", "index": 0}]]}}}