{"meta": {"instanceId": "84ba6d895254e080ac2b4916d987aa66b000f88d4d919a6b9c76848f9b8a7616", "templateId": "2359"}, "nodes": [{"id": "654e210f-08b1-4ba4-b464-9499084092a2", "name": "split custom_fields", "type": "n8n-nodes-base.splitOut", "position": [980, 640], "parameters": {"include": "allOtherFields", "options": {}, "fieldToSplitOut": "custom_fields"}, "typeVersion": 1}, {"id": "9b1a4071-7dd8-4d60-b077-d686fff40d24", "name": "Stripe | Get latest checkout sessions1", "type": "n8n-nodes-base.httpRequest", "position": [460, 640], "parameters": {"url": "=https://api.stripe.com/v1/checkout/sessions", "options": {"pagination": {"pagination": {"parameters": {"parameters": [{"name": "starting_after", "value": "={{ $response.body.data.last().id }}"}]}, "completeExpression": "={{ $response.body.has_more == false }}", "paginationCompleteWhen": "other"}}}, "jsonQuery": "={\n  \"created\": {\n    \"gte\":{{ $today.minus(20, 'days').toSeconds() }},\n    \"lte\":{{ $today.toSeconds() }}\n  }\n}", "sendQuery": true, "specifyQuery": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "stripeApi"}, "typeVersion": 4.2}, {"id": "17016a73-5338-49c7-af8d-8587c778c2f6", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [380, 240], "parameters": {"color": 7, "width": 252.741654751449, "height": 593.3373455805055, "content": "## Retrieve all checkout sessions from the last 7 days.\n\nYou can adjust the period by changing the \"created\" value.\n\n[🔍 Learn more about the \"created\" parameter](https://docs.stripe.com/api/checkout/sessions/list?lang=curl#list_checkout_sessions-created)\n\n\nAnd this node uses pagination to get all results. You want to keep those settings at the bottom."}, "typeVersion": 1}, {"id": "e46a5332-a008-4617-be57-eb22e713022d", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [700, 545], "parameters": {"color": 7, "width": 451.2991079615292, "height": 267.24226082469556, "content": "## Split data for easier visualization"}, "typeVersion": 1}, {"id": "ebf8a12a-787c-4ab8-9060-2241bbf38489", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [1220, 237], "parameters": {"color": 7, "height": 598.2429925878827, "content": "## Select the custom fields you want\n\nHere you can choose to filter your contacts to keep only the ones who contain certain custom_fields.\n\nLet's say you only want the ones who have filled their nickname and job title."}, "typeVersion": 1}, {"id": "e9c54905-dadb-4b5e-9ce0-cfe7d436c51e", "name": "Filter by custom_field", "type": "n8n-nodes-base.filter", "position": [1280, 640], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "4579d72e-8d48-4146-952d-9b5b400f5bce", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.custom_fields.key }}", "rightValue": "nickname"}, {"id": "34197f40-9b41-46e4-8796-be3a86e4dcca", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.custom_fields.key }}", "rightValue": "job_title"}]}}, "typeVersion": 2}, {"id": "14915079-68ba-48ab-9a9d-fe627aa2bd33", "name": "split all data", "type": "n8n-nodes-base.splitOut", "position": [760, 640], "parameters": {"options": {}, "fieldToSplitOut": "data"}, "typeVersion": 1}], "pinData": {}, "connections": {"split all data": {"main": [[{"node": "split custom_fields", "type": "main", "index": 0}]]}, "split custom_fields": {"main": [[{"node": "Filter by custom_field", "type": "main", "index": 0}]]}, "Stripe | Get latest checkout sessions1": {"main": [[{"node": "split all data", "type": "main", "index": 0}]]}}}