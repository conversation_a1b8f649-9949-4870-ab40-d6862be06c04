{"meta": {"instanceId": "257476b1ef58bf3cb6a46e65fac7ee34a53a5e1a8492d5c6e4da5f87c9b82833", "templateId": "2105"}, "nodes": [{"id": "3abfbefa-0a41-4dd2-a79b-99aa02447a6f", "name": "When clicking \"Test workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [380, 240], "parameters": {}, "typeVersion": 1}, {"id": "5233daa6-9b3f-4048-8187-b78decac0bbd", "name": "Delete ID", "type": "n8n-nodes-base.googleSheets", "position": [1900, 380], "parameters": {"operation": "delete", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1lj15jPOKrfS0-EAnCmths4-SVXwQJ78eBnq8C4DFRx4/edit#gid=0", "cachedResultName": "Last Member"}, "documentId": {"__rl": true, "mode": "url", "value": "={{ $('Setup: Edit this to get started').first().json['Google Sheets URL'] }}"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "9", "name": "<PERSON>'s Google"}}, "executeOnce": true, "typeVersion": 4.2, "alwaysOutputData": true}, {"id": "d3be48cd-9652-43ea-9bbf-d9d3a6c972ae", "name": "SaveID", "type": "n8n-nodes-base.googleSheets", "position": [2040, 380], "parameters": {"columns": {"value": {"ID": "={{ $('Merge').last().json.user.id }}"}, "schema": [{"id": "ID", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ID", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["ID"]}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1lj15jPOKrfS0-EAnCmths4-SVXwQJ78eBnq8C4DFRx4/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "url", "value": "={{ $('Setup: Edit this to get started').first().json['Google Sheets URL'] }}"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "9", "name": "<PERSON>'s Google"}}, "typeVersion": 4.2}, {"id": "a8cb3b10-1143-4467-936c-36ea29c3489a", "name": "Get ID", "type": "n8n-nodes-base.googleSheets", "position": [920, 240], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1lj15jPOKrfS0-EAnCmths4-SVXwQJ78eBnq8C4DFRx4/edit#gid=0", "cachedResultName": "Last Member"}, "documentId": {"__rl": true, "mode": "url", "value": "={{ $('Setup: Edit this to get started').first().json['Google Sheets URL'] }}"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "9", "name": "<PERSON>'s Google"}}, "typeVersion": 4.2, "alwaysOutputData": true}, {"id": "82bdeec7-5ff5-4ed5-8c57-f3007bd7f81e", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [1520, 240], "parameters": {}, "typeVersion": 2.1}, {"id": "19247435-e0b0-4eac-8807-cb9e4ac532ab", "name": "Check if we have more members left", "type": "n8n-nodes-base.if", "position": [1740, 240], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "11bd5681-d979-40a8-ba0c-8c697532cf0d", "operator": {"type": "number", "operation": "lt"}, "leftValue": "={{ $input.all().length }}", "rightValue": 100}]}}, "typeVersion": 2}, {"id": "9845c82b-942e-4265-be8c-c4b1a9199b1e", "name": "We're done", "type": "n8n-nodes-base.noOp", "position": [2040, 160], "parameters": {}, "typeVersion": 1}, {"id": "86bf2fe1-22b3-4563-a4b7-b3603f96cada", "name": "Check if we have an ID", "type": "n8n-nodes-base.if", "position": [1100, 240], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "8cabfe61-be13-462f-a8ce-99ba5304fa10", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.ID }}", "rightValue": ""}]}}, "executeOnce": true, "typeVersion": 2}, {"id": "96324abb-2464-418a-850f-c6f8d3ce209f", "name": "Filter to only include members with role", "type": "n8n-nodes-base.filter", "position": [1740, -80], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "cac0aeae-ff45-4717-b11e-4e19995649fe", "operator": {"type": "number", "operation": "gt"}, "leftValue": "={{ $json.roles.filter(role => role === $('Setup: Edit this to get started').first().json['Role ID']).length }}", "rightValue": 0}]}}, "typeVersion": 2}, {"id": "bc012053-c619-479b-8bcb-9325c209d999", "name": "Get First 100 Members", "type": "n8n-nodes-base.discord", "position": [1300, 260], "parameters": {"guildId": {"__rl": true, "mode": "id", "value": "={{ $('Setup: Edit this to get started').first().json['Discord ID'] }}"}, "options": {"simplify": true}, "resource": "member"}, "credentials": {"discordBotApi": {"id": "M7ApR1tTlF4HFHn4", "name": "Discord Bot account"}}, "typeVersion": 2}, {"id": "7214e807-5a51-438d-9db8-32821307f4ea", "name": "Get next 100 Members after last ID", "type": "n8n-nodes-base.discord", "position": [1300, 80], "parameters": {"after": "={{ $('Get ID').first().json.ID }}", "guildId": {"__rl": true, "mode": "id", "value": "={{ $('Setup: Edit this to get started').first().json['Discord ID'] }}"}, "options": {"simplify": true}, "resource": "member"}, "credentials": {"discordBotApi": {"id": "M7ApR1tTlF4HFHn4", "name": "Discord Bot account"}}, "typeVersion": 2}, {"id": "158d3e7a-cc8c-4ab3-b59f-5a2251c79613", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [517, -60.**************], "parameters": {"color": 5, "width": 350.*************, "height": 491.*************, "content": "## Setup\n1. Add your Google Sheets and Discord credentials.\n2. Create a Google Sheets document that contains `ID` as a column. We're using this to remember which member we received last.\n3. Edit the fields in the setup node `Setup: Edit this to get started`. *You can read up on how to get the Discord IDs via [this link](https://www.pythondiscord.com/pages/guides/pydis-guides/contributing/obtaining-discord-ids/).*\n4. Link to your Discord server in the Discord nodes\n5. Activate the workflow\n6. Call the production webhook URL in your browser"}, "typeVersion": 1}, {"id": "11926dbb-a5e0-48f9-8453-7dc21ecf6717", "name": "Setup: Edit this to get started", "type": "n8n-nodes-base.set", "position": [640, 240], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "7c8cce4f-1330-425a-baff-4c40320f2335", "name": "Role ID", "type": "string", "value": "<Enter your roleID here>"}, {"id": "8533b358-d8e6-4eba-9159-f6bdd2e0df65", "name": "Google Sheets URL", "type": "string", "value": "<Enter your Sheets URL here>"}, {"id": "bb87e6f5-def9-4625-818a-ce6ff7b44ed7", "name": "Discord ID", "type": "string", "value": "<Enter your server/guild ID here>"}]}}, "typeVersion": 3.3}, {"id": "334377fc-ddb8-4c0d-9ddc-f6949b98578c", "name": "Webhook", "type": "n8n-nodes-base.webhook", "disabled": true, "position": [380, 420], "webhookId": "b40c1140-75a7-481e-b8c7-789eef1f8bac", "parameters": {"path": "discord-template", "options": {}, "responseMode": "responseNode"}, "typeVersion": 1.1}, {"id": "8fac2863-a046-4ce7-8391-72486141ea98", "name": "Send Response", "type": "n8n-nodes-base.respondToWebhook", "position": [1960, -80], "parameters": {"options": {}, "respondWith": "allIncomingItems"}, "typeVersion": 1}, {"id": "10677a2d-9bcb-4b51-8cab-a49c7f16a8d7", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1900, -180], "parameters": {"color": 7, "height": 265.6674473067916, "content": "You can replace this node according to your use case. In my case, I've send a DM to all users"}, "typeVersion": 1}], "pinData": {}, "connections": {"Merge": {"main": [[{"node": "Check if we have more members left", "type": "main", "index": 0}, {"node": "Filter to only include members with role", "type": "main", "index": 0}]]}, "Get ID": {"main": [[{"node": "Check if we have an ID", "type": "main", "index": 0}]]}, "SaveID": {"main": [[{"node": "Get ID", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Setup: Edit this to get started", "type": "main", "index": 0}]]}, "Delete ID": {"main": [[{"node": "SaveID", "type": "main", "index": 0}]]}, "Get First 100 Members": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Check if we have an ID": {"main": [[{"node": "Get next 100 Members after last ID", "type": "main", "index": 0}], [{"node": "Get First 100 Members", "type": "main", "index": 0}]]}, "When clicking \"Test workflow\"": {"main": [[{"node": "Setup: Edit this to get started", "type": "main", "index": 0}]]}, "Setup: Edit this to get started": {"main": [[{"node": "Get ID", "type": "main", "index": 0}]]}, "Check if we have more members left": {"main": [[{"node": "We're done", "type": "main", "index": 0}], [{"node": "Delete ID", "type": "main", "index": 0}]]}, "Get next 100 Members after last ID": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Filter to only include members with role": {"main": [[{"node": "Send Response", "type": "main", "index": 0}]]}}}