{"id": "aZSJ2BZQhNduZZ8w", "meta": {"instanceId": "d47f3738b860eed937a1b18d7345fa2c65cf4b4957554e29477cb064a7039870", "templateCredsSetupCompleted": true}, "name": "Get Airtable data in Obsidian Notes", "tags": [{"id": "zalLN3OHeRqcq4di", "name": "Obsidian", "createdAt": "2024-12-01T19:07:59.925Z", "updatedAt": "2024-12-01T19:07:59.925Z"}], "nodes": [{"id": "584cfe61-7f1b-4deb-ab4b-45a5ffd20daf", "name": "Airtable", "type": "n8n-nodes-base.airtableTool", "position": [540, 340], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appP3ocJy1rXIo6ko", "cachedResultUrl": "https://airtable.com/appP3ocJy1rXIo6ko", "cachedResultName": "table"}, "table": {"__rl": true, "mode": "list", "value": "tblywtlpPtGQMTJRm", "cachedResultUrl": "https://airtable.com/appP3ocJy1rXIo6ko/tblywtlpPtGQMTJRm", "cachedResultName": "Dummy"}, "options": {}, "operation": "search"}, "credentials": {"airtableTokenApi": {"id": "yiZ7ZC1md4geZovu", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "8a100c92-7971-464b-b3c0-18272f0a0bef", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [220, 340], "parameters": {"model": "gpt-4o-mini", "options": {}}, "credentials": {"openAiApi": {"id": "q8L9oWVM7QyzYEE5", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "98887b9b-2eae-4a2e-af2b-d40c1786c5a2", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [280, 200], "parameters": {"text": "={{ $json.body.content }}", "options": {}, "promptType": "define"}, "typeVersion": 1.6}, {"id": "********-3d78-4a9e-9f4c-a4136abcca4e", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-160, -260], "parameters": {"color": 7, "width": 497.*************, "height": 389.*************, "content": "[![YouTube Video](https://img.youtube.com/vi/2PIdeTgsENo/0.jpg)](https://www.youtube.com/watch?v=2PIdeTgsENo)"}, "typeVersion": 1}, {"id": "7adae874-d388-4265-aff8-28a1970bd0fb", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [360, -240], "parameters": {"width": 563.*************, "height": 368.*************, "content": "## Get Airtable Data in Obsidian with AI Agent\n<-- Watch the video to see it in action!\n\n**How to Set Up:**\n- Install the [Post Webhook Plugin](https://github.com/Masterb1234/obsidian-post-webhook/) in Obsidian.\n- Insert the n8n Webhook URL into the Post Webhook plugin settings.\n- Configure Your Airtable Node to match your workflow needs.\n\n\n**How to Use:**\n- Highlight text containing a question about your Airtable data.\n- Open the Obsidian Command Palette (Ctrl+P) and choose 'Send Selection to [Your Webhook]'.\n- Click, wait for the AI Agent to process your request, and see the result appear below your selected text."}, "typeVersion": 1}, {"id": "52c40581-656d-45b5-b366-d67cf2474312", "name": "Respond to Obsidian", "type": "n8n-nodes-base.respondToWebhook", "position": [700, 200], "parameters": {"options": {}, "respondWith": "text", "responseBody": "={{ $json.output }}"}, "typeVersion": 1.1}, {"id": "f2bf502e-5e6f-4e71-8c4f-27ec2dc5ab67", "name": "Webhook Set Up in Obsidian", "type": "n8n-nodes-base.webhook", "position": [-40, 200], "webhookId": "59fc8248-d3f7-4dbc-bdf3-39d59e427160", "parameters": {"path": "59fc8248-d3f7-4dbc-bdf3-39d59e427160", "options": {}, "httpMethod": "POST", "responseMode": "responseNode"}, "typeVersion": 2}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "dab99881-2d04-4113-9a4e-2f942fdf1c24", "connections": {"AI Agent": {"main": [[{"node": "Respond to Obsidian", "type": "main", "index": 0}]]}, "Airtable": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Webhook Set Up in Obsidian": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}}}