{"nodes": [{"name": "FileMaker", "type": "n8n-nodes-base.filemaker", "position": [450, 320], "parameters": {"action": "create", "layout": "My Form Layout", "fieldsParametersUi": {"fields": [{"name": "first_name", "value": "<PERSON><PERSON><PERSON>"}, {"name": "last_name", "value": "<PERSON><PERSON><PERSON>"}]}}, "credentials": {"fileMaker": "FileMaker API Credentials"}, "typeVersion": 1}, {"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 320], "parameters": {}, "typeVersion": 1}, {"name": "FileMaker", "type": "n8n-nodes-base.filemaker", "position": [450, 320], "parameters": {"action": "create", "layout": "My Form Layout", "fieldsParametersUi": {"fields": [{"name": "first_name", "value": "<PERSON><PERSON><PERSON>"}, {"name": "last_name", "value": "<PERSON><PERSON><PERSON>"}]}}, "credentials": {"fileMaker": "FileMaker API Credentials"}, "typeVersion": 1}, {"name": "FileMaker2", "type": "n8n-nodes-base.filemaker", "position": [650, 320], "parameters": {"modId": "={{$json[\"response\"][\"modId\"]}}", "recid": "={{$json[\"response\"][\"recordId\"]}}", "action": "edit", "layout": "My Form Layout", "fieldsParametersUi": {"fields": [{"name": "address_country", "value": "Germany"}]}}, "credentials": {"fileMaker": "FileMaker API Credentials"}, "typeVersion": 1}, {"name": "FileMaker3", "type": "n8n-nodes-base.filemaker", "position": [850, 320], "parameters": {"recid": "={{$node[\"FileMaker\"].json[\"response\"][\"recordId\"]}}", "layout": "My Form Layout"}, "credentials": {"fileMaker": "FileMaker API Credentials"}, "typeVersion": 1}], "connections": {"FileMaker": {"main": [[{"node": "FileMaker2", "type": "main", "index": 0}]]}, "FileMaker2": {"main": [[{"node": "FileMaker3", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "FileMaker", "type": "main", "index": 0}]]}}}