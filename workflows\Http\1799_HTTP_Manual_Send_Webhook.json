{"id": "a5tCsfMzJPd8WDUj", "meta": {"instanceId": "fddb3e91967f1012c95dd02bf5ad21f279fc44715f47a7a96a33433621caa253", "templateCredsSetupCompleted": true}, "name": "line message api demo", "tags": [], "nodes": [{"id": "2bc1cc31-136c-46a4-a789-476e33c76f3d", "name": "Line : Reply with token", "type": "n8n-nodes-base.httpRequest", "position": [-540, -460], "parameters": {"url": "https://api.line.me/v2/bot/message/reply", "method": "POST", "options": {}, "jsonBody": "={\n  \"replyToken\": \"{{ $('Webhook from Line Message').item.json.body.events[0].replyToken }}\",\n  \"messages\": [\n    {\n      \"type\": \"text\",\n      \"text\": \"收到您的訊息 : {{ $('Webhook from Line Message').item.json.body.events[0].message.text }}\"\n    }\n  ]\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "xB2Ip7YKSIDq7BoI", "name": "Line n8n demo auth"}}, "typeVersion": 4.2}, {"id": "a1d9c986-4712-4d40-955d-40d1b19d74db", "name": "Webhook from Line Message", "type": "n8n-nodes-base.webhook", "position": [-1020, -440], "webhookId": "638c118e-1c98-4491-b6ff-14e2e75380b6", "parameters": {"path": "638c118e-1c98-4491-b6ff-14e2e75380b6", "options": {}, "httpMethod": "POST"}, "typeVersion": 2}, {"id": "a0c94852-290f-48b9-8e11-b498ada90c8f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1100, -620], "parameters": {"width": 720, "height": 340, "content": "## Line Message API Reply\n\nReceived Message from user and reply with same text by using reply token  \n\nThere are many event types. So we need to determine if the type is message."}, "typeVersion": 1}, {"id": "278aff13-c081-47f0-a1f6-67920642e991", "name": "If", "type": "n8n-nodes-base.if", "position": [-800, -440], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "b63773bb-f010-4018-8142-240c9aaa4570", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.body.events[0].type }}", "rightValue": "message"}]}}, "typeVersion": 2.2}, {"id": "cff2f1d3-b7a4-4940-a1d1-1e5a80d6ea28", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-1100, -200], "parameters": {"width": 720, "height": 340, "content": "## Line Message API Send Message\n\nYou need to get the Line UID first.\nEvery user is differnt.\n\nIf you have the Line UID. Then you can push the message to the User."}, "typeVersion": 1}, {"id": "9348fc83-0aeb-4591-85b6-48f556512478", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-1020, -20], "parameters": {}, "typeVersion": 1}, {"id": "74db3e1b-9a22-4033-bf04-a8ff485a5d3b", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [-800, -20], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "6278f340-6287-4e89-b774-f6c584954d5b", "name": "line_uid", "type": "string", "value": "Uxxxxxxxxxxxx"}]}}, "typeVersion": 3.4}, {"id": "c593bd58-8f6a-4689-bb12-e71256ccf6e6", "name": "Line : Push Message", "type": "n8n-nodes-base.httpRequest", "position": [-560, -20], "parameters": {"url": "https://api.line.me/v2/bot/message/push", "method": "POST", "options": {}, "jsonBody": "={\n  \"to\": \"{{ $json.line_uid }}\",\n  \"messages\": [\n    {\n      \"type\": \"text\",\n      \"text\": \"推播測試\"\n    }\n  ]\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "xB2Ip7YKSIDq7BoI", "name": "Line n8n demo auth"}}, "typeVersion": 4.2}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "240dc848-8803-4776-b01d-5f10c765f72b", "connections": {"If": {"main": [[{"node": "Line : Reply with token", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Line : Push Message", "type": "main", "index": 0}]]}, "Webhook from Line Message": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}}}