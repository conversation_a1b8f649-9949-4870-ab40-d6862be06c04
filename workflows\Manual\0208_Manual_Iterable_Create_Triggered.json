{"id": "165", "name": "Create, update and get a user from Iterable", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [310, 340], "parameters": {}, "typeVersion": 1}, {"name": "Iterable", "type": "n8n-nodes-base.iterable", "position": [510, 340], "parameters": {"value": "", "identifier": "email", "additionalFields": {}}, "credentials": {"iterableApi": "Iterable"}, "typeVersion": 1}, {"name": "Iterable1", "type": "n8n-nodes-base.iterable", "position": [710, 340], "parameters": {"value": "={{$node[\"Iterable\"].parameter[\"value\"]}}", "identifier": "email", "additionalFields": {"dataFieldsUi": {"dataFieldValues": [{"key": "Name", "value": ""}]}}}, "credentials": {"iterableApi": "Iterable"}, "typeVersion": 1}, {"name": "Iterable2", "type": "n8n-nodes-base.iterable", "position": [910, 340], "parameters": {"email": "={{$node[\"Iterable\"].parameter[\"value\"]}}", "operation": "get"}, "credentials": {"iterableApi": "Iterable"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Iterable": {"main": [[{"node": "Iterable1", "type": "main", "index": 0}]]}, "Iterable1": {"main": [[{"node": "Iterable2", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Iterable", "type": "main", "index": 0}]]}}}