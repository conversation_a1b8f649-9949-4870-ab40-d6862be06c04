{"id": "29", "name": "Receive updates when a subscriber unsubscribes in Customer.io", "nodes": [{"name": "Customer.io <PERSON>", "type": "n8n-nodes-base.customerIoTrigger", "position": [650, 260], "webhookId": "88092579-1b8d-4d44-98d5-f24b3579cbc2", "parameters": {"events": ["customer.unsubscribed"]}, "credentials": {"customerIoApi": "customerIO"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {}}