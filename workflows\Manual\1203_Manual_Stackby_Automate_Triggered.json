{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [450, 300], "parameters": {"values": {"number": [{"name": "ID", "value": 1}], "string": [{"name": "Name", "value": "n8n"}]}, "options": {}}, "typeVersion": 1}, {"name": "Stackby", "type": "n8n-nodes-base.stackby", "position": [650, 300], "parameters": {"table": "Table 1", "columns": "ID, Name", "stackId": "stbgReRhlmmAgT2suT"}, "credentials": {"stackbyApi": "Stackby API credentials"}, "typeVersion": 1}, {"name": "Stackby1", "type": "n8n-nodes-base.stackby", "position": [850, 300], "parameters": {"table": "={{$node[\"Stackby\"].parameter[\"table\"]}}", "stackId": "={{$node[\"Stackby\"].parameter[\"stackId\"]}}", "operation": "list", "additionalFields": {}}, "credentials": {"stackbyApi": "Stackby API credentials"}, "typeVersion": 1}], "connections": {"Set": {"main": [[{"node": "Stackby", "type": "main", "index": 0}]]}, "Stackby": {"main": [[{"node": "Stackby1", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}}