{"nodes": [{"name": "TimescaleDB", "type": "n8n-nodes-base.timescaleDb", "position": [1110, 260], "parameters": {"table": "iss", "columns": "latitude, longitude, timestamp"}, "credentials": {"timescaleDb": "TimescaleDB"}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [910, 260], "parameters": {"values": {"string": [{"name": "latitude", "value": "={{$json[\"0\"][\"latitude\"]}}"}, {"name": "longitude", "value": "={{$json[\"0\"][\"longitude\"]}}"}, {"name": "timestamp", "value": "={{$json[\"0\"][\"timestamp\"]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [710, 260], "parameters": {"url": "https://api.wheretheiss.at/v1/satellites/25544/positions", "options": {}, "queryParametersUi": {"parameter": [{"name": "timestamps", "value": "={{Date.now()}}"}]}}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [510, 260], "parameters": {"triggerTimes": {"item": [{"mode": "everyMinute"}]}}, "typeVersion": 1}], "connections": {"Set": {"main": [[{"node": "TimescaleDB", "type": "main", "index": 0}]]}, "Cron": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}}