{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "FunctionItem", "type": "n8n-nodes-base.functionItem", "position": [450, 300], "parameters": {"functionCode": "// hashtag list\nconst Hashtags = [\n \"#techtwitter\",\n \"#n8n\"\n];\n\n// random output function\nconst randomHashtag = Hashtags[Math.floor(Math.random() * Hashtags.length)];\nitem.hashtag = randomHashtag;\nreturn item;"}, "typeVersion": 1}, {"name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [650, 300], "parameters": {"url": "https://api.openai.com/v1/engines/text-davinci-001/completions", "options": {}, "requestMethod": "POST", "authentication": "headerAuth", "jsonParameters": true, "bodyParametersJson": "={\n \"prompt\": \"Generate a tweet, with under 100 characters, about and including the hashtag {{$node[\"FunctionItem\"].json[\"hashtag\"]}}:\",\n \"temperature\": 0.7,\n \"max_tokens\": 64,\n \"top_p\": 1,\n \"frequency_penalty\": 0,\n \"presence_penalty\": 0\n}"}, "credentials": {"httpHeaderAuth": ""}, "typeVersion": 1}, {"name": "Airtable", "type": "n8n-nodes-base.airtable", "position": [1050, 300], "parameters": {"table": "main", "options": {}, "operation": "append", "application": "appOaG8kEA8FAABOr"}, "credentials": {"airtableApi": ""}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [850, 300], "parameters": {"values": {"string": [{"name": "Hashtag", "value": "={{$node[\"FunctionItem\"].json[\"hashtag\"]}}"}, {"name": "Content", "value": "={{$node[\"HTTP Request\"].json[\"choices\"][0][\"text\"]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}], "connections": {"Set": {"main": [[{"node": "Airtable", "type": "main", "index": 0}]]}, "FunctionItem": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "FunctionItem", "type": "main", "index": 0}]]}}}