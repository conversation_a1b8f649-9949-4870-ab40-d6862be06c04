{"nodes": [{"name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [-550, 450], "parameters": {"path": "PuHq2RQsmc3HXB/hook", "options": {"rawBody": false}, "httpMethod": "POST"}, "typeVersion": 1}, {"name": "Mautic", "type": "n8n-nodes-base.mautic", "position": [1260, 180], "parameters": {"email": "={{$node[\"@MAIN STUDENT DATA\"].json[\"student\"][\"email\"]}}", "company": 1, "options": {}, "lastName": "={{$node[\"@MAIN STUDENT DATA\"].json[\"student\"][\"lastName\"]}}", "firstName": "={{$node[\"@MAIN STUDENT DATA\"].json[\"student\"][\"firstName\"]}}", "authentication": "oAuth2", "additionalFields": {}}, "credentials": {"mauticOAuth2Api": "OAuth2 Mautic"}, "typeVersion": 1, "alwaysOutputData": false}, {"name": "Find User", "type": "n8n-nodes-base.mautic", "position": [170, 260], "parameters": {"limit": 1, "options": {"search": "={{$node[\"Set Webhook Request\"].json[\"student\"][\"email\"]}}"}, "operation": "getAll", "authentication": "oAuth2"}, "credentials": {"mauticOAuth2Api": "OAuth2 Mautic"}, "notesInFlow": false, "typeVersion": 1, "alwaysOutputData": false}, {"name": "Update User", "type": "n8n-nodes-base.mautic", "position": [1560, 250], "parameters": {"options": {}, "contactId": "={{$node[\"@MAIN STUDENT DATA\"].json[\"userFound\"]}}", "operation": "update", "updateFields": {"email": "={{$node[\"@MAIN STUDENT DATA\"].json[\"student\"][\"email\"]}}", "lastName": "={{$node[\"@MAIN STUDENT DATA\"].json[\"student\"][\"lastName\"]}}", "firstName": "={{$node[\"@MAIN STUDENT DATA\"].json[\"student\"][\"firstName\"]}}"}, "authentication": "oAuth2"}, "credentials": {"mauticOAuth2Api": "OAuth2 Mautic"}, "typeVersion": 1}, {"name": "Tag User", "type": "n8n-nodes-base.mautic", "position": [430, 670], "parameters": {"options": {}, "contactId": "={{$node[\"Find User To Tag Sale\"].json[\"id\"]}}", "operation": "update", "updateFields": {"tags": "={{$node[\"Set Webhook Request\"].json[\"student\"][\"course\"][\"name\"]}}"}, "authentication": "oAuth2"}, "credentials": {"mauticOAuth2Api": "OAuth2 Mautic"}, "typeVersion": 1}, {"name": "Unsubscribe User", "type": "n8n-nodes-base.mautic", "position": [2170, 410], "parameters": {"options": {}, "contactId": "={{$node[\"@MAIN STUDENT DATA\"].json[\"userFound\"]}}", "operation": "update", "updateFields": {"tags": "=#unsubscribe"}, "authentication": "oAuth2"}, "credentials": {"mauticOAuth2Api": "OAuth2 Mautic"}, "typeVersion": 1}, {"name": "Split Full Name", "type": "n8n-nodes-base.function", "position": [340, 420], "parameters": {"functionCode": "const student = items[0].json.student\nstudent.firstName = student.name ? student.name.split(' ').slice(0, -1).join(' ') : ''\nstudent.lastName= student.name ? student.name.split(' ').slice(-1).join(' ') : ''\nitems[0].json.student = student\nreturn items;"}, "typeVersion": 1}, {"name": "If not found return -1", "type": "n8n-nodes-base.function", "position": [450, 260], "parameters": {"functionCode": "items[0].json.id = items[0].json.id || -1\nreturn items"}, "typeVersion": 1}, {"name": "@MAIN STUDENT DATA", "type": "n8n-nodes-base.merge", "position": [900, 400], "parameters": {"join": "inner", "mode": "mergeByIndex"}, "typeVersion": 1}, {"name": "Remove unsubscribe", "type": "n8n-nodes-base.mautic", "position": [1770, 500], "parameters": {"options": {}, "contactId": "={{$node[\"@MAIN STUDENT DATA\"].json[\"userFound\"]}}", "operation": "update", "updateFields": {"tags": "=-#unsubscribe"}, "authentication": "oAuth2"}, "credentials": {"mauticOAuth2Api": "OAuth2 Mautic"}, "typeVersion": 1}, {"name": "Find User To Tag Sale", "type": "n8n-nodes-base.mautic", "position": [190, 670], "parameters": {"limit": 1, "options": {"search": "={{$node[\"Set Webhook Request\"].json[\"student\"][\"user\"][\"email\"]}}"}, "operation": "getAll", "authentication": "oAuth2"}, "credentials": {"mauticOAuth2Api": "OAuth2 Mautic"}, "notesInFlow": false, "typeVersion": 1, "alwaysOutputData": false}, {"name": "Set userFound", "type": "n8n-nodes-base.set", "position": [700, 260], "parameters": {"values": {"string": [{"name": "userFound", "value": "={{$node[\"If not found return -1\"].json[\"id\"]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "Switch Webhook Types", "type": "n8n-nodes-base.switch", "position": [-70, 450], "parameters": {"rules": {"rules": [{"value2": "User.", "operation": "contains"}, {"output": 1, "value2": "Sale.", "operation": "contains"}]}, "value1": "={{$node[\"Set Webhook Request\"].json[\"type\"]}}", "dataType": "string"}, "typeVersion": 1}, {"name": "Set Webhook Request", "type": "n8n-nodes-base.set", "position": [-310, 450], "parameters": {"values": {"string": [{"name": "student", "value": "={{$node[\"Webhook\"].json[\"body\"][\"object\"]}}"}, {"name": "type", "value": "={{$node[\"Webhook\"].json[\"body\"][\"type\"]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "IF NOT userFound", "type": "n8n-nodes-base.if", "position": [1090, 400], "parameters": {"conditions": {"string": [{"value1": "={{$node[\"@MAIN STUDENT DATA\"].json[\"userFound\"]}}", "value2": "-1", "operation": "regex"}]}}, "typeVersion": 1}, {"name": "Switch User.type", "type": "n8n-nodes-base.switch", "position": [1380, 420], "parameters": {"rules": {"rules": [{"value2": "User.updated"}, {"output": 1, "value2": "User.unsubscribe_from_marketing_emails"}, {"output": 2, "value2": "=User.subscribe_to_marketing_emails"}]}, "value1": "={{$node[\"@MAIN STUDENT DATA\"].json[\"type\"]}}", "dataType": "string"}, "typeVersion": 1}, {"name": "IF unsubscribe_from_marketing_emails", "type": "n8n-nodes-base.if", "position": [1770, 250], "parameters": {"conditions": {"string": [], "boolean": [{"value1": "={{$node[\"@MAIN STUDENT DATA\"].json[\"student\"][\"unsubscribe_from_marketing_emails\"]}}", "value2": true}]}}, "typeVersion": 1}], "connections": {"Webhook": {"main": [[{"node": "Set Webhook Request", "type": "main", "index": 0}]]}, "Find User": {"main": [[{"node": "If not found return -1", "type": "main", "index": 0}]]}, "Update User": {"main": [[{"node": "IF unsubscribe_from_marketing_emails", "type": "main", "index": 0}]]}, "Set userFound": {"main": [[{"node": "@MAIN STUDENT DATA", "type": "main", "index": 0}]]}, "Split Full Name": {"main": [[{"node": "@MAIN STUDENT DATA", "type": "main", "index": 1}]]}, "IF NOT userFound": {"main": [[{"node": "Mautic", "type": "main", "index": 0}], [{"node": "Switch User.type", "type": "main", "index": 0}]]}, "Switch User.type": {"main": [[{"node": "Update User", "type": "main", "index": 0}], [{"node": "Unsubscribe User", "type": "main", "index": 0}], [{"node": "Remove unsubscribe", "type": "main", "index": 0}]]}, "@MAIN STUDENT DATA": {"main": [[{"node": "IF NOT userFound", "type": "main", "index": 0}]]}, "Set Webhook Request": {"main": [[{"node": "Switch Webhook Types", "type": "main", "index": 0}]]}, "Switch Webhook Types": {"main": [[{"node": "Find User", "type": "main", "index": 0}, {"node": "Split Full Name", "type": "main", "index": 0}], [{"node": "Find User To Tag Sale", "type": "main", "index": 0}]]}, "Find User To Tag Sale": {"main": [[{"node": "Tag User", "type": "main", "index": 0}]]}, "If not found return -1": {"main": [[{"node": "Set userFound", "type": "main", "index": 0}]]}, "IF unsubscribe_from_marketing_emails": {"main": [[{"node": "Unsubscribe User", "type": "main", "index": 0}], [{"node": "Remove unsubscribe", "type": "main", "index": 0}]]}}}