{"nodes": [{"id": "6d908a58-8893-48da-8311-8c28ebd8ec62", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-520, -280], "parameters": {"color": 7, "width": 1160, "height": 120, "content": "**Summarize YouTube videos**\n\nThis project automates the summarization of YouTube videos, transforming lengthy content into concise, actionable insights. By leveraging AI and workflow automation, it extracts video transcripts, analyzes key points, and generates summaries, saving time for content creators, researchers, and professionals. Perfect for staying informed, conducting research, or repurposing video content efficiently."}, "typeVersion": 1}, {"id": "98de613a-1b1e-4b46-915f-7bebcfd6a931", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-540, 120], "parameters": {"width": 230, "height": 80, "content": "Add the full YouTube URL. ☝️\nYou can change this input to a webhook or anything else."}, "typeVersion": 1}, {"id": "064208d4-52c3-46a9-9f9f-d37258189d06", "name": "Request YouTube Transcript", "type": "n8n-nodes-base.httpRequest", "position": [-200, -20], "parameters": {"url": "Apify API_KEY Here ???", "method": "POST", "options": {}, "jsonBody": "={\n    \"startUrls\": [\n        \"{{ $json['Full URL'] }}\"\n    ]\n}", "sendBody": true, "specifyBody": "json"}, "typeVersion": 4.2}, {"id": "ba5e52fd-18b1-4232-961c-b53b01e21202", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-280, -140], "parameters": {"color": 3, "width": 280, "height": 340, "content": "Once you follow the Setup Instructions (mentioned in the template page description), you can insert the full URL endpoint, which includes both the POST Endpoint and API Key. 👇"}, "typeVersion": 1}, {"id": "f3caad55-0c7d-4e8e-8649-79cc25b4e6aa", "name": "No Operation, do nothing", "type": "n8n-nodes-base.noOp", "position": [380, -20], "parameters": {}, "typeVersion": 1}, {"id": "8d72e533-a053-4317-9437-9d80d3ed098f", "name": "Summarization of a YouTube script", "type": "@n8n/n8n-nodes-langchain.chainSummarization", "position": [40, -20], "parameters": {"options": {}}, "typeVersion": 2}, {"id": "8f4e1c7c-286b-48aa-8f50-404e8f1d430b", "name": "YouTube video URL", "type": "n8n-nodes-base.formTrigger", "position": [-420, -20], "webhookId": "3dc17600-3020-40b1-be8f-e65ef45269b6", "parameters": {"options": {"path": "ddd"}, "formTitle": "Summarize YouTube video's", "formFields": {"values": [{"fieldLabel": "Full URL"}]}}, "typeVersion": 2.2}, {"id": "fb861e09-d415-4f32-a4de-a6ff84ac7f7b", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [380, 120], "parameters": {"color": 4, "height": 100, "content": "☝️ Optional\nIf the workflow ends here, Consider checking with another enrichment service."}, "typeVersion": 1}, {"id": "17c0dc77-bee4-4271-b957-e0c793537a03", "name": "Summarization Engine", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [40, 160], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "g0eql8rqZWICDd5g", "name": "OpenAi"}}, "typeVersion": 1.1}, {"id": "a8d5362e-459e-4a76-8ee2-b1eb977215a2", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [40, -140], "parameters": {"color": 5, "width": 280, "content": "The summarization node works automatically and professionally, recognizing the input text and processing it directly without requiring any enhancements from your side👇"}, "typeVersion": 1}], "pinData": {}, "connections": {"YouTube video URL": {"main": [[{"node": "Request YouTube Transcript", "type": "main", "index": 0}]]}, "Summarization Engine": {"ai_languageModel": [[{"node": "Summarization of a YouTube script", "type": "ai_languageModel", "index": 0}]]}, "Request YouTube Transcript": {"main": [[{"node": "Summarization of a YouTube script", "type": "main", "index": 0}]]}, "Summarization of a YouTube script": {"main": [[{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}}}