{"id": "Iz8TMdlc6JhaKkd9", "meta": {"instanceId": "dacfda6d2e1dbefe99a005845405509e9929b16710017f4bd5d2bf758941295c", "templateCredsSetupCompleted": true}, "name": "YouTube Video Transcriber", "tags": [], "nodes": [{"id": "a4e2f554-ebae-41df-912a-0d1081fa1736", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-100, -60], "webhookId": "70129cbe-1a05-495f-bd92-18d36c1bc260", "parameters": {"public": true, "options": {"title": "Youtube Video Transcriber 🚀", "subtitle": "Have a great transcription!  📖", "inputPlaceholder": "Insert a URL of a YouTube video.  💻"}, "initialMessages": "Give me a URL of a video from YouTube to start! 👍"}, "typeVersion": 1.1}, {"id": "34b2b12e-0eb5-4f59-bd30-e7b595d06b8c", "name": "Code", "type": "n8n-nodes-base.code", "position": [280, -60], "parameters": {"language": "python", "pythonCode": "import re\n\ndef youtube_video_url_validatior(video_url) -> str:\n  try:\n    if not video_url:\n      return {\"text\": 'URL from the video is required.', \"is_valid\": False}\n    \n    video_url: str = re.sub(r\"\\s{2,}\", \" \", video_url.strip())\n    \n    if not video_url:\n      return {\"text\": 'URL from the video is required.', \"is_valid\": False}\n    \n    if len(video_url) < 25:\n      return {\"text\": 'URL is too short to be a valid YouTube URL.', \"is_valid\": False}\n    \n    # if not re.match(r'^[A-Za-z0-9:/._?&=-]+$', video_url):\n    #   return {\"text\": 'URL contains invalid characters.', \"is_valid\": False}\n    \n    is_valid: bool = False\n    \n    for pattern in [\n        r'^https?://(?:www\\.)?youtube\\.com/watch\\?v=[\\w-]{11}',\n        r'^https?://youtu\\.be/[\\w-]{11}',\n        r'^https?://(?:www\\.)?youtube\\.com/embed/[\\w-]{11}',\n        r'^https?://(?:www\\.)?youtube\\.com/v/[\\w-]{11}',\n    ]:\n        if re.match(pattern, video_url):\n          is_valid = True\n          \n          break\n    \n    if not is_valid:\n      return {\"text\": 'Invalid YouTube URL format.', \"is_valid\": False}\n      \n    video_url_id: str | None = re.search(r'(?:v=|youtu\\.be/|embed/|v/)([\\w-]{11})', video_url).group(1)\n    \n    if not video_url_id or not re.match(r'^[\\w-]{11}$', video_url_id):\n      return {\"text\": 'Invalid YouTube video ID.', \"is_valid\": False}\n    \n    return {\"text\": video_url, \"is_valid\": True}\n  except Exception as ex:\n    raise ex\n\nreturn youtube_video_url_validatior(_input.first().json.chatInput)"}, "typeVersion": 2}, {"id": "712cbf28-df12-44fc-b54a-bc21e13e55e7", "name": "If", "type": "n8n-nodes-base.if", "position": [600, -240], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "loose"}, "combinator": "and", "conditions": [{"id": "b8927a53-2755-4364-84b1-5340c5c31af5", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.is_valid }}", "rightValue": ""}]}, "looseTypeValidation": true}, "typeVersion": 2.2}, {"id": "be9e1903-25bc-4f1b-8793-5e657205dd5d", "name": "Respond to Webhook - Chat Message", "type": "n8n-nodes-base.respondToWebhook", "position": [600, 60], "parameters": {"options": {}, "respondWith": "text", "responseBody": "={{ $json.text }}"}, "retryOnFail": true, "typeVersion": 1.1}, {"id": "98bc7747-e688-4683-8686-ca44023f8648", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-200, -240], "parameters": {"color": 5, "width": 300, "height": 420, "content": "## Entry Point\n\nThe workflow entry point is the  node chat message."}, "typeVersion": 1}, {"id": "7ca94ab6-7306-4b04-8b34-eb9e0937d681", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [180, -240], "parameters": {"color": 3, "width": 300, "height": 420, "content": "## Validation - URL\n\nThis node ensures that only a valid youtube video url goes forward."}, "typeVersion": 1}, {"id": "c719c022-b55b-42b3-ab5f-36c0e1d62512", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1360, -320], "parameters": {"color": 4, "width": 460, "height": 560, "content": "## Data Structuring\n\nHere is the core of the workflow, where structuring is done to get the right format to answer.\n\n**NOTE:**\n\n1. Users implementing this template must modify the language in the OpenAI prompt to suit their desired output.\n\n2. An OpenAI API key is essential and must be properly configured to support data structuring and processing.  "}, "typeVersion": 1}, {"id": "7e1fa50f-7bd3-4bed-8537-969baa4c61de", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [820, -480], "parameters": {"color": 7, "width": 460, "height": 900, "content": "## Supadata\n\nSupadata is a powerful tool that converts YouTube video URLs into structured data via a simple API. It efficiently extracts transcriptions, making it ideal for AI training, data analysis, or text-based applications.\n\n**NOTE:**\n\n1. Users implementing this template must change the language in the query parameter to suit their needs. \n\n2. An API key is required and must be configured for the workflow to function properly."}, "typeVersion": 1}, {"id": "35bf3191-4113-4a92-85a8-b22d3b2a4134", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-920, -180], "parameters": {"width": 640, "height": 300, "content": "## Description\n\nThis workflow simplifies access to YouTube video content converting into clear and concise transcriptions, ideal for users seeking practicality. It transcribes YouTube videos directly and returns the text, eliminating the need to watch the full video. \n\nThe need for this workflow arose from the demands of studying, where, amidst the fast-paced routine of daily life, reading transcribed content proved faster and more efficient for creating summaries than watching entire videos. Often, time constraints make it difficult to watch videos in full, and written text allows for quicker absorption of information. This solution provides a seamless way to access and review content from any YouTube video, regardless of the topic."}, "typeVersion": 1}, {"id": "2ea0b992-231b-4f6d-9f6f-9f488d266cfb", "name": "<PERSON> Fields - Re<PERSON>ond to Chat Message 2", "type": "n8n-nodes-base.set", "position": [1000, 180], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "66270798-60eb-4ab8-8572-ab957474e260", "name": "text", "type": "string", "value": "={{ $json.error }} - {{ $json.message }}"}]}}, "typeVersion": 3.4}, {"id": "9846e903-015a-4111-b582-572d473fe4d3", "name": "<PERSON> Fields - Respond to Chat Message 3", "type": "n8n-nodes-base.set", "position": [1900, -320], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "97e0c175-8060-43da-9761-5c25d660c7ed", "name": "text", "type": "string", "value": "={{ $json.message.content }}"}]}}, "typeVersion": 3.4}, {"id": "775e067c-3518-4c64-a939-5f9b9b435b3c", "name": "<PERSON> Fields - <PERSON><PERSON>ond to Chat Message 4", "type": "n8n-nodes-base.set", "position": [1900, 100], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "66270798-60eb-4ab8-8572-ab957474e260", "name": "text", "type": "string", "value": "=Something went wrong with the data structuring."}]}}, "typeVersion": 3.4}, {"id": "91e22fcc-79b8-48d2-ba6e-bfb699ed9a07", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "onError": "continueErrorOutput", "position": [1000, -100], "parameters": {"url": "=https://api.supadata.ai/v1/youtube/transcript?url={{ $json.text }}&text=true&lang=pt", "options": {"timeout": 300000}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-api-key", "value": "SUPADATA_API_KEY"}]}}, "executeOnce": false, "notesInFlow": false, "retryOnFail": false, "typeVersion": 4.2, "alwaysOutputData": false}, {"id": "3fce199e-2e95-40a8-a78e-20a25c3f4300", "name": "OpenAI", "type": "@n8n/n8n-nodes-langchain.openAi", "onError": "continueErrorOutput", "position": [1460, 20], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini-2024-07-18", "cachedResultName": "GPT-4O-MINI-2024-07-18"}, "options": {}, "messages": {"values": [{"role": "system", "content": "You are an expert in grammar corrections and textual structuring.\n\nCorrect the classification of the provided text, adding commas, periods, question marks and other symbols necessary for natural and consistent reading. Do not change any words, just adjust the punctuation according to the grammatical rules and context.\n\nOrganize your content using markdown, structuring it with titles, subtitles, lists or other protected elements to clearly highlight the topics and information captured. Leave it in Portuguese and remember to always maintain the original formatting.\n\nTextual organization should always be a priority according to the content of the text, as well as the appropriate title, which must make sense."}, {"content": "={{ $json.content }}"}]}}, "credentials": {"openAiApi": {"id": "GpAe9wonPZjokqpc", "name": "OpenAi account"}}, "retryOnFail": true, "typeVersion": 1.8}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "d2f6a7fb-f3e1-462f-8627-7f67cc7bfa5b", "connections": {"If": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}], [{"node": "Respond to Webhook - Chat Message", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "<PERSON> Fields - Respond to Chat Message 3", "type": "main", "index": 0}], [{"node": "<PERSON> Fields - <PERSON><PERSON>ond to Chat Message 4", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}], [{"node": "<PERSON> Fields - Re<PERSON>ond to Chat Message 2", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}}}