{"meta": {"instanceId": "8c8c5237b8e37b006a7adce87f4369350c58e41f3ca9de16196d3197f69eabcd"}, "nodes": [{"id": "9971f7ab-ecc3-468b-8eb9-b58491b660bd", "name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [1040, 360], "parameters": {}, "typeVersion": 1}, {"id": "bb212963-9b6f-434c-9777-3360fb456d4b", "name": "Note", "type": "n8n-nodes-base.stickyNote", "position": [1320, 600], "parameters": {"width": 1020, "height": 360, "content": "# 3. Add items from B below items from A\n"}, "typeVersion": 1}, {"id": "cc9461f1-1016-4ef5-bc10-525942c45047", "name": "Note1", "type": "n8n-nodes-base.stickyNote", "position": [1320, -200], "parameters": {"width": 1020, "height": 380, "content": "# 1. Keep items from A if there's a match in B\n"}, "typeVersion": 1}, {"id": "09a68f64-5b2d-43a8-acff-7c26817cc025", "name": "Note2", "type": "n8n-nodes-base.stickyNote", "position": [1320, 200], "parameters": {"width": 1020, "height": 380, "content": "# 2. Enrich items from A with matching data from B"}, "typeVersion": 1}, {"id": "bcf0c7df-fb64-4ef8-9d75-300ff9b55f40", "name": "Note4", "type": "n8n-nodes-base.stickyNote", "position": [175, 235], "parameters": {"width": 740, "height": 460, "content": "# Aggregating data with the Merge node\n\n## The merge node is one of the most useful nodes in n8n. In this workflow we show how to merge data from two different sources (similar to SQL joins).\n\n## The most-used operations of the merge node are presented here. For more info, browse the [merge node docs](https://docs.n8n.io/integrations/core-nodes/n8n-nodes-base.merge/)\n\n## Click the `Execute Workflow` button and double click on the nodes to see the input and output items."}, "typeVersion": 1}, {"id": "b418defd-f58f-4f53-9bac-b1e6611151dc", "name": "Note6", "type": "n8n-nodes-base.stickyNote", "position": [1855, 335], "parameters": {"width": 480, "content": "## Adds the quantity needed to each ingredient in the recipe\n\n## Similar to SQL Left join\n\n"}, "typeVersion": 1}, {"id": "017b5902-865e-4481-98d2-0a969cc09482", "name": "Note8", "type": "n8n-nodes-base.stickyNote", "position": [1855, -65], "parameters": {"width": 480, "content": "## This will keep only the ingredients needed that are also in stock\n\n## Similar to SQL Inner join"}, "typeVersion": 1}, {"id": "e2b46667-da41-4448-a74d-3aa095f72619", "name": "Note9", "type": "n8n-nodes-base.stickyNote", "position": [1855, 695], "parameters": {"width": 480, "height": 200, "content": "## This will create a super band by merging <PERSON> and Led Zeppelin\n\n## Similar to SQL Union All \n(more flexible as not requires all fields to be the same)"}, "typeVersion": 1}, {"id": "9726c9cc-cab1-44f8-8c62-2b80899af4aa", "name": "Ingredients in stock from recipe", "type": "n8n-nodes-base.merge", "position": [1600, -20], "parameters": {"mode": "combine", "options": {}, "mergeByFields": {"values": [{"field1": "Name", "field2": "Name"}]}}, "typeVersion": 2}, {"id": "42367b1e-8a5d-4b0c-bfd3-8bb3f1b63df9", "name": "Super Band", "type": "n8n-nodes-base.merge", "position": [1620, 760], "parameters": {}, "typeVersion": 2}, {"id": "b4a756d8-a729-4add-aafa-9868738a6790", "name": "<PERSON><PERSON> Needed", "type": "n8n-nodes-base.code", "position": [1360, -100], "parameters": {"jsCode": " return [\n  {\n    \"Name\": \"F<PERSON>\",\n  },\n  {\n    \"Name\": \"Eggs\",\n  },\n  {\n    \"Name\": \"Milk\",\n  },\n  {\n    \"Name\": \"<PERSON>\",\n  },\n  {\n    \"Name\": \"<PERSON>\",\n  },\n];\n"}, "typeVersion": 1}, {"id": "eb69abdc-cb89-43c5-bcd6-5f1f6383b391", "name": "B. Ingredients in stock", "type": "n8n-nodes-base.code", "position": [1360, 40], "parameters": {"jsCode": " return [\n  {\n    \"Name\": \"<PERSON>s\",\n  },\n  {\n    \"Name\": \"<PERSON>\",\n  },\n  {\n    \"Name\": \"<PERSON>\",\n  },\n];\n"}, "typeVersion": 1}, {"id": "b01228b8-c860-4725-a0e1-00b4c11218cc", "name": "Merge recipe", "type": "n8n-nodes-base.merge", "position": [1620, 380], "parameters": {"mode": "combine", "options": {}, "joinMode": "enrichInput1", "mergeByFields": {"values": [{"field1": "Name", "field2": "Name"}]}}, "typeVersion": 2}, {"id": "fdb8a9cb-8a85-4a9a-bd2f-c9711178333f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.code", "position": [1360, 300], "parameters": {"jsCode": " return [\n  {\n    \"Name\": \"F<PERSON>\",\n  },\n  {\n    \"Name\": \"Eggs\",\n  },\n  {\n    \"Name\": \"Milk\",\n  },\n  {\n    \"Name\": \"<PERSON>\",\n  },\n  {\n    \"Name\": \"<PERSON>\",\n  },\n];\n"}, "typeVersion": 1}, {"id": "2ca385e5-6833-49fa-b052-abc8583b4a7a", "name": "B. Recipe quantities", "type": "n8n-nodes-base.code", "position": [1360, 440], "parameters": {"jsCode": " return [\n  {\n    \"Name\": \"Flour\",\n    \"Quantity\": \"100g\",\n  },\n  {\n    \"Name\": \"Eggs\",\n    \"Quantity\": 2,\n  },\n  {\n    \"Name\": \"Salt\",\n    \"Quantity\": \"50g\"\n  },\n  {\n    \"Name\": \"<PERSON>\",\n    \"Quantity\": 1,\n  },\n  {\n    \"Name\": \"<PERSON>\",\n    \"Quantity\": \"6tbsp\",\n  },\n];\n"}, "typeVersion": 1}, {"id": "8e4c7da8-3700-4b1f-b937-739debf7aba4", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.code", "position": [1360, 680], "parameters": {"jsCode": " return [\n{\n\"FirstName\": \"<PERSON>\",\n\"<PERSON><PERSON><PERSON>\": \"<PERSON>\",\n\"Instrument\": \"Drums\",\n},\n{\n\"FirstName\": \"<PERSON>\",\n\"LastName\": \"Mercury\",\n\"Instrument\": \"Vocals and Piano\",\n\"Superpower\": \"Crowd control\"\n},\n{\n\"FirstName\": \"<PERSON>\",\n\"LastName\": \"May\",\n\"Instrument\": \"<PERSON>\",\n},\n{\n\"FirstName\": \"<PERSON>\",\n\"LastName\": \"<PERSON>\",\n\"Instrument\": \"Bass\",\n}\n];\n"}, "typeVersion": 1}, {"id": "260c7a0a-43ba-46aa-bfa8-cbbb66aca493", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.code", "position": [1360, 820], "parameters": {"jsCode": " return [\n{\n\"FirstName\": \"<PERSON>\",\n\"<PERSON>N<PERSON>\": \"<PERSON>\",\n\"Instrument\": \"<PERSON>\"\n},\n{\n\"FirstName\": \"<PERSON>\",\n\"LastName\": \"Plant\",\n\"Instrument\": \"Vocals\",\n},\n{\n\"FirstName\": \"<PERSON>\",\n\"LastName\": \"<PERSON><PERSON>\",\n\"Instrument\": \"Drums\",\n},\n{\n\"FirstName\": \"<PERSON>\",\n\"LastName\": \"<PERSON>\",\n\"Instrument\": \"<PERSON>\",\n\"Second Instrument\": \"Keyboard\",\n}\n];\n"}, "typeVersion": 1}], "connections": {"A. Queen": {"main": [[{"node": "Super Band", "type": "main", "index": 0}]]}, "A. Ingredients": {"main": [[{"node": "Merge recipe", "type": "main", "index": 0}]]}, "B. Led Zeppelin": {"main": [[{"node": "Super Band", "type": "main", "index": 1}]]}, "B. Recipe quantities": {"main": [[{"node": "Merge recipe", "type": "main", "index": 1}]]}, "A. Ingredients Needed": {"main": [[{"node": "Ingredients in stock from recipe", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "<PERSON><PERSON> Needed", "type": "main", "index": 0}, {"node": "B. Ingredients in stock", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 0}, {"node": "B. Recipe quantities", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "B. Ingredients in stock": {"main": [[{"node": "Ingredients in stock from recipe", "type": "main", "index": 1}]]}}}