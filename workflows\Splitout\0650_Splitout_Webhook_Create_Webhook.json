{"nodes": [{"id": "ec2683b4-06ae-4255-bf20-b6c5850f4fc5", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.set", "position": [-480, 1100], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "e63f9299-a19d-4ba1-93b0-59f458769fb2", "name": "response", "type": "object", "value": "={{ $json.body.payload }}"}]}}, "typeVersion": 3.3}, {"id": "bb178ce9-3177-433e-a877-3635be7c3705", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-820, 740], "parameters": {"color": 7, "width": 466.8168310000617, "height": 567.6433222116042, "content": "![Imgur](https://i.imgur.com/iKyMV0N.png)\n## Events Webhook Trigger\nThe first node receives all messages from Slack API via Subscription Events API. You can find more information about setting up the subscription events API by [clicking here](https://api.slack.com/apis/connections/events-api). \n\nThe second node extracts the payload from slack into an object that n8n can understand.  "}, "typeVersion": 1}, {"id": "04d35926-1c7d-406b-90f1-9641680cb3b7", "name": "Sticky Note15", "type": "n8n-nodes-base.stickyNote", "position": [-340, 420], "parameters": {"color": 7, "width": 566.*************, "height": 1390.*************, "content": "![n8n](https://i.imgur.com/lKnBNnH.png)\n## Efficient Slack Interaction Handling with n8n\n\nThis section of the workflow is designed to efficiently manage and route messages and submissions from Slack based on specific triggers and conditions. When a Slack interaction occurs—such as a user triggering a vulnerability scan or generating a report through a modal—the workflow intelligently routes the message to the appropriate action:\n\n- **Dynamic Routing**: Uses conditions to determine the nature of the Slack interaction, whether it's a direct command to initiate a scan or a request to generate a report.\n- **Modal Management**: Differentiates actions based on modal titles and `callback_id`s, ensuring that each type of submission is processed according to its context.\n- **Streamlined Responses**: After routing, the workflow promptly handles the necessary responses or actions, including closing modal popups and responding to Slack with appropriate confirmation or data.\n\n**Purpose**: This mechanism ensures that all interactions within Slack are handled quickly and accurately, automating responses and actions in real-time to enhance user experience and workflow efficiency."}, "typeVersion": 1}, {"id": "e6a046b1-1c8b-4585-b257-117f562dd30f", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [240, 520], "parameters": {"color": 7, "width": 396.*************, "height": 1553.*************, "content": "![Imgur](https://i.imgur.com/iKyMV0N.png)\n## Display Modal Popup\nThis section pops open a modal window that is later used to send data into TheHive. \n\nModals can be customized to perform all sorts of actions. And they are natively mobile! You can see a screenshot of the Slack Modals on the right. \n\nLearn more about them by [clicking here](https://api.slack.com/surfaces/modals)"}, "typeVersion": 1}, {"id": "93b094eb-4a0a-4639-b343-932b7f261b0d", "name": "Close Mo<PERSON>up", "type": "n8n-nodes-base.respondToWebhook", "position": [-320, 2180], "parameters": {"options": {}, "respondWith": "json", "responseBody": "{\n  \"response_action\": \"clear\"\n}"}, "typeVersion": 1.1}, {"id": "6d67a6f5-9966-40a9-a9ad-db514027257b", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [240, 860], "parameters": {"color": 5, "width": 376.26546828439086, "height": 113.6416448104651, "content": "### 🙋 Don't forget your slack credentials!\nThankfully n8n makes it easy, as long as you've added credentials to a normal slack node, these http nodes are a snap to change via the drop down. "}, "typeVersion": 1}, {"id": "3222f63e-036f-43b6-9d60-a9d1a19bafa5", "name": "Idea Selector <PERSON>", "type": "n8n-nodes-base.httpRequest", "position": [320, 1000], "parameters": {"url": "https://slack.com/api/views.open", "method": "POST", "options": {}, "jsonBody": "=  {\n    \"trigger_id\": \"{{ $('Parse Webhook').item.json['response']['trigger_id'] }}\",\n    \"external_id\": \"Image Uploader\",\n    \"view\": {\n\t\"title\": {\n\t\t\"type\": \"plain_text\",\n\t\t\"text\": \"File Upload - Select\",\n\t\t\"emoji\": true\n\t},\n\t\"type\": \"modal\",\n\t\"external_id\": \"file_upload_selector\",\n\t\"close\": {\n\t\t\"type\": \"plain_text\",\n\t\t\"text\": \"Cancel\",\n\t\t\"emoji\": true\n\t},\n\t\"blocks\": [\n\t\t{\n\t\t\t\"type\": \"section\",\n\t\t\t\"block_id\": \"greeting_section\",\n\t\t\t\"text\": {\n\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\"text\": \":wave: Hey {{ $('Route Message').item.json.response.user.username }}!\\n\\nNeed to upload an image to a public repository? If so, you've come to the right place. Use the form below to upload your images to our public S3 CDN. You will get a message with the link to the file after submission. This tool only accepts .jpg, .png, and .pdf uploads.\",\n\t\t\t\t\"emoji\": true\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\t\"type\": \"divider\",\n\t\t\t\"block_id\": \"divider_1\"\n\t\t},\n\t\t{\n\t\t\t\"type\": \"actions\",\n\t\t\t\"block_id\": \"folder_type_selection\",\n\t\t\t\"elements\": [\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"radio_buttons\",\n\t\t\t\t\t\"options\": [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"text\": {\n\t\t\t\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\t\t\t\"text\": \"Create New Folder\",\n\t\t\t\t\t\t\t\t\"emoji\": true\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\"value\": \"createfolder\"\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"text\": {\n\t\t\t\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\t\t\t\"text\": \"Use Existing Folder\",\n\t\t\t\t\t\t\t\t\"emoji\": true\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\"value\": \"selectfolder\"\n\t\t\t\t\t\t}\n\t\t\t\t\t],\n\t\t\t\t\t\"action_id\": \"folder-type\"\n\t\t\t\t}\n\t\t\t]\n\t\t}\n\t]\n}\n}", "sendBody": true, "jsonQuery": "{\n  \"Content-type\": \"application/json\"\n}", "sendQuery": true, "specifyBody": "json", "specifyQuery": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "slackApi"}, "credentials": {"slackApi": {"id": "GjRorC99RZt4Wnrp", "name": "Image Upload Bot"}}, "typeVersion": 4.2}, {"id": "a23e7c3b-7f20-4832-a4f0-a696e661accf", "name": "Route Message", "type": "n8n-nodes-base.switch", "position": [-300, 1100], "parameters": {"rules": {"values": [{"outputKey": "Idea Selector", "conditions": {"options": {"version": 1, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.response.callback_id }}", "rightValue": "idea_selector"}]}, "renameOutput": true}, {"outputKey": "Block Action", "conditions": {"options": {"version": 1, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a0374196-2553-4916-bc55-c2ea663a7c1f", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.response.type }}", "rightValue": "block_actions"}]}, "renameOutput": true}, {"outputKey": "Submit Data", "conditions": {"options": {"version": 1, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "65daa75f-2e17-4ba0-8fd8-2ac2159399e3", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.response.type }}", "rightValue": "view_submission"}]}, "renameOutput": true}]}, "options": {"fallbackOutput": "none"}}, "typeVersion": 3}, {"id": "91cde8d3-2eca-4a00-a2cc-61a4f2d3280f", "name": "Route Message1", "type": "n8n-nodes-base.switch", "position": [40, 1400], "parameters": {"rules": {"values": [{"outputKey": "Create Folder", "conditions": {"options": {"version": 1, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "02868fd8-2577-4c6d-af5e-a1963cb2f786", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.response.view.state.values.folder_type_selection['folder-type'].selected_option.value }}", "rightValue": "createfolder"}]}, "renameOutput": true}, {"outputKey": "Select Folder", "conditions": {"options": {"version": 1, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "211e13e8-3433-42d3-8884-ad89f2fee5d0", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.response.view.state.values.folder_type_selection['folder-type'].selected_option.value }}", "rightValue": "selectfolder"}]}, "renameOutput": true}]}, "options": {"fallbackOutput": "none"}}, "typeVersion": 3}, {"id": "0dd0e945-8a1d-4ba8-b711-e8ccc4a98ec1", "name": "Create Folder", "type": "n8n-nodes-base.httpRequest", "position": [320, 1320], "parameters": {"url": "https://slack.com/api/views.push", "method": "POST", "options": {}, "jsonBody": "=  {\n    \"trigger_id\": \"{{ $('Parse Webhook').item.json['response']['trigger_id'] }}\",\n    \"view\": {\n\t\"title\": {\n\t\t\"type\": \"plain_text\",\n\t\t\"text\": \"File Upload - New Folder\",\n\t\t\"emoji\": true\n\t},\n\t\"submit\": {\n\t\t\"type\": \"plain_text\",\n\t\t\"text\": \"Upload\",\n\t\t\"emoji\": true\n\t},\n\t\"type\": \"modal\",\n\t\"external_id\": \"file_upload_new_folder\",\n\t\"close\": {\n\t\t\"type\": \"plain_text\",\n\t\t\"text\": \"Cancel\",\n\t\t\"emoji\": true\n\t},\n\t\"blocks\": [\n\t\t{\n\t\t\t\"type\": \"section\",\n\t\t\t\"block_id\": \"greeting_section\",\n\t\t\t\"text\": {\n\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\"text\": \":wave: Hey there!\\n\\nNeed to upload an image to a public repository? If so, you've come to the right place. Use the form below to upload your images to our public S3 CDN. You will get a message with the link to the file after submission. This tool only accepts .jpg, .png, and .pdf uploads.\",\n\t\t\t\t\"emoji\": true\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\t\"type\": \"divider\",\n\t\t\t\"block_id\": \"divider_1\"\n\t\t},\n\t\t{\n\t\t\t\"type\": \"input\",\n\t\t\t\"block_id\": \"folder_name_block\",\n\t\t\t\"element\": {\n\t\t\t\t\"type\": \"plain_text_input\",\n\t\t\t\t\"action_id\": \"folder_name_input_action\",\n\t\t\t\t\"placeholder\": {\n\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\"text\": \"example_folder_name\"\n\t\t\t\t}\n\t\t\t},\n\t\t\t\"label\": {\n\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\"text\": \"Folder Name\",\n\t\t\t\t\"emoji\": true\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\t\"type\": \"context\",\n\t\t\t\"block_id\": \"folder_creation_context\",\n\t\t\t\"elements\": [\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\"text\": \"This will create a new folder in the CDN.\",\n\t\t\t\t\t\"emoji\": true\n\t\t\t\t}\n\t\t\t]\n\t\t},\n\t\t{\n\t\t\t\"type\": \"input\",\n\t\t\t\"block_id\": \"input_block_file\",\n\t\t\t\"label\": {\n\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\"text\": \"Image File Binary\"\n\t\t\t},\n\t\t\t\"element\": {\n\t\t\t\t\"type\": \"file_input\",\n\t\t\t\t\"action_id\": \"file_input_action\",\n\t\t\t\t\"filetypes\": [\n\t\t\t\t\t\"jpg\",\n\t\t\t\t\t\"png\",\n\t\t\t\t\t\"pdf\"\n\t\t\t\t],\n\t\t\t\t\"max_files\": 10\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\t\"type\": \"context\",\n\t\t\t\"elements\": [\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\"text\": \"You can upload up to 10 files at a time.\",\n\t\t\t\t\t\"emoji\": true\n\t\t\t\t}\n\t\t\t]\n\t\t}\n\t]\n}\n}", "sendBody": true, "jsonQuery": "{\n  \"Content-type\": \"application/json\"\n}", "sendQuery": true, "specifyBody": "json", "specifyQuery": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "slackApi"}, "credentials": {"slackApi": {"id": "GjRorC99RZt4Wnrp", "name": "Image Upload Bot"}}, "typeVersion": 4.2}, {"id": "d4cdcd35-b28e-4d01-a35f-20d239f92fca", "name": "Select Folder", "type": "n8n-nodes-base.httpRequest", "position": [320, 1560], "parameters": {"url": "https://slack.com/api/views.push", "method": "POST", "options": {}, "jsonBody": "=  {\n    \"trigger_id\": \"{{ $('Parse Webhook').item.json['response']['trigger_id'] }}\",\n    \"view\": {\n\t\"title\": {\n\t\t\"type\": \"plain_text\",\n\t\t\"text\": \"File Upload - Old Folder\",\n\t\t\"emoji\": true\n\t},\n\t\"submit\": {\n\t\t\"type\": \"plain_text\",\n\t\t\"text\": \"Upload\",\n\t\t\"emoji\": true\n\t},\n\t\"type\": \"modal\",\n\t\"external_id\": \"file_upload_old_folder\",\n\t\"close\": {\n\t\t\"type\": \"plain_text\",\n\t\t\"text\": \"Cancel\",\n\t\t\"emoji\": true\n\t},\n\t\"blocks\": [\n\t\t{\n\t\t\t\"type\": \"section\",\n\t\t\t\"block_id\": \"greeting_section\",\n\t\t\t\"text\": {\n\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\"text\": \":wave: Hey there!\\n\\nNeed to upload an image to a public repository? If so, you've come to the right place. Use the form below to upload your images to our public S3 CDN. You will get a message with the link to the file after submission. This tool only accepts .jpg, .png, and .pdf uploads.\",\n\t\t\t\t\"emoji\": true\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\t\"type\": \"divider\",\n\t\t\t\"block_id\": \"divider_1\"\n\t\t},\n\t\t{\n\t\t\t\"type\": \"input\",\n\t\t\t\"block_id\": \"tool_selector\",\n\t\t\t\"element\": {\n\t\t\t\t\"type\": \"external_select\",\n\t\t\t\t\"placeholder\": {\n\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\"text\": \"Search For Existing Folder Name\",\n\t\t\t\t\t\"emoji\": true\n\t\t\t\t},\n\t\t\t\t\"action_id\": \"folder_selector\"\n\t\t\t},\n\t\t\t\"label\": {\n\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\"text\": \"Folder Selector\",\n\t\t\t\t\"emoji\": true\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\t\"type\": \"context\",\n\t\t\t\"elements\": [\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\"text\": \"To see all folders, type in 'all'\",\n\t\t\t\t\t\"emoji\": true\n\t\t\t\t}\n\t\t\t]\n\t\t},\n\t\t{\n\t\t\t\"type\": \"input\",\n\t\t\t\"block_id\": \"input_block_file\",\n\t\t\t\"label\": {\n\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\"text\": \"Image File Binary\"\n\t\t\t},\n\t\t\t\"element\": {\n\t\t\t\t\"type\": \"file_input\",\n\t\t\t\t\"action_id\": \"file_input_action\",\n\t\t\t\t\"filetypes\": [\n\t\t\t\t\t\"jpg\",\n\t\t\t\t\t\"png\",\n\t\t\t\t\t\"pdf\"\n\t\t\t\t],\n\t\t\t\t\"max_files\": 10\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\t\"type\": \"context\",\n\t\t\t\"elements\": [\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\"text\": \"You can upload up to 10 files at a time.\",\n\t\t\t\t\t\"emoji\": true\n\t\t\t\t}\n\t\t\t]\n\t\t}\n\t]\n}\n}", "sendBody": true, "jsonQuery": "{\n  \"Content-type\": \"application/json\"\n}", "sendQuery": true, "specifyBody": "json", "specifyQuery": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "slackApi"}, "credentials": {"slackApi": {"id": "GjRorC99RZt4Wnrp", "name": "Image Upload Bot"}}, "typeVersion": 4.2}, {"id": "997821dc-c8e8-45f5-87e9-d006fe6b5de7", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [460, 2140], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "e7ae5827-2fe3-411b-9689-f0f6b2d9dfc0", "name": "Success Response", "type": "n8n-nodes-base.set", "position": [1440, 2360], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "bd5f7054-0259-45a4-b01e-11c63b76c18e", "name": "link", "type": "string", "value": "=https://uploads.n8n.io/{{ $('Parse Webhook').item.json.response.view.state.values.folder_name_block?.folder_name_input_action?.value ? $('Parse Webhook').item.json.response.view.state.values.folder_name_block.folder_name_input_action.value.replace(/\\s+/g, '_') : $('Parse Webhook').item.json.response.view.state.values.tool_selector.folder_selector.selected_option.value }}/{{ $('Split Out Files').item.json.name.replace(/\\s+/g, '_') }}"}, {"id": "2ed40d88-8ca5-4fe6-9387-3b021fe00dcf", "name": "slackresponse", "type": "string", "value": "={\"type\":\"section\",\"text\":{\"type\":\"mrkdwn\",\"text\":\"`https://uploads.n8n.io/{{ $('Parse Webhook').item.json.response.view.state.values.folder_name_block?.folder_name_input_action?.value ? $('Parse Webhook').item.json.response.view.state.values.folder_name_block.folder_name_input_action.value.replace(/\\s+/g, '_') : $('Parse Webhook').item.json.response.view.state.values.tool_selector.folder_selector.selected_option.value }}/{{ $('Split Out Files').item.json.name.replace(/\\s+/g, '_') }}`\"}}"}]}}, "typeVersion": 3.4}, {"id": "72f1af25-faef-4556-8b71-97deb03b7755", "name": "Check if uploaded successfully", "type": "n8n-nodes-base.if", "position": [1160, 2420], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "8b51d4d6-feb6-4e1a-9077-9bd88207d3b7", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.success }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "912e8689-53e0-4919-a664-b9025b4618b6", "name": "move on to next", "type": "n8n-nodes-base.noOp", "position": [1800, 2360], "parameters": {}, "typeVersion": 1}, {"id": "69d94e2e-9ff8-42ae-8969-bbe4b11976d2", "name": "No Operation, do nothing", "type": "n8n-nodes-base.noOp", "position": [760, 1940], "parameters": {}, "typeVersion": 1}, {"id": "8c43201b-97ee-419a-81a3-5cd9c204022a", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "position": [980, 1940], "parameters": {"options": {"mergeLists": false}, "fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "slackresponse"}]}}, "typeVersion": 1}, {"id": "792eaa0e-e281-451a-b582-4e3ecef9cb20", "name": "Route Action", "type": "n8n-nodes-base.switch", "position": [-80, 2180], "parameters": {"rules": {"values": [{"outputKey": "File Upload", "conditions": {"options": {"version": 1, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "54f7e9ca-23d5-428c-8148-41f27cafffd8", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "t", "rightValue": "f"}]}, "renameOutput": true}]}, "options": {"fallbackOutput": 0}}, "typeVersion": 3}, {"id": "3877766c-dc3f-4e4e-9921-5ef36c7ae787", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [-720, 1100], "webhookId": "7f9dd2fb-e324-4f72-8fbf-d1f6b4fa5c79", "parameters": {"path": "slack-image-upload-bot", "options": {}, "httpMethod": "POST", "responseMode": "responseNode"}, "typeVersion": 2}, {"id": "b2ee67cb-dd60-4775-aa1a-8d52e192991a", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-320, 2080], "parameters": {"color": 7, "width": 940, "height": 300, "content": "## Split Files out for processing\nTakes the single response from Slack and splits out the file objects to loop across them."}, "typeVersion": 1}, {"id": "c28de034-b4d6-4f78-a91b-0667830a7632", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [640, 2180], "parameters": {"color": 7, "width": 1360, "height": 540, "content": "## Loop through files to upload to S3 Cloudflare Bucket\nThe success and failure path report back to slack once all files are uploaded. "}, "typeVersion": 1}, {"id": "141cb7dc-d9a3-4440-b60f-7a3b3dd8f831", "name": "Failure Response", "type": "n8n-nodes-base.set", "position": [1460, 2560], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "bd5f7054-0259-45a4-b01e-11c63b76c18e", "name": "link", "type": "string", "value": "=Unable to upload {{ $('Parse Webhook').item.json.response.view.state.values.folder_name_block?.folder_name_input_action?.value ? $('Parse Webhook').item.json.response.view.state.values.folder_name_block.folder_name_input_action.value.replace(/\\s+/g, '_') : $('Parse Webhook').item.json.response.view.state.values.tool_selector.folder_selector.selected_option.value }}/{{ $('Split Out Files').item.json.name.replace(/\\s+/g, '_') }}"}, {"id": "39bbddba-e7a4-44cf-aab4-a90669548454", "name": "slackresponse", "type": "string", "value": "={\"type\":\"section\",\"text\":{\"type\":\"mrkdwn\",\"text\":\":warning:Unable to upload: `https://uploads.n8n.io/{{ $('Parse Webhook').item.json.response.view.state.values.folder_name_block?.folder_name_input_action?.value ? $('Parse Webhook').item.json.response.view.state.values.folder_name_block.folder_name_input_action.value.replace(/\\s+/g, '_') : $('Parse Webhook').item.json.response.view.state.values.tool_selector.folder_selector.selected_option.value }}/{{ $('Split Out Files').item.json.name.replace(/\\s+/g, '_') }}`\"}}"}]}}, "typeVersion": 3.4}, {"id": "e947e3b4-a016-4d9e-a647-53a666d4c1b9", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [640, 1780], "parameters": {"color": 7, "width": 1080, "height": 380, "content": "## Combine Success and failure responses in final message\nAllows for the workflow to fail gracefully. "}, "typeVersion": 1}, {"id": "7e7275c8-976b-493d-bfd8-7180517bac53", "name": "Respond to Slack Webhook - Success", "type": "n8n-nodes-base.respondToWebhook", "position": [40, 1000], "parameters": {"options": {}, "respondWith": "noData"}, "typeVersion": 1.1}, {"id": "d508c32d-414d-4316-8fdc-e1c8687f6fa8", "name": "Respond to Slack Webhook - No Action", "type": "n8n-nodes-base.respondToWebhook", "position": [-140, 1400], "parameters": {"options": {}, "respondWith": "noData"}, "typeVersion": 1.1}, {"id": "b2a06a70-4ec6-4d10-94e6-0467009af01e", "name": "Download File Binary", "type": "n8n-nodes-base.httpRequest", "position": [760, 2420], "parameters": {"url": "={{ $json.url_private_download }}", "options": {"response": {"response": {"responseFormat": "file"}}}, "authentication": "predefinedCredentialType", "nodeCredentialType": "slackApi"}, "credentials": {"slackApi": {"id": "bqdMGoCMwzFKzBXQ", "name": "Image Upload Bot User Token"}}, "typeVersion": 4.2}, {"id": "3ea9d291-233b-4f25-8538-f9427e55001b", "name": "Upload to S3 Bucket", "type": "n8n-nodes-base.s3", "position": [960, 2420], "parameters": {"fileName": "={{ $('Parse Webhook').item.json.response.view.state.values.folder_name_block?.folder_name_input_action?.value ? $('Parse Webhook').item.json.response.view.state.values.folder_name_block.folder_name_input_action.value.replace(/\\s+/g, '_') : $('Parse Webhook').item.json.response.view.state.values.tool_selector.folder_selector.selected_option.value }}/{{ $('Split Out Files').item.json.name.replace(/\\s+/g, '_') }}", "operation": "upload", "bucketName": "n8n-uploads", "additionalFields": {}}, "credentials": {"s3": {"id": "5sdH8lDK8m8bje6X", "name": "S3 account"}}, "typeVersion": 1}, {"id": "e1182b20-d90d-4f53-96e7-90b36aff7053", "name": "Post Image to Channel", "type": "n8n-nodes-base.slack", "position": [1420, 1940], "webhookId": "050fb588-26db-489d-86c0-9ac5d573108d", "parameters": {"text": "New Files Uploaded", "select": "channel", "blocksUi": "={\n\t\"blocks\": [\n\t\t{\n\t\t\t\"type\": \"section\",\n\t\t\t\"text\": {\n\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\"text\": \":file_folder: *{{ $('Parse Webhook').item.json.response.view.state.values.folder_name_block?.folder_name_input_action?.value ? $('Parse Webhook').item.json.response.view.state.values.folder_name_block.folder_name_input_action.value.replace(/\\s+/g, '_') : $('Parse Webhook').item.json.response.view.state.values.tool_selector.folder_selector.selected_option.value }}*\"\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\t\"type\": \"divider\"\n\t\t},\n\t\t{\n\t\t\t\"type\": \"section\",\n\t\t\t\"text\": {\n\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\"text\": \"*Here are the file URLs you uploaded:*\"\n\t\t\t}\n\t\t},\n\t\t{{ $('Aggregate').item.json.slackresponse }}\n\t]\n}", "channelId": {"__rl": true, "mode": "id", "value": "C081EHWKKH6"}, "messageType": "block", "otherOptions": {}}, "credentials": {"slackApi": {"id": "GjRorC99RZt4Wnrp", "name": "Image Upload Bot"}}, "typeVersion": 2.2}, {"id": "39814189-fbc3-46c0-992a-41623d7d0e7b", "name": "Split Out Files", "type": "n8n-nodes-base.splitOut", "position": [140, 2180], "parameters": {"options": {}, "fieldToSplitOut": "response.view.state.values.input_block_file.file_input_action.files"}, "typeVersion": 1}], "pinData": {"Webhook": [{"body": {"payload": "{\"type\":\"view_submission\",\"team\":{\"id\":\"T07JRGYN3KR\",\"domain\":\"n8n-labs\"},\"user\":{\"id\":\"U07K60SESLB\",\"username\":\"angel\",\"name\":\"angel\",\"team_id\":\"T07JRGYN3KR\"},\"api_app_id\":\"A07S1KHUHRD\",\"token\":\"dBcQKoCOKOLa2AkgMZH3EGvt\",\"trigger_id\":\"8124283638884.7637576751671.e215c65a755f3dcb5523094558e07a50\",\"view\":{\"id\":\"V0848FGHQ2C\",\"team_id\":\"T07JRGYN3KR\",\"type\":\"modal\",\"blocks\":[{\"type\":\"section\",\"block_id\":\"greeting_section\",\"text\":{\"type\":\"plain_text\",\"text\":\":wave: Hey there!\\n\\nNeed to upload an image to a public repository? If so, you've come to the right place. Use the form below to upload your images to our public S3 CDN. You will get a message with the link to the file after submission. This tool only accepts .jpg, .png, and .pdf uploads.\",\"emoji\":true}},{\"type\":\"divider\",\"block_id\":\"divider_1\"},{\"type\":\"input\",\"block_id\":\"tool_selector\",\"label\":{\"type\":\"plain_text\",\"text\":\"Folder Selector\",\"emoji\":true},\"optional\":false,\"dispatch_action\":false,\"element\":{\"type\":\"external_select\",\"action_id\":\"folder_selector\",\"placeholder\":{\"type\":\"plain_text\",\"text\":\"Search For Existing Folder Name\",\"emoji\":true}}},{\"type\":\"context\",\"block_id\":\"2nw+9\",\"elements\":[{\"type\":\"plain_text\",\"text\":\"To see all folders, type in 'all'\",\"emoji\":true}]},{\"type\":\"input\",\"block_id\":\"input_block_file\",\"label\":{\"type\":\"plain_text\",\"text\":\"Image File Binary\",\"emoji\":true},\"optional\":false,\"dispatch_action\":false,\"element\":{\"type\":\"file_input\",\"action_id\":\"file_input_action\",\"filetypes\":[\"jpg\",\"jpeg\",\"png\",\"pdf\"],\"max_files\":10,\"max_file_size_bytes\":10000000}},{\"type\":\"context\",\"block_id\":\"PsTmm\",\"elements\":[{\"type\":\"plain_text\",\"text\":\"You can upload up to 10 files at a time.\",\"emoji\":true}]}],\"private_metadata\":\"\",\"callback_id\":\"\",\"state\":{\"values\":{\"tool_selector\":{\"folder_selector\":{\"type\":\"external_select\",\"selected_option\":{\"text\":{\"type\":\"plain_text\",\"text\":\"\\ud83d\\udcc1 test_folder\",\"emoji\":true},\"value\":\"test_folder\"}}},\"input_block_file\":{\"file_input_action\":{\"type\":\"file_input\",\"files\":[{\"id\":\"F0848GKNTB2\",\"created\":1733297013,\"timestamp\":1733297013,\"name\":\"loveslack.png\",\"title\":\"loveslack.png\",\"mimetype\":\"image\\/png\",\"filetype\":\"png\",\"pretty_type\":\"PNG\",\"user\":\"U07K60SESLB\",\"user_team\":\"T07JRGYN3KR\",\"editable\":false,\"size\":31334,\"mode\":\"hosted\",\"is_external\":false,\"external_type\":\"\",\"is_public\":false,\"public_url_shared\":false,\"display_as_bot\":false,\"username\":\"\",\"url_private\":\"https:\\/\\/files.slack.com\\/files-pri\\/T07JRGYN3KR-F0848GKNTB2\\/loveslack.png\",\"url_private_download\":\"https:\\/\\/files.slack.com\\/files-pri\\/T07JRGYN3KR-F0848GKNTB2\\/download\\/loveslack.png\",\"media_display_type\":\"unknown\",\"thumb_64\":\"https:\\/\\/files.slack.com\\/files-tmb\\/T07JRGYN3KR-F0848GKNTB2-930517eeb6\\/loveslack_64.png\",\"thumb_80\":\"https:\\/\\/files.slack.com\\/files-tmb\\/T07JRGYN3KR-F0848GKNTB2-930517eeb6\\/loveslack_80.png\",\"thumb_360\":\"https:\\/\\/files.slack.com\\/files-tmb\\/T07JRGYN3KR-F0848GKNTB2-930517eeb6\\/loveslack_360.png\",\"thumb_360_w\":360,\"thumb_360_h\":360,\"thumb_160\":\"https:\\/\\/files.slack.com\\/files-tmb\\/T07JRGYN3KR-F0848GKNTB2-930517eeb6\\/loveslack_160.png\",\"original_w\":400,\"original_h\":400,\"thumb_tiny\":\"AwAwADDTooqFrhQ+MEgd6BN2JSQoyTgUKwYZBzUVx\\/qtw5A5qqkzbsZwDU31sPSxoUVHCSVOenapKoSdxGO1ST2rPmJ3nHAPNaBAIIPeozAnV+QPWmKSbGWjb4ijDIHH4ULaxplnOQPXoKil1CKMhYxux1I4Ap96nmQCRTkDn6ipaLjHZMVryNThAWHqKsIwdAy9DWXFCzsM\\/KD3NacaCNAo6ChO5c4xWw6q16rtGu0EgHkCrNFMhOzuYptnLZxtU9zWlZjEGwkkLxzU7KrDDDNCqFGFGBQJtt3GCEBs5OPSpKKKSSWw27n\\/2Q==\",\"permalink\":\"https:\\/\\/n8n-labs.slack.com\\/files\\/U07K60SESLB\\/F0848GKNTB2\\/loveslack.png\",\"permalink_public\":\"https:\\/\\/slack-files.com\\/T07JRGYN3KR-F0848GKNTB2-135b89a0c2\",\"comments_count\":0,\"shares\":{},\"channels\":[],\"groups\":[],\"ims\":[],\"has_more_shares\":false,\"has_rich_preview\":false,\"file_access\":\"visible\"}]}}}},\"hash\":\"1733296393.EXon8ZjS\",\"title\":{\"type\":\"plain_text\",\"text\":\"File Upload - Old Folder\",\"emoji\":true},\"clear_on_close\":false,\"notify_on_close\":false,\"close\":{\"type\":\"plain_text\",\"text\":\"Cancel\",\"emoji\":true},\"submit\":{\"type\":\"plain_text\",\"text\":\"Upload\",\"emoji\":true},\"previous_view_id\":\"V083KJV6BDH\",\"root_view_id\":\"V083KJV6BDH\",\"app_id\":\"A07S1KHUHRD\",\"external_id\":\"file_upload_old_folder\",\"app_installed_team_id\":\"T07JRGYN3KR\",\"bot_id\":\"B07SG49L53M\"},\"response_urls\":[],\"is_enterprise_install\":false,\"enterprise\":null}"}, "query": {}, "params": {}, "headers": {"host": "internal.users.n8n.cloud", "accept": "application/json,*/*", "x-real-ip": "**********", "user-agent": "Slackbot 1.0 (+https://api.slack.com/robots)", "content-type": "application/x-www-form-urlencoded", "content-length": "6177", "accept-encoding": "gzip,deflate", "x-forwarded-for": "**********", "x-forwarded-host": "internal.users.n8n.cloud", "x-forwarded-port": "443", "x-forwarded-proto": "https", "x-slack-signature": "v0=33ddc24aff06b872a518fafa28b78939ea0c88696498b5054d2624f096e02293", "x-forwarded-server": "076ef9270428", "x-slack-request-timestamp": "1733297021"}, "webhookUrl": "https://internal.users.n8n.cloud/webhook/slack-image-upload-bot", "executionMode": "production"}]}, "connections": {"Webhook": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "Post Image to Channel", "type": "main", "index": 0}]]}, "Route Action": {"main": [[{"node": "Split Out Files", "type": "main", "index": 0}]]}, "Parse Webhook": {"main": [[{"node": "Route Message", "type": "main", "index": 0}]]}, "Route Message": {"main": [[{"node": "Respond to Slack Webhook - Success", "type": "main", "index": 0}], [{"node": "Respond to Slack Webhook - No Action", "type": "main", "index": 0}], [{"node": "Close Mo<PERSON>up", "type": "main", "index": 0}]]}, "Route Message1": {"main": [[{"node": "Create Folder", "type": "main", "index": 0}], [{"node": "Select Folder", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "No Operation, do nothing", "type": "main", "index": 0}], [{"node": "Download File Binary", "type": "main", "index": 0}]]}, "Split Out Files": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "move on to next": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Failure Response": {"main": [[{"node": "move on to next", "type": "main", "index": 0}]]}, "Success Response": {"main": [[{"node": "move on to next", "type": "main", "index": 0}]]}, "Close Modal Popup": {"main": [[{"node": "Route Action", "type": "main", "index": 0}]]}, "Upload to S3 Bucket": {"main": [[{"node": "Check if uploaded successfully", "type": "main", "index": 0}]]}, "Download File Binary": {"main": [[{"node": "Upload to S3 Bucket", "type": "main", "index": 0}]]}, "No Operation, do nothing": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Check if uploaded successfully": {"main": [[{"node": "Success Response", "type": "main", "index": 0}], [{"node": "Failure Response", "type": "main", "index": 0}]]}, "Respond to Slack Webhook - Success": {"main": [[{"node": "Idea Selector <PERSON>", "type": "main", "index": 0}]]}, "Respond to Slack Webhook - No Action": {"main": [[{"node": "Route Message1", "type": "main", "index": 0}]]}}}