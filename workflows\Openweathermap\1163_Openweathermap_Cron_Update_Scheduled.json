{"id": "84", "name": "Send daily weather updates to a phone number using the Vonage node", "nodes": [{"name": "Vonage", "type": "n8n-nodes-base.von<PERSON>", "position": [770, 260], "parameters": {"to": "1234", "from": "Vonage APIs", "message": "=Hey! The temperature outside is {{$node[\"OpenWeatherMap\"].json[\"main\"][\"temp\"]}}°C.", "additionalFields": {}}, "credentials": {"vonageApi": "vonage"}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [370, 260], "parameters": {"triggerTimes": {"item": [{"hour": 9}]}}, "typeVersion": 1}, {"name": "OpenWeatherMap", "type": "n8n-nodes-base.openWeatherMap", "position": [570, 260], "parameters": {"cityName": "berlin"}, "credentials": {"openWeatherMapApi": "owm"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Cron": {"main": [[{"node": "OpenWeatherMap", "type": "main", "index": 0}]]}, "OpenWeatherMap": {"main": [[{"node": "Vonage", "type": "main", "index": 0}]]}}}