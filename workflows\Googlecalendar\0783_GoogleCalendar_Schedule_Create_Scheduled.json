{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "cbc2ee05-3bb9-4064-ac26-fed7241e673f", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-460, 0], "parameters": {"rule": {"interval": [{"triggerAtHour": 6}]}}, "typeVersion": 1.2}, {"id": "4a18dea4-9eda-4b8e-9d0c-fff9793802c5", "name": "Get Past Events", "type": "n8n-nodes-base.googleCalendar", "position": [-280, 0], "parameters": {"options": {}, "timeMax": "={{ $now.minus({ day: 2 }) }}", "timeMin": "={{ $now.minus({ day: 4 }) }}", "calendar": {"__rl": true, "mode": "id", "value": "<your-calendar>"}, "operation": "getAll"}, "credentials": {"googleCalendarOAuth2Api": {"id": "kWMxmDbMDDJoYFVK", "name": "Google Calendar account"}}, "typeVersion": 1.3}, {"id": "df2ef6d0-5fcb-43c5-8ba9-2d094dffb4e1", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [200, 40], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "bedc77ad-f0c9-47ae-9609-48ceda47a224", "name": "Flag to Follow Up", "type": "n8n-nodes-base.set", "position": [580, 200], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "={{\n{\n  ...$('Loop Over Items').first().json,\n  followUp: $json.isEmpty()\n}\n}}", "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "b332ca5d-45d5-4a79-a028-baa1728aea78", "name": "Only Follow Ups", "type": "n8n-nodes-base.filter", "position": [400, 40], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "73f38d1b-75c6-4372-8e81-a2db61b045a8", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "1b8a6510-f1c5-4969-a68d-143874b5737d", "name": "Get Emails Since", "type": "n8n-nodes-base.gmail", "position": [400, 200], "webhookId": "08fbccff-cce6-461a-b040-f5a74920c803", "parameters": {"limit": 1, "filters": {"q": "=(from:{{ $json.attendees.find(attendee => !attendee.self)?.email }} OR to:{{ $json.attendees.find(attendee => !attendee.self)?.email }})", "receivedAfter": "={{ $json.end.dateTime }}"}, "resource": "thread"}, "credentials": {"gmailOAuth2": {"id": "Sf5Gfl9NiFTNXFWb", "name": "Gmail account"}}, "typeVersion": 2.1, "alwaysOutputData": true}, {"id": "4ce7ac3f-bad8-4822-b166-fd164d733734", "name": "Output", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [1140, 220], "parameters": {"schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"slots\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"start\": { \"type\": \"string\" },\n          \"end\": { \"type\": \"string\" }\n        }\n      }\n    }\n  }\n}"}, "typeVersion": 1.2}, {"id": "a22c5b78-d213-4e37-b2c6-f3d1dac96858", "name": "Availability", "type": "n8n-nodes-base.googleCalendarTool", "position": [1020, 220], "parameters": {"options": {"timezone": {"__rl": true, "mode": "id", "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Timezone', ``, 'string') }}", "__regex": "([-+/_a-zA-Z0-9]*)"}}, "timeMax": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('End_Time', ``, 'string') }}", "timeMin": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Start_Time', ``, 'string') }}", "calendar": {"__rl": true, "mode": "id", "value": "<your-calendar>"}, "resource": "calendar"}, "credentials": {"googleCalendarOAuth2Api": {"id": "kWMxmDbMDDJoYFVK", "name": "Google Calendar account"}}, "typeVersion": 1.3}, {"id": "690c79d3-cf0e-4d15-9419-dafb7d86025b", "name": "Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [900, 220], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "4e9d23c0-f9a0-4e71-b1b8-1011313942ba", "name": "Meeting Availability Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [920, 40], "parameters": {"text": "=### Details of the previous call as following\ntitle: {{ $json.summary }}\ndate: {{ $json.start.dateTime }} {{ $json.start.timeZone }}\nduration: {{ DateTime.fromISO($json.end.dateTime).diffTo(DateTime.fromISO($json.start.dateTime), 'minutes') }} minutes", "options": {"systemMessage": "=You are a calendar availability assistant. Analyse the previous meeting and help me find a similar available slot for the next meeting.\n* take into consideration the day, time of day and duration of the previous meeting and try to set the same or similar for the next\n* next meeting should be in the future.\n* return a list of available slots so that I can forward them to the user.\n\nToday's date is {{ $now }}."}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.7}, {"id": "851728bf-7f94-4434-9dc6-23569544cdb7", "name": "Generate Message", "type": "n8n-nodes-base.set", "position": [1260, 40], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "cf09c95c-f25e-4fd7-bade-a0feaeaffb3b", "name": "message", "type": "string", "value": "=Hey, just noticed there wasn't a follow-up email to {{ $('Only Follow Ups').item.json.attendees.find(x => !x.self).email }} after your last call a few days ago.\n\nHere's are a few available slots to book the next call.\n{{\n$json.output.slots\n  .filter(slot => !DateTime.fromISO(slot.start).isWeekend())\n  .map(slot => `* ${DateTime.fromISO(slot.start).format('cccc, DDD @ hh:mm')} - ${DateTime.fromISO(slot.end).format('hh:mm')}`)\n.join('\\n')\n}}\n\nLet me know which I should book or let me know if it's okay to dismiss."}]}}, "typeVersion": 3.4}, {"id": "7e45eddc-8c34-402a-86a2-ed89ff463095", "name": "Meetings", "type": "n8n-nodes-base.googleCalendarTool", "position": [2360, 240], "parameters": {"end": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('End', ``, 'string') }}", "start": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Start', ``, 'string') }}", "calendar": {"__rl": true, "mode": "id", "value": "<your-calendar>"}, "additionalFields": {"summary": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Summary', ``, 'string') }}", "description": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Description', ``, 'string') }}"}}, "credentials": {"googleCalendarOAuth2Api": {"id": "kWMxmDbMDDJoYFVK", "name": "Google Calendar account"}}, "typeVersion": 1.3}, {"id": "74618cf0-1fe5-4abb-ba38-6818162ce826", "name": "Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [2180, 240], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "790cc7ee-fe1b-434f-8736-38952bffbb85", "name": "Meeting Booking Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [2180, 60], "parameters": {"text": "={{ $json.data.text }}", "options": {"systemMessage": "=You are a meeting booking agent. Your task is to book the meeting requested if confirmed by the user or otherwise do nothing if the user wants to disregard. No need to ask for further approval.\n\nAI: {{ $('Generate Message').first().json.message }}"}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "7ed171b2-08ee-49b0-9f9b-b4943549b2f6", "name": "<PERSON> as Seen", "type": "n8n-nodes-base.removeDuplicates", "position": [-100, 0], "parameters": {"options": {}, "operation": "removeItemsSeenInPreviousExecutions", "dedupeValue": "={{ $json.id }}"}, "typeVersion": 2}, {"id": "c8198538-4e02-429d-9fef-4cc2cb0bb7d0", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-540, -200], "parameters": {"color": 7, "width": 620, "height": 420, "content": "## 1. Get Recent Meetings\n[Learn more about the GCalendar node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googlecalendar)\n\nFor this template, a scheduled trigger is set to fire every morning to pull in past meetings from 2-3 days ago. A \"Remove Duplicates\" node ensures we don't double process events more than once between runs."}, "typeVersion": 1}, {"id": "ef4888e2-249f-4501-a731-4dc8886dfa1a", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [100, -160], "parameters": {"color": 7, "width": 680, "height": 600, "content": "## 2. Check If Any Messages Since\n[Read more about the Gmail node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.gmail)\n\nNext, we want to check if there has been any messages/contact between the lead and the user since the meeting ended. Where there is not, it could be a good opportunity to remind the user to reengage the lead as to not lose them."}, "typeVersion": 1}, {"id": "d9ccc4d5-2ccb-4f85-ada1-6a6fc5374ff2", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [800, -160], "parameters": {"color": 7, "width": 620, "height": 580, "content": "## 3. Suggest Availability For Next Call\n[Read more about AI Agents](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.agent/)\n\nOnce filtered for applicable leads, we can use an AI Agent to suggest another meeting slot for them. An AI Agent can analyse the previous meeting details and use that information to suggest a similar date and time."}, "typeVersion": 1}, {"id": "851b15f6-ea6a-4d30-a45b-f9ed087a37fa", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1440, -200], "parameters": {"color": 7, "width": 540, "height": 520, "content": "## 4. Get Human Approval\n[Learn more about n8n's Human-in-the-loop features](https://docs.n8n.io/advanced-ai/examples/human-fallback/)\n\nOf course, we don't want the AI to actually book the meeting unless the user confirms it is something he/she wants to do and the best way to confirm is just to ask the user directly! Thanks for n8n's Human-in-the-loop feature, we can achieve this with a number of messaging protocols.\n\nHere, we're using the Gmail node's **Send-and-wait-for-approval** mode. This will send an email to the user and give them a textbox to tell our agent what they want to do next."}, "typeVersion": 1}, {"id": "725b187f-d59b-4a7d-bf11-6265a4c995ed", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [2000, -160], "parameters": {"color": 7, "width": 640, "height": 560, "content": "## 5. Book the meeting If Accepted\n[Learn more about the AI Agent node](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.agent/)\n\nFinally, the response from the user combined with the suggested availability slots are given to another AI agent which can book meetings. If the user accepted and confirmed a date, this agent will book the meeting on behalf of the user. If the user declined, then the agent takes no action."}, "typeVersion": 1}, {"id": "ae59a45a-01e9-42be-99da-f75ed90f881b", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-1000, -700], "parameters": {"width": 420, "height": 980, "content": "## Try it out!\n### This n8n template extends the idea of sales leads follow-up reminders by having an AI agent suggest and book the next call or message to reengage the prospect.\n\nWhat makes this template practical for use is that it uses the Human-in-the-loop approach to wait for a user's approval before actually making the booking. Without, this could be annoying for both the user and the recipient!\n\n### How it works\n* A scheduled trigger checks your google calendar for sales meetings which happened a few days ago.\n* For each event, gmail search is used to figure out if a follow-up message has been sent or received from the other party since the meeting. If none, we want to remind ourselves to reengage the lead.\n* For leads applicable for follow-up, we first get an AI Agent to find available meeting slots in the calendar.\n* These slots and reminder are sent to the user via send-and-approval mode of the gmail node. The user replies in natural language either picking a slot, suggesting an entirely new slot or declines the request.\n* When accepted, another AI Agent books the meeting in the calendar with the proposed dates and lead.\n\n### How to use\n* Update all calendar nodes (+subnodes) to point to the right calendar. If this is a shared-purpose calendar, you may need to either filter or create a new calendar.\n* Update the gmail nodes to point to the right accounts.\n\n### Customising the template\n* Not using Google? Swap out for Microsoft or otherwise.\n* Try swapping out or adding in additional send-for-approval methods such as telegram or whatsapp.\n\n### Need Help?\nJoin the [Discord](https://discord.com/invite/XPKeKXeB7d) or ask in the [Forum](https://community.n8n.io/)!"}, "typeVersion": 1}, {"id": "46ef7220-49ea-4dfc-8e4c-ce7da5119daf", "name": "Send for Human Approval", "type": "n8n-nodes-base.gmail", "position": [1660, 80], "webhookId": "76b88312-1c54-482e-abdd-e699159577f0", "parameters": {"sendTo": "=<your-email-here>", "message": "={{ $json.message }}", "options": {}, "subject": "=Book a follow-up meeting with {{ $('Only Follow Ups').item.json.attendees.find(x => !x.self).email }}?", "operation": "sendAndWait", "responseType": "freeText"}, "credentials": {"gmailOAuth2": {"id": "Sf5Gfl9NiFTNXFWb", "name": "Gmail account"}}, "typeVersion": 2.1}], "pinData": {}, "connections": {"Model": {"ai_languageModel": [[{"node": "Meeting Availability Agent", "type": "ai_languageModel", "index": 0}]]}, "Model1": {"ai_languageModel": [[{"node": "Meeting Booking Agent", "type": "ai_languageModel", "index": 0}]]}, "Output": {"ai_outputParser": [[{"node": "Meeting Availability Agent", "type": "ai_outputParser", "index": 0}]]}, "Meetings": {"ai_tool": [[{"node": "Meeting Booking Agent", "type": "ai_tool", "index": 0}]]}, "Availability": {"ai_tool": [[{"node": "Meeting Availability Agent", "type": "ai_tool", "index": 0}]]}, "Mark as Seen": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Get Past Events": {"main": [[{"node": "<PERSON> as Seen", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Only Follow Ups", "type": "main", "index": 0}], [{"node": "Get Emails Since", "type": "main", "index": 0}]]}, "Only Follow Ups": {"main": [[{"node": "Meeting Availability Agent", "type": "main", "index": 0}]]}, "Generate Message": {"main": [[{"node": "Send for Human Approval", "type": "main", "index": 0}]]}, "Get Emails Since": {"main": [[{"node": "Flag to Follow Up", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Get Past Events", "type": "main", "index": 0}]]}, "Flag to Follow Up": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Send for Human Approval": {"main": [[{"node": "Meeting Booking Agent", "type": "main", "index": 0}]]}, "Meeting Availability Agent": {"main": [[{"node": "Generate Message", "type": "main", "index": 0}]]}}}