{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "a8211c61-5ca5-4b0a-adce-b7954a387aba", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-540, 900], "parameters": {"width": 300, "height": 225, "content": "### The conversation history (last 20 messages) is stored in a buffer memory"}, "typeVersion": 1}, {"id": "639ef27d-3e6e-4d2b-804a-5d1c95d509fc", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-200, 900], "parameters": {"width": 340, "height": 225, "content": "### Tools which agent can use to accomplish the task"}, "typeVersion": 1}, {"id": "dcb7ade3-005c-44e3-a369-526baa5b8813", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-500, 500], "parameters": {"width": 422, "height": 211, "content": "### Conversational agent will utilise available tools to answer the prompt. "}, "typeVersion": 1}, {"id": "2830de15-bdd2-48f4-8957-659014cd0a82", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-800, 580], "webhookId": "d48f9e07-3c05-4be8-86ca-5cee4c27b78f", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "bd1865fc-c37f-4b81-8ee1-83205e67e42b", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-720, 1000], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "d9ee6da6-f2cd-4077-913c-9215433dfc31", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [-440, 1000], "parameters": {"contextWindowLength": 20}, "typeVersion": 1.3}, {"id": "fe8ddba3-37ba-43c3-9797-021b14a1be49", "name": "SerpAPI", "type": "@n8n/n8n-nodes-langchain.toolSerpApi", "position": [-140, 1000], "parameters": {"options": {}}, "credentials": {"serpApi": {"id": "aJCKjxx6U3K7ydDe", "name": "SerpAPI account"}}, "typeVersion": 1}, {"id": "f7cee7ea-6a21-4eae-a1c6-36716683a3eb", "name": "Wikipedia", "type": "@n8n/n8n-nodes-langchain.toolWikipedia", "position": [0, 1000], "parameters": {}, "typeVersion": 1}, {"id": "e6f6fe48-3ad0-4bfe-a2f2-922e4c652306", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-420, 580], "parameters": {"options": {}}, "typeVersion": 1.8}], "pinData": {}, "connections": {"SerpAPI": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Wikipedia": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}}}