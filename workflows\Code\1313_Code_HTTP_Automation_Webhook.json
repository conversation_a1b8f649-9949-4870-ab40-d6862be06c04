{"id": "LIAes1kWVZAWZBX2", "meta": {"instanceId": "31e69f7f4a77bf465b805824e303232f0227212ae922d12133a0f96ffeab4fef", "templateCredsSetupCompleted": true}, "name": "🎥 Analyze YouTube Video for Summaries, Transcripts & Content + Google Gemini AI", "tags": [], "nodes": [{"id": "6d96092e-a12e-42e7-9700-63d19c3f2403", "name": "Config", "type": "n8n-nodes-base.set", "position": [2760, 540], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "24e9b1c3-2955-4e0b-9b4b-a6b9d046fb72", "name": "google_api_key", "type": "string", "value": "={{ $env.GOOGLE_API_KEY }}"}, {"id": "b6600a42-1b8d-486a-a51d-0868bc45452e", "name": "youtube_url", "type": "string", "value": "=https://www.youtube.com/watch?v={{ $json[\"YouTube Video Id\"] }}"}, {"id": "ce9a9a40-5ae4-4106-ae61-0daba2ec185f", "name": "prompt_type", "type": "string", "value": "={{ $json[\"Prompt Type\"] }}"}, {"id": "47094d96-2e89-4294-b6da-7ee66917bd98", "name": "video_id", "type": "string", "value": "={{ $json[\"YouTube Video Id\"] }}"}]}}, "typeVersion": 3.4}, {"id": "4b4373dd-6b54-41c5-a490-91ec78afdb0b", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [2320, 760], "parameters": {"width": 300, "height": 600, "content": "### Prompt Options\n\n- **default**: Summarizes the video with emphasis on actionable insights, tools, strategies, and resources mentioned.\n\n- **transcribe**: Provides verbatim transcription of all spoken dialogue in the video without additional commentary.\n\n- **timestamps**: Creates a timestamped transcript of the video dialogue in [hh:mm:ss] format.\n\n- **summary**: Generates a concise bullet-point summary of the video's main points.\n\n- **scene**: Provides a comprehensive visual description of the video scene including setting, objects, people, lighting, colors, and camera techniques.\n\n- **clips**: Identifies shareable video segments with timestamps, transcripts, and explanations of their social media appeal.\n\n\n"}, "typeVersion": 1}, {"id": "41605c14-9936-43f2-8f06-c411bfddda99", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [2660, 420], "parameters": {"color": 7, "width": 300, "height": 300, "content": "## Set Workflow Config Variables"}, "typeVersion": 1}, {"id": "fdc9aeb2-35b4-4f33-9438-00a10f0cb0d5", "name": "Get Video Audience MetaData", "type": "n8n-nodes-base.httpRequest", "onError": "continueRegularOutput", "position": [3440, 540], "parameters": {"url": "=https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={{ $('Config').item.json.google_api_key }}", "method": "POST", "options": {}, "jsonBody": "={{ JSON.stringify({\n  \"contents\": [\n    {\n      \"role\": \"user\",\n      \"parts\": [\n        {\n          \"text\": $json.meta_prompt\n        },\n        { \n          \"file_data\": { \n            \"file_uri\": $('Config').item.json.youtube_url\n          } \n        }\n      ]\n    }\n  ],\n  \"generationConfig\": {\n    \"temperature\": 0.2,\n    \"topP\": 0.8,\n    \"topK\": 40,\n    \"maxOutputTokens\": 2048,\n  },\n  \"model\": \"gemini-1.5-flash\"\n}) }}\n", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "typeVersion": 4.2, "alwaysOutputData": true}, {"id": "8cd500b5-7c78-4ae0-be2a-79862e599da3", "name": "Compose Prompts", "type": "n8n-nodes-base.set", "position": [2760, 980], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "90bd636a-aa19-4f6b-80b3-bb236f29b317", "name": "content", "type": "string", "value": "=<default>\n<prompt>\nCreate a practical summary of this {{ $json.text.content_purpose }} about {{ $json.text.key_topics[0] }} for busy professionals in a {{ $json.text.video_tone }} tone seeking actionable takeaways. Use a structured format with primary and secondary bullets. Highlight specific tools, methodologies, and resources mentioned, including direct quotes when they provide valuable context.  Provide only the response and avoid any preamble text or further explanations.\n</prompt>\n<model>\ngemini-1.5-flash\n</model>\n</default>\n\n<transcribe>\n<prompt>\nAct as a professional transcriptionist and transcribe this {{ $json.text.video_type }} video verbatim. Include only spoken dialogue, maintaining speech patterns and verbal tics. Omit background sounds, music, or descriptions.  Provide only the response and avoid any preamble text or further explanations.\n</prompt>\n<model>\ngemini-1.5-flash\n</model>\n</transcribe>\n\n<timestamps>\n<prompt>\nCreate a professional timestamped transcript of this {{ $json.text.video_type }} video for {{ $json.text.primary_audience }}. Format each entry exactly as [hh:mm:ss] Dialogue. Capture speaker changes and significant pauses. Prioritize accuracy over completeness.  Provide only the response and avoid any preamble text or further explanations.\n</prompt>\n<model>\ngemini-1.5-flash\n</model>\n</timestamps>\n\n<summary>\n<prompt>\nAnalyze this {{ $json.text.video_type }} video and create a concise summary (approximately 150 words) for {{ $json.text.primary_audience }}. Use nested bullets to organize key points. Include direct quotes only when they significantly enhance understanding. Begin immediately with the content.  Provide only the response and avoid any preamble text or further explanations.\n</prompt>\n<model>\ngemini-1.5-flash\n</model>\n</summary>\n\n<scene>\n<prompt>\nAs a professional video production analyst, describe this scene comprehensively for {{ $json.text.content_purpose }}. Focus on setting, objects, people, lighting, colors, and camera techniques that contribute most to the scene's impact. Be specific with visual details that would matter to {{ $json.text.primary_audience }}.  Provide only the response and avoid any preamble text or further explanations.\n</prompt>\n<model>\ngemini-1.5-flash\n</model>\n</scene>\n\n<clips>\n<prompt>\nIdentify 3-5 high-engagement segments from this video specifically for {{ $json.text.best_social_platforms }} users interested in {{ $json.text.key_topics }}. For each clip, provide exact timestamps [hh:mm:ss-hh:mm:ss], verbatim transcript, and a compelling rationale focused on virality potential (shares, comments, saves).  Provide only the response and avoid any preamble text or further explanations.\n</prompt>\n<model>\ngemini-1.5-flash\n</model>\n</clips>\n\n\n\n\n"}]}}, "typeVersion": 3.4}, {"id": "8f80f2f1-c46c-45ef-8468-0eb7dda2814e", "name": "Extract MetaData Object", "type": "n8n-nodes-base.set", "position": [3780, 540], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "e1a2e48b-0190-4f13-bf3f-8e74cbc8ab65", "name": "text", "type": "object", "value": "={{ $json.candidates[0].content.parts[0].text.replaceAll('```json', '').replaceAll('```', '') }}"}]}}, "typeVersion": 3.4}, {"id": "b1065050-1a32-423e-b15f-0cef3f377ae6", "name": "Get Prompt by Prompt Type", "type": "n8n-nodes-base.code", "position": [3100, 980], "parameters": {"jsCode": "// Get the XML content from the input\nconst xmlContent = $input.first().json.content;\n\n// Get the tag name from the Config node\nconst tagName = $node[\"Config\"].json.prompt_type;\n\n// Create regex patterns for both prompt and model within the main tag\nconst promptRegex = new RegExp(`<${tagName}>[\\\\s\\\\S]*?<prompt>([\\\\s\\\\S]*?)</prompt>[\\\\s\\\\S]*?</${tagName}>`, \"i\");\nconst modelRegex = new RegExp(`<${tagName}>[\\\\s\\\\S]*?<model>([\\\\s\\\\S]*?)</model>[\\\\s\\\\S]*?</${tagName}>`, \"i\");\n\n// Use the match method to apply the regex patterns\nconst promptMatch = xmlContent.match(promptRegex);\nconst modelMatch = xmlContent.match(modelRegex);\n\n// Create the output item with proper structure\nlet outputItem = {\n  json: {\n    prompt: null,\n    model: null\n  }\n};\n\n// Extract prompt content if found\nif (promptMatch) {\n  outputItem.json.prompt = promptMatch[1].trim();\n}\n\n// Extract model content if found\nif (modelMatch) {\n  outputItem.json.model = modelMatch[1].trim();\n}\n\n// Return the properly structured item\nreturn [outputItem];\n"}, "typeVersion": 2}, {"id": "a66e5240-ad86-47df-8a23-d45eb31e41ce", "name": "Define Audience Meta Prompt", "type": "n8n-nodes-base.set", "position": [3100, 540], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c3524064-c7fb-4f63-8421-f18f35cf5556", "name": "meta_prompt", "type": "string", "value": "=Analyze this YouTube video and extract key metadata to help optimize AI-generated content about it. Return ONLY a valid JSON object with the following fields:\n\n{\n  \"video_type\": \"The video format/genre (tutorial, vlog, review, interview, etc.)\",\n  \"primary_audience\": \"The main target audience based on content, language, and presentation style\",\n  \"secondary_audiences\": [\"List of 2-3 other potential audience segments\"],\n  \"content_purpose\": \"The main goal of the video (educate, entertain, persuade, etc.)\",\n  \"key_topics\": [\"3-5 main topics or themes covered\"],\n  \"best_social_platforms\": [\"2-3 platforms where clips would perform best\"],\n  \"video_tone\": \"Overall tone (professional, casual, humorous, serious, etc.)\",\n  \"engagement_drivers\": [\"2-3 aspects that would drive viewer engagement\"]\n}\n\nFocus on objective analysis of visual and verbal cues. Do not include subjective quality assessments.\n\nReturn your response as a valid JSON object without any markdown formatting, code blocks, or explanatory text.  Always remove all ```json and ``` from final response.  Avoid all preamble or further explanation.\n"}]}}, "typeVersion": 3.4}, {"id": "cdb4ec99-37ac-45ae-9d5c-80851e992488", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [3340, 420], "parameters": {"color": 3, "width": 300, "height": 300, "content": "## Analyze YouTube Video for Audience MetaData"}, "typeVersion": 1}, {"id": "904938e4-4242-4a90-b124-fe0ba10ee4ec", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [3340, 860], "parameters": {"color": 3, "width": 300, "height": 300, "content": "## Get YouTube Information by Prompt Type"}, "typeVersion": 1}, {"id": "942789bd-ab7c-432c-9916-f1fdb5344e1e", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [3000, 420], "parameters": {"color": 7, "width": 300, "height": 300, "content": "## Define Audience Meta Prompt"}, "typeVersion": 1}, {"id": "4c032c02-3eb7-48d1-8a74-75db0e02fe24", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [3680, 420], "parameters": {"color": 7, "width": 300, "height": 300, "content": "## Extract MetaData Object"}, "typeVersion": 1}, {"id": "58e23d26-8cb1-4819-9d29-0658e8b7a95b", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [2660, 860], "parameters": {"color": 7, "width": 300, "height": 300, "content": "## Compose the Prompts with Audience MetaData"}, "typeVersion": 1}, {"id": "ecadd3a9-7ce4-433d-9a04-76ebe4ba3875", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [3000, 860], "parameters": {"color": 7, "width": 300, "height": 300, "content": "## Get Prompt by Prompt Type"}, "typeVersion": 1}, {"id": "a00048cb-e30c-4b14-9dd3-b986d2ee5f9c", "name": "Get YouTube Information by Prompt Type", "type": "n8n-nodes-base.httpRequest", "onError": "continueRegularOutput", "position": [3440, 980], "parameters": {"url": "=https://generativelanguage.googleapis.com/v1beta/models/{{ $json.model }}:generateContent?key={{$('Config').item.json.google_api_key }}", "method": "POST", "options": {}, "jsonBody": "={\n  \"contents\": [{\n    \"parts\": [\n      { \"text\": {{ JSON.stringify($json.prompt) }} },\n      { \"file_data\": { \n          \"file_uri\": \"{{ $('Config').item.json.youtube_url }}\" \n        } \n      }\n    ]\n  }]\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "typeVersion": 4.2, "alwaysOutputData": true}, {"id": "a071b040-9091-4081-aedd-d8e8b9166568", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [2320, 420], "parameters": {"color": 4, "width": 300, "height": 300, "content": "## 👍Try Me!\nYouTube Video Id: wBuULAoJxok"}, "typeVersion": 1}, {"id": "e243fe41-26fe-48d3-b215-20e033a0c0aa", "name": "Save to Google Drive as Text File", "type": "n8n-nodes-base.googleDrive", "position": [3780, 1320], "parameters": {"name": "={{ $('Start Workflow').item.json['YouTube Video Id'] }} - {{ $now }}", "content": "={{ $('Start Workflow').item.json['YouTube Video Id'] }} - {{ $now }}\n\n{{ $('Extract MetaData Object').item.json.text.key_topics[0] }}\n{{ $('Extract MetaData Object').item.json.text.content_purpose }}\n{{ $('Extract MetaData Object').item.json.text.primary_audience }}\n\n{{ $json.candidates[0].content.parts[0].text }}\n\nVideo Details:\n{{ $('Merge').item.json.items.toJsonString() }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "root", "cachedResultName": "/ (Root folder)"}, "operation": "createFromText"}, "credentials": {"googleDriveOAuth2Api": {"id": "UhdXGYLTAJbsa0xX", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "87ad7860-a364-406a-999b-5b9f9ef356e0", "name": "Send to Gmail as HTML", "type": "n8n-nodes-base.gmail", "position": [4120, 1320], "webhookId": "ccf34c87-14a3-4103-96fb-595cf9fa0636", "parameters": {"sendTo": "={{ $env.EMAIL_ADDRESS_JOE }}", "message": "=<p>{{ $('Merge').item.json.items[0].snippet.title }}</p>\n<p>{{ $('Merge').item.json.items[0].id }}</p>\n\n<img src=\"{{ $('Merge').item.json.items[0].snippet.thumbnails.medium.url }}\">\n\n{{ $json.data }}", "options": {"appendAttribution": false}, "subject": "={{ $('Start Workflow').item.json['YouTube Video Id'] }} - {{ $('Extract MetaData Object').item.json.text.key_topics[0] }}"}, "credentials": {"gmailOAuth2": {"id": "1xpVDEQ1yx8gV022", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "d42e0de6-560e-4aa0-b2a5-8b79d84b660a", "name": "Convert Markdown to HTML", "type": "n8n-nodes-base.markdown", "position": [3780, 980], "parameters": {"mode": "markdownToHtml", "options": {}, "markdown": "={{ $json.candidates[0].content.parts[0].text }}"}, "typeVersion": 1}, {"id": "3f5cac85-ee4d-45a5-9a95-07fc6e195bd8", "name": "Provide YouTube Information to User as HTML", "type": "n8n-nodes-base.form", "position": [4120, 980], "webhookId": "49b5f9c9-e4c2-4cc4-b01c-c27b1cdba918", "parameters": {"operation": "completion", "respondWith": "showText", "responseText": "=<img src=\"{{ $('Merge').item.json.items[0].snippet.thumbnails.medium.url }}\">\n\n{{ $json.data }}\n"}, "typeVersion": 1}, {"id": "8f0ed7d9-9b78-49d2-858a-34418e1ee517", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [3340, 320], "parameters": {"color": 5, "width": 300, "height": 100, "content": "## Google Generative Language API"}, "typeVersion": 1}, {"id": "271816a9-9e1a-4b4d-afe5-94f3023c9337", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [3340, 760], "parameters": {"color": 5, "width": 300, "height": 100, "content": "## Google Generative Language API"}, "typeVersion": 1}, {"id": "ba68bd32-0f4b-4ce0-9af6-3dc87b8ae5ea", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [3680, 860], "parameters": {"color": 7, "width": 300, "height": 300, "content": "## Convert Markdown to HTML"}, "typeVersion": 1}, {"id": "b69d97a9-748e-430a-b1af-5befecb226a3", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [3680, 1200], "parameters": {"color": 7, "width": 300, "height": 300, "content": "## Save YouTube Information to Google Drive"}, "typeVersion": 1}, {"id": "3a0bca9c-56da-4425-b719-3e2ccb1cd1d8", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [4020, 1200], "parameters": {"color": 7, "width": 300, "height": 300, "content": "## Email YouTube Information"}, "typeVersion": 1}, {"id": "004154f2-ac1a-4b79-a5f8-3af0959cc3ce", "name": "Sticky Note14", "type": "n8n-nodes-base.stickyNote", "position": [4020, 860], "parameters": {"color": 4, "width": 300, "height": 300, "content": "## Provide YouTube Information in Completion Form"}, "typeVersion": 1}, {"id": "f815a2f5-2e12-40bf-8849-30429344afae", "name": "Sticky Note15", "type": "n8n-nodes-base.stickyNote", "position": [2280, -120], "parameters": {"color": 7, "width": 2080, "height": 1660, "content": "# 🎥 Analyze YouTube Video for Summaries, Transcripts & Content + Google Gemini"}, "typeVersion": 1}, {"id": "dbbae73b-735b-4bb8-bcad-6266c08d9fae", "name": "Start Workflow", "type": "n8n-nodes-base.formTrigger", "position": [2420, 540], "webhookId": "92148b0b-bbf7-4ce9-80a2-768207adee7b", "parameters": {"options": {}, "formTitle": "Extract Information from YouTube Videos", "formFields": {"values": [{"fieldType": "dropdown", "fieldLabel": "Prompt Type", "fieldOptions": {"values": [{"option": "default"}, {"option": "transcribe"}, {"option": "timestamps"}, {"option": "summary"}, {"option": "scene"}, {"option": "clips"}]}, "requiredField": true}, {"fieldLabel": "YouTube Video Id", "placeholder": "wBuULAoJxok", "requiredField": true}]}, "responseMode": "lastNode", "formDescription": "This workflow allows you to extract various types of actionable information from YouTube videos that is audience specific using dynamically composed prompts."}, "typeVersion": 2.2}, {"id": "c63d236c-99d5-43f6-825e-836ddd41ad6f", "name": "Create YouTube API URL", "type": "n8n-nodes-base.code", "position": [3100, 100], "parameters": {"jsCode": "// Define the base URL for the YouTube Data API\nconst BASE_URL = 'https://www.googleapis.com/youtube/v3/videos';\n\n// Get the first input item\nconst item = $input.first();\n\n// Extract the videoId and google_api_key from the input JSON\nconst VIDEO_ID = item.json.video_id;\nconst GOOGLE_API_KEY = item.json.google_api_key; // Dynamically retrieve API key\n\nif (!VIDEO_ID) {\n  throw new Error('The video ID parameter is empty.');\n}\n\nif (!GOOGLE_API_KEY) {\n  throw new Error('The Google API Key is missing.');\n}\n\n// Construct the API URL with the video ID and dynamically retrieved API key\nconst youtubeUrl = `${BASE_URL}?part=snippet,contentDetails,status,statistics,player,topicDetails&id=${VIDEO_ID}&key=${GOOGLE_API_KEY}`;\n\n// Return the constructed URL\nreturn [\n  {\n    json: {\n      youtubeUrl: youtubeUrl,\n    },\n  },\n];\n"}, "typeVersion": 2}, {"id": "17daf9d1-4bee-4632-b929-0696e71b9fa2", "name": "Get YouTube Video Details", "type": "n8n-nodes-base.httpRequest", "position": [3440, 100], "parameters": {"url": "={{ $json.youtubeUrl }}", "options": {}}, "typeVersion": 4.2}, {"id": "42b45f4c-9447-4ffa-ae7f-ffa68de395ba", "name": "Sticky Note16", "type": "n8n-nodes-base.stickyNote", "position": [3340, -20], "parameters": {"color": 3, "width": 300, "height": 300, "content": "## Get YouTube Video Details"}, "typeVersion": 1}, {"id": "3f3a5e5a-5c15-42a0-81d5-53248b76495e", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [4100, 540], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3}, {"id": "377870dd-7dfe-49dc-a444-67017a97e8c8", "name": "Sticky Note17", "type": "n8n-nodes-base.stickyNote", "position": [3000, -20], "parameters": {"color": 7, "width": 300, "height": 300, "content": "## Create YouTube API URL"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "45041f00-7c30-4490-aa2b-807bcb91ca2b", "connections": {"Merge": {"main": [[{"node": "Compose Prompts", "type": "main", "index": 0}]]}, "Config": {"main": [[{"node": "Create YouTube API URL", "type": "main", "index": 0}, {"node": "Define Audience Meta Prompt", "type": "main", "index": 0}]]}, "Start Workflow": {"main": [[{"node": "Config", "type": "main", "index": 0}]]}, "Compose Prompts": {"main": [[{"node": "Get Prompt by Prompt Type", "type": "main", "index": 0}]]}, "Create YouTube API URL": {"main": [[{"node": "Get YouTube Video Details", "type": "main", "index": 0}]]}, "Extract MetaData Object": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Convert Markdown to HTML": {"main": [[{"node": "Send to Gmail as HTML", "type": "main", "index": 0}, {"node": "Provide YouTube Information to User as HTML", "type": "main", "index": 0}]]}, "Get Prompt by Prompt Type": {"main": [[{"node": "Get YouTube Information by Prompt Type", "type": "main", "index": 0}]]}, "Get YouTube Video Details": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Define Audience Meta Prompt": {"main": [[{"node": "Get Video Audience MetaData", "type": "main", "index": 0}]]}, "Get Video Audience MetaData": {"main": [[{"node": "Extract MetaData Object", "type": "main", "index": 0}]]}, "Get YouTube Information by Prompt Type": {"main": [[{"node": "Convert Markdown to HTML", "type": "main", "index": 0}, {"node": "Save to Google Drive as Text File", "type": "main", "index": 0}]]}}}