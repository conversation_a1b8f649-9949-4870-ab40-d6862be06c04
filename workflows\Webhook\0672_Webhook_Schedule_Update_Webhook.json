{"nodes": [{"id": "25a059ad-c3d1-4848-a729-cbb50254e94a", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [40, 980], "parameters": {}, "typeVersion": 1}, {"id": "3ae8469e-cbb4-436a-b5c2-2e6a146c5666", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-40, 600], "parameters": {"color": 7, "width": 2160, "height": 1540, "content": "# Try me out !\n## Dummy Ugly Workflow\n---\nTry mixing it up of changing some connections to see how this workflow gets positionned !\n\n1. **Save this workfow** (Ctrl + S)\n2. **Execute the Magic Positioning Node**\n3. **Reload the page** (Ctrl + R)\n..watch the magic !"}, "typeVersion": 1}, {"id": "4a67e81f-1638-4047-b9e7-85247f4cc291", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [480, 1420], "parameters": {"options": {}}, "typeVersion": 1.2}, {"id": "56293367-e676-44d6-ac05-8432c8181299", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [420, 1660], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "3", "name": "Together.ai (lucas.photos)"}}, "typeVersion": 1}, {"id": "cacdff1d-f65d-40f3-b0b5-9913a8e249ed", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [880, 1760], "parameters": {}, "typeVersion": 1.3}, {"id": "9ccd8613-ded7-421a-bf2d-95e6465d9a34", "name": "Vector Store Retriever", "type": "@n8n/n8n-nodes-langchain.retrieverVectorStore", "position": [1780, 1800], "parameters": {}, "typeVersion": 1}, {"id": "cc5df13b-2d2e-4c59-b3e3-dfbcbffcfdf9", "name": "In-Memory Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStoreInMemory", "position": [1060, 1880], "parameters": {}, "typeVersion": 1}, {"id": "8c71b8fc-0699-4000-8021-fab3529690c6", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [1660, 1940], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "3", "name": "Together.ai (lucas.photos)"}}, "typeVersion": 1.1}, {"id": "83bbca90-f9aa-4aae-9f1b-68d7eb1e7272", "name": "Question and Answer Chain", "type": "@n8n/n8n-nodes-langchain.chainRetrievalQa", "position": [760, 1540], "parameters": {"options": {}}, "typeVersion": 1.4}, {"id": "6d124c93-a476-4b54-ad65-391eaf948605", "name": "Switch", "type": "n8n-nodes-base.switch", "position": [1360, 1220], "parameters": {"rules": {"values": [{"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "equals"}, "leftValue": "", "rightValue": ""}]}}, {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "5f1ec5b3-385c-4421-9791-a612f61cc634", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "", "rightValue": ""}]}}, {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "737211eb-e4e2-4bb2-a32b-a6d819e158ba", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "", "rightValue": ""}]}}, {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "3a420a92-cd22-46d4-b2fa-1dffa6b28374", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "", "rightValue": ""}]}}, {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "29c0fce3-aefb-4caf-a076-a548c108b641", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "", "rightValue": ""}]}}]}, "options": {}}, "typeVersion": 3.2}, {"id": "886142ac-822e-4e25-875c-65632f682140", "name": "IF", "type": "n8n-nodes-base.if", "position": [440, 1480], "parameters": {}, "typeVersion": 1}, {"id": "3a247df2-ea43-40f0-a395-0ce160fcbc92", "name": "Dummy <PERSON>", "type": "n8n-nodes-base.noOp", "notes": "Big description of what happens here", "position": [1360, 1520], "parameters": {}, "notesInFlow": true, "typeVersion": 1}, {"id": "53e46b03-026a-4645-b9c7-e913eea62fe9", "name": "<PERSON><PERSON> (1)", "type": "n8n-nodes-base.noOp", "notes": "Big description of what happens here", "position": [700, 900], "parameters": {}, "notesInFlow": true, "typeVersion": 1}, {"id": "0d9a5f0a-e224-41b9-8ef0-4ba16e71c237", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [920, 980], "parameters": {"options": {}, "batchSize": "=1"}, "typeVersion": 3}, {"id": "2442eccb-8f95-4c2d-ae93-7e216e93e7f4", "name": "<PERSON><PERSON> (2)", "type": "n8n-nodes-base.noOp", "notes": "Big description of what happens here", "position": [1140, 940], "parameters": {}, "notesInFlow": true, "typeVersion": 1}, {"id": "2de8782c-f848-44cc-87b1-307506cdca38", "name": "<PERSON><PERSON> (3)", "type": "n8n-nodes-base.noOp", "notes": "Big description of what happens here", "position": [700, 1200], "parameters": {}, "notesInFlow": true, "typeVersion": 1}, {"id": "f1bb16c1-b330-4208-b629-5a6e074b9178", "name": "<PERSON><PERSON> (4)", "type": "n8n-nodes-base.noOp", "notes": "Big description of what happens here", "position": [900, 1460], "parameters": {}, "notesInFlow": true, "typeVersion": 1}, {"id": "93c393c5-d258-431e-ba22-c7de7f6560f1", "name": "<PERSON><PERSON> (5)", "type": "n8n-nodes-base.noOp", "notes": "Big description of what happens here", "position": [1140, 1200], "parameters": {}, "notesInFlow": true, "typeVersion": 1}, {"id": "c55e1584-4bab-4406-9b6c-a7ba96828c4b", "name": "<PERSON><PERSON> (6)", "type": "n8n-nodes-base.noOp", "notes": "Big description of what happens here", "position": [1580, 980], "parameters": {}, "notesInFlow": true, "typeVersion": 1}, {"id": "612a7a10-242e-4bd0-b4bd-6089e1fcd78b", "name": "<PERSON><PERSON> (7)", "type": "n8n-nodes-base.noOp", "notes": "Big description of what happens here", "position": [1780, 1300], "parameters": {}, "notesInFlow": true, "typeVersion": 1}, {"id": "eb5d2519-2f1e-4841-b8e8-58333cf9293d", "name": "<PERSON><PERSON> (8)", "type": "n8n-nodes-base.noOp", "notes": "Big description of what happens here", "position": [1340, 1720], "parameters": {}, "notesInFlow": true, "typeVersion": 1}, {"id": "661d3849-155b-4911-b923-4cd2a4227202", "name": "<PERSON><PERSON> (9)", "type": "n8n-nodes-base.noOp", "notes": "Big description of what happens here", "position": [1580, 1640], "parameters": {}, "notesInFlow": true, "typeVersion": 1}, {"id": "af2cca30-302d-47e1-bebc-3f6a92cef939", "name": "Dummy <PERSON>", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [720, 1660], "parameters": {"url": "https://www.example.com"}, "typeVersion": 1.1}, {"id": "7dded6b7-c60a-45e4-a49f-338bf4b549b8", "name": "<PERSON><PERSON> (1)", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [680, 1760], "parameters": {"url": "https://www.example.com"}, "typeVersion": 1.1}, {"id": "d4f0d637-abda-4e79-ae6b-7af7050d6768", "name": "OpenAI Chat Model (1)", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1360, 1880], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "3", "name": "Together.ai (lucas.photos)"}}, "typeVersion": 1}, {"id": "e5ea3f5b-cd91-49b6-9191-f60a0d19bf40", "name": "Update n8n Workflow", "type": "n8n-nodes-base.n8n", "position": [700, 380], "parameters": {"operation": "update", "workflowId": {"__rl": true, "mode": "id", "value": "={{ $('POST /workflow/magic/position/id').last().json.body.workflow_id }}"}, "requestOptions": {}, "workflowObject": "={{ $json.toJsonString() }}"}, "credentials": {"n8nApi": {"id": "10", "name": "n8n account"}}, "typeVersion": 1}, {"id": "5df2795d-f1fa-437f-9444-92a0ec4003da", "name": "Magic Positioning IA2S", "type": "n8n-nodes-base.httpRequest", "position": [480, 380], "parameters": {"url": "https://api.ia2s.app/webhook/workflow/magic/position", "method": "POST", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "workflow", "value": "={{ $json }}"}]}}, "typeVersion": 4.2}, {"id": "f6820c3e-d0ec-45ef-92ce-b6a7466997fb", "name": "POST /workflow/magic/position/id", "type": "n8n-nodes-base.webhook", "position": [40, 380], "webhookId": "3f637a82-df5e-4580-b1af-81ebec0b345a", "parameters": {"path": "workflow/magic/positioning/id", "options": {}, "httpMethod": "POST", "responseMode": "responseNode"}, "typeVersion": 2}, {"id": "f0a4176b-1fa4-4884-8a51-ecc00af7d246", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-40, -60], "parameters": {"color": 6, "width": 300, "height": 380, "content": "## Put this node in any workflow.\n1. **Save the workfow** (Ctrl + S)\n2. **Execute the Magic Positioning Node**\n3. **Reload the page** (Ctrl + R)\n..and voilà !"}, "typeVersion": 1}, {"id": "20d8af29-e07a-4205-a7b6-223b2cdb801a", "name": "Get n8n Workflow", "type": "n8n-nodes-base.n8n", "position": [260, 380], "parameters": {"operation": "get", "workflowId": {"__rl": true, "mode": "id", "value": "={{ $json.body.workflow_id }}"}, "requestOptions": {}}, "credentials": {"n8nApi": {"id": "10", "name": "n8n account"}}, "typeVersion": 1}, {"id": "468a95e2-11bc-4bf6-be8a-4eb5f89654ef", "name": "Simple Webhook Response", "type": "n8n-nodes-base.respondToWebhook", "position": [920, 380], "parameters": {"options": {}, "respondWith": "text", "responseBody": "Workflow Updated"}, "typeVersion": 1.1}, {"id": "496ba599-cf72-4ba1-8e50-2e369f199b6f", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [80, 1260], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.2}, {"id": "107e6cb3-673b-4554-b42c-c9d2d7a00ce9", "name": "Magic Positioning", "type": "n8n-nodes-base.httpRequest", "position": [40, 160], "parameters": {"url": "=https://{{ \"n8n.your-instance-url.com\" }}/webhook/workflow/magic/positioning/id", "method": "POST", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "workflow_id", "value": "={{ $workflow.id }}"}]}}, "typeVersion": 4.2}, {"id": "55e8a9fc-1699-4890-b73e-a6201259a559", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [300, -60], "parameters": {"color": 5, "width": 400, "height": 380, "content": "# Setup :\n---\n\n1. **Open the Webhook node** \n('Post /workflow/magic/position/id')\n2. Copy the **Production URL**\n3. Go to the **'Magic Positioning' Http Request** Node\n4. **Paste the URL**\n5. Select your **n8n credentials** in the n8n nodes"}, "typeVersion": 1}], "pinData": {"POST /workflow/magic/position/id": [{"body": {"workflow_id": "zwa7VqGx8GrqsPhb"}, "query": {}, "params": {}, "headers": {"host": "api.ia2s.app", "accept": "application/json,text/html,application/xhtml+xml,application/xml,text/*;q=0.9, image/*;q=0.8, */*;q=0.7", "user-agent": "axios/1.7.4", "content-type": "application/json", "content-length": "34", "accept-encoding": "gzip, compress, deflate, br", "x-forwarded-for": "**********", "x-forwarded-host": "api.ia2s.app", "x-forwarded-proto": "https"}, "webhookUrl": "https://api.ia2s.app/webhook/workflow/magic/positioning/id", "executionMode": "production"}]}, "connections": {"IF": {"main": [[{"node": "Dummy <PERSON>", "type": "main", "index": 0}], [{"node": "AI Agent", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "<PERSON><PERSON> (6)", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON> (7)", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON> (8)", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON> (9)", "type": "main", "index": 0}], [{"node": "Question and Answer Chain", "type": "main", "index": 0}]]}, "Dummy Node": {"main": [[{"node": "<PERSON><PERSON> (1)", "type": "main", "index": 0}, {"node": "<PERSON><PERSON> (3)", "type": "main", "index": 0}]]}, "Dummy Tool": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Dummy Node (1)": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Dummy Node (2)": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Dummy Node (3)": {"main": [[{"node": "<PERSON><PERSON> (4)", "type": "main", "index": 0}]]}, "Dummy Node (5)": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Dummy Tool (1)": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "<PERSON><PERSON> (2)", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON> (5)", "type": "main", "index": 0}]]}, "Get n8n Workflow": {"main": [[{"node": "Magic Positioning IA2S", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "In-Memory Vector Store", "type": "ai_embedding", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Update n8n Workflow": {"main": [[{"node": "Simple Webhook Response", "type": "main", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "OpenAI Chat Model (1)": {"ai_languageModel": [[{"node": "Question and Answer Chain", "type": "ai_languageModel", "index": 0}]]}, "In-Memory Vector Store": {"ai_vectorStore": [[{"node": "Vector Store Retriever", "type": "ai_vectorStore", "index": 0}]]}, "Magic Positioning IA2S": {"main": [[{"node": "Update n8n Workflow", "type": "main", "index": 0}]]}, "Vector Store Retriever": {"ai_retriever": [[{"node": "Question and Answer Chain", "type": "ai_retriever", "index": 0}]]}, "POST /workflow/magic/position/id": {"main": [[{"node": "Get n8n Workflow", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}}}