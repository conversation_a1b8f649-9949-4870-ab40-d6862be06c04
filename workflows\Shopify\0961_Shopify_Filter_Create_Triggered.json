{"id": "44PIIGwPzUe9dGfb", "meta": {"instanceId": "6b3e8c6c30cdfbf06283a3fa57016932c6b4ec959896c5c546ef5865ff697ff1"}, "name": "Sync New Shopify Products to Odoo Product", "tags": [], "nodes": [{"id": "5ad7f941-4146-4897-ad30-dcdccab85e82", "name": "Odoo6", "type": "n8n-nodes-base.odoo", "position": [320, 0], "parameters": {"limit": 1, "options": {}, "resource": "custom", "operation": "getAll", "filterRequest": {"filter": [{"value": "={{ $('Shopify Trigger').all()[0].json.variants[0].product_id }}", "fieldName": "default_code"}]}, "customResource": "product.product"}, "credentials": {"odooApi": {"id": "0qIK4Cq1BwOSbxT8", "name": "Odoo **************:8069"}}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "1b1a3753-e645-465c-8155-ad8c006f3e13", "name": "Filter2", "type": "n8n-nodes-base.filter", "position": [740, 0], "parameters": {"conditions": {"boolean": [{"value1": "={{ $json.existing }}"}]}}, "typeVersion": 1}, {"id": "5b388afc-de9a-4246-85a8-0ef4ec8ac0bc", "name": "Odoo7", "type": "n8n-nodes-base.odoo", "position": [920, 0], "parameters": {"resource": "custom", "customResource": "product.product", "fieldsToCreateOrUpdate": {"fields": [{"fieldName": "name", "fieldValue": "={{ $json.product_detail.title }}"}, {"fieldName": "default_code", "fieldValue": "={{ $json.product_detail.variants[0].product_id }}"}, {"fieldName": "description", "fieldValue": "={{ $json.product_detail.body_html }}"}, {"fieldName": "list_price", "fieldValue": "={{ $json.product_detail.variants[0].price }}"}]}}, "credentials": {"odooApi": {"id": "0qIK4Cq1BwOSbxT8", "name": "Odoo **************:8069"}}, "typeVersion": 1, "alwaysOutputData": false}, {"id": "765aeea5-bfe8-4d6c-96a4-ebbc192a9d60", "name": "Shopify Trigger", "type": "n8n-nodes-base.shopifyTrigger", "position": [80, 0], "webhookId": "30b89f06-e54c-4461-9e1e-9ef7f221e08b", "parameters": {"topic": "products/create", "authentication": "accessToken"}, "credentials": {"shopifyAccessTokenApi": {"id": "zkXzZzc97XyALfN8", "name": "Evozard - Shopify"}}, "typeVersion": 1}, {"id": "e1b2f842-0b54-4f55-9c69-a4d40777fd0c", "name": "Code", "type": "n8n-nodes-base.code", "position": [560, 0], "parameters": {"mode": "runOnceForEachItem", "jsCode": "var product_detail = $('Shopify Trigger').first().json\nconsole.log('-------product_detail--------',product_detail)\nvar existing_product = $('Odoo6').item.json\nreturn {existing:existing_product.id ? true:false,product_detail:product_detail}\n"}, "typeVersion": 2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "5dc6f917-daa8-4819-b8ff-1c46ab75b680", "connections": {"Code": {"main": [[{"node": "Filter2", "type": "main", "index": 0}]]}, "Odoo6": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Filter2": {"main": [[{"node": "Odoo7", "type": "main", "index": 0}]]}, "Shopify Trigger": {"main": [[{"node": "Odoo6", "type": "main", "index": 0}]]}}}