{"meta": {"instanceId": "257476b1ef58bf3cb6a46e65fac7ee34a53a5e1a8492d5c6e4da5f87c9b82833", "templateId": "2112"}, "nodes": [{"id": "99d9377f-263b-4deb-8450-6f9ca17d77c7", "name": "Send outreach email", "type": "n8n-nodes-base.gmail", "position": [1420, 320], "parameters": {"sendTo": "={{ $json.properties.email }}", "message": "={{ $json.html }}", "options": {"senderName": "<PERSON><PERSON><PERSON> from n8n", "appendAttribution": false}, "subject": "={{ $json.subject }}"}, "typeVersion": 2.1}, {"id": "aa2d7d84-66e1-4df3-9244-9a9182cd2eb7", "name": "Get uncontacted HubSpot contacts", "type": "n8n-nodes-base.hubspot", "position": [960, 540], "parameters": {"operation": "search", "authentication": "oAuth2", "filterGroupsUi": {"filterGroupsValues": [{"filtersUi": {"filterValues": [{"operator": "NOT_HAS_PROPERTY", "propertyName": "notes_last_contacted|datetime"}]}}]}, "additionalFields": {}}, "typeVersion": 2}, {"id": "cecf3de5-43d8-4d63-a557-adbd1d7d0e81", "name": "Every day at 9 am", "type": "n8n-nodes-base.scheduleTrigger", "position": [460, 540], "parameters": {"rule": {"interval": [{"triggerAtHour": 9}]}}, "typeVersion": 1.1}, {"id": "faa91fac-7a22-440d-8575-a9f6ef858641", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [820, 240], "parameters": {"width": 348.2877732355713, "height": 526.4585335073351, "content": "## Search for all contacts that last contact date for is unknown\n\n1. Setup Oauth2 creds using n8n docs\nhttps://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.hubspottrigger/\n\n### Be careful with scopes. Scopes must be exactly as defined in the n8n docs"}, "typeVersion": 1}, {"id": "edf7e39d-efc7-405c-a610-0b098f86de07", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1380, 560], "parameters": {"color": 3, "width": 289.74216745960825, "height": 402.1775107197669, "content": "## Record outreach in Hubspot\n\nOnce outreach is added, last contact date is updated and won't be contacted again\n"}, "typeVersion": 1}, {"id": "07dc70c8-bf11-4dbd-9f99-1dad8d233e70", "name": "Set keys", "type": "n8n-nodes-base.set", "position": [1200, 540], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "f3ecc873-2d60-4f2d-bc40-81f9379c725b", "name": "html", "type": "string", "value": "=Hey {{ $json.properties.firstname }},\n\nI'm with n8n, and we work with organizations like yours to empower you to automate away boring and difficult tasks with ease.\n\nCan you point me towards the right person on your team to chat with about this?\n\nCheers,\n\n<PERSON><PERSON><PERSON>"}, {"id": "9f4f5b68-984b-415e-a110-a35ded22dd41", "name": "subject", "type": "string", "value": "Why n8n?"}, {"id": "5362aa67-f3fa-4a6e-b6e8-4c50cc7a3192", "name": "to", "type": "string", "value": "={{ $json.properties.email }}"}, {"id": "5b11e503-868d-4fca-bb44-59bb44d597a8", "name": "id", "type": "string", "value": "={{ $json.id }}"}]}}, "typeVersion": 3.3}, {"id": "506b5b31-8aec-4f74-b194-474c9b09c3f1", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [380, 240], "parameters": {"color": 5, "width": 407.25356360335365, "height": 242.51175804432177, "content": "## Send outreach/cold email using Gmail to new Hubspot contacts\n\nThis workflow uses Gmail to send outreach emails to Hubspot contacts that have yet to contacted (usually unknown contacts), and records the engagement in Hubspot. "}, "typeVersion": 1}, {"id": "************************************", "name": "Record engagement in HubSpot", "type": "n8n-nodes-base.hubspot", "position": [1460, 760], "parameters": {"type": "email", "metadata": {"html": "={{ $json.html }}", "subject": "={{ $json.subject }}", "toEmail": ["={{ $json.to }}"], "firstName": "Mu<PERSON><PERSON>", "fromEmail": "<EMAIL>"}, "resource": "engagement", "authentication": "oAuth2", "additionalFields": {"associations": {"contactIds": "={{ $json.id }}"}}}, "typeVersion": 2}], "pinData": {}, "connections": {"Set keys": {"main": [[{"node": "Send outreach email", "type": "main", "index": 0}, {"node": "Record engagement in HubSpot", "type": "main", "index": 0}]]}, "Every day at 9 am": {"main": [[{"node": "Get uncontacted HubSpot contacts", "type": "main", "index": 0}]]}, "Get uncontacted HubSpot contacts": {"main": [[{"node": "Set keys", "type": "main", "index": 0}]]}}}