{"nodes": [{"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [560, 350], "parameters": {}, "typeVersion": 1}, {"name": "Pipedrive", "type": "n8n-nodes-base.pipedrive", "position": [760, 250], "parameters": {"resource": "person", "operation": "getAll", "returnAll": true, "additionalFields": {}}, "credentials": {"pipedriveApi": {"id": "28", "name": "pipedrive_api"}}, "typeVersion": 1}, {"name": "Update Pipedrive", "type": "n8n-nodes-base.pipedrive", "position": [1160, 250], "parameters": {"name": "={{$json[\"properties\"][\"firstname\"][\"value\"]}}", "resource": "person", "additionalFields": {"email": ["={{$json[\"identity-profiles\"][0][\"identities\"][0][\"value\"]}}"]}}, "credentials": {"pipedriveApi": {"id": "28", "name": "pipedrive_api"}}, "typeVersion": 1}, {"name": "HubSpot", "type": "n8n-nodes-base.hubspot", "position": [760, 450], "parameters": {"resource": "contact", "operation": "getAll", "returnAll": true, "additionalFields": {}}, "credentials": {"hubspotApi": {"id": "21", "name": "hubspot_account"}}, "typeVersion": 1}, {"name": "Update HubSpot", "type": "n8n-nodes-base.hubspot", "position": [1160, 450], "parameters": {"email": "={{$json[\"email\"][0][\"value\"]}}", "resource": "contact", "additionalFields": {"firstName": "={{$json[\"first_name\"]}}"}}, "credentials": {"hubspotApi": {"id": "21", "name": "hubspot_account"}}, "typeVersion": 1}, {"name": "Merge1", "type": "n8n-nodes-base.merge", "position": [960, 250], "parameters": {"mode": "removeKeyMatches", "propertyName1": "identity-profiles[0].identities[0].value", "propertyName2": "email[0].value"}, "typeVersion": 1}, {"name": "Merge2", "type": "n8n-nodes-base.merge", "position": [960, 450], "parameters": {"mode": "removeKeyMatches", "propertyName1": "email[0].value", "propertyName2": "identity-profiles[0].identities[0].value"}, "typeVersion": 1}], "connections": {"Cron": {"main": [[{"node": "Pipedrive", "type": "main", "index": 0}, {"node": "HubSpot", "type": "main", "index": 0}]]}, "Merge1": {"main": [[{"node": "Update Pipedrive", "type": "main", "index": 0}]]}, "Merge2": {"main": [[{"node": "Update HubSpot", "type": "main", "index": 0}]]}, "HubSpot": {"main": [[{"node": "Merge1", "type": "main", "index": 0}, {"node": "Merge2", "type": "main", "index": 1}]]}, "Pipedrive": {"main": [[{"node": "Merge1", "type": "main", "index": 1}, {"node": "Merge2", "type": "main", "index": 0}]]}}}