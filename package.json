{"name": "n8n-workflow-docs", "version": "1.0.0", "description": "N8N Workflow Documentation System - Node.js Implementation", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "init": "node src/init-db.js", "index": "node src/index-workflows.js"}, "dependencies": {"chokidar": "^3.5.3", "commander": "^11.1.0", "compression": "^1.8.1", "cors": "^2.8.5", "express": "^4.21.2", "express-rate-limit": "^7.5.1", "fs-extra": "^11.3.0", "helmet": "^7.2.0", "sqlite3": "^5.1.7"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["n8n", "workflows", "documentation", "automation"], "author": "", "license": "MIT"}