{"meta": {"instanceId": "cb484ba7b742928a2048bf8829668bed5b5ad9787579adea888f05980292a4a7"}, "nodes": [{"id": "72c8c4a7-ee03-4e43-97db-f6fc8904e5e0", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.webhook", "position": [1100, 360], "webhookId": "e6d88547-5423-4b01-bc7f-e1f94274c4b2", "parameters": {"path": "e6d88547-5423-4b01-bc7f-e1f94274c4b2", "options": {}, "httpMethod": "POST"}, "typeVersion": 1}, {"id": "d1f3a8c8-d4af-452f-b4df-1e2dc73f7bd3", "name": "Hidden message to add bug details", "type": "n8n-nodes-base.httpRequest", "position": [1840, 360], "parameters": {"url": "={{ $('Bug Webhook').item.json.body.response_url }}", "method": "POST", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "=Thanks for adding the bug `{{$node[\"Bug Webhook\"].json[\"body\"][\"text\"]}}` <@{{$node[\"Bug Webhook\"].json[\"body\"][\"user_id\"]}}> :rocket: Please make sure to add a way to reproduce, expected behavior and current behavior.\n\n:point_right: <{{ $json[\"data\"][\"issueCreate\"][\"issue\"][\"url\"] }}|Add your details here>"}]}}, "typeVersion": 3}, {"id": "42977fb4-389f-4cef-855d-104f4cf0754f", "name": "Create linear issue", "type": "n8n-nodes-base.httpRequest", "position": [1660, 360], "parameters": {"url": "https://api.linear.app/graphql", "method": "POST", "options": {}, "jsonBody": "={\n    \"query\":\"mutation IssueCreate($input: IssueCreateInput!) {issueCreate(input: $input) {issue {id title url}}}\",\n    \"variables\":{\"input\":{\"title\":\"{{ $json[\"body\"][\"text\"].replaceAll('\"',\"'\") }}\",\"teamId\":\"7a330c36-4b39-4bf1-922e-b4ceeb91850a\", \"description\":\"## Description  \\n [Add a description here]  \\n## Expected  \\n [What behavior did you expect?]  \\n## Actual  \\n [What was the actual behavior? Use screenshots or videos to show the behavior]  \\n## Steps or workflow to reproduce (with screenshots/recordings)  \\n **n8n version:** [Deployment type] [version]  \\n 1. [Replace me]   \\n  \\n Created by: {{ $json[\"body\"][\"user_name\"].toSentenceCase() }}\", \"labelIds\": [\"f2b6e3e9-b42d-4106-821c-6a08dcb489a9\"]}} \n}", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "linearOAuth2Api"}, "credentials": {"linearOAuth2Api": {"id": "02MqKUMdPxr9t3mX", "name": "Nik's <PERSON><PERSON>"}}, "typeVersion": 3}, {"id": "ff733f62-3381-46c1-af9f-53d35f4b76ec", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [580, 140], "parameters": {"color": 7, "width": 446, "height": 321, "content": "## Needed pre-work: Add a Slack App\n1. Visit https://api.slack.com/apps, click on `New App` and choose a name and workspace.\n2. Click on `OAuth & Permissions` and scroll down to Scopes -> Bot token Scopes\n3. Add the `chat:write` scope\n4. Head over to `Slash Commands` and click on `Create New Command`\n5. Use `/bug` as the command\n6. Copy the test URL from the **Webhook** node into `Request URL`\n7. Add whatever feels best to the description and usage hint\n8. Go to `Install app` and click install"}, "typeVersion": 1}, {"id": "eca6f08d-fa8d-4ac7-a048-42ce839d3e01", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [580, 540], "parameters": {"color": 7, "width": 599.3676814988288, "height": 298.0562060889928, "content": "## Helper nodes\nRun these to find the IDs of your team and wanted labels"}, "typeVersion": 1}, {"id": "9d42e8ea-0f35-4c46-bb75-9c6a6123f4d5", "name": "Set me up", "type": "n8n-nodes-base.set", "position": [1380, 360], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "38e3a1ba-fd53-43f7-949d-427425727c7e", "name": "labelIds", "type": "array", "value": "[\"f2b6e3e9-b42d-4106-821c-6a08dcb489a9\"]"}, {"id": "3825e332-a905-48d3-ac9a-46b0ce3439f6", "name": "teamId", "type": "string", "value": "7a330c36-4b39-4bf1-922e-b4ceeb91850a"}]}}, "typeVersion": 3.3}, {"id": "b95148b2-17e0-444e-a642-a4319df9c4c5", "name": "Get all linear teams", "type": "n8n-nodes-base.httpRequest", "position": [634, 660], "parameters": {"url": "https://api.linear.app/graphql", "method": "POST", "options": {}, "sendBody": true, "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "query", "value": "{ teams { nodes { id name } } }"}]}, "nodeCredentialType": "linearOAuth2Api"}, "credentials": {"linearOAuth2Api": {"id": "02MqKUMdPxr9t3mX", "name": "Nik's <PERSON><PERSON>"}}, "typeVersion": 3}, {"id": "04ad2f49-ef78-4d08-ab6b-d0384aee5b80", "name": "Get linear labels for a team", "type": "n8n-nodes-base.httpRequest", "position": [1014, 660], "parameters": {"url": "https://api.linear.app/graphql", "method": "POST", "options": {}, "sendBody": true, "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "query", "value": "query { team(id: \"16de8638-2729-4245-b9f8-74daf4780cb3\") { labels { nodes { id name } } } }"}]}, "nodeCredentialType": "linearOAuth2Api"}, "credentials": {"linearOAuth2Api": {"id": "02MqKUMdPxr9t3mX", "name": "Nik's <PERSON><PERSON>"}}, "typeVersion": 3}, {"id": "4045dc92-4b9f-471c-8fb1-4d76942d0330", "name": "Set team ID", "type": "n8n-nodes-base.set", "position": [854, 660], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "25ed1c7d-e2c0-44b0-8b43-aa19122f6e88", "name": "teamId", "type": "string", "value": "38b31539-61e2-451c-ba06-ba8cf0d33650"}]}}, "typeVersion": 3.3}, {"id": "e45fe192-6846-41ad-ad75-699184486b6f", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1246.2295081967216, 164.12177985948486], "parameters": {"color": 5, "width": 372.78688524590143, "height": 358.12646370023407, "content": "## Setup\n1. Congifure your Slack bot using the sticky to the left\n2. Fill the `Set me up` node. You can find the IDs easily using the Helper nodes section\n3. Make sure to exchange the `Request URL` in your Slack with the Prod URL of the Webhook node before activating this workflow  "}, "typeVersion": 1}], "pinData": {"Bug Webhook": [{"body": {"text": "My bug", "token": "OROQZiopO3NiQVLFg0muEISq", "command": "/bug", "team_id": "TG9695PUK", "user_id": "U047V1J0E7J", "user_name": "<PERSON><PERSON><PERSON>", "api_app_id": "A06MQ8S7QM6", "channel_id": "C03600UUFSS", "trigger_id": "6716864450738.553213193971.0ef33a2db05a1d2dcf02c178d8efc534", "team_domain": "n8nio", "channel_name": "updates-workflow-templates", "response_url": "https://hooks.slack.com/commands/TG9695PUK/6713943368277/ogqoFMjMytSkbWNUdtg9Cp73", "is_enterprise_install": "false"}, "query": {}, "params": {}, "headers": {"host": "internal.users.n8n.cloud", "accept": "application/json,*/*", "x-real-ip": "**********", "user-agent": "Slackbot 1.0 (+https://api.slack.com/robots)", "content-type": "application/x-www-form-urlencoded", "content-length": "428", "accept-encoding": "gzip,deflate", "x-forwarded-for": "**********", "x-forwarded-host": "internal.users.n8n.cloud", "x-forwarded-port": "443", "x-forwarded-proto": "https", "x-slack-signature": "v0=dae629e837d8585faf0feffd1778020aa7a47dfe759def3088179a4a70cf31db", "x-forwarded-server": "3d9f11a36e52", "x-slack-request-timestamp": "1709135352"}}]}, "connections": {"Set me up": {"main": [[{"node": "Create linear issue", "type": "main", "index": 0}]]}, "Bug Webhook": {"main": [[{"node": "Set me up", "type": "main", "index": 0}]]}, "Set team ID": {"main": [[{"node": "Get linear labels for a team", "type": "main", "index": 0}]]}, "Create linear issue": {"main": [[{"node": "Hidden message to add bug details", "type": "main", "index": 0}]]}}}