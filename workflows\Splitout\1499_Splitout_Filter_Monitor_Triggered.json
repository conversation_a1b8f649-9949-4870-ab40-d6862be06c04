{"id": "I2qMAcQET7isaqYD", "meta": {"instanceId": "fddb3e91967f1012c95dd02bf5ad21f279fc44715f47a7a96a33433621caa253", "templateCredsSetupCompleted": true}, "name": "n8napi-check-workflow-which-model-is-using", "tags": [], "nodes": [{"id": "a027dc3c-b3a2-45f6-9126-dbec39f55b39", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-880, -40], "parameters": {}, "typeVersion": 1}, {"id": "0aafc781-6847-4b5d-8f80-3bd457f16db3", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [-220, -40], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "242d1965-d1e3-4b74-8064-53ea56118e94", "name": "Edit Fields-set_model_data", "type": "n8n-nodes-base.set", "position": [460, 80], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "3c08b3a3-092d-4f88-81ef-7a7b3acf47b2", "name": "node_name", "type": "string", "value": "={{ $json.name }}"}, {"id": "9b060fdb-f6a6-444a-a28d-deeacb21b3d3", "name": "model", "type": "string", "value": "={{ $json?.parameters?.model?.value || $json?.parameters?.model ||  $json?.parameters?.modelId?.cachedResultName }}"}, {"id": "848c0e23-0aa6-4cf5-8a64-abe38351b63a", "name": "workflow_name", "type": "string", "value": "={{ $('Loop Over Items').item.json.name }}"}, {"id": "cf3fea4e-4e22-4bd5-930b-6b8d25afbf9a", "name": "workflow_id", "type": "string", "value": "={{ $('Loop Over Items').item.json.id }}"}, {"id": "8a8a2a83-d742-4450-b5ed-2089047076d8", "name": "workflow_url", "type": "string", "value": "={Your-n8n-domain}/workflow/{{ $('Loop Over Items').item.json.id }}/{{ $json.id }}"}]}}, "typeVersion": 3.4}, {"id": "9693eb8b-4ce5-4d4b-984d-a77098896bc3", "name": "Google Sheets-Clear Sheet Data", "type": "n8n-nodes-base.googleSheets", "position": [-440, -220], "parameters": {"operation": "clear", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1iMh0C-Niu1ko4-u2BHo0cgGeVQKcYHflBzRxtbWJiRI/edit#gid=0", "cachedResultName": "data"}, "documentId": {"__rl": true, "mode": "list", "value": "1iMh0C-Niu1ko4-u2BHo0cgGeVQKcYHflBzRxtbWJiRI", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1iMh0C-Niu1ko4-u2BHo0cgGeVQKcYHflBzRxtbWJiRI/edit?usp=drivesdk", "cachedResultName": "n8n-check-workflow-use-what-ai-model"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "tufEzuSTEveV3tuA", "name": "(Personal)Google Sheets account"}}, "executeOnce": true, "typeVersion": 4.5, "alwaysOutputData": false}, {"id": "d325597e-b12f-427c-ba18-f69fa6ec9ed4", "name": "n8n-get all workflow", "type": "n8n-nodes-base.n8n", "position": [-660, -40], "parameters": {"filters": {}, "requestOptions": {}}, "credentials": {"n8nApi": {"id": "dXWG3XrAfEA64tjh", "name": "n8n account"}}, "typeVersion": 1}, {"id": "f8fba3ae-f4f3-4db3-bd0f-7caa84fd6cee", "name": "Filter-get workflow contain modelid", "type": "n8n-nodes-base.filter", "position": [-440, -40], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "f7433843-53c6-4e77-8f51-c70921342a0f", "operator": {"type": "string", "operation": "contains"}, "leftValue": "={{ $json.nodes.toJsonString() }}", "rightValue": "modelId"}, {"id": "8a9ff036-dc80-4b55-919b-e2dba22667cf", "operator": {"type": "string", "operation": "notEquals"}, "leftValue": "={{ $json.id }}", "rightValue": "={{ $workflow.id }}"}]}}, "typeVersion": 2.2}, {"id": "727dd95d-c788-4cae-8b25-4ffeff705579", "name": "Split Out-nodes", "type": "n8n-nodes-base.splitOut", "position": [-40, 80], "parameters": {"options": {}, "fieldToSplitOut": "nodes"}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "5a9d8012-a559-4c06-a3f9-be1a7b8f7ce6", "name": "Filter-node contain modelId", "type": "n8n-nodes-base.filter", "position": [180, 80], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "or", "conditions": [{"id": "5c06371f-9bc4-4fdd-bac2-9b9cdc28f77c", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.parameters.modelId.value.toString() }}", "rightValue": ""}, {"id": "6888c3a4-c988-48a1-aefc-d359f2ffeef5", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.parameters.model.toString() }}", "rightValue": ""}]}}, "typeVersion": 2.2, "alwaysOutputData": true}, {"id": "d4301765-8855-46fe-b2a2-06b03577a3b9", "name": "Google Sheets-Save node and workflow data", "type": "n8n-nodes-base.googleSheets", "position": [700, 80], "parameters": {"columns": {"value": {}, "schema": [{"id": "node_name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "node_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "modelId_value", "type": "string", "display": true, "removed": false, "required": false, "displayName": "modelId_value", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "modelId_name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "modelId_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "workflow_name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "workflow_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "workflow_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "workflow_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "workflow_url", "type": "string", "display": true, "removed": false, "required": false, "displayName": "workflow_url", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1iMh0C-Niu1ko4-u2BHo0cgGeVQKcYHflBzRxtbWJiRI/edit#gid=0", "cachedResultName": "data"}, "documentId": {"__rl": true, "mode": "list", "value": "1iMh0C-Niu1ko4-u2BHo0cgGeVQKcYHflBzRxtbWJiRI", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1iMh0C-Niu1ko4-u2BHo0cgGeVQKcYHflBzRxtbWJiRI/edit?usp=drivesdk", "cachedResultName": "n8n-check-workflow-use-what-ai-model"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "tufEzuSTEveV3tuA", "name": "(Personal)Google Sheets account"}}, "typeVersion": 4.5}, {"id": "78ae0f64-d6fa-46f6-a09f-e0a6bd6d21df", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [380, -40], "parameters": {"width": 260, "height": 320, "content": "## Change to your n8n domain here\n\n"}, "typeVersion": 1}, {"id": "2b8853d8-0436-4347-9c44-df45fcacfbd4", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-920, -160], "parameters": {"color": 3, "width": 420, "height": 320, "content": "## Be careful\nif you have more than 100 workflows. It might have performance issue.\n"}, "typeVersion": 1}, {"id": "611a6d7f-3955-43b5-b029-e738be2372cd", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-920, -440], "parameters": {"color": 7, "width": 340, "height": 240, "content": "## Created by da<PERSON>_tw_ \n\nAn engineer now focus on AI and Automation\n\n### contact me with following:\n[X](https://x.com/darrell_tw_)\n[Threads](https://www.threads.net/@darrell_tw_)\n[Instagram](https://www.instagram.com/darrell_tw_/)\n[Website](https://www.darrelltw.com/)"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1", "timeSavedPerExecution": 3}, "versionId": "30ea02b5-e1a3-4789-86a3-cdd937e2ce82", "connections": {"Loop Over Items": {"main": [[], [{"node": "Split Out-nodes", "type": "main", "index": 0}]]}, "Split Out-nodes": {"main": [[{"node": "Filter-node contain modelId", "type": "main", "index": 0}]]}, "n8n-get all workflow": {"main": [[{"node": "Filter-get workflow contain modelid", "type": "main", "index": 0}, {"node": "Google Sheets-Clear Sheet Data", "type": "main", "index": 0}]]}, "Edit Fields-set_model_data": {"main": [[{"node": "Google Sheets-Save node and workflow data", "type": "main", "index": 0}]]}, "Filter-node contain modelId": {"main": [[{"node": "Edit Fields-set_model_data", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "n8n-get all workflow", "type": "main", "index": 0}]]}, "Filter-get workflow contain modelid": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Google Sheets-Save node and workflow data": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}}}