{"\"meta\"": "{", "\"instanceId\"": "\"26ba763460b97c249b82942b23b6384876dfeb9327513332e743c5f6219c2b8e\"", "\"nodes\"": "[", "\"id\"": "\"39191834-ecc2-46f0-a31a-0a7e9c47ac5d\",", "\"name\"": "\"Sticky Note8\",", "\"type\"": "\"ai_textSplitter\",", "\"position\"": "[", "\"parameters\"": "{", "\"typeVersion\"": "1", "\"url\"": "\"=http://qdrant:6333/collections/hello_fresh/points/recommend/groups\",", "\"options\"": "{},", "\"jsCode\"": "\"const pageData = JSON.parse($input.first().json.data)\\nreturn pageData.props.pageProps.ssrPayload.courses.slice(0, 10);\"", "\"trimValues\"": "false,", "\"cleanUpText\"": "true", "\"operation\"": "\"extractHtmlContent\",", "\"extractionValues\"": "{", "\"values\"": "[", "\"key\"": "\"instructions\",", "\"cssSelector\"": "\"[data-test-id=\\\"instructions\\\"]\",", "\"assignments\"": "[", "\"value\"": "\"hello_fresh\",", "\"credentials\"": "{", "\"mistralCloudApi\"": "{", "\"metadata\"": "{", "\"metadataValues\"": "[", "\"jsonData\"": "\"={{ $json.data }}\",", "\"jsonMode\"": "\"expressionData\"", "\"mode\"": "\"list\",", "\"combinationMode\"": "\"mergeByPosition\"", "\"webhookId\"": "\"e86d8ae4-3b0d-4c40-9d12-a11d6501a043\",", "\"skipSelectors\"": "\"img,a\"", "\"fields\"": "{", "\"stringValue\"": "\"={{ $now.year }}-W{{ $now.weekNumber }}\"", "\"schemaType\"": "\"manual\",", "\"workflowId\"": "\"={{ $workflow.id }}\",", "\"description\"": "\"Call this tool to get a recipe recommendation. Pass in the following params as a json object:\\n* positives - a description of what the user wants to cook. This could be ingredients, flavours, utensils available, number of diners, type of meal etc.\\n* negatives - a description of what the user wants to avoid in the recipe. This could be flavours to avoid, allergen considerations, conflicts with theme of meal etc.\",", "\"inputSchema\"": "\"{\\n\\\"type\\\": \\\"object\\\",\\n\\\"properties\\\": {\\n\\t\\\"positive\\\": {\\n\\t\\t\\\"type\\\": \\\"string\\\",\\n\\t\\t\\\"description\\\": \\\"a description of what the user wants to cook. This could be ingredients, flavours, utensils available, number of diners, type of meal etc.\\\"\\n\\t},\\n \\\"negative\\\": {\\n \\\"type\\\": \\\"string\\\",\\n \\\"description\\\": \\\"a description of what the user wants to avoid in the recipe. This could be flavours to avoid, allergen considerations, conflicts with theme of meal etc.\\\"\\n }\\n}\\n}\",", "\"specifyInputSchema\"": "true", "\"model\"": "\"mistral-large-2402\",", "\"amount\"": "1.1", "\"method\"": "\"POST\",", "\"sendBody\"": "true,", "\"authentication\"": "\"predefinedCredentialType\",", "\"bodyParameters\"": "{", "\"nodeCredentialType\"": "\"qdrantApi\"", "\"qdrantApi\"": "{", "\"language\"": "\"python\",", "\"pythonCode\"": "\"import sqlite3\\ncon = sqlite3.connect(\\\"hello_fresh_1.db\\\")\\n\\ncur = con.cursor()\\ncur.execute(\\\"CREATE TABLE IF NOT EXISTS recipes (id TEXT PRIMARY KEY, name TEXT, data TEXT, cuisine TEXT, category TEXT, tag TEXT, week TEXT);\\\")\\n\\nfor item in _input.all():\\n cur.execute('INSERT OR REPLACE INTO recipes VALUES(?,?,?,?,?,?,?)', (\\n item.json.id,\\n item.json.name,\\n item.json.data,\\n ','.join(item.json.cuisine),\\n item.json.category,\\n ','.join(item.json.tag),\\n item.json.week\\n ))\\n\\ncon.commit()\\ncon.close()\\n\\nreturn [{ \\\"affected_rows\\\": len(_input.all()) }]\"", "\"color\"": "7,", "\"width\"": "213.30551928619226,", "\"height\"": "332.38559808882246,", "\"content\"": "\"\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n### 🚨Configure Your Qdrant Connection\\n* Be sure to enter your endpoint address\"", "\"systemMessage\"": "\"=You are a recipe bot for the company, \\\"Hello fresh\\\". You will help the user choose which Hello Fresh recipe to choose from this week's menu. The current week is {{ $now.year }}-W{{ $now.weekNumber }}.\\nDo not recommend any recipes other from the current week's menu. If there are no recipes to recommend, please ask the user to visit the website instead https://hellofresh.com.\"", "\"qdrantCollection\"": "{", "\"__rl\"": "true,", "\"cachedResultName\"": "\"hello_fresh\"", "\"pinData\"": "{},", "\"connections\"": "{", "\"Get Recipe\"": "{", "\"main\"": "[", "\"node\"": "\"Default Data Loader\",", "\"index\"": "0", "\"Chat Trigger\"": "{", "\"Prepare Documents\"": "{", "\"Default Data Loader\"": "{", "\"ai_document\"": "[", "\"Extract Server Data\"": "{", "\"Get Course Metadata\"": "{", "\"Get Recipes From DB\"": "{", "\"Get This Week's Menu\"": "{", "\"Qdrant Recommend API\"": "{", "\"ai_tool\"": "[", "\"Wait for Rate Limits\"": "{", "\"Merge Course & Recipe\"": "{", "\"Extract Recipe Details\"": "{", "\"Get Mistral Embeddings\"": "{", "\"Embeddings Mistral Cloud\"": "{", "\"ai_embedding\"": "[", "\"Execute Workflow Trigger\"": "{", "\"Mistral Cloud Chat Model\"": "{", "\"ai_languageModel\"": "[", "\"Use Qdrant Recommend API\"": "{", "\"Extract Available Courses\"": "{", "\"When clicking \\\"Test workflow\\\"\"": "{", "\"Recursive Character Text Splitter\"": "{", "\"ai_textSplitter\"": "["}