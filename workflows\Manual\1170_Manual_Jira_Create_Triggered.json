{"id": "87", "name": "Create a new issue in Jira", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [350, 300], "parameters": {}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.jira", "position": [550, 300], "parameters": {"project": "", "summary": "Firewall on fire", "issueType": "10001", "additionalFields": {}}, "credentials": {"jiraSoftwareCloudApi": ""}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"On clicking 'execute'": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}}}