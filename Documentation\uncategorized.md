# Uncategorized - N8N Workflows

## Overview
This document catalogs the **Uncategorized** workflows from the n8n Community Workflows repository.

**Category:** Uncategorized  
**Total Workflows:** 876  
**Generated:** 2025-07-27  
**Source:** https://scan-might-updates-postage.trycloudflare.com/api

---

## Workflows

### Complete Guide to Setting Up and Generating TOTP Codes in n8n 🔐
**Filename:** `0002_Manual_Totp_Automation_Triggered.json`  
**Description:** Manual workflow that integrates with Totp for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Totp,  

---

### Bitwarden Automate
**Filename:** `0003_Bitwarden_Automate.json`  
**Description:** Manual workflow that integrates with Bitwarden for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Bitwarden,  

---

### Process
**Filename:** `0009_Process.json`  
**Description:** Manual workflow that for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** None  

---

### Writebinaryfile Create
**Filename:** `0010_Writebinaryfile_Create.json`  
**Description:** Manual workflow that integrates with Writebinaryfile to create new records. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Writebinaryfile,  

---

### Loading data into a spreadsheet
**Filename:** `0013_Manual_Noop_Import_Triggered.json`  
**Description:** Manual workflow that for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** None  

---

### Manual Filemaker Automate Triggered
**Filename:** `0032_Manual_Filemaker_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Filemaker for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Filemaker,  

---

### Code Filter Create Scheduled
**Filename:** `0034_Code_Filter_Create_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Nocodb, Splitinbatches, and Spotify to create new records. Uses 30 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** high (30 nodes)  
**Integrations:** Nocodb,Splitinbatches,Spotify,  

---

### Calendly Notion Automate Triggered
**Filename:** `0039_Calendly_Notion_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that connects Cal.com and Notion for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** Cal.com,Notion,  

---

### Humanticai Calendly Automate Triggered
**Filename:** `0043_Humanticai_Calendly_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Cal.com, Notion, and Humanticai for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Cal.com,Notion,Humanticai,  

---

### Uptimerobot Automate
**Filename:** `0050_Uptimerobot_Automate.json`  
**Description:** Manual workflow that integrates with Uptimerobot for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Uptimerobot,  

---

### Manual Microsofttodo Automate Triggered
**Filename:** `0051_Manual_Microsofttodo_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Microsofttodo for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Microsofttodo,  

---

### Standup Bot - Initialize
**Filename:** `0054_Manual_Writebinaryfile_Automate_Triggered.json`  
**Description:** Manual workflow that connects Writebinaryfile and Movebinarydata for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Writebinaryfile,Movebinarydata,  

---

### Standup Bot - Read Config
**Filename:** `0058_Manual_Readbinaryfile_Automate_Triggered.json`  
**Description:** Manual workflow that connects Readbinaryfile and Movebinarydata for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Readbinaryfile,Movebinarydata,  

---

### Standup Bot - Override Config
**Filename:** `0064_Manual_Writebinaryfile_Automate_Triggered.json`  
**Description:** Manual workflow that connects Writebinaryfile and Movebinarydata for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Writebinaryfile,Movebinarydata,  

---

### Standup Bot - Worker
**Filename:** `0066_Webhook_Cron_Automate_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Mattermost, Webhook, and Httprequest for data processing. Uses 29 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (29 nodes)  
**Integrations:** Mattermost,Webhook,Httprequest,Executeworkflow,Cal.com,  

---

### Archive empty pages in Notion Database
**Filename:** `0070_Splitinbatches_Notion_Export_Scheduled.json`  
**Description:** Scheduled automation that connects Splitinbatches and Notion for data processing. Uses 10 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (10 nodes)  
**Integrations:** Splitinbatches,Notion,  

---

### Manual Rssfeedread Automate Triggered
**Filename:** `0073_Manual_Rssfeedread_Automate_Triggered.json`  
**Description:** Manual workflow that connects Splitinbatches and Rssfeedread for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (6 nodes)  
**Integrations:** Splitinbatches,Rssfeedread,  

---

### Get all the contacts from GetResponse and update them
**Filename:** `0075_Manual_Noop_Update_Triggered.json`  
**Description:** Manual workflow that integrates with Getresponse to update existing data. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Getresponse,  

---

### ↔️ Airtable Batch Processing
**Filename:** `0091_Wait_Splitout_Process_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Debughelper, and Airtable for data processing. Uses 35 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (35 nodes)  
**Integrations:** Splitinbatches,Debughelper,Airtable,Splitout,Httprequest,  

---

### Wait Datetime Automate Triggered
**Filename:** `0092_Wait_Datetime_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Cal.com, Datetime, and Pipedrive for data processing. Uses 5 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Cal.com,Datetime,Pipedrive,Slack,  

---

### Steam + CF Report
**Filename:** `0097_Executecommand_Mailgun_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Executecommand, Webhook, and Mailgun for data processing. Uses 9 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (9 nodes)  
**Integrations:** Executecommand,Webhook,Mailgun,  

---

### Wait Manual Automation Webhook
**Filename:** `0101_Wait_Manual_Automation_Webhook.json`  
**Description:** Manual workflow that orchestrates Httprequest, Splitinbatches, and N8Ntrainingcustomerdatastore for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (6 nodes)  
**Integrations:** Httprequest,Splitinbatches,N8Ntrainingcustomerdatastore,  

---

### Netlify Webhook Automate Webhook
**Filename:** `0104_Netlify_Webhook_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that connects Webhook and Netlify for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** Webhook,Netlify,  

---

### Create, update, and get a profile in Humantic AI
**Filename:** `0110_Manual_Humanticai_Create_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Humanticai to create new records. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Httprequest,Humanticai,  

---

### Manual Awstextract Automate Triggered
**Filename:** `0112_Manual_Awstextract_Automate_Triggered.json`  
**Description:** Manual workflow that connects Awstextract and Awss3 for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Awstextract,Awss3,  

---

### Create Onfleet tasks from Spreadsheets
**Filename:** `0118_Readbinaryfile_Onfleet_Create.json`  
**Description:** Manual workflow that orchestrates Readbinaryfile, Spreadsheetfile, and Onfleet to create new records. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Readbinaryfile,Spreadsheetfile,Onfleet,  

---

### Find a New Book
**Filename:** `0119_Manual_Cron_Create_Webhook.json`  
**Description:** Scheduled automation that connects Httprequest and Emailsend for data processing. Uses 13 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (13 nodes)  
**Integrations:** Httprequest,Emailsend,  

---

### Respondtowebhook Webhook Automate Webhook
**Filename:** `0121_Respondtowebhook_Webhook_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that integrates with Webhook for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** Webhook,  

---

### Calendly Notion Automate Triggered
**Filename:** `0125_Calendly_Notion_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Cal.com, Notion, and Dropcontact for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Cal.com,Notion,Dropcontact,  

---

### Manual Noop Monitor Triggered
**Filename:** `0127_Manual_Noop_Monitor_Triggered.json`  
**Description:** Manual workflow that for monitoring and reporting. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (6 nodes)  
**Integrations:** None  

---

### Manual N8ntrainingcustomerdatastore Automation Webhook
**Filename:** `0128_Manual_N8Ntrainingcustomerdatastore_Automation_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and N8Ntrainingcustomerdatastore for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Httprequest,N8Ntrainingcustomerdatastore,  

---

### Manual Start Automation Webhook
**Filename:** `0131_Manual_Start_Automation_Webhook.json`  
**Description:** Manual workflow that connects Start and Httprequest for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Start,Httprequest,  

---

### Notion Webhook Create Webhook
**Filename:** `0141_Notion_Webhook_Create_Webhook.json`  
**Description:** Webhook-triggered automation that connects Notion and Webhook to create new records. Uses 12 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (12 nodes)  
**Integrations:** Notion,Webhook,  

---

### Notion Webhook Create Webhook
**Filename:** `0142_Notion_Webhook_Create_Webhook.json`  
**Description:** Webhook-triggered automation that connects Notion and Webhook to create new records. Uses 23 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (23 nodes)  
**Integrations:** Notion,Webhook,  

---

### Get new time entries from Toggl
**Filename:** `0147_Toggl_Create_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Toggl for data processing. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Toggl,  

---

### Get today's date and day using the Function node
**Filename:** `0157_Manual_Import_Triggered.json`  
**Description:** Manual workflow that for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** None  

---

### Assign values to variables using the Set node
**Filename:** `0160_Manual_Automation_Triggered.json`  
**Description:** Manual workflow that for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** None  

---

### Respondtowebhook Spreadsheetfile Automate Webhook
**Filename:** `0163_Respondtowebhook_Spreadsheetfile_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Spreadsheetfile, Webhook, and Itemlists for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Spreadsheetfile,Webhook,Itemlists,  

---

### Crypto Webhook Automate Webhook
**Filename:** `0164_Crypto_Webhook_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that connects Crypto and Webhook for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Crypto,Webhook,  

---

### Webhook Respondtowebhook Create Webhook
**Filename:** `0165_Webhook_Respondtowebhook_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Crypto, Airtable, and Webhook to create new records. Uses 16 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (16 nodes)  
**Integrations:** Crypto,Airtable,Webhook,Respondtowebhook,Executeworkflow,Slack,  

---

### OpenAI-model-examples
**Filename:** `0171_Readbinaryfiles_Code_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Readbinaryfiles, and OpenAI for data processing. Uses 27 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (27 nodes)  
**Integrations:** Httprequest,Readbinaryfiles,OpenAI,Html,  

---

### Manual Automate Triggered
**Filename:** `0173_Manual_Automate_Triggered.json`  
**Description:** Manual workflow that for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** None  

---

### Activity Encouragement
**Filename:** `0174_Noop_Emailsend_Automation_Scheduled.json`  
**Description:** Scheduled automation that connects Strava and Emailsend for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (6 nodes)  
**Integrations:** Strava,Emailsend,  

---

### Add a subscriber to a list and create and send a campaign
**Filename:** `0175_Manual_Sendy_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Sendy to create new records. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Sendy,  

---

### Manual Automate Triggered
**Filename:** `0179_Manual_Automate_Triggered.json`  
**Description:** Manual workflow that for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** None  

---

### Respondtowebhook Webhook Import Webhook
**Filename:** `0194_Respondtowebhook_Webhook_Import_Webhook.json`  
**Description:** Webhook-triggered automation that integrates with Webhook for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Webhook,  

---

### Tools / Backup Gitlab
**Filename:** `0200_Manual_Executecommand_Export_Scheduled.json`  
**Description:** Scheduled automation that integrates with Executecommand for data backup operations. Uses 7 nodes.  
**Status:** Active  
**Trigger:** Scheduled  
**Complexity:** medium (7 nodes)  
**Integrations:** Executecommand,  

---

### Write a file to the host machine
**Filename:** `0203_Manual_Writebinaryfile_Automation_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Writebinaryfile for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Httprequest,Writebinaryfile,  

---

### Manual Stickynote Automation Webhook
**Filename:** `0206_Manual_Stickynote_Automation_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Compression for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (6 nodes)  
**Integrations:** Httprequest,Compression,  

---

### Smart Factory Data Generator
**Filename:** `0211_Interval_Amqp_Automation_Scheduled.json`  
**Description:** Manual workflow that connects Interval and Amqp for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Interval,Amqp,  

---

### Manual Markdown Create Webhook
**Filename:** `0213_Manual_Markdown_Create_Webhook.json`  
**Description:** Manual workflow that orchestrates Markdown, Movebinarydata, and Emailsend to create new records. Uses 10 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (10 nodes)  
**Integrations:** Markdown,Movebinarydata,Emailsend,Httprequest,Itemlists,  

---

### Manual Markdown Create Webhook
**Filename:** `0214_Manual_Markdown_Create_Webhook.json`  
**Description:** Manual workflow that orchestrates Markdown, Movebinarydata, and Emailsend to create new records. Uses 10 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (10 nodes)  
**Integrations:** Markdown,Movebinarydata,Emailsend,Httprequest,Itemlists,  

---

### Very quick quickstart
**Filename:** `0216_Manual_N8Ntrainingcustomerdatastore_Automation_Triggered.json`  
**Description:** Manual workflow that integrates with N8Ntrainingcustomerdatastore for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (6 nodes)  
**Integrations:** N8Ntrainingcustomerdatastore,  

---

### Create a table, and insert and update data in the table in Snowflake
**Filename:** `0219_Manual_Snowflake_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Snowflake to create new records. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (6 nodes)  
**Integrations:** Snowflake,  

---

### Readbinaryfile Manual Automate Triggered
**Filename:** `0220_Readbinaryfile_Manual_Automate_Triggered.json`  
**Description:** Manual workflow that orchestrates Readbinaryfile, Spreadsheetfile, and Writebinaryfile for data processing. Uses 5 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Readbinaryfile,Spreadsheetfile,Writebinaryfile,Movebinarydata,  

---

### Manual Stickynote Update Triggered
**Filename:** `0226_Manual_Stickynote_Update_Triggered.json`  
**Description:** Manual workflow that connects Datetime and Form Trigger to update existing data. Uses 9 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (9 nodes)  
**Integrations:** Datetime,Form Trigger,  

---

### Manual N8ntrainingcustomerdatastore Automate Triggered
**Filename:** `0227_Manual_N8Ntrainingcustomerdatastore_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with N8Ntrainingcustomerdatastore for data processing. Uses 9 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (9 nodes)  
**Integrations:** N8Ntrainingcustomerdatastore,  

---

### Manual Stickynote Automate Triggered
**Filename:** `0228_Manual_Stickynote_Automate_Triggered.json`  
**Description:** Manual workflow that for data processing. Uses 17 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** high (17 nodes)  
**Integrations:** None  

---

### N8ntrainingcustomermessenger Wait Create Triggered
**Filename:** `0230_N8Ntrainingcustomermessenger_Wait_Create_Triggered.json`  
**Description:** Manual workflow that orchestrates Splitinbatches, Server-Sent Events, and N8Ntrainingcustomerdatastore to create new records. Uses 13 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (13 nodes)  
**Integrations:** Splitinbatches,Server-Sent Events,N8Ntrainingcustomerdatastore,  

---

### Respondtowebhook Stickynote Create Webhook
**Filename:** `0232_Respondtowebhook_Stickynote_Create_Webhook.json`  
**Description:** Webhook-triggered automation that integrates with Webhook to create new records. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Webhook,  

---

### Manual N8ntrainingcustomerdatastore Create Triggered
**Filename:** `0233_Manual_N8Ntrainingcustomerdatastore_Create_Triggered.json`  
**Description:** Manual workflow that connects Google Sheets and N8Ntrainingcustomerdatastore to create new records. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (6 nodes)  
**Integrations:** Google Sheets,N8Ntrainingcustomerdatastore,  

---

### Code Typeform Create Triggered
**Filename:** `0239_Code_Typeform_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Pipedrive and Form Trigger to create new records. Uses 8 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (8 nodes)  
**Integrations:** Pipedrive,Form Trigger,  

---

### Manual Baserow Update Webhook
**Filename:** `0250_Manual_Baserow_Update_Webhook.json`  
**Description:** Scheduled automation that orchestrates Htmlextract, Baserow, and Httprequest to update existing data. Uses 9 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (9 nodes)  
**Integrations:** Htmlextract,Baserow,Httprequest,Form Trigger,Cal.com,Sendgrid,  

---

### Webhook Respondtowebhook Automation Webhook
**Filename:** `0260_Webhook_Respondtowebhook_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Webhook, Respondtowebhook, and Htmlextract for data processing. Uses 11 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (11 nodes)  
**Integrations:** Webhook,Respondtowebhook,Htmlextract,Httprequest,Itemlists,  

---

### Create, update, and get a document in Google Cloud Firestore
**Filename:** `0261_Manual_Googlefirebasecloudfirestore_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Googlefirebasecloudfirestore to create new records. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (6 nodes)  
**Integrations:** Googlefirebasecloudfirestore,  

---

### Typeform Spreadsheetfile Automate Triggered
**Filename:** `0262_Typeform_Spreadsheetfile_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Typeform, Spreadsheetfile, and Nextcloud for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (6 nodes)  
**Integrations:** Typeform,Spreadsheetfile,Nextcloud,  

---

### Code Webhook Create Webhook
**Filename:** `0273_Code_Webhook_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Zendesk, Webhook, and Slack to create new records. Uses 9 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (9 nodes)  
**Integrations:** Zendesk,Webhook,Slack,  

---

### Microsoftonedrive Readbinaryfile Automation Webhook
**Filename:** `0276_Microsoftonedrive_Readbinaryfile_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Readbinaryfile, Ftp, and Writebinaryfile for data processing. Uses 24 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (24 nodes)  
**Integrations:** Readbinaryfile,Ftp,Writebinaryfile,Google Drive,Httprequest,Spreadsheetfile,OneDrive,  

---

### Stickynote Notion Create Webhook
**Filename:** `0281_Stickynote_Notion_Create_Webhook.json`  
**Description:** Webhook-triggered automation that connects Notion and Webhook to create new records. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Notion,Webhook,  

---

### Manual Readbinaryfile Create Triggered
**Filename:** `0284_Manual_Readbinaryfile_Create_Triggered.json`  
**Description:** Manual workflow that orchestrates Readbinaryfile, Spreadsheetfile, and MySQL to create new records. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Readbinaryfile,Spreadsheetfile,MySQL,  

---

### Code Schedule Create Webhook
**Filename:** `0288_Code_Schedule_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Hubspot, and Stripe to create new records. Uses 24 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (24 nodes)  
**Integrations:** Httprequest,Hubspot,Stripe,Itemlists,  

---

### Wait Code Update Webhook
**Filename:** `0290_Wait_Code_Update_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, OpenAI, and Google Sheets to update existing data. Uses 11 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (11 nodes)  
**Integrations:** Splitinbatches,OpenAI,Google Sheets,Htmlextract,Httprequest,  

---

### Manual Stickynote Export Triggered
**Filename:** `0292_Manual_Stickynote_Export_Triggered.json`  
**Description:** Manual workflow that connects Spreadsheetfile and MySQL for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Spreadsheetfile,MySQL,  

---

### Create, update and get a product from WooCommerce
**Filename:** `0293_Manual_Woocommerce_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Woocommerce to create new records. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Woocommerce,  

---

### Webhook Dropcontact Create Webhook
**Filename:** `0295_Webhook_Dropcontact_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Hubspot, Lemlist, and LinkedIn to create new records. Uses 18 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (18 nodes)  
**Integrations:** Hubspot,Lemlist,LinkedIn,Dropcontact,Cal.com,Slack,  

---

### Code Webhook Create Webhook
**Filename:** `0296_Code_Webhook_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates N8N, Respondtowebhook, and Html to create new records. Uses 9 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (9 nodes)  
**Integrations:** N8N,Respondtowebhook,Html,Webhook,  

---

### Manual Openai Export Triggered
**Filename:** `0297_Manual_Openai_Export_Triggered.json`  
**Description:** Manual workflow that connects Reddit and OpenAI for data processing. Uses 15 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (15 nodes)  
**Integrations:** Reddit,OpenAI,  

---

### Code Webhook Create Webhook
**Filename:** `0299_Code_Webhook_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Crypto, OpenAI, and Google Sheets to create new records. Uses 49 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (49 nodes)  
**Integrations:** Crypto,OpenAI,Google Sheets,Webhook,Gmail,Html,Respondtowebhook,Form Trigger,  

---

### Create, update, and get a subscriber using the e-goi node
**Filename:** `0300_Manual_Egoi_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Egoi to create new records. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Egoi,  

---

### Manual N8ntrainingcustomerdatastore Automate Triggered
**Filename:** `0302_Manual_N8Ntrainingcustomerdatastore_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with N8Ntrainingcustomerdatastore for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** N8Ntrainingcustomerdatastore,  

---

### Manual Stickynote Export Triggered
**Filename:** `0303_Manual_Stickynote_Export_Triggered.json`  
**Description:** Manual workflow that connects Microsoftsql and Spreadsheetfile for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Microsoftsql,Spreadsheetfile,  

---

### Manual Stickynote Automation Webhook
**Filename:** `0304_Manual_Stickynote_Automation_Webhook.json`  
**Description:** Manual workflow that integrates with Httprequest for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (6 nodes)  
**Integrations:** Httprequest,  

---

### Code Schedule Automate Scheduled
**Filename:** `0308_Code_Schedule_Automate_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Notion, Slack, and Itemlists for data processing. Uses 11 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (11 nodes)  
**Integrations:** Notion,Slack,Itemlists,  

---

### Code Filter Automate Triggered
**Filename:** `0309_Code_Filter_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that connects Linear and Slack for data processing. Uses 10 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** Linear,Slack,  

---

### Datetime Schedule Create Webhook
**Filename:** `0311_Datetime_Schedule_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Notion, and Datetime to create new records. Uses 14 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** Httprequest,Notion,Datetime,Gmail,  

---

### Manual Comparedatasets Automate Triggered
**Filename:** `0315_Manual_Comparedatasets_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Comparedatasets for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (6 nodes)  
**Integrations:** Comparedatasets,  

---

### Datetime Schedule Create Webhook
**Filename:** `0316_Datetime_Schedule_Create_Webhook.json`  
**Description:** Scheduled automation that orchestrates Notion, Datetime, and Outlook to create new records. Uses 9 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (9 nodes)  
**Integrations:** Notion,Datetime,Outlook,Itemlists,  

---

### Manual Movebinarydata Process Triggered
**Filename:** `0317_Manual_Movebinarydata_Process_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Xml, Writebinaryfile, and Movebinarydata for data processing. Uses 13 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (13 nodes)  
**Integrations:** Xml,Writebinaryfile,Movebinarydata,MySQL,Itemlists,  

---

### Splitout Limit Automation Webhook
**Filename:** `0318_Splitout_Limit_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Splitout, and Html for data processing. Uses 16 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (16 nodes)  
**Integrations:** OpenAI,Splitout,Html,Httprequest,Documentdefaultdataloader,Textsplitterrecursivecharactertextsplitter,Chainsummarization,  

---

### Manual Stickynote Automate Triggered
**Filename:** `0321_Manual_Stickynote_Automate_Triggered.json`  
**Description:** Manual workflow that orchestrates Toolcode, Agent, and OpenAI for data processing. Uses 10 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (10 nodes)  
**Integrations:** Toolcode,Agent,OpenAI,  

---

### Manual Stickynote Process Triggered
**Filename:** `0323_Manual_Stickynote_Process_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Chainllm, OpenAI, and Outputparserstructured for data processing. Uses 11 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (11 nodes)  
**Integrations:** Chainllm,OpenAI,Outputparserstructured,Outputparserautofixing,  

---

### Manual Stickynote Update Triggered
**Filename:** `0324_Manual_Stickynote_Update_Triggered.json`  
**Description:** Manual workflow that orchestrates Chainretrievalqa, OpenAI, and Retrieverworkflow to update existing data. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (7 nodes)  
**Integrations:** Chainretrievalqa,OpenAI,Retrieverworkflow,  

---

### Stopanderror Webhook Create Webhook
**Filename:** `0333_Stopanderror_Webhook_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Executeworkflow, Splitinbatches, and Webhook to create new records. Uses 32 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (32 nodes)  
**Integrations:** Executeworkflow,Splitinbatches,Webhook,Nextcloud,  

---

### Openai Form Create Triggered
**Filename:** `0334_Openai_Form_Create_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Google Sheets, OpenAI, and Form Trigger to create new records. Uses 9 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (9 nodes)  
**Integrations:** Google Sheets,OpenAI,Form Trigger,  

---

### Snowflake CSV
**Filename:** `0336_Manual_Snowflake_Automation_Webhook.json`  
**Description:** Manual workflow that orchestrates Httprequest, Spreadsheetfile, and Snowflake for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Httprequest,Spreadsheetfile,Snowflake,  

---

### Structured Data Extract, Data Mining with Bright Data & Google Gemini
**Filename:** `0337_Manual_Stickynote_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Readwritefile, Lmchatgooglegemini, and Webhook for data processing. Uses 18 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (18 nodes)  
**Integrations:** Readwritefile,Lmchatgooglegemini,Webhook,Informationextractor,Chainllm,Form Trigger,  

---

### Capture Website Screenshots with Bright Data Web Unlocker and Save to Disk
**Filename:** `0338_Manual_Stickynote_Export_Webhook.json`  
**Description:** Manual workflow that connects Readwritefile and Httprequest for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (6 nodes)  
**Integrations:** Readwritefile,Httprequest,  

---

### Printify Automation - Update Title and Description - AlexK1919
**Filename:** `0339_Splitout_Code_Update_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, OpenAI, and Splitout to update existing data. Uses 26 nodes and integrates with 7 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (26 nodes)  
**Integrations:** Splitinbatches,OpenAI,Splitout,Google Sheets,Httprequest,Toolwikipedia,Cal.com,  

---

### [1/3 - anomaly detection] [1/2 - KNN classification] Batch upload dataset to Qdrant (crops dataset)
**Filename:** `0341_Code_Filter_Import_Webhook.json`  
**Description:** Manual workflow that orchestrates Googlecloudstorage, Httprequest, and Form Trigger for data processing. Uses 25 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** high (25 nodes)  
**Integrations:** Googlecloudstorage,Httprequest,Form Trigger,  

---

### Readbinaryfile Manual Automate Triggered
**Filename:** `0351_Readbinaryfile_Manual_Automate_Triggered.json`  
**Description:** Manual workflow that orchestrates Readbinaryfile, Splitinbatches, and Spreadsheetfile for data processing. Uses 6 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (6 nodes)  
**Integrations:** Readbinaryfile,Splitinbatches,Spreadsheetfile,Emailsend,  

---

### Readbinaryfile Spreadsheetfile Create
**Filename:** `0352_Readbinaryfile_Spreadsheetfile_Create.json`  
**Description:** Manual workflow that orchestrates Readbinaryfile, Spreadsheetfile, and PostgreSQL to create new records. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Readbinaryfile,Spreadsheetfile,PostgreSQL,  

---

### Code Manual Automation Webhook
**Filename:** `0366_Code_Manual_Automation_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Google Sheets for data processing. Uses 8 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (8 nodes)  
**Integrations:** Httprequest,Google Sheets,  

---

### Stickynote Webhook Automate Webhook
**Filename:** `0368_Stickynote_Webhook_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that integrates with Webhook for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Webhook,  

---

### Code Schedule Create Webhook
**Filename:** `0370_Code_Schedule_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, Google Sheets, and Html to create new records. Uses 13 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (13 nodes)  
**Integrations:** Markdown,Google Sheets,Html,Httprequest,Itemlists,Slack,  

---

### Code Webhook Automation Webhook
**Filename:** `0373_Code_Webhook_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Webhook, and Shopify for data processing. Uses 15 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (15 nodes)  
**Integrations:** Httprequest,Webhook,Shopify,  

---

### Webhook Code Create Webhook
**Filename:** `0376_Webhook_Code_Create_Webhook.json`  
**Description:** Webhook-triggered automation that connects Webhook and MySQL to create new records. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Webhook,MySQL,  

---

### Manual Stickynote Update Triggered
**Filename:** `0377_Manual_Stickynote_Update_Triggered.json`  
**Description:** Manual workflow that integrates with Airtable to update existing data. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Airtable,  

---

### Stickynote Notion Automate Webhook
**Filename:** `0378_Stickynote_Notion_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that connects Notion and Webhook for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Notion,Webhook,  

---

### Code Manual Create Triggered
**Filename:** `0380_Code_Manual_Create_Triggered.json`  
**Description:** Manual workflow that connects Splitinbatches and Google Drive to create new records. Uses 9 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (9 nodes)  
**Integrations:** Splitinbatches,Google Drive,  

---

### Splitout Filter Update Scheduled
**Filename:** `0386_Splitout_Filter_Update_Scheduled.json`  
**Description:** Scheduled automation that connects Httprequest and Splitout to update existing data. Uses 8 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (8 nodes)  
**Integrations:** Httprequest,Splitout,  

---

### Get analytics of a website and store it Airtable
**Filename:** `0389_Manual_Googleanalytics_Import_Triggered.json`  
**Description:** Manual workflow that connects Airtable and Google Analytics for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Airtable,Google Analytics,  

---

### Code Filter Create Scheduled
**Filename:** `0391_Code_Filter_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Google Sheets, and Itemlists to create new records. Uses 20 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (20 nodes)  
**Integrations:** Httprequest,Google Sheets,Itemlists,Form Trigger,  

---

### Datetime Schedule Automation Scheduled
**Filename:** `0396_Datetime_Schedule_Automation_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates N8N, Datetime, and Dropbox for data processing. Uses 17 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** N8N,Datetime,Dropbox,Movebinarydata,  

---

### Code Schedule Import Scheduled
**Filename:** `0397_Code_Schedule_Import_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Google Sheets, and LinkedIn for data processing. Uses 13 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (13 nodes)  
**Integrations:** Httprequest,Google Sheets,LinkedIn,Itemlists,  

---

### Manual Stickynote Automate Triggered
**Filename:** `0399_Manual_Stickynote_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with OpenAI for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (6 nodes)  
**Integrations:** OpenAI,  

---

### Manual Code Create Webhook
**Filename:** `0400_Manual_Code_Create_Webhook.json`  
**Description:** Manual workflow that orchestrates Httprequest, Chainllm, and OpenAI to create new records. Uses 13 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (13 nodes)  
**Integrations:** Httprequest,Chainllm,OpenAI,  

---

### Schedule Filter Update Scheduled
**Filename:** `0402_Schedule_Filter_Update_Scheduled.json`  
**Description:** Scheduled automation that connects Google Sheets and Gmail to update existing data. Uses 12 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (12 nodes)  
**Integrations:** Google Sheets,Gmail,  

---

### Webhook Filter Update Webhook
**Filename:** `0410_Webhook_Filter_Update_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Google Sheets, Discord, and Respondtowebhook to update existing data. Uses 16 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (16 nodes)  
**Integrations:** Google Sheets,Discord,Respondtowebhook,Webhook,  

---

### Webhook Filter Create Webhook
**Filename:** `0414_Webhook_Filter_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Webhook, Clearbit, and Slack to create new records. Uses 12 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (12 nodes)  
**Integrations:** Webhook,Clearbit,Slack,  

---

### Splitout Filter Export Scheduled
**Filename:** `0418_Splitout_Filter_Export_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Splitout, and Clearbit for data processing. Uses 15 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (15 nodes)  
**Integrations:** Httprequest,Splitout,Clearbit,Google Sheets,  

---

### Splitout Schedule Import Webhook
**Filename:** `0421_Splitout_Schedule_Import_Webhook.json`  
**Description:** Scheduled automation that orchestrates Httprequest, Splitout, and Hubspot for data processing. Uses 22 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** high (22 nodes)  
**Integrations:** Httprequest,Splitout,Hubspot,  

---

### Stopanderror Wait Monitor Webhook
**Filename:** `0427_Stopanderror_Wait_Monitor_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Splitinbatches, and Xml for monitoring and reporting. Uses 12 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** Httprequest,Splitinbatches,Xml,Splitout,  

---

### Calendly Filter Create Triggered
**Filename:** `0430_Calendly_Filter_Create_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Cal.com, Hubspot, and Clearbit to create new records. Uses 16 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (16 nodes)  
**Integrations:** Cal.com,Hubspot,Clearbit,  

---

### Schedule Filter Create Scheduled
**Filename:** `0432_Schedule_Filter_Create_Scheduled.json`  
**Description:** Scheduled automation that connects Hubspot and Slack to create new records. Uses 8 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (8 nodes)  
**Integrations:** Hubspot,Slack,  

---

### Splitout Webhook Update Webhook
**Filename:** `0433_Splitout_Webhook_Update_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Xml, Webhook, and Respondtowebhook to update existing data. Uses 9 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (9 nodes)  
**Integrations:** Xml,Webhook,Respondtowebhook,Splitout,Httprequest,  

---

### Splitout Webhook Automation Webhook
**Filename:** `0434_Splitout_Webhook_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Splitout, and Removeduplicates for data processing. Uses 10 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** Httprequest,Splitout,Removeduplicates,Webhook,  

---

### Splitout Filter Create Webhook
**Filename:** `0435_Splitout_Filter_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitout, Httprequest, and Clearbit to create new records. Uses 20 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (20 nodes)  
**Integrations:** Splitout,Httprequest,Clearbit,Pipedrive,Slack,  

---

### Code Filter Create Scheduled
**Filename:** `0437_Code_Filter_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Executeworkflow, Cal.com, and Google Sheets to create new records. Uses 32 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (32 nodes)  
**Integrations:** Executeworkflow,Cal.com,Google Sheets,Gmail,  

---

### Code Filter Create Webhook
**Filename:** `0438_Code_Filter_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, GraphQL, and Linear to create new records. Uses 24 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (24 nodes)  
**Integrations:** Splitinbatches,GraphQL,Linear,OpenAI,Respondtowebhook,Form Trigger,Notion,  

---

### Manual Schedule Create Scheduled
**Filename:** `0439_Manual_Schedule_Create_Scheduled.json`  
**Description:** Scheduled automation that connects Twitter/X and OpenAI to create new records. Uses 12 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (12 nodes)  
**Integrations:** Twitter/X,OpenAI,  

---

### Schedule Filter Automation Scheduled
**Filename:** `0443_Schedule_Filter_Automation_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Box, Gmail, and Itemlists for data processing. Uses 10 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (10 nodes)  
**Integrations:** Box,Gmail,Itemlists,  

---

### Splitout Code Import Scheduled
**Filename:** `0445_Splitout_Code_Import_Scheduled.json`  
**Description:** Scheduled automation that orchestrates GraphQL, Google Sheets, and Splitout for data processing. Uses 14 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (14 nodes)  
**Integrations:** GraphQL,Google Sheets,Splitout,  

---

### Splitout Webhook Create Webhook
**Filename:** `0449_Splitout_Webhook_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Splitout, and Respondtowebhook to create new records. Uses 18 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (18 nodes)  
**Integrations:** Httprequest,Splitout,Respondtowebhook,Webhook,  

---

### Splitout Webhook Create Webhook
**Filename:** `0452_Splitout_Webhook_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Splitout, Respondtowebhook, and Webhook to create new records. Uses 9 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (9 nodes)  
**Integrations:** Splitout,Respondtowebhook,Webhook,Httprequest,Form Trigger,  

---

### Webhook Code Create Webhook
**Filename:** `0453_Webhook_Code_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Webhook, Httprequest, and PostgreSQL to create new records. Uses 34 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (34 nodes)  
**Integrations:** Webhook,Httprequest,PostgreSQL,Form Trigger,Executeworkflow,Slack,  

---

### Create, update, and get a user using the G Suite Admin node
**Filename:** `0455_Manual_Gsuiteadmin_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Gsuiteadmin to create new records. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Gsuiteadmin,  

---

### Splitout Webhook Create Webhook
**Filename:** `0457_Splitout_Webhook_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Dhl, OpenAI, and Splitout to create new records. Uses 40 nodes and integrates with 12 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (40 nodes)  
**Integrations:** Dhl,OpenAI,Splitout,Webhook,Toolworkflow,Agent,Httprequest,Chat,Form Trigger,Executeworkflow,Memorybufferwindow,Woocommerce,  

---

### Manual Code Create Triggered
**Filename:** `0458_Manual_Code_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Google Drive, and Vectorstorepinecone to create new records. Uses 20 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (20 nodes)  
**Integrations:** OpenAI,Google Drive,Vectorstorepinecone,Informationextractor,Documentdefaultdataloader,Chat,Textsplitterrecursivecharactertextsplitter,  

---

### Splitout Webhook Update Webhook
**Filename:** `0459_Splitout_Webhook_Update_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Splitout, and Webhook to update existing data. Uses 14 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** OpenAI,Splitout,Webhook,Httprequest,Chainllm,  

---

### Graphql Webhook Automate Webhook
**Filename:** `0461_Graphql_Webhook_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that connects GraphQL and Webhook for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** GraphQL,Webhook,  

---

### Openai Form Create Webhook
**Filename:** `0464_Openai_Form_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Webhook, OpenAI, and Form Trigger to create new records. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Webhook,OpenAI,Form Trigger,  

---

### Splitout Code Create Webhook
**Filename:** `0468_Splitout_Code_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Splitout, and Wordpress to create new records. Uses 37 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (37 nodes)  
**Integrations:** OpenAI,Splitout,Wordpress,Httprequest,Form Trigger,Toolwikipedia,  

---

### Limit Code Create Scheduled
**Filename:** `0473_Limit_Code_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, Splitinbatches, and OpenAI to create new records. Uses 23 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (23 nodes)  
**Integrations:** Markdown,Splitinbatches,OpenAI,Gmail,Httprequest,  

---

### Automate Google Analytics Reporting - AlexK1919
**Filename:** `0475_Googleanalytics_Code_Automate_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Google Analytics, Gmail, and Form Trigger for data processing. Uses 23 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** high (23 nodes)  
**Integrations:** Google Analytics,Gmail,Form Trigger,  

---

### Grist Stickynote Create Webhook
**Filename:** `0479_Grist_Stickynote_Create_Webhook.json`  
**Description:** Webhook-triggered automation that connects Grist and Webhook to create new records. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Grist,Webhook,  

---

### [n8n] YouTube Channel Advanced RSS Feeds Generator
**Filename:** `0482_Code_Respondtowebhook_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Webhook, and Form Trigger for data processing. Uses 20 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (20 nodes)  
**Integrations:** Httprequest,Webhook,Form Trigger,  

---

### Webhook Extractfromfile Update Webhook
**Filename:** `0483_Webhook_Extractfromfile_Update_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Xml, Webhook, and Respondtowebhook to update existing data. Uses 12 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** Xml,Webhook,Respondtowebhook,Extractfromfile,Slack,  

---

### Dynamic credentials using expressions
**Filename:** `0484_Form_Stickynote_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Nasa, Webhook, and Form Trigger for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Nasa,Webhook,Form Trigger,  

---

### Manual Debughelper Create Triggered
**Filename:** `0489_Manual_Debughelper_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Debughelper to create new records. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Debughelper,  

---

### Code Webhook Monitor Webhook
**Filename:** `0491_Code_Webhook_Monitor_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Webhook, and Respondtowebhook for monitoring and reporting. Uses 8 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (8 nodes)  
**Integrations:** Httprequest,Webhook,Respondtowebhook,  

---

### Wait Splitout Process Scheduled
**Filename:** `0498_Wait_Splitout_Process_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Splitout, and PostgreSQL for data processing. Uses 12 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** Splitinbatches,Splitout,PostgreSQL,Httprequest,Form Trigger,Slack,  

---

### Webhook Respondtowebhook Create Webhook
**Filename:** `0499_Webhook_Respondtowebhook_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Cal.com, N8Ntrainingcustomerdatastore, and Respondtowebhook to create new records. Uses 9 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (9 nodes)  
**Integrations:** Cal.com,N8Ntrainingcustomerdatastore,Respondtowebhook,  

---

### Splitout Code Create Scheduled
**Filename:** `0503_Splitout_Code_Create_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Splitinbatches, Splitout, and Spotify to create new records. Uses 23 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** high (23 nodes)  
**Integrations:** Splitinbatches,Splitout,Spotify,  

---

### Code Filter Create Scheduled
**Filename:** `0506_Code_Filter_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Webflow, and Comparedatasets to create new records. Uses 29 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (29 nodes)  
**Integrations:** Splitinbatches,Webflow,Comparedatasets,Notion,Slack,  

---

### Manual Stickynote Automation Webhook
**Filename:** `0507_Manual_Stickynote_Automation_Webhook.json`  
**Description:** Manual workflow that connects Readwritefile and Httprequest for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Readwritefile,Httprequest,  

---

### Converttofile Manual Process Triggered
**Filename:** `0508_Converttofile_Manual_Process_Triggered.json`  
**Description:** Manual workflow that connects N8N and Converttofile for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (7 nodes)  
**Integrations:** N8N,Converttofile,  

---

### Manual Stickynote Automation Webhook
**Filename:** `0509_Manual_Stickynote_Automation_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Readwritefile for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (6 nodes)  
**Integrations:** Httprequest,Readwritefile,  

---

### Splitout Code Update Webhook
**Filename:** `0512_Splitout_Code_Update_Webhook.json`  
**Description:** Manual workflow that orchestrates Httprequest, Splitout, and N8N to update existing data. Uses 15 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (15 nodes)  
**Integrations:** Httprequest,Splitout,N8N,  

---

### Manual Stickynote Automation Webhook
**Filename:** `0513_Manual_Stickynote_Automation_Webhook.json`  
**Description:** Manual workflow that connects Readwritefile and Httprequest for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Readwritefile,Httprequest,  

---

### Manual Stickynote Automation Webhook
**Filename:** `0514_Manual_Stickynote_Automation_Webhook.json`  
**Description:** Manual workflow that connects Readwritefile and Httprequest for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Readwritefile,Httprequest,  

---

### Code Manual Create Webhook
**Filename:** `0519_Code_Manual_Create_Webhook.json`  
**Description:** Manual workflow that connects Readwritefile and Httprequest to create new records. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (6 nodes)  
**Integrations:** Readwritefile,Httprequest,  

---

### Splitout Filter Create Webhook
**Filename:** `0520_Splitout_Filter_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, OpenAI, and Splitout to create new records. Uses 38 nodes and integrates with 10 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (38 nodes)  
**Integrations:** Markdown,OpenAI,Splitout,Agent,Toolworkflow,Html,Outputparserstructured,Httprequest,Removeduplicates,Supabase,  

---

### Manual Stickynote Automation Webhook
**Filename:** `0521_Manual_Stickynote_Automation_Webhook.json`  
**Description:** Manual workflow that connects Readwritefile and Httprequest for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Readwritefile,Httprequest,  

---

### Manual Stickynote Automation Webhook
**Filename:** `0522_Manual_Stickynote_Automation_Webhook.json`  
**Description:** Manual workflow that connects Readwritefile and Httprequest for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Readwritefile,Httprequest,  

---

### Wait Splitout Create Webhook
**Filename:** `0523_Wait_Splitout_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Google Sheets, and Splitout to create new records. Uses 26 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (26 nodes)  
**Integrations:** OpenAI,Google Sheets,Splitout,Gmail,Outputparserstructured,Httprequest,Chainllm,  

---

### Googledocs Webhook Create Webhook
**Filename:** `0524_Googledocs_Webhook_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, OpenAI, and Google Docs to create new records. Uses 23 nodes and integrates with 9 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (23 nodes)  
**Integrations:** Splitinbatches,OpenAI,Google Docs,Webhook,Extractfromfile,Outputparseritemlist,Gmail,Chainllm,Slack,  

---

### Schedule Manual Update Scheduled
**Filename:** `0527_Schedule_Manual_Update_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, OpenAI, and Google Sheets to update existing data. Uses 22 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (22 nodes)  
**Integrations:** Splitinbatches,OpenAI,Google Sheets,Agent,Toolworkflow,Outputparserstructured,Toolserpapi,  

---

### Splitout Elasticsearch Create Webhook
**Filename:** `0532_Splitout_Elasticsearch_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Splitout, and Elasticsearch to create new records. Uses 17 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** Httprequest,Splitout,Elasticsearch,Editimage,  

---

### Wait Code Export Webhook
**Filename:** `0533_Wait_Code_Export_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Embeddingsmistralcloud, Toolworkflow, and Html for data processing. Uses 33 nodes and integrates with 11 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (33 nodes)  
**Integrations:** Embeddingsmistralcloud,Toolworkflow,Html,Agent,Httprequest,Documentdefaultdataloader,Vectorstoreqdrant,Textsplitterrecursivecharactertextsplitter,Chat,Executeworkflow,Lmchatmistralcloud,  

---

### Executecommand Localfile Process Triggered
**Filename:** `0534_Executecommand_Localfile_Process_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Executecommand, Splitout, and Outputparserstructured for data processing. Uses 16 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (16 nodes)  
**Integrations:** Executecommand,Splitout,Outputparserstructured,Chainllm,Cal.com,Lmchatmistralcloud,  

---

### Localfile Manual Create Webhook
**Filename:** `0535_Localfile_Manual_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Readwritefile, Embeddingsmistralcloud, and Chainretrievalqa to create new records. Uses 29 nodes and integrates with 11 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (29 nodes)  
**Integrations:** Readwritefile,Embeddingsmistralcloud,Chainretrievalqa,Httprequest,Documentdefaultdataloader,Vectorstoreqdrant,Textsplitterrecursivecharactertextsplitter,Chat,Retrievervectorstore,Cal.com,Lmchatmistralcloud,  

---

### Localfile Wait Create Triggered
**Filename:** `0537_Localfile_Wait_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Readwritefile, Splitinbatches, and Converttofile to create new records. Uses 42 nodes and integrates with 16 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (42 nodes)  
**Integrations:** Readwritefile,Splitinbatches,Converttofile,Splitout,Embeddingsmistralcloud,Outputparseritemlist,Chainretrievalqa,Extractfromfile,Documentdefaultdataloader,Vectorstoreqdrant,Chainllm,Textsplitterrecursivecharactertextsplitter,Retrievervectorstore,Cal.com,Chainsummarization,Lmchatmistralcloud,  

---

### Manual Stickynote Automation Webhook
**Filename:** `0540_Manual_Stickynote_Automation_Webhook.json`  
**Description:** Manual workflow that orchestrates Toolhttprequest, OpenAI, and Agent for data processing. Uses 12 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (12 nodes)  
**Integrations:** Toolhttprequest,OpenAI,Agent,  

---

### Manual Stickynote Update Triggered
**Filename:** `0541_Manual_Stickynote_Update_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Editimage, Vectorstoreinmemory, and OpenAI to update existing data. Uses 22 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (22 nodes)  
**Integrations:** Editimage,Vectorstoreinmemory,OpenAI,Google Drive,Documentdefaultdataloader,Textsplitterrecursivecharactertextsplitter,Form Trigger,  

---

### Manual N8n Export Triggered
**Filename:** `0543_Manual_N8N_Export_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Agent, and N8N for data processing. Uses 13 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (13 nodes)  
**Integrations:** OpenAI,Agent,N8N,Chat,Toolcode,Memorybufferwindow,  

---

### Code Schedule Create Scheduled
**Filename:** `0546_Code_Schedule_Create_Scheduled.json`  
**Description:** Scheduled automation that connects GitHub and Slack to create new records. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (7 nodes)  
**Integrations:** GitHub,Slack,  

---

### Wait Splitout Create Webhook
**Filename:** `0547_Wait_Splitout_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Toolhttprequest, and OpenAI to create new records. Uses 39 nodes and integrates with 10 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (39 nodes)  
**Integrations:** Splitinbatches,Toolhttprequest,OpenAI,Splitout,Agent,LinkedIn,Outputparserstructured,Httprequest,Removeduplicates,Notion,  

---

### Code Webhook Create Webhook
**Filename:** `0548_Code_Webhook_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Toolhttprequest, OpenAI, and Textclassifier to create new records. Uses 39 nodes and integrates with 18 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (39 nodes)  
**Integrations:** Toolhttprequest,OpenAI,Textclassifier,Webhook,Agent,Chainretrievalqa,Vectorstorepinecone,Anthropic,Gmail,Httprequest,Box,Documentdefaultdataloader,Retrievervectorstore,Textsplitterrecursivecharactertextsplitter,Chat,Cal.com,Memorybufferwindow,Slack,  

---

### Splitout Code Create Webhook
**Filename:** `0554_Splitout_Code_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, OpenAI, and Splitout to create new records. Uses 42 nodes and integrates with 10 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (42 nodes)  
**Integrations:** Splitinbatches,OpenAI,Splitout,Google Sheets,Informationextractor,Httprequest,Documentdefaultdataloader,Vectorstoreqdrant,Textsplitterrecursivecharactertextsplitter,Executeworkflow,  

---

### Splitout Code Export Webhook
**Filename:** `0555_Splitout_Code_Export_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Splitout, and Google Sheets for data processing. Uses 37 nodes and integrates with 10 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (37 nodes)  
**Integrations:** OpenAI,Splitout,Google Sheets,Html,Informationextractor,Httprequest,Documentdefaultdataloader,Vectorstoreqdrant,Textsplitterrecursivecharactertextsplitter,Executeworkflow,  

---

### Splitout Code Create Webhook
**Filename:** `0556_Splitout_Code_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Hackernews, OpenAI, and Splitout to create new records. Uses 36 nodes and integrates with 10 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (36 nodes)  
**Integrations:** Hackernews,OpenAI,Splitout,Google Sheets,Httprequest,Documentdefaultdataloader,Vectorstoreqdrant,Textsplitterrecursivecharactertextsplitter,Form Trigger,Executeworkflow,  

---

### TOTP VALIDATION (WITHOUT CREATING CREDENTIAL)
**Filename:** `0558_Manual_Stickynote_Automation_Triggered.json`  
**Description:** Manual workflow that for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** None  

---

### Splitout Filter Import Webhook
**Filename:** `0560_Splitout_Filter_Import_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Splitout, and Spotify for data processing. Uses 26 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (26 nodes)  
**Integrations:** Httprequest,Splitout,Spotify,Mqtt,  

---

### Splitout Filter Create Webhook
**Filename:** `0562_Splitout_Filter_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Google Drive, Splitout, and Gmail to create new records. Uses 13 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (13 nodes)  
**Integrations:** Google Drive,Splitout,Gmail,Html,Httprequest,  

---

### Schedule Filter Update Scheduled
**Filename:** `0563_Schedule_Filter_Update_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Todoist, Box, and OpenAI to update existing data. Uses 12 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (12 nodes)  
**Integrations:** Todoist,Box,OpenAI,  

---

### Supabase Stickynote Create Triggered
**Filename:** `0564_Supabase_Stickynote_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Google Drive, and Vectorstoresupabase to create new records. Uses 21 nodes and integrates with 9 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (21 nodes)  
**Integrations:** OpenAI,Google Drive,Vectorstoresupabase,Chainretrievalqa,Documentdefaultdataloader,Retrievervectorstore,Textsplitterrecursivecharactertextsplitter,Chat,Supabase,  

---

### Backup n8n Workflows to Bitbucket
**Filename:** `0567_Wait_Code_Export_Webhook.json`  
**Description:** Scheduled automation that orchestrates Splitinbatches, Cal.com, and N8N for data backup operations. Uses 9 nodes and integrates with 4 services.  
**Status:** Active  
**Trigger:** Scheduled  
**Complexity:** medium (9 nodes)  
**Integrations:** Splitinbatches,Cal.com,N8N,Httprequest,  

---

### Splitout Datetime Create Webhook
**Filename:** `0570_Splitout_Datetime_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Splitout, and Datetime to create new records. Uses 29 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (29 nodes)  
**Integrations:** Httprequest,Splitout,Datetime,Form Trigger,  

---

### Stickynote Notion Create Triggered
**Filename:** `0574_Stickynote_Notion_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Agent, Anthropic, and Outputparserstructured to create new records. Uses 24 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (24 nodes)  
**Integrations:** Agent,Anthropic,Outputparserstructured,Chat,Outputparserautofixing,Notion,Textclassifier,  

---

### Respondtowebhook Form Automation Webhook
**Filename:** `0576_Respondtowebhook_Form_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Cal.com, Respondtowebhook, and S3 for data processing. Uses 19 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (19 nodes)  
**Integrations:** Cal.com,Respondtowebhook,S3,Form Trigger,  

---

### Wait Schedule Create Webhook
**Filename:** `0578_Wait_Schedule_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Splitinbatches, and Linear to create new records. Uses 34 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (34 nodes)  
**Integrations:** Lmchatgooglegemini,Splitinbatches,Linear,Google Drive,Google Sheets,Outputparserstructured,Httprequest,Chainllm,  

---

### Wait Splitout Create Webhook
**Filename:** `0583_Wait_Splitout_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Splitout, and Gmail to create new records. Uses 21 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (21 nodes)  
**Integrations:** OpenAI,Splitout,Gmail,Html,Httprequest,  

---

### Respondtowebhook Stickynote Automate Webhook
**Filename:** `0586_Respondtowebhook_Stickynote_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Cal.com, Respondtowebhook, and OpenAI for data processing. Uses 7 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Cal.com,Respondtowebhook,OpenAI,Agent,  

---

### Manual Filter Update Webhook
**Filename:** `0589_Manual_Filter_Update_Webhook.json`  
**Description:** Manual workflow that integrates with Httprequest to update existing data. Uses 16 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** high (16 nodes)  
**Integrations:** Httprequest,  

---

### Webhook Respondtowebhook Create Webhook
**Filename:** `0591_Webhook_Respondtowebhook_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Webhook, and Html to create new records. Uses 11 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (11 nodes)  
**Integrations:** Httprequest,Webhook,Html,  

---

### Wait Code Import Webhook
**Filename:** `0596_Wait_Code_Import_Webhook.json`  
**Description:** Scheduled automation that orchestrates Httprequest, Splitinbatches, and Gmail for data processing. Uses 12 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (12 nodes)  
**Integrations:** Httprequest,Splitinbatches,Gmail,  

---

### Respondtowebhook Stickynote Automate Webhook
**Filename:** `0597_Respondtowebhook_Stickynote_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Webhook, Respondtowebhook, and OpenAI for data processing. Uses 10 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** Webhook,Respondtowebhook,OpenAI,  

---

### Code Extractfromfile Create Webhook
**Filename:** `0600_Code_Extractfromfile_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Editimage, Converttofile, and OpenAI to create new records. Uses 50 nodes and integrates with 18 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (50 nodes)  
**Integrations:** Editimage,Converttofile,OpenAI,Airtabletool,Compression,Airtable,Agent,Extractfromfile,Informationextractor,Httprequest,Documentdefaultdataloader,Chainllm,Vectorstoreqdrant,Textsplitterrecursivecharactertextsplitter,Chat,Executeworkflow,Memorybufferwindow,Toolvectorstore,  

---

### Extractfromfile Manual Create Webhook
**Filename:** `0601_Extractfromfile_Manual_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Vectorstoreinmemory, OpenAI, and Agent to create new records. Uses 28 nodes and integrates with 10 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (28 nodes)  
**Integrations:** Vectorstoreinmemory,OpenAI,Agent,Extractfromfile,Httprequest,Documentdefaultdataloader,WhatsApp,Textsplitterrecursivecharactertextsplitter,Memorybufferwindow,Toolvectorstore,  

---

### Wait Splitout Create Webhook
**Filename:** `0603_Wait_Splitout_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Converttofile, and Editimage to create new records. Uses 21 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (21 nodes)  
**Integrations:** Splitinbatches,Converttofile,Editimage,OpenAI,Google Drive,Splitout,Httprequest,Chainllm,  

---

### Jiratool Schedule Create Scheduled
**Filename:** `0604_Jiratool_Schedule_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Notiontool, OpenAI, and Textclassifier to create new records. Uses 36 nodes and integrates with 11 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (36 nodes)  
**Integrations:** Notiontool,OpenAI,Textclassifier,Agent,Jira,Outputparserstructured,Chainllm,Jiratool,Executeworkflow,Sentimentanalysis,Slack,  

---

### Code Itemlists Create Scheduled
**Filename:** `0605_Code_Itemlists_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Google Drive, and Movebinarydata to create new records. Uses 33 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (33 nodes)  
**Integrations:** Splitinbatches,Google Drive,Movebinarydata,N8N,Itemlists,  

---

### Splitout Aggregate Automate Triggered
**Filename:** `0607_Splitout_Aggregate_Automate_Triggered.json`  
**Description:** Manual workflow that orchestrates Splitout, Agent, and Anthropic for data processing. Uses 15 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (15 nodes)  
**Integrations:** Splitout,Agent,Anthropic,  

---

### Splitout Code Import Webhook
**Filename:** `0608_Splitout_Code_Import_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Splitout for data processing. Uses 19 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** high (19 nodes)  
**Integrations:** Httprequest,Splitout,  

---

### Wait Limit Import Webhook
**Filename:** `0609_Wait_Limit_Import_Webhook.json`  
**Description:** Manual workflow that orchestrates Httprequest, Splitinbatches, and Splitout for data processing. Uses 17 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** high (17 nodes)  
**Integrations:** Httprequest,Splitinbatches,Splitout,  

---

### Splitout Code Create Webhook
**Filename:** `0613_Splitout_Code_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Airtable, Google Sheets, and LinkedIn to create new records. Uses 33 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (33 nodes)  
**Integrations:** Airtable,Google Sheets,LinkedIn,Httprequest,Slack,  

---

### Splitout Manual Import Webhook
**Filename:** `0614_Splitout_Manual_Import_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Splitout for data processing. Uses 12 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (12 nodes)  
**Integrations:** Httprequest,Splitout,  

---

### Webhook Filemaker Create Webhook
**Filename:** `0615_Webhook_Filemaker_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Crypto, Webhook, and Respondtowebhook to create new records. Uses 11 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (11 nodes)  
**Integrations:** Crypto,Webhook,Respondtowebhook,Movebinarydata,Form Trigger,  

---

### Elasticsearch Cron Create Webhook
**Filename:** `0616_Elasticsearch_Cron_Create_Webhook.json`  
**Description:** Scheduled automation that connects Httprequest and Elasticsearch to create new records. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (5 nodes)  
**Integrations:** Httprequest,Elasticsearch,  

---

### Manual Noop Automation Webhook
**Filename:** `0617_Manual_Noop_Automation_Webhook.json`  
**Description:** Manual workflow that orchestrates Httprequest, Cal.com, and Google Drive for data processing. Uses 18 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** high (18 nodes)  
**Integrations:** Httprequest,Cal.com,Google Drive,  

---

### Splitout Code Create Scheduled
**Filename:** `0618_Splitout_Code_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Spotify, Splitout, and Google Sheets to create new records. Uses 37 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (37 nodes)  
**Integrations:** Spotify,Splitout,Google Sheets,Anthropic,Outputparserstructured,Httprequest,Chainllm,Form Trigger,  

---

### Webhook Respondtowebhook Create Webhook
**Filename:** `0619_Webhook_Respondtowebhook_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Webhook, Respondtowebhook, and Httprequest to create new records. Uses 23 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (23 nodes)  
**Integrations:** Webhook,Respondtowebhook,Httprequest,Executeworkflow,Slack,  

---

### Comparedatasets Manual Create Triggered
**Filename:** `0623_Comparedatasets_Manual_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Youtube, Splitinbatches, and Spotify to create new records. Uses 12 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** Youtube,Splitinbatches,Spotify,Comparedatasets,  

---

### Splitout Code Create Triggered
**Filename:** `0625_Splitout_Code_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Converttofile, OpenAI, and Google Drive to create new records. Uses 39 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (39 nodes)  
**Integrations:** Converttofile,OpenAI,Google Drive,Google Sheets,Splitout,Form Trigger,  

---

### Wait Splitout Create Scheduled
**Filename:** `0627_Wait_Splitout_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Xml, and Thehiveproject to create new records. Uses 23 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (23 nodes)  
**Integrations:** Splitinbatches,Xml,Thehiveproject,Splitout,Httprequest,Server-Sent Events,N8N,Executeworkflow,  

---

### Backup workflows to git repository
**Filename:** `0628_Code_Schedule_Export_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Splitinbatches, GitHub, and N8N for data backup operations. Uses 17 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** high (17 nodes)  
**Integrations:** Splitinbatches,GitHub,N8N,  

---

### Wait Code Update Webhook
**Filename:** `0629_Wait_Code_Update_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Cal.com, and Respondtowebhook to update existing data. Uses 18 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (18 nodes)  
**Integrations:** Splitinbatches,Cal.com,Respondtowebhook,Webhook,  

---

### Code Webhook Create Scheduled
**Filename:** `0630_Code_Webhook_Create_Scheduled.json`  
**Description:** Scheduled automation that connects Httprequest and Webhook to create new records. Uses 9 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (9 nodes)  
**Integrations:** Httprequest,Webhook,  

---

### Webhook Manual Create Webhook
**Filename:** `0632_Webhook_Manual_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Server-Sent Events, Webhook, and OpenAI to create new records. Uses 21 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (21 nodes)  
**Integrations:** Server-Sent Events,Webhook,OpenAI,Supabase,  

---

### Splitout Manual Export Webhook
**Filename:** `0634_Splitout_Manual_Export_Webhook.json`  
**Description:** Manual workflow that orchestrates OpenAI, Splitout, and Google Sheets for data processing. Uses 7 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (7 nodes)  
**Integrations:** OpenAI,Splitout,Google Sheets,Httprequest,Form Trigger,  

---

### Wait Splitout Create Scheduled
**Filename:** `0640_Wait_Splitout_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Readwritefile, Splitinbatches, and Converttofile to create new records. Uses 19 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (19 nodes)  
**Integrations:** Readwritefile,Splitinbatches,Converttofile,Splitout,Extractfromfile,Httprequest,  

---

### Import Productboard Notes, Companies and Features into Snowflake
**Filename:** `0643_Splitout_Snowflake_Import_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Splitout, and Httprequest for data processing. Uses 35 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (35 nodes)  
**Integrations:** Splitinbatches,Splitout,Httprequest,Server-Sent Events,Snowflake,Slack,  

---

### Linear Project Status and End Date to Productboard feature Sync
**Filename:** `0645_Splitout_Code_Sync_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Splitout, and Linear to synchronize data. Uses 17 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** Httprequest,Splitout,Linear,Slack,  

---

### Extractfromfile Form Export Webhook
**Filename:** `0646_Extractfromfile_Form_Export_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Airtable, and Extractfromfile for data processing. Uses 23 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (23 nodes)  
**Integrations:** OpenAI,Airtable,Extractfromfile,Outputparserstructured,Httprequest,Chainllm,Form Trigger,Textclassifier,  

---

### Splitout Webhook Create Webhook
**Filename:** `0650_Splitout_Webhook_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Webhook, and Respondtowebhook to create new records. Uses 29 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (29 nodes)  
**Integrations:** Splitinbatches,Webhook,Respondtowebhook,Splitout,Httprequest,S3,Slack,  

---

### Code Schedule Create Webhook
**Filename:** `0651_Code_Schedule_Create_Webhook.json`  
**Description:** Scheduled automation that integrates with Httprequest to create new records. Uses 10 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (10 nodes)  
**Integrations:** Httprequest,  

---

### Splitout Schedule Create Scheduled
**Filename:** `0652_Splitout_Schedule_Create_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Markdown, GitHub, and Splitout to create new records. Uses 8 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (8 nodes)  
**Integrations:** Markdown,GitHub,Splitout,Emailsend,  

---

### Splitout Code Create Webhook
**Filename:** `0654_Splitout_Code_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Airtable, and Splitout to create new records. Uses 34 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (34 nodes)  
**Integrations:** Httprequest,Airtable,Splitout,Form Trigger,  

---

### Splitout Schedule Update Webhook
**Filename:** `0657_Splitout_Schedule_Update_Webhook.json`  
**Description:** Scheduled automation that orchestrates Httprequest, Splitout, and Google Sheets to update existing data. Uses 18 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** high (18 nodes)  
**Integrations:** Httprequest,Splitout,Google Sheets,  

---

### Code Schedule Create Scheduled
**Filename:** `0658_Code_Schedule_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Editimage, OpenAI, and Lmchatgroq to create new records. Uses 32 nodes and integrates with 11 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (32 nodes)  
**Integrations:** Editimage,OpenAI,Lmchatgroq,Airtable,Agent,Gmail,Executiondata,Form Trigger,Executeworkflow,Toolwikipedia,Memorybufferwindow,  

---

### Splitout Schedule Create Scheduled
**Filename:** `0659_Splitout_Schedule_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Airtable, and Splitout to create new records. Uses 24 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (24 nodes)  
**Integrations:** OpenAI,Airtable,Splitout,Gmail,Html,Informationextractor,Httprequest,Removeduplicates,  

---

### Calendly Noop Create Triggered
**Filename:** `0660_Calendly_Noop_Create_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Server-Sent Events, Cal.com, and Splitout to create new records. Uses 19 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (19 nodes)  
**Integrations:** Server-Sent Events,Cal.com,Splitout,  

---

### Calendly Noop Create Triggered
**Filename:** `0661_Calendly_Noop_Create_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Server-Sent Events, Cal.com, and Splitout to create new records. Uses 19 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (19 nodes)  
**Integrations:** Server-Sent Events,Cal.com,Splitout,  

---

### Manual Schedule Automation Scheduled
**Filename:** `0662_Manual_Schedule_Automation_Scheduled.json`  
**Description:** Scheduled automation that integrates with N8N for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (7 nodes)  
**Integrations:** N8N,  

---

### Splitout Schedule Update Scheduled
**Filename:** `0663_Splitout_Schedule_Update_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, GraphQL, and OpenAI to update existing data. Uses 19 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (19 nodes)  
**Integrations:** Splitinbatches,GraphQL,OpenAI,Airtable,Splitout,Informationextractor,Removeduplicates,Slack,  

---

### Splitout Limit Create Webhook
**Filename:** `0664_Splitout_Limit_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, OpenAI, and Splitout to create new records. Uses 27 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (27 nodes)  
**Integrations:** Markdown,OpenAI,Splitout,Html,Informationextractor,Httprequest,Wordpress,Chainllm,  

---

### Wait Splitout Create Webhook
**Filename:** `0668_Wait_Splitout_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Splitinbatches, and Google Drive to create new records. Uses 88 nodes and integrates with 15 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (88 nodes)  
**Integrations:** Lmchatgooglegemini,Splitinbatches,Google Drive,Splitout,Google Sheets,Informationextractor,Executiondata,Httprequest,Documentdefaultdataloader,Vectorstoreqdrant,Textsplitterrecursivecharactertextsplitter,Removeduplicates,Executeworkflow,Embeddingsgooglegemini,Textclassifier,  

---

### Code Webhook Create Webhook
**Filename:** `0669_Code_Webhook_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Google Drive, and Airtable to create new records. Uses 18 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (18 nodes)  
**Integrations:** OpenAI,Google Drive,Airtable,Webhook,Httprequest,  

---

### Code Converttofile Create Webhook
**Filename:** `0671_Code_Converttofile_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Converttofile, OpenAI, and Gmail to create new records. Uses 25 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (25 nodes)  
**Integrations:** Converttofile,OpenAI,Gmail,Jira,Httprequest,Outlook,Form Trigger,  

---

### Webhook Schedule Update Webhook
**Filename:** `0672_Webhook_Schedule_Update_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Vectorstoreinmemory, and Toolhttprequest to update existing data. Uses 34 nodes and integrates with 11 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (34 nodes)  
**Integrations:** Splitinbatches,Vectorstoreinmemory,Toolhttprequest,OpenAI,Webhook,Agent,Chainretrievalqa,Httprequest,N8N,Retrievervectorstore,Memorybufferwindow,  

---

### Limit Code Create Webhook
**Filename:** `0673_Limit_Code_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Form Trigger, and Webhook to create new records. Uses 41 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (41 nodes)  
**Integrations:** Httprequest,Form Trigger,Webhook,Microsoftoutlook,  

---

### Limit Webhook Automation Webhook
**Filename:** `0674_Limit_Webhook_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that connects Gmail and Form Trigger for data processing. Uses 40 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (40 nodes)  
**Integrations:** Gmail,Form Trigger,  

---

### Limit Code Automation Scheduled
**Filename:** `0675_Limit_Code_Automation_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Google Sheets, Strava, and Removeduplicates for data processing. Uses 13 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (13 nodes)  
**Integrations:** Google Sheets,Strava,Removeduplicates,  

---

### Manual Stickynote Automate Triggered
**Filename:** `0678_Manual_Stickynote_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with GraphQL for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (7 nodes)  
**Integrations:** GraphQL,  

---

### Datetime Schedule Create Scheduled
**Filename:** `0682_Datetime_Schedule_Create_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Datetime, Slack, and Servicenow to create new records. Uses 14 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (14 nodes)  
**Integrations:** Datetime,Slack,Servicenow,  

---

### Code Webhook Update Webhook
**Filename:** `0686_Code_Webhook_Update_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Webhook, Respondtowebhook, and Extractfromfile to update existing data. Uses 17 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** Webhook,Respondtowebhook,Extractfromfile,Httprequest,Slack,  

---

### Aggregate Jotform Create Triggered
**Filename:** `0691_Aggregate_Jotform_Create_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Form Trigger to create new records. Uses 14 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (14 nodes)  
**Integrations:** Form Trigger,  

---

### Webhook Code Update Webhook
**Filename:** `0692_Webhook_Code_Update_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Webhook, and Agent to update existing data. Uses 39 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (39 nodes)  
**Integrations:** Lmchatgooglegemini,Webhook,Agent,Extractfromfile,Erpnext,Httprequest,Outlook,WhatsApp,  

---

### Code Webhook Create Webhook
**Filename:** `0693_Code_Webhook_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Googlesheetstool, OpenAI, and Webhook to create new records. Uses 27 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (27 nodes)  
**Integrations:** Googlesheetstool,OpenAI,Webhook,Agent,Googledocstool,Httprequest,Outlook,  

---

### Extractfromfile Manual Automation Webhook
**Filename:** `0694_Extractfromfile_Manual_Automation_Webhook.json`  
**Description:** Manual workflow that orchestrates Cal.com, Extractfromfile, and Google Drive for data processing. Uses 11 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (11 nodes)  
**Integrations:** Cal.com,Extractfromfile,Google Drive,  

---

### Aggregate Stickynote Create Webhook
**Filename:** `0695_Aggregate_Stickynote_Create_Webhook.json`  
**Description:** Webhook-triggered automation that integrates with Form Trigger to create new records. Uses 14 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (14 nodes)  
**Integrations:** Form Trigger,  

---

### Code Webhook Create Webhook
**Filename:** `0696_Code_Webhook_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, OpenAI, and Airtable to create new records. Uses 51 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (51 nodes)  
**Integrations:** Splitinbatches,OpenAI,Airtable,Webhook,Extractfromfile,Httprequest,Chainllm,  

---

### Aggregate Typeform Create Triggered
**Filename:** `0697_Aggregate_Typeform_Create_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Typeform to create new records. Uses 14 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (14 nodes)  
**Integrations:** Typeform,  

---

### Splitout Code Automation Triggered
**Filename:** `0698_Splitout_Code_Automation_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Splitout, Extractfromfile, and OpenAI for data processing. Uses 10 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** Splitout,Extractfromfile,OpenAI,Google Drive,  

---

### Splitout Code Create Webhook
**Filename:** `0699_Splitout_Code_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, OpenAI, and Webhook to create new records. Uses 45 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (45 nodes)  
**Integrations:** Splitinbatches,OpenAI,Webhook,Splitout,Extractfromfile,Httprequest,Chainllm,  

---

### Code Schedule Create Scheduled
**Filename:** `0706_Code_Schedule_Create_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Httprequest, Splitinbatches, and Notion to create new records. Uses 27 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** high (27 nodes)  
**Integrations:** Httprequest,Splitinbatches,Notion,  

---

### Code Filter Update Webhook
**Filename:** `0708_Code_Filter_Update_Webhook.json`  
**Description:** Manual workflow that orchestrates Cal.com, Google Sheets, and LinkedIn to update existing data. Uses 10 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (10 nodes)  
**Integrations:** Cal.com,Google Sheets,LinkedIn,  

---

### Manual Stickynote Automation Webhook
**Filename:** `0710_Manual_Stickynote_Automation_Webhook.json`  
**Description:** Manual workflow that integrates with Httprequest for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (6 nodes)  
**Integrations:** Httprequest,  

---

### Splitout Code Update Webhook
**Filename:** `0712_Splitout_Code_Update_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Splitout, and Ssh to update existing data. Uses 16 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (16 nodes)  
**Integrations:** Httprequest,Splitout,Ssh,Emailsend,  

---

### Wait Schedule Create Scheduled
**Filename:** `0715_Wait_Schedule_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Spotify, and Discord to create new records. Uses 54 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (54 nodes)  
**Integrations:** Splitinbatches,Spotify,Discord,Comparedatasets,Removeduplicates,Youtube,Supabase,  

---

### Wait Webhook Process Webhook
**Filename:** `0716_Wait_Webhook_Process_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Cal.com, and Webhook for data processing. Uses 23 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (23 nodes)  
**Integrations:** Httprequest,Cal.com,Webhook,  

---

### Stopanderror Splitout Create Webhook
**Filename:** `0719_Stopanderror_Splitout_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, Splitinbatches, and Lmchatgooglegemini to create new records. Uses 85 nodes and integrates with 12 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (85 nodes)  
**Integrations:** Markdown,Splitinbatches,Lmchatgooglegemini,OpenAI,Splitout,Outputparserstructured,Executiondata,Httprequest,Chainllm,Form Trigger,Executeworkflow,Notion,  

---

### Schedule Filter Create Scheduled
**Filename:** `0720_Schedule_Filter_Create_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Splitinbatches, N8N, and Google Drive to create new records. Uses 10 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (10 nodes)  
**Integrations:** Splitinbatches,N8N,Google Drive,Converttofile,  

---

### Automate Drive-To-Store Lead Generation System (with coupon) on SuiteCRM
**Filename:** `0722_Webhook_Respondtowebhook_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Webhook, Google Sheets, and Respondtowebhook for data processing. Uses 16 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (16 nodes)  
**Integrations:** Webhook,Google Sheets,Respondtowebhook,Httprequest,Form Trigger,  

---

### Splitout Code Create Webhook
**Filename:** `0724_Splitout_Code_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Notion, and Splitout to create new records. Uses 12 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (12 nodes)  
**Integrations:** Httprequest,Notion,Splitout,  

---

### Splitout Code Update Triggered
**Filename:** `0725_Splitout_Code_Update_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Splitout, Chainllm, and Executeworkflow to update existing data. Uses 18 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (18 nodes)  
**Integrations:** Splitout,Chainllm,Executeworkflow,Lmollama,Lmchatollama,  

---

### Code Schedule Update Scheduled
**Filename:** `0726_Code_Schedule_Update_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Ssh, Emailsend, and Form Trigger to update existing data. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (7 nodes)  
**Integrations:** Ssh,Emailsend,Form Trigger,  

---

### Stickynote Create Webhook
**Filename:** `0727_Stickynote_Create_Webhook.json`  
**Description:** Manual workflow that orchestrates Toolhttprequest, Manualchat, and Agent to create new records. Uses 10 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (10 nodes)  
**Integrations:** Toolhttprequest,Manualchat,Agent,Toolwikipedia,Memorybufferwindow,Lmchatollama,  

---

### Splitout Limit Create Webhook
**Filename:** `0731_Splitout_Limit_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Splitout, and Html to create new records. Uses 22 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (22 nodes)  
**Integrations:** Lmchatgooglegemini,Splitout,Html,Outputparserstructured,Httprequest,Chainllm,Textclassifier,  

---

### Form Code Create Triggered
**Filename:** `0733_Form_Code_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Google Drive and Form Trigger to create new records. Uses 13 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (13 nodes)  
**Integrations:** Google Drive,Form Trigger,  

---

### N8N Español - Ejemplos
**Filename:** `0737_Manual_Executecommand_Automation_Triggered.json`  
**Description:** Manual workflow that integrates with Executecommand for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (7 nodes)  
**Integrations:** Executecommand,  

---

### YogiAI
**Filename:** `0740_Splitout_Code_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Googlesheetstool, OpenAI, and Google Sheets for data processing. Uses 31 nodes and integrates with 9 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (31 nodes)  
**Integrations:** Googlesheetstool,OpenAI,Google Sheets,Agent,Splitout,Outputparserstructured,Httprequest,Chainllm,Outputparserautofixing,  

---

### RAG AI Agent with Milvus and Cohere
**Filename:** `0741_Extractfromfile_Stickynote_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Embeddingscohere, Google Drive, and OpenAI for data processing. Uses 14 nodes and integrates with 10 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** Embeddingscohere,Google Drive,OpenAI,Vectorstoremilvus,Agent,Extractfromfile,Documentdefaultdataloader,Chat,Textsplitterrecursivecharactertextsplitter,Memorybufferwindow,  

---

### Exponential Backoff for Google APIs
**Filename:** `0743_Stopanderror_Wait_Automation_Triggered.json`  
**Description:** Manual workflow that connects Splitinbatches and Google Sheets for data processing. Uses 8 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (8 nodes)  
**Integrations:** Splitinbatches,Google Sheets,  

---

### Writebinaryfile Spreadsheetfile Automate
**Filename:** `0747_Writebinaryfile_Spreadsheetfile_Automate.json`  
**Description:** Manual workflow that orchestrates PostgreSQL, Spreadsheetfile, and Writebinaryfile for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** PostgreSQL,Spreadsheetfile,Writebinaryfile,  

---

### Luma AI Dream Machine - Simple v1 - AK
**Filename:** `0753_Code_Executiondata_Automation_Webhook.json`  
**Description:** Manual workflow that orchestrates Httprequest, Airtable, and Executiondata for data processing. Uses 8 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (8 nodes)  
**Integrations:** Httprequest,Airtable,Executiondata,  

---

### Schedule Manual Monitor Scheduled
**Filename:** `0758_Schedule_Manual_Monitor_Scheduled.json`  
**Description:** Scheduled automation that connects Cal.com and Salesforce for monitoring and reporting. Uses 11 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (11 nodes)  
**Integrations:** Cal.com,Salesforce,  

---

### Splitout Comparedatasets Create Triggered
**Filename:** `0759_Splitout_Comparedatasets_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Google Sheets, Executeworkflow, and Cal.com to create new records. Uses 26 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (26 nodes)  
**Integrations:** Google Sheets,Executeworkflow,Cal.com,Notion,Gong,  

---

### Aggregate Stickynote Create Triggered
**Filename:** `0762_Aggregate_Stickynote_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Agent, and Outputparserstructured to create new records. Uses 27 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (27 nodes)  
**Integrations:** OpenAI,Agent,Outputparserstructured,Server-Sent Events,Executeworkflow,Cal.com,  

---

### Wait Splitout Create Webhook
**Filename:** `0763_Wait_Splitout_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Executeworkflow, Cal.com, and Notion to create new records. Uses 37 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (37 nodes)  
**Integrations:** Executeworkflow,Cal.com,Notion,Splitout,  

---

### Wait Splitout Create Triggered
**Filename:** `0764_Wait_Splitout_Create_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Executeworkflow, Notion, and Splitout to create new records. Uses 24 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (24 nodes)  
**Integrations:** Executeworkflow,Notion,Splitout,  

---

### Wait Splitout Create Triggered
**Filename:** `0765_Wait_Splitout_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Executeworkflow, Cal.com, and Notion to create new records. Uses 19 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (19 nodes)  
**Integrations:** Executeworkflow,Cal.com,Notion,Splitout,  

---

### Wait Limit Update Webhook
**Filename:** `0766_Wait_Limit_Update_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Google Sheets, OpenAI, and Slack to update existing data. Uses 21 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (21 nodes)  
**Integrations:** Google Sheets,OpenAI,Slack,  

---

### Manual Stickynote Create Webhook
**Filename:** `0770_Manual_Stickynote_Create_Webhook.json`  
**Description:** Manual workflow that integrates with Httprequest to create new records. Uses 10 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (10 nodes)  
**Integrations:** Httprequest,  

---

### Splitout Filter Process Webhook
**Filename:** `0772_Splitout_Filter_Process_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Splitinbatches, and Splitout for data processing. Uses 28 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (28 nodes)  
**Integrations:** Lmchatgooglegemini,Splitinbatches,Splitout,Agent,Httprequest,Form Trigger,Cal.com,  

---

### Automatically Update YouTube Video Descriptions with Inserted Text
**Filename:** `0773_Code_Manual_Update_Triggered.json`  
**Description:** Manual workflow that connects Youtube and Splitinbatches to update existing data. Uses 9 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (9 nodes)  
**Integrations:** Youtube,Splitinbatches,  

---

### Splitout Code Automate Webhook
**Filename:** `0774_Splitout_Code_Automate_Webhook.json`  
**Description:** Scheduled automation that connects Httprequest and Splitout for data processing. Uses 28 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** high (28 nodes)  
**Integrations:** Httprequest,Splitout,  

---

### mails2notion V2
**Filename:** `0777_Code_Filter_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Airtable, and Agent for data processing. Uses 38 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (38 nodes)  
**Integrations:** OpenAI,Airtable,Agent,Gmail,Outputparserstructured,Server-Sent Events,Cal.com,Notion,  

---

### Splitout Filter Process Webhook
**Filename:** `0780_Splitout_Filter_Process_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Xml, Lmchatopenrouter, and Lmchatanthropic for data processing. Uses 51 nodes and integrates with 12 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (51 nodes)  
**Integrations:** Xml,Lmchatopenrouter,Lmchatanthropic,Airtable,Splitout,Html,Informationextractor,Outputparserstructured,Httprequest,Chainllm,Outputparserautofixing,Executeworkflow,  

---

### Code Schedule Export Scheduled
**Filename:** `0781_Code_Schedule_Export_Scheduled.json`  
**Description:** Scheduled automation that connects N8N and Google Drive for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (6 nodes)  
**Integrations:** N8N,Google Drive,  

---

### Code Form Automation Webhook
**Filename:** `0784_Code_Form_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Google Drive, and Form Trigger for data processing. Uses 8 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (8 nodes)  
**Integrations:** Httprequest,Google Drive,Form Trigger,  

---

### Stopanderror Stickynote Create Webhook
**Filename:** `0786_Stopanderror_Stickynote_Create_Webhook.json`  
**Description:** Webhook-triggered automation that integrates with Webhook to create new records. Uses 15 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (15 nodes)  
**Integrations:** Webhook,  

---

### Googletranslate Noop Create Webhook
**Filename:** `0788_Googletranslate_Noop_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Google Drive, and Google Sheets to create new records. Uses 22 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (22 nodes)  
**Integrations:** OpenAI,Google Drive,Google Sheets,Agent,Outputparserstructured,Googletranslate,Httprequest,Cal.com,  

---

### Splitout Schedule Create Webhook
**Filename:** `0790_Splitout_Schedule_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Readwritefile, Converttofile, and Splitout to create new records. Uses 24 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (24 nodes)  
**Integrations:** Readwritefile,Converttofile,Splitout,Extractfromfile,Httprequest,Form Trigger,  

---

### Stopanderror Splitout Create Webhook
**Filename:** `0791_Stopanderror_Splitout_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Rssfeedread, Splitout, and Emailsend to create new records. Uses 18 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (18 nodes)  
**Integrations:** Rssfeedread,Splitout,Emailsend,Httprequest,Youtube,  

---

### Splitout Code Monitor Scheduled
**Filename:** `0792_Splitout_Code_Monitor_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Httprequest, Splitout, and Google Sheets for monitoring and reporting. Uses 11 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (11 nodes)  
**Integrations:** Httprequest,Splitout,Google Sheets,  

---

### Code Schedule Create Scheduled
**Filename:** `0794_Code_Schedule_Create_Scheduled.json`  
**Description:** Scheduled automation that connects Httprequest and Google Sheets to create new records. Uses 11 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (11 nodes)  
**Integrations:** Httprequest,Google Sheets,  

---

### Splitout Code Monitor Webhook
**Filename:** `0797_Splitout_Code_Monitor_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Splitout, and Google Sheets for monitoring and reporting. Uses 13 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (13 nodes)  
**Integrations:** Httprequest,Splitout,Google Sheets,Converttofile,  

---

### Splitout Code Automation Webhook
**Filename:** `0798_Splitout_Code_Automation_Webhook.json`  
**Description:** Manual workflow that orchestrates Httprequest, Splitout, and Readwritefile for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (7 nodes)  
**Integrations:** Httprequest,Splitout,Readwritefile,  

---

### Splitout Executecommand Automate Scheduled
**Filename:** `0799_Splitout_Executecommand_Automate_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Executecommand, Splitout, and N8N for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (7 nodes)  
**Integrations:** Executecommand,Splitout,N8N,  

---

### Filter Schedule Import Webhook
**Filename:** `0801_Filter_Schedule_Import_Webhook.json`  
**Description:** Scheduled automation that orchestrates Httprequest, Splitinbatches, and Shopify for data processing. Uses 13 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (13 nodes)  
**Integrations:** Httprequest,Splitinbatches,Shopify,  

---

### Webhook Nocodb Create Webhook
**Filename:** `0802_Webhook_Nocodb_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Nocodb, Webhook, and Dropbox to create new records. Uses 20 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (20 nodes)  
**Integrations:** Nocodb,Webhook,Dropbox,Executeworkflow,  

---

### Googlebigquery Stickynote Automate Triggered
**Filename:** `0806_Googlebigquery_Stickynote_Automate_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Agent, and Chat for data processing. Uses 12 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** OpenAI,Agent,Chat,Executeworkflow,Cal.com,Memorybufferwindow,Googlebigquery,  

---

### Splitout Schedule Automation Webhook
**Filename:** `0810_Splitout_Schedule_Automation_Webhook.json`  
**Description:** Scheduled automation that orchestrates Httprequest, Splitinbatches, and Splitout for data processing. Uses 12 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (12 nodes)  
**Integrations:** Httprequest,Splitinbatches,Splitout,  

---

### Respondtowebhook Webhook Automate Webhook
**Filename:** `0811_Respondtowebhook_Webhook_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that integrates with Webhook for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Webhook,  

---

### Webhook Respondtowebhook Process Webhook
**Filename:** `0813_Webhook_Respondtowebhook_Process_Webhook.json`  
**Description:** Webhook-triggered automation that integrates with Webhook for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Webhook,  

---

### Splitout Code Automation Scheduled
**Filename:** `0816_Splitout_Code_Automation_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Discord, and Lmchatopenai for data processing. Uses 30 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (30 nodes)  
**Integrations:** Splitinbatches,Discord,Lmchatopenai,Splitout,Removeduplicates,Executeworkflow,Textclassifier,  

---

### Schedule Removeduplicates Create Webhook
**Filename:** `0817_Schedule_Removeduplicates_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, Crypto, and Google Drive to create new records. Uses 14 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** Markdown,Crypto,Google Drive,Google Sheets,Gmail,Html,Httprequest,Removeduplicates,  

---

### Splitout Schedule Create Scheduled
**Filename:** `0819_Splitout_Schedule_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, OpenAI, and Splitout to create new records. Uses 36 nodes and integrates with 10 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (36 nodes)  
**Integrations:** Splitinbatches,OpenAI,Splitout,Vectorstoresupabase,Agent,Jira,Informationextractor,Documentdefaultdataloader,Textsplitterrecursivecharactertextsplitter,Removeduplicates,  

---

### Manual Noop Create Triggered
**Filename:** `0821_Manual_Noop_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Lmchatgooglegemini, and Hubspot to create new records. Uses 17 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** Splitinbatches,Lmchatgooglegemini,Hubspot,Gmail,Informationextractor,  

---

### Wait Splitout Automation Webhook
**Filename:** `0826_Wait_Splitout_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Memorymanager, Splitout, and Anthropic for data processing. Uses 39 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (39 nodes)  
**Integrations:** Memorymanager,Splitout,Anthropic,Executiondata,Httprequest,Executeworkflow,Memorybufferwindow,  

---

### Webhook Code Create Webhook
**Filename:** `0829_Webhook_Code_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Form Trigger, Airtable, and Webhook to create new records. Uses 92 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (92 nodes)  
**Integrations:** Form Trigger,Airtable,Webhook,Respondtowebhook,Itemlists,Cal.com,Google Calendar,  

---

### Wait Code Monitor Webhook
**Filename:** `0831_Wait_Code_Monitor_Webhook.json`  
**Description:** Webhook-triggered automation that connects Webhook and Redis for monitoring and reporting. Uses 18 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (18 nodes)  
**Integrations:** Webhook,Redis,  

---

### Splitout Limit Create Webhook
**Filename:** `0832_Splitout_Limit_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Splitout, and Httprequest to create new records. Uses 40 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (40 nodes)  
**Integrations:** Splitinbatches,Splitout,Httprequest,N8N,Removeduplicates,Form Trigger,  

---

### Splitout Schedule Create Webhook
**Filename:** `0833_Splitout_Schedule_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, OpenAI, and Splitout to create new records. Uses 33 nodes and integrates with 9 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (33 nodes)  
**Integrations:** Splitinbatches,OpenAI,Splitout,Html,Httprequest,Chainllm,Microsoftoutlook,Removeduplicates,Microsoftexcel,  

---

### Wait Code Create Scheduled
**Filename:** `0836_Wait_Code_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, Splitinbatches, and Google Sheets to create new records. Uses 23 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (23 nodes)  
**Integrations:** Markdown,Splitinbatches,Google Sheets,Rssfeedread,  

---

### Receive updates when a sale is made in Gumroad
**Filename:** `0843_Gumroad_Update_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Gumroad to update existing data. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Gumroad,  

---

### Webhook Filter Export Webhook
**Filename:** `0845_Webhook_Filter_Export_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Airtable, Webhook, and Google Sheets for data processing. Uses 14 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** Airtable,Webhook,Google Sheets,Cal.com,Notion,  

---

### Splitout Code Create Webhook
**Filename:** `0846_Splitout_Code_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Converttofile, and Splitout to create new records. Uses 18 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (18 nodes)  
**Integrations:** Lmchatgooglegemini,Converttofile,Splitout,Gmail,Informationextractor,Httprequest,  

---

### Code Filter Update Triggered
**Filename:** `0848_Code_Filter_Update_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Google Drive to update existing data. Uses 20 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (20 nodes)  
**Integrations:** Google Drive,  

---

### Filter Extractfromfile Create Triggered
**Filename:** `0849_Filter_Extractfromfile_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, OpenAI, and Google Drive to create new records. Uses 19 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (19 nodes)  
**Integrations:** Splitinbatches,OpenAI,Google Drive,Google Sheets,Agent,Extractfromfile,  

---

### Code Extractfromfile Monitor Triggered
**Filename:** `0851_Code_Extractfromfile_Monitor_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Extractfromfile, and Informationextractor for monitoring and reporting. Uses 22 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (22 nodes)  
**Integrations:** OpenAI,Extractfromfile,Informationextractor,Outlook,Form Trigger,Textclassifier,Microsoftoutlook,  

---

### Manual Executecommand Automate Triggered
**Filename:** `0853_Manual_Executecommand_Automate_Triggered.json`  
**Description:** Manual workflow that connects Executecommand and Readbinaryfiles for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Executecommand,Readbinaryfiles,  

---

### Splitout Filter Create Scheduled
**Filename:** `0854_Splitout_Filter_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Google Drive, and Google Sheets to create new records. Uses 19 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (19 nodes)  
**Integrations:** Splitinbatches,Google Drive,Google Sheets,Splitout,Gmail,  

---

### Code Schedule Update Scheduled
**Filename:** `0856_Code_Schedule_Update_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Httprequest, Google Sheets, and Form Trigger to update existing data. Uses 10 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (10 nodes)  
**Integrations:** Httprequest,Google Sheets,Form Trigger,  

---

### Wait Schedule Update Scheduled
**Filename:** `0858_Wait_Schedule_Update_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Google Sheets, and Anthropic to update existing data. Uses 21 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (21 nodes)  
**Integrations:** Splitinbatches,Google Sheets,Anthropic,LinkedIn,Httprequest,Chainllm,Cal.com,  

---

### Splitout Code Create Scheduled
**Filename:** `0859_Splitout_Code_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Rssfeedread, and Splitout to create new records. Uses 23 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (23 nodes)  
**Integrations:** OpenAI,Rssfeedread,Splitout,Html,Emailsend,Httprequest,Cal.com,  

---

### Manual Stickynote Update Triggered
**Filename:** `0861_Manual_Stickynote_Update_Triggered.json`  
**Description:** Manual workflow that connects Quickchart and Google Drive to update existing data. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Quickchart,Google Drive,  

---

### Wait Code Create Webhook
**Filename:** `0862_Wait_Code_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Cal.com, and Google Sheets to create new records. Uses 15 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (15 nodes)  
**Integrations:** Httprequest,Cal.com,Google Sheets,Form Trigger,  

---

### Code Schedule Import Webhook
**Filename:** `0863_Code_Schedule_Import_Webhook.json`  
**Description:** Scheduled automation that connects Httprequest and Google Sheets for data processing. Uses 12 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (12 nodes)  
**Integrations:** Httprequest,Google Sheets,  

---

### Wait Splitout Create Webhook
**Filename:** `0866_Wait_Splitout_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Google Sheets, and Splitout to create new records. Uses 17 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** OpenAI,Google Sheets,Splitout,Httprequest,Chainllm,Form Trigger,Cal.com,  

---

### Wait Splitout Create Webhook
**Filename:** `0867_Wait_Splitout_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Google Sheets, and Splitout to create new records. Uses 16 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (16 nodes)  
**Integrations:** OpenAI,Google Sheets,Splitout,Httprequest,Chainllm,Form Trigger,Cal.com,  

---

### Wait Filter Create Webhook
**Filename:** `0868_Wait_Filter_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Google Sheets, and Gmail to create new records. Uses 18 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (18 nodes)  
**Integrations:** OpenAI,Google Sheets,Gmail,Httprequest,Chainllm,Form Trigger,Cal.com,  

---

### Splitout Code Create Webhook
**Filename:** `0877_Splitout_Code_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Splitout, and Toolworkflow to create new records. Uses 44 nodes and integrates with 9 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (44 nodes)  
**Integrations:** OpenAI,Splitout,Toolworkflow,Httprequest,Documentdefaultdataloader,Mcp,Vectorstoreqdrant,Textsplitterrecursivecharactertextsplitter,Executeworkflow,  

---

### Limit Code Create Webhook
**Filename:** `0880_Limit_Code_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Splitinbatches, and Google Sheets to create new records. Uses 14 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** Httprequest,Splitinbatches,Google Sheets,OpenAI,  

---

### Splitout Code Create Webhook
**Filename:** `0883_Splitout_Code_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, OpenAI, and Google Sheets to create new records. Uses 21 nodes and integrates with 9 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (21 nodes)  
**Integrations:** Splitinbatches,OpenAI,Google Sheets,Agent,Gmail,Html,Splitout,Outputparserstructured,Httprequest,  

---

### Manual Stickynote Import Webhook
**Filename:** `0886_Manual_Stickynote_Import_Webhook.json`  
**Description:** Manual workflow that integrates with Httprequest for data processing. Uses 9 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (9 nodes)  
**Integrations:** Httprequest,  

---

### Manual Stickynote Create Webhook
**Filename:** `0887_Manual_Stickynote_Create_Webhook.json`  
**Description:** Manual workflow that orchestrates Cal.com, Converttofile, and OpenAI to create new records. Uses 11 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (11 nodes)  
**Integrations:** Cal.com,Converttofile,OpenAI,  

---

### Code Manual Create Triggered
**Filename:** `0891_Code_Manual_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Executeworkflow and Google Drive to create new records. Uses 16 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (16 nodes)  
**Integrations:** Executeworkflow,Google Drive,  

---

### Webhook Code Create Webhook
**Filename:** `0892_Webhook_Code_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Google Drive, and Webhook to create new records. Uses 20 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (20 nodes)  
**Integrations:** OpenAI,Google Drive,Webhook,Gmail,Readpdf,  

---

### Stickynote Emailreadimap Create
**Filename:** `0893_Stickynote_Emailreadimap_Create.json`  
**Description:** Complex multi-step automation that orchestrates Hubspot, OpenAI, and Email (IMAP) to create new records. Uses 13 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (13 nodes)  
**Integrations:** Hubspot,OpenAI,Email (IMAP),Outputparserstructured,Chainllm,  

---

### Splitout Code Create Webhook
**Filename:** `0895_Splitout_Code_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitout, Google Sheets, and Extractfromfile to create new records. Uses 36 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (36 nodes)  
**Integrations:** Splitout,Google Sheets,Extractfromfile,Httprequest,Form Trigger,  

---

### Facebookleadads Stickynote Automate Triggered
**Filename:** `0896_Facebookleadads_Stickynote_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Facebook for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Facebook,  

---

### Code Schedule Create Scheduled
**Filename:** `0898_Code_Schedule_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Toolthink, OpenAI, and Lmchatopenai to create new records. Uses 45 nodes and integrates with 13 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (45 nodes)  
**Integrations:** Toolthink,OpenAI,Lmchatopenai,Agent,Toolworkflow,Html,Emailsend,Outputparserstructured,Httprequest,Form Trigger,Executeworkflow,Cal.com,Memorybufferwindow,  

---

### Respondtowebhook Stickynote Automate Webhook
**Filename:** `0900_Respondtowebhook_Stickynote_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that integrates with Webhook for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Webhook,  

---

### Splitout Code Create Scheduled
**Filename:** `0902_Splitout_Code_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, OpenAI, and Google Sheets to create new records. Uses 35 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (35 nodes)  
**Integrations:** Splitinbatches,OpenAI,Google Sheets,Splitout,Httprequest,Server-Sent Events,Slack,  

---

### Wait Code Create Webhook
**Filename:** `0904_Wait_Code_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Splitinbatches, and Respondtowebhook to create new records. Uses 11 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (11 nodes)  
**Integrations:** Httprequest,Splitinbatches,Respondtowebhook,Chat,  

---

### Wait Schedule Create Webhook
**Filename:** `0905_Wait_Schedule_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Instagram, and Google Sheets to create new records. Uses 26 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (26 nodes)  
**Integrations:** Lmchatgooglegemini,Instagram,Google Sheets,Outputparserstructured,Httprequest,Chainllm,  

---

### Schedule Removeduplicates Create Scheduled
**Filename:** `0907_Schedule_Removeduplicates_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, OpenAI, and Jira to create new records. Uses 27 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (27 nodes)  
**Integrations:** Splitinbatches,OpenAI,Jira,Outputparserstructured,Chainllm,Removeduplicates,  

---

### Manual Stickynote Automate Triggered
**Filename:** `0908_Manual_Stickynote_Automate_Triggered.json`  
**Description:** Manual workflow that for data processing. Uses 9 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (9 nodes)  
**Integrations:** None  

---

### Manual Stickynote Process Triggered
**Filename:** `0909_Manual_Stickynote_Process_Triggered.json`  
**Description:** Manual workflow that for data processing. Uses 9 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (9 nodes)  
**Integrations:** None  

---

### Schedule Removeduplicates Create Scheduled
**Filename:** `0911_Schedule_Removeduplicates_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, OpenAI, and Jira to create new records. Uses 12 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** Markdown,OpenAI,Jira,Outputparserstructured,Chainllm,Removeduplicates,Microsoftoutlook,  

---

### Schedule Removeduplicates Create Scheduled
**Filename:** `0912_Schedule_Removeduplicates_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, Linear, and OpenAI to create new records. Uses 13 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (13 nodes)  
**Integrations:** Markdown,Linear,OpenAI,Gmail,Outputparserstructured,Chainllm,Removeduplicates,  

---

### Splitout Code Create Webhook
**Filename:** `0913_Splitout_Code_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Lmchatopenrouter, and Google Sheets to create new records. Uses 11 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (11 nodes)  
**Integrations:** Splitinbatches,Lmchatopenrouter,Google Sheets,Splitout,Outputparserstructured,Httprequest,Chainllm,  

---

### Webhook Respondtowebhook Create Webhook
**Filename:** `0914_Webhook_Respondtowebhook_Create_Webhook.json`  
**Description:** Webhook-triggered automation that connects Converttofile and Webhook to create new records. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (6 nodes)  
**Integrations:** Converttofile,Webhook,  

---

### Splitout Code Create Webhook
**Filename:** `0915_Splitout_Code_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitout, Google Sheets, and Gmail to create new records. Uses 46 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (46 nodes)  
**Integrations:** Splitout,Google Sheets,Gmail,LinkedIn,Httprequest,Executeworkflow,  

---

### Filter Whatsapp Create Triggered
**Filename:** `0917_Filter_Whatsapp_Create_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with WhatsApp to create new records. Uses 12 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (12 nodes)  
**Integrations:** WhatsApp,  

---

### Splitout Extractfromfile Create Webhook
**Filename:** `0919_Splitout_Extractfromfile_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Converttofile, Splitout, and Gmail to create new records. Uses 21 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (21 nodes)  
**Integrations:** Converttofile,Splitout,Gmail,Html,Extractfromfile,Httprequest,Form Trigger,  

---

### Code Webhook Create Webhook
**Filename:** `0922_Code_Webhook_Create_Webhook.json`  
**Description:** Webhook-triggered automation that connects Webhook and Respondtowebhook to create new records. Uses 16 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (16 nodes)  
**Integrations:** Webhook,Respondtowebhook,  

---

### Code Respondtowebhook Process Webhook
**Filename:** `0924_Code_Respondtowebhook_Process_Webhook.json`  
**Description:** Webhook-triggered automation that integrates with Webhook for data processing. Uses 12 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (12 nodes)  
**Integrations:** Webhook,  

---

### Prevent concurrent workflow runs using Redis
**Filename:** `0925_Stopanderror_Wait_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that connects Executeworkflow and Redis for data processing. Uses 43 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (43 nodes)  
**Integrations:** Executeworkflow,Redis,  

---

### Code Webhook Create Webhook
**Filename:** `0926_Code_Webhook_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Google Sheets, Webhook, and Gmail to create new records. Uses 51 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (51 nodes)  
**Integrations:** Google Sheets,Webhook,Gmail,Form Trigger,Cal.com,Supabase,  

---

### Manual N8n Automate Triggered
**Filename:** `0928_Manual_N8N_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with N8N for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** N8N,  

---

### OpenAI e-mail classification - application
**Filename:** `0929_Noop_Extractfromfile_Automation.json`  
**Description:** Manual workflow that orchestrates OpenAI, Email (IMAP), and Extractfromfile for data processing. Uses 10 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (10 nodes)  
**Integrations:** OpenAI,Email (IMAP),Extractfromfile,Informationextractor,Textclassifier,  

---

### PostgreSQL export to CSV
**Filename:** `0930_Manual_Spreadsheetfile_Export_Triggered.json`  
**Description:** Manual workflow that connects PostgreSQL and Spreadsheetfile for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** PostgreSQL,Spreadsheetfile,  

---

### Extract And Decode Google News RSS URLs to Clean Article Links
**Filename:** `0932_Limit_Code_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Cal.com, and Rssfeedread for data processing. Uses 20 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (20 nodes)  
**Integrations:** Httprequest,Cal.com,Rssfeedread,Html,  

---

### Create AI-Ready Vector Datasets for LLMs with Bright Data, Gemini & Pinecone
**Filename:** `0933_Manual_Stickynote_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Webhook, and Agent to create new records. Uses 21 nodes and integrates with 10 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (21 nodes)  
**Integrations:** Lmchatgooglegemini,Webhook,Agent,Vectorstorepinecone,Outputparserstructured,Httprequest,Documentdefaultdataloader,Textsplitterrecursivecharactertextsplitter,Form Trigger,Embeddingsgooglegemini,  

---

### Filter Schedule Create Scheduled
**Filename:** `0948_Filter_Schedule_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Splitinbatches, and Google Sheets to create new records. Uses 17 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** Lmchatgooglegemini,Splitinbatches,Google Sheets,Gmail,Outputparserstructured,Chainllm,  

---

### Manual Htmlextract Automation Webhook
**Filename:** `0954_Manual_Htmlextract_Automation_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Htmlextract for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Httprequest,Htmlextract,  

---

### LLM Chaining examples
**Filename:** `0958_Splitout_Webhook_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, Memorymanager, and Splitout for data processing. Uses 38 nodes and integrates with 9 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (38 nodes)  
**Integrations:** Markdown,Memorymanager,Splitout,Webhook,Anthropic,Agent,Httprequest,Chainllm,Memorybufferwindow,  

---

### Receive updates when an email is bounced or opened
**Filename:** `0968_Postmark_Update_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Postmark to update existing data. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Postmark,  

---

### ⚡AI-Powered YouTube Playlist & Video Summarization and Analysis v2
**Filename:** `0971_Limit_Splitout_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Splitout, and Agent for data processing. Uses 72 nodes and integrates with 15 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (72 nodes)  
**Integrations:** Lmchatgooglegemini,Splitout,Agent,Outputparserstructured,Httprequest,Chainllm,Vectorstoreqdrant,Documentdefaultdataloader,Textsplitterrecursivecharactertextsplitter,Chat,YouTube,Embeddingsgooglegemini,Memorybufferwindow,Redis,Toolvectorstore,  

---

### Import Odoo Product Images from Google Drive
**Filename:** `0977_Odoo_Code_Import_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Extractfromfile, Googlechat, and Odoo for data processing. Uses 19 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (19 nodes)  
**Integrations:** Extractfromfile,Googlechat,Odoo,Google Drive,  

---

### comentarios automaticos
**Filename:** `0979_Webhook_Filter_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Webhook, and Agent for data processing. Uses 14 nodes and integrates with 4 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** Httprequest,Webhook,Agent,Lmchatopenrouter,  

---

### Google Page Entity Extraction Template
**Filename:** `0980_Code_Webhook_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that connects Httprequest and Webhook for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (6 nodes)  
**Integrations:** Httprequest,Webhook,  

---

### Manual Facebookgraphapi Automation Triggered
**Filename:** `0987_Manual_Facebookgraphapi_Automation_Triggered.json`  
**Description:** Manual workflow that integrates with Facebook for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Facebook,  

---

### Manual Writebinaryfile Automate Triggered
**Filename:** `0988_Manual_Writebinaryfile_Automate_Triggered.json`  
**Description:** Manual workflow that connects Writebinaryfile and Google Drive for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Writebinaryfile,Google Drive,  

---

### Manual Mailgun Automate Triggered
**Filename:** `0995_Manual_Mailgun_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Mailgun for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Mailgun,  

---

### Bitbucket Automate Triggered
**Filename:** `0999_Bitbucket_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Bitbucket for data processing. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Bitbucket,  

---

### Acuityscheduling Automate Triggered
**Filename:** `1002_Acuityscheduling_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Acuityscheduling for data processing. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Acuityscheduling,  

---

### Eventbrite Automate Triggered
**Filename:** `1007_Eventbrite_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Eventbrite for data processing. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Eventbrite,  

---

### Calendly Automate Triggered
**Filename:** `1009_Calendly_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Cal.com for data processing. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Cal.com,  

---

### Jotform Automate Triggered
**Filename:** `1010_Jotform_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Form Trigger for data processing. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Form Trigger,  

---

### Surveymonkey Automate Triggered
**Filename:** `1020_Surveymonkey_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Surveymonkey for data processing. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Surveymonkey,  

---

### Manual Graphql Automate Triggered
**Filename:** `1026_Manual_Graphql_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with GraphQL for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** GraphQL,  

---

### Manual Microsoftonedrive Automate Triggered
**Filename:** `1032_Manual_Microsoftonedrive_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with OneDrive for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** OneDrive,  

---

### Jira Automate Triggered
**Filename:** `1035_Jira_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Jira for data processing. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Jira,  

---

### Manual Crypto Automate Triggered
**Filename:** `1038_Manual_Crypto_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Crypto for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Crypto,  

---

### Manual Datetime Automate Triggered
**Filename:** `1039_Manual_Datetime_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Datetime for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Datetime,  

---

### Manual Readbinaryfile Automate Triggered
**Filename:** `1041_Manual_Readbinaryfile_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Readbinaryfile for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Readbinaryfile,  

---

### Manual Readbinaryfiles Automate Triggered
**Filename:** `1042_Manual_Readbinaryfiles_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Readbinaryfiles for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Readbinaryfiles,  

---

### Manual Automate Triggered
**Filename:** `1044_Manual_Automate_Triggered.json`  
**Description:** Manual workflow that for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** None  

---

### Manual Renamekeys Automate Triggered
**Filename:** `1045_Manual_Renamekeys_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Renamekeys for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Renamekeys,  

---

### Manual Rssfeedread Automate Triggered
**Filename:** `1046_Manual_Rssfeedread_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Rssfeedread for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Rssfeedread,  

---

### Manual Readpdf Automate Triggered
**Filename:** `1048_Manual_Readpdf_Automate_Triggered.json`  
**Description:** Manual workflow that connects Readbinaryfile and Readpdf for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Readbinaryfile,Readpdf,  

---

### Manual Readbinaryfile Automate Triggered
**Filename:** `1049_Manual_Readbinaryfile_Automate_Triggered.json`  
**Description:** Manual workflow that connects Readbinaryfile and Spreadsheetfile for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Readbinaryfile,Spreadsheetfile,  

---

### Turn on a light and set its brightness
**Filename:** `1053_Manual_Philipshue_Automation_Triggered.json`  
**Description:** Manual workflow that integrates with Philipshue for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Philipshue,  

---

### Get Comments from Facebook Page
**Filename:** `1058_Splitout_Code_Import_Triggered.json`  
**Description:** Manual workflow that connects Splitout and Facebook for data processing. Uses 11 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (11 nodes)  
**Integrations:** Splitout,Facebook,  

---

### Make OpenAI Citation for File Retrieval RAG
**Filename:** `1059_Splitout_Code_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, OpenAI, and Splitout for data processing. Uses 19 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (19 nodes)  
**Integrations:** Markdown,OpenAI,Splitout,Httprequest,Chat,Form Trigger,Memorybufferwindow,  

---

### POC - Chatbot Order by Sheet Data
**Filename:** `1060_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Toolhttprequest, OpenAI, and Agent for data processing. Uses 8 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (8 nodes)  
**Integrations:** Toolhttprequest,OpenAI,Agent,Chat,Cal.com,Memorybufferwindow,  

---

### Automate Figma Versioning and Jira Updates with n8n Webhook Integration
**Filename:** `1069_Figma_Stickynote_Update_Triggered.json`  
**Description:** Webhook-triggered automation that connects Figma and Jira to update existing data. Uses 4 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Figma,Jira,  

---

### n8n_mysql_purge_history_greater_than_10_days
**Filename:** `1076_Manual_Cron_Automation_Scheduled.json`  
**Description:** Scheduled automation that integrates with MySQL for data processing. Uses 3 nodes.  
**Status:** Active  
**Trigger:** Scheduled  
**Complexity:** low (3 nodes)  
**Integrations:** MySQL,  

---

### Store the data received from the CocktailDB API in JSON
**Filename:** `1089_Manual_Writebinaryfile_Automation_Webhook.json`  
**Description:** Manual workflow that orchestrates Httprequest, Writebinaryfile, and Movebinarydata for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Httprequest,Writebinaryfile,Movebinarydata,  

---

### Manual Code Automate Triggered
**Filename:** `1090_Manual_Code_Automate_Triggered.json`  
**Description:** Manual workflow that for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** None  

---

### Two Way Sync Pipedrive and MySQL
**Filename:** `1092_Datetime_Schedule_Sync_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Datetime, Comparedatasets, and MySQL to synchronize data. Uses 14 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** Datetime,Comparedatasets,MySQL,Pipedrive,  

---

### Manual Teams Automate Triggered
**Filename:** `1095_Manual_Teams_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Microsoft Teams for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Microsoft Teams,  

---

### Manual Noop Automate Triggered
**Filename:** `1097_Manual_Noop_Automate_Triggered.json`  
**Description:** Manual workflow that for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (7 nodes)  
**Integrations:** None  

---

### Manual Import Triggered
**Filename:** `1098_Manual_Import_Triggered.json`  
**Description:** Manual workflow that integrates with Cal.com for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Cal.com,  

---

### Prepare CSV files with GPT-4
**Filename:** `1102_Manual_Openai_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Writebinaryfile, and OpenAI for data processing. Uses 11 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (11 nodes)  
**Integrations:** Splitinbatches,Writebinaryfile,OpenAI,Movebinarydata,Spreadsheetfile,Itemlists,  

---

<!-- Error fetching workflow: 1104_Stickynote_Create_Triggered.json -->
### Text to Speech (OpenAI)
**Filename:** `1105_Manual_Stickynote_Automation_Webhook.json`  
**Description:** Manual workflow that integrates with OpenAI for data processing. Uses 8 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (8 nodes)  
**Integrations:** OpenAI,  

---

### YouTube to Airtable Anonym
**Filename:** `1109_Code_Schedule_Automation_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Httprequest, Airtable, and Informationextractor for data processing. Uses 13 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (13 nodes)  
**Integrations:** Httprequest,Airtable,Informationextractor,  

---

### Read RSS feed from two different sources
**Filename:** `1122_Manual_Rssfeedread_Automation_Triggered.json`  
**Description:** Manual workflow that connects Splitinbatches and Rssfeedread for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Splitinbatches,Rssfeedread,  

---

### Automate
**Filename:** `1123_Automate.json`  
**Description:** Manual workflow that for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** None  

---

### Create
**Filename:** `1124_Create.json`  
**Description:** Manual workflow that to create new records. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** None  

---

### Create
**Filename:** `1125_Create.json`  
**Description:** Manual workflow that to create new records. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** None  

---

### Receive updates when a form is submitted in Wufoo
**Filename:** `1129_Wufoo_Update_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Wufoo to update existing data. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Wufoo,  

---

### Convert Squarespace Profiles to Shopify Customers in Google Sheets
**Filename:** `1132_Webhook_Extractfromfile_Process_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Shopify, Splitinbatches, and Webhook for data processing. Uses 8 nodes and integrates with 4 services.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** medium (8 nodes)  
**Integrations:** Shopify,Splitinbatches,Webhook,Google Sheets,  

---

### Generate Exam Questions
**Filename:** `1134_Googledocs_Code_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Textsplittertokensplitter, Lmchatgooglegemini, and Converttofile for data processing. Uses 37 nodes and integrates with 17 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (37 nodes)  
**Integrations:** Textsplittertokensplitter,Lmchatgooglegemini,Converttofile,Splitinbatches,OpenAI,Google Docs,Google Sheets,Agent,Outputparseritemlist,Chainretrievalqa,Outputparserstructured,Httprequest,Documentdefaultdataloader,Vectorstoreqdrant,Chainllm,Retrievervectorstore,Toolvectorstore,  

---

### Create Threads on Bluesky
**Filename:** `1135_Wait_Code_Create_Webhook.json`  
**Description:** Scheduled automation that connects Httprequest and Splitinbatches to create new records. Uses 20 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** high (20 nodes)  
**Integrations:** Httprequest,Splitinbatches,  

---

### Read sitemap and filter URLs
**Filename:** `1143_Splitout_Filter_Automation_Webhook.json`  
**Description:** Manual workflow that orchestrates Httprequest, Splitout, and Xml for data processing. Uses 10 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (10 nodes)  
**Integrations:** Httprequest,Splitout,Xml,  

---

### Google Site Index - sitemap.xml example
**Filename:** `1145_Wait_Splitout_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Splitinbatches, and Splitout for data processing. Uses 21 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (21 nodes)  
**Integrations:** Httprequest,Splitinbatches,Splitout,Xml,  

---

### LinkedIn Leads Scraping & Enrichment (Main)
**Filename:** `1146_Splitout_Code_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Splitout, and Google Sheets for data processing. Uses 66 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (66 nodes)  
**Integrations:** OpenAI,Splitout,Google Sheets,LinkedIn,Httprequest,Form Trigger,  

---

### Execute a command that gives the hard disk memory used on the host machine
**Filename:** `1150_Noop_Executecommand_Automation_Scheduled.json`  
**Description:** Scheduled automation that connects Twilio and Executecommand for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (5 nodes)  
**Integrations:** Twilio,Executecommand,  

---

### Slack Webhook - Verify Signature
**Filename:** `1164_Stopanderror_Code_Automation_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Executeworkflow, Crypto, and Slack for data processing. Uses 12 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (12 nodes)  
**Integrations:** Executeworkflow,Crypto,Slack,  

---

### Workflow Importer
**Filename:** `1169_Splitout_Code_Import_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Readwritefile, Executecommand, and Splitout for data processing. Uses 58 nodes and integrates with 9 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (58 nodes)  
**Integrations:** Readwritefile,Executecommand,Splitout,Extractfromfile,Httprequest,N8N,Renamekeys,Removeduplicates,Form Trigger,  

---

### Create a new issue in Jira
**Filename:** `1170_Manual_Jira_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Jira to create new records. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Jira,  

---

### Manual Readbinaryfile Automate Triggered
**Filename:** `1174_Manual_Readbinaryfile_Automate_Triggered.json`  
**Description:** Manual workflow that connects Readbinaryfile and Spreadsheetfile for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Readbinaryfile,Spreadsheetfile,  

---

### Get only new RSS with Photo
**Filename:** `1180_Rssfeedread_Htmlextract_Create_Scheduled.json`  
**Description:** Scheduled automation that connects Htmlextract and Rssfeedread for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (5 nodes)  
**Integrations:** Htmlextract,Rssfeedread,  

---

### Enhance Chat Responses with Real-Time Search Data via Bright Data & Gemini AI
**Filename:** `1183_Manual_Stickynote_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Webhook, and Agent for data processing. Uses 18 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (18 nodes)  
**Integrations:** Lmchatgooglegemini,Webhook,Agent,Chat,Memorybufferwindow,  

---

### Executecommand Readbinaryfile Automate Triggered
**Filename:** `1190_Executecommand_Readbinaryfile_Automate_Triggered.json`  
**Description:** Manual workflow that orchestrates Readbinaryfile, Executecommand, and Movebinarydata for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (7 nodes)  
**Integrations:** Readbinaryfile,Executecommand,Movebinarydata,  

---

### Manual Securityscorecard Automate Triggered
**Filename:** `1196_Manual_Securityscorecard_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Securityscorecard for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Securityscorecard,  

---

### Translate text from English to German
**Filename:** `1200_Manual_Googletranslate_Automation_Triggered.json`  
**Description:** Manual workflow that integrates with Googletranslate for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Googletranslate,  

---

### Gotowebinar Automate
**Filename:** `1213_Gotowebinar_Automate.json`  
**Description:** Manual workflow that integrates with Gotowebinar for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Gotowebinar,  

---

### Manual Schedule Automate Scheduled
**Filename:** `1216_Manual_Schedule_Automate_Scheduled.json`  
**Description:** Scheduled automation that connects Signl4 and Openweathermap for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (5 nodes)  
**Integrations:** Signl4,Openweathermap,  

---

