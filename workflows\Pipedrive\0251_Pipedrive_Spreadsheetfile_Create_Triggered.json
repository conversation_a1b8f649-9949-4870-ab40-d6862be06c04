{"meta": {"instanceId": "4eea70f6789129b82c5f438f374db25affb0eba28902cc3663e308cff7659044"}, "nodes": [{"id": "97b052c3-2a98-4dee-973a-f170a5e575c8", "name": "Google Drive Trigger", "type": "n8n-nodes-base.googleDriveTrigger", "position": [960, 140], "parameters": {"event": "fileCreated", "options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": "1uQ0YnGnQNzIaWGdTt2UBT58tTy8xDlpW"}, "credentials": {"googleDriveOAuth2Api": {"id": "36", "name": "<PERSON>'s Google Drive account"}}, "typeVersion": 1}, {"id": "1e82f8f8-175d-4493-a3a9-35380431d91c", "name": "Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [1180, 140], "parameters": {"fileId": "={{ $json[\"id\"] }}", "options": {}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "36", "name": "<PERSON>'s Google Drive account"}}, "typeVersion": 2}, {"id": "fb36224d-4acb-4aba-9543-dd534e76477f", "name": "Spreadsheet File", "type": "n8n-nodes-base.spreadsheetFile", "position": [1400, 140], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "323b2a18-fc98-4b73-9c7f-421780f04e94", "name": "Pipedrive", "type": "n8n-nodes-base.pipedrive", "position": [1540, 400], "parameters": {"filters": {}, "resource": "lead", "operation": "getAll", "returnAll": true}, "credentials": {"pipedriveApi": {"id": "22", "name": "n8n Production"}}, "executeOnce": true, "typeVersion": 1}, {"id": "80d9733e-ccfb-4140-981f-8b818c4b9e70", "name": "Pipedrive1", "type": "n8n-nodes-base.pipedrive", "position": [1920, 380], "parameters": {"personId": "={{ $json[\"person_id\"] }}", "resource": "person", "operation": "get"}, "credentials": {"pipedriveApi": {"id": "22", "name": "n8n Production"}}, "typeVersion": 1}, {"id": "********-b0a9-4f15-9e10-f3750a60936c", "name": "IF", "type": "n8n-nodes-base.if", "position": [1720, 400], "parameters": {"conditions": {"number": [{"value1": "={{ $json[\"person_id\"] }}", "operation": "larger"}]}}, "typeVersion": 1}, {"id": "e5592e1d-da1f-4536-b816-3a6df764cd0a", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [2140, 100], "parameters": {"mode": "removeKeyMatches", "propertyName1": "Email address", "propertyName2": "email[0].value"}, "typeVersion": 1}, {"id": "29918402-d224-411d-b563-44d68c5b1c10", "name": "Set", "type": "n8n-nodes-base.set", "position": [2360, 100], "parameters": {"values": {"string": [{"name": "company", "value": "={{ $json[\"Company name\"] }}"}, {"name": "name", "value": "={{ $json[\"First name\"] }} {{ $json[\"Last name\"] }}"}, {"name": "email", "value": "={{ $json[\"Email address\"] }}"}, {"name": "employees", "value": "={{ $json[\"Company size\"] }}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"id": "a3c83915-3b87-41ec-ba3b-5db1134b1763", "name": "Create Organization", "type": "n8n-nodes-base.pipedrive", "position": [2840, 100], "parameters": {"name": "={{ $json[\"company\"] }}", "resource": "organization", "additionalFields": {}}, "credentials": {"pipedriveApi": {"id": "22", "name": "n8n Production"}}, "typeVersion": 1}, {"id": "e8f0a561-cc7a-4302-83dc-8c4a407b9b53", "name": "Create Person", "type": "n8n-nodes-base.pipedrive", "position": [3180, 100], "parameters": {"name": "={{ $node[\"Set\"].json[\"name\"] }}", "resource": "person", "additionalFields": {"email": ["={{ $node[\"Set\"].json[\"email\"] }}"], "org_id": "={{ $json[\"id\"] }}", "customProperties": {"property": [{"name": "0bf0c49725830779ff146f5a087853d959dee064", "value": "LinkedIn_Ad"}]}}}, "credentials": {"pipedriveApi": {"id": "22", "name": "n8n Production"}}, "typeVersion": 1}, {"id": "7c038ae1-030e-4047-b4af-d13333ed14af", "name": "Create Lead", "type": "n8n-nodes-base.pipedrive", "position": [3380, 100], "parameters": {"title": "={{$node[\"Set\"].json[\"company\"]}} lead", "resource": "lead", "organization_id": "={{$node[\"Create Organization\"].json.id}}", "additionalFields": {"owner_id": 12672788, "person_id": "={{$json.id}}"}}, "credentials": {"pipedriveApi": {"id": "22", "name": "n8n Production"}}, "typeVersion": 1}, {"id": "46a433d1-0248-4208-89d2-747644e1face", "name": "Create Note", "type": "n8n-nodes-base.pipedrive", "position": [3580, 100], "parameters": {"content": "=\nCompany Size:\n{{$node[\"Set\"].json[\"employees\"]}}", "resource": "note", "additionalFields": {"lead_id": "={{$json.id}}"}}, "credentials": {"pipedriveApi": {"id": "22", "name": "n8n Production"}}, "typeVersion": 1}], "connections": {"IF": {"main": [[{"node": "Pipedrive1", "type": "main", "index": 0}]]}, "Set": {"main": [[{"node": "Create Organization", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}, "Pipedrive": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}, "Pipedrive1": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Create Lead": {"main": [[{"node": "Create Note", "type": "main", "index": 0}]]}, "Google Drive": {"main": [[{"node": "Spreadsheet File", "type": "main", "index": 0}]]}, "Create Person": {"main": [[{"node": "Create Lead", "type": "main", "index": 0}]]}, "Spreadsheet File": {"main": [[{"node": "Pipedrive", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Create Organization": {"main": [[{"node": "Create Person", "type": "main", "index": 0}]]}, "Google Drive Trigger": {"main": [[{"node": "Google Drive", "type": "main", "index": 0}]]}}}