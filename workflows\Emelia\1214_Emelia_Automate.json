{"nodes": [{"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.emelia", "position": [530, 310], "parameters": {"operation": "create", "campaignName": "n8n-docs"}, "credentials": {"emeliaApi": "Emelia API Credentials"}, "typeVersion": 1}, {"name": "Emelia1", "type": "n8n-nodes-base.emelia", "position": [730, 310], "parameters": {"operation": "addContact", "campaignId": "603dfd70cbe34c3c9730fd09", "contactEmail": "<EMAIL>", "additionalFields": {"firstName": "NAME"}}, "credentials": {"emeliaApi": "Emelia API Credentials"}, "typeVersion": 1}, {"name": "Emelia2", "type": "n8n-nodes-base.emelia", "position": [930, 310], "parameters": {"campaignId": "={{$node[\"Emeli<PERSON>\"].json[\"_id\"]}}"}, "credentials": {"emeliaApi": "Emelia API Credentials"}, "typeVersion": 1}], "connections": {"Emelia": {"main": [[{"node": "Emelia1", "type": "main", "index": 0}]]}, "Emelia1": {"main": [[{"node": "Emelia2", "type": "main", "index": 0}]]}}}