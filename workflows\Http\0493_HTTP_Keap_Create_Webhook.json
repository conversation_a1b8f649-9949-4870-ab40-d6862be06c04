{"meta": {"instanceId": "041bccf206a3546a759ec4c0a3bf1256e62051945bb270c48f91f3acb13dc080"}, "nodes": [{"id": "82d5281b-a4a3-4407-859e-49cb16567b28", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [340, -260], "parameters": {"width": 747, "height": 428, "content": "## Purpose \nTo verify the mailing address for new contacts in Keap. \n\nWhenever I add a new contact to Keap, I run this automation to ensure I have a valid mailing address. It also helps me check for misspellings if the contact address was manually entered.\n\nQuick Video Overview:\n\nhttps://www.youtube.com/watch?v=YyIpQw5gyhk\n"}, "typeVersion": 1}, {"id": "78fbe4ae-e72b-4bf9-8387-0d126316b148", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1480, -180], "parameters": {"color": 5, "width": 515, "height": 763, "content": "Update Keap to indicate if the address is deliverable.\n\nPossible Options: \n- Add Tag\n- Add Note\n- Start Automation\n- Update a Field\n\nFor Deliverable Addresses - I apply a tag that the address was verified.\n\nFor Non Deliverable Addresses - I apply a tag, which triggers an automation for my team to manually verify the address. You could also trigger an automation to reach out to the contact to verify their address.\n\n"}, "typeVersion": 1}, {"id": "b6313993-fa07-463d-a77a-a3c273ebc2c5", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [520, 200], "parameters": {"color": 4, "height": 339, "content": "Receive a webhook from your CRM with the contact address fields"}, "typeVersion": 1}, {"id": "f79e9d7a-7ce9-49f3-bd0f-b827ce04b5e2", "name": "Set Address Fields", "type": "n8n-nodes-base.set", "position": [840, 280], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "8216105e-23ad-4c5c-8f4a-4f97658e0947", "name": "address", "type": "string", "value": "={{ $json.address }}"}, {"id": "111da971-2473-4c5e-a106-22589cf47daf", "name": "address2", "type": "string", "value": "={{ $json.address2 }}"}, {"id": "ed62cf39-10f1-42f6-b18f-bfa58b4fe646", "name": "city", "type": "string", "value": "={{ $json.city }}"}, {"id": "d9550200-04ac-4cf4-b7e6-cd40b793ce97", "name": "state", "type": "string", "value": "={{ $json.state }}"}, {"id": "62269d11-c98c-4016-83ef-291176f2fc12", "name": "zip", "type": "string", "value": "={{ $json.zip_code }}"}]}, "includeOtherFields": true}, "typeVersion": 3.3}, {"id": "61d0ba59-dff6-4357-b085-a6d129171060", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1000, 480], "parameters": {"color": 3, "width": 430, "height": 216, "content": "1. Create an Account a https://www.lob.com/pricing\n2. Create API Key (https://help.lob.com/account-management/api-keys)\n3. Update Node with your Credentials (Basic Auth)"}, "typeVersion": 1}, {"id": "4275e2a4-60a9-447e-8d64-f0073b9abe6b", "name": "Address Verification", "type": "n8n-nodes-base.httpRequest", "position": [1060, 280], "parameters": {"url": "https://api.lob.com/v1/us_verifications", "method": "POST", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "primary_line", "value": "={{ $json.address }}"}, {"name": "secondary_line", "value": "={{ $json.address2 }}"}, {"name": "city", "value": "={{ $json.city }}"}, {"name": "state", "value": "={{ $json.state }}"}, {"name": "zip_code", "value": "={{ $json.zip_code }}"}]}}, "typeVersion": 4.1}, {"id": "89da689e-1f96-4aa6-9236-150dc087caf0", "name": "Update Keap - Deliverable", "type": "n8n-nodes-base.keap", "position": [1580, 140], "parameters": {"tagIds": "=Mailing Address Deliverable", "resource": "contactTag", "contactId": "={{ $('CRM Webhook Trigger').item.json.id }}"}, "credentials": {"keapOAuth2Api": {"id": "5gXMihvp2f0IT5i1", "name": "Blank"}}, "typeVersion": 1}, {"id": "67ca486b-fc17-43e0-a2ae-757ab65422f7", "name": "Update Keap - NOT Deliverable", "type": "n8n-nodes-base.keap", "position": [1580, 360], "parameters": {"tagIds": "=Mailing Address NOT Deliverable", "resource": "contactTag", "contactId": "={{ $('CRM Webhook Trigger').item.json.id }}"}, "credentials": {"keapOAuth2Api": {"id": "5gXMihvp2f0IT5i1", "name": "Blank"}}, "typeVersion": 1}, {"id": "bd2a2468-80d5-4a76-81b5-ea9cb181eb7a", "name": "CRM Webhook Trigger", "type": "n8n-nodes-base.webhook", "position": [600, 280], "webhookId": "fd51bba5-929d-4610-bd3f-a3032bcf16c3", "parameters": {"path": "727deb6f-9d10-4492-92e6-38f3292510b0", "options": {}, "httpMethod": "POST"}, "typeVersion": 1.1}, {"id": "15221022-7eb3-40db-85b3-cf310e8bc2d2", "name": "Switch", "type": "n8n-nodes-base.switch", "position": [1280, 280], "parameters": {"rules": {"rules": [{"value2": "=deliverable", "outputKey": "deliverable"}, {"value2": "deliverable", "operation": "notEqual", "outputKey": "NOT deliverable"}]}, "value1": "={{ $json.deliverability }}", "dataType": "string"}, "typeVersion": 2}], "pinData": {"CRM Webhook Trigger": [{"id": "5551212", "city": "Washington", "email": "<EMAIL>", "phone": "************", "state": "DC", "address": "1600 Pennsylvania Avenue NW", "zip_code": "20500"}]}, "connections": {"Switch": {"main": [[{"node": "Update Keap - Deliverable", "type": "main", "index": 0}], [{"node": "Update Keap - NOT Deliverable", "type": "main", "index": 0}]]}, "Set Address Fields": {"main": [[{"node": "Address Verification", "type": "main", "index": 0}]]}, "CRM Webhook Trigger": {"main": [[{"node": "Set Address Fields", "type": "main", "index": 0}]]}, "Address Verification": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}}}