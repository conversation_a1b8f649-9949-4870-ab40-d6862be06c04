{"id": "IO0OrQ6ao4vm9urI", "meta": {"instanceId": "0d6ec6d73242e93a616bed7dc657bb92fd6b05466b19318f83d18293848e971a", "templateCredsSetupCompleted": true}, "name": "Automated Resume Review System Using OpenAI + Google Sheets", "tags": [], "nodes": [{"id": "8585c01d-f26c-453e-a705-7783b3a28a46", "name": "On form submission", "type": "n8n-nodes-base.formTrigger", "position": [-780, 180], "webhookId": "6ea62ea0-de12-4134-b646-121474b3b846", "parameters": {"options": {"ignoreBots": true, "appendAttribution": false}, "formTitle": "Submit your CV", "formFields": {"values": [{"fieldLabel": "First name", "placeholder": "First Name", "requiredField": true}, {"fieldLabel": "Last Name", "placeholder": "Last Name", "requiredField": true}, {"fieldType": "email", "fieldLabel": "Email", "placeholder": "Email", "requiredField": true}, {"fieldType": "file", "fieldLabel": "Resume", "requiredField": true, "acceptFileTypes": "=.pdf"}]}}, "typeVersion": 2.2}, {"id": "d7acbd9b-f24a-4801-9a00-94308df9a55e", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [540, 140], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineAll"}, "typeVersion": 3}, {"id": "68f1cb96-fca5-473b-b79c-707682206135", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [1600, 340], "parameters": {"schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"vote\": {\n      \"type\": \"string\"\n    },\n    \"consideration\": {\n      \"type\": \"string\"\n    }\n  }\n}\n"}, "typeVersion": 1.2}, {"id": "04b20070-141a-466c-87d3-7de4323f83df", "name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [1900, 120], "parameters": {"columns": {"value": {"DOB": "={{ $('Merge').item.json.output.birthdate }}", "City": "={{ $('Merge').item.json.output.city }}", "Vote": "={{ $json.output.vote }}", "Email": "={{ $('Merge').item.json.output.email }}", "Skills": "={{ $('Merge').item.json.output.Skills }}", "Website": "={{ $('Merge').item.json.output.website }}", "Last Name": "={{ $('Merge').item.json.output.last_name }}", "Experience": "={{ $('Merge').item.json.output['Job History'] }}", "First Name": "={{ $('Merge').item.json.output.first_name }}", "Applied Date": "={{ $now.format('MM-dd-yyyy') }}", "Education Qualification": "={{ $('Merge').item.json.output['Educational Qualification'] }}"}, "schema": [{"id": "First Name", "type": "string", "display": true, "required": false, "displayName": "First Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Last Name", "type": "string", "display": true, "required": false, "displayName": "Last Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "City", "type": "string", "display": true, "required": false, "displayName": "City", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone number", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Phone number", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Email", "type": "string", "display": true, "required": false, "displayName": "Email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "LinkedIn", "type": "string", "display": true, "removed": false, "required": false, "displayName": "LinkedIn", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Website", "type": "string", "display": true, "required": false, "displayName": "Website", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "DOB", "type": "string", "display": true, "required": false, "displayName": "DOB", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Education Qualification", "type": "string", "display": true, "required": false, "displayName": "Education Qualification", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Experience", "type": "string", "display": true, "required": false, "displayName": "Experience", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Skills", "type": "string", "display": true, "required": false, "displayName": "Skills", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Vote", "type": "string", "display": true, "required": false, "displayName": "Vote", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Consideration", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Consideration", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Applied Date", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Applied Date", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1tmg5CW1d3ZNVJ98hODs24RLGyKul98cAtVHULLNDAyU/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1tmg5CW1d3ZNVJ98hODs24RLGyKul98cAtVHULLNDAyU", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1tmg5CW1d3ZNVJ98hODs24RLGyKul98cAtVHULLNDAyU/edit?usp=drivesdk", "cachedResultName": "HR New"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "d63Esv5UOI7IgJEf", "name": "Google Sheets account 2"}}, "typeVersion": 4.5}, {"id": "dab7587f-890d-4571-b03d-1a2948baa91c", "name": "Personal Info", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [-140, 80], "parameters": {"text": "={{ $json.text }}", "options": {}, "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"first_name\": {\n      \"type\": \"string\"\n    },\n    \"last_name\": {\n      \"type\": \"string\"\n    },\n    \"email\": {\n      \"type\": \"string\"\n    },\n    \"telephone\": {\n      \"type\": \"string\"\n    },\n    \"city\": {\n      \"type\": \"string\"\n    },\n    \"birthdate\": {\n      \"type\": \"string\"\n    },\n    \"linkedin\": {\n      \"type\": \"string\"\n    },\n    \"website\": {\n      \"type\": \"string\"\n    },\n    \"summary\": {\n      \"type\": \"string\"\n    }\n  }\n}\n"}, "typeVersion": 1}, {"id": "5c815337-80c4-4332-9179-77c0a446c205", "name": "Qualification", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [-140, 320], "parameters": {"text": "={{ $json.text }}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}, "attributes": {"attributes": [{"name": "Educational Qualification", "required": true, "description": "Summary of your academic career. Focus on your high school and university studies. Summarize in 100 words maximum and also include your grade if applicable."}, {"name": "Job History", "required": true, "description": "Work history summary. Focus on your most recent work experiences. Summarize in 100 words maximum"}, {"name": "Skills", "required": true, "description": "Extract the candidate’s technical skills. What software and frameworks they are proficient in. Make a bulleted list."}]}}, "typeVersion": 1}, {"id": "ad245152-b1cc-4dcd-b9bc-c8ec8a592115", "name": "HR Expert", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1500, 120], "parameters": {"text": "=Profile:\n{{ $json['wanted-profile'] }}\n\nCandidate:{{ $('Summarizer').item.json.response.text }}", "messages": {"messageValues": [{"message": "=You are an HR expert, and your task is to determine whether a candidate aligns with the company's desired profile. You must assign a rating from 1 to 10, where 1 means the candidate does not meet the requirements, while 10 means the candidate is the perfect match for the role. Additionally, in the \"consideration\" field, explain the reasoning behind the given score."}]}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.5}, {"id": "86a37824-a3d0-4199-bb2d-c7608d65f6de", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-820, -140], "parameters": {"width": 500, "height": 660, "content": "## Submission, Saving to Google Drive & Extraction\n\n**Captures user info from the form.**\n**Uploads resume to Google Drive.**\n**Extracts data from the PDF (resume).**"}, "typeVersion": 1}, {"id": "6a42aedf-e2a0-44fc-ae02-00d25ab56a91", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-300, -140], "parameters": {"color": 7, "width": 560, "height": 660, "content": "## Extraction (Personal Info & Qualification)\n\n**Extracts personal details (name, city, etc.).**\n**Retrieves educational qualifications and job history.**"}, "typeVersion": 1}, {"id": "17134f76-322d-4621-ab85-340d1f9ea115", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [280, -140], "parameters": {"color": 5, "width": 1120, "height": 660, "content": "## Merge & Summarization\n\n**Merges extracted information.**\n**Generates a concise professional summary.**\n\n"}, "typeVersion": 1}, {"id": "cf09a615-65dc-4c4d-8419-f05cadeb9405", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1420, -140], "parameters": {"color": 4, "width": 640, "height": 660, "content": "## Voting, Consideration & Google Sheets\n\n**HR expert reviews and analyzes the summary.\n\nAssigns a rating (1-10) and provides hiring insights.\n\nAppends all details to Google Sheets for record-keeping.**\n\n"}, "typeVersion": 1}, {"id": "ee76a66a-ce2a-4a4f-8c57-0cc48b8f7dcb", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-1960, -140], "parameters": {"color": 2, "width": 1060, "height": 660, "content": "# Automated Resume Processing & Evaluation System\n\n**This workflow streamlines the process of handling resume submissions, extracting key details, summarizing qualifications, and aiding HR in the decision-making process.**\n\n## 1. Submission, Saving to Google Drive & Extraction\n**The user submits a resume via a form.\nThe system saves the uploaded file to Google Drive for record-keeping.\nAI-powered text extraction retrieves personal details, qualifications, and job history from the document.**\n\n## 2. Extraction (Personal Info & Qualification)\n**The workflow identifies and extracts key details such as the candidate’s name, contact information, and location.\nIt scans for educational background, certifications, and past work experiences.**\n\n## 3. Merge & Summarization\n**The extracted data is merged into a structured format.\nA concise summary is generated, highlighting the candidate’s most relevant qualifications and experiences.**\n\n## 4. Voting, Consideration & Google Sheets\n**HR reviews the summarized profile and assigns a rating (1-10).\nHiring insights and comments are recorded for evaluation.\nAll processed data, including extracted details and review scores, are appended to a Google Sheet for tracking and further consideration.**\n\n"}, "typeVersion": 1}, {"id": "51e5290b-fadd-4f7c-99fc-8bfd54a1ee27", "name": "Upload to google drive", "type": "n8n-nodes-base.googleDrive", "position": [-440, 100], "parameters": {"name": "=Resume-{{ $now.format('yyyyLLdd') }}-{{ $json.Resume[0].filename }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "root", "cachedResultName": "/ (Root folder)"}, "inputDataFieldName": "Resume"}, "credentials": {"googleDriveOAuth2Api": {"id": "Z5vnQSvmzFvtqQeL", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "b69e22ba-ab56-4199-830a-4d74fd8c8e74", "name": "Resume extraction", "type": "n8n-nodes-base.extractFromFile", "position": [-440, 260], "parameters": {"options": {}, "operation": "pdf", "binaryPropertyName": "Resume"}, "typeVersion": 1}, {"id": "653d83ad-309f-4e09-acf5-e7d0a1890e1e", "name": "wanted profile", "type": "n8n-nodes-base.set", "position": [1240, 120], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "8b812d8f-87d6-46e2-855a-b465c1248c2d", "name": "wanted-profile", "type": "string", "value": "We are a web agency looking for an Automation Expert skilled in workflow automation, API integrations, and AI-driven process optimization. The ideal candidate should have expertise in n8n, Python, and JavaScript, with a strong understanding of automation tools and webhooks. Experience in building custom automations for businesses is required.  Requirements:  Proficiency in n8n, Python, and JavaScript Experience in workflow automation, API integrations, and AI agents Ability to optimize business processes through automation Prior experience in the automation industry Must be based in Northern Italy If you have a passion for automation and want to work with a forward-thinking agency, we'd love to hear from you!"}]}}, "typeVersion": 3.4}, {"id": "9ed47811-30d8-48e7-a05e-e5213f0e0526", "name": "Summa<PERSON><PERSON>", "type": "@n8n/n8n-nodes-langchain.chainSummarization", "position": [840, 120], "parameters": {"options": {"summarizationMethodAndPrompts": {"values": {"prompt": "=Write a concise summary of the following:\nFirst name:{{ $json.output.first_name }}\nLast name:{{ $json.output.last_name }}\nCity: {{ $json.output.city }}\nEducational Qualification:{{ $json.output['Educational Qualification'] }}\nPrevious experience:{{ $json.output['Job History'] }}\nSkills:{{ $json.output.Skills }}\nApplied date:{{$now.format('yyyy-MM-dd')}}\n\nWrite a concise Summary and summary of 100 words or less. Be concise and professional.\n\n", "combineMapPrompt": "=Write a concise summary of the following:\nFirst name:{{ $json.output.first_name }}\nLast name:{{ $json.output.last_name }}\nCity: {{ $json.output.city }}\nEducational Qualification:{{ $json.output['Educational Qualification'] }}\nPrevious experience:{{ $json.output['Job History'] }}\nSkills:{{ $json.output.Skills }}\nApplied date:{{$now.format('yyyy-MM-dd')}}\n\nWrite a concise Summary and summary of 100 words or less. Be concise and professional.\n\n"}}}}, "typeVersion": 2}, {"id": "56329dd0-53e2-4617-ba54-c91e9f96d6ca", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [860, 400], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "n2UwuicTmLclqMaY", "name": "OpenAi account 2"}}, "typeVersion": 1.2}], "active": false, "pinData": {}, "settings": {"callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1"}, "versionId": "24ebf6c2-c041-4dc0-8fec-5728f86625f1", "connections": {"Merge": {"main": [[{"node": "Summa<PERSON><PERSON>", "type": "main", "index": 0}]]}, "HR Expert": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}, "Summarizer": {"main": [[{"node": "wanted profile", "type": "main", "index": 0}]]}, "Personal Info": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Qualification": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "wanted profile": {"main": [[{"node": "HR Expert", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Qualification", "type": "ai_languageModel", "index": 0}, {"node": "Personal Info", "type": "ai_languageModel", "index": 0}, {"node": "Summa<PERSON><PERSON>", "type": "ai_languageModel", "index": 0}, {"node": "HR Expert", "type": "ai_languageModel", "index": 0}]]}, "Resume extraction": {"main": [[{"node": "Personal Info", "type": "main", "index": 0}, {"node": "Qualification", "type": "main", "index": 0}]]}, "On form submission": {"main": [[{"node": "Upload to google drive", "type": "main", "index": 0}, {"node": "Resume extraction", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "HR Expert", "type": "ai_outputParser", "index": 0}]]}}}