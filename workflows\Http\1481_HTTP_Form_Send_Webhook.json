{"id": "GrGmuKzZAsCkd4bt", "meta": {"instanceId": "a4bfc93e975ca233ac45ed7c9227d84cf5a2329310525917adaf3312e10d5462", "templateCredsSetupCompleted": true}, "name": "Send TTS (Text-to-speech) voice calls", "tags": [], "nodes": [{"id": "2b14ce1c-5213-4684-90a6-ef8b6885f2ef", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-300, -520], "parameters": {"width": 440, "height": 180, "content": "## STEP 1\n[Register here to ClickSend](https://clicksend.com/?u=586989) and obtain your API Key and 2 € of free credits\n\nIn the node \"Send Voice\" create a \"Basic Auth\" with the username you registered and the API Key provided as your password"}, "typeVersion": 1}, {"id": "b3931dc5-7021-4ca2-ae73-8bf670a56cb7", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-300, -300], "parameters": {"width": 440, "content": "## STEP 2\n\nSubmit the form and you will receive a call to the phone number you entered where the selected voice will tell you the content of the text you wrote."}, "typeVersion": 1}, {"id": "a548f92d-199e-4cd2-ae34-742617484831", "name": "Send Voice", "type": "n8n-nodes-base.httpRequest", "position": [-40, -100], "parameters": {"url": "https://rest.clicksend.com/v3/voice/send", "method": "POST", "options": {}, "jsonBody": "={\n  \"messages\": [\n    {\n      \"source\": \"n8n\",\n      \"body\": \"{{ $json.Body }}\",\n      \"to\": \"{{ $json.To }}\",\n      \"voice\": \"{{ $json.Voice }}\",\n      \"lang\": \"{{ $json.Lang }}\",\n      \"machine_detection\": 1\n    }\n  ]\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth", "headerParameters": {"parameters": [{"name": "Content-Type", "value": " application/json"}]}}, "credentials": {"httpBasicAuth": {"id": "UwsDe2JxT39eWIvY", "name": "ClickSend API"}}, "typeVersion": 4.2}, {"id": "ffc2cbe9-6e31-4d54-8e6a-26e94ec50ef4", "name": "On form submission", "type": "n8n-nodes-base.formTrigger", "position": [-300, -100], "webhookId": "8bfdf9f3-9323-4295-ab96-f9852d5981d5", "parameters": {"options": {}, "formTitle": "Send Voice Message", "formFields": {"values": [{"fieldType": "textarea", "fieldLabel": "Body", "placeholder": "Body (max. 600 chars)", "requiredField": true}, {"fieldLabel": "To", "placeholder": "+39xxxxxxxxxx", "requiredField": true}, {"fieldType": "dropdown", "fieldLabel": "Voice", "fieldOptions": {"values": [{"option": "male"}, {"option": "female"}]}, "requiredField": true}, {"fieldType": "dropdown", "fieldLabel": "<PERSON>", "fieldOptions": {"values": [{"option": "en-us \t"}, {"option": "it-it"}, {"option": "en-au"}, {"option": "en-gb"}, {"option": "de-de"}, {"option": "es-es"}, {"option": "fr-fr"}, {"option": "is-is"}, {"option": "da-dk"}, {"option": "nl-nl"}, {"option": "pl-pl"}, {"option": "pt-br"}, {"option": "ru-ru"}]}, "requiredField": true}]}}, "typeVersion": 2.2}, {"id": "397e0b9f-7407-47d6-b242-1b87955a701b", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-300, -720], "parameters": {"color": 3, "width": 440, "content": "## Automate text-to-speech voice calls\nThis workflow is a simple yet powerful way to automate text-to-speech voice calls using the ClickSend API. It’s ideal for notifications, reminders, or any scenario where voice communication is needed."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "1ad6da32-7197-4f64-b770-88dae8348db2", "connections": {"On form submission": {"main": [[{"node": "Send Voice", "type": "main", "index": 0}]]}}}