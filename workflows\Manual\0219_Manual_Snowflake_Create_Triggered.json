{"id": "172", "name": "Create a table, and insert and update data in the table in Snowflake", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [650, 300], "parameters": {"values": {"number": [{"name": "id", "value": 1}], "string": [{"name": "name", "value": "n8n"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "Snowflake", "type": "n8n-nodes-base.snowflake", "position": [450, 300], "parameters": {"query": "CREATE TABLE docs (id INT, name STRING);", "operation": "execute<PERSON>uery"}, "credentials": {"snowflake": "Snowflake n8n Credentials"}, "typeVersion": 1}, {"name": "Snowflake1", "type": "n8n-nodes-base.snowflake", "position": [850, 300], "parameters": {"table": "docs", "columns": "id, name"}, "credentials": {"snowflake": "Snowflake n8n Credentials"}, "typeVersion": 1}, {"name": "Set1", "type": "n8n-nodes-base.set", "position": [1050, 300], "parameters": {"values": {"number": [{"name": "id", "value": 1}], "string": [{"name": "name", "value": "nodemation"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "Snowflake2", "type": "n8n-nodes-base.snowflake", "position": [1250, 300], "parameters": {"table": "={{$node[\"Snowflake1\"].parameter[\"table\"]}}", "columns": "name", "operation": "update"}, "credentials": {"snowflake": "Snowflake n8n Credentials"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Set": {"main": [[{"node": "Snowflake1", "type": "main", "index": 0}]]}, "Set1": {"main": [[{"node": "Snowflake2", "type": "main", "index": 0}]]}, "Snowflake": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}, "Snowflake1": {"main": [[{"node": "Set1", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Snowflake", "type": "main", "index": 0}]]}}}