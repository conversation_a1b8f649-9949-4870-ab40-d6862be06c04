{"meta": {"instanceId": "6b6a2db47bdf8371d21090c511052883cc9a3f6af5d0d9d567c702d74a18820e"}, "nodes": [{"id": "6fb16611-0ee4-4c89-91ef-dc8a1e39406d", "name": "Upload Img to ImgBB for URL", "type": "n8n-nodes-base.httpRequest", "position": [120, 6220], "parameters": {"url": "https://api.imgbb.com/1/upload", "method": "POST", "options": {}, "sendBody": true, "contentType": "multipart-form-data", "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "image", "parameterType": "formBinaryData", "inputDataFieldName": "data"}]}, "genericAuthType": "httpQueryAuth", "headerParameters": {"parameters": [{"name": "Content-type", "value": "multipart/form-data"}]}}, "notesInFlow": true, "typeVersion": 4.2}, {"id": "e94ebd4f-4459-4705-8fc5-f7ebbc996add", "name": "ReSmush.it Image Optimisation", "type": "n8n-nodes-base.httpRequest", "position": [320, 6220], "parameters": {"url": "=http://api.resmush.it/ws.php?img={{ $json.data.url }}", "options": {}}, "notesInFlow": true, "typeVersion": 4.2}, {"id": "e337dcf1-27d3-4f75-850b-f2c5bff48ed6", "name": "Store Optimised Image ImgBB", "type": "n8n-nodes-base.httpRequest", "position": [540, 6220], "parameters": {"url": "https://api.imgbb.com/1/upload", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "image", "value": "={{ $json.dest }}"}]}, "genericAuthType": "httpQueryAuth", "headerParameters": {"parameters": [{"name": "Content-type", "value": "application/x-www-form-urlencoded"}]}}, "notesInFlow": true, "typeVersion": 4.2}, {"id": "e51c199e-e435-4bbd-a977-dc96200729cc", "name": "Sticky Note50", "type": "n8n-nodes-base.stickyNote", "position": [-343.4815115846739, 6060], "parameters": {"color": 7, "width": 415.48118604428106, "height": 320.9196076003899, "content": "**Image Prompt**\n\nPrompt takes input of image description from the `set image description` node and generates using OpenAI"}, "typeVersion": 1}, {"id": "95a551f0-c164-4ac7-94e2-5aac4c5fc548", "name": "Sticky Note51", "type": "n8n-nodes-base.stickyNote", "position": [80, 6060], "parameters": {"color": 7, "width": 619.0692735087202, "height": 320.9196076003899, "content": "**Upload image to ImgBB, Optimise using ReSmush.it and store as URL**\n"}, "typeVersion": 1}, {"id": "93737b01-cd2f-4f49-b611-f47782a9eed8", "name": "Sticky Note52", "type": "n8n-nodes-base.stickyNote", "position": [-1160, 6020], "parameters": {"color": 4, "width": 773.6179704580734, "height": 875.8289847608302, "content": "## Convert Image Files (JPG, PNG, JPEG) to URLs and Reduce File Size\n\n## Use Case\nTransform and optimize images for web use:\n- You need to host local images online\n- You want to reduce image file sizes automatically\n- You need image URLs for web content\n- You want to generate and optimize AI-created images\n\n## What this Workflow Does\nThe workflow processes images through two services:\n- Uploads images to ImgBB for hosting and URL generation (free but need API key)\n- Optimizes images using ReSmush.it to reduce file size (free)\n- Optional: Creates images using OpenAI's image generation\n- Returns optimized image URLs ready for use\n\n## Setup\n1. Create an [ImgBB account](https://api.imgbb.com/) and get your API key\n2. Add your ImgBB API key to the HTTP Request node (key parameter)\n3. Optional: Configure OpenAI credentials for image generation\n4. Connect your image input source\n\n## How to Adjust it to Your Needs\n- Skip OpenAI nodes if using your own image files\n- Adjust image optimization parameters\n- Customize image hosting settings\n- Modify output format for your needs\n\n\nMade by <PERSON> @ [automake.io](https://automake.io)"}, "typeVersion": 1}, {"id": "8f4bfed3-820c-495d-9d5f-0dbdae7beb1a", "name": "Sticky Note53", "type": "n8n-nodes-base.stickyNote", "position": [80, 6400], "parameters": {"color": 3, "width": 620.*************, "height": 218.**************, "content": "**REQUIRED**\n\n**ImgBB - image hosting i.e. gives you an img url**\n1. [Create an ImgBB account](https://api.imgbb.com/) (free) and generate an api key\n2. Input the API key as Query Auth - `name`=key, `value`=your-own-api-key\n\n\n**ReSmush.it - image optimisation i.e. shrinks the file size of the image**\n1. No account or auth needed\n2. Url will pass from previous node"}, "typeVersion": 1}, {"id": "085ef8b4-4762-4675-a1fd-6771f09628fb", "name": "Sticky Note54", "type": "n8n-nodes-base.stickyNote", "position": [-340, 6400], "parameters": {"color": 2, "width": 409.*************, "height": 133.**************, "content": "**OPTIONAL**\n`Set image description` to create an Image using OpenAI and your own prompt (requires: API credentials) or alternatively replace these nodes with your own image file"}, "typeVersion": 1}, {"id": "ee6c01dd-94fd-4ebf-baf6-03360e01ffc0", "name": "Set image description", "type": "n8n-nodes-base.set", "position": [-300, 6220], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9026b5d5-97ed-484e-a168-ac1c57a60fa1", "name": "description", "type": "string", "value": "=Balancing Autonomy and Human Interaction in AI Applications, featuring a person"}]}}, "typeVersion": 3.4}, {"id": "7bb7374c-a11e-4ac8-8ef7-ba506fa8619d", "name": "Generate Image", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [-100, 6220], "parameters": {"prompt": "=Create a minimalist professional illustration of {{ $json.description }} with these specifications:\n\n1. Visual Style:\n- Modern tech-focused minimalist design\n- Clean, uncluttered composition\n- Professional business aesthetic\n- Soft shadows and subtle depth\n- 2-3 primary colors maximum plus white space\n\n2. Core Elements:\n- Main icon/symbol representing {{ $json.description }} as focal point\n- Simple supporting elements representing key sections\n- Subtle connecting elements showing relationship\n- Plenty of white space (40% minimum)\n- No text overlay\n\n3. Technical Requirements:\n- High contrast for clarity\n- Crisp edges and smooth lines\n- Professional lighting from upper left\n- Matte finish\n- Square aspect ratio (1:1)", "options": {}, "resource": "image"}, "credentials": {"openAiApi": {"id": "gaOzEcyxSfqBNYsI", "name": "OpenAi account"}}, "typeVersion": 1.4}, {"id": "87f80a8d-932a-46bc-b003-877883ba73c8", "name": "No Operation, do nothing", "type": "n8n-nodes-base.noOp", "position": [760, 6220], "parameters": {}, "typeVersion": 1}], "pinData": {}, "connections": {"Generate Image": {"main": [[{"node": "Upload Img to ImgBB for URL", "type": "main", "index": 0}]]}, "Set image description": {"main": [[{"node": "Generate Image", "type": "main", "index": 0}]]}, "Store Optimised Image ImgBB": {"main": [[{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "Upload Img to ImgBB for URL": {"main": [[{"node": "ReSmush.it Image Optimisation", "type": "main", "index": 0}]]}, "ReSmush.it Image Optimisation": {"main": [[{"node": "Store Optimised Image ImgBB", "type": "main", "index": 0}]]}}}