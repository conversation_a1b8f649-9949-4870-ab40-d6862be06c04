{"meta": {"instanceId": "e634e668fe1fc93a75c4f2a7fc0dad807ca318b79654157eadb9578496acbc76", "templateCredsSetupCompleted": true}, "nodes": [{"id": "ff5634c3-349b-4181-a03a-97b310e5232b", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [120, 60], "parameters": {}, "typeVersion": 1}, {"id": "66e204fb-3e00-45e0-b1b2-341836476b95", "name": "Extract page_info ", "type": "n8n-nodes-base.code", "position": [900, 120], "parameters": {"jsCode": "function parseNextParams(headerValue) {\n    // Match the URL inside <>\n    const urlMatch = headerValue.match(/<([^>]+)>;\\s*rel=\"next\"/);\n    if (!urlMatch) return null;\n\n    const url = urlMatch[1]; // Extracted URL\n    const paramsString = url.split(\"?\")[1]; // Get query string\n\n    if (!paramsString) return {}; // No params found\n\n    // Convert query string to object\n    return paramsString.split(\"&\").reduce((acc, param) => {\n        const [key, value] = param.split(\"=\");\n        acc[decodeURIComponent(key)] = decodeURIComponent(value);\n        return acc;\n    }, {});\n}\n\n/* Example usage\n`<https://59b774-3.myshopify.com/admin/api/2025-01/orders.json?limit=250&fields=id%2Cnote%2Cemail%2Cprocessed_at%2Ccustomer&page_info=eyJzdGF0dXMiOiJhbnkiLCJsYXN0X2lkIjo2MzQ5MjI3MDAwMDk0LCJsYXN0X3ZhbHVlIjoiMjAyNC0xMi0zMSAwOToxMzowMi42MTcxNjYiLCJkaXJlY3Rpb24iOiJuZXh0In0>; rel=\"next\"`\n*/\nconst headerValue = $input.first().json.headers.link;\nconst params = parseNextParams(headerValue);\nreturn params;"}, "typeVersion": 2}, {"id": "5b0086ce-f09b-4d55-86b6-9a14574506ab", "name": "Merge Loop items", "type": "n8n-nodes-base.code", "position": [1120, -100], "parameters": {"jsCode": "let results = [],\n  i = 0;\n\ndo {\n  try {\n    results = results.concat($(\"Get Customers\").all(0, i));\n  } catch (error) {\n    return results;\n  }\n  i++;\n} while (true);"}, "typeVersion": 2}, {"id": "2302257c-51c0-42d7-8745-ecc0b4fc9faf", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [440, -160], "parameters": {"width": 232, "height": 346, "content": "## Edit this node 👇\n\nGet your store URL and replace in the GET url: https://{your-store}.myshopify.com/admin/api/2025-01/customers.json\n"}, "typeVersion": 1}, {"id": "d857962a-6599-44b2-acb0-3eb8165e93ce", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1560, -440], "parameters": {"width": 272, "height": 506, "content": "## Clone this spreadsheet\n\nhttps://docs.google.com/spreadsheets/d/1E8i98hwiFW7XG9HuxIZrOWfuLxGFaDm3EOAGQBZjhfk/edit?usp=sharing\n\nYour spreadsheet can have up to three columns, and need to be arranged in this order (no header):\n\nEmail address\nFirst name (optional)\nLast name (optional)\nShopify Customer ID (will be ignored)"}, "typeVersion": 1}, {"id": "be7cf143-893a-44f8-ace9-8ad581bddb68", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [120, -120], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.2}, {"id": "58097363-29ec-4067-a439-717d355df91f", "name": "Assign page_info parameter", "type": "n8n-nodes-base.set", "position": [1120, 120], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "57e59bb7-ac20-4a1b-b54a-3468fc0519d3", "name": "page_info", "type": "string", "value": "={{ $json.page_info }}"}]}}, "typeVersion": 3.4}, {"id": "0676abce-4405-42a1-87d3-ba75355fe264", "name": "Check page_info existence", "type": "n8n-nodes-base.if", "position": [720, 0], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "30d965c3-cbba-430e-81c2-ef8b543665e7", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.headers.link }}", "rightValue": "rel=\"next\""}]}}, "typeVersion": 2.2}, {"id": "1cc63979-b2f8-4678-b40b-f3f0ad63d377", "name": "Get Customers", "type": "n8n-nodes-base.httpRequest", "position": [500, 0], "parameters": {"url": "https://{your-store}.myshopify.com/admin/api/2025-01/customers.json", "options": {"response": {"response": {"fullResponse": true}}}, "sendQuery": true, "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "limit", "value": "250"}, {"name": "fields", "value": "id,email,first_name,last_name"}, {"name": "={{ $json.page_info ? \"page_info\" : \"status\" }}", "value": "={{ $json.page_info ? $json.page_info : 'any' }}"}]}, "nodeCredentialType": "shopifyAccessTokenApi"}, "credentials": {"shopifyAccessTokenApi": {"id": "vtyKGPLLdjc7MLea", "name": "Shopify Access Token account"}}, "typeVersion": 4.2}, {"id": "ce91af42-1634-4773-944a-2b24dcaf812b", "name": "List Customers", "type": "n8n-nodes-base.splitOut", "position": [1380, -100], "parameters": {"options": {}, "fieldToSplitOut": "body.customers"}, "typeVersion": 1}, {"id": "19a0f6a7-b86a-43bd-8504-62e3bd37af89", "name": "Customers Spreadsheet", "type": "n8n-nodes-base.googleSheets", "position": [1620, -100], "parameters": {"columns": {"value": {"Last name": "={{ $json.last_name }}", "First name": "={{ $json.first_name }}", "Email address": "={{ $json.email }}", "Shopify Customer ID": "={{ $json.id }}"}, "schema": [{"id": "Email address", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Email address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "First name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "First name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Last name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Last name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Shopify Customer ID", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Shopify Customer ID", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["Shopify Customer ID"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "list", "value": **********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1yf_RYZGFHpMyOvD3RKGSvIFY2vumvI4474Qm_1t4-jM/edit#gid=**********", "cachedResultName": "sqs_contacts"}, "documentId": {"__rl": true, "mode": "list", "value": "1yf_RYZGFHpMyOvD3RKGSvIFY2vumvI4474Qm_1t4-jM", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1yf_RYZGFHpMyOvD3RKGSvIFY2vumvI4474Qm_1t4-jM/edit?usp=drivesdk", "cachedResultName": "Squarespace automation"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JgI9maibw5DnBXRP", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "48ec5e3b-9bb5-451c-9495-b03080c9211e", "name": "Convert to Squarespace contacts csv", "type": "n8n-nodes-base.convertToFile", "position": [1920, 160], "parameters": {"options": {"headerRow": false}}, "typeVersion": 1.1}, {"id": "8de9174a-af87-4602-a9aa-a5c35a3f0ed4", "name": "Extract customers data", "type": "n8n-nodes-base.set", "position": [1620, 160], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "27c42d50-df07-46b4-b67a-13a1b64b5145", "name": "Email address", "type": "string", "value": "={{ $json['Email address'] }}"}, {"id": "9fd2c3fd-9b03-4562-ad78-9ce30da7bb26", "name": "First name", "type": "string", "value": "={{ $json['First name'] }}"}, {"id": "f51b7da6-0065-41ea-b04c-420058ce3b9c", "name": "Last name", "type": "string", "value": "={{ $json['Last name'] }}"}]}}, "typeVersion": 3.4}], "pinData": {}, "connections": {"Get Customers": {"main": [[{"node": "Check page_info existence", "type": "main", "index": 0}]]}, "List Customers": {"main": [[{"node": "Customers Spreadsheet", "type": "main", "index": 0}, {"node": "Extract customers data", "type": "main", "index": 0}]]}, "Merge Loop items": {"main": [[{"node": "List Customers", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Get Customers", "type": "main", "index": 0}]]}, "Extract page_info ": {"main": [[{"node": "Assign page_info parameter", "type": "main", "index": 0}]]}, "Customers Spreadsheet": {"main": [[]]}, "Extract customers data": {"main": [[{"node": "Convert to Squarespace contacts csv", "type": "main", "index": 0}]]}, "Check page_info existence": {"main": [[{"node": "Merge Loop items", "type": "main", "index": 0}], [{"node": "Extract page_info ", "type": "main", "index": 0}]]}, "Assign page_info parameter": {"main": [[{"node": "Get Customers", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Get Customers", "type": "main", "index": 0}]]}, "Convert to Squarespace contacts csv": {"main": [[]]}}}