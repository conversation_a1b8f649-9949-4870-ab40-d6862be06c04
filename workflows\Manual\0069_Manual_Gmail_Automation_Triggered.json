{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [-40, 240], "parameters": {}, "typeVersion": 1}, {"name": "Gmail", "type": "n8n-nodes-base.gmail", "position": [150, 240], "parameters": {"resource": "message", "operation": "getAll", "returnAll": true, "additionalFields": {"q": "-in:chats unsubscribe -license -key -password", "format": "ids"}}, "credentials": {"gmailOAuth2": "Gmail"}, "typeVersion": 1}, {"name": "Delete Old Gmail", "type": "n8n-nodes-base.gmail", "position": [500, 410], "parameters": {"resource": "message", "messageId": "={{$json[\"id\"]}}", "operation": "delete"}, "credentials": {"gmailOAuth2": "Gmail"}, "typeVersion": 1}, {"name": "SplitInBatches", "type": "n8n-nodes-base.splitInBatches", "position": [310, 240], "parameters": {"options": {}, "batchSize": 100}, "typeVersion": 1}], "connections": {"Gmail": {"main": [[{"node": "SplitInBatches", "type": "main", "index": 0}]]}, "SplitInBatches": {"main": [[{"node": "Delete Old Gmail", "type": "main", "index": 0}]]}, "Delete Old Gmail": {"main": [[{"node": "SplitInBatches", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Gmail", "type": "main", "index": 0}]]}}}