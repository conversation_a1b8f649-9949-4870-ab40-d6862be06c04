{"id": "175", "name": "Get messages with a certain label, remove the label, and add a new one", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "Gmail", "type": "n8n-nodes-base.gmail", "position": [450, 300], "parameters": {"resource": "message", "operation": "getAll", "additionalFields": {"format": "full", "labelIds": ["Label_103811885290186237"]}}, "credentials": {"gmailOAuth2": "Gmail"}, "typeVersion": 1}, {"name": "Gmail1", "type": "n8n-nodes-base.gmail", "position": [650, 300], "parameters": {"labelIds": ["Label_103811885290186237"], "resource": "messageLabel", "messageId": "={{$node[\"Gmail\"].json[\"id\"]}}", "operation": "remove"}, "credentials": {"gmailOAuth2": "Gmail"}, "typeVersion": 1}, {"name": "Gmail2", "type": "n8n-nodes-base.gmail", "position": [850, 300], "parameters": {"labelIds": ["Label_140673791182006844"], "resource": "messageLabel", "messageId": "={{$node[\"Gmail\"].json[\"id\"]}}"}, "credentials": {"gmailOAuth2": "Gmail"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Gmail": {"main": [[{"node": "Gmail1", "type": "main", "index": 0}]]}, "Gmail1": {"main": [[{"node": "Gmail2", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Gmail", "type": "main", "index": 0}]]}}}