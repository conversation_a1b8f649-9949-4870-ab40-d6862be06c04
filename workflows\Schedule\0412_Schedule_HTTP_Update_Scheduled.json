{"meta": {"instanceId": "21754f977ce20b07e6fe64be3fbc663f6e6f730423d6e46c6cd2bf5b5e70a383"}, "nodes": [{"id": "49a3829e-3127-4761-8ac0-edaa6d0224c2", "name": "HTTP Request - Utilize Prospeo.io LinkedIn Email Finder API1", "type": "n8n-nodes-base.httpRequest", "position": [3820, 680], "parameters": {"url": "https://api.prospeo.io/linkedin-email-finder", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "bodyParameters": {"parameters": [{"name": "url", "value": "={{ $json['Linkden URL'] }}"}, {"name": "id", "value": "={{ $json.ID }}"}]}, "headerParameters": {"parameters": [{"name": "X-KEY", "value": "43b7e4f5c6558ccaa539e0e5f5778f09"}]}}, "typeVersion": 4.1}, {"id": "705aef1b-5e45-4fe8-b1fd-5ebb5d461dd5", "name": "No Operation, do nothing", "type": "n8n-nodes-base.noOp", "position": [3760, 1140], "parameters": {}, "typeVersion": 1}, {"id": "f2b8a1b8-13b6-4de3-9cde-336494bf31dc", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [3140, 940], "parameters": {"rule": {"interval": [{"field": "minutes"}]}}, "typeVersion": 1.1}, {"id": "28b1d9c4-c89e-460d-8a5b-0fea42d2d1d8", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [3660, 460], "parameters": {"color": 4, "width": 468.54622634118857, "height": 380.67540639666913, "content": "- Utilized the following API: \n  - [Prospeo.io LinkedIn Email Finder API](https://prospeo.io/api/linkedin-email-finder)\n\n- **Benefit:**\n  - The benefit of this API is to provide an efficient way to find email addresses of users on LinkedIn, aiding in updating and enriching data more accurately and comprehensively in Google Sheets or any other system used in the workflow.\n"}, "typeVersion": 1}, {"id": "d8edc9fa-3012-46ab-9ed2-473f55213e78", "name": "Conditional Check", "type": "n8n-nodes-base.if", "position": [3520, 940], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "2b355bc4-0ef4-415a-a437-d8ed6538c1e3", "operator": {"type": "string", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json.Name }}", "rightValue": ""}, {"id": "1757a7d7-ce91-4df1-b54d-c9285f88e3ee", "operator": {"type": "string", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json.Gender }}", "rightValue": ""}, {"id": "78089c18-e9d6-40e5-8d0c-e2b96c1f1348", "operator": {"type": "string", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json['Job Title'] }}", "rightValue": ""}, {"id": "0ee10296-113d-4467-92d7-368111426cf5", "operator": {"type": "string", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json.<PERSON>y }}", "rightValue": ""}, {"id": "2ec7486d-e753-4c87-a6df-10056c7ee4b2", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json['Linkden URL'] }}", "rightValue": ""}]}}, "typeVersion": 2}, {"id": "bdf82dbf-7b6b-4d42-9a6a-34d5cfb691ad", "name": "Field Editing", "type": "n8n-nodes-base.set", "position": [4100, 900], "parameters": {"fields": {"values": [{"name": "Name", "stringValue": "={{ $json.response.full_name }}"}, {"name": "Gender", "stringValue": "={{ $json.response.gender }}"}, {"name": "Email", "stringValue": "={{ $json.response.email.email }}"}, {"name": "Summary", "stringValue": "={{ $json.response.summary}}"}, {"name": "Education", "stringValue": "={{ $json.response.education[0].school.name }}"}, {"name": "Skills", "stringValue": "={{ $json.response.skills }}"}, {"name": "Picture", "stringValue": "={{ $json.response.picture }}"}, {"name": "Job Title", "stringValue": "={{ $json.response.job_title }}"}, {"name": "Location", "stringValue": "={{ $json.response.location.raw }}"}, {"name": "Linkden link", "stringValue": "={{ $json.response.linkedin }}"}, {"name": "ID", "stringValue": "={{ $json.ID }}"}]}, "include": "selected", "options": {}}, "typeVersion": 3.2}, {"id": "897734e2-5d05-4480-a24b-e4b3ae44dce6", "name": "Data Merge", "type": "n8n-nodes-base.merge", "position": [3860, 900], "parameters": {"mode": "combine", "options": {}, "combinationMode": "mergeByPosition"}, "typeVersion": 2.1}, {"id": "92a9861d-9e42-4fe2-84a7-03b3b0dbb1b0", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [2300, 520], "parameters": {"color": 5, "width": 803.4846614963799, "height": 938.2393052135303, "content": "\n- **Schedule Trigger:**\n  - Description: This node triggers the workflow based on a defined schedule interval, in this case, based on minutes.\n  - Documentation: [Schedule Trigger Node](https://docs.n8n.io/nodes/n8n-nodes-base.scheduleTrigger)\n\n- **Google Sheets Read:**\n  - Description: This node reads data from a Google Sheets document and sheet based on the provided document ID and sheet name.\n  - Documentation: [Google Sheets Node](https://docs.n8n.io/nodes/n8n-nodes-base.googleSheets)\n\n- **Conditional Check:**\n  - Description: This node checks multiple conditions based on the input data and performs further actions based on the conditions.\n  - Documentation: [Conditional Node](https://docs.n8n.io/nodes/n8n-nodes-base.if)\n\n- **HTTP Request:**\n  - Description: This node sends an HTTP POST request to a specified URL with the provided headers and body parameters.\n  - Documentation: [HTTP Request Node](https://docs.n8n.io/nodes/n8n-nodes-base.httpRequest)\n\n- **No Operation, do nothing:**\n  - Description: This node does not perform any operation and is used as a placeholder.\n  - Documentation: [No Operation Node](https://docs.n8n.io/nodes/n8n-nodes-base.noOp)\n\n\n- **Data Merge:**\n  - Description: This node merges data based on the specified mode and combination settings to combine multiple fields into a single object.\n  - Documentation: [Merge Node](https://docs.n8n.io/nodes/n8n-nodes-base.merge)\n\n- **Field Editing:**\n  - Description: This node edits fields by setting specific values for each field based on the provided input data.\n  - Documentation: [Set Node](https://docs.n8n.io/nodes/n8n-nodes-base.set)\n\n\n- **Google Sheets Update:**\n  - Description: This node updates data in a Google Sheets document and sheet based on the specified columns and values.\n  - Documentation: [Google Sheets Node](https://docs.n8n.io/nodes/n8n-nodes-base.googleSheets)\n\n"}, "typeVersion": 1}, {"id": "644f38d3-ccf0-4ce3-b759-e129e1074512", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [2260, 240], "parameters": {"width": 2292.************, "height": 1214.0709576942727, "content": "## Find contact information for linkedin profile URLs stored in a Google Sheet\n**Video link.** [Guide](https://www.canva.com/design/DAF9a_UBxWY/xSSlSUzRdxCPtfgx9RzGSg/watch?utm_content=DAF9a_UBxWY&utm_campaign=designshare&utm_medium=link&utm_source=editor)"}, "typeVersion": 1}, {"id": "8ddaddad-b976-46c5-b8a1-e49ecb493e87", "name": "Get links from Google Sheet", "type": "n8n-nodes-base.googleSheets", "position": [3340, 940], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ochnGSCy8V5Mz-nr51dBmugqR50m62K7d6pvbwOHewo/edit#gid=0", "cachedResultName": "الورقة1"}, "documentId": {"__rl": true, "mode": "list", "value": "1ochnGSCy8V5Mz-nr51dBmugqR50m62K7d6pvbwOHewo", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ochnGSCy8V5Mz-nr51dBmugqR50m62K7d6pvbwOHewo/edit?usp=drivesdk", "cachedResultName": "linkden URls"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "L5CnisK8R3BgVGcO", "name": "Omar sheet"}}, "typeVersion": 4.2}, {"id": "0923a13d-1097-432d-b22e-375fec9f383e", "name": "Update the sheet with information", "type": "n8n-nodes-base.googleSheets", "position": [4320, 900], "parameters": {"columns": {"value": {"ID": "={{ $json.ID }}", "Name": "={{ $json.Name }}", "Email": "={{ $json.Email }}", "Gender": "={{ $json.Gender }}", "Skills": "={{ $json.Skills }}", "Picture": "={{ $json.Picture }}", "Summery": "={{ $json.Summary }}", "Location": "={{ $json.Location }}", "Education": "={{ $json.Education }}", "Job Title": "={{ $json['Job Title'] }}"}, "schema": [{"id": "ID", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Linkden URL", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Linkden URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Name", "type": "string", "display": true, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Gender", "type": "string", "display": true, "required": false, "displayName": "Gender", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Email", "type": "string", "display": true, "required": false, "displayName": "Email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Education", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Education", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Location", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Location", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Job Title", "type": "string", "display": true, "required": false, "displayName": "Job Title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Summery", "type": "string", "display": true, "required": false, "displayName": "Summery", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Skills", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Skills", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Picture", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Picture", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["ID"]}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ochnGSCy8V5Mz-nr51dBmugqR50m62K7d6pvbwOHewo/edit#gid=0", "cachedResultName": "الورقة1"}, "documentId": {"__rl": true, "mode": "list", "value": "1ochnGSCy8V5Mz-nr51dBmugqR50m62K7d6pvbwOHewo", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ochnGSCy8V5Mz-nr51dBmugqR50m62K7d6pvbwOHewo/edit?usp=drivesdk", "cachedResultName": "linkden URls"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "L5CnisK8R3BgVGcO", "name": "Omar sheet"}}, "typeVersion": 4.2}], "pinData": {}, "connections": {"Data Merge": {"main": [[{"node": "Field Editing", "type": "main", "index": 0}]]}, "Field Editing": {"main": [[{"node": "Update the sheet with information", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Get links from Google Sheet", "type": "main", "index": 0}]]}, "Conditional Check": {"main": [[{"node": "Data Merge", "type": "main", "index": 1}, {"node": "HTTP Request - Utilize Prospeo.io LinkedIn Email Finder API1", "type": "main", "index": 0}], [{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "Get links from Google Sheet": {"main": [[{"node": "Conditional Check", "type": "main", "index": 0}]]}, "HTTP Request - Utilize Prospeo.io LinkedIn Email Finder API1": {"main": [[{"node": "Data Merge", "type": "main", "index": 0}]]}}}