{"meta": {"instanceId": "4a11afdb3c52fd098e3eae9fad4b39fdf1bbcde142f596adda46c795e366b326"}, "nodes": [{"id": "17ca0437-6101-4277-9ed2-e37e6b92df02", "name": "When clicking 'Test workflow'", "type": "n8n-nodes-base.manualTrigger", "position": [-160, 280], "parameters": {}, "typeVersion": 1}, {"id": "d3dd600a-2ab5-4d52-92ef-ab3f29dd1790", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [260, 400], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "typeVersion": 1.2}, {"id": "c29d58a2-243b-41ab-99c6-f8a8c92219cf", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [460, 400], "parameters": {"schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"message\": {\n      \"type\": \"string\"\n    },\n    \"mail_object\": {\n      \"type\": \"string\"\n    }\n  }\n}"}, "typeVersion": 1.2}, {"id": "3cb31448-5bc3-47c2-a119-d9e33a464d1f", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-160, 80], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.2}, {"id": "18b243a5-db1f-4a27-a8a1-3a7c74135d6d", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [580, 20], "parameters": {"width": 260, "height": 120, "content": "## ElevenlabsAPI key\n**Click** to get your Elevenlabs  API key. [Elevenlabs](https://try.elevenlabs.io/text-audio)"}, "typeVersion": 1}, {"id": "62a9bd08-27f8-45a8-9eb4-30950500a36f", "name": "Change filename", "type": "n8n-nodes-base.code", "position": [880, 180], "parameters": {"jsCode": "/*\n * Filename: addFileName.js\n * Purpose: Add a file name to binary data in an n8n workflow using mail_object from input\n */\n\nconst mailObject = $input.first().json.output.mail_object;\nconst fileName = `${mailObject}.mp3`;\n\nreturn items.map(item => {\n  if (item.binary && item.binary.data) {\n    item.binary.data.fileName = fileName;\n  }\n  return item;\n});"}, "typeVersion": 2}, {"id": "41043058-ca06-4c3a-8b7d-597e2941d92b", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1020, 20], "parameters": {"width": 300, "height": 120, "content": "## Gmail API Credentials  \n**Click here** to view the [documentation](https://docs.n8n.io/integrations/builtin/credentials/google/) and configure your access permissions for the Google Gmail API."}, "typeVersion": 1}, {"id": "3475e3ae-439d-4245-8994-4444266a67e3", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [0, 0], "parameters": {"width": 300, "height": 140, "content": "## Calendar API Credentials  \n**Click here** to view the [documentation](https://docs.n8n.io/integrations/builtin/credentials/google/) and configure your access permissions for the Google Calendar API."}, "typeVersion": 1}, {"id": "7784fc2d-3e64-40f0-990f-965fba4ad67c", "name": "Generate Voice Reminder", "type": "n8n-nodes-base.httpRequest", "position": [660, 180], "parameters": {"url": "https://api.elevenlabs.io/v1/text-to-speech/JBFqnCBsd6RMkjVDRZzb", "method": "POST", "options": {}, "sendBody": true, "sendQuery": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "text", "value": "={{ $json.output.message }}"}, {"name": "model_id", "value": "eleven_multilingual_v2"}]}, "genericAuthType": "httpCustomAuth", "queryParameters": {"parameters": [{"name": "output_format", "value": "mp3_22050_32"}]}}, "notesInFlow": true, "retryOnFail": true, "typeVersion": 4.2}, {"id": "a2081f29-493b-43c0-bad5-1b273d5db527", "name": "Send Voice Reminder", "type": "n8n-nodes-base.gmail", "position": [1100, 180], "webhookId": "5ba2c8cb-84f1-4363-8410-b8d138286c3a", "parameters": {"sendTo": "={{ $('Get Appointments').item.json.attendees[0].email }}", "message": "=👇 Information for tomorrow 🗣️", "options": {"senderName": "<PERSON>", "attachmentsUi": {"attachmentsBinary": [{}]}, "appendAttribution": false}, "subject": "={{ $('create message').item.json.output.mail_object }}"}, "typeVersion": 2.1}, {"id": "dd3bf7b2-f951-452a-8912-47ceace50cc0", "name": "create message", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [280, 180], "parameters": {"text": "=name: {{ $json.summary }}\ntime: {{ $json.start.dateTime }}\naddress: {{ $json.location }}\nToday's date: {{ $now }}", "messages": {"messageValues": [{"message": "=You are an assistant. You will create a structured message in JSON.\n\n**\nmessage:\nGenerate a voice script reminder for a real estate appointment. The message should be clear, professional, and engaging.\n\nIt must include:\n1. The recipient's name.\n2. The date and time of the appointment, expressed naturally (e.g., at noon, quarter past noon, half past three, quarter to five).\n3. The complete address of the property, expressed naturally (e.g., 12 Baker Street in London, Madison Avenue in New York, 5 Oakwood Drive in Los Angeles).\n4. A mention of the sender: Mr. <PERSON> from Super Agency.\n5. A confirmation sentence or an invitation to contact if needed.\n\nInput variables:\n• Recipient's name (prefixed with Mr. or Ms.)\n• Time: Appointment time\n• Address: Complete property address (only the street, number, and city; not the postal code)\n\nThe tone should be cordial and professional, suitable for an automated voice message.\n\nExample expected output: \"Hello Mrs. <PERSON>, this is Mr. <PERSON> from Super Immo Agency.\nI am reminding you of your appointment scheduled for tomorrow at 8:15, at 63 Taverniers Road in Talence. If you have any questions or need to reschedule, please do not hesitate to contact me. See you tomorrow and have a great day!\"\n\n**\nmail_object: a very short email subject\nExample: Your appointment reminder for tomorrow"}]}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.5}, {"id": "63806db8-6814-4fe4-ba2e-80511273ee51", "name": "Get Appointments", "type": "n8n-nodes-base.googleCalendar", "position": [60, 180], "parameters": {"limit": 2, "options": {}, "timeMax": "={{ $now.plus({ day: 2 }) }}", "calendar": {"__rl": true, "mode": "list", "value": "<EMAIL>", "cachedResultName": "<EMAIL>"}, "operation": "getAll"}, "typeVersion": 1.3}], "pinData": {}, "connections": {"create message": {"main": [[{"node": "Generate Voice Reminder", "type": "main", "index": 0}]]}, "Change filename": {"main": [[{"node": "Send Voice Reminder", "type": "main", "index": 0}]]}, "Get Appointments": {"main": [[{"node": "create message", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Get Appointments", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "create message", "type": "ai_languageModel", "index": 0}]]}, "Generate Voice Reminder": {"main": [[{"node": "Change filename", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "create message", "type": "ai_outputParser", "index": 0}]]}, "When clicking 'Test workflow'": {"main": [[{"node": "Get Appointments", "type": "main", "index": 0}]]}}}