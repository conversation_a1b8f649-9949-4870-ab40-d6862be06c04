{"meta": {"instanceId": "9890889b6220dd611ebaa1144286714cf45b0e89f22a3c881f9e9d30deb831db"}, "nodes": [{"id": "b9962fd6-af11-4a3a-935c-c168ac85eaa1", "name": "When clicking \"Test workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [80, 300], "parameters": {}, "typeVersion": 1}, {"id": "2ba3fe3a-e4c5-4014-8cb2-80716f18b222", "name": "Get records", "type": "n8n-nodes-base.airtable", "position": [300, 300], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appkkDhXu7vZCFspD", "cachedResultUrl": "https://airtable.com/appkkDhXu7vZCFspD", "cachedResultName": "n8n test"}, "limit": 10, "table": {"__rl": true, "mode": "list", "value": "tblMdmUiSTBrvrLq3", "cachedResultUrl": "https://airtable.com/appkkDhXu7vZCFspD/tblMdmUiSTBrvrLq3", "cachedResultName": "SEO meta title & desc"}, "options": {}, "operation": "search", "returnAll": false, "filterByFormula": "=AND(url != \"\", {title tag} = \"\", {meta desc} = \"\")"}, "credentials": {"airtableTokenApi": {"id": "yw6pm1U4Hw8kKDhu", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2}, {"id": "0f26bb3c-f2cc-476b-b1af-3d4cd98463ce", "name": "Get url content", "type": "n8n-nodes-base.httpRequest", "position": [500, 300], "parameters": {"url": "={{ $json.url }}", "options": {}}, "typeVersion": 4.2}, {"id": "3c67c390-5144-44cb-8618-d7e7e6c6cae5", "name": "Extract title tag and meta description", "type": "n8n-nodes-base.html", "position": [700, 300], "parameters": {"options": {}, "operation": "extractHtmlContent", "extractionValues": {"values": [{"key": "titleTag", "cssSelector": "title"}, {"key": "metaDesc", "attribute": "content", "cssSelector": "meta[name=\"description\"]", "returnValue": "attribute"}]}}, "typeVersion": 1.2}, {"id": "7028b7af-0959-4ed5-bc54-fceb2e224976", "name": "Update original record", "type": "n8n-nodes-base.airtable", "position": [940, 300], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appkkDhXu7vZCFspD", "cachedResultUrl": "https://airtable.com/appkkDhXu7vZCFspD", "cachedResultName": "n8n test"}, "table": {"__rl": true, "mode": "list", "value": "tblMdmUiSTBrvrLq3", "cachedResultUrl": "https://airtable.com/appkkDhXu7vZCFspD/tblMdmUiSTBrvrLq3", "cachedResultName": "SEO meta title & desc"}, "columns": {"value": {"id": "={{ $('Get records').item.json.id }}", "meta desc": "={{ $json.metaDesc }}", "title tag": "={{ $json.titleTag }}"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "url", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "title tag", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "title tag", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "meta desc", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "meta desc", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Created", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Created", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Calculation", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Calculation", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["id"]}, "options": {}, "operation": "update"}, "credentials": {"airtableTokenApi": {"id": "yw6pm1U4Hw8kKDhu", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2}, {"id": "5b518969-553e-462f-ad4f-eb07e9b17eef", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [140, -60], "parameters": {"width": 862.*************, "height": 316.*************, "content": "## How to use the workflow\n1. Set a Base in Airtable with a table with the following structure:\n  `url`, `title tag`, `meta desc`\n2. Connect Airtable to the nodes and, with the following formula, get all the records that miss `title tag` and `meta desc`.\n3. Put a bunch of url in the table in the field `url` and let the workflow work.\n\n## Extra\n\n* You can also calculate the length for title tag and meta desc using formula field inside Airtable. This is the formula:\n  `LEN({title tag})` or `LEN({meta desc})`\n* You can automate the process calling a Webhook from Airtable. For this, you need an Airtable paid plan."}, "typeVersion": 1}], "pinData": {}, "connections": {"Get records": {"main": [[{"node": "Get url content", "type": "main", "index": 0}]]}, "Get url content": {"main": [[{"node": "Extract title tag and meta description", "type": "main", "index": 0}]]}, "When clicking \"Test workflow\"": {"main": [[{"node": "Get records", "type": "main", "index": 0}]]}, "Extract title tag and meta description": {"main": [[{"node": "Update original record", "type": "main", "index": 0}]]}}}