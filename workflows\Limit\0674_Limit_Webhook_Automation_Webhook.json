{"meta": {"instanceId": "03e9d14e9196363fe7191ce21dc0bb17387a6e755dcc9acc4f5904752919dca8"}, "nodes": [{"id": "05096721-e15a-4d2a-83b3-3b31d6435c59", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.gmailTrigger", "disabled": true, "position": [-680, -140], "parameters": {"simple": false, "filters": {}, "options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}}, "credentials": {"gmailOAuth2": {"id": "kkhNhqKpZt6IUZd0", "name": "Gmail"}}, "typeVersion": 1.2}, {"id": "9eb59c41-fa15-45ee-b343-cf30ac058600", "name": "Gmail - Extract Received Headers", "type": "n8n-nodes-base.code", "position": [200, -80], "parameters": {"jsCode": "// Extract the headers object from the JSON\nconst headers = $('Gmail - Set Headers').item.json.headers;\n\n// Find all keys that start with \"received\" (case-insensitive)\nconst receivedHeaders = Object.entries(headers)\n    .filter(([key, value]) => key.toLowerCase() === 'received')\n    .map(([key, value]) => ({ key, value }));\n\n// Return each header as an object\nreturn receivedHeaders.map(header => ({ json: header }));\n"}, "executeOnce": false, "typeVersion": 2}, {"id": "05ba1e0a-1f47-492b-b57c-c82b2b8af99d", "name": "Gmail - Extract Original From IP", "type": "n8n-nodes-base.set", "position": [620, -80], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "5f740d1f-de62-4fe0-aa20-625063344c07", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "value": "={{ $json.value.replace(/\\b(127\\.(?:\\d{1,3}\\.){2}\\d{1,3})|(10\\.(?:\\d{1,3}\\.){2}\\d{1,3})|(172\\.(?:1[6-9]|2[0-9]|3[0-1])\\.\\d{1,3}\\.\\d{1,3})|(192\\.168\\.\\d{1,3}\\.\\d{1,3})\\b/g, \"\").match(/(\\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])(\\.(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])(\\.(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])(\\.(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])(\\.(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])(\\.(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])(\\.(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])(\\.(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])){3}))|:)))(%.+)?\\s*)|(\\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)[.]){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\b)/)[0] }}"}]}}, "typeVersion": 3.4}, {"id": "86bdebd4-fa96-4622-bc9d-67cea96486a4", "name": "Gmail - Original IP Found?", "type": "n8n-nodes-base.if", "position": [840, -20], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "1c27e7ba-d243-4673-b1cc-608c35951168", "operator": {"type": "boolean", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.extractedfromip?.toBoolean() }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "18c23866-58fc-4cdb-9bea-961da75dbac5", "name": "Gmail - Query IP Quality Score API", "type": "n8n-nodes-base.httpRequest", "position": [1080, -160], "parameters": {"url": "=https://ipqualityscore.com/api/json/ip/********************************/{{ $('Gmail - Extract Original From IP').item.json.extractedfromip }}?strictness=1&allow_public_access_points=true&lighter_penalties=true", "options": {}}, "typeVersion": 4.2}, {"id": "9b35ce2c-d382-41b2-8e31-3238cc7c83bc", "name": "Gmail - Query IP API", "type": "n8n-nodes-base.httpRequest", "position": [1280, -160], "parameters": {"url": "=http://ip-api.com/json/{{ $('Gmail - Extract Original From IP').item.json.extractedfromip }}", "options": {}}, "typeVersion": 4.2}, {"id": "dbd95b55-f54a-477e-bdfe-4fd564b71154", "name": "Gmail - Authentication-Results Header?", "type": "n8n-nodes-base.if", "position": [1480, -20], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "ead2b640-ad80-4189-a692-ae454723fd85", "operator": {"type": "array", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ Object.entries($('Gmail - Set Headers').item.json.headers)\n    .filter(([key, value]) => key.toLowerCase() === 'authentication-results')\n    .map(([key, value]) => ({ key, value })) }}", "rightValue": "true"}]}}, "typeVersion": 2.2}, {"id": "972aee72-e5fd-4215-91d5-ea099b0ce379", "name": "Gmail - Received-<PERSON><PERSON>?", "type": "n8n-nodes-base.if", "position": [1820, 620], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a38ebc9b-f896-4432-81fb-4f3db98f3409", "operator": {"type": "array", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ Object.entries($('Gmail - Set Headers').item.json.headers)\n    .filter(([key, value]) => key.toLowerCase() === 'received-spf')\n    .map(([key, value]) => ({ key, value })) }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "814810d9-46d4-4c4b-8b48-09504b39bab9", "name": "Gmail - Extract Authentication-Results Header", "type": "n8n-nodes-base.code", "position": [1840, -180], "parameters": {"jsCode": "// Extract the headers object from the JSON\nconst headers = $('Gmail - Set Headers').item.json.headers;\n\n// Find all keys that start with \"received\" (case-insensitive)\nconst receivedHeaders = Object.entries(headers)\n    .filter(([key, value]) => key.toLowerCase() === 'authentication-results')\n    .map(([key, value]) => ({ key, value }));\n\n// Return each header as an object\nreturn receivedHeaders.map(header => ({ json: header }));\n"}, "executeOnce": false, "typeVersion": 2}, {"id": "98fd0bec-db8c-41bf-b5da-b485872366a5", "name": "Gmail - Extract Received-<PERSON><PERSON>", "type": "n8n-nodes-base.code", "position": [2160, 460], "parameters": {"jsCode": "// Extract the headers object from the JSON\nconst headers = $('Gmail - Set Headers').item.json.headers;\n\n// Find all keys that start with \"received\" (case-insensitive)\nconst receivedHeaders = Object.entries(headers)\n    .filter(([key, value]) => key.toLowerCase() === 'received-spf')\n    .map(([key, value]) => ({ key, value }));\n\n// Return each header as an object\nreturn receivedHeaders.map(header => ({ json: header }));\n"}, "executeOnce": false, "typeVersion": 2}, {"id": "6c893235-19dd-40fb-860d-368de317907b", "name": "Gmail - Determine <PERSON>th <PERSON>s", "type": "n8n-nodes-base.set", "position": [2560, -180], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "cd0b3f49-fe38-4686-a1f5-bc03a145adef", "name": "spfvalue", "type": "string", "value": "={{ $json.value.toLowerCase().includes('spf=pass') ? \"pass\" : $json.value.toLowerCase().includes('spf=fail') ? \"fail\" : $json.value.toLowerCase().includes('spf=neutral') ? \"neutral\" : \"unknown\" }}"}, {"id": "6aa90f4d-773e-475f-8cbc-fe5c4fe93653", "name": "dkimvalue", "type": "string", "value": "={{ $json.value.toLowerCase().includes('dkim=pass') ? \"pass\" : $json.value.toLowerCase().includes('dkim=fail') ? \"fail\" : $json.value.toLowerCase().includes('dkim=temperror') ? \"error\" : \"unknown\" }}"}, {"id": "d3b7b0c1-0680-4cb9-b376-d365e5602a29", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "string", "value": "={{ $json.value.toLowerCase().includes('dmarc=pass') ? \"pass\" : $json.value.toLowerCase().includes('dmarc=fail') ? \"fail\" : \"unknown\" }}"}]}}, "typeVersion": 3.4}, {"id": "66203758-da3b-499a-a95e-2e04f196fc30", "name": "Gmail - Set SPF Value", "type": "n8n-nodes-base.set", "position": [2600, 460], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "179c48eb-97e5-48ab-82b8-ef4269f11366", "name": "spfvalue", "type": "string", "value": "={{ $json.data.last().value.toLowerCase().includes('fail') ? \"fail\" : $json.data.last().value.toLowerCase().includes('pass') ? \"pass\" : \"unknown\"}}"}]}}, "typeVersion": 3.4}, {"id": "8393c205-673a-46d7-977e-9a60720b1c39", "name": "Gmail - No SPF Found", "type": "n8n-nodes-base.set", "position": [2600, 640], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "ae3158bf-3d91-4a61-a58c-c151362e52d7", "name": "spfvalue", "type": "string", "value": "not found"}]}}, "typeVersion": 3.4}, {"id": "2d7c752a-32cf-4c2c-9e59-e703b0b22ee9", "name": "Gmail - Format Output", "type": "n8n-nodes-base.set", "position": [3520, 100], "parameters": {"options": {}, "assignments": {"assignments": []}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "08b8b071-a94e-43d6-b7a0-85c101630a5c", "name": "Gmail - DKIM Signature Found", "type": "n8n-nodes-base.set", "position": [2600, 820], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "ae3158bf-3d91-4a61-a58c-c151362e52d7", "name": "dkimvalue", "type": "string", "value": "=found"}]}}, "typeVersion": 3.4}, {"id": "e90d4a39-7bd7-4475-a5fa-1a31077004d9", "name": "Gmail - DKIM-Signature Header?", "type": "n8n-nodes-base.if", "position": [1820, 900], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a38ebc9b-f896-4432-81fb-4f3db98f3409", "operator": {"type": "array", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ Object.entries($('Gmail - Set Headers').item.json.headers)\n    .filter(([key, value]) => key.toLowerCase() === 'dkim-signature')\n    .map(([key, value]) => ({ key, value })) }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "c8136c1a-7fcd-4301-a36d-d27fb535c868", "name": "Gmail - No DKIM Signature Found", "type": "n8n-nodes-base.set", "position": [2600, 1020], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "ae3158bf-3d91-4a61-a58c-c151362e52d7", "name": "dkimvalue", "type": "string", "value": "not found"}]}}, "typeVersion": 3.4}, {"id": "84f3ddb5-9a52-45ed-bb98-c88145b69d9a", "name": "Gmail - Set DMARC Value", "type": "n8n-nodes-base.set", "position": [2600, 1240], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "179c48eb-97e5-48ab-82b8-ef4269f11366", "name": "spfvalue", "type": "string", "value": "={{ $json.value.toLowerCase().includes('pass') ? \"pass\" : $json.value.toLowerCase().includes('fail') ? \"fail\" : \"unknown\"}}"}]}}, "typeVersion": 3.4}, {"id": "4ac86848-09db-47a0-afed-20aa92e86ff8", "name": "Gmail - Extract DMARC Header", "type": "n8n-nodes-base.code", "position": [2260, 1240], "parameters": {"jsCode": "// Extract the headers object from the JSON\nconst headers = $('Gmail - Set Headers').item.json.headers;\n\n// Find all keys that start with \"received\" (case-insensitive)\nconst receivedHeaders = Object.entries(headers)\n    .filter(([key, value]) => key.toLowerCase() === 'dmarc')\n    .map(([key, value]) => ({ key, value }));\n\n// Return each header as an object\nreturn receivedHeaders.map(header => ({ json: header }));\n"}, "executeOnce": false, "typeVersion": 2}, {"id": "9e80bfaf-c3f4-4ba9-acfd-c467ecf4563a", "name": "Gmail - DMARC Header?", "type": "n8n-nodes-base.if", "position": [1820, 1340], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a38ebc9b-f896-4432-81fb-4f3db98f3409", "operator": {"type": "array", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ Object.entries($('Gmail - Set Headers').item.json.headers)\n    .filter(([key, value]) => key.toLowerCase() === 'dmarc')\n    .map(([key, value]) => ({ key, value })) }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "448095c0-5d26-4ab6-aae4-3a3ff568de62", "name": "Gmail - No DMARC Header", "type": "n8n-nodes-base.set", "position": [2600, 1440], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "ae3158bf-3d91-4a61-a58c-c151362e52d7", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "string", "value": "=not found"}]}}, "typeVersion": 3.4}, {"id": "a972ebae-67e6-4216-ae24-9db64906c523", "name": "Set Gmail Headers Here", "type": "n8n-nodes-base.set", "position": [-320, -140], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "851a621a-509a-4a10-818c-a885a053cbf6", "name": "headers", "type": "object", "value": "={{ $json.headers }}"}]}}, "typeVersion": 3.4}, {"id": "46b1e8fe-c564-49a7-b38b-22b267fb6fc5", "name": "Format Individual Auth Outputs1", "type": "n8n-nodes-base.set", "position": [3280, 100], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "1f466a9d-e8a1-4095-918c-89fd8e3dae57", "name": "spf", "type": "string", "value": "={{ $json.data[0].spfvalue }}"}, {"id": "797b0e35-9a2e-4261-8741-a8d636e0d1ae", "name": "dkim", "type": "string", "value": "={{ $json.data[1].dkimvalue }}"}, {"id": "8b6f9dda-081d-45b6-98a9-04a96642800b", "name": "dmarc", "type": "string", "value": "={{ $json.data[2].dmarcvalue }}"}, {"id": "6d24a794-0d06-4f12-8bfb-cc3c71720a1b", "name": "initialIP", "type": "string", "value": "={{ $('Gmail - Extract Original From IP').item.json.extractedfromip || 'Originating IP Not Found'}}"}, {"id": "e9ec6f54-0ef7-451b-bbeb-8bb9291e4bcd", "name": "organization", "type": "string", "value": "={{ $('Gmail - Query IP API').item.json.org || \"No Organization Found\" }}"}, {"id": "719b8414-72e1-4916-855b-00abdfc8e776", "name": "country", "type": "string", "value": "={{ $('Gmail - Query IP API').item.json.country || \"No Country Found\" }}"}, {"id": "ab0dc08c-ba54-4e2c-b4df-9f23d36cb350", "name": "city", "type": "string", "value": "={{ $('Gmail - Query IP API').item.json.city || \"No City Found\" }}"}, {"id": "f8214eea-dfb6-4fe1-8e45-e0b8d3d44ee3", "name": "recentSpamActivity", "type": "string", "value": "={{ $('Gmail - Query IP Quality Score API').item.json.fraud_score>=85 ? \"Identified spam in the last 48 hours\" : $('Gmail - Query IP Quality Score API').item.json.fraud_score>=75 ? \"Identified spam in the last month\" : \"Not associated with recent spam\" }}"}, {"id": "fe3488b2-ad00-45ad-b947-ca2dc4242363", "name": "ipSenderReputation", "type": "string", "value": "={{ $('Gmail - Query IP Quality Score API').item.json.fraud_score>=85 ? \"Bad\" : $('Gmail - Query IP Quality Score API').item.json.fraud_score>=75 ? \"Poor\" : $('Gmail - Query IP Quality Score API').item.json.fraud_score>=50 ? \"Suspicious\" : $('Gmail - Query IP Quality Score API').item.json.fraud_score>=11 ? \"OK\" : $('Gmail - Query IP Quality Score API').item.json.fraud_score<11 ? \"Good\" : \"Unknown\"}}"}]}}, "typeVersion": 3.4}, {"id": "8532b9d6-a4e2-4185-a624-26559a6449f4", "name": "Format Combined Auth Output1", "type": "n8n-nodes-base.set", "position": [3100, -80], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "1f466a9d-e8a1-4095-918c-89fd8e3dae57", "name": "spf", "type": "string", "value": "={{ $json.spfvalue }}"}, {"id": "797b0e35-9a2e-4261-8741-a8d636e0d1ae", "name": "dkim", "type": "string", "value": "={{ $json.dkimvalue }}"}, {"id": "8b6f9dda-081d-45b6-98a9-04a96642800b", "name": "dmarc", "type": "string", "value": "={{ $json.dmarcvalue }}"}, {"id": "6d24a794-0d06-4f12-8bfb-cc3c71720a1b", "name": "initialIP", "type": "string", "value": "={{ $('Gmail - Extract Original From IP').item.json.extractedfromip || 'Originating IP Not Found'}}"}, {"id": "e9ec6f54-0ef7-451b-bbeb-8bb9291e4bcd", "name": "organization", "type": "string", "value": "={{ $('Gmail - Query IP API').item.json.org || \"No Organization Found\" }}"}, {"id": "ba720521-9c2d-4906-8567-714e411f1663", "name": "country", "type": "string", "value": "={{ $('Gmail - Query IP API').item.json.country || \"No Country Found\" }}"}, {"id": "2d53a2b1-2600-4fe3-8273-8a54db4e5b87", "name": "city", "type": "string", "value": "={{ $('Gmail - Query IP API').item.json.city || \"No City Found\" }}"}, {"id": "84158095-89e2-48f6-9f78-2f9e0f71fcc9", "name": "recentSpamActivity", "type": "string", "value": "={{ $('Gmail - Query IP Quality Score API').item.json.fraud_score>=85 ? \"Identified spam in the last 48 hours\" : $('Gmail - Query IP Quality Score API').item.json.fraud_score>=75 ? \"Identified spam in the last month\" : \"Not associated with recent spam\" }}"}, {"id": "9907705d-5f70-4cc7-bac0-0411f4b4ea37", "name": "ipSenderReputation", "type": "string", "value": "={{ $('Gmail - Query IP Quality Score API').item.json.fraud_score>=85 ? \"Bad\" : $('Gmail - Query IP Quality Score API').item.json.fraud_score>=75 ? \"Poor\" : $('Gmail - Query IP Quality Score API').item.json.fraud_score>=50 ? \"Suspicious\" : $('Gmail - Query IP Quality Score API').item.json.fraud_score>=11 ? \"OK\" : $('Gmail - Query IP Quality Score API').item.json.fraud_score<11 ? \"Good\" : \"Unknown\"}}"}]}}, "typeVersion": 3.4}, {"id": "3e1323d4-a963-4a13-bbca-b6bb8ee5a9ce", "name": "Gmail - Webhook", "type": "n8n-nodes-base.webhook", "position": [-673, 541], "webhookId": "fb37cff7-b543-45f0-922d-4e0edcae5e43", "parameters": {"path": "fb37cff7-b543-45f0-922d-4e0edcae5e43", "options": {}, "httpMethod": "POST", "responseMode": "responseNode"}, "typeVersion": 2}, {"id": "8938765f-c569-4935-91d7-15c555e9fb99", "name": "Gmail - Remove Extra Received Headers", "type": "n8n-nodes-base.limit", "position": [420, -80], "parameters": {"keep": "lastItems"}, "typeVersion": 1}, {"id": "755b1716-5f63-4f5c-bc76-4bcaa7ffbb03", "name": "Gmail - <PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [2880, 100], "parameters": {"numberInputs": 3}, "typeVersion": 3}, {"id": "19bbc6ce-feb3-49f8-b8d6-a99538810555", "name": "Gmail - Aggregate", "type": "n8n-nodes-base.aggregate", "position": [3100, 100], "parameters": {"options": {}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}, {"id": "33625d80-1bb7-474c-935b-0878d9185a41", "name": "Gmail - <PERSON> Headers", "type": "n8n-nodes-base.set", "position": [0, -80], "parameters": {"options": {}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "db297206-5433-413b-8e78-dcf5f10dc41e", "name": "Gmail - Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [3800, 100], "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "cd401445-2b7a-42c9-9bf9-b17cc12a817b", "name": "Aggregate Received-SPF Headers1", "type": "n8n-nodes-base.aggregate", "position": [2380, 460], "parameters": {"options": {}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}, {"id": "6863e527-bc58-438c-8c3c-87f43994ac61", "name": "Set Gmail Webhook Headers Here", "type": "n8n-nodes-base.set", "position": [-233, 541], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "851a621a-509a-4a10-818c-a885a053cbf6", "name": "headers", "type": "object", "value": "={{ $json.body.headers }}"}]}}, "typeVersion": 3.4}, {"id": "8af99afc-538f-4e5b-9d69-e3c41ee3d300", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-722.5965764931168, -597.9994506078199], "parameters": {"color": 7, "width": 630.8094744668451, "height": 645.5004204663932, "content": "![](https://uploads.n8n.io/templates/gmaillogo.png)  \n## **Testing Email Header Analysis Workflow**\n\nThis section of the workflow is designed for testing purposes to ensure that the setup functions correctly with your Gmail email client before deploying it as an API for third-party platforms. The process begins with the `Gmail Trigger` node, which monitors your Gmail inbox and triggers the workflow whenever a new email arrives.\n\nOnce an email is detected, the `Set Gmail Headers Here` node extracts the email headers from the detected email and organizes them into a standardized format as an object called `headers`. This prepares the email data for further processing in subsequent sections of the workflow. By validating these steps, you can confirm the workflow is functioning correctly before integrating it into broader use cases."}, "typeVersion": 1}, {"id": "eeca8b68-8536-4131-a370-02822a8a13df", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-82, -524.2941123664101], "parameters": {"color": 7, "width": 869.3564073187465, "height": 611.2507800793627, "content": "![](https://uploads.n8n.io/templates/n8n.png)  \n## **Extract and Process Email Headers**\n\nThis section processes the headers from incoming email data to extract critical information, particularly focusing on the originating IP address. The workflow begins with the `Gmail - Set Headers` node, which prepares the headers for analysis.\n\nThe `Gmail - Extract Received Headers` node filters through the headers and isolates those labeled as \"Received.\" These headers document the servers through which the email has passed, providing a traceable path of its journey. Next, the `Gmail - Remove Extra Received Headers` node narrows the focus to the most recent \"Received\" header, typically containing the originating IP address of the email sender.\n\nUsing the `Gmail - Extract Original From IP` node, the workflow applies a regular expression to extract the IP address from the retained header, removing internal or private IP addresses. This ensures that only the relevant external IP address is identified."}, "typeVersion": 1}, {"id": "bdabc308-6f17-413d-beb6-fde80d54140a", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [800, -599.6286292747894], "parameters": {"color": 7, "width": 922.1859426288208, "height": 824.9161858198846, "content": "![](https://uploads.n8n.io/templates/ipqualityscoretemplate.png)  \n## **Analyze IP Address and Check Authentication Results**\n\nThis section analyzes the originating IP address and verifies the presence of essential email authentication headers. The `Gmail - Original IP Found?` node determines whether the extracted IP address is valid and non-empty. If valid, the workflow proceeds; otherwise, it triggers the `Skip IP Check` node to move on to the next steps.\n\nThe `Gmail - Query IP Quality Score API` node evaluates the IP’s reputation, identifying associations with spam, fraud, or other malicious activities. The `Gmail - Query IP API` node enriches the analysis by providing additional details such as geographic location and organizational affiliation of the IP. \n\nFinally, the `Gmail - Authentication-Results Header?` node checks for the presence of the \"Authentication-Results\" header, which indicates SPF, DKIM, and DMARC checks performed by the receiving email server. If present, the header is further analyzed in subsequent sections."}, "typeVersion": 1}, {"id": "164c6c86-582f-44a8-b866-8865c6d4c2e5", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1735, -551.4521798091497], "parameters": {"color": 7, "width": 1016.1357697283069, "height": 541.7962991053803, "content": "![](https://uploads.n8n.io/templates/n8n.png)  \n## **Extract and Evaluate Authentication Results**\n\nThe workflow continues with the `Gmail - Extract Authentication-Results Header` node, which isolates and parses the authentication results header. The `Gmail - Determine Auth Values` node processes the extracted data, categorizing the SPF, DKIM, and DMARC results as `pass`, `fail`, `neutral`, `error`, or `unknown`.\n\nThe `Gmail - Format Combined Auth Output` node consolidates the authentication results with metadata from previous nodes, including the originating IP, geographic details, organization, IP reputation, and spam activity. This structured output provides a comprehensive overview of the email's legitimacy, ready for external integration or reporting."}, "typeVersion": 1}, {"id": "8019b817-c8f7-425a-b372-5ba3109f5b64", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [2773, -500.1753788350808], "parameters": {"color": 7, "width": 1285.8545784346588, "height": 759.649504764657, "content": "![](https://uploads.n8n.io/templates/n8n.png)\n## **Combine Results and Respond to Webhook**\n\nThe final section consolidates results from previous nodes into a cohesive output for the webhook response. The `Gmail - Merge` node combines data streams from SPF, DKIM, and DMARC evaluations. The `Gmail - Aggregate` node structures the data into a unified format.\n\nThe `Gmail - Format Individual Auth Outputs1` and `Gmail - Format Combined Auth Output1` nodes prepare the data as a structured JSON object, including all authentication results and metadata such as IP reputation and geographic information. The `Gmail - Format Output` node finalizes the response structure, and the `Gmail - Respond to Webhook` node sends the response to the requesting system.\n\nThis ensures seamless integration and delivers actionable insights to external platforms, completing the email analysis pipeline."}, "typeVersion": 1}, {"id": "4d7e8d8f-bd8e-4152-b375-8561b6f2d3fb", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [1733, -1], "parameters": {"color": 7, "width": 1016.1357697283069, "height": 1666.528211982754, "content": "![](https://uploads.n8n.io/templates/n8n.png)\n## **Evaluate SPF, DKIM, and DMARC Compliance**\n\nThis section performs detailed analysis of SPF, DKIM, and DMARC headers. The `Gmail - Received-SPF Header?` node identifies the presence of the \"Received-SPF\" header, while the `Gmail - Extract Received-SPF Header` and `Aggregate Received-SPF Headers1` nodes extract and analyze SPF validation results. The `Gmail - Set SPF Value` node records the SPF status, and the `Gmail - No SPF Found` node handles cases where the header is absent.\n\nSimilarly, the `Gmail - DKIM-Signature Header?` node checks for a DKIM signature. If found, the `Gmail - DKIM Signature Found` node records its presence; otherwise, the `Gmail - No DKIM Signature Found` node handles its absence. \n\nThe `Gmail - DMARC Header?` node evaluates the DMARC policy header, with results extracted by `Gmail - Extract DMARC Header` or noted as absent by the `Gmail - No DMARC Header` node."}, "typeVersion": 1}, {"id": "544764a9-35f1-4a42-a9c7-b97f6c09314e", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-721.3492584351117, 60], "parameters": {"color": 7, "width": 625.8275790033185, "height": 660.0846008994936, "content": "![](https://uploads.n8n.io/templates/n8n.png)\n## **Webhook Integration for Production**\n\nThis section transitions the workflow into production, enabling it to function as an API for analyzing email headers received from third-party platforms. To utilize this webhook functionality, it is essential to **activate the workflow**, as the webhook will only respond when the workflow is live.\n\nThe `Gmail - Webhook` node listens for incoming HTTP POST requests at the specified path. When the webhook is triggered, it receives and processes the payload containing email data, including headers sent by the third-party platform. The `Set Gmail Webhook Headers Here` node extracts the `headers` array from the payload's body, ensuring the incoming data is formatted correctly and ready for further processing in subsequent steps.\n\nBy activating the workflow and integrating it with external systems, users can automate the analysis of email headers seamlessly in a production environment."}, "typeVersion": 1}, {"id": "fe9bb5cc-8bbd-4929-9bbe-8a78adce5434", "name": "Skip IP Check", "type": "n8n-nodes-base.noOp", "position": [1160, 80], "parameters": {}, "typeVersion": 1}], "pinData": {}, "connections": {"Gmail - Merge": {"main": [[{"node": "Gmail - Aggregate", "type": "main", "index": 0}]]}, "Gmail Trigger": {"main": [[{"node": "Set Gmail Headers Here", "type": "main", "index": 0}]]}, "Skip IP Check": {"main": [[{"node": "Gmail - Authentication-Results Header?", "type": "main", "index": 0}]]}, "Gmail - Webhook": {"main": [[{"node": "Set Gmail Webhook Headers Here", "type": "main", "index": 0}]]}, "Gmail - Aggregate": {"main": [[{"node": "Format Individual Auth Outputs1", "type": "main", "index": 0}]]}, "Gmail - Set Headers": {"main": [[{"node": "Gmail - Extract Received Headers", "type": "main", "index": 0}]]}, "Gmail - No SPF Found": {"main": [[{"node": "Gmail - <PERSON><PERSON>", "type": "main", "index": 0}]]}, "Gmail - Query IP API": {"main": [[{"node": "Gmail - Authentication-Results Header?", "type": "main", "index": 0}]]}, "Gmail - DMARC Header?": {"main": [[{"node": "Gmail - Extract DMARC Header", "type": "main", "index": 0}], [{"node": "Gmail - No DMARC Header", "type": "main", "index": 0}]]}, "Gmail - Format Output": {"main": [[{"node": "Gmail - Respond to Webhook", "type": "main", "index": 0}]]}, "Gmail - Set SPF Value": {"main": [[{"node": "Gmail - <PERSON><PERSON>", "type": "main", "index": 0}]]}, "Set Gmail Headers Here": {"main": [[{"node": "Gmail - <PERSON> Headers", "type": "main", "index": 0}]]}, "Gmail - No DMARC Header": {"main": [[{"node": "Gmail - <PERSON><PERSON>", "type": "main", "index": 2}]]}, "Gmail - Set DMARC Value": {"main": [[{"node": "Gmail - <PERSON><PERSON>", "type": "main", "index": 2}]]}, "Gmail - Original IP Found?": {"main": [[{"node": "Gmail - Query IP Quality Score API", "type": "main", "index": 0}], [{"node": "Skip IP Check", "type": "main", "index": 0}]]}, "Format Combined Auth Output1": {"main": [[{"node": "Gmail - Format Output", "type": "main", "index": 0}]]}, "Gmail - DKIM Signature Found": {"main": [[{"node": "Gmail - <PERSON><PERSON>", "type": "main", "index": 1}]]}, "Gmail - Extract DMARC Header": {"main": [[{"node": "Gmail - Set DMARC Value", "type": "main", "index": 0}]]}, "Gmail - Received-SPF Header?": {"main": [[{"node": "Gmail - Extract Received-<PERSON><PERSON>", "type": "main", "index": 0}], [{"node": "Gmail - No SPF Found", "type": "main", "index": 0}]]}, "Gmail - Determine Auth Values": {"main": [[{"node": "Format Combined Auth Output1", "type": "main", "index": 0}]]}, "Gmail - DKIM-Signature Header?": {"main": [[{"node": "Gmail - DKIM Signature Found", "type": "main", "index": 0}], [{"node": "Gmail - No DKIM Signature Found", "type": "main", "index": 0}]]}, "Set Gmail Webhook Headers Here": {"main": [[{"node": "Gmail - <PERSON> Headers", "type": "main", "index": 0}]]}, "Aggregate Received-SPF Headers1": {"main": [[{"node": "Gmail - Set SPF Value", "type": "main", "index": 0}]]}, "Format Individual Auth Outputs1": {"main": [[{"node": "Gmail - Format Output", "type": "main", "index": 0}]]}, "Gmail - No DKIM Signature Found": {"main": [[{"node": "Gmail - <PERSON><PERSON>", "type": "main", "index": 1}]]}, "Gmail - Extract Original From IP": {"main": [[{"node": "Gmail - Original IP Found?", "type": "main", "index": 0}]]}, "Gmail - Extract Received Headers": {"main": [[{"node": "Gmail - Remove Extra Received Headers", "type": "main", "index": 0}]]}, "Gmail - Query IP Quality Score API": {"main": [[{"node": "Gmail - Query IP API", "type": "main", "index": 0}]]}, "Gmail - Extract Received-SPF Header": {"main": [[{"node": "Aggregate Received-SPF Headers1", "type": "main", "index": 0}]]}, "Gmail - Remove Extra Received Headers": {"main": [[{"node": "Gmail - Extract Original From IP", "type": "main", "index": 0}]]}, "Gmail - Authentication-Results Header?": {"main": [[{"node": "Gmail - Extract Authentication-Results Header", "type": "main", "index": 0}], [{"node": "Gmail - Received-<PERSON><PERSON>?", "type": "main", "index": 0}, {"node": "Gmail - DKIM-Signature Header?", "type": "main", "index": 0}, {"node": "Gmail - DMARC Header?", "type": "main", "index": 0}]]}, "Gmail - Extract Authentication-Results Header": {"main": [[{"node": "Gmail - Determine <PERSON>th <PERSON>s", "type": "main", "index": 0}]]}}}