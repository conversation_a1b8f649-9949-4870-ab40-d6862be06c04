{"nodes": [{"id": "b9256d00-9dff-432a-a678-e71a4074b26c", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [-20, -160], "webhookId": "06d29616-8fa9-42cf-8b5f-abe856083c75", "parameters": {"path": "06d29616-8fa9-42cf-8b5f-abe856083c75", "options": {}, "httpMethod": "POST"}, "typeVersion": 2}, {"id": "2726dd28-5366-4c0e-ad16-bae6dc2cbc0b", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-880, -200], "parameters": {"color": 4, "width": 320, "height": 440, "content": "## What does it do?\nObjective:\n\nStreamline the onboarding process for new customers, ensuring they receive all necessary resources and support.\nTrigger: Set a webhook trigger or a CRM trigger (like HubSpot or Salesforce) for when a new customer is added.\n\nSend Welcome Email: Use the Gmail or SMTP node to send a personalized welcome email to the customer.\n\nSchedule a Welcome Call: Use the Calendar node (Google Calendar) to automatically create a calendar event for a welcome call.\n\nAssign a CSM: Use the CRM node (like HubSpot) to assign the new customer to a dedicated CSM."}, "typeVersion": 1}, {"id": "680bdd4e-f382-4d20-8197-a7d65454ce36", "name": "Try Again", "type": "n8n-nodes-base.set", "position": [1000, 500], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "7ab380a2-a8d3-421c-ab4e-748ea8fb7904", "name": "response", "type": "string", "value": "Unable to perform task. Please try again."}]}}, "typeVersion": 3.4}, {"id": "1ab48997-7533-4572-86d5-980af557d09d", "name": "Success", "type": "n8n-nodes-base.set", "position": [1000, 300], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "39c2f302-03be-4464-a17a-d7cc481d6d44", "name": "=response", "type": "string", "value": "={{$json.output}}"}]}}, "typeVersion": 3.4}, {"id": "4cb493a1-1eff-42ca-9c51-8f070fe3e9ba", "name": "Calendar Agent", "type": "@n8n/n8n-nodes-langchain.agent", "onError": "continueErrorOutput", "position": [412, 400], "parameters": {"text": "={{ $json.query }}", "options": {"systemMessage": "=# Overview\nYou are a calendar assistant. Your responsibilities include creating, getting, and deleting events in the user's calendar.\nIf no date is proposed, find the next available slot using \"Get Events\" and create an event using \"Create Event with Attendee\"\n\n**Calendar Management Tools**  \n   - Use \"Create Event with Attendee\" when an event includes a participant.     \n   - Use \"Get Events\" to fetch calendar schedules when requested.\n   - Use \"Delete Event\" to delete an event. You must use \"Get Events\" first to get the ID of the event to delete.\n   - Use \"Update Event\" to update an event. You must use \"Get Events\" first to get the ID of the event to update.\n\n## Final Notes\nHere is the current date/time: {{ $now }}\nIf a duration for an event isn't specified, assume it will be one hour."}, "promptType": "define"}, "typeVersion": 1.6}, {"id": "8c088b37-1005-4bc4-bdf5-0558ccb0c873", "name": "Create Event with <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.googleCalendarTool", "position": [320, 620], "parameters": {"end": "={{ $fromAI(\"eventEnd\") }}", "start": "={{ $fromAI(\"eventStart\") }}", "calendar": {"__rl": true, "mode": "id", "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Calendar', ``, 'string') }}", "__regex": "(^[a-zA-Z0-9.!#$%&’*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\\.[a-zA-Z0-9-]+)*)"}, "additionalFields": {"summary": "={{ $fromAI(\"eventTitle\") }}", "attendees": ["={{ $fromAI(\"eventAttendeeEmail\") }}"]}}, "credentials": {"googleCalendarOAuth2Api": {"id": "90bjjmYqtg3hnvFM", "name": "Google Calendar account"}}, "typeVersion": 1.3}, {"id": "91a0e49a-888f-4511-94e7-e0166ce7dd58", "name": "Create Event", "type": "n8n-nodes-base.googleCalendarTool", "position": [440, 620], "parameters": {"end": "={{ $fromAI(\"eventEnd\") }}", "start": "={{ $fromAI(\"eventStart\") }}", "calendar": {"__rl": true, "mode": "id", "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Calendar', ``, 'string') }}", "__regex": "(^[a-zA-Z0-9.!#$%&’*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\\.[a-zA-Z0-9-]+)*)"}, "additionalFields": {"summary": "={{ $fromAI(\"eventTitle\") }}", "attendees": []}}, "credentials": {"googleCalendarOAuth2Api": {"id": "90bjjmYqtg3hnvFM", "name": "Google Calendar account"}}, "typeVersion": 1.3}, {"id": "8cfb90e5-6108-4003-b048-271650d4bc6c", "name": "Get Events", "type": "n8n-nodes-base.googleCalendarTool", "position": [560, 620], "parameters": {"options": {}, "timeMax": "={{ $fromAI(\"dayBefore\",\"today plus 7 days\") }}", "timeMin": "={{ $fromAI(\"dayAfter\",\"today\") }}", "calendar": {"__rl": true, "mode": "id", "value": "={{ $fromAI('Calendar', `sender's email`, 'string') }}"}, "operation": "getAll"}, "credentials": {"googleCalendarOAuth2Api": {"id": "90bjjmYqtg3hnvFM", "name": "Google Calendar account"}}, "typeVersion": 1.3}, {"id": "c5cc3550-7d9a-43c9-8434-e1ab78f7f596", "name": "Delete Event", "type": "n8n-nodes-base.googleCalendarTool", "position": [680, 620], "parameters": {"eventId": "={{ $fromAI(\"eventID\") }}", "options": {}, "calendar": {"__rl": true, "mode": "id", "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Calendar', ``, 'string') }}", "__regex": "(^[a-zA-Z0-9.!#$%&’*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\\.[a-zA-Z0-9-]+)*)"}, "operation": "delete"}, "credentials": {"googleCalendarOAuth2Api": {"id": "90bjjmYqtg3hnvFM", "name": "Google Calendar account"}}, "typeVersion": 1.3}, {"id": "7ce45da8-dc24-4634-9f48-3864165885cd", "name": "Update Event", "type": "n8n-nodes-base.googleCalendarTool", "position": [800, 620], "parameters": {"eventId": "={{ $fromAI(\"eventID\") }}", "calendar": {"__rl": true, "mode": "id", "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Calendar', ``, 'string') }}", "__regex": "(^[a-zA-Z0-9.!#$%&’*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\\.[a-zA-Z0-9-]+)*)"}, "operation": "update", "updateFields": {"end": "={{ $fromAI(\"endTime\") }}", "start": "={{ $fromAI(\"startTime\") }}"}}, "credentials": {"googleCalendarOAuth2Api": {"id": "90bjjmYqtg3hnvFM", "name": "Google Calendar account"}}, "typeVersion": 1.3}, {"id": "b46f24a4-719c-414e-94d9-ecfb1e7dfe39", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [200, 400], "parameters": {"inputSource": "passthrough"}, "typeVersion": 1.1}, {"id": "aedbe138-ed51-4300-881b-6b58928f5bb4", "name": "OpenAI Chat Model2", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1520, 160], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "1IOLtYX7aTspCAN8", "name": "OpenAI Pollup"}}, "typeVersion": 1.2}, {"id": "a540ae6b-e1ee-4d91-988e-e60bae743377", "name": "calendarAgent", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [1640, 160], "parameters": {"workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id}}", "cachedResultName": "={{ $workflow.id}}"}, "description": "Call this tool for any calendar action.", "workflowInputs": {"value": {}, "schema": [], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2.2}, {"id": "64979b9f-a29a-4c53-b87a-cec84e7ba1fe", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [1760, 160], "parameters": {"jsonSchemaExample": "{\n\t\"subject\": \"\",\n\t\"body\": \"\"\n}"}, "typeVersion": 1.2}, {"id": "359d7296-a8e9-494c-b519-cca62c0805df", "name": "Get list of owners", "type": "n8n-nodes-base.httpRequest", "position": [420, -60], "parameters": {"url": "https://api.hubapi.com/crm/v3/owners", "options": {}, "authentication": "predefinedCredentialType", "nodeCredentialType": "hubspotOAuth2Api"}, "credentials": {"hubspotOAuth2Api": {"id": "qubiIFrowxvUdpu6", "name": "HubSpot account for node"}}, "typeVersion": 4.2}, {"id": "e8aab719-a5d9-4168-9c68-eea32c7d3ef4", "name": "Split Out owners", "type": "n8n-nodes-base.splitOut", "position": [640, -60], "parameters": {"options": {}, "fieldToSplitOut": "results"}, "typeVersion": 1}, {"id": "5e44ea67-e2f9-4cea-a030-c452b8bb482f", "name": "Get current owner", "type": "n8n-nodes-base.filter", "position": [860, -60], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "7c6aec6e-66a9-4739-8a59-28f2ab1c4a26", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.email }}", "rightValue": "={{ $('Enter your company data here').item.json.sender_email }}"}]}}, "typeVersion": 2.2}, {"id": "c03bd58c-7a42-4966-96e8-45928f745475", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-540, -200], "parameters": {"color": 5, "width": 680, "height": 1340, "content": "## How to Set a Webhook in n8n and HubSpot\n\n## 1. Set Up a Webhook in n8n\n\n### Step 1: Create a New Workflow\n- Go to your **n8n dashboard**.\n- Click on **\"New Workflow.\"**\n\n### Step 2: Add the Webhook Node\n- Click on the **“+”** icon to add a new node.\n- Search for **“Webhook”** and select it.\n- Set the **Webhook Method** (usually POST).\n- Define the **Webhook URL path**, for example, `/hubspot-webhook`.\n- Set the **\"Response Mode\"** (e.g., \"On Received\").\n- Save the workflow.\n\n### Step 3: Set Webhook URL\n- Copy the **Webhook URL** generated by n8n. It should look something like:https://your-n8n-domain/webhook/hubspot-webhook\n\n- Ensure the workflow is **active**.\n\n---\n\n## 2. Set Up a Webhook in HubSpot\n\n### Step 1: Log in to HubSpot\n- Go to **HubSpot Developer Account** (required for webhook setup).\n- Navigate to **\"Settings\" > \"Integrations\" > \"Webhooks.\"**\n\n### Step 2: Create a New Webhook Subscription\n- Click **“Create Webhook”** or **“Add Webhook”** if this is your first one.\n- Select the **events** you want to track (e.g., contact creation, form submission).\n- Set the **Webhook URL** as the n8n Webhook URL you copied earlier.\n- Choose **“POST”** as the request method.\n- Set the **Authentication** if needed (you can set a secret or use OAuth).\n\n### Step 3: Test the Webhook\n- Use the **“Test Webhook”** feature in HubSpot to send a test request.\n- Switch to n8n and ensure the webhook is triggering properly.\n\n---\n\n## 3. Process the Data in n8n\n- After the webhook is triggered in n8n, you will see the data sent by HubSpot.\n- You can now add additional nodes to **process the data** (e.g., save to database, send email, perform actions in another app).\n\n---\n\n## 4. Make the Workflow Active\n- Once you are done configuring, make sure the workflow is **set to “Active.”**\n- This will allow it to receive live data from HubSpot."}, "typeVersion": 1}, {"id": "************************************", "name": "HubSpot Trigger", "type": "n8n-nodes-base.hubspotTrigger", "position": [-20, 40], "webhookId": "632f3fc8-b921-4697-ba12-037d5c7f8971", "parameters": {"eventsUi": {"eventValues": [{}]}, "additionalFields": {}}, "credentials": {"hubspotDeveloperApi": {"id": "DVrqcbIPANwtlVSg", "name": "HubSpot Developer account for trigger"}}, "typeVersion": 1}, {"id": "9051cc3d-06be-4238-998e-7cb938313d24", "name": "Enter your company data here", "type": "n8n-nodes-base.set", "position": [200, -60], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "11a8b9e9-a7ed-454a-9aef-a9137c0e17ea", "name": "company_name", "type": "string", "value": "Pollup Data Services"}, {"id": "f2dcfe2e-3145-4a30-9731-0a8d02c7aa9a", "name": "sender_name", "type": "string", "value": "<PERSON>"}, {"id": "18b5c0bd-4e75-4b98-92fc-5fca90a8b680", "name": "sender_email", "type": "string", "value": "<EMAIL>"}, {"id": "2c8de3ed-57dc-455b-bfa5-87a0d8d046d2", "name": "company_activity", "type": "string", "value": "Whether it’s automating recurring tasks, analysing data faster, or personalising customer interactions, we build bespoke AI agents to help your workforce work smarter."}]}}, "notesInFlow": true, "typeVersion": 3.4}, {"id": "5260ec73-0733-47d1-af03-66ead128820e", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [160, -200], "parameters": {"color": 4, "width": 400, "height": 320, "content": "## Set your data and your company's \nThe sender_email you set here has to be the same as the one you use in hubspot "}, "typeVersion": 1}, {"id": "************************************", "name": "If a contact is created", "type": "n8n-nodes-base.if", "position": [1080, -60], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "b70f4699-008f-4924-8e69-af4fa69422a5", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Webhook').item.json.body[0].subscriptionType }}", "rightValue": "contact.creation"}]}}, "typeVersion": 2.2}, {"id": "b575fed7-da03-412d-aa00-fcf0edc85ae2", "name": "Get all info about the contact", "type": "n8n-nodes-base.hubspot", "position": [1300, -60], "parameters": {"contactId": {"__rl": true, "mode": "id", "value": "={{ $('Webhook').item.json.body[0].objectId }}"}, "operation": "get", "authentication": "oAuth2", "additionalFields": {}}, "credentials": {"hubspotOAuth2Api": {"id": "qubiIFrowxvUdpu6", "name": "HubSpot account for node"}}, "typeVersion": 2.1}, {"id": "956a02eb-970b-49bd-b1a5-3eebf7acb852", "name": "Write a personalized message", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1552, -60], "parameters": {"text": "=Your task is to write a personalized Welcome email to a recipient.\nWrite also to indicate him that he will receive shortly an invitation for a meeting to resolve his doubts. Use for that the calendarAgent.\nUse the \"Sender's calendar ID\" as the Calendar. And the \"Recipient email\" as an attendee\n\n## Tools\n- calendarAgent: Use this tool to take action in calendar. Send it a query like \"Schedule a meeting with attendee 'Recipient email' on 'Sender's calendar ID' calendar.\"\n\n## Rules\n- Some actions require you to look up contact information first. For the following actions, you must get contact information and send that to the agent who needs it:\n- creating calendar event with attendee, create it as son as there is some free slot\n\nreturn the message as a json like this one:{\"subject\":\"Subject of the message\",\"body\":\"Body of the message\"}\n\n## Use the variables below\nSender's name:  {{ $('Enter your company data here').item.json.sender_name }}\nSender's email: {{ $('Enter your company data here').item.json.sender_email }}\nSender's company name: {{ $('Enter your company data here').item.json.company_name }}\nSender's company activity: {{ $('Enter your company data here').item.json.company_activity }}\nSender's calendar ID: <EMAIL>\nRecipient first name: {{ $json.properties.firstname.value }}\nRecipient last name: {{ $json.properties.lastname }}\nRecipient email: {{ $json.properties.email.value }}", "options": {"systemMessage": "=# Overview\nYou are a professional Customer Success Manager.\n"}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.9}, {"id": "6d392f67-5940-43ed-ac8d-d27f8dab91ed", "name": "Send the message", "type": "n8n-nodes-base.gmail", "position": [2180, -60], "webhookId": "d1d18d77-71ad-4eab-91c6-08b6a9f5d736", "parameters": {"sendTo": "={{ $('Get all info about the contact').item.json.properties.email.value }}", "message": "={{ $json.data }}", "options": {"bccList": "<EMAIL>"}, "subject": "={{ $json.output.subject }}"}, "credentials": {"gmailOAuth2": {"id": "DLjspol9TLgpGaXa", "name": "Gmail account 2"}}, "typeVersion": 2.1}, {"id": "1928e760-4ca1-443c-9de0-211b3c3c88b8", "name": "Set owner to contact", "type": "n8n-nodes-base.hubspot", "position": [2400, -60], "parameters": {"email": "={{ $('Get all info about the contact').item.json.properties.email.value }}", "options": {}, "authentication": "oAuth2", "additionalFields": {"contactOwner": "={{ $('Get current owner').item.json.id }}"}}, "credentials": {"hubspotOAuth2Api": {"id": "qubiIFrowxvUdpu6", "name": "HubSpot account for node"}}, "typeVersion": 2.1}, {"id": "727e52cc-ba62-4f1e-b7b3-c8cd17ef1f42", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [160, 220], "parameters": {"color": 4, "width": 1080, "height": 560, "content": "## Calendar tool\nThis part has been borrowed from the excellent [<PERSON>](https://www.youtube.com/@nateherk) youtube channel"}, "typeVersion": 1}, {"id": "e605ec3f-ecbe-47c7-a46b-d20ded665c55", "name": "Transforms markdown to HTML", "type": "n8n-nodes-base.markdown", "position": [1960, -60], "parameters": {"mode": "markdownToHtml", "options": {}, "markdown": "={{ $json.output.body }}"}, "typeVersion": 1}, {"id": "ac43422a-3642-424c-a95a-902652705dbc", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1460, -220], "parameters": {"color": 4, "width": 440, "height": 540, "content": "## Email writer\n- This agent writes a personalized Email\n- Uses the calendar Agent tool to create an appointmenton an empty slot.\nFeel free to personalize the prompt"}, "typeVersion": 1}, {"id": "c0e5511a-a84f-4603-9c23-3d5266f761c1", "name": "OpenAI Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [200, 620], "parameters": {"model": "gpt-4o", "options": {}}, "credentials": {"openAiApi": {"id": "1IOLtYX7aTspCAN8", "name": "OpenAI Pollup"}}, "typeVersion": 1}, {"id": "c195ad96-7b04-4b01-a3a5-cb0df3c5cb26", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-880, 260], "parameters": {"width": 320, "height": 260, "content": "## Contact me\n- If you need any modification to this workflow\n- if you need some help with this workflow\n- Or if you need any workflow in n8n, Make, or Langchain / Langgraph\n\nWrite to me: [<EMAIL>](mailto:<EMAIL>)\n\n**Take a look at my others workflows [here](https://n8n.io/creators/zeerobug/)**\n\n"}, "typeVersion": 1}], "connections": {"Webhook": {"main": [[{"node": "Enter your company data here", "type": "main", "index": 0}]]}, "Get Events": {"ai_tool": [[{"node": "Calendar Agent", "type": "ai_tool", "index": 0}]]}, "Create Event": {"ai_tool": [[{"node": "Calendar Agent", "type": "ai_tool", "index": 0}]]}, "Delete Event": {"ai_tool": [[{"node": "Calendar Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI Model": {"ai_languageModel": [[{"node": "Calendar Agent", "type": "ai_languageModel", "index": 0}]]}, "Update Event": {"ai_tool": [[{"node": "Calendar Agent", "type": "ai_tool", "index": 0}]]}, "calendarAgent": {"ai_tool": [[{"node": "Write a personalized message", "type": "ai_tool", "index": 0}]]}, "Calendar Agent": {"main": [[{"node": "Success", "type": "main", "index": 0}], [{"node": "Try Again", "type": "main", "index": 0}]]}, "HubSpot Trigger": {"main": [[{"node": "Enter your company data here", "type": "main", "index": 0}]]}, "Send the message": {"main": [[{"node": "Set owner to contact", "type": "main", "index": 0}]]}, "Split Out owners": {"main": [[{"node": "Get current owner", "type": "main", "index": 0}]]}, "Get current owner": {"main": [[{"node": "If a contact is created", "type": "main", "index": 0}]]}, "Get list of owners": {"main": [[{"node": "Split Out owners", "type": "main", "index": 0}]]}, "OpenAI Chat Model2": {"ai_languageModel": [[{"node": "Write a personalized message", "type": "ai_languageModel", "index": 0}]]}, "If a contact is created": {"main": [[{"node": "Get all info about the contact", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Write a personalized message", "type": "ai_outputParser", "index": 0}]]}, "Create Event with Attendee": {"ai_tool": [[{"node": "Calendar Agent", "type": "ai_tool", "index": 0}]]}, "Transforms markdown to HTML": {"main": [[{"node": "Send the message", "type": "main", "index": 0}]]}, "Enter your company data here": {"main": [[{"node": "Get list of owners", "type": "main", "index": 0}]]}, "Write a personalized message": {"main": [[{"node": "Transforms markdown to HTML", "type": "main", "index": 0}]]}, "Get all info about the contact": {"main": [[{"node": "Write a personalized message", "type": "main", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Calendar Agent", "type": "main", "index": 0}]]}}}