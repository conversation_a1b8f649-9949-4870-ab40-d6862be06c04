{"meta": {"instanceId": "be27b2af86ae3a5dc19ef2a1947644c0aec45fd8c88f29daa7dea6f0ce537691", "templateCredsSetupCompleted": true}, "nodes": [{"id": "ca8b122d-1739-4377-ac99-e20dd2341342", "name": "Incoming Message", "type": "n8n-nodes-base.telegramTrigger", "position": [-1020, -320], "webhookId": "75921955-c8ed-4ff6-8de2-e436c6bbe69d", "parameters": {"updates": ["message"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "ayMpCvQ69GjrbPdP", "name": "gatu_pa_bot"}}, "typeVersion": 1.2}, {"id": "68f7568b-e677-454b-a1e8-6c07a05e7570", "name": "MCP Server Trigger", "type": "@n8n/n8n-nodes-langchain.mcpTrigger", "position": [-860, 240], "webhookId": "562ffc95-cf8e-4d4d-8f5b-29b3ff22d5ee", "parameters": {"path": "562ffc95-cf8e-4d4d-8f5b-29b3ff22d5ee"}, "typeVersion": 1}, {"id": "635b8ecc-0f50-477d-8e19-631f868e30f6", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [340, -320], "parameters": {"options": {"systemMessage": "=You are a helpful assistant. Whenever askes to update a task, call the get_tasks tools first to retrieve the appropriate task ids then use that to update the tasks.\n\nToday's date: {{ $now }}\n"}}, "typeVersion": 1.8}, {"id": "ab7740dc-bac2-4044-8317-40d90252d992", "name": "MCP Client", "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "position": [540, -100], "parameters": {"sseEndpoint": "https://ai.gatuservices.info/mcp/562ffc95-cf8e-4d4d-8f5b-29b3ff22d5ee/sse"}, "typeVersion": 1}, {"id": "5298eee0-747a-496a-a3a2-e395f7c1caa1", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [300, -100], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "lcpI0YZU9bebg3uW", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "c5b7e10d-2d7c-403c-bcb5-a10033252f97", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [420, -100], "parameters": {"sessionKey": "={{ $('Incoming Message').item.json.message.from.id }}", "sessionIdType": "customKey", "contextWindowLength": 20}, "typeVersion": 1.3}, {"id": "06d2e8c8-3912-45cd-a074-4eea27c2e5eb", "name": "chatInput", "type": "n8n-nodes-base.set", "position": [80, -220], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "ab70dc2d-35d0-4742-988f-ed7077633467", "name": "chatInput", "type": "string", "value": "={{ $json.message.text }}"}, {"id": "6439fc2c-dc2d-41fc-b8a3-b33ef80d2878", "name": "id", "type": "number", "value": "={{ $json.message.from.id }}"}]}}, "typeVersion": 3.4}, {"id": "a9309816-8c1d-435c-ad49-2e45053718c1", "name": "create_todays_task", "type": "n8n-nodes-base.googleTasksTool", "position": [-1020, 460], "parameters": {"task": "MDg2MzM1OTA5NzI0NzUzNjUwNjc6MDow", "title": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Title', `Title summary of the task to be done`, 'string') }}", "additionalFields": {"notes": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Notes', `Detailed description of the task`, 'string') }}", "dueDate": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Due_Date', `Date the task should be completed`, 'string') }}", "completed": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Completion_Date', `Date the task was completed`, 'string') }}"}}, "credentials": {"googleTasksOAuth2Api": {"id": "8sBGA2BWJuF6SObU", "name": "Connected Account"}}, "typeVersion": 1}, {"id": "ad6cfc1a-7094-434a-98d1-a6f030067091", "name": "chatOutput", "type": "n8n-nodes-base.set", "position": [740, -320], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "df6bd510-e63f-41b1-b5b4-d2c612d5b8d0", "name": "chatOutput", "type": "string", "value": "={{ $json.output }}"}]}}, "typeVersion": 3.4}, {"id": "e342066f-3cf8-4926-94df-798e831226be", "name": "sendMessage", "type": "n8n-nodes-base.telegram", "position": [960, -320], "webhookId": "c5eb133f-338f-4918-8e49-83ac339d841b", "parameters": {"text": "={{ $json.chatOutput }}", "chatId": "={{ $('Incoming Message').item.json.message.chat.id }}", "additionalFields": {"appendAttribution": false, "disable_notification": false}}, "credentials": {"telegramApi": {"id": "ayMpCvQ69GjrbPdP", "name": "gatu_pa_bot"}}, "typeVersion": 1.2}, {"id": "e4a1bc16-549f-46a2-92a8-a06e6023089c", "name": "create_upcoming_task", "type": "n8n-nodes-base.googleTasksTool", "position": [-900, 460], "parameters": {"task": "OFVvNlh6ZmhScHVvNll4dw", "title": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Title', `Title summary of the task to be done`, 'string') }}", "additionalFields": {"notes": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Notes', `Detailed description of the task`, 'string') }}", "dueDate": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Due_Date', `Date the task should be completed`, 'string') }}", "completed": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Completion_Date', `Date the task was completed`, 'string') }}"}}, "credentials": {"googleTasksOAuth2Api": {"id": "8sBGA2BWJuF6SObU", "name": "Connected Account"}}, "typeVersion": 1}, {"id": "df71bb02-016d-4d56-b80d-404a60c0e7cf", "name": "complete_task", "type": "n8n-nodes-base.googleTasksTool", "position": [-780, 460], "parameters": {"task": "RS1rbkNCS2JsdVFnVl80cg", "taskId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Task_ID', `The task id tom be marked as completed. Get it from the get tasks tool`, 'string') }}", "operation": "update", "updateFields": {}}, "credentials": {"googleTasksOAuth2Api": {"id": "8sBGA2BWJuF6SObU", "name": "Connected Account"}}, "typeVersion": 1}, {"id": "a33812bd-986e-4762-87a0-199ff8a7c9aa", "name": "get_todays_tasks", "type": "n8n-nodes-base.googleTasksTool", "position": [-660, 460], "parameters": {"task": "MDg2MzM1OTA5NzI0NzUzNjUwNjc6MDow", "operation": "getAll", "returnAll": true, "additionalFields": {}}, "credentials": {"googleTasksOAuth2Api": {"id": "8sBGA2BWJuF6SObU", "name": "Connected Account"}}, "typeVersion": 1}, {"id": "dcb3a6c9-5d7c-4fe6-8b52-f07cf74cfa0c", "name": "get_upcoming_tasks", "type": "n8n-nodes-base.googleTasksTool", "position": [-540, 460], "parameters": {"task": "OFVvNlh6ZmhScHVvNll4dw", "operation": "getAll", "returnAll": true, "additionalFields": {}}, "credentials": {"googleTasksOAuth2Api": {"id": "8sBGA2BWJuF6SObU", "name": "Connected Account"}}, "typeVersion": 1}, {"id": "ce63c24a-ce2f-4e06-8ae5-7de75540d438", "name": "Switch", "type": "n8n-nodes-base.switch", "position": [-800, -320], "parameters": {"rules": {"values": [{"outputKey": "Voice Note", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "8415cc8d-65a2-448e-a106-1ceb54634dfd", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.message.voice }}", "rightValue": ""}]}, "renameOutput": true}]}, "options": {"fallbackOutput": "extra"}}, "typeVersion": 3.2}, {"id": "a58488c3-38b8-4492-9f13-a900c7697812", "name": "audio_id", "type": "n8n-nodes-base.set", "position": [-580, -420], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "eb7f5d62-e4f3-4b4e-9f1b-6c329feafb3e", "name": "file_id", "type": "string", "value": "={{ $json.message.voice.file_id }}"}, {"id": "803031b8-6b21-47fa-b339-ad674ccbbb1e", "name": "file_unique_id", "type": "string", "value": "={{ $json.message.voice.file_unique_id }}"}]}}, "typeVersion": 3.4}, {"id": "83c2ecae-b601-4669-b820-b5c35d3f936e", "name": "download_audio", "type": "n8n-nodes-base.telegram", "position": [-360, -420], "webhookId": "c2dbc0eb-0f3a-4f11-9525-804bd5bef4b1", "parameters": {"fileId": "={{ $json.file_id }}", "resource": "file"}, "credentials": {"telegramApi": {"id": "ayMpCvQ69GjrbPdP", "name": "gatu_pa_bot"}}, "typeVersion": 1.2}, {"id": "4a496e3a-2e3a-4ce0-9344-192847de1760", "name": "transcribeAudio", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [-140, -420], "parameters": {"options": {}, "resource": "audio", "operation": "transcribe"}, "credentials": {"openAiApi": {"id": "lcpI0YZU9bebg3uW", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "38b6aa37-d279-4b1b-be42-7f7cc1bbe688", "name": "audioInput", "type": "n8n-nodes-base.set", "position": [80, -420], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d57b4fc2-10f7-46cd-a89c-0021a92f41d1", "name": "chatInput", "type": "string", "value": "={{ $json.text }}"}]}}, "typeVersion": 3.4}, {"id": "e524f12f-205f-4fc8-b2f0-b308ec4066b7", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1060, -500], "parameters": {"color": 4, "width": 2180, "height": 540, "content": "## Main Function to Receive and Send Telegram Messages\n"}, "typeVersion": 1}, {"id": "709b252a-b5e8-4c7e-8bcd-a7092d588070", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-1060, 140], "parameters": {"color": 3, "width": 640, "height": 480, "content": "## MCP Server to Carry Out Actions"}, "typeVersion": 1}], "pinData": {"Incoming Message": [{"message": {"chat": {"id": **********, "type": "private", "first_name": "Gatu"}, "date": 1745294191, "from": {"id": **********, "is_bot": false, "first_name": "Gatu", "language_code": "en"}, "voice": {"file_id": "AwACAgQAAxkBAAMYaAcTb6Sm3bpJ_8Cc2q1q4vC7MLYAAg8ZAAJAOjlQQhWQOxUBqfU2BA", "duration": 2, "file_size": 9854, "mime_type": "audio/ogg", "file_unique_id": "AgADDxkAAkA6OVA"}, "message_id": 24}, "update_id": 656804764}]}, "connections": {"Switch": {"main": [[{"node": "audio_id", "type": "main", "index": 0}], [{"node": "chatInput", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "chatOutput", "type": "main", "index": 0}]]}, "audio_id": {"main": [[{"node": "download_audio", "type": "main", "index": 0}]]}, "chatInput": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "MCP Client": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "audioInput": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "chatOutput": {"main": [[{"node": "sendMessage", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "complete_task": {"ai_tool": [[{"node": "MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "download_audio": {"main": [[{"node": "transcribeAudio", "type": "main", "index": 0}]]}, "transcribeAudio": {"main": [[{"node": "audioInput", "type": "main", "index": 0}]]}, "Incoming Message": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "get_todays_tasks": {"ai_tool": [[{"node": "MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "create_todays_task": {"ai_tool": [[{"node": "MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "get_upcoming_tasks": {"ai_tool": [[{"node": "MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "create_upcoming_task": {"ai_tool": [[{"node": "MCP Server Trigger", "type": "ai_tool", "index": 0}]]}}}