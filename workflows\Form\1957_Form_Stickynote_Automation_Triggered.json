{"id": "plzObaqgoEvV4UU0", "meta": {"instanceId": "28a947b92b197fc2524eaba16e57560338657b2b0b5796300b2f1cedc1d0d355", "templateCredsSetupCompleted": true}, "name": "Post on X", "tags": [{"id": "gNiDOCnjqCXR7phD", "name": "Marketing", "createdAt": "2025-04-15T01:08:25.516Z", "updatedAt": "2025-04-15T01:08:25.516Z"}, {"id": "zKNO4Omjzfu6J25M", "name": "Demo", "createdAt": "2025-04-15T18:59:57.364Z", "updatedAt": "2025-04-15T18:59:57.364Z"}], "nodes": [{"id": "203a06a1-2e25-46df-9465-4d5740177249", "name": "Create session", "type": "n8n-nodes-base.airtop", "position": [60, 180], "parameters": {"profileName": "={{ $json.airtop_profile }}", "timeoutMinutes": 5, "saveProfileOnTermination": true}, "credentials": {"airtopApi": {"id": "Yi4YPNnovLVUjFn5", "name": "Airtop API"}}, "typeVersion": 1}, {"id": "18c8ade3-8492-4e75-8310-3be4d7815ab6", "name": "Create window", "type": "n8n-nodes-base.airtop", "position": [280, 180], "parameters": {"url": "https://x.com/", "resource": "window", "additionalFields": {}}, "credentials": {"airtopApi": {"id": "Yi4YPNnovLVUjFn5", "name": "Airtop API"}}, "typeVersion": 1}, {"id": "c46baeac-5d91-4656-a30f-0ca932e8042c", "name": "Type text", "type": "n8n-nodes-base.airtop", "position": [500, 180], "parameters": {"text": "={{ $('Parameters').item.json.post_text }}", "resource": "interaction", "operation": "type", "pressEnterKey": true, "additionalFields": {}, "elementDescription": "\"What's happening?\" text box on top"}, "credentials": {"airtopApi": {"id": "Yi4YPNnovLVUjFn5", "name": "Airtop API"}}, "typeVersion": 1}, {"id": "cfc19d89-8fb2-49c5-97a3-38ad03dffe31", "name": "Click on Post", "type": "n8n-nodes-base.airtop", "position": [720, 180], "parameters": {"resource": "interaction", "additionalFields": {"visualScope": "viewport"}, "elementDescription": "Click on the Post button "}, "credentials": {"airtopApi": {"id": "Yi4YPNnovLVUjFn5", "name": "Airtop API"}}, "typeVersion": 1}, {"id": "1b2a4d37-1fcd-4b6a-8db7-a7056c569ad4", "name": "End session", "type": "n8n-nodes-base.airtop", "position": [940, 180], "parameters": {"operation": "terminate"}, "credentials": {"airtopApi": {"id": "Yi4YPNnovLVUjFn5", "name": "Airtop API"}}, "typeVersion": 1}, {"id": "2fdae018-aaca-4101-acdc-42d799463880", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-380, 280], "parameters": {"workflowInputs": {"values": [{"name": "airtop_profile"}, {"name": "post_text"}]}}, "typeVersion": 1.1}, {"id": "2a2125ff-6acd-4aca-bc69-d148b6cbb678", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [0, 20], "parameters": {"color": 5, "width": 220, "height": 320, "content": "### Heads up!\nTo make sure everything works smoothly, use an [Airtop Profile](https://docs.airtop.ai/guides/how-to/saving-a-profile) signed into x.com for the \"Create session\" node"}, "typeVersion": 1}, {"id": "ca75bf36-55c4-4496-9a77-3870d078bec2", "name": "On form submission", "type": "n8n-nodes-base.formTrigger", "position": [-380, 80], "webhookId": "bf22d894-7313-40b1-aefa-98bc518473bf", "parameters": {"options": {"buttonLabel": "Post on X", "appendAttribution": false, "respondWithOptions": {"values": {"formSubmittedText": "✅ Your post has been published!"}}}, "formTitle": "Post on X", "formFields": {"values": [{"fieldLabel": "Airtop profile name", "placeholder": "e.g. my-x-profile", "requiredField": true}, {"fieldLabel": "Text to post", "placeholder": "e.g. This X post was made with Airtop and n8n", "requiredField": true}]}, "responseMode": "lastNode", "formDescription": "Enter the <a href=\"https://docs.airtop.ai/guides/how-to/saving-a-profile\" target=\"_blank\">Airtop Profile</a> and the content you would like to post on x.com"}, "typeVersion": 2.2}, {"id": "d56e067b-9825-4a81-88a4-c65dac5a919c", "name": "Parameters", "type": "n8n-nodes-base.set", "position": [-160, 180], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "e612bf63-72bd-4b61-82c9-786a90b58b7b", "name": "airtop_profile", "type": "string", "value": "={{ $json[\"Airtop profile name\"] || $json.airtop_profile }}"}, {"id": "567e5e7d-4efd-4d0a-a93c-6c7aed02c305", "name": "post_text", "type": "string", "value": "={{ $json[\"Text to post\"] || $json.post_text }}"}]}}, "typeVersion": 3.4}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "9129144f-d078-48f8-825a-7f8bbda4570b", "connections": {"Type text": {"main": [[{"node": "Click on Post", "type": "main", "index": 0}]]}, "Parameters": {"main": [[{"node": "Create session", "type": "main", "index": 0}]]}, "Click on Post": {"main": [[{"node": "End session", "type": "main", "index": 0}]]}, "Create window": {"main": [[{"node": "Type text", "type": "main", "index": 0}]]}, "Create session": {"main": [[{"node": "Create window", "type": "main", "index": 0}]]}, "On form submission": {"main": [[{"node": "Parameters", "type": "main", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Parameters", "type": "main", "index": 0}]]}}}