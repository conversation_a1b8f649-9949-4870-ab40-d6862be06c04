{"nodes": [{"name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [700, 350], "parameters": {"path": "test", "responseData": "firstEntryBinary", "responseMode": "lastNode"}, "typeVersion": 1}, {"name": "Edit Image", "type": "n8n-nodes-base.editImage", "position": [1100, 350], "parameters": {"text": "=They found the killer it was {{$node[\"Webhook\"].data[\"query\"][\"name\"]}}!", "fontSize": "=25", "operation": "text", "positionX": 150, "positionY": 180, "lineLength": 18}, "typeVersion": 1}, {"name": "Read File URL", "type": "n8n-nodes-base.httpRequest", "position": [900, 350], "parameters": {"url": "https://www.needpix.com/file_download.php?url=//storage.needpix.com/thumbs/newspaper-412809_1280.jpg", "responseFormat": "file"}, "typeVersion": 1}], "connections": {"Webhook": {"main": [[{"node": "Read File URL", "type": "main", "index": 0}]]}, "Read File URL": {"main": [[{"node": "Edit Image", "type": "main", "index": 0}]]}}}