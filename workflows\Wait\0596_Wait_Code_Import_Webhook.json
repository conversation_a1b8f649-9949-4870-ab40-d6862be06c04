{"meta": {"instanceId": "7858a8e25b8fc4dae485c1ef345e6fe74effb1f5060433ef500b4c186c965c18"}, "nodes": [{"id": "4fe13927-84cb-4227-9daa-d6cef72d10b9", "name": "CarrierNameLookup", "type": "n8n-nodes-base.code", "position": [740, 320], "parameters": {"jsCode": "var carrierCodes={}\nJSON.parse($('Get Carrier Codes').first().json.data).data.forEach(datum=>{\n  carrierCodes[datum.iataCode] = {icao:datum.icaoCode, name:datum.commonName}\n})\nreturn carrierCodes"}, "typeVersion": 2}, {"id": "cb0ab93c-5fc5-402d-8ac9-672960b14112", "name": "Gmail", "type": "n8n-nodes-base.gmail", "position": [2080, 400], "parameters": {"message": "=Hi! We just found a bargain flight:\nDeparture Time: {{ $json.time }}\n[{{ $json.legs[0].carrier }}] {{ $json.duration }} flight from {{ $('FromTo').first().json.from }} to {{ $('FromTo').first().json.to }}\n", "options": {}, "subject": "=Bargain Flight Found! {{ $('FromTo').first().json.from }} -> {{ $('FromTo').first().json.to }} @ {{ $json.price }} on {{ $json.time }}"}, "credentials": {"gmailOAuth2": {"id": "1MUdv1HbrQUFABiZ", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "2f98b3e2-8a25-496e-89f3-b1ebe7e33192", "name": "Get Dates", "type": "n8n-nodes-base.code", "position": [940, 300], "parameters": {"jsCode": "const getNextSevenDays = () => {\n    const dates = [];\n    const today = new Date();\n\n    for (const i of [7, 14]) {\n        const nextDate = new Date(today);\n        nextDate.setDate(today.getDate() + i);\n        \n        // Format the date as YYYY-MM-DD\n        const formattedDate = nextDate.toISOString().split('T')[0];\n        dates.push({date:formattedDate});\n    }\n\n    return dates;\n};\n\nreturn getNextSevenDays()"}, "typeVersion": 2}, {"id": "3d8cf3fa-6ce7-422a-978f-afe2884c1e1a", "name": "Merge & Extract", "type": "n8n-nodes-base.code", "position": [1660, 400], "parameters": {"jsCode": "//Merge\nresult = []\nfor (const item of $input.all()) {\n result = result.concat(JSON.parse(item.json.data).data)\n}\n\n//Extract data fields\nfinal_result = []\nfor (x of result){\n  let legs = x.itineraries[0].segments.map(y=>{\n          let a = $('CarrierNameLookup').item.json[y.carrierCode];\n           let carrier = a.name? a.name: a.icao;\n           return {carrier:carrier, duration:y.duration}})\n\n\n  console.log(x.itineraries[0].segments[0].departure.at)\n  let duration = x.itineraries[0].duration\n  let price = x.price.total+' '+x.price.currency\n\n  final_result.push({legs:legs, time:x.itineraries[0].segments[0].departure.at, duration:duration, price:price})\n}\n\nreturn final_result"}, "typeVersion": 2}, {"id": "89df1c9b-c863-4cf5-88a2-18793d542f02", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [1200, 240], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "5595e34d-3736-42f6-ad64-e7f3c72c7f0a", "name": "Wait", "type": "n8n-nodes-base.wait", "position": [1060, 440], "webhookId": "f1f32ed2-cead-4ced-ba43-d15613316721", "parameters": {"amount": 3}, "typeVersion": 1.1}, {"id": "550005ad-ea97-4d83-90ac-67c7c583f2dc", "name": "Under Price", "type": "n8n-nodes-base.filter", "position": [1880, 400], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "bc2a9b61-41eb-45b1-9ee3-00fe211dadc3", "operator": {"type": "number", "operation": "lt"}, "leftValue": "={{ parseFloat($json.price) }}", "rightValue": 600}]}}, "typeVersion": 2.1}, {"id": "ce1beef1-4189-4cd7-b8c6-dd5bef2d9963", "name": "FromTo", "type": "n8n-nodes-base.set", "position": [560, 320], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "1944c696-6cfd-4d4d-8f3d-31cb89b37d3d", "name": "from", "type": "string", "value": "LHR"}, {"id": "9c4d5ac9-fa75-4fa7-a369-2b0493150203", "name": "to", "type": "string", "value": "JFK"}]}}, "typeVersion": 3.4}, {"id": "a4956257-28ce-4014-b549-ad413264c012", "name": "Amadeus Flight Search", "type": "n8n-nodes-base.httpRequest", "position": [1340, 440], "parameters": {"url": "https://test.api.amadeus.com/v2/shopping/flight-offers", "options": {}, "sendQuery": true, "authentication": "genericCredentialType", "genericAuthType": "oAuth2Api", "queryParameters": {"parameters": [{"name": "originLocationCode", "value": "={{ $('FromTo').item.json.from }}"}, {"name": "destinationLocationCode", "value": "={{ $('FromTo').item.json.to }}"}, {"name": "adults", "value": "1"}, {"name": "departureDate", "value": "={{ $json.date }}"}]}}, "credentials": {"oAuth2Api": {"id": "dVIDzfKxdhu5ZEpE", "name": "<PERSON><PERSON><PERSON>"}}, "typeVersion": 4.2}, {"id": "b7a41d45-799d-4f65-a904-f8fa82e59620", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [260, -320], "parameters": {"width": 490.**************, "height": 538.*************, "content": "# Amadeus Flight Bargains\nEvery day checks for bargain flights for an itinerary and price target of your choosing, and emails you if it finds a match.\n\n## Setup\n1. Create an api account on https://developers.amadeus.com/\n2. In **Amadeus Flight Search**, connect to Oauth2 API:\n  -- Grant Type - Client Credentials\n  -- Access Token URL - https://test.api.amadeus.com/v1/security/oauth2/token\n  -- Client ID/Secret - from your account\n3. Set your details in **Gmail**\n4. Set your desired Origin/Destination airports in FromTo\n5. Set the dates ahead you wish to search in **Get Dates** (default is 7 days and 14 days)\n6. Set the price target in **Under Price**\n\n## Test\nHit 'Test workflow'!"}, "typeVersion": 1}, {"id": "********-c96a-4905-87db-57ad19cead23", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [360, 320], "parameters": {"rule": {"interval": [{"triggerAtHour": 8}]}}, "typeVersion": 1.2}, {"id": "0fa74451-6053-470c-b5c5-9b25fd2e5b55", "name": "Get Carrier Codes", "type": "n8n-nodes-base.httpRequest", "position": [360, 600], "parameters": {"url": "https://test.api.amadeus.com/v1/reference-data/airlines", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "oAuth2Api"}, "credentials": {"oAuth2Api": {"id": "dVIDzfKxdhu5ZEpE", "name": "<PERSON><PERSON><PERSON>"}}, "typeVersion": 4.2}], "pinData": {"CarrierNameLookup": [{"0A": {}, "0B": {"icao": "BLA", "name": "BLUE AIR AVIATION"}, "0C": {}, "0D": {}, "0E": {"icao": "XYS"}, "0F": {}, "0G": {}, "0H": {}, "0I": {}, "0J": {"icao": "PJZ", "name": "PREMIUM JET AG"}, "0K": {}, "0L": {}, "0M": {}, "0N": {"name": "NORTH STAR AIR LTD"}, "0O": {"name": "STA TRAVEL"}, "0P": {}, "0Q": {}, "0R": {}, "0S": {}, "0T": {"icao": "TTL"}, "0U": {}, "0V": {"name": "VIETNAM AIR SERVICE"}, "0W": {}, "0X": {"icao": "XYT"}, "0Y": {"icao": "XYY"}, "0Z": {"icao": "XYR"}, "1A": {"icao": "AGT", "name": "AMADEUS"}, "1B": {"name": "ABACUS"}, "1C": {"name": "GEMINI"}, "1D": {"name": "RADDIX SOLUTIONS INTL"}, "1E": {"name": "CIVIL CHINA"}, "1F": {"icao": "TTF", "name": "INFINI TRAVEL"}, "1G": {"name": "TRAVELPORT"}, "1H": {"name": "SIRENA-TRAVEL"}, "1I": {}, "1J": {"name": "AXESS INTERNATIONAL"}, "1K": {"name": "MIXVEL"}, "1L": {"name": "CITIZENPLANE"}, "1M": {"name": "SYSTEMS TAIS"}, "1N": {"name": "NAVITAIRE NEW SKIES"}, "1O": {}, "1P": {"icao": "WSP", "name": "WORLDSPAN"}, "1Q": {}, "1R": {}, "1S": {"name": "SABRE"}, "1T": {"name": "HITIT"}, "1U": {"name": "ITA SOFTWARE"}, "1V": {"name": "GALILEO INTERNATIONAL"}, "1W": {}, "1X": {"name": "BRANSON AIR"}, "1Y": {"name": "DXC TECHNOLOGY SERVICES"}, "1Z": {"icao": "APD"}, "2A": {"name": "AIR ASTRA"}, "2B": {"icao": "AWT", "name": "ALBAWINGS"}, "2C": {}, "2D": {"icao": "DYA", "name": "EASTERN AIRLINES"}, "2E": {"name": "SMOKEY BAY AIR"}, "2F": {"icao": "AFU"}, "2G": {"name": "ANGARA AIRLINES"}, "2H": {"name": "THALYS"}, "2I": {"icao": "SRU", "name": "STAR PERU"}, "2J": {"icao": "VBW", "name": "AIR BURKINA"}, "2K": {"icao": "GLG", "name": "AVIANCA ECUADOR S.A."}, "2L": {"icao": "OAW", "name": "HELVETIC AIRWAYS"}, "2M": {"icao": "MYD", "name": "MAYA ISLAND AIR"}, "2N": {"icao": "XLE", "name": "NG Eagle Ltd"}, "2O": {"name": "REDEMPTION"}, "2P": {"icao": "GAP", "name": "PAL EXPRESS"}, "2Q": {}, "2R": {"icao": "RLB", "name": "SUNLIGHT AIR"}, "2S": {"icao": "STW", "name": "SOUTHWIND AIRLINES"}, "2T": {"name": "BERMUDAIR LIMITED"}, "2U": {"icao": "ERO", "name": "SUN D OR"}, "2V": {"name": "AMTRAK"}, "2W": {"icao": "WFL", "name": "W2FLY"}, "2X": {"icao": "XYA", "name": "AMADEUS TWO"}, "2Y": {"name": "AIR ANDAMAN"}, "2Z": {"icao": "PTB", "name": "PASSAREDO TRANSPORTES"}, "3A": {"name": "CHU KONG PASSENGER TSPT"}, "3B": {"icao": "NTB", "name": "BESTFLY"}, "3C": {"icao": "CVA", "name": "AIR CHATHAMS"}, "3D": {}, "3E": {"icao": "OMQ", "name": "MULTI AERO"}, "3F": {}, "3G": {}, "3H": {"icao": "AIE", "name": "AIR INUIT"}, "3I": {}, "3J": {"icao": "JBW", "name": "JUBBA AIRWAYS LIMITED"}, "3K": {"icao": "JSA", "name": "JETSTAR ASIA"}, "3L": {"icao": "ADY", "name": "INTERSKY"}, "3M": {"icao": "SIL", "name": "SILVER AIRWAYS CORP"}, "3N": {"icao": "URG", "name": "AIR URGA"}, "3O": {"icao": "MAC", "name": "AIR ARABIA MA"}, "3P": {"icao": "WPT", "name": "WORLD 2 FLY PORTUGAL"}, "3Q": {}, "3R": {"icao": "DVR", "name": "DIVI DIVI AIR"}, "3S": {"name": "AIR ANTILLES EXPRESS"}, "3T": {"icao": "TQQ", "name": "TARCO AVIATION"}, "3U": {"icao": "CSC", "name": "SICHUAN AIRLINES"}, "3V": {"icao": "TAY", "name": "ASL AIRLINES BELGIUM"}, "3W": {"icao": "MWI", "name": "MALAWI AIRLINES"}, "3X": {}, "3Y": {}, "3Z": {"icao": "TVP", "name": "SMARTWINGS POLAND"}, "4A": {"icao": "AMP", "name": "AERO TRANSPORTE"}, "4B": {"name": "BOUTIQUE AIR"}, "4C": {"icao": "ARE", "name": "LATAM AIRLINES COLOMBIA"}, "4D": {"icao": "ASD", "name": "AIR SINAI"}, "4E": {}, "4F": {"name": "FREEDOM AIRLINE EXPRESS"}, "4G": {"icao": "GZP", "name": "GAZPROMAVIA"}, "4H": {"icao": "HGG", "name": "HI AIR"}, "4I": {"name": "air antilles"}, "4J": {"name": "JETAIR CARIBBEAN"}, "4K": {"icao": "SMK", "name": "FREEDOM AIRLINE EXPRESS"}, "4L": {}, "4M": {"name": "LANARGENTINA"}, "4N": {"name": "AIR NORTH"}, "4P": {"name": "REGIONAL SKY"}, "4Q": {}, "4R": {"name": "RENFE VIAJEROS"}, "4S": {"name": "RED SEA AIRLINES"}, "4T": {"name": "TRANSWEST AIR LIMITED"}, "4U": {"icao": "GWI", "name": "GERMANWINGS"}, "4V": {"icao": "FGW", "name": "<PERSON>"}, "4W": {"icao": "WAV", "name": "WARBELOWS AIR VENTURES"}, "4X": {"icao": "MLH", "name": "AVION EXPRESS MALTA LTD"}, "4Y": {"icao": "OCN", "name": "EW Discover"}, "4Z": {"icao": "LNK", "name": "SA AIRLINK"}, "5A": {}, "5B": {"icao": "BSX", "name": "BASSAKA AIR"}, "5C": {}, "5D": {"icao": "SLI", "name": "AEROLITORAL"}, "5E": {}, "5F": {"icao": "FIA", "name": "BONAIRE AIR"}, "5G": {}, "5H": {}, "5I": {}, "5J": {"icao": "CEB", "name": "CEBU AIR"}, "5K": {"name": "HI FLY TRANSPORTES AEREO"}, "5L": {}, "5M": {"icao": "MNT", "name": "FLY MONTSERRAT"}, "5N": {"icao": "AUL", "name": "SMARTAVIA"}, "5O": {"icao": "FPO", "name": "ASL AIRLINES FRANCE"}, "5P": {"icao": "UZP", "name": "PANORAMA AIRWAYS"}, "5Q": {"name": "HOLIDAY EUROPE"}, "5R": {"icao": "RUC", "name": "RUTACA"}, "5S": {"icao": "GAK", "name": "GLOBAL AIR TRANSPORT"}, "5T": {"name": "CANADIAN NORTH"}, "5U": {"icao": "TGU", "name": "L A D E"}, "5V": {"name": "EVERTS"}, "5W": {"icao": "WAZ", "name": "WIZZ AIR ABU DHABI"}, "5X": {}, "5Y": {}, "5Z": {"icao": "KEM", "name": "CEMAIR"}, "6A": {"icao": "AMW", "name": "ARMENIA AIRWAYS"}, "6B": {"icao": "BLX", "name": "TUIFLY NORDIC"}, "6C": {}, "6D": {"icao": "TVQ", "name": "SMARTWINGS SLOVAKIA"}, "6E": {"icao": "IGO", "name": "INDIGO"}, "6F": {"name": "PRIMERA AIR NORDIC"}, "6G": {}, "6H": {"icao": "ISR", "name": "ISRAIR"}, "6I": {"icao": "MMD", "name": "AIR ALSIE"}, "6J": {"name": "SOLASEED AIR"}, "6K": {"icao": "TAH"}, "6L": {}, "6M": {}, "6N": {"icao": "NIN", "name": "NIGER AIRLINES"}, "6O": {"icao": "OBS", "name": "ORBEST GHD"}, "6P": {}, "6Q": {}, "6R": {"icao": "DRU", "name": "MIRNY AIR"}, "6S": {"name": "KATO AIRLINES"}, "6T": {}, "6U": {}, "6V": {}, "6W": {"icao": "FBS", "name": "FLYBOSNIA"}, "6X": {"icao": "XYB", "name": "AMADEUS SIX"}, "6Y": {"icao": "ART", "name": "SMARTLYNX AIRLINES"}, "6Z": {}, "7A": {}, "7B": {"icao": "UBE", "name": "BEES AIRLINE LLC"}, "7C": {"icao": "JJA", "name": "JEJU AIR"}, "7D": {}, "7E": {"icao": "AWU", "name": "SYLT AIR GMBH"}, "7F": {"icao": "FAB", "name": "FIRST AIR"}, "7G": {"icao": "SFJ", "name": "STAR FLYER"}, "7H": {"icao": "RVF", "name": "RAVN ALASKA"}, "7I": {"icao": "TLR", "name": "AIR LIBYA"}, "7J": {"icao": "TJK", "name": "TAJIK AIR"}, "7K": {}, "7L": {}, "7M": {"icao": "PAM", "name": "MAP LINHAS AEREAS"}, "7N": {}, "7O": {"icao": "TVL", "name": "SMARTWINGS HUNGARY"}, "7P": {"icao": "PST", "name": "AIR PANAMA"}, "7Q": {"icao": "MNU", "name": "ELITE AIRWAYS"}, "7R": {"icao": "RLU", "name": "RUSLINE"}, "7S": {"icao": "XYW", "name": "AMADEUS PDF 7S"}, "7T": {}, "7U": {}, "7V": {"name": "FEDERAL AIRLINES"}, "7W": {"icao": "WRC", "name": "WIND ROSE AVIATION"}, "7X": {"icao": "XYC", "name": "AMADEUS SEVEN"}, "7Y": {}, "7Z": {"icao": "EZR", "name": "Z AIR"}, "8A": {}, "8B": {"name": "TRANSNUSA AVIATION"}, "8C": {"name": "EAST STAR"}, "8D": {"icao": "EXV", "name": "FITS AVIATION  PVT  LTD"}, "8E": {"icao": "BRG", "name": "BERING AIR"}, "8F": {"icao": "STP", "name": "STP AIRWAYS"}, "8G": {"icao": "DTL", "name": "GIRJET"}, "8H": {"name": "BH AIR"}, "8I": {"icao": "LIP", "name": "LIPICAN AER"}, "8J": {}, "8K": {}, "8L": {"icao": "LKE"}, "8M": {"icao": "MMA", "name": "MYANMAR AIRWAYS INTL"}, "8N": {"icao": "REG", "name": "REGIONAL AIR SERVICES"}, "8O": {}, "8P": {"icao": "PCO", "name": "PAC.COASTAL"}, "8Q": {"icao": "OHY", "name": "ONUR AIR"}, "8R": {"icao": "AIA", "name": "AMELIA"}, "8S": {"name": "SHUN TAK"}, "8T": {"name": "AIR TINDI LTD"}, "8U": {"icao": "AAW", "name": "AFRIQIYAH AIRWAYS"}, "8V": {"name": "WRIGHT AIR SERVICES"}, "8W": {"icao": "EDR", "name": "FLY ALWAYS"}, "8X": {"icao": "XYD", "name": "AMADEUS EIGHT"}, "8Y": {"icao": "AAV", "name": "ASTRO AIR INTERNATIONAL"}, "8Z": {"icao": "CGA", "name": "CONGO AIRWAYS"}, "9A": {"name": "GRAN COLOMBIA DE AV"}, "9B": {"name": "ACCESRAIL"}, "9C": {"name": "SPRING AIRLINES"}, "9D": {"name": "GENGHIS KHAN AIRLINES"}, "9E": {"icao": "FLG", "name": "ENDEAVOR AIR"}, "9F": {"name": "EUROSTAR UK"}, "9G": {}, "9H": {"name": "AIR CHANGAN"}, "9I": {"icao": "LLR", "name": "ALLIANCE AIR"}, "9J": {"icao": "DAN"}, "9K": {"icao": "KAP", "name": "CAPE AIR"}, "9L": {}, "9M": {"icao": "GLR", "name": "CENTRAL MOUNTAIN AIR"}, "9N": {"icao": "TOS", "name": "TROPIC AIR LIMITED"}, "9O": {}, "9P": {"name": "FLY JINNAH"}, "9Q": {"icao": "CXE", "name": "CAICOS EXPRESS AIRWAYS"}, "9R": {"icao": "NSE", "name": "SATENA"}, "9S": {"icao": "XYX", "name": "AMADEUS  9S"}, "9T": {"icao": "AST", "name": "THAI SUMMER AIRWAYS"}, "9U": {"icao": "MLD", "name": "AIR MOLDOVA"}, "9V": {"icao": "ROI", "name": "AVIOR AIRLINES"}, "9W": {}, "9X": {"icao": "FDY"}, "9Y": {"name": "NATIONAL AIRWAYS"}, "9Z": {}, "A0": {"icao": "EFW", "name": "BA EUROFLYER"}, "A1": {"name": "A.P.G. DISTRIBUTION SYST"}, "A2": {"icao": "AWG", "name": "ANIMA WINGS"}, "A3": {"icao": "AEE", "name": "AEGEAN AIR"}, "A4": {"name": "JSC AZIMUTH AIRLINES"}, "A5": {"icao": "HOP", "name": "AIRLINAIR"}, "A6": {"icao": "HTU", "name": "AIR TRAVEL"}, "A7": {}, "A8": {"icao": "XAU", "name": "AEROLINK UGANDA LIMITED"}, "A9": {"icao": "TGZ", "name": "GEORGIAN AIRWAYS"}, "AA": {"icao": "AAL", "name": "AMERICAN AIRLINES"}, "AB": {}, "AC": {"icao": "ACA", "name": "AIR CANADA"}, "AD": {"icao": "AZU", "name": "AZUL LINHAS"}, "AE": {"icao": "MDA", "name": "MANDARIN AIR"}, "AF": {"icao": "AFR", "name": "AIR FRANCE"}, "AG": {"icao": "ARU", "name": "ARUBA AIRLINES"}, "AH": {"icao": "DAH", "name": "AIR ALGERIE"}, "AI": {"icao": "AIC", "name": "AIR INDIA"}, "AJ": {}, "AK": {"icao": "AXM", "name": "AIRASIA SDN BHD"}, "AL": {"icao": "APP", "name": "ALPAVIA"}, "AM": {"icao": "AMX", "name": "AEROMEXICO"}, "AN": {"name": "ADVANCED AIR"}, "AO": {"name": "AIR JUAN AVIATION"}, "AP": {"icao": "LAV"}, "AQ": {"name": "9 AIR"}, "AR": {"icao": "ARG", "name": "AEROLINEAS ARGENTINAS"}, "AS": {"icao": "ASA", "name": "ALASKA AIRLINES"}, "AT": {"icao": "RAM", "name": "R.AIR MAROC"}, "AU": {"icao": "CJL", "name": "CANADA JETLINES"}, "AV": {"icao": "AVA", "name": "AVIANCA"}, "AW": {"icao": "AFW", "name": "AFRICA WORLD AIRLINES"}, "AX": {}, "AY": {"icao": "FIN", "name": "FINNAIR"}, "AZ": {"icao": "ITY", "name": "ITA AIRWAYS"}, "B0": {"icao": "DJT", "name": "LA COMPAGNIE"}, "B1": {}, "B2": {"icao": "BRU", "name": "BELAVIA"}, "B3": {"icao": "BTN", "name": "BHUTAN AIRLINES"}, "B4": {"name": "BEOND"}, "B5": {"name": "EAST AFRICAN SAFARI AIR"}, "B6": {"icao": "JBU", "name": "JETBLUE AIRWAYS"}, "B7": {"icao": "UIA", "name": "UNI AIRWAYS"}, "B8": {"icao": "ERT", "name": "ERITREAN AIRLINES"}, "B9": {"icao": "IRB", "name": "IRAN AIRTOUR"}, "BA": {"icao": "BAW", "name": "BRITISH A/W"}, "BB": {"name": "SEABORNE AIRLINES"}, "BC": {"icao": "SKY", "name": "SKYMARK AIRLINES"}, "BD": {"name": "CAMBODIA BAYON AIRLINES"}, "BE": {"icao": "BEE", "name": "BRIT EUROP"}, "BF": {"icao": "FBU", "name": "FRENCH BEE"}, "BG": {"icao": "BBC", "name": "BIMAN"}, "BH": {}, "BI": {"icao": "RBA", "name": "ROYALBRUNEI"}, "BJ": {"icao": "LBT", "name": "NOUVELAIR"}, "BK": {"icao": "OKA"}, "BL": {"icao": "PIC", "name": "PACIFIC AIRLINES"}, "BM": {"icao": "MNS"}, "BN": {"icao": "LWG", "name": "LUXWING LTD"}, "BO": {}, "BP": {"icao": "BOT", "name": "AIR BOTSWANA"}, "BQ": {"icao": "SWU"}, "BR": {"icao": "EVA", "name": "EVA AIRWAYS"}, "BS": {"name": "US BANGLA AIRLINES"}, "BT": {"icao": "BTI", "name": "AIR BALTIC"}, "BU": {"name": "AFRICAINE"}, "BV": {"icao": "BPA", "name": "BLUE PANORAMA AIRLINES"}, "BW": {"icao": "BWA", "name": "CARIBBEAN AIR"}, "BX": {"icao": "ABL", "name": "AIR BUSAN"}, "BY": {"icao": "TOM", "name": "TUI"}, "BZ": {"icao": "BBG", "name": "BLUE BIRD AIRWAYS"}, "C0": {}, "C1": {"name": "TECTIMES SUDAMERICA"}, "C2": {"name": "CEIBA"}, "C3": {"icao": "TDR", "name": "TRADE AIR"}, "C4": {}, "C5": {"name": "COMMUTEAIR"}, "C6": {"icao": "MFX", "name": "MY FREIGHTER"}, "C7": {"icao": "CIN", "name": "CINNAMON AIR"}, "C8": {"icao": "CRA", "name": "CRONOS AIRLINES"}, "C9": {"icao": "CKL", "name": "CRONOS AIRLINES BENIN"}, "CA": {"icao": "CCA", "name": "AIR CHINA"}, "CB": {"name": "Trans Caribbean Air Exp"}, "CC": {}, "CD": {"icao": "CND", "name": "SEAVIEW AIR"}, "CE": {"icao": "CLG", "name": "CHALAIR AVIATION"}, "CF": {}, "CG": {"icao": "TOK", "name": "PNG AIR"}, "CH": {}, "CI": {"icao": "CAL", "name": "CHINA AIR"}, "CJ": {"icao": "CFE", "name": "BA CITYFLYER"}, "CK": {}, "CL": {"icao": "CLH", "name": "LUFTHANSA CITYLINE"}, "CM": {"icao": "CMP", "name": "COPA AIRLINES"}, "CN": {"name": "GRAND CHINA AIR"}, "CO": {}, "CP": {"name": "COMPASS AIRLINES"}, "CQ": {"icao": "CSV", "name": "COASTAL AIR"}, "CR": {}, "CS": {}, "CT": {"icao": "CYL", "name": "ALITALIA CITY LINER SPA"}, "CU": {"icao": "CUB", "name": "CUBANA"}, "CV": {}, "CW": {"icao": "SCW", "name": "SKYWEST CHARTER LLC"}, "CX": {"icao": "CPA", "name": "CATHAYPACIFIC"}, "CY": {"icao": "CYP", "name": "CYPRUS AIRWAYS"}, "CZ": {"icao": "CSN", "name": "CHINA SOUTHERN AIRLINES"}, "D0": {}, "D1": {"name": "AIR4 PASSENGER SERVICE"}, "D2": {"name": "SEVERSTAL AIRCOMPANY"}, "D3": {"icao": "DAO", "name": "DAALLO AIRLINES"}, "D4": {"icao": "GEL", "name": "AIRLINE GEO SKY"}, "D5": {}, "D6": {"name": "GECA"}, "D7": {"icao": "XAX", "name": "AIRASIAX SDN BHD"}, "D8": {"icao": "NSZ", "name": "NORWEGIAN AIR"}, "D9": {"icao": "DMQ", "name": "DAALLO AIRLINES SOMALIA"}, "DA": {}, "DB": {}, "DC": {}, "DD": {"icao": "NOK", "name": "NOK AIR"}, "DE": {"icao": "CFG", "name": "CONDOR"}, "DF": {"icao": "CIB"}, "DG": {"icao": "SRQ", "name": "CEBGO"}, "DH": {"name": "FLYADEN"}, "DI": {"icao": "MBU", "name": "MARABU AIRLINES"}, "DJ": {"icao": "DJI", "name": "AIR DJIBOUTI"}, "DK": {"icao": "VKG", "name": "SUNCLASS AIRLINES"}, "DL": {"icao": "DAL", "name": "DELTA AIRLINES"}, "DM": {"icao": "DWI", "name": "ARAJET"}, "DN": {"name": "DAN AIR"}, "DO": {"name": "SKY HIGH"}, "DP": {"icao": "PBD", "name": "POBEDA AIRLINES"}, "DQ": {"icao": "KHH", "name": "ALEXANDRIA AIRLINES"}, "DR": {"name": "RUILI AIRLINES"}, "DS": {"icao": "EZS", "name": "EASYJET SWITZERLAND"}, "DT": {"icao": "DTA", "name": "TAAG Linhas A<PERSON>as"}, "DU": {"icao": "LIZ", "name": "SKY JET M.G. INC."}, "DV": {"icao": "VSV", "name": "JSC AIRCOMPANY SCAT"}, "DW": {}, "DX": {"icao": "DTR", "name": "DAT"}, "DY": {"icao": "NOZ", "name": "NORWEGIAN AIR"}, "DZ": {"name": "DONGHAI AIRLINES"}, "E0": {}, "E1": {}, "E2": {"name": "EUROWINGS EUROPE"}, "E3": {"icao": "EGW", "name": "EGO AIRWAYS"}, "E5": {"icao": "RBG", "name": "AIR ARABIA EGYPT"}, "E6": {"icao": "EWL", "name": "EUROWINGS EUROPE"}, "E7": {"name": "EQUAFLIGHT SERVICE"}, "E9": {"icao": "EVE", "name": "IBEROJET AIRLINES"}, "EA": {"icao": "EHN", "name": "EMERALD AIRLINES"}, "EB": {"icao": "PLM", "name": "WAMOS AIR"}, "EC": {}, "ED": {"name": "AIRBLUE"}, "EE": {"icao": "EST", "name": "XFLY"}, "EF": {}, "EG": {"name": "AER LINGUS UK LIMITED"}, "EH": {"icao": "AKX", "name": "ANA WINGS"}, "EI": {"icao": "EIN", "name": "AER LINGUS"}, "EJ": {"name": "EQUATORIAL CONGO ECAIR"}, "EK": {"icao": "UAE", "name": "EMIRATES"}, "EL": {"icao": "RIE", "name": "ARIELLA"}, "EM": {}, "EN": {"icao": "DLA", "name": "AIR DOLOMITI"}, "EO": {"name": "AIR GO EGYPT"}, "EP": {"icao": "IRC", "name": "IRAN ASEMAN AIRLINES"}, "EQ": {"name": "FLY ANGOLA"}, "ER": {"icao": "SEP", "name": "SERENE AIR"}, "ES": {"icao": "ETR", "name": "ESTELAR"}, "ET": {"icao": "ETH", "name": "ETHIOPIAN AIRLINES"}, "EU": {"name": "CHENGDU AIRLINES"}, "EV": {"icao": "ASQ", "name": "EXPRESSJET AIRLINES"}, "EW": {"icao": "EWG", "name": "EUROWINGS"}, "EX": {"icao": "5AH", "name": "REGIONAL EXPRESS AMERICA"}, "EY": {"icao": "ETD", "name": "ETIHAD AIRWAYS"}, "EZ": {"icao": "SUS", "name": "SUN AIR OF SCANDINAVIA"}, "F0": {}, "F1": {"name": "FARELOGIX"}, "F2": {"name": "SAFARILINK AVIATION"}, "F3": {"icao": "FAD", "name": "FLYADEAL"}, "F4": {}, "F5": {}, "F6": {"icao": "VAW", "name": "FLY2SKY"}, "F7": {"icao": "RSY", "name": "I FLY"}, "F8": {"icao": "FLE", "name": "FLAIR AIRLINES"}, "F9": {"icao": "FFT", "name": "FRONTIER AIRLINES"}, "FA": {"icao": "SFR", "name": "SAFAIR"}, "FB": {"icao": "LZB", "name": "BALKAN AIR TO"}, "FC": {"name": "LINK AIRWAYS FLY FC"}, "FD": {"icao": "AIQ", "name": "THAI AIRASIA"}, "FE": {"icao": "IHO", "name": "SEVEN FOUR EIGHT AIR SER"}, "FF": {"icao": "FXX", "name": "FELIX AIRWAYS"}, "FG": {"icao": "AFG", "name": "ARIANA AFGHAN AIRLINES"}, "FH": {"icao": "FHY", "name": "FREEBIRD AIRLINES"}, "FI": {"icao": "ICE", "name": "ICELANDAIR"}, "FJ": {"icao": "FJI", "name": "FIJI AIRWAYS"}, "FK": {}, "FL": {"icao": "LPA", "name": "AIR LEAP AVIATION"}, "FM": {"icao": "CSH", "name": "SHANGHAI AIRLINES"}, "FN": {"icao": "FJW", "name": "REGIONAL AIR"}, "FO": {}, "FP": {"name": "PELICAN AIRLINES"}, "FQ": {"icao": "CWN"}, "FR": {"icao": "RYR", "name": "RYANAIR"}, "FS": {"icao": "FOX", "name": "FLYR AS"}, "FT": {"name": "FLYEGYPT"}, "FU": {"name": "FUZHOU AIRLINES"}, "FV": {"icao": "SDM", "name": "ROSSIYA AIRLINES"}, "FW": {"name": "IBEX AIRLINES"}, "FX": {}, "FY": {"icao": "FFM", "name": "FIREFLY"}, "FZ": {"icao": "FDB", "name": "FLYDUBAI"}, "G0": {}, "G1": {}, "G2": {"icao": "TJJ", "name": "GULLIVAIR"}, "G3": {"icao": "GLO", "name": "GOL LINHAS AEREAS S/A"}, "G4": {"icao": "AAY", "name": "ALLEGIANT AIR"}, "G5": {"icao": "HXA", "name": "CHINA EXPRESS AIRLINES"}, "G6": {"name": "FLY ARNA"}, "G7": {"icao": "GJS", "name": "GOJET AIRLINES"}, "G8": {"icao": "GOW", "name": "GO FIRST"}, "G9": {"icao": "ABY", "name": "AIR ARABIA"}, "GA": {"icao": "GIA", "name": "GARUDA"}, "GB": {}, "GC": {}, "GD": {"name": "AVIAIR"}, "GE": {"icao": "GBB", "name": "TRANSASIA"}, "GF": {"icao": "GFA", "name": "GULF AIR"}, "GG": {}, "GH": {"icao": "GHA", "name": "GHANA AIR"}, "GI": {}, "GJ": {"icao": "CDC", "name": "ZHEJIANG LOONG AIRLINES"}, "GK": {"icao": "JJP", "name": "JETSTAR JAPAN"}, "GL": {"icao": "GRL", "name": "AIR GREENLAND"}, "GM": {"icao": "GSW", "name": "CHAIR AIRLINES"}, "GN": {}, "GO": {"icao": "GHN", "name": "AIR GHANA LIMITED"}, "GP": {"icao": "RIV"}, "GQ": {"icao": "SEH", "name": "SKY EXPRESS"}, "GR": {"icao": "AUR", "name": "AURIGNY AIR SERVICES"}, "GS": {"name": "TIANJIN AIRLINES"}, "GT": {"name": "AIR GUILIN"}, "GU": {"icao": "GUG", "name": "AVIATECA"}, "GV": {"icao": "GUN", "name": "GRANT AVIATION"}, "GW": {}, "GX": {"name": "GX AIRLINES"}, "GY": {}, "GZ": {"name": "AIR RAROTONGA"}, "H0": {}, "H1": {"name": "HAHN AIR SYSTEMS"}, "H2": {"icao": "SKU", "name": "SKY AIRLINE"}, "H3": {"icao": "HLJ", "name": "HARBOUR AIR"}, "H4": {"icao": "HYS", "name": "HISKY EUROPE SRL"}, "H5": {"icao": "OMT", "name": "CM AIRLINES"}, "H6": {}, "H7": {"icao": "HYM", "name": "HISKY"}, "H8": {"name": "SKY AIRLINE PERU"}, "H9": {"icao": "HIM", "name": "HIMALAYA AIRLINES"}, "HA": {"icao": "HAL", "name": "HAWAIIAN AIRLINES"}, "HB": {"icao": "HGB", "name": "GREATER BAY AIRLINES"}, "HC": {"icao": "SZN", "name": "AIR SENEGAL"}, "HD": {"icao": "ADO", "name": "AIRDO"}, "HE": {"name": "BAR AVIATION LIMITED"}, "HF": {"icao": "VRE", "name": "AIRCOTEIVOIRE"}, "HG": {"icao": "HTP", "name": "HALA AIR"}, "HH": {"icao": "QNT", "name": "QANOT SHARQ"}, "HI": {"name": "PAPILLON AIRWAYS"}, "HJ": {"icao": "HEJ", "name": "HELLENIC STAR"}, "HK": {"name": "SKIPPERS AVIATION PTY"}, "HL": {}, "HM": {"icao": "SEY", "name": "AIR SEYCHELLES"}, "HN": {"icao": "EQX", "name": "EQUINOXAIR"}, "HO": {"icao": "DKH", "name": "JUNEYAO AIRLINES"}, "HP": {"name": "POPULAIR"}, "HQ": {}, "HR": {"icao": "HHN", "name": "HAHN AIR"}, "HS": {"name": "HELI SECURITE"}, "HT": {}, "HU": {"icao": "CHH", "name": "HAINAN AIRLINES"}, "HV": {"icao": "TRA", "name": "TRANSAVIA AIRLINES"}, "HW": {"name": "NORTH WRIGHT AIR"}, "HX": {"icao": "CRK", "name": "HONG KONG AIRLINES"}, "HY": {"icao": "UZB", "name": "UZBEKISTAN AIRWAYS"}, "HZ": {"icao": "SHU", "name": "AURORA AIRLINES"}, "I0": {}, "I1": {"name": "CTS VIAGGI"}, "I2": {"name": "IBERIA EXPRESS"}, "I3": {}, "I4": {"name": "ISLAND AIR EXPRESS"}, "I5": {"icao": "IAD", "name": "AIR INDIA EXPRESS"}, "I6": {"name": "IRYO"}, "I7": {"name": "INDIAONE AIR"}, "I8": {"icao": "IZA", "name": "IZHAVIA"}, "I9": {}, "IA": {"icao": "IAW", "name": "I A W"}, "IB": {"icao": "IBE", "name": "IBERIA"}, "IC": {"name": "FLY91"}, "ID": {"icao": "BTK", "name": "BATIK AIR INDONESIA"}, "IE": {"icao": "SOL", "name": "SOLOMON AIR"}, "IF": {"icao": "FBA", "name": "FBA"}, "IG": {}, "IH": {"icao": "SRS", "name": "SOUTHERN SKY AIRLINES"}, "II": {}, "IJ": {"name": "SPRING JAPAN"}, "IK": {"name": "AIR KIRIBATI"}, "IL": {"name": "PT.TRIGANA AIR SERVICE"}, "IM": {}, "IN": {"icao": "LKN", "name": "NAM AIR"}, "IO": {"icao": "IAE", "name": "IrAero"}, "IP": {"icao": "PAS"}, "IQ": {"icao": "QAZ", "name": "QAZAQ AIR"}, "IR": {"icao": "IRA", "name": "IRAN AIR"}, "IS": {"icao": "SHI", "name": "SEPEHRAN AIRLINES"}, "IT": {"name": "TIGERAIR TAIWAN"}, "IU": {"name": "PT. SUPER AIR JET"}, "IV": {"icao": "GPX", "name": "GP AVIATION"}, "IW": {"name": "PT WINGS ABADI AIRLINES"}, "IX": {"icao": "AXB", "name": "AIR INDIA EXPRESS"}, "IY": {"icao": "IYE", "name": "YEMEN AIRWAYS"}, "IZ": {"icao": "AIZ", "name": "ARKIA"}, "J0": {}, "J1": {}, "J2": {"icao": "AHY", "name": "AZERBAIJAN AI"}, "J3": {"icao": "PLR", "name": "NORTHWESTERN AIR LEASE"}, "J4": {"name": "BADR AIRLINES"}, "J5": {"name": "ALASKA SEAPLANE SERVICE"}, "J6": {}, "J7": {"icao": "ABS", "name": "CENTRE AVIA"}, "J8": {"icao": "BVT", "name": "BERJAYA AIR"}, "J9": {"icao": "JZR", "name": "JAZEERA AIRWAYS"}, "JA": {"icao": "JAT", "name": "JETSMART SPA"}, "JB": {}, "JC": {"icao": "JAC"}, "JD": {"icao": "CBJ", "name": "BEIJING CAPIT"}, "JE": {"icao": "MNO", "name": "MANGO"}, "JF": {"icao": "OTT", "name": "OTT AIRLINES"}, "JG": {}, "JH": {"icao": "FDA", "name": "FUJI DREAM AIRLINES"}, "JI": {"icao": "AAG", "name": "ARMENIAN AIRLINES"}, "JJ": {"icao": "TAM", "name": "LATAM AIRLINES BRASIL"}, "JK": {}, "JL": {"icao": "JAL", "name": "JAPAN AIRLINES"}, "JM": {"icao": "JMA", "name": "JAMBOJET"}, "JN": {"name": "ALASKA AIR TRANSIT"}, "JO": {}, "JP": {}, "JQ": {"icao": "JST", "name": "JETSTAR"}, "JR": {"icao": "JOY", "name": "JOY AIR"}, "JS": {"icao": "KOR", "name": "AIR KORYO"}, "JT": {"icao": "LNI", "name": "LION AIRLINES"}, "JU": {"icao": "ASL", "name": "AIR SERBIA"}, "JV": {"icao": "BLS", "name": "BEARSKIN AIRLINES"}, "JW": {}, "JX": {"icao": "SJX", "name": "STARLUX AIRLINES"}, "JY": {"name": "INTERCARIBBEAN AIRWAYS"}, "JZ": {"icao": "JAP", "name": "JETSMART AIRLINES PERU"}, "K0": {}, "K1": {}, "K2": {"name": "PAKLOOK AIR"}, "K3": {"icao": "SAQ", "name": "Safe Air"}, "K4": {}, "K5": {}, "K6": {"icao": "KHV", "name": "CAMBODIA ANGKOR AIR"}, "K7": {"icao": "KBZ", "name": "MINGALAR AIRLINES"}, "K8": {}, "K9": {"icao": "TEZ"}, "KA": {"icao": "ANK", "name": "AERO NOMAD AIRLINES"}, "KB": {"icao": "DRK", "name": "DRUK AIR"}, "KC": {"icao": "KZR", "name": "AIR ASTANA"}, "KD": {}, "KE": {"icao": "KAL", "name": "KOREAN AIR"}, "KF": {"icao": "ABB", "name": "AIR BELGIUM"}, "KG": {"icao": "LYM", "name": "KEY LIME AIR CORPORATION"}, "KI": {"icao": "SJB", "name": "SKS AIRWAYS"}, "KJ": {"icao": "AIH", "name": "AIR INCHEON"}, "KK": {"icao": "NGN", "name": "LEAV AVIATION GMBH"}, "KL": {"icao": "KLM", "name": "KLM"}, "KM": {"icao": "KMM", "name": "KM MALTA AIRLINES"}, "KN": {"icao": "CUA", "name": "CHINA UNITED AIRLINES"}, "KO": {"name": "OJSC KOMIAVIATRANS"}, "KP": {"icao": "SKK", "name": "ASKY"}, "KQ": {"icao": "KQA", "name": "KENYAAIRWAY"}, "KR": {"name": "CAMBODIA AIRWAYS"}, "KS": {"icao": "KON", "name": "AIR CONNECT AVIATION GRO"}, "KT": {"icao": "HOG", "name": "MAHOGANY AIR"}, "KU": {"icao": "KAC", "name": "KUWAIT AIR"}, "KV": {"name": "KRASAVIA"}, "KW": {"icao": "JRQ"}, "KX": {"icao": "CAY", "name": "CAYMAN AIRWAYS"}, "KY": {"icao": "KNA", "name": "Kunming Airlines"}, "KZ": {}, "L0": {"name": "LIZ AVIATION BF"}, "L1": {}, "L2": {}, "L3": {}, "L4": {}, "L5": {"icao": "REA", "name": "RED AIR SRL"}, "L6": {"icao": "MAI", "name": "MAURITANIA AIRLINES"}, "L7": {}, "L8": {"icao": "TON", "name": "LULUTAI AIRLINES"}, "L9": {"icao": "LWI", "name": "LUMIWINGS"}, "LA": {"icao": "LAN", "name": "LATAM AIRLINES GROUP"}, "LB": {"icao": "LXX", "name": "LIBYAN EXPRESS"}, "LC": {"icao": "NIS", "name": "AEROTAXI LA COSTENA"}, "LD": {}, "LE": {"icao": "STB"}, "LF": {"icao": "VTE", "name": "CONTOUR AIRLINES"}, "LG": {"icao": "LGL", "name": "LUXAIR"}, "LH": {"icao": "DLH", "name": "LUFTHANSA"}, "LI": {"icao": "LIA", "name": "LIAT"}, "LJ": {"icao": "JNA", "name": "JIN AIR"}, "LK": {"icao": "LLL", "name": "LAO SKYWAY"}, "LL": {"name": "CHINA GENERAL AVIATION"}, "LM": {"icao": "LOG", "name": "LOGANAIR"}, "LN": {"icao": "LAA", "name": "LIBYAN AIR"}, "LO": {"icao": "LOT", "name": "LOT"}, "LP": {"icao": "LPE", "name": "LATAM AIRLINES PERU"}, "LQ": {"name": "LANMEI AIRLINES"}, "LR": {"icao": "LRC", "name": "L A C S A"}, "LS": {"icao": "EXS", "name": "JET2.COM"}, "LT": {"name": "LONGJIANG AIRLINES"}, "LU": {"name": "LATAM AIRLINES CHILE"}, "LV": {"icao": "DTG", "name": "AIRCOMPANY AIRZENA"}, "LW": {"icao": "LDA", "name": "LAUDA EUROPE"}, "LX": {"icao": "SWR", "name": "SWISS"}, "LY": {"icao": "ELY", "name": "EL AL"}, "LZ": {}, "M0": {"icao": "MNG", "name": "AEROMONGOLIA"}, "M1": {}, "M2": {"name": "MHS AVIATION"}, "M3": {}, "M4": {"name": "MISTRAL AIR"}, "M5": {"icao": "KEN", "name": "KENMORE AIR"}, "M6": {}, "M7": {}, "M8": {"name": "UNDEFINED"}, "M9": {"icao": "MSI", "name": "MOTOR SICH AIRLINES"}, "MA": {}, "MC": {}, "MD": {"icao": "MGY", "name": "MADAGASCAR AIRLINES"}, "ME": {"icao": "MEA", "name": "MIDDLE EAST"}, "MF": {"icao": "CXA", "name": "XIAMEN AIRLINES"}, "MG": {"icao": "EZA", "name": "EZNIS AIRWAYS"}, "MH": {"icao": "MAS", "name": "MALAYSIA"}, "MI": {"icao": "FHM", "name": "FREEBIRD AIRLINES EUROPE"}, "MJ": {"icao": "MYW", "name": "MYWAY AIRLINES"}, "MK": {"icao": "MAU", "name": "AIR MAURITI"}, "ML": {"icao": "FML", "name": "SKY MALI"}, "MM": {"icao": "APJ", "name": "PEACH AVIATION"}, "MN": {"icao": "CAW", "name": "COMAIR LTD"}, "MO": {"icao": "CAV", "name": "CALM AIR INTERNATIONAL"}, "MP": {}, "MQ": {"icao": "ENY", "name": "ENVOY AIR"}, "MR": {"icao": "MML", "name": "MONGOLIAN AIR"}, "MS": {"icao": "MSR", "name": "EGYPTAIR"}, "MT": {"name": "MALTA MEDAIR"}, "MU": {"icao": "CES", "name": "CHINA EASTERN AIRLINES"}, "MV": {"icao": "MAR", "name": "AIR MEDITERRANEAN"}, "MW": {"name": "Connect Airlines"}, "MX": {"name": "MEXICANA"}, "MY": {"name": "MASWINGS"}, "MZ": {"icao": "AHX", "name": "AMAKUSA AIRLINES"}, "N0": {}, "N1": {"name": "DARWIN TRAVEL TECHNOLOGY"}, "N2": {"icao": "NIG", "name": "AERO CONTRACTORS NIGERIA"}, "N3": {"icao": "VOS", "name": "VOLARIS EL SALVADOR"}, "N4": {"icao": "NWS", "name": "NORD WIND"}, "N5": {"icao": "NRL"}, "N6": {"icao": "TZS", "name": "TCA"}, "N7": {"icao": "FCM"}, "N8": {"icao": "NCR", "name": "NATIONAL AIRLINES"}, "N9": {"name": "SHREE AIRLINES"}, "NA": {"name": "NESMA AIRLINES"}, "NB": {"icao": "BNL", "name": "BERNIQ AIRWAYS"}, "NC": {"icao": "NJS"}, "ND": {"icao": "NDA", "name": "NORDICA"}, "NE": {"icao": "NMA", "name": "NESMA AIRLINES"}, "NF": {"icao": "AVN", "name": "AIR VANUATU"}, "NG": {"icao": "NAI", "name": "NOVAIR"}, "NH": {"icao": "ANA", "name": "ALL NIPPON"}, "NI": {"icao": "PGA", "name": "PORTUGALIA"}, "NJ": {}, "NK": {"icao": "NKS", "name": "SPIRIT AIRLINES"}, "NL": {"icao": "AEH", "name": "Amelia International"}, "NM": {"icao": "NTR", "name": "AIR MOANA"}, "NN": {}, "NO": {"icao": "NOS", "name": "NEOS SPA"}, "NP": {"icao": "NIA", "name": "NILE AIR"}, "NQ": {"icao": "AJX", "name": "AIR JAPAN COMPANY LTD"}, "NR": {"icao": "MAV", "name": "MANTA AVIATION"}, "NS": {"icao": "HBH", "name": "HEBEI AIRLINES"}, "NT": {"icao": "IBB", "name": "BINTER CAN"}, "NU": {"icao": "JTA", "name": "JAPAN TRANSOC"}, "NV": {}, "NW": {"name": "CELESTE"}, "NX": {"icao": "AMU", "name": "AIR MACAU"}, "NY": {"icao": "FXI", "name": "FLUGFELAG ISLANDS"}, "NZ": {"icao": "ANZ", "name": "AIR NEW ZEALAND"}, "O0": {}, "O1": {}, "O2": {"icao": "HPK", "name": "LINEAR AIR"}, "O3": {}, "O4": {"icao": "OTF", "name": "ORANGE2FLY AIRLINES"}, "O5": {}, "O6": {}, "O7": {"icao": "OMB", "name": "OMNI-BLU"}, "O8": {}, "O9": {}, "OA": {"icao": "OAL", "name": "OLYMPIC AIR"}, "OB": {"icao": "BOV", "name": "BOLIVIANA"}, "OC": {"icao": "ORC", "name": "ORIENTAL AIR BRIDGE"}, "OD": {"icao": "MXD", "name": "BATIK AIR MALAYSIA"}, "OE": {"icao": "LDM", "name": "LAUDAMOTION"}, "OF": {"name": "OVERLAND AIRWAYS"}, "OG": {"icao": "FPY", "name": "FLY PLAY"}, "OH": {"icao": "JIA", "name": "PSA AIRLINES"}, "OI": {"icao": "HND", "name": "HINTERLAND AVIATION"}, "OJ": {"icao": "OLA", "name": "NYXAIR"}, "OK": {"icao": "CSA", "name": "CZECH AIRLINE"}, "OL": {"icao": "PAO", "name": "SAMOA AIRWAYS"}, "OM": {"icao": "MGL", "name": "MIAT"}, "ON": {"icao": "RON", "name": "NAURU AIRLINES"}, "OO": {"icao": "SKW", "name": "SKYWEST AIRLINES"}, "OP": {"icao": "DIG", "name": "PASSION AIR"}, "OQ": {"name": "CHONGQING AIRLINES"}, "OR": {"icao": "TFL", "name": "TUI FLY NETHERLANDS"}, "OS": {"icao": "AUA", "name": "AUSTRIANAIR"}, "OT": {"icao": "CDO", "name": "TCHADIA AIRLINES"}, "OU": {"icao": "CTN", "name": "CROATIA"}, "OV": {"icao": "OMS", "name": "SALAM AIR"}, "OW": {"icao": "SEW", "name": "SKYWARD EXPRESS"}, "OX": {"icao": "OEW", "name": "ONE AIRWAYS"}, "OY": {}, "OZ": {"icao": "AAR", "name": "ASIANA"}, "P0": {"icao": "PFZ", "name": "PROFLIGHT ZAMBIA"}, "P1": {"name": "PUBLICCHARTERS.COM"}, "P2": {"icao": "XAK", "name": "AIRKENYA EXPRESS"}, "P3": {"icao": "POE", "name": "PORTER AIRLINES"}, "P4": {"icao": "APK", "name": "AIR PEACE LIMITED"}, "P5": {"icao": "RPB", "name": "AERO REPUBLICA"}, "P6": {"icao": "PSC", "name": "PASCAN"}, "P7": {}, "P8": {"icao": "SRN", "name": "SPRINTAIR"}, "P9": {}, "PA": {"name": "AIRBLUE"}, "PB": {"icao": "SPR", "name": "PAL AIRLINES"}, "PC": {"icao": "PGT", "name": "PEGASUS AIRLINES"}, "PD": {"name": "PORTER AIRLINES CANADA"}, "PE": {"icao": "PEV", "name": "PEOPLES"}, "PF": {"icao": "SIF", "name": "AIR SIAL LIMITED"}, "PG": {"icao": "BKP", "name": "BANGKOK AIR"}, "PH": {"icao": "SFZ", "name": "PIONAIR AUSTRALIA"}, "PI": {"icao": "RKA", "name": "POLAR AIRLINES"}, "PJ": {"name": "AIR SAINT PIERRE"}, "PK": {"icao": "PIA", "name": "PAKISTAN INTERNATIONAL"}, "PL": {"name": "SOUTHERN AIR CHARTER"}, "PM": {"icao": "CNF", "name": "CANARY FLY"}, "PN": {"name": "WEST AIR"}, "PO": {}, "PP": {}, "PQ": {"icao": "SQP", "name": "SKYUP AIRLINES"}, "PR": {"icao": "PAL", "name": "PHILIPPINE AL"}, "PS": {"icao": "AUI", "name": "UIA"}, "PT": {"name": "PIEDMONT AIRLINES"}, "PU": {"icao": "PUE", "name": "PLUS ULTRA"}, "PV": {"icao": "SBU", "name": "SAINT BARTH COMMUTER"}, "PW": {"icao": "PRF", "name": "PRECISION AIR"}, "PX": {"icao": "ANG", "name": "AIR NIUGINI"}, "PY": {"icao": "SLM", "name": "SURINAM AIRWAYS"}, "PZ": {"icao": "LAP", "name": "LATAM AIRLINES PARAGUAY"}, "Q0": {}, "Q1": {"name": "SQIVA SISTEM"}, "Q2": {}, "Q3": {"name": "ANGUILLA AIR SERVICES"}, "Q4": {"icao": "ELE", "name": "EUROAIRLINES"}, "Q5": {"icao": "MLA", "name": "FORTY MILE AIR"}, "Q6": {"icao": "VOC", "name": "VOLARIS COSTA RICA"}, "Q7": {}, "Q8": {"icao": "TSG", "name": "TRANS AIR"}, "Q9": {}, "QA": {}, "QB": {"icao": "IRQ", "name": "QESHM AIR"}, "QC": {"icao": "CRC", "name": "CAMAIR-CO"}, "QD": {}, "QE": {}, "QF": {"icao": "QFA", "name": "QANTAS"}, "QG": {"icao": "CTV", "name": "CITILINK"}, "QH": {"icao": "BAV", "name": "BAMBOO AIRWAYS"}, "QI": {"icao": "IAN", "name": "IBOM AIRLINES"}, "QJ": {}, "QK": {"icao": "JZA", "name": "JAZZ AVIATION"}, "QL": {"icao": "LER", "name": "LINEA AEREA DE SERVICIO"}, "QM": {"name": "MONACAIR"}, "QN": {"icao": "SKP", "name": "Skytrans"}, "QO": {}, "QP": {"icao": "AKJ", "name": "AKASA AIR"}, "QQ": {"icao": "UTY", "name": "ALLIANCE AIRLINES"}, "QR": {"icao": "QTR", "name": "QATAR AIRWAYS"}, "QS": {"icao": "TVS", "name": "TRAVELSERVICE"}, "QT": {}, "QU": {}, "QV": {"icao": "LAO", "name": "LAO AIRLINES"}, "QW": {"name": "QINGDAO AIRLINES"}, "QX": {"icao": "QXE", "name": "HORIZON AIR"}, "QY": {}, "QZ": {"icao": "AWQ", "name": "AIRASIA INDONESIA"}, "R0": {}, "R1": {}, "R2": {"name": "TRANSAIR SENEGAL"}, "R3": {"icao": "SYL", "name": "JSC AIRCOMPANY YAKUTIA"}, "R4": {}, "R5": {"icao": "JAV", "name": "JORDAN AVIATION"}, "R6": {"icao": "DNU", "name": "DAT"}, "R7": {}, "R8": {}, "R9": {}, "RA": {"icao": "RNA", "name": "NEPAL AIRLINES"}, "RB": {"icao": "SYR", "name": "SYRIAN ARAB AIRLINES"}, "RC": {"icao": "FLI", "name": "ATL.AIRWAYS"}, "RD": {"name": "SKY CANA"}, "RE": {}, "RF": {"icao": "EOK", "name": "AERO K AIRLINES"}, "RG": {"icao": "RJD", "name": "ROTANA JET"}, "RH": {}, "RI": {"name": "PT MANDALA"}, "RJ": {"icao": "RJA", "name": "RYLJORDANIA"}, "RK": {"icao": "RUK", "name": "RYANAIR UK"}, "RL": {"icao": "ABG", "name": "ROYAL FLIGHT AIRLINES"}, "RM": {"name": "AIRCOMPANY ARMENIA"}, "RN": {"icao": "SZL", "name": "ESWATINI AIR"}, "RO": {"icao": "ROT", "name": "TAROM"}, "RP": {"icao": "BPS"}, "RQ": {"icao": "KMF", "name": "KAM AIR"}, "RR": {"icao": "RFR", "name": "R.A.F NO1"}, "RS": {"icao": "ASV", "name": "AIR SEOUL"}, "RT": {"name": "JSC UVT AERO"}, "RU": {}, "RV": {"icao": "ROU", "name": "AIR CANADA ROUGE"}, "RW": {"icao": "RYL", "name": "ROYAL AIR"}, "RX": {}, "RY": {"name": "JIANGXI AIR"}, "RZ": {"icao": "LRS", "name": "SANSA"}, "S0": {"icao": "NSO", "name": "AEROLINEAS SOSA"}, "S1": {"name": "LUFTHANSA SYSTEMS"}, "S3": {}, "S4": {"icao": "RZO", "name": "SATA INTL"}, "S5": {}, "S6": {"icao": "KSZ", "name": "SUNRISE AIRWAYS"}, "S7": {"icao": "SBI", "name": "S7 AIRLINES"}, "S8": {"icao": "SDA", "name": "SOUNDS AIR"}, "S9": {"name": "TRI STATE CHARTER"}, "SA": {"icao": "SAA", "name": "S A A"}, "SB": {"icao": "ACI", "name": "CAL.INT"}, "SC": {"icao": "CDG", "name": "SHANDONG AIRLINES"}, "SD": {"icao": "SUD", "name": "SUDAN AIRWAYS"}, "SE": {}, "SF": {"icao": "DTH", "name": "TASSILI AIRLINES"}, "SG": {"icao": "SEJ", "name": "SPICEJET"}, "SH": {"name": "SHARP AVIATION"}, "SI": {"icao": "BCI", "name": "BLUE ISLANDS"}, "SJ": {"icao": "SJY", "name": "SRIWIJAYA AIR"}, "SK": {"icao": "SAS", "name": "SAS"}, "SL": {"icao": "TLM", "name": "RIO SUL"}, "SM": {"icao": "MSC", "name": "AIR CAIRO"}, "SN": {"icao": "BEL", "name": "BRUSSELS AIR"}, "SO": {"icao": "SNR", "name": "SUN AIR AVIATION"}, "SP": {"icao": "SAT", "name": "SATA"}, "SQ": {"icao": "SIA", "name": "SINGAPORE"}, "SR": {"icao": "SDR", "name": "SUNDAIR"}, "SS": {"icao": "CRL", "name": "CORSE AIR"}, "ST": {"icao": "RTL", "name": "AIR THANLWIN LIMITED"}, "SU": {"icao": "AFL", "name": "AEROFLOT"}, "SV": {"icao": "SVA", "name": "SAUDIARABI"}, "SW": {"icao": "NMB", "name": "AIR NAMIBIA"}, "SX": {"icao": "TOR", "name": "FLYGTA"}, "SY": {"icao": "SCX", "name": "SUN COUNTRY"}, "SZ": {"icao": "SMR", "name": "SOMON AIR"}, "T0": {"name": "AVIANCA PERU"}, "T1": {"name": "AVTRASOFT LIMITED"}, "T2": {}, "T3": {"icao": "EZE", "name": "EASTERN AIRWAYS"}, "T4": {}, "T5": {"icao": "TUA", "name": "TURKMENISTAN AIRLINES"}, "T6": {"icao": "ATX", "name": "AIRSWIFT"}, "T7": {"name": "TWIN JET"}, "T8": {}, "T9": {"icao": "VTU", "name": "TURPIAL AIRLINES"}, "TA": {"icao": "TAI", "name": "TACA"}, "TB": {"icao": "JAF", "name": "TUI FLY BELGIUM"}, "TC": {"icao": "ATC", "name": "AIR TANZANIA"}, "TD": {"icao": "TBC", "name": "AIRCOMPANY TBILISI"}, "TE": {}, "TF": {"icao": "BRX", "name": "BRA"}, "TG": {"icao": "THA", "name": "THAI"}, "TH": {}, "TI": {"name": "TROPIC OCEAN AIRWAYS"}, "TJ": {"icao": "GPD", "name": "TRADEWIND AVIATION"}, "TK": {"icao": "THY", "name": "TURKISH AIRLINES"}, "TL": {"icao": "ANO", "name": "AIRNORTH"}, "TM": {"icao": "LAM", "name": "LAM"}, "TN": {"icao": "THT", "name": "AIR TAHITI"}, "TO": {"icao": "TVF", "name": "TRANSAVIA FRANCE"}, "TP": {"icao": "TAP", "name": "TAP PORTUGAL"}, "TQ": {}, "TR": {"icao": "TGW", "name": "SCOOT"}, "TS": {"icao": "TSC", "name": "AIR TRANSAT"}, "TT": {"icao": "TGG", "name": "TIGER AIRWAYS AUSTRALIA"}, "TU": {"icao": "TAR", "name": "TUNIS AIR"}, "TV": {"name": "TIBET AIR"}, "TW": {"icao": "TWB", "name": "TWAY AIR"}, "TX": {"icao": "FWI", "name": "AIR CARAIBES"}, "TY": {"icao": "TPC", "name": "AIR CALEDONIE"}, "TZ": {"name": "TSARADIA"}, "U0": {}, "U1": {"name": "VIDECOM INTERNATIONAL"}, "U2": {"icao": "EZY", "name": "EASYJET"}, "U3": {}, "U4": {"name": "BUDDHA AIR"}, "U5": {"icao": "SEU", "name": "SKYUP MT"}, "U6": {"icao": "SVR", "name": "URAL AIRLINES"}, "U7": {}, "U8": {"icao": "CYF", "name": "TUS AIRWAYS"}, "U9": {}, "UA": {"icao": "UAL", "name": "UNITED AIRLINES"}, "UB": {"icao": "UBA", "name": "MYANMAR"}, "UC": {}, "UD": {"icao": "UBD", "name": "UBD"}, "UE": {"icao": "UJC", "name": "ULTIMATE AIR SHUTTLE"}, "UF": {"icao": "PER", "name": "PETROLEUM AIR SERVICES"}, "UG": {"icao": "TUX", "name": "TUNINTER"}, "UH": {"icao": "UJX", "name": "ATLASJET"}, "UI": {"icao": "AUK", "name": "AURIC AIR SERVICES"}, "UJ": {"icao": "LMU", "name": "AL MASRIA AIR"}, "UK": {"icao": "VTI", "name": "VISTARA"}, "UL": {"icao": "ALK", "name": "SRILANKAN AIR"}, "UM": {"icao": "AZW", "name": "AIR ZIMBABWE"}, "UN": {"icao": "NUA", "name": "UNITED NIGERIA AIRLINES"}, "UO": {"icao": "HKE", "name": "HK EXPRESS"}, "UP": {"icao": "BHS", "name": "BAHAMASAIR"}, "UQ": {"icao": "CUH", "name": "URUMQI AIRLINES"}, "UR": {"icao": "UGD", "name": "UGANDA AIRLINES"}, "US": {"name": "SILK AVIA"}, "UT": {"icao": "UTA", "name": "UTAIR AVIATION JSC"}, "UU": {"icao": "REU", "name": "AIR AUSTRAL"}, "UV": {}, "UW": {}, "UX": {"icao": "AEA", "name": "AIR EUROPA"}, "UY": {"icao": "CSG", "name": "AIR CAUCASUS"}, "UZ": {"icao": "BRQ", "name": "BURAQ AIR"}, "V0": {"icao": "VCV", "name": "CONVIASA"}, "V1": {}, "V2": {}, "V3": {"icao": "KRP", "name": "CARPATAIR"}, "V4": {"name": "VIEQUES AIR"}, "V5": {"icao": "DAP", "name": "AEROVIAS DAP"}, "V6": {}, "V7": {"icao": "VOE", "name": "VOLOTEA"}, "V8": {"icao": "IAR", "name": "ILIAMNA AIR"}, "V9": {"name": "VAN AIR EUROPE"}, "VA": {"icao": "VOZ", "name": "VIRGIN AUSTRALIA"}, "VB": {"icao": "VIV", "name": "VIVA AEROBUS"}, "VC": {"icao": "SRY", "name": "STERLING-ALEUTIAN AIRWAY"}, "VD": {}, "VE": {"icao": "EFY", "name": "CLIC AIR S.A."}, "VF": {"icao": "TKJ", "name": "AJET"}, "VG": {"name": "VIPPER.COM"}, "VH": {}, "VI": {}, "VJ": {"icao": "VJC", "name": "VIETJET AVIATION"}, "VK": {"name": "VALUEJET"}, "VL": {"icao": "LHX", "name": "CITY AIRLINES"}, "VM": {"icao": "NGL", "name": "MAX AIR LIMITED"}, "VN": {"icao": "HVN", "name": "VIETNAM AIRL"}, "VO": {"icao": "UVL", "name": "UNIVERSAL AIR CHARTER AN"}, "VP": {"icao": "VQI", "name": "VILLA AIR"}, "VQ": {"name": "NOVOAIR"}, "VR": {"icao": "TCV", "name": "T A C V"}, "VS": {"icao": "VIR", "name": "VIRGIN ATLANTIC"}, "VT": {"icao": "VTA", "name": "AIR TAHITI"}, "VU": {"icao": "VAG", "name": "VIETRAVEL AIRLINES"}, "VV": {"name": "Viva Peru"}, "VW": {"icao": "TAO", "name": "AEROMAR"}, "VX": {"icao": "VRD", "name": "VIRGIN AMERICA"}, "VY": {"icao": "VLG", "name": "VUELING AIRLINES"}, "VZ": {"icao": "TVJ", "name": "THAI VIETJET AIR"}, "W0": {}, "W1": {}, "W2": {"icao": "FXT", "name": "FLEXFLIGHT"}, "W3": {"icao": "ARA", "name": "ARIK AIR LIMITED"}, "W4": {"icao": "WMT", "name": "WIZZ AIR MALTA"}, "W5": {"icao": "IRM", "name": "MAHAN AIR"}, "W6": {"icao": "WZZ", "name": "WIZZ AIR HUNGARY"}, "W7": {"icao": "WMA", "name": "MAKERS AIR"}, "W8": {}, "W9": {"icao": "WUK", "name": "WIZZ AIR UK"}, "WA": {"icao": "KLC"}, "WB": {"icao": "RWD", "name": "RWANDAIR"}, "WC": {"icao": "WCT", "name": "MEREGRASS"}, "WD": {}, "WE": {"icao": "THD", "name": "THAI SMILE"}, "WF": {"icao": "WIF", "name": "WIDEROE"}, "WG": {"icao": "SWG", "name": "SUNWING AIRLINES INC."}, "WH": {"name": "WEST AFRICAN AIRLINES"}, "WI": {"icao": "WHT"}, "WJ": {"icao": "JES", "name": "JETSMART AIRLINES"}, "WK": {"icao": "EDW", "name": "EDELWEISS AIR"}, "WL": {}, "WM": {"icao": "WIA", "name": "WINDWARD ISLAND AIRWAYS"}, "WN": {"icao": "SWA", "name": "SW AIRLINES"}, "WO": {"icao": "WSW", "name": "SWOOP"}, "WP": {"icao": "WSG", "name": "WASAYA AIRWAYS"}, "WQ": {}, "WR": {"icao": "WEN", "name": "WESTJET ENCORE LTD"}, "WS": {"icao": "WJA", "name": "WESTJET"}, "WT": {"icao": "SWT", "name": "UEPFLY/SWIFTAIR"}, "WU": {"icao": "JFX", "name": "WESTERN AIR"}, "WV": {"icao": "WAA", "name": "FLY NAMIBIA"}, "WW": {"icao": "VNE", "name": "RAVSA"}, "WX": {"icao": "BCY", "name": "CITYJET"}, "WY": {"icao": "OMA", "name": "OMAN AIR"}, "WZ": {"icao": "RWZ"}, "X0": {}, "X1": {"name": "HAHN AIR TECHNOLOGIES"}, "X2": {}, "X3": {"icao": "TUI", "name": "TUIFLY"}, "X4": {"name": "AIR EXCURSIONS"}, "X5": {}, "X6": {"icao": "ATA", "name": "ARC"}, "X7": {}, "X8": {}, "X9": {"icao": "NVD", "name": "CITY STAR AIR"}, "XA": {}, "XB": {"name": "IATA"}, "XC": {"icao": "CAI", "name": "K D AIR"}, "XD": {}, "XE": {"icao": "BTA", "name": "JSX AIR"}, "XF": {"icao": "MGW", "name": "MONGOLIAN AIRWAYS CARGO"}, "XG": {"name": "UNDEFINED"}, "XH": {}, "XI": {}, "XJ": {"icao": "TAX", "name": "THAI AIRASIA X COMPANY"}, "XK": {"icao": "CCM", "name": "AIR CORSICA"}, "XL": {"icao": "LNE", "name": "LATAM AIRLINES ECUADOR"}, "XM": {"name": "Zimex Aviation Ltd"}, "XN": {"icao": "MXA", "name": "MEXICANA DE AVIACION"}, "XO": {"name": "SEAIR"}, "XP": {"icao": "CXP", "name": "AVELO AIRLINES"}, "XQ": {"icao": "SXS", "name": "SUNEXPRESS"}, "XR": {"icao": "CXI"}, "XS": {"icao": "SIT"}, "XT": {"icao": "CTU", "name": "LLC GLOBUS"}, "XU": {"icao": "AXK", "name": "AFRICAN EXPRESS AIRWAYS"}, "XV": {}, "XW": {}, "XX": {}, "XY": {"icao": "KNE", "name": "FLYNAS"}, "XZ": {"icao": "AEZ", "name": "AEROITALIA SRL"}, "Y0": {}, "Y1": {}, "Y2": {"icao": "CEY", "name": "AIR CENTURY"}, "Y3": {}, "Y4": {"icao": "VOI", "name": "VOLARIS"}, "Y5": {"name": "GOLDEN MYANMAR AIRLINES"}, "Y6": {"icao": "AYD", "name": "AB AVIATION"}, "Y7": {"icao": "TYA", "name": "NORDSTAR"}, "Y8": {"icao": "YZR", "name": "SUPARNA AIRLINES"}, "Y9": {"icao": "IRK", "name": "KISH AIRLINES"}, "YA": {}, "YB": {}, "YC": {"icao": "LLM", "name": "YAMAL AIRLINES"}, "YD": {}, "YE": {"name": "YAN AIR"}, "YF": {}, "YG": {}, "YH": {}, "YI": {"icao": "OYA", "name": "FLY OYA"}, "YJ": {"icao": "WUA", "name": "ASIAN WINGS"}, "YK": {"icao": "AVJ", "name": "AVIA TRAFFIC COMPANY"}, "YL": {"name": "LIBYAN WINGS AIRLINE"}, "YM": {"icao": "MGX", "name": "MONTENEGRO AL"}, "YN": {"icao": "CRQ", "name": "AIR CREEBEC"}, "YO": {"icao": "MCM", "name": "HELIAIRMONA"}, "YP": {"icao": "APZ", "name": "AIR PREMIA"}, "YQ": {"icao": "LCT", "name": "TAR"}, "YR": {"icao": "EGJ", "name": "SCENIC AIRLINES"}, "YS": {"icao": "FLZ", "name": "FLIGHTLINK"}, "YT": {"icao": "NYT", "name": "YETI AIRLINES"}, "YU": {"icao": "MMZ", "name": "EUROATLANTIC AIRWAYS"}, "YV": {"icao": "ASH", "name": "MESA AIRLINES"}, "YW": {"icao": "ANE", "name": "AIR NOSTRUM"}, "YX": {"icao": "RPA", "name": "REPUBLIC AIRWAYS"}, "YZ": {}, "Z0": {}, "Z1": {}, "Z2": {"icao": "APG", "name": "PHILIPPINES AIRASIA"}, "Z3": {}, "Z4": {}, "Z5": {}, "Z6": {}, "Z7": {"icao": "AUZ", "name": "AMASZONAS URUGUAY"}, "Z8": {"icao": "AZN", "name": "AMASZONAS S.A."}, "Z9": {}, "ZA": {"icao": "SWM", "name": "SKY ANGKOR AIRLINES"}, "ZB": {"name": "AIR ALBANIA"}, "ZC": {}, "ZD": {"icao": "EWR", "name": "EWA AIR"}, "ZE": {"icao": "ESR", "name": "EASTAR JET"}, "ZF": {"icao": "AZV", "name": "AZUR AIR"}, "ZG": {"icao": "TZP", "name": "ZIPAIR TOKYO"}, "ZH": {"icao": "CSZ", "name": "SHENZHEN AIRLINES"}, "ZI": {}, "ZJ": {}, "ZK": {}, "ZL": {"name": "REGIONAL EXPRESS"}, "ZM": {"icao": "MBB", "name": "AIR MANAS AIR COMPANY"}, "ZN": {"icao": "AZB", "name": "ZAMBIA AIRWAYS"}, "ZO": {}, "ZP": {"icao": "AZP", "name": "PARANAIR"}, "ZQ": {"icao": "GER", "name": "GERMAN AIRWAYS"}, "ZR": {}, "ZS": {}, "ZT": {"icao": "AWC", "name": "TITAN AIRWAYS"}, "ZU": {}, "ZV": {"icao": "RFD", "name": "AEROTRANSPORTES RAFILHER"}, "ZW": {"icao": "AWI", "name": "AIR WISCONSIN"}, "ZX": {"icao": "GGN", "name": "2746904 ONTARIO INC"}, "ZY": {"icao": "SHY", "name": "CHINA AIR CARGO"}, "ZZ": {}}], "Get Carrier Codes": [{"data": "{\"meta\":{\"count\":1189,\"links\":{\"self\":\"https://test.api.amadeus.com/v1/reference-data/airlines\"}},\"data\":[{\"type\":\"airline\",\"iataCode\":\"8B\",\"businessName\":\"TRANSNUSA AVIATION\",\"commonName\":\"TRANSNUSA AVIATION\"},{\"type\":\"airline\",\"iataCode\":\"8C\",\"businessName\":\"UNDEFINED\",\"commonName\":\"EAST STAR\"},{\"type\":\"airline\",\"iataCode\":\"8D\",\"icaoCode\":\"EXV\",\"businessName\":\"FITS AVIATION (PVT) LTD\",\"commonName\":\"FITS AVIATION  PVT  LTD\"},{\"type\":\"airline\",\"iataCode\":\"8E\",\"icaoCode\":\"BRG\",\"businessName\":\"BERING AIR\",\"commonName\":\"BERING AIR\"},{\"type\":\"airline\",\"iataCode\":\"8F\",\"icaoCode\":\"STP\",\"businessName\":\"STP AIRWAYS\",\"commonName\":\"STP AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"8G\",\"icaoCode\":\"DTL\",\"businessName\":\"AERO DILI\",\"commonName\":\"GIRJET\"},{\"type\":\"airline\",\"iataCode\":\"8H\",\"businessName\":\"BH AIR\",\"commonName\":\"BH AIR\"},{\"type\":\"airline\",\"iataCode\":\"8I\",\"icaoCode\":\"LIP\",\"businessName\":\"LIPICAN AER\",\"commonName\":\"LIPICAN AER\"},{\"type\":\"airline\",\"iataCode\":\"8J\",\"businessName\":\"LINEA AEREA ECO JET\"},{\"type\":\"airline\",\"iataCode\":\"8K\",\"businessName\":\"EXPLOITS VALLEY AIR SERVICES\"},{\"type\":\"airline\",\"iataCode\":\"8L\",\"icaoCode\":\"LKE\",\"businessName\":\"LUCKY AIR\"},{\"type\":\"airline\",\"iataCode\":\"8M\",\"icaoCode\":\"MMA\",\"businessName\":\"MYANMAR AIRWAYS INTL\",\"commonName\":\"MYANMAR AIRWAYS INTL\"},{\"type\":\"airline\",\"iataCode\":\"8N\",\"icaoCode\":\"REG\",\"businessName\":\"REGIONAL AIR SERVICES\",\"commonName\":\"REGIONAL AIR SERVICES\"},{\"type\":\"airline\",\"iataCode\":\"8O\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"8P\",\"icaoCode\":\"PCO\",\"businessName\":\"PACIFIC COASTAL AIRLINES\",\"commonName\":\"PAC.COASTAL\"},{\"type\":\"airline\",\"iataCode\":\"8Q\",\"icaoCode\":\"OHY\",\"businessName\":\"ONUR AIR\",\"commonName\":\"ONUR AIR\"},{\"type\":\"airline\",\"iataCode\":\"8R\",\"icaoCode\":\"AIA\",\"businessName\":\"Amelia\",\"commonName\":\"AMELIA\"},{\"type\":\"airline\",\"iataCode\":\"8S\",\"businessName\":\"SHUN TAK-CHINA TRAVEL\",\"commonName\":\"SHUN TAK\"},{\"type\":\"airline\",\"iataCode\":\"8T\",\"businessName\":\"AIR TINDI LTD\",\"commonName\":\"AIR TINDI LTD\"},{\"type\":\"airline\",\"iataCode\":\"8U\",\"icaoCode\":\"AAW\",\"businessName\":\"AFRIQIYAH AIRWAYS\",\"commonName\":\"AFRIQIYAH AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"8V\",\"businessName\":\"WRIGHT AIR SERVICES\",\"commonName\":\"WRIGHT AIR SERVICES\"},{\"type\":\"airline\",\"iataCode\":\"8W\",\"icaoCode\":\"EDR\",\"businessName\":\"FLY ALWAYS\",\"commonName\":\"FLY ALWAYS\"},{\"type\":\"airline\",\"iataCode\":\"8X\",\"icaoCode\":\"XYD\",\"businessName\":\"AMADEUS EIGHT\",\"commonName\":\"AMADEUS EIGHT\"},{\"type\":\"airline\",\"iataCode\":\"8Y\",\"icaoCode\":\"AAV\",\"businessName\":\"ASTRO AIR INTERNATIONAL\",\"commonName\":\"ASTRO AIR INTERNATIONAL\"},{\"type\":\"airline\",\"iataCode\":\"8Z\",\"icaoCode\":\"CGA\",\"businessName\":\"CONGO AIRWAYS\",\"commonName\":\"CONGO AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"9A\",\"businessName\":\"GRAN COLOMBIA DE AVIACION\",\"commonName\":\"GRAN COLOMBIA DE AV\"},{\"type\":\"airline\",\"iataCode\":\"9B\",\"businessName\":\"ACCESRAIL\",\"commonName\":\"ACCESRAIL\"},{\"type\":\"airline\",\"iataCode\":\"9C\",\"businessName\":\"SPRING AIRLINES\",\"commonName\":\"SPRING AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"9D\",\"businessName\":\"GENGHIS KHAN AIRLINES\",\"commonName\":\"GENGHIS KHAN AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"9E\",\"icaoCode\":\"FLG\",\"businessName\":\"ENDEAVOR AIR\",\"commonName\":\"ENDEAVOR AIR\"},{\"type\":\"airline\",\"iataCode\":\"9F\",\"businessName\":\"EUROSTAR\",\"commonName\":\"EUROSTAR UK\"},{\"type\":\"airline\",\"iataCode\":\"9G\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"9H\",\"businessName\":\"AIR CHANGAN\",\"commonName\":\"AIR CHANGAN\"},{\"type\":\"airline\",\"iataCode\":\"9I\",\"icaoCode\":\"LLR\",\"businessName\":\"ALLIANCE AIR\",\"commonName\":\"ALLIANCE AIR\"},{\"type\":\"airline\",\"iataCode\":\"9J\",\"icaoCode\":\"DAN\",\"businessName\":\"DANA AIRLINES LIMITED\"},{\"type\":\"airline\",\"iataCode\":\"9K\",\"icaoCode\":\"KAP\",\"businessName\":\"CAPE AIR\",\"commonName\":\"CAPE AIR\"},{\"type\":\"airline\",\"iataCode\":\"9L\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"9M\",\"icaoCode\":\"GLR\",\"businessName\":\"CENTRAL MOUNTAIN AIR\",\"commonName\":\"CENTRAL MOUNTAIN AIR\"},{\"type\":\"airline\",\"iataCode\":\"9O\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"9P\",\"businessName\":\"FLY JINNAH\",\"commonName\":\"FLY JINNAH\"},{\"type\":\"airline\",\"iataCode\":\"9Q\",\"icaoCode\":\"CXE\",\"businessName\":\"CAICOS EXPRESS AIRWAYS\",\"commonName\":\"CAICOS EXPRESS AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"9R\",\"icaoCode\":\"NSE\",\"businessName\":\"SATENA\",\"commonName\":\"SATENA\"},{\"type\":\"airline\",\"iataCode\":\"9S\",\"icaoCode\":\"XYX\",\"businessName\":\"AMADEUS PDF 9S\",\"commonName\":\"AMADEUS  9S\"},{\"type\":\"airline\",\"iataCode\":\"9T\",\"icaoCode\":\"AST\",\"businessName\":\"THAI SUMMER AIRWAYS\",\"commonName\":\"THAI SUMMER AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"9U\",\"icaoCode\":\"MLD\",\"businessName\":\"AIR MOLDOVA\",\"commonName\":\"AIR MOLDOVA\"},{\"type\":\"airline\",\"iataCode\":\"9V\",\"icaoCode\":\"ROI\",\"businessName\":\"AVIOR AIRLINES\",\"commonName\":\"AVIOR AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"9W\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"9X\",\"icaoCode\":\"FDY\",\"businessName\":\"SOUTHERN AIRWAYS EXPRESS\"},{\"type\":\"airline\",\"iataCode\":\"9Y\",\"businessName\":\"NATIONAL AIRWAYS\",\"commonName\":\"NATIONAL AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"9Z\",\"businessName\":\"AMADEUS PDF 9Z\"},{\"type\":\"airline\",\"iataCode\":\"A0\",\"icaoCode\":\"EFW\",\"businessName\":\"BA EUROFLYER\",\"commonName\":\"BA EUROFLYER\"},{\"type\":\"airline\",\"iataCode\":\"A1\",\"businessName\":\"A.P.G. DISTRIBUTION SYSTEM\",\"commonName\":\"A.P.G. DISTRIBUTION SYST\"},{\"type\":\"airline\",\"iataCode\":\"A2\",\"icaoCode\":\"AWG\",\"businessName\":\"ANIMA WINGS\",\"commonName\":\"ANIMA WINGS\"},{\"type\":\"airline\",\"iataCode\":\"A3\",\"icaoCode\":\"AEE\",\"businessName\":\"AEGEAN AIRLINES\",\"commonName\":\"AEGEAN AIR\"},{\"type\":\"airline\",\"iataCode\":\"A5\",\"icaoCode\":\"HOP\",\"businessName\":\"HOP\",\"commonName\":\"AIRLINAIR\"},{\"type\":\"airline\",\"iataCode\":\"A6\",\"icaoCode\":\"HTU\",\"businessName\":\"AIR TRAVEL\",\"commonName\":\"AIR TRAVEL\"},{\"type\":\"airline\",\"iataCode\":\"A7\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"A8\",\"icaoCode\":\"XAU\",\"businessName\":\"AEROLINK UGANDA LIMITED\",\"commonName\":\"AEROLINK UGANDA LIMITED\"},{\"type\":\"airline\",\"iataCode\":\"A9\",\"icaoCode\":\"TGZ\",\"businessName\":\"GEORGIAN AIRWAYS\",\"commonName\":\"GEORGIAN AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"AA\",\"icaoCode\":\"AAL\",\"businessName\":\"AMERICAN AIRLINES\",\"commonName\":\"AMERICAN AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"AB\",\"businessName\":\"BONZA AVIATION\"},{\"type\":\"airline\",\"iataCode\":\"AC\",\"icaoCode\":\"ACA\",\"businessName\":\"AIR CANADA\",\"commonName\":\"AIR CANADA\"},{\"type\":\"airline\",\"iataCode\":\"AD\",\"icaoCode\":\"AZU\",\"businessName\":\"AZUL LINHAS AEREAS BRASILEIRAS\",\"commonName\":\"AZUL LINHAS\"},{\"type\":\"airline\",\"iataCode\":\"AE\",\"icaoCode\":\"MDA\",\"businessName\":\"MANDARIN AIRLINES\",\"commonName\":\"MANDARIN AIR\"},{\"type\":\"airline\",\"iataCode\":\"AF\",\"icaoCode\":\"AFR\",\"businessName\":\"AIR FRANCE\",\"commonName\":\"AIR FRANCE\"},{\"type\":\"airline\",\"iataCode\":\"AG\",\"icaoCode\":\"ARU\",\"businessName\":\"ARUBA AIRLINES\",\"commonName\":\"ARUBA AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"AH\",\"icaoCode\":\"DAH\",\"businessName\":\"AIR ALGERIE\",\"commonName\":\"AIR ALGERIE\"},{\"type\":\"airline\",\"iataCode\":\"AI\",\"icaoCode\":\"AIC\",\"businessName\":\"AIR INDIA\",\"commonName\":\"AIR INDIA\"},{\"type\":\"airline\",\"iataCode\":\"AJ\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"AK\",\"icaoCode\":\"AXM\",\"businessName\":\"AIRASIA SDN BHD\",\"commonName\":\"AIRASIA SDN BHD\"},{\"type\":\"airline\",\"iataCode\":\"AL\",\"icaoCode\":\"APP\",\"businessName\":\"ALPAVIA\",\"commonName\":\"ALPAVIA\"},{\"type\":\"airline\",\"iataCode\":\"AM\",\"icaoCode\":\"AMX\",\"businessName\":\"AEROMEXICO\",\"commonName\":\"AEROMEXICO\"},{\"type\":\"airline\",\"iataCode\":\"AN\",\"businessName\":\"ADVANCED AIR\",\"commonName\":\"ADVANCED AIR\"},{\"type\":\"airline\",\"iataCode\":\"AO\",\"businessName\":\"AVIANOVA LCC\",\"commonName\":\"AIR JUAN AVIATION\"},{\"type\":\"airline\",\"iataCode\":\"AP\",\"icaoCode\":\"LAV\",\"businessName\":\"ALBA STAR\"},{\"type\":\"airline\",\"iataCode\":\"AQ\",\"businessName\":\"9 AIR\",\"commonName\":\"9 AIR\"},{\"type\":\"airline\",\"iataCode\":\"AR\",\"icaoCode\":\"ARG\",\"businessName\":\"AEROLINEAS ARGENTINAS\",\"commonName\":\"AEROLINEAS ARGENTINAS\"},{\"type\":\"airline\",\"iataCode\":\"AS\",\"icaoCode\":\"ASA\",\"businessName\":\"ALASKA AIRLINES\",\"commonName\":\"ALASKA AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"CS\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"0I\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"0J\",\"icaoCode\":\"PJZ\",\"businessName\":\"PREMIUM JET AG\",\"commonName\":\"PREMIUM JET AG\"},{\"type\":\"airline\",\"iataCode\":\"0K\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"0L\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"0M\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"FS\",\"icaoCode\":\"FOX\",\"businessName\":\"FLYR\",\"commonName\":\"FLYR AS\"},{\"type\":\"airline\",\"iataCode\":\"FT\",\"businessName\":\"FLYEGYPT\",\"commonName\":\"FLYEGYPT\"},{\"type\":\"airline\",\"iataCode\":\"FU\",\"businessName\":\"FUZHOU AIRLINES\",\"commonName\":\"FUZHOU AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"K5\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"K6\",\"icaoCode\":\"KHV\",\"businessName\":\"CAMBODIA ANGKOR AIR\",\"commonName\":\"CAMBODIA ANGKOR AIR\"},{\"type\":\"airline\",\"iataCode\":\"0R\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"0T\",\"icaoCode\":\"TTL\",\"businessName\":\"TOTAL LINHAS AEREAS S/A\"},{\"type\":\"airline\",\"iataCode\":\"0U\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"CY\",\"icaoCode\":\"CYP\",\"businessName\":\"CYPRUS AIRWAYS\",\"commonName\":\"CYPRUS AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"QV\",\"icaoCode\":\"LAO\",\"businessName\":\"LAO AIRLINES\",\"commonName\":\"LAO AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"1A\",\"icaoCode\":\"AGT\",\"businessName\":\"AMADEUS\",\"commonName\":\"AMADEUS\"},{\"type\":\"airline\",\"iataCode\":\"1B\",\"businessName\":\"ABACUS\",\"commonName\":\"ABACUS\"},{\"type\":\"airline\",\"iataCode\":\"1C\",\"businessName\":\"EDS INFORMATION BUSINESS\",\"commonName\":\"GEMINI\"},{\"type\":\"airline\",\"iataCode\":\"KF\",\"icaoCode\":\"ABB\",\"businessName\":\"AIR BELGIUM\",\"commonName\":\"AIR BELGIUM\"},{\"type\":\"airline\",\"iataCode\":\"KG\",\"icaoCode\":\"LYM\",\"businessName\":\"KEY LIME AIR CORPORATION\",\"commonName\":\"KEY LIME AIR CORPORATION\"},{\"type\":\"airline\",\"iataCode\":\"G2\",\"icaoCode\":\"TJJ\",\"businessName\":\"GULLIVAIR\",\"commonName\":\"GULLIVAIR\"},{\"type\":\"airline\",\"iataCode\":\"RB\",\"icaoCode\":\"SYR\",\"businessName\":\"SYRIAN ARAB AIRLINES\",\"commonName\":\"SYRIAN ARAB AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"RO\",\"icaoCode\":\"ROT\",\"businessName\":\"TAROM\",\"commonName\":\"TAROM\"},{\"type\":\"airline\",\"iataCode\":\"7S\",\"icaoCode\":\"XYW\",\"businessName\":\"AMADEUS PDF 7S\",\"commonName\":\"AMADEUS PDF 7S\"},{\"type\":\"airline\",\"iataCode\":\"W4\",\"icaoCode\":\"WMT\",\"businessName\":\"WIZZ AIR MALTA\",\"commonName\":\"WIZZ AIR MALTA\"},{\"type\":\"airline\",\"iataCode\":\"D7\",\"icaoCode\":\"XAX\",\"businessName\":\"AIRASIAX SDN BHD\",\"commonName\":\"AIRASIAX SDN BHD\"},{\"type\":\"airline\",\"iataCode\":\"RH\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"D9\",\"icaoCode\":\"DMQ\",\"businessName\":\"DAALLO AIRLINES(SOMALIA)\",\"commonName\":\"DAALLO AIRLINES SOMALIA\"},{\"type\":\"airline\",\"iataCode\":\"GB\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"M5\",\"icaoCode\":\"KEN\",\"businessName\":\"KENMORE AIR\",\"commonName\":\"KENMORE AIR\"},{\"type\":\"airline\",\"iataCode\":\"GC\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"RV\",\"icaoCode\":\"ROU\",\"businessName\":\"AIR CANADA ROUGE\",\"commonName\":\"AIR CANADA ROUGE\"},{\"type\":\"airline\",\"iataCode\":\"7W\",\"icaoCode\":\"WRC\",\"businessName\":\"WIND ROSE AVIATION\",\"commonName\":\"WIND ROSE AVIATION\"},{\"type\":\"airline\",\"iataCode\":\"GH\",\"icaoCode\":\"GHA\",\"businessName\":\"GLOBUS LLC\",\"commonName\":\"GHANA AIR\"},{\"type\":\"airline\",\"iataCode\":\"GI\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"GJ\",\"icaoCode\":\"CDC\",\"businessName\":\"ZHEJIANG LOONG AIRLINES\",\"commonName\":\"ZHEJIANG LOONG AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"GK\",\"icaoCode\":\"JJP\",\"businessName\":\"JETSTAR JAPAN\",\"commonName\":\"JETSTAR JAPAN\"},{\"type\":\"airline\",\"iataCode\":\"GL\",\"icaoCode\":\"GRL\",\"businessName\":\"AIR GREENLAND\",\"commonName\":\"AIR GREENLAND\"},{\"type\":\"airline\",\"iataCode\":\"GM\",\"icaoCode\":\"GSW\",\"businessName\":\"CHAIR AIRLINES\",\"commonName\":\"CHAIR AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"GN\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"GO\",\"icaoCode\":\"GHN\",\"businessName\":\"AIR GHANA LIMITED\",\"commonName\":\"AIR GHANA LIMITED\"},{\"type\":\"airline\",\"iataCode\":\"GP\",\"icaoCode\":\"RIV\",\"businessName\":\"APG AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"GQ\",\"icaoCode\":\"SEH\",\"businessName\":\"SKY EXPRESS\",\"commonName\":\"SKY EXPRESS\"},{\"type\":\"airline\",\"iataCode\":\"GR\",\"icaoCode\":\"AUR\",\"businessName\":\"AURIGNY AIR SERVICES\",\"commonName\":\"AURIGNY AIR SERVICES\"},{\"type\":\"airline\",\"iataCode\":\"GS\",\"businessName\":\"TIANJIN AIRLINES\",\"commonName\":\"TIANJIN AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"GT\",\"businessName\":\"AIR GUILIN\",\"commonName\":\"AIR GUILIN\"},{\"type\":\"airline\",\"iataCode\":\"GU\",\"icaoCode\":\"GUG\",\"businessName\":\"AVIATECA\",\"commonName\":\"AVIATECA\"},{\"type\":\"airline\",\"iataCode\":\"GV\",\"icaoCode\":\"GUN\",\"businessName\":\"GRANT AVIATION\",\"commonName\":\"GRANT AVIATION\"},{\"type\":\"airline\",\"iataCode\":\"GW\",\"businessName\":\"COSTA RICA GREEN AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"GX\",\"businessName\":\"GX AIRLINES\",\"commonName\":\"GX AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"GZ\",\"businessName\":\"AIR RAROTONGA\",\"commonName\":\"AIR RAROTONGA\"},{\"type\":\"airline\",\"iataCode\":\"H0\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"H1\",\"businessName\":\"HAHN AIR SYSTEMS\",\"commonName\":\"HAHN AIR SYSTEMS\"},{\"type\":\"airline\",\"iataCode\":\"H2\",\"icaoCode\":\"SKU\",\"businessName\":\"SKY AIRLINE\",\"commonName\":\"SKY AIRLINE\"},{\"type\":\"airline\",\"iataCode\":\"H3\",\"icaoCode\":\"HLJ\",\"businessName\":\"HELLO JETS S.R.L\",\"commonName\":\"HARBOUR AIR\"},{\"type\":\"airline\",\"iataCode\":\"H4\",\"icaoCode\":\"HYS\",\"businessName\":\"HISKY EUROPE SRL\",\"commonName\":\"HISKY EUROPE SRL\"},{\"type\":\"airline\",\"iataCode\":\"H5\",\"icaoCode\":\"OMT\",\"businessName\":\"CM AIRLINES\",\"commonName\":\"CM AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"H6\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"H7\",\"icaoCode\":\"HYM\",\"businessName\":\"HISKY\",\"commonName\":\"HISKY\"},{\"type\":\"airline\",\"iataCode\":\"H8\",\"businessName\":\"SKY AIRLINE PERU\",\"commonName\":\"SKY AIRLINE PERU\"},{\"type\":\"airline\",\"iataCode\":\"H9\",\"icaoCode\":\"HIM\",\"businessName\":\"HIMALAYA AIRLINES\",\"commonName\":\"HIMALAYA AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"HA\",\"icaoCode\":\"HAL\",\"businessName\":\"HAWAIIAN AIRLINES\",\"commonName\":\"HAWAIIAN AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"HB\",\"icaoCode\":\"HGB\",\"businessName\":\"GREATER BAY AIRLINES\",\"commonName\":\"GREATER BAY AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"HC\",\"icaoCode\":\"SZN\",\"businessName\":\"AIR SENEGAL\",\"commonName\":\"AIR SENEGAL\"},{\"type\":\"airline\",\"iataCode\":\"HD\",\"icaoCode\":\"ADO\",\"businessName\":\"AIRDO\",\"commonName\":\"AIRDO\"},{\"type\":\"airline\",\"iataCode\":\"HE\",\"businessName\":\"BAR AVIATION LIMITED\",\"commonName\":\"BAR AVIATION LIMITED\"},{\"type\":\"airline\",\"iataCode\":\"HF\",\"icaoCode\":\"VRE\",\"businessName\":\"AIR COTE D IVOIRE\",\"commonName\":\"AIRCOTEIVOIRE\"},{\"type\":\"airline\",\"iataCode\":\"HG\",\"icaoCode\":\"HTP\",\"businessName\":\"HALA AIR\",\"commonName\":\"HALA AIR\"},{\"type\":\"airline\",\"iataCode\":\"HH\",\"icaoCode\":\"QNT\",\"businessName\":\"QANOT SHARQ\",\"commonName\":\"QANOT SHARQ\"},{\"type\":\"airline\",\"iataCode\":\"HI\",\"businessName\":\"PAPILLON AIRWAYS\",\"commonName\":\"PAPILLON AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"HJ\",\"icaoCode\":\"HEJ\",\"businessName\":\"HELLAS JET\",\"commonName\":\"HELLENIC STAR\"},{\"type\":\"airline\",\"iataCode\":\"HK\",\"businessName\":\"SKIPPERS AVIATION PTY\",\"commonName\":\"SKIPPERS AVIATION PTY\"},{\"type\":\"airline\",\"iataCode\":\"HL\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"HM\",\"icaoCode\":\"SEY\",\"businessName\":\"AIR SEYCHELLES\",\"commonName\":\"AIR SEYCHELLES\"},{\"type\":\"airline\",\"iataCode\":\"HN\",\"icaoCode\":\"EQX\",\"businessName\":\"EQUINOXAIR\",\"commonName\":\"EQUINOXAIR\"},{\"type\":\"airline\",\"iataCode\":\"HO\",\"icaoCode\":\"DKH\",\"businessName\":\"JUNEYAO AIRLINES\",\"commonName\":\"JUNEYAO AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"HP\",\"businessName\":\"POPULAIR\",\"commonName\":\"POPULAIR\"},{\"type\":\"airline\",\"iataCode\":\"HQ\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"HR\",\"icaoCode\":\"HHN\",\"businessName\":\"HAHN AIR\",\"commonName\":\"HAHN AIR\"},{\"type\":\"airline\",\"iataCode\":\"HS\",\"businessName\":\"HELI SECURITE\",\"commonName\":\"HELI SECURITE\"},{\"type\":\"airline\",\"iataCode\":\"HT\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"HU\",\"icaoCode\":\"CHH\",\"businessName\":\"HAINAN AIRLINES\",\"commonName\":\"HAINAN AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"HV\",\"icaoCode\":\"TRA\",\"businessName\":\"TRANSAVIA AIRLINES\",\"commonName\":\"TRANSAVIA AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"HW\",\"businessName\":\"NORTH WRIGHT AIR\",\"commonName\":\"NORTH WRIGHT AIR\"},{\"type\":\"airline\",\"iataCode\":\"HX\",\"icaoCode\":\"CRK\",\"businessName\":\"HONG KONG AIRLINES\",\"commonName\":\"HONG KONG AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"HY\",\"icaoCode\":\"UZB\",\"businessName\":\"UZBEKISTAN AIRWAYS\",\"commonName\":\"UZBEKISTAN AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"HZ\",\"icaoCode\":\"SHU\",\"businessName\":\"AURORA AIRLINES\",\"commonName\":\"AURORA AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"I0\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"I1\",\"businessName\":\"CTS VIAGGI\",\"commonName\":\"CTS VIAGGI\"},{\"type\":\"airline\",\"iataCode\":\"I2\",\"businessName\":\"IBERIA EXPRESS\",\"commonName\":\"IBERIA EXPRESS\"},{\"type\":\"airline\",\"iataCode\":\"I3\",\"businessName\":\"ATA AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"I4\",\"businessName\":\"ISLAND AIR EXPRESS\",\"commonName\":\"ISLAND AIR EXPRESS\"},{\"type\":\"airline\",\"iataCode\":\"I5\",\"icaoCode\":\"IAD\",\"businessName\":\"AIR INDIA EXPRESS\",\"commonName\":\"AIR INDIA EXPRESS\"},{\"type\":\"airline\",\"iataCode\":\"I6\",\"businessName\":\"IRYO\",\"commonName\":\"IRYO\"},{\"type\":\"airline\",\"iataCode\":\"I7\",\"businessName\":\"INDIAONE AIR\",\"commonName\":\"INDIAONE AIR\"},{\"type\":\"airline\",\"iataCode\":\"I8\",\"icaoCode\":\"IZA\",\"businessName\":\"IZHAVIA\",\"commonName\":\"IZHAVIA\"},{\"type\":\"airline\",\"iataCode\":\"I9\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"IA\",\"icaoCode\":\"IAW\",\"businessName\":\"Iraqi Airways\",\"commonName\":\"I A W\"},{\"type\":\"airline\",\"iataCode\":\"IB\",\"icaoCode\":\"IBE\",\"businessName\":\"IBERIA\",\"commonName\":\"IBERIA\"},{\"type\":\"airline\",\"iataCode\":\"IC\",\"businessName\":\"FLY91\",\"commonName\":\"FLY91\"},{\"type\":\"airline\",\"iataCode\":\"ID\",\"icaoCode\":\"BTK\",\"businessName\":\"BATIK AIR INDONESIA\",\"commonName\":\"BATIK AIR INDONESIA\"},{\"type\":\"airline\",\"iataCode\":\"IE\",\"icaoCode\":\"SOL\",\"businessName\":\"SOLOMON AIRLINES\",\"commonName\":\"SOLOMON AIR\"},{\"type\":\"airline\",\"iataCode\":\"IF\",\"icaoCode\":\"FBA\",\"businessName\":\"FBA\",\"commonName\":\"FBA\"},{\"type\":\"airline\",\"iataCode\":\"IG\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"IH\",\"icaoCode\":\"SRS\",\"businessName\":\"SOUTHERN SKY AIRLINES JSC\",\"commonName\":\"SOUTHERN SKY AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"IJ\",\"businessName\":\"SPRING JAPAN\",\"commonName\":\"SPRING JAPAN\"},{\"type\":\"airline\",\"iataCode\":\"IL\",\"businessName\":\"PT.TRIGANA AIR SERVICE\",\"commonName\":\"PT.TRIGANA AIR SERVICE\"},{\"type\":\"airline\",\"iataCode\":\"IM\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"OQ\",\"businessName\":\"CHONGQING AIRLINES\",\"commonName\":\"CHONGQING AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"OR\",\"icaoCode\":\"TFL\",\"businessName\":\"TUIFLY NETHERLANDS\",\"commonName\":\"TUI FLY NETHERLANDS\"},{\"type\":\"airline\",\"iataCode\":\"0A\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"0B\",\"icaoCode\":\"BLA\",\"businessName\":\"BLUE AIR AVIATION\",\"commonName\":\"BLUE AIR AVIATION\"},{\"type\":\"airline\",\"iataCode\":\"0C\",\"businessName\":\"COBRA AVIATION\"},{\"type\":\"airline\",\"iataCode\":\"0D\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"0E\",\"icaoCode\":\"XYS\",\"businessName\":\"AMADEUS PRESALES 0E\"},{\"type\":\"airline\",\"iataCode\":\"0F\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"0G\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"0H\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"AT\",\"icaoCode\":\"RAM\",\"businessName\":\"ROYAL AIR MAROC\",\"commonName\":\"R.AIR MAROC\"},{\"type\":\"airline\",\"iataCode\":\"0O\",\"businessName\":\"STA TRAVEL\",\"commonName\":\"STA TRAVEL\"},{\"type\":\"airline\",\"iataCode\":\"0P\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"0Q\",\"businessName\":\"AMADEUS AIRLINE\"},{\"type\":\"airline\",\"iataCode\":\"ZG\",\"icaoCode\":\"TZP\",\"businessName\":\"ZIPAIR TOKYO\",\"commonName\":\"ZIPAIR TOKYO\"},{\"type\":\"airline\",\"iataCode\":\"7B\",\"icaoCode\":\"UBE\",\"businessName\":\"BEES AIRLINE LLC\",\"commonName\":\"BEES AIRLINE LLC\"},{\"type\":\"airline\",\"iataCode\":\"7C\",\"icaoCode\":\"JJA\",\"businessName\":\"JEJU AIR\",\"commonName\":\"JEJU AIR\"},{\"type\":\"airline\",\"iataCode\":\"7D\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"7E\",\"icaoCode\":\"AWU\",\"businessName\":\"SYLT AIR GMBH\",\"commonName\":\"SYLT AIR GMBH\"},{\"type\":\"airline\",\"iataCode\":\"0V\",\"businessName\":\"VIETNAM AIR SERVICE\",\"commonName\":\"VIETNAM AIR SERVICE\"},{\"type\":\"airline\",\"iataCode\":\"0W\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"0X\",\"icaoCode\":\"XYT\",\"businessName\":\"AMADEUS AIRLINE 0X\"},{\"type\":\"airline\",\"iataCode\":\"0Z\",\"icaoCode\":\"XYR\",\"businessName\":\"AMADEUS 0Z\"},{\"type\":\"airline\",\"iataCode\":\"7I\",\"icaoCode\":\"TLR\",\"businessName\":\"AIR LIBYA\",\"commonName\":\"AIR LIBYA\"},{\"type\":\"airline\",\"iataCode\":\"7J\",\"icaoCode\":\"TJK\",\"businessName\":\"TAJIK AIR\",\"commonName\":\"TAJIK AIR\"},{\"type\":\"airline\",\"iataCode\":\"DI\",\"icaoCode\":\"MBU\",\"businessName\":\"MARABU AIRLINES\",\"commonName\":\"MARABU AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"DJ\",\"icaoCode\":\"DJI\",\"businessName\":\"AIR DJIBOUTI\",\"commonName\":\"AIR DJIBOUTI\"},{\"type\":\"airline\",\"iataCode\":\"DK\",\"icaoCode\":\"VKG\",\"businessName\":\"SUNCLASS AIRLINES\",\"commonName\":\"SUNCLASS AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"DL\",\"icaoCode\":\"DAL\",\"businessName\":\"DELTA AIR LINES\",\"commonName\":\"DELTA AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"DM\",\"icaoCode\":\"DWI\",\"businessName\":\"ARAJET\",\"commonName\":\"ARAJET\"},{\"type\":\"airline\",\"iataCode\":\"DN\",\"businessName\":\"DAN AIR (AOC) S.R.L\",\"commonName\":\"DAN AIR\"},{\"type\":\"airline\",\"iataCode\":\"DO\",\"businessName\":\"SKY HIGH\",\"commonName\":\"SKY HIGH\"},{\"type\":\"airline\",\"iataCode\":\"DP\",\"icaoCode\":\"PBD\",\"businessName\":\"POBEDA AIRLINES\",\"commonName\":\"POBEDA AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"DQ\",\"icaoCode\":\"KHH\",\"businessName\":\"ALEXANDRIA AIRLINES\",\"commonName\":\"ALEXANDRIA AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"DR\",\"businessName\":\"RUILI AIRLINES\",\"commonName\":\"RUILI AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"DS\",\"icaoCode\":\"EZS\",\"businessName\":\"EASYJET SWITZERLAND\",\"commonName\":\"EASYJET SWITZERLAND\"},{\"type\":\"airline\",\"iataCode\":\"DT\",\"icaoCode\":\"DTA\",\"businessName\":\"TAAG Angola Airlines\",\"commonName\":\"TAAG Linhas Aereas\"},{\"type\":\"airline\",\"iataCode\":\"DV\",\"icaoCode\":\"VSV\",\"businessName\":\"JSC AIRCOMPANY SCAT\",\"commonName\":\"JSC AIRCOMPANY SCAT\"},{\"type\":\"airline\",\"iataCode\":\"DW\",\"businessName\":\"GREAT DANE AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"DX\",\"icaoCode\":\"DTR\",\"businessName\":\"DAT\",\"commonName\":\"DAT\"},{\"type\":\"airline\",\"iataCode\":\"DY\",\"icaoCode\":\"NOZ\",\"businessName\":\"Norwegian Air Shuttle AOC AS\",\"commonName\":\"NORWEGIAN AIR\"},{\"type\":\"airline\",\"iataCode\":\"DZ\",\"businessName\":\"DONGHAI AIRLINES\",\"commonName\":\"DONGHAI AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"E1\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"E2\",\"businessName\":\"EUROWINGS EUROPE GMBH\",\"commonName\":\"EUROWINGS EUROPE\"},{\"type\":\"airline\",\"iataCode\":\"E3\",\"icaoCode\":\"EGW\",\"businessName\":\"Ego Airways\",\"commonName\":\"EGO AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"E5\",\"icaoCode\":\"RBG\",\"businessName\":\"AIR ARABIA EGYPT\",\"commonName\":\"AIR ARABIA EGYPT\"},{\"type\":\"airline\",\"iataCode\":\"E6\",\"icaoCode\":\"EWL\",\"businessName\":\"EUROWINGS EUROPE LTD\",\"commonName\":\"EUROWINGS EUROPE\"},{\"type\":\"airline\",\"iataCode\":\"E7\",\"businessName\":\"EQUAFLIGHT SERVICE\",\"commonName\":\"EQUAFLIGHT SERVICE\"},{\"type\":\"airline\",\"iataCode\":\"E9\",\"icaoCode\":\"EVE\",\"businessName\":\"IBEROJET AIRLINES\",\"commonName\":\"IBEROJET AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"EA\",\"icaoCode\":\"EHN\",\"businessName\":\"EAST HORIZON AIRLINES\",\"commonName\":\"EMERALD AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"EB\",\"icaoCode\":\"PLM\",\"businessName\":\"WAMOS AIR\",\"commonName\":\"WAMOS AIR\"},{\"type\":\"airline\",\"iataCode\":\"ED\",\"businessName\":\"AIR EXPLORE\",\"commonName\":\"AIRBLUE\"},{\"type\":\"airline\",\"iataCode\":\"EE\",\"icaoCode\":\"EST\",\"businessName\":\"XFLY\",\"commonName\":\"XFLY\"},{\"type\":\"airline\",\"iataCode\":\"EF\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"EG\",\"businessName\":\"AER LINGUS UK LIMITED\",\"commonName\":\"AER LINGUS UK LIMITED\"},{\"type\":\"airline\",\"iataCode\":\"EH\",\"icaoCode\":\"AKX\",\"businessName\":\"ANA WINGS\",\"commonName\":\"ANA WINGS\"},{\"type\":\"airline\",\"iataCode\":\"EI\",\"icaoCode\":\"EIN\",\"businessName\":\"AER LINGUS\",\"commonName\":\"AER LINGUS\"},{\"type\":\"airline\",\"iataCode\":\"EJ\",\"businessName\":\"EQUATORIAL CONGO AIRLINES ECAIR\",\"commonName\":\"EQUATORIAL CONGO ECAIR\"},{\"type\":\"airline\",\"iataCode\":\"EK\",\"icaoCode\":\"UAE\",\"businessName\":\"EMIRATES\",\"commonName\":\"EMIRATES\"},{\"type\":\"airline\",\"iataCode\":\"EL\",\"icaoCode\":\"RIE\",\"businessName\":\"ARIELLA AIRLINES\",\"commonName\":\"ARIELLA\"},{\"type\":\"airline\",\"iataCode\":\"EM\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"EN\",\"icaoCode\":\"DLA\",\"businessName\":\"AIR DOLOMITI\",\"commonName\":\"AIR DOLOMITI\"},{\"type\":\"airline\",\"iataCode\":\"EO\",\"businessName\":\"PEGAS FLY\",\"commonName\":\"AIR GO EGYPT\"},{\"type\":\"airline\",\"iataCode\":\"EP\",\"icaoCode\":\"IRC\",\"businessName\":\"IRAN ASEMAN AIRLINES\",\"commonName\":\"IRAN ASEMAN AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"EQ\",\"businessName\":\"FLY ANGOLA\",\"commonName\":\"FLY ANGOLA\"},{\"type\":\"airline\",\"iataCode\":\"ER\",\"icaoCode\":\"SEP\",\"businessName\":\"SERENE AIR\",\"commonName\":\"SERENE AIR\"},{\"type\":\"airline\",\"iataCode\":\"ES\",\"icaoCode\":\"ETR\",\"businessName\":\"ESTELAR\",\"commonName\":\"ESTELAR\"},{\"type\":\"airline\",\"iataCode\":\"ET\",\"icaoCode\":\"ETH\",\"businessName\":\"ETHIOPIAN AIRLINES\",\"commonName\":\"ETHIOPIAN AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"EU\",\"businessName\":\"CHENGDU AIRLINES\",\"commonName\":\"CHENGDU AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"EV\",\"icaoCode\":\"ASQ\",\"businessName\":\"EXPRESSJET AIRLINES\",\"commonName\":\"EXPRESSJET AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"EW\",\"icaoCode\":\"EWG\",\"businessName\":\"EUROWINGS\",\"commonName\":\"EUROWINGS\"},{\"type\":\"airline\",\"iataCode\":\"EX\",\"icaoCode\":\"5AH\",\"businessName\":\"REGIONAL EXPRESS AMERICAS\",\"commonName\":\"REGIONAL EXPRESS AMERICA\"},{\"type\":\"airline\",\"iataCode\":\"EY\",\"icaoCode\":\"ETD\",\"businessName\":\"ETIHAD AIRWAYS\",\"commonName\":\"ETIHAD AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"EZ\",\"icaoCode\":\"SUS\",\"businessName\":\"SUN AIR OF SCANDINAVIA\",\"commonName\":\"SUN AIR OF SCANDINAVIA\"},{\"type\":\"airline\",\"iataCode\":\"F0\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"F1\",\"businessName\":\"FARELOGIX\",\"commonName\":\"FARELOGIX\"},{\"type\":\"airline\",\"iataCode\":\"F2\",\"businessName\":\"SAFARILINK AVIATION\",\"commonName\":\"SAFARILINK AVIATION\"},{\"type\":\"airline\",\"iataCode\":\"F4\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"F5\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"F6\",\"icaoCode\":\"VAW\",\"businessName\":\"FLY2SKY\",\"commonName\":\"FLY2SKY\"},{\"type\":\"airline\",\"iataCode\":\"F7\",\"icaoCode\":\"RSY\",\"businessName\":\"I FLY\",\"commonName\":\"I FLY\"},{\"type\":\"airline\",\"iataCode\":\"F8\",\"icaoCode\":\"FLE\",\"businessName\":\"FLAIR AIRLINES\",\"commonName\":\"FLAIR AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"F9\",\"icaoCode\":\"FFT\",\"businessName\":\"FRONTIER AIRLINES\",\"commonName\":\"FRONTIER AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"FA\",\"icaoCode\":\"SFR\",\"businessName\":\"SAFAIR\",\"commonName\":\"SAFAIR\"},{\"type\":\"airline\",\"iataCode\":\"FB\",\"icaoCode\":\"LZB\",\"businessName\":\"BULGARIA AIR\",\"commonName\":\"BALKAN AIR TO\"},{\"type\":\"airline\",\"iataCode\":\"FC\",\"businessName\":\"LINK AIRWAYS FLY FC\",\"commonName\":\"LINK AIRWAYS FLY FC\"},{\"type\":\"airline\",\"iataCode\":\"FD\",\"icaoCode\":\"AIQ\",\"businessName\":\"THAI AIRASIA\",\"commonName\":\"THAI AIRASIA\"},{\"type\":\"airline\",\"iataCode\":\"FE\",\"icaoCode\":\"IHO\",\"businessName\":\"SEVEN FOUR EIGHT AIR SERVICES\",\"commonName\":\"SEVEN FOUR EIGHT AIR SER\"},{\"type\":\"airline\",\"iataCode\":\"FF\",\"icaoCode\":\"FXX\",\"businessName\":\"FELIX AIRWAYS\",\"commonName\":\"FELIX AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"FG\",\"icaoCode\":\"AFG\",\"businessName\":\"ARIANA AFGHAN AIRLINES\",\"commonName\":\"ARIANA AFGHAN AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"FH\",\"icaoCode\":\"FHY\",\"businessName\":\"FREEBIRD AIRLINES\",\"commonName\":\"FREEBIRD AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"FJ\",\"icaoCode\":\"FJI\",\"businessName\":\"FIJI AIRWAYS\",\"commonName\":\"FIJI AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"YH\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"FK\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"TQ\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"J5\",\"businessName\":\"ALASKA SEAPLANE SERVICE\",\"commonName\":\"ALASKA SEAPLANE SERVICE\"},{\"type\":\"airline\",\"iataCode\":\"J6\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"J7\",\"icaoCode\":\"ABS\",\"businessName\":\"AFRIJET BUSINESS SERVICE\",\"commonName\":\"CENTRE AVIA\"},{\"type\":\"airline\",\"iataCode\":\"FM\",\"icaoCode\":\"CSH\",\"businessName\":\"SHANGHAI AIRLINES\",\"commonName\":\"SHANGHAI AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"FN\",\"icaoCode\":\"FJW\",\"businessName\":\"FASTJET ZIMBABWE\",\"commonName\":\"REGIONAL AIR\"},{\"type\":\"airline\",\"iataCode\":\"FO\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"FP\",\"businessName\":\"FLYPELICAN\",\"commonName\":\"PELICAN AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"FQ\",\"icaoCode\":\"CWN\",\"businessName\":\"CROWN AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"FR\",\"icaoCode\":\"RYR\",\"businessName\":\"RYANAIR\",\"commonName\":\"RYANAIR\"},{\"type\":\"airline\",\"iataCode\":\"6Y\",\"icaoCode\":\"ART\",\"businessName\":\"SMARTLYNX AIRLINES\",\"commonName\":\"SMARTLYNX AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"PZ\",\"icaoCode\":\"LAP\",\"businessName\":\"LATAM AIRLINES PARAGUAY\",\"commonName\":\"LATAM AIRLINES PARAGUAY\"},{\"type\":\"airline\",\"iataCode\":\"6Z\",\"businessName\":\"AMADEUS 6Z\"},{\"type\":\"airline\",\"iataCode\":\"7A\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"FV\",\"icaoCode\":\"SDM\",\"businessName\":\"ROSSIYA AIRLINES\",\"commonName\":\"ROSSIYA AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"FW\",\"businessName\":\"IBEX AIRLINES\",\"commonName\":\"IBEX AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"FX\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"1D\",\"businessName\":\"RADDIX SOLUTIONS INTL\",\"commonName\":\"RADDIX SOLUTIONS INTL\"},{\"type\":\"airline\",\"iataCode\":\"7M\",\"icaoCode\":\"PAM\",\"businessName\":\"MAP LINHAS AEREAS\",\"commonName\":\"MAP LINHAS AEREAS\"},{\"type\":\"airline\",\"iataCode\":\"LJ\",\"icaoCode\":\"JNA\",\"businessName\":\"JIN AIR\",\"commonName\":\"JIN AIR\"},{\"type\":\"airline\",\"iataCode\":\"O4\",\"icaoCode\":\"OTF\",\"businessName\":\"ORANGE2FLY AIRLINES\",\"commonName\":\"ORANGE2FLY AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"LK\",\"icaoCode\":\"LLL\",\"businessName\":\"LAO SKYWAY\",\"commonName\":\"LAO SKYWAY\"},{\"type\":\"airline\",\"iataCode\":\"G3\",\"icaoCode\":\"GLO\",\"businessName\":\"GOL LINHAS AEREAS S/A\",\"commonName\":\"GOL LINHAS AEREAS S/A\"},{\"type\":\"airline\",\"iataCode\":\"G4\",\"icaoCode\":\"AAY\",\"businessName\":\"ALLEGIANT AIR\",\"commonName\":\"ALLEGIANT AIR\"},{\"type\":\"airline\",\"iataCode\":\"1I\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"1J\",\"businessName\":\"AXESS INTERNATIONAL\",\"commonName\":\"AXESS INTERNATIONAL\"},{\"type\":\"airline\",\"iataCode\":\"OB\",\"icaoCode\":\"BOV\",\"businessName\":\"BOLIVIANA DE AVIACION\",\"commonName\":\"BOLIVIANA\"},{\"type\":\"airline\",\"iataCode\":\"LG\",\"icaoCode\":\"LGL\",\"businessName\":\"LUXAIR\",\"commonName\":\"LUXAIR\"},{\"type\":\"airline\",\"iataCode\":\"GA\",\"icaoCode\":\"GIA\",\"businessName\":\"GARUDA INDONESIA\",\"commonName\":\"GARUDA\"},{\"type\":\"airline\",\"iataCode\":\"TI\",\"businessName\":\"TROPIC OCEAN AIRWAYS\",\"commonName\":\"TROPIC OCEAN AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"B1\",\"businessName\":\"BRAVO PASSENGER SOLUTION\"},{\"type\":\"airline\",\"iataCode\":\"7U\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"B4\",\"businessName\":\"BEOND\",\"commonName\":\"BEOND\"},{\"type\":\"airline\",\"iataCode\":\"J9\",\"icaoCode\":\"JZR\",\"businessName\":\"JAZEERA AIRWAYS\",\"commonName\":\"JAZEERA AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"GF\",\"icaoCode\":\"GFA\",\"businessName\":\"GULF AIR\",\"commonName\":\"GULF AIR\"},{\"type\":\"airline\",\"iataCode\":\"XI\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"GG\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"7X\",\"icaoCode\":\"XYC\",\"businessName\":\"AMADEUS SEVEN\",\"commonName\":\"AMADEUS SEVEN\"},{\"type\":\"airline\",\"iataCode\":\"7Y\",\"businessName\":\"AMADEUS 7Y\"},{\"type\":\"airline\",\"iataCode\":\"7Z\",\"icaoCode\":\"EZR\",\"businessName\":\"AMADEUS 7Z\",\"commonName\":\"Z AIR\"},{\"type\":\"airline\",\"iataCode\":\"UU\",\"icaoCode\":\"REU\",\"businessName\":\"AIR AUSTRAL\",\"commonName\":\"AIR AUSTRAL\"},{\"type\":\"airline\",\"iataCode\":\"R8\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"AX\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"QS\",\"icaoCode\":\"TVS\",\"businessName\":\"SMARTWINGS\",\"commonName\":\"TRAVELSERVICE\"},{\"type\":\"airline\",\"iataCode\":\"1G\",\"businessName\":\"TRAVELPORT\",\"commonName\":\"TRAVELPORT\"},{\"type\":\"airline\",\"iataCode\":\"1H\",\"businessName\":\"SIRENA-TRAVEL\",\"commonName\":\"SIRENA-TRAVEL\"},{\"type\":\"airline\",\"iataCode\":\"TD\",\"icaoCode\":\"TBC\",\"businessName\":\"AIRCOMPANY TBILISI AIRWAYS\",\"commonName\":\"AIRCOMPANY TBILISI\"},{\"type\":\"airline\",\"iataCode\":\"7Q\",\"icaoCode\":\"MNU\",\"businessName\":\"ELITE AIRWAYS\",\"commonName\":\"ELITE AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"VE\",\"icaoCode\":\"EFY\",\"businessName\":\"CLIC AIR S.A.\",\"commonName\":\"CLIC AIR S.A.\"},{\"type\":\"airline\",\"iataCode\":\"DA\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"TS\",\"icaoCode\":\"TSC\",\"businessName\":\"AIR TRANSAT\",\"commonName\":\"AIR TRANSAT\"},{\"type\":\"airline\",\"iataCode\":\"TJ\",\"icaoCode\":\"GPD\",\"businessName\":\"TRADEWIND AVIATION\",\"commonName\":\"TRADEWIND AVIATION\"},{\"type\":\"airline\",\"iataCode\":\"M8\",\"businessName\":\"UNDEFINED\",\"commonName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"P0\",\"icaoCode\":\"PFZ\",\"businessName\":\"PROFLIGHT ZAMBIA\",\"commonName\":\"PROFLIGHT ZAMBIA\"},{\"type\":\"airline\",\"iataCode\":\"P1\",\"businessName\":\"PUBLICCHARTERS.COM\",\"commonName\":\"PUBLICCHARTERS.COM\"},{\"type\":\"airline\",\"iataCode\":\"M1\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"G7\",\"icaoCode\":\"GJS\",\"businessName\":\"GOJET AIRLINES\",\"commonName\":\"GOJET AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"1P\",\"icaoCode\":\"WSP\",\"businessName\":\"WORLDSPAN\",\"commonName\":\"WORLDSPAN\"},{\"type\":\"airline\",\"iataCode\":\"1Q\",\"businessName\":\"INTELISYS AVIATION SYSTEMS\"},{\"type\":\"airline\",\"iataCode\":\"1R\",\"businessName\":\"JR TECHNOLOGIES IRELAND\"},{\"type\":\"airline\",\"iataCode\":\"1S\",\"businessName\":\"SABRE\",\"commonName\":\"SABRE\"},{\"type\":\"airline\",\"iataCode\":\"1T\",\"businessName\":\"HITIT BILGISAYAR HIZMETLERI\",\"commonName\":\"HITIT\"},{\"type\":\"airline\",\"iataCode\":\"1U\",\"businessName\":\"ITA SOFTWARE\",\"commonName\":\"ITA SOFTWARE\"},{\"type\":\"airline\",\"iataCode\":\"1V\",\"businessName\":\"GALILEO INTERNATIONAL\",\"commonName\":\"GALILEO INTERNATIONAL\"},{\"type\":\"airline\",\"iataCode\":\"1W\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"1X\",\"businessName\":\"BRANSON AIR\",\"commonName\":\"BRANSON AIR\"},{\"type\":\"airline\",\"iataCode\":\"1Y\",\"businessName\":\"DXC TECHNOLOGY SERVICES\",\"commonName\":\"DXC TECHNOLOGY SERVICES\"},{\"type\":\"airline\",\"iataCode\":\"1Z\",\"icaoCode\":\"APD\",\"businessName\":\"BIRD INFORMATION SYSTEM\"},{\"type\":\"airline\",\"iataCode\":\"2A\",\"businessName\":\"AIR ASTRA\",\"commonName\":\"AIR ASTRA\"},{\"type\":\"airline\",\"iataCode\":\"2B\",\"icaoCode\":\"AWT\",\"businessName\":\"ALBAWINGS\",\"commonName\":\"ALBAWINGS\"},{\"type\":\"airline\",\"iataCode\":\"2C\",\"businessName\":\"SNCF\"},{\"type\":\"airline\",\"iataCode\":\"2D\",\"icaoCode\":\"DYA\",\"businessName\":\"EASTERN AIRLINES\",\"commonName\":\"EASTERN AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"2E\",\"businessName\":\"SMOKEY BAY AIR\",\"commonName\":\"SMOKEY BAY AIR\"},{\"type\":\"airline\",\"iataCode\":\"2F\",\"icaoCode\":\"AFU\",\"businessName\":\"AFRINAT INTERNATIONAL AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"2G\",\"businessName\":\"ANGARA AIRLINES\",\"commonName\":\"ANGARA AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"2H\",\"businessName\":\"THALYS INTERNATIONAL\",\"commonName\":\"THALYS\"},{\"type\":\"airline\",\"iataCode\":\"2I\",\"icaoCode\":\"SRU\",\"businessName\":\"STAR PERU\",\"commonName\":\"STAR PERU\"},{\"type\":\"airline\",\"iataCode\":\"2J\",\"icaoCode\":\"VBW\",\"businessName\":\"AIR BURKINA\",\"commonName\":\"AIR BURKINA\"},{\"type\":\"airline\",\"iataCode\":\"2K\",\"icaoCode\":\"GLG\",\"businessName\":\"AVIANCA ECUADOR S.A.\",\"commonName\":\"AVIANCA ECUADOR S.A.\"},{\"type\":\"airline\",\"iataCode\":\"2L\",\"icaoCode\":\"OAW\",\"businessName\":\"HELVETIC AIRWAYS\",\"commonName\":\"HELVETIC AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"2M\",\"icaoCode\":\"MYD\",\"businessName\":\"MAYA ISLAND AIR\",\"commonName\":\"MAYA ISLAND AIR\"},{\"type\":\"airline\",\"iataCode\":\"2N\",\"icaoCode\":\"XLE\",\"businessName\":\"NG EAGLE LTD\",\"commonName\":\"NG Eagle Ltd\"},{\"type\":\"airline\",\"iataCode\":\"2O\",\"businessName\":\"REDEMPTION\",\"commonName\":\"REDEMPTION\"},{\"type\":\"airline\",\"iataCode\":\"2P\",\"icaoCode\":\"GAP\",\"businessName\":\"PAL EXPRESS\",\"commonName\":\"PAL EXPRESS\"},{\"type\":\"airline\",\"iataCode\":\"2Q\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"2R\",\"icaoCode\":\"RLB\",\"businessName\":\"SUNLIGHT AIR\",\"commonName\":\"SUNLIGHT AIR\"},{\"type\":\"airline\",\"iataCode\":\"2S\",\"icaoCode\":\"STW\",\"businessName\":\"SOUTHWIND AIRLINES\",\"commonName\":\"SOUTHWIND AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"2T\",\"businessName\":\"TURBO MEGHA AIRWAYS\",\"commonName\":\"BERMUDAIR LIMITED\"},{\"type\":\"airline\",\"iataCode\":\"2U\",\"icaoCode\":\"ERO\",\"businessName\":\"ERO SUN D'OR\",\"commonName\":\"SUN D OR\"},{\"type\":\"airline\",\"iataCode\":\"2V\",\"businessName\":\"AMTRAK\",\"commonName\":\"AMTRAK\"},{\"type\":\"airline\",\"iataCode\":\"2W\",\"icaoCode\":\"WFL\",\"businessName\":\"World 2 Fly\",\"commonName\":\"W2FLY\"},{\"type\":\"airline\",\"iataCode\":\"2X\",\"icaoCode\":\"XYA\",\"businessName\":\"AMADEUS TWO\",\"commonName\":\"AMADEUS TWO\"},{\"type\":\"airline\",\"iataCode\":\"2Y\",\"businessName\":\"AMADEUS PDF 2Y\",\"commonName\":\"AIR ANDAMAN\"},{\"type\":\"airline\",\"iataCode\":\"2Z\",\"icaoCode\":\"PTB\",\"businessName\":\"PASSAREDO TRANSPORTES\",\"commonName\":\"PASSAREDO TRANSPORTES\"},{\"type\":\"airline\",\"iataCode\":\"3A\",\"businessName\":\"CHU KONG PASSENGER TRANSPORT\",\"commonName\":\"CHU KONG PASSENGER TSPT\"},{\"type\":\"airline\",\"iataCode\":\"3B\",\"icaoCode\":\"NTB\",\"businessName\":\"BESTFLY\",\"commonName\":\"BESTFLY\"},{\"type\":\"airline\",\"iataCode\":\"3C\",\"icaoCode\":\"CVA\",\"businessName\":\"AIR CHATHAMS\",\"commonName\":\"AIR CHATHAMS\"},{\"type\":\"airline\",\"iataCode\":\"3D\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"3E\",\"icaoCode\":\"OMQ\",\"businessName\":\"MULTI AERO\",\"commonName\":\"MULTI AERO\"},{\"type\":\"airline\",\"iataCode\":\"3F\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"3G\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"3H\",\"icaoCode\":\"AIE\",\"businessName\":\"AIR INUIT\",\"commonName\":\"AIR INUIT\"},{\"type\":\"airline\",\"iataCode\":\"3I\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"3J\",\"icaoCode\":\"JBW\",\"businessName\":\"JUBBA AIRWAYS LIMITED\",\"commonName\":\"JUBBA AIRWAYS LIMITED\"},{\"type\":\"airline\",\"iataCode\":\"3K\",\"icaoCode\":\"JSA\",\"businessName\":\"JETSTAR ASIA\",\"commonName\":\"JETSTAR ASIA\"},{\"type\":\"airline\",\"iataCode\":\"3L\",\"icaoCode\":\"ADY\",\"businessName\":\"AIR ARABIA ABU DHABI\",\"commonName\":\"INTERSKY\"},{\"type\":\"airline\",\"iataCode\":\"3M\",\"icaoCode\":\"SIL\",\"businessName\":\"SILVER AIRWAYS CORP\",\"commonName\":\"SILVER AIRWAYS CORP\"},{\"type\":\"airline\",\"iataCode\":\"3N\",\"icaoCode\":\"URG\",\"businessName\":\"AIR URGA\",\"commonName\":\"AIR URGA\"},{\"type\":\"airline\",\"iataCode\":\"3O\",\"icaoCode\":\"MAC\",\"businessName\":\"AIR ARABIA MAROC\",\"commonName\":\"AIR ARABIA MA\"},{\"type\":\"airline\",\"iataCode\":\"3P\",\"icaoCode\":\"WPT\",\"businessName\":\"WORLD 2 FLY PORTUGAL\",\"commonName\":\"WORLD 2 FLY PORTUGAL\"},{\"type\":\"airline\",\"iataCode\":\"3Q\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"3R\",\"icaoCode\":\"DVR\",\"businessName\":\"DIVI DIVI AIR\",\"commonName\":\"DIVI DIVI AIR\"},{\"type\":\"airline\",\"iataCode\":\"3S\",\"businessName\":\"AIR ANTILLES EXPRESS\",\"commonName\":\"AIR ANTILLES EXPRESS\"},{\"type\":\"airline\",\"iataCode\":\"3T\",\"icaoCode\":\"TQQ\",\"businessName\":\"TARCO AVIATION\",\"commonName\":\"TARCO AVIATION\"},{\"type\":\"airline\",\"iataCode\":\"3U\",\"icaoCode\":\"CSC\",\"businessName\":\"SICHUAN AIRLINES\",\"commonName\":\"SICHUAN AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"3V\",\"icaoCode\":\"TAY\",\"businessName\":\"TNT AIRWAYS\",\"commonName\":\"ASL AIRLINES BELGIUM\"},{\"type\":\"airline\",\"iataCode\":\"3W\",\"icaoCode\":\"MWI\",\"businessName\":\"MALAWI AIRLINES\",\"commonName\":\"MALAWI AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"3X\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"3Y\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"3Z\",\"icaoCode\":\"TVP\",\"businessName\":\"SMARTWINGS POLAND\",\"commonName\":\"SMARTWINGS POLAND\"},{\"type\":\"airline\",\"iataCode\":\"4A\",\"icaoCode\":\"AMP\",\"businessName\":\"AERO TRANSPORTE\",\"commonName\":\"AERO TRANSPORTE\"},{\"type\":\"airline\",\"iataCode\":\"4B\",\"businessName\":\"BOUTIQUE AIR\",\"commonName\":\"BOUTIQUE AIR\"},{\"type\":\"airline\",\"iataCode\":\"4C\",\"icaoCode\":\"ARE\",\"businessName\":\"LATAM AIRLINES COLOMBIA\",\"commonName\":\"LATAM AIRLINES COLOMBIA\"},{\"type\":\"airline\",\"iataCode\":\"4D\",\"icaoCode\":\"ASD\",\"businessName\":\"AIR SINAI\",\"commonName\":\"AIR SINAI\"},{\"type\":\"airline\",\"iataCode\":\"4F\",\"businessName\":\"FREEDOM AIRLINE EXPRESS\",\"commonName\":\"FREEDOM AIRLINE EXPRESS\"},{\"type\":\"airline\",\"iataCode\":\"4G\",\"icaoCode\":\"GZP\",\"businessName\":\"GAZPROMAVIA\",\"commonName\":\"GAZPROMAVIA\"},{\"type\":\"airline\",\"iataCode\":\"4H\",\"icaoCode\":\"HGG\",\"businessName\":\"HI AIR\",\"commonName\":\"HI AIR\"},{\"type\":\"airline\",\"iataCode\":\"4I\",\"businessName\":\"AIR ANTILLES\",\"commonName\":\"air antilles\"},{\"type\":\"airline\",\"iataCode\":\"4J\",\"businessName\":\"JETAIR CARIBBEAN\",\"commonName\":\"JETAIR CARIBBEAN\"},{\"type\":\"airline\",\"iataCode\":\"4K\",\"icaoCode\":\"SMK\",\"businessName\":\"FREEDOM AIRLINE EXPRESS\",\"commonName\":\"FREEDOM AIRLINE EXPRESS\"},{\"type\":\"airline\",\"iataCode\":\"4L\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"4M\",\"businessName\":\"LATAM AIRLINES ARGENTINA\",\"commonName\":\"LANARGENTINA\"},{\"type\":\"airline\",\"iataCode\":\"4N\",\"businessName\":\"AIR NORTH\",\"commonName\":\"AIR NORTH\"},{\"type\":\"airline\",\"iataCode\":\"4P\",\"businessName\":\"Regional Sky\",\"commonName\":\"REGIONAL SKY\"},{\"type\":\"airline\",\"iataCode\":\"4Q\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"4R\",\"businessName\":\"RENFE VIAJEROS\",\"commonName\":\"RENFE VIAJEROS\"},{\"type\":\"airline\",\"iataCode\":\"4S\",\"businessName\":\"RED SEA AIRLINES\",\"commonName\":\"RED SEA AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"4T\",\"businessName\":\"TRANSWEST AIR LIMITED\",\"commonName\":\"TRANSWEST AIR LIMITED\"},{\"type\":\"airline\",\"iataCode\":\"4U\",\"icaoCode\":\"GWI\",\"businessName\":\"GERMANWINGS\",\"commonName\":\"GERMANWINGS\"},{\"type\":\"airline\",\"iataCode\":\"4V\",\"icaoCode\":\"FGW\",\"businessName\":\"FLY GANGWON\",\"commonName\":\"Fly Gangwon\"},{\"type\":\"airline\",\"iataCode\":\"4W\",\"icaoCode\":\"WAV\",\"businessName\":\"WARBELOWS AIR VENTURES\",\"commonName\":\"WARBELOWS AIR VENTURES\"},{\"type\":\"airline\",\"iataCode\":\"4X\",\"icaoCode\":\"MLH\",\"businessName\":\"AVION EXPRESS MALTA LTD\",\"commonName\":\"AVION EXPRESS MALTA LTD\"},{\"type\":\"airline\",\"iataCode\":\"4Y\",\"icaoCode\":\"OCN\",\"businessName\":\"EW Discover\",\"commonName\":\"EW Discover\"},{\"type\":\"airline\",\"iataCode\":\"4Z\",\"icaoCode\":\"LNK\",\"businessName\":\"AIRLINK\",\"commonName\":\"SA AIRLINK\"},{\"type\":\"airline\",\"iataCode\":\"5A\",\"businessName\":\"BVI AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"5B\",\"icaoCode\":\"BSX\",\"businessName\":\"BASSAKA AIR\",\"commonName\":\"BASSAKA AIR\"},{\"type\":\"airline\",\"iataCode\":\"5C\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"5D\",\"icaoCode\":\"SLI\",\"businessName\":\"AEROLITORAL\",\"commonName\":\"AEROLITORAL\"},{\"type\":\"airline\",\"iataCode\":\"5E\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"5F\",\"icaoCode\":\"FIA\",\"businessName\":\"FLY ONE\",\"commonName\":\"BONAIRE AIR\"},{\"type\":\"airline\",\"iataCode\":\"5G\",\"businessName\":\"Mayair\"},{\"type\":\"airline\",\"iataCode\":\"5H\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"5I\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"5J\",\"icaoCode\":\"CEB\",\"businessName\":\"CEBU AIR\",\"commonName\":\"CEBU AIR\"},{\"type\":\"airline\",\"iataCode\":\"5K\",\"businessName\":\"HI FLY TRANSPORTES AEREO\",\"commonName\":\"HI FLY TRANSPORTES AEREO\"},{\"type\":\"airline\",\"iataCode\":\"5L\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"5M\",\"icaoCode\":\"MNT\",\"businessName\":\"FLY MONTSERRAT\",\"commonName\":\"FLY MONTSERRAT\"},{\"type\":\"airline\",\"iataCode\":\"5N\",\"icaoCode\":\"AUL\",\"businessName\":\"SMARTAVIA\",\"commonName\":\"SMARTAVIA\"},{\"type\":\"airline\",\"iataCode\":\"5O\",\"icaoCode\":\"FPO\",\"businessName\":\"ASL AIRLINES FRANCE\",\"commonName\":\"ASL AIRLINES FRANCE\"},{\"type\":\"airline\",\"iataCode\":\"5P\",\"icaoCode\":\"UZP\",\"businessName\":\"PANORAMA AIRWAYS\",\"commonName\":\"PANORAMA AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"5Q\",\"businessName\":\"HOLIDAY EUROPE\",\"commonName\":\"HOLIDAY EUROPE\"},{\"type\":\"airline\",\"iataCode\":\"5R\",\"icaoCode\":\"RUC\",\"businessName\":\"RUTACA\",\"commonName\":\"RUTACA\"},{\"type\":\"airline\",\"iataCode\":\"5S\",\"icaoCode\":\"GAK\",\"businessName\":\"GLOBAL AIR TRANSPORT\",\"commonName\":\"GLOBAL AIR TRANSPORT\"},{\"type\":\"airline\",\"iataCode\":\"5T\",\"businessName\":\"CANADIAN NORTH\",\"commonName\":\"CANADIAN NORTH\"},{\"type\":\"airline\",\"iataCode\":\"5U\",\"icaoCode\":\"TGU\",\"businessName\":\"TRANSPORTES AEREOS\",\"commonName\":\"L A D E\"},{\"type\":\"airline\",\"iataCode\":\"5V\",\"businessName\":\"EVERTS\",\"commonName\":\"EVERTS\"},{\"type\":\"airline\",\"iataCode\":\"5W\",\"icaoCode\":\"WAZ\",\"businessName\":\"WIZZ AIR ABU DHABI\",\"commonName\":\"WIZZ AIR ABU DHABI\"},{\"type\":\"airline\",\"iataCode\":\"5X\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"5Y\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"5Z\",\"icaoCode\":\"KEM\",\"businessName\":\"CEMAIR\",\"commonName\":\"CEMAIR\"},{\"type\":\"airline\",\"iataCode\":\"6A\",\"icaoCode\":\"AMW\",\"businessName\":\"ARMENIA AIRWAYS\",\"commonName\":\"ARMENIA AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"6B\",\"icaoCode\":\"BLX\",\"businessName\":\"TUIFLY NORDIC\",\"commonName\":\"TUIFLY NORDIC\"},{\"type\":\"airline\",\"iataCode\":\"6C\",\"businessName\":\"AIR TIMOR\"},{\"type\":\"airline\",\"iataCode\":\"6D\",\"icaoCode\":\"TVQ\",\"businessName\":\"SMARTWINGS SLOVAKIA\",\"commonName\":\"SMARTWINGS SLOVAKIA\"},{\"type\":\"airline\",\"iataCode\":\"6E\",\"icaoCode\":\"IGO\",\"businessName\":\"INDIGO\",\"commonName\":\"INDIGO\"},{\"type\":\"airline\",\"iataCode\":\"6F\",\"businessName\":\"FOX AIRCRAFT LLC\",\"commonName\":\"PRIMERA AIR NORDIC\"},{\"type\":\"airline\",\"iataCode\":\"6G\",\"businessName\":\"SERVICIO AEREO REGIONAL REGAIR\"},{\"type\":\"airline\",\"iataCode\":\"6H\",\"icaoCode\":\"ISR\",\"businessName\":\"ISRAIR\",\"commonName\":\"ISRAIR\"},{\"type\":\"airline\",\"iataCode\":\"6I\",\"icaoCode\":\"MMD\",\"businessName\":\"AIR ALSIE\",\"commonName\":\"AIR ALSIE\"},{\"type\":\"airline\",\"iataCode\":\"6J\",\"businessName\":\"SOLASEED AIR\",\"commonName\":\"SOLASEED AIR\"},{\"type\":\"airline\",\"iataCode\":\"6K\",\"icaoCode\":\"TAH\",\"businessName\":\"AIR ANKA\"},{\"type\":\"airline\",\"iataCode\":\"6L\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"6M\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"6N\",\"icaoCode\":\"NIN\",\"businessName\":\"NIGER AIRLINES\",\"commonName\":\"NIGER AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"6O\",\"icaoCode\":\"OBS\",\"businessName\":\"ORBEST (GHD)\",\"commonName\":\"ORBEST GHD\"},{\"type\":\"airline\",\"iataCode\":\"6P\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"DB\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"DC\",\"businessName\":\"BRAATHENS REG. AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"DD\",\"icaoCode\":\"NOK\",\"businessName\":\"NOK AIR\",\"commonName\":\"NOK AIR\"},{\"type\":\"airline\",\"iataCode\":\"DE\",\"icaoCode\":\"CFG\",\"businessName\":\"CONDOR\",\"commonName\":\"CONDOR\"},{\"type\":\"airline\",\"iataCode\":\"DF\",\"icaoCode\":\"CIB\",\"businessName\":\"CONDOR BERLIN\"},{\"type\":\"airline\",\"iataCode\":\"X5\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"D3\",\"icaoCode\":\"DAO\",\"businessName\":\"DAALLO AIRLINES\",\"commonName\":\"DAALLO AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"7O\",\"icaoCode\":\"TVL\",\"businessName\":\"SMARTWINGS HUNGARY\",\"commonName\":\"SMARTWINGS HUNGARY\"},{\"type\":\"airline\",\"iataCode\":\"D4\",\"icaoCode\":\"GEL\",\"businessName\":\"AIRLINE GEO SKY\",\"commonName\":\"AIRLINE GEO SKY\"},{\"type\":\"airline\",\"iataCode\":\"D5\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"D6\",\"businessName\":\"GECA\",\"commonName\":\"GECA\"},{\"type\":\"airline\",\"iataCode\":\"GD\",\"businessName\":\"AVIAIR\",\"commonName\":\"AVIAIR\"},{\"type\":\"airline\",\"iataCode\":\"GE\",\"icaoCode\":\"GBB\",\"businessName\":\"GLOBAL AVIATION OPERATIONS\",\"commonName\":\"TRANSASIA\"},{\"type\":\"airline\",\"iataCode\":\"B0\",\"icaoCode\":\"DJT\",\"businessName\":\"LA COMPAGNIE\",\"commonName\":\"LA COMPAGNIE\"},{\"type\":\"airline\",\"iataCode\":\"1L\",\"businessName\":\"CITIZENPLANE\",\"commonName\":\"CITIZENPLANE\"},{\"type\":\"airline\",\"iataCode\":\"1M\",\"businessName\":\"ONLINE RESERVATION SYSTEM JSC\",\"commonName\":\"SYSTEMS TAIS\"},{\"type\":\"airline\",\"iataCode\":\"1N\",\"businessName\":\"NAVITAIRE NEW SKIES\",\"commonName\":\"NAVITAIRE NEW SKIES\"},{\"type\":\"airline\",\"iataCode\":\"B7\",\"icaoCode\":\"UIA\",\"businessName\":\"UNI AIRWAYS\",\"commonName\":\"UNI AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"B8\",\"icaoCode\":\"ERT\",\"businessName\":\"ERITREAN AIRLINES\",\"commonName\":\"ERITREAN AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"B9\",\"icaoCode\":\"IRB\",\"businessName\":\"IRAN AIRTOUR\",\"commonName\":\"IRAN AIRTOUR\"},{\"type\":\"airline\",\"iataCode\":\"BA\",\"icaoCode\":\"BAW\",\"businessName\":\"BRITISH AIRWAYS\",\"commonName\":\"BRITISH A/W\"},{\"type\":\"airline\",\"iataCode\":\"BB\",\"businessName\":\"SEABORNE AIRLINES\",\"commonName\":\"SEABORNE AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"BC\",\"icaoCode\":\"SKY\",\"businessName\":\"SKYMARK AIRLINES\",\"commonName\":\"SKYMARK AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"BD\",\"businessName\":\"CAMBODIA BAYON AIRLINES\",\"commonName\":\"CAMBODIA BAYON AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"BE\",\"icaoCode\":\"BEE\",\"businessName\":\"FLYBE\",\"commonName\":\"BRIT EUROP\"},{\"type\":\"airline\",\"iataCode\":\"BF\",\"icaoCode\":\"FBU\",\"businessName\":\"FRENCH BEE\",\"commonName\":\"FRENCH BEE\"},{\"type\":\"airline\",\"iataCode\":\"BG\",\"icaoCode\":\"BBC\",\"businessName\":\"BIMAN BANGLADESH AIRLINE\",\"commonName\":\"BIMAN\"},{\"type\":\"airline\",\"iataCode\":\"BI\",\"icaoCode\":\"RBA\",\"businessName\":\"ROYAL BRUNEI\",\"commonName\":\"ROYALBRUNEI\"},{\"type\":\"airline\",\"iataCode\":\"BJ\",\"icaoCode\":\"LBT\",\"businessName\":\"NOUVELAIR\",\"commonName\":\"NOUVELAIR\"},{\"type\":\"airline\",\"iataCode\":\"BL\",\"icaoCode\":\"PIC\",\"businessName\":\"PACIFIC AIRLINES\",\"commonName\":\"PACIFIC AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"BM\",\"icaoCode\":\"MNS\",\"businessName\":\"MEDSKY AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"BN\",\"icaoCode\":\"LWG\",\"businessName\":\"LUXWING LTD\",\"commonName\":\"LUXWING LTD\"},{\"type\":\"airline\",\"iataCode\":\"BO\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"BP\",\"icaoCode\":\"BOT\",\"businessName\":\"AIR BOTSWANA\",\"commonName\":\"AIR BOTSWANA\"},{\"type\":\"airline\",\"iataCode\":\"BQ\",\"icaoCode\":\"SWU\",\"businessName\":\"SKYALPS SRL\"},{\"type\":\"airline\",\"iataCode\":\"BR\",\"icaoCode\":\"EVA\",\"businessName\":\"EVA AIR\",\"commonName\":\"EVA AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"BS\",\"businessName\":\"US BANGLA AIRLINES\",\"commonName\":\"US BANGLA AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"BT\",\"icaoCode\":\"BTI\",\"businessName\":\"AIR BALTIC\",\"commonName\":\"AIR BALTIC\"},{\"type\":\"airline\",\"iataCode\":\"BU\",\"businessName\":\"COMPAGNIE AFRICAINE D'AVIATION\",\"commonName\":\"AFRICAINE\"},{\"type\":\"airline\",\"iataCode\":\"BV\",\"icaoCode\":\"BPA\",\"businessName\":\"BLUE PANORAMA AIRLINES\",\"commonName\":\"BLUE PANORAMA AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"BW\",\"icaoCode\":\"BWA\",\"businessName\":\"CARIBBEAN AIRLINES\",\"commonName\":\"CARIBBEAN AIR\"},{\"type\":\"airline\",\"iataCode\":\"BX\",\"icaoCode\":\"ABL\",\"businessName\":\"AIR BUSAN\",\"commonName\":\"AIR BUSAN\"},{\"type\":\"airline\",\"iataCode\":\"BY\",\"icaoCode\":\"TOM\",\"businessName\":\"TUI\",\"commonName\":\"TUI\"},{\"type\":\"airline\",\"iataCode\":\"BZ\",\"icaoCode\":\"BBG\",\"businessName\":\"BLUE BIRD AIRWAYS\",\"commonName\":\"BLUE BIRD AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"C0\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"C1\",\"businessName\":\"TECTIMES SUDAMERICA\",\"commonName\":\"TECTIMES SUDAMERICA\"},{\"type\":\"airline\",\"iataCode\":\"C2\",\"businessName\":\"CEIBA INTERCONTINENTAL\",\"commonName\":\"CEIBA\"},{\"type\":\"airline\",\"iataCode\":\"C3\",\"icaoCode\":\"TDR\",\"businessName\":\"TRADE AIR\",\"commonName\":\"TRADE AIR\"},{\"type\":\"airline\",\"iataCode\":\"C4\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"C5\",\"businessName\":\"COMMUTEAIR\",\"commonName\":\"COMMUTEAIR\"},{\"type\":\"airline\",\"iataCode\":\"C6\",\"icaoCode\":\"MFX\",\"businessName\":\"MY FREIGHTER\",\"commonName\":\"MY FREIGHTER\"},{\"type\":\"airline\",\"iataCode\":\"C7\",\"icaoCode\":\"CIN\",\"businessName\":\"CINNAMON AIR\",\"commonName\":\"CINNAMON AIR\"},{\"type\":\"airline\",\"iataCode\":\"C8\",\"icaoCode\":\"CRA\",\"businessName\":\"CRONOS AIRLINES\",\"commonName\":\"CRONOS AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"C9\",\"icaoCode\":\"CKL\",\"businessName\":\"CRONOS AIRLINES BENIN\",\"commonName\":\"CRONOS AIRLINES BENIN\"},{\"type\":\"airline\",\"iataCode\":\"CA\",\"icaoCode\":\"CCA\",\"businessName\":\"AIR CHINA\",\"commonName\":\"AIR CHINA\"},{\"type\":\"airline\",\"iataCode\":\"CB\",\"businessName\":\"TRANS CARIBBEAN AIR EXPORTIMPORT\",\"commonName\":\"Trans Caribbean Air Exp\"},{\"type\":\"airline\",\"iataCode\":\"CD\",\"icaoCode\":\"CND\",\"businessName\":\"CORENDON DUTCH AIRLINES\",\"commonName\":\"SEAVIEW AIR\"},{\"type\":\"airline\",\"iataCode\":\"CE\",\"icaoCode\":\"CLG\",\"businessName\":\"CHALAIR AVIATION\",\"commonName\":\"CHALAIR AVIATION\"},{\"type\":\"airline\",\"iataCode\":\"CG\",\"icaoCode\":\"TOK\",\"businessName\":\"AIRLINES PNG\",\"commonName\":\"PNG AIR\"},{\"type\":\"airline\",\"iataCode\":\"CH\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"CI\",\"icaoCode\":\"CAL\",\"businessName\":\"CHINA AIRLINES LTD.\",\"commonName\":\"CHINA AIR\"},{\"type\":\"airline\",\"iataCode\":\"CJ\",\"icaoCode\":\"CFE\",\"businessName\":\"BA CITYFLYER\",\"commonName\":\"BA CITYFLYER\"},{\"type\":\"airline\",\"iataCode\":\"CK\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"CL\",\"icaoCode\":\"CLH\",\"businessName\":\"LUFTHANSA CITYLINE\",\"commonName\":\"LUFTHANSA CITYLINE\"},{\"type\":\"airline\",\"iataCode\":\"CM\",\"icaoCode\":\"CMP\",\"businessName\":\"COPA AIRLINES\",\"commonName\":\"COPA AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"CN\",\"businessName\":\"GRAND CHINA AIR\",\"commonName\":\"GRAND CHINA AIR\"},{\"type\":\"airline\",\"iataCode\":\"CO\",\"businessName\":\"Undefined\"},{\"type\":\"airline\",\"iataCode\":\"CP\",\"businessName\":\"COMPASS AIRLINES\",\"commonName\":\"COMPASS AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"CQ\",\"icaoCode\":\"CSV\",\"businessName\":\"COASTAL AIR\",\"commonName\":\"COASTAL AIR\"},{\"type\":\"airline\",\"iataCode\":\"CR\",\"businessName\":\"OAG WORLDWIDE\"},{\"type\":\"airline\",\"iataCode\":\"W7\",\"icaoCode\":\"WMA\",\"businessName\":\"MAKERS AIR\",\"commonName\":\"MAKERS AIR\"},{\"type\":\"airline\",\"iataCode\":\"Q3\",\"businessName\":\"ANGUILLA AIR SERVICES\",\"commonName\":\"ANGUILLA AIR SERVICES\"},{\"type\":\"airline\",\"iataCode\":\"ZN\",\"icaoCode\":\"AZB\",\"businessName\":\"ZAMBIA AIRWAYS\",\"commonName\":\"ZAMBIA AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"ZO\",\"businessName\":\"ZAGROS AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"6U\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"6V\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"6W\",\"icaoCode\":\"FBS\",\"businessName\":\"FLYBOSNIA\",\"commonName\":\"FLYBOSNIA\"},{\"type\":\"airline\",\"iataCode\":\"SF\",\"icaoCode\":\"DTH\",\"businessName\":\"TASSILI AIRLINES\",\"commonName\":\"TASSILI AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"UG\",\"icaoCode\":\"TUX\",\"businessName\":\"TUNISAIR EXPRESS\",\"commonName\":\"TUNINTER\"},{\"type\":\"airline\",\"iataCode\":\"UH\",\"icaoCode\":\"UJX\",\"businessName\":\"ATLASJET UKRAINE\",\"commonName\":\"ATLASJET\"},{\"type\":\"airline\",\"iataCode\":\"PP\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"PQ\",\"icaoCode\":\"SQP\",\"businessName\":\"SKYUP AIRLINES\",\"commonName\":\"SKYUP AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"PR\",\"icaoCode\":\"PAL\",\"businessName\":\"PHILIPPINE AIRLINES\",\"commonName\":\"PHILIPPINE AL\"},{\"type\":\"airline\",\"iataCode\":\"PS\",\"icaoCode\":\"AUI\",\"businessName\":\"UIA\",\"commonName\":\"UIA\"},{\"type\":\"airline\",\"iataCode\":\"PT\",\"businessName\":\"PIEDMONT AIRLINES\",\"commonName\":\"PIEDMONT AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"6X\",\"icaoCode\":\"XYB\",\"businessName\":\"AMADEUS SIX\",\"commonName\":\"AMADEUS SIX\"},{\"type\":\"airline\",\"iataCode\":\"CX\",\"icaoCode\":\"CPA\",\"businessName\":\"CATHAY PACIFIC\",\"commonName\":\"CATHAYPACIFIC\"},{\"type\":\"airline\",\"iataCode\":\"7F\",\"icaoCode\":\"FAB\",\"businessName\":\"FIRST AIR\",\"commonName\":\"FIRST AIR\"},{\"type\":\"airline\",\"iataCode\":\"7G\",\"icaoCode\":\"SFJ\",\"businessName\":\"STAR FLYER\",\"commonName\":\"STAR FLYER\"},{\"type\":\"airline\",\"iataCode\":\"7H\",\"icaoCode\":\"RVF\",\"businessName\":\"RAVN ALASKA\",\"commonName\":\"RAVN ALASKA\"},{\"type\":\"airline\",\"iataCode\":\"FY\",\"icaoCode\":\"FFM\",\"businessName\":\"FIREFLY\",\"commonName\":\"FIREFLY\"},{\"type\":\"airline\",\"iataCode\":\"FZ\",\"icaoCode\":\"FDB\",\"businessName\":\"FLYDUBAI\",\"commonName\":\"FLYDUBAI\"},{\"type\":\"airline\",\"iataCode\":\"CZ\",\"icaoCode\":\"CSN\",\"businessName\":\"CHINA SOUTHERN AIRLINES\",\"commonName\":\"CHINA SOUTHERN AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"G1\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"QJ\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"QK\",\"icaoCode\":\"JZA\",\"businessName\":\"JAZZ AVIATION\",\"commonName\":\"JAZZ AVIATION\"},{\"type\":\"airline\",\"iataCode\":\"QL\",\"icaoCode\":\"LER\",\"businessName\":\"LINEA AEREA DE SERVICIO\",\"commonName\":\"LINEA AEREA DE SERVICIO\"},{\"type\":\"airline\",\"iataCode\":\"D0\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"R9\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"D1\",\"businessName\":\"AIR4 PASSENGER SERVICE SYSTEMS\",\"commonName\":\"AIR4 PASSENGER SERVICE\"},{\"type\":\"airline\",\"iataCode\":\"1E\",\"businessName\":\"TRAVELSKY TECHNOLOGY LTD\",\"commonName\":\"CIVIL CHINA\"},{\"type\":\"airline\",\"iataCode\":\"1F\",\"icaoCode\":\"TTF\",\"businessName\":\"INFINI TRAVEL INFORMATION\",\"commonName\":\"INFINI TRAVEL\"},{\"type\":\"airline\",\"iataCode\":\"7P\",\"icaoCode\":\"PST\",\"businessName\":\"AIR PANAMA\",\"commonName\":\"AIR PANAMA\"},{\"type\":\"airline\",\"iataCode\":\"G5\",\"icaoCode\":\"HXA\",\"businessName\":\"CHINA EXPRESS AIRLINES\",\"commonName\":\"CHINA EXPRESS AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"G6\",\"businessName\":\"FLY ARNA\",\"commonName\":\"FLY ARNA\"},{\"type\":\"airline\",\"iataCode\":\"7R\",\"icaoCode\":\"RLU\",\"businessName\":\"RUSLINE\",\"commonName\":\"RUSLINE\"},{\"type\":\"airline\",\"iataCode\":\"KB\",\"icaoCode\":\"DRK\",\"businessName\":\"DRUK AIR\",\"commonName\":\"DRUK AIR\"},{\"type\":\"airline\",\"iataCode\":\"Q5\",\"icaoCode\":\"MLA\",\"businessName\":\"FORTY MILE AIR\",\"commonName\":\"FORTY MILE AIR\"},{\"type\":\"airline\",\"iataCode\":\"AW\",\"icaoCode\":\"AFW\",\"businessName\":\"AFRICA WORLD AIRLINES\",\"commonName\":\"AFRICA WORLD AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"7K\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"NW\",\"businessName\":\"CELESTE\",\"commonName\":\"CELESTE\"},{\"type\":\"airline\",\"iataCode\":\"B2\",\"icaoCode\":\"BRU\",\"businessName\":\"BELAVIA\",\"commonName\":\"BELAVIA\"},{\"type\":\"airline\",\"iataCode\":\"B3\",\"icaoCode\":\"BTN\",\"businessName\":\"BHUTAN AIRLINES\",\"commonName\":\"BHUTAN AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"X4\",\"businessName\":\"AIR EXCURSIONS\",\"commonName\":\"AIR EXCURSIONS\"},{\"type\":\"airline\",\"iataCode\":\"XB\",\"businessName\":\"IATA\",\"commonName\":\"IATA\"},{\"type\":\"airline\",\"iataCode\":\"B5\",\"businessName\":\"EAST AFRICAN SAFARI AIR\",\"commonName\":\"EAST AFRICAN SAFARI AIR\"},{\"type\":\"airline\",\"iataCode\":\"B6\",\"icaoCode\":\"JBU\",\"businessName\":\"JETBLUE AIRWAYS\",\"commonName\":\"JETBLUE AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"PD\",\"businessName\":\"PORTER AIRLINES CANADA LTD\",\"commonName\":\"PORTER AIRLINES CANADA\"},{\"type\":\"airline\",\"iataCode\":\"PE\",\"icaoCode\":\"PEV\",\"businessName\":\"PEOPLES\",\"commonName\":\"PEOPLES\"},{\"type\":\"airline\",\"iataCode\":\"PH\",\"icaoCode\":\"SFZ\",\"businessName\":\"PIONAIR AUSTRALIA\",\"commonName\":\"PIONAIR AUSTRALIA\"},{\"type\":\"airline\",\"iataCode\":\"PI\",\"icaoCode\":\"RKA\",\"businessName\":\"POLAR AIRLINES\",\"commonName\":\"POLAR AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"O7\",\"icaoCode\":\"OMB\",\"businessName\":\"OMNI-BLU\",\"commonName\":\"OMNI-BLU\"},{\"type\":\"airline\",\"iataCode\":\"SG\",\"icaoCode\":\"SEJ\",\"businessName\":\"SPICEJET\",\"commonName\":\"SPICEJET\"},{\"type\":\"airline\",\"iataCode\":\"UI\",\"icaoCode\":\"AUK\",\"businessName\":\"AURIC AIR SERVICES\",\"commonName\":\"AURIC AIR SERVICES\"},{\"type\":\"airline\",\"iataCode\":\"NB\",\"icaoCode\":\"BNL\",\"businessName\":\"BERNIQ AIRWAYS\",\"commonName\":\"BERNIQ AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"NC\",\"icaoCode\":\"NJS\",\"businessName\":\"NATIONAL JET SYSTEMS\"},{\"type\":\"airline\",\"iataCode\":\"ND\",\"icaoCode\":\"NDA\",\"businessName\":\"NORDICA\",\"commonName\":\"NORDICA\"},{\"type\":\"airline\",\"iataCode\":\"NE\",\"icaoCode\":\"NMA\",\"businessName\":\"NESMA AIRLINES\",\"commonName\":\"NESMA AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"NF\",\"icaoCode\":\"AVN\",\"businessName\":\"AIR VANUATU\",\"commonName\":\"AIR VANUATU\"},{\"type\":\"airline\",\"iataCode\":\"Q6\",\"icaoCode\":\"VOC\",\"businessName\":\"VOLARIS COSTA RICA\",\"commonName\":\"VOLARIS COSTA RICA\"},{\"type\":\"airline\",\"iataCode\":\"Q7\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"Q8\",\"icaoCode\":\"TSG\",\"businessName\":\"TRANS AIR CONGO\",\"commonName\":\"TRANS AIR\"},{\"type\":\"airline\",\"iataCode\":\"Q9\",\"businessName\":\"GREEN AFRICA AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"QA\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"QB\",\"icaoCode\":\"IRQ\",\"businessName\":\"QESHM AIR\",\"commonName\":\"QESHM AIR\"},{\"type\":\"airline\",\"iataCode\":\"QC\",\"icaoCode\":\"CRC\",\"businessName\":\"CAMAIR-CO\",\"commonName\":\"CAMAIR-CO\"},{\"type\":\"airline\",\"iataCode\":\"QD\",\"businessName\":\"JC CAMBODIA INTL AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"QE\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"QF\",\"icaoCode\":\"QFA\",\"businessName\":\"QANTAS AIRWAYS\",\"commonName\":\"QANTAS\"},{\"type\":\"airline\",\"iataCode\":\"QG\",\"icaoCode\":\"CTV\",\"businessName\":\"CITILINK\",\"commonName\":\"CITILINK\"},{\"type\":\"airline\",\"iataCode\":\"QH\",\"icaoCode\":\"BAV\",\"businessName\":\"BAMBOO AIRWAYS\",\"commonName\":\"BAMBOO AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"QI\",\"icaoCode\":\"IAN\",\"businessName\":\"IBOM AIRLINES\",\"commonName\":\"IBOM AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"QM\",\"businessName\":\"MONACAIR\",\"commonName\":\"MONACAIR\"},{\"type\":\"airline\",\"iataCode\":\"QO\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"QP\",\"icaoCode\":\"AKJ\",\"businessName\":\"AKASA AIR\",\"commonName\":\"AKASA AIR\"},{\"type\":\"airline\",\"iataCode\":\"QQ\",\"icaoCode\":\"UTY\",\"businessName\":\"ALLIANCE AIRLINES\",\"commonName\":\"ALLIANCE AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"O2\",\"icaoCode\":\"HPK\",\"businessName\":\"LINEAR AIR\",\"commonName\":\"LINEAR AIR\"},{\"type\":\"airline\",\"iataCode\":\"TC\",\"icaoCode\":\"ATC\",\"businessName\":\"AIR TANZANIA\",\"commonName\":\"AIR TANZANIA\"},{\"type\":\"airline\",\"iataCode\":\"VF\",\"icaoCode\":\"TKJ\",\"businessName\":\"AJET\",\"commonName\":\"AJET\"},{\"type\":\"airline\",\"iataCode\":\"VG\",\"businessName\":\"VIPPER.COM\",\"commonName\":\"VIPPER.COM\"},{\"type\":\"airline\",\"iataCode\":\"VH\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"VI\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"VJ\",\"icaoCode\":\"VJC\",\"businessName\":\"VIETJET AVIATION\",\"commonName\":\"VIETJET AVIATION\"},{\"type\":\"airline\",\"iataCode\":\"WP\",\"icaoCode\":\"WSG\",\"businessName\":\"WASAYA AIRWAYS\",\"commonName\":\"WASAYA AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"WQ\",\"businessName\":\"SWIFT AIR\"},{\"type\":\"airline\",\"iataCode\":\"LY\",\"icaoCode\":\"ELY\",\"businessName\":\"EL AL\",\"commonName\":\"EL AL\"},{\"type\":\"airline\",\"iataCode\":\"S0\",\"icaoCode\":\"NSO\",\"businessName\":\"AEROLINEAS SOSA S.A. DE C.V.\",\"commonName\":\"AEROLINEAS SOSA\"},{\"type\":\"airline\",\"iataCode\":\"S1\",\"businessName\":\"LUFTHANSA SYSTEMS\",\"commonName\":\"LUFTHANSA SYSTEMS\"},{\"type\":\"airline\",\"iataCode\":\"S3\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"S4\",\"icaoCode\":\"RZO\",\"businessName\":\"AZORES AIRLINES\",\"commonName\":\"SATA INTL\"},{\"type\":\"airline\",\"iataCode\":\"S5\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"S6\",\"icaoCode\":\"KSZ\",\"businessName\":\"SUNRISE AIRWAYS\",\"commonName\":\"SUNRISE AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"WU\",\"icaoCode\":\"JFX\",\"businessName\":\"WESTERN AIR\",\"commonName\":\"WESTERN AIR\"},{\"type\":\"airline\",\"iataCode\":\"RZ\",\"icaoCode\":\"LRS\",\"businessName\":\"SANSA\",\"commonName\":\"SANSA\"},{\"type\":\"airline\",\"iataCode\":\"LI\",\"icaoCode\":\"LIA\",\"businessName\":\"LIAT\",\"commonName\":\"LIAT\"},{\"type\":\"airline\",\"iataCode\":\"RG\",\"icaoCode\":\"RJD\",\"businessName\":\"ROTANA JET\",\"commonName\":\"ROTANA JET\"},{\"type\":\"airline\",\"iataCode\":\"VD\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"OC\",\"icaoCode\":\"ORC\",\"businessName\":\"ORIENTAL AIR BRIDGE\",\"commonName\":\"ORIENTAL AIR BRIDGE\"},{\"type\":\"airline\",\"iataCode\":\"OU\",\"icaoCode\":\"CTN\",\"businessName\":\"CROATIA AIRLINES\",\"commonName\":\"CROATIA\"},{\"type\":\"airline\",\"iataCode\":\"FI\",\"icaoCode\":\"ICE\",\"businessName\":\"ICELANDAIR\",\"commonName\":\"ICELANDAIR\"},{\"type\":\"airline\",\"iataCode\":\"JF\",\"icaoCode\":\"OTT\",\"businessName\":\"OTT AIRLINES\",\"commonName\":\"OTT AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"TZ\",\"businessName\":\"TSARADIA\",\"commonName\":\"TSARADIA\"},{\"type\":\"airline\",\"iataCode\":\"U0\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"U1\",\"businessName\":\"VIDECOM INTERNATIONAL\",\"commonName\":\"VIDECOM INTERNATIONAL\"},{\"type\":\"airline\",\"iataCode\":\"U2\",\"icaoCode\":\"EZY\",\"businessName\":\"EASYJET\",\"commonName\":\"EASYJET\"},{\"type\":\"airline\",\"iataCode\":\"U3\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"U4\",\"businessName\":\"BUDDHA AIR\",\"commonName\":\"BUDDHA AIR\"},{\"type\":\"airline\",\"iataCode\":\"U5\",\"icaoCode\":\"SEU\",\"businessName\":\"SKYUP MT\",\"commonName\":\"SKYUP MT\"},{\"type\":\"airline\",\"iataCode\":\"U6\",\"icaoCode\":\"SVR\",\"businessName\":\"URAL AIRLINES\",\"commonName\":\"URAL AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"U7\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"U8\",\"icaoCode\":\"CYF\",\"businessName\":\"TUS AIRWAYS\",\"commonName\":\"TUS AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"U9\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"UA\",\"icaoCode\":\"UAL\",\"businessName\":\"UNITED AIRLINES\",\"commonName\":\"UNITED AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"UB\",\"icaoCode\":\"UBA\",\"businessName\":\"MYANMAR NATIONAL AIRLINES\",\"commonName\":\"MYANMAR\"},{\"type\":\"airline\",\"iataCode\":\"UC\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"UD\",\"icaoCode\":\"UBD\",\"businessName\":\"UBD\",\"commonName\":\"UBD\"},{\"type\":\"airline\",\"iataCode\":\"UE\",\"icaoCode\":\"UJC\",\"businessName\":\"ULTIMATE AIR SHUTTLE\",\"commonName\":\"ULTIMATE AIR SHUTTLE\"},{\"type\":\"airline\",\"iataCode\":\"UF\",\"icaoCode\":\"PER\",\"businessName\":\"PETROLEUM AIR SERVICES\",\"commonName\":\"PETROLEUM AIR SERVICES\"},{\"type\":\"airline\",\"iataCode\":\"SI\",\"icaoCode\":\"BCI\",\"businessName\":\"BLUE ISLANDS\",\"commonName\":\"BLUE ISLANDS\"},{\"type\":\"airline\",\"iataCode\":\"SJ\",\"icaoCode\":\"SJY\",\"businessName\":\"SRIWIJAYA AIR\",\"commonName\":\"SRIWIJAYA AIR\"},{\"type\":\"airline\",\"iataCode\":\"SK\",\"icaoCode\":\"SAS\",\"businessName\":\"SCANDINAVIAN AIRLINES\",\"commonName\":\"SAS\"},{\"type\":\"airline\",\"iataCode\":\"SL\",\"icaoCode\":\"TLM\",\"businessName\":\"THAI LION AIR\",\"commonName\":\"RIO SUL\"},{\"type\":\"airline\",\"iataCode\":\"SM\",\"icaoCode\":\"MSC\",\"businessName\":\"AIR CAIRO\",\"commonName\":\"AIR CAIRO\"},{\"type\":\"airline\",\"iataCode\":\"SN\",\"icaoCode\":\"BEL\",\"businessName\":\"BRUSSELS AIRLINES\",\"commonName\":\"BRUSSELS AIR\"},{\"type\":\"airline\",\"iataCode\":\"SO\",\"icaoCode\":\"SNR\",\"businessName\":\"SUN AIR AVIATION\",\"commonName\":\"SUN AIR AVIATION\"},{\"type\":\"airline\",\"iataCode\":\"SP\",\"icaoCode\":\"SAT\",\"businessName\":\"SATA AIR ACORES\",\"commonName\":\"SATA\"},{\"type\":\"airline\",\"iataCode\":\"SQ\",\"icaoCode\":\"SIA\",\"businessName\":\"SINGAPORE AIRLINES\",\"commonName\":\"SINGAPORE\"},{\"type\":\"airline\",\"iataCode\":\"SR\",\"icaoCode\":\"SDR\",\"businessName\":\"SUNDAIR\",\"commonName\":\"SUNDAIR\"},{\"type\":\"airline\",\"iataCode\":\"SS\",\"icaoCode\":\"CRL\",\"businessName\":\"CORSAIR\",\"commonName\":\"CORSE AIR\"},{\"type\":\"airline\",\"iataCode\":\"ST\",\"icaoCode\":\"RTL\",\"businessName\":\"AIR THANLWIN LIMITED\",\"commonName\":\"AIR THANLWIN LIMITED\"},{\"type\":\"airline\",\"iataCode\":\"SU\",\"icaoCode\":\"AFL\",\"businessName\":\"AEROFLOT\",\"commonName\":\"AEROFLOT\"},{\"type\":\"airline\",\"iataCode\":\"SV\",\"icaoCode\":\"SVA\",\"businessName\":\"SAUDI ARABIAN AIRLINES\",\"commonName\":\"SAUDIARABI\"},{\"type\":\"airline\",\"iataCode\":\"SW\",\"icaoCode\":\"NMB\",\"businessName\":\"AIR NAMIBIA\",\"commonName\":\"AIR NAMIBIA\"},{\"type\":\"airline\",\"iataCode\":\"SX\",\"icaoCode\":\"TOR\",\"businessName\":\"FLYGTA\",\"commonName\":\"FLYGTA\"},{\"type\":\"airline\",\"iataCode\":\"SY\",\"icaoCode\":\"SCX\",\"businessName\":\"SUN COUNTRY\",\"commonName\":\"SUN COUNTRY\"},{\"type\":\"airline\",\"iataCode\":\"SZ\",\"icaoCode\":\"SMR\",\"businessName\":\"SOMON AIR\",\"commonName\":\"SOMON AIR\"},{\"type\":\"airline\",\"iataCode\":\"T0\",\"businessName\":\"AVIANCA PERU S.A.\",\"commonName\":\"AVIANCA PERU\"},{\"type\":\"airline\",\"iataCode\":\"T1\",\"businessName\":\"AVTRASOFT LIMITED\",\"commonName\":\"AVTRASOFT LIMITED\"},{\"type\":\"airline\",\"iataCode\":\"T2\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"T4\",\"businessName\":\"TRIP\"},{\"type\":\"airline\",\"iataCode\":\"T6\",\"icaoCode\":\"ATX\",\"businessName\":\"AIRSWIFT\",\"commonName\":\"AIRSWIFT\"},{\"type\":\"airline\",\"iataCode\":\"T7\",\"businessName\":\"TWIN JET\",\"commonName\":\"TWIN JET\"},{\"type\":\"airline\",\"iataCode\":\"TA\",\"icaoCode\":\"TAI\",\"businessName\":\"TACA INTERNATIONAL AIRLINES\",\"commonName\":\"TACA\"},{\"type\":\"airline\",\"iataCode\":\"LO\",\"icaoCode\":\"LOT\",\"businessName\":\"LOT POLISH AIRLINES\",\"commonName\":\"LOT\"},{\"type\":\"airline\",\"iataCode\":\"LP\",\"icaoCode\":\"LPE\",\"businessName\":\"LATAM AIRLINES PERU\",\"commonName\":\"LATAM AIRLINES PERU\"},{\"type\":\"airline\",\"iataCode\":\"LM\",\"icaoCode\":\"LOG\",\"businessName\":\"LOGANAIR\",\"commonName\":\"LOGANAIR\"},{\"type\":\"airline\",\"iataCode\":\"LS\",\"icaoCode\":\"EXS\",\"businessName\":\"JET2.COM\",\"commonName\":\"JET2.COM\"},{\"type\":\"airline\",\"iataCode\":\"LT\",\"businessName\":\"LONGJIANG AIRLINES\",\"commonName\":\"LONGJIANG AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"TE\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"TF\",\"icaoCode\":\"BRX\",\"businessName\":\"BRAATHENS REGIONAL AIRWAYS AB\",\"commonName\":\"BRA\"},{\"type\":\"airline\",\"iataCode\":\"WC\",\"icaoCode\":\"WCT\",\"businessName\":\"MEREGRASS\",\"commonName\":\"MEREGRASS\"},{\"type\":\"airline\",\"iataCode\":\"WD\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"WE\",\"icaoCode\":\"THD\",\"businessName\":\"THAI SMILE AIRWAYS\",\"commonName\":\"THAI SMILE\"},{\"type\":\"airline\",\"iataCode\":\"WF\",\"icaoCode\":\"WIF\",\"businessName\":\"WIDEROE\",\"commonName\":\"WIDEROE\"},{\"type\":\"airline\",\"iataCode\":\"WG\",\"icaoCode\":\"SWG\",\"businessName\":\"SUNWING AIRLINES INC.\",\"commonName\":\"SUNWING AIRLINES INC.\"},{\"type\":\"airline\",\"iataCode\":\"7T\",\"businessName\":\"TRENITALIA\"},{\"type\":\"airline\",\"iataCode\":\"KN\",\"icaoCode\":\"CUA\",\"businessName\":\"CHINA UNITED AIRLINES\",\"commonName\":\"CHINA UNITED AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"LQ\",\"businessName\":\"LANMEI AIRLINES\",\"commonName\":\"LANMEI AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"AY\",\"icaoCode\":\"FIN\",\"businessName\":\"FINNAIR\",\"commonName\":\"FINNAIR\"},{\"type\":\"airline\",\"iataCode\":\"V7\",\"icaoCode\":\"VOE\",\"businessName\":\"VOLOTEA\",\"commonName\":\"VOLOTEA\"},{\"type\":\"airline\",\"iataCode\":\"V8\",\"icaoCode\":\"IAR\",\"businessName\":\"ILIAMNA AIR\",\"commonName\":\"ILIAMNA AIR\"},{\"type\":\"airline\",\"iataCode\":\"RY\",\"businessName\":\"JIANGXI AIR\",\"commonName\":\"JIANGXI AIR\"},{\"type\":\"airline\",\"iataCode\":\"TM\",\"icaoCode\":\"LAM\",\"businessName\":\"LAM MOZAMBIQUE\",\"commonName\":\"LAM\"},{\"type\":\"airline\",\"iataCode\":\"TN\",\"icaoCode\":\"THT\",\"businessName\":\"AIR TAHITI NUI\",\"commonName\":\"AIR TAHITI\"},{\"type\":\"airline\",\"iataCode\":\"TO\",\"icaoCode\":\"TVF\",\"businessName\":\"TRANSAVIA FRANCE\",\"commonName\":\"TRANSAVIA FRANCE\"},{\"type\":\"airline\",\"iataCode\":\"TP\",\"icaoCode\":\"TAP\",\"businessName\":\"TAP PORTUGAL\",\"commonName\":\"TAP PORTUGAL\"},{\"type\":\"airline\",\"iataCode\":\"WY\",\"icaoCode\":\"OMA\",\"businessName\":\"OMAN AIR\",\"commonName\":\"OMAN AIR\"},{\"type\":\"airline\",\"iataCode\":\"G8\",\"icaoCode\":\"GOW\",\"businessName\":\"GO FIRST\",\"commonName\":\"GO FIRST\"},{\"type\":\"airline\",\"iataCode\":\"WZ\",\"icaoCode\":\"RWZ\",\"businessName\":\"RED WINGS AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"M0\",\"icaoCode\":\"MNG\",\"businessName\":\"AEROMONGOLIA\",\"commonName\":\"AEROMONGOLIA\"},{\"type\":\"airline\",\"iataCode\":\"7V\",\"businessName\":\"FEDERAL AIRLINES\",\"commonName\":\"FEDERAL AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"RM\",\"businessName\":\"AIRCOMPANY ARMENIA\",\"commonName\":\"AIRCOMPANY ARMENIA\"},{\"type\":\"airline\",\"iataCode\":\"RN\",\"icaoCode\":\"SZL\",\"businessName\":\"ESWATINI AIR\",\"commonName\":\"ESWATINI AIR\"},{\"type\":\"airline\",\"iataCode\":\"M9\",\"icaoCode\":\"MSI\",\"businessName\":\"MOTOR SICH AIRLINES\",\"commonName\":\"MOTOR SICH AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"MA\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"MC\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"MD\",\"icaoCode\":\"MGY\",\"businessName\":\"MADAGASCAR AIRLINES\",\"commonName\":\"MADAGASCAR AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"ME\",\"icaoCode\":\"MEA\",\"businessName\":\"MIDDLE EAST AIRLINES\",\"commonName\":\"MIDDLE EAST\"},{\"type\":\"airline\",\"iataCode\":\"OS\",\"icaoCode\":\"AUA\",\"businessName\":\"AUSTRIAN AIRLINES\",\"commonName\":\"AUSTRIANAIR\"},{\"type\":\"airline\",\"iataCode\":\"MF\",\"icaoCode\":\"CXA\",\"businessName\":\"XIAMEN AIRLINES\",\"commonName\":\"XIAMEN AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"MG\",\"icaoCode\":\"EZA\",\"businessName\":\"EZNIS AIRWAYS\",\"commonName\":\"EZNIS AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"MH\",\"icaoCode\":\"MAS\",\"businessName\":\"MALAYSIA AIRLINES\",\"commonName\":\"MALAYSIA\"},{\"type\":\"airline\",\"iataCode\":\"KO\",\"businessName\":\"OJSC KOMIAVIATRANS\",\"commonName\":\"OJSC KOMIAVIATRANS\"},{\"type\":\"airline\",\"iataCode\":\"0N\",\"businessName\":\"NORTH STAR AIR LTD\",\"commonName\":\"NORTH STAR AIR LTD\"},{\"type\":\"airline\",\"iataCode\":\"KR\",\"businessName\":\"CAMBODIA AIRWAYS\",\"commonName\":\"CAMBODIA AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"KS\",\"icaoCode\":\"KON\",\"businessName\":\"Air Connect\",\"commonName\":\"AIR CONNECT AVIATION GRO\"},{\"type\":\"airline\",\"iataCode\":\"KU\",\"icaoCode\":\"KAC\",\"businessName\":\"KUWAIT AIRWAYS\",\"commonName\":\"KUWAIT AIR\"},{\"type\":\"airline\",\"iataCode\":\"KV\",\"businessName\":\"KRASAVIA\",\"commonName\":\"KRASAVIA\"},{\"type\":\"airline\",\"iataCode\":\"KW\",\"icaoCode\":\"JRQ\",\"businessName\":\"KENAI AVIATION\"},{\"type\":\"airline\",\"iataCode\":\"KX\",\"icaoCode\":\"CAY\",\"businessName\":\"CAYMAN AIRWAYS\",\"commonName\":\"CAYMAN AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"KY\",\"icaoCode\":\"KNA\",\"businessName\":\"Kunming Airlines\",\"commonName\":\"Kunming Airlines\"},{\"type\":\"airline\",\"iataCode\":\"KZ\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"L0\",\"businessName\":\"LIZ AVIATION BF\",\"commonName\":\"LIZ AVIATION BF\"},{\"type\":\"airline\",\"iataCode\":\"L1\",\"businessName\":\"CLH2\"},{\"type\":\"airline\",\"iataCode\":\"L2\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"L3\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"L4\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"L5\",\"icaoCode\":\"REA\",\"businessName\":\"RED AIR SRL\",\"commonName\":\"RED AIR SRL\"},{\"type\":\"airline\",\"iataCode\":\"L6\",\"icaoCode\":\"MAI\",\"businessName\":\"MAURITANIA AIRLINES\",\"commonName\":\"MAURITANIA AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"L7\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"L8\",\"icaoCode\":\"TON\",\"businessName\":\"LULUTAI AIRLINES\",\"commonName\":\"LULUTAI AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"L9\",\"icaoCode\":\"LWI\",\"businessName\":\"LUMIWINGS\",\"commonName\":\"LUMIWINGS\"},{\"type\":\"airline\",\"iataCode\":\"LA\",\"icaoCode\":\"LAN\",\"businessName\":\"LATAM AIRLINES GROUP\",\"commonName\":\"LATAM AIRLINES GROUP\"},{\"type\":\"airline\",\"iataCode\":\"ZP\",\"icaoCode\":\"AZP\",\"businessName\":\"PARANAIR\",\"commonName\":\"PARANAIR\"},{\"type\":\"airline\",\"iataCode\":\"ZR\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"ZS\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"ZT\",\"icaoCode\":\"AWC\",\"businessName\":\"TITAN AIRWAYS\",\"commonName\":\"TITAN AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"ZU\",\"businessName\":\"ETHIOPIAN MOZAMBIQUE\"},{\"type\":\"airline\",\"iataCode\":\"ZV\",\"icaoCode\":\"RFD\",\"businessName\":\"AEROTRANSPORTES RAFILHER\",\"commonName\":\"AEROTRANSPORTES RAFILHER\"},{\"type\":\"airline\",\"iataCode\":\"ZW\",\"icaoCode\":\"AWI\",\"businessName\":\"AIR WISCONSIN\",\"commonName\":\"AIR WISCONSIN\"},{\"type\":\"airline\",\"iataCode\":\"ZX\",\"icaoCode\":\"GGN\",\"businessName\":\"2746904 ONTARIO INC\",\"commonName\":\"2746904 ONTARIO INC\"},{\"type\":\"airline\",\"iataCode\":\"ZY\",\"icaoCode\":\"SHY\",\"businessName\":\"SKY AIRLINES\",\"commonName\":\"CHINA AIR CARGO\"},{\"type\":\"airline\",\"iataCode\":\"ZZ\",\"businessName\":\"AIRLINE SERVICE\"},{\"type\":\"airline\",\"iataCode\":\"DU\",\"icaoCode\":\"LIZ\",\"businessName\":\"SKY JET M.G. INC.\",\"commonName\":\"SKY JET M.G. INC.\"},{\"type\":\"airline\",\"iataCode\":\"LN\",\"icaoCode\":\"LAA\",\"businessName\":\"LIBYAN AIRLINES\",\"commonName\":\"LIBYAN AIR\"},{\"type\":\"airline\",\"iataCode\":\"7N\",\"businessName\":\"PAN AMERICAN WORLD\"},{\"type\":\"airline\",\"iataCode\":\"LL\",\"businessName\":\"CHINA GENERAL AVIATION\",\"commonName\":\"CHINA GENERAL AVIATION\"},{\"type\":\"airline\",\"iataCode\":\"QT\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"O3\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"QU\",\"businessName\":\"AIRLINE UTAIR UKRAINE\"},{\"type\":\"airline\",\"iataCode\":\"OI\",\"icaoCode\":\"HND\",\"businessName\":\"HINTERLAND AVIATION\",\"commonName\":\"HINTERLAND AVIATION\"},{\"type\":\"airline\",\"iataCode\":\"OJ\",\"icaoCode\":\"OLA\",\"businessName\":\"NYXAIR\",\"commonName\":\"NYXAIR\"},{\"type\":\"airline\",\"iataCode\":\"AZ\",\"icaoCode\":\"ITY\",\"businessName\":\"ITA AIRWAYS\",\"commonName\":\"ITA AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"RJ\",\"icaoCode\":\"RJA\",\"businessName\":\"ROYAL JORDANIAN\",\"commonName\":\"RYLJORDANIA\"},{\"type\":\"airline\",\"iataCode\":\"VU\",\"icaoCode\":\"VAG\",\"businessName\":\"VIETRAVEL AIRLINES\",\"commonName\":\"VIETRAVEL AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"VV\",\"businessName\":\"Viva Peru\",\"commonName\":\"Viva Peru\"},{\"type\":\"airline\",\"iataCode\":\"LH\",\"icaoCode\":\"DLH\",\"businessName\":\"LUFTHANSA\",\"commonName\":\"LUFTHANSA\"},{\"type\":\"airline\",\"iataCode\":\"ON\",\"icaoCode\":\"RON\",\"businessName\":\"NAURU AIRLINES\",\"commonName\":\"NAURU AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"OO\",\"icaoCode\":\"SKW\",\"businessName\":\"SKYWEST AIRLINES\",\"commonName\":\"SKYWEST AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"OP\",\"icaoCode\":\"DIG\",\"businessName\":\"PASSION AIR\",\"commonName\":\"PASSION AIR\"},{\"type\":\"airline\",\"iataCode\":\"1K\",\"businessName\":\"MIXVEL\",\"commonName\":\"MIXVEL\"},{\"type\":\"airline\",\"iataCode\":\"X1\",\"businessName\":\"HAHN AIR TECHNOLOGIES\",\"commonName\":\"HAHN AIR TECHNOLOGIES\"},{\"type\":\"airline\",\"iataCode\":\"OW\",\"icaoCode\":\"SEW\",\"businessName\":\"SKYWARD EXPRESS\",\"commonName\":\"SKYWARD EXPRESS\"},{\"type\":\"airline\",\"iataCode\":\"TT\",\"icaoCode\":\"TGG\",\"businessName\":\"TIGER AIRWAYS AUSTRALIA\",\"commonName\":\"TIGER AIRWAYS AUSTRALIA\"},{\"type\":\"airline\",\"iataCode\":\"JA\",\"icaoCode\":\"JAT\",\"businessName\":\"JETSMART SPA\",\"commonName\":\"JETSMART SPA\"},{\"type\":\"airline\",\"iataCode\":\"JB\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"JC\",\"icaoCode\":\"JAC\",\"businessName\":\"JAPAN AIR COMMUTER\"},{\"type\":\"airline\",\"iataCode\":\"1O\",\"businessName\":\"PHOENIX SYSTEMS DERTOURS\"},{\"type\":\"airline\",\"iataCode\":\"JD\",\"icaoCode\":\"CBJ\",\"businessName\":\"BEIJING CAPITAL AIRLINES\",\"commonName\":\"BEIJING CAPIT\"},{\"type\":\"airline\",\"iataCode\":\"JE\",\"icaoCode\":\"MNO\",\"businessName\":\"MANGO\",\"commonName\":\"MANGO\"},{\"type\":\"airline\",\"iataCode\":\"PJ\",\"businessName\":\"AIR SAINT PIERRE\",\"commonName\":\"AIR SAINT PIERRE\"},{\"type\":\"airline\",\"iataCode\":\"PK\",\"icaoCode\":\"PIA\",\"businessName\":\"PAKISTAN INTERNATIONAL\",\"commonName\":\"PAKISTAN INTERNATIONAL\"},{\"type\":\"airline\",\"iataCode\":\"PL\",\"businessName\":\"SOUTHERN AIR CHARTER\",\"commonName\":\"SOUTHERN AIR CHARTER\"},{\"type\":\"airline\",\"iataCode\":\"YE\",\"businessName\":\"YAN AIR\",\"commonName\":\"YAN AIR\"},{\"type\":\"airline\",\"iataCode\":\"YF\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"MR\",\"icaoCode\":\"MML\",\"businessName\":\"HUNNU AIR\",\"commonName\":\"MONGOLIAN AIR\"},{\"type\":\"airline\",\"iataCode\":\"MS\",\"icaoCode\":\"MSR\",\"businessName\":\"EGYPTAIR\",\"commonName\":\"EGYPTAIR\"},{\"type\":\"airline\",\"iataCode\":\"MT\",\"businessName\":\"MALTA MEDAIR\",\"commonName\":\"MALTA MEDAIR\"},{\"type\":\"airline\",\"iataCode\":\"MU\",\"icaoCode\":\"CES\",\"businessName\":\"CHINA EASTERN AIRLINES\",\"commonName\":\"CHINA EASTERN AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"MV\",\"icaoCode\":\"MAR\",\"businessName\":\"AIR MEDITERRANEAN\",\"commonName\":\"AIR MEDITERRANEAN\"},{\"type\":\"airline\",\"iataCode\":\"MW\",\"businessName\":\"Connect Airlines\",\"commonName\":\"Connect Airlines\"},{\"type\":\"airline\",\"iataCode\":\"MX\",\"businessName\":\"BREEZE AIRWAYS\",\"commonName\":\"MEXICANA\"},{\"type\":\"airline\",\"iataCode\":\"MY\",\"businessName\":\"MASWINGS\",\"commonName\":\"MASWINGS\"},{\"type\":\"airline\",\"iataCode\":\"MZ\",\"icaoCode\":\"AHX\",\"businessName\":\"AMAKUSA AIRLINES\",\"commonName\":\"AMAKUSA AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"N0\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"N1\",\"businessName\":\"DARWIN TRAVEL TECHNOLOGY\",\"commonName\":\"DARWIN TRAVEL TECHNOLOGY\"},{\"type\":\"airline\",\"iataCode\":\"N2\",\"icaoCode\":\"NIG\",\"businessName\":\"AERO CONTRACTORS NIGERIA\",\"commonName\":\"AERO CONTRACTORS NIGERIA\"},{\"type\":\"airline\",\"iataCode\":\"N3\",\"icaoCode\":\"VOS\",\"businessName\":\"VOLARIS EL SALVADOR\",\"commonName\":\"VOLARIS EL SALVADOR\"},{\"type\":\"airline\",\"iataCode\":\"N4\",\"icaoCode\":\"NWS\",\"businessName\":\"NORD WIND\",\"commonName\":\"NORD WIND\"},{\"type\":\"airline\",\"iataCode\":\"N5\",\"icaoCode\":\"NRL\",\"businessName\":\"LES INVESTISSEMENTS NOLINOR\"},{\"type\":\"airline\",\"iataCode\":\"N6\",\"icaoCode\":\"TZS\",\"businessName\":\"TCA\",\"commonName\":\"TCA\"},{\"type\":\"airline\",\"iataCode\":\"N7\",\"icaoCode\":\"FCM\",\"businessName\":\"NORDIC REGIONAL AIRLINES OY\"},{\"type\":\"airline\",\"iataCode\":\"N8\",\"icaoCode\":\"NCR\",\"businessName\":\"NATIONAL AIRLINES\",\"commonName\":\"NATIONAL AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"N9\",\"businessName\":\"SHREE AIRLINES\",\"commonName\":\"SHREE AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"UT\",\"icaoCode\":\"UTA\",\"businessName\":\"UTAIR AVIATION JSC\",\"commonName\":\"UTAIR AVIATION JSC\"},{\"type\":\"airline\",\"iataCode\":\"PY\",\"icaoCode\":\"SLM\",\"businessName\":\"SURINAM AIRWAYS\",\"commonName\":\"SURINAM AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"Q0\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"Q1\",\"businessName\":\"SQIVA SISTEM\",\"commonName\":\"SQIVA SISTEM\"},{\"type\":\"airline\",\"iataCode\":\"Q2\",\"businessName\":\"ISLAND AVIATION SERVICES\"},{\"type\":\"airline\",\"iataCode\":\"NI\",\"icaoCode\":\"PGA\",\"businessName\":\"PORTUGALIA\",\"commonName\":\"PORTUGALIA\"},{\"type\":\"airline\",\"iataCode\":\"NJ\",\"businessName\":\"GHADAMES AIR TRANSPORT\"},{\"type\":\"airline\",\"iataCode\":\"NK\",\"icaoCode\":\"NKS\",\"businessName\":\"SPIRIT AIRLINES\",\"commonName\":\"SPIRIT AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"NL\",\"icaoCode\":\"AEH\",\"businessName\":\"Amelia International\",\"commonName\":\"Amelia International\"},{\"type\":\"airline\",\"iataCode\":\"NM\",\"icaoCode\":\"NTR\",\"businessName\":\"AIR MOANA\",\"commonName\":\"AIR MOANA\"},{\"type\":\"airline\",\"iataCode\":\"NN\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"NO\",\"icaoCode\":\"NOS\",\"businessName\":\"NEOS SPA\",\"commonName\":\"NEOS SPA\"},{\"type\":\"airline\",\"iataCode\":\"NP\",\"icaoCode\":\"NIA\",\"businessName\":\"NILE AIR\",\"commonName\":\"NILE AIR\"},{\"type\":\"airline\",\"iataCode\":\"NQ\",\"icaoCode\":\"AJX\",\"businessName\":\"AIR JAPAN COMPANY LTD\",\"commonName\":\"AIR JAPAN COMPANY LTD\"},{\"type\":\"airline\",\"iataCode\":\"NR\",\"icaoCode\":\"MAV\",\"businessName\":\"MANTA AVIATION\",\"commonName\":\"MANTA AVIATION\"},{\"type\":\"airline\",\"iataCode\":\"TB\",\"icaoCode\":\"JAF\",\"businessName\":\"JETAIRFLY\",\"commonName\":\"TUI FLY BELGIUM\"},{\"type\":\"airline\",\"iataCode\":\"V5\",\"icaoCode\":\"DAP\",\"businessName\":\"AEROVIAS DAP\",\"commonName\":\"AEROVIAS DAP\"},{\"type\":\"airline\",\"iataCode\":\"V6\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"LC\",\"icaoCode\":\"NIS\",\"businessName\":\"AEROTAXI LA COSTENA\",\"commonName\":\"AEROTAXI LA COSTENA\"},{\"type\":\"airline\",\"iataCode\":\"VR\",\"icaoCode\":\"TCV\",\"businessName\":\"TACV CABO VERDE AIRLINES\",\"commonName\":\"T A C V\"},{\"type\":\"airline\",\"iataCode\":\"M2\",\"businessName\":\"MHS AVIATION\",\"commonName\":\"MHS AVIATION\"},{\"type\":\"airline\",\"iataCode\":\"X0\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"RK\",\"icaoCode\":\"RUK\",\"businessName\":\"RYANAIR UK\",\"commonName\":\"RYANAIR UK\"},{\"type\":\"airline\",\"iataCode\":\"X2\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"D8\",\"icaoCode\":\"NSZ\",\"businessName\":\"Norwegian Air Sweden AOC AB\",\"commonName\":\"NORWEGIAN AIR\"},{\"type\":\"airline\",\"iataCode\":\"OK\",\"icaoCode\":\"CSA\",\"businessName\":\"CZECH AIRLINES\",\"commonName\":\"CZECH AIRLINE\"},{\"type\":\"airline\",\"iataCode\":\"WV\",\"icaoCode\":\"WAA\",\"businessName\":\"FLY NAMIBIA\",\"commonName\":\"FLY NAMIBIA\"},{\"type\":\"airline\",\"iataCode\":\"RF\",\"icaoCode\":\"EOK\",\"businessName\":\"AERO K AIRLINES\",\"commonName\":\"AERO K AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"G9\",\"icaoCode\":\"ABY\",\"businessName\":\"AIR ARABIA\",\"commonName\":\"AIR ARABIA\"},{\"type\":\"airline\",\"iataCode\":\"TR\",\"icaoCode\":\"TGW\",\"businessName\":\"SCOOT\",\"commonName\":\"SCOOT\"},{\"type\":\"airline\",\"iataCode\":\"9N\",\"icaoCode\":\"TOS\",\"businessName\":\"TROPIC AIR LIMITED\",\"commonName\":\"TROPIC AIR LIMITED\"},{\"type\":\"airline\",\"iataCode\":\"IN\",\"icaoCode\":\"LKN\",\"businessName\":\"NAM AIR\",\"commonName\":\"NAM AIR\"},{\"type\":\"airline\",\"iataCode\":\"IO\",\"icaoCode\":\"IAE\",\"businessName\":\"IrAero\",\"commonName\":\"IrAero\"},{\"type\":\"airline\",\"iataCode\":\"IP\",\"icaoCode\":\"PAS\",\"businessName\":\"PELITA AIR SERVICE\"},{\"type\":\"airline\",\"iataCode\":\"IQ\",\"icaoCode\":\"QAZ\",\"businessName\":\"QAZAQ AIR\",\"commonName\":\"QAZAQ AIR\"},{\"type\":\"airline\",\"iataCode\":\"IR\",\"icaoCode\":\"IRA\",\"businessName\":\"IRAN AIR\",\"commonName\":\"IRAN AIR\"},{\"type\":\"airline\",\"iataCode\":\"IS\",\"icaoCode\":\"SHI\",\"businessName\":\"SEPEHRAN AIRLINES\",\"commonName\":\"SEPEHRAN AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"IT\",\"businessName\":\"TIGERAIR TAIWAN\",\"commonName\":\"TIGERAIR TAIWAN\"},{\"type\":\"airline\",\"iataCode\":\"IU\",\"businessName\":\"PT. SUPER AIR JET\",\"commonName\":\"PT. SUPER AIR JET\"},{\"type\":\"airline\",\"iataCode\":\"IV\",\"icaoCode\":\"GPX\",\"businessName\":\"GP AVIATION\",\"commonName\":\"GP AVIATION\"},{\"type\":\"airline\",\"iataCode\":\"IW\",\"businessName\":\"PT WINGS ABADI AIRLINES\",\"commonName\":\"PT WINGS ABADI AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"XK\",\"icaoCode\":\"CCM\",\"businessName\":\"AIR CORSICA\",\"commonName\":\"AIR CORSICA\"},{\"type\":\"airline\",\"iataCode\":\"XL\",\"icaoCode\":\"LNE\",\"businessName\":\"LATAM AIRLINES ECUADOR\",\"commonName\":\"LATAM AIRLINES ECUADOR\"},{\"type\":\"airline\",\"iataCode\":\"XM\",\"businessName\":\"Zimex Aviation Ltd\",\"commonName\":\"Zimex Aviation Ltd\"},{\"type\":\"airline\",\"iataCode\":\"XN\",\"icaoCode\":\"MXA\",\"businessName\":\"MEXICANA DE AVIACION\",\"commonName\":\"MEXICANA DE AVIACION\"},{\"type\":\"airline\",\"iataCode\":\"XO\",\"businessName\":\"SEAIR\",\"commonName\":\"SEAIR\"},{\"type\":\"airline\",\"iataCode\":\"XP\",\"icaoCode\":\"CXP\",\"businessName\":\"AVELO AIRLINES\",\"commonName\":\"AVELO AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"XQ\",\"icaoCode\":\"SXS\",\"businessName\":\"SUNEXPRESS\",\"commonName\":\"SUNEXPRESS\"},{\"type\":\"airline\",\"iataCode\":\"XR\",\"icaoCode\":\"CXI\",\"businessName\":\"CORENDON AIRLINES EUROPE\"},{\"type\":\"airline\",\"iataCode\":\"XS\",\"icaoCode\":\"SIT\",\"businessName\":\"SITA\"},{\"type\":\"airline\",\"iataCode\":\"XT\",\"icaoCode\":\"CTU\",\"businessName\":\"LLC GLOBUS\",\"commonName\":\"LLC GLOBUS\"},{\"type\":\"airline\",\"iataCode\":\"XU\",\"icaoCode\":\"AXK\",\"businessName\":\"AFRICAN EXPRESS AIRWAYS\",\"commonName\":\"AFRICAN EXPRESS AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"XV\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"XW\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"XX\",\"businessName\":\"GENERAL AVIATION\"},{\"type\":\"airline\",\"iataCode\":\"XY\",\"icaoCode\":\"KNE\",\"businessName\":\"FLYNAS\",\"commonName\":\"FLYNAS\"},{\"type\":\"airline\",\"iataCode\":\"XZ\",\"icaoCode\":\"AEZ\",\"businessName\":\"AEROITALIA SRL\",\"commonName\":\"AEROITALIA SRL\"},{\"type\":\"airline\",\"iataCode\":\"Y0\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"Y1\",\"businessName\":\"TRAVEL TECHNOLOGY INTERACTIVE\"},{\"type\":\"airline\",\"iataCode\":\"Y3\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"Y4\",\"icaoCode\":\"VOI\",\"businessName\":\"VOLARIS\",\"commonName\":\"VOLARIS\"},{\"type\":\"airline\",\"iataCode\":\"Y5\",\"businessName\":\"GOLDEN MYANMAR AIRLINES\",\"commonName\":\"GOLDEN MYANMAR AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"Y6\",\"icaoCode\":\"AYD\",\"businessName\":\"AB AVIATION\",\"commonName\":\"AB AVIATION\"},{\"type\":\"airline\",\"iataCode\":\"Y7\",\"icaoCode\":\"TYA\",\"businessName\":\"JSC AIRLINE TAIMYR\",\"commonName\":\"NORDSTAR\"},{\"type\":\"airline\",\"iataCode\":\"Y8\",\"icaoCode\":\"YZR\",\"businessName\":\"SUPARNA AIRLINES\",\"commonName\":\"SUPARNA AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"Y9\",\"icaoCode\":\"IRK\",\"businessName\":\"KISH AIRLINES\",\"commonName\":\"KISH AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"YA\",\"businessName\":\"NEGO AIRLINE ONE\"},{\"type\":\"airline\",\"iataCode\":\"YB\",\"businessName\":\"HARBOUR AIR\"},{\"type\":\"airline\",\"iataCode\":\"YC\",\"icaoCode\":\"LLM\",\"businessName\":\"YAMAL AIRLINES\",\"commonName\":\"YAMAL AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"YD\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"JQ\",\"icaoCode\":\"JST\",\"businessName\":\"JETSTAR\",\"commonName\":\"JETSTAR\"},{\"type\":\"airline\",\"iataCode\":\"JR\",\"icaoCode\":\"JOY\",\"businessName\":\"JOY AIR\",\"commonName\":\"JOY AIR\"},{\"type\":\"airline\",\"iataCode\":\"JS\",\"icaoCode\":\"KOR\",\"businessName\":\"AIR KORYO\",\"commonName\":\"AIR KORYO\"},{\"type\":\"airline\",\"iataCode\":\"JT\",\"icaoCode\":\"LNI\",\"businessName\":\"LION AIRLINES\",\"commonName\":\"LION AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"JU\",\"icaoCode\":\"ASL\",\"businessName\":\"AIR SERBIA\",\"commonName\":\"AIR SERBIA\"},{\"type\":\"airline\",\"iataCode\":\"JV\",\"icaoCode\":\"BLS\",\"businessName\":\"BEARSKIN AIRLINES\",\"commonName\":\"BEARSKIN AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"JW\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"JX\",\"icaoCode\":\"SJX\",\"businessName\":\"STARLUX AIRLINES\",\"commonName\":\"STARLUX AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"JY\",\"businessName\":\"INTERCARIBBEAN AIRWAYS\",\"commonName\":\"INTERCARIBBEAN AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"JZ\",\"icaoCode\":\"JAP\",\"businessName\":\"Jetsmart Airlines Peru\",\"commonName\":\"JETSMART AIRLINES PERU\"},{\"type\":\"airline\",\"iataCode\":\"K0\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"K1\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"Z7\",\"icaoCode\":\"AUZ\",\"businessName\":\"AMASZONAS URUGUAY\",\"commonName\":\"AMASZONAS URUGUAY\"},{\"type\":\"airline\",\"iataCode\":\"PU\",\"icaoCode\":\"PUE\",\"businessName\":\"PLUS ULTRA LINEAS AEREAS\",\"commonName\":\"PLUS ULTRA\"},{\"type\":\"airline\",\"iataCode\":\"PV\",\"icaoCode\":\"SBU\",\"businessName\":\"SAINT BARTH COMMUTER\",\"commonName\":\"SAINT BARTH COMMUTER\"},{\"type\":\"airline\",\"iataCode\":\"PW\",\"icaoCode\":\"PRF\",\"businessName\":\"PRECISION AIR\",\"commonName\":\"PRECISION AIR\"},{\"type\":\"airline\",\"iataCode\":\"PX\",\"icaoCode\":\"ANG\",\"businessName\":\"AIR NIUGINI\",\"commonName\":\"AIR NIUGINI\"},{\"type\":\"airline\",\"iataCode\":\"0S\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"K7\",\"icaoCode\":\"KBZ\",\"businessName\":\"MINGALAR AIRLINES\",\"commonName\":\"MINGALAR AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"K8\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"K9\",\"icaoCode\":\"TEZ\",\"businessName\":\"TEZ JET LLC\"},{\"type\":\"airline\",\"iataCode\":\"KA\",\"icaoCode\":\"ANK\",\"businessName\":\"AERO NOMAD AIRLINES\",\"commonName\":\"AERO NOMAD AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"Q4\",\"icaoCode\":\"ELE\",\"businessName\":\"EUROAIRLINES\",\"commonName\":\"EUROAIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"QW\",\"businessName\":\"QINGDAO AIRLINES\",\"commonName\":\"QINGDAO AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"QX\",\"icaoCode\":\"QXE\",\"businessName\":\"HORIZON AIR\",\"commonName\":\"HORIZON AIR\"},{\"type\":\"airline\",\"iataCode\":\"QY\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"QZ\",\"icaoCode\":\"AWQ\",\"businessName\":\"AIRASIA INDONESIA\",\"commonName\":\"AIRASIA INDONESIA\"},{\"type\":\"airline\",\"iataCode\":\"R0\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"R1\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"R2\",\"businessName\":\"TRANSAIR SENEGAL\",\"commonName\":\"TRANSAIR SENEGAL\"},{\"type\":\"airline\",\"iataCode\":\"R3\",\"icaoCode\":\"SYL\",\"businessName\":\"JSC AIRCOMPANY YAKUTIA\",\"commonName\":\"JSC AIRCOMPANY YAKUTIA\"},{\"type\":\"airline\",\"iataCode\":\"R4\",\"businessName\":\"RANO AIR LIMITED\"},{\"type\":\"airline\",\"iataCode\":\"R5\",\"icaoCode\":\"JAV\",\"businessName\":\"JORDAN AVIATION\",\"commonName\":\"JORDAN AVIATION\"},{\"type\":\"airline\",\"iataCode\":\"R6\",\"icaoCode\":\"DNU\",\"businessName\":\"DAT\",\"commonName\":\"DAT\"},{\"type\":\"airline\",\"iataCode\":\"RA\",\"icaoCode\":\"RNA\",\"businessName\":\"NEPAL AIRLINES\",\"commonName\":\"NEPAL AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"RE\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"TK\",\"icaoCode\":\"THY\",\"businessName\":\"TURKISH AIRLINES\",\"commonName\":\"TURKISH AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"TL\",\"icaoCode\":\"ANO\",\"businessName\":\"AIRNORTH\",\"commonName\":\"AIRNORTH\"},{\"type\":\"airline\",\"iataCode\":\"VY\",\"icaoCode\":\"VLG\",\"businessName\":\"VUELING AIRLINES\",\"commonName\":\"VUELING AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"DG\",\"icaoCode\":\"SRQ\",\"businessName\":\"CEBGO\",\"commonName\":\"CEBGO\"},{\"type\":\"airline\",\"iataCode\":\"TV\",\"businessName\":\"TIBET AIRLINES\",\"commonName\":\"TIBET AIR\"},{\"type\":\"airline\",\"iataCode\":\"TW\",\"icaoCode\":\"TWB\",\"businessName\":\"TWAY AIR\",\"commonName\":\"TWAY AIR\"},{\"type\":\"airline\",\"iataCode\":\"TX\",\"icaoCode\":\"FWI\",\"businessName\":\"AIR CARAIBES\",\"commonName\":\"AIR CARAIBES\"},{\"type\":\"airline\",\"iataCode\":\"TY\",\"icaoCode\":\"TPC\",\"businessName\":\"AIR CALEDONIE\",\"commonName\":\"AIR CALEDONIE\"},{\"type\":\"airline\",\"iataCode\":\"PF\",\"icaoCode\":\"SIF\",\"businessName\":\"AIR SIAL LIMITED\",\"commonName\":\"AIR SIAL LIMITED\"},{\"type\":\"airline\",\"iataCode\":\"PG\",\"icaoCode\":\"BKP\",\"businessName\":\"BANGKOK AIRWAYS\",\"commonName\":\"BANGKOK AIR\"},{\"type\":\"airline\",\"iataCode\":\"MI\",\"icaoCode\":\"FHM\",\"businessName\":\"FREEBIRD AIRLINES EUROPE\",\"commonName\":\"FREEBIRD AIRLINES EUROPE\"},{\"type\":\"airline\",\"iataCode\":\"MJ\",\"icaoCode\":\"MYW\",\"businessName\":\"MYWAY AIRLINES\",\"commonName\":\"MYWAY AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"MK\",\"icaoCode\":\"MAU\",\"businessName\":\"AIR MAURITIUS\",\"commonName\":\"AIR MAURITI\"},{\"type\":\"airline\",\"iataCode\":\"ML\",\"icaoCode\":\"FML\",\"businessName\":\"SKY MALI\",\"commonName\":\"SKY MALI\"},{\"type\":\"airline\",\"iataCode\":\"XJ\",\"icaoCode\":\"TAX\",\"businessName\":\"THAI AIRASIA X COMPANY\",\"commonName\":\"THAI AIRASIA X COMPANY\"},{\"type\":\"airline\",\"iataCode\":\"JG\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"JH\",\"icaoCode\":\"FDA\",\"businessName\":\"FUJI DREAM AIRLINES\",\"commonName\":\"FUJI DREAM AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"JI\",\"icaoCode\":\"AAG\",\"businessName\":\"ARMENIAN AIRLINES\",\"commonName\":\"ARMENIAN AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"JJ\",\"icaoCode\":\"TAM\",\"businessName\":\"LATAM AIRLINES BRASIL\",\"commonName\":\"LATAM AIRLINES BRASIL\"},{\"type\":\"airline\",\"iataCode\":\"JK\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"JL\",\"icaoCode\":\"JAL\",\"businessName\":\"JAPAN AIRLINES\",\"commonName\":\"JAPAN AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"JM\",\"icaoCode\":\"JMA\",\"businessName\":\"JAMBOJET\",\"commonName\":\"JAMBOJET\"},{\"type\":\"airline\",\"iataCode\":\"JN\",\"businessName\":\"ALASKA AIR TRANSIT\",\"commonName\":\"ALASKA AIR TRANSIT\"},{\"type\":\"airline\",\"iataCode\":\"JP\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"YG\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"GY\",\"businessName\":\"COLORFUL GUIZHOU AIRLINE\"},{\"type\":\"airline\",\"iataCode\":\"YI\",\"icaoCode\":\"OYA\",\"businessName\":\"FLY OYA\",\"commonName\":\"FLY OYA\"},{\"type\":\"airline\",\"iataCode\":\"YJ\",\"icaoCode\":\"WUA\",\"businessName\":\"ASIAN WINGS\",\"commonName\":\"ASIAN WINGS\"},{\"type\":\"airline\",\"iataCode\":\"YK\",\"icaoCode\":\"AVJ\",\"businessName\":\"AVIA TRAFFIC COMPANY\",\"commonName\":\"AVIA TRAFFIC COMPANY\"},{\"type\":\"airline\",\"iataCode\":\"YL\",\"businessName\":\"LIBYAN WINGS AIRLINE\",\"commonName\":\"LIBYAN WINGS AIRLINE\"},{\"type\":\"airline\",\"iataCode\":\"YM\",\"icaoCode\":\"MGX\",\"businessName\":\"MONTENEGRO AIRLINES\",\"commonName\":\"MONTENEGRO AL\"},{\"type\":\"airline\",\"iataCode\":\"YN\",\"icaoCode\":\"CRQ\",\"businessName\":\"AIR CREEBEC\",\"commonName\":\"AIR CREEBEC\"},{\"type\":\"airline\",\"iataCode\":\"YO\",\"icaoCode\":\"MCM\",\"businessName\":\"HELI AIR MONACO\",\"commonName\":\"HELIAIRMONA\"},{\"type\":\"airline\",\"iataCode\":\"YP\",\"icaoCode\":\"APZ\",\"businessName\":\"AIR PREMIA\",\"commonName\":\"AIR PREMIA\"},{\"type\":\"airline\",\"iataCode\":\"YQ\",\"icaoCode\":\"LCT\",\"businessName\":\"TAR\",\"commonName\":\"TAR\"},{\"type\":\"airline\",\"iataCode\":\"YR\",\"icaoCode\":\"EGJ\",\"businessName\":\"SCENIC AIRLINES\",\"commonName\":\"SCENIC AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"YS\",\"icaoCode\":\"FLZ\",\"businessName\":\"FLIGHTLINK\",\"commonName\":\"FLIGHTLINK\"},{\"type\":\"airline\",\"iataCode\":\"YT\",\"icaoCode\":\"NYT\",\"businessName\":\"YETI AIRLINES\",\"commonName\":\"YETI AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"YU\",\"icaoCode\":\"MMZ\",\"businessName\":\"EUROATLANTIC AIRWAYS\",\"commonName\":\"EUROATLANTIC AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"YV\",\"icaoCode\":\"ASH\",\"businessName\":\"MESA AIRLINES\",\"commonName\":\"MESA AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"YW\",\"icaoCode\":\"ANE\",\"businessName\":\"AIR NOSTRUM\",\"commonName\":\"AIR NOSTRUM\"},{\"type\":\"airline\",\"iataCode\":\"YX\",\"icaoCode\":\"RPA\",\"businessName\":\"REPUBLIC AIRWAYS\",\"commonName\":\"REPUBLIC AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"YZ\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"Z0\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"Z1\",\"businessName\":\"APG INC\"},{\"type\":\"airline\",\"iataCode\":\"Z2\",\"icaoCode\":\"APG\",\"businessName\":\"PHILIPPINES AIRASIA\",\"commonName\":\"PHILIPPINES AIRASIA\"},{\"type\":\"airline\",\"iataCode\":\"Z3\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"Z4\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"Z5\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"Z6\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"US\",\"businessName\":\"SILK AVIA\",\"commonName\":\"SILK AVIA\"},{\"type\":\"airline\",\"iataCode\":\"UV\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"UW\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"UX\",\"icaoCode\":\"AEA\",\"businessName\":\"AIR EUROPA\",\"commonName\":\"AIR EUROPA\"},{\"type\":\"airline\",\"iataCode\":\"WK\",\"icaoCode\":\"EDW\",\"businessName\":\"EDELWEISS AIR\",\"commonName\":\"EDELWEISS AIR\"},{\"type\":\"airline\",\"iataCode\":\"WL\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"WM\",\"icaoCode\":\"WIA\",\"businessName\":\"WINDWARD ISLAND AIRWAYS\",\"commonName\":\"WINDWARD ISLAND AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"WN\",\"icaoCode\":\"SWA\",\"businessName\":\"SOUTHWEST AIRLINES TEXAS\",\"commonName\":\"SW AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"W5\",\"icaoCode\":\"IRM\",\"businessName\":\"MAHAN AIR\",\"commonName\":\"MAHAN AIR\"},{\"type\":\"airline\",\"iataCode\":\"W6\",\"icaoCode\":\"WZZ\",\"businessName\":\"WIZZ AIR HUNGARY\",\"commonName\":\"WIZZ AIR HUNGARY\"},{\"type\":\"airline\",\"iataCode\":\"W8\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"W9\",\"icaoCode\":\"WUK\",\"businessName\":\"WIZZ AIR UK\",\"commonName\":\"WIZZ AIR UK\"},{\"type\":\"airline\",\"iataCode\":\"WA\",\"icaoCode\":\"KLC\",\"businessName\":\"KLM CITYHOPPER\"},{\"type\":\"airline\",\"iataCode\":\"VK\",\"businessName\":\"VALUEJET\",\"commonName\":\"VALUEJET\"},{\"type\":\"airline\",\"iataCode\":\"WR\",\"icaoCode\":\"WEN\",\"businessName\":\"WESTJET ENCORE LTD\",\"commonName\":\"WESTJET ENCORE LTD\"},{\"type\":\"airline\",\"iataCode\":\"WS\",\"icaoCode\":\"WJA\",\"businessName\":\"WESTJET\",\"commonName\":\"WESTJET\"},{\"type\":\"airline\",\"iataCode\":\"WT\",\"icaoCode\":\"SWT\",\"businessName\":\"UEPFLY (SWIFTAIR)\",\"commonName\":\"UEPFLY/SWIFTAIR\"},{\"type\":\"airline\",\"iataCode\":\"LZ\",\"businessName\":\"AIR LINK\"},{\"type\":\"airline\",\"iataCode\":\"QR\",\"icaoCode\":\"QTR\",\"businessName\":\"QATAR AIRWAYS\",\"commonName\":\"QATAR AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"VS\",\"icaoCode\":\"VIR\",\"businessName\":\"VIRGIN ATLANTIC\",\"commonName\":\"VIRGIN ATLANTIC\"},{\"type\":\"airline\",\"iataCode\":\"VT\",\"icaoCode\":\"VTA\",\"businessName\":\"AIR TAHITI\",\"commonName\":\"AIR TAHITI\"},{\"type\":\"airline\",\"iataCode\":\"VB\",\"icaoCode\":\"VIV\",\"businessName\":\"VIVA AEROBUS\",\"commonName\":\"VIVA AEROBUS\"},{\"type\":\"airline\",\"iataCode\":\"VC\",\"icaoCode\":\"SRY\",\"businessName\":\"STERLING-ALEUTIAN AIRWAYS\",\"commonName\":\"STERLING-ALEUTIAN AIRWAY\"},{\"type\":\"airline\",\"iataCode\":\"X6\",\"icaoCode\":\"ATA\",\"businessName\":\"AIRLINES REPORTING CORP\",\"commonName\":\"ARC\"},{\"type\":\"airline\",\"iataCode\":\"X7\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"X8\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"X9\",\"icaoCode\":\"NVD\",\"businessName\":\"AVION EXPRESS\",\"commonName\":\"CITY STAR AIR\"},{\"type\":\"airline\",\"iataCode\":\"XA\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"X3\",\"icaoCode\":\"TUI\",\"businessName\":\"TUIFLY\",\"commonName\":\"TUIFLY\"},{\"type\":\"airline\",\"iataCode\":\"XC\",\"icaoCode\":\"CAI\",\"businessName\":\"CORENDON AIRLINES\",\"commonName\":\"K D AIR\"},{\"type\":\"airline\",\"iataCode\":\"XD\",\"businessName\":\"ARC\"},{\"type\":\"airline\",\"iataCode\":\"XE\",\"icaoCode\":\"BTA\",\"businessName\":\"UNDEFINED\",\"commonName\":\"JSX AIR\"},{\"type\":\"airline\",\"iataCode\":\"XF\",\"icaoCode\":\"MGW\",\"businessName\":\"MONGOLIAN AIRWAYS CARGO\",\"commonName\":\"MONGOLIAN AIRWAYS CARGO\"},{\"type\":\"airline\",\"iataCode\":\"XG\",\"businessName\":\"UNDEFINED\",\"commonName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"XH\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"IX\",\"icaoCode\":\"AXB\",\"businessName\":\"AIR INDIA EXPRESS\",\"commonName\":\"AIR INDIA EXPRESS\"},{\"type\":\"airline\",\"iataCode\":\"IY\",\"icaoCode\":\"IYE\",\"businessName\":\"YEMEN AIRWAYS\",\"commonName\":\"YEMEN AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"IZ\",\"icaoCode\":\"AIZ\",\"businessName\":\"ARKIA\",\"commonName\":\"ARKIA\"},{\"type\":\"airline\",\"iataCode\":\"J0\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"J1\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"J2\",\"icaoCode\":\"AHY\",\"businessName\":\"AZERBAIJAN AIRLINES\",\"commonName\":\"AZERBAIJAN AI\"},{\"type\":\"airline\",\"iataCode\":\"J3\",\"icaoCode\":\"PLR\",\"businessName\":\"NORTHWESTERN AIR LEASE\",\"commonName\":\"NORTHWESTERN AIR LEASE\"},{\"type\":\"airline\",\"iataCode\":\"J4\",\"businessName\":\"BADR AIRLINES\",\"commonName\":\"BADR AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"S7\",\"icaoCode\":\"SBI\",\"businessName\":\"SIBERIA AIRLINES\",\"commonName\":\"S7 AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"S8\",\"icaoCode\":\"SDA\",\"businessName\":\"SOUNDS AIR\",\"commonName\":\"SOUNDS AIR\"},{\"type\":\"airline\",\"iataCode\":\"S9\",\"businessName\":\"TRI STATE CHARTER\",\"commonName\":\"TRI STATE CHARTER\"},{\"type\":\"airline\",\"iataCode\":\"SA\",\"icaoCode\":\"SAA\",\"businessName\":\"SOUTH AFRICAN AIRWAYS\",\"commonName\":\"S A A\"},{\"type\":\"airline\",\"iataCode\":\"SB\",\"icaoCode\":\"ACI\",\"businessName\":\"AIRCALIN\",\"commonName\":\"CAL.INT\"},{\"type\":\"airline\",\"iataCode\":\"SC\",\"icaoCode\":\"CDG\",\"businessName\":\"SHANDONG AIRLINES\",\"commonName\":\"SHANDONG AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"SD\",\"icaoCode\":\"SUD\",\"businessName\":\"SUDAN AIRWAYS\",\"commonName\":\"SUDAN AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"SE\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"MN\",\"icaoCode\":\"CAW\",\"businessName\":\"COMAIR LTD\",\"commonName\":\"COMAIR LTD\"},{\"type\":\"airline\",\"iataCode\":\"MO\",\"icaoCode\":\"CAV\",\"businessName\":\"CALM AIR INTERNATIONAL\",\"commonName\":\"CALM AIR INTERNATIONAL\"},{\"type\":\"airline\",\"iataCode\":\"MP\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"MQ\",\"icaoCode\":\"ENY\",\"businessName\":\"ENVOY AIR\",\"commonName\":\"ENVOY AIR\"},{\"type\":\"airline\",\"iataCode\":\"UJ\",\"icaoCode\":\"LMU\",\"businessName\":\"AL MASRIA UNIVERSAL AIRLINES\",\"commonName\":\"AL MASRIA AIR\"},{\"type\":\"airline\",\"iataCode\":\"UK\",\"icaoCode\":\"VTI\",\"businessName\":\"VISTARA\",\"commonName\":\"VISTARA\"},{\"type\":\"airline\",\"iataCode\":\"UL\",\"icaoCode\":\"ALK\",\"businessName\":\"SRILANKAN AIRLINES\",\"commonName\":\"SRILANKAN AIR\"},{\"type\":\"airline\",\"iataCode\":\"UM\",\"icaoCode\":\"AZW\",\"businessName\":\"AIR ZIMBABWE\",\"commonName\":\"AIR ZIMBABWE\"},{\"type\":\"airline\",\"iataCode\":\"UN\",\"icaoCode\":\"NUA\",\"businessName\":\"UNITED NIGERIA AIRLINES\",\"commonName\":\"UNITED NIGERIA AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"UO\",\"icaoCode\":\"HKE\",\"businessName\":\"HONG KONG EXPRESS AIRWAYS\",\"commonName\":\"HK EXPRESS\"},{\"type\":\"airline\",\"iataCode\":\"UP\",\"icaoCode\":\"BHS\",\"businessName\":\"BAHAMASAIR\",\"commonName\":\"BAHAMASAIR\"},{\"type\":\"airline\",\"iataCode\":\"UQ\",\"icaoCode\":\"CUH\",\"businessName\":\"URUMQI AIRLINES\",\"commonName\":\"URUMQI AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"UR\",\"icaoCode\":\"UGD\",\"businessName\":\"UGANDA AIRLINES\",\"commonName\":\"UGANDA AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"Z8\",\"icaoCode\":\"AZN\",\"businessName\":\"AMASZONAS S.A.\",\"commonName\":\"AMASZONAS S.A.\"},{\"type\":\"airline\",\"iataCode\":\"Z9\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"ZA\",\"icaoCode\":\"SWM\",\"businessName\":\"SKY ANGKOR AIRLINES\",\"commonName\":\"SKY ANGKOR AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"ZB\",\"businessName\":\"AIR ALBANIA\",\"commonName\":\"AIR ALBANIA\"},{\"type\":\"airline\",\"iataCode\":\"ZC\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"ZD\",\"icaoCode\":\"EWR\",\"businessName\":\"EWA AIR\",\"commonName\":\"EWA AIR\"},{\"type\":\"airline\",\"iataCode\":\"ZE\",\"icaoCode\":\"ESR\",\"businessName\":\"EASTAR JET\",\"commonName\":\"EASTAR JET\"},{\"type\":\"airline\",\"iataCode\":\"ZF\",\"icaoCode\":\"AZV\",\"businessName\":\"AZUR AIR\",\"commonName\":\"AZUR AIR\"},{\"type\":\"airline\",\"iataCode\":\"ZH\",\"icaoCode\":\"CSZ\",\"businessName\":\"SHENZHEN AIRLINES\",\"commonName\":\"SHENZHEN AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"ZI\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"ZJ\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"ZK\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"ZL\",\"businessName\":\"REGIONAL EXPRESS\",\"commonName\":\"REGIONAL EXPRESS\"},{\"type\":\"airline\",\"iataCode\":\"ZM\",\"icaoCode\":\"MBB\",\"businessName\":\"AIR MANAS AIR COMPANY LLC\",\"commonName\":\"AIR MANAS AIR COMPANY\"},{\"type\":\"airline\",\"iataCode\":\"NG\",\"icaoCode\":\"NAI\",\"businessName\":\"NOVAIR\",\"commonName\":\"NOVAIR\"},{\"type\":\"airline\",\"iataCode\":\"NH\",\"icaoCode\":\"ANA\",\"businessName\":\"ALL NIPPON AIRWAYS\",\"commonName\":\"ALL NIPPON\"},{\"type\":\"airline\",\"iataCode\":\"NS\",\"icaoCode\":\"HBH\",\"businessName\":\"HEBEI AIRLINES\",\"commonName\":\"HEBEI AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"NT\",\"icaoCode\":\"IBB\",\"businessName\":\"BINTER CANARIAS\",\"commonName\":\"BINTER CAN\"},{\"type\":\"airline\",\"iataCode\":\"NU\",\"icaoCode\":\"JTA\",\"businessName\":\"JAPAN TRANSOCEAN AIR\",\"commonName\":\"JAPAN TRANSOC\"},{\"type\":\"airline\",\"iataCode\":\"NV\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"7L\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"NX\",\"icaoCode\":\"AMU\",\"businessName\":\"AIR MACAU\",\"commonName\":\"AIR MACAU\"},{\"type\":\"airline\",\"iataCode\":\"NY\",\"icaoCode\":\"FXI\",\"businessName\":\"FLUGFELAG ISLANDS EHF\",\"commonName\":\"FLUGFELAG ISLANDS\"},{\"type\":\"airline\",\"iataCode\":\"NZ\",\"icaoCode\":\"ANZ\",\"businessName\":\"AIR NEW ZEALAND\",\"commonName\":\"AIR NEW ZEALAND\"},{\"type\":\"airline\",\"iataCode\":\"O0\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"O1\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"LR\",\"icaoCode\":\"LRC\",\"businessName\":\"AVIANCA COSTA RICA SA\",\"commonName\":\"L A C S A\"},{\"type\":\"airline\",\"iataCode\":\"LU\",\"businessName\":\"LATAM AIRLINES CHILE\",\"commonName\":\"LATAM AIRLINES CHILE\"},{\"type\":\"airline\",\"iataCode\":\"RP\",\"icaoCode\":\"BPS\",\"businessName\":\"BUDAPEST AIRCRAFT SERVICE\"},{\"type\":\"airline\",\"iataCode\":\"RQ\",\"icaoCode\":\"KMF\",\"businessName\":\"KAM AIR\",\"commonName\":\"KAM AIR\"},{\"type\":\"airline\",\"iataCode\":\"RR\",\"icaoCode\":\"RFR\",\"businessName\":\"ROYAL AIR FORCE\",\"commonName\":\"R.A.F NO1\"},{\"type\":\"airline\",\"iataCode\":\"RS\",\"icaoCode\":\"ASV\",\"businessName\":\"AIR SEOUL\",\"commonName\":\"AIR SEOUL\"},{\"type\":\"airline\",\"iataCode\":\"RT\",\"businessName\":\"JSC UVT AERO\",\"commonName\":\"JSC UVT AERO\"},{\"type\":\"airline\",\"iataCode\":\"RU\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"OF\",\"businessName\":\"UNDEFINED\",\"commonName\":\"OVERLAND AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"WB\",\"icaoCode\":\"RWD\",\"businessName\":\"RWANDAIR\",\"commonName\":\"RWANDAIR\"},{\"type\":\"airline\",\"iataCode\":\"6Q\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"6R\",\"icaoCode\":\"DRU\",\"businessName\":\"MIRNY AIR\",\"commonName\":\"MIRNY AIR\"},{\"type\":\"airline\",\"iataCode\":\"6S\",\"businessName\":\"SAUDI GULF AIRLINES\",\"commonName\":\"KATO AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"LE\",\"icaoCode\":\"STB\",\"businessName\":\"ST BARTH EXECUTIVE\"},{\"type\":\"airline\",\"iataCode\":\"LF\",\"icaoCode\":\"VTE\",\"businessName\":\"CONTOUR AIRLINES\",\"commonName\":\"CONTOUR AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"WO\",\"icaoCode\":\"WSW\",\"businessName\":\"SWOOP\",\"commonName\":\"SWOOP\"},{\"type\":\"airline\",\"iataCode\":\"RW\",\"icaoCode\":\"RYL\",\"businessName\":\"ROYAL AIR\",\"commonName\":\"ROYAL AIR\"},{\"type\":\"airline\",\"iataCode\":\"RX\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"8A\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"VW\",\"icaoCode\":\"TAO\",\"businessName\":\"AEROMAR\",\"commonName\":\"AEROMAR\"},{\"type\":\"airline\",\"iataCode\":\"VX\",\"icaoCode\":\"VRD\",\"businessName\":\"VIRGIN AMERICA\",\"commonName\":\"VIRGIN AMERICA\"},{\"type\":\"airline\",\"iataCode\":\"CT\",\"icaoCode\":\"CYL\",\"businessName\":\"ALITALIA CITY LINER SPA\",\"commonName\":\"ALITALIA CITY LINER SPA\"},{\"type\":\"airline\",\"iataCode\":\"CU\",\"icaoCode\":\"CUB\",\"businessName\":\"CUBANA DE AVIACION\",\"commonName\":\"CUBANA\"},{\"type\":\"airline\",\"iataCode\":\"OD\",\"icaoCode\":\"MXD\",\"businessName\":\"BATIK AIR MALAYSIA\",\"commonName\":\"BATIK AIR MALAYSIA\"},{\"type\":\"airline\",\"iataCode\":\"JO\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"VZ\",\"icaoCode\":\"TVJ\",\"businessName\":\"THAI VIETJET AIR\",\"commonName\":\"THAI VIETJET AIR\"},{\"type\":\"airline\",\"iataCode\":\"W0\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"W1\",\"businessName\":\"WORLD TICKET LTD\"},{\"type\":\"airline\",\"iataCode\":\"W2\",\"icaoCode\":\"FXT\",\"businessName\":\"FLEXFLIGHT\",\"commonName\":\"FLEXFLIGHT\"},{\"type\":\"airline\",\"iataCode\":\"W3\",\"icaoCode\":\"ARA\",\"businessName\":\"ARIK AIR LIMITED\",\"commonName\":\"ARIK AIR LIMITED\"},{\"type\":\"airline\",\"iataCode\":\"WI\",\"icaoCode\":\"WHT\",\"businessName\":\"WHITE AIRWAYS S.A.\"},{\"type\":\"airline\",\"iataCode\":\"LV\",\"icaoCode\":\"DTG\",\"businessName\":\"AIRCOMPANY AIRZENA\",\"commonName\":\"AIRCOMPANY AIRZENA\"},{\"type\":\"airline\",\"iataCode\":\"LW\",\"icaoCode\":\"LDA\",\"businessName\":\"LAUDA EUROPE\",\"commonName\":\"LAUDA EUROPE\"},{\"type\":\"airline\",\"iataCode\":\"LX\",\"icaoCode\":\"SWR\",\"businessName\":\"SWISS INTERNATIONAL AIR LINES\",\"commonName\":\"SWISS\"},{\"type\":\"airline\",\"iataCode\":\"VL\",\"icaoCode\":\"LHX\",\"businessName\":\"Lufthansa City\",\"commonName\":\"CITY AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"RC\",\"icaoCode\":\"FLI\",\"businessName\":\"ATLANTIC AIRWAYS\",\"commonName\":\"ATL.AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"RD\",\"businessName\":\"SKY CANA\",\"commonName\":\"SKY CANA\"},{\"type\":\"airline\",\"iataCode\":\"LB\",\"icaoCode\":\"LXX\",\"businessName\":\"LEPL\",\"commonName\":\"LIBYAN EXPRESS\"},{\"type\":\"airline\",\"iataCode\":\"OV\",\"icaoCode\":\"OMS\",\"businessName\":\"SALAM AIR\",\"commonName\":\"SALAM AIR\"},{\"type\":\"airline\",\"iataCode\":\"M3\",\"businessName\":\"Undefined\"},{\"type\":\"airline\",\"iataCode\":\"M4\",\"businessName\":\"MISTRAL AIR\",\"commonName\":\"MISTRAL AIR\"},{\"type\":\"airline\",\"iataCode\":\"M6\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"M7\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"PM\",\"icaoCode\":\"CNF\",\"businessName\":\"Canaryfly\",\"commonName\":\"CANARY FLY\"},{\"type\":\"airline\",\"iataCode\":\"PN\",\"businessName\":\"WEST AIR\",\"commonName\":\"WEST AIR\"},{\"type\":\"airline\",\"iataCode\":\"PO\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"K2\",\"businessName\":\"PAKLOOK AIR\",\"commonName\":\"PAKLOOK AIR\"},{\"type\":\"airline\",\"iataCode\":\"K3\",\"icaoCode\":\"SAQ\",\"businessName\":\"SAFE AIR\",\"commonName\":\"Safe Air\"},{\"type\":\"airline\",\"iataCode\":\"K4\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"UY\",\"icaoCode\":\"CSG\",\"businessName\":\"AIR CAUCASUS\",\"commonName\":\"AIR CAUCASUS\"},{\"type\":\"airline\",\"iataCode\":\"UZ\",\"icaoCode\":\"BRQ\",\"businessName\":\"BURAQ AIR\",\"commonName\":\"BURAQ AIR\"},{\"type\":\"airline\",\"iataCode\":\"V0\",\"icaoCode\":\"VCV\",\"businessName\":\"CONVIASA\",\"commonName\":\"CONVIASA\"},{\"type\":\"airline\",\"iataCode\":\"V1\",\"businessName\":\"IBS SOFTWARE SERVICES AMERICAS\"},{\"type\":\"airline\",\"iataCode\":\"V2\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"V3\",\"icaoCode\":\"KRP\",\"businessName\":\"CARPATAIR\",\"commonName\":\"CARPATAIR\"},{\"type\":\"airline\",\"iataCode\":\"V4\",\"businessName\":\"VIEQUES AIR\",\"commonName\":\"VIEQUES AIR\"},{\"type\":\"airline\",\"iataCode\":\"KC\",\"icaoCode\":\"KZR\",\"businessName\":\"JSC AIR ASTANA\",\"commonName\":\"AIR ASTANA\"},{\"type\":\"airline\",\"iataCode\":\"KD\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"KE\",\"icaoCode\":\"KAL\",\"businessName\":\"KOREAN AIR\",\"commonName\":\"KOREAN AIR\"},{\"type\":\"airline\",\"iataCode\":\"KI\",\"icaoCode\":\"SJB\",\"businessName\":\"SKS AIRWAYS\",\"commonName\":\"SKS AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"KJ\",\"icaoCode\":\"AIH\",\"businessName\":\"AIR INCHEON\",\"commonName\":\"AIR INCHEON\"},{\"type\":\"airline\",\"iataCode\":\"KM\",\"icaoCode\":\"KMM\",\"businessName\":\"KM MALTA AIRLINES\",\"commonName\":\"KM MALTA AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"6T\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"J8\",\"icaoCode\":\"BVT\",\"businessName\":\"BERJAYA AIR\",\"commonName\":\"BERJAYA AIR\"},{\"type\":\"airline\",\"iataCode\":\"FL\",\"icaoCode\":\"LPA\",\"businessName\":\"AIR LEAP AVIATION\",\"commonName\":\"AIR LEAP AVIATION\"},{\"type\":\"airline\",\"iataCode\":\"V9\",\"businessName\":\"VAN AIR EUROPE\",\"commonName\":\"VAN AIR EUROPE\"},{\"type\":\"airline\",\"iataCode\":\"VA\",\"icaoCode\":\"VOZ\",\"businessName\":\"VIRGIN AUSTRALIA INTL\",\"commonName\":\"VIRGIN AUSTRALIA\"},{\"type\":\"airline\",\"iataCode\":\"R7\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"DH\",\"businessName\":\"FLYADEN\",\"commonName\":\"FLYADEN\"},{\"type\":\"airline\",\"iataCode\":\"CV\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"CW\",\"icaoCode\":\"SCW\",\"businessName\":\"SKYWEST CHARTER LLC\",\"commonName\":\"SKYWEST CHARTER LLC\"},{\"type\":\"airline\",\"iataCode\":\"AV\",\"icaoCode\":\"AVA\",\"businessName\":\"AVIANCA\",\"commonName\":\"AVIANCA\"},{\"type\":\"airline\",\"iataCode\":\"O5\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"O6\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"O8\",\"businessName\":\"MARATHON AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"O9\",\"businessName\":\"NOVA AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"OA\",\"icaoCode\":\"OAL\",\"businessName\":\"OLYMPIC AIR\",\"commonName\":\"OLYMPIC AIR\"},{\"type\":\"airline\",\"iataCode\":\"TG\",\"icaoCode\":\"THA\",\"businessName\":\"THAI AIRWAYS INTERNATIONAL\",\"commonName\":\"THAI\"},{\"type\":\"airline\",\"iataCode\":\"OE\",\"icaoCode\":\"LDM\",\"businessName\":\"LAUDAMOTION\",\"commonName\":\"LAUDAMOTION\"},{\"type\":\"airline\",\"iataCode\":\"OG\",\"icaoCode\":\"FPY\",\"businessName\":\"FLY PLAY\",\"commonName\":\"FLY PLAY\"},{\"type\":\"airline\",\"iataCode\":\"OH\",\"icaoCode\":\"JIA\",\"businessName\":\"PSA AIRLINES\",\"commonName\":\"PSA AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"TH\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"KL\",\"icaoCode\":\"KLM\",\"businessName\":\"KLM ROYAL DUTCH AIRLINES\",\"commonName\":\"KLM\"},{\"type\":\"airline\",\"iataCode\":\"LD\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"TU\",\"icaoCode\":\"TAR\",\"businessName\":\"TUNISAIR\",\"commonName\":\"TUNIS AIR\"},{\"type\":\"airline\",\"iataCode\":\"VP\",\"icaoCode\":\"VQI\",\"businessName\":\"VILLA AIR\",\"commonName\":\"VILLA AIR\"},{\"type\":\"airline\",\"iataCode\":\"VQ\",\"businessName\":\"NOVOAIR\",\"commonName\":\"NOVOAIR\"},{\"type\":\"airline\",\"iataCode\":\"OX\",\"icaoCode\":\"OEW\",\"businessName\":\"LATTITUDE HUB (ONE AIRWAYS)\",\"commonName\":\"ONE AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"OY\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"OZ\",\"icaoCode\":\"AAR\",\"businessName\":\"ASIANA AIRLINES\",\"commonName\":\"ASIANA\"},{\"type\":\"airline\",\"iataCode\":\"P2\",\"icaoCode\":\"XAK\",\"businessName\":\"AIRKENYA EXPRESS\",\"commonName\":\"AIRKENYA EXPRESS\"},{\"type\":\"airline\",\"iataCode\":\"P3\",\"icaoCode\":\"POE\",\"businessName\":\"PORTER AIRLINES INC\",\"commonName\":\"PORTER AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"P4\",\"icaoCode\":\"APK\",\"businessName\":\"AIR PEACE LIMITED\",\"commonName\":\"AIR PEACE LIMITED\"},{\"type\":\"airline\",\"iataCode\":\"P5\",\"icaoCode\":\"RPB\",\"businessName\":\"AERO REPUBLICA\",\"commonName\":\"AERO REPUBLICA\"},{\"type\":\"airline\",\"iataCode\":\"P6\",\"icaoCode\":\"PSC\",\"businessName\":\"PASCAN\",\"commonName\":\"PASCAN\"},{\"type\":\"airline\",\"iataCode\":\"P7\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"P8\",\"icaoCode\":\"SRN\",\"businessName\":\"SPRINTAIR\",\"commonName\":\"SPRINTAIR\"},{\"type\":\"airline\",\"iataCode\":\"P9\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"PA\",\"businessName\":\"AIRBLUE\",\"commonName\":\"AIRBLUE\"},{\"type\":\"airline\",\"iataCode\":\"PB\",\"icaoCode\":\"SPR\",\"businessName\":\"PAL AIRLINES\",\"commonName\":\"PAL AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"PC\",\"icaoCode\":\"PGT\",\"businessName\":\"PEGASUS AIRLINES\",\"commonName\":\"PEGASUS AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"T5\",\"icaoCode\":\"TUA\",\"businessName\":\"TURKMENISTAN AIRLINES\",\"commonName\":\"TURKMENISTAN AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"T8\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"T9\",\"icaoCode\":\"VTU\",\"businessName\":\"TURPIAL AIRLINES\",\"commonName\":\"TURPIAL AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"OL\",\"icaoCode\":\"PAO\",\"businessName\":\"SAMOA AIRWAYS\",\"commonName\":\"SAMOA AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"WW\",\"icaoCode\":\"VNE\",\"businessName\":\"RAVSA\",\"commonName\":\"RAVSA\"},{\"type\":\"airline\",\"iataCode\":\"WX\",\"icaoCode\":\"BCY\",\"businessName\":\"CITYJET\",\"commonName\":\"CITYJET\"},{\"type\":\"airline\",\"iataCode\":\"VM\",\"icaoCode\":\"NGL\",\"businessName\":\"MAX AIR LIMITED\",\"commonName\":\"MAX AIR LIMITED\"},{\"type\":\"airline\",\"iataCode\":\"RL\",\"icaoCode\":\"ABG\",\"businessName\":\"ROYAL FLIGHT AIRLINES\",\"commonName\":\"ROYAL FLIGHT AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"SH\",\"businessName\":\"SHARP AVIATION\",\"commonName\":\"SHARP AVIATION\"},{\"type\":\"airline\",\"iataCode\":\"OM\",\"icaoCode\":\"MGL\",\"businessName\":\"MIAT MONGOLIAN AIRLINES\",\"commonName\":\"MIAT\"},{\"type\":\"airline\",\"iataCode\":\"VN\",\"icaoCode\":\"HVN\",\"businessName\":\"VIETNAM AIRLINES\",\"commonName\":\"VIETNAM AIRL\"},{\"type\":\"airline\",\"iataCode\":\"KP\",\"icaoCode\":\"SKK\",\"businessName\":\"ASKY\",\"commonName\":\"ASKY\"},{\"type\":\"airline\",\"iataCode\":\"KQ\",\"icaoCode\":\"KQA\",\"businessName\":\"KENYA AIRWAYS\",\"commonName\":\"KENYAAIRWAY\"},{\"type\":\"airline\",\"iataCode\":\"OT\",\"icaoCode\":\"CDO\",\"businessName\":\"TCHADIA AIRLINES\",\"commonName\":\"TCHADIA AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"D2\",\"businessName\":\"SEVERSTAL AIRCOMPANY\",\"commonName\":\"SEVERSTAL AIRCOMPANY\"},{\"type\":\"airline\",\"iataCode\":\"G0\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"A4\",\"businessName\":\"JSC AZIMUTH AIRLINES\",\"commonName\":\"JSC AZIMUTH AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"ZQ\",\"icaoCode\":\"GER\",\"businessName\":\"GERMAN AIRWAYS\",\"commonName\":\"GERMAN AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"T3\",\"icaoCode\":\"EZE\",\"businessName\":\"EASTERN AIRWAYS\",\"commonName\":\"EASTERN AIRWAYS\"},{\"type\":\"airline\",\"iataCode\":\"E0\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"4E\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"0Y\",\"icaoCode\":\"XYY\",\"businessName\":\"AMADEUS AIRLINE 0Y\"},{\"type\":\"airline\",\"iataCode\":\"IK\",\"businessName\":\"AIR KIRIBATI\",\"commonName\":\"AIR KIRIBATI\"},{\"type\":\"airline\",\"iataCode\":\"CF\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"BH\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"MM\",\"icaoCode\":\"APJ\",\"businessName\":\"PEACH AVIATION\",\"commonName\":\"PEACH AVIATION\"},{\"type\":\"airline\",\"iataCode\":\"F3\",\"icaoCode\":\"FAD\",\"businessName\":\"FLYADEAL\",\"commonName\":\"FLYADEAL\"},{\"type\":\"airline\",\"iataCode\":\"WH\",\"businessName\":\"UNDEFINED\",\"commonName\":\"WEST AFRICAN AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"BK\",\"icaoCode\":\"OKA\",\"businessName\":\"OKAY AIRWAYS COMPANY LIMITED\"},{\"type\":\"airline\",\"iataCode\":\"Y2\",\"icaoCode\":\"CEY\",\"businessName\":\"AIR CENTURY\",\"commonName\":\"AIR CENTURY\"},{\"type\":\"airline\",\"iataCode\":\"EC\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"NA\",\"businessName\":\"NESMA AIRLINES\",\"commonName\":\"NESMA AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"II\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"WJ\",\"icaoCode\":\"JES\",\"businessName\":\"JETSMART AIRLINES\",\"commonName\":\"JETSMART AIRLINES\"},{\"type\":\"airline\",\"iataCode\":\"AU\",\"icaoCode\":\"CJL\",\"businessName\":\"CANADA JETLINES OPERATIONS\",\"commonName\":\"CANADA JETLINES\"},{\"type\":\"airline\",\"iataCode\":\"RI\",\"businessName\":\"MANDALA AIRLINES\",\"commonName\":\"PT MANDALA\"},{\"type\":\"airline\",\"iataCode\":\"KK\",\"icaoCode\":\"NGN\",\"businessName\":\"LEAV AVIATION GMBH\",\"commonName\":\"LEAV AVIATION GMBH\"},{\"type\":\"airline\",\"iataCode\":\"VO\",\"icaoCode\":\"UVL\",\"businessName\":\"UNIVERSAL AIR CHARTER AND MANAGE\",\"commonName\":\"UNIVERSAL AIR CHARTER AN\"},{\"type\":\"airline\",\"iataCode\":\"KT\",\"icaoCode\":\"HOG\",\"businessName\":\"MAHOGANY AIR\",\"commonName\":\"MAHOGANY AIR\"},{\"type\":\"airline\",\"iataCode\":\"CC\",\"businessName\":\"UNDEFINED\"},{\"type\":\"airline\",\"iataCode\":\"QN\",\"icaoCode\":\"SKP\",\"businessName\":\"SKYTRANS\",\"commonName\":\"Skytrans\"}]}"}]}, "connections": {"Wait": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "FromTo": {"main": [[{"node": "CarrierNameLookup", "type": "main", "index": 0}]]}, "Get Dates": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Under Price": {"main": [[{"node": "Gmail", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Merge & Extract", "type": "main", "index": 0}], [{"node": "Amadeus Flight Search", "type": "main", "index": 0}]]}, "Merge & Extract": {"main": [[{"node": "Under Price", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "FromTo", "type": "main", "index": 0}]]}, "CarrierNameLookup": {"main": [[{"node": "Get Dates", "type": "main", "index": 0}]]}, "Amadeus Flight Search": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}}}