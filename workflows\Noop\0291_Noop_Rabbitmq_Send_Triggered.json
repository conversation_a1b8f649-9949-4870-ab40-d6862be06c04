{"id": "186", "name": "Receive messages from a queue via RabbitMQ and send an SMS", "nodes": [{"name": "RabbitMQ", "type": "n8n-nodes-base.rabbitmqTrigger", "position": [520, 220], "parameters": {"queue": "temp", "options": {"onlyContent": true, "jsonParseBody": true}}, "credentials": {"rabbitmq": "RabbitMQ Credentials"}, "typeVersion": 1}, {"name": "IF", "type": "n8n-nodes-base.if", "position": [720, 220], "parameters": {"conditions": {"number": [{"value1": "={{$node[\"RabbitMQ\"].json[\"temp\"]}}", "value2": 50, "operation": "larger"}]}}, "typeVersion": 1}, {"name": "Vonage", "type": "n8n-nodes-base.von<PERSON>", "position": [930, 120], "parameters": {"message": "=Alert!\nThe value of temp is {{$node[\"RabbitMQ\"].json[\"temp\"]}}.", "additionalFields": {}}, "credentials": {"vonageApi": "vonage"}, "typeVersion": 1}, {"name": "NoOp", "type": "n8n-nodes-base.noOp", "position": [920, 370], "parameters": {}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"IF": {"main": [[{"node": "Vonage", "type": "main", "index": 0}], [{"node": "NoOp", "type": "main", "index": 0}]]}, "RabbitMQ": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}}}