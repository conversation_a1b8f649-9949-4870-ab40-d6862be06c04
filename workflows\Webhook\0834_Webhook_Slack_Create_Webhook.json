{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "e37622d2-d9d4-4aff-8c0f-a2945e739ccd", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-180, 40], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "702c21cf-6ca5-4b1b-8511-fd082152e50b", "name": "Search All Outlook Events", "type": "n8n-nodes-base.microsoftOutlookTool", "position": [180, 40], "webhookId": "486fda30-984a-4af6-990f-d5f30865fc29", "parameters": {"limit": 20, "filters": {"custom": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Filter_Query', ``, 'string') }}"}, "resource": "event", "descriptionType": "manual", "toolDescription": "Call this tool to consume Microsoft Outlook API and fetch all outlook calendar events across all available calendars for a given filter."}, "credentials": {"microsoftOutlookOAuth2Api": {"id": "EWg6sbhPKcM5y3Mr", "name": "Microsoft Outlook account"}}, "typeVersion": 2}, {"id": "c4d7571d-0d96-42f5-a1dd-d2ee8e467731", "name": "Create New Calendar Event", "type": "n8n-nodes-base.microsoftOutlookTool", "position": [320, 40], "webhookId": "c4f72f45-2c3f-49cf-ac16-6b8fe701cc41", "parameters": {"subject": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Title', ``, 'string') }}", "resource": "event", "operation": "create", "calendarId": {"__rl": true, "mode": "id", "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Calendar', ``, 'string') }}"}, "endDateTime": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('End', ``, 'string') }}", "startDateTime": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Start', ``, 'string') }}", "descriptionType": "manual", "toolDescription": "Call this tool to consume Microsoft Outlook API and create a new outlook calendar event. Ensure the calendar ID exists before proceeding.", "additionalFields": {"body": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Description', ``, 'string') }}"}}, "credentials": {"microsoftOutlookOAuth2Api": {"id": "EWg6sbhPKcM5y3Mr", "name": "Microsoft Outlook account"}}, "typeVersion": 2}, {"id": "db5e44ab-7ec8-4831-9e41-34c963cd2314", "name": "Get Available Calendars", "type": "n8n-nodes-base.microsoftOutlookTool", "position": [460, 40], "webhookId": "605be4f6-e8c4-4350-9da9-55988b069c5d", "parameters": {"limit": 20, "filters": {}, "resource": "calendar", "descriptionType": "manual", "toolDescription": "Call this tool to consume Microsoft Outlook API and fetch a list of available calendars."}, "credentials": {"microsoftOutlookOAuth2Api": {"id": "EWg6sbhPKcM5y3Mr", "name": "Microsoft Outlook account"}}, "typeVersion": 2}, {"id": "8102e365-eec4-48c6-986b-4ab8aac9e72a", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [-20, 40], "parameters": {"sessionKey": "={{ $json.ts }}_{{ $json.user }}", "sessionIdType": "customKey"}, "typeVersion": 1.3}, {"id": "ebd79d18-86b9-4e8b-9a27-f9878fd3d617", "name": "Outlook Calendar Assistant", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-60, -180], "parameters": {"text": "={{ $json.message.substr($json.message.indexOf('>')+1, 9999).trim() }}", "options": {"systemMessage": "=You are a helpful calendar assistant who can help users with calendar and event enquiries.\n* Today's date and time is {{ $now.toISO() }}."}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "f976cea5-be3e-4e14-89f5-b5d05d66f0c7", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1340, -860], "parameters": {"width": 440, "height": 720, "content": "### This n8n template demonstrates how easy it is to build an Outlook Calendar Assistant powered by an AI agent equipped with Tools.\n\nn8n's AI agents makes it easy to build powerful assistants which can interact with your existing services and tools. With little effort, you can expose such an agent to team members and colleagues though something like <PERSON>lack and enable a company-wide productivity booster.\n\n### How it works\n* A Slack Trigger node is configured to catch \"bot mentions\" events in a designated channel.\n* The message is parsed using the Edit fields node to extract only the required attributes of the event.\n* An AI Agent equipped with Outlook Calendar Tools enables question and answer capability for the organisation's shared calendars and events.\n* The AI agent's response is sent back to <PERSON><PERSON>ck as a reply to the user's query.\n\n### How to use\n* The workflow is triggered via @mention-ing the bot followed by the query. eg. \"@bot how many meetings does <PERSON> have to attend to this week?\"\n* To start listening to real mentions, you must activate the workflow and set it to production mode. You must use the production webhook URL for the event subscription.\n\n\n### Need Help?\nJoin the [Discord](https://discord.com/invite/XPKeKXeB7d) or ask in the [Forum](https://community.n8n.io/)!"}, "typeVersion": 1}, {"id": "03083765-b3bb-42f6-8f30-7087687bc6eb", "name": "Send Reply", "type": "n8n-nodes-base.slack", "position": [620, -180], "webhookId": "68154e10-0b98-4d18-816c-2af8ab954694", "parameters": {"text": "={{ $json.output }}", "select": "channel", "channelId": {"__rl": true, "mode": "id", "value": "={{ $('Get Message').item.json.channel }}"}, "otherOptions": {"thread_ts": {"replyValues": {"thread_ts": "={{ $('Get Message').item.json.ts }}"}}, "includeLinkToWorkflow": false}}, "credentials": {"slackApi": {"id": "VfK3js0YdqBdQLGP", "name": "Slack account"}}, "typeVersion": 2.3}, {"id": "19c8e68b-2bf1-403a-a43d-cdc465233436", "name": "Respond to Challenge", "type": "n8n-nodes-base.respondToWebhook", "position": [-240, -440], "parameters": {"options": {}, "respondWith": "text", "responseBody": "={{ $json.body.challenge }}"}, "typeVersion": 1.1}, {"id": "2b9f7d68-8e76-440b-9a8b-b9eb4fc7061c", "name": "Is Auth Challenge?", "type": "n8n-nodes-base.if", "position": [-520, -300], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "cd56f5f2-dbb8-4cf0-83c8-f0566510ff51", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.body.challenge }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "c91350ef-5701-4188-8b1f-de12a0076a56", "name": "Get Message", "type": "n8n-nodes-base.set", "position": [-240, -180], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "44da9c3a-35eb-4636-9483-65492e858d96", "name": "ts", "type": "string", "value": "={{ $json.body.event.ts }}"}, {"id": "761840aa-d2e3-4345-95bb-e7866b755880", "name": "message", "type": "string", "value": "={{ $json.body.event.text }}"}, {"id": "094457fc-c149-4175-bed2-f0906cb70dea", "name": "is_bot", "type": "boolean", "value": "={{ $json.body.authorizations[0].is_bot }}"}, {"id": "baf91a59-88fa-45fc-bfcb-ff27d0fe397d", "name": "user", "type": "string", "value": "={{ $json.body.event.user }}"}, {"id": "abc6c16e-50e2-4154-9db9-4e12f9009d01", "name": "channel", "type": "string", "value": "={{ $json.body.event.channel }}"}]}}, "typeVersion": 3.4}, {"id": "0681782d-21f3-4130-809c-188d83ebb7a9", "name": "On BOT/APP Mention", "type": "n8n-nodes-base.webhook", "position": [-800, -300], "webhookId": "c63b08ce-360d-4185-aae1-294afef5cf2b", "parameters": {"path": "c63b08ce-360d-4185-aae1-294afef5cf2b", "options": {}, "httpMethod": "POST", "responseMode": "responseNode"}, "typeVersion": 2}, {"id": "d0b12a70-e3e8-4149-98ba-dc2cf01f9953", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-800, -520], "parameters": {"color": 7, "width": 380, "height": 180, "content": "## 1. Listen for <PERSON><PERSON> Mentions\n[Read more about Webhook Trigger](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.webhook)\n\n**Example**:\n`@bot how many meetings does <PERSON> have to attend to this week?` "}, "typeVersion": 1}, {"id": "095fd13e-a660-46a8-95c6-b960083681f7", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [0, -440], "parameters": {"color": 7, "width": 540, "height": 220, "content": "## 2. AI Agent with Outlook Calendar Tools\n[Learn more about the AI Agent node](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.agent)\n\nThis agent has 3 Outlook tools to search, browse and even create calendar events for the user. Agents are great in that we don't have to tell the agent what and when to use the tools - it'll make that decision on its own!"}, "typeVersion": 1}, {"id": "3b2662a2-9a79-4848-89db-a5699942f39c", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [620, 0], "parameters": {"color": 7, "width": 400, "height": 200, "content": "## 3. Reply to User\n[Learn more about the Slack node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.slack)\n\nSimple sends a reply back to the user answering their query. Of course, this is the simplest case and it'll be up to you to handle multi-turn conversation as needed."}, "typeVersion": 1}, {"id": "f00e8727-12f2-4dad-8736-98bd0996f19a", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-1340, -120], "parameters": {"color": 5, "width": 440, "height": 340, "content": "### Setting Up Slack App Event Subscriptions\n1. Go to https://api.slack.com/apps\n2. Create or Select your App\n3. Under Features, click into \"Event Subscriptions\"\n4. On this page, toggle on the \"Enable Events\" option\n5. Enter the production URL of this template - your workflow but be active and publicly accessible for this to work.\n6. Slack will issue a \"challenge\" request to this workflow which will respond and verify the subscription.\n7. If successful, under \"subscribe to bot events\", find and select the \"app_mention\" option.\n8. Hit \"save changes\" at the bottom of the page.\n9. This workflow should now trigger when your bot is \"@mention\" in the channel.  "}, "typeVersion": 1}], "pinData": {}, "connections": {"Get Message": {"main": [[{"node": "Outlook Calendar Assistant", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "Outlook Calendar Assistant", "type": "ai_memory", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Outlook Calendar Assistant", "type": "ai_languageModel", "index": 0}]]}, "Is Auth Challenge?": {"main": [[{"node": "Respond to Challenge", "type": "main", "index": 0}], [{"node": "Get Message", "type": "main", "index": 0}]]}, "On BOT/APP Mention": {"main": [[{"node": "Is Auth Challenge?", "type": "main", "index": 0}]]}, "Get Available Calendars": {"ai_tool": [[{"node": "Outlook Calendar Assistant", "type": "ai_tool", "index": 0}]]}, "Create New Calendar Event": {"ai_tool": [[{"node": "Outlook Calendar Assistant", "type": "ai_tool", "index": 0}]]}, "Search All Outlook Events": {"ai_tool": [[{"node": "Outlook Calendar Assistant", "type": "ai_tool", "index": 0}]]}, "Outlook Calendar Assistant": {"main": [[{"node": "Send Reply", "type": "main", "index": 0}]]}}}