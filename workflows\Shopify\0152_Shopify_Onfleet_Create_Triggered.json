{"id": 13, "name": "Creating an Onfleet Task for a new Shopify Fulfillment", "nodes": [{"name": "Shopify Trigger", "type": "n8n-nodes-base.shopifyTrigger", "position": [240, 440], "webhookId": "576e8785-bbb4-426b-a922-da671efced68", "parameters": {"topic": "fulfillments/create"}, "credentials": {"shopifyApi": {"id": "6", "name": "Shopify account"}}, "typeVersion": 1}, {"name": "Onfleet", "type": "n8n-nodes-base.onfleet", "position": [460, 440], "parameters": {"operation": "create", "additionalFields": {}}, "credentials": {"onfleetApi": {"id": "2", "name": "Onfleet API Key"}}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Shopify Trigger": {"main": [[{"node": "Onfleet", "type": "main", "index": 0}]]}}}