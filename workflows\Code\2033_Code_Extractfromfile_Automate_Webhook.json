{"id": "zMtPPjJ80JJznrJP", "meta": {"instanceId": "a4bfc93e975ca233ac45ed7c9227d84cf5a2329310525917adaf3312e10d5462", "templateCredsSetupCompleted": true}, "name": "AI-Powered WhatsApp Chatbot for Text, Voice, Images & PDFs", "tags": [], "nodes": [{"id": "38246f5d-0cf4-49ed-957e-0189243d0dec", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.whatsAppTrigger", "position": [-700, 80], "webhookId": "d3978cae-2aca-4553-8ac7-ab89068deabc", "parameters": {"options": {}, "updates": ["messages"]}, "credentials": {"whatsAppTriggerApi": {"id": "gylriO2te7NRPXxN", "name": "WhatsApp OAuth account"}}, "typeVersion": 1}, {"id": "4cc0b70b-3ecc-4415-af2f-e50d4f302786", "name": "Download Image", "type": "n8n-nodes-base.httpRequest", "position": [720, 120], "parameters": {"url": "={{ $json.url }}", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "FDP9FxauzJt9rkjr", "name": "WhatsApp"}}, "typeVersion": 4.2}, {"id": "528984be-b9ad-41c7-8b2e-ccbf275f7805", "name": "Analyze Image", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [960, 120], "parameters": {"text": "=You are an advanced image description AI assistant . Your primary function is to provide detailed, accurate descriptions of images submitted through WhatsApp.\n\nCORE FUNCTIONALITY:\n- When presented with an image, you will analyze it thoroughly and provide a comprehensive description in English.\n- Your descriptions should capture both the obvious and subtle elements within the image.\n\nIMAGE DESCRIPTION GUIDELINES:\n- Begin with a broad overview of what the image contains\n- Describe key subjects, people, objects, and their relationships\n- Note significant visual elements such as colors, lighting, composition, and perspective\n- Identify any text visible in the image\n- Describe the setting or environment\n- Mention any notable actions or events taking place\n- Comment on mood, tone, or atmosphere when relevant\n- If applicable, identify landmarks, famous people, or cultural references\n\nRESPONSE FORMAT:\n- Start with \"Image Description:\" followed by your analysis\n- Structure your description in a logical manner (general to specific)\n- Use clear, precise language appropriate for visual description\n- Format longer descriptions with paragraphs to enhance readability\n- End with any notable observations that might require special attention\n\nLIMITATIONS:\n- If the image is blurry, low resolution, or difficult to interpret, acknowledge these limitations\n- If an image contains potentially sensitive content, provide a factual description without judgment\n- Do not make assumptions about elements that cannot be clearly determined\n\nYour descriptions should be informative, objective, and thorough, enabling someone who cannot see the image to form an accurate mental picture of its contents.", "modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {"detail": "auto"}, "resource": "image", "inputType": "base64", "operation": "analyze"}, "credentials": {"openAiApi": {"id": "4zwP0MSr8zkNvvV9", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "b8898138-f987-4ef7-a5c1-d6d6b9c815f0", "name": "Download Audio", "type": "n8n-nodes-base.httpRequest", "position": [720, -180], "parameters": {"url": "={{ $json.url }}", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "FDP9FxauzJt9rkjr", "name": "WhatsApp"}}, "typeVersion": 4.2}, {"id": "e68bea55-f43a-4143-afca-1348b35e5879", "name": "Transcribe Audio", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [960, -180], "parameters": {"options": {}, "resource": "audio", "operation": "transcribe"}, "credentials": {"openAiApi": {"id": "4zwP0MSr8zkNvvV9", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "1c0aa0c3-cee4-40f1-9e13-9365cc06443a", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [160, 1440], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "4zwP0MSr8zkNvvV9", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "d2215cf8-49e1-433b-b9c3-a219e6432cba", "name": "AI Agent1", "type": "@n8n/n8n-nodes-langchain.agent", "position": [160, 1220], "parameters": {"text": "={{ $json.text }}", "options": {"systemMessage": "You are an intelligent assistant. Your purpose is to analyze various types of input and provide helpful, accurate responses.\n\nCAPABILITIES:\n- Process and respond to text messages\n- Analyze uploaded files\n- Interpret and describe images\n- Transcribe and understand voice messages\n\nINPUT HANDLING:\n1. For text messages: Analyze the content, understand the intent, and provide a relevant response.\n2. For file analysis: Examine the file content, extract key information, and summarize important points also based on the questions asked.\n3. For image analysis: Describe what you see in the image, identify key elements, and respond to any questions about the image.\n4. For voice messages: Transcribe the audio, understand the message, and respond appropriately.\n\nRESPONSE GUIDELINES:\n- Be concise but thorough\n- Prioritize accuracy over speculation\n- Maintain a professional and helpful tone\n- When uncertain, acknowledge limitations\n- Format responses for easy reading on mobile devices\n- Include actionable information when appropriate\n\nLIMITATIONS:\n- Mention if you're unable to process certain file formats\n- Indicate if an image is unclear or if details are difficult to discern\n- Note if audio quality impacts transcription accuracy\n\nSECURITY & PRIVACY:\n- Do not store or remember sensitive information shared in files, images, or voice notes\n- Do not share personal information across different user interactions\n- Inform users about data privacy limitations when relevant\n\nAnalyze all inputs carefully before responding. Your goal is to provide value through accurate information and helpful assistance."}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "103825d0-8521-43c7-8246-9cc1faca42e1", "name": "Download File", "type": "n8n-nodes-base.httpRequest", "position": [720, 460], "parameters": {"url": "={{ $json.url }}", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "FDP9FxauzJt9rkjr", "name": "WhatsApp"}}, "typeVersion": 4.2}, {"id": "98077f98-6dcf-4932-9363-19bf1fdc299e", "name": "Extract from File", "type": "n8n-nodes-base.extractFromFile", "position": [980, 460], "parameters": {"options": {}, "operation": "pdf"}, "typeVersion": 1}, {"id": "fefc8b87-d64e-4ff9-9f62-1ac3af2bf17e", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [300, 1440], "parameters": {"sessionKey": "=memory_{{ $('WhatsApp Trigger').item.json.contacts[0].wa_id }}", "sessionIdType": "customKey", "contextWindowLength": 10}, "typeVersion": 1.3}, {"id": "83ac997d-6ba0-4eb7-bd6c-80671f03c56c", "name": "Get File Url", "type": "n8n-nodes-base.whatsApp", "position": [500, 460], "webhookId": "280bd5de-32d7-4d8f-93d2-e91e3b0bc161", "parameters": {"resource": "media", "operation": "mediaUrlGet", "mediaGetId": "={{ $('WhatsApp Trigger').item.json.messages[0].document.id }}"}, "credentials": {"whatsAppApi": {"id": "HDUOWQXeRXMVjo0Z", "name": "WhatsApp account"}}, "typeVersion": 1}, {"id": "56b23f60-57c3-4ea7-a4e8-64029a2e44c1", "name": "Only PDF File", "type": "n8n-nodes-base.if", "position": [220, 480], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "f52d2aaa-e0b2-45e5-8c4b-ceef42182a0d", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.messages[0].document.mime_type }}", "rightValue": "application/pdf"}]}}, "typeVersion": 2.2}, {"id": "fa733b7b-16a7-4a9b-86b3-88395535ecd5", "name": "Fix mimeType for Audio", "type": "n8n-nodes-base.code", "position": [1040, 1080], "parameters": {"jsCode": "for (const item of $input.all()) {\n  if (item.binary) {\n    const binaryPropertyNames = Object.keys(item.binary);\n    for (const propName of binaryPropertyNames) {\n      if (item.binary[propName].mimeType === 'audio/mp3') {\n        item.binary[propName].mimeType = 'audio/mpeg';\n      }\n    }\n  }\n}\n\nreturn $input.all();"}, "typeVersion": 2}, {"id": "0d99c2fa-945b-487a-b929-742e8b1b6859", "name": "Send message", "type": "n8n-nodes-base.whatsApp", "position": [840, 1360], "webhookId": "********-5066-48ba-8e19-549680df2b27", "parameters": {"textBody": "={{ $json.output }}", "operation": "send", "phoneNumberId": "***************", "additionalFields": {}, "recipientPhoneNumber": "={{ $('WhatsApp Trigger').item.json.messages[0].from }}"}, "credentials": {"whatsAppApi": {"id": "HDUOWQXeRXMVjo0Z", "name": "WhatsApp account"}}, "typeVersion": 1}, {"id": "046328e9-e948-479c-ac42-567877350f1e", "name": "Send audio", "type": "n8n-nodes-base.whatsApp", "position": [1260, 1080], "webhookId": "d18b2c98-84e4-43cf-a532-0c47d5161684", "parameters": {"mediaPath": "useMedian8n", "operation": "send", "messageType": "audio", "phoneNumberId": "***************", "additionalFields": {}, "recipientPhoneNumber": "={{ $('Input type').item.json.contacts[0].wa_id }}"}, "credentials": {"whatsAppApi": {"id": "HDUOWQXeRXMVjo0Z", "name": "WhatsApp account"}}, "typeVersion": 1}, {"id": "aa20a408-42ab-4011-9cea-331e23cda4ce", "name": "Incorrect format", "type": "n8n-nodes-base.whatsApp", "position": [500, 700], "webhookId": "********-5066-48ba-8e19-549680df2b27", "parameters": {"textBody": "=Sorry but you can only send PDF files", "operation": "send", "phoneNumberId": "***************", "additionalFields": {}, "recipientPhoneNumber": "={{ $('WhatsApp Trigger').item.json.messages[0].from }}"}, "credentials": {"whatsAppApi": {"id": "HDUOWQXeRXMVjo0Z", "name": "WhatsApp account"}}, "typeVersion": 1}, {"id": "23b3750d-3638-4fd0-bab8-6082f53f19f9", "name": "Text", "type": "n8n-nodes-base.set", "position": [1240, -520], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c53cd9f9-77c1-4331-98ff-bfc9bdf95a3c", "name": "text", "type": "string", "value": "={{ $('WhatsApp Trigger').item.json.messages[0].text.body }}"}]}}, "typeVersion": 3.4}, {"id": "435c020b-826b-4946-b19e-f9663f4f9f23", "name": "Audio", "type": "n8n-nodes-base.set", "position": [1240, -180], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "219577d5-b028-48fc-90be-980f4171ab68", "name": "text", "type": "string", "value": "={{ $json.text }}"}]}}, "typeVersion": 3.4}, {"id": "0139bb33-651e-4f37-901d-ccc705c9833a", "name": "Image", "type": "n8n-nodes-base.set", "position": [1220, 120], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "********-de2e-494a-878e-c2948e8cb6bb", "name": "text", "type": "string", "value": "=User request on the image:\n{{ \"Describe the following image\" || $('WhatsApp Trigger').item.json.messages[0].image.caption }}\n\nImage description:\n{{ $json.content }}"}]}}, "typeVersion": 3.4}, {"id": "d66b7190-f83b-483e-b3f3-8c220e2c815f", "name": "File", "type": "n8n-nodes-base.set", "position": [1240, 460], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "********-de2e-494a-878e-c2948e8cb6bb", "name": "text", "type": "string", "value": "=User request on the file:\n{{ \"Describe this file\" || $('Only PDF File').item.json.messages[0].document.caption }}\n\nFile content:\n{{ $json.text }}"}]}}, "typeVersion": 3.4}, {"id": "20239933-418c-436f-b15b-c293043a0328", "name": "Not supported", "type": "n8n-nodes-base.whatsApp", "position": [-260, 360], "webhookId": "********-5066-48ba-8e19-549680df2b27", "parameters": {"textBody": "=You can only send text messages, images, audio files and PDF documents.", "operation": "send", "phoneNumberId": "***************", "additionalFields": {}, "recipientPhoneNumber": "={{ $('WhatsApp Trigger').item.json.messages[0].from }}"}, "credentials": {"whatsAppApi": {"id": "HDUOWQXeRXMVjo0Z", "name": "WhatsApp account"}}, "typeVersion": 1}, {"id": "117fd705-1f64-4bcc-88db-357df679fa3d", "name": "Get Image Url", "type": "n8n-nodes-base.whatsApp", "position": [480, 120], "webhookId": "280bd5de-32d7-4d8f-93d2-e91e3b0bc161", "parameters": {"resource": "media", "operation": "mediaUrlGet", "mediaGetId": "={{ $('WhatsApp Trigger').item.json.messages[0].image.id }}"}, "credentials": {"whatsAppApi": {"id": "HDUOWQXeRXMVjo0Z", "name": "WhatsApp account"}}, "typeVersion": 1}, {"id": "3bf7364c-6263-4825-aec5-693adaed7d03", "name": "Get Audio Url", "type": "n8n-nodes-base.whatsApp", "position": [460, -180], "webhookId": "87caa300-7204-47b5-959a-94f4a8fbf8cf", "parameters": {"resource": "media", "operation": "mediaUrlGet", "mediaGetId": "={{ $('WhatsApp Trigger').item.json.messages[0].audio.id }}"}, "credentials": {"whatsAppApi": {"id": "HDUOWQXeRXMVjo0Z", "name": "WhatsApp account"}}, "typeVersion": 1}, {"id": "b23f8467-480a-45c1-a7df-e512290a8e13", "name": "Generate Audio Response", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [840, 1080], "parameters": {"input": "={{ $('AI Agent1').item.json.output }}", "voice": "onyx", "options": {}, "resource": "audio"}, "credentials": {"openAiApi": {"id": "4zwP0MSr8zkNvvV9", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "0b139e60-fbf3-43ae-ae3f-40588f135443", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [120, -560], "parameters": {"width": 1340, "height": 240, "content": "## Text"}, "typeVersion": 1}, {"id": "3622ad4c-79c7-479f-a050-ff21d3077c77", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [120, -240], "parameters": {"width": 1340, "height": 240, "content": "## Voice"}, "typeVersion": 1}, {"id": "1f35e179-22d1-4019-a807-21803df51a46", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [120, 80], "parameters": {"width": 1340, "height": 240, "content": "## Image"}, "typeVersion": 1}, {"id": "314b8ae2-e518-44a3-80a5-dc8482ab1fa9", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [120, 420], "parameters": {"width": 1340, "height": 240, "content": "## Document"}, "typeVersion": 1}, {"id": "a55cc899-3490-4b7c-b793-4f20605fc711", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [120, 960], "parameters": {"color": 5, "width": 1340, "height": 600, "content": "## Response"}, "typeVersion": 1}, {"id": "f37e975b-c112-4af8-badd-1fdbdb90d2f5", "name": "From audio to audio?", "type": "n8n-nodes-base.if", "position": [580, 1220], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "b9d1d759-f585-4791-a743-b9d72951e77c", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $('WhatsApp Trigger').item.json.messages[0].audio }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "864be43a-e280-4c6d-bab4-878b88304807", "name": "Input type", "type": "n8n-nodes-base.switch", "position": [-420, 40], "parameters": {"rules": {"values": [{"outputKey": "Text", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "08fd0c80-307e-4f45-b1de-35192ee4ec5e", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.messages[0].text.body }}", "rightValue": ""}]}, "renameOutput": true}, {"outputKey": "Voice", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "b7b64446-f1ea-4622-990c-22f3999a8269", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.messages[0].audio }}", "rightValue": ""}]}, "renameOutput": true}, {"outputKey": "Image", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "202af928-a324-411a-bf15-68a349e7bf9e", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.messages[0].image }}", "rightValue": ""}]}, "renameOutput": true}, {"outputKey": "Document", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "c63299e9-6069-4bc6-afb9-7beebf6e3d69", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.messages[0].document }}", "rightValue": ""}]}, "renameOutput": true}]}, "options": {"fallbackOutput": "extra"}}, "typeVersion": 3.2}, {"id": "cf327372-d2cc-40db-a057-9bfb10d6a520", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-1580, -1140], "parameters": {"color": 3, "width": 780, "height": 2680, "content": "How to obtain Whatsapp API?\n\n\n### ✅ Prerequisites\nBefore you begin, make sure you have:\n- A **Meta Business Account**\n- A **Facebook Developer Account**\n- A **Verified Business**\n- A **Phone Number** to link to WhatsApp\n- Access to **Meta's Graph API Explorer** or **Meta for Developers portal**\n\n---\n\n### 🪜 STEP 1: Create a Meta App\n\n1. Go to [https://developers.facebook.com/apps](https://developers.facebook.com/apps)\n2. Click **“Create App”**\n3. Choose **\"Business\"** as the app type, then click **Next**\n4. Give your app a name and enter a contact email\n5. Choose your Business Account (or create one)\n6. Click **Create App**\n\n---\n\n### 🪜 STEP 2: Add WhatsApp Product\n\n1. In your app dashboard, scroll down to **\"Add a Product\"**\n2. Find **\"WhatsApp\"** and click **“Set Up”**\n3. Link your **Business Manager Account**\n\n---\n\n### 🪜 STEP 3: Create a WhatsApp Business Account (if needed)\n\n1. If you haven’t already, go to [https://business.facebook.com/](https://business.facebook.com/)\n2. Click **“Create Account”**, and complete your business information\n3. Go to **Business Settings > Accounts > WhatsApp Accounts**\n4. Add a **Phone Number** (you'll receive a verification code)\n\n---\n\n### 🪜 STEP 4: Generate a Temporary Access Token (for development)\n\n1. In the **App Dashboard**, go to **WhatsApp > Getting Started**\n2. Choose your test phone number\n3. Copy the **temporary access token** (valid for 24 hours)\n4. Copy the **Phone Number ID** and **WhatsApp Business Account ID**\n\n✅ Save these 3 values:\n- **Access Token**\n- **Phone Number ID**\n- **WABA ID**\n\n📝 Tip: For production, you will later need to create a **permanent token** (see step 7).\n\n---\n\n### 🪜 STEP 5: Set Up a Webhook URL (n8n)\n\n1. In n8n, set up a **Webhook node** (or use the `WhatsApp Trigger` node)\n2. Copy the webhook URL\n3. In the Meta Developer Dashboard:\n   - Go to **WhatsApp > Configuration**\n   - Click **“Edit Callback URL”**\n   - Paste your n8n webhook URL and add a random **verify token**\n4. In n8n, configure your webhook to respond to the verification request\n\n---\n\n### 🪜 STEP 6: Subscribe to Webhook Fields\n\n1. Still under **WhatsApp > Configuration**, click **\"Manage Subscriptions\"**\n2. Enable:\n   - `messages`\n   - `message_status`\n   - (Optionally `message_template_status_update`)\n\n---\n\n### 🪜 STEP 7: (Optional but recommended) Generate a Permanent Token\n\n1. Go to [https://developers.facebook.com/tools/access_token/](https://developers.facebook.com/tools/access_token/)\n2. Select your app\n3. Click **Get Token > System User Token**\n4. Select the permissions:\n   - `whatsapp_business_management`\n   - `whatsapp_business_messaging`\n   - `business_management`\n5. Click **Generate Token**\n6. Copy and securely store this token\n\n---\n\n### 🪜 STEP 8: Configure Credentials in n8n\n\n1. Go to **n8n > Settings > Credentials**\n2. Create new credentials of type **HTTP Header Auth**\n   - **Name:** WhatsApp\n   - **Header Name:** `Authorization`\n   - **Value:** `Bearer <your_access_token>`\n3. Save\n\nThen, in your workflows:\n- Use the HTTP Request node or WhatsApp node\n- Set the `phone_number_id` in the node parameters\n- Connect it to your WhatsApp credential\n\n---\n\n### 🧪 STEP 9: Test the Connection\n\n1. Use a WhatsApp number to send a message to your business phone\n2. Your n8n workflow should be triggered\n3. You can now send and receive messages programmatically 🎉\n"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "4dfe4c80-3a9a-4292-bcb2-3f68bbea5a3a", "connections": {"File": {"main": [[{"node": "AI Agent1", "type": "main", "index": 0}]]}, "Text": {"main": [[{"node": "AI Agent1", "type": "main", "index": 0}]]}, "Audio": {"main": [[{"node": "AI Agent1", "type": "main", "index": 0}]]}, "Image": {"main": [[{"node": "AI Agent1", "type": "main", "index": 0}]]}, "AI Agent1": {"main": [[{"node": "From audio to audio?", "type": "main", "index": 0}]]}, "Input type": {"main": [[{"node": "Text", "type": "main", "index": 0}], [{"node": "Get Audio Url", "type": "main", "index": 0}], [{"node": "Get Image Url", "type": "main", "index": 0}], [{"node": "Only PDF File", "type": "main", "index": 0}], [{"node": "Not supported", "type": "main", "index": 0}]]}, "Get File Url": {"main": [[{"node": "Download File", "type": "main", "index": 0}]]}, "Analyze Image": {"main": [[{"node": "Image", "type": "main", "index": 0}]]}, "Download File": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "Get Audio Url": {"main": [[{"node": "Download Audio", "type": "main", "index": 0}]]}, "Get Image Url": {"main": [[{"node": "Download Image", "type": "main", "index": 0}]]}, "Only PDF File": {"main": [[{"node": "Get File Url", "type": "main", "index": 0}], [{"node": "Incorrect format", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent1", "type": "ai_memory", "index": 0}]]}, "Download Audio": {"main": [[{"node": "Transcribe Audio", "type": "main", "index": 0}]]}, "Download Image": {"main": [[{"node": "Analyze Image", "type": "main", "index": 0}]]}, "Transcribe Audio": {"main": [[{"node": "Audio", "type": "main", "index": 0}]]}, "WhatsApp Trigger": {"main": [[{"node": "Input type", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "File", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent1", "type": "ai_languageModel", "index": 0}]]}, "From audio to audio?": {"main": [[{"node": "Generate Audio Response", "type": "main", "index": 0}], [{"node": "Send message", "type": "main", "index": 0}]]}, "Fix mimeType for Audio": {"main": [[{"node": "Send audio", "type": "main", "index": 0}]]}, "Generate Audio Response": {"main": [[{"node": "Fix mimeType for Audio", "type": "main", "index": 0}]]}}}