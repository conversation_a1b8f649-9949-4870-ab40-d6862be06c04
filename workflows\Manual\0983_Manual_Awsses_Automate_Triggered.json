{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "AWS SES", "type": "n8n-nodes-base.awsSes", "position": [450, 300], "parameters": {"body": "This is a sample message body in an email\n", "subject": "n8n Rocks", "fromEmail": "<EMAIL>", "toAddresses": ["<EMAIL>", "<EMAIL>"], "additionalFields": {}}, "credentials": {"aws": "aws"}, "typeVersion": 1}], "connections": {"On clicking 'execute'": {"main": [[{"node": "AWS SES", "type": "main", "index": 0}]]}}}