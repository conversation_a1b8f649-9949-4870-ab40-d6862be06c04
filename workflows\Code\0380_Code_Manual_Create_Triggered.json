{"meta": {"instanceId": "c59a6b1daf09a846754bc2cf0a94db3299bd5a69fb14687c3a5e692704c548dd"}, "nodes": [{"id": "2165cd37-10ff-46bd-88a5-c8377bf4bef7", "name": "Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [1280, 1100], "parameters": {"limit": 100, "options": {"spaces": ["*"], "corpora": "allDrives"}, "operation": "list", "queryString": "='{{ $json[\"Folder ID\"] }}' in parents", "authentication": "oAuth2", "useQueryString": true}, "credentials": {"googleDriveOAuth2Api": {"id": "KJE0ZORR1Q1fJCd5", "name": "Google Drive account 2"}}, "typeVersion": 1}, {"id": "5061db5e-2137-4c50-8902-a24cd53a6bdf", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [1480, 1160], "parameters": {"options": {}, "batchSize": 50}, "typeVersion": 3}, {"id": "62a16fb8-9bfc-46db-a556-23fac7f403f5", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [1720, 1020], "parameters": {"mode": "combine", "options": {}, "combinationMode": "multiplex"}, "typeVersion": 2.1}, {"id": "bd410148-e745-43a2-960b-128bbb49828f", "name": "Set Folder ID", "type": "n8n-nodes-base.set", "notes": "Enter desired Folder", "position": [1120, 1100], "parameters": {"fields": {"values": [{"name": "Folder ID", "stringValue": "Enter Your Folder ID here"}]}, "options": {}}, "notesInFlow": true, "typeVersion": 3.2}, {"id": "16def9df-5c8b-4359-a879-11e66f191f92", "name": "Manual Execute Workflow", "type": "n8n-nodes-base.manualTrigger", "notes": "Optional", "position": [940, 1100], "parameters": {}, "notesInFlow": true, "typeVersion": 1}, {"id": "e7d54620-e5e6-470e-add5-ccefdfb2a979", "name": "Generate Download Links", "type": "n8n-nodes-base.code", "position": [1480, 980], "parameters": {"jsCode": "// This function will create an array of file links from the given Google Drive folder\nreturn items.map(file => {\n  return { json: { 'link': `https://drive.google.com/u/3/uc?id=${file.json.id}&export=download&confirm=t&authuser=0`, 'name': file.json.name } };\n});"}, "typeVersion": 2}, {"id": "04e71edf-c40f-4c80-961c-f511e145232c", "name": "Change Status", "type": "n8n-nodes-base.googleDrive", "notes": "Make Files Public to anyone with a link", "position": [1660, 1180], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "options": {"supportsAllDrives": true}, "operation": "share", "permissionsUi": {"permissionsValues": {"role": "reader", "type": "anyone"}}, "authentication": "oAuth2"}, "credentials": {"googleDriveOAuth2Api": {"id": "KJE0ZORR1Q1fJCd5", "name": "Google Drive account 2"}}, "notesInFlow": true, "typeVersion": 1}, {"id": "4452cd81-e94a-465e-987b-5acf46e25428", "name": "Replace Me", "type": "n8n-nodes-base.noOp", "position": [1880, 1020], "parameters": {}, "typeVersion": 1}, {"id": "dab69e10-d9af-4ece-a6c6-cb35468e3bf0", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [880, 820], "parameters": {"width": 1235.*************, "height": 545.*************, "content": "## Example Output:\n```JSON\n{\n\"link\": \"https://drive.google.com/u/3/uc?id=1hojqPfXchNTY8YRTNkxSo-8txK9re-V4&export=download&confirm=t&authuser=0\",\n\"name\": \"firefox_rNjA0ybKu7.png\",\n\"kind\": \"drive#permission\",\n\"id\": \"anyoneWithLink\",\n\"type\": \"anyone\",\n\"role\": \"reader\",\n\"allowFileDiscovery\": false\n}\n```\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n### You can store the output data with any data store node you want\n### for example save them into Excel Sheet or Airtable etc..."}, "typeVersion": 1}], "pinData": {}, "connections": {"Merge": {"main": [[{"node": "Replace Me", "type": "main", "index": 0}]]}, "Google Drive": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}, {"node": "Generate Download Links", "type": "main", "index": 0}]]}, "Change Status": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Set Folder ID": {"main": [[{"node": "Google Drive", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}], [{"node": "Change Status", "type": "main", "index": 0}]]}, "Generate Download Links": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Manual Execute Workflow": {"main": [[{"node": "Set Folder ID", "type": "main", "index": 0}]]}}}