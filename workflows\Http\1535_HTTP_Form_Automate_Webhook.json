{"id": "L1UcBZ9UJvN9gnSb", "meta": {"instanceId": "a2b23892dd6989fda7c1209b381f5850373a7d2b85609624d7c2b7a092671d44", "templateCredsSetupCompleted": true}, "name": "💥🛠️Automate Blog Content Creation with GPT-4, Perplexity & WordPress", "tags": [], "nodes": [{"id": "b86a4b08-6fb6-4ebc-8ddb-f1cd0e4b1492", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [0, 0], "parameters": {"color": 4, "width": 460, "height": 300, "content": "## Perplexity Section\n🌐 Calls Perplexity API to get fresh research based on a form input.\n\n\n"}, "typeVersion": 1}, {"id": "16509f9d-ce54-4dab-b3ff-24760b0bde09", "name": "Perplexity Research", "type": "n8n-nodes-base.httpRequest", "position": [80, 100], "parameters": {"url": "https://api.perplexity.ai/chat/completions", "method": "POST", "options": {}, "jsonBody": "={\n  \"model\": \"sonar-pro\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"Act as a professional news researcher who is capable of finding detailed summaries about a news topic from highly reputable sources.\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \" Research the following topic and return everything you can find about: '{{ $json['Topic or Question'] }}'.\"\n    }\n  ]\n}\n", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "DB99xYLrmwZl7Sqf", "name": "Header Auth account"}}, "typeVersion": 4.2}, {"id": "500b2464-88b1-44f5-bcc4-12c0acdc5773", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [560, 0], "webhookId": "b132ff74-2807-4cbf-b5b7-a62a207161d3", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "aec9523b-245a-48ff-a860-3239b869f676", "name": "Slack-List", "type": "n8n-nodes-mcp.mcpClientTool", "position": [1500, 400], "parameters": {}, "credentials": {"mcpClientApi": {"id": "mC6b1h1p0lFikSzU", "name": "slack"}}, "typeVersion": 1}, {"id": "1ecdcfed-c5d4-4ddc-aeb1-e760d295e5bc", "name": "Copywriting AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1020, 100], "parameters": {"text": "=You are an expert in SEO content writing.\n\nYour mission is to create, publish, and notify about a search engine optimized article for a blog focused on artificial intelligence. Follow the steps below: {{ $('Format Research Output').item.json.research }}\n\n1. **Write an SEO-optimized article with a maximum of 20 lines** based on the provided information:\n   - Structure the article with a catchy **H1 title**, one or two **H2 subtitles**, and a professional yet accessible tone.\n   - Extract and include relevant keywords from the data.\n   - Optimize for readability: short sentences, clear paragraphs, and a CTA if relevant.\n   - Do not exceed 20 lines of content.\n\n2. **Publish the article on WordPress**, including:\n   - The **title** as the article's headline\n   - The **SEO content** as the body\n\n3. **Send an email** to my address : {{ $json.emailAddress }} containing:\n   - The article's title\n   - The **URL** of the published article on WordPress\n\n4. **Retrieve the list of available Slack tools first** using “Slack Tools”.\n   - Then, send a notification on Slack that the article has been published, including:\n     - The article title\n     - The article link\n     - Slack channel ID: {{ $json.slackChannelId }}\n\n5. **Retrieve the list of available Notion tools first** using “Notion Tools”.\n   Then, **add a new entry to my Notion database** (ID: {{ $json.notionDatabaseId }}) with the following fields:\n   - The 'Name' column is of type 'title'  → {{ $('Start with Research Query Submission').item.json['Topic or Question'] }}\n   The 'Subject' column is of type 'rich_text' → [the article's headline]\n   - The 'Content'column is of type 'rich_text' → [The SEO content]\n   - The 'URL' column is of type 'URL': → [The article link]\n   - The 'Status' column is of type 'select' → Select: `publish`\n\nImportant: Ensure that each step is successfully completed **before proceeding to the next**.\n", "options": {}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "aaab95dd-7fd2-411e-ba05-fa84568c0d56", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [480, -180], "parameters": {"width": 1300, "height": 820, "content": "## My Copywriting AI Agent\n✍️ Transforms live research into SEO-optimized blog articles using GPT-4, then automatically publishes to WordPress, sends notifications via Gmail & Slack, and logs everything to Notion. This is your full-stack content assistant — from prompt to post, hands-free.\n**mcp-notion-server** : [Guide](https://github.com/suekou/mcp-notion-server)\n**mcp-slack-server** : [Guide](https://github.com/modelcontextprotocol/servers/tree/main/src/slack)"}, "typeVersion": 1}, {"id": "d3cbf58c-7c14-4695-8331-1750daf21d0d", "name": "Format Research Output", "type": "n8n-nodes-base.set", "position": [280, 100], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "23b8e8c4-9191-415a-9661-1b60d413528a", "name": "research", "type": "string", "value": "={{ $json.choices[0].message.content.replaceAll(\"[1]\", \" - source: \" +$json.citations[0]).replaceAll(\"[2]\",\" - source:\" +$json.citations[1]).replaceAll(\"[3]\",\" - source: \" +$json.citations[2]).replaceAll(\"[4]\",\" - source: \"+$json.citations[3]).replaceAll(\"[5]\",\" - source: \"+$json.citations[4]).replaceAll(\"[6]\",\" - source: \"+$json.citations[5]).replaceAll(\"[7]\",\" - source: \"+$json.citations[6]).replaceAll(\"[8]\",\" - source: \"+$json.citations[7]).replaceAll(\"[9]\",\" - source: \"+$json.citations[8]).replaceAll(\"[10]\",\" - source: \"+$json.citations[9]) }}"}]}}, "typeVersion": 3.4}, {"id": "cd073bb3-3b6a-4910-9de4-bef66fc00a1f", "name": "Publish Article to WordPress", "type": "n8n-nodes-base.wordpressTool", "position": [840, 400], "parameters": {"title": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Title', ``, 'string') }}", "additionalFields": {"status": "publish", "content": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Content', ``, 'string') }}"}}, "credentials": {"wordpressApi": {"id": "KIuXvzjOEnOsHKQE", "name": "Wordpress account"}}, "typeVersion": 1}, {"id": "6940575a-d504-4276-8964-c41f26418f3c", "name": "Send Email Notification", "type": "n8n-nodes-base.gmailTool", "position": [1360, 400], "webhookId": "b68c6af8-46e6-4ed9-ae72-445e9cb7ab88", "parameters": {"sendTo": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('To', ``, 'string') }}", "message": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message', ``, 'string') }}", "options": {}, "subject": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Subject', ``, 'string') }}"}, "credentials": {"gmailOAuth2": {"id": "rKxQHWZ2F5XLJmwF", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "da3c994c-60d5-41ef-9cf9-52daa77dc980", "name": "Notify Slack Channel", "type": "n8n-nodes-mcp.mcpClientTool", "position": [1640, 400], "parameters": {"toolName": "={{ $fromAI(\"tool\", \"the tool selected\")  }}", "operation": "executeTool", "toolParameters": "={{ $fromAI('Tool_Parameters', ``, 'json') }}"}, "credentials": {"mcpClientApi": {"id": "mC6b1h1p0lFikSzU", "name": "slack"}}, "typeVersion": 1}, {"id": "9d36b649-f5e6-442c-bcab-53f0ca0dc2c2", "name": "Generate SEO Blog Content (GPT-4o)", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [600, 400], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "6h3DfVhNPw9I25nO", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "7a034005-68a3-40fa-bb94-cfdfab717cfc", "name": "Start with Research Query Submission", "type": "n8n-nodes-base.formTrigger", "position": [-180, 100], "webhookId": "a29cbcd3-9d11-4f7c-9aad-14681c356c53", "parameters": {"options": {}, "formTitle": "AutoBlog Creator", "formFields": {"values": [{"fieldType": "textarea", "fieldLabel": "Topic or Question", "placeholder": "=How is GPT-4 transforming content creation in 2025?", "requiredField": true}]}, "formDescription": "From research to article — no writing required"}, "typeVersion": 2.2}, {"id": "8cbad4aa-1802-4275-bbc4-c4d17673cd23", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [0, -180], "parameters": {"color": 3, "width": 460, "height": 140, "content": "## Intro Sticky \n🔁 **This workflow automates the full cycle of SEO blog content creation** — from live topic research using Perplexity to blog publishing on WordPress, Slack/Gmail notifications, and Notion logging."}, "typeVersion": 1}, {"id": "93e258e4-baa2-4af2-ba12-0f1727150e19", "name": "Edit Workflow Variables", "type": "n8n-nodes-base.set", "position": [120, 460], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c06b2d24-1fd7-40f0-aee5-b5d6553e289e", "name": "emailAddress", "type": "string", "value": ""}, {"id": "451aad67-5190-4eab-a982-56092734bb07", "name": "slackChannelId", "type": "string", "value": ""}, {"id": "8a294900-f367-47a2-b260-344b133dc2ff", "name": "notionDatabaseId", "type": "string", "value": ""}]}}, "typeVersion": 3.4, "alwaysOutputData": true}, {"id": "88cb2c49-be54-4df9-81ff-d709bde839e1", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [0, 340], "parameters": {"color": 6, "width": 460, "height": 300, "content": "## Workflow Configuration Panel\n🛠️ **Set your variables here** (email, Slack, Notion, OpenAI model)"}, "typeVersion": 1}, {"id": "5075e00d-f1e9-4db2-85c1-d4d851f57abf", "name": "Notion-List", "type": "n8n-nodes-mcp.mcpClientTool", "position": [1000, 400], "parameters": {}, "credentials": {"mcpClientApi": {"id": "QQbMEB7i2XAAWTSc", "name": "Notion"}}, "typeVersion": 1}, {"id": "edf2d95b-b04d-40f9-9b8d-b53dd5912bab", "name": "Insert Article in Notion", "type": "n8n-nodes-mcp.mcpClientTool", "position": [1180, 400], "parameters": {"toolName": "={{ $fromAI(\"tool\", \"the tool selected\")  }}", "operation": "executeTool", "toolParameters": "={{ $fromAI('tool_parameters', ``, 'json') }}"}, "credentials": {"mcpClientApi": {"id": "QQbMEB7i2XAAWTSc", "name": "Notion"}}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "553f26d7-2dcf-4900-871e-b3aa25a68ffa", "connections": {"Slack-List": {"ai_tool": [[{"node": "Copywriting AI Agent", "type": "ai_tool", "index": 0}]]}, "Notion-List": {"ai_tool": [[{"node": "Copywriting AI Agent", "type": "ai_tool", "index": 0}]]}, "Perplexity Research": {"main": [[{"node": "Format Research Output", "type": "main", "index": 0}]]}, "Notify Slack Channel": {"ai_tool": [[{"node": "Copywriting AI Agent", "type": "ai_tool", "index": 0}]]}, "Format Research Output": {"main": [[{"node": "Edit Workflow Variables", "type": "main", "index": 0}]]}, "Edit Workflow Variables": {"main": [[{"node": "Copywriting AI Agent", "type": "main", "index": 0}]]}, "Send Email Notification": {"ai_tool": [[{"node": "Copywriting AI Agent", "type": "ai_tool", "index": 0}]]}, "Insert Article in Notion": {"ai_tool": [[{"node": "Copywriting AI Agent", "type": "ai_tool", "index": 0}]]}, "When chat message received": {"main": [[{"node": "Copywriting AI Agent", "type": "main", "index": 0}]]}, "Publish Article to WordPress": {"ai_tool": [[{"node": "Copywriting AI Agent", "type": "ai_tool", "index": 0}]]}, "Generate SEO Blog Content (GPT-4o)": {"ai_languageModel": [[{"node": "Copywriting AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Start with Research Query Submission": {"main": [[{"node": "Perplexity Research", "type": "main", "index": 0}]]}}}