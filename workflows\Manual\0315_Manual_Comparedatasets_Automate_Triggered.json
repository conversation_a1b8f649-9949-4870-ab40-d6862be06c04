{"meta": {"instanceId": "cb484ba7b742928a2048bf8829668bed5b5ad9787579adea888f05980292a4a7"}, "nodes": [{"id": "31a9f34c-c5b0-462e-885d-f394b6d83f3a", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [840, 500], "parameters": {}, "typeVersion": 1}, {"id": "a16c48dd-070d-4d0b-b220-20a5e98288a6", "name": "Dataset 1", "type": "n8n-nodes-base.code", "position": [1060, 360], "parameters": {"jsCode": " return [\n{\n\"fruit\": \"apple\",\n\"color\": \"green\",\n},\n{\n\"fruit\": \"orange\",\n\"color\": \"orange\",\n},\n{\n\"fruit\": \"grape\",\n\"color\": \"green\",  \n},\n{\n\"fruit\": \"strawberry\",\n\"color\": \"red\",\n},\n{\n\"fruit\": \"banana\",\n\"color\": \"yellow\",\n}\n];\n"}, "typeVersion": 2}, {"id": "11b41146-8682-4c8d-84db-259acddced4b", "name": "Dataset 2", "type": "n8n-nodes-base.code", "position": [1060, 620], "parameters": {"jsCode": " return [\n{\n\"fruit\": \"apple\",\n\"color\": \"green\",\n},\n{\n\"fruit\": \"grape\",\n\"color\": \"purple\",\n},\n{\n\"fruit\": \"orange\",\n\"color\": \"orange\",\n},\n{\n \"fruit\": \"kiwi\",\n \"color\": \"mostly green\"\n},\n{\n\"fruit\": \"banana\",\n\"color\": \"yellow\",\n}\n];\n"}, "typeVersion": 2}, {"id": "dc976f9e-e645-4bcf-999a-b3a62be661e3", "name": "Compare Datasets", "type": "n8n-nodes-base.compareDatasets", "position": [1380, 500], "parameters": {"options": {}, "mergeByFields": {"values": [{"field1": "fruit", "field2": "fruit"}]}}, "typeVersion": 2.3}, {"id": "1945d250-b5dd-4aa3-aa85-8c41aeb1f04a", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [460, 440], "parameters": {"width": 321, "height": 250, "content": "## Comparing data with the Compare Datasets node\n\nThe [Compare Datasets](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.comparedatasets/) node compares data streams before merging them. It outputs up to four different branches.\n\nClick the **Execute Workflow** button, then double click on the nodes to see the input and output items."}, "typeVersion": 1}, {"id": "313571f3-b249-43d1-b152-1e45c31b0b8c", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1300, 340], "parameters": {"width": 302, "height": 385, "content": "## Explore outputs \n\nIn the OUTPUT panel of this node, click on the different tabs to see which data goes to which output stream."}, "typeVersion": 1}], "connections": {"Dataset 1": {"main": [[{"node": "Compare Datasets", "type": "main", "index": 0}]]}, "Dataset 2": {"main": [[{"node": "Compare Datasets", "type": "main", "index": 1}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "Dataset 1", "type": "main", "index": 0}, {"node": "Dataset 2", "type": "main", "index": 0}]]}}}