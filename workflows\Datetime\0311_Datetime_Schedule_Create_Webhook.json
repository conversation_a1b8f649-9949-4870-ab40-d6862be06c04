{"meta": {"instanceId": "a2434c94d549548a685cca39cc4614698e94f527bcea84eefa363f1037ae14cd"}, "nodes": [{"id": "0bacf032-53d6-4ba6-ab71-e01625c49cc4", "name": "On schedule", "type": "n8n-nodes-base.scheduleTrigger", "position": [-1960, 160], "parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 1}]}}, "typeVersion": 1.1}, {"id": "2e0d9aef-0a60-4506-9c11-c6c2cccb16ea", "name": "Derive last request time", "type": "n8n-nodes-base.dateTime", "position": [-1740, 160], "parameters": {"duration": 1, "timeUnit": "minutes", "magnitude": "={{ $json.timestamp }}", "operation": "subtractFromDate", "outputFieldName": "last_request_time"}, "typeVersion": 2}, {"id": "f726c448-b4c4-4159-8ca5-c94c092127b7", "name": "Get emails from label and last request time", "type": "n8n-nodes-base.gmail", "position": [-1520, 160], "parameters": {"filters": {"labelIds": ["Label_9178764513576607415"]}, "operation": "getAll", "returnAll": true}, "credentials": {"gmailOAuth2": {"id": "31", "name": "REPLACE ME"}}, "typeVersion": 2}, {"id": "9b86331f-d33b-4266-ba34-bc0491a0da24", "name": "Create database page", "type": "n8n-nodes-base.notion", "position": [-620, 60], "parameters": {"title": "={{ $('If database page not found').item.json.Subject }}", "blockUi": {"blockValues": [{"type": "heading_3", "textContent": "Snippet"}, {"textContent": "={{ $('If database page not found').item.json.snippet }}"}, {"text": {"text": [{"text": "See more", "isLink": true, "textLink": "=https://mail.google.com/mail/u/{{ $json.emailAddress }}/#all/{{ $('If database page not found').item.json.id }}", "annotationUi": {}}]}, "richText": true}]}, "options": {"icon": "https://avatars.githubusercontent.com/u/45487711?s=280&v=4", "iconType": "file"}, "resource": "databasePage", "databaseId": {"__rl": true, "mode": "list", "value": "e606a7c1-e93d-47fd-8b8d-8000cd6e7522", "cachedResultUrl": "https://www.notion.so/e606a7c1e93d47fd8b8d8000cd6e7522", "cachedResultName": "Gmail"}, "propertiesUi": {"propertyValues": [{"key": "Thread ID|rich_text", "textContent": "={{ $('If database page not found').item.json.id }}"}, {"key": "Email thread|url", "urlValue": "=https://mail.google.com/mail/u/{{ $json.emailAddress }}/#all/{{ $('If database page not found').item.json.id }}"}]}}, "credentials": {"notionApi": {"id": "18", "name": "[UPDATE ME]"}}, "typeVersion": 2}, {"id": "d7198578-4c83-4f57-8eba-5b5a9b89195c", "name": "Try get database page", "type": "n8n-nodes-base.notion", "position": [-1360, 220], "parameters": {"filters": {"conditions": [{"key": "Thread ID|rich_text", "condition": "equals", "richTextValue": "={{ $json.id }}"}]}, "options": {}, "resource": "databasePage", "operation": "getAll", "returnAll": true, "databaseId": {"__rl": true, "mode": "list", "value": "e606a7c1-e93d-47fd-8b8d-8000cd6e7522", "cachedResultUrl": "https://www.notion.so/e606a7c1e93d47fd8b8d8000cd6e7522", "cachedResultName": "My Gmail Tasks"}, "filterType": "manual"}, "credentials": {"notionApi": {"id": "18", "name": "[UPDATE ME]"}}, "typeVersion": 2, "alwaysOutputData": true}, {"id": "f8188ab9-9a80-4aa9-b773-73cd90b8dbd3", "name": "If checked off", "type": "n8n-nodes-base.if", "position": [-1740, 460], "parameters": {"conditions": {"boolean": [{"value1": "={{ $json.Complete }}", "value2": true}]}}, "typeVersion": 1}, {"id": "bfcfeeb1-ad8b-47fb-8a09-b58e7b649a25", "name": "On updated database page", "type": "n8n-nodes-base.notionTrigger", "position": [-1960, 460], "parameters": {"event": "pagedUpdatedInDatabase", "pollTimes": {"item": [{"mode": "everyMinute"}]}, "databaseId": {"__rl": true, "mode": "list", "value": "e606a7c1-e93d-47fd-8b8d-8000cd6e7522", "cachedResultUrl": "https://www.notion.so/e606a7c1e93d47fd8b8d8000cd6e7522", "cachedResultName": "My Gmail Tasks"}}, "credentials": {"notionApi": {"id": "18", "name": "[UPDATE ME]"}}, "typeVersion": 1}, {"id": "dc2c59b8-6e0d-46b3-946a-e48b0461c48f", "name": "Remove label from target email", "type": "n8n-nodes-base.gmail", "position": [-1520, 460], "parameters": {"labelIds": ["Label_9178764513576607415"], "messageId": "={{ $json['Thread ID'] }}", "operation": "<PERSON><PERSON><PERSON><PERSON>"}, "credentials": {"gmailOAuth2": {"id": "31", "name": "REPLACE ME"}}, "typeVersion": 2}, {"id": "0f693c2f-ce89-4a2f-a85f-9230b7bcb94d", "name": "Not yet checked off, do nothing", "type": "n8n-nodes-base.noOp", "position": [-1520, 660], "parameters": {}, "typeVersion": 1}, {"id": "bf792470-fc0a-45a2-b655-df5c977faa97", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [-1220, 100], "parameters": {"mode": "combine", "options": {}, "joinMode": "enrichInput1", "mergeByFields": {"values": [{"field1": "id", "field2": "property_thread_id"}]}}, "typeVersion": 2.1}, {"id": "f910c34c-4c3d-481f-8223-a8aae710dbbd", "name": "If found, do nothing", "type": "n8n-nodes-base.noOp", "position": [-840, 260], "parameters": {}, "typeVersion": 1}, {"id": "7086cd15-9f2e-40e4-be3b-47d117dde670", "name": "If database page not found", "type": "n8n-nodes-base.if", "position": [-1060, 160], "parameters": {"conditions": {"string": [{"value1": "={{ $json.property_thread_id }}", "operation": "isEmpty"}]}}, "typeVersion": 1}, {"id": "86ce380c-0810-4edb-94e4-fb67b0ca422c", "name": "Find my email address", "type": "n8n-nodes-base.httpRequest", "position": [-840, 60], "parameters": {"url": "https://gmail.googleapis.com/gmail/v1/users/me/profile", "options": {}, "authentication": "predefinedCredentialType", "nodeCredentialType": "gmailOAuth2"}, "credentials": {"gmailOAuth2": {"id": "31", "name": "REPLACE ME"}}, "typeVersion": 4.1}, {"id": "f576f785-49e4-4ed2-b83e-400b001b6c3a", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-2540, 100], "parameters": {"width": 501.0810810810809, "height": 545.405405405404, "content": "## Send labeled email to a Notion database\nThis workflow sends the contents of an email to a Notion database. The email must be labeled with a specific label for the workflow to trigger. The email subject will be the title of the Notion page, and a snippet of the email body will be the content of the Notion page. The email link will be added to the Notion page as a property.\n\n### How it works\nOn scheduled intervals, find all emails with a specific label. For each email, check if the email already exists in the Notion database. If it does not exist, create a new page in the Notion database, otherwise do nothing. When the task in the Notion database is checked off, the label will be removed from the email.\n\n### Setup\nThis workflow requires that you set up a Notion database or use an existing one with at least the following fields:\n- Title (title)\n- Thread ID (text)\n- Email thread (URL)\n\n\nAdditionally, create a label that will be used to trigger the workflow in Gmail. In this workflow, the label is called \"Notion\"."}, "typeVersion": 1}], "connections": {"Merge": {"main": [[{"node": "If database page not found", "type": "main", "index": 0}]]}, "On schedule": {"main": [[{"node": "Derive last request time", "type": "main", "index": 0}]]}, "If checked off": {"main": [[{"node": "Remove label from target email", "type": "main", "index": 0}], [{"node": "Not yet checked off, do nothing", "type": "main", "index": 0}]]}, "Find my email address": {"main": [[{"node": "Create database page", "type": "main", "index": 0}]]}, "Try get database page": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Derive last request time": {"main": [[{"node": "Get emails from label and last request time", "type": "main", "index": 0}]]}, "On updated database page": {"main": [[{"node": "If checked off", "type": "main", "index": 0}]]}, "If database page not found": {"main": [[{"node": "Find my email address", "type": "main", "index": 0}], [{"node": "If found, do nothing", "type": "main", "index": 0}]]}, "Get emails from label and last request time": {"main": [[{"node": "Try get database page", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}}}