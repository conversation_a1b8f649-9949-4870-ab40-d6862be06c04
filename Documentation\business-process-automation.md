# Business Process Automation - N8N Workflows

## Overview
This document catalogs the **Business Process Automation** workflows from the n8n Community Workflows repository.

**Category:** Business Process Automation  
**Total Workflows:** 77  
**Generated:** 2025-07-27  
**Source:** https://scan-might-updates-postage.trycloudflare.com/api

---

## Workflows

### screenshot
**Filename:** `0031_Functionitem_Dropbox_Automation_Webhook.json`  
**Description:** Manual workflow that orchestrates Dropbox, Awsses, and Functionitem for data processing. Uses 10 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (10 nodes)  
**Integrations:** Dropbox,Awsses,Functionitem,Httprequest,Uproc,  

---

### Functionitem Manual Import Scheduled
**Filename:** `0068_Functionitem_Manual_Import_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Httprequest, Google Drive, and Movebinarydata for data processing. Uses 9 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (9 nodes)  
**Integrations:** Httprequest,Google Drive,Movebinarydata,Functionitem,  

---

### Create a client in Harvest
**Filename:** `0088_Manual_Harvest_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Harvest to create new records. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Harvest,  

---

### Get all the tasks in Flow
**Filename:** `0122_Manual_Flow_Import_Triggered.json`  
**Description:** Manual workflow that integrates with Flow for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Flow,  

---

### Receive updates for specified tasks in Flow
**Filename:** `0133_Flow_Update_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Flow to update existing data. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Flow,  

---

### Functionitem Telegram Create Webhook
**Filename:** `0146_Functionitem_Telegram_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Telegram, and Webhook to create new records. Uses 8 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (8 nodes)  
**Integrations:** Httprequest,Telegram,Webhook,Functionitem,  

---

### Datetime Functionitem Create Webhook
**Filename:** `0159_Datetime_Functionitem_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Webhook, Htmlextract, and Functionitem to create new records. Uses 12 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** Webhook,Htmlextract,Functionitem,Httprequest,Itemlists,Form Trigger,  

---

### Datetime Googlecalendar Send Scheduled
**Filename:** `0168_Datetime_GoogleCalendar_Send_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Emailsend, Datetime, and Google Calendar for data processing. Uses 13 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (13 nodes)  
**Integrations:** Emailsend,Datetime,Google Calendar,  

---

### extract_swifts
**Filename:** `0178_Functionitem_Executecommand_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates MongoDB, Splitinbatches, and Readbinaryfile for data processing. Uses 23 nodes and integrates with 9 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (23 nodes)  
**Integrations:** MongoDB,Splitinbatches,Readbinaryfile,Executecommand,Writebinaryfile,Htmlextract,Functionitem,Httprequest,Uproc,  

---

### Functionitem Itemlists Automate
**Filename:** `0184_Functionitem_Itemlists_Automate.json`  
**Description:** Manual workflow that connects Functionitem and Itemlists for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Functionitem,Itemlists,  

---

### Executecommand Functionitem Automate
**Filename:** `0190_Executecommand_Functionitem_Automate.json`  
**Description:** Manual workflow that connects Executecommand and Functionitem for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Executecommand,Functionitem,  

---

### Functionitem Pipedrive Create Scheduled
**Filename:** `0246_Functionitem_Pipedrive_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Form Trigger, Stripe, and Functionitem to create new records. Uses 11 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (11 nodes)  
**Integrations:** Form Trigger,Stripe,Functionitem,Pipedrive,Itemlists,  

---

### Functionitem HTTP Create Webhook
**Filename:** `0247_Functionitem_HTTP_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Itemlists, Stripe, and Pipedrive to create new records. Uses 7 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Itemlists,Stripe,Pipedrive,Functionitem,  

---

### Functionitem Manual Create Triggered
**Filename:** `0255_Functionitem_Manual_Create_Triggered.json`  
**Description:** Manual workflow that orchestrates Emailsend, N8Ntrainingcustomerdatastore, and Functionitem to create new records. Uses 8 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (8 nodes)  
**Integrations:** Emailsend,N8Ntrainingcustomerdatastore,Functionitem,Itemlists,  

---

### Functionitem Zendesk Create Webhook
**Filename:** `0266_Functionitem_Zendesk_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Zendesk, Pipedrive, and Functionitem to create new records. Uses 17 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** Zendesk,Pipedrive,Functionitem,Form Trigger,  

---

### Functionitem Zendesk Create Scheduled
**Filename:** `0267_Functionitem_Zendesk_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Zendesk, and Functionitem to create new records. Uses 21 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (21 nodes)  
**Integrations:** Splitinbatches,Zendesk,Functionitem,Httprequest,Pipedrive,Itemlists,  

---

### Add a event to Calender
**Filename:** `0342_Manual_GoogleCalendar_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Cal.com for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Cal.com,  

---

### Google Cal to Zoom meeting
**Filename:** `0348_Datetime_GoogleCalendar_Automation_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Cal.com, Zoom, and Datetime for data processing. Uses 6 nodes.  
**Status:** Active  
**Trigger:** Scheduled  
**Complexity:** medium (6 nodes)  
**Integrations:** Cal.com,Zoom,Datetime,  

---

### Executeworkflow Summarize Send Triggered
**Filename:** `0371_Executeworkflow_Summarize_Send_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Chat, and Executeworkflow for data processing. Uses 15 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (15 nodes)  
**Integrations:** OpenAI,Chat,Executeworkflow,Cal.com,Toolcode,Memorybufferwindow,  

---

### Executeworkflow Hackernews Create Triggered
**Filename:** `0372_Executeworkflow_Hackernews_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Hackernews, OpenAI, and Agent to create new records. Uses 12 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** Hackernews,OpenAI,Agent,Chat,Executeworkflow,Cal.com,  

---

### Add a datapoint to Beeminder when new activity is added to Strava
**Filename:** `0403_Beeminder_Strava_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Beeminder and Strava for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** Beeminder,Strava,  

---

### Executeworkflow Slack Send Triggered
**Filename:** `0406_Executeworkflow_Slack_Send_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Agent, and Toolworkflow for data processing. Uses 17 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** OpenAI,Agent,Toolworkflow,Chat,Executeworkflow,Memorybufferwindow,Slack,  

---

### Code Googlecalendar Create Webhook
**Filename:** `0415_Code_GoogleCalendar_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Gmail, and Google Calendar to create new records. Uses 12 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** Httprequest,Gmail,Google Calendar,Form Trigger,  

---

### Splitout Googlecalendar Send Webhook
**Filename:** `0428_Splitout_GoogleCalendar_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitout, Gmail, and LinkedIn for data processing. Uses 19 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (19 nodes)  
**Integrations:** Splitout,Gmail,LinkedIn,Html,Httprequest,Clearbit,Form Trigger,Google Calendar,  

---

### Splitout Googlecalendar Send Webhook
**Filename:** `0429_Splitout_GoogleCalendar_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Splitout, and Gmail for data processing. Uses 21 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (21 nodes)  
**Integrations:** OpenAI,Splitout,Gmail,LinkedIn,Html,Httprequest,Clearbit,Google Calendar,  

---

### Splitout Googlecalendar Create Scheduled
**Filename:** `0528_Splitout_GoogleCalendar_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Splitout, and Outputparserstructured to create new records. Uses 33 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (33 nodes)  
**Integrations:** OpenAI,Splitout,Outputparserstructured,Toolwikipedia,Cal.com,Toolserpapi,Slack,Google Calendar,  

---

### Splitout Googlecalendar Create Webhook
**Filename:** `0530_Splitout_GoogleCalendar_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Google Drive, and Splitout to create new records. Uses 28 nodes and integrates with 11 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (28 nodes)  
**Integrations:** OpenAI,Google Drive,Splitout,Agent,Extractfromfile,Toolworkflow,Outputparserstructured,Httprequest,Executeworkflow,Cal.com,Google Calendar,  

---

### Executeworkflow Telegram Update Triggered
**Filename:** `0569_Executeworkflow_Telegram_Update_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Executeworkflow, Telegram, and Google Sheets to update existing data. Uses 29 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (29 nodes)  
**Integrations:** Executeworkflow,Telegram,Google Sheets,  

---

### Googlecalendar Form Create Triggered
**Filename:** `0647_GoogleCalendar_Form_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Textclassifier, and Gmail to create new records. Uses 25 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (25 nodes)  
**Integrations:** OpenAI,Textclassifier,Gmail,Chainllm,Form Trigger,Executeworkflow,Google Calendar,  

---

### Splitout Googlecalendar Create Webhook
**Filename:** `0649_Splitout_GoogleCalendar_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Splitout, and Gmail to create new records. Uses 61 nodes and integrates with 11 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (61 nodes)  
**Integrations:** OpenAI,Splitout,Gmail,LinkedIn,Html,Httprequest,Chainllm,WhatsApp,Form Trigger,Executeworkflow,Google Calendar,  

---

### Code Strava Send Triggered
**Filename:** `0701_Code_Strava_Send_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Agent, and Gmail for data processing. Uses 15 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (15 nodes)  
**Integrations:** Lmchatgooglegemini,Agent,Gmail,Emailsend,WhatsApp,Strava,  

---

### Webhook Googlecalendar Create Webhook
**Filename:** `0702_Webhook_GoogleCalendar_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Airtable, Webhook, and Respondtowebhook to create new records. Uses 33 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (33 nodes)  
**Integrations:** Airtable,Webhook,Respondtowebhook,Google Sheets,Httprequest,Form Trigger,Cal.com,  

---

### Syncro to Clockify
**Filename:** `0750_Clockify_Webhook_Sync_Webhook.json`  
**Description:** Webhook-triggered automation that connects Webhook and Clockify to synchronize data. Uses 2 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** Webhook,Clockify,  

---

### Googlecalendar Schedule Create Scheduled
**Filename:** `0783_GoogleCalendar_Schedule_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Lmchatopenai, and Agent to create new records. Uses 22 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (22 nodes)  
**Integrations:** Splitinbatches,Lmchatopenai,Agent,Gmail,Outputparserstructured,Removeduplicates,Googlecalendartool,Google Calendar,  

---

### Code Googlecalendar Create Webhook
**Filename:** `0787_Code_GoogleCalendar_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Gmail, and Outputparserstructured to create new records. Uses 12 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** OpenAI,Gmail,Outputparserstructured,Httprequest,Chainllm,Google Calendar,  

---

### Email body parser by aprenden8n.com
**Filename:** `0827_Manual_Functionitem_Send_Triggered.json`  
**Description:** Manual workflow that integrates with Functionitem for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Functionitem,  

---

### Executeworkflow Executecommandtool Create Triggered
**Filename:** `0872_Executeworkflow_Executecommandtool_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Executecommand, Toolworkflow, and Mcp to create new records. Uses 14 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** Executecommand,Toolworkflow,Mcp,Executeworkflow,Executecommandtool,  

---

### Stickynote Executeworkflow Create Triggered
**Filename:** `0874_Stickynote_Executeworkflow_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Executeworkflow, Toolcode, and Mcp to create new records. Uses 16 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (16 nodes)  
**Integrations:** Executeworkflow,Toolcode,Mcp,Toolworkflow,  

---

### Splitout Googlecalendar Update Webhook
**Filename:** `0899_Splitout_GoogleCalendar_Update_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, Splitinbatches, and Splitout to update existing data. Uses 18 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (18 nodes)  
**Integrations:** Markdown,Splitinbatches,Splitout,Gmail,Httprequest,Cal.com,  

---

### Workflow Results to Markdown Notes in Your Obsidian Vault, via Google Drive
**Filename:** `0947_Executeworkflow_Stickynote_Automate_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Google Drive, and Agent for data processing. Uses 15 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (15 nodes)  
**Integrations:** OpenAI,Google Drive,Agent,Outputparserstructured,Executeworkflow,  

---

### Clockify Automate Triggered
**Filename:** `1005_Clockify_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Clockify for data processing. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Clockify,  

---

### Manual Executeworkflow Automate Triggered
**Filename:** `1051_Manual_Executeworkflow_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Executeworkflow for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Executeworkflow,  

---

### Example - Backup n8n to Nextcloud
**Filename:** `1067_Functionitem_Manual_Export_Webhook.json`  
**Description:** Scheduled automation that orchestrates Httprequest, Nextcloud, and Movebinarydata for data backup operations. Uses 9 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (9 nodes)  
**Integrations:** Httprequest,Nextcloud,Movebinarydata,Functionitem,  

---

### Build an MCP Server with Google Calendar
**Filename:** `1071_Googlecalendartool_Stickynote_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatopenai, Agent, and Chat for data processing. Uses 23 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (23 nodes)  
**Integrations:** Lmchatopenai,Agent,Chat,Googlecalendartool,Cal.com,Memorybufferwindow,  

---

### Manual Unleashedsoftware Automation Triggered
**Filename:** `1087_Manual_Unleashedsoftware_Automation_Triggered.json`  
**Description:** Manual workflow that integrates with Unleashedsoftware for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Unleashedsoftware,  

---

### Googlecalendar Googlesheets Create Triggered
**Filename:** `1116_GoogleCalendar_GoogleSheets_Create_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Typeform, Mattermost, and Google Sheets to create new records. Uses 10 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** Typeform,Mattermost,Google Sheets,Gmail,Google Calendar,  

---

### Create a project, tag, and time entry, and update the time entry in Clockify
**Filename:** `1126_Manual_Clockify_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Clockify to create new records. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Clockify,  

---

### YouTube to Raindrop
**Filename:** `1140_Functionitem_Raindrop_Automation_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Youtube, Raindrop, and Functionitem for data processing. Uses 6 nodes.  
**Status:** Active  
**Trigger:** Scheduled  
**Complexity:** medium (6 nodes)  
**Integrations:** Youtube,Raindrop,Functionitem,  

---

### Functionitem Executecommand Update Webhook
**Filename:** `1157_Functionitem_Executecommand_Update_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Readbinaryfile, Executecommand, and Writebinaryfile to update existing data. Uses 25 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (25 nodes)  
**Integrations:** Readbinaryfile,Executecommand,Writebinaryfile,Htmlextract,Movebinarydata,Functionitem,Httprequest,Emailsend,  

---

### Create, update, and get activity in Strava
**Filename:** `1206_Manual_Strava_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Strava to create new records. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Strava,  

---

### Raindrop Automate
**Filename:** `1209_Raindrop_Automate.json`  
**Description:** Manual workflow that integrates with Raindrop for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Raindrop,  

---

### AI Agent : Google calendar assistant using OpenAI
**Filename:** `1247_Googlecalendartool_Stickynote_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Cal.com, Memorybufferwindow, and OpenAI for data processing. Uses 13 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (13 nodes)  
**Integrations:** Cal.com,Memorybufferwindow,OpenAI,Chat,  

---

### Code Strava Automation Triggered
**Filename:** `1259_Code_Strava_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Agent, and Gmail for data processing. Uses 15 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (15 nodes)  
**Integrations:** Lmchatgooglegemini,Agent,Gmail,Emailsend,WhatsApp,Strava,  

---

### Splitout Googlecalendar Automation Webhook
**Filename:** `1297_Splitout_GoogleCalendar_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Google Drive, and Splitout for data processing. Uses 28 nodes and integrates with 11 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (28 nodes)  
**Integrations:** OpenAI,Google Drive,Splitout,Agent,Extractfromfile,Toolworkflow,Outputparserstructured,Httprequest,Executeworkflow,Cal.com,Google Calendar,  

---

### Splitout Googlecalendar Automate Webhook
**Filename:** `1333_Splitout_GoogleCalendar_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Splitout, and Gmail for data processing. Uses 61 nodes and integrates with 11 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (61 nodes)  
**Integrations:** OpenAI,Splitout,Gmail,LinkedIn,Html,Httprequest,Chainllm,WhatsApp,Form Trigger,Executeworkflow,Google Calendar,  

---

### Automate Event Creation in Google Calendar from Google Sheets
**Filename:** `1346_GoogleCalendar_GoogleSheets_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Cal.com, Google Sheets, and Form Trigger for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Cal.com,Google Sheets,Form Trigger,  

---

### Build a Chatbot, Voice Agent and Phone Agent with Voiceflow, Google Calendar and RAG
**Filename:** `1361_GoogleCalendar_Webhook_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Textsplittertokensplitter, OpenAI, and Google Drive for data processing. Uses 34 nodes and integrates with 12 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (34 nodes)  
**Integrations:** Textsplittertokensplitter,OpenAI,Google Drive,Webhook,Agent,Outputparserstructured,Httprequest,Chainllm,Vectorstoreqdrant,Documentdefaultdataloader,Cal.com,Toolvectorstore,  

---

### CoinMarketCap_DEXScan_Agent_Tool
**Filename:** `1507_Stickynote_Executeworkflow_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Toolhttprequest, Lmchatopenai, and Agent for data processing. Uses 15 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (15 nodes)  
**Integrations:** Toolhttprequest,Lmchatopenai,Agent,Executeworkflow,Cal.com,Memorybufferwindow,  

---

### Personal Assistant MCP server
**Filename:** `1534_Stickynote_Googlecalendartool_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Googlesheetstool, and Agent for data processing. Uses 20 nodes and integrates with 9 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (20 nodes)  
**Integrations:** Lmchatgooglegemini,Googlesheetstool,Agent,Mcp,Gmailtool,Chat,Googlecalendartool,Mcpclienttool,Memorybufferwindow,  

---

### Generate google meet links in slack
**Filename:** `1573_GoogleCalendar_Slack_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Cal.com, Webhook, and Google Calendar for data processing. Uses 9 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (9 nodes)  
**Integrations:** Cal.com,Webhook,Google Calendar,Slack,  

---

### Googlecalendar Form Automation Triggered
**Filename:** `1620_GoogleCalendar_Form_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Textclassifier, and Gmail for data processing. Uses 25 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (25 nodes)  
**Integrations:** OpenAI,Textclassifier,Gmail,Chainllm,Form Trigger,Executeworkflow,Google Calendar,  

---

### CoinMarketCap_Crypto_Agent_Tool
**Filename:** `1624_Stickynote_Executeworkflow_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Toolhttprequest, Lmchatopenai, and Agent for data processing. Uses 13 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (13 nodes)  
**Integrations:** Toolhttprequest,Lmchatopenai,Agent,Executeworkflow,Memorybufferwindow,  

---

### Calendar_scheduling
**Filename:** `1668_GoogleCalendar_Filter_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Agent, and Gmail for data processing. Uses 21 nodes and integrates with 10 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (21 nodes)  
**Integrations:** OpenAI,Agent,Gmail,Toolworkflow,Outputparserstructured,Chainllm,Itemlists,Form Trigger,Executeworkflow,Cal.com,  

---

### OpenSea NFT Agent Tool
**Filename:** `1779_Stickynote_Executeworkflow_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Toolhttprequest, Lmchatopenai, and Agent for data processing. Uses 17 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** Toolhttprequest,Lmchatopenai,Agent,Executeworkflow,Memorybufferwindow,  

---

### 🤖Calendar Agent
**Filename:** `1792_Googlecalendartool_Executeworkflow_Automation_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Executeworkflow, Cal.com, and OpenAI for data processing. Uses 10 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** Executeworkflow,Cal.com,OpenAI,Googlecalendartool,  

---

### 🤖Contact Agent
**Filename:** `1793_Executeworkflow_Airtabletool_Automation_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Executeworkflow, Agent, and OpenAI for data processing. Uses 7 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Executeworkflow,Agent,OpenAI,Airtabletool,  

---

### 🤖Content Creator Agent
**Filename:** `1794_Executeworkflow_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Executeworkflow, Toolhttprequest, and Anthropic for data processing. Uses 6 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (6 nodes)  
**Integrations:** Executeworkflow,Toolhttprequest,Anthropic,Agent,  

---

### 🤖Email Agent
**Filename:** `1795_Gmailtool_Executeworkflow_Send_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Executeworkflow, Gmailtool, and Agent for data processing. Uses 12 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** Executeworkflow,Gmailtool,Agent,OpenAI,  

---

### Inverview Scheduler
**Filename:** `1813_Code_GoogleCalendar_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Agent, and Toolworkflow for data processing. Uses 25 nodes and integrates with 9 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (25 nodes)  
**Integrations:** OpenAI,Agent,Toolworkflow,Outputparserstructured,Chat,Executeworkflow,Cal.com,Memorybufferwindow,Google Calendar,  

---

### OpenSea Marketplace Agent Tool
**Filename:** `1816_Stickynote_Executeworkflow_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Toolhttprequest, Lmchatopenai, and Agent for data processing. Uses 17 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** Toolhttprequest,Lmchatopenai,Agent,Executeworkflow,Memorybufferwindow,  

---

### Stickynote Executeworkflow Automate Triggered
**Filename:** `1846_Stickynote_Executeworkflow_Automate_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatopenrouter, Outputparserstructured, and Chainllm for data processing. Uses 12 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** Lmchatopenrouter,Outputparserstructured,Chainllm,Form Trigger,Executeworkflow,  

---

### MCP_CALENDAR
**Filename:** `1872_Googlecalendartool_Automation_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Cal.com for data processing. Uses 7 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Cal.com,  

---

### CoinMarketCap_Exchange_and_Community_Agent_Tool
**Filename:** `1902_Stickynote_Executeworkflow_Update_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Toolhttprequest, Lmchatopenai, and Agent for data processing. Uses 12 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** Toolhttprequest,Lmchatopenai,Agent,Server-Sent Events,Executeworkflow,Memorybufferwindow,  

---

### Format US Phone Number
**Filename:** `1918_Executeworkflow_Automation_Triggered.json`  
**Description:** Webhook-triggered automation that connects Executeworkflow and Form Trigger for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Executeworkflow,Form Trigger,  

---

### Add new clients from Notion to Clockify
**Filename:** `1923_Clockify_Stickynote_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Notion and Clockify for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Notion,Clockify,  

---

### Reservation Medcin
**Filename:** `1928_Googlecalendartool_Stickynote_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Googlesheetstool, OpenAI, and Agent for data processing. Uses 12 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** Googlesheetstool,OpenAI,Agent,Chat,Googlecalendartool,Memorybufferwindow,  

---

### OpenSea Analytics Agent Tool
**Filename:** `2027_Stickynote_Executeworkflow_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Toolhttprequest, Lmchatopenai, and Agent for data processing. Uses 12 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** Toolhttprequest,Lmchatopenai,Agent,Executeworkflow,Memorybufferwindow,  

---


## Summary

**Total Business Process Automation workflows:** 77  
**Documentation generated:** 2025-07-27 14:31:42  
**API Source:** https://scan-might-updates-postage.trycloudflare.com/api  

This documentation was automatically generated using the n8n workflow API endpoints.
