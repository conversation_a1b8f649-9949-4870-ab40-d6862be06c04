{"id": "nJwkSOrJIFvutw1n", "meta": {"instanceId": "08daa2aa5b6032ff63690600b74f68f5b0f34a3b100102e019b35c4419168977"}, "name": "Flux Dev Image Generation Fal.ai", "tags": [], "nodes": [{"id": "00f3a7d9-9931-40a4-8eb5-5b9086d6995c", "name": "Fal Flux", "type": "n8n-nodes-base.httpRequest", "position": [420, 0], "parameters": {"url": "https://queue.fal.run/fal-ai/flux/dev", "method": "POST", "options": {}, "jsonBody": "={\n \"prompt\": \"{{ $json.Prompt }}\",\n \"image_size\": {\n \"width\": {{ $json.Width }},\n \"height\": {{ $json.Height }}\n},\n \"num_inference_steps\": {{ $json.Steps }},\n \"guidance_scale\": {{ $json.Guidance }},\n \"num_images\": 1,\n \"enable_safety_checker\": true\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "lNxvZHlUafPAHBYN", "name": "Fal Flux Header Auth account"}}, "typeVersion": 4.2}, {"id": "3032a543-2e21-415e-a5bd-d56ea33e4411", "name": "Get Image Result URL", "type": "n8n-nodes-base.httpRequest", "position": [1220, -20], "parameters": {"url": "=https://queue.fal.run/fal-ai/flux/requests/{{ $json.request_id }}", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "lNxvZHlUafPAHBYN", "name": "Fal Flux Header Auth account"}}, "typeVersion": 4.2}, {"id": "56e13e53-1697-4970-9bea-b75e0e849425", "name": "Download Image", "type": "n8n-nodes-base.httpRequest", "position": [1400, -20], "parameters": {"url": "={{ $json.images[0].url }}", "options": {}}, "typeVersion": 4.2}, {"id": "dd2efd2c-8712-4a77-8786-cccebdec876b", "name": "Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [1580, -20], "parameters": {"name": "={{ $binary.data.fileName }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "1R3PSyHXWHlY9DRFdOUEAPEop2fZy-_-K", "cachedResultUrl": "https://drive.google.com/drive/folders/1R3PSyHXWHlY9DRFdOUEAPEop2fZy-_-K", "cachedResultName": "Flux Image"}}, "credentials": {"googleDriveOAuth2Api": {"id": "CFiX9XTXGg4hGaGV", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "a598d868-0461-41fc-b6aa-f9998e9d6146", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-60, 0], "parameters": {}, "typeVersion": 1}, {"id": "a576d7b6-b2f3-4d53-8e7f-bb6449ff9c64", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [80, -120], "parameters": {"width": 260, "height": 120, "content": "## Set Parameter Here \nset Image Prompt and related settings"}, "typeVersion": 1}, {"id": "d39e85a8-3ddd-4f10-ba4c-beb86a850e10", "name": "Wait 3 Sec", "type": "n8n-nodes-base.wait", "position": [640, 0], "webhookId": "61a8626c-e281-4d4b-abb0-b9d87d1b4e7c", "parameters": {"amount": 3}, "typeVersion": 1.1}, {"id": "b27ac2f1-3f14-467e-81c4-af8b8fb37138", "name": "Check Status", "type": "n8n-nodes-base.httpRequest", "position": [840, 0], "parameters": {"url": "=https://queue.fal.run/fal-ai/flux/requests/{{ $json.request_id }}/status", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "lNxvZHlUafPAHBYN", "name": "Fal Flux Header Auth account"}}, "typeVersion": 4.2}, {"id": "7ee45dab-8e31-44de-bbb1-e99a565ee19c", "name": "Completed?", "type": "n8n-nodes-base.if", "position": [1020, 0], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "299a7c34-dcff-4991-a73f-5b1a84f188ea", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "COMPLETED"}]}}, "typeVersion": 2.2}, {"id": "c5036a7d-1879-449f-8ce9-9c1cf2c7426b", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1300, -100], "parameters": {"width": 220, "height": 100, "content": "## Set Drive Folder Here "}, "typeVersion": 1}, {"id": "c8887168-6234-486c-b7cb-cc0752c6341c", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [360, -180], "parameters": {"width": 260, "height": 180, "content": "### Generic Credential Type\n### Header : Authorization\nKey $FAL_KEY\"\n\nfor example:\nKey 6f2960baxxxxxxxxx"}, "typeVersion": 1}, {"id": "587043c4-e808-4c3f-910f-60f5eb8aff15", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [180, 0], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "f0a033cf-fa2b-4930-93b9-ff9c45fa7c14", "name": "Prompt", "type": "string", "value": "Thai young woman net idol 25 yrs old, walking on the street"}, {"id": "2b56185d-5c61-4c17-85f1-53ac4aab2b18", "name": "<PERSON><PERSON><PERSON>", "type": "number", "value": 1024}, {"id": "51eb65c0-ae0a-4ce7-ab00-9d13f05ce1e6", "name": "Height", "type": "number", "value": 768}, {"id": "8e89fca7-d380-4876-b973-69caa0394bc5", "name": "Steps", "type": "number", "value": 30}, {"id": "875e06b7-352a-4dde-8595-3274e9969c9c", "name": "Guidance", "type": "number", "value": 3.5}]}}, "typeVersion": 3.4}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "82877b10-5bbc-4c59-828b-4abc3ad53a5f", "connections": {"Fal Flux": {"main": [[{"node": "Wait 3 Sec", "type": "main", "index": 0}]]}, "Completed?": {"main": [[{"node": "Get Image Result URL", "type": "main", "index": 0}], [{"node": "Wait 3 Sec", "type": "main", "index": 0}]]}, "Wait 3 Sec": {"main": [[{"node": "Check Status", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Fal Flux", "type": "main", "index": 0}]]}, "Check Status": {"main": [[{"node": "Completed?", "type": "main", "index": 0}]]}, "Download Image": {"main": [[{"node": "Google Drive", "type": "main", "index": 0}]]}, "Get Image Result URL": {"main": [[{"node": "Download Image", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}}}