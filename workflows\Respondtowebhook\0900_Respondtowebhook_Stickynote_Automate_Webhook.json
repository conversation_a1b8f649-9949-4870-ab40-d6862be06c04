{"meta": {"instanceId": "f4f5d195bb2162a0972f737368404b18be694648d365d6c6771d7b4909d28167"}, "nodes": [{"id": "693e094e-0329-455d-9c42-ba398dd40955", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [-500, -160], "webhookId": "825f729b-34e1-4c34-8687-f48b7118b749", "parameters": {"path": "hotel-retell-template", "options": {"responseData": "={\"response\":\"Your booking is confirmed\"}"}, "httpMethod": "POST"}, "typeVersion": 2}, {"id": "6172b8b6-4950-4b7f-a60c-ce782139f24c", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [300, -160], "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "6ebeaeb7-4ec5-4f5d-b8d5-3fd51ca8604c", "name": "[Replace me!] Set response", "type": "n8n-nodes-base.set", "position": [-100, -160], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "4159ec1f-4bd6-4473-90e5-a0c24e2bf302", "name": "response", "type": "string", "value": "Your booking has been confirmed!"}]}}, "typeVersion": 3.4}, {"id": "86c16dd4-0d9d-40bb-a495-02c1a2ac06f1", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-240, -340], "parameters": {"color": 5, "width": 400, "height": 360, "content": "### 🧠 Place your logic here!\nYour Agent logic goes here.\nYou can, for example, use an **AI Agent** to provide information back to your Retell agent and/or **make an action in a third party service**. For example: book an event, add a contact to a CRM, etc. \nSky is the limit! 🙌"}, "typeVersion": 1}, {"id": "f8be07ca-1d3f-46cb-b858-bb22b15efe0f", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-560, -340], "parameters": {"color": 5, "width": 220, "height": 360, "content": "### Retell Custom Function Webhook\nPOST Webhook received from Retell's Custom Function each time it is triggered by Retell's Voice Agent"}, "typeVersion": 1}, {"id": "94ec297f-aa00-4358-b1f9-834438548a19", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [200, -340], "parameters": {"color": 5, "width": 300, "height": 360, "content": "### Retell Custom Function Response\nResponse to the webhook that will be provided back to Retell's Voice Agent.\n\n**Modify the response accordingly** if you want anything else than the first incoming input being sent back"}, "typeVersion": 1}, {"id": "baff741b-8cd3-4f7d-a56f-5029e1babc26", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-1500, -700], "parameters": {"color": 7, "width": 880, "height": 1220, "content": "## Connect Retell Voice Agents to Custom Functions\n\n## Overview\n- This workflow allows you to **trigger custom logic in n8n directly from Retell's Voice Agent** using [Custom Functions](https://docs.retellai.com/build/conversation-flow/custom-function#custom-function).\n- It captures a POST webhook from Retell every time a Voice Agent reaches a Custom Function node.\n- You can plug in any logic—call an external API, book a meeting, update a CRM, or even return a dynamic response back to the agent.\n\n## Who is it for\nFor builders using [Retell](https://www.retellai.com/) who want to **extend Voice Agent functionality** with real-time custom workflows or AI-generated responses.\n\n## Prerequisites\n- Have a [Retell AI Account](https://www.retellai.com/)\n- A Retell agent with a Custom Function node in its conversation flow (see template below)\n- Set your n8n webhook URL in the Custom Function configuration (see \"How to use it\" below)\n- (Optional) Familiarity with [Retell's Custom Function docs](https://docs.retellai.com/build/conversation-flow/custom-function#custom-function)\n- Start a conversation with the agent (text or voice)\n\n## Retell Agent Example\nTo get you started, **[we've prepared a Retell Agent](https://drive.google.com/file/d/1rAcsNz-f8SyuOxO0VJ_84oPscYFpir4-/view?usp=sharing)** ready to be imported, that includes the call to this template.\n- Import the agent to your Retell workspace (top-right button [on your agent's page](https://dashboard.retellai.com/agents))\n- You will need to modify the function URL in order to call your own instance.\n- This template is a simple hotel agent that calls the custom function to confirm a booking, passing basic formatted data.\n\n## How it works\n- Retell sends a webhook to n8n whenever a Custom Function is triggered during a call (or test chat).\n- The webhook includes:\n    - Full call context (transcript, call ID, etc.)\n    - Parameters defined in the Retell function node\n- You can process this data and return a response string back to the Voice Agent in real-time.\n\n## How to use it\n1. Copy the webhook URL (e.g. `https://your-instance.app.n8n.cloud/webhook/hotel-retell-template`)\n2. Modify the Retell Custom Function webhook URL (see template description for screenshots)\n    - Edit the function\n    - Modify the URL\n3. Modify the logic in the Set node or replace it with your own custom flow\n4. Deploy and test: Retell will hit your n8n workflow during the conversation\n\n## Extension Ideas\n- Call a third-party API to fetch data (e.g. hotel availability, CRM records)\n- Use an LLM node to generate dynamic responses\n- Trigger a parallel automation (Slack message, calendar invite, etc.)\n\n#### 👉 [Reach out to us](mailto:<EMAIL>) if you're interested in **analyzing your Retell Agent conversations**.\n"}, "typeVersion": 1}], "pinData": {"Webhook": [{"body": {"args": {"guest-name": "<PERSON>", "hotel-name": "Agent Studios", "total-cost": "190", "check-in-date": "2025-03-29", "check-out-date": "2025-03-30", "number-of-nights": 1, "room-type-booked": "Deluxe Suite"}, "call": {"call_id": "playground", "latency": {}, "call_type": "web_call", "tool_mocks": [], "transcript": "Agent: \nUser: Hi, I would like to make a reservation for me and my wife on March 29th. We have a budget of 250 euros. Can you help me with that?\nAgent: Good day, and welcome to Agent Studios. I'm <PERSON>, and I'm here to assist you with your reservation. May I have your name and email address, please?\nUser: My name is <PERSON>, and my email <NAME_EMAIL>.\nAgent: Thank you, <PERSON>. For your stay on March 29th, we have the Deluxe Suite available, which is priced at 190 euros per night. This fits within your budget of 250 euros. Would you like to proceed with booking the Deluxe Suite for one night?\nUser: Yes, that sounds great! Please proceed with the booking for the Deluxe Suite for one night on March 29th.\n", "transcript_object": [{"role": "agent", "words": [], "content": ""}, {"role": "user", "words": [{"end": 1, "word": "Hi, ", "start": 1}, {"end": 1, "word": "I ", "start": 1}, {"end": 1, "word": "would ", "start": 1}, {"end": 1, "word": "like ", "start": 1}, {"end": 1, "word": "to ", "start": 1}, {"end": 1, "word": "make ", "start": 1}, {"end": 1, "word": "a ", "start": 1}, {"end": 1, "word": "reservation ", "start": 1}, {"end": 1, "word": "for ", "start": 1}, {"end": 1, "word": "me ", "start": 1}, {"end": 1, "word": "and ", "start": 1}, {"end": 1, "word": "my ", "start": 1}, {"end": 1, "word": "wife ", "start": 1}, {"end": 1, "word": "on ", "start": 1}, {"end": 1, "word": "March ", "start": 1}, {"end": 1, "word": "29th. ", "start": 1}, {"end": 1, "word": "We ", "start": 1}, {"end": 1, "word": "have ", "start": 1}, {"end": 1, "word": "a ", "start": 1}, {"end": 1, "word": "budget ", "start": 1}, {"end": 1, "word": "of ", "start": 1}, {"end": 1, "word": "250 ", "start": 1}, {"end": 1, "word": "euros. ", "start": 1}, {"end": 1, "word": "Can ", "start": 1}, {"end": 1, "word": "you ", "start": 1}, {"end": 1, "word": "help ", "start": 1}, {"end": 1, "word": "me ", "start": 1}, {"end": 1, "word": "with ", "start": 1}, {"end": 1, "word": "that?", "start": 1}], "content": "Hi, I would like to make a reservation for me and my wife on March 29th. We have a budget of 250 euros. Can you help me with that?"}, {"role": "agent", "words": [{"end": 2, "word": "Good ", "start": 2}, {"end": 2, "word": "day, ", "start": 2}, {"end": 2, "word": "and ", "start": 2}, {"end": 2, "word": "welcome ", "start": 2}, {"end": 2, "word": "to ", "start": 2}, {"end": 2, "word": "Agent ", "start": 2}, {"end": 2, "word": "Studios ", "start": 2}, {"end": 2, "word": "I'm ", "start": 2}, {"end": 2, "word": "<PERSON>, ", "start": 2}, {"end": 2, "word": "and ", "start": 2}, {"end": 2, "word": "I'm ", "start": 2}, {"end": 2, "word": "here ", "start": 2}, {"end": 2, "word": "to ", "start": 2}, {"end": 2, "word": "assist ", "start": 2}, {"end": 2, "word": "you ", "start": 2}, {"end": 2, "word": "with ", "start": 2}, {"end": 2, "word": "your ", "start": 2}, {"end": 2, "word": "reservation. ", "start": 2}, {"end": 2, "word": "May ", "start": 2}, {"end": 2, "word": "I ", "start": 2}, {"end": 2, "word": "have ", "start": 2}, {"end": 2, "word": "your ", "start": 2}, {"end": 2, "word": "name ", "start": 2}, {"end": 2, "word": "and ", "start": 2}, {"end": 2, "word": "email ", "start": 2}, {"end": 2, "word": "address, ", "start": 2}, {"end": 2, "word": "please?", "start": 2}], "content": "Good day, and welcome to Agent Studios. I'm <PERSON>, and I'm here to assist you with your reservation. May I have your name and email address, please?"}, {"role": "user", "words": [{"end": 3, "word": "My ", "start": 3}, {"end": 3, "word": "name ", "start": 3}, {"end": 3, "word": "is ", "start": 3}, {"end": 3, "word": "<PERSON> ", "start": 3}, {"end": 3, "word": "<PERSON>, ", "start": 3}, {"end": 3, "word": "and ", "start": 3}, {"end": 3, "word": "my ", "start": 3}, {"end": 3, "word": "email ", "start": 3}, {"end": 3, "word": "address ", "start": 3}, {"end": 3, "word": "is ", "start": 3}, {"end": 3, "word": "<EMAIL>.", "start": 3}], "content": "My name is <PERSON>, and my email <NAME_EMAIL>."}, {"role": "agent", "words": [{"end": 4, "word": "Thank ", "start": 4}, {"end": 4, "word": "you, ", "start": 4}, {"end": 4, "word": "<PERSON>. ", "start": 4}, {"end": 4, "word": "For ", "start": 4}, {"end": 4, "word": "your ", "start": 4}, {"end": 4, "word": "stay ", "start": 4}, {"end": 4, "word": "on ", "start": 4}, {"end": 4, "word": "March ", "start": 4}, {"end": 4, "word": "29th, ", "start": 4}, {"end": 4, "word": "we ", "start": 4}, {"end": 4, "word": "have ", "start": 4}, {"end": 4, "word": "the ", "start": 4}, {"end": 4, "word": "Deluxe ", "start": 4}, {"end": 4, "word": "Suite ", "start": 4}, {"end": 4, "word": "available, ", "start": 4}, {"end": 4, "word": "which ", "start": 4}, {"end": 4, "word": "is ", "start": 4}, {"end": 4, "word": "priced ", "start": 4}, {"end": 4, "word": "at ", "start": 4}, {"end": 4, "word": "190 ", "start": 4}, {"end": 4, "word": "euros ", "start": 4}, {"end": 4, "word": "per ", "start": 4}, {"end": 4, "word": "night. ", "start": 4}, {"end": 4, "word": "This ", "start": 4}, {"end": 4, "word": "fits ", "start": 4}, {"end": 4, "word": "within ", "start": 4}, {"end": 4, "word": "your ", "start": 4}, {"end": 4, "word": "budget ", "start": 4}, {"end": 4, "word": "of ", "start": 4}, {"end": 4, "word": "250 ", "start": 4}, {"end": 4, "word": "euros. ", "start": 4}, {"end": 4, "word": "Would ", "start": 4}, {"end": 4, "word": "you ", "start": 4}, {"end": 4, "word": "like ", "start": 4}, {"end": 4, "word": "to ", "start": 4}, {"end": 4, "word": "proceed ", "start": 4}, {"end": 4, "word": "with ", "start": 4}, {"end": 4, "word": "booking ", "start": 4}, {"end": 4, "word": "the ", "start": 4}, {"end": 4, "word": "Deluxe ", "start": 4}, {"end": 4, "word": "Suite ", "start": 4}, {"end": 4, "word": "for ", "start": 4}, {"end": 4, "word": "one ", "start": 4}, {"end": 4, "word": "night?", "start": 4}], "content": "Thank you, <PERSON>. For your stay on March 29th, we have the Deluxe Suite available, which is priced at 190 euros per night. This fits within your budget of 250 euros. Would you like to proceed with booking the Deluxe Suite for one night?"}, {"role": "user", "words": [{"end": 5, "word": "Yes, ", "start": 5}, {"end": 5, "word": "that ", "start": 5}, {"end": 5, "word": "sounds ", "start": 5}, {"end": 5, "word": "great! ", "start": 5}, {"end": 5, "word": "Please ", "start": 5}, {"end": 5, "word": "proceed ", "start": 5}, {"end": 5, "word": "with ", "start": 5}, {"end": 5, "word": "the ", "start": 5}, {"end": 5, "word": "booking ", "start": 5}, {"end": 5, "word": "for ", "start": 5}, {"end": 5, "word": "the ", "start": 5}, {"end": 5, "word": "Deluxe ", "start": 5}, {"end": 5, "word": "Suite ", "start": 5}, {"end": 5, "word": "for ", "start": 5}, {"end": 5, "word": "one ", "start": 5}, {"end": 5, "word": "night ", "start": 5}, {"end": 5, "word": "on ", "start": 5}, {"end": 5, "word": "March ", "start": 5}, {"end": 5, "word": "29th.", "start": 5}], "content": "Yes, that sounds great! Please proceed with the booking for the Deluxe Suite for one night on March 29th."}], "transcript_with_tool_calls": [{"role": "agent", "words": [], "content": ""}, {"role": "user", "words": [{"end": 1, "word": "Hi, ", "start": 1}, {"end": 1, "word": "I ", "start": 1}, {"end": 1, "word": "would ", "start": 1}, {"end": 1, "word": "like ", "start": 1}, {"end": 1, "word": "to ", "start": 1}, {"end": 1, "word": "make ", "start": 1}, {"end": 1, "word": "a ", "start": 1}, {"end": 1, "word": "reservation ", "start": 1}, {"end": 1, "word": "for ", "start": 1}, {"end": 1, "word": "me ", "start": 1}, {"end": 1, "word": "and ", "start": 1}, {"end": 1, "word": "my ", "start": 1}, {"end": 1, "word": "wife ", "start": 1}, {"end": 1, "word": "on ", "start": 1}, {"end": 1, "word": "March ", "start": 1}, {"end": 1, "word": "29th. ", "start": 1}, {"end": 1, "word": "We ", "start": 1}, {"end": 1, "word": "have ", "start": 1}, {"end": 1, "word": "a ", "start": 1}, {"end": 1, "word": "budget ", "start": 1}, {"end": 1, "word": "of ", "start": 1}, {"end": 1, "word": "250 ", "start": 1}, {"end": 1, "word": "euros. ", "start": 1}, {"end": 1, "word": "Can ", "start": 1}, {"end": 1, "word": "you ", "start": 1}, {"end": 1, "word": "help ", "start": 1}, {"end": 1, "word": "me ", "start": 1}, {"end": 1, "word": "with ", "start": 1}, {"end": 1, "word": "that?", "start": 1}], "content": "Hi, I would like to make a reservation for me and my wife on March 29th. We have a budget of 250 euros. Can you help me with that?"}, {"role": "agent", "words": [{"end": 2, "word": "Good ", "start": 2}, {"end": 2, "word": "day, ", "start": 2}, {"end": 2, "word": "and ", "start": 2}, {"end": 2, "word": "welcome ", "start": 2}, {"end": 2, "word": "to ", "start": 2}, {"end": 2, "word": "Agent ", "start": 2}, {"end": 2, "word": "Studios ", "start": 2}, {"end": 2, "word": "I'm ", "start": 2}, {"end": 2, "word": "<PERSON>, ", "start": 2}, {"end": 2, "word": "and ", "start": 2}, {"end": 2, "word": "I'm ", "start": 2}, {"end": 2, "word": "here ", "start": 2}, {"end": 2, "word": "to ", "start": 2}, {"end": 2, "word": "assist ", "start": 2}, {"end": 2, "word": "you ", "start": 2}, {"end": 2, "word": "with ", "start": 2}, {"end": 2, "word": "your ", "start": 2}, {"end": 2, "word": "reservation. ", "start": 2}, {"end": 2, "word": "May ", "start": 2}, {"end": 2, "word": "I ", "start": 2}, {"end": 2, "word": "have ", "start": 2}, {"end": 2, "word": "your ", "start": 2}, {"end": 2, "word": "name ", "start": 2}, {"end": 2, "word": "and ", "start": 2}, {"end": 2, "word": "email ", "start": 2}, {"end": 2, "word": "address, ", "start": 2}, {"end": 2, "word": "please?", "start": 2}], "content": "Good day, and welcome to Agent Studios. I'm <PERSON>, and I'm here to assist you with your reservation. May I have your name and email address, please?"}, {"role": "user", "words": [{"end": 3, "word": "My ", "start": 3}, {"end": 3, "word": "name ", "start": 3}, {"end": 3, "word": "is ", "start": 3}, {"end": 3, "word": "<PERSON> ", "start": 3}, {"end": 3, "word": "<PERSON>, ", "start": 3}, {"end": 3, "word": "and ", "start": 3}, {"end": 3, "word": "my ", "start": 3}, {"end": 3, "word": "email ", "start": 3}, {"end": 3, "word": "address ", "start": 3}, {"end": 3, "word": "is ", "start": 3}, {"end": 3, "word": "<EMAIL>.", "start": 3}], "content": "My name is <PERSON>, and my email <NAME_EMAIL>."}, {"role": "agent", "words": [{"end": 4, "word": "Thank ", "start": 4}, {"end": 4, "word": "you, ", "start": 4}, {"end": 4, "word": "<PERSON>. ", "start": 4}, {"end": 4, "word": "For ", "start": 4}, {"end": 4, "word": "your ", "start": 4}, {"end": 4, "word": "stay ", "start": 4}, {"end": 4, "word": "on ", "start": 4}, {"end": 4, "word": "March ", "start": 4}, {"end": 4, "word": "29th, ", "start": 4}, {"end": 4, "word": "we ", "start": 4}, {"end": 4, "word": "have ", "start": 4}, {"end": 4, "word": "the ", "start": 4}, {"end": 4, "word": "Deluxe ", "start": 4}, {"end": 4, "word": "Suite ", "start": 4}, {"end": 4, "word": "available, ", "start": 4}, {"end": 4, "word": "which ", "start": 4}, {"end": 4, "word": "is ", "start": 4}, {"end": 4, "word": "priced ", "start": 4}, {"end": 4, "word": "at ", "start": 4}, {"end": 4, "word": "190 ", "start": 4}, {"end": 4, "word": "euros ", "start": 4}, {"end": 4, "word": "per ", "start": 4}, {"end": 4, "word": "night. ", "start": 4}, {"end": 4, "word": "This ", "start": 4}, {"end": 4, "word": "fits ", "start": 4}, {"end": 4, "word": "within ", "start": 4}, {"end": 4, "word": "your ", "start": 4}, {"end": 4, "word": "budget ", "start": 4}, {"end": 4, "word": "of ", "start": 4}, {"end": 4, "word": "250 ", "start": 4}, {"end": 4, "word": "euros. ", "start": 4}, {"end": 4, "word": "Would ", "start": 4}, {"end": 4, "word": "you ", "start": 4}, {"end": 4, "word": "like ", "start": 4}, {"end": 4, "word": "to ", "start": 4}, {"end": 4, "word": "proceed ", "start": 4}, {"end": 4, "word": "with ", "start": 4}, {"end": 4, "word": "booking ", "start": 4}, {"end": 4, "word": "the ", "start": 4}, {"end": 4, "word": "Deluxe ", "start": 4}, {"end": 4, "word": "Suite ", "start": 4}, {"end": 4, "word": "for ", "start": 4}, {"end": 4, "word": "one ", "start": 4}, {"end": 4, "word": "night?", "start": 4}], "content": "Thank you, <PERSON>. For your stay on March 29th, we have the Deluxe Suite available, which is priced at 190 euros per night. This fits within your budget of 250 euros. Would you like to proceed with booking the Deluxe Suite for one night?"}, {"role": "user", "words": [{"end": 5, "word": "Yes, ", "start": 5}, {"end": 5, "word": "that ", "start": 5}, {"end": 5, "word": "sounds ", "start": 5}, {"end": 5, "word": "great! ", "start": 5}, {"end": 5, "word": "Please ", "start": 5}, {"end": 5, "word": "proceed ", "start": 5}, {"end": 5, "word": "with ", "start": 5}, {"end": 5, "word": "the ", "start": 5}, {"end": 5, "word": "booking ", "start": 5}, {"end": 5, "word": "for ", "start": 5}, {"end": 5, "word": "the ", "start": 5}, {"end": 5, "word": "Deluxe ", "start": 5}, {"end": 5, "word": "Suite ", "start": 5}, {"end": 5, "word": "for ", "start": 5}, {"end": 5, "word": "one ", "start": 5}, {"end": 5, "word": "night ", "start": 5}, {"end": 5, "word": "on ", "start": 5}, {"end": 5, "word": "March ", "start": 5}, {"end": 5, "word": "29th.", "start": 5}], "content": "Yes, that sounds great! Please proceed with the booking for the Deluxe Suite for one night on March 29th."}, {"name": "send_email_confirmation", "role": "tool_call_invocation", "arguments": "{\"room-type-booked\":\"Deluxe Suite\",\"number-of-nights\":1,\"check-in-date\":\"2025-03-29\",\"check-out-date\":\"2025-03-30\",\"guest-name\":\"<PERSON>\",\"total-cost\":\"190\",\"hotel-name\":\"Agent Studios\"}", "tool_call_id": "ae23cddc1dec67cf"}], "retell_llm_dynamic_variables": {}}, "name": "send_email_confirmation"}, "query": {}, "params": {}, "headers": {"host": "your-instance.app.n8n.cloud", "accept": "application/json, text/plain, */*", "cf-ray": "925f6cd523d6c77a-SEA", "cdn-loop": "cloudflare; loops=1; subreqs=1", "cf-ew-via": "15", "cf-worker": "n8n.cloud", "x-real-ip": "************", "cf-visitor": "{\"scheme\":\"https\"}", "user-agent": "axios/1.7.7", "cf-ipcountry": "US", "content-type": "application/json", "x-is-trusted": "yes", "content-length": "12572", "accept-encoding": "gzip, br", "x-forwarded-for": "************, **************", "cf-connecting-ip": "************", "x-forwarded-host": "your-instance.app.n8n.cloud", "x-forwarded-port": "443", "x-forwarded-proto": "https", "x-forwarded-server": "traefik-prod-users-gwc-44-68df68bcf-shrw2", "x-retell-signature": "v=1742916370585,d=f2a2305f630c02e30127dabb9db80a9756c0ed03318245e5c4b5745620ac7b72"}, "webhookUrl": "https://your-instance.app.n8n.cloud/webhook/hotel-retell-template", "executionMode": "production"}]}, "connections": {"Webhook": {"main": [[{"node": "[Replace me!] Set response", "type": "main", "index": 0}]]}, "[Replace me!] Set response": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}}}