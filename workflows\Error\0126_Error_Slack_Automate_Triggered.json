{"nodes": [{"name": "<PERSON><PERSON>ck", "type": "n8n-nodes-base.slack", "position": [650, 300], "parameters": {"text": "=🐞 What?!\n*This execution{{$node[\"Error Trigger\"].json[\"workflow\"][\"name\"]}} went wrong*\\nWhy don't you go take a look {{$node[\"Error Trigger\"].json[\"execution\"][\"url\"]}}", "channel": "", "attachments": [], "otherOptions": {}}, "credentials": {"slackApi": {"id": "", "name": ""}}, "typeVersion": 1}, {"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.errorTrigger", "position": [450, 300], "parameters": {}, "executeOnce": false, "retryOnFail": false, "typeVersion": 1, "alwaysOutputData": true}], "connections": {"Error Trigger": {"main": [[{"node": "<PERSON><PERSON>ck", "type": "main", "index": 0}]]}}}