{"id": "27", "name": "Create a release and get all releases", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [210, 300], "parameters": {}, "typeVersion": 1}, {"name": "Sentry.io", "type": "n8n-nodes-base.sentryIo", "position": [410, 300], "parameters": {"url": "", "version": "0.0.1", "projects": [""], "resource": "release", "operation": "create", "additionalFields": {}, "organizationSlug": ""}, "credentials": {"sentryIoApi": "sentry"}, "typeVersion": 1}, {"name": "Sentry.io1", "type": "n8n-nodes-base.sentryIo", "position": [610, 300], "parameters": {"resource": "release", "operation": "getAll", "returnAll": true, "additionalFields": {}, "organizationSlug": ""}, "credentials": {"sentryIoApi": "sentry"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Sentry.io": {"main": [[{"node": "Sentry.io1", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Sentry.io", "type": "main", "index": 0}]]}}}