{"meta": {"instanceId": "=", "templateCredsSetupCompleted": true}, "nodes": [{"id": "4815105b-4175-45ad-85bc-07917de9526c", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-140, -720], "parameters": {}, "typeVersion": 1}, {"id": "b8f2a706-4868-4f0d-99a1-c31e1f7022e3", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1220, -580], "parameters": {"text": "=Article Title: {{ $json.title }}\nArticle Link: {{ $json.link }}\nArticle Content: {{ $json.clean_content }}", "options": {"systemMessage": "=You are a content marketing assistant. Based on the article metadata (ID, title) and cleaned content, generate a short LinkedIn promotional message for a professional audience.\n\nFollow this structure:\n\nStart with a hook that grabs attention (a bold insight, surprising fact, or thought-provoking question).\n\nBriefly summarize the article’s value — what readers will learn or gain from it.\n\nInclude a clear call-to-action encouraging readers to read the article.\n\nEnd with this author signature and invitation:\n“<PERSON>\n<PERSON><PERSON> Chain Data Scientist & Founder of LogiGreen\n📩 Contact me: https://logi-green.com/contactus”\n\nUse a professional and engaging tone. Do not include hashtags or Markdown formatting."}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "ac1538f6-67ef-4fd0-b4a9-d44b49149e5f", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1160, -420], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "typeVersion": 1.2}, {"id": "bac79ecf-b92d-42ba-bb0f-f1e1f85ca1c9", "name": "Clean HTML", "type": "n8n-nodes-base.code", "position": [780, -620], "parameters": {"jsCode": "const htmlContent = $input.first().json.content;\n\nconst cleanText = htmlContent\n  .replace(/<[^>]*>/g, '') // remove  tags\n  .replace(/\\s+/g, ' ')    // normalize spaces\n  .replace(/&nbsp;/g, ' ') // decode common entity\n  .trim();\n\nreturn [\n  {\n    json: {\n      clean_content: cleanText\n    }\n  }\n];\n"}, "typeVersion": 2}, {"id": "fa57b494-370a-4f37-bcbe-38ba6138da76", "name": "Extract Blog Posts", "type": "n8n-nodes-base.ghost", "position": [80, -720], "parameters": {"limit": 3, "options": {}, "operation": "getAll"}, "notesInFlow": true, "typeVersion": 1}, {"id": "dc19b6a4-fa17-41b4-8f8c-352519f07569", "name": "Extract Post Content", "type": "n8n-nodes-base.set", "position": [300, -720], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "00b337cd-1c61-4f19-8c51-b76f3a8dece1", "name": "id", "type": "string", "value": "={{ $json.id }}"}, {"id": "8d38f4bc-bca6-4343-8c5e-5d9fd9cbe178", "name": "title", "type": "string", "value": "={{ $json.title }}"}, {"id": "c34ddd76-0db6-4225-82fa-04d5542f9c7c", "name": "featured_image", "type": "string", "value": "={{ $json.feature_image }}"}, {"id": "c0f9593c-0d5a-4659-9e25-91b098318bd6", "name": "excerpt", "type": "string", "value": "={{ $json.excerpt }}"}, {"id": "0d11d3d5-49f8-473a-8602-b49769f88005", "name": "content", "type": "string", "value": "={{ $json.html }}"}, {"id": "ec89a00d-9d76-4594-a8ce-98aa177e6737", "name": "link", "type": "string", "value": "={{ $json.url }}"}]}}, "notesInFlow": true, "typeVersion": 3.4}, {"id": "45656e13-5f03-48f9-8422-0ea3993e3289", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-180, -1080], "parameters": {"color": 7, "width": 200, "height": 520, "content": "### 1. Workflow Trigger\nThis workflow uses simple trigger.\n\n#### How to setup?\n*Nothing to do.*\n"}, "typeVersion": 1}, {"id": "7b8c3c49-069f-464b-acd2-a1a047fb2138", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [40, -1080], "parameters": {"color": 7, "width": 400, "height": 520, "content": "### 2. Extract Blog Posts Content\nThe Ghost node extracts all the posts of your blog with content and metadata. In the second node, we extract description, URL, content and features image url.\n\n#### How to setup?\n- **Ghost Account API**:\n   1. Add your Ghost Blog Account Credentials\n   2. Select the number of Blog Posts you want to collect\n  [Learn more about the Ghost Node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.ghost)\n\n"}, "typeVersion": 1}, {"id": "0a5e4045-7df2-4713-a475-509844c58344", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [480, -1080], "parameters": {"color": 7, "width": 1520, "height": 800, "content": "### 3. Generate a Linkedin Post for each Post with an AI Agent\nThis block loops through all the posts pulled by the Ghost Node, send the content to the AI agent that generates a Linkedin post. The results are combined and pulled in a Google Sheet.\n\n#### How to setup?\n- **AI Agent with the Chat Model**:\n   1. Add a **chat model** with the required credentials *(Example: Open AI 4o-mini)*\n   2. Adapt the system prompt with your **post signature** and additional points you want to add in your posts\n  [Learn more about the AI Agent Node](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.agent)\n- **Record Long Break in the Google Sheet Node**:\n   1. Add your Google Sheet API credentials to access the Google Sheet file\n   2. Select the file using the list, an URL or an ID\n   3. Select the sheet in which you want to record your working sessions\n   4. Map the fields\n  [Learn more about the Google Sheet Node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googlesheets)\n\n"}, "typeVersion": 1}, {"id": "29b09c7f-c39c-414d-b9d5-897b0d540328", "name": "Record the posts", "type": "n8n-nodes-base.googleSheets", "position": [1840, -480], "parameters": {"columns": {"value": {"id": "={{ $json.id }}", "title": "={{ $json.title }}", "content": "={{ $json.content }}", "excerpt": "={{ $json.excerpt }}", "clean_content": "={{ $json.clean_content }}", "linkedin_post": "={{ $json.output }}", "featured_image": "={{ $json.featured_image }}"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "id", "defaultMatch": true, "canBeUsedToMatch": true}, {"id": "title", "type": "string", "display": true, "required": false, "displayName": "title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "featured_image", "type": "string", "display": true, "required": false, "displayName": "featured_image", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "excerpt", "type": "string", "display": true, "required": false, "displayName": "excerpt", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "content", "type": "string", "display": true, "required": false, "displayName": "content", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "clean_content", "type": "string", "display": true, "required": false, "displayName": "clean_content", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedin_post", "type": "string", "display": true, "required": false, "displayName": "linkedin_post", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["id"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "=", "cachedResultName": "="}, "documentId": {"__rl": true, "mode": "list", "value": "=", "cachedResultUrl": "=", "cachedResultName": "="}}, "notesInFlow": true, "typeVersion": 4.5}, {"id": "6f1c58db-a4bf-421a-a182-8149dac28725", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [1600, -720], "parameters": {"mode": "combineBySql"}, "notesInFlow": true, "typeVersion": 3}, {"id": "ebae3ccc-2727-44d9-9309-320c7d8e8349", "name": "Add Clean HTML", "type": "n8n-nodes-base.merge", "position": [1020, -720], "parameters": {"mode": "combineBySql"}, "notesInFlow": true, "typeVersion": 3}, {"id": "d839ca8d-f898-4617-955f-9c6d9a5412b7", "name": "Loop Over Posts", "type": "n8n-nodes-base.splitInBatches", "position": [580, -720], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "7e759203-b524-4cc5-89df-5e113c800504", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-180, -540], "parameters": {"width": 660, "height": 460, "content": "### [📺Complete Tutorial](https://www.youtube.com/watch?v=Lhi6hV6rWEo)\n![Thumbnail](https://www.samirsaci.com/content/images/2025/04/temp-4.png)\n"}, "typeVersion": 1}], "pinData": {}, "connections": {"AI Agent": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Clean HTML": {"main": [[{"node": "Add Clean HTML", "type": "main", "index": 1}]]}, "Add Clean HTML": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Merge Linkedin": {"main": [[{"node": "Record the posts", "type": "main", "index": 0}]]}, "Loop Over Posts": {"main": [[], [{"node": "Clean HTML", "type": "main", "index": 0}, {"node": "Add Clean HTML", "type": "main", "index": 0}]]}, "Record the posts": {"main": [[{"node": "Loop Over Posts", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Extract Blog Posts": {"main": [[{"node": "Extract Post Content", "type": "main", "index": 0}]]}, "Extract Post Content": {"main": [[{"node": "Loop Over Posts", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Extract Blog Posts", "type": "main", "index": 0}]]}}}