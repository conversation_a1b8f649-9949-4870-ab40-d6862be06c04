{"id": "5", "name": "new", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [540, 350], "parameters": {}, "typeVersion": 1}, {"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.github", "position": [790, 350], "parameters": {"owner": "n8n-io", "resource": "repository", "operation": "getProfile", "repository": "n8n"}, "credentials": {"githubApi": "shraddha"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"On clicking 'execute'": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}}}