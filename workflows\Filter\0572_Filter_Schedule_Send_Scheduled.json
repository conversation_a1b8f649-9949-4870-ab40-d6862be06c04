{"meta": {"instanceId": "46264913bc099c31e7222b2cfd112772e1c7867192afd7716e58254079b3333f"}, "nodes": [{"id": "dac02623-ee83-444b-b039-fd310dee3260", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [700, 1000], "parameters": {}, "typeVersion": 1}, {"id": "7268d9c0-44ae-4226-9e5f-f3b19e3fbfa1", "name": "Notion", "type": "n8n-nodes-base.notion", "position": [1360, 980], "parameters": {"options": {}, "resource": "databasePage", "operation": "getAll", "databaseId": {"__rl": true, "mode": "list", "value": "b1e11f75-06df-42b4-8dd9-557ba937978d", "cachedResultUrl": "https://www.notion.so/b1e11f7506df42b48dd9557ba937978d", "cachedResultName": "Tasks"}, "filterType": "manual"}, "credentials": {"notionApi": {"id": "03mmrqQX1rffebZp", "name": "<PERSON>ion <PERSON>"}}, "typeVersion": 2.2}, {"id": "607752ef-ac76-4a07-a3e7-39be7d5770e7", "name": "Sort by closest deadline", "type": "n8n-nodes-base.sort", "position": [1760, 880], "parameters": {"options": {}, "sortFieldsUi": {"sortField": [{"fieldName": "property_deadline.start"}]}}, "typeVersion": 1}, {"id": "81c6ded2-7766-4351-b597-27794b595283", "name": "Filter for deadline", "type": "n8n-nodes-base.filter", "position": [1600, 880], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "179eecfc-7eea-46b9-a971-78824e5774dc", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.property_deadline.start }}", "rightValue": ""}]}}, "typeVersion": 2.1}, {"id": "21ecbd8d-7a2f-4a0a-8d99-3365c88a187b", "name": "Send Email", "type": "n8n-nodes-base.emailSend", "position": [4100, 900], "parameters": {"html": "={{ $json.html }}", "options": {}, "subject": "Weekly Update about Notion Tasks", "toEmail": "={{ $('Set Workflow vars').item.json.your_email }}", "fromEmail": "<EMAIL>"}, "credentials": {"smtp": {"id": "cvpDbugXPc0TsdmZ", "name": "Unitize - SMTP Mailserver"}}, "typeVersion": 2.1}, {"id": "8379f4e4-cab6-46cd-9ba0-e6bf78076de5", "name": "HTML", "type": "n8n-nodes-base.html", "position": [3720, 900], "parameters": {"html": "<!DOCTYPE html>\n\n<html>\n<head>\n  <meta charset=\"UTF-8\" />\n  <title>Weekly Update about Notion Tasks</title>\n  <meta name=\"color-scheme\" content=\"only\">\n  <style>\n    body {font-family: 'Courier'; color: #15124a; background-color: #ffffff;padding:20px;}\n    .button {background-color: #126bf3; padding: 10px 15px; border-radius: 5px; text-decoration: none; color: white}\n    a {text-decoration: none; color: #126bf3}\n    h3 {margin-top: 0px}\n    .task {background-color: #f0f0f0; padding: 20px; border-radius: 5px;}\n    p:last-child {margin-bottom: 0px;}\n  </style>\n</head>\n<body>\n  <div class=\"container\">\n    <img width=\"40\" src=\"{{ $('Set Workflow vars').item.json.logo_path }}\" />\n    <h1>Weekly Update about Notion Tasks</h1>\n    <p><a class=\"button\" href=\"{{ $('Set Workflow vars').item.json.notion_database_url }}\">To the Task Board in Notion</a></p>\n    <br>\n    {{ $json.html_groups.pluck('html') }}\n  </div>\n</body>\n</html>"}, "typeVersion": 1.2}, {"id": "c86a8391-90ed-450a-b142-85ff62d84ab8", "name": "Aggregate due to tasks", "type": "n8n-nodes-base.aggregate", "position": [2700, 1040], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "due_to"}, "typeVersion": 1}, {"id": "07506629-4244-4270-aecc-87b0237c65e7", "name": "Aggregate overdue tasks", "type": "n8n-nodes-base.aggregate", "position": [2700, 760], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "overdue"}, "typeVersion": 1}, {"id": "93d4f3be-8081-41d9-bd59-5a7a0439c27b", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.pushover", "position": [4100, 1100], "parameters": {"message": "You received a weekly update about your Notion Tasks. Check your mails!", "userKey": "={{ $('Set Workflow vars').item.json.pushover_user_key }}", "priority": 1, "additionalFields": {}}, "credentials": {"pushoverApi": {"id": "Z002A4WQRAOy6XUT", "name": "<PERSON><PERSON><PERSON> - <PERSON>"}}, "typeVersion": 1}, {"id": "112aa538-1497-4a53-85ff-b04504896b81", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [700, 780], "parameters": {"rule": {"interval": [{"field": "weeks", "triggerAtDay": [1], "triggerAtHour": 9}]}}, "typeVersion": 1.2}, {"id": "1ec9608a-9a06-4140-a6e0-2e38b4a8c201", "name": "If deadline is overdue", "type": "n8n-nodes-base.if", "position": [2460, 900], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "e65c0597-d067-423a-8496-35e91a8ddf1b", "operator": {"type": "dateTime", "operation": "beforeOrEquals"}, "leftValue": "={{ $json.property_deadline.start.toDateTime() }}", "rightValue": "={{ $now }}"}]}}, "typeVersion": 2.1}, {"id": "2a25952d-7149-4b42-b520-497997d2838c", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [2220, 900], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3}, {"id": "a80b5658-b83d-45e7-ade3-6a828f26a356", "name": "HTML for Task", "type": "n8n-nodes-base.html", "position": [2000, 1060], "parameters": {"html": "<div class=\"task\">\n  <a href=\"{{ $json.url }}\">\n    <h3>{{ $json.name }}\"</h3>\n  </a>\n  <p>\n    <strong>Deadline: </strong>{{ $json.property_deadline.start.toDateTime().format('dd.MM.yyyy') }}\n    <br>\n    <strong>Prio: </strong>{{ $json.property_prio }}\n    <br>\n    <strong>Status: </strong>{{ $json.property_status }}\n    <br>\n    <strong>Tags: </strong>{{ $json.property_tags }}\n  </p>\n</div>"}, "typeVersion": 1.2}, {"id": "8589e878-249d-43a0-b523-994108b3471b", "name": "HTML due to List", "type": "n8n-nodes-base.html", "position": [2920, 1040], "parameters": {"html": "<h2>Tasks with an <u>upcoming</u> deadline</h2>\n{{ $json.due_to.pluck('html') }}"}, "typeVersion": 1.2}, {"id": "c3eccab0-56f8-4038-8526-f2f51a19fb59", "name": "HTML overdue List", "type": "n8n-nodes-base.html", "position": [2920, 760], "parameters": {"html": "<h2>Tasks which are already <u>overdue</u></h2>\n{{ $if($json.overdue.length > 0, $json.overdue.pluck('html'), 'No overdue tasks. Great!') }}"}, "typeVersion": 1.2}, {"id": "054aa055-6f71-4ecb-80fe-69e5b95f6390", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "position": [3440, 900], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "html_groups"}, "typeVersion": 1}, {"id": "10a799fb-66f3-4fe3-b7b8-01d3a93047d2", "name": "Merge groups", "type": "n8n-nodes-base.merge", "position": [3220, 900], "parameters": {}, "typeVersion": 3}, {"id": "acde5a16-bdd1-4fb6-a986-14ae0b1b1240", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [620, 640], "parameters": {"color": 4, "width": 265.6985239367243, "height": 702.0052321200026, "content": "## Triggers\nCurrent schedule is every monday at 9 am."}, "typeVersion": 1}, {"id": "7766fa25-2486-4eb5-a3d6-23ec2472be94", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1240, 640], "parameters": {"width": 648.1928627806343, "height": 710.0046767294216, "content": "## Fetch, filter and sort notion tasks\nCurrently tasks are filtered by having a deadline and sorted by this"}, "typeVersion": 1}, {"id": "5a44f536-5af9-40dd-a9ae-9d56e4540971", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1920, 640], "parameters": {"width": 442.45022302855995, "height": 707.700156943336, "content": "## Generate HTML template per task\nGenerate a template for each task. It displays the headline and some prperties.\nYou can adjust the template here to show more or less information about each task."}, "typeVersion": 1}, {"id": "765f25ad-3dfa-4a48-9c07-07c7fc0049b6", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [2400, 640], "parameters": {"width": 1185.3702378922917, "height": 707.7001569433354, "content": "## Create groups of tasks to \"overdue\" and \"due to\"\nThis is used to group the tasks and display them accordingly in the final html email template."}, "typeVersion": 1}, {"id": "5fb2c5d9-fba9-463c-9574-38146d14e272", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [3620, 640], "parameters": {"width": 314.11124235866913, "height": 705.8925656662948, "content": "## Create html email template\nHere the whole html email template is set up.\nStyles are applied and some sugar around list of tasks are shown.\nYou may change this to your design and even replace the logo."}, "typeVersion": 1}, {"id": "891e126b-1a34-489b-8a3d-fa3a56308153", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [3980, 640], "parameters": {"color": 4, "width": 355.68584173060526, "height": 704.0849743892543, "content": "## Send email and push notification\nIn the Pushover node you need to place you User Key to receive push notifications.\nUse the Pushover docs to read more about how to setup this service."}, "typeVersion": 1}, {"id": "6cd611da-f3da-4da1-90ae-e5e04a91f915", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [620, 400], "parameters": {"color": 6, "width": 539.*************, "height": 199.**************, "content": "## Dependencies\n- You need to have access to your notion page/database\n- You need to create a Pushover account in order to receive push notifications via this service"}, "typeVersion": 1}, {"id": "8da5e4b3-ee1a-4c62-aeec-40d85fb9754e", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [920, 640], "parameters": {"width": 284.**************, "height": 706.*************, "content": "## Set workflow variables\nAdjust this node to your needs!"}, "typeVersion": 1}, {"id": "4759edd5-edae-4d4c-8cc7-55c8cd8336ca", "name": "Set Workflow vars", "type": "n8n-nodes-base.set", "position": [1000, 880], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "976aac71-63c6-48a4-a965-8112ae3480bf", "name": "logo_path", "type": "string", "value": ""}, {"id": "d9ec1fff-56ff-4c3e-befd-99520b78200e", "name": "pushover_user_key", "type": "string", "value": ""}, {"id": "8271abe0-b9c7-4102-b1a2-37181dcb4ea6", "name": "notion_database_url", "type": "string", "value": ""}, {"id": "ed7c4c03-f8e2-46fa-ac3b-ccabbeab24fa", "name": "your_email", "type": "string", "value": ""}]}}, "typeVersion": 3.4}, {"id": "25f16b5e-7500-4b51-ac8e-e7d8b3b205be", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [1260, 740], "parameters": {"color": 3, "width": 296.4350404695249, "height": 463.2108881217612, "content": "## Adjustment needed\nIn order to not receive \"Done\" or \"Closed\" items from your notion database you need to add some filters in this Notion node.\n\nE.g. you could add \"Status\" is not equal to \"Closed\", to not get closed items."}, "typeVersion": 1}], "pinData": {}, "connections": {"HTML": {"main": [[{"node": "Send Email", "type": "main", "index": 0}, {"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "If deadline is overdue", "type": "main", "index": 0}]]}, "Notion": {"main": [[{"node": "Filter for deadline", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "HTML", "type": "main", "index": 0}]]}, "Merge groups": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "HTML for Task": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "HTML due to List": {"main": [[{"node": "Merge groups", "type": "main", "index": 1}]]}, "Schedule Trigger": {"main": [[{"node": "Set Workflow vars", "type": "main", "index": 0}]]}, "HTML overdue List": {"main": [[{"node": "Merge groups", "type": "main", "index": 0}]]}, "Set Workflow vars": {"main": [[{"node": "Notion", "type": "main", "index": 0}]]}, "Filter for deadline": {"main": [[{"node": "Sort by closest deadline", "type": "main", "index": 0}]]}, "Aggregate due to tasks": {"main": [[{"node": "HTML due to List", "type": "main", "index": 0}]]}, "If deadline is overdue": {"main": [[{"node": "Aggregate overdue tasks", "type": "main", "index": 0}], [{"node": "Aggregate due to tasks", "type": "main", "index": 0}]]}, "Aggregate overdue tasks": {"main": [[{"node": "HTML overdue List", "type": "main", "index": 0}]]}, "Sort by closest deadline": {"main": [[{"node": "HTML for Task", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set Workflow vars", "type": "main", "index": 0}]]}}}