{"meta": {"instanceId": "abc123", "templateCredsSetupCompleted": true}, "nodes": [{"id": "c8481fc0-4cc2-4662-b008-e81eaeb4840b", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.gmailTrigger", "position": [-340, 0], "parameters": {"simple": false, "filters": {"sender": "<EMAIL>", "readStatus": "unread"}, "options": {"downloadAttachments": true, "dataPropertyAttachmentsPrefixName": "attachment_"}, "pollTimes": {"item": [{"mode": "everyMinute"}]}}, "credentials": {"gmailOAuth2": {"id": "egorWvqjkdIG2ovh", "name": "Gmail account - rthoma<PERSON><PERSON>r"}}, "typeVersion": 1.2}, {"id": "fd82d244-dfab-46db-af8e-e674501db75d", "name": "Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [540, 0], "parameters": {"name": "={{ $binary.values()[0].fileName }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "0BwqhgrfUUaOuM2x1NXhxLUlGVEE", "cachedResultUrl": "https://drive.google.com/drive/folders/0BwqhgrfUUaOuM2x1NXhxLUlGVEE?resourcekey=0-fQoeO57wF_vlzIWPZAoNXg", "cachedResultName": "misc"}, "inputDataFieldName": "={{ $binary.keys()[0] }}"}, "credentials": {"googleDriveOAuth2Api": {"id": "fwkvLJni8GfLNqBZ", "name": "Google Drive account - rthomascharter"}}, "typeVersion": 3}, {"id": "5686e523-e12c-41b1-818d-03545122ad6f", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [-120, 0], "parameters": {"options": {}, "fieldToSplitOut": "$binary"}, "typeVersion": 1}, {"id": "1774a0d8-2909-49e4-b0f7-1c3e343602b1", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [420, -360], "parameters": {"width": 380, "height": 820, "content": "## Reference \"Single\" Binary Using Expressions\nThis contains examples of how to reference a single binary in each input item **regardless of its key name.**"}, "typeVersion": 1}, {"id": "204fe711-c5f3-4243-be3b-829419a07c82", "name": "Switch", "type": "n8n-nodes-base.switch", "position": [100, 0], "parameters": {"rules": {"values": [{"outputKey": "Large Files", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "number", "operation": "gt"}, "leftValue": "={{ $binary.values()[0].fileSize.split(' ')[0].toNumber() }}", "rightValue": 300}]}, "renameOutput": true}, {"outputKey": "Medium Files", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "27a59343-5f2a-43b0-a74d-ddb0a988c0cb", "operator": {"type": "number", "operation": "gt"}, "leftValue": "={{ $binary.values()[0].fileSize.split(' ')[0].toNumber() }}", "rightValue": 10}]}, "renameOutput": true}]}, "options": {"fallbackOutput": "extra"}}, "typeVersion": 3.2}, {"id": "1e00cb68-fed2-4f88-be84-4860c26c8a3b", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-200, -240], "parameters": {"width": 260, "height": 460, "content": "## Split Multiple Binary Files\nThis uses the `$binary` name (not expression var) to make individual items for each attachment binary.\n* Note: This still doesn't homogenize the name of each binary."}, "typeVersion": 1}, {"id": "1089eb84-51d3-4669-8a5a-fd1d0855ca41", "name": "Send \" Too Big\" Notification (for example)", "type": "n8n-nodes-base.noOp", "position": [540, -200], "parameters": {}, "typeVersion": 1}, {"id": "29c83742-72b6-40ec-a5fc-aab5ef1d5149", "name": "Ignore Little Graphics / Icons (for example)", "type": "n8n-nodes-base.noOp", "position": [540, 220], "parameters": {}, "typeVersion": 1}], "pinData": {}, "connections": {"Switch": {"main": [[{"node": "Send \" Too Big\" Notification (for example)", "type": "main", "index": 0}], [{"node": "Google Drive", "type": "main", "index": 0}], [{"node": "Ignore Little Graphics / Icons (for example)", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Gmail Trigger": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}}}