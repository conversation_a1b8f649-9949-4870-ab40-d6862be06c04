{"meta": {"instanceId": "c911aed9995230b93fd0d9bc41c258d697c2fe97a3bab8c02baf85963eeda618", "templateCredsSetupCompleted": true}, "nodes": [{"id": "3239827a-ba1c-4131-bfbe-6fa7d35bfaae", "name": "Parameters", "type": "n8n-nodes-base.set", "position": [360, 720], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "1b65def6-4984-497d-a4bc-232af22927ad", "name": "directory", "type": "string", "value": "https://drive.google.com/drive/folders/your-directory-id"}, {"id": "c8c98f88-9f22-4574-88b8-1db99f6e4ec4", "name": "parentdrive", "type": "string", "value": "https://drive.google.com/drive/my-drive"}]}}, "typeVersion": 3.4}, {"id": "de6411b5-5d53-4d42-b3b6-0fc4b84c52ea", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [180, 720], "parameters": {"rule": {"interval": [{"triggerAtHour": 1, "triggerAtMinute": 30}]}}, "typeVersion": 1.2}, {"id": "5b25b86a-c957-4aa3-9c10-b884ee30d9a1", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [160, 460], "parameters": {"color": 3, "width": 560, "height": 140, "content": "## Simplest n8n Workflow Backup – Automating Your Data Security in Google Drive"}, "typeVersion": 1}, {"id": "f5033398-ccf6-4126-9039-6fa8a5968552", "name": "Code", "type": "n8n-nodes-base.code", "position": [720, 720], "parameters": {"jsCode": "return items.map(item => {\n  const jsonData = JSON.stringify(item.json);\n  const binaryData = Buffer.from(jsonData).toString('base64');\n  item.binary = {\n    data: {\n      data: binaryData,\n      mimeType: 'application/json',\n      fileName: 'data.json'\n    }\n  };\n  return item;\n});"}, "typeVersion": 2}, {"id": "b8532f27-a619-4683-a835-096f3a450397", "name": "Get all n8n Workflows", "type": "n8n-nodes-base.n8n", "position": [540, 720], "parameters": {"filters": {}, "requestOptions": {}}, "credentials": {"n8nApi": {"id": "lkbDvgt244nzvwuE", "name": "n8n account"}}, "typeVersion": 1}, {"id": "e6c815c6-00ac-4d91-b92f-dfc0c962bcd3", "name": "Backup to Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [900, 720], "parameters": {"name": "={{  $json.name+ \".json\"}}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive", "cachedResultUrl": "https://drive.google.com/drive/my-drive", "cachedResultName": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "url", "value": "={{ $('Parameters').item.json.directory }}"}}, "retryOnFail": true, "typeVersion": 3}], "pinData": {}, "connections": {"Code": {"main": [[{"node": "Backup to Google Drive", "type": "main", "index": 0}]]}, "Parameters": {"main": [[{"node": "Get all n8n Workflows", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Parameters", "type": "main", "index": 0}]]}, "Get all n8n Workflows": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}}}