{"meta": {"instanceId": "7614f731d9ac88c16c6149bd495fa54d325e3f79ab527ffc7e3b1b1f42dbf884", "templateCredsSetupCompleted": true}, "nodes": [{"id": "56e70371-54a2-4421-9ce2-e626d9c6ef60", "name": "Form", "type": "n8n-nodes-base.formTrigger", "position": [-440, -120], "webhookId": "622256ee-9248-43a2-840e-b28436800aac", "parameters": {"options": {}, "formTitle": "Form", "formFields": {"values": [{"fieldLabel": "name", "requiredField": true}]}}, "typeVersion": 2.2}, {"id": "7cbd263e-ca5b-436e-bdce-c30a66df73a6", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-440, 100], "parameters": {"color": 3, "width": 320, "content": "# 👆\nPlease add authentication to form by selecting Basic Auth to prevent unauthorized access to the form.\n"}, "typeVersion": 1}, {"id": "e1c4d0a8-6e48-45d9-bec6-ee8bb3751b4f", "name": "Copy template file", "type": "n8n-nodes-base.googleDrive", "position": [-220, -120], "parameters": {"name": "={{ $json.name }}", "fileId": {"__rl": true, "mode": "list", "value": "1KyR0UMIOpEkjwa6o1gTggNBP2A6EWwppV59Y6NQuDYw", "cachedResultUrl": "https://docs.google.com/document/d/1KyR0UMIOpEkjwa6o1gTggNBP2A6EWwppV59Y6NQuDYw/edit?usp=drivesdk", "cachedResultName": "Szablon: <PERSON><PERSON><PERSON> testowy"}, "options": {}, "operation": "copy"}, "credentials": {"googleDriveOAuth2Api": {"id": "aPSwizdvnxio0J7A", "name": "Google Drive account 2"}}, "typeVersion": 3}, {"id": "52a27a15-ca68-4381-9a0d-faa1127d7de9", "name": "Format form data", "type": "n8n-nodes-base.code", "position": [0, -120], "parameters": {"jsCode": "const data = [];\n\nObject.keys($('Form').all().map((item) => {\n  Object.keys(item.json).map((bodyProperty) => {\n    data.push({\n      key: bodyProperty,\n      value: item.json[bodyProperty],\n    });\n  })\n}));\n\nreturn {\n  webhook_data: data,\n  pairedItem: 0,\n};"}, "typeVersion": 2}, {"id": "08dbeb42-16f6-4771-bbf8-a358fda54097", "name": "Format form data to Google Doc API", "type": "n8n-nodes-base.code", "position": [220, -120], "parameters": {"jsCode": "const result = [];\n\n$('Format form data').all().map((item) => {\n  item.json.webhook_data.map((data) => {\n    if (\"submittedAt\" !== data.key && \"formMode\" !== data.key) {\n      result.push({\n        \"replaceAllText\": {\n            \"containsText\": {\n              \"text\": `{{${data.key}}}`, \n              \"matchCase\": true\n            },\n            \"replaceText\": `${data.value}`\n        },\n      });\n    }\n  });\n})\n\nreturn {\n  data: result,\n  pairedItem: 0,\n};"}, "typeVersion": 2}, {"id": "99b03034-8c9b-4e23-8cc9-bf9960a4e06a", "name": "Replace data in Google Doc", "type": "n8n-nodes-base.httpRequest", "position": [440, -120], "parameters": {"url": "=https://docs.googleapis.com/v1/documents/{{ $('Copy template file').first().json.id }}:batchUpdate", "method": "POST", "options": {}, "sendBody": true, "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "requests", "value": "={{ $json.data }}"}]}, "nodeCredentialType": "googleDocsOAuth2Api"}, "credentials": {"googleDocsOAuth2Api": {"id": "uhqGUvBF00zGb9vB", "name": "Google Docs account 2"}}, "typeVersion": 4.2}, {"id": "204b57da-2791-40e3-84f5-23a0ed5c8beb", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-440, -420], "parameters": {"color": 6, "width": 520, "height": 180, "content": "# 🙋‍♂️\nThe workflow automatically fetches all form fields and converts them into variables in Google Doc. For example, if you add a text field to the form called \"address,\" you can use the variable {{address}} in the Google Doc template."}, "typeVersion": 1}, {"id": "fa17044d-191e-45eb-9559-563889ad2aef", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [440, 100], "parameters": {"color": 3, "content": "# 👆\nIn Authentication, you need to select Predefined Credential Type and then choose Google Docs OAuth API."}, "typeVersion": 1}], "pinData": {}, "connections": {"Form": {"main": [[{"node": "Copy template file", "type": "main", "index": 0}]]}, "Format form data": {"main": [[{"node": "Format form data to Google Doc API", "type": "main", "index": 0}]]}, "Copy template file": {"main": [[{"node": "Format form data", "type": "main", "index": 0}]]}, "Replace data in Google Doc": {"main": [[]]}, "Format form data to Google Doc API": {"main": [[{"node": "Replace data in Google Doc", "type": "main", "index": 0}]]}}}