{"nodes": [{"name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [450, 300], "webhookId": "39f1b81f-f538-4b94-8788-29180d5e4016", "parameters": {"path": "39f1b81f-f538-4b94-8788-29180d5e4016", "options": {"rawBody": true}, "httpMethod": "POST", "authentication": "headerAuth"}, "credentials": {"httpHeaderAuth": "Webhook Workflow Credentials"}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [650, 300], "parameters": {"values": {"string": [{"name": "Name", "value": "={{$json[\"body\"][\"name\"]}}"}, {"name": "Number", "value": "={{$json[\"body\"][\"number\"]}}"}, {"name": "City", "value": "={{$json[\"body\"][\"city\"]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "Airtable", "type": "n8n-nodes-base.airtable", "position": [850, 300], "parameters": {"table": "Table 1", "options": {}, "operation": "append"}, "credentials": {"airtableApi": "Airtable Credentials n8n"}, "typeVersion": 1}, {"name": "OpenWeatherMap", "type": "n8n-nodes-base.openWeatherMap", "position": [1050, 300], "parameters": {"cityName": "={{$node[\"Webhook\"].json[\"body\"][\"city\"]}}"}, "credentials": {"openWeatherMapApi": "open-weather-map"}, "typeVersion": 1}, {"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.twilio", "position": [1250, 300], "parameters": {"to": "={{$node[\"Webhook\"].json[\"body\"][\"number\"]}}", "message": "=The weather in {{$json[\"name\"]}}, {{$json[\"sys\"][\"country\"]}} is {{$json[\"main\"][\"temp\"]}} ℃ with {{$json[\"weather\"][0][\"description\"]}}. Humidity is {{$json[\"main\"][\"humidity\"]}} and windspeed is {{$json[\"wind\"][\"speed\"]}}."}, "credentials": {"twilioApi": "twi<PERSON>"}, "typeVersion": 1}], "connections": {"Set": {"main": [[{"node": "Airtable", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}, "Airtable": {"main": [[{"node": "OpenWeatherMap", "type": "main", "index": 0}]]}, "OpenWeatherMap": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}}}