{"meta": {"instanceId": "2039b9ae6bdd2cfe7f6a132b7dee66390e92afbc2ec29f67cafa1edf6cad8d55"}, "nodes": [{"id": "cc07b2ca-27f2-4a0e-92f7-2d0fbc63ab04", "name": "libraries_set", "type": "n8n-nodes-base.set", "position": [-520, 260], "parameters": {"options": {"ignoreConversionErrors": false}, "assignments": {"assignments": [{"id": "ab1fe8b7-6706-4f59-bc39-1f80726d2890", "name": "libraries", "type": "string", "value": "axios,cheerio,node-fetch"}]}}, "typeVersion": 3.4}, {"id": "f5f22c1a-704b-47db-9f5e-88feb4db75b8", "name": "trigger_manual", "type": "n8n-nodes-base.manualTrigger", "position": [-720, 260], "parameters": {}, "typeVersion": 1}, {"id": "85f6ad54-a991-407e-b018-fedaa7fb3a4d", "name": "libraries_array", "type": "n8n-nodes-base.set", "position": [-300, 260], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "6fb15a6a-7cda-4080-a255-10f85d188854", "name": "libraries", "type": "array", "value": "={{ $json.libraries.split(\",\") }}"}]}}, "typeVersion": 3.4}, {"id": "19caae56-6cb0-4f90-a4e9-533712a09d14", "name": "libraries_split", "type": "n8n-nodes-base.splitOut", "position": [-100, 260], "parameters": {"options": {"destinationFieldName": "library"}, "fieldToSplitOut": "libraries"}, "typeVersion": 1}, {"id": "fe06a42d-21a1-474a-8442-d703f1664c68", "name": "library_install", "type": "n8n-nodes-base.executeCommand", "position": [120, 260], "parameters": {"command": "=#!/bin/bash\n\n# Get library name from variable\nLIBRARY_NAME=\"{{$json.library}}\"\n\n# Check if library directory exists\nLIBRARY_DIR=\"/home/<USER>/node_modules/$LIBRARY_NAME\"\n\n# Check if library is already installed\nif [ ! -d \"$LIBRARY_DIR\" ]; then\n  echo \"Installing $LIBRARY_NAME...\"\n  npm install \"$LIBRARY_NAME\"\n  \n  # Verify installation\n  if [ -d \"$LIBRARY_DIR\" ]; then\n    echo \"$LIBRARY_NAME was successfully installed.\"\n  else\n    echo \"Failed to install $LIBRARY_NAME. Please check for errors.\"\n    exit 1\n  fi\nelse\n  echo \"$LIBRARY_NAME is already installed at $LIBRARY_DIR.\"\nfi\n", "executeOnce": false}, "typeVersion": 1}, {"id": "8b31c25c-0076-4c71-ae70-80c73d1b8220", "name": "trigger_schedule", "type": "n8n-nodes-base.scheduleTrigger", "position": [-720, 100], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.2}, {"id": "a4a07417-00ce-478e-bcf7-3cc9dd0a75fa", "name": "trigger_instance", "type": "n8n-nodes-base.n8nTrigger", "position": [-720, 440], "parameters": {"events": ["init"]}, "typeVersion": 1}], "pinData": {}, "connections": {"libraries_set": {"main": [[{"node": "libraries_array", "type": "main", "index": 0}]]}, "trigger_manual": {"main": [[{"node": "libraries_set", "type": "main", "index": 0}]]}, "libraries_array": {"main": [[{"node": "libraries_split", "type": "main", "index": 0}]]}, "libraries_split": {"main": [[{"node": "library_install", "type": "main", "index": 0}]]}, "trigger_instance": {"main": [[{"node": "libraries_set", "type": "main", "index": 0}]]}, "trigger_schedule": {"main": [[{"node": "libraries_set", "type": "main", "index": 0}]]}}}