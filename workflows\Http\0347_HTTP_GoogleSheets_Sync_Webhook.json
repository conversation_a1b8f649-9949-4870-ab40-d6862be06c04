{"id": "1", "name": "Dialpad to Syncro", "nodes": [{"name": "GetCustomer", "type": "n8n-nodes-base.httpRequest", "position": [350, 180], "parameters": {"url": "={{$node[\"EnvVariables\"].json[\"syncro_baseurl\"]}}/api/v1/search?query={{$json[\"body\"][\"external_number\"].replace(/\\+/g, '').replace(/^[01]/, '')}}", "options": {}, "authentication": "headerAuth"}, "credentials": {"httpHeaderAuth": "<PERSON><PERSON><PERSON>"}, "typeVersion": 1}, {"name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [-60, 180], "webhookId": "ec452bb5-58d9-4e0d-9cd2-c6df1c2cd957", "parameters": {"path": "moezdialpad", "options": {}, "httpMethod": "POST", "responseData": "allEntries", "responseMode": "lastNode"}, "typeVersion": 1}, {"name": "CreateTicket", "type": "n8n-nodes-base.httpRequest", "position": [1190, 110], "parameters": {"url": "={{$node[\"EnvVariables\"].json[\"syncro_baseurl\"]}}/api/v1/tickets", "options": {"bodyContentType": "json"}, "requestMethod": "POST", "authentication": "headerAuth", "bodyParametersUi": {"parameter": [{"name": "customer_id", "value": "={{$node[\"Contacts\"].json[\"contacts\"][0][\"customer_id\"]}}"}, {"name": "subject", "value": "=Phone call from {{$node[\"Function\"].json[\"contacts\"][0][\"firstname\"]}} {{$node[\"Function\"].json[\"contacts\"][0][\"lastname\"]}} ({{$node[\"Webhook\"].json[\"body\"][\"contact\"][\"phone\"]}})"}, {"name": "status", "value": "In Progress"}, {"name": "contact_id", "value": "={{$node[\"Contacts\"].json[\"contacts\"][0][\"id\"]}}"}, {"name": "user_id", "value": "={{$node[\"EnvVariables\"].parameter[\"values\"][\"string\"][1][\"value\"]}}"}]}}, "credentials": {"httpHeaderAuth": "<PERSON><PERSON><PERSON>"}, "typeVersion": 1}, {"name": "GetTicket", "type": "n8n-nodes-base.httpRequest", "position": [860, 40], "parameters": {"url": "={{$node[\"EnvVariables\"].json[\"syncro_baseurl\"]}}/api/v1/tickets?contact_id={{$json[\"contacts\"][0][\"id\"]}}&status=Not%20Closed", "options": {}, "authentication": "headerAuth"}, "credentials": {"httpHeaderAuth": "<PERSON><PERSON><PERSON>"}, "typeVersion": 1}, {"name": "IFMoreThanOne", "type": "n8n-nodes-base.if", "position": [1000, 40], "parameters": {"conditions": {"number": [{"value1": "={{$node[\"GetTicket\"].json[\"tickets\"].length}}", "value2": 1, "operation": "equal"}], "boolean": [{"value1": "={{$json[\"tickets\"]}}", "value2": true}]}, "combineOperation": "any"}, "typeVersion": 1}, {"name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [1480, -40], "parameters": {"range": "A:B", "options": {"valueInputMode": "USER_ENTERED"}, "sheetId": "xxx", "operation": "append"}, "credentials": {"googleApi": "Google"}, "typeVersion": 1}, {"name": "ForGoogle", "type": "n8n-nodes-base.set", "position": [1340, -40], "parameters": {"values": {"string": [{"name": "Call", "value": "={{$node[\"Webhook\"].json[\"body\"][\"call_id\"]}}"}, {"name": "Ticket", "value": "={{$node[\"GetTicket\"].json[\"tickets\"][0][\"id\"]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "UpdateTicket", "type": "n8n-nodes-base.httpRequest", "position": [1190, -40], "parameters": {"url": "={{$node[\"EnvVariables\"].json[\"syncro_baseurl\"]}}/api/v1/tickets/{{$json[\"tickets\"][0][\"id\"]}}/comment", "options": {}, "requestMethod": "POST", "authentication": "headerAuth", "bodyParametersUi": {"parameter": [{"name": "subject", "value": "=Phone call from {{$node[\"GetCustomer\"].json[\"results\"][0][\"table\"][\"_source\"][\"table\"][\"firstname\"]}} {{$node[\"GetCustomer\"].json[\"results\"][0][\"table\"][\"_source\"][\"table\"][\"lastname\"]}} ({{$node[\"Webhook\"].json[\"body\"][\"contact\"][\"phone\"]}})"}, {"name": "body", "value": "={{$node[\"GetCustomer\"].json[\"results\"][0][\"table\"][\"_source\"][\"table\"][\"firstname\"]}} {{$node[\"GetCustomer\"].json[\"results\"][0][\"table\"][\"_source\"][\"table\"][\"lastname\"]}} called."}, {"name": "hidden", "value": "true"}, {"name": "user_id", "value": "={{$node[\"EnvVariables\"].parameter[\"values\"][\"string\"][1][\"value\"]}}"}]}}, "credentials": {"httpHeaderAuth": "<PERSON><PERSON><PERSON>"}, "typeVersion": 1}, {"name": "ForGoogle1", "type": "n8n-nodes-base.set", "position": [1340, 110], "parameters": {"values": {"string": [{"name": "Call", "value": "={{$node[\"Webhook\"].json[\"body\"][\"call_id\"]}}"}, {"name": "Ticket", "value": "={{$node[\"CreateTicket\"].json[\"ticket\"][\"id\"]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "Google Sheets1", "type": "n8n-nodes-base.googleSheets", "position": [1480, 110], "parameters": {"range": "A:B", "options": {"valueInputMode": "USER_ENTERED"}, "sheetId": "xxx", "operation": "append"}, "credentials": {"googleApi": "Google"}, "typeVersion": 1}, {"name": "NoOp", "type": "n8n-nodes-base.noOp", "position": [830, 220], "parameters": {}, "typeVersion": 1}, {"name": "Contacts", "type": "n8n-nodes-base.function", "position": [510, 180], "parameters": {"functionCode": "const { json: { results } } = items[0];\n\nconst getData = (results, type) => results.filter(r => r.table._index === type).map(x => ({\n   id: x.table._id,\n   firstname: x.table._source.table.firstname,\n   lastname: x.table._source.table.lastname,\n   customer_id: x.table._source.table.customer_id,\n   email: x.table._source.table.email,\n   business_name: x.table._source.table.business_name,\n   phones: x.table._source.table.phones\n }));\n \nreturn [ { json: { contacts: getData(results, 'contacts') } } ];\n"}, "typeVersion": 1, "alwaysOutputData": false}, {"name": "IFContacts", "type": "n8n-nodes-base.if", "position": [670, 180], "parameters": {"conditions": {"number": [{"value1": "={{$node[\"Contacts\"].json[\"contacts\"].length}}", "value2": 1, "operation": "equal"}], "string": [], "boolean": []}}, "typeVersion": 1}, {"name": "Customers", "type": "n8n-nodes-base.function", "position": [510, 370], "parameters": {"functionCode": "const { json: { results } } = items[0];\n\nconst getData = (results, type) => results.filter(r => r.table._index === type).map(x => ({\n   id: x.table._id,\n   firstname: x.table._source.table.firstname,\n   lastname: x.table._source.table.lastname,\n   customer_id: x.table._source.table.customer_id,\n   email: x.table._source.table.email,\n   business_name: x.table._source.table.business_name,\n   phones: x.table._source.table.phones\n }));\n \nreturn [ { json: { customers: getData(results, 'customers') } } ];\n"}, "typeVersion": 1}, {"name": "IFCustomers", "type": "n8n-nodes-base.if", "position": [670, 370], "parameters": {"conditions": {"number": [{"value1": "={{$node[\"Customers\"].json[\"customers\"].length}}", "value2": 1, "operation": "equal"}], "string": [], "boolean": []}}, "typeVersion": 1}, {"name": "NoOp1", "type": "n8n-nodes-base.noOp", "position": [810, 520], "parameters": {}, "typeVersion": 1}, {"name": "CreateTicketForCustomer", "type": "n8n-nodes-base.httpRequest", "position": [860, 360], "parameters": {"url": "={{$node[\"EnvVariables\"].json[\"syncro_baseurl\"]}}/api/v1/tickets", "options": {"bodyContentType": "json"}, "requestMethod": "POST", "authentication": "headerAuth", "bodyParametersUi": {"parameter": [{"name": "customer_id", "value": "={{$node[\"Customers\"].json[\"customers\"][0][\"id\"]}}"}, {"name": "subject", "value": "=Phone call from {{$node[\"Customers\"].json[\"customers\"][0][\"business_name\"]}} ({{$node[\"Webhook\"].json[\"body\"][\"contact\"][\"phone\"]}})"}, {"name": "status", "value": "In Progress"}, {"name": "user_id", "value": "={{$node[\"EnvVariables\"].parameter[\"values\"][\"string\"][1][\"value\"]}}"}]}}, "credentials": {"httpHeaderAuth": "<PERSON><PERSON><PERSON>"}, "typeVersion": 1}, {"name": "ForGoogle2", "type": "n8n-nodes-base.set", "position": [1040, 360], "parameters": {"values": {"string": [{"name": "Call", "value": "={{$node[\"Webhook\"].json[\"body\"][\"call_id\"]}}"}, {"name": "Ticket", "value": "={{$node[\"CreateTicketForCustomer\"].json[\"ticket\"][\"id\"]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "Google Sheets2", "type": "n8n-nodes-base.googleSheets", "position": [1210, 360], "parameters": {"range": "A:B", "options": {"valueInputMode": "USER_ENTERED"}, "sheetId": "xxx", "operation": "append"}, "credentials": {"googleApi": "Google"}, "typeVersion": 1}, {"name": "EnvVariables", "type": "n8n-nodes-base.set", "position": [210, 180], "parameters": {"values": {"string": [{"name": "syncro_baseurl", "value": "https://subdomain.syncromsp.com"}, {"name": "user_id", "value": "1234"}]}, "options": {}}, "typeVersion": 1}, {"name": "IF", "type": "n8n-nodes-base.if", "position": [70, 180], "parameters": {"conditions": {"string": [{"value1": "={{$node[\"Webhook\"].json[\"body\"][\"direction\"]}}", "value2": "inbound"}]}}, "typeVersion": 1}, {"name": "NoOp2", "type": "n8n-nodes-base.noOp", "position": [70, 370], "parameters": {}, "typeVersion": 1}], "active": true, "settings": {}, "connections": {"IF": {"main": [[{"node": "EnvVariables", "type": "main", "index": 0}], [{"node": "NoOp2", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}, "Contacts": {"main": [[{"node": "IFContacts", "type": "main", "index": 0}]]}, "Customers": {"main": [[{"node": "IFCustomers", "type": "main", "index": 0}]]}, "ForGoogle": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}, "GetTicket": {"main": [[{"node": "IFMoreThanOne", "type": "main", "index": 0}]]}, "ForGoogle1": {"main": [[{"node": "Google Sheets1", "type": "main", "index": 0}]]}, "ForGoogle2": {"main": [[{"node": "Google Sheets2", "type": "main", "index": 0}]]}, "IFContacts": {"main": [[{"node": "GetTicket", "type": "main", "index": 0}], [{"node": "NoOp", "type": "main", "index": 0}]]}, "GetCustomer": {"main": [[{"node": "Contacts", "type": "main", "index": 0}, {"node": "Customers", "type": "main", "index": 0}]]}, "IFCustomers": {"main": [[{"node": "CreateTicketForCustomer", "type": "main", "index": 0}], [{"node": "NoOp1", "type": "main", "index": 0}]]}, "CreateTicket": {"main": [[{"node": "ForGoogle1", "type": "main", "index": 0}]]}, "EnvVariables": {"main": [[{"node": "GetCustomer", "type": "main", "index": 0}]]}, "UpdateTicket": {"main": [[{"node": "ForGoogle", "type": "main", "index": 0}]]}, "IFMoreThanOne": {"main": [[{"node": "UpdateTicket", "type": "main", "index": 0}], [{"node": "CreateTicket", "type": "main", "index": 0}]]}, "CreateTicketForCustomer": {"main": [[{"node": "ForGoogle2", "type": "main", "index": 0}]]}}}