{"meta": {"instanceId": "014363851c6b81282e1489df62d7f66bb7c99af5dcb6c1032b3a83a1d72baee4"}, "nodes": [{"id": "0b4eb8e4-e98b-4f67-b134-914a5aa46b4d", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "position": [960, 400], "webhookId": "9c8b833c-7aa7-430d-8fc0-47936f695ddf", "parameters": {"updates": ["message"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "4lzd2F9cNrnR7j0j", "name": "Telegram account"}}, "typeVersion": 1.1}, {"id": "339246f2-76cb-44c4-8828-da0cb5d3ad5e", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1100, 600], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "m3YyjGXFLLWwcnk7", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "70a981e2-7833-473b-a27a-fedf860901cb", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1200, 400], "parameters": {"text": "=Respond to this as a helpful assistant with emojis: {{ $json.message.text }}", "options": {}}, "typeVersion": 1.2}, {"id": "fb6ff65b-56b4-44c4-978a-b9a5c3d535d6", "name": "Telegram", "type": "n8n-nodes-base.telegram", "position": [1560, 400], "parameters": {"text": "={{ $json.output }}", "chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "4lzd2F9cNrnR7j0j", "name": "Telegram account"}}, "typeVersion": 1.1}], "pinData": {}, "connections": {"AI Agent": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "Telegram Trigger": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}}}