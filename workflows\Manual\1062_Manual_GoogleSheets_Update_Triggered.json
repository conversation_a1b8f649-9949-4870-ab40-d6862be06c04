{"id": "5", "name": "Append, lookup, update, and read data from a Google Sheets spreadsheet", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [450, 450], "parameters": {}, "typeVersion": 1}, {"name": "Google Sheets2", "type": "n8n-nodes-base.googleSheets", "position": [1450, 450], "parameters": {"key": "ID", "range": "A:D", "options": {"valueInputMode": "USER_ENTERED", "valueRenderMode": "UNFORMATTED_VALUE"}, "sheetId": "1remFwo--5ehUgIU7UUndKldPI0Xm93e1T3DldD9GOg0", "operation": "update", "authentication": "oAuth2"}, "credentials": {"googleSheetsOAuth2Api": "google-sheet"}, "typeVersion": 1}, {"name": "Set1", "type": "n8n-nodes-base.set", "position": [1250, 450], "parameters": {"values": {"number": [{"name": "Rent", "value": "={{$node[\"Google Sheets1\"].json[\"Rent\"]+100}}"}, {"name": "ID", "value": "={{$node[\"Google Sheets1\"].json[\"ID\"]}}"}], "string": [{"name": "Name", "value": "={{$node[\"Google Sheets1\"].json[\"Name\"]}}"}, {"name": "City", "value": "={{$node[\"Google Sheets1\"].json[\"City\"]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "Google Sheets1", "type": "n8n-nodes-base.googleSheets", "position": [1050, 450], "parameters": {"range": "A:D", "options": {"valueRenderMode": "UNFORMATTED_VALUE", "returnAllMatches": true}, "sheetId": "1remFwo--5ehUgIU7UUndKldPI0Xm93e1T3DldD9GOg0", "operation": "lookup", "lookupValue": "Berlin", "lookupColumn": "City", "authentication": "oAuth2"}, "credentials": {"googleSheetsOAuth2Api": "google-sheet"}, "typeVersion": 1}, {"name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [850, 450], "parameters": {"range": "A:D", "options": {"valueInputMode": "USER_ENTERED"}, "sheetId": "1remFwo--5ehUgIU7UUndKldPI0Xm93e1T3DldD9GOg0", "operation": "append", "authentication": "oAuth2"}, "credentials": {"googleSheetsOAuth2Api": "google-sheet"}, "typeVersion": 1}, {"name": "Google Sheets3", "type": "n8n-nodes-base.googleSheets", "position": [1650, 450], "parameters": {"range": "A:D", "options": {"valueRenderMode": "FORMATTED_VALUE"}, "sheetId": "1remFwo--5ehUgIU7UUndKldPI0Xm93e1T3DldD9GOg0", "authentication": "oAuth2"}, "credentials": {"googleSheetsOAuth2Api": "google-sheet"}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [650, 450], "parameters": {"values": {"number": [{"name": "ID", "value": "={{Math.floor(Math.random()*1000)}}"}], "string": [{"name": "Name", "value": "John's Place"}, {"name": "Rent", "value": "$1,000"}, {"name": "City", "value": "Berlin"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Set": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}, "Set1": {"main": [[{"node": "Google Sheets2", "type": "main", "index": 0}]]}, "Google Sheets": {"main": [[{"node": "Google Sheets1", "type": "main", "index": 0}]]}, "Google Sheets1": {"main": [[{"node": "Set1", "type": "main", "index": 0}]]}, "Google Sheets2": {"main": [[{"node": "Google Sheets3", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}}