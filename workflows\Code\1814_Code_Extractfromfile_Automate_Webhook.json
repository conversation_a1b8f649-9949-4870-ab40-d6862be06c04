{"id": "bhWsUxipJ9wuTA5K", "meta": {"instanceId": "fd11e31161384d7618b8c5580f01ec2285d2165d3df82195429972f6a3f814eb", "templateCredsSetupCompleted": true}, "name": "n8n workflow deployer", "tags": [], "nodes": [{"id": "8db6d045-5ef8-444a-ae3e-0f0611946008", "name": "Get Existing Workflow Tags", "type": "n8n-nodes-base.httpRequest", "position": [-580, -580], "parameters": {"url": "={{ $json.N8N_Instance_URL }}api/v1/tags", "options": {}, "sendHeaders": true, "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{"name": "accept", "value": "application/json"}]}, "nodeCredentialType": "n8nApi"}, "credentials": {"n8nApi": {"id": "eOE2pATZyQiS1K4C", "name": "n8n account"}}, "retryOnFail": true, "typeVersion": 4.2, "waitBetweenTries": 5000}, {"id": "da4aeef3-05a4-48c9-ae5c-9038f07e3693", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-300, -1040], "parameters": {"color": 3, "width": 1460, "height": 760, "content": "## Setup Instructions\n\n**1.** In Google Drive create a **ToDeploy** folder and a **Deployed** folder\n+ Update \"**Google Drive Trigger -ToDeploy folder**\" to your ToDeploy folder\n+ Update \"**Move JSON file to Deployed folder**\" to you Deployed folder\n\n\n**2.** Create a **n8n API key**:\n+Go to Settings > n8n API\n+Select Create an API key\n+Copy API Key\n\n**3.** In \"**Get Existing Workflow Tags**\" node: \nCreate n8n API Authentication\n**Authentication:** Predefined Credential Type\n**Credential Type:** n8n API\n\nCreate new credential:\n+Paste in API key\n+Baseurl: https://SUB.DOMAINNAME.com/api/v1/\n\n**4.** Add n8n API authentication to: \n+ \"**Create n8n Workflow**\" node\n+ \"**Set Workflow Tag**\" node\n\n\n**5.** Add your N8N instance URL to the **N8N_Instance_URL** variable in \"**Set n8n URL variable**\" node.\n\n**6.** Run **\"Get Workflow Tags\"** node and copy the ID of your chosen tag.\n\n**7.** In \"**Set n8n API URL & Tag ID variables**\" node:\n+ Add the Workflow Tag ID to the **N8N_Instance_Tag** variable\n+ Add your N8N instance URL to the **N8N_Instance_URL** variable\n\n\n**9.** Set workflow to Active\n\n**10.** Add n8n json files to Google Drive folder \n\n"}, "typeVersion": 1}, {"id": "520aa22e-0456-4383-ba6d-fd89fd77f193", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-680, -140], "parameters": {"color": 4, "width": 260, "height": 280, "content": "### Set variables:\n**N8N_Instance_Tag** **N8N_Instance_URL** "}, "typeVersion": 1}, {"id": "2e0794eb-0213-48fd-a974-26301bfdfc8a", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [300, -120], "parameters": {"color": 4, "width": 440, "height": 260, "content": "### Configure n8n API authentication"}, "typeVersion": 1}, {"id": "f77ad2ef-32e3-4d24-b79b-9898152c<PERSON>c", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-1100, -780], "parameters": {"color": 5, "width": 740, "height": 420, "content": "## 1. Get Workflow Tags"}, "typeVersion": 1}, {"id": "cf10c998-44fc-4f1a-8d61-9187a9eae82a", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-1040, -580], "parameters": {}, "typeVersion": 1}, {"id": "206fffc1-d7ee-41eb-b6ad-55be8ae60526", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-880, -700], "parameters": {"color": 4, "width": 220, "height": 280, "content": "### Set variable:\n**N8N_Instance_URL** "}, "typeVersion": 1}, {"id": "2bbe9b3e-c302-497f-a724-e8c51ce673ef", "name": "Extract JSON object from File", "type": "n8n-nodes-base.extractFromFile", "position": [-80, -40], "parameters": {"options": {}, "operation": "fromJson"}, "typeVersion": 1}, {"id": "2f0acb18-86c4-4f94-8f76-b72174809643", "name": "Clean JSON file ready for import", "type": "n8n-nodes-base.code", "position": [140, -40], "parameters": {"mode": "runOnceForEachItem", "jsCode": "const fullWorkflow = $json.data || $json;\n\n// Build settings with only allowed fields\nconst cleanSettings = {};\nif (fullWorkflow.settings?.executionOrder) {\n  cleanSettings.executionOrder = fullWorkflow.settings.executionOrder;\n}\nif (fullWorkflow.settings?.timezone) {\n  cleanSettings.timezone = fullWorkflow.settings.timezone;\n}\n\n// Construct clean workflow object\nconst cleanWorkflow = {\n  name: fullWorkflow.name,\n  nodes: fullWorkflow.nodes,\n  connections: fullWorkflow.connections,\n  settings: cleanSettings,\n};\n\nreturn { json: cleanWorkflow };\n"}, "typeVersion": 2}, {"id": "f0428a03-2194-4390-b14b-5149ea3a220b", "name": "Set n8n API URL & Tag ID variables", "type": "n8n-nodes-base.set", "position": [-600, -40], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "41afa23f-bacf-4c2b-9630-68483acc9fe6", "name": "N8N_Instance_URL", "type": "string", "value": "https://SUB.DOMAINNAME.com/"}, {"id": "c27f2d9d-ee1f-4ada-90cc-20177017b342", "name": "N8N_Instance_Tag", "type": "string", "value": "mIzqUB1qBwewiiX3"}]}}, "typeVersion": 3.4}, {"id": "9f59cba9-9452-4e05-9d95-3e405ec195cf", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-980, -140], "parameters": {"color": 4, "width": 260, "height": 280, "content": "### Change Google Drive Folder"}, "typeVersion": 1}, {"id": "87ce4868-407c-461f-92e2-6b3bf1dd616e", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-640, -760], "parameters": {"color": 4, "height": 340, "content": "### Configure n8n API authentication.\n\n### Tag ID\nCopy your chosen Tag ID to **N8N_Instance_Tag** "}, "typeVersion": 1}, {"id": "b1c3f693-a587-4928-a90a-8288eb84a879", "name": "Create n8n Workflow", "type": "n8n-nodes-base.httpRequest", "onError": "continueErrorOutput", "position": [360, -40], "parameters": {"url": "={{ $('Set n8n API URL & Tag ID variables').item.json.N8N_Instance_URL }}api/v1/workflows", "body": "={{ $json }}", "method": "POST", "options": {}, "sendBody": true, "contentType": "raw", "sendHeaders": true, "authentication": "predefinedCredentialType", "rawContentType": "application/json", "headerParameters": {"parameters": [{"name": "accept", "value": "application/json"}, {"name": "Content-Type", "value": "application/json"}]}, "nodeCredentialType": "n8nApi"}, "credentials": {"n8nApi": {"id": "eOE2pATZyQiS1K4C", "name": "n8n account"}}, "typeVersion": 4.2}, {"id": "70ff3b11-3664-4fec-a220-72696a6083c5", "name": "Set Workflow Tag", "type": "n8n-nodes-base.httpRequest", "onError": "continueRegularOutput", "position": [600, -40], "parameters": {"url": "={{ $('Set n8n API URL & Tag ID variables').item.json.N8N_Instance_URL }}api/v1/workflows/{{ $json.id }}/tags", "method": "PUT", "options": {}, "jsonBody": "=[\n  {\n    \"id\": \"{{ $('Set n8n API URL & Tag ID variables').item.json.N8N_Instance_Tag }}\"\n  }\n]", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "n8nApi"}, "credentials": {"n8nApi": {"id": "eOE2pATZyQiS1K4C", "name": "n8n account"}}, "retryOnFail": true, "typeVersion": 4.2, "waitBetweenTries": 5000}, {"id": "********-7027-476b-8eff-9d9a16385cce", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [800, -120], "parameters": {"color": 4, "width": 260, "height": 260, "content": "### Change Google Drive Deployed Folder"}, "typeVersion": 1}, {"id": "e8a9bbd8-a41d-4b82-931c-5570651d8583", "name": "Capture Name If Fails To Create Workflow", "type": "n8n-nodes-base.code", "position": [600, 160], "parameters": {"jsCode": "return [{\n  json: {\n    workflowName:   $json.name,\n    errorMessage:   $json.error.message,\n  }\n}];\n"}, "typeVersion": 2}, {"id": "0fee0939-f3bd-4fd1-b444-40509f4b0f50", "name": "Move JSON file to Deployed folder", "type": "n8n-nodes-base.googleDrive", "position": [880, -40], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $('Google Drive Trigger -ToDeploy folder').item.json.id }}"}, "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "mode": "list", "value": "1nQb17Xf7ZTF75E-aettkFtBVKI_nOrsW", "cachedResultUrl": "https://drive.google.com/drive/folders/1nQb17Xf7ZTF75E-aettkFtBVKI_nOrsW", "cachedResultName": "Deployed"}, "operation": "move"}, "credentials": {"googleDriveOAuth2Api": {"id": "SfLfcExz8PihKGNB", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "59567f07-4d69-4d30-a5ef-934198ff101d", "name": "Download n8n JSON File", "type": "n8n-nodes-base.googleDrive", "position": [-320, -40], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $('Google Drive Trigger -ToDeploy folder').item.json.id }}"}, "options": {"binaryPropertyName": "data"}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "SfLfcExz8PihKGNB", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "a5837765-9787-43a5-bbfe-44e5f3728aee", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [-1100, -260], "parameters": {"color": 5, "width": 2260, "height": 620, "content": "## 2. Import JSON Workflow Into n8n Instance"}, "typeVersion": 1}, {"id": "bcb77b34-36c8-4839-b3e2-72f8e60871ba", "name": "Set n8n URL variable", "type": "n8n-nodes-base.set", "position": [-820, -580], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "41afa23f-bacf-4c2b-9630-68483acc9fe6", "name": "N8N_Instance_URL", "type": "string", "value": "https://SUB.DOMAINNAME.com/"}]}}, "typeVersion": 3.4}, {"id": "7281ab81-d1e8-4a78-8e2f-e1049633d6e6", "name": "Google Drive Trigger -ToDeploy folder", "type": "n8n-nodes-base.googleDriveTrigger", "position": [-880, -40], "parameters": {"event": "fileCreated", "options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "mode": "list", "value": "1EPGHT5fBn0Hx_EVDixJiJMJgRbNNdB0I", "cachedResultUrl": "https://drive.google.com/drive/folders/1EPGHT5fBn0Hx_EVDixJiJMJgRbNNdB0I", "cachedResultName": "toDeploy"}}, "credentials": {"googleDriveOAuth2Api": {"id": "SfLfcExz8PihKGNB", "name": "Google Drive account"}}, "typeVersion": 1}], "active": true, "settings": {"executionOrder": "v1"}, "versionId": "77325a25-51f0-441a-8750-fe6d1d5a266f", "connections": {"Set Workflow Tag": {"main": [[{"node": "Move JSON file to Deployed folder", "type": "main", "index": 0}]]}, "Create n8n Workflow": {"main": [[{"node": "Set Workflow Tag", "type": "main", "index": 0}], [{"node": "Capture Name If Fails To Create Workflow", "type": "main", "index": 0}]]}, "Set n8n URL variable": {"main": [[{"node": "Get Existing Workflow Tags", "type": "main", "index": 0}]]}, "Download n8n JSON File": {"main": [[{"node": "Extract JSON object from File", "type": "main", "index": 0}]]}, "Extract JSON object from File": {"main": [[{"node": "Clean JSON file ready for import", "type": "main", "index": 0}]]}, "Clean JSON file ready for import": {"main": [[{"node": "Create n8n Workflow", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set n8n URL variable", "type": "main", "index": 0}]]}, "Set n8n API URL & Tag ID variables": {"main": [[{"node": "Download n8n JSON File", "type": "main", "index": 0}]]}, "Google Drive Trigger -ToDeploy folder": {"main": [[{"node": "Set n8n API URL & Tag ID variables", "type": "main", "index": 0}]]}}}