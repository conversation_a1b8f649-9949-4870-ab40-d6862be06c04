{"id": "43gMd18arOcxqDcC", "meta": {"instanceId": "fb924c73af8f703905bc09c9ee8076f48c17b596ed05b18c0ff86915ef8a7c4a", "templateCredsSetupCompleted": true}, "name": "LLM Chaining examples", "tags": [], "nodes": [{"id": "35e53ce7-06b4-47ca-b7f3-b147bd059fcf", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [200, 520], "parameters": {}, "typeVersion": 1}, {"id": "aeef734e-1c3b-4a91-93ae-2ae9c50951b8", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [400, 520], "parameters": {"url": "https://blog.n8n.io/", "options": {}}, "typeVersion": 4.2}, {"id": "7f6b95eb-df8c-4f0f-ba69-6b298d624ccd", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.markdown", "position": [600, 520], "parameters": {"html": "={{ $json.data }}", "options": {}, "destinationKey": "markdown"}, "typeVersion": 1}, {"id": "994dbe06-4c25-4fb3-a8f3-566eb5b66c6d", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [160, 340], "parameters": {"color": 4, "width": 700, "height": 360, "content": "# Connect to one of the blue sections -->\n## This can be anything:\n- Chat input\n- Trigger from external system\n- CRON-scheduled event"}, "typeVersion": 1}, {"id": "8ba3039d-dabf-43b7-ab35-117332f65ced", "name": "Anthropic <PERSON>", "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "position": [1460, -20], "parameters": {"model": {"__rl": true, "mode": "list", "value": "claude-3-7-sonnet-********", "cachedResultName": "Claude 3.7 Sonnet"}, "options": {"temperature": 0.5}}, "credentials": {"anthropicApi": {"id": "cJno7gKlYez56WtP", "name": "Anthropic account"}}, "typeVersion": 1.3}, {"id": "7e1da020-e01d-410c-aa7f-a19d6e1c368d", "name": "Anthropic Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "position": [1820, -20], "parameters": {"model": {"__rl": true, "mode": "list", "value": "claude-3-7-sonnet-********", "cachedResultName": "Claude 3.7 Sonnet"}, "options": {"temperature": 0.5}}, "credentials": {"anthropicApi": {"id": "cJno7gKlYez56WtP", "name": "Anthropic account"}}, "typeVersion": 1.3}, {"id": "620503cb-2d51-4102-8975-75255cf15b1b", "name": "Anthropic Chat Model2", "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "position": [2180, -20], "parameters": {"model": {"__rl": true, "mode": "list", "value": "claude-3-7-sonnet-********", "cachedResultName": "Claude 3.7 Sonnet"}, "options": {"temperature": 0.5}}, "credentials": {"anthropicApi": {"id": "cJno7gKlYez56WtP", "name": "Anthropic account"}}, "typeVersion": 1.3}, {"id": "5f0d11ce-c1ea-4c36-8b2d-d3f70b19f0ba", "name": "Anthropic Chat Model3", "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "position": [2540, -20], "parameters": {"model": {"__rl": true, "mode": "list", "value": "claude-3-7-sonnet-********", "cachedResultName": "Claude 3.7 Sonnet"}, "options": {"temperature": 0.5}}, "credentials": {"anthropicApi": {"id": "cJno7gKlYez56WtP", "name": "Anthropic account"}}, "typeVersion": 1.3}, {"id": "f973d01e-fad7-4143-8379-54438f5412cb", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [2440, 360], "parameters": {"mode": "combine", "options": {"includeUnpaired": true}, "combineBy": "combineByPosition"}, "typeVersion": 3.1}, {"id": "c7e58b90-bc96-421c-88f2-4e9f95f87248", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [2680, 780], "parameters": {"sessionKey": "fixed_session", "sessionIdType": "customKey", "contextWindowLength": 10}, "typeVersion": 1.3}, {"id": "0e606f7c-2cdb-4e34-8c0b-2303996077fb", "name": "Clean memory", "type": "@n8n/n8n-nodes-langchain.memoryManager", "position": [1500, 480], "parameters": {"mode": "delete", "deleteMode": "all"}, "typeVersion": 1.1}, {"id": "af0fb574-9964-4f7d-8348-a2cf614b8562", "name": "Initial prompts", "type": "n8n-nodes-base.set", "position": [1880, 480], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "84a50c9c-2265-4dd6-a774-efc852615862", "name": "system_prompt", "type": "string", "value": "You are a helpful assistant"}, {"id": "559f19f7-042c-465e-b85f-ab52cfbab04a", "name": "step1", "type": "string", "value": "What is on this page?"}, {"id": "6791cd09-c5f7-48c8-b753-8d383db6863f", "name": "step2", "type": "string", "value": "List all authors on this page"}, {"id": "1f92ac04-e5dd-4161-afde-14562aea454c", "name": "step3", "type": "string", "value": "List all posts on this page"}, {"id": "ad8ee0b0-fa7d-4f4a-85a8-82d0d0dc0a40", "name": "step4", "type": "string", "value": "Make a bold funny joke based on the content on this page"}]}}, "typeVersion": 3.4}, {"id": "6743e44a-cc76-4e73-b4f3-ba7c65d179d3", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [2240, 480], "parameters": {"include": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options": {}, "fieldToSplitOut": "data", "fieldsToInclude": "system_prompt"}, "typeVersion": 1}, {"id": "caddd26c-ee84-455f-8ee6-aecf21536930", "name": "Reshape", "type": "n8n-nodes-base.set", "position": [2060, 480], "parameters": {"mode": "raw", "include": "selected", "options": {}, "jsonOutput": "={ \"data\" : {{ Object.entries($json).filter(([key]) => key !== \"system_prompt\").map(([key, value]) => ({ step: key, instruction: value })) }}\n}", "includeFields": "system_prompt", "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "bd244988-d074-42f3-af42-960e5aa1d35d", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1840, 400], "parameters": {"width": 540, "height": 240, "content": "# An array of prompts here"}, "typeVersion": 1}, {"id": "7e9e5287-8d4e-43a9-b8cf-ae26a177bfbb", "name": "Anthropic Chat Model4", "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "position": [2600, 600], "parameters": {"model": {"__rl": true, "mode": "list", "value": "claude-3-7-sonnet-********", "cachedResultName": "Claude 3.7 Sonnet"}, "options": {"thinking": false, "temperature": 0.5}}, "credentials": {"anthropicApi": {"id": "cJno7gKlYez56WtP", "name": "Anthropic account"}}, "typeVersion": 1.3}, {"id": "47816a45-b906-47ef-9510-c63867bfc8b7", "name": "Merge2", "type": "n8n-nodes-base.merge", "position": [1860, 1120], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineAll"}, "typeVersion": 3}, {"id": "e63b89a1-c2ca-4ed2-ae50-e3a7b429609c", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [2040, 1020], "parameters": {"width": 320, "height": 520, "content": "## Make sure URL matches\n### ⚠️ Cloud users!\nReplace `{{ $env.WEBHOOK_URL }}` \nwith your n8n instance URL"}, "typeVersion": 1}, {"id": "7b99df1a-bf6c-4cf1-b58a-************", "name": "Basic LLM Chain4", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [2440, 1300], "parameters": {"text": "={{ $json.body.userprompt }}\n\nHere's page data:\n~~~~\n{{ $json.body.markdown }}\n~~~~", "promptType": "define"}, "typeVersion": 1.5}, {"id": "6f6e0667-5164-4b65-a796-1d2112c7c072", "name": "Split Out1", "type": "n8n-nodes-base.splitOut", "position": [1680, 1340], "parameters": {"options": {}, "fieldToSplitOut": "userprompt"}, "typeVersion": 1}, {"id": "9dfd2145-2427-4131-92d2-99aca620217f", "name": "Anthropic Chat Model5", "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "position": [2420, 1460], "parameters": {"model": {"__rl": true, "mode": "list", "value": "claude-3-7-sonnet-********", "cachedResultName": "Claude 3.7 Sonnet"}, "options": {"thinking": false, "temperature": 0.5}}, "credentials": {"anthropicApi": {"id": "cJno7gKlYez56WtP", "name": "Anthropic account"}}, "typeVersion": 1.3}, {"id": "616fc635-107d-4929-b9d6-4ccd34e42909", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [2140, 1400], "webhookId": "58d2b899-e09c-45bf-b59b-961a5d7a2470", "parameters": {"path": "58d2b899-e09c-45bf-b59b-961a5d7a2470", "options": {}, "httpMethod": "POST", "responseMode": "lastNode"}, "typeVersion": 2}, {"id": "c863252b-f8b6-4704-be4e-a69d3005a85a", "name": "CONNECT ME", "type": "n8n-nodes-base.noOp", "position": [1240, -220], "parameters": {}, "typeVersion": 1}, {"id": "90ab4402-cbea-4441-9097-558ec72e5d38", "name": "CONNECT ME1", "type": "n8n-nodes-base.noOp", "position": [1280, 340], "parameters": {}, "typeVersion": 1}, {"id": "1c04650f-8043-496f-aeab-866e85548f9d", "name": "CONNECT ME2", "type": "n8n-nodes-base.noOp", "position": [1280, 1100], "parameters": {}, "typeVersion": 1}, {"id": "4097f12d-eba7-477a-9152-da5eb8c9aa03", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [960, -300], "parameters": {"color": 5, "width": 1980, "height": 440, "content": "# 1 - Naive Chaining\n### PROs:\n- Easy to setup\n- Beginner-friendly\n\n### CONs\n- Not scalable\n- Hard to maintain long chains\n- SLOOOW!"}, "typeVersion": 1}, {"id": "ce806bc6-a57e-47da-bbba-4698c3956022", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [960, 240], "parameters": {"color": 5, "width": 2160, "height": 660, "content": "# 2 - Iterative Agent Processing\n\n### PROs:\n- Scalable\n- All inputs & outputs in a single node\n- Supports Agent memory\n\n### CONs\n- Still Slow!"}, "typeVersion": 1}, {"id": "49c4507f-de1e-422b-8058-db82668614d3", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [960, 1000], "parameters": {"color": 5, "width": 1880, "height": 600, "content": "# 3 - Parallel Processing\n\n### PROs:\n- Scalable\n- All inputs & outputs in a single place\n- FAST!\n\n### CONs\n- Independent requests\n  (no Agent memory)"}, "typeVersion": 1}, {"id": "c30b8132-9291-4855-89ec-6a98bcee8247", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [1420, 1260], "parameters": {"width": 400, "height": 240, "content": "# Array of prompts here"}, "typeVersion": 1}, {"id": "4c1b5816-7393-47f6-8a88-008d8deea119", "name": "Initial prompts1", "type": "n8n-nodes-base.set", "position": [1460, 1340], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "ed7f1cc6-99d3-481c-b5fb-d9900d6ee0f6", "name": "userprompt", "type": "array", "value": "=[\n\"What is on this page?\",\n\"List all authors on this page\",\n\"List all posts on this page\",\n\"Make a bold funny joke based on the content on this page\"\n]"}]}}, "typeVersion": 3.4}, {"id": "8248a20f-1f90-42b0-8167-7ddcc90242a2", "name": "LLM Chain - Step 1", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1500, -220], "parameters": {"text": "={{ $('Markdown').first().json.markdown }}", "messages": {"messageValues": [{"message": "What is on this page?"}]}, "promptType": "define"}, "typeVersion": 1.6}, {"id": "3788b23b-ccdc-4326-8ce0-1e57934d23bd", "name": "LLM Chain - Step 2", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1860, -220], "parameters": {"text": "={{ $('Markdown').first().json.markdown }}", "messages": {"messageValues": [{"message": "List all authors on this page"}]}, "promptType": "define"}, "typeVersion": 1.6}, {"id": "89e69a39-bf13-4599-8ddc-a01c4590fb9c", "name": "LLM Chain - Step 3", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [2220, -220], "parameters": {"text": "={{ $('Markdown').first().json.markdown }}", "messages": {"messageValues": [{"message": "List all posts on this page"}]}, "promptType": "define"}, "typeVersion": 1.6}, {"id": "7e395991-9404-490e-8946-0da8f81e7243", "name": "LLM Chain - Step 4", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [2580, -220], "parameters": {"text": "={{ $('Markdown').first().json.markdown }}", "messages": {"messageValues": [{"message": "Make a bold funny joke based on the content on this page"}]}, "promptType": "define"}, "typeVersion": 1.6}, {"id": "efb8d836-8a4a-4a70-baed-4a9b77461aca", "name": "All LLM steps here - sequentially", "type": "@n8n/n8n-nodes-langchain.agent", "position": [2640, 440], "parameters": {"text": "={{ $json.markdown || \"\" }}\n{{ `Your task: ${$json.data.step}. ${$json.data.instruction}` }}", "options": {"systemMessage": "={{ $json.system_prompt }}"}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "926b1705-a24c-4659-bf61-8ed97ade7290", "name": "LLM steps - parallel", "type": "n8n-nodes-base.httpRequest", "position": [2140, 1240], "parameters": {"url": "={{ $env.WEBHOOK_URL }}webhook/58d2b899-e09c-45bf-b59b-961a5d7a2470", "method": "POST", "options": {}, "jsonBody": "={{ $json }}", "sendBody": true, "specifyBody": "json"}, "typeVersion": 4.2}, {"id": "7748574b-1abd-4697-9644-db8bb79fb08d", "name": "Merge output with initial prompts", "type": "n8n-nodes-base.merge", "position": [2440, 1140], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3.1}, {"id": "b207d83b-ecda-4a9f-af78-cfbb2253c119", "name": "Merge output with initial prompts1", "type": "n8n-nodes-base.merge", "position": [3000, 380], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3.1}], "active": true, "pinData": {}, "settings": {"callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1", "saveDataSuccessExecution": "all"}, "versionId": "7b1849db-1c4c-4943-89b1-184926649776", "connections": {"Merge": {"main": [[{"node": "All LLM steps here - sequentially", "type": "main", "index": 0}, {"node": "Merge output with initial prompts1", "type": "main", "index": 0}]]}, "Merge2": {"main": [[{"node": "LLM steps - parallel", "type": "main", "index": 0}, {"node": "Merge output with initial prompts", "type": "main", "index": 0}]]}, "Reshape": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Basic LLM Chain4", "type": "main", "index": 0}]]}, "Markdown": {"main": [[]]}, "Split Out": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "CONNECT ME": {"main": [[{"node": "LLM Chain - Step 1", "type": "main", "index": 0}]]}, "Split Out1": {"main": [[{"node": "Merge2", "type": "main", "index": 1}]]}, "CONNECT ME1": {"main": [[{"node": "Clean memory", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "CONNECT ME2": {"main": [[{"node": "Initial prompts1", "type": "main", "index": 0}, {"node": "Merge2", "type": "main", "index": 0}]]}, "Clean memory": {"main": [[{"node": "Initial prompts", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "All LLM steps here - sequentially", "type": "ai_memory", "index": 0}, {"node": "Clean memory", "type": "ai_memory", "index": 0}]]}, "Initial prompts": {"main": [[{"node": "Reshape", "type": "main", "index": 0}]]}, "Initial prompts1": {"main": [[{"node": "Split Out1", "type": "main", "index": 0}]]}, "LLM Chain - Step 1": {"main": [[{"node": "LLM Chain - Step 2", "type": "main", "index": 0}]]}, "LLM Chain - Step 2": {"main": [[{"node": "LLM Chain - Step 3", "type": "main", "index": 0}]]}, "LLM Chain - Step 3": {"main": [[{"node": "LLM Chain - Step 4", "type": "main", "index": 0}]]}, "Anthropic Chat Model": {"ai_languageModel": [[{"node": "LLM Chain - Step 1", "type": "ai_languageModel", "index": 0}]]}, "LLM steps - parallel": {"main": [[{"node": "Merge output with initial prompts", "type": "main", "index": 1}]]}, "Anthropic Chat Model1": {"ai_languageModel": [[{"node": "LLM Chain - Step 2", "type": "ai_languageModel", "index": 0}]]}, "Anthropic Chat Model2": {"ai_languageModel": [[{"node": "LLM Chain - Step 3", "type": "ai_languageModel", "index": 0}]]}, "Anthropic Chat Model3": {"ai_languageModel": [[{"node": "LLM Chain - Step 4", "type": "ai_languageModel", "index": 0}]]}, "Anthropic Chat Model4": {"ai_languageModel": [[{"node": "All LLM steps here - sequentially", "type": "ai_languageModel", "index": 0}]]}, "Anthropic Chat Model5": {"ai_languageModel": [[{"node": "Basic LLM Chain4", "type": "ai_languageModel", "index": 0}]]}, "All LLM steps here - sequentially": {"main": [[{"node": "Merge output with initial prompts1", "type": "main", "index": 1}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}}}