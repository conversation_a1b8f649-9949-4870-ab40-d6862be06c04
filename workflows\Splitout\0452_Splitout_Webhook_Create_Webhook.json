{"meta": {"instanceId": "8eadf351d49a11e77d3a57adf374670f06c5294af8b1b7c86a1123340397e728"}, "nodes": [{"id": "551a3a1f-07ad-48aa-bc9a-18f39c883929", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [940, 180], "parameters": {"options": {}, "fieldToSplitOut": "toplevel.CompleteSuggestion"}, "typeVersion": 1}, {"id": "f451dc0d-a78d-4ba6-adcf-c1180502a904", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "position": [1260, 180], "parameters": {"options": {}, "fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "Keywords"}]}}, "typeVersion": 1}, {"id": "ccad69b0-7f88-490e-bfbd-50ef702f48ce", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [350.13991769547397, 134.716049382716], "parameters": {"color": 4, "width": 1323.884773662551, "height": 224.79012345679018, "content": "* Generating keywords for your SEO"}, "typeVersion": 1}, {"id": "9ed26e36-e05a-416e-a517-3f5d07718256", "name": "Receive Keyword", "type": "n8n-nodes-base.webhook", "position": [400, 180], "webhookId": "76a63718-b3cb-4141-bc55-efa614d13f1d", "parameters": {"path": "76a63718-b3cb-4141-bc55-efa614d13f1d", "options": {}, "responseMode": "lastNode"}, "typeVersion": 1.1}, {"id": "51aa0811-7f31-4476-9460-4eacad81e469", "name": "Autogenerate Keywords", "type": "n8n-nodes-base.httpRequest", "position": [600, 180], "parameters": {"url": "=https://google.com/complete/search?output=toolbar&gl=US&q={{ $json.query.q }}", "options": {}}, "typeVersion": 4.1}, {"id": "f3bd360c-bf72-4e5f-92ec-ca08c8e4daed", "name": "Format Keywords", "type": "n8n-nodes-base.xml", "position": [760, 180], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "17bef508-47e1-482b-8dc1-aeb1f6faca63", "name": "Clean Keywords", "type": "n8n-nodes-base.set", "position": [1100, 180], "parameters": {"options": {"ignoreConversionErrors": true}, "assignments": {"assignments": [{"id": "fb95058f-0c20-4249-8a45-7b935fde1874", "name": "Keywords", "type": "array", "value": "={{ $json.suggestion.data }}"}]}}, "typeVersion": 3.3}, {"id": "81e3ced0-d3b7-4019-a6a7-5e940ad33df1", "name": "return Keywords", "type": "n8n-nodes-base.respondToWebhook", "position": [1440, 180], "parameters": {"options": {}, "respondWith": "allIncomingItems"}, "typeVersion": 1}, {"id": "fafc57a6-64e1-4463-bbf0-c9dccd880345", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [360, 380], "parameters": {"width": 767.7695473251028, "content": "* If you are using this one, just copy the this webhook url http://localhost:5678/webhook/76a63718-b3cb-4141-bc55-efa614d13f1d?q=keyword%20research\n* All you need is to change the keyword to e your desired keyword and you will be good to go\n\n* You can use the keyword with a space and the results will be the same"}, "typeVersion": 1}], "pinData": {}, "connections": {"Aggregate": {"main": [[{"node": "return Keywords", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Clean Keywords", "type": "main", "index": 0}]]}, "Clean Keywords": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Format Keywords": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Receive Keyword": {"main": [[{"node": "Autogenerate Keywords", "type": "main", "index": 0}]]}, "Autogenerate Keywords": {"main": [[{"node": "Format Keywords", "type": "main", "index": 0}]]}}}