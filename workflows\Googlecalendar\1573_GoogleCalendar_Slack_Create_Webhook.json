{"id": "O2R3U22TB968fWUo", "meta": {"instanceId": "cb484ba7b742928a2048bf8829668bed5b5ad9787579adea888f05980292a4a7"}, "name": "Generate google meet links in slack", "tags": [{"id": "GkyPPgldsTmLDY6O", "name": "createdBy:JC", "createdAt": "2024-02-29T21:51:58.448Z", "updatedAt": "2024-02-29T21:51:58.448Z"}], "nodes": [{"id": "5577aaf6-f682-49c3-9d21-f819151f77c5", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [300, 480], "webhookId": "f442a7bb-451e-4371-8b7a-614caa0e04dd", "parameters": {"path": "slack-meet-trigger", "options": {}, "httpMethod": "POST", "responseData": "noData", "responseMode": "lastNode"}, "typeVersion": 1.1}, {"id": "018c32c7-c3eb-4679-8064-ab92bb62cac5", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [140, 142], "parameters": {"color": 6, "width": 463.09809221779403, "height": 482.56534054190786, "content": "### 1. Setup: Add a Slack App\n**a.** Visit https://api.slack.com/apps, click on `New App` and choose a name and workspace.\n**b.** Click on `OAuth & Permissions` and scroll down to Scopes -> Bo<PERSON> token Scopes\n**c.** Add the `chat:write` scope & `chat:write.public`\n**d.** Navigate to `Slash Commands` and click `Create New Command`\n**e.** Use `/meet` as the command\n**f.** Copy the production URL from the **Webhook** node into `Request URL` within your slash command\n**g.** Add relevant description and usage hint\n**h.** Go to `Install app` and click install\n**i.** Don't worry about app distribution, that's only if you're trying to publish an app on the slack store"}, "typeVersion": 1}, {"id": "3bfa07d4-ef3e-4ec4-91a2-ca94e2346299", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [640, 240], "parameters": {"color": 6, "width": 291.779972644588, "height": 192.66150688057675, "content": "### 2. Setup: Google auth & calendar\n**a.** Visit [the docs](https://docs.n8n.io/integrations/builtin/credentials/google/oauth-single-service/) and follow the steps to setup Google auth credential\n**b.** Choose the calendar you wish to create google meet links from\n\n\n\n👇"}, "typeVersion": 1}, {"id": "aab60499-7123-43c0-8f99-d0eade0f5672", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [960, 238], "parameters": {"color": 6, "width": 292.*************, "height": 192.**************, "content": "### 3. Setup: Configure slack node authentication and your message\n**a.** Connect your slack account\n**b.** Configure your message text. Be sure to include the hangoutLink expression to output a meeting link\n\n👇"}, "typeVersion": 1}, {"id": "a15fc232-ec8e-4dfb-add7-2a3c27c5a232", "name": "Create event with google meet link", "type": "n8n-nodes-base.googleCalendar", "position": [740, 480], "parameters": {"end": "={{ $now.plus({minutes: 15}) }}", "start": "={{ $now }}", "calendar": {"__rl": true, "mode": "list", "value": ""}, "additionalFields": {"conferenceDataUi": {"conferenceDataValues": {"conferenceSolution": "hangouts<PERSON><PERSON><PERSON>"}}}}, "typeVersion": 1}, {"id": "57c2d5b8-f5d7-4db1-9e13-48265d174679", "name": "Send msg with Google meet link", "type": "n8n-nodes-base.slack", "position": [1060, 480], "parameters": {"text": "=Join me here: {{ $('Create event with google meet link').item.json.hangoutLink }}", "select": "channel", "channelId": {"__rl": true, "mode": "id", "value": "={{ $('Webhook').item.json.body.channel_id }}"}, "otherOptions": {"unfurl_links": false, "includeLinkToWorkflow": false}}, "typeVersion": 2.1}, {"id": "898b9681-c532-490e-aea2-a4f693b52f35", "name": "Delete temporary calendar event", "type": "n8n-nodes-base.googleCalendar", "position": [1400, 480], "parameters": {"eventId": "={{ $('Create event with google meet link').item.json[\"id\"] }}", "options": {}, "calendar": {"__rl": true, "mode": "list", "value": ""}, "operation": "delete"}, "typeVersion": 1}, {"id": "ec70003a-6dea-4c1b-a16e-e64a206aba16", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [140, -20], "parameters": {"color": 4, "width": 459.*************, "height": 146.*************, "content": "## Generate google meet links with a slack command \nSpin up instant google meet links directly from slack and send to all channel participants\n\n"}, "typeVersion": 1}, {"id": "eee48232-8477-4bfb-8164-bfaf66062071", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1280, 240], "parameters": {"color": 6, "width": 292.*************, "height": 192.**************, "content": "### 3. Setup: Select google calendar account\n**a.** Select the same calendar you're using to create the initial event\n\n\n\n\n👇"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "09457e4b-ccba-497f-b046-3529edc7b332", "connections": {"Webhook": {"main": [[{"node": "Create event with google meet link", "type": "main", "index": 0}]]}, "Send msg with Google meet link": {"main": [[{"node": "Delete temporary calendar event", "type": "main", "index": 0}]]}, "Create event with google meet link": {"main": [[{"node": "Send msg with Google meet link", "type": "main", "index": 0}]]}}}