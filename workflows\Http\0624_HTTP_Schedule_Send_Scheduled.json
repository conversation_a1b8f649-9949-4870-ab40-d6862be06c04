{"nodes": [{"id": "e4929773-39f9-4b8a-b462-235c37514479", "name": "Get Elastic Alert", "type": "n8n-nodes-base.httpRequest", "position": [620, 440], "parameters": {"url": "https://your-prism-elastic-api-endpoint.com/alerts", "options": {}}, "typeVersion": 2}, {"id": "973a8254-5ec0-4ea0-95b5-7e6a0f0625ab", "name": "Send Email Notification", "type": "n8n-nodes-base.httpRequest", "position": [1440, 220], "parameters": {"url": "https://graph.microsoft.com/v1.0/me/sendMail", "options": {"bodyContentType": "json"}, "requestMethod": "POST", "authentication": "oAuth2", "jsonParameters": true, "bodyParametersJson": "={\n  \"message\": {\n    \"subject\": \"PRISM Elastic Alert: {{$json[\"alert_name\"]}}\",\n    \"body\": {\n      \"contentType\": \"HTML\",\n      \"content\": \"Hello,<br><br>An alert has been triggered:<br><strong>Alert Name:</strong> {{$json[\"alert_name\"]}}<br><strong>Severity:</strong> {{$json[\"severity\"]}}<br><strong>Timestamp:</strong> {{$json[\"timestamp\"]}}<br><br>Details:<br>{{$json[\"alert_message\"]}}<br><br>Regards,<br>PRISM Alert System\"\n    },\n    \"toRecipients\": [\n      {\n        \"emailAddress\": {\n          \"address\": \"<EMAIL>\"\n        }\n      }\n    ]\n  },\n  \"saveToSentItems\": \"true\"\n}"}, "typeVersion": 2}, {"id": "f7f4feee-6854-4997-ae15-870cab4abdbb", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [380, 440], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.2}, {"id": "b8578c55-a052-43f2-9d6a-24d8084dae8a", "name": "Response is not empty", "type": "n8n-nodes-base.if", "position": [840, 440], "parameters": {"options": {}}, "typeVersion": 2.1}, {"id": "664216e6-c212-4f4b-8b09-60675c4fcd91", "name": "No Operation, do nothing", "type": "n8n-nodes-base.noOp", "position": [1100, 680], "parameters": {}, "typeVersion": 1}, {"id": "bcead903-56ed-4ae8-bff9-cec274b2fe71", "name": "Loop Over Each Alert Items", "type": "n8n-nodes-base.splitInBatches", "position": [1100, 200], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "a5e55903-a245-4d70-88e7-14c1f18cde25", "name": "No Operation, end of loop", "type": "n8n-nodes-base.noOp", "position": [1440, 0], "parameters": {}, "typeVersion": 1}], "pinData": {}, "connections": {"Schedule Trigger": {"main": [[{"node": "Get PRISM Elastic Alert", "type": "main", "index": 0}]]}, "Response is not empty": {"main": [[{"node": "Loop Over Each Alert Items", "type": "main", "index": 0}], [{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "Get PRISM Elastic Alert": {"main": [[{"node": "Response is not empty", "type": "main", "index": 0}]]}, "Send Email Notification": {"main": [[{"node": "Loop Over Each Alert Items", "type": "main", "index": 0}]]}, "Loop Over Each Alert Items": {"main": [[{"node": "No Operation, end of loop", "type": "main", "index": 0}], [{"node": "Send Email Notification", "type": "main", "index": 0}]]}}}