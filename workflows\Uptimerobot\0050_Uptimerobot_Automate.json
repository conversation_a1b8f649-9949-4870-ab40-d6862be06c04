{"nodes": [{"name": "UptimeRobot2", "type": "n8n-nodes-base.uptimeRobot", "position": [890, 320], "parameters": {"id": "={{$json[\"id\"]}}", "resource": "monitor", "operation": "get"}, "credentials": {"uptimeRobotApi": "UptimeRobot API Credentials"}, "typeVersion": 1}, {"name": "UptimeRobot", "type": "n8n-nodes-base.uptimeRobot", "position": [490, 320], "parameters": {"url": "https://n8n.io", "type": 1, "resource": "monitor", "operation": "create", "friendlyName": "n8n"}, "credentials": {"uptimeRobotApi": "UptimeRobot API Credentials"}, "typeVersion": 1}, {"name": "UptimeRobot1", "type": "n8n-nodes-base.uptimeRobot", "position": [690, 320], "parameters": {"id": "={{$json[\"id\"]}}", "resource": "monitor", "operation": "update", "updateFields": {"friendly_name": "n8n website"}}, "credentials": {"uptimeRobotApi": "UptimeRobot API Credentials"}, "typeVersion": 1}], "connections": {"UptimeRobot": {"main": [[{"node": "UptimeRobot1", "type": "main", "index": 0}]]}, "UptimeRobot1": {"main": [[{"node": "UptimeRobot2", "type": "main", "index": 0}]]}}}