{"id": "Xfz2YRxH6qFfpqHw", "meta": {"instanceId": "2b69b24ad1a51b447e1a0d6f8c70b16aca715ccfaf123eb531f92865766fce1c", "templateId": "seo_blog_generator_gpt4o_perplexity_telegram", "templateCredsSetupCompleted": true}, "name": "SEO Blog Generator with GPT-4o, Perplexity, and Telegram Integration", "tags": [], "nodes": [{"id": "17ab0b24-b1eb-4e4e-a249-9889c9876fe4", "name": "Note4", "type": "n8n-nodes-base.stickyNote", "position": [880, 180], "parameters": {"color": 3, "width": 420, "height": 440, "content": "## Write SEO Optimized Blog Post\n\n\n"}, "typeVersion": 1}, {"id": "6bf602e0-ad29-47e6-93d7-79fd2a4228c2", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1480, -120], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "qX50oKgUA6tXfxne", "name": "ChatBot Content"}}, "typeVersion": 1.2}, {"id": "8a3739ac-9492-400c-b5b8-eeb305647752", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [1740, -100], "parameters": {"jsonSchemaExample": "{\n\"slug\": \"rpo-benefits-recruitment\",\n\"title\": \"7 Key Advantages of RPO for Modern Recruitment\",\n\"meta\": \"Explore how Recruitment Process Outsourcing (RPO) enhances hiring efficiency, reduces costs, and expands talent pools for businesses seeking top candidates.\"\n}"}, "typeVersion": 1.2}, {"id": "af02ee94-4c26-4be5-bd21-09e020bff876", "name": "Metadata Generator", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1500, -300], "parameters": {"text": "=**Create a slug, blog post title, and meta description for the following blog post:**\n\n{{ $json.output }}\n\n**Slug Guidelines:**\n- Keep it concise (4-5 words maximum).\n- Include the primary keyword related to recruitment or HR.\n- Use hyphens to separate words.\n- Avoid unnecessary words, articles, or prepositions.\n- Ensure it reflects the main topic of the blog post.\n- Make it readable and relevant for both users and search engines.\n\n**Title Guidelines:**\n- Avoid AI words like \"Transform\" or \"Revolutionize\" and similar overused terms.\n- Avoid using a colon (:) in the title.\n- Never structure it as a primary/secondary title separated by a colon.\n- Include the primary keyword related to recruitment or HR (e.g., 'AI in recruitment' or 'talent acquisition trends').\n- Clearly inform users what they can expect from reading the blog post.\n- Be concise and engaging, ideally 50-60 characters long.\n- Incorporate power words that appeal to HR professionals and recruiters.\n\n**Meta Description Guidelines:**\n- Avoid AI words like \"Transform\" or \"Revolutionize\" and similar overused terms.\n- Be concise: Limit to 150-160 characters to ensure full visibility in search results.\n- Include keywords: Naturally incorporate primary recruitment-related keywords to enhance relevance and visibility.\n- Provide value: Clearly convey the benefits or insights readers will gain from the article.\n- Be engaging: Use action-oriented language or a thought-provoking question to encourage clicks.\n- Align with content: Accurately reflect the blog post's content to meet user expectations and reduce bounce rates.\n- Highlight expertise: Subtly emphasize SocialFind's authority in the recruitment field.\n\nYour output must be a single valid JSON object with these 3 fields:\n-slug: The slug\n-title: The blog post title\n-meta: The meta description  \n\nEach should be presented without any additional text, explanation, quotation marks, or formatting.\n", "options": {}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.8}, {"id": "4756c8f2-406e-4a56-adb0-0c4708dabe6a", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [2020, 140], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3}, {"id": "bf05eaf3-2522-488e-893d-1ed9b2ed88b2", "name": "Note5", "type": "n8n-nodes-base.stickyNote", "position": [220, 240], "parameters": {"color": 4, "width": 620, "height": 300, "content": "## Perplexity Research\n\n\n"}, "typeVersion": 1}, {"id": "22e8c044-ed98-495a-957e-c5e3fecc2b7d", "name": "On form submission", "type": "n8n-nodes-base.formTrigger", "position": [-260, 260], "webhookId": "a29cbcd3-9d11-4f7c-9aad-14681c356c53", "parameters": {"options": {}, "formTitle": "Blog Factory", "formFields": {"values": [{"fieldType": "textarea", "fieldLabel": "Research Query", "placeholder": "=What are the most common challenges facing Canadian employers regarding recruitment and why would they want to hire a recruiting firm to solve these problems.", "requiredField": true}]}, "formDescription": "Create SEO optimized blog posts"}, "typeVersion": 2.2}, {"id": "6e6d4952-793f-4dc5-8d29-219d420149a9", "name": "Note", "type": "n8n-nodes-base.stickyNote", "position": [360, 580], "parameters": {"width": 460, "height": 500, "content": "## Sample Generic Search Terms and write content\n\nYou are part of a marketing team that creates high-quality blog posts for the Men's Health Consulting and Workflow Automation industry based in Da Nang City, Vietnam.\n\nYour goal is to create engaging, SEO-optimized content that positions you as an authority in the Men's Health Consulting industry and attracts leads.\n\nUpon receiving the information, your team will post a blog on the most trending topics in Men's Health Consulting and Care. As a copywriter/blogger, you are provided with the following information:\n\n- Query: The main topic for the blog post, representing the most trending news in the Men's Health field.\n\n- Other Keywords: A list of high-volume keywords related to Men's Health Consulting and Support and Care. Incorporate these keywords naturally into the blog post when relevant, without forcing it or changing the meaning of the post.\n\n- Research findings: Detailed information from credible sources relevant to the blog topic. Your post should be based on this research.\n\nWith this information, write a comprehensive blog post that:\n\n- Include the query in the blog title, H2 heading, and the beginning of the introduction.\n\n- Incorporate all the details from the research findings, including the source URL for potential hyperlinks.\n\n- Be detailed and informative, demonstrating the company's expertise in Urology consulting and the support and care process for automation.\n\n- Use a professional but engaging tone, highlighting interesting developments and challenges in the industry.\n\n- Speak in a natural and logical manner, making it easy for readers to follow.\n\n- Be 1500 to 2000 words long.\n\n- Be written at a level that can be read by people who are interested in learning more. And want to receive additional care and support to solve the problem of interest.\n\nAdditional Requirements:\n- Include practical lessons or helpful tips for recruiters and HR professionals.\n\n- Highlight how the topic relates to the company's services or expertise.\n\n- Include a call to action (CTA) that encourages readers to explore the company's services or contact you for more information.\n\nCreate an entire blog post draft in your first output. Don't stop or cut it short.\n\nYour output should be the blog post and nothing else.\n\nHere are the details for this blog post project:\nQuery: [Execute previous nodes for preview]"}, "typeVersion": 1}, {"id": "f81c9505-111f-473a-94b6-c79364410810", "name": "Blog Content Generator", "type": "@n8n/n8n-nodes-langchain.agent", "position": [920, 260], "parameters": {"text": "=You are part of a marketing team that creates high-quality blog posts for the Men's Health Consulting and Workflow Automation industry based in Da Nang City, Vietnam.\n\nYour goal is to create engaging, SEO-optimized content that positions you as an authority in the Men's Health Consulting industry and attracts leads.\n\nUpon receiving the information, your team will post a blog on the most trending topics in Men's Health Consulting and Care. As a copywriter/blogger, you are provided with the following information:\n\n- Query: The main topic for the blog post, representing the most trending news in the Men's Health field.\n\n- Other Keywords: A list of high-volume keywords related to Men's Health Consulting and Support and Care. Incorporate these keywords naturally into the blog post when relevant, without forcing it or changing the meaning of the post.\n\n- Research findings: Detailed information from credible sources relevant to the blog topic. Your post should be based on this research.\n\nWith this information, write a comprehensive blog post that:\n\n- Include the query in the blog title, H2 heading, and the beginning of the introduction.\n\n- Incorporate all the details from the research findings, including the source URL for potential hyperlinks.\n\n- Be detailed and informative, demonstrating the company's expertise in Urology consulting and the support and care process for automation.\n\n- Use a professional but engaging tone, highlighting interesting developments and challenges in the industry.\n\n- Speak in a natural and logical manner, making it easy for readers to follow.\n\n- Be 1500 to 2000 words long.\n\n- Be written at a level that can be read by people who are interested in learning more. And want to receive additional care and support to solve the problem of interest.\n\nAdditional Requirements:\n- Include practical lessons or helpful tips for recruiters and HR professionals.\n\n- Highlight how the topic relates to the company's services or expertise.\n\n- Include a call to action (CTA) that encourages readers to explore the company's services or contact you for more information.\n\nCreate an entire blog post draft in your first output. Don't stop or cut it short.\n\nYour output should be the blog post and nothing else.\n\nHere are the details for this blog post project:\nQuery: {{ $json.output }}\n\n\n\n\n", "options": {}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "1ee6bb8f-6441-4ed9-83e0-d0839b2d0e01", "name": "Note7", "type": "n8n-nodes-base.stickyNote", "position": [1440, -380], "parameters": {"color": 7, "width": 440, "height": 440, "content": "## Create Title, Slug & Meta\n\n\n"}, "typeVersion": 1}, {"id": "30f0fa84-9918-4bf6-86e4-ef8f1dcf079c", "name": "gpt-4o-mini", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [920, 460], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "qX50oKgUA6tXfxne", "name": "ChatBot Content"}}, "typeVersion": 1.2}, {"id": "d2d83cc5-1502-4b04-ac12-0bb351a90e58", "name": "Combine Blog Details", "type": "n8n-nodes-base.aggregate", "position": [2620, 160], "parameters": {"options": {}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}, {"id": "bbb5b2c7-18a1-49f7-88d5-ebc0c585d128", "name": "Perplexity_Searcher", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [700, 420], "parameters": {"name": "Perplexity_Searcher", "workflowId": {"__rl": true, "mode": "id", "value": "5uapJIjLLhwnhX0n"}, "description": "=Tôi sử dụng AI agent n<PERSON><PERSON> để tìm kiếm những thông tin mới nhất. Nhằm phục vụ cho việc tìm kiếm thông tin, dữ liệu với đầy đủ thông tin mới nhất.", "workflowInputs": {"value": {}, "schema": [], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2.1}, {"id": "e0d9a29e-2dd9-42ff-a0b8-d81c02014b05", "name": "Tele HoangSP_Social_Media", "type": "n8n-nodes-base.telegramTrigger", "position": [-260, 440], "webhookId": "302be40c-6f54-4447-88a9-1c415a1fd72d", "parameters": {"updates": ["message"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "mjSBJIunOl3D8zbe", "name": "Telegram account"}}, "typeVersion": 1.1}, {"id": "95cc8220-d484-4ec9-a191-46a749de94a2", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [480, 260], "parameters": {"text": "=Tôi là một bác sĩ y khoa làm việc trong lĩnh vực y tế, chuyên môn của tôi là các Vấn đề liên quan đến bệnh Nam Khoa. \n- Tôi muốn dùng dữ liệu này để tìm kiếm thông tin, dựa trên từ khoá mà tôi đưa và để tìm những thông tin mới nhất có liên quan. \n\n- Chuẩn bị nội dung/ nguồn cho việc viết blog.\n\n- Hãy đưa ra những list nội dung đầu dòng ngắn gọn, hiệu quả để làm nội dung chính cho những blog sắp tới.\n\nĐây là nội dung tôi đưa vào tìm kiếm: {{ $json['Research Query'] }}\nHoặc nội dung này: {{ $json.message.text }}", "options": {"systemMessage": "<PERSON><PERSON>n làm vi<PERSON><PERSON> rất chuyên nghiệp trong lĩnh vực của mình"}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "1321ee35-f97d-475f-81cb-de00f833c89b", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [420, 420], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "ScDgXbCy0e7Omp6y", "name": "OpenAi account 3"}}, "typeVersion": 1.2}, {"id": "88ed96de-99da-4e8d-ba61-c7742109bc06", "name": "AI Agent1", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1520, 360], "parameters": {"text": "=\nExtract a JSON object from the following content: {{ $json.output }}.\nThe object must contain the following fields:\n{\n  \"title\": string,\n  \"subtitle\": string,\n  \"content\": string,\n  \"hashtags\": [string]\n}\nIf any field is missing, infer it based on the content. Respond only with the JSON object. Do not include explanations.\n\n", "options": {}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.8}, {"id": "cad725b7-9360-4f4a-8f72-1034f09192b5", "name": "OpenAI Chat Model2", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1480, 700], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "ScDgXbCy0e7Omp6y", "name": "OpenAi account 3"}}, "typeVersion": 1.2}, {"id": "5e5e91c5-787e-4c6e-99bf-a2d08638b26f", "name": "Metadata Extractor", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [1740, 640], "parameters": {"schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"title\": {\n      \"type\": \"string\"\n    },\n    \"subtitle\": {\n      \"type\": \"string\"\n    },\n    \"content\": {\n      \"type\": \"string\"\n    },\n    \"hashtags\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      },\n      \"maxItems\": 5\n    }\n  },\n  \"required\": [\"title\", \"content\"]\n}\n"}, "typeVersion": 1.2}, {"id": "38fd2a3b-bbc3-41d5-9d13-eb61b67709cb", "name": "Telegram", "type": "n8n-nodes-base.telegram", "position": [2620, 380], "webhookId": "13eeb1fb-8890-430b-b988-3e90fdf032c9", "parameters": {"text": "={{ $json.output.title }}\n{{ $json.output.title }}\n{{ $json.output.subtitle }}\n{{ $json.output.content }}", "chatId": "={{ $('Tele <PERSON>SP_Social_Media').item.json.message.chat.id }}", "additionalFields": {"appendAttribution": false, "disable_notification": false}}, "credentials": {"telegramApi": {"id": "1kmgeuArwvrmbfeu", "name": "Telegram account 2"}}, "typeVersion": 1.2}, {"id": "6a165afa-074d-4f2c-9f3b-2c8f02f3ae46", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [1600, 540], "parameters": {"sessionKey": "={{ $json.output }}", "sessionIdType": "customKey", "contextWindowLength": 10}, "typeVersion": 1.3}, {"id": "433b5974-6bd9-4bd8-a718-e9a1970de35b", "name": "Simple Memory1", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [1600, -60], "parameters": {"sessionKey": "={{ $json.output }}", "sessionIdType": "customKey", "contextWindowLength": 10}, "typeVersion": 1.3}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "ecebdadc-5bb8-43e7-bd42-48fd894195b1", "connections": {"Merge": {"main": [[{"node": "Telegram", "type": "main", "index": 0}, {"node": "Combine Blog Details", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Copywriter AI Agent", "type": "main", "index": 0}]]}, "Telegram": {"main": [[]]}, "AI Agent1": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "gpt-4o-mini": {"ai_languageModel": [[{"node": "Copywriter AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent1", "type": "ai_memory", "index": 0}]]}, "Simple Memory1": {"ai_memory": [[{"node": "Create Title, <PERSON>lug, <PERSON>a", "type": "ai_memory", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Create Title, <PERSON>lug, <PERSON>a", "type": "ai_languageModel", "index": 0}]]}, "On form submission": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model2": {"ai_languageModel": [[{"node": "AI Agent1", "type": "ai_languageModel", "index": 0}]]}, "Copywriter AI Agent": {"main": [[{"node": "Create Title, <PERSON>lug, <PERSON>a", "type": "main", "index": 0}, {"node": "AI Agent1", "type": "main", "index": 0}]]}, "Combine Blog Details": {"main": [[]]}, "Call n8n Workflow Tool": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Create Title, Slug, Meta": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Create Title, <PERSON>lug, <PERSON>a", "type": "ai_outputParser", "index": 0}]]}, "Structured Output Parser1": {"ai_outputParser": [[{"node": "AI Agent1", "type": "ai_outputParser", "index": 0}]]}, "Tele HoangSP_Social_Media": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}}}