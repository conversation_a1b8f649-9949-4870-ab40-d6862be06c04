{"id": "RJ4PaYq0JBr29KJm", "meta": {"instanceId": "e3de7ac3dee198637aeea8f82bd3b7f55121370bf7582aeef633e085d2f68ac8"}, "name": "Reschedule overdue <PERSON><PERSON> tasks and clean up completed tasks", "tags": [{"id": "oMfA3lEfbqs7MU2P", "name": "Template", "createdAt": "2025-01-06T20:33:18.396Z", "updatedAt": "2025-01-06T20:33:18.396Z"}], "nodes": [{"id": "9262720e-2beb-4426-a472-3d7bf8bc28af", "name": "Everyday at 7am", "type": "n8n-nodes-base.scheduleTrigger", "position": [80, -520], "parameters": {"rule": {"interval": [{"triggerAtHour": 7}]}}, "typeVersion": 1.2}, {"id": "0d074451-5d61-4ed4-86a8-f6cdf002e84b", "name": "Get user tasks", "type": "n8n-nodes-base.asana", "position": [320, -520], "parameters": {"filters": {"assignee": "****************", "workspace": "****************", "completed_since": "={{ DateTime.now().format('yyyy-MM-dd') }}"}, "operation": "getAll", "returnAll": true}, "credentials": {"asanaApi": {"id": "u7fFpY0SmMcpBCdn", "name": "Asana account"}}, "typeVersion": 1}, {"id": "********-9bda-4fc1-9fef-aa6a74c2365a", "name": "Get task infos", "type": "n8n-nodes-base.asana", "position": [540, -520], "parameters": {"id": "={{ $json.gid }}", "operation": "get"}, "credentials": {"asanaApi": {"id": "u7fFpY0SmMcpBCdn", "name": "Asana account"}}, "typeVersion": 1}, {"id": "e7d9a37c-66b7-46b9-b228-7372cb0d7b09", "name": "Task is open?", "type": "n8n-nodes-base.if", "position": [780, -520], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "145d9367-7662-4ed9-8195-bf9b35c78d6b", "operator": {"type": "boolean", "operation": "false", "singleValue": true}, "leftValue": "={{ $json.completed }}", "rightValue": "false"}]}}, "typeVersion": 2.2}, {"id": "11ae0bbb-8d76-4623-9a24-2c2a36600dd3", "name": "Due date in the past?", "type": "n8n-nodes-base.if", "position": [1020, -640], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "loose"}, "combinator": "and", "conditions": [{"id": "dbecabb3-8075-4cc0-94af-b678c8af8f66", "operator": {"type": "number", "operation": "lt"}, "leftValue": "={{ $json.due_on.replaceAll(\"-\",\"\") }}", "rightValue": "={{ DateTime.now().format('yyyyMMdd') }}"}]}, "looseTypeValidation": true}, "typeVersion": 2.2}, {"id": "282d79c7-e74a-4249-ad37-b4d81655a206", "name": "Set due date to Today", "type": "n8n-nodes-base.asana", "position": [1280, -680], "parameters": {"id": "={{ $json.gid }}", "operation": "update", "otherProperties": {"due_on": "={{ DateTime.now().format('yyyy-MM-dd') }}"}}, "credentials": {"asanaApi": {"id": "u7fFpY0SmMcpBCdn", "name": "Asana account"}}, "typeVersion": 1}, {"id": "7cc18243-d3d4-4624-a906-a1617e411b0c", "name": "Clean up task", "type": "n8n-nodes-base.asana", "position": [1020, -440], "parameters": {"id": "={{ $json.gid }}", "operation": "delete"}, "credentials": {"asanaApi": {"id": "u7fFpY0SmMcpBCdn", "name": "Asana account"}}, "typeVersion": 1}, {"id": "f4aafa1f-8c5b-4fd1-9aca-fd096508dbfb", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [40, -800], "parameters": {"color": 5, "width": 640, "height": 240, "content": "### ⚙️ Set Up \n\n1. Add your **Asana** credentials\n2. Schedule the workflow to run at desired intervals (e.g., daily or weekly).\n3. Select your **Workspace Name** and your **Assignee Name** (user) in the **Get user tasks** node\n4. *(Optional) Tailor filtering conditions to match your preferred due-date rules and removal criteria.*\n5. **Activate the workflow** and watch your Asana workspace stay up to date and clutter-free."}, "typeVersion": 1}, {"id": "e4fcbdee-5dd0-40dc-b1ef-f7b8ce00dd03", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [60, -360], "parameters": {"color": 7, "width": 160, "height": 100, "content": "👆 \nUpdate the **Scheduler** here"}, "typeVersion": 1}, {"id": "195f467d-1124-4216-ab0e-048c6a9fc752", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [280, -360], "parameters": {"color": 7, "width": 200, "height": 100, "content": "👆 \nSelect your **Workspace Name** & **Assignee Name** here"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"timezone": "Europe/Paris", "callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1"}, "versionId": "fdc51229-75f4-4489-a7f7-1f36a35d43ac", "connections": {"Task is open?": {"main": [[{"node": "Due date in the past?", "type": "main", "index": 0}], [{"node": "Clean up task", "type": "main", "index": 0}]]}, "Get task infos": {"main": [[{"node": "Task is open?", "type": "main", "index": 0}]]}, "Get user tasks": {"main": [[{"node": "Get task infos", "type": "main", "index": 0}]]}, "Everyday at 7am": {"main": [[{"node": "Get user tasks", "type": "main", "index": 0}]]}, "Due date in the past?": {"main": [[{"node": "Set due date to Today", "type": "main", "index": 0}]]}}}