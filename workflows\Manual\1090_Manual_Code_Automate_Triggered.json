{"meta": {"instanceId": "257476b1ef58bf3cb6a46e65fac7ee34a53a5e1a8492d5c6e4da5f87c9b82833"}, "nodes": [{"id": "f7f8068b-52c9-4038-bd67-9ee50136e4fd", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [380, 240], "parameters": {}, "typeVersion": 1}, {"id": "860e5e46-a04d-41cb-b91a-c9f02603bcdf", "name": "Sample data (name + language)", "type": "n8n-nodes-base.code", "position": [600, 160], "parameters": {"jsCode": "return [\n  {\n    json: {\n      name: '<PERSON>',\n      language: 'de',\n    }\n  },\n  {\n    json: {\n      name: '<PERSON>',\n      language: 'en',\n    }\n  },\n  {\n    json: {\n      name: '<PERSON>',\n      language: 'de',\n    }\n  }\n];"}, "typeVersion": 2}, {"id": "5c6a867b-fd8a-49b7-ac35-ff84ed6d89f7", "name": "Sample data (greeting + language)", "type": "n8n-nodes-base.code", "position": [600, 320], "parameters": {"jsCode": "return [\n\t  {\n    json: {\n      greeting: 'Hello',\n      language: 'en',\n    }\n  },\n  {\n    json: {\n      greeting: '<PERSON><PERSON>',\n      language: 'de',\n    }\n  }\n];"}, "typeVersion": 2}, {"id": "08fca489-8f4c-4327-9919-922bd1be1cd5", "name": "Merge (name + language + greeting)", "type": "n8n-nodes-base.merge", "position": [820, 240], "parameters": {"mode": "combine", "options": {}, "fieldsToMatchString": "language"}, "typeVersion": 3}], "pinData": {}, "connections": {"Sample data (name + language)": {"main": [[{"node": "Merge (name + language + greeting)", "type": "main", "index": 0}]]}, "Sample data (greeting + language)": {"main": [[{"node": "Merge (name + language + greeting)", "type": "main", "index": 1}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Sample data (name + language)", "type": "main", "index": 0}, {"node": "Sample data (greeting + language)", "type": "main", "index": 0}]]}}}