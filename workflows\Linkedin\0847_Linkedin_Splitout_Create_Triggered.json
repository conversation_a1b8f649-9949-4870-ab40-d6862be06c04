{"meta": {"instanceId": "2f17285f1745a5069c9edd8be78921f40c6549f5b2e1cfd76834c7f73edd2c07", "templateCredsSetupCompleted": true}, "nodes": [{"id": "02628817-d072-4caa-b935-945d09f57a85", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [0, 0], "parameters": {}, "typeVersion": 1}, {"id": "7361f9a8-d834-49d3-b0c1-bb4510f654cc", "name": "Gmail", "type": "n8n-nodes-base.gmail", "position": [220, 0], "webhookId": "326419f6-008b-4814-b55d-efaae118eab7", "parameters": {"limit": 1, "simple": false, "filters": {"sender": "<EMAIL>"}, "options": {}, "operation": "getAll"}, "credentials": {"gmailOAuth2": {"id": "pwMK2jDEWY5arMX3", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "39e63d5f-db0d-4fc6-a5e8-a9ac3c2a703c", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [816, 0], "parameters": {"options": {}, "fieldToSplitOut": "message.content.news_items"}, "typeVersion": 1}, {"id": "70e64a00-8dc0-4ef4-a4fd-3ac2e50c8fb3", "name": "Extract News Items", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [440, 0], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "o3-mini-2025-01-31", "cachedResultName": "O3-MINI-2025-01-31"}, "options": {}, "messages": {"values": [{"content": "=Given the following newsletter content, identify and summarize the 5 main news items. Focus on factual updates like new AI tools, product launches, or strategic investments. For each item, extract a headline and provide a concise summary. Please ignore purely promotional sections (e.g., calls to book demos or product advertisements).\n\n<text>\n{{ $json.text }}\n</text>"}]}, "jsonOutput": true}, "credentials": {"openAiApi": {"id": "29u49HnATSs6YuKN", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "cecf013b-bcf2-49a3-acc2-b81e355446b6", "name": "Create LinkedIn Posts", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1040, 0], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "o3-mini-2025-01-31", "cachedResultName": "O3-MINI-2025-01-31"}, "options": {}, "messages": {"values": [{"content": "=Using the news item details below:\n\nHeadline: {{ $json.headline }}\nSummary: {{ $json.summary }}\n\nCraft a concise, non-promotional LinkedIn post in a smart, deadpan style with subtle humor. Focus on clearly conveying the main points and insights so readers gain practical value. \n- Break up the text into short paragraphs or bullet points for clarity.\n- Use line breaks where helpful.\n- End with an observation or question that encourages reflection—without being overly salesy or flashy.\n- Keep it under 80 words total.\n\n"}]}}, "credentials": {"openAiApi": {"id": "29u49HnATSs6YuKN", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "31412fb3-ef9a-4c98-840b-a97fd7075181", "name": "LinkedIn", "type": "n8n-nodes-base.linkedIn", "position": [1420, 0], "parameters": {"text": "={{ $json.message.content }}", "person": "EI5XKdiMv1", "additionalFields": {}}, "credentials": {"linkedInOAuth2Api": {"id": "G3JLFJtB5Y7q9FSY", "name": "LinkedIn account"}}, "typeVersion": 1}, {"id": "a80f43a1-35c8-4f41-8d96-6e64e4ae0cf7", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-20, -620], "parameters": {"width": 900, "height": 520, "content": "# Workflow Overview\n\n**Name:** Transform Gmail Newsletters into Insightful LinkedIn Posts Using OpenAI\n\n**Purpose:**  \n- **Filter Newsletters:** Use the Gmail node to process emails from a specific sender (e.g., `<EMAIL>`).  \n- **Extract Key Items:** Leverage an OpenAI node to identify and summarize the top news items from each newsletter.  \n- **Generate Posts:** Automatically create concise, informative, and subtly humorous LinkedIn posts for each news item.  \n- **Publish:** Post the refined content to your LinkedIn account with the LinkedIn node.\n\n**Setup Steps:**  \n1. **Gmail Node:** Configure and rename to \"Filter Gmail Newsletter\" with the appropriate sender filter.  \n2. **OpenAI Nodes:** Ensure API credentials are set; customize prompt texts if desired.  \n3. **LinkedIn Node:** Rename to \"Post to LinkedIn\" and verify correct OAuth2 credentials.\n\n**Customization Tips:**  \n- Modify the OpenAI prompts to fine-tune the tone and structure of the LinkedIn posts.  \n- Add additional formatting (e.g., Function nodes) for post readability if needed.\n\n*This workflow turns your regular newsletters into engaging, ready-to-share LinkedIn insights in just a few simple steps!*\n"}, "typeVersion": 1}], "pinData": {}, "connections": {"Gmail": {"main": [[{"node": "Extract News Items", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Create LinkedIn Posts", "type": "main", "index": 0}]]}, "Extract News Items": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Create LinkedIn Posts": {"main": [[{"node": "LinkedIn", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Gmail", "type": "main", "index": 0}]]}}}