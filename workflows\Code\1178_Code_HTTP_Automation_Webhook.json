{"id": "8Sbrzc7Au3ZGf62p", "meta": {"instanceId": "bcc0fe85b176c2837affb21bb7d7397fad2549880e73dc1f7a42e76ae94fd996", "templateCredsSetupCompleted": true}, "name": "Publish Videos & Images - Blotato", "tags": [{"id": "3ys8SQgNTiRr899i", "name": "social media", "createdAt": "2025-03-17T08:37:35.227Z", "updatedAt": "2025-04-07T06:13:46.923Z"}, {"id": "zyM31CVcOgUrUm2P", "name": "blotato", "createdAt": "2025-04-25T13:38:49.620Z", "updatedAt": "2025-04-25T13:38:49.620Z"}, {"id": "2wv2YbZIQoYNx98Y", "name": "schedule", "createdAt": "2025-04-25T13:38:53.789Z", "updatedAt": "2025-04-25T13:38:53.789Z"}, {"id": "PqlvV87F8bOW0yAK", "name": "publish", "createdAt": "2025-04-25T13:38:58.944Z", "updatedAt": "2025-04-25T13:38:58.944Z"}], "nodes": [{"id": "53b36edb-e273-4e68-8ae9-7d3de3f7533f", "name": "[Instagram] Publish via Blotato", "type": "n8n-nodes-base.httpRequest", "onError": "continueErrorOutput", "position": [96, 80], "parameters": {"url": "https://backend.blotato.com/v2/posts", "method": "POST", "options": {}, "jsonBody": "={\n  \"post\": {\n    \"target\": {\n      \"targetType\": \"instagram\"\n    },\n    \"content\": {\n      \"text\": {{ $('Prepare for Publish').item.json.final_text_short.toJsonString() }},\n      \"platform\": \"instagram\",\n      \"mediaUrls\": [\"{{ $json.url }}\"]\n    },\n    \"accountId\": \"{{ $('Prepare for Publish').item.json.instagram_id }}\"\n  }\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "OEAfX6pMtcbyFBAp", "name": "<PERSON><PERSON><PERSON>"}}, "typeVersion": 4.2}, {"id": "6fef0d35-9679-40f6-9224-cfb7c442056b", "name": "[Facebook] Publish via Blotato", "type": "n8n-nodes-base.httpRequest", "onError": "continueErrorOutput", "position": [96, 480], "parameters": {"url": "https://backend.blotato.com/v2/posts", "method": "POST", "options": {}, "jsonBody": "={\n  \"post\": {\n    \"target\": {\n      \"targetType\": \"facebook\",\n      \"pageId\": \"{{ $('Prepare for Publish').item.json.facebook_page_id }}\"\n    },\n    \"content\": {\n      \"text\": {{ $('Prepare for Publish').item.json.final_text_long.toJsonString() }},\n      \"platform\": \"facebook\",\n      \"mediaUrls\": [\"{{ $json.url }}\"]\n    },\n    \"accountId\": \"{{ $('Prepare for Publish').item.json.facebook_id }}\"\n  }\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "OEAfX6pMtcbyFBAp", "name": "<PERSON><PERSON><PERSON>"}}, "typeVersion": 4.2}, {"id": "3f744ee2-988b-45a6-9d88-a718780421cf", "name": "[Linkedin] Publish via Blotato", "type": "n8n-nodes-base.httpRequest", "onError": "continueErrorOutput", "position": [96, 880], "parameters": {"url": "https://backend.blotato.com/v2/posts", "method": "POST", "options": {}, "jsonBody": "={\n  \"post\": {\n    \"target\": {\n      \"targetType\": \"linkedin\"\n    },\n    \"content\": {\n      \"text\": {{ $('Prepare for Publish').item.json.final_text_long.toJsonString() }},\n      \"platform\": \"linkedin\",\n      \"mediaUrls\": [\"{{ $json.url }}\"]\n    },\n    \"accountId\": \"{{ $('Prepare for Publish').item.json.linkedin_id }}\"\n  }\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "OEAfX6pMtcbyFBAp", "name": "<PERSON><PERSON><PERSON>"}}, "typeVersion": 4.2}, {"id": "7f0ae540-090a-4b13-a094-2ac74e1a14b3", "name": "[Tiktok] Publish via Blotato", "type": "n8n-nodes-base.httpRequest", "onError": "continueErrorOutput", "position": [96, 1280], "parameters": {"url": "https://backend.blotato.com/v2/posts", "method": "POST", "options": {}, "jsonBody": "={\n  \"post\": {\n    \"target\": {\n      \"targetType\": \"tiktok\",\n      \"isYourBrand\": false,\n      \"disabledDuet\": false,\n      \"privacyLevel\": \"PUBLIC_TO_EVERYONE\",\n      \"isAiGenerated\": true,\n      \"disabledStitch\": false,\n      \"disabledComments\": false,\n      \"isBrandedContent\": false\n    },\n    \"content\": {\n      \"text\": \"{{ $('Prepare for Publish').item.json.final_text_short }}\",\n      \"platform\": \"tiktok\",\n      \"mediaUrls\": [\"{{ $json.url }}\"]\n    },\n    \"accountId\": \"{{ $('Prepare for Publish').item.json.tiktok_id }}\"\n  }\n}\n", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "OEAfX6pMtcbyFBAp", "name": "<PERSON><PERSON><PERSON>"}}, "typeVersion": 4.2}, {"id": "2593a621-0d0b-42b3-97ab-bf85785fb33c", "name": "[Pinterest] Publish via Blotato", "type": "n8n-nodes-base.httpRequest", "onError": "continueErrorOutput", "position": [96, 1680], "parameters": {"url": "https://backend.blotato.com/v2/posts", "method": "POST", "options": {}, "jsonBody": "={\n  \"post\": {\n    \"target\": {\n      \"targetType\": \"pinterest\",\n      \"boardId\": \"{{ $('Prepare for Publish').item.json.pinterested_board_id }}\",\n      \"link\": \"https://www.AIwithApex.com/\"\n    },\n    \"content\": {\n      \"text\": {{ $('Prepare for Publish').item.json.final_text_short.toJsonString() }},\n      \"platform\": \"pinterest\",\n      \"mediaUrls\": [\"{{ $('Upload Image to Blotato').item.json.url }}\"]\n    },\n    \"accountId\": \"{{ $('Prepare for Publish').item.json.pinterest_id }}\"\n  }\n}\n", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "OEAfX6pMtcbyFBAp", "name": "<PERSON><PERSON><PERSON>"}}, "typeVersion": 4.2}, {"id": "b47da27b-b81f-4736-9c34-cd224d78f29d", "name": "[Youtube] Publish via Blotato", "type": "n8n-nodes-base.httpRequest", "onError": "continueErrorOutput", "position": [96, 280], "parameters": {"url": "https://backend.blotato.com/v2/posts", "method": "POST", "options": {}, "jsonBody": "={\n  \"post\": {\n    \"target\": {\n      \"targetType\": \"youtube\",\n      \"title\": \"{{ $('Ensure Valid YouTube Title').item.json.message.content.youtube_title }}\",\n      \"privacyStatus\": \"public\",\n      \"shouldNotifySubscribers\": true\n    },\n    \"content\": {\n      \"text\": {{ $('Prepare for Publish').item.json.final_text_long.toJsonString() }},\n      \"platform\": \"youtube\",\n      \"mediaUrls\": [\"{{ $json.url }}\"]\n    },\n    \"accountId\": \"{{ $('Prepare for Publish').item.json.youtube_id }}\"\n  }\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "OEAfX6pMtcbyFBAp", "name": "<PERSON><PERSON><PERSON>"}}, "typeVersion": 4.2}, {"id": "19f17323-45f5-4865-b754-e2d5f8fc6073", "name": "[Threads] Publish via Blotato", "type": "n8n-nodes-base.httpRequest", "onError": "continueErrorOutput", "position": [96, 680], "parameters": {"url": "https://backend.blotato.com/v2/posts", "method": "POST", "options": {}, "jsonBody": "={\n  \"post\": {\n    \"target\": {\n      \"targetType\": \"threads\"\n    },\n    \"content\": {\n      \"text\": {{ $('Prepare for Publish').item.json.final_text_short.toJsonString() }},\n      \"platform\": \"threads\",\n      \"mediaUrls\": [\"{{ $json.url }}\"]\n    },\n    \"accountId\": \"{{ $('Prepare for Publish').item.json.threads_id }}\"\n  }\n}\n", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "OEAfX6pMtcbyFBAp", "name": "<PERSON><PERSON><PERSON>"}}, "typeVersion": 4.2}, {"id": "adaa3ac1-fa2a-422e-8b70-8c4cc74782ed", "name": "[Twitter] Publish via Blotato", "type": "n8n-nodes-base.httpRequest", "onError": "continueErrorOutput", "position": [96, 1080], "parameters": {"url": "https://backend.blotato.com/v2/posts", "method": "POST", "options": {}, "jsonBody": "={\n  \"post\": {\n    \"target\": {\n      \"targetType\": \"twitter\"\n    },\n    \"content\": {\n      \"text\": {{ $('Prepare for Publish').item.json.final_text_short.toJsonString() }},\n      \"platform\": \"twitter\",\n      \"mediaUrls\": [\"{{ $json.url }}\"]\n    },\n    \"accountId\": \"{{ $('Prepare for Publish').item.json.twitter_id }}\"\n  }\n}\n", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "OEAfX6pMtcbyFBAp", "name": "<PERSON><PERSON><PERSON>"}}, "typeVersion": 4.2}, {"id": "af2d0a29-73d1-4ee0-84c9-f337a628b3ea", "name": "[<PERSON><PERSON>] Publish via Blotato", "type": "n8n-nodes-base.httpRequest", "onError": "continueErrorOutput", "position": [96, 1480], "parameters": {"url": "https://backend.blotato.com/v2/posts", "method": "POST", "options": {}, "jsonBody": "={\n  \"post\": {\n    \"target\": {\n      \"targetType\": \"bluesky\"\n    },\n    \"content\": {\n      \"text\": {{ $('Prepare for Publish').item.json.final_text_short.toJsonString() }},\n      \"platform\": \"bluesky\",\n      \"mediaUrls\": [\"{{ $('Upload Image to Blotato').item.json.url }}\"]\n    },\n    \"accountId\": \"{{ $('Prepare for Publish').item.json.bluesky_id }}\"\n  }\n}\n", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "OEAfX6pMtcbyFBAp", "name": "<PERSON><PERSON><PERSON>"}}, "typeVersion": 4.2}, {"id": "08253c79-44f6-471a-b7c9-b84046f16888", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [16, -40], "parameters": {"color": 2, "width": 260, "height": 1880, "content": "# Publish to Social Media"}, "typeVersion": 1}, {"id": "dae9e738-c391-47f6-bdf7-87055a5a77f0", "name": "Prepare for Publish", "type": "n8n-nodes-base.set", "position": [-564, 880], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "={\n  \"instagram_id\": \"2244\",\n  \"youtube_id\": \"1300\",\n  \"tiktok_id\": \"2761\",\n  \"facebook_id\": \"2152\",\n  \"facebook_page_id\": \"***************\",\n  \"threads_id\": \"670\",\n  \"twitter_id\": \"1576\",\n  \"linkedin_id\": \"1730\",\n  \"pinterest_id\": \"447\",\n  \"pinterested_board_id\": \"1097611809123639891\",\n  \"bluesky_id\": \"1311\",\n  \"final_text_long\": {{ $('Airtable').item.json.Script.toJsonString() }},\n  \"final_text_short\": {{ $('Airtable').item.json['Text for X'].toJsonString() }}\n}"}, "typeVersion": 3.4}, {"id": "aaa21d7f-e175-4516-8bde-e0b87e6e183d", "name": "Airtable", "type": "n8n-nodes-base.airtable", "position": [-1160, 880], "parameters": {"id": "={{ $json.airtableID }}", "base": {"__rl": true, "mode": "list", "value": "appt2yDl6xXXyqboD", "cachedResultUrl": "https://airtable.com/appt2yDl6xXXyqboD", "cachedResultName": "Social Media System"}, "table": {"__rl": true, "mode": "list", "value": "tblM3kDu1qB2FdTOF", "cachedResultUrl": "https://airtable.com/appt2yDl6xXXyqboD/tblM3kDu1qB2FdTOF", "cachedResultName": "Media Creation"}, "options": {}}, "credentials": {"airtableTokenApi": {"id": "YzrajURFsZkojT3x", "name": "Delete Me Later Please!"}}, "typeVersion": 2.1}, {"id": "e05d1ede-7d43-4c55-a353-6bc2bd0216d4", "name": "Upload Video to Blotato", "type": "n8n-nodes-base.httpRequest", "position": [-124, 880], "parameters": {"url": "https://backend.blotato.com/v2/media", "method": "POST", "options": {}, "sendBody": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "url", "value": "={{ $('Airtable').item.json['Video URL'] }}"}]}, "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "gNaQW1qT8liDV4ls", "name": "Delete me toooooo!"}}, "typeVersion": 4.2}, {"id": "92fd39a9-74c1-4cbb-abb3-511f8230cee7", "name": "Upload Image to Blotato", "type": "n8n-nodes-base.httpRequest", "position": [-344, 880], "parameters": {"url": "https://backend.blotato.com/v2/media", "method": "POST", "options": {}, "sendBody": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "url", "value": "={{ $('Airtable').item.json['Image URL'] }}"}]}, "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "gNaQW1qT8liDV4ls", "name": "Delete me toooooo!"}}, "typeVersion": 4.2}, {"id": "12b8ecfe-f83b-4e32-a8d7-a71b7f5c9fd1", "name": "Ensure Valid YouTube Title", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [-940, 880], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4.1-mini", "cachedResultName": "GPT-4.1-MINI"}, "options": {}, "messages": {"values": [{"content": "=CURRENT_TITLE:\n{{ $json['Media Title'] }}"}, {"role": "assistant", "content": "# TASK\nYou specialize in creating Viral YouTube Short Video Titles.  You are to take User's CURRENT_TITLE and re-write it to go viral.\n## Rules\n - Maximum 100 Characters\n - Goal is Virality!\n - Must be valid title for a YouTube Short Video\n# OUTPUT\nOutput must be in JSON format, example:\n{ \"youtube_title\": \"<generated title per instructions>\" }"}]}, "jsonOutput": true}, "credentials": {"openAiApi": {"id": "KzjXYSuzUOCnnvzB", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "d83577ae-5aa7-4d47-a67b-30e30fcd2fa4", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-1660, 1060], "parameters": {"width": 880, "height": 360, "content": "## Quick Debug Checking\n### I set up quick links to social media to check whether the posting system succeeded or not.  I tried video if possible, if not I used image.  You can also find <PERSON><PERSON>ato failed posts here: https://my.blotato.com/failed\n\n[replace these with your own links if you like]\nInstagram: https://www.instagram.com/moshehbenavraham/reels/\nYoutube: https://www.youtube.com/@AIwithApex/shorts\nFacebook: https://www.facebook.com/MoshehApexWebServices/grid\nThreads: https://www.threads.com/@moshehbenavraham\nLinkedIn: https://www.linkedin.com/in/moshehbenavraham/recent-activity/all/\nX / Twitter: https://x.com/MoshehAvraham\nTikTok: https://www.tiktok.com/@moshehavraham\n<PERSON>: https://bsky.app/profile/aiwithapex.bsky.social\nPinterest: https://www.pinterest.com/aiwithapex/artificial-intelligence-ai-ai-automation/\n"}, "typeVersion": 1}, {"id": "356518e3-1eb6-4cfe-bf77-a5f095553b74", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-880, 1440], "parameters": {"width": 880, "height": 300, "content": "## Current Issues (last updated April 29, 2025)\n\n- Haven't confirmed, but you -have- to post to a FB Page?\n- I believe you can only post to a particular Board in Pinterest\n- Some Endpoints can handle longer text, some not\n- Some Endpoints can handle videos, some not\n- With Facebook, apparently only plain text is accepted\n- With LinkedIn, apparently only plain text is accepted\n- Haven't found info about ways to access what you have uploaded to Blotato, nor capacity limit, nor how long the assets are stored for -- did find a concurrency limit of using it 10 requests/minute\n- Encountered this error with YouTube POST despite not posting that much: \"Error: The request cannot be completed because you have exceeded your <a href=\"/youtube/v3/getting-started#quota\">quota</a>.\""}, "typeVersion": 1}, {"id": "503a04c9-a740-4c5d-9f67-143a8dd1422f", "name": "Airtable: Posted Instagram", "type": "n8n-nodes-base.airtable", "position": [316, -20], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appt2yDl6xXXyqboD", "cachedResultUrl": "https://airtable.com/appt2yDl6xXXyqboD", "cachedResultName": "Social Media System"}, "table": {"__rl": true, "mode": "list", "value": "tblM3kDu1qB2FdTOF", "cachedResultUrl": "https://airtable.com/appt2yDl6xXXyqboD/tblM3kDu1qB2FdTOF", "cachedResultName": "Media Creation"}, "columns": {"value": {"id": "={{ $('Airtable').item.json.id", "Production": "Completed"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "Media Title", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Media Title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON><PERSON>", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "<PERSON><PERSON><PERSON>", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON>t Len", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "<PERSON><PERSON>t Len", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Production", "type": "options", "display": true, "options": [{"name": "Not Started", "value": "Not Started"}, {"name": "In progress", "value": "In progress"}, {"name": "Ready", "value": "Ready"}, {"name": "Review", "value": "Review"}, {"name": "Completed", "value": "Completed"}, {"name": "Scheduled", "value": "Scheduled"}, {"name": "Published", "value": "Published"}], "removed": false, "readOnly": false, "required": false, "displayName": "Production", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Video URL", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Video URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Video", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Video", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Publish Date (from Content Creation)", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Publish Date (from Content Creation)", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Publish Time (from Content Creation)", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Publish Time (from Content Creation)", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Test", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Test", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Content Creation", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Content Creation", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Scenes", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Scenes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Image URL", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Image URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Image", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Image", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Image Caption", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Image Caption", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Text for X", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Text for X", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Text for LinkedIn", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Text for LinkedIn", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Social Channels", "type": "array", "display": true, "options": [{"name": "Blog", "value": "Blog"}, {"name": "Facebook", "value": "Facebook"}, {"name": "Instagram", "value": "Instagram"}, {"name": "LinkedIn", "value": "LinkedIn"}, {"name": "TikTok", "value": "TikTok"}, {"name": "X", "value": "X"}, {"name": "YouTube", "value": "YouTube"}], "removed": true, "readOnly": false, "required": false, "displayName": "Social Channels", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "n8n Publishing Date", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "n8n Publishing Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "n8n Publishing Time", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "n8n Publishing Time", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Publishing Log", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Publishing Log", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "House Keeping", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "House Keeping", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "workflowId (from House Keeping)", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "workflowId (from House Keeping)", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "recordID (from House Keeping)", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "recordID (from House Keeping)", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Created Time", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Created Time", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Modified Time", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Modified Time", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Record ID", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Record ID", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["id"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update"}, "credentials": {"airtableTokenApi": {"id": "YzrajURFsZkojT3x", "name": "Delete Me Later Please!"}}, "typeVersion": 2.1}, {"id": "f42a3763-8906-4278-b8d0-73611a1fae31", "name": "Airtable: Posted Instagram1", "type": "n8n-nodes-base.airtable", "position": [316, 180], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appt2yDl6xXXyqboD", "cachedResultUrl": "https://airtable.com/appt2yDl6xXXyqboD", "cachedResultName": "Social Media System"}, "table": {"__rl": true, "mode": "list", "value": "tblM3kDu1qB2FdTOF", "cachedResultUrl": "https://airtable.com/appt2yDl6xXXyqboD/tblM3kDu1qB2FdTOF", "cachedResultName": "Media Creation"}, "columns": {"value": {"id": "={{ $('Airtable').item.json.id", "Production": "In progress"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "Media Title", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Media Title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON><PERSON>", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "<PERSON><PERSON><PERSON>", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON>t Len", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "<PERSON><PERSON>t Len", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Production", "type": "options", "display": true, "options": [{"name": "Not Started", "value": "Not Started"}, {"name": "In progress", "value": "In progress"}, {"name": "Ready", "value": "Ready"}, {"name": "Review", "value": "Review"}, {"name": "Completed", "value": "Completed"}, {"name": "Scheduled", "value": "Scheduled"}, {"name": "Published", "value": "Published"}], "removed": false, "readOnly": false, "required": false, "displayName": "Production", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Video URL", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Video URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Video", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Video", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Publish Date (from Content Creation)", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Publish Date (from Content Creation)", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Publish Time (from Content Creation)", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Publish Time (from Content Creation)", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Test", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Test", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Content Creation", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Content Creation", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Scenes", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Scenes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Image URL", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Image URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Image", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Image", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Image Caption", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Image Caption", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Text for X", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Text for X", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Text for LinkedIn", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Text for LinkedIn", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Social Channels", "type": "array", "display": true, "options": [{"name": "Blog", "value": "Blog"}, {"name": "Facebook", "value": "Facebook"}, {"name": "Instagram", "value": "Instagram"}, {"name": "LinkedIn", "value": "LinkedIn"}, {"name": "TikTok", "value": "TikTok"}, {"name": "X", "value": "X"}, {"name": "YouTube", "value": "YouTube"}], "removed": true, "readOnly": false, "required": false, "displayName": "Social Channels", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "n8n Publishing Date", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "n8n Publishing Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "n8n Publishing Time", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "n8n Publishing Time", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Publishing Log", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Publishing Log", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "House Keeping", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "House Keeping", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "workflowId (from House Keeping)", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "workflowId (from House Keeping)", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "recordID (from House Keeping)", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "recordID (from House Keeping)", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Created Time", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Created Time", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Modified Time", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Modified Time", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Record ID", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Record ID", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["id"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update"}, "credentials": {"airtableTokenApi": {"id": "YzrajURFsZkojT3x", "name": "Delete Me Later Please!"}}, "typeVersion": 2.1}, {"id": "e6d9834b-a03e-4e98-9e38-a774eec2bc93", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [291, -300], "parameters": {"color": 2, "width": 150, "height": 640, "content": "# Update Post Status\n\n*note I didn't finish attaching all the platforms, left the labor to y'all :)"}, "typeVersion": 1}, {"id": "fed0373f-7fa5-469c-9972-47d0a9a447a7", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-1600, 880], "parameters": {}, "typeVersion": 1}, {"id": "c68f2436-f471-45a6-9292-2410937bd444", "name": "Airtable Record ID", "type": "n8n-nodes-base.set", "position": [-1380, 880], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "ca998655-fcdd-4169-b470-492cf5113b6a", "name": "=airtableID", "type": "string", "value": "=recJBpGmgd7nuLpfe"}]}}, "typeVersion": 3.4}, {"id": "7617f48c-f498-4458-a64c-3fb60938546e", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-972, 780], "parameters": {"color": 5, "width": 320, "height": 260, "content": "### May Not <PERSON>\n\nI added this because my incoming Titles were over the 100 character limit"}, "typeVersion": 1}, {"id": "054bc64d-3a3c-4ba4-9bcc-e30729bb9c87", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-2260, 300], "parameters": {"width": 580, "height": 1880, "content": "# How to Add Example Table and Connect n8n to Airtable\n\n---\n\n## Part 1: Add the Example Table to Airtable\n\n1. **Create and Log into Your Airtable Account**  \n   - If you don't have an Airtable account: [Sign up here (Affiliate link)](https://airtable.com/invite/r/6UyZyAAd)\n\n2. **Open the Example Base**  \n   - Link: [Social Media System Base](https://airtable.com/appbOSIspSmMfeJeg/shr7htmWB9GNRrpw3)\n\n3. **Copy the Base**  \n   - To the right of the title *\"Social Media System\"*, click **\"Copy base\"**.\n\n4. **Choose Your Workspace**  \n   - Pick the workspace to copy the base into, then click **\"Add base\"**.\n\n**✅ Congrats! You now have the example Base added.**\n\n---\n\n## Part 2: Connect n8n to Airtable\n\n### Step A: Create a Personal Access Token in Airtable\n\n1. **Create and Log into Your Airtable Account**  \n   - [Sign up here (Affiliate link)](https://airtable.com/invite/r/6UyZyAAd)\n\n2. **Access Personal Tokens**\n   - Top right: click your **Account Icon** → select **\"Builder hub\"**.\n   - Left navigation: go to **\"Developers\"** → click **\"Personal access tokens\"**.\n\n3. **Create a New Token**\n   - Click **\"Create token\"**.\n   - Name your token (example: *\"Airtable personal access token for n8n\"*).  \n     **(Don't create yet!)**\n\n4. **Set Scopes**\n   - Click **\"+ Add a scope\"** and enable these scopes:\n     - `data.records:read`\n     - `data.records:write`\n     - `schema.bases:read`\n\n5. **Optional: Restrict Access**\n   - If you want the credential limited to certain bases:\n     - Under **Access**, click **\"+ Add a base\"** and select the Base(s).\n\n6. **Finalize and Save the Token**\n   - After creation, a pop-up will show your token **only once**.\n   - **Copy and store it safely!**\n\n---\n\n### Step B: Add Airtable Credentials in n8n\n\n1. **Create and Log into Your n8n Account**  \n   - [Sign up here (Affiliate link)](https://n8n.partnerlinks.io/aiwithapex)\n\n2. **Create a New Credential**\n   - Top right: next to the red-orange **\"Create Workflow\"** button, open the dropdown → select **\"Create Credential\"**.\n   - (Alternatively, you can create it from inside any Airtable node.)\n\n3. **Input Token Details**\n   - In the popup, type **\"Airtable personal access token api\"**, click **\"Continue\"**.\n   - Paste your **saved Airtable token**.\n\n4. **Name the Credential Properly**\n   - Top left of the dialogue box: rename the token to something clearly recognizable.\n\n5. **Save and Test Connection**\n   - Click the top right **\"Save\"** button.\n   - You should see **\"Connection tested successfully\"**.\n   - You may now **close** the dialogue box.\n\n**✅ Done! n8n is now connected to your Airtable base.**\n"}, "typeVersion": 1}, {"id": "f10bfe19-511e-4211-9d53-b72130bcf0ec", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-1660, 300], "parameters": {"width": 1300, "height": 100, "content": "# Blotato Affiliate Link, Please Support My Work:  https://blotato.com/?ref=max\nYou will need the API key for blotato-api-key"}, "typeVersion": 1}, {"id": "825d529c-813c-42d3-b100-0dba4a84d7fe", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-644, 420], "parameters": {"color": 5, "width": 260, "height": 620, "content": "## FILL ME IN!\n\n### Use Link Above to Log into Blotato\n\n- Bottom Left Gear for Settings\n- **IMPORTANT** Log into each social media platform you want to connect before using the connection buttons and do NOT use the \"connect all pages\" option.\n- Log into each account and copy each \"Account ID\" into a safe place\n- If using FaceBook, copy also the 'Page ID'\n- If using Pinterest, use my PINTEREST BOARD ID SYSTEM (tm) to get your Board ID"}, "typeVersion": 1}, {"id": "b27f7a30-aa19-4d0f-a7a0-7e5d5c0a1a2d", "name": "Pinterest System (tm)", "type": "n8n-nodes-base.formTrigger", "position": [-1600, 1940], "webhookId": "0724d1b9-05f6-46c2-9b74-e89fd44cbef3", "parameters": {"options": {}, "formTitle": "Pinterest System (tm)", "formFields": {"values": [{"fieldLabel": "Pinterest Board URL", "placeholder": "https://www.pinterest.com/USERNAME/BOARD_NAME/", "requiredField": true}]}, "formDescription": "Put in your Pinterest Board Link here, it should look like this:\n\nhttps://www.pinterest.com/USERNAME/BOARD_NAME/\n\nExample:\nhttps://www.pinterest.com/aiwithapex/artificial-intelligence-ai-ai-automation/"}, "typeVersion": 2.2}, {"id": "3a5976ba-fb7d-4e33-a0bd-efbbf212031b", "name": "Grab Pinterest Board Page", "type": "n8n-nodes-base.httpRequest", "position": [-1380, 1940], "parameters": {"url": "={{ $json['Pinterest Board URL'] }}", "options": {}, "jsonHeaders": "{\n  \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\n  \"Accept\": \"text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8\",\n  \"Accept-Language\": \"en-US,en;q=0.9\",\n  \"Accept-Encoding\": \"gzip, deflate, br\",\n  \"Connection\": \"keep-alive\",\n  \"Referer\": \"https://www.pinterest.com/\"\n}", "sendHeaders": true, "specifyHeaders": "json"}, "typeVersion": 4.2}, {"id": "659bb65c-7aaf-4aa4-958b-0f8e5bafaf7e", "name": "Pinterest Page Sleuth", "type": "n8n-nodes-base.code", "position": [-1160, 1940], "parameters": {"jsCode": "// n8n Code Node JavaScript\n\n// Get the first item OBJECT using $input.first() or $items[0]\n// Let's use $input.first() as it's slightly more modern n8n syntax\nconst item = $input.first();\n\n// Check if an item was actually received\nif (!item) {\n  console.error(\"No input item received.\");\n  // Return an empty array as expected by n8n if no input\n  return [];\n}\n\n// Check if the 'json' property exists on the item object\n// (This check might be redundant if the previous node always outputs JSON, but good practice)\nif (!item.json || typeof item.json !== 'object') {\n  console.error(\"Input item does not have a 'json' object property.\");\n  // Ensure item.json exists before adding error to it\n  item.json = item.json || {};\n  item.json.error = \"Input item does not have a 'json' object property.\";\n  // Return the item INSIDE an array\n  return [item];\n}\n\n// Check if 'data' exists within 'json'\n// <<< VERIFY this path 'item.json.data' using the INPUT panel in n8n editor >>>\nif (!item.json.hasOwnProperty('data')) {\n   console.error(\"Input item's 'json' property does not have a 'data' key.\");\n   item.json.error = \"Input item's 'json' property does not have a 'data' key.\";\n   // Return the item INSIDE an array\n   return [item];\n}\n\n// Assign the HTML string (adjust path if needed based on INPUT panel)\nconst htmlString = item.json.data;\n\n// Check if HTML string exists and is a string type\nif (typeof htmlString !== 'string') {\n  console.error(\"item.json.data is not a string.\");\n  item.json.error = \"item.json.data exists but is not a string.\";\n  // Return the item INSIDE an array\n  return [item];\n}\n\n// Check if HTML string is empty\nif (!htmlString) {\n   console.error(\"item.json.data is an empty string or null/undefined.\");\n   item.json.error = \"item.json.data is empty or null.\";\n   // Return the item INSIDE an array\n   return [item];\n}\n// </ END OF INPUT CHECKING >\n\nlet extractedBoardInfo = {};\nlet processingError = null; // Renamed to avoid conflict with built-in 'error'\n\ntry {\n  // 1. Find the JSON within the specific script tag using regex\n  const regex = /<script id=\"__PWS_INITIAL_PROPS__\" type=\"application\\/json\">(.*?)<\\/script>/s;\n  const match = htmlString.match(regex);\n\n  if (match && match[1]) {\n    const jsonString = match[1];\n    // 2. Parse the extracted JSON string\n    const parsedData = JSON.parse(jsonString);\n\n    // 3. Navigate through the nested structure\n    const boardsData = parsedData?.initialReduxState?.boards;\n\n    if (boardsData && typeof boardsData === 'object' && Object.keys(boardsData).length > 0) {\n      const boardId = Object.keys(boardsData)[0];\n      const boardDetails = boardsData[boardId];\n\n      if (boardDetails && boardDetails.id) {\n        // 4. Extract the desired information\n        extractedBoardInfo = {\n          boardId: boardDetails.id,\n          name: boardDetails.name || null,\n          description: boardDetails.description || null,\n          url: boardDetails.url || null,\n          privacy: boardDetails.privacy || null,\n          pinCount: boardDetails.pin_count !== undefined ? boardDetails.pin_count : null,\n          followerCount: boardDetails.follower_count !== undefined ? boardDetails.follower_count : null,\n          createdAt: boardDetails.created_at || null,\n          ownerUsername: boardDetails.owner?.username || null,\n          ownerId: boardDetails.owner?.id || null,\n          ownerFullName: boardDetails.owner?.full_name || null,\n        };\n      } else {\n         processingError = \"Board data structure invalid within __PWS_INITIAL_PROPS__ JSON.\";\n         console.error(processingError);\n      }\n    } else {\n      processingError = \"Boards data not found or empty in __PWS_INITIAL_PROPS__ JSON.\";\n      console.error(processingError);\n    }\n  } else {\n    processingError = \"Script tag with id='__PWS_INITIAL_PROPS__' not found or empty in HTML.\";\n    console.error(processingError);\n  }\n} catch (e) {\n  processingError = `Error processing HTML/JSON: ${e.message}`;\n  console.error(processingError, e);\n}\n\n// 5. Prepare the final item\nif (Object.keys(extractedBoardInfo).length > 0) {\n  item.json.extractedBoardInfo = extractedBoardInfo;\n  // Optionally delete the large HTML string if no longer needed\n  // delete item.json.data;\n} else {\n  // Add error information if extraction failed\n  item.json.error = processingError || \"Failed to extract board information for unknown reasons.\";\n  item.json.extractedBoardInfo = null;\n}\n\n// Return the modified item INSIDE an array as required by n8n\nreturn [item];"}, "typeVersion": 2}, {"id": "09f00540-3fde-45a8-a20b-7415e81aa777", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [-1660, 1800], "parameters": {"color": 4, "width": 660, "height": 300, "content": "# Pinterest Page Sleuth\n - Use either testing or active URL respectively depending if your workflow is active or not\n  - Simply paste your board's link and fetch ID!"}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "4b3ebaa1-ec81-4de8-9366-633594a1b0ca", "connections": {"Airtable": {"main": [[{"node": "Ensure Valid YouTube Title", "type": "main", "index": 0}]]}, "Airtable Record ID": {"main": [[{"node": "Airtable", "type": "main", "index": 0}]]}, "Prepare for Publish": {"main": [[{"node": "Upload Image to Blotato", "type": "main", "index": 0}]]}, "Pinterest System (tm)": {"main": [[{"node": "Grab Pinterest Board Page", "type": "main", "index": 0}]]}, "Upload Image to Blotato": {"main": [[{"node": "Upload Video to Blotato", "type": "main", "index": 0}]]}, "Upload Video to Blotato": {"main": [[{"node": "[Instagram] Publish via Blotato", "type": "main", "index": 0}, {"node": "[Facebook] Publish via Blotato", "type": "main", "index": 0}, {"node": "[Linkedin] Publish via Blotato", "type": "main", "index": 0}, {"node": "[Tiktok] Publish via Blotato", "type": "main", "index": 0}, {"node": "[Youtube] Publish via Blotato", "type": "main", "index": 0}, {"node": "[Threads] Publish via Blotato", "type": "main", "index": 0}, {"node": "[Twitter] Publish via Blotato", "type": "main", "index": 0}, {"node": "[<PERSON><PERSON>] Publish via Blotato", "type": "main", "index": 0}, {"node": "[Pinterest] Publish via Blotato", "type": "main", "index": 0}]]}, "Grab Pinterest Board Page": {"main": [[{"node": "Pinterest Page Sleuth", "type": "main", "index": 0}]]}, "Ensure Valid YouTube Title": {"main": [[{"node": "Prepare for Publish", "type": "main", "index": 0}]]}, "[Instagram] Publish via Blotato": {"main": [[{"node": "Airtable: Posted Instagram", "type": "main", "index": 0}], [{"node": "Airtable: Posted Instagram1", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Airtable Record ID", "type": "main", "index": 0}]]}}}