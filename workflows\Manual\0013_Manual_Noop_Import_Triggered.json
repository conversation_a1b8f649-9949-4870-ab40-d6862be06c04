{"id": "1028", "name": "Loading data into a spreadsheet", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [160, 300], "parameters": {}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [650, 300], "parameters": {"values": {"string": [{"name": "Name", "value": "={{$json[\"properties\"][\"firstname\"][\"value\"]}} {{$json[\"properties\"][\"lastname\"][\"value\"]}}"}, {"name": "Email", "value": "={{$json[\"identity-profiles\"][0][\"identities\"][0][\"value\"]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "Mock data (CRM Contacts)", "type": "n8n-nodes-base.function", "notes": "\"Get contacts\" data from Hubspot node. ", "position": [400, 300], "parameters": {"functionCode": "var newItems = [];\nnewItems.push({json:{\n    \"addedAt\": 1606827045601,\n    \"vid\": 1,\n    \"canonical-vid\": 1,\n    \"merged-vids\": [],\n    \"portal-id\": 8924380,\n    \"is-contact\": true,\n    \"profile-token\": \"AO_T-mMZqmgHPI5CLLlw2qE24AlgWOJUL0LdMb2CegxeMzQK1LXyh7iZAgjNd-00ZdPAfnFU9Lv_7nq6qlrKvfAh8hr_cw-VBH1RCCMgHHYQ06DOXoIGAlViWmMKY-0lF9dv7lBVOMf5\",\n    \"profile-url\": \"https://app.hubspot.com/contacts/8924380/contact/1\",\n    \"properties\": {\n      \"firstname\": {\n        \"value\": \"Maria\"\n      },\n      \"lastmodifieddate\": {\n        \"value\": \"1606827057310\"\n      },\n      \"company\": {\n        \"value\": \"HubSpot\"\n      },\n      \"lastname\": {\n        \"value\": \"<PERSON> (Sample Contact)\"\n      }\n    },\n    \"form-submissions\": [],\n    \"identity-profiles\": [\n      {\n        \"vid\": 1,\n        \"saved-at-timestamp\": 1606827045478,\n        \"deleted-changed-timestamp\": 0,\n        \"identities\": [\n          {\n            \"type\": \"EMAIL\",\n            \"value\": \"<EMAIL>\",\n            \"timestamp\": 1606827045444,\n            \"is-primary\": true\n          },\n          {\n            \"type\": \"LEAD_GUID\",\n            \"value\": \"cfa8b21f-164e-4c9a-aab1-1235c81a7d26\",\n            \"timestamp\": 1606827045475\n          }\n        ]\n      }\n    ],\n    \"merge-audits\": []\n  }});\nnewItems.push({json:{\n    \"addedAt\": 1606827045834,\n    \"vid\": 51,\n    \"canonical-vid\": 51,\n    \"merged-vids\": [],\n    \"portal-id\": 8924380,\n    \"is-contact\": true,\n    \"profile-token\": \"AO_T-mMX1jbZjaachMJ8t1F2yRdvyAvsir5RMvooW7XjbPZTdAv8hc24U0Rnc_PDF1gp1qmc8Tg2hDytOaRXRiWVyg-Eg8rbPFEiXNdU6jfMneow46tsSiQH1yyRf03mMi5ALZXMVfyA\",\n    \"profile-url\": \"https://app.hubspot.com/contacts/8924380/contact/51\",\n    \"properties\": {\n      \"firstname\": {\n        \"value\": \"Brian\"\n      },\n      \"lastmodifieddate\": {\n        \"value\": \"1606827060106\"\n      },\n      \"company\": {\n        \"value\": \"HubSpot\"\n      },\n      \"lastname\": {\n        \"value\": \"Halligan (Sample Contact)\"\n      }\n    },\n    \"form-submissions\": [],\n    \"identity-profiles\": [\n      {\n        \"vid\": 51,\n        \"saved-at-timestamp\": 1606827045720,\n        \"deleted-changed-timestamp\": 0,\n        \"identities\": [\n          {\n            \"type\": \"EMAIL\",\n            \"value\": \"<EMAIL>\",\n            \"timestamp\": 1606827045444,\n            \"is-primary\": true\n          },\n          {\n            \"type\": \"LEAD_GUID\",\n            \"value\": \"d3749acc-06e1-4511-84fd-7b0d847f6eff\",\n            \"timestamp\": 1606827045717\n          }\n        ]\n      }\n    ],\n    \"merge-audits\": []\n  } });\nreturn newItems;"}, "notesInFlow": true, "typeVersion": 1}, {"name": "Replace me", "type": "n8n-nodes-base.noOp", "notes": "Google Sheet/ Airtable/ Database with an \"append\" or \"Add row\" operation", "position": [910, 300], "parameters": {}, "notesInFlow": true, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Set": {"main": [[{"node": "Replace me", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Mock data (CRM Contacts)", "type": "main", "index": 0}]]}, "Mock data (CRM Contacts)": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}}