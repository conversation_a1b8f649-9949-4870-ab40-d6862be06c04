{"nodes": [{"id": "d45cf237-dbbc-48ed-a7f0-fa9506ae1d67", "name": "Update priority in todoist", "type": "n8n-nodes-base.todoist", "position": [2060, 520], "parameters": {"taskId": "={{ $('Get inbox tasks').item.json.id }}", "operation": "update", "updateFields": {"priority": "={{ $('Your Projects').first().json.projects[$json.message.content] }}"}}, "credentials": {"todoistApi": {"id": "1", "name": "Todoist account"}}, "retryOnFail": true, "typeVersion": 2, "waitBetweenTries": 5000}, {"id": "4d0ebf98-5a1d-4dfd-85df-da182b3c5099", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [600, 520], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.2}, {"id": "a950e470-6885-42f4-9b17-7b2c2525d3e4", "name": "Get inbox tasks", "type": "n8n-nodes-base.todoist", "position": [1020, 520], "parameters": {"filters": {"projectId": "*********"}, "operation": "getAll", "returnAll": true}, "credentials": {"todoistApi": {"id": "1", "name": "Todoist account"}}, "retryOnFail": true, "typeVersion": 2, "waitBetweenTries": 5000}, {"id": "093bcb2e-79b7-427e-b13d-540a5b28f427", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [540, 200], "parameters": {"color": 3, "width": 358.*************, "height": 256.*************, "content": "## 💫 To setup this template\n\n1. Add your Todoist credentials\n2. Add your OpenAI credentials\n3. Set your project names and add priority"}, "typeVersion": 1}, {"id": "430290e7-1732-46fe-a38d-fa6dc7f78a26", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [800, 700], "parameters": {"width": 192.**************, "height": 80, "content": " 👆🏽 Add your projects and priority here"}, "typeVersion": 1}, {"id": "6d5a1b7e-f7fa-4a1b-848c-1b4e79f6f667", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1020, 420], "parameters": {"width": 192.**************, "height": 80, "content": " 👇🏽 Add your Todoist credentials here"}, "typeVersion": 1}, {"id": "feff35d2-e37d-48a5-9a90-c5a2efde688f", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [2060, 420], "parameters": {"width": 192.**************, "height": 80, "content": " 👇🏽 Add your Todoist credentials here"}, "typeVersion": 1}, {"id": "e454ebfe-47f6-4e39-8b89-d706da742911", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1540, 700], "parameters": {"width": 192.**************, "height": 80, "content": " 👆🏽 Add your OpenAI credentials here"}, "typeVersion": 1}, {"id": "a79effcb-6904-4abf-835b-e1ccd94ca429", "name": "Your Projects", "type": "n8n-nodes-base.set", "position": [820, 520], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "50dc1412-21f8-4158-898d-3940a146586b", "name": "projects", "type": "object", "value": "={{ {\n  apartment: 1,\n  health: 2,\n  german: 3\n} }}"}]}}, "typeVersion": 3.4}, {"id": "b5988629-2225-455f-b579-73e60449d2a3", "name": "Categorize", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1460, 520], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "messages": {"values": [{"role": "system", "content": "=Categorize the user's todo item to a project. Return the project name or just \"other\" if it does not belong to a project."}, {"content": "=Projects:\n{{ $('Your Projects').first().json.projects.keys().join('\\n') }}\n\nTodo item:\n{{ $('Get inbox tasks').item.json.content }}"}]}}, "credentials": {"openAiApi": {"id": "9", "name": "n8n OpenAi"}}, "typeVersion": 1.4}, {"id": "0dca3953-c0ac-4319-9323-c3aed9488bfb", "name": "If task is not a subtask", "type": "n8n-nodes-base.filter", "position": [1240, 520], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "36dd4bc9-1282-4342-89dd-1dac81c7290e", "operator": {"type": "string", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json.parent_id }}", "rightValue": ""}]}}, "typeVersion": 2.1}, {"id": "12e25a81-dbde-4542-a137-365329da415e", "name": "If other or ai hallucinates", "type": "n8n-nodes-base.filter", "position": [1820, 520], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "c4f69265-abe1-451c-8462-e68ff3b06799", "operator": {"type": "array", "operation": "contains", "rightType": "any"}, "leftValue": "={{ $('Your Projects').first().json.projects.keys() }}", "rightValue": "={{ $json.message.content }}"}]}}, "typeVersion": 2.1}], "pinData": {}, "connections": {"Categorize": {"main": [[{"node": "If other or ai hallucinates", "type": "main", "index": 0}]]}, "Your Projects": {"main": [[{"node": "Get inbox tasks", "type": "main", "index": 0}]]}, "Get inbox tasks": {"main": [[{"node": "If task is not a subtask", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Your Projects", "type": "main", "index": 0}]]}, "If task is not a subtask": {"main": [[{"node": "Categorize", "type": "main", "index": 0}]]}, "If other or ai hallucinates": {"main": [[{"node": "Update priority in todoist", "type": "main", "index": 0}]]}}}