{"meta": {"instanceId": "be27b2af86ae3a5dc19ef2a1947644c0aec45fd8c88f29daa7dea6f0ce537691"}, "nodes": [{"id": "11abe711-000c-4960-9f07-4e124532ba83", "name": "create_folder", "type": "n8n-nodes-base.googleDrive", "position": [20, 440], "parameters": {"name": "={{ $('topic_variables').item.json.Title }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "root", "cachedResultUrl": "https://drive.google.com/drive", "cachedResultName": "/ (Root folder)"}, "resource": "folder"}, "credentials": {"googleDriveOAuth2Api": {"id": "MHcgKR744VHXSe3X", "name": "Drive n8n"}}, "typeVersion": 3}, {"id": "8198bcdb-3082-43d8-84aa-77e292b56e05", "name": "input_brief", "type": "n8n-nodes-base.set", "position": [1040, 440], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "eff28505-c438-4c44-8db4-188797b1e5f3", "name": "content", "type": "string", "value": "={{ $('create_outline').item.json.message.content }}"}]}}, "typeVersion": 3.4}, {"id": "9b2be845-91c5-4fa8-9007-c0cee4058ddd", "name": "new_lines", "type": "n8n-nodes-base.set", "position": [1260, 440], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "dda6ee09-0629-4ebc-a4cf-80ebe0172dee", "name": "content", "type": "array", "value": "={{ $json.content.split(/(?:\\r?\\n){2}## /) }}"}]}}, "typeVersion": 3.4}, {"id": "e5228041-32e7-4834-9d87-6b7152bf97e3", "name": "input_sections", "type": "n8n-nodes-base.set", "position": [1980, 480], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "4b2dbae1-2e78-46f4-8be7-6240abf5c1d6", "name": "content", "type": "string", "value": "={{ $json.content.replace($json.content,$json.content+\"⟵\") }}"}]}}, "typeVersion": 3.4}, {"id": "4b7020ae-d38e-437c-871e-02f78012f691", "name": "section_text", "type": "n8n-nodes-base.set", "position": [2540, 480], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "1bc2b4fc-7cc9-4aea-b733-5d062b3ee648", "name": "message", "type": "string", "value": "={{ $json.message.content }}"}, {"id": "3f599644-8c86-46c6-8048-1166cced462a", "name": "idx", "type": "number", "value": "={{ $('section_paragraphs').item.pairedItem.item }}"}]}}, "typeVersion": 3.4}, {"id": "599af95b-391c-4d57-868d-0db6eaa39da1", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [3660, 480], "parameters": {}, "typeVersion": 3}, {"id": "0aa60c0b-0537-4539-8312-0be3cfa6c4de", "name": "Sort", "type": "n8n-nodes-base.sort", "position": [3880, 480], "parameters": {"options": {}, "sortFieldsUi": {"sortField": [{"fieldName": "idx"}]}}, "typeVersion": 1}, {"id": "f19ef511-bf86-4c4c-9adf-731704bf64ae", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "position": [4500, 360], "parameters": {"options": {"mergeLists": true}, "fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "message"}]}}, "typeVersion": 1}, {"id": "568fd895-fce6-4af8-89de-26e51ae5a66d", "name": "final_article", "type": "n8n-nodes-base.set", "position": [4700, 360], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "f410b139-0e21-41ed-9848-260e4bf7cf33", "name": "article", "type": "string", "value": "={{ $json.message.join() }}"}]}}, "typeVersion": 3.4}, {"id": "771f197a-02e1-4809-9505-e1a3900581f0", "name": "set_introduction", "type": "n8n-nodes-base.set", "position": [1980, 300], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "21f3dd4b-db14-472b-94b6-7165206f94e7", "name": "message", "type": "string", "value": "={{ $json.content+\"\\n\\n\" }}"}]}}, "typeVersion": 3.4}, {"id": "3ad780e5-1dcd-43f0-816c-e6f2608461d5", "name": "Merge1", "type": "n8n-nodes-base.merge", "position": [4280, 320], "parameters": {}, "typeVersion": 3}, {"id": "1902e5e3-10c2-49e8-8da1-9d1cd6ae681c", "name": "receive_topic", "type": "n8n-nodes-base.formTrigger", "position": [-580, 440], "webhookId": "578f48e7-78a0-4450-b301-a66ca5fe822d", "parameters": {"path": "generator", "options": {"respondWithOptions": {"values": {"formSubmittedText": "={{ \"Nice work! Your content is generating.\".bold()}} Allow 3-5 minutes for your finished article."}}}, "formTitle": "Content Genrator", "formFields": {"values": [{"fieldLabel": "What is the title of the content?", "requiredField": true}, {"fieldType": "number", "fieldLabel": "How many words should the content have?", "requiredField": true}, {"fieldLabel": "What is the primary keyword for the content?", "requiredField": true}, {"fieldLabel": "What are the secondary keywords for the content?"}, {"fieldLabel": "Are there any internal links that should be included in the content?", "placeholder": "If so, list here. Including multiple? Separate using commas (link1.com, link2.com)"}, {"fieldLabel": "Are there any external links that should be included in the content?", "placeholder": "If so, list here. Including multiple? Separate using commas (link1.com, link2.com)"}, {"fieldLabel": "Additional instructions or specific requirements for the content."}]}}, "typeVersion": 2.1}, {"id": "af8d14aa-d095-4bce-a525-8427e0f450e2", "name": "add_row", "type": "n8n-nodes-base.googleSheets", "position": [-380, 440], "parameters": {"columns": {"value": {"Title": "={{ $json['What is the title of the content?'] }}", "Word Count": "={{ $json['How many words should the content have?'] }}", "External Links": "={{ $json['Are there any external links that should be included in the content?'] }}", "Internal Links": "={{ $json['Are there any internal links that should be included in the content?'] }}", "Primary Keyword": "={{ $json['What is the primary keyword for the content?'] }}", "Secondary Keyword(s)": "={{ $json['What are the secondary keywords for the content?'] }}", "Additional Instructions": "={{ $json['Additional instructions or specific requirements for the content.'] }}"}, "schema": [{"id": "Title", "type": "string", "display": true, "required": false, "displayName": "Title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Word Count", "type": "string", "display": true, "required": false, "displayName": "Word Count", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Primary Keyword", "type": "string", "display": true, "required": false, "displayName": "Primary Keyword", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Secondary Keyword(s)", "type": "string", "display": true, "required": false, "displayName": "Secondary Keyword(s)", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Internal Links", "type": "string", "display": true, "required": false, "displayName": "Internal Links", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "External Links", "type": "string", "display": true, "required": false, "displayName": "External Links", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Additional Instructions", "type": "string", "display": true, "required": false, "displayName": "Additional Instructions", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Outline Doc", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Outline Doc", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Article Doc", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Article Doc", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Website URL", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Website URL", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": []}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1qslCOQCBepqvixsp2RzILDxBlME5siXJRLFF8yC9jlc/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1qslCOQCBepqvixsp2RzILDxBlME5siXJRLFF8yC9jlc", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1qslCOQCBepqvixsp2RzILDxBlME5siXJRLFF8yC9jlc/edit?usp=drivesdk", "cachedResultName": " Generator"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "Epe0euL6qKcOzKeG", "name": "google"}}, "typeVersion": 4.5}, {"id": "3f240ed3-9eff-4ae1-91ec-689a96f1c97e", "name": "topic_variables", "type": "n8n-nodes-base.set", "position": [-200, 440], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "dae56384-1e23-46c7-923f-7635d45eaa35", "name": "Title", "type": "string", "value": "={{ $('receive_topic').item.json['What is the title of the content?'] }}"}, {"id": "2c0ac2a3-6b45-4b63-b9f6-c3d51d064203", "name": "Word Count", "type": "number", "value": "={{ $('receive_topic').item.json['How many words should the content have?'] }}"}, {"id": "c05d869d-098e-442a-ab8b-21e6feea5987", "name": "Primary Keyword", "type": "string", "value": "={{ $('receive_topic').item.json['What is the primary keyword for the content?'] }}"}, {"id": "133a25e4-8f18-44c3-b743-9dee224688e3", "name": "Secondary Keywords", "type": "array", "value": "={{ $if($('receive_topic').item.json['What are the secondary keywords for the content?'].includes(','),$('receive_topic').item.json['What are the secondary keywords for the content?'].split(','),$('receive_topic').item.json['What are the secondary keywords for the content?']) }}"}, {"id": "9d77b794-445d-4613-aa04-01ebe004f454", "name": "Internal Links", "type": "array", "value": "={{ $if($('receive_topic').item.json['Are there any external links that should be included in the content?'].includes(','),$('receive_topic').item.json['Are there any internal links that should be included in the content?'].split(','),$('receive_topic').item.json['Are there any internal links that should be included in the content?']) }}"}, {"id": "24e92ba2-2448-40b6-af62-749351ff1483", "name": "External Links", "type": "array", "value": "={{ $if($('receive_topic').item.json['Are there any external links that should be included in the content?'].includes(','),$('receive_topic').item.json['Are there any external links that should be included in the content?'].split(','),$('receive_topic').item.json['Are there any external links that should be included in the content?']) }}"}, {"id": "7466794b-7994-4d54-a72e-feab7d383556", "name": "Additional Instructions", "type": "string", "value": "={{ $('receive_topic').item.json['Additional instructions or specific requirements for the content.'] }}"}]}}, "typeVersion": 3.4}, {"id": "d4715acb-0f04-45e2-a476-32cec57a840a", "name": "markdown_to_file", "type": "n8n-nodes-base.convertToFile", "position": [600, 440], "parameters": {"options": {}, "operation": "toText", "sourceProperty": "message.content"}, "typeVersion": 1.1}, {"id": "58db0daf-09cb-420d-a0a0-1fac4f2d97ea", "name": "split_out", "type": "n8n-nodes-base.splitOut", "position": [1480, 440], "parameters": {"options": {}, "fieldToSplitOut": "content"}, "typeVersion": 1}, {"id": "02ed3e37-038e-497d-83af-e8f720b3811d", "name": "section_starts_with_#", "type": "n8n-nodes-base.if", "position": [1700, 440], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "3a8dc0bb-2bcf-416d-b28e-360a1173042c", "operator": {"type": "string", "operation": "startsWith"}, "leftValue": "={{ $json.content }}", "rightValue": "#"}]}}, "typeVersion": 2.2}, {"id": "b3b0138a-ca44-411e-8ecd-7a951fe22919", "name": "25_percent_chance", "type": "n8n-nodes-base.if", "position": [2760, 480], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "71cd7f94-f30d-4eb5-8c31-a0674ef3ffc9", "operator": {"type": "number", "operation": "equals"}, "leftValue": "={{ Math.ceil($('section_paragraphs').all().length * 0.25) }}", "rightValue": "={{ $json.idx }}"}]}}, "typeVersion": 2.2}, {"id": "70015edc-babc-4123-9589-ac4c70345fa7", "name": "set_section_content", "type": "n8n-nodes-base.set", "position": [3400, 420], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "071e977f-6534-4635-a2a5-9178709bdfc9", "name": "message", "type": "string", "value": "={{ $json.message.content }}"}, {"id": "b1678132-2e98-4fc2-b303-7e89083c287e", "name": "=idx", "type": "number", "value": "={{ Math.ceil($('section_paragraphs').all().length * 0.25) }}"}]}}, "typeVersion": 3.4}, {"id": "e413900a-dc61-4150-93ac-308032ec4aed", "name": "add_2_new_lines", "type": "n8n-nodes-base.code", "position": [4100, 480], "parameters": {"jsCode": "// Create an array to hold the rearranged items\nconst rearrangedItems = [];\n\n// Loop over input items and push each message into the rearrangedItems array\nfor (const item of $input.all()) {\n  rearrangedItems[item.json.idx] = {\n    json: {\n      message: item.json.message + '\\n\\n' // Add two new lines at the end of each message\n    }\n  };\n}\n\n// Return the rearranged items\nreturn rearrangedItems.filter(Boolean); // Filter out any undefined entries\n"}, "typeVersion": 2}, {"id": "25110a53-8627-4598-bc11-f2217856e10d", "name": "final_article_file", "type": "n8n-nodes-base.convertToFile", "position": [4940, 360], "parameters": {"options": {}, "operation": "toText", "sourceProperty": "article", "binaryPropertyName": "final_article"}, "typeVersion": 1.1}, {"id": "0122e867-252d-4e54-8576-5bd3ddc7a464", "name": "upload_fiinalArticle", "type": "n8n-nodes-base.googleDrive", "position": [5160, 360], "parameters": {"name": "={{ $('topic_variables').item.json['Primary Keyword'] }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "id", "value": "={{ $('create_folder').item.json.id }}"}}, "credentials": {"googleDriveOAuth2Api": {"id": "MHcgKR744VHXSe3X", "name": "Drive n8n"}}, "typeVersion": 3}, {"id": "e4fa62e3-6c05-4e99-a53e-ede197e2ca02", "name": "update_article_link", "type": "n8n-nodes-base.googleSheets", "position": [5380, 360], "parameters": {"columns": {"value": {"Title": "={{ $('add_row').item.json.Title }}", "Article Doc": "={{ $('upload_fiinalArticle').item.json.webViewLink }}", "Outline Doc": "={{ $('upload_outline_file').item.json.webViewLink }}"}, "schema": [{"id": "Title", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Word Count", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Word Count", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Primary Keyword", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Primary Keyword", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Secondary Keyword(s)", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Secondary Keyword(s)", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Internal Links", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Internal Links", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "External Links", "type": "string", "display": true, "removed": true, "required": false, "displayName": "External Links", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Additional Instructions", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Additional Instructions", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Outline Doc", "type": "string", "display": true, "required": false, "displayName": "Outline Doc", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Article Doc", "type": "string", "display": true, "required": false, "displayName": "Article Doc", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Website URL", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Website URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["Title"]}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1qslCOQCBepqvixsp2RzILDxBlME5siXJRLFF8yC9jlc/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1qslCOQCBepqvixsp2RzILDxBlME5siXJRLFF8yC9jlc", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1qslCOQCBepqvixsp2RzILDxBlME5siXJRLFF8yC9jlc/edit?usp=drivesdk", "cachedResultName": " Generator"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "Epe0euL6qKcOzKeG", "name": "google"}}, "typeVersion": 4.5}, {"id": "578cc443-0261-401c-a948-44f69773cfd7", "name": "upload_outline_file", "type": "n8n-nodes-base.googleDrive", "position": [820, 440], "parameters": {"name": "=O: {{ $('topic_variables').item.json['Primary Keyword'] }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "id", "value": "={{ $('create_folder').item.json.id }}"}}, "credentials": {"googleDriveOAuth2Api": {"id": "MHcgKR744VHXSe3X", "name": "Drive n8n"}}, "typeVersion": 3}, {"id": "d1e70bea-ea88-4b02-9516-ea791b569cd8", "name": "section_paragraphs", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [2180, 480], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "messages": {"values": [{"role": "system", "content": "You're a helpful, intelligent writing assistant"}, {"content": "=The following is an outline of an award winning article. Your task is to write one section and one section only: the one marked by a \"⟵\". Tone of voice: 50% spartan, casual.\n\n------\n\n{{ $json.content }}"}]}}, "credentials": {"openAiApi": {"id": "0Q6M4JEKewP9VKl8", "name": "Bulkbox"}}, "typeVersion": 1.5}, {"id": "87cf609d-682d-4e17-a1a4-185908a2411c", "name": "change_section_format", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [3020, 420], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "messages": {"values": [{"role": "system", "content": "You're a helpful, intelligent writing assistant"}, {"content": "=Edit the following text to break up the flow. Add bullet points and subheadings where needed for variety. Use Markdown(atx) format."}, {"content": "=# Making use of AI Writing Tools\n\nIncorporating AI into your writing workflow requires a strong understanding of the available tools and how they can boost your productivity. Whether you're a novel writer looking to streamline your plot development process or a content marketer aiming to optimize your SEO, AI tools can significantly enhance your efficiency. Among the most versatile tools is ChatGPT, designed by OpenAI. It assists with content generation, ideation, automatic formatting, and translation. Thanks to its machine learning capabilities, the quality of the content it helps generate improves over time based on your input and feedback.\n\nFor those primarily concerned with editing and proofreading, tools like Grammarly and ProWritingAid offer high-quality solutions to streamline the QA process. Using natural language processing, these tools instantly assess your writing for grammatical errors, stylistic issues, and clarity improvements. Both Grammarly and ProWritingAid provide real-time suggestions, allowing you to refine your writing quickly and efficiently. They also offer detailed reports to help you understand patterns in your writing and areas for improvement.\n"}, {"role": "assistant", "content": "# Making use of AI Writing Tools\n\nIncorporating AI into your writing workflow requires a strong understanding of the available tools and how they can boost your productivity. Whether you're a novel writer looking to streamline your plot development process or a content marketer aiming to optimize your SEO, below are a few of the hottest AI tools you can use to improve your productivity.\n\n## AI Writing Tools\n\nAmong the most versatile is ChatGPT, designed by OpenAI. This tool is designed to assist with:\n\n- Content generation,\n- Ideation,\n- Automatic formatting, &\n- Translation\n\nThanks to its machine learning capabilities, the quality of the content it helps you generate even improves over time based on your input and feedback.\n\n## AI Editing Tools\n\nFor those primarily concerned with editing and proofreading, on the other hand, here are a few high-quality tools you can use to skip the QA.\n\n- Grammarly,\n- ProWritingAid\n\nUsing natural language processing, these tools instantly assess your writing for grammatical errors, stylistic issues, and clarity improvements. Both Grammarly and ProWritingAid provide suggestions in real-time, allowing you to refine your writing quickly and efficiently. They also offer detailed reports that help you understand patterns in your writing and areas for improvement.\n"}, {"content": "={{ $json.message }}"}]}}, "credentials": {"openAiApi": {"id": "0Q6M4JEKewP9VKl8", "name": "Bulkbox"}}, "typeVersion": 1.5}, {"id": "eecf903b-3d60-4c6e-9972-78803e34682a", "name": "create_outline", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [220, 440], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "messages": {"values": [{"role": "system", "content": "You're a helpful, intelligent writing assistant"}, {"content": "Use the following JSON brief to write an engaging, comprehensive outline in Markdown format (atx). Tone of voice 50% spartan, casual."}, {"content": "={\n\"title\": \"Accessibility in Retail: How to Make Ypur Store More Accessible\",\n\"wordCount\": 1000,\n\"primaryKeword\":\"accessibility in retail\",\n\"secondaryKeywords\":[\"accessible e-commerce\", \"how to make your website more accessible\"],\n\"internalLinks\": [\"https://www.ontario.ca/page/how-make-websites-accessible\"],\n\"externalLinks\": [\"https://www.forbes.com/sites/forbesbusinesscouncil/2023/03/20/understanding-the-importance-of-web-accessibility/\"]\n\"additionalinstructions\": \"Don't pitch the company at all - make it very informative and provide sa uch value as you can.\"\n}"}, {"role": "assistant", "content": "=# Accessibility in Retail: How to Make Your Store More Accessible.\n\nOnline arketplaces have become more popular since the mid-90swjen retail juggernauts like Amazon intially burst onto the scene. Amd while it's true that nearly everybody buyd products in e-commerce store these days, it's crucial to understand that not all consumers have the same needs. \nOver one billion people have diabilities worldwide, meaning up to 15% of the globale population may need assistive technology to use online shopping platforms. Addressing thei needs is critical if you want to maximize your reach & customer experience. \nSo, how exactly does digital accessibility impact businesses, and what can we (practically) do bout it? Read on for answers below.\n\n## Introduction\n- Briefly define what \"accessibility in retail\" means.\n- Highlight the importance of retail accessibility.\n\n## Significance of Accessility in Retail\n- Discuss how creating an accessible retail space can broaden market reach.\n- Discuss some benefits of retail accessibility. \n- Share valuable insights about retail accessibility from [forbes](https://www.forbes.com/sites/forbesbusinesscouncil/2023/03/20/understanding-the-importance-of-web-accessibility/))\n\n## Exploring Accessible E-comerce: The New Normal in Retail\n- Explain the rise and importance of e-commerce in today's retail landscape.\n- Highlight how e-commerce has opened up more possibilities for accessibility in retail.\n\n## Guidance to Make E-commerce Stores Accessible\n- Present some of the key accessibility standards for online stores.\n- Offer brief step-by-step guide on how to make your website accessible.\n- INdicate the best practices in creating accessible e-commerce stores, citing resources from [\"Ontario.ca\"](https://www.ontario.ca/page/how-make-websites-accessible))\n\n## Physical Store Accessibility: Not to be Overlooked\n- Assert the need for physical store accessibility in addition to online stores.\n- Enumerate practical solutions for enhancing physical store accessibility.\n\n## Encouraging Continuous Efforts for Accessibility\n- Inspire readers to continually strive for better accessibility in theire retail environments.\n- Reiterate the benefits of and the need for accessibility in the retail sector for a more inclusive feature.\n\n## Conclusion\n- Sumarixe the key points and lessons learned about retail accessibility. \n- Encourage readers to implement the suggestions provided."}, {"content": "={\n\"title\": \"{{ $('topic_variables').item.json.Title }}\",\n\"wordCount\": {{ $('topic_variables').item.json['Word Count'] }},\n\"primaryKeyword\":\"{{ $('topic_variables').item.json['Primary Keyword'] }}\",\n\"secondaryKeywords\": {{ $('topic_variables').item.json['Secondary Keywords'] }},\n\"internaLinks\": {{ $('topic_variables').item.json['Internal Links'] }},\n\"externalLinks\": {{ $('topic_variables').item.json['External Links'] }},\n\"additionalinstructions\":\"{{ $('topic_variables').item.json['Additional Instructions'] }}\"\n}"}]}}, "credentials": {"openAiApi": {"id": "0Q6M4JEKewP9VKl8", "name": "Bulkbox"}}, "typeVersion": 1.5}, {"id": "202dfc30-15c9-4a60-b281-33a4f0fad97e", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1240, 280], "parameters": {"color": 4, "width": 626.3622313971345, "height": 534.5136001658811, "content": "## Overview\n### This workflow automates the generation of high-quality content using AI and integrates with tools like Google Drive and Google Sheets for content management and organization.\n\n## Key Features:\n- ### **Form-Based Content Input**: Collects user inputs via a form, including title, word count, keywords, and additional instructions.\n- ### **AI-Generated Content Outline**: Creates an outline using AI based on user inputs.\n- ### **Detailed Section Processing**: Each section of the content is refined individually.\n- ### **Content Aggregation**: Combines all sections into a cohesive article.\n- ### **Document Management**:\n      - Saves generated content and outlines to Google Drive.\n      - Updates links to generated content in Google Sheets.\n\n## Prerequisites:\n- ### Google Drive and Google Sheets API: Ensure the respective OAuth2 credentials are configured in n8n.\n- ### OpenAI API Key: Required for AI-powered content generation."}, "typeVersion": 1}, {"id": "397a506f-2a58-4cb1-a181-cf32b2d4a936", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-600, 300], "parameters": {"color": 2, "width": 522.4076473360327, "height": 269.1338026993484, "content": "- ### Captures user inputs like title, word count, keywords, and additional instructions for content generation. This is the starting point of the workflow\n\n- ### Parses form inputs into variables for easy access and consistent use in the workflow"}, "typeVersion": 1}, {"id": "deac2389-35af-4acc-9904-df539fff603f", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [0, 320], "parameters": {"width": 152.8844206522747, "height": 245.20095123019289, "content": "### Creates a folder in Google Drive to store the generated content and outline"}, "typeVersion": 1}, {"id": "371fdd93-437c-456b-95a1-f14d0334595e", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [220, 340], "parameters": {"width": 263.93285146915525, "height": 203.07913264447978, "content": "### Sends user inputs to an AI model to generate a detailed content outline in Markdown format.\"\n"}, "typeVersion": 1}, {"id": "3f9c4cd6-c384-47da-ab90-4e87696da121", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [560, 380], "parameters": {"width": 376.89591040356845, "height": 193.50599205681746, "content": "### Uploads the AI-generated outline to the Google Drive folder created earlier."}, "typeVersion": 1}, {"id": "50ce135a-8fb6-4486-be7c-7ba0d066e6a2", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1040, 380], "parameters": {"width": 540.5966144525913, "height": 159.0426859412338, "content": "### Breaks the AI-generated outline into manageable sections. Each section will be individually processed to ensure clarity, structure, and relevance."}, "typeVersion": 1}, {"id": "5d272c75-a2bc-4847-95bd-2ba13555d7c5", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [2140, 300], "parameters": {"color": 7, "width": 1360.0574487564681, "height": 295.93859634480214, "content": "### Refines each section using AI. Adds formatting, adjusts tone, and enhances readability. Ensures the content meets high-quality standards before merging into a full article."}, "typeVersion": 1}, {"id": "1d8b9e13-1ba0-4298-aeef-9322e610128e", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [3655.31702494026, 280], "parameters": {"color": 7, "width": 723.7577183524706, "height": 299.4686919365027, "content": "### Aligns all sections in the correct order. Prepares the refined content for aggregation into a single, cohesive article"}, "typeVersion": 1}, {"id": "9c6cdb3b-1be2-48d1-b6c1-0e5438816b5b", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [4500, 260], "parameters": {"width": 301.26809976103766, "height": 198.29256235064872, "content": "### Joins all ordered sections into a unified article. Ensures the flow and structure of the final content remain consistent and logical"}, "typeVersion": 1}, {"id": "bfc18c66-7d7b-48b1-9d87-d159b53ca4ec", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [4940, 240], "parameters": {"width": 320.41438093636225, "height": 213.60958729090797, "content": "### Converts the aggregated article into a downloadable text file. Uploads it to the Google Drive folder created earlier, ready for review and sharing."}, "typeVersion": 1}, {"id": "e6378913-abed-41de-91d4-e30cc83fb3cf", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [5340, 200], "parameters": {"width": 192.13429706168935, "height": 255.7314058766213, "content": "### Records the generated outline and article links in a Google Sheets document. This ensures easy access and tracking of the generated content for future use."}, "typeVersion": 1}, {"id": "e6ebb536-2d4f-4c3a-88ab-6145aef59046", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [5580, 300], "parameters": {"color": 4, "width": 570.2733502743432, "height": 280.62157140454303, "content": "## Next Steps: Automate Social Media Posting\n\n- ### Your content creation process is now complete! You can take automation to the next level by integrating this workflow with social media platforms such as Twitter, LinkedIn, or Facebook.\n    - Use the generated article to create tailored posts for each platform.\n    - Add nodes for social media integrations (e.g., Twitter, Facebook, or LinkedIn API).\n    - Schedule posts using a timestamp or trigger them based on specific events.\n\n### This enhancement allows you to seamlessly distribute your content across multiple channels, ensuring a wider reach and saving even more time!"}, "typeVersion": 1}], "pinData": {}, "connections": {"Sort": {"main": [[{"node": "add_2_new_lines", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "Sort", "type": "main", "index": 0}]]}, "Merge1": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "add_row": {"main": [[{"node": "topic_variables", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "final_article", "type": "main", "index": 0}]]}, "new_lines": {"main": [[{"node": "split_out", "type": "main", "index": 0}]]}, "split_out": {"main": [[{"node": "section_starts_with_#", "type": "main", "index": 0}]]}, "input_brief": {"main": [[{"node": "new_lines", "type": "main", "index": 0}]]}, "section_text": {"main": [[{"node": "25_percent_chance", "type": "main", "index": 0}]]}, "create_folder": {"main": [[{"node": "create_outline", "type": "main", "index": 0}]]}, "final_article": {"main": [[{"node": "final_article_file", "type": "main", "index": 0}]]}, "receive_topic": {"main": [[{"node": "add_row", "type": "main", "index": 0}]]}, "create_outline": {"main": [[{"node": "markdown_to_file", "type": "main", "index": 0}]]}, "input_sections": {"main": [[{"node": "section_paragraphs", "type": "main", "index": 0}]]}, "add_2_new_lines": {"main": [[{"node": "Merge1", "type": "main", "index": 1}]]}, "topic_variables": {"main": [[{"node": "create_folder", "type": "main", "index": 0}]]}, "markdown_to_file": {"main": [[{"node": "upload_outline_file", "type": "main", "index": 0}]]}, "set_introduction": {"main": [[{"node": "Merge1", "type": "main", "index": 0}]]}, "25_percent_chance": {"main": [[{"node": "change_section_format", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "final_article_file": {"main": [[{"node": "upload_fiinalArticle", "type": "main", "index": 0}]]}, "section_paragraphs": {"main": [[{"node": "section_text", "type": "main", "index": 0}]]}, "set_section_content": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "upload_outline_file": {"main": [[{"node": "input_brief", "type": "main", "index": 0}]]}, "upload_fiinalArticle": {"main": [[{"node": "update_article_link", "type": "main", "index": 0}]]}, "change_section_format": {"main": [[{"node": "set_section_content", "type": "main", "index": 0}]]}, "section_starts_with_#": {"main": [[{"node": "set_introduction", "type": "main", "index": 0}], [{"node": "input_sections", "type": "main", "index": 0}]]}}}