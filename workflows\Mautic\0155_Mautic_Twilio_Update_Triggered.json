{"id": "13", "name": "Receive updates when a form is submitted in Mautic, and send a confirmation SMS", "nodes": [{"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.mauticTrigger", "position": [510, 300], "webhookId": "9dce2b84-33fe-4816-ae4b-301c208b5384", "parameters": {"events": ["mautic.form_on_submit"]}, "credentials": {"mauticApi": "mautic"}, "typeVersion": 1}, {"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.twilio", "position": [710, 300], "parameters": {"to": "={{$node[\"Mautic Trigger\"].json[\"mautic.form_on_submit\"][0][\"submission\"][\"results\"][\"phone_number\"]}}", "from": "1234", "message": "=Hey, {{$node[\"Mautic Trigger\"].json[\"mautic.form_on_submit\"][0][\"submission\"][\"results\"][\"first_name\"]}} 👋\nThank you for signing up for the Webinar - Getting Started with n8n. The webinar will start at 1800 CEST on 31st October 2020.\nSee you there!"}, "credentials": {"twilioApi": "twi<PERSON>"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Mautic Trigger": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}}}