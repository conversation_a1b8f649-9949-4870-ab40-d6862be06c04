{"meta": {"instanceId": "6a5e68bcca67c4cdb3e0b698d01739aea084e1ec06e551db64aeff43d174cb23", "templateCredsSetupCompleted": true}, "nodes": [{"id": "bc49829b-45f2-4910-9c37-907271982f14", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-4200, -560], "parameters": {"width": 780, "height": 540, "content": "### 5. Do you need more details?\nFind a step-by-step guide in this tutorial\n![Guide](https://www.samirsaci.com/content/images/2025/04/EDI-Message-Parser.png)\n[🎥 Watch My Tutorial](https://youtu.be/-phwXeYk7Es)"}, "typeVersion": 1}, {"id": "fca5a1f8-874b-4b25-92af-066e7ca03f67", "name": "Order Information", "type": "n8n-nodes-base.set", "position": [-4360, -1000], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "a55ebbb4-3eba-4584-8894-9e8d623d498f", "name": "documentType", "type": "string", "value": "={{ $json.summary.documentType }}"}, {"id": "cbbff4da-4679-4258-bc3c-848075c5f1df", "name": "documentNumber", "type": "string", "value": "={{ $json.summary.documentNumber }}"}, {"id": "a2eb5f07-8d1b-4c3a-b08b-a785045aeb34", "name": "orderDate", "type": "string", "value": "={{ $json.summary.orderDate }}"}, {"id": "7e319d29-463b-4875-b556-684cb0c06c59", "name": "lineItemCount", "type": "string", "value": "={{ $json.summary.lineItemCount }}"}, {"id": "5c9fc86c-e5c0-411f-a7d5-1121b5779906", "name": "totalQuantity", "type": "string", "value": "={{ $json.summary.totalQuantity }}"}]}}, "notesInFlow": true, "typeVersion": 3.4}, {"id": "3598dc97-a0d3-4d34-8220-b91925014e4a", "name": "Return Orders", "type": "n8n-nodes-base.googleSheets", "position": [-3620, -960], "parameters": {"columns": {"value": {}, "schema": [{"id": "documentType", "type": "string", "display": true, "removed": false, "required": false, "displayName": "documentType", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "documentNumber", "type": "string", "display": true, "removed": false, "required": false, "displayName": "documentNumber", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "orderDate", "type": "string", "display": true, "removed": false, "required": false, "displayName": "orderDate", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "lineItemCount", "type": "string", "display": true, "removed": false, "required": false, "displayName": "lineItemCount", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "totalQuantity", "type": "string", "display": true, "removed": false, "required": false, "displayName": "totalQuantity", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "header_Document_Type", "type": "string", "display": true, "removed": false, "required": false, "displayName": "header_Document_Type", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "header_Document_Number", "type": "string", "display": true, "removed": false, "required": false, "displayName": "header_Document_Number", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "header_Message_Function", "type": "string", "display": true, "removed": false, "required": false, "displayName": "header_Message_Function", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "header_Sender_ID", "type": "string", "display": true, "removed": false, "required": false, "displayName": "header_Sender_ID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "header_Receiver_ID", "type": "string", "display": true, "removed": false, "required": false, "displayName": "header_Receiver_ID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "header_Date", "type": "string", "display": true, "removed": false, "required": false, "displayName": "header_Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "header_Time", "type": "string", "display": true, "removed": false, "required": false, "displayName": "header_Time", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "header_Control_Reference", "type": "string", "display": true, "removed": false, "required": false, "displayName": "header_Control_Reference", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date1_Qualifier", "type": "string", "display": true, "removed": false, "required": false, "displayName": "date1_Qualifier", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date1_Description", "type": "string", "display": true, "removed": false, "required": false, "displayName": "date1_Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date1_Date", "type": "string", "display": true, "removed": false, "required": false, "displayName": "date1_Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date1_Format", "type": "string", "display": true, "removed": false, "required": false, "displayName": "date1_Format", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date2_Qualifier", "type": "string", "display": true, "removed": false, "required": false, "displayName": "date2_Qualifier", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date2_Description", "type": "string", "display": true, "removed": false, "required": false, "displayName": "date2_Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date2_Date", "type": "string", "display": true, "removed": false, "required": false, "displayName": "date2_Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date2_Format", "type": "string", "display": true, "removed": false, "required": false, "displayName": "date2_Format", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date3_Qualifier", "type": "string", "display": true, "removed": false, "required": false, "displayName": "date3_Qualifier", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date3_Description", "type": "string", "display": true, "removed": false, "required": false, "displayName": "date3_Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date3_Date", "type": "string", "display": true, "removed": false, "required": false, "displayName": "date3_Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date3_Format", "type": "string", "display": true, "removed": false, "required": false, "displayName": "date3_Format", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "party1_Type", "type": "string", "display": true, "removed": false, "required": false, "displayName": "party1_Type", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "party1_Description", "type": "string", "display": true, "removed": false, "required": false, "displayName": "party1_Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "party1_ID", "type": "string", "display": true, "removed": false, "required": false, "displayName": "party1_ID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "party1_Name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "party1_Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "party2_Type", "type": "string", "display": true, "removed": false, "required": false, "displayName": "party2_Type", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "party2_Description", "type": "string", "display": true, "removed": false, "required": false, "displayName": "party2_Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "party2_ID", "type": "string", "display": true, "removed": false, "required": false, "displayName": "party2_ID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "party2_Name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "party2_Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "party3_Type", "type": "string", "display": true, "removed": false, "required": false, "displayName": "party3_Type", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "party3_Description", "type": "string", "display": true, "removed": false, "required": false, "displayName": "party3_Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "party3_ID", "type": "string", "display": true, "removed": false, "required": false, "displayName": "party3_ID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "party3_Name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "party3_Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "party4_Type", "type": "string", "display": true, "removed": false, "required": false, "displayName": "party4_Type", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "party4_Description", "type": "string", "display": true, "removed": false, "required": false, "displayName": "party4_Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "party4_ID", "type": "string", "display": true, "removed": false, "required": false, "displayName": "party4_ID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "party4_Name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "party4_Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "line_Number", "type": "string", "display": true, "removed": false, "required": false, "displayName": "line_Number", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "line_Product_ID", "type": "string", "display": true, "removed": false, "required": false, "displayName": "line_Product_ID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "line_Product_ID_Type", "type": "string", "display": true, "removed": false, "required": false, "displayName": "line_Product_ID_Type", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "line_Description", "type": "string", "display": true, "removed": false, "required": false, "displayName": "line_Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "line_Quantity", "type": "string", "display": true, "removed": false, "required": false, "displayName": "line_Quantity", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "line_Unit", "type": "string", "display": true, "removed": false, "required": false, "displayName": "line_Unit", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "line_Price", "type": "string", "display": true, "removed": false, "required": false, "displayName": "line_Price", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "line_Price_Qualifier", "type": "string", "display": true, "removed": false, "required": false, "displayName": "line_Price_Qualifier", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": 1261096359, "cachedResultUrl": "=", "cachedResultName": "="}, "documentId": {"__rl": true, "mode": "list", "value": "1SaSFnJx80wrArf6DLx8zZx2y5VFOAmp0u-a26wliTbU", "cachedResultUrl": "=", "cachedResultName": "="}}, "notesInFlow": true, "typeVersion": 4.5}, {"id": "edfa5ef9-3095-47c2-ad80-c09cac647823", "name": "Outbound Orders", "type": "n8n-nodes-base.googleSheets", "position": [-3640, -780], "parameters": {"columns": {"value": {}, "schema": [{"id": "documentType", "type": "string", "display": true, "removed": false, "required": false, "displayName": "documentType", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "documentNumber", "type": "string", "display": true, "removed": false, "required": false, "displayName": "documentNumber", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "orderDate", "type": "string", "display": true, "removed": false, "required": false, "displayName": "orderDate", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "lineItemCount", "type": "string", "display": true, "removed": false, "required": false, "displayName": "lineItemCount", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "totalQuantity", "type": "string", "display": true, "removed": false, "required": false, "displayName": "totalQuantity", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "header_Document_Type", "type": "string", "display": true, "removed": false, "required": false, "displayName": "header_Document_Type", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "header_Document_Number", "type": "string", "display": true, "removed": false, "required": false, "displayName": "header_Document_Number", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "header_Message_Function", "type": "string", "display": true, "removed": false, "required": false, "displayName": "header_Message_Function", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "header_Sender_ID", "type": "string", "display": true, "removed": false, "required": false, "displayName": "header_Sender_ID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "header_Receiver_ID", "type": "string", "display": true, "removed": false, "required": false, "displayName": "header_Receiver_ID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "header_Date", "type": "string", "display": true, "removed": false, "required": false, "displayName": "header_Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "header_Time", "type": "string", "display": true, "removed": false, "required": false, "displayName": "header_Time", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "header_Control_Reference", "type": "string", "display": true, "removed": false, "required": false, "displayName": "header_Control_Reference", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date1_Qualifier", "type": "string", "display": true, "removed": false, "required": false, "displayName": "date1_Qualifier", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date1_Description", "type": "string", "display": true, "removed": false, "required": false, "displayName": "date1_Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date1_Date", "type": "string", "display": true, "removed": false, "required": false, "displayName": "date1_Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date1_Format", "type": "string", "display": true, "removed": false, "required": false, "displayName": "date1_Format", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date2_Qualifier", "type": "string", "display": true, "removed": false, "required": false, "displayName": "date2_Qualifier", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date2_Description", "type": "string", "display": true, "removed": false, "required": false, "displayName": "date2_Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date2_Date", "type": "string", "display": true, "removed": false, "required": false, "displayName": "date2_Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date2_Format", "type": "string", "display": true, "removed": false, "required": false, "displayName": "date2_Format", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date3_Qualifier", "type": "string", "display": true, "removed": false, "required": false, "displayName": "date3_Qualifier", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date3_Description", "type": "string", "display": true, "removed": false, "required": false, "displayName": "date3_Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date3_Date", "type": "string", "display": true, "removed": false, "required": false, "displayName": "date3_Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date3_Format", "type": "string", "display": true, "removed": false, "required": false, "displayName": "date3_Format", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "party1_Type", "type": "string", "display": true, "removed": false, "required": false, "displayName": "party1_Type", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "party1_Description", "type": "string", "display": true, "removed": false, "required": false, "displayName": "party1_Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "party1_ID", "type": "string", "display": true, "removed": false, "required": false, "displayName": "party1_ID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "party1_Name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "party1_Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "party2_Type", "type": "string", "display": true, "removed": false, "required": false, "displayName": "party2_Type", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "party2_Description", "type": "string", "display": true, "removed": false, "required": false, "displayName": "party2_Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "party2_ID", "type": "string", "display": true, "removed": false, "required": false, "displayName": "party2_ID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "party2_Name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "party2_Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "party3_Type", "type": "string", "display": true, "removed": false, "required": false, "displayName": "party3_Type", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "party3_Description", "type": "string", "display": true, "removed": false, "required": false, "displayName": "party3_Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "party3_ID", "type": "string", "display": true, "removed": false, "required": false, "displayName": "party3_ID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "party3_Name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "party3_Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "party4_Type", "type": "string", "display": true, "removed": false, "required": false, "displayName": "party4_Type", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "party4_Description", "type": "string", "display": true, "removed": false, "required": false, "displayName": "party4_Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "party4_ID", "type": "string", "display": true, "removed": false, "required": false, "displayName": "party4_ID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "party4_Name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "party4_Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "line_Number", "type": "string", "display": true, "removed": false, "required": false, "displayName": "line_Number", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "line_Product_ID", "type": "string", "display": true, "removed": false, "required": false, "displayName": "line_Product_ID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "line_Product_ID_Type", "type": "string", "display": true, "removed": false, "required": false, "displayName": "line_Product_ID_Type", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "line_Description", "type": "string", "display": true, "removed": false, "required": false, "displayName": "line_Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "line_Quantity", "type": "string", "display": true, "removed": false, "required": false, "displayName": "line_Quantity", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "line_Unit", "type": "string", "display": true, "removed": false, "required": false, "displayName": "line_Unit", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "line_Price", "type": "string", "display": true, "removed": false, "required": false, "displayName": "line_Price", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "line_Price_Qualifier", "type": "string", "display": true, "removed": false, "required": false, "displayName": "line_Price_Qualifier", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": 1261096359, "cachedResultUrl": "=", "cachedResultName": "="}, "documentId": {"__rl": true, "mode": "list", "value": "1SaSFnJx80wrArf6DLx8zZx2y5VFOAmp0u-a26wliTbU", "cachedResultUrl": "=", "cachedResultName": "="}}, "notesInFlow": true, "typeVersion": 4.5}, {"id": "6d1c614f-9301-4f25-ab11-350018f145e3", "name": "Order Type", "type": "n8n-nodes-base.if", "position": [-3840, -880], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "fc591c63-edfe-4e6d-8074-6ab3079988c8", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.documentType }}", "rightValue": "Return Order"}]}}, "typeVersion": 2.2}, {"id": "fc206367-2fbf-4943-b2ce-9fe399dd2730", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-5420, -1240], "parameters": {"color": 7, "width": 380, "height": 620, "content": "### 1. Workflow Trigger with Gmail Trigger\nThe workflow is triggered by a new email received in your Gmail mailbox. \nIf the subject includes the string \"EDI\" we proceed, if not we do nothing.\n\n#### How to setup?\n- **Gmail Trigger Node:** set up your Gmail API credentials\n[Learn more about the Gmail Trigger Node](https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.gmailtrigger)\n"}, "typeVersion": 1}, {"id": "c6da1a85-d725-4a41-b63f-504fa8b552fb", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-5420, -540], "parameters": {"width": 800, "height": 880, "content": "### Example of EDI Message\nYou can send yourself this email to test the workflow.\n\nUNA:+.? '\nUNB+UNOC:3+SENDER_ID+RECEIVER_ID+240317:1200+ORDER67890'\nUNH+1+ORDERS:D:96A:UN'\nBGM+220+PO56789012+9'\nDTM+137:20250318:102'      \nDTM+2:20250325:102'        \nDTM+10:20250324:102'      \nNAD+BY+BUYER_ABC::91'\nNAD+SU+SUPPLIER_XYZ::91'\nNAD+DP+WAREHOUSE_001::91'\nNAD+DP+Main Distribution Center'\nLIN+1++987654:IN'\nIMD+F++:::Product X Description'\nQTY+21:50:EA'\nPRI+AAA:20.00'\nLIN+2++654987:IN'\nIMD+F++:::Product Y Description'\nQTY+21:150:EA'\nPRI+AAA:12.75'\nUNT+10+1'\nUNZ+1+ORDER67890'UNA:+.? '\nUNB+UNOC:3+SENDER_ID+RECEIVER_ID+240317:1200+ORDER67890'\nUNH+1+ORDERS:D:96A:UN'\nBGM+220+PO56789012+9'\nDTM+137:20250318:102'      \nDTM+2:20250325:102'        \nDTM+10:20250324:102'      \nNAD+BY+BUYER_ABC::91'\nNAD+SU+SUPPLIER_XYZ::91'\nNAD+DP+WAREHOUSE_001::91'\nNAD+DP+Main Distribution Center'\nLIN+1++987654:IN'\nIMD+F++:::Product X Description'\nQTY+21:50:EA'\nPRI+AAA:20.00'\nLIN+2++654987:IN'\nIMD+F++:::Product Y Description'\nQTY+21:150:EA'\nPRI+AAA:12.75'\nUNT+10+1'\nUNZ+1+ORDER67890'"}, "typeVersion": 1}, {"id": "4c82f8ff-e405-4e5f-8386-9c622805023a", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-4980, -1240], "parameters": {"color": 7, "width": 440, "height": 620, "content": "### 2. Get Email Body & Parse EDI Message\nThe first node extracts the email body using the ID from the trigger. This body is parsed using the code node to extract order information.\n\n#### How to setup?\n- **Gmail Node:** set up your Gmail API credentials\n[Learn more about the Gmail Node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.gmail)\n"}, "typeVersion": 1}, {"id": "e90096b1-ad36-4b18-96a2-9259377b4873", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.gmailTrigger", "position": [-5400, -860], "parameters": {"filters": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}}, "notesInFlow": true, "typeVersion": 1.2}, {"id": "26433f0f-487d-49dc-8de7-d4bd3bcf895c", "name": "Subject includes EDI", "type": "n8n-nodes-base.if", "position": [-5180, -860], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "3bc8a327-7e66-48e3-b442-38125b6f8670", "operator": {"type": "string", "operation": "contains"}, "leftValue": "={{ $json.Subject }}", "rightValue": "EDI"}]}}, "typeVersion": 2.2}, {"id": "6960941f-6b49-41c2-88c6-9442bcb7cb34", "name": "Extract Body", "type": "n8n-nodes-base.set", "position": [-4820, -860], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "38358bb8-1b5a-4adc-816d-6710f53f7c0d", "name": "body", "type": "string", "value": "={{ $json.text.replace(/\\\\n/g, '\\n').replace(/^'|'$/g, '') }}"}]}}, "notesInFlow": true, "typeVersion": 3.4}, {"id": "6e64556e-07ce-4d9d-89fc-07971dd9c553", "name": "Get Email", "type": "n8n-nodes-base.gmail", "position": [-4960, -860], "webhookId": "7476ecd4-e3ba-471d-a73a-ddef7b3ffd28", "parameters": {"simple": false, "options": {}, "messageId": "={{ $json.id }}", "operation": "get"}, "notesInFlow": true, "typeVersion": 2.1}, {"id": "0346eabe-552a-47d8-ac9e-9619926d0242", "name": "Parse EDI Message", "type": "n8n-nodes-base.code", "position": [-4660, -860], "parameters": {"jsCode": "// EDI Parser function for n8n JavaScript node\nfunction parseEDI(ediMessage) {\n  // Define the data structure to store parsed results\n  const result = {\n    interchangeHeader: {},\n    messageHeader: {},\n    orderDetails: {},\n    dates: [],\n    parties: [],\n    lineItems: []\n  };\n  \n  // Split the message into lines and remove empty lines\n  const lines = ediMessage.split(\"'\").filter(line => line.trim().length > 0);\n  \n  // Parse each line\n  let currentLineItem = null;\n  \n  for (const line of lines) {\n    const segments = line.trim().split('+');\n    const segmentName = segments[0];\n    \n    switch (segmentName) {\n      case 'UNA':\n        // Service String Advice - contains delimiter information\n        break;\n        \n      case 'UNB':\n        // Interchange Header\n        // UNB+UNOC:3+SENDER_ID+RECEIVER_ID+240318:1200+ORDER54321\n        result.interchangeHeader = {\n          syntax: segments[1],\n          senderId: segments[2],\n          receiverId: segments[3],\n          dateTime: segments[4]?.split(':')[0] || '',\n          time: segments[4]?.split(':')[1] || '',\n          controlReference: segments[5] || ''\n        };\n        break;\n        \n      case 'UNH':\n        // Message Header\n        // UNH+1+ORDERS:D:96A:UN\n        if (segments.length > 2) {\n          const messageParts = segments[2].split(':');\n          result.messageHeader = {\n            messageReference: segments[1],\n            messageType: messageParts[0],\n            messageVersion: messageParts[1],\n            messageRelease: messageParts[2],\n            controlAgency: messageParts[3]\n          };\n        }\n        break;\n        \n      case 'BGM':\n        // Beginning of Message\n        // BGM+230+RT54321098+9\n        result.orderDetails = {\n          documentType: segments[1],\n          documentNumber: segments[2],\n          messageFunction: segments[3]\n        };\n        break;\n        \n      case 'DTM':\n        // Date/Time/Period\n        // DTM+137:20250319:102\n        if (segments[1]) {\n          const dateParts = segments[1].split(':');\n          const dateObj = {\n            qualifier: dateParts[0],\n            date: dateParts[1],\n            format: dateParts[2]\n          };\n          \n          // Add human-readable description based on qualifier\n          switch (dateParts[0]) {\n            case '137':\n              dateObj.description = 'Document Date';\n              break;\n            case '2':\n              dateObj.description = 'Delivery Date';\n              break;\n            case '10':\n              dateObj.description = 'Shipment Date';\n              break;\n            default:\n              dateObj.description = 'Other Date';\n          }\n          \n          result.dates.push(dateObj);\n        }\n        break;\n      \n      case 'NAD':\n        // Name and Address\n        // NAD+BY+CUSTOMER_123::91\n        if (segments.length > 1) {\n          const partyCode = segments[1];\n          const partyId = segments[2]?.split(':')[0] || '';\n          \n          const party = {\n            partyQualifier: partyCode,\n            partyId: partyId,\n            qualifierDescription: ''\n          };\n          \n          // Add human-readable description\n          switch (partyCode) {\n            case 'BY':\n              party.qualifierDescription = 'Buyer';\n              break;\n            case 'SU':\n              party.qualifierDescription = 'Supplier';\n              break;\n            case 'DP':\n              party.qualifierDescription = 'Delivery Party';\n              break;\n            default:\n              party.qualifierDescription = 'Other Party';\n          }\n          \n          // If there's a full name instead of a code (like \"Returns Processing Hub\")\n          if (segments[2] && !segments[2].includes(':')) {\n            party.partyName = segments[2];\n            party.partyId = '';\n          }\n          \n          result.parties.push(party);\n        }\n        break;\n      \n      case 'LIN':\n        // Line Item\n        // LIN+1++321654:IN\n        currentLineItem = {\n          lineNumber: segments[1],\n          productId: '',\n          productIdType: '',\n          description: '',\n          quantity: 0,\n          unit: '',\n          price: 0\n        };\n        \n        // Parse product ID if present\n        if (segments[3]) {\n          const productParts = segments[3].split(':');\n          currentLineItem.productId = productParts[0];\n          currentLineItem.productIdType = productParts[1] || '';\n        }\n        \n        result.lineItems.push(currentLineItem);\n        break;\n      \n      case 'IMD':\n        // Item Description\n        // IMD+F++:::Defective Product A\n        if (currentLineItem && segments.length > 3) {\n          // The description is typically in the last component after multiple colons\n          const descriptionParts = segments[3].split(':');\n          currentLineItem.description = descriptionParts[descriptionParts.length - 1];\n        }\n        break;\n      \n      case 'QTY':\n        // Quantity\n        // QTY+21:10:EA\n        if (currentLineItem && segments[1]) {\n          const quantityParts = segments[1].split(':');\n          currentLineItem.quantityQualifier = quantityParts[0];\n          currentLineItem.quantity = parseFloat(quantityParts[1] || '0');\n          currentLineItem.unit = quantityParts[2] || '';\n        }\n        break;\n      \n      case 'PRI':\n        // Price Details\n        // PRI+AAA:0.00\n        if (currentLineItem && segments[1]) {\n          const priceParts = segments[1].split(':');\n          currentLineItem.priceQualifier = priceParts[0];\n          currentLineItem.price = parseFloat(priceParts[1] || '0');\n        }\n        break;\n      \n      case 'UNT':\n        // Message Trailer\n        break;\n      \n      case 'UNZ':\n        // Interchange Trailer\n        break;\n    }\n  }\n  \n  // Add some summary info\n  result.summary = {\n    documentType: 'Return Order',\n    documentNumber: result.orderDetails.documentNumber,\n    orderDate: result.dates.find(d => d.qualifier === '137')?.date || '',\n    lineItemCount: result.lineItems.length,\n    totalQuantity: result.lineItems.reduce((sum, item) => sum + item.quantity, 0)\n  };\n  \n  return result;\n}\n\n// Return the parsed EDI data\nconst ediMessage =  $input.first().json.body;\n\nif (!ediMessage) {\n  throw new Error('No EDI message found in input. Please provide the EDI message in the \"ediMessage\" property.');\n}\n\nconst parsedData = parseEDI(ediMessage);\nreturn { json: parsedData };"}, "typeVersion": 2}, {"id": "0fa4b446-bb37-48ab-a44b-8b2c52e2660b", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-4100, -1240], "parameters": {"color": 7, "width": 700, "height": 620, "content": "### 4. Store the Transactions in a Google Sheet\nThis block will filter the order based on the order type (Return Orders, Outbound Orders) extracted from the order information node. Results are stored in two distinct sheets of the same Google Sheet file.\n\n#### How to setup?\n- **Add Results in Google Sheets**:\n   1. Add your Google Sheet API credentials to access the Google Sheet file\n   2. Select the file using the list, an URL or an ID\n   3. Select the sheet in which the vocabulary list is stored\n   4. You don't need to create columns as the mapping is automatic.\n  [Learn more about the Google Sheet Node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googlesheets)"}, "typeVersion": 1}, {"id": "7a1451db-9390-431f-bca5-54a537ff8016", "name": "Order Info + Orderlines", "type": "n8n-nodes-base.merge", "position": [-4040, -880], "parameters": {"mode": "combineBySql"}, "typeVersion": 3}, {"id": "e64a1db7-4c22-4925-9597-9c14fdddbfe4", "name": "Flatten Data to Orderlines", "type": "n8n-nodes-base.code", "position": [-4460, -860], "parameters": {"jsCode": "// EDI to Flattened Tabular Data Transformer for n8n JavaScript node\nfunction transformToFlattened(parsedEDI) {\n  const flattened = [];\n  \n  // Create a header object with all order header fields\n  const headerObj = {\n    header_Document_Type: parsedEDI.orderDetails.documentType || '',\n    header_Document_Number: parsedEDI.orderDetails.documentNumber || '',\n    header_Message_Function: parsedEDI.orderDetails.messageFunction || '',\n    header_Sender_ID: parsedEDI.interchangeHeader.senderId || '',\n    header_Receiver_ID: parsedEDI.interchangeHeader.receiverId || '',\n    header_Date: parsedEDI.interchangeHeader.dateTime || '',\n    header_Time: parsedEDI.interchangeHeader.time || '',\n    header_Control_Reference: parsedEDI.interchangeHeader.controlReference || ''\n  };\n  \n  // Process all dates\n  const dateObjs = {};\n  if (parsedEDI.dates && Array.isArray(parsedEDI.dates)) {\n    parsedEDI.dates.forEach((date, index) => {\n      const prefix = `date${index + 1}_`;\n      dateObjs[`${prefix}Qualifier`] = date.qualifier || '';\n      dateObjs[`${prefix}Description`] = date.description || '';\n      dateObjs[`${prefix}Date`] = date.date || '';\n      dateObjs[`${prefix}Format`] = date.format || '';\n    });\n  }\n  \n  // Process all parties\n  const partyObjs = {};\n  if (parsedEDI.parties && Array.isArray(parsedEDI.parties)) {\n    parsedEDI.parties.forEach((party, index) => {\n      const prefix = `party${index + 1}_`;\n      partyObjs[`${prefix}Type`] = party.partyQualifier || '';\n      partyObjs[`${prefix}Description`] = party.qualifierDescription || '';\n      partyObjs[`${prefix}ID`] = party.partyId || '';\n      partyObjs[`${prefix}Name`] = party.partyName || '';\n    });\n  }\n  \n  // Create one row for each line item with all header, date, and party info\n  if (parsedEDI.lineItems && Array.isArray(parsedEDI.lineItems)) {\n    parsedEDI.lineItems.forEach((item) => {\n      const lineItem = {\n        line_Number: item.lineNumber || '',\n        line_Product_ID: item.productId || '',\n        line_Product_ID_Type: item.productIdType || '',\n        line_Description: item.description || '',\n        line_Quantity: item.quantity || 0,\n        line_Unit: item.unit || '',\n        line_Price: item.price || 0,\n        line_Price_Qualifier: item.priceQualifier || ''\n      };\n      \n      // Combine all information into one flat object\n      const flatRow = {\n        ...headerObj,\n        ...dateObjs,\n        ...partyObjs,\n        ...lineItem\n      };\n      \n      flattened.push(flatRow);\n    });\n  }\n  \n  // If there are no line items, create at least one row with header info\n  if (flattened.length === 0) {\n    flattened.push({\n      ...headerObj,\n      ...dateObjs,\n      ...partyObjs\n    });\n  }\n  \n  return flattened;\n}\n\nconst parsedEDI = $input.all()[0].json;\n\n// Make sure we have valid data\nif (!parsedEDI || !parsedEDI.orderDetails) {\n  throw new Error('Invalid EDI data format. Please ensure the input is from the EDI parser.');\n}\n\nconst flattenedData = transformToFlattened(parsedEDI);\n\n// Return the flattened data\nreturn { json: { data: flattenedData } };"}, "typeVersion": 2}, {"id": "5b56fe40-9cfb-4668-946d-470dc9e3a39e", "name": "Split Out by Line", "type": "n8n-nodes-base.splitOut", "position": [-4280, -860], "parameters": {"options": {}, "fieldToSplitOut": "data"}, "typeVersion": 1}, {"id": "903399b2-cdee-40c0-99cb-1c44d84e96d2", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-4480, -1240], "parameters": {"color": 7, "width": 320, "height": 620, "content": "### 3. Process Parsed Data\nThis block extract order information and format the orderlines to be stored in a google sheet.\n\n#### How to setup?\nNothing to do."}, "typeVersion": 1}], "pinData": {}, "connections": {"Get Email": {"main": [[{"node": "Extract Body", "type": "main", "index": 0}]]}, "Order Type": {"main": [[{"node": "Return Orders", "type": "main", "index": 0}], [{"node": "Outbound Orders", "type": "main", "index": 0}]]}, "Extract Body": {"main": [[{"node": "Parse EDI Message", "type": "main", "index": 0}]]}, "Email Trigger": {"main": [[{"node": "Subject includes EDI", "type": "main", "index": 0}]]}, "Order Information": {"main": [[{"node": "Order Info + Orderlines", "type": "main", "index": 0}]]}, "Parse EDI Message": {"main": [[{"node": "Order Information", "type": "main", "index": 0}, {"node": "Flatten Data to Orderlines", "type": "main", "index": 0}]]}, "Split Out by Line": {"main": [[{"node": "Order Info + Orderlines", "type": "main", "index": 1}]]}, "Subject includes EDI": {"main": [[{"node": "Get Email", "type": "main", "index": 0}]]}, "Order Info + Orderlines": {"main": [[{"node": "Order Type", "type": "main", "index": 0}]]}, "Flatten Data to Orderlines": {"main": [[{"node": "Split Out by Line", "type": "main", "index": 0}]]}}}