{"id": "OqfQNcgTqUK7UvZG", "meta": {"instanceId": "5ce52989094be90be3b3bdd9ed9cee1d7ce1fcecaa598afaec4a50646d32e291", "templateCredsSetupCompleted": true}, "name": "Youtube Discord <PERSON>", "tags": [{"id": "5eZb3e5PJspoJjVN", "name": "Discord", "createdAt": "2025-02-22T09:31:58.972Z", "updatedAt": "2025-02-22T09:31:58.972Z"}], "nodes": [{"id": "39832819-a14b-445c-bf5c-0bd93613b1ca", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [80, 440], "webhookId": "b0631bec-9ccc-4eb8-b143-d73609b213c7", "parameters": {"path": "b0631bec-9ccc-4eb8-b143-d73609b213c7", "options": {}, "httpMethod": "POST", "responseMode": "responseNode"}, "typeVersion": 2}, {"id": "5e70b649-5678-4718-98a7-302a4c784155", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [460, 680], "parameters": {"sessionKey": "={{ $json.body.userId }}", "sessionIdType": "customKey", "contextWindowLength": 50}, "typeVersion": 1.3}, {"id": "7cc849c3-3ed8-4fe2-a378-a213736a9aef", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [180, 700], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash"}, "credentials": {"googlePalmApi": {"id": "clmB8ZYJMHaHmnsu", "name": "Stardawn#1"}}, "typeVersion": 1}, {"id": "4b664f21-6f1c-4894-9196-beecbd865d3e", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [880, 440], "parameters": {"options": {}, "respondWith": "allIncomingItems"}, "typeVersion": 1.1}, {"id": "c7c779d3-e324-4a3f-a5a1-5218ec61d856", "name": "correctNaming", "type": "n8n-nodes-base.code", "position": [680, 440], "parameters": {"jsCode": "// Hole alle Items\nconst items = $input.all();\n\n// Nehme das erste Item (falls mehrere vorhanden sind)\nconst item = items[0];\n\n// Extrahiere den output\nconst antwort = item.json.output;\n\n// Formatiere die Antwort im richtigen Format für den Discord-Bot\nreturn {\n  json: {\n    answer: antwort\n  }\n};"}, "typeVersion": 2}, {"id": "9ff7ad77-88ce-467e-91b1-4fc2d13636fd", "name": "Discord AI Response Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [300, 440], "parameters": {"text": "=Username: {{ $json.body.userName }}\n\nQuestion/Prompt: {{ $json.body.question }}", "options": {"systemMessage": "You are a helpful assistant. You answer in the language you receive the question in. Interactions might be all over the place. If there is any questions regarding the Youtube Videos of the channel: Presting Podcasts, you have the transcript of the podcast videos as additional knowledge.\nAlways begin your answer with a @insertusername to mark the guy who asked the question.  "}, "promptType": "define"}, "typeVersion": 1.8}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "429e2ccd-5a58-4287-9ad8-314ef<PERSON>cb8f", "connections": {"Webhook": {"main": [[{"node": "Discord AI Response Agent", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "Discord AI Response Agent", "type": "ai_memory", "index": 0}]]}, "correctNaming": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Discord AI Response Agent", "type": "ai_languageModel", "index": 0}]]}, "Discord AI Response Agent": {"main": [[{"node": "correctNaming", "type": "main", "index": 0}]]}}}