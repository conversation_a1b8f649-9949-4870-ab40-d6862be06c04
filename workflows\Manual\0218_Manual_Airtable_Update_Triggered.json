{"id": "171", "name": "Insert and update data in Airtable", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [500, 350], "parameters": {}, "typeVersion": 1}, {"name": "Airtable", "type": "n8n-nodes-base.airtable", "position": [900, 350], "parameters": {"table": "Table 1", "options": {}, "operation": "append", "application": ""}, "credentials": {"airtableApi": "Airtable Credentials n8n"}, "typeVersion": 1}, {"name": "Airtable1", "type": "n8n-nodes-base.airtable", "position": [1100, 350], "parameters": {"table": "={{$node[\"Airtable\"].parameter[\"table\"]}}", "operation": "list", "application": "={{$node[\"Airtable\"].parameter[\"application\"]}}", "additionalOptions": {"filterByFormula": "Name='n8n'"}}, "credentials": {"airtableApi": "Airtable Credentials n8n"}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [700, 350], "parameters": {"values": {"number": [{"name": "ID", "value": 3}], "string": [{"name": "Name", "value": "n8n"}]}, "options": {}}, "typeVersion": 1}, {"name": "Set1", "type": "n8n-nodes-base.set", "position": [1300, 350], "parameters": {"values": {"string": [{"name": "Name", "value": "nodemation"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "Airtable2", "type": "n8n-nodes-base.airtable", "position": [1500, 350], "parameters": {"id": "={{$node[\"Airtable1\"].json[\"id\"]}}", "table": "={{$node[\"Airtable\"].parameter[\"table\"]}}", "options": {}, "operation": "update", "application": "={{$node[\"Airtable\"].parameter[\"application\"]}}"}, "credentials": {"airtableApi": "Airtable Credentials n8n"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Set": {"main": [[{"node": "Airtable", "type": "main", "index": 0}]]}, "Set1": {"main": [[{"node": "Airtable2", "type": "main", "index": 0}]]}, "Airtable": {"main": [[{"node": "Airtable1", "type": "main", "index": 0}]]}, "Airtable1": {"main": [[{"node": "Set1", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}}