{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "49620d3a-d4ec-4017-ade1-ff2ef5473c11", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-220, -80], "parameters": {"color": 7, "width": 680, "height": 660, "content": "## 1. Set up an MCP Server Trigger\n[Read more about the MCP Server Trigger](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-langchain.mcptrigger)"}, "typeVersion": 1}, {"id": "f0646a81-d328-4f07-a744-60f576b5a51e", "name": "Insert", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [-40, 380], "parameters": {"name": "insert_review", "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "description": "=Call this tool to insert a customer's review into our review database.", "workflowInputs": {"value": {"text": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('text', `The contents of the review`, 'string') }}", "text2": "null", "operation": "insert", "companyIds": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('companyIds', `The company ID is their url address.`, 'string') }}"}, "schema": [{"id": "operation", "type": "string", "display": true, "removed": false, "required": false, "displayName": "operation", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "text", "type": "string", "display": true, "removed": false, "required": false, "displayName": "text", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "text2", "type": "string", "display": true, "removed": false, "required": false, "displayName": "text2", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "companyIds", "type": "string", "display": true, "removed": false, "required": false, "displayName": "companyIds", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2.1}, {"id": "21e8beac-dbd5-44d7-8472-4edff3f63308", "name": "Search", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [80, 440], "parameters": {"name": "search_reviews", "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}", "cachedResultName": "={{ $workflow.id }}"}, "description": "Call this tool to search our reviews database.", "workflowInputs": {"value": {"text": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('text', `the query or search terms to use`, 'string') }}", "text2": "null", "operation": "search", "companyIds": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('companyIds', `Optional, leave blank to search over all companies otherwise one or more company IDs comma-delimited. The company ID is their url address.`, 'string') }}"}, "schema": [{"id": "operation", "type": "string", "display": true, "required": false, "displayName": "operation", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "text", "type": "string", "display": true, "removed": false, "required": false, "displayName": "text", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "text2", "type": "string", "display": true, "removed": false, "required": false, "displayName": "text2", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "companyIds", "type": "string", "display": true, "removed": false, "required": false, "displayName": "companyIds", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2.1}, {"id": "ffb100a4-9108-4ccd-a897-e5cd9e752232", "name": "Recommend", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [280, 240], "parameters": {"name": "recommend_reviews", "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "description": "Call this tool to generate a recommendation for a review based on positive and/or negative preferences.", "workflowInputs": {"value": {"text": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('text', `preferences to include.`, 'string') }}", "text2": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('text2', `preference to avoid.`, 'string') }}", "operation": "recommend", "companyIds": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('companyIds', `Optional, leave blank to search across all reviews otherwise one or more company IDs, comma-delimited. The company ID is their url address.`, 'string') }}"}, "schema": [{"id": "operation", "type": "string", "display": true, "required": false, "displayName": "operation", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "text", "type": "string", "display": true, "removed": false, "required": false, "displayName": "text", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "text2", "type": "string", "display": true, "removed": false, "required": false, "displayName": "text2", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "companyIds", "type": "string", "display": true, "removed": false, "required": false, "displayName": "companyIds", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2.1}, {"id": "d1d53cbc-0a22-409d-9336-d8c98eeaa170", "name": "Qdrant MCP Server", "type": "@n8n/n8n-nodes-langchain.mcpTrigger", "position": [-40, 60], "webhookId": "a1aff1b5-e5c7-4ca2-91eb-017c1fe32dab", "parameters": {"path": "a1aff1b5-e5c7-4ca2-91eb-017c1fe32dab"}, "typeVersion": 1}, {"id": "82d747a5-ff5f-44ff-9f68-cc1aa01ba1de", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [540, 280], "parameters": {"workflowInputs": {"values": [{"name": "operation"}, {"name": "text"}, {"name": "text2"}, {"name": "companyIds"}]}}, "typeVersion": 1.1}, {"id": "c7a2e948-5cd5-4545-a633-c1157e63edec", "name": "Operation", "type": "n8n-nodes-base.switch", "position": [760, 240], "parameters": {"rules": {"values": [{"outputKey": "listCompanies", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "fe782b0f-f501-4985-a9d2-f63f4019177f", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.operation }}", "rightValue": "listCompanies"}]}, "renameOutput": true}, {"outputKey": "insert", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "81b134bc-d671-4493-b3ad-8df9be3f49a6", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.operation }}", "rightValue": "insert"}]}, "renameOutput": true}, {"outputKey": "search", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "8d57914f-6587-4fb3-88e0-aa1de6ba56c1", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.operation }}", "rightValue": "search"}]}, "renameOutput": true}, {"outputKey": "compare", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "7c38f238-213a-46ec-aefe-22e0bcb8dffc", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.operation }}", "rightValue": "compare"}]}, "renameOutput": true}, {"outputKey": "recommend", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "2c691501-786a-433f-a185-3a6e0d08d336", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.operation }}", "rightValue": "recommend"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "18d805db-376c-4583-963a-db1e5d09aa50", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [480, -140], "parameters": {"color": 7, "width": 580, "height": 320, "content": "## 2. Expand Functionality Beyond Vendor Implementation\n[Read more about the Qdrant Node](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.vectorstoreqdrant)\n\nOfficially supported MCP servers are cool but may not always have the features you want. N8N MCP servers give you the freedom to expand and customise to fit your business or product needs and requirements.\n\nFor our Qdrant MCP server, we've added 2 additional capabilities from the Qdrant API; The Group Search and Recommendation API. With these, we can explore many more use-cases for our users.\n"}, "typeVersion": 1}, {"id": "ab8aeea3-0564-4c96-a67c-ff319df3297b", "name": "Compare", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [220, 380], "parameters": {"name": "compare_reviews", "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}", "cachedResultName": "={{ $workflow.id }}"}, "description": "Call this tool to compare search results across 2 or more companies.", "workflowInputs": {"value": {"text": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('text', `the query or search terms to use`, 'string') }}", "text2": "null", "operation": "compare", "companyIds": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('companyIds', `Two or more company IDs, comma-delimited. The company ID is their url address.`, 'string') }}"}, "schema": [{"id": "operation", "type": "string", "display": true, "required": false, "displayName": "operation", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "text", "type": "string", "display": true, "removed": false, "required": false, "displayName": "text", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "text2", "type": "string", "display": true, "removed": false, "required": false, "displayName": "text2", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "companyIds", "type": "string", "display": true, "removed": false, "required": false, "displayName": "companyIds", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2.1}, {"id": "4f0876cb-dc07-486e-937b-334fa2cb754f", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [1920, 860], "parameters": {"width": 213.30551928619226, "height": 332.38559808882246, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n### 🚨Configure Your Qdrant Connection\n* Be sure to enter your endpoint address"}, "typeVersion": 1}, {"id": "02031bac-016f-4715-8413-6255cf73e103", "name": "Recommend API", "type": "n8n-nodes-base.httpRequest", "position": [1980, 900], "parameters": {"url": "=http://qdrant:6333/collections/trustpilot_reviews/points/recommend", "method": "POST", "options": {}, "sendBody": true, "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "strategy", "value": "average_vector"}, {"name": "limit", "value": "={{ 3 }}"}, {"name": "positive", "value": "={{ [$json.embeddings[0]] }}"}, {"name": "negative", "value": "={{ [$json.embeddings[1]] }}"}, {"name": "filter", "value": "={{\n$('Operation').first().json.companyIds\n  ? {\n    \"must\": {\n      \"key\": \"metadata.company_id\",\n      \"match\": {\n        \"any\": $('Operation').first().json.companyIds.split(',')\n      }\n    }\n  }\n  : {}\n}}"}, {"name": "with_payload", "value": "={{ true }}"}]}, "nodeCredentialType": "qdrantApi"}, "credentials": {"qdrantApi": {"id": "NyinAS3Pgfik66w5", "name": "QdrantApi account"}}, "typeVersion": 4.2}, {"id": "9d058dae-2f24-4e34-bdb6-ba5649a3b431", "name": "Get Embeddings", "type": "n8n-nodes-base.httpRequest", "position": [1580, 900], "parameters": {"url": "https://api.openai.com/v1/embeddings", "method": "POST", "options": {}, "sendBody": true, "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "input", "value": "={{ $json.text }}"}, {"name": "model", "value": "text-embedding-3-small"}]}, "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 4.2}, {"id": "dfbb9a02-91dd-4dc4-8738-0aff41db2156", "name": "Preferences to Items", "type": "n8n-nodes-base.code", "position": [1380, 900], "parameters": {"jsCode": "return [\n  { text: $input.first().json.text },\n  { text: $input.first().json.text2 }\n]"}, "typeVersion": 2}, {"id": "7d6bda64-4f98-43d1-b181-343238f678bb", "name": "Aggregate Embeddings", "type": "n8n-nodes-base.aggregate", "position": [1780, 900], "parameters": {"options": {}, "fieldsToAggregate": {"fieldToAggregate": [{"renameField": true, "outputFieldName": "embeddings", "fieldToAggregate": "data[0].embedding"}]}}, "typeVersion": 1}, {"id": "8eacac44-9cfd-4aa0-be7d-534ae630a2d4", "name": "Get Embeddings1", "type": "n8n-nodes-base.httpRequest", "position": [1380, 560], "parameters": {"url": "https://api.openai.com/v1/embeddings", "method": "POST", "options": {}, "sendBody": true, "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "input", "value": "={{ $json.text }}"}, {"name": "model", "value": "text-embedding-3-small"}]}, "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 4.2}, {"id": "5b23221c-aeb8-4a4f-b33b-75d46ba7a4fd", "name": "Aggregate Embeddings1", "type": "n8n-nodes-base.aggregate", "position": [1580, 560], "parameters": {"options": {}, "fieldsToAggregate": {"fieldToAggregate": [{"renameField": true, "outputFieldName": "embeddings", "fieldToAggregate": "data[0].embedding"}]}}, "typeVersion": 1}, {"id": "bbbfaf2f-8294-401f-9f01-efeb58e99f15", "name": "Group Search API", "type": "n8n-nodes-base.httpRequest", "position": [1780, 560], "parameters": {"url": "http://qdrant:6333/collections/trustpilot_reviews/points/search/groups", "method": "POST", "options": {}, "jsonBody": "={{\n{\n  \"vector\": $json.embeddings[0],\n  \"group_by\": \"metadata.company_id\",\n  \"limit\": 10,\n  \"group_size\": 3,\n  \"with_payload\": true\n}\n}}", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "qdrantApi"}, "credentials": {"qdrantApi": {"id": "NyinAS3Pgfik66w5", "name": "QdrantApi account"}}, "typeVersion": 4.2}, {"id": "bbed1c97-eebb-47f1-9486-73bade35d290", "name": "Has Results?", "type": "n8n-nodes-base.if", "position": [2600, 560], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a83c0c10-74a7-4a52-b6c4-26dc8313023b", "operator": {"type": "object", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "e995b27a-f331-434c-a828-bc02f978b43c", "name": "Simplify Group Results", "type": "n8n-nodes-base.set", "position": [2180, 560], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "caf5bf23-087a-496f-8d20-56ab70e303a8", "name": "category", "type": "string", "value": "={{ $json.id }}"}, {"id": "db3c2c92-b951-4365-9c19-d5d0f8654a42", "name": "results", "type": "array", "value": "={{\n$json.hits?.map(hit => ({\n  content: hit.payload.content,\n  metadata: hit.payload.metadata\n}))\n}}"}]}}, "typeVersion": 3.4}, {"id": "e0c327a5-ab2e-46ad-9d4b-0fe8cdd1605c", "name": "Empty Compare Response", "type": "n8n-nodes-base.set", "position": [2820, 660], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "24ab771e-0e19-4bfe-bfee-2fed3a34f7fe", "name": "response", "type": "string", "value": "no results."}]}}, "typeVersion": 3.4}, {"id": "52890718-81b4-4880-9a12-b8456e96ad98", "name": "Aggregate Compare Response", "type": "n8n-nodes-base.aggregate", "position": [2820, 440], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "response"}, "typeVersion": 1}, {"id": "bb87afe4-6910-4c79-9371-d49e77134ac3", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [1340, -40], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "ba6e426b-9629-445f-b96c-b8b51fcafe3d", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [1460, -40], "parameters": {"options": {"metadata": {"metadataValues": [{"name": "company_id", "value": "={{ $json.companyIds ?? 'unspecified' }}"}]}}, "jsonData": "={{ $json.text }}", "jsonMode": "expressionData"}, "typeVersion": 1}, {"id": "de9e7ffa-49e8-40de-acef-fd92216ec10c", "name": "Recursive Character Text Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "position": [1560, 80], "parameters": {"options": {}, "chunkSize": 3000}, "typeVersion": 1}, {"id": "9d639f7f-5c3e-478f-ba6f-70b2e06394de", "name": "Simplify Recommend Response", "type": "n8n-nodes-base.set", "position": [2180, 900], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "56f12f89-75dc-4143-ae32-45f1561da19d", "name": "content", "type": "string", "value": "={{ $json.result[0].payload.content }}"}, {"id": "57afc394-3793-4605-a751-8c0d446857e7", "name": "metadata", "type": "object", "value": "={{ $json.result[0].payload.metadata }}"}]}}, "typeVersion": 3.4, "alwaysOutputData": true}, {"id": "736d02f1-8658-4d84-bdc6-3f43ad712a76", "name": "Get Insert Response", "type": "n8n-nodes-base.set", "position": [1780, -200], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "cb612470-0d50-4179-a9af-144e592369a8", "name": "response", "type": "string", "value": "ok"}]}}, "executeOnce": true, "typeVersion": 3.4}, {"id": "2a557659-8668-4dba-b3a7-2bc18981ca10", "name": "Get Search Response", "type": "n8n-nodes-base.aggregate", "position": [1780, 240], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "response"}, "typeVersion": 1}, {"id": "017f0628-f08f-4d7c-b3da-83ada24bcfad", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [1720, 500], "parameters": {"width": 213.30551928619226, "height": 332.38559808882246, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n### 🚨Configure Your Qdrant Connection\n* Be sure to enter your endpoint address"}, "typeVersion": 1}, {"id": "cbbc7999-e2c4-4d84-9c21-7ce47a70ef2d", "name": "Insert Reviews", "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "position": [1380, -200], "parameters": {"mode": "insert", "options": {}, "qdrantCollection": {"__rl": true, "mode": "list", "value": "trustpilot_reviews", "cachedResultName": "trustpilot_reviews"}}, "credentials": {"qdrantApi": {"id": "NyinAS3Pgfik66w5", "name": "QdrantApi account"}}, "typeVersion": 1.1}, {"id": "268dd3b8-631e-498c-8c50-33e61244ef7a", "name": "Search Reviews", "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "position": [1380, 240], "parameters": {"mode": "load", "topK": 10, "prompt": "={{ $json.text }}", "options": {"searchFilterJson": "={{\n$json.companyIds\n  ? {\n    \"must\": [\n      {\n        \"key\": \"metadata.company_id\",\n        \"match\": {\n          \"any\": $json.companyIds.split(',')\n        }\n      }\n    ]\n  }\n  : {}\n}}"}, "qdrantCollection": {"__rl": true, "mode": "list", "value": "trustpilot_reviews", "cachedResultName": "trustpilot_reviews"}}, "credentials": {"qdrantApi": {"id": "NyinAS3Pgfik66w5", "name": "QdrantApi account"}}, "typeVersion": 1.1}, {"id": "45ff74b6-0e9e-4698-98b9-e778f1605df2", "name": "Split Out Companies", "type": "n8n-nodes-base.splitOut", "position": [1980, 560], "parameters": {"options": {}, "fieldToSplitOut": "result.groups"}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "1856fa8e-f184-4cef-b66f-6254e3f323ac", "name": "Filter By CompanyId", "type": "n8n-nodes-base.filter", "position": [2380, 560], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "6b6fac92-c001-4070-b8ed-0c63ef54d293", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{\n$('Operation').item.json.companyIds\n  ? $('Operation').item.json.companyIds.split(',').includes($json.id)\n  : true\n}}", "rightValue": "={{ $json.id }}"}]}}, "typeVersion": 2.2, "alwaysOutputData": true}, {"id": "3c468d41-9462-4ee0-b9c8-97e8213c0f32", "name": "Aggregate Recommend Response", "type": "n8n-nodes-base.aggregate", "position": [2600, 780], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "response"}, "typeVersion": 1}, {"id": "825ee679-b0bd-4b7a-8684-4a1f7959926e", "name": "Has Results?1", "type": "n8n-nodes-base.if", "position": [2380, 900], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a83c0c10-74a7-4a52-b6c4-26dc8313023b", "operator": {"type": "object", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "bfa17c34-7f84-40e2-8c9b-f92c0475fa05", "name": "Empty Compare Response1", "type": "n8n-nodes-base.set", "position": [2600, 1000], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "24ab771e-0e19-4bfe-bfee-2fed3a34f7fe", "name": "response", "type": "string", "value": "no results."}]}}, "typeVersion": 3.4}, {"id": "********-eb7a-40b1-8a4e-1824d0aebe01", "name": "Embeddings OpenAI1", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [1480, 400], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "c3d89756-9073-4640-88ba-eb2c5cf370a1", "name": "ListCompanies", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [-120, 240], "parameters": {"name": "listAvailableCompanies", "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "description": "Call this tool to list all available companies in the reviews database.", "workflowInputs": {"value": {"text": "null", "text2": "null", "operation": "listCompanies", "companyIds": "null"}, "schema": [{"id": "operation", "type": "string", "display": true, "removed": false, "required": false, "displayName": "operation", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "text", "type": "string", "display": true, "removed": false, "required": false, "displayName": "text", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "text2", "type": "string", "display": true, "removed": false, "required": false, "displayName": "text2", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "companyIds", "type": "string", "display": true, "removed": false, "required": false, "displayName": "companyIds", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2.1}, {"id": "d9a02360-f9aa-4aea-b26f-f3482f55fc18", "name": "List by Facet API", "type": "n8n-nodes-base.httpRequest", "position": [1380, -440], "parameters": {"url": "http://qdrant:6333/collections/trustpilot_reviews/facet", "method": "POST", "options": {}, "jsonBody": "{\n  \"key\": \"metadata.company_id\"\n}", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "qdrantApi"}, "credentials": {"qdrantApi": {"id": "NyinAS3Pgfik66w5", "name": "QdrantApi account"}}, "typeVersion": 4.2}, {"id": "6822aee2-7dfe-43ea-8238-ea0a3c8f0188", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-720, -740], "parameters": {"width": 440, "height": 1320, "content": "## Try It Out!\n### This n8n demonstrates how to build your own Qdrant MCP server to extend its functionality beyond that of the official implementation.\n\nThis n8n implementation exposes other cool API features from Qdrant such as facet search, grouped search and recommendations APIs. With this, we can build an easily customisable and maintainable Qdrant MCP server for business intelligence.\n\nThis MCP example is based off an official MCP reference implementation which can be found here -https://github.com/qdrant/mcp-server-qdrant/\n\n### How it works\n* A MCP server trigger is used and connected to 5 custom workflow tools. We're using custom workflow tools as there is quite a few nodes required for each task.\n* We use a mix of n8n supported Qdrant nodes for simple operations such as insert documents and similarity search, and HTTP node to hit the Qdrant API directly for Facet search, group search and recommendations.\n* We use \"Edit Field\" and \"Aggregate\" nodes to return suitable responses to the MCP client.\n\n### How to use\n* This Qdrant MCP server allows any compatible MCP client to manage a Qdrant Collection by supporting select and create operations. You will need to have a collection available before you can use this server. Use the Prerequisite manual steps to get started!\n* Connect your MCP client by following the n8n guidelines here - https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-langchain.mcptrigger/#integrating-with-claude-desktop\n* Try the following queries in your MCP client:\n  * \"Can you help me list the available companies in the collection?\"\n  * \"What do customers say about product deliveries from company X?\"\n  * \"What do customers of company X and company Y say about product ease of use?\"\n\n### Requirements\n* Qdrant for vector store. This can be an a cloud-hosted instance or one you can self-host internally.\n* MCP Client or Agent for usage such as Claude Desktop - https://claude.ai/download\n\n### Customising this workflow\n* Depending on what queries you'll receive, adjust the tool inputs to make it easier for the agent to set the right parameters.\n* Not interested in Reviews? The techniques shared in this template can be used for other types of collections.\n* Remember to set the MCP server to require credentials before going to production and sharing this MCP server with others!"}, "typeVersion": 1}, {"id": "f0b1f04f-b3bd-4ad0-b128-17a96cf52b81", "name": "Create Facet Index", "type": "n8n-nodes-base.httpRequest", "position": [260, -580], "parameters": {"url": "http://qdrant:6333/collections/trustpilot_reviews/index", "method": "PUT", "options": {}, "jsonBody": "{\n  \"field_name\": \"metadata.company_id\",\n  \"field_schema\": \"keyword\"\n}", "sendBody": true, "specifyBody": "json"}, "typeVersion": 4.2}, {"id": "481c298b-c9e7-412e-aaa0-2e1565fabda8", "name": "Create Collection", "type": "n8n-nodes-base.httpRequest", "position": [60, -580], "parameters": {"url": "http://qdrant:6333/collections/trustpilot_reviews", "method": "PUT", "options": {}, "jsonBody": "{\n  \"vectors\": {\n    \"distance\": \"Cosine\",\n    \"size\": 1536\n  }\n}", "sendBody": true, "specifyBody": "json"}, "typeVersion": 4.2}, {"id": "07e1f0f3-6f24-4a67-9b07-75667f496a9a", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-220, -740], "parameters": {"color": 4, "width": 700, "height": 360, "content": "## Prerequisite: Setup Qdrant Collection\nIf you don't have an existing Qdrant Collection, you can use this manual flow to get started.\n1. Creates a collection called \"trustpilot_reviews\"\n2. Creates an index to allow for facet search ie. list of values of a the \"company_id\" metadata key."}, "typeVersion": 1}, {"id": "df0dacdb-4238-475b-9d98-ae7ce9e3142d", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-140, -580], "parameters": {}, "typeVersion": 1}, {"id": "8c0f0e65-82d2-427e-8c6d-2f89bd225f46", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-220, -200], "parameters": {"color": 5, "width": 380, "height": 100, "content": "### Always Authenticate Your Server!\nBefore going to production, it's always advised to enable authentication on your MCP server trigger."}, "typeVersion": 1}], "pinData": {}, "connections": {"Insert": {"ai_tool": [[{"node": "Qdrant MCP Server", "type": "ai_tool", "index": 0}]]}, "Search": {"ai_tool": [[{"node": "Qdrant MCP Server", "type": "ai_tool", "index": 0}]]}, "Compare": {"ai_tool": [[{"node": "Qdrant MCP Server", "type": "ai_tool", "index": 0}]]}, "Operation": {"main": [[{"node": "List by Facet API", "type": "main", "index": 0}], [{"node": "Insert Reviews", "type": "main", "index": 0}], [{"node": "Search Reviews", "type": "main", "index": 0}], [{"node": "Get Embeddings1", "type": "main", "index": 0}], [{"node": "Preferences to Items", "type": "main", "index": 0}]]}, "Recommend": {"ai_tool": [[{"node": "Qdrant MCP Server", "type": "ai_tool", "index": 0}]]}, "Has Results?": {"main": [[{"node": "Aggregate Compare Response", "type": "main", "index": 0}], [{"node": "Empty Compare Response", "type": "main", "index": 0}]]}, "Has Results?1": {"main": [[{"node": "Aggregate Recommend Response", "type": "main", "index": 0}], [{"node": "Empty Compare Response1", "type": "main", "index": 0}]]}, "ListCompanies": {"ai_tool": [[{"node": "Qdrant MCP Server", "type": "ai_tool", "index": 0}]]}, "Recommend API": {"main": [[{"node": "Simplify Recommend Response", "type": "main", "index": 0}]]}, "Get Embeddings": {"main": [[{"node": "Aggregate Embeddings", "type": "main", "index": 0}]]}, "Insert Reviews": {"main": [[{"node": "Get Insert Response", "type": "main", "index": 0}]]}, "Search Reviews": {"main": [[{"node": "Get Search Response", "type": "main", "index": 0}]]}, "Get Embeddings1": {"main": [[{"node": "Aggregate Embeddings1", "type": "main", "index": 0}]]}, "Group Search API": {"main": [[{"node": "Split Out Companies", "type": "main", "index": 0}]]}, "Create Collection": {"main": [[{"node": "Create Facet Index", "type": "main", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Insert Reviews", "type": "ai_embedding", "index": 0}]]}, "Embeddings OpenAI1": {"ai_embedding": [[{"node": "Search Reviews", "type": "ai_embedding", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Insert Reviews", "type": "ai_document", "index": 0}]]}, "Filter By CompanyId": {"main": [[{"node": "Has Results?", "type": "main", "index": 0}]]}, "Split Out Companies": {"main": [[{"node": "Simplify Group Results", "type": "main", "index": 0}]]}, "Aggregate Embeddings": {"main": [[{"node": "Recommend API", "type": "main", "index": 0}]]}, "Preferences to Items": {"main": [[{"node": "Get Embeddings", "type": "main", "index": 0}]]}, "Aggregate Embeddings1": {"main": [[{"node": "Group Search API", "type": "main", "index": 0}]]}, "Simplify Group Results": {"main": [[{"node": "Filter By CompanyId", "type": "main", "index": 0}]]}, "Simplify Recommend Response": {"main": [[{"node": "Has Results?1", "type": "main", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Operation", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Create Collection", "type": "main", "index": 0}]]}}}