{"id": 14, "name": "Onfleet Driver signup message in Slack", "nodes": [{"name": "Onfleet Trigger", "type": "n8n-nodes-base.onfleetTrigger", "position": [460, 300], "webhookId": "a005e163-13a2-4ea2-a127-6e00e30a82f4", "parameters": {"triggerOn": "workerCreated", "additionalFields": {}}, "credentials": {"onfleetApi": {"id": "2", "name": "Onfleet API Key"}}, "typeVersion": 1}, {"name": "<PERSON><PERSON>ck", "type": "n8n-nodes-base.slack", "position": [680, 300], "parameters": {"text": "A new driver has signed up!", "channel": "#new-driver-signup", "attachments": [], "otherOptions": {}}, "credentials": {"slackApi": {"id": "7", "name": "Slack account"}}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Onfleet Trigger": {"main": [[{"node": "<PERSON><PERSON>ck", "type": "main", "index": 0}]]}}}