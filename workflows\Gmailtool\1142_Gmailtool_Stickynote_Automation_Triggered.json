{"id": "7eyNPahKcCuqK39V", "meta": {"instanceId": "a2b23892dd6989fda7c1209b381f5850373a7d2b85609624d7c2b7a092671d44", "templateCredsSetupCompleted": true}, "name": "DeepSeek v3.1", "tags": [{"id": "ZGwSiT2o3NGleZvi", "name": "DeepSeek", "createdAt": "2025-03-28T00:29:11.856Z", "updatedAt": "2025-03-28T00:29:11.856Z"}], "nodes": [{"id": "5ccc1b78-0795-4653-8438-c9a65781e516", "name": "Watch Notion Updates", "type": "n8n-nodes-base.notionTrigger", "position": [-620, -100], "parameters": {"event": "pagedUpdatedInDatabase", "pollTimes": {"item": [{"mode": "everyHour"}]}, "databaseId": {"__rl": true, "mode": "id", "value": "1c33d655-0fd9-8057-ac1a-eabf12d12f6b", "__regex": "^([0-9a-f]{8}-?[0-9a-f]{4}-?[0-9a-f]{4}-?[0-9a-f]{4}-?[0-9a-f]{12})"}}, "credentials": {"notionApi": {"id": "5rz9xchmiSCmcoOx", "name": "Notion account"}}, "typeVersion": 1}, {"id": "f6bcd3cd-6bf9-42d7-a54a-31e945d5730d", "name": "AI Task Planner", "type": "@n8n/n8n-nodes-langchain.agent", "position": [320, -100], "parameters": {"text": "=You are an expert in SEO content writing.\nYour mission is to create, publish, and notify about a search engine-optimized article for a blog.\nHere are the keywords related to my topic:  {{ $('Watch Notion Updates').item.json.Name }}\n\nFollow the steps below:\n\n1. **Write an SEO-optimized article with a maximum of 20 lines** based on the provided information:\n   - Structure the article with a catchy **H1 title**, one or two **H2 subtitles**, and a professional yet accessible tone.\n   - Extract and include relevant keywords from the data.\n   - Optimize for readability: short sentences, clear paragraphs, and a CTA if relevant.\n   - Do not exceed 20 lines of content.\n\n2. **Publish the article on WordPress**, including:\n   - The **title** as the article's headline\n   - The **SEO content** as the body\n\n3. **Send an email** to my address : {{ $json.emailAddress }} containing:\n   - The article's title\n   - The **URL** of the published article on WordPress\n\n4. **Retrieve the list of available Notion tools first** using “Notion Tools”.\n   Then, **add a update the entry to my Notion database** (ID database: {{ $json.notionDatabaseId }}) ID items : {{ $json.notionItemId }}\nwith the following fields:\n   - The 'Name' column is of type 'title'  → {{ $('Watch Notion Updates').item.json.Name }}\n   The 'Subject' column is of type 'rich_text' → [the article's headline]\n   - The 'Content'column is of type 'rich_text' → [The SEO content]\n   - The 'URL' column is of type 'URL': → [The article link]\n   - The 'Status' column is of type 'select' → Select: `publish`\n\nImportant: Ensure that each step is successfully completed **before proceeding to the next**.", "options": {}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "1627a1ae-424e-4124-ac09-bd0f7bc92d2b", "name": "Send Email", "type": "n8n-nodes-base.gmailTool", "position": [380, 160], "webhookId": "f87279e8-34e4-4fd1-81d3-677707e215de", "parameters": {"sendTo": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('To', ``, 'string') }}", "message": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message', ``, 'string') }}", "options": {}, "subject": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Subject', ``, 'string') }}"}, "credentials": {"gmailOAuth2": {"id": "rKxQHWZ2F5XLJmwF", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "c9c6f4f5-59ff-4c58-8fbd-f7cc0bd3eb2d", "name": "Publish Blog Post", "type": "n8n-nodes-base.wordpressTool", "position": [500, 160], "parameters": {"title": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Title', ``, 'string') }}", "additionalFields": {"status": "draft", "content": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Content', ``, 'string') }}"}}, "credentials": {"wordpressApi": {"id": "KIuXvzjOEnOsHKQE", "name": "Wordpress account"}}, "typeVersion": 1}, {"id": "0064198f-cfe0-424b-9a75-afbdd8a67c14", "name": "Notion List Available Tools", "type": "n8n-nodes-mcp.mcpClientTool", "position": [660, 160], "parameters": {}, "credentials": {"mcpClientApi": {"id": "QQbMEB7i2XAAWTSc", "name": "Notion"}}, "typeVersion": 1}, {"id": "fac061a7-0e91-4944-82f6-463db3e418ce", "name": "Notion Run a Tool", "type": "n8n-nodes-mcp.mcpClientTool", "position": [820, 160], "parameters": {"toolName": "={{ $fromAI(\"tool\", \"the tool selected\")  }}", "operation": "executeTool", "toolParameters": "={{ $fromAI('tool_parameters', ``, 'json') }}"}, "credentials": {"mcpClientApi": {"id": "QQbMEB7i2XAAWTSc", "name": "Notion"}}, "typeVersion": 1}, {"id": "378f291a-bea7-47b3-a629-07fb8d3f9110", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-60, -220], "parameters": {"width": 1100, "height": 580, "content": "## Smart Content Automation Workflow\nAutomatically reacts to Notion updates, uses AI to process data, and triggers actions like sending emails or publishing blog posts.\n**Openrouter** : [API](https://openrouter.ai/settings/keys)"}, "typeVersion": 1}, {"id": "5a8d00c1-752d-4573-ace8-e578b58892d4", "name": "Edit Workflow Variables", "type": "n8n-nodes-base.set", "position": [-300, -100], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c06b2d24-1fd7-40f0-aee5-b5d6553e289e", "name": "emailAddress", "type": "string", "value": ""}, {"id": "8a294900-f367-47a2-b260-344b133dc2ff", "name": "notionDatabaseId", "type": "string", "value": ""}, {"id": "a34469ad-5229-4c4d-bc5d-71c88686bd37", "name": "notionItemId", "type": "string", "value": "={{ $json.id }}"}]}}, "typeVersion": 3.4, "alwaysOutputData": true}, {"id": "3e76c55d-9052-4568-9e21-29e8fd305369", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-440, -220], "parameters": {"color": 6, "width": 360, "height": 300, "content": "## Workflow Configuration Panel\n🛠️ **Set your variables here** (email, Slack, Notion, OpenAI model)"}, "typeVersion": 1}, {"id": "27f461de-609a-4743-829c-74705191e692", "name": "DeepSeek Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatDeepSeek", "position": [100, 160], "parameters": {"options": {}}, "credentials": {"deepSeekApi": {"id": "N4JPoebNdVQUNxXH", "name": "DeepSeek account"}}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "1eae918e-03f8-49d8-9dea-cf0e441e679d", "connections": {"Send Email": {"ai_tool": [[{"node": "AI Task Planner", "type": "ai_tool", "index": 0}]]}, "Notion Run a Tool": {"ai_tool": [[{"node": "AI Task Planner", "type": "ai_tool", "index": 0}]]}, "Publish Blog Post": {"ai_tool": [[{"node": "AI Task Planner", "type": "ai_tool", "index": 0}]]}, "DeepSeek Chat Model": {"ai_languageModel": [[{"node": "AI Task Planner", "type": "ai_languageModel", "index": 0}]]}, "Watch Notion Updates": {"main": [[{"node": "Edit Workflow Variables", "type": "main", "index": 0}]]}, "Edit Workflow Variables": {"main": [[{"node": "AI Task Planner", "type": "main", "index": 0}]]}, "Notion List Available Tools": {"ai_tool": [[{"node": "AI Task Planner", "type": "ai_tool", "index": 0}]]}}}