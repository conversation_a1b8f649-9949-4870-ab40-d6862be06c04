{"nodes": [{"id": "9f2dc93f-bae5-4419-8411-d2fff4b31f5e", "name": "Creates an email engagement", "type": "n8n-nodes-base.hubspot", "position": [916, -260], "parameters": {"type": "email", "metadata": {"html": "={{ $('When an email is received').item.json.textHtml || $('When an email is received').item.json.textPlain}}", "subject": "={{ $('When an email is received').item.json.subject }}", "toEmail": ["={{ $('When an email is received').item.json.to }}"], "fromEmail": "={{ $('When an email is received').item.json.from }}"}, "resource": "engagement", "authentication": "oAuth2", "additionalFields": {"associations": {"contactIds": "={{ $json.vid }}"}}}, "credentials": {"hubspotOAuth2Api": {"id": "JxzF93M0SJ00jDD9", "name": "HubSpot account"}}, "typeVersion": 2.1}, {"id": "0a56ec28-afc6-40a9-bf42-4d8742e48eb4", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-140, -40], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "1IOLtYX7aTspCAN8", "name": "OpenAI Pollup"}}, "typeVersion": 1.2}, {"id": "8e53aeb6-7d84-4739-b482-b8cd844b89ac", "name": "Search for the contact via email", "type": "n8n-nodes-base.hubspot", "position": [256, -260], "parameters": {"operation": "search", "authentication": "oAuth2", "filterGroupsUi": {"filterGroupsValues": [{"filtersUi": {"filterValues": [{"value": "={{ $json.output.contact_info.email }}", "propertyName": "email|string"}]}}]}, "additionalFields": {}}, "credentials": {"hubspotOAuth2Api": {"id": "JxzF93M0SJ00jDD9", "name": "HubSpot account"}}, "typeVersion": 2.1, "alwaysOutputData": true}, {"id": "19e54445-d0cb-40f2-a11f-5e4cb22ad7ec", "name": "Parse the mail with AI", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [-120, -260], "parameters": {"text": "=Get all important info from this email like first name, last name, email, phone number, name of the company, country, Postal code, city, etc. Return it as a json.  The email content:  {{ $json.textHtml || $json.textPlain}} \nFrom: {{ $json.from }} \nSubject: {{ $json.subject }}\nDate sent:  {{ $json.date }}", "messages": {"messageValues": [{"message": "=You are a professional Email parser"}]}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.6}, {"id": "8b257214-0001-46aa-84df-cad844e3130b", "name": "When an email is received", "type": "n8n-nodes-base.emailReadImap", "position": [-340, -260], "parameters": {"options": {"forceReconnect": 3}}, "credentials": {"imap": {"id": "g7C5Z9V9vQUbsLIw", "name": "IMAP account"}}, "typeVersion": 2}, {"id": "32820b69-3918-4951-9ddc-45bdbcb60aca", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-440, -400], "parameters": {"color": 4, "width": 280, "height": 300, "content": "## Set receiving email account\n- <PERSON><PERSON><PERSON>ults to an IMAP account node, but you can put a gmail account or any  email trigger"}, "typeVersion": 1}, {"id": "adbed044-08ae-4744-9b0c-09a225860267", "name": "Set the output Json", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [80, -40], "parameters": {"jsonSchemaExample": "{\"contact_info\": \n{\n\"first name\": \n\"<PERSON>\",\n\"last name\": \"<PERSON><PERSON>\",\n\"position\": \n\"CTO\",\n\"company\": \n\"Pollup Data Services\",\n\"email\": \n\"<PERSON>@pollup.net\",\n\"phone\": \n\"+34 *********\",\n\"website\": \n\"https://pollup.net\",\n\"address\": \n{\n\"street\": \n\"Oppelner Str. 32\",\n\"postal_code\": \n\"10997\",\n\"city\": \n\"Berlin\",\n\"country\": \n\"Germany\"\n}}}"}, "typeVersion": 1.2}, {"id": "e58575ee-6ac8-4de1-b4db-8525146efd74", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-140, -400], "parameters": {"color": 4, "width": 320, "height": 300, "content": "## Upgrade the prompt!\nThis is a very simple prompt but oit does the job. Improve it and send it to me!"}, "typeVersion": 1}, {"id": "23465910-0a89-45f7-9bbf-fb17abadc5de", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [200, -400], "parameters": {"color": 4, "width": 840, "height": 400, "content": "## Hubspot integration\n- Search for the contact in hubspot\n- If it is present creates an egagement\n- It it is not, creates it and adds an engagement"}, "typeVersion": 1}, {"id": "f5573c22-85f3-4eda-ba5a-172567827991", "name": "contact exists?", "type": "n8n-nodes-base.if", "position": [476, -260], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "554c2aa3-dbdb-4955-8510-6b09bc762f63", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.id }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "914f2e6b-7a5f-4c9c-bd3b-4bfb2693728d", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [696, -360], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "75c8fc2d-dc8e-4b6c-a853-1dbd8d72f779", "name": "vid", "type": "string", "value": "={{ $json.id }}"}]}}, "typeVersion": 3.4}, {"id": "4c8fa2d1-e3b4-4323-bdc8-3a4e2bbc706d", "name": "Creates contact", "type": "n8n-nodes-base.hubspot", "position": [696, -160], "parameters": {"email": "={{ $('Parse the mail with AI').item.json.output.contact_info.email }}", "options": {}, "authentication": "oAuth2", "additionalFields": {"city": "={{ $('Parse the mail with AI').item.json.output.contact_info.address.city }}", "country": "={{ $('Parse the mail with AI').item.json.output.contact_info.address.country }}", "jobTitle": "={{ $('Parse the mail with AI').item.json.output.contact_info.position }}", "lastName": "={{ $('Parse the mail with AI').item.json.output.contact_info['last name'] }}", "postalCode": "={{ $('Parse the mail with AI').item.json.output.contact_info.address.postal_code }}", "websiteUrl": "={{ $('Parse the mail with AI').item.json.output.contact_info.website }}", "companyName": "={{ $('Parse the mail with AI').item.json.output.contact_info.company }}", "phoneNumber": "={{ $('Parse the mail with AI').item.json.output.contact_info.phone }}", "streetAddress": "={{ $('Parse the mail with AI').item.json.output.contact_info.address.street }}"}}, "credentials": {"hubspotOAuth2Api": {"id": "JxzF93M0SJ00jDD9", "name": "HubSpot account"}}, "typeVersion": 2.1}, {"id": "5f94ba18-49db-4bc0-9f0a-16a9d05ca6b0", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-440, -620], "parameters": {"width": 460, "height": 200, "content": "## Contact me\n- If you need any modification to this workflow\n- if you need some help with this workflow\n- Or if you need any workflow in n8n, Make, or Langchain / Langgraph\n\nWrite to me: [<EMAIL>](mailto:<EMAIL>)"}, "typeVersion": 1}], "connections": {"Edit Fields": {"main": [[{"node": "Creates an email engagement", "type": "main", "index": 0}]]}, "Creates contact": {"main": [[{"node": "Creates an email engagement", "type": "main", "index": 0}]]}, "contact exists?": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}], [{"node": "Creates contact", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Parse the mail with AI", "type": "ai_languageModel", "index": 0}]]}, "Set the output Json": {"ai_outputParser": [[{"node": "Parse the mail with AI", "type": "ai_outputParser", "index": 0}]]}, "Parse the mail with AI": {"main": [[{"node": "Search for the contact via email", "type": "main", "index": 0}]]}, "When an email is received": {"main": [[{"node": "Parse the mail with AI", "type": "main", "index": 0}]]}, "Search for the contact via email": {"main": [[{"node": "contact exists?", "type": "main", "index": 0}]]}}}