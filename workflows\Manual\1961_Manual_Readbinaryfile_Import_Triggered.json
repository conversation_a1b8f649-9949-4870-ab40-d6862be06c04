{"id": "q8GNbRhjQDwDpXoo", "meta": {"instanceId": "0c2f219d911381bce56d337dbc86e66ee815b6ed822f8553d03a4cd4a8f25805", "templateCredsSetupCompleted": true}, "name": "How to automatically import CSV files into postgres", "tags": [], "nodes": [{"id": "9ae270f2-6e32-4a14-8a03-634b9c66004d", "name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [-340, -80], "parameters": {}, "typeVersion": 1}, {"id": "96de1409-9c48-4357-aaef-2202dec478a9", "name": "Read From File", "type": "n8n-nodes-base.readBinaryFile", "position": [-140, -80], "parameters": {"filePath": "/tmp/t1.csv"}, "typeVersion": 1}, {"id": "22b002df-51fd-4074-8741-c9a754996170", "name": "Convert To Spreadsheet", "type": "n8n-nodes-base.spreadsheetFile", "position": [60, -80], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "0ec04e46-be13-40c3-a4a4-60787bf02a1f", "name": "Postgres", "type": "n8n-nodes-base.postgres", "position": [320, -80], "parameters": {"table": {"__rl": true, "mode": "name", "value": "t1"}, "schema": {"__rl": true, "mode": "list", "value": "public", "cachedResultName": "public"}, "columns": {"value": {"id": 0}, "schema": [{"id": "id", "type": "number", "display": true, "removed": false, "required": false, "displayName": "id", "defaultMatch": true, "canBeUsedToMatch": true}, {"id": "name", "type": "string", "display": true, "required": false, "displayName": "name", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": ["id"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "credentials": {"postgres": {"id": "cgLBOWHeiHmIZuFx", "name": "Postgres account"}}, "typeVersion": 2.5}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "332ff892-d7c2-4e11-8119-e95a2ded82e7", "connections": {"Read From File": {"main": [[{"node": "Convert To Spreadsheet", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Read From File", "type": "main", "index": 0}]]}, "Convert To Spreadsheet": {"main": [[{"node": "Postgres", "type": "main", "index": 0}]]}}}