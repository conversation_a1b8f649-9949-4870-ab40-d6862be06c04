{"nodes": [{"id": "9df72ef9-3b9d-40e4-9cb5-a5ada153c0bb", "name": "Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [120, -180], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "options": {}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "wpiZXesxk9S8fkVG", "name": "Google Drive account 2"}}, "typeVersion": 3}, {"id": "e21bb906-658c-4a52-9c7b-b77d6e0e7ea5", "name": "Upload File", "type": "n8n-nodes-base.httpRequest", "position": [360, -180], "parameters": {"url": "https://api.cloud.llamaindex.ai/api/parsing/upload", "method": "POST", "options": {}, "sendBody": true, "contentType": "multipart-form-data", "sendHeaders": true, "bodyParameters": {"parameters": [{"name": "webhook_url", "value": "https://n8n.lowcoding.dev/webhook/0f7f5ebb-8b66-453b-a818-20cc3647c783"}, {"name": "file", "parameterType": "formBinaryData", "inputDataFieldName": "data"}, {"name": "disable_ocr", "value": "true"}, {"name": "disable_image_extraction", "value": "True"}]}, "headerParameters": {"parameters": [{"name": "accept", "value": "application/json"}, {"name": "Authorization", "value": "Bearer "}, {"name": "parsing_instruction", "value": "Please extract invoice line items: Name, Quantity, Unit Price, Amount "}]}}, "typeVersion": 4.2}, {"id": "2a0c2331-4612-4b92-a0cc-b316bc663907", "name": "Google Drive Trigger", "type": "n8n-nodes-base.googleDriveTrigger", "position": [-80, -180], "parameters": {"event": "fileCreated", "options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "mode": "list", "value": "1IC39VXU8rewBU85offxYlBd9QlYzf8S7", "cachedResultUrl": "https://drive.google.com/drive/folders/1IC39VXU8rewBU85offxYlBd9QlYzf8S7", "cachedResultName": "Invoices"}}, "credentials": {"googleDriveOAuth2Api": {"id": "wpiZXesxk9S8fkVG", "name": "Google Drive account 2"}}, "typeVersion": 1}, {"id": "4ad70b03-54f1-4715-9848-56fa6ba18278", "name": "Create Invoice", "type": "n8n-nodes-base.airtable", "position": [400, 340], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appndgSF4faN4jPXi", "cachedResultUrl": "https://airtable.com/appndgSF4faN4jPXi", "cachedResultName": "Philipp's Base"}, "table": {"__rl": true, "mode": "list", "value": "tbloPc7Eay4Cvwysq", "cachedResultUrl": "https://airtable.com/appndgSF4faN4jPXi/tbloPc7Eay4Cvwysq", "cachedResultName": "Invoices"}, "columns": {"value": {}, "schema": [{"id": "Name", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Line Items", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Line Items", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": []}, "options": {}, "operation": "create"}, "credentials": {"airtableTokenApi": {"id": "XT7hvl1w201jtBhx", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "a408eeb4-2dc2-45ff-a989-92676356f596", "name": "Create Line Item", "type": "n8n-nodes-base.airtable", "position": [800, 340], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appndgSF4faN4jPXi", "cachedResultUrl": "https://airtable.com/appndgSF4faN4jPXi", "cachedResultName": "Philipp's Base"}, "table": {"__rl": true, "mode": "list", "value": "tblIuVR9ocAomznzK", "cachedResultUrl": "https://airtable.com/appndgSF4faN4jPXi/tblIuVR9ocAomznzK", "cachedResultName": "Line Items"}, "columns": {"value": {"Qty": "={{ $json.qty }}", "Amount": "={{ parseFloat($json.amount.replace('$', '').trim()) }}", "Invoices": "=[\"{{ $('Create Invoice').item.json.id }}\"]", "Unit price": "={{ parseFloat($json.unit_price.replace('$', '').trim()) }}", "Description": "={{ $json.description }}"}, "schema": [{"id": "Name", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Description", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Qty", "type": "number", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Qty", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Unit price", "type": "number", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Unit price", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Amount", "type": "number", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Amount", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Invoices", "type": "array", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Invoices", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": []}, "options": {}, "operation": "create"}, "credentials": {"airtableTokenApi": {"id": "XT7hvl1w201jtBhx", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "7ee324e8-6df3-48d6-b1b8-6fdb610b1ec7", "name": "OpenAI - Extract Line Items", "type": "n8n-nodes-base.httpRequest", "position": [180, 340], "parameters": {"url": "=https://api.openai.com/v1/chat/completions", "method": "POST", "options": {}, "jsonBody": "={\n    \"model\": \"gpt-4o-mini\",\n    \"messages\": [\n      {\n        \"role\": \"system\",\n        \"content\": {{ JSON.stringify($('Set Fields').item.json.prompt) }}\n      },\n      {\n        \"role\": \"user\",\n        \"content\": {{ JSON.stringify( JSON.stringify($('Webhook').item.json.body.json[0].items) ) }}\n      }\n    ],\n  \"response_format\":{ \"type\": \"json_schema\", \"json_schema\":  {{ $('Set Fields').item.json.schema }}\n\n }\n  }", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "9RivS2BmSh1DDBFm", "name": "OpenAi account 3"}}, "typeVersion": 4.2}, {"id": "eda31919-9091-4d45-bd73-4609b71f93a9", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [-40, 340], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "dc09a5b4-ff6a-4cee-b87e-35de7336ac05", "name": "prompt", "type": "string", "value": "Please, process parsed data and return only needed."}, {"id": "4e0f9af6-517f-42af-9ced-df0e8a7118b0", "name": "schema", "type": "string", "value": "={\n  \"name\": \"generate_schema\",\n  \"description\": \"Generate schema for an array of objects representing items with their descriptions, quantities, unit prices, and amounts.\",\n  \"strict\": true,\n  \"schema\": {\n    \"type\": \"object\",\n    \"required\": [\n      \"items\"\n    ],\n    \"properties\": {\n      \"items\": {\n        \"type\": \"array\",\n        \"description\": \"Array of item objects\",\n        \"items\": {\n          \"type\": \"object\",\n          \"required\": [\n            \"description\",\n            \"qty\",\n            \"unit_price\",\n            \"amount\"\n          ],\n          \"properties\": {\n            \"description\": {\n              \"type\": \"string\",\n              \"description\": \"Description of the item\"\n            },\n            \"qty\": {\n              \"type\": \"string\",\n              \"description\": \"Quantity of the item\"\n            },\n            \"unit_price\": {\n              \"type\": \"string\",\n              \"description\": \"Unit price of the item formatted as a string\"\n            },\n            \"amount\": {\n              \"type\": \"string\",\n              \"description\": \"Total amount for the item formatted as a string\"\n            }\n          },\n          \"additionalProperties\": false\n        }\n      }\n    },\n    \"additionalProperties\": false\n  }\n}"}]}}, "typeVersion": 3.4}, {"id": "cc0d97d8-fb62-43eb-b484-4dd39f8db4b4", "name": "Process Line Items", "type": "n8n-nodes-base.code", "position": [600, 340], "parameters": {"jsCode": "// Get the input from the \"OpenAI - Extract Line Items\" node\nconst input = $(\"OpenAI - Extract Line Items\").first().json;\n\n// Initialize an array for the output\nconst outputItems = [];\n\n// Navigate to the 'content' field in the choices array\nconst content = input.choices[0]?.message?.content;\n\nif (content) {\n  try {\n    // Parse the stringified JSON in the 'content' field\n    const parsedContent = JSON.parse(content);\n\n    // Extract 'items' and add them to the output array\n    if (Array.isArray(parsedContent.items)) {\n      outputItems.push(...parsedContent.items.map(i => ({ json: i })));\n    }\n  } catch (error) {\n    // Handle any parsing errors\n    console.error('Error parsing content:', error);\n  }\n}\n\n// Return the extracted items\nreturn outputItems;\n"}, "typeVersion": 2}, {"id": "741dc44e-6d47-4a77-80c2-5e18b291da33", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [-220, 340], "webhookId": "0f7f5ebb-8b66-453b-a818-20cc3647c783", "parameters": {"path": "0f7f5ebb-8b66-453b-a818-20cc3647c783", "options": {}, "httpMethod": "POST"}, "typeVersion": 2}, {"id": "fbc196c8-7518-4deb-ac47-f37f1b8150eb", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-260, -300], "parameters": {"width": 920, "height": 400, "content": "## Scenario 1\n\n"}, "typeVersion": 1}, {"id": "96368d41-7886-487f-a8a7-e4dac3b01f45", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-280, 240], "parameters": {"width": 1340, "height": 460, "content": "## Scenario 2\n\n"}, "typeVersion": 1}, {"id": "6b7c94d7-c844-4246-ba1a-cea5937792db", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-60, 0], "parameters": {"color": 3, "width": 270, "height": 80, "content": "### Replace Google Drive connection"}, "typeVersion": 1}, {"id": "9c8141d0-428a-44e5-b900-b07fa64db4f5", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [320, 0], "parameters": {"color": 3, "width": 170, "height": 80, "content": "### Replace API key in header"}, "typeVersion": 1}, {"id": "48243fe4-4ed1-43dc-b508-8b3f9472bb67", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [140, 540], "parameters": {"color": 3, "width": 170, "height": 80, "content": "### Replace OpenAI connection"}, "typeVersion": 1}, {"id": "ffc6b530-69ab-4ccb-945d-94f8fdc1e3ab", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [400, 540], "parameters": {"color": 3, "width": 530, "height": 80, "content": "### Replace Airtable connection"}, "typeVersion": 1}, {"id": "15047f43-5f7e-4c70-a754-fffb41c04611", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [-760, 380], "parameters": {"color": 7, "width": 330.5152611046425, "height": 239.5888196628349, "content": "### ... or watch set up video [7 min]\n[![Youtube Thumbnail](https://cflobdhpqwnoisuctsoc.supabase.co/storage/v1/object/public/my_storage/Video%2010%20-%20Parser%20invoices%20Blur.png)](https://youtu.be/E4I0nru-fa8)\n"}, "typeVersion": 1}, {"id": "812f6cc7-a093-41d0-9750-48253d9f04a8", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-1060, -300], "parameters": {"color": 7, "width": 636, "height": 657, "content": "![5min Logo](https://cflobdhpqwnoisuctsoc.supabase.co/storage/v1/object/public/my_storage/banner.png)\n## AI Agent for realtime insights on meetings\n**Made by [<PERSON>](https://www.linkedin.com/in/marklowcoding/) from community [5minAI](https://www.skool.com/5minai)**\n\nTranscribing meetings manually can be tedious and prone to error. This workflow automates the transcription process in real-time, ensuring that key discussions and decisions are accurately captured and easily accessible for later review, thus enhancing productivity and clarity in communications.\n\nThe workflow leverages n8n and LlamaParse to automatically detect new invoices in a designated Google Drive folder, parse essential billing details, and store the extracted data in a structured format. The key functionalities include:\n- Real-time detection of new invoices via Google Drive triggers.\n- Automated HTTP requests to initiate parsing through Lama Cloud.\n- Structured storage of invoice details and line items in a database for future reference.\n\n1. **Google Drive Integration**: Monitors a specific folder in Google Drive for new invoice uploads.\n2. **Parsing with LlamaParse**: Automatically sends invoices for parsing and processes results through webhooks.\n3. **Data Storage in Airtable**: Creates records for invoices and their associated line items, allowing for detailed tracking."}, "typeVersion": 1}, {"id": "a80e6528-cf79-4229-8c58-6856fd86b6e7", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-1060, 380], "parameters": {"color": 7, "width": 280, "height": 626, "content": "### Set up steps\n\n1. **Google Drive Trigger**: \n   - Set up a trigger to detect new files in a specified folder dedicated to invoices.\n\n2. **File Upload to LlamaParse**: \n   - Create an HTTP request that sends the invoice file to LlamaParse for parsing, including relevant header settings and webhook URL.\n\n3. **Webhook Processing**: \n   - Establish a webhook node to handle parsed results from LlamaParse, extracting needed invoice details effectively.\n\n4. **Invoice Record Creation**: \n   - Create initial records for invoices in your database using the parsed details received from the webhook.\n\n5. **Line Item Processing**: \n   - Transform string data into structured line item arrays and create individual records for each item linked to the main invoice."}, "typeVersion": 1}], "pinData": {}, "connections": {"Webhook": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Set Fields": {"main": [[{"node": "OpenAI - Extract Line Items", "type": "main", "index": 0}]]}, "Google Drive": {"main": [[{"node": "Upload File", "type": "main", "index": 0}]]}, "Create Invoice": {"main": [[{"node": "Process Line Items", "type": "main", "index": 0}]]}, "Process Line Items": {"main": [[{"node": "Create Line Item", "type": "main", "index": 0}]]}, "Google Drive Trigger": {"main": [[{"node": "Google Drive", "type": "main", "index": 0}]]}, "OpenAI - Extract Line Items": {"main": [[{"node": "Create Invoice", "type": "main", "index": 0}]]}}}