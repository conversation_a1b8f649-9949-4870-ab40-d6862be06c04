{"id": "116", "name": "Get all the contacts from GetResponse and update them", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "GetResponse", "type": "n8n-nodes-base.getResponse", "position": [450, 300], "parameters": {"options": {}, "operation": "getAll", "returnAll": true}, "credentials": {"getResponseApi": "getresponse-api"}, "typeVersion": 1}, {"name": "IF", "type": "n8n-nodes-base.if", "position": [650, 300], "parameters": {"conditions": {"string": [{"value1": "={{$node[\"GetResponse\"].json[\"campaign\"][\"name\"]}}", "value2": "n8n", "operation": "notEqual"}]}}, "typeVersion": 1}, {"name": "GetResponse1", "type": "n8n-nodes-base.getResponse", "position": [860, 200], "parameters": {"contactId": "={{$node[\"IF\"].json[\"contactId\"]}}", "operation": "update", "updateFields": {"campaignId": "WRVXO"}}, "credentials": {"getResponseApi": "getresponse-api"}, "typeVersion": 1}, {"name": "NoOp", "type": "n8n-nodes-base.noOp", "position": [860, 400], "parameters": {}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"IF": {"main": [[{"node": "GetResponse1", "type": "main", "index": 0}], [{"node": "NoOp", "type": "main", "index": 0}]]}, "GetResponse": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "GetResponse", "type": "main", "index": 0}]]}}}