{"nodes": [{"name": "Typeform Trigger", "type": "n8n-nodes-base.typeformTrigger", "position": [590, 300], "webhookId": "c8e53100-0616-4d3c-95b8-261eb0d1632b", "parameters": {"formId": "dpr2kxSL"}, "credentials": {"typeformApi": "Typeform Access Token"}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [790, 300], "parameters": {"values": {"string": [{"name": "Name", "value": "={{$json[\"Let's start with your name.\"]}}"}, {"name": "Email", "value": "={{$json[\"What's your email address?\"]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "Airtable", "type": "n8n-nodes-base.airtable", "position": [990, 300], "parameters": {"table": "Table 1", "options": {}, "operation": "append", "application": ""}, "credentials": {"airtableApi": "Airtable Credentials n8n"}, "typeVersion": 1}, {"name": "<PERSON><PERSON>ck", "type": "n8n-nodes-base.slack", "position": [1190, 300], "parameters": {"text": "=*New Submission* 🙌\nName: {{$node[\"Set\"].json[\"Name\"]}}\nEmail: {{$node[\"Set\"].json[\"Email\"]}}", "channel": "general", "attachments": [], "otherOptions": {}}, "credentials": {"slackApi": "<PERSON><PERSON>ck Bot Credentials"}, "typeVersion": 1}], "connections": {"Set": {"main": [[{"node": "Airtable", "type": "main", "index": 0}]]}, "Airtable": {"main": [[{"node": "<PERSON><PERSON>ck", "type": "main", "index": 0}]]}, "Typeform Trigger": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}}