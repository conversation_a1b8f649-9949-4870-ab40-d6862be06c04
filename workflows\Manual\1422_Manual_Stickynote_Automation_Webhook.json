{"id": "EWIrJ8e9z7AijmTu", "meta": {"instanceId": "e8ec316b54e91908f34cbfdc330e5d1d5e97aa0ea8f7277c00d8a8a3892c9983", "templateCredsSetupCompleted": true}, "name": "Lead Generation System (Template)", "tags": [], "nodes": [{"id": "03eabaeb-ad13-4764-98de-183325e32cbd", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [160, -80], "parameters": {}, "typeVersion": 1}, {"id": "e7df072c-fba8-4dc2-94ce-ae20a135a633", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [840, -80], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "ab7e33d5-986e-4fba-b4d0-bc47bcd1cf82", "name": "first_name", "type": "string", "value": "={{ $json.first_name }}"}, {"id": "f29e8cf7-3cd0-4ffc-a071-96be9dd1da50", "name": "last_name", "type": "string", "value": "={{ $json.last_name }}"}, {"id": "54ee5cec-ccaf-4f34-8030-d17206abef5d", "name": "email", "type": "string", "value": "={{ $json.email }}"}, {"id": "daf1fa7c-a7fc-4b96-8184-a5569b9ab9a0", "name": "email_status", "type": "string", "value": "={{ $json.email_status }}"}, {"id": "2c7e31e5-42a2-4295-ae8b-108d8a7d409a", "name": "linkedin_url", "type": "string", "value": "={{ $json.linkedin_url }}"}, {"id": "4002f912-0581-4219-8443-96c13133dc76", "name": "headline", "type": "string", "value": "={{ $json.headline }}"}, {"id": "fa92887f-fff5-4ee4-9b80-05115b83f718", "name": "organization", "type": "string", "value": "={{ $json.organization_name }}"}, {"id": "c274e875-6a53-484a-87b1-3c672101603f", "name": "organization_website", "type": "string", "value": "={{ $json.organization_website_url }}"}, {"id": "af6208a2-d064-471b-a417-7d96e9d05803", "name": "organization_linkedin_url", "type": "string", "value": "={{ $json.organization_linkedin_url }}"}, {"id": "d61f2ddb-4c50-4548-8a7f-08261c60c429", "name": "current_job_title", "type": "string", "value": "={{ $json.title }}"}, {"id": "838c77f2-618e-43b2-9df6-e5e1bde39105", "name": "country", "type": "string", "value": "={{ $json.country }}"}, {"id": "9dc784cf-ae01-44e3-afed-16a95192bf71", "name": "city", "type": "string", "value": "={{ $json.city }}"}]}}, "typeVersion": 3.4}, {"id": "3aaa9ea7-0c5d-4ea3-aa10-6cd0125ea91a", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [80, -280], "parameters": {"color": 5, "width": 1200, "height": 520, "content": "## Lead Generation\nGet thousands of enriched leads in seconds."}, "typeVersion": 1}, {"id": "42e3f6de-4ec4-46a7-a7f9-c028d27a677b", "name": "Scrape Leads", "type": "n8n-nodes-base.httpRequest", "position": [380, -80], "parameters": {"url": "=", "options": {}, "jsonBody": "{\n    \"getPersonalEmails\": true,\n    \"getWorkEmails\": true,\n    \"totalRecords\": 500,\n    \"url\": \"\"\n}", "sendBody": true, "specifyBody": "json"}, "typeVersion": 4.2}, {"id": "dfd580f6-99b5-414a-b6f3-77c73edc85ec", "name": "Save Leads in database", "type": "n8n-nodes-base.airtable", "position": [1040, -80], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appy1hlfTk0UuYwRb", "cachedResultUrl": "https://airtable.com/appy1hlfTk0UuYwRb", "cachedResultName": "Lead Gen - <PERSON><PERSON><PERSON>"}, "table": {"__rl": true, "mode": "list", "value": "tbl0rwfpUYkqMiysR", "cachedResultUrl": "https://airtable.com/appy1hlfTk0UuYwRb/tbl0rwfpUYkqMiysR", "cachedResultName": "Leads"}, "columns": {"value": {"city": "={{ $json.city }}", "country": "={{ $json.country }}", "headline": "={{ $json.headline }}", "last_name": "={{ $json.last_name }}", "first_name": "={{ $json.first_name }}", "email_status": "={{ $json.email_status }}", "linkedin_url": "={{ $json.linkedin_url }}", "email_address": "={{ $json.email }}", "current_job_title": "={{ $json.current_job_title }}", "organization_name": "={{ $json.organization }}", "organization_website": "={{ $json.organization_website }}", "organization_linkedin_url": "={{ $json.organization_linkedin_url }}"}, "schema": [{"id": "email_address", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "email_address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "first_name", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "first_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "last_name", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "last_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "headline", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "headline", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedin_url", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "linkedin_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "organization_name", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "organization_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "organization_website", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "organization_website", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "organization_linkedin_url", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "organization_linkedin_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "current_job_title", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "current_job_title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "country", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "country", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "email_status", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "email_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "city", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "city", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "create"}, "credentials": {"airtableTokenApi": {"id": "WKxw33bpSEDiQEaU", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "ff009ef3-c7da-460a-80e5-ba1330631c00", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-400, -280], "parameters": {"color": 3, "width": 460, "height": 180, "content": "## 🚨 readMeFirst 🚨\nThis template is built by [Not Another Marketer](https://notanothermarketer.com)\n\nStep-by-step setup guide: https://notanothermarketer.gitbook.io/\n\nAny questions? [Reach out on X](https://x.com/notanothermrktr) "}, "typeVersion": 1}, {"id": "0915cea5-746a-4dde-9208-885e08644ae2", "name": "Filter leads without email", "type": "n8n-nodes-base.if", "position": [600, -80], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "231ec40a-bd12-46e2-ab6b-a8c4d6728983", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.email }}", "rightValue": ""}]}}, "typeVersion": 2.2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "f574ed33-bd0a-496b-865e-e6be2c1e3060", "connections": {"Edit Fields": {"main": [[{"node": "Save Leads in database", "type": "main", "index": 0}]]}, "Scrape Leads": {"main": [[{"node": "Filter leads without email", "type": "main", "index": 0}]]}, "Filter leads without email": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Scrape Leads", "type": "main", "index": 0}]]}}}