{"id": "ZCAkUSpaxzoRPbse", "meta": {"instanceId": "885b4fb4a6a9c2cb5621429a7b972df0d05bb724c20ac7dac7171b62f1c7ef40", "templateCredsSetupCompleted": true}, "name": "Search & Summarize Web Data with Perplexity, Gemini AI & Bright Data to Webhooks", "tags": [{"id": "Kujft2FOjmOVQAmJ", "name": "Engineering", "createdAt": "2025-04-09T01:31:00.558Z", "updatedAt": "2025-04-09T01:31:00.558Z"}, {"id": "ddPkw7Hg5dZhQu2w", "name": "AI", "createdAt": "2025-04-13T05:38:08.053Z", "updatedAt": "2025-04-13T05:38:08.053Z"}], "nodes": [{"id": "674c6b61-76fa-4ac0-ab32-3f48ed5cba39", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-1140, 400], "parameters": {}, "typeVersion": 1}, {"id": "f6066e4c-4f6f-48fd-b19f-2c25fdc5b8b2", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "notes": "Gemini Experimental Model", "position": [760, 580], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-thinking-exp-01-21"}, "credentials": {"googlePalmApi": {"id": "YeO7dHZnuGBVQKVZ", "name": "Google Gemini(PaLM) Api account"}}, "notesInFlow": true, "typeVersion": 1}, {"id": "e16a1442-924a-4558-90cb-1c9ddc606532", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [940, 580], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "a8d9bc8e-c5f6-4d66-af60-9eecb9f6569c", "name": "Recursive Character Text Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "position": [1040, 800], "parameters": {"options": {}, "chunkOverlap": 100}, "typeVersion": 1}, {"id": "4ba96504-4ca5-43cf-962c-87320a683b09", "name": "If", "type": "n8n-nodes-base.if", "position": [-200, 400], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "6a7e5360-4cb5-4806-892e-5c85037fa71c", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "ready"}]}}, "typeVersion": 2.2}, {"id": "11fbf88d-99f7-453c-946d-65c886bd50b8", "name": "Set Snapshot Id", "type": "n8n-nodes-base.set", "position": [-740, 400], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "2c3369c6-9206-45d7-9349-f577baeaf189", "name": "snapshot_id", "type": "string", "value": "={{ $json.snapshot_id }}"}]}}, "typeVersion": 3.4}, {"id": "2635d7ff-3de9-40af-925e-e391c3fd5f54", "name": "Download Snapshot", "type": "n8n-nodes-base.httpRequest", "position": [140, 200], "parameters": {"url": "=https://api.brightdata.com/datasets/v3/snapshot/{{ $json.snapshot_id }}", "options": {"timeout": 10000}, "sendQuery": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "queryParameters": {"parameters": [{"name": "format", "value": "json"}]}}, "credentials": {"httpHeaderAuth": {"id": "kdbqXuxIR8qIxF7y", "name": "Header Auth account"}}, "typeVersion": 4.2}, {"id": "fe5bff52-4745-4c8f-a5e8-b9b48d421ffe", "name": "Google Gemini Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [380, 380], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "YeO7dHZnuGBVQKVZ", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "8124f050-ad7f-4478-8edf-c4d02193f54c", "name": "Wait", "type": "n8n-nodes-base.wait", "position": [-200, 620], "webhookId": "631cd5de-36b3-4264-88ae-45b30e2c2ccc", "parameters": {"amount": 30}, "typeVersion": 1.1}, {"id": "1926f22c-e269-40e8-a55d-3945810d13e2", "name": "Check on the errors", "type": "n8n-nodes-base.if", "position": [-80, 40], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "b267071c-7102-407b-a98d-f613bcb1a106", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.errors.toString() }}", "rightValue": "0"}]}}, "typeVersion": 2.2}, {"id": "50a8f7ac-bf66-493e-956e-7278ea7702c1", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1140, 40], "parameters": {"width": 400, "height": 240, "content": "## Note\n\nDeals with the Perplexity Search using the Bright Data Web Scrapper API.\n\nThe information extraction and summarization are done to demonstrate the usage of the N8N AI capabilities.\n\n**Please make sure to update the Webhook Notification URL**"}, "typeVersion": 1}, {"id": "4906478c-6f10-4f47-94cc-78e36939e929", "name": "Webhook Notifier", "type": "n8n-nodes-base.httpRequest", "position": [1140, 200], "parameters": {"url": "https://webhook.site/ce41e056-c097-48c8-a096-9b876d3abbf7", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "response", "value": "={{ $json.output }}"}]}}, "typeVersion": 4.2}, {"id": "dd5dcbf3-bc3e-4676-af64-8a41807ba970", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-620, 40], "parameters": {"width": 420, "height": 240, "content": "## LLM Usages\n\nGoogle Gemini Flash Exp model is being used.\n\nInformation extraction is being used for formatting the html to text\n\nSummarization Chain is being used for summarization of the content"}, "typeVersion": 1}, {"id": "4cc0e400-5722-4eaf-ac95-10b0c9592345", "name": "Perplexity Search Request", "type": "n8n-nodes-base.httpRequest", "position": [-920, 400], "parameters": {"url": "https://api.brightdata.com/datasets/v3/trigger", "method": "POST", "options": {}, "jsonBody": "[\n  {\n    \"url\": \"https://www.perplexity.ai\",\n    \"prompt\": \"tell me about BrightData\",\n    \"country\": \"US\"\n  }\n]", "sendBody": true, "sendQuery": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "queryParameters": {"parameters": [{"name": "dataset_id", "value": "gd_m7dhdot1vw9a7gc1n"}, {"name": "include_errors", "value": "true"}]}, "headerParameters": {"parameters": [{}]}}, "credentials": {"httpHeaderAuth": {"id": "kdbqXuxIR8qIxF7y", "name": "Header Auth account"}}, "typeVersion": 4.2}, {"id": "be9cc310-8f0d-4065-8246-aeddde697953", "name": "Check Snapshot Status", "type": "n8n-nodes-base.httpRequest", "position": [-460, 400], "parameters": {"url": "=https://api.brightdata.com/datasets/v3/progress/{{ $json.snapshot_id }}", "options": {}, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "kdbqXuxIR8qIxF7y", "name": "Header Auth account"}}, "typeVersion": 4.2}, {"id": "66efd680-1d4d-4930-9712-ba9fd1b3a3be", "name": "Readable Data Extractor", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [360, 200], "parameters": {"text": "={{ $json.answer_html }}", "options": {}, "attributes": {"attributes": [{"name": "readable content", "description": "Readable Content"}]}}, "typeVersion": 1}, {"id": "3c5b4744-7475-40a6-a1f5-cce2b700c84a", "name": "Summarization of search result", "type": "@n8n/n8n-nodes-langchain.chainSummarization", "position": [760, 200], "parameters": {"options": {}, "operationMode": "documentLoader"}, "typeVersion": 2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "4628ec64-b023-4185-b38f-a74e2de76ec5", "connections": {"If": {"main": [[{"node": "Check on the errors", "type": "main", "index": 0}], [{"node": "Wait", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "Check Snapshot Status", "type": "main", "index": 0}]]}, "Set Snapshot Id": {"main": [[{"node": "Check Snapshot Status", "type": "main", "index": 0}]]}, "Download Snapshot": {"main": [[{"node": "Readable Data Extractor", "type": "main", "index": 0}]]}, "Check on the errors": {"main": [[{"node": "Download Snapshot", "type": "main", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Summarization of search result", "type": "ai_document", "index": 0}]]}, "Check Snapshot Status": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Readable Data Extractor": {"main": [[{"node": "Summarization of search result", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Summarization of search result", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini Chat Model1": {"ai_languageModel": [[{"node": "Readable Data Extractor", "type": "ai_languageModel", "index": 0}]]}, "Perplexity Search Request": {"main": [[{"node": "Set Snapshot Id", "type": "main", "index": 0}]]}, "Summarization of search result": {"main": [[{"node": "Webhook Notifier", "type": "main", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Perplexity Search Request", "type": "main", "index": 0}]]}}}