{"name": "AccountCraft WhatsApp Automation - Infridet", "nodes": [{"id": "1", "name": "Webhook - Lead Capture", "type": "n8n-nodes-base.webhook", "position": [250, 300], "parameters": {"path": "lead-capture", "responseMode": "onReceived"}, "typeVersion": 1}, {"id": "2", "name": "Google Sheets - Backup Log", "type": "n8n-nodes-base.googleSheets", "position": [500, 200], "parameters": {"range": "Leads!A1", "options": {}, "sheetId": "your_google_sheet_id_here", "valueInputMode": "USER_ENTERED"}, "credentials": {"googleApi": "Google Account"}, "typeVersion": 1}, {"id": "3", "name": "FluentCRM - Add Contact", "type": "n8n-nodes-base.httpRequest", "position": [500, 400], "parameters": {"url": "https://your-crm-domain.com/wp-json/fluent-crm/v2/contacts", "method": "POST", "options": {}, "jsonParameters": true, "bodyParametersJson": "{\n  \"email\": \"{{$json[\"email\"]}}\",\n  \"first_name\": \"{{$json[\"name\"]}}\",\n  \"tags\": [\"New Lead\"]\n}"}, "credentials": {"httpBasicAuth": {"user": "your_crm_api_user", "password": "your_crm_api_key"}}, "typeVersion": 1}, {"id": "4", "name": "Send Warmup Email", "type": "n8n-nodes-base.emailSend", "position": [750, 200], "parameters": {"text": "Hey {{$json[\"name\"]}},\n\nThanks for joining Account Craft! We’ll help you build your YouTube channel and earn like a pro. Stay tuned. 🔥\n\nCheers,\n<PERSON><PERSON>", "subject": "Welcome to Account Craft 🚀", "toEmail": "={{$json[\"email\"]}}", "fromEmail": "<EMAIL>"}, "credentials": {"smtp": "SMTP Account"}, "typeVersion": 1}, {"id": "5", "name": "Send WhatsApp via Whinta", "type": "n8n-nodes-base.httpRequest", "position": [1000, 200], "parameters": {"url": "https://api.whinta.com/send", "method": "POST", "options": {}, "jsonParameters": true, "bodyParametersJson": "{\n  \"phone\": \"{{$json[\"phone\"]}}\",\n  \"message\": \"Hey {{$json[\"name\"]}}, <PERSON><PERSON> here from Account Craft 👋 Just saw your form – want help starting your YouTube channel?\"\n}"}, "typeVersion": 1}, {"id": "6", "name": "Update CRM Tag to Customer", "type": "n8n-nodes-base.httpRequest", "position": [1250, 200], "parameters": {"url": "https://your-crm-domain.com/wp-json/fluent-crm/v2/contacts/update", "method": "POST", "options": {}, "jsonParameters": true, "bodyParametersJson": "{\n  \"email\": \"{{$json[\"email\"]}}\",\n  \"tags\": [\"Customer\"]\n}"}, "credentials": {"httpBasicAuth": {"user": "your_crm_api_user", "password": "your_crm_api_key"}}, "typeVersion": 1}], "active": false, "settings": {}, "versionId": "1", "connections": {"Send Warmup Email": {"main": [[{"node": "Send WhatsApp via Whinta", "type": "main", "index": 0}]]}, "Webhook - Lead Capture": {"main": [[{"node": "Google Sheets - Backup Log", "type": "main", "index": 0}, {"node": "FluentCRM - Add Contact", "type": "main", "index": 0}]]}, "Send WhatsApp via Whinta": {"main": [[{"node": "Update CRM Tag to Customer", "type": "main", "index": 0}]]}, "Google Sheets - Backup Log": {"main": [[{"node": "Send Warmup Email", "type": "main", "index": 0}]]}}}