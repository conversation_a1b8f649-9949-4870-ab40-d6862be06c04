{"id": "SvZQB2gsI57KlfvO", "meta": {"instanceId": "a2b23892dd6989fda7c1209b381f5850373a7d2b85609624d7c2b7a092671d44", "templateCredsSetupCompleted": true}, "name": "💥AI Social Video Generator with GPT-4, <PERSON>ling & Blotato —Auto-Post to Instagram, Facebook,, TikTok, Twitter & Pinterest - vide", "tags": [], "nodes": [{"id": "c8cbe0d7-50d5-44a8-9552-ef2531f324d2", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-880, -280], "parameters": {"width": 2640, "height": 420, "content": "# 🟫 STEP 1 — Create Video Using AI\n## This step handles the full video creation pipeline using AI.\n### It starts from a Telegram message containing a prompt, \n### refines the prompt with GPT-4 to suit <PERSON><PERSON>’s video engine, \n### and generates a cinematic video based entirely on text input.\n"}, "typeVersion": 1}, {"id": "127b9d89-c7ef-4dfc-8431-3da68ce098a8", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-880, 180], "parameters": {"width": 2640, "height": 260, "content": "# 🟫 STEP 2 — Add Voice-Over to Video\n## Here, a short-form voice-over script is generated using GPT-4 based on the topic.\n### The script is converted to speech, uploaded, and merged with the AI-generated video — resulting in a fully narrated visual asset.\n"}, "typeVersion": 1}, {"id": "f2406554-ea6c-4664-aa2a-562dc2bc5b39", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-880, 1080], "parameters": {"color": 3, "width": 2640, "height": 540, "content": "# 🟥 STEP 5 — Auto-Publish to 9 Social Platforms\n## The final step automates distribution using Blotato’s API.\n## The video is auto-published to Instagram, YouTube, TikTok, Facebook, \n## LinkedIn, Threads, Twitter (X), Bluesky, and Pinterest \n## — all in one go, with no manual work required.\n"}, "typeVersion": 1}, {"id": "ceb59771-dd83-40f8-bbda-872ca5d81000", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-880, 780], "parameters": {"width": 2640, "height": 260, "content": "# 🟫 STEP 4 — Save Video & Notify via Telegram\n## This step generates a title and caption for the video, \n## saves the content metadata to a Google Sheet for future tracking, \n### And sends both the final video and its description to a Telegram chat for validation or reuse.\n### The script is converted to speech, uploaded, and merged with the AI-generated video — resulting in a fully narrated visual asset.\n"}, "typeVersion": 1}, {"id": "f837f9d4-e072-44e2-813a-16c18e05ecb8", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-880, 480], "parameters": {"width": 2640, "height": 260, "content": "# 🟫 STEP 3 — Add Captions to Enhance Engagement\n## To increase accessibility and boost social engagement, \n## this step overlays professional-looking subtitles on the video using a styling template.\n### This results in a final video that includes visuals, voice-over, and captions.\n"}, "typeVersion": 1}, {"id": "5f13f1cd-b16b-4427-965e-91e2343213d0", "name": "Trigger: Telegram Prompt", "type": "n8n-nodes-base.telegramTrigger", "position": [-180, -160], "webhookId": "3f5195e9-9837-4ad1-a502-aed61a8931ad", "parameters": {"updates": ["message"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "PJcx2VBF9ubQIy3y", "name": "Telegram account 2"}}, "typeVersion": 1.1}, {"id": "64b2ff19-595e-434f-a71d-70be7c1a9a1d", "name": "Extract Prompt & Caption", "type": "n8n-nodes-base.code", "position": [140, -160], "parameters": {"jsCode": "\n  inputText=$input.first().json.message.text;\n  // Remove \"generate video\" prefix (case-insensitive) and trim whitespace\n  const cleaned = inputText.replace(/^generate video/i, '').trim();\n\n  // Split at the first comma only\n  const [videoPrompt, captionIdea] = cleaned.split(/,(.+)/).map(s => s.trim());\n\n  // Return as a JSON object\n  return {\n    videoPrompt,\n    captionIdea\n  };\n"}, "typeVersion": 2}, {"id": "d52f9138-df37-477b-b86b-7a1dd1cb2e7e", "name": "Transform Prompt for Kling (GPT-4)", "type": "@n8n/n8n-nodes-langchain.agent", "position": [480, -160], "parameters": {"text": "={{ $json.videoPrompt }}", "options": {"systemMessage": "=⚙️ System Instructions – AI Agent:\nYou are a prompt optimization assistant for Kling AI, a cutting-edge text-to-video generation platform.\n\n🎯 Mission:\nTransform brief user inputs into cinematic, high-quality video prompts rich in sensory details, spatial context, and dynamic motion—perfectly tuned for Kling AI.\n\n✍️ Instructions:\nExpand short ideas into visually immersive scenes.\n\nUse concrete visual elements: colors, textures, lighting, motion, atmosphere, camera angles.\n\nKeep descriptions concise but vivid, like a scene from a movie script.\n\nOutput only the final prompt—no explanations, no formatting tips.\n\n🌟 Examples:\nUser Input: “Rainy street at night”\nFinal Prompt:\nNeon signs reflect in puddles on a dimly lit city street. Raindrops ripple across a parked motorcycle's chrome. A figure in a trench coat walks past flickering lights as steam rises from a nearby sewer grate. The camera tracks from ground level, capturing water splashing underfoot.\n\nUser Input: “Mountain hike”\nFinal Prompt:\nA hiker climbs a rugged mountain trail at sunrise, golden light hitting snow-capped peaks in the distance. Wind rustles through pine trees as birds soar overhead. The camera pans slowly from behind, revealing a vast valley below bathed in morning mist.\n\nUser Input: “Cozy library”\nFinal Prompt:\nA warm library room with wooden shelves lined with old books. Dust floats in the sunbeams streaming through tall windows. A cat naps on a leather armchair while a hand flips through a weathered novel. The camera gently rotates around the room, revealing small glowing lamps."}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "afac6532-4683-4515-9464-faf44bbfc470", "name": "OpenAI Model Bridge", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [500, 0], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "6h3DfVhNPw9I25nO", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "388c97e3-6c00-4a35-9ee9-85b7db71d221", "name": "Generate Video via Kling API", "type": "n8n-nodes-base.httpRequest", "position": [940, -160], "parameters": {"url": "https://api.piapi.ai/api/v1/task", "method": "POST", "options": {}, "jsonBody": "={\n\"model\": \"kling\",\n\"task_type\": \"video_generation\",\n\"input\": {\n\"prompt\": \"{{ $('Transform Prompt for Kling (GPT-4)').item.json.output }}\",\n\"negative_prompt\": \"\",\n\"cfg_scale\": 0.5,\n\"duration\": 10,\n\"aspect_ratio\": \"9:16\",\n\"version\": \"1.6\",\n\"camera_control\": {\n\"type\": \"simple\",\n\"config\": {\n\"horizontal\": 0,\n\"vertical\": 0,\n\"pan\": -10,\n\"tilt\": 0,\n\"roll\": 0,\n\"zoom\": 0\n}\n},\n\"mode\": \"std\"\n},\n\"config\": {\n\"service_mode\": \"\",\n\"webhook_config\": {\n\"endpoint\": \"\",\n\"secret\": \"\"\n}\n}\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "DB99xYLrmwZl7Sqf", "name": "Header Auth account"}}, "typeVersion": 4.2}, {"id": "a6a6c4bf-a382-4a53-bec9-8291f09c9679", "name": "Wait for Video Generation", "type": "n8n-nodes-base.wait", "position": [1240, -160], "webhookId": "1b0f9389-5af5-40de-8522-170c8fcc76ae", "parameters": {"unit": "minutes", "amount": 7}, "typeVersion": 1.1}, {"id": "07c6e882-131d-4547-9bf7-51b6df8b5527", "name": "Get Generated Video URL", "type": "n8n-nodes-base.httpRequest", "position": [1520, -160], "parameters": {"url": "=https://api.piapi.ai/api/v1/task/{{ $json.data.task_id }}", "options": {"response": {"response": {"responseFormat": "json"}}}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "DB99xYLrmwZl7Sqf", "name": "Header Auth account"}}, "typeVersion": 4.2}, {"id": "53c76bb9-eadc-4e9e-886a-acb3f14e9b50", "name": "Generate Voice-<PERSON>", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [140, 200], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "GPT-4O"}, "options": {}, "messages": {"values": [{"content": "=Task:\nCraft a short-form voice-over script that perfectly fits a 7-second video duration. The script should align with the following topic:\n\n{{ $('Extract Prompt & Caption').item.json.captionIdea }}\n\nRequirements:\n\nMatch the average spoken word count for a 7-second voice-over (approximately 18–22 words, depending on pacing).\n\nThe script must be tight, impactful, and written in a natural spoken tone suitable for narration.\n\nNo intro text or labels — return only the plain voice-over script with no headers or commentary.\n\n✅ Example (For Context Only):\nTopic: \"Why cold showers boost productivity\"\nGenerated Output:\nCold showers shock your system awake, boost focus, and kickstart circulation—training your brain to embrace discomfort."}]}}, "credentials": {"openAiApi": {"id": "6h3DfVhNPw9I25nO", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "8993c2b5-bf34-4ce3-80e0-9a0b1bdc8569", "name": "Convert Sc<PERSON>t to Audio (TTS)", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [500, 200], "parameters": {"input": "={{ $json.message.content }}", "options": {}, "resource": "audio"}, "credentials": {"openAiApi": {"id": "6h3DfVhNPw9I25nO", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "e17a25fd-704a-4c1e-9a43-e3fe84ec2d73", "name": "Upload Audio to Cloudinary", "type": "n8n-nodes-base.httpRequest", "position": [700, 200], "parameters": {"url": "https://api.cloudinary.com/v1_1/dc5wapno3/auto/upload", "options": {}, "sendBody": true, "contentType": "multipart-form-data", "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "file", "parameterType": "formBinaryData", "inputDataFieldName": "data"}, {"name": "upload_preset", "value": "n8n_video"}]}, "genericAuthType": "httpBasicAuth"}, "credentials": {"httpBasicAuth": {"id": "K1UGehJnDI8N25UA", "name": "Unnamed credential"}}, "typeVersion": 4.2}, {"id": "12c785d5-fab8-41cb-9387-3d5f698448b8", "name": "Merge Audio + Video", "type": "n8n-nodes-base.httpRequest", "position": [940, 200], "parameters": {"url": "https://api.json2video.com/v2/movies", "method": "POST", "options": {}, "jsonBody": "={\n\"resolution\": \"custom\",\n\"width\": 720,\n\"height\": 1280,\n\"scenes\": [\n{\n\"elements\": [\n{\n\"type\": \"video\",\n\"src\": \"{{ $('Get Generated Video URL').item.json.data.output.video_url }}\",\n\"resize\": \"cover\"\n},\n{\n\"type\": \"audio\",\n\"src\": \"{{ $json.url }}\"\n}\n]\n}\n]\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpBasicAuth": {"id": "K1UGehJnDI8N25UA", "name": "Unnamed credential"}, "httpHeaderAuth": {"id": "S8rqcKkBDLZ3xAso", "name": "Head<PERSON> Au<PERSON> account 2"}}, "typeVersion": 4.2}, {"id": "7fdfd093-3cb4-47cf-a333-c17ebd896295", "name": "Wait for Audio/Video Fusion", "type": "n8n-nodes-base.wait", "position": [1240, 200], "webhookId": "36dbee1e-d027-43ab-9ae8-7b3b6d233746", "parameters": {"unit": "minutes", "amount": 1}, "typeVersion": 1.1}, {"id": "bacce3e0-547e-45e5-ae4b-127f1e8c3c0d", "name": "Get Video URL with Audio", "type": "n8n-nodes-base.httpRequest", "position": [1520, 200], "parameters": {"url": "=https://api.json2video.com/v2/movies?id={{ $json.project }}", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "S8rqcKkBDLZ3xAso", "name": "Head<PERSON> Au<PERSON> account 2"}}, "typeVersion": 4.2}, {"id": "1bcfd35d-f746-40b6-9caf-0bd86c47b57e", "name": "Wait Before Captioning", "type": "n8n-nodes-base.wait", "position": [700, 500], "webhookId": "6ab51bf7-67da-468f-af3d-3270cf0e7c77", "parameters": {"amount": 30}, "typeVersion": 1.1}, {"id": "7fb36e35-5d96-4fce-9a7d-e3dc5801b838", "name": "Add Captions/Subtitles to Video", "type": "n8n-nodes-base.httpRequest", "position": [940, 500], "parameters": {"url": "https://api.json2video.com/v2/movies", "method": "POST", "options": {}, "jsonBody": "={\n\"id\": \"qbaasr7s\",\n\"resolution\": \"custom\",\n\"quality\": \"high\",\n\"scenes\": [\n{\n\"id\": \"qyjh9lwj\",\n\"comment\": \"Scene 1\",\n\"elements\": []\n}\n],\n\"elements\": [\n{\n\"id\": \"q6dznzcv\",\n\"type\": \"video\",\n\"src\": \"{{ $json.movie.url }}\"\n},\n{\n\"id\": \"q41n9kxp\",\n\"type\": \"subtitles\",\n\"settings\": {\n\"style\": \"classic-progressive\",\n\"position\": \"center-center\",\n\"font-family\": \"Oswald\",\n\"font-size\": 140,\n\"word-color\": \"#ffd700\",\n\"shadow-color\": \"#260B1B\",\n\"line-color\": \"#F1E7F4\",\n\"shadow-offset\": 0,\n\"box-color\": \"#260B1B\",\n\"outline-color\": \"#000000\",\n\"outline-width\": 8\n},\n\"language\": \"en\"\n}\n],\n\"width\": 720,\n\"height\": 1280\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "S8rqcKkBDLZ3xAso", "name": "Head<PERSON> Au<PERSON> account 2"}}, "typeVersion": 4.2}, {"id": "116ec15c-c611-4fba-ab61-5090d777827e", "name": "Wait for Caption Render", "type": "n8n-nodes-base.wait", "position": [1240, 500], "webhookId": "1b1b1f4e-c08b-4b92-adf5-9f8e5f65ce66", "parameters": {"unit": "minutes", "amount": 1}, "typeVersion": 1.1}, {"id": "05ea1d12-50d3-48cf-8b2a-416bcb3dd7a7", "name": "Get Final Video URL (Audio + Captions)", "type": "n8n-nodes-base.httpRequest", "position": [1520, 500], "parameters": {"url": "=https://api.json2video.com/v2/movies?id={{ $json.project }}", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "S8rqcKkBDLZ3xAso", "name": "Head<PERSON> Au<PERSON> account 2"}}, "typeVersion": 4.2}, {"id": "466e2c7c-f50b-4fd8-a862-d2cecb255579", "name": "Generate Social Caption from Voiceover", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [160, 840], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "GPT-4O"}, "options": {}, "messages": {"values": [{"content": "=📲 Prompt: Caption Generator for Voiceover-Based Social Posts\nYour Role:\nCreate a concise and engaging social media caption that builds directly on the voiceover script below:\n{{ $('Generate Voice-Over Script').item.json.message.content }}\n\n🧠 Caption Guidelines:\nKeep it short, compelling, and value-driven.\n\nAvoid generic motivational fluff — focus on real, actionable insight or highlight a problem/solution pattern.\n\n\nStructure:\n\nOne sentence per line.\n\nNote: Do not use this character: \" in the result.\nReturn a single short paragraph with no line breaks and no special characters.\nNote: Do not use this character: \" in the result.\n"}]}}, "credentials": {"openAiApi": {"id": "6h3DfVhNPw9I25nO", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "6591e8e3-db92-4725-ab78-24cbfaf8bebd", "name": "Generate YouTube-Style Title", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [560, 840], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "GPT-4O"}, "options": {}, "messages": {"values": [{"content": "=Act as a YouTube Title Expert.\nBased on the following video description:\n{{ $('Generate Voice-Over Script').item.json.message.content }}\nGenerate a short, punchy, and curiosity-driven YouTube video title that makes people want to click.\nMake it feel urgent, valuable, or surprising — and avoid generic or boring phrases.\nKeep it under 70 characters. Return only the title, no explanations.\nNote: The title must be free of special characters and the character \". Return only a plain text title.\n\n"}]}}, "credentials": {"openAiApi": {"id": "6h3DfVhNPw9I25nO", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "6b32d227-6d63-409f-ad17-0d24ea5dd2e1", "name": "Save Video Metadata to Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [940, 840], "parameters": {"columns": {"value": {"Titre": "={{ $json.message.content }}", "PROMPT": "={{ $('Trigger: Telegram Prompt').item.json.message.text }}", "URL VIDEO": "={{ $('Get Final Video URL (Audio + Captions)').item.json.movie.url }}", "DESCRIPTION": "={{ $('Generate Social Caption from Voiceover').item.json.message.content }}"}, "schema": [{"id": "PROMPT", "type": "string", "display": true, "required": false, "displayName": "PROMPT", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "DESCRIPTION", "type": "string", "display": true, "required": false, "displayName": "DESCRIPTION", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URL VIDEO", "type": "string", "display": true, "required": false, "displayName": "URL VIDEO", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Titre", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Titre", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "id", "value": "="}, "documentId": {"__rl": true, "mode": "id", "value": "="}}, "credentials": {"googleSheetsOAuth2Api": {"id": "51us92xkOlrvArhV", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "6ff5ebe5-b95d-4977-947e-5fce267dd73d", "name": "Send Final Video to Telegram", "type": "n8n-nodes-base.telegram", "position": [1240, 840], "webhookId": "c70c5281-99b5-4eaa-a597-c110fc3e6fab", "parameters": {"file": "={{ $json['URL VIDEO'] }}", "chatId": "={{ $('Trigger: Telegram Prompt').item.json.message.chat.id }}", "operation": "sendVideo", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "PJcx2VBF9ubQIy3y", "name": "Telegram account 2"}}, "typeVersion": 1.2}, {"id": "1886cd48-2486-4507-882e-0b0eb602a78e", "name": "Send Caption Link via Telegram", "type": "n8n-nodes-base.telegram", "position": [1520, 840], "webhookId": "d9e210ef-c704-4478-b78c-21662156b700", "parameters": {"text": "={{ $('Save Video Metadata to Google Sheets').item.json.DESCRIPTION }}\n\nLink here : {{ $('Save Video Metadata to Google Sheets').item.json['URL VIDEO'] }}", "chatId": "={{ $json.result.chat.id }}", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "PJcx2VBF9ubQIy3y", "name": "Telegram account 2"}}, "typeVersion": 1.2}, {"id": "570303c5-190c-450c-8fb7-1a33f3bfaa7a", "name": "Assign Social Media IDs", "type": "n8n-nodes-base.set", "position": [160, 1300], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "{\n  \"instagram_id\": \"1687\",\n  \"youtube_id\": \"873\",\n  \"threads_id\": \"507\",\n  \"tiktok_id\": \"2079\",\n  \"facebook_id\": \"1759\",\n  \"facebook_page_id\": \"***************\",\n  \"twitter_id\": \"1289\",\n  \"linkedin_id\": \"1446\",\n  \"pinterest_id\": \"363\",\n  \"pinterest_board_id\": \"1146658823815436667\",\n  \"bluesky_id\": \"932\"\n}\n"}, "typeVersion": 3.4}, {"id": "5ee6edcf-936d-42ba-bb28-7c02ea28b7ee", "name": "Upload Video to Blotato", "type": "n8n-nodes-base.httpRequest", "position": [380, 1300], "parameters": {"url": "https://backend.blotato.com/v2/media", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "bodyParameters": {"parameters": [{"name": "url", "value": "={{ $('Save Video Metadata to Google Sheets').item.json['URL VIDEO'] }}"}]}, "headerParameters": {"parameters": [{"name": "blotato-api-key"}]}}, "typeVersion": 4.2}, {"id": "b9eb6f28-d2d3-40f7-98aa-e00db4ede869", "name": "Post to Instagram", "type": "n8n-nodes-base.httpRequest", "position": [940, 1120], "parameters": {"url": "https://backend.blotato.com/v2/posts", "method": "POST", "options": {}, "jsonBody": "={\n  \"post\": {\n    \"accountId\": \"{{ $('Assign Social Media IDs').item.json.instagram_id }}\",\n    \"target\": {\n      \"targetType\": \"instagram\"\n    },\n    \"content\": {\n      \"text\": \"{{ $('Save Video Metadata to Google Sheets').item.json.DESCRIPTION }}\",\n      \"platform\": \"instagram\",\n      \"mediaUrls\": [\n        \"{{ $json.url }}\"\n      ]\n    }\n  }\n}\n", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "blotato-api-key"}]}}, "typeVersion": 4.2}, {"id": "b379365c-832a-422c-b42d-6a6ed5dc90d5", "name": "Post to YouTube", "type": "n8n-nodes-base.httpRequest", "position": [1240, 1120], "parameters": {"url": "https://backend.blotato.com/v2/posts", "method": "POST", "options": {}, "jsonBody": "={\n  \"post\": {\n    \"accountId\": \"{{ $('Assign Social Media IDs').item.json.youtube_id }}\",\n    \"target\": {\n      \"targetType\": \"youtube\",\n      \"title\": \"{{ $('Save Video Metadata to Google Sheets').item.json.Titre }}\",\n      \"privacyStatus\": \"unlisted\",\n      \"shouldNotifySubscribers\": \"false\"\n    },\n    \"content\": {\n      \"text\": \"{{ $('Save Video Metadata to Google Sheets').item.json.DESCRIPTION }}\",\n      \"platform\": \"youtube\",\n      \"mediaUrls\": [\n        \"{{ $json.url }}\"\n      ]\n    }\n  }\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "blotato-api-key"}]}}, "typeVersion": 4.2}, {"id": "859b0b81-2b92-47b7-8858-e748b92f1091", "name": "Post to TikTok", "type": "n8n-nodes-base.httpRequest", "position": [1520, 1120], "parameters": {"url": "https://backend.blotato.com/v2/posts", "method": "POST", "options": {}, "jsonBody": "={\n  \"post\": {\n    \"accountId\": \"{{ $('Assign Social Media IDs').item.json.tiktok_id }}\",\n    \"target\": {\n      \"targetType\": \"tiktok\",\n      \"isYourBrand\": \"false\", \n      \"disabledDuet\": \"false\",\n      \"privacyLevel\": \"PUBLIC_TO_EVERYONE\",\n      \"isAiGenerated\": \"true\",\n      \"disabledStitch\": \"false\",\n      \"disabledComments\": \"false\",\n      \"isBrandedContent\": \"false\"\n      \n    },\n    \"content\": {\n      \"text\": \"{{ $('Save Video Metadata to Google Sheets').item.json.DESCRIPTION }}\",\n      \"platform\": \"tiktok\",\n      \"mediaUrls\": [\n        \"{{ $json.url }}\"\n      ]\n    }\n  }\n}\n", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "blotato-api-key"}]}}, "typeVersion": 4.2}, {"id": "bd94c7cb-a9f9-44e2-94e0-e717e8dd2534", "name": "Post to Facebook Page", "type": "n8n-nodes-base.httpRequest", "position": [940, 1300], "parameters": {"url": "https://backend.blotato.com/v2/posts", "method": "POST", "options": {}, "jsonBody": "={\n  \"post\": {\n    \"accountId\": \"{{ $('Assign Social Media IDs').item.json.facebook_id }}\",\n    \"target\": {\n      \"targetType\": \"facebook\",\n      \"pageId\": \"{{ $('Assign Social Media IDs').item.json.facebook_page_id }}\"\n\n      \n    },\n    \"content\": {\n      \"text\": \"{{ $('Save Video Metadata to Google Sheets').item.json.DESCRIPTION }}\",\n      \"platform\": \"facebook\",\n      \"mediaUrls\": [\n        \"{{ $json.url }}\"\n      ]\n    }\n  }\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "blotato-api-key"}]}}, "typeVersion": 4.2}, {"id": "b13a95eb-998e-445a-a27c-0a474d95bf2b", "name": "Post to Threads", "type": "n8n-nodes-base.httpRequest", "position": [1240, 1300], "parameters": {"url": "https://backend.blotato.com/v2/posts", "method": "POST", "options": {}, "jsonBody": "={\n  \"post\": {\n    \"accountId\": \"{{ $('Assign Social Media IDs').item.json.threads_id }}\",\n    \"target\": {\n      \"targetType\": \"threads\"\n      \n    },\n    \"content\": {\n      \"text\": \"{{ $('Save Video Metadata to Google Sheets').item.json.DESCRIPTION }}\",\n      \"platform\": \"threads\",\n      \"mediaUrls\": [\n        \"{{ $json.url }}\"\n      ]\n    }\n  }\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "blotato-api-key"}]}}, "typeVersion": 4.2}, {"id": "d8a0f1a1-3938-493e-8178-e86829571502", "name": "Post to Twitter (X)", "type": "n8n-nodes-base.httpRequest", "position": [1520, 1300], "parameters": {"url": "https://backend.blotato.com/v2/posts", "method": "POST", "options": {}, "jsonBody": "={\n  \"post\": {\n    \"accountId\": \"{{ $('Assign Social Media IDs').item.json.twitter_id }}\",\n    \"target\": {\n      \"targetType\": \"twitter\"\n      \n    },\n    \"content\": {\n      \"text\": \"{{ $('Save Video Metadata to Google Sheets').item.json.DESCRIPTION }}\",\n      \"platform\": \"twitter\",\n      \"mediaUrls\": [\n        \"{{ $json.url }}\"\n      ]\n    }\n  }\n}\n", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "blotato-api-key"}]}}, "typeVersion": 4.2}, {"id": "dd0e8c1d-b4dc-4f11-ac75-580ff7aa8fec", "name": "Post to LinkedIn", "type": "n8n-nodes-base.httpRequest", "position": [940, 1460], "parameters": {"url": "https://backend.blotato.com/v2/posts", "method": "POST", "options": {}, "jsonBody": "={\n  \"post\": {\n    \"accountId\": \"{{ $('Assign Social Media IDs').item.json.linkedin_id }}\",\n    \"target\": {\n      \"targetType\": \"linkedin\"\n      \n    },\n    \"content\": {\n      \"text\": \"{{ $('Save Video Metadata to Google Sheets').item.json.DESCRIPTION }}\",\n      \"platform\": \"linkedin\",\n      \"mediaUrls\": [\n        \"{{ $json.url }}\"\n      ]\n    }\n  }\n}\n", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "blotato-api-key"}]}}, "typeVersion": 4.2}, {"id": "a404095d-47b7-464b-82b1-cbc732e6a0a5", "name": "Post to <PERSON>ky", "type": "n8n-nodes-base.httpRequest", "position": [1240, 1460], "parameters": {"url": "https://backend.blotato.com/v2/posts", "method": "POST", "options": {}, "jsonBody": "={\n  \"post\": {\n    \"accountId\": \"{{ $('Assign Social Media IDs').item.json.bluesky_id }}\",\n    \"target\": {\n      \"targetType\": \"bluesky\"\n      \n    },\n    \"content\": {\n      \"text\": \"{{ $('Save Video Metadata to Google Sheets').item.json.DESCRIPTION }}\",\n      \"platform\": \"bluesky\",\n      \"mediaUrls\": [\n        \"https://pbs.twimg.com/media/GE8MgIiWEAAfsK3.jpg\"\n      ]\n    }\n  }\n}\n", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "blotato-api-key"}]}}, "typeVersion": 4.2}, {"id": "a23845dc-e81d-4eab-817d-d1d8b480e386", "name": "Post to Pinterest", "type": "n8n-nodes-base.httpRequest", "position": [1520, 1460], "parameters": {"url": "https://backend.blotato.com/v2/posts", "method": "POST", "options": {}, "jsonBody": "={\n  \"post\": {\n    \"accountId\": \"{{ $('Assign Social Media IDs').item.json.pinterest_id }}\",\n    \"target\": {\n      \"targetType\": \"pinterest\",\n      \"boardId\": \"{{ $('Assign Social Media IDs').item.json.pinterest_board_id }}\"      \n    },\n    \"content\": {\n      \"text\": \"{{ $('Save Video Metadata to Google Sheets').item.json.DESCRIPTION }}\",\n      \"platform\": \"pinterest\",\n      \"mediaUrls\": [\n        \"https://pbs.twimg.com/media/GE8MgIiWEAAfsK3.jpg\"\n      ]\n    }\n  }\n}\n", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "blotato-api-key"}]}}, "typeVersion": 4.2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "ba0fabe4-69f5-4c38-984b-d1df6ec572b8", "connections": {"Post to Bluesky": {"main": [[]]}, "Post to Threads": {"main": [[]]}, "Post to YouTube": {"main": [[]]}, "Post to LinkedIn": {"main": [[]]}, "Post to Instagram": {"main": [[]]}, "Merge Audio + Video": {"main": [[{"node": "Wait for Audio/Video Fusion", "type": "main", "index": 0}]]}, "OpenAI Model Bridge": {"ai_languageModel": [[{"node": "Transform Prompt for Kling (GPT-4)", "type": "ai_languageModel", "index": 0}]]}, "Post to Facebook Page": {"main": [[]]}, "Wait Before Captioning": {"main": [[{"node": "Add Captions/Subtitles to Video", "type": "main", "index": 0}]]}, "Assign Social Media IDs": {"main": [[{"node": "Upload Video to Blotato", "type": "main", "index": 0}]]}, "Get Generated Video URL": {"main": [[{"node": "Generate Voice-<PERSON>", "type": "main", "index": 0}]]}, "Upload Video to Blotato": {"main": [[{"node": "Post to Instagram", "type": "main", "index": 0}, {"node": "Post to YouTube", "type": "main", "index": 0}, {"node": "Post to TikTok", "type": "main", "index": 0}, {"node": "Post to Facebook Page", "type": "main", "index": 0}, {"node": "Post to Threads", "type": "main", "index": 0}, {"node": "Post to Twitter (X)", "type": "main", "index": 0}, {"node": "Post to LinkedIn", "type": "main", "index": 0}, {"node": "Post to <PERSON>ky", "type": "main", "index": 0}, {"node": "Post to Pinterest", "type": "main", "index": 0}]]}, "Wait for Caption Render": {"main": [[{"node": "Get Final Video URL (Audio + Captions)", "type": "main", "index": 0}]]}, "Extract Prompt & Caption": {"main": [[{"node": "Transform Prompt for Kling (GPT-4)", "type": "main", "index": 0}]]}, "Get Video URL with Audio": {"main": [[{"node": "Wait Before Captioning", "type": "main", "index": 0}]]}, "Trigger: Telegram Prompt": {"main": [[{"node": "Extract Prompt & Caption", "type": "main", "index": 0}]]}, "Wait for Video Generation": {"main": [[{"node": "Get Generated Video URL", "type": "main", "index": 0}]]}, "Generate Voice-Over Script": {"main": [[{"node": "Convert Sc<PERSON>t to Audio (TTS)", "type": "main", "index": 0}]]}, "Upload Audio to Cloudinary": {"main": [[{"node": "Merge Audio + Video", "type": "main", "index": 0}]]}, "Wait for Audio/Video Fusion": {"main": [[{"node": "Get Video URL with Audio", "type": "main", "index": 0}]]}, "Generate Video via Kling API": {"main": [[{"node": "Wait for Video Generation", "type": "main", "index": 0}]]}, "Generate YouTube-Style Title": {"main": [[{"node": "Save Video Metadata to Google Sheets", "type": "main", "index": 0}]]}, "Send Final Video to Telegram": {"main": [[{"node": "Send Caption Link via Telegram", "type": "main", "index": 0}]]}, "Convert Script to Audio (TTS)": {"main": [[{"node": "Upload Audio to Cloudinary", "type": "main", "index": 0}]]}, "Send Caption Link via Telegram": {"main": [[{"node": "Assign Social Media IDs", "type": "main", "index": 0}]]}, "Add Captions/Subtitles to Video": {"main": [[{"node": "Wait for Caption Render", "type": "main", "index": 0}]]}, "Transform Prompt for Kling (GPT-4)": {"main": [[{"node": "Generate Video via Kling API", "type": "main", "index": 0}]]}, "Save Video Metadata to Google Sheets": {"main": [[{"node": "Send Final Video to Telegram", "type": "main", "index": 0}]]}, "Generate Social Caption from Voiceover": {"main": [[{"node": "Generate YouTube-Style Title", "type": "main", "index": 0}]]}, "Get Final Video URL (Audio + Captions)": {"main": [[{"node": "Generate Social Caption from Voiceover", "type": "main", "index": 0}]]}}}