{"id": "215", "name": "Create, update, and get a user using the G Suite Admin node", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [240, 300], "parameters": {}, "typeVersion": 1}, {"name": "G Suite Admin", "type": "n8n-nodes-base.gSuiteAdmin", "position": [440, 300], "parameters": {"domain": "n8n.io", "lastName": "Nat", "password": "sjhdjsdhbajshdb", "username": "nat", "firstName": "<PERSON>", "additionalFields": {}}, "credentials": {"gSuiteAdminOAuth2Api": "Google"}, "typeVersion": 1}, {"name": "G Suite Admin1", "type": "n8n-nodes-base.gSuiteAdmin", "position": [640, 300], "parameters": {"userId": "={{$node[\"G Suite Admin\"].json[\"id\"]}}", "operation": "update", "updateFields": {"lastName": "Nate"}}, "credentials": {"gSuiteAdminOAuth2Api": "Google"}, "typeVersion": 1}, {"name": "G Suite Admin2", "type": "n8n-nodes-base.gSuiteAdmin", "position": [840, 300], "parameters": {"userId": "={{$node[\"G Suite Admin\"].json[\"id\"]}}", "options": {}, "operation": "get"}, "credentials": {"gSuiteAdminOAuth2Api": "Google"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"G Suite Admin": {"main": [[{"node": "G Suite Admin1", "type": "main", "index": 0}]]}, "G Suite Admin1": {"main": [[{"node": "G Suite Admin2", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "G Suite Admin", "type": "main", "index": 0}]]}}}