{"id": "6MRJ2tfl8c2f3AuE", "meta": {"instanceId": "31e69f7f4a77bf465b805824e303232f0227212ae922d12133a0f96ffeab4fef"}, "name": "💥🛠️Build a Web Search Chatbot with GPT-4o and MCP Brave Search", "tags": [], "nodes": [{"id": "b6e5eaa8-ddb3-4c13-8069-ce360bf4a945", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [240, -180], "parameters": {"options": {}}, "typeVersion": 1.8}, {"id": "dde0154e-f7c2-4778-abcc-f79406db5e6b", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-260, -180], "webhookId": "68e54e15-548a-44df-ad06-7fb9e4e912a9", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "877ce640-4d08-4ba7-b1d3-bcfc79600d2c", "name": "MCP Get Brave Tools", "type": "n8n-nodes-mcp.mcpClientTool", "position": [200, 280], "parameters": {}, "credentials": {"mcpClientApi": {"id": "t2IDYWq0EcqBWvMA", "name": "MCP Client (STDIO) account 2"}}, "typeVersion": 1}, {"id": "fb3ce3c2-a809-43e5-92d0-82db0d78a971", "name": "MCP Execute Brave Search", "type": "n8n-nodes-mcp.mcpClientTool", "position": [460, 280], "parameters": {"toolName": "={{ $fromAI('tool', 'Set this with the specific tool name') }}", "operation": "executeTool", "toolParameters": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Tool_Parameters', ``, 'json') }}"}, "credentials": {"mcpClientApi": {"id": "t2IDYWq0EcqBWvMA", "name": "MCP Client (STDIO) account 2"}}, "typeVersion": 1}, {"id": "357bde6a-66d0-48dc-972d-d0b35e3868ed", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [-120, 280], "parameters": {}, "typeVersion": 1.3}, {"id": "3eba14c5-e4ed-4c4f-8f1d-2b5671b462cc", "name": "gpt-4o", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-380, 280], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "gpt-4o"}, "options": {}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "781e5d92-6e9d-4874-93fc-5ea17d11f67f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [120, 160], "parameters": {"color": 4, "height": 280, "content": "## 1️⃣ MCP Get Brave Tools"}, "typeVersion": 1}, {"id": "78a52697-352f-47ed-a7d2-3a65c9641fd7", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [380, 160], "parameters": {"color": 4, "height": 280, "content": "## 2️⃣ MCP Execute Brave Search"}, "typeVersion": 1}, {"id": "876003d5-7d90-4865-af36-3c0e504b02e7", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-200, 160], "parameters": {"color": 3, "height": 280, "content": "## Short Term Chat Memory"}, "typeVersion": 1}, {"id": "9f64f499-73d7-414f-a3d3-02c0417368a6", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-460, 160], "parameters": {"color": 5, "height": 280, "content": "## Cloud LLM"}, "typeVersion": 1}, {"id": "fc423452-832c-4377-9bde-04ab6d5c89aa", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-500, -400], "parameters": {"color": 7, "width": 1200, "height": 920, "content": "# 💥🛠️Your First Simple MCP AI Chatbot using Brave Search\nhttps://github.com/nerding-io/n8n-nodes-mcp\nhttps://brave.com/search/api/"}, "typeVersion": 1}, {"id": "5c6c7307-3283-4698-9104-c80df8a62888", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [80, 40], "parameters": {"width": 580, "height": 440, "content": "## 🛠️ MCP Toolbox\nhttps://github.com/nerding-io/n8n-nodes-mcp\nhttps://brave.com/search/api/"}, "typeVersion": 1}, {"id": "9d1bb515-f8fa-4d48-bbf5-c083f5efd89d", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [-360, -240], "parameters": {"color": 4, "width": 300, "height": 240, "content": "## 👍Try Me!"}, "typeVersion": 1}, {"id": "b093a455-aee7-4822-b079-7d9cbac783c2", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-1060, -400], "parameters": {"width": 520, "height": 1040, "content": "### **Who is this for?**\nThis workflow is ideal for developers, automation enthusiasts, and businesses looking to integrate AI-powered chat capabilities into their workflows. It's particularly useful for those leveraging Brave Search and MCP tools to enhance user interactions and streamline data retrieval.\n\n### **What problem is this workflow solving?**\nThis workflow addresses the challenge of creating an intelligent chatbot that can process user queries, execute searches using Brave Search, and provide responses enriched by AI. It simplifies the integration of multiple tools into a cohesive system, saving time and effort for users who need a robust conversational AI solution.\n\n### **What this workflow does**\n- Listens for incoming chat messages using the **Chat Trigger** node.\n- Processes user input with an **AI Agent** powered by GPT-4o.\n- Retrieves relevant tools using the **MCP Get Brave Tools** node.\n- Executes specific search queries via the **MCP Execute Brave Search** node.\n- Maintains short-term memory of conversations with the **Simple Memory** node.\n\n### **Setup**\n1. **Prerequisites**:\n   - Access to an n8n instance (self-hosted).\n   - API credentials for OpenAI and MCP Client Tools.\n   - Brave Search API key.\n\n2. **Steps**:\n   - Import the workflow JSON into your n8n instance.\n   - Configure the API credentials for OpenAI and MCP Client Tools in their respective nodes.\n   - Set up your Brave Search API key in the MCP nodes. https://brave.com/search/api/\n\n3. **Testing**:\n   - Use the built-in chat interface to send test messages.\n   - Verify that the chatbot processes queries and returns results as expected.\n\n### **How to customize this workflow to your needs**\n- Modify the AI Agent's prompt settings to tailor responses to your specific use case.\n- Adjust the memory buffer in the Simple Memory node to retain more or less conversational context.\n- Replace or add additional tools in the MCP nodes to expand functionality.\n"}, "typeVersion": 1}, {"id": "8fb4f215-da26-43ad-b187-9b52ed6485ba", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [80, -280], "parameters": {"width": 580, "height": 280, "content": "## 🤖 AI Agent with Tools"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "a555f325-abd3-44bd-ac48-8b0f6910824e", "connections": {"gpt-4o": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "MCP Get Brave Tools": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "MCP Execute Brave Search": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}}}