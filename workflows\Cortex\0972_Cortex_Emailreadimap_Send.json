{"id": 4, "name": "Email", "nodes": [{"name": "IMAP Email", "type": "n8n-nodes-base.emailReadImap", "position": [-300, 200], "parameters": {"format": "resolved", "options": {}}, "credentials": {"imap": {"id": "5", "name": "IMAP account"}}, "typeVersion": 1}, {"name": "TheHive", "type": "n8n-nodes-base.theHive", "position": [-20, 200], "parameters": {"tags": "Email", "type": "Email", "title": "={{$node[\"IMAP Email\"].binary.attachment_0.fileName}}", "source": "Outlook", "sourceRef": "={{$node[\"IMAP Email\"].json[\"messageId\"]}}", "artifactUi": {"artifactValues": [{"dataType": "file", "binaryProperty": "attachment_0"}]}, "description": "={{$node[\"IMAP Email\"].binary.attachment_0.fileName}}", "additionalFields": {}}, "credentials": {"theHiveApi": {"id": "1", "name": "The Hive account"}}, "typeVersion": 1, "alwaysOutputData": true}, {"name": "Create Case", "type": "n8n-nodes-base.theHive", "position": [280, 200], "parameters": {"id": "={{$node[\"TheHive\"].json[\"_id\"]}}", "operation": "promote", "additionalFields": {}}, "credentials": {"theHiveApi": {"id": "1", "name": "The Hive account"}}, "typeVersion": 1, "alwaysOutputData": true}, {"name": "Case", "type": "n8n-nodes-base.theHive", "position": [540, 200], "parameters": {"id": "={{$node[\"Create Case\"].json[\"_id\"]}}", "resource": "case", "operation": "get"}, "credentials": {"theHiveApi": {"id": "1", "name": "The Hive account"}}, "typeVersion": 1, "alwaysOutputData": true}, {"name": "Observable", "type": "n8n-nodes-base.theHive", "position": [1060, 200], "parameters": {"caseId": "={{$node[\"Case\"].json[\"_id\"]}}", "options": {}, "resource": "observable", "returnAll": true}, "credentials": {"theHiveApi": {"id": "1", "name": "The Hive account"}}, "typeVersion": 1, "alwaysOutputData": true}, {"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.theHive", "position": [1340, 200], "parameters": {"id": "={{$node[\"Observable\"].json[\"_id\"]}}", "dataType": "file", "resource": "observable", "analyzers": ["24a64a086a410e1c7d7ace74003c4480::CORTEX"], "operation": "executeAnalyzer"}, "credentials": {"theHiveApi": {"id": "1", "name": "The Hive account"}}, "retryOnFail": true, "typeVersion": 1, "alwaysOutputData": true}, {"name": "<PERSON>rtex", "type": "n8n-nodes-base.cortex", "position": [1560, 200], "parameters": {"jobId": "={{$node[\"Analyzer Email\"].json[\"cortexJobId\"]}}", "resource": "job", "operation": "report"}, "credentials": {"cortexApi": {"id": "2", "name": "Cortex account"}}, "typeVersion": 1}, {"name": "IF", "type": "n8n-nodes-base.if", "position": [-20, 600], "parameters": {"conditions": {"number": [{"value1": "={{$node[\"Cortex\"].json[\"report\"][\"full\"][\"iocs\"][\"domain\"].length}}", "operation": "larger"}, {"value1": "={{$node[\"Cortex\"].json[\"report\"][\"full\"][\"iocs\"][\"email\"].length}}", "operation": "larger"}, {"value1": "={{$node[\"Cortex\"].json[\"report\"][\"full\"][\"iocs\"][\"ip\"].length}}", "operation": "larger"}]}, "combineOperation": "any"}, "typeVersion": 1}, {"name": "Update Case Domain", "type": "n8n-nodes-base.theHive", "position": [420, 480], "parameters": {"ioc": true, "data": "={{$node[\"Cortex\"].json[\"report\"][\"full\"][\"iocs\"][\"domain\"]}}", "caseId": "={{$node[\"Case\"].json[\"_id\"]}}", "status": "Ok", "message": "={{$node[\"Cortex\"].json[\"analyzerName\"]}}", "options": {"tags": "Domain"}, "dataType": "domain", "resource": "observable", "operation": "create"}, "credentials": {"theHiveApi": {"id": "1", "name": "The Hive account"}}, "typeVersion": 1}, {"name": "Update Case Email", "type": "n8n-nodes-base.theHive", "position": [420, 620], "parameters": {"ioc": true, "data": "={{$node[\"Cortex\"].json[\"report\"][\"full\"][\"iocs\"][\"email\"]}}", "caseId": "={{$node[\"Case\"].json[\"_id\"]}}", "status": "Ok", "message": "={{$node[\"Cortex\"].json[\"analyzerName\"]}}", "options": {"tags": "Domain"}, "dataType": "mail", "resource": "observable", "operation": "create"}, "credentials": {"theHiveApi": {"id": "1", "name": "The Hive account"}}, "typeVersion": 1}, {"name": "Update Case Ip", "type": "n8n-nodes-base.theHive", "position": [420, 760], "parameters": {"ioc": true, "data": "={{$node[\"Cortex\"].json[\"report\"][\"full\"][\"iocs\"][\"ip\"]}}", "caseId": "={{$node[\"Case\"].json[\"_id\"]}}", "status": "Ok", "message": "={{$node[\"Cortex\"].json[\"analyzerName\"]}}", "options": {"tags": "Domain"}, "dataType": "ip", "resource": "observable", "operation": "create"}, "credentials": {"theHiveApi": {"id": "1", "name": "The Hive account"}}, "typeVersion": 1}, {"name": "Wait", "type": "n8n-nodes-base.wait", "position": [800, 200], "webhookId": "ecada1d5-a671-44fc-906e-c64c6f05e760", "parameters": {"unit": "seconds", "amount": 5}, "typeVersion": 1}, {"name": "Email Reputation", "type": "n8n-nodes-base.theHive", "position": [640, 620], "parameters": {"id": "={{$node[\"Update Case Email\"].json[\"id\"]}}", "dataType": "mail", "resource": "observable", "analyzers": ["9902b4e5c58015184b177de13f2151c7::CORTEX"], "operation": "executeAnalyzer"}, "credentials": {"theHiveApi": {"id": "1", "name": "The Hive account"}}, "typeVersion": 1}, {"name": "OTX IP", "type": "n8n-nodes-base.theHive", "position": [640, 760], "parameters": {"id": "={{$node[\"Update Case Ip\"].json[\"id\"]}}", "dataType": "ip", "resource": "observable", "analyzers": ["b084bf78d1aea92966b6ef6a4f6193a5::CORTEX"], "operation": "executeAnalyzer"}, "credentials": {"theHiveApi": {"id": "1", "name": "The Hive account"}}, "typeVersion": 1}, {"name": "OTX DOMAIN", "type": "n8n-nodes-base.theHive", "position": [640, 480], "parameters": {"id": "={{$node[\"Update Case Domain\"].json[\"id\"]}}", "dataType": "domain", "resource": "observable", "analyzers": ["b084bf78d1aea92966b6ef6a4f6193a5::CORTEX"], "operation": "executeAnalyzer"}, "credentials": {"theHiveApi": {"id": "1", "name": "The Hive account"}}, "typeVersion": 1}], "active": true, "settings": {}, "connections": {"IF": {"main": [[{"node": "Update Case Domain", "type": "main", "index": 0}, {"node": "Update Case Email", "type": "main", "index": 0}, {"node": "Update Case Ip", "type": "main", "index": 0}]]}, "Case": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "Observable", "type": "main", "index": 0}]]}, "Cortex": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}, "TheHive": {"main": [[{"node": "Create Case", "type": "main", "index": 0}]]}, "IMAP Email": {"main": [[{"node": "TheHive", "type": "main", "index": 0}]]}, "Observable": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Create Case": {"main": [[{"node": "Case", "type": "main", "index": 0}]]}, "Analyzer Email": {"main": [[{"node": "<PERSON>rtex", "type": "main", "index": 0}]]}, "Update Case Ip": {"main": [[{"node": "OTX IP", "type": "main", "index": 0}]]}, "Update Case Email": {"main": [[{"node": "Email Reputation", "type": "main", "index": 0}]]}, "Update Case Domain": {"main": [[{"node": "OTX DOMAIN", "type": "main", "index": 0}]]}}}