{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "c499b8cc-7cc8-411d-9c22-d46c7654e169", "name": "Mistral Upload", "type": "n8n-nodes-base.httpRequest", "position": [700, -20], "parameters": {"url": "https://api.mistral.ai/v1/files", "method": "POST", "options": {}, "sendBody": true, "contentType": "multipart-form-data", "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "purpose", "value": "ocr"}, {"name": "file", "parameterType": "formBinaryData", "inputDataFieldName": "data"}]}, "nodeCredentialType": "mistralCloudApi"}, "credentials": {"mistralCloudApi": {"id": "EIl2QxhXAS9Hkg37", "name": "Mistral Cloud account"}}, "typeVersion": 4.2}, {"id": "08cbe4b7-2adc-4ea0-8dfc-af107369b1dd", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-540, -20], "parameters": {}, "typeVersion": 1}, {"id": "965f294a-5d77-4190-ad4f-ff191aba0948", "name": "<PERSON><PERSON><PERSON> Signed URL", "type": "n8n-nodes-base.httpRequest", "position": [900, -20], "parameters": {"url": "=https://api.mistral.ai/v1/files/{{ $json.id }}/url", "options": {}, "sendQuery": true, "sendHeaders": true, "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "expiry", "value": "24"}]}, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}]}, "nodeCredentialType": "mistralCloudApi"}, "credentials": {"mistralCloudApi": {"id": "EIl2QxhXAS9Hkg37", "name": "Mistral Cloud account"}}, "typeVersion": 4.2}, {"id": "7abfb1f8-f6c8-4fd0-a78e-9d4b97a4d6bc", "name": "Import PDF", "type": "n8n-nodes-base.googleDrive", "position": [480, -20], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "15BcE6nXto9lQDHPmwjm7y9JPerAVEutY"}, "options": {}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "yOwz41gMQclOadgu", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "8942c6bf-4d86-4a95-aa4a-c819008e2534", "name": "Import Image", "type": "n8n-nodes-base.googleDrive", "position": [480, 200], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "1a2FcRDWHHncMO8CYxD80uNUBGH1Sy1k2"}, "options": {}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "yOwz41gMQclOadgu", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "94a1c3ca-1ca7-4bb1-9e7c-8314742423ab", "name": "Mistral Upload1", "type": "n8n-nodes-base.httpRequest", "position": [700, 200], "parameters": {"url": "https://api.mistral.ai/v1/files", "method": "POST", "options": {}, "sendBody": true, "contentType": "multipart-form-data", "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "purpose", "value": "ocr"}, {"name": "file", "parameterType": "formBinaryData", "inputDataFieldName": "data"}]}, "nodeCredentialType": "mistralCloudApi"}, "credentials": {"mistralCloudApi": {"id": "EIl2QxhXAS9Hkg37", "name": "Mistral Cloud account"}}, "typeVersion": 4.2}, {"id": "9d075eec-ba0e-41e1-8bdc-a732bc0f9229", "name": "Mistral Signed URL1", "type": "n8n-nodes-base.httpRequest", "position": [900, 200], "parameters": {"url": "=https://api.mistral.ai/v1/files/{{ $json.id }}/url", "options": {}, "sendQuery": true, "sendHeaders": true, "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "expiry", "value": "24"}]}, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}]}, "nodeCredentialType": "mistralCloudApi"}, "credentials": {"mistralCloudApi": {"id": "EIl2QxhXAS9Hkg37", "name": "Mistral Cloud account"}}, "typeVersion": 4.2}, {"id": "f623f066-fc70-40fa-b608-f28a75a8ac8c", "name": "Mistral DOC OCR", "type": "n8n-nodes-base.httpRequest", "position": [1100, -20], "parameters": {"url": "https://api.mistral.ai/v1/ocr", "method": "POST", "options": {}, "jsonBody": "={\n  \"model\": \"mistral-ocr-latest\",\n  \"document\": {\n    \"type\": \"document_url\",\n    \"document_url\": \"{{ $json.url }}\"\n  },\n  \"include_image_base64\": true\n}", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "mistralCloudApi"}, "credentials": {"mistralCloudApi": {"id": "EIl2QxhXAS9Hkg37", "name": "Mistral Cloud account"}}, "typeVersion": 4.2}, {"id": "********-e7c8-451a-8e7b-da7a3b10db02", "name": "Mistral IMAGE OCR", "type": "n8n-nodes-base.httpRequest", "position": [1100, 200], "parameters": {"url": "https://api.mistral.ai/v1/ocr", "method": "POST", "options": {}, "jsonBody": "={\n  \"model\": \"mistral-ocr-latest\",\n  \"document\": {\n    \"type\": \"image_url\",\n    \"image_url\": \"{{ $json.url }}\"\n  }\n}", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "mistralCloudApi"}, "credentials": {"mistralCloudApi": {"id": "EIl2QxhXAS9Hkg37", "name": "Mistral Cloud account"}}, "typeVersion": 4.2}, {"id": "a79524ff-84f0-46db-bb8e-afc18f1ddd40", "name": "Document URL", "type": "n8n-nodes-base.set", "position": [-160, -20], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "1eb5f18b-eb06-48df-8491-d60de75b4855", "name": "url", "type": "string", "value": "=https://pub-d4aa9be14ae34d6ebcebe06f13af667b.r2.dev/multimodal_bank_statement_scan.pdf"}]}}, "typeVersion": 3.4}, {"id": "72b89f33-a67c-4f73-9a78-e8ccd02fbc98", "name": "Image URL", "type": "n8n-nodes-base.set", "position": [-160, 200], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "1eb5f18b-eb06-48df-8491-d60de75b4855", "name": "url", "type": "string", "value": "=https://pub-d4aa9be14ae34d6ebcebe06f13af667b.r2.dev/multimodal_bank_statement_2.png"}]}}, "typeVersion": 3.4}, {"id": "808ccbb3-0dae-4e2c-9166-bf40c589824a", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-320, -160], "parameters": {"color": 7, "width": 680, "height": 580, "content": "### Example 1. Publicly Hosted Files\nThe default way to use Mistral OCR is to give it a public URL of the file you want processed. Great for your own semi-private docs or other people's. If you rather not expose files due to privacy concerns, then you'd want to check out example 2."}, "typeVersion": 1}, {"id": "b968d6ab-9582-495d-84af-f75833701e2a", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [400, -160], "parameters": {"color": 7, "width": 920, "height": 560, "content": "### Example 2. Privately Hosted via Mistral Cloud\nGive Mistral OCR private and secure access to your files by uploading them to Mistral cloud first. Retrieve the file using a signed URL and pass this to Mistral OCR. Benefit of storing via Mistral could be faster cache access and reduced latency for repeat docs."}, "typeVersion": 1}, {"id": "f2c0b30a-49be-4850-b102-f23d0feac0ec", "name": "Mistral DOC OCR1", "type": "n8n-nodes-base.httpRequest", "position": [60, -20], "parameters": {"url": "https://api.mistral.ai/v1/ocr", "method": "POST", "options": {}, "jsonBody": "={\n  \"model\": \"mistral-ocr-latest\",\n  \"document\": {\n    \"type\": \"document_url\",\n    \"document_url\": \"{{ $json.url }}\"\n  }\n}", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "mistralCloudApi"}, "credentials": {"mistralCloudApi": {"id": "EIl2QxhXAS9Hkg37", "name": "Mistral Cloud account"}}, "typeVersion": 4.2}, {"id": "64a28537-e70b-48bc-b580-cc9d5a5a1b80", "name": "Mistral IMAGE OCR1", "type": "n8n-nodes-base.httpRequest", "position": [60, 200], "parameters": {"url": "https://api.mistral.ai/v1/ocr", "method": "POST", "options": {}, "jsonBody": "={\n  \"model\": \"mistral-ocr-latest\",\n  \"document\": {\n    \"type\": \"image_url\",\n    \"image_url\": \"{{ $json.url }}\"\n  }\n}", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "mistralCloudApi"}, "credentials": {"mistralCloudApi": {"id": "EIl2QxhXAS9Hkg37", "name": "Mistral Cloud account"}}, "typeVersion": 4.2}, {"id": "3caaa30b-21f2-4643-93dd-8dff6f3c1920", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-740, -440], "parameters": {"width": 380, "height": 640, "content": "## Document Parsing with Mistral OCR\nUp your structured document parsing game with Mistral's latest release... **Mistral-OCR**!\n* Designed to specifically parse PDF and image files.\n* Handles multiple-page documents and images up to 10k pixels.\n* Each page is conveniently transcribed as markdown only - there is no plain text output.\n* Incredible pricing at only $0.001 per page!\n\n### Requirements\n* You'll need a Mistral Cloud API Key\n* This template only works with the Mistral Cloud API for Mistral OCR."}, "typeVersion": 1}, {"id": "bd4d0c9f-f4a6-4527-8f9d-5af90a2858c2", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1360, -160], "parameters": {"color": 7, "width": 680, "height": 580, "content": "### Example 3. No need for Extraction? Talk Directly with the File!\nIt seems Mistral were also able to integrate OCR capabilities into its text models which allows you to carry out tasks such as document classification and sentiment analysis really quickly. Unfortunately, it doesn't work the same way with images - you have to use Pixtral but the results are really bad!"}, "typeVersion": 1}, {"id": "c9a9d4bb-4ee6-413a-9f08-97fdcee22bf2", "name": "Document URL1", "type": "n8n-nodes-base.set", "position": [1520, -20], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "1eb5f18b-eb06-48df-8491-d60de75b4855", "name": "url", "type": "string", "value": "=https://pub-d4aa9be14ae34d6ebcebe06f13af667b.r2.dev/multimodal_bank_statement_scan.pdf"}, {"id": "c639fce3-6967-444d-be18-6c9ce802ef22", "name": "query", "type": "string", "value": "what is the total number of deposits?"}]}}, "typeVersion": 3.4}, {"id": "5de4d62c-af8f-4e6d-adbd-2f591a2165f7", "name": "Document Understanding", "type": "n8n-nodes-base.httpRequest", "position": [1740, -20], "parameters": {"url": "https://api.mistral.ai/v1/chat/completions", "method": "POST", "options": {}, "jsonBody": "={\n  \"model\": \"mistral-small-latest\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": [\n        {\n          \"type\": \"text\",\n          \"text\": \"{{ $json.query }}\"\n        },\n        {\n          \"type\": \"document_url\",\n          \"document_url\": \"{{ $json.url }}\"\n        }\n      ]\n    }\n  ],\n  \"document_image_limit\": 8,\n  \"document_page_limit\": 64\n}", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "mistralCloudApi"}, "credentials": {"mistralCloudApi": {"id": "EIl2QxhXAS9Hkg37", "name": "Mistral Cloud account"}}, "typeVersion": 4.2}, {"id": "9a7bbff0-d446-469d-aa61-838e8c025ad5", "name": "Image URL1", "type": "n8n-nodes-base.set", "position": [1520, 200], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "1eb5f18b-eb06-48df-8491-d60de75b4855", "name": "url", "type": "string", "value": "=https://pub-d4aa9be14ae34d6ebcebe06f13af667b.r2.dev/multimodal_bank_statement_2.png"}, {"id": "639cd062-ebef-44ab-97a2-79ee388f8b41", "name": "query", "type": "string", "value": "what is the total number of deposits?"}]}}, "typeVersion": 3.4}, {"id": "ca43d437-a373-4938-a0a8-8087a98d46a8", "name": "Document Mis-Understanding?", "type": "n8n-nodes-base.httpRequest", "position": [1740, 200], "parameters": {"url": "https://api.mistral.ai/v1/chat/completions", "method": "POST", "options": {}, "jsonBody": "={\n  \"model\": \"pixtral-large-latest\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": [\n        {\n          \"type\": \"text\",\n          \"text\": \"{{ $json.query }}\"\n        },\n        {\n          \"type\": \"image_url\",\n          \"image_url\": \"{{ $json.url }}\"\n        }\n      ]\n    }\n  ],\n  \"document_image_limit\": 8,\n  \"document_page_limit\": 64\n}", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "mistralCloudApi"}, "credentials": {"mistralCloudApi": {"id": "EIl2QxhXAS9Hkg37", "name": "Mistral Cloud account"}}, "typeVersion": 4.2}], "pinData": {}, "connections": {"Image URL": {"main": [[{"node": "Mistral IMAGE OCR1", "type": "main", "index": 0}]]}, "Image URL1": {"main": [[{"node": "Document Mis-Understanding?", "type": "main", "index": 0}]]}, "Import PDF": {"main": [[{"node": "Mistral Upload", "type": "main", "index": 0}]]}, "Document URL": {"main": [[{"node": "Mistral DOC OCR1", "type": "main", "index": 0}]]}, "Import Image": {"main": [[{"node": "Mistral Upload1", "type": "main", "index": 0}]]}, "Document URL1": {"main": [[{"node": "Document Understanding", "type": "main", "index": 0}]]}, "Mistral Upload": {"main": [[{"node": "<PERSON><PERSON><PERSON> Signed URL", "type": "main", "index": 0}]]}, "Mistral Upload1": {"main": [[{"node": "Mistral Signed URL1", "type": "main", "index": 0}]]}, "Mistral DOC OCR1": {"main": [[]]}, "Mistral Signed URL": {"main": [[{"node": "Mistral DOC OCR", "type": "main", "index": 0}]]}, "Mistral Signed URL1": {"main": [[{"node": "Mistral IMAGE OCR", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Document URL", "type": "main", "index": 0}, {"node": "Image URL", "type": "main", "index": 0}]]}}}