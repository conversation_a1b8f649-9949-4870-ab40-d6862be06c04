{"id": "1", "name": "Google Sheet to Mailchimp", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [110, 300], "parameters": {}, "typeVersion": 1}, {"name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [530, 300], "parameters": {"range": "sheetone!A:C", "options": {}, "sheetId": "1jwEoPPrkQ2qYMYLZ_I0hlME_Ya_p2YZvaxG10Nf_R20"}, "credentials": {"googleApi": "Google mailchimp"}, "typeVersion": 1}, {"name": "Mailchimp", "type": "n8n-nodes-base.mailchimp", "position": [720, 300], "parameters": {"list": "90d12734de", "email": "={{$node[\"Google Sheets\"].json[\"email\"]}}", "status": "subscribed", "options": {}}, "credentials": {"mailchimpApi": "Google mailchimp"}, "typeVersion": 1}, {"name": "Interval", "type": "n8n-nodes-base.interval", "position": [290, 300], "parameters": {"interval": 2}, "typeVersion": 1}], "active": true, "settings": {}, "connections": {"Interval": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}, "Google Sheets": {"main": [[{"node": "Mailchimp", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[]]}}}