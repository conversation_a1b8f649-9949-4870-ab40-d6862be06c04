{"meta": {"instanceId": "95b3ab5a70ab1c8c1906357a367f1b236ef12a1409406fd992f60255f0f95f85", "templateCredsSetupCompleted": true}, "nodes": [{"id": "aec24e02-fc90-482f-98b0-ba1fe8e069ef", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [140, -240], "parameters": {"color": 4, "width": 380, "height": 880, "content": "## Data reception via Webhook call or message"}, "typeVersion": 1}, {"id": "16d48a81-06cf-4c58-8769-8e8fd90ed735", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [540, 40], "parameters": {"color": 5, "width": 380, "height": 600, "content": "## Data filtering and message check"}, "typeVersion": 1}, {"id": "b137f46c-2e00-42be-a708-b6d9e803cde7", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [940, -240], "parameters": {"width": 380, "height": 560, "content": "## Sending WhatsApp message templates"}, "typeVersion": 1}, {"id": "661df01d-7f5c-429f-a1ea-c29278e76f29", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [940, 340], "parameters": {"color": 3, "width": 380, "height": 300, "content": "## Contact subscription and tagging"}, "typeVersion": 1}, {"id": "b44aba0c-1ecc-44f2-bd6c-66e903a0b5e7", "name": "New message in WhatsApp", "type": "n8n-nodes-base.whatsAppTrigger", "notes": "Listens for incoming WhatsApp messages. This serves as the entry point of the workflow, capturing the message content and sender details for routing decisions.", "position": [320, 140], "webhookId": "e2861f19-0da7-4320-878c-6ec0e138a7d4", "parameters": {"options": {}, "updates": ["messages"]}, "credentials": {"whatsAppTriggerApi": {"id": "hGrWILflNJ7mqZq6", "name": "Ricardo'S WhatsApp OAuth account"}}, "notesInFlow": true, "typeVersion": 1}, {"id": "018da945-7aca-45ca-a1dc-a25d6ed1eeb7", "name": "Cancellation check", "type": "n8n-nodes-base.switch", "notes": "Evaluates incoming WhatsApp message content to determine if it begins with the keyword 'STOP' (ignoring whitespace and case). This allows routing messages either towards support or subscription logic.", "position": [780, 140], "parameters": {"rules": {"values": [{"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "fb517cd9-362b-4ea2-b9c0-7aaad80255b4", "operator": {"type": "string", "operation": "notStartsWith"}, "leftValue": "={{ \n// Normalize the message content to lowercase and remove all spaces\n$json.messages[0].text.body.toLowerCase().replace(/\\s+/g, '') }}", "rightValue": "stop"}]}}, {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "55d55779-eb4d-4562-a462-8dbcfc85852d", "operator": {"type": "string", "operation": "startsWith"}, "leftValue": "={{ \n// Normalize the message content to lowercase and remove all spaces\n$json.messages[0].text.body.toLowerCase().replace(/\\s+/g, '') }}", "rightValue": "stop"}]}}]}, "options": {}}, "notesInFlow": true, "typeVersion": 3.2}, {"id": "7d13f787-95f7-4c13-8674-ef20c82e6fa1", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> Outbound triggered", "type": "CUSTOM.klicktippTrigger", "notes": "Triggers this workflow when a relevant event occurs in KlickTipp. Used to initiate notifications via WhatsApp message templates when subscriber data changes or a specific event is captured.", "position": [320, -140], "webhookId": "ede76771-57d8-440e-8daf-73cc4c27b7cb", "parameters": {}, "credentials": {"klickTippApi": {"id": "K9JyBdCM4SZc1cXl", "name": "DEMO KlickTipp account"}}, "notesInFlow": true, "typeVersion": 1}, {"id": "964324f7-a818-46e6-b51f-************", "name": "Sending WhatsApp offer template", "type": "n8n-nodes-base.whatsApp", "notes": "Sends a WhatsApp message template when the KlickTipp trigger is activated. This is typically used to confirm an action, notify about updates, or alert based on subscriber activity.", "position": [1060, -140], "webhookId": "fd384a0a-0356-490c-bc7c-9be38ef7754f", "parameters": {"template": "offer_for_manual|de", "components": {"component": [{"bodyParameters": {"parameter": [{"text": "={{ $json.CustomFieldFirstName }}"}, {"text": "={{ $json.CustomField217373 }}"}, {"text": "={{ $json.CustomField217511 }}"}]}}, {"type": "button", "sub_type": "url", "buttonParameters": {"parameter": {"text": "={{ $json.CustomField218042 }}", "type": "text"}}}]}, "phoneNumberId": "***************", "recipientPhoneNumber": "={{ //Formats phone numbers by replacing the international dialing prefix eg. (0049) with the plus format (+49)\n$json.PhoneNumber.replace(/^00/, '+') }}"}, "credentials": {"whatsAppApi": {"id": "HqfpRQa1HyDz8IQI", "name": "Ricardo's WhatsApp account"}}, "notesInFlow": true, "typeVersion": 1}, {"id": "629c4059-c03e-4b66-841e-674f03519a3f", "name": "Sending WhatsApp auto-responder template", "type": "n8n-nodes-base.whatsApp", "notes": "Sends a WhatsApp template message to the sender when their message begins with 'STOP', signaling intent to reach support. Personalizes the message using the sender’s name.", "position": [1060, 140], "webhookId": "632b8645-0d1c-479c-875b-b04e01dcff34", "parameters": {"template": "auto_forward_to_support|de", "components": {"component": [{"bodyParameters": {"parameter": [{"text": "={{ \n// Insert the profile name of the contact to personalize the message\n$json.contacts[0].profile.name }}"}]}}]}, "phoneNumberId": "***************", "recipientPhoneNumber": "={{ \n// Extract the phone number of the sender from the message\n$json.messages[0].from }}"}, "credentials": {"whatsAppApi": {"id": "HqfpRQa1HyDz8IQI", "name": "Ricardo's WhatsApp account"}}, "notesInFlow": true, "typeVersion": 1}, {"id": "a5142a5b-d0cc-4965-8462-588477641d3f", "name": "Subscribe number to opt-out from WA messages", "type": "CUSTOM.klicktipp", "notes": "Subscribes the WhatsApp sender to the KlickTipp list using their phone number. Formats the number with a '+' prefix for compatibility with KlickTipp.", "position": [1060, 460], "parameters": {"listId": "358895", "resource": "subscriber", "operation": "subscribe", "smsNumber": "={{\n// Add a \"+\" prefix to the WhatsApp ID to align with expected format in KlickTipp\n'+' + $json.contacts[0].wa_id }}"}, "credentials": {"klickTippApi": {"id": "K9JyBdCM4SZc1cXl", "name": "DEMO KlickTipp account"}}, "notesInFlow": true, "typeVersion": 2}, {"id": "3593831c-4c99-441b-9424-c59440feba3b", "name": "Filter user messages", "type": "n8n-nodes-base.filter", "notes": "This node filters out the messages that come from users responding to automated messages. Otherwise automated messages would trigger the flow.", "position": [580, 140], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "c3399312-f3df-4a89-9ce4-3e7773b025fb", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.messages[0] }}", "rightValue": ""}]}}, "notesInFlow": true, "typeVersion": 2.2}, {"id": "96d54af1-44c1-48c0-9bf3-269e2d084a5c", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [240, 660], "parameters": {"color": 7, "width": 988, "height": 1109, "content": "### Introduction\nThis workflow enables the automated delivery of personalized WhatsApp messages via WhatsApp Business Cloud triggered by KlickTipp and processes the user's responses to control campaigns in KlickTipp. The setup is ideal for use cases like birthday greetings, coupon codes, or product-specific campaigns using pre-approved WhatsApp templates.\n\n### Benefits\n- **Multi-channel automation**: Enrich your email campaigns with WhatsApp messages, ensuring higher open and engagement rates.\n- **Personalized outreach**: Templates can dynamically insert contact-specific info such as name, product, or promo link.\n- **Full integration**: Connect KlickTipp and WhatsApp through n8n for seamless, event-driven messaging.\n\n### Key Feature\n- **KlickTipp Trigger**: Starts the workflow when a contact is tagged via Outbound.\n- **WhatsApp Template Messaging and response processing**:\n  - Uses pre-approved WhatsApp Message Templates (required for messages outside of a 24h session).\n  - Fills dynamic placeholders with data from KlickTipp custom fields such as:\n    - First name\n    - Product name\n    - Discount link\n    - Sender name\n  - Supports CTAs like \"Redeem Now\" with dynamic URLs - you can control the ending of the base URL.\n  - Listens to the contacts responses and either answers with an auto responder or tags the contact in KlickTipp\n\n#### Setup Instructions\n1. Set up the KlickTipp and WhatsApp Business nodes in your n8n instance.\n2. Authenticate your KlickTipp and Whatsapp accounts.\n3. Create the necessary custom fields to match the data structure\n4. Verify and customize field assignments in the workflow to align with your specific form and subscriber list setup.\n\nCustom Fields:\n   - `Whatsapp_Produkt/Dienstleistung` (Zeile)  \n   - `Whatsapp_Name/Unternehmen` (Zeile)  \n   - `Whatsapp_Link_Endung` (Zeile)\n\n### Testing and Deployment:\n1. Test the workflow by triggering the activation Tag of your Outbound in KlickTipp or by sending a response to the offer template message. Fill the custom fields with all the necessary data beforehand.\n2. Verify the reception of the message template from WhatsApp and or the subscription and tagging in KlickTipp.\n\n> ⚠️ *Cooldown Warning*: Repeated tests via Outbound trigger may require a 6-hour wait unless routed through a campaign, which bypasses the cooldown.\n\n- **Customization**: Adjust templates per product or audience segment. Use custom domain endings to redirect to different product pages. Segment users by WhatsApp availability (e.g. fallback to email for non-WA users).\n\n### Campaign Expansion Ideas\n- Combine with KlickTipp email series (e.g. welcome mail + WA message).\n- Add product-based segmentation tags (e.g. `product_X_interest`).\n- Analyze click rates from WhatsApp CTAs and experiment with A/B message variants.\n\n"}, "typeVersion": 1}], "pinData": {}, "connections": {"Cancellation check": {"main": [[{"node": "Sending WhatsApp auto-responder template", "type": "main", "index": 0}], [{"node": "Subscribe number to opt-out from WA messages", "type": "main", "index": 0}]]}, "Filter user messages": {"main": [[{"node": "Cancellation check", "type": "main", "index": 0}]]}, "New message in WhatsApp": {"main": [[{"node": "Filter user messages", "type": "main", "index": 0}]]}, "KlickTipp Outbound triggered": {"main": [[{"node": "Sending WhatsApp offer template", "type": "main", "index": 0}]]}}}