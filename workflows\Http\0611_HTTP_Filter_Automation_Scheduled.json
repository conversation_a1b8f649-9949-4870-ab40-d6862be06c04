{"meta": {"instanceId": "38d37c49298b42c645e6a7693766d7c3522b24e54454034f955422b5d7af611c"}, "nodes": [{"id": "b6582c37-00c3-467c-95cb-fc6eb7ccd27d", "name": "Filter", "type": "n8n-nodes-base.filter", "position": [1080, 420], "parameters": {"conditions": {"number": [{"value1": "={{ $json.meta.total }}", "value2": 4, "operation": "largerEqual"}]}}, "typeVersion": 1}, {"id": "54b0f895-7e56-40eb-bc6c-f657457d004a", "name": "List Snapshots for a Droplet", "type": "n8n-nodes-base.httpRequest", "position": [840, 420], "parameters": {"url": "=https://api.digitalocean.com/v2/droplets/{{ $json.droplets[0].id }}/snapshots ", "options": {}, "authentication": "headerAuth"}, "credentials": {"httpHeaderAuth": {"id": "1kwUrzy4cJXZx48R", "name": "Digital Ocean Account (<EMAIL>)"}}, "typeVersion": 1, "alwaysOutputData": false}, {"id": "7c47438f-db04-41f7-aed6-a460d0a6889b", "name": "List All Droplets", "type": "n8n-nodes-base.httpRequest", "notes": "f3bc462f9219860aafe79747ee369e2f79ccd7f9b096dfe66b55d946871e8942", "position": [600, 420], "parameters": {"url": "https://api.digitalocean.com/v2/droplets", "options": {}, "authentication": "headerAuth"}, "credentials": {"httpHeaderAuth": {"id": "1kwUrzy4cJXZx48R", "name": "Digital Ocean Account (<EMAIL>)"}}, "typeVersion": 1}, {"id": "e751f6a4-0fdc-4be5-84f0-fecba100da09", "name": "Delete a Snapshot", "type": "n8n-nodes-base.httpRequest", "notes": "f3bc462f9219860aafe79747ee369e2f79ccd7f9b096dfe66b55d946871e8942", "position": [1320, 420], "parameters": {"url": "=https://api.digitalocean.com/v2/snapshots/{{ $json.snapshots[0].id }}", "options": {}, "requestMethod": "DELETE", "authentication": "headerAuth"}, "credentials": {"httpHeaderAuth": {"id": "1kwUrzy4cJXZx48R", "name": "Digital Ocean Account (<EMAIL>)"}}, "typeVersion": 1}, {"id": "d4cc4a72-f909-4c10-bada-e5c731e46c5e", "name": "Droplet Actions snapshot (n8n.optimus01.co.za)", "type": "n8n-nodes-base.httpRequest", "notes": "f3bc462f9219860aafe79747ee369e2f79ccd7f9b096dfe66b55d946871e8942", "position": [1560, 420], "parameters": {"url": "=https://api.digitalocean.com/v2/droplets/{{ $('List All Droplets').item.json.droplets[0].id }}/actions ", "options": {}, "requestMethod": "POST", "authentication": "headerAuth", "bodyParametersUi": {"parameter": [{"name": "type", "value": "snapshot"}]}}, "credentials": {"httpHeaderAuth": {"id": "1kwUrzy4cJXZx48R", "name": "Digital Ocean Account (<EMAIL>)"}}, "typeVersion": 1}, {"id": "4f3be74a-add7-4a2c-99df-d5d47f17efee", "name": "Runs every 48hrs", "type": "n8n-nodes-base.cron", "position": [360, 420], "parameters": {"triggerTimes": {"item": [{"mode": "everyX", "value": 48}]}}, "typeVersion": 1}, {"id": "518a7b8c-adf6-448e-9f4a-5acc0f31523d", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [300, 180], "parameters": {"color": 7, "width": 232.*************, "height": 411.*************, "content": "## Trigger workflow every 48 hours\n\nThis node triggers the workflow to run every 48 hours. You can adjust the frequency if needed to suit your snapshot management requirements."}, "typeVersion": 1}, {"id": "70fe9177-e770-4f19-8fbc-3782167dda55", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [540, 180], "parameters": {"color": 5, "width": 232.*************, "height": 411.*************, "content": "## Get all droplets from DigitalOcean\nFetches a list of all the droplets in your DigitalOcean account. This is the first step in managing snapshots for each droplet.\n"}, "typeVersion": 1}, {"id": "04d74698-0198-45c8-8a79-183fd5f19820", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [780, 180], "parameters": {"color": 5, "width": 232.*************, "height": 412.*************, "content": "## Retrieve snapshots for a droplet\nRetrieves all the snapshots associated with a specific droplet. This ensures that we know how many snapshots currently exist for each droplet.\n"}, "typeVersion": 1}, {"id": "4a971e9a-dfdf-4932-8280-3991a83c2a72", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1020, 180], "parameters": {"color": 7, "width": 232.*************, "height": 411.*************, "content": "## Check if there are more than 4 snapshots\nChecks if the number of snapshots for a droplet is equal to or greater than 4. If true, it proceeds to delete the oldest snapshot.\n"}, "typeVersion": 1}, {"id": "bb9a553a-a8fc-4b72-b0e0-704ebaf8b806", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [1260, 180], "parameters": {"color": 5, "width": 232.*************, "height": 411.*************, "content": "## Delete the oldest snapshot\nDeletes the oldest snapshot from the droplet if the number of snapshots exceeds the limit (4 in this case), based on the filter's condition.\n"}, "typeVersion": 1}, {"id": "1811812f-db56-494a-8ffa-d64cc6f5037c", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [1500, 180], "parameters": {"color": 5, "width": 232.*************, "height": 411.*************, "content": "## Create a new snapshot\nCreates a new snapshot for the droplet after cleaning up the old snapshots. Ensures that backups are always up to date."}, "typeVersion": 1}, {"id": "cb2bd85e-578b-4888-9625-ffed7249082c", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-545, 200], "parameters": {"color": 7, "width": 366.**************, "height": 381.*************, "content": "### What this workflow does\n1. **`Runs every 48 hours`**: The workflow is triggered by a cron node that runs every 48 hours, ensuring timely snapshot management.\n2. **`List all droplets`**: The workflow retrieves all droplets in the DigitalOcean account.\n3. **`Retrieve snapshots`**: For each droplet, the workflow retrieves a list of existing snapshots.\n4. **`Filter snapshots`**: If the number of snapshots exceeds 4, the workflow filters for snapshots that need to be deleted.\n5. **`Delete snapshots`**: Excess snapshots are automatically deleted based on the filter criteria.\n6. **`Create new snapshot`**: After cleaning up, the workflow creates a new snapshot for each droplet, ensuring that backups are always up-to-date."}, "typeVersion": 1}, {"id": "7fbb406b-9343-4d3c-9876-80cb3b7bd51e", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [-165, 240], "parameters": {"color": 6, "width": 396.*************, "height": 282.*************, "content": "### Get More Templates Like This 👇\n[![Video Thumbnail](https://onlinethinking.io/wp-content/uploads/2024/10/Lets-Automate-It-Community.png)](http://onlinethinking.io/community)\n"}, "typeVersion": 1}, {"id": "8afb93b2-e547-4f3b-be25-5ab85a3f477d", "name": "Sticky Note14", "type": "n8n-nodes-base.stickyNote", "position": [-545, 600], "parameters": {"width": 777.0408639013781, "height": 201.45195676871373, "content": "## Setup\n1. **`DigitalOcean API Key`**: You’ll need to configure the HTTP Request nodes with your DigitalOcean API key. This key is required for authenticating requests to list droplets, retrieve snapshots, delete snapshots, and create new ones.\n2. **`Snapshot Threshold`**: By default, the workflow is set to keep no more than 4 snapshots per droplet. This can be adjusted by modifying the filter node conditions.\n3. **`Set Execution Frequency`**: The cron node is set to run every 48 hours, but you can adjust the timing to suit your needs."}, "typeVersion": 1}, {"id": "325a4b9c-9bd4-4f29-8595-98f0579d15c1", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [-560, 60], "parameters": {"color": 7, "width": 809.515353297114, "height": 944.3745310796205, "content": "## Automate Droplet Snapshot Management on DigitalOcean\nBuilt for the [Let's Automate It Community](http://onlinethinking.io/community) by [Optimus Agency](https://optimus01.co.za/)\n\nThis workflow automates the management of DigitalOcean Droplet snapshots by keeping the number of snapshots under a defined limit, deleting the oldest ones, and ensuring new snapshots are created at regular intervals."}, "typeVersion": 1}, {"id": "9540cfa4-4b72-40c2-b1d1-5bf3f9bd7884", "name": "Sticky Note15", "type": "n8n-nodes-base.stickyNote", "position": [-545, 820], "parameters": {"color": 7, "width": 777.0408639013781, "height": 168.5111194243667, "content": "## How to customize this workflow\n- **`Adjust Snapshot Limit`**: Change the value in the filter node if you want to keep more or fewer snapshots.\n- **`Modify Run Frequency`**: The workflow runs every 48 hours by default. You can change the frequency in the cron node to run more or less often.\n- **`Enhance with Notifications`**: You can add a notification node (e.g., Slack or email) to alert you when snapshots are deleted or created."}, "typeVersion": 1}], "pinData": {}, "connections": {"Filter": {"main": [[{"node": "Delete a Snapshot", "type": "main", "index": 0}]]}, "Runs every 48hrs": {"main": [[{"node": "List All Droplets", "type": "main", "index": 0}]]}, "Delete a Snapshot": {"main": [[{"node": "Droplet Actions snapshot (n8n.optimus01.co.za)", "type": "main", "index": 0}]]}, "List All Droplets": {"main": [[{"node": "List Snapshots for a Droplet", "type": "main", "index": 0}]]}, "List Snapshots for a Droplet": {"main": [[{"node": "Filter", "type": "main", "index": 0}]]}}}