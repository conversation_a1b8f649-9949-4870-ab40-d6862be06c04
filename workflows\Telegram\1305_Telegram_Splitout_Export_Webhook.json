{"id": "Dctc6QKyRXK17oEq", "meta": {"instanceId": "ca87034fd2d5cf9c52373260a6e37ca15a4a059ecc2971ac41c079ce4aa21138", "templateCredsSetupCompleted": true}, "name": "All-in-One Telegram/<PERSON>row AI Assistant 🤖🧠 Voice/Photo/Save Notes/Long Term Mem", "tags": [], "nodes": [{"id": "bbcaac28-f07c-421b-91c6-e3b2d024a71f", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-900, 80], "parameters": {"color": 4, "width": 340, "height": 380, "content": "## Retrieve Long Term Memories\nBaserow"}, "typeVersion": 1}, {"id": "399c0a44-72b5-4e71-b578-3e4eb8087b3c", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [-180, 420], "parameters": {"width": 280, "height": 380, "content": "## Save To Current Chat Memory"}, "typeVersion": 1}, {"id": "d0438c1d-42e4-47d0-b23b-78f9cee5a28c", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [140, 420], "parameters": {"color": 4, "width": 280, "height": 380, "content": "## Save Long Term Memories\nBaserow"}, "typeVersion": 1}, {"id": "d8cbf733-2a69-45d5-9767-43e8c67a43ee", "name": "gpt-4o-mini", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-400, 580], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "lpt7A1AOIMshaaE7", "name": "OpenAi account"}}, "typeVersion": 1.1}, {"id": "3cd6b923-b369-45e6-a3c1-5fff74a3074c", "name": "Chat Response", "type": "n8n-nodes-base.set", "position": [220, 160], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d6f68b1c-a6a6-44d4-8686-dc4dcdde4767", "name": "output", "type": "string", "value": "={{ $json.output }}"}]}}, "typeVersion": 3.4}, {"id": "912bf06d-ae8f-4513-a0c9-83c1edb1c159", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-500, 420], "parameters": {"color": 3, "width": 280, "height": 380, "content": "## LLM"}, "typeVersion": 1}, {"id": "d5cf2019-d9bb-4fb6-b95c-e9e9f9ac5808", "name": "Telegram Response", "type": "n8n-nodes-base.telegram", "position": [560, 180], "webhookId": "2ae9d05c-68e8-440a-9f13-08e5ef92bf6c", "parameters": {"text": "={{ $json.output }}", "chatId": "={{ $('Listen for Telegram Events').item.json.body.message.chat.id }}", "additionalFields": {"parse_mode": "HTML", "appendAttribution": false}}, "credentials": {"telegramApi": {"id": "EDuT6lIMLHRxdqqN", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "0858e353-8d83-437c-90bc-f57b31e92d1c", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [440, 60], "parameters": {"width": 360, "height": 300, "content": "## Telegram \n(Optional)"}, "typeVersion": 1}, {"id": "c315e630-2ed3-4141-a715-ab08849336be", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-540, 80], "parameters": {"color": 5, "width": 1320, "height": 780, "content": "## AI AGENT with Long Term Memory & Note Storage"}, "typeVersion": 1}, {"id": "2d09319d-f3be-43b8-a134-929992d4f469", "name": "AI Tools Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-140, 180], "parameters": {"text": "={{\n  (() => {\n    try {\n      return $('Edit Fields').item.json.text;\n    } catch (e) {\n      return undefined;\n    }\n  })() ||\n  (() => {\n    try {\n      return $('Edit Fields1').item.json.text;\n    } catch (e) {\n      return undefined;\n    }\n  })() ||\n  (() => {\n    try {\n      return $('Transcribe Recording').item.json.text;\n    } catch (e) {\n      return undefined;\n    }\n  })() ||\n  'default value' // optional default if all are undefined\n}}\n", "options": {"systemMessage": "=## ROLE  \nYou are a friendly, attentive, and helpful AI assistant. Your primary goal is to assist the user while maintaining a personalized and engaging interaction. When possible, address the user by their first name to create a warmer, more personal experience.\n\n---\n\n## KEY TOOLS: MEMORY & NOTE MANAGEMENT\n\nThese are the core features of this workflow. **You do not need permission to save notes and memories; it’s your decision based on the conversation.**\n\n### Memory Management  \n- **When to Use:**  \n  Evaluate each incoming message for noteworthy or personal information (e.g., preferences, habits, goals, important events, etc.).  \n- **Action:**  \n  If the message contains such details, autonomously use the **Save Memory** Baserow tool to store a clear, concise summary.  \n- **Response:**  \n  Always provide a meaningful reply that naturally acknowledges the input without revealing that a memory was saved.\n\n### Note Management  \n- **When to Use:**  \n  If the user shares specific instructions, reminders, or standalone pieces of information meant to be retained, use the **Save Note** Baserow tool.  \n- **Action:**  \n  Save these as concise notes without asking for permission.  \n- **Response:**  \n  Your reply should integrate the note context naturally, ensuring that the note’s purpose is honored without overemphasizing the saving action.\n\n---\n\n## GENERAL RULES\n\n1. **Context Awareness:**  \n   - Use stored memories and notes to craft contextually relevant and personalized responses.  \n   - Always consider the date and time when a memory or note was collected to ensure your responses are up-to-date.\n\n2. **User-Centric Responses:**  \n   - Tailor your responses based on the user's preferences and past interactions.  \n   - If the user’s first name is provided in the user info section, address them by name when it feels natural and appropriate.  \n   - Be proactive in recalling relevant details from memory or notes without overwhelming the conversation.\n\n3. **Privacy and Sensitivity:**  \n   - Handle all user data with care. Do not share or expose stored information unless it directly enhances the interaction.  \n   - Never store passwords or usernames.\n\n4. **Fallback Responses:**  \n   - If no specific task or question arises (e.g., when only saving information), respond in a way that keeps the conversation flowing naturally:  \n     - “Thanks for sharing that, [First Name]! Is there anything else I can help you with today?”  \n   - DO NOT tell jokes as a fallback response.\n\n5. **Additional Tools:**  \n   - The remaining tools (Contacts, Calendar, Web Search, Qdrant Vector Retrieval) are available as extra features. They can be used when relevant but are secondary to the core Memory and Note management functionalities.\n\n---\n\n## USER INFO\n\n- **First Name**: {{ $('Listen for Telegram Events').item.json.body.message.from.first_name }}\n- **Age**: (if provided)  \n- **Location**: (if provided)  \n- **Job/Profession**: (if provided)\n\nUtilize this information to personalize your responses. For example, if the user's first name is available, begin responses with “Hi [First Name], …”\n\n---\n\n## REMAINING TOOLS\n\n### <More Tools>\n- <if you add more tools, add them here and add descriptions>\n\n---\n\n## MEMORIES  \n\n### Recent Noteworthy Memories  \nHere are the most recent memories collected from the user, including their date and time of collection:  \n\n**{{  \n  $json.data.map(item => item.Memory).join('\\n')  \n}}**\n\n**Guidelines:**  \n- Prioritize recent memories while considering older ones if still relevant.  \n- Cross-reference memories for consistency (e.g., if conflicting details are present, clarify as needed).\n\n---\n\n## NOTES  \n\n### Recent Notes Collected from User:  \nHere are the most recent notes collected from the user:  \n\n**{{  \n  $json.data.map(item => item.Notes).join('\\n')  \n}}**\n\n**Guidelines:**  \n- Use notes for specific instructions or reminders.  \n- Keep note content distinct from general memory content.\n\n---\n\n## ADDITIONAL INSTRUCTIONS  \n\n- Think critically before responding to ensure your answers are thoughtful and accurate.  \n- Strive to build trust with the user by being consistent, reliable, and personable.  \n- Avoid robotic or overly formal language; aim for a conversational tone that is both friendly and helpful.\n\n## CURRENT DATE\n\n - {{ $now.setZone('America/Chicago').toISO() }} (Set Timezone Accordingly) \n"}, "promptType": "define"}, "typeVersion": 1.7, "alwaysOutputData": false}, {"id": "4a5a636a-e526-4b0f-bf11-8cd623e46c68", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [460, 420], "parameters": {"color": 4, "width": 280, "height": 380, "content": "## Save Notes\nBaserow"}, "typeVersion": 1}, {"id": "46cb38d9-4932-481e-96f2-14b3224de0a9", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [-900, 480], "parameters": {"color": 4, "width": 340, "height": 380, "content": "## Retrieve Notes\nBaserow"}, "typeVersion": 1}, {"id": "c643683f-eee6-49c2-9f1a-05ba9a0742a6", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "position": [-320, 180], "parameters": {"options": {}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}, {"id": "cdd999a7-d14f-4e20-88ea-4170c8cd521c", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [-480, 180], "parameters": {}, "typeVersion": 3}, {"id": "a60582a7-63ca-497e-aec0-2be8077ebc53", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-2160, 180], "parameters": {"color": 7, "width": 420, "height": 260, "content": "## Validate Telegram User\n"}, "typeVersion": 1}, {"id": "3b3b2573-5a2e-4c0f-8005-376ed55b068b", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-2220, 780], "parameters": {"color": 6, "width": 1289, "height": 432, "content": "# Process Image"}, "typeVersion": 1}, {"id": "3d27c492-758b-45d2-bc79-a51cc69ac2ef", "name": "Get Audio File", "type": "n8n-nodes-base.telegram", "position": [-1380, 180], "webhookId": "4f5c2290-ff1a-4ce0-8319-e84807b1b1fc", "parameters": {"fileId": "={{ $json.body.message.voice.file_id }}", "resource": "file"}, "credentials": {"telegramApi": {"id": "EDuT6lIMLHRxdqqN", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "9bef0848-30a5-4e98-b0e8-640e061c475d", "name": "Analyze Image", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [-1460, 1000], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "resource": "image", "inputType": "base64", "operation": "analyze"}, "credentials": {"openAiApi": {"id": "lpt7A1AOIMshaaE7", "name": "OpenAi account"}}, "typeVersion": 1.6}, {"id": "b493ea36-c9f6-470d-97a9-8df43c10af18", "name": "Transcribe Recording", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [-1100, 180], "parameters": {"options": {}, "resource": "audio", "operation": "transcribe", "binaryPropertyName": "=data"}, "credentials": {"openAiApi": {"id": "lpt7A1AOIMshaaE7", "name": "OpenAi account"}}, "typeVersion": 1.6}, {"id": "05aff1e5-daf0-47d3-8df3-a548ca4ca2cd", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [-1240, 540], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "b37b48ba-8fef-4e6c-bbca-73e6c2e1e0a8", "name": "text", "type": "string", "value": "={{ $json.body.message.text }}"}]}}, "typeVersion": 3.4}, {"id": "ea70790d-716b-4f5d-b3d0-99259481ef22", "name": "Convert to Image File", "type": "n8n-nodes-base.convertToFile", "position": [-1640, 1000], "parameters": {"options": {"fileName": "={{ $json.result.file_path }}"}, "operation": "toBinary", "sourceProperty": "data"}, "typeVersion": 1.1}, {"id": "3e5913bf-0270-4e32-8647-80138abefde5", "name": "Extract from File to Base64", "type": "n8n-nodes-base.extractFromFile", "position": [-1820, 1000], "parameters": {"options": {}, "operation": "binaryToPropery"}, "typeVersion": 1}, {"id": "7d13ffbe-9496-4037-a7f2-d7b5774698ca", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [-1440, 80], "parameters": {"color": 6, "width": 513, "height": 309, "content": "# Process Audio"}, "typeVersion": 1}, {"id": "73570d5a-87a6-4d1f-95f3-2b8ed087a28a", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-1440, 420], "parameters": {"color": 6, "width": 513, "height": 329, "content": "# Process Text"}, "typeVersion": 1}, {"id": "f8851e90-d710-4bc6-9705-af4828d65a48", "name": "Message Router", "type": "n8n-nodes-base.switch", "position": [-1880, 480], "parameters": {"rules": {"values": [{"outputKey": "audio", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.body.message.voice }}", "rightValue": ""}]}, "renameOutput": true}, {"outputKey": "text", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "342f0883-d959-44a2-b80d-379e39c76218", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.body.message.text }}", "rightValue": ""}]}, "renameOutput": true}, {"outputKey": "image", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "ded3a600-f861-413a-8892-3fc5ea935ecb", "operator": {"type": "array", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.body.message.photo }}", "rightValue": ""}]}, "renameOutput": true}]}, "options": {"fallbackOutput": "extra"}}, "typeVersion": 3.2}, {"id": "88127339-2cc5-4364-9f27-6dea9df505ff", "name": "Image Schema1", "type": "n8n-nodes-base.set", "position": [-2160, 880], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "17989eb0-feca-4631-b5c8-34b1d4a6c72b", "name": "image_file_id", "type": "string", "value": "={{ $json.body.message.photo.last().file_id }}"}, {"id": "9317d7ae-dffd-4b1f-9a9c-b3cc4f1e0dd3", "name": "caption", "type": "string", "value": "={{ $json.body.message.caption }}"}]}}, "typeVersion": 3.4}, {"id": "b0922d42-9864-4d9a-a5c9-b674892935ff", "name": "caption", "type": "n8n-nodes-base.splitOut", "position": [-1460, 860], "parameters": {"options": {}, "fieldToSplitOut": "caption"}, "typeVersion": 1}, {"id": "74256b12-6f3b-44ba-a98b-bd233f90c360", "name": "Merge1", "type": "n8n-nodes-base.merge", "position": [-1260, 860], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3}, {"id": "9f77577b-167c-4628-b2d1-37e17d9ddb10", "name": "Get Image", "type": "n8n-nodes-base.telegram", "position": [-2000, 1000], "webhookId": "2d9b0a49-b46f-4794-9f4b-f406a213e74a", "parameters": {"fileId": "={{ $json.image_file_id }}", "resource": "file"}, "credentials": {"telegramApi": {"id": "EDuT6lIMLHRxdqqN", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "ffdf119a-cfa1-408a-aaca-458691f8d56b", "name": "Edit Fields1", "type": "n8n-nodes-base.set", "position": [-1100, 860], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "b37b48ba-8fef-4e6c-bbca-73e6c2e1e0a8", "name": "text", "type": "string", "value": "={{ $json.caption }}\n\n{{ $json.content }}"}]}}, "typeVersion": 3.4}, {"id": "2b750b39-c72a-46ae-8e3f-0060181d2a51", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [-2220, 80], "parameters": {"color": 4, "width": 753, "height": 669, "content": "# Secure User"}, "typeVersion": 1}, {"id": "18fcbf54-301e-449b-bb67-7f63753ac4c5", "name": "Sticky Note14", "type": "n8n-nodes-base.stickyNote", "position": [-1720, 60], "parameters": {"color": 3, "width": 273, "height": 269, "content": "# Error\n"}, "typeVersion": 1}, {"id": "8d7824b1-92d0-4907-8468-1d04e7d40d6c", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "position": [-20, 540], "parameters": {"sessionKey": "={{ $('Listen for Telegram Events').item.json.body.message.from.id }}", "sessionIdType": "customKey", "contextWindowLength": 50}, "credentials": {"postgres": {"id": "aVzWyCraiN3kiV5K", "name": "Postgres account"}}, "typeVersion": 1.3}, {"id": "f21c0dfa-d16a-461d-9ff3-8b9932e8abb6", "name": "Save Note <PERSON>", "type": "n8n-nodes-base.baserowTool", "position": [560, 600], "parameters": {"tableId": 640, "fieldsUi": {"fieldValues": [{"fieldId": 6025, "fieldValue": "={{ $fromAI('notes', `note to be created`, 'string') }}"}, {"fieldId": 6027, "fieldValue": "={{ $fromAI('date-added', 'date created', 'string') }}"}]}, "operation": "create", "databaseId": 122, "descriptionType": "manual", "toolDescription": "Save Notes"}, "credentials": {"baserowApi": {"id": "1YeHyTi4fmB007co", "name": "Baserow account"}}, "typeVersion": 1}, {"id": "242ff3e7-3421-4bc4-a877-0164cee7cd69", "name": "Save Memory", "type": "n8n-nodes-base.baserowTool", "position": [240, 600], "parameters": {"tableId": 639, "fieldsUi": {"fieldValues": [{"fieldId": 6022, "fieldValue": "={{ $fromAI('memory', 'memory to be created', 'string') }}"}, {"fieldId": 6024, "fieldValue": "={{ $fromAI('date-added', 'date created', 'string') }}"}]}, "operation": "create", "databaseId": 122, "descriptionType": "manual", "toolDescription": "Save Long Term Memories"}, "credentials": {"baserowApi": {"id": "1YeHyTi4fmB007co", "name": "Baserow account"}}, "typeVersion": 1}, {"id": "f681f671-7759-46ba-b674-5b8b0e385d09", "name": "Listen for Telegram Events", "type": "n8n-nodes-base.webhook", "position": [-2380, 240], "webhookId": "097f36f3-1574-44f9-815f-58387e3b20bf", "parameters": {"path": "gram", "options": {"binaryPropertyName": "data"}, "httpMethod": "POST"}, "typeVersion": 2}, {"id": "959a8a01-0fb9-4bc9-9997-cc49b3a81216", "name": "Validation", "type": "n8n-nodes-base.set", "position": [-2100, 240], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "0cea6da1-652a-4c1e-94c3-30608ced90f8", "name": "first_name", "type": "string", "value": "[Your First Name on Telegram]"}, {"id": "b90280c6-3e36-49ca-9e7e-e15c42d256cc", "name": "last_name", "type": "string", "value": "[Your Last Name on Telegram]"}, {"id": "f6d86283-16ca-447e-8427-7d3d190babc0", "name": "id", "type": "number", "value": **********}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "7903c22c-26f2-4e89-9e27-ee945e225b0e", "name": "Check User & Chat ID", "type": "n8n-nodes-base.if", "position": [-1920, 240], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "5fe3c0d8-bd61-4943-b152-9e6315134520", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Listen for Telegram Events').item.json.body.message.chat.first_name }}", "rightValue": "={{ $json.first_name }}"}, {"id": "98a0ea91-0567-459c-bbce-06abc14a49ce", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Listen for Telegram Events').item.json.body.message.chat.last_name }}", "rightValue": "={{ $json.last_name }}"}, {"id": "18a96c1f-f2a0-4a2a-b789-606763df4423", "operator": {"type": "number", "operation": "equals"}, "leftValue": "={{ $('Listen for Telegram Events').item.json.body.message.chat.id }}", "rightValue": "={{ $json.id }}"}]}, "looseTypeValidation": "="}, "typeVersion": 2.2}, {"id": "262c2621-5499-4481-ad94-97650b0f9714", "name": "Sticky Note15", "type": "n8n-nodes-base.stickyNote", "position": [-2460, 160], "parameters": {"color": 6, "width": 273, "height": 269, "content": "# Start Here"}, "typeVersion": 1}, {"id": "1cc31571-7d01-4cac-81a0-8cb6691ef1ed", "name": "Error message", "type": "n8n-nodes-base.telegram", "position": [-1640, 160], "webhookId": "b3b41cd7-40d9-47dc-817a-291733213c8b", "parameters": {"text": "=Unable to process your message.", "chatId": "={{ $json.body.message.chat.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "EDuT6lIMLHRxdqqN", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "72b0fd9e-a82c-455b-a1d7-face4a9d55b4", "name": "Baserow Retrieve Memories", "type": "n8n-nodes-base.baserow", "position": [-780, 220], "parameters": {"tableId": 639, "returnAll": true, "databaseId": 122, "additionalOptions": {}}, "credentials": {"baserowApi": {"id": "1YeHyTi4fmB007co", "name": "Baserow account"}}, "typeVersion": 1}, {"id": "40618bf6-657a-4b8a-818b-90b5e7aecbd5", "name": "Baserow Retrieve Notes", "type": "n8n-nodes-base.baserow", "position": [-780, 620], "parameters": {"tableId": 640, "returnAll": true, "databaseId": 122, "additionalOptions": {}}, "credentials": {"baserowApi": {"id": "1YeHyTi4fmB007co", "name": "Baserow account"}}, "typeVersion": 1}, {"id": "87bfdbe7-bf2f-40d8-8fb5-c0c0015d8a10", "name": "Window Memory (Easy Mode)", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [-140, 640], "parameters": {"sessionKey": "={{ $('Listen for Telegram Events').item.json.body.message.from.id }}", "sessionIdType": "customKey", "contextWindowLength": 50}, "typeVersion": 1.3}, {"id": "02f650e9-3d8d-4622-9cd9-16154d4d27c5", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [300, 820], "parameters": {"width": 280, "height": 520, "content": "## Baserow Set-up:\n- Create a new Workspace called \"Memories and Notes\"\n- Create a table within the new workspace called \"Memory Table\"\n- Within the \"Memory Table\" table, create two fields\n- First field is called \"Memory\" (select \"long text\" as the field type)\n- Second field is called \"Date Added\" (select 'US' date format, and check include time\n\n## Notes Setup:\n- For the notes table we can simply \"Duplicate\" the \"Memory Table\" table that we just created\n- Rename the table to \"Notes Table\" \n- Rename the first from \"Memory\" to \"Notes\"\n- **Make sure time and date are on and set correctly in \"Date Added\" field.**"}, "typeVersion": 1}, {"id": "26efbccd-7605-4308-85d2-c91cdd4897ad", "name": "Sticky Note16", "type": "n8n-nodes-base.stickyNote", "position": [580, 840], "parameters": {"color": 6, "width": 720, "height": 1180, "content": "## Baserow Memory Table Setup:\n![Baserow example 1](https://i.imgur.com/hXADtza.png)\n## Name first field \"Memory\" and set to \"Long Text\":\n![Baserow example 2](https://i.imgur.com/AJG0jO6.png)\n## Name second field \"Date Added\" and set to 'US' date format\n![Baserow example 2](https://i.imgur.com/z8AJWfB.png)\n"}, "typeVersion": 1}, {"id": "d30c8a6a-4668-49f9-8555-0f827d8157c0", "name": "Sticky Note17", "type": "n8n-nodes-base.stickyNote", "position": [1300, 860], "parameters": {"color": 5, "width": 540, "height": 1120, "content": "## Tool node Setup Example:\n![Baserow example 2](https://i.imgur.com/DQ6jolu.png)"}, "typeVersion": 1}, {"id": "54dce9dd-f06c-4e61-9a96-b691fe76dfa3", "name": "Sticky Note18", "type": "n8n-nodes-base.stickyNote", "position": [-2120, 400], "parameters": {"width": 150, "height": 80, "content": "## *Setup validation"}, "typeVersion": 1}, {"id": "9166bd3d-2f21-4f19-b787-ccf9be5b5acc", "name": "Sticky Note19", "type": "n8n-nodes-base.stickyNote", "position": [-120, 140], "parameters": {"width": 200, "height": 120, "content": "## *Setup Prompt\n"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "755d754f-9577-41b2-ae5c-1f0918389fc6", "connections": {"Merge": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Merge1": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "caption": {"main": [[{"node": "Merge1", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "AI Tools Agent", "type": "main", "index": 0}]]}, "Get Image": {"main": [[{"node": "Extract from File to Base64", "type": "main", "index": 0}]]}, "Validation": {"main": [[{"node": "Check User & Chat ID", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Baserow Retrieve Memories", "type": "main", "index": 0}, {"node": "Baserow Retrieve Notes", "type": "main", "index": 0}]]}, "Save Memory": {"ai_tool": [[{"node": "AI Tools Agent", "type": "ai_tool", "index": 0}]]}, "gpt-4o-mini": {"ai_languageModel": [[{"node": "AI Tools Agent", "type": "ai_languageModel", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "Baserow Retrieve Memories", "type": "main", "index": 0}, {"node": "Baserow Retrieve Notes", "type": "main", "index": 0}]]}, "Analyze Image": {"main": [[{"node": "Merge1", "type": "main", "index": 1}]]}, "Image Schema1": {"main": [[{"node": "Get Image", "type": "main", "index": 0}, {"node": "caption", "type": "main", "index": 0}]]}, "AI Tools Agent": {"main": [[{"node": "Telegram Response", "type": "main", "index": 0}, {"node": "Chat Response", "type": "main", "index": 0}]]}, "Get Audio File": {"main": [[{"node": "Transcribe Recording", "type": "main", "index": 0}]]}, "Message Router": {"main": [[{"node": "Get Audio File", "type": "main", "index": 0}], [{"node": "<PERSON>", "type": "main", "index": 0}], [{"node": "Image Schema1", "type": "main", "index": 0}], [{"node": "Error message", "type": "main", "index": 0}]]}, "Save Note Tool": {"ai_tool": [[{"node": "AI Tools Agent", "type": "ai_tool", "index": 0}]]}, "Check User & Chat ID": {"main": [[{"node": "Message Router", "type": "main", "index": 0}], [{"node": "Error message", "type": "main", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "AI Tools Agent", "type": "ai_memory", "index": 0}]]}, "Transcribe Recording": {"main": [[{"node": "Baserow Retrieve Memories", "type": "main", "index": 0}, {"node": "Baserow Retrieve Notes", "type": "main", "index": 0}]]}, "Convert to Image File": {"main": [[{"node": "Analyze Image", "type": "main", "index": 0}]]}, "Baserow Retrieve Notes": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Baserow Retrieve Memories": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Listen for Telegram Events": {"main": [[{"node": "Validation", "type": "main", "index": 0}]]}, "Extract from File to Base64": {"main": [[{"node": "Convert to Image File", "type": "main", "index": 0}]]}}}