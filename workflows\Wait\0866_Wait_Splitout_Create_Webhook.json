{"meta": {"instanceId": "1eadd5bc7c3d70c587c28f782511fd898c6bf6d97963d92e836019d2039d1c79"}, "nodes": [{"id": "ce73f49d-96f8-4a9f-a8f0-48c00da00ac7", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [-80, -40], "parameters": {"color": 4, "width": 1280, "height": 480, "content": "=======================================\n            WORKFLOW ASSISTANCE\n=======================================\nScrape Indeed Job Listings for Hiring Signals Using Bright Data and LLMs\n\nFor any questions or support, please contact:\n    <EMAIL>\n\nExplore more tips and tutorials here:\n   - YouTube: https://www.youtube.com/@YaronBeen/videos\n   - LinkedIn: https://www.linkedin.com/in/yaronbeen/\n=======================================\nBright Data Docs: https://docs.brightdata.com/introduction\n\n*Important*\nMake Sure To Add Your API Keys to the HTTTP REQUESTS NODES (BRIGHT DATA API), GOOGLE RELATED NODES AND LLM NODE"}, "typeVersion": 1}, {"id": "a06fbae2-1ea3-4b9d-8b7b-e4ec775d1a53", "name": "Snapshot Progress", "type": "n8n-nodes-base.httpRequest", "position": [2520, 380], "parameters": {"url": "=https://api.brightdata.com/datasets/v3/progress/{{ $('HTTP Request- Post API call to Bright Data').item.json.snapshot_id }}", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer <YOUR_BRIGHT_DATA_API_KEY>"}]}}, "typeVersion": 4.2}, {"id": "bb369578-eb82-4ca1-8513-92743f572c82", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [3580, 160], "parameters": {"width": 195, "height": 646, "content": "In this workflow, I use Google Sheets to store the results. \n\nYou can use my template to get started faster:\n\n1. [Click on this link to get the template](https://docs.google.com/spreadsheets/d/1vHHNShHD96AWsPnbXzlDAhPg_DbXr_Yx3wsAnQEtuyU/edit?usp=sharing)\n2. Make a copy of the Sheets\n3. Add the URL to this node \n\n\n"}, "typeVersion": 1}, {"id": "9c356e04-7a0c-4e5f-93a4-0c62d6e91a34", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1220, -40], "parameters": {"width": 480, "height": 2240, "content": "# 🔍 Indeed Jobs API – Parameter Guide\nUse this object to query Indeed job listings In Bright Data Web Scraper API.  \nEach field lets you filter results based on different criteria.\n```json\n{\n  \"country\": \"US\",\n  \"domain\": \"indeed.com\",\n  \"keyword_search\": \"Software Engineer\",\n  \"location\": \"Austin, TX\",\n  \"date_posted\": \"Last 7 days\",\n  \"posted_by\": \"Microsoft\",\n  \"pay\": 85000\n}\n```\n\n## 🧾 Field Explanations & Valid Options\n\n### 🌍 country\n**Required**\nCountry of the job, use 2-letter ISO code.\n✅ Example: \"US\", \"FR\", \"DE\"\n\n### 🌐 domain\n**Required**\nThe Indeed domain you want to collect from.\n✅ Example: \"indeed.com\", \"fr.indeed.com\"\n\n### 🧠 keyword_search\n**Required**\nSearch jobs by job title or company.\n✅ Example: \"Data Scientist\", \"Marketing Manager\"\n\n### 🗺️ location\n**Required**\nEnter specific job location you want to discover.\n✅ Example: \"New York\", \"London\"\n\n### ⏱️ date_posted\nFilter jobs by posting date.\nAccepted values:\n- Last 24 hours\n- Last 3 days\n- Last 7 days\n- Last 14 days\n\n✅ Example: \"Last 7 days\"\n\n### 👔 posted_by\nFilter jobs by posting entity or recruiter.\n✅ Example: \"Company name\", \"Recruiter name\"\n\n### 💰 pay\nFilter jobs by salary or pay rate.\nUse numerical values only.\n✅ Example: 50000, 75000\n\n## ✅ Full Example\n```json\n{\n  \"country\": \"US\",\n  \"domain\": \"indeed.com\",\n  \"keyword_search\": \"Software Developer\",\n  \"location\": \"San Francisco\",\n  \"date_posted\": \"Last 3 days\",\n  \"posted_by\": \"Microsoft\",\n  \"pay\": 85000\n}\n```"}, "typeVersion": 1}, {"id": "723655d5-1878-4f8f-92d8-82f7d884cc7a", "name": "On form submission - Discover Jobs", "type": "n8n-nodes-base.formTrigger", "position": [1600, 600], "webhookId": "8d0269c7-d1fc-45a1-a411-19634a1e0b82", "parameters": {"options": {}, "formTitle": "Linkedin High Intent Prospects And Job Post Hunt", "formFields": {"values": [{"fieldLabel": "Job Location", "placeholder": "example: new york", "requiredField": true}, {"fieldLabel": "Keyword", "placeholder": "example: <PERSON><PERSON>, AI architect", "requiredField": true}, {"fieldLabel": "Country (2 letters)", "placeholder": "example: US,UK,IL", "requiredField": true}]}, "formDescription": "This form lets you customize your job search / prospecting by choosing:\n\nLocation (city or region)\n\nJob title or keywords\n\nCountry code\n"}, "typeVersion": 2.2}, {"id": "46470e2b-a702-4f23-871d-6993a344410c", "name": "HTTP Request- Post API call to Bright Data", "type": "n8n-nodes-base.httpRequest", "position": [1940, 640], "parameters": {"url": "https://api.brightdata.com/datasets/v3/trigger", "method": "POST", "options": {}, "jsonBody": "=[\n  {\n    \"country\": \"{{ $json['Country (2 letters)'] }}\",\n    \"domain\": \"indeed.com\",\n    \"keyword_search\": \"{{ $json.Keyword }}\",\n    \"location\": \"{{ $json['Job Location'] }}\",\n    \"date_posted\": \"Last 24 hours\",\n    \"posted_by\": \"\"\n  }\n]", "sendBody": true, "sendQuery": true, "sendHeaders": true, "specifyBody": "json", "queryParameters": {"parameters": [{"name": "dataset_id", "value": "gd_l4dx9j9sscpvs7no2"}, {"name": "include_errors", "value": "true"}, {"name": "type", "value": "discover_new"}, {"name": "discover_by", "value": "keyword"}, {"name": "uncompressed_webhook", "value": "true"}, {"name": "type", "value": "discover_new"}, {"name": "discover_by", "value": "=keyword"}]}, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer <YOUR_BRIGHT_DATA_API_KEY>"}]}}, "typeVersion": 4.2}, {"id": "651be52b-9649-47ca-b07b-67012ef18397", "name": "Wait - Polling Bright Data", "type": "n8n-nodes-base.wait", "position": [2280, 380], "webhookId": "8005a2b3-2195-479e-badb-d90e4240e699", "parameters": {"unit": "minutes", "amount": 1}, "executeOnce": false, "typeVersion": 1.1}, {"id": "5fdfe171-8597-44c7-9600-afff9296626b", "name": "If - Checking status of Snapshot - if data is ready or not", "type": "n8n-nodes-base.if", "position": [2720, 380], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "7932282b-71bb-4bbb-ab73-4978e554de7e", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "running"}]}}, "typeVersion": 2.2}, {"id": "c618eb47-ab85-4dcc-a609-73a824d97f00", "name": "HTTP Request - Getting data from Bright Data", "type": "n8n-nodes-base.httpRequest", "position": [3000, 400], "parameters": {"url": "=https://api.brightdata.com/datasets/v3/snapshot/{{ $('HTTP Request- Post API call to Bright Data').item.json.snapshot_id }}", "options": {}, "sendQuery": true, "sendHeaders": true, "queryParameters": {"parameters": [{"name": "format", "value": "json"}]}, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer <YOUR_BRIGHT_DATA_API_KEY>"}]}}, "typeVersion": 4.2}, {"id": "717fc332-0679-42b0-8481-1320577856c6", "name": "Google Sheets - Adding All Job Posts", "type": "n8n-nodes-base.googleSheets", "position": [3620, 460], "parameters": {"columns": {"value": {}, "schema": [{"id": "jobid", "type": "string", "display": true, "required": false, "displayName": "jobid", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_name", "type": "string", "display": true, "required": false, "displayName": "company_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date_posted_parsed", "type": "string", "display": true, "required": false, "displayName": "date_posted_parsed", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_title", "type": "string", "display": true, "required": false, "displayName": "job_title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "description_text", "type": "string", "display": true, "required": false, "displayName": "description_text", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "benefits", "type": "string", "display": true, "required": false, "displayName": "benefits", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_type", "type": "string", "display": true, "required": false, "displayName": "job_type", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "location", "type": "string", "display": true, "required": false, "displayName": "location", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "salary_formatted", "type": "string", "display": true, "required": false, "displayName": "salary_formatted", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_rating", "type": "string", "display": true, "required": false, "displayName": "company_rating", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_reviews_count", "type": "string", "display": true, "required": false, "displayName": "company_reviews_count", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "country", "type": "string", "display": true, "required": false, "displayName": "country", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date_posted", "type": "string", "display": true, "required": false, "displayName": "date_posted", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "description", "type": "string", "display": true, "required": false, "displayName": "description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_link", "type": "string", "display": true, "required": false, "displayName": "company_link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "domain", "type": "string", "display": true, "required": false, "displayName": "domain", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "apply_link", "type": "string", "display": true, "required": false, "displayName": "apply_link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "url", "type": "string", "display": true, "required": false, "displayName": "url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "is_expired", "type": "string", "display": true, "required": false, "displayName": "is_expired", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "timestamp", "type": "string", "display": true, "required": false, "displayName": "timestamp", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_location", "type": "string", "display": true, "required": false, "displayName": "job_location", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_description_formatted", "type": "string", "display": true, "required": false, "displayName": "job_description_formatted", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "logo_url", "type": "string", "display": true, "required": false, "displayName": "logo_url", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1vHHNShHD96AWsPnbXzlDAhPg_DbXr_Yx3wsAnQEtuyU/edit#gid=0", "cachedResultName": "input"}, "documentId": {"__rl": true, "mode": "list", "value": "1vHHNShHD96AWsPnbXzlDAhPg_DbXr_Yx3wsAnQEtuyU", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1vHHNShHD96AWsPnbXzlDAhPg_DbXr_Yx3wsAnQEtuyU/edit?usp=drivesdk", "cachedResultName": "NoFluff-N8N-Sheet-Template- Indeed <PERSON> Scraping WIth Bright Data"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "4RJOMlGAcB9ZoYfm", "name": "Google Sheets account 2"}}, "typeVersion": 4.3, "alwaysOutputData": true}, {"id": "9f3f3b0f-65c2-4b6d-bd6c-74a5a8542a33", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1840, -20], "parameters": {"width": 300, "height": 880, "content": "🧠 Bright Data Trigger – Customize Your Job Query\n\nThis HTTP Request sends a POST call to Bright Data to start a new dataset snapshot based on your filters.\n\n👋 If you don’t want to use the Form Trigger,\nyou can directly adjust the filters here in this node.\n\nYou can customize:\n\n\"location\" → city, region, or keyword (e.g. \"New York\", \"Remote\")\n\n\"keyword\" → job title or role (e.g. \"CMO\", \"AI Engineer\")\n\n\"country\" → 2-letter country code (e.g. \"US\", \"UK\")\n\n\"time_range\" → \"Past 24 hours\", \"Last 7 days\", etc.\n\n\n\n📌 Tip:\nUse \"Past 24 hours\" or \"Last 7 days\" for the freshest results."}, "typeVersion": 1}, {"id": "5827ef89-c6aa-4e62-91d5-a778fcf1daad", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [2220, 240], "parameters": {"color": 4, "width": 940, "height": 360, "content": "Bright Data Getting Jobs\n"}, "typeVersion": 1}, {"id": "7fb03a36-1e06-4d0e-8899-8b6e28109136", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [3840, 460], "parameters": {"options": {}, "fieldToSplitOut": "company_name, job_title, description_text"}, "typeVersion": 1}, {"id": "1a248b8c-d50a-4229-8843-56c2eda16e45", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [4160, 680], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "npdTsI2acWhX0UbE", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "156c6fd4-8aaf-4d62-8575-cb94e6d08390", "name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [4420, 460], "parameters": {"columns": {"value": {"AM I a Fit?": "={{ $json.text }}", "company_name": "={{ $('Split Out').item.json.company_name }}"}, "schema": [{"id": "jobid", "type": "string", "display": true, "removed": true, "required": false, "displayName": "jobid", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date_posted_parsed", "type": "string", "display": true, "removed": true, "required": false, "displayName": "date_posted_parsed", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_title", "type": "string", "display": true, "removed": true, "required": false, "displayName": "job_title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "description_text", "type": "string", "display": true, "removed": true, "required": false, "displayName": "description_text", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "benefits", "type": "string", "display": true, "removed": true, "required": false, "displayName": "benefits", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_type", "type": "string", "display": true, "removed": true, "required": false, "displayName": "job_type", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "location", "type": "string", "display": true, "removed": true, "required": false, "displayName": "location", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "salary_formatted", "type": "string", "display": true, "removed": true, "required": false, "displayName": "salary_formatted", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_rating", "type": "string", "display": true, "removed": true, "required": false, "displayName": "company_rating", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_reviews_count", "type": "string", "display": true, "removed": true, "required": false, "displayName": "company_reviews_count", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "country", "type": "string", "display": true, "removed": true, "required": false, "displayName": "country", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date_posted", "type": "string", "display": true, "removed": true, "required": false, "displayName": "date_posted", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "description", "type": "string", "display": true, "removed": true, "required": false, "displayName": "description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_link", "type": "string", "display": true, "removed": true, "required": false, "displayName": "company_link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "domain", "type": "string", "display": true, "removed": true, "required": false, "displayName": "domain", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "apply_link", "type": "string", "display": true, "removed": true, "required": false, "displayName": "apply_link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "url", "type": "string", "display": true, "removed": true, "required": false, "displayName": "url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "is_expired", "type": "string", "display": true, "removed": true, "required": false, "displayName": "is_expired", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "timestamp", "type": "string", "display": true, "removed": true, "required": false, "displayName": "timestamp", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_location", "type": "string", "display": true, "removed": true, "required": false, "displayName": "job_location", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_description_formatted", "type": "string", "display": true, "removed": true, "required": false, "displayName": "job_description_formatted", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "logo_url", "type": "string", "display": true, "removed": true, "required": false, "displayName": "logo_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "region", "type": "string", "display": true, "removed": true, "required": false, "displayName": "region", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "srcname", "type": "string", "display": true, "removed": true, "required": false, "displayName": "srcname", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "discovery_input", "type": "string", "display": true, "removed": true, "required": false, "displayName": "discovery_input", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "input", "type": "string", "display": true, "removed": true, "required": false, "displayName": "input", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "AM I a Fit?", "type": "string", "display": true, "required": false, "displayName": "AM I a Fit?", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["company_name"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1vHHNShHD96AWsPnbXzlDAhPg_DbXr_Yx3wsAnQEtuyU/edit#gid=0", "cachedResultName": "input"}, "documentId": {"__rl": true, "mode": "list", "value": "1vHHNShHD96AWsPnbXzlDAhPg_DbXr_Yx3wsAnQEtuyU", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1vHHNShHD96AWsPnbXzlDAhPg_DbXr_Yx3wsAnQEtuyU/edit?usp=drivesdk", "cachedResultName": "NoFluff-N8N-Sheet-Template- Indeed <PERSON> Scraping WIth Bright Data"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "4RJOMlGAcB9ZoYfm", "name": "Google Sheets account 2"}}, "typeVersion": 4.5}, {"id": "4c884a08-ddf0-4d21-a039-88eb9a331877", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [4040, 300], "parameters": {"width": 280, "height": 620, "content": "Checking if each job post is relevant to you\n"}, "typeVersion": 1}, {"id": "53a830d6-82f6-4294-9a43-494937d85d8a", "name": "Basic LLM Chain - Checking Fit", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [4060, 460], "parameters": {"text": "=Read the following job post:\nCompany Name {{ $json.company_name }}, job Title:\n{{ $json.job_title }},\nAnd job description {{ $json.description_text }}, and tell me if you think I'm a good fit. Answer only YES or NO.\n\nI'm looking for roles in Pfizer", "promptType": "define"}, "typeVersion": 1.6}], "pinData": {"On form submission - Discover Jobs": [{"Keyword": "Marketing", "formMode": "test", "submittedAt": "2025-04-17T14:03:33.242+04:00", "Job Location": "Miami", "Country (2 letters)": "US"}]}, "connections": {"Split Out": {"main": [[{"node": "Basic LLM Chain - Checking Fit", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Basic LLM Chain - Checking Fit", "type": "ai_languageModel", "index": 0}]]}, "Snapshot Progress": {"main": [[{"node": "If - Checking status of Snapshot - if data is ready or not", "type": "main", "index": 0}]]}, "Wait - Polling Bright Data": {"main": [[{"node": "Snapshot Progress", "type": "main", "index": 0}]]}, "Basic LLM Chain - Checking Fit": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}, "On form submission - Discover Jobs": {"main": [[{"node": "HTTP Request- Post API call to Bright Data", "type": "main", "index": 0}]]}, "Google Sheets - Adding All Job Posts": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "HTTP Request- Post API call to Bright Data": {"main": [[{"node": "Wait - Polling Bright Data", "type": "main", "index": 0}]]}, "HTTP Request - Getting data from Bright Data": {"main": [[{"node": "Google Sheets - Adding All Job Posts", "type": "main", "index": 0}]]}, "If - Checking status of Snapshot - if data is ready or not": {"main": [[{"node": "Wait - Polling Bright Data", "type": "main", "index": 0}], [{"node": "HTTP Request - Getting data from Bright Data", "type": "main", "index": 0}]]}}}