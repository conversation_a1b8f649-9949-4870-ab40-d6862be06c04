{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 320], "parameters": {}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [450, 320], "parameters": {"values": {"string": [{"name": "key", "value": "somevalue"}]}, "options": {}}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.rename<PERSON><PERSON>s", "position": [650, 320], "parameters": {"keys": {"key": [{"newKey": "newkey", "currentKey": "key"}]}}, "typeVersion": 1}], "connections": {"Set": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}}