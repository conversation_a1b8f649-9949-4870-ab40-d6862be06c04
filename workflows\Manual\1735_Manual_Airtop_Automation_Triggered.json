{"id": "ViCY8FzVGcRsxVcK", "meta": {"instanceId": "660cf2c29eb19fa42319afac3bd2a4a74c6354b7c006403f6cba388968b63f5d", "templateCredsSetupCompleted": true}, "name": "Sell a Used Car", "tags": [{"id": "a8B9vqj0vNLXcKVQ", "name": "template", "createdAt": "2025-04-04T15:38:37.785Z", "updatedAt": "2025-04-04T15:38:37.785Z"}], "nodes": [{"id": "282a99b3-986e-4e3e-a673-f100ebcbc7cc", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-20, 150], "parameters": {}, "typeVersion": 1}, {"id": "57d4671a-b782-46f1-8987-3100a2841aa4", "name": "Variables", "type": "n8n-nodes-base.set", "position": [200, 150], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "3c71f7f7-072c-47a0-8fa3-67c272d40e1c", "name": "car_description", "type": "string", "value": "VIN: 1FTRF17253NB81140  Milage: 221081  Zip code: 01952  Condition: Perfect, no interior or exterior damages, all tires are inflated, have 2 keys, working battery, has and attached catalytic converter, Airbags not deployed, No flood or fire damage. Ownership: full clean title with no debt answer yes to all other questions"}]}}, "typeVersion": 3.4}, {"id": "5ab2351d-08f5-46b6-b790-3c388572a4ae", "name": "Wait 7 secs", "type": "n8n-nodes-base.wait", "position": [1080, 150], "webhookId": "ddb2af85-52a4-4ba0-bdb0-01fa489fe974", "parameters": {"amount": 7}, "typeVersion": 1.1}, {"id": "038a36e2-ed4e-4675-b933-3e3564cb8dc0", "name": "Parse response", "type": "n8n-nodes-base.code", "position": [1740, -100], "parameters": {"mode": "runOnceForEachItem", "jsCode": "const sessionId = $json.sessionId\nconst windowId = $json.windowId\nconst response = JSON.parse($json.data.modelResponse)\n\nreturn { json: {\n  sessionId,\n  windowId,\n  response\n}}"}, "typeVersion": 2}, {"id": "1017f269-03ea-4cd6-866c-b91539a05844", "name": "Switch", "type": "n8n-nodes-base.switch", "position": [1960, -100], "parameters": {"rules": {"values": [{"outputKey": "Type", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "b35db1bb-6e63-4dee-be52-658d1b78fbbb", "operator": {"type": "string", "operation": "contains"}, "leftValue": "={{ $json.response.action }}", "rightValue": "TYPE"}]}, "renameOutput": true}, {"outputKey": "Click", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "c4710a65-293c-4df4-a716-3004ae885362", "operator": {"type": "string", "operation": "contains"}, "leftValue": "={{ $json.response.action }}", "rightValue": "CLICK"}]}, "renameOutput": true}, {"outputKey": "Got price", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "e83a9d67-a55e-4272-9410-6a79e397b291", "operator": {"type": "string", "operation": "contains"}, "leftValue": "={{ $json.response.action }}", "rightValue": "PRICE"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "a0c966af-1555-4837-a700-d2ebdc0628fa", "name": "Offer received", "type": "n8n-nodes-base.set", "position": [2180, 200], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "0b75e600-36e1-4d31-bd6a-0cb9955611fc", "name": "Offer_price", "type": "string", "value": "={{ $json.response.question }}"}, {"id": "5f13d212-852c-466a-97fb-d776491b06ea", "name": "Offer_id", "type": "string", "value": "={{ $json.response.element }}"}, {"id": "af0e7609-85e2-4243-a1b1-52627783fbea", "name": "Offer_URL", "type": "string", "value": "={{ $json.response.text }}"}]}}, "typeVersion": 3.4}, {"id": "a0a4c48a-568b-4ad4-9e18-a7e1167d2b06", "name": "Type", "type": "n8n-nodes-base.airtop", "onError": "continueRegularOutput", "position": [2180, -200], "parameters": {"text": "={{ $json.response.text }}", "resource": "interaction", "operation": "type", "pressEnterKey": true, "additionalFields": {"visualScope": "viewport"}, "elementDescription": "={{ $json.response.element }}"}, "credentials": {"airtopApi": {"id": "byhouJF8RLH5DkmY", "name": "[PROD] Airtop"}}, "typeVersion": 1}, {"id": "3dfe1739-0c68-4d66-96f0-ee6999cf7cbb", "name": "Click", "type": "n8n-nodes-base.airtop", "onError": "continueRegularOutput", "position": [2180, 0], "parameters": {"resource": "interaction", "additionalFields": {"visualScope": "viewport"}, "elementDescription": "={{ $json.response.element }}"}, "credentials": {"airtopApi": {"id": "byhouJF8RLH5DkmY", "name": "[PROD] Airtop"}}, "typeVersion": 1}, {"id": "058544cb-a5d0-4e9d-bc95-a351a7432f09", "name": "Create session", "type": "n8n-nodes-base.airtop", "position": [420, 150], "parameters": {}, "credentials": {"airtopApi": {"id": "byhouJF8RLH5DkmY", "name": "[PROD] Airtop"}}, "typeVersion": 1}, {"id": "0418f412-c5e0-409c-93a6-45b281034711", "name": "Load website", "type": "n8n-nodes-base.airtop", "position": [640, 150], "parameters": {"url": "https://sell.peddle.com/instant-offer", "resource": "window", "additionalFields": {"waitUntil": "domContentLoaded"}}, "credentials": {"airtopApi": {"id": "byhouJF8RLH5DkmY", "name": "[PROD] Airtop"}}, "typeVersion": 1}, {"id": "39fdcead-1709-4648-8660-575055e82284", "name": "Click VIN button", "type": "n8n-nodes-base.airtop", "position": [860, 150], "parameters": {"resource": "interaction", "additionalFields": {}, "elementDescription": "Rounded white button \"Autofill with VIN\""}, "credentials": {"airtopApi": {"id": "byhouJF8RLH5DkmY", "name": "[PROD] Airtop"}}, "typeVersion": 1}, {"id": "74f40e8f-e6cd-4738-b98a-6871c1d75654", "name": "Terminate session", "type": "n8n-nodes-base.airtop", "position": [2400, 200], "parameters": {"operation": "terminate", "sessionId": "={{ $('Create session').last().json.sessionId }}"}, "credentials": {"airtopApi": {"id": "byhouJF8RLH5DkmY", "name": "[PROD] Airtop"}}, "typeVersion": 1}, {"id": "777542c9-c926-4d06-ad8a-2a0c43258a7d", "name": "Take screenshot", "type": "n8n-nodes-base.airtop", "notes": "Useful to validate the current screen the agent is on", "position": [1300, -100], "parameters": {"resource": "window", "operation": "takeScreenshot"}, "credentials": {"airtopApi": {"id": "byhouJF8RLH5DkmY", "name": "[PROD] Airtop"}}, "notesInFlow": true, "typeVersion": 1}, {"id": "e5442c98-7382-45ef-b810-c4b8ae4cf999", "name": "Think next action", "type": "n8n-nodes-base.airtop", "position": [1520, -100], "parameters": {"prompt": "=You are trying to get an estimate for a used car price or a purchase offer. Your task is to answer the main question presented to the user on this screen, based on the car information below, until you will get a price estimate or offer to purchase (\"We'd love to buy your car...\"). \n\nReview this page:\nIf there is a question, extract the main question presented to the user on this screen. Return your answer in the following format:\nQuestion: the main question presented to the user on this screen\nAction: <CLICK> or <TYPE>\nElement: description of the text box to type in or button to click, for example VIN text box or Yes button\nText: <Text to type> (if TYPE operation)\n\nIf the page includes the price estimate or offer to purchase, extract the price (offer_price), the offer ID (offer_id) and current page URL (page_url) and return the following:\nQuestion:  <offer_price>\nAction: <PRICE>\nElement: <offer_id>\nText: <page_url>\n\nIf there is no price estimate or offer to purchase and the Next button at the bottom is yellow and clickable, return the following:\nQuestion:  <Next>\nAction: <CLIICK>\nElement: Yellow Next Button at the bottom\nText: \n\nHere's the info about the car: \n{{ $('Variables').item.json.car_description }}", "resource": "extraction", "operation": "query", "additionalFields": {"outputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"question\": {\n      \"type\": \"string\",\n      \"description\": \"The main question presented to the user on the screen.\"\n    },\n    \"action\": {\n      \"type\": \"string\",\n      \"description\": \"The action to be performed, either clicking a button or typing in a text box.\"\n    },\n    \"element\": {\n      \"type\": \"string\",\n      \"description\": \"Description of the text box to type in or button to click.\"\n    },\n    \"text\": {\n      \"type\": \"string\",\n      \"description\": \"Text to type if the action is TYPE.\"\n    }\n  },\n  \"required\": [\n    \"question\",\n    \"action\",\n    \"element\",\n    \"text\"\n  ],\n  \"additionalProperties\": false,\n  \"$schema\": \"http://json-schema.org/draft-07/schema#\"\n}", "includeVisualAnalysis": true}}, "credentials": {"airtopApi": {"id": "byhouJF8RLH5DkmY", "name": "[PROD] Airtop"}}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "7ad89f7e-cdbc-4ec4-b5a9-cc724677be15", "connections": {"Type": {"main": [[{"node": "Wait 7 secs", "type": "main", "index": 0}]]}, "Click": {"main": [[{"node": "Wait 7 secs", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Type", "type": "main", "index": 0}], [{"node": "Click", "type": "main", "index": 0}], [{"node": "Offer received", "type": "main", "index": 0}]]}, "Variables": {"main": [[{"node": "Create session", "type": "main", "index": 0}]]}, "Wait 7 secs": {"main": [[{"node": "Take screenshot", "type": "main", "index": 0}]]}, "Load website": {"main": [[{"node": "Click VIN button", "type": "main", "index": 0}]]}, "Create session": {"main": [[{"node": "Load website", "type": "main", "index": 0}]]}, "Offer received": {"main": [[{"node": "Terminate session", "type": "main", "index": 0}]]}, "Parse response": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Take screenshot": {"main": [[{"node": "Think next action", "type": "main", "index": 0}]]}, "Click VIN button": {"main": [[{"node": "Wait 7 secs", "type": "main", "index": 0}]]}, "Think next action": {"main": [[{"node": "Parse response", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Variables", "type": "main", "index": 0}]]}}}