{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [460, 230], "parameters": {}, "typeVersion": 1}, {"name": "MySQL", "type": "n8n-nodes-base.mySql", "position": [660, 230], "parameters": {"query": "CREATE TABLE test (id INT, name VARCHAR(255), PRIMARY KEY (id));", "operation": "execute<PERSON>uery"}, "credentials": {"mySql": "mysql_creds"}, "typeVersion": 1}, {"name": "MySQL1", "type": "n8n-nodes-base.mySql", "position": [1060, 230], "parameters": {"table": "test", "columns": "id, name"}, "credentials": {"mySql": "mysql_creds"}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [860, 230], "parameters": {"values": {"number": [{"name": "id"}], "string": [{"name": "name", "value": "n8n"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1, "alwaysOutputData": false}], "connections": {"Set": {"main": [[{"node": "MySQL1", "type": "main", "index": 0}]]}, "MySQL": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "MySQL", "type": "main", "index": 0}]]}}}