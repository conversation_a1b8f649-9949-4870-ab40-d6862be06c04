{"id": "FQ0Uljxi7aIBdTFX", "meta": {"instanceId": "a5283507e1917a33cc3ae615b2e7d5ad2c1e50955e6f831272ddd5ab816f3fb6", "templateCredsSetupCompleted": true}, "name": "Coinmarketcap Price Agent", "tags": [], "nodes": [{"id": "4f7066a4-9baa-428e-8b98-f4a3d0a6cf8a", "name": "Telegram Send Message", "type": "n8n-nodes-base.telegram", "position": [1280, 0], "webhookId": "0eeae020-ed6f-4900-ae38-d646d893171d", "parameters": {"text": "={{ $json.output }}", "chatId": "={{ $('Telegram Trigger1').item.json.message.chat.id }}", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "R3vpGq0SURbvEw2Z", "name": "Telegram account"}}, "typeVersion": 1}, {"id": "39c91f2b-87ed-46e9-8cc4-8c6ea547f170", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [660, 320], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "yUizd8t0sD5wMYVG", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "c87b5030-de78-4b86-8bb3-b93ee6b76a54", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [820, 320], "parameters": {}, "typeVersion": 1.3}, {"id": "ae3ec7a6-bf62-4381-acf8-05c7c425f471", "name": "Telegram Trigger1", "type": "n8n-nodes-base.telegramTrigger", "position": [100, 0], "webhookId": "b33d2025-01c2-4386-b677-206a87a1856b", "parameters": {"updates": ["message"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "R3vpGq0SURbvEw2Z", "name": "Telegram account"}}, "typeVersion": 1.1}, {"id": "3f3594f5-64d5-4d82-8d0f-0e5f58244d17", "name": "CoinMarketCap Price Agent", "type": "@n8n/n8n-nodes-langchain.agent", "notes": "{{ $json.sessionId }}", "position": [760, 0], "parameters": {"text": "={{ $json.message.text }}", "options": {}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "360dc88a-a714-4ceb-be25-5ebe7d1e0273", "name": "Adds SessionId", "type": "n8n-nodes-base.set", "position": [420, 0], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "b5c25cd4-226b-4778-863f-79b13b4a5202", "name": "sessionId", "type": "string", "value": "={{ $json.message.chat.id }}"}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "8d53c2a0-a255-4fe9-8e5c-38c957825413", "name": "CoinMarketCap Price", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [980, 320], "parameters": {"url": "https://pro-api.coinmarketcap.com/v1/cryptocurrency/quotes/latest", "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "parametersQuery": {"values": [{"name": "symbol"}, {"name": "convert", "value": "USD", "valueProvider": "fieldValue"}]}, "toolDescription": "The tool going to recieve input of cryptocurrency name and then request the price from CoinMarketCap and send the price back in a message.", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "OKXROn8aWkgAOvvV", "name": "CoinMarketCap"}}, "typeVersion": 1.1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "595f494f-4109-4cd7-bf69-d1300d3a5408", "connections": {"Adds SessionId": {"main": [[{"node": "CoinMarketCap Price Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "CoinMarketCap Price Agent", "type": "ai_languageModel", "index": 0}]]}, "Telegram Trigger1": {"main": [[{"node": "Adds SessionId", "type": "main", "index": 0}]]}, "CoinMarketCap Price": {"ai_tool": [[{"node": "CoinMarketCap Price Agent", "type": "ai_tool", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "CoinMarketCap Price Agent", "type": "ai_memory", "index": 0}]]}, "CoinMarketCap Price Agent": {"main": [[{"node": "Telegram Send Message", "type": "main", "index": 0}]]}}}