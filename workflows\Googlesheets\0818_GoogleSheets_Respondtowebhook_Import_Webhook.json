{"meta": {"instanceId": "f4f5d195bb2162a0972f737368404b18be694648d365d6c6771d7b4909d28167", "templateCredsSetupCompleted": true}, "nodes": [{"id": "9a8d7d07-a1b3-4bca-8e77-10da3a2abc45", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [-160, 0], "webhookId": "7f35a3a8-54c3-49d7-879d-6c3429f0e5da", "parameters": {"path": "retell-dynamic-variables", "options": {"ipWhitelist": "************"}, "httpMethod": "POST", "responseMode": "responseNode"}, "typeVersion": 2}, {"id": "79e77d72-6e13-428c-ad10-58e6930e2d90", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [440, 0], "parameters": {"options": {}, "respondWith": "json", "responseBody": "={\n  \"call_inbound\": {\n    \"dynamic_variables\": {\n        \"first_name\": \"{{ $json['First Name'] }}\",\n        \"last_name\": \"{{ $json['Last name'] }}\",\n        \"email\": \"{{ $json['E-Mail'] }}\",\n        \"variable_1\": \"{{ $json['User Variable 1'] }}\",\n        \"variable_2\": \"{{ $json['User Variable 2']}}\"\n    },\n    \"metadata\": {\n    }\n  }\n}"}, "typeVersion": 1.1}, {"id": "10919781-9750-417f-bba6-293bf99dbc3e", "name": "Get user in DB by Phone Number", "type": "n8n-nodes-base.googleSheets", "position": [140, 0], "parameters": {"options": {}, "filtersUI": {"values": [{"lookupValue": "={{ $json.body.call_inbound.from_number }}", "lookupColumn": "Phone Number"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1TYgk8PK5w2l8Q5NtepdyLvgtuHXBHcODy-2hXOPP6AU/edit#gid=0", "cachedResultName": "Users"}, "documentId": {"__rl": true, "mode": "list", "value": "1TYgk8PK5w2l8Q5NtepdyLvgtuHXBHcODy-2hXOPP6AU", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1TYgk8PK5w2l8Q5NtepdyLvgtuHXBHcODy-2hXOPP6AU/edit?usp=drivesdk", "cachedResultName": "Retell sample UserDB"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "ufBkeygvc1l17m5N", "name": "Baptiste AS - Google Sheets account"}}, "typeVersion": 4.5}, {"id": "de9a2ff5-690e-4e1e-ab5c-5a8825986871", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-880, -440], "parameters": {"color": 7, "width": 601, "height": 1105, "content": "## Handle Retell's Inbound call webhooks\n\n## Overview\n- This workflow provides Retell agent builders with a simple way to populate [dynamic variables](https://docs.retellai.com/build/dynamic-variables) using n8n.\n- The workflow fetches user information from a Google Sheet based on the phone number and sends it back to Retell.\n- It is based on Retell's [Inbound Webhook Call](https://docs.retellai.com/features/inbound-call-webhook).\n- Retell is a service that lets you create Voice Agents that handle voice calls simply, based on a prompt or using a conversational flow builder.\n\n## Prerequisites\n- Have a [Retell AI Account](https://www.retellai.com/)\n- [Create a Retell agent](https://docs.retellai.com/get-started/quick-start)\n- [Purchase a phone number](https://docs.retellai.com/deploy/purchase-number) and associate it with your agent\n- Create a Google Sheets - for example, [make a copy of this one](https://docs.google.com/spreadsheets/d/1TYgk8PK5w2l8Q5NtepdyLvgtuHXBHcODy-2hXOPP6AU/edit?usp=sharing).\n- Your Google Sheet must have at least one column with the phone number. The remaining columns will be used to populate your Retell agent’s dynamic variables.\n- All fields are returned as strings to Retell (variables are replaced as text)\n\n## How it works\n- The webhook call is received from Retell. We filter the call using their whitelisted IP address.\n- It extracts data from the webhook call and uses it to retrieve the user from Google Sheets.\n- It formats the data in the response to match Retell's expected format.\n- Retell uses this data to replace [dynamic variables](https://docs.retellai.com/build/dynamic-variables#dynamic-variables) in the prompts.\n\n\n## How to use it\nSee the description for screenshots!\n- Set the webhook name (keep it as POST).\n- Copy the Webhook URL (e.g., `https://your-instance.app.n8n.cloud/webhook/retell-dynamic-variables`) and paste it into Retell's interface. Navigate to \"Phone Numbers\", click on the phone number, and enable \"Add an inbound webhook\".\n- In your prompt (e.g., \"welcome message\"), use the variable with this syntax: `{{variable_name}}` (see [Retell's documentation](https://docs.retellai.com/build/dynamic-variables)).\n- These variables will be dynamically replaced by the data in your Google Sheet.\n\n\n## Notes\n- In Google Sheets, the phone number must start with `'+'`.\n- Phone numbers must be formatted like the example: with the `+`, extension, and no spaces.\n- You can use any database—just replace Google Sheets with your own, making sure to keep the phone number formatting consistent.\n"}, "typeVersion": 1}, {"id": "55b087bf-d51f-4660-94c7-3742915ff79b", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-220, -120], "parameters": {"color": 5, "width": 220, "height": 300, "content": "Change the path if needed"}, "typeVersion": 1}, {"id": "bd6a7c81-5125-4f46-a1ba-86029d3a0eda", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [80, -120], "parameters": {"color": 5, "width": 220, "height": 300, "content": "Replace with your own Google Sheets, including the dynamic variables of your Retell Agent"}, "typeVersion": 1}, {"id": "7105c832-ffbe-4d36-90ec-b8c868388c4e", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [380, -120], "parameters": {"color": 5, "width": 220, "height": 300, "content": "Adapt the response to match your Retell dynamic variables"}, "typeVersion": 1}], "pinData": {}, "connections": {"Webhook": {"main": [[{"node": "Get user in DB by Phone Number", "type": "main", "index": 0}]]}, "Get user in DB by Phone Number": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}}}