{"id": "mvgpK03LMiYSiyxH", "meta": {"instanceId": "d58ea5647f14a122a558f2a99ce9c999af3b31f43e8079989af146576e4a2268"}, "name": "SearchApi AI Agent", "tags": [], "nodes": [{"id": "72554855-a492-4382-9e6d-f3eb4b8bccdd", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [600, 480], "webhookId": "d48f9e07-3c05-4be8-86ca-5cee4c27b78f", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "95d926d7-5c58-485d-bb44-0655ea71a172", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [980, 700], "parameters": {"contextWindowLength": 20}, "typeVersion": 1.3}, {"id": "3c62679b-66c9-4d06-a291-90c33b0b6c1a", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [860, 480], "parameters": {"options": {}}, "typeVersion": 1.8}, {"id": "050a87a7-b035-4d1b-bea6-915d413b31ac", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [500, 260], "parameters": {"color": 5, "width": 340, "content": "## SearchApi AI Agent\nWhenever you ask a question that should be searched on the web, the AI Agent will use SearchAPI to do it. To run this workflow, you need to have the credentials for Searchapi.io and some LLM provider."}, "typeVersion": 1}, {"id": "8322c743-0f0a-49a8-bff7-ec4960a75287", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1360, 800], "parameters": {"width": 260, "height": 120, "content": "## Tip\nYou can change the node to use any of the engines available on [SearchAPI.io](https://www.searchapi.io/)"}, "typeVersion": 1}, {"id": "45085fa9-7be4-41b0-9f2f-a6d4c8ff6979", "name": "SearchApi", "type": "@searchapi/n8n-nodes-searchapi.searchApiTool", "position": [1120, 700], "parameters": {"parameters": {"parameter": [{"name": "q", "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('parameter0_Value', ``, 'string') }}"}]}, "requestOptions": {}}, "typeVersion": 1}, {"id": "f4edfcf7-a083-4781-9381-0b3c57f0d0bb", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [840, 700], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "typeVersion": 1.2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "1256a1a1-cf4e-4c91-8047-70bca3d93ca2", "connections": {"SearchApi": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}}}