{"meta": {"instanceId": "6af2f94153ea0551e6264b16187490bd4c4739c7f5f3d7adab90c5cf186e22a1", "templateCredsSetupCompleted": true}, "nodes": [{"id": "43e68fe1-7f48-4bc9-b19a-66d39bee5bbd", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-520, 20], "parameters": {}, "typeVersion": 1}, {"id": "32aa401a-60c3-4436-94d5-5ba09d3be6ae", "name": "OpenRouter <PERSON>", "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "position": [80, 0], "parameters": {"model": "openai/gpt-4.1-nano", "options": {}}, "credentials": {"openRouterApi": {"id": "eQmkAlMDYm8oEtBL", "name": "OpenRouter account"}}, "typeVersion": 1}, {"id": "f6d325b4-ff87-4bba-9f27-b68590c8a533", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "position": [-520, -220], "webhookId": "e61d3286-920e-406c-b787-d330cf897ef4", "parameters": {"updates": ["message"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "ZOl2ZojetuN1uFiX", "name": "My Mail Agent <PERSON> via Telegram"}}, "typeVersion": 1.1}, {"id": "8e10c622-9bf8-414b-8364-185c5c4808a0", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-600, -480], "parameters": {"width": 1660, "height": 680, "content": "## Mail Agent\nFor emails in the inbox, archive those that are completely unnecessary, and label the rest based on their relevance.\n\n"}, "typeVersion": 1}, {"id": "2e664cd4-37af-4b8f-84a5-ff07911b8aaa", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-560, -380], "parameters": {"color": 5, "width": 180, "height": 360, "content": "### <PERSON><PERSON>\nRun by communicating with Telegram"}, "typeVersion": 1}, {"id": "966af8d0-bfca-40fa-b97c-ec1bb7de82d2", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-360, -380], "parameters": {"color": 4, "width": 180, "height": 360, "content": "### Get Mail via Gmail\nRetrieve all emails in the Gmail inbox.\n(Inbox = Label: INBOX)"}, "typeVersion": 1}, {"id": "07dabeda-a075-4e45-9ecf-9a0e6d0df0b2", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-160, -380], "parameters": {"color": 4, "width": 180, "height": 360, "content": "### Filter\nFilter out emails that have already been processed to avoid unnecessary work for the AI.\n\n"}, "typeVersion": 1}, {"id": "b9a96646-283e-4328-8c79-57befa97bb69", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [40, -380], "parameters": {"color": 3, "width": 980, "height": 540, "content": "### AI Agent\nCheck each email one by one, categorize them as necessary or unnecessary according to the provided prompt, and instruct Gmail to apply the appropriate labels."}, "typeVersion": 1}, {"id": "32c73c57-61b5-430b-a011-f0b44fa2b226", "name": "mail_label_setter", "type": "n8n-nodes-base.gmailTool", "position": [360, 0], "webhookId": "37bb94d2-6aeb-4038-afc7-e25a330e7860", "parameters": {"labelIds": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Label_Names_or_IDs', ``, 'string') }}", "messageId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message_ID', ``, 'string') }}", "operation": "addLabels"}, "credentials": {"gmailOAuth2": {"id": "5GhcPqZ48DrfujWd", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "7cf38850-939c-4c8e-af62-1f730d5b7e34", "name": "mail_archiver", "type": "n8n-nodes-base.gmailTool", "position": [220, 0], "webhookId": "********-38dd-4acf-b97a-8e68f332f56a", "parameters": {"labelIds": ["INBOX"], "messageId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message_ID', ``, 'string') }}", "operation": "<PERSON><PERSON><PERSON><PERSON>"}, "credentials": {"gmailOAuth2": {"id": "5GhcPqZ48DrfujWd", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "5fab497e-a632-4565-8048-7ae9b209728d", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "position": [380, -220], "parameters": {"options": {}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}, {"id": "f7144884-6ba6-4e97-be35-f5f8b27d56ad", "name": "Telegram", "type": "n8n-nodes-base.telegram", "position": [820, -220], "webhookId": "6324ebbf-b2c3-42c3-b4ee-849184380b4f", "parameters": {"text": "={{ $json.output }}", "chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "ZOl2ZojetuN1uFiX", "name": "My Mail Agent <PERSON> via Telegram"}}, "typeVersion": 1.2}, {"id": "9236fbc1-ffad-4bf0-b3a1-5d389e5b422c", "name": "OpenRouter Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "position": [520, 0], "parameters": {"model": "openai/gpt-4.1-nano", "options": {}}, "credentials": {"openRouterApi": {"id": "eQmkAlMDYm8oEtBL", "name": "OpenRouter account"}}, "typeVersion": 1}, {"id": "e0ec10ca-ad72-4784-891e-5bd5bcff7082", "name": "Reporter", "type": "@n8n/n8n-nodes-langchain.agent", "position": [520, -220], "parameters": {"text": "=Summarize data\n```\n{{ $json.data.map(item => item.output + '\\n\\n') }}\n```\n", "options": {"systemMessage": "=# persona\n* You are a helpful assistant.\n"}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "9b4f8e14-7b9c-45b3-97cb-32f2fe756440", "name": "Get mails in INBOX", "type": "n8n-nodes-base.gmail", "position": [-320, -220], "webhookId": "f4c95906-916d-4c94-8e35-cb37c9472043", "parameters": {"filters": {"labelIds": ["INBOX"]}, "operation": "getAll", "returnAll": true}, "credentials": {"gmailOAuth2": {"id": "5GhcPqZ48DrfujWd", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "13088de9-6f96-463e-bcb6-92f97d7144d9", "name": "Filter processed", "type": "n8n-nodes-base.filter", "position": [-120, -220], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "1091eba0-3d75-47b6-92c5-404f93e263ae", "operator": {"type": "array", "operation": "notContains", "rightType": "any"}, "leftValue": "={{ $json.labels.map(item => item.name)}}", "rightValue": "NotNeed"}, {"id": "********-98ce-43ac-8c7b-116cd7da5ebc", "operator": {"type": "array", "operation": "notContains", "rightType": "any"}, "leftValue": "={{ $json.labels.map(item => item.name)}}", "rightValue": "MustRead"}]}}, "typeVersion": 2.2}, {"id": "317ea413-e8fd-4148-8115-8b4d2b9a7fe4", "name": "Categoriser", "type": "@n8n/n8n-nodes-langchain.agent", "position": [80, -220], "parameters": {"text": "=<task>\nProcess mail\n</task>\n<mail>\n<id>{{ $json.id }}</id>\n<from>{{ $json.From }}</from>\n<subject>{{ $json.Subject }}</subject>\n<body>{{ $json.snippet }}</body>\n</mail>", "options": {"systemMessage": "=<persona>\nYou are an email processing assistant.\n</persona>\n<task>\nLook at the content of the email and decide whether to apply a label or archive it, processing it only once. First, archive those that are absolutely unnecessary using the mail_archiver tool. This judgment is the top priority. After that, if it does not fall into that category, determine whether it should be read based on the following criteria and use the mail_label_setter tool to apply the label.\n<case>Absolutely unnecessary: Archive using the mail_archiver tool</case>\n<case>Needs to be read: Apply \"Label_[label_id]\" using the mail_label_setter tool</case>\n<case>Other: Apply \"Label_[label_id]\" using the mail_label_setter tool</case>\nReport the processing results carefully.\n</task>\n<rules>\n<Archive>\nEmails that are absolutely unnecessary and will be archived\n<item>[list up your rule1]</item>\n<item>[list up your rule2]</item>\n</Archive><MustRead>\nEmails that need to be read\n<item>[list up your rule1]</item>\n<item>[list up your rule2]</item>\n</MustRead>\n<Other>\nEmails that are not necessary to read but will not be archived\n<item>[list up your rule1]</item>\n<item>[list up your rule2]</item>\n</Other>\n</rules>"}, "promptType": "define"}, "typeVersion": 1.7}], "pinData": {}, "connections": {"Reporter": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "Reporter", "type": "main", "index": 0}]]}, "Categoriser": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "mail_archiver": {"ai_tool": [[{"node": "Categoriser", "type": "ai_tool", "index": 0}]]}, "Filter processed": {"main": [[{"node": "Categoriser", "type": "main", "index": 0}]]}, "Telegram Trigger": {"main": [[{"node": "Get mails in INBOX", "type": "main", "index": 0}]]}, "mail_label_setter": {"ai_tool": [[{"node": "Categoriser", "type": "ai_tool", "index": 0}]]}, "Get mails in INBOX": {"main": [[{"node": "Filter processed", "type": "main", "index": 0}]]}, "OpenRouter Chat Model": {"ai_languageModel": [[{"node": "Categoriser", "type": "ai_languageModel", "index": 0}]]}, "OpenRouter Chat Model1": {"ai_languageModel": [[{"node": "Reporter", "type": "ai_languageModel", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Get mails in INBOX", "type": "main", "index": 0}]]}}}