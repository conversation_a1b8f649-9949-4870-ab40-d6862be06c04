{"nodes": [{"id": "a39274d0-6709-4e66-95a7-8c0fc4c0e8b1", "name": "if after unsnooze date", "type": "n8n-nodes-base.if", "position": [1840, 500], "parameters": {"conditions": {"dateTime": [{"value1": "={{ DateTime.now() }}", "value2": "={{ $json.unsnoozeDate }}"}]}}, "typeVersion": 1}, {"id": "a4e2d915-4714-41ea-8995-76b7198df675", "name": "at 5am", "type": "n8n-nodes-base.scheduleTrigger", "position": [780, 500], "parameters": {"rule": {"interval": [{"triggerAtHour": 5}]}}, "typeVersion": 1.1}, {"id": "7ad8e2f6-0499-4537-8325-9dffc2d7ea3c", "name": "every 5 min", "type": "n8n-nodes-base.scheduleTrigger", "position": [780, 280], "parameters": {"rule": {"interval": [{"field": "minutes"}]}}, "typeVersion": 1.1}, {"id": "370f380a-923b-4e4f-b025-9e7723662083", "name": "Get snoozed tasks", "type": "n8n-nodes-base.todoist", "position": [980, 500], "parameters": {"filters": {"projectId": "**********"}, "operation": "getAll", "returnAll": true}, "credentials": {"todoistApi": {"id": "1", "name": "Todoist account"}}, "retryOnFail": true, "typeVersion": 2, "waitBetweenTries": 5000}, {"id": "f239a87d-0229-4964-bca0-75bbf371626b", "name": "if task is not a subtask", "type": "n8n-nodes-base.if", "position": [1200, 500], "parameters": {"conditions": {"number": [{"value1": "={{ $json.parent_id }}", "operation": "isEmpty"}]}}, "typeVersion": 1}, {"id": "b9b29371-254f-45d1-846c-c2db7efae907", "name": "If task has due date", "type": "n8n-nodes-base.if", "position": [1420, 500], "parameters": {"conditions": {"boolean": [{"value1": "={{ !$json.due }}"}]}}, "typeVersion": 1}, {"id": "1d1fe683-68b5-4a9c-af29-b20a01c2473b", "name": "Get date to unsnooze", "type": "n8n-nodes-base.dateTime", "position": [1640, 500], "parameters": {"options": {"includeInputFields": true}, "duration": 3, "magnitude": "={{ $json.due.date }}", "operation": "subtractFromDate", "outputFieldName": "unsnoozeDate"}, "typeVersion": 2}, {"id": "9e0e3241-d2fd-4bc4-9273-aa5237cbeaa4", "name": "Get inbox tasks to snooze", "type": "n8n-nodes-base.todoist", "position": [980, 280], "parameters": {"filters": {"projectId": "*********"}, "operation": "getAll", "returnAll": true}, "credentials": {"todoistApi": {"id": "1", "name": "Todoist account"}}, "retryOnFail": true, "typeVersion": 2, "waitBetweenTries": 5000}, {"id": "90e83f5f-dd9f-431d-92b5-cd52a792dee2", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1220, 220], "parameters": {"color": 5, "width": 390.**************, "height": 182.**************, "content": "### 👨‍🎤 Setup\n1. Add your Todoist creds\n2. Create a Todoist project called `snoozed`\n3. Set the project ids in the relevant nodes\n4. Add due dates to your tasks in Inbox. Watch them disappear to `snoozed`. Set their date to tomorrow, watch it return to inbox."}, "typeVersion": 1}, {"id": "c7a6b401-f518-45ba-a185-c8bf0cd92394", "name": "Set inbox project id", "type": "n8n-nodes-base.set", "position": [2060, 420], "parameters": {"fields": {"values": [{"name": "target_project_id", "type": "numberValue", "numberValue": "**********"}]}, "options": {}}, "typeVersion": 3.2}, {"id": "********-5036-490a-ba3c-d40db8c3dc89", "name": "If not same project", "type": "n8n-nodes-base.filter", "position": [2280, 500], "parameters": {"conditions": {"number": [{"value1": "={{ parseInt($json.target_project_id) }}", "value2": "={{ parseInt($json.project_id) }}", "operation": "notEqual"}]}}, "typeVersion": 1}, {"id": "62009b22-d0e3-40a0-b7f9-88dc2ec02284", "name": "Set args to move", "type": "n8n-nodes-base.set", "position": [2480, 500], "parameters": {"fields": {"values": [{"name": "args", "type": "objectValue", "objectValue": "={ id: {{ $json.id }}, project_id: {{ $json.target_project_id }} }"}, {"name": "type", "stringValue": "item_move"}]}, "include": "none", "options": {}}, "typeVersion": 3.2}, {"id": "4d628334-12a3-451f-b49e-ce749241e411", "name": "Generate unique uuid for move", "type": "n8n-nodes-base.crypto", "position": [2680, 500], "parameters": {"action": "generate", "dataPropertyName": "uuid"}, "typeVersion": 1}, {"id": "8b6bf7ae-6d15-473d-8f00-5aaa4ea7d2f3", "name": "Merge all items into a list", "type": "n8n-nodes-base.itemLists", "position": [2880, 500], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "operation": "concatenateItems", "destinationFieldName": "commands"}, "typeVersion": 3.1}, {"id": "7882c3c6-0d24-4fe2-99b6-3e878e4d0dea", "name": "Move the tasks", "type": "n8n-nodes-base.httpRequest", "position": [3080, 500], "parameters": {"url": "https://api.todoist.com/sync/v9/sync", "method": "POST", "options": {}, "sendBody": true, "contentType": "form-urlencoded", "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "commands", "value": "={{ JSON.stringify($json.commands) }}"}]}, "nodeCredentialType": "to<PERSON><PERSON><PERSON><PERSON>"}, "credentials": {"todoistApi": {"id": "1", "name": "Todoist account"}}, "typeVersion": 4.1}, {"id": "259c337b-38f6-4c2a-8e23-9fe5d154a2aa", "name": "Set snoozed project id", "type": "n8n-nodes-base.set", "position": [2060, 600], "parameters": {"fields": {"values": [{"name": "target_project_id", "type": "numberValue", "numberValue": "**********"}]}, "options": {}}, "typeVersion": 3.2}, {"id": "2795502f-cdeb-4b94-a6fe-ef3657bdc091", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [2080, 780], "parameters": {"color": 7, "width": 202, "height": 100, "content": "👆 Set `snoozed` project id here. You can find it in the URL. "}, "typeVersion": 1}, {"id": "ef6c23d5-386e-48c2-a2ed-eea67fe1f117", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [2060, 260], "parameters": {"color": 7, "width": 202, "height": 100, "content": "👇🏽 Set `inbox` project id here. You can find it in the URL. "}, "typeVersion": 1}, {"id": "6727670f-b340-47cd-b86a-632ef29e2135", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1660, 660], "parameters": {"color": 7, "width": 202, "height": 100, "content": "👆🏽 Adjust here the timeline to return tasks to Inbox (here set to 3 days before due date)"}, "typeVersion": 1}], "pinData": {}, "connections": {"at 5am": {"main": [[{"node": "Get snoozed tasks", "type": "main", "index": 0}]]}, "every 5 min": {"main": [[{"node": "Get inbox tasks to snooze", "type": "main", "index": 0}]]}, "Set args to move": {"main": [[{"node": "Generate unique uuid for move", "type": "main", "index": 0}]]}, "Get snoozed tasks": {"main": [[{"node": "if task is not a subtask", "type": "main", "index": 0}]]}, "If not same project": {"main": [[{"node": "Set args to move", "type": "main", "index": 0}]]}, "Get date to unsnooze": {"main": [[{"node": "if after unsnooze date", "type": "main", "index": 0}]]}, "If task has due date": {"main": [[{"node": "Get date to unsnooze", "type": "main", "index": 0}]]}, "Set inbox project id": {"main": [[{"node": "If not same project", "type": "main", "index": 0}]]}, "Set snoozed project id": {"main": [[{"node": "If not same project", "type": "main", "index": 0}]]}, "if after unsnooze date": {"main": [[{"node": "Set inbox project id", "type": "main", "index": 0}], [{"node": "Set snoozed project id", "type": "main", "index": 0}]]}, "if task is not a subtask": {"main": [[{"node": "If task has due date", "type": "main", "index": 0}]]}, "Get inbox tasks to snooze": {"main": [[{"node": "if task is not a subtask", "type": "main", "index": 0}]]}, "Merge all items into a list": {"main": [[{"node": "Move the tasks", "type": "main", "index": 0}]]}, "Generate unique uuid for move": {"main": [[{"node": "Merge all items into a list", "type": "main", "index": 0}]]}}}