{"id": "3", "name": "StatsInstagram", "nodes": [{"name": "Mattermost", "type": "n8n-nodes-base.mattermost", "position": [1030, 290], "parameters": {"message": "=Bonjour ! Voici les stats de notre Instagram {{$json[\"Compte\"]}} en ce beau matin du {{$node[\"Date & Time\"].json[\"day_today\"]}} {{$node[\"Date & Time\"].json[\"data\"]}}\nLe nombre de Followers est de : {{$json[\"Followers\"]}}\nNous avons réalisé : {{$json[\"Posts\"]}} posts, \nBravo !", "channelId": "xxxxxxx", "attachments": [], "otherOptions": {}}, "credentials": {"mattermostApi": "API"}, "typeVersion": 1}, {"name": "Date & Time", "type": "n8n-nodes-base.dateTime", "position": [640, 290], "parameters": {"value": "={{$json[\"date_today\"]}}", "custom": true, "options": {}, "toFormat": "DD-MM-YYYY"}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [310, 290], "parameters": {"triggerTimes": {"item": [{"hour": 8}]}}, "typeVersion": 1}, {"name": "Get the date today", "type": "n8n-nodes-base.function", "position": [470, 290], "parameters": {"functionCode": "var date = new Date().toISOString();\nvar day = new Date().getDay();\nconst weekday = [\"<PERSON><PERSON><PERSON>\", \"<PERSON>i\", \"Mar<PERSON>\", \"<PERSON><PERSON><PERSON>i\", \"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\"];\n\nitems[0].json.date_today = date;\nitems[0].json.day_today = weekday[day];\n\nreturn items;\n"}, "typeVersion": 1}, {"name": "Read data on Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [850, 290], "parameters": {"range": "cells", "options": {}, "sheetId": "sheetID", "authentication": "oAuth2"}, "credentials": {"googleSheetsOAuth2Api": "GoogleAPI"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Cron": {"main": [[{"node": "Get the date today", "type": "main", "index": 0}]]}, "Date & Time": {"main": [[{"node": "Read data on Google Sheets", "type": "main", "index": 0}]]}, "Get the date today": {"main": [[{"node": "Date & Time", "type": "main", "index": 0}]]}, "Read data on Google Sheets": {"main": [[{"node": "Mattermost", "type": "main", "index": 0}]]}}}