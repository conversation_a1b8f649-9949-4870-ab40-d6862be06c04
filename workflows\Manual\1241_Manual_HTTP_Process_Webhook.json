{"id": "A0xnegTHL43LL3eP", "meta": {"instanceId": "a1ae5c8dc6c65e674f9c3947d083abcc749ef2546dff9f4ff01de4d6a36ebfe6", "templateCredsSetupCompleted": true}, "name": "Convert YouTube Videos into SEO Blog Posts", "tags": [], "nodes": [{"id": "c79371d9-c1be-48d4-a2c7-d97a12f4e23c", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [0, 0], "parameters": {}, "typeVersion": 1}, {"id": "7812d81b-3fe8-42a0-8ac8-53161c345e60", "name": "Get YouTube Transcript", "type": "n8n-nodes-base.httpRequest", "position": [440, 0], "parameters": {"url": "https://app.dumplingai.com/api/v1/get-youtube-transcript", "method": "POST", "options": {}, "sendBody": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "videoUrl", "value": "={{ $json['YouTube Video Url'] }}"}, {"name": "includeTimestamps", "value": "={{false}}"}]}, "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpBearerAuth": {"id": "0pq31j7wKqOIHFaR", "name": "Dumpling AI Bear<PERSON> Auth account"}, "httpHeaderAuth": {"id": "ASc5gIQaW1c63ZhO", "name": "Dumpling <PERSON>"}}, "typeVersion": 4.2}, {"id": "bd25a5d9-c2a2-49fa-a73a-dc1d65875629", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [140, -260], "parameters": {"width": 260, "height": 240, "content": "## Set Variables\nSet your variables here, such as:\n- YouTube Video URL: The YouTube video you want to convert into a SEO Blog Post\n- Recipient Email Address: This is the email we send all generated content to at the end of the workflow.\n"}, "typeVersion": 1}, {"id": "52e1de35-84ee-4019-aa5f-3502e6b728e1", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [360, 180], "parameters": {"width": 280, "height": 200, "content": "## Get YouTube Transcript\nThis step gets the transcript of the YouTube video with Dumpling AI. The target video must have captions or subtitles enabled. You can modify this to specify a language.\n "}, "typeVersion": 1}, {"id": "ec94d583-7e25-4ac4-8fb9-beb369832355", "name": "Set Variables", "type": "n8n-nodes-base.set", "position": [220, 0], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "a777e7e9-4334-4a6a-8a4c-f3b6bf5fc94b", "name": "YouTube Video Url", "type": "string", "value": "https://www.youtube.com/watch?v=Dpie2Cd4iB4"}, {"id": "257054fa-5348-475e-965e-5ecd03d901bd", "name": "Recipient Email Address", "type": "string", "value": "<EMAIL>"}]}}, "typeVersion": 3.4}, {"id": "fa1b0e6f-3892-4bc8-8fd8-c96d3a596991", "name": "Generate Blog Post", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [660, 0], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "GPT-4O"}, "options": {}, "messages": {"values": [{"role": "system", "content": "Write a detailed SEO-optimized blog post using the provided YouTube video transcript.\n\nUse the transcript content as the foundation for the blog, extracting key ideas, topics, and themes to highlight. Ensure the blog post is structured with clear headings, subheadings, and paragraphs, incorporating SEO keywords naturally. The tone should be engaging and informative, targeted towards readers interested in the video's subject matter.\n\n- Identify the main topics and themes of the transcript.\n- Extract key points, arguments, or stories present in the transcript.\n- Determine suitable SEO keywords related to the content and integrate them meaningfully.\n- Craft a title that is both engaging and SEO-friendly.\n- Write a comprehensive and well-structured blog post with an introduction, body, and conclusion.\n- Use bullet points, lists, or numbers if necessary for clarity and readability.\n\n# Steps\n\n1. **Transcript Analysis**: Begin by thoroughly reading the provided transcript to understand the core messages and detailed content.\n   \n2. **Identify Key Points**: Extract important points, arguments, or themes that should be highlighted in the blog post.\n\n3. **SEO Keyword Research**: Determine relevant SEO keywords that align with the content’s themes and audiences.\n\n4. **Blog Structuring**: Create an outline for the blog post, arranging sections logically with appropriate headings (H2, H3) for SEO.\n\n5. **Content Writing**: Write each section based on the transcript’s content, ensuring the inclusion of SEO keywords and maintaining a clear and engaging tone.\n\n6. **Review and Edit**: Proofread for grammatical accuracy and SEO optimization. Ensure smooth flow and coherence.\n\n# Output Format\n\nRespond in the following JSON format:\n```json\n{\n  \"title\": \"string\",\n  \"blogImagePrompt\": \"string\",\n  \"description\": \"string\",\n  \"content\": \"string\"\n}\n```\n\n- Title: Craft an SEO-friendly engaging title.\n- Blog Image Prompt: Provide a textual prompt that can be used to generate an image relevant to the blog post content. We prefer abstract images.\n- Description: Summarize the blog post in a concise and engaging way.\n- Content: Write a comprehensive blog using the transcript as reference, structured with clear headings and engaging content.\n\n# Examples\n\n### Input\n[YouTube Transcript]\n\n### Output\n```json\n{\n  \"title\": \"Understanding Renewable Energy: A Path to Sustainability\",\n  \"blogImagePrompt\": \"An illustration of various renewable energy sources like solar panels and wind turbines.\",\n  \"description\": \"Explore the importance and impact of renewable energy on our planet. This blog delves into key themes and actionable insights from the transcript, providing a comprehensive understanding of sustainable energy solutions.\",\n  \"content\": \"Introduction: Renewable energy is transforming our world...\\nBody: Today's pressing challenges around fossil fuels...\\nConclusion: In embracing renewable energy, we...\"\n}\n```\n\n(This example reflects the JSON structure. Use real content from the transcript to fill these sections.)"}, {"content": "={{ $json.transcript }}"}]}, "jsonOutput": true}, "credentials": {"openAiApi": {"id": "fdhWALG84tBLgSZT", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "50bfb015-554d-48f3-aa81-130677620fdd", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [660, -340], "parameters": {"width": 260, "height": 320, "content": "## Generate Blog Post\nHere we get GPT-4o (or a model of your choice) to generate a detailed SEO blog post. We ask for:\n- title \n- description\n- blogImagePrompt\n- content\n\nTo improve this step, you can consider generating section by section or replacing this with an AI Agent with research capabilities"}, "typeVersion": 1}, {"id": "ecc499c0-776e-4cb1-8361-6f05e0cda021", "name": "Generate AI Image", "type": "n8n-nodes-base.httpRequest", "position": [1036, 0], "parameters": {"url": "https://app.dumplingai.com/api/v1/generate-ai-image", "method": "POST", "options": {}, "jsonBody": "={\n  \"model\": \"FLUX.1-dev\",\n  \"input\": {\n    \"prompt\": \"{{ $json.message.content.blogImagePrompt }}\"\n  }\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpBearerAuth": {"id": "0pq31j7wKqOIHFaR", "name": "Dumpling AI Bear<PERSON> Auth account"}, "httpHeaderAuth": {"id": "ASc5gIQaW1c63ZhO", "name": "Dumpling <PERSON>"}}, "typeVersion": 4.2}, {"id": "c0e5dfef-5314-4d30-836a-e9e4d13e4679", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [980, 180], "parameters": {"height": 260, "content": "## Generate Blog Image\nHere we use the FLUX.1-dev model via Dumpling AI to generate a image for the blog post. Feel free to use the image model you prefer. You may need to update to a more powerful (expensive) model if you want text in your images."}, "typeVersion": 1}, {"id": "7d7c0463-7d19-46a3-be5a-d5bd47b82032", "name": "Gmail", "type": "n8n-nodes-base.gmail", "position": [1696, 0], "webhookId": "********-65f6-41ab-9ea7-2f6ffb8cbc40", "parameters": {"sendTo": "={{ $('Set Variables').item.json['Recipient Email Address'] }}", "message": "=Description: {{ $('Generate Blog Post').item.json.message.content.description }}\n\nContent:\n{{ $('Markdown').item.json.htmlContent }}", "options": {"attachmentsUi": {"attachmentsBinary": [{}]}}, "subject": "={{ $('Generate Blog Post').item.json.message.content.title }}"}, "credentials": {"gmailOAuth2": {"id": "g5pJ3U0ehy2NiEiI", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "5af18f00-3d7d-4db4-ab2b-595ef7e8adc3", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.markdown", "position": [1256, 0], "parameters": {"mode": "markdownToHtml", "options": {}, "markdown": "={{ $('Generate Blog Post').item.json.message.content.content }}", "destinationKey": "htmlContent"}, "typeVersion": 1}, {"id": "ca2b7c3e-44e9-4043-916a-b44c3ef662f2", "name": "Download Image", "type": "n8n-nodes-base.httpRequest", "position": [1476, 0], "parameters": {"url": "={{ $('Generate AI Image').item.json.images[0].url }}", "options": {}}, "typeVersion": 4.2}, {"id": "a7414823-fc14-4981-97bb-d14f4622172e", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1180, -200], "parameters": {"width": 260, "height": 180, "content": "## Convert Markdown to HTML\nOpenAI LLMs tend to output markdown. We need to convert to HTML for formatting in the the Gmail node."}, "typeVersion": 1}, {"id": "a0213875-25cb-48b1-b845-e14b2e83efac", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1400, 180], "parameters": {"width": 260, "height": 180, "content": "## Download Image for Attachment\nThe image URL is a temporary URL, so we need to download the image and attach it to the email being sent."}, "typeVersion": 1}, {"id": "35307e5f-662b-41ab-89fb-3519d790fa52", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [1620, -200], "parameters": {"width": 260, "height": 180, "content": "## Send blog post via email for next steps\nWe send all generated content to your email address so you have a record of all generations and can get it ready for publication"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "f91b5401-28b5-435c-a147-64bc124b1993", "connections": {"Markdown": {"main": [[{"node": "Download Image", "type": "main", "index": 0}]]}, "Set Variables": {"main": [[{"node": "Get YouTube Transcript", "type": "main", "index": 0}]]}, "Download Image": {"main": [[{"node": "Gmail", "type": "main", "index": 0}]]}, "Generate AI Image": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Generate Blog Post": {"main": [[{"node": "Generate AI Image", "type": "main", "index": 0}]]}, "Get YouTube Transcript": {"main": [[{"node": "Generate Blog Post", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set Variables", "type": "main", "index": 0}]]}}}