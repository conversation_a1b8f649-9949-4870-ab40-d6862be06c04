{"nodes": [{"name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "position": [0, 250], "parameters": {"updates": ["message"]}, "credentials": {"telegramApi": "Telegram"}, "typeVersion": 1}, {"name": "Monday.com", "type": "n8n-nodes-base.mondayCom", "position": [650, 150], "parameters": {"name": "={{$node[\"Freshdesk\"].json[\"subject\"]}}", "boardId": "565971708", "groupId": "new_group", "resource": "boardItem", "additionalFields": {}}, "credentials": {"mondayComApi": "Monday"}, "typeVersion": 1}, {"name": "Monday.com1", "type": "n8n-nodes-base.mondayCom", "position": [650, 350], "parameters": {"name": "={{$node[\"Telegram Trigger\"].json[\"message\"][\"text\"]}}", "boardId": "565971708", "groupId": "topics", "resource": "boardItem", "additionalFields": {}}, "credentials": {"mondayComApi": "Monday"}, "typeVersion": 1}, {"name": "Telegram", "type": "n8n-nodes-base.telegram", "position": [500, 150], "parameters": {"text": "Hi, thanks for sending this. We will review your request for refund as soon as possible 💶 💵 💷", "chatId": "={{$node[\"Telegram Trigger\"].json[\"message\"][\"chat\"][\"id\"]}}", "additionalFields": {}}, "credentials": {"telegramApi": "Telegram"}, "typeVersion": 1}, {"name": "IF1", "type": "n8n-nodes-base.if", "position": [180, 250], "parameters": {"conditions": {"string": [{"value1": "={{$node[\"Telegram Trigger\"].json[\"message\"][\"text\"]}}", "value2": "refund", "operation": "contains"}]}}, "typeVersion": 1}, {"name": "Freshdesk", "type": "n8n-nodes-base.freshdesk", "position": [350, 150], "parameters": {"options": {"tags": "refund", "subject": "={{$node[\"IF1\"].json[\"message\"][\"text\"]}}"}, "requester": "email", "requesterIdentificationValue": ""}, "credentials": {"freshdeskApi": "Freshdesk"}, "typeVersion": 1}, {"name": "Freshdesk1", "type": "n8n-nodes-base.freshdesk", "position": [350, 350], "parameters": {"options": {"tags": "complaint", "subject": "={{$node[\"IF1\"].json[\"message\"][\"text\"]}}"}, "requester": "email", "requesterIdentificationValue": ""}, "credentials": {"freshdeskApi": "Freshdesk"}, "typeVersion": 1}, {"name": "Telegram1", "type": "n8n-nodes-base.telegram", "position": [500, 350], "parameters": {"text": "Hi, thanks for sending this. We will review your complaint as soon as possible 📬 ☀️ ✅", "chatId": "={{$node[\"Telegram Trigger\"].json[\"message\"][\"chat\"][\"id\"]}}", "additionalFields": {}}, "credentials": {"telegramApi": "Telegram"}, "typeVersion": 1}], "connections": {"IF1": {"main": [[{"node": "Freshdesk", "type": "main", "index": 0}], [{"node": "Freshdesk1", "type": "main", "index": 0}]]}, "Telegram": {"main": [[{"node": "Monday.com", "type": "main", "index": 0}]]}, "Freshdesk": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "Telegram1": {"main": [[{"node": "Monday.com1", "type": "main", "index": 0}]]}, "Freshdesk1": {"main": [[{"node": "Telegram1", "type": "main", "index": 0}]]}, "Telegram Trigger": {"main": [[{"node": "IF1", "type": "main", "index": 0}]]}}}