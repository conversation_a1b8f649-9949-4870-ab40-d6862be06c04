{"nodes": [{"id": "764c42ae-3761-4375-9de4-69ecdaf82b10", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-20, 520], "parameters": {"width": 377.*************, "height": 590.*************, "content": "## 👋 How to use this template\nThis template shows how you can take any event from any service, transform its data and send an alert to your desired app. Here's how to use it:\n\n1. Double click the `Slack` node and connect to your Slack account by creating a Credential.\n2. Change the channel name in the `Slack` node to a channel or user you have in Slack.\n2. Click the `Execute Workflow` button, then double click the nodes to see their input and output data\n\n### To customize this template to you needs:\n1. Enable or swap the `Linear trigger` with any service that fits your use case.\n2. Change the data transformation to fit your needs\n3. Adjust the Slack node or swap it with any node that fits your use case\n4. Disable or remove the `When clicking \"Execute Workflow\"` and `Code` node\n"}, "typeVersion": 1}, {"id": "b35b39f5-2937-437e-b4bb-bfd4fc06b2e2", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [423.*************, 520], "parameters": {"width": 398.*************, "height": 600.*************, "content": "### 1. Trigger step listens for new events\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nWe added a `Linear trigger` that starts the workflow every time we have an `Issue` event int the `Product & Design` team. \n\n**You can replace this node with any trigger you wish, like [<PERSON>ra](https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.jiratrigger/?utm_source=n8n_app&utm_medium=node_settings_modal-credential_link&utm_campaign=n8n-nodes-base.jiraTrigger), [Clickup](https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.clickuptrigger/?utm_source=n8n_app&utm_medium=node_settings_modal-credential_link&utm_campaign=n8n-nodes-base.clickUpTrigger), [HubSpot](https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.hubspottrigger/?utm_source=n8n_app&utm_medium=node_settings_modal-credential_link&utm_campaign=n8n-nodes-base.hubspotTrigger), [Google Sheets](https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.googlesheetstrigger/?utm_source=n8n_app&utm_medium=node_settings_modal-credential_link&utm_campaign=n8n-nodes-base.googleSheetsTrigger) etc.**"}, "typeVersion": 1}, {"id": "466097b6-a830-43fb-9776-d3c7f676fc9a", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1400, 620], "parameters": {"width": 317.52886836027733, "height": 408.7361996915138, "content": "### 3. Notify the right channel\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nLast but not least we're sending a message to the `#important-bugs` channel in Slack.\n\n**You can replace this node with any service like [Teams](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.microsoftteams/?utm_source=n8n_app&utm_medium=node_settings_modal-credential_link&utm_campaign=n8n-nodes-base.microsoftTeams), [Telegram](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.telegram/?utm_source=n8n_app&utm_medium=node_settings_modal-credential_link&utm_campaign=n8n-nodes-base.telegram), [Email](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.sendemail/?utm_source=n8n_app&utm_medium=node_settings_modal-credential_link&utm_campaign=n8n-nodes-base.emailSend) etc.**"}, "typeVersion": 1}, {"id": "99b3eadc-f3ff-4f73-91c2-909ab17ea8ff", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [880, 620], "parameters": {"width": 462, "height": 407, "content": "### 2. Filter and transform your data\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nWe only want to notify the team, if the event is fired on creating an urgent bug.\n\nTo edit the nodes, simply drag and drop input data into the fields or change the values directly. **Besides filters, n8n does have other powerful transformation nodes like [Set](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.set/?utm_source=n8n_app&utm_medium=node_settings_modal-credential_link&utm_campaign=n8n-nodes-base.set), [ItemList](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.itemlists/?utm_source=n8n_app&utm_medium=node_settings_modal-credential_link&utm_campaign=n8n-nodes-base.itemLists), [Code](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.code/?utm_source=n8n_app&utm_medium=node_settings_modal-credential_link&utm_campaign=n8n-nodes-base.code) and many more.**"}, "typeVersion": 1}, {"id": "90e3e605-f497-4aaa-b0be-cb064e9b9ac9", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.linearTrigger", "disabled": true, "position": [500, 600], "webhookId": "b705f01f-3262-46d4-90f2-fc9f962e6766", "parameters": {"teamId": "583b87b7-a8f8-436b-872c-61373503d61d", "resources": ["issue"]}, "credentials": {"linearApi": {"id": "15", "name": "Linear account"}}, "typeVersion": 1}, {"id": "f956bf3b-b119-4006-b964-6fdb089ff877", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "notes": "For testing the workflow", "position": [500, 800], "parameters": {}, "notesInFlow": true, "typeVersion": 1}, {"id": "2b347886-f7a8-44eb-b26a-57c436eda594", "name": "Code", "type": "n8n-nodes-base.code", "notes": "<PERSON><PERSON>", "position": [680, 800], "parameters": {"jsCode": "return [\n  {\n    \"action\": \"create\",\n    \"createdAt\": \"2023-06-27T13:15:14.118Z\",\n    \"data\": {\n      \"id\": \"204224f8-3084-49b0-981f-3ad7f9060316\",\n      \"createdAt\": \"2023-06-27T13:15:14.118Z\",\n      \"updatedAt\": \"2023-06-27T13:15:14.118Z\",\n      \"number\": 647,\n      \"title\": \"Test event\",\n      \"priority\": 3,\n      \"boardOrder\": 0,\n      \"sortOrder\": -48454,\n      \"teamId\": \"583b87b7-a8f8-436b-872c-61373503d61d\",\n      \"previousIdentifiers\": [],\n      \"creatorId\": \"49ae7598-ae5d-42e6-8a03-9f6038a0d37a\",\n      \"stateId\": \"49c4401a-3d9e-40f6-a904-2a5eb95e0237\",\n      \"priorityLabel\": \"No priority\",\n      \"subscriberIds\": [\n        \"49ae7598-ae5d-42e6-8a03-9f6038a0d37a\"\n      ],\n      \"labelIds\": [\n        \"23381844-cdf1-4547-8d42-3b369af5b4ef\"\n      ],\n      \"state\": {\n        \"id\": \"49c4401a-3d9e-40f6-a904-2a5eb95e0237\",\n        \"color\": \"#bec2c8\",\n        \"name\": \"Backlog\",\n        \"type\": \"backlog\"\n      },\n      \"team\": {\n        \"id\": \"583b87b7-a8f8-436b-872c-61373503d61d\",\n        \"key\": \"PD\",\n        \"name\": \"Product & Design\"\n      },\n      \"labels\": [\n        {\n          \"id\": \"23381844-cdf1-4547-8d42-3b369af5b4ef\",\n          \"color\": \"#4CB782\",\n          \"name\": \"bug\"\n        }\n      ]\n    },\n    \"url\": \"https://linear.app/n8n/issue/PD-647/test-event\",\n    \"type\": \"Issue\",\n    \"organizationId\": \"1c35bbc6-9cd4-427e-8bc5-e5d370a9869f\",\n    \"webhookTimestamp\": 1687871714230\n  }\n]"}, "notesInFlow": true, "typeVersion": 1}, {"id": "750acf22-5fc7-40b6-8989-aa8ba1cb207b", "name": "Filter", "type": "n8n-nodes-base.filter", "notes": "Keep urgent bugs only", "position": [960, 700], "parameters": {"conditions": {"number": [{"value1": "={{ $json.data.priority }}", "value2": 3, "operation": "largerEqual"}], "string": [{"value1": "={{ $json.data.labels[0].name }}", "value2": "bug"}]}}, "notesInFlow": true, "typeVersion": 1}, {"id": "8ce7bb41-30f6-4d28-a5c7-ae5cb856ecc2", "name": "Set", "type": "n8n-nodes-base.set", "notes": "Transform title", "position": [1180, 700], "parameters": {"values": {"string": [{"name": "title", "value": "={{ $json.data.title.toTitleCase() }}"}, {"name": "url", "value": "={{ $json.url }}"}]}, "options": {}, "keepOnlySet": true}, "notesInFlow": true, "typeVersion": 2}, {"id": "b9c6f60a-5b69-4bf5-9514-9c9dc9813595", "name": "<PERSON><PERSON>ck", "type": "n8n-nodes-base.slack", "position": [1500, 700], "parameters": {"text": "=<!channel> New urgent bug *<{{ $json.url }}|{{ $json.title }}>*", "select": "channel", "channelId": {"__rl": true, "mode": "name", "value": "#important bugs"}, "otherOptions": {}}, "credentials": {"slackApi": {"id": "6", "name": "<PERSON><PERSON>"}}, "typeVersion": 2}], "connections": {"Set": {"main": [[{"node": "<PERSON><PERSON>ck", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "Filter", "type": "main", "index": 0}]]}, "Filter": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}, "Linear Trigger": {"main": [[{"node": "Filter", "type": "main", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}}}