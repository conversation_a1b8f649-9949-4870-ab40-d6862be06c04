{"id": "2pMoIW58KP6ZeGir", "meta": {"instanceId": "ecc960f484e18b0e09045fd93acf0d47f4cfff25cc212ea348a08ac3aae81850", "templateCredsSetupCompleted": true}, "name": "Luma AI Dream Machine - Simple v1 - AK", "tags": [{"id": "tUlWC9t8VhwpFaci", "name": "Alex - WIP", "createdAt": "2025-02-20T17:17:53.411Z", "updatedAt": "2025-02-20T17:17:53.411Z"}], "nodes": [{"id": "dbe1dbcc-05a0-4439-869c-157e51a99dd1", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-440, 0], "parameters": {}, "typeVersion": 1}, {"id": "603f7fdd-e590-4a51-b606-a9bb9396a0c0", "name": "Text 2 Video", "type": "n8n-nodes-base.httpRequest", "position": [220, 0], "parameters": {"url": "https://api.lumalabs.ai/dream-machine/v1/generations", "method": "POST", "options": {}, "jsonBody": "={\n  \"model\": \"ray-2\",\n  \"prompt\": {{ JSON.stringify($('Global SETTINGS').first().json.video_prompt + \"; camera motion: \" + $json.action) }},\n  \"aspect_ratio\": \"{{ $('Global SETTINGS').first().json.aspect_ratio }}\",\n  \"duration\": \"{{ $('Global SETTINGS').item.json.duration }}\",\n  \"loop\": {{ $('Global SETTINGS').first().json.loop }},\n  \"callback_url\": \"{{ $('Global SETTINGS').first().json.callback_url }}\"\n  \n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "accept", "value": "application/json"}]}}, "credentials": {"httpHeaderAuth": {"id": "zzIlODir90EUTwHh", "name": "<PERSON><PERSON>er Auth account"}}, "typeVersion": 4.2}, {"id": "494ac05e-e0c5-465e-b805-2749683ab789", "name": "RANDOM Camera Motion", "type": "n8n-nodes-base.code", "position": [0, 0], "parameters": {"jsCode": "const items = [\n  \"Static\",\n  \"Move Left\",\n  \"Move Right\",\n  \"Move Up\",\n  \"Move Down\",\n  \"Push In\",\n  \"Pull Out\",\n  \"Zoom In\",\n  \"Zoom Out\",\n  \"Pan Left\",\n  \"Pan Right\",\n  \"Orbit Left\",\n  \"Orbit Right\",\n  \"Crane Up\",\n  \"Crane Down\"\n];\n\nconst randomItem = items[Math.floor(Math.random() * items.length)];\n\nreturn [{ json: { action: randomItem } }];\n"}, "typeVersion": 2}, {"id": "30ba7cfc-d2c3-478f-ae01-0a3397ceb439", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-260, -120], "parameters": {"color": 3, "width": 180, "content": "## Define your SETTINGS here"}, "typeVersion": 1}, {"id": "********-b2a4-43a0-8ec5-1b13c0357e40", "name": "Global SETTINGS", "type": "n8n-nodes-base.set", "position": [-220, 0], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "7064f685-d91f-4049-9fcb-dd7018c1bc8d", "name": "aspect_ratio", "type": "string", "value": "9:16"}, {"id": "3d6d3fe0-4e4a-4d1b-9f6a-08037a4e2785", "name": "video_prompt", "type": "string", "value": "a superhero flying through a volcano"}, {"id": "7ae48bee-0be5-487f-8d6d-ea7fe98fdd36", "name": "loop", "type": "string", "value": "true"}, {"id": "82930db0-971e-4de4-911d-ff5a7fab5d67", "name": "duration", "type": "string", "value": "5s"}, {"id": "b51d9834-87c8-4358-a257-6a02ebe2576d", "name": "cluster_id", "type": "string", "value": "={{ Date.now() + '_' + Math.random().toString(36).slice(2, 10) }}"}, {"id": "8756fe2d-df04-48d4-9cd4-d29b8d9a3ab1", "name": "airtable_base", "type": "string", "value": "appvk87mtcwRve5p5"}, {"id": "a83707ef-3a1c-4b3c-939c-1376bc43cc76", "name": "airtable_table_generated_videos", "type": "string", "value": "tblOzRFWgcsfttRWK"}, {"id": "694528cd-c51e-45ac-8dbe-1b33b347f590", "name": "callback_url", "type": "string", "value": "https://YOURURL.com/luma-ai"}]}}, "typeVersion": 3.4}, {"id": "9f4732b5-8e3e-4fb6-942f-32c72b3eb041", "name": "ADD Video Info", "type": "n8n-nodes-base.airtable", "position": [660, 0], "parameters": {"base": {"__rl": true, "mode": "id", "value": "={{ $('Global SETTINGS').first().json.airtable_base }}"}, "table": {"__rl": true, "mode": "id", "value": "={{ $('Global SETTINGS').first().json.airtable_table_generated_videos }}"}, "columns": {"value": {"Model": "={{ $json.model }}", "Aspect": "={{ $json.request.aspect_ratio }}", "Length": "={{ $json.request.duration }}", "Prompt": "={{ $('Global SETTINGS').first().json.video_prompt }}", "Status": "Done", "Cluster ID": "={{ $('Global SETTINGS').first().json.cluster_id }}", "Resolution": "={{ $json.request.resolution }}", "Generation ID": "={{ $json.id }}"}, "schema": [{"id": "Generation ID", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Generation ID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Status", "type": "options", "display": true, "options": [{"name": "Todo", "value": "Todo"}, {"name": "In progress", "value": "In progress"}, {"name": "Done", "value": "Done"}], "removed": false, "readOnly": false, "required": false, "displayName": "Status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Content Title", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Content Title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Video URL", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Video URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Thumb URL", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Thumb URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Prompt", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Prompt", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "VO", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "VO", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Aspect", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Aspect", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Model", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Model", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Resolution", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Resolution", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Length", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Length", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Created", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "Created", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Cluster ID", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Cluster ID", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "create"}, "credentials": {"airtableTokenApi": {"id": "yqBrLbgHXLcwqH0p", "name": "AlexK Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "9923373d-d4ce-42bb-9f2d-34350f64ac5b", "name": "Execution Data", "type": "n8n-nodes-base.executionData", "position": [440, 0], "parameters": {}, "typeVersion": 1}, {"id": "5044e1f2-c985-4c3a-9386-f4fe4f85f37b", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-40, -120], "parameters": {"color": 5, "width": 840, "content": "## This is where the magic happens... "}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "e756199d-31fc-4e2f-8937-3625295a147c", "connections": {"Text 2 Video": {"main": [[{"node": "Execution Data", "type": "main", "index": 0}]]}, "ADD Video Info": {"main": [[]]}, "Execution Data": {"main": [[{"node": "ADD Video Info", "type": "main", "index": 0}]]}, "Global SETTINGS": {"main": [[{"node": "RANDOM Camera Motion", "type": "main", "index": 0}]]}, "RANDOM Camera Motion": {"main": [[{"node": "Text 2 Video", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Global SETTINGS", "type": "main", "index": 0}]]}}}