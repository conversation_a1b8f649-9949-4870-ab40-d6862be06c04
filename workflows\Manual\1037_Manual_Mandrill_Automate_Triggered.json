{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "Mandrill", "type": "n8n-nodes-base.mandrill", "position": [450, 300], "parameters": {"options": {}, "toEmail": "<EMAIL>", "template": "welcomeemailv2", "fromEmail": "<EMAIL>"}, "credentials": {"mandrillApi": "mandrill_creds"}, "typeVersion": 1}], "connections": {"On clicking 'execute'": {"main": [[{"node": "Mandrill", "type": "main", "index": 0}]]}}}