{"id": "55", "name": "Expense Tracker App", "nodes": [{"name": "Get Receipt", "type": "n8n-nodes-base.typeformTrigger", "position": [450, 300], "webhookId": "b51cc683-1ef6-412f-9885-91e65f151cc0", "parameters": {"formId": ""}, "credentials": {"typeformApi": ""}, "typeVersion": 1}, {"name": "Get Image", "type": "n8n-nodes-base.httpRequest", "position": [650, 300], "parameters": {"url": "={{$node[\"Get Receipt\"].json[\"Upload receipt\"]}}", "options": {}, "responseFormat": "file"}, "typeVersion": 1}, {"name": "Extract Information", "type": "n8n-nodes-base.mindee", "position": [850, 300], "parameters": {}, "credentials": {"mindeeReceiptApi": ""}, "typeVersion": 1}, {"name": "Set Information", "type": "n8n-nodes-base.set", "position": [1050, 300], "parameters": {"values": {"number": [{"name": "Amount", "value": "={{$node[\"Extract Information\"].json[\"total\"]}}"}], "string": [{"name": "Merchant", "value": "={{$node[\"Extract Information\"].json[\"merchant\"]}}"}, {"name": "Date", "value": "={{$node[\"Extract Information\"].json[\"date\"]}}"}, {"name": "Time", "value": "={{$node[\"Extract Information\"].json[\"time\"]}}"}, {"name": "Receipt URL", "value": "={{$node[\"Get Receipt\"].json[\"Upload receipt\"]}}"}, {"name": "Category", "value": "={{$node[\"Extract Information\"].json[\"category\"]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "Store Information", "type": "n8n-nodes-base.airtable", "position": [1250, 300], "parameters": {"table": "Expenses", "options": {}, "operation": "append", "application": ""}, "credentials": {"airtableApi": ""}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Get Image": {"main": [[{"node": "Extract Information", "type": "main", "index": 0}]]}, "Get Receipt": {"main": [[{"node": "Get Image", "type": "main", "index": 0}]]}, "Set Information": {"main": [[{"node": "Store Information", "type": "main", "index": 0}]]}, "Extract Information": {"main": [[{"node": "Set Information", "type": "main", "index": 0}]]}}}