{"id": "0GCQ1fO3d5MBdKmi", "meta": {"instanceId": "fddb3e91967f1012c95dd02bf5ad21f279fc44715f47a7a96a33433621caa253"}, "name": "template-demo-chatgpt-image-1-with-drive-and-sheet copy", "tags": [], "nodes": [{"id": "7d78d4e3-cbb3-4f32-82d9-73c9d7f6c892", "name": "When clicking 'Test workflow'", "type": "n8n-nodes-base.manualTrigger", "disabled": true, "position": [-480, -245], "parameters": {}, "typeVersion": 1}, {"id": "b32b61bb-c837-4697-9742-a1bb2854b628", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [-260, -120], "parameters": {"url": "https://api.openai.com/v1/images/generations", "method": "POST", "options": {}, "sendBody": true, "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "model", "value": "gpt-image-1"}, {"name": "prompt", "value": "={{ $json.chatInput }}"}, {"name": "output_format", "value": "jpeg"}, {"name": "quality", "value": "low"}, {"name": "output_compression", "value": "={{parseInt('80')}}"}, {"name": "size", "value": "1024x1024"}, {"name": "n", "value": "={{parseInt('1')}}"}, {"name": "moderation", "value": "low"}]}, "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "GgwYNKMKKqKJICYO", "name": "OpenAi account - Image"}}, "typeVersion": 4.2}, {"id": "0ead70d0-9e3b-4f19-afee-b5d4a7b532e9", "name": "Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [860, -20], "parameters": {"name": "=chatgpt_created_by_n8n_{{ $('HTTP Request').item.json.created }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "1sIbMHDtcOafBVdCq0gTEuGvnT63s8Fdy", "cachedResultUrl": "https://drive.google.com/drive/folders/1sIbMHDtcOafBVdCq0gTEuGvnT63s8Fdy", "cachedResultName": "n8n-demo-gpt_image_1"}, "inputDataFieldName": "=data"}, "credentials": {"googleDriveOAuth2Api": {"id": "iQdqjdvLVh5ldUIq", "name": "Personal-Google Drive account"}}, "typeVersion": 3}, {"id": "a76c4340-9f34-49d1-a831-1ba4515933ee", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [-40, -120], "parameters": {"options": {"includeBinary": true}, "fieldToSplitOut": "data"}, "typeVersion": 1}, {"id": "c8090e15-b9b9-4999-89f0-97d45e6176d6", "name": "Convert to File", "type": "n8n-nodes-base.convertToFile", "position": [640, -20], "parameters": {"options": {"fileName": "={{ $now.format(\"yyyyMMddHHmmSSS\") }}"}, "operation": "toBinary", "sourceProperty": "b64_json"}, "typeVersion": 1.1}, {"id": "692a71fb-6fe3-4728-a588-f9283f5ab968", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [200, -20], "parameters": {"options": {"reset": false}, "batchSize": "=1"}, "executeOnce": false, "typeVersion": 3}, {"id": "dfa88c15-4d38-4670-9c5a-4e52a9ce9d33", "name": "Edit Fields-file_name", "type": "n8n-nodes-base.set", "position": [420, -20], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "10e6d39e-c44c-4db4-bf88-806b2f36c09f", "name": "file_name", "type": "string", "value": "={{ $now.format(\"yyyyMMddHHmmSSS\") }}"}, {"id": "c2610584-aafa-4d90-8977-399e49015c32", "name": "b64_json", "type": "string", "value": "={{ $json.b64_json }}"}]}}, "typeVersion": 3.4}, {"id": "c34c1a91-2601-4750-8134-d31cf377c349", "name": "Edit Fields1", "type": "n8n-nodes-base.set", "position": [1080, -20], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "dcfa49d6-a8ed-43a2-9aaa-86751f34e61d", "name": "id", "type": "string", "value": "={{ $json.id }}"}, {"id": "d2f7f22d-9453-4b61-bd46-fb3e8d5ad4d8", "name": "webViewLink", "type": "string", "value": "={{ $json.webViewLink }}"}, {"id": "b8cf5a41-e354-416e-b548-8d1a274873e0", "name": "thumbnailLink", "type": "string", "value": "={{ $json.thumbnailLink }}"}, {"id": "76c11a24-087c-4a6c-a5b4-8901e9436786", "name": "file_name", "type": "string", "value": "={{ $('Edit Fields-file_name').item.json.file_name }}"}]}}, "typeVersion": 3.4}, {"id": "6bd8f7dc-1006-4d7f-b3eb-0a3aaa1b9a84", "name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [1300, -20], "parameters": {"columns": {"value": {"image": "={{ $json.webViewLink }}", "prompt": "={{ $('When chat message received').item.json.chatInput }}", "image_thumb": "==IMAGE(\"{{ $('Edit Fields1').item.json.thumbnailLink }}\")"}, "schema": [{"id": "prompt", "type": "string", "display": true, "required": false, "displayName": "prompt", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "image", "type": "string", "display": true, "required": false, "displayName": "image", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "image_thumb", "type": "string", "display": true, "required": false, "displayName": "image_thumb", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {"cellFormat": "USER_ENTERED"}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/11K1tui8itMzcSZqHOmzvFnM0G-ihn1uiLUZ_o478j88/edit#gid=0", "cachedResultName": "工作表1"}, "documentId": {"__rl": true, "mode": "list", "value": "11K1tui8itMzcSZqHOmzvFnM0G-ihn1uiLUZ_o478j88", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/11K1tui8itMzcSZqHOmzvFnM0G-ihn1uiLUZ_o478j88/edit?usp=drivesdk", "cachedResultName": "n8n-chatgpt-image-1-model"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "tufEzuSTEveV3tuA", "name": "(Personal)Google Sheets account"}}, "typeVersion": 4.5}, {"id": "8ee28143-d9e7-4d14-929f-c9b6592c366e", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-480, -45], "webhookId": "f64b2006-672a-4ad6-8c30-428b76f5a332", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "1e687fde-8465-4490-8738-c9832904f2b5", "name": "Google Sheets1", "type": "n8n-nodes-base.googleSheets", "position": [700, -400], "parameters": {"columns": {"value": {"prompt": "={{ $('When chat message received').item.json.chatInput }}", "datetime": "={{ $('HTTP Request').item.json.created.toDateTime('s').format('yyyy-MM-dd HH:mm:ss') }}", "input token": "={{ $('HTTP Request').item.json.usage.input_tokens }}", "output token": "={{ $('HTTP Request').item.json.usage.output_tokens }}", "input estimated price": "={{    (     ($('HTTP Request').item.json.usage.input_tokens || 0) * 10 / 1000000   ).toFixed(6)  }}", "total estimated price": "={{ \n  (\n    (($('HTTP Request').item.json.usage.input_tokens || 0) * 10 / 1000000) +\n    (($('HTTP Request').item.json.usage.output_tokens || 0) * 40 / 1000000)\n  ).toFixed(6)\n}}", "output estimated price": "={{    (     ($('HTTP Request').item.json.usage.output_tokens || 0) * 40 / 1000000   ).toFixed(6)  }}"}, "schema": [{"id": "prompt", "type": "string", "display": true, "required": false, "displayName": "prompt", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "datetime", "type": "string", "display": true, "removed": false, "required": false, "displayName": "datetime", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "input token", "type": "string", "display": true, "removed": false, "required": false, "displayName": "input token", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "input estimated price", "type": "string", "display": true, "removed": false, "required": false, "displayName": "input estimated price", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "output token", "type": "string", "display": true, "removed": false, "required": false, "displayName": "output token", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "output estimated price", "type": "string", "display": true, "removed": false, "required": false, "displayName": "output estimated price", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "total estimated price", "type": "string", "display": true, "removed": false, "required": false, "displayName": "total estimated price", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": *********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/11K1tui8itMzcSZqHOmzvFnM0G-ihn1uiLUZ_o478j88/edit#gid=*********", "cachedResultName": "usage"}, "documentId": {"__rl": true, "mode": "list", "value": "11K1tui8itMzcSZqHOmzvFnM0G-ihn1uiLUZ_o478j88", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/11K1tui8itMzcSZqHOmzvFnM0G-ihn1uiLUZ_o478j88/edit?usp=drivesdk", "cachedResultName": "n8n-chatgpt-image-1-model"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "tufEzuSTEveV3tuA", "name": "(Personal)Google Sheets account"}}, "typeVersion": 4.5}, {"id": "5e1f6dd3-6c1a-4838-86c7-2a3c0cf05c3d", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "position": [480, -400], "parameters": {"options": {}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}, {"id": "f14edb71-0778-40dc-9f2d-4cfc72b8a351", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-540, -600], "parameters": {"color": 7, "width": 340, "height": 240, "content": "## Created by da<PERSON>_tw_ \n\nAn engineer now focus on AI and Automation\n\n### contact me with following:\n[X](https://x.com/darrell_tw_)\n[Threads](https://www.threads.net/@darrell_tw_)\n[Instagram](https://www.instagram.com/darrell_tw_/)\n[Website](https://www.darrelltw.com/)"}, "typeVersion": 1}, {"id": "6bbe2346-287b-491d-bf20-a76d39a6e297", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-540, -340], "parameters": {"width": 660, "height": 480, "content": "## Use Chat to input prompts for image generation"}, "typeVersion": 1}, {"id": "8dd6607a-16cf-424d-902a-04c43e68f424", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [160, -200], "parameters": {"color": 2, "width": 1260, "height": 420, "content": "## Process image array data with Loop\nRegardless of single or multiple images\nThey will be in the data[] array\nJust use <PERSON> to process them\n\nImages will be uploaded to Drive and saved as a row in the Sheet with links and thumbnails"}, "typeVersion": 1}, {"id": "f8d0819a-e38a-4f7a-aa79-594ebca465a0", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [400, -480], "parameters": {"color": 6, "width": 480, "height": 260, "content": "## After processing, save Cost to Sheet"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "cf533114-4aa9-4b06-8247-3c06f9dcbc79", "connections": {"Aggregate": {"main": [[{"node": "Google Sheets1", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}, "Google Drive": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Google Sheets": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[{"node": "Google Drive", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}], [{"node": "Edit Fields-file_name", "type": "main", "index": 0}]]}, "Edit Fields-file_name": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "When clicking 'Test workflow'": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}}}