{"meta": {"instanceId": "cb484ba7b742928a2048bf8829668bed5b5ad9787579adea888f05980292a4a7", "templateCredsSetupCompleted": true}, "nodes": [{"id": "25fb4302-853a-421d-8e4f-4a18d723c4a0", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "disabled": true, "position": [-860, 380], "parameters": {}, "typeVersion": 1}, {"id": "acb8e29a-75b8-4ccb-aca8-20d5a7053334", "name": "<PERSON>", "type": "n8n-nodes-base.gong", "disabled": true, "position": [-440, 120], "parameters": {"filters": {"toDateTime": "={{ $now.toISO() }}", "fromDateTime": "={{ $now.minus({ days: 2 }).toISO() }}"}, "options": {}, "returnAll": true, "requestOptions": {}}, "credentials": {"gongApi": {"id": "EchfvOC4rjw8MUkr", "name": "<PERSON>"}}, "typeVersion": 1}, {"id": "930a7fc9-64a1-4966-be0d-c58132b735e5", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-700, -60], "parameters": {"color": 7, "width": 1080, "height": 920, "content": "## Get Gong Calls and Supporting Data\nBesides the phone calls, integration and competitor data is extracted to supplement the AI prompt with accurate data to compare against mispronunciations. "}, "typeVersion": 1}, {"id": "f21ae8cc-eed1-4d31-8b1f-cc731e3dc642", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [400, -60], "parameters": {"color": 7, "width": 880, "height": 920, "content": "## Remove Duplicates from Queue\nChecks notion for already processed calls and removes them from the processing queue ensuring data is not duplicated. "}, "typeVersion": 1}, {"id": "d796312a-2a7f-429f-8550-d4af6d81a26d", "name": "Transcript Processor", "type": "n8n-nodes-base.executeWorkflow", "position": [2200, 640], "parameters": {"options": {}, "workflowId": {"__rl": true, "mode": "list", "value": "7BAQDjnHQVYO1SWG", "cachedResultName": "Transcript Processor Demo"}}, "typeVersion": 1.1}, {"id": "b381f944-d865-450e-a24d-31d394a01b36", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1820, 420], "parameters": {"color": 7, "width": 700, "height": 440, "content": "## Generate Clean Transcript \nAllows for reduced prompting in the OpenAI node. "}, "typeVersion": 1}, {"id": "7a87e6a0-0009-4776-bf2e-bea68702c808", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1820, -40], "parameters": {"color": 7, "width": 700, "height": 440, "content": "## Pass Call Transcripts to Call Processor\nThe OpenAI node handles this process and outputs in structured JSON."}, "typeVersion": 1}, {"id": "4c3b5280-c5e1-49d1-9651-e3fdd45978f7", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1300, -60], "parameters": {"color": 7, "width": 500, "height": 920, "content": "## Loop through all calls to get enrichment\nAllows for easier processing due to complexity "}, "typeVersion": 1}, {"id": "ce942178-c93e-490a-9b4e-0798f8c5c742", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-1080, -340], "parameters": {"color": 5, "width": 360, "height": 1200, "content": "![Callforge](https://uploads.n8n.io/templates/callforgeshadow.png)\n## CallForge - The AI Gong Sales Call Processor\nCallForge allows you to extract important information for different departments from your Sales Gong Calls. \n\n### Call PreProcessor\nThis workflow preps the calls to pass into the call processor. It also pulls data from the product in order to enrich the AI Prompt to catch typos in the Gong call transcript. It then cleans up the transcript into a single string and then sends it to the call processor."}, "typeVersion": 1}, {"id": "41f3d049-e25b-453a-bf98-501af7f177d0", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-860, 580], "parameters": {}, "typeVersion": 1}, {"id": "02265963-ab06-4bf5-8b0b-5299cd3330c9", "name": "Call Aggregator", "type": "n8n-nodes-base.aggregate", "position": [-100, 120], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "calls"}, "typeVersion": 1}, {"id": "044e1b65-059c-4084-b85a-e10e6149be34", "name": "Integration Aggregator", "type": "n8n-nodes-base.aggregate", "position": [-240, 380], "parameters": {"options": {}, "fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "Google Sheets"}]}}, "typeVersion": 1}, {"id": "31df099f-45ec-4dff-b968-a90e7eaa67b5", "name": "Get Integrations", "type": "n8n-nodes-base.googleSheets", "position": [-460, 380], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "list", "value": 1859794756, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1DKrLntdoNScMey5Bb4ggSpS8NFHlYN3kuTJQbrbJU7I/edit#gid=1859794756", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1DKrLntdoNScMey5Bb4ggSpS8NFHlYN3kuTJQbrbJU7I", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1DKrLntdoNScMey5Bb4ggSpS8NFHlYN3kuTJQbrbJU7I/edit?usp=drivesdk", "cachedResultName": "Most Popular Node Combos"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "4ZBfVX71VUd6pRy3", "name": "Google Sheets Angel Access"}}, "executeOnce": true, "typeVersion": 4.5}, {"id": "42a8299e-d2e4-48a9-bba9-1c5e419d8c0c", "name": "Comma Separate Integrations", "type": "n8n-nodes-base.set", "position": [-20, 380], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "39dfde65-e5e0-46d8-8596-af7ea31fcd3b", "name": "integrations", "type": "string", "value": "={{ $json[\"Google Sheets\"].join() }}"}]}}, "typeVersion": 3.4}, {"id": "4226f79a-8b28-46d7-8b3b-972ef41d9535", "name": "Comma separate competitors", "type": "n8n-nodes-base.set", "position": [-20, 580], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c419af9b-f161-4aac-863f-3a450aaf759f", "name": "competitors", "type": "string", "value": "={{ $jmespath($json.properties['Competitor vs.'].select.options, '[].name').join() }}"}]}}, "typeVersion": 3.4}, {"id": "cce3fab9-63fb-49de-8e3f-4fb7956d0b80", "name": "Get list of Competitors", "type": "n8n-nodes-base.notion", "position": [-460, 580], "parameters": {"simple": false, "resource": "database", "databaseId": {"__rl": true, "mode": "list", "value": "2cb8596f-2029-4d15-bf56-7001652f6fcf", "cachedResultUrl": "https://www.notion.so/2cb8596f20294d15bf567001652f6fcf", "cachedResultName": "n8n vs."}}, "credentials": {"notionApi": {"id": "80", "name": "Notion david-internal"}}, "retryOnFail": true, "typeVersion": 2.2, "waitBetweenTries": 3000}, {"id": "05e82483-ed91-4733-a8a7-621d8cf6f3f1", "name": "Merge 3 objects into one", "type": "n8n-nodes-base.merge", "position": [260, 380], "parameters": {"numberInputs": 3}, "typeVersion": 3}, {"id": "f01f839a-b0b5-4368-8cd3-3466f3cd44a4", "name": "Aggregate Call Data", "type": "n8n-nodes-base.aggregate", "position": [560, 240], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "calldata"}, "typeVersion": 1}, {"id": "c89b9deb-7f52-4d0d-8cdf-ce1b2a1771d2", "name": "Split Out Call Data and Competitors", "type": "n8n-nodes-base.splitOut", "position": [760, 240], "parameters": {"include": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options": {}, "fieldToSplitOut": "calldata[0].calls", "fieldsToInclude": "calldata[1].integrations, , calldata[2].competitors"}, "typeVersion": 1}, {"id": "c5c90fd2-03f4-425c-9a49-b26887705c6c", "name": "Reduce down to 1 object", "type": "n8n-nodes-base.aggregate", "position": [480, 580], "parameters": {"options": {}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}, {"id": "3835aeb8-589c-49b5-995a-2bf0bc0698a8", "name": "Get Previous Phone Calls", "type": "n8n-nodes-base.notion", "position": [700, 580], "parameters": {"options": {}, "resource": "databasePage", "operation": "getAll", "returnAll": true, "databaseId": {"__rl": true, "mode": "list", "value": "1a85b6e0-c94f-81a3-aa21-e3ccf8296d72", "cachedResultUrl": "https://www.notion.so/1a85b6e0c94f81a3aa21e3ccf8296d72", "cachedResultName": "Sales Call Summaries Demo"}}, "credentials": {"notionApi": {"id": "2B3YIiD4FMsF9Rjn", "name": "Angelbot Notion"}}, "retryOnFail": true, "typeVersion": 2.2, "waitBetweenTries": 3000}, {"id": "9b7d60bd-d08a-4529-aefb-c89f277fcd8f", "name": "Isolate Only Call IDs", "type": "n8n-nodes-base.set", "position": [900, 580], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "328e6ac8-88f3-4c2f-b8e8-d4a0756efd24", "name": "Call ID", "type": "string", "value": "={{ $json.property_gong_call_id ? $json.property_gong_call_id : \"none\" }}"}]}}, "typeVersion": 3.4}, {"id": "3eb7613c-2eaa-430a-9f67-5fc486b84ff0", "name": "Only Process New Calls", "type": "n8n-nodes-base.compareDatasets", "position": [1120, 420], "parameters": {"options": {}, "resolve": "preferInput1", "mergeByFields": {"values": [{"field1": "['calldata[0].calls'].id", "field2": "Call ID"}]}}, "typeVersion": 2.3}, {"id": "5e69b2a1-eb6a-4bb4-a126-0605f60ff95b", "name": "Loop Over Calls", "type": "n8n-nodes-base.splitInBatches", "position": [1500, 400], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "6b170ccb-d492-4e7e-9aeb-13c769d36040", "name": "Process All Call Transcripts", "type": "n8n-nodes-base.executeWorkflow", "position": [2200, 140], "parameters": {"options": {}, "workflowId": {"__rl": true, "mode": "list", "value": "cg4Eo7yZlhWkqHCB", "cachedResultName": "Call Processor Demo"}}, "typeVersion": 1.1}, {"id": "3107c6d8-0c8c-4dad-bcbc-c897b0be45b9", "name": "Receive all Transcripts", "type": "n8n-nodes-base.noOp", "position": [1920, 140], "parameters": {}, "typeVersion": 1}], "pinData": {}, "connections": {"Gong": {"main": [[{"node": "Call Aggregator", "type": "main", "index": 0}]]}, "Call Aggregator": {"main": [[{"node": "Merge 3 objects into one", "type": "main", "index": 0}]]}, "Loop Over Calls": {"main": [[{"node": "Receive all Transcripts", "type": "main", "index": 0}], [{"node": "Transcript Processor", "type": "main", "index": 0}]]}, "Get Integrations": {"main": [[{"node": "Integration Aggregator", "type": "main", "index": 0}]]}, "Aggregate Call Data": {"main": [[{"node": "Split Out Call Data and Competitors", "type": "main", "index": 0}]]}, "Transcript Processor": {"main": [[{"node": "Loop Over Calls", "type": "main", "index": 0}]]}, "Isolate Only Call IDs": {"main": [[{"node": "Only Process New Calls", "type": "main", "index": 1}]]}, "Integration Aggregator": {"main": [[{"node": "Comma Separate Integrations", "type": "main", "index": 0}]]}, "Only Process New Calls": {"main": [[{"node": "Loop Over Calls", "type": "main", "index": 0}]]}, "Get list of Competitors": {"main": [[{"node": "Comma separate competitors", "type": "main", "index": 0}]]}, "Receive all Transcripts": {"main": [[{"node": "Process All Call Transcripts", "type": "main", "index": 0}]]}, "Reduce down to 1 object": {"main": [[{"node": "Get Previous Phone Calls", "type": "main", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}, {"node": "Get list of Competitors", "type": "main", "index": 0}, {"node": "Get Integrations", "type": "main", "index": 0}]]}, "Get Previous Phone Calls": {"main": [[{"node": "Isolate Only Call IDs", "type": "main", "index": 0}]]}, "Merge 3 objects into one": {"main": [[{"node": "Aggregate Call Data", "type": "main", "index": 0}, {"node": "Reduce down to 1 object", "type": "main", "index": 0}]]}, "Comma separate competitors": {"main": [[{"node": "Merge 3 objects into one", "type": "main", "index": 2}]]}, "Comma Separate Integrations": {"main": [[{"node": "Merge 3 objects into one", "type": "main", "index": 1}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}, {"node": "Get Integrations", "type": "main", "index": 0}, {"node": "Get list of Competitors", "type": "main", "index": 0}]]}, "Split Out Call Data and Competitors": {"main": [[{"node": "Only Process New Calls", "type": "main", "index": 0}]]}}}