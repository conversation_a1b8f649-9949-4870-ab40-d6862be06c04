{"meta": {"instanceId": "1dd912a1610cd0376bae7bb8f1b5838d2b601f42ac66a48e012166bb954fed5a", "templateId": "2299", "templateCredsSetupCompleted": true}, "nodes": [{"id": "edf41c95-2421-4008-9097-73687fe4bbfc", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [380, 240], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.2}, {"id": "bde8d167-b7c4-4fc8-a256-b022bb33347d", "name": "Test Data", "type": "n8n-nodes-base.set", "position": [800, 240], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "e0e09aa8-2374-43f7-87bf-f2ffcac8e1d9", "name": "name", "type": "string", "value": "n8n"}, {"id": "2086908e-c301-4392-9cf6-b6461e11dcd4", "name": "url", "type": "string", "value": "https://n8n.io/"}]}}, "typeVersion": 3.3}, {"id": "e53d7ec5-f98a-41fe-b082-00e2f680dcea", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [760, 40], "parameters": {"content": "## Test Data \n\nUsing n8n.io as test url.\n\nFor production use, you have to connect your data here."}, "typeVersion": 1}, {"id": "835c2a8c-edd6-43dc-b898-e2c49dd65beb", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1120, -40], "parameters": {"width": 389, "height": 255.7976193268613, "content": "## Web Scraping \n\nUsing **Scrappey's** API to scrape every website.\n\nDon't get blocked again by anti-bot technologies while scraping the web.\n\n**Setup:**\nReplace YOUR_API_KEY with [your Scrappey API key.](https://scrappey.com/?ref=n8n)\n"}, "typeVersion": 1}, {"id": "7f8b3077-ec09-4fec-a4f0-f6b7f3f7ec0e", "name": "Scrape website with Scrappey", "type": "n8n-nodes-base.httpRequest", "position": [1280, 240], "parameters": {"url": "https://publisher.scrappey.com/api/v1", "method": "POST", "options": {"redirect": {"redirect": {}}}, "sendBody": true, "sendQuery": true, "bodyParameters": {"parameters": [{"name": "cmd", "value": "request.get"}, {"name": "url", "value": "={{ $json.url }}"}]}, "queryParameters": {"parameters": [{"name": "key", "value": "YOUR_API_KEY"}]}}, "typeVersion": 4.2}], "pinData": {}, "connections": {"Test Data": {"main": [[{"node": "Scrape website with Scrappey", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Test Data", "type": "main", "index": 0}]]}}}