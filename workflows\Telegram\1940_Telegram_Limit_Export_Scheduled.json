{"id": "o4sdVtTrkuZXDATf", "meta": {"instanceId": "31e69f7f4a77bf465b805824e303232f0227212ae922d12133a0f96ffeab4fef", "templateCredsSetupCompleted": true}, "name": "✨😃Automated Workflow Backups to Google Drive", "tags": [], "nodes": [{"id": "f3eba5f7-534e-4eaa-ac84-850d51ff2936", "name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [620, -140], "parameters": {}, "typeVersion": 1}, {"id": "383e4bed-38ec-4b2f-890c-9b0d9cda8e11", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [1140, 340], "parameters": {"options": {"reset": false}}, "typeVersion": 3}, {"id": "38c4c909-fa18-4fa3-abf5-6b9bd2d46718", "name": "Every Day", "type": "n8n-nodes-base.scheduleTrigger", "position": [620, 80], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.2}, {"id": "67f62b6e-fe37-4638-89ce-9fe1de041282", "name": "Create Folder with DateTime Stamp", "type": "n8n-nodes-base.googleDrive", "position": [1220, -40], "parameters": {"name": "=n8n-Workflow-Backups-{{ $json.datetime }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "root", "cachedResultName": "/ (Root folder)"}, "resource": "folder"}, "credentials": {"googleDriveOAuth2Api": {"id": "UhdXGYLTAJbsa0xX", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "cfbda56a-2d14-4d64-b40d-89961c0cf0f4", "name": "Get DateTIme", "type": "n8n-nodes-base.set", "position": [920, -40], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "2589e80c-e8c3-4872-bd7a-d3e92f4a6ab7", "name": "datetime", "type": "string", "value": "={{ $now }}"}]}}, "typeVersion": 3.4}, {"id": "93e8097f-9e7d-49ff-9133-4fd8590f7e31", "name": "Get Workflows", "type": "n8n-nodes-base.n8n", "position": [1520, -40], "parameters": {"filters": {}, "requestOptions": {}}, "credentials": {"n8nApi": {"id": "49QOgnBpyVcT7XQF", "name": "n8n account"}}, "typeVersion": 1}, {"id": "d848ce1d-beb7-4e25-82ac-ed0e8f9523e0", "name": "Limit to 200", "type": "n8n-nodes-base.limit", "position": [1820, -40], "parameters": {"maxItems": 200}, "typeVersion": 1}, {"id": "18f61908-97ce-478c-8544-cfedef22a94c", "name": "Convert Workflow to JSON File", "type": "n8n-nodes-base.convertToFile", "position": [1400, 340], "parameters": {"options": {"fileName": "={{ $json.name }}"}, "operation": "to<PERSON><PERSON>"}, "typeVersion": 1.1}, {"id": "97690d84-a0cd-4169-83a8-e4f1d189837e", "name": "Save JSON File to Google Drive Folder", "type": "n8n-nodes-base.googleDrive", "position": [1600, 340], "parameters": {"name": "={{ $binary.data.fileName }}.json", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "id", "value": "={{ $('Create Folder with DateTime Stamp').item.json.id }}"}}, "credentials": {"googleDriveOAuth2Api": {"id": "UhdXGYLTAJbsa0xX", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "452c0ce8-6e1f-41d7-a94c-25c7abbc32ad", "name": "Execute Once", "type": "n8n-nodes-base.noOp", "position": [980, 720], "parameters": {}, "executeOnce": true, "typeVersion": 1}, {"id": "aadc54d7-2458-4b5b-aa65-07aff52626d2", "name": "Search Folder Names", "type": "n8n-nodes-base.googleDrive", "position": [1180, 720], "parameters": {"limit": 10, "filter": {"whatToSearch": "folders"}, "options": {}, "resource": "fileFolder", "queryString": "n8n-Workflow-Backups"}, "credentials": {"googleDriveOAuth2Api": {"id": "UhdXGYLTAJbsa0xX", "name": "Google Drive account"}}, "executeOnce": true, "typeVersion": 3}, {"id": "fcb210bf-4947-4178-b85a-8425eb72d937", "name": "Delete Folders", "type": "n8n-nodes-base.googleDrive", "onError": "continueRegularOutput", "position": [1600, 720], "parameters": {"options": {"deletePermanently": true}, "resource": "folder", "operation": "deleteFolder", "folderNoRootId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}}, "credentials": {"googleDriveOAuth2Api": {"id": "UhdXGYLTAJbsa0xX", "name": "Google Drive account"}}, "typeVersion": 3, "alwaysOutputData": true}, {"id": "ccdc0655-75e9-4c6b-8ebb-76477733289b", "name": "Complete Message", "type": "n8n-nodes-base.telegram", "position": [960, 1040], "webhookId": "382a3b43-b83f-47b1-a276-67c6b98a441a", "parameters": {"text": "={{ $now }}\nWorkflows Backup Complete\n{{ $('Create Folder with DateTime Stamp').item.json.name }}\nhttps://drive.google.com/drive/folders/{{ $('Create Folder with DateTime Stamp').item.json.id }}", "chatId": "={{ $env.TELEGRAM_CHAT_ID }}", "additionalFields": {"parse_mode": "HTML", "appendAttribution": false}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "972b4921-803f-4510-9894-9acd2713816a", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1020, 220], "parameters": {"color": 5, "width": 800, "height": 360, "content": "## Save Workflows to Google Drive"}, "typeVersion": 1}, {"id": "254d12e9-0ca6-4953-b375-66a883b44d41", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [840, 620], "parameters": {"color": 3, "width": 980, "height": 300, "content": "## Keep Most Recent 7 Folders (Days) and Delete Others"}, "typeVersion": 1}, {"id": "a1f25512-16d1-45e9-8b18-706288543e03", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [840, 960], "parameters": {"width": 340, "height": 260, "content": "## Notify User via Telegram"}, "typeVersion": 1}, {"id": "36182be7-f575-4f28-8d63-39802b8428ba", "name": "Find Folders to Delete", "type": "n8n-nodes-base.code", "position": [1400, 720], "parameters": {"jsCode": "// Get all input items and sort by name in descending order\nconst sortedItems = $input.all().sort((a, b) => {\n  if (!a.name || !b.name) return 0;\n  return b.name.localeCompare(a.name);\n});\n\n// Get items older than 7 days\nconst olderItems = sortedItems.slice(7);\n\nreturn olderItems\n\n"}, "typeVersion": 2}, {"id": "3a31ee24-3d6c-4340-9c5e-bb1c1cce6151", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1740, -160], "parameters": {"width": 260, "height": 340, "content": "## Limit for Debugging\nRemove this once you have it up and running"}, "typeVersion": 1}, {"id": "df815c43-f6f9-44b8-9503-6a8d0167b844", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1440, -160], "parameters": {"width": 260, "height": 340, "content": "## Get All Workflows\n"}, "typeVersion": 1}, {"id": "c0433a5f-7f6c-4af4-bbbb-ca914aeef33f", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1140, -160], "parameters": {"width": 260, "height": 340, "content": "## Create NEW Google Folder\n"}, "typeVersion": 1}, {"id": "adba380e-16c2-4647-a701-9d5cec1baa0f", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [840, -160], "parameters": {"width": 260, "height": 340, "content": "## Get DateTime Stamp\n"}, "typeVersion": 1}, {"id": "4041f4fb-2b51-48e7-af55-b7351a52e4ea", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-160, -160], "parameters": {"color": 7, "width": 700, "height": 1480, "content": "# ✨😃 Automated Workflow Backups to Google Drive\n\nThis workflow automates the process of backing up your n8n workflows to Google Drive daily. It creates timestamped folders, saves workflows as JSON files, and manages old backups by retaining only the most recent seven days of data. Notifications are sent via Telegram to keep you informed of the backup status.\n\n## How It Works\n\n### Backup Creation Process 🗂️\n- **Triggering Backups**: The workflow starts with either a manual trigger or a scheduled trigger that runs daily.\n- **Folder Creation**: Creates a new folder in Google Drive with a timestamped name (e.g., `n8n-Workflow-Backups-YYYY-MM-DD`).\n- **Workflow Retrieval**: Fetches all workflows from your n8n instance.\n- **File Conversion**: Converts each workflow into a JSON file for storage.\n- **File Upload**: Saves the JSON files into the newly created Google Drive folder.\n\n### Backup Management 🔄\n- **Folder Search**: Searches for existing backup folders in Google Drive with names matching `n8n-Workflow-Backups`.\n- **Retention Policy**: Identifies folders older than seven days using a custom JavaScript function and deletes them permanently to free up space.\n\n### Notifications 📲\n- **Telegram Alerts**: Sends a message via Telegram once the backup process is complete, including the folder name and a link to access it in Google Drive.\n\n## Setup Steps\n\n### API Configuration 🔑\n1. **Google Drive Integration**:\n   - Set up Google Drive OAuth2 credentials in n8n.\n   - Specify the root folder or desired location for backups.\n2. **n8n API Access**:\n   - Configure n8n API credentials to allow fetching workflows.\n3. **Telegram Notifications**:\n   - Add your Telegram bot credentials and chat ID for notification delivery.\n\n### Workflow Customization ⚙️\n1. Define the schedule for automatic backups (e.g., daily at midnight).\n2. Adjust the retention period if you need more or fewer days of backups.\n3. Customize the Telegram message format as needed.\n\n### Testing & Deployment 🚀\n1. Run the workflow manually to verify folder creation and file uploads.\n2. Check that old folders are deleted correctly after seven days.\n3. Confirm Telegram notifications are sent with accurate details.\n\n## Use Case Scenarios\nThis workflow is perfect for teams or individuals who want to ensure their n8n workflows are securely backed up and organized. It is especially useful for:\n- Protecting against accidental data loss.\n- Automating routine administrative tasks.\n\n\nBy combining automated backups, retention management, and real-time notifications, this workflow ensures your n8n workflows are always safe and accessible!\n"}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"timezone": "America/Vancouver", "callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1"}, "versionId": "11ff8d25-bbc5-4681-b292-ac60a00fd7b0", "connections": {"Every Day": {"main": [[{"node": "Get DateTIme", "type": "main", "index": 0}]]}, "Execute Once": {"main": [[{"node": "Search Folder Names", "type": "main", "index": 0}, {"node": "Complete Message", "type": "main", "index": 0}]]}, "Get DateTIme": {"main": [[{"node": "Create Folder with DateTime Stamp", "type": "main", "index": 0}]]}, "Limit to 200": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Get Workflows": {"main": [[{"node": "Limit to 200", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Execute Once", "type": "main", "index": 0}], [{"node": "Convert Workflow to JSON File", "type": "main", "index": 0}]]}, "Search Folder Names": {"main": [[{"node": "Find Folders to Delete", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Get DateTIme", "type": "main", "index": 0}]]}, "Find Folders to Delete": {"main": [[{"node": "Delete Folders", "type": "main", "index": 0}]]}, "Convert Workflow to JSON File": {"main": [[{"node": "Save JSON File to Google Drive Folder", "type": "main", "index": 0}]]}, "Create Folder with DateTime Stamp": {"main": [[{"node": "Get Workflows", "type": "main", "index": 0}]]}, "Save JSON File to Google Drive Folder": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}}}