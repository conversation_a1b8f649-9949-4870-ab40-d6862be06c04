{"nodes": [{"id": "e10615ff-41dc-4ea6-981a-d8e949e2e386", "name": "telegram account", "type": "n8n-nodes-base.code", "position": [-220, 0], "parameters": {"jsCode": "const accountId = $('jira-webhook').first().json.body.fields.assignee?.accountId\n\nconst telegramAccounts = {\n  \"[jira account id]\": ********, // telegram chat id\n}\n\nconst telegramChatId = telegramAccounts[accountId]\n\nreturn [{telegramChatId}]"}, "typeVersion": 2}, {"id": "a0effbdb-8f99-4248-9a98-aba34ff67690", "name": "check tg account exists", "type": "n8n-nodes-base.if", "position": [40, 120], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "loose"}, "combinator": "and", "conditions": [{"id": "149c600c-7030-4480-a4ef-18f02fd9ade9", "operator": {"type": "number", "operation": "exists", "singleValue": true}, "leftValue": "={{ $('telegram account').item.json.telegramChatId }}", "rightValue": ""}]}, "looseTypeValidation": true}, "typeVersion": 2.2}, {"id": "71d58c37-9934-4b10-8aed-d66175a1bc3a", "name": "check type", "type": "n8n-nodes-base.switch", "position": [300, 0], "parameters": {"rules": {"values": [{"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $('jira-webhook').item.json.headers.type }}", "rightValue": "created"}]}}, {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "1ec37373-db94-401d-8913-9f18d2bb8b08", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('jira-webhook').item.json.headers.type }}", "rightValue": "updated"}]}}, {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "12b237f5-d9ef-46be-98f9-60fe74a54298", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('jira-webhook').item.json.headers.type }}", "rightValue": "change-assignee"}]}}]}, "options": {}}, "typeVersion": 3.2}, {"id": "251f6e9b-439a-46f6-bb7d-be04e722a494", "name": "Send Update", "type": "n8n-nodes-base.telegram", "position": [580, 0], "parameters": {"text": "=⚠️ Update {{ $('jira-webhook').item.json.body.fields.issuetype.name }}\n\n🔰 Project: `{{ $('jira-webhook').item.json.body.fields.project.name }}`\n\n🆔 Key: `{{ $('jira-webhook').item.json.body.key }}`\n\n🔰 Title: `{{ $('jira-webhook').item.json.body.fields.summary }}`\n\n🔰 Description: `{{ $('jira-webhook').item.json.body.fields.description }}`\n\nCreate Time: `{{ DateTime.fromMillis($('jira-webhook').item.json.body.fields.created).format(\"yyyy-MM-dd HH:mm\") }}`", "chatId": "={{ $(\"telegram account\").item.json.telegramChatId }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "Sg6YvV1Qx1JnVVWu", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "8efbed55-8642-440c-9ec7-8b93256a27f5", "name": "Send Create", "type": "n8n-nodes-base.telegram", "position": [580, -180], "parameters": {"text": "=🆕 New {{ $('jira-webhook').item.json.body.fields.issuetype.name }}\n\n🔰 Project: `{{ $('jira-webhook').item.json.body.fields.project.name }}`\n\n🆔 Key: `{{ $('jira-webhook').item.json.body.key }}`\n\n🔰 Title: `{{ $('jira-webhook').item.json.body.fields.summary }}`\n\n🔰 Description: `{{ $('jira-webhook').item.json.body.fields.description }}`\n\nCreate Time: `{{ DateTime.fromMillis($('jira-webhook').item.json.body.fields.created).format(\"yyyy-MM-dd HH:mm\") }}`", "chatId": "={{ $(\"telegram account\").item.json.telegramChatId }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "Sg6YvV1Qx1JnVVWu", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "9c2889e7-7c9c-490c-8293-fed3c255f086", "name": "Send Assign <PERSON>", "type": "n8n-nodes-base.telegram", "position": [580, 180], "parameters": {"text": "=👩‍💻👨‍💻 Assigned to you {{ $('jira-webhook').item.json.body.fields.issuetype.name }}\n\n🔰 Project: `{{ $('jira-webhook').item.json.body.fields.project.name }}`\n\n🆔 Key: `{{ $('jira-webhook').item.json.body.key }}`\n\n🔰 Title: `{{ $('jira-webhook').item.json.body.fields.summary }}`\n\n🔰 Description: `{{ $('jira-webhook').item.json.body.fields.description }}`\n\nCreate Time: `{{ DateTime.fromMillis($('jira-webhook').item.json.body.fields.created).format(\"yyyy-MM-dd HH:mm\") }}`", "chatId": "={{ $(\"telegram account\").item.json.telegramChatId }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "Sg6YvV1Qx1JnVVWu", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "f660857d-ff24-4c08-bb13-e2461da950d6", "name": "check issue body, assignee and hook type", "type": "n8n-nodes-base.if", "position": [-480, 120], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "6862ba4b-7f46-44d2-9f82-da33b3ed0166", "operator": {"type": "object", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $('jira-webhook').item.json.body }}", "rightValue": ""}, {"id": "67527de5-e12c-4917-b1f6-791c79b08637", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $('jira-webhook').item.json.headers.type }}", "rightValue": ""}, {"id": "26a19a6a-a072-4035-a1cd-************", "operator": {"type": "object", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $('jira-webhook').item.json.body.fields.assignee }}", "rightValue": "="}]}}, "typeVersion": 2.2}, {"id": "6ed72f04-7b15-4fb4-8699-0691beac69c0", "name": "jira-webhook", "type": "n8n-nodes-base.webhook", "position": [-740, 0], "webhookId": "1e4989bf-6a23-4415-bd17-72d08130c5c4", "parameters": {"path": "1e4989bf-6a23-4415-bd17-72d08130c5c4", "options": {}, "httpMethod": "POST", "authentication": "headerAuth"}, "credentials": {"httpHeaderAuth": {"id": "9EPLvRDcYuohsyim", "name": "Header Auth account"}}, "typeVersion": 2}], "pinData": {}, "connections": {"check type": {"main": [[{"node": "Send Create", "type": "main", "index": 0}], [{"node": "Send Update", "type": "main", "index": 0}], [{"node": "Send Assign <PERSON>", "type": "main", "index": 0}]]}, "jira-webhook": {"main": [[{"node": "check issue body, assignee and hook type", "type": "main", "index": 0}]]}, "telegram account": {"main": [[{"node": "check tg account exists", "type": "main", "index": 0}]]}, "check tg account exists": {"main": [[{"node": "check type", "type": "main", "index": 0}]]}, "check issue body, assignee and hook type": {"main": [[{"node": "telegram account", "type": "main", "index": 0}]]}}}