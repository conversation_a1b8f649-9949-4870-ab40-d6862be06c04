{"meta": {"instanceId": "1a23006df50de49624f69e85993be557d137b6efe723a867a7d68a84e0b32704"}, "nodes": [{"id": "54065cc9-047c-4741-95f6-cec3e352abd7", "name": "Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [2700, -1840], "parameters": {"fileId": {"__rl": true, "mode": "url", "value": "https://drive.google.com/file/d/xxxxxxxxxxxxxxx/view"}, "options": {}, "operation": "download"}, "typeVersion": 3}, {"id": "62af57f5-a001-4174-bece-260a1fc595e8", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [3120, -1620], "parameters": {"loader": "epubLoader", "options": {}, "dataType": "binary"}, "typeVersion": 1}, {"id": "ce3d9c7c-6ce9-421a-b4d0-4235217cf8e6", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [2620, -2000], "parameters": {"width": 749.1276349295781, "height": 820.5109034066329, "content": "# INSERTING\n\n- it's important to use the same embedding model when for any interaction with your vector database (inserting, upserting and retrieval)"}, "typeVersion": 1}, {"id": "81cb3d3e-70af-46c8-bc18-3d076a222d0b", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1720, -1160], "parameters": {"color": 3, "width": 873.9739981925188, "height": 534.0012007720542, "content": "# UPSERTING\n"}, "typeVersion": 1}, {"id": "60ebdb71-c7e0-429b-9394-b680cc000951", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1720, -2000], "parameters": {"color": 4, "width": 876.5116990000852, "height": 821.787041589866, "content": "# PREPARATION (in Supabase)\n\n- your database needs the extension 'pgvector' enabled -> select Database > Extension > Search for 'vector'\n- make sure you have a table that has the following columns (if not, use the query below in the Supabase SQL Editor)\n\n```\nALTER TABLE \"YOUR TABLE NAME\"\nADD COLUMN embedding VECTOR(1536), // check which number of dimensions you need (depends on the embed model)\nADD COLUMN metadata JSONB,\nADD COLUMN content TEXT;\n```\n\n- make sure you have the right policies set -> select Authentication > Policies\n- make sure you have the custom function `match_documents` set up in Supabase -> This is needed for the Vector Store Node (as query name) \n(if not, use the query below in the Supabase SQL Editor to create that function)\n- make sure you check the size of the AI model as it should be the same vector size for the table \n(e.g. OpenAI's Text-Embedding-3-Small uses 1536)\n\n```\nCREATE OR REPLACE FUNCTION public.match_documents(\n  filter JSONB,\n  match_count INT,\n  query_embedding VECTOR(1536) // should match same dimensions as from insertion\n)\nRETURNS TABLE (\n  id BIGINT,\n  content TEXT,\n  metadata JSONB,\n  embedding VECTOR(1536), // should match same dimensions as from insertion\n  similarity FLOAT\n)\nLANGUAGE plpgsql AS $$\nBEGIN\n  RETURN QUERY\n  SELECT\n    v.id,\n    v.content,\n    v.metadata,\n    v.embedding,\n    1 - (v.embedding <=> match_documents.query_embedding) AS similarity\n  FROM \"YOUR TABLE NAME\" v\n  WHERE v.metadata @> filter\n  ORDER BY v.embedding <=> match_documents.query_embedding\n  LIMIT match_count;\nEND;\n$$\n;\n```\n"}, "typeVersion": 1}, {"id": "ae95b0c3-b8b3-44eb-8070-b1bc6cac5cd2", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [3400, -2000], "parameters": {"color": 5, "width": 810.9488123113013, "height": 821.9537074055816, "content": "# RETRIEVAL"}, "typeVersion": 1}, {"id": "58168721-cbd7-498c-9d16-41b4d5c6a68f", "name": "Question and Answer Chain", "type": "@n8n/n8n-nodes-langchain.chainRetrievalQa", "position": [3680, -1860], "parameters": {}, "typeVersion": 1.3}, {"id": "ddf1228f-f051-445b-8a42-54c2510a0b2e", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [3600, -1680], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "734a2c48-b445-4e62-99b7-dc1dcd921c52", "name": "Vector Store Retriever", "type": "@n8n/n8n-nodes-langchain.retrieverVectorStore", "position": [3760, -1680], "parameters": {"topK": 10}, "typeVersion": 1}, {"id": "43f761b7-f4da-4b29-8099-9b2c15f79fe9", "name": "Recursive Character Text Splitter1", "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "position": [3120, -1460], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "de0d2666-88e4-4a4d-ba46-cf789b9cba85", "name": "Customize Response", "type": "n8n-nodes-base.set", "notes": "output || text", "position": [4020, -1860], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "440fc115-ccae-4e30-85a5-501d0617b2cf", "name": "output", "type": "string", "value": "={{ $json.response.text }}"}]}}, "notesInFlow": true, "typeVersion": 3.4}, {"id": "a396671f-a217-4f05-b969-cb64f10e4b01", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [3480, -1860], "webhookId": "d7431c58-89aa-4d70-b5bd-044be981b3a9", "parameters": {"public": true, "options": {"responseMode": "lastNode"}, "initialMessages": "=Hi there! 🙏\n\nYou can ask me anything about Venerable <PERSON><PERSON><PERSON>'s Book - 'How To Transform Your Life'\n\nWhat would you like to know? "}, "typeVersion": 1.1}, {"id": "6312f6bc-c69c-4d4f-8838-8a9d0d22ed55", "name": "Retrieve by Query", "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "position": [3700, -1520], "parameters": {"options": {"queryName": "match_documents"}, "tableName": {"__rl": true, "mode": "list", "value": "Ka<PERSON><PERSON>", "cachedResultName": "Ka<PERSON><PERSON>"}}, "typeVersion": 1}, {"id": "ba6b87b9-e96d-47a3-83f8-169d7172325a", "name": "Embeddings OpenAI Retrieval", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [3700, -1360], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "bcd1b31f-c60b-4c40-b039-d47dadc86b23", "name": "Embeddings OpenAI Insertion", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [2920, -1620], "parameters": {"model": "text-embedding-3-small", "options": {}}, "typeVersion": 1}, {"id": "dfd7f734-eb00-4af3-9179-724503422fe4", "name": "Placeholder (File/Content to Upsert)", "type": "n8n-nodes-base.set", "position": [1900, -1000], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "={\n  \"Date\": \"{{ $now.format('dd MMM yyyy') }}\",\n  \"Time\": \"{{ $now.format('HH:mm ZZZZ z') }}\"\n}\n"}, "typeVersion": 3.4}, {"id": "c54c9458-9b8a-4ef1-a6db-5265729be19d", "name": "Embeddings OpenAI Upserting", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [2120, -840], "parameters": {"model": "text-embedding-3-small", "options": {}}, "typeVersion": 1}, {"id": "30c18e9e-d047-40d3-8324-f5d0e7892db6", "name": "Insert Documents", "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "position": [2920, -1840], "parameters": {"mode": "insert", "options": {}, "tableName": {"__rl": true, "mode": "list", "value": "Ka<PERSON><PERSON>", "cachedResultName": "Ka<PERSON><PERSON>"}}, "typeVersion": 1}, {"id": "3c0ed0ee-9134-4b4e-bcfd-632dd67a57da", "name": "Retrieve Rows from Table", "type": "n8n-nodes-base.supabase", "position": [3960, -1380], "parameters": {"tableId": "n8n", "operation": "getAll", "returnAll": true}, "typeVersion": 1}, {"id": "53aca1b4-31e8-4699-b158-673623bc9b95", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [2620, -1160], "parameters": {"color": 6, "width": 1587.0771183771394, "height": 537.3056597675153, "content": "# DELETION\n\nAt the moment n8n does not have a built-in Supabase Node to delete records in a Vector Database. For this you would typically use the HTTP Request node to make an authorized API call to Supabase. \n\n## HTTP Request Node\n\nUse this node to send a DELETE request to your Supabase instance.\n\n- Supabase API Endpoint: Use the appropriate URL for your Supabase project. The endpoint will typically look like this: [https://<your-supabase-ref>.supabase.co/rest/v1/<your-vector-table>](https://supabase.com/docs/guides/api). Replace `<your-supabase-ref>` and `<your-vector-table>` with your details.\n### HEADERS:\n- apikey: Your Supabase API key.\n- Authorization: Bearer token with your Supabase JWT.\n- Query Parameters: Use query parameters to specify which record(s) to delete. For example, `?id=eq.<your-record-id>` where `<your-record-id>` is the specific record ID you want to delete \n(You can also reference back to the **Retrieve Rows From Table** Node to get the ID dynamically)\n\nEnsure you have the necessary permissions set up in Supabase to delete records through the API.\n\nPlease refer to the official n8n documentation for more detailed information on using the [HTTP Request Node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/).\n\n_Note:_ Deleting records is a sensitive operation, so make sure that your permissions are correctly configured and that you are targeting the correct records to avoid unwanted data loss."}, "typeVersion": 1}, {"id": "4ffaccdb-9e0f-464d-9284-7771f6599fd8", "name": "Update Documents", "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "position": [2100, -1000], "parameters": {"id": "1", "mode": "update", "options": {"queryName": "match_documents"}, "tableName": {"__rl": true, "mode": "list", "value": "n8n", "cachedResultName": "n8n"}}, "typeVersion": 1}], "pinData": {}, "connections": {"Google Drive": {"main": [[{"node": "Insert Documents", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Question and Answer Chain", "type": "ai_languageModel", "index": 0}]]}, "Retrieve by Query": {"ai_vectorStore": [[{"node": "Vector Store Retriever", "type": "ai_vectorStore", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Insert Documents", "type": "ai_document", "index": 0}]]}, "Vector Store Retriever": {"ai_retriever": [[{"node": "Question and Answer Chain", "type": "ai_retriever", "index": 0}]]}, "Question and Answer Chain": {"main": [[{"node": "Customize Response", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "Question and Answer Chain", "type": "main", "index": 0}]]}, "Embeddings OpenAI Insertion": {"ai_embedding": [[{"node": "Insert Documents", "type": "ai_embedding", "index": 0}]]}, "Embeddings OpenAI Retrieval": {"ai_embedding": [[{"node": "Retrieve by Query", "type": "ai_embedding", "index": 0}]]}, "Embeddings OpenAI Upserting": {"ai_embedding": [[{"node": "Update Documents", "type": "ai_embedding", "index": 0}]]}, "Recursive Character Text Splitter1": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Placeholder (File/Content to Upsert)": {"main": [[{"node": "Update Documents", "type": "main", "index": 0}]]}}}