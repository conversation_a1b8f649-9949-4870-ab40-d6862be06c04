{"id": "9r4T5kELOXAV8L1F", "meta": {"instanceId": "1e003a7ea4715b6b35e9947791386a7d07edf3b5bf8d4c9b7ee4fdcbec0447d7"}, "name": "3D Figurine Orthographic Views with Midjourney and GPT-4o-Image API", "tags": [], "nodes": [{"id": "30ff7c89-7fb6-4daf-b7f2-d178ee702243", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [720, 220], "parameters": {}, "typeVersion": 1}, {"id": "26cb9d6e-6a73-4a27-805d-8577c84101fa", "name": "Midjourney Generator", "type": "n8n-nodes-base.httpRequest", "position": [940, 220], "parameters": {"url": "https://api.piapi.ai/api/v1/task", "method": "POST", "options": {}, "jsonBody": "{\n  \"model\": \"midjourney\",\n  \"task_type\": \"imagine\",\n  \"input\": {\n    \"prompt\": \"IP design, pop mart style, cartoon-style characters, a little girl with a red satche on her back, a pair of big eyes, long eyelashes, with pigtails, wearing a red beret, red shoes, chubby body, wearing a red and white striped dress, clean white background, crystal-clear material::5, 3D rendering, 3D modeling --ar 3:4 --niji 6\",\n    \"aspect_ratio\": \"3:2\",\n    \"process_mode\": \"turbo\",\n    \"skip_prompt_check\": false\n  }\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "x-api-key"}]}}, "typeVersion": 4.2}, {"id": "4220555c-f4f4-43ab-8d03-1ae4b959bdd1", "name": "Get Midjourney URL", "type": "n8n-nodes-base.httpRequest", "position": [760, 460], "parameters": {"url": "=https://api.piapi.ai/api/v1/task/{{ $json.data.task_id }}", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-api-key", "value": "72858adea87ad16865d5b0a24c3d9b9f58a6e7b1a8a8a8a0d6b81a9f3a9812f3"}]}}, "typeVersion": 4.2}, {"id": "e81311ee-4522-4fb3-929f-1c062427c859", "name": "Verify URL Acquisition", "type": "n8n-nodes-base.if", "position": [960, 460], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a0f8758e-d6fd-44f8-bd79-bc3c4dceddcf", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "completed"}]}}, "typeVersion": 2.2}, {"id": "9519899e-c246-474e-8fd6-7dd16cf27a5b", "name": "Wait for Generation", "type": "n8n-nodes-base.wait", "position": [980, 700], "webhookId": "f3bcf634-8c4b-4bf9-a7f2-d4ee369f5349", "parameters": {}, "typeVersion": 1.1}, {"id": "693348ab-12ab-4896-9fda-a9141263ccbf", "name": "Get Random Image URL", "type": "n8n-nodes-base.code", "position": [1240, 520], "parameters": {"jsCode": "// JavaScript Code for Function Node\nreturn {\n  random_temp_url: $input.all()[0].json.data.output.temporary_image_urls[\n    Math.floor(Math.random() * $input.all()[0].json.data.output.temporary_image_urls.length)\n  ]\n};"}, "typeVersion": 2}, {"id": "18f030a6-7333-45df-af1d-f8c5492084c6", "name": "Generation 3-view Image with GPT-4o-Image", "type": "n8n-nodes-base.httpRequest", "position": [1440, 520], "parameters": {"url": "https://api.piapi.ai/v1/chat/completions", "method": "POST", "options": {}, "jsonBody": "={\n    \"model\": \"gpt-4o-image-preview\",\n    \"messages\": [\n        {\n            \"role\": \"user\",\n            \"content\": [\n                {\n                    \"type\": \"image_url\",\n                    \"image_url\": {\n                        \"url\": \"{{ $json.random_temp_url }}\"\n                    }\n                },\n                {\n                    \"type\": \"text\",\n                    \"text\": \"Convert this image into a 3D figurine image, with front view, side view, and back view in one page. Generate a turnaround sheet showing the figurine’s front with full details, profile, and back views in left-to-right sequence. ar=10:3\"\n                }\n            ]\n        }\n    ],\n    \"stream\": true\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer "}]}}, "credentials": {"httpHeaderAuth": {"id": "fsJeCNd9BkJ1CIrt", "name": "Head<PERSON> Au<PERSON> account 2"}}, "typeVersion": 4.2}, {"id": "4d758715-79d5-4c2e-94d2-d1cfe1bdda6d", "name": "Check if the URL is obtained", "type": "n8n-nodes-base.if", "position": [1840, 520], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "08a2ebe6-dc95-4b8a-ada1-1173645cc3f4", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.finish_reason }}", "rightValue": "not_found"}, {"id": "ed245d42-677f-4465-a3f1-d23c6e609f5e", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "690c002c-8641-4bb9-8c55-8b5cd6f01f2c", "name": "Get Final Output", "type": "n8n-nodes-base.code", "position": [2080, 540], "parameters": {"jsCode": "// Method 2: n8n workflow compatible version\nreturn $input.all().map(item => {\n  return {\n    image_url: item.json.image_url\n  };\n});"}, "typeVersion": 2}, {"id": "db38b578-1078-4a89-b429-55ba337a21fc", "name": "Get Image", "type": "n8n-nodes-base.code", "position": [1660, 520], "parameters": {"jsCode": "const chunks = $input.first().json.data.split('\\n\\n');\n\nlet imageUrl = null;\n\n// 反向遍历 chunks (从最新数据开始检查)\nfor (let i = chunks.length - 1; i >= 0; i--) {\n    const chunk = chunks[i];\n    \n    if (!chunk.startsWith('data: ')) continue;\n    \n    try {\n        const jsonStr = chunk.substring(6); // 去掉 \"data: \" 前缀\n        if (jsonStr.trim() === '[DONE]') continue;\n        \n        const data = JSON.parse(jsonStr);\n        \n        // 检查是否包含图片标记（Markdown 图片语法）\n        if (data.choices && data.choices[0].delta.content) {\n            const content = data.choices[0].delta.content;\n            const urlMatch = content.match(/!\\[.*?\\]\\((https?:\\/\\/[^\\s]+)\\)/);\n            \n            if (urlMatch && urlMatch[1]) {\n                imageUrl = urlMatch[1];\n                break;\n            }\n        }\n    } catch (e) {\n        continue;\n    }\n}\n\nreturn {\n    image_url: imageUrl,\n    finish_reason: imageUrl ? \"success\" : \"not_found\"\n};"}, "typeVersion": 2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "0e663ca1-c9b2-4fb2-a8bb-14ed8bb63d11", "connections": {"Get Image": {"main": [[{"node": "Check if the URL is obtained", "type": "main", "index": 0}]]}, "Get Midjourney URL": {"main": [[{"node": "Verify URL Acquisition", "type": "main", "index": 0}]]}, "Wait for Generation": {"main": [[{"node": "Get Midjourney URL", "type": "main", "index": 0}]]}, "Get Random Image URL": {"main": [[{"node": "Generation 3-view Image with GPT-4o-Image", "type": "main", "index": 0}]]}, "Midjourney Generator": {"main": [[{"node": "Get Midjourney URL", "type": "main", "index": 0}]]}, "Verify URL Acquisition": {"main": [[{"node": "Get Random Image URL", "type": "main", "index": 0}], [{"node": "Wait for Generation", "type": "main", "index": 0}]]}, "Check if the URL is obtained": {"main": [[{"node": "Generation 3-view Image with GPT-4o-Image", "type": "main", "index": 0}], [{"node": "Get Final Output", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Midjourney Generator", "type": "main", "index": 0}]]}, "Generation 3-view Image with GPT-4o-Image": {"main": [[{"node": "Get Image", "type": "main", "index": 0}]]}}}