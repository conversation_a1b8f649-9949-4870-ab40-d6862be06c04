{"id": "5ec2322573f7590007802e1f", "name": "Extranet Releases", "nodes": [{"name": "<PERSON><PERSON>ck", "type": "n8n-nodes-base.slack", "position": [560, 550], "parameters": {"text": "=New release is available in {{$node[\"Github Trigger\"].json[\"body\"][\"repository\"][\"full_name\"]}} !\n{{$node[\"Github Trigger\"].json[\"body\"][\"release\"][\"tag_name\"]}} Details:\n{{$node[\"Github Trigger\"].json[\"body\"][\"release\"][\"body\"]}}\n\nLink: {{$node[\"Github Trigger\"].json[\"body\"][\"release\"][\"html_url\"]}}", "as_user": true, "channel": "extranet-md", "attachments": [], "otherOptions": {}}, "credentials": {"slackApi": "Extranet-md"}, "typeVersion": 1}, {"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.githubTrigger", "position": [350, 550], "parameters": {"owner": "Mesdocteurs", "events": ["release"], "repository": "mda-admin-partner-api"}, "credentials": {"githubApi": "Github API"}, "typeVersion": 1}], "active": true, "settings": {}, "connections": {"Github Trigger": {"main": [[{"node": "<PERSON><PERSON>ck", "type": "main", "index": 0}]]}}}