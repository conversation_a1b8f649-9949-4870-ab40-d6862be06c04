{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "b26e5f35-214a-4eba-83f6-a61736a2f017", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [240, 560], "parameters": {"color": 7, "width": 398, "height": 217, "content": "Call the assistant, passing in the previous chat messages"}, "typeVersion": 1}, {"id": "7cba00f3-7824-47eb-a17f-6e34fab51c0d", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-440, 460], "parameters": {"height": 300.48941882630095, "content": "## Try me out\n1. In the OpenAI Assistant node, make sure your OpenAI credentials are set and choose an assistant to use (you'll need to create one if you don't have one already)\n2. Click the 'Chat' button below\n\n  - In the first message, tell the AI what your name is\n  - In a second message, ask the AI what your name is"}, "typeVersion": 1}, {"id": "a71b8aef-5ee9-4ff2-9a77-5154fee67cc8", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [180, 920], "parameters": {"sessionKey": "={{ $('When chat message received').first().json.sessionId }}", "sessionIdType": "customKey", "contextWindowLength": 20}, "typeVersion": 1.3}, {"id": "24faa70e-52e7-40e4-abc1-05c8b18df583", "name": "OpenAI", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [300, 640], "parameters": {"text": "={{ $('When chat message received').item.json.chatInput }}", "prompt": "define", "options": {}, "resource": "assistant", "assistantId": {"__rl": true, "mode": "id", "value": "asst_HDSAnzsp4WqY4UC1iI9auH5z"}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "37b70475-f28b-4e5f-a7e2-3dad715b2e8d", "name": "Calculator1", "type": "@n8n/n8n-nodes-langchain.toolCalculator", "position": [600, 920], "parameters": {}, "typeVersion": 1}, {"id": "79d644c4-6d24-4f1e-9c43-08fa8b20da0e", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-100, 640], "webhookId": "9eab0524-6cd7-4b81-8bd8-4d050a972a08", "parameters": {"public": true, "options": {"loadPreviousSession": "memory"}}, "typeVersion": 1.1}], "pinData": {}, "connections": {"Calculator1": {"ai_tool": [[{"node": "OpenAI", "type": "ai_tool", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "OpenAI", "type": "ai_memory", "index": 0}, {"node": "When chat message received", "type": "ai_memory", "index": 0}]]}, "When chat message received": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}}}