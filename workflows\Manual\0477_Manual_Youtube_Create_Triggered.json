{"nodes": [{"id": "fdb6c202-ea97-4a87-b141-7aae4bae9917", "name": "Config", "type": "n8n-nodes-base.set", "position": [520, 340], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "eed16103-d07f-4e81-93ac-567b096f54be", "name": "splitter", "type": "string", "value": "--- n8ninja ---"}, {"id": "62e585b6-f908-4a9b-8abb-a2bd22ce4423", "name": "description", "type": "string", "value": "n8n is the most powerful automation tool available today. It is simple yet powerful.\nn8n automation is a node-based automation tool that offers countless possibilities.\nWith more than 400 integrations, the use cases of n8n are endless.\n\nIn my long journey as a digital ninja, this is by far my weapon of choice when it comes to saving time and cutting BS tasks!\n\n⭐️ Try n8n for free: https://n8n.partnerlinks.io/try-for-free\n🆇 Following me on X: https://twitter.com/n8nja\n🥷 My Website: https://www.n8n.ninja/\n📋 My Templates https://n8n.io/creators/emmanuel/"}]}}, "typeVersion": 3.3}, {"id": "fdd88c25-911f-413a-bb16-4b84315c2d6b", "name": "Generate Description", "type": "n8n-nodes-base.set", "position": [960, 340], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "a20ac17b-6aaa-45b2-995f-2751a7aaa238", "name": "description", "type": "string", "value": "={{ $json.snippet.description.split($('Config').item.json.splitter)[0] }}{{ $('Config').item.json.splitter }}\n\n{{ $('Config').item.json[\"description\"] }}"}]}, "includeOtherFields": ""}, "typeVersion": 3.3}, {"id": "ac1b3a81-12a4-4be9-abbe-cce155218fb6", "name": "Check if has changed", "type": "n8n-nodes-base.if", "position": [1180, 340], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "f4329949-b775-45ca-aacb-1fc0f2df8ef1", "operator": {"type": "string", "operation": "notEquals"}, "leftValue": "={{ $json.description }}", "rightValue": "={{ $('List all videos').item.json.snippet.description }}"}]}}, "typeVersion": 2}, {"id": "3daaae7a-2a7b-4894-aa2d-f38ed7b91b9b", "name": "Update Description", "type": "n8n-nodes-base.youTube", "position": [1420, 320], "parameters": {"title": "={{ $('List all videos').item.json.snippet.title }}", "videoId": "={{ $('List all videos').item.json.id.videoId }}", "resource": "video", "operation": "update", "categoryId": "27", "regionCode": "US", "updateFields": {"description": "={{ $json.description }}"}}, "credentials": {"youTubeOAuth2Api": {"id": "WZul9rD4MH9aVAY8", "name": "YouTube account"}}, "typeVersion": 1}, {"id": "dc83d27d-cfec-4989-a009-ecc42194b133", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [520, -20], "parameters": {"color": 6, "width": 275.**************, "height": 313.*************, "content": "# Setup\n### 1/ Add Your credentials\n[Youtube](https://docs.n8n.io/integrations/builtin/credentials/google/)\n\n### 2/ Define in the config node the delimiter and the text you want to add to all your videos. \n\n# 👇"}, "typeVersion": 1}, {"id": "b984c720-852b-46d2-bbb1-fa22bcefce78", "name": "Trigger Workflow", "type": "n8n-nodes-base.manualTrigger", "position": [300, 340], "parameters": {}, "typeVersion": 1}, {"id": "a3002568-57c8-451d-b8fd-70b4b1323f78", "name": "List all videos", "type": "n8n-nodes-base.youTube", "position": [740, 340], "parameters": {"filters": {}, "options": {}, "resource": "video"}, "credentials": {"youTubeOAuth2Api": {"id": "WZul9rD4MH9aVAY8", "name": "YouTube account"}}, "typeVersion": 1}, {"id": "3b26af11-a5c6-4ba6-9e0c-31396f82f55f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [860, 200], "parameters": {"color": 7, "width": 202.**************, "height": 85.**************, "content": "### Crafted by the\n## [🥷 n8n.ninja](n8n.ninja)"}, "typeVersion": 1}, {"id": "bf6f8b3d-7182-4417-ab71-785e4215d2e9", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-120, 300], "parameters": {"color": 6, "width": 372, "height": 120.**************, "content": "## Run this workflow every time you want to update all your Youtube video descriptions 👉🏻\n"}, "typeVersion": 1}], "pinData": {}, "connections": {"Config": {"main": [[{"node": "List all videos", "type": "main", "index": 0}]]}, "List all videos": {"main": [[{"node": "Generate Description", "type": "main", "index": 0}]]}, "Trigger Workflow": {"main": [[{"node": "Config", "type": "main", "index": 0}]]}, "Check if has changed": {"main": [[{"node": "Update Description", "type": "main", "index": 0}]]}, "Generate Description": {"main": [[{"node": "Check if has changed", "type": "main", "index": 0}]]}}}