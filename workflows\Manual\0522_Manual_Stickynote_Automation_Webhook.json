{"meta": {"instanceId": "1dd912a1610cd0376bae7bb8f1b5838d2b601f42ac66a48e012166bb954fed5a", "templateId": "2317"}, "nodes": [{"id": "30aca7bf-cf50-4182-97b5-0e8f006e1429", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [380, 240], "parameters": {}, "typeVersion": 1}, {"id": "f43cc22f-00c0-4881-b610-ade09a3a2340", "name": "Write Result File to Disk", "type": "n8n-nodes-base.readWriteFile", "position": [1200, 240], "parameters": {"options": {}, "fileName": "document.pdf", "operation": "write", "dataPropertyName": "=data"}, "typeVersion": 1}, {"id": "1fdac712-f93c-4001-9510-d533a81304e3", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [720, 100], "parameters": {"width": 218, "height": 132, "content": "## Authentication\nConversion requests must be authenticated. Please create \n[ConvertAPI account to get authentication secret](https://www.convertapi.com/a/signin)"}, "typeVersion": 1}, {"id": "b79ad903-15c2-48b8-8108-e9e3ec8e6134", "name": "Download PDF File", "type": "n8n-nodes-base.httpRequest", "position": [580, 240], "parameters": {"url": "https://cdn.convertapi.com/public/files/demo.pdf", "options": {"response": {"response": {"responseFormat": "file"}}}}, "typeVersion": 4.2}, {"id": "b374eeb9-0246-431e-ab1e-2ca48692c899", "name": "File conversion to PDFA", "type": "n8n-nodes-base.httpRequest", "position": [780, 240], "parameters": {"url": "https://v2.convertapi.com/convert/pdf/to/pdfa", "method": "POST", "options": {"response": {"response": {"responseFormat": "file"}}}, "sendBody": true, "contentType": "multipart-form-data", "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "file", "parameterType": "formBinaryData", "inputDataFieldName": "=data"}, {"name": "PdfaVersion", "value": "pdfa"}]}, "genericAuthType": "httpQueryAuth", "headerParameters": {"parameters": [{"name": "Accept", "value": "application/octet-stream"}]}}, "credentials": {"httpQueryAuth": {"id": "WdAklDMod8fBEMRk", "name": "Query Auth account"}}, "notesInFlow": true, "typeVersion": 4.2}], "pinData": {}, "connections": {"Download PDF File": {"main": [[{"node": "File conversion to PDFA", "type": "main", "index": 0}]]}, "File conversion to PDFA": {"main": [[{"node": "Write Result File to Disk", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Download PDF File", "type": "main", "index": 0}]]}}}