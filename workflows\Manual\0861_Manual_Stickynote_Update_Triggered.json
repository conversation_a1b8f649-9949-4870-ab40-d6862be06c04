{"meta": {"instanceId": "c6511943b220d4ab672ac957465b13db475def5fbbd0b0e41240952f5fd0c300"}, "nodes": [{"id": "e0721f8a-d157-4ec4-91b3-94060a841dc8", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.quick<PERSON><PERSON>", "position": [240, -40], "parameters": {"data": "={{ $json.jsonData.salesData }}", "chartType": "line", "labelsMode": "array", "labelsArray": "={{ $json.jsonData.labels }}", "chartOptions": {}, "datasetOptions": {}}, "typeVersion": 1}, {"id": "b178ca51-357f-4731-8953-75e2370edc2d", "name": "Edit Fields: Set JSON data to test", "type": "n8n-nodes-base.set", "position": [-80, -40], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "1b3ae0ac-7fa5-406c-8e61-d6a9a6c27f07", "name": "jsonData", "type": "object", "value": "={ \"reportTitle\": \"Quarterly Sales\", \"labels\": [\"Q1\", \"Q2\", \"Q3\", \"Q4\"], \"salesData\": [1250, 1800, 1550, 2100] }"}]}}, "typeVersion": 3.4}, {"id": "393665db-f6a6-4294-afd8-3a9f32192c64", "name": "Google Drive: Upload File", "type": "n8n-nodes-base.googleDrive", "position": [520, -40], "parameters": {"name": "=chart.{{ $binary.data.fileExtension }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "root", "cachedResultName": "/ (Root folder)"}}, "credentials": {"googleDriveOAuth2Api": {"id": "Vt3z79hk8lh9TUQq", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "c4f2df73-50dc-4b9f-bcb8-43644c0cbed9", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-600, -740], "parameters": {"width": 1460, "height": 1060, "content": "## Chart Generator\n**Generate Dynamic Line Chart from JSON Data to Upload to Google Drive\n### How to Use & Customize\n\n* **Change Input Data:** Modify the `labels` and `salesData` arrays within the `Edit Fields: Set JSON data to test` node to use your own data. Ensure the number of labels matches the number of data points.\n* **Use Real Data Sources:** Replace the `Edit Fields: Set JSON data to test` node with nodes that fetch data from real sources like:\n    * HTTP Request (APIs)\n    * Postgres / MongoDB nodes (Databases)\n    * Google Sheets node\n    * Ensure the output data from your source node is formatted similarly (providing `labels` and `salesData` arrays). You might need another Set node to structure the data correctly before the QuickChart node.\n* **Change Chart Type:** In the QuickChart node, modify the `Chart Type` parameter (e.g., change from `line` to `bar`, `pie`, `doughnut`, etc.).\n* **Customize Chart Appearance:** Explore the `Chart Options` parameter within the QuickChart node to add titles, change colors, modify axes, etc., using QuickChart's standard JSON configuration options.\n* **Use Datasets (Recommended for Complex Charts):** For multiple lines/bars or more control, configure datasets explicitly in the QuickChart node:\n    * Remove the expression from the top-level `Data` field.\n    * Go to `Dataset Options` -&gt; `Add option` -&gt; `Add dataset`.\n    * Set the `Data` field within the dataset using an expression like `{{ $json.jsonData.salesData }}`.\n    * You can add multiple datasets this way.\n* **Change Output Destination:** Replace the `Google Drive: Upload File` node with other nodes to handle the chart image differently:\n    * `Write Binary File`: Save the chart to the local filesystem where n8n is running.\n    * `Slack` / `Discord` / `Telegram`: Send the chart to messaging platforms.\n    * `Move Binary Data`: Convert the image to Base64 to embed in HTML or return via webhook response."}, "typeVersion": 1}, {"id": "1af3cfc6-f690-4af2-a812-4a4da118a55c", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-400, -40], "parameters": {}, "typeVersion": 1}], "pinData": {}, "connections": {"QuickChart": {"main": [[{"node": "Google Drive: Upload File", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Edit Fields: Set JSON data to test", "type": "main", "index": 0}]]}, "Edit Fields: Set JSON data to test": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}}}