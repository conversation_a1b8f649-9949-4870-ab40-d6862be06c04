{"id": "aDPpPIaeM7zfUCdJ", "meta": {"instanceId": "e5595d8cd58f3a24b5a8cf05dd852846c05423873db868a2b7d01a778210c45a", "templateCredsSetupCompleted": true}, "name": "GROQ LLAVA V1.5 7B", "tags": [], "nodes": [{"id": "d831f75e-0385-482c-b2d5-e8da58216f4c", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "position": [540, 280], "webhookId": "6021108f-f0e8-4d7a-918b-f49baa28ba85", "parameters": {"updates": ["*"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "JLNFgAyYUUyvLhcv", "name": "Bot 2"}}, "typeVersion": 1.1}, {"id": "0fd97c36-3e3d-45a3-929d-975d17baf1fb", "name": "Telegram send the text", "type": "n8n-nodes-base.telegram", "position": [1640, 280], "parameters": {"text": "={{ $json.choices[0].message.content }}", "chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "", "name": ""}}, "typeVersion": 1.2}, {"id": "bd39b29f-e128-4891-bc6a-3eb75de29182", "name": "Get only the text", "type": "n8n-nodes-base.set", "position": [1420, 280], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "52a2f0d9-3137-4f6e-a2c1-8285694f6159", "name": "choices[0].message.content", "type": "string", "value": "={{ $json.choices[0].message.content }}"}]}}, "typeVersion": 3.4}, {"id": "f1a96061-6d81-4d21-adac-dab475a00eb1", "name": "HTTP Request GROQ LLAVA", "type": "n8n-nodes-base.httpRequest", "position": [1200, 280], "parameters": {"url": "https://api.groq.com/openai/v1/chat/completions", "method": "POST", "options": {}, "jsonBody": "={\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": [\n        {\n          \"type\": \"text\",\n          \"text\": \"Describe this image in great detail\"\n        },\n        {\n          \"type\": \"image_url\",\n          \"image_url\": {\n            \"url\": \"data:image/jpeg;base64,{{ $json.data }}\"\n          }\n        }\n      ]\n    }\n  ],\n  \"model\": \"llava-v1.5-7b-4096-preview\"\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer YOUR_API_TOKEN"}, {"name": "Content-Type", "value": "application/json"}]}}, "typeVersion": 4.2}, {"id": "ab6be84f-06df-4f6f-b7fc-e328bc854116", "name": "convert the image file to base64", "type": "n8n-nodes-base.extractFromFile", "position": [980, 280], "parameters": {"options": {}, "operation": "binaryToPropery"}, "typeVersion": 1}, {"id": "888397d6-4fd1-4e9b-852e-1731159df4f5", "name": "Receive the File", "type": "n8n-nodes-base.telegram", "position": [760, 280], "parameters": {"fileId": "={{ $json.message.photo[0].file_id }}", "resource": "file"}, "credentials": {"telegramApi": {"id": "", "name": ""}}, "typeVersion": 1.2}, {"id": "7d117dd2-bd9f-4930-a727-8bff38cb5b72", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [440, -16.000000000000057], "parameters": {"color": 4, "width": 691.428571428571, "height": 521.142857142858, "content": "## Set Up\n\nOpen the Telegram app and search for the BotFather user (@BotFather)\nStart a chat with the BotFather\nType /newbot to create a new bot\nFollow the prompts to name your bot and get a unique API token\nSave your access token and username\n## Start Using\nOnce you set the Bot, you can send the image. \nThe second node get the image and send to the next node to be convert in base64, that is required by <PERSON>roq in the documentation.\n\n [Groq docs](https://console.groq.com/docs/vision)"}, "typeVersion": 1}, {"id": "a935a3a6-85cd-43c6-aa0a-a37f6c40372a", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1160, -20], "parameters": {"width": 650.2857142857147, "height": 524.571428571429, "content": "## Using GROQ API\n\nNow we send the image in base64 to the API and get the description of the image."}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "9253a6c2-d5d0-444a-9035-8fd562d54628", "connections": {"Receive the File": {"main": [[{"node": "convert the image file to base64", "type": "main", "index": 0}]]}, "Telegram Trigger": {"main": [[{"node": "Receive the File", "type": "main", "index": 0}]]}, "Get only the text": {"main": [[{"node": "Telegram send the text", "type": "main", "index": 0}]]}, "HTTP Request GROQ LLAVA": {"main": [[{"node": "Get only the text", "type": "main", "index": 0}]]}, "convert the image file to base64": {"main": [[{"node": "HTTP Request GROQ LLAVA", "type": "main", "index": 0}]]}}}