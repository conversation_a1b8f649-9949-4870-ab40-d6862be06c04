{"id": "gP9EsxKN5agUGzDS", "meta": {"instanceId": "73d9d5380db181d01f4e26492c771d4cb5c4d6d109f18e2621cf49cac4c50763", "templateCredsSetupCompleted": true}, "name": "Automate Pinterest Analysis & AI-Powered Content Suggestions With Pinterest API", "tags": [], "nodes": [{"id": "7f582bb4-97cd-458e-a7b7-b518c5b8a4ca", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [540, -260], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "95QGJD3XSz0piaNU", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "c6772882-468c-4391-a259-93e52daf49d4", "name": "Airtable2", "type": "n8n-nodes-base.airtableTool", "position": [700, -260], "parameters": {"id": "=", "base": {"__rl": true, "mode": "list", "value": "appfsNi1QEhw6WvXK", "cachedResultUrl": "https://airtable.com/appfsNi1QEhw6WvXK", "cachedResultName": "Pinterest_Metrics"}, "table": {"__rl": true, "mode": "list", "value": "tbl9Dxdrwx5QZGFnp", "cachedResultUrl": "https://airtable.com/appfsNi1QEhw6WvXK/tbl9Dxdrwx5QZGFnp", "cachedResultName": "Pinterest_Organic_Data"}, "options": {}}, "credentials": {"airtableTokenApi": {"id": "0ApVmNsLu7aFzQD6", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "85ea8bec-14c8-4277-b2e3-eb145db0713a", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [920, -280], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "95QGJD3XSz0piaNU", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "b8f7d0d6-b58f-4a41-a15d-99f4d838bb8c", "name": "8:00am Morning Scheduled Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-660, -140], "parameters": {"rule": {"interval": [{"daysInterval": 7, "triggerAtHour": 8}]}}, "typeVersion": 1.2}, {"id": "593a320d-825e-42f9-8ab6-adafd5288fa5", "name": "Pull List of Pinterest Pins From Account", "type": "n8n-nodes-base.httpRequest", "position": [-340, -140], "parameters": {"url": "https://api.pinterest.com/v5/pins", "options": {"redirect": {"redirect": {}}}, "sendBody": true, "sendHeaders": true, "bodyParameters": {"parameters": [{}]}, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer "}]}}, "typeVersion": 4.2}, {"id": "1e6d00fe-2b32-4d46-a230-063254ebab74", "name": "Update Data Field To Include Organic", "type": "n8n-nodes-base.code", "position": [-20, -140], "parameters": {"jsCode": "// Initialize an array to hold the output formatted for Airtable\nconst outputItems = [];\n\nfor (const item of $input.all()) {\n if (item.json.items && Array.isArray(item.json.items)) {\n for (const subItem of item.json.items) {\n // Construct an object with only the required fields for Airtable\n outputItems.push({\n id: subItem.id || null,\n created_at: subItem.created_at || null,\n title: subItem.title || null,\n description: subItem.description || null,\n link: subItem.link || null,\n type: \"Organic\" // Assign the value \"Organic\" to the 'Type' field\n });\n }\n }\n}\n\n// Return the structured output\nreturn outputItems;\n"}, "typeVersion": 2}, {"id": "539de144-dc67-4b14-b58e-2896edb1c3e6", "name": "Create Record Within Pinterest Data Table", "type": "n8n-nodes-base.airtable", "position": [260, -140], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appfsNi1QEhw6WvXK", "cachedResultUrl": "https://airtable.com/appfsNi1QEhw6WvXK", "cachedResultName": "Pinterest_Metrics"}, "table": {"__rl": true, "mode": "list", "value": "tbl9Dxdrwx5QZGFnp", "cachedResultUrl": "https://airtable.com/appfsNi1QEhw6WvXK/tbl9Dxdrwx5QZGFnp", "cachedResultName": "Pinterest_Organic_Data"}, "columns": {"value": {"link": "={{ $json.link }}", "type": "={{ $json.type }}", "title": "={{ $json.title }}", "pin_id": "={{ $json.id }}", "created_at": "={{ $json.created_at }}", "description": "={{ $json.description }}"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "pin_id", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "pin_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "created_at", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "created_at", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "title", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "description", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "link", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "type", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "type", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "active7DayUsers", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "active7DayUsers", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "sessions", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "sessions", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "userEngagementDuration", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "userEngagementDuration", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["id"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "upsert"}, "credentials": {"airtableTokenApi": {"id": "0ApVmNsLu7aFzQD6", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "250f5121-437e-4bff-82af-95a156126127", "name": "Pinterest Analysis AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [540, -440], "parameters": {"text": "You are a data analysis expert. You will pull data from the table and provide any information in regards to trends in the data. \n\nYour output should be suggestions of new pins that we can post to reach the target audiences. \n\nAnalyze the data and just summary of the pin suggestions that the team should build. ", "options": {}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "181e9d89-c0f9-4de2-bdce-9359b967157c", "name": "Pinterest Data Analysis Summary LLM", "type": "@n8n/n8n-nodes-langchain.chainSummarization", "position": [900, -440], "parameters": {"options": {"summarizationMethodAndPrompts": {"values": {"prompt": "=Write a concise summary of the following:\n\n\n\"{{ $json.output }}\"\n\n\nCONCISE SUMMARY:"}}}}, "typeVersion": 2}, {"id": "432e7bd7-36b4-4903-8e93-c8bd6e140a04", "name": "Send Marketing Trends & Pinterest Analysis To Marketing Manager", "type": "n8n-nodes-base.gmail", "position": [1220, -440], "webhookId": "f149c1b5-c028-4dff-9d22-a72951f2ef91", "parameters": {"sendTo": "<EMAIL>", "message": "={{ $json.response.text }}", "options": {}, "subject": "Pinterest Trends & Suggestions"}, "credentials": {"gmailOAuth2": {"id": "pIXP1ZseBP4Z5CCp", "name": "Gmail account"}}, "executeOnce": true, "typeVersion": 2.1}, {"id": "dadfb22a-b1d3-459d-a332-5a2c52fd4ca0", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-740, -320], "parameters": {"color": 5, "width": 280, "height": 440, "content": "Scheduled trigger at 8:00am to start the workflow. \n\nThis can be updated to your schedule preference as an email with marketing trends can be sent to best fit one's schedule. "}, "typeVersion": 1}, {"id": "3b156d97-11bf-4d8a-9bd9-c1e23a0592d8", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-420, -300], "parameters": {"color": 6, "width": 860, "height": 360, "content": "Scheduled trigger begin process to gather Pinterest Pin data and store them within Airtable. This data can be referenced or analyzed accordingly. \n\n*If you would like to bring in Pinterest Ads data, the data is already labeled as Organic.\n\nThis is perfect for those who are creating content calendars to understand content scheduling."}, "typeVersion": 1}, {"id": "65586422-a631-477b-833d-5c445b1be744", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [480, -580], "parameters": {"color": 4, "width": 940, "height": 460, "content": "AI Agent will go through Pinterest Pins and analyze any data and trends to be able to reach target audience. The data is then summarized within the Summary LLM.\n\nThe summarized results are then sent to the Marketing Manager within an email to help lead content creation efforts. "}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "d6f64ee7-ae49-4a6b-8bf8-9a709c580357", "connections": {"Airtable2": {"ai_tool": [[{"node": "Pinterest Analysis AI Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Pinterest Analysis AI Agent", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Pinterest Data Analysis Summary LLM", "type": "ai_languageModel", "index": 0}]]}, "Pinterest Analysis AI Agent": {"main": [[{"node": "Pinterest Data Analysis Summary LLM", "type": "main", "index": 0}]]}, "8:00am Morning Scheduled Trigger": {"main": [[{"node": "Pull List of Pinterest Pins From Account", "type": "main", "index": 0}]]}, "Pinterest Data Analysis Summary LLM": {"main": [[{"node": "Send Marketing Trends & Pinterest Analysis To Marketing Manager", "type": "main", "index": 0}]]}, "Update Data Field To Include Organic": {"main": [[{"node": "Create Record Within Pinterest Data Table", "type": "main", "index": 0}, {"node": "Pinterest Analysis AI Agent", "type": "main", "index": 0}]]}, "Pull List of Pinterest Pins From Account": {"main": [[{"node": "Update Data Field To Include Organic", "type": "main", "index": 0}]]}}}