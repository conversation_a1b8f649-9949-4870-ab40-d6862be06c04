{"id": "a4GTp998ENMMfuqK", "meta": {"instanceId": "24bd2f3b51439b955590389bfa4dd9889fbd30343962de0b7daedce624cf4a71"}, "name": "Save new Files received on Telegram to Google Drive", "tags": [], "nodes": [{"id": "0fcb072b-ea4b-43b2-ad7c-46ad62b1e2ad", "name": "On new Telegram Message", "type": "n8n-nodes-base.telegramTrigger", "position": [900, 520], "webhookId": "1e92584a-dd10-4fec-86a6-3b2691b85bba", "parameters": {"updates": ["message"], "additionalFields": {"download": true}}, "credentials": {"telegramApi": {"id": "EO2PA74ehePPYVFU", "name": "Telegram Notification Bot"}}, "typeVersion": 1}, {"id": "08e492f8-b969-4de2-b207-17fcd3cb8787", "name": "If Message contains a File", "type": "n8n-nodes-base.if", "position": [1160, 520], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "9b876834-1a86-48f1-9890-df60c739c91c", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.message.document }}", "rightValue": ""}]}}, "typeVersion": 2}, {"id": "f155a855-0eac-44c0-a52a-93446b9b3455", "name": "Upload File to GDrive", "type": "n8n-nodes-base.googleDrive", "position": [1500, 500], "parameters": {"name": "={{ $json.message.document.file_name }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "11gyG2TvG0sqCG202CN-w9rloGW-CzKBc", "cachedResultUrl": "https://drive.google.com/drive/folders/11gyG2TvG0sqCG202CN-w9rloGW-CzKBc", "cachedResultName": "Demos"}}, "credentials": {"googleDriveOAuth2Api": {"id": "lFPZxFgMIaEnEtm9", "name": "Google Drive account (automate everything)"}}, "typeVersion": 3}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "f474f0f2-6d57-4bb8-bf1d-15ed35cf8ef2", "connections": {"On new Telegram Message": {"main": [[{"node": "If Message contains a File", "type": "main", "index": 0}]]}, "If Message contains a File": {"main": [[{"node": "Upload File to GDrive", "type": "main", "index": 0}]]}}}