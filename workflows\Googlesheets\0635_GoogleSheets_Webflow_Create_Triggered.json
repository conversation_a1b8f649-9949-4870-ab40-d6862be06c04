{"nodes": [{"id": "096a8e0c-8f72-40fb-aa1e-118fb33a3916", "name": "Prepare Fields", "type": "n8n-nodes-base.code", "position": [1740, 860], "parameters": {"jsCode": "const formData = $input.all()[0].json.payload.data\nconst Date = $input.all()[0].json.payload.submittedAt || new Date()\n\nreturn {\n  ...formData, // creates a new field for every element inside formData\n  Date\n}\n\n  \n"}, "notesInFlow": false, "typeVersion": 2}, {"id": "c98bb655-aa79-447f-897d-56ba9640073b", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1660, 780], "parameters": {"color": 2, "width": 270, "height": 250, "content": "1 line of code to take the data object (adding date as a plus)"}, "typeVersion": 1}, {"id": "05a27975-ac48-48db-9c82-c9658a8d14c2", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1260, 640], "parameters": {"color": 6, "width": 267, "height": 394, "content": "Make sure to disable legacy API\n\n![](https://imgur.com/0tebypt.png)"}, "typeVersion": 1}, {"id": "59d25f8e-bc9d-43ac-9c4b-3013f81c3e3d", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [2040, 760], "parameters": {"color": 4, "width": 270, "height": 274, "content": "Automatically create column names and append data (works even on empty sheets)"}, "typeVersion": 1}, {"id": "33c45b7e-e696-4aed-9374-0b232bfd52f1", "name": "On Form Submission", "type": "n8n-nodes-base.webflowTrigger", "position": [1340, 860], "webhookId": "c3ef5b9f-88f6-40e6-bc54-067e421b059a", "parameters": {"site": "640cfc01791fc750653436fd"}, "credentials": {"webflowOAuth2Api": {"id": "a3UDqxewt1XM79VP", "name": "Webflow account"}}, "typeVersion": 2}, {"id": "4ce0eeea-dd09-4d79-967e-210f2762d5c3", "name": "Append New Row", "type": "n8n-nodes-base.googleSheets", "position": [2120, 860], "parameters": {"columns": {"value": {"Name": "={{ $json.data.Name }}", "Email": "={{ $json.data.Email }}", "Message": "={{ $json.data.Message }}"}, "schema": [{"id": "Name", "type": "string", "display": true, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Email", "type": "string", "display": true, "required": false, "displayName": "Email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Message", "type": "string", "display": true, "required": false, "displayName": "Message", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "data", "type": "string", "display": true, "removed": true, "required": false, "displayName": "data", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": []}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1gLJ5I4ZJ9FQHJH56lunUKnHUBUsIms9PciIkJYi8SJE/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1gLJ5I4ZJ9FQHJH56lunUKnHUBUsIms9PciIkJYi8SJE", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1gLJ5I4ZJ9FQHJH56lunUKnHUBUsIms9PciIkJYi8SJE/edit?usp=drivesdk", "cachedResultName": "Automation test"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "QkZbOZMXiUKxATjx", "name": "Google Sheets account 2"}}, "typeVersion": 4.5}, {"id": "01a09112-930c-493a-b16c-660e4dc3d272", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [260, 160], "parameters": {"color": 7, "width": 520, "height": 1680, "content": "## Self-hosted N8N users only:\n\n### How to get Client ID and Client Secret\n\n- From your Webflow dashboard go to \"Apps & Integrations\"\n![](https://imgur.com/IX2ruVB.png)\n\n- Look for \"App development\" and click \"Create an App\"\n![](https://imgur.com/J0be6lz.png)\n\n- Fill the fields and click \"Continue\"\n![](https://imgur.com/Uiwo7vp.png)\n\n- Inside \"Building blocks\" enable REST API, insert your \"Redirect URL\" from N8N, enable form access and click \"Create App\"\n![](https://imgur.com/lf8Xv7R.png)\n![](https://imgur.com/5yyex2U.png)\n\n- Copy and paste Client ID and Client Secret to N8N and connect\n\n\n"}, "typeVersion": 1}], "pinData": {}, "connections": {"Prepare Fields": {"main": [[{"node": "Append New Row", "type": "main", "index": 0}]]}, "On Form Submission": {"main": [[{"node": "Prepare Fields", "type": "main", "index": 0}]]}}}