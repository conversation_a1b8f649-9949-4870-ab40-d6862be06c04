{"id": "iA0rm7IWi7xmY5sQ", "meta": {"instanceId": "08daa2aa5b6032ff63690600b74f68f5b0f34a3b100102e019b35c4419168977", "templateCredsSetupCompleted": true}, "name": "Get Long Lived Facebook User or Page Access Token", "tags": [], "nodes": [{"id": "2a5077ad-89ee-4659-93a9-e01bcceba0ad", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [480, 280], "parameters": {}, "typeVersion": 1}, {"id": "e2c7ce4c-fbf9-4552-8a9e-5b41d72c8921", "name": "Get Long Lived FB User Token", "type": "n8n-nodes-base.httpRequest", "position": [1000, 280], "parameters": {"url": "https://graph.facebook.com/v20.0/oauth/access_token", "options": {"response": {"response": {"fullResponse": true}}}, "sendQuery": true, "queryParameters": {"parameters": [{"name": " grant_type", "value": "fb_exchange_token"}, {"name": " client_id", "value": "={{ $json[' client_id'] }}"}, {"name": " client_secret", "value": "={{ $json[' client_secret'] }}"}, {"name": " fb_exchange_token", "value": "={{ $json.user_access_token }}"}]}}, "typeVersion": 4.2}, {"id": "4e898760-43cd-4d4f-a76c-555175fb2a27", "name": "Get Long Lived FB Page Token", "type": "n8n-nodes-base.httpRequest", "position": [1280, 280], "parameters": {"url": "=https://graph.facebook.com/v20.0/{{ $('Set Parameter').item.json.app_scoped_user_id }}/accounts", "method": "=GET", "options": {"response": {"response": {"fullResponse": true}}}, "sendQuery": true, "queryParameters": {"parameters": [{"name": " access_token", "value": "={{ $json.body.access_token }}"}]}}, "typeVersion": 4.2}, {"id": "6ac48498-014f-4721-9629-acd3bd1329e7", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [640, -87.**************], "parameters": {"width": 260.**************, "height": 552.*************, "content": "## Set Required Facebook Parameter \n- client_id\n- client_secret\n- user_access_token\n- app-scoped-user-id (optional)\n\n## according to this doc\n https://developers.facebook.com/docs/facebook-login/guides/access-tokens/get-long-lived/"}, "typeVersion": 1}, {"id": "11a68266-f7ec-4c56-9327-c4fd0e4478f6", "name": "Set Parameter", "type": "n8n-nodes-base.set", "position": [700, 280], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "b1f6b8a1-dc25-4b1e-9aa3-0c0e452ae2de", "name": " client_id", "type": "string", "value": "xxxx"}, {"id": "9a63879a-c359-49ad-9fec-19c9e4c78dd6", "name": " client_secret", "type": "string", "value": "yyyy"}, {"id": "6971a841-ed5e-4d96-bdab-1eaec2d51ccc", "name": "user_access_token", "type": "string", "value": "zzzzz"}, {"id": "c75d5e50-62ea-4ee0-bfaa-5f79cf4d147e", "name": "app_scoped_user_id", "type": "string", "value": "uuuuuu"}]}}, "typeVersion": 3.4}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "390c37ce-2c9a-4341-bcb4-2f669f65e209", "connections": {"Set Parameter": {"main": [[{"node": "Get Long Lived FB User Token", "type": "main", "index": 0}]]}, "Get Long Lived FB User Token": {"main": [[{"node": "Get Long Lived FB Page Token", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set Parameter", "type": "main", "index": 0}]]}}}