{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "c2429079-50b7-4da8-9fe4-9a1879bd681c", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.twilioTrigger", "position": [-380, -460], "webhookId": "********-e049-480d-899e-d3318a93276b", "parameters": {"updates": ["com.twilio.messaging.inbound-message.received"]}, "credentials": {"twilioApi": {"id": "TJv4H4lXxPCLZT50", "name": "Twilio account"}}, "typeVersion": 1}, {"id": "b1c0dc4c-593f-49aa-8fec-a77c7e40928e", "name": "Search Available Courses", "type": "n8n-nodes-base.airtableTool", "position": [380, -80], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appO5xvP1aUBYKyJ7", "cachedResultUrl": "https://airtable.com/appO5xvP1aUBYKyJ7", "cachedResultName": "Northvale Institute of Technology Courses 2025-2026"}, "limit": 5, "table": {"__rl": true, "mode": "list", "value": "tblRfh0t0KNSJYJVw", "cachedResultUrl": "https://airtable.com/appO5xvP1aUBYKyJ7/tblRfh0t0KNSJYJVw", "cachedResultName": "Imported table"}, "options": {}, "operation": "search", "returnAll": false, "descriptionType": "manual", "filterByFormula": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Filter_By_Formula', ``, 'string') }}", "toolDescription": "Call this tool to access the course database. Ensure you have the course database schema before using this tool."}, "credentials": {"airtableTokenApi": {"id": "Und0frCQ6SNVX3VV", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "ad06d5f6-cd6d-4804-b633-cf065866f41e", "name": "Get Course Database Schema", "type": "n8n-nodes-base.airtableTool", "position": [240, -160], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appO5xvP1aUBYKyJ7", "cachedResultUrl": "https://airtable.com/appO5xvP1aUBYKyJ7", "cachedResultName": "Northvale Institute of Technology Courses 2025-2026"}, "resource": "base", "operation": "getSchema", "descriptionType": "manual", "toolDescription": "Call this tool to get the course database schema."}, "credentials": {"airtableTokenApi": {"id": "Und0frCQ6SNVX3VV", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "7d16ef89-3e63-4f64-9470-eb1bf9c76ece", "name": "Get User Message", "type": "n8n-nodes-base.set", "position": [-160, -460], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "5ca2fffb-2926-42df-ae2b-95ba081345ef", "name": "message", "type": "string", "value": "={{ $json.Body || $json.chatInput }}"}, {"id": "3bfdb166-0ab1-44b9-b6e4-ce6ad52a465c", "name": "sessionId", "type": "string", "value": "={{ $json.From || $json.sessionId }}"}]}}, "typeVersion": 3.4}, {"id": "b2b03e59-2c1d-4852-a8a6-37fb20f38b55", "name": "Send SMS reply", "type": "n8n-nodes-base.twilio", "position": [660, -460], "parameters": {"to": "={{ $json.fields.from }}", "from": "={{ $('<PERSON><PERSON><PERSON>').item.json.To }}", "message": "={{ $('Course Assistant Agent').item.json.output }}", "options": {}}, "credentials": {"twilioApi": {"id": "TJv4H4lXxPCLZT50", "name": "Twilio account"}}, "typeVersion": 1}, {"id": "c07ba0c0-2e22-48fc-bca9-cbaeb221ccf9", "name": "Append to Call Log", "type": "n8n-nodes-base.airtable", "position": [440, -460], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appO5xvP1aUBYKyJ7", "cachedResultUrl": "https://airtable.com/appO5xvP1aUBYKyJ7", "cachedResultName": "Northvale Institute of Technology Courses 2025-2026"}, "table": {"__rl": true, "mode": "list", "value": "tblRFuaayw0En6T0c", "cachedResultUrl": "https://airtable.com/appO5xvP1aUBYKyJ7/tblRFuaayw0En6T0c", "cachedResultName": "Call Log"}, "columns": {"value": {"from": "={{ $('Get User Message').first().json.sessionId }}", "answer": "={{ $json.output }}", "question": "={{ $('Get User Message').first().json.message }}"}, "schema": [{"id": "from", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "from", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "question", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "question", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "answer", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "answer", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Created", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Created", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["id"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "create"}, "credentials": {"airtableTokenApi": {"id": "Und0frCQ6SNVX3VV", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "ba7b4d7b-7b78-41f0-b158-3d1f09d14120", "name": "Course Assistant Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [60, -460], "parameters": {"text": "={{ $json.message }}", "options": {"systemMessage": "=You are a course enquiry assistant for the Northvale Institute of Technology helping students with various questions about the available courses for the year.\n* Answer factually and source the information from the course database to ensure you have updated information.\n* Avoid answering or engaging in any discussion not related to the Northvale Institute of Technology courses and instead, direct the student <NAME_EMAIL>.\n* always query the course database schema before using tools.\n\nNote: The airtable filter by query syntax was updated\n* Wrap your query in AND() or OR() to join parameters.\n* To filter select or multiple select finds, use the FIND() operation. eg. AND({Schedule_from}>=900, FIND('Wed', {Schedule_day}))\n* times should be inclusive unless otherwise stated. Use the >= or <= operators."}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "3c790125-6665-4a0c-85b4-397e71faae35", "name": "Get List of Professors", "type": "n8n-nodes-base.airtableTool", "position": [560, -180], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appO5xvP1aUBYKyJ7", "cachedResultUrl": "https://airtable.com/appO5xvP1aUBYKyJ7", "cachedResultName": "Northvale Institute of Technology Courses 2025-2026"}, "table": {"__rl": true, "mode": "list", "value": "tblRfh0t0KNSJYJVw", "cachedResultUrl": "https://airtable.com/appO5xvP1aUBYKyJ7/tblRfh0t0KNSJYJVw", "cachedResultName": "Imported table"}, "options": {"fields": ["<PERSON><PERSON><PERSON><PERSON>"]}, "operation": "search", "descriptionType": "manual", "toolDescription": "Call this tool to get a list of active professors."}, "credentials": {"airtableTokenApi": {"id": "Und0frCQ6SNVX3VV", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "27aacf1e-b8a7-46d0-915e-0481d9608251", "name": "Get List of Departments", "type": "n8n-nodes-base.airtableTool", "position": [500, -20], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appO5xvP1aUBYKyJ7", "cachedResultUrl": "https://airtable.com/appO5xvP1aUBYKyJ7", "cachedResultName": "Northvale Institute of Technology Courses 2025-2026"}, "table": {"__rl": true, "mode": "list", "value": "tblRfh0t0KNSJYJVw", "cachedResultUrl": "https://airtable.com/appO5xvP1aUBYKyJ7/tblRfh0t0KNSJYJVw", "cachedResultName": "Imported table"}, "options": {"fields": ["Department"]}, "operation": "search", "descriptionType": "manual", "toolDescription": "Call this tool to get a list of departments."}, "credentials": {"airtableTokenApi": {"id": "Und0frCQ6SNVX3VV", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "f1991f1f-9666-43d9-88ce-a2c083491a78", "name": "Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-40, -240], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "2afd9d28-a1ba-4364-a576-ed3e86c640b6", "name": "Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [100, -240], "parameters": {}, "typeVersion": 1.3}, {"id": "774472f7-eb3d-4251-97e3-8e4033a0cf4f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-940, -1100], "parameters": {"width": 420, "height": 1320, "content": "## Try It Out!\n### This n8n template offers a simple yet capable chatbot assistant who can answer course enquiries over SMS.\n\nGiven the right access to data, AI Agents are capable of planning and performing relatively complex research tasks to get their answers. In this example, the agent must first understand the database schema, retrieve lists of values before generating it's own query to search over the database.\n\n**Checkout the example database here - https://airtable.com/appO5xvP1aUBYKyJ7/shr8jSFDaghubDOrw**\n\n### How it works\n* A Twilio trigger gives us the ability to receive SMS input into our workflow via webhook.\n* The message is then directed to our AI agent who is instructed to assist the user and use the course database as reference. The database is an Airtable base.\n* The agent autonomously figures out which tool it needs to use and generates it's own \"filter_by_formula\" query to search over the available courses.\n* On successful search results, the Agent can then use this information to answer the user's query.\n* The Agent's output is logged in a second sheet of the Airtable base. We can use this later for analysis and lead gen.\n* Finally, the response is sent back to the user through SMS using Twilio.\n\n### How to use\n* Ensure your Twilio number is set to forward messages to this workflow's webhook URL.\n* Configure and update the course database as required. If you're not interested in courses, you can swap this out for inventory, deliveries or any other data relevant to your business.\n* Ask questions like:\n  * \"Can you help me find suitable courses to fill my Wednesday mornings?\"\n  * \"Which courses are being instructed by profession Lee?\"\n  * \"I'm interested in creative arts. What courses are available which could be relevant to me?\"\n\n### Requirements\n* Twilio for SMS receiving and sending\n* OpenAI for LLM and Agent\n* Airtable for Course Database\n\n### Customising this workflow\n* Add additional tools and expand the range of queries the agent is able to answer or assist with.\n* Not using Airtable? This technique also works with SQL databases like PostgreSQL.\n\n### Need Help?\nJoin the [Discord](https://discord.com/invite/XPKeKXeB7d) or ask in the [Forum](https://community.n8n.io/)!\n\nHappy Hacking!"}, "typeVersion": 1}], "pinData": {}, "connections": {"Model": {"ai_languageModel": [[{"node": "Course Assistant Agent", "type": "ai_languageModel", "index": 0}]]}, "Memory": {"ai_memory": [[{"node": "Course Assistant Agent", "type": "ai_memory", "index": 0}]]}, "Twilio Trigger": {"main": [[{"node": "Get User Message", "type": "main", "index": 0}]]}, "Get User Message": {"main": [[{"node": "Course Assistant Agent", "type": "main", "index": 0}]]}, "Append to Call Log": {"main": [[{"node": "Send SMS reply", "type": "main", "index": 0}]]}, "Course Assistant Agent": {"main": [[{"node": "Append to Call Log", "type": "main", "index": 0}]]}, "Get List of Professors": {"ai_tool": [[{"node": "Course Assistant Agent", "type": "ai_tool", "index": 0}]]}, "Get List of Departments": {"ai_tool": [[{"node": "Course Assistant Agent", "type": "ai_tool", "index": 0}]]}, "Search Available Courses": {"ai_tool": [[{"node": "Course Assistant Agent", "type": "ai_tool", "index": 0}]]}, "Get Course Database Schema": {"ai_tool": [[{"node": "Course Assistant Agent", "type": "ai_tool", "index": 0}]]}}}