{"meta": {"instanceId": "7599ed929ea25767a019b87ecbc83b90e16a268cb51892887b450656ac4518a2"}, "nodes": [{"id": "b962ef3d-b0ad-4b21-bb15-61b6521bfd03", "name": "HTML to PDF", "type": "@custom-js/n8n-nodes-pdf-toolkit.html2Pdf", "position": [220, 0], "parameters": {"htmlInput": "<h1>Hello World</h1>"}, "credentials": {"customJsApi": {"id": "h29wo2anYKdANAzm", "name": "CustomJS account"}}, "notesInFlow": false, "typeVersion": 1}, {"id": "988f427e-7eca-43e5-a77d-c69a92ec6158", "name": "Compress PDF file", "type": "@custom-js/n8n-nodes-pdf-toolkit.CompressPDF", "position": [460, 0], "parameters": {}, "credentials": {"customJsApi": {"id": "h29wo2anYKdANAzm", "name": "CustomJS account"}}, "typeVersion": 1}, {"id": "bbbf9fb1-2fc2-4de1-9854-149b63c7070c", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [0, 100], "parameters": {}, "typeVersion": 1}, {"id": "492b07d3-4643-4d1e-acbb-b0a7b7fde350", "name": "Compress PDF file1", "type": "@custom-js/n8n-nodes-pdf-toolkit.CompressPDF", "position": [460, 200], "parameters": {"resource": "url", "field_name": "={{ $json.path }}"}, "credentials": {"customJsApi": {"id": "h29wo2anYKdANAzm", "name": "CustomJS account"}}, "typeVersion": 1}, {"id": "d60193ff-0bf6-4692-83e2-d0e1e59c5656", "name": "Set PDF URL", "type": "n8n-nodes-base.code", "position": [220, 200], "parameters": {"jsCode": "return {\"json\": {\"path\": \"https://www.nlbk.niedersachsen.de/download/164891/Test-pdf_3.pdf.pdf\"}};"}, "typeVersion": 2}, {"id": "c68fc714-fc5a-456d-9126-ccbcfedce3ca", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [140, -100], "parameters": {"color": 4, "height": 260, "content": "### HTML to PDF\n- Request HTML Data\n- Convert HTML to PDF"}, "typeVersion": 1}, {"id": "5388484e-5b74-4ece-90a0-75fc3d9963b5", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [380, -100], "parameters": {"color": 5, "width": 260, "height": 260, "content": "### Compress Pages from PDF\n- Compress PDF as a binary file."}, "typeVersion": 1}, {"id": "014c6536-0270-4ac7-881a-4334816a9ffb", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [140, 160], "parameters": {"color": 3, "height": 260, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n### Set PDF URL\n- Request PDF from URL."}, "typeVersion": 1}, {"id": "f6e18c8b-3109-414b-a539-dbb586d6e75e", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [380, 160], "parameters": {"color": 2, "width": 260, "height": 260, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n### Compress Pages from PDF\n- Compress PDF as a binary file."}, "typeVersion": 1}], "pinData": {}, "connections": {"HTML to PDF": {"main": [[{"node": "Compress PDF file", "type": "main", "index": 0}]]}, "Set PDF URL": {"main": [[{"node": "Compress PDF file1", "type": "main", "index": 0}]]}, "Compress PDF file": {"main": [[]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "HTML to PDF", "type": "main", "index": 0}, {"node": "Set PDF URL", "type": "main", "index": 0}]]}}}