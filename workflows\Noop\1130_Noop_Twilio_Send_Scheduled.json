{"id": "79", "name": "Get the price of BTC in EUR and send an SMS when the price is larger than EUR 9000", "nodes": [{"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [590, 500], "parameters": {"triggerTimes": {"item": [{"mode": "everyMinute"}]}}, "typeVersion": 1}, {"name": "CoinGecko", "type": "n8n-nodes-base.coinGecko", "position": [790, 500], "parameters": {"coinIds": ["bitcoin"], "options": {}, "operation": "price", "currencies": ["eur"]}, "typeVersion": 1}, {"name": "IF", "type": "n8n-nodes-base.if", "position": [990, 500], "parameters": {"conditions": {"number": [{"value1": "={{$node[\"CoinGecko\"].json[\"bitcoin\"][\"eur\"]}}", "value2": 9000, "operation": "largerEqual"}]}}, "typeVersion": 1}, {"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.twilio", "position": [1190, 400], "parameters": {"to": "1234", "from": "1234", "message": "=The price went up! The new price is {{$node[\"CoinGecko\"].json[\"bitcoin\"][\"eur\"]}}"}, "credentials": {"twilioApi": "twilio-credentials"}, "typeVersion": 1}, {"name": "NoOp", "type": "n8n-nodes-base.noOp", "position": [1190, 600], "parameters": {}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"IF": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}], [{"node": "NoOp", "type": "main", "index": 0}]]}, "Cron": {"main": [[{"node": "CoinGecko", "type": "main", "index": 0}]]}, "CoinGecko": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}}}