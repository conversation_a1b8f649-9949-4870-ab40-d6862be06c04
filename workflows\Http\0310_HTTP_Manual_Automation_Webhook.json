{"nodes": [{"id": "3d58a8a9-50dd-4f06-8955-c73c30b64225", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [380, 240], "parameters": {"url": "https://randomuser.me/api/", "options": {}}, "typeVersion": 2}, {"id": "ceaf349d-3fa6-44b0-9238-2998ce026175", "name": "Spreadsheet File", "type": "n8n-nodes-base.spreadsheetFile", "position": [920, 480], "parameters": {"options": {"fileName": "users_spreadsheet"}, "operation": "toFile", "fileFormat": "csv"}, "typeVersion": 1}, {"id": "a8cd75a4-1b2c-4e1f-bd96-0377cc156025", "name": "Note", "type": "n8n-nodes-base.stickyNote", "position": [680, -14], "parameters": {"width": 523, "height": 302, "content": "### JSON to Google Sheets\nWe map data from the HTTP Request directly in the `Google Sheets` node, so we don't need a `Set` node before to transform the incoming data."}, "typeVersion": 1}, {"id": "a81fb564-f34a-4fd8-9758-6a2fb9bac6e0", "name": "Note1", "type": "n8n-nodes-base.stickyNote", "position": [680, 340], "parameters": {"width": 522, "height": 299, "content": "### JSON to .CSV\nWe use the `Set` node to trim down the data that we convert to CSV file format (and flatten it from it's previous object-like data structure). Change settings in `Spreadsheet File` node to convert to .xls etc."}, "typeVersion": 1}, {"id": "003a33f1-e060-4373-a97a-0be2c4a5e2a1", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [140, 240], "parameters": {}, "typeVersion": 1}, {"id": "b63a19f6-008c-4a38-8112-073433a2d125", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-340, 20], "parameters": {"width": 377.1993316649719, "height": 590.2004455566864, "content": "## 👋 How to use this template\nThis template shows how you can load JSON data from an API and load it into an App (Google Sheets) or convert to a file. Here's how to use it:\n\n1. Open the `Google Sheets` node and add a credential (or disabled the node)\n2. Click the `Execute Workflow` button, then double click the nodes to see their input and output data\n\n### To customize this template to you needs:\n1. Swap `When clicking \"Execute Workflow\"` and the `HTTP Request` node with an App trigger. If we don't have a Native app trigger, just replace `When clicking \"Execute Workflow\"` with a [Schedule trigger](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.scheduletrigger/).\n2. Disable or remove parts of the workflow that are not relevant to your usecase.\n4. Activate the workflow \n"}, "typeVersion": 1}, {"id": "426c8cce-0af6-4c9a-9702-9695093fe7fd", "name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [720, 120], "parameters": {"columns": {"value": {}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "id", "defaultMatch": true, "canBeUsedToMatch": true}, {"id": "status", "type": "string", "display": true, "required": false, "displayName": "status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "name", "type": "string", "display": true, "required": false, "displayName": "name", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["id"]}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1fAy_eUTZqaUBnCHTvF7F-VCu0zqlGlupgcAdL68UuJA/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1fAy_eUTZqaUBnCHTvF7F-VCu0zqlGlupgcAdL68UuJA", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1fAy_eUTZqaUBnCHTvF7F-VCu0zqlGlupgcAdL68UuJA/edit?usp=drivesdk", "cachedResultName": "Sync data from one app to another [one-way sync] (Destination example)"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "uJ1SWmfKH3MikNyZ", "name": "Google Sheets account 2"}}, "typeVersion": 4}, {"id": "5886f624-ab5a-4cd2-be2b-b166f617f77c", "name": "Set", "type": "n8n-nodes-base.set", "position": [720, 480], "parameters": {"values": {"string": [{"name": "Full Name", "value": "={{ $json.results[0].name.first }} {{ $json.results[0].name.last }}"}, {"name": "Country", "value": "={{ $json.results[0].location.country }}"}, {"name": "email", "value": "={{ $json.results[0].email }}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 2}], "connections": {"Set": {"main": [[{"node": "Spreadsheet File", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}, {"node": "Set", "type": "main", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}}}