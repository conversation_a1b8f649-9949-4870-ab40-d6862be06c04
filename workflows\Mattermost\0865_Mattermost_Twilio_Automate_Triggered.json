{"nodes": [{"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.twilio", "position": [900, 300], "parameters": {"message": "=The workflow named '{{$node[\"Error Trigger\"].json[\"workflow\"][\"name\"]}}' with the ID {{$node[\"Error Trigger\"].json[\"workflow\"][\"id\"]}} has encountered an error."}, "credentials": {"twilioApi": "<PERSON><PERSON><PERSON>"}, "typeVersion": 1}, {"name": "Mattermost", "type": "n8n-nodes-base.mattermost", "position": [650, 300], "parameters": {"message": "=The workflow named '{{$json[\"workflow\"][\"name\"]}}' with the ID {{$json[\"workflow\"][\"id\"]}} has encountered an error. The last node that was executed was {{$json[\"execution\"][\"lastNodeExecuted\"]}}.", "attachments": [], "otherOptions": {}}, "credentials": {"mattermostApi": "Mattermost"}, "typeVersion": 1}, {"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.errorTrigger", "position": [450, 300], "parameters": {}, "typeVersion": 1}], "connections": {"Mattermost": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Error Trigger": {"main": [[{"node": "Mattermost", "type": "main", "index": 0}]]}}}