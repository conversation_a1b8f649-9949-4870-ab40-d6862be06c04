{"meta": {"instanceId": "03e9d14e9196363fe7191ce21dc0bb17387a6e755dcc9acc4f5904752919dca8"}, "nodes": [{"id": "be5b0c9c-de92-4e34-88cb-98e88b0c19df", "name": "Start VM Scan in Qualys", "type": "n8n-nodes-base.httpRequest", "position": [1340, 500], "parameters": {"": "", "url": "={{ $json.platformurl }}/api/2.0/fo/scan/", "method": "POST", "options": {}, "sendBody": true, "sendQuery": true, "curlImport": "", "contentType": "multipart-form-data", "infoMessage": "", "sendHeaders": true, "specifyQuery": "keypair", "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "asset_groups", "value": "={{ $json.asset_groups }}", "parameterType": "formData"}, {"name": "scan_title", "value": "={{ $json.scan_title }}", "parameterType": "formData"}, {"name": "option_title", "value": "={{ $json.option_title }}", "parameterType": "formData"}]}, "specifyHeaders": "keypair", "queryParameters": {"parameters": [{"name": "action", "value": "launch"}]}, "headerParameters": {"parameters": [{"name": "X-Requested-With", "value": "n8n"}]}, "httpVariantWarning": "", "nodeCredentialType": "qualys<PERSON>pi", "provideSslCertificates": false}, "credentials": {"qualysApi": {"id": "KdkmNjVYkDUzHAvw", "name": "Qualys account"}}, "typeVersion": 4.2, "extendsCredential": "qualys<PERSON>pi"}, {"id": "0d140ce1-89e0-4135-821f-0b32004fc6aa", "name": "Convert XML to JSON", "type": "n8n-nodes-base.xml", "position": [1540, 500], "parameters": {"options": {}, "dataPropertyName": "=data"}, "typeVersion": 1}, {"id": "ec737485-bf8b-4e8a-9843-2566c13106a8", "name": "<PERSON><PERSON>an Results", "type": "n8n-nodes-base.httpRequest", "position": [2640, 460], "parameters": {"": "", "url": "={{ $('Demo Data').item.json[\"platformurl\"] }}/api/2.0/fo/scan/vm/summary", "method": "GET", "options": {}, "sendBody": false, "sendQuery": true, "curlImport": "", "infoMessage": "", "sendHeaders": true, "specifyQuery": "keypair", "authentication": "predefinedCredentialType", "specifyHeaders": "keypair", "queryParameters": {"parameters": [{"name": "action", "value": "list"}, {"name": "scan_reference", "value": "={{ $('Convert XML to JSON').item.json.SIMPLE_RETURN.RESPONSE.ITEM_LIST.ITEM[1].VALUE }}"}]}, "headerParameters": {"parameters": [{"name": "X-Requested-With", "value": "n8n"}]}, "httpVariantWarning": "", "nodeCredentialType": "qualys<PERSON>pi", "provideSslCertificates": false}, "credentials": {"qualysApi": {"id": "KdkmNjVYkDUzHAvw", "name": "Qualys account"}}, "typeVersion": 4.2, "extendsCredential": "qualys<PERSON>pi"}, {"id": "56a60798-3db1-4c69-962f-75009f894196", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [2220, 420], "parameters": {"options": {"reset": true}}, "typeVersion": 3}, {"id": "37ac0cdf-8412-40c7-b01c-d592e4d1f378", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [2560, 180], "parameters": {"color": 7, "width": 596.*************, "height": 493.**************, "content": "![Imgur](https://uploads.n8n.io/templates/qualys.png)\nFor more information about the query that is being performed on the Qualys end, check out the [Manage Scans Documentation](https://qualysguard.qg2.apps.qualys.com/qwebhelp/fo_portal/api_doc/scans/index.htm#t=vm_scans%2Fmanage_vm_scans.htm). The results are returned in XML, which n8n can natively convert to JSON. This allows for easy checking of the status in n8n. "}, "typeVersion": 1}, {"id": "075a4e21-cc30-4e31-a1f9-d2f872ab978c", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1274.994996265108, 51.04030212612997], "parameters": {"color": 7, "width": 447.57018680355174, "height": 642.6627860215806, "content": "![Qualys](https://uploads.n8n.io/templates/qualys.png)\n## Trigger scan on Qualys agent\nFor more information on launching a scan, visit the\n[Launch Documentation](\nhttps://qualysguard.qg2.apps.qualys.com/qwebhelp/fo_portal/api_doc/scans/index.htm#t=vm_scans%2Flaunch_vm_scan.htm). The responses from Qualys are in XML, which n8n makes short work of. "}, "typeVersion": 1}, {"id": "5da3f500-0ccf-4eed-9d05-7709668cf2bb", "name": "Wait 5 Min", "type": "n8n-nodes-base.wait", "position": [2440, 460], "webhookId": "f2d07724-882a-4010-9ce2-ff389ee962af", "parameters": {"unit": "minutes"}, "typeVersion": 1.1}, {"id": "5cf921ac-cd6b-4a27-b679-3d1ecdb3eb49", "name": "Convert XML to JSON1", "type": "n8n-nodes-base.xml", "position": [2800, 460], "parameters": {"options": {}, "dataPropertyName": "=data"}, "typeVersion": 1}, {"id": "0580bb11-38c4-49a1-ab00-4cdfb49c8f9d", "name": "Check if <PERSON><PERSON> Finished", "type": "n8n-nodes-base.if", "position": [3000, 460], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "ef397200-064a-428f-a5b2-19d2342a9113", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.SCAN_SUMMARY_OUTPUT.RESPONSE.SCAN_SUMMARY_LIST.SCAN_SUMMARY.SCAN_DETAILS.STATUS }}", "rightValue": "FINISHED"}]}}, "typeVersion": 2}, {"id": "ec05f06b-e009-4f1c-97e4-223705d3be32", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [260, 520], "parameters": {}, "typeVersion": 1}, {"id": "1cbd10cd-342c-41bf-ae8f-832324cfbb30", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [220, 40], "parameters": {"color": 7, "width": 1043.6429958055905, "height": 657.4661247924577, "content": "![Slack](https://uploads.n8n.io/templates/slack.png)\n## Triggered from Slack Parent Workflow\nThe workflow begins with the execute workflow trigger, but the manual execution trigger was left in to test it manually. Make sure to turn of the execute workflow trigger when running it manually. Make sure to set your Slack Channel ID in the Edit node to ensure that the same channel is set across all slack nodes. From there, n8n sends a message to slack to let the user know that their request is being processed. The two threads are then merged to ensure only one thing is done at a time. Don't forget to set your Platform URL in the Global Variables. More information about that can be found at Qualys's [Platform Documentation](https://www.qualys.com/platform-identification/) page. "}, "typeVersion": 1}, {"id": "fb59e00b-36c6-429e-8696-f49b78445925", "name": "Sticky Note15", "type": "n8n-nodes-base.stickyNote", "position": [2160, 60], "parameters": {"color": 7, "width": 387.82834121275613, "height": 620.5198690828006, "content": "![n8n](https://uploads.n8n.io/templates/n8n.png)\n## n8n Loop Node\nThe report objects are queried then loops every 5 min until the report returns a finished status. We have found that a report can take 40 minutes or more to complete. This is where n8n steps in and checks for us every 5 minutes. When the status of the scan changes to finished, the loop ends and the results are posted to Slack along with a link back to the scan results. "}, "typeVersion": 1}, {"id": "2337ad0e-e361-474a-9923-75c4826400b6", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [3167, 184.84774251864644], "parameters": {"color": 7, "width": 679.3808146538605, "height": 493.10714356069377, "content": "![Imgur](https://uploads.n8n.io/templates/slack.png)\n## Upload Report to Slack\nOnce the scan is completed, the summary of the report and a link to report object is posted to Slack for easy retrieval. Additionally the original receipt message is deleted to ensure the new message generates a Slack notification. "}, "typeVersion": 1}, {"id": "68a9eee6-05c4-4655-ab74-4a68fc68af26", "name": "Post Receipt", "type": "n8n-nodes-base.slack", "position": [740, 340], "parameters": {"text": "Vulnerability Scan request received, processing now. ", "select": "channel", "channelId": {"__rl": true, "mode": "id", "value": "={{ $('Global Variables & Slack Channel').item.json[\"slackChannelId\"] }}"}, "otherOptions": {"includeLinkToWorkflow": false}}, "credentials": {"slackApi": {"id": "DZJDes1ZtGpqClNk", "name": "<PERSON><PERSON><PERSON>"}}, "typeVersion": 2.2}, {"id": "43af793b-061f-4048-b110-546903b803b6", "name": "Confirm Waiting", "type": "n8n-nodes-base.slack", "position": [1800, 540], "parameters": {"ts": "={{ $('Save receipt message timestamp').item.json[\"ts\"] }}", "text": "=Scan successfully initiated, now waiting for `{{ $('Convert XML to JSON').item.json.SIMPLE_RETURN.RESPONSE.ITEM_LIST.ITEM[1].VALUE }}` to complete. \n\nNo action is needed, and I will post the summary report and link to results when it's complete. ", "channelId": {"__rl": true, "mode": "id", "value": "={{ $('Global Variables & Slack Channel').item.json[\"slackChannelId\"] }}"}, "operation": "update", "updateFields": {"parse": "client", "link_names": false}}, "credentials": {"slackApi": {"id": "DZJDes1ZtGpqClNk", "name": "<PERSON><PERSON><PERSON>"}}, "typeVersion": 2.2}, {"id": "326bb10c-0e8e-4df7-bc67-dad015240d15", "name": "Delete Receipt", "type": "n8n-nodes-base.slack", "position": [3480, 440], "parameters": {"select": "channel", "channelId": {"__rl": true, "mode": "id", "value": "={{ $('Global Variables & Slack Channel').item.json[\"slackChannelId\"] }}"}, "operation": "delete", "timestamp": "={{ $('Save receipt message timestamp').item.json[\"ts\"] }}"}, "credentials": {"slackApi": {"id": "DZJDes1ZtGpqClNk", "name": "<PERSON><PERSON><PERSON>"}}, "typeVersion": 2.2}, {"id": "c8668283-e6ec-4dbd-92d0-aec1f07c01a7", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1740.3532113511565, 44.007696543933434], "parameters": {"color": 7, "width": 408.91770357210225, "height": 645.055566466257, "content": "![Slack](https://uploads.n8n.io/templates/slack.png)\n## Let user's know that it's time to wait\nGood customer service comes from communication. And that's what this section does, it alerts the user that the scan was triggered successfully, and now it is time to wait for it to finish. Feel free to change this message to better suit your needs. It will be deleted when the results are posted. "}, "typeVersion": 1}, {"id": "defa2773-ea65-481d-a6d6-bb40c70e6762", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [-440, 40], "parameters": {"width": 646.7396383244529, "height": 994.2389415638766, "content": "![n8n](https://uploads.n8n.io/templates/n8n.png)\n# Qualys Vulnerability Trigger Scan Workflow\n\n## This workflow is triggered by a parent workflow initiated via a Slack shortcut. Upon activation, it collects input from a modal window in Slack and initiates a vulnerability scan using the Qualys API.\n\n**Key Features:**\n- **Trigger:** Launched by a parent workflow through a Slack shortcut with modal input.\n- **API Integration:** Utilizes the Qualys API for vulnerability scanning.\n- **Data Conversion:** Converts XML scan results to JSON for further processing.\n- **Loop Mechanism:** Continuously checks the scan status until completion.\n- **Slack Notifications:** Posts scan summary and detailed results to a specified Slack channel.\n\n\n**Workflow Nodes:**\n1. **Start VM Scan in Qualys:** Initiates the scan with specified parameters.\n2. **Convert XML to JSON:** Converts the scan results from XML format to JSON.\n3. **Fetch Scan Results:** Retrieves scan results from Qualys.\n4. **Check if Scan Finished:** Verifies whether the scan is complete.\n5. **Loop Mechanism:** Handles the repetitive checking of the scan status.\n6. **Slack Notifications:** Posts updates and results to Slack.\n\n\n**Relevant Links:**\n- [Qualys API Documentation](https://qualysguard.qg2.apps.qualys.com/qwebhelp/fo_portal/api_doc/scans/index.htm#t=vm_scans%2Flaunch_vm_scan.htm)\n- [Qualys Platform Documentation](https://www.qualys.com/platform-identification/)\n"}, "typeVersion": 1}, {"id": "de2c15bd-4144-4ca8-9c0d-370ecf334650", "name": "Demo Data", "type": "n8n-nodes-base.set", "position": [560, 520], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "070178a6-73b0-458b-8657-20ab4ff0485c", "name": "option_title", "type": "string", "value": "Initial Options"}, {"id": "3605424b-5bfc-44f0-b6e4-e0d6b1130b8e", "name": "scan_title", "type": "string", "value": "n8n Scan 1"}, {"id": "2320d966-b834-46fb-b674-be97cc08682e", "name": "asset_groups", "type": "string", "value": "Group1"}]}}, "typeVersion": 3.3}, {"id": "0ec55480-424c-4686-b8f7-8a98b5941c8e", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [820, 700], "parameters": {"color": 5, "width": 535.8333316661617, "height": 702.5170959123625, "content": "![Qualys](https://uploads.n8n.io/templates/qualysmodalscan.png)\n### 🔄The data input into this Modal will be processed in this workflow"}, "typeVersion": 1}, {"id": "9f6291ad-280f-4a0c-b84a-5eebfbb9172f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [1120, 500], "parameters": {"mode": "combine", "options": {}, "combinationMode": "multiplex"}, "typeVersion": 2.1}, {"id": "783d9bcd-faf1-4427-ab5c-de32df64f819", "name": "Post Vulnerability <PERSON><PERSON> to Slack", "type": "n8n-nodes-base.slack", "position": [3240, 500], "parameters": {"select": "channel", "blocksUi": "={\n\t\"blocks\": [\n\t\t{\n\t\t\t\"type\": \"image\",\n\t\t\t\"block_id\": \"image_1\",\n\t\t\t\"image_url\": \"https://i.imgur.com/6BtgQVV.png\",\n\t\t\t\"alt_text\": \"{{ $('Convert XML to JSON').item.json[\"SIMPLE_RETURN\"][\"RESPONSE\"][\"ITEM_LIST\"][\"ITEM\"][0][\"VALUE\"] }}\"\n\t\t},\n\t\t{\n\t\t\t\"type\": \"header\",\n\t\t\t\"block_id\": \"header_1\",\n\t\t\t\"text\": {\n\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\"text\": \"📊 Qualys Scan Summary\"\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\t\"type\": \"section\",\n\t\t\t\"block_id\": \"section_scan_details\",\n\t\t\t\"text\": {\n\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\"text\": \"*📝 Scan Title:* {{ $json[\"SCAN_SUMMARY_OUTPUT\"][\"RESPONSE\"][\"SCAN_SUMMARY_LIST\"][\"SCAN_SUMMARY\"][\"SCAN_INPUT\"][\"TITLE\"] }}\\n*👤 User:* {{ $json[\"SCAN_SUMMARY_OUTPUT\"][\"RESPONSE\"][\"SCAN_SUMMARY_LIST\"][\"SCAN_SUMMARY\"][\"SCAN_INPUT\"][\"USER\"][\"USERNAME\"] }}\\n*🔍 Scan Status:* FINISHED\"\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\t\"type\": \"section\",\n\t\t\t\"block_id\": \"section_general_info\",\n\t\t\t\"fields\": [\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\t\"text\": \"*⏱️ Scheduled:* {{ $json[\"SCAN_SUMMARY_OUTPUT\"][\"RESPONSE\"][\"SCAN_SUMMARY_LIST\"][\"SCAN_SUMMARY\"][\"SCAN_INPUT\"][\"SCHEDULED\"] }}\"\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\t\"text\": \"*📋 Option Profile:* {{ $json[\"SCAN_SUMMARY_OUTPUT\"][\"RESPONSE\"][\"SCAN_SUMMARY_LIST\"][\"SCAN_SUMMARY\"][\"SCAN_INPUT\"][\"OPTION_PROFILE\"][\"NAME\"] }}\"\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\t\"text\": \"*🎯 Targets:* IP List ({{ $json[\"SCAN_SUMMARY_OUTPUT\"][\"RESPONSE\"][\"SCAN_SUMMARY_LIST\"][\"SCAN_SUMMARY\"][\"SCAN_INPUT\"][\"TARGETS\"][\"IP_LIST\"][\"COUNT\"] }} IPs), Asset Group {{ $json[\"SCAN_SUMMARY_OUTPUT\"][\"RESPONSE\"][\"SCAN_SUMMARY_LIST\"][\"SCAN_SUMMARY\"][\"SCAN_INPUT\"][\"TARGETS\"][\"ASSET_GROUP_LIST\"][\"ASSET_GROUP_DATA\"][\"ASSET_GROUP\"][\"NAME\"] }}\"\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\t\"text\": \"*🚀 Scan Launched:* {{ $json[\"SCAN_SUMMARY_OUTPUT\"][\"RESPONSE\"][\"SCAN_SUMMARY_LIST\"][\"SCAN_SUMMARY\"][\"SCAN_INPUT\"][\"SCAN_DATETIME\"] }}\"\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\t\"text\": \"*⏳ Duration:* {{ $json[\"SCAN_SUMMARY_OUTPUT\"][\"RESPONSE\"][\"SCAN_SUMMARY_LIST\"][\"SCAN_SUMMARY\"][\"SCAN_DETAILS\"][\"DURATION\"] }} seconds\"\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\t\"text\": \"*🖥️ Detected Hosts:* {{ $json[\"SCAN_SUMMARY_OUTPUT\"][\"RESPONSE\"][\"SCAN_SUMMARY_LIST\"][\"SCAN_SUMMARY\"][\"SCAN_RESULTS\"][\"HOSTS\"][\"COUNT\"] }}\"\n\t\t\t\t}\n\t\t\t]\n\t\t},\n\t\t{\n\t\t\t\"type\": \"section\",\n\t\t\t\"block_id\": \"section_detections_summary\",\n\t\t\t\"text\": {\n\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\"text\": \"*🔎 Detections Summary:*\"\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\t\"type\": \"section\",\n\t\t\t\"block_id\": \"section_detections_details\",\n\t\t\t\"fields\": [\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\t\"text\": \"*🛡️ Confirmed Vulnerabilities:* {{ $json[\"SCAN_SUMMARY_OUTPUT\"][\"RESPONSE\"][\"SCAN_SUMMARY_LIST\"][\"SCAN_SUMMARY\"][\"SCAN_RESULTS\"][\"DETECTIONS\"][\"VULN\"][\"CONFIRMED\"][\"TOTAL_COUNT\"] }}\\n   - Minimal Severity: {{ $json[\"SCAN_SUMMARY_OUTPUT\"][\"RESPONSE\"][\"SCAN_SUMMARY_LIST\"][\"SCAN_SUMMARY\"][\"SCAN_RESULTS\"][\"DETECTIONS\"][\"VULN\"][\"CONFIRMED\"][\"COUNT_BY_SEVERITY\"][\"SEVERITY_1\"] }}\\n   - Medium Severity: {{ $json[\"SCAN_SUMMARY_OUTPUT\"][\"RESPONSE\"][\"SCAN_SUMMARY_LIST\"][\"SCAN_SUMMARY\"][\"SCAN_RESULTS\"][\"DETECTIONS\"][\"VULN\"][\"CONFIRMED\"][\"COUNT_BY_SEVERITY\"][\"SEVERITY_2\"] }}\\n   - Serious Severity: {{ $json[\"SCAN_SUMMARY_OUTPUT\"][\"RESPONSE\"][\"SCAN_SUMMARY_LIST\"][\"SCAN_SUMMARY\"][\"SCAN_RESULTS\"][\"DETECTIONS\"][\"VULN\"][\"CONFIRMED\"][\"COUNT_BY_SEVERITY\"][\"SEVERITY_3\"] }}\\n   - Critical Severity: {{ $json[\"SCAN_SUMMARY_OUTPUT\"][\"RESPONSE\"][\"SCAN_SUMMARY_LIST\"][\"SCAN_SUMMARY\"][\"SCAN_RESULTS\"][\"DETECTIONS\"][\"VULN\"][\"CONFIRMED\"][\"COUNT_BY_SEVERITY\"][\"SEVERITY_4\"] }}\\n   - Urgent Severity: {{ $json[\"SCAN_SUMMARY_OUTPUT\"][\"RESPONSE\"][\"SCAN_SUMMARY_LIST\"][\"SCAN_SUMMARY\"][\"SCAN_RESULTS\"][\"DETECTIONS\"][\"VULN\"][\"CONFIRMED\"][\"COUNT_BY_SEVERITY\"][\"SEVERITY_5\"] }}\"\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\t\"text\": \"*📈 Information Gathered:* {{ $json[\"SCAN_SUMMARY_OUTPUT\"][\"RESPONSE\"][\"SCAN_SUMMARY_LIST\"][\"SCAN_SUMMARY\"][\"SCAN_RESULTS\"][\"DETECTIONS\"][\"IG\"][\"TOTAL_COUNT\"] }}\\n   - Minimal Severity:  {{ $json[\"SCAN_SUMMARY_OUTPUT\"][\"RESPONSE\"][\"SCAN_SUMMARY_LIST\"][\"SCAN_SUMMARY\"][\"SCAN_RESULTS\"][\"DETECTIONS\"][\"IG\"][\"COUNT_BY_SEVERITY\"][\"SEVERITY_1\"] }}\\n   - Medium Severity: {{ $json[\"SCAN_SUMMARY_OUTPUT\"][\"RESPONSE\"][\"SCAN_SUMMARY_LIST\"][\"SCAN_SUMMARY\"][\"SCAN_RESULTS\"][\"DETECTIONS\"][\"IG\"][\"COUNT_BY_SEVERITY\"][\"SEVERITY_2\"] }}\\n   - Serious Severity: {{ $json[\"SCAN_SUMMARY_OUTPUT\"][\"RESPONSE\"][\"SCAN_SUMMARY_LIST\"][\"SCAN_SUMMARY\"][\"SCAN_RESULTS\"][\"DETECTIONS\"][\"IG\"][\"COUNT_BY_SEVERITY\"][\"SEVERITY_3\"] }}\"\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\t\"text\": \"*⚠️ Potential Vulnerabilities:* {{ $json[\"SCAN_SUMMARY_OUTPUT\"][\"RESPONSE\"][\"SCAN_SUMMARY_LIST\"][\"SCAN_SUMMARY\"][\"SCAN_RESULTS\"][\"DETECTIONS\"][\"VULN\"][\"POTENTIAL\"][\"TOTAL_COUNT\"] }}\\n   - Minimal Severity: {{ $json[\"SCAN_SUMMARY_OUTPUT\"][\"RESPONSE\"][\"SCAN_SUMMARY_LIST\"][\"SCAN_SUMMARY\"][\"SCAN_RESULTS\"][\"DETECTIONS\"][\"VULN\"][\"POTENTIAL\"][\"COUNT_BY_SEVERITY\"][\"SEVERITY_1\"] }}\\n   - Medium Severity: {{ $json[\"SCAN_SUMMARY_OUTPUT\"][\"RESPONSE\"][\"SCAN_SUMMARY_LIST\"][\"SCAN_SUMMARY\"][\"SCAN_RESULTS\"][\"DETECTIONS\"][\"VULN\"][\"POTENTIAL\"][\"COUNT_BY_SEVERITY\"][\"SEVERITY_2\"] }}\\n   - Serious Severity: {{ $json[\"SCAN_SUMMARY_OUTPUT\"][\"RESPONSE\"][\"SCAN_SUMMARY_LIST\"][\"SCAN_SUMMARY\"][\"SCAN_RESULTS\"][\"DETECTIONS\"][\"VULN\"][\"POTENTIAL\"][\"COUNT_BY_SEVERITY\"][\"SEVERITY_3\"] }}\\n   - Critical Severity: {{ $json[\"SCAN_SUMMARY_OUTPUT\"][\"RESPONSE\"][\"SCAN_SUMMARY_LIST\"][\"SCAN_SUMMARY\"][\"SCAN_RESULTS\"][\"DETECTIONS\"][\"VULN\"][\"POTENTIAL\"][\"COUNT_BY_SEVERITY\"][\"SEVERITY_4\"] }}\\n   - Urgent Severity: {{ $json[\"SCAN_SUMMARY_OUTPUT\"][\"RESPONSE\"][\"SCAN_SUMMARY_LIST\"][\"SCAN_SUMMARY\"][\"SCAN_RESULTS\"][\"DETECTIONS\"][\"VULN\"][\"POTENTIAL\"][\"COUNT_BY_SEVERITY\"][\"SEVERITY_5\"] }}\"\n\t\t\t\t}\n\t\t\t]\n\t\t},\n\t\t{\n\t\t\t\"type\": \"section\",\n\t\t\t\"block_id\": \"final_section_with_button\",\n\t\t\t\"text\": {\n\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\"text\": \"🔗 View the full report in Qualys\"\n\t\t\t},\n\t\t\t\"accessory\": {\n\t\t\t\t\"type\": \"button\",\n\t\t\t\t\"text\": {\n\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\"text\": \"View Report in Qualys\",\n\t\t\t\t\t\"emoji\": true\n\t\t\t\t},\n\t\t\t\t\"value\": \"click_me_123\",\n\t\t\t\t\"style\": \"primary\",\n\t\t\t\t\"url\": \"{{ $('Demo Data').item.json[\"platformurl\"] }}/fo/report/report_view.php?id={{ $('Convert XML to JSON').item.json[\"SIMPLE_RETURN\"][\"RESPONSE\"][\"ITEM_LIST\"][\"ITEM\"][0][\"VALUE\"] }}&default=1&format=30\",\n\t\t\t\t\"action_id\": \"button-action\"\n\t\t\t}\n\t\t}\n\t]\n}", "channelId": {"__rl": true, "mode": "id", "value": "={{ $('Global Variables & Slack Channel').item.json[\"slackChannelId\"] }}"}, "messageType": "block", "otherOptions": {}}, "credentials": {"slackApi": {"id": "DZJDes1ZtGpqClNk", "name": "<PERSON><PERSON><PERSON>"}}, "typeVersion": 2.2}, {"id": "91444583-66d8-4d5b-ba88-4d8869d508b6", "name": "When clicking \"Test workflow\"", "type": "n8n-nodes-base.manualTrigger", "disabled": true, "position": [260, 340], "parameters": {}, "typeVersion": 1}, {"id": "4b8ade25-0377-4f00-a744-f610b17eea93", "name": "Begin Wait Loop", "type": "n8n-nodes-base.noOp", "position": [1800, 400], "parameters": {}, "typeVersion": 1}, {"id": "b830b9d8-e7aa-49bb-9640-d1def697f3e1", "name": "Merge1", "type": "n8n-nodes-base.merge", "position": [2020, 420], "parameters": {"mode": "chooseBranch"}, "typeVersion": 2.1}, {"id": "389381c3-bd51-4e22-a102-e47b5945576c", "name": "Save receipt message timestamp", "type": "n8n-nodes-base.set", "position": [920, 340], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "111526ec-0501-4af9-b66e-c677cb8fe25f", "name": "ts", "type": "string", "value": "={{ $json.message.ts }}"}]}}, "typeVersion": 3.3}, {"id": "51005deb-2676-4375-9ac8-780eb301f7f5", "name": "Global Variables & Slack Channel", "type": "n8n-nodes-base.set", "position": [560, 340], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9849fe48-7a7a-4f2b-a404-c7827249e9c2", "name": "slackChannelId", "type": "string", "value": "C05LAN72WJK"}, {"id": "36aad8b5-b51a-4df0-b1a7-159a90b802b2", "name": "<PERSON>url", "type": "string", "value": "https://qualysapi.qg3.apps.qualys.com"}]}}, "typeVersion": 3.3}, {"id": "7d6d5ab7-5a87-46c8-baa8-d79a05d8346d", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [220, 700], "parameters": {"color": 5, "width": 596.6847639718076, "height": 438.8903816479826, "content": "![Qualys](https://uploads.n8n.io/templates/qualysscanshortcut.png)\n### 🤖 Triggering this workflow is as easy as typing a backslash in Slack and filling out the modal on the right"}, "typeVersion": 1}], "pinData": {}, "connections": {"Merge": {"main": [[{"node": "Start VM Scan in Qualys", "type": "main", "index": 0}]]}, "Merge1": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Demo Data": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Wait 5 Min": {"main": [[{"node": "<PERSON><PERSON>an Results", "type": "main", "index": 0}]]}, "Post Receipt": {"main": [[{"node": "Save receipt message timestamp", "type": "main", "index": 0}]]}, "Begin Wait Loop": {"main": [[{"node": "Merge1", "type": "main", "index": 0}]]}, "Confirm Waiting": {"main": [[{"node": "Merge1", "type": "main", "index": 1}]]}, "Loop Over Items": {"main": [null, [{"node": "Wait 5 Min", "type": "main", "index": 0}]]}, "Fetch Scan Results": {"main": [[{"node": "Convert XML to JSON1", "type": "main", "index": 0}]]}, "Convert XML to JSON": {"main": [[{"node": "Confirm Waiting", "type": "main", "index": 0}, {"node": "Begin Wait Loop", "type": "main", "index": 0}]]}, "Convert XML to JSON1": {"main": [[{"node": "Check if <PERSON><PERSON> Finished", "type": "main", "index": 0}]]}, "Check if Scan Finished": {"main": [[{"node": "Delete Receipt", "type": "main", "index": 0}, {"node": "Post Vulnerability <PERSON><PERSON> to Slack", "type": "main", "index": 0}], [{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Start VM Scan in Qualys": {"main": [[{"node": "Convert XML to JSON", "type": "main", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "Demo Data", "type": "main", "index": 0}, {"node": "Global Variables & Slack Channel", "type": "main", "index": 0}]]}, "When clicking \"Test workflow\"": {"main": [[{"node": "Demo Data", "type": "main", "index": 0}, {"node": "Global Variables & Slack Channel", "type": "main", "index": 0}]]}, "Save receipt message timestamp": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Global Variables & Slack Channel": {"main": [[{"node": "Post Receipt", "type": "main", "index": 0}]]}}}