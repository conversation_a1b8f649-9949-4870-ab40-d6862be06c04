{"nodes": [{"id": "169e3a8c-82f5-4527-a187-27b8e5d903c1", "name": "Spotify Next", "type": "n8n-nodes-base.spotify", "position": [1300, -40], "parameters": {"operation": "nextSong"}, "credentials": {"spotifyOAuth2Api": {"id": "4AquI6TQMHILfmvx", "name": "Spotify App Credentials"}}, "typeVersion": 1}, {"id": "7840d6b8-7eb4-4ac2-8bd0-946561f7de38", "name": "Spotify Resume", "type": "n8n-nodes-base.spotify", "position": [1300, 660], "parameters": {"operation": "resume"}, "credentials": {"spotifyOAuth2Api": {"id": "4AquI6TQMHILfmvx", "name": "Spotify App Credentials"}}, "typeVersion": 1}, {"id": "35e768a3-b648-4d5e-a6a4-fa5f5be3d922", "name": "Spotify Pause", "type": "n8n-nodes-base.spotify", "position": [1300, 480], "parameters": {"operation": "pause"}, "credentials": {"spotifyOAuth2Api": {"id": "4AquI6TQMHILfmvx", "name": "Spotify App Credentials"}}, "typeVersion": 1}, {"id": "0a391400-a8f0-4c1e-ac79-bbdea4aa21b4", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-361, 55], "parameters": {"width": 611.1911357340722, "height": 291.1542012927053, "content": "### Receive MQTT message from IKEA 5-button Switch, and route actions."}, "typeVersion": 1}, {"id": "164e904f-278d-4e48-81de-e1fc050e683a", "name": "Spotify API - Volume up 5pct", "type": "n8n-nodes-base.httpRequest", "position": [1300, 120], "parameters": {"url": "https://api.spotify.com/v1/me/player/volume", "method": "PUT", "options": {}, "sendQuery": true, "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "=device_id", "value": "={{ $json.device.id }}"}, {"name": "volume_percent", "value": "={{ Math.min($json.device.volume_percent + 5, 100) }}"}]}, "nodeCredentialType": "spotifyOAuth2Api"}, "credentials": {"spotifyOAuth2Api": {"id": "4AquI6TQMHILfmvx", "name": "Spotify App Credentials"}}, "typeVersion": 4.1, "alwaysOutputData": true}, {"id": "a75cfc9b-ba21-4771-a2ff-f7aee843f344", "name": "Spotify API - Volume down 5pct", "type": "n8n-nodes-base.httpRequest", "position": [1300, 300], "parameters": {"url": "https://api.spotify.com/v1/me/player/volume", "method": "PUT", "options": {}, "sendQuery": true, "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "device_id", "value": "={{ $json.device.id }}"}, {"name": "volume_percent", "value": "={{ Math.max($json.device.volume_percent - 5, 20) }}"}]}, "nodeCredentialType": "spotifyOAuth2Api"}, "credentials": {"spotifyOAuth2Api": {"id": "4AquI6TQMHILfmvx", "name": "Spotify App Credentials"}}, "typeVersion": 4.1, "alwaysOutputData": true}, {"id": "deae216d-aaaa-406c-b978-45b790c5d837", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [-360, 608.2274931489221], "parameters": {"color": 5, "width": 906.3175117167951, "height": 278.70214810442735, "content": "### Find the target player device (or spotify device group) by name, and activate it."}, "typeVersion": 1}, {"id": "2733fd1e-4c58-4f3e-bf7d-f4111fea6efc", "name": "Spotify API - Get Available Devices", "type": "n8n-nodes-base.httpRequest", "position": [-260, 680], "parameters": {"url": "https://api.spotify.com/v1/me/player/devices", "options": {}, "sendQuery": true, "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{}]}, "nodeCredentialType": "spotifyOAuth2Api"}, "credentials": {"spotifyOAuth2Api": {"id": "4AquI6TQMHILfmvx", "name": "Spotify App Credentials"}}, "typeVersion": 4.1, "alwaysOutputData": true}, {"id": "1d7fcab5-e49d-4d03-8e7d-aa339afa45ec", "name": "Extract Individual Devices", "type": "n8n-nodes-base.splitOut", "position": [-60, 680], "parameters": {"options": {}, "fieldToSplitOut": "devices"}, "typeVersion": 1}, {"id": "7ae0af1c-2fbb-47e4-b2ab-670be441d86f", "name": "Select Device by Name to get device_id", "type": "n8n-nodes-base.filter", "position": [140, 680], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "069d11c8-75a2-4a5c-81c4-e6f771ee4829", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.name }}", "rightValue": "={{ $('Globals').first().json.target_spotify_playback_device_name }}"}]}}, "typeVersion": 2}, {"id": "738d57fd-9dcb-4d3d-b070-73867c926d3f", "name": "Custom Function 1 - P1", "type": "n8n-nodes-base.httpRequest", "position": [880, 840], "parameters": {"url": "https://api.spotify.com/v1/me/player/volume", "method": "PUT", "options": {}, "sendQuery": true, "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "volume_percent", "value": "80"}]}, "nodeCredentialType": "spotifyOAuth2Api"}, "credentials": {"spotifyOAuth2Api": {"id": "4AquI6TQMHILfmvx", "name": "Spotify App Credentials"}}, "typeVersion": 4.1, "alwaysOutputData": true}, {"id": "e3fc6784-1612-427d-9b78-a3f4050ed176", "name": "Custom Function 2 - P2", "type": "n8n-nodes-base.spotify", "position": [1080, 840], "parameters": {"id": "spotify:track:4PTG3Z6ehGkBFwjybzWkR8"}, "credentials": {"spotifyOAuth2Api": {"id": "4AquI6TQMHILfmvx", "name": "Spotify App Credentials"}}, "typeVersion": 1}, {"id": "340f23ff-ae8a-4032-a641-30bc32af09c7", "name": "Custom Function 1 - P3", "type": "n8n-nodes-base.spotify", "position": [1300, 840], "parameters": {"operation": "nextSong"}, "credentials": {"spotifyOAuth2Api": {"id": "4AquI6TQMHILfmvx", "name": "Spotify App Credentials"}}, "typeVersion": 1}, {"id": "30a413da-5ce3-44a9-a43e-c6679b712087", "name": "Spotify API - Activate Target Playback Device", "type": "n8n-nodes-base.httpRequest", "position": [360, 680], "parameters": {"url": "https://api.spotify.com/v1/me/player", "method": "PUT", "options": {}, "jsonBody": "={\n  \"device_ids\": [\n    \"{{ $('Select Device by Name to get device_id').first().json.id }}\"\n  ],\n  \"play\": true\n}", "sendBody": true, "sendQuery": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{}]}, "nodeCredentialType": "spotifyOAuth2Api"}, "credentials": {"spotifyOAuth2Api": {"id": "4AquI6TQMHILfmvx", "name": "Spotify App Credentials"}}, "typeVersion": 4.1, "alwaysOutputData": true}, {"id": "91dd48fe-6c3c-4170-8392-3d9885e61047", "name": "Route to Requested Function", "type": "n8n-nodes-base.switch", "position": [900, 420], "parameters": {"rules": {"values": [{"outputKey": "=volume_up", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $('MQTT Trigger - Ikea Remote Switch').first().json.message }}", "rightValue": "brightness_up_click"}]}, "renameOutput": true}, {"outputKey": "volume_down", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "c0726ee0-31b2-48fd-a860-0b923d8c18e7", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('MQTT Trigger - Ikea Remote Switch').first().json.message }}", "rightValue": "brightness_down_click"}]}, "renameOutput": true}, {"outputKey": "play/pause", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "569014d8-0db4-4126-a0dd-7264a3b6db51", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('MQTT Trigger - Ikea Remote Switch').first().json.message }}", "rightValue": "toggle"}]}, "renameOutput": true}, {"outputKey": "custom_function_1", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "af6d07f3-0ac2-4c05-8535-26d618892b8b", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('MQTT Trigger - Ikea Remote Switch').first().json.message }}", "rightValue": "brightness_up_hold"}]}, "renameOutput": true}, {"outputKey": "custom_function_2", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a5e8ce30-4b18-450a-8b15-342a698fec61", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('MQTT Trigger - Ikea Remote Switch').first().json.message }}", "rightValue": "brightness_down_hold"}]}, "renameOutput": true}]}, "options": {"fallbackOutput": "extra"}}, "typeVersion": 3}, {"id": "4a9ddd4d-ae2d-43c8-b3fd-70a2b15c5743", "name": "Custom Function 2 - P1", "type": "n8n-nodes-base.spotify", "position": [840, 1020], "parameters": {"resource": "playlist", "operation": "getUserPlaylists", "returnAll": true}, "credentials": {"spotifyOAuth2Api": {"id": "4AquI6TQMHILfmvx", "name": "Spotify App Credentials"}}, "typeVersion": 1}, {"id": "29f842ad-f7cb-47b5-81fe-349f193e54bb", "name": "Filter", "type": "n8n-nodes-base.filter", "position": [1040, 1020], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "f7b6844a-ad78-4f29-801b-cef817a42e94", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.name }}", "rightValue": "={{ $('Globals').first().json.favorite_playlist_name }}"}]}}, "typeVersion": 2}, {"id": "cb038583-e930-4fee-9166-fa182d20868e", "name": "Globals", "type": "n8n-nodes-base.set", "position": [-100, 160], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "2124b4d2-f929-459d-b285-3ac18df3ab60", "name": "target_spotify_playback_device_name", "type": "string", "value": "My Smart-Speaker Playback <PERSON><PERSON>"}, {"id": "b7f0468d-c5c3-4424-8db8-af823a10c7f0", "name": "favorite_playlist_name", "type": "string", "value": "Discover Weekly"}]}}, "typeVersion": 3.4}, {"id": "f4505b9a-6f04-4ff7-9384-05bae95b2fc8", "name": "Custom Function 2 - P3", "type": "n8n-nodes-base.spotify", "position": [1260, 1020], "parameters": {"id": "=spotify:playlist:{{ $json.id }}", "operation": "startMusic"}, "credentials": {"spotifyOAuth2Api": {"id": "4AquI6TQMHILfmvx", "name": "Spotify App Credentials"}}, "typeVersion": 1}, {"id": "2b2a81f9-69b6-42ca-accc-d4d987d6823c", "name": "Oops. How was this reached?", "type": "n8n-nodes-base.noOp", "position": [1080, 680], "parameters": {}, "typeVersion": 1}, {"id": "e45f04d6-bb3c-4580-9ef4-307d3692ad29", "name": "Spotify API - Get Device Status", "type": "n8n-nodes-base.httpRequest", "position": [440, 260], "parameters": {"url": "https://api.spotify.com/v1/me/player", "options": {}, "sendQuery": true, "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "device_id", "value": "={{ $('Globals').first().json.target_spotify_playback_device_id }}"}]}, "nodeCredentialType": "spotifyOAuth2Api"}, "credentials": {"spotifyOAuth2Api": {"id": "4AquI6TQMHILfmvx", "name": "Spotify App Credentials"}}, "typeVersion": 4.1, "alwaysOutputData": true}, {"id": "ffa38f10-f9b4-4a52-954e-39adcd924633", "name": "Already playing on Target Device?", "type": "n8n-nodes-base.if", "position": [640, 300], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "92d0f5fc-0743-4ea7-aad3-c8b72481bb97", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Spotify API - Get Device Status').first().json.device.name }}", "rightValue": "={{ $('Globals').first().json.target_spotify_playback_device_name }}"}]}}, "typeVersion": 2}, {"id": "f92ed1ea-c9a7-4818-9a8e-a5fa460177ea", "name": "Is Playing?", "type": "n8n-nodes-base.if", "position": [1140, 520], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "caa4edf5-6436-4416-92f7-febd63cd47c5", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $('Spotify API - Get Device Status').first().json.is_playing && !$('Spotify API - Activate Target Playback Device').isExecuted }}", "rightValue": ""}]}}, "typeVersion": 2}, {"id": "5580215d-0f20-4349-a7c2-b74f0e01080e", "name": "Spotify Prev", "type": "n8n-nodes-base.spotify", "position": [1300, -200], "parameters": {"operation": "previousSong"}, "credentials": {"spotifyOAuth2Api": {"id": "4AquI6TQMHILfmvx", "name": "Spotify App Credentials"}}, "typeVersion": 1}, {"id": "f3babdeb-86d4-4dc6-85a4-95c48f4f07ef", "name": "MQTT Trigger - Remote Switch", "type": "n8n-nodes-base.mqttTrigger", "position": [-280, 160], "parameters": {"topics": "zigbee2mqtt/MyIOTButton1234/action", "options": {"jsonParseBody": true}}, "credentials": {"mqtt": {"id": "65ppR4lt7hVNzfVG", "name": "MQTT account"}}, "typeVersion": 1}, {"id": "0c1bd86f-49f7-4505-9c8a-047dcb10a1bd", "name": "Remote Action -> Function Router", "type": "n8n-nodes-base.switch", "position": [100, 140], "parameters": {"rules": {"rules": [{"value2": "arrow_left_click", "outputKey": "left"}, {"value2": "arrow_right_click", "outputKey": "right"}, {"value2": "brightness_up_click", "outputKey": "up"}, {"value2": "brightness_down_click", "outputKey": "down"}, {"value2": "toggle", "outputKey": "on_off"}, {"value2": "brightness_up_hold", "outputKey": "custom_function_1"}, {"value2": "brightness_down_hold", "outputKey": "custom_function_2"}]}, "value1": "={{ $('MQTT Trigger - Ikea Remote Switch').first().json.message }}", "dataType": "string"}, "typeVersion": 2}], "pinData": {}, "connections": {"Filter": {"main": [[{"node": "Custom Function 2 - P3", "type": "main", "index": 0}]]}, "Globals": {"main": [[{"node": "Remote Action -> Function Router", "type": "main", "index": 0}]]}, "Is Playing?": {"main": [[{"node": "Spotify Pause", "type": "main", "index": 0}], [{"node": "Spotify Resume", "type": "main", "index": 0}]]}, "Custom Function 1 - P1": {"main": [[{"node": "Custom Function 2 - P2", "type": "main", "index": 0}]]}, "Custom Function 2 - P1": {"main": [[{"node": "Filter", "type": "main", "index": 0}]]}, "Custom Function 2 - P2": {"main": [[{"node": "Custom Function 1 - P3", "type": "main", "index": 0}]]}, "Extract Individual Devices": {"main": [[{"node": "Select Device by Name to get device_id", "type": "main", "index": 0}]]}, "Route to Requested Function": {"main": [[{"node": "Spotify API - Volume up 5pct", "type": "main", "index": 0}], [{"node": "Spotify API - Volume down 5pct", "type": "main", "index": 0}], [{"node": "Is Playing?", "type": "main", "index": 0}], [{"node": "Custom Function 1 - P1", "type": "main", "index": 0}], [{"node": "Custom Function 2 - P1", "type": "main", "index": 0}], [{"node": "Oops. How was this reached?", "type": "main", "index": 0}]]}, "Spotify API - Get Device Status": {"main": [[{"node": "Already playing on Target Device?", "type": "main", "index": 0}]]}, "Remote Action -> Function Router": {"main": [[{"node": "Spotify Prev", "type": "main", "index": 0}], [{"node": "Spotify Next", "type": "main", "index": 0}], [{"node": "Spotify API - Get Device Status", "type": "main", "index": 0}], [{"node": "Spotify API - Get Device Status", "type": "main", "index": 0}], [{"node": "Spotify API - Get Device Status", "type": "main", "index": 0}], [{"node": "Spotify API - Get Device Status", "type": "main", "index": 0}], [{"node": "Spotify API - Get Device Status", "type": "main", "index": 0}]]}, "Already playing on Target Device?": {"main": [[{"node": "Route to Requested Function", "type": "main", "index": 0}], [{"node": "Spotify API - Get Available Devices", "type": "main", "index": 0}]]}, "MQTT Trigger - Ikea Remote Switch": {"main": [[{"node": "Globals", "type": "main", "index": 0}]]}, "Spotify API - Get Available Devices": {"main": [[{"node": "Extract Individual Devices", "type": "main", "index": 0}]]}, "Select Device by Name to get device_id": {"main": [[{"node": "Spotify API - Activate Target Playback Device", "type": "main", "index": 0}]]}, "Spotify API - Activate Target Playback Device": {"main": [[{"node": "Route to Requested Function", "type": "main", "index": 0}]]}}}