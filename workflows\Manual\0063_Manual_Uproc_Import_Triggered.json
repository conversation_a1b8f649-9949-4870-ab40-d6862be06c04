{"id": "113", "name": "Get DNS entries", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [240, 290], "parameters": {}, "typeVersion": 1}, {"name": "Create Domain Item", "type": "n8n-nodes-base.functionItem", "position": [450, 290], "parameters": {"functionCode": "item.domain = \"n8n.io\";\nreturn item;"}, "typeVersion": 1}, {"name": "Get DNS records", "type": "n8n-nodes-base.uproc", "position": [650, 290], "parameters": {"tool": "getDomainRecords", "group": "internet", "domain": "= {{$node[\"Create Domain Item\"].json[\"domain\"]}}", "additionalOptions": {}}, "credentials": {"uprocApi": "miquel-uproc"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Create Domain Item": {"main": [[{"node": "Get DNS records", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Create Domain Item", "type": "main", "index": 0}]]}}}