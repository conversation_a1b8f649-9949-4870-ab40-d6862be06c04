{"id": 23, "name": "Zendesk-to-slack", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [360, 350], "parameters": {}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "color": "#068906", "position": [360, 560], "parameters": {"triggerTimes": {"item": [{"hour": 16, "minute": 30}]}}, "typeVersion": 1}, {"name": "Function", "type": "n8n-nodes-base.function", "position": [690, 460], "parameters": {"functionCode": "// Create our Slack message\n// This will output a list of Ticket URLs with the status and the subject\n// 12345 [STATUS] - Ticket Subject\nlet message = \"*Unassigned Tickets*\\n\\n\";\n\n// Loop the input items\nfor (item of items) {\n  // Append the ticket information to the message\n  message += \"*<\" + item.json.url.replace(\"api/v2\",\"agent\").replace(\".json\",\"\") + \"|\" + item.json.id + \">* [\" + item.json.status.toUpperCase() + \"] - \" + item.json.subject + \"\\n\"; \n}\n\n// Return our message\nreturn [{json: {message}}];"}, "typeVersion": 1}, {"name": "<PERSON><PERSON>ck", "type": "n8n-nodes-base.slack", "position": [870, 460], "parameters": {"text": "={{$json[\"message\"]}}", "channel": "jarvis-test", "attachments": [], "otherOptions": {}}, "credentials": {"slackApi": {"id": "2", "name": "<PERSON><PERSON>ck"}}, "typeVersion": 1}, {"name": "Zendesk", "type": "n8n-nodes-base.zendesk", "position": [510, 460], "parameters": {"options": {"query": "assignee:none status<pending"}, "operation": "getAll", "returnAll": true}, "credentials": {"zendeskApi": {"id": "1", "name": "Zendesk"}}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Cron": {"main": [[{"node": "Zendesk", "type": "main", "index": 0}]]}, "Zendesk": {"main": [[{"node": "Function", "type": "main", "index": 0}]]}, "Function": {"main": [[{"node": "<PERSON><PERSON>ck", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Zendesk", "type": "main", "index": 0}]]}}}