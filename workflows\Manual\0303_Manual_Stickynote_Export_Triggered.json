{"meta": {"instanceId": "dfdeafd1c3ed2ee08eeab8c2fa0c3f522066931ed8138ccd35dc20a1e69decd3"}, "nodes": [{"id": "4e670880-61cf-4870-8d29-525f4e677162", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [-40, 600], "parameters": {}, "typeVersion": 1}, {"id": "cd21e063-59fe-42a5-87c7-b4d63df2e2b7", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [500, 480], "parameters": {"width": 682, "height": 280, "content": "## Save SQL table as a CSV file\n### You can send it via e-mail, upload to the file storage or download on your computer.\n### Just connect one or two extra n8n Nodes here!"}, "typeVersion": 1}, {"id": "f960451e-d04e-4023-aed2-e039898b7cab", "name": "TableName", "type": "n8n-nodes-base.set", "position": [160, 600], "parameters": {"values": {"string": [{"name": "TableName", "value": "SalesLT.ProductCategory"}]}, "options": {}}, "typeVersion": 1}, {"id": "e2b4f557-663e-4b1c-b90e-9fde44dcd63a", "name": "LoadMSSQLData", "type": "n8n-nodes-base.microsoftSql", "position": [340, 600], "parameters": {"query": "=SELECT * FROM {{ $json[\"TableName\"] }}", "operation": "execute<PERSON>uery"}, "credentials": {"microsoftSql": {"id": "69", "name": "Microsoft SQL account"}}, "typeVersion": 1}, {"id": "cec2452f-e3e9-47ad-bcc6-4d411b1cd532", "name": "SaveCSV", "type": "n8n-nodes-base.spreadsheetFile", "position": [760, 600], "parameters": {"options": {"fileName": "={{ $('TableName').first().json.TableName }}.{{ $parameter[\"fileFormat\"] }}"}, "operation": "toFile", "fileFormat": "csv"}, "typeVersion": 1}], "connections": {"TableName": {"main": [[{"node": "LoadMSSQLData", "type": "main", "index": 0}]]}, "LoadMSSQLData": {"main": [[{"node": "SaveCSV", "type": "main", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "TableName", "type": "main", "index": 0}]]}}}