{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 200], "parameters": {}, "typeVersion": 1}, {"name": "Microsoft To Do", "type": "n8n-nodes-base.microsoftToDo", "position": [450, 200], "parameters": {"title": "Document Microsoft To Do node", "operation": "create", "taskListId": "AQMkADAwATNiZmYAZC0zOTkAMy02ZWZjLTAwAi0wMAoALgAAA3i1fHMTrftIhQBzhywL64UBAFB0wRiJW1FJmmlvlAkVFQA-AAACARIAAAA=", "additionalFields": {"importance": "high"}}, "credentials": {"microsoftToDoOAuth2Api": "Microsoft OAuth Credentials"}, "typeVersion": 1}, {"name": "Microsoft To Do1", "type": "n8n-nodes-base.microsoftToDo", "position": [650, 200], "parameters": {"taskId": "={{$json[\"id\"]}}", "operation": "update", "taskListId": "={{$node[\"Microsoft To Do\"].parameter[\"taskListId\"]}}", "updateFields": {"status": "inProgress"}}, "credentials": {"microsoftToDoOAuth2Api": "Microsoft OAuth Credentials"}, "typeVersion": 1}, {"name": "Microsoft To Do2", "type": "n8n-nodes-base.microsoftToDo", "position": [850, 200], "parameters": {"taskId": "={{$json[\"id\"]}}", "taskListId": "={{$node[\"Microsoft To Do\"].parameter[\"taskListId\"]}}"}, "credentials": {"microsoftToDoOAuth2Api": "Microsoft OAuth Credentials"}, "typeVersion": 1}], "connections": {"Microsoft To Do": {"main": [[{"node": "Microsoft To Do1", "type": "main", "index": 0}]]}, "Microsoft To Do1": {"main": [[{"node": "Microsoft To Do2", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Microsoft To Do", "type": "main", "index": 0}]]}}}