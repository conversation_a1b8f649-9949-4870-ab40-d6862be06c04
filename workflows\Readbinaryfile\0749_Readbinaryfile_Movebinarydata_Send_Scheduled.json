{"id": "2", "name": "SIGNL4 Alert", "nodes": [{"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [350, 500], "parameters": {"triggerTimes": {"item": [{"mode": "everyHour"}]}}, "typeVersion": 1}, {"name": "Write Binary File", "type": "n8n-nodes-base.writeBinaryFile", "position": [880, 500], "parameters": {"fileName": "alert-data.json"}, "typeVersion": 1}, {"name": "Read Binary File", "type": "n8n-nodes-base.readBinaryFile", "position": [450, 270], "parameters": {"filePath": "alert-data.json"}, "typeVersion": 1}, {"name": "Binary to JSON", "type": "n8n-nodes-base.moveBinaryData", "position": [630, 270], "parameters": {"options": {}}, "typeVersion": 1}, {"name": "JSON to Binary", "type": "n8n-nodes-base.moveBinaryData", "position": [720, 500], "parameters": {"mode": "jsonToBinary", "options": {}}, "typeVersion": 1}, {"name": "<PERSON> as <PERSON>", "type": "n8n-nodes-base.function", "position": [560, 500], "parameters": {"functionCode": "items[0].json.Body = $node[\"Binary to JSON\"].json.Body;\nitems[0].json.Done = true;\nitems[0].json.eventId = $node[\"SIGNL4 Alert\"].json.eventId;\nitems[0].json.lastId = $node[\"Binary to JSON\"].json.eventId;\n\nreturn items;"}, "typeVersion": 1}, {"name": "IF", "type": "n8n-nodes-base.if", "position": [810, 270], "parameters": {"conditions": {"boolean": [{"value1": "={{$node[\"Binary to JSO<PERSON>\"].json[\"Done\"]}}"}]}, "combineOperation": "=all"}, "typeVersion": 1}, {"name": "SIGNL4 Resolve", "type": "n8n-nodes-base.signl4", "position": [1040, 500], "parameters": {"operation": "resolve", "externalId": "={{$node[\"Binary to JSON\"].json[\"lastId\"]}}"}, "credentials": {"signl4Api": "Team"}, "typeVersion": 1}, {"name": "SIGNL4 Alert", "type": "n8n-nodes-base.signl4", "position": [990, 270], "parameters": {"message": "={{$node[\"Binary to JSON\"].json[\"Body\"]}}", "additionalFields": {"externalId": "={{$node[\"Binary to JSON\"].json[\"eventId\"]}}", "locationFieldsUi": {"locationFieldsValues": {"latitude": "52.3984235", "longitude": "13.0544149"}}}}, "credentials": {"signl4Api": "Team"}, "typeVersion": 1}], "active": true, "settings": {"timezone": "Europe/Berlin"}, "connections": {"IF": {"main": [[{"node": "SIGNL4 Alert", "type": "main", "index": 0}], [{"node": "SIGNL4 Resolve", "type": "main", "index": 0}]]}, "Cron": {"main": [[{"node": "Read Binary File", "type": "main", "index": 0}]]}, "Mark as Done": {"main": [[{"node": "JSON to Binary", "type": "main", "index": 0}]]}, "SIGNL4 Alert": {"main": [[{"node": "<PERSON> as <PERSON>", "type": "main", "index": 0}]]}, "Binary to JSON": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}, "JSON to Binary": {"main": [[{"node": "Write Binary File", "type": "main", "index": 0}]]}, "Read Binary File": {"main": [[{"node": "Binary to JSON", "type": "main", "index": 0}]]}}}