{"id": "7Qa2mH7PnDxy7Qat", "meta": {"instanceId": "a4bfc93e975ca233ac45ed7c9227d84cf5a2329310525917adaf3312e10d5462", "templateCredsSetupCompleted": true}, "name": "Generate Exam Questions", "tags": [], "nodes": [{"id": "4e037d6e-93a9-4c1b-b84a-dbbcf77beaf5", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-740, 120], "parameters": {}, "typeVersion": 1}, {"id": "febc8bb7-5de7-46d6-bc23-54673089cd3d", "name": "Qdrant Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "position": [900, 240], "parameters": {"mode": "insert", "options": {}, "qdrantCollection": {"__rl": true, "mode": "list", "value": "ai_article_test", "cachedResultName": "ai_article_test"}}, "credentials": {"qdrantApi": {"id": "iyQ6MQiVaF3VMBmt", "name": "QdrantApi account (<PERSON><PERSON><PERSON>)"}}, "typeVersion": 1}, {"id": "2d7e2673-6559-49b3-9ed0-29ca2c376f00", "name": "Create collection", "type": "n8n-nodes-base.httpRequest", "position": [-440, -20], "parameters": {"url": "http://QDRANT_URL/collections/COLLECTIONS", "method": "PUT", "options": {}, "jsonBody": "{\n  \"vectors\": {\n    \"size\": 1536,\n    \"distance\": \"Cosine\"  \n  },\n  \"shard_number\": 1,  \n  \"replication_factor\": 1,  \n  \"write_consistency_factor\": 1 \n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "credentials": {"httpHeaderAuth": {"id": "qhny6r5ql9wwotpn", "name": "Qdrant API (Hetzner)"}}, "typeVersion": 4.2}, {"id": "615f26b2-930c-4b74-a35c-00b83460a7c9", "name": "Refresh collection", "type": "n8n-nodes-base.httpRequest", "position": [-440, 240], "parameters": {"url": "http://QDRANT_URL/collections/COLLECTIONS/points/delete", "method": "POST", "options": {}, "jsonBody": "{\n  \"filter\": {}\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "credentials": {"httpHeaderAuth": {"id": "qhny6r5ql9wwotpn", "name": "Qdrant API (Hetzner)"}}, "typeVersion": 4.2}, {"id": "eb34b8dd-353b-41c4-8a02-6565c3f8a7d3", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [820, 440], "parameters": {"options": {"stripNewLines": false}}, "credentials": {"openAiApi": {"id": "4zwP0MSr8zkNvvV9", "name": "OpenAi account"}}, "typeVersion": 1.1}, {"id": "fb639802-e099-4857-823b-5e6d89fb3e86", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [1080, 460], "parameters": {"loader": "text<PERSON><PERSON><PERSON>", "options": {}, "dataType": "binary"}, "typeVersion": 1}, {"id": "0af5028d-56a4-4bbc-8af0-f088e54f178b", "name": "Token Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterTokenSplitter", "position": [1040, 640], "parameters": {"chunkSize": 450, "chunkOverlap": 50}, "typeVersion": 1}, {"id": "6a10192e-4b2e-4705-865a-fa90328ba3c1", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-240, -80], "parameters": {"color": 6, "width": 880, "height": 220, "content": "# STEP 1\n\n## Create Qdrant Collection\nChange:\n- QDRANTURL\n- COLLECTION"}, "typeVersion": 1}, {"id": "1ebefe44-e5c9-43fb-b9fa-fee47b08e2c2", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-460, 180], "parameters": {"color": 4, "width": 620, "height": 400, "content": "# STEP 2\n\n\n\n\n\n\n\n\n\n\n\n\n## Documents vectorization with Qdrant and Google Drive\nChange:\n- QDRANTURL\n- COLLECTION"}, "typeVersion": 1}, {"id": "88f816ae-4331-46e0-b1f9-636ec94e8bb3", "name": "<PERSON><PERSON><PERSON> di MD", "type": "n8n-nodes-base.code", "position": [240, 240], "parameters": {"jsCode": "function convertToMarkdown(docContent) {\n  let markdown = '';\n\n  const headingMap = {\n    'HEADING_1': '#',\n    'HEADING_2': '##',\n    'HEADING_3': '###',\n    'HEADING_4': '####',\n    'HEADING_5': '#####',\n    'HEADING_6': '######',\n  };\n\n  for (const element of docContent.body.content) {\n    if (!element.paragraph) continue;\n\n    const para = element.paragraph;\n    let line = '';\n\n    // Tipo di paragrafo (normale o heading)\n    const style = para.paragraphStyle?.namedStyleType;\n    const prefix = headingMap[style] || '';\n\n    for (const el of para.elements) {\n      if (!el.textRun) continue;\n\n      let text = el.textRun.content || '';\n      const style = el.textRun.textStyle || {};\n\n      if (style.bold) text = `**${text.trim()}**`;\n      if (style.italic) text = `*${text.trim()}*`;\n      if (!style.bold && !style.italic) text = text.trim();\n\n      line += text;\n    }\n\n    if (prefix) {\n      markdown += `${prefix} ${line}\\n\\n`;\n    } else {\n      markdown += `${line}\\n\\n`;\n    }\n  }\n\n  return markdown.trim();\n}\n\n// Assumiamo che il JSON completo sia in items[0].json\nconst docJson = items[0].json;\nconst markdown = convertToMarkdown(docJson);\n\nreturn [\n  {\n    json: {\n      markdown,\n    },\n  },\n];"}, "typeVersion": 2}, {"id": "5c733b2d-3d0a-4260-af88-7907907e209f", "name": "Get <PERSON>", "type": "n8n-nodes-base.googleDocs", "position": [-60, 240], "parameters": {"simple": false, "operation": "get", "documentURL": "XXXXXXXXXXXXXXXX"}, "credentials": {"googleDocsOAuth2Api": {"id": "LpmDV1ry0BPLvW8b", "name": "Google Docs account"}}, "typeVersion": 2}, {"id": "5de82976-2376-4201-a5a4-dbdd6bfcb596", "name": "Vector Store Retriever", "type": "@n8n/n8n-nodes-langchain.retrieverVectorStore", "position": [1540, 1040], "parameters": {}, "typeVersion": 1}, {"id": "25bcb865-7b15-4272-81da-4ff41a4ccc60", "name": "Qdrant Vector Store1", "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "position": [1440, 1180], "parameters": {"options": {}, "qdrantCollection": {"__rl": true, "mode": "list", "value": "ai_article_test", "cachedResultName": "ai_article_test"}}, "credentials": {"qdrantApi": {"id": "iyQ6MQiVaF3VMBmt", "name": "QdrantApi account (<PERSON><PERSON><PERSON>)"}}, "typeVersion": 1.1}, {"id": "7dacd3ac-2d25-4960-ba53-e44ae9722dca", "name": "Convert to File", "type": "n8n-nodes-base.convertToFile", "position": [560, 240], "parameters": {"options": {}, "operation": "toText", "sourceProperty": "markdown"}, "typeVersion": 1.1}, {"id": "9d7561f0-5b01-4327-ab62-68a105364155", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [540, 980], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "0p34rXqIqy8WuoPg", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "4f63e896-45b1-484f-9fa1-0b488691023a", "name": "Item List Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserItemList", "position": [740, 1000], "parameters": {"options": {"numberOfItems": 10}}, "typeVersion": 1}, {"id": "911e8654-dfef-4d4f-b1c8-247fe0091381", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [1100, 780], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "987e13f8-f8c9-4bc1-9e4f-d11a5f8af4d7", "name": "Google Gemini Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1360, 1020], "parameters": {"options": {}, "modelName": "models/gemini-2.0-pro-exp"}, "credentials": {"googlePalmApi": {"id": "0p34rXqIqy8WuoPg", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "c2f70831-4d5d-403b-b92d-af82205cbbdc", "name": "Google Gemini Chat Model2", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [520, 1720], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "0p34rXqIqy8WuoPg", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "2f4ca583-8005-4e26-88df-ffebdc2be2f6", "name": "Item List Output Parser1", "type": "@n8n/n8n-nodes-langchain.outputParserItemList", "position": [760, 1720], "parameters": {"options": {"numberOfItems": 10}}, "typeVersion": 1}, {"id": "cacecdab-2f1c-4730-a7c5-d46dca32969c", "name": "Loop Over Items1", "type": "n8n-nodes-base.splitInBatches", "position": [1080, 1540], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "2de66223-475c-4fef-aa85-13e954a5c1cc", "name": "Google Gemini Chat Model3", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1320, 1840], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "0p34rXqIqy8WuoPg", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "********-369c-477d-beee-ece1916aebb7", "name": "Qdrant Vector Store2", "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "position": [1380, 2020], "parameters": {"options": {}, "qdrantCollection": {"__rl": true, "mode": "list", "value": "ai_article_test", "cachedResultName": "ai_article_test"}}, "credentials": {"qdrantApi": {"id": "iyQ6MQiVaF3VMBmt", "name": "QdrantApi account (<PERSON><PERSON><PERSON>)"}}, "typeVersion": 1.1}, {"id": "27dddcae-e20a-41a9-879e-ce8ae8a0347f", "name": "Embeddings OpenAI2", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [1360, 2200], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "4zwP0MSr8zkNvvV9", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "37d164a7-94aa-4273-b91a-8b22684a45fd", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [1820, 1820], "parameters": {"schemaType": "manual", "inputSchema": "{\n\t\"type\": \"object\",\n\t\"properties\": {\n\t\t\"correct\": {\n\t\t\t\"type\": \"string\"\n\t\t},\n\t\t\"answers\": {\n\t\t\t\"type\": \"array\",\n\t\t\t\"items\": {\n\t\t\t\t\"type\": \"string\"\n\t\t\t}\n\t\t}\n\t}\n}"}, "typeVersion": 1.2}, {"id": "42d627b5-c033-4b2e-8ea4-fe704601b3d6", "name": "RAG", "type": "@n8n/n8n-nodes-langchain.toolVectorStore", "position": [1500, 1820], "parameters": {"description": "In base alla domanda consulta il database vettoriale ed estrapola la risposta corretta. Elabora anche altre 3 risposte non corrette."}, "typeVersion": 1.1}, {"id": "ce763ef2-eb54-484b-8046-7bc008012ec5", "name": "Google Gemini Chat Model4", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1700, 1980], "parameters": {"options": {}, "modelName": "models/gemini-2.0-pro-exp"}, "credentials": {"googlePalmApi": {"id": "0p34rXqIqy8WuoPg", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "076994e8-0326-424e-a5c3-3d07958af0af", "name": "Open questions", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [560, 780], "parameters": {"text": "=Article:\n'''\n{{ $json.markdown }}\n'''", "messages": {"messageValues": [{"message": "=## Purpose\nYou are a specialized AI designed to analyze articles and create challenging questions that test comprehension and knowledge retention. Your task is to generate questions that encourage critical thinking about the article's content.\n\n## Input\nThe input will be a text article on any subject. This could be academic, news, technical, or general interest content.\n\n## Output Requirements\n- Create exactly 10 questions based on the article content\n- DO NOT number the questions\n- Questions should cover key facts, concepts, and implications from the article\n- Include a mix of question types:\n  - Factual recall questions\n  - Inference questions that require reading between the lines\n  - Application questions that ask how concepts might be applied\n  - Analysis questions that probe deeper understanding\n  - Questions about relationships between different parts of the article\n- Questions should vary in difficulty level\n- Avoid creating questions with simple yes/no answers\n- Ensure questions are clearly worded and unambiguous\n- Questions should test genuine understanding rather than trivial details\n\n## Output Format\n- Present each question as a separate paragraph\n- Do not include answers\n- Do not include numbering or bullet points\n- Do not include any introductory text\n- Do not include any explanatory notes\n\n## Behavior Guidelines\n- Focus on the most significant and meaningful content in the article\n- Ensure questions thoroughly cover the entire article, not just the beginning\n- If the article contains technical terms, create questions that test understanding of these terms\n- If the article presents contrasting viewpoints, create questions about both perspectives\n- Maintain neutrality - do not frame questions that suggest a particular stance\n- If the article is highly specialized, adjust question complexity accordingly\n- Do not create questions about information not contained in the article\n- If the article is in a language other than English, generate questions in the same language\n\n## Examples of Good Questions\n- How does the author's description of X relate to the concept of Y discussed later in the article?\n- What evidence does the article provide to support the claim that X leads to Y?\n- How might the framework described in the article be applied to solve similar problems in different contexts?\n- What underlying assumptions inform the author's perspective on this issue?\n- In what ways does the article suggest the relationship between X and Y has evolved over time?"}]}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.6}, {"id": "5df02a14-175f-4923-9a2f-ad4514f98c71", "name": "Closed questions", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [560, 1540], "parameters": {"text": "=Article:\n'''\n{{ $json.markdown }}\n'''", "messages": {"messageValues": [{"message": "=## Purpose\nYou are a specialized AI designed to analyze articles and create high-quality multiple-choice questions that effectively test knowledge comprehension and retention. Your task is to generate questions with appropriate answer options that accurately assess understanding of the article's content.\n\n## Input\nThe input will be a text article on any subject. This could be academic, news, technical, or general interest content.\n\n## Output Requirements\n- Create exactly 10 multiple-choice questions based on the article content\n- DO NOT number the questions\n- Each question must include:\n  - A clear question stem\n  - Four answer options (labeled A, B, C, D)\n  - One correct answer and three plausible distractors\n- Questions should cover key facts, concepts, and implications from the article\n- Include a mix of question types:\n  - Factual recall questions\n  - Inference questions requiring deeper understanding\n  - Application questions testing practical knowledge\n  - Analysis questions examining relationships between concepts\n- Questions should vary in difficulty level\n- Ensure questions are clearly worded and unambiguous\n- Distractors should be plausible but clearly incorrect upon careful reading of the article\n\n## Output Format\n- Present each question as a separate paragraph\n- Format each question as:\n  [Question]\n  A. [Option A]\n  B. [Option B]\n  C. [Option C]\n  D. [Option D]\n- Do not indicate which answer is correct in the output\n- Do not include any introductory text\n- Do not include any explanatory notes\n- Do not include numbering for questions\n\n## Behavior Guidelines\n- Focus on the most significant and meaningful content in the article\n- Ensure questions thoroughly cover the entire article, not just the beginning\n- Make all answer options approximately the same length\n- Avoid using absolute terms like \"always\" or \"never\" in the options\n- Avoid grammatical clues that hint at the correct answer\n- Make distractors plausible by:\n  - Using common misconceptions\n  - Including partially correct information\n  - Using correct information from the wrong context\n- If the article contains technical terms, create questions that test understanding of these terms\n- If the article presents contrasting viewpoints, create questions about both perspectives\n- Maintain neutrality - do not frame questions that suggest a particular stance\n- If the article is in a language other than English, generate questions in the same language\n\n## Examples of Good Multiple-Choice Questions\n- What is the primary factor contributing to the phenomenon described in the article?\n  A. [Plausible but incorrect factor]\n  B. [Correct factor from article]\n  C. [Plausible but incorrect factor]\n  D. [Plausible but incorrect factor]\n\n- According to the article, how does [concept X] impact [concept Y]?\n  A. [Correct relationship described in article]\n  B. [Plausible but incorrect relationship]\n  C. [Plausible but incorrect relationship]\n  D. [Plausible but incorrect relationship]\n\n- Which application of the described technology would align with the principles outlined in the article?\n  A. [Plausible but incorrect application]\n  B. [Plausible but incorrect application]\n  C. [Correct application based on article]\n  D. [Plausible but incorrect application]"}]}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.6}, {"id": "53c89d9a-4a69-47f7-bbf1-f523e2763741", "name": "Answer questions", "type": "@n8n/n8n-nodes-langchain.chainRetrievalQa", "position": [1400, 800], "parameters": {"text": "={{ $json.text }}", "options": {"systemPromptTemplate": "You are an assistant for question-answering tasks. Use the following pieces of retrieved context to answer the question.\n\nIf you don't know the answer, just say that you don't know, don't try to make up an answer.\nUse text plain (not markdown).\n----------------\nContext: {context}"}, "promptType": "define"}, "typeVersion": 1.5}, {"id": "93d55b4f-2a93-474e-b431-6fd8ef868c45", "name": "Answer and create options", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1420, 1560], "parameters": {"text": "={{ $json.text }}", "options": {"systemMessage": "=System Prompt for RAG-Based Multiple-Choice Exam Creation\n\nPURPOSE:\nYou are an AI assistant specialized in creating multiple-choice exams. Your task is to generate questions with one correct answer and three plausible but incorrect options using only the Retrieval Augmented Generation (RAG) tool to source accurate information.\n\nINPUT:\nYou will receive a topic, subject area, or specific question to create exam items for.\n\nOUTPUT REQUIREMENTS:\n- Create multiple-choice questions with exactly four options per question\n- Each question must have one correct answer and three false answers\n- The correct answer must be derived directly from the RAG tool's retrieved information\n- All false answers must be plausible but clearly incorrect when compared to the retrieved information\n- Use plain text only (no markdown formatting)\n- Present all content in a clean, simple format without any special formatting\n\nPROCESS:\n1. For each question:\n   - Use the RAG tool to retrieve accurate information on the topic\n   - Formulate a clear, unambiguous question based on the retrieved information\n   - Extract the correct answer directly from the retrieved information\n   - Create three false answers that are plausible but contradicted by the retrieved information\n   - Mix the order of correct and incorrect answers\n\n2. For creating false answers:\n   - Use common misconceptions related to the topic\n   - Create answers that contain partial truths but are ultimately incorrect\n   - Modify correct information slightly to make it incorrect\n   - Avoid obviously wrong answers that would be too easy to eliminate\n\nOUTPUT FORMAT:\nQuestion: [Question text]\nA. [Option A]\nB. [Option B]\nC. [Option C]\nD. [Option D]\n\nGUIDELINES:\n- Questions should be clear and direct\n- Use simple, straightforward language\n- Avoid negatively phrased questions (e.g., \"Which of the following is NOT...\")\n- Ensure all answer options are approximately the same length\n- Do not include any explanations, notes, or additional information\n- Do not include any formatting beyond plain text\n- Do not indicate which answer is correct in the output\n- Ensure all questions and answers are factually accurate based on the RAG tool's information\n- Make sure distractors (false answers) are genuinely plausible to someone not familiar with the topic\n\nCONSTRAINTS:\n- You must use the RAG tool for every question\n- You must not rely on your general knowledge without verification through RAG\n- You must not use markdown formatting\n- You must not include any meta-information about the questions\n- You must ensure all answer options are mutually exclusive (no overlap in meaning)\n- You must use plain text only for all output"}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.9}, {"id": "c7e55f54-d851-4786-839d-fe839659caea", "name": "Write open", "type": "n8n-nodes-base.googleSheets", "position": [1880, 800], "parameters": {"columns": {"value": {"ANSWER": "={{ $json.response }}", "QUESTION": "={{ $('Loop Over Items').item.json.text }}"}, "schema": [{"id": "QUESTION", "type": "string", "display": true, "required": false, "displayName": "QUESTION", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ANSWER", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ANSWER", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/16zkksQMG1U9U850DFC5nDy-90VYZCgxLlyVwDB9I28Q/edit#gid=0", "cachedResultName": "Open questions"}, "documentId": {"__rl": true, "mode": "list", "value": "16zkksQMG1U9U850DFC5nDy-90VYZCgxLlyVwDB9I28Q", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/16zkksQMG1U9U850DFC5nDy-90VYZCgxLlyVwDB9I28Q/edit?usp=drivesdk", "cachedResultName": "Question for <PERSON>am"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "1c72d8f0-b5b7-4e10-ad03-6c8491136cdf", "name": "Write closed", "type": "n8n-nodes-base.googleSheets", "position": [1860, 1560], "parameters": {"columns": {"value": {"CORRECT": "={{ $json.output.correct }}", "ANSWER A": "={{ $json.output.answers[0] }}", "ANSWER B": "={{ $json.output.answers[1] }}", "ANSWER C": "={{ $json.output.answers[2] }}", "ANSWER D": "={{ $json.output.answers[3] }}", "QUESTION": "={{ $('Closed questions').item.json.text }}"}, "schema": [{"id": "QUESTION", "type": "string", "display": true, "required": false, "displayName": "QUESTION", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ANSWER A", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ANSWER A", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ANSWER B", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ANSWER B", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ANSWER C", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ANSWER C", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ANSWER D", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ANSWER D", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CORRECT", "type": "string", "display": true, "removed": false, "required": false, "displayName": "CORRECT", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": *********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/16zkksQMG1U9U850DFC5nDy-90VYZCgxLlyVwDB9I28Q/edit#gid=*********", "cachedResultName": "Closed questions"}, "documentId": {"__rl": true, "mode": "list", "value": "16zkksQMG1U9U850DFC5nDy-90VYZCgxLlyVwDB9I28Q", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/16zkksQMG1U9U850DFC5nDy-90VYZCgxLlyVwDB9I28Q/edit?usp=drivesdk", "cachedResultName": "Question for <PERSON>am"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "9e5e41b1-32b2-413e-b63f-13e946857569", "name": "Embeddings OpenAI1", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [1420, 1340], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "4zwP0MSr8zkNvvV9", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "a87ab6ba-39b0-4c7c-be19-9003e38c9495", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-460, 780], "parameters": {"width": 620, "height": 180, "content": "# STEP 3\n\nThe chain analyzes the document and creates 10 \"open\" questions and another chain analyzes each single question and through the consultation of the vector database the optimal answer is obtained."}, "typeVersion": 1}, {"id": "ea81bccc-d204-44d7-89b2-85f7b3267e34", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-460, 1540], "parameters": {"width": 620, "height": 180, "content": "# STEP 4\n\nThe chain analyzes the document and creates 10 questions with \"closed\" answers and another chain analyzes each single question and through the consultation of the vector database the correct answer and 3 other wrong answers are obtained to be used as a quiz."}, "typeVersion": 1}, {"id": "b510a77d-7436-4b84-b7a3-d42d75b15b59", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-480, -360], "parameters": {"color": 3, "width": 1120, "height": 200, "content": "## Auto-Generate Exam Questions from Google Docs with AI\n\nThis workflow automates the creation of exam questions (both open-ended and multiple-choice) from educational content stored in Google Docs, using AI-powered analysis and vector database retrieval\n\nThis workflow **saves educators hours of manual work** while ensuring high-quality, curriculum-aligned assessments. Let me know if you'd like help adapting it for specific subjects!\n"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "626a1ef7-45ae-4724-af3b-8a04b37fffc8", "connections": {"RAG": {"ai_tool": [[{"node": "Answer and create options", "type": "ai_tool", "index": 0}]]}, "Get Doc": {"main": [[{"node": "<PERSON><PERSON><PERSON> di MD", "type": "main", "index": 0}]]}, "Write open": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Write closed": {"main": [[{"node": "Loop Over Items1", "type": "main", "index": 0}]]}, "Converto di MD": {"main": [[{"node": "Closed questions", "type": "main", "index": 0}, {"node": "Convert to File", "type": "main", "index": 0}, {"node": "Open questions", "type": "main", "index": 0}]]}, "Open questions": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Token Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Convert to File": {"main": [[{"node": "Qdrant Vector Store", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Answer questions", "type": "main", "index": 0}]]}, "Answer questions": {"main": [[{"node": "Write open", "type": "main", "index": 0}]]}, "Closed questions": {"main": [[{"node": "Loop Over Items1", "type": "main", "index": 0}]]}, "Loop Over Items1": {"main": [[], [{"node": "Answer and create options", "type": "main", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Qdrant Vector Store", "type": "ai_embedding", "index": 0}]]}, "Embeddings OpenAI1": {"ai_embedding": [[{"node": "Qdrant Vector Store1", "type": "ai_embedding", "index": 0}]]}, "Embeddings OpenAI2": {"ai_embedding": [[{"node": "Qdrant Vector Store2", "type": "ai_embedding", "index": 0}]]}, "Refresh collection": {"main": [[{"node": "Get <PERSON>", "type": "main", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Qdrant Vector Store", "type": "ai_document", "index": 0}]]}, "Qdrant Vector Store1": {"ai_vectorStore": [[{"node": "Vector Store Retriever", "type": "ai_vectorStore", "index": 0}]]}, "Qdrant Vector Store2": {"ai_vectorStore": [[{"node": "RAG", "type": "ai_vectorStore", "index": 0}]]}, "Vector Store Retriever": {"ai_retriever": [[{"node": "Answer questions", "type": "ai_retriever", "index": 0}]]}, "Item List Output Parser": {"ai_outputParser": [[{"node": "Open questions", "type": "ai_outputParser", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Open questions", "type": "ai_languageModel", "index": 0}]]}, "Item List Output Parser1": {"ai_outputParser": [[{"node": "Closed questions", "type": "ai_outputParser", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Answer and create options", "type": "ai_outputParser", "index": 0}]]}, "Answer and create options": {"main": [[{"node": "Write closed", "type": "main", "index": 0}]]}, "Google Gemini Chat Model1": {"ai_languageModel": [[{"node": "Answer questions", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini Chat Model2": {"ai_languageModel": [[{"node": "Closed questions", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini Chat Model3": {"ai_languageModel": [[{"node": "Answer and create options", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini Chat Model4": {"ai_languageModel": [[{"node": "RAG", "type": "ai_languageModel", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Refresh collection", "type": "main", "index": 0}]]}}}