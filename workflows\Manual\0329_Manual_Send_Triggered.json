{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "912b279c-30e5-4991-92ab-040fc1e89c7a", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-60, 0], "parameters": {}, "typeVersion": 1}, {"id": "749d8762-d213-4dd3-b404-4c6518fcd28f", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-60, 200], "webhookId": "c2e664e6-645f-422a-99d3-cf0f4c53c345", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "1eeff044-b914-40f7-8d37-8b69007862cd", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [460, 0], "parameters": {"text": "={{ $json.chatInput }}", "options": {}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "ac34f7f0-d1dc-4ffb-8f49-6ddc925e97bc", "name": "Debug Input", "type": "n8n-nodes-base.set", "position": [160, 0], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "25d97d59-b0cf-46ae-916d-18059b3d6847", "name": "chatInput", "type": "string", "value": "Return a random color but not green or blue"}]}}, "typeVersion": 3.4}, {"id": "a410a0a5-1ea1-4ade-a32c-8f6fd959bae8", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [440, 200], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "923b1597-2e9c-4c38-b3bb-7d6dffb52e4a", "name": "Code Tool", "type": "@n8n/n8n-nodes-langchain.toolCode", "position": [660, 200], "parameters": {"name": "my_color_selector", "jsCode": "const colors = [\n    'red',\n    'green',\n    'blue',\n    'yellow',\n    'pink',\n    'white',\n    'black',\n    'orange',\n    'brown',\n];\n\nconst ignoreColors = query.split(',').map((text) => text.trim());\n\n// remove all the colors that should be ignored\nconst availableColors = colors.filter((color) => {\n    return !ignoreColors.includes(color);\n});\n\n// Select a random color\nreturn availableColors[Math.floor(Math.random() * availableColors.length)];\n", "description": "Call this tool to get a random color. The input should be a string with comma-separated names of colors to exclude."}, "typeVersion": 1.1}], "pinData": {}, "connections": {"Code Tool": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Debug Input": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Debug Input", "type": "main", "index": 0}]]}}}