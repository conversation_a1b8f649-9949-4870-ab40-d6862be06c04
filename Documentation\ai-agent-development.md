# AI Agent Development - N8N Workflows

## Overview
This document catalogs the **AI Agent Development** workflows from the n8n Community Workflows repository.

**Category:** AI Agent Development  
**Total Workflows:** 4  
**Generated:** 2025-07-27  
**Source:** https://scan-might-updates-postage.trycloudflare.com/api

---

## Workflows

### Awsrekognition Googlesheets Automation Webhook
**Filename:** `0150_Awsrekognition_GoogleSheets_Automation_Webhook.json`  
**Description:** Manual workflow that orchestrates Httprequest, Google Sheets, and Awsrekognition for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (6 nodes)  
**Integrations:** Httprequest,Google Sheets,Awsrekognition,  

---

### Translate cocktail instructions using LingvaNex
**Filename:** `0166_Manual_Lingvanex_Automation_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Lingvanex for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Httprequest,Lingvanex,  

---

### Get synonyms of a German word
**Filename:** `0192_Manual_Openthesaurus_Import_Triggered.json`  
**Description:** Manual workflow that integrates with Openthesaurus for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Openthesaurus,  

---

### Pyragogy AI Village - Orchestrazione Master (Architettura Profonda V2)
**Filename:** `generate-collaborative-handbooks-with-gpt4o-multi-agent-orchestration-human-review.json`  
**Description:** Complex multi-step automation that orchestrates Start, GitHub, and OpenAI for data processing. Uses 35 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (35 nodes)  
**Integrations:** Start,GitHub,OpenAI,Webhook,Respondtowebhook,Emailsend,PostgreSQL,Slack,  

---


## Summary

**Total AI Agent Development workflows:** 4  
**Documentation generated:** 2025-07-27 14:31:09  
**API Source:** https://scan-might-updates-postage.trycloudflare.com/api  

This documentation was automatically generated using the n8n workflow API endpoints.
