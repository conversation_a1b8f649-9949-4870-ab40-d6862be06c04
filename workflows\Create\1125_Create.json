{"nodes": [{"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.function", "position": [802, 307], "parameters": {"functionCode": "return [\n  {\n    json: {\n      id: 1,\n      name: \"<PERSON>\"\n    }\n  },\n  {\n    json: {\n      id: 2,\n      name: \"<PERSON>\"\n    }\n  },\n  {\n    json: {\n      id: 3,\n      name: \"<PERSON>\"\n    }\n  }\n];"}, "typeVersion": 1}, {"name": "Create an array of objects", "type": "n8n-nodes-base.function", "position": [1052, 307], "parameters": {"functionCode": "return [\n  {\n    json: {\n      data_object: items.map(item => item.json),\n    },\n  }\n];\n"}, "typeVersion": 1}], "connections": {"Mock Data": {"main": [[{"node": "Create an array of objects", "type": "main", "index": 0}]]}}}