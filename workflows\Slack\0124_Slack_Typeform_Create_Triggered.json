{"nodes": [{"name": "Typeform Trigger", "type": "n8n-nodes-base.typeformTrigger", "position": [140, 200], "webhookId": "", "parameters": {"formId": ""}, "credentials": {"typeformApi": {"id": "", "name": ""}}, "typeVersion": 1}, {"name": "Slack1", "type": "n8n-nodes-base.slack", "position": [1360, 300], "parameters": {"text": "🥳 An existing lead has just subscribed!", "channel": "", "attachments": [], "otherOptions": {}}, "credentials": {"slackApi": {"id": "", "name": ""}}, "typeVersion": 1}, {"name": "Airtable - Contact List", "type": "n8n-nodes-base.airtable", "position": [540, 200], "parameters": {"table": "Contacts", "operation": "list", "returnAll": false, "application": "", "additionalOptions": {"fields": [], "filterByFormula": "=fullName=\"{{$json[\"full_name\"]}}\""}}, "credentials": {"airtableApi": {"id": "", "name": ""}}, "executeOnce": false, "typeVersion": 1, "alwaysOutputData": true}, {"name": "Airtable - Update Contacts1", "type": "n8n-nodes-base.airtable", "position": [1150, 300], "parameters": {"id": "={{$node[\"Airtable - Contact List\"].json[\"id\"]}}", "table": "Contacts", "fields": ["firstName", "lastName", "linkedInProfile", "Email", "Phone", "website", "LinkedIn Company", "Industry", "Address"], "options": {"typecast": true}, "operation": "update", "application": "", "updateAllFields": false}, "credentials": {"airtableApi": {"id": "", "name": ""}}, "typeVersion": 1}, {"name": "<PERSON><PERSON>ck", "type": "n8n-nodes-base.slack", "position": [1360, 100], "parameters": {"text": "=🎉 A new lead has just subscribed!", "channel": "", "attachments": [], "otherOptions": {}}, "credentials": {"slackApi": {"id": "", "name": ""}}, "typeVersion": 1}, {"name": "Set - Contacts to update", "type": "n8n-nodes-base.set", "position": [940, 100], "parameters": {"values": {"string": [{"name": "firstName", "value": "={{$node[\"Dropcontact\"].json[\"first_name\"]}}"}, {"name": "lastName", "value": "={{$node[\"Dropcontact\"].json[\"last_name\"]}}"}, {"name": "linkedInProfile", "value": "={{$node[\"Dropcontact\"].json[\"linkedin\"]}}"}, {"name": "Email", "value": "={{$node[\"Dropcontact\"].json[\"email\"][0][\"email\"]}}"}, {"name": "Phone", "value": "={{$node[\"Dropcontact\"].json[\"phone\"]}}"}, {"name": "website", "value": "={{$node[\"Dropcontact\"].json[\"website\"]}}"}, {"name": "LinkedIn Company", "value": "={{$node[\"Dropcontact\"].json[\"company_linkedin\"]}}"}, {"name": "Industry", "value": "={{$node[\"Dropcontact\"].json[\"naf5_des\"]}}"}, {"name": "Address", "value": "={{$node[\"Dropcontact\"].json[\"siret_address\"]}}, {{$node[\"Dropcontact\"].json[\"siret_zip\"]}} {{$node[\"Dropcontact\"].json[\"siret_city\"]}}"}]}, "options": {"dotNotation": true}}, "typeVersion": 1}, {"name": "Dropcontact", "type": "n8n-nodes-base.dropcontact", "position": [340, 200], "parameters": {"email": "=", "options": {"siren": true, "language": "fr"}, "additionalFields": {"company": "={{$json[\"and your company ?\"]}}", "website": "={{$node[\"Typeform Trigger\"].json[\"tell me more... What's your website ?\"]}}", "last_name": "={{$json[\"Hi [field:1c6436830dfffbf1], what's your last name ?\"]}}", "first_name": "={{$json[\"First, what's your name?\"]}}"}}, "credentials": {"dropcontactApi": {"id": "", "name": ""}}, "typeVersion": 1}, {"name": "Contact ID empty ?", "type": "n8n-nodes-base.if", "position": [730, 200], "parameters": {"conditions": {"string": [{"value1": "={{$json[\"id\"]}}", "operation": "isEmpty"}]}}, "typeVersion": 1, "alwaysOutputData": true}, {"name": "Airtable - Create Contacts", "type": "n8n-nodes-base.airtable", "position": [1150, 100], "parameters": {"table": "Contacts", "options": {"typecast": true}, "operation": "append", "application": ""}, "credentials": {"airtableApi": {"id": "", "name": ""}}, "typeVersion": 1}, {"name": "Set - Contacts to create", "type": "n8n-nodes-base.set", "position": [940, 300], "parameters": {"values": {"string": [{"name": "firstName", "value": "={{$node[\"Dropcontact\"].json[\"first_name\"]}}"}, {"name": "lastName", "value": "={{$node[\"Dropcontact\"].json[\"last_name\"]}}"}, {"name": "linkedInProfile", "value": "={{$node[\"Dropcontact\"].json[\"linkedin\"]}}"}, {"name": "Email", "value": "={{$node[\"Dropcontact\"].json[\"email\"][0][\"email\"]}}"}, {"name": "Phone", "value": "={{$node[\"Dropcontact\"].json[\"phone\"]}}"}, {"name": "website", "value": "={{$node[\"Dropcontact\"].json[\"website\"]}}"}, {"name": "LinkedIn Company", "value": "={{$node[\"Dropcontact\"].json[\"company_linkedin\"]}}"}, {"name": "Industry", "value": "={{$node[\"Dropcontact\"].json[\"naf5_des\"]}}"}, {"name": "Address", "value": "={{$node[\"Dropcontact\"].json[\"siret_address\"]}}, {{$node[\"Dropcontact\"].json[\"siret_zip\"]}} {{$node[\"Dropcontact\"].json[\"siret_city\"]}}"}]}, "options": {"dotNotation": true}}, "typeVersion": 1}], "connections": {"Dropcontact": {"main": [[{"node": "Airtable - Contact List", "type": "main", "index": 0}]]}, "Typeform Trigger": {"main": [[{"node": "Dropcontact", "type": "main", "index": 0}]]}, "Contact ID empty ?": {"main": [[{"node": "Set - Contacts to update", "type": "main", "index": 0}], [{"node": "Set - Contacts to create", "type": "main", "index": 0}]]}, "Airtable - Contact List": {"main": [[{"node": "Contact ID empty ?", "type": "main", "index": 0}]]}, "Set - Contacts to create": {"main": [[{"node": "Airtable - Update Contacts1", "type": "main", "index": 0}]]}, "Set - Contacts to update": {"main": [[{"node": "Airtable - Create Contacts", "type": "main", "index": 0}]]}, "Airtable - Create Contacts": {"main": [[{"node": "<PERSON><PERSON>ck", "type": "main", "index": 0}]]}, "Airtable - Update Contacts1": {"main": [[{"node": "Slack1", "type": "main", "index": 0}]]}}}