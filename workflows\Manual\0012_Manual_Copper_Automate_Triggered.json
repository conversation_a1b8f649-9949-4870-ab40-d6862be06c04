{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 320], "parameters": {}, "typeVersion": 1}, {"name": "Copper", "type": "n8n-nodes-base.copper", "position": [450, 320], "parameters": {"name": "<PERSON><PERSON><PERSON>", "resource": "person", "additionalFields": {"emails": {"emailFields": [{"email": "<EMAIL>", "category": "work"}]}}}, "credentials": {"copperApi": "Copper API Credentials"}, "typeVersion": 1}, {"name": "Copper1", "type": "n8n-nodes-base.copper", "position": [650, 320], "parameters": {"personId": "={{$json[\"id\"]}}", "resource": "person", "operation": "update", "updateFields": {"phone_numbers": {"phoneFields": [{"number": "1234567890", "category": "work"}]}}}, "credentials": {"copperApi": "Copper API Credentials"}, "typeVersion": 1}, {"name": "Copper2", "type": "n8n-nodes-base.copper", "position": [850, 320], "parameters": {"personId": "={{$json[\"id\"]}}", "resource": "person", "operation": "get"}, "credentials": {"copperApi": "Copper API Credentials"}, "typeVersion": 1}], "connections": {"Copper": {"main": [[{"node": "Copper1", "type": "main", "index": 0}]]}, "Copper1": {"main": [[{"node": "Copper2", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Copper", "type": "main", "index": 0}]]}}}