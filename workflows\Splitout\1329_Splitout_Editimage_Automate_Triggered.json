{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9"}, "nodes": [{"id": "6c78b4c7-993b-410d-93e7-e11b3052e53b", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [0, 420], "parameters": {}, "typeVersion": 1}, {"id": "c2ab6497-6d6d-483b-bd43-494ae95394c0", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [1440, 600], "parameters": {"schemaType": "manual", "inputSchema": "{\n\t\"type\": \"object\",\n\t\"properties\": {\n\t\t\"is_valid\": { \"type\": \"boolean\" },\n \"photo_description\": {\n \"type\": \"string\",\n \"description\": \"describe the appearance of the person(s), object(s) if any and the background in the image. Mention any colours of each if possible.\"\n },\n\t\t\"reasons\": {\n \"type\": \"array\",\n \"items\": { \"type\": \"string\" }\n }\n\t}\n}"}, "typeVersion": 1.2}, {"id": "b23f5298-17c7-49ac-a8ca-78e006b2d294", "name": "Photo URLs", "type": "n8n-nodes-base.set", "position": [360, 380], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "6baa3e08-8957-454e-8ee9-d5414a0ff990", "name": "data", "type": "array", "value": "={{\n[\n{\n \"name\": \"portrait_1\",\n \"url\": \"https://drive.google.com/file/d/1zs963iFkO-3g2rKak8Hcy555h55D8gjF/view?usp=sharing\"\n},\n{\n \"name\": \"portrait_2\",\n \"url\": \"https://drive.google.com/file/d/19FyDcs68dZauQSEf6SEulJMag51SPsFy/view?usp=sharing\"\n},\n{\n \"name\": \"portrait_3\",\n \"url\": \"https://drive.google.com/file/d/1gbXjfNYE7Tvuw_riFmHMKoqPPu696VfW/view?usp=sharing\",\n\n},\n{\n \"name\": \"portrait_4\",\n \"url\": \"https://drive.google.com/file/d/1s19hYdxgfMkrnU25l6YIDq-myQr1tQMa/view?usp=sharing\"\n},\n{\n \"name\": \"portrait_5\",\n \"url\": \"https://drive.google.com/file/d/193FqIXJWAKj6O2SmOj3cLBfypHBkgdI5/view?usp=sharing\"\n}\n]\n}}"}]}}, "typeVersion": 3.4}, {"id": "8d445f73-dff7-485b-87e2-5b64da09cbf0", "name": "Photos To List", "type": "n8n-nodes-base.splitOut", "position": [520, 380], "parameters": {"options": {}, "fieldToSplitOut": "data"}, "typeVersion": 1}, {"id": "7fb3b829-88a7-42ec-abfd-3ddaa042c916", "name": "Download Photos", "type": "n8n-nodes-base.googleDrive", "position": [680, 380], "parameters": {"fileId": {"__rl": true, "mode": "url", "value": "={{ $json.url }}"}, "options": {}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "yOwz41gMQclOadgu", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "b8644f6d-691f-49bc-b0fe-33a68c59638d", "name": "Resize For AI", "type": "n8n-nodes-base.editImage", "position": [1060, 440], "parameters": {"width": 1024, "height": 1024, "options": {}, "operation": "resize", "resizeOption": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "typeVersion": 1}, {"id": "ecb266f2-0d2d-4cbe-a641-26735f0bdf18", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [280, 180], "parameters": {"color": 7, "width": 594, "height": 438, "content": "## 1. Import Photos To Validate\n[Read more about using Google Drive](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googledrive)\n\nIn this demonstration, we'll import 5 different portraits to test our AI vision model. For convenience, we'll use Google Drive but feel free to swap this out for other sources such as other storage or by using webhooks."}, "typeVersion": 1}, {"id": "a1034923-0905-4cdd-a6bf-21d28aa3dd71", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [900, 180], "parameters": {"color": 7, "width": 774, "height": 589.25, "content": "## 2. Verify Passport Photo Validity Using AI Vision Model\n[Learn more about Basic LLM Chain](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.chainllm)\n\nVerifying if a photo is suitable for a passport photo is a great use-case for AI vision and to automate the process is an equally great use-case for using n8n. Here's we've pasted in the UK governments guidelines copied from gov.uk and have asked the AI to validate the incoming photos following those rules. A structured output parser is used to simplify the AI response which can be used to update a database or backend of your choosing."}, "typeVersion": 1}, {"id": "af231ee5-adff-4d27-ba5f-8c04ddd4892d", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-140, 0], "parameters": {"width": 386, "height": 610.0104651162792, "content": "## Try It Out!\n\n### This workflow takes a portrait and verifies if it makes for a valid passport photo. It achieves this by using an AI vision model following the UK government guidance.\n\nOpenAI's vision model was found to perform well for understanding photographs and so is recommended for this type of workflow. However, any capable vision model should work.\n\n### Need Help?\nJoin the [Discord](https://discord.com/invite/XPKeKXeB7d) or ask in the [Forum](https://community.n8n.io/)!"}, "typeVersion": 1}, {"id": "e07e1655-2683-4e21-b2b7-e0c0bfb569c0", "name": "Passport Photo Validator", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1240, 440], "parameters": {"text": "Assess if the image is a valid according to the passport photo criteria as set by the UK Government.", "messages": {"messageValues": [{"message": "=You help verify passport photo validity.\n\n## Rules for digital photos\nhttps://www.gov.uk/photos-for-passports\n\n### The quality of your digital photo\nYour photo must be:\n* clear and in focus\n* in colour\n* unaltered by computer software\n* at least 600 pixels wide and 750 pixels tall\n* at least 50KB and no more than 10MB\n\n### What your digital photo must show\nThe digital photo must:\n* contain no other objects or people\n* be taken against a plain white or light-coloured background\n* be in clear contrast to the background\n* not have ‘red eye’\n* If you’re using a photo taken on your own device, include your head, shoulders and upper body. Do not crop your photo - it will be done for you.\n\nIn your photo you must:\n* be facing forwards and looking straight at the camera\n* have a plain expression and your mouth closed\n* have your eyes open and visible\n* not have hair in front of your eyes\n* not have a head covering (unless it’s for religious or medical reasons)\n* not have anything covering your face\n* not have any shadows on your face or behind you - shadows on light background are okay\n* Do not wear glasses in your photo unless you have to do so. If you must wear glasses, they cannot be sunglasses or tinted glasses, and you must make sure your eyes are not covered by the frames or any glare, reflection or shadow.\n\n### Photos of babies and children\n* Children must be on their own in the picture. Babies must not be holding toys or using dummies.\n* Children under 6 do not have to be looking directly at the camera or have a plain expression.\n* Children under one do not have to have their eyes open. You can support their head with your hand, but your hand must not be visible in the photo.\n* Children under one should lie on a plain light-coloured sheet. Take the photo from above.\n\n"}, {"type": "HumanMessagePromptTemplate", "messageType": "imageBinary"}]}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.4}, {"id": "0a36ba22-90b2-4abf-943b-c1cc8e7317d5", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1240, 600], "parameters": {"options": {}, "modelName": "models/gemini-1.5-pro-latest"}, "credentials": {"googlePalmApi": {"id": "dSxo6ns5wn658r8N", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}], "pinData": {}, "connections": {"Photo URLs": {"main": [[{"node": "Photos To List", "type": "main", "index": 0}]]}, "Resize For AI": {"main": [[{"node": "Passport Photo Validator", "type": "main", "index": 0}]]}, "Photos To List": {"main": [[{"node": "Download Photos", "type": "main", "index": 0}]]}, "Download Photos": {"main": [[{"node": "Resize For AI", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Passport Photo Validator", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Passport Photo Validator", "type": "ai_outputParser", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Photo URLs", "type": "main", "index": 0}]]}}}