{"id": "5opbTWPZRN05bYdz", "meta": {"instanceId": "2ca62dfdbee183085041310c6198e97a69dbf85e4843e42c21169e2f5e3db806", "templateCredsSetupCompleted": true}, "name": "Build an MCP Server with Google Calendar", "tags": [], "nodes": [{"id": "4be79e3f-3e83-4432-b23f-4e4e9cac171b", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [-360, -800], "parameters": {"color": 2, "width": 2720, "height": 140, "content": ""}, "typeVersion": 1}, {"id": "439a0233-c8ec-4ea5-8630-0f6e62c76bef", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [520, -780], "parameters": {"color": 2, "width": 960, "height": 80, "content": "# Learn How to Build a MCP Server with Google Calendar"}, "typeVersion": 1}, {"id": "08996f0a-4a2d-438f-a8d7-aca78968d33f", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-360, -600], "parameters": {"color": 7, "width": 620, "height": 280, "content": "# Introduce\n\nThis tutorial focuses on guiding users through the process of deploying MCP service with Google Calendar. By following this step - by - step guide, you'll be able to leverage the powerful features of MCP Server with Google Calendar, such as creating, reading, updating, and deleting events."}, "typeVersion": 1}, {"id": "0f866ad6-d1af-4732-be64-8c97af7e55ac", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-360, -240], "parameters": {"color": 6, "width": 620, "height": 760, "content": "# Author\n![<PERSON><PERSON><PERSON><PERSON>](https://avatars.githubusercontent.com/u/176571840?v=4)\n### <PERSON><PERSON><PERSON>nan\nFreelance consultant from China, specializing in automations and data analysis. I work with select clients, addressing their toughest projects.\n\nFor business inquiries, email <NAME_EMAIL>.\n"}, "typeVersion": 1}, {"id": "4e2cdec7-8d04-40a7-9270-0f408ebf2efb", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [300, -600], "parameters": {"color": 5, "width": 620, "content": "## Step1: Google Calendar tools require credentials\nIf you don't have your Google Credentials set up in n8n yet, watch [this](https://www.youtube.com/watch?v=3Ai1EPznlAc) video to learn how to do it.\n\nIf you are using n8n Cloud plans, it's very intuitive to setup and you may not even need the tutorial."}, "typeVersion": 1}, {"id": "0a3941f5-959f-499c-b5a6-b2b66b203b1e", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [300, -420], "parameters": {"color": 5, "width": 620, "height": 220, "content": "## Step 2: Create MCP Server Trigger and activate\nLog in to n8n and create a new workflow. On the new workflow page, click “Add First Step” to open a searchable menu of nodes and triggers. \n\nType “MCP Server Trigger” in the search bar and select it from the results to start your workflow. \n\nThis sets up how n8n receives events from the MCP Server, laying the groundwork for integrating Google Calendar into your automation. "}, "typeVersion": 1}, {"id": "42800020-7ed3-4419-9847-d2a751aa3071", "name": "SearchEvent", "type": "n8n-nodes-base.googleCalendarTool", "position": [400, 260], "parameters": {"limit": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Limit', ``, 'number') }}", "options": {}, "timeMax": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Before', ``, 'string') }}", "timeMin": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('After', ``, 'string') }}", "calendar": {"__rl": true, "mode": "list", "value": "<EMAIL>", "cachedResultName": "<EMAIL>"}, "operation": "getAll"}, "credentials": {"googleCalendarOAuth2Api": {"id": "Wi0S7gZu9R8zFjTC", "name": "Google Calendar account"}}, "typeVersion": 1.3}, {"id": "5d2bce57-f77d-4fd1-9342-d81107a6009d", "name": "CreateEvent", "type": "n8n-nodes-base.googleCalendarTool", "position": [520, 260], "parameters": {"end": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('End', ``, 'string') }}", "start": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Start', ``, 'string') }}", "calendar": {"__rl": true, "mode": "list", "value": "<EMAIL>", "cachedResultName": "<EMAIL>"}, "additionalFields": {"summary": "={{ $fromAI(\"event_title\", \"The event title\", \"string\") }}", "description": "={{ $fromAI(\"event_description\", \"The event description\", \"string\") }}"}}, "credentials": {"googleCalendarOAuth2Api": {"id": "Wi0S7gZu9R8zFjTC", "name": "Google Calendar account"}}, "typeVersion": 1.3}, {"id": "dbebec9c-fecc-4154-ba77-cfbb519ba40a", "name": "UpdateEvent", "type": "n8n-nodes-base.googleCalendarTool", "position": [640, 260], "parameters": {"eventId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Event_ID', ``, 'string') }}", "calendar": {"__rl": true, "mode": "list", "value": "<EMAIL>", "cachedResultName": "<EMAIL>"}, "operation": "update", "updateFields": {"end": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('End', ``, 'string') }}", "start": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Start', ``, 'string') }}", "summary": "={{ $fromAI(\"event_title\", \"The event title\", \"string\") }}", "description": "={{ $fromAI(\"event_description\", \"The event description\", \"string\") }}"}}, "credentials": {"googleCalendarOAuth2Api": {"id": "Wi0S7gZu9R8zFjTC", "name": "Google Calendar account"}}, "typeVersion": 1.3}, {"id": "24ef1fd5-29dc-4208-a33b-5337307d01e0", "name": "DeleteEvent", "type": "n8n-nodes-base.googleCalendarTool", "position": [760, 260], "parameters": {"eventId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Event_ID', ``, 'string') }}", "options": {}, "calendar": {"__rl": true, "mode": "list", "value": "<EMAIL>", "cachedResultName": "<EMAIL>"}, "operation": "delete"}, "credentials": {"googleCalendarOAuth2Api": {"id": "Wi0S7gZu9R8zFjTC", "name": "Google Calendar account"}}, "typeVersion": 1.3}, {"id": "ec4aa55d-c6ee-4990-9c51-6ee1892600dd", "name": "Google Calendar MCP", "type": "@n8n/n8n-nodes-langchain.mcpTrigger", "position": [400, 60], "webhookId": "f9d9d5ea-6f83-42c8-ae50-ee6c71789bca", "parameters": {"path": "my-calendar"}, "typeVersion": 1}, {"id": "7e49bc5e-c3c1-47b3-8a0a-8f3b91ad954b", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [300, -180], "parameters": {"color": 5, "width": 620, "height": 600, "content": "## Step 3: Incorporate Google Calendar Tools\nAfter creating the MCP Server Trigger, rename it to \"Google Calendar MCP \" for clarity. \n\nClick \"Tools\" and type \"Google Calendar\" in the search bar to find tools for various Google Calendar operations. \n\nYou can add multiple tools, each for a specific task. For example, \"Get Many\" retrieves multiple events, \"Create\" makes new ones, \"Update\" modifies existing events, and \"Delete\" removes them. Use these tools to build customized, efficient workflows for your Google Calendar data. "}, "typeVersion": 1}, {"id": "6a86eb61-0e1f-4de1-a77f-0470fe1cd3ec", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [300, 440], "parameters": {"color": 5, "width": 620, "height": 580, "content": "## Step 4: Copy Your MCP Server Trigger URL and Activate Your Workflow\nDouble - click on the \"Google Calendar MCP\" node. On the node detail page, you'll locate the production URL, which might look something like \"https://xxx/mcp/my - calendar/sse\". Make sure to copy this URL as it will be used later in your workflow setup.\n\nAfter obtaining the URL, save the workflow. Then, check the \"Inactive\" button to activate the trigger. \n![Inactive](https://1.gravatar.com/userimage/*********/9a4d54537ef20427192f47fd8e413814?size=256)\n![Active](https://1.gravatar.com/userimage/*********/01bf3678cce04b3428586c908beb9954?size=256)\nOnce activated, your workflow will start listening for events from the MCP Server, enabling seamless integration with the Google Calendar service."}, "typeVersion": 1}, {"id": "aed25c42-78e1-4984-8831-768e2bbe6888", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [960, -600], "parameters": {"color": 4, "width": 620, "height": 140, "content": "## Step 5: Create a New Workflow for AI Agent\nAt this stage, you're required to create a new workflow. Once the new workflow interface is open, click on the \"Add First Step\" option. In the list of available nodes and triggers that appears, search for and select the \"on Chat Message\" option to add it to your workflow. This sets the initial trigger for your AI-Agent-related workflow."}, "typeVersion": 1}, {"id": "214dbba6-dffe-4c43-8c14-77babd52107f", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [960, -440], "parameters": {"color": 4, "width": 620, "height": 1060, "content": "## Step 6: Add AI Agent Node\nAfter successfully creating the Chat Messages Trigger, you can proceed to add an \"AI Agent\" node right after it. Double - click on this newly added \"AI Agent\" node to open its configuration panel.\n\nIn the configuration, you'll need to add a specific option. Under the System Message field, enter the following text: \"You are a helpful assistant. Current datetime is {{ $now.toString() }}\". This message provides the AI with the current date and time, which can be useful for context in various interactions.\n\nNext, select an appropriate Large Language Model (LLM) from the available options. This model will be responsible for handling the chat and delivering events.\n\nTo enable continuous and context - aware conversations, add memory to the Agent. This allows the AI Agent to remember previous interactions, providing a more seamless and engaging chat experience.\n\nFinally, search for and add the \"MCP Client\" tool. In the SSE Endpoint section of the \"MCP Client\" configuration, paste the URL that you copied in Step 4. This step connects the AI Agent workflow to the MCP Server, enabling data flow and interaction between the two. "}, "typeVersion": 1}, {"id": "7ba10d96-e1cc-456d-9174-c848524466dd", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1220, 20], "parameters": {"options": {"systemMessage": "=You are a helpful assistant.\nCurrent datetime is {{ $now.toString() }}"}}, "typeVersion": 1.8}, {"id": "2d577167-74d2-4966-8c39-79477787ed68", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [1020, 20], "webhookId": "7b02318f-1c6b-4f2a-9a4f-b17fa69ea680", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "0c5f70f5-5156-42f1-90ab-1f294f2fa2d9", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [1320, 240], "parameters": {}, "typeVersion": 1.3}, {"id": "cf747bc2-9c08-4f8f-9408-135e17ef0d3d", "name": "Calendar MCP", "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "position": [1440, 240], "parameters": {"sseEndpoint": "https://xxx.app.n8n.cloud/mcp/my-calendar/sse"}, "typeVersion": 1}, {"id": "8891a5de-e35f-4367-bfb7-0e54ce4452be", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [1020, 360], "parameters": {"color": 7, "height": 240, "content": "## Why model 4o? 👆\nAfter testing 4o-mini it had some difficulties handling the calendar requests, while the 4o model handled it with ease.\n\nDepending on your prompt and tools, 4o-mini might be able to work well too, but it requires further testing."}, "typeVersion": 1}, {"id": "f5d9ddb5-5957-4d22-8d85-a1c08eb813d8", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [1620, -600], "parameters": {"color": 6, "width": 740, "height": 520, "content": "# Let's Try!\n\n![create](https://0.gravatar.com/userimage/*********/5dfab90301432c344990fafb166546e1?size=256)\n\n![create-finish](https://0.gravatar.com/userimage/*********/7126b569dd9868c056f9ad3a23be2a25?size=256)"}, "typeVersion": 1}, {"id": "31b467cd-1d70-4c05-ae14-9f9e455cd55c", "name": "gpt-4o", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1180, 240], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "40ZaiQQN82bPTck0", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "007f0f3f-e7ca-4ea8-acba-cfde3bd8d1dd", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [1620, -40], "parameters": {"color": 7, "width": 740, "height": 80, "content": "# Enjoy It! 😊 😊 😊 "}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "c99542aa-af94-4e26-b255-473a26e0a962", "connections": {"gpt-4o": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "CreateEvent": {"ai_tool": [[{"node": "Google Calendar MCP", "type": "ai_tool", "index": 0}]]}, "DeleteEvent": {"ai_tool": [[{"node": "Google Calendar MCP", "type": "ai_tool", "index": 0}]]}, "SearchEvent": {"ai_tool": [[{"node": "Google Calendar MCP", "type": "ai_tool", "index": 0}]]}, "UpdateEvent": {"ai_tool": [[{"node": "Google Calendar MCP", "type": "ai_tool", "index": 0}]]}, "Calendar MCP": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}}}