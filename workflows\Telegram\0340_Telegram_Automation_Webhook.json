{"id": "1ZfA8Do3j7lCB3zF", "meta": {"instanceId": "a5283507e1917a33cc3ae615b2e7d5ad2c1e50955e6f831272ddd5ab816f3fb6", "templateCredsSetupCompleted": true}, "name": "Blockchain DEX Screener Insights Agent", "tags": [], "nodes": [{"id": "0e57bcd4-661d-40e3-a9d2-c66d5b84171c", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-280, 340], "webhookId": "e79527d8-89bd-4974-926c-2bcd8020cfa4", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "518565fc-1ee9-4c19-a300-a2c2bef2bb60", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [80, 340], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "yUizd8t0sD5wMYVG", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "a52660f2-b13a-4dfb-9429-3f8e382fb4a6", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [240, 340], "parameters": {}, "typeVersion": 1.3}, {"id": "6714c6df-cc31-4758-956b-1db42ec3112f", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "position": [-260, -140], "webhookId": "********-2756-4c11-9ac1-106d63c5af18", "parameters": {"updates": ["message"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "R3vpGq0SURbvEw2Z", "name": "Telegram account"}}, "typeVersion": 1.1}, {"id": "91b1aecd-cbbf-4e17-afca-bb9e6b98e4d0", "name": "Blockchain DEX Screener Insights Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [580, 40], "parameters": {"text": "={{ $('<PERSON><PERSON><PERSON> Trigger').item.json.message.text }}", "options": {"systemMessage": "You are the Blockchain DEX Screener Insights Agent. You have direct access to a suite of tools that interact with the DexScreener API to provide real-time insights from blockchain DEX data. Below is a summary of the available tools, their purposes, and how to use them:\n\n1. **DexScreener Latest Token Profiles**  \n   - **Purpose:** Fetches the latest token profiles.  \n   - **Endpoint:** `/token-profiles/latest/v1`  \n   - **Usage:** Use this tool to retrieve updated profiles, including token details, images, descriptions, and links.\n\n2. **DexScreener Latest Boosted Tokens**  \n   - **Purpose:** Retrieves the latest boosted tokens.  \n   - **Endpoint:** `/token-boosts/latest/v1`  \n   - **Usage:** Use this tool to get current boosted tokens data along with associated details such as token addresses, amounts, and descriptions.\n\n3. **DexScreener Top Token Boosts**  \n   - **Purpose:** Gets tokens with the most active boosts.  \n   - **Endpoint:** `/token-boosts/top/v1`  \n   - **Usage:** Use this tool when you need to identify tokens that are currently experiencing the highest levels of boosting activity.\n\n4. **DexScreener Search Pairs**  \n   - **Purpose:** Searches for trading pairs matching a query.  \n   - **Endpoint:** `/latest/dex/search`  \n   - **Usage:** Provide a query (e.g., `\"SOL/USDC\"`) to find specific pairs along with detailed information on base and quote tokens, pricing, volume, and more.\n\n5. **DexScreener Check Orders Paid for Token**  \n   - **Purpose:** Checks orders paid for a specific token.  \n   - **Endpoint:** `/orders/v1/{chainId}/{tokenAddress}`  \n   - **Usage:** Specify the `chainId` and `tokenAddress` to review the status and details (e.g., processing status, payment timestamp) of token orders.\n\n6. **DexScreener Get Pairs by Chain and Pair Address**  \n   - **Purpose:** Retrieves one or multiple pairs by chain and pair address.  \n   - **Endpoint:** `/latest/dex/pairs/{chainId}/{pairId}`  \n   - **Usage:** Use this tool to obtain detailed pair information by providing the chain ID and specific pair address.\n\n7. **DexScreener Token Pools**  \n   - **Purpose:** Fetches the pools of a given token address.  \n   - **Endpoint:** `/token-pairs/v1/{chainId}/{tokenAddress}`  \n   - **Usage:** Provide the chain ID and token address to receive information on available liquidity pools for that token.\n\n8. **DexScreener Pairs by Token Address**  \n   - **Purpose:** Retrieves one or multiple pairs by token address (supports comma-separated multiple addresses).  \n   - **Endpoint:** `/tokens/v1/{chainId}/{tokenAddresses}`  \n   - **Usage:** Use this tool when you need pair details for one or more tokens. Supply the chain ID and one or more token addresses (up to 30, comma-separated).\n\n**Usage Guidelines:**\n\n- **Rate Limits:** Adhere to the specified rate limits for each endpoint (ranging from 60 to 300 requests per minute).  \n- **Headers:** Each tool sends the header `Accept: */*` by default.  \n- **Parameters:** Use the appropriate path or query parameters as specified to tailor your request.  \n- **Insight Generation:** Leverage these tools to gather data and provide insightful analysis regarding token profiles, boosted tokens, pair search, orders, liquidity pools, and more.\n\nWhen responding to user queries, determine which tool or combination of tools is best suited to fetch the required data and generate comprehensive insights. Use these tools to validate data points and present up-to-date and reliable information on blockchain DEX activity.\n\nProceed with providing insights based on the available data from these DexScreener tools."}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "dfe730d6-a93c-45a6-a600-5fd552cc88b8", "name": "Telegram", "type": "n8n-nodes-base.telegram", "position": [1020, 40], "webhookId": "24c73b37-4374-4fcf-b3c9-fa9121e25049", "parameters": {"text": "={{ $json.output }}", "chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "R3vpGq0SURbvEw2Z", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "223fa9b3-8f49-407c-9a28-0f67bf6a13cc", "name": "Adds SessionId", "type": "n8n-nodes-base.set", "position": [240, 40], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "b5c25cd4-226b-4778-863f-79b13b4a5202", "name": "sessionId", "type": "string", "value": "={{ $json.message.chat.id }}"}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "f88141f2-e5be-46f5-abd5-3f095e04b09d", "name": "DexScreener Latest Token Profiles", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [400, 340], "parameters": {"url": "https://api.dexscreener.com/token-profiles/latest/v1", "sendHeaders": true, "toolDescription": "This tool fetches the latest token profiles from the DexScreener API (rate limit: 60 requests per minute).", "parametersHeaders": {"values": [{"name": "Accept", "value": "*/*", "valueProvider": "fieldValue"}]}}, "typeVersion": 1.1}, {"id": "6adb778c-5c98-45b5-9979-013abe5b88a8", "name": "DexScreener Latest <PERSON><PERSON><PERSON>", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [580, 340], "parameters": {"url": "https://api.dexscreener.com/token-boosts/latest/v1", "sendHeaders": true, "toolDescription": "This tool fetches the latest boosted tokens from the DexScreener API (rate limit: 60 requests per minute).", "parametersHeaders": {"values": [{"name": "Accept", "value": "*/*", "valueProvider": "fieldValue"}]}}, "typeVersion": 1.1}, {"id": "10ecdbbe-8d9c-4485-8ce1-45afe72c0ae2", "name": "DexScreener Top Token Boosts", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [760, 340], "parameters": {"url": "https://api.dexscreener.com/token-boosts/top/v1", "sendHeaders": true, "toolDescription": "This tool fetches the tokens with the most active boosts from the DexScreener API (rate limit: 60 requests per minute).", "parametersHeaders": {"values": [{"name": "Accept", "value": "*/*", "valueProvider": "fieldValue"}]}}, "typeVersion": 1.1}, {"id": "2a9de1cd-aed7-4037-aaee-582ec1c3a244", "name": "DexScreener Search Pairs", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1280, 340], "parameters": {"url": "https://api.dexscreener.com/latest/dex/search", "sendQuery": true, "sendHeaders": true, "parametersQuery": {"values": [{"name": "q"}]}, "toolDescription": "This tool searches for pairs matching a query from the DexScreener API (rate limit: 300 requests per minute).", "parametersHeaders": {"values": [{"name": "Accept", "value": "*/*", "valueProvider": "fieldValue"}]}}, "typeVersion": 1.1}, {"id": "fe355be2-b158-4f44-bd52-c3ad14297c8b", "name": "DexScreener Check Orders Paid for Token", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [940, 340], "parameters": {"url": "https://api.dexscreener.com/orders/v1/{chainId}/{tokenAddress}", "sendHeaders": true, "toolDescription": "This tool checks orders paid for a token on DexScreener (rate limit: 60 requests per minute).", "parametersHeaders": {"values": [{"name": "Accept", "value": "*/*", "valueProvider": "fieldValue"}]}}, "typeVersion": 1.1}, {"id": "a3519f26-61ce-4e5b-9fb8-06a080fbaea4", "name": "DexScreener Get Pairs by Chain and Pair Address", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1100, 340], "parameters": {"url": "https://api.dexscreener.com/latest/dex/pairs/{chainId}/{pairId}", "sendHeaders": true, "toolDescription": "This tool retrieves one or multiple pairs by chain and pair address from the DexScreener API (rate limit: 300 requests per minute).", "parametersHeaders": {"values": [{"name": "Accept", "value": "*/*", "valueProvider": "fieldValue"}]}}, "typeVersion": 1.1}, {"id": "da965564-a024-4358-8399-e01775142b36", "name": "DexScreener Token Pools", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1480, 340], "parameters": {"url": "https://api.dexscreener.com/token-pairs/v1/{chainId}/{tokenAddress}", "sendHeaders": true, "toolDescription": "This tool retrieves the pools of a given token address from the DexScreener API (rate limit: 300 requests per minute).", "parametersHeaders": {"values": [{"name": "Accept", "value": "*/*", "valueProvider": "fieldValue"}]}}, "typeVersion": 1.1}, {"id": "31cb228c-9a6d-4519-a6a9-7be9cc75716e", "name": "DexScreener Pairs by <PERSON><PERSON>", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1700, 340], "parameters": {"url": "https://api.dexscreener.com/tokens/v1/{chainId}/{tokenAddresses}", "sendHeaders": true, "toolDescription": "This tool retrieves one or multiple pairs by token address from the DexScreener API (rate limit: 300 requests per minute).", "parametersHeaders": {"values": [{"name": "Accept", "value": "*/*", "valueProvider": "fieldValue"}]}}, "typeVersion": 1.1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "2fbb101c-f139-4e20-88d9-88db0d7ce4f9", "connections": {"Adds SessionId": {"main": [[{"node": "Blockchain DEX Screener Insights Agent", "type": "main", "index": 0}]]}, "Telegram Trigger": {"main": [[{"node": "Adds SessionId", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Blockchain DEX Screener Insights Agent", "type": "ai_languageModel", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "Blockchain DEX Screener Insights Agent", "type": "ai_memory", "index": 0}]]}, "DexScreener Token Pools": {"ai_tool": [[{"node": "Blockchain DEX Screener Insights Agent", "type": "ai_tool", "index": 0}]]}, "DexScreener Search Pairs": {"ai_tool": [[{"node": "Blockchain DEX Screener Insights Agent", "type": "ai_tool", "index": 0}]]}, "When chat message received": {"main": [[{"node": "Adds SessionId", "type": "main", "index": 0}]]}, "DexScreener Top Token Boosts": {"ai_tool": [[{"node": "Blockchain DEX Screener Insights Agent", "type": "ai_tool", "index": 0}]]}, "DexScreener Latest Boosted Tokens": {"ai_tool": [[{"node": "Blockchain DEX Screener Insights Agent", "type": "ai_tool", "index": 0}]]}, "DexScreener Latest Token Profiles": {"ai_tool": [[{"node": "Blockchain DEX Screener Insights Agent", "type": "ai_tool", "index": 0}]]}, "DexScreener Pairs by Token Address": {"ai_tool": [[{"node": "Blockchain DEX Screener Insights Agent", "type": "ai_tool", "index": 0}]]}, "Blockchain DEX Screener Insights Agent": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "DexScreener Check Orders Paid for Token": {"ai_tool": [[{"node": "Blockchain DEX Screener Insights Agent", "type": "ai_tool", "index": 0}]]}, "DexScreener Get Pairs by Chain and Pair Address": {"ai_tool": [[{"node": "Blockchain DEX Screener Insights Agent", "type": "ai_tool", "index": 0}]]}}}