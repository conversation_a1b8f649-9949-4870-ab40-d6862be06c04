{"id": "GToc9QTzJY1h1w3y", "meta": {"instanceId": "cba4a4a2eb5d7683330e2944837278938831ed3c042e20da6f5049c07ad14798", "templateCredsSetupCompleted": true}, "name": "AI-Powered Research with Jina AI Deep Search", "tags": [], "nodes": [{"id": "c76a7993-e7b1-426e-bcb4-9a18d9c72b83", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-820, -140], "parameters": {"color": 6, "width": 740, "height": 760, "content": "\n# **🚀 Developed by <PERSON>**  \n\nThank you for using **FREE: Open Deep Research 2.0**! 🎉  \n\nThis workflow was created to **democratize AI-powered research** and make advanced **automated knowledge discovery** available to **everyone**, without **API restrictions** or **cost barriers**.  \n\nIf you find this useful, feel free to **connect with me on LinkedIn** and stay updated on my latest AI & automation projects!  \n\n🔗 **Follow me on LinkedIn**: [<PERSON>](https://www.linkedin.com/in/leonard-van-<PERSON>/)  \n\nI truly appreciate the support from the **n8n community**, and I can’t wait to see how you use and improve this workflow! 🚀  \n\nHappy researching,  \n**<PERSON>** 💡"}, "typeVersion": 1}, {"id": "5620b6b5-1485-43a8-9acd-3368147bd742", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-60, -140], "parameters": {"width": 740, "height": 300, "content": "## 🚀 **FREE: Open Deep Research 2.0**  \nFully automated **AI-powered research workflow** using **Jina AI’s DeepSearch** to generate structured, fact-based reports—**no API key required!**  "}, "typeVersion": 1}, {"id": "dbe1cc91-34b4-4e5b-b404-dd86f47d1ebf", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-60, 180], "parameters": {"width": 740, "height": 440, "content": "## 🧠 **How This Workflow Works**  \n\nThis workflow automates **deep research and report generation** using **Jina AI's DeepSearch API**, making **advanced knowledge discovery accessible for free**.  \n\n1️⃣ **User Input → AI Research**  \n- A user **enters a research query** via chat.  \n- The workflow **sends the query** to **Jina AI’s DeepSearch API** for **in-depth analysis**.  \n\n2️⃣ **AI-Powered Insights**  \n- DeepSearch **retrieves** and **analyzes** relevant information.  \n- The response includes **key insights, structured analysis, and sources**.  \n\n3️⃣ **Markdown Formatting & Cleanup**  \n- The response **passes through a Code Node** that extracts, cleans, and **formats** the AI-generated insights into **readable Markdown output**.  \n- URLs are properly formatted, footnotes are structured, and the report is easy to read.  \n\n4️⃣ **Final Output**  \n- The final, **well-structured research report** is ready for use, **fully automated and free of charge!**  "}, "typeVersion": 1}, {"id": "42fd2f04-7d83-44c9-a41b-48860efbcf79", "name": "Jina AI DeepSearch Request", "type": "n8n-nodes-base.httpRequest", "position": [220, 0], "parameters": {"url": "https://deepsearch.jina.ai/v1/chat/completions", "method": "POST", "options": {}, "jsonBody": "={\n  \"model\": \"jina-deepsearch-v1\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": \"You are an advanced AI researcher that provides precise, well-structured, and insightful reports based on deep analysis. Your responses are factual, concise, and highly relevant.\"\n    },\n    {\n      \"role\": \"assistant\",\n      \"content\": \"<PERSON>, how can I help you?\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"Provide a deep and insightful analysis on: \\\"{{ $json.chatInput }}\\\". Ensure the response is well-structured, fact-based, and directly relevant to the topic, with no unnecessary information.\"\n    }\n  ],\n  \"stream\": true,\n  \"reasoning_effort\": \"low\"\n}", "sendBody": true, "specifyBody": "json"}, "typeVersion": 4.2}, {"id": "1b7b3bbe-2068-4d3a-a962-134bbb6ee516", "name": "User Research Query Input", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [0, 0], "webhookId": "8a4b05af-cd63-4692-9924-e35aaed5f077", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "218cbfe2-78de-4b00-875a-51761ac9f5c7", "name": "Format & Clean AI Response", "type": "n8n-nodes-base.code", "position": [440, 0], "parameters": {"jsCode": "function extractAndFormatMarkdown(input) {\n    let extractedContent = [];\n\n    // Extract raw data string from n8n input\n    let rawData = input.first().json.data;\n\n    // Split into individual JSON strings\n    let jsonStrings = rawData.split(\"\\n\\ndata: \").map(s => s.replace(/^data: /, ''));\n\n    let lastContent = \"\";\n    \n    // Reverse loop to find the last \"content\" field\n    for (let i = jsonStrings.length - 1; i >= 0; i--) {\n        try {\n            let parsedChunk = JSON.parse(jsonStrings[i]);\n\n            if (parsedChunk.choices && parsedChunk.choices.length > 0) {\n                for (let j = parsedChunk.choices.length - 1; j >= 0; j--) {\n                    let choice = parsedChunk.choices[j];\n\n                    if (choice.delta && choice.delta.content) {\n                        lastContent = choice.delta.content.trim();\n                        break;\n                    }\n                }\n            }\n\n            if (lastContent) break; // Stop once the last content is found\n        } catch (error) {\n            console.error(\"Failed to parse JSON string:\", jsonStrings[i], error);\n        }\n    }\n\n    // Clean and format Markdown\n    lastContent = lastContent.replace(/\\[\\^(\\d+)\\]: (.*?)\\n/g, \"[$1]: $2\\n\");  // Format footnotes\n    lastContent = lastContent.replace(/\\[\\^(\\d+)\\]/g, \"[^$1]\");  // Inline footnotes\n    lastContent = lastContent.replace(/(https?:\\/\\/[^\\s]+)(?=[^]]*\\])/g, \"<$1>\");  // Format links\n\n    // Return formatted content as an array of objects (n8n expects this format)\n    return [{ text: lastContent.trim() }];\n}\n\n// Execute function and return formatted output\nreturn extractAndFormatMarkdown($input);\n"}, "typeVersion": 2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "e03d69b5-3304-4f28-b99f-970d6fd1225b", "connections": {"User Research Query Input": {"main": [[{"node": "Jina AI DeepSearch Request", "type": "main", "index": 0}]]}, "Format & Clean AI Response": {"main": [[]]}, "Jina AI DeepSearch Request": {"main": [[{"node": "Format & Clean AI Response", "type": "main", "index": 0}]]}}}