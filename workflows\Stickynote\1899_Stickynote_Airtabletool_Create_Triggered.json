{"id": "kS9EfgZeaK3QV6Mw", "meta": {"instanceId": "9219ebc7795bea866f70aa3d977d54417fdf06c41944be95e20cfb60f992db19", "templateCredsSetupCompleted": true}, "name": "Build an MCP server with Airtable", "tags": [], "nodes": [{"id": "357649f0-43c5-4d6c-97b9-079fa3b5c1f3", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-100, -80], "webhookId": "c42d1e2e-b175-48cf-bfd4-aa8289266a20", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "ddf28f88-d76c-4ab6-82c4-c1ab1b746009", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [152, -180], "parameters": {"options": {}}, "typeVersion": 1.9}, {"id": "3170d4fd-700c-4449-a800-0395c06711aa", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [260, 40], "parameters": {}, "typeVersion": 1.3}, {"id": "557b0e0a-133b-4e80-afba-408803ed9898", "name": "Airtable MCP Client", "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "position": [600, 100], "parameters": {"sseEndpoint": "https://your-sse-endpoint-url"}, "typeVersion": 1}, {"id": "a0bc9aa3-decb-42f1-bee4-b9e425db81e8", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [80, 40], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "gpt-4o"}, "options": {}}, "credentials": {"openAiApi": {"id": "vupAk5StuhOafQcb", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "7737e491-ddd4-4e4f-a34d-73f518497990", "name": "MCP Server Trigger", "type": "@n8n/n8n-nodes-langchain.mcpTrigger", "position": [140, 240], "webhookId": "a93f35fb-3a86-4475-9ebd-1434aef8e433", "parameters": {"path": "insert-your-cool-path-here"}, "typeVersion": 1}, {"id": "0ce9e128-be31-41d8-ae06-************", "name": "Get", "type": "n8n-nodes-base.airtableTool", "position": [0, 460], "parameters": {"id": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Record_ID', ``, 'string') }}", "base": {"__rl": true, "mode": "list", "value": "appltMFy409fOqCVt", "cachedResultUrl": "https://airtable.com/appltMFy409fOqCVt", "cachedResultName": "AI news and social posts"}, "table": {"__rl": true, "mode": "list", "value": "tblZwA0JCNPeORaGi", "cachedResultUrl": "https://airtable.com/appltMFy409fOqCVt/tblZwA0JCNPeORaGi", "cachedResultName": "Social Posts"}, "options": {}}, "credentials": {"airtableTokenApi": {"id": "4hNTBxRPe8ft4Iic", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "1f9c6a61-9357-4fa1-81e0-42719284d291", "name": "Search", "type": "n8n-nodes-base.airtableTool", "position": [140, 460], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appltMFy409fOqCVt", "cachedResultUrl": "https://airtable.com/appltMFy409fOqCVt", "cachedResultName": "AI news and social posts"}, "table": {"__rl": true, "mode": "list", "value": "tblZwA0JCNPeORaGi", "cachedResultUrl": "https://airtable.com/appltMFy409fOqCVt/tblZwA0JCNPeORaGi", "cachedResultName": "Social Posts"}, "options": {}, "operation": "search", "returnAll": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Return_All', ``, 'boolean') }}", "filterByFormula": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Filter_By_Formula', ``, 'string') }}"}, "credentials": {"airtableTokenApi": {"id": "4hNTBxRPe8ft4Iic", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "061a0eb9-26de-47f1-b444-5dd98c984d70", "name": "Update", "type": "n8n-nodes-base.airtableTool", "position": [260, 460], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appltMFy409fOqCVt", "cachedResultUrl": "https://airtable.com/appltMFy409fOqCVt", "cachedResultName": "AI news and social posts"}, "table": {"__rl": true, "mode": "list", "value": "tblZwA0JCNPeORaGi", "cachedResultUrl": "https://airtable.com/appltMFy409fOqCVt/tblZwA0JCNPeORaGi", "cachedResultName": "Social Posts"}, "columns": {"value": {}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "sourceHeadline", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "sourceHeadline", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "sourceSummary", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "sourceSummary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "goToArticle", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "goToArticle", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "sourceURL", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "sourceURL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "socialChannels", "type": "array", "display": true, "options": [{"name": "Twitter", "value": "Twitter"}, {"name": "LinkedIn", "value": "LinkedIn"}, {"name": "Blog", "value": "Blog"}, {"name": "Instagram", "value": "Instagram"}, {"name": "Facebook", "value": "Facebook"}], "removed": false, "readOnly": false, "required": false, "displayName": "socialChannels", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "needsImage?", "type": "options", "display": true, "options": [{"name": "Yes", "value": "Yes"}, {"name": "No", "value": "No"}], "removed": false, "readOnly": false, "required": false, "displayName": "needsImage?", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "twitterCopy", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "twitterCopy", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedinCopy", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "linkedinCopy", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "instagramCopy", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "instagramCopy", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "facebookCopy", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "facebookCopy", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "blogCopy", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "blogCopy", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "imagePrompt", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "imagePrompt", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "postImage", "type": "array", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "postImage", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Status", "type": "options", "display": true, "options": [{"name": "Waiting for Content", "value": "Waiting for Content"}, {"name": "Needs Approval", "value": "Needs Approval"}, {"name": "Approved", "value": "Approved"}, {"name": "Posted", "value": "Posted"}], "removed": false, "readOnly": false, "required": false, "displayName": "Status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "datePosted", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "datePosted", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ID", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "ID", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": ["id"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update"}, "credentials": {"airtableTokenApi": {"id": "4hNTBxRPe8ft4Iic", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "b0e17724-5a56-4b71-997d-f9f44d16e5bc", "name": "Delete", "type": "n8n-nodes-base.airtableTool", "position": [400, 460], "parameters": {"id": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Record_ID', ``, 'string') }}", "base": {"__rl": true, "mode": "list", "value": "appltMFy409fOqCVt", "cachedResultUrl": "https://airtable.com/appltMFy409fOqCVt", "cachedResultName": "AI news and social posts"}, "table": {"__rl": true, "mode": "list", "value": "tblZwA0JCNPeORaGi", "cachedResultUrl": "https://airtable.com/appltMFy409fOqCVt/tblZwA0JCNPeORaGi", "cachedResultName": "Social Posts"}, "operation": "deleteRecord"}, "credentials": {"airtableTokenApi": {"id": "4hNTBxRPe8ft4Iic", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "2d0273b6-520b-45b7-8192-a83b10661028", "name": "Create", "type": "n8n-nodes-base.airtableTool", "position": [520, 460], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appltMFy409fOqCVt", "cachedResultUrl": "https://airtable.com/appltMFy409fOqCVt", "cachedResultName": "AI news and social posts"}, "table": {"__rl": true, "mode": "list", "value": "tblZwA0JCNPeORaGi", "cachedResultUrl": "https://airtable.com/appltMFy409fOqCVt/tblZwA0JCNPeORaGi", "cachedResultName": "Social Posts"}, "columns": {"value": {}, "schema": [{"id": "sourceHeadline", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "sourceHeadline", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "sourceSummary", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "sourceSummary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "goToArticle", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "goToArticle", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "sourceURL", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "sourceURL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "socialChannels", "type": "array", "display": true, "options": [{"name": "Twitter", "value": "Twitter"}, {"name": "LinkedIn", "value": "LinkedIn"}, {"name": "Blog", "value": "Blog"}, {"name": "Instagram", "value": "Instagram"}, {"name": "Facebook", "value": "Facebook"}], "removed": false, "readOnly": false, "required": false, "displayName": "socialChannels", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "needsImage?", "type": "options", "display": true, "options": [{"name": "Yes", "value": "Yes"}, {"name": "No", "value": "No"}], "removed": false, "readOnly": false, "required": false, "displayName": "needsImage?", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "twitterCopy", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "twitterCopy", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedinCopy", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "linkedinCopy", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "instagramCopy", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "instagramCopy", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "facebookCopy", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "facebookCopy", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "blogCopy", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "blogCopy", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "imagePrompt", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "imagePrompt", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "postImage", "type": "array", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "postImage", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Status", "type": "options", "display": true, "options": [{"name": "Waiting for Content", "value": "Waiting for Content"}, {"name": "Needs Approval", "value": "Needs Approval"}, {"name": "Approved", "value": "Approved"}, {"name": "Posted", "value": "Posted"}], "removed": false, "readOnly": false, "required": false, "displayName": "Status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "datePosted", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "datePosted", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ID", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "ID", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "create"}, "credentials": {"airtableTokenApi": {"id": "4hNTBxRPe8ft4Iic", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "69e906cf-82da-45c4-bacc-00970902d1f5", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [480, -60], "parameters": {"width": 360, "height": 280, "content": "## Update SSE endpoint "}, "typeVersion": 1}, {"id": "819d82c9-da54-48c6-a007-2e8750cfb3e2", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-520, -220], "parameters": {"width": 380, "height": 540, "content": "## Talk to your Airtable database \nPoint to your SSE endpoint, update your credentials and talk to your Airtable to:\n\n- Get records\n- Search records\n- Update records\n- Delete records\n- Create records\n\nand more!\n\nThis example showcases basic yet powerful functionality for a table.\n\nFeel free to combine it with other tools, connect a Slack channel as trigger node or another as output to receive the updates for the stakeholders and project owners.\n\nEnjoy!\n\nAitor\n[1 Node](https://1node.ai)"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "2355fe0d-0515-4d1b-8a02-42712191f466", "connections": {"Get": {"ai_tool": [[{"node": "MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "Create": {"ai_tool": [[{"node": "MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "Delete": {"ai_tool": [[{"node": "MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "Search": {"ai_tool": [[{"node": "MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "Update": {"ai_tool": [[{"node": "MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "AI Agent": {"main": [[]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Airtable MCP Client": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}}}