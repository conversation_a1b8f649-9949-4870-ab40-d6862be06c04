{"id": "1", "name": "TwitterWorkflow", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "disabled": true, "position": [400, 850], "parameters": {}, "typeVersion": 1}, {"name": "Filter Tweet Data", "type": "n8n-nodes-base.set", "position": [680, 300], "parameters": {"values": {"string": [{"name": "Tweet", "value": "={{$node[\"n8n.io mentions\"].json[\"text\"]}}"}, {"name": "Tweet ID", "value": "={{$node[\"n8n.io mentions\"].json[\"id\"]}}"}, {"name": "Tweet URL", "value": "=https://twitter.com/{{$node[\"n8n.io mentions\"].json[\"user\"][\"screen_name\"]}}/status/{{$node[\"n8n.io mentions\"].json[\"id_str\"]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "Only get new tweets", "type": "n8n-nodes-base.function", "position": [910, 300], "parameters": {"functionCode": "const staticData = getWorkflowStaticData('global');\nconst newTweetIds = items.map(item => item.json[\"Tweet ID\"]);\nconst oldTweetIds = staticData.oldTweetIds; \n\nif (!oldTweetIds) {\n  staticData.oldTweetIds = newTweetIds;\n  return items;\n}\n\n\nconst actualNewTweetIds = newTweetIds.filter((id) => !oldTweetIds.includes(id));\nconst actualNewTweets = items.filter((data) => actualNewTweetIds.includes(data.json['Tweet ID']));\nstaticData.oldTweetIds = [...actualNewTweetIds, ...oldTweetIds];\n\nreturn actualNewTweets;\n"}, "typeVersion": 1}, {"name": "n8n.io mentions", "type": "n8n-nodes-base.twitter", "position": [480, 300], "parameters": {"operation": "search", "searchText": "@n8n_io", "additionalFields": {}}, "credentials": {"twitterOAuth1Api": "Twitter Credentials"}, "typeVersion": 1}, {"name": "RocketChat", "type": "n8n-nodes-base.rocketchat", "position": [1150, 300], "parameters": {"text": "=New Mention!: {{$node[\"Filter Tweet Data\"].json[\"Tweet\"]}}.\nSee it here: {{$node[\"Only get new tweets\"].json[\"Tweet URL\"]}}", "channel": "general", "options": {}, "jsonParameters": true}, "credentials": {"rocketchatApi": "Rocket Chat API"}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [270, 300], "parameters": {"triggerTimes": {"item": [{"mode": "everyX", "unit": "minutes", "value": 1}]}}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Cron": {"main": [[{"node": "n8n.io mentions", "type": "main", "index": 0}]]}, "n8n.io mentions": {"main": [[{"node": "Filter Tweet Data", "type": "main", "index": 0}]]}, "Filter Tweet Data": {"main": [[{"node": "Only get new tweets", "type": "main", "index": 0}]]}, "Only get new tweets": {"main": [[{"node": "RocketChat", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[]]}}}