{"id": "9Or3kzIEI2tskRyR", "meta": {"instanceId": "885b4fb4a6a9c2cb5621429a7b972df0d05bb724c20ac7dac7171b62f1c7ef40", "templateCredsSetupCompleted": true}, "name": "Google Trend Data Extract, Summarization with Bright Data & Google Gemini", "tags": [{"id": "Kujft2FOjmOVQAmJ", "name": "Engineering", "createdAt": "2025-04-09T01:31:00.558Z", "updatedAt": "2025-04-09T01:31:00.558Z"}, {"id": "ddPkw7Hg5dZhQu2w", "name": "AI", "createdAt": "2025-04-13T05:38:08.053Z", "updatedAt": "2025-04-13T05:38:08.053Z"}], "nodes": [{"id": "29e6ce01-c42f-4155-add1-8a5cfff56967", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [200, -420], "parameters": {}, "typeVersion": 1}, {"id": "6abf0439-8286-4198-9b5e-226a7bf805dc", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [200, -780], "parameters": {"width": 400, "height": 300, "content": "## Note\n\nThis workflow deals with the structured data extraction by utilizing Bright Data Web Unlocker Product.\n\nThe Basic LLM Chain, Information Extraction, Summarization Chain are being used to demonstrate the usage of the N8N AI capabilities.\n\n**Please make sure to set the web URL of your interest within the \"Set URL and Bright Data Zone\" node and update the Webhook Notification URL**"}, "typeVersion": 1}, {"id": "6443bdea-4577-4983-adb7-0f52d6eb3825", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [620, -780], "parameters": {"width": 480, "height": 300, "content": "## LLM Usages\n\nGoogle Gemini Flash Exp model is being used.\n\nBasic LLM Chain Data Extractor.\n\nInformation Extraction is being used for the handling the structured data extraction.\n\nSummarization Chain is being used for building the summary."}, "typeVersion": 1}, {"id": "31280203-1ab1-4fb5-862f-e9c4f2969436", "name": "Markdown to Textual Data Extractor", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [860, -420], "parameters": {"text": "=You need to analyze the below markdown and convert to textual data. Please do not output with your own thoughts. Make sure to output with textual data only with no links, scripts, css etc.\n\n{{ $json.data }}", "messages": {"messageValues": [{"message": "You are a markdown expert"}]}, "promptType": "define"}, "typeVersion": 1.6}, {"id": "80e40926-aff3-4512-ad1e-61b3741b2387", "name": "Set URL and Bright Data Zone", "type": "n8n-nodes-base.set", "position": [420, -420], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "3aedba66-f447-4d7a-93c0-8158c5e795f9", "name": "url", "type": "string", "value": "https://trends.google.com/trends/explore?gprop=youtube&hl=en-US"}, {"id": "4e7ee31d-da89-422f-8079-2ff2d357a0ba", "name": "zone", "type": "string", "value": "web_unlocker1"}]}}, "typeVersion": 3.4}, {"id": "a60b2ac6-42c9-42af-a7fe-9cf570fcd017", "name": "Initiate a Webhook Notification for Markdown to Textual Data Extraction", "type": "n8n-nodes-base.httpRequest", "position": [1320, -720], "parameters": {"url": "https://webhook.site/3c36d7d1-de1b-4171-9fd3-643ea2e4dd76", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "content", "value": "={{ $json.text }}"}]}}, "typeVersion": 4.2}, {"id": "c8f9b2ad-8e66-43d0-aeb5-3f5e202910d3", "name": "Google Gemini Chat Model for Data Extract", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [948, -200], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "YeO7dHZnuGBVQKVZ", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "30d3b080-d35a-422d-990d-0df0d73b96a8", "name": "Perform Bright Data Web Request", "type": "n8n-nodes-base.httpRequest", "position": [640, -420], "parameters": {"url": "https://api.brightdata.com/request", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "zone", "value": "={{ $json.zone }}"}, {"name": "url", "value": "={{ $json.url }}?product=unlocker&method=api"}, {"name": "format", "value": "raw"}, {"name": "data_format", "value": "markdown"}]}, "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{}]}}, "credentials": {"httpHeaderAuth": {"id": "kdbqXuxIR8qIxF7y", "name": "Header Auth account"}}, "typeVersion": 4.2}, {"id": "18acbc0a-f0e2-4f5b-b98c-dec69c656a7e", "name": "Create a binary data", "type": "n8n-nodes-base.function", "position": [1980, -640], "parameters": {"functionCode": "items[0].binary = {\n  data: {\n    data: new Buffer(JSON.stringify(items[0].json, null, 2)).toString('base64')\n  }\n};\nreturn items;"}, "typeVersion": 1}, {"id": "1c386966-85ae-4b30-a485-259f1eb0727b", "name": "Structured Data Extractor", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [1280, -420], "parameters": {"text": "=Extract the Google Trend Data in JSON.\n\nHere's the content:\n\n {{ $json.text }}", "options": {}, "schemaType": "manual", "inputSchema": "{\n\t\"type\": \"array\",\n\t\"properties\": {\n\t\t\"topics\": {\n\t\t\t\"type\": \"string\"\n\t\t},\"desc\": {\n\t\t\t\"type\": \"string\"\n\t\t}\n\t}\n}"}, "typeVersion": 1}, {"id": "aa7b5dd7-53c7-4197-b2e8-886832cad82e", "name": "Summarize Google Trends", "type": "@n8n/n8n-nodes-langchain.chainSummarization", "position": [1760, -420], "parameters": {"options": {}, "chunkingMode": "advanced"}, "typeVersion": 2}, {"id": "25f0a115-ba3a-4ec6-8fe6-8e33e6302a2b", "name": "Initiate a Webhook Notification for Summarization", "type": "n8n-nodes-base.httpRequest", "position": [2200, -420], "parameters": {"url": "https://webhook.site/3c36d7d1-de1b-4171-9fd3-643ea2e4dd76", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "content", "value": "={{ $json.response.text }}"}]}}, "typeVersion": 4.2}, {"id": "50b55d73-5506-439c-8e82-e198f3b4f431", "name": "Write the file to disk", "type": "n8n-nodes-base.readWriteFile", "position": [2200, -640], "parameters": {"options": {}, "fileName": "d:\\google-trends.json", "operation": "write"}, "typeVersion": 1}, {"id": "a163f8d3-2b5c-48a5-8a1d-26c0caba6383", "name": "Google Gemini Chat Model for Summarization", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1860, -200], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "YeO7dHZnuGBVQKVZ", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "9e3db8e9-ad4c-4247-841e-1f5f4937b93c", "name": "Google Gemini Chat Model for Structured Data Extract", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1380, -200], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "YeO7dHZnuGBVQKVZ", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "122d3269-e932-48e0-af01-e2c421650e16", "name": "Send Summary to Gmail", "type": "n8n-nodes-base.gmail", "position": [2200, -160], "webhookId": "a57ca2f7-42dc-4ee9-808d-85455bb7c12f", "parameters": {"sendTo": "<EMAIL>", "message": "={{ $json.response.text }}", "options": {}, "subject": "Google Trends Summary"}, "credentials": {"gmailOAuth2": {"id": "WiMjt9PIpypF2dJF", "name": "Gmail account"}}, "typeVersion": 2.1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "bc73fbca-1218-47bd-93cf-b308b424894d", "connections": {"Create a binary data": {"main": [[{"node": "Write the file to disk", "type": "main", "index": 0}]]}, "Write the file to disk": {"main": [[]]}, "Summarize Google Trends": {"main": [[{"node": "Initiate a Webhook Notification for Summarization", "type": "main", "index": 0}, {"node": "Send Summary to Gmail", "type": "main", "index": 0}]]}, "Structured Data Extractor": {"main": [[{"node": "Create a binary data", "type": "main", "index": 0}, {"node": "Summarize Google Trends", "type": "main", "index": 0}]]}, "Set URL and Bright Data Zone": {"main": [[{"node": "Perform Bright Data Web Request", "type": "main", "index": 0}]]}, "Perform Bright Data Web Request": {"main": [[{"node": "Markdown to Textual Data Extractor", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set URL and Bright Data Zone", "type": "main", "index": 0}]]}, "Markdown to Textual Data Extractor": {"main": [[{"node": "Initiate a Webhook Notification for Markdown to Textual Data Extraction", "type": "main", "index": 0}, {"node": "Structured Data Extractor", "type": "main", "index": 0}]]}, "Google Gemini Chat Model for Data Extract": {"ai_languageModel": [[{"node": "Markdown to Textual Data Extractor", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini Chat Model for Summarization": {"ai_languageModel": [[{"node": "Summarize Google Trends", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini Chat Model for Structured Data Extract": {"ai_languageModel": [[{"node": "Structured Data Extractor", "type": "ai_languageModel", "index": 0}]]}}}