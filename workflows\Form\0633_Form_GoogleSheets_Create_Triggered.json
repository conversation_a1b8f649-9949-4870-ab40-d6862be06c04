{"nodes": [{"id": "4110f060-6945-4c52-9ea0-1dedb9309704", "name": "Add  to Waitlist Sheet", "type": "n8n-nodes-base.googleSheets", "position": [160, -440], "parameters": {"columns": {"value": {"Email": "={{ $json.Email }}", "Company": "={{ $json['Company Website'] }}", "Lastname": "={{ $json.Lastname }}", "Firstname": "={{ $json.Firstname }}", "Verification-Code": "={{ $json.code }}"}, "schema": [{"id": "Firstname", "type": "string", "display": true, "required": false, "displayName": "Firstname", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Lastname", "type": "string", "display": true, "required": false, "displayName": "Lastname", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Email", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Company", "type": "string", "display": true, "required": false, "displayName": "Company", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Verification-Code", "type": "string", "display": true, "required": false, "displayName": "Verification-Code", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Verified", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Verified", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Intended Use", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Intended Use", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["Email"]}, "options": {}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ydEoVn5uY36bEVXDmfdbj3Q-OabaPIqTifrzx49PTHA/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1ydEoVn5uY36bEVXDmfdbj3Q-OabaPIqTifrzx49PTHA", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ydEoVn5uY36bEVXDmfdbj3Q-OabaPIqTifrzx49PTHA/edit?usp=drivesdk", "cachedResultName": "n8n demo Waitlist"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "7508uyvd9qA3loJG", "name": "Demo Creds Sheets"}}, "typeVersion": 4.5}, {"id": "44bd9df4-5744-4beb-acfc-ad4c2d7a4359", "name": "Clean and Standardize", "type": "n8n-nodes-base.set", "position": [-320, -280], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "f17a256a-f7cc-444b-9a10-29ab471c0510", "name": "Email", "type": "string", "value": "={{ $json.Email.trim().toLowerCase() }}"}, {"id": "7c84b1f2-518b-4966-8dd1-594123a54e6e", "name": "Company Website", "type": "string", "value": "=https://{{ $json['Company Website'].toLowerCase().trim().trim('/').replace('https://','').replace('http://','') }}"}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "ba3db4e8-8622-4b9f-bf6e-bb563adcf4cc", "name": "Send Verification Email", "type": "n8n-nodes-base.emailSend", "position": [660, -300], "parameters": {"html": "=Hey {{ $json.Firstname }}\n\nThank you for your interest in joining the white list. To complete your registration, please verify your email address by using the code provided below:\n\nYour Verification Code: {{ $json.code }}\n\nPlease enter this code on the verification page to secure your spot on our waitlist.\n\nIf you didn’t request this email or believe it was sent to you by mistake, please ignore it.\n\nFor any questions or assistance, feel free to contact us.\n\nBest regards,\n[your name]\n\nNote: This is an automated message. Please do not reply directly to this email.", "options": {}, "subject": "Your Waitlist Verification Code", "toEmail": "={{ $json.Email }}", "fromEmail": "<EMAIL>"}, "credentials": {"smtp": {"id": "kiPWdk4KFJwOLaYT", "name": "<PERSON><PERSON> Automailer"}}, "typeVersion": 2.1, "alwaysOutputData": false}, {"id": "4fdc7af2-0739-40ab-a3b8-04394eab2732", "name": "Validate with Verification Code", "type": "n8n-nodes-base.form", "position": [880, -300], "webhookId": "15fbe5e4-88f8-4b74-8a29-eb1cac45c261", "parameters": {"options": {"formTitle": "Validate your Email", "buttonLabel": "Verify", "formDescription": "You should have received an Email with a Verification Code."}, "formFields": {"values": [{"fieldLabel": "Verification Code", "requiredField": true}]}}, "typeVersion": 1}, {"id": "2f764fe1-da60-4804-9caf-8eb3b2d15093", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-400, -540], "parameters": {"width": 740, "height": 520, "content": "## Adding to GSheet-List, Creating a OTP / Verification Code\n\n"}, "typeVersion": 1}, {"id": "c3168dc7-e25f-4d9c-9efe-8bfb46b14a09", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [580, -420], "parameters": {"color": 4, "width": 480, "height": 360, "content": "## Let the user enter the Verification Code\n"}, "typeVersion": 1}, {"id": "5bdf433e-d9e6-4e63-a995-9781ac21a07d", "name": "Get all Data from the Prev Form + Current", "type": "n8n-nodes-base.set", "position": [1240, -300], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "={{ $(\"Generate Random Verification Code\").item.json }}", "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "788d6847-25a0-4ea3-8dfb-50fed04a497d", "name": "Additional Data for the Sheet", "type": "n8n-nodes-base.form", "position": [2220, -400], "webhookId": "6bd68611-49e9-49f4-a470-4a2da66a29df", "parameters": {"options": {"formTitle": "Intended Use", "buttonLabel": "Submit", "formDescription": "What are you planing to Build with our Software?"}, "formFields": {"values": [{"fieldType": "textarea", "fieldLabel": "Use Case"}]}}, "typeVersion": 1}, {"id": "5fed2449-3225-4678-a35e-e7408fe3e1ea", "name": "Every Step Data", "type": "n8n-nodes-base.set", "position": [2420, -400], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "={{ $(\"Get all Data from the Prev Form + Current\").item.json }}", "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "92d2b42b-9190-48c1-92c1-34c2144bfdf9", "name": "is the Code correct?", "type": "n8n-nodes-base.if", "position": [1420, -300], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "e2fe68a3-f1df-4912-af93-393a046b9114", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json['Verification Code'] }}", "rightValue": "={{ $json.code }}"}]}}, "typeVersion": 2.2}, {"id": "ce161a0a-aec4-40db-97c0-5ce53<PERSON><PERSON>acac", "name": "Let the User Reenter Code", "type": "n8n-nodes-base.form", "position": [1640, -220], "webhookId": "9a39ad9a-8c7d-445f-93e4-9af472678d38", "parameters": {"options": {"formTitle": "Code was not valid", "buttonLabel": "Verify", "formDescription": "Please enter your Verification Code and try again."}, "formFields": {"values": [{"fieldLabel": "Verification Code", "requiredField": true}]}}, "typeVersion": 1}, {"id": "008ed28c-2af3-4006-987e-9e083e72f10b", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [400, -300], "parameters": {"mode": "chooseBranch", "useDataOfInput": 2}, "typeVersion": 3}, {"id": "099e9089-ea39-4d67-a1ec-c063257c8cb0", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1160, -440], "parameters": {"color": 2, "width": 680, "height": 480, "content": "## Verification Loop"}, "typeVersion": 1}, {"id": "073574ce-f55c-4b01-a4a1-18171c4647c5", "name": "Save Intend to List", "type": "n8n-nodes-base.googleSheets", "position": [2620, -400], "parameters": {"columns": {"value": {"Email": "={{ $json.Email }}", "Intended Use": "={{ $json['Use Case'] }}"}, "schema": [{"id": "Firstname", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Firstname", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Lastname", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Lastname", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Email", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Company", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Company", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Verification-Code", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Verification-Code", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Verified", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Verified", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Intended Use", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Intended Use", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["Email"]}, "options": {}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ydEoVn5uY36bEVXDmfdbj3Q-OabaPIqTifrzx49PTHA/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1ydEoVn5uY36bEVXDmfdbj3Q-OabaPIqTifrzx49PTHA", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ydEoVn5uY36bEVXDmfdbj3Q-OabaPIqTifrzx49PTHA/edit?usp=drivesdk", "cachedResultName": "n8n demo Waitlist"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "7508uyvd9qA3loJG", "name": "Demo Creds Sheets"}}, "typeVersion": 4.5}, {"id": "e1a4618c-4a58-4ed0-bbad-68c8af3fba5d", "name": "Save as Verified", "type": "n8n-nodes-base.googleSheets", "position": [1960, -400], "parameters": {"columns": {"value": {"Email": "={{ $json.Email }}", "Verified": "true"}, "schema": [{"id": "Firstname", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Firstname", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Lastname", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Lastname", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Email", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Company", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Company", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Verification-Code", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Verification-Code", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Verified", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Verified", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Intended Use", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Intended Use", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["Email"]}, "options": {}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ydEoVn5uY36bEVXDmfdbj3Q-OabaPIqTifrzx49PTHA/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1ydEoVn5uY36bEVXDmfdbj3Q-OabaPIqTifrzx49PTHA", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ydEoVn5uY36bEVXDmfdbj3Q-OabaPIqTifrzx49PTHA/edit?usp=drivesdk", "cachedResultName": "n8n demo Waitlist"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "7508uyvd9qA3loJG", "name": "Demo Creds Sheets"}}, "typeVersion": 4.5}, {"id": "1e48dc65-18ba-45b4-a3f1-7a9298697596", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [2160, -500], "parameters": {"color": 4, "width": 640, "height": 340, "content": "## Last Page, let them add some details and save them"}, "typeVersion": 1}, {"id": "9f899bac-9a8f-4659-a90f-b9835f5abc51", "name": "Generate Random Verification Code", "type": "n8n-nodes-base.crypto", "position": [-60, -280], "parameters": {"action": "generate", "encodingType": "hex", "stringLength": 6, "dataPropertyName": "code"}, "typeVersion": 1}, {"id": "f009aec4-c640-4a85-9417-98c4938db380", "name": "Waitlist Form", "type": "n8n-nodes-base.formTrigger", "position": [-560, -280], "webhookId": "b1fac105-169a-47b9-83b7-8ed52edb3209", "parameters": {"options": {"path": "demo-waitlist-2"}, "formTitle": "Waitlist Form", "formFields": {"values": [{"fieldLabel": "Firstname", "requiredField": true}, {"fieldLabel": "Lastname", "requiredField": true}, {"fieldType": "email", "fieldLabel": "Email", "placeholder": "<EMAIL>", "requiredField": true}, {"fieldLabel": "Company Website", "placeholder": "https://my-company.com"}]}, "responseMode": "lastNode", "formDescription": "Thank you for the interest in our Service!\nJoin our waitlist to be one of the first users getting access to our service!"}, "typeVersion": 2.2}, {"id": "1a71859d-24a1-4f2c-a7ff-3cb7e6a1f522", "name": "Note3", "type": "n8n-nodes-base.stickyNote", "position": [-1320, -620], "parameters": {"width": 668, "height": 786, "content": "## Instructions\n\nThis automation streamlines the process of **collecting user information** using a Form Node, enabling individuals to join a **waitlist managed via Google Sheets.**\n\nIt also **generates a verification code**, prompting users to input this code in the Second Form Step. If the code is _nvalid, the workflow keeps the user in a verification loop until a valid code is entered.\n\nOnce a **valid code is provided**, the workflow proceeds to the final step, where **additional information** can be collected.\n\nAll e**ntered data and intermediate states** are securely **stored in the Google Sheet**.\n\n### Structure of the GoogleSheet\n\nFirstname | Lastname | Email | Company | Verification-Code | Verified | Intended Use\nMarcel | Claus-Ahrens | foo[at]bar| foobar | abc123 | TRUE | Just testing\n\n### Setup\n\n1. Set Up a Google Sheet: Create a Google Sheet with the specified columns, or customize them to suit your needs.\n2. Verify the \"Send Mail\" Node: Ensure your \"Send Mail\" node is functional, or replace it with another email-sending node.\n3. Customize Texts and Fields: Update email content, form texts, and form fields to align with your specific use case.\n4. Done!\n\n![Image](https://cloud.let-the-work-flow.com/logo-64.png)  \nEnjoy the workflow! ❤️  \n[let the workf low](https://let-the-work-flow.com) — Workflow Automation & Development"}, "typeVersion": 1}], "pinData": {}, "connections": {"Merge": {"main": [[{"node": "Send Verification Email", "type": "main", "index": 0}]]}, "Waitlist Form": {"main": [[{"node": "Clean and Standardize", "type": "main", "index": 0}]]}, "Every Step Data": {"main": [[{"node": "Save Intend to List", "type": "main", "index": 0}]]}, "Save as Verified": {"main": [[{"node": "Additional Data for the Sheet", "type": "main", "index": 0}]]}, "is the Code correct?": {"main": [[{"node": "Save as Verified", "type": "main", "index": 0}], [{"node": "Let the User Reenter Code", "type": "main", "index": 0}]]}, "Clean and Standardize": {"main": [[{"node": "Generate Random Verification Code", "type": "main", "index": 0}]]}, "Add  to Waitlist Sheet": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Send Verification Email": {"main": [[{"node": "Validate with Verification Code", "type": "main", "index": 0}]]}, "Let the User Reenter Code": {"main": [[{"node": "Get all Data from the Prev Form + Current", "type": "main", "index": 0}]]}, "Additional Data for the Sheet": {"main": [[{"node": "Every Step Data", "type": "main", "index": 0}]]}, "Validate with Verification Code": {"main": [[{"node": "Get all Data from the Prev Form + Current", "type": "main", "index": 0}]]}, "Generate Random Verification Code": {"main": [[{"node": "Add  to Waitlist Sheet", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Get all Data from the Prev Form + Current": {"main": [[{"node": "is the Code correct?", "type": "main", "index": 0}]]}}}