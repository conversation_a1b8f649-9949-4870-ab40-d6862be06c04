# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Application specific
database/workflows.db
database/workflows.db-*
*.log

# Temporary files
*.tmp
*.temp

# Development artifacts
*.log
*.tmp
temp/
tmp/
.cache/

# Documentation artifacts (generated)
workflow-documentation.html

# Test files
test_*.json
*_test.json

# Backup files
*.bak
*.backup

# Workflow backup directories (created during renaming)
workflow_backups/

# Database files (SQLite)
*.db
*.sqlite
*.sqlite3

# Rename logs
workflow_rename_log.json

# Node.js artifacts (if using npm)
node_modules/
package-lock.json

#db
*.db-shm
*.db-wal

# versions
.python-version

# Claude Code local settings (created during development)
.claude/settings.local.json