{"nodes": [{"name": "Data 1", "type": "n8n-nodes-base.function", "position": [602, 350], "parameters": {"functionCode": "items[0].json = {\n\"data\": [\n{\n\"pointer\": \"12345\",\n\"panel\": \"234234\",\n\"subject\": \"Blah Blah\",\n\"note\": \"\",\n\"interviewers\": [\n{\n\"id\": \"111222333\",\n\"name\": \"<PERSON>\",\n\"email\": \"<EMAIL>\"\n}\n],\n\"timezone\": \"America/Los_Angeles\",\n},\n{\n\"pointer\": \"98754\",\n\"panel\": \"3243234\",\n\"subject\": \"Yadda Yadda\",\n\"note\": \"\",\n\"interviewers\": [\n{\n\"id\": \"444555666\",\n\"name\": \"<PERSON>\",\n\"email\": \"<EMAIL>\"\n}\n],\n\"timezone\": \"America/Los_Angeles\",\n},\n],\n\"hasNext\": false\n};\nreturn items;\n"}, "typeVersion": 1}, {"name": "Data 2", "type": "n8n-nodes-base.function", "position": [602, 550], "parameters": {"functionCode": "items[0].json = [\n{\n\"name\": \"test\",\n\"fields\": {\n\"FirstName\": \"<PERSON>\",\n\"LastName\": \"<PERSON>\",\n\"JobTitleDescription\": \"Recruiter\",\n\"HomeDepartmentDescription\": \"Recruiting Team\",\n\"Photo\": [\n{\n\"x\": \"attPuc6gAIHUOHjsY\",\n\"url\": \"http://urlto.com/BobbyPhoto.jpg\",\n\"filename\": \"photo.jpg\",\n\"size\": 28956,\n\"type\": \"image/jpeg\"\n}\n],\n\"eid\": \"111222333\"\n},\n\"createdTime\": \"2019-09-23T04:06:48.000Z\"\n},\n{\n\"name\": \"test2\",\n\"fields\": {\n\"FirstName\": \"<PERSON>\",\n\"LastName\": \"<PERSON>\",\n\"JobTitleDescription\": \"CEO\",\n\"HomeDepartmentDescription\": \"Boss Team\",\n\"Photo\": [\n{\n\"x\": \"attPuc6gAIHUOHjsY\",\n\"url\": \"http://urlto.com/BillyPhoto.jpg\",\n\"filename\": \"photo.jpg\",\n\"size\": 28956,\n\"type\": \"image/jpeg\"\n}\n],\n\"eid\": \"444555666\"\n},\n\"createdTime\": \"2019-09-23T04:06:48.000Z\"\n}\n,\n{\n\"name\": \"test3\",\n\"fields\": {\n\"FirstName\": \"Susan\",\n\"LastName\": \"Smith\",\n\"JobTitleDescription\": \"CFO\",\n\"HomeDepartmentDescription\": \"Boss Team\",\n\"Photo\": [\n{\n\"x\": \"attPuc6gAIHUOHjsY\",\n\"url\": \"http://urlto.com/SusanPhoto.jpg\",\n\"filename\": \"photo.jpg\",\n\"size\": 28956,\n\"type\": \"image/jpeg\"\n}\n],\n\"eid\": \"777888999\"\n},\n\"createdTime\": \"2019-09-23T04:06:48.000Z\"\n}\n];\nreturn items;"}, "typeVersion": 1}, {"name": "Convert Data 1", "type": "n8n-nodes-base.function", "position": [752, 350], "parameters": {"functionCode": "const newItems = [];\n\nfor (const item of items[0].json.data) {\n  newItems.push({ json: item });\n}\n\nreturn newItems;"}, "typeVersion": 1}, {"name": "Convert Data 2", "type": "n8n-nodes-base.function", "position": [752, 550], "parameters": {"functionCode": "const newItems = [];\n\nfor (const item of items[0].json) {\n  newItems.push({ json: item });\n}\n\nreturn newItems;"}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [990, 430], "parameters": {"mode": "mergeByKey", "propertyName1": "interviewers[0].id", "propertyName2": "fields.eid"}, "typeVersion": 1}], "connections": {"Data 1": {"main": [[{"node": "Convert Data 1", "type": "main", "index": 0}]]}, "Data 2": {"main": [[{"node": "Convert Data 2", "type": "main", "index": 0}]]}, "Convert Data 1": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Convert Data 2": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}}}