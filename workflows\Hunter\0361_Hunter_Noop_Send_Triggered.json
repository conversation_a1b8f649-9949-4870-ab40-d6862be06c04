{"id": "1blBTEfOEjamDB0N", "meta": {"instanceId": "558d88703fb65b2d0e44613bc35916258b0f0bf983c5d4730c00c424b77ca36a", "templateCredsSetupCompleted": true}, "name": "Email form", "tags": [], "nodes": [{"id": "0994dde9-bad8-49b8-b164-1f191decf9ff", "name": "Email is not valid, do nothing", "type": "n8n-nodes-base.noOp", "position": [940, 480], "parameters": {}, "typeVersion": 1}, {"id": "b27e140e-7758-42d4-bf07-39b17f85fc82", "name": "Check if the email is valid", "type": "n8n-nodes-base.if", "position": [620, 260], "parameters": {"options": {}, "conditions": {"options": {"version": 1, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "54d84c8a-63ee-40ed-8fb2-301fff0194ba", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "valid"}]}}, "typeVersion": 2}, {"id": "a691af9a-f66f-4fd1-ab82-3d3450098d67", "name": "Verify email", "type": "n8n-nodes-base.hunter", "position": [360, 260], "parameters": {"email": "={{ $json.Email }}", "operation": "emailVerifier"}, "credentials": {"hunterApi": {"id": "wC6eWJWcNeFHvBqV", "name": "Hunter account"}}, "typeVersion": 1}, {"id": "cfe4d91b-209c-49df-8483-141f5e27fba2", "name": "Submit form", "type": "n8n-nodes-base.formTrigger", "position": [80, 260], "webhookId": "80be3272-e1bc-47e4-8112-d39488e84f4b", "parameters": {"options": {}, "formTitle": "Join my mailing list now", "formFields": {"values": [{"fieldLabel": "Email", "requiredField": true}]}, "formDescription": "10x your productivity with my A.I. tips. I'll cut the B.S. and give you the most practical tips for A.I. automation."}, "typeVersion": 2.2}, {"id": "30d816d9-7a91-47b2-8c06-da0b9114f375", "name": "Add contact to list", "type": "n8n-nodes-base.sendGrid", "position": [940, 240], "parameters": {"email": "={{ $json.Email }}", "resource": "contact", "additionalFields": {"listIdsUi": {"listIdValues": {"listIds": ["11a55438-d4a8-4740-b054-d273359b7dfe"]}}}}, "credentials": {"sendGridApi": {"id": "AFtBIAiI3x5QS0WL", "name": "SendGrid account"}}, "typeVersion": 1}, {"id": "e80255c8-25b2-48d5-8605-d7702cbf7bc7", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [60, -100], "parameters": {"width": 505, "height": 180, "content": "## Automate Email List Building with n8<PERSON> and <PERSON> io\n\n💡 Read the [case study here](https://rumjahn.com/create-email-capture-forms-for-free-using-n8n-and-sendgrid-and-easily-grow-your-subscriber-list/).\n\n📺 Watch the [youtube tutorial here](https://www.youtube.com/watch?v=NgvEHwu19Rs&t=2s)\n\n"}, "typeVersion": 1}, {"id": "f989d552-81b9-4ee7-aa28-a006b703280f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [300, 100], "parameters": {"color": 4, "height": 320, "content": "## Hunter io\n\nYou need to get a Hunter.io account and input the API key. There's 50 free credits per month."}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "1df322f8-6d69-4ae7-b094-3f0dec019d3b", "connections": {"Submit form": {"main": [[{"node": "Verify email", "type": "main", "index": 0}]]}, "Verify email": {"main": [[{"node": "Check if the email is valid", "type": "main", "index": 0}]]}, "Check if the email is valid": {"main": [[{"node": "Add contact to list", "type": "main", "index": 0}], [{"node": "Email is not valid, do nothing", "type": "main", "index": 0}]]}}}