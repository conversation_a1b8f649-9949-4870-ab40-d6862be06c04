{"nodes": [{"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.githubTrigger", "position": [500, 350], "parameters": {"owner": "n8n-io", "events": ["star"], "repository": "n8n"}, "credentials": {"githubApi": ""}, "typeVersion": 1}, {"name": "IF", "type": "n8n-nodes-base.if", "position": [700, 350], "parameters": {"conditions": {"string": [{"value1": "={{$node[\"Github Trigger\"].data[\"body\"][\"action\"]}}", "value2": "created"}]}}, "typeVersion": 1}, {"name": "Slack - Add", "type": "n8n-nodes-base.slack", "position": [900, 250], "parameters": {"channel": "#general", "attachments": [{"text": "=The project has now: {{$node[\"Github Trigger\"].data[\"body\"][\"repository\"][\"stargazers_count\"]}} Stars", "color": "#88FF00", "title": "=Got new star from: {{$node[\"Github Trigger\"].data[\"body\"][\"sender\"][\"login\"]}}", "image_url": "={{$node[\"Github Trigger\"].data[\"body\"][\"sender\"][\"avatar_url\"]}}", "title_link": "={{$node[\"Github Trigger\"].data[\"body\"][\"sender\"][\"html_url\"]}}"}], "otherOptions": {}}, "credentials": {"slackApi": ""}, "typeVersion": 1}, {"name": "Slack - Remove", "type": "n8n-nodes-base.slack", "position": [900, 450], "parameters": {"channel": "#general", "attachments": [{"text": "=The project has now: {{$node[\"Github Trigger\"].data[\"body\"][\"repository\"][\"stargazers_count\"]}} Stars", "color": "#ff0000", "title": "=Star got removed by: {{$node[\"Github Trigger\"].data[\"body\"][\"sender\"][\"login\"]}}", "image_url": "={{$node[\"Github Trigger\"].data[\"body\"][\"sender\"][\"avatar_url\"]}}", "title_link": "={{$node[\"Github Trigger\"].data[\"body\"][\"sender\"][\"html_url\"]}}"}], "otherOptions": {}}, "credentials": {"slackApi": ""}, "typeVersion": 1}], "connections": {"IF": {"main": [[{"node": "Slack - Add", "type": "main", "index": 0}], [{"node": "Slack - Remove", "type": "main", "index": 0}]]}, "Github Trigger": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}}}