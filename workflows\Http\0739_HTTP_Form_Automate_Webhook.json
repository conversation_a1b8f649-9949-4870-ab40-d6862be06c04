{"id": "2DT5BW5tOdy87AUl", "meta": {"instanceId": "8418cffce8d48086ec0a73fd90aca708aa07591f2fefa6034d87fe12a09de26e"}, "name": "Streamline Your Zoom Meetings with Secure, Automated Stripe Payments", "tags": [], "nodes": [{"id": "fcc38ae8-0dbf-4676-b47b-ba77f97a38b8", "name": "Create Zoom meeting", "type": "n8n-nodes-base.zoom", "position": [180, 480], "parameters": {"topic": "={{ $('Creation Form').item.json.title }}", "authentication": "oAuth2", "additionalFields": {"password": "={{ Math.random().toString(36).slice(-4); }}", "startTime": "={{ new Date(new Date($('Creation Form').item.json.date_start).getTime() + ($('Creation Form').item.json.hour * 3600000) + ($('Creation Form').item.json.minute * 60000)).toISOString() }}"}}, "credentials": {"zoomOAuth2Api": {"id": "JQ9fG5WNTVssHxGj", "name": "Zoom account"}}, "typeVersion": 1}, {"id": "3d2dea09-c463-447b-9a9d-daca8fdcac06", "name": "Create Stripe Product", "type": "n8n-nodes-base.httpRequest", "position": [400, 480], "parameters": {"url": "https://api.stripe.com/v1/products", "method": "POST", "options": {}, "sendBody": true, "contentType": "form-urlencoded", "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "name", "value": "={{ $('Creation Form').item.json.title }}"}, {"name": "default_price_data[unit_amount]", "value": "={{ $('Creation Form').item.json.price * 100 }}"}, {"name": "default_price_data[currency]", "value": "={{ $('Config').item.json.currency }}"}]}, "nodeCredentialType": "stripeApi"}, "credentials": {"stripeApi": {"id": "qjose8z3RR7Xzm7b", "name": "Stripe Dev"}}, "typeVersion": 4.1}, {"id": "01ab74fb-19a1-42ef-a0ad-31107c7ded3f", "name": "Config", "type": "n8n-nodes-base.set", "notes": "Setup your flow", "position": [-220, 640], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "038b54b7-9559-444e-8653-c5256a5b784e", "name": "currency", "type": "string", "value": "EUR"}, {"id": "64d1eeee-cabe-403b-a634-f3238f586f58", "name": "sheet_url", "type": "string", "value": "https://docs.google.com/spreadsheets/d/1ZliqqBNo6X0iM9yXBOiCG1e4Q7L7bQKMFmjvbSgUSnA/edit#gid=0"}, {"id": "997fe5a1-f601-458d-899c-673dff4acb04", "name": "teacher_email", "type": "string", "value": "<EMAIL>"}]}}, "notesInFlow": true, "typeVersion": 3.3}, {"id": "2aa87b96-924b-472c-8cc6-2de028ce0195", "name": "Send email to teacher", "type": "n8n-nodes-base.gmail", "position": [1040, 480], "parameters": {"sendTo": "={{ $('Config').item.json.teacher_email }}", "message": "=<b>Congratulations, your event has been succesfully created 🎉</b><br/><br/>\n\nTitle: {{ $('Creation Form').item.json.title }}<br/>\nPrice:  {{ $('Creation Form').item.json.price }} {{ $('Config').item.json.currency }}<br/>\nStart date: {{ $('Creation Form').item.json.date_start }}<br/><br/>\n\n<b>Payment link:</b><br/>\n {{ $('Create payment link').item.json.url }}<br/>\n<i>Start sharing this link to get subscriptions</i><br/><br/>\n<b>Participant list:</b><br/>\n{{ $('Config').item.json.sheet_url }}#gid={{ $('Create Stripe Product').item.json.created }}\n<br/><br/>\n<b>Zoom infos:</b><br/>\nLink: {{ $('Create Zoom meeting').item.json.join_url }}<br/>\nSession ID: {{ $('Create Zoom meeting').item.json.id }}<br/>\nPassword: {{ $('Create Zoom meeting').item.json.password }}<br/> ", "options": {}, "subject": "=🎉 {{ $('Creation Form').item.json.title }} has been created!"}, "credentials": {"gmailOAuth2": {"id": "DMcPDN0IHPwGmI7f", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "40f66f09-19c9-40eb-a9c4-138464ccd371", "name": "Create participant list", "type": "n8n-nodes-base.googleSheets", "position": [840, 480], "parameters": {"title": "={{ $('Creation Form').item.json.date_start }} - {{ $('Creation Form').item.json.title }} - {{ $('Create Stripe Product').item.json.created }}", "options": {"index": 0, "sheetId": "={{ $('Create Stripe Product').item.json.created }}"}, "operation": "create", "documentId": {"__rl": true, "mode": "url", "value": "={{ $('Config').item.json.sheet_url }}"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "RICzFHixgHXMuKmg", "name": "Google Sheets account"}}, "typeVersion": 4.3, "alwaysOutputData": true}, {"id": "67ff21d2-57b8-4ccd-91ee-a1bff1ea23b2", "name": "Add participant to list", "type": "n8n-nodes-base.googleSheets", "position": [400, 800], "parameters": {"columns": {"value": {}, "schema": [{"id": "city", "type": "string", "display": true, "removed": false, "required": false, "displayName": "city", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "email", "type": "string", "display": true, "removed": false, "required": false, "displayName": "email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "country", "type": "string", "display": true, "removed": false, "required": false, "displayName": "country", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "postal_code", "type": "string", "display": true, "removed": false, "required": false, "displayName": "postal_code", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "amount", "type": "string", "display": true, "removed": false, "required": false, "displayName": "amount", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "currency", "type": "string", "display": true, "removed": false, "required": false, "displayName": "currency", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": []}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "id", "value": "={{ $('On payment').item.json.data.object.metadata.event_sheet_id }}"}, "documentId": {"__rl": true, "mode": "url", "value": "={{ $('Config').item.json.sheet_url }}"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "RICzFHixgHXMuKmg", "name": "Google Sheets account"}}, "typeVersion": 4.3}, {"id": "67e317ba-77d5-4f77-8fe2-d38e1a68c6f1", "name": "Send confirmation to participant", "type": "n8n-nodes-base.gmail", "position": [620, 800], "parameters": {"sendTo": "={{ $('On payment').item.json.data.object.customer_details.email }}", "message": "=Dear {{ $('On payment').item.json.data.object.customer_details.name }},<br/><br/>\n\nWe are very happy to announce that your subscription to our event <b>{{ $json.title }}</b> starting on <b>{{ $json.start }}</b> is now confirmed.<br/><br/>\n\nHere are the infos you will need to participate:<br/> \nZoom link:  {{ $('On payment').item.json.data.object.metadata.zoom_link }}<br/>\nZoom password:{{ $('On payment').item.json.data.object.metadata.zoom_password }}<br/>\nZoom ID: {{ $('On payment').item.json.data.object.metadata.zoom_id }}<br/><br/> \n\nLooking forward to see you there!<br/>\nKind regards<br/>", "options": {"appendAttribution": false}, "subject": "Than you for your subscription 🙏"}, "credentials": {"gmailOAuth2": {"id": "DMcPDN0IHPwGmI7f", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "ac5ca5f3-f9ca-494f-8e78-33dd663111ab", "name": "Notify teacher", "type": "n8n-nodes-base.gmail", "position": [840, 800], "parameters": {"sendTo": "={{ $('Config').item.json.teacher_email }}", "message": "=<b>A new participant registred for the event {{ $('Retrieve event infos').item.json.title }} ({{ $('Retrieve event infos').item.json.start }})!</b><br/><br/>\n\n<b>Name: {{ $('On payment').item.json.data.object.customer_details.name }}</b><br/>\n<b>Email: {{ $('On payment').item.json.data.object.customer_details.email }}</b><br/><br/>\n\n<b>Participant list:</b><br/>\n{{ $('Config').item.json.sheet_url }}#gid={{ $('On payment').item.json.data.object.metadata.event_sheet_id }} ", "options": {}, "subject": "New participant registred ☝️"}, "credentials": {"gmailOAuth2": {"id": "DMcPDN0IHPwGmI7f", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "33e5283f-3854-4ada-8412-858c205f1d1e", "name": "Create payment link", "type": "n8n-nodes-base.httpRequest", "position": [620, 480], "parameters": {"url": "https://api.stripe.com/v1/payment_links", "method": "POST", "options": {}, "sendBody": true, "contentType": "form-urlencoded", "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "line_items[0][price]", "value": "={{ $json.default_price }}"}, {"name": "line_items[0][quantity]", "value": "1"}, {"name": "metadata[event_sheet_id]", "value": "={{ $('Create Stripe Product').item.json.created }}"}, {"name": "metadata[zoom_link]", "value": "={{ $('Create Zoom meeting').item.json.join_url }}"}, {"name": "metadata[zoom_password]", "value": "={{ $('Create Zoom meeting').item.json.password }}"}, {"name": "metadata[zoom_id]", "value": "={{ $('Create Zoom meeting').item.json.id }}"}, {"name": "metadata[title]", "value": "={{ $('Creation Form').item.json.title }}"}, {"name": "metadata[start_time]", "value": "={{ $('Create Zoom meeting').item.json.start_time }}"}, {"name": "metadata[price]", "value": "={{ $('Creation Form').item.json.price }}"}, {"name": "metadata[currency]", "value": "={{ $('Config').item.json.currency }}"}]}, "nodeCredentialType": "stripeApi"}, "credentials": {"stripeApi": {"id": "qjose8z3RR7Xzm7b", "name": "Stripe Dev"}}, "typeVersion": 4.1}, {"id": "600c5382-bdac-4131-a784-399f5be2b54b", "name": "Format participant", "type": "n8n-nodes-base.set", "position": [180, 800], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "dabd3bc2-ca92-4d99-a223-b0ad18945121", "name": "email", "type": "string", "value": "={{ $('On payment').item.json.data.object.customer_details.email }}"}, {"id": "d40709f6-ffcd-4055-a374-9044a9a5e3b2", "name": "name", "type": "string", "value": "={{ $('On payment').item.json.data.object.customer_details.name }}"}]}}, "typeVersion": 3.3}, {"id": "c8a90ac5-14cd-4ff2-bd5b-c35724f085d1", "name": "Format event", "type": "n8n-nodes-base.set", "position": [840, 280], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "a29943ba-b516-41a8-8f85-5bcee5eda0d1", "name": "title", "type": "string", "value": "={{ $('Creation Form').item.json.title }}"}, {"id": "bf642fde-c4c2-42b4-beed-ef65efdab55b", "name": "start", "type": "string", "value": "={{ $('Creation Form').item.json.date_start }}"}, {"id": "33f7a58e-624d-4ccc-bbea-ed3365cede20", "name": "price", "type": "number", "value": "={{ $('Creation Form').item.json.price }}"}, {"id": "c948f71e-3b12-4c6a-a1f9-ee9a511fe262", "name": "currency", "type": "string", "value": "={{ $('Config').item.json.currency }}"}, {"id": "887461ca-db0d-442e-8008-5fe6a6fbdd8f", "name": "zoom_link", "type": "string", "value": "={{ $('Create Zoom meeting').item.json.join_url }}"}, {"id": "4b2bd5e2-3bd5-443a-94a3-9ababfd9d881", "name": "zoom_id", "type": "string", "value": "={{ $('Create Zoom meeting').item.json.id }}"}, {"id": "a1cea8e2-9954-4143-b71f-5ea194a873dd", "name": "zoom_password", "type": "string", "value": "={{ $('Create Zoom meeting').item.json.password }}"}, {"id": "faa52bc6-dfbe-49e2-bc95-dae198a61293", "name": "payment_link", "type": "string", "value": "={{ $json.url }}"}, {"id": "d7f5f0f5-cc7b-436a-9ad1-0b8f410c62c6", "name": "payment_id", "type": "string", "value": "={{ $json.id }}"}, {"id": "020b22d0-f525-4120-9f8b-2fa33e88c2e1", "name": "event_sheet_id", "type": "string", "value": "={{ $json.metadata.event_sheet_id }}"}]}}, "typeVersion": 3.3}, {"id": "def10b04-98c3-46cc-bdeb-9592c7466992", "name": "Store event", "type": "n8n-nodes-base.googleSheets", "position": [1040, 280], "parameters": {"columns": {"value": {}, "schema": [], "mappingMode": "autoMapInputData", "matchingColumns": []}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "id", "value": "0"}, "documentId": {"__rl": true, "mode": "url", "value": "={{ $('Config').item.json.sheet_url }}"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "RICzFHixgHXMuKmg", "name": "Google Sheets account"}}, "typeVersion": 4.3, "alwaysOutputData": true}, {"id": "594fc7a1-f299-49c4-a25b-07cf2ced16f7", "name": "Creation Form", "type": "n8n-nodes-base.formTrigger", "position": [-500, 480], "webhookId": "1c6fe52c-48ab-4688-b5ae-7e24361aa603", "parameters": {"path": "1c6fe52c-48ab-4688-b5ae-7e24361aa602", "options": {}, "formTitle": "Create a new meeting", "formFields": {"values": [{"fieldLabel": "title", "requiredField": true}, {"fieldType": "number", "fieldLabel": "price", "requiredField": true}, {"fieldType": "date", "fieldLabel": "date_start", "requiredField": true}, {"fieldType": "number", "fieldLabel": "hour"}, {"fieldType": "number", "fieldLabel": "minute"}]}, "responseMode": "lastNode", "formDescription": "This automates the creation of a Zoom Meeting and a Stripe Payment page, streamlining your event setup process."}, "typeVersion": 2}, {"id": "18fec11b-da39-4fe2-afab-d1585e3d9a99", "name": "On payment", "type": "n8n-nodes-base.stripeTrigger", "disabled": true, "position": [-500, 780], "webhookId": "ee7d6932-0583-47a3-b442-8bc161eee5e9", "parameters": {"events": ["checkout.session.completed"]}, "credentials": {"stripeApi": {"id": "qjose8z3RR7Xzm7b", "name": "Stripe Dev"}}, "typeVersion": 1}, {"id": "1d95a7a5-7ddc-4338-9784-1d0554f39808", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-220, 118], "parameters": {"color": 6, "width": 275.01592825011585, "height": 468.76027109756643, "content": "# Setup\n### 1/ Add Your credentials\n[Zoom](https://docs.n8n.io/integrations/builtin/credentials/zoom/)\n[Google](https://docs.n8n.io/integrations/builtin/credentials/google/)\n[Stripe](https://docs.n8n.io/integrations/builtin/credentials/stripe/)\n\nNote: For Google, you need to add Gmail and Google Sheet.\n\n### 2/ Create a [new Google Sheet](https://sheets.new/).\nKeep this sheet blank for now; it contains your meeting and participant information. Place it wherever it fits best in your organization.\n\n### 3/ And fill the config node\n# 👇"}, "typeVersion": 1}, {"id": "58312523-1bee-4a56-9ab2-dc166fe30573", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-920, 500], "parameters": {"color": 6, "width": 372, "height": 200.14793114506386, "content": "# Create a meeting 👉🏻\n\nYour journey to easy event management starts here.\n\nClick this node, copy the production URL, and keep it handy. It's your personal admin tool for quickly creating new meetings. Simple and efficient!"}, "typeVersion": 1}, {"id": "09153c6b-33cb-4fd1-8fa2-3513bca01f0c", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [620, 660], "parameters": {"color": 6, "width": 519.9859025074911, "height": 106.11515926602786, "content": "# 🖋️ Customize\n### Feel free to adapt email contents to your needs."}, "typeVersion": 1}, {"id": "da13aadc-eb3c-4d99-8e2b-3e56a40d09f3", "name": "if is creation flow", "type": "n8n-nodes-base.if", "position": [-20, 640], "parameters": {"options": {"looseTypeValidation": true}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "loose"}, "combinator": "and", "conditions": [{"id": "40ddf809-1602-4120-ae7e-8be61437b50d", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $(\"Creation Form\").isExecuted }}", "rightValue": ""}]}}, "typeVersion": 2}, {"id": "ca62dd52-cb79-45c1-a26a-91ba4c16b6ed", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [180, 340], "parameters": {"color": 7, "width": 202.64787116404852, "height": 85.79488430601403, "content": "### Crafted by the\n## [🥷 n8n.ninja](https://n8n.ninja)"}, "typeVersion": 1}, {"id": "aebdc1b5-ccf7-4299-a8ec-10eb448c4d72", "name": "the end", "type": "n8n-nodes-base.noOp", "position": [1040, 800], "parameters": {}, "typeVersion": 1}], "active": true, "pinData": {"On payment": [{"json": {"id": "evt_1Ou0e4BH8XCwzsfXEKVN0GkI", "data": {"object": {"id": "cs_test_a1G73c0pSu8hnD8y4we2ZVGy3MdmDuam1jLT07DqcBgYkuH1vOpWSkclBr", "url": null, "mode": "payment", "locale": "auto", "object": "checkout.session", "status": "complete", "consent": null, "created": 1710370285, "invoice": null, "ui_mode": "hosted", "currency": "eur", "customer": null, "livemode": false, "metadata": {"zoom_id": "86579738722", "zoom_link": "https://us06web.zoom.us/j/86579738722?pwd=i8QeOxKGO8GODInTP3gsYUjvrCYarA.1", "zoom_password": "260j", "event_sheet_id": "1710369993"}, "shipping": null, "cancel_url": "https://stripe.com", "expires_at": 1710456685, "custom_text": {"submit": null, "after_submit": null, "shipping_address": null, "terms_of_service_acceptance": null}, "submit_type": "auto", "success_url": "https://stripe.com", "amount_total": 2000, "payment_link": "plink_1Ou0ZCBH8XCwzsfXUongWL67", "setup_intent": null, "subscription": null, "automatic_tax": {"status": null, "enabled": false, "liability": null}, "client_secret": null, "custom_fields": [], "shipping_rate": null, "total_details": {"amount_tax": 0, "amount_discount": 0, "amount_shipping": 0}, "customer_email": null, "payment_intent": "pi_3Ou0e2BH8XCwzsfX14Vi1Pak", "payment_status": "paid", "recovered_from": null, "amount_subtotal": 2000, "after_expiration": null, "customer_details": {"name": "<PERSON>", "email": "<EMAIL>", "phone": null, "address": {"city": "Lausanne", "line1": "Avenue Charles Dickens 10", "line2": null, "state": null, "country": "CH", "postal_code": "1006"}, "tax_ids": [], "tax_exempt": "none"}, "invoice_creation": {"enabled": false, "invoice_data": {"footer": null, "issuer": null, "metadata": {}, "description": null, "custom_fields": null, "account_tax_ids": null, "rendering_options": null}}, "shipping_options": [], "customer_creation": "if_required", "consent_collection": null, "client_reference_id": null, "currency_conversion": null, "payment_method_types": ["card", "bancontact", "eps", "giropay", "ideal", "link", "klarna"], "allow_promotion_codes": false, "payment_method_options": {"card": {"request_three_d_secure": "automatic"}}, "phone_number_collection": {"enabled": false}, "payment_method_collection": "always", "billing_address_collection": "auto", "shipping_address_collection": null, "payment_method_configuration_details": {"id": "pmc_1Om7TPBH8XCwzsfXBB30jrJh", "parent": null}}}, "type": "checkout.session.completed", "object": "event", "created": **********, "request": {"id": null, "idempotency_key": null}, "livemode": false, "api_version": "2020-08-27", "pending_webhooks": 4}}]}, "settings": {"executionOrder": "v1"}, "versionId": "9e350a8f-30e0-43ab-8dab-a7edbfd637d8", "connections": {"Config": {"main": [[{"node": "if is creation flow", "type": "main", "index": 0}]]}, "On payment": {"main": [[{"node": "Config", "type": "main", "index": 0}]]}, "Format event": {"main": [[{"node": "Store event", "type": "main", "index": 0}]]}, "Creation Form": {"main": [[{"node": "Config", "type": "main", "index": 0}]]}, "Notify teacher": {"main": [[{"node": "the end", "type": "main", "index": 0}]]}, "Format participant": {"main": [[{"node": "Add participant to list", "type": "main", "index": 0}]]}, "Create Zoom meeting": {"main": [[{"node": "Create Stripe Product", "type": "main", "index": 0}]]}, "Create payment link": {"main": [[{"node": "Create participant list", "type": "main", "index": 0}, {"node": "Format event", "type": "main", "index": 0}]]}, "if is creation flow": {"main": [[{"node": "Create Zoom meeting", "type": "main", "index": 0}], [{"node": "Format participant", "type": "main", "index": 0}]]}, "Create Stripe Product": {"main": [[{"node": "Create payment link", "type": "main", "index": 0}]]}, "Add participant to list": {"main": [[{"node": "Send confirmation to participant", "type": "main", "index": 0}]]}, "Create participant list": {"main": [[{"node": "Send email to teacher", "type": "main", "index": 0}]]}, "Send confirmation to participant": {"main": [[{"node": "Notify teacher", "type": "main", "index": 0}]]}}}