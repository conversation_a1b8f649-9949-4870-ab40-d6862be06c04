{"nodes": [{"name": "n8n Trigger", "type": "n8n-nodes-base.n8nTrigger", "position": [450, 200], "parameters": {"events": ["init"]}, "typeVersion": 1}, {"name": "Mattermost", "type": "n8n-nodes-base.mattermost", "position": [650, 200], "parameters": {"message": "=Your n8n instance started at {{$json[\"timestamp\"]}}", "channelId": "toyi3uoycf8rirtm7d5jm15sso", "attachments": [], "otherOptions": {}}, "credentials": {"mattermostApi": "Mattermost Credentials"}, "typeVersion": 1}], "connections": {"n8n Trigger": {"main": [[{"node": "Mattermost", "type": "main", "index": 0}]]}}}