{"meta": {"instanceId": "6a2a7715680b8313f7cb4676321c5baa46680adfb913072f089f2766f42e43bd"}, "nodes": [{"id": "f4b3833b-cf25-4bbc-927c-080586c5713c", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [700, 760], "parameters": {"color": 7, "width": 330.5152611046425, "height": 239.5888196628349, "content": "### ... or watch set up video [13 min]\n[![Youtube Thumbnail](https://cflobdhpqwnoisuctsoc.supabase.co/storage/v1/object/public/my_storage/Youtube%20AI%20Agent%20Blur.png)](https://youtu.be/6RmLZS8Yl4E)\n"}, "typeVersion": 1}, {"id": "64d96c53-b3e2-4aea-9a29-9b9e5c729f4f", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [400, 240], "parameters": {"color": 7, "width": 636.2128494576581, "height": 497.1532689930921, "content": "![5min Logo](https://cflobdhpqwnoisuctsoc.supabase.co/storage/v1/object/public/my_storage/banner.png)\n## AI Agent To Chat With Youtube\n**Made by [<PERSON>](https://www.linkedin.com/in/marklowcoding/) from community [5minAI](https://www.skool.com/5minai)**\n\nNavigating the content generation and optimization process can be complex, especially without significant audience insight. This workflow automates insights extraction from YouTube videos and comments, empowering users to create more engaging and relevant content effectively.\n\nThe workflow integrates various APIs to gather insights from YouTube videos, enabling automated commentary analysis, video transcription, and thumbnail evaluation. The main functionalities include:\n- Extracting user preferences from comments.\n- Transcribing video content for enhanced understanding.\n- Analyzing thumbnails via AI for maximum viewer engagement insights.\n\n"}, "typeVersion": 1}, {"id": "57d2ede9-1bf9-4449-9dc9-af1ccee763b6", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [400, 760], "parameters": {"color": 7, "width": 280.2462120317618, "height": 545.9087885077763, "content": "### Set up steps\n\n1. **API Setup**:\n - Create a [Google Cloud](https://console.cloud.google.com/apis/dashboard) project and enable the YouTube Data API.\n - Generate an API key for [Apify](https://www.apify.com?fpr=ujogj).\n - Generate API key for [OpenAI](https://platform.openai.com)\n - Create all credentials in N8N - OpenAI, Apify, Google Cloud.\n\n2. **YouTube Creator and Video Selection**:\n - Start by defining a request to identify top creators based on their video views.\n - Capture the YouTube video IDs for further analysis of comments and other video metrics.\n\n3. **Comment Analysis**:\n - Gather comments associated with the selected videos and analyze them for user insights.\n - Implement pagination to handle the maximum comment retrieval limits in API requests.\n\n4. **Video Transcription**:\n - Request transcriptions for videos of interest, ensuring to manage potential costs associated with longer video processing.\n - Utilize the insights from transcriptions to formulate content plans.\n\n5. **Thumbnail Analysis**:\n - Evaluate your video thumbnails by submitting the URL through the OpenAI API to gain insights into their effectiveness.\n\n6. **Data Management**:\n - Incorporate a database agent to organize video data and metrics, allowing efficient record management and future content planning."}, "typeVersion": 1}, {"id": "ca0fd549-88a7-44fd-ab81-7fd5ca140dae", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1540, 820], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "zJhr5piyEwVnWtaI", "name": "OpenAi club"}}, "typeVersion": 1}, {"id": "7f2cf209-2e9d-4d6a-bc9e-d1bfd6df7266", "name": "get_channel_details", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [1900, 820], "parameters": {"name": "get_channel_details", "fields": {"values": [{"name": "command", "stringValue": "=get_channel_details"}]}, "schemaType": "manual", "workflowId": {"__rl": true, "mode": "list", "value": "FgknOUpOBkpY85NX", "cachedResultName": "Youtube parser - tools"}, "description": "Get channel_id, title and description by handle/username.\nChannel_id is required to find videos and details about this channel.\nIf Youtube link to channel provided - parse handle from there or return channel_id. (e.g. https://www.youtube.com/@example_handle - example_handle)\n\n\nExample Input:\nexample_handle\n\nExample Output:\nid:UCOgz_YflAsYnGbdvzXuKNCA\ntitle:<PERSON>\ndescription:Digital Diary 🤎\\n\\n\\nWeekly videos around fashion...", "inputSchema": "{\n \"type\": \"object\",\n \"properties\": {\n \"handle\": {\n \"type\": \"string\",\n \"description\": \"Handle/username of channel\"\n }},\n \"required\": [\"handle\"]\n}", "specifyInputSchema": true}, "typeVersion": 1.2}, {"id": "c02f5c19-6e50-4a06-95b9-eceb3eec1012", "name": "get_video_description", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [2020, 820], "parameters": {"name": "get_video_description", "fields": {"values": [{"name": "command", "stringValue": "video_details"}]}, "schemaType": "manual", "workflowId": {"__rl": true, "mode": "list", "value": "FgknOUpOBkpY85NX", "cachedResultName": "Youtube parser - tools"}, "description": "Fetch video details - the full description, title, and publish date of a video using its video_id.\n\nExample input:\nvideo_id:dQw4w9WgXcQ\n\nExample Output:\ntitle:Never Gonna Give You Up\ndescription: \"The official video for “Never Gonna Give You Up” by <PERSON>.\nduration:4 min\nviewCount:154\nlikeCount:6\nthumbnails: urls", "inputSchema": "{\n \"type\": \"object\",\n \"properties\": {\n \"video_id\": {\n \"type\": \"string\",\n \"description\": \"The ID of the video to fetch details for\"\n }\n },\n \"required\": [\"video_id\"]\n}", "specifyInputSchema": true}, "typeVersion": 1.2}, {"id": "2d61160b-3a65-4766-ace6-947a7c5de6e5", "name": "get_list_of_videos", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [2140, 820], "parameters": {"name": "get_list_of_videos", "fields": {"values": [{"name": "command", "stringValue": "videos"}]}, "schemaType": "manual", "workflowId": {"__rl": true, "mode": "list", "value": "FgknOUpOBkpY85NX", "cachedResultName": "Youtube parser - tools"}, "description": "Retrieve a list of videos from a channel using channel_id. Supports sorting by date, relevance, or view count.\n\nExample Input:\nchannel_id\": \"UCxxxxxxxxxxxxxxxx\"\nnumber_of_videos\": 5\norder: \"date\"\npublishedAfter: \"timestamp\"\n\nExample Output:\nvideo_id:abc123\ntitle:Latest Video\nshort cut description:Latest Video\npublished_at:2023-12-05T10:00:00Z", "inputSchema": "{\n \"type\": \"object\",\n \"properties\": {\n \"channel_id\": {\n \"type\": \"string\",\n \"description\": \"The ID of the channel to fetch videos from\"\n },\n \"number_of_videos\": {\n \"type\": \"integer\",\n \"description\": \"The maximum number of videos to retrieve (max 50)\"\n },\n \"order\": {\n \"type\": \"string\",\n \"enum\": [\"date\", \"relevance\", \"viewCount\"],\n \"description\": \"Order in which to fetch videos\"\n },\n \"publishedAfter\": {\n \"type\": \"string\",\n \"description\": \"Timestamp for filtering like 2023-11-03T15:28:05Z.\"\n }\n },\n \"required\": [\"channel_id\", \"number_of_videos\", \"order\"]\n}", "specifyInputSchema": true}, "typeVersion": 1.2}, {"id": "c5aa2f7c-7748-4f88-abb6-fd274ad1295a", "name": "get_list_of_comments", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [2260, 820], "parameters": {"name": "get_list_of_comments", "fields": {"values": [{"name": "command", "stringValue": "comments"}]}, "schemaType": "manual", "workflowId": {"__rl": true, "mode": "list", "value": "FgknOUpOBkpY85NX", "cachedResultName": "Youtube parser - tools"}, "description": "Retrieve a list of comments from a video using video_id.\n\nInput:\n \"video_id\": \"dQw4w9WgXcQ\"\n\nOutput:\n \"author\": \"<PERSON>\",\n \"comment\": \"This is an amazing video!\",\n \"published_at\": \"2023-12-04T12:00:00Z\"", "inputSchema": "{\n \"type\": \"object\",\n \"properties\": {\n \"video_id\": {\n \"type\": \"string\",\n \"description\": \"The ID of the video to fetch comments from\"\n }\n },\n \"required\": [\"video_id\"]\n}", "specifyInputSchema": true}, "typeVersion": 1.2}, {"id": "c68cad77-1d71-45a3-b94b-8f7c701f56fb", "name": "search", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [2380, 820], "parameters": {"name": "search", "fields": {"values": [{"name": "command", "stringValue": "search"}]}, "schemaType": "manual", "workflowId": {"__rl": true, "mode": "list", "value": "FgknOUpOBkpY85NX", "cachedResultName": "Youtube parser - tools"}, "description": "Search for videos or channels using a query. Supports filtering by type (video or channel) and sorting (date, viewCount, relevance). Use | for OR and - to exclude terms in the query.\n\nInput:\ntype: video or channel\nquery: search query\nsorting: date, viewCount, relevance\npublishedAfter: timestamp\n\nOutput:\n- id, title, short cut description, and published_at.", "inputSchema": "{\n \"type\": \"object\",\n \"properties\": {\n \"type\": {\n \"type\": \"string\",\n \"enum\": [\"video\", \"channel\"],\n \"description\": \"Type of results to retrieve: video or channel\"\n },\n \"query\": {\n \"type\": \"string\",\n \"description\": \"Search query. Supports | for OR and - to exclude terms\"\n },\n \"sorting\": {\n \"type\": \"string\",\n \"enum\": [\"date\", \"viewCount\", \"relevance\"],\n \"description\": \"Sorting criteria for search results\"\n },\n \"publishedAfter\": {\n \"type\": \"string\",\n \"description\": \"Timestamp for filtering like 2023-11-03T15:28:05Z\"\n }\n },\n \"required\": [\"type\", \"query\", \"sorting\"]\n}", "specifyInputSchema": true}, "typeVersion": 1.2}, {"id": "c87d5392-8a5c-4999-9e58-89a5e0700c40", "name": "analyze_thumbnail", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [2500, 820], "parameters": {"name": "analyze_thumbnail", "fields": {"values": [{"name": "command", "stringValue": "analyze_thumbnail"}]}, "schemaType": "manual", "workflowId": {"__rl": true, "mode": "list", "value": "FgknOUpOBkpY85NX", "cachedResultName": "Youtube parser - tools"}, "description": "Analyze a thumbnail image based on a given prompt. The prompt can be customized for specific analysis needs, such as design critique, color scheme evaluation, or content assessment.\nUse link of maxRes thumbnail. \n\nInput:\n- url: URL of the thumbnail image.\n- prompt: Customizable instruction for the analysis.\n\nOutput:\n- Results of the analysis based on the given prompt.", "inputSchema": "{\n \"type\": \"object\",\n \"properties\": {\n \"url\": {\n \"type\": \"string\",\n \"description\": \"URL of the thumbnail image to analyze\"\n },\n \"prompt\": {\n \"type\": \"string\",\n \"description\": \"Customizable instruction to guide the image analysis\"\n }\n },\n \"required\": [\"url\", \"prompt\"]\n}", "specifyInputSchema": true}, "typeVersion": 1.2}, {"id": "1be2fa35-9091-4db8-a8eb-50f822d618d3", "name": "video_transcription", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [2620, 820], "parameters": {"name": "video_transcription", "fields": {"values": [{"name": "command", "stringValue": "video_transcription"}]}, "schemaType": "manual", "workflowId": {"__rl": true, "mode": "list", "value": "FgknOUpOBkpY85NX", "cachedResultName": "Youtube parser - tools"}, "description": "Transcribe a video and retrieve its text transcription. Useful for analyzing video content or repurposing it for other formats.\n\nInput:\n- video_url: URL of the video to transcribe.\n\nOutput:\n- The text transcription of the video.", "inputSchema": "{\n \"type\": \"object\",\n \"properties\": {\n \"video_url\": {\n \"type\": \"string\",\n \"description\": \"URL of the video to transcribe\"\n }\n },\n \"required\": [\"video_url\"]\n}", "specifyInputSchema": true}, "typeVersion": 1.2}, {"id": "fbfcd82f-e247-4a21-be12-339df7afe681", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "position": [1700, 820], "parameters": {"sessionKey": "={{ $('When chat message received').item.json.sessionId }}", "sessionIdType": "customKey"}, "credentials": {"postgres": {"id": "AO9cER6p8uX7V07T", "name": "Postgres 5minai"}}, "typeVersion": 1.3}, {"id": "6a4bbad9-27ab-448b-9222-2c8843fe241a", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1760, 560], "parameters": {"text": "={{ $('When chat message received').item.json.chatInput }}", "agent": "openAiFunctionsAgent", "options": {"systemMessage": "You are Youtube assistant. \nYou need to process user's requests and run relevant tools for that. \n\nPlan and execute in right order runs of tools to get data for user's request.\n\nIMPORTANT Search query and list of videos for channel tools returns all videos including shorts - use Get Video description tool to identify shorts (less than minute) and filter them out if needed.\n\nFeel free to ask questions before do actions - especially if you noticed some inconcistency in user requests that might be error/misspelling. "}, "promptType": "define"}, "typeVersion": 1.6}, {"id": "739cc12a-27d1-48e9-b124-7f83fb372514", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [1460, 600], "webhookId": "6e95bc27-99a6-417c-8bf7-2831d7f7a4be", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "613af9f2-77fa-42c4-86d3-87e20f2c0c89", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1380, 500], "parameters": {"width": 1430.34590072234, "height": 588.1344471094899, "content": "## Sc<PERSON><PERSON> 1: AI agent"}, "typeVersion": 1}, {"id": "54116346-bc73-4a6a-8bca-f2a6e6699374", "name": "Get Comments", "type": "n8n-nodes-base.httpRequest", "position": [2064, 1598], "parameters": {"url": "=https://www.googleapis.com/youtube/v3/commentThreads", "options": {}, "sendQuery": true, "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth", "queryParameters": {"parameters": [{"name": "part", "value": "id,snippet,replies"}, {"name": "videoId", "value": "={{ $('Execute Workflow Trigger').item.json.query.video_id }}"}, {"name": "maxResults", "value": "100"}]}}, "credentials": {"httpQueryAuth": {"id": "1DXeuNaLSixqGPaU", "name": "Query Auth account Youtube"}}, "typeVersion": 4.2}, {"id": "faabf71a-69f2-4113-802e-124a09fa9a0a", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [1444, 1598], "parameters": {}, "typeVersion": 1}, {"id": "4b3ec3aa-7c69-4a72-a989-02f97acdf612", "name": "Get Channel Details", "type": "n8n-nodes-base.httpRequest", "position": [2064, 1278], "parameters": {"url": "=https://www.googleapis.com/youtube/v3/channels", "options": {}, "sendQuery": true, "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth", "queryParameters": {"parameters": [{"name": "part", "value": "snippet"}, {"name": "forHandle", "value": "={{ $('Execute Workflow Trigger').item.json.query.handle }}"}]}}, "credentials": {"httpQueryAuth": {"id": "1DXeuNaLSixqGPaU", "name": "Query Auth account Youtube"}}, "typeVersion": 4.2}, {"id": "ed8dec73-8c50-4eb9-8efe-68ee72c4d5e6", "name": "Get Video Description", "type": "n8n-nodes-base.httpRequest", "position": [2064, 1438], "parameters": {"url": "=https://www.googleapis.com/youtube/v3/videos", "options": {}, "sendQuery": true, "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth", "queryParameters": {"parameters": [{"name": "part", "value": "snippet,contentDetails,statistics"}, {"name": "id", "value": "={{ $('Execute Workflow Trigger').item.json.query.video_id }}"}]}}, "credentials": {"httpQueryAuth": {"id": "1DXeuNaLSixqGPaU", "name": "Query Auth account Youtube"}}, "typeVersion": 4.2}, {"id": "c1ff3837-8d7e-49ad-a333-c177833fcd05", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [2224, 1598], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "469d89ba-23fc-482a-b4ae-ce5d3bc13579", "name": "response", "type": "string", "value": "={{ JSON.stringify(` Comments: ${$json.items.map(item => { const topLevelComment = `${item.snippet.topLevelComment.snippet.authorDisplayName}: ${item.snippet.topLevelComment.snippet.textOriginal}`; const replies = item.replies?.comments.map(reply => `${reply.snippet.authorDisplayName}: ${reply.snippet.textOriginal}` ).join('\\n') || ''; return [topLevelComment, replies].filter(<PERSON><PERSON>an).join('\\n'); }).join('\\n\\n')} `) }}"}]}}, "typeVersion": 3.4}, {"id": "5f0c44fe-2523-4170-a27d-0ccd1bef24a7", "name": "Run Query", "type": "n8n-nodes-base.httpRequest", "position": [2064, 1758], "parameters": {"url": "=https://www.googleapis.com/youtube/v3/search", "options": {}, "sendQuery": true, "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth", "queryParameters": {"parameters": [{"name": "part", "value": "snippet"}, {"name": "q", "value": "={{ $('Execute Workflow Trigger').item.json.query.query }}"}, {"name": "order", "value": "={{ $('Execute Workflow Trigger').item.json.query.order }}"}, {"name": "type", "value": "={{ $('Execute Workflow Trigger').item.json.query.type }}"}, {"name": "maxResults", "value": "={{ $('Execute Workflow Trigger').item.json.query.number_of_videos }}"}, {"name": "publishedAfter", "value": "={{ $('Execute Workflow Trigger').item.json.query.publishedAfter }}"}]}}, "credentials": {"httpQueryAuth": {"id": "1DXeuNaLSixqGPaU", "name": "Query Auth account Youtube"}}, "typeVersion": 4.2}, {"id": "3e192718-6710-4143-ac6e-15df79ee5363", "name": "Get Videos by Channel", "type": "n8n-nodes-base.httpRequest", "position": [2064, 1918], "parameters": {"url": "=https://www.googleapis.com/youtube/v3/search", "options": {}, "sendQuery": true, "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth", "queryParameters": {"parameters": [{"name": "part", "value": "snippet"}, {"name": "channelId", "value": "={{ $('Execute Workflow Trigger').item.json.query.channel_id }}"}, {"name": "order", "value": "={{ $('Execute Workflow Trigger').item.json.query.order }}"}, {"name": "maxResults", "value": "={{ $('Execute Workflow Trigger').item.json.query.number_of_videos }}"}, {"name": "type", "value": "video"}, {"name": "publishedAfter", "value": "={{ $('Execute Workflow Trigger').item.json.query.publishedAfter }}"}]}}, "credentials": {"httpQueryAuth": {"id": "1DXeuNaLSixqGPaU", "name": "Query Auth account Youtube"}}, "typeVersion": 4.2}, {"id": "8bcb50a4-0cd1-4311-ac6a-2ee8653cfb71", "name": "Response", "type": "n8n-nodes-base.set", "position": [2564, 1598], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "cfdbe2f5-921e-496d-87bd-9c57fdc22a7a", "name": "response", "type": "object", "value": "={{$json}}"}]}}, "typeVersion": 3.4}, {"id": "7f5a36d3-6710-4e69-8459-7c8c748ee7d9", "name": "Switch", "type": "n8n-nodes-base.switch", "position": [1624, 1578], "parameters": {"rules": {"values": [{"outputKey": "get_channel_details", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $('Execute Workflow Trigger').item.json.command }}", "rightValue": "get_channel_details"}]}, "renameOutput": true}, {"outputKey": "video_details", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "26a3ffe8-c8a6-4564-8d18-5494a8059372", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Execute Workflow Trigger').item.json.command }}", "rightValue": "video_details"}]}, "renameOutput": true}, {"outputKey": "comments", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "0f51cc26-2e42-42e1-a5c2-cb1d2e384962", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Execute Workflow Trigger').item.json.command }}", "rightValue": "comments"}]}, "renameOutput": true}, {"outputKey": "search", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "51031140-5ceb-48aa-9f33-d314131a9653", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Execute Workflow Trigger').item.json.command }}", "rightValue": "search"}]}, "renameOutput": true}, {"outputKey": "videos", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "f160bf0a-423f-448d-ab80-50a0b6a177ca", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Execute Workflow Trigger').item.json.command }}", "rightValue": "videos"}]}, "renameOutput": true}, {"outputKey": "analyze_thumbnail", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "29542ac4-7b9d-413f-aabb-a1cdabed2fa7", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Execute Workflow Trigger').item.json.command }}", "rightValue": "analyze_thumbnail"}]}, "renameOutput": true}, {"outputKey": "video_transcription", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "35fc39b8-6cf1-4ea6-9609-4a195c5526f8", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Execute Workflow Trigger').item.json.command }}", "rightValue": "video_transcription"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "df432d53-33bf-4e91-9ead-7f4b36bd788a", "name": "Get Video Transcription", "type": "n8n-nodes-base.httpRequest", "position": [2064, 2238], "parameters": {"url": "=https://api.apify.com/v2/acts/dB9f4B02ocpTICIEY/run-sync-get-dataset-items", "method": "POST", "options": {}, "jsonBody": "={\n \"startUrls\": [\n \"{{ $('Execute Workflow Trigger').item.json.query.video_url }}\"\n ]\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth"}, "credentials": {"httpQueryAuth": {"id": "XDavOaI9qH5Zi3QC", "name": "Apify"}}, "typeVersion": 4.2}, {"id": "8079e5c9-4a52-45ce-ac41-7fc707177a5a", "name": "OpenAI", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [2064, 2078], "parameters": {"text": "={{ $('Execute Workflow Trigger').item.json.query.prompt }}", "modelId": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "GPT-4O"}, "options": {}, "resource": "image", "imageUrls": "={{ $('Execute Workflow Trigger').item.json.query.url }}", "operation": "analyze"}, "credentials": {"openAiApi": {"id": "SphXAX7rlwRLkiox", "name": "Test club key"}}, "typeVersion": 1.7}, {"id": "7847e82a-fe82-498c-8c14-4c1c718d632c", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1380, 1140], "parameters": {"width": 1427.3810326521016, "height": 1313.2689194736308, "content": "## Scenario 2: Agent tools"}, "typeVersion": 1}, {"id": "3a0fbbb0-4c0e-41f1-abb3-c87e955ad1b3", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1540, 960], "parameters": {"color": 4, "width": 266.7375650720483, "height": 80, "content": "### Replace credentials"}, "typeVersion": 1}, {"id": "363eaca0-aaa5-4551-845f-528f19bba57a", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [2004, 1178], "parameters": {"color": 4, "width": 266.7375650720483, "height": 80, "content": "### Replace credentials in all nodes - Apify, OpenAI, Google"}, "typeVersion": 1}], "pinData": {"Execute Workflow Trigger": [{"query": {"type": "video", "query": "Web scraping data with n8n and Puppeteer", "sorting": "relevance"}, "command": "search"}]}, "connections": {"OpenAI": {"main": [[{"node": "Response", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Get Channel Details", "type": "main", "index": 0}], [{"node": "Get Video Description", "type": "main", "index": 0}], [{"node": "Get Comments", "type": "main", "index": 0}], [{"node": "Run Query", "type": "main", "index": 0}], [{"node": "Get Videos by Channel", "type": "main", "index": 0}], [{"node": "OpenAI", "type": "main", "index": 0}], [{"node": "Get Video Transcription", "type": "main", "index": 0}]]}, "search": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Run Query": {"main": [[{"node": "Response", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Response", "type": "main", "index": 0}]]}, "Get Comments": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "analyze_thumbnail": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "get_list_of_videos": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Get Channel Details": {"main": [[{"node": "Response", "type": "main", "index": 0}]]}, "get_channel_details": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "video_transcription": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "get_list_of_comments": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Get Video Description": {"main": [[{"node": "Response", "type": "main", "index": 0}]]}, "Get Videos by Channel": {"main": [[{"node": "Response", "type": "main", "index": 0}]]}, "get_video_description": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Get Video Transcription": {"main": [[{"node": "Response", "type": "main", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}}}