{"id": "30", "name": "Receive updates when a subscriber is added to a group and strore the information in Airtable", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "disabled": true, "position": [110, 600], "parameters": {}, "typeVersion": 1}, {"name": "MailerLite <PERSON>", "type": "n8n-nodes-base.mailerLiteTrigger", "position": [530, 300], "webhookId": "dd15d919-18b3-4af7-a5c9-c4583cdda9f5", "parameters": {"event": "subscriber.add_to_group"}, "credentials": {"mailerLiteApi": "mailerlite"}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [730, 300], "parameters": {"values": {"string": [{"name": "Name", "value": "={{$node[\"MailerLite Trigger\"].json[\"data\"][\"subscriber\"][\"name\"]}}"}, {"name": "Email", "value": "={{$node[\"MailerLite Trigger\"].json[\"data\"][\"subscriber\"][\"email\"]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "Airtable", "type": "n8n-nodes-base.airtable", "position": [930, 300], "parameters": {"table": "Data", "options": {}, "operation": "append", "application": ""}, "credentials": {"airtableApi": "airtable-harshil"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Set": {"main": [[{"node": "Airtable", "type": "main", "index": 0}]]}, "MailerLite Trigger": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[]]}}}