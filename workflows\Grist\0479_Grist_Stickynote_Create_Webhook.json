{"meta": {"instanceId": "11cdc3de0458a725de3bc4f700573556888270388b4b36af8a7651aaafd542a8"}, "nodes": [{"id": "93eba4f0-218d-47d3-a55f-09d490d5e0bb", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [100, 320], "webhookId": "03e24572-a381-455e-a5b8-ae697647f7d4", "parameters": {"path": "03e24572-a381-455e-a5b8-ae697647f7d4", "options": {}, "httpMethod": "POST"}, "typeVersion": 1.1}, {"id": "e2c8d43e-79f9-45a4-9d6d-37e8768e7f81", "name": "Create Row", "type": "n8n-nodes-base.grist", "position": [940, 240], "parameters": {"docId": "", "tableId": "", "operation": "create", "fieldsToSend": {"properties": [{"fieldId": "Source", "fieldValue": "={{ $json.body[0].id }}"}]}}, "credentials": {"gristApi": {"id": "2", "name": "Grist"}}, "typeVersion": 1}, {"id": "1e6e741e-2890-4e08-a97a-efae1812d507", "name": "Confirmed?", "type": "n8n-nodes-base.if", "position": [300, 320], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "df1c1dba-dc96-42e9-86ee-8ccd4c82b048", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.body[0].Confirmed }}", "rightValue": ""}]}}, "notesInFlow": true, "typeVersion": 2}, {"id": "c6b1b482-6121-4484-b524-bc3e7e175fe8", "name": "get existing", "type": "n8n-nodes-base.grist", "position": [560, 160], "parameters": {"docId": "", "tableId": "", "additionalOptions": {"filter": {"filterProperties": [{"field": "Source", "values": "={{ $json.body[0].id }}"}]}}}, "credentials": {"gristApi": {"id": "2", "name": "Grist"}}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "a52e000c-73ef-4f2d-811d-cbcaf45e2b75", "name": "has existing?", "type": "n8n-nodes-base.if", "position": [700, 160], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "6f08b500-956e-493c-abbe-845b5352110c", "operator": {"type": "object", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json }}", "rightValue": ""}]}}, "typeVersion": 2}, {"id": "fe609754-3dd6-4bbd-932a-a30f7d100911", "name": "Confirmation-based", "type": "n8n-nodes-base.stickyNote", "position": [460, 420], "parameters": {"width": 346.820338983051, "height": 144.13559322033893, "content": "## Confirmation-based\nIn the source table there is a boolean column \"Confirmed\" that will trigger the transfer.\nThis way there is a manual check involved & it's a conscious step to trigger the workflow."}, "typeVersion": 1}, {"id": "edb074f6-b264-45ec-87e2-cf91063ca63b", "name": "Runs once", "type": "n8n-nodes-base.stickyNote", "position": [900, 60], "parameters": {"width": 253.74915254237288, "height": 139.9050847457627, "content": "## Runs once\nIf the destination table already contains an entry, **we will not re-create/update** it (as it might've already been changed manually)\n"}, "typeVersion": 1}], "pinData": {"Webhook": [{"body": [{"id": 2, "Datum": 1712275200, "Confirmed": true, "manualSort": 2}], "query": {}, "params": {}, "headers": {"host": "wh.n8n.zt.ax", "accept": "*/*", "x-real-ip": "***********", "user-agent": "node-fetch/1.0 (+https://github.com/bitinn/node-fetch)", "content-type": "application/json", "content-length": "1097", "accept-encoding": "gzip,deflate", "x-forwarded-for": "***********", "x-forwarded-host": "wh.n8n.zt.ax", "x-forwarded-port": "443", "x-forwarded-proto": "https", "x-forwarded-server": "5d1c8421e216"}}]}, "connections": {"Webhook": {"main": [[{"node": "Confirmed?", "type": "main", "index": 0}]]}, "Confirmed?": {"main": [[{"node": "get existing", "type": "main", "index": 0}]]}, "get existing": {"main": [[{"node": "has existing?", "type": "main", "index": 0}]]}, "has existing?": {"main": [null, [{"node": "Create Row", "type": "main", "index": 0}]]}}}