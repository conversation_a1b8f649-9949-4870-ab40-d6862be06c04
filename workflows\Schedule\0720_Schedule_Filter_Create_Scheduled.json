{"meta": {"instanceId": "db80165df40cb07c0377167c050b3f9ab0b0fb04f0e8cae0dc53f5a8527103ca", "templateCredsSetupCompleted": true}, "nodes": [{"id": "62edf095-a02a-4b8d-a7b1-e194ae0d3652", "name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [-660, 1100], "parameters": {}, "typeVersion": 1}, {"id": "1e10875b-f54b-43a8-a7a2-43d4fcbf248d", "name": "n8n", "type": "n8n-nodes-base.n8n", "position": [-300, 1220], "parameters": {"filters": {}, "requestOptions": {}}, "credentials": {"n8nApi": {"id": "uqWyCDytVt4ZKbVE", "name": "Phoenix✅"}}, "retryOnFail": true, "typeVersion": 1, "alwaysOutputData": true}, {"id": "1f5caabb-d76b-4744-be76-97e9abea1ddc", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [-100, 1220], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "755e0803-c5c0-48a7-9c0c-44f8d5718d0b", "name": "create new folder", "type": "n8n-nodes-base.googleDrive", "position": [-480, 1220], "parameters": {"name": "=Workflow Backups {{ $now.format('cccc t dd-MM-yyyy') }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "1hnHubRgcstU8OgV8BPwPNivfTZT5g2Wf", "cachedResultUrl": "https://drive.google.com/drive/folders/1hnHubRgcstU8OgV8BPwPNivfTZT5g2Wf", "cachedResultName": "Workflow Backups"}, "resource": "folder"}, "credentials": {"googleDriveOAuth2Api": {"id": "HqlejV5xP0lqTq5e", "name": "Google Drive account✅"}}, "typeVersion": 3}, {"id": "********-6d87-4a72-bb51-dd8c6e03c0c1", "name": "Convert to File", "type": "n8n-nodes-base.convertToFile", "position": [120, 1320], "parameters": {"options": {"format": true, "fileName": "={{ $json.name + \".json\" }} "}, "operation": "to<PERSON><PERSON>"}, "typeVersion": 1.1}, {"id": "0b0155f1-15bc-4580-af6e-7dec3b0d5737", "name": "Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [300, 1320], "parameters": {"name": "={{ $('Loop Over Items').item.json.name + \".json\" }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "id", "value": "={{ $('create new folder').item.json.id }}"}}, "credentials": {"googleDriveOAuth2Api": {"id": "HqlejV5xP0lqTq5e", "name": "Google Drive account✅"}}, "typeVersion": 3}, {"id": "c7b73036-1831-4dd6-8dd9-fef1356a184c", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-660, 1360], "parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 4}]}}, "typeVersion": 1.2}, {"id": "666dcf95-928c-4270-808f-755a9771a410", "name": "Filter", "type": "n8n-nodes-base.filter", "position": [300, 1120], "parameters": {"options": {"ignoreCase": true}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": false, "typeValidation": "loose"}, "combinator": "and", "conditions": [{"id": "538fc29d-2693-4c62-9848-bdcaf8566909", "operator": {"type": "string", "operation": "notEquals"}, "leftValue": "={{ $json.id }}", "rightValue": "={{ $('create new folder').item.json.id }}"}]}, "looseTypeValidation": true}, "typeVersion": 2.2}, {"id": "f6f44cbe-a98e-4a49-8c4c-59ebe02db9e5", "name": "delete folder", "type": "n8n-nodes-base.googleDrive", "position": [480, 1120], "parameters": {"options": {"deletePermanently": true}, "resource": "folder", "operation": "deleteFolder", "folderNoRootId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}}, "credentials": {"googleDriveOAuth2Api": {"id": "HqlejV5xP0lqTq5e", "name": "Google Drive account✅"}}, "typeVersion": 3}, {"id": "d96a009f-08d3-4f0d-9f70-f9e0de9b9f91", "name": "Get folders", "type": "n8n-nodes-base.googleDrive", "position": [120, 1120], "parameters": {"filter": {"folderId": {"__rl": true, "mode": "list", "value": "1hnHubRgcstU8OgV8BPwPNivfTZT5g2Wf", "cachedResultUrl": "https://drive.google.com/drive/folders/1hnHubRgcstU8OgV8BPwPNivfTZT5g2Wf", "cachedResultName": "Workflow Backups"}}, "options": {}, "resource": "fileFolder"}, "credentials": {"googleDriveOAuth2Api": {"id": "HqlejV5xP0lqTq5e", "name": "Google Drive account✅"}}, "typeVersion": 3}], "pinData": {}, "connections": {"n8n": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Filter": {"main": [[{"node": "delete folder", "type": "main", "index": 0}]]}, "Get folders": {"main": [[{"node": "Filter", "type": "main", "index": 0}]]}, "Google Drive": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "delete folder": {"main": [[]]}, "Convert to File": {"main": [[{"node": "Google Drive", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Get folders", "type": "main", "index": 0}], [{"node": "Convert to File", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "create new folder", "type": "main", "index": 0}]]}, "create new folder": {"main": [[{"node": "n8n", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "create new folder", "type": "main", "index": 0}]]}}}