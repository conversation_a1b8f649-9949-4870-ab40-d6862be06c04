{"id": "A4hqQNFLymCRKnYK", "meta": {"instanceId": "5a64ae2dac98d415b280f5a86dd824858150b2ae6e4b41f2e62e7315042262b3", "templateCredsSetupCompleted": true}, "name": "Discord Agent", "tags": [], "nodes": [{"id": "b0f78e4d-e6f9-496c-a9d1-f2ec17612770", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [80, 60], "parameters": {"workflowInputs": {"values": [{"name": "Task"}]}}, "typeVersion": 1.1}, {"id": "3e185fb3-0b5f-4ba6-b382-c332cefa727e", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [380, 120], "parameters": {"text": "={{ $json.Task }}{{ $json.chatInput }}", "options": {"systemMessage": "You are a helpful assistant In Charge OF managing Discord always use channel id to reference channels. Always convert and output text in stylish discord formats. Reduce Text To 1800 characters Max.\n\nBefore sending any message absolutely ensure it is less than 1800 characters\n\nYou can Use One tool to send to free guides channel and another for ai-tools channel. make sure to read tool descriptions"}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "1aa1b4df-71af-4b85-9a6e-b371a2349598", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [380, 280], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "F4px3oxuWY5zBrvn", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "d6f59c6e-3bc0-4e85-8b89-b1a480db5317", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [80, 240], "webhookId": "913628ac-d409-49fa-8a34-a11349a30da6", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "f0aa9420-0207-4f6b-a659-ef89104e4925", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [540, 280], "parameters": {"sessionKey": "={{ $json.Task }}", "sessionIdType": "customKey"}, "typeVersion": 1.3}, {"id": "6b77b8f1-8fd2-4b0b-8993-3567d03b8b9b", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [780, 320], "parameters": {"color": 4, "width": 460, "height": 260, "content": "## Discord Management Tools"}, "typeVersion": 1}, {"id": "8947bfd3-88ed-48e5-a07f-c012cc3202c6", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [340, 40], "parameters": {"color": 5, "width": 360, "height": 380, "content": "## Main Discord Manager Agent"}, "typeVersion": 1}, {"id": "7fb0e4b2-5e66-4b3a-a976-926a4427f3c5", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [0, 0], "parameters": {"color": 3, "width": 260, "height": 400, "content": "## Trigger Automation "}, "typeVersion": 1}, {"id": "d100828e-6877-427d-ab8c-8486970b17e6", "name": "Discord", "type": "n8n-nodes-base.discordTool", "position": [960, 420], "webhookId": "aa558762-c5da-491d-9881-1a091632864f", "parameters": {"sendTo": "=channel", "userId": {"__rl": true, "mode": "list", "value": ""}, "content": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message', ``, 'string') }}", "guildId": {"__rl": true, "mode": "list", "value": "1236784625196601386", "cachedResultUrl": "https://discord.com/channels/1236784625196601386", "cachedResultName": "YungCEO SOCIETY💰"}, "options": {}, "resource": "message", "channelId": {"__rl": true, "mode": "list", "value": "1352547978308485192", "cachedResultUrl": "https://discord.com/channels/1236784625196601386/1352547978308485192", "cachedResultName": "ai-tools"}, "descriptionType": "manual", "toolDescription": "Use this tool to post a message in ai-tools discord channel"}, "credentials": {"discordBotApi": {"id": "ENuG6EzBN712IDLU", "name": "Motion Assistant"}}, "typeVersion": 2}, {"id": "d6dc1210-4049-4fa6-9896-67e8353db922", "name": "Discord1", "type": "n8n-nodes-base.discordTool", "position": [1080, 420], "webhookId": "7e07794e-e474-46c8-a23c-e9440a61d87b", "parameters": {"guildId": {"__rl": true, "mode": "list", "value": "1236784625196601386", "cachedResultUrl": "https://discord.com/channels/1236784625196601386", "cachedResultName": "YungCEO SOCIETY💰"}, "options": {}, "operation": "getAll", "returnAll": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Return_All', ``, 'boolean') }}"}, "credentials": {"discordBotApi": {"id": "ENuG6EzBN712IDLU", "name": "Motion Assistant"}}, "typeVersion": 2}, {"id": "1908e48f-51a7-4d42-a543-622a28c22136", "name": "Discord2", "type": "n8n-nodes-base.discordTool", "position": [820, 420], "webhookId": "aa558762-c5da-491d-9881-1a091632864f", "parameters": {"sendTo": "=channel", "userId": {"__rl": true, "mode": "list", "value": ""}, "content": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message', ``, 'string') }}", "guildId": {"__rl": true, "mode": "list", "value": "1236784625196601386", "cachedResultUrl": "https://discord.com/channels/1236784625196601386", "cachedResultName": "YungCEO SOCIETY💰"}, "options": {}, "resource": "message", "channelId": {"__rl": true, "mode": "list", "value": "1352242462520901632", "cachedResultUrl": "https://discord.com/channels/1236784625196601386/1352242462520901632", "cachedResultName": "free-guides"}, "descriptionType": "manual", "toolDescription": "Use this tool to post a message in free-guides discord channel"}, "credentials": {"discordBotApi": {"id": "ENuG6EzBN712IDLU", "name": "Motion Assistant"}}, "typeVersion": 2}, {"id": "557189d6-d5f3-4059-b349-9c3a9b642083", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-1000, -1300], "parameters": {"color": 4, "width": 1340, "height": 1220, "content": "# N8N Discord Workflow Setup Guide\n\n## Prerequisites\n- N8N account\n- Discord bot\n- OpenAI API key\n- Discord server access\n\n## 1. Discord Bot Creation\n### Steps\n- Open Discord Developer Portal\n- Create new application\n- Generate bot token\n- Add bot to server\n- Set permissions:\n  - Send Messages\n  - Read Message History\n  - View Channels\n\n### Required Info\n- Guild (Server) ID\n- Channel IDs:\n  - AI Tools Channel\n  - Free Guides Channel\n\n## 2. Credential Configuration\n### Discord Bot Credentials\n- Go to N8N Credentials section\n- Create 'Discord Bot API' credential\n- Enter bot token\n- Name credential (e.g., 'Motion Assistant')\n\n### OpenAI Credentials\n- Create 'OpenAI API' credential\n- Enter API key\n- Name credential (e.g., 'OpenAI Account')\n\n## 3. Workflow Setup\n### AI Agent Configuration\n- Customize system message\n- Define character limits\n- Specify output format\n\n### Trigger Types\n1. Workflow Execution Trigger\n2. Direct Chat Message Trigger\n\n"}, "typeVersion": 1}, {"id": "9b554c72-cb38-43d8-abcf-1bf48ee4fcec", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [420, -1200], "parameters": {"width": 1200, "height": 880, "content": "## Workflow Operation Modes\n### Mode 1: Workflow Trigger\n- Execute from another workflow\n- Input: Task/message string\n- Use cases:\n  - Automated messaging\n  - Scheduled updates\n  - External event triggers\n\n### Mode 2: Chat Trigger\n- Webhook-based activation\n- Process flow:\n  1. Receive message\n  2. AI Agent processing\n  3. Generate response\n  4. Maintain context\n\n## Customization Points\n- Modify AI system message\n- Adjust character limits\n- Configure channel interactions\n- Select OpenAI model\n\n## Potential Enhancements\n- Error handling\n- Advanced conversation memory\n- Additional channel tools\n- Message filtering\n\n## Troubleshooting\n- Verify bot permissions\n- Check API credentials\n- Validate character limits\n- Confirm channel IDs"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "8a8376c5-04e5-42da-9031-a9be0a34c211", "connections": {"Discord": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Discord1": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Discord2": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}}}