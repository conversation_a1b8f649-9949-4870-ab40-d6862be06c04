{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "Freshdesk", "type": "n8n-nodes-base.freshdesk", "position": [450, 300], "parameters": {"status": "open", "options": {}, "requester": "email", "requesterIdentificationValue": "<EMAIL>"}, "credentials": {"freshdeskApi": "freshdesk-api"}, "typeVersion": 1}], "connections": {"On clicking 'execute'": {"main": [[{"node": "Freshdesk", "type": "main", "index": 0}]]}}}