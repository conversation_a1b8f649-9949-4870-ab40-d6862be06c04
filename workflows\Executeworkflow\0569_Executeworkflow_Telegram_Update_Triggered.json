{"meta": {"instanceId": "1dbc26c48fe55fbd6f6084822260e5ffcc6df7c619b3d6ceeb699da53e67c82c"}, "nodes": [{"id": "5d9cf2ce-4808-4d44-9f0d-2c15d8dcea91", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.telegramTrigger", "position": [-400, 340], "webhookId": "96a20e88-ba8f-4827-b874-b0a9867c59e9", "parameters": {"updates": ["*"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "BsrAeDsPMOnQOFa7", "name": "n8n template"}}, "typeVersion": 1.1}, {"id": "22fc0669-96f2-4767-9bc2-03644c7ced21", "name": "Global data", "type": "n8n-nodes-base.set", "position": [-200, 340], "parameters": {"options": {}}, "typeVersion": 3.4}, {"id": "d5925fd8-abde-45bf-ac3d-22649ecb1f4e", "name": "Telegram1", "type": "n8n-nodes-base.telegram", "position": [1700, -360], "parameters": {"text": "Photo", "chatId": "={{ $('Trigger').item.json.message.from.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "BsrAeDsPMOnQOFa7", "name": "n8n template"}}, "typeVersion": 1.2}, {"id": "5dc06f04-26b4-45af-99d6-a06b7c1b936d", "name": "Telegram2", "type": "n8n-nodes-base.telegram", "position": [1700, -120], "parameters": {"text": "File", "chatId": "={{ $('Trigger').item.json.message.from.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "BsrAeDsPMOnQOFa7", "name": "n8n template"}}, "typeVersion": 1.2}, {"id": "d036c602-17bb-45b5-b7b0-331339570cb3", "name": "Telegram3", "type": "n8n-nodes-base.telegram", "position": [1260, 460], "parameters": {"text": "Callback", "chatId": "={{ $('Trigger').item.json.callback_query.data }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "BsrAeDsPMOnQOFa7", "name": "n8n template"}}, "typeVersion": 1.2}, {"id": "a86fe429-65df-471b-bdcb-e4765b14f109", "name": "Telegram4", "type": "n8n-nodes-base.telegram", "position": [1600, -700], "parameters": {"text": "Text", "chatId": "={{ $('Trigger').item.json.message.from.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "BsrAeDsPMOnQOFa7", "name": "n8n template"}}, "typeVersion": 1.2}, {"id": "b28ef71b-4e4b-48cb-b64d-029feee13ee4", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [980, 360], "parameters": {"color": 7, "width": 1200.5980355767667, "height": 326.00218267794156, "content": "## Callback"}, "typeVersion": 1}, {"id": "d51d4ac4-e182-4245-b26f-248f99235de8", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [980, -920], "parameters": {"color": 7, "width": 1200.5980355767667, "height": 481.314448671577, "content": "## Text\n"}, "typeVersion": 1}, {"id": "05754c06-8f64-44c6-be55-3eb480e0cb3d", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [980, -400], "parameters": {"color": 7, "width": 1200.5980355767667, "height": 198.69915410333263, "content": "## Photo"}, "typeVersion": 1}, {"id": "d9906042-25cd-4812-bbf1-4c46aa2c0492", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [980, -160], "parameters": {"color": 7, "width": 1200.5980355767667, "height": 198.69915410333263, "content": "## File"}, "typeVersion": 1}, {"id": "0e0bfc7f-23a3-478e-a3a1-cffbc9f9f95e", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [980, 100], "parameters": {"color": 7, "width": 1200.5980355767667, "height": 198.69915410333263, "content": "## Voice"}, "typeVersion": 1}, {"id": "a4519088-76c3-427c-95b6-7982814bf8e3", "name": "Telegram5", "type": "n8n-nodes-base.telegram", "position": [1700, 140], "parameters": {"text": "Voice", "chatId": "={{ $('Trigger').item.json.message.from.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "BsrAeDsPMOnQOFa7", "name": "n8n template"}}, "typeVersion": 1.2}, {"id": "0b597db9-d240-4be3-90f3-095117b1c6bc", "name": "Switch_MessageType", "type": "n8n-nodes-base.switch", "position": [720, -120], "parameters": {"rules": {"values": [{"outputKey": "Text", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "360a2e5b-8736-488c-87dc-b5fcbd2b5102", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $('Trigger').item.json.message.hasOwnProperty('text') }}", "rightValue": "/"}]}, "renameOutput": true}, {"outputKey": "Photo", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $('Trigger').item.json.message.hasOwnProperty('photo') }}", "rightValue": ""}]}, "renameOutput": true}, {"outputKey": "File", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "eb5a5507-4374-46c9-b8eb-25b36cbe17ee", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $('Trigger').item.json.message.hasOwnProperty('document') }}", "rightValue": "2"}]}, "renameOutput": true}, {"outputKey": "Voice", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "b5a43050-e657-4b56-aa9b-290a94aa8902", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $('Trigger').item.json.message.hasOwnProperty('voice') }}", "rightValue": ""}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3}, {"id": "efb08696-e76f-494c-8872-d117a379adec", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [980, -1440], "parameters": {"color": 7, "width": 1195.9520561291508, "height": 481.314448671577, "content": "## Commands"}, "typeVersion": 1}, {"id": "7edecce3-6371-45ab-8dc1-f3e1a2052daa", "name": "Telegram6", "type": "n8n-nodes-base.telegram", "position": [1840, -1180], "parameters": {"text": "Don't know the command", "chatId": "={{ $('Trigger').item.json.message.from.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "BsrAeDsPMOnQOFa7", "name": "n8n template"}}, "typeVersion": 1.2}, {"id": "c3b030e2-a085-4e21-8645-0224d6bb7c35", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.switch", "position": [1420, 460], "parameters": {"rules": {"values": [{"outputKey": "Conditions", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "798e1a40-e85b-4294-afcb-b129f92eb833", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Trigger').item.json.callback_query.data }}", "rightValue": "menu_conditions"}]}, "renameOutput": true}, {"outputKey": "Reviews", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "f91b8a94-8961-4d20-ae9c-0ee34ff04000", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Trigger').item.json.callback_query.data }}", "rightValue": "menu_reviews"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3}, {"id": "71907904-21f3-459c-a445-ca44a432dd36", "name": "Command?", "type": "n8n-nodes-base.if", "position": [1380, -760], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a2025331-2c2b-4df8-9e23-37035a5c808a", "operator": {"type": "string", "operation": "startsWith"}, "leftValue": "={{ $('Trigger').item.json.message.text }}\n", "rightValue": "/"}]}}, "typeVersion": 2}, {"id": "23e5c351-095b-4485-ad09-4dc4df195a8d", "name": "Change status", "type": "n8n-nodes-base.googleSheets", "position": [1460, 1220], "parameters": {"columns": {"value": {"Status": "0"}, "schema": [{"id": "ID", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Name", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Lastname", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Lastname", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Username", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Username", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Date", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Language", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Language", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Status", "type": "string", "display": true, "required": false, "displayName": "Status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Balance", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Balance", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["ID"]}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1nTDcSinsEdKUA_BzISUwCa8WogQZXc7sjIdBvs3D7o0/edit#gid=0", "cachedResultName": "USERS"}, "documentId": {"__rl": true, "mode": "url", "value": "https://docs.google.com/spreadsheets/d/1nTDcSinsEdKUA_BzISUwCa8WogQZXc7sjIdBvs3D7o0/edit?gid=0#gid=0"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "3", "name": "Google Sheets account"}}, "typeVersion": 4.4}, {"id": "12e9c69a-ac4d-4c1b-ba2e-18602a1ac715", "name": "Start bot?", "type": "n8n-nodes-base.if", "position": [1260, 1040], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "253b4dfb-2b86-499a-a1a6-b6d916c9c25f", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Trigger').item.json.my_chat_member.new_chat_member.status }}", "rightValue": "member"}]}}, "typeVersion": 2.1}, {"id": "b60a7063-a62f-4fbe-bc33-40ff55170f3e", "name": "Register", "type": "n8n-nodes-base.executeWorkflow", "position": [1620, 920], "parameters": {"options": {"waitForSubWorkflow": false}, "workflowId": "XZKoHGcXJE1fUizb"}, "typeVersion": 1}, {"id": "6a7249dd-f664-4115-a907-883d1da4e1c5", "name": "Payment Handler", "type": "n8n-nodes-base.executeWorkflow", "position": [1460, 1660], "parameters": {"options": {}, "workflowId": {"__rl": true, "mode": "id", "value": "lPX901W8CIMbKbww"}}, "typeVersion": 1.1}, {"id": "586f875f-e119-467e-8a3d-6090b8eaed80", "name": "Trigger Data for Payment", "type": "n8n-nodes-base.set", "notes": "Chat ID required. \n\nSend action name to handle it inside Payment workflow", "position": [1280, 1660], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "={{ Object.assign({}, $('Trigger').item.json, { \"action\": \"HandlePayment\" }) }}"}, "notesInFlow": true, "typeVersion": 3.4}, {"id": "f6f231a0-f5b7-4e58-acda-4f8dfe46a666", "name": "Trigger Data for Register", "type": "n8n-nodes-base.set", "position": [1460, 920], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "={{ $('Trigger').item.json }}"}, "typeVersion": 3.4}, {"id": "b7287bcc-b4d0-4b42-bdfc-eeb8c1a0c289", "name": "Switch", "type": "n8n-nodes-base.switch", "position": [-20, 340], "parameters": {"rules": {"values": [{"outputKey": "Message", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "360a2e5b-8736-488c-87dc-b5fcbd2b5102", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $('Trigger').item.json.message && (!$('Trigger').item.json.message.successful_payment) }}", "rightValue": "/"}]}, "renameOutput": true}, {"outputKey": "Callback", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $('Trigger').item.json.hasOwnProperty('callback_query') }}", "rightValue": ""}]}, "renameOutput": true}, {"outputKey": "System", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "e014a230-519a-4028-98b3-33c10d408c85", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $('Trigger').item.json.hasOwnProperty('my_chat_member') }}", "rightValue": "System"}]}, "renameOutput": true}, {"outputKey": "Payment", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "b03aaa37-d57f-437c-acb2-de2068d3241a", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $('Trigger').item.json.hasOwnProperty('pre_checkout_query') || $('Trigger').item.json.message.hasOwnProperty('successful_payment') }}", "rightValue": ""}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3}, {"id": "509c5575-2226-486c-8398-887eb69a74f8", "name": "Data for Invoice", "type": "n8n-nodes-base.set", "notes": "Chat ID required. \n\nSend action name to handle it inside Payment workflow", "position": [1840, -1380], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "={{ Object.assign({}, $('Trigger').item.json, { \"action\": \"SendInvoice\" }) }}"}, "notesInFlow": true, "typeVersion": 3.4}, {"id": "6634d3a8-848a-4a14-ba37-58f33e3409f2", "name": "Send Invoice", "type": "n8n-nodes-base.executeWorkflow", "position": [2040, -1380], "parameters": {"options": {"waitForSubWorkflow": false}, "workflowId": "lPX901W8CIMbKbww"}, "typeVersion": 1}, {"id": "e4bef639-3451-4ddc-ad26-481ba2acf33d", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [980, 820], "parameters": {"color": 7, "width": 1216.6513404859077, "height": 612.9550079288388, "content": "## New member or Member left"}, "typeVersion": 1}, {"id": "4affaf7d-0694-4aa0-9616-8e12ca5bee15", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [980, 1500], "parameters": {"color": 7, "width": 1216.6513404859077, "height": 496.56854733756575, "content": "## Payment handler"}, "typeVersion": 1}, {"id": "58008f10-7336-4633-ab15-51556e3b53bd", "name": "Switch_Commands", "type": "n8n-nodes-base.switch", "position": [1540, -1280], "parameters": {"rules": {"values": [{"outputKey": "Command 1", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "360a2e5b-8736-488c-87dc-b5fcbd2b5102", "operator": {"type": "string", "operation": "contains"}, "leftValue": "={{ $('Trigger').item.json.message.text }}", "rightValue": "/pay"}]}, "renameOutput": true}, {"outputKey": "Command 2", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "546c81bf-2ee0-46b2-847b-1f92b84efaf3", "operator": {"type": "string", "operation": "contains"}, "leftValue": "={{ $('Trigger').item.json.message.text }}", "rightValue": "/command2"}]}, "renameOutput": true}]}, "options": {"fallbackOutput": "extra"}}, "typeVersion": 3}], "pinData": {}, "connections": {"Switch": {"main": [[{"node": "Switch_MessageType", "type": "main", "index": 0}], [{"node": "Telegram3", "type": "main", "index": 0}], [{"node": "Start bot?", "type": "main", "index": 0}], [{"node": "Trigger Data for Payment", "type": "main", "index": 0}]]}, "Trigger": {"main": [[{"node": "Global data", "type": "main", "index": 0}]]}, "Command?": {"main": [[{"node": "Switch_Commands", "type": "main", "index": 0}], [{"node": "Telegram4", "type": "main", "index": 0}]]}, "Telegram3": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Start bot?": {"main": [[{"node": "Trigger Data for Register", "type": "main", "index": 0}], [{"node": "Change status", "type": "main", "index": 0}]]}, "Global data": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Switch_Commands": {"main": [[{"node": "Data for Invoice", "type": "main", "index": 0}], [{"node": "Telegram6", "type": "main", "index": 0}]]}, "Data for Invoice": {"main": [[{"node": "Send Invoice", "type": "main", "index": 0}]]}, "Switch_MessageType": {"main": [[{"node": "Command?", "type": "main", "index": 0}], [{"node": "Telegram1", "type": "main", "index": 0}], [{"node": "Telegram2", "type": "main", "index": 0}], [{"node": "Telegram5", "type": "main", "index": 0}]]}, "Trigger Data for Payment": {"main": [[{"node": "Payment Handler", "type": "main", "index": 0}]]}, "Trigger Data for Register": {"main": [[{"node": "Register", "type": "main", "index": 0}]]}}}