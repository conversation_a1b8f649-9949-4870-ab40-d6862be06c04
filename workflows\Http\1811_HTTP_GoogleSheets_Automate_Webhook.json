{"id": "bPxDenPJ5Ixx0txY", "meta": {"instanceId": "42d7f9cf04ccdfd3d3df5ffa87039b320845693c4b4e380cbb8cc2807641f810", "templateCredsSetupCompleted": true}, "name": "Line_Chatbot_Extract_Text_from_Pay_Slip_with_<PERSON>", "tags": [], "nodes": [{"id": "83f758b4-a80b-4f27-ac13-ee0958ed97f2", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [200, 320], "parameters": {"sessionKey": "={{ $json.body.events[0].source.userId }}", "sessionIdType": "customKey"}, "typeVersion": 1.3}, {"id": "c41976eb-4a35-4c59-8167-538c651ad7e5", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-200, 520], "parameters": {"width": 620, "height": 500, "content": "## Extract text from image\n**Prompt for Gemini**\nAnalyze image and then return in JSON Response that has the only following value: Status, From, To, Date, Amount"}, "typeVersion": 1}, {"id": "c3eb2420-a503-4039-874c-df3c2799c561", "name": "Line: Get Image", "type": "n8n-nodes-base.httpRequest", "position": [-160, 660], "parameters": {"url": "=https://api-data.line.me/v2/bot/message/{{ $json.body.events[0].message.id }}/content ", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "uFkmYj5e89iPyHcG", "name": "Line Automate Task Header Auth account"}}, "typeVersion": 4.2}, {"id": "e39e5392-b287-4efe-a9a9-1f241e82cd92", "name": "Message Type", "type": "n8n-nodes-base.set", "position": [-620, 400], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "e9deec19-c171-4af5-bfb7-f0917ba658c5", "name": "body.events[0].message.text", "type": "string", "value": "={{ $json.body.events[0].message.text }}"}, {"id": "ae9ee257-494f-4c65-a39d-4dc3505f2c01", "name": "body.events[0].message.id", "type": "string", "value": "={{ $json.body.events[0].message.id }}"}, {"id": "5e3dfc31-ed6e-4899-880d-ce73076e0cfd", "name": "body.events[0].source.userId", "type": "string", "value": "={{ $json.body.events[0].source.userId }}"}, {"id": "8918e8d3-2a30-40df-b452-c07f340972cf", "name": "body.events[0].message.type", "type": "string", "value": "={{ $json.body.events[0].message.type }}"}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "a166e880-9291-4794-a6be-47f0a86e77e7", "name": "Message Classification", "type": "n8n-nodes-base.switch", "position": [-420, 400], "parameters": {"rules": {"values": [{"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "7f862599-1eb2-4f76-910f-6caae33ea292", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $('Line: Messaging API').item.json.body.events[0].message.type }}", "rightValue": "text"}]}}, {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "0b661fab-e556-45ee-b845-67aff27fd862", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Line: Messaging API').item.json.body.events[0].message.type }}", "rightValue": "image"}]}}, {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "550e6e18-6b3e-4b08-8344-12bc76a1f736", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Line: Messaging API').item.json.body.events[0].message.stickerId }}", "rightValue": "=150"}]}}]}, "options": {}}, "typeVersion": 3.2}, {"id": "d7c29939-dd8e-43e9-89f2-879dc8ea318c", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [0, 0], "parameters": {"width": 420, "height": 460, "content": "## Gemini AI Assistant\n\nAI Assistant using Gemini 2.0 Flash Experiment unlocks new possibilities for AI agents - intelligent systems that can use memory, reasoning, and planning to complete tasks for you."}, "typeVersion": 1}, {"id": "0df36c5d-ec2a-492d-b688-4bad8d81cf38", "name": "Text Message Processing", "type": "@n8n/n8n-nodes-langchain.agent", "position": [100, 140], "parameters": {"text": "=This is the message from User: {{ $json.body.events[0].message.text }}", "agent": "conversationalAgent", "options": {}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "dfafa5ba-a855-4ebf-a19d-2addb556e791", "name": "Image Message Processing", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [100, 660], "parameters": {"text": "Analyze image and then return in JSON Response that has the only following Value:\nStatus, From, To, Date, Amount", "messages": {"messageValues": [{"message": "You are the image analyzer. You can analyze image and extract the important information from image."}, {"type": "HumanMessagePromptTemplate", "messageType": "imageBinary"}]}, "promptType": "define"}, "typeVersion": 1.5}, {"id": "b9a309bf-2c49-40e1-a0e4-9cced43d6e85", "name": "Line: Response to User", "type": "n8n-nodes-base.httpRequest", "position": [580, 660], "parameters": {"url": "https://api.line.me/v2/bot/message/reply", "method": "POST", "options": {}, "jsonBody": "={\n  \"replyToken\":\"{{ $('Line: Messaging API').item.json.body.events[0].replyToken }}\",\n  \"messages\":[\n    {\n      \"type\":\"text\",\n      \"text\": {{ JSON.stringify($json.text.replace(/^```(?:json|markdown)?\\n?/, \"\").replace(/\\n?```$/, \"\")) }}\n    }\n  ]\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "uFkmYj5e89iPyHcG", "name": "Line Automate Task Header Auth account"}}, "typeVersion": 4.2}, {"id": "ff5561fa-b334-4639-a513-554ee3507ab0", "name": "Line: Text Response to User", "type": "n8n-nodes-base.httpRequest", "position": [580, 140], "parameters": {"url": "https://api.line.me/v2/bot/message/reply", "method": "POST", "options": {}, "jsonBody": "={\n  \"replyToken\":\"{{ $('Line: Messaging API').item.json.body.events[0].replyToken }}\",\n  \"messages\":[\n    {\n      \"type\":\"text\",\n      \"text\": {{ JSON.stringify($json.output) }}\n    }\n  ]\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "uFkmYj5e89iPyHcG", "name": "Line Automate Task Header Auth account"}}, "typeVersion": 4.2}, {"id": "850f1079-cecf-4680-835f-34af829ee8f5", "name": "Text from <PERSON><PERSON>", "type": "n8n-nodes-base.googleSheets", "position": [1020, 660], "parameters": {"columns": {"value": {"To": "={{ JSON.parse($('Image Message Processing').item.json.text.replace(/^```(?:json|markdown)?\\n?/, \"\").replace(/\\n?```$/, \"\")).To }}", "Date": "={{ JSON.parse($('Image Message Processing').item.json.text.replace(/^```(?:json|markdown)?\\n?/, \"\").replace(/\\n?```$/, \"\")).Date }}", "From": "={{ JSON.parse($('Image Message Processing').item.json.text.replace(/^```(?:json|markdown)?\\n?/, \"\").replace(/\\n?```$/, \"\")).From}}", "Amount": "={{ JSON.parse($('Image Message Processing').item.json.text.replace(/^```(?:json|markdown)?\\n?/, \"\").replace(/\\n?```$/, \"\")).Amount }}", "Status": "={{ JSON.parse($('Image Message Processing').item.json.text.replace(/^```(?:json|markdown)?\\n?/, \"\").replace(/\\n?```$/, \"\")).Status }}"}, "schema": [{"id": "Status", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "From", "type": "string", "display": true, "required": false, "displayName": "From", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "To", "type": "string", "display": true, "required": false, "displayName": "To", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Date", "type": "string", "display": true, "required": false, "displayName": "Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Amount", "type": "string", "display": true, "required": false, "displayName": "Amount", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["Status"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1PUXj_t3G-arnfzNDbY0g9Pr1G4YMGrc68fDs98pV-n4/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "url", "value": "https://docs.google.com/spreadsheets/d/1PUXj_t3G-arnfzNDbY0g9Pr1G4YMGrc68fDs98pV-n4/edit?gid=0#gid=0"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "tENCv7liPQDhRoqL", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "a268daa7-76d9-437b-99e9-bd755eb4d36f", "name": "Line: Messaging API", "type": "n8n-nodes-base.webhook", "position": [-820, 400], "webhookId": "4c0de537-2889-47d2-ac44-3a9cda89c9f3", "parameters": {"path": "4c0de537-2889-47d2-ac44-3a9cda89c9f3", "options": {}, "httpMethod": "POST"}, "typeVersion": 2}, {"id": "b3c4c66a-78d6-4ad5-9a5c-afef6f86e5cc", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [460, 0], "parameters": {"width": 420, "height": 1020, "content": "## Reply to User\n\nReply the processing result to the user without coding or OCR processing."}, "typeVersion": 1}, {"id": "6c76dc81-6c10-4522-9d5f-da4579391281", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [900, 520], "parameters": {"width": 420, "height": 500, "content": "## Insert result to Google Sheet\nGet all important information from the Pay Slip and insert into Google Sheet in the same format that we have provided in our prompt.\n"}, "typeVersion": 1}, {"id": "49bac770-adb1-4ef3-8bf9-c8cf107471ad", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-860, 260], "parameters": {"width": 620, "height": 500, "content": "## Get Line Message from User\nUser can send message in both text and Pay Slip image then classify the message type in text or image so we could have single workflow for AI Assistant that support anything."}, "typeVersion": 1}, {"id": "9f034b6f-bb5b-4dc6-941d-b745f15da254", "name": "Google Gemini for Text", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [60, 320], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "Gqc4JMC0dFmMRP7Z", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "15fa3203-9230-4a1d-9e0d-87652cb9d9ab", "name": "Google Gemini for Image", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [60, 880], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "Gqc4JMC0dFmMRP7Z", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "d14ef869-77c2-49a8-9867-1775d8f0b085", "connections": {"Message Type": {"main": [[{"node": "Message Classification", "type": "main", "index": 0}]]}, "Line: Get Image": {"main": [[{"node": "Image Message Processing", "type": "main", "index": 0}]]}, "Line: Messaging API": {"main": [[{"node": "Message Type", "type": "main", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "Text Message Processing", "type": "ai_memory", "index": 0}]]}, "Google Gemini for Text": {"ai_languageModel": [[{"node": "Text Message Processing", "type": "ai_languageModel", "index": 0}]]}, "Line: Response to User": {"main": [[{"node": "Text from <PERSON><PERSON>", "type": "main", "index": 0}]]}, "Message Classification": {"main": [[{"node": "Text Message Processing", "type": "main", "index": 0}], [{"node": "Line: Get Image", "type": "main", "index": 0}], []]}, "Google Gemini for Image": {"ai_languageModel": [[{"node": "Image Message Processing", "type": "ai_languageModel", "index": 0}]]}, "Text Message Processing": {"main": [[{"node": "Line: Text Response to User", "type": "main", "index": 0}]]}, "Image Message Processing": {"main": [[{"node": "Line: Response to User", "type": "main", "index": 0}]]}}}