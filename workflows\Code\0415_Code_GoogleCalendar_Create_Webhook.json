{"meta": {"instanceId": "3c58c896c9089c8fb4d7f2b069bf3119193f239a1f538829758e2f4d6b5f5b24"}, "nodes": [{"id": "9aa9fa6c-5ccb-4f2b-b6a8-2b91f4a58355", "name": "Setup", "type": "n8n-nodes-base.set", "position": [420, 680], "parameters": {"fields": {"values": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "stringValue": "32aa914c947342169c4998b6701a77e0"}, {"name": "newsAge", "type": "numberValue", "numberValue": "10"}, {"name": "maxArticles", "stringValue": "20"}, {"name": "emails"}]}, "options": {}}, "typeVersion": 3.2}, {"id": "6f471217-b69b-4f67-981d-c7c1e2d710b6", "name": "Extract company name", "type": "n8n-nodes-base.set", "position": [1100, 480], "parameters": {"fields": {"values": [{"name": "companyName", "stringValue": "={{ $json.summary.toLowerCase().replace('meeting with', '').replace('call with', '').trim() }}"}]}, "options": {}}, "typeVersion": 3.2}, {"id": "9bb5adfa-5a36-453e-ad8d-59229ca2f1ab", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [200, 320], "parameters": {"color": 4, "width": 436, "height": 192, "content": "### Latest company news before a call\n\nThis workflow will send you a list of latest news about a company for every meeting in your calendar each day, keeping you up to date with your leads and meeting partners.\n"}, "typeVersion": 1}, {"id": "ddfa92e0-ff37-4733-9002-65fe21989d8a", "name": "Every morning @ 7", "type": "n8n-nodes-base.scheduleTrigger", "position": [200, 680], "parameters": {"rule": {"interval": [{"triggerAtHour": 7}]}}, "typeVersion": 1.1}, {"id": "b71c3683-6077-41b4-ab23-66ee22f64532", "name": "Filter meetings", "type": "n8n-nodes-base.if", "position": [840, 680], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "or", "conditions": [{"id": "bcfb06b1-d365-43a8-9802-869529baca98", "operator": {"type": "string", "operation": "startsWith"}, "leftValue": "={{ $json.summary.toLowerCase() }}", "rightValue": "call with"}, {"id": "4ea43ccf-d586-4985-87db-fc1e9f734351", "operator": {"type": "string", "operation": "startsWith"}, "leftValue": "={{ $json.summary.toLowerCase() }}", "rightValue": "meeting with"}]}}, "typeVersion": 2}, {"id": "34c4241e-e29a-4d9a-b8a8-130b9f19383f", "name": "Get latest news", "type": "n8n-nodes-base.httpRequest", "position": [1300, 480], "parameters": {"url": "=https://newsapi.org/v2/everything?apiKey={{ $('Setup').first().json.apiKey }}&q={{ $json.companyName }}&from={{ DateTime.now().minus({ days: $('Setup').first().json.newsAge }).toFormat('yyyy-MM-dd') }}&sortBy=publishedAt&language=en&pageSize={{ $('Setup').first().json.maxArticles }}&searchIn=title", "options": {}}, "typeVersion": 4.1}, {"id": "51059db7-5fec-4287-bf3f-a6a4e76ac5a4", "name": "Format for email", "type": "n8n-nodes-base.code", "position": [1500, 480], "parameters": {"mode": "runOnceForEachItem", "jsCode": "let html = `<table style=\"width: 100%\">`;\nhtml += '</table>';\n\nfor(article of $input.item.json.articles) {\n  console.log(article)\n  html += `\n    <tr>\n      <td style=\"display: flex; background-color: #f2f4f8; font-family: sans-serif; padding: 0.3em 0.5em\">\n        <div style=\"padding: 1em\">\n          <a style=\"display: block; margin-bottom: 10px; font-size: 1.2em\" href=\"${article.url}\">${article.title}</a>\n          <i>\n            ${article.description ? article.description : article.content}\n          </i>\n          <div style=\"margin-top: 1em\">\n            ${ article.source?.name ? '<b>Source:</b> ' + article.source?.name : '' }\n          </div>\n        </div>\n      </td>\n    </tr>\n  `\n}\nreturn { \"html\": html };"}, "typeVersion": 2}, {"id": "9b4351a8-edf9-49ef-829e-6998cb1eea2c", "name": "Send news", "type": "n8n-nodes-base.gmail", "position": [1700, 480], "parameters": {"sendTo": "={{ $('Setup').first().json.emails }}", "message": "={{ $json.html }}", "options": {}, "subject": "=Latest news for '{{ $('Extract company name').item.json.summary }}'"}, "credentials": {"gmailOAuth2": {"id": "10", "name": "m<PERSON><PERSON><PERSON>@gmail.com"}}, "typeVersion": 2.1}, {"id": "182504b0-3cf6-4afe-ba93-1d2bf7a02fa3", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [360, 640], "parameters": {"height": 511.499984507795, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n### Configure your workflow here\n1. `api<PERSON>ey` - Your API key for [News API](https://newsapi.org)\n2. `newsAge` - How old should news be, in days\n3. `maxArticles` - Number of articles that will be sent, max 100\n4. `emails`- List of email addresses that should receive the news, separated by commas"}, "typeVersion": 1}, {"id": "604bc73b-f805-40df-baa0-eb3de4c515f3", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [820, 660], "parameters": {"width": 231.52514020446353, "height": 275.2500697149263, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\nThis will get all meetings that start with *Meeting with* or *Call with* but feel free to update the filter to suit your needs."}, "typeVersion": 1}, {"id": "318b2bdc-712f-42a8-b224-8f0dc2c9c4e5", "name": "No meetings today", "type": "n8n-nodes-base.noOp", "position": [1700, 700], "parameters": {}, "typeVersion": 1}, {"id": "96b075cd-5c16-453e-93a6-348b22b704bb", "name": "Get meetings for today", "type": "n8n-nodes-base.googleCalendar", "position": [660, 680], "parameters": {"options": {"timeMax": "={{ $today.plus({ days: 1 }) }}", "timeMin": "={{ $today }}", "singleEvents": true}, "calendar": {"__rl": true, "mode": "list", "value": "milorad.filip<PERSON><PERSON><EMAIL>", "cachedResultName": "milorad.filip<PERSON><PERSON><EMAIL>"}, "operation": "getAll"}, "credentials": {"googleCalendarOAuth2Api": {"id": "22", "name": "Google Calendar account"}}, "typeVersion": 1}], "pinData": {}, "connections": {"Setup": {"main": [[{"node": "Get meetings for today", "type": "main", "index": 0}]]}, "Filter meetings": {"main": [[{"node": "Extract company name", "type": "main", "index": 0}], [{"node": "No meetings today", "type": "main", "index": 0}]]}, "Get latest news": {"main": [[{"node": "Format for email", "type": "main", "index": 0}]]}, "Format for email": {"main": [[{"node": "Send news", "type": "main", "index": 0}]]}, "Every morning @ 7": {"main": [[{"node": "Setup", "type": "main", "index": 0}]]}, "Extract company name": {"main": [[{"node": "Get latest news", "type": "main", "index": 0}]]}, "Get meetings for today": {"main": [[{"node": "Filter meetings", "type": "main", "index": 0}]]}}}