{"id": "mjCQV12PbF6fw8hR", "meta": {"instanceId": "021d3c82ba2d3bc090cbf4fc81c9312668bcc34297e022bb3438c5c88a43a5ff"}, "name": "Lang<PERSON>hain - Example - Workflow Retriever", "tags": [{"id": "snf16n0p2UrGP838", "name": "LangChain - Example", "createdAt": "2023-09-25T16:21:55.962Z", "updatedAt": "2023-09-25T16:21:55.962Z"}], "nodes": [{"id": "efdc3050-6c68-4419-9f12-f37d6fefb276", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [460, 200], "parameters": {}, "typeVersion": 1}, {"id": "e0edb9ab-c59f-4d34-983d-861bb2df4f01", "name": "Workflow Retriever", "type": "@n8n/n8n-nodes-langchain.retrieverWorkflow", "position": [1120, 440], "parameters": {"workflowId": "QacfBRBnf1xOyckC"}, "typeVersion": 1}, {"id": "ba47dd13-67d0-499a-b9a2-16928099efce", "name": "Retrieval QA Chain2", "type": "@n8n/n8n-nodes-langchain.chainRetrievalQa", "position": [900, 200], "parameters": {}, "typeVersion": 1}, {"id": "f6d16571-0573-4860-aed9-611f93b050ad", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [800, 480], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "4jRB4A20cPycBqP5", "name": "OpenAI account - n8n"}}, "typeVersion": 1}, {"id": "4fd00751-3db0-489b-8c7f-4ee0fb32fb51", "name": "Example Prompt", "type": "n8n-nodes-base.set", "position": [680, 200], "parameters": {"fields": {"values": [{"name": "input", "stringValue": "What notes can you find for <PERSON> and what is his email address?"}]}, "options": {}}, "typeVersion": 3}, {"id": "732b6277-cb4d-4586-ab95-778ac9473fe5", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [860, 140], "parameters": {"width": 363, "height": 211.**************, "content": "### Q&A on data returned from a workflow"}, "typeVersion": 1}, {"id": "f09583a3-78e3-4888-8251-2148ffb7ab18", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1040, 400], "parameters": {"width": 262.**************, "height": 255.*************, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nReplace \"Workflow ID\" with the ID the Subworkflow got saved as"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "48d3bdae-4cec-4b18-b92a-89215def0c68", "connections": {"Example Prompt": {"main": [[{"node": "Retrieval QA Chain2", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Retrieval QA Chain2", "type": "ai_languageModel", "index": 0}]]}, "Workflow Retriever": {"ai_retriever": [[{"node": "Retrieval QA Chain2", "type": "ai_retriever", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "Example Prompt", "type": "main", "index": 0}]]}}}