{"id": "134", "name": "Receive updates for the position of the ISS every minute and push it to a database", "nodes": [{"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [550, 300], "parameters": {"triggerTimes": {"item": [{"mode": "everyMinute"}]}}, "typeVersion": 1}, {"name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [750, 300], "parameters": {"url": "https://api.wheretheiss.at/v1/satellites/25544/positions", "options": {}, "queryParametersUi": {"parameter": [{"name": "timestamps", "value": "={{Date.now();}}"}]}}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [950, 300], "parameters": {"values": {"number": [{"name": "latitude", "value": "={{$node[\"HTTP Request\"].json[\"0\"][\"latitude\"]}}"}, {"name": "longitude", "value": "={{$node[\"HTTP Request\"].json[\"0\"][\"longitude\"]}}"}, {"name": "timestamp", "value": "={{$node[\"HTTP Request\"].json[\"0\"][\"timestamp\"]}}"}], "string": []}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "Google Cloud Realtime Database", "type": "n8n-nodes-base.googleFirebaseRealtimeDatabase", "position": [1150, 300], "parameters": {"path": "iss", "operation": "push", "projectId": "", "attributes": "latitude, longitude, timestamp"}, "credentials": {"googleFirebaseRealtimeDatabaseOAuth2Api": "firebase realtime credentials"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Set": {"main": [[{"node": "Google Cloud Realtime Database", "type": "main", "index": 0}]]}, "Cron": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}}