{"id": "BMI5WkmyU8nZqfII", "meta": {"instanceId": "e03b0f22ca12c92061d789d5980a9bc31d9d7e7dd7513ac93c09ac5a0d147623", "templateCredsSetupCompleted": true}, "name": "modelo do chatbot", "tags": [], "nodes": [{"id": "c6e454af-70a1-4c65-8450-8159f7fc738b", "name": "If", "type": "n8n-nodes-base.if", "position": [160, 560], "parameters": {"options": {}, "conditions": {"options": {"version": 1, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "7ea831a4-0e20-4725-a6f5-3dc2f41f1cf4", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.leadData }}", "rightValue": ""}, {"id": "ccb46339-4e43-42e6-aa45-d5a0cbd62214", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "", "rightValue": ""}]}}, "typeVersion": 2}, {"id": "2221736f-ef99-4ac8-8a81-51af6d4e7dcd", "name": "Edit Fields1", "type": "n8n-nodes-base.set", "position": [440, 960], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "19a16867-b574-4b99-82f1-a86752b7fe9f", "name": "chatInput", "type": "string", "value": "=\"Hello, just so you can get to know me, with no intention of a response, please save this information in your memory. My name is {{ $json.leadData.name }}. I am {{ $json.leadData.age }} years old and currently live in {{ $json.leadData.city }}, {{ $json.leadData.state }}. My profession is {{ $json.leadData.profession }}, and my education level is {{ $json.leadData.educationLevel }}.\nIf I’m part of an adhesion group and have an entity, it would be {{ $json.leadData.entity }}.\n\nI am using a {{ $json.leadData.deviceType }} device to access this through the {{ $json.leadData.channel }} channel. At the moment, I am looking for a health insurance plan of type {{ $json.leadData.quotationType }}.\""}, {"id": "0df8d578-8332-4cde-9044-489de16ab390", "name": "session_id", "type": "string", "value": "={{ $json.session_id }}"}]}}, "typeVersion": 3.4}, {"id": "6aa1b3a4-0e6a-4312-9d9f-f67c4bf8f443", "name": "Edit Fields2", "type": "n8n-nodes-base.set", "position": [920, 960], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "19a16867-b574-4b99-82f1-a86752b7fe9f", "name": "chatInput", "type": "string", "value": "={{ $('Chat Trigger').item.json.chatInput}}"}, {"id": "0df8d578-8332-4cde-9044-489de16ab390", "name": "session_id", "type": "string", "value": "={{ $('Chat Trigger').item.json.session_id }}"}]}}, "typeVersion": 3.4}, {"id": "6afe6158-7a8b-4a83-a778-6fd28e2a11af", "name": "OpenAI", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [600, 960], "parameters": {"options": {}, "resource": "assistant", "assistantId": {"__rl": true, "mode": "list", "value": "asst_numdCoMZPQ6GwfiJg5drg9hr", "cachedResultName": "Chat IA - Testes - Dezembro - APIS"}}, "credentials": {"openAiApi": {"id": "FW1FWHcMcwemQ1kZ", "name": "OpenAi account"}}, "typeVersion": 1.4}, {"id": "4b961f1d-7da2-4a0b-98e3-7ec35ee14335", "name": "<PERSON><PERSON>", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-20, 560], "webhookId": "1f83e8ac-d465-454a-8327-cef7f0149cb1", "parameters": {"public": true, "options": {}, "initialMessages": "Olá 👋\n<PERSON>u Jo<PERSON>, o serviço de IA do Joov, me mande sua pergunta e responderei em seguida! :)"}, "typeVersion": 1}, {"id": "dccdb07f-97db-4a5a-9b09-02a5de65246e", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "position": [640, 720], "parameters": {"tableName": "aimessages", "sessionKey": "={{ $('Chat Trigger').item.json.session_id }}{{ $json.sessionId }}", "sessionIdType": "customKey", "contextWindowLength": 30}, "credentials": {"postgres": {"id": "M1cYa0bOSX1nfczy", "name": "Postgres account"}}, "typeVersion": 1.3}, {"id": "553dd27b-ab06-4605-99e0-8f15735cfff3", "name": "Postgres Chat Memory1", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "position": [760, 1160], "parameters": {"tableName": "aimessages", "sessionKey": "={{ $('Chat Trigger').item.json.session_id }}{{ $json.sessionId }}", "sessionIdType": "customKey", "contextWindowLength": 1}, "credentials": {"postgres": {"id": "M1cYa0bOSX1nfczy", "name": "Postgres account"}}, "typeVersion": 1.3}, {"id": "0103fb97-c691-4bd3-b26d-85aaa9774594", "name": "Products in Daatabase", "type": "n8n-nodes-base.mySqlTool", "position": [1460, 600], "parameters": {"query": "SELECT * \nFROM Products p \nWHERE \n  cityQuery = '{{ $fromAI(\"cityQuery\") }}' AND \n  state = '{{ $fromAI(\"state\") }}' AND \n  modality = 'PME' AND \n  removed = 0 AND \n  ({{ $fromAI(\"holderCount\") || 1 }} + {{ $fromAI(\"dependentsCount\") || 0 }}) BETWEEN p.minLifeAmount AND p.maxLifeAmount AND\n  (CASE\n      WHEN {{ $fromAI(\"holderAge\") }} BETWEEN 0 AND 18 THEN priceAtAge0To18\n      WHEN {{ $fromAI(\"holderAge\") }} BETWEEN 19 AND 23 THEN priceAtAge19To23\n      WHEN {{ $fromAI(\"holderAge\") }} BETWEEN 24 AND 28 THEN priceAtAge24To28\n      WHEN {{ $fromAI(\"holderAge\") }} BETWEEN 29 AND 33 THEN priceAtAge29To33\n      WHEN {{ $fromAI(\"holderAge\") }} BETWEEN 34 AND 38 THEN priceAtAge34To38\n      WHEN {{ $fromAI(\"holderAge\") }} BETWEEN 39 AND 43 THEN priceAtAge39To43\n      WHEN {{ $fromAI(\"holderAge\") }} BETWEEN 44 AND 48 THEN priceAtAge44To48\n      WHEN {{ $fromAI(\"holderAge\") }} BETWEEN 49 AND 53 THEN priceAtAge49To53\n      WHEN {{ $fromAI(\"holderAge\") }} BETWEEN 54 AND 58 THEN priceAtAge54To58\n      ELSE priceAtAge59To199\n  END) IS NOT NULL\nORDER BY \n  (CASE\n      WHEN {{ $fromAI(\"holderAge\") }} BETWEEN 0 AND 18 THEN priceAtAge0To18\n      WHEN {{ $fromAI(\"holderAge\") }} BETWEEN 19 AND 23 THEN priceAtAge19To23\n      WHEN {{ $fromAI(\"holderAge\") }} BETWEEN 24 AND 28 THEN priceAtAge24To28\n      WHEN {{ $fromAI(\"holderAge\") }} BETWEEN 29 AND 33 THEN priceAtAge29To33\n      WHEN {{ $fromAI(\"holderAge\") }} BETWEEN 34 AND 38 THEN priceAtAge34To38\n      WHEN {{ $fromAI(\"holderAge\") }} BETWEEN 39 AND 43 THEN priceAtAge39To43\n      WHEN {{ $fromAI(\"holderAge\") }} BETWEEN 44 AND 48 THEN priceAtAge44To48\n      WHEN {{ $fromAI(\"holderAge\") }} BETWEEN 49 AND 53 THEN priceAtAge49To53\n      WHEN {{ $fromAI(\"holderAge\") }} BETWEEN 54 AND 58 THEN priceAtAge54To58\n      ELSE priceAtAge59To199\n  END) ASC, \n  createdAt DESC\nLIMIT 3;\n", "options": {"detailedOutput": true}, "operation": "execute<PERSON>uery", "descriptionType": "manual", "toolDescription": "//  Search for the X product bla bla bla"}, "credentials": {"mySql": {"id": "lkGJt8aNB0azyaGy", "name": "MySQL account 2"}}, "typeVersion": 2.4}, {"id": "0cdfd89f-eb9e-4b6c-90d1-1cf8d6ed96bb", "name": "Knowledge Base", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1340, 600], "parameters": {"url": "https://quotation.joov.com.br/widget/info?modalidade={modalidade}&estado=SP&cidade={city}&operadora={operadora}", "toolDescription": "Here you will find the knowlegde base of my shop and bla bla bla Use this when they ask for price, whatever i want."}, "typeVersion": 1.1}, {"id": "393f792a-4eff-4b33-aac0-025fc622a4b3", "name": "External API", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1200, 600], "parameters": {"url": "https://integracao-sed-alb-*********.us-east-1.elb.amazonaws.com/findByNameAndBirthDate", "method": "POST", "jsonBody": "={\n    \"name\": \"{{json.name}}\",\n    \"birthdate\": \"{{json.birthdate }}\"\n}", "sendBody": true, "specifyBody": "json", "toolDescription": "Pegue o nome completo em camel case, exemplo: <PERSON><PERSON><PERSON>, e a data de nacimento nesse formato: 1990-03-28"}, "typeVersion": 1.1}, {"id": "7ce7a5e7-6238-4479-a26f-bdcde1784188", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1160, 414], "parameters": {"color": 5, "width": 436.73182*********, "height": 367.*************, "content": "TOOLS"}, "typeVersion": 1}, {"id": "df6737ca-c588-48fc-9761-2a5307841298", "name": "OpenAI2", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [460, 460], "parameters": {"text": "={{ $json.chatInput }}", "prompt": "define", "options": {}, "resource": "assistant", "assistantId": {"__rl": true, "mode": "list", "value": "asst_x2qfc7EuoPv7XGOL84ClEZ3L", "cachedResultName": "PINE"}}, "credentials": {"openAiApi": {"id": "FW1FWHcMcwemQ1kZ", "name": "OpenAi account"}}, "typeVersion": 1.4}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "d1dc3988-6677-47c9-b91a-6875c7b6151d", "connections": {"If": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}], [{"node": "OpenAI2", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "Edit Fields2", "type": "main", "index": 0}]]}, "Chat Trigger": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}, "Edit Fields2": {"main": [[{"node": "OpenAI2", "type": "main", "index": 0}]]}, "External API": {"ai_tool": [[{"node": "OpenAI2", "type": "ai_tool", "index": 0}]]}, "Knowledge Base": {"ai_tool": [[{"node": "OpenAI2", "type": "ai_tool", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "OpenAI2", "type": "ai_memory", "index": 0}]]}, "Postgres Chat Memory1": {"ai_memory": [[{"node": "OpenAI", "type": "ai_memory", "index": 0}]]}, "Products in Daatabase": {"ai_tool": [[{"node": "OpenAI2", "type": "ai_tool", "index": 0}]]}}}