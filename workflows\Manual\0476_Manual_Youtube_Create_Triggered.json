{"id": "21", "name": "Upload video, create playlist and add video to playlist", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [210, 300], "parameters": {}, "typeVersion": 1}, {"name": "YouTube", "type": "n8n-nodes-base.youTube", "position": [610, 300], "parameters": {"title": "n8n", "options": {}, "resource": "video", "operation": "upload", "categoryId": "28", "regionCode": "IN", "binaryProperty": "=data"}, "credentials": {"youTubeOAuth2Api": "google-youtube"}, "typeVersion": 1}, {"name": "Read Binary File", "type": "n8n-nodes-base.readBinaryFile", "position": [410, 300], "parameters": {"filePath": ""}, "typeVersion": 1}, {"name": "YouTube1", "type": "n8n-nodes-base.youTube", "position": [810, 300], "parameters": {"title": "n8n", "options": {}, "resource": "playlist", "operation": "create"}, "credentials": {"youTubeOAuth2Api": "google-youtube"}, "typeVersion": 1}, {"name": "YouTube2", "type": "n8n-nodes-base.youTube", "position": [1010, 300], "parameters": {"options": {}, "videoId": "={{$node[\"YouTube\"].json[\"id\"]}}", "resource": "playlistItem", "playlistId": ""}, "credentials": {"youTubeOAuth2Api": "google-youtube"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"YouTube": {"main": [[{"node": "YouTube1", "type": "main", "index": 0}]]}, "YouTube1": {"main": [[{"node": "YouTube2", "type": "main", "index": 0}]]}, "Read Binary File": {"main": [[{"node": "YouTube", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Read Binary File", "type": "main", "index": 0}]]}}}