{"id": "K3uf8aY8wipScEay", "meta": {"instanceId": "558d88703fb65b2d0e44613bc35916258b0f0bf983c5d4730c00c424b77ca36a", "templateCredsSetupCompleted": true}, "name": "Google analytics template", "tags": [], "nodes": [{"id": "6a9fc442-d0a3-48be-8dff-94f8d9cd5cf1", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [460, 460], "parameters": {"rule": {"interval": [{"field": "weeks"}]}}, "typeVersion": 1.2}, {"id": "484cbc41-f57d-4c3d-a458-e439d480d290", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [460, 640], "parameters": {}, "typeVersion": 1}, {"id": "b1b66e9b-5fea-407b-9c1e-39bd2a9d4a90", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [460, 100], "parameters": {"width": 714.172987012987, "content": "## Send Google analytics to A.I. and save results to baserow\n\nThis workflow will check for country views, page engagement and google search console results. It will take this week's data and compare it to last week's data.\n\n[You can read more about this workflow here](https://rumjahn.com/how-i-used-a-i-to-be-an-seo-expert-and-analyzed-my-google-analytics-data-in-n8n-and-make-com/)"}, "typeVersion": 1}, {"id": "adde29fc-ddb5-4b50-aa78-313ac9ede879", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [633.6540259740264, 320], "parameters": {"color": 4, "width": 2097.92831168831, "height": 342.6576623376624, "content": "## Property ID\n\n1. Create your [Google Analytics Credentials](https://docs.n8n.io/integrations/builtin/credentials/google/oauth-single-service/?utm_source=n8n_app&utm_medium=credential_settings&utm_campaign=create_new_credentials_modal)\n2. Enter your [property ID](https://developers.google.com/analytics/devguides/reporting/data/v1/property-id)."}, "typeVersion": 1}, {"id": "f2fb8535-e81e-4ca1-80df-ee68edba6386", "name": "Get Page Engagement Stats for this week", "type": "n8n-nodes-base.googleAnalytics", "position": [700, 460], "parameters": {"simple": false, "returnAll": true, "metricsGA4": {"metricValues": [{"name": "screenPageViews", "listName": "other"}, {"name": "activeUsers", "listName": "other"}, {"name": "screenPageViewsPerUser", "listName": "other"}, {"name": "eventCount", "listName": "other"}]}, "propertyId": {"__rl": true, "mode": "id", "value": "*********"}, "dimensionsGA4": {"dimensionValues": [{"name": "unifiedScreenName", "listName": "other"}]}, "additionalFields": {}}, "credentials": {"googleAnalyticsOAuth2": {"id": "b1GX8VBMKCUNweV1", "name": "Google Analytics account"}}, "typeVersion": 2}, {"id": "1d761425-cebf-4787-b286-b723a0851485", "name": "Get Page Engagement Stats for prior week", "type": "n8n-nodes-base.googleAnalytics", "position": [1060, 460], "parameters": {"simple": false, "endDate": "2024-10-23T00:00:00", "dateRange": "custom", "returnAll": true, "startDate": "={{$today.minus({days: 14})}}", "metricsGA4": {"metricValues": [{"name": "screenPageViews", "listName": "other"}, {"name": "activeUsers", "listName": "other"}, {"name": "screenPageViewsPerUser", "listName": "other"}, {"name": "eventCount", "listName": "other"}]}, "propertyId": {"__rl": true, "mode": "id", "value": "*********"}, "dimensionsGA4": {"dimensionValues": [{"name": "unifiedScreenName", "listName": "other"}]}, "additionalFields": {}}, "typeVersion": 2}, {"id": "f8dac36b-9e8a-407f-b923-b4cea368f1bc", "name": "Parse data from Google Analytics", "type": "n8n-nodes-base.code", "position": [880, 460], "parameters": {"jsCode": "function transformToUrlString(items) {\n // Debug logging\n console.log('Input items:', JSON.stringify(items, null, 2));\n \n // Check if items is an array and has content\n if (!Array.isArray(items) || items.length === 0) {\n console.log('Items is not an array or is empty');\n throw new Error('Invalid data structure');\n }\n\n // Check if first item exists and has json property\n if (!items[0] || !items[0].json) {\n console.log('First item is missing or has no json property');\n throw new Error('Invalid data structure');\n }\n\n // Get the analytics data\n const analyticsData = items[0].json;\n \n // Check if analyticsData has rows\n if (!analyticsData || !Array.isArray(analyticsData.rows)) {\n console.log('Analytics data is missing or has no rows array');\n throw new Error('Invalid data structure');\n }\n \n // Map each row to a simplified object\n const simplified = analyticsData.rows.map(row => {\n if (!row.dimensionValues?.[0]?.value || !row.metricValues?.length) {\n console.log('Invalid row structure:', row);\n throw new Error('Invalid row structure');\n }\n \n return {\n page: row.dimensionValues[0].value,\n pageViews: parseInt(row.metricValues[0].value) || 0,\n activeUsers: parseInt(row.metricValues[1].value) || 0,\n viewsPerUser: parseFloat(row.metricValues[2].value) || 0,\n eventCount: parseInt(row.metricValues[3].value) || 0\n };\n });\n \n // Convert to JSON string and encode for URL\n return encodeURIComponent(JSON.stringify(simplified));\n}\n\n// Get input data and transform it\nconst urlString = transformToUrlString($input.all());\n\n// Return the result\nreturn { json: { urlString } };"}, "typeVersion": 2}, {"id": "ed880442-c92e-4347-b277-e8794aea6fbc", "name": "Parse GA data", "type": "n8n-nodes-base.code", "position": [1240, 460], "parameters": {"jsCode": "function transformToUrlString(items) {\n // Debug logging\n console.log('Input items:', JSON.stringify(items, null, 2));\n \n // Check if items is an array and has content\n if (!Array.isArray(items) || items.length === 0) {\n console.log('Items is not an array or is empty');\n throw new Error('Invalid data structure');\n }\n\n // Check if first item exists and has json property\n if (!items[0] || !items[0].json) {\n console.log('First item is missing or has no json property');\n throw new Error('Invalid data structure');\n }\n\n // Get the analytics data\n const analyticsData = items[0].json;\n \n // Check if analyticsData has rows\n if (!analyticsData || !Array.isArray(analyticsData.rows)) {\n console.log('Analytics data is missing or has no rows array');\n throw new Error('Invalid data structure');\n }\n \n // Map each row to a simplified object\n const simplified = analyticsData.rows.map(row => {\n if (!row.dimensionValues?.[0]?.value || !row.metricValues?.length) {\n console.log('Invalid row structure:', row);\n throw new Error('Invalid row structure');\n }\n \n return {\n page: row.dimensionValues[0].value,\n pageViews: parseInt(row.metricValues[0].value) || 0,\n activeUsers: parseInt(row.metricValues[1].value) || 0,\n viewsPerUser: parseFloat(row.metricValues[2].value) || 0,\n eventCount: parseInt(row.metricValues[3].value) || 0\n };\n });\n \n // Convert to JSON string and encode for URL\n return encodeURIComponent(JSON.stringify(simplified));\n}\n\n// Get input data and transform it\nconst urlString = transformToUrlString($input.all());\n\n// Return the result\nreturn { json: { urlString } };"}, "typeVersion": 2}, {"id": "46e092cc-af94-4e64-aa92-931c56345eff", "name": "Get Google Search Results for this week", "type": "n8n-nodes-base.googleAnalytics", "position": [1420, 460], "parameters": {"simple": false, "returnAll": true, "metricsGA4": {"metricValues": [{"name": "activeUsers", "listName": "other"}, {"name": "engagedSessions", "listName": "other"}, {"name": "engagementRate", "listName": "other"}, {"name": "eventCount", "listName": "other"}, {"name": "organicGoogleSearchAveragePosition", "listName": "other"}, {"name": "organicGoogleSearchClickThroughRate", "listName": "other"}, {"name": "organicGoogleSearchClicks", "listName": "other"}, {"name": "organicGoogleSearchImpressions", "listName": "other"}]}, "propertyId": {"__rl": true, "mode": "id", "value": "*********"}, "dimensionsGA4": {"dimensionValues": [{"name": "landingPagePlusQueryString", "listName": "other"}]}, "additionalFields": {}}, "credentials": {"googleAnalyticsOAuth2": {"id": "b1GX8VBMKCUNweV1", "name": "Google Analytics account"}}, "typeVersion": 2}, {"id": "709d0aaf-bd3d-4d83-9e66-b7df495855bd", "name": "Get Google Search Results for last week", "type": "n8n-nodes-base.googleAnalytics", "position": [1780, 460], "parameters": {"simple": false, "endDate": "={{$today.minus({days: 7})}}", "dateRange": "custom", "returnAll": true, "startDate": "={{$today.minus({days: 14})}}", "metricsGA4": {"metricValues": [{"name": "activeUsers", "listName": "other"}, {"name": "engagedSessions", "listName": "other"}, {"name": "engagementRate", "listName": "other"}, {"name": "eventCount", "listName": "other"}, {"name": "organicGoogleSearchAveragePosition", "listName": "other"}, {"name": "organicGoogleSearchClickThroughRate", "listName": "other"}, {"name": "organicGoogleSearchClicks", "listName": "other"}, {"name": "organicGoogleSearchImpressions", "listName": "other"}]}, "propertyId": {"__rl": true, "mode": "id", "value": "*********"}, "dimensionsGA4": {"dimensionValues": [{"name": "landingPagePlusQueryString", "listName": "other"}]}, "additionalFields": {}}, "credentials": {"googleAnalyticsOAuth2": {"id": "b1GX8VBMKCUNweV1", "name": "Google Analytics account"}}, "typeVersion": 2}, {"id": "7d3835d6-d1f5-4159-8e34-871871e63989", "name": "Parse Google Analytics Data", "type": "n8n-nodes-base.code", "position": [1600, 460], "parameters": {"jsCode": "function transformToUrlString(items) {\n // In n8n, we need to check if items is an array and get the json property\n const data = items[0].json;\n \n if (!data || !data.rows) {\n console.log('No valid data found');\n return encodeURIComponent(JSON.stringify([]));\n }\n \n try {\n // Process each row\n const simplified = data.rows.map(row => ({\n page: row.dimensionValues[0].value,\n activeUsers: parseInt(row.metricValues[0].value) || 0,\n engagedSessions: parseInt(row.metricValues[1].value) || 0,\n engagementRate: parseFloat(row.metricValues[2].value) || 0,\n eventCount: parseInt(row.metricValues[3].value) || 0,\n avgPosition: parseFloat(row.metricValues[4].value) || 0,\n ctr: parseFloat(row.metricValues[5].value) || 0,\n clicks: parseInt(row.metricValues[6].value) || 0,\n impressions: parseInt(row.metricValues[7].value) || 0\n }));\n \n return encodeURIComponent(JSON.stringify(simplified));\n } catch (error) {\n console.log('Error processing data:', error);\n throw new Error('Invalid data structure');\n }\n}\n\n// Get the input data\nconst items = $input.all();\n\n// Process the data\nconst result = transformToUrlString(items);\n\n// Return the result\nreturn { json: { urlString: result } };"}, "typeVersion": 2}, {"id": "c018fda4-a2e6-48f4-aabb-039c66374dc7", "name": "Parse Google Analytics Data1", "type": "n8n-nodes-base.code", "position": [1940, 460], "parameters": {"jsCode": "function transformToUrlString(items) {\n // In n8n, we need to check if items is an array and get the json property\n const data = items[0].json;\n \n if (!data || !data.rows) {\n console.log('No valid data found');\n return encodeURIComponent(JSON.stringify([]));\n }\n \n try {\n // Process each row\n const simplified = data.rows.map(row => ({\n page: row.dimensionValues[0].value,\n activeUsers: parseInt(row.metricValues[0].value) || 0,\n engagedSessions: parseInt(row.metricValues[1].value) || 0,\n engagementRate: parseFloat(row.metricValues[2].value) || 0,\n eventCount: parseInt(row.metricValues[3].value) || 0,\n avgPosition: parseFloat(row.metricValues[4].value) || 0,\n ctr: parseFloat(row.metricValues[5].value) || 0,\n clicks: parseInt(row.metricValues[6].value) || 0,\n impressions: parseInt(row.metricValues[7].value) || 0\n }));\n \n return encodeURIComponent(JSON.stringify(simplified));\n } catch (error) {\n console.log('Error processing data:', error);\n throw new Error('Invalid data structure');\n }\n}\n\n// Get the input data\nconst items = $input.all();\n\n// Process the data\nconst result = transformToUrlString(items);\n\n// Return the result\nreturn { json: { urlString: result } };"}, "typeVersion": 2}, {"id": "d8f775cd-daf9-42de-a527-d932be46d945", "name": "Get Country views data for this week", "type": "n8n-nodes-base.googleAnalytics", "position": [2120, 460], "parameters": {"simple": false, "returnAll": true, "metricsGA4": {"metricValues": [{"name": "activeUsers", "listName": "other"}, {"name": "newUsers", "listName": "other"}, {"name": "engagementRate", "listName": "other"}, {"name": "engagedSessions", "listName": "other"}, {"name": "eventCount", "listName": "other"}, {"listName": "other"}, {"name": "sessions", "listName": "other"}]}, "propertyId": {"__rl": true, "mode": "id", "value": "*********"}, "dimensionsGA4": {"dimensionValues": [{"name": "country", "listName": "other"}]}, "additionalFields": {}}, "credentials": {"googleAnalyticsOAuth2": {"id": "b1GX8VBMKCUNweV1", "name": "Google Analytics account"}}, "typeVersion": 2}, {"id": "7119e57c-cbf4-49a9-b0c9-1f3da1fd2af3", "name": "Get Country views data for last week", "type": "n8n-nodes-base.googleAnalytics", "position": [2440, 460], "parameters": {"simple": false, "endDate": "={{$today.minus({days: 7})}}", "dateRange": "custom", "returnAll": true, "startDate": "={{$today.minus({days: 14})}}", "metricsGA4": {"metricValues": [{"name": "activeUsers", "listName": "other"}, {"name": "newUsers", "listName": "other"}, {"name": "engagementRate", "listName": "other"}, {"name": "engagedSessions", "listName": "other"}, {"name": "eventCount", "listName": "other"}, {"listName": "other"}, {"name": "sessions", "listName": "other"}]}, "propertyId": {"__rl": true, "mode": "id", "value": "*********"}, "dimensionsGA4": {"dimensionValues": [{"name": "country", "listName": "other"}]}, "additionalFields": {}}, "typeVersion": 2}, {"id": "546d6cd2-6db6-4276-be35-abbe5a7e9b6a", "name": "Parse Google analytics data", "type": "n8n-nodes-base.code", "position": [2280, 460], "parameters": {"jsCode": "function transformToUrlString(items) {\n // In n8n, we need to check if items is an array and get the json property\n const data = items[0].json;\n \n if (!data || !data.rows) {\n console.log('No valid data found');\n return encodeURIComponent(JSON.stringify([]));\n }\n \n try {\n // Process each row\n const simplified = data.rows.map(row => ({\n country: row.dimensionValues[0].value,\n activeUsers: parseInt(row.metricValues[0].value) || 0,\n newUsers: parseInt(row.metricValues[1].value) || 0,\n engagementRate: parseFloat(row.metricValues[2].value) || 0,\n engagedSessions: parseInt(row.metricValues[3].value) || 0,\n eventCount: parseInt(row.metricValues[4].value) || 0,\n totalUsers: parseInt(row.metricValues[5].value) || 0,\n sessions: parseInt(row.metricValues[6].value) || 0\n }));\n \n return encodeURIComponent(JSON.stringify(simplified));\n } catch (error) {\n console.log('Error processing data:', error);\n throw new Error('Invalid data structure');\n }\n}\n\n// Get the input data\nconst items = $input.all();\n\n// Process the data\nconst result = transformToUrlString(items);\n\n// Return the result\nreturn { json: { urlString: result } };"}, "typeVersion": 2}, {"id": "87cb137c-686d-49a5-8657-06ed0c5f5c27", "name": "Parse Google analytics data1", "type": "n8n-nodes-base.code", "position": [2600, 460], "parameters": {"jsCode": "function transformToUrlString(items) {\n // In n8n, we need to check if items is an array and get the json property\n const data = items[0].json;\n \n if (!data || !data.rows) {\n console.log('No valid data found');\n return encodeURIComponent(JSON.stringify([]));\n }\n \n try {\n // Process each row\n const simplified = data.rows.map(row => ({\n country: row.dimensionValues[0].value,\n activeUsers: parseInt(row.metricValues[0].value) || 0,\n newUsers: parseInt(row.metricValues[1].value) || 0,\n engagementRate: parseFloat(row.metricValues[2].value) || 0,\n engagedSessions: parseInt(row.metricValues[3].value) || 0,\n eventCount: parseInt(row.metricValues[4].value) || 0,\n totalUsers: parseInt(row.metricValues[5].value) || 0,\n sessions: parseInt(row.metricValues[6].value) || 0\n }));\n \n return encodeURIComponent(JSON.stringify(simplified));\n } catch (error) {\n console.log('Error processing data:', error);\n throw new Error('Invalid data structure');\n }\n}\n\n// Get the input data\nconst items = $input.all();\n\n// Process the data\nconst result = transformToUrlString(items);\n\n// Return the result\nreturn { json: { urlString: result } };"}, "typeVersion": 2}, {"id": "06c4478d-a13a-4587-9f1f-451a68798a9f", "name": "Send page data to A.I.", "type": "n8n-nodes-base.httpRequest", "position": [2760, 460], "parameters": {"url": "https://openrouter.ai/api/v1/chat/completions", "method": "POST", "options": {}, "jsonBody": "={\n \"model\": \"meta-llama/llama-3.1-70b-instruct:free\",\n \"messages\": [\n {\n \"role\": \"user\",\n \"content\": \"You are an SEO expert. Compare the data from past 2 weeks, give me a table in markdown. Then give me 5 suggestions to improve my SEO. Output the data so that it works with markdown editors. Data from 2 weeks ago:{{ $json.urlString }} Data from last week: {{ $('Parse data from Google Analytics').item.json.urlString }}\"\n }\n ]\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "typeVersion": 4.2, "alwaysOutputData": false}, {"id": "4ad522b0-afe4-4eff-aa16-b86cc892ead8", "name": "Send page Search data to A.I.", "type": "n8n-nodes-base.httpRequest", "position": [2920, 460], "parameters": {"url": "https://openrouter.ai/api/v1/chat/completions", "method": "POST", "options": {}, "jsonBody": "={\n \"model\": \"meta-llama/llama-3.1-70b-instruct:free\",\n \"messages\": [\n {\n \"role\": \"user\",\n \"content\": \"You are an SEO expert. Compare the data from past 2 weeks, give me a table in markdown. Then give me 5 suggestions to improve my SEO. Output the data so that it works with markdown editors. Data from 2 weeks ago:{{ $('Parse Google Analytics Data1').item.json.urlString }} Data from last week:{{ $('Parse Google Analytics Data').item.json.urlString }}\"\n }\n ]\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "typeVersion": 4.2, "alwaysOutputData": false}, {"id": "07e1eebf-f16a-44c0-83b5-76bf65a3d3fc", "name": "Send country view data to A.I.", "type": "n8n-nodes-base.httpRequest", "position": [3080, 460], "parameters": {"url": "https://openrouter.ai/api/v1/chat/completions", "method": "POST", "options": {}, "jsonBody": "={\n \"model\": \"meta-llama/llama-3.1-70b-instruct:free\",\n \"messages\": [\n {\n \"role\": \"user\",\n \"content\": \"You are an SEO expert. Compare the data from past 2 weeks, give me a table in markdown. Then give me 5 suggestions to improve my SEO. Output the data so that it works with markdown editors. Data from 2 weeks ago:{{ $('Parse Google analytics data1').item.json.urlString }} Data from last week:{{ $('Parse Google analytics data').item.json.urlString }}\"\n }\n ]\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "typeVersion": 4.2, "alwaysOutputData": false}, {"id": "c4648ad8-2377-42a0-a431-931b53631c9d", "name": "Save A.I. output to Baserow", "type": "n8n-nodes-base.baserow", "position": [3240, 460], "parameters": {"tableId": 601, "fieldsUi": {"fieldValues": [{"fieldId": 5833, "fieldValue": "Name of your blog"}, {"fieldId": 5831, "fieldValue": "={{ $('Send page data to <PERSON>.<PERSON>.').item.json.choices[0].message.content }}"}, {"fieldId": 5830, "fieldValue": "={{ $('Send page Search data to <PERSON>.I.').item.json.choices[0].message.content }}"}, {"fieldId": 5832, "fieldValue": "={{ $json.choices[0].message.content }}"}, {"fieldId": 5829, "fieldValue": "={{ DateTime.now() }}"}]}, "operation": "create", "databaseId": 121}, "typeVersion": 1}, {"id": "e185c836-c12f-4452-92bd-0daaf33b653a", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [2760, 180], "parameters": {"color": 5, "width": 441.7412987012988, "height": 508.95792207792226, "content": "## Send data to <PERSON><PERSON><PERSON><PERSON> in your Openrouter A.I. credentials. Use Header Auth.\n- Username: Authorization\n- Password: Bearer {insert your API key}\n\nRemember to add a space after bearer. Also, feel free to modify the prompt to A.1."}, "typeVersion": 1}, {"id": "a1de2d16-d09e-4c74-8be1-f6bab8c34246", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [3220, 180], "parameters": {"color": 6, "width": 331.32883116883124, "height": 474.88, "content": "## Send data to Baserow\n\nCreate a table first with the following columns:\n- Name\n- Country Views\n- Page Views\n- Search Report\n- Blog \n\nEnter the name of your website under \"Blog\" field."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "ac4b5eac-1c84-49ce-9ff7-794f857265b4", "connections": {"Parse GA data": {"main": [[{"node": "Get Google Search Results for this week", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Get Page Engagement Stats for this week", "type": "main", "index": 0}]]}, "Send page data to A.I.": {"main": [[{"node": "Send page Search data to A.I.", "type": "main", "index": 0}]]}, "Parse Google Analytics Data": {"main": [[{"node": "Get Google Search Results for last week", "type": "main", "index": 0}]]}, "Parse Google analytics data": {"main": [[{"node": "Get Country views data for last week", "type": "main", "index": 0}]]}, "Parse Google Analytics Data1": {"main": [[{"node": "Get Country views data for this week", "type": "main", "index": 0}]]}, "Parse Google analytics data1": {"main": [[{"node": "Send page data to A.I.", "type": "main", "index": 0}]]}, "Send page Search data to A.I.": {"main": [[{"node": "Send country view data to A.I.", "type": "main", "index": 0}]]}, "Send country view data to A.I.": {"main": [[{"node": "Save A.I. output to Baserow", "type": "main", "index": 0}]]}, "Parse data from Google Analytics": {"main": [[{"node": "Get Page Engagement Stats for prior week", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Get Page Engagement Stats for this week", "type": "main", "index": 0}]]}, "Get Country views data for last week": {"main": [[{"node": "Parse Google analytics data1", "type": "main", "index": 0}]]}, "Get Country views data for this week": {"main": [[{"node": "Parse Google analytics data", "type": "main", "index": 0}]]}, "Get Google Search Results for last week": {"main": [[{"node": "Parse Google Analytics Data1", "type": "main", "index": 0}]]}, "Get Google Search Results for this week": {"main": [[{"node": "Parse Google Analytics Data", "type": "main", "index": 0}]]}, "Get Page Engagement Stats for this week": {"main": [[{"node": "Parse data from Google Analytics", "type": "main", "index": 0}]]}, "Get Page Engagement Stats for prior week": {"main": [[{"node": "Parse GA data", "type": "main", "index": 0}]]}}}