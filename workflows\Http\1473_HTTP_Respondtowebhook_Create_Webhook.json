{"nodes": [{"id": "73b64763-5e18-4ff1-bb52-ba25a08d3c3a", "name": "If params correct", "type": "n8n-nodes-base.if", "position": [500, 200], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "2e968b41-88f7-4b28-9837-af50ae130979", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "voice_id", "rightValue": ""}, {"id": "ad961bc9-6db8-4cac-8c63-30930e8beca7", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "text", "rightValue": ""}]}}, "typeVersion": 2}, {"id": "39079dec-54c5-458e-afa1-56ee5723f3a3", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [960, 180], "parameters": {"options": {}, "respondWith": "binary"}, "typeVersion": 1.1}, {"id": "b6a344f4-28ac-41a7-8e6a-a2782a5d1c68", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [300, 200], "webhookId": "5acc6769-6c0f-42a8-a69c-b05e437e18a9", "parameters": {"path": "generate-voice", "options": {}, "httpMethod": "POST", "responseMode": "responseNode"}, "typeVersion": 2}, {"id": "a25dec72-152b-4457-a18f-9cbbd31840ec", "name": "Generate voice", "type": "n8n-nodes-base.httpRequest", "position": [740, 180], "parameters": {"url": "=https://api.elevenlabs.io/v1/text-to-speech/{{ $json.body.voice_id }}", "method": "POST", "options": {}, "jsonBody": "={\n \"text\": \"{{ $json.body.text }}\"\n} ", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpCustomAuth", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "credentials": {"httpCustomAuth": {"id": "nhkU37chaiBU6X3j", "name": "Custom Auth account"}}, "typeVersion": 4.2}, {"id": "e862955e-76d9-4a24-9501-0d5eb8fbe778", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [280, -360], "parameters": {"width": 806.*************, "height": 495.**************, "content": "## Generate Text-to-Speech Using Elevenlabs via API\nThis workflow provides an API endpoint to generate speech from text using [Elevenlabs.io](https://elevenlabs.io/), a popular text-to-speech service.\n\n### Step 1: Configure Custom Credentials in n8n\nTo set up your credentials in n8n, create a new custom authentication entry with the following JSON structure:\n```json\n{\n \"headers\": {\n \"xi-api-key\": \"your-elevenlabs-api-key\"\n }\n}\n```\nReplace `\"your-elevenlabs-api-key\"` with your actual Elevenlabs API key.\n\n### Step 2: Send a POST Request to the Webhook\nSend a POST request to the workflow's webhook endpoint with these two parameters:\n- `voice_id`: The ID of the voice from Elevenlabs that you want to use.\n- `text`: The text you want to convert to speech.\n\nThis workflow has been a significant time-saver in my video production tasks. I hope it proves just as useful to you!\n\nHappy automating! \nThe n8Ninja"}, "typeVersion": 1}, {"id": "275ca523-8b43-4723-9dc4-f5dc1832fcd1", "name": "Error", "type": "n8n-nodes-base.respondToWebhook", "position": [740, 360], "parameters": {"options": {}, "respondWith": "json", "responseBody": "{\n \"error\": \"Invalid inputs.\"\n}"}, "typeVersion": 1.1}], "pinData": {}, "connections": {"Webhook": {"main": [[{"node": "If params correct", "type": "main", "index": 0}]]}, "Generate voice": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "If params correct": {"main": [[{"node": "Generate voice", "type": "main", "index": 0}], [{"node": "Error", "type": "main", "index": 0}]]}}}