{"id": "brRSLvIkYp3mLq0K", "meta": {"instanceId": "a5283507e1917a33cc3ae615b2e7d5ad2c1e50955e6f831272ddd5ab816f3fb6"}, "name": "OpenSea Marketplace Agent Tool", "tags": [], "nodes": [{"id": "13579b30-83df-4da6-b0de-90eeaf3252e7", "name": "Marketplace Agent Brain", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-300, -260], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "yUizd8t0sD5wMYVG", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "9f979fae-49c6-4a50-b96b-92de5a49ba14", "name": "Marketplace Agent Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [-120, -260], "parameters": {}, "typeVersion": 1.3}, {"id": "202ca463-f038-46df-99ea-84fbda70a933", "name": "OpenSea Marketplace Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [420, -540], "parameters": {"text": "={{ $json.message }}", "options": {"systemMessage": "### **🛒 OpenSea Marketplace Agent Overview**\nThis agent interacts with the OpenSea marketplace API to retrieve marketplace data, including NFT listings, offers, orders, and trait-specific data. The system follows strict input requirements to ensure compatibility with OpenSea API rules.\n\n---\n\n## **⚡ Available Tools & Usage Guidelines**\nThe OpenSea Marketplace Agent has access to the following marketplace-related tools:\n\n---\n\n### **1️⃣ Get All Listings (by Collection)**\n📍 **Endpoint**: `/api/v2/listings/collection/{collection_slug}/all`  \n🔹 **Description**: Retrieves all active, valid listings for a given collection.  \n🔹 **Required Parameter**:  \n   - `collection_slug` → The unique collection identifier from OpenSea.  \n🔹 **Optional Query Parameters**:  \n   - `limit` → Number of listings to return (1-100, default: 100).  \n   - `next` → Cursor for pagination.  \n🔹 **Example Query**:  \n   - _\"Retrieve all active listings for the 'boredapeyachtclub' collection.\"_  \n\n---\n\n### **2️⃣ Get All Offers (by Collection)**\n📍 **Endpoint**: `/api/v2/offers/collection/{collection_slug}/all`  \n🔹 **Description**: Retrieves all valid offers for a given NFT collection, including individual and criteria-based offers.  \n🔹 **Required Parameter**:  \n   - `collection_slug` → The unique collection identifier from OpenSea.  \n🔹 **Optional Query Parameters**:  \n   - `limit` → Number of offers to return (1-100, default: 100).  \n   - `next` → Cursor for pagination.  \n🔹 **Example Query**:  \n   - _\"Fetch all current offers for 'azuki' collection.\"_  \n\n---\n\n### **3️⃣ Get Best Listing (by NFT)**  \n📍 **Endpoint**: `/api/v2/listings/collection/{collection_slug}/nfts/{identifier}/best`  \n🔹 **Description**: Retrieves the best (cheapest) active listing for a specific NFT.  \n🔹 **Required Parameters**:  \n   - `collection_slug` → The collection identifier.  \n   - `identifier` → The NFT token ID.  \n🔹 **Optional Query Parameters**:  \n   - `include_private_listings` → Boolean (default: `false`).  \n🔹 **Example Query**:  \n   - _\"Find the lowest-priced listing for NFT #1234 in 'doodles' collection.\"_  \n\n---\n\n### **4️⃣ Get Best Listings (by Collection)**  \n📍 **Endpoint**: `/api/v2/listings/collection/{collection_slug}/best`  \n🔹 **Description**: Retrieves the lowest-priced active listings for a specific collection.  \n🔹 **Required Parameter**:  \n   - `collection_slug` → The collection identifier.  \n🔹 **Optional Query Parameters**:  \n   - `include_private_listings` → Boolean (default: `false`).  \n   - `limit` → Number of listings to return (1-100, default: 100).  \n   - `next` → Cursor for pagination.  \n🔹 **Example Query**:  \n   - _\"Get the 10 cheapest listings for 'mutantapeyachtclub'.\"_  \n\n---\n\n### **5️⃣ Get Best Offer (by NFT)**  \n📍 **Endpoint**: `/api/v2/offers/collection/{collection_slug}/nfts/{identifier}/best`  \n🔹 **Description**: Retrieves the highest offer made for a specific NFT.  \n🔹 **Required Parameters**:  \n   - `collection_slug` → The collection identifier.  \n   - `identifier` → The NFT token ID.  \n🔹 **Example Query**:  \n   - _\"Find the highest offer for NFT #5678 in 'moonbirds' collection.\"_  \n\n---\n\n### **6️⃣ Get Collection Offers**  \n📍 **Endpoint**: `/api/v2/offers/collection/{collection_slug}`  \n🔹 **Description**: Retrieves all active collection-wide offers for a specific NFT collection.  \n🔹 **Required Parameter**:  \n   - `collection_slug` → The collection identifier.  \n🔹 **Example Query**:  \n   - _\"List all collection offers for 'clonex'.\"_  \n\n---\n\n### **7️⃣ Get Item Offers**  \n📍 **Endpoint**: `/api/v2/orders/{chain}/{protocol}/offers`  \n🔹 **Description**: Retrieves all valid **individual** offers (excluding criteria-based offers).  \n🔹 **Required Parameters**:  \n   - `chain` → The blockchain network (must use an **allowed chain**, see below).  \n   - `protocol` → The token settlement protocol (only `\"seaport\"` is supported).  \n🔹 **Optional Query Parameters**:  \n   - `asset_contract_address`, `cursor`, `limit`, `listed_after`, `listed_before`, `maker`, `order_by`, `order_direction`, `payment_token_address`, `taker`, `token_ids`.  \n🔹 **Example Query**:  \n   - _\"Fetch all active item offers for NFTs on Ethereum using Seaport protocol.\"_  \n\n---\n\n### **8️⃣ Get Listings (by Chain & Protocol)**  \n📍 **Endpoint**: `/api/v2/orders/{chain}/{protocol}/listings`  \n🔹 **Description**: Retrieves all active listings filtered by blockchain and protocol.  \n🔹 **Required Parameters**:  \n   - `chain` → The blockchain network (**must be an allowed chain**).  \n   - `protocol` → `\"seaport\"` protocol.  \n🔹 **Optional Query Parameters**:  \n   - `asset_contract_address`, `cursor`, `limit`, `listed_after`, `listed_before`, `maker`, `order_by`, `order_direction`, `payment_token_address`, `taker`, `token_ids`.  \n🔹 **Example Query**:  \n   - _\"Retrieve all active listings for Ethereum Seaport orders.\"_  \n\n---\n\n### **9️⃣ Get Order (Single Order by Hash)**  \n📍 **Endpoint**: `/api/v2/orders/chain/{chain}/protocol/{protocol_address}/{order_hash}`  \n🔹 **Description**: Retrieves a specific order (offer or listing) based on its hash.  \n🔹 **Required Parameters**:  \n   - `chain` → The blockchain network (**must be an allowed chain**).  \n   - `protocol_address` → **Always set to** `******************************************`.  \n   - `order_hash` → The hash of the order.  \n🔹 **Example Query**:  \n   - _\"Fetch details for order `0x123abc...` on Ethereum.\"_  \n\n---\n\n### **🔟 Get Trait Offers**  \n📍 **Endpoint**: `/api/v2/offers/collection/{collection_slug}/traits`  \n🔹 **Description**: Retrieves all active offers made for a specific trait in a collection.  \n🔹 **Required Parameter**:  \n   - `collection_slug` → The collection identifier.  \n🔹 **Optional Query Parameters**:  \n   - `float_value`, `int_value`, `type`, `value`.  \n🔹 **Example Query**:  \n   - _\"Find all offers for 'Background: Blue' in the 'azuki' collection.\"_  \n\n---\n\n## **⚠️ Critical Notes & Restrictions**\n1. **Only Allowed Blockchains Can Be Used**  \n   - ✅ Supported Chains:  \n     - `amoy`, `ape_chain`, `ape_curtis`, `arbitrum`, `arbitrum_nova`, `arbitrum_sepolia`, `avalanche`, `avalanche_fuji`, `b3`, `b3_sepolia`, `baobab`, `base`, `base_sepolia`, `bera_chain`, `blast`, `blast_sepolia`, `ethereum`, `flow`, `flow_testnet`, `klaytn`, `matic`, `monad_testnet`, `mumbai`, `optimism`, `optimism_sepolia`, `sei_testnet`, `sepolia`, `shape`, `solana`, `soldev`, `soneium`, `soneium_minato`, `unichain`, `zora`, `zora_sepolia`.  \n   - ❌ **Incorrect Chain Inputs Will Cause Errors**  \n     - `\"polygon\"` ❌ **will fail**. Use `\"matic\"` instead.\n\n2. **Protocol Must Be `\"seaport\"` for Item & Listing Queries**  \n   - The `\"protocol\"` field must always be set to `\"seaport\"`.\n\n3. **Fixed Protocol Address for Get Order**  \n   - **For retrieving a specific order**, the `protocol_address` **must always be**:  \n     - `******************************************`.\n\n---\n\n## **✅ Example Queries**\n- _\"Fetch all best listings for Ethereum NFTs.\"_  \n- _\"Find the highest offer for a Bored Ape #456.\"_  \n- _\"Get details for a specific order hash.\"_  \n\n🚀 **Follow these rules to ensure successful API queries!**"}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "c055762a-8fe7-4141-a639-df2372f30060", "name": "Workflow Input Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-60, -540], "parameters": {"workflowInputs": {"values": [{"name": "message"}, {"name": "sessionId"}]}}, "typeVersion": 1.1}, {"id": "e25c62f0-1047-4fbb-815c-caeaa22d2fe1", "name": "OpenSea Get All Listings by Collection", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [60, -260], "parameters": {"url": "https://api.opensea.io/api/v2/listings/collection/{collection_slug}/all", "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "parametersQuery": {"values": [{"name": "limit", "valueProvider": "modelOptional"}, {"name": "next", "valueProvider": "modelOptional"}]}, "toolDescription": "This tool retrieves all active, valid listings for a single NFT collection on OpenSea, allowing pagination and limit options.", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "3v99GVMGF4tKP5nM", "name": "OpenSea"}}, "typeVersion": 1.1}, {"id": "d568d5de-82e4-4be1-b9e9-9ec56ca9d872", "name": "OpenSea Get All Offers by Collection", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [240, -260], "parameters": {"url": "https://api.opensea.io/api/v2/offers/collection/{collection_slug}/all", "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "parametersQuery": {"values": [{"name": "limit", "valueProvider": "modelOptional"}, {"name": "next", "valueProvider": "modelOptional"}]}, "toolDescription": "This tool retrieves all active, valid offers for a specified NFT collection on OpenSea, including individual and criteria offers.", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "3v99GVMGF4tKP5nM", "name": "OpenSea"}}, "typeVersion": 1.1}, {"id": "1b591b2d-787f-4519-9dfc-fc0489bc0725", "name": "OpenSea Get Best Listing by NFT", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [440, -260], "parameters": {"url": "https://api.opensea.io/api/v2/listings/collection/{collection_slug}/nfts/{identifier}/best", "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "parametersQuery": {"values": [{"name": "include_private_listings", "valueProvider": "modelOptional"}]}, "toolDescription": "This tool retrieves the best available listing for a specific NFT from OpenSea.", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "3v99GVMGF4tKP5nM", "name": "OpenSea"}}, "typeVersion": 1.1}, {"id": "33222cfb-17c7-4507-8d09-fa0a7ba1beae", "name": "OpenSea Get Best Listings by Collection", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [640, -260], "parameters": {"url": "https://api.opensea.io/api/v2/listings/collection/{collection_slug}/best", "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "parametersQuery": {"values": [{"name": "include_private_listings", "valueProvider": "modelOptional"}, {"name": "limit", "valueProvider": "modelOptional"}, {"name": "next", "valueProvider": "modelOptional"}]}, "toolDescription": "This tool retrieves the cheapest active and valid listings for a specific NFT collection on OpenSea.", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "3v99GVMGF4tKP5nM", "name": "OpenSea"}}, "typeVersion": 1.1}, {"id": "7fd0ddd6-96eb-487d-b7a2-b8fcb29b4e22", "name": "OpenSea Get Best Offer by NFT", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [860, -260], "parameters": {"url": "https://api.opensea.io/api/v2/offers/collection/{collection_slug}/nfts/{identifier}/best", "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "toolDescription": "This tool retrieves the best offers for a specific NFT on OpenSea.", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "3v99GVMGF4tKP5nM", "name": "OpenSea"}}, "typeVersion": 1.1}, {"id": "7047b8bc-ea5e-4b9b-9230-0fc46c46c58f", "name": "OpenSea Get Collection Offers", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1080, -260], "parameters": {"url": "https://api.opensea.io/api/v2/offers/collection/{collection_slug}", "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "toolDescription": "This tool retrieves the active, valid collection offers for a specified NFT collection on OpenSea.", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "3v99GVMGF4tKP5nM", "name": "OpenSea"}}, "typeVersion": 1.1}, {"id": "cab63cc4-96b4-4e14-8eb7-9fca08791040", "name": "OpenSea Get Item Offers", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1300, -260], "parameters": {"url": "https://api.opensea.io/api/v2/orders/{chain}/{protocol}/offers", "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "parametersQuery": {"values": [{"name": "asset_contract_address", "valueProvider": "modelOptional"}, {"name": "cursor", "valueProvider": "modelOptional"}, {"name": "limit", "valueProvider": "modelOptional"}, {"name": "listed_after", "valueProvider": "modelOptional"}, {"name": "listed_before", "valueProvider": "modelOptional"}, {"name": "maker", "valueProvider": "modelOptional"}, {"name": "order_by", "valueProvider": "modelOptional"}, {"name": "order_direction", "valueProvider": "modelOptional"}, {"name": "payment_token_address", "valueProvider": "modelOptional"}, {"name": "taker", "valueProvider": "modelOptional"}, {"name": "token_ids", "valueProvider": "modelOptional"}]}, "toolDescription": "This tool retrieves active, valid individual offers for NFTs on OpenSea. It does not include criteria offers.", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "3v99GVMGF4tKP5nM", "name": "OpenSea"}}, "typeVersion": 1.1}, {"id": "63760966-bbec-466d-83dc-a52b235df43a", "name": "OpenSea Get Listings", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1500, -260], "parameters": {"url": "https://api.opensea.io/api/v2/orders/{chain}/{protocol}/listings", "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "parametersQuery": {"values": [{"name": "asset_contract_address", "valueProvider": "modelOptional"}, {"name": "cursor", "valueProvider": "modelOptional"}, {"name": "limit", "valueProvider": "modelOptional"}, {"name": "listed_after", "valueProvider": "modelOptional"}, {"name": "listed_before", "valueProvider": "modelOptional"}, {"name": "maker", "valueProvider": "modelOptional"}, {"name": "order_by", "valueProvider": "modelOptional"}, {"name": "order_direction", "valueProvider": "modelOptional"}, {"name": "payment_token_address", "valueProvider": "modelOptional"}, {"name": "taker", "valueProvider": "modelOptional"}, {"name": "token_ids", "valueProvider": "modelOptional"}]}, "toolDescription": "This tool retrieves the complete set of active, valid listings for NFTs on OpenSea.", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "3v99GVMGF4tKP5nM", "name": "OpenSea"}}, "typeVersion": 1.1}, {"id": "d0365a8a-dfd4-4a86-88cf-4e8ccbdf6c36", "name": "OpenSea Get Trait Offers", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1900, -260], "parameters": {"url": "https://api.opensea.io/api/v2/offers/collection/{collection_slug}/traits", "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "parametersQuery": {"values": [{"name": "float_value", "valueProvider": "modelOptional"}, {"name": "int_value", "valueProvider": "modelOptional"}, {"name": "type", "valueProvider": "modelOptional"}, {"name": "value", "valueProvider": "modelOptional"}]}, "toolDescription": "This tool retrieves the active, valid trait offers for a specified collection on OpenSea.", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "3v99GVMGF4tKP5nM", "name": "OpenSea"}}, "typeVersion": 1.1}, {"id": "148a00a5-d8f4-4708-9afd-b1111f7d71bd", "name": "OpenSea Get Order", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1700, -260], "parameters": {"url": "https://api.opensea.io/api/v2/orders/chain/{chain}/protocol/******************************************/{order_hash}", "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "parametersQuery": {"values": [{"name": "chain"}, {"name": "order_hash"}]}, "toolDescription": "This tool retrieves a single order (offer or listing) from OpenSea using its order hash. Protocol and Chain are required to prevent hash collisions. The protocol address is always set to ******************************************.", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "3v99GVMGF4tKP5nM", "name": "OpenSea"}}, "typeVersion": 1.1}, {"id": "2b616d18-f719-42dd-a616-d91ae11be009", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-2080, -1840], "parameters": {"color": 2, "width": 1380, "height": 1860, "content": "# OpenSea Marketplace Agent Tool (n8n Workflow) Guide\n\n## 🚀 Workflow Overview\nThe **OpenSea Marketplace Agent Tool** is an **AI-driven marketplace analytics system** for **NFT trading insights**. This tool integrates with **OpenSea's API** to fetch and analyze **NFT listings, offers, orders, and trait-specific data**, helping traders and collectors make informed decisions.\n\n### 🎯 **Key Features**:\n- Retrieve **active NFT listings** for a collection.\n- Fetch **valid offers** for individual NFTs or entire collections.\n- Find the **cheapest available NFT listings** by collection or NFT.\n- Track **the highest offer** made for an NFT or collection-wide offers.\n- Access **detailed order data** based on order hash.\n- Ensure **API query compliance** to prevent errors.\n\n---\n\n## 🔗 **Nodes & Functions**\n### **1️⃣ Marketplace Agent Brain**\n- **Type**: AI Language Model (GPT-4o Mini)\n- **Purpose**: Processes marketplace-related API requests and user queries.\n\n### **2️⃣ Marketplace Agent Memory**\n- **Type**: AI Memory Buffer\n- **Purpose**: Stores session data to maintain context across multiple queries.\n\n### **3️⃣ OpenSea Get All Listings by Collection**\n- **Type**: API Request\n- **Endpoint**: `/api/v2/listings/collection/{collection_slug}/all`\n- **Function**: Retrieves all active listings for a given collection.\n\n### **4️⃣ OpenSea Get All Offers by Collection**\n- **Type**: API Request\n- **Endpoint**: `/api/v2/offers/collection/{collection_slug}/all`\n- **Function**: Fetches all active offers made for NFTs in a collection.\n\n### **5️⃣ OpenSea Get Best Listing by NFT**\n- **Type**: API Request\n- **Endpoint**: `/api/v2/listings/collection/{collection_slug}/nfts/{identifier}/best`\n- **Function**: Retrieves the **lowest-priced** active listing for a specific NFT.\n\n### **6️⃣ OpenSea Get Best Listings by Collection**\n- **Type**: API Request\n- **Endpoint**: `/api/v2/listings/collection/{collection_slug}/best`\n- **Function**: Fetches the **cheapest listings** for a given NFT collection.\n\n### **7️⃣ OpenSea Get Best Offer by NFT**\n- **Type**: API Request\n- **Endpoint**: `/api/v2/offers/collection/{collection_slug}/nfts/{identifier}/best`\n- **Function**: Retrieves the **highest offer** made for a specific NFT.\n\n### **8️⃣ OpenSea Get Collection Offers**\n- **Type**: API Request\n- **Endpoint**: `/api/v2/offers/collection/{collection_slug}`\n- **Function**: Retrieves all **active collection-wide offers**.\n\n### **9️⃣ OpenSea Get Item Offers**\n- **Type**: API Request\n- **Endpoint**: `/api/v2/orders/{chain}/{protocol}/offers`\n- **Function**: Fetches **individual active offers** (excluding criteria-based offers).\n\n### **🔟 OpenSea Get Listings by Chain & Protocol**\n- **Type**: API Request\n- **Endpoint**: `/api/v2/orders/{chain}/{protocol}/listings`\n- **Function**: Retrieves all active **listings filtered by blockchain and protocol**.\n\n### **11️⃣ OpenSea Get Order by Hash**\n- **Type**: API Request\n- **Endpoint**: `/api/v2/orders/chain/{chain}/protocol/******************************************/{order_hash}`\n- **Function**: Fetches **a specific order (listing or offer)** based on its order hash.\n\n### **12️⃣ OpenSea Get Trait Offers**\n- **Type**: API Request\n- **Endpoint**: `/api/v2/offers/collection/{collection_slug}/traits`\n- **Function**: Retrieves **active offers** for specific traits in a collection.\n\n---\n\n"}, "typeVersion": 1}, {"id": "f483a29b-626d-4c15-84a9-ac9937aea302", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-600, -1840], "parameters": {"color": 5, "width": 1500, "height": 1080, "content": "\n## 📌 **How to Use the Workflow**\n\n### ✅ **Step 1: Input Data**\n- Provide required parameters such as `collection_slug`, `identifier`, `chain`, `protocol`, or `order_hash`.\n\n### ✅ **Step 2: Execute API Calls**\n- The system processes requests and fetches NFT marketplace data.\n\n### ✅ **Step 3: Analyze & Output Results**\n- Results can be integrated into dashboards, alerts, or Telegram notifications.\n\n---\n\n## ⚠️ **Common API Queries & Examples**\n\n### **1️⃣ Get All Listings for a Collection**\n```plaintext\nGET https://api.opensea.io/api/v2/listings/collection/boredapeyachtclub/all\n```\n\n### **2️⃣ Get All Offers for a Collection**\n```plaintext\nGET https://api.opensea.io/api/v2/offers/collection/azuki/all\n```\n\n### **3️⃣ Get Best Listing for an NFT**\n```plaintext\nGET https://api.opensea.io/api/v2/listings/collection/doodles/nfts/1234/best\n```\n\n### **4️⃣ Get Best Offer for an NFT**\n```plaintext\nGET https://api.opensea.io/api/v2/offers/collection/moonbirds/nfts/5678/best\n```\n\n### **5️⃣ Get Order Details by Order Hash**\n```plaintext\nGET https://api.opensea.io/api/v2/orders/chain/ethereum/protocol/******************************************/0x123abc...\n```\n\n---\n\n"}, "typeVersion": 1}, {"id": "6c111fd9-0076-438e-8516-3a0e03e63510", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1040, -1840], "parameters": {"color": 3, "width": 1060, "height": 520, "content": "## ⚡ **Error Handling & Troubleshooting**\n| **Error Code** | **Description** |\n|--------------|----------------|\n| `200` | Success |\n| `400` | Bad Request (Invalid input) |\n| `404` | Not Found (Incorrect slug, address, or identifier) |\n| `500` | Server Error (OpenSea API issue) |\n\n### 🔹 **Fixing Common Errors**\n- Ensure correct **collection slug** and **NFT identifier**.\n- Always use `\"matic\"` instead of `\"polygon\"` for chain input.\n- Verify that the **protocol is set to `\"seaport\"`** where required.\n- **Order hash queries require the fixed protocol address:** `******************************************`.\n- Retry after some time if the OpenSea API is experiencing downtime.\n\n---\n\n## 🚀 **Connect with Me for Support**\nIf you need assistance, custom OpenSea marketplace insights, or automation support, feel free to connect with me on LinkedIn:\n\n🌐 **<PERSON> – LinkedIn**  \n🔗 [http://linkedin.com/in/donjayamahajr](http://linkedin.com/in/donjayamahajr)\n"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "f82ae6e7-43e0-4c9d-ae7e-0ddacc93a92a", "connections": {"OpenSea Get Order": {"ai_tool": [[{"node": "OpenSea Marketplace Agent", "type": "ai_tool", "index": 0}]]}, "OpenSea Get Listings": {"ai_tool": [[{"node": "OpenSea Marketplace Agent", "type": "ai_tool", "index": 0}]]}, "Workflow Input Trigger": {"main": [[{"node": "OpenSea Marketplace Agent", "type": "main", "index": 0}]]}, "Marketplace Agent Brain": {"ai_languageModel": [[{"node": "OpenSea Marketplace Agent", "type": "ai_languageModel", "index": 0}]]}, "OpenSea Get Item Offers": {"ai_tool": [[{"node": "OpenSea Marketplace Agent", "type": "ai_tool", "index": 0}]]}, "Marketplace Agent Memory": {"ai_memory": [[{"node": "OpenSea Marketplace Agent", "type": "ai_memory", "index": 0}]]}, "OpenSea Get Trait Offers": {"ai_tool": [[{"node": "OpenSea Marketplace Agent", "type": "ai_tool", "index": 0}]]}, "OpenSea Get Best Offer by NFT": {"ai_tool": [[{"node": "OpenSea Marketplace Agent", "type": "ai_tool", "index": 0}]]}, "OpenSea Get Collection Offers": {"ai_tool": [[{"node": "OpenSea Marketplace Agent", "type": "ai_tool", "index": 0}]]}, "OpenSea Get Best Listing by NFT": {"ai_tool": [[{"node": "OpenSea Marketplace Agent", "type": "ai_tool", "index": 0}]]}, "OpenSea Get All Offers by Collection": {"ai_tool": [[{"node": "OpenSea Marketplace Agent", "type": "ai_tool", "index": 0}]]}, "OpenSea Get All Listings by Collection": {"ai_tool": [[{"node": "OpenSea Marketplace Agent", "type": "ai_tool", "index": 0}]]}, "OpenSea Get Best Listings by Collection": {"ai_tool": [[{"node": "OpenSea Marketplace Agent", "type": "ai_tool", "index": 0}]]}}}