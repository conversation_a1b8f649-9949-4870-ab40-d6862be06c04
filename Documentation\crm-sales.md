# CRM & Sales - N8N Workflows

## Overview
This document catalogs the **CRM & Sales** workflows from the n8n Community Workflows repository.

**Category:** CRM & Sales  
**Total Workflows:** 29  
**Generated:** 2025-07-27  
**Source:** https://scan-might-updates-postage.trycloudflare.com/api

---

## Workflows

### Manual Copper Automate Triggered
**Filename:** `0011_Manual_Copper_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Copper for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Copper,  

---

### Manual Copper Automate Triggered
**Filename:** `0012_Manual_Copper_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Copper for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Copper,  

---

### Create a new member, update the information of the member, create a note and a post for the member in Orbit
**Filename:** `0029_Manual_Orbit_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Orbit to create new records. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Orbit,  

---

### Create an deal in Pipedrive
**Filename:** `0062_Manual_Pipedrive_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Pipedrive to create new records. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Pipedrive,  

---

### Receive updates for all changes in Pipedrive
**Filename:** `0071_Pipedrive_Update_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Pipedrive to update existing data. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Pipedrive,  

---

### Zohocrm Trello Create Triggered
**Filename:** `0086_Zohocrm_Trello_Create_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Shopify, Trello, and Mailchimp to create new records. Uses 9 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (9 nodes)  
**Integrations:** Shopify,Trello,Mailchimp,Gmail,Harvest,Zohocrm,  

---

### Create a company in Salesmate
**Filename:** `0114_Manual_Salesmate_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Salesmate to create new records. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Salesmate,  

---

### Hubspot Clearbit Update Triggered
**Filename:** `0115_HubSpot_Clearbit_Update_Triggered.json`  
**Description:** Webhook-triggered automation that connects Hubspot and Clearbit to update existing data. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Hubspot,Clearbit,  

---

### Hubspot Cron Update Scheduled
**Filename:** `0129_HubSpot_Cron_Update_Scheduled.json`  
**Description:** Scheduled automation that connects Hubspot and Pipedrive to update existing data. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (7 nodes)  
**Integrations:** Hubspot,Pipedrive,  

---

### Hubspot Cron Automate Scheduled
**Filename:** `0130_HubSpot_Cron_Automate_Scheduled.json`  
**Description:** Scheduled automation that connects Hubspot and Pipedrive for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (5 nodes)  
**Integrations:** Hubspot,Pipedrive,  

---

### Hubspot Mailchimp Create Scheduled
**Filename:** `0243_HubSpot_Mailchimp_Create_Scheduled.json`  
**Description:** Scheduled automation that connects Hubspot and Mailchimp to create new records. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (3 nodes)  
**Integrations:** Hubspot,Mailchimp,  

---

### Hubspot Mailchimp Create Scheduled
**Filename:** `0244_HubSpot_Mailchimp_Create_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Hubspot, Mailchimp, and Functionitem to create new records. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (5 nodes)  
**Integrations:** Hubspot,Mailchimp,Functionitem,  

---

### Pipedrive Stickynote Create Webhook
**Filename:** `0249_Pipedrive_Stickynote_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Pipedrive, and Itemlists to create new records. Uses 11 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (11 nodes)  
**Integrations:** Httprequest,Pipedrive,Itemlists,  

---

### Pipedrive Spreadsheetfile Create Triggered
**Filename:** `0251_Pipedrive_Spreadsheetfile_Create_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Spreadsheetfile, Google Drive, and Pipedrive to create new records. Uses 12 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (12 nodes)  
**Integrations:** Spreadsheetfile,Google Drive,Pipedrive,  

---

### Code Pipedrive Create Triggered
**Filename:** `0379_Code_Pipedrive_Create_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Box, OpenAI, and Pipedrive to create new records. Uses 11 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (11 nodes)  
**Integrations:** Box,OpenAI,Pipedrive,  

---

### Create, update and get a contact in Google Contacts
**Filename:** `0409_Manual_Googlecontacts_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Googlecontacts to create new records. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Googlecontacts,  

---

### Noop Hubspot Create Webhook
**Filename:** `0416_Noop_HubSpot_Create_Webhook.json`  
**Description:** Webhook-triggered automation that connects Httprequest and Hubspot to create new records. Uses 12 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (12 nodes)  
**Integrations:** Httprequest,Hubspot,  

---

### Hubspot Splitout Create Webhook
**Filename:** `0920_HubSpot_Splitout_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Form Trigger, Hubspot, and OpenAI to create new records. Uses 31 nodes and integrates with 12 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (31 nodes)  
**Integrations:** Form Trigger,Hubspot,OpenAI,Webhook,Splitout,Agent,Gmail,Outputparserstructured,Httprequest,Googlecalendartool,Executeworkflow,Cal.com,  

---

### Copper Automate Triggered
**Filename:** `1006_Copper_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Copper for data processing. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Copper,  

---

### Manual Zohocrm Automate Triggered
**Filename:** `1021_Manual_Zohocrm_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Zohocrm for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Zohocrm,  

---

### Manual Keap Automate Triggered
**Filename:** `1022_Manual_Keap_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Keap for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Keap,  

---

### Keap Automate Triggered
**Filename:** `1023_Keap_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Keap for data processing. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Keap,  

---

### Hubspot Automate Triggered
**Filename:** `1081_HubSpot_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Hubspot for data processing. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Hubspot,  

---

### Receive updates when a new list is created in Affinity
**Filename:** `1085_Affinity_Create_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Affinity to create new records. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Affinity,  

---

### Manual Salesforce Automate Triggered
**Filename:** `1094_Manual_Salesforce_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Salesforce for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Salesforce,  

---

### 6
**Filename:** `1136_Manual_HubSpot_Automation_Triggered.json`  
**Description:** Manual workflow that integrates with Hubspot for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Hubspot,  

---

### Create an organization in Affinity
**Filename:** `1210_Manual_Affinity_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Affinity to create new records. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Affinity,  

---

### Send Daily Birthday Reminders from Google Contacts to Slack
**Filename:** `1239_Googlecontacts_Schedule_Send_Scheduled.json`  
**Description:** Scheduled automation that connects Googlecontacts and Slack for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (7 nodes)  
**Integrations:** Googlecontacts,Slack,  

---

### Code Pipedrive Automation Triggered
**Filename:** `1619_Code_Pipedrive_Automation_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Box, OpenAI, and Pipedrive for data processing. Uses 11 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (11 nodes)  
**Integrations:** Box,OpenAI,Pipedrive,  

---


## Summary

**Total CRM & Sales workflows:** 29  
**Documentation generated:** 2025-07-27 14:31:54  
**API Source:** https://scan-might-updates-postage.trycloudflare.com/api  

This documentation was automatically generated using the n8n workflow API endpoints.
