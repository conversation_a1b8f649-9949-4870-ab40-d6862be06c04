{"id": 113, "name": "Standup <PERSON><PERSON> - Override Config", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [240, 300], "parameters": {}, "typeVersion": 1}, {"name": "Write Binary File", "type": "n8n-nodes-base.writeBinaryFile", "position": [600, 300], "parameters": {"fileName": "/home/<USER>/.n8n/standup-bot-config.json"}, "typeVersion": 1}, {"name": "Move Binary Data", "type": "n8n-nodes-base.moveBinaryData", "position": [420, 300], "parameters": {"mode": "jsonToBinary", "options": {"encoding": "utf8", "fileName": "standup-bot-config.json"}}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Move Binary Data": {"main": [[{"node": "Write Binary File", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Move Binary Data", "type": "main", "index": 0}]]}}}