{"meta": {"instanceId": "1dd912a1610cd0376bae7bb8f1b5838d2b601f42ac66a48e012166bb954fed5a", "templateId": "2310"}, "nodes": [{"id": "df9d04c7-2116-421a-9061-f3ae9118817a", "name": "Convert web page to PDF", "type": "n8n-nodes-base.httpRequest", "position": [560, 240], "parameters": {"url": "https://v2.convertapi.com/convert/web/to/pdf", "method": "POST", "options": {"response": {"response": {"responseFormat": "file"}}}, "sendBody": true, "contentType": "multipart-form-data", "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "url", "value": "https://n8n.io"}]}, "genericAuthType": "httpQueryAuth", "headerParameters": {"parameters": [{"name": "Accept", "value": "application/octet-stream"}]}}, "credentials": {"httpQueryAuth": {"id": "WdAklDMod8fBEMRk", "name": "Query Auth account"}}, "typeVersion": 4.2}, {"id": "2f559bbd-54ca-40db-bb7c-3a00481a017d", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [380, 240], "parameters": {}, "typeVersion": 1}, {"id": "d265d2b7-0079-4db8-a208-88bbeb965475", "name": "Read/Write Files from Disk", "type": "n8n-nodes-base.readWriteFile", "position": [960, 240], "parameters": {"options": {}, "fileName": "document.pdf", "operation": "write", "dataPropertyName": "=data"}, "typeVersion": 1}, {"id": "6e17fb0d-cc52-4e33-b0e0-7256cdef1240", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [520, 80], "parameters": {"width": 218, "height": 132, "content": "## Authentication\nConversion requests must be authenticated. Please create \n[ConvertAPI account to get authentication secret](https://www.convertapi.com/a/signin)"}, "typeVersion": 1}, {"id": "13d9a34a-7516-4fb2-9e5b-62cc8f5259ac", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [500, 420], "parameters": {"width": 281, "content": "## Set Url to Webpage\nSet the url to the webpage, that should be converted to pdf in the parameter `url`"}, "typeVersion": 1}], "pinData": {}, "connections": {"Convert web page to PDF": {"main": [[{"node": "Read/Write Files from Disk", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Convert web page to PDF", "type": "main", "index": 0}]]}}}