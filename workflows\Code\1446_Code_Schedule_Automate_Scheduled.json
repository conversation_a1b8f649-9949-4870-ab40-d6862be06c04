{"id": "Eyh4jc7RK7rCTh4z", "meta": {"instanceId": "38fb1860cc6284b8af9ba3b485f32cc1851cd97470ef1b4a472b5e707f1c93b5", "templateCredsSetupCompleted": true}, "name": "My workflow 2", "tags": [], "nodes": [{"id": "084bcc9e-9d05-4b69-8cb1-eccdcb67358e", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [500, 720], "parameters": {}, "typeVersion": 1}, {"id": "f593e3f1-adea-4ef7-9779-4f2436fe7774", "name": "XML", "type": "n8n-nodes-base.xml", "position": [1540, 880], "parameters": {"options": {"normalize": false, "explicitArray": false}}, "executeOnce": true, "typeVersion": 1}, {"id": "5906371f-d5da-4141-876f-542cb5d0d1a8", "name": "GoogleTrends", "type": "n8n-nodes-base.httpRequest", "position": [1280, 880], "parameters": {"url": "https://trends.google.it/trending/rss?geo=IT", "options": {}}, "executeOnce": true, "retryOnFail": true, "typeVersion": 4.2}, {"id": "7badc1ad-48c2-4142-88bb-fa3f442abd66", "name": "CONFIG", "type": "n8n-nodes-base.set", "position": [760, 880], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "25d7e553-9678-40ad-bb69-e4eb4bce4d11", "name": "min_traffic", "type": "number", "value": 500}, {"id": "decd0a3d-ddc5-45c3-a56f-ee1f14705019", "name": "max_results", "type": "number", "value": 3}, {"id": "12cdd78a-45a7-499e-8fe5-0ab6a7da8a10", "name": "jina_key", "type": "string", "value": ""}]}}, "typeVersion": 3.4}, {"id": "b92ad672-ea1d-4b5b-ae1d-0aa883c5db9a", "name": "Get saved keywords", "type": "n8n-nodes-base.googleSheets", "position": [1020, 880], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "", "cachedResultName": ""}, "documentId": {"__rl": true, "mode": "list", "value": "", "cachedResultUrl": "", "cachedResultName": ""}}, "credentials": {"googleSheetsOAuth2Api": {"id": "0HENZXUy9PlxLx0O", "name": "Google Sheets account"}}, "executeOnce": true, "typeVersion": 4.5, "alwaysOutputData": false}, {"id": "e5639494-d757-442f-942f-75927ecadd86", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [740, 1380], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "2c81d605-6749-47a2-95ba-846d86388c04", "name": "Mapping", "type": "n8n-nodes-base.set", "position": [1000, 1380], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "7230decf-45d9-4006-b005-614fb1dede10", "name": "summary", "type": "string", "value": "={{ $('content1').item.json.data.text.replaceAll('\\n', ' ').trim() }}\n---\n{{ $('content2').item.json.data.text.replaceAll('\\n', ' ').trim() }}\n---\n{{ $('content3').item.json.data.text.replaceAll('\\n', ' ').trim() }}"}, {"id": "ad8f7dcd-fc93-41f3-b643-db4a2b569119", "name": "trending_keyword", "type": "string", "value": "={{ $('New keywords').item.json.trending_keyword }}"}, {"id": "a3838385-90e2-4308-b147-5ef6de4a2c19", "name": "approx_traffic", "type": "number", "value": "={{ $('New keywords').item.json.approx_traffic }}"}, {"id": "fc8523d5-a68d-443b-ad49-9057dee85617", "name": "pubDate", "type": "string", "value": "={{ $('New keywords').item.json.pubDate }}"}, {"id": "139fd57f-8ccc-453b-9f8f-94c9546bbd1c", "name": "status", "type": "string", "value": "idea"}, {"id": "39fa6799-78db-453e-ad29-359ab441e912", "name": "news_item_url1", "type": "string", "value": "={{ $('New keywords').item.json.news_item_url1 }}"}, {"id": "1e6e7545-526a-4003-ac92-520fa04cfe1d", "name": "news_item_title1", "type": "string", "value": "={{ $('New keywords').item.json.news_item_title1 }}"}, {"id": "12c019fc-2fe6-41e8-a8b8-e38bdfa16215", "name": "news_item_title2", "type": "string", "value": "={{ $('New keywords').item.json.news_item_title2 }}"}, {"id": "b14b5835-66b7-448c-b9a5-d9f85d9f7f12", "name": "news_item_url2", "type": "string", "value": "={{ $('New keywords').item.json.news_item_url2 }}"}, {"id": "4df8d3e0-7c8d-40e1-8ed7-b1743a8bbf17", "name": "news_item_title3", "type": "string", "value": "={{ $('New keywords').item.json.news_item_title3 }}"}, {"id": "7fe45e6d-1978-49b4-b289-c33e3d68f71a", "name": "news_item_url3", "type": "string", "value": "={{ $('New keywords').item.json.news_item_url3 }}"}, {"id": "ef39509c-c4e7-49b1-9ee8-ad82a8af9514", "name": "news_item_picture1", "type": "string", "value": "={{ $('New keywords').item.json.news_item_picture1 }}"}, {"id": "a2210ea6-8ee5-408a-9ba1-5e07bd4d7f1b", "name": "news_item_source1", "type": "string", "value": "={{ $('New keywords').item.json.news_item_source1 }}"}, {"id": "b6136672-4c09-4da0-ba5b-d9026877ca1e", "name": "news_item_picture2", "type": "string", "value": "={{ $('New keywords').item.json.news_item_picture2 }}"}, {"id": "f9a54dca-079c-4431-af34-6bb98a6d8711", "name": "news_item_source2", "type": "string", "value": "={{ $('New keywords').item.json.news_item_source2 }}"}, {"id": "aa38fecd-3743-447f-aa54-a1a86b5ad717", "name": "news_item_picture3", "type": "string", "value": "={{ $('New keywords').item.json.news_item_picture3 }}"}, {"id": "2ff53574-9f9d-4e35-afbe-161e77a58515", "name": "news_item_source3", "type": "string", "value": "={{ $('New keywords').item.json.news_item_source3 }}"}]}}, "typeVersion": 3.4}, {"id": "5ca98d8f-0bc6-4b77-a367-81ed2509deba", "name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [1580, 1180], "parameters": {"columns": {"value": {"status": "idea", "pubDate": "={{ $json.pubDate }}", "abstract": "={{ $json.abstract.replaceAll('  ', '').substring(0, 49999) }}", "approx_traffic": "={{ $json.approx_traffic }}", "news_item_url1": "={{ $json.news_item_url1 }}", "news_item_url2": "={{ $json.news_item_url2 }}", "news_item_url3": "={{ $json.news_item_url3 }}", "news_item_title1": "={{ $json.news_item_title1 }}", "news_item_title2": "={{ $json.news_item_title2 }}", "news_item_title3": "={{ $json.news_item_title3 }}", "trending_keyword": "={{ $json.trending_keyword }}", "news_item_source1": "={{ $json.news_item_source1 }}", "news_item_source2": "={{ $json.news_item_source2 }}", "news_item_source3": "={{ $json.news_item_source3 }}", "news_item_picture1": "={{ $json.news_item_picture1 }}", "news_item_picture2": "={{ $json.news_item_picture2 }}", "news_item_picture3": "={{ $json.news_item_picture3 }}"}, "schema": [{"id": "status", "type": "string", "display": true, "required": false, "displayName": "status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "trending_keyword", "type": "string", "display": true, "required": false, "displayName": "trending_keyword", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "approx_traffic", "type": "string", "display": true, "required": false, "displayName": "approx_traffic", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "abstract", "type": "string", "display": true, "required": false, "displayName": "abstract", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "pubDate", "type": "string", "display": true, "required": false, "displayName": "pubDate", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "news_item_url1", "type": "string", "display": true, "required": false, "displayName": "news_item_url1", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "news_item_title1", "type": "string", "display": true, "required": false, "displayName": "news_item_title1", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "news_item_picture1", "type": "string", "display": true, "required": false, "displayName": "news_item_picture1", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "news_item_source1", "type": "string", "display": true, "required": false, "displayName": "news_item_source1", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "news_item_url2", "type": "string", "display": true, "required": false, "displayName": "news_item_url2", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "news_item_title2", "type": "string", "display": true, "required": false, "displayName": "news_item_title2", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "news_item_picture2", "type": "string", "display": true, "required": false, "displayName": "news_item_picture2", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "news_item_source2", "type": "string", "display": true, "required": false, "displayName": "news_item_source2", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "news_item_url3", "type": "string", "display": true, "required": false, "displayName": "news_item_url3", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "news_item_title3", "type": "string", "display": true, "required": false, "displayName": "news_item_title3", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "news_item_picture3", "type": "string", "display": true, "required": false, "displayName": "news_item_picture3", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "news_item_source3", "type": "string", "display": true, "required": false, "displayName": "news_item_source3", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "", "cachedResultName": ""}, "documentId": {"__rl": true, "mode": "list", "value": "", "cachedResultUrl": "", "cachedResultName": ""}}, "typeVersion": 4.5}, {"id": "41fcb412-ea5d-4adc-8d40-d72398537150", "name": "New keywords", "type": "n8n-nodes-base.code", "position": [1780, 880], "parameters": {"jsCode": "const max_results = $('CONFIG').first().json.max_results;\nconst min_traffic = $('CONFIG').first().json.min_traffic;\n\nconst gsheet = $(\"Get saved keywords\").all();\nconst gsheetKeys = gsheet.map(record => record.json.trending_keyword);\n\nconst items = $('XML').first().json.rss.channel.item;\nconst trafficKey = Object.keys(items[0]).find(key => key.includes(\"approx_traffic\"));\nconst parseTraffic = (traffic) => parseInt(traffic.replace('+', ''), 10);\n\nconst newItems = items.map(item => {\n    const links = Array.isArray(item[\"ht:news_item\"]) ? item[\"ht:news_item\"].slice(0, 3) : [];\n\n    const flattenedLinks = links.reduce((acc, news, index) => {\n        acc[`news_item_url${index + 1}`] = news[\"ht:news_item_url\"];\n        acc[`news_item_title${index + 1}`] = news[\"ht:news_item_title\"];\n        acc[`news_item_picture${index + 1}`] = news[\"ht:news_item_picture\"];\n        acc[`news_item_source${index + 1}`] = news[\"ht:news_item_source\"];\n        return acc;\n    }, {});\n\n    return {\n        trending_keyword: item.title,\n        approx_traffic: parseTraffic(item[trafficKey]),\n        pubDate: item.pubDate,\n        ...flattenedLinks, // Aggiungi i link\n    };\n}).filter(item => \n    item.approx_traffic >= min_traffic && \n    !gsheetKeys.includes(item.trending_keyword) // Filtra quelli già presenti in Google Sheets\n);\n\nlet sortedItems = newItems.sort((a, b) => b.approx_traffic - a.approx_traffic);\nif (max_results > 0) {\n    sortedItems = sortedItems.slice(0, max_results);\n}\n\nreturn sortedItems;\n"}, "typeVersion": 2, "alwaysOutputData": false}, {"id": "56a953da-15a7-48da-a299-c53a7947c45e", "name": "content1", "type": "n8n-nodes-base.httpRequest", "position": [1020, 1700], "parameters": {"url": "=https://r.jina.ai/{{ $('New keywords').item.json.news_item_url1 }}", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{ $('CONFIG').item.json.jina_key }}"}, {"name": "Accept", "value": "application/json"}, {"name": "X-Remove-Selector", "value": "a, link, script, footer, img, svg"}, {"name": "X-Retain-Images", "value": "none"}, {"name": "X-Return-Format", "value": "text"}]}}, "typeVersion": 4.2}, {"id": "e3dbb73f-eac8-47aa-b621-0775dd09c5bf", "name": "content2", "type": "n8n-nodes-base.httpRequest", "position": [1280, 1700], "parameters": {"url": "=https://r.jina.ai/{{ $('New keywords').item.json.news_item_url2 }}", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{ $('CONFIG').item.json.jina_key }}"}, {"name": "Accept", "value": "application/json"}, {"name": "X-Remove-Selector", "value": "a, link, script, footer, img, svg"}, {"name": "X-Retain-Images", "value": "none"}, {"name": "X-Return-Format", "value": "text"}]}}, "typeVersion": 4.2}, {"id": "0723267a-5e4e-40e2-87bf-4c215c79b66c", "name": "content3", "type": "n8n-nodes-base.httpRequest", "position": [1560, 1700], "parameters": {"url": "=https://r.jina.ai/{{ $('New keywords').item.json.news_item_url3 }}", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{ $('CONFIG').item.json.jina_key }}"}, {"name": "Accept", "value": "application/json"}, {"name": "X-Remove-Selector", "value": "a, link, script, footer, img, svg"}, {"name": "X-Retain-Images", "value": "none"}, {"name": "X-Return-Format", "value": "text"}]}}, "typeVersion": 4.2}, {"id": "8621b782-a182-479a-afa1-de0b525d3909", "name": "Start every hour past 11 minutes", "type": "n8n-nodes-base.scheduleTrigger", "position": [500, 880], "parameters": {"rule": {"interval": [{"field": "cronExpression", "expression": "11 */1 * * *"}]}}, "typeVersion": 1.2}, {"id": "d04e89cd-a578-45d9-88f2-be4c72407049", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [420, 560], "parameters": {"color": 5, "height": 1300, "content": "## <PERSON><PERSON> trigger\nGoogle Trends update the RSS feed every 10 minutes. This will start wordflow 1 minute after. "}, "typeVersion": 1}, {"id": "4644b6ca-43da-42ab-870c-eeb52610208c", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [680, 560], "parameters": {"color": 3, "height": 480, "content": "## CONFIGURATION\nmin_traffic is a numeric value. Google Trend RSS has an approx traffic value 100, 200, 500, 1000 etc.\n\nmax_result is a numeric value used to limit max rss to scrape\n\njina_key is the jina.ai API key"}, "typeVersion": 1}, {"id": "b4ac3f6a-dc72-4e3e-9fa1-80298a66ddf9", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [940, 560], "parameters": {"height": 480, "content": "## Google Sheet Database\nThis is main sheet where all your Editorial plan will be saved.\n\nThe column status value could be a trigger for other automations"}, "typeVersion": 1}, {"id": "09fa9ed7-2557-46ae-857f-e251bf25b10e", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1200, 560], "parameters": {"height": 480, "content": "## Google Trends request\n\nWe get last kwyword in trend. Every item has a main keyword and 3 URL. We will use those url to scrape content and generate a combined summary"}, "typeVersion": 1}, {"id": "6d3d6f90-4eae-4239-b8cd-bfdb40bf01e9", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1460, 560], "parameters": {"height": 480, "content": "## Simple conversion\n\nConverts XML RSS into a more readable json object"}, "typeVersion": 1}, {"id": "3b5dd19f-ac2d-426b-8853-1af3819e10f6", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1720, 560], "parameters": {"height": 480, "content": "## Building dataset\n\nHere we limits results, filter by mmin traffic and we flat the RSS structure to adapt to Google Sheet, fields are renamed as per description.\n\nThen RSS result and Google Sheet is compared, if a new keyword is found we have result. If RSS give a keyword already srtored, this node doesn't give any output."}, "typeVersion": 1}, {"id": "cce50db5-6566-439c-9b90-3f8720411613", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [940, 1080], "parameters": {"height": 460, "content": "## Data mapping\n\nHere you have all fields needed in Google Sheet.\n\nWhile done, the content of 3 website linked in Google Trends RSS will be merged in a single Summary field"}, "typeVersion": 1}, {"id": "d57a8c16-21c6-4388-bb91-093428061ac5", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [1200, 1080], "parameters": {"color": 3, "height": 460, "content": "## Data check\n\nSometimes scraping HTML content fails (for some reasons), that's normal but this should avoid to save a zero content if all 3 scraping nodes will fail"}, "typeVersion": 1}, {"id": "2bcbf0a9-423d-45eb-a444-ab2140db2db6", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [680, 1080], "parameters": {"height": 460, "content": "## Data mapping\n\nHere you have all fields needed in Google Sheet.\n\nWhile done, the content of 3 website linked in Google Trends RSS will be merged in a single Summary field"}, "typeVersion": 1}, {"id": "692b342c-c252-48c6-ad11-b58906aa62e2", "name": "If we have scraped min 1 url -> Save", "type": "n8n-nodes-base.if", "position": [1280, 1380], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "42b15ebc-f2f7-4dc0-957f-b04d1bdacb41", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.summary.length > 100 }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "f2f94f0b-dcb4-4b52-86f8-c92aa2fc3d88", "name": "All scraping node failed. Don't save record without summary", "type": "n8n-nodes-base.noOp", "position": [1580, 1380], "parameters": {}, "typeVersion": 1}, {"id": "d3f42eb0-5e37-40ff-a476-46b6384f2647", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [680, 1560], "parameters": {"color": 7, "width": 1280, "height": 300, "content": "## Scraping\n\nHere jina.ai will get text content from 3 Google Trends URLs"}, "typeVersion": 1}, {"id": "8178a70e-f3d2-4157-8f4b-9adaf8932e8e", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [1460, 1080], "parameters": {"color": 4, "width": 500, "height": 460, "content": "## Saving output\n\n"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "81aec91d-f995-4a14-b801-ef44070e7153", "connections": {"XML": {"main": [[{"node": "New keywords", "type": "main", "index": 0}]]}, "CONFIG": {"main": [[{"node": "Get saved keywords", "type": "main", "index": 0}]]}, "Mapping": {"main": [[{"node": "If we have scraped min 1 url -> Save", "type": "main", "index": 0}]]}, "content1": {"main": [[{"node": "content2", "type": "main", "index": 0}]]}, "content2": {"main": [[{"node": "content3", "type": "main", "index": 0}]]}, "content3": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "GoogleTrends": {"main": [[{"node": "XML", "type": "main", "index": 0}]]}, "New keywords": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Mapping", "type": "main", "index": 0}], [{"node": "content1", "type": "main", "index": 0}]]}, "Get saved keywords": {"main": [[{"node": "GoogleTrends", "type": "main", "index": 0}]]}, "Start every hour past 11 minutes": {"main": [[{"node": "CONFIG", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "CONFIG", "type": "main", "index": 0}]]}, "If we have scraped min 1 url -> Save": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}], [{"node": "All scraping node failed. Don't save record without summary", "type": "main", "index": 0}]]}}}