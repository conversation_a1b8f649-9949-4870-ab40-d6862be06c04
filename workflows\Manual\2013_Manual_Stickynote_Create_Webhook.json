{"id": "xQ0xqhNzFeEdBpFK", "meta": {"instanceId": "1e003a7ea4715b6b35e9947791386a7d07edf3b5bf8d4c9b7ee4fdcbec0447d7"}, "name": "Generate 360° Virtual Try-on Videos for Clothing with Kling API", "tags": [], "nodes": [{"id": "978b4ac4-0357-4d2b-8a02-7da04e6f3f1f", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [160, 140], "parameters": {}, "typeVersion": 1}, {"id": "54d1c23f-3a13-4ec0-9b3b-3806e5faae18", "name": "<PERSON><PERSON> Virtual Try-On Task", "type": "n8n-nodes-base.httpRequest", "position": [620, 140], "parameters": {"url": "https://api.piapi.ai/api/v1/task", "method": "POST", "options": {}, "jsonBody": "={\n  \"model\": \"kling\",\n  \"task_type\": \"ai_try_on\",\n  \"input\": {\n    \"model_input\": \"{{ $json.model_input }}\",\n    \"dress_input\": \"{{ $json.dress_input }}\",\n    \"upper_input\": \"{{ $json.upper_input }}\",\n    \"lower_input\": \"{{ $json.lower_input }}\",\n    \"batch_size\": 1\n  }\n} ", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "x-api-key", "value": "={{ $json['x-api-key'] }}"}]}}, "typeVersion": 4.2}, {"id": "5be9d932-c102-4a7e-b995-09c6bf17026c", "name": "Switch", "type": "n8n-nodes-base.switch", "position": [960, 200], "parameters": {"rules": {"values": [{"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "5f61ee56-4ebe-411f-95e6-b47d9741e7a2", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "completed"}]}}]}, "options": {}}, "typeVersion": 3.2}, {"id": "cdda4f40-1580-4a5a-a7f4-f1e4fbf7ceb4", "name": "Get Kling Video Task", "type": "n8n-nodes-base.httpRequest", "position": [1180, 440], "parameters": {"url": "=https://api.piapi.ai/api/v1/task/{{ $json.data.task_id }}", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-api-key", "value": "={{ $('Preset Parameters').item.json['x-api-key'] }}"}]}}, "typeVersion": 4.2}, {"id": "3e794d14-b55f-4936-90af-8237977d6635", "name": "Generate kling video", "type": "n8n-nodes-base.httpRequest", "position": [1140, 200], "parameters": {"url": "https://api.piapi.ai/api/v1/task", "method": "POST", "options": {}, "jsonBody": "={\n    \"model\": \"kling\",\n    \"task_type\": \"video_generation\",\n    \"input\": {\n        \"version\": \"1.6\",\n        \"image_url\": \"{{ $json.data.output.works[0].image.resource }}\",\n        \"prompt\": \"{{ $('Preset Parameters').item.json.generate_video_prompt }}\"\n    }\n} ", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "x-api-key", "value": "={{ $('Preset Parameters').item.json['x-api-key'] }}"}]}}, "typeVersion": 4.2}, {"id": "3ae849b2-4bd4-454f-a759-e44a9736100d", "name": "Preset Parameters", "type": "n8n-nodes-base.set", "position": [380, 140], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "{\n  \"x-api-key\":\"\",\n  \"model_input\": \"\",\n  \"dress_input\": \"\",\n  \"upper_input\":\"\",\n  \"lower_input\":\"\",\n  \"generate_video_prompt\": \"Walk on the catwalk, turn around, and finally stand still and pose\"\n}\n"}, "typeVersion": 3.4}, {"id": "18c606e3-82e2-4c09-a87e-6bbc71363c1c", "name": "Get Kling Virtual Try-On Task", "type": "n8n-nodes-base.httpRequest", "position": [420, 460], "parameters": {"url": "=https://api.piapi.ai/api/v1/task/{{ $json.data.task_id }}", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-api-key", "value": "={{ $('Preset Parameters').item.json['x-api-key'] }}"}]}}, "typeVersion": 4.2}, {"id": "becf3d7b-d468-4b4a-b22f-d6d747e52664", "name": "Check Data Status", "type": "n8n-nodes-base.if", "position": [640, 460], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "or", "conditions": [{"id": "e97a02cc-8d1d-4500-bce5-0a296c792b76", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "completed"}, {"id": "50b63a7a-52b5-4766-a859-96ac1ff949ec", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "failed"}]}}, "typeVersion": 2.2}, {"id": "d8ec251d-d47c-4341-909d-abdea385c1f9", "name": "Wait for Image Generation", "type": "n8n-nodes-base.wait", "position": [160, 460], "webhookId": "af79053d-1291-4dd2-889e-4593dbbb2512", "parameters": {}, "typeVersion": 1.1}, {"id": "88e3067f-0b1f-472a-937b-926c6d208453", "name": "Wait for Video Generation", "type": "n8n-nodes-base.wait", "position": [920, 440], "webhookId": "af79053d-1291-4dd2-889e-4593dbbb2512", "parameters": {}, "typeVersion": 1.1}, {"id": "36d75678-918f-42c5-97a7-7a13d1eacbd4", "name": "Check Video Data Status", "type": "n8n-nodes-base.switch", "position": [1560, 180], "parameters": {"rules": {"values": [{"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "5f61ee56-4ebe-411f-95e6-b47d9741e7a2", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "completed"}]}}]}, "options": {}}, "typeVersion": 3.2}, {"id": "7356d963-83c0-47a1-a728-9191f66d2f57", "name": "Get Video Data Status", "type": "n8n-nodes-base.if", "position": [1400, 440], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "or", "conditions": [{"id": "e97a02cc-8d1d-4500-bce5-0a296c792b76", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "completed"}, {"id": "50b63a7a-52b5-4766-a859-96ac1ff949ec", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "failed"}]}}, "typeVersion": 2.2}, {"id": "9ef52637-ccc9-4817-8c14-5c54fa0af178", "name": "Get Final Video URL", "type": "n8n-nodes-base.set", "position": [1760, 180], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "={\n  \"video_url\": \"{{ $json.data.output.video_url }}\"\n}\n "}, "typeVersion": 3.4}, {"id": "9a0194bd-59a5-45b1-a6e2-db0605eb4d7a", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [140, -120], "parameters": {"width": 460, "height": 220, "content": "## Generate 360° Virtual Try-on Videos for Clothing with Kling API (unofficial)\nThis tool is designed for e-commerce platforms, fashion brands, content creators, and content influencers. By uploading model and clothing images and linking your PiAPI account, you can swiftly generate a realistic video of the model sporting the outfit with a 360° turn, offering an immersive viewing experience."}, "typeVersion": 1}, {"id": "629697ae-cd49-4e8e-953d-a2f091ed9202", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [120, 700], "parameters": {"width": 340, "height": 200, "content": "## Generate Virtual Try-on Image\nUpload model url, users have two solutions to upload clothing url: \n1. Upload `dress_input`\n2. Upload 'upper_input` and 'lower_input`"}, "typeVersion": 1}, {"id": "710bd0f0-8b5a-469a-8b31-b6f738dc7f79", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1640, 460], "parameters": {"width": 340, "content": "## Generate Final Video \nWait for generation and get the output url in the final node."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "97ee31dd-b8be-4b37-bbed-363ac35d5268", "connections": {"Switch": {"main": [[{"node": "Generate kling video", "type": "main", "index": 0}]]}, "Check Data Status": {"main": [[{"node": "Switch", "type": "main", "index": 0}], [{"node": "Wait for Image Generation", "type": "main", "index": 0}]]}, "Preset Parameters": {"main": [[{"node": "<PERSON><PERSON> Virtual Try-On Task", "type": "main", "index": 0}]]}, "Generate kling video": {"main": [[{"node": "Wait for Video Generation", "type": "main", "index": 0}]]}, "Get Kling Video Task": {"main": [[{"node": "Get Video Data Status", "type": "main", "index": 0}]]}, "Get Video Data Status": {"main": [[{"node": "Check Video Data Status", "type": "main", "index": 0}], [{"node": "Wait for Video Generation", "type": "main", "index": 0}]]}, "Check Video Data Status": {"main": [[{"node": "Get Final Video URL", "type": "main", "index": 0}]]}, "Kling Virtual Try-On Task": {"main": [[{"node": "Wait for Image Generation", "type": "main", "index": 0}]]}, "Wait for Image Generation": {"main": [[{"node": "Get Kling Virtual Try-On Task", "type": "main", "index": 0}]]}, "Wait for Video Generation": {"main": [[{"node": "Get Kling Video Task", "type": "main", "index": 0}]]}, "Get Kling Virtual Try-On Task": {"main": [[{"node": "Check Data Status", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Preset Parameters", "type": "main", "index": 0}]]}}}