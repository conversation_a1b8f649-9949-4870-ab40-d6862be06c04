{"meta": {"instanceId": "f4b99447bb6b56ad425b30ab755dc982ee1c258e7ce783958190eabedd1bcbb0"}, "nodes": [{"id": "d496660c-88be-4130-ad6c-32e55f820af0", "name": "Set Default Error Workflow", "type": "n8n-nodes-base.postgres", "position": [1700, 500], "parameters": {"table": {"__rl": true, "mode": "list", "value": "workflow_entity", "cachedResultName": "workflow_entity"}, "schema": {"__rl": true, "mode": "list", "value": "public"}, "columns": {"value": {"id": "={{ $json.id }}", "settings": "={{ JSON.stringify({ ...$json.settings, errorWorkflow: $('Set Vars').item.json.default_error_workflow_id }, null, null) }}"}, "schema": [{"id": "name", "type": "string", "display": true, "removed": true, "required": true, "displayName": "name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "active", "type": "boolean", "display": true, "removed": true, "required": true, "displayName": "active", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "nodes", "type": "object", "display": true, "removed": true, "required": true, "displayName": "nodes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "connections", "type": "object", "display": true, "removed": true, "required": true, "displayName": "connections", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "createdAt", "type": "dateTime", "display": true, "removed": true, "required": false, "displayName": "createdAt", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "updatedAt", "type": "dateTime", "display": true, "removed": true, "required": false, "displayName": "updatedAt", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "settings", "type": "object", "display": true, "removed": false, "required": false, "displayName": "settings", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "staticData", "type": "object", "display": true, "removed": true, "required": false, "displayName": "staticData", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "pinData", "type": "object", "display": true, "removed": true, "required": false, "displayName": "pinData", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "versionId", "type": "string", "display": true, "removed": true, "required": false, "displayName": "versionId", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "triggerCount", "type": "number", "display": true, "removed": true, "required": false, "displayName": "triggerCount", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "id", "type": "string", "display": true, "removed": false, "required": true, "displayName": "id", "defaultMatch": true, "canBeUsedToMatch": true}, {"id": "meta", "type": "object", "display": true, "removed": true, "required": false, "displayName": "meta", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["id"]}, "options": {}, "operation": "update"}, "credentials": {"postgres": {"id": "rFLN9F42378ayUmI", "name": "GCS:threat-intel-context/dev-n8n-conf"}}, "retryOnFail": true, "typeVersion": 2.3}, {"id": "334c557c-bc6c-44f8-85ac-3cacc145cf2f", "name": "Set Vars", "type": "n8n-nodes-base.set", "position": [1040, 500], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "b2302801-f93e-4134-a785-47454dfe31d4", "name": "default_error_workflow_id", "type": "string", "value": "2fgSBCqYJyEZWtTO"}, {"id": "efe2c80d-2b98-4a6b-8f76-7e2d5866c4ea", "name": "default_error_exclusion_tag", "type": "string", "value": "default_error:false"}]}}, "retryOnFail": true, "typeVersion": 3.3}, {"id": "858d36f2-1024-43dd-89e9-00402fb1bae2", "name": "Exclude default_error:false Tagged Workflows", "type": "n8n-nodes-base.filter", "position": [1480, 500], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "911501c7-18cc-4292-a4e8-fe8f8c3cb8aa", "operator": {"type": "boolean", "operation": "false", "singleValue": true}, "leftValue": "={{ $json.tags.some(item => item.name === $('Set Vars').item.json.default_error_exclusion_tag) }}", "rightValue": ""}, {"id": "e22db4f5-ec03-4000-a996-d3150db17a73", "operator": {"type": "string", "operation": "notEquals"}, "leftValue": "={{ $json.settings.errorWorkflow ? $json.settings.errorWorkflow : \"\" }}", "rightValue": "={{ $('Set Vars').item.json.default_error_workflow_id }}"}]}}, "retryOnFail": true, "typeVersion": 2}, {"id": "f0ac7515-8175-458c-9357-b5246019a22c", "name": "When clicking \"Test workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [780, 580], "parameters": {}, "typeVersion": 1}, {"id": "2545b766-a0a0-4e31-9941-d51d5594aff6", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [780, 400], "parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 4}]}}, "notesInFlow": false, "typeVersion": 1.1}, {"id": "901e4df3-4dd3-4b92-ac09-555d51d2d7e9", "name": "Get All Workflows", "type": "n8n-nodes-base.n8n", "position": [1260, 500], "parameters": {"filters": {}}, "credentials": {"n8nApi": {"id": "r2RZq6ObikiqFu1y", "name": "n8n account"}}, "retryOnFail": true, "typeVersion": 1}], "pinData": {}, "connections": {"Set Vars": {"main": [[{"node": "Get All Workflows", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Set Vars", "type": "main", "index": 0}]]}, "Get All Workflows": {"main": [[{"node": "Exclude default_error:false Tagged Workflows", "type": "main", "index": 0}]]}, "When clicking \"Test workflow\"": {"main": [[{"node": "Set Vars", "type": "main", "index": 0}]]}, "Exclude default_error:false Tagged Workflows": {"main": [[{"node": "Set Default Error Workflow", "type": "main", "index": 0}]]}}}