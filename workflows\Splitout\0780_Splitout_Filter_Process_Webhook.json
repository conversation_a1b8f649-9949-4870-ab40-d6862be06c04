{"meta": {"instanceId": "d4d7965840e96e50a3e02959a8487c692901dfa8d5cc294134442c67ce1622d3", "templateCredsSetupCompleted": true}, "nodes": [{"id": "5d02237f-151b-4bb4-9341-b11149925309", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-980, -40], "parameters": {}, "typeVersion": 1}, {"id": "dc60bffa-b6f8-432d-85ed-0d08f092a454", "name": "Filter", "type": "n8n-nodes-base.filter", "position": [-20, -220], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "3b4c2e25-862d-4b4e-aa66-38c5f0e5a7b2", "operator": {"type": "string", "operation": "contains"}, "leftValue": "={{ $json['urlset.url']['news:news']['news:title'] }}", "rightValue": "raise"}]}}, "typeVersion": 2.2}, {"id": "f779a004-57f6-451b-984b-3fd9517e4842", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [1900, 100], "parameters": {"jsonSchemaExample": "{\n\t\"website_url\": \"https://example.com\"\n}"}, "typeVersion": 1.2}, {"id": "adcffd7e-943e-488f-a11b-0b64e45c6ff6", "name": "Perplexity", "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "position": [1380, 100], "parameters": {"model": "perplexity/llama-3.1-sonar-small-128k-online", "options": {}}, "credentials": {"openRouterApi": {"id": "Wz9uIFEMzOmhbt1D", "name": "OpenRouter account"}}, "typeVersion": 1}, {"id": "0c90db72-bbd0-4f8c-bf9e-6005dc99344f", "name": "Filter1", "type": "n8n-nodes-base.filter", "position": [-20, 120], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "3b4c2e25-862d-4b4e-aa66-38c5f0e5a7b2", "operator": {"type": "string", "operation": "contains"}, "leftValue": "={{ $json.loc }}", "rightValue": "raise"}]}}, "typeVersion": 2.2}, {"id": "f770791d-a987-4509-b8fc-648f31deda88", "name": "Extract Structured Data ", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [960, -60], "parameters": {"text": "=Article Title: {{ $json.title }}\nArticle Text:{{ $json.text }}", "options": {}, "attributes": {"attributes": [{"name": "company_name", "description": "What is the company called"}, {"name": "funding_round", "description": "Seed, Series A,B,C,D etc."}, {"name": "funding_amount", "type": "number", "description": "How much is the amount of the funding round - full numbers please"}, {"name": "lead_investor", "description": "Who is leading the funding round"}, {"name": "market", "description": "In which market is the company operating"}, {"name": "participating_investors", "description": "Comma separated list of other participating investors"}, {"name": "press_release_url", "description": "The URL to the original press release "}, {"name": "evaluation", "type": "number", "description": "How high is the evaluation of the company - full numbers please"}]}}, "typeVersion": 1}, {"id": "370e4875-a95b-4736-b500-7427dd6b9e57", "name": "Research URL", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1300, -60], "parameters": {"text": "=Find the website for this company: {{ $json.output.company_name }}", "promptType": "define"}, "typeVersion": 1.5}, {"id": "6d0cd072-f97f-466b-8e11-c2affad19a3f", "name": "Extract URL", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1680, -60], "parameters": {"text": "={{ $json.text }}", "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.5}, {"id": "196cca20-66f0-4732-a36b-235606700bd4", "name": "Merge Extracted Data", "type": "n8n-nodes-base.merge", "position": [720, -60], "parameters": {}, "typeVersion": 3}, {"id": "a7063f58-6eaa-4248-b9e9-6f6cc3551d24", "name": "Split TC Articles", "type": "n8n-nodes-base.splitOut", "position": [-160, -220], "parameters": {"include": "=", "options": {}, "fieldToSplitOut": "urlset.url"}, "typeVersion": 1}, {"id": "2bfebd7a-3f63-4206-9de0-1053b8e760da", "name": "TC HTML Parser", "type": "n8n-nodes-base.html", "position": [440, -220], "parameters": {"options": {"cleanUpText": true}, "operation": "extractHtmlContent", "extractionValues": {"values": [{"key": "text", "cssSelector": ".wp-block-post-content"}, {"key": "title", "cssSelector": ".wp-block-post-title"}]}}, "typeVersion": 1.2}, {"id": "2c52fc2d-42bc-4f62-8157-ff0cece23d48", "name": "Split VB Articles", "type": "n8n-nodes-base.splitOut", "position": [-160, 120], "parameters": {"options": {}, "fieldToSplitOut": "urlset.url"}, "typeVersion": 1}, {"id": "b9fd414c-70a8-40b0-bb1a-b499b7be5ff5", "name": "VB HTML Parser", "type": "n8n-nodes-base.html", "position": [440, 120], "parameters": {"options": {}, "operation": "extractHtmlContent", "extractionValues": {"values": [{"key": "text", "cssSelector": "#content"}, {"key": "title", "cssSelector": ".article-title"}]}}, "typeVersion": 1.2}, {"id": "67825bec-b689-4257-a421-0446884b918e", "name": "Venturebeat (VB)", "type": "n8n-nodes-base.httpRequest", "position": [-640, 120], "parameters": {"url": "https://venturebeat.com/news-sitemap.xml", "options": {}}, "typeVersion": 4.2}, {"id": "68d1f94b-89c1-4d00-8400-475722ff8a0f", "name": "Techcrunch (TC)", "type": "n8n-nodes-base.httpRequest", "position": [-640, -220], "parameters": {"url": "https://techcrunch.com/news-sitemap.xml", "options": {}}, "typeVersion": 4.2}, {"id": "30a62f02-089b-4d10-ae6f-6f19119934c2", "name": "Claude 3.5 Sonnet", "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "position": [1060, 100], "parameters": {"model": "claude-3-5-sonnet-********", "options": {}}, "credentials": {"anthropicApi": {"id": "IuDNko14nN79w51k", "name": "Anthropic account 2"}}, "typeVersion": 1.2}, {"id": "6e8453ba-ac37-4e10-bb50-df92d3d342a1", "name": "Claude 3.5 Haiku", "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "position": [1740, 100], "parameters": {"model": "claude-3-5-haiku-********", "options": {}}, "credentials": {"anthropicApi": {"id": "IuDNko14nN79w51k", "name": "Anthropic account 2"}}, "typeVersion": 1.2}, {"id": "2bb5b903-c08d-4231-962b-7c56616b4f1e", "name": "Collect Data", "type": "n8n-nodes-base.set", "position": [2060, -60], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "379c7461-0ede-413a-9976-02c1351caf9b", "name": "website_url", "type": "string", "value": "={{ $json.output.website_url }}"}, {"id": "1e638aa9-bbc6-4869-8aa3-9ebb102cf290", "name": "company_name", "type": "string", "value": "={{ $('Extract Structured Data ').item.json.output.company_name }}"}, {"id": "8047a593-0aa0-4ef5-89c1-1e1f3c42ee23", "name": "funding_round", "type": "string", "value": "={{ $('Extract Structured Data ').item.json.output.funding_round }}"}, {"id": "fb324383-fe81-4964-bc18-a5992e1005a8", "name": "funding_amount", "type": "number", "value": "={{ $('Extract Structured Data ').item.json.output.funding_amount }}"}, {"id": "75c1c919-0249-468d-8c08-ce818a8260b9", "name": "lead_investor", "type": "string", "value": "={{ $('Extract Structured Data ').item.json.output.lead_investor }}"}, {"id": "1b938b68-68ad-4b59-a372-d141b3fa188a", "name": "market", "type": "string", "value": "={{ $('Extract Structured Data ').item.json.output.market }}"}, {"id": "0b2efd2b-ef69-4e59-ac2b-7ef47e288965", "name": "participating_investors", "type": "string", "value": "={{ $('Extract Structured Data ').item.json.output.participating_investors }}"}, {"id": "f49e6523-f000-4c8b-bdec-7e436ead6359", "name": "press_release_url", "type": "string", "value": "={{ $('Extract Structured Data ').item.json.output.press_release_url }}"}, {"id": "270451dc-2f26-41f0-8b6a-2afe4e498652", "name": "evaluation", "type": "number", "value": "={{ $('Extract Structured Data ').item.json.output.evaluation }}"}]}}, "typeVersion": 3.4}, {"id": "29a49e31-22dd-4f33-89ca-d12bbf217c76", "name": "Airtable", "type": "n8n-nodes-base.airtable", "position": [2320, -160], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appYwSYZShjr8TN5r", "cachedResultUrl": "https://airtable.com/appYwSYZShjr8TN5r", "cachedResultName": "Funding Rounds"}, "table": {"__rl": true, "mode": "list", "value": "tblQTIWUC8FBMF16F", "cachedResultUrl": "https://airtable.com/appYwSYZShjr8TN5r/tblQTIWUC8FBMF16F", "cachedResultName": "Funding Round Base"}, "columns": {"value": {}, "schema": [{"id": "company_name", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "company_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "website_url", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "website_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "funding_round", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "funding_round", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "funding_amount", "type": "number", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "funding_amount", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "lead_investor", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "lead_investor", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "participating_investors", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "participating_investors", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "market", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "market", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "press_release_url", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "press_release_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "evaluation", "type": "number", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "evaluation", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "create"}, "credentials": {"airtableTokenApi": {"id": "JwUch1mrw0pUVtnE", "name": "Airtable Personal Access Token account 2"}}, "typeVersion": 2.1}, {"id": "497815af-12ea-4d1e-94aa-c403bcc55f7b", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1420, 400], "parameters": {"color": 4, "width": 300, "content": "## Company Research\nUsing Perplexity Deep Research  we can find more information about the company."}, "typeVersion": 1}, {"id": "13a156e5-d3e0-46b8-9ad6-0a1c3de775b0", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-700, -440], "parameters": {"color": 6, "content": "## TechCrunch & VentureBeat\nHTTP GET requests to fetch the latest articles from tech news sitemap feeds."}, "typeVersion": 1}, {"id": "0f202470-8506-4e01-a5fb-f3519ede91a8", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-440, -440], "parameters": {"content": "## Parse XML\nConverts XML data to structured JSON for easier processing of article metadata."}, "typeVersion": 1}, {"id": "dd485685-9962-47b0-9876-f8ebdb434040", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-180, -440], "parameters": {"width": 280, "content": "## Split Articles & Filter:\nSeparates individual articles and filters to keep only the most relevant ones based on keywords (raised)"}, "typeVersion": 1}, {"id": "fda62c36-cb3e-45a9-8c8c-42d2a3b13ea6", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [120, -440], "parameters": {"content": "## Get Article\nFetches the full article content for each relevant article in the feed."}, "typeVersion": 1}, {"id": "caca7808-997d-48a1-8869-edcbd2dce2dd", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [380, -440], "parameters": {"content": "## HTML Parser\nExtracts clean text content from the HTML articles for analysis."}, "typeVersion": 1}, {"id": "97cbd997-6f42-4454-a33a-81737ea8bd9f", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [640, -440], "parameters": {"width": 260, "content": "## Merge Extracted Data\nCombines articles from multiple sources into a unified dataset for comprehensive analysis."}, "typeVersion": 1}, {"id": "d9b84d9e-7718-419a-9d81-6403e3273f36", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [920, -440], "parameters": {"width": 300, "content": "## Extract Structured Data\nIdentifies and structures key information from article text such as company names, funding details, and tech trends."}, "typeVersion": 1}, {"id": "060e8914-c025-4594-a99a-539c5c5cfec4", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [1240, -440], "parameters": {"width": 360, "content": "## Research company website\nUses perplexity to find the company website with search"}, "typeVersion": 1}, {"id": "7f1dc67d-7c2d-4b6a-83b2-c2775bc91085", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [1620, -440], "parameters": {"width": 360, "content": "## Extract URL\nSince perplexity uses Llama which is not great at structured output - 2 step approach for a more reliable run"}, "typeVersion": 1}, {"id": "fa7d5737-7970-478d-aa4d-24db31c6ac2e", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [2000, -440], "parameters": {"width": 420, "content": "## Collect data & write to airtable\nCollecting all data to pass on to detailed company research and store information in airtable"}, "typeVersion": 1}, {"id": "1ce484cc-d22c-44b7-8de3-421904307353", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [2040, 400], "parameters": {"color": 4, "width": 440, "content": "## Extract structured data from report\nDeep Research produces long text output. We use a parser here to make the information available in structured format. As the json structure is quite complex I am using a strong model and the Auto-fixing Output Parser\n"}, "typeVersion": 1}, {"id": "50fbc465-a6ab-4fc7-af1e-f91bd2f40e5d", "name": "Auto-fixing Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserAutofixing", "position": [2280, 780], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "3eb9aa59-9b57-4375-ac39-4e1fd900365e", "name": "Extract Structured JSON ", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [2440, 920], "parameters": {"schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"company_name\": {\n      \"type\": \"string\",\n      \"description\": \"Official name of the company receiving funding\"\n    },\n    \"funding_round\": {\n      \"type\": \"string\",\n      \"description\": \"Type of funding round (Seed, Series A, B, C, etc.)\",\n      \"enum\": [\"Pre-Seed\", \"Seed\", \"Series A\", \"Series B\", \"Series C\", \"Series D\", \"Series E+\", \"Growth Equity\", \"Late Stage\", \"Venture Round\", \"Convertible Note\", \"Debt Financing\", \"Grant\", \"Private Equity\", \"Other\"]\n    },\n    \"funding_amount\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"value\": {\n          \"type\": \"number\",\n          \"description\": \"Numerical value of the funding amount\"\n        },\n        \"currency\": {\n          \"type\": \"string\",\n          \"description\": \"Currency of the funding amount\",\n          \"default\": \"USD\"\n        }\n      }\n    },\n    \"announcement_date\": {\n      \"type\": \"string\",\n      \"format\": \"date\",\n      \"description\": \"Date when the funding was publicly announced (YYYY-MM-DD)\"\n    },\n    \"lead_investor\": {\n      \"type\": [\"string\", \"array\"],\n      \"description\": \"Primary investor(s) leading the round. Can be single string or array for multiple leads.\"\n    },\n    \"participating_investors\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      },\n      \"description\": \"Other firms or angels who participated in the round\"\n    },\n    \"industry\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      },\n      \"description\": \"Primary industry categories or verticals\"\n    },\n    \"company_description\": {\n      \"type\": \"string\",\n      \"description\": \"Brief explanation of what the company does\"\n    },\n    \"hq_location\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"city\": {\n          \"type\": \"string\"\n        },\n        \"country\": {\n          \"type\": \"string\"\n        }\n      },\n      \"description\": \"Company headquarters location\"\n    },\n    \"website_url\": {\n      \"type\": \"string\",\n      \"format\": \"uri\",\n      \"description\": \"Company's official website\"\n    },\n    \"founding_year\": {\n      \"type\": \"integer\",\n      \"description\": \"Year the company was founded\"\n    },\n    \"founder_names\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      },\n      \"description\": \"Names of company founders\"\n    },\n    \"ceo_name\": {\n      \"type\": \"string\",\n      \"description\": \"Name of current CEO\"\n    },\n    \"employee_count\": {\n      \"type\": [\"integer\", \"string\"],\n      \"description\": \"Current number of employees (exact or range)\"\n    },\n    \"total_funding\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"value\": {\n          \"type\": \"number\"\n        },\n        \"currency\": {\n          \"type\": \"string\",\n          \"default\": \"USD\"\n        }\n      },\n      \"description\": \"Total funding raised to date including this round\"\n    },\n    \"funding_purpose\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      },\n      \"description\": \"Stated use of funds (e.g., expansion, R&D, marketing)\"\n    },\n    \"business_model\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\",\n        \"enum\": [\"B2B\", \"B2C\", \"B2B2C\", \"D2C\", \"Marketplace\", \"SaaS\", \"Hardware\", \"Hybrid\", \"Other\"]\n      },\n      \"description\": \"Company's business model categories\"\n    },\n    \"valuation\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"pre_money\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"value\": {\n              \"type\": \"number\"\n            },\n            \"currency\": {\n              \"type\": \"string\",\n              \"default\": \"USD\"\n            }\n          }\n        },\n        \"post_money\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"value\": {\n              \"type\": \"number\"\n            },\n            \"currency\": {\n              \"type\": \"string\",\n              \"default\": \"USD\"\n            }\n          }\n        }\n      },\n      \"description\": \"Company valuation information (if disclosed)\"\n    },\n    \"previous_rounds\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"date\": {\n            \"type\": \"string\",\n            \"format\": \"date\"\n          },\n          \"round\": {\n            \"type\": \"string\"\n          },\n          \"amount\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"value\": {\n                \"type\": \"number\"\n              },\n              \"currency\": {\n                \"type\": \"string\"\n              }\n            }\n          },\n          \"investors\": {\n            \"type\": \"array\",\n            \"items\": {\n              \"type\": \"string\"\n            }\n          }\n        }\n      },\n      \"description\": \"Information about previous funding rounds\"\n    }\n  }\n}"}, "typeVersion": 1.2}, {"id": "fde5dc11-c264-4f82-982b-23398de84888", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [960, 400], "parameters": {"color": 4, "content": "## Exectuted as a subworkflow\n"}, "typeVersion": 1}, {"id": "618170f3-eacd-4c9f-bd0f-1530408ff50d", "name": "Prompts", "type": "n8n-nodes-base.set", "position": [1280, 600], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "6751c31d-b5d5-4c87-bc36-5b7f5e317062", "name": "user_prompt", "type": "string", "value": "=I need comprehensive information about {{ $json.company_name }} that recently announced a {{ $json.funding_round }} funding round of {{ $json.funding_amount }}.  \n\nPlease research and compile the following:  \n\n## Company Background  \n- Year founded and founding story \n- Founder backgrounds and previous ventures \n- Current executive team composition \n- Total funding raised to date (including all previous rounds) \n- Previous investors before this round \n\n## Business Analysis  \n- Detailed description of products/services \n- Primary revenue model (subscription, freemium, transaction fees, etc.) \n- Target customer segments \n- Current estimated customer/user count \n- Estimated annual revenue (if available) \n\n## Market Position  \n- Primary competitors in their space \n- Unique selling proposition/competitive advantage \n- Recent partnerships or major client announcements \n- Market size and growth rate of their industry \n- Their estimated market share Growth Trajectory  \n- Employee growth rate over past 1-2 years \n- Geographic expansion plans \n- Recent product launches or roadmap information \n- Any stated plans for the funding (expansion, R&D, etc.) \n\n## Additional Context  \n- Any recent news about the company beyond funding \n- Relevant industry trends affecting their business \n- Notable advisors or board members \n- Any regulatory considerations for their market\n"}, {"id": "362a60c8-8c48-44c6-819e-217d361a4c4d", "name": "system_prompt", "type": "string", "value": "=You are a company research assistant.\nPlease include sources for all information gathered so I can verify and explore further."}]}}, "typeVersion": 3.4}, {"id": "170f4ffb-ebeb-4277-8d72-b5bd553b5a3e", "name": "Deep Research", "type": "n8n-nodes-base.httpRequest", "position": [1520, 600], "parameters": {"url": "https://api.perplexity.ai/chat/completions", "method": "POST", "options": {}, "jsonBody": "={\n  \"model\": \"sonar-deep-research\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"{{ $json.system_prompt.replace(/\\n/g, \" \").replace(/\\s+/g, \" \").trim() }}\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"{{ $json.user_prompt.replace(/\\n/g, \" \").replace(/\\s+/g, \" \").trim() }}\"\n    }\n  ]\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{}]}}, "credentials": {"httpHeaderAuth": {"id": "zeTmfLMIZb16l3SX", "name": "Perplexity Auth"}}, "typeVersion": 4.2}, {"id": "a6a227b1-7590-408f-b4e4-8ea3eeef871e", "name": "Pick data (Perplexity)", "type": "n8n-nodes-base.set", "position": [1800, 600], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d99c7dc9-5d1a-4cb8-b391-62df3e905530", "name": "report", "type": "string", "value": "={{ $json.choices[0].message.content }}"}, {"id": "7f2ff728-a4f6-4422-bd65-34a09e5b6fab", "name": "links", "type": "array", "value": "={{ $json.citations }}"}]}}, "typeVersion": 3.4}, {"id": "b836050a-9cab-4cbc-bd4c-b53730e8df06", "name": "Pick data (jina)", "type": "n8n-nodes-base.set", "position": [1800, 840], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "93201e0b-ad34-421a-92f0-bf7e78a81743", "name": "report", "type": "string", "value": "={{ $json.choices[0].message.content }}"}, {"id": "39133a41-16fb-4008-8e60-8994f2158963", "name": "links", "type": "array", "value": "={{ $json.visitedURLs }}"}]}}, "typeVersion": 3.4}, {"id": "925c6359-de4f-409f-b872-d12180d3a957", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1500, 1000], "parameters": {"color": 4, "width": 420, "height": 140, "content": "## Optional: Use jina Deep Search\nhttps://jina.ai/news/a-practical-guide-to-implementing-deepsearch-deepresearch\n\n"}, "typeVersion": 1}, {"id": "fd00e4d5-dffd-4ecb-a722-7f37d6dc5b9f", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [1020, 600], "parameters": {"workflowInputs": {"values": [{"name": "company_name"}, {"name": "funding_amount", "type": "number"}, {"name": "funding_round"}]}}, "typeVersion": 1.1}, {"id": "859ed482-0670-4023-92cf-aec4d688193d", "name": "JINA Deep Search", "type": "n8n-nodes-base.httpRequest", "position": [1520, 840], "parameters": {"url": "https://deepsearch.jina.ai/v1/chat/completions", "method": "POST", "options": {}, "jsonBody": "={\n  \"model\": false,\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": \"{{ $json.system_prompt.replace(/\\n/g, \" \").replace(/\\s+/g, \" \").trim() }}\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"{{ $json.user_prompt.replace(/\\n/g, \" \").replace(/\\s+/g, \" \").trim() }}\"\n    }\n  ],\n  \"stream\": false,\n  \"reasoning_effort\": false\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "30Y0DulqMzqn5psh", "name": "<PERSON><PERSON>"}}, "typeVersion": 4.2}, {"id": "9b3f8bc7-72b4-4010-8427-5fd6a9706e50", "name": "Write Results to Airtable", "type": "n8n-nodes-base.airtable", "position": [2720, 600], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appYwSYZShjr8TN5r", "cachedResultUrl": "https://airtable.com/appYwSYZShjr8TN5r", "cachedResultName": "Funding Rounds"}, "table": {"__rl": true, "mode": "list", "value": "tbltUvIthISpEbgUp", "cachedResultUrl": "https://airtable.com/appYwSYZShjr8TN5r/tbltUvIthISpEbgUp", "cachedResultName": "Company Deep Research"}, "columns": {"value": {"ceo_name": "={{ $json.output.ceo_name }}", "currency": "={{ $json.output.funding_amount.currency }}", "industry": "={{ Array.isArray($json.output.industry) ? $json.output.industry.join(', ') : $json.output.industry }}", "valuation": "={{ JSON.stringify($json.output.valuation) }}", "hq_location": "={{ $json.output.hq_location.city }}, {{ $json.output.hq_location.country }}", "source_urls": "={{ $('Pick data (Perplexity)').item.json.links.map((item, idx) => `${idx + 1}: ${item}`).join('\\n') }}", "company_name": "={{ $json.output.company_name }}", "founder_names": "={{ Array.isArray($json.output.founder_names) ? $json.output.founder_names.join(', ') : $json.output.founder_names }}", "founding_year": "={{ $json.output.founding_year }}", "funding_round": "={{ $json.output.funding_round }}", "lead_investor": "={{ Array.isArray($json.output.lead_investor) ? $json.output.lead_investor.join(', ') : $json.output.lead_investor }}", "total_funding": "={{ $json.output.total_funding.value }}", "business_model": "={{ Array.isArray($json.output.business_model) ? $json.output.business_model.join(', ') : $json.output.business_model }}", "employee_count": "={{ $json.output.employee_count }}", "funding_amount": "={{ $json.output.funding_amount.value }}", "funding_purpose": "={{ Array.isArray($json.output.funding_purpose) ? $json.output.funding_purpose.join(', ') : $json.output.funding_purpose }}\n", "original_report": "={{ $('Pick data (Perplexity)').item.json.report }}", "previous_rounds": "={{ JSON.stringify($json.output.previous_rounds) }}", "announcement_date": "={{ $json.output.announcement_date }}", "company_description": "={{ $json.output.company_description }}", "total_funding_currency": "={{ $json.output.total_funding.currency }}", "participating_investors": "={{ Array.isArray($json.output.participating_investors) ? $json.output.participating_investors.join(', ') : $json.output.participating_investors }}"}, "schema": [{"id": "company_name", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "company_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "funding_round", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "funding_round", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "funding_amount", "type": "number", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "funding_amount", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "currency", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "currency", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "announcement_date", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "announcement_date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "lead_investor", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "lead_investor", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "participating_investors", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "participating_investors", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "industry", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "industry", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_description", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "company_description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "hq_location", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "hq_location", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "website_url", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "website_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "founding_year", "type": "number", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "founding_year", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "founder_names", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "founder_names", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ceo_name", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "ceo_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "employee_count", "type": "number", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "employee_count", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "total_funding", "type": "number", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "total_funding", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "total_funding_currency", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "total_funding_currency", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "funding_purpose", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "funding_purpose", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "business_model", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "business_model", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "valuation", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "valuation", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "previous_rounds", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "previous_rounds", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "source_urls", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "source_urls", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "original_report", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "original_report", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "create"}, "credentials": {"airtableTokenApi": {"id": "JwUch1mrw0pUVtnE", "name": "Airtable Personal Access Token account 2"}}, "typeVersion": 2.1}, {"id": "703707ad-8bb4-4d86-9531-90866a400e39", "name": "Extract Structured Data", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [2120, 600], "parameters": {"text": "=\nSources Urls: \n{{ $json.links.map((item, idx) => `${idx + 1}: ${item}`).join('\\n') }}\n\nReport: {{ $json.report.replace(/<think>[\\s\\S]*?<\\/think>/g, ''); }}", "messages": {"messageValues": [{"message": "Only extract available information. Do not fill in information that cant be backed with the provided document."}]}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.5}, {"id": "8475db8d-9d57-4476-bdac-beee857f50df", "name": "Sticky Note14", "type": "n8n-nodes-base.stickyNote", "position": [-1140, -440], "parameters": {"color": 6, "width": 420, "content": "## Identify companies that just raised funds\n\nIncludes deep research of the Company Background, Market Position and Business Analysis "}, "typeVersion": 1}, {"id": "330e38c4-9941-4c27-ac88-243ef032dd5d", "name": "Route to Deep Research", "type": "n8n-nodes-base.executeWorkflow", "position": [2320, 40], "parameters": {"options": {}, "workflowId": {"__rl": true, "mode": "id", "value": "TtsCaYjVToaUEE6V"}, "workflowInputs": {"value": {"company_name": "={{ $json.company_name }}", "funding_round": "={{ $json.funding_round }}", "funding_amount": "={{ $json.funding_amount }}"}, "schema": [{"id": "company_name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "funding_amount", "type": "number", "display": true, "removed": false, "required": false, "displayName": "funding_amount", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "funding_round", "type": "string", "display": true, "removed": false, "required": false, "displayName": "funding_round", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": true}}, "typeVersion": 1.2}, {"id": "d4061dc5-5849-487b-ae2c-a0cf60b8ed12", "name": "Parse TC XML", "type": "n8n-nodes-base.xml", "position": [-380, -220], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "3c8a9ac6-8f7b-4b21-91d3-7d8cf25670f7", "name": "Parse VB XML", "type": "n8n-nodes-base.xml", "position": [-380, 120], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "3b61cc4d-dc62-4266-8f59-740a23cd8a1c", "name": "Sticky Note15", "type": "n8n-nodes-base.stickyNote", "position": [2560, -160], "parameters": {"color": 6, "width": 400, "height": 120, "content": "## Airtable Base \nhttps://airtable.com/appYwSYZShjr8TN5r/shryOEdmJmZE5ROce"}, "typeVersion": 1}, {"id": "02c13204-1e3e-4f1a-be15-5ee842696340", "name": "Claude  3.5 Sonnet", "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "position": [2120, 920], "parameters": {"model": "claude-3-5-sonnet-********", "options": {}}, "credentials": {"anthropicApi": {"id": "IuDNko14nN79w51k", "name": "Anthropic account 2"}}, "typeVersion": 1.2}, {"id": "77df4556-e5da-4bd6-9df2-bb4993256258", "name": "Get Funding Article HTML for scraping (TC)", "type": "n8n-nodes-base.httpRequest", "position": [180, -220], "parameters": {"url": "={{ $json['urlset.url'].loc }}", "options": {}}, "typeVersion": 4.2}, {"id": "c6082aa7-84ce-40ce-8372-a73163439a8f", "name": "Get Funding Article HTML for scraping (VB)", "type": "n8n-nodes-base.httpRequest", "position": [180, 120], "parameters": {"url": "={{ $json.loc }}", "options": {}}, "typeVersion": 4.2}], "pinData": {}, "connections": {"Filter": {"main": [[{"node": "Get Funding Article HTML for scraping (TC)", "type": "main", "index": 0}]]}, "Filter1": {"main": [[{"node": "Get Funding Article HTML for scraping (VB)", "type": "main", "index": 0}]]}, "Prompts": {"main": [[{"node": "Deep Research", "type": "main", "index": 0}]]}, "Perplexity": {"ai_languageModel": [[{"node": "Research URL", "type": "ai_languageModel", "index": 0}]]}, "Extract URL": {"main": [[{"node": "Collect Data", "type": "main", "index": 0}]]}, "Collect Data": {"main": [[{"node": "Route to Deep Research", "type": "main", "index": 0}, {"node": "Airtable", "type": "main", "index": 0}]]}, "Parse TC XML": {"main": [[{"node": "Split TC Articles", "type": "main", "index": 0}]]}, "Parse VB XML": {"main": [[{"node": "Split VB Articles", "type": "main", "index": 0}]]}, "Research URL": {"main": [[{"node": "Extract URL", "type": "main", "index": 0}]]}, "Deep Research": {"main": [[{"node": "Pick data (Perplexity)", "type": "main", "index": 0}]]}, "TC HTML Parser": {"main": [[{"node": "Merge Extracted Data", "type": "main", "index": 0}]]}, "VB HTML Parser": {"main": [[{"node": "Merge Extracted Data", "type": "main", "index": 1}]]}, "Techcrunch (TC)": {"main": [[{"node": "Parse TC XML", "type": "main", "index": 0}]]}, "Claude 3.5 Haiku": {"ai_languageModel": [[{"node": "Extract URL", "type": "ai_languageModel", "index": 0}]]}, "JINA Deep Search": {"main": [[{"node": "Pick data (jina)", "type": "main", "index": 0}]]}, "Pick data (jina)": {"main": [[]]}, "Venturebeat (VB)": {"main": [[{"node": "Parse VB XML", "type": "main", "index": 0}]]}, "Claude 3.5 Sonnet": {"ai_languageModel": [[{"node": "Extract Structured Data ", "type": "ai_languageModel", "index": 0}]]}, "Split TC Articles": {"main": [[{"node": "Filter", "type": "main", "index": 0}]]}, "Split VB Articles": {"main": [[{"node": "Filter1", "type": "main", "index": 0}]]}, "Claude  3.5 Sonnet": {"ai_languageModel": [[{"node": "Extract Structured Data", "type": "ai_languageModel", "index": 0}, {"node": "Auto-fixing Output Parser", "type": "ai_languageModel", "index": 0}]]}, "Merge Extracted Data": {"main": [[{"node": "Extract Structured Data ", "type": "main", "index": 0}]]}, "Pick data (Perplexity)": {"main": [[{"node": "Extract Structured Data", "type": "main", "index": 0}]]}, "Extract Structured Data": {"main": [[{"node": "Write Results to Airtable", "type": "main", "index": 0}]]}, "Extract Structured Data ": {"main": [[{"node": "Research URL", "type": "main", "index": 0}]]}, "Extract Structured JSON ": {"ai_outputParser": [[{"node": "Auto-fixing Output Parser", "type": "ai_outputParser", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Extract URL", "type": "ai_outputParser", "index": 0}]]}, "Auto-fixing Output Parser": {"ai_outputParser": [[{"node": "Extract Structured Data", "type": "ai_outputParser", "index": 0}]]}, "Write Results to Airtable": {"main": [[]]}, "When Executed by Another Workflow": {"main": [[{"node": "Prompts", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Techcrunch (TC)", "type": "main", "index": 0}, {"node": "Venturebeat (VB)", "type": "main", "index": 0}]]}, "Get Funding Article HTML for scraping (TC)": {"main": [[{"node": "TC HTML Parser", "type": "main", "index": 0}]]}, "Get Funding Article HTML for scraping (VB)": {"main": [[{"node": "VB HTML Parser", "type": "main", "index": 0}]]}}}