{"id": "U8EOTtZvmZPMYc6m", "meta": {"instanceId": "fb924c73af8f703905bc09c9ee8076f48c17b596ed05b18c0ff86915ef8a7c4a", "templateCredsSetupCompleted": true}, "name": "Agentic Telegram AI bot with LangChain nodes and new tools", "tags": [], "nodes": [{"id": "13b3488e-af72-4d89-bef4-e9b895e3bf76", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1640, 580], "parameters": {"model": "gpt-4o", "options": {"temperature": 0.7, "frequencyPenalty": 0.2}}, "credentials": {"openAiApi": {"id": "rveqdSfp7pCRON1T", "name": "Ted's Tech Talks OpenAi"}}, "typeVersion": 1}, {"id": "864937a1-43f6-4055-bdea-61ab07db9903", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [1760, 580], "parameters": {"sessionKey": "=chat_with_{{ $('Listen for incoming events').first().json.message.chat.id }}", "contextWindowLength": 10}, "typeVersion": 1}, {"id": "4ef838d4-feaa-4bd3-b2c7-ccd938be4373", "name": "Listen for incoming events", "type": "n8n-nodes-base.telegramTrigger", "position": [1580, 360], "webhookId": "322dce18-f93e-4f86-b9b1-3305519b7834", "parameters": {"updates": ["*"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "9dexJXnlVPA6wt8K", "name": "Chat & Sound"}}, "typeVersion": 1}, {"id": "fed51c41-2846-4a1a-a5f5-ce121ee7fe88", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1460, 180], "parameters": {"color": 7, "width": 926.3188190787038, "height": 553.452795998601, "content": "## Generate an image with Dall-E-<PERSON> and send it via Telegram"}, "typeVersion": 1}, {"id": "1c7a204b-3ed7-47bd-a434-202b05272d18", "name": "Send final reply", "type": "n8n-nodes-base.telegram", "onError": "continueErrorOutput", "position": [2140, 360], "parameters": {"text": "={{ $json.output }}", "chatId": "={{ $('Listen for incoming events').first().json.message.from.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "9dexJXnlVPA6wt8K", "name": "Chat & Sound"}}, "typeVersion": 1.1}, {"id": "bebbe9d4-47ba-4c13-9e1e-d36bfe6e472e", "name": "Send back an image", "type": "n8n-nodes-base.telegramTool", "position": [2020, 580], "parameters": {"file": "={{ $fromAI(\"url\", \"a valid url of an image\", \"string\", \" \") }}", "chatId": "={{ $('Listen for incoming events').first().json.message.from.id }}", "operation": "sendDocument", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "9dexJXnlVPA6wt8K", "name": "Chat & Sound"}}, "typeVersion": 1.2}, {"id": "38f2410d-bd55-4ddf-8aaa-4e28919de78f", "name": "Generate image in Dalle", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1880, 580], "parameters": {"url": "https://api.openai.com/v1/images/generations", "method": "POST", "sendBody": true, "authentication": "predefinedCredentialType", "parametersBody": {"values": [{"name": "model", "value": "dall-e-3", "valueProvider": "fieldValue"}, {"name": "prompt"}]}, "toolDescription": "Call this tool to request a Dall-E-3 model, when the user asks to draw something. If you gеt a response from this tool, forward it to the Telegram tool.", "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "rveqdSfp7pCRON1T", "name": "Ted's Tech Talks OpenAi"}}, "typeVersion": 1.1}, {"id": "34265eab-9f37-475a-a2ae-a6c37c69c595", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1780, 360], "parameters": {"text": "={{ $json.message.text }}", "options": {"systemMessage": "=You are a helpful assistant. You are communicating with a user named {{ $json.message.from.first_name }}. Address the user by name every time. If the user asks for an image, always send the link to the image in the final reply."}, "promptType": "define"}, "typeVersion": 1.7}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "b36989c5-295a-4df6-84e9-776815509bc9", "connections": {"AI Agent": {"main": [[{"node": "Send final reply", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Send back an image": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Generate image in Dalle": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Listen for incoming events": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}}}