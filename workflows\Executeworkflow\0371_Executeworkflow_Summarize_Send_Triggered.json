{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "79573e58-f33f-445a-ad9a-0a92fde845c2", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-280, 480], "parameters": {"width": 1174.6162657502882, "height": 578.9520146851776, "content": "## Sub-workflow: Return the capitals of fictional countries\nIt can either list the countries it knows about or return the capital of a specific country"}, "typeVersion": 1}, {"id": "5eddcce9-7ee5-4ec7-a0a1-525a9b806994", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-280, -80], "parameters": {"width": 1168, "height": 528, "content": "## Main workflow: Cha<PERSON> with OpenAI Assistant\nClick the 'Chat' button at the bottom of the screen to try"}, "typeVersion": 1}, {"id": "a6c38394-1be1-4002-a9b7-c4672823aaa5", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-180, 40], "webhookId": "91f22813-2f7b-4ff9-a3e6-9d53fc86fbd9", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "de0398ea-c2ad-49b9-860b-695149b94590", "name": "OpenAI", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [100, 40], "parameters": {"options": {}, "resource": "assistant", "assistantId": {"__rl": true, "mode": "id", "value": "asst_BWy0154vMGMdrX7MjCYaYv6a"}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "050042f4-f5ff-433a-9651-43cbec8eafb6", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [360, 260], "parameters": {}, "typeVersion": 1.3}, {"id": "e4ed4cfe-78fb-44a5-8bef-67168dac95ec", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-180, 740], "parameters": {"workflowInputs": {"values": [{"name": "query"}]}}, "typeVersion": 1.1}, {"id": "a3a1059c-5c36-4d90-90e2-98e37f62bdd2", "name": "Tool to call the workflow below", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [40, 260], "parameters": {"name": "country_capitals_tool", "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "description": "This tool has two modes:\n1. Pass 'list' to the tool to get a list of countries that the tool has the capitals for (one per line). This is useful if you can't find a match, to see if the country being asked about might have been misspelled.\n2. Pass one of the country names in the list to the tool to get the capital of that country. Note that the country must be spelled exactly as it is in the list of countries returned in mode 1", "workflowInputs": {"value": {"query": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('query', ``, 'string') }}"}, "schema": [{"id": "query", "type": "string", "display": true, "removed": false, "required": false, "displayName": "query", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2}, {"id": "80924ec9-5e82-4e90-8b72-42fc805d83c0", "name": "Tool: Get current date and time", "type": "@n8n/n8n-nodes-langchain.toolCode", "position": [200, 260], "parameters": {"name": "date_tool", "jsCode": "let now = DateTime.now()\nreturn now.toISO()", "description": "Call this tool to get the current timestamp (in ISO format). No parameters necessary"}, "typeVersion": 1.1}, {"id": "8f6a83bd-71eb-4f2d-b906-a18476f18f40", "name": "List countries?", "type": "n8n-nodes-base.if", "position": [40, 740], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "ca43a9bd-5db3-4240-ae46-0402c8411818", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.query }}", "rightValue": "list"}]}}, "typeVersion": 2.2}, {"id": "40d93a59-4c91-43e4-a4a3-3732475617d6", "name": "Mapping data", "type": "n8n-nodes-base.code", "position": [260, 600], "parameters": {"jsCode": "return [\n    {\n        \"country\": \"Wakanda\",\n        \"capital\": \"Birnin Zana\"\n    },\n    {\n        \"country\": \"Narnia\",\n        \"capital\": \"Cair Paravel\"\n    },\n    {\n        \"country\": \"Gondor\",\n        \"capital\": \"Minas Tirith\"\n    },\n    {\n        \"country\": \"Oz\",\n        \"capital\": \"The Emerald City\"\n    },\n    {\n        \"country\": \"Westeros\",\n        \"capital\": \"King's Landing\"\n    },\n    {\n        \"country\": \"Panem\",\n        \"capital\": \"The Capitol\"\n    },\n    {\n        \"country\": \"Ruritania\",\n        \"capital\": \"Strelsau\"\n    },\n    {\n        \"country\": \"Mordor\",\n        \"capital\": \"Barad-dûr\"\n    },\n    {\n        \"country\": \"Latveria\",\n        \"capital\": \"Doomstadt\"\n    },\n    {\n        \"country\": \"Atlantis\",\n        \"capital\": \"Poseidonis\"\n    }\n]\n"}, "typeVersion": 2}, {"id": "8765b405-8991-421c-bdc5-3eb6d3757fcb", "name": "Concatenate country names", "type": "n8n-nodes-base.summarize", "position": [460, 600], "parameters": {"options": {}, "fieldsToSummarize": {"values": [{"field": "country", "separateBy": "\n", "aggregation": "concatenate"}]}}, "typeVersion": 1.1}, {"id": "c0b21934-8518-49b4-bbab-f13ad0a74343", "name": "Return country list", "type": "n8n-nodes-base.set", "position": [660, 600], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c97c3abc-40b2-4238-912d-030eb9ca3440", "name": "response", "type": "string", "value": "={{ $json.concatenated_country }}"}]}}, "typeVersion": 3.4}, {"id": "5d4f05cc-f4e3-4ce6-9ea8-a324257fa7c3", "name": "Mapping data1", "type": "n8n-nodes-base.code", "position": [260, 880], "parameters": {"jsCode": "return [\n    {\n        \"country\": \"Wakanda\",\n        \"capital\": \"Birnin Zana\"\n    },\n    {\n        \"country\": \"Narnia\",\n        \"capital\": \"Cair Paravel\"\n    },\n    {\n        \"country\": \"Gondor\",\n        \"capital\": \"Minas Tirith\"\n    },\n    {\n        \"country\": \"Oz\",\n        \"capital\": \"The Emerald City\"\n    },\n    {\n        \"country\": \"Westeros\",\n        \"capital\": \"King's Landing\"\n    },\n    {\n        \"country\": \"Panem\",\n        \"capital\": \"The Capitol\"\n    },\n    {\n        \"country\": \"Ruritania\",\n        \"capital\": \"Strelsau\"\n    },\n    {\n        \"country\": \"Mordor\",\n        \"capital\": \"Barad-dûr\"\n    },\n    {\n        \"country\": \"Latveria\",\n        \"capital\": \"Doomstadt\"\n    },\n    {\n        \"country\": \"Atlantis\",\n        \"capital\": \"Poseidonis\"\n    }\n]\n"}, "typeVersion": 2}, {"id": "5ed65e2c-b56d-49d9-a205-1e4cc8914fa9", "name": "Get the matching country's details", "type": "n8n-nodes-base.merge", "position": [460, 820], "parameters": {"mode": "combine", "options": {}, "advanced": true, "joinMode": "enrichInput1", "mergeByFields": {"values": [{"field1": "query", "field2": "country"}]}}, "typeVersion": 3}, {"id": "313775a0-a4ce-488e-a7db-b1ddd49dc3cd", "name": "Return specific capital", "type": "n8n-nodes-base.set", "position": [660, 820], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "03ac1883-126f-4419-93e4-c5062b2d766d", "name": "response", "type": "string", "value": "={{ $ifEmpty($json.capital, 'Capital not found') }}"}]}}, "typeVersion": 3.4}], "pinData": {}, "connections": {"Mapping data": {"main": [[{"node": "Concatenate country names", "type": "main", "index": 0}]]}, "Mapping data1": {"main": [[{"node": "Get the matching country's details", "type": "main", "index": 1}]]}, "Simple Memory": {"ai_memory": [[{"node": "OpenAI", "type": "ai_memory", "index": 0}]]}, "List countries?": {"main": [[{"node": "Mapping data", "type": "main", "index": 0}], [{"node": "Get the matching country's details", "type": "main", "index": 0}, {"node": "Mapping data1", "type": "main", "index": 0}]]}, "Concatenate country names": {"main": [[{"node": "Return country list", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}, "Tool to call the workflow below": {"ai_tool": [[{"node": "OpenAI", "type": "ai_tool", "index": 0}]]}, "Tool: Get current date and time": {"ai_tool": [[{"node": "OpenAI", "type": "ai_tool", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "List countries?", "type": "main", "index": 0}]]}, "Get the matching country's details": {"main": [[{"node": "Return specific capital", "type": "main", "index": 0}]]}}}