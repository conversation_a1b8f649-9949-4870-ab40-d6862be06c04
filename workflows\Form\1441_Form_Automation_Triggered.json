{"id": "B37wvB0tdKgjuabw", "meta": {"instanceId": "98bf0d6aef1dd8b7a752798121440fb171bf7686b95727fd617f43452393daa3", "templateCredsSetupCompleted": true}, "name": "Image to license plate number", "tags": [], "nodes": [{"id": "a656334a-0135-4d93-a6df-ca97222c9753", "name": "Basic LLM Chain", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [-140, -380], "parameters": {"text": "={{ $json.prompt }}", "messages": {"messageValues": [{"type": "HumanMessagePromptTemplate", "messageType": "imageBinary", "binaryImageDataKey": "Image"}]}, "promptType": "define"}, "typeVersion": 1.5}, {"id": "41a90592-2a91-40ff-abf4-3a795733d521", "name": "FormResultPage", "type": "n8n-nodes-base.form", "position": [220, -380], "webhookId": "218822fe-5eb9-4451-ae8a-14b8f484fdde", "parameters": {"options": {"formTitle": ""}, "operation": "completion", "completionTitle": "Extracted information:", "completionMessage": "={{ $json.text }}"}, "typeVersion": 1}, {"id": "c23b95d9-b7a2-4e9e-a019-5724a9662abd", "name": "OpenRouter LLM", "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "position": [-60, -180], "parameters": {"model": "={{ $json.model }}", "options": {}}, "credentials": {"openRouterApi": {"id": "bs7tPtvgDTJNGAFJ", "name": "OpenRouter account"}}, "typeVersion": 1}, {"id": "8298cd51-8c47-4bc4-af78-2c216207ef76", "name": "Settings", "type": "n8n-nodes-base.set", "position": [-340, -380], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "1b8381dc-5b9a-42a2-8a67-cc706b433180", "name": "model", "type": "string", "value": "openai/gpt-4o"}, {"id": "72aec130-ab56-4e61-b60b-9a31dd8d02e6", "name": "prompt", "type": "string", "value": "Extract the number of the license plate on the front-most car depicted in the attached image and return only the extracted characters without any other text or structure."}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "fae79fc9-b510-44a4-beec-4dc26dc2a13a", "name": "FromTrigger", "type": "n8n-nodes-base.formTrigger", "position": [-560, -380], "webhookId": "41e3f34b-7abe-4c64-95cd-2942503d5e98", "parameters": {"options": {}, "formTitle": "Analyse image", "formFields": {"values": [{"fieldType": "file", "fieldLabel": "Image", "requiredField": true, "acceptFileTypes": ".jpg, .png"}]}, "responseMode": "lastNode", "formDescription": "To analyse an image, upload it here."}, "typeVersion": 2.2}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "5b9c53b9-3998-4676-999d-1ba117bf6695", "connections": {"Settings": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "FromTrigger": {"main": [[{"node": "Settings", "type": "main", "index": 0}]]}, "OpenRouter LLM": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "Basic LLM Chain": {"main": [[{"node": "FormResultPage", "type": "main", "index": 0}]]}}}