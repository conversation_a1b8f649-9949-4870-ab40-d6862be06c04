{"meta": {"instanceId": "1dd912a1610cd0376bae7bb8f1b5838d2b601f42ac66a48e012166bb954fed5a", "templateId": "2297", "templateCredsSetupCompleted": true}, "nodes": [{"id": "41ce128f-e9e5-478f-8954-c94019884721", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-160, 240], "parameters": {}, "typeVersion": 1}, {"id": "3a3b3212-2eb3-411e-981e-37bd3f3e46fe", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [680, 240], "parameters": {"url": "https://v2.convertapi.com/convert/docx/to/pdf", "method": "POST", "options": {"response": {"response": {"responseFormat": "file"}}}, "sendBody": true, "contentType": "multipart-form-data", "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "file", "value": "={{ $json.url_to_file }}"}]}, "genericAuthType": "httpQueryAuth", "headerParameters": {"parameters": [{"name": "Accept", "value": "application/octet-stream"}]}}, "credentials": {"httpQueryAuth": {"id": "WdAklDMod8fBEMRk", "name": "Query Auth account"}}, "typeVersion": 4.2}, {"id": "987ec4b3-3241-4cb6-b735-04754ead8ef8", "name": "Read/Write Files from Disk", "type": "n8n-nodes-base.readWriteFile", "position": [1000, 240], "parameters": {"options": {}, "fileName": "document.pdf", "operation": "write", "dataPropertyName": "=data"}, "typeVersion": 1}, {"id": "d99ed058-ab0c-4310-8e75-3d4b073c234b", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [540, 40], "parameters": {"width": 372, "height": 383, "content": "## Authentication\nConversion requests must be authenticated. Please create \n[ConvertAPI account to get authentication secret](https://www.convertapi.com/a/signin)\n\nCreate a query auth credential with `secret` as name and your secret from the convertAPI dashboard as value"}, "typeVersion": 1}, {"id": "3e4f5f45-36c8-4a71-b053-6b5beafa3025", "name": "Config", "type": "n8n-nodes-base.set", "position": [220, 240], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "********-5709-49d4-9c01-27dd5eeba879", "name": "url_to_file", "type": "string", "value": "https://cdn.convertapi.com/cara/testfiles/document.docx"}]}}, "typeVersion": 3.3}, {"id": "895324aa-e373-4049-8b4b-aefed7a61239", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [100, 40], "parameters": {"width": 353, "height": 375, "content": "## Configuration \nChange the `url_to_file` parameter here to the file you want to convert"}, "typeVersion": 1}], "pinData": {}, "connections": {"Config": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Read/Write Files from Disk", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Config", "type": "main", "index": 0}]]}}}