{"meta": {"instanceId": "8c8c5237b8e37b006a7adce87f4369350c58e41f3ca9de16196d3197f69eabcd"}, "nodes": [{"id": "1e603735-dd86-4691-8ece-c81fff396161", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [370, 250], "webhookId": "484b94c9-8285-4ec9-aa52-f5a41eb84d1a", "parameters": {"path": "timersyncro", "options": {}, "httpMethod": "POST", "responseData": "allEntries", "responseMode": "lastNode"}, "typeVersion": 1}, {"id": "2b243a13-a258-4198-9cad-057c6117b50a", "name": "EnvVariables", "type": "n8n-nodes-base.set", "position": [570, 250], "parameters": {"values": {"string": [{"name": "syncro_baseurl", "value": "https://subdomain.syncromsp.com"}]}, "options": {}}, "typeVersion": 1}, {"id": "0108d71b-ae26-4e64-9a52-9b6de15c4fbd", "name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [750, 250], "parameters": {"range": "A:B", "options": {}, "sheetId": "xxx", "operation": "lookup", "lookupValue": "={{$node[\"Webhook\"].json[\"body\"][\"call_id\"]}}", "lookupColumn": "Call"}, "credentials": {"googleApi": {"id": null, "name": "Google"}}, "typeVersion": 1}, {"id": "6747ff1c-f7f0-48a2-9aa2-fd1c72401233", "name": "ConfirmMatch", "type": "n8n-nodes-base.if", "position": [900, 250], "parameters": {"conditions": {"number": [], "string": [{"value1": "={{$node[\"Google Sheets\"].json[\"Ticket\"]}}", "operation": "isEmpty"}], "boolean": []}}, "typeVersion": 1}, {"id": "207192d8-f8f4-4f23-af61-91e254cbeee9", "name": "NoOp", "type": "n8n-nodes-base.noOp", "position": [1060, 100], "parameters": {}, "typeVersion": 1}, {"id": "7cd7ba20-951d-4654-82b5-2e8081774723", "name": "AddTimertoSyncro", "type": "n8n-nodes-base.httpRequest", "position": [1080, 420], "parameters": {"url": "={{$node[\"EnvVariables\"].parameter[\"values\"][\"string\"][0][\"value\"]}}/api/v1/tickets/{{$node[\"Google Sheets\"].json[\"Ticket\"]}}/timer_entry", "method": "POST", "options": {}, "sendBody": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "start_at", "value": "={{new Date(parseInt($node[\"Webhook\"].json[\"body\"][\"date_started\"])).toISOString()}}"}, {"name": "end_at", "value": "={{new Date(parseInt($node[\"Webhook\"].json[\"body\"][\"date_ended\"])).toISOString()}}"}, {"name": "notes", "value": "=Phone call from {{$node[\"Webhook\"].json[\"body\"][\"contact\"][\"name\"]}} ({{$node[\"Webhook\"].json[\"body\"][\"contact\"][\"phone\"]}})."}, {"name": "user_id", "value": "24223"}]}, "genericAuthType": "httpHeaderAuth"}, "typeVersion": 3}], "connections": {"Webhook": {"main": [[{"node": "EnvVariables", "type": "main", "index": 0}]]}, "ConfirmMatch": {"main": [[{"node": "NoOp", "type": "main", "index": 0}], [{"node": "AddTimertoSyncro", "type": "main", "index": 0}]]}, "EnvVariables": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}, "Google Sheets": {"main": [[{"node": "ConfirmMatch", "type": "main", "index": 0}]]}}}