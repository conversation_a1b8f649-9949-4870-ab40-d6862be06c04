{"meta": {"instanceId": "78ab5e476ecaa1f377d804637c3e86d3fd449c31126b69159de63d266513b694"}, "nodes": [{"id": "d46a710d-0d0e-4040-b2b2-a2bd2e2410ff", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.errorTrigger", "position": [440, 520], "parameters": {}, "typeVersion": 1}, {"id": "2e3a9cf6-9a9f-4f11-ab53-e3fa9c393e1f", "name": "n8n", "type": "n8n-nodes-base.n8n", "position": [900, 180], "parameters": {"filters": {}, "requestOptions": {}}, "credentials": {"n8nApi": {"id": "27", "name": "n8n account"}}, "typeVersion": 1}, {"id": "7fc93f47-24ee-4000-ac3f-eb2746a926bb", "name": "Gmail", "type": "n8n-nodes-base.gmail", "position": [660, 520], "parameters": {"sendTo": "=(your email address)", "message": "={{ $json.execution.url }}", "options": {}, "subject": "=[n8n] workflow failed:  {{ $json.workflow.name }}"}, "credentials": {"gmailOAuth2": {"id": "3", "name": "gmail <EMAIL>"}}, "typeVersion": 2.1}, {"id": "25ed8ec8-2c28-498a-a951-c5ef1b2a2c59", "name": "get error handler", "type": "n8n-nodes-base.n8n", "position": [660, 180], "parameters": {"operation": "get", "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "requestOptions": {}}, "credentials": {"n8nApi": {"id": "27", "name": "n8n account"}}, "typeVersion": 1}, {"id": "44713be9-786a-4bff-b562-a23146792995", "name": "n8n | update", "type": "n8n-nodes-base.n8n", "position": [1500, 180], "parameters": {"operation": "update", "workflowId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "requestOptions": {}, "workflowObject": "={{ JSON.stringify($json) }}"}, "credentials": {"n8nApi": {"id": "27", "name": "n8n account"}}, "typeVersion": 1}, {"id": "be27247a-71e5-4204-9c7c-2692d8a82c8b", "name": "set fields", "type": "n8n-nodes-base.code", "position": [1300, 180], "parameters": {"mode": "runOnceForEachItem", "jsCode": "const data = $json\n\ndata.settings.errorWorkflow = $('get error handler').item.json.id ;\ndelete data.settings.callerPolicy;\n\nreturn {\n  id: data.id,\n  name: data.name,\n  nodes: data.nodes,\n  connections: data.connections,\n  settings: data.settings\n}"}, "typeVersion": 2}, {"id": "d8774911-f4b2-4198-838b-2d0b89002e25", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [380, 400], "parameters": {"width": 483.*************, "height": 308.**************, "content": "## Default <PERSON><PERSON><PERSON>\n\nUpdate this to your preferred notification mechanism"}, "typeVersion": 1}, {"id": "0baa0fc3-4d5e-4507-bd5d-65ebce68178f", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [605.*************, 126.**************], "parameters": {"width": 232.91556831986873, "height": 216.67545344104974, "content": "get ID of self"}, "typeVersion": 1}, {"id": "fabb0db7-7364-4349-8563-952c9f0e07b2", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [440, 180], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.2}, {"id": "dd1e0036-1093-4160-adad-ed1b0c1b3548", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [380, 125.83113663973751], "parameters": {"width": 214.6984582852457, "height": 219.7116384468202, "content": "Runs every day at midnight to update new workflows"}, "typeVersion": 1}, {"id": "aca838c8-ff3e-4630-824b-a6d1d8414326", "name": "active && no error handler set && not this  handler workflow", "type": "n8n-nodes-base.if", "position": [1100, 180], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "290fd302-4e2d-44d6-8a8a-14a0b8f2c360", "operator": {"type": "string", "operation": "notExists", "singleValue": true}, "leftValue": "={{ $json.settings.errorWorkflow }}", "rightValue": "=Default <PERSON><PERSON><PERSON>"}, {"id": "2a5799e9-2030-4281-bf11-e7f9777906c5", "operator": {"type": "string", "operation": "notEquals"}, "leftValue": "={{ $json.id }}", "rightValue": "={{ $('get error handler').item.json.id }}"}, {"id": "8bc4c2a0-e094-4426-8ae6-71b6e4fa9842", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.active }}", "rightValue": ""}]}}, "typeVersion": 2}], "pinData": {}, "connections": {"n8n": {"main": [[{"node": "active && no error handler set && not this  handler workflow", "type": "main", "index": 0}]]}, "set fields": {"main": [[{"node": "n8n | update", "type": "main", "index": 0}]]}, "Error Trigger": {"main": [[{"node": "Gmail", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "get error handler", "type": "main", "index": 0}]]}, "get error handler": {"main": [[{"node": "n8n", "type": "main", "index": 0}]]}, "active && no error handler set && not this  handler workflow": {"main": [[{"node": "set fields", "type": "main", "index": 0}]]}}}