{"id": "178", "name": "Create a channel, add a member, and post a message to the channel", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [270, 340], "parameters": {}, "typeVersion": 1}, {"name": "Mattermost", "type": "n8n-nodes-base.mattermost", "position": [470, 340], "parameters": {"teamId": "4zhpirmh97fn7jgp7qhyue5a6e", "channel": "docs", "resource": "channel", "displayName": "Docs"}, "credentials": {"mattermostApi": "Mattermost Credentials"}, "typeVersion": 1}, {"name": "Mattermost1", "type": "n8n-nodes-base.mattermost", "position": [670, 340], "parameters": {"userId": "5oiy71hukjgd9eprj1o4a3poio", "resource": "channel", "channelId": "={{$node[\"Mattermost\"].json[\"id\"]}}", "operation": "addUser"}, "credentials": {"mattermostApi": "Mattermost Credentials"}, "typeVersion": 1}, {"name": "Mattermost2", "type": "n8n-nodes-base.mattermost", "position": [870, 340], "parameters": {"message": "Hey! Welcome to the channel!", "channelId": "={{$node[\"Mattermost\"].json[\"id\"]}}", "attachments": [], "otherOptions": {}}, "credentials": {"mattermostApi": "Mattermost Credentials"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Mattermost": {"main": [[{"node": "Mattermost1", "type": "main", "index": 0}]]}, "Mattermost1": {"main": [[{"node": "Mattermost2", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Mattermost", "type": "main", "index": 0}]]}}}