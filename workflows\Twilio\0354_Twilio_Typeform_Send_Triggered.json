{"id": 1, "name": "Send Typeforms leads via Whatsapp (Twilio)", "nodes": [{"name": "Typeform Trigger", "type": "n8n-nodes-base.typeformTrigger", "position": [460, 300], "webhookId": "a3c4dab3-6550-4e82-906f-db7f73ab35a5", "parameters": {"formId": "agRe2poK", "onlyAnswers": false, "authentication": "oAuth2"}, "credentials": {"typeformOAuth2Api": {"id": "2", "name": "Typeform account"}}, "retryOnFail": true, "typeVersion": 1}, {"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.twilio", "position": [900, 300], "parameters": {"to": "+***********", "from": "+***********", "message": "=Hello, Here is a new customer who is looking for a Test : \n\n{{$json[\"Data\"]}}\n\nRegards, HelloSafe"}, "credentials": {"twilioApi": {"id": "1", "name": "Twilio account"}}, "retryOnFail": true, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [680, 300], "parameters": {"values": {"string": [{"name": "Data", "value": "=Last name : {{$node[\"Typeform Trigger\"].json[\"form_response\"][\"answers\"][\"And your *last name*?\"]}}\nFirst name :{{$node[\"Typeform Trigger\"].json[\"form_response\"][\"answers\"][\"Let's start with your* first name.*\"]}}\nNumber of child : {{$node[\"Typeform Trigger\"].json[\"form_response\"][\"answers\"][\"How many child do you have ?\"]}}\nCountry : {{$node[\"Typeform Trigger\"].json[\"form_response\"][\"answers\"][\"Lastly, [field:d566770d2197a78b], what country do you live in?\"]}}\nMail adress : {{$node[\"Typeform Trigger\"].json[\"form_response\"][\"answers\"][\"What *email address* can we reach you at? This is only to get in touch, not to send spam.\"]}}\nBirth date : {{$node[\"Typeform Trigger\"].json[\"form_response\"][\"answers\"][\"What is your birth date ?\"]}}"}]}, "options": {}}, "typeVersion": 1}], "active": true, "settings": {}, "connections": {"Set": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Twilio": {"main": [[]]}, "Typeform Trigger": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}}