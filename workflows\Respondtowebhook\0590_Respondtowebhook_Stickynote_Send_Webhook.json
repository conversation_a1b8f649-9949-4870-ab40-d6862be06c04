{"meta": {"instanceId": "84ba6d895254e080ac2b4916d987aa66b000f88d4d919a6b9c76848f9b8a7616", "templateId": "2446"}, "nodes": [{"id": "af0765f4-75b5-445c-80d7-51b0aa180fe5", "name": "OpenAI Chat Model2", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [820, 620], "parameters": {"model": "gpt-4o", "options": {"temperature": 0, "responseFormat": "text"}}, "typeVersion": 1}, {"id": "497c534e-e117-4592-b76f-bef424a7fd5a", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [1500, 400], "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "5b358850-cbc3-4a8c-b2b8-12e3b7aa1e44", "name": "calendarAgent", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [1060, 620], "parameters": {"name": "calendarAgent", "fields": {"values": [{"name": "sessionId", "stringValue": "={{ $json.sessionId }}"}, {"name": "prompt", "stringValue": "={{ $json.chatInput }}"}]}, "workflowId": {"__rl": true, "mode": "list", "value": "yPCMz4zxB291oM31", "cachedResultName": "Google Calendar Agent"}, "description": "Call this workflow to do handle every request regarding calendar management.", "responsePropertyName": "output"}, "typeVersion": 1.2}, {"id": "8bcc4b27-59b9-4ce3-8525-34221c10f11a", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [460, 480], "webhookId": "96e410fe-ef91-4767-aa9a-bf95ba50f972", "parameters": {"public": true, "options": {}}, "typeVersion": 1.1}, {"id": "0aa8e0ff-7ed3-4fef-9b7c-f2caa8f85612", "name": "taskAgent", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [1180, 620], "parameters": {"name": "taskAgent", "fields": {"values": [{"name": "sessionId", "stringValue": "={{ $json.sessionId }}"}, {"name": "prompt", "stringValue": "={{ $json.chatInput }}"}]}, "workflowId": {"__rl": true, "mode": "list", "value": "ICTXOidW1oyJDYP7", "cachedResultName": "Notion Task Agent"}, "description": "Call this workflow to do handle every request regarding task management.", "responsePropertyName": "output"}, "typeVersion": 1.2}, {"id": "b46f4ed0-6de6-44ab-8b91-521b011d7869", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [940, 620], "parameters": {"sessionKey": "={{ $json.sessionId }}", "sessionIdType": "customKey", "contextWindowLength": 15}, "typeVersion": 1.2}, {"id": "e778c2bf-1681-418d-a434-d1a0cdeaa5d7", "name": "Map Fields", "type": "n8n-nodes-base.set", "position": [680, 320], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "f8c5a03f-ea21-4877-a71b-32e8b4dd30fb", "name": "chatInput", "type": "string", "value": "={{ $json.body.prompt }}"}, {"id": "3d4fecc4-78a5-47ba-a239-5fdc9b224d82", "name": "sessionId", "type": "string", "value": "={{ $json.body.sessionID }}"}]}}, "typeVersion": 3.4}, {"id": "c54d0fab-b25c-48fc-b027-dcdf78dd2b09", "name": "Map Fields1", "type": "n8n-nodes-base.set", "position": [680, 480], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "36f24729-17ae-4d69-961f-424a1797b42c", "name": "chatInput", "type": "string", "value": "={{ $json.chatInput }}"}, {"id": "05ea359a-d82e-4917-9245-38016314ad10", "name": "sessionId", "type": "string", "value": "={{ $json.sessionId }}"}]}}, "typeVersion": 3.4}, {"id": "cefe6cc8-4a87-47c8-a518-c0bf06f96a2a", "name": "Exclude Previews from Speech", "type": "n8n-nodes-base.set", "position": [1280, 400], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "424b3c35-fd3d-4021-86e7-0d90529550b0", "name": "response.text", "type": "string", "value": "={{ $json.output }}"}, {"id": "0cbe6fd9-3464-4bd1-b9c0-365548dc232a", "name": "response.speech", "type": "string", "value": "={{ $if($json.output.search(/>\\s/) > -1, $json.output.substring(0, $json.output.search(/>\\s/)), $json.output) }}"}]}}, "typeVersion": 3.4}, {"id": "815eb1a4-ef2d-430d-8884-************", "name": "Main Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [900, 400], "parameters": {"text": "={{ $json.chatInput }}", "options": {"systemMessage": "=# Role:\nYou are a helpful assistant. Your sole responsibility is to determine which tool to forward the original chat input to. Do not process, modify, or interpret the input or output in any way. Only route it to the correct tool.\n\n# Behavior:\nBe clear, very concise, and accurate in tool routing. Do not modify, interpret, or analyze the incoming input or the tool's response. If the request is ambiguous, ask for clarification regarding tool selection only.\n\n# Command:\nRoute all incoming requests to the available tools if they match their description.\nCheck the memory to route ongoing conversations correctly — only choose another tool if a new task has been requested or the context clearly has been switched. If the context has changed (e.g. you were asked to create a task before, but now the user asks to create an event), forget everything before the context switch.\n\nOnly call one tool at a time.\n\nDo not modify or alter the input before sending it to the tool or the output after receiving it from the tool. Simply pass through both input and output as they are.\n\n# Format:\nPass every response of each tool in raw format to the output. Do not modify, interpret, or add any information at all."}, "promptType": "define"}, "typeVersion": 1.6}, {"id": "07b6d7e2-ab73-4f23-8dca-7c8b0309574c", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1520, 1100], "parameters": {"model": "gpt-4o", "options": {"temperature": 0}}, "typeVersion": 1}, {"id": "882a93d8-886e-465d-9c81-cc8069abd281", "name": "HTTP Request", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1760, 1100], "parameters": {"url": "https://api.notion.com/v1/pages", "method": "POST", "jsonBody": "={\n  \"parent\": {\n    \"database_id\": \"{{ $json.databaseID }}\"\n  },\n  \"properties\": {\n    \"Name\": {\n      \"title\": [\n        {\n          \"text\": {\n            \"content\": \"{title}\"\n          }\n        }\n      ]\n    },\n    \"Priority\": {\n      \"select\": {\n        \"name\": \"{priority}\"\n      }\n    }\n  },\n  \"children\": [\n    {\n      \"object\": \"block\",\n      \"type\": \"paragraph\",\n      \"paragraph\": {\n        \"rich_text\": [\n          {\n            \"type\": \"text\",\n            \"text\": {\n              \"content\": \"{description}\"\n            }\n          }\n        ]\n      }\n    }\n  ]\n}", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "toolDescription": "Create a Task in Notion using a title, description and priority.", "nodeCredentialType": "notionApi", "placeholderDefinitions": {"values": [{"name": "title", "type": "string", "description": "The name / title of the task."}, {"name": "description", "type": "string", "description": "The description of the task."}, {"name": "priority", "type": "string", "description": "The priority of the task. One of these values: \"do first\", \"important\", \"urgent\""}]}}, "typeVersion": 1.1}, {"id": "e19aad2c-132e-454f-a091-334f128b0636", "name": "Settings", "type": "n8n-nodes-base.set", "position": [1320, 880], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "3f65df4c-fae1-4da3-acfd-acf352a3f8d2", "name": "sessionId", "type": "string", "value": "={{ $json.sessionId }}"}, {"id": "9745bdbd-fd97-46db-a742-6540f86dd43c", "name": "chatInput", "type": "string", "value": "={{ $json.prompt }}"}, {"id": "5e757768-d780-4b11-a6e0-593b08f32cc3", "name": "databaseID", "type": "string", "value": "92da2aa018ed4095afc0f1a0670f36e9"}]}}, "typeVersion": 3.4}, {"id": "0fe6aa57-7a64-40f4-af2d-30f4286b8aee", "name": "Format output", "type": "n8n-nodes-base.set", "position": [1920, 880], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "934c387a-71a5-4549-a68a-312708368117", "name": "output", "type": "string", "value": "=Please respond this to the user without modifications:\n\n{{ $json.output }}"}]}}, "typeVersion": 3.4}, {"id": "b2170997-5ebb-4261-92ce-70b33d68931f", "name": "Notion Task Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1540, 880], "parameters": {"options": {"systemMessage": "=# Role:\nYou are a helpful assistant. Your job is to create Tasks in Notion.\n\n# Behavior:\nBe clear, very concise, efficient, and accurate in responses. If the request is ambiguous, ask for clarification.\n\n# Command:\nYou create tasks in Notion. Each task consists of the mandatory fields title, description and priority. Priority is an enum value consisting of 'do first', 'important' and 'urgent'.\n\n# Ask questions:\nIf required information is missing, ask the user about the missing information and only the missing ones. Ask priority as last.\n\nIf the user only describes the task within a few words, use that as the title. In that case, ask the user, if he wants to add a more detailed description. If he responds with \"No\", leave the description empty when creating the task.\nOn the other hand if the user describes the task more detailed from the beginning, use that as the description and create a short meaningful title for that. \n\nIf you have all the required information, ask for approval, before creating the task. In that case, always return a draft, containing the title, description and priority.\n\n# Format:\nThe output of the draft for approval should always be in markdown and in this format (placeholders in angle brackets):\n\nHere is the drafted task. Shall I create it?\n\n> **<title>**  \n> *<priority>*  \n>  \n> <description (optional)>\n\n# Responses:\nAfter successfully created event, only respond with \"Okay, done.\""}}, "typeVersion": 1.6}, {"id": "a83fdec9-8c0e-45d0-8439-41c23440a21e", "name": "Window Buffer Memory1", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [1640, 1100], "parameters": {"sessionKey": "={{ $json.sessionId }}-{{ $workflow.id }}", "sessionIdType": "customKey", "contextWindowLength": 15}, "typeVersion": 1.2}, {"id": "80ae1a6f-5811-407a-a287-5150b8ecba22", "name": "Get calendar availability", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [820, 1100], "parameters": {"url": "https://www.googleapis.com/calendar/v3/freeBusy", "method": "POST", "jsonBody": "={\n  \"timeMin\": \"{timeMin}\",\n  \"timeMax\": \"{timeMax}\",\n  \"timeZone\": \"{{ $json.timeZone }}\",\n  \"groupExpansionMax\": 20,\n  \"calendarExpansionMax\": 10,\n  \"items\": [\n    {\n      \"id\": \"{{ $json.calendarID }}\"\n    }\n  ]\n}", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "toolDescription": "Call this tool to get the calendar availability for a particular period on the calendar. The tool may refer to availability as \"Free\" or \"Busy\". \n\nUse {timeMin} and {timeMax} to specify the window for the availability query. For example, to get availability for 25 July, 2024 the {timeMin} would be 2024-07-25T00:00:00+02:00 and {timeMax} would be 2024-07-26T00:00:00+02:00.\n\nIf the tool returns an empty response, it means that something went wrong. It does not mean that there is no availability.", "nodeCredentialType": "googleCalendarOAuth2Api"}, "typeVersion": 1}, {"id": "1d44a1eb-1b14-4ce8-b874-673db7be482c", "name": "Book appointment", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [940, 1100], "parameters": {"url": "=https://www.googleapis.com/calendar/v3/calendars/{{ $json.calendarID }}/events", "method": "POST", "jsonBody": "={\n  \"summary\": \"{eventName}\",\n  \"start\": {\n    \"dateTime\": \"{startTime}\",\n    \"timeZone\": \"Europe/Berlin\"\n  },\n  \"end\": {\n    \"dateTime\": \"{endTime}\",\n    \"timeZone\": \"Europe/Berlin\"\n  }\n}", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "toolDescription": "Call this tool to create an event in the calendar.", "nodeCredentialType": "googleCalendarOAuth2Api", "placeholderDefinitions": {"values": [{"name": "eventName", "description": "A short but precise title for the event."}, {"name": "startTime", "description": "The start time of the event in Europe/Berlin timezone. For example, 2024-07-24T10:00:00+02:00"}, {"name": "endTime", "description": "The end time of the event in Europe/Berlin timezone. It should always be 30 minutes after the startTime. "}]}}, "typeVersion": 1}, {"id": "d814abe2-fe6f-43ba-99b7-8380ed78dd26", "name": "Google Calendar Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [660, 880], "parameters": {"options": {"systemMessage": "=# Role:\nYou are a helpful assistant. Your job is to schedule Google Calendar Events.\n\n# Behavior:\nBe clear, very concise, efficient, and accurate in responses. If the request is ambiguous, ask for clarification.\n\n# Command:\nYou create Google Calendar events. Each event requires a title, date and time. The default duration - if not provided by the user - is 1 hour.\n\nBefore creating even showing a draft of the event, use the provided tool to check for the availibilty in the calendar. If there are any conflicts, tell the user about the timespans which are blocked, propose another time slot close by and ask the user if he would like to change the time to that.\n\n# Ask questions:\nIf required information is missing, ask the user about the missing information and only the missing ones.\n\nIf you have all the required information, ask for approval, before creating the event. In that case, always return a draft, containing the title, date and time.\n\n# Format:\nThe output of the draft for approval should always be in markdown and in this format (placeholders in angle brackets):\n\nHere is the event. Shall I create it?\n\n> **<title>**  \n> <date e.g., September 15, 2024>  \n> <time e.g., 10am-12pm>\n\n# Responses:\nAfter successfully created event, only respond with \"You are all set.\"\n\n# Additional Guidelines:\nToday’s date is {{ $now.setZone($json.timeZone).format('dd LLL yyyy') }}.\n"}}, "typeVersion": 1.6}, {"id": "9c4738b9-c7bc-4e90-9dc3-7f99822a19ca", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [580, 1100], "parameters": {"options": {"temperature": 0}}, "typeVersion": 1}, {"id": "40b9b4e0-0e84-43fa-a4bc-a5eb99988cbd", "name": "Settings1", "type": "n8n-nodes-base.set", "position": [440, 880], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "3f65df4c-fae1-4da3-acfd-acf352a3f8d2", "name": "sessionId", "type": "string", "value": "={{ $json.sessionId }}"}, {"id": "9745bdbd-fd97-46db-a742-6540f86dd43c", "name": "chatInput", "type": "string", "value": "={{ $json.prompt }}"}, {"id": "5e757768-d780-4b11-a6e0-593b08f32cc3", "name": "calendarID", "type": "string", "value": "primary"}, {"id": "4085421e-7f2c-429c-a85f-c6170a655823", "name": "timeZone", "type": "string", "value": "Europe/Berlin"}]}}, "typeVersion": 3.4}, {"id": "4bafef7b-3474-42c0-9f21-5c0c02cd9e73", "name": "Format output1", "type": "n8n-nodes-base.set", "position": [1040, 880], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "934c387a-71a5-4549-a68a-312708368117", "name": "output", "type": "string", "value": "=Please respond this to the user without modifications:\n\n{{ $json.output }}"}]}}, "typeVersion": 3.4}, {"id": "6ebcf3cd-8ff2-4605-b4b6-69155918b290", "name": "Window Buffer Memory2", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [700, 1100], "parameters": {"sessionKey": "={{ $json.sessionId }}-{{ $workflow.id }}", "sessionIdType": "customKey", "contextWindowLength": 15}, "typeVersion": 1.2}, {"id": "1269083c-aac7-4a4f-9b3b-bb82a670ff94", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [380, 800], "parameters": {"width": 857.6171119733089, "height": 469.7141529250314, "content": "## Sub-Agent for scheduling calendar events"}, "typeVersion": 1}, {"id": "48df65e9-4628-4b5d-bbad-a8a68289d8b8", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1260, 800], "parameters": {"width": 859.4283058500632, "height": 469.7141529250314, "content": "## Sub-Agent for crating notion tasks\n"}, "typeVersion": 1}, {"id": "48f050ec-3bda-4105-a859-e8f2039abe8e", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [380, 240], "parameters": {"width": 1323.0939382992326, "height": 537.6599060701709, "content": "## Main Agent which is connected to Vagent.io"}, "typeVersion": 1}, {"id": "c768a760-9511-4543-b3ea-f1d83c263098", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1720, 240], "parameters": {"color": 5, "width": 398.5850611544016, "height": 537.9088390659099, "content": "## Setup\n\n### Create workflows\n- Create a separate workflow for each Sub-Agent and move over the nodes from here\n- In each of those workflows add an **Execute Workflow Trigger** node and connect it to the beginning of the copied nodes\n\n### Configure main workflow\n- Select the corresponding credentials\n- Update the Webhook URL and create a Header Auth (Key: Authorization)\n- Update the tools \"claendarAgent\" and \"task Agent\" by choosing the corresponding sub-workflows you just created in the \"Workflow\" dropdown menu\n- Activate the workflow\n\n### Configure sub-workflows\n- Select the corresponding credentials\n- Update the \"Settings\" node (do not touch the first two values), e.g. set your Notion DB ID"}, "typeVersion": 1}, {"id": "42abacd3-02ba-466e-a8d4-f1c2f5c96c00", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [460, 320], "webhookId": "46116445-3b13-48c0-9a38-cd034bee92ac", "parameters": {"path": "46116445-3b13-48c0-9a38-cd034bee92ac", "options": {}, "httpMethod": "POST", "responseMode": "responseNode", "authentication": "headerAuth"}, "typeVersion": 2}], "pinData": {}, "connections": {"Webhook": {"main": [[{"node": "Map Fields", "type": "main", "index": 0}]]}, "Settings": {"main": [[{"node": "Notion Task Agent", "type": "main", "index": 0}]]}, "Settings1": {"main": [[{"node": "Google Calendar Agent", "type": "main", "index": 0}]]}, "taskAgent": {"ai_tool": [[{"node": "Main Agent", "type": "ai_tool", "index": 0}]]}, "Main Agent": {"main": [[{"node": "Exclude Previews from Speech", "type": "main", "index": 0}]]}, "Map Fields": {"main": [[{"node": "Main Agent", "type": "main", "index": 0}]]}, "Map Fields1": {"main": [[{"node": "Main Agent", "type": "main", "index": 0}]]}, "HTTP Request": {"ai_tool": [[{"node": "Notion Task Agent", "type": "ai_tool", "index": 0}]]}, "calendarAgent": {"ai_tool": [[{"node": "Main Agent", "type": "ai_tool", "index": 0}]]}, "Book appointment": {"ai_tool": [[{"node": "Google Calendar Agent", "type": "ai_tool", "index": 0}]]}, "Notion Task Agent": {"main": [[{"node": "Format output", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Notion Task Agent", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Google Calendar Agent", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model2": {"ai_languageModel": [[{"node": "Main Agent", "type": "ai_languageModel", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "Main Agent", "type": "ai_memory", "index": 0}]]}, "Google Calendar Agent": {"main": [[{"node": "Format output1", "type": "main", "index": 0}]]}, "Window Buffer Memory1": {"ai_memory": [[{"node": "Notion Task Agent", "type": "ai_memory", "index": 0}]]}, "Window Buffer Memory2": {"ai_memory": [[{"node": "Google Calendar Agent", "type": "ai_memory", "index": 0}]]}, "Get calendar availability": {"ai_tool": [[{"node": "Google Calendar Agent", "type": "ai_tool", "index": 0}]]}, "When chat message received": {"main": [[{"node": "Map Fields1", "type": "main", "index": 0}]]}, "Exclude Previews from Speech": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}}}