{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "b87cc222-82ec-4b46-9573-68f41d096969", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [640, 620], "parameters": {"color": 7, "width": 740, "height": 680, "content": "## 2. Manually Convert XLSX to Markdown\n[Learn more about the Extract From File node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.extractfromfile/)\n\nToday's LLMs cannot parse Excel files directly so the best we can do is to convert the spreadsheet into a format that they can, namely markdown. This conversion is also a good solution for excels which aren't really datasheets - the cells are used like layout elements - which is still common for invoices and purchase orders.\n\nTo perform the conversion, we can use the 'Extract from File' node to get the each row from the xlsx and then iterate and concatenate to form our markdown table using the code node."}, "typeVersion": 1}, {"id": "c4c55042-02c8-4364-ae7e-d1ec5a75437a", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1400, 620], "parameters": {"color": 7, "width": 640, "height": 680, "content": "## 3. Extract Purchase Order Details using AI\n[Learn more about the Information Extractor](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.information-extractor)\n\nData entry is probably the number one reason as to why we need AI/LLMs. This time consuming and menial task can be completed in seconds and with a high degree of accuracy. Here, we ask the AI to extract each event with the term dates to a list of events using structured output."}, "typeVersion": 1}, {"id": "b9530f93-464b-4116-add7-da218fe8eb12", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-700, -80], "parameters": {"width": 460, "height": 1400, "content": "## Try it out!\n### This n8n template imports purchase order submissions from Outlook and converts attached purchase order form in XLSX format into structured output.\n\nData entry jobs with user-submitted XLSX forms is a time consuming, incredibly mundane but necessary tasks which in likelihood are inherited and critical to business operation.\n\nWhile we could dream of system overhauls and modernisation, the fact is that change is hard. There is another way however -  using n8n and AI!\n\n### How it works\n* An Outlook trigger is used to watch for incoming purchase order forms submitted via a shared inbox.\n* The email attachment for the submission is a form in xlsx format - like this one https://1drv.ms/x/c/8f1f7dda12b7a145/ETWH8dKwgZ1OiVz7ISUWYf8BwiyihBjXPXEbCYkVi8XDyw?e=WWU2eR - which is imported into the workflow.\n* The 'Extract from File' node is used with the 'code' node to convert the xlsx file to markdown. This is so our LLM can understand it.\n* The Information Extractor node is used to read and extract the relevant purchase order details and line items from the form.\n* A simple validation step is used to check for common errors such as missing PO number or the amounts not matching up. A notification is automated to reply to the buyer if so.\n* Once validation passes, a confirmation is sent to the buyer and the purchase order structured output can be sent along to internal systems.\n\n### How to use\n* This template only works if you're expecting and receiving forms in XLSX format. These can be invoices, request forms as well as purchase order forms.\n* Update the Outlook nodes with your email or other emails as required.\n* What's next? I've omitted the last steps to send to an ERP or accounting system as this is dependent on your org.\n\n### Requirements\n* Outlook for Emails\n  * Check out how to setup credentials here: https://docs.n8n.io/integrations/builtin/credentials/microsoft/\n* OpenAI for LLM document understanding and extraction.\n\n### Customising the workflow\n* This template should work for other Excel files. Some will be more complicated than others so experiment with different parsers and extraction tools and strategies.\n* Customise the Information Extractor Schema to pull out the specific data you need. For example, capture any notes or comments given by the buyer.\n\n### Need Help?\nJoin the [Discord](https://discord.com/invite/XPKeKXeB7d) or ask in the [Forum](https://community.n8n.io/)!\n\nHappy Hacking!"}, "typeVersion": 1}, {"id": "f5a2d1e7-f73b-4bfa-8e02-f30db275bbcc", "name": "Extract Purchase Order Details", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [1500, 920], "parameters": {"text": "={{ $json.table }}", "options": {"systemPromptTemplate": "Capture the values as seen. Do not convert dates."}, "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"purchase_order_number\": { \"type\": \"string\" },\n    \"purchase_order_date\": { \"type\": \"string\" },\n    \"purchase_order_total\": { \"type\": \"number\" },\n    \"vendor_name\": { \"type\": \"string\" },\n    \"vendor_address\": { \"type\": \"string\" },\n    \"vendor_contact\": { \"type\": \"string\" },\n    \"delivery_contact\": { \"type\": \"string\" },\n    \"delivery_address\": { \"type\": \"string\" },\n    \"delivery_method\": { \"type\": \"string\" },\n    \"items\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"description\": { \"type\": \"string\" },\n          \"part_number\": { \"type\": \"string\" },\n          \"quantity\": { \"type\": \"number\" },\n          \"unit\": { \"type\": \"number\" },\n          \"unit_price\": { \"type\": \"number\" }\n        }\n      }\n    }\n  }\n}"}, "typeVersion": 1}, {"id": "0ce545f0-8147-4ad2-bb9e-14ef0b0c26ef", "name": "Is Excel Document?", "type": "n8n-nodes-base.if", "position": [760, 1020], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "f723ab0a-8f2d-4501-8273-fd6455c57cdd", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $binary.data.mimeType }}", "rightValue": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}]}}, "typeVersion": 2.2}, {"id": "ccbd9531-66be-4e07-8b73-faf996622f9f", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-220, 460], "parameters": {"color": 5, "width": 340, "height": 140, "content": "### PURCHASE ORDER EXAMPLE\nThis is the purchase order XLSX which is used an example for this template.\nhttps://1drv.ms/x/c/8f1f7dda12b7a145/ETWH8dKwgZ1OiVz7ISUWYf8BwiyihBjXPXEbCYkVi8XDyw?e=WWU2eR"}, "typeVersion": 1}, {"id": "ef8b00eb-dba6-47dd-a825-1aa5c85ee215", "name": "Run Checks", "type": "n8n-nodes-base.set", "position": [2160, 940], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "049c7aca-7663-4eed-93b4-9eec3760c058", "name": "has_po_number", "type": "boolean", "value": "={{ Boolean($json.output.purchase_order_number) }}"}, {"id": "94d2224a-cf81-4a42-acd0-de5276a5e493", "name": "has_valid_po_date", "type": "boolean", "value": "={{ $json.output.purchase_order_date.toDateTime() < $now.plus({ 'day': 1 }) }}"}, {"id": "a8f69605-dad6-4ec2-a22f-d13ff99e27cd", "name": "has_items", "type": "boolean", "value": "={{ $json.output.items.length > 0 }}"}, {"id": "c11db99e-9cc2-40b7-b3a5-f3c65f88dc13", "name": "is_math_correct", "type": "boolean", "value": "={{\n$json.output.items.map(item => item.unit_price * item.quantity).sum().round(2) === $json.output.purchase_order_total.round(2) }}"}]}}, "typeVersion": 3.4}, {"id": "801848cc-558c-4a30-aab5-eb403564b68f", "name": "Is Valid Purchase Order?", "type": "n8n-nodes-base.if", "position": [2360, 940], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "11fa8087-7809-4bc9-9fbe-32bfd35821a6", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.has_po_number }}", "rightValue": ""}, {"id": "c45ae85a-e060-4416-aa2c-daf58db8ba0e", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.has_valid_po_date }}", "rightValue": ""}, {"id": "d0ae9518-2f4b-43fb-87b1-7108a6a75424", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.has_items }}", "rightValue": ""}, {"id": "eed09f78-ce1a-4e09-8940-febcf7e41078", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.is_math_correct }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "7c7dd7a0-45fe-4549-8341-3b3fd18e1725", "name": "Extract from File", "type": "n8n-nodes-base.extractFromFile", "position": [980, 920], "parameters": {"options": {"rawData": true, "headerRow": false, "includeEmptyCells": true}, "operation": "xlsx"}, "typeVersion": 1}, {"id": "dfb6b00f-fe50-42d6-8597-8fdcb562714b", "name": "XLSX to Markdown Table", "type": "n8n-nodes-base.code", "position": [1180, 920], "parameters": {"jsCode": "const rows = $input.all().map(item => item.json.row);\nconst maxLength = Math.max(...rows.map(row => row.length));\n\nconst table = [\n  '|' + rows[0].join('|') + '|',\n  '|' + Array(maxLength).fill(0).map(_ => '-').join('|') + '|',\n  rows.slice(1, rows.length)\n    .filter(row => row.some(Boolean))\n    .map(row =>\n      '|' + row.join('|') + '|'\n    ).join('\\n')\n].join('\\n')\n\nreturn { table }"}, "typeVersion": 2}, {"id": "1a3de516-1d21-4664-b2e3-8c8d6ec90ef2", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1600, 1080], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "1a29236f-5eaa-4a38-a0a1-6e19abd77d2c", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [2060, 620], "parameters": {"color": 7, "width": 940, "height": 680, "content": "## 4. Use Simple Validation to Save Time and Effort\n[Learn more about the Edit Fields node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.set)\n\nWith our extracted output, we can run simple validation checks to save on admin time. Common errors such as missing purchase order numbers or miscalculated cost amounts are easy to detect and a quick response can be given. Once validation passes, it's up to you how you use the extracted output next."}, "typeVersion": 1}, {"id": "79a39a03-5f71-4021-bcfd-06edbc285e8a", "name": "Reply Invalid Format", "type": "n8n-nodes-base.microsoftOutlook", "position": [980, 1120], "webhookId": "9464583e-9505-49ec-865e-58aa1ab3c2ed", "parameters": {"message": "PO rejected due to invalid file format. Please try again with XLSX.", "options": {}, "messageId": {"__rl": true, "mode": "id", "value": "={{ $('Outlook Trigger').first().json.id }}"}, "operation": "reply", "additionalFields": {}, "replyToSenderOnly": true}, "credentials": {"microsoftOutlookOAuth2Api": {"id": "EWg6sbhPKcM5y3Mr", "name": "Microsoft Outlook account"}}, "typeVersion": 2}, {"id": "ec973438-4d6c-4d2e-8702-1d195f514528", "name": "Outlook Trigger", "type": "n8n-nodes-base.microsoftOutlookTrigger", "position": [-120, 920], "parameters": {"fields": ["body", "categories", "conversationId", "from", "hasAttachments", "internetMessageId", "sender", "subject", "toRecipients", "receivedDateTime", "webLink"], "output": "fields", "filters": {"hasAttachments": true, "foldersToInclude": []}, "options": {"downloadAttachments": true}, "pollTimes": {"item": [{"mode": "everyHour"}]}}, "credentials": {"microsoftOutlookOAuth2Api": {"id": "EWg6sbhPKcM5y3Mr", "name": "Microsoft Outlook account"}}, "typeVersion": 1}, {"id": "fcb173ce-7dad-497a-9376-9650c2a24a84", "name": "Reply Rejection", "type": "n8n-nodes-base.microsoftOutlook", "position": [2580, 1040], "webhookId": "9464583e-9505-49ec-865e-58aa1ab3c2ed", "parameters": {"message": "=PO Rejected due to the following errors:\n{{\n[\n  !$json.has_po_number ? '* PO number was not provided' : '',\n  !$json.has_valid_po_date ? '* PO date was missing or invalid' : '',\n  !$json.has_items ? '* No line items detected' : '',\n  !$json.is_math_correct ? '* Line items prices do not match up to PO total' : ''\n]\n  .compact()\n  .join('\\n')\n}}", "options": {}, "messageId": {"__rl": true, "mode": "id", "value": "={{ $('Outlook Trigger').first().json.id }}"}, "operation": "reply", "additionalFields": {}, "replyToSenderOnly": true}, "credentials": {"microsoftOutlookOAuth2Api": {"id": "EWg6sbhPKcM5y3Mr", "name": "Microsoft Outlook account"}}, "typeVersion": 2}, {"id": "64ced193-6b12-4ee9-b1e2-************", "name": "Reply Accepted", "type": "n8n-nodes-base.microsoftOutlook", "position": [2580, 820], "webhookId": "9464583e-9505-49ec-865e-58aa1ab3c2ed", "parameters": {"message": "=Thank you for the purchase order.\nThis is an automated reply.", "options": {}, "messageId": {"__rl": true, "mode": "id", "value": "={{ $('Outlook Trigger').first().json.id }}"}, "operation": "reply", "additionalFields": {}, "replyToSenderOnly": true}, "credentials": {"microsoftOutlookOAuth2Api": {"id": "EWg6sbhPKcM5y3Mr", "name": "Microsoft Outlook account"}}, "typeVersion": 2}, {"id": "7bfe0e44-cd5d-4290-ba2e-0064c95bc4e2", "name": "Do Something with Purchase Order", "type": "n8n-nodes-base.noOp", "position": [2800, 940], "parameters": {}, "typeVersion": 1}, {"id": "6f517f2f-6072-46a2-8a9d-cca4e958d601", "name": "Fix Excel Dates", "type": "n8n-nodes-base.set", "position": [1840, 920], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "={{\n{\n  output: {\n    ...$json.output,\n    purchase_order_date: $json.output.purchase_order_date\n      ? new Date((new Date(1900, 0, 1)).getTime() + (Number($json.output.purchase_order_date) - 2) * (24 * 60 * 60 * 1000))\n      : $json.output.purchase_order_date\n  }\n}\n}}"}, "typeVersion": 3.4}, {"id": "f3a31b63-ebcb-4d93-8c5a-f626897b7d68", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-220, 620], "parameters": {"color": 7, "width": 840, "height": 680, "content": "## 1. Wait For Incoming Purchase Orders\n[Read more about the Outlook trigger](https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.microsoftoutlooktrigger)\n\nOur template starts by watching for new emails to a shared inbox (eg. \"<EMAIL>\") using the Outlook Trigger node. Our goal is to identify and capture buyer purchase orders so that we can automating validate and use AI to reduce the data entry time and cost at scale.\n\nWe can also use the Text Classifier node to validate intent. This ensures we catch valid submissions are not just queries about purchase-orders or replies."}, "typeVersion": 1}, {"id": "bb395dfc-2831-4e57-90c9-62f13f84302e", "name": "Is Submitting a Purchase Order?", "type": "@n8n/n8n-nodes-langchain.textClassifier", "position": [80, 920], "parameters": {"options": {"fallback": "other"}, "inputText": "=from: {{ $json.from.emailAddress.name }} <{{ $json.from.emailAddress.address }}>\nsubject: {{ $json.subject }}\nmessage:\n{{ $json.body.content }}", "categories": {"categories": [{"category": "is_purchase_order", "description": "The message's intent is to submit a purchase order"}]}}, "typeVersion": 1}, {"id": "e52ec2e2-8be5-40ab-b1f8-8d7c0b161e1a", "name": "Do Nothing", "type": "n8n-nodes-base.noOp", "position": [420, 1040], "parameters": {}, "typeVersion": 1}, {"id": "5ca6be4e-bc33-42d7-91bc-d30f7ccfdd25", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [180, 1080], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}], "pinData": {}, "connections": {"Run Checks": {"main": [[{"node": "Is Valid Purchase Order?", "type": "main", "index": 0}]]}, "Reply Accepted": {"main": [[{"node": "Do Something with Purchase Order", "type": "main", "index": 0}]]}, "Fix Excel Dates": {"main": [[{"node": "Run Checks", "type": "main", "index": 0}]]}, "Outlook Trigger": {"main": [[{"node": "Is Submitting a Purchase Order?", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "XLSX to Markdown Table", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Extract Purchase Order Details", "type": "ai_languageModel", "index": 0}]]}, "Is Excel Document?": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}], [{"node": "Reply Invalid Format", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Is Submitting a Purchase Order?", "type": "ai_languageModel", "index": 0}]]}, "XLSX to Markdown Table": {"main": [[{"node": "Extract Purchase Order Details", "type": "main", "index": 0}]]}, "Is Valid Purchase Order?": {"main": [[{"node": "Reply Accepted", "type": "main", "index": 0}], [{"node": "Reply Rejection", "type": "main", "index": 0}]]}, "Extract Purchase Order Details": {"main": [[{"node": "Fix Excel Dates", "type": "main", "index": 0}]]}, "Is Submitting a Purchase Order?": {"main": [[{"node": "Is Excel Document?", "type": "main", "index": 0}], [{"node": "Do Nothing", "type": "main", "index": 0}]]}}}