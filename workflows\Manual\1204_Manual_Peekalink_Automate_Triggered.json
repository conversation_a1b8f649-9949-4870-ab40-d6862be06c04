{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [310, 300], "parameters": {}, "typeVersion": 1}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.peekalink", "position": [510, 300], "parameters": {"url": "https://n8n1.io", "operation": "isAvailable"}, "credentials": {"peekalinkApi": "Peekalink API Credentials"}, "typeVersion": 1}, {"name": "IF", "type": "n8n-nodes-base.if", "position": [710, 300], "parameters": {"conditions": {"string": [], "boolean": [{"value1": "={{$json[\"isAvailable\"]}}", "value2": true}]}}, "typeVersion": 1}, {"name": "Peekalink1", "type": "n8n-nodes-base.peekalink", "position": [910, 200], "parameters": {"url": "={{$node[\"Peekalink\"].parameter[\"url\"]}}"}, "credentials": {"peekalinkApi": "Peekalink API Credentials"}, "typeVersion": 1}, {"name": "NoOp", "type": "n8n-nodes-base.noOp", "position": [910, 400], "parameters": {}, "typeVersion": 1}], "connections": {"IF": {"main": [[{"node": "Peekalink1", "type": "main", "index": 0}], [{"node": "NoOp", "type": "main", "index": 0}]]}, "Peekalink": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "<PERSON><PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}}}