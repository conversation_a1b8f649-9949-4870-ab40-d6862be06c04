{"nodes": [{"name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [450, 300], "parameters": {"url": "https://api.digitalocean.com/v2/droplets", "options": {"bodyContentType": "json"}, "requestMethod": "POST", "bodyParametersUi": {"parameter": [{"name": "name", "value": "API-creation-test"}, {"name": "region", "value": "blr1"}, {"name": "size", "value": "s-1vcpu-1gb"}, {"name": "image", "value": "ubuntu-20-04-x64"}]}, "headerParametersUi": {"parameter": [{"name": "Authorization", "value": "Bearer {your_personal_access_token}"}]}}, "typeVersion": 1}], "connections": {}}