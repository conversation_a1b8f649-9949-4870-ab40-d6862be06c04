{"meta": {"instanceId": "bb6a1286a4ce98dce786d6c2748b867c1252d53458c87d87fbf6824b862d4c9c"}, "nodes": [{"id": "95252956-51fb-49ee-924e-df01ea27b98d", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [60, 340], "parameters": {}, "typeVersion": 1}, {"id": "bfaaee00-7545-404b-9526-fb77726e833e", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-940, 120], "parameters": {"color": 5, "width": 819.6790739248162, "height": 212.7465225154412, "content": "# Monitor Multiple Github Repos\nThis workflow allows you to monitor multiple Github repos simultaneously without polling due to use of Webhooks. It programmatically allows for adding and deleting of repos to your watchlist to make management convenient.\n\n"}, "typeVersion": 1}, {"id": "d1075f59-356e-47c4-9f85-c9067127d70f", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [380, 340], "parameters": {"options": {}, "fieldToSplitOut": "repos"}, "typeVersion": 1}, {"id": "7d2a3226-e3b1-4cab-91e2-01f60c1184cb", "name": "Register Github Webhook", "type": "n8n-nodes-base.httpRequest", "position": [540, 340], "parameters": {"url": "={{$json.repos.replace('https://github.com','https://api.github.com/repos')}}/hooks", "method": "POST", "options": {}, "jsonBody": "{\"name\":\"web\",\"active\":true,\"events\":[\"push\",\"pull_request\"],\"config\":{\"url\":\"https://webhook.site/d53d7bb9-72f5-4743-af4d-15c86f811492\",\"content_type\":\"json\",\"insecure_ssl\":\"0\"}}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "Accept", "value": "application/vnd.github+json"}, {"name": "X-GitHub-Api-Version", "value": "2022-11-28"}]}}, "credentials": {"httpHeaderAuth": {"id": "A8NIXOiG7JTWqrUI", "name": "Header Auth account"}}, "typeVersion": 4.2}, {"id": "c1b8a02b-38fd-43d1-b14b-18de6d84b729", "name": "Split Out1", "type": "n8n-nodes-base.splitOut", "position": [400, 760], "parameters": {"options": {}, "fieldToSplitOut": "repos"}, "typeVersion": 1}, {"id": "35c3e7e0-50c8-4660-8e89-46849da751a9", "name": "Delete Github Webhook", "type": "n8n-nodes-base.httpRequest", "position": [900, 760], "parameters": {"url": "={{ $json.url }}", "method": "DELETE", "options": {}, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "Accept", "value": "application/vnd.github+json"}, {"name": "X-GitHub-Api-Version", "value": "2022-11-28"}]}}, "credentials": {"httpHeaderAuth": {"id": "A8NIXOiG7JTWqrUI", "name": "Header Auth account"}}, "typeVersion": 4.2}, {"id": "8eeb818d-9ac3-48bb-9a85-7099216bb243", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [160, 680], "parameters": {"width": 858.*************, "height": 279.**************, "content": "## Delete All Webhooks"}, "typeVersion": 1}, {"id": "eb1a649a-8408-4e2f-a0a4-b9761ba8565b", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [167.*************, 260], "parameters": {"width": 848.*************, "height": 283.*************, "content": "## Register Webhooks"}, "typeVersion": 1}, {"id": "3053ad9f-2756-4518-b17e-56a4ba8a287f", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [160, 1060], "parameters": {"width": 858.*************, "height": 279.**************, "content": "## <PERSON>le Github Event"}, "typeVersion": 1}, {"id": "6aca0ef9-a8d7-4e8a-a875-a0f46c624cc7", "name": "Fields", "type": "n8n-nodes-base.set", "position": [280, 1180], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "8dc55086-d1f5-4074-ba38-3ae6b477773c", "name": "repo", "type": "string", "value": "={{ $json.body.repository.full_name}}"}, {"id": "384fc78d-0125-4cbc-83f0-a4d67194beee", "name": "repo_avatar", "type": "string", "value": "={{ $json.body.repository.owner.avatar_url }}"}, {"id": "537313d4-074c-454e-b57f-0f952b1a590c", "name": "date", "type": "string", "value": "={{ $json.body.commits[0].timestamp }}"}, {"id": "34bcccc2-cad4-4306-ad54-b3685d7bc896", "name": "author", "type": "string", "value": "={{ $json.body.commits[0].author.name }} ({{ $json.body.commits[0].author.username }})"}, {"id": "c22e9ca3-9dbc-4f01-96e2-f914bd4230a1", "name": "modified_files", "type": "string", "value": "={{ $json.body.commits[0].modified.join(', ') }}"}, {"id": "c17f33cf-0d27-4813-8f35-7cd276245a8b", "name": "url", "type": "string", "value": "={{ $json.body.commits[0].url }}"}, {"id": "4b23a64e-2acc-476c-a36b-936c32360e67", "name": "description", "type": "string", "value": "={{ $json.body.commits[0].message }}"}]}}, "typeVersion": 3.4}, {"id": "72f1ac3f-4277-481d-bbc7-c5137e7ef431", "name": "Notify Slack", "type": "n8n-nodes-base.slack", "disabled": true, "position": [640, 1060], "parameters": {"text": "=[Github Event] {{ $json.date }}: {{ $json.author }} committed to {{ $json.repo }}!\n\nDescription:\n```{{ $json.description }}```\n\nModified Files:\n```{{ $json.modified_files }}```\n{{ $json.url }}", "select": "channel", "channelId": {"__rl": true, "mode": "id", "value": "="}, "otherOptions": {"mrkdwn": true, "sendAsUser": "<PERSON><PERSON><PERSON>", "includeLinkToWorkflow": false}}, "typeVersion": 2.1}, {"id": "85ec09d2-fccb-4669-80d1-ba3bb7ce3544", "name": "Telegram", "type": "n8n-nodes-base.telegram", "position": [640, 1260], "parameters": {"text": "=*[Github Event] @* `{{ $json.date }}`: \n`{{ $json.author }}` committed to `{{ $json.repo }}`!\n\nDescription:\n```{{ $json.description }}```\n\nModified Files:\n```{{ $json.modified_files }}```\n{{ $json.url }}", "replyMarkup": "inlineKeyboard", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "lulhyqZvExuxci8F", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "1f57a9cb-7061-4679-97ce-081746acfd55", "name": "Repos to Monitor", "type": "n8n-nodes-base.set", "position": [220, 340], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "{\n  \"repos\":[\n    \"https://github.com/arose26/testrepo2\",\n    \"https://github.com/arose26/testrepo3\"\n    \n  ]\n}\n"}, "typeVersion": 3.4}, {"id": "6a83a757-673b-4ffc-9f91-54e5a24b8437", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-640, 1180], "parameters": {"color": 4, "width": 520.*************, "height": 381.**************, "content": "## Test\n## 1. Register Webhooks\n- In `Repos to Monitor`, add any repo you want to monitor changes for. \n- Disable `Webhook Trigger`, Click `Test Workflow` and if your Github credentials were set correctly, it will automatically register the webhooks. - You can test this by running the single node `Get Existing Webhook` and confirming it outputs the repo addresses.\n## 2. Handle Github Events\n- Now that you have registered the webhooks, reenable `Webhook Trigger` and activate the workflow.\n- Make a commit to any of the registered repos.\n- Confirm that the notification went through.\n*That's it!*\n"}, "typeVersion": 1}, {"id": "cb204806-1f7d-494a-9e0f-340b56d2dcd5", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [-940, 440], "parameters": {"color": 4, "width": 821.180**********, "height": 693.*************, "content": "## Setup\n## 1. Creating Credentials on Github\n#### Generate a personal access token on github by following these esteps;\n- Right hand side of page -> Settings -> scroll to bottom -> Developer Settings > Personal Access Token > Tokens (classic) > Generate New Token\n- Give scopes:\n   *admin:repo_hook*\n   *repo* (if you want to use it for your own private repo)\n\nif you need more help, see here:\nhttps://docs.github.com/en/authentication/keeping-your-account-and-data-secure/managing-your-personal-access-tokens\n\n## 2. Setting Credentials in n8n\nIn `Register Github Webhook`\n*Authenticaion > Generic Credential Type*\n*Generic Auth Type > Header Auth*\n*Header Auth > Create New Credential* with Name set to *'Authorization'* and Value set to *'Bearer <YOUR GITHUB TOKEN HERE>'*.\n(You can reuse this for `Delete Github Webhook` and `Get Existing Webhooks`).\nNow in `Register Github Webhook`, scroll down to Send Body > JSON and inside the JSON, change the value of *\"url\"* to the webhook address given as Production URL in the node `Webhook Trigger`.\n\n\n## 3. Notification settings\nIn the third row, link up the Webhook Trigger to any API of your choice. Slack and Telegram are given as examples.\nYou can also format the notification message as you wish.\n\n"}, "typeVersion": 1}, {"id": "28bd218b-7dfb-460e-a2a8-012af08835cd", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "position": [40, 1180], "webhookId": "e90c3560-2c95-4e7e-9df3-2d084d7e8fde", "parameters": {"path": "e90c3560-2c95-4e7e-9df3-2d084d7e8fde", "options": {}, "httpMethod": "POST"}, "typeVersion": 2}, {"id": "b68dff7d-f7ee-47dc-b360-08d9ea2d7f42", "name": "Repos to Monitor1", "type": "n8n-nodes-base.set", "position": [240, 760], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "{\n  \"repos\":[\n     \"https://github.com/arose26/testrepo\",\n    \"https://github.com/arose26/testrepo2\",\n    \"https://github.com/arose26/testrepo3\"\n    \n  ]\n}\n"}, "typeVersion": 3.4}, {"id": "39dd7062-bb85-4f95-90f7-47fe27a257c8", "name": "Get Existing Hook", "type": "n8n-nodes-base.httpRequest", "position": [740, 760], "parameters": {"url": "={{ $json.url }}", "options": {}, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "Accept", "value": "application/vnd.github+json"}, {"name": "X-GitHub-Api-Version", "value": "2022-11-28"}]}}, "credentials": {"httpHeaderAuth": {"id": "A8NIXOiG7JTWqrUI", "name": "Header Auth account"}}, "typeVersion": 4.2, "alwaysOutputData": false}, {"id": "6d092a2f-ba48-4b0f-a772-4f55ba761d64", "name": "Hook URL", "type": "n8n-nodes-base.set", "position": [560, 760], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "b90c27f3-b81a-4098-9cd8-7934880d78a7", "name": "url", "type": "string", "value": "=https://api.github.com/repos/{{ $json.repos.replace('https://github.com/','')}}/hooks"}]}}, "typeVersion": 3.4}], "pinData": {}, "connections": {"Fields": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "Hook URL": {"main": [[{"node": "Get Existing Hook", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Register Github Webhook", "type": "main", "index": 0}]]}, "Split Out1": {"main": [[{"node": "Hook URL", "type": "main", "index": 0}]]}, "Webhook Trigger": {"main": [[{"node": "Fields", "type": "main", "index": 0}]]}, "Repos to Monitor": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Get Existing Hook": {"main": [[{"node": "Delete Github Webhook", "type": "main", "index": 0}]]}, "Repos to Monitor1": {"main": [[{"node": "Split Out1", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Repos to Monitor", "type": "main", "index": 0}]]}}}