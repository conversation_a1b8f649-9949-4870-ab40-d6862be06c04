{"meta": {"instanceId": "cb484ba7b742928a2048bf8829668bed5b5ad9787579adea888f05980292a4a7"}, "nodes": [{"id": "4c52efcf-039b-4550-8a63-3d3d4dde488b", "name": "On new manual Chat Message", "type": "@n8n/n8n-nodes-langchain.manualChatTrigger", "position": [740, 300], "parameters": {}, "typeVersion": 1.1}, {"id": "adb528f1-b87b-4bb2-99e1-776fd839522e", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [680, 940], "parameters": {}, "typeVersion": 1}, {"id": "092cf737-5b53-4fc8-82f5-c775b77ea0bd", "name": "Hacker News", "type": "n8n-nodes-base.hackerNews", "position": [900, 940], "parameters": {"limit": 50, "resource": "all", "additionalFields": {}}, "typeVersion": 1}, {"id": "a0805137-630c-4065-826e-88afa000660f", "name": "Clean up data", "type": "n8n-nodes-base.set", "position": [1120, 940], "parameters": {"fields": {"values": [{"name": "title", "stringValue": "={{ $json._highlightResult.title.value }}"}, {"name": "points", "type": "numberValue", "numberValue": "={{ $json.points }}"}, {"name": "url", "stringValue": "={{ $json.url }}"}, {"name": "created_at", "stringValue": "={{ $json.created_at }}"}, {"name": "author", "stringValue": "={{ $json.author }}"}]}, "include": "none", "options": {}}, "typeVersion": 3.2}, {"id": "e1b255f4-e970-42d6-9870-4e302bf2da83", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [960, 300], "parameters": {"options": {"maxIterations": 10}}, "typeVersion": 1.1}, {"id": "91e3391e-909e-4d63-9649-ff62781dbba9", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [960, 520], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "VQtv7frm7eLiEDnd", "name": "OpenAi account 7"}}, "typeVersion": 1}, {"id": "cd1f0028-635e-48eb-ac38-4c6fb25ed63e", "name": "Stringify", "type": "n8n-nodes-base.code", "position": [1340, 940], "parameters": {"jsCode": "return {\n 'response': JSON.stringify($input.all().map(x => x.json))\n}"}, "typeVersion": 2}, {"id": "7df241eb-67d3-4724-8b32-4b53561ed55f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [880, 820], "parameters": {"color": 7, "width": 150, "height": 293, "content": "### Replace me\nwith any other service, e.g. fetching your own data"}, "typeVersion": 1}, {"id": "270845df-7c2d-4035-9ac0-e41d418b3026", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [600, 738.125], "parameters": {"color": 7, "width": 927.5, "height": 406.875, "content": "### Sub-workflow: Custom tool\nThis can be called by the agent above. This example fetches the top 50 posts ever on Hacker News"}, "typeVersion": 1}, {"id": "1d796a86-45d1-4fc4-8245-893525505d1f", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [600, 200], "parameters": {"color": 7, "width": 927.5, "height": 486.5625, "content": "### Main workflow: AI agent using custom tool\nTry it out by clicking 'Chat' and entering 'What is the 5th most popular post ever on Hacker News?'"}, "typeVersion": 1}, {"id": "38ff64b5-6f47-4d2d-9051-caab418bb0e8", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [440, 300], "parameters": {"width": 185.9375, "height": 218, "content": "## Try me out\n\nClick the 'Chat' button and enter:\n\n_What is the 5th most popular post ever on Hacker News?_"}, "typeVersion": 1}, {"id": "3532e461-bd74-48f7-93e1-96d608c88688", "name": "Custom tool to call the wf below", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [1120, 520], "parameters": {"name": "hn_tool", "workflowId": "={{ $workflow.id }}", "description": "Returns a list of the most popular posts ever on Hacker News, in json format"}, "typeVersion": 1}], "pinData": {}, "connections": {"Hacker News": {"main": [[{"node": "Clean up data", "type": "main", "index": 0}]]}, "Clean up data": {"main": [[{"node": "Stringify", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "Hacker News", "type": "main", "index": 0}]]}, "On new manual Chat Message": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Custom tool to call the wf below": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}}}