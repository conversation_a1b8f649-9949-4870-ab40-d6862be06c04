{"id": "2", "name": "Daily Text Affirmations", "nodes": [{"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [350, 380], "parameters": {"triggerTimes": {"item": [{"hour": 9}]}}, "typeVersion": 1}, {"name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [760, 380], "parameters": {"url": "https://affirmations.dev", "options": {}}, "typeVersion": 1}, {"name": "Telegram", "type": "n8n-nodes-base.telegram", "position": [1140, 380], "parameters": {"text": "=Hey <PERSON>, here's your daily affirmation...\n\n{{$node[\"HTTP Request\"].json[\"affirmation\"]}}", "additionalFields": {}}, "credentials": {"telegramApi": "Telegram Token"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Cron": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}}}