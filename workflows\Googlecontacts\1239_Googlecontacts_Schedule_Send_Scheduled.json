{"id": "9w5vu5VmXxpdBLWi", "meta": {"instanceId": "14e4c77104722ab186539dfea5182e419aecc83d85963fe13f6de862c875ebfa"}, "name": "Send Daily Birthday Reminders from Google Contacts to Slack", "tags": [{"id": "uScnF9NzR3PLIyvU", "name": "Published", "createdAt": "2025-03-21T07:22:28.491Z", "updatedAt": "2025-03-21T07:22:28.491Z"}], "nodes": [{"id": "e4de5385-6b00-4245-b06e-3003703a348a", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [80, 140], "parameters": {"rule": {"interval": [{"triggerAtHour": 8}]}}, "typeVersion": 1.2}, {"id": "df65de90-d931-450e-bed1-bf8b4f79a090", "name": "Google Contacts", "type": "n8n-nodes-base.googleContacts", "notes": "Get the contact details\n", "position": [300, 140], "parameters": {"fields": ["emailAddresses", "birthdays", "names", "nicknames"], "options": {}, "operation": "getAll", "returnAll": true}, "notesInFlow": true, "typeVersion": 1}, {"id": "6e3dfeea-b22d-4156-a9a9-a8d5bb610848", "name": "If", "type": "n8n-nodes-base.if", "position": [800, 180], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "eff6fe23-651d-474d-8d77-3734e1ac4c13", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.today }}", "rightValue": "={{ $('Google Contacts').item.json.birthdays }}"}]}}, "typeVersion": 2.2}, {"id": "32bd420e-11ab-4e82-a732-ed155f36094b", "name": "<PERSON><PERSON>ck", "type": "n8n-nodes-base.slack", "notes": "Reminds to the birthday message", "position": [1020, 60], "webhookId": "b5fda056-5b45-49ee-8e09-cd4bc7a2a881", "parameters": {"text": "Todays Birthday of your friend", "select": "channel", "blocksUi": "=Today is {{$json[\"first_name\"]}} {{$json[\"last_name\"]}}'s birthday! 🎉", "channelId": {"__rl": true, "mode": "url", "value": "", "__regex": "https://app.slack.com/client/.*/([a-zA-Z0-9]{2,})"}, "messageType": "block", "otherOptions": {}, "authentication": "oAuth2"}, "credentials": {"slackOAuth2Api": {"id": "", "name": ""}}, "notesInFlow": true, "typeVersion": 2.3}, {"id": "caa5a301-ff68-4d61-801f-ac8c95edded3", "name": "Filter Contact ", "type": "n8n-nodes-base.filter", "position": [560, 140], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "edb146b2-f338-4563-a991-d38613d1d5aa", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $('Google Contacts').item.json.birthdays }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "4a156b56-ab25-4d29-aa1b-8cf00e4114c9", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [0, 0], "parameters": {"width": 1220, "height": 320, "content": "Send Daily Birthday Reminders from Google Contacts to Slack"}, "typeVersion": 1}, {"id": "b1b04e75-e674-4389-a5ad-ebdcdfedca78", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [0, 360], "parameters": {"width": 1220, "height": 100, "content": "This workflow automates the process of retrieving your Google Contacts, filtering out the ones with birthdays on the current day, and sending a reminder to a designated Slack channel. By scheduling it to run daily at a specific time, the workflow ensures that you never miss a birthday reminder. Whether for team celebrations, personal reminders, or simply keeping track of important dates, this workflow can be easily customized to notify you or your team about upcoming birthdays directly in Slack."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "22eaeed6-6d9e-430b-8a1d-3848257cf3b2", "connections": {"If": {"main": [[{"node": "<PERSON><PERSON>ck", "type": "main", "index": 0}]]}, "Filter Contact ": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Google Contacts": {"main": [[{"node": "Filter Contact ", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Google Contacts", "type": "main", "index": 0}]]}}}