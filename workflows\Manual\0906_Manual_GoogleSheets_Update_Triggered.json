{"meta": {"instanceId": "5b9aff0ecdeb17791c04b93eac72e39e69151cfa63708980e5d936abe9308b8c"}, "nodes": [{"id": "b0a2f427-1788-4707-b2ca-07b7ba9878ab", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-2040, 1040], "parameters": {}, "typeVersion": 1}, {"id": "9bf6edaf-e2ad-4702-8730-0600775531cb", "name": "Post Summarization", "type": "@n8n/n8n-nodes-langchain.chainSummarization", "position": [20, 680], "parameters": {"options": {}}, "typeVersion": 2}, {"id": "9541106a-af9c-4326-91b1-07f68c9ee386", "name": "Merge <PERSON>", "type": "n8n-nodes-base.merge", "position": [-720, 980], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3}, {"id": "b0ad0465-0daa-48f6-a9c5-8dadca2ca4e1", "name": "Output The Results", "type": "n8n-nodes-base.googleSheets", "position": [1140, 820], "parameters": {"columns": {"value": {}, "schema": [], "mappingMode": "autoMapInputData", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": 979106892, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1cIMIh_DjoWXMDaJEH-AyTZbnAha6TxthCSSEam4NLsE/edit#gid=979106892", "cachedResultName": "Find-Leads"}, "documentId": {"__rl": true, "mode": "id", "value": "1cIMIh_DjoWXMDaJEH-AyTZbnAha6TxthCSSEam4NLsE"}}, "typeVersion": 4.5}, {"id": "16c397ea-1625-43c6-8602-9150a79858a4", "name": "Merge 3 Inputs", "type": "n8n-nodes-base.merge", "position": [800, 820], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3}, {"id": "f59b3a35-9502-4a69-8b18-6760538765ab", "name": "Filter Posts By Features", "type": "n8n-nodes-base.if", "position": [-1620, 1040], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "0823d10a-ad54-4d82-bcea-9dd236e97857", "operator": {"type": "number", "operation": "gt"}, "leftValue": "={{ $json.ups }}", "rightValue": 2}, {"id": "bb8187aa-f0f1-4999-8d4b-bdc9abba0618", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.selftext }}", "rightValue": ""}, {"id": "539f0f5c-025a-4f82-9b3a-2ef1ad3a2d96", "operator": {"type": "dateTime", "operation": "after"}, "leftValue": "={{ DateTime.fromSeconds($json.created).toISO() }}", "rightValue": "={{ $today.minus(180,'days').toISO() }}"}]}}, "typeVersion": 2.2}, {"id": "d88efb10-91cf-4ac0-9f7b-796bfa8a75ab", "name": "Filter Posts By Content", "type": "n8n-nodes-base.if", "position": [-460, 980], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "d5d38c01-3a88-4767-b488-d9c04145bb8f", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.output }}", "rightValue": "yes"}]}}, "typeVersion": 2.2}, {"id": "405f37a4-d3a8-4d92-8add-ad232be014b7", "name": "Select Key Fields", "type": "n8n-nodes-base.set", "position": [-1380, 1020], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "e5082ecc-3add-474e-bdb5-b8ad64729930", "name": "upvotes", "type": "string", "value": "={{ $json.ups }}"}, {"id": "a92b5859-fbcc-40c2-95e0-452b12530d98", "name": "subreddit_subscribers", "type": "number", "value": "={{ $json.subreddit_subscribers }}"}, {"id": "a846e21c-6cff-4521-9e0c-a32fa1305376", "name": "postcontent", "type": "string", "value": "={{ $json.selftext }}"}, {"id": "b8045389-684d-4872-9e32-9a6b5511eb2b", "name": "url", "type": "string", "value": "={{ $json.url }}"}, {"id": "f182fedc-1b09-40fe-aeb5-2473263da442", "name": "date", "type": "string", "value": "={{ DateTime.fromSeconds($json.created).toISO() }}"}]}}, "typeVersion": 3.4}, {"id": "6a6143e1-6181-45a0-988f-e8ed7e634bd8", "name": "Get Posts", "type": "n8n-nodes-base.reddit", "position": [-1820, 1040], "parameters": {"keyword": "how do I find leads", "operation": "search", "subreddit": "=Entrepreneur", "additionalFields": {"sort": "hot"}}, "typeVersion": 1}, {"id": "1937b724-18a1-4b5b-8338-1752f478eccf", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-2140, 800], "parameters": {"width": 880, "height": 440, "content": "# Data Extraction\n## Retrieves recent posts from specific Reddit community (subreddit)\n## Filters content by keywords and upvotes"}, "typeVersion": 1}, {"id": "5d4a1df6-16a5-473e-b6b4-42e7966a5cd2", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-1140, 580], "parameters": {"color": 4, "width": 820, "height": 660, "content": "# Transformation Step\n## Analyze using LLM (AI)\n## Filter for business opportunities"}, "typeVersion": 1}, {"id": "399afd5a-ced1-447b-95de-a7191632d266", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-200, 480], "parameters": {"color": 6, "width": 1460, "height": 760, "content": "#Transformation 2nd Step + Load to Gsheet\n## Insight Generation \n## Generates executive summaries of key opportunities\n## Submit findings in Google Sheets"}, "typeVersion": 1}, {"id": "571bf6e7-ef36-4a7d-9e9d-e97aef4e7015", "name": "OpenRouter <PERSON>", "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "position": [-1100, 940], "parameters": {"model": "openai/gpt-4.1-mini", "options": {}}, "typeVersion": 1}, {"id": "7e157fc9-a7cd-42d4-80d5-233308a2441a", "name": "OpenRouter Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "position": [40, 860], "parameters": {"model": "openai/gpt-4.1-mini", "options": {}}, "typeVersion": 1}, {"id": "9f2020d1-77f2-4dbc-b6ce-7dae7acd1263", "name": "Basic LLM Chain", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [-1100, 760], "parameters": {"text": "=Decide whether this reddit post is describing a business-related problem or a need for a solution.", "messages": {"messageValues": [{"message": "The post should mention a specific challenge or requirement that a business is trying to address. Is this post about a business problem or need for a solution ? Output only yes or no"}, {"type": "HumanMessagePromptTemplate", "message": "=Reddit post:  {{ $json.postcontent }}"}]}, "promptType": "define"}, "typeVersion": 1.6}, {"id": "b048b2f6-3c46-4ad2-8255-5b0509e9da0f", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [460, 680], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "7038812d-f325-4196-89b6-3623d81dec7b", "name": "summary", "type": "string", "value": "={{ $json.response.text }}"}]}}, "typeVersion": 3.4}, {"id": "924219fa-ae4d-44b3-a42d-e2c67dc85545", "name": "Edit Fields1", "type": "n8n-nodes-base.set", "position": [280, 960], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "1f34c3f3-7be7-474c-9026-7058807a7b3d", "name": "date", "type": "string", "value": "={{ $json.date }}"}, {"id": "0e0e5227-e37b-43fc-8a88-2bb76631108d", "name": "subreddit_subscribers", "type": "number", "value": "={{ $json.subreddit_subscribers }}"}, {"id": "68e2ca82-6b1d-42ec-acc7-b784e9ed61b5", "name": "url", "type": "string", "value": "={{ $json.url }}"}, {"id": "946800a2-ec8b-4f99-a4db-9248bf305747", "name": "upvotes", "type": "string", "value": "={{ $json.upvotes }}"}, {"id": "da86d4d3-db84-44e3-a684-38aff2fd5b77", "name": "postcontent", "type": "string", "value": "={{ $json.postcontent }}"}, {"id": "b67148e1-67a5-4b10-be6c-c819ff910be0", "name": "business_opportunity", "type": "string", "value": "={{ $json.output }}"}]}}, "typeVersion": 3.4}], "pinData": {"Merge Input": [{"url": "https://www.reddit.com/r/smallbusiness/comments/1iqletb/need_help_and_advice_for_a_business_name_idea/", "date": "2025-02-16T13:42:12.000+08:00", "output": "yes", "upvotes": "4", "postcontent": "Hello guys,\n\nMy partner and I are planning to open an accounting business that will focus on tax services such as filling taxes and tax advisor and we have plan for future to add wealth management and capital advising. Initially, we were thinking of using the name \"Global Solutions,\" but we found out that another company already has it, so we can’t use it.\n\nWe’re looking for a professional name that’s easy to pronounce and somewhat similar to \"Global Solutions.\" Also, unique enough that we won’t want to change it in the future. Any ideas or suggestions would be greatly appreciated! We would love to list all name suggestions to share with my partner so we can pick the best one.\n\nThanks in advance for your help! Appreciate it! ", "subreddit_subscribers": 1944498}, {"url": "https://www.reddit.com/r/smallbusiness/comments/1iob5ez/business_acquisition_loan_what_are_my_odds_what/", "date": "2025-02-13T12:34:29.000+08:00", "output": "yes", "upvotes": "3", "postcontent": "Hello!  \nLongtime friends have offered to sell my their local biz.    \n12 years running, last year did 950k gross, 325K SDE.  \nYOY growth has been good.  \n650k price.  \nThey have offered to seller finance up to 61.5% of the purchase price so far.  \nThey might go even higher on the seller financing if I ask.\n\n**The good (about me):**  \n  \n\\- I have good credit, probably 720+.  \n\\- I do have \\~200k equity in my home, I'm willing to collateralize.  \n  \n**The ugly (about me):**  \n  \n\\- I have only 5% down possible for equity injection, but would prefer 0%  \nI read that with a SBA 7(a) loan the seller can do 5% of my equity injection with a standby note (deferred payment until SBA loan is paid off).  I'm not sure if that could be done in tandem with another (much larger) note that would be payable (in payments) at closing.  \n  \n\\- I've had no / negative income the last couple of years.  I took some time off from my 20+ year profession, lived off of savings and some credit while I explored other career paths because I needed a change.  I did learn a couple of high value trades, and did incur some expenses in that process.\n\n\\- No direct industry experience.  I do have much professional experience I bring to the table, but not in this industry.  I have managed people on my team... but not employees.  \n\n**The rest:**  \n  \nThey are willing to hire me as store manager now, if that helps.    \nThey will be providing complete training and ongoing support.  \nIt is a simple business, really.  \nThey obviously believe in their business, given the willingness to seller finance so much of it.  \n  \nWhat are the odds I could get a SBA 7(a) loan with 5% down?  \nAre there any loans with 0% down?  \n  \nI would like to get an extra 50k or so for startup costs - it is an acquisition however I'm going to have startup expenses like first and last months lease payments, jurisdictional inspections, electricity deposit, liability insurance / workman's comp, that sort of stuff.\n\nI realize the scenario is not ideal, however it seems to me there should be SOME option out there given that all I have to do is not mess up the business!  It's a great business, well loved in the community.  \nThere is good room in the revenue for me to make accelerated loan repayments, establish business savings, grow the business, and even pay myself enough to cover my living expenses.  That is one heck of a deal, I have to find some way to pull this off!\n\nI'm willing to look at less fantastic loan offers with higher rates.    \nIt really seems to me that some entity would be willing to lend based on the cashflow / success / stability of the existing business.\n\nOne idea I had - if sellers would be willing to carry 100% for 12-24 months, would I then likely have an easier time qualifying for a SBA 7(a) loan to pay off their note, or part of it (depending on what they want)?\n\nAnother idea - store manager -&gt; partner -&gt; partner buyout  \nI do need to find out their maximum timeline for getting out.  \n  \nHad I known a couple of years ago this was going to come up, I would have made different decisions!  \nI really don't want to sell my house and rent something in order to do this, but I'm considering that as a last resort.  \nIf these weren't my longtime friends whom I trust with my life, I wouldn't consider this.  I'd be too chicken.  This is like winning the lottery to me, frankly... I'm not the perfect buyer on paper but they really want me to have it.  They know it will be my baby, as it has been theirs.\n\nGrateful for any solutions / ideas, thank you in advance!  =D\n\n", "subreddit_subscribers": 1944498}, {"url": "https://www.reddit.com/r/smallbusiness/comments/1ikcdi5/seeking_a_reliable_alternative_to_stripe_for/", "date": "2025-02-08T10:09:44.000+08:00", "output": "yes", "upvotes": "3", "postcontent": "Hi everyone,\n\nI'm looking for advice on the **best alternative to Stripe** for my service-based business. Here’s the situation:\n\n* I handle **monthly recurring payments** from customers who prefer paying by **credit card**.\n* My customers provide me with their credit card information, and I need a solution to **send invoices** or **auto-charge their cards monthly** without issues.\n\n# Problems I’ve Faced:\n\n1. **Stripe**: I’ve lost countless disputes despite providing proof of service, and I’m fed up with their **chargeback process**.\n2. **Square**: I processed just **two paid invoices totaling $180**, and they **deactivated my account**, holding my money for **90 days**!\n\nI’m desperate to find a platform that:\n\n* Allows **invoicing** and **recurring auto-charges**.\n* Has **minimal chargebacks or disputes**, or at least a fair dispute resolution process. or **BEST: no disputes at all**\n* Doesn’t hold funds unnecessarily or shut down accounts without notice.\n\nI’m open to hearing about **any reliable options**, whether they are traditional payment processors, blockchain-based platforms, or other innovative solutions.\n\n**Please help!** Any advice would mean the world to me right now.\n\nThank you in advance for your suggestions!", "subreddit_subscribers": 1944498}, {"url": "https://www.reddit.com/r/smallbusiness/comments/1ibkmzd/business_number_being_used_to_spam_call_people/", "date": "2025-01-28T05:28:30.000+08:00", "output": "yes", "upvotes": "7", "postcontent": "So I just got off the phone with the umpteenth person who has gotten a spam call from someone spoofing with our business number, and I’m just waiting for the day that we start getting negative reviews based on this.\n\nWe’ve gotten angry calls from people for a number of scams, and apparently it’s repeated calls to them.\n\nI feel bad, cos those calls make me mad too, but I get tired of getting cussed out several times a week, and having to explain what spam calls are. I haven’t found any solutions online that look like they’d actually solve the problem.\n\nDoes anyone else get this with their business numbers?", "subreddit_subscribers": 1944498}, {"url": "https://www.reddit.com/r/smallbusiness/comments/1i43orw/im_a_small_business_owner_which_software_should_i/", "date": "2025-01-18T17:06:49.000+08:00", "output": "yes", "upvotes": "38", "postcontent": "I generate about $100k in annual revenue and don’t have payroll. What software would you recommend, and why? Currently, I create invoices using Excel, but I’m looking for a more efficient solution to send invoices and receive payments seamlessly.\n\nAlso, is there a fee every time I receive a payment? For example, if I receive $20k, $10k, $30k, or $40k?", "subreddit_subscribers": 1944498}, {"url": "https://www.reddit.com/r/smallbusiness/comments/1i1euah/small_business_automation_can_someone_help_me/", "date": "2025-01-15T03:54:19.000+08:00", "output": "yes", "upvotes": "3", "postcontent": "So I am looking for ways to bring some automations to my business by leveraging the technology available and I started with programing a smart chat bot for my website that literally is an agent who knows everything about my company which is nice when I am not around.  Then I took it further and thought that I could make automated virtual receptionist for my company which I did which makes life better because when I am on a job I miss probably a few calls a day and then when I try to reach them back, they usually have already started to talk to other competitors and then it gets challenging from there.  So this has been my solution and now I never miss a call and started building automations to even sell for me on my products and services that I offer and now even can send a booking link to them by text and email and this has allowed me to convert better and not miss an opportunity that comes my way.  I say all this because I created another on that is used strictly to role play with and I need testers to help me refine and debug it.  Essentially I just need other business owners to role play with my agent and provide any feedback that would make it better or enhance it.  \n\nIf you're willing to help me test it just call 1-855-449-7005.  Thanks in advance to anyone who tries it out!            ", "subreddit_subscribers": 1944498}, {"url": "https://www.reddit.com/r/smallbusiness/comments/1hzlhcg/customer_emailcommunication_tracker/", "date": "2025-01-12T20:17:33.000+08:00", "output": "yes", "upvotes": "3", "postcontent": "What system do you use for customer communication?\n\nLooking for recommendations on CSR communication. I have a retail store with one full time retail manager and a handful of seasonal and part time associates. \n\nWebsite inquiries for retail are routed to a generic email of which all associates can respond. The goal was that with a generic email (accessed from one terminal plus an iPad) customers would get responded to quickly but Mozilla Thunderbird’s interface is clunky and associates never remember to “file” completed conversations. \n\nI am frugal (hence one email address) but am willing to invest in a solution that can better track inquiries (only a handful a week) to provide a better experience. Just curious what you might use that works well. ", "subreddit_subscribers": 1944498}, {"url": "https://www.reddit.com/r/smallbusiness/comments/1hyzgts/had_a_customer_fire_themselves_and_it_felt_good/", "date": "2025-01-12T00:24:35.000+08:00", "output": "yes", "upvotes": "57", "postcontent": "My work had a newer customer that we were happy to have because we knew they were working with our competition. We did some work for them and they would blame us for their problems. We would offer solutions and never hear back and to top it off they paid late. I also met the owner at a trade show and he treated me like I wasn't even there when I went to say thank you. He just looked at me blankly and ignored me. So we stopped calling.\n\nThen a half year later they send some work in. I quoted it extremely high. They asked for a price discount so they could get the job for their customer. I went down 10% knowing it was still high. Then the owner emailed back about his 25 year relationship with our competitor and how they would do it at half the price. \n\nI felt happy wasting their time and money. Also, if our competitor is so great, why did they start sending us work? \n\nI'm glad we won't hear from them. I have many other customers that are fantastic to work with and pay on time. ", "subreddit_subscribers": 1944498}, {"url": "https://www.reddit.com/r/smallbusiness/comments/1hnqv0q/attention_business_owners_using_benchco/", "date": "2024-12-28T06:31:18.000+08:00", "output": "yes", "upvotes": "8", "postcontent": "You may have seen in your email that [Bench.co](http://Bench.co) is closing its doors for bookkeeping services. They are giving business owners until **March 7th, 2025**, at **5 PM** ET to download their financial data.\n\nDon't wait until the last minute! This is absolutely critical - your financial data is too important to risk losing. The timing couldn't be worse in the middle of the holidays and so close to year-end... and the lack of advance warning is frustrating.\n\nI know this situation will leave many business owners scrambling for a new bookkeeping solution. But don’t stress—there are excellent alternatives out there that can serve you even better!\n\nI primarily wanted to make this post to alert people of the closure (in case you missed the email) and encourage everyone to secure their data ASAP. If you're looking for a reliable path forward, I'd recommend exploring smaller firms or individual remote bookkeepers. Many offer highly personalized services at a wide range of prices - with services often far better quality than Bench.\n\nI'm not here to promote my business, but if you're feeling overwhelmed or don't know where to start, I'm happy to chat and share advice based on my experience running a remote bookkeeping and accounting firm. At the end of the day, I hope all Bench clients find a bookkeeping service that's a better fit: personalized, reliable, and capable of supporting your business long-term.\n\n  \nTo add a question and make sure I'm following the sub rules: \n\nWhat are you currently doing for your bookkeeping and accounting? How did you find that solution and what do you wish was different about it?", "subreddit_subscribers": 1944498}]}, "connections": {"Get Posts": {"main": [[{"node": "Filter Posts By Features", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Merge 3 Inputs", "type": "main", "index": 0}]]}, "Merge Input": {"main": [[{"node": "Filter Posts By Content", "type": "main", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "Merge 3 Inputs", "type": "main", "index": 1}]]}, "Merge 3 Inputs": {"main": [[{"node": "Output The Results", "type": "main", "index": 0}]]}, "Basic LLM Chain": {"main": [[{"node": "Merge <PERSON>", "type": "main", "index": 0}]]}, "Select Key Fields": {"main": [[{"node": "Merge <PERSON>", "type": "main", "index": 1}, {"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "Post Summarization": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "OpenRouter Chat Model": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "OpenRouter Chat Model1": {"ai_languageModel": [[{"node": "Post Summarization", "type": "ai_languageModel", "index": 0}]]}, "Filter Posts By Content": {"main": [[{"node": "Post Summarization", "type": "main", "index": 0}, {"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Filter Posts By Features": {"main": [[{"node": "Select Key Fields", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Get Posts", "type": "main", "index": 0}]]}}}