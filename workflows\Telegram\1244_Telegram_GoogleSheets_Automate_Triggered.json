{"id": "A7dRnMf9WybO8O02", "meta": {"instanceId": "a4bfc93e975ca233ac45ed7c9227d84cf5a2329310525917adaf3312e10d5462", "templateCredsSetupCompleted": true}, "name": "Telegram ChatBot with multiple sessions", "tags": [], "nodes": [{"id": "d3104851-90ec-4f0c-ab4d-aee5a6faf81b", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [640, 1180], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "4zwP0MSr8zkNvvV9", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "e25050a1-e49b-4b46-8452-ce7b3388c7f3", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [780, 1180], "parameters": {"sessionKey": "={{ $('Get session').item.json.SESSION }}", "sessionIdType": "customKey", "contextWindowLength": 100}, "typeVersion": 1.3}, {"id": "d50951ae-e9a2-492c-8ff5-a039ca6975e4", "name": "Get message", "type": "n8n-nodes-base.telegramTrigger", "position": [-520, -60], "webhookId": "8d87dcf7-1608-4255-a1f1-03b700a42f0e", "parameters": {"updates": ["message"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "rQ5q95W7uKesMDx4", "name": "Telegram account Fastewb"}}, "typeVersion": 1.2}, {"id": "1bc9aa0f-8af8-47b8-b7d7-6bfd9280b65f", "name": "Command or text?", "type": "n8n-nodes-base.switch", "position": [-20, -60], "parameters": {"rules": {"values": [{"outputKey": "New session", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "955f3c39-0732-4a63-b3f7-70ab0753a68a", "operator": {"type": "string", "operation": "startsWith"}, "leftValue": "={{ $('Get message').item.json.message.text }}", "rightValue": "/new"}]}, "renameOutput": true}, {"outputKey": "Current session", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "790f64bc-f3cc-4ef4-9d43-80853742fee6", "operator": {"type": "string", "operation": "startsWith"}, "leftValue": "={{ $('Get message').item.json.message.text }}", "rightValue": "/current"}]}, "renameOutput": true}, {"outputKey": "Resume session", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "39f35352-aee4-4a16-b82f-7f58ab8120f0", "operator": {"type": "string", "operation": "startsWith"}, "leftValue": "={{ $('Get message').item.json.message.text }}", "rightValue": "/resume"}]}, "renameOutput": true}, {"outputKey": "Summary ", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "270d71bf-8ecf-46e7-a601-cc5d1dc58e72", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Get message').item.json.message.text }}", "rightValue": "/summary"}]}, "renameOutput": true}, {"outputKey": "Question", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "55fe98fa-e39a-41b3-983f-359a8e730f21", "operator": {"type": "string", "operation": "startsWith"}, "leftValue": "={{ $('Get message').item.json.message.text }}", "rightValue": "/question"}]}, "renameOutput": true}]}, "options": {"fallbackOutput": "extra"}}, "typeVersion": 3.2}, {"id": "4a405332-99a8-4ec9-84d5-89f9f5f1610f", "name": "Get session", "type": "n8n-nodes-base.googleSheets", "position": [-260, -60], "parameters": {"options": {"returnFirstMatch": true}, "filtersUI": {"values": [{"lookupValue": "current", "lookupColumn": "STATE"}]}, "sheetName": {"__rl": true, "mode": "list", "value": *********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1MCJLAqKP0Y7Qr68ZYoSSBeEVyKI1QgAAZnlEiyqkzXo/edit#gid=*********", "cachedResultName": "Session"}, "documentId": {"__rl": true, "mode": "list", "value": "1MCJLAqKP0Y7Qr68ZYoSSBeEVyKI1QgAAZnlEiyqkzXo", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1MCJLAqKP0Y7Qr68ZYoSSBeEVyKI1QgAAZnlEiyqkzXo/edit?usp=drivesdk", "cachedResultName": "<PERSON><PERSON><PERSON> with session"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "860fa8d5-6c9b-476b-857b-9c47a023d72f", "name": "Disable previous session", "type": "n8n-nodes-base.googleSheets", "position": [640, -760], "parameters": {"columns": {"value": {"STATE": "expire", "SESSION": "={{ $('Get session').item.json.SESSION }}"}, "schema": [{"id": "SESSION", "type": "string", "display": true, "removed": false, "required": false, "displayName": "SESSION", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "STATE", "type": "string", "display": true, "removed": false, "required": false, "displayName": "STATE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["SESSION"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": *********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1MCJLAqKP0Y7Qr68ZYoSSBeEVyKI1QgAAZnlEiyqkzXo/edit#gid=*********", "cachedResultName": "Session"}, "documentId": {"__rl": true, "mode": "list", "value": "1MCJLAqKP0Y7Qr68ZYoSSBeEVyKI1QgAAZnlEiyqkzXo", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1MCJLAqKP0Y7Qr68ZYoSSBeEVyKI1QgAAZnlEiyqkzXo/edit?usp=drivesdk", "cachedResultName": "<PERSON><PERSON><PERSON> with session"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "6ff1cf54-5406-4c86-a77e-8424208d4728", "name": "Set new session", "type": "n8n-nodes-base.googleSheets", "position": [960, -760], "parameters": {"columns": {"value": {"STATE": "current", "SESSION": "={{ $('Get message').item.json.update_id }}"}, "schema": [{"id": "SESSION", "type": "string", "display": true, "required": false, "displayName": "SESSION", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "STATE", "type": "string", "display": true, "removed": false, "required": false, "displayName": "STATE", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": *********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1MCJLAqKP0Y7Qr68ZYoSSBeEVyKI1QgAAZnlEiyqkzXo/edit#gid=*********", "cachedResultName": "Session"}, "documentId": {"__rl": true, "mode": "list", "value": "1MCJLAqKP0Y7Qr68ZYoSSBeEVyKI1QgAAZnlEiyqkzXo", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1MCJLAqKP0Y7Qr68ZYoSSBeEVyKI1QgAAZnlEiyqkzXo/edit?usp=drivesdk", "cachedResultName": "<PERSON><PERSON><PERSON> with session"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "be9c755a-0c52-453c-b448-98975d4c6e87", "name": "Session activated", "type": "n8n-nodes-base.telegram", "position": [1240, -760], "webhookId": "fa5e7641-068c-40cb-b490-a6809e74c629", "parameters": {"text": "New session activated", "chatId": "={{ $('Get message').item.json.message.from.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "rQ5q95W7uKesMDx4", "name": "Telegram account Fastewb"}}, "typeVersion": 1.2}, {"id": "31478ec5-c1de-43db-ac9d-9b34f68e951c", "name": "Send response", "type": "n8n-nodes-base.telegram", "position": [1100, 980], "webhookId": "fa5e7641-068c-40cb-b490-a6809e74c629", "parameters": {"text": "={{ $json.output }}", "chatId": "={{ $('Get message').item.json.message.from.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "rQ5q95W7uKesMDx4", "name": "Telegram account Fastewb"}}, "typeVersion": 1.2}, {"id": "be35b9a6-2914-409f-bc9c-6199ccb9e4ed", "name": "Update database", "type": "n8n-nodes-base.googleSheets", "position": [1420, 980], "parameters": {"columns": {"value": {"DATE": "={{$now}}", "PROMPT": "={{ $('Get message').item.json.message.text }}", "SESSION": "={{ $('Get session').item.json.SESSION }}", "RESPONSE": "={{ $('<PERSON><PERSON><PERSON>bot').item.json.output }}"}, "schema": [{"id": "SESSION", "type": "string", "display": true, "required": false, "displayName": "SESSION", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "DATE", "type": "string", "display": true, "required": false, "displayName": "DATE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "PROMPT", "type": "string", "display": true, "required": false, "displayName": "PROMPT", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "RESPONSE", "type": "string", "display": true, "required": false, "displayName": "RESPONSE", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1MCJLAqKP0Y7Qr68ZYoSSBeEVyKI1QgAAZnlEiyqkzXo/edit#gid=0", "cachedResultName": "Database"}, "documentId": {"__rl": true, "mode": "list", "value": "1MCJLAqKP0Y7Qr68ZYoSSBeEVyKI1QgAAZnlEiyqkzXo", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1MCJLAqKP0Y7Qr68ZYoSSBeEVyKI1QgAAZnlEiyqkzXo/edit?usp=drivesdk", "cachedResultName": "<PERSON><PERSON><PERSON> with session"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "dacc79e0-56ff-47d6-b09f-16ebfb31cbfa", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-540, -540], "parameters": {"color": 3, "width": 620, "height": 360, "content": "# Telegram ChatBot with multiple sessions\n\nThis workflow creates an **AI-powered Telegram chatbot** with **session management**, allowing users to:  \n- **Start new conversations** (`/new`).  \n- **Check current sessions** (`/current`).  \n- **Resume past sessions** (`/resume`).  \n- **Get summaries** (`/summary`).  \n- **Ask questions** (`/question`). \n\n- <PERSON>lone [this sheet](https://docs.google.com/spreadsheets/d/1MCJLAqKP0Y7Qr68ZYoSSBeEVyKI1QgAAZnlEiyqkzXo/edit?usp=sharing)"}, "typeVersion": 1}, {"id": "55751f99-ee2e-429b-99cd-fef30eb56fb8", "name": "Summarization Chain", "type": "@n8n/n8n-nodes-langchain.chainSummarization", "position": [1040, 200], "parameters": {"options": {"summarizationMethodAndPrompts": {"values": {"prompt": "=Write a concise summary of the following:\n\n\n\"{{ $json.fullText }}\"\n\n\nCONCISE SUMMARY:", "combineMapPrompt": "=Write a concise summary of the following:\n\n\n\"{{ $json.fullText }}\"\n\n\nCONCISE SUMMARY:"}}}}, "typeVersion": 2}, {"id": "c7f0d136-4a77-4bc8-b2ef-b03fa41ad1ff", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1020, 380], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "4zwP0MSr8zkNvvV9", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "a455cf00-2f72-4377-ac2a-d65ab8c30e31", "name": "OpenAI Chat Model2", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1660, 800], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "4zwP0MSr8zkNvvV9", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "e55082e4-4e65-4fbb-8850-d3b64093eab6", "name": "Basic LLM Chain", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1680, 600], "parameters": {"text": "=Question:\n{{ $json.question }}", "messages": {"messageValues": [{"message": "=You have to answer the questions that are asked by analyzing the following text:\n\n{{ $json.fullText }}"}]}, "promptType": "define"}, "typeVersion": 1.6}, {"id": "1e670eaa-fd9f-4e79-b2a8-bd43240455db", "name": "Get message1", "type": "n8n-nodes-base.set", "position": [600, -100], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "56bf47bc-e84c-4cee-8aa0-927f3d2e31c5", "name": "text", "type": "string", "value": "={{ $('Get message').item.json.message.text }}"}]}}, "typeVersion": 3.4}, {"id": "65771b1e-47fc-439d-a1ea-9901bac047d8", "name": "Set to expire", "type": "n8n-nodes-base.googleSheets", "position": [1040, -100], "parameters": {"columns": {"value": {"STATE": "expire", "SESSION": "={{ $('Get session').item.json.SESSION }}"}, "schema": [{"id": "SESSION", "type": "string", "display": true, "removed": false, "required": false, "displayName": "SESSION", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "STATE", "type": "string", "display": true, "removed": false, "required": false, "displayName": "STATE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["SESSION"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": *********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1MCJLAqKP0Y7Qr68ZYoSSBeEVyKI1QgAAZnlEiyqkzXo/edit#gid=*********", "cachedResultName": "Session"}, "documentId": {"__rl": true, "mode": "list", "value": "1MCJLAqKP0Y7Qr68ZYoSSBeEVyKI1QgAAZnlEiyqkzXo", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1MCJLAqKP0Y7Qr68ZYoSSBeEVyKI1QgAAZnlEiyqkzXo/edit?usp=drivesdk", "cachedResultName": "<PERSON><PERSON><PERSON> with session"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "d18268f6-8792-4a66-aa32-35b596e7fcad", "name": "Exist?", "type": "n8n-nodes-base.if", "position": [1540, -100], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "1710e38c-80d1-49ba-9814-2b2c6b3c3b8d", "operator": {"type": "object", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "6eeb7e4d-8af0-4d71-981d-66a39f4870a3", "name": "OK", "type": "n8n-nodes-base.telegram", "position": [1880, -40], "webhookId": "fa5e7641-068c-40cb-b490-a6809e74c629", "parameters": {"text": "=The current session is {{ $json.SESSION }}", "chatId": "={{ $('Get message').item.json.message.from.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "rQ5q95W7uKesMDx4", "name": "Telegram account Fastewb"}}, "typeVersion": 1.2}, {"id": "1a6b9065-154f-4c43-9406-110636884f3d", "name": "KO", "type": "n8n-nodes-base.telegram", "position": [1880, -200], "webhookId": "fa5e7641-068c-40cb-b490-a6809e74c629", "parameters": {"text": "=This session doesn't exist", "chatId": "={{ $('Get message').item.json.message.from.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "rQ5q95W7uKesMDx4", "name": "Telegram account Fastewb"}}, "typeVersion": 1.2}, {"id": "a2463ecc-fc82-401d-bf1b-6104f4b379e6", "name": "Trim resume", "type": "n8n-nodes-base.code", "position": [820, -100], "parameters": {"jsCode": "for (const item of $input.all()) {\n  const text = item.json.text || '';\n  const match = text.match(/\\/resume\\s+(.*)/);\n\n  if (match) {\n    item.json.resume = match[1].trim();\n  } else {\n    item.json.resume = null; \n  }\n}\n\nreturn $input.all();"}, "typeVersion": 2}, {"id": "c24a0c1d-290f-4e0c-8da9-98de14a65e2b", "name": "Get session1", "type": "n8n-nodes-base.googleSheets", "position": [600, 200], "parameters": {"options": {}, "filtersUI": {"values": [{"lookupValue": "={{ $('Get session').item.json.SESSION }}", "lookupColumn": "SESSION"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1MCJLAqKP0Y7Qr68ZYoSSBeEVyKI1QgAAZnlEiyqkzXo/edit#gid=0", "cachedResultName": "Database"}, "documentId": {"__rl": true, "mode": "list", "value": "1MCJLAqKP0Y7Qr68ZYoSSBeEVyKI1QgAAZnlEiyqkzXo", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1MCJLAqKP0Y7Qr68ZYoSSBeEVyKI1QgAAZnlEiyqkzXo/edit?usp=drivesdk", "cachedResultName": "<PERSON><PERSON><PERSON> with session"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "84cc4b12-ab2b-4509-a2e3-1c4d952904c5", "name": "Prompt + Resume", "type": "n8n-nodes-base.code", "position": [820, 200], "parameters": {"jsCode": "let fullText = '';\n\nfor (const item of $input.all()) {\n  const prompt = item.json.PROMPT || '';\n  const response = item.json.RESPONSE || '';\n  fullText += `PROMPT: ${prompt}\\nRESPONSE: ${response}\\n`;\n}\nconst chat_id=$('Get message').first().json.message.from.id\n\nreturn [{ json: { fullText, chat_id } }];\n"}, "typeVersion": 2}, {"id": "3ae34388-95a0-41d7-8f20-7fa74817dab8", "name": "Send summary", "type": "n8n-nodes-base.telegram", "position": [1420, 200], "webhookId": "fa5e7641-068c-40cb-b490-a6809e74c629", "parameters": {"text": "={{ $json.response.text }}", "chatId": "={{ $('Prompt + Resume').item.json.chat_id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "rQ5q95W7uKesMDx4", "name": "Telegram account Fastewb"}}, "typeVersion": 1.2}, {"id": "d38eb058-29a3-4500-b6de-f700b8f501e9", "name": "Get message2", "type": "n8n-nodes-base.set", "position": [620, 600], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "56bf47bc-e84c-4cee-8aa0-927f3d2e31c5", "name": "text", "type": "string", "value": "={{ $('Get message').item.json.message.text }}"}]}}, "typeVersion": 3.4}, {"id": "ee6713b5-6264-43e8-86a4-f9584049f05b", "name": "Trim question", "type": "n8n-nodes-base.code", "position": [860, 600], "parameters": {"jsCode": "for (const item of $input.all()) {\n  const text = item.json.text || '';\n  const match = text.match(/\\/question\\s+(.*)/);\n\n  if (match) {\n    item.json.question = match[1].trim();\n  } else {\n    item.json.question = null; // oppure \"\" se preferisci\n  }\n}\n\nreturn $input.all();"}, "typeVersion": 2}, {"id": "********-1c41-4977-8f18-75f192866fdd", "name": "Set new current session", "type": "n8n-nodes-base.googleSheets", "position": [1280, -100], "parameters": {"columns": {"value": {"STATE": "current", "SESSION": "={{ $('Trim resume').item.json.resume }}"}, "schema": [{"id": "SESSION", "type": "string", "display": true, "removed": false, "required": false, "displayName": "SESSION", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "STATE", "type": "string", "display": true, "removed": false, "required": false, "displayName": "STATE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["SESSION"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": *********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1MCJLAqKP0Y7Qr68ZYoSSBeEVyKI1QgAAZnlEiyqkzXo/edit#gid=*********", "cachedResultName": "Session"}, "documentId": {"__rl": true, "mode": "list", "value": "1MCJLAqKP0Y7Qr68ZYoSSBeEVyKI1QgAAZnlEiyqkzXo", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1MCJLAqKP0Y7Qr68ZYoSSBeEVyKI1QgAAZnlEiyqkzXo/edit?usp=drivesdk", "cachedResultName": "<PERSON><PERSON><PERSON> with session"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "notesInFlow": false, "typeVersion": 4.5, "alwaysOutputData": true}, {"id": "91ef2595-acca-4efd-8331-3450fa466d33", "name": "Response + Text", "type": "n8n-nodes-base.googleSheets", "position": [1140, 600], "parameters": {"options": {}, "filtersUI": {"values": [{"lookupValue": "={{ $('Get session').item.json.SESSION }}", "lookupColumn": "SESSION"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1MCJLAqKP0Y7Qr68ZYoSSBeEVyKI1QgAAZnlEiyqkzXo/edit#gid=0", "cachedResultName": "Database"}, "documentId": {"__rl": true, "mode": "list", "value": "1MCJLAqKP0Y7Qr68ZYoSSBeEVyKI1QgAAZnlEiyqkzXo", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1MCJLAqKP0Y7Qr68ZYoSSBeEVyKI1QgAAZnlEiyqkzXo/edit?usp=drivesdk", "cachedResultName": "<PERSON><PERSON><PERSON> with session"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "2fbaf240-c01e-48dc-90bf-cc655eb7cd4d", "name": "fullText", "type": "n8n-nodes-base.code", "position": [1440, 600], "parameters": {"jsCode": "let fullText = '';\n\nfor (const item of $input.all()) {\n  const prompt = item.json.PROMPT || '';\n  const response = item.json.RESPONSE || '';\n  fullText += `PROMPT: ${prompt}\\nRESPONSE: ${response}\\n`;\n}\nconst chat_id=$('Get message').first().json.message.from.id;\nconst question=$('Trim question').first().json.question;\n\nreturn [{ json: { fullText, chat_id, question } }];\n"}, "typeVersion": 2}, {"id": "30e29b4f-da86-4cee-b875-7985b5d82b7e", "name": "Send answer", "type": "n8n-nodes-base.telegram", "position": [2080, 600], "webhookId": "fa5e7641-068c-40cb-b490-a6809e74c629", "parameters": {"text": "={{ $json.text }}", "chatId": "={{ $('fullText').item.json.chat_id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "rQ5q95W7uKesMDx4", "name": "Telegram account Fastewb"}}, "typeVersion": 1.2}, {"id": "41e2b5b3-25a2-411c-929a-ad4c22f45cd1", "name": "Send current session", "type": "n8n-nodes-base.telegram", "position": [620, -440], "webhookId": "fa5e7641-068c-40cb-b490-a6809e74c629", "parameters": {"text": "=The current session is {{ $('Get session').item.json.SESSION }}", "chatId": "={{ $('Get message').item.json.message.from.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "rQ5q95W7uKesMDx4", "name": "Telegram account Fastewb"}}, "typeVersion": 1.2}, {"id": "e30bb12b-8e46-4fe4-81b7-72ce036d431f", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [640, -880], "parameters": {"width": 580, "height": 80, "content": "## NEW SESSION\n"}, "typeVersion": 1}, {"id": "7e23ee2d-b4ee-4be8-969b-c713039dacdf", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [620, -560], "parameters": {"width": 580, "height": 80, "content": "## GET CURRENT SESSION\n"}, "typeVersion": 1}, {"id": "5b10cf55-3730-4e0c-8db7-49c82e9b1350", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [600, -220], "parameters": {"width": 580, "height": 80, "content": "## RESUME SESSION\n"}, "typeVersion": 1}, {"id": "f0932a2c-ebf2-4179-972a-073e5edfa50d", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [600, 100], "parameters": {"width": 580, "height": 80, "content": "## GET SUMMARY\n"}, "typeVersion": 1}, {"id": "9d9fdca4-870b-4ef4-9415-2fb750eb6466", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [620, 500], "parameters": {"width": 580, "height": 80, "content": "## SEND QUESTION\n"}, "typeVersion": 1}, {"id": "aa55d92a-ec31-42c3-8b17-fa9d81d99c31", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [640, 860], "parameters": {"width": 580, "height": 80, "content": "## CHATBOT\n"}, "typeVersion": 1}, {"id": "fa4dd49c-881b-41d2-84b4-82cbd3e62d0e", "name": "Telegram <PERSON>", "type": "@n8n/n8n-nodes-langchain.agent", "position": [640, 980], "parameters": {"text": "={{ $('Get message').item.json.message.text }}", "options": {"systemMessage": "=Sei un assitente virtuale:\n\nData e ora corrente: {{ $now }}"}, "promptType": "define"}, "typeVersion": 1.9}], "active": false, "pinData": {}, "settings": {"timezone": "Europe/Rome", "callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1", "executionTimeout": -1}, "versionId": "c6d418c0-aec9-498a-b5c7-318a3e4efe0b", "connections": {"Exist?": {"main": [[{"node": "KO", "type": "main", "index": 0}], [{"node": "OK", "type": "main", "index": 0}]]}, "fullText": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "Get message": {"main": [[{"node": "Get session", "type": "main", "index": 0}]]}, "Get session": {"main": [[{"node": "Command or text?", "type": "main", "index": 0}]]}, "Trim resume": {"main": [[{"node": "Set to expire", "type": "main", "index": 0}]]}, "Get message1": {"main": [[{"node": "Trim resume", "type": "main", "index": 0}]]}, "Get message2": {"main": [[{"node": "Trim question", "type": "main", "index": 0}]]}, "Get session1": {"main": [[{"node": "Prompt + Resume", "type": "main", "index": 0}]]}, "Send response": {"main": [[{"node": "Update database", "type": "main", "index": 0}]]}, "Set to expire": {"main": [[{"node": "Set new current session", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "Telegram <PERSON>", "type": "ai_memory", "index": 0}]]}, "Trim question": {"main": [[{"node": "Response + Text", "type": "main", "index": 0}]]}, "Basic LLM Chain": {"main": [[{"node": "Send answer", "type": "main", "index": 0}]]}, "Prompt + Resume": {"main": [[{"node": "Summarization Chain", "type": "main", "index": 0}]]}, "Response + Text": {"main": [[{"node": "fullText", "type": "main", "index": 0}]]}, "Set new session": {"main": [[{"node": "Session activated", "type": "main", "index": 0}]]}, "Command or text?": {"main": [[{"node": "Disable previous session", "type": "main", "index": 0}], [{"node": "Send current session", "type": "main", "index": 0}], [{"node": "Get message1", "type": "main", "index": 0}], [{"node": "Get session1", "type": "main", "index": 0}], [{"node": "Get message2", "type": "main", "index": 0}], [{"node": "Telegram <PERSON>", "type": "main", "index": 0}]]}, "Telegram Chatbot": {"main": [[{"node": "Send response", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Telegram <PERSON>", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Summarization Chain", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model2": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "Summarization Chain": {"main": [[{"node": "Send summary", "type": "main", "index": 0}]]}, "Set new current session": {"main": [[{"node": "Exist?", "type": "main", "index": 0}]]}, "Disable previous session": {"main": [[{"node": "Set new session", "type": "main", "index": 0}]]}}}