{"meta": {"instanceId": "97d44c78f314fab340d7a5edaf7e2c274a7fbb8a7cd138f53cc742341e706fe7", "templateCredsSetupCompleted": true}, "nodes": [{"id": "1ec0e1ad-0666-417b-b5af-b381b06e126f", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-120, 180], "parameters": {}, "typeVersion": 1}, {"id": "c34a92d3-b4bd-4c2f-9fa0-66832729a31c", "name": "Upload photo", "type": "n8n-nodes-base.httpRequest", "position": [980, 180], "parameters": {"url": "=https://graph.microsoft.com/v1.0/sites/root/drive/root:{{ $json.TARGET_FOLDER }}/{{ $json.FILE_NAME }}:/content", "method": "PUT", "options": {}, "sendBody": true, "contentType": "binaryData", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{ $json.access_token }}"}, {"name": "Content-Type", "value": "application/octet-stream"}]}, "inputDataFieldName": "data"}, "typeVersion": 4.2}, {"id": "49ce594c-83c7-4b47-be03-6811ebdcc57b", "name": "Set config (sensitive data)", "type": "n8n-nodes-base.set", "position": [100, 180], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "399d42f3-41e0-4043-9a57-85771bf5cd07", "name": "TENANT_ID", "type": "string", "value": "00000000-0000-0000-0000-000000000000"}, {"id": "dd63a519-3681-46c4-b122-ab379ed11c42", "name": "CLIENT_ID", "type": "string", "value": "00000000-0000-0000-0000-000000000000"}, {"id": "4d50c934-c306-4198-853a-68198b8b84eb", "name": "CLIENT_SECRET", "type": "string", "value": "uU~8Q~THEQLIE2TX7UsecretT2g_JCADyxBxN0bx3"}]}}, "typeVersion": 3.4}, {"id": "53b78aa9-d86f-461b-bff5-bd2a63a693b5", "name": "Get photo (for testing purposes)", "type": "n8n-nodes-base.httpRequest", "position": [540, 180], "parameters": {"url": "https://fastly.picsum.photos/id/459/200/300.jpg?hmac=4Cn5sZqOdpuzOwSTs65XA75xvN-quC4t9rfYYyoTCEI", "options": {}}, "typeVersion": 4.2}, {"id": "a551951c-f192-4b15-accb-ca936baef9a8", "name": "Set destination", "type": "n8n-nodes-base.set", "position": [760, 180], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9f66b3f9-c161-45f4-bdc0-8cf736b53eda", "name": "TARGET_FOLDER", "type": "string", "value": "/uploads/pictures from n8n"}, {"id": "e8584729-2746-48a0-ad80-d0308a49e195", "name": "FILE_NAME", "type": "string", "value": "example.jpg"}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "66129973-bf5f-4799-b676-2ee40fd2b519", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-240, -220], "parameters": {"width": 320, "height": 200, "content": "## Prerequisites\n1. [Create an application user](https://learn.microsoft.com/en-us/power-platform/admin/manage-application-users)\n2. Ensure the following permissions are set:\n- Sites.ReadWrite.All - for SharePoint site access\n- Files.ReadWrite.All - for file upload operations\n"}, "typeVersion": 1}, {"id": "43bbf2cd-3ac5-4c46-b3c0-bd6158dbe25e", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [160, -280], "parameters": {"width": 320, "height": 340, "content": "## Authentication\nFor a succesful authentication it is required to provide:\n\n- TENANT_ID\n- CLIENT_ID\n- CLIENT_SECRET\n---\n## Attention!\nFor demonstration purposes and template restrictions we store these values in a 'Set' node but in production environment please ensure safety of such data via utilizing credentials, secure vault or any other safe way of storing such information."}, "typeVersion": 1}, {"id": "daa3e6b9-a9ea-4bb4-8e2d-faa516c699ea", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [620, -280], "parameters": {"width": 440, "height": 340, "content": "## Set destination\nIn this step we will set the destination.\nThe destination is made of two parameters:\n\n- TARGET_FOLDER\n- FILE_NAME\n---\n### Example\nLet's say this is our desired file location:\n`https://contoso.sharepoint.com/uploads/pictures from n8n/example.jpg`\n\nThus we will set the following:\n- TARGET_FOLDER = `/uploads/pictures from n8n`\n- FILE_NAME = `example.jpg`\n"}, "typeVersion": 1}, {"id": "52bd314b-6a5e-499a-904e-a7e9becbbd59", "name": "Authentication", "type": "n8n-nodes-base.httpRequest", "notes": "Get an access token for graph API", "position": [320, 180], "parameters": {"url": "=https://login.microsoftonline.com/{{ $json.TENANT_ID }}/oauth2/token", "method": "POST", "options": {}, "sendBody": true, "contentType": "form-urlencoded", "bodyParameters": {"parameters": [{"name": "grant_type", "value": "client_credentials"}, {"name": "client_id", "value": "={{ $json.CLIENT_ID }}"}, {"name": "client_secret", "value": "={{ $json.CLIENT_SECRET }}"}, {"name": "resource", "value": "https://graph.microsoft.com"}]}}, "notesInFlow": true, "typeVersion": 4.2}], "pinData": {}, "connections": {"Authentication": {"main": [[{"node": "Get photo (for testing purposes)", "type": "main", "index": 0}]]}, "Set destination": {"main": [[{"node": "Upload photo", "type": "main", "index": 0}]]}, "Set config (sensitive data)": {"main": [[{"node": "Authentication", "type": "main", "index": 0}]]}, "Get photo (for testing purposes)": {"main": [[{"node": "Set destination", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set config (sensitive data)", "type": "main", "index": 0}]]}}}