{"id": "93", "name": "Create, update, and get activity in Strava", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [270, 340], "parameters": {}, "typeVersion": 1}, {"name": "Strava", "type": "n8n-nodes-base.strava", "position": [470, 340], "parameters": {"name": "Morning Run", "type": "Run", "startDate": "2020-10-01T18:30:00.000Z", "elapsedTime": 3600, "additionalFields": {"distance": 1000}}, "credentials": {"stravaOAuth2Api": "strava"}, "typeVersion": 1}, {"name": "Strava1", "type": "n8n-nodes-base.strava", "position": [670, 340], "parameters": {"operation": "update", "activityId": "={{$node[\"Strava\"].json[\"id\"]}}", "updateFields": {"description": "Morning run in the park"}}, "credentials": {"stravaOAuth2Api": "strava"}, "typeVersion": 1}, {"name": "Strava2", "type": "n8n-nodes-base.strava", "position": [870, 340], "parameters": {"operation": "get", "activityId": "={{$node[\"Strava\"].json[\"id\"]}}"}, "credentials": {"stravaOAuth2Api": "strava"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Strava": {"main": [[{"node": "Strava1", "type": "main", "index": 0}]]}, "Strava1": {"main": [[{"node": "Strava2", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Strava", "type": "main", "index": 0}]]}}}