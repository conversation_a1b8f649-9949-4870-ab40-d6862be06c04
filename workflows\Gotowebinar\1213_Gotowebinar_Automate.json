{"nodes": [{"name": "GoToWebinar2", "type": "n8n-nodes-base.goToWebinar", "position": [930, 280], "parameters": {"resource": "webinar", "webinarKey": "={{$node[\"GoToWebinar\"].json[\"webinarKey\"]}}"}, "credentials": {"goToWebinarOAuth2Api": "GoToWebinar OAuth Credentials"}, "typeVersion": 1}, {"name": "GoToWebinar1", "type": "n8n-nodes-base.goToWebinar", "position": [730, 280], "parameters": {"resource": "webinar", "operation": "update", "webinarKey": "={{$json[\"webinarKey\"]}}", "updateFields": {"description": "Get started with n8n and create your first automation workflow"}}, "credentials": {"goToWebinarOAuth2Api": "GoToWebinar OAuth Credentials"}, "typeVersion": 1}, {"name": "GoToWebinar", "type": "n8n-nodes-base.goToWebinar", "position": [520, 280], "parameters": {"times": {"timesProperties": [{"endTime": "2021-03-02T10:00:00.000Z", "startTime": "2021-03-02T09:00:00.000Z"}]}, "subject": "Getting started with n8n", "resource": "webinar", "operation": "create", "additionalFields": {}}, "credentials": {"goToWebinarOAuth2Api": "GoToWebinar OAuth Credentials"}, "typeVersion": 1}], "connections": {"GoToWebinar": {"main": [[{"node": "GoToWebinar1", "type": "main", "index": 0}]]}, "GoToWebinar1": {"main": [[{"node": "GoToWebinar2", "type": "main", "index": 0}]]}}}