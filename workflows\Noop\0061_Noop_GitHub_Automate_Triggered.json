{"nodes": [{"name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "position": [460, 320], "webhookId": "4d8556a0-8fdf-4228-8ee2-3e3c72f5fc57", "parameters": {"updates": ["message"], "additionalFields": {}}, "credentials": {"telegramApi": ""}, "typeVersion": 1}, {"name": "IF", "type": "n8n-nodes-base.if", "position": [660, 320], "parameters": {"conditions": {"string": [{"value1": "={{$json[\"message\"][\"text\"]}}", "value2": "/deploy", "operation": "contains"}]}}, "typeVersion": 1}, {"name": "GitHub", "type": "n8n-nodes-base.github", "position": [1060, 220], "parameters": {"owner": "n8n-io", "resource": "release", "releaseTag": "={{$json[\"version\"]}}", "repository": "n8n", "authentication": "oAuth2", "additionalFields": {}}, "credentials": {"githubOAuth2Api": ""}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [860, 220], "parameters": {"values": {"string": [{"name": "version", "value": "={{$json[\"message\"][\"text\"].split(' ')[1]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "NoOp", "type": "n8n-nodes-base.noOp", "position": [860, 420], "parameters": {}, "typeVersion": 1}], "connections": {"IF": {"main": [[{"node": "Set", "type": "main", "index": 0}], [{"node": "NoOp", "type": "main", "index": 0}]]}, "Set": {"main": [[{"node": "GitHub", "type": "main", "index": 0}]]}, "Telegram Trigger": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}}}