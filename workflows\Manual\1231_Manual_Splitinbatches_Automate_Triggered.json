{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [430, 310], "parameters": {}, "typeVersion": 1}, {"name": "Function", "type": "n8n-nodes-base.function", "position": [630, 310], "parameters": {"functionCode": "const newItems = [];\n\nfor (let i=0;i<10;i++) {\n  newItems.push({json:{i}});\n}\n\nreturn newItems;"}, "typeVersion": 1}, {"name": "SplitInBatches", "type": "n8n-nodes-base.splitInBatches", "position": [830, 310], "parameters": {"options": {}, "batchSize": 1}, "typeVersion": 1}, {"name": "IF", "type": "n8n-nodes-base.if", "position": [1030, 460], "parameters": {"conditions": {"boolean": [{"value1": true, "value2": "={{$node[\"SplitInBatches\"].context[\"noItemsLeft\"]}}"}]}}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [1230, 360], "parameters": {"values": {"string": [{"name": "Message", "value": "No Items Left"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}], "connections": {"IF": {"main": [[{"node": "Set", "type": "main", "index": 0}], [{"node": "SplitInBatches", "type": "main", "index": 0}]]}, "Function": {"main": [[{"node": "SplitInBatches", "type": "main", "index": 0}]]}, "SplitInBatches": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Function", "type": "main", "index": 0}]]}}}