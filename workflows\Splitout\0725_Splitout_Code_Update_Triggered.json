{"meta": {"instanceId": "6e361bfcd1e8378c9b07774b22409c7eaea7080f01d5248da45077c0c6108b99", "templateCredsSetupCompleted": true}, "nodes": [{"id": "cbc036f7-b0e1-4eb4-94c3-7571c67a1efe", "name": "Code", "type": "n8n-nodes-base.code", "position": [-120, 40], "parameters": {"mode": "runOnceForEachItem", "jsCode": "// Get the input text\nconst text = $input.item.json.text;\n\n// Ensure text is not null or undefined\nif (!text) {\n  throw new Error('Input text is empty');\n}\n\n// Function to split text into sentences while preserving dates and list items\nfunction splitIntoSentences(text) {\n  const monthNames = '(?:Jan<PERSON><PERSON>|Februar|März|April|Mai|Juni|Juli|August|September|Oktober|November|Dezember)';\n  const datePattern = `(?:\\\\d{1,2}\\\\.\\\\s*(?:${monthNames}|\\\\d{1,2}\\\\.)\\\\s*\\\\d{2,4})`;\n  \n  // Split by sentence-ending punctuation, but not within dates or list items\n  const regex = new RegExp(`(?<=[.!?])\\\\s+(?=[A-ZÄÖÜ]|$)(?!${datePattern}|\\\\s*[-•]\\\\s)`, 'g');\n  \n  return text.split(regex)\n    .map(sentence => sentence.trim())\n    .filter(sentence => sentence !== '');\n}\n\n// Split the text into sentences\nconst sentences = splitIntoSentences(text);\n\n// Output a single object with an array of sentences\nreturn { json: { sentences: sentences } };"}, "typeVersion": 2}, {"id": "faae4740-a529-4275-be0e-b079c3bfde58", "name": "Split Out1", "type": "n8n-nodes-base.splitOut", "position": [340, -180], "parameters": {"options": {"destinationFieldName": "claim"}, "fieldToSplitOut": "sentences"}, "typeVersion": 1}, {"id": "c3944f89-e267-4df0-8fc4-9281eac4e759", "name": "Basic LLM Chain4", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [640, -40], "parameters": {"text": "=Document: {{ $('Merge1').item.json.facts }}\nClaim: {{ $json.claim }}", "promptType": "define"}, "typeVersion": 1.5}, {"id": "4e53c7f1-ab9f-42be-a253-9328b209fc68", "name": "<PERSON><PERSON><PERSON> Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOllama", "position": [700, 160], "parameters": {"model": "bespoke-minicheck:latest", "options": {}}, "credentials": {"ollamaApi": {"id": "DeuK54dDNrCCnXHl", "name": "Ollama account"}}, "typeVersion": 1}, {"id": "0252e47e-0e50-4024-92a0-74b554c8cbd1", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-760, 40], "parameters": {}, "typeVersion": 1}, {"id": "8dd3f67c-e36f-4b03-8f9f-9b52ea23e0ed", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [-460, 40], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "55748f38-486f-495f-91ec-02c1d49acf18", "name": "facts", "type": "string", "value": "<PERSON> came to MIT as an assistant professor in MIT’s Department of Electrical Engineering and Computer Science (EECS) eager to focus on ecological challenges. She has fashioned her research career around the opportunity to apply her expertise in computer vision, machine learning, and data science to tackle real-world issues in conservation and sustainability. <PERSON><PERSON> was drawn to the Institute’s commitment to “computing for the planet,” and set out to bring her methods to global-scale environmental and biodiversity monitoring.\n\nIn the Pacific Northwest, salmon have a disproportionate impact on the health of their ecosystems, and their complex reproductive needs have attracted <PERSON><PERSON>’s attention. Each year, millions of salmon embark on a migration to spawn. Their journey begins in freshwater stream beds where the eggs hatch. Young salmon fry (newly hatched salmon) make their way to the ocean, where they spend several years maturing to adulthood. As adults, the salmon return to the streams where they were born in order to spawn, ensuring the continuation of their species by depositing their eggs in the gravel of the stream beds. Both male and female salmon die shortly after supplying the river habitat with the next generation of salmon."}, {"id": "7d8e29db-4a4b-47c5-8c93-fda1e72137a7", "name": "text", "type": "string", "value": "MIT's AI <PERSON>s Salmon Conservation  Professor <PERSON>, a rising star in MIT's Department of Electrical Engineering and Computer Science, is revolutionizing ecological conservation through cutting-edge technology. Specializing in computer vision, machine learning, and data science, <PERSON><PERSON> has set her sights on addressing real-world sustainability challenges.  Her current focus? The vital salmon populations of the Pacific Northwest. These fish play a crucial role in their ecosystems, with their complex life cycle spanning from freshwater streams to the open ocean and back again. <PERSON><PERSON>'s innovative approach uses AI to monitor salmon migration patterns, providing unprecedented insights into their behavior and habitat needs.  <PERSON><PERSON>'s work has led to the development of underwater AI cameras that can distinguish between different salmon species with 99.9% accuracy. Her team has also created a revolutionary \"salmon translator\" that can predict spawning locations based on fish vocalizations.  As climate change threatens these delicate ecosystems, <PERSON><PERSON>'s research offers hope for more effective conservation strategies. By harnessing the power of technology, she's not just studying nature – she's actively working to preserve it for future generations."}]}}, "typeVersion": 3.4}, {"id": "25849b47-1550-464c-9e70-e787712e5765", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [1120, -160], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3}, {"id": "eaea7ef4-a5d5-42b8-b262-e9a4bd6b7281", "name": "Filter", "type": "n8n-nodes-base.filter", "position": [1340, -160], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "20a4ffd6-0dd0-44f9-97bc-7d891f689f4d", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.text }}", "rightValue": "No"}]}}, "typeVersion": 2.2}, {"id": "9f074bdb-b1a6-4c36-be1c-203f78092657", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-760, -200], "parameters": {"workflowInputs": {"values": [{"name": "facts"}, {"name": "text"}]}}, "typeVersion": 1.1}, {"id": "0a08ac40-b497-4f6e-ac2c-2213a00d63f2", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "position": [1560, -160], "parameters": {"options": {}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}, {"id": "b0d79886-01fc-43c7-88fe-a7a5b8b56b35", "name": "Merge1", "type": "n8n-nodes-base.merge", "position": [80, -180], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3}, {"id": "82640408-9db4-4a12-9136-1a22985b609b", "name": "Basic LLM Chain", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1780, -160], "parameters": {"text": "={{ $json.data }}", "messages": {"messageValues": [{"message": "You are a fact-checking assistant. Your task is to analyze a list of statements, each accompanied by a \"yes\" or \"no\" indicating whether the statement is correct. Follow these guidelines:\n\n1. Review Process:\n   a) Carefully read through each statement and its corresponding yes/no answer.\n   b) Identify which statements are marked as incorrect (no).\n   c) Ignore chit-chat sentences or statements that don't contain factual information.\n   d) Count the total number of incorrect factual statements.\n\n2. Statement Classification:\n   - Factual Statements: Contains specific information, data, or claims that can be verified.\n   - Chit-chat/Non-factual: General comments, introductions, or transitions that don't present verifiable facts.\n\n3. Summary Structure:\n   a) Overview: Provide a brief summary of the number of factual errors found.\n   b) List of Problems: Enumerate the incorrect factual statements.\n   c) Final Assessment: Offer a concise evaluation of the overall state of the article's factual accuracy.\n\n4. Prioritization:\n   - Focus only on the factual statements marked as incorrect (no).\n   - Ignore statements marked as correct (yes) and non-factual chit-chat.\n\n5. Feedback Tone:\n   - Maintain a neutral and objective tone.\n   - Present the information factually without additional commentary.\n\n6. Output Format:\n   Present your summary in the following structure:\n\n   ## Problem Summary\n   [Number] incorrect factual statements were identified in the article.\n\n   ## List of Incorrect Factual Statements\n   1. [First incorrect factual statement]\n   2. [Second incorrect factual statement]\n   3. [Third incorrect factual statement]\n   (Continue listing all incorrect factual statements)\n\n   ## Final Assessment\n   Based on the number of incorrect factual statements:\n   - If 0-1 errors: The article appears to be highly accurate and may only need minor factual adjustments.\n   - If 2-3 errors: The article requires some revision to address these factual inaccuracies.\n   - If 4 or more errors: The article needs significant revision to improve its factual accuracy.\n\nRemember, your role is to provide a clear, concise summary of the incorrect factual statements to help the writing team quickly understand what needs to be addressed. Ignore any chit-chat or non-factual statements in your analysis and summary."}]}, "promptType": "define"}, "typeVersion": 1.5}, {"id": "719054ef-0863-4e52-8390-23313c750aac", "name": "Ollama Model", "type": "@n8n/n8n-nodes-langchain.lmOllama", "position": [1880, 60], "parameters": {"model": "qwen2.5:1.5b", "options": {}}, "credentials": {"ollamaApi": {"id": "DeuK54dDNrCCnXHl", "name": "Ollama account"}}, "typeVersion": 1}, {"id": "6595eb25-32ce-49f5-a013-b87d7f3c65d3", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1480, -320], "parameters": {"width": 860, "height": 600, "content": "## Build a summary\n\nThis is useful to run it in an agentic workflow. You may remove the summary part and return the raw array with the found issues."}, "typeVersion": 1}, {"id": "9f6cde97-d2a7-44e4-b715-321ec1e68bd3", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-240, -320], "parameters": {"width": 760, "height": 600, "content": "## Split into sentences"}, "typeVersion": 1}, {"id": "1ceb8f3c-c00b-4496-82b2-20578550c4be", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [540, -320], "parameters": {"width": 920, "height": 600, "content": "## Fact checking\n\nThis use a small ollama model that is specialized on that task: https://ollama.com/library/bespoke-minicheck\n\nYou have to install it before use with `ollama pull bespoke-minicheck`."}, "typeVersion": 1}, {"id": "6e340925-d4e5-4fe1-ba9d-a89a23b68226", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-860, -20], "parameters": {"width": 600, "height": 300, "content": "## Test workflow\n"}, "typeVersion": 1}, {"id": "5561d606-93d2-4887-839d-8ce2230ff30c", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-860, -320], "parameters": {"width": 600, "height": 280, "content": "## Entrypoint to use in other workflows\n"}, "typeVersion": 1}], "pinData": {}, "connections": {"Code": {"main": [[{"node": "Merge1", "type": "main", "index": 1}]]}, "Merge": {"main": [[{"node": "Filter", "type": "main", "index": 0}]]}, "Filter": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Merge1": {"main": [[{"node": "Split Out1", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "Split Out1": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}, {"node": "Basic LLM Chain4", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Code", "type": "main", "index": 0}, {"node": "Merge1", "type": "main", "index": 0}]]}, "Ollama Model": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "Basic LLM Chain4": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Ollama Chat Model": {"ai_languageModel": [[{"node": "Basic LLM Chain4", "type": "ai_languageModel", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Code", "type": "main", "index": 0}, {"node": "Merge1", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}}}