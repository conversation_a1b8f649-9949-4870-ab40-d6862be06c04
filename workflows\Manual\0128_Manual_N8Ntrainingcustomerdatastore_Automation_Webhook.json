{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [450, 300], "parameters": {"values": {"string": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "value": "n8n-secret-keey"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "Customer Datastore", "type": "n8n-nodes-base.n8nTrainingCustomerDatastore", "position": [650, 300], "parameters": {"operation": "getAllPeople", "returnAll": true}, "typeVersion": 1}, {"name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [850, 300], "parameters": {"url": "https://webhook.site/f99d65ab-8959-4466-a427-cdd0ad482220", "options": {}, "requestMethod": "POST", "bodyParametersUi": {"parameter": [{"name": "name", "value": "={{$json[\"name\"]}}"}]}, "headerParametersUi": {"parameter": [{"name": "api-key", "value": "={{ $item(0).$node[\"Set\"].json[\"apiKey\"] }}"}]}}, "typeVersion": 1}], "connections": {"Set": {"main": [[{"node": "Customer Datastore", "type": "main", "index": 0}]]}, "Customer Datastore": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}}