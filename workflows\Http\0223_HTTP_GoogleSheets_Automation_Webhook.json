{"nodes": [{"name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [560, 840], "parameters": {"url": "https://randomuser.me/api/", "options": {}}, "typeVersion": 2}, {"name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [960, 700], "parameters": {"range": "A:C", "options": {"usePathForKeyRow": true}, "sheetId": "qwertz", "operation": "append", "authentication": "oAuth2"}, "credentials": {"googleSheetsOAuth2Api": {"id": "2", "name": "google_sheets_oauth"}}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [760, 840], "parameters": {"values": {"string": [{"name": "name", "value": "={{$json[\"results\"][0][\"name\"][\"first\"]}} {{$json[\"results\"][0][\"name\"][\"last\"]}}"}, {"name": "country", "value": "={{$json[\"results\"][0][\"location\"][\"country\"]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "Spreadsheet File", "type": "n8n-nodes-base.spreadsheetFile", "position": [960, 980], "parameters": {"options": {"fileName": "users_spreadsheet"}, "operation": "toFile", "fileFormat": "csv"}, "typeVersion": 1}, {"name": "Note", "type": "n8n-nodes-base.stickyNote", "position": [1160, 720], "parameters": {"width": 320, "height": 80, "content": "## JSON > Google Sheets"}, "typeVersion": 1}, {"name": "Note1", "type": "n8n-nodes-base.stickyNote", "position": [1140, 980], "parameters": {"width": 320, "height": 80, "content": "## JSON > CSV"}, "typeVersion": 1}], "connections": {"Set": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}, {"node": "Spreadsheet File", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}}