{"id": "wDD4XugmHIvx3KMT", "meta": {"instanceId": "149cdf730f0c143663259ddc6124c9c26e824d8d2d059973b871074cf4bda531"}, "name": "Analyze Screenshots with AI", "tags": [], "nodes": [{"id": "6d7f34b8-6203-4512-a428-7b5a18c63db6", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [240, 1100], "parameters": {"width": 373.2796418305297, "height": 381.1230421279239, "content": "## Setup \n**For Testing use the Setup node to put in test name & url.**\n\nIf you want to use this workflow in production, you can expand it to load data from other sources like a DB or Google Sheet"}, "typeVersion": 1}, {"id": "ae568c65-e8f6-45bb-9c96-a870da1fc7d6", "name": "Setup", "type": "n8n-nodes-base.set", "position": [360, 1320], "parameters": {"values": {"string": [{"name": "website_name", "value": "=n8n"}, {"name": "url", "value": "https://n8n.io/"}]}, "options": {}}, "typeVersion": 2}, {"id": "ca9f0357-a596-4453-b351-fdd8d47c81ad", "name": "URLbox API Request", "type": "n8n-nodes-base.httpRequest", "position": [780, 1120], "parameters": {"url": "https://api.urlbox.io/v1/render/sync", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "bodyParameters": {"parameters": [{"name": "url", "value": "={{ $json.url }}"}, {"name": "full_page", "value": true}]}, "headerParameters": {"parameters": [{"name": "Authorization", "value": "YOUR_API_KEY"}]}}, "retryOnFail": true, "typeVersion": 4.1}, {"id": "3caffa3c-657a-4f74-a3cb-daf7beb67890", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [640, 920], "parameters": {"width": 373.2796418305297, "height": 381.1230421279239, "content": "## URLbox API call \n[URLbox](https://urlbox.com/) is a Screenshot API. With this API you can automate making screenshots based on website url's.\n\nYou have to replace the Placeholder with your API Key"}, "typeVersion": 1}, {"id": "d2b81b41-1497-4733-8130-67f8de0acff4", "name": "Analyze the Screenshot", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1220, 1120], "parameters": {"text": "=Your Input is a Screenshot of a Website.\nDescribe the content of the Website in one sentence.", "options": {}, "resource": "image", "imageUrls": "renderURL", "operation": "analyze"}, "typeVersion": 1.1}, {"id": "68d86931-69bb-4b78-a7fe-44969172672f", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1080, 920], "parameters": {"width": 373.2796418305297, "height": 381.1230421279239, "content": "## Analyze the Screenshot \nAnalyze the screenshot using OpenAI.\n\nAdd your OpenAI Credentials on the top of the node.\n\nThe prompt is an example. Change it based on what you want to extract from the screenshot."}, "typeVersion": 1}, {"id": "8a22fca5-7f06-45fb-a03f-585a7eb35b40", "name": "Merge Name & Description", "type": "n8n-nodes-base.merge", "position": [1620, 1300], "parameters": {"mode": "combine", "options": {}, "combinationMode": "mergeByPosition"}, "typeVersion": 2.1}, {"id": "4f902a0a-ee93-4190-9b1e-ab3fa15eb4aa", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1480, 1200], "parameters": {"width": 371.85912137154685, "height": 300.15337596590155, "content": "## Merge\nMerge the description with the name of the website & the url."}, "typeVersion": 1}, {"id": "8b3eb3f4-b31a-48f0-94bb-35379d07a81f", "name": "Manual Execution", "type": "n8n-nodes-base.manualTrigger", "position": [20, 1320], "parameters": {}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "ff37faa1-c61c-44be-89f0-62f8e1b8317c", "connections": {"Setup": {"main": [[{"node": "URLbox API Request", "type": "main", "index": 0}, {"node": "Merge Name & Description", "type": "main", "index": 1}]]}, "Manual Execution": {"main": [[{"node": "Setup", "type": "main", "index": 0}]]}, "URLbox API Request": {"main": [[{"node": "Analyze the Screenshot", "type": "main", "index": 0}]]}, "Analyze the Screenshot": {"main": [[{"node": "Merge Name & Description", "type": "main", "index": 0}]]}}}