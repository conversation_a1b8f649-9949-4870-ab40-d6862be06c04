{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [-60, 400], "parameters": {}, "typeVersion": 1}, {"name": "Telegram", "type": "n8n-nodes-base.telegram", "position": [500, 400], "parameters": {"text": "Hello", "chatId": "={{$node[\"SplitInBatches\"].json[\"Chat ID\"]}}", "additionalFields": {}}, "credentials": {"telegramApi": {"id": null, "name": "telegram-bot"}}, "typeVersion": 1}, {"name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [120, 400], "parameters": {"range": "A:A", "options": {}, "authentication": "oAuth2"}, "credentials": {"googleSheetsOAuth2Api": {"id": null, "name": "google-sheet"}}, "typeVersion": 1}, {"name": "SplitInBatches", "type": "n8n-nodes-base.splitInBatches", "position": [320, 400], "parameters": {"options": {}, "batchSize": 30}, "typeVersion": 1}, {"name": "Wait1", "type": "n8n-nodes-base.wait", "position": [660, 180], "webhookId": "22fca54c-eac4-44bc-adf7-68b33818695c", "parameters": {"unit": "seconds", "amount": 30}, "typeVersion": 1}], "connections": {"Wait1": {"main": [[{"node": "SplitInBatches", "type": "main", "index": 0}]]}, "Telegram": {"main": [[{"node": "Wait1", "type": "main", "index": 0}]]}, "Google Sheets": {"main": [[{"node": "SplitInBatches", "type": "main", "index": 0}]]}, "SplitInBatches": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}}}