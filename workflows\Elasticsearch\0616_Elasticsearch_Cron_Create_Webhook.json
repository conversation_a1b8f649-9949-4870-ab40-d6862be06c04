{"meta": {"instanceId": "43da491ee7afc3232a99276a123dc774d0498da8891013b60e82828d6f1f40c7"}, "nodes": [{"id": "77af14bb-db74-4069-adcc-d66e3bb3f893", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [400, 300], "parameters": {"triggerTimes": {"item": [{"hour": 12, "minute": 15}]}}, "typeVersion": 1}, {"id": "286b8b82-78c5-458a-b708-79f0b7d1437c", "name": "Elasticsearch Query", "type": "n8n-nodes-base.elasticsearch", "position": [600, 300], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "425719a5-41d2-4f3a-80ba-241620d9f793", "name": "Check for Alerts", "type": "n8n-nodes-base.if", "position": [800, 300], "parameters": {"conditions": {"number": [{"value1": "={{$json[\"hits\"][\"total\"][\"value\"]}}", "operation": "greater"}]}}, "typeVersion": 1}, {"id": "a2c6bd3d-c65d-4653-8183-9525a4c3af79", "name": "Create Work Item", "type": "n8n-nodes-base.httpRequest", "position": [1040, 280], "parameters": {"url": "https://dev.azure.com/<organization>/<project>/_apis/wit/workitems/$Task?api-version=7.1-preview.3", "options": {}, "authentication": "basicAuth", "headerParametersUi": {"parameter": [{"name": "Content-Type", "value": "application/json-patch+json"}]}}, "typeVersion": 1}, {"id": "71ee087f-4f75-4544-b26a-95c7ce12d020", "name": "No Operation, do nothing", "type": "n8n-nodes-base.noOp", "position": [1060, 460], "parameters": {}, "typeVersion": 1}], "pinData": {}, "connections": {"Cron Trigger": {"main": [[{"node": "Elasticsearch Query", "type": "main", "index": 0}]]}, "Check for Alerts": {"main": [[{"node": "Create Work Item", "type": "main", "index": 0}], [{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "Elasticsearch Query": {"main": [[{"node": "Check for Alerts", "type": "main", "index": 0}]]}}}