# Communication & Messaging - N8N Workflows

## Overview
This document catalogs the **Communication & Messaging** workflows from the n8n Community Workflows repository.

**Category:** Communication & Messaging  
**Total Workflows:** 321  
**Generated:** 2025-07-27  
**Source:** https://scan-might-updates-postage.trycloudflare.com/api

---

## Workflows

### #️⃣Nostr #damus AI Powered Reporting + Gmail + Telegram
**Filename:** `0001_Telegram_Schedule_Automation_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, Lmchatgooglegemini, and Telegram for data processing. Uses 24 nodes and integrates with 5 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (24 nodes)  
**Integrations:** Markdown,Lmchatgooglegemini,Telegram,Gmail,Chainllm,  

---

### On new Stripe Invoice Payment update Hubspot and notify the team in Slack
**Filename:** `0008_Slack_Stripe_Create_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>lack to update existing data. Uses 8 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (8 nodes)  
**Integrations:** Hubspot,Stripe,Slack,  

---

### Mattermost Emelia Automate Triggered
**Filename:** `0017_Mattermost_Emelia_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that connects Emelia and Mattermost for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** Emelia,Mattermost,  

---

### Mattermost Emelia Automate Triggered
**Filename:** `0020_Mattermost_Emelia_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that connects Emelia and Mattermost for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** Emelia,Mattermost,  

---

### Mattermost N8n Automate Triggered
**Filename:** `0027_Mattermost_N8N_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that connects N8N and Mattermost for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** N8N,Mattermost,  

---

### Mattermost Workflow Automate Webhook
**Filename:** `0028_Mattermost_Workflow_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Webhook, Workflow, and Mattermost for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Webhook,Workflow,Mattermost,  

---

### Gmail Googledrive Import
**Filename:** `0036_Gmail_GoogleDrive_Import.json`  
**Description:** Manual workflow that connects Gmail and Google Drive for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Gmail,Google Drive,  

---

### Mattermost Noop Automate Triggered
**Filename:** `0040_Mattermost_Noop_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that connects Notion and Mattermost for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Notion,Mattermost,  

---

### Get SSL Certificate
**Filename:** `0045_Manual_Telegram_Import_Triggered.json`  
**Description:** Manual workflow that orchestrates Uproc, Telegram, and Functionitem for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Uproc,Telegram,Functionitem,  

---

### Signl4 Interval Create Scheduled
**Filename:** `0055_Signl4_Interval_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Interval, Notion, and Signl4 to create new records. Uses 13 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (13 nodes)  
**Integrations:** Interval,Notion,Signl4,Webhook,  

---

### Manual Gmail Automation Triggered
**Filename:** `0069_Manual_Gmail_Automation_Triggered.json`  
**Description:** Manual workflow that connects Splitinbatches and Gmail for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Splitinbatches,Gmail,  

---

### Google Calendar to Slack Status & Philips Hue
**Filename:** `0078_Manual_Slack_Monitor_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Cal.com, and Google Calendar for data processing. Uses 9 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (9 nodes)  
**Integrations:** Httprequest,Cal.com,Google Calendar,Slack,  

---

### Get details of a forum in Disqus
**Filename:** `0080_Manual_Disqus_Import_Triggered.json`  
**Description:** Manual workflow that integrates with Disqus for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Disqus,  

---

### Datetime Slack Automate Scheduled
**Filename:** `0087_Datetime_Slack_Automate_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Shopify, Google Sheets, and Datetime for data processing. Uses 9 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (9 nodes)  
**Integrations:** Shopify,Google Sheets,Datetime,Slack,  

---

### Noop Telegram Automate Triggered
**Filename:** `0089_Noop_Telegram_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that connects Telegram and Googleperspective for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Telegram,Googleperspective,  

---

### Noop Gmail Create Triggered
**Filename:** `0094_Noop_Gmail_Create_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Typeform, Hubspot, and Form Trigger to create new records. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Typeform,Hubspot,Form Trigger,  

---

### Create a ticket in Zendesk
**Filename:** `0100_Manual_Zendesk_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Zendesk to create new records. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Zendesk,  

---

### Netlify Slack Automate Triggered
**Filename:** `0105_Netlify_Slack_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that connects Netlify and Slack for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** Netlify,Slack,  

---

### Create a contact in Drift
**Filename:** `0106_Manual_Drift_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Drift to create new records. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Drift,  

---

### Send a private message on Zulip
**Filename:** `0107_Manual_Zulip_Send_Triggered.json`  
**Description:** Manual workflow that integrates with Zulip for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Zulip,  

---

### Slack Cron Automate Scheduled
**Filename:** `0109_Slack_Cron_Automate_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Cal.com, Datetime, and Slack for data processing. Uses 12 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (12 nodes)  
**Integrations:** Cal.com,Datetime,Slack,  

---

### Graphql Discord Automate Scheduled
**Filename:** `0116_Graphql_Discord_Automate_Scheduled.json`  
**Description:** Scheduled automation that orchestrates GraphQL, Discord, and Itemlists for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (5 nodes)  
**Integrations:** GraphQL,Discord,Itemlists,  

---

### Receive a Mattermost message when a user updates their profile on Facebook
**Filename:** `0123_Facebook_Mattermost_Update_Triggered.json`  
**Description:** Webhook-triggered automation that connects Facebook and Mattermost to update existing data. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** Facebook,Mattermost,  

---

### Slack Typeform Create Triggered
**Filename:** `0124_Slack_Typeform_Create_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Airtable, Typeform, and Dropcontact to create new records. Uses 10 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** Airtable,Typeform,Dropcontact,Slack,  

---

### Error Slack Automate Triggered
**Filename:** `0126_Error_Slack_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Slack for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** Slack,  

---

### Analyze the sentiment of feedback and send a message on Mattermost
**Filename:** `0132_Mattermost_Googlecloudnaturallanguage_Send_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Typeform, Googlecloudnaturallanguage, and Mattermost for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Typeform,Googlecloudnaturallanguage,Mattermost,  

---

### Telegram Webhook Automation Webhook
**Filename:** `0140_Telegram_Webhook_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Telegram, Webhook, and MySQL for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Telegram,Webhook,MySQL,  

---

### Awstextract Telegram Automate Triggered
**Filename:** `0148_Awstextract_Telegram_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Airtable, Awstextract, and Telegram for data processing. Uses 4 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Airtable,Awstextract,Telegram,Awss3,  

---

### Telegram Functionitem Create Scheduled
**Filename:** `0158_Telegram_Functionitem_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Readbinaryfile, Writebinaryfile, and Telegram to create new records. Uses 11 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (11 nodes)  
**Integrations:** Readbinaryfile,Writebinaryfile,Telegram,Movebinarydata,Functionitem,Httprequest,  

---

### Send financial metrics monthly to Mattermost
**Filename:** `0169_Mattermost_Profitwell_Send_Scheduled.json`  
**Description:** Scheduled automation that connects Profitwell and Mattermost for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (3 nodes)  
**Integrations:** Profitwell,Mattermost,  

---

### Telegram Wait Automation Scheduled
**Filename:** `0170_Telegram_Wait_Automation_Scheduled.json`  
**Description:** Scheduled automation that connects Httprequest and Telegram for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (7 nodes)  
**Integrations:** Httprequest,Telegram,  

---

### Onfleet Driver signup message in Slack
**Filename:** `0176_Slack_Onfleet_Send_Triggered.json`  
**Description:** Webhook-triggered automation that connects Slack and Onfleet for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** Slack,Onfleet,  

---

### Receive a Mattermost message when new data gets added to Airtable
**Filename:** `0180_Mattermost_Airtable_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Airtable and Mattermost for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** Airtable,Mattermost,  

---

### Rssfeedread Telegram Create Scheduled
**Filename:** `0188_Rssfeedread_Telegram_Create_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Splitinbatches, Telegram, and Rssfeedread to create new records. Uses 11 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (11 nodes)  
**Integrations:** Splitinbatches,Telegram,Rssfeedread,  

---

### Manual Slack Automation Webhook
**Filename:** `0191_Manual_Slack_Automation_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Slack for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Httprequest,Slack,  

---

### Nocodb Telegram Create Webhook
**Filename:** `0193_Nocodb_Telegram_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Telegram, and Nocodb to create new records. Uses 18 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (18 nodes)  
**Integrations:** Httprequest,Telegram,Nocodb,  

---

### Telegram Executecommand Process Webhook
**Filename:** `0201_Telegram_Executecommand_Process_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Readbinaryfile, Executecommand, and Writebinaryfile for data processing. Uses 21 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (21 nodes)  
**Integrations:** Readbinaryfile,Executecommand,Writebinaryfile,Telegram,Httprequest,Spreadsheetfile,  

---

### Create a channel, invite users to the channel, post a message, and upload a file
**Filename:** `0207_Manual_Slack_Create_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Slack to create new records. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (6 nodes)  
**Integrations:** Httprequest,Slack,  

---

### Gmail Movebinarydata Send
**Filename:** `0221_Gmail_Movebinarydata_Send.json`  
**Description:** Manual workflow that orchestrates Spreadsheetfile, Gmail, and Movebinarydata for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Spreadsheetfile,Gmail,Movebinarydata,  

---

### Create and update a channel, and send a message on Twist
**Filename:** `0225_Manual_Twist_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Twist to create new records. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Twist,  

---

### Send the Astronomy Picture of the day daily to a Telegram channel
**Filename:** `0231_Telegram_Nasa_Send_Scheduled.json`  
**Description:** Scheduled automation that connects Nasa and Telegram for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (3 nodes)  
**Integrations:** Nasa,Telegram,  

---

### Get messages with a certain label, remove the label, and add a new one
**Filename:** `0240_Manual_Gmail_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Gmail for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Gmail,  

---

### Telegram AI-bot
**Filename:** `0248_Openai_Telegram_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that connects Telegram and OpenAI for data processing. Uses 16 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** high (16 nodes)  
**Integrations:** Telegram,OpenAI,  

---

### Create a channel, add a member, and post a message to the channel
**Filename:** `0254_Manual_Mattermost_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Mattermost to create new records. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Mattermost,  

---

### Discord AI bot
**Filename:** `0270_Webhook_Discord_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Webhook, Discord, and OpenAI for data processing. Uses 9 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (9 nodes)  
**Integrations:** Webhook,Discord,OpenAI,  

---

### Zendesk Asana Create Webhook
**Filename:** `0274_Zendesk_Asana_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Zendesk, Webhook, and Asana to create new records. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Zendesk,Webhook,Asana,  

---

### Zendesk Github Create Webhook
**Filename:** `0279_Zendesk_GitHub_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Zendesk, Webhook, and GitHub to create new records. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Zendesk,Webhook,GitHub,  

---

### Zendesk Jira Create Webhook
**Filename:** `0280_Zendesk_Jira_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Zendesk, Webhook, and Jira to create new records. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Zendesk,Webhook,Jira,  

---

### Zendesk Hubspot Create Scheduled
**Filename:** `0285_Zendesk_HubSpot_Create_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Zendesk, Hubspot, and Functionitem to create new records. Uses 9 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (9 nodes)  
**Integrations:** Zendesk,Hubspot,Functionitem,  

---

### Zendesk Hubspot Create Scheduled
**Filename:** `0286_Zendesk_HubSpot_Create_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Zendesk, Hubspot, and Functionitem to create new records. Uses 13 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (13 nodes)  
**Integrations:** Zendesk,Hubspot,Functionitem,  

---

### Send a message on Mattermost when an order is created in WooCommerce
**Filename:** `0294_Mattermost_Woocommerce_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Mattermost and Woocommerce to create new records. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** Mattermost,Woocommerce,  

---

### Gender Inclusive Language
**Filename:** `0301_Mattermost_Noop_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that connects Webhook and Mattermost for data processing. Uses 4 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Webhook,Mattermost,  

---

### Create a screenshot of a website and send it to a telegram channel
**Filename:** `0305_Manual_Telegram_Create_Triggered.json`  
**Description:** Manual workflow that connects Uproc and Telegram to create new records. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Uproc,Telegram,  

---

### Gmail Googlecalendartool Send Triggered
**Filename:** `0319_Gmail_Googlecalendartool_Send_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates OpenAI, Agent, and Gmail for data processing. Uses 10 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** OpenAI,Agent,Gmail,Cal.com,Textclassifier,  

---

### Noop Slack Send Webhook
**Filename:** `0327_Noop_Slack_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Webhook, and Agent for data processing. Uses 14 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** OpenAI,Webhook,Agent,Toolwikipedia,Memorybufferwindow,Toolserpapi,Slack,  

---

### Filter Telegram Send Triggered
**Filename:** `0335_Filter_Telegram_Send_Triggered.json`  
**Description:** Webhook-triggered automation that connects Telegram and N8N for data processing. Uses 12 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (12 nodes)  
**Integrations:** Telegram,N8N,  

---

### Blockchain DEX Screener Insights Agent
**Filename:** `0340_Telegram_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Toolhttprequest, OpenAI, and Telegram for data processing. Uses 15 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (15 nodes)  
**Integrations:** Toolhttprequest,OpenAI,Telegram,Agent,Chat,Memorybufferwindow,  

---

### Daily Journal Reminder
**Filename:** `0346_Telegram_Cron_Automation_Scheduled.json`  
**Description:** Scheduled automation that connects Telegram and Form Trigger for data processing. Uses 3 nodes.  
**Status:** Active  
**Trigger:** Scheduled  
**Complexity:** low (3 nodes)  
**Integrations:** Telegram,Form Trigger,  

---

### Send SMS to numbers stored in Airtable with Twilio
**Filename:** `0353_Manual_Twilio_Send_Triggered.json`  
**Description:** Manual workflow that connects Airtable and Twilio for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Airtable,Twilio,  

---

### Send Typeforms leads via Whatsapp (Twilio)
**Filename:** `0354_Twilio_Typeform_Send_Triggered.json`  
**Description:** Webhook-triggered automation that connects Typeform and Twilio for data processing. Uses 3 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Typeform,Twilio,  

---

### Twitter notifications
**Filename:** `0357_Mattermost_Twitter_Automation_Scheduled.json`  
**Description:** Scheduled automation that connects Twitter/X and Mattermost for notifications and alerts. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (5 nodes)  
**Integrations:** Twitter/X,Mattermost,  

---

### cheems
**Filename:** `0360_Discord_Cron_Automation_Scheduled.json`  
**Description:** Scheduled automation that integrates with Discord for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (6 nodes)  
**Integrations:** Discord,  

---

### Telegram Code Update Webhook
**Filename:** `0381_Telegram_Code_Update_Webhook.json`  
**Description:** Scheduled automation that connects Httprequest and Telegram to update existing data. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (7 nodes)  
**Integrations:** Httprequest,Telegram,  

---

### Telegram Wait Create Webhook
**Filename:** `0383_Telegram_Wait_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Telegram, and Httprequest to create new records. Uses 36 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (36 nodes)  
**Integrations:** Splitinbatches,Telegram,Httprequest,Form Trigger,Redis,  

---

### Telegram Code Create Triggered
**Filename:** `0388_Telegram_Code_Create_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Telegram, Spreadsheetfile, and Facebookgraphapi to create new records. Uses 11 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (11 nodes)  
**Integrations:** Telegram,Spreadsheetfile,Facebookgraphapi,  

---

### Code Slack Create Webhook
**Filename:** `0393_Code_Slack_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Airtable, and Cal.com to create new records. Uses 14 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** Httprequest,Airtable,Cal.com,Slack,  

---

### Telegram Wait Send Webhook
**Filename:** `0398_Telegram_Wait_Send_Webhook.json`  
**Description:** Manual workflow that orchestrates Telegram, Email (IMAP), and GitHub for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (7 nodes)  
**Integrations:** Telegram,Email (IMAP),GitHub,  

---

### Intercom Code Create Webhook
**Filename:** `0413_Intercom_Code_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Intercom, and Webhook to create new records. Uses 13 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (13 nodes)  
**Integrations:** Httprequest,Intercom,Webhook,  

---

### Schedule Gmail Send Scheduled
**Filename:** `0417_Schedule_Gmail_Send_Scheduled.json`  
**Description:** Scheduled automation that connects Hubspot and Gmail for data processing. Uses 8 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (8 nodes)  
**Integrations:** Hubspot,Gmail,  

---

### Telegram Automate Triggered
**Filename:** `0419_Telegram_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Telegram, Agent, and OpenAI for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Telegram,Agent,OpenAI,  

---

### Slack Hunter Send Webhook
**Filename:** `0423_Slack_Hunter_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Hunter, and Slack for data processing. Uses 11 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (11 nodes)  
**Integrations:** Httprequest,Hunter,Slack,Form Trigger,  

---

### Telegram Hunter Send Webhook
**Filename:** `0425_Telegram_Hunter_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Telegram, and Hunter for data processing. Uses 12 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** Httprequest,Telegram,Hunter,Form Trigger,  

---

### Error Slack Send Triggered
**Filename:** `0447_Error_Slack_Send_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Slack for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Slack,  

---

### Filter Slack Update Webhook
**Filename:** `0451_Filter_Slack_Update_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Linear, Microsoft Teams, and Slack to update existing data. Uses 12 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** Linear,Microsoft Teams,Slack,OpenAI,  

---

### Error Telegram Send Triggered
**Filename:** `0454_Error_Telegram_Send_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Telegram for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Telegram,  

---

### Error Gmail Send Triggered
**Filename:** `0456_Error_Gmail_Send_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Gmail for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Gmail,  

---

### Telegram Code Create Webhook
**Filename:** `0462_Telegram_Code_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Converttofile, OpenAI, and Telegram to create new records. Uses 43 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (43 nodes)  
**Integrations:** Converttofile,OpenAI,Telegram,Extractfromfile,Outputparserstructured,Httprequest,Chainllm,Outputparserautofixing,  

---

### Telegram Filter Send Scheduled
**Filename:** `0465_Telegram_Filter_Send_Scheduled.json`  
**Description:** Scheduled automation that connects Telegram and N8N for data processing. Uses 10 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (10 nodes)  
**Integrations:** Telegram,N8N,  

---

### Aggregate Gmail Create Triggered
**Filename:** `0472_Aggregate_Gmail_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Splitout, and Gmail to create new records. Uses 19 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (19 nodes)  
**Integrations:** OpenAI,Splitout,Gmail,Outputparserstructured,Chainllm,  

---

### Aggregate Telegram Automate Triggered
**Filename:** `0480_Aggregate_Telegram_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that connects Telegram and OpenAI for data processing. Uses 12 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (12 nodes)  
**Integrations:** Telegram,OpenAI,  

---

### Telegram Code Automation Webhook
**Filename:** `0481_Telegram_Code_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Converttofile, OpenAI, and Telegram for data processing. Uses 14 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** Converttofile,OpenAI,Telegram,Gmail,Httprequest,Chainsummarization,  

---

### Schedule Telegram Create Scheduled
**Filename:** `0486_Schedule_Telegram_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Telegram, Chainsummarization, and OpenAI to create new records. Uses 14 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** Telegram,Chainsummarization,OpenAI,Textsplitterrecursivecharactertextsplitter,  

---

### Schedule Telegram Create Scheduled
**Filename:** `0487_Schedule_Telegram_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Telegram, Chainsummarization, and OpenAI to create new records. Uses 15 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (15 nodes)  
**Integrations:** Telegram,Chainsummarization,OpenAI,Textsplitterrecursivecharactertextsplitter,  

---

### Telegram Stickynote Update Triggered
**Filename:** `0488_Telegram_Stickynote_Update_Triggered.json`  
**Description:** Webhook-triggered automation that connects Telegram and OpenAI to update existing data. Uses 8 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (8 nodes)  
**Integrations:** Telegram,OpenAI,  

---

### Schedule Slack Create Scheduled
**Filename:** `0526_Schedule_Slack_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Linear, OpenAI, and Outputparserstructured to create new records. Uses 19 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (19 nodes)  
**Integrations:** Linear,OpenAI,Outputparserstructured,Chainllm,Slack,  

---

### Schedule Slack Update Webhook
**Filename:** `0529_Schedule_Slack_Update_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Google Sheets, and Gmail to update existing data. Uses 15 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (15 nodes)  
**Integrations:** Splitinbatches,Google Sheets,Gmail,Form Trigger,Cal.com,Slack,  

---

### Schedule Twilio Create Webhook
**Filename:** `0539_Schedule_Twilio_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Toolhttprequest, OpenAI, and Airtable to create new records. Uses 36 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (36 nodes)  
**Integrations:** Toolhttprequest,OpenAI,Airtable,Agent,Outputparserstructured,Chainllm,Outputparserautofixing,Twilio,  

---

### Gmail Googledrive Create Triggered
**Filename:** `0544_Gmail_GoogleDrive_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Gmail and Google Drive to create new records. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Gmail,Google Drive,  

---

### Slack Stickynote Send Webhook
**Filename:** `0552_Slack_Stickynote_Send_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Lmchatgooglegemini, Webhook, and Agent for data processing. Uses 10 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** Lmchatgooglegemini,Webhook,Agent,Memorybufferwindow,Slack,  

---

### Webhook Slack Automation Webhook
**Filename:** `0565_Webhook_Slack_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Webhook, and Agent for data processing. Uses 20 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (20 nodes)  
**Integrations:** OpenAI,Webhook,Agent,Cal.com,Memorybufferwindow,Slack,  

---

### Zendesk-to-slack
**Filename:** `0568_Manual_Zendesk_Automation_Scheduled.json`  
**Description:** Scheduled automation that connects Zendesk and Slack for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (5 nodes)  
**Integrations:** Zendesk,Slack,  

---

### Webhook Slack Create Webhook
**Filename:** `0581_Webhook_Slack_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Webhook, and Respondtowebhook to create new records. Uses 38 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (38 nodes)  
**Integrations:** OpenAI,Webhook,Respondtowebhook,Httprequest,Venafitlsprotectcloud,Executeworkflow,Slack,  

---

### Telegram Splitout Automation Webhook
**Filename:** `0585_Telegram_Splitout_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates GitHub, Telegram, and Splitout for data processing. Uses 19 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (19 nodes)  
**Integrations:** GitHub,Telegram,Splitout,Webhook,Httprequest,Slack,  

---

### Telegram Gmailtool Send Triggered
**Filename:** `0599_Telegram_Gmailtool_Send_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Telegram, and Agent for data processing. Uses 15 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (15 nodes)  
**Integrations:** OpenAI,Telegram,Agent,Gmailtool,Cal.com,Memorybufferwindow,Baserowtool,  

---

### Congratulations Workflow
**Filename:** `0610_Noop_Twilio_Automate_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Cal.com, Google Sheets, and Twilio for data processing. Uses 8 nodes.  
**Status:** Active  
**Trigger:** Scheduled  
**Complexity:** medium (8 nodes)  
**Integrations:** Cal.com,Google Sheets,Twilio,  

---

### Filter Slack Send Scheduled
**Filename:** `0612_Filter_Slack_Send_Scheduled.json`  
**Description:** Scheduled automation that connects Notion and Slack for data processing. Uses 24 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** high (24 nodes)  
**Integrations:** Notion,Slack,  

---

### Wait Slack Automate Webhook
**Filename:** `0620_Wait_Slack_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Xml, and Httprequest for data processing. Uses 28 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (28 nodes)  
**Integrations:** Splitinbatches,Xml,Httprequest,Executeworkflow,Slack,  

---

### Wait Slack Monitor Webhook
**Filename:** `0621_Wait_Slack_Monitor_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Xml, and Httprequest for monitoring and reporting. Uses 22 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (22 nodes)  
**Integrations:** Splitinbatches,Xml,Httprequest,Executeworkflow,Slack,  

---

### Webhook Slack Create Webhook
**Filename:** `0644_Webhook_Slack_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Thehiveproject, Webhook, and Respondtowebhook to create new records. Uses 63 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (63 nodes)  
**Integrations:** Thehiveproject,Webhook,Respondtowebhook,Httprequest,Form Trigger,Slack,  

---

### Code Microsoftoutlook Create Webhook
**Filename:** `0670_Code_Microsoftoutlook_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Gmail, and Jira to create new records. Uses 18 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (18 nodes)  
**Integrations:** OpenAI,Gmail,Jira,Httprequest,Outlook,Form Trigger,  

---

### Telegram Splitout Import Webhook
**Filename:** `0676_Telegram_Splitout_Import_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Telegram, Webhook, and Splitout for data processing. Uses 12 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** Telegram,Webhook,Splitout,Lmchatopenai,Chainllm,YouTube,Youtube,  

---

### Gmailtool Splitout Create Webhook
**Filename:** `0677_Gmailtool_Splitout_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Airtable, and Splitout to create new records. Uses 18 nodes and integrates with 10 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (18 nodes)  
**Integrations:** OpenAI,Airtable,Splitout,Agent,Toolworkflow,Webhook,Httprequest,Gmailtool,Googlecalendartool,Executeworkflow,  

---

### Telegram Splitout Create Webhook
**Filename:** `0679_Telegram_Splitout_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Telegram, and Splitout to create new records. Uses 13 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (13 nodes)  
**Integrations:** Lmchatgooglegemini,Telegram,Splitout,Html,Httprequest,Chainllm,  

---

### Webhook Telegram Create Webhook
**Filename:** `0683_Webhook_Telegram_Create_Webhook.json`  
**Description:** Webhook-triggered automation that connects Telegram and Jira to create new records. Uses 8 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (8 nodes)  
**Integrations:** Telegram,Jira,  

---

### Stickynote Gmail Create Triggered
**Filename:** `0689_Stickynote_Gmail_Create_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Memorybufferwindow, Gmail, and OpenAI to create new records. Uses 13 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (13 nodes)  
**Integrations:** Memorybufferwindow,Gmail,OpenAI,  

---

### Telegram Webhook Send Webhook
**Filename:** `0690_Telegram_Webhook_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Form Trigger, and Toolhttprequest for data processing. Uses 35 nodes and integrates with 11 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (35 nodes)  
**Integrations:** Lmchatgooglegemini,Form Trigger,Toolhttprequest,Telegram,Webhook,Agent,Gmail,Outputparserstructured,Httprequest,Chat,Outputparserautofixing,  

---

### N8N Español - BOT
**Filename:** `0704_Telegram_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Telegram for data processing. Uses 5 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Telegram,  

---

### Telegram Automate Triggered
**Filename:** `0705_Telegram_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Telegram for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Telegram,  

---

### Schedule Slack Create Scheduled
**Filename:** `0711_Schedule_Slack_Create_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Httprequest, MongoDB, and Slack to create new records. Uses 9 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (9 nodes)  
**Integrations:** Httprequest,MongoDB,Slack,  

---

### Splitout Zendesk Update Triggered
**Filename:** `0714_Splitout_Zendesk_Update_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Textsplittertokensplitter, Splitinbatches, and OpenAI to update existing data. Uses 26 nodes and integrates with 13 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (26 nodes)  
**Integrations:** Textsplittertokensplitter,Splitinbatches,OpenAI,Google Drive,Zendesk,Splitout,Agent,Extractfromfile,Outputparserstructured,Documentdefaultdataloader,Vectorstoreqdrant,Chat,Memorybufferwindow,  

---

### AI Phone Agent with RetellAI
**Filename:** `0735_Telegram_GoogleCalendar_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Textsplittertokensplitter, OpenAI, and Google Drive for data processing. Uses 36 nodes and integrates with 13 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (36 nodes)  
**Integrations:** Textsplittertokensplitter,OpenAI,Google Drive,Telegram,Webhook,Agent,Outputparserstructured,Httprequest,Documentdefaultdataloader,Vectorstoreqdrant,Chainllm,Cal.com,Toolvectorstore,  

---

### YT AI News Playlist Creator/AI News Form Updater
**Filename:** `0742_Telegram_Splitout_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Telegram, Google Sheets, and Splitout to update existing data. Uses 23 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (23 nodes)  
**Integrations:** Telegram,Google Sheets,Splitout,Httprequest,Youtube,  

---

### Discord Intro
**Filename:** `0746_Manual_Discord_Automation_Triggered.json`  
**Description:** Manual workflow that integrates with Discord for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Discord,  

---

### RSS to Telegram
**Filename:** `0748_Noop_Telegram_Automation_Scheduled.json`  
**Description:** Scheduled automation that connects Telegram and Rssfeedread for data processing. Uses 7 nodes.  
**Status:** Active  
**Trigger:** Scheduled  
**Complexity:** medium (7 nodes)  
**Integrations:** Telegram,Rssfeedread,  

---

### Slack Comparedatasets Create Triggered
**Filename:** `0761_Slack_Comparedatasets_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Executeworkflow, Cal.com, and Notion to create new records. Uses 25 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (25 nodes)  
**Integrations:** Executeworkflow,Cal.com,Notion,Slack,  

---

### Telegram Stickynote Create Triggered
**Filename:** `0768_Telegram_Stickynote_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Telegram, and Outputparserstructured to create new records. Uses 13 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (13 nodes)  
**Integrations:** OpenAI,Telegram,Outputparserstructured,Chainllm,Todoist,  

---

### Telegram Webhook Send Webhook
**Filename:** `0769_Telegram_Webhook_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, OpenAI, and Airtabletool for data processing. Uses 35 nodes and integrates with 12 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (35 nodes)  
**Integrations:** Splitinbatches,OpenAI,Airtabletool,Telegram,Webhook,Agent,Toolworkflow,Airtable,Httprequest,Executeworkflow,Cal.com,Memorybufferwindow,  

---

### Telegram Redis Create Webhook
**Filename:** `0782_Telegram_Redis_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Memorymanager, Memoryredischat, and Telegram to create new records. Uses 40 nodes and integrates with 10 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (40 nodes)  
**Integrations:** Memorymanager,Memoryredischat,Telegram,Google Sheets,Agent,Lmchatopenai,Executiondata,Httprequest,Redis,Textclassifier,  

---

### Telegram Code Create Triggered
**Filename:** `0789_Telegram_Code_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Telegram, Gmail, and Google Drive to create new records. Uses 37 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (37 nodes)  
**Integrations:** Telegram,Gmail,Google Drive,Form Trigger,  

---

### Stickynote Gmail Create Triggered
**Filename:** `0796_Stickynote_Gmail_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Gmail, and Emailsend to create new records. Uses 16 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (16 nodes)  
**Integrations:** Lmchatgooglegemini,Gmail,Emailsend,Sendinblue,Textclassifier,  

---

### Aggregate Telegram Automate Triggered
**Filename:** `0800_Aggregate_Telegram_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates OpenAI, Telegram, and Google Sheets for data processing. Uses 10 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** OpenAI,Telegram,Google Sheets,Agent,Memorybufferwindow,  

---

### Telegram Wait Send Triggered
**Filename:** `0807_Telegram_Wait_Send_Triggered.json`  
**Description:** Webhook-triggered automation that connects Telegram and Google Sheets for data processing. Uses 23 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (23 nodes)  
**Integrations:** Telegram,Google Sheets,  

---

### Check To Do on Notion and send message on Slack
**Filename:** `0809_Noop_Slack_Send_Scheduled.json`  
**Description:** Scheduled automation that connects Notion and Slack for data processing. Uses 6 nodes.  
**Status:** Active  
**Trigger:** Scheduled  
**Complexity:** medium (6 nodes)  
**Integrations:** Notion,Slack,  

---

### Telegram Code Update Triggered
**Filename:** `0815_Telegram_Code_Update_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Memorymanager, Memoryredischat, and OpenAI to update existing data. Uses 39 nodes and integrates with 10 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (39 nodes)  
**Integrations:** Memorymanager,Memoryredischat,OpenAI,Telegram,Lmchatopenai,Agent,Toolworkflow,Form Trigger,Executeworkflow,Redis,  

---

### Receive updates for support in Zendesk
**Filename:** `0823_Zendesk_Update_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Zendesk to update existing data. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Zendesk,  

---

### n8n_check
**Filename:** `0824_Telegram_Rssfeedread_Monitor_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Awsses, Telegram, and Rssfeedread for data processing. Uses 7 nodes.  
**Status:** Active  
**Trigger:** Scheduled  
**Complexity:** medium (7 nodes)  
**Integrations:** Awsses,Telegram,Rssfeedread,  

---

### Extractfromfile Gmail Send Triggered
**Filename:** `0828_Extractfromfile_Gmail_Send_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Agent, and Gmail for data processing. Uses 14 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** OpenAI,Agent,Gmail,Extractfromfile,Outputparserstructured,Form Trigger,  

---

### Webhook Slack Create Webhook
**Filename:** `0834_Webhook_Slack_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Webhook, and Respondtowebhook to create new records. Uses 16 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (16 nodes)  
**Integrations:** OpenAI,Webhook,Respondtowebhook,Outlook,Cal.com,Memorybufferwindow,Slack,  

---

### Microsoftoutlook Schedule Automation Scheduled
**Filename:** `0835_Microsoftoutlook_Schedule_Automation_Scheduled.json`  
**Description:** Scheduled automation that connects Outlook and MySQL for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (6 nodes)  
**Integrations:** Outlook,MySQL,  

---

### Twilio Stickynote Send Triggered
**Filename:** `0841_Twilio_Stickynote_Send_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Airtabletool, Lmchatopenai, and Cal.com for data processing. Uses 12 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** Airtabletool,Lmchatopenai,Cal.com,Agent,Twilio,Memorybufferwindow,  

---

### Monitoring and alerting
**Filename:** `0842_Twilio_Cron_Send_Scheduled.json`  
**Description:** Scheduled automation that connects PostgreSQL and Twilio for notifications and alerts. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (5 nodes)  
**Integrations:** PostgreSQL,Twilio,  

---

### Mattermost Pagerduty Automate Webhook
**Filename:** `0850_Mattermost_Pagerduty_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Pagerduty, Mattermost, and Webhook for data processing. Uses 14 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** Pagerduty,Mattermost,Webhook,Jira,  

---

### Gmail Googlesheets Create Triggered
**Filename:** `0852_Gmail_GoogleSheets_Create_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates OpenAI, Google Sheets, and Agent to create new records. Uses 7 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** OpenAI,Google Sheets,Agent,Extractfromfile,Gmail,Outputparserstructured,  

---

### Mattermost Pagerduty Automate Webhook
**Filename:** `0855_Mattermost_Pagerduty_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Pagerduty, Webhook, and Mattermost for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Pagerduty,Webhook,Mattermost,  

---

### Mattermost Webhook Automate Webhook
**Filename:** `0857_Mattermost_Webhook_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Pagerduty, Webhook, and Jira for data processing. Uses 5 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Pagerduty,Webhook,Jira,Mattermost,  

---

### Telegram Splitout Create Triggered
**Filename:** `0864_Telegram_Splitout_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Telegram, and Splitout to create new records. Uses 23 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (23 nodes)  
**Integrations:** OpenAI,Telegram,Splitout,Agent,Google Sheets,Outputparserstructured,Chat,  

---

### Mattermost Twilio Automate Triggered
**Filename:** `0865_Mattermost_Twilio_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that connects Twilio and Mattermost for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Twilio,Mattermost,  

---

### Telegram Googletaskstool Create Triggered
**Filename:** `0882_Telegram_Googletaskstool_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Googletaskstool, OpenAI, and Telegram to create new records. Uses 21 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (21 nodes)  
**Integrations:** Googletaskstool,OpenAI,Telegram,Agent,Mcp,Mcpclienttool,Memorybufferwindow,  

---

### Telegram Filter Export Triggered
**Filename:** `0884_Telegram_Filter_Export_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatopenrouter, Telegram, and Agent for data processing. Uses 17 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** Lmchatopenrouter,Telegram,Agent,Box,Server-Sent Events,Gmailtool,  

---

### Telegram Mondaycom Automate Triggered
**Filename:** `0885_Telegram_Mondaycom_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Monday.com, Telegram, and Freshdesk for data processing. Uses 8 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (8 nodes)  
**Integrations:** Monday.com,Telegram,Freshdesk,  

---

### Telegram Gmail Create Triggered
**Filename:** `0916_Telegram_Gmail_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Telegram, and Gmail to create new records. Uses 24 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (24 nodes)  
**Integrations:** OpenAI,Telegram,Gmail,Outputparserstructured,Box,Chainllm,  

---

### N8N Financial Tracker Telegram Invoices to Notion with AI Summaries & Reports
**Filename:** `0931_Telegram_Splitout_Monitor_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Editimage, and Telegram for data processing. Uses 28 nodes and integrates with 8 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (28 nodes)  
**Integrations:** Lmchatgooglegemini,Editimage,Telegram,Splitout,Outputparserstructured,Chainllm,Quickchart,Notion,  

---

### Orlen
**Filename:** `0940_Slack_Manual_Automation_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Gmail, Google Drive, and Slack for data processing. Uses 10 nodes.  
**Status:** Active  
**Trigger:** Scheduled  
**Complexity:** medium (10 nodes)  
**Integrations:** Gmail,Google Drive,Slack,  

---

### StatsInstagram
**Filename:** `0941_Mattermost_GoogleSheets_Automation_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Datetime, Google Sheets, and Mattermost for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (5 nodes)  
**Integrations:** Datetime,Google Sheets,Mattermost,  

---

### TheHive
**Filename:** `0942_Webhook_Signl4_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Thehive, Webhook, and Signl4 for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Thehive,Webhook,Signl4,  

---

### rss-telegram
**Filename:** `0944_Telegram_Rssfeedread_Automation_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Telegram, and Instagram for data processing. Uses 18 nodes and integrates with 4 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (18 nodes)  
**Integrations:** Splitinbatches,Telegram,Instagram,Rssfeedread,  

---

### A workflow with the Twilio node
**Filename:** `0949_Manual_Twilio_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Twilio for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Twilio,  

---

### Manual Signl4 Automate Triggered
**Filename:** `0959_Manual_Signl4_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Signl4 for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Signl4,  

---

### Manual Freshdesk Automate Triggered
**Filename:** `0960_Manual_Freshdesk_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Freshdesk for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Freshdesk,  

---

### Zammad Open Tickets
**Filename:** `0975_Manual_Zulip_Automation_Scheduled.json`  
**Description:** Scheduled automation that connects Zulip and Zammad for data processing. Uses 5 nodes.  
**Status:** Active  
**Trigger:** Scheduled  
**Complexity:** low (5 nodes)  
**Integrations:** Zulip,Zammad,  

---

### Manual Awssns Automate Triggered
**Filename:** `0981_Manual_Awssns_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Awssns for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Awssns,  

---

### Awssns Automate Triggered
**Filename:** `0984_Awssns_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Awssns for data processing. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Awssns,  

---

### MCP Client with Brave and Telegram
**Filename:** `1001_Telegram_Stickynote_Automation_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Telegram for data processing. Uses 14 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (14 nodes)  
**Integrations:** Telegram,  

---

### Manual Helpscout Automate Triggered
**Filename:** `1034_Manual_Helpscout_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Helpscout for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Helpscout,  

---

### Send an SMS when a workflow fails
**Filename:** `1036_Error_Twilio_Send_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Twilio for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** Twilio,  

---

### Send an SMS using the Mocean node
**Filename:** `1057_Manual_Mocean_Send_Triggered.json`  
**Description:** Manual workflow that integrates with Mocean for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Mocean,  

---

### Telegram RAG pdf
**Filename:** `1061_Stopanderror_Telegram_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Telegram, and Chainretrievalqa for data processing. Uses 20 nodes and integrates with 9 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (20 nodes)  
**Integrations:** OpenAI,Telegram,Chainretrievalqa,Vectorstorepinecone,Embeddingsopenai,Documentdefaultdataloader,Textsplitterrecursivecharactertextsplitter,Retrievervectorstore,Lmchatgroq,  

---

### Slack-GitHub User Info
**Filename:** `1063_Slack_Graphql_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates GraphQL, Webhook, and Slack for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** GraphQL,Webhook,Slack,  

---

### bash-dash telegram
**Filename:** `1065_Telegram_Webhook_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that connects Telegram and Webhook for data processing. Uses 3 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Telegram,Webhook,  

---

### 🔍🛠️Generate SEO-Optimized WordPress Content with Perplexity Research
**Filename:** `1070_Telegram_Wordpress_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Telegram, and Lmchatopenai for data processing. Uses 25 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (25 nodes)  
**Integrations:** OpenAI,Telegram,Lmchatopenai,Agent,Outputparserstructured,Wordpress,Httprequest,Form Trigger,  

---

### Mattermost Webhook Automate Webhook
**Filename:** `1077_Mattermost_Webhook_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that connects Webhook and Mattermost for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Webhook,Mattermost,  

---

### Receive updates when a customer is created in HelpScout
**Filename:** `1079_Helpscout_Create_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Helpscout to create new records. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Helpscout,  

---

### Slack Readbinaryfile Create
**Filename:** `1082_Slack_Readbinaryfile_Create.json`  
**Description:** Manual workflow that orchestrates Readbinaryfile, Email (IMAP), and Emailsend to create new records. Uses 9 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (9 nodes)  
**Integrations:** Readbinaryfile,Email (IMAP),Emailsend,Spreadsheetfile,Slack,  

---

### Error Gmail Send Triggered
**Filename:** `1099_Error_Gmail_Send_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Gmail for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** Gmail,  

---

### agente
**Filename:** `1103_Googletaskstool_Telegram_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Googletaskstool, Converttofile, and OpenAI for data processing. Uses 38 nodes and integrates with 11 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (38 nodes)  
**Integrations:** Googletaskstool,Converttofile,OpenAI,Lmchatopenrouter,Telegram,Webhook,Agent,Gmail,PostgreSQL,Telegramtool,Cal.com,  

---

### 🤖🧑‍💻 AI Agent  for Top n8n Creators Leaderboard Reporting
**Filename:** `1113_Telegram_Splitout_Automation_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Readwritefile, Markdown, and Converttofile for data processing. Uses 49 nodes and integrates with 14 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (49 nodes)  
**Integrations:** Readwritefile,Markdown,Converttofile,Lmchatgooglegemini,Google Drive,Lmchatopenai,Splitout,Agent,Toolworkflow,Gmail,Telegram,Httprequest,Chainllm,Executeworkflow,  

---

### Mattermost Googlesheets Automate Scheduled
**Filename:** `1117_Mattermost_GoogleSheets_Automate_Scheduled.json`  
**Description:** Scheduled automation that connects Google Sheets and Mattermost for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (3 nodes)  
**Integrations:** Google Sheets,Mattermost,  

---

### Telegram Wait Automate Triggered
**Filename:** `1127_Telegram_Wait_Automate_Triggered.json`  
**Description:** Manual workflow that orchestrates Splitinbatches, Telegram, and Google Sheets for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Splitinbatches,Telegram,Google Sheets,  

---

### Get the price of BTC in EUR and send an SMS when the price is larger than EUR 9000
**Filename:** `1130_Noop_Twilio_Send_Scheduled.json`  
**Description:** Scheduled automation that connects Twilio and Coingecko for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (5 nodes)  
**Integrations:** Twilio,Coingecko,  

---

### Coffee Bot (Mattermost)
**Filename:** `1137_Mattermost_Cron_Automate_Scheduled.json`  
**Description:** Scheduled automation that connects Cal.com and Mattermost for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (6 nodes)  
**Integrations:** Cal.com,Mattermost,  

---

### DeepSeek v3.1
**Filename:** `1142_Gmailtool_Stickynote_Automation_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Agent, Lmchatdeepseek, and Gmailtool for data processing. Uses 10 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** Agent,Lmchatdeepseek,Gmailtool,Wordpresstool,Notion,  

---

### New WooCommerce product to Slack
**Filename:** `1148_Woocommerce_Slack_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Slack and Woocommerce for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** Slack,Woocommerce,  

---

### New WooCommerce order to Slack
**Filename:** `1151_Woocommerce_Slack_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Slack and Woocommerce for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Slack,Woocommerce,  

---

### New WooCommerce refund to Slack
**Filename:** `1155_Woocommerce_Slack_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Slack and Woocommerce for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Slack,Woocommerce,  

---

### Create a room, invite members from a different room, and send a message in the room we created
**Filename:** `1158_Manual_Matrix_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Matrix to create new records. Uses 8 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (8 nodes)  
**Integrations:** Matrix,  

---

### Creating a meeting with the Zoom node
**Filename:** `1159_Manual_Zoom_Automation_Triggered.json`  
**Description:** Manual workflow that integrates with Zoom for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Zoom,  

---

### Receive_and_analyze_emails_with_rules_in_Sublime_Security
**Filename:** `1161_Code_Slack_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Email (IMAP), Movebinarydata, and Httprequest for data processing. Uses 13 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (13 nodes)  
**Integrations:** Email (IMAP),Movebinarydata,Httprequest,Form Trigger,Slack,  

---

### Sending an SMS with MessageBird
**Filename:** `1166_Manual_Messagebird_Send_Triggered.json`  
**Description:** Manual workflow that integrates with Messagebird for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Messagebird,  

---

### Mattermost Googlesheets Create Scheduled
**Filename:** `1167_Mattermost_GoogleSheets_Create_Scheduled.json`  
**Description:** Manual workflow that orchestrates Interval, Google Sheets, and Mattermost to create new records. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Interval,Google Sheets,Mattermost,  

---

### Check for valid Hubspot contact email
**Filename:** `1172_Slack_HubSpot_Send_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Hubspot, Onesimpleapi, and Slack for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Hubspot,Onesimpleapi,Slack,  

---

### Post RSS feed items from yesterday to Slack
**Filename:** `1176_Rssfeedread_Slack_Automation_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Datetime, Rssfeedread, and Slack for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (6 nodes)  
**Integrations:** Datetime,Rssfeedread,Slack,  

---

### 🤖 Telegram Messaging Agent for Text/Audio/Images
**Filename:** `1182_Telegram_Webhook_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Converttofile, OpenAI, and Telegram for data processing. Uses 39 nodes and integrates with 7 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (39 nodes)  
**Integrations:** Converttofile,OpenAI,Telegram,Webhook,Lmchatopenai,Extractfromfile,Textclassifier,  

---

### 🤖 AI Powered RAG Chatbot for Your Docs + Google Drive + Gemini + Qdrant
**Filename:** `1185_Telegram_Wait_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Textsplittertokensplitter, Splitinbatches, and Lmchatgooglegemini for data processing. Uses 50 nodes and integrates with 17 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (50 nodes)  
**Integrations:** Textsplittertokensplitter,Splitinbatches,Lmchatgooglegemini,OpenAI,Google Drive,Lmchatopenai,Telegram,Webhook,Extractfromfile,Embeddingsopenai,Informationextractor,Agent,Google Docs,Documentdefaultdataloader,Vectorstoreqdrant,Chat,Memorybufferwindow,  

---

### Crypto News & Sentiment
**Filename:** `1186_Rssfeedread_Telegram_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Telegram, Rssfeedread, and Agent for data processing. Uses 30 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (30 nodes)  
**Integrations:** Telegram,Rssfeedread,Agent,OpenAI,  

---

### Post a message to a channel in RocketChat
**Filename:** `1189_Manual_Rocketchat_Send_Triggered.json`  
**Description:** Manual workflow that integrates with Rocket.Chat for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Rocket.Chat,  

---

### Slack Typeform Automate Triggered
**Filename:** `1191_Slack_Typeform_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Airtable, Typeform, and Slack for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Airtable,Typeform,Slack,  

---

### Create a new user in Intercom
**Filename:** `1193_Manual_Intercom_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Intercom to create new records. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Intercom,  

---

### New invoice email notification
**Filename:** `1194_Slack_Emailreadimap_Create.json`  
**Description:** Manual workflow that orchestrates Emailsend, Email (IMAP), and Mindee for notifications and alerts. Uses 6 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (6 nodes)  
**Integrations:** Emailsend,Email (IMAP),Mindee,Slack,  

---

### Send an SMS to a number whenever you go out
**Filename:** `1198_Twilio_Pushcut_Send_Triggered.json`  
**Description:** Webhook-triggered automation that connects Pushcut and Twilio for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** Pushcut,Twilio,  

---

### Manual Discourse Automate Triggered
**Filename:** `1201_Manual_Discourse_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Discourse for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Discourse,  

---

### Typeform Demio Automate Triggered
**Filename:** `1207_Typeform_Demio_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that connects Typeform and Demio for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** Typeform,Demio,  

---

### Mattermost Typeform Automate Triggered
**Filename:** `1215_Mattermost_Typeform_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Typeform, Awscomprehend, and Mattermost for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Typeform,Awscomprehend,Mattermost,  

---

### Mattermost Lemlist Automate Triggered
**Filename:** `1221_Mattermost_Lemlist_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that connects Lemlist and Mattermost for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** Lemlist,Mattermost,  

---

### Coffee Bot (Matrix)
**Filename:** `1236_Matrix_Cron_Automate_Scheduled.json`  
**Description:** Scheduled automation that integrates with Matrix for data processing. Uses 5 nodes.  
**Status:** Active  
**Trigger:** Scheduled  
**Complexity:** low (5 nodes)  
**Integrations:** Matrix,  

---

### google drive to instagram, tiktok and youtube
**Filename:** `1237_Error_Telegram_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Readbinaryfile, Instagram, and Writebinaryfile for data processing. Uses 15 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (15 nodes)  
**Integrations:** Readbinaryfile,Instagram,Writebinaryfile,OpenAI,Google Drive,Telegram,Httprequest,  

---

### Discord Agent
**Filename:** `1242_Discordtool_Stickynote_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Discord, OpenAI, and Agent for data processing. Uses 13 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (13 nodes)  
**Integrations:** Discord,OpenAI,Agent,Chat,Executeworkflow,Memorybufferwindow,  

---

### Telegram ChatBot with multiple sessions
**Filename:** `1244_Telegram_GoogleSheets_Automate_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Telegram, and Google Sheets for data processing. Uses 38 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (38 nodes)  
**Integrations:** OpenAI,Telegram,Google Sheets,Chainllm,Chainsummarization,Memorybufferwindow,  

---

### Gmailtool Splitout Automation Webhook
**Filename:** `1248_Gmailtool_Splitout_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Airtable, and Splitout for data processing. Uses 18 nodes and integrates with 10 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (18 nodes)  
**Integrations:** OpenAI,Airtable,Splitout,Agent,Toolworkflow,Webhook,Httprequest,Gmailtool,Googlecalendartool,Executeworkflow,  

---

### Schedule Telegram Automation Scheduled
**Filename:** `1275_Schedule_Telegram_Automation_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Telegram, Chainsummarization, and OpenAI for data processing. Uses 15 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (15 nodes)  
**Integrations:** Telegram,Chainsummarization,OpenAI,Textsplitterrecursivecharactertextsplitter,  

---

### Schedule Telegram Automation Scheduled
**Filename:** `1276_Schedule_Telegram_Automation_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Telegram, Chainsummarization, and OpenAI for data processing. Uses 14 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** Telegram,Chainsummarization,OpenAI,Textsplitterrecursivecharactertextsplitter,  

---

### 💥AI Social Video Generator with GPT-4, Kling & Blotato —Auto-Post to Instagram, Facebook,, TikTok, Twitter & Pinterest - vide
**Filename:** `1288_Telegram_Wait_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Instagram, Twitter/X, and OpenAI for data processing. Uses 38 nodes and integrates with 9 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (38 nodes)  
**Integrations:** Instagram,Twitter/X,OpenAI,Telegram,Google Sheets,LinkedIn,Httprequest,Form Trigger,Facebook,  

---

### ✍️🌄 Your First Wordpress Content Creator - Quick Start
**Filename:** `1291_Telegram_Code_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, Google Drive, and Lmchatopenai for data processing. Uses 39 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (39 nodes)  
**Integrations:** Markdown,Google Drive,Lmchatopenai,Telegram,Agent,Outputparserstructured,Httprequest,Wordpress,  

---

### Agentic Telegram AI bot with LangChain nodes and new tools
**Filename:** `1300_Telegram_Stickynote_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Toolhttprequest, OpenAI, and Telegram for data processing. Uses 8 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (8 nodes)  
**Integrations:** Toolhttprequest,OpenAI,Telegram,Agent,Telegramtool,Memorybufferwindow,  

---

### Monitor USDT ERC-20 Wallet Balance with Etherscan and Telegram Notifications
**Filename:** `1304_Telegram_Code_Monitor_Webhook.json`  
**Description:** Scheduled automation that connects Httprequest and Telegram for notifications and alerts. Uses 8 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (8 nodes)  
**Integrations:** Httprequest,Telegram,  

---

### All-in-One Telegram/Baserow AI Assistant 🤖🧠 Voice/Photo/Save Notes/Long Term Mem
**Filename:** `1305_Telegram_Splitout_Export_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Converttofile, OpenAI, and Lmchatopenai for data processing. Uses 48 nodes and integrates with 11 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (48 nodes)  
**Integrations:** Converttofile,OpenAI,Lmchatopenai,Telegram,Agent,Extractfromfile,Splitout,PostgreSQL,Baserow,Memorybufferwindow,Baserowtool,  

---

### Code Microsoftoutlook Send Webhook
**Filename:** `1308_Code_Microsoftoutlook_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Gmail, and Jira for data processing. Uses 18 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (18 nodes)  
**Integrations:** OpenAI,Gmail,Jira,Httprequest,Outlook,Form Trigger,  

---

### Analyze the sentiment of feedback and send a message on Mattermost
**Filename:** `1309_Mattermost_Googlecloudnaturallanguage_Send_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Typeform, Googlecloudnaturallanguage, and Mattermost for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Typeform,Googlecloudnaturallanguage,Mattermost,  

---

### Mattermost Typeform Send Triggered
**Filename:** `1310_Mattermost_Typeform_Send_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Typeform, Awscomprehend, and Mattermost for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Typeform,Awscomprehend,Mattermost,  

---

### Telegram Gmailtool Automation Triggered
**Filename:** `1315_Telegram_Gmailtool_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Telegram, and Agent for data processing. Uses 15 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (15 nodes)  
**Integrations:** OpenAI,Telegram,Agent,Gmailtool,Cal.com,Memorybufferwindow,Baserowtool,  

---

### Ask a human
**Filename:** `1318_Slack_Stickynote_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatopenai, Agent, and Toolworkflow for data processing. Uses 17 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** Lmchatopenai,Agent,Toolworkflow,Chat,Executeworkflow,Memorybufferwindow,Slack,  

---

### Aggregate Gmail Send Triggered
**Filename:** `1324_Aggregate_Gmail_Send_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Splitout, and Gmail for data processing. Uses 19 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (19 nodes)  
**Integrations:** OpenAI,Splitout,Gmail,Outputparserstructured,Chainllm,  

---

### Splitout Zendesk Send Triggered
**Filename:** `1332_Splitout_Zendesk_Send_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Textsplittertokensplitter, Splitinbatches, and OpenAI for data processing. Uses 26 nodes and integrates with 13 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (26 nodes)  
**Integrations:** Textsplittertokensplitter,Splitinbatches,OpenAI,Google Drive,Zendesk,Splitout,Agent,Extractfromfile,Outputparserstructured,Documentdefaultdataloader,Vectorstoreqdrant,Chat,Memorybufferwindow,  

---

### Telegram Stickynote Automate Triggered
**Filename:** `1338_Telegram_Stickynote_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that connects Telegram and OpenAI for data processing. Uses 8 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (8 nodes)  
**Integrations:** Telegram,OpenAI,  

---

### Automated Research Report Generation with OpenAI, Wikipedia, Google Search, and Gmail/Telegram
**Filename:** `1341_Telegram_Splitout_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Toolhttprequest, OpenAI, and Google Drive for data processing. Uses 26 nodes and integrates with 12 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (26 nodes)  
**Integrations:** Toolhttprequest,OpenAI,Google Drive,Telegram,Splitout,Agent,Google Sheets,Gmail,Outputparserstructured,Httprequest,Executeworkflow,Memorybufferwindow,  

---

### YouTube to X Post- AlexK1919
**Filename:** `1345_Schedule_Discord_Automation_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Twitter/X, Discord, and OpenAI for data processing. Uses 28 nodes and integrates with 10 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (28 nodes)  
**Integrations:** Twitter/X,Discord,OpenAI,Google Sheets,Gmail,Removeduplicates,Youtube,Cal.com,Toolwikipedia,Slack,  

---

### Forward Filtered Gmail Notifications to Telegram Chat
**Filename:** `1347_Telegram_Gmail_Automation_Triggered.json`  
**Description:** Webhook-triggered automation that connects Telegram and Gmail for notifications and alerts. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Telegram,Gmail,  

---

### Stickynote Gmail Send Triggered
**Filename:** `1353_Stickynote_Gmail_Send_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Memorybufferwindow, Gmail, and OpenAI for data processing. Uses 13 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (13 nodes)  
**Integrations:** Memorybufferwindow,Gmail,OpenAI,  

---

### 🦜✨Use OpenAI to Transcribe Audio + Summarize with AI + Save to Google Drive
**Filename:** `1368_Telegram_Limit_Export_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Google Drive, and Telegram for data processing. Uses 33 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (33 nodes)  
**Integrations:** OpenAI,Google Drive,Telegram,Gmail,Form Trigger,  

---

### Telegram Automate Triggered
**Filename:** `1375_Telegram_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Telegram, Agent, and OpenAI for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Telegram,Agent,OpenAI,  

---

### Telegram-bot AI Da Nang
**Filename:** `1380_Telegram_Code_Automate_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatopenrouter, Telegram, and Google Sheets for data processing. Uses 23 nodes and integrates with 6 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (23 nodes)  
**Integrations:** Lmchatopenrouter,Telegram,Google Sheets,Agent,Chat,Memorybufferwindow,  

---

### Filter Slack Create Webhook
**Filename:** `1383_Filter_Slack_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Linear, Microsoft Teams, and Slack to create new records. Uses 12 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** Linear,Microsoft Teams,Slack,OpenAI,  

---

### Post new Google Calendar events to Telegram
**Filename:** `1384_Telegram_Stickynote_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Cal.com and Telegram for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Cal.com,Telegram,  

---

### Google Analytics: Weekly Report
**Filename:** `1392_Telegram_Googleanalytics_Automation_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Telegram, and Cal.com for data processing. Uses 14 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** OpenAI,Telegram,Cal.com,Emailsend,Google Analytics,  

---

### Slack Stickynote Automate Webhook
**Filename:** `1396_Slack_Stickynote_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Lmchatgooglegemini, Webhook, and Agent for data processing. Uses 10 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** Lmchatgooglegemini,Webhook,Agent,Memorybufferwindow,Slack,  

---

### Schedule Slack Automation Scheduled
**Filename:** `1399_Schedule_Slack_Automation_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Linear, OpenAI, and Outputparserstructured for data processing. Uses 19 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (19 nodes)  
**Integrations:** Linear,OpenAI,Outputparserstructured,Chainllm,Slack,  

---

### DSP Agent
**Filename:** `1404_Aggregate_Telegram_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, OpenAI, and Airtabletool for data processing. Uses 17 nodes and integrates with 10 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** Lmchatgooglegemini,OpenAI,Airtabletool,Telegram,Airtable,Agent,Toolworkflow,Toolwikipedia,Cal.com,Memorybufferwindow,  

---

### Daily meetings summarization with Gemini AI
**Filename:** `1406_Schedule_Slack_Automation_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Lmchatgooglegemini, Cal.com, and Slack for data processing. Uses 9 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (9 nodes)  
**Integrations:** Lmchatgooglegemini,Cal.com,Slack,  

---

### Discord AI bot
**Filename:** `1410_Webhook_Discord_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Webhook, Discord, and OpenAI for data processing. Uses 9 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (9 nodes)  
**Integrations:** Webhook,Discord,OpenAI,  

---

### Telegram Chat with Buffering
**Filename:** `1411_Telegram_Wait_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Telegram, and Agent for data processing. Uses 22 nodes and integrates with 5 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (22 nodes)  
**Integrations:** OpenAI,Telegram,Agent,PostgreSQL,Supabase,  

---

### Dsp agent
**Filename:** `1413_Aggregate_Telegram_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, OpenAI, and Airtabletool for data processing. Uses 18 nodes and integrates with 10 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (18 nodes)  
**Integrations:** Lmchatgooglegemini,OpenAI,Airtabletool,Telegram,Airtable,Agent,Toolworkflow,Toolwikipedia,Cal.com,Memorybufferwindow,  

---

### 📄✨ Easy Wordpress Content Creation from PDF Document + Human In The Loop with Gmail Approval
**Filename:** `1424_Telegram_Code_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, Lmchatopenai, and Telegram for data processing. Uses 27 nodes and integrates with 9 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (27 nodes)  
**Integrations:** Markdown,Lmchatopenai,Telegram,Extractfromfile,Gmail,Httprequest,Wordpress,Chainllm,Form Trigger,  

---

### piepdrive-test
**Filename:** `1435_Code_Slack_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Markdown, OpenAI, and Httprequest for data processing. Uses 8 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (8 nodes)  
**Integrations:** Markdown,OpenAI,Httprequest,Pipedrive,Slack,  

---

### Telegram Code Create Webhook
**Filename:** `1439_Telegram_Code_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Converttofile, OpenAI, and Telegram to create new records. Uses 43 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (43 nodes)  
**Integrations:** Converttofile,OpenAI,Telegram,Extractfromfile,Outputparserstructured,Httprequest,Chainllm,Outputparserautofixing,  

---

### Play with Spotify from Telegram
**Filename:** `1448_Telegram_Stickynote_Automation_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Telegram, Spotify, and OpenAI for data processing. Uses 14 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** medium (14 nodes)  
**Integrations:** Telegram,Spotify,OpenAI,  

---

### Coinmarketcap Price Agent
**Filename:** `1450_Telegram_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Toolhttprequest, OpenAI, and Telegram for data processing. Uses 7 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Toolhttprequest,OpenAI,Telegram,Agent,Memorybufferwindow,  

---

### Speech Support Workflow
**Filename:** `1452_Telegram_Stickynote_Automate_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Memorymanager, and OpenAI for data processing. Uses 22 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (22 nodes)  
**Integrations:** Lmchatgooglegemini,Memorymanager,OpenAI,Telegram,Agent,Memorybufferwindow,  

---

### Generate Instagram Content from Top Trends with AI Image Generation
**Filename:** `1470_Telegram_Code_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Instagram, and OpenAI for data processing. Uses 44 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (44 nodes)  
**Integrations:** Splitinbatches,Instagram,OpenAI,Telegram,PostgreSQL,Httprequest,Facebookgraphapi,  

---

### Get event triggered notifications / updates on preferred messaging channels with TwentyCRM
**Filename:** `1477_Webhook_Slack_Update_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Google Sheets, Gmail, and Slack to update existing data. Uses 11 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (11 nodes)  
**Integrations:** Google Sheets,Gmail,Slack,Webhook,  

---

### Gmail AI auto-responder: create draft replies to incoming emails
**Filename:** `1479_Gmail_Stickynote_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Gmail, and Outputparserstructured to create new records. Uses 12 nodes and integrates with 5 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** OpenAI,Gmail,Outputparserstructured,Server-Sent Events,Chainllm,  

---

### Generate Instagram Content from Top Trends with AI Image Generation
**Filename:** `1482_Telegram_Code_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Instagram, and OpenAI for data processing. Uses 44 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (44 nodes)  
**Integrations:** Splitinbatches,Instagram,OpenAI,Telegram,PostgreSQL,Httprequest,Facebookgraphapi,  

---

### Telegram AI multi-format chatbot
**Filename:** `1485_Telegram_Stickynote_Automate_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Telegram, Agent, and Memorybufferwindow for data processing. Uses 15 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (15 nodes)  
**Integrations:** Telegram,Agent,Memorybufferwindow,OpenAI,  

---

### HR & IT Helpdesk Chatbot with Audio Transcription
**Filename:** `1487_Telegram_Extractfromfile_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Telegram, and Agent for data processing. Uses 27 nodes and integrates with 10 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (27 nodes)  
**Integrations:** OpenAI,Telegram,Agent,Extractfromfile,Vectorstorepgvector,Httprequest,PostgreSQL,Documentdefaultdataloader,Textsplitterrecursivecharactertextsplitter,Toolvectorstore,  

---

### Telegram Splitout Create Webhook
**Filename:** `1490_Telegram_Splitout_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Telegram, and Splitout to create new records. Uses 13 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (13 nodes)  
**Integrations:** Lmchatgooglegemini,Telegram,Splitout,Html,Httprequest,Chainllm,  

---

### Schedule Twilio Automation Webhook
**Filename:** `1492_Schedule_Twilio_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Toolhttprequest, OpenAI, and Airtable for data processing. Uses 36 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (36 nodes)  
**Integrations:** Toolhttprequest,OpenAI,Airtable,Agent,Outputparserstructured,Chainllm,Outputparserautofixing,Twilio,  

---

### 🔍🛠️Perplexity Researcher to HTML Web Page
**Filename:** `1496_Telegram_Webhook_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatopenai, Webhook, and Telegram for data processing. Uses 47 nodes and integrates with 9 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (47 nodes)  
**Integrations:** Lmchatopenai,Webhook,Telegram,Agent,Outputparserstructured,Httprequest,Chainllm,Executeworkflow,Cal.com,  

---

### Webhook Slack Automate Webhook
**Filename:** `1502_Webhook_Slack_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Webhook, and Agent for data processing. Uses 20 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (20 nodes)  
**Integrations:** OpenAI,Webhook,Agent,Cal.com,Memorybufferwindow,Slack,  

---

### Aggregate Telegram Automation Triggered
**Filename:** `1506_Aggregate_Telegram_Automation_Triggered.json`  
**Description:** Webhook-triggered automation that connects Telegram and OpenAI for data processing. Uses 12 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (12 nodes)  
**Integrations:** Telegram,OpenAI,  

---

### Translate Telegram audio messages with AI (55 supported languages) v1
**Filename:** `1515_Telegram_Stickynote_Send_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Telegram, Chainllm, and OpenAI for data processing. Uses 13 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** medium (13 nodes)  
**Integrations:** Telegram,Chainllm,OpenAI,  

---

### Template - SSL Expiry Alert System
**Filename:** `1522_Telegram_Schedule_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Telegram, and Google Sheets for notifications and alerts. Uses 21 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (21 nodes)  
**Integrations:** Httprequest,Telegram,Google Sheets,Gmail,  

---

### Send Telegram Alerts for New WooCommerce Orders
**Filename:** `1525_Webhook_Telegram_Create_Webhook.json`  
**Description:** Webhook-triggered automation that connects Telegram and Webhook for notifications and alerts. Uses 6 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** medium (6 nodes)  
**Integrations:** Telegram,Webhook,  

---

### Summarize Google Drive Documents with Mistral AI and Send via Gmail
**Filename:** `1528_Manual_Gmail_Send_Triggered.json`  
**Description:** Manual workflow that orchestrates Chainsummarization, Gmail, and Lmchatmistralcloud for data processing. Uses 5 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Chainsummarization,Gmail,Lmchatmistralcloud,Google Drive,  

---

### Summarize YouTube Videos & Chat About Content with GPT-4o-mini via Telegram
**Filename:** `1533_Telegram_Splitout_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Lmchatopenai, and Splitout for data processing. Uses 22 nodes and integrates with 11 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (22 nodes)  
**Integrations:** OpenAI,Lmchatopenai,Splitout,Telegram,Webhook,Google Docs,Agent,Googledocstool,Chainllm,YouTube,Memorybufferwindow,  

---

### AI Document Assistant via Telegram + Supabase
**Filename:** `1539_Telegram_Splitout_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Toolthink, and Telegram for data processing. Uses 28 nodes and integrates with 14 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (28 nodes)  
**Integrations:** Lmchatgooglegemini,Toolthink,Telegram,Splitout,Agent,Extractfromfile,Vectorstoresupabase,Documentdefaultdataloader,Textsplitterrecursivecharactertextsplitter,Form Trigger,Openweathermaptool,Embeddingsgooglegemini,Memorybufferwindow,Toolvectorstore,  

---

### Save New Sales Opportunities
**Filename:** `1565_Gmail_Stickynote_Create_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Chainsummarization, Odoo, and Gmail for data processing. Uses 5 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Chainsummarization,Odoo,Gmail,OpenAI,  

---

### WooCommerce AI Chatbot Workflow for Post-Sales Support
**Filename:** `1575_Telegramtool_Woocommercetool_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Textsplittertokensplitter, OpenAI, and Google Drive for data processing. Uses 31 nodes and integrates with 16 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (31 nodes)  
**Integrations:** Textsplittertokensplitter,OpenAI,Google Drive,Lmchatopenai,Agent,Toolworkflow,Httprequest,Documentdefaultdataloader,Vectorstoreqdrant,Telegramtool,Chat,Executeworkflow,Cal.com,Woocommercetool,Memorybufferwindow,Toolvectorstore,  

---

### Slack Stickynote Automate Webhook
**Filename:** `1592_Slack_Stickynote_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Webhook, Chainllm, and Slack for data processing. Uses 11 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (11 nodes)  
**Integrations:** Webhook,Chainllm,Slack,OpenAI,  

---

### n8n update
**Filename:** `1595_Telegram_Schedule_Update_Webhook.json`  
**Description:** Scheduled automation that orchestrates GitHub, Telegram, and Ssh to update existing data. Uses 27 nodes.  
**Status:** Active  
**Trigger:** Scheduled  
**Complexity:** high (27 nodes)  
**Integrations:** GitHub,Telegram,Ssh,  

---

### Parents smart bot
**Filename:** `1596_Telegram_Code_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Toolhttprequest, OpenAI, and Telegram for data processing. Uses 27 nodes and integrates with 11 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (27 nodes)  
**Integrations:** Toolhttprequest,OpenAI,Telegram,Agent,Toolworkflow,Documentdefaultdataloader,Vectorstoreqdrant,Textsplitterrecursivecharactertextsplitter,Toolwikipedia,Cal.com,Memorybufferwindow,  

---

### Telegram Webhook Automation Webhook
**Filename:** `1606_Telegram_Webhook_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Form Trigger, and Toolhttprequest for data processing. Uses 35 nodes and integrates with 11 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (35 nodes)  
**Integrations:** Lmchatgooglegemini,Form Trigger,Toolhttprequest,Telegram,Webhook,Agent,Gmail,Outputparserstructured,Httprequest,Chat,Outputparserautofixing,  

---

### 🤖🧠 AI Agent Chatbot + LONG TERM Memory + Note Storage + Telegram
**Filename:** `1610_Telegram_Googledocs_Automate_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatopenai, Google Docs, and Telegram for data processing. Uses 21 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (21 nodes)  
**Integrations:** Lmchatopenai,Google Docs,Telegram,Googledocstool,Agent,Chat,Memorybufferwindow,  

---

### Gmail MCP Server
**Filename:** `1613_Gmailtool_Stickynote_Automation_Triggered.json`  
**Description:** Webhook-triggered automation that connects Gmailtool and Gmail for data processing. Uses 27 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** high (27 nodes)  
**Integrations:** Gmailtool,Gmail,  

---

### Slack AI Chatbot with RAG for company staff
**Filename:** `1643_Slack_Manual_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Textsplittertokensplitter, OpenAI, and Google Drive for data processing. Uses 21 nodes and integrates with 11 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (21 nodes)  
**Integrations:** Textsplittertokensplitter,OpenAI,Google Drive,Agent,Anthropic,Httprequest,Documentdefaultdataloader,Vectorstoreqdrant,Cal.com,Memorybufferwindow,Slack,  

---

### Slack Stickynote Automate Webhook
**Filename:** `1663_Slack_Stickynote_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Webhook, Chainllm, and Slack for data processing. Uses 11 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (11 nodes)  
**Integrations:** Webhook,Chainllm,Slack,OpenAI,  

---

### Google Calendar Event Reminder
**Filename:** `1679_Telegram_GoogleCalendar_Automation_Scheduled.json`  
**Description:** Scheduled automation that orchestrates OpenAI, Telegram, and Agent for data processing. Uses 9 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (9 nodes)  
**Integrations:** OpenAI,Telegram,Agent,Removeduplicates,Google Calendar,  

---

### Airtop Web Agent
**Filename:** `1681_Airtoptool_Slack_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Airtoptool, Lmchatanthropic, and Agent for data processing. Uses 19 nodes and integrates with 9 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (19 nodes)  
**Integrations:** Airtoptool,Lmchatanthropic,Agent,Toolworkflow,Outputparserstructured,Airtop,Form Trigger,Executeworkflow,Slack,  

---

### Telegram AI-bot
**Filename:** `1685_Openai_Telegram_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that connects Telegram and OpenAI for data processing. Uses 16 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** high (16 nodes)  
**Integrations:** Telegram,OpenAI,  

---

### Telegram AI multi-format chatbot
**Filename:** `1686_Telegram_Stickynote_Automate_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Telegram, Agent, and Memorybufferwindow for data processing. Uses 15 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (15 nodes)  
**Integrations:** Telegram,Agent,Memorybufferwindow,OpenAI,  

---

### Telegram RAG pdf
**Filename:** `1689_Stopanderror_Telegram_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Telegram, and Chainretrievalqa for data processing. Uses 20 nodes and integrates with 9 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (20 nodes)  
**Integrations:** OpenAI,Telegram,Chainretrievalqa,Vectorstorepinecone,Embeddingsopenai,Documentdefaultdataloader,Textsplitterrecursivecharactertextsplitter,Retrievervectorstore,Lmchatgroq,  

---

### Play with Spotify from Telegram
**Filename:** `1690_Telegram_Stickynote_Automation_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Telegram, Spotify, and OpenAI for data processing. Uses 14 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** medium (14 nodes)  
**Integrations:** Telegram,Spotify,OpenAI,  

---

### Translate Telegram audio messages with AI (55 supported languages) v1
**Filename:** `1701_Telegram_Stickynote_Send_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Telegram, Chainllm, and OpenAI for data processing. Uses 13 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** medium (13 nodes)  
**Integrations:** Telegram,Chainllm,OpenAI,  

---

### Agentic Telegram AI bot with LangChain nodes and new tools
**Filename:** `1708_Telegram_Stickynote_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Toolhttprequest, OpenAI, and Telegram for data processing. Uses 8 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (8 nodes)  
**Integrations:** Toolhttprequest,OpenAI,Telegram,Agent,Telegramtool,Memorybufferwindow,  

---

### Ultimate Personal Assistant
**Filename:** `1712_Telegram_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Toolhttprequest, OpenAI, and Telegram for data processing. Uses 15 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (15 nodes)  
**Integrations:** Toolhttprequest,OpenAI,Telegram,Agent,Toolworkflow,Cal.com,Memorybufferwindow,  

---

### template in store
**Filename:** `1715_Error_Telegram_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Readbinaryfile, Instagram, and Writebinaryfile for data processing. Uses 13 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (13 nodes)  
**Integrations:** Readbinaryfile,Instagram,Writebinaryfile,OpenAI,Google Drive,Telegram,Httprequest,  

---

### Webhook Slack Automate Webhook
**Filename:** `1733_Webhook_Slack_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Webhook, and Respondtowebhook for data processing. Uses 38 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (38 nodes)  
**Integrations:** OpenAI,Webhook,Respondtowebhook,Httprequest,Venafitlsprotectcloud,Executeworkflow,Slack,  

---

### 2. Add Beehiiv newsletter subscribers from Gumroad sales
**Filename:** `1741_Telegram_Gumroad_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Gumroad, and Google Sheets for data processing. Uses 10 nodes and integrates with 4 services.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** Httprequest,Gumroad,Google Sheets,Telegram,  

---

### ClockifyBlockiaWorkflow
**Filename:** `1754_Executiondata_Slack_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Toolhttprequest, OpenAI, and Agent for data processing. Uses 16 nodes and integrates with 8 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (16 nodes)  
**Integrations:** Toolhttprequest,OpenAI,Agent,Executiondata,Cal.com,Toolcode,Memorybufferwindow,Slack,  

---

### AI-Driven WooCommerce Product Importer with SEO
**Filename:** `1775_Telegram_Code_Import_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Lmchatopenrouter, and Telegram for data processing. Uses 16 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (16 nodes)  
**Integrations:** Splitinbatches,Lmchatopenrouter,Telegram,Google Sheets,Outputparserstructured,Chainllm,Woocommerce,  

---

### piepdrive-test
**Filename:** `1796_Code_Slack_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Markdown, OpenAI, and Httprequest for data processing. Uses 8 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (8 nodes)  
**Integrations:** Markdown,OpenAI,Httprequest,Pipedrive,Slack,  

---

### Save new Files received on Telegram to Google Drive
**Filename:** `1797_Telegram_GoogleDrive_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Telegram and Google Drive for data processing. Uses 3 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Telegram,Google Drive,  

---

### Telegram-bot AI Da Nang
**Filename:** `1812_Telegram_Code_Automate_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatopenrouter, Telegram, and Google Sheets for data processing. Uses 23 nodes and integrates with 6 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (23 nodes)  
**Integrations:** Lmchatopenrouter,Telegram,Google Sheets,Agent,Chat,Memorybufferwindow,  

---

### Send Discord message from Webflow form submission
**Filename:** `1819_Code_Discord_Send_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Discord, Slack, and Form Trigger for data processing. Uses 13 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (13 nodes)  
**Integrations:** Discord,Slack,Form Trigger,  

---

### 外送記帳
**Filename:** `1835_Manual_Slack_Automation_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Splitinbatches, Gmail, and Slack for data processing. Uses 6 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** medium (6 nodes)  
**Integrations:** Splitinbatches,Gmail,Slack,  

---

### Telegram Manual Automate Triggered
**Filename:** `1841_Telegram_Manual_Automate_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Telegram, and Agent for data processing. Uses 17 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** Lmchatgooglegemini,Telegram,Agent,Gmail,Outputparserstructured,  

---

### e-mail Chatbot with both semantic and structured RAG, using Telegram and Pgvector
**Filename:** `1843_Telegram_Code_Automate_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, OpenAI, and Telegram for data processing. Uses 20 nodes and integrates with 9 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (20 nodes)  
**Integrations:** Splitinbatches,OpenAI,Telegram,Embeddingsollama,Agent,PostgreSQL,Chat,Cal.com,Memorybufferwindow,  

---

### Agent Access Control Template
**Filename:** `1856_Telegram_Stickynote_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Toolhttprequest, OpenAI, and Airtable for data processing. Uses 36 nodes and integrates with 11 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (36 nodes)  
**Integrations:** Toolhttprequest,OpenAI,Airtable,Telegram,Agent,Toolworkflow,Executeworkflow,Cal.com,Toolcode,Toolwikipedia,Memorybufferwindow,  

---

### Monitor ProductHunt
**Filename:** `1859_Schedule_Slack_Monitor_Scheduled.json`  
**Description:** Scheduled automation that connects Airtop and Slack for monitoring and reporting. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (5 nodes)  
**Integrations:** Airtop,Slack,  

---

### Google calendar to Outlook
**Filename:** `1870_Microsoftoutlook_GoogleCalendar_Automation_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Outlook, Google Calendar, and Microsoftoutlook for data processing. Uses 7 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Outlook,Google Calendar,Microsoftoutlook,  

---

### Optimize Prompt
**Filename:** `1877_Telegram_Stickynote_Automation_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates OpenAI, Telegram, and Agent for data processing. Uses 10 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** OpenAI,Telegram,Agent,Executeworkflow,Memorybufferwindow,  

---

### Auto-create and publish AI social videos with Telegram, GPT-4 and Blotato
**Filename:** `1878_Telegram_Wait_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Instagram, Twitter/X, and OpenAI to create new records. Uses 42 nodes and integrates with 9 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (42 nodes)  
**Integrations:** Instagram,Twitter/X,OpenAI,Telegram,Google Sheets,LinkedIn,Httprequest,Form Trigger,Facebook,  

---

### N_01_Simple_Lead_Tracker_Automation_v4
**Filename:** `1879_Wait_Slack_Monitor_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Google Sheets, Gmail, and Hubspot for data processing. Uses 14 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** Google Sheets,Gmail,Hubspot,Slack,  

---

### Daily meetings summarization with Gemini AI
**Filename:** `1891_Schedule_Slack_Automation_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Lmchatgooglegemini, Cal.com, and Slack for data processing. Uses 9 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (9 nodes)  
**Integrations:** Lmchatgooglegemini,Cal.com,Slack,  

---

### ✨🔪 Advanced AI Powered Document Parsing & Text Extraction with Llama Parse
**Filename:** `1904_Telegram_Limit_Process_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Google Drive, Lmchatopenai, and Webhook for data processing. Uses 54 nodes and integrates with 9 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (54 nodes)  
**Integrations:** Google Drive,Lmchatopenai,Webhook,Google Sheets,Gmail,Telegram,Httprequest,Chainllm,Textclassifier,  

---

### Online Marketing Weekly Report
**Filename:** `1905_Telegram_Googleanalytics_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Telegram, and Agent for data processing. Uses 51 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (51 nodes)  
**Integrations:** OpenAI,Telegram,Agent,Toolworkflow,Emailsend,Form Trigger,Executeworkflow,Cal.com,  

---

### MCP_GMAIL
**Filename:** `1909_Gmailtool_Automation_Triggered.json`  
**Description:** Webhook-triggered automation that connects Gmailtool and Gmail for data processing. Uses 5 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Gmailtool,Gmail,  

---

### Discord MCP Server
**Filename:** `1913_Discordtool_Stickynote_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that connects Discordtool and Discord for data processing. Uses 16 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** high (16 nodes)  
**Integrations:** Discordtool,Discord,  

---

### (G) - Email Classification
**Filename:** `1914_Gmail_Stickynote_Send_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Agent, and Gmail for data processing. Uses 15 nodes and integrates with 5 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** medium (15 nodes)  
**Integrations:** Lmchatgooglegemini,Agent,Gmail,Lmchatgroq,Textclassifier,  

---

### CoinMarketCap_AI_Data_Analyst_Agent
**Filename:** `1916_Telegram_Stickynote_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Telegram, Lmchatopenai, and Agent for data processing. Uses 12 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** Telegram,Lmchatopenai,Agent,Toolworkflow,Memorybufferwindow,  

---

### 🌐 Confluence Page AI Powered Chatbot
**Filename:** `1919_Telegram_Splitout_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, Lmchatopenai, and Manualchat for data processing. Uses 16 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (16 nodes)  
**Integrations:** Markdown,Lmchatopenai,Manualchat,Agent,Telegram,Splitout,Httprequest,Memorybufferwindow,  

---

### Outlook
**Filename:** `1925_Microsoftoutlook_Microsoftoutlooktool_Automation_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Agent, Outlook, and Microsoftoutlooktool for data processing. Uses 9 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (9 nodes)  
**Integrations:** Agent,Outlook,Microsoftoutlooktool,OpenAI,  

---

### Automatically Send Daily Meeting List to Telegram
**Filename:** `1932_Schedule_Telegram_Send_Scheduled.json`  
**Description:** Scheduled automation that connects Telegram and Google Calendar for data processing. Uses 8 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (8 nodes)  
**Integrations:** Telegram,Google Calendar,  

---

### #️⃣Nostr #damus AI Powered Reporting + Gmail + Telegram
**Filename:** `1938_Telegram_Schedule_Automation_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, Lmchatgooglegemini, and Telegram for data processing. Uses 24 nodes and integrates with 5 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (24 nodes)  
**Integrations:** Markdown,Lmchatgooglegemini,Telegram,Gmail,Chainllm,  

---

### ✨😃Automated Workflow Backups to Google Drive
**Filename:** `1940_Telegram_Limit_Export_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Converttofile, and Google Drive for data backup operations. Uses 22 nodes and integrates with 5 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (22 nodes)  
**Integrations:** Splitinbatches,Converttofile,Google Drive,Telegram,N8N,  

---

### Telegram echo-bot
**Filename:** `1941_Telegram_Stickynote_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Telegram for data processing. Uses 3 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Telegram,  

---

### send file to kindle through telegram bot
**Filename:** `1944_Microsoftoutlook_Telegram_Send_Triggered.json`  
**Description:** Webhook-triggered automation that connects Telegram and Microsoftoutlook for data processing. Uses 8 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (8 nodes)  
**Integrations:** Telegram,Microsoftoutlook,  

---

### FetchGithubIssues
**Filename:** `1945_Telegram_Schedule_Import_Scheduled.json`  
**Description:** Scheduled automation that connects Telegram and GitHub for data processing. Uses 9 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (9 nodes)  
**Integrations:** Telegram,GitHub,  

---

### Error Handler send Telegram
**Filename:** `1948_Error_Telegram_Send_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Telegram for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Telegram,  

---

### 🧠 Give Your AI Agent Chatbot Long Term Memory Tools Router
**Filename:** `1950_Telegram_Googledocs_Automate_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Google Docs, and Telegram for data processing. Uses 39 nodes and integrates with 9 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (39 nodes)  
**Integrations:** OpenAI,Google Docs,Telegram,Agent,Gmail,Toolworkflow,Chat,Executeworkflow,Memorybufferwindow,  

---

### Send Slack message from Webflow form submission
**Filename:** `1958_Code_Slack_Send_Triggered.json`  
**Description:** Webhook-triggered automation that connects Slack and Form Trigger for data processing. Uses 13 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (13 nodes)  
**Integrations:** Slack,Form Trigger,  

---

### 🐋🤖 DeepSeek AI Agent + Telegram + LONG TERM Memory 🧠
**Filename:** `1975_Telegram_Googledocs_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Telegram, Google Docs, and Agent for data processing. Uses 23 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (23 nodes)  
**Integrations:** Telegram,Google Docs,Agent,Googledocstool,Lmchatopenai,Chat,Memorybufferwindow,  

---

### 🎦💌Advanced YouTube RSS Feed Buddy for Your Favorite Channels
**Filename:** `1982_Telegram_Splitout_Automation_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Rssfeedread, OpenAI, and Telegram for data processing. Uses 41 nodes and integrates with 8 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (41 nodes)  
**Integrations:** Rssfeedread,OpenAI,Telegram,Splitout,Gmail,Httprequest,Chainllm,Form Trigger,  

---

### OpenSea AI-Powered Insights via Telegram
**Filename:** `2004_Telegram_Stickynote_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Telegram, Lmchatopenai, and Agent for data processing. Uses 13 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (13 nodes)  
**Integrations:** Telegram,Lmchatopenai,Agent,Toolworkflow,Chat,Memorybufferwindow,  

---

### MAIA - Health Check
**Filename:** `2005_Telegram_Schedule_Monitor_Webhook.json`  
**Description:** Scheduled automation that orchestrates Httprequest, Telegram, and Google Sheets for data processing. Uses 7 nodes.  
**Status:** Active  
**Trigger:** Scheduled  
**Complexity:** medium (7 nodes)  
**Integrations:** Httprequest,Telegram,Google Sheets,  

---

### Meeting booked - to newsletter and CRM
**Filename:** `2018_Telegram_Cal_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Telegram, Splitout, and Google Sheets for data processing. Uses 9 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (9 nodes)  
**Integrations:** Telegram,Splitout,Google Sheets,Httprequest,Cal.com,  

---

### Discord Hunter Automate Triggered
**Filename:** `2028_Discord_Hunter_Automate_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Discord, Hunter, and Google Sheets for data processing. Uses 12 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** Discord,Hunter,Google Sheets,Gmail,Form Trigger,  

---

### 📦 New Email ➔ Create Google Task
**Filename:** `2031_Googletasks_Gmail_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Gmail and Google Tasks to create new records. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Gmail,Google Tasks,  

---

### HR & IT Helpdesk Chatbot with Audio Transcription
**Filename:** `2038_Telegram_Extractfromfile_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Telegram, and Agent for data processing. Uses 27 nodes and integrates with 10 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (27 nodes)  
**Integrations:** OpenAI,Telegram,Agent,Extractfromfile,Vectorstorepgvector,Httprequest,PostgreSQL,Documentdefaultdataloader,Textsplitterrecursivecharactertextsplitter,Toolvectorstore,  

---

### Telegram Splitout Automation Webhook
**Filename:** `2040_Telegram_Splitout_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Telegram, Webhook, and Splitout for data processing. Uses 12 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** Telegram,Webhook,Splitout,Lmchatopenai,Chainllm,YouTube,Youtube,  

---

### 🐋🤖 DeepSeek AI Agent + Telegram + LONG TERM Memory 🧠
**Filename:** `2044_Telegram_Googledocs_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Telegram, Google Docs, and Agent for data processing. Uses 23 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (23 nodes)  
**Integrations:** Telegram,Google Docs,Agent,Googledocstool,Lmchatopenai,Chat,Memorybufferwindow,  

---

### 🤖 Telegram Messaging Agent for Text/Audio/Images
**Filename:** `2051_Telegram_Webhook_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Converttofile, OpenAI, and Telegram for data processing. Uses 39 nodes and integrates with 7 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (39 nodes)  
**Integrations:** Converttofile,OpenAI,Telegram,Webhook,Lmchatopenai,Extractfromfile,Textclassifier,  

---

### 🤖🧑‍💻 AI Agent for Top n8n Creators Leaderboard Reporting
**Filename:** `2052_Telegram_Splitout_Automation_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Readwritefile, Markdown, and Converttofile for data processing. Uses 49 nodes and integrates with 14 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (49 nodes)  
**Integrations:** Readwritefile,Markdown,Converttofile,Lmchatgooglegemini,Google Drive,Lmchatopenai,Splitout,Agent,Toolworkflow,Gmail,Telegram,Httprequest,Chainllm,Executeworkflow,  

---

### 🤖🧠 AI Agent Chatbot + LONG TERM Memory + Note Storage + Telegram
**Filename:** `2053_Telegram_Googledocs_Automate_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatopenai, Google Docs, and Telegram for data processing. Uses 21 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (21 nodes)  
**Integrations:** Lmchatopenai,Google Docs,Telegram,Googledocstool,Agent,Chat,Memorybufferwindow,  

---


## Summary

**Total Communication & Messaging workflows:** 321  
**Documentation generated:** 2025-07-27 14:34:25  
**API Source:** https://scan-might-updates-postage.trycloudflare.com/api  

This documentation was automatically generated using the n8n workflow API endpoints.
