{"id": "161", "name": "Create a table and insert data into it", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [440, 460], "parameters": {}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [840, 460], "parameters": {"values": {"number": [{"name": "id"}], "string": [{"name": "name", "value": "<PERSON><PERSON>"}]}, "options": {}}, "typeVersion": 1}, {"name": "QuestDB", "type": "n8n-nodes-base.questDb", "position": [640, 460], "parameters": {"query": "CREATE TABLE test (id INT, name STRING);", "operation": "execute<PERSON>uery"}, "credentials": {"questDb": "QuestDB"}, "typeVersion": 1, "alwaysOutputData": true}, {"name": "QuestDB1", "type": "n8n-nodes-base.questDb", "position": [1040, 460], "parameters": {"table": "test", "columns": "id, name"}, "credentials": {"questDb": "QuestDB"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Set": {"main": [[{"node": "QuestDB1", "type": "main", "index": 0}]]}, "QuestDB": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "QuestDB", "type": "main", "index": 0}]]}}}