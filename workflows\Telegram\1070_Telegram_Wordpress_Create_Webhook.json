{"id": "5lMPjSDuoMvCJnko", "meta": {"instanceId": "31e69f7f4a77bf465b805824e303232f0227212ae922d12133a0f96ffeab4fef", "templateCredsSetupCompleted": true}, "name": "🔍🛠️Generate SEO-Optimized WordPress Content with Perplexity Research", "tags": [], "nodes": [{"id": "17ab0b24-b1eb-4e4e-a249-9889c9876fe4", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-1440, 460], "parameters": {"color": 3, "width": 420, "height": 440, "content": "## Write SEO Optimized Blog Post\n\n\n"}, "typeVersion": 1}, {"id": "0931aacf-5c47-4bb0-86b6-158c2c7470b1", "name": "Wordpress", "type": "n8n-nodes-base.wordpress", "position": [-1220, 1120], "parameters": {"title": "={{ $('Combine Blog Details').item.json.data[2].output.title }}", "additionalFields": {"slug": "={{ $('Combine Blog Details').item.json.data[2].output.slug }}", "status": "draft", "sticky": false, "content": "={{ $json.content }}", "authorId": 2, "postTemplate": {"values": {}}, "commentStatus": "closed"}}, "credentials": {"wordpressApi": {"id": "50Ph69y0TPKvO9tn", "name": "Wordpress"}}, "typeVersion": 1}, {"id": "81329ff1-b26a-499c-bd82-fd334503ab4f", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-980, 220], "parameters": {"color": 7, "width": 440, "height": 280, "content": "## Create HTML\n\n\n"}, "typeVersion": 1}, {"id": "03de9f23-e5ec-483b-a3dd-97617bd5165d", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [-1320, 1020], "parameters": {"color": 4, "width": 300, "height": 280, "content": "## Post on Wordpress\n\n\n"}, "typeVersion": 1}, {"id": "6bf602e0-ad29-47e6-93d7-79fd2a4228c2", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-900, 820], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "8a3739ac-9492-400c-b5b8-eeb305647752", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [-680, 820], "parameters": {"jsonSchemaExample": "{\n\"slug\": \"rpo-benefits-recruitment\",\n\"title\": \"7 Key Advantages of RPO for Modern Recruitment\",\n\"meta\": \"Explore how Recruitment Process Outsourcing (RPO) enhances hiring efficiency, reduces costs, and expands talent pools for businesses seeking top candidates.\"\n}"}, "typeVersion": 1.2}, {"id": "af02ee94-4c26-4be5-bd21-09e020bff876", "name": "Create Title, <PERSON>lug, <PERSON>a", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-880, 640], "parameters": {"text": "=**Create a slug, blog post title, and meta description for the following blog post:**\n\n{{ $json.output }}\n\n**Slug Guidelines:**\n- Keep it concise (4-5 words maximum).\n- Include the primary keyword related to recruitment or HR.\n- Use hyphens to separate words.\n- Avoid unnecessary words, articles, or prepositions.\n- Ensure it reflects the main topic of the blog post.\n- Make it readable and relevant for both users and search engines.\n\n**Title Guidelines:**\n- Avoid AI words like \"Transform\" or \"Revolutionize\" and similar overused terms.\n- Avoid using a colon (:) in the title.\n- Never structure it as a primary/secondary title separated by a colon.\n- Include the primary keyword related to recruitment or HR (e.g., 'AI in recruitment' or 'talent acquisition trends').\n- Clearly inform users what they can expect from reading the blog post.\n- Be concise and engaging, ideally 50-60 characters long.\n- Incorporate power words that appeal to HR professionals and recruiters.\n\n**Meta Description Guidelines:**\n- Avoid AI words like \"Transform\" or \"Revolutionize\" and similar overused terms.\n- Be concise: Limit to 150-160 characters to ensure full visibility in search results.\n- Include keywords: Naturally incorporate primary recruitment-related keywords to enhance relevance and visibility.\n- Provide value: Clearly convey the benefits or insights readers will gain from the article.\n- Be engaging: Use action-oriented language or a thought-provoking question to encourage clicks.\n- Align with content: Accurately reflect the blog post's content to meet user expectations and reduce bounce rates.\n- Highlight expertise: Subtly emphasize SocialFind's authority in the recruitment field.\n\nYour output must be a single valid JSON object with these 3 fields:\n-slug: The slug\n-title: The blog post title\n-meta: The meta description  \n\nEach should be presented without any additional text, explanation, quotation marks, or formatting.\n", "options": {}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.8}, {"id": "115c4043-6fda-42a4-ac3c-c7979b2f327e", "name": "Create HTML", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [-880, 300], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "messages": {"values": [{"content": "=Only output the HTML content without preamble or further explanation. Generate WordPress-compatible HTML for a blog post based on the provided content.\n\n### CONTENT PROCESSING:\n- Process all content from {{ $json.output }}\n- Preserve all original facts, information, and URLs\n- Format according to the specifications below\n\n### REQUIRED STRUCTURE (IN THIS ORDER):\n1. Title (H2)\n2. Estimated reading time\n3. Key takeaways (3-5 bullet points)\n4. Table of contents (linked to all headings)\n5. Main content (with proper heading hierarchy)\n6. FAQ section\n\n### STYLING REQUIREMENTS:\n- Style Override: Include a style section with !important declarations\n- Links: All hyperlinks, TOC items, and FAQ questions must be #00c2ff (blue)\n- Headings: All headings need a bottom border in #00c2ff with padding\n- Spacing: Add <br><br> between each major section\n\n### ENGAGEMENT FORMATTING:\n- Use bold, italics, bullet points, quotes, and highlighting for emphasis\n- Create proper paragraph structure with appropriate line breaks\n- NO emojis allowed\n- Use whitespace strategically for readability\n\n### HYPERLINK HANDLING (CRITICAL):\n- When URLs appear next to keyphrases (e.g., \"AI tools (https://example.com)\")\n- Convert to: <a href=\"https://example.com\" style=\"color: #00c2ff !important;\">AI tools</a>\n- The KEYPHRASE must be linked, never the URL itself\n\n### WORDPRESS COMPATIBILITY:\n- Use WordPress block classes (wp-block-heading, wp-block-paragraph, etc.)\n- Add heading IDs starting with \"h-\" for better TOC linking\n- Ensure all styles use !important to override theme styles\n\nDO NOT include any explanations, code tags, or comments. Output ONLY the raw HTML.\n"}]}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "4756c8f2-406e-4a56-adb0-0c4708dabe6a", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [-420, 560], "parameters": {"numberInputs": 3}, "typeVersion": 3}, {"id": "1205aecf-08a1-499d-ac9e-822dd66b295f", "name": "Upload Image to Wordpress", "type": "n8n-nodes-base.httpRequest", "position": [-520, 1120], "parameters": {"url": "https://commonclone.com/wp-json/wp/v2/media", "method": "POST", "options": {}, "sendBody": true, "contentType": "binaryData", "sendHeaders": true, "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{"name": "Content-Disposition", "value": "=attachment; filename=\"cover-image-{{ $('Wordpress').item.json.id }}.jpeg\""}]}, "inputDataFieldName": "data", "nodeCredentialType": "wordpressApi"}, "credentials": {"wordpressApi": {"id": "50Ph69y0TPKvO9tn", "name": "Wordpress CommonClone.com RazorCX"}}, "typeVersion": 4.2}, {"id": "b4437d9e-8b90-4d15-a96d-46645618a56d", "name": "Set Image on Wordpress Post", "type": "n8n-nodes-base.httpRequest", "position": [-320, 1120], "parameters": {"url": "=https://commonclone.com/wp-json/wp/v2/posts/{{ $('Wordpress').item.json.id }}", "method": "POST", "options": {}, "sendQuery": true, "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "featured_media", "value": "={{ $json.id }}"}]}, "nodeCredentialType": "wordpressApi"}, "credentials": {"wordpressApi": {"id": "50Ph69y0TPKvO9tn", "name": "Wordpress CommonClone.com RazorCX"}}, "typeVersion": 4.2}, {"id": "bf05eaf3-2522-488e-893d-1ed9b2ed88b2", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-1940, 460], "parameters": {"color": 4, "width": 460, "height": 300, "content": "## Perplexity Research\n\n\n"}, "typeVersion": 1}, {"id": "22e8c044-ed98-495a-957e-c5e3fecc2b7d", "name": "On form submission", "type": "n8n-nodes-base.formTrigger", "position": [-2120, 560], "webhookId": "a29cbcd3-9d11-4f7c-9aad-14681c356c53", "parameters": {"options": {}, "formTitle": "Blog Factory", "formFields": {"values": [{"fieldType": "textarea", "fieldLabel": "Research Query", "placeholder": "=What are the most common challenges facing Canadian employers regarding recruitment and why would they want to hire a recruiting firm to solve these problems.", "requiredField": true}]}, "formDescription": "Create SEO optimized blog posts"}, "typeVersion": 2.2}, {"id": "6e6d4952-793f-4dc5-8d29-219d420149a9", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1940, 800], "parameters": {"width": 460, "height": 500, "content": "## Sample Generic Search Terms\nAdd your own or try these for your specific geo location.\n\n1. **Severe skills shortages in healthcare, construction, and education sectors.**  \n2. **Aging workforce widens employment gaps in key industries.**  \n3. **Tight labor market with 110 vacancies per 100 unemployed people.**  \n4. **High demand for specialized skills due to economic changes.**  \n5. **Housing shortages deter international candidates from relocating to the Netherlands.**  \n6. **Strict employment regulations complicate hiring processes for non-EU workers.**  \n7. **Intense competition for talent due to low unemployment rates.**  \n8. **Mismatch between available talent and job-specific skill requirements.**  \n9. **Candidates expect high benefits packages, increasing recruitment costs significantly.**  \n10. **Difficulty navigating compliance and labor laws for international hiring processes.**"}, "typeVersion": 1}, {"id": "bb94017e-dc2a-43e3-ae5c-1f3227b1f0ef", "name": "Perplexity Research", "type": "n8n-nodes-base.httpRequest", "position": [-1860, 560], "parameters": {"url": "https://api.perplexity.ai/chat/completions", "method": "POST", "options": {}, "jsonBody": "={\n  \"model\": \"sonar-pro\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"Act as a professional news researcher who is capable of finding detailed summaries about a news topic from highly reputable sources.\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \" Research the following topic and return everything you can find about: '{{ $json['Research Query'] }}'.\"\n    }\n  ]\n}\n", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "05RfNG280MisTyPP", "name": "Perplexity"}}, "typeVersion": 4.2}, {"id": "66086876-4b49-45fe-aecc-f7f062a59dba", "name": "Cleanup Links", "type": "n8n-nodes-base.set", "position": [-1660, 560], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "23b8e8c4-9191-415a-9661-1b60d413528a", "name": "research", "type": "string", "value": "={{ $json.choices[0].message.content.replaceAll(\"[1]\", \" - source: \" +$json.citations[0]).replaceAll(\"[2]\",\" - source:\" +$json.citations[1]).replaceAll(\"[3]\",\" - source: \" +$json.citations[2]).replaceAll(\"[4]\",\" - source: \"+$json.citations[3]).replaceAll(\"[5]\",\" - source: \"+$json.citations[4]).replaceAll(\"[6]\",\" - source: \"+$json.citations[5]).replaceAll(\"[7]\",\" - source: \"+$json.citations[6]).replaceAll(\"[8]\",\" - source: \"+$json.citations[7]).replaceAll(\"[9]\",\" - source: \"+$json.citations[8]).replaceAll(\"[10]\",\" - source: \"+$json.citations[9]) }}"}]}}, "typeVersion": 3.4}, {"id": "f81c9505-111f-473a-94b6-c79364410810", "name": "Copywriter AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-1360, 560], "parameters": {"text": "=You are part of a marketing team that creates high-quality blog posts for the AI consulting and workflow automation industry based in Canada. Your goal is to produce engaging, SEO-optimized content that positions the company as an authority in the AI consulting industry and attracts inbound leads.\n\nEvery 2 days, your team posts a blog on the most trending topics in AI consulting and n8n workflows. As the copywriter, you are provided with the following information:\n\n- Query: The main topic for this week's blog post, representing the most trending news in the recruitment space.\n\n- Other keywords: A list of high-search-volume keywords related to AI consulting and n8n workflows. Incorporate these naturally into the blog post where relevant, without forcing them or changing the post's meaning.\n\n- Research findings: Detailed information from reputable sources related to the blog topic. Your post must be based on this research.\n\nGiven this information, write a comprehensive blog post that:\n\n- Includes the query in the blog title, H2 header, and early in the introduction.\n- Incorporates all details from the research findings, including source URLs for potential hyperlinks.\n- Is detailed and informative, showcasing the companies expertise in AI consulting and n8n workflows to automate business processes.\n- Uses a professional yet engaging tone, highlighting the exciting developments and challenges in the recruitment industry.\n- Flows naturally and logically, making it easy for readers to follow.\n- Is between 1500 to 2000 words long.\n- Is written at a level accessible to HR professionals and business leaders.\n\nAdditional requirements:\n- Include practical takeaways or actionable advice for recruiters and HR professionals.\n- Highlight how the topic relates to the companies services or expertise.\n- Include a call-to-action (CTA) that encourages readers to explore the comapnies services or contact for more information.\n\nCreate the entire blog post draft in your first output. Don't stop or cut it short.\n\nYour output must be the blog post and nothing else.\n\nHere are the details of this blog post project:\n\nQuery:\n{{ $('On form submission').item.json['Research Query'] }}\n\nDetailed Research:\n{{ $('Cleanup Links').item.json.research }}\n\n\n", "options": {}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "1ee6bb8f-6441-4ed9-83e0-d0839b2d0e01", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-980, 540], "parameters": {"color": 7, "width": 440, "height": 440, "content": "## Create Title, Slug & Meta\n\n\n"}, "typeVersion": 1}, {"id": "cc53a2af-ef22-446b-a9ee-b6f4ee649865", "name": "Cleanup HTML ", "type": "n8n-nodes-base.set", "position": [-220, 820], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "0afb2988-1481-4b04-b16d-fb33c50a16d0", "name": "content", "type": "string", "value": "={{ $json.data[0].message.content.replaceAll('```html', '').replaceAll('```','') }}"}]}}, "typeVersion": 3.4}, {"id": "b31c8afe-e402-49c4-ba49-ee418cecc44e", "name": "GET Image", "type": "n8n-nodes-base.httpRequest", "position": [-720, 1120], "parameters": {"url": "={{ $json['image-url'] }}", "options": {}}, "typeVersion": 4.2}, {"id": "1089466a-1307-4f22-a242-d324c9165379", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-980, 1020], "parameters": {"width": 820, "height": 280, "content": "## Set Image for Wordpress Post"}, "typeVersion": 1}, {"id": "288e212b-5aa5-452e-87d6-ae06c6ad062a", "name": "Set Image URL", "type": "n8n-nodes-base.set", "position": [-920, 1120], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "1f0541df-05ab-4e3d-a5d8-3904579fc8a9", "name": "image-url", "type": "string", "value": "=https://smartcdn.gprod.postmedia.digital/healthing/wp-content/uploads/2024/07/GettyImages-**********.jpg?quality=90&strip=all&w=704&h=395"}]}}, "typeVersion": 3.4}, {"id": "38bf38e5-888e-4d63-a48e-e6affab28158", "name": "Send Success Message to Telegram", "type": "n8n-nodes-base.telegram", "position": [-80, 1120], "webhookId": "91f7d710-450a-4b66-8e46-82f53492351e", "parameters": {"text": "=Success! Your blog post was created at {{ $now }}", "chatId": "={{ $env.TELEGRAM_CHAT_ID }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "30f0fa84-9918-4bf6-86e4-ef8f1dcf079c", "name": "gpt-4o-mini", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-1360, 760], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "d2d83cc5-1502-4b04-ac12-0bb351a90e58", "name": "Combine Blog Details", "type": "n8n-nodes-base.aggregate", "position": [-220, 560], "parameters": {"options": {}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "4209c818-8b02-453e-9254-c70bde66f743", "connections": {"Merge": {"main": [[{"node": "Combine Blog Details", "type": "main", "index": 0}]]}, "GET Image": {"main": [[{"node": "Upload Image to Wordpress", "type": "main", "index": 0}]]}, "Wordpress": {"main": [[{"node": "Set Image URL", "type": "main", "index": 0}]]}, "Create HTML": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "gpt-4o-mini": {"ai_languageModel": [[{"node": "Copywriter AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Cleanup HTML ": {"main": [[{"node": "Wordpress", "type": "main", "index": 0}]]}, "Cleanup Links": {"main": [[{"node": "Copywriter AI Agent", "type": "main", "index": 0}]]}, "Set Image URL": {"main": [[{"node": "GET Image", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Create Title, <PERSON>lug, <PERSON>a", "type": "ai_languageModel", "index": 0}]]}, "On form submission": {"main": [[{"node": "Perplexity Research", "type": "main", "index": 0}]]}, "Copywriter AI Agent": {"main": [[{"node": "Create HTML", "type": "main", "index": 0}, {"node": "Create Title, <PERSON>lug, <PERSON>a", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Perplexity Research": {"main": [[{"node": "Cleanup Links", "type": "main", "index": 0}]]}, "Combine Blog Details": {"main": [[{"node": "Cleanup HTML ", "type": "main", "index": 0}]]}, "Create Title, Slug, Meta": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 2}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Create Title, <PERSON>lug, <PERSON>a", "type": "ai_outputParser", "index": 0}]]}, "Upload Image to Wordpress": {"main": [[{"node": "Set Image on Wordpress Post", "type": "main", "index": 0}]]}, "Set Image on Wordpress Post": {"main": [[{"node": "Send Success Message to Telegram", "type": "main", "index": 0}]]}}}