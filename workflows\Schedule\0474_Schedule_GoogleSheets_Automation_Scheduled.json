{"meta": {"instanceId": "8418cffce8d48086ec0a73fd90aca708aa07591f2fefa6034d87fe12a09de26e"}, "nodes": [{"id": "3f4a15ab-64d8-49af-ba80-3aa1d424a62a", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [620, 160], "parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 6}]}}, "typeVersion": 1.1}, {"id": "8a8681b7-2d28-403f-92a7-58c9030cb8a6", "name": "Get Tweets", "type": "n8n-nodes-base.googleSheets", "position": [820, 160], "parameters": {"options": {"returnAllMatches": "returnFirstMatch"}, "sheetName": {"__rl": true, "mode": "list", "value": *********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1QyscSsUITnoJRnvyBbRpWeNF90TGD4dF5yj8DyZYQsA/edit#gid=*********", "cachedResultName": "Tweets"}, "documentId": {"__rl": true, "mode": "list", "value": "1QyscSsUITnoJRnvyBbRpWeNF90TGD4dF5yj8DyZYQsA", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1QyscSsUITnoJRnvyBbRpWeNF90TGD4dF5yj8DyZYQsA/edit?usp=drivesdk", "cachedResultName": "Tweets"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "RICzFHixgHXMuKmg", "name": "Google Sheets account"}}, "typeVersion": 4.3}, {"id": "bcce591e-b92e-43b4-b672-b02e32f95d15", "name": "Post on X", "type": "n8n-nodes-base.twitter", "position": [1000, 160], "parameters": {"text": "={{ $json.tweet }}", "additionalFields": {}}, "credentials": {"twitterOAuth2Api": {"id": "Yz7PjesMFvasMWkd", "name": "X account"}}, "typeVersion": 2}, {"id": "8acdd2a7-6104-490d-b8d0-26e5ff2fa37d", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [640, -280], "parameters": {"color": 6, "width": 275.**************, "height": 406.*************, "content": "# Setup\n### 1/ Add Your credentials\n[Google - Sheet](https://docs.n8n.io/integrations/builtin/credentials/google/)\n[X - Twitter](https://docs.n8n.io/integrations/builtin/credentials/twitter/)\n\n### 2/ Create a new Google Spread Sheet, with one sheet named Tweets and in the first cell, write tweet.\n\n### 3/ Define your desire frequency\n\n# 👇"}, "typeVersion": 1}, {"id": "255e1f0f-beea-43fd-bfe6-0cc551a9eb6f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [940, 40], "parameters": {"color": 7, "width": 202.**************, "height": 85.**************, "content": "### Crafted by the\n## [🥷 n8n.ninja](https://n8n.ninja)"}, "typeVersion": 1}, {"id": "f834409b-bba2-4e8a-9fb9-5971a49960dd", "name": "Remove from list", "type": "n8n-nodes-base.googleSheets", "position": [1180, 160], "parameters": {"operation": "delete", "sheetName": {"__rl": true, "mode": "list", "value": *********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1QyscSsUITnoJRnvyBbRpWeNF90TGD4dF5yj8DyZYQsA/edit#gid=*********", "cachedResultName": "Tweets"}, "documentId": {"__rl": true, "mode": "list", "value": "1QyscSsUITnoJRnvyBbRpWeNF90TGD4dF5yj8DyZYQsA", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1QyscSsUITnoJRnvyBbRpWeNF90TGD4dF5yj8DyZYQsA/edit?usp=drivesdk", "cachedResultName": "Tweets"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "RICzFHixgHXMuKmg", "name": "Google Sheets account"}}, "typeVersion": 4.3}], "pinData": {}, "connections": {"Post on X": {"main": [[{"node": "Remove from list", "type": "main", "index": 0}]]}, "Get Tweets": {"main": [[{"node": "Post on X", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Get Tweets", "type": "main", "index": 0}]]}}}