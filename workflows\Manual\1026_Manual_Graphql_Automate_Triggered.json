{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "GraphQL", "type": "n8n-nodes-base.graphql", "position": [450, 300], "parameters": {"query": "{\n  launchesPast(limit: 5) {\n    mission_name\n    launch_date_local\n    launch_site {\n      site_name_long\n    }\n    links {\n      article_link\n      video_link\n    }\n    rocket {\n      rocket_name\n      first_stage {\n        cores {\n          flight\n          core {\n            reuse_count\n            status\n          }\n        }\n      }\n      second_stage {\n        payloads {\n          payload_type\n          payload_mass_kg\n          payload_mass_lbs\n        }\n      }\n    }\n    ships {\n      name\n      home_port\n      image\n    }\n  }\n}", "endpoint": "https://api.spacex.land/graphql/", "requestFormat": "json", "responseFormat": "string", "headerParametersUi": {"parameter": []}}, "typeVersion": 1}], "connections": {"On clicking 'execute'": {"main": [[{"node": "GraphQL", "type": "main", "index": 0}]]}}}