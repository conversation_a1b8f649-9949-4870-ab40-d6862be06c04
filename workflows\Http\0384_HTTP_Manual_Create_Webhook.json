{"meta": {"instanceId": "dbd43d88d26a9e30d8aadc002c9e77f1400c683dd34efe3778d43d27250dde50"}, "nodes": [{"id": "14a494bf-acda-4758-ab79-bae07b7bbd10", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [2800, 1744], "parameters": {"color": 6, "width": 305, "height": 254.26094733974475, "content": "## List NMs\nto change parameters read [Docs](https://developers.cloudflare.com/api/operations/workers-kv-namespace-list-namespaces)"}, "typeVersion": 1}, {"id": "a97a332f-e8f2-4011-9158-66cff1f0773f", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [476, 1460], "parameters": {"color": 4, "width": 289.22425544359976, "height": 259, "content": "## Create NM\nto change parameters read [Docs](https://developers.cloudflare.com/api/operations/workers-kv-namespace-create-a-namespace)"}, "typeVersion": 1}, {"id": "b7575223-8467-4723-9b4d-38941382aea8", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1399, 1180], "parameters": {"color": 3, "width": 767.*************, "height": 260.*************, "content": "## Delete NM of KV (By Name Serach)\nto change anything read [Docs](https://developers.cloudflare.com/api/operations/workers-kv-namespace-remove-a-namespace)"}, "typeVersion": 1}, {"id": "259848b4-5472-436e-9ef7-dbba14f21348", "name": "Delete KV", "type": "n8n-nodes-base.httpRequest", "notes": "Delete Selected KV", "position": [2000, 1280], "parameters": {"url": "=https://api.cloudflare.com/client/v4/accounts/{{ $('Account Path').params[\"fields\"][\"values\"][0][\"stringValue\"] }}/storage/kv/namespaces/{{ $node[\"List KV-NMs (2)\"].json[\"result\"].find(kv => kv.title === $node[\"Set KV-NM Name (2)\"].json[\"NameSpace\"]).id }}", "method": "DELETE", "options": {}, "authentication": "predefinedCredentialType", "nodeCredentialType": "cloudflareApi"}, "notesInFlow": true, "typeVersion": 4.1}, {"id": "509c3878-e7b0-4a04-84a0-86b4c81db3b3", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1401, 1460], "parameters": {"color": 4, "width": 762.*************, "height": 259, "content": "## Rename NM of KV (By Serach)\nto change parameters read [Docs](https://developers.cloudflare.com/api/operations/workers-kv-namespace-rename-a-namespace)"}, "typeVersion": 1}, {"id": "2f6a6158-f755-4ad5-a874-c08f2182e2bd", "name": "Delete KV1", "type": "n8n-nodes-base.httpRequest", "notes": "/storage/kv/namespaces/", "position": [2000, 1540], "parameters": {"url": "=https://api.cloudflare.com/client/v4/accounts/{{ $('Account Path').params[\"fields\"][\"values\"][0][\"stringValue\"] }}/storage/kv/namespaces/{{ $node[\"List KV-NMs (5)\"].json[\"result\"].find(kv => kv.title === $node[\"KV to Rename\"].json[\"Previous KV Name\"]).id }}", "method": "PUT", "options": {}, "sendBody": true, "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "title", "value": "={{ $('KV to Rename').item.json[\"New KV Name\"] }}"}]}, "nodeCredentialType": "cloudflareApi"}, "notesInFlow": true, "typeVersion": 4.1}, {"id": "466b9ec4-1ee6-46c9-ac52-65a9c9c942ba", "name": "KV to Rename", "type": "n8n-nodes-base.set", "position": [1440, 1540], "parameters": {"fields": {"values": [{"name": "Previous KV Name", "stringValue": "<Enter your previous Namespace name>"}, {"name": "New KV Name", "stringValue": "<Enter your new Namespace name>"}]}, "options": {}}, "typeVersion": 3.2}, {"id": "f74468c5-235b-43f1-aa9a-544b5b8a6d3c", "name": "Account Path", "type": "n8n-nodes-base.set", "notes": "Required for all nodes", "position": [3000, 920], "parameters": {"fields": {"values": [{"name": "Account Path (account_identifier)", "stringValue": "65889d72a808df2e380018d87fffca5f"}]}, "options": {}}, "notesInFlow": true, "typeVersion": 3.2}, {"id": "32ffd694-52a3-4da5-b6f4-b4f7a17365cb", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [2260, 1184], "parameters": {"color": 3, "width": 838.*************, "height": 259, "content": "## Delete multiple KV pairs\nto change anything read [Docs](https://developers.cloudflare.com/api/operations/workers-kv-namespace-delete-multiple-key-value-pairs)"}, "typeVersion": 1}, {"id": "e90de48a-8d2b-4924-accc-6ef1a1448a95", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [460, 460], "parameters": {"color": 7, "width": 1264.************, "height": 607, "content": "#  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ **`Cloudflare Key-Value Full API integration Workflow`**\n[![Hetzner Cloud](https://developers.cloudflare.com/assets/kv-write-fdb5578b.svg)](https://www.hetzner.com/cloud)\n##  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ _Integrate your N8N with CF KV Free instead of selfhosting Redis or any RAM based Storages!!_"}, "typeVersion": 1}, {"id": "5ca2dfda-2662-44ad-a4f5-3a036533eaac", "name": "Delete KVs inside NM", "type": "n8n-nodes-base.httpRequest", "notes": "Delete bulk Keys-Values inside select Namespace", "position": [2920, 1284], "parameters": {"url": "=https://api.cloudflare.com/client/v4/accounts/{{ $('Account Path').params[\"fields\"][\"values\"][0][\"stringValue\"] }}/storage/kv/namespaces/{{ $node[\"List KV-NMs (3)\"].json[\"result\"].find(kv => kv.title === $node[\"Set KV-NM Name (3)\"].json[\"NameSpace\"]).id }}/bulk", "method": "DELETE", "options": {}, "jsonBody": "[   \"key1\",   \"key2\",   \"key3\" ]", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "cloudflareApi"}, "notesInFlow": true, "typeVersion": 4.1}, {"id": "63d0419c-a031-41df-ab0b-89de6fe3459e", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [2260, 1464], "parameters": {"color": 4, "width": 839.***********, "height": 257.*************, "content": "## Write multiple KV pairs\nto change anything read [Docs](https://developers.cloudflare.com/api/operations/workers-kv-namespace-write-multiple-key-value-pairs)"}, "typeVersion": 1}, {"id": "689575b2-8dcf-4a6b-bedb-2aa1e1de48f7", "name": "Create KV-NM", "type": "n8n-nodes-base.httpRequest", "notes": "Create New Key-Value Namespace", "position": [560, 1540], "parameters": {"url": "=https://api.cloudflare.com/client/v4/accounts/{{ $('Account Path').params[\"fields\"][\"values\"][0][\"stringValue\"] }}/storage/kv/namespaces", "method": "POST", "options": {}, "sendBody": true, "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "title", "value": "<Enter Your Key-Value Namespace Here>"}]}, "nodeCredentialType": "cloudflareApi"}, "notesInFlow": true, "typeVersion": 4.1}, {"id": "e78616dc-bf3d-4913-a463-427d4b62c07b", "name": "Write KVs inside NM", "type": "n8n-nodes-base.httpRequest", "notes": "/storage/kv/namespaces/", "position": [2920, 1560], "parameters": {"url": "=https://api.cloudflare.com/client/v4/accounts/{{ $('Account Path').params[\"fields\"][\"values\"][0][\"stringValue\"] }}/storage/kv/namespaces/{{ $node[\"List KV-NMs (6)\"].json[\"result\"].find(kv => kv.title === $node[\"Set KV-NM Name (5)\"].json[\"NameSpace\"]).id }}/bulk", "method": "PUT", "options": {}, "jsonBody": "=[{\n        \"key\": \"key1\",\n        \"value\": \"Value1\",\n        \"base64\": false,\n        \"expiration\": **********,\n        \"expiration_ttl\": 300\n      },\n      {\n        \"key\": \"key2\",\n        \"value\": \"Value2\",\n        \"base64\": false,\n        \"expiration\": **********,\n        \"expiration_ttl\": 300\n      },\n      {\n        \"key\": \"key3\",\n        \"value\": \"Value3\",\n        \"base64\": false,\n        \"expiration\": **********,\n        \"expiration_ttl\": 300\n      }]", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer undefined"}]}, "nodeCredentialType": "cloudflareApi"}, "notesInFlow": true, "typeVersion": 4.1}, {"id": "d6843312-6186-475c-9a30-e8d913cb9b17", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [2260, 1747], "parameters": {"color": 6, "width": 513, "height": 254.**************, "content": "## List NM-Keys\nto change anything read [Docs](https://developers.cloudflare.com/api/operations/workers-kv-namespace-list-a-namespace'-s-keys)"}, "typeVersion": 1}, {"id": "1e444f44-4561-493d-b295-812f71e14385", "name": "-Get <PERSON> inside NM", "type": "n8n-nodes-base.httpRequest", "notes": "Get Available Keys", "position": [2620, 1840], "parameters": {"url": "=https://api.cloudflare.com/client/v4/accounts/{{ $('Account Path').params[\"fields\"][\"values\"][0][\"stringValue\"] }}/storage/kv/namespaces/{{ $node[\"List KV-NMs (9)\"].json[\"result\"].find(kv => kv.title === $node[\"Set KV-NM Name (8)\"].json[\"NameSpace\"]).id }}/keys", "options": {}, "jsonBody": "=[{\n        \"key\": \"key1\",\n        \"value\": \"Value1\",\n        \"base64\": false,\n        \"expiration\": **********,\n        \"expiration_ttl\": 300\n      },\n      {\n        \"key\": \"key2\",\n        \"value\": \"Value2\",\n        \"base64\": false,\n        \"expiration\": **********,\n        \"expiration_ttl\": 300\n      },\n      {\n        \"key\": \"key3\",\n        \"value\": \"Value3\",\n        \"base64\": false,\n        \"expiration\": **********,\n        \"expiration_ttl\": 300\n      }]", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer undefined"}]}, "nodeCredentialType": "cloudflareApi"}, "notesInFlow": true, "typeVersion": 4.1}, {"id": "623e3bb7-8bb4-4da0-8434-c9472efee11a", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [2240, 1120], "parameters": {"color": 7, "width": 878.0751770171937, "height": 920.8960116234484, "content": "##     ‌ ‌‌‌‌ ‌ ‌ ‌ ‌‌‌‌ ‌   Bulk Actions"}, "typeVersion": 1}, {"id": "dba1cfb6-207b-4938-ae95-7f9aaef1e85c", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [1405, 1740], "parameters": {"color": 6, "width": 755.5520725546517, "height": 257.*************, "content": "## Read MD in spesific key\nto change anything read [Docs](https://developers.cloudflare.com/api/operations/workers-kv-namespace-read-the-metadata-for-a-key)"}, "typeVersion": 1}, {"id": "0d02ba91-eebd-4dfd-8711-8060bfbbb0f5", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [480, 1180], "parameters": {"color": 3, "width": 828.************, "height": 259, "content": "## Delete KV\nto change anything read [Docs](https://developers.cloudflare.com/api/operations/workers-kv-namespace-delete-key-value-pair)"}, "typeVersion": 1}, {"id": "89cde428-54c7-4f8f-b16c-5803ede6e015", "name": "Delete KV inside NM", "type": "n8n-nodes-base.httpRequest", "notes": "Delete selected <PERSON><PERSON> in NM", "position": [1120, 1280], "parameters": {"url": "=https://api.cloudflare.com/client/v4/accounts/{{ $('Account Path').params[\"fields\"][\"values\"][0][\"stringValue\"] }}/storage/kv/namespaces/{{ $node[\"List KV-NMs (1)\"].json[\"result\"].find(kv => kv.title === $node[\"Set KV-NM Name (1)\"].json[\"NameSpace\"]).id }}/values/{{ $('Set KV-NM Name (1)').item.json['Key Name'] }}", "method": "DELETE", "options": {}, "authentication": "predefinedCredentialType", "nodeCredentialType": "cloudflareApi"}, "notesInFlow": true, "typeVersion": 4.1}, {"id": "0a03189d-9a69-4c57-bac3-f5d92b6724e3", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [474, 1740], "parameters": {"color": 6, "width": 834.*************, "height": 259, "content": "## Read KV\nto change anything read [Docs](https://developers.cloudflare.com/api/operations/workers-kv-namespace-read-key-value-pair)"}, "typeVersion": 1}, {"id": "71e7e9e3-57e6-43da-a90a-64aa79197769", "name": "Read Value Of KV In NM", "type": "n8n-nodes-base.httpRequest", "notes": "/storage/kv/namespaces/", "position": [1140, 1840], "parameters": {"url": "=https://api.cloudflare.com/client/v4/accounts/{{ $('Account Path').params[\"fields\"][\"values\"][0][\"stringValue\"] }}/storage/kv/namespaces/{{ $node[\"List KV-NMs (7)\"].json[\"result\"].find(kv => kv.title === $node[\"Set KV-NM Name (6)\"].json[\"NameSpace\"]).id }}/values/{{ $('Set KV-NM Name (6)').item.json['Key Name'] }}", "options": {"response": {"response": {"responseFormat": "text", "outputPropertyName": "={{ $('Set KV-NM Name (6)').item.json['Key Name'] }}"}}}, "authentication": "predefinedCredentialType", "nodeCredentialType": "cloudflareApi"}, "notesInFlow": true, "typeVersion": 4.1}, {"id": "c0442be4-8a66-46d4-a158-f5d452270374", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [780, 1460], "parameters": {"color": 4, "width": 531.*************, "height": 263.************, "content": "## Write KV\nto change anything read [Docs](https://developers.cloudflare.com/api/operations/workers-kv-namespace-write-key-value-pair-with-metadata)"}, "typeVersion": 1}, {"id": "229dda34-b608-4dab-af3a-e81aaed1b8a5", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [1375, 1120], "parameters": {"color": 7, "width": 817.7528311355856, "height": 917.7366431832784, "content": "##     ‌ ‌‌‌‌ ‌ ‌ ‌ Specific Actions"}, "typeVersion": 1}, {"id": "0b677885-bc87-4573-8d0b-02bfaaaf164e", "name": "Sticky Note14", "type": "n8n-nodes-base.stickyNote", "position": [461, 1124], "parameters": {"color": 7, "width": 876.5452405896403, "height": 913.7927070441785, "content": "##     ‌ ‌‌‌‌ ‌ ‌ ‌  ‌‌‌‌ ‌ ‌‌‌‌ ‌ ‌ ‌  ‌ ‌ Single Actions"}, "typeVersion": 1}, {"id": "500121cd-3ec5-4e3d-b3ad-de38f3733fc6", "name": "Sticky Note15", "type": "n8n-nodes-base.stickyNote", "position": [1720, 460], "parameters": {"color": 7, "width": 1389.*************, "height": 607, "content": "## This n8n template provides a seamless and efficient way to manage Key-Value (KV) pairs in Cloudflare's KV storage. all you need just take the part of action you want then use it with your workflow, keep in mind that the **_`Account Path`_** node is required for all actions as it's used to set the path of account, other authentication values is automatically set by n8n pre configured cloudflare api.\n ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌   ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌   ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ![Cloudflare Logo](https://cf-assets.www.cloudflare.com/slt3lc6tev37/7bIgGp4hk4SFO0o3SBbOKJ/546b2df59d9ef1ac226d70c40b17d019/Cloudflare-logo-transparent-v-rgb_thumbnail.png)\n\n\n# shortcuts:\n- ## **`NM`** or **`NMs`** =  _**`NameSpace/s`**_\n- ## **`KV`** or **`KVs`** = _**`Key/s - Value/s`**_\n- ## **`MD`** = _**`MetaData`**_"}, "typeVersion": 1}, {"id": "b65a6aab-08de-4217-b580-fd3afae16b47", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "notes": "Replace Me", "position": [2460, 920], "parameters": {}, "notesInFlow": true, "typeVersion": 1}, {"id": "197ca562-fc20-4cd6-8990-cd3e9e3d3b0d", "name": "List KV-NMs (1)", "type": "n8n-nodes-base.httpRequest", "notes": "Get Available Namespaces", "position": [820, 1280], "parameters": {"url": "=https://api.cloudflare.com/client/v4/accounts/{{ $json[\"Account Path (account_identifier)\"] }}/storage/kv/namespaces", "options": {}, "sendQuery": true, "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "direction", "value": "asc"}, {"name": "order", "value": "id"}, {"name": "page", "value": "1"}, {"name": "per_page", "value": "20"}]}, "nodeCredentialType": "cloudflareApi"}, "notesInFlow": true, "typeVersion": 4.1}, {"id": "64e26fad-eeab-4d04-b8ff-c42bb581a409", "name": "Set KV-NM Name (2)", "type": "n8n-nodes-base.set", "notes": "Set Key-Value Namespace for deleting", "position": [1440, 1280], "parameters": {"fields": {"values": [{"name": "NameSpace", "stringValue": "<Enter Your Key-Value Namespace Here>"}]}, "options": {}}, "notesInFlow": true, "typeVersion": 3.2}, {"id": "4a12fa4c-59df-4a27-bf94-59c5ccd05d40", "name": "Set KV-NM Name (1)", "type": "n8n-nodes-base.set", "notes": "Set Key-Value Namespace for deleting", "position": [560, 1280], "parameters": {"fields": {"values": [{"name": "NameSpace", "stringValue": "<Enter Your Key-Value Namespace Here>"}, {"name": "Key Name", "stringValue": "<Enter Your Key Name Here>"}]}, "options": {}}, "notesInFlow": true, "typeVersion": 3.2}, {"id": "6f33a389-0b2e-4c9c-9cf5-38bfbf482f3b", "name": "Set KV-NM Name (3)", "type": "n8n-nodes-base.set", "notes": "Set Key-Value Namespace for Deleting", "position": [2320, 1284], "parameters": {"fields": {"values": [{"name": "NameSpace", "stringValue": "Set Key-Value Namespace for "}]}, "options": {}}, "notesInFlow": true, "typeVersion": 3.2}, {"id": "a9692d7c-f0bc-45c3-b99c-3c7a36d98f5c", "name": "List KV-NMs (2)", "type": "n8n-nodes-base.httpRequest", "notes": "Get Available Namespaces", "position": [1720, 1280], "parameters": {"url": "=https://api.cloudflare.com/client/v4/accounts/{{ $json[\"Account Path (account_identifier)\"] }}/storage/kv/namespaces", "options": {}, "sendQuery": true, "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "direction", "value": "asc"}, {"name": "order", "value": "id"}, {"name": "page", "value": "1"}, {"name": "per_page", "value": "20"}]}, "nodeCredentialType": "cloudflareApi"}, "notesInFlow": true, "typeVersion": 4.1}, {"id": "1c655d60-bcf8-42ee-a993-57b151789d44", "name": "List KV-NMs (3)", "type": "n8n-nodes-base.httpRequest", "notes": "Get Available Namespaces", "position": [2640, 1284], "parameters": {"url": "=https://api.cloudflare.com/client/v4/accounts/{{ $json[\"Account Path (account_identifier)\"] }}/storage/kv/namespaces", "options": {}, "sendQuery": true, "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "direction", "value": "asc"}, {"name": "order", "value": "id"}, {"name": "page", "value": "1"}, {"name": "per_page", "value": "20"}]}, "nodeCredentialType": "cloudflareApi"}, "notesInFlow": true, "typeVersion": 4.1}, {"id": "fdbbc7bc-9825-4882-a7ea-c30c2e1fb909", "name": "Set KV-NM Name (4)", "type": "n8n-nodes-base.set", "notes": "Set Key-Value Namespace for kv", "position": [840, 1540], "parameters": {"fields": {"values": [{"name": "NameSpace", "stringValue": "<Enter Your Key-Value Namespace Here>"}, {"name": "Key Name", "stringValue": "<Enter Your Key-Value Name Here>"}]}, "options": {}}, "notesInFlow": true, "typeVersion": 3.2}, {"id": "b72a4e3e-60d6-4c31-9299-3d09c934d1c7", "name": "Write V & MD of KV In NM", "type": "n8n-nodes-base.httpRequest", "notes": "Put value with Metadata to NM key", "position": [1160, 1540], "parameters": {"url": "=https://api.cloudflare.com/client/v4/accounts/{{ $('Account Path').params[\"fields\"][\"values\"][0][\"stringValue\"] }}/storage/kv/namespaces/{{ $node[\"List KV-NMs (4)\"].json[\"result\"].find(kv => kv.title === $node[\"Set KV-NM Name (4)\"].json[\"NameSpace\"]).id }}/values/{{ $('Set KV-NM Name (4)').item.json['Key Name'] }}", "method": "PUT", "options": {}, "sendBody": true, "contentType": "multipart-form-data", "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "value", "value": "Some Value"}, {"name": "metadata", "value": "{\"someMetadataKey\": \"someMetadataValue\"}"}]}, "nodeCredentialType": "cloudflareApi"}, "notesInFlow": true, "typeVersion": 4.1}, {"id": "143d7590-608c-4742-844d-0033b0066aab", "name": "Set KV-NM Name (5)", "type": "n8n-nodes-base.set", "position": [2320, 1560], "parameters": {"fields": {"values": [{"name": "NameSpace", "stringValue": "<Enter Your Key-Value Namespace Here>"}]}, "options": {}}, "notesInFlow": false, "typeVersion": 3.2}, {"id": "e6eb4b67-75e7-4264-a312-c66d21db947b", "name": "Set KV-NM Name (6)", "type": "n8n-nodes-base.set", "notes": "Set Key-Value Namespace", "position": [560, 1840], "parameters": {"fields": {"values": [{"name": "NameSpace", "stringValue": "<Enter Your Key-Value Namespace Here>"}, {"name": "Key Name", "stringValue": "<Enter Your Key-Value Name Here>"}]}, "options": {}}, "notesInFlow": true, "typeVersion": 3.2}, {"id": "c2635c89-51a0-4a84-aa63-775f318fdc7a", "name": "List KV-NMs (4)", "type": "n8n-nodes-base.httpRequest", "notes": "Get Available Namespaces", "position": [1000, 1540], "parameters": {"url": "=https://api.cloudflare.com/client/v4/accounts/{{ $json[\"Account Path (account_identifier)\"] }}/storage/kv/namespaces", "options": {}, "sendQuery": true, "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "direction", "value": "asc"}, {"name": "order", "value": "id"}, {"name": "page", "value": "1"}, {"name": "per_page", "value": "20"}]}, "nodeCredentialType": "cloudflareApi"}, "notesInFlow": true, "typeVersion": 4.1}, {"id": "b0bcd87c-f19f-4e8f-8899-37676e66aa95", "name": "List KV-NMs (5)", "type": "n8n-nodes-base.httpRequest", "notes": "Get Available Namespaces", "position": [1720, 1540], "parameters": {"url": "=https://api.cloudflare.com/client/v4/accounts/{{ $json[\"Account Path (account_identifier)\"] }}/storage/kv/namespaces", "options": {}, "sendQuery": true, "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "direction", "value": "asc"}, {"name": "order", "value": "id"}, {"name": "page", "value": "1"}, {"name": "per_page", "value": "20"}]}, "nodeCredentialType": "cloudflareApi"}, "notesInFlow": true, "typeVersion": 4.1}, {"id": "50f41db8-62ba-4bb2-abc0-cecbce4bcd12", "name": "List KV-NMs (6)", "type": "n8n-nodes-base.httpRequest", "notes": "Get Available Namespaces", "position": [2640, 1560], "parameters": {"url": "=https://api.cloudflare.com/client/v4/accounts/{{ $json[\"Account Path (account_identifier)\"] }}/storage/kv/namespaces", "options": {}, "sendQuery": true, "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "direction", "value": "asc"}, {"name": "order", "value": "id"}, {"name": "page", "value": "1"}, {"name": "per_page", "value": "20"}]}, "nodeCredentialType": "cloudflareApi"}, "notesInFlow": true, "typeVersion": 4.1}, {"id": "********-0ed0-47df-bbd1-98a8a089a018", "name": "List KV-NMs (7)", "type": "n8n-nodes-base.httpRequest", "notes": "Get Available Namespaces", "position": [820, 1840], "parameters": {"url": "=https://api.cloudflare.com/client/v4/accounts/{{ $json[\"Account Path (account_identifier)\"] }}/storage/kv/namespaces", "options": {}, "sendQuery": true, "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "direction", "value": "asc"}, {"name": "order", "value": "id"}, {"name": "page", "value": "1"}, {"name": "per_page", "value": "20"}]}, "nodeCredentialType": "cloudflareApi"}, "notesInFlow": true, "typeVersion": 4.1}, {"id": "ecce1146-2562-4beb-8c88-b30158251999", "name": "List KV-NMs (8)", "type": "n8n-nodes-base.httpRequest", "notes": "Get Available Namespaces", "position": [1720, 1840], "parameters": {"url": "=https://api.cloudflare.com/client/v4/accounts/{{ $json[\"Account Path (account_identifier)\"] }}/storage/kv/namespaces", "options": {}, "sendQuery": true, "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "direction", "value": "asc"}, {"name": "order", "value": "id"}, {"name": "page", "value": "1"}, {"name": "per_page", "value": "20"}]}, "nodeCredentialType": "cloudflareApi"}, "notesInFlow": true, "typeVersion": 4.1}, {"id": "bcad9996-5d32-4367-8b81-618a3af70879", "name": "List KV-NMs (9)", "type": "n8n-nodes-base.httpRequest", "notes": "Get Available Namespaces", "position": [2460, 1840], "parameters": {"url": "=https://api.cloudflare.com/client/v4/accounts/{{ $json[\"Account Path (account_identifier)\"] }}/storage/kv/namespaces", "options": {}, "sendQuery": true, "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "direction", "value": "asc"}, {"name": "order", "value": "id"}, {"name": "page", "value": "1"}, {"name": "per_page", "value": "20"}]}, "nodeCredentialType": "cloudflareApi"}, "notesInFlow": true, "typeVersion": 4.1}, {"id": "64253d04-7ce4-4045-a41f-45faae2b6fd7", "name": "List KV-NMs (10)", "type": "n8n-nodes-base.httpRequest", "notes": "Get Available Namespaces", "position": [2900, 1835], "parameters": {"url": "=https://api.cloudflare.com/client/v4/accounts/{{ $json[\"Account Path (account_identifier)\"] }}/storage/kv/namespaces", "options": {}, "sendQuery": true, "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "direction", "value": "asc"}, {"name": "order", "value": "id"}, {"name": "page", "value": "1"}, {"name": "per_page", "value": "20"}]}, "nodeCredentialType": "cloudflareApi"}, "notesInFlow": true, "typeVersion": 4.1}, {"id": "66372c83-f593-4262-a0af-7902afa2d819", "name": "Set KV-NM Name (7)", "type": "n8n-nodes-base.set", "position": [1440, 1840], "parameters": {"fields": {"values": [{"name": "NameSpace", "stringValue": "<Enter Your Key-Value Namespace Here>"}, {"name": "Key Name", "stringValue": "<Enter Your Key-Value Name Here>"}]}, "options": {}}, "notesInFlow": false, "typeVersion": 3.2}, {"id": "18f0e8c6-c1bb-4632-9333-7ca296333966", "name": "Set KV-NM Name (8)", "type": "n8n-nodes-base.set", "notes": "Set Key-Value Namespace", "position": [2300, 1840], "parameters": {"fields": {"values": [{"name": "NameSpace", "stringValue": "<Enter Your Key-Value Namespace Here>"}]}, "options": {}}, "notesInFlow": true, "typeVersion": 3.2}, {"id": "16f84be9-30b3-4664-9f9e-69b2ac961034", "name": "<PERSON> from Key", "type": "n8n-nodes-base.httpRequest", "notes": "/storage/kv/namespaces/", "position": [2000, 1840], "parameters": {"url": "=https://api.cloudflare.com/client/v4/accounts/{{ $('Account Path').params[\"fields\"][\"values\"][0][\"stringValue\"] }}/storage/kv/namespaces/{{ $node[\"List KV-NMs (8)\"].json[\"result\"].find(kv => kv.title === $node[\"Set KV-NM Name (7)\"].json[\"NameSpace\"]).id }}/metadata/{{ $('Set KV-NM Name (7)').item.json['Key Name'] }}", "options": {}, "sendHeaders": true, "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer undefined"}]}, "nodeCredentialType": "cloudflareApi"}, "notesInFlow": true, "typeVersion": 4.1}], "pinData": {}, "connections": {"Account Path": {"main": [[{"node": "Set KV-NM Name (1)", "type": "main", "index": 0}, {"node": "Create KV-NM", "type": "main", "index": 0}, {"node": "Set KV-NM Name (6)", "type": "main", "index": 0}, {"node": "Set KV-NM Name (2)", "type": "main", "index": 0}, {"node": "KV to Rename", "type": "main", "index": 0}, {"node": "Set KV-NM Name (7)", "type": "main", "index": 0}, {"node": "Set KV-NM Name (3)", "type": "main", "index": 0}, {"node": "Set KV-NM Name (5)", "type": "main", "index": 0}, {"node": "Set KV-NM Name (8)", "type": "main", "index": 0}]]}, "KV to Rename": {"main": [[{"node": "List KV-NMs (5)", "type": "main", "index": 0}]]}, "Manual Trigger": {"main": [[{"node": "Account Path", "type": "main", "index": 0}]]}, "List KV-NMs (1)": {"main": [[{"node": "Delete KV inside NM", "type": "main", "index": 0}]]}, "List KV-NMs (2)": {"main": [[{"node": "Delete KV", "type": "main", "index": 0}]]}, "List KV-NMs (3)": {"main": [[{"node": "Delete KVs inside NM", "type": "main", "index": 0}]]}, "List KV-NMs (4)": {"main": [[{"node": "Write V & MD of KV In NM", "type": "main", "index": 0}]]}, "List KV-NMs (5)": {"main": [[{"node": "Delete KV1", "type": "main", "index": 0}]]}, "List KV-NMs (6)": {"main": [[{"node": "Write KVs inside NM", "type": "main", "index": 0}]]}, "List KV-NMs (7)": {"main": [[{"node": "Read Value Of KV In NM", "type": "main", "index": 0}]]}, "List KV-NMs (8)": {"main": [[{"node": "<PERSON> from Key", "type": "main", "index": 0}]]}, "List KV-NMs (9)": {"main": [[{"node": "-Get <PERSON> inside NM", "type": "main", "index": 0}]]}, "Set KV-NM Name (1)": {"main": [[{"node": "List KV-NMs (1)", "type": "main", "index": 0}]]}, "Set KV-NM Name (2)": {"main": [[{"node": "List KV-NMs (2)", "type": "main", "index": 0}]]}, "Set KV-NM Name (3)": {"main": [[{"node": "List KV-NMs (3)", "type": "main", "index": 0}]]}, "Set KV-NM Name (4)": {"main": [[{"node": "List KV-NMs (4)", "type": "main", "index": 0}]]}, "Set KV-NM Name (5)": {"main": [[{"node": "List KV-NMs (6)", "type": "main", "index": 0}]]}, "Set KV-NM Name (6)": {"main": [[{"node": "List KV-NMs (7)", "type": "main", "index": 0}]]}, "Set KV-NM Name (7)": {"main": [[{"node": "List KV-NMs (8)", "type": "main", "index": 0}]]}, "Set KV-NM Name (8)": {"main": [[{"node": "List KV-NMs (9)", "type": "main", "index": 0}]]}}}