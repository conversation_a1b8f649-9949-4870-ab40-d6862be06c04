{"id": "19", "meta": {"instanceId": "590b8a6424ded2dccf0f04ef13db2f02f968ec0b6d208436c385cdb410341348"}, "name": "Snowflake CSV", "tags": [], "nodes": [{"id": "da710a80-484b-4fe3-80fa-e699bb6499ad", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [440, 380], "parameters": {}, "typeVersion": 1}, {"id": "f419ebfb-9eae-4fea-b05b-aabc97b5f47f", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [640, 380], "parameters": {"url": "https://n8niostorageaccount.blob.core.windows.net/n8nio-strapi-blobs-prod/assets/example_c0b48ce677.csv?updated_at=2023-05-30T10:36:21.820Z", "options": {"response": {"response": {"responseFormat": "file"}}}}, "typeVersion": 4.1}, {"id": "fe45e2a2-b50f-4459-a8ee-78615239dee0", "name": "Spreadsheet File", "type": "n8n-nodes-base.spreadsheetFile", "position": [820, 380], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "54e31892-c8e1-423c-a24a-8e5eb1312b0a", "name": "Set", "type": "n8n-nodes-base.set", "position": [1000, 380], "parameters": {"values": {"number": [{"name": "first_name", "value": "={{ $json.first_name }}"}, {"name": "id", "value": "={{ $json.id }}"}], "string": [{"name": "last_name", "value": "={{ $json.last_name }}"}]}, "options": {"dotNotation": false}, "keepOnlySet": true}, "typeVersion": 2}, {"id": "c482d8e8-0792-4b61-a2e0-d437c9fe9062", "name": "Snowflake", "type": "n8n-nodes-base.snowflake", "position": [1200, 380], "parameters": {"table": "users", "columns": "id,first_name,last_name"}, "credentials": {"snowflake": {"id": "23", "name": "Snowflake account"}}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {}, "versionId": "a6348461-b174-4608-961f-d9d86730b573", "connections": {"Set": {"main": [[{"node": "Snowflake", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Spreadsheet File", "type": "main", "index": 0}]]}, "Spreadsheet File": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}}}