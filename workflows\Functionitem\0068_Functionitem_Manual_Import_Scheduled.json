{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [320, 170], "parameters": {}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [960, 320], "parameters": {"mode": "mergeByIndex"}, "typeVersion": 1}, {"name": "Move Binary Data", "type": "n8n-nodes-base.moveBinaryData", "position": [1260, 320], "parameters": {"mode": "jsonToBinary", "options": {"useRawData": false}}, "typeVersion": 1}, {"name": "Map", "type": "n8n-nodes-base.function", "position": [710, 320], "parameters": {"functionCode": "return items[0].json.data.map(item => {\n  return {json: item}\n});"}, "typeVersion": 1}, {"name": "Get Workflow", "type": "n8n-nodes-base.httpRequest", "notes": "Don't forget to add your credentials for your n8n instance in this Node. Use Basic Auth for this. ", "position": [830, 460], "parameters": {"url": "=http://localhost:5678/rest/workflows/{{$node[\"Map\"].data[\"id\"]}}", "options": {}, "authentication": "basicAuth"}, "credentials": {"httpBasicAuth": "n8n Creds"}, "notesInFlow": false, "typeVersion": 1}, {"name": "Get Workflow List", "type": "n8n-nodes-base.httpRequest", "notes": "Don't forget to add your credentials for your n8n instance in this Node. Use Basic Auth for this. ", "position": [520, 320], "parameters": {"url": "http://localhost:5678/rest/workflows", "options": {}, "authentication": "basicAuth"}, "credentials": {"httpBasicAuth": "n8n Creds"}, "typeVersion": 1}, {"name": "FunctionItem", "type": "n8n-nodes-base.functionItem", "position": [1110, 320], "parameters": {"functionCode": "item = item.data;\nreturn item;"}, "typeVersion": 1}, {"name": "Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [1450, 320], "parameters": {"name": "={{$node[\"Merge\"].data[\"name\"]}}.json", "parents": ["Delete this text and put id for folder you want to upload into in this field. The folder ID can be found by opening the folder in your browser and copying the portion after https://drive.google.com/drive/u/0/folders/"], "binaryData": true, "resolveData": true}, "credentials": {"googleApi": "test"}, "typeVersion": 1}, {"name": "Run Daily at 2:30am", "type": "n8n-nodes-base.cron", "position": [330, 320], "parameters": {"triggerTimes": {"item": [{"hour": 2, "minute": 30}]}}, "typeVersion": 1}], "connections": {"Map": {"main": [[{"node": "Get Workflow", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "FunctionItem", "type": "main", "index": 0}]]}, "FunctionItem": {"main": [[{"node": "Move Binary Data", "type": "main", "index": 0}]]}, "Get Workflow": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Move Binary Data": {"main": [[{"node": "Google Drive", "type": "main", "index": 0}]]}, "Get Workflow List": {"main": [[{"node": "Map", "type": "main", "index": 0}]]}, "Run Daily at 2:30am": {"main": [[{"node": "Get Workflow List", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Get Workflow List", "type": "main", "index": 0}]]}}}