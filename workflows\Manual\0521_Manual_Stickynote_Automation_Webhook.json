{"meta": {"instanceId": "1dd912a1610cd0376bae7bb8f1b5838d2b601f42ac66a48e012166bb954fed5a", "templateId": "2316"}, "nodes": [{"id": "7f4ecd85-1f6e-418e-a224-1a690741192b", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [380, 240], "parameters": {}, "typeVersion": 1}, {"id": "43a0e1f6-f9d1-4be2-8e84-8cf8be4add8e", "name": "Write Result File to Disk", "type": "n8n-nodes-base.readWriteFile", "position": [1200, 240], "parameters": {"options": {}, "fileName": "document.pdf", "operation": "write", "dataPropertyName": "=data"}, "typeVersion": 1}, {"id": "1094bca9-c48c-45bf-8cd4-17f074cd269a", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [720, 100], "parameters": {"width": 218, "height": 132, "content": "## Authentication\nConversion requests must be authenticated. Please create \n[ConvertAPI account to get authentication secret](https://www.convertapi.com/a/signin)"}, "typeVersion": 1}, {"id": "3e168f2e-f811-489a-b1ad-4973a86a2a6a", "name": "Download Image", "type": "n8n-nodes-base.httpRequest", "position": [580, 240], "parameters": {"url": "https://cdn.convertapi.com/public/files/demo.jpg", "options": {"response": {"response": {"responseFormat": "file"}}}}, "typeVersion": 4.2}, {"id": "c43b179c-5538-424c-90df-51699a5e6b87", "name": "File conversion to PDF", "type": "n8n-nodes-base.httpRequest", "position": [780, 240], "parameters": {"url": "https://v2.convertapi.com/convert/jpg/to/pdf", "method": "POST", "options": {"response": {"response": {"responseFormat": "file"}}}, "sendBody": true, "contentType": "multipart-form-data", "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "file", "parameterType": "formBinaryData", "inputDataFieldName": "=data"}]}, "genericAuthType": "httpQueryAuth", "headerParameters": {"parameters": [{"name": "Accept", "value": "application/octet-stream"}]}}, "credentials": {"httpQueryAuth": {"id": "WdAklDMod8fBEMRk", "name": "Query Auth account"}}, "notesInFlow": true, "typeVersion": 4.2}], "pinData": {}, "connections": {"Download Image": {"main": [[{"node": "File conversion to PDF", "type": "main", "index": 0}]]}, "File conversion to PDF": {"main": [[{"node": "Write Result File to Disk", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Download Image", "type": "main", "index": 0}]]}}}