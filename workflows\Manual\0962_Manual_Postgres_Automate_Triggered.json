{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "Postgres", "type": "n8n-nodes-base.postgres", "position": [450, 300], "parameters": {"query": "SELECT * from sometable;", "operation": "execute<PERSON>uery"}, "credentials": {"postgres": "postgres-creds"}, "typeVersion": 1}], "connections": {"On clicking 'execute'": {"main": [[{"node": "Postgres", "type": "main", "index": 0}]]}}}