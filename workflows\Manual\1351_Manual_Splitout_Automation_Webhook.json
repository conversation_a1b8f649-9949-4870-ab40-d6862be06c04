{"id": "BXfxO6faULfsy2JN", "meta": {"instanceId": "0b0f5302e78710cf1b1457ee15a129d8e5d83d4e366bd96d14cc37da6693e692"}, "name": "Scrape Today's Github Trend 13 Top Repositories", "tags": [], "nodes": [{"id": "e2981cad-c09b-46ee-b2db-cb007a95c4a1", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [0, 0], "parameters": {}, "typeVersion": 1}, {"id": "990de0c9-f540-4a10-8a1a-63a0526444ff", "name": "Extract Box", "type": "n8n-nodes-base.html", "position": [440, 0], "parameters": {"options": {}, "operation": "extractHtmlContent", "extractionValues": {"values": [{"key": "box", "cssSelector": "div.Box", "returnValue": "html"}]}}, "typeVersion": 1.2}, {"id": "7f7968ce-3935-488e-98f9-7ddd270d14b0", "name": "Request to Github Trend", "type": "n8n-nodes-base.httpRequest", "position": [220, 0], "parameters": {"url": "https://github.com/trending", "options": {}}, "typeVersion": 4.2}, {"id": "87cd7fa1-d896-49a3-9336-17663ca522aa", "name": "Turn to a list", "type": "n8n-nodes-base.splitOut", "position": [880, 0], "parameters": {"options": {}, "fieldToSplitOut": "repositories"}, "typeVersion": 1}, {"id": "bed61dad-0066-45de-bcf2-79fd143e360c", "name": "Set Result Variables", "type": "n8n-nodes-base.set", "position": [1320, 0], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "a0e76646-60d7-44a6-af77-33f27fb465cb", "name": "author", "type": "string", "value": "={{ $json.repository.split('/')[0].trim() }}"}, {"id": "a2bd790a-784e-4d72-9a4e-92be22edea8f", "name": "title", "type": "string", "value": "={{ $json.repository.split('/')[1].trim() }}"}, {"id": "22f1518a-7081-4417-ab9d-88f26a7b5cfe", "name": "repository", "type": "string", "value": "={{ $json.repository }}"}, {"id": "baff9a9f-020a-4968-bb80-a4a91a94144a", "name": "url", "type": "string", "value": "=https://github.com/{{ $json.repository.replaceAll(' ','') }}"}, {"id": "f5c48a02-b55d-4167-a823-53ac1d851ee5", "name": "created_at", "type": "string", "value": "={{$now}}"}, {"id": "27a44ce9-4b5b-44b2-94d9-eb5b2ae81dcd", "name": "description", "type": "string", "value": "={{ $json.description }}"}]}, "includeOtherFields": "="}, "typeVersion": 3.4}, {"id": "d7b39e99-38df-4025-9afd-a602c4bd01cf", "name": "Extract repository data", "type": "n8n-nodes-base.html", "position": [1100, 0], "parameters": {"options": {}, "operation": "extractHtmlContent", "dataPropertyName": "repositories", "extractionValues": {"values": [{"key": "repository", "cssSelector": "<PERSON><PERSON>"}, {"key": "language", "cssSelector": "span.d-inline-block"}, {"key": "description", "cssSelector": "p"}]}}, "typeVersion": 1.2}, {"id": "382e7a3b-f65f-4a79-a69f-2818f09f5daa", "name": "Extract all repositories", "type": "n8n-nodes-base.html", "position": [660, 0], "parameters": {"options": {"trimValues": true, "cleanUpText": true}, "operation": "extractHtmlContent", "dataPropertyName": "box", "extractionValues": {"values": [{"key": "repositories", "cssSelector": "article.Box-row", "returnArray": true, "returnValue": "html"}]}}, "typeVersion": 1.2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "33ada4c0-b6ad-4ad6-bee8-51b630908c04", "connections": {"Extract Box": {"main": [[{"node": "Extract all repositories", "type": "main", "index": 0}]]}, "Turn to a list": {"main": [[{"node": "Extract repository data", "type": "main", "index": 0}]]}, "Extract repository data": {"main": [[{"node": "Set Result Variables", "type": "main", "index": 0}]]}, "Request to Github Trend": {"main": [[{"node": "Extract Box", "type": "main", "index": 0}]]}, "Extract all repositories": {"main": [[{"node": "Turn to a list", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Request to Github Trend", "type": "main", "index": 0}]]}}}