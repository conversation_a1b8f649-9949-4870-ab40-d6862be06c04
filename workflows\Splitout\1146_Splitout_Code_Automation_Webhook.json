{"id": "7wwY8wfZdNpL83QQ", "meta": {"instanceId": "b3c467df4053d13fe31cc98f3c66fa1d16300ba750506bfd019a0913cec71ea3", "templateCredsSetupCompleted": true}, "name": "LinkedIn Leads Scraping & Enrichment (Main)", "tags": [], "nodes": [{"id": "5d07dfa4-6e6a-41a6-a2ea-8a8787331735", "name": "On form submission", "type": "n8n-nodes-base.formTrigger", "position": [-80, -80], "webhookId": "28ff6927-5d05-4182-910b-be2381e3b2c4", "parameters": {"options": {}, "formTitle": "Leads Search", "formFields": {"values": [{"fieldLabel": "Job Title", "requiredField": true}, {"fieldLabel": "Location", "requiredField": true}, {"fieldType": "number", "fieldLabel": "Number of Leads", "requiredField": true}]}}, "typeVersion": 2.2}, {"id": "76ade63a-7b44-4fd1-85d8-6dbacd83aca7", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [360, -80], "parameters": {"options": {}, "fieldToSplitOut": "people"}, "typeVersion": 1, "alwaysOutputData": false}, {"id": "3c7b7179-a8b9-41d6-9617-8eb7af38f571", "name": "Google Sheets Trigger", "type": "n8n-nodes-base.googleSheetsTrigger", "position": [-100, 280], "parameters": {"event": "rowAdded", "options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit?usp=drivesdk", "cachedResultName": "apollo ai leads & enrichment"}}, "credentials": {"googleSheetsTriggerOAuth2Api": {"id": "DKYpQQpUt3ceJiG4", "name": "Google Sheets Trigger account"}}, "typeVersion": 1}, {"id": "9a55977c-638f-49ae-a51f-420f71c15454", "name": "OpenAI1", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [360, 280], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-3.5-turbo", "cachedResultName": "GPT-3.5-TURBO"}, "options": {}, "messages": {"values": [{"content": "=remove the http or https://www.linkedin.com/in/ from this  {{ $json.linkedin_url }}"}]}}, "credentials": {"openAiApi": {"id": "DO9F6MAeTGLeqgoF", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "ca4d6b69-d4cb-4aa1-89bc-f0b5555f31cd", "name": "Google Sheets Trigger2", "type": "n8n-nodes-base.googleSheetsTrigger", "position": [-100, 680], "parameters": {"event": "rowAdded", "options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit?usp=drivesdk", "cachedResultName": "a<PERSON><PERSON> ai leads"}}, "credentials": {"googleSheetsTriggerOAuth2Api": {"id": "DKYpQQpUt3ceJiG4", "name": "Google Sheets Trigger account"}}, "typeVersion": 1}, {"id": "********-fa1f-42e4-8387-7d2d4f154938", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-200, 200], "parameters": {"color": 3, "width": 1260, "height": 300, "content": "## Extract Linkedin Username \n"}, "typeVersion": 1}, {"id": "f8ffe414-9ca9-463a-9b98-22a65a86d4c4", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-200, -160], "parameters": {"width": 1260, "height": 300, "content": "## <PERSON><PERSON><PERSON> Leads from Apollo\n"}, "typeVersion": 1}, {"id": "a09d3993-99d6-488e-8bc0-76542de7b53c", "name": "Clean Data", "type": "n8n-nodes-base.set", "position": [600, -80], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "e3bfe30e-9136-4ac9-b3da-c26eb678153b", "name": "id", "type": "string", "value": "={{ $json.id }}"}, {"id": "d45c81fb-1461-45fd-be95-d5d9901d72d7", "name": "name", "type": "string", "value": "={{ $json.name }}"}, {"id": "b4b8f660-7758-4a5f-a8f6-dc8ab6355132", "name": "linkedin_url", "type": "string", "value": "={{ $json.linkedin_url }}"}, {"id": "399f533a-6e6b-4f40-8ed8-aa5dd39017cd", "name": "title", "type": "string", "value": "={{ $json.title }}"}, {"id": "227d34c5-17db-4436-b0c2-f74e5ae453f2", "name": "organization", "type": "string", "value": "={{ $json.employment_history[0].organization_name }}"}]}}, "typeVersion": 3.4}, {"id": "92d03853-644e-4c73-8ecf-1e216238b37f", "name": "Add Leads to Google Sheet", "type": "n8n-nodes-base.googleSheets", "position": [840, -80], "parameters": {"columns": {"value": {"name": "={{ $json.name }}", "title": "={{ $json.title }}", "apollo_id": "={{ $json.id }}", "linkedin_url": "={{ $json.linkedin_url }}", "organization": "={{ $json.organization }}", "posts_scrape_status": "unscraped", "contacts_scrape_status": "pending", "profile_summary_scrape": "pending", "extract_username_status": "pending"}, "schema": [{"id": "apollo_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "apollo_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "name", "type": "string", "display": true, "required": false, "displayName": "name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "organization", "type": "string", "display": true, "removed": false, "required": false, "displayName": "organization", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "title", "type": "string", "display": true, "required": false, "displayName": "title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedin_url", "type": "string", "display": true, "required": false, "displayName": "linkedin_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedin_username", "type": "string", "display": true, "removed": true, "required": false, "displayName": "linkedin_username", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "extract_username_status", "type": "string", "display": true, "removed": false, "required": false, "displayName": "extract_username_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "email_address", "type": "string", "display": true, "removed": true, "required": false, "displayName": "email_address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "contacts_scrape_status", "type": "string", "display": true, "removed": false, "required": false, "displayName": "contacts_scrape_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "about_linkedin_profile", "type": "string", "display": true, "removed": true, "required": false, "displayName": "about_linkedin_profile", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "profile_summary_scrape", "type": "string", "display": true, "removed": false, "required": false, "displayName": "profile_summary_scrape", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "recent_posts_summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "recent_posts_summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "posts_scrape_status", "type": "string", "display": true, "removed": false, "required": false, "displayName": "posts_scrape_status", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["id"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit?usp=drivesdk", "cachedResultName": "apollo ai leads & enrichment"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "d0qeLhShx9sGXalR", "name": "Google Sheets"}}, "typeVersion": 4.5}, {"id": "0275f8ba-78d7-4464-bc80-ee939e9575e7", "name": "Add Linkedin Username", "type": "n8n-nodes-base.googleSheets", "position": [720, 280], "parameters": {"columns": {"value": {"apollo_id": "={{ $('Get Pending Username Row').item.json.apollo_id }}", "linkedin_username": "={{ $json.message.content }}", "extract_username_status": "finished"}, "schema": [{"id": "apollo_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "apollo_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "name", "type": "string", "display": true, "removed": true, "required": false, "displayName": "name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "title", "type": "string", "display": true, "removed": true, "required": false, "displayName": "title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedin_url", "type": "string", "display": true, "removed": true, "required": false, "displayName": "linkedin_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedin_username", "type": "string", "display": true, "removed": false, "required": false, "displayName": "linkedin_username", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "extract_username_status", "type": "string", "display": true, "removed": false, "required": false, "displayName": "extract_username_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "email_address", "type": "string", "display": true, "removed": true, "required": false, "displayName": "email_address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "phone_number", "type": "string", "display": true, "removed": true, "required": false, "displayName": "phone_number", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "contacts_scrape_status", "type": "string", "display": true, "removed": true, "required": false, "displayName": "contacts_scrape_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "about_linkedin_profile", "type": "string", "display": true, "removed": true, "required": false, "displayName": "about_linkedin_profile", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "profile_summary_scrape", "type": "string", "display": true, "removed": true, "required": false, "displayName": "profile_summary_scrape", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "recent_posts_summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "recent_posts_summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "posts_scrape_status", "type": "string", "display": true, "removed": true, "required": false, "displayName": "posts_scrape_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["apollo_id"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit?usp=drivesdk", "cachedResultName": "a<PERSON><PERSON> ai leads"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "d0qeLhShx9sGXalR", "name": "Google Sheets"}}, "typeVersion": 4.5}, {"id": "42827be7-8727-46e6-8f12-5d517c3ce6f3", "name": "Get Pending Username Row", "type": "n8n-nodes-base.googleSheets", "position": [140, 280], "parameters": {"options": {"returnFirstMatch": true}, "filtersUI": {"values": [{"lookupValue": "pending", "lookupColumn": "extract_username_status"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit?usp=drivesdk", "cachedResultName": "apollo ai leads & enrichment"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "d0qeLhShx9sGXalR", "name": "Google Sheets"}}, "executeOnce": true, "typeVersion": 4.5}, {"id": "ffbb16a6-020e-4825-9690-3e89a8bbbbf7", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-200, 560], "parameters": {"color": 5, "width": 1700, "height": 360, "content": "## Get Email Address, Validate Deliverability & Update Column Status"}, "typeVersion": 1}, {"id": "d2a1b54a-bc1b-4f63-a1a4-b9bd2017f82d", "name": "Add Email Address", "type": "n8n-nodes-base.googleSheets", "position": [1240, 600], "parameters": {"columns": {"value": {"apollo_id": "={{ $('Get Pending Email Statuses').item.json.apollo_id }}", "email_address": "={{ $json.data.email }}", "contacts_scrape_status": "finished"}, "schema": [{"id": "apollo_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "apollo_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "name", "type": "string", "display": true, "removed": true, "required": false, "displayName": "name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "title", "type": "string", "display": true, "removed": true, "required": false, "displayName": "title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedin_url", "type": "string", "display": true, "removed": true, "required": false, "displayName": "linkedin_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedin_username", "type": "string", "display": true, "removed": true, "required": false, "displayName": "linkedin_username", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "extract_username_status", "type": "string", "display": true, "removed": true, "required": false, "displayName": "extract_username_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "email_address", "type": "string", "display": true, "removed": false, "required": false, "displayName": "email_address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "contacts_scrape_status", "type": "string", "display": true, "removed": false, "required": false, "displayName": "contacts_scrape_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "about_linkedin_profile", "type": "string", "display": true, "removed": true, "required": false, "displayName": "about_linkedin_profile", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "profile_summary_scrape", "type": "string", "display": true, "removed": true, "required": false, "displayName": "profile_summary_scrape", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "recent_posts_summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "recent_posts_summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "posts_scrape_status", "type": "string", "display": true, "removed": true, "required": false, "displayName": "posts_scrape_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["apollo_id"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit?usp=drivesdk", "cachedResultName": "a<PERSON><PERSON> ai leads"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "d0qeLhShx9sGXalR", "name": "Google Sheets"}}, "executeOnce": true, "typeVersion": 4.5}, {"id": "7abbce6e-90fa-4ac5-9a0b-0ba2669aa77a", "name": "<PERSON>", "type": "n8n-nodes-base.googleSheets", "position": [1240, 760], "parameters": {"columns": {"value": {"apollo_id": "={{ $('Get Pending Email Statuses').item.json.apollo_id }}", "contacts_scrape_status": "invalid_email"}, "schema": [{"id": "apollo_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "apollo_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "name", "type": "string", "display": true, "removed": true, "required": false, "displayName": "name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "title", "type": "string", "display": true, "removed": true, "required": false, "displayName": "title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedin_url", "type": "string", "display": true, "removed": true, "required": false, "displayName": "linkedin_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedin_username", "type": "string", "display": true, "removed": true, "required": false, "displayName": "linkedin_username", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "extract_username_status", "type": "string", "display": true, "removed": true, "required": false, "displayName": "extract_username_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "email_address", "type": "string", "display": true, "removed": true, "required": false, "displayName": "email_address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "contacts_scrape_status", "type": "string", "display": true, "required": false, "displayName": "contacts_scrape_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "about_linkedin_profile", "type": "string", "display": true, "removed": true, "required": false, "displayName": "about_linkedin_profile", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "profile_summary_scrape", "type": "string", "display": true, "removed": true, "required": false, "displayName": "profile_summary_scrape", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "recent_posts_summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "recent_posts_summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "posts_scrape_status", "type": "string", "display": true, "removed": true, "required": false, "displayName": "posts_scrape_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["apollo_id"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit?usp=drivesdk", "cachedResultName": "apollo ai leads & enrichment"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "d0qeLhShx9sGXalR", "name": "Google Sheets"}}, "typeVersion": 4.5}, {"id": "2e1b9e78-8953-4059-9bbd-117c78742e4b", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1560, 560], "parameters": {"color": 5, "width": 800, "height": 360, "content": "## Update Contact Scrape Status from Invalid back to Pending"}, "typeVersion": 1}, {"id": "dc603dc3-148c-4609-a698-eaae32832423", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [1640, 680], "parameters": {"rule": {"interval": [{"field": "weeks", "triggerAtDay": [2], "triggerAtHour": 8, "weeksInterval": 4}]}}, "typeVersion": 1.2}, {"id": "a23feccf-f6b5-4440-b12a-ea10b41f4037", "name": "Get Email from Apollo", "type": "n8n-nodes-base.httpRequest", "position": [380, 680], "parameters": {"url": "https://api.apollo.io/api/v1/people/match", "method": "POST", "options": {}, "sendBody": true, "sendQuery": true, "sendHeaders": true, "bodyParameters": {"parameters": [{"name": "id", "value": "={{ $json.apollo_id }}"}]}, "queryParameters": {"parameters": [{"name": "reveal_personal_emails", "value": "true"}, {"name": "reveal_phone_number", "value": "false"}]}, "headerParameters": {"parameters": [{"name": "Cache-Control", "value": "no-cache"}, {"name": "accept", "value": "application/json"}, {"name": "x-api-key", "value": "\"your_api_key\""}]}}, "typeVersion": 4.2}, {"id": "dfd5224d-fdb6-41f2-a219-a4a8f94930cd", "name": "Confirm Email Validity", "type": "n8n-nodes-base.httpRequest", "position": [620, 680], "parameters": {"url": "=https://api.mails.so/v1/validate?email={{ $json.person.email }}", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-mails-api-key", "value": "\"your_api_key\""}]}}, "typeVersion": 4.2}, {"id": "14fab17d-b38b-43de-a38e-c8d49afcf1d0", "name": "Get Pending Email Statuses", "type": "n8n-nodes-base.googleSheets", "position": [140, 680], "parameters": {"options": {"returnFirstMatch": true}, "filtersUI": {"values": [{"lookupValue": "pending", "lookupColumn": "contacts_scrape_status"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit?usp=drivesdk", "cachedResultName": "a<PERSON><PERSON> ai leads"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "d0qeLhShx9sGXalR", "name": "Google Sheets"}}, "executeOnce": true, "typeVersion": 4.5}, {"id": "09a0c5eb-d098-47f1-a85c-13c93d8b5296", "name": "Google Sheets Trigger3", "type": "n8n-nodes-base.googleSheetsTrigger", "position": [-100, 1160], "parameters": {"event": "rowAdded", "options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit?usp=drivesdk", "cachedResultName": "a<PERSON><PERSON> ai leads"}}, "credentials": {"googleSheetsTriggerOAuth2Api": {"id": "DKYpQQpUt3ceJiG4", "name": "Google Sheets Trigger account"}}, "typeVersion": 1}, {"id": "3f592a9c-f692-44c0-b5be-9c5ff1506f24", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-200, 1040], "parameters": {"color": 6, "width": 1920, "height": 360, "content": "## Get Profile Summary & Update Status (Rapid API✅ - Primary Option)"}, "typeVersion": 1}, {"id": "6bc025b4-b150-493b-ae38-33772cb51a76", "name": "Get Profile Posts", "type": "n8n-nodes-base.httpRequest", "onError": "continueRegularOutput", "maxTries": 2, "position": [340, 1640], "parameters": {"url": "https://linkedin-data-api.p.rapidapi.com/get-profile-posts", "options": {}, "sendQuery": true, "sendHeaders": true, "queryParameters": {"parameters": [{"name": "username", "value": "={{ $json.linkedin_username }}"}, {"name": "start", "value": "0"}]}, "headerParameters": {"parameters": [{"name": "x-rapidapi-host", "value": "linkedin-data-api.p.rapidapi.com"}, {"name": "x-rapidapi-key", "value": "\"your_api_key\""}]}}, "retryOnFail": true, "typeVersion": 4.2}, {"id": "758da5bf-91e9-434d-9de5-eb3e907524e1", "name": "Get About Profile", "type": "n8n-nodes-base.httpRequest", "onError": "continueRegularOutput", "maxTries": 2, "position": [380, 1160], "parameters": {"url": "https://linkedin-data-api.p.rapidapi.com", "options": {}, "sendQuery": true, "sendHeaders": true, "queryParameters": {"parameters": [{"name": "username", "value": "={{ $json.linkedin_username }}"}]}, "headerParameters": {"parameters": [{"name": "x-rapidapi-host", "value": "linkedin-data-api.p.rapidapi.com"}, {"name": "x-rapidapi-key", "value": "\"your_api_key\""}]}}, "retryOnFail": true, "typeVersion": 4.2}, {"id": "9f88649f-3c41-4624-b94f-242d94118b0b", "name": "Get Pending About and Posts Rows", "type": "n8n-nodes-base.googleSheets", "position": [140, 1160], "parameters": {"options": {"returnFirstMatch": true}, "filtersUI": {"values": [{"lookupValue": "pending", "lookupColumn": "profile_summary_scrape"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit?usp=drivesdk", "cachedResultName": "a<PERSON><PERSON> ai leads"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "d0qeLhShx9sGXalR", "name": "Google Sheets"}}, "executeOnce": true, "typeVersion": 4.5}, {"id": "2e8bb4a1-5fa9-446a-8b55-bffd6ceb5648", "name": "Clean Profile Data", "type": "n8n-nodes-base.code", "onError": "continueErrorOutput", "maxTries": 2, "position": [620, 1160], "parameters": {"jsCode": "return [{\n  summary: $input.first().json.summary,\n  headline: $input.first().json.headline,\n  nationality: $input.first().json.geo.country,\n  languaage: $input.first().json.languages[0].name,\n  education: $input.first().json.educations[0].schoolName,\n  fieldOfStudy: $input.first().json.educations[0].fieldOfStudy,\n  employment_company: $input.first().json.position[0].companyName,\n  company_industry: $input.first().json.position[0].companyIndustry,\n  position: $input.first().json.position[0].title,\n  company_location: $input.first().json.position[0].location,\n  employment_description_1: $input.first().json.position[0].description,\n}];\n"}, "retryOnFail": true, "typeVersion": 2}, {"id": "87aa2108-44df-4b7f-8090-f2e86cbf5bf9", "name": "Google Sheets Trigger4", "type": "n8n-nodes-base.googleSheetsTrigger", "position": [-120, 1640], "parameters": {"event": "rowAdded", "options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit?usp=drivesdk", "cachedResultName": "a<PERSON><PERSON> ai leads"}}, "credentials": {"googleSheetsTriggerOAuth2Api": {"id": "DKYpQQpUt3ceJiG4", "name": "Google Sheets Trigger account"}}, "typeVersion": 1}, {"id": "e3318dd3-4b6a-4994-92bc-2e715f398397", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-200, 1520], "parameters": {"color": 7, "width": 1920, "height": 360, "content": "## Get Summary of Latest Linkedin Profile Posts (Rapid API✅ - Primary Option)"}, "typeVersion": 1}, {"id": "b1c117e8-493a-443f-912b-dbf4718c3348", "name": "Get Pending About and Posts Rows1", "type": "n8n-nodes-base.googleSheets", "position": [100, 1640], "parameters": {"options": {"returnFirstMatch": true}, "filtersUI": {"values": [{"lookupValue": "unscraped", "lookupColumn": "posts_scrape_status"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit?usp=drivesdk", "cachedResultName": "a<PERSON><PERSON> ai leads"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "d0qeLhShx9sGXalR", "name": "Google Sheets"}}, "executeOnce": true, "typeVersion": 4.5}, {"id": "c480f3ed-f316-4dd8-877b-49191d5aa694", "name": "Clean Posts Data", "type": "n8n-nodes-base.code", "onError": "continueErrorOutput", "maxTries": 2, "position": [580, 1640], "parameters": {"jsCode": "const data = $input.first().json.data || [];\n\nfunction getText(post, reshared = false) {\n  if (!post) return \"\";\n  return reshared ? (post.resharedPost?.text || \"\") : (post.text || \"\");\n}\n\nfunction getDate(post) {\n  if (!post) return \"\";\n  return post.postedDate || post.postedDateTimestamp || \"\";\n}\n\nreturn [{\n  json: {\n    post_1: getText(data[0]),\n    post_1_date: getDate(data[0]),\n\n    post_2: getText(data[1]),\n    post_2_date: getDate(data[1]),\n\n    post_3: getText(data[2]),\n    post_3_date: getDate(data[2]),\n\n    post_4: getText(data[3], true),\n    post_4_date: getDate(data[3]),\n\n    post_5: getText(data[4], true),\n    post_5_date: getDate(data[4]),\n  }\n}];\n"}, "retryOnFail": true, "typeVersion": 2, "waitBetweenTries": 3000}, {"id": "88d093e7-0180-4585-b338-11f4974fa0a7", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-200, 1980], "parameters": {"color": 4, "width": 800, "height": 360, "content": "## Update Completely Enriched Profile to Final Database"}, "typeVersion": 1}, {"id": "a9a96a05-f135-4a19-827f-85a750bc1551", "name": "Google Sheets Trigger5", "type": "n8n-nodes-base.googleSheetsTrigger", "position": [-120, 2100], "parameters": {"event": "rowAdded", "options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit?usp=drivesdk", "cachedResultName": "a<PERSON><PERSON> ai leads"}}, "credentials": {"googleSheetsTriggerOAuth2Api": {"id": "DKYpQQpUt3ceJiG4", "name": "Google Sheets Trigger account"}}, "typeVersion": 1}, {"id": "ef709a3f-7e75-4904-9d3e-299db8db5d60", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [1780, 1040], "parameters": {"color": 6, "width": 800, "height": 360, "content": "## Update profile summary status from failed back to pending"}, "typeVersion": 1}, {"id": "91a1eaed-8645-4eeb-8558-c28a6d55f689", "name": "Schedule Trigger2", "type": "n8n-nodes-base.scheduleTrigger", "position": [1860, 1160], "parameters": {"rule": {"interval": [{"field": "weeks", "triggerAtDay": [2], "triggerAtHour": 8, "weeksInterval": 4}]}}, "typeVersion": 1.2}, {"id": "f7658227-39b3-4efb-971c-f17860d20fb8", "name": "get invalid email rows", "type": "n8n-nodes-base.googleSheets", "position": [1880, 680], "parameters": {"options": {"returnFirstMatch": false}, "filtersUI": {"values": [{"lookupValue": "invalid_email", "lookupColumn": "contacts_scrape_status"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit?usp=drivesdk", "cachedResultName": "a<PERSON><PERSON> ai leads"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "d0qeLhShx9sGXalR", "name": "Google Sheets"}}, "executeOnce": true, "typeVersion": 4.5}, {"id": "fce87e8a-9eb3-4c86-a4e3-f93534333206", "name": "update_to_pending", "type": "n8n-nodes-base.googleSheets", "position": [2100, 680], "parameters": {"columns": {"value": {"apollo_id": "={{ $json.apollo_id }}", "contacts_scrape_status": "pending"}, "schema": [{"id": "apollo_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "apollo_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "name", "type": "string", "display": true, "removed": true, "required": false, "displayName": "name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "title", "type": "string", "display": true, "removed": true, "required": false, "displayName": "title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedin_url", "type": "string", "display": true, "removed": true, "required": false, "displayName": "linkedin_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedin_username", "type": "string", "display": true, "removed": true, "required": false, "displayName": "linkedin_username", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "extract_username_status", "type": "string", "display": true, "removed": true, "required": false, "displayName": "extract_username_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "email_address", "type": "string", "display": true, "removed": true, "required": false, "displayName": "email_address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "contacts_scrape_status", "type": "string", "display": true, "required": false, "displayName": "contacts_scrape_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "about_linkedin_profile", "type": "string", "display": true, "removed": true, "required": false, "displayName": "about_linkedin_profile", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "profile_summary_scrape", "type": "string", "display": true, "removed": true, "required": false, "displayName": "profile_summary_scrape", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "recent_posts_summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "recent_posts_summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "posts_scrape_status", "type": "string", "display": true, "removed": true, "required": false, "displayName": "posts_scrape_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["apollo_id"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit?usp=drivesdk", "cachedResultName": "apollo ai leads & enrichment"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "d0qeLhShx9sGXalR", "name": "Google Sheets"}}, "typeVersion": 4.5}, {"id": "9583cab2-6373-4e44-b9f2-ba0e8c8871e7", "name": "get_failed_profile_summary_rows", "type": "n8n-nodes-base.googleSheets", "position": [2100, 1160], "parameters": {"options": {"returnFirstMatch": false}, "filtersUI": {"values": [{"lookupValue": "failed", "lookupColumn": "profile_summary_scrape"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit?usp=drivesdk", "cachedResultName": "a<PERSON><PERSON> ai leads"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "d0qeLhShx9sGXalR", "name": "Google Sheets"}}, "executeOnce": true, "typeVersion": 4.5}, {"id": "a09d1560-2213-46ff-9668-5e98d5051e1f", "name": "update_to_pending1", "type": "n8n-nodes-base.googleSheets", "position": [2320, 1160], "parameters": {"columns": {"value": {"apollo_id": "={{ $json.apollo_id }}", "profile_summary_scrape": "pending"}, "schema": [{"id": "apollo_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "apollo_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "name", "type": "string", "display": true, "removed": true, "required": false, "displayName": "name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "title", "type": "string", "display": true, "removed": true, "required": false, "displayName": "title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedin_url", "type": "string", "display": true, "removed": true, "required": false, "displayName": "linkedin_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedin_username", "type": "string", "display": true, "removed": true, "required": false, "displayName": "linkedin_username", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "extract_username_status", "type": "string", "display": true, "removed": true, "required": false, "displayName": "extract_username_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "email_address", "type": "string", "display": true, "removed": true, "required": false, "displayName": "email_address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "contacts_scrape_status", "type": "string", "display": true, "removed": true, "required": false, "displayName": "contacts_scrape_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "about_linkedin_profile", "type": "string", "display": true, "removed": true, "required": false, "displayName": "about_linkedin_profile", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "profile_summary_scrape", "type": "string", "display": true, "removed": false, "required": false, "displayName": "profile_summary_scrape", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "recent_posts_summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "recent_posts_summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "posts_scrape_status", "type": "string", "display": true, "removed": true, "required": false, "displayName": "posts_scrape_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["apollo_id"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit?usp=drivesdk", "cachedResultName": "apollo ai leads & enrichment"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "d0qeLhShx9sGXalR", "name": "Google Sheets"}}, "typeVersion": 4.5}, {"id": "809d74c0-bb21-4651-bf52-ccb7e8fac5a6", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [1760, 1520], "parameters": {"color": 7, "width": 800, "height": 360, "content": "## Update posts summary status from failed back to pending"}, "typeVersion": 1}, {"id": "ee1a5073-87a9-4395-9c79-b56165a5325d", "name": "Schedule Trigger3", "type": "n8n-nodes-base.scheduleTrigger", "position": [1840, 1640], "parameters": {"rule": {"interval": [{"field": "weeks", "triggerAtDay": [2], "triggerAtHour": 8, "weeksInterval": 4}]}}, "typeVersion": 1.2}, {"id": "4a89e593-adb3-4999-bb0a-cf8cc751a76a", "name": "get_failed_posts_summary_rows1", "type": "n8n-nodes-base.googleSheets", "position": [2080, 1640], "parameters": {"options": {"returnFirstMatch": false}, "filtersUI": {"values": [{"lookupValue": "failed", "lookupColumn": "posts_scrape_status"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit?usp=drivesdk", "cachedResultName": "a<PERSON><PERSON> ai leads"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "d0qeLhShx9sGXalR", "name": "Google Sheets"}}, "executeOnce": true, "typeVersion": 4.5}, {"id": "369715fe-67c5-41f1-9070-af5063f80fe0", "name": "Posts AI Summarizer", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1060, 1560], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-3.5-turbo", "cachedResultName": "GPT-3.5-TURBO"}, "options": {}, "messages": {"values": [{"content": "=Below are the most recent posts and reposts from a LinkedIn user. Summarize them collectively in no more than two short paragraphs. Focus on capturing the main themes, tone, and any recurring interests or professional concerns.\n\nAvoid listing each post separately — instead, synthesize the information into a narrative that gives a clear idea of what this person is currently focused on or passionate about.\n\nPosts: {{ $json.postsString }}\n\nKeep it insightful but brief — no more than 2 concise paragraphs."}]}, "simplify": false}, "credentials": {"openAiApi": {"id": "DO9F6MAeTGLeqgoF", "name": "OpenAi account"}}, "executeOnce": true, "typeVersion": 1.8}, {"id": "a571b341-1a4b-42d7-a4fc-5dd8333e3dab", "name": "AI Profile Summarizer", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1100, 1080], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-3.5-turbo", "cachedResultName": "GPT-3.5-TURBO"}, "options": {}, "messages": {"values": [{"content": "=Please summarize the following linkedin profile data {{ $json.profileString }} for a lead I want to cold email. I want to combine this summary with another information about them to send personalied emails, so please make sure you include relevant bits in the summary"}]}, "simplify": false}, "credentials": {"openAiApi": {"id": "DO9F6MAeTGLeqgoF", "name": "OpenAi account"}}, "executeOnce": true, "typeVersion": 1.8}, {"id": "8a81b29d-0ffa-4418-9aec-21eb94df7d26", "name": "Update Profile Summary", "type": "n8n-nodes-base.googleSheets", "position": [1500, 1080], "parameters": {"columns": {"value": {"apollo_id": "={{ $('Get Pending About and Posts Rows').item.json.apollo_id }}", "about_linkedin_profile": "={{ $json.choices[0].message.content }}", "profile_summary_scrape": "completed"}, "schema": [{"id": "apollo_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "apollo_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "name", "type": "string", "display": true, "removed": true, "required": false, "displayName": "name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "title", "type": "string", "display": true, "removed": true, "required": false, "displayName": "title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedin_url", "type": "string", "display": true, "removed": true, "required": false, "displayName": "linkedin_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedin_username", "type": "string", "display": true, "removed": true, "required": false, "displayName": "linkedin_username", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "extract_username_status", "type": "string", "display": true, "removed": true, "required": false, "displayName": "extract_username_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "email_address", "type": "string", "display": true, "removed": true, "required": false, "displayName": "email_address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "contacts_scrape_status", "type": "string", "display": true, "removed": true, "required": false, "displayName": "contacts_scrape_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "about_linkedin_profile", "type": "string", "display": true, "required": false, "displayName": "about_linkedin_profile", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "profile_summary_scrape", "type": "string", "display": true, "required": false, "displayName": "profile_summary_scrape", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "recent_posts_summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "recent_posts_summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "posts_scrape_status", "type": "string", "display": true, "removed": true, "required": false, "displayName": "posts_scrape_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["apollo_id"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit?usp=drivesdk", "cachedResultName": "apollo ai leads & enrichment"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "d0qeLhShx9sGXalR", "name": "Google Sheets"}}, "typeVersion": 4.5}, {"id": "4803c443-be3c-4578-90fc-ab78403a0913", "name": "Update Posts Summary", "type": "n8n-nodes-base.googleSheets", "position": [1440, 1560], "parameters": {"columns": {"value": {"apollo_id": "={{ $('Get Pending About and Posts Rows1').item.json.apollo_id }}", "posts_scrape_status": "scraped", "recent_posts_summary": "={{ $json.choices[0].message.content }}"}, "schema": [{"id": "apollo_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "apollo_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "name", "type": "string", "display": true, "removed": true, "required": false, "displayName": "name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "organization", "type": "string", "display": true, "removed": true, "required": false, "displayName": "organization", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "title", "type": "string", "display": true, "removed": true, "required": false, "displayName": "title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedin_url", "type": "string", "display": true, "removed": true, "required": false, "displayName": "linkedin_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedin_username", "type": "string", "display": true, "removed": true, "required": false, "displayName": "linkedin_username", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "extract_username_status", "type": "string", "display": true, "removed": true, "required": false, "displayName": "extract_username_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "email_address", "type": "string", "display": true, "removed": true, "required": false, "displayName": "email_address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "contacts_scrape_status", "type": "string", "display": true, "removed": true, "required": false, "displayName": "contacts_scrape_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "about_linkedin_profile", "type": "string", "display": true, "removed": true, "required": false, "displayName": "about_linkedin_profile", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "profile_summary_scrape", "type": "string", "display": true, "removed": true, "required": false, "displayName": "profile_summary_scrape", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "recent_posts_summary", "type": "string", "display": true, "removed": false, "required": false, "displayName": "recent_posts_summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "posts_scrape_status", "type": "string", "display": true, "removed": false, "required": false, "displayName": "posts_scrape_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["apollo_id"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit?usp=drivesdk", "cachedResultName": "apollo ai leads & enrichment"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "d0qeLhShx9sGXalR", "name": "Google Sheets"}}, "typeVersion": 4.5}, {"id": "ccf8b76d-1aba-4c5a-b19e-30aac3ab682a", "name": "Get Completely Enriched Profiles", "type": "n8n-nodes-base.googleSheets", "position": [120, 2100], "parameters": {"options": {"returnFirstMatch": true}, "filtersUI": {"values": [{"lookupValue": "finished", "lookupColumn": "contacts_scrape_status"}, {"lookupValue": "completed", "lookupColumn": "profile_summary_scrape"}, {"lookupValue": "scraped", "lookupColumn": "posts_scrape_status"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit?usp=drivesdk", "cachedResultName": "a<PERSON><PERSON> ai leads"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "d0qeLhShx9sGXalR", "name": "Google Sheets"}}, "executeOnce": true, "typeVersion": 4.5}, {"id": "bfe41bd1-3edc-456a-9bf4-8e88009060e4", "name": "update_to_unscraped", "type": "n8n-nodes-base.googleSheets", "position": [2300, 1640], "parameters": {"columns": {"value": {"apollo_id": "={{ $json.apollo_id }}", "posts_scrape_status": "unscraped"}, "schema": [{"id": "apollo_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "apollo_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "name", "type": "string", "display": true, "removed": true, "required": false, "displayName": "name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "title", "type": "string", "display": true, "removed": true, "required": false, "displayName": "title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedin_url", "type": "string", "display": true, "removed": true, "required": false, "displayName": "linkedin_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedin_username", "type": "string", "display": true, "removed": true, "required": false, "displayName": "linkedin_username", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "extract_username_status", "type": "string", "display": true, "removed": true, "required": false, "displayName": "extract_username_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "email_address", "type": "string", "display": true, "removed": true, "required": false, "displayName": "email_address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "contacts_scrape_status", "type": "string", "display": true, "removed": true, "required": false, "displayName": "contacts_scrape_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "about_linkedin_profile", "type": "string", "display": true, "removed": true, "required": false, "displayName": "about_linkedin_profile", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "profile_summary_scrape", "type": "string", "display": true, "removed": true, "required": false, "displayName": "profile_summary_scrape", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "recent_posts_summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "recent_posts_summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "posts_scrape_status", "type": "string", "display": true, "removed": false, "required": false, "displayName": "posts_scrape_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["apollo_id"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit?usp=drivesdk", "cachedResultName": "apollo ai leads & enrichment"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "d0qeLhShx9sGXalR", "name": "Google Sheets"}}, "typeVersion": 4.5}, {"id": "0d6e42a6-8b7e-493e-b920-7cb4d3994151", "name": "Append to Enriched Leads Database", "type": "n8n-nodes-base.googleSheets", "position": [340, 2100], "parameters": {"columns": {"value": {"Position": "={{ $json.title }}", "Lead Name": "={{ $json.name }}", "Email Address": "={{ $json.email_address }}", "Company/Organization": "={{ $json.organization }}", "Recent Posts Summary": "={{ $json.recent_posts_summary }}", "Linkedin Profile Summary": "={{ $json.about_linkedin_profile }}"}, "schema": [{"id": "Lead Name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Lead Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Email Address", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Email Address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Company/Organization", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Company/Organization", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Position", "type": "string", "display": true, "required": false, "displayName": "Position", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Linkedin Profile Summary", "type": "string", "display": true, "required": false, "displayName": "Linkedin Profile Summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Recent Posts Summary", "type": "string", "display": true, "required": false, "displayName": "Recent Posts Summary", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["Email Address"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1c5USULUPS-2_RdNf29cyDguuHH7A7JNwzFCjQQUJTvE/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1c5USULUPS-2_RdNf29cyDguuHH7A7JNwzFCjQQUJTvE", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1c5USULUPS-2_RdNf29cyDguuHH7A7JNwzFCjQQUJTvE/edit?usp=drivesdk", "cachedResultName": "Enriched Leads Database"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "d0qeLhShx9sGXalR", "name": "Google Sheets"}}, "typeVersion": 4.5}, {"id": "dfc1a7e4-6348-4a84-b6b9-d9f53184948e", "name": "update status to failed", "type": "n8n-nodes-base.googleSheets", "position": [880, 1240], "parameters": {"columns": {"value": {"apollo_id": "={{ $('Get Pending About and Posts Rows').item.json.apollo_id }}", "profile_summary_scrape": "failed"}, "schema": [{"id": "apollo_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "apollo_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "name", "type": "string", "display": true, "removed": true, "required": false, "displayName": "name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "organization", "type": "string", "display": true, "removed": true, "required": false, "displayName": "organization", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "title", "type": "string", "display": true, "removed": true, "required": false, "displayName": "title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedin_url", "type": "string", "display": true, "removed": true, "required": false, "displayName": "linkedin_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedin_username", "type": "string", "display": true, "removed": true, "required": false, "displayName": "linkedin_username", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "extract_username_status", "type": "string", "display": true, "removed": true, "required": false, "displayName": "extract_username_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "email_address", "type": "string", "display": true, "removed": true, "required": false, "displayName": "email_address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "contacts_scrape_status", "type": "string", "display": true, "removed": true, "required": false, "displayName": "contacts_scrape_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "about_linkedin_profile", "type": "string", "display": true, "removed": true, "required": false, "displayName": "about_linkedin_profile", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "profile_summary_scrape", "type": "string", "display": true, "required": false, "displayName": "profile_summary_scrape", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "recent_posts_summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "recent_posts_summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "posts_scrape_status", "type": "string", "display": true, "removed": true, "required": false, "displayName": "posts_scrape_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["apollo_id"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit?usp=drivesdk", "cachedResultName": "apollo ai leads & enrichment"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "d0qeLhShx9sGXalR", "name": "Google Sheets"}}, "typeVersion": 4.5}, {"id": "a14fce10-9a03-4005-b440-f2bb5e4c4eed", "name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [840, 1720], "parameters": {"columns": {"value": {"apollo_id": "={{ $('Get Pending About and Posts Rows1').item.json.apollo_id }}", "posts_scrape_status": "failed"}, "schema": [{"id": "apollo_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "apollo_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "name", "type": "string", "display": true, "removed": true, "required": false, "displayName": "name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "organization", "type": "string", "display": true, "removed": true, "required": false, "displayName": "organization", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "title", "type": "string", "display": true, "removed": true, "required": false, "displayName": "title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedin_url", "type": "string", "display": true, "removed": true, "required": false, "displayName": "linkedin_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedin_username", "type": "string", "display": true, "removed": true, "required": false, "displayName": "linkedin_username", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "extract_username_status", "type": "string", "display": true, "removed": true, "required": false, "displayName": "extract_username_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "email_address", "type": "string", "display": true, "removed": true, "required": false, "displayName": "email_address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "contacts_scrape_status", "type": "string", "display": true, "removed": true, "required": false, "displayName": "contacts_scrape_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "about_linkedin_profile", "type": "string", "display": true, "removed": true, "required": false, "displayName": "about_linkedin_profile", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "profile_summary_scrape", "type": "string", "display": true, "removed": true, "required": false, "displayName": "profile_summary_scrape", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "recent_posts_summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "recent_posts_summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "posts_scrape_status", "type": "string", "display": true, "required": false, "displayName": "posts_scrape_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["apollo_id"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit?usp=drivesdk", "cachedResultName": "apollo ai leads & enrichment"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "d0qeLhShx9sGXalR", "name": "Google Sheets"}}, "typeVersion": 4.5}, {"id": "77a48254-5972-4a41-90e9-3903219e78cc", "name": "Stringify Profile Data1", "type": "n8n-nodes-base.code", "position": [880, 1080], "parameters": {"jsCode": "const profile = items[0].json;\n\nreturn [{\n  json: {\n    profileString: JSON.stringify(profile, null, 2) // Pretty print with 2-space indentation\n  }\n}];"}, "typeVersion": 2}, {"id": "fb8cf143-7a62-4fdc-8888-b60841354352", "name": "Stringify Posts Data", "type": "n8n-nodes-base.code", "position": [840, 1560], "parameters": {"jsCode": "const profile = items[0].json;\n\nreturn [{\n  json: {\n    postsString: JSON.stringify(profile, null, 2) // Pretty print with 2-space indentation\n  }\n}];"}, "typeVersion": 2}, {"id": "c440d713-3371-41a5-9093-8084ecb0eabd", "name": "Generate Leads with Apollo.io", "type": "n8n-nodes-base.httpRequest", "position": [140, -80], "parameters": {"url": "https://api.apollo.io/api/v1/mixed_people/search", "method": "POST", "options": {"response": {"response": {}}}, "jsonBody": "={\n  \"person_locations\": [\"{{ $json.Location }}\"],\n  \"person_titles\": [\"{{ $json['Job Title'] }}\"],\n  \"page\": 1,\n  \"per_page\": {{$json['Number of Leads']}},\n  \"projection\": [\"id\", \"name\", \"linkedin_url\", \"title\"]\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "Cache-Control", "value": "no-cache"}, {"name": "accept", "value": "application/json"}, {"name": "x-api-key", "value": "\"your_api_key\""}]}}, "typeVersion": 4.2}, {"id": "81edf4b0-cc78-4d62-ba0d-5e655c01c824", "name": "Google Sheets Trigger6", "type": "n8n-nodes-base.googleSheetsTrigger", "position": [2920, 1380], "parameters": {"event": "rowAdded", "options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit?usp=drivesdk", "cachedResultName": "a<PERSON><PERSON> ai leads"}}, "credentials": {"googleSheetsTriggerOAuth2Api": {"id": "DKYpQQpUt3ceJiG4", "name": "Google Sheets Trigger account"}}, "typeVersion": 1}, {"id": "7227fad7-9799-4159-b522-d552e0c0bd44", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [2840, 1060], "parameters": {"color": 3, "width": 2040, "height": 820, "content": "## Get Profile and Latest Posts Summary & Update Status (Apify Alternative - Secondary, if rapid api option doesn't work )\nSome users that dont have an account on rapid API cant sign up hence are unable to get to the api and get an api key. This alternative is tailored for you. Immediatelty you get access to rapid API, please switch back to using that one. It is more reliable in scraping detailed data. See updated [documentation](https://docs.google.com/document/d/1U0Kk0HXO-9thjPcgFPu2Jk_yMtqLTv-hZobaKIGWOxM/edit?usp=sharing)"}, "typeVersion": 1}, {"id": "c807c49c-ddea-4d63-8d06-070c13ae34f3", "name": "Get Pending About and Posts Rows2", "type": "n8n-nodes-base.googleSheets", "position": [3160, 1380], "parameters": {"options": {"returnFirstMatch": true}, "filtersUI": {"values": [{"lookupValue": "pending", "lookupColumn": "profile_summary_scrape"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit?usp=drivesdk", "cachedResultName": "a<PERSON><PERSON> ai leads"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "d0qeLhShx9sGXalR", "name": "Google Sheets"}}, "executeOnce": true, "typeVersion": 4.5}, {"id": "7d79491d-5ab8-4435-b655-97e81380428f", "name": "Clean Profile Data1", "type": "n8n-nodes-base.code", "maxTries": 2, "position": [3720, 1280], "parameters": {"jsCode": "// Safe LinkedIn profile details scraper for n8n\nconst profileData = {};\nconst input = $input.first() && $input.first().json ? $input.first().json : {};\n\n// Basic profile information with fallbacks\nif (input.about) profileData.summary = input.about;\nif (input.headline) profileData.headline = input.headline;\nif (input.addressCountryOnly) profileData.nationality = input.addressCountryOnly;\n\n// Education information with fallbacks\nif (input.educations && Array.isArray(input.educations) && input.educations.length > 0) {\n  const education = input.educations[0];\n  if (education.title) profileData.education = education.title;\n  if (education.subtitle) profileData.fieldOfStudy = education.subtitle;\n}\n\n// Employment information with fallbacks\nif (input.experiences && Array.isArray(input.experiences) && input.experiences.length > 0) {\n  const experience = input.experiences[0];\n  if (experience.subtitle) profileData.employment_company = experience.subtitle;\n  if (experience.title) profileData.position = experience.title;\n  if (experience.metadata) profileData.company_location = experience.metadata;\n}\n\n// Return the profile data in n8n expected format\nreturn [{ json: profileData }];"}, "retryOnFail": true, "typeVersion": 2}, {"id": "e13acd2c-5d4a-4683-9293-6520fc4da4a6", "name": "Stringify Data2", "type": "n8n-nodes-base.code", "position": [3900, 1280], "parameters": {"jsCode": "const profile = items[0].json;\n\nreturn [{\n  json: {\n    profileString: JSON.stringify(profile, null, 2) // Pretty print with 2-space indentation\n  }\n}];"}, "typeVersion": 2}, {"id": "e774ffc5-2c4a-46fc-89a1-1ecb3ba2ef10", "name": "AI Profile Summarizer1", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [4100, 1280], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-3.5-turbo", "cachedResultName": "GPT-3.5-TURBO"}, "options": {}, "messages": {"values": [{"content": "=Please summarize the following linkedin profile data {{ $json.profileString }} for a lead I want to cold email. I want to combine this summary with another information about them to send personalied emails, so please make sure you include relevant bits in the summary"}]}, "simplify": false}, "credentials": {"openAiApi": {"id": "DO9F6MAeTGLeqgoF", "name": "OpenAi account"}}, "executeOnce": true, "typeVersion": 1.8}, {"id": "557a763a-8388-4f34-bd35-d91cd7018b97", "name": "Update Profile Summary1", "type": "n8n-nodes-base.googleSheets", "position": [4640, 1380], "parameters": {"columns": {"value": {"apollo_id": "={{ $('Get Pending About and Posts Rows2').item.json.apollo_id }}", "posts_scrape_status": "scraped", "about_linkedin_profile": "={{ $json.choices[0].message.content }}", "profile_summary_scrape": "completed"}, "schema": [{"id": "apollo_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "apollo_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "name", "type": "string", "display": true, "removed": true, "required": false, "displayName": "name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "organization", "type": "string", "display": true, "removed": true, "required": false, "displayName": "organization", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "title", "type": "string", "display": true, "removed": true, "required": false, "displayName": "title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedin_url", "type": "string", "display": true, "removed": true, "required": false, "displayName": "linkedin_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedin_username", "type": "string", "display": true, "removed": true, "required": false, "displayName": "linkedin_username", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "extract_username_status", "type": "string", "display": true, "removed": true, "required": false, "displayName": "extract_username_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "email_address", "type": "string", "display": true, "removed": true, "required": false, "displayName": "email_address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "contacts_scrape_status", "type": "string", "display": true, "removed": true, "required": false, "displayName": "contacts_scrape_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "about_linkedin_profile", "type": "string", "display": true, "required": false, "displayName": "about_linkedin_profile", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "profile_summary_scrape", "type": "string", "display": true, "required": false, "displayName": "profile_summary_scrape", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "recent_posts_summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "recent_posts_summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "posts_scrape_status", "type": "string", "display": true, "removed": false, "required": false, "displayName": "posts_scrape_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["apollo_id"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit?usp=drivesdk", "cachedResultName": "apollo ai leads & enrichment"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "d0qeLhShx9sGXalR", "name": "Google Sheets"}}, "typeVersion": 4.5}, {"id": "867e28c2-e24f-419c-838f-d1ddc97cbd84", "name": "Stringify Data3", "type": "n8n-nodes-base.code", "position": [3900, 1520], "parameters": {"jsCode": "const profile = items[0].json;\n\nreturn [{\n  json: {\n    postsString: JSON.stringify(profile, null, 2) // Pretty print with 2-space indentation\n  }\n}];"}, "typeVersion": 2}, {"id": "ba3ba6c9-803d-4f84-ad95-314fbe68e391", "name": "Posts AI Summarizer1", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [4100, 1520], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-3.5-turbo", "cachedResultName": "GPT-3.5-TURBO"}, "options": {}, "messages": {"values": [{"content": "=Below are the most recent posts and reposts from a LinkedIn user. Summarize them collectively in no more than two short paragraphs. Focus on capturing the main themes, tone, and any recurring interests or professional concerns.\n\nAvoid listing each post separately — instead, synthesize the information into a narrative that gives a clear idea of what this person is currently focused on or passionate about.\n\nPosts: {{ $json.postsString }}\n\nKeep it insightful but brief — no more than 2 concise paragraphs."}]}, "simplify": false}, "credentials": {"openAiApi": {"id": "DO9F6MAeTGLeqgoF", "name": "OpenAi account"}}, "executeOnce": true, "typeVersion": 1.8}, {"id": "8de0e703-1af4-491a-b915-391e94bc0b18", "name": "Get About Profile2", "type": "n8n-nodes-base.httpRequest", "onError": "continueErrorOutput", "position": [3400, 1380], "parameters": {"url": "\"apify-actor-endpoint\"", "options": {}, "jsonBody": "={\n    \"profileUrls\": [\n        \"{{ $json.linkedin_url }}\"\n    ]\n}", "sendBody": true, "specifyBody": "json"}, "typeVersion": 4.2}, {"id": "2a082269-015e-4d01-850b-89db56135df1", "name": "Clean Profile Data2", "type": "n8n-nodes-base.code", "maxTries": 2, "position": [3720, 1520], "parameters": {"jsCode": "// n8n function to extract LinkedIn posts\nconst items = [];\n\n// Check if input exists and has updates\nif (!$input.first() || !$input.first().json || !$input.first().json.updates || !Array.isArray($input.first().json.updates)) {\n  // Return a single item with \"no posts\" message\n  items.push({\n    json: {\n      message: \"No posts found\"\n    }\n  });\n  return items;\n}\n\nconst updates = $input.first().json.updates;\n\n// If no posts available, return the \"no posts\" message\nif (updates.length === 0) {\n  items.push({\n    json: {\n      message: \"No posts found\"\n    }\n  });\n  return items;\n}\n\n// Process available posts (up to 4)\nfor (let i = 0; i < Math.min(updates.length, 4); i++) {\n  if (updates[i] && updates[i].postText) {\n    // Create an item for each post with the required n8n structure\n    items.push({\n      json: {\n        postNumber: i + 1,\n        postText: updates[i].postText\n      }\n    });\n  }\n}\n\n// If no valid posts were processed\nif (items.length === 0) {\n  items.push({\n    json: {\n      message: \"No posts found\"\n    }\n  });\n}\n\nreturn items;"}, "retryOnFail": true, "typeVersion": 2}, {"id": "966bf4ba-2680-4092-b62c-d37f77ffa2fc", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [4460, 1380], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineAll"}, "typeVersion": 3.1}, {"id": "36682aa5-73bf-405b-ab10-43bc144b37af", "name": "update status to failed1", "type": "n8n-nodes-base.googleSheets", "position": [3400, 1640], "parameters": {"columns": {"value": {"apollo_id": "={{ $('Get Pending About and Posts Rows2').item.json.apollo_id }}", "profile_summary_scrape": "failed"}, "schema": [{"id": "apollo_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "apollo_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "name", "type": "string", "display": true, "removed": true, "required": false, "displayName": "name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "organization", "type": "string", "display": true, "removed": true, "required": false, "displayName": "organization", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "title", "type": "string", "display": true, "removed": true, "required": false, "displayName": "title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedin_url", "type": "string", "display": true, "removed": true, "required": false, "displayName": "linkedin_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedin_username", "type": "string", "display": true, "removed": true, "required": false, "displayName": "linkedin_username", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "extract_username_status", "type": "string", "display": true, "removed": true, "required": false, "displayName": "extract_username_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "email_address", "type": "string", "display": true, "removed": true, "required": false, "displayName": "email_address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "contacts_scrape_status", "type": "string", "display": true, "removed": true, "required": false, "displayName": "contacts_scrape_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "about_linkedin_profile", "type": "string", "display": true, "removed": true, "required": false, "displayName": "about_linkedin_profile", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "profile_summary_scrape", "type": "string", "display": true, "required": false, "displayName": "profile_summary_scrape", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "recent_posts_summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "recent_posts_summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "posts_scrape_status", "type": "string", "display": true, "removed": true, "required": false, "displayName": "posts_scrape_status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["apollo_id"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d99PlHkp9RPeSAtmATgQ4OC4Selcp8JSFLNuKx-n1EQ/edit?usp=drivesdk", "cachedResultName": "apollo ai leads & enrichment"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "d0qeLhShx9sGXalR", "name": "Google Sheets"}}, "typeVersion": 4.5}, {"id": "a3d2a7e0-87ed-46ac-bb75-91c54e6e1f09", "name": "If email is valid", "type": "n8n-nodes-base.if", "position": [880, 680], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "loose"}, "combinator": "or", "conditions": [{"id": "bc3200af-7ae4-4944-b410-ebb459e2d927", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.data.result }}", "rightValue": "undeliverable"}, {"id": "b0c7d1c3-a733-4547-899e-ff76b66765ad", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.data.mx_record }}", "rightValue": "[null]"}]}, "looseTypeValidation": true}, "typeVersion": 2.2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "eef068bb-348c-46c5-940b-d6042431aa01", "connections": {"Merge": {"main": [[{"node": "Update Profile Summary1", "type": "main", "index": 0}]]}, "OpenAI1": {"main": [[{"node": "Add Linkedin Username", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Clean Data", "type": "main", "index": 0}]]}, "Clean Data": {"main": [[{"node": "Add Leads to Google Sheet", "type": "main", "index": 0}]]}, "Stringify Data2": {"main": [[{"node": "AI Profile Summarizer1", "type": "main", "index": 0}]]}, "Stringify Data3": {"main": [[{"node": "Posts AI Summarizer1", "type": "main", "index": 0}]]}, "Clean Posts Data": {"main": [[{"node": "Stringify Posts Data", "type": "main", "index": 0}], [{"node": "Google Sheets", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "get invalid email rows", "type": "main", "index": 0}]]}, "Get About Profile": {"main": [[{"node": "Clean Profile Data", "type": "main", "index": 0}]]}, "Get Profile Posts": {"main": [[{"node": "Clean Posts Data", "type": "main", "index": 0}]]}, "If email is valid": {"main": [[{"node": "Add Email Address", "type": "main", "index": 0}], [{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Schedule Trigger2": {"main": [[{"node": "get_failed_profile_summary_rows", "type": "main", "index": 0}]]}, "Schedule Trigger3": {"main": [[{"node": "get_failed_posts_summary_rows1", "type": "main", "index": 0}]]}, "update_to_pending": {"main": [[]]}, "Clean Profile Data": {"main": [[{"node": "Stringify Profile Data1", "type": "main", "index": 0}], [{"node": "update status to failed", "type": "main", "index": 0}]]}, "Get About Profile2": {"main": [[{"node": "Clean Profile Data1", "type": "main", "index": 0}, {"node": "Clean Profile Data2", "type": "main", "index": 0}], [{"node": "update status to failed1", "type": "main", "index": 0}]]}, "On form submission": {"main": [[{"node": "Generate Leads with Apollo.io", "type": "main", "index": 0}]]}, "Clean Profile Data1": {"main": [[{"node": "Stringify Data2", "type": "main", "index": 0}]]}, "Clean Profile Data2": {"main": [[{"node": "Stringify Data3", "type": "main", "index": 0}]]}, "Posts AI Summarizer": {"main": [[{"node": "Update Posts Summary", "type": "main", "index": 0}]]}, "Posts AI Summarizer1": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Stringify Posts Data": {"main": [[{"node": "Posts AI Summarizer", "type": "main", "index": 0}]]}, "AI Profile Summarizer": {"main": [[{"node": "Update Profile Summary", "type": "main", "index": 0}]]}, "Get Email from Apollo": {"main": [[{"node": "Confirm Email Validity", "type": "main", "index": 0}]]}, "Google Sheets Trigger": {"main": [[{"node": "Get Pending Username Row", "type": "main", "index": 0}]]}, "AI Profile Summarizer1": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Confirm Email Validity": {"main": [[{"node": "If email is valid", "type": "main", "index": 0}]]}, "Google Sheets Trigger2": {"main": [[{"node": "Get Pending Email Statuses", "type": "main", "index": 0}]]}, "Google Sheets Trigger3": {"main": [[{"node": "Get Pending About and Posts Rows", "type": "main", "index": 0}]]}, "Google Sheets Trigger4": {"main": [[{"node": "Get Pending About and Posts Rows1", "type": "main", "index": 0}]]}, "Google Sheets Trigger5": {"main": [[{"node": "Get Completely Enriched Profiles", "type": "main", "index": 0}]]}, "Google Sheets Trigger6": {"main": [[{"node": "Get Pending About and Posts Rows2", "type": "main", "index": 0}]]}, "get invalid email rows": {"main": [[{"node": "update_to_pending", "type": "main", "index": 0}]]}, "Stringify Profile Data1": {"main": [[{"node": "AI Profile Summarizer", "type": "main", "index": 0}]]}, "Get Pending Username Row": {"main": [[{"node": "OpenAI1", "type": "main", "index": 0}]]}, "Get Pending Email Statuses": {"main": [[{"node": "Get Email from Apollo", "type": "main", "index": 0}]]}, "Generate Leads with Apollo.io": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "get_failed_posts_summary_rows1": {"main": [[{"node": "update_to_unscraped", "type": "main", "index": 0}]]}, "get_failed_profile_summary_rows": {"main": [[{"node": "update_to_pending1", "type": "main", "index": 0}]]}, "Get Completely Enriched Profiles": {"main": [[{"node": "Append to Enriched Leads Database", "type": "main", "index": 0}]]}, "Get Pending About and Posts Rows": {"main": [[{"node": "Get About Profile", "type": "main", "index": 0}]]}, "Get Pending About and Posts Rows1": {"main": [[{"node": "Get Profile Posts", "type": "main", "index": 0}]]}, "Get Pending About and Posts Rows2": {"main": [[{"node": "Get About Profile2", "type": "main", "index": 0}]]}}}