{"meta": {"instanceId": "4359279a248a64f23ddf72d3bc2de4dead8a687e643e9296f8a007dd65120396"}, "nodes": [{"id": "59b786fe-8e45-4616-aa45-9748df144c3a", "name": "MySQL", "type": "n8n-nodes-base.mySql", "position": [-80, 220], "parameters": {"query": "SELECT \n    company,\n    cost_center AS default_cost_center,\n    COUNT(*) AS project_count\nFROM \n    tabProject\nWHERE \n    status = 'Open' \n    AND project_type = 'External'\n    AND is_active = 'Yes'\n    AND budgeted_project_cost = 0\n    \nGROUP BY \n    company, cost_center\nORDER BY \n    company, project_count DESC;\n", "options": {}, "operation": "execute<PERSON>uery"}, "typeVersion": 2.4}, {"id": "48c20822-9f2e-4108-8bfb-b300689a9724", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-360, 220], "parameters": {"rule": {"interval": [{"field": "weeks", "triggerAtHour": 8}]}}, "typeVersion": 1.2}, {"id": "3757860b-b7a0-4617-a398-37ac42f1acea", "name": "Switch", "type": "n8n-nodes-base.switch", "position": [180, 200], "parameters": {"rules": {"values": [{"outputKey": "A", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "423062ba-e116-4e22-aa00-29107e8c24ce", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.default_cost_center }}", "rightValue": "Cost Center A"}]}, "renameOutput": true}, {"outputKey": "B", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "e065ab84-61fd-4e6c-8835-92d08be3e359", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.default_cost_center }}", "rightValue": "Cost Center B"}]}, "renameOutput": true}, {"outputKey": "C", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "0ef8ce35-2507-4ff4-8dea-11380262098e", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.default_cost_center }}", "rightValue": "=COST CENTER C"}]}, "renameOutput": true}, {"outputKey": "D", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "9152e548-cca9-441c-b4b6-8903f449dc2b", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.default_cost_center }}", "rightValue": "Cost Center D"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "bf8fd5f4-e107-44e8-af1a-be32596d664e", "name": "Microsoft Outlook6", "type": "n8n-nodes-base.microsoftOutlook", "position": [560, -20], "webhookId": "dce42873-919a-4dac-9f9d-792b0a39b7f7", "parameters": {"subject": "Project Cost Missing", "bodyContent": "==<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <title>Missing Budgeted Cost Notification</title>\n  <style>\n    body { font-family: Arial, sans-serif; background-color: #f4f4f4; margin: 0; padding: 0; }\n    .email-container { max-width: 600px; margin: 20px auto; background-color: #ffffff; border-radius: 8px; overflow: hidden; }\n    .email-header { background-color: #007BFF; color: #ffffff; padding: 20px; text-align: center; font-size: 18px; font-weight: bold; }\n    .email-body { padding: 20px; font-size: 16px; color: #333333; }\n    .email-body strong { color: #007BFF; }\n    .email-footer { padding: 10px 20px; font-size: 14px; color: #555555; text-align: left; }\n  </style>\n</head>\n<body>\n  <div class=\"email-container\">\n    <div class=\"email-header\">\n      {{ $json.default_cost_center }} - Project Data Missing\n    </div>\n    <div class=\"email-body\">\n      Dear {{ $json.default_cost_center }} Team,<br><br>\n      There are <strong>{{ $json.project_count }}</strong> active projects with missing <strong>Budgeted Cost</strong>.<br>\n      Kindly coordinate with the <strong>Accounts Team</strong> to update the missing values for accurate tracking.<br><br>\n      Your timely attention is appreciated.<br><br>\n      Regards,\n    </div>\n    <div class=\"email-footer\">\n      <strong>Amjid Ali</strong><br>\n      Automation Demo – n8n\n    </div>\n  </div>\n</body>\n</html>\n", "toRecipients": "<EMAIL>", "additionalFields": {"bodyContentType": "html"}}, "typeVersion": 2}, {"id": "e4ffe557-0862-401e-9f65-7195a72db1d9", "name": "Microsoft Outlook1", "type": "n8n-nodes-base.microsoftOutlook", "position": [560, 160], "webhookId": "ea8b2720-cbb6-4712-b9ff-4b443958d0d0", "parameters": {"subject": "Projects Cost Missing", "bodyContent": "==<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <title>Missing Budgeted Cost Notification</title>\n  <style>\n    body { font-family: Arial, sans-serif; background-color: #f4f4f4; margin: 0; padding: 0; }\n    .email-container { max-width: 600px; margin: 20px auto; background-color: #ffffff; border-radius: 8px; overflow: hidden; }\n    .email-header { background-color: #007BFF; color: #ffffff; padding: 20px; text-align: center; font-size: 18px; font-weight: bold; }\n    .email-body { padding: 20px; font-size: 16px; color: #333333; }\n    .email-body strong { color: #007BFF; }\n    .email-footer { padding: 10px 20px; font-size: 14px; color: #555555; text-align: left; }\n  </style>\n</head>\n<body>\n  <div class=\"email-container\">\n    <div class=\"email-header\">\n      {{ $json.default_cost_center }} - Project Data Missing\n    </div>\n    <div class=\"email-body\">\n      Dear {{ $json.default_cost_center }} Team,<br><br>\n      There are <strong>{{ $json.project_count }}</strong> active projects with missing <strong>Budgeted Cost</strong>.<br>\n      Kindly coordinate with the <strong>Accounts Team</strong> to update the missing values for accurate tracking.<br><br>\n      Your timely attention is appreciated.<br><br>\n      Regards,\n    </div>\n    <div class=\"email-footer\">\n      <strong>Amjid Ali</strong><br>\n      Automation Demo – n8n\n    </div>\n  </div>\n</body>\n</html>\n", "toRecipients": "<EMAIL>", "additionalFields": {"bodyContentType": "html"}}, "typeVersion": 2}, {"id": "e0722ebd-1e05-4efe-a27a-e4db193dec80", "name": "Microsoft Outlook7", "type": "n8n-nodes-base.microsoftOutlook", "position": [560, 380], "webhookId": "46e6a678-d922-4dfc-b51d-864477e6b01e", "parameters": {"subject": "Projects Cost Missing", "bodyContent": "==<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <title>Missing Budgeted Cost Notification</title>\n  <style>\n    body { font-family: Arial, sans-serif; background-color: #f4f4f4; margin: 0; padding: 0; }\n    .email-container { max-width: 600px; margin: 20px auto; background-color: #ffffff; border-radius: 8px; overflow: hidden; }\n    .email-header { background-color: #007BFF; color: #ffffff; padding: 20px; text-align: center; font-size: 18px; font-weight: bold; }\n    .email-body { padding: 20px; font-size: 16px; color: #333333; }\n    .email-body strong { color: #007BFF; }\n    .email-footer { padding: 10px 20px; font-size: 14px; color: #555555; text-align: left; }\n  </style>\n</head>\n<body>\n  <div class=\"email-container\">\n    <div class=\"email-header\">\n      {{ $json.default_cost_center }} - Project Data Missing\n    </div>\n    <div class=\"email-body\">\n      Dear {{ $json.default_cost_center }} Team,<br><br>\n      There are <strong>{{ $json.project_count }}</strong> active projects with missing <strong>Budgeted Cost</strong>.<br>\n      Kindly coordinate with the <strong>Accounts Team</strong> to update the missing values for accurate tracking.<br><br>\n      Your timely attention is appreciated.<br><br>\n      Regards,\n    </div>\n    <div class=\"email-footer\">\n      <strong>Amjid Ali</strong><br>\n      Automation Demo – n8n\n    </div>\n  </div>\n</body>\n</html>\n", "toRecipients": "<EMAIL>", "additionalFields": {"bodyContentType": "html"}}, "typeVersion": 2}], "pinData": {}, "connections": {"MySQL": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Microsoft Outlook6", "type": "main", "index": 0}], [{"node": "Microsoft Outlook1", "type": "main", "index": 0}], [{"node": "Microsoft Outlook7", "type": "main", "index": 0}], []]}, "Schedule Trigger": {"main": [[{"node": "MySQL", "type": "main", "index": 0}]]}}}