{"meta": {"instanceId": "8418cffce8d48086ec0a73fd90aca708aa07591f2fefa6034d87fe12a09de26e", "templateCredsSetupCompleted": true}, "nodes": [{"id": "0f70dc82-f4af-444a-a3eb-381623091cb1", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-980, -200], "parameters": {}, "typeVersion": 1}, {"id": "cff3d74c-b381-42f9-96c0-b607a410ffeb", "name": "Wait", "type": "n8n-nodes-base.wait", "position": [-180, -200], "webhookId": "1c3e61f9-9bd3-489b-a0a1-e20c1f52d496", "parameters": {"amount": 10}, "typeVersion": 1.1}, {"id": "0ec6969b-17e2-41c3-a2c1-2c362cda54ce", "name": "Output", "type": "n8n-nodes-base.set", "position": [440, -180], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "53226f92-5381-4f9f-9be5-4b25f31db99c", "name": "data.video_url", "type": "string", "value": "={{ $json.data.video_url }}"}]}}, "typeVersion": 3.4}, {"id": "887660ad-0ca3-4364-a2d2-443349de19de", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-260, -300], "parameters": {"color": 7, "width": 660, "height": 340, "content": "## Check video status"}, "typeVersion": 1}, {"id": "7c9ee0c5-9a0a-44be-8d8a-4af99c2f3022", "name": "is Completed", "type": "n8n-nodes-base.if", "position": [220, -200], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "2643b070-cbb2-4562-9269-a61389e0c242", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "completed"}]}}, "typeVersion": 2.2}, {"id": "893813b4-1a55-4e21-a7a4-da47bf60ada2", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-1920, -320], "parameters": {"width": 820, "height": 860, "content": "# Generate AI Videos with <PERSON><PERSON><PERSON> in n8n\n\nThis workflow allows you to generate AI-powered videos using [HeyGen](https://www.heygen.com), a platform that provides customizable AI avatars and voices. By integrating HeyGen with n8n, you can create videos by providing a text input, which is then spoken by an AI-generated avatar.\n\n# [👉🏻 Try HeyGen for free 👈🏻](https://www.heygen.com)\n\n## Setup\n\n### Step 1: Create a HeyGen Account & API Key\n1. Sign up for a [HeyGen account](https://www.heygen.com).\n2. Navigate to your account settings and locate your **API Key**.\n3. Copy your API key for use in n8n.\n\n\n⚠️ To use Heygen API you need to purchase API credits\n\n### Step 2: Create n8n Credentials\n1. In n8n, create new credentials and select **\"Custom Auth\"** as the authentication type.\n2. In the Name provide : `X-Api-Key`\n3. And in the value paste your API key from Heygen\n4. Update the 2 http node with the right credentials.\n\n### Step 3: Choose an Avatar and a Voice\nHeyGen provides multiple AI avatars and voice options. You need to choose:\n- An **Avatar ID** (representing the AI-generated presenter)\n- A **Voice ID** (which will read your text)\n\nTo find available avatars and voices:\n1. Visit the HeyGen [API Documentation](https://www.heygen.com/api) or check the list in your HeyGen account.\n2. Copy the **Avatar ID** and **Voice ID** that you want to use.\n"}, "typeVersion": 1}, {"id": "36e45b12-1edd-45ec-b3d2-ac3b6f78f7b1", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-720, 60], "parameters": {"width": 440, "height": 180, "content": "# ☝️ Provide Video Details\n\n   - **Avatar ID** \n   - **Voice ID** \n   - **Text**"}, "typeVersion": 1}, {"id": "c0ebe61f-ca8f-4928-8e89-93ef50aa17ee", "name": "Create Video", "type": "n8n-nodes-base.httpRequest", "position": [-500, -140], "parameters": {"url": "https://api.heygen.com/v2/video/generate", "method": "POST", "options": {}, "jsonBody": "={\n  \"video_inputs\": [\n    {\n      \"character\": {\n        \"type\": \"avatar\",\n        \"avatar_id\": \"{{ $json.avatar_id }}\",\n        \"avatar_style\": \"normal\"\n      },\n      \"voice\": {\n        \"type\": \"text\",\n        \"input_text\": \"{{ $json.text }}\",\n        \"voice_id\": \"{{ $json.voice_id }}\",\n        \"speed\": 1\n      }\n    }\n  ],\n  \"caption\": true,\n  \"dimension\": {\n    \"width\": 1080,\n    \"height\": 1920\n  }\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "LQl4w1qH5sdfcP9o", "name": "HeyGen - Thais"}}, "typeVersion": 4.2}, {"id": "2fd1e0cf-0dc0-4ef5-b5a0-52c87631efd7", "name": "Config", "type": "n8n-nodes-base.set", "position": [-740, -120], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "dc091aca-844f-404f-ad0c-8ad4b48a505b", "name": "avatar_id", "type": "string", "value": "7895d2d9f4f9453899e1d80e5accb6be"}, {"id": "eb2ed34c-53d2-41e8-ab2f-1b8278bde235", "name": "voice_id", "type": "string", "value": "PBgwoAVFZIC0UB6sU914"}, {"id": "2c939d6c-73f8-482d-b11f-71bdd7baf04e", "name": "text", "type": "string", "value": "Imagine <PERSON><PERSON> as that super energetic friend who jumps from one cool idea to the next. Now, add <PERSON>—the smart helper trying to keep things on track. Sometimes, they work together perfectly, and other times, things get a little goofy. One minute you're starting a project, and the next, you're off chasing a shiny new idea! But that's the fun of it. With a bit of AI magic, even the craziest thoughts find their place. Embrace the chaos, laugh at the mix-ups, and let your creativity shine!"}]}}, "typeVersion": 3.4}, {"id": "c63f1b7a-0ec0-4329-aeee-229e8433add7", "name": "Get Video Status", "type": "n8n-nodes-base.httpRequest", "position": [20, -200], "parameters": {"url": "https://api.heygen.com/v1/video_status.get", "options": {}, "sendQuery": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "queryParameters": {"parameters": [{"name": "video_id", "value": "={{ $('Create Video').first().json.data.video_id }}"}]}}, "credentials": {"httpCustomAuth": {"id": "nhkU37chaiBU6X3j", "name": "Eleven Labs"}, "httpHeaderAuth": {"id": "LQl4w1qH5sdfcP9o", "name": "HeyGen - Thais"}}, "typeVersion": 4.2}], "pinData": {}, "connections": {"Wait": {"main": [[{"node": "Get Video Status", "type": "main", "index": 0}]]}, "Config": {"main": [[{"node": "Create Video", "type": "main", "index": 0}]]}, "Create Video": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "is Completed": {"main": [[{"node": "Output", "type": "main", "index": 0}], [{"node": "Wait", "type": "main", "index": 0}]]}, "Get Video Status": {"main": [[{"node": "is Completed", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Config", "type": "main", "index": 0}]]}}}