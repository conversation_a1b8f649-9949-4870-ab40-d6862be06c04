{"meta": {"instanceId": "8c8c5237b8e37b006a7adce87f4369350c58e41f3ca9de16196d3197f69eabcd"}, "nodes": [{"id": "302c87d4-2c92-40a0-9a77-cef4ddd7db6d", "name": "XML", "type": "n8n-nodes-base.xml", "position": [840, 440], "parameters": {"mode": "jsonToxml", "options": {}}, "typeVersion": 1}, {"id": "88ba5ee7-4788-452f-9d64-bf192fe90e5f", "name": "Set", "type": "n8n-nodes-base.set", "position": [660, 440], "parameters": {"values": {"number": [{"name": "number", "value": 1}], "string": [{"name": "string", "value": "my text"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"id": "6cda9dc3-0fdd-4f3a-aecf-0ff0efd28c33", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [1020, 440], "parameters": {"options": {"responseHeaders": {"entries": [{"name": "content-type", "value": "application/xml"}]}}, "respondWith": "text", "responseBody": "={{ $json.data }}"}, "typeVersion": 1}, {"id": "94644433-fb9b-4532-81d2-d9673eb6e15e", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [480, 440], "webhookId": "89fb6783-adc5-4cbc-bacc-dbd7b85df403", "parameters": {"path": "test", "options": {}, "responseMode": "responseNode"}, "typeVersion": 1}], "connections": {"Set": {"main": [[{"node": "XML", "type": "main", "index": 0}]]}, "XML": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}}