{"id": "ZVUQL1bUQ8gBCZTl", "meta": {"instanceId": "23e6ce638471979c8a2c72a9fb50e44f4f2bfd5a9fc2f5b7f5c842b9abeb9393"}, "name": "Chat with Google Sheet", "tags": [], "nodes": [{"id": "89af21df-1125-4df6-9d43-a643e02bb53f", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [540, 1240], "parameters": {}, "typeVersion": 1}, {"id": "f571d0cc-eb43-46c9-bdd5-45abc51dfbe7", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [461.9740563285368, 970.616715060075], "parameters": {"color": 7, "width": 1449.2963504228514, "height": 612.0936015224503, "content": "### Sub-workflow: Custom tool\nThis can be called by the agent above. It returns three different types of data from the Google Sheet, which can be used together for more complex queries without returning the whole sheet (which might be too big for GPT to handle)"}, "typeVersion": 1}, {"id": "8761e314-c1f2-4edd-88ea-bfeb02dc8f1a", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [460, 460], "parameters": {"color": 7, "width": 927.5, "height": 486.5625, "content": "### Main workflow: AI agent using custom tool"}, "typeVersion": 1}, {"id": "e793b816-68d9-42ef-b9b0-6fe22aa375e8", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [300, 540], "parameters": {"width": 185.9375, "height": 183.85014518022527, "content": "## Try me out\n\nClick the 'Chat' button at the bottom and enter:\n\n_Which is our biggest customer?_"}, "typeVersion": 1}, {"id": "f895d926-0f70-415b-9492-c3ecf186e761", "name": "Get Google sheet contents", "type": "n8n-nodes-base.googleSheets", "position": [980, 1240], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "url", "value": "={{ $json.sheetUrl }}"}, "documentId": {"__rl": true, "mode": "url", "value": "={{ $json.sheetUrl }}"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "cTLaIZBSFJlHuZNs", "name": "Google Sheets account"}}, "typeVersion": 4.2}, {"id": "daca1624-6c35-473a-bf3a-5fa0686a0a62", "name": "Set Google Sheet URL", "type": "n8n-nodes-base.set", "position": [760, 1240], "parameters": {"fields": {"values": [{"name": "sheetUrl", "stringValue": "https://docs.google.com/spreadsheets/d/1GjFBV8HpraNWG_JyuaQAgTb3zUGguh0S_25nO0CMd8A/edit#gid=*********"}]}, "options": {}}, "typeVersion": 3.2}, {"id": "68edca41-0196-47d8-9378-31fed0a70918", "name": "Get column names", "type": "n8n-nodes-base.set", "position": [1460, 1060], "parameters": {"fields": {"values": [{"name": "response", "stringValue": "={{ Object.keys($json) }}"}]}, "include": "none", "options": {}}, "executeOnce": true, "typeVersion": 3.2}, {"id": "7a9dea08-f9e9-4139-842a-9066a9cf04ea", "name": "Prepare output", "type": "n8n-nodes-base.code", "position": [1720, 1240], "parameters": {"jsCode": "return {\n 'response': JSON.stringify($input.all().map(x => x.json))\n}"}, "typeVersion": 2}, {"id": "616eebc5-5c5c-4fa1-b13f-61a477742c72", "name": "List columns tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [940, 780], "parameters": {"name": "list_columns", "fields": {"values": [{"name": "operation", "stringValue": "column_names"}]}, "workflowId": "={{ $workflow.id }}", "description": "=List all column names in customer data\n\nCall this tool to find out what data is available for each customer. It should be called first at the beginning to understand which columns are available for querying."}, "typeVersion": 1}, {"id": "891ad3a8-72f0-45ad-8777-1647a7342c00", "name": "Get customer tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [1220, 780], "parameters": {"name": "get_customer", "fields": {"values": [{"name": "operation", "stringValue": "row"}]}, "workflowId": "={{ $workflow.id }}", "description": "=Get all columns for a given customer\n\nThe input should be a stringified row number of the customer to fetch; only single string inputs are allowed. Returns a JSON object with all the column names and their values."}, "typeVersion": 1}, {"id": "0f3ca6ff-fc01-4f33-b1a7-cb82a0ec5c88", "name": "Get column values tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [1080, 780], "parameters": {"name": "column_values", "fields": {"values": [{"name": "operation", "stringValue": "column_values"}]}, "workflowId": "={{ $workflow.id }}", "description": "=Get the specified column value for all customers\n\nUse this tool to find out which customers have a certain value for a given column. Returns an array of JSON objects, one per customer. Each JSON object includes the column being requested plus the row_number column. Input should be a single string representing the name of the column to fetch.\n"}, "typeVersion": 1}, {"id": "deef6eb4-2a11-4490-ad56-bc1ea9077843", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [900, 740.8693557231958], "parameters": {"color": 7, "width": 432.3271051132649, "height": 179.21380662202682, "content": "These tools all call the sub-workflow below"}, "typeVersion": 1}, {"id": "94e4dbe5-dc41-4879-bffc-ec8f5341f3b5", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [723, 1172], "parameters": {"width": 179.99762227826224, "height": 226.64416053838073, "content": "Change the URL of the Google Sheet here"}, "typeVersion": 1}, {"id": "dbb887f0-93a7-466e-9c9f-8aa4e7da935d", "name": "Prepare column data", "type": "n8n-nodes-base.set", "position": [1460, 1240], "parameters": {"fields": {"values": [{"name": "={{ $('Execute Workflow Trigger').item.json.query }}", "stringValue": "={{ $json[$('Execute Workflow Trigger').item.json.query] }}"}, {"name": "row_number", "stringValue": "={{ $json.row_number }}"}]}, "include": "none", "options": {}}, "typeVersion": 3.2}, {"id": "041d32ca-e59a-4b67-a3e6-4e2f19e3de72", "name": "Filter", "type": "n8n-nodes-base.filter", "position": [1460, 1400], "parameters": {"options": {"looseTypeValidation": true}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "loose"}, "combinator": "and", "conditions": [{"id": "bf712098-97e4-42cb-8e08-2ee32d19d3e7", "operator": {"type": "number", "operation": "equals"}, "leftValue": "={{ $json.row_number }}", "rightValue": "={{ $('Execute Workflow Trigger').item.json.query }}"}]}}, "typeVersion": 2, "alwaysOutputData": true}, {"id": "69b9e70a-9104-4731-9f16-8324a3f7e423", "name": "Check operation", "type": "n8n-nodes-base.switch", "position": [1200, 1240], "parameters": {"rules": {"values": [{"outputKey": "col names", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $('Execute Workflow Trigger').item.json.operation }}", "rightValue": "column_names"}]}, "renameOutput": true}, {"outputKey": "col values", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "b7968ce7-0d20-43d0-bcca-7b66e0aec715", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Execute Workflow Trigger').item.json.operation }}", "rightValue": "column_values"}]}, "renameOutput": true}, {"outputKey": "rows", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "de3bb9b5-edc6-4448-839e-eda07b72144a", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Execute Workflow Trigger').item.json.operation }}", "rightValue": "row"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3}, {"id": "d955e499-5a3e-45a3-9fc8-266e2f687ecc", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [800, 780], "parameters": {"model": "gpt-3.5-turbo-0125", "options": {"temperature": 0}}, "credentials": {"openAiApi": {"id": "58qWzMjeNE8GjMmI", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "28fbda0b-1e01-4f59-af5b-fe02eba899b1", "name": "<PERSON><PERSON>", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [620, 560], "webhookId": "2b9d9c42-adf4-425d-b0a5-e4f60c750e63", "parameters": {}, "typeVersion": 1}, {"id": "c89614f4-d8b1-4f7b-9e7c-856e3f89eadb", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [900, 560], "parameters": {"agent": "reActAgent", "options": {"suffix": "Begin! Use `list_columns` tool first to determine which columns are available.\n\n\tQuestion: {input}\n\tThought:{agent_scratchpad}", "returnIntermediateSteps": false}}, "typeVersion": 1.3}], "active": false, "pinData": {"Execute Workflow Trigger": [{"json": {"query": "222", "operation": "row"}}]}, "settings": {"executionOrder": "v1"}, "versionId": "********-92bb-498c-9628-35d9044593e7", "connections": {"Filter": {"main": [[{"node": "Prepare output", "type": "main", "index": 0}]]}, "Chat Trigger": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Check operation": {"main": [[{"node": "Get column names", "type": "main", "index": 0}], [{"node": "Prepare column data", "type": "main", "index": 0}], [{"node": "Filter", "type": "main", "index": 0}]]}, "Get column names": {"main": [[{"node": "Prepare output", "type": "main", "index": 0}]]}, "Get customer tool": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "List columns tool": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Prepare column data": {"main": [[{"node": "Prepare output", "type": "main", "index": 0}]]}, "Set Google Sheet URL": {"main": [[{"node": "Get Google sheet contents", "type": "main", "index": 0}]]}, "Get column values tool": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "Set Google Sheet URL", "type": "main", "index": 0}]]}, "Get Google sheet contents": {"main": [[{"node": "Check operation", "type": "main", "index": 0}]]}}}