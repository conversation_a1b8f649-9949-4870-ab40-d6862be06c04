{"id": "5", "name": "Syncro Status Update Clockify", "nodes": [{"name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [560, 310], "webhookId": "3300d16f-5d43-4ae7-887e-376eecaeec17", "parameters": {"path": "4500d16f-5d43-4ae7-887e-376eecaeec17", "options": {}, "httpMethod": "POST", "responseData": "allEntries", "responseMode": "lastNode"}, "typeVersion": 1}, {"name": "Clockify", "type": "n8n-nodes-base.clockify", "position": [960, 310], "parameters": {"operation": "getAll", "returnAll": true, "workspaceId": "xxx", "additionalFields": {"name": "=Ticket {{$node[\"Webhook\"].json[\"body\"][\"attributes\"][\"number\"]}} - {{$node[\"Webhook\"].json[\"body\"][\"attributes\"][\"customer_business_then_name\"]}} [{{$node[\"Webhook\"].json[\"body\"][\"attributes\"][\"id\"]}}]", "archived": true}}, "credentials": {"clockifyApi": "Clockify"}, "typeVersion": 1}, {"name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [1130, 310], "parameters": {"url": "=https://api.clockify.me/api/v1/workspaces/{{$node[\"Clockify\"].parameter[\"workspaceId\"]}}/projects/{{$json[\"id\"]}}", "options": {}, "requestMethod": "PUT", "authentication": "headerAuth", "bodyParametersUi": {"parameter": [{"name": "archived", "value": "false"}, {"name": "isPublic", "value": "true"}]}, "headerParametersUi": {"parameter": []}}, "credentials": {"httpHeaderAuth": "Clockify API"}, "typeVersion": 1}, {"name": "IF1", "type": "n8n-nodes-base.if", "position": [730, 310], "parameters": {"conditions": {"string": [{"value1": "={{$json[\"body\"][\"attributes\"][\"status\"]}}", "value2": "Resolved", "operation": "notEqual"}]}}, "typeVersion": 1}, {"name": "Clockify1", "type": "n8n-nodes-base.clockify", "position": [960, 540], "parameters": {"operation": "getAll", "returnAll": true, "workspaceId": "xxx", "additionalFields": {"name": "=Ticket {{$node[\"Webhook\"].json[\"body\"][\"attributes\"][\"number\"]}} - {{$node[\"Webhook\"].json[\"body\"][\"attributes\"][\"customer_business_then_name\"]}} [{{$node[\"Webhook\"].json[\"body\"][\"attributes\"][\"id\"]}}]", "archived": false}}, "credentials": {"clockifyApi": "Clockify"}, "typeVersion": 1}, {"name": "HTTP Request1", "type": "n8n-nodes-base.httpRequest", "position": [1130, 540], "parameters": {"url": "=https://api.clockify.me/api/v1/workspaces/{{$node[\"Clockify1\"].parameter[\"workspaceId\"]}}/projects/{{$node[\"Clockify1\"].json[\"id\"]}}", "options": {}, "requestMethod": "PUT", "authentication": "headerAuth", "bodyParametersUi": {"parameter": [{"name": "archived", "value": "true"}, {"name": "isPublic", "value": "true"}]}, "headerParametersUi": {"parameter": []}}, "credentials": {"httpHeaderAuth": "Clockify API"}, "typeVersion": 1}], "active": true, "settings": {}, "connections": {"IF1": {"main": [[{"node": "Clockify", "type": "main", "index": 0}], [{"node": "Clockify1", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "IF1", "type": "main", "index": 0}]]}, "Clockify": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "Clockify1": {"main": [[{"node": "HTTP Request1", "type": "main", "index": 0}]]}}}