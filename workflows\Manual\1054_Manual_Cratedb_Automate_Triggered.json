{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [550, 400], "parameters": {}, "typeVersion": 1}, {"name": "CrateDB", "type": "n8n-nodes-base.crateDb", "position": [750, 400], "parameters": {"query": "CREATE TABLE test (id INT, name STRING);", "operation": "execute<PERSON>uery"}, "credentials": {"crateDb": "cratedb_creds"}, "typeVersion": 1, "alwaysOutputData": true}, {"name": "CrateDB1", "type": "n8n-nodes-base.crateDb", "position": [1150, 400], "parameters": {"table": "test", "columns": "id, name"}, "credentials": {"crateDb": "cratedb_creds"}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [950, 400], "parameters": {"values": {"number": [{"name": "id", "value": 0}], "string": [{"name": "name", "value": "n8n"}]}, "options": {}}, "typeVersion": 1, "alwaysOutputData": false}], "connections": {"Set": {"main": [[{"node": "CrateDB1", "type": "main", "index": 0}]]}, "CrateDB": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "CrateDB", "type": "main", "index": 0}]]}}}