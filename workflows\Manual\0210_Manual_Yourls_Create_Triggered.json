{"id": "167", "name": "Create a short URL and get the statistics of the URL", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [370, 300], "parameters": {}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.yourls", "position": [570, 300], "parameters": {"url": "https://medium.com/n8n-io/sending-sms-the-low-code-way-with-airtable-twilio-programmable-sms-and-n8n-90dbde74223e?source=---------4-----------------------", "additionalFields": {"title": "Sending SMS the Low-Code Way with Airtable, Twilio Programmable SMS, and n8n"}}, "credentials": {"yourlsApi": "<PERSON><PERSON>"}, "typeVersion": 1}, {"name": "Yourls1", "type": "n8n-nodes-base.yourls", "position": [770, 300], "parameters": {"shortUrl": "={{$node[\"Yourls\"].json[\"shorturl\"]}}", "operation": "stats"}, "credentials": {"yourlsApi": "<PERSON><PERSON>"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Yourls": {"main": [[{"node": "Yourls1", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}}}