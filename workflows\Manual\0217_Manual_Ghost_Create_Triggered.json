{"id": "170", "name": "Create, update, and get a post in Ghost", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [310, 300], "parameters": {}, "typeVersion": 1}, {"name": "Ghost", "type": "n8n-nodes-base.ghost", "position": [510, 300], "parameters": {"title": "Running ghost with n8n!", "source": "adminApi", "content": "<p>In this article, you will learn how to automate your Ghost site with n8n!</p>", "operation": "create", "additionalFields": {}}, "credentials": {"ghostAdminApi": "Ghost Admin API"}, "typeVersion": 1}, {"name": "Ghost1", "type": "n8n-nodes-base.ghost", "position": [710, 300], "parameters": {"postId": "={{$node[\"Ghost\"].json[\"id\"]}}", "source": "adminApi", "operation": "update", "updateFields": {"status": "published"}}, "credentials": {"ghostAdminApi": "Ghost Admin API"}, "typeVersion": 1}, {"name": "Ghost2", "type": "n8n-nodes-base.ghost", "position": [910, 300], "parameters": {"by": "id", "source": "adminApi", "options": {}, "identifier": "={{$node[\"Ghost\"].json[\"id\"]}}"}, "credentials": {"ghostAdminApi": "Ghost Admin API"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Ghost": {"main": [[{"node": "Ghost1", "type": "main", "index": 0}]]}, "Ghost1": {"main": [[{"node": "Ghost2", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Ghost", "type": "main", "index": 0}]]}}}