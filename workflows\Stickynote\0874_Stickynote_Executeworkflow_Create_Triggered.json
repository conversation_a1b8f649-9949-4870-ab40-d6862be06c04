{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9"}, "nodes": [{"id": "fcbf7023-7e12-49d8-9c7d-4cb431c79905", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [460, 260], "parameters": {"workflowInputs": {"values": [{"name": "operation"}, {"name": "tableName"}, {"name": "values", "type": "object"}, {"name": "where", "type": "object"}]}}, "typeVersion": 1.1}, {"id": "58c93321-ded9-48c1-812f-c35d160e257b", "name": "Operation", "type": "n8n-nodes-base.switch", "position": [640, 260], "parameters": {"rules": {"values": [{"outputKey": "READ", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "81b134bc-d671-4493-b3ad-8df9be3f49a6", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.operation }}", "rightValue": "read"}]}, "renameOutput": true}, {"outputKey": "INSERT", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "8d57914f-6587-4fb3-88e0-aa1de6ba56c1", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.operation }}", "rightValue": "insert"}]}, "renameOutput": true}, {"outputKey": "UPDATE", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "7c38f238-213a-46ec-aefe-22e0bcb8dffc", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.operation }}", "rightValue": "update"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "865ae43a-14ec-4aac-9396-d0aef1ab4a75", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-340, -100], "parameters": {"color": 7, "width": 680, "height": 660, "content": "## 1. Set up an MCP Server Trigger\n[Read more about the MCP Server Trigger](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-langchain.mcptrigger)"}, "typeVersion": 1}, {"id": "35551851-319a-47cf-87cd-a63b128300cc", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [360, -100], "parameters": {"color": 7, "width": 820, "height": 720, "content": "## 2. Keep Secure by Preventing Raw SQL Statements\n[Read more about the Code Node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.code/)\n\nWhilst it may be easier to just let the Agent provide the full raw SQL statement,\nit may expose you or your organisation to a real security risk where in the worst\ncase, data may be unknowingly leaked or deleted.\n\nForcing the agent to provide only the parameters of the query\nmeans we can guard somewhat against this risk and also allows\nuse of query parameters as best practice against SQL injection attacks.\n"}, "typeVersion": 1}, {"id": "95c35568-e447-4634-afe8-c902ba5c7d2f", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-340, -220], "parameters": {"color": 5, "width": 380, "height": 100, "content": "### Always Authenticate Your Server!\nBefore going to production, it's always advised to enable authentication on your MCP server trigger."}, "typeVersion": 1}, {"id": "2d0f98f8-043a-459c-8b77-634e06ee0f57", "name": "SQLite MCP Server", "type": "@n8n/n8n-nodes-langchain.mcpTrigger", "position": [-160, 60], "webhookId": "3124a4cd-4e93-4c1b-b4db-b5599f4889b1", "parameters": {"path": "3124a4cd-4e93-4c1b-b4db-b5599f4889b1"}, "typeVersion": 1}, {"id": "6f313137-eb8f-429b-a6c9-7b17e067dc5e", "name": "CreateRecord", "type": "n8n-nodes-base.code", "position": [940, 260], "parameters": {"jsCode": "const sqlite3 = require('sqlite3').verbose();\nconst { promisify } = require('util');\n\nconst db = new sqlite3.Database('/home/<USER>/test.db');\nconst run = promisify(db.run.bind(db));\n\nconst { json } = $input.first();\n\n\nlet output = '';\nconst statement = [\n  `INSERT INTO ${json.tableName}`,\n  `   (${Object.keys(json.values).join(',')})`,\n  `VALUES`,\n  `  (${Object.keys(json.values).map(_ => '?').join(',')})`\n].join(' ');\nconst params = Object.values(json.values);\n\ntry {\n  await run(statement.trim(), params);\n  output = { output: 'ok', error: null };\n} catch (err) {\n  output = { output: null, error: err };\n} finally {\n  await db.close();\n}\n\nreturn output;"}, "typeVersion": 2}, {"id": "b2530656-bbf4-4316-8b8e-c5d27865e45f", "name": "UpdateRecord", "type": "n8n-nodes-base.code", "position": [940, 440], "parameters": {"jsCode": "const sqlite3 = require('sqlite3').verbose();\nconst { promisify } = require('util');\n\nconst db = new sqlite3.Database('/home/<USER>/test.db');\nconst run = promisify(db.run.bind(db));\n\nconst { json } = $input.first();\n\nlet output = '';\nconst statement = [\n  `UPDATE ${json.tableName}`,\n  `SET`,\n  `${Object.keys(json.values)\n    .map(key => `${key} = ?`)\n    .join(',')}`,\n  `WHERE`,\n  `${Object.keys(json.where)\n     .map((key,idx) => `${key} = ?`)\n     .join(' AND ')}`\n].join(' ');\nconst params = [ ...Object.values(json.values), ...Object.values(json.where)];\n\ntry {\n  await run(statement, params);\n  output = { output: 'ok', error: null };\n} catch (err) {\n  output = { output: null, error: err };\n} finally {\n  await db.close();\n}\n\nreturn output;"}, "typeVersion": 2}, {"id": "8c1b8bcb-20f1-4ef9-b646-9d89177651dd", "name": "ReadRecords", "type": "n8n-nodes-base.code", "position": [940, 80], "parameters": {"jsCode": "const sqlite3 = require('sqlite3').verbose();\nconst { promisify } = require('util');\n\nconst db = new sqlite3.Database('/home/<USER>/test.db');\nconst all = promisify(db.all.bind(db));\n\nconst { json } = $input.first();\n\nlet output = '';\nconst statement = [\n  `SELECT * FROM ${json.tableName}`,\n  json?.where && Object.keys(json?.where).length > 0\n    ? `WHERE ` + Object.keys(json.where)\n        .map((key,idx) => `${key} = $${idx+1}`)\n        .join(' AND ')\n    : ''\n].join(' ');\nconst params = json.where ? Object.values(json.where) : undefined;\n\ntry {\n  \n  const results = await all(statement.trim(), params);\n\n  output = { output: [].concat(results), error: null };\n} catch (err) {\n  output = { output: null, error: err };\n} finally {\n  await db.close();\n}\n\nreturn output"}, "typeVersion": 2}, {"id": "87df3eed-b4d5-4a9c-bd82-0ad455449cd2", "name": "DescribeTables", "type": "@n8n/n8n-nodes-langchain.toolCode", "position": [-160, 340], "parameters": {"name": "describeTable", "jsCode": "const sqlite3 = require('sqlite3').verbose();\nconst { promisify } = require('util');\n\nconst db = new sqlite3.Database('/home/<USER>/test.db');\nconst all = promisify(db.all.bind(db));\n\nlet output = '';\ntry {\n  const rows = await all(`PRAGMA table_info(${query.tableName})`);\n  const results = rows.map((col) => (\n    `${col.name} | ${col.type} | NOT NULL: ${col.notnull} | Default: ${col.dflt_value}`\n  )).join('\\n');\n  \n  output = { output: [].concat(results), error: null };\n} catch (err) {\n  output = { output: null, error: err };\n} finally {\n  await db.close();\n}\n\nreturn JSON.stringify(output);", "schemaType": "manual", "description": "Call this tool to describe a table's schema.", "inputSchema": "{\n  \"type\": \"object\",\n  \"required\": [\"tableName\"],\n  \"properties\": {\n    \"tableName\": {\n      \"type\": \"string\",\n      \"description\": \"Name of the table\"\n    }\n  }\n}", "specifyInputSchema": true}, "typeVersion": 1.1}, {"id": "4a0ba0d0-4955-44fd-92de-ad031ebb64cb", "name": "ListTables", "type": "@n8n/n8n-nodes-langchain.toolCode", "position": [-260, 240], "parameters": {"name": "listTables", "jsCode": "const sqlite3 = require('sqlite3').verbose();\nconst { promisify } = require('util');\n\nconst db = new sqlite3.Database('/home/<USER>/test.db');\nconst all = promisify(db.all.bind(db));\n\nlet output = '';\ntry {\n  const rows = await all(`SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'`, []);\n  const results = rows.map((row) => row.name).join('\\n');\n  \n  output = { output: [].concat(results), error: null };\n} catch (err) {\n  output = { output: null, error: err };\n} finally {\n  await db.close();\n}\n\nreturn JSON.stringify(output);", "description": "Call this tool to list all available tables in the SQLite Database."}, "typeVersion": 1.1}, {"id": "69e8e720-7e91-4b46-8db5-1afdf1f3dbe0", "name": "CreateRecords", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [-40, 440], "parameters": {"name": "CreateRecords", "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "description": "Call this tool to create a row in a SQLite table.", "workflowInputs": {"value": {"where": "={{ {} }}", "values": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('values', `An object of key-value pair where key represents the column name.`, 'string') }}", "operation": "insert", "tableName": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('tableName', `table to insert into`, 'string') }}"}, "schema": [{"id": "operation", "type": "string", "display": true, "removed": false, "required": false, "displayName": "operation", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "tableName", "type": "string", "display": true, "removed": false, "required": false, "displayName": "tableName", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "values", "type": "object", "display": true, "removed": false, "required": false, "displayName": "values", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "where", "type": "object", "display": true, "removed": false, "required": false, "displayName": "where", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2.1}, {"id": "f2e18ae5-89a0-4d61-805b-e777f11300a2", "name": "UpdateRows", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [100, 360], "parameters": {"name": "updateRows", "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "description": "Call this tool to create a row in a table.", "workflowInputs": {"value": {"where": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('where', `An object of key-value pair where key represents the column name.`, 'string') }}", "values": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('values', `An object of key-value pair where key represents the column name.`, 'string') }}", "operation": "update", "tableName": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('tableName', `table to update`, 'string') }}"}, "schema": [{"id": "operation", "type": "string", "display": true, "required": false, "displayName": "operation", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "tableName", "type": "string", "display": true, "required": false, "displayName": "tableName", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "values", "type": "object", "display": true, "required": false, "displayName": "values", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "where", "type": "object", "display": true, "required": false, "displayName": "where", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2.1}, {"id": "22645721-1b66-4a36-9be5-f1e5edde30f8", "name": "ReadRows", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [180, 240], "parameters": {"name": "readRows", "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "description": "Call this tool to read one or more rows in a table", "workflowInputs": {"value": {"where": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('where', `An object of key-value pair where key represents the column name.`, 'string') }}", "values": "={}", "operation": "read", "tableName": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('tableName', `table to read from`, 'string') }}"}, "schema": [{"id": "operation", "type": "string", "display": true, "required": false, "displayName": "operation", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "tableName", "type": "string", "display": true, "required": false, "displayName": "tableName", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "values", "type": "object", "display": true, "required": false, "displayName": "values", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "where", "type": "object", "display": true, "required": false, "displayName": "where", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2.1}, {"id": "2176742a-5a28-41c6-9cd7-ac3229ddcdb6", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-820, -800], "parameters": {"width": 440, "height": 1360, "content": "## Try It Out!\n**NOTE: This template is for Self-Hosted N8N Instances only.**\n\n### This n8n demonstrates how to build a simple SQLite MCP server to perform local database operations as well as use it for Business Intelligence.\n\nThis MCP example is based off an official MCP reference implementation which can be found here -https://github.com/modelcontextprotocol/servers/tree/main/src/sqlite\n\n### How it works\n* A MCP server trigger is used and connected to 5 tools: 2 Code Node and 3 Custom Workflow.\n* The 2 Code Node tools use the SQLLite3 library and are simple read-only queries and as such, the Code Node tool can be simply used.\n* The 3 custom workflow tools are used for select, insert and update queries as these are operations which require a bit more discretion.\n* Whilst it may be easier to allow the agent to use raw SQL queries, we may find it a little safer to just allow for the parameters instead. The custom workflow tool allows us to define this restricted schema for tool input which we'll use to construct the SQL statement ourselves.\n* All 3 custom workflow tools trigger the same \"Execute workflow\" trigger in this very template which has a switch to route the operation to the correct handler.\n* Finally, we use our Code nodes to handle select, insert and update operations. The responses are then sent back to the the MCP client.\n\n### How to use\n* This SQLite MCP server allows any compatible MCP client to manage a SQLite database by supporting select, create and update operations. You will need to have a SQLite database available before you can use this server.\n* Connect your MCP client by following the n8n guidelines here - https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-langchain.mcptrigger/#integrating-with-claude-desktop\n* Try the following queries in your MCP client:\n  * \"Please create a table to store business insights and add the following...\"\n  * \"what business insights do we have on current retail trends?\"\n  * \"Who has contributed the most business insights in the past week?\"\n\n### Requirements\n* SQLite for database.\n* MCP Client or Agent for usage such as Claude Desktop - https://claude.ai/download\n\n### Customising this workflow\n* If the scope of schemas or tables is too open, try restrict it so the MCP serves a specific purpose for business operations. eg. Confine the querying and editing to HR only tables before providing access to people in that department.\n* Remember to set the MCP server to require credentials before going to production and sharing this MCP server with others!"}, "typeVersion": 1}, {"id": "5a9a4763-2952-4d95-8f35-25238affa049", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-340, -340], "parameters": {"color": 3, "width": 380, "height": 100, "content": "### SELF-HOSTED ONLY\nThis template only works for self-hosted n8n instances as it reads the database file on disk."}, "typeVersion": 1}], "pinData": {}, "connections": {"ReadRows": {"ai_tool": [[{"node": "SQLite MCP Server", "type": "ai_tool", "index": 0}]]}, "Operation": {"main": [[{"node": "ReadRecords", "type": "main", "index": 0}], [{"node": "CreateRecord", "type": "main", "index": 0}], [{"node": "UpdateRecord", "type": "main", "index": 0}]]}, "ListTables": {"ai_tool": [[{"node": "SQLite MCP Server", "type": "ai_tool", "index": 0}]]}, "UpdateRows": {"ai_tool": [[{"node": "SQLite MCP Server", "type": "ai_tool", "index": 0}]]}, "CreateRecords": {"ai_tool": [[{"node": "SQLite MCP Server", "type": "ai_tool", "index": 0}]]}, "DescribeTables": {"ai_tool": [[{"node": "SQLite MCP Server", "type": "ai_tool", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Operation", "type": "main", "index": 0}]]}}}