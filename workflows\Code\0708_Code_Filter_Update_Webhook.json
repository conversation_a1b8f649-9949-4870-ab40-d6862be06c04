{"nodes": [{"id": "835afb8f-5bb3-42da-9694-d04646a80cef", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [0, 0], "parameters": {}, "typeVersion": 1}, {"id": "1e85bf4f-52d5-4ec0-8d0b-a1deeb30c9c6", "name": "Call Rapid API", "type": "n8n-nodes-base.httpRequest", "position": [880, 0], "parameters": {"url": "https://fresh-linkedin-profile-data.p.rapidapi.com/get-linkedin-profile", "options": {}, "sendQuery": true, "sendHeaders": true, "queryParameters": {"parameters": [{"name": "linkedin_url", "value": "={{ $json[\"Linkedin Profile\"] }}"}]}, "headerParameters": {"parameters": [{"name": "x-rapidapi-key"}, {"name": "x-rapidapi-host"}]}}, "typeVersion": 4.2}, {"id": "9fa011f4-d1fe-46d2-abda-28ae33929874", "name": "Filter already enriched", "type": "n8n-nodes-base.filter", "position": [440, 0], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "5907d2f7-b15d-41cc-8fee-45631bb874e1", "operator": {"type": "string", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json.about }}", "rightValue": ""}, {"id": "2857554e-a635-43d3-bf9e-a617b85009ca", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.linkedin_url }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "3f0b5717-38b4-4371-b3fa-9f19acf3e624", "name": "Encode URI", "type": "n8n-nodes-base.set", "position": [660, 0], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "fd914708-c85f-4c0e-a277-d8164c616699", "name": "Linkedin Profile", "type": "string", "value": "={{ encodeURI($json.linkedin_url) }}"}]}}, "typeVersion": 3.4}, {"id": "632e2555-5474-4d00-85f0-e95ee984c0dd", "name": "FiIter out all arrays", "type": "n8n-nodes-base.code", "position": [1100, 0], "parameters": {"mode": "runOnceForEachItem", "jsCode": "// Initialize an empty object to store filtered items\nlet filteredData = {};\n\n// Loop through each item in $input.item.json.data\nfor (const item in $input.item.json.data) {\n  // Check if the item is not an array\n  if (!Array.isArray($input.item.json.data[item])) {\n    // Add the item to the filteredData object\n    filteredData[item] = $input.item.json.data[item];\n  }\n}\nfilteredData['row_number'] = $('Pull linkedin profiles').first().json.row_number\n// Return the filteredData array\nreturn filteredData;"}, "typeVersion": 2}, {"id": "24b27c51-0f22-400c-bdc3-a09186c74639", "name": "Update the profile", "type": "n8n-nodes-base.googleSheets", "position": [1320, 0], "parameters": {"columns": {"value": {}, "schema": [{"id": "linkedin_url", "type": "string", "display": true, "removed": false, "required": false, "displayName": "linkedin_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "about", "type": "string", "display": true, "required": false, "displayName": "about", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "city", "type": "string", "display": true, "required": false, "displayName": "city", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company", "type": "string", "display": true, "required": false, "displayName": "company", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_description", "type": "string", "display": true, "required": false, "displayName": "company_description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_domain", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_domain", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_employee_range", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_employee_range", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_industry", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_industry", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_linkedin_url", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_linkedin_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_logo_url", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_logo_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_website", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_website", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company_year_founded", "type": "string", "display": true, "removed": false, "required": false, "displayName": "company_year_founded", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "connection_count", "type": "string", "display": true, "removed": false, "required": false, "displayName": "connection_count", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "country", "type": "string", "display": true, "removed": false, "required": false, "displayName": "country", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "current_company_join_month", "type": "string", "display": true, "removed": false, "required": false, "displayName": "current_company_join_month", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "current_company_join_year", "type": "string", "display": true, "removed": false, "required": false, "displayName": "current_company_join_year", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "current_job_duration", "type": "string", "display": true, "removed": false, "required": false, "displayName": "current_job_duration", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "email", "type": "string", "display": true, "removed": false, "required": false, "displayName": "email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "first_name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "first_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "follower_count", "type": "string", "display": true, "removed": false, "required": false, "displayName": "follower_count", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "full_name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "full_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "headline", "type": "string", "display": true, "removed": false, "required": false, "displayName": "headline", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "hq_city", "type": "string", "display": true, "removed": false, "required": false, "displayName": "hq_city", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "hq_country", "type": "string", "display": true, "removed": false, "required": false, "displayName": "hq_country", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "hq_region", "type": "string", "display": true, "removed": false, "required": false, "displayName": "hq_region", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_title", "type": "string", "display": true, "removed": false, "required": false, "displayName": "job_title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "languages", "type": "string", "display": true, "removed": false, "required": false, "displayName": "languages", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "last_name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "last_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "location", "type": "string", "display": true, "removed": false, "required": false, "displayName": "location", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "phone", "type": "string", "display": true, "removed": false, "required": false, "displayName": "phone", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "profile_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "profile_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "profile_image_url", "type": "string", "display": true, "removed": false, "required": false, "displayName": "profile_image_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "public_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "public_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "school", "type": "string", "display": true, "removed": false, "required": false, "displayName": "school", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "state", "type": "string", "display": true, "removed": false, "required": false, "displayName": "state", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "urn", "type": "string", "display": true, "removed": false, "required": false, "displayName": "urn", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": ["linkedin_url"]}, "options": {}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/10cSUaj-YZhrgwXLIGpJzLjv6RMN6cYiw9EK-rNw0-AM/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "10cSUaj-YZhrgwXLIGpJzLjv6RMN6cYiw9EK-rNw0-AM", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/10cSUaj-YZhrgwXLIGpJzLjv6RMN6cYiw9EK-rNw0-AM/edit?usp=drivesdk", "cachedResultName": "Linkedin contact info"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "gdLmm513ROUyH6oU", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "41e0e213-a1f4-47ff-aebd-6cd08df06eae", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [160, -200], "parameters": {"color": 4, "width": 220, "height": 380, "content": "## Create a Google sheet\nWith just one column named \"linkedin_url\" and fill it with the profiles you want to enrich"}, "typeVersion": 1}, {"id": "da28d424-10ce-499d-95c9-81979dab0f6b", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [780, -300], "parameters": {"color": 4, "width": 300, "height": 480, "content": "## Call RapidAPI Fresh Linkedin Profile Data\nYou have to create an account in [RapidAPI](https://rapidapi.com) and subscribe to Fresh LinkedIn Profile Data. With a free account you will be able to scrape 100 profile / month.\nAfter your subscription you will have to replace the header values: \"x-rapidapi-key\" and \"x-rapidapi-host\" with the values given in the RapidAPI interface\n"}, "typeVersion": 1}, {"id": "2bae0a2a-0c88-465b-854d-728280539e90", "name": "Pull linkedin profiles", "type": "n8n-nodes-base.googleSheets", "position": [220, 0], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/10cSUaj-YZhrgwXLIGpJzLjv6RMN6cYiw9EK-rNw0-AM/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "10cSUaj-YZhrgwXLIGpJzLjv6RMN6cYiw9EK-rNw0-AM", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/10cSUaj-YZhrgwXLIGpJzLjv6RMN6cYiw9EK-rNw0-AM/edit?usp=drivesdk", "cachedResultName": "Linkedin contact info"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "gdLmm513ROUyH6oU", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "d93a0d4c-1db8-4604-85e1-7d02bbbdcdb8", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-500, -760], "parameters": {"color": 7, "width": 460, "height": 1160, "content": "### LinkedIn Profile Enrichment Workflow\n\n#### Who is this for?\n\nThis workflow is ideal for recruiters, sales professionals, and marketing teams who need to enrich LinkedIn profiles with additional data for lead generation, talent sourcing, or market research.\n\n#### What problem is this workflow solving?\n\nManually gathering detailed LinkedIn profile information can be time-consuming and prone to errors. This workflow automates the process of enriching profile data from LinkedIn, saving time and ensuring accuracy.\n\n#### What this workflow does\n\n1.  **Input**: Reads LinkedIn profile URLs from a Google Sheet.\n2.  **Validation**: Filters out already enriched profiles to avoid redundant processing.\n3.  **Data Enrichment**: Uses RapidAPI's Fresh LinkedIn Profile Data API to retrieve detailed profile information.\n4.  **Output**: Updates the Google Sheet with enriched profile data, appending new information efficiently.\n\n#### Setup\n\n1.  **Google Sheet**: Create a sheet with a column named `linkedin_url` and populate it with the profile URLs to enrich.\n2.  **RapidAPI Account**: Sign up at [RapidAPI](https://rapidapi.com) and subscribe to the Fresh LinkedIn Profile Data API.\n3.  **API Integration**: Replace the `x-rapidapi-key` and `x-rapidapi-host` values with your credentials from RapidAPI.\n4.  **Run the Workflow**: Trigger the workflow and monitor the updates to your Google Sheet.\n\n#### How to customize this workflow\n\n*   **Filter Criteria**: Modify the filter step to include additional conditions for processing profiles.\n*   **API Configuration**: Adjust API parameters to retrieve specific fields or extend usage.\n*   **Output Format**: Customize how the enriched data is appended to the Google Sheet (e.g., format, column mappings).\n*   **Error Handling**: Add steps to handle API rate limits or missing data for smoother automation.\n\nThis workflow streamlines LinkedIn profile enrichment, making it faster and more effective for data-driven decision-making."}, "typeVersion": 1}], "connections": {"Encode URI": {"main": [[{"node": "Call Rapid API", "type": "main", "index": 0}]]}, "Call Rapid API": {"main": [[{"node": "FiIter out all arrays", "type": "main", "index": 0}]]}, "Update the profile": {"main": [[]]}, "FiIter out all arrays": {"main": [[{"node": "Update the profile", "type": "main", "index": 0}]]}, "Pull linkedin profiles": {"main": [[{"node": "Filter already enriched", "type": "main", "index": 0}]]}, "Filter already enriched": {"main": [[{"node": "Encode URI", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Pull linkedin profiles", "type": "main", "index": 0}]]}}}