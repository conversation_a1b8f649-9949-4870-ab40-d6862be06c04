{"meta": {"instanceId": "257476b1ef58bf3cb6a46e65fac7ee34a53a5e1a8492d5c6e4da5f87c9b82833", "templateId": "2113"}, "nodes": [{"id": "2e93b7a1-f22c-4e34-8bbe-09763d428ab6", "name": "Get Leads", "type": "n8n-nodes-base.httpRequest", "position": [1040, 522], "parameters": {"url": "=https://api.leadfeeder.com/accounts/{{ $json.id }}/leads", "options": {}, "sendQuery": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "queryParameters": {"parameters": [{"name": "start_date", "value": "={{ $now.minus(1, 'day').toFormat('yyyy-MM-dd') }}"}, {"name": "end_date", "value": "={{ $now.toFormat('yyyy-MM-dd') }}"}]}}, "credentials": {"httpHeaderAuth": {"id": "xipzlNJVo73gB17T", "name": "Leapfeeder Token"}}, "typeVersion": 4.1}, {"id": "0274033f-582a-40f6-9c08-161a81cfb2ab", "name": "Filter Leads by company criteria", "type": "n8n-nodes-base.filter", "position": [640, 762.*************], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "077363ca-c785-497c-bae9-24829bb321cd", "operator": {"type": "number", "operation": "gt"}, "leftValue": "={{ $json.metrics.employees }}", "rightValue": 100}]}}, "typeVersion": 2}, {"id": "918af2ea-38ab-4d36-abf0-************", "name": "Enrich company", "type": "n8n-nodes-base.clearbit", "position": [420, 762], "parameters": {"domain": "={{ $json.attributes.website_url }}", "additionalFields": {}}, "credentials": {"clearbitApi": {"id": "cKDImrinp9tg0ZHW", "name": "Clearbit account"}}, "typeVersion": 1}, {"id": "5ee23d2a-5eb6-4edd-8668-6053294d26cb", "name": "Setup", "type": "n8n-nodes-base.set", "position": [640, 282.*************], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "7b5f7c85-7455-439a-8427-7b45c67c7903", "name": "Leadfeeder Accounts", "type": "array", "value": "=[\"n8n\",\"someOtherAccount\"]"}, {"id": "61e2ddbd-380e-4b6e-8652-048b948994e5", "name": "Google Sheets URL", "type": "string", "value": "https://docs.google.com/spreadsheets/d/1a2gfBjZZpN0jiD7apR8fPplRp2aPHVy2_5lp4Yzp778/edit?usp=sharing"}]}}, "typeVersion": 3.3}, {"id": "425b3030-d745-4fe0-a489-c6d79f1d6dca", "name": "Get all Leedfeeder accounts", "type": "n8n-nodes-base.httpRequest", "position": [420, 522.*************], "parameters": {"url": "https://api.leadfeeder.com/accounts", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "xipzlNJVo73gB17T", "name": "Leapfeeder Token"}}, "typeVersion": 4.1}, {"id": "2a547ddc-131d-4628-83f1-516d07dddde9", "name": "Only for wanted accounts", "type": "n8n-nodes-base.filter", "position": [840, 522.*************], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "7c08f7c1-b6d4-47cc-91f8-e55a6d800eb3", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $('Setup').first().json[\"Leadfeeder Accounts\"].includes($json.attributes.name) }}", "rightValue": "n8n"}]}}, "typeVersion": 2}, {"id": "8083e9c2-ffcd-4060-87eb-f726349d1bc3", "name": "Split out accounts", "type": "n8n-nodes-base.splitOut", "position": [640, 522.*************], "parameters": {"options": {}, "fieldToSplitOut": "data"}, "typeVersion": 1}, {"id": "59696b1e-ff16-49d4-abc2-d830ce612fb5", "name": "Split Out Leads", "type": "n8n-nodes-base.splitOut", "position": [1220, 522.*************], "parameters": {"options": {}, "fieldToSplitOut": "data"}, "typeVersion": 1}, {"id": "0618bc71-d8b3-4f8e-891b-127a391994fe", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [799.*************, 208.**************], "parameters": {"color": 6, "width": 370.**************, "height": 239.**************, "content": "### Setup\n1. Add your **Leedfeeder** credentials. The name should be `Authorization` and the value `Token token=yourapitoken`. You can find your token via **Settings -> Personal -> API-Token**\n2. Add your **Google Sheet** credentials\n3. Save the **Leedfeeder** account names you want to use in the `Setup` node\n4. Copy the [Google Sheets Template](https://docs.google.com/spreadsheets/d/1a2gfBjZZpN0jiD7apR8fPplRp2aPHVy2_5lp4Yzp778/edit?usp=sharing) and add its URL to the `Setup` node"}, "typeVersion": 1}, {"id": "c23e9507-6413-4343-add3-ee1539b424fd", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [420, 282.*************], "parameters": {"rule": {"interval": [{"triggerAtHour": 7}]}}, "typeVersion": 1.1}, {"id": "68f4a72e-c857-4e7c-8176-5e2494be2553", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1380, 466.*************], "parameters": {"color": 5, "width": 193, "height": 220, "content": "Adjust your engagement criteria here"}, "typeVersion": 1}, {"id": "648ef9d3-1ea7-42ff-9c80-16f05e9ff7f6", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [600, 707.*************], "parameters": {"color": 5, "width": 193, "height": 215, "content": "Adjust your company criteria here"}, "typeVersion": 1}, {"id": "bc1e5cb1-fb6b-45a8-a23b-b3d890b7cc79", "name": "Filter leads by engagement", "type": "n8n-nodes-base.filter", "position": [1420, 522.*************], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "b97c4134-48e9-4a55-b002-5db3e4304e0d", "operator": {"type": "number", "operation": "gt"}, "leftValue": "={{ $json.attributes.visits }}", "rightValue": 3}]}}, "typeVersion": 2}, {"id": "9c59a495-7fc6-4d11-b15c-0e72c9f33164", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [380, 240], "parameters": {"color": 5, "width": 193, "height": 186.77446808510643, "content": "Adjust the schedule here"}, "typeVersion": 1}, {"id": "9ee5c6ac-544b-42fa-9432-92ae45ee40dd", "name": "Save leads to Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [840, 762.*************], "parameters": {"columns": {"value": {"name": "={{ $json.name }}", "domain": "={{ $json.domain }}", "visits": "={{ $('Split Out Leads').item.json.attributes.visits }}", "quality": "={{ $('Split Out Leads').item.json.attributes.quality }}", "twitter": "={{ $json.twitter.handle ? $json.twitter.handle : $('Split Out Leads').item.json.attributes.twitter_handle  }}", "linkedin": "={{ $json.linkedin.handle ? $json.linkedin.handle  : $('Split Out Leads').item.json.attributes.linkedin_handle  }}", "employees": "={{ $json.metrics.employees ? $json.metrics.employees :  $('Split Out Leads').item.json.attributes.employee_count }}", "description": "={{ $json.description }}"}, "schema": [{"id": "domain", "type": "string", "display": true, "removed": false, "required": false, "displayName": "domain", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "description", "type": "string", "display": true, "removed": false, "required": false, "displayName": "description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "employees", "type": "string", "display": true, "required": false, "displayName": "employees", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "twitter", "type": "string", "display": true, "required": false, "displayName": "twitter", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "linkedin", "type": "string", "display": true, "required": false, "displayName": "linkedin", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "quality", "type": "string", "display": true, "required": false, "displayName": "quality", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "visits", "type": "string", "display": true, "required": false, "displayName": "visits", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["domain"]}, "options": {}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1a2gfBjZZpN0jiD7apR8fPplRp2aPHVy2_5lp4Yzp778/edit#gid=0", "cachedResultName": "Visitors"}, "documentId": {"__rl": true, "mode": "url", "value": "={{ $('Setup').first().json[\"Google Sheets URL\"] }}"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "9", "name": "<PERSON>'s Google"}}, "typeVersion": 4.2}], "pinData": {"Get Leads": [{"data": [{"id": "400360f2-6131-11e5-90dc-638a476d8c19_6xeTMh2VfiAzFd86n7GNJ3", "type": "leads", "attributes": {"name": "Leadfeeder Inc.", "phone": "+358504303422", "status": "new", "visits": 10, "quality": 6, "revenue": null, "assignee": null, "industry": "N/A", "logo_url": "https://logos.leadfeeder.com/123/logo.png", "emailed_to": null, "business_id": null, "website_url": "https://www.leadfeeder.com", "facebook_url": "https://www.facebook.com/Leadfeeder", "linkedin_url": "https://www.linkedin.com/company/leadfeeder", "twitter_handle": "Leadfeeder", "last_visit_date": "2015-09-21", "first_visit_date": "2015-09-18", "view_in_leadfeeder": "https://app.leadfeeder.com/link/9088f1ad86"}, "relationships": {"location": {"data": {"id": "6xeTMh2VfiAzFd86n7GNJ3", "type": "locations"}}}}, {"id": "40157512-6131-11e5-90dc-638a476d8c19_6xeTMh2VfiAzFd86n7GNJ3", "type": "leads", "attributes": {"name": "Organic Mint", "tags": ["tag1", "tag2"], "phone": null, "status": "new", "visits": 15, "quality": 4, "revenue": "EUR *********.0", "assignee": "<EMAIL>", "industry": "N/A", "logo_url": "https://logos.leadfeeder.com/123/logo.png", "emailed_to": "<EMAIL>,<EMAIL>", "business_id": "KVK Number 123", "crm_lead_id": 110, "website_url": "https://www.organicmint.com", "facebook_url": null, "linkedin_url": null, "employee_count": 550, "twitter_handle": null, "employees_range": {"max": 550, "min": 501}, "last_visit_date": "2015-09-21", "first_visit_date": "2015-09-18", "view_in_leadfeeder": "https://app.leadfeeder.com/link/9088f1ad85", "crm_organization_id": null}, "relationships": {"location": {"data": {"id": "6xeTMh2VfiAzFd86n7GNJ3", "type": "locations"}}}}], "links": {"last": "https://api.leadfeeder.com/accounts/6002/leads?end_date=2015-09-22&page%5Bnumber%5D=2&page%5Bsize%5D=2&start_date=2015-09-11", "next": "https://api.leadfeeder.com/accounts/6002/leads?end_date=2015-09-22&page%5Bnumber%5D=2&page%5Bsize%5D=2&start_date=2015-09-11", "self": "https://api.leadfeeder.com/accounts/6002/leads?end_date=2015-09-22&page%5Bnumber%5D=1&page%5Bsize%5D=10&start_date=2015-09-11"}, "included": [{"id": "6xeTMh2VfiAzFd86n7GNJ3", "type": "locations", "attributes": {"city": "Helsinki", "region": "Uusimaa", "country": "Finland", "state_code": null, "region_code": null, "country_code": "FI"}}]}], "Enrich company": [{"id": "fea80fd1-c0f5-4d45-afa2-cb3f33a676b8", "geo": {"lat": 60.162394, "lng": 24.9350836, "city": "Helsinki", "state": null, "country": "Finland", "stateCode": null, "postalCode": "00120", "streetName": "<PERSON>udenma<PERSON><PERSON><PERSON>", "subPremise": null, "countryCode": "FI", "streetNumber": "100", "streetAddress": "100 Uudenmaankatu"}, "logo": "https://logo.clearbit.com/leadfeeder.com", "name": "Leadfeeder", "site": {"phoneNumbers": ["+358 ************", "+358 45 ********", "******-224-6059"], "emailAddresses": ["<EMAIL>"]}, "tags": ["Information Technology & Services", "Telemarketing", "Call Center", "Business Management and Planning", "Technology", "B2B", "SAAS"], "tech": ["amazon_ses", "aws_route_53", "google_apps", "google_tag_manager", "visual_website_optimizer", "google_maps", "google_analytics", "stripe", "appnexus", "rabbitmq", "dstillery", "openx", "mediamath", "okta", "pubmatic", "apache_spark", "rubicon_project", "salesforce", "microsoft_dynamics", "google_search_appliance", "bluekai", "github", "zoho_crm", "unbounce", "liferay", "postgresql", "mysql", "aws_iam", "pipedrive", "atlassian_jira", "apache_cassandra"], "type": "private", "phone": "+358 ************", "domain": "leadfeeder.com", "parent": {"domain": null}, "ticker": null, "metrics": {"raised": 4900000, "employees": 210, "marketCap": null, "alexaUsRank": null, "trafficRank": "high", "annualRevenue": null, "fiscalYearEnd": null, "employeesRange": "51-250", "alexaGlobalRank": 27194, "estimatedAnnualRevenue": "$10M-$50M"}, "twitter": {"id": "1073300916", "bio": "Leadfeeder shows you the companies visiting your website. Install the Leadfeeder Tracker & connect to Google Analytics. Try Leadfeeder Premium free for 14 days.", "site": "https://t.co/CYhY1hHFZi", "avatar": "https://pbs.twimg.com/profile_images/929961392261693440/Stt2t1K-_normal.jpg", "handle": "Leadfeeder", "location": "Helsinki, Finland", "followers": 4302, "following": 200}, "category": {"sector": "Industrials", "sicCode": "73", "gicsCode": "20201060", "industry": "Professional Services", "naicsCode": "56", "sic4Codes": ["7389"], "naics6Codes": ["561422"], "subIndustry": "Professional Services", "industryGroup": "Commercial & Professional Services", "naics6Codes2022": ["561422"]}, "facebook": {"likes": 3206, "handle": "leadfeeder"}, "linkedin": {"handle": "company/leadfeeder"}, "location": "Uudenmaankatu 100, 00120 Helsinki, Finland", "timeZone": "Europe/Helsinki", "indexedAt": "2024-02-08T18:10:40.453Z", "legalName": null, "utcOffset": 2, "crunchbase": {"handle": "organization/leadfeeder"}, "description": "Leadfeeder is a lead generation tool for B2B companies. You can turn your website visitors into sales leads by discovering the companies that visit your website and what they do there. Generating good sales leads is important. There are many potential ...", "foundedYear": null, "identifiers": {"usCIK": null, "usEIN": null}, "domainAliases": ["leadfeeder.nl", "leadfeeder.info", "leadfeeder.pl", "leadfeeder.us", "leadfeeder.ch", "lfeeder.com", "leadfeeder.fi", "leadfeeder.es"], "emailProvider": false, "techCategories": ["email_delivery_service", "dns", "productivity", "tag_management", "website_optimization", "geolocation", "analytics", "payment", "advertising", "data_management", "security", "data_processing", "crm", "marketing_automation", "content_management_system", "database", "project_management_software"], "ultimateParent": {"domain": null}}, {"id": "bd960361-64c4-4f23-b24b-5d2284a9b4e4", "geo": {"lat": 42.2010397, "lng": -71.0009158, "city": "Braintree", "state": "Massachusetts", "country": "United States", "stateCode": "MA", "postalCode": "02184", "streetName": "Pearl Street", "subPremise": null, "countryCode": "US", "streetNumber": "125", "streetAddress": "125 Pearl Street"}, "logo": "https://logo.clearbit.com/vermints.com", "name": "VerMints", "site": {"phoneNumbers": ["******-367-4442", "******-340-4440"], "emailAddresses": ["<EMAIL>", "<EMAIL>", "<EMAIL>"]}, "tags": ["Food", "<PERSON>", "E-commerce", "Manufacturing", "Food Manufacturing", "B2C"], "tech": ["facebook_connect", "google_analytics", "cloud_flare", "facebook_advertiser", "bigcommerce", "google_tag_manager", "adobe_dynamic_tag_management", "appnexus", "openx", "the_trade_desk", "pubmatic", "rubicon_project", "bluekai", "matomo", "aggregate_knowledge"], "type": "private", "phone": "******-367-4442", "domain": "vermints.com", "parent": {"domain": null}, "ticker": null, "metrics": {"raised": null, "employees": 5, "marketCap": null, "alexaUsRank": null, "trafficRank": "very_low", "annualRevenue": null, "fiscalYearEnd": null, "employeesRange": "1-10", "alexaGlobalRank": 7316867, "estimatedAnnualRevenue": "$0-$1M"}, "twitter": {"id": "46449053", "bio": "#Organic, #glutenfree, #GMOfree, #vegan, #nutfree & #kosher mints. The best mints on earth, made from nature and nature only.", "site": "https://t.co/d9rBNvmHqo", "avatar": "https://pbs.twimg.com/profile_images/1323280146502164486/Nave3hYb_normal.jpg", "handle": "VerMints", "location": "USA", "followers": 2283, "following": 484}, "category": {"sector": "Consumer Staples", "sicCode": "54", "gicsCode": "30202030", "industry": "Food Products", "naicsCode": "31", "sic4Codes": ["5441"], "naics6Codes": ["311340"], "subIndustry": "Packaged Foods & Meats", "industryGroup": "Food, Beverage & Tobacco", "naics6Codes2022": ["311340"]}, "facebook": {"likes": 1890, "handle": "115824945738"}, "linkedin": {"handle": "company/vermints-inc."}, "location": "125 Pearl St, Braintree, MA 02184, USA", "timeZone": "America/New_York", "indexedAt": "2024-02-04T03:04:43.285Z", "legalName": null, "utcOffset": -5, "crunchbase": {"handle": null}, "description": "VerMints is a leading manufacturer of organic breath mints and pastilles. All six flavors PepperMint, Wintergreen, Cinnamon, GingerMint, Chai and Cafe Express are certified organic, non GMO, gluten free and kosher. VerMints are True Mints. They are hon...", "foundedYear": 2001, "identifiers": {"usCIK": null, "usEIN": null}, "domainAliases": [], "emailProvider": false, "techCategories": ["authentication_services", "analytics", "dns", "advertising", "ecommerce", "tag_management", "data_management"], "ultimateParent": {"domain": null}}], "Get all Leedfeeder accounts": [{"data": [{"id": "233695", "type": "accounts", "attributes": {"name": "n8n", "on_trial": true, "timezone": "Europe/Berlin", "subscription": "premium", "subscription_addons": ["guard", "google_data_studio"]}}]}]}, "connections": {"Setup": {"main": [[{"node": "Get all Leedfeeder accounts", "type": "main", "index": 0}]]}, "Get Leads": {"main": [[{"node": "Split Out Leads", "type": "main", "index": 0}]]}, "Enrich company": {"main": [[{"node": "Filter Leads by company criteria", "type": "main", "index": 0}]]}, "Split Out Leads": {"main": [[{"node": "Filter leads by engagement", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Setup", "type": "main", "index": 0}]]}, "Split out accounts": {"main": [[{"node": "Only for wanted accounts", "type": "main", "index": 0}]]}, "Only for wanted accounts": {"main": [[{"node": "Get Leads", "type": "main", "index": 0}]]}, "Filter leads by engagement": {"main": [[{"node": "Enrich company", "type": "main", "index": 0}]]}, "Get all Leedfeeder accounts": {"main": [[{"node": "Split out accounts", "type": "main", "index": 0}]]}, "Filter Leads by company criteria": {"main": [[{"node": "Save leads to Google Sheets", "type": "main", "index": 0}]]}}}