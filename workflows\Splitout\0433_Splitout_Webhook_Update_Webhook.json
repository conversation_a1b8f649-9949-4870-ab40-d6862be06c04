{"meta": {"instanceId": "29aba5a622661908a48f94e4ff4983d5d88a33ca233b57cebe114886a24f3172"}, "nodes": [{"id": "85c8481e-9bc8-49ca-bce1-1d2d915829bd", "name": "Respond All Items", "type": "n8n-nodes-base.respondToWebhook", "position": [2180, 500], "parameters": {"options": {}, "respondWith": "allIncomingItems"}, "typeVersion": 1}, {"id": "194a1e37-ae2a-4142-a3f6-38161abbc20b", "name": "Respond Asked <PERSON>em", "type": "n8n-nodes-base.respondToWebhook", "position": [2180, 280], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "9bb8cb37-9723-4f85-8878-f3b0abe5763f", "name": "Incoming Webhook Trigger", "type": "n8n-nodes-base.webhook", "position": [700, 300], "webhookId": "309c36da-224c-4023-b989-8f991502b625", "parameters": {"path": "eu-exchange-rate", "options": {}, "responseMode": "responseNode"}, "typeVersion": 1.1}, {"id": "f1fe517a-bd74-45e0-b9df-9d7167d50068", "name": "Get latest Euro exchange rates", "type": "n8n-nodes-base.httpRequest", "position": [920, 300], "parameters": {"url": "={{ \"https://www.ecb.europa.eu/stats/eurofxref/eurofxref-daily.xml?\" + Math.floor(Math.random() * (999999999 - 100000000 + 1)) + 100000000 }}", "options": {}}, "typeVersion": 4.1}, {"id": "92d6936f-2c6f-4069-89bd-fe044664bb8b", "name": "Convert XML to JSON", "type": "n8n-nodes-base.xml", "position": [1140, 300], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "a923e692-5da1-4e87-99c1-c22372a99d96", "name": "Split Out Data", "type": "n8n-nodes-base.splitOut", "position": [1360, 300], "parameters": {"options": {}, "fieldToSplitOut": "['gesmes:Envelope'].Cube.Cube.Cube"}, "typeVersion": 1}, {"id": "6a1de054-ef7a-41d9-886c-f31d4801b83e", "name": "If Webhook Trigger has URL query", "type": "n8n-nodes-base.if", "position": [1580, 300], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "c3c32528-8f02-4414-be79-0cb8e18a4cbf", "operator": {"type": "object", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $('Incoming Webhook Trigger').item.json.query }}", "rightValue": ""}]}}, "typeVersion": 2}, {"id": "be62a49c-36db-48cf-819a-0c004fa37a0e", "name": "Filter the currency symbol", "type": "n8n-nodes-base.filter", "position": [1880, 280], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "b67b8d32-f164-473d-9822-78759b4ea827", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.currency }}", "rightValue": "={{ $('Incoming Webhook Trigger').item.json.query.foreign }}"}]}}, "typeVersion": 2}, {"id": "99b449df-b350-4e35-ad9f-4555a7cacbc9", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [860, 100], "parameters": {"width": 431.3108108108107, "height": 424.89189189189204, "content": "## Note\n* The HTTP request adds a randomized URL parameter to ensure getting the latest data by prevent caching.\n* The provided data is XML-formatted and therefore converted to JSON formatting.\n\nRead more about Euro foreign exchange reference rates [here](https://www.ecb.europa.eu/stats/policy_and_exchange_rates/euro_reference_exchange_rates/html/index.en.html)."}, "typeVersion": 1}], "pinData": {}, "connections": {"Split Out Data": {"main": [[{"node": "If Webhook Trigger has URL query", "type": "main", "index": 0}]]}, "Convert XML to JSON": {"main": [[{"node": "Split Out Data", "type": "main", "index": 0}]]}, "Incoming Webhook Trigger": {"main": [[{"node": "Get latest Euro exchange rates", "type": "main", "index": 0}]]}, "Filter the currency symbol": {"main": [[{"node": "Respond Asked <PERSON>em", "type": "main", "index": 0}]]}, "Get latest Euro exchange rates": {"main": [[{"node": "Convert XML to JSON", "type": "main", "index": 0}]]}, "If Webhook Trigger has URL query": {"main": [[{"node": "Filter the currency symbol", "type": "main", "index": 0}], [{"node": "Respond All Items", "type": "main", "index": 0}]]}}}