{"id": 1, "name": "Daily Journal Reminder", "nodes": [{"name": "Morning reminder", "type": "n8n-nodes-base.cron", "notes": "Trigger very morning", "position": [220, 60], "parameters": {"triggerTimes": {"item": [{"hour": 6}]}}, "notesInFlow": true, "typeVersion": 1, "alwaysOutputData": true}, {"name": "format reminder", "type": "n8n-nodes-base.functionItem", "position": [460, 60], "parameters": {"functionCode": "\n// Creates message with todays date\nconst today = new Date()\nconst yesterday = new Date(today)\n\nyesterday.setDate(yesterday.getDate() - 1)\nconst message = `What did you do: ${yesterday.toISOString().split('T')[0]}`\n\nreturn {message};"}, "typeVersion": 1}, {"name": "Send journal reminder", "type": "n8n-nodes-base.telegram", "position": [700, 60], "parameters": {"text": "={{$node[\"format reminder\"].json[\"message\"]}}", "chatId": "666884239", "additionalFields": {}}, "credentials": {}, "typeVersion": 1}], "active": true, "settings": {}, "connections": {"format reminder": {"main": [[{"node": "Send journal reminder", "type": "main", "index": 0}]]}, "Morning reminder": {"main": [[{"node": "format reminder", "type": "main", "index": 0}]]}}}