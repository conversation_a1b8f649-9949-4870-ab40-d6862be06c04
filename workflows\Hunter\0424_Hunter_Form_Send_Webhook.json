{"nodes": [{"id": "74e0d9d8-9a05-4bf6-82a1-7c7c6b488ac7", "name": "n8n Form Trigger", "type": "n8n-nodes-base.formTrigger", "position": [380, 420], "webhookId": "ee00f236-5dad-49db-8f29-71b7bce37894", "parameters": {"path": "0bf8840f-1cc4-46a9-86af-a3fa8da80608", "options": {}, "formTitle": "Contact us", "formFields": {"values": [{"fieldLabel": "What's your business email?"}]}, "formDescription": "We'll get back to you soon"}, "typeVersion": 2}, {"id": "86956707-6a69-465e-b73e-e49bfb6fa252", "name": "Check if the email is valid", "type": "n8n-nodes-base.if", "position": [800, 420], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "54d84c8a-63ee-40ed-8fb2-301fff0194ba", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "valid"}]}}, "typeVersion": 2}, {"id": "15991bbc-77c7-405f-8d8d-aeb5693b8eed", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [380, 220], "parameters": {"color": 5, "width": 547, "height": 158, "content": "### 👨‍🎤 Setup\n1. Add you **Mad<PERSON>udu**, **Hunter**, and **Email** credentials \n2. Set the email where you want the alert\n3. Click the Test Workflow button, enter your email and check the Slack channel\n4. Activate the workflow and use the form trigger production URL to collect your leads in a smart way "}, "typeVersion": 1}, {"id": "0a1d7df3-d536-4530-a3f1-d374bb645738", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [380, 560], "parameters": {"color": 7, "width": 162, "height": 139, "content": "👆 You can exchange this with any form you like (*e.g. Typeform, Google forms, Survey Monkey...*)"}, "typeVersion": 1}, {"id": "694b79a4-878e-4014-8975-8b81fa10f556", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1360, 480], "parameters": {"color": 7, "width": 162, "height": 84, "content": "👆 Adjust the fit as you see necessary"}, "typeVersion": 1}, {"id": "e843b7e4-631a-4679-952e-3f4f3ef4592d", "name": "Email is not valid, do nothing", "type": "n8n-nodes-base.noOp", "position": [1140, 560], "parameters": {}, "typeVersion": 1}, {"id": "9e20efa9-add0-4109-8e83-67fd9ed6e2f9", "name": "Score lead with <PERSON><PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.httpRequest", "position": [1140, 320], "parameters": {"url": "=https://api.madkudu.com/v1/persons?email={{ $json.email }}", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "71W5Bt9g1G9GOhVL", "name": "<PERSON><PERSON><PERSON><PERSON> Lead score"}}, "typeVersion": 4.1}, {"id": "1e0c1e73-b027-481c-a560-379e7c609b8e", "name": "Verify email with <PERSON>", "type": "n8n-nodes-base.hunter", "position": [600, 420], "parameters": {"email": "={{ $json['What\\'s your business email?'] }}", "operation": "emailVerifier"}, "credentials": {"hunterApi": {"id": "ecwmdHFSBU5GGnV1", "name": "Hunter account"}}, "typeVersion": 1}, {"id": "1769cddc-e479-4816-8807-b2c1a7cd72c3", "name": "Not interesting enough", "type": "n8n-nodes-base.noOp", "position": [1680, 460], "parameters": {}, "typeVersion": 1}, {"id": "f01ed0bd-e198-47d0-95de-cf15ff04be75", "name": "if customer fit score > 60", "type": "n8n-nodes-base.if", "position": [1380, 320], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "c23d7b34-a4ae-421f-bd7a-6a3ebb05aafe", "operator": {"type": "number", "operation": "gt"}, "leftValue": "={{ $json.properties.customer_fit.score }}", "rightValue": 60}]}}, "typeVersion": 2}, {"id": "f500ad36-3f4c-4c3e-aadc-ab014be7cb7d", "name": "Gmail", "type": "n8n-nodes-base.gmail", "position": [1680, 160], "parameters": {"sendTo": "<EMAIL>", "message": "=Got a hot lead for you  {{ $json.properties.first_name }} {{ $json.properties.last_name }} from  {{ $json.company.properties.name }} ({{ $json.company.properties.domain }}) based out of {{ $json.company.properties.location.state }}, {{ $json.company.properties.location.country }}.\n\n\n{{ $('Score lead with MadKudu').item.json.properties.customer_fit.top_signals_formatted }}", "options": {}, "subject": "=⭐ Hot lead alert: {{ $json.properties.first_name }} {{ $json.properties.last_name }}", "emailType": "text"}, "credentials": {"gmailOAuth2": {"id": "rd2agqPeJBD2377h", "name": "Work Gmail"}}, "typeVersion": 2.1}, {"id": "b47b0249-fa84-42b8-b7c5-0e204bc35db4", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1640, 40], "parameters": {"color": 7, "width": 162, "height": 84, "content": "👇🏽 Update the email to send to"}, "typeVersion": 1}], "pinData": {"n8n Form Trigger": [{"formMode": "test", "submittedAt": "2024-02-22T13:59:54.709Z", "What's your business email?": "<EMAIL>"}]}, "connections": {"n8n Form Trigger": {"main": [[{"node": "Verify email with <PERSON>", "type": "main", "index": 0}]]}, "Score lead with MadKudu": {"main": [[{"node": "if customer fit score > 60", "type": "main", "index": 0}]]}, "Verify email with Hunter": {"main": [[{"node": "Check if the email is valid", "type": "main", "index": 0}]]}, "if customer fit score > 60": {"main": [[{"node": "Gmail", "type": "main", "index": 0}], [{"node": "Not interesting enough", "type": "main", "index": 0}]]}, "Check if the email is valid": {"main": [[{"node": "Score lead with <PERSON><PERSON><PERSON><PERSON>", "type": "main", "index": 0}], [{"node": "Email is not valid, do nothing", "type": "main", "index": 0}]]}}}