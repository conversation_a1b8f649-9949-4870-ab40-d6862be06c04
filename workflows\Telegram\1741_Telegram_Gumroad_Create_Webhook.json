{"id": "W1xEzKKEd1qV2D7V", "meta": {"instanceId": "dfec462482c1b16c8ef1928d51584c7f0ae64b3bfaa72e08675b15754b903bd2", "templateCredsSetupCompleted": true}, "name": "2. Add Beehiiv newsletter subscribers from Gumroad sales", "tags": [{"id": "IQNCfEb2qHXxw7NO", "name": "template", "createdAt": "2025-04-26T14:50:39.694Z", "updatedAt": "2025-04-26T14:50:39.694Z"}, {"id": "K4VMFA2Vwk2LRKCu", "name": "1node", "createdAt": "2025-04-26T11:57:21.772Z", "updatedAt": "2025-04-26T11:57:21.772Z"}, {"id": "mAtRn7JRKGsmOL3v", "name": "gumroad", "createdAt": "2025-04-26T11:57:16.167Z", "updatedAt": "2025-04-26T11:57:16.167Z"}], "nodes": [{"id": "18e8530e-d04f-47d4-b406-b2961d45f1c1", "name": "Gumroad Sale Trigger", "type": "n8n-nodes-base.gumroadTrigger", "position": [-380, -280], "webhookId": "98ba7c08-2193-4ddf-9249-af7899716925", "parameters": {"resource": "sale"}, "credentials": {"gumroadApi": {"id": "wgjGSvLjsRBJImsQ", "name": "Gumroad account"}}, "typeVersion": 1}, {"id": "6e464a73-a5c0-4a5d-95ce-c3cc2547a373", "name": "append row in CRM", "type": "n8n-nodes-base.googleSheets", "position": [300, -280], "parameters": {"columns": {"value": {"date": "={{ $('Gumroad Sale Trigger').item.json.sale_timestamp }}", "email": "={{ $('Gumroad Sale Trigger').item.json.email }}", "country": "={{ $('Gumroad Sale Trigger').item.json.ip_country }}", "product name": "={{ $('Gumroad Sale Trigger').item.json.product_name }}"}, "schema": [{"id": "date", "type": "string", "display": true, "removed": false, "required": false, "displayName": "date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "product name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "product name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "email", "type": "string", "display": true, "removed": false, "required": false, "displayName": "email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "country", "type": "string", "display": true, "removed": false, "required": false, "displayName": "country", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1XYMstoZ4j3O5T-UYz21ky7P5bkUtzYXQGYCQTRVWCI4/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1XYMstoZ4j3O5T-UYz21ky7P5bkUtzYXQGYCQTRVWCI4", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1XYMstoZ4j3O5T-UYz21ky7P5bkUtzYXQGYCQTRVWCI4/edit?usp=drivesdk", "cachedResultName": "Gumroad sales CRM"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "Ou2SgvNZctBeYWT5", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "1f1b0840-0da9-4118-96d5-62a1a36f902b", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-560, -580], "parameters": {"width": 320, "height": 460, "content": "## Trigger on a new Gumroad sale\n### Requirements\n- A [Gumroad]() account\n- A product listed. We used ours [here](https://1node.gumroad.com/l/topaitools)\n- Head to Settings > Advanced, and create a new application\n\n### Set up\n- Paste your access token on this Gumroad sale trigger"}, "typeVersion": 1}, {"id": "35f93009-1960-4cde-bfa6-dc7dfed5e194", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-220, -500], "parameters": {"color": 4, "width": 400, "height": 380, "content": "## Connection to [Beehiiv](https://www.beehiiv.com?via=1node-ai) newsletter \n### Requirements\n- A [Beehiiv](https://www.beehiiv.com?via=1node-ai) account\n- A publication created\n- Generate a new API"}, "typeVersion": 1}, {"id": "bbfcab7c-92fa-4a23-abc2-480c286905ac", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [200, -540], "parameters": {"color": 4, "width": 320, "height": 420, "content": "## Load into CRM\n### Requirements\n- Set up your api and credentials for Google Sheets. You can find the n8n docs [here](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googlesheets/?utm_source=n8n_app&utm_medium=node_settings_modal-credential_link&utm_campaign=n8n-nodes-base.googleSheets)\n- Append the row to your table with your desired data collected previously"}, "typeVersion": 1}, {"id": "46a7cfcf-a042-4fe3-9f76-62eb46ecbbd0", "name": "List publications", "type": "n8n-nodes-base.httpRequest", "position": [-160, -280], "parameters": {"url": "https://api.beehiiv.com/v2/publications", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpBearerAuth": {"id": "ZcZlbMhodQQpmBk3", "name": "Bear<PERSON>"}, "httpHeaderAuth": {"id": "Qvu08SMoEOK2V2xB", "name": "Beehiiv newsletter"}}, "typeVersion": 4.2}, {"id": "ab7bede8-0019-4cb4-ad16-b9ccbbe8b15a", "name": "Post subscription", "type": "n8n-nodes-base.httpRequest", "position": [20, -280], "parameters": {"url": "=https://api.beehiiv.com/v2/publications/{{ $json.data[0].id }}/subscriptions", "method": "POST", "options": {}, "sendBody": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "email", "value": "={{ $('Gumroad Sale Trigger').item.json.email }}"}]}, "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "Qvu08SMoEOK2V2xB", "name": "Beehiiv newsletter"}}, "typeVersion": 4.2}, {"id": "cafb7301-06fe-49f9-a033-434459b181e5", "name": "Notify in channel", "type": "n8n-nodes-base.telegram", "position": [760, -280], "webhookId": "16dedd5e-7f93-45fb-8add-2928a53f125f", "parameters": {"text": "=🔔 New Gumroad sale!\nProduct: {{ $('Gumroad Sale Trigger').item.json.product_name }} \nEmail: {{ $('Gumroad Sale Trigger').item.json.email }} \nCountry: {{ $('Gumroad Sale Trigger').item.json.ip_country }}", "chatId": "={{ $json.telegramChatId }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "TbJJ7DHhEE1GwKQQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "912c8a8f-074e-486f-b337-b828ae19b6af", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [540, -440], "parameters": {"width": 360, "height": 320, "content": "## Notify team in Telegram\nSet up your Telegram bot and add to a channel as admin to notify everyone about the updates."}, "typeVersion": 1}, {"id": "5613a93b-f5ae-4478-86a8-4ea87ac5b9bd", "name": "Set <PERSON>t<PERSON>", "type": "n8n-nodes-base.set", "position": [580, -280], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "089c1b05-3ac3-419e-a25e-e98d0b7fa49c", "name": "telegramChatId", "type": "string", "value": "<your chat id>"}]}}, "typeVersion": 3.4}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "34946f82-9af3-4e1b-bf98-67fb4c55a26c", "connections": {"Set ChatID": {"main": [[{"node": "Notify in channel", "type": "main", "index": 0}]]}, "List publications": {"main": [[{"node": "Post subscription", "type": "main", "index": 0}]]}, "Post subscription": {"main": [[{"node": "append row in CRM", "type": "main", "index": 0}]]}, "append row in CRM": {"main": [[{"node": "Set <PERSON>t<PERSON>", "type": "main", "index": 0}]]}, "Gumroad Sale Trigger": {"main": [[{"node": "List publications", "type": "main", "index": 0}]]}}}