{"id": "22PddLUgcjSJbT1w", "meta": {"instanceId": "fa7d5e2425ec76075df7100dbafffed91cc6f71f12fe92614bf78af63c54a61d", "templateCredsSetupCompleted": true}, "name": "MongoDB Agent", "tags": [], "nodes": [{"id": "d8c07efe-eca0-48cb-80e6-ea8117073c5f", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1300, 560], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "TreGPMKr9hrtCvVp", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "636de178-7b68-429a-9371-41cf2a950076", "name": "MongoDBAggregate", "type": "n8n-nodes-base.mongoDbTool", "position": [1640, 540], "parameters": {"query": "={{ $fromAI(\"pipeline\", \"The MongoDB pipeline to execute\" , \"string\" , [{\"$match\" : { \"rating\" : 5 } }])}}", "operation": "aggregate", "collection": "movies", "descriptionType": "manual", "toolDescription": "Get from AI the MongoDB Aggregation pipeline to get context based on the provided pipeline, the document structure of the documents is : {\n \"plot\": \"A group of bandits stage a brazen train hold-up, only to find a determined posse hot on their heels.\",\n \"genres\": [\n \"Short\",\n \"Western\"\n ],\n \"runtime\": 11,\n \"cast\": [\n \"<PERSON><PERSON><PERSON>. Abadie\",\n \"<PERSON> '<PERSON>cho <PERSON>\",\n ...\n ],\n \"poster\": \"...jpg\",\n \"title\": \"The Great Train Robbery\",\n \"fullplot\": \"Among the earliest existing films in American cinema - notable as the ...\",\n \"languages\": [\n \"English\"\n ],\n \"released\": \"date\"\n },\n \"directors\": [\n \"<PERSON>\"\n ],\n \"rated\": \"TV-G\",\n \"awards\": {\n \"wins\": 1,\n \"nominations\": 0,\n \"text\": \"1 win.\"\n },\n \"lastupdated\": \"2015-08-13 00:27:59.*********\",\n \"year\": 1903,\n \"imdb\": {\n \"rating\": 7.4,"}, "credentials": {"mongoDb": {"id": "8xGgiXzf2o0L4a0y", "name": "MongoDB account"}}, "typeVersion": 1.1}, {"id": "e0f248dc-22b7-40a2-a00e-6298b51e4470", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [1500, 540], "parameters": {"contextWindowLength": 10}, "typeVersion": 1.2}, {"id": "da27ee52-43db-4818-9844-3c0a064bf958", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [1160, 400], "webhookId": "0730df2d-2f90-45e0-83dc-609668260fda", "parameters": {"mode": "webhook", "public": true, "options": {"allowedOrigins": "*"}}, "typeVersion": 1.1}, {"id": "9ad79da9-3145-44be-9026-e37b0e856f5d", "name": "insertFavorite", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [1860, 520], "parameters": {"name": "insertFavorites", "workflowId": {"__rl": true, "mode": "list", "value": "6QuKnOrpusQVu66Q", "cachedResultName": "insertMongoDB"}, "description": "=Use this tool only to add favorites with the structure of {\"title\" : \"recieved title\" }"}, "typeVersion": 1.2}, {"id": "4d7713d1-d2ad-48bf-971b-b86195e161ca", "name": "AI Agent - Movie Recommendation", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1380, 300], "parameters": {"text": "=Assistant for best movies context, you have tools to search using \"MongoDBAggregate\" and you need to provide a MongoDB aggregation pipeline code array as a \"query\" input param. User input and request: {{ $json.chatInput }}. Only when a user confirms a favorite movie use the insert favorite using the \"insertFavorite\" workflow tool of to insertFavorite as { \"title\" : \"<TITLE>\" }.", "options": {}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "2eac8aed-9677-4d89-bd76-456637f5b979", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [880, 300], "parameters": {"width": 216.0875923062025, "height": 499.89779507612025, "content": "## AI Agent powered by OpenAI and MongoDB \n\nThis flow is designed to work as an AI autonomous agent that can get chat messages, query data from MongoDB using the aggregation framework.\n\nFollowing by augmenting the results from the sample movies collection and allowing storing my favorite movies back to the database using an \"insert\" flow. "}, "typeVersion": 1}, {"id": "4d8130fe-4aed-4e09-9c1d-60fb9ac1a500", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1300, 720], "parameters": {"content": "## Process\n\nThe message is being processed by the \"Chat Model\" and the correct tool is used according to the message. "}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "879aab24-6346-435f-8fd4-3fca856ba64c", "connections": {"insertFavorite": {"ai_tool": [[{"node": "AI Agent - Movie Recommendation", "type": "ai_tool", "index": 0}]]}, "MongoDBAggregate": {"ai_tool": [[{"node": "AI Agent - Movie Recommendation", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent - Movie Recommendation", "type": "ai_languageModel", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent - Movie Recommendation", "type": "ai_memory", "index": 0}]]}, "When chat message received": {"main": [[{"node": "AI Agent - Movie Recommendation", "type": "main", "index": 0}]]}}}