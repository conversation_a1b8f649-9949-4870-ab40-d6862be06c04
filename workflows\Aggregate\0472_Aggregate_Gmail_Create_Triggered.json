{"meta": {"instanceId": "d17dadc75de867b08b7744d7ba00e531e75580e2dec35d52f2d34e58481e1fb7", "templateCredsSetupCompleted": true}, "nodes": [{"id": "814e3849-1ae1-4124-bdfc-b72017e9d7c2", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [0, 0], "parameters": {"color": 4, "width": 420.*************, "height": 240.**************, "content": "## Add AI labels to Gmail messages\nWith this workflow you can automatically set labels for your Gmail message according to its content. \n\nIn this workflow available are 3 labels: \"Partnership\", \"Inquiry\" and \"Notification\". Feel free to adjust labels according to your needs. \n\n**Please remember to set label names both in your Gmail account and workflow.**"}, "typeVersion": 1}, {"id": "e83fa311-b5ba-427e-a98e-573394b882dd", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [0, 260], "parameters": {"width": 421.*************, "height": 257.**************, "content": "## ⚠️ Note\n\n1. Complete video guide for this workflow is available [on my YouTube](https://youtu.be/a8Dhj3Zh9vQ). \n2. Remember to add your credentials and configure nodes (covered in the video guide).\n3. If you like this workflow, please subscribe to [my YouTube channel](https://www.youtube.com/@workfloows) and/or [my newsletter](https://workfloows.com/).\n\n**Thank you for your support!**"}, "typeVersion": 1}, {"id": "4c20d029-750f-476b-9348-6e250ea64d52", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [440, 0], "parameters": {"width": 238.*************, "height": 348.*************, "content": "### Gmail Trigger\nReceive data from Gmail about new incoming message. \n\n⚠️ Set polling interval according to your needs."}, "typeVersion": 1}, {"id": "22923079-80ce-4495-b0f0-da7122195c56", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1160, 380], "parameters": {"width": 241.53974014153226, "height": 319.*************, "content": "###\n\n\n\n\n\n\n\n\n\n\n### JSON schema\nEdit JSON schema and label names according to your needs.\n\n⚠️ **Label names in system prompt and JSON schema should be the same.**"}, "typeVersion": 1}, {"id": "40735a58-daaa-43ac-9658-706c3cf0cbba", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1900, 20], "parameters": {"width": 226.**************, "height": 347.*************, "content": "### Merge labels\nCombine labels retrieved from Gmail account and assigned by AI together."}, "typeVersion": 1}, {"id": "87e0f9e2-a2ff-46cf-896a-138b1bde2d0e", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [2160, 20], "parameters": {"width": 452.**************, "height": 347.*************, "content": "### Aggregarte labels and add to message\nCreate array of label IDs and add to the desired email message in Gmail."}, "typeVersion": 1}, {"id": "1d533664-e5e8-4dc8-afac-bfc5996e4bf9", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [700, 0], "parameters": {"width": 238.*************, "height": 348.*************, "content": "### Get message content\nBased on Gmail message ID retrieve body content of the email and pass it to AI chain."}, "typeVersion": 1}, {"id": "e613ca64-50ae-4d7c-b0fc-15812dadcd68", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [960, 0], "parameters": {"width": 378.**************, "height": 348.*************, "content": "### Assign labels\nLet the AI decide which labels suit the best content of the message.\n\n⚠️ **Remember to edit system prompt** - modify label names and instructions according to your needs."}, "typeVersion": 1}, {"id": "a2005e70-6774-45ce-b9c6-742786f49964", "name": "Gmail trigger", "type": "n8n-nodes-base.gmailTrigger", "position": [500, 180], "parameters": {"simple": false, "filters": {}, "options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}}, "credentials": {"gmailOAuth2": {"id": "SPECSn66s6QHmld9", "name": "Gmail account"}}, "typeVersion": 1.2}, {"id": "4b6f7d21-6155-42b7-93ff-2f530df3692f", "name": "Get message content", "type": "n8n-nodes-base.gmail", "position": [760, 180], "webhookId": "b773894c-18c6-454d-9271-6de10be1b7c4", "parameters": {"messageId": "={{ $json.id }}", "operation": "get"}, "credentials": {"gmailOAuth2": {"id": "SPECSn66s6QHmld9", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "ad577660-d9f4-4031-ad16-7021a02bb18e", "name": "Assign labels for message", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1020, 180], "parameters": {"text": "={{ $('Gmail trigger').item.json.text }}", "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.5}, {"id": "1e11d30f-4c73-4fd0-a365-aeb43bee4252", "name": "OpenAI Chat", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1000, 400], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "PrK67ozsBFqSIYG9", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "4a504b64-fb28-44fb-a80a-6f5e5c5a1949", "name": "JSON Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [1240, 400], "parameters": {"schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"labels\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\",\n        \"enum\": [\"Inquiry\", \"Partnership\", \"Notification\"]\n      }\n    }\n  },\n  \"required\": [\"labels\"]\n}"}, "typeVersion": 1.2}, {"id": "f5ac1b01-0980-4ee4-b4f5-5057258eab70", "name": "Set label values", "type": "n8n-nodes-base.set", "position": [1400, 180], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "72d11a72-6693-447c-b7ca-4ba1a3579075", "name": "labels", "type": "array", "value": "={{ $json.output.labels }}"}]}}, "typeVersion": 3.4}, {"id": "e368e343-728e-4e2f-a37f-5e203000d090", "name": "Get all labels", "type": "n8n-nodes-base.gmail", "position": [1680, 60], "webhookId": "dec6f574-f47c-4b5d-86b9-2b0f6c957145", "parameters": {"resource": "label", "returnAll": true}, "credentials": {"gmailOAuth2": {"id": "SPECSn66s6QHmld9", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "48ce8351-5d04-4697-b68d-bb84286e0b2b", "name": "Split out assigned labels", "type": "n8n-nodes-base.splitOut", "position": [1680, 280], "parameters": {"options": {}, "fieldToSplitOut": "labels"}, "typeVersion": 1}, {"id": "cc1aa3ac-7427-4761-aacd-caf16c64d7fb", "name": "Merge corresponding labels", "type": "n8n-nodes-base.merge", "position": [1960, 180], "parameters": {"mode": "combine", "options": {}, "advanced": true, "mergeByFields": {"values": [{"field1": "name", "field2": "labels"}]}}, "typeVersion": 3.1}, {"id": "97fefda6-5936-42a7-a30a-8de4149aa445", "name": "Aggregate label IDs", "type": "n8n-nodes-base.aggregate", "position": [2220, 180], "parameters": {"options": {}, "fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "id"}]}}, "typeVersion": 1}, {"id": "7befd422-7243-43af-9b34-21c05a069013", "name": "Add labels to message", "type": "n8n-nodes-base.gmail", "position": [2440, 180], "webhookId": "4f345fc9-2afd-478b-be3b-d3d28f0fbc82", "parameters": {"labelIds": "={{ $json.id }}", "messageId": "={{ $('Gmail trigger').item.json[\"id\"] }}", "operation": "addLabels"}, "credentials": {"gmailOAuth2": {"id": "SPECSn66s6QHmld9", "name": "Gmail account"}}, "typeVersion": 2.1}], "pinData": {"Gmail trigger": [{"id": "1962eb5ee3119d76", "to": {"html": "<span class=\"mp_address_group\"><span class=\"mp_address_name\">Workfloows Tutorial</span> &lt;<a href=\"mailto:<EMAIL>\" class=\"mp_address_email\"><EMAIL></a>&gt;</span>", "text": "\"Workfloows Tutorial\" <<EMAIL>>", "value": [{"name": "Workfloows Tutorial", "address": "<EMAIL>"}]}, "date": "2025-04-13T10:33:05.000Z", "from": {"html": "<span class=\"mp_address_group\"><span class=\"mp_address_name\">Workfloows</span> &lt;<a href=\"mailto:<EMAIL>\" class=\"mp_address_email\"><EMAIL></a>&gt;</span>", "text": "\"Workfloows\" <<EMAIL>>", "value": [{"name": "Workfloows", "address": "<EMAIL>"}]}, "html": "<div dir=\"ltr\">Hey! <div><br></div><div>We&#39;d love to cooperate with you - could you please send us your offer? </div><div><br></div><div>Best,</div><div><PERSON><PERSON></div></div>\n", "text": "Hey!\n\nWe'd love to cooperate with you - could you please send us your offer?\n\nBest,\n<PERSON><PERSON>\n", "headers": {"to": "To: Workfloows Tutorial <<EMAIL>>", "date": "Date: Sun, 13 Apr 2025 12:33:05 +0200", "from": "From: Workfloows <<EMAIL>>", "subject": "Subject: Inquiry for cooperation", "x-gm-gg": "X-Gm-Gg: ASbGncsLoGTllITLV/hYh7p2Re1X0A4Fd5a1uQb58nQ1FCzXrvjCL9BY2H/6U4fN3wn\r\n\tFkTSzNo0PUVLScNsBjkkOdwaqHhHLT+UzxaAtr8LpnucVTxhWbI08sl8lxjJUsHJwsJwIpSaAqX\r\n\tkKKBKUewdQhcwJNh4P22vOalA=", "arc-seal": "ARC-Seal: i=1; a=rsa-sha256; t=1744540397; cv=none;\r\n        d=google.com; s=arc-20240605;\r\n        b=BWNyT3FtnssueCPH4di13k++uCiJsB73BRfuQ63N0/+fUQqAvkZRMdN4cZiSCXpLph\r\n         +ag3l4hgkp9yuE66MQjv18vWzMaUsmaj5obHWe+6x6YcPkMRW/y+gNitCD+mftpYsQpz\r\n         nQpkoyZaY3h9o9vmcUUmOPWCWrUysy8y8sOOhht7Tmekzs3tQj+aLyXJNv+j9SCwvsTE\r\n         yd5uisDlrWv1zfpdUZLwNKZuCP+Jtfr01w3QT/zhBCweOccIJaFzfO4s97q8JgUgRrmx\r\n         JkrsGpSWJZKWPDh44mkmHH+bw43omIJKXYTHN9nOO3vGyqBWdGYlE0T9ZhCetHHyBbpS\r\n         b+Mw==", "received": "Received: from mail-sor-f41.google.com (mail-sor-f41.google.com. [*************])\r\n        by mx.google.com with SMTPS id 2adb3069b0e04-54c455e2176sor1769457e87.11.2025.***********.17\r\n        for <<EMAIL>>\r\n        (Google Transport Security);\r\n        Sun, 13 Apr 2025 03:33:17 -0700 (PDT)", "message-id": "Message-ID: <CA+sg_9eV=X+LusGnDSP8pDrrYZ8SLn2Maq4CYpLrg=uG=<EMAIL>>", "x-received": "X-Received: by 2002:a2e:bd88:0:b0:30d:629c:4333 with SMTP id\r\n 38308e7fff4ca-31049aacf9dmr29831171fa.34.1744540396464; Sun, 13 Apr 2025\r\n 03:33:16 -0700 (PDT)", "return-path": "Return-Path: <<EMAIL>>", "content-type": "Content-Type: multipart/alternative; boundary=\"000000000000a0b4660632a67692\"", "delivered-to": "Delivered-To: <EMAIL>", "mime-version": "MIME-Version: 1.0", "received-spf": "Received-SPF: pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) client-ip=*************;", "x-gm-features": "X-Gm-Features: ATxdqUEDRqHsd35x8e-h-zd4BcGaOVs83Rpm-BRaGlzjaiGxZMiGfgHEjxn3hNE", "dkim-signature": "DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed;\r\n        d=gmail.com; s=20230601; t=1744540397; x=1745145197; dara=google.com;\r\n        h=to:subject:message-id:date:from:mime-version:from:to:cc:subject\r\n         :date:message-id:reply-to;\r\n        bh=yhyki7Kf5bQNf/8oq2uNTa/y/MoSnhI+j3ZqeBT892s=;\r\n        b=Njlov8RLGs/rZIz07rSJIfn8oQDEXgybU4mJ0ujD8T8m7J4NabveIhdobrrHraaZqN\r\n         iwOZHBn0TTWAbuccHjfU+BBB8FvJ4/jfCXKbWSwPIWHd53P1wuTxvXYgbkXX4A/W675L\r\n         zPSVraK4W1heQDTViCc2MmV5+tH6pbe/52xTOwvx8Xf0WTN1Ku3K/DY8EIsnd0OKdrEn\r\n         ml+/LHhVMmwR5lZtte7mTlYi/c5FG8XO95Nh/Ftl22RpuKl1QPFUdJcx+bEVeUh62uHM\r\n         Bd8pyi0y/LVKIqNtL/DIvpt2+bt9TLm7MB2P61KMUAP75qZCparl2MWLR62c8tW7cFqm\r\n         wPHA==", "x-gm-message-state": "X-Gm-Message-State: AOJu0YzGNl9So86XWoTm+y0PO71OilI6ljQ/cHqUDKwYpIrbLMy8ZiCe\r\n\ty2NKHmx051OaBkuEbe2bQD3dl78xO6sJPWWrTXUn1mV9b52v6vaQsLXXkQWx5cKuaw9spNE1dpU\r\n\tzsVB7chkTKdZ5HO31p29RiSug2SJ0AZXS", "x-google-smtp-source": "X-Google-Smtp-Source: AGHT+IF6PTdq6zPXbZy1CEUmKyNDSavbnDjbcWp5Y3hfiFlZruW8yABRwE9q5LKSffpes/dVbAryLGt6F27ROQMhWMg=", "arc-message-signature": "ARC-Message-Signature: i=1; a=rsa-sha256; c=relaxed/relaxed; d=google.com; s=arc-20240605;\r\n        h=to:subject:message-id:date:from:mime-version:dkim-signature;\r\n        bh=yhyki7Kf5bQNf/8oq2uNTa/y/MoSnhI+j3ZqeBT892s=;\r\n        fh=fBYbxd7sNeSi3dX3VeF2+nxOJZdPrQsXgdNq9LWGYGE=;\r\n        b=V70ViYZcIyYaZCIMeEXNv4R6X5fkIYJxel9I6iHuCI2RJLc824inbFBL3Enb/JD8yt\r\n         Sk1iK/RGh+PYMU1FAHeq/uUri2PG1Z9RZc7e7jjLil/nCWpYF91AhFEZE8B7kl5uWKZb\r\n         qA4ASGlYUTJwjoWMpJle0uvlOBksdXIb2Zb5K6kyHe4zlqhHeM6ySiJLEu7bj/eS5TYg\r\n         vnmoySAYAsLH5T/08gj6OwaBWcmqhfMVO8adMkIZe1VZQqC9nKVJJis0I3Hsl9UwhicB\r\n         VDSvEH/KsHrDDqPkSMDHykv1NzBK9cPgQ8cAG4QdgSd3zuEp5uJkxXNycF1NN1cZwRfZ\r\n         eC2A==;\r\n        dara=google.com", "authentication-results": "Authentication-Results: mx.google.com;\r\n       dkim=pass header.i=@gmail.com header.s=20230601 header.b=Njlov8RL;\r\n       spf=pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) smtp.mailfrom=<EMAIL>;\r\n       dmarc=pass (p=NONE sp=QUARANTINE dis=NONE) header.from=gmail.com;\r\n       dara=pass header.i=@gmail.com", "x-google-dkim-signature": "X-Google-DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed;\r\n        d=1e100.net; s=20230601; t=1744540397; x=1745145197;\r\n        h=to:subject:message-id:date:from:mime-version:x-gm-message-state\r\n         :from:to:cc:subject:date:message-id:reply-to;\r\n        bh=yhyki7Kf5bQNf/8oq2uNTa/y/MoSnhI+j3ZqeBT892s=;\r\n        b=o2krTN0ebufVGn92FP/xtW+t8OQ46Jc9sSrWVXrWihY1hBM7C9fEwuF9svkxx3SB8B\r\n         m8qZVS5TIDCv+JkZKK9jpHw3cD09s/YSr7aPP5bAWibx5UhB1/Ki7Kn0hdgt90LS2Kob\r\n         jr7CP8QrrWfftq7zutBxaVoCdBtTrod/TJKDxDr1b3vFaoN/XxGnUeqj8EoAbdTDf859\r\n         5hmRQUODpJaybi3MDmBzStjIh9rlUBLkt4csANAuUZWX1/b28+HAiT7AOdq9ksbROpgi\r\n         h5LedT5dMXPYU6yU0lQ6kk14R6eX6tHQN3AV5I1kCOaaeArC7NvUK5o8mUH2QDKZgWIe\r\n         DR5A==", "arc-authentication-results": "ARC-Authentication-Results: i=1; mx.google.com;\r\n       dkim=pass header.i=@gmail.com header.s=20230601 header.b=Njlov8RL;\r\n       spf=pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) smtp.mailfrom=<EMAIL>;\r\n       dmarc=pass (p=NONE sp=QUARANTINE dis=NONE) header.from=gmail.com;\r\n       dara=pass header.i=@gmail.com"}, "subject": "Inquiry for cooperation", "labelIds": ["UNREAD", "IMPORTANT", "CATEGORY_PERSONAL", "INBOX"], "threadId": "1962eb5ee3119d76", "messageId": "<CA+sg_9eV=X+LusGnDSP8pDrrYZ8SLn2Maq4CYpLrg=uG=<EMAIL>>", "textAsHtml": "<p>Hey!</p><p><PERSON>&apos;d love to cooperate with you - could you please send us your offer?</p><p><PERSON>,<br/><PERSON><PERSON></p>", "sizeEstimate": 5849}]}, "connections": {"JSON Parser": {"ai_outputParser": [[{"node": "Assign labels for message", "type": "ai_outputParser", "index": 0}]]}, "OpenAI Chat": {"ai_languageModel": [[{"node": "Assign labels for message", "type": "ai_languageModel", "index": 0}]]}, "Gmail trigger": {"main": [[{"node": "Get message content", "type": "main", "index": 0}]]}, "Get all labels": {"main": [[{"node": "Merge corresponding labels", "type": "main", "index": 0}]]}, "Set label values": {"main": [[{"node": "Get all labels", "type": "main", "index": 0}, {"node": "Split out assigned labels", "type": "main", "index": 0}]]}, "Aggregate label IDs": {"main": [[{"node": "Add labels to message", "type": "main", "index": 0}]]}, "Get message content": {"main": [[{"node": "Assign labels for message", "type": "main", "index": 0}]]}, "Assign labels for message": {"main": [[{"node": "Set label values", "type": "main", "index": 0}]]}, "Split out assigned labels": {"main": [[{"node": "Merge corresponding labels", "type": "main", "index": 1}]]}, "Merge corresponding labels": {"main": [[{"node": "Aggregate label IDs", "type": "main", "index": 0}]]}}}