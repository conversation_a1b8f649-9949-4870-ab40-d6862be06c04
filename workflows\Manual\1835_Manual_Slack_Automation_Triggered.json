{"id": "dDInVHNAfSedBUCb", "meta": {"instanceId": "fddb3e91967f1012c95dd02bf5ad21f279fc44715f47a7a96a33433621caa253"}, "name": "外送記帳", "tags": [], "nodes": [{"id": "09c19ba1-45f2-43af-9985-3508d801c1b7", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [440, 0], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "18625b1d-f8ff-4e48-8b64-a9d42d24eccc", "name": "Click to Test Flow", "type": "n8n-nodes-base.manualTrigger", "position": [40, 0], "parameters": {}, "typeVersion": 1}, {"id": "649933c4-b16b-46de-9038-7d8c0b3d8e88", "name": "Get emails from Gmail with certain subject", "type": "n8n-nodes-base.gmail", "position": [220, 0], "webhookId": "99c4deca-17c7-47ae-a38c-50344938e792", "parameters": {"simple": false, "filters": {"q": "subject:透過 Uber Eats 系統送出的訂單"}, "options": {}, "operation": "getAll", "returnAll": true}, "credentials": {"gmailOAuth2": {"id": "34rX9kxKlJadOY6u", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "b2118a34-52ad-4464-b7ea-7f3105536fad", "name": "Receive certain keyword Gmail Trigger", "type": "n8n-nodes-base.gmailTrigger", "position": [120, -180], "parameters": {"simple": false, "filters": {"q": "subject:透過 Uber Eats 系統送出的訂單"}, "options": {}, "pollTimes": {"item": [{"mode": "everyHour", "minute": 30}]}}, "credentials": {"gmailOAuth2": {"id": "34rX9kxKlJadOY6u", "name": "Gmail account"}}, "typeVersion": 1.2}, {"id": "********-d01a-4b11-bbaa-60c73a1dae02", "name": "Extract Price, Shop, Date, TIme", "type": "n8n-nodes-base.set", "position": [620, 60], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c24405f8-267f-4933-a178-1b51145d62bf", "name": "price", "type": "string", "value": "={{ $json[\"text\"].match(/\\$(\\d+(\\.\\d{2})?)/)[1] }}"}, {"id": "968cf7cd-6e28-4328-a829-3fe2cb327643", "name": "shop", "type": "string", "value": "={{ $json[\"text\"].match(/以下是您在([\\u4e00-\\u9fa5a-zA-Z0-9\\s]+)訂購/)[1] }}"}, {"id": "53642bcb-f3a6-4086-bdc1-2f8d27927462", "name": "date", "type": "string", "value": "={{ $json[\"text\"].match(/Date: (\\d{4}年\\d{1,2}月\\d{1,2}日)/)[1].replace(\"年\", \".\").replace(\"月\", \".\").replace(\"日\", \"\") }}"}, {"id": "cd227132-971b-4970-8b5d-724463efe036", "name": "time", "type": "string", "value": "={{ \n  $json[\"text\"].match(/(上午|下午) (\\d{1,2}):(\\d{2})/) ? \n  ($json[\"text\"].match(/(上午|下午) (\\d{1,2}):(\\d{2})/)[1] === '下午' && $json[\"text\"].match(/(上午|下午) (\\d{1,2}):(\\d{2})/)[2] !== '12' \n    ? (parseInt($json[\"text\"].match(/(上午|下午) (\\d{1,2}):(\\d{2})/)[2]) + 12) + ':' + $json[\"text\"].match(/(上午|下午) (\\d{1,2}):(\\d{2})/)[3] \n    : $json[\"text\"].match(/(上午|下午) (\\d{1,2}):(\\d{2})/)[2] + ':' + $json[\"text\"].match(/(上午|下午) (\\d{1,2}):(\\d{2})/)[3]\n  )\n  : null \n}}"}]}}, "typeVersion": 3.4}, {"id": "3d8f97ea-4a0d-4939-898f-8a0ca9415e7d", "name": "Send to Slack with Block", "type": "n8n-nodes-base.slack", "position": [800, 60], "webhookId": "0e812732-74d2-4924-8db3-6b9234965937", "parameters": {"text": "=Ubereat 訂餐資訊: \n商家:  {{ $json.shop }}\n金額: {{ $json.price }}\n日期: {{ $json.date }}\n\n記帳網址:\nmoze3://expense?amount={{ $json.price }}&account=信用卡&subcategory=外送&store={{ $json.shop }}&date={{ $json.date }}", "select": "channel", "blocksUi": "={\n\t\"blocks\": [\n\t\t{\n\t\t\t\"type\": \"section\",\n\t\t\t\"text\": {\n\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\"text\": \"Ubereat 訂餐資訊:\\n\\n*商家:* {{ $json.shop }}\\n*金額:* {{ $json.price }}\\n*日期:* {{ $json.date }}\"\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\t\"type\": \"divider\"\n\t\t},\n\t\t{\n\t\t\t\"type\": \"section\",\n\t\t\t\"text\": {\n\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\"text\": \"Moze 記帳請點我\"\n\t\t\t},\n\t\t\t\"accessory\": {\n\t\t\t\t\"type\": \"button\",\n\t\t\t\t\"text\": {\n\t\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\t\"text\": \"記帳\",\n\t\t\t\t\t\"emoji\": true\n\t\t\t\t},\n\t\t\t\t\"value\": \"click\",\n\t\t\t\t\"url\": \"moze3://expense?amount={{ $json.price }}&account=信用卡&subcategory=外送&store={{ $json.shop }}&date={{ $json.date }}&&project=生活開銷&&time={{ $json.time }}\",\n\t\t\t\t\"action_id\": \"button-action\"\n\t\t\t}\n\t\t}\n\t]\n}", "channelId": {"__rl": true, "mode": "list", "value": "C0883CJM1UH", "cachedResultName": "外送記帳自動化"}, "messageType": "block", "otherOptions": {}, "authentication": "oAuth2"}, "credentials": {"slackOAuth2Api": {"id": "sD1J9ZLyEhcglrRa", "name": "Slack account"}}, "typeVersion": 2.3}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "0840254c-0058-47fe-9b22-7fbb93144788", "connections": {"Loop Over Items": {"main": [[], [{"node": "Extract Price, Shop, Date, TIme", "type": "main", "index": 0}]]}, "Click to Test Flow": {"main": [[{"node": "Get emails from Gmail with certain subject", "type": "main", "index": 0}]]}, "Send to Slack with Block": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Extract Price, Shop, Date, TIme": {"main": [[{"node": "Send to Slack with Block", "type": "main", "index": 0}]]}, "Receive certain keyword Gmail Trigger": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Get emails from Gmail with certain subject": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}}}